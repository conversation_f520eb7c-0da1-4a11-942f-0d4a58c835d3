// node_modules/bpmn-js/node_modules/min-dash/dist/index.esm.js
function flatten(arr) {
  return Array.prototype.concat.apply([], arr);
}
var nativeToString = Object.prototype.toString;
var nativeHasOwnProperty = Object.prototype.hasOwnProperty;
function isUndefined(obj) {
  return obj === void 0;
}
function isDefined(obj) {
  return obj !== void 0;
}
function isNil(obj) {
  return obj == null;
}
function isArray(obj) {
  return nativeToString.call(obj) === "[object Array]";
}
function isObject(obj) {
  return nativeToString.call(obj) === "[object Object]";
}
function isNumber(obj) {
  return nativeToString.call(obj) === "[object Number]";
}
function isFunction(obj) {
  var tag = nativeToString.call(obj);
  return tag === "[object Function]" || tag === "[object AsyncFunction]" || tag === "[object GeneratorFunction]" || tag === "[object AsyncGeneratorFunction]" || tag === "[object Proxy]";
}
function isString(obj) {
  return nativeToString.call(obj) === "[object String]";
}
function ensureArray(obj) {
  if (isArray(obj)) {
    return;
  }
  throw new Error("must supply array");
}
function has(target, key) {
  return nativeHasOwnProperty.call(target, key);
}
function find(collection, matcher) {
  matcher = toMatcher(matcher);
  var match;
  forEach(collection, function(val, key) {
    if (matcher(val, key)) {
      match = val;
      return false;
    }
  });
  return match;
}
function findIndex(collection, matcher) {
  matcher = toMatcher(matcher);
  var idx = isArray(collection) ? -1 : void 0;
  forEach(collection, function(val, key) {
    if (matcher(val, key)) {
      idx = key;
      return false;
    }
  });
  return idx;
}
function filter(collection, matcher) {
  var result = [];
  forEach(collection, function(val, key) {
    if (matcher(val, key)) {
      result.push(val);
    }
  });
  return result;
}
function forEach(collection, iterator) {
  var val, result;
  if (isUndefined(collection)) {
    return;
  }
  var convertKey = isArray(collection) ? toNum : identity;
  for (var key in collection) {
    if (has(collection, key)) {
      val = collection[key];
      result = iterator(val, convertKey(key));
      if (result === false) {
        return val;
      }
    }
  }
}
function without(arr, matcher) {
  if (isUndefined(arr)) {
    return [];
  }
  ensureArray(arr);
  matcher = toMatcher(matcher);
  return arr.filter(function(el, idx) {
    return !matcher(el, idx);
  });
}
function reduce(collection, iterator, result) {
  forEach(collection, function(value, idx) {
    result = iterator(result, value, idx);
  });
  return result;
}
function every(collection, matcher) {
  return !!reduce(collection, function(matches, val, key) {
    return matches && matcher(val, key);
  }, true);
}
function some(collection, matcher) {
  return !!find(collection, matcher);
}
function map(collection, fn) {
  var result = [];
  forEach(collection, function(val, key) {
    result.push(fn(val, key));
  });
  return result;
}
function keys(collection) {
  return collection && Object.keys(collection) || [];
}
function size(collection) {
  return keys(collection).length;
}
function values(collection) {
  return map(collection, function(val) {
    return val;
  });
}
function groupBy(collection, extractor) {
  var grouped = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};
  extractor = toExtractor(extractor);
  forEach(collection, function(val) {
    var discriminator = extractor(val) || "_";
    var group = grouped[discriminator];
    if (!group) {
      group = grouped[discriminator] = [];
    }
    group.push(val);
  });
  return grouped;
}
function uniqueBy(extractor) {
  extractor = toExtractor(extractor);
  var grouped = {};
  for (var _len = arguments.length, collections = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
    collections[_key - 1] = arguments[_key];
  }
  forEach(collections, function(c) {
    return groupBy(c, extractor, grouped);
  });
  var result = map(grouped, function(val, key) {
    return val[0];
  });
  return result;
}
var unionBy = uniqueBy;
function sortBy(collection, extractor) {
  extractor = toExtractor(extractor);
  var sorted = [];
  forEach(collection, function(value, key) {
    var disc = extractor(value, key);
    var entry = {
      d: disc,
      v: value
    };
    for (var idx = 0; idx < sorted.length; idx++) {
      var d = sorted[idx].d;
      if (disc < d) {
        sorted.splice(idx, 0, entry);
        return;
      }
    }
    sorted.push(entry);
  });
  return map(sorted, function(e) {
    return e.v;
  });
}
function matchPattern(pattern) {
  return function(el) {
    return every(pattern, function(val, key) {
      return el[key] === val;
    });
  };
}
function toExtractor(extractor) {
  return isFunction(extractor) ? extractor : function(e) {
    return e[extractor];
  };
}
function toMatcher(matcher) {
  return isFunction(matcher) ? matcher : function(e) {
    return e === matcher;
  };
}
function identity(arg) {
  return arg;
}
function toNum(arg) {
  return Number(arg);
}
function debounce(fn, timeout) {
  var timer;
  var lastArgs;
  var lastThis;
  var lastNow;
  function fire(force) {
    var now = Date.now();
    var scheduledDiff = force ? 0 : lastNow + timeout - now;
    if (scheduledDiff > 0) {
      return schedule(scheduledDiff);
    }
    fn.apply(lastThis, lastArgs);
    clear();
  }
  function schedule(timeout2) {
    timer = setTimeout(fire, timeout2);
  }
  function clear() {
    if (timer) {
      clearTimeout(timer);
    }
    timer = lastNow = lastArgs = lastThis = void 0;
  }
  function flush() {
    if (timer) {
      fire(true);
    }
    clear();
  }
  function callback() {
    lastNow = Date.now();
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    lastArgs = args;
    lastThis = this;
    if (!timer) {
      schedule(timeout);
    }
  }
  callback.flush = flush;
  callback.cancel = clear;
  return callback;
}
function bind(fn, target) {
  return fn.bind(target);
}
function _extends() {
  _extends = Object.assign || function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends.apply(this, arguments);
}
function assign(target) {
  for (var _len = arguments.length, others = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
    others[_key - 1] = arguments[_key];
  }
  return _extends.apply(void 0, [target].concat(others));
}
function pick(target, properties) {
  var result = {};
  var obj = Object(target);
  forEach(properties, function(prop) {
    if (prop in obj) {
      result[prop] = target[prop];
    }
  });
  return result;
}
function omit(target, properties) {
  var result = {};
  var obj = Object(target);
  forEach(obj, function(prop, key) {
    if (properties.indexOf(key) === -1) {
      result[key] = prop;
    }
  });
  return result;
}

export {
  flatten,
  isUndefined,
  isDefined,
  isNil,
  isArray,
  isObject,
  isNumber,
  isFunction,
  isString,
  has,
  find,
  findIndex,
  filter,
  forEach,
  without,
  reduce,
  every,
  some,
  map,
  keys,
  size,
  values,
  groupBy,
  uniqueBy,
  unionBy,
  sortBy,
  matchPattern,
  debounce,
  bind,
  assign,
  pick,
  omit
};
//# sourceMappingURL=chunk-VRJCCLJE.js.map
