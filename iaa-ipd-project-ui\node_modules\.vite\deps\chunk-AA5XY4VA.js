// node_modules/diagram-js/lib/util/Event.js
function getOriginal(event) {
  return event.originalEvent || event.srcEvent;
}

// node_modules/diagram-js/lib/util/Platform.js
function isMac() {
  return /mac/i.test(navigator.platform);
}

// node_modules/diagram-js/lib/util/Mouse.js
function isButton(event, button) {
  return (getOriginal(event) || event).button === button;
}
function isPrimaryButton(event) {
  return isButton(event, 0);
}
function isAuxiliaryButton(event) {
  return isButton(event, 1);
}
function isSecondaryButton(event) {
  return isButton(event, 2);
}
function hasPrimaryModifier(event) {
  var originalEvent = getOriginal(event) || event;
  if (!isPrimaryButton(event)) {
    return false;
  }
  if (isMac()) {
    return originalEvent.metaKey;
  } else {
    return originalEvent.ctrlKey;
  }
}
function hasSecondaryModifier(event) {
  var originalEvent = getOriginal(event) || event;
  return isPrimaryButton(event) && originalEvent.shiftKey;
}

export {
  isMac,
  isButton,
  isPrimaryButton,
  isAuxiliaryButton,
  isSecondaryButton,
  hasPrimaryModifier,
  hasSecondaryModifier
};
//# sourceMappingURL=chunk-AA5XY4VA.js.map
