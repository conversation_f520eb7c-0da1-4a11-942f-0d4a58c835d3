import {
  DateControlRenderer
} from "./chunk-BQ7HYGA6.js";
import "./chunk-JHAOQP73.js";
import "./chunk-GFI6XVUE.js";
import "./chunk-ZRTRFIWU.js";
import "./chunk-6C4OBBQZ.js";
import "./chunk-M5OFQAQB.js";
import "./chunk-YPPVVTGH.js";
import {
  __assign,
  __decorate,
  __extends
} from "./chunk-F25BIIHK.js";
import {
  Renderer
} from "./chunk-LZQZ2OHM.js";
import "./chunk-QMS2VRR4.js";
import "./chunk-5QW7M2DY.js";
import "./chunk-ZJNJ4ZV3.js";
import "./chunk-KEABM62F.js";
import "./chunk-CNJUQDSN.js";
import "./chunk-YZFIV7S4.js";
import "./chunk-7XBFYOTW.js";
import "./chunk-ZUWHLQVA.js";
import "./chunk-KNGT5NUO.js";
import "./chunk-X3JSFYRQ.js";
import "./chunk-GFT2G5UO.js";

// node_modules/amis/esm/renderers/Calendar.js
var CalendarRenderer = (
  /** @class */
  function(_super) {
    __extends(CalendarRenderer2, _super);
    function CalendarRenderer2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    CalendarRenderer2.defaultProps = __assign(__assign({}, DateControlRenderer.defaultProps), { embed: true });
    CalendarRenderer2 = __decorate([
      Renderer({
        type: "calendar"
      })
    ], CalendarRenderer2);
    return CalendarRenderer2;
  }(DateControlRenderer)
);
export {
  CalendarRenderer
};
//# sourceMappingURL=Calendar-AURZ3YFH.js.map
