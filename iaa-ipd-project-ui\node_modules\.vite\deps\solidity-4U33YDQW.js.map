{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/solidity/solidity.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    comments: {\n        lineComment: '//',\n        blockComment: ['/*', '*/']\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')'],\n        ['<', '>']\n    ],\n    autoClosingPairs: [\n        { open: '\"', close: '\"', notIn: ['string', 'comment'] },\n        { open: '{', close: '}', notIn: ['string', 'comment'] },\n        { open: '[', close: ']', notIn: ['string', 'comment'] },\n        { open: '(', close: ')', notIn: ['string', 'comment'] }\n    ]\n};\nexport var language = {\n    defaultToken: '',\n    tokenPostfix: '.sol',\n    brackets: [\n        { token: 'delimiter.curly', open: '{', close: '}' },\n        { token: 'delimiter.parenthesis', open: '(', close: ')' },\n        { token: 'delimiter.square', open: '[', close: ']' },\n        { token: 'delimiter.angle', open: '<', close: '>' }\n    ],\n    keywords: [\n        // Main keywords\n        'pragma',\n        'solidity',\n        'contract',\n        'library',\n        'using',\n        'struct',\n        'function',\n        'modifier',\n        'constructor',\n        //Built-in types\n        'address',\n        'string',\n        'bool',\n        //Other types\n        'Int',\n        'Uint',\n        'Byte',\n        'Fixed',\n        'Ufixed',\n        //All int\n        'int',\n        'int8',\n        'int16',\n        'int24',\n        'int32',\n        'int40',\n        'int48',\n        'int56',\n        'int64',\n        'int72',\n        'int80',\n        'int88',\n        'int96',\n        'int104',\n        'int112',\n        'int120',\n        'int128',\n        'int136',\n        'int144',\n        'int152',\n        'int160',\n        'int168',\n        'int176',\n        'int184',\n        'int192',\n        'int200',\n        'int208',\n        'int216',\n        'int224',\n        'int232',\n        'int240',\n        'int248',\n        'int256',\n        //All uint\n        'uint',\n        'uint8',\n        'uint16',\n        'uint24',\n        'uint32',\n        'uint40',\n        'uint48',\n        'uint56',\n        'uint64',\n        'uint72',\n        'uint80',\n        'uint88',\n        'uint96',\n        'uint104',\n        'uint112',\n        'uint120',\n        'uint128',\n        'uint136',\n        'uint144',\n        'uint152',\n        'uint160',\n        'uint168',\n        'uint176',\n        'uint184',\n        'uint192',\n        'uint200',\n        'uint208',\n        'uint216',\n        'uint224',\n        'uint232',\n        'uint240',\n        'uint248',\n        'uint256',\n        //All Byte\n        'byte',\n        'bytes',\n        'bytes1',\n        'bytes2',\n        'bytes3',\n        'bytes4',\n        'bytes5',\n        'bytes6',\n        'bytes7',\n        'bytes8',\n        'bytes9',\n        'bytes10',\n        'bytes11',\n        'bytes12',\n        'bytes13',\n        'bytes14',\n        'bytes15',\n        'bytes16',\n        'bytes17',\n        'bytes18',\n        'bytes19',\n        'bytes20',\n        'bytes21',\n        'bytes22',\n        'bytes23',\n        'bytes24',\n        'bytes25',\n        'bytes26',\n        'bytes27',\n        'bytes28',\n        'bytes29',\n        'bytes30',\n        'bytes31',\n        'bytes32',\n        //All fixed\n        'fixed',\n        'fixed0x8',\n        'fixed0x16',\n        'fixed0x24',\n        'fixed0x32',\n        'fixed0x40',\n        'fixed0x48',\n        'fixed0x56',\n        'fixed0x64',\n        'fixed0x72',\n        'fixed0x80',\n        'fixed0x88',\n        'fixed0x96',\n        'fixed0x104',\n        'fixed0x112',\n        'fixed0x120',\n        'fixed0x128',\n        'fixed0x136',\n        'fixed0x144',\n        'fixed0x152',\n        'fixed0x160',\n        'fixed0x168',\n        'fixed0x176',\n        'fixed0x184',\n        'fixed0x192',\n        'fixed0x200',\n        'fixed0x208',\n        'fixed0x216',\n        'fixed0x224',\n        'fixed0x232',\n        'fixed0x240',\n        'fixed0x248',\n        'fixed0x256',\n        'fixed8x8',\n        'fixed8x16',\n        'fixed8x24',\n        'fixed8x32',\n        'fixed8x40',\n        'fixed8x48',\n        'fixed8x56',\n        'fixed8x64',\n        'fixed8x72',\n        'fixed8x80',\n        'fixed8x88',\n        'fixed8x96',\n        'fixed8x104',\n        'fixed8x112',\n        'fixed8x120',\n        'fixed8x128',\n        'fixed8x136',\n        'fixed8x144',\n        'fixed8x152',\n        'fixed8x160',\n        'fixed8x168',\n        'fixed8x176',\n        'fixed8x184',\n        'fixed8x192',\n        'fixed8x200',\n        'fixed8x208',\n        'fixed8x216',\n        'fixed8x224',\n        'fixed8x232',\n        'fixed8x240',\n        'fixed8x248',\n        'fixed16x8',\n        'fixed16x16',\n        'fixed16x24',\n        'fixed16x32',\n        'fixed16x40',\n        'fixed16x48',\n        'fixed16x56',\n        'fixed16x64',\n        'fixed16x72',\n        'fixed16x80',\n        'fixed16x88',\n        'fixed16x96',\n        'fixed16x104',\n        'fixed16x112',\n        'fixed16x120',\n        'fixed16x128',\n        'fixed16x136',\n        'fixed16x144',\n        'fixed16x152',\n        'fixed16x160',\n        'fixed16x168',\n        'fixed16x176',\n        'fixed16x184',\n        'fixed16x192',\n        'fixed16x200',\n        'fixed16x208',\n        'fixed16x216',\n        'fixed16x224',\n        'fixed16x232',\n        'fixed16x240',\n        'fixed24x8',\n        'fixed24x16',\n        'fixed24x24',\n        'fixed24x32',\n        'fixed24x40',\n        'fixed24x48',\n        'fixed24x56',\n        'fixed24x64',\n        'fixed24x72',\n        'fixed24x80',\n        'fixed24x88',\n        'fixed24x96',\n        'fixed24x104',\n        'fixed24x112',\n        'fixed24x120',\n        'fixed24x128',\n        'fixed24x136',\n        'fixed24x144',\n        'fixed24x152',\n        'fixed24x160',\n        'fixed24x168',\n        'fixed24x176',\n        'fixed24x184',\n        'fixed24x192',\n        'fixed24x200',\n        'fixed24x208',\n        'fixed24x216',\n        'fixed24x224',\n        'fixed24x232',\n        'fixed32x8',\n        'fixed32x16',\n        'fixed32x24',\n        'fixed32x32',\n        'fixed32x40',\n        'fixed32x48',\n        'fixed32x56',\n        'fixed32x64',\n        'fixed32x72',\n        'fixed32x80',\n        'fixed32x88',\n        'fixed32x96',\n        'fixed32x104',\n        'fixed32x112',\n        'fixed32x120',\n        'fixed32x128',\n        'fixed32x136',\n        'fixed32x144',\n        'fixed32x152',\n        'fixed32x160',\n        'fixed32x168',\n        'fixed32x176',\n        'fixed32x184',\n        'fixed32x192',\n        'fixed32x200',\n        'fixed32x208',\n        'fixed32x216',\n        'fixed32x224',\n        'fixed40x8',\n        'fixed40x16',\n        'fixed40x24',\n        'fixed40x32',\n        'fixed40x40',\n        'fixed40x48',\n        'fixed40x56',\n        'fixed40x64',\n        'fixed40x72',\n        'fixed40x80',\n        'fixed40x88',\n        'fixed40x96',\n        'fixed40x104',\n        'fixed40x112',\n        'fixed40x120',\n        'fixed40x128',\n        'fixed40x136',\n        'fixed40x144',\n        'fixed40x152',\n        'fixed40x160',\n        'fixed40x168',\n        'fixed40x176',\n        'fixed40x184',\n        'fixed40x192',\n        'fixed40x200',\n        'fixed40x208',\n        'fixed40x216',\n        'fixed48x8',\n        'fixed48x16',\n        'fixed48x24',\n        'fixed48x32',\n        'fixed48x40',\n        'fixed48x48',\n        'fixed48x56',\n        'fixed48x64',\n        'fixed48x72',\n        'fixed48x80',\n        'fixed48x88',\n        'fixed48x96',\n        'fixed48x104',\n        'fixed48x112',\n        'fixed48x120',\n        'fixed48x128',\n        'fixed48x136',\n        'fixed48x144',\n        'fixed48x152',\n        'fixed48x160',\n        'fixed48x168',\n        'fixed48x176',\n        'fixed48x184',\n        'fixed48x192',\n        'fixed48x200',\n        'fixed48x208',\n        'fixed56x8',\n        'fixed56x16',\n        'fixed56x24',\n        'fixed56x32',\n        'fixed56x40',\n        'fixed56x48',\n        'fixed56x56',\n        'fixed56x64',\n        'fixed56x72',\n        'fixed56x80',\n        'fixed56x88',\n        'fixed56x96',\n        'fixed56x104',\n        'fixed56x112',\n        'fixed56x120',\n        'fixed56x128',\n        'fixed56x136',\n        'fixed56x144',\n        'fixed56x152',\n        'fixed56x160',\n        'fixed56x168',\n        'fixed56x176',\n        'fixed56x184',\n        'fixed56x192',\n        'fixed56x200',\n        'fixed64x8',\n        'fixed64x16',\n        'fixed64x24',\n        'fixed64x32',\n        'fixed64x40',\n        'fixed64x48',\n        'fixed64x56',\n        'fixed64x64',\n        'fixed64x72',\n        'fixed64x80',\n        'fixed64x88',\n        'fixed64x96',\n        'fixed64x104',\n        'fixed64x112',\n        'fixed64x120',\n        'fixed64x128',\n        'fixed64x136',\n        'fixed64x144',\n        'fixed64x152',\n        'fixed64x160',\n        'fixed64x168',\n        'fixed64x176',\n        'fixed64x184',\n        'fixed64x192',\n        'fixed72x8',\n        'fixed72x16',\n        'fixed72x24',\n        'fixed72x32',\n        'fixed72x40',\n        'fixed72x48',\n        'fixed72x56',\n        'fixed72x64',\n        'fixed72x72',\n        'fixed72x80',\n        'fixed72x88',\n        'fixed72x96',\n        'fixed72x104',\n        'fixed72x112',\n        'fixed72x120',\n        'fixed72x128',\n        'fixed72x136',\n        'fixed72x144',\n        'fixed72x152',\n        'fixed72x160',\n        'fixed72x168',\n        'fixed72x176',\n        'fixed72x184',\n        'fixed80x8',\n        'fixed80x16',\n        'fixed80x24',\n        'fixed80x32',\n        'fixed80x40',\n        'fixed80x48',\n        'fixed80x56',\n        'fixed80x64',\n        'fixed80x72',\n        'fixed80x80',\n        'fixed80x88',\n        'fixed80x96',\n        'fixed80x104',\n        'fixed80x112',\n        'fixed80x120',\n        'fixed80x128',\n        'fixed80x136',\n        'fixed80x144',\n        'fixed80x152',\n        'fixed80x160',\n        'fixed80x168',\n        'fixed80x176',\n        'fixed88x8',\n        'fixed88x16',\n        'fixed88x24',\n        'fixed88x32',\n        'fixed88x40',\n        'fixed88x48',\n        'fixed88x56',\n        'fixed88x64',\n        'fixed88x72',\n        'fixed88x80',\n        'fixed88x88',\n        'fixed88x96',\n        'fixed88x104',\n        'fixed88x112',\n        'fixed88x120',\n        'fixed88x128',\n        'fixed88x136',\n        'fixed88x144',\n        'fixed88x152',\n        'fixed88x160',\n        'fixed88x168',\n        'fixed96x8',\n        'fixed96x16',\n        'fixed96x24',\n        'fixed96x32',\n        'fixed96x40',\n        'fixed96x48',\n        'fixed96x56',\n        'fixed96x64',\n        'fixed96x72',\n        'fixed96x80',\n        'fixed96x88',\n        'fixed96x96',\n        'fixed96x104',\n        'fixed96x112',\n        'fixed96x120',\n        'fixed96x128',\n        'fixed96x136',\n        'fixed96x144',\n        'fixed96x152',\n        'fixed96x160',\n        'fixed104x8',\n        'fixed104x16',\n        'fixed104x24',\n        'fixed104x32',\n        'fixed104x40',\n        'fixed104x48',\n        'fixed104x56',\n        'fixed104x64',\n        'fixed104x72',\n        'fixed104x80',\n        'fixed104x88',\n        'fixed104x96',\n        'fixed104x104',\n        'fixed104x112',\n        'fixed104x120',\n        'fixed104x128',\n        'fixed104x136',\n        'fixed104x144',\n        'fixed104x152',\n        'fixed112x8',\n        'fixed112x16',\n        'fixed112x24',\n        'fixed112x32',\n        'fixed112x40',\n        'fixed112x48',\n        'fixed112x56',\n        'fixed112x64',\n        'fixed112x72',\n        'fixed112x80',\n        'fixed112x88',\n        'fixed112x96',\n        'fixed112x104',\n        'fixed112x112',\n        'fixed112x120',\n        'fixed112x128',\n        'fixed112x136',\n        'fixed112x144',\n        'fixed120x8',\n        'fixed120x16',\n        'fixed120x24',\n        'fixed120x32',\n        'fixed120x40',\n        'fixed120x48',\n        'fixed120x56',\n        'fixed120x64',\n        'fixed120x72',\n        'fixed120x80',\n        'fixed120x88',\n        'fixed120x96',\n        'fixed120x104',\n        'fixed120x112',\n        'fixed120x120',\n        'fixed120x128',\n        'fixed120x136',\n        'fixed128x8',\n        'fixed128x16',\n        'fixed128x24',\n        'fixed128x32',\n        'fixed128x40',\n        'fixed128x48',\n        'fixed128x56',\n        'fixed128x64',\n        'fixed128x72',\n        'fixed128x80',\n        'fixed128x88',\n        'fixed128x96',\n        'fixed128x104',\n        'fixed128x112',\n        'fixed128x120',\n        'fixed128x128',\n        'fixed136x8',\n        'fixed136x16',\n        'fixed136x24',\n        'fixed136x32',\n        'fixed136x40',\n        'fixed136x48',\n        'fixed136x56',\n        'fixed136x64',\n        'fixed136x72',\n        'fixed136x80',\n        'fixed136x88',\n        'fixed136x96',\n        'fixed136x104',\n        'fixed136x112',\n        'fixed136x120',\n        'fixed144x8',\n        'fixed144x16',\n        'fixed144x24',\n        'fixed144x32',\n        'fixed144x40',\n        'fixed144x48',\n        'fixed144x56',\n        'fixed144x64',\n        'fixed144x72',\n        'fixed144x80',\n        'fixed144x88',\n        'fixed144x96',\n        'fixed144x104',\n        'fixed144x112',\n        'fixed152x8',\n        'fixed152x16',\n        'fixed152x24',\n        'fixed152x32',\n        'fixed152x40',\n        'fixed152x48',\n        'fixed152x56',\n        'fixed152x64',\n        'fixed152x72',\n        'fixed152x80',\n        'fixed152x88',\n        'fixed152x96',\n        'fixed152x104',\n        'fixed160x8',\n        'fixed160x16',\n        'fixed160x24',\n        'fixed160x32',\n        'fixed160x40',\n        'fixed160x48',\n        'fixed160x56',\n        'fixed160x64',\n        'fixed160x72',\n        'fixed160x80',\n        'fixed160x88',\n        'fixed160x96',\n        'fixed168x8',\n        'fixed168x16',\n        'fixed168x24',\n        'fixed168x32',\n        'fixed168x40',\n        'fixed168x48',\n        'fixed168x56',\n        'fixed168x64',\n        'fixed168x72',\n        'fixed168x80',\n        'fixed168x88',\n        'fixed176x8',\n        'fixed176x16',\n        'fixed176x24',\n        'fixed176x32',\n        'fixed176x40',\n        'fixed176x48',\n        'fixed176x56',\n        'fixed176x64',\n        'fixed176x72',\n        'fixed176x80',\n        'fixed184x8',\n        'fixed184x16',\n        'fixed184x24',\n        'fixed184x32',\n        'fixed184x40',\n        'fixed184x48',\n        'fixed184x56',\n        'fixed184x64',\n        'fixed184x72',\n        'fixed192x8',\n        'fixed192x16',\n        'fixed192x24',\n        'fixed192x32',\n        'fixed192x40',\n        'fixed192x48',\n        'fixed192x56',\n        'fixed192x64',\n        'fixed200x8',\n        'fixed200x16',\n        'fixed200x24',\n        'fixed200x32',\n        'fixed200x40',\n        'fixed200x48',\n        'fixed200x56',\n        'fixed208x8',\n        'fixed208x16',\n        'fixed208x24',\n        'fixed208x32',\n        'fixed208x40',\n        'fixed208x48',\n        'fixed216x8',\n        'fixed216x16',\n        'fixed216x24',\n        'fixed216x32',\n        'fixed216x40',\n        'fixed224x8',\n        'fixed224x16',\n        'fixed224x24',\n        'fixed224x32',\n        'fixed232x8',\n        'fixed232x16',\n        'fixed232x24',\n        'fixed240x8',\n        'fixed240x16',\n        'fixed248x8',\n        //All ufixed\n        'ufixed',\n        'ufixed0x8',\n        'ufixed0x16',\n        'ufixed0x24',\n        'ufixed0x32',\n        'ufixed0x40',\n        'ufixed0x48',\n        'ufixed0x56',\n        'ufixed0x64',\n        'ufixed0x72',\n        'ufixed0x80',\n        'ufixed0x88',\n        'ufixed0x96',\n        'ufixed0x104',\n        'ufixed0x112',\n        'ufixed0x120',\n        'ufixed0x128',\n        'ufixed0x136',\n        'ufixed0x144',\n        'ufixed0x152',\n        'ufixed0x160',\n        'ufixed0x168',\n        'ufixed0x176',\n        'ufixed0x184',\n        'ufixed0x192',\n        'ufixed0x200',\n        'ufixed0x208',\n        'ufixed0x216',\n        'ufixed0x224',\n        'ufixed0x232',\n        'ufixed0x240',\n        'ufixed0x248',\n        'ufixed0x256',\n        'ufixed8x8',\n        'ufixed8x16',\n        'ufixed8x24',\n        'ufixed8x32',\n        'ufixed8x40',\n        'ufixed8x48',\n        'ufixed8x56',\n        'ufixed8x64',\n        'ufixed8x72',\n        'ufixed8x80',\n        'ufixed8x88',\n        'ufixed8x96',\n        'ufixed8x104',\n        'ufixed8x112',\n        'ufixed8x120',\n        'ufixed8x128',\n        'ufixed8x136',\n        'ufixed8x144',\n        'ufixed8x152',\n        'ufixed8x160',\n        'ufixed8x168',\n        'ufixed8x176',\n        'ufixed8x184',\n        'ufixed8x192',\n        'ufixed8x200',\n        'ufixed8x208',\n        'ufixed8x216',\n        'ufixed8x224',\n        'ufixed8x232',\n        'ufixed8x240',\n        'ufixed8x248',\n        'ufixed16x8',\n        'ufixed16x16',\n        'ufixed16x24',\n        'ufixed16x32',\n        'ufixed16x40',\n        'ufixed16x48',\n        'ufixed16x56',\n        'ufixed16x64',\n        'ufixed16x72',\n        'ufixed16x80',\n        'ufixed16x88',\n        'ufixed16x96',\n        'ufixed16x104',\n        'ufixed16x112',\n        'ufixed16x120',\n        'ufixed16x128',\n        'ufixed16x136',\n        'ufixed16x144',\n        'ufixed16x152',\n        'ufixed16x160',\n        'ufixed16x168',\n        'ufixed16x176',\n        'ufixed16x184',\n        'ufixed16x192',\n        'ufixed16x200',\n        'ufixed16x208',\n        'ufixed16x216',\n        'ufixed16x224',\n        'ufixed16x232',\n        'ufixed16x240',\n        'ufixed24x8',\n        'ufixed24x16',\n        'ufixed24x24',\n        'ufixed24x32',\n        'ufixed24x40',\n        'ufixed24x48',\n        'ufixed24x56',\n        'ufixed24x64',\n        'ufixed24x72',\n        'ufixed24x80',\n        'ufixed24x88',\n        'ufixed24x96',\n        'ufixed24x104',\n        'ufixed24x112',\n        'ufixed24x120',\n        'ufixed24x128',\n        'ufixed24x136',\n        'ufixed24x144',\n        'ufixed24x152',\n        'ufixed24x160',\n        'ufixed24x168',\n        'ufixed24x176',\n        'ufixed24x184',\n        'ufixed24x192',\n        'ufixed24x200',\n        'ufixed24x208',\n        'ufixed24x216',\n        'ufixed24x224',\n        'ufixed24x232',\n        'ufixed32x8',\n        'ufixed32x16',\n        'ufixed32x24',\n        'ufixed32x32',\n        'ufixed32x40',\n        'ufixed32x48',\n        'ufixed32x56',\n        'ufixed32x64',\n        'ufixed32x72',\n        'ufixed32x80',\n        'ufixed32x88',\n        'ufixed32x96',\n        'ufixed32x104',\n        'ufixed32x112',\n        'ufixed32x120',\n        'ufixed32x128',\n        'ufixed32x136',\n        'ufixed32x144',\n        'ufixed32x152',\n        'ufixed32x160',\n        'ufixed32x168',\n        'ufixed32x176',\n        'ufixed32x184',\n        'ufixed32x192',\n        'ufixed32x200',\n        'ufixed32x208',\n        'ufixed32x216',\n        'ufixed32x224',\n        'ufixed40x8',\n        'ufixed40x16',\n        'ufixed40x24',\n        'ufixed40x32',\n        'ufixed40x40',\n        'ufixed40x48',\n        'ufixed40x56',\n        'ufixed40x64',\n        'ufixed40x72',\n        'ufixed40x80',\n        'ufixed40x88',\n        'ufixed40x96',\n        'ufixed40x104',\n        'ufixed40x112',\n        'ufixed40x120',\n        'ufixed40x128',\n        'ufixed40x136',\n        'ufixed40x144',\n        'ufixed40x152',\n        'ufixed40x160',\n        'ufixed40x168',\n        'ufixed40x176',\n        'ufixed40x184',\n        'ufixed40x192',\n        'ufixed40x200',\n        'ufixed40x208',\n        'ufixed40x216',\n        'ufixed48x8',\n        'ufixed48x16',\n        'ufixed48x24',\n        'ufixed48x32',\n        'ufixed48x40',\n        'ufixed48x48',\n        'ufixed48x56',\n        'ufixed48x64',\n        'ufixed48x72',\n        'ufixed48x80',\n        'ufixed48x88',\n        'ufixed48x96',\n        'ufixed48x104',\n        'ufixed48x112',\n        'ufixed48x120',\n        'ufixed48x128',\n        'ufixed48x136',\n        'ufixed48x144',\n        'ufixed48x152',\n        'ufixed48x160',\n        'ufixed48x168',\n        'ufixed48x176',\n        'ufixed48x184',\n        'ufixed48x192',\n        'ufixed48x200',\n        'ufixed48x208',\n        'ufixed56x8',\n        'ufixed56x16',\n        'ufixed56x24',\n        'ufixed56x32',\n        'ufixed56x40',\n        'ufixed56x48',\n        'ufixed56x56',\n        'ufixed56x64',\n        'ufixed56x72',\n        'ufixed56x80',\n        'ufixed56x88',\n        'ufixed56x96',\n        'ufixed56x104',\n        'ufixed56x112',\n        'ufixed56x120',\n        'ufixed56x128',\n        'ufixed56x136',\n        'ufixed56x144',\n        'ufixed56x152',\n        'ufixed56x160',\n        'ufixed56x168',\n        'ufixed56x176',\n        'ufixed56x184',\n        'ufixed56x192',\n        'ufixed56x200',\n        'ufixed64x8',\n        'ufixed64x16',\n        'ufixed64x24',\n        'ufixed64x32',\n        'ufixed64x40',\n        'ufixed64x48',\n        'ufixed64x56',\n        'ufixed64x64',\n        'ufixed64x72',\n        'ufixed64x80',\n        'ufixed64x88',\n        'ufixed64x96',\n        'ufixed64x104',\n        'ufixed64x112',\n        'ufixed64x120',\n        'ufixed64x128',\n        'ufixed64x136',\n        'ufixed64x144',\n        'ufixed64x152',\n        'ufixed64x160',\n        'ufixed64x168',\n        'ufixed64x176',\n        'ufixed64x184',\n        'ufixed64x192',\n        'ufixed72x8',\n        'ufixed72x16',\n        'ufixed72x24',\n        'ufixed72x32',\n        'ufixed72x40',\n        'ufixed72x48',\n        'ufixed72x56',\n        'ufixed72x64',\n        'ufixed72x72',\n        'ufixed72x80',\n        'ufixed72x88',\n        'ufixed72x96',\n        'ufixed72x104',\n        'ufixed72x112',\n        'ufixed72x120',\n        'ufixed72x128',\n        'ufixed72x136',\n        'ufixed72x144',\n        'ufixed72x152',\n        'ufixed72x160',\n        'ufixed72x168',\n        'ufixed72x176',\n        'ufixed72x184',\n        'ufixed80x8',\n        'ufixed80x16',\n        'ufixed80x24',\n        'ufixed80x32',\n        'ufixed80x40',\n        'ufixed80x48',\n        'ufixed80x56',\n        'ufixed80x64',\n        'ufixed80x72',\n        'ufixed80x80',\n        'ufixed80x88',\n        'ufixed80x96',\n        'ufixed80x104',\n        'ufixed80x112',\n        'ufixed80x120',\n        'ufixed80x128',\n        'ufixed80x136',\n        'ufixed80x144',\n        'ufixed80x152',\n        'ufixed80x160',\n        'ufixed80x168',\n        'ufixed80x176',\n        'ufixed88x8',\n        'ufixed88x16',\n        'ufixed88x24',\n        'ufixed88x32',\n        'ufixed88x40',\n        'ufixed88x48',\n        'ufixed88x56',\n        'ufixed88x64',\n        'ufixed88x72',\n        'ufixed88x80',\n        'ufixed88x88',\n        'ufixed88x96',\n        'ufixed88x104',\n        'ufixed88x112',\n        'ufixed88x120',\n        'ufixed88x128',\n        'ufixed88x136',\n        'ufixed88x144',\n        'ufixed88x152',\n        'ufixed88x160',\n        'ufixed88x168',\n        'ufixed96x8',\n        'ufixed96x16',\n        'ufixed96x24',\n        'ufixed96x32',\n        'ufixed96x40',\n        'ufixed96x48',\n        'ufixed96x56',\n        'ufixed96x64',\n        'ufixed96x72',\n        'ufixed96x80',\n        'ufixed96x88',\n        'ufixed96x96',\n        'ufixed96x104',\n        'ufixed96x112',\n        'ufixed96x120',\n        'ufixed96x128',\n        'ufixed96x136',\n        'ufixed96x144',\n        'ufixed96x152',\n        'ufixed96x160',\n        'ufixed104x8',\n        'ufixed104x16',\n        'ufixed104x24',\n        'ufixed104x32',\n        'ufixed104x40',\n        'ufixed104x48',\n        'ufixed104x56',\n        'ufixed104x64',\n        'ufixed104x72',\n        'ufixed104x80',\n        'ufixed104x88',\n        'ufixed104x96',\n        'ufixed104x104',\n        'ufixed104x112',\n        'ufixed104x120',\n        'ufixed104x128',\n        'ufixed104x136',\n        'ufixed104x144',\n        'ufixed104x152',\n        'ufixed112x8',\n        'ufixed112x16',\n        'ufixed112x24',\n        'ufixed112x32',\n        'ufixed112x40',\n        'ufixed112x48',\n        'ufixed112x56',\n        'ufixed112x64',\n        'ufixed112x72',\n        'ufixed112x80',\n        'ufixed112x88',\n        'ufixed112x96',\n        'ufixed112x104',\n        'ufixed112x112',\n        'ufixed112x120',\n        'ufixed112x128',\n        'ufixed112x136',\n        'ufixed112x144',\n        'ufixed120x8',\n        'ufixed120x16',\n        'ufixed120x24',\n        'ufixed120x32',\n        'ufixed120x40',\n        'ufixed120x48',\n        'ufixed120x56',\n        'ufixed120x64',\n        'ufixed120x72',\n        'ufixed120x80',\n        'ufixed120x88',\n        'ufixed120x96',\n        'ufixed120x104',\n        'ufixed120x112',\n        'ufixed120x120',\n        'ufixed120x128',\n        'ufixed120x136',\n        'ufixed128x8',\n        'ufixed128x16',\n        'ufixed128x24',\n        'ufixed128x32',\n        'ufixed128x40',\n        'ufixed128x48',\n        'ufixed128x56',\n        'ufixed128x64',\n        'ufixed128x72',\n        'ufixed128x80',\n        'ufixed128x88',\n        'ufixed128x96',\n        'ufixed128x104',\n        'ufixed128x112',\n        'ufixed128x120',\n        'ufixed128x128',\n        'ufixed136x8',\n        'ufixed136x16',\n        'ufixed136x24',\n        'ufixed136x32',\n        'ufixed136x40',\n        'ufixed136x48',\n        'ufixed136x56',\n        'ufixed136x64',\n        'ufixed136x72',\n        'ufixed136x80',\n        'ufixed136x88',\n        'ufixed136x96',\n        'ufixed136x104',\n        'ufixed136x112',\n        'ufixed136x120',\n        'ufixed144x8',\n        'ufixed144x16',\n        'ufixed144x24',\n        'ufixed144x32',\n        'ufixed144x40',\n        'ufixed144x48',\n        'ufixed144x56',\n        'ufixed144x64',\n        'ufixed144x72',\n        'ufixed144x80',\n        'ufixed144x88',\n        'ufixed144x96',\n        'ufixed144x104',\n        'ufixed144x112',\n        'ufixed152x8',\n        'ufixed152x16',\n        'ufixed152x24',\n        'ufixed152x32',\n        'ufixed152x40',\n        'ufixed152x48',\n        'ufixed152x56',\n        'ufixed152x64',\n        'ufixed152x72',\n        'ufixed152x80',\n        'ufixed152x88',\n        'ufixed152x96',\n        'ufixed152x104',\n        'ufixed160x8',\n        'ufixed160x16',\n        'ufixed160x24',\n        'ufixed160x32',\n        'ufixed160x40',\n        'ufixed160x48',\n        'ufixed160x56',\n        'ufixed160x64',\n        'ufixed160x72',\n        'ufixed160x80',\n        'ufixed160x88',\n        'ufixed160x96',\n        'ufixed168x8',\n        'ufixed168x16',\n        'ufixed168x24',\n        'ufixed168x32',\n        'ufixed168x40',\n        'ufixed168x48',\n        'ufixed168x56',\n        'ufixed168x64',\n        'ufixed168x72',\n        'ufixed168x80',\n        'ufixed168x88',\n        'ufixed176x8',\n        'ufixed176x16',\n        'ufixed176x24',\n        'ufixed176x32',\n        'ufixed176x40',\n        'ufixed176x48',\n        'ufixed176x56',\n        'ufixed176x64',\n        'ufixed176x72',\n        'ufixed176x80',\n        'ufixed184x8',\n        'ufixed184x16',\n        'ufixed184x24',\n        'ufixed184x32',\n        'ufixed184x40',\n        'ufixed184x48',\n        'ufixed184x56',\n        'ufixed184x64',\n        'ufixed184x72',\n        'ufixed192x8',\n        'ufixed192x16',\n        'ufixed192x24',\n        'ufixed192x32',\n        'ufixed192x40',\n        'ufixed192x48',\n        'ufixed192x56',\n        'ufixed192x64',\n        'ufixed200x8',\n        'ufixed200x16',\n        'ufixed200x24',\n        'ufixed200x32',\n        'ufixed200x40',\n        'ufixed200x48',\n        'ufixed200x56',\n        'ufixed208x8',\n        'ufixed208x16',\n        'ufixed208x24',\n        'ufixed208x32',\n        'ufixed208x40',\n        'ufixed208x48',\n        'ufixed216x8',\n        'ufixed216x16',\n        'ufixed216x24',\n        'ufixed216x32',\n        'ufixed216x40',\n        'ufixed224x8',\n        'ufixed224x16',\n        'ufixed224x24',\n        'ufixed224x32',\n        'ufixed232x8',\n        'ufixed232x16',\n        'ufixed232x24',\n        'ufixed240x8',\n        'ufixed240x16',\n        'ufixed248x8',\n        'event',\n        'enum',\n        'let',\n        'mapping',\n        'private',\n        'public',\n        'external',\n        'inherited',\n        'payable',\n        'true',\n        'false',\n        'var',\n        'import',\n        'constant',\n        'if',\n        'else',\n        'for',\n        'else',\n        'for',\n        'while',\n        'do',\n        'break',\n        'continue',\n        'throw',\n        'returns',\n        'return',\n        'suicide',\n        'new',\n        'is',\n        'this',\n        'super'\n    ],\n    operators: [\n        '=',\n        '>',\n        '<',\n        '!',\n        '~',\n        '?',\n        ':',\n        '==',\n        '<=',\n        '>=',\n        '!=',\n        '&&',\n        '||',\n        '++',\n        '--',\n        '+',\n        '-',\n        '*',\n        '/',\n        '&',\n        '|',\n        '^',\n        '%',\n        '<<',\n        '>>',\n        '>>>',\n        '+=',\n        '-=',\n        '*=',\n        '/=',\n        '&=',\n        '|=',\n        '^=',\n        '%=',\n        '<<=',\n        '>>=',\n        '>>>='\n    ],\n    // we include these common regular expressions\n    symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n    escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n    integersuffix: /(ll|LL|u|U|l|L)?(ll|LL|u|U|l|L)?/,\n    floatsuffix: /[fFlL]?/,\n    // The main tokenizer for our languages\n    tokenizer: {\n        root: [\n            // identifiers and keywords\n            [\n                /[a-zA-Z_]\\w*/,\n                {\n                    cases: {\n                        '@keywords': { token: 'keyword.$0' },\n                        '@default': 'identifier'\n                    }\n                }\n            ],\n            // whitespace\n            { include: '@whitespace' },\n            // [[ attributes ]].\n            [/\\[\\[.*\\]\\]/, 'annotation'],\n            // Preprocessor directive\n            [/^\\s*#\\w+/, 'keyword'],\n            //DataTypes\n            [/int\\d*/, 'keyword'],\n            // delimiters and operators\n            [/[{}()\\[\\]]/, '@brackets'],\n            [/[<>](?!@symbols)/, '@brackets'],\n            [\n                /@symbols/,\n                {\n                    cases: {\n                        '@operators': 'delimiter',\n                        '@default': ''\n                    }\n                }\n            ],\n            // numbers\n            [/\\d*\\d+[eE]([\\-+]?\\d+)?(@floatsuffix)/, 'number.float'],\n            [/\\d*\\.\\d+([eE][\\-+]?\\d+)?(@floatsuffix)/, 'number.float'],\n            [/0[xX][0-9a-fA-F']*[0-9a-fA-F](@integersuffix)/, 'number.hex'],\n            [/0[0-7']*[0-7](@integersuffix)/, 'number.octal'],\n            [/0[bB][0-1']*[0-1](@integersuffix)/, 'number.binary'],\n            [/\\d[\\d']*\\d(@integersuffix)/, 'number'],\n            [/\\d(@integersuffix)/, 'number'],\n            // delimiter: after number because of .\\d floats\n            [/[;,.]/, 'delimiter'],\n            // strings\n            [/\"([^\"\\\\]|\\\\.)*$/, 'string.invalid'],\n            [/\"/, 'string', '@string'],\n            // characters\n            [/'[^\\\\']'/, 'string'],\n            [/(')(@escapes)(')/, ['string', 'string.escape', 'string']],\n            [/'/, 'string.invalid']\n        ],\n        whitespace: [\n            [/[ \\t\\r\\n]+/, ''],\n            [/\\/\\*\\*(?!\\/)/, 'comment.doc', '@doccomment'],\n            [/\\/\\*/, 'comment', '@comment'],\n            [/\\/\\/.*$/, 'comment']\n        ],\n        comment: [\n            [/[^\\/*]+/, 'comment'],\n            [/\\*\\//, 'comment', '@pop'],\n            [/[\\/*]/, 'comment']\n        ],\n        //Identical copy of comment above, except for the addition of .doc\n        doccomment: [\n            [/[^\\/*]+/, 'comment.doc'],\n            [/\\*\\//, 'comment.doc', '@pop'],\n            [/[\\/*]/, 'comment.doc']\n        ],\n        string: [\n            [/[^\\\\\"]+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/\\\\./, 'string.escape.invalid'],\n            [/\"/, 'string', '@pop']\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC7B;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EAC1D;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACN,EAAE,OAAO,mBAAmB,MAAM,KAAK,OAAO,IAAI;AAAA,IAClD,EAAE,OAAO,yBAAyB,MAAM,KAAK,OAAO,IAAI;AAAA,IACxD,EAAE,OAAO,oBAAoB,MAAM,KAAK,OAAO,IAAI;AAAA,IACnD,EAAE,OAAO,mBAAmB,MAAM,KAAK,OAAO,IAAI;AAAA,EACtD;AAAA,EACA,UAAU;AAAA;AAAA,IAEN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,WAAW;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA;AAAA,EAEA,SAAS;AAAA,EACT,SAAS;AAAA,EACT,eAAe;AAAA,EACf,aAAa;AAAA;AAAA,EAEb,WAAW;AAAA,IACP,MAAM;AAAA;AAAA,MAEF;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,aAAa,EAAE,OAAO,aAAa;AAAA,YACnC,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB,CAAC,cAAc,YAAY;AAAA;AAAA,MAE3B,CAAC,YAAY,SAAS;AAAA;AAAA,MAEtB,CAAC,UAAU,SAAS;AAAA;AAAA,MAEpB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,oBAAoB,WAAW;AAAA,MAChC;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,cAAc;AAAA,YACd,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,CAAC,wCAAwC,cAAc;AAAA,MACvD,CAAC,0CAA0C,cAAc;AAAA,MACzD,CAAC,iDAAiD,YAAY;AAAA,MAC9D,CAAC,iCAAiC,cAAc;AAAA,MAChD,CAAC,qCAAqC,eAAe;AAAA,MACrD,CAAC,8BAA8B,QAAQ;AAAA,MACvC,CAAC,sBAAsB,QAAQ;AAAA;AAAA,MAE/B,CAAC,SAAS,WAAW;AAAA;AAAA,MAErB,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,KAAK,UAAU,SAAS;AAAA;AAAA,MAEzB,CAAC,YAAY,QAAQ;AAAA,MACrB,CAAC,oBAAoB,CAAC,UAAU,iBAAiB,QAAQ,CAAC;AAAA,MAC1D,CAAC,KAAK,gBAAgB;AAAA,IAC1B;AAAA,IACA,YAAY;AAAA,MACR,CAAC,cAAc,EAAE;AAAA,MACjB,CAAC,gBAAgB,eAAe,aAAa;AAAA,MAC7C,CAAC,QAAQ,WAAW,UAAU;AAAA,MAC9B,CAAC,WAAW,SAAS;AAAA,IACzB;AAAA,IACA,SAAS;AAAA,MACL,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,SAAS,SAAS;AAAA,IACvB;AAAA;AAAA,IAEA,YAAY;AAAA,MACR,CAAC,WAAW,aAAa;AAAA,MACzB,CAAC,QAAQ,eAAe,MAAM;AAAA,MAC9B,CAAC,SAAS,aAAa;AAAA,IAC3B;AAAA,IACA,QAAQ;AAAA,MACJ,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,UAAU,MAAM;AAAA,IAC1B;AAAA,EACJ;AACJ;", "names": []}