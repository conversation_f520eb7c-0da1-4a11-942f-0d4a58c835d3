{"version": 3, "sources": ["../../lodash/_baseInRange.js", "../../lodash/inRange.js", "../../amis/esm/renderers/Form/Checkboxes.js"], "sourcesContent": ["/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * The base implementation of `_.inRange` which doesn't coerce arguments.\n *\n * @private\n * @param {number} number The number to check.\n * @param {number} start The start of the range.\n * @param {number} end The end of the range.\n * @returns {boolean} Returns `true` if `number` is in the range, else `false`.\n */\nfunction baseInRange(number, start, end) {\n  return number >= nativeMin(start, end) && number < nativeMax(start, end);\n}\n\nmodule.exports = baseInRange;\n", "var baseInRange = require('./_baseInRange'),\n    toFinite = require('./toFinite'),\n    toNumber = require('./toNumber');\n\n/**\n * Checks if `n` is between `start` and up to, but not including, `end`. If\n * `end` is not specified, it's set to `start` with `start` then set to `0`.\n * If `start` is greater than `end` the params are swapped to support\n * negative ranges.\n *\n * @static\n * @memberOf _\n * @since 3.3.0\n * @category Number\n * @param {number} number The number to check.\n * @param {number} [start=0] The start of the range.\n * @param {number} end The end of the range.\n * @returns {boolean} Returns `true` if `number` is in the range, else `false`.\n * @see _.range, _.rangeRight\n * @example\n *\n * _.inRange(3, 2, 4);\n * // => true\n *\n * _.inRange(4, 8);\n * // => true\n *\n * _.inRange(4, 2);\n * // => false\n *\n * _.inRange(2, 2);\n * // => false\n *\n * _.inRange(1.2, 2);\n * // => true\n *\n * _.inRange(5.2, 4);\n * // => false\n *\n * _.inRange(-3, -2, -6);\n * // => true\n */\nfunction inRange(number, start, end) {\n  start = toFinite(start);\n  if (end === undefined) {\n    end = start;\n    start = 0;\n  } else {\n    end = toFinite(end);\n  }\n  number = toNumber(number);\n  return baseInRange(number, start, end);\n}\n\nmodule.exports = inRange;\n", "/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __assign, __decorate, __metadata } from 'tslib';\nimport React from 'react';\nimport inRange from 'lodash/inRange';\nimport { getVariable, createObject, hasAbility, columnsSplit, flattenTreeWithLeafNodes, formateCheckThemeCss, setThemeClassName, CustomStyle, autobind, OptionsControl } from 'amis-core';\nimport { Checkbox, Icon, Spinner } from 'amis-ui';\nimport { supportStatic } from './StaticHoc.js';\nimport debounce from 'lodash/debounce';\n\nvar CheckboxesControl = /** @class */ (function (_super) {\n    __extends(CheckboxesControl, _super);\n    function CheckboxesControl() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.checkboxRef = React.createRef();\n        _this.childRefs = [];\n        return _this;\n    }\n    CheckboxesControl.prototype.doAction = function (action, data, throwErrors) {\n        var _a, _b;\n        var _c = this.props, resetValue = _c.resetValue, onChange = _c.onChange, formStore = _c.formStore, store = _c.store, name = _c.name;\n        var actionType = action === null || action === void 0 ? void 0 : action.actionType;\n        if (actionType === 'clear') {\n            onChange('');\n        }\n        else if (actionType === 'reset') {\n            var pristineVal = (_b = getVariable((_a = formStore === null || formStore === void 0 ? void 0 : formStore.pristine) !== null && _a !== void 0 ? _a : store === null || store === void 0 ? void 0 : store.pristine, name)) !== null && _b !== void 0 ? _b : resetValue;\n            onChange(pristineVal !== null && pristineVal !== void 0 ? pristineVal : '');\n        }\n    };\n    CheckboxesControl.prototype.reload = function (subpath, query) {\n        var reload = this.props.reloadOptions;\n        reload && reload(subpath, query);\n    };\n    CheckboxesControl.prototype.handleAddClick = function () {\n        var onAdd = this.props.onAdd;\n        onAdd && onAdd();\n    };\n    CheckboxesControl.prototype.handleEditClick = function (e, item) {\n        var onEdit = this.props.onEdit;\n        e.preventDefault();\n        e.stopPropagation();\n        onEdit && onEdit(item);\n    };\n    CheckboxesControl.prototype.handleDeleteClick = function (e, item) {\n        var onDelete = this.props.onDelete;\n        e.preventDefault();\n        e.stopPropagation();\n        onDelete && onDelete(item);\n    };\n    CheckboxesControl.prototype.componentDidMount = function () {\n        this.updateBorderStyle();\n        var updateBorderStyleFn = debounce(this.updateBorderStyle, 100);\n        if (this.checkboxRef.current) {\n            // 监听容器宽度变化，更新边框样式\n            this.checkboxRefObserver = new ResizeObserver(updateBorderStyleFn);\n            this.checkboxRefObserver.observe(this.checkboxRef.current);\n        }\n    };\n    CheckboxesControl.prototype.componentWillUnmount = function () {\n        var _a;\n        (_a = this.checkboxRefObserver) === null || _a === void 0 ? void 0 : _a.disconnect();\n    };\n    CheckboxesControl.prototype.updateBorderStyle = function () {\n        if (this.props.optionType !== 'button') {\n            return;\n        }\n        if (!this.childRefs.length) {\n            return;\n        }\n        var children = this.childRefs.map(function (item) { return item === null || item === void 0 ? void 0 : item.ref; });\n        var lastOffsetTop = children[0].labelRef.current.offsetTop;\n        var options = [];\n        var len = children.length;\n        options[0] = 'first';\n        var lastLineStart = 0;\n        for (var i = 1; i < len; i++) {\n            var item = children[i];\n            // 如果当前元素的 offsetTop 与上一个元素的 offsetTop 不同，则说明是新的一行\n            var currentOffsetTop = item.labelRef.current.offsetTop;\n            options[i] = '';\n            if (currentOffsetTop !== lastOffsetTop) {\n                options[i] = 'first';\n                options[i - 1] += ' last';\n                lastLineStart = i;\n                lastOffsetTop = currentOffsetTop;\n            }\n        }\n        options[len - 1] += ' last';\n        options.forEach(function (option, index) {\n            if (index >= lastLineStart) {\n                option += ' last-line';\n            }\n            children[index].setClassName(option);\n        });\n    };\n    CheckboxesControl.prototype.renderGroup = function (option, index) {\n        var _this = this;\n        var _a;\n        var _b = this.props, cx = _b.classnames, labelField = _b.labelField;\n        if (!((_a = option.children) === null || _a === void 0 ? void 0 : _a.length)) {\n            return null;\n        }\n        var children = option.children.map(function (option, index) {\n            return _this.renderItem(option, index);\n        });\n        var body = this.columnsSplit(children);\n        return (React.createElement(\"div\", { key: 'group-' + index, className: cx('CheckboxesControl-group', option.className) },\n            React.createElement(\"label\", { className: cx('CheckboxesControl-groupLabel', option.labelClassName) }, option[labelField || 'label']),\n            body));\n    };\n    CheckboxesControl.prototype.addChildRefs = function (el) {\n        el && this.childRefs.push(el);\n    };\n    CheckboxesControl.prototype.renderItem = function (option, index) {\n        var _this = this;\n        var _a;\n        if ((_a = option.children) === null || _a === void 0 ? void 0 : _a.length) {\n            return this.renderGroup(option, index);\n        }\n        var _b = this.props, render = _b.render, itemClassName = _b.itemClassName, onToggle = _b.onToggle, selectedOptions = _b.selectedOptions, disabled = _b.disabled, inline = _b.inline, labelClassName = _b.labelClassName, labelField = _b.labelField, removable = _b.removable, editable = _b.editable, __ = _b.translate, optionType = _b.optionType, menuTpl = _b.menuTpl, data = _b.data, testIdBuilder = _b.testIdBuilder;\n        var labelText = String(option[labelField || 'label']);\n        var optionLabelClassName = option['labelClassName'];\n        var itemTestIdBuilder = testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild('item-' + labelText || index);\n        return (React.createElement(Checkbox, { className: itemClassName, key: index, onChange: function () { return onToggle(option); }, checked: !!~selectedOptions.indexOf(option), disabled: disabled || option.disabled, inline: inline, labelClassName: optionLabelClassName || labelClassName, description: option.description, optionType: optionType, testIdBuilder: itemTestIdBuilder, ref: this.addChildRefs },\n            menuTpl\n                ? render(\"checkboxes/\".concat(index), menuTpl, {\n                    data: createObject(data, option)\n                })\n                : labelText,\n            removable && hasAbility(option, 'removable') ? (React.createElement(\"a\", { \"data-tooltip\": __('Select.clear'), \"data-position\": \"left\" },\n                React.createElement(Icon, { icon: \"minus\", className: \"icon\", onClick: function (e) { return _this.handleDeleteClick(e, option); } }))) : null,\n            editable && hasAbility(option, 'editable') ? (React.createElement(\"a\", { \"data-tooltip\": \"\\u7F16\\u8F91\", \"data-position\": \"left\" },\n                React.createElement(Icon, { icon: \"pencil\", className: \"icon\", onClick: function (e) { return _this.handleEditClick(e, option); } }))) : null));\n    };\n    CheckboxesControl.prototype.columnsSplit = function (body) {\n        var _a = this.props, columnsCount = _a.columnsCount, cx = _a.classnames;\n        var result = [];\n        var tmp = [];\n        body.forEach(function (node) {\n            // 如果有分组，组内单独分列\n            if (node && node.key && String(node.key).startsWith('group')) {\n                // 夹杂在分组间的无分组选项，分别成块\n                if (tmp.length) {\n                    result.push(columnsSplit(tmp, cx, columnsCount));\n                    tmp = [];\n                }\n                result.push(node);\n            }\n            else {\n                tmp.push(node);\n            }\n        });\n        // 收尾\n        tmp.length && result.push(columnsSplit(tmp, cx, columnsCount));\n        return result;\n    };\n    CheckboxesControl.prototype.render = function () {\n        var _this = this;\n        var _a = this.props, className = _a.className, style = _a.style, disabled = _a.disabled, placeholder = _a.placeholder, options = _a.options, inline = _a.inline, columnsCount = _a.columnsCount, selectedOptions = _a.selectedOptions, onToggle = _a.onToggle, onToggleAll = _a.onToggleAll, checkAll = _a.checkAll, checkAllText = _a.checkAllText, cx = _a.classnames, itemClassName = _a.itemClassName, labelClassName = _a.labelClassName, creatable = _a.creatable, addApi = _a.addApi, createBtnLabel = _a.createBtnLabel, __ = _a.translate, optionType = _a.optionType, loading = _a.loading, loadingConfig = _a.loadingConfig, themeCss = _a.themeCss, id = _a.id, env = _a.env, ns = _a.classPrefix;\n        var body = [];\n        if (options && options.length) {\n            body = options.map(function (option, key) { return _this.renderItem(option, key); });\n        }\n        if (checkAll && body.length && optionType === 'default') {\n            body.unshift(React.createElement(Checkbox, { key: \"checkall\", className: itemClassName, onChange: onToggleAll, checked: !!selectedOptions.length, partial: inRange(selectedOptions.length, 0, flattenTreeWithLeafNodes(options).length), disabled: disabled, inline: inline, labelClassName: labelClassName }, checkAllText !== null && checkAllText !== void 0 ? checkAllText : __('Checkboxes.selectAll')));\n        }\n        body = this.columnsSplit(body);\n        var css = formateCheckThemeCss(themeCss, 'checkboxes');\n        return (React.createElement(\"div\", { className: cx(\"CheckboxesControl\", className, setThemeClassName(__assign(__assign({}, this.props), { name: [\n                    'checkboxesControlClassName',\n                    'checkboxesControlCheckedClassName',\n                    'checkboxesClassName',\n                    'checkboxesCheckedClassName',\n                    'checkboxesInnerClassName',\n                    'checkboxesShowClassName'\n                ], id: id, themeCss: css }))), ref: this.checkboxRef },\n            body && body.length ? (body) : loading ? null : (React.createElement(\"span\", { className: \"Form-placeholder\" }, __(placeholder))),\n            loading ? (React.createElement(Spinner, { show: true, icon: \"reload\", size: \"sm\", spinnerClassName: cx('Checkboxes-spinner'), loadingConfig: loadingConfig })) : null,\n            (creatable || addApi) && !disabled ? (React.createElement(\"a\", { className: cx('Checkboxes-addBtn'), onClick: this.handleAddClick },\n                React.createElement(Icon, { icon: \"plus\", className: \"icon\" }),\n                __(createBtnLabel))) : null,\n            React.createElement(CustomStyle, __assign({}, this.props, { config: {\n                    themeCss: css,\n                    classNames: [\n                        {\n                            key: 'checkboxesControlClassName',\n                            weights: {\n                                default: {\n                                    inner: \".\".concat(ns, \"Checkbox:not(.checked):not(.disabled)\")\n                                },\n                                hover: {\n                                    suf: \" .\".concat(ns, \"Checkbox:not(.disabled):not(.checked)\")\n                                },\n                                disabled: {\n                                    inner: \".\".concat(ns, \"Checkbox.disabled:not(.checked)\")\n                                }\n                            }\n                        },\n                        {\n                            key: 'checkboxesControlCheckedClassName',\n                            weights: {\n                                default: {\n                                    inner: \".\".concat(ns, \"Checkbox.checked:not(.disabled)\")\n                                },\n                                hover: {\n                                    suf: \" .\".concat(ns, \"Checkbox.checked:not(.disabled)\")\n                                },\n                                disabled: {\n                                    inner: \".\".concat(ns, \"Checkbox.checked.disabled\")\n                                }\n                            }\n                        },\n                        {\n                            key: 'checkboxesClassName',\n                            weights: {\n                                default: {\n                                    inner: \".\".concat(ns, \"Checkbox:not(.checked):not(.disabled) > i\")\n                                },\n                                hover: {\n                                    suf: \" .\".concat(ns, \"Checkbox:not(.disabled):not(.checked)\"),\n                                    inner: '> i'\n                                },\n                                disabled: {\n                                    inner: \".\".concat(ns, \"Checkbox.disabled:not(.checked) > i\")\n                                }\n                            }\n                        },\n                        {\n                            key: 'checkboxesCheckedClassName',\n                            weights: {\n                                default: {\n                                    inner: \".\".concat(ns, \"Checkbox:not(.disabled) > i\")\n                                },\n                                hover: {\n                                    suf: \" .\".concat(ns, \"Checkbox:not(.disabled)\"),\n                                    inner: '> i'\n                                },\n                                disabled: {\n                                    inner: \".\".concat(ns, \"Checkbox.disabled > i\")\n                                }\n                            }\n                        },\n                        {\n                            key: 'checkboxesInnerClassName',\n                            weights: {\n                                default: {\n                                    inner: \".\".concat(ns, \"Checkbox:not(.disabled) > i .icon\")\n                                },\n                                hover: {\n                                    suf: \" .\".concat(ns, \"Checkbox:not(.disabled)\"),\n                                    inner: '> i .icon'\n                                },\n                                disabled: {\n                                    inner: \".\".concat(ns, \"Checkbox.disabled > i .icon\")\n                                }\n                            }\n                        },\n                        {\n                            key: 'checkboxesShowClassName',\n                            weights: {\n                                default: {\n                                    inner: \".\".concat(ns, \"Checkbox > i\")\n                                }\n                            }\n                        }\n                    ],\n                    id: id\n                }, env: env }))));\n    };\n    CheckboxesControl.defaultProps = {\n        columnsCount: 1,\n        multiple: true,\n        placeholder: 'placeholder.noOption',\n        creatable: false,\n        inline: true,\n        createBtnLabel: 'Select.createLabel',\n        optionType: 'default'\n    };\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", []),\n        __metadata(\"design:returntype\", void 0)\n    ], CheckboxesControl.prototype, \"handleAddClick\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Event, Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], CheckboxesControl.prototype, \"handleEditClick\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Event, Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], CheckboxesControl.prototype, \"handleDeleteClick\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", []),\n        __metadata(\"design:returntype\", void 0)\n    ], CheckboxesControl.prototype, \"updateBorderStyle\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], CheckboxesControl.prototype, \"addChildRefs\", null);\n    __decorate([\n        supportStatic(),\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", []),\n        __metadata(\"design:returntype\", void 0)\n    ], CheckboxesControl.prototype, \"render\", null);\n    return CheckboxesControl;\n}(React.Component));\nvar CheckboxesControlRenderer = /** @class */ (function (_super) {\n    __extends(CheckboxesControlRenderer, _super);\n    function CheckboxesControlRenderer() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CheckboxesControlRenderer = __decorate([\n        OptionsControl({\n            type: 'checkboxes',\n            sizeMutable: false,\n            thin: true\n        })\n    ], CheckboxesControlRenderer);\n    return CheckboxesControlRenderer;\n}(CheckboxesControl));\n\nexport { CheckboxesControlRenderer, CheckboxesControl as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AACA,QAAI,YAAY,KAAK;AAArB,QACI,YAAY,KAAK;AAWrB,aAAS,YAAY,QAAQ,OAAO,KAAK;AACvC,aAAO,UAAU,UAAU,OAAO,GAAG,KAAK,SAAS,UAAU,OAAO,GAAG;AAAA,IACzE;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAAA,QAAI,cAAc;AAAlB,QACI,WAAW;AADf,QAEI,WAAW;AAwCf,aAASA,SAAQ,QAAQ,OAAO,KAAK;AACnC,cAAQ,SAAS,KAAK;AACtB,UAAI,QAAQ,QAAW;AACrB,cAAM;AACN,gBAAQ;AAAA,MACV,OAAO;AACL,cAAM,SAAS,GAAG;AAAA,MACpB;AACA,eAAS,SAAS,MAAM;AACxB,aAAO,YAAY,QAAQ,OAAO,GAAG;AAAA,IACvC;AAEA,WAAO,UAAUA;AAAA;AAAA;;;AC/CjB,mBAAkB;AAClB,qBAAoB;AAIpB,sBAAqB;AAErB,IAAI;AAAA;AAAA,EAAmC,SAAU,QAAQ;AACrD,cAAUC,oBAAmB,MAAM;AACnC,aAASA,qBAAoB;AACzB,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,cAAc,aAAAC,QAAM,UAAU;AACpC,YAAM,YAAY,CAAC;AACnB,aAAO;AAAA,IACX;AACA,IAAAD,mBAAkB,UAAU,WAAW,SAAU,QAAQ,MAAM,aAAa;AACxE,UAAI,IAAI;AACR,UAAI,KAAK,KAAK,OAAO,aAAa,GAAG,YAAY,WAAW,GAAG,UAAU,YAAY,GAAG,WAAW,QAAQ,GAAG,OAAO,OAAO,GAAG;AAC/H,UAAI,aAAa,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO;AACxE,UAAI,eAAe,SAAS;AACxB,iBAAS,EAAE;AAAA,MACf,WACS,eAAe,SAAS;AAC7B,YAAI,eAAe,KAAK,aAAa,KAAK,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,cAAc,QAAQ,OAAO,SAAS,KAAK,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,UAAU,IAAI,OAAO,QAAQ,OAAO,SAAS,KAAK;AAC3P,iBAAS,gBAAgB,QAAQ,gBAAgB,SAAS,cAAc,EAAE;AAAA,MAC9E;AAAA,IACJ;AACA,IAAAA,mBAAkB,UAAU,SAAS,SAAU,SAAS,OAAO;AAC3D,UAAI,SAAS,KAAK,MAAM;AACxB,gBAAU,OAAO,SAAS,KAAK;AAAA,IACnC;AACA,IAAAA,mBAAkB,UAAU,iBAAiB,WAAY;AACrD,UAAI,QAAQ,KAAK,MAAM;AACvB,eAAS,MAAM;AAAA,IACnB;AACA,IAAAA,mBAAkB,UAAU,kBAAkB,SAAU,GAAG,MAAM;AAC7D,UAAI,SAAS,KAAK,MAAM;AACxB,QAAE,eAAe;AACjB,QAAE,gBAAgB;AAClB,gBAAU,OAAO,IAAI;AAAA,IACzB;AACA,IAAAA,mBAAkB,UAAU,oBAAoB,SAAU,GAAG,MAAM;AAC/D,UAAI,WAAW,KAAK,MAAM;AAC1B,QAAE,eAAe;AACjB,QAAE,gBAAgB;AAClB,kBAAY,SAAS,IAAI;AAAA,IAC7B;AACA,IAAAA,mBAAkB,UAAU,oBAAoB,WAAY;AACxD,WAAK,kBAAkB;AACvB,UAAI,0BAAsB,gBAAAE,SAAS,KAAK,mBAAmB,GAAG;AAC9D,UAAI,KAAK,YAAY,SAAS;AAE1B,aAAK,sBAAsB,IAAI,eAAe,mBAAmB;AACjE,aAAK,oBAAoB,QAAQ,KAAK,YAAY,OAAO;AAAA,MAC7D;AAAA,IACJ;AACA,IAAAF,mBAAkB,UAAU,uBAAuB,WAAY;AAC3D,UAAI;AACJ,OAAC,KAAK,KAAK,yBAAyB,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW;AAAA,IACvF;AACA,IAAAA,mBAAkB,UAAU,oBAAoB,WAAY;AACxD,UAAI,KAAK,MAAM,eAAe,UAAU;AACpC;AAAA,MACJ;AACA,UAAI,CAAC,KAAK,UAAU,QAAQ;AACxB;AAAA,MACJ;AACA,UAAI,WAAW,KAAK,UAAU,IAAI,SAAUG,OAAM;AAAE,eAAOA,UAAS,QAAQA,UAAS,SAAS,SAASA,MAAK;AAAA,MAAK,CAAC;AAClH,UAAI,gBAAgB,SAAS,CAAC,EAAE,SAAS,QAAQ;AACjD,UAAI,UAAU,CAAC;AACf,UAAI,MAAM,SAAS;AACnB,cAAQ,CAAC,IAAI;AACb,UAAI,gBAAgB;AACpB,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,YAAI,OAAO,SAAS,CAAC;AAErB,YAAI,mBAAmB,KAAK,SAAS,QAAQ;AAC7C,gBAAQ,CAAC,IAAI;AACb,YAAI,qBAAqB,eAAe;AACpC,kBAAQ,CAAC,IAAI;AACb,kBAAQ,IAAI,CAAC,KAAK;AAClB,0BAAgB;AAChB,0BAAgB;AAAA,QACpB;AAAA,MACJ;AACA,cAAQ,MAAM,CAAC,KAAK;AACpB,cAAQ,QAAQ,SAAU,QAAQ,OAAO;AACrC,YAAI,SAAS,eAAe;AACxB,oBAAU;AAAA,QACd;AACA,iBAAS,KAAK,EAAE,aAAa,MAAM;AAAA,MACvC,CAAC;AAAA,IACL;AACA,IAAAH,mBAAkB,UAAU,cAAc,SAAU,QAAQ,OAAO;AAC/D,UAAI,QAAQ;AACZ,UAAI;AACJ,UAAI,KAAK,KAAK,OAAO,KAAK,GAAG,YAAY,aAAa,GAAG;AACzD,UAAI,GAAG,KAAK,OAAO,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS;AAC1E,eAAO;AAAA,MACX;AACA,UAAI,WAAW,OAAO,SAAS,IAAI,SAAUI,SAAQC,QAAO;AACxD,eAAO,MAAM,WAAWD,SAAQC,MAAK;AAAA,MACzC,CAAC;AACD,UAAI,OAAO,KAAK,aAAa,QAAQ;AACrC,aAAQ,aAAAJ,QAAM;AAAA,QAAc;AAAA,QAAO,EAAE,KAAK,WAAW,OAAO,WAAW,GAAG,2BAA2B,OAAO,SAAS,EAAE;AAAA,QACnH,aAAAA,QAAM,cAAc,SAAS,EAAE,WAAW,GAAG,gCAAgC,OAAO,cAAc,EAAE,GAAG,OAAO,cAAc,OAAO,CAAC;AAAA,QACpI;AAAA,MAAI;AAAA,IACZ;AACA,IAAAD,mBAAkB,UAAU,eAAe,SAAU,IAAI;AACrD,YAAM,KAAK,UAAU,KAAK,EAAE;AAAA,IAChC;AACA,IAAAA,mBAAkB,UAAU,aAAa,SAAU,QAAQ,OAAO;AAC9D,UAAI,QAAQ;AACZ,UAAI;AACJ,WAAK,KAAK,OAAO,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ;AACvE,eAAO,KAAK,YAAY,QAAQ,KAAK;AAAA,MACzC;AACA,UAAI,KAAK,KAAK,OAAO,SAAS,GAAG,QAAQ,gBAAgB,GAAG,eAAe,WAAW,GAAG,UAAU,kBAAkB,GAAG,iBAAiB,WAAW,GAAG,UAAU,SAAS,GAAG,QAAQ,iBAAiB,GAAG,gBAAgB,aAAa,GAAG,YAAY,YAAY,GAAG,WAAW,WAAW,GAAG,UAAU,KAAK,GAAG,WAAW,aAAa,GAAG,YAAY,UAAU,GAAG,SAAS,OAAO,GAAG,MAAM,gBAAgB,GAAG;AAC/Y,UAAI,YAAY,OAAO,OAAO,cAAc,OAAO,CAAC;AACpD,UAAI,uBAAuB,OAAO,gBAAgB;AAClD,UAAI,oBAAoB,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,SAAS,UAAU,aAAa,KAAK;AACzI,aAAQ,aAAAC,QAAM;AAAA,QAAc;AAAA,QAAU,EAAE,WAAW,eAAe,KAAK,OAAO,UAAU,WAAY;AAAE,iBAAO,SAAS,MAAM;AAAA,QAAG,GAAG,SAAS,CAAC,CAAC,CAAC,gBAAgB,QAAQ,MAAM,GAAG,UAAU,YAAY,OAAO,UAAU,QAAgB,gBAAgB,wBAAwB,gBAAgB,aAAa,OAAO,aAAa,YAAwB,eAAe,mBAAmB,KAAK,KAAK,aAAa;AAAA,QAC5Y,UACM,OAAO,cAAc,OAAO,KAAK,GAAG,SAAS;AAAA,UAC3C,MAAM,aAAa,MAAM,MAAM;AAAA,QACnC,CAAC,IACC;AAAA,QACN,aAAa,WAAW,QAAQ,WAAW,IAAK,aAAAA,QAAM;AAAA,UAAc;AAAA,UAAK,EAAE,gBAAgB,GAAG,cAAc,GAAG,iBAAiB,OAAO;AAAA,UACnI,aAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,SAAS,WAAW,QAAQ,SAAS,SAAU,GAAG;AAAE,mBAAO,MAAM,kBAAkB,GAAG,MAAM;AAAA,UAAG,EAAE,CAAC;AAAA,QAAC,IAAK;AAAA,QAC9I,YAAY,WAAW,QAAQ,UAAU,IAAK,aAAAA,QAAM;AAAA,UAAc;AAAA,UAAK,EAAE,gBAAgB,MAAgB,iBAAiB,OAAO;AAAA,UAC7H,aAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,UAAU,WAAW,QAAQ,SAAS,SAAU,GAAG;AAAE,mBAAO,MAAM,gBAAgB,GAAG,MAAM;AAAA,UAAG,EAAE,CAAC;AAAA,QAAC,IAAK;AAAA,MAAI;AAAA,IACzJ;AACA,IAAAD,mBAAkB,UAAU,eAAe,SAAU,MAAM;AACvD,UAAI,KAAK,KAAK,OAAO,eAAe,GAAG,cAAc,KAAK,GAAG;AAC7D,UAAI,SAAS,CAAC;AACd,UAAI,MAAM,CAAC;AACX,WAAK,QAAQ,SAAU,MAAM;AAEzB,YAAI,QAAQ,KAAK,OAAO,OAAO,KAAK,GAAG,EAAE,WAAW,OAAO,GAAG;AAE1D,cAAI,IAAI,QAAQ;AACZ,mBAAO,KAAK,aAAa,KAAK,IAAI,YAAY,CAAC;AAC/C,kBAAM,CAAC;AAAA,UACX;AACA,iBAAO,KAAK,IAAI;AAAA,QACpB,OACK;AACD,cAAI,KAAK,IAAI;AAAA,QACjB;AAAA,MACJ,CAAC;AAED,UAAI,UAAU,OAAO,KAAK,aAAa,KAAK,IAAI,YAAY,CAAC;AAC7D,aAAO;AAAA,IACX;AACA,IAAAA,mBAAkB,UAAU,SAAS,WAAY;AAC7C,UAAI,QAAQ;AACZ,UAAI,KAAK,KAAK,OAAO,YAAY,GAAG,WAAW,QAAQ,GAAG,OAAO,WAAW,GAAG,UAAU,cAAc,GAAG,aAAa,UAAU,GAAG,SAAS,SAAS,GAAG,QAAQ,eAAe,GAAG,cAAc,kBAAkB,GAAG,iBAAiB,WAAW,GAAG,UAAU,cAAc,GAAG,aAAa,WAAW,GAAG,UAAU,eAAe,GAAG,cAAc,KAAK,GAAG,YAAY,gBAAgB,GAAG,eAAe,iBAAiB,GAAG,gBAAgB,YAAY,GAAG,WAAW,SAAS,GAAG,QAAQ,iBAAiB,GAAG,gBAAgB,KAAK,GAAG,WAAW,aAAa,GAAG,YAAY,UAAU,GAAG,SAAS,gBAAgB,GAAG,eAAe,WAAW,GAAG,UAAU,KAAK,GAAG,IAAI,MAAM,GAAG,KAAK,KAAK,GAAG;AAClqB,UAAI,OAAO,CAAC;AACZ,UAAI,WAAW,QAAQ,QAAQ;AAC3B,eAAO,QAAQ,IAAI,SAAU,QAAQ,KAAK;AAAE,iBAAO,MAAM,WAAW,QAAQ,GAAG;AAAA,QAAG,CAAC;AAAA,MACvF;AACA,UAAI,YAAY,KAAK,UAAU,eAAe,WAAW;AACrD,aAAK,QAAQ,aAAAC,QAAM,cAAc,YAAU,EAAE,KAAK,YAAY,WAAW,eAAe,UAAU,aAAa,SAAS,CAAC,CAAC,gBAAgB,QAAQ,aAAS,eAAAK,SAAQ,gBAAgB,QAAQ,GAAG,yBAAyB,OAAO,EAAE,MAAM,GAAG,UAAoB,QAAgB,eAA+B,GAAG,iBAAiB,QAAQ,iBAAiB,SAAS,eAAe,GAAG,sBAAsB,CAAC,CAAC;AAAA,MAChZ;AACA,aAAO,KAAK,aAAa,IAAI;AAC7B,UAAI,MAAM,qBAAqB,UAAU,YAAY;AACrD,aAAQ,aAAAL,QAAM;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,GAAG,qBAAqB,WAAW,kBAAkB,SAAS,SAAS,CAAC,GAAG,KAAK,KAAK,GAAG,EAAE,MAAM;AAAA,UACpI;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ,GAAG,IAAQ,UAAU,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,KAAK,YAAY;AAAA,QACzD,QAAQ,KAAK,SAAU,OAAQ,UAAU,OAAQ,aAAAA,QAAM,cAAc,QAAQ,EAAE,WAAW,mBAAmB,GAAG,GAAG,WAAW,CAAC;AAAA,QAC/H,UAAW,aAAAA,QAAM,cAAc,WAAS,EAAE,MAAM,MAAM,MAAM,UAAU,MAAM,MAAM,kBAAkB,GAAG,oBAAoB,GAAG,cAA6B,CAAC,IAAK;AAAA,SAChK,aAAa,WAAW,CAAC,WAAY,aAAAA,QAAM;AAAA,UAAc;AAAA,UAAK,EAAE,WAAW,GAAG,mBAAmB,GAAG,SAAS,KAAK,eAAe;AAAA,UAC9H,aAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,QAAQ,WAAW,OAAO,CAAC;AAAA,UAC7D,GAAG,cAAc;AAAA,QAAC,IAAK;AAAA,QAC3B,aAAAA,QAAM,cAAc,aAAa,SAAS,CAAC,GAAG,KAAK,OAAO,EAAE,QAAQ;AAAA,UAC5D,UAAU;AAAA,UACV,YAAY;AAAA,YACR;AAAA,cACI,KAAK;AAAA,cACL,SAAS;AAAA,gBACL,SAAS;AAAA,kBACL,OAAO,IAAI,OAAO,IAAI,uCAAuC;AAAA,gBACjE;AAAA,gBACA,OAAO;AAAA,kBACH,KAAK,KAAK,OAAO,IAAI,uCAAuC;AAAA,gBAChE;AAAA,gBACA,UAAU;AAAA,kBACN,OAAO,IAAI,OAAO,IAAI,iCAAiC;AAAA,gBAC3D;AAAA,cACJ;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,cACL,SAAS;AAAA,gBACL,SAAS;AAAA,kBACL,OAAO,IAAI,OAAO,IAAI,iCAAiC;AAAA,gBAC3D;AAAA,gBACA,OAAO;AAAA,kBACH,KAAK,KAAK,OAAO,IAAI,iCAAiC;AAAA,gBAC1D;AAAA,gBACA,UAAU;AAAA,kBACN,OAAO,IAAI,OAAO,IAAI,2BAA2B;AAAA,gBACrD;AAAA,cACJ;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,cACL,SAAS;AAAA,gBACL,SAAS;AAAA,kBACL,OAAO,IAAI,OAAO,IAAI,2CAA2C;AAAA,gBACrE;AAAA,gBACA,OAAO;AAAA,kBACH,KAAK,KAAK,OAAO,IAAI,uCAAuC;AAAA,kBAC5D,OAAO;AAAA,gBACX;AAAA,gBACA,UAAU;AAAA,kBACN,OAAO,IAAI,OAAO,IAAI,qCAAqC;AAAA,gBAC/D;AAAA,cACJ;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,cACL,SAAS;AAAA,gBACL,SAAS;AAAA,kBACL,OAAO,IAAI,OAAO,IAAI,6BAA6B;AAAA,gBACvD;AAAA,gBACA,OAAO;AAAA,kBACH,KAAK,KAAK,OAAO,IAAI,yBAAyB;AAAA,kBAC9C,OAAO;AAAA,gBACX;AAAA,gBACA,UAAU;AAAA,kBACN,OAAO,IAAI,OAAO,IAAI,uBAAuB;AAAA,gBACjD;AAAA,cACJ;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,cACL,SAAS;AAAA,gBACL,SAAS;AAAA,kBACL,OAAO,IAAI,OAAO,IAAI,mCAAmC;AAAA,gBAC7D;AAAA,gBACA,OAAO;AAAA,kBACH,KAAK,KAAK,OAAO,IAAI,yBAAyB;AAAA,kBAC9C,OAAO;AAAA,gBACX;AAAA,gBACA,UAAU;AAAA,kBACN,OAAO,IAAI,OAAO,IAAI,6BAA6B;AAAA,gBACvD;AAAA,cACJ;AAAA,YACJ;AAAA,YACA;AAAA,cACI,KAAK;AAAA,cACL,SAAS;AAAA,gBACL,SAAS;AAAA,kBACL,OAAO,IAAI,OAAO,IAAI,cAAc;AAAA,gBACxC;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AAAA,UACA;AAAA,QACJ,GAAG,IAAS,CAAC,CAAC;AAAA,MAAC;AAAA,IAC3B;AACA,IAAAD,mBAAkB,eAAe;AAAA,MAC7B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,aAAa;AAAA,MACb,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,gBAAgB;AAAA,MAChB,YAAY;AAAA,IAChB;AACA,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,CAAC;AAAA,MAClC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,mBAAkB,WAAW,kBAAkB,IAAI;AACtD,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,OAAO,MAAM,CAAC;AAAA,MAC/C,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,mBAAkB,WAAW,mBAAmB,IAAI;AACvD,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,OAAO,MAAM,CAAC;AAAA,MAC/C,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,mBAAkB,WAAW,qBAAqB,IAAI;AACzD,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,CAAC;AAAA,MAClC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,mBAAkB,WAAW,qBAAqB,IAAI;AACzD,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,mBAAkB,WAAW,gBAAgB,IAAI;AACpD,eAAW;AAAA,MACP,cAAc;AAAA,MACd,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,CAAC;AAAA,MAClC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,mBAAkB,WAAW,UAAU,IAAI;AAC9C,WAAOA;AAAA,EACX,EAAE,aAAAC,QAAM,SAAS;AAAA;AACjB,IAAI;AAAA;AAAA,EAA2C,SAAU,QAAQ;AAC7D,cAAUM,4BAA2B,MAAM;AAC3C,aAASA,6BAA4B;AACjC,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,6BAA4B,WAAW;AAAA,MACnC,eAAe;AAAA,QACX,MAAM;AAAA,QACN,aAAa;AAAA,QACb,MAAM;AAAA,MACV,CAAC;AAAA,IACL,GAAGA,0BAAyB;AAC5B,WAAOA;AAAA,EACX,EAAE,iBAAiB;AAAA;", "names": ["inRange", "CheckboxesControl", "React", "debounce", "item", "option", "index", "inRange", "CheckboxesControlRenderer"]}