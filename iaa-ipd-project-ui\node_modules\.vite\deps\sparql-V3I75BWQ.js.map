{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/sparql/sparql.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    comments: {\n        lineComment: '#'\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: \"'\", close: \"'\", notIn: ['string'] },\n        { open: '\"', close: '\"', notIn: ['string'] },\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' }\n    ]\n};\nexport var language = {\n    defaultToken: '',\n    tokenPostfix: '.rq',\n    brackets: [\n        { token: 'delimiter.curly', open: '{', close: '}' },\n        { token: 'delimiter.parenthesis', open: '(', close: ')' },\n        { token: 'delimiter.square', open: '[', close: ']' },\n        { token: 'delimiter.angle', open: '<', close: '>' }\n    ],\n    keywords: [\n        'add',\n        'as',\n        'asc',\n        'ask',\n        'base',\n        'by',\n        'clear',\n        'construct',\n        'copy',\n        'create',\n        'data',\n        'delete',\n        'desc',\n        'describe',\n        'distinct',\n        'drop',\n        'false',\n        'filter',\n        'from',\n        'graph',\n        'group',\n        'having',\n        'in',\n        'insert',\n        'limit',\n        'load',\n        'minus',\n        'move',\n        'named',\n        'not',\n        'offset',\n        'optional',\n        'order',\n        'prefix',\n        'reduced',\n        'select',\n        'service',\n        'silent',\n        'to',\n        'true',\n        'undef',\n        'union',\n        'using',\n        'values',\n        'where',\n        'with'\n    ],\n    builtinFunctions: [\n        'a',\n        'abs',\n        'avg',\n        'bind',\n        'bnode',\n        'bound',\n        'ceil',\n        'coalesce',\n        'concat',\n        'contains',\n        'count',\n        'datatype',\n        'day',\n        'encode_for_uri',\n        'exists',\n        'floor',\n        'group_concat',\n        'hours',\n        'if',\n        'iri',\n        'isblank',\n        'isiri',\n        'isliteral',\n        'isnumeric',\n        'isuri',\n        'lang',\n        'langmatches',\n        'lcase',\n        'max',\n        'md5',\n        'min',\n        'minutes',\n        'month',\n        'now',\n        'rand',\n        'regex',\n        'replace',\n        'round',\n        'sameterm',\n        'sample',\n        'seconds',\n        'sha1',\n        'sha256',\n        'sha384',\n        'sha512',\n        'str',\n        'strafter',\n        'strbefore',\n        'strdt',\n        'strends',\n        'strlang',\n        'strlen',\n        'strstarts',\n        'struuid',\n        'substr',\n        'sum',\n        'timezone',\n        'tz',\n        'ucase',\n        'uri',\n        'uuid',\n        'year'\n    ],\n    // describe tokens\n    ignoreCase: true,\n    tokenizer: {\n        root: [\n            // resource indicators\n            [/<[^\\s\\u00a0>]*>?/, 'tag'],\n            // strings\n            { include: '@strings' },\n            // line comment\n            [/#.*/, 'comment'],\n            // special chars with special meaning\n            [/[{}()\\[\\]]/, '@brackets'],\n            [/[;,.]/, 'delimiter'],\n            // (prefixed) name\n            [/[_\\w\\d]+:(\\.(?=[\\w_\\-\\\\%])|[:\\w_-]|\\\\[-\\\\_~.!$&'()*+,;=/?#@%]|%[a-f\\d][a-f\\d])*/, 'tag'],\n            [/:(\\.(?=[\\w_\\-\\\\%])|[:\\w_-]|\\\\[-\\\\_~.!$&'()*+,;=/?#@%]|%[a-f\\d][a-f\\d])+/, 'tag'],\n            // identifiers, builtinFunctions and keywords\n            [\n                /[$?]?[_\\w\\d]+/,\n                {\n                    cases: {\n                        '@keywords': { token: 'keyword' },\n                        '@builtinFunctions': { token: 'predefined.sql' },\n                        '@default': 'identifier'\n                    }\n                }\n            ],\n            // operators\n            [/\\^\\^/, 'operator.sql'],\n            [/\\^[*+\\-<>=&|^\\/!?]*/, 'operator.sql'],\n            [/[*+\\-<>=&|\\/!?]/, 'operator.sql'],\n            // symbol\n            [/@[a-z\\d\\-]*/, 'metatag.html'],\n            // whitespaces\n            [/\\s+/, 'white']\n        ],\n        strings: [\n            [/'([^'\\\\]|\\\\.)*$/, 'string.invalid'],\n            [/'$/, 'string.sql', '@pop'],\n            [/'/, 'string.sql', '@stringBody'],\n            [/\"([^\"\\\\]|\\\\.)*$/, 'string.invalid'],\n            [/\"$/, 'string.sql', '@pop'],\n            [/\"/, 'string.sql', '@dblStringBody']\n        ],\n        // single-quoted strings\n        stringBody: [\n            [/[^\\\\']+/, 'string.sql'],\n            [/\\\\./, 'string.escape'],\n            [/'/, 'string.sql', '@pop']\n        ],\n        // double-quoted strings\n        dblStringBody: [\n            [/[^\\\\\"]+/, 'string.sql'],\n            [/\\\\./, 'string.escape'],\n            [/\"/, 'string.sql', '@pop']\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,aAAa;AAAA,EACjB;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACN,EAAE,OAAO,mBAAmB,MAAM,KAAK,OAAO,IAAI;AAAA,IAClD,EAAE,OAAO,yBAAyB,MAAM,KAAK,OAAO,IAAI;AAAA,IACxD,EAAE,OAAO,oBAAoB,MAAM,KAAK,OAAO,IAAI;AAAA,IACnD,EAAE,OAAO,mBAAmB,MAAM,KAAK,OAAO,IAAI;AAAA,EACtD;AAAA,EACA,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,kBAAkB;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA;AAAA,EAEA,YAAY;AAAA,EACZ,WAAW;AAAA,IACP,MAAM;AAAA;AAAA,MAEF,CAAC,oBAAoB,KAAK;AAAA;AAAA,MAE1B,EAAE,SAAS,WAAW;AAAA;AAAA,MAEtB,CAAC,OAAO,SAAS;AAAA;AAAA,MAEjB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,SAAS,WAAW;AAAA;AAAA,MAErB,CAAC,mFAAmF,KAAK;AAAA,MACzF,CAAC,2EAA2E,KAAK;AAAA;AAAA,MAEjF;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,aAAa,EAAE,OAAO,UAAU;AAAA,YAChC,qBAAqB,EAAE,OAAO,iBAAiB;AAAA,YAC/C,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,CAAC,QAAQ,cAAc;AAAA,MACvB,CAAC,uBAAuB,cAAc;AAAA,MACtC,CAAC,mBAAmB,cAAc;AAAA;AAAA,MAElC,CAAC,eAAe,cAAc;AAAA;AAAA,MAE9B,CAAC,OAAO,OAAO;AAAA,IACnB;AAAA,IACA,SAAS;AAAA,MACL,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,MAAM,cAAc,MAAM;AAAA,MAC3B,CAAC,KAAK,cAAc,aAAa;AAAA,MACjC,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,MAAM,cAAc,MAAM;AAAA,MAC3B,CAAC,KAAK,cAAc,gBAAgB;AAAA,IACxC;AAAA;AAAA,IAEA,YAAY;AAAA,MACR,CAAC,WAAW,YAAY;AAAA,MACxB,CAAC,OAAO,eAAe;AAAA,MACvB,CAAC,KAAK,cAAc,MAAM;AAAA,IAC9B;AAAA;AAAA,IAEA,eAAe;AAAA,MACX,CAAC,WAAW,YAAY;AAAA,MACxB,CAAC,OAAO,eAAe;AAAA,MACvB,CAAC,KAAK,cAAc,MAAM;AAAA,IAC9B;AAAA,EACJ;AACJ;", "names": []}