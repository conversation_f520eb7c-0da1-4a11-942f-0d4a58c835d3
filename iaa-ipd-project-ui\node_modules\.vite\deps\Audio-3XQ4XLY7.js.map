{"version": 3, "sources": ["../../amis/esm/renderers/Audio.js"], "sourcesContent": ["/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __decorate, __metadata } from 'tslib';\nimport React from 'react';\nimport upperFirst from 'lodash/upperFirst';\nimport { getPropValue, filter, detectPropValueChanged, autobind, Renderer } from 'amis-core';\nimport { Icon } from 'amis-ui';\n\nvar Audio = /** @class */ (function (_super) {\n    __extends(Audio, _super);\n    function Audio() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.state = {\n            src: getPropValue(_this.props, function (props) {\n                return props.src ? filter(props.src, props.data, '| raw') : undefined;\n            }) || '',\n            isReady: false,\n            muted: false,\n            playing: false,\n            played: 0,\n            seeking: false,\n            volume: 0.8,\n            prevVolume: 0.8,\n            loaded: 0,\n            playbackRate: 1.0,\n            showHandlePlaybackRate: false,\n            showHandleVolume: false\n        };\n        return _this;\n    }\n    Audio.prototype.componentWillUnmount = function () {\n        clearTimeout(this.progressTimeout);\n        clearTimeout(this.durationTimeout);\n    };\n    Audio.prototype.componentDidMount = function () {\n        var autoPlay = this.props.autoPlay;\n        var playing = autoPlay ? true : false;\n        this.setState({\n            playing: playing\n        }, this.progress);\n    };\n    Audio.prototype.componentDidUpdate = function (prevProps) {\n        var _this = this;\n        var props = this.props;\n        detectPropValueChanged(props, prevProps, function (value) {\n            return _this.setState({\n                src: value,\n                playing: false\n            }, function () {\n                _this.audio.load();\n                _this.progress();\n            });\n        }, function (props) { return (props.src ? filter(props.src, props.data, '| raw') : undefined); });\n    };\n    Audio.prototype.progress = function () {\n        clearTimeout(this.progressTimeout);\n        if (this.state.src && this.audio) {\n            var currentTime = this.audio.currentTime || 0;\n            var duration = this.audio.duration;\n            var played = currentTime / duration;\n            var playing = this.state.playing;\n            playing = played != 1 && playing ? true : false;\n            this.setState({\n                played: played,\n                playing: playing\n            });\n            this.progressTimeout = setTimeout(this.progress, this.props.progressInterval / this.state.playbackRate);\n        }\n    };\n    Audio.prototype.audioRef = function (audio) {\n        this.audio = audio;\n    };\n    Audio.prototype.load = function () {\n        this.setState({\n            isReady: true\n        });\n    };\n    Audio.prototype.handlePlaybackRate = function (rate) {\n        this.audio.playbackRate = rate;\n        this.setState({\n            playbackRate: rate,\n            showHandlePlaybackRate: false\n        });\n    };\n    Audio.prototype.handleMute = function () {\n        if (!this.state.src) {\n            return;\n        }\n        var _a = this.state, muted = _a.muted, prevVolume = _a.prevVolume;\n        var curVolume = !muted ? 0 : prevVolume;\n        this.audio.muted = !muted;\n        this.setState({\n            muted: !muted,\n            volume: curVolume\n        });\n    };\n    Audio.prototype.handlePlaying = function () {\n        if (!this.state.src) {\n            return;\n        }\n        var playing = this.state.playing;\n        playing ? this.audio.pause() : this.audio.play();\n        this.setState({\n            playing: !playing\n        });\n    };\n    Audio.prototype.getCurrentTime = function () {\n        if (!this.audio || !this.state.src || !this.state.isReady) {\n            return '0:00';\n        }\n        var duration = this.audio.duration;\n        var played = this.state.played;\n        return this.formatTime(duration * (played || 0));\n    };\n    Audio.prototype.getDuration = function () {\n        if (!this.audio || !this.state.src) {\n            return '0:00';\n        }\n        if (!this.state.isReady) {\n            this.onDurationCheck();\n            return '0:00';\n        }\n        var _a = this.audio, duration = _a.duration, seekable = _a.seekable;\n        // on iOS, live streams return Infinity for the duration\n        // so instead we use the end of the seekable timerange\n        if (duration === Infinity && seekable.length > 0) {\n            return seekable.end(seekable.length - 1);\n        }\n        return this.formatTime(duration);\n    };\n    Audio.prototype.onDurationCheck = function () {\n        clearTimeout(this.durationTimeout);\n        var duration = this.audio && this.audio.duration;\n        if (!duration) {\n            this.durationTimeout = setTimeout(this.onDurationCheck, 500);\n        }\n    };\n    Audio.prototype.onSeekChange = function (e) {\n        if (!this.state.src) {\n            return;\n        }\n        var played = e.target.value;\n        this.setState({ played: played });\n    };\n    Audio.prototype.onSeekMouseDown = function () {\n        this.setState({ seeking: true });\n    };\n    Audio.prototype.onSeekMouseUp = function (e) {\n        if (!this.state.src) {\n            return;\n        }\n        if (!this.state.seeking) {\n            return;\n        }\n        var played = e.target.value;\n        var duration = this.audio.duration;\n        this.audio.currentTime = duration * played;\n        var loop = this.props.loop;\n        var playing = this.state.playing;\n        playing = played < 1 || loop ? playing : false;\n        this.setState({\n            playing: playing,\n            seeking: false\n        });\n    };\n    Audio.prototype.setVolume = function (e) {\n        if (!this.state.src) {\n            return;\n        }\n        var volume = e.target.value;\n        this.audio.volume = volume;\n        this.setState({\n            volume: volume,\n            prevVolume: volume\n        });\n    };\n    Audio.prototype.formatTime = function (seconds) {\n        var date = new Date(seconds * 1000);\n        var hh = date.getUTCHours();\n        var mm = isNaN(date.getUTCMinutes()) ? 0 : date.getUTCMinutes();\n        var ss = isNaN(date.getUTCSeconds())\n            ? '00'\n            : this.pad(date.getUTCSeconds());\n        if (hh) {\n            return \"\".concat(hh, \":\").concat(this.pad(mm), \":\").concat(ss);\n        }\n        return \"\".concat(mm, \":\").concat(ss);\n    };\n    Audio.prototype.pad = function (string) {\n        return ('0' + string).slice(-2);\n    };\n    Audio.prototype.toggleHandlePlaybackRate = function () {\n        if (!this.state.src) {\n            return;\n        }\n        this.setState({\n            showHandlePlaybackRate: !this.state.showHandlePlaybackRate\n        });\n    };\n    Audio.prototype.toggleHandleVolume = function (type) {\n        if (!this.state.src) {\n            return;\n        }\n        this.setState({\n            showHandleVolume: type\n        });\n    };\n    Audio.prototype.renderRates = function () {\n        var _this = this;\n        var _a = this.props, rates = _a.rates, cx = _a.classnames;\n        var _b = this.state, showHandlePlaybackRate = _b.showHandlePlaybackRate, playbackRate = _b.playbackRate;\n        return rates && rates.length ? (showHandlePlaybackRate ? (React.createElement(\"div\", { className: cx('Audio-rateControl') }, rates.map(function (rate, index) { return (React.createElement(\"div\", { key: index, className: cx('Audio-rateControlItem'), onClick: function () { return _this.handlePlaybackRate(rate); } },\n            \"x\",\n            rate.toFixed(1))); }))) : (React.createElement(\"div\", { className: cx('Audio-rates'), onClick: this.toggleHandlePlaybackRate },\n            \"x\",\n            playbackRate.toFixed(1)))) : null;\n    };\n    Audio.prototype.renderPlay = function () {\n        var cx = this.props.classnames;\n        var playing = this.state.playing;\n        return (React.createElement(\"div\", { className: cx('Audio-play'), onClick: this.handlePlaying }, playing ? (React.createElement(Icon, { icon: \"pause\", className: \"icon\" })) : (React.createElement(Icon, { icon: \"play\", className: \"icon\" }))));\n    };\n    Audio.prototype.renderTime = function () {\n        var cx = this.props.classnames;\n        return (React.createElement(\"div\", { className: cx('Audio-times') },\n            this.getCurrentTime(),\n            \" / \",\n            this.getDuration()));\n    };\n    Audio.prototype.renderProcess = function () {\n        var cx = this.props.classnames;\n        var played = this.state.played;\n        return (React.createElement(\"div\", { className: cx('Audio-process') },\n            React.createElement(\"input\", { type: \"range\", min: 0, max: 1, step: \"any\", value: played || 0, onMouseDown: this.onSeekMouseDown, onChange: this.onSeekChange, onMouseUp: this.onSeekMouseUp })));\n    };\n    Audio.prototype.renderVolume = function () {\n        var _this = this;\n        var cx = this.props.classnames;\n        var _a = this.state, volume = _a.volume, showHandleVolume = _a.showHandleVolume;\n        return showHandleVolume ? (React.createElement(\"div\", { className: cx('Audio-volumeControl'), onMouseLeave: function () { return _this.toggleHandleVolume(false); } },\n            React.createElement(\"div\", { className: cx('Audio-volumeControlIcon'), onClick: this.handleMute }, volume > 0 ? (React.createElement(Icon, { icon: \"volume\", className: \"icon\" })) : (React.createElement(Icon, { icon: \"mute\", className: \"icon\" }))),\n            React.createElement(\"input\", { type: \"range\", min: 0, max: 1, step: \"any\", value: volume, onChange: this.setVolume }))) : (React.createElement(\"div\", { className: cx('Audio-volume'), onMouseEnter: function () { return _this.toggleHandleVolume(true); } }, volume > 0 ? (React.createElement(Icon, { icon: \"volume\", className: \"icon\" })) : (React.createElement(Icon, { icon: \"mute\", className: \"icon\" }))));\n    };\n    Audio.prototype.render = function () {\n        var _this = this;\n        var _a = this.props, className = _a.className, style = _a.style, inline = _a.inline, autoPlay = _a.autoPlay, loop = _a.loop, controls = _a.controls, cx = _a.classnames;\n        var _b = this.state, muted = _b.muted, src = _b.src;\n        return (React.createElement(\"div\", { className: cx('Audio', className, inline ? 'Audio--inline' : ''), style: style },\n            React.createElement(\"audio\", { className: cx('Audio-original'), ref: this.audioRef, onCanPlay: this.load, autoPlay: autoPlay, controls: true, muted: muted, loop: loop },\n                React.createElement(\"source\", { src: src })),\n            React.createElement(\"div\", { className: cx('Audio-controls') }, controls &&\n                controls.map(function (control, index) {\n                    control = 'render' + upperFirst(control);\n                    var method = control;\n                    return (React.createElement(React.Fragment, { key: index }, _this[method]()));\n                }))));\n    };\n    Audio.defaultProps = {\n        inline: true,\n        autoPlay: false,\n        playbackRate: 1,\n        loop: false,\n        rates: [],\n        progressInterval: 1000,\n        controls: ['rates', 'play', 'time', 'process', 'volume']\n    };\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", []),\n        __metadata(\"design:returntype\", void 0)\n    ], Audio.prototype, \"progress\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [HTMLMediaElement]),\n        __metadata(\"design:returntype\", void 0)\n    ], Audio.prototype, \"audioRef\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", []),\n        __metadata(\"design:returntype\", void 0)\n    ], Audio.prototype, \"load\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Number]),\n        __metadata(\"design:returntype\", void 0)\n    ], Audio.prototype, \"handlePlaybackRate\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", []),\n        __metadata(\"design:returntype\", void 0)\n    ], Audio.prototype, \"handleMute\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", []),\n        __metadata(\"design:returntype\", void 0)\n    ], Audio.prototype, \"handlePlaying\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", []),\n        __metadata(\"design:returntype\", void 0)\n    ], Audio.prototype, \"getCurrentTime\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", []),\n        __metadata(\"design:returntype\", void 0)\n    ], Audio.prototype, \"getDuration\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", []),\n        __metadata(\"design:returntype\", void 0)\n    ], Audio.prototype, \"onDurationCheck\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], Audio.prototype, \"onSeekChange\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", []),\n        __metadata(\"design:returntype\", void 0)\n    ], Audio.prototype, \"onSeekMouseDown\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], Audio.prototype, \"onSeekMouseUp\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], Audio.prototype, \"setVolume\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Number]),\n        __metadata(\"design:returntype\", void 0)\n    ], Audio.prototype, \"formatTime\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Number]),\n        __metadata(\"design:returntype\", void 0)\n    ], Audio.prototype, \"pad\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", []),\n        __metadata(\"design:returntype\", void 0)\n    ], Audio.prototype, \"toggleHandlePlaybackRate\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Boolean]),\n        __metadata(\"design:returntype\", void 0)\n    ], Audio.prototype, \"toggleHandleVolume\", null);\n    return Audio;\n}(React.Component));\nvar AudioRenderer = /** @class */ (function (_super) {\n    __extends(AudioRenderer, _super);\n    function AudioRenderer() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    AudioRenderer = __decorate([\n        Renderer({\n            type: 'audio'\n        })\n    ], AudioRenderer);\n    return AudioRenderer;\n}(Audio));\n\nexport { Audio, AudioRenderer };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,mBAAkB;AAClB,wBAAuB;AAIvB,IAAI;AAAA;AAAA,EAAuB,SAAU,QAAQ;AACzC,cAAUA,QAAO,MAAM;AACvB,aAASA,SAAQ;AACb,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,QAAQ;AAAA,QACV,KAAK,aAAa,MAAM,OAAO,SAAU,OAAO;AAC5C,iBAAO,MAAM,MAAM,OAAO,MAAM,KAAK,MAAM,MAAM,OAAO,IAAI;AAAA,QAChE,CAAC,KAAK;AAAA,QACN,SAAS;AAAA,QACT,OAAO;AAAA,QACP,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,kBAAkB;AAAA,MACtB;AACA,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,uBAAuB,WAAY;AAC/C,mBAAa,KAAK,eAAe;AACjC,mBAAa,KAAK,eAAe;AAAA,IACrC;AACA,IAAAA,OAAM,UAAU,oBAAoB,WAAY;AAC5C,UAAI,WAAW,KAAK,MAAM;AAC1B,UAAI,UAAU,WAAW,OAAO;AAChC,WAAK,SAAS;AAAA,QACV;AAAA,MACJ,GAAG,KAAK,QAAQ;AAAA,IACpB;AACA,IAAAA,OAAM,UAAU,qBAAqB,SAAU,WAAW;AACtD,UAAI,QAAQ;AACZ,UAAI,QAAQ,KAAK;AACjB,6BAAuB,OAAO,WAAW,SAAU,OAAO;AACtD,eAAO,MAAM,SAAS;AAAA,UAClB,KAAK;AAAA,UACL,SAAS;AAAA,QACb,GAAG,WAAY;AACX,gBAAM,MAAM,KAAK;AACjB,gBAAM,SAAS;AAAA,QACnB,CAAC;AAAA,MACL,GAAG,SAAUC,QAAO;AAAE,eAAQA,OAAM,MAAM,OAAOA,OAAM,KAAKA,OAAM,MAAM,OAAO,IAAI;AAAA,MAAY,CAAC;AAAA,IACpG;AACA,IAAAD,OAAM,UAAU,WAAW,WAAY;AACnC,mBAAa,KAAK,eAAe;AACjC,UAAI,KAAK,MAAM,OAAO,KAAK,OAAO;AAC9B,YAAI,cAAc,KAAK,MAAM,eAAe;AAC5C,YAAI,WAAW,KAAK,MAAM;AAC1B,YAAI,SAAS,cAAc;AAC3B,YAAI,UAAU,KAAK,MAAM;AACzB,kBAAU,UAAU,KAAK,UAAU,OAAO;AAC1C,aAAK,SAAS;AAAA,UACV;AAAA,UACA;AAAA,QACJ,CAAC;AACD,aAAK,kBAAkB,WAAW,KAAK,UAAU,KAAK,MAAM,mBAAmB,KAAK,MAAM,YAAY;AAAA,MAC1G;AAAA,IACJ;AACA,IAAAA,OAAM,UAAU,WAAW,SAAU,OAAO;AACxC,WAAK,QAAQ;AAAA,IACjB;AACA,IAAAA,OAAM,UAAU,OAAO,WAAY;AAC/B,WAAK,SAAS;AAAA,QACV,SAAS;AAAA,MACb,CAAC;AAAA,IACL;AACA,IAAAA,OAAM,UAAU,qBAAqB,SAAU,MAAM;AACjD,WAAK,MAAM,eAAe;AAC1B,WAAK,SAAS;AAAA,QACV,cAAc;AAAA,QACd,wBAAwB;AAAA,MAC5B,CAAC;AAAA,IACL;AACA,IAAAA,OAAM,UAAU,aAAa,WAAY;AACrC,UAAI,CAAC,KAAK,MAAM,KAAK;AACjB;AAAA,MACJ;AACA,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,aAAa,GAAG;AACvD,UAAI,YAAY,CAAC,QAAQ,IAAI;AAC7B,WAAK,MAAM,QAAQ,CAAC;AACpB,WAAK,SAAS;AAAA,QACV,OAAO,CAAC;AAAA,QACR,QAAQ;AAAA,MACZ,CAAC;AAAA,IACL;AACA,IAAAA,OAAM,UAAU,gBAAgB,WAAY;AACxC,UAAI,CAAC,KAAK,MAAM,KAAK;AACjB;AAAA,MACJ;AACA,UAAI,UAAU,KAAK,MAAM;AACzB,gBAAU,KAAK,MAAM,MAAM,IAAI,KAAK,MAAM,KAAK;AAC/C,WAAK,SAAS;AAAA,QACV,SAAS,CAAC;AAAA,MACd,CAAC;AAAA,IACL;AACA,IAAAA,OAAM,UAAU,iBAAiB,WAAY;AACzC,UAAI,CAAC,KAAK,SAAS,CAAC,KAAK,MAAM,OAAO,CAAC,KAAK,MAAM,SAAS;AACvD,eAAO;AAAA,MACX;AACA,UAAI,WAAW,KAAK,MAAM;AAC1B,UAAI,SAAS,KAAK,MAAM;AACxB,aAAO,KAAK,WAAW,YAAY,UAAU,EAAE;AAAA,IACnD;AACA,IAAAA,OAAM,UAAU,cAAc,WAAY;AACtC,UAAI,CAAC,KAAK,SAAS,CAAC,KAAK,MAAM,KAAK;AAChC,eAAO;AAAA,MACX;AACA,UAAI,CAAC,KAAK,MAAM,SAAS;AACrB,aAAK,gBAAgB;AACrB,eAAO;AAAA,MACX;AACA,UAAI,KAAK,KAAK,OAAO,WAAW,GAAG,UAAU,WAAW,GAAG;AAG3D,UAAI,aAAa,YAAY,SAAS,SAAS,GAAG;AAC9C,eAAO,SAAS,IAAI,SAAS,SAAS,CAAC;AAAA,MAC3C;AACA,aAAO,KAAK,WAAW,QAAQ;AAAA,IACnC;AACA,IAAAA,OAAM,UAAU,kBAAkB,WAAY;AAC1C,mBAAa,KAAK,eAAe;AACjC,UAAI,WAAW,KAAK,SAAS,KAAK,MAAM;AACxC,UAAI,CAAC,UAAU;AACX,aAAK,kBAAkB,WAAW,KAAK,iBAAiB,GAAG;AAAA,MAC/D;AAAA,IACJ;AACA,IAAAA,OAAM,UAAU,eAAe,SAAU,GAAG;AACxC,UAAI,CAAC,KAAK,MAAM,KAAK;AACjB;AAAA,MACJ;AACA,UAAI,SAAS,EAAE,OAAO;AACtB,WAAK,SAAS,EAAE,OAAe,CAAC;AAAA,IACpC;AACA,IAAAA,OAAM,UAAU,kBAAkB,WAAY;AAC1C,WAAK,SAAS,EAAE,SAAS,KAAK,CAAC;AAAA,IACnC;AACA,IAAAA,OAAM,UAAU,gBAAgB,SAAU,GAAG;AACzC,UAAI,CAAC,KAAK,MAAM,KAAK;AACjB;AAAA,MACJ;AACA,UAAI,CAAC,KAAK,MAAM,SAAS;AACrB;AAAA,MACJ;AACA,UAAI,SAAS,EAAE,OAAO;AACtB,UAAI,WAAW,KAAK,MAAM;AAC1B,WAAK,MAAM,cAAc,WAAW;AACpC,UAAI,OAAO,KAAK,MAAM;AACtB,UAAI,UAAU,KAAK,MAAM;AACzB,gBAAU,SAAS,KAAK,OAAO,UAAU;AACzC,WAAK,SAAS;AAAA,QACV;AAAA,QACA,SAAS;AAAA,MACb,CAAC;AAAA,IACL;AACA,IAAAA,OAAM,UAAU,YAAY,SAAU,GAAG;AACrC,UAAI,CAAC,KAAK,MAAM,KAAK;AACjB;AAAA,MACJ;AACA,UAAI,SAAS,EAAE,OAAO;AACtB,WAAK,MAAM,SAAS;AACpB,WAAK,SAAS;AAAA,QACV;AAAA,QACA,YAAY;AAAA,MAChB,CAAC;AAAA,IACL;AACA,IAAAA,OAAM,UAAU,aAAa,SAAU,SAAS;AAC5C,UAAI,OAAO,IAAI,KAAK,UAAU,GAAI;AAClC,UAAI,KAAK,KAAK,YAAY;AAC1B,UAAI,KAAK,MAAM,KAAK,cAAc,CAAC,IAAI,IAAI,KAAK,cAAc;AAC9D,UAAI,KAAK,MAAM,KAAK,cAAc,CAAC,IAC7B,OACA,KAAK,IAAI,KAAK,cAAc,CAAC;AACnC,UAAI,IAAI;AACJ,eAAO,GAAG,OAAO,IAAI,GAAG,EAAE,OAAO,KAAK,IAAI,EAAE,GAAG,GAAG,EAAE,OAAO,EAAE;AAAA,MACjE;AACA,aAAO,GAAG,OAAO,IAAI,GAAG,EAAE,OAAO,EAAE;AAAA,IACvC;AACA,IAAAA,OAAM,UAAU,MAAM,SAAU,QAAQ;AACpC,cAAQ,MAAM,QAAQ,MAAM,EAAE;AAAA,IAClC;AACA,IAAAA,OAAM,UAAU,2BAA2B,WAAY;AACnD,UAAI,CAAC,KAAK,MAAM,KAAK;AACjB;AAAA,MACJ;AACA,WAAK,SAAS;AAAA,QACV,wBAAwB,CAAC,KAAK,MAAM;AAAA,MACxC,CAAC;AAAA,IACL;AACA,IAAAA,OAAM,UAAU,qBAAqB,SAAU,MAAM;AACjD,UAAI,CAAC,KAAK,MAAM,KAAK;AACjB;AAAA,MACJ;AACA,WAAK,SAAS;AAAA,QACV,kBAAkB;AAAA,MACtB,CAAC;AAAA,IACL;AACA,IAAAA,OAAM,UAAU,cAAc,WAAY;AACtC,UAAI,QAAQ;AACZ,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,KAAK,GAAG;AAC/C,UAAI,KAAK,KAAK,OAAO,yBAAyB,GAAG,wBAAwB,eAAe,GAAG;AAC3F,aAAO,SAAS,MAAM,SAAU,yBAA0B,aAAAE,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,mBAAmB,EAAE,GAAG,MAAM,IAAI,SAAU,MAAM,OAAO;AAAE,eAAQ,aAAAA,QAAM;AAAA,UAAc;AAAA,UAAO,EAAE,KAAK,OAAO,WAAW,GAAG,uBAAuB,GAAG,SAAS,WAAY;AAAE,mBAAO,MAAM,mBAAmB,IAAI;AAAA,UAAG,EAAE;AAAA,UACrT;AAAA,UACA,KAAK,QAAQ,CAAC;AAAA,QAAC;AAAA,MAAI,CAAC,CAAC,IAAM,aAAAA,QAAM;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,GAAG,aAAa,GAAG,SAAS,KAAK,yBAAyB;AAAA,QAC7H;AAAA,QACA,aAAa,QAAQ,CAAC;AAAA,MAAC,IAAM;AAAA,IACrC;AACA,IAAAF,OAAM,UAAU,aAAa,WAAY;AACrC,UAAI,KAAK,KAAK,MAAM;AACpB,UAAI,UAAU,KAAK,MAAM;AACzB,aAAQ,aAAAE,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,YAAY,GAAG,SAAS,KAAK,cAAc,GAAG,UAAW,aAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,SAAS,WAAW,OAAO,CAAC,IAAM,aAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,QAAQ,WAAW,OAAO,CAAC,CAAE;AAAA,IACnP;AACA,IAAAF,OAAM,UAAU,aAAa,WAAY;AACrC,UAAI,KAAK,KAAK,MAAM;AACpB,aAAQ,aAAAE,QAAM;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,GAAG,aAAa,EAAE;AAAA,QAC9D,KAAK,eAAe;AAAA,QACpB;AAAA,QACA,KAAK,YAAY;AAAA,MAAC;AAAA,IAC1B;AACA,IAAAF,OAAM,UAAU,gBAAgB,WAAY;AACxC,UAAI,KAAK,KAAK,MAAM;AACpB,UAAI,SAAS,KAAK,MAAM;AACxB,aAAQ,aAAAE,QAAM;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,GAAG,eAAe,EAAE;AAAA,QAChE,aAAAA,QAAM,cAAc,SAAS,EAAE,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,MAAM,OAAO,OAAO,UAAU,GAAG,aAAa,KAAK,iBAAiB,UAAU,KAAK,cAAc,WAAW,KAAK,cAAc,CAAC;AAAA,MAAC;AAAA,IACvM;AACA,IAAAF,OAAM,UAAU,eAAe,WAAY;AACvC,UAAI,QAAQ;AACZ,UAAI,KAAK,KAAK,MAAM;AACpB,UAAI,KAAK,KAAK,OAAO,SAAS,GAAG,QAAQ,mBAAmB,GAAG;AAC/D,aAAO,mBAAoB,aAAAE,QAAM;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,GAAG,qBAAqB,GAAG,cAAc,WAAY;AAAE,iBAAO,MAAM,mBAAmB,KAAK;AAAA,QAAG,EAAE;AAAA,QAChK,aAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,yBAAyB,GAAG,SAAS,KAAK,WAAW,GAAG,SAAS,IAAK,aAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,UAAU,WAAW,OAAO,CAAC,IAAM,aAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,QAAQ,WAAW,OAAO,CAAC,CAAE;AAAA,QACrP,aAAAA,QAAM,cAAc,SAAS,EAAE,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,MAAM,OAAO,OAAO,QAAQ,UAAU,KAAK,UAAU,CAAC;AAAA,MAAC,IAAM,aAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,cAAc,GAAG,cAAc,WAAY;AAAE,eAAO,MAAM,mBAAmB,IAAI;AAAA,MAAG,EAAE,GAAG,SAAS,IAAK,aAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,UAAU,WAAW,OAAO,CAAC,IAAM,aAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,QAAQ,WAAW,OAAO,CAAC,CAAE;AAAA,IACzZ;AACA,IAAAF,OAAM,UAAU,SAAS,WAAY;AACjC,UAAI,QAAQ;AACZ,UAAI,KAAK,KAAK,OAAO,YAAY,GAAG,WAAW,QAAQ,GAAG,OAAO,SAAS,GAAG,QAAQ,WAAW,GAAG,UAAU,OAAO,GAAG,MAAM,WAAW,GAAG,UAAU,KAAK,GAAG;AAC7J,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,MAAM,GAAG;AAChD,aAAQ,aAAAE,QAAM;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,GAAG,SAAS,WAAW,SAAS,kBAAkB,EAAE,GAAG,MAAa;AAAA,QAChH,aAAAA,QAAM;AAAA,UAAc;AAAA,UAAS,EAAE,WAAW,GAAG,gBAAgB,GAAG,KAAK,KAAK,UAAU,WAAW,KAAK,MAAM,UAAoB,UAAU,MAAM,OAAc,KAAW;AAAA,UACnK,aAAAA,QAAM,cAAc,UAAU,EAAE,IAAS,CAAC;AAAA,QAAC;AAAA,QAC/C,aAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,gBAAgB,EAAE,GAAG,YAC5D,SAAS,IAAI,SAAU,SAAS,OAAO;AACnC,oBAAU,eAAW,kBAAAC,SAAW,OAAO;AACvC,cAAI,SAAS;AACb,iBAAQ,aAAAD,QAAM,cAAc,aAAAA,QAAM,UAAU,EAAE,KAAK,MAAM,GAAG,MAAM,MAAM,EAAE,CAAC;AAAA,QAC/E,CAAC,CAAC;AAAA,MAAC;AAAA,IACf;AACA,IAAAF,OAAM,eAAe;AAAA,MACjB,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,cAAc;AAAA,MACd,MAAM;AAAA,MACN,OAAO,CAAC;AAAA,MACR,kBAAkB;AAAA,MAClB,UAAU,CAAC,SAAS,QAAQ,QAAQ,WAAW,QAAQ;AAAA,IAC3D;AACA,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,CAAC;AAAA,MAClC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,YAAY,IAAI;AACpC,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,gBAAgB,CAAC;AAAA,MAClD,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,YAAY,IAAI;AACpC,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,CAAC;AAAA,MAClC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,QAAQ,IAAI;AAChC,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,sBAAsB,IAAI;AAC9C,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,CAAC;AAAA,MAClC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,cAAc,IAAI;AACtC,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,CAAC;AAAA,MAClC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,iBAAiB,IAAI;AACzC,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,CAAC;AAAA,MAClC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,kBAAkB,IAAI;AAC1C,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,CAAC;AAAA,MAClC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,eAAe,IAAI;AACvC,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,CAAC;AAAA,MAClC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,mBAAmB,IAAI;AAC3C,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,gBAAgB,IAAI;AACxC,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,CAAC;AAAA,MAClC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,mBAAmB,IAAI;AAC3C,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,iBAAiB,IAAI;AACzC,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,aAAa,IAAI;AACrC,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,cAAc,IAAI;AACtC,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,OAAO,IAAI;AAC/B,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,CAAC;AAAA,MAClC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,4BAA4B,IAAI;AACpD,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,OAAO,CAAC;AAAA,MACzC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,sBAAsB,IAAI;AAC9C,WAAOA;AAAA,EACX,EAAE,aAAAE,QAAM,SAAS;AAAA;AACjB,IAAI;AAAA;AAAA,EAA+B,SAAU,QAAQ;AACjD,cAAUE,gBAAe,MAAM;AAC/B,aAASA,iBAAgB;AACrB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,iBAAgB,WAAW;AAAA,MACvB,SAAS;AAAA,QACL,MAAM;AAAA,MACV,CAAC;AAAA,IACL,GAAGA,cAAa;AAChB,WAAOA;AAAA,EACX,EAAE,KAAK;AAAA;", "names": ["Audio", "props", "React", "upperFirst", "AudioR<PERSON><PERSON>"]}