var _xeUtils=_interopRequireDefault(require("xe-utils")),_ui=require("../../../ui"),_util=require("../../src/util"),_dom=require("../../../ui/src/dom");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let hooks=_ui.VxeUI.hooks,browseObj=_xeUtils.default.browse();function getTargetOffset(e,t){let l=0,o=0;var r,n,u=!browseObj.firefox&&(0,_dom.hasClass)(e,"vxe-checkbox--label");for(u&&(r=getComputedStyle(e),l-=_xeUtils.default.toNumber(r.paddingTop),o-=_xeUtils.default.toNumber(r.paddingLeft));e&&e!==t;)l+=e.offsetTop,o+=e.offsetLeft,e=e.offsetParent,u&&(n=getComputedStyle(e),l-=_xeUtils.default.toNumber(n.paddingTop),o-=_xeUtils.default.toNumber(n.paddingLeft));return{offsetTop:l,offsetLeft:o}}hooks.add("tableKeyboardModule",{setupTable(k){let{props:M,reactData:y,internalData:O}=k,D=k.getRefMaps().refElem,{computeEditOpts:v,computeCheckboxOpts:a,computeMouseOpts:s,computeTreeOpts:c,computeRowOpts:F,computeColumnOpts:C,computeCellOpts:H,computeDefaultRowHeight:V,computeCurrentRowOpts:w,computeCurrentColumnOpts:p}=k.getComputeMaps();let d=(e,S)=>{var t=O.elemStore,l=(0,_util.getRefElem)(t["main-body-scroll"]),o=(0,_util.getRefElem)(t["left-body-scroll"]),t=(0,_util.getRefElem)(t["right-body-scroll"]),{column:r,cell:n}=S;if("checkbox"===r.type){let _=l;if(o&&"left"===r.fixed?_=o:t&&"right"===r.fixed&&(_=t),_){let t=D.value,i=e.clientX,a=e.clientY,s=_.querySelector(".vxe-table--checkbox-range"),c=n.parentElement,d=k.getCheckboxRecords(),g=[],h=1;l=getTargetOffset(e.target,_);let m=l.offsetTop+e.offsetY,f=l.offsetLeft+e.offsetX,v=_.scrollTop,u=c.offsetHeight,C=c.getBoundingClientRect(),w=a-C.y,p=null,x=!1,b=1,R=(e,t)=>{k.dispatchEvent("checkbox-range-"+e,{records:()=>k.getCheckboxRecords(),reserves:()=>k.getCheckboxReserveRecords()},t)},T=e=>{var{clientX:t,clientY:l}=e,t=t-i,l=l-a+(_.scrollTop-v);let o=Math.abs(l),r=Math.abs(t),n=m,u=f;l<h?(n+=l)<h&&(n=h,o=m):o=Math.min(o,_.scrollHeight-m-h),t<h?(u+=t,r>f&&(u=h,r=f)):r=Math.min(r,_.clientWidth-f-h),s.style.height=o+"px",s.style.width=r+"px",s.style.left=u+"px",s.style.top=n+"px",s.style.display="block";t=((e,t,l,o,r)=>{var n=M.showOverflow,{fullAllDataRowIdData:u,isResizeCellHeight:i}=O,a=F.value,s=H.value,c=V.value,e=e.row;let d=0,g=[],h=0;var m=0<r,f=y.scrollYLoad,v=O.afterFullData;if(h=m?o+r:l.height-o+Math.abs(r),f){l=k.getVTRowIndex(e);if(!(i||s.height||a.height)&&n)g=m?v.slice(l,l+Math.ceil(h/c)):v.slice(l-Math.floor(h/c),l+1);else if(m)for(let e=l;e<v.length;e++){var C=v[e],w=u[k.getRowid(C)]||{};if(d+=w.resizeHeight||s.height||a.height||w.height||c,g.push(C),d>h)return g}else for(let e=l;0<=e;e--){var p=v[e],x=u[k.getRowid(p)]||{};if(d+=x.resizeHeight||s.height||a.height||x.height||c,g.push(p),d>h)return g}}else for(var b=m?"next":"previous";t&&d<h;){var R=k.getRowNode(t);R&&(g.push(R.item),d+=t.offsetHeight,t=t[b+"ElementSibling"])}return g})(S,c,C,w,l<h?-o:o);10<o&&t.length!==g.length&&(l=(0,_dom.hasControlKey)(e),g=t,l?t.forEach(e=>{k.handleBatchSelectRows([e],-1===d.indexOf(e))}):(k.setAllCheckboxRow(!1),k.handleCheckedCheckboxRow(t,!0,!1)),R("change",e))},I=()=>{clearTimeout(p),p=null},E=n=>{I(),p=setTimeout(()=>{var e,t,l,o,r;p&&({scrollLeft:e,scrollTop:t,clientHeight:l,scrollHeight:o}=_,r=Math.ceil(50*b/u),x?t+l<o?(k.scrollTo(e,t+r),E(n),T(n)):I():t?(k.scrollTo(e,t-r),E(n),T(n)):I())},50)};(0,_dom.addClass)(t,"drag--range"),document.onmousemove=e=>{e.preventDefault(),e.stopPropagation();var t=e.clientY,l=(0,_dom.getAbsolutePos)(_).boundingTop;t<l?(x=!1,b=l-t,p||E(e)):t>l+_.clientHeight?(x=!0,b=t-l-_.clientHeight,p||E(e)):p&&I(),T(e)},document.onmouseup=e=>{I(),(0,_dom.removeClass)(t,"drag--range"),s.removeAttribute("style"),document.onmousemove=null,document.onmouseup=null,R("end",e)},R("start",e)}}};let g=(e,t,l,o,r,n)=>{var{afterFullData:u,visibleColumn:i}=O;let a=Object.assign({},t);var t=k.getVTRowIndex(a.row),s=k.getVTColumnIndex(a.column);return e.preventDefault(),o&&0<t?(a.rowIndex=t-1,a.row=u[a.rowIndex]):n&&t<u.length-1?(a.rowIndex=t+1,a.row=u[a.rowIndex]):l&&s?(a.columnIndex=s-1,a.column=i[a.columnIndex]):r&&s<i.length-1&&(a.columnIndex=s+1,a.column=i[a.columnIndex]),k.scrollToRow(a.row,a.column).then(()=>{a.cell=k.getCellElement(a.row,a.column),k.handleSelected(a,e)}),a};return{moveTabSelected(e,t,l){var o=M.editConfig,{afterFullData:r,visibleColumn:n}=O,u=v.value,i=F.value,a=w.value,s=C.value,c=p.value;let d,g,h,m=Object.assign({},e);var e=k.getVTRowIndex(m.row),f=k.getVTColumnIndex(m.column),t=(l.preventDefault(),t?f<=0?0<e&&(g=e-1,d=r[g],h=n.length-1):h=f-1:f>=n.length-1?e<r.length-1&&(g=e+1,d=r[g],h=0):h=f+1,n[h]);t&&(d?(m.rowIndex=g,m.row=d):m.rowIndex=e,m.columnIndex=h,m.column=t,m.cell=k.getCellElement(m.row,m.column),i.isCurrent&&a.isFollowSelected&&k.triggerCurrentRowEvent(l,m),s.isCurrent&&c.isFollowSelected&&k.triggerCurrentColumnEvent(l,m),o?"click"!==u.trigger&&"dblclick"!==u.trigger||("row"===u.mode?k.handleEdit(m,l):k.scrollToRow(m.row,m.column).then(()=>{k.handleSelected(m,l)})):k.scrollToRow(m.row,m.column).then(()=>{k.handleSelected(m,l)}))},moveCurrentRow(e,t,l){var o=M.treeConfig;let r=y.currentRow;var n=O.afterFullData,u=c.value,u=u.children||u.childrenField;let i;if(r?o?({index:o,items:u}=_xeUtils.default.findTree(n,e=>e===r,{children:u}),e&&0<o?i=u[o-1]:t&&o<u.length-1&&(i=u[o+1])):(u=k.getVTRowIndex(r),e&&0<u?i=n[u-1]:t&&u<n.length-1&&(i=n[u+1])):i=n[0],i){l.preventDefault();let e={$table:k,row:i,rowIndex:k.getRowIndex(i),$rowIndex:k.getVMRowIndex(i)};k.scrollToRow(i).then(()=>k.triggerCurrentRowEvent(l,e))}},moveCurrentColumn(e,t,l){var o=y.currentColumn,r=O.visibleColumn;let n=null;if(o?(o=k.getVTColumnIndex(o),e&&0<o?n=r[o-1]:t&&o<r.length-1&&(n=r[o+1])):n=r[0],n){l.preventDefault();let e={$table:k,column:n,columnIndex:k.getColumnIndex(n),$columnIndex:k.getVMColumnIndex(n)};k.scrollToColumn(n).then(()=>k.triggerCurrentColumnEvent(l,e))}},moveArrowSelected(e,t,l,o,r,n){var{highlightCurrentRow:u,highlightCurrentColumn:i}=M,a=F.value,s=w.value,c=C.value,d=p.value,e=g(n,e,t,l,o,r);(a.isCurrent||u)&&(s.isFollowSelected?k.triggerCurrentRowEvent(n,e):(l||r)&&(a.isCurrent||u)&&k.moveCurrentRow(l,r,n)),(c.isCurrent||i)&&(d.isFollowSelected?k.triggerCurrentColumnEvent(n,e):(t||o)&&(c.isCurrent||i)&&k.moveCurrentColumn(t,o,n))},moveEnterSelected(e,t,l,o,r,n){var{highlightCurrentRow:u,highlightCurrentColumn:i}=M,a=F.value,s=w.value,c=C.value,d=p.value,e=g(n,e,t,l,o,r);(a.isCurrent||u)&&s.isFollowSelected&&k.triggerCurrentRowEvent(n,e),(c.isCurrent||i)&&d.isFollowSelected&&k.triggerCurrentColumnEvent(n,e)},moveSelected(e,t,l,o,r,n){g(n,e,t,l,o,r)},handleCellMousedownEvent:(e,t)=>{var{editConfig:l,checkboxConfig:o,mouseConfig:r}=M,n=a.value,u=s.value,i=v.value;if(r&&u.area&&k.triggerCellAreaMousedownEvent)return k.triggerCellAreaMousedownEvent(e,t);o&&n.range&&d(e,t),r&&u.selected&&(l&&"cell"!==i.mode||k.handleSelected(t,e))}}}});