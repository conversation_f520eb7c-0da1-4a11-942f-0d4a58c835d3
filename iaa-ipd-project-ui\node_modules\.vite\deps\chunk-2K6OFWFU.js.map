{"version": 3, "sources": ["../../amis/esm/renderers/Table2/ColumnToggler.js"], "sourcesContent": ["/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __rest, __assign, __decorate } from 'tslib';\nimport React from 'react';\nimport { isVisible, Renderer } from 'amis-core';\nimport { Checkbox } from 'amis-ui';\nimport ColumnToggler from '../Table/ColumnToggler.js';\n\nvar ColumnTogglerRenderer = /** @class */ (function (_super) {\n    __extends(ColumnTogglerRenderer, _super);\n    function ColumnTogglerRenderer() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    ColumnTogglerRenderer.prototype.render = function () {\n        var _a = this.props, className = _a.className, store = _a.store, render = _a.render, ns = _a.classPrefix, cx = _a.classnames, tooltip = _a.tooltip, align = _a.align, cols = _a.cols, toggleAllColumns = _a.toggleAllColumns, toggleToggle = _a.toggleToggle, data = _a.data, size = _a.size, popOverContainer = _a.popOverContainer, rest = __rest(_a, [\"className\", \"store\", \"render\", \"classPrefix\", \"classnames\", \"tooltip\", \"align\", \"cols\", \"toggleAllColumns\", \"toggleToggle\", \"data\", \"size\", \"popOverContainer\"]);\n        var __ = rest.translate;\n        var env = rest.env;\n        if (!cols) {\n            return null;\n        }\n        var toggableColumns = cols.filter(function (item) {\n            return isVisible(item.pristine || item, data) && item.toggable !== false;\n        });\n        var activeToggaleColumns = toggableColumns.filter(function (item) { return item.toggled !== false; });\n        return (React.createElement(ColumnToggler, __assign({}, rest, { render: render, tooltip: tooltip || __('Table.columnsVisibility'), tooltipContainer: popOverContainer || env.getModalContainer, isActived: cols.findIndex(function (column) { return !column.toggled; }) !== -1, align: align !== null && align !== void 0 ? align : 'right', size: size || 'sm', classnames: cx, classPrefix: ns, key: \"columns-toggable\", columns: cols, activeToggaleColumns: activeToggaleColumns, data: data }),\n            (toggableColumns === null || toggableColumns === void 0 ? void 0 : toggableColumns.length) ? (React.createElement(\"li\", { className: cx('ColumnToggler-menuItem'), key: 'selectAll', onClick: function () {\n                    toggleAllColumns &&\n                        toggleAllColumns((activeToggaleColumns === null || activeToggaleColumns === void 0 ? void 0 : activeToggaleColumns.length) <= 0);\n                } },\n                React.createElement(Checkbox, { size: \"sm\", classPrefix: ns, key: \"checkall\", checked: !!(activeToggaleColumns === null || activeToggaleColumns === void 0 ? void 0 : activeToggaleColumns.length), partial: !!((activeToggaleColumns === null || activeToggaleColumns === void 0 ? void 0 : activeToggaleColumns.length) &&\n                        (activeToggaleColumns === null || activeToggaleColumns === void 0 ? void 0 : activeToggaleColumns.length) !== (toggableColumns === null || toggableColumns === void 0 ? void 0 : toggableColumns.length)) }, __('Select.checkAll')))) : null, toggableColumns === null || toggableColumns === void 0 ? void 0 :\n            toggableColumns.map(function (column, index) { return (React.createElement(\"li\", { className: cx('ColumnToggler-menuItem'), key: 'item' + (column.index || index), onClick: function () {\n                    toggleToggle && toggleToggle(index);\n                } },\n                React.createElement(Checkbox, { size: \"sm\", classPrefix: ns, checked: column.toggled !== false }, column.title\n                    ? render('tpl', column.title)\n                    : column.label || null))); })));\n    };\n    ColumnTogglerRenderer = __decorate([\n        Renderer({\n            type: 'column-toggler',\n            name: 'column-toggler'\n        })\n    ], ColumnTogglerRenderer);\n    return ColumnTogglerRenderer;\n}(React.Component));\n\nexport { ColumnTogglerRenderer };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAOA,mBAAkB;AAKlB,IAAI;AAAA;AAAA,EAAuC,SAAU,QAAQ;AACzD,cAAUA,wBAAuB,MAAM;AACvC,aAASA,yBAAwB;AAC7B,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,uBAAsB,UAAU,SAAS,WAAY;AACjD,UAAI,KAAK,KAAK,OAAO,YAAY,GAAG,WAAW,QAAQ,GAAG,OAAO,SAAS,GAAG,QAAQ,KAAK,GAAG,aAAa,KAAK,GAAG,YAAY,UAAU,GAAG,SAAS,QAAQ,GAAG,OAAO,OAAO,GAAG,MAAM,mBAAmB,GAAG,kBAAkB,eAAe,GAAG,cAAc,OAAO,GAAG,MAAM,OAAO,GAAG,MAAM,mBAAmB,GAAG,kBAAkB,OAAO,OAAO,IAAI,CAAC,aAAa,SAAS,UAAU,eAAe,cAAc,WAAW,SAAS,QAAQ,oBAAoB,gBAAgB,QAAQ,QAAQ,kBAAkB,CAAC;AACzf,UAAI,KAAK,KAAK;AACd,UAAI,MAAM,KAAK;AACf,UAAI,CAAC,MAAM;AACP,eAAO;AAAA,MACX;AACA,UAAI,kBAAkB,KAAK,OAAO,SAAU,MAAM;AAC9C,eAAO,UAAU,KAAK,YAAY,MAAM,IAAI,KAAK,KAAK,aAAa;AAAA,MACvE,CAAC;AACD,UAAI,uBAAuB,gBAAgB,OAAO,SAAU,MAAM;AAAE,eAAO,KAAK,YAAY;AAAA,MAAO,CAAC;AACpG,aAAQ,aAAAC,QAAM;AAAA,QAAc;AAAA,QAAe,SAAS,CAAC,GAAG,MAAM,EAAE,QAAgB,SAAS,WAAW,GAAG,yBAAyB,GAAG,kBAAkB,oBAAoB,IAAI,mBAAmB,WAAW,KAAK,UAAU,SAAU,QAAQ;AAAE,iBAAO,CAAC,OAAO;AAAA,QAAS,CAAC,MAAM,IAAI,OAAO,UAAU,QAAQ,UAAU,SAAS,QAAQ,SAAS,MAAM,QAAQ,MAAM,YAAY,IAAI,aAAa,IAAI,KAAK,oBAAoB,SAAS,MAAM,sBAA4C,KAAW,CAAC;AAAA,SAC9d,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,UAAW,aAAAA,QAAM;AAAA,UAAc;AAAA,UAAM,EAAE,WAAW,GAAG,wBAAwB,GAAG,KAAK,aAAa,SAAS,WAAY;AAClM,gCACI,kBAAkB,yBAAyB,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,WAAW,CAAC;AAAA,UACvI,EAAE;AAAA,UACF,aAAAA,QAAM,cAAc,YAAU,EAAE,MAAM,MAAM,aAAa,IAAI,KAAK,YAAY,SAAS,CAAC,EAAE,yBAAyB,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,SAAS,SAAS,CAAC,GAAG,yBAAyB,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,YACzS,yBAAyB,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,aAAa,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,SAAS,GAAG,GAAG,iBAAiB,CAAC;AAAA,QAAC,IAAK;AAAA,QAAM,oBAAoB,QAAQ,oBAAoB,SAAS,SACnT,gBAAgB,IAAI,SAAU,QAAQ,OAAO;AAAE,iBAAQ,aAAAA,QAAM;AAAA,YAAc;AAAA,YAAM,EAAE,WAAW,GAAG,wBAAwB,GAAG,KAAK,UAAU,OAAO,SAAS,QAAQ,SAAS,WAAY;AAChL,8BAAgB,aAAa,KAAK;AAAA,YACtC,EAAE;AAAA,YACF,aAAAA,QAAM,cAAc,YAAU,EAAE,MAAM,MAAM,aAAa,IAAI,SAAS,OAAO,YAAY,MAAM,GAAG,OAAO,QACnG,OAAO,OAAO,OAAO,KAAK,IAC1B,OAAO,SAAS,IAAI;AAAA,UAAC;AAAA,QAAI,CAAC;AAAA,MAAC;AAAA,IAC7C;AACA,IAAAD,yBAAwB,WAAW;AAAA,MAC/B,SAAS;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,MACV,CAAC;AAAA,IACL,GAAGA,sBAAqB;AACxB,WAAOA;AAAA,EACX,EAAE,aAAAC,QAAM,SAAS;AAAA;", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "React"]}