{"version": 3, "sources": ["../../react-cropper/dist/react-cropper.es.js"], "sourcesContent": ["import e,{useRef as r,useEffect as o}from\"react\";import t from\"cropperjs\";var n=function(){return n=Object.assign||function(e){for(var r,o=1,t=arguments.length;o<t;o++)for(var n in r=arguments[o])Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n]);return e},n.apply(this,arguments)};function a(e,r){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.indexOf(t)<0&&(o[t]=e[t]);if(null!=e&&\"function\"==typeof Object.getOwnPropertySymbols){var n=0;for(t=Object.getOwnPropertySymbols(e);n<t.length;n++)r.indexOf(t[n])<0&&Object.prototype.propertyIsEnumerable.call(e,t[n])&&(o[t[n]]=e[t[n]])}return o}var c=[\"aspectRatio\",\"autoCrop\",\"autoCropArea\",\"background\",\"center\",\"checkCrossOrigin\",\"checkOrientation\",\"cropBoxMovable\",\"cropBoxResizable\",\"data\",\"dragMode\",\"guides\",\"highlight\",\"initialAspectRatio\",\"minCanvasHeight\",\"minCanvasWidth\",\"minContainerHeight\",\"minContainerWidth\",\"minCropBoxHeight\",\"minCropBoxWidth\",\"modal\",\"movable\",\"preview\",\"responsive\",\"restore\",\"rotatable\",\"scalable\",\"toggleDragModeOnDblclick\",\"viewMode\",\"wheelZoomRatio\",\"zoomOnTouch\",\"zoomOnWheel\",\"zoomable\",\"cropstart\",\"cropmove\",\"cropend\",\"crop\",\"zoom\",\"ready\"],i={opacity:0,maxWidth:\"100%\"},l=e.forwardRef((function(l,s){var u=a(l,[]),p=u.dragMode,d=void 0===p?\"crop\":p,v=u.src,f=u.style,m=u.className,g=u.crossOrigin,y=u.scaleX,b=u.scaleY,h=u.enable,O=u.zoomTo,T=u.rotateTo,z=u.alt,C=void 0===z?\"picture\":z,w=u.ready,x=u.onInitialized,j=a(u,[\"dragMode\",\"src\",\"style\",\"className\",\"crossOrigin\",\"scaleX\",\"scaleY\",\"enable\",\"zoomTo\",\"rotateTo\",\"alt\",\"ready\",\"onInitialized\"]),M={scaleY:b,scaleX:y,enable:h,zoomTo:O,rotateTo:T},E=function(){for(var o=[],t=0;t<arguments.length;t++)o[t]=arguments[t];var n=r(null);return e.useEffect((function(){o.forEach((function(e){e&&(\"function\"==typeof e?e(n.current):e.current=n.current)}))}),[o]),n}(s,r(null));o((function(){var e;(null===(e=E.current)||void 0===e?void 0:e.cropper)&&\"number\"==typeof O&&E.current.cropper.zoomTo(O)}),[u.zoomTo]),o((function(){var e;(null===(e=E.current)||void 0===e?void 0:e.cropper)&&void 0!==v&&E.current.cropper.reset().clear().replace(v)}),[v]),o((function(){if(null!==E.current){var e=new t(E.current,n(n({dragMode:d},j),{ready:function(e){null!==e.currentTarget&&function(e,r){void 0===r&&(r={});var o=r.enable,t=void 0===o||o,n=r.scaleX,a=void 0===n?1:n,c=r.scaleY,i=void 0===c?1:c,l=r.zoomTo,s=void 0===l?0:l,u=r.rotateTo;t?e.enable():e.disable(),e.scaleX(a),e.scaleY(i),void 0!==u&&e.rotateTo(u),s>0&&e.zoomTo(s)}(e.currentTarget.cropper,M),w&&w(e)}}));x&&x(e)}return function(){var e,r;null===(r=null===(e=E.current)||void 0===e?void 0:e.cropper)||void 0===r||r.destroy()}}),[E]);var R=function(e){return c.reduce((function(e,r){var o=e,t=r;return o[t],a(o,[\"symbol\"==typeof t?t:t+\"\"])}),e)}(n(n({},j),{crossOrigin:g,src:v,alt:C}));return e.createElement(\"div\",{style:f,className:m},e.createElement(\"img\",n({},R,{style:i,ref:E})))}));export{l as Cropper,l as default};\n"], "mappings": ";;;;;;;;;;;AAAA,mBAAyC;AAAiC,IAAI,IAAE,WAAU;AAAC,SAAO,IAAE,OAAO,UAAQ,SAASA,IAAE;AAAC,aAAQC,IAAEC,KAAE,GAAE,IAAE,UAAU,QAAOA,KAAE,GAAEA;AAAI,eAAQC,MAAKF,KAAE,UAAUC,EAAC;AAAE,eAAO,UAAU,eAAe,KAAKD,IAAEE,EAAC,MAAIH,GAAEG,EAAC,IAAEF,GAAEE,EAAC;AAAG,WAAOH;AAAA,EAAC,GAAE,EAAE,MAAM,MAAK,SAAS;AAAC;AAAE,SAAS,EAAEA,IAAEC,IAAE;AAAC,MAAIC,KAAE,CAAC;AAAE,WAAQ,KAAKF;AAAE,WAAO,UAAU,eAAe,KAAKA,IAAE,CAAC,KAAGC,GAAE,QAAQ,CAAC,IAAE,MAAIC,GAAE,CAAC,IAAEF,GAAE,CAAC;AAAG,MAAG,QAAMA,MAAG,cAAY,OAAO,OAAO,uBAAsB;AAAC,QAAIG,KAAE;AAAE,SAAI,IAAE,OAAO,sBAAsBH,EAAC,GAAEG,KAAE,EAAE,QAAOA;AAAI,MAAAF,GAAE,QAAQ,EAAEE,EAAC,CAAC,IAAE,KAAG,OAAO,UAAU,qBAAqB,KAAKH,IAAE,EAAEG,EAAC,CAAC,MAAID,GAAE,EAAEC,EAAC,CAAC,IAAEH,GAAE,EAAEG,EAAC,CAAC;AAAA,EAAE;AAAC,SAAOD;AAAC;AAAC,IAAI,IAAE,CAAC,eAAc,YAAW,gBAAe,cAAa,UAAS,oBAAmB,oBAAmB,kBAAiB,oBAAmB,QAAO,YAAW,UAAS,aAAY,sBAAqB,mBAAkB,kBAAiB,sBAAqB,qBAAoB,oBAAmB,mBAAkB,SAAQ,WAAU,WAAU,cAAa,WAAU,aAAY,YAAW,4BAA2B,YAAW,kBAAiB,eAAc,eAAc,YAAW,aAAY,YAAW,WAAU,QAAO,QAAO,OAAO;AAA1hB,IAA4hB,IAAE,EAAC,SAAQ,GAAE,UAAS,OAAM;AAAxjB,IAA0jB,IAAE,aAAAF,QAAE,WAAY,SAASI,IAAE,GAAE;AAAC,MAAI,IAAE,EAAEA,IAAE,CAAC,CAAC,GAAE,IAAE,EAAE,UAAS,IAAE,WAAS,IAAE,SAAO,GAAE,IAAE,EAAE,KAAI,IAAE,EAAE,OAAM,IAAE,EAAE,WAAU,IAAE,EAAE,aAAY,IAAE,EAAE,QAAO,IAAE,EAAE,QAAO,IAAE,EAAE,QAAO,IAAE,EAAE,QAAO,IAAE,EAAE,UAAS,IAAE,EAAE,KAAI,IAAE,WAAS,IAAE,YAAU,GAAE,IAAE,EAAE,OAAM,IAAE,EAAE,eAAc,IAAE,EAAE,GAAE,CAAC,YAAW,OAAM,SAAQ,aAAY,eAAc,UAAS,UAAS,UAAS,UAAS,YAAW,OAAM,SAAQ,eAAe,CAAC,GAAE,IAAE,EAAC,QAAO,GAAE,QAAO,GAAE,QAAO,GAAE,QAAO,GAAE,UAAS,EAAC,GAAE,IAAE,WAAU;AAAC,aAAQF,KAAE,CAAC,GAAE,IAAE,GAAE,IAAE,UAAU,QAAO;AAAI,MAAAA,GAAE,CAAC,IAAE,UAAU,CAAC;AAAE,QAAIC,SAAE,aAAAF,QAAE,IAAI;AAAE,WAAO,aAAAD,QAAE,UAAW,WAAU;AAAC,MAAAE,GAAE,QAAS,SAASF,IAAE;AAAC,QAAAA,OAAI,cAAY,OAAOA,KAAEA,GAAEG,GAAE,OAAO,IAAEH,GAAE,UAAQG,GAAE;AAAA,MAAQ,CAAE;AAAA,IAAC,GAAG,CAACD,EAAC,CAAC,GAAEC;AAAA,EAAC,EAAE,OAAE,aAAAF,QAAE,IAAI,CAAC;AAAE,mBAAAC,WAAG,WAAU;AAAC,QAAIF;AAAE,KAAC,UAAQA,KAAE,EAAE,YAAU,WAASA,KAAE,SAAOA,GAAE,YAAU,YAAU,OAAO,KAAG,EAAE,QAAQ,QAAQ,OAAO,CAAC;AAAA,EAAC,GAAG,CAAC,EAAE,MAAM,CAAC,OAAE,aAAAE,WAAG,WAAU;AAAC,QAAIF;AAAE,KAAC,UAAQA,KAAE,EAAE,YAAU,WAASA,KAAE,SAAOA,GAAE,YAAU,WAAS,KAAG,EAAE,QAAQ,QAAQ,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC;AAAA,EAAC,GAAG,CAAC,CAAC,CAAC,OAAE,aAAAE,WAAG,WAAU;AAAC,QAAG,SAAO,EAAE,SAAQ;AAAC,UAAIF,KAAE,IAAI,QAAE,EAAE,SAAQ,EAAE,EAAE,EAAC,UAAS,EAAC,GAAE,CAAC,GAAE,EAAC,OAAM,SAASA,IAAE;AAAC,iBAAOA,GAAE,iBAAe,SAASA,IAAEC,IAAE;AAAC,qBAASA,OAAIA,KAAE,CAAC;AAAG,cAAIC,KAAED,GAAE,QAAO,IAAE,WAASC,MAAGA,IAAEC,KAAEF,GAAE,QAAOI,KAAE,WAASF,KAAE,IAAEA,IAAEG,KAAEL,GAAE,QAAOM,KAAE,WAASD,KAAE,IAAEA,IAAEF,KAAEH,GAAE,QAAOO,KAAE,WAASJ,KAAE,IAAEA,IAAEK,KAAER,GAAE;AAAS,cAAED,GAAE,OAAO,IAAEA,GAAE,QAAQ,GAAEA,GAAE,OAAOK,EAAC,GAAEL,GAAE,OAAOO,EAAC,GAAE,WAASE,MAAGT,GAAE,SAASS,EAAC,GAAED,KAAE,KAAGR,GAAE,OAAOQ,EAAC;AAAA,QAAC,EAAER,GAAE,cAAc,SAAQ,CAAC,GAAE,KAAG,EAAEA,EAAC;AAAA,MAAC,EAAC,CAAC,CAAC;AAAE,WAAG,EAAEA,EAAC;AAAA,IAAC;AAAC,WAAO,WAAU;AAAC,UAAIA,IAAEC;AAAE,gBAAQA,KAAE,UAAQD,KAAE,EAAE,YAAU,WAASA,KAAE,SAAOA,GAAE,YAAU,WAASC,MAAGA,GAAE,QAAQ;AAAA,IAAC;AAAA,EAAC,GAAG,CAAC,CAAC,CAAC;AAAE,MAAI,IAAE,SAASD,IAAE;AAAC,WAAO,EAAE,OAAQ,SAASA,IAAEC,IAAE;AAAC,UAAIC,KAAEF,IAAE,IAAEC;AAAE,aAAOC,GAAE,CAAC,GAAE,EAAEA,IAAE,CAAC,YAAU,OAAO,IAAE,IAAE,IAAE,EAAE,CAAC;AAAA,IAAC,GAAGF,EAAC;AAAA,EAAC,EAAE,EAAE,EAAE,CAAC,GAAE,CAAC,GAAE,EAAC,aAAY,GAAE,KAAI,GAAE,KAAI,EAAC,CAAC,CAAC;AAAE,SAAO,aAAAA,QAAE,cAAc,OAAM,EAAC,OAAM,GAAE,WAAU,EAAC,GAAE,aAAAA,QAAE,cAAc,OAAM,EAAE,CAAC,GAAE,GAAE,EAAC,OAAM,GAAE,KAAI,EAAC,CAAC,CAAC,CAAC;AAAC,CAAE;", "names": ["e", "r", "o", "n", "l", "a", "c", "i", "s", "u"]}