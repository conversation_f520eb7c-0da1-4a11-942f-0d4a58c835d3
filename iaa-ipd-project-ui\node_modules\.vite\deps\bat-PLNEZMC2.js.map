{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/bat/bat.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    comments: {\n        lineComment: 'REM'\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' }\n    ],\n    surroundingPairs: [\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' }\n    ],\n    folding: {\n        markers: {\n            start: new RegExp('^\\\\s*(::\\\\s*|REM\\\\s+)#region'),\n            end: new RegExp('^\\\\s*(::\\\\s*|REM\\\\s+)#endregion')\n        }\n    }\n};\nexport var language = {\n    defaultToken: '',\n    ignoreCase: true,\n    tokenPostfix: '.bat',\n    brackets: [\n        { token: 'delimiter.bracket', open: '{', close: '}' },\n        { token: 'delimiter.parenthesis', open: '(', close: ')' },\n        { token: 'delimiter.square', open: '[', close: ']' }\n    ],\n    keywords: /call|defined|echo|errorlevel|exist|for|goto|if|pause|set|shift|start|title|not|pushd|popd/,\n    // we include these common regular expressions\n    symbols: /[=><!~?&|+\\-*\\/\\^;\\.,]+/,\n    escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n    // The main tokenizer for our languages\n    tokenizer: {\n        root: [\n            [/^(\\s*)(rem(?:\\s.*|))$/, ['', 'comment']],\n            [/(\\@?)(@keywords)(?!\\w)/, [{ token: 'keyword' }, { token: 'keyword.$2' }]],\n            // whitespace\n            [/[ \\t\\r\\n]+/, ''],\n            // blocks\n            [/setlocal(?!\\w)/, 'keyword.tag-setlocal'],\n            [/endlocal(?!\\w)/, 'keyword.tag-setlocal'],\n            // words\n            [/[a-zA-Z_]\\w*/, ''],\n            // labels\n            [/:\\w*/, 'metatag'],\n            // variables\n            [/%[^%]+%/, 'variable'],\n            [/%%[\\w]+(?!\\w)/, 'variable'],\n            // punctuations\n            [/[{}()\\[\\]]/, '@brackets'],\n            [/@symbols/, 'delimiter'],\n            // numbers\n            [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, 'number.float'],\n            [/0[xX][0-9a-fA-F_]*[0-9a-fA-F]/, 'number.hex'],\n            [/\\d+/, 'number'],\n            // punctuation: after number because of .\\d floats\n            [/[;,.]/, 'delimiter'],\n            // strings:\n            [/\"/, 'string', '@string.\"'],\n            [/'/, 'string', \"@string.'\"]\n        ],\n        string: [\n            [\n                /[^\\\\\"'%]+/,\n                {\n                    cases: {\n                        '@eos': { token: 'string', next: '@popall' },\n                        '@default': 'string'\n                    }\n                }\n            ],\n            [/@escapes/, 'string.escape'],\n            [/\\\\./, 'string.escape.invalid'],\n            [/%[\\w ]+%/, 'variable'],\n            [/%%[\\w]+(?!\\w)/, 'variable'],\n            [\n                /[\"']/,\n                {\n                    cases: {\n                        '$#==$S2': { token: 'string', next: '@pop' },\n                        '@default': 'string'\n                    }\n                }\n            ],\n            [/$/, 'string', '@popall']\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,aAAa;AAAA,EACjB;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,SAAS;AAAA,IACL,SAAS;AAAA,MACL,OAAO,IAAI,OAAO,8BAA8B;AAAA,MAChD,KAAK,IAAI,OAAO,iCAAiC;AAAA,IACrD;AAAA,EACJ;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,UAAU;AAAA,IACN,EAAE,OAAO,qBAAqB,MAAM,KAAK,OAAO,IAAI;AAAA,IACpD,EAAE,OAAO,yBAAyB,MAAM,KAAK,OAAO,IAAI;AAAA,IACxD,EAAE,OAAO,oBAAoB,MAAM,KAAK,OAAO,IAAI;AAAA,EACvD;AAAA,EACA,UAAU;AAAA;AAAA,EAEV,SAAS;AAAA,EACT,SAAS;AAAA;AAAA,EAET,WAAW;AAAA,IACP,MAAM;AAAA,MACF,CAAC,yBAAyB,CAAC,IAAI,SAAS,CAAC;AAAA,MACzC,CAAC,0BAA0B,CAAC,EAAE,OAAO,UAAU,GAAG,EAAE,OAAO,aAAa,CAAC,CAAC;AAAA;AAAA,MAE1E,CAAC,cAAc,EAAE;AAAA;AAAA,MAEjB,CAAC,kBAAkB,sBAAsB;AAAA,MACzC,CAAC,kBAAkB,sBAAsB;AAAA;AAAA,MAEzC,CAAC,gBAAgB,EAAE;AAAA;AAAA,MAEnB,CAAC,QAAQ,SAAS;AAAA;AAAA,MAElB,CAAC,WAAW,UAAU;AAAA,MACtB,CAAC,iBAAiB,UAAU;AAAA;AAAA,MAE5B,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,YAAY,WAAW;AAAA;AAAA,MAExB,CAAC,4BAA4B,cAAc;AAAA,MAC3C,CAAC,iCAAiC,YAAY;AAAA,MAC9C,CAAC,OAAO,QAAQ;AAAA;AAAA,MAEhB,CAAC,SAAS,WAAW;AAAA;AAAA,MAErB,CAAC,KAAK,UAAU,WAAW;AAAA,MAC3B,CAAC,KAAK,UAAU,WAAW;AAAA,IAC/B;AAAA,IACA,QAAQ;AAAA,MACJ;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ,EAAE,OAAO,UAAU,MAAM,UAAU;AAAA,YAC3C,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,YAAY,UAAU;AAAA,MACvB,CAAC,iBAAiB,UAAU;AAAA,MAC5B;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,WAAW,EAAE,OAAO,UAAU,MAAM,OAAO;AAAA,YAC3C,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,CAAC,KAAK,UAAU,SAAS;AAAA,IAC7B;AAAA,EACJ;AACJ;", "names": []}