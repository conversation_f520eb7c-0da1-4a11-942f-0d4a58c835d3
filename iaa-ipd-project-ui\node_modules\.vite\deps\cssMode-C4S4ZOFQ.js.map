{"version": 3, "sources": ["../../monaco-editor/esm/vs/language/css/workerManager.js", "../../monaco-editor/esm/vs/language/css/_deps/vscode-languageserver-types/main.js", "../../monaco-editor/esm/vs/language/css/languageFeatures.js", "../../monaco-editor/esm/vs/language/css/cssMode.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { editor } from './fillers/monaco-editor-core.js';\nvar STOP_WHEN_IDLE_FOR = 2 * 60 * 1000; // 2min\nvar WorkerManager = /** @class */ (function () {\n    function WorkerManager(defaults) {\n        var _this = this;\n        this._defaults = defaults;\n        this._worker = null;\n        this._idleCheckInterval = window.setInterval(function () { return _this._checkIfIdle(); }, 30 * 1000);\n        this._lastUsedTime = 0;\n        this._configChangeListener = this._defaults.onDidChange(function () { return _this._stopWorker(); });\n    }\n    WorkerManager.prototype._stopWorker = function () {\n        if (this._worker) {\n            this._worker.dispose();\n            this._worker = null;\n        }\n        this._client = null;\n    };\n    WorkerManager.prototype.dispose = function () {\n        clearInterval(this._idleCheckInterval);\n        this._configChangeListener.dispose();\n        this._stopWorker();\n    };\n    WorkerManager.prototype._checkIfIdle = function () {\n        if (!this._worker) {\n            return;\n        }\n        var timePassedSinceLastUsed = Date.now() - this._lastUsedTime;\n        if (timePassedSinceLastUsed > STOP_WHEN_IDLE_FOR) {\n            this._stopWorker();\n        }\n    };\n    WorkerManager.prototype._getClient = function () {\n        this._lastUsedTime = Date.now();\n        if (!this._client) {\n            this._worker = editor.createWebWorker({\n                // module that exports the create() method and returns a `CSSWorker` instance\n                moduleId: 'vs/language/css/cssWorker',\n                label: this._defaults.languageId,\n                // passed in to the create() method\n                createData: {\n                    options: this._defaults.options,\n                    languageId: this._defaults.languageId\n                }\n            });\n            this._client = this._worker.getProxy();\n        }\n        return this._client;\n    };\n    WorkerManager.prototype.getLanguageServiceWorker = function () {\n        var _this = this;\n        var resources = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            resources[_i] = arguments[_i];\n        }\n        var _client;\n        return this._getClient()\n            .then(function (client) {\n            _client = client;\n        })\n            .then(function (_) {\n            return _this._worker.withSyncedResources(resources);\n        })\n            .then(function (_) { return _client; });\n    };\n    return WorkerManager;\n}());\nexport { WorkerManager };\n", "/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\n'use strict';\nexport var integer;\n(function (integer) {\n    integer.MIN_VALUE = -**********;\n    integer.MAX_VALUE = **********;\n})(integer || (integer = {}));\nexport var uinteger;\n(function (uinteger) {\n    uinteger.MIN_VALUE = 0;\n    uinteger.MAX_VALUE = **********;\n})(uinteger || (uinteger = {}));\n/**\n * The Position namespace provides helper functions to work with\n * [Position](#Position) literals.\n */\nexport var Position;\n(function (Position) {\n    /**\n     * Creates a new Position literal from the given line and character.\n     * @param line The position's line.\n     * @param character The position's character.\n     */\n    function create(line, character) {\n        if (line === Number.MAX_VALUE) {\n            line = uinteger.MAX_VALUE;\n        }\n        if (character === Number.MAX_VALUE) {\n            character = uinteger.MAX_VALUE;\n        }\n        return { line: line, character: character };\n    }\n    Position.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Position](#Position) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Is.uinteger(candidate.line) && Is.uinteger(candidate.character);\n    }\n    Position.is = is;\n})(Position || (Position = {}));\n/**\n * The Range namespace provides helper functions to work with\n * [Range](#Range) literals.\n */\nexport var Range;\n(function (Range) {\n    function create(one, two, three, four) {\n        if (Is.uinteger(one) && Is.uinteger(two) && Is.uinteger(three) && Is.uinteger(four)) {\n            return { start: Position.create(one, two), end: Position.create(three, four) };\n        }\n        else if (Position.is(one) && Position.is(two)) {\n            return { start: one, end: two };\n        }\n        else {\n            throw new Error(\"Range#create called with invalid arguments[\" + one + \", \" + two + \", \" + three + \", \" + four + \"]\");\n        }\n    }\n    Range.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Range](#Range) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Position.is(candidate.start) && Position.is(candidate.end);\n    }\n    Range.is = is;\n})(Range || (Range = {}));\n/**\n * The Location namespace provides helper functions to work with\n * [Location](#Location) literals.\n */\nexport var Location;\n(function (Location) {\n    /**\n     * Creates a Location literal.\n     * @param uri The location's uri.\n     * @param range The location's range.\n     */\n    function create(uri, range) {\n        return { uri: uri, range: range };\n    }\n    Location.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Location](#Location) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Range.is(candidate.range) && (Is.string(candidate.uri) || Is.undefined(candidate.uri));\n    }\n    Location.is = is;\n})(Location || (Location = {}));\n/**\n * The LocationLink namespace provides helper functions to work with\n * [LocationLink](#LocationLink) literals.\n */\nexport var LocationLink;\n(function (LocationLink) {\n    /**\n     * Creates a LocationLink literal.\n     * @param targetUri The definition's uri.\n     * @param targetRange The full range of the definition.\n     * @param targetSelectionRange The span of the symbol definition at the target.\n     * @param originSelectionRange The span of the symbol being defined in the originating source file.\n     */\n    function create(targetUri, targetRange, targetSelectionRange, originSelectionRange) {\n        return { targetUri: targetUri, targetRange: targetRange, targetSelectionRange: targetSelectionRange, originSelectionRange: originSelectionRange };\n    }\n    LocationLink.create = create;\n    /**\n     * Checks whether the given literal conforms to the [LocationLink](#LocationLink) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Range.is(candidate.targetRange) && Is.string(candidate.targetUri)\n            && (Range.is(candidate.targetSelectionRange) || Is.undefined(candidate.targetSelectionRange))\n            && (Range.is(candidate.originSelectionRange) || Is.undefined(candidate.originSelectionRange));\n    }\n    LocationLink.is = is;\n})(LocationLink || (LocationLink = {}));\n/**\n * The Color namespace provides helper functions to work with\n * [Color](#Color) literals.\n */\nexport var Color;\n(function (Color) {\n    /**\n     * Creates a new Color literal.\n     */\n    function create(red, green, blue, alpha) {\n        return {\n            red: red,\n            green: green,\n            blue: blue,\n            alpha: alpha,\n        };\n    }\n    Color.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Color](#Color) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.numberRange(candidate.red, 0, 1)\n            && Is.numberRange(candidate.green, 0, 1)\n            && Is.numberRange(candidate.blue, 0, 1)\n            && Is.numberRange(candidate.alpha, 0, 1);\n    }\n    Color.is = is;\n})(Color || (Color = {}));\n/**\n * The ColorInformation namespace provides helper functions to work with\n * [ColorInformation](#ColorInformation) literals.\n */\nexport var ColorInformation;\n(function (ColorInformation) {\n    /**\n     * Creates a new ColorInformation literal.\n     */\n    function create(range, color) {\n        return {\n            range: range,\n            color: color,\n        };\n    }\n    ColorInformation.create = create;\n    /**\n     * Checks whether the given literal conforms to the [ColorInformation](#ColorInformation) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Range.is(candidate.range) && Color.is(candidate.color);\n    }\n    ColorInformation.is = is;\n})(ColorInformation || (ColorInformation = {}));\n/**\n * The Color namespace provides helper functions to work with\n * [ColorPresentation](#ColorPresentation) literals.\n */\nexport var ColorPresentation;\n(function (ColorPresentation) {\n    /**\n     * Creates a new ColorInformation literal.\n     */\n    function create(label, textEdit, additionalTextEdits) {\n        return {\n            label: label,\n            textEdit: textEdit,\n            additionalTextEdits: additionalTextEdits,\n        };\n    }\n    ColorPresentation.create = create;\n    /**\n     * Checks whether the given literal conforms to the [ColorInformation](#ColorInformation) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.string(candidate.label)\n            && (Is.undefined(candidate.textEdit) || TextEdit.is(candidate))\n            && (Is.undefined(candidate.additionalTextEdits) || Is.typedArray(candidate.additionalTextEdits, TextEdit.is));\n    }\n    ColorPresentation.is = is;\n})(ColorPresentation || (ColorPresentation = {}));\n/**\n * Enum of known range kinds\n */\nexport var FoldingRangeKind;\n(function (FoldingRangeKind) {\n    /**\n     * Folding range for a comment\n     */\n    FoldingRangeKind[\"Comment\"] = \"comment\";\n    /**\n     * Folding range for a imports or includes\n     */\n    FoldingRangeKind[\"Imports\"] = \"imports\";\n    /**\n     * Folding range for a region (e.g. `#region`)\n     */\n    FoldingRangeKind[\"Region\"] = \"region\";\n})(FoldingRangeKind || (FoldingRangeKind = {}));\n/**\n * The folding range namespace provides helper functions to work with\n * [FoldingRange](#FoldingRange) literals.\n */\nexport var FoldingRange;\n(function (FoldingRange) {\n    /**\n     * Creates a new FoldingRange literal.\n     */\n    function create(startLine, endLine, startCharacter, endCharacter, kind) {\n        var result = {\n            startLine: startLine,\n            endLine: endLine\n        };\n        if (Is.defined(startCharacter)) {\n            result.startCharacter = startCharacter;\n        }\n        if (Is.defined(endCharacter)) {\n            result.endCharacter = endCharacter;\n        }\n        if (Is.defined(kind)) {\n            result.kind = kind;\n        }\n        return result;\n    }\n    FoldingRange.create = create;\n    /**\n     * Checks whether the given literal conforms to the [FoldingRange](#FoldingRange) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.uinteger(candidate.startLine) && Is.uinteger(candidate.startLine)\n            && (Is.undefined(candidate.startCharacter) || Is.uinteger(candidate.startCharacter))\n            && (Is.undefined(candidate.endCharacter) || Is.uinteger(candidate.endCharacter))\n            && (Is.undefined(candidate.kind) || Is.string(candidate.kind));\n    }\n    FoldingRange.is = is;\n})(FoldingRange || (FoldingRange = {}));\n/**\n * The DiagnosticRelatedInformation namespace provides helper functions to work with\n * [DiagnosticRelatedInformation](#DiagnosticRelatedInformation) literals.\n */\nexport var DiagnosticRelatedInformation;\n(function (DiagnosticRelatedInformation) {\n    /**\n     * Creates a new DiagnosticRelatedInformation literal.\n     */\n    function create(location, message) {\n        return {\n            location: location,\n            message: message\n        };\n    }\n    DiagnosticRelatedInformation.create = create;\n    /**\n     * Checks whether the given literal conforms to the [DiagnosticRelatedInformation](#DiagnosticRelatedInformation) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Location.is(candidate.location) && Is.string(candidate.message);\n    }\n    DiagnosticRelatedInformation.is = is;\n})(DiagnosticRelatedInformation || (DiagnosticRelatedInformation = {}));\n/**\n * The diagnostic's severity.\n */\nexport var DiagnosticSeverity;\n(function (DiagnosticSeverity) {\n    /**\n     * Reports an error.\n     */\n    DiagnosticSeverity.Error = 1;\n    /**\n     * Reports a warning.\n     */\n    DiagnosticSeverity.Warning = 2;\n    /**\n     * Reports an information.\n     */\n    DiagnosticSeverity.Information = 3;\n    /**\n     * Reports a hint.\n     */\n    DiagnosticSeverity.Hint = 4;\n})(DiagnosticSeverity || (DiagnosticSeverity = {}));\n/**\n * The diagnostic tags.\n *\n * @since 3.15.0\n */\nexport var DiagnosticTag;\n(function (DiagnosticTag) {\n    /**\n     * Unused or unnecessary code.\n     *\n     * Clients are allowed to render diagnostics with this tag faded out instead of having\n     * an error squiggle.\n     */\n    DiagnosticTag.Unnecessary = 1;\n    /**\n     * Deprecated or obsolete code.\n     *\n     * Clients are allowed to rendered diagnostics with this tag strike through.\n     */\n    DiagnosticTag.Deprecated = 2;\n})(DiagnosticTag || (DiagnosticTag = {}));\n/**\n * The CodeDescription namespace provides functions to deal with descriptions for diagnostic codes.\n *\n * @since 3.16.0\n */\nexport var CodeDescription;\n(function (CodeDescription) {\n    function is(value) {\n        var candidate = value;\n        return candidate !== undefined && candidate !== null && Is.string(candidate.href);\n    }\n    CodeDescription.is = is;\n})(CodeDescription || (CodeDescription = {}));\n/**\n * The Diagnostic namespace provides helper functions to work with\n * [Diagnostic](#Diagnostic) literals.\n */\nexport var Diagnostic;\n(function (Diagnostic) {\n    /**\n     * Creates a new Diagnostic literal.\n     */\n    function create(range, message, severity, code, source, relatedInformation) {\n        var result = { range: range, message: message };\n        if (Is.defined(severity)) {\n            result.severity = severity;\n        }\n        if (Is.defined(code)) {\n            result.code = code;\n        }\n        if (Is.defined(source)) {\n            result.source = source;\n        }\n        if (Is.defined(relatedInformation)) {\n            result.relatedInformation = relatedInformation;\n        }\n        return result;\n    }\n    Diagnostic.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Diagnostic](#Diagnostic) interface.\n     */\n    function is(value) {\n        var _a;\n        var candidate = value;\n        return Is.defined(candidate)\n            && Range.is(candidate.range)\n            && Is.string(candidate.message)\n            && (Is.number(candidate.severity) || Is.undefined(candidate.severity))\n            && (Is.integer(candidate.code) || Is.string(candidate.code) || Is.undefined(candidate.code))\n            && (Is.undefined(candidate.codeDescription) || (Is.string((_a = candidate.codeDescription) === null || _a === void 0 ? void 0 : _a.href)))\n            && (Is.string(candidate.source) || Is.undefined(candidate.source))\n            && (Is.undefined(candidate.relatedInformation) || Is.typedArray(candidate.relatedInformation, DiagnosticRelatedInformation.is));\n    }\n    Diagnostic.is = is;\n})(Diagnostic || (Diagnostic = {}));\n/**\n * The Command namespace provides helper functions to work with\n * [Command](#Command) literals.\n */\nexport var Command;\n(function (Command) {\n    /**\n     * Creates a new Command literal.\n     */\n    function create(title, command) {\n        var args = [];\n        for (var _i = 2; _i < arguments.length; _i++) {\n            args[_i - 2] = arguments[_i];\n        }\n        var result = { title: title, command: command };\n        if (Is.defined(args) && args.length > 0) {\n            result.arguments = args;\n        }\n        return result;\n    }\n    Command.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Command](#Command) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.title) && Is.string(candidate.command);\n    }\n    Command.is = is;\n})(Command || (Command = {}));\n/**\n * The TextEdit namespace provides helper function to create replace,\n * insert and delete edits more easily.\n */\nexport var TextEdit;\n(function (TextEdit) {\n    /**\n     * Creates a replace text edit.\n     * @param range The range of text to be replaced.\n     * @param newText The new text.\n     */\n    function replace(range, newText) {\n        return { range: range, newText: newText };\n    }\n    TextEdit.replace = replace;\n    /**\n     * Creates a insert text edit.\n     * @param position The position to insert the text at.\n     * @param newText The text to be inserted.\n     */\n    function insert(position, newText) {\n        return { range: { start: position, end: position }, newText: newText };\n    }\n    TextEdit.insert = insert;\n    /**\n     * Creates a delete text edit.\n     * @param range The range of text to be deleted.\n     */\n    function del(range) {\n        return { range: range, newText: '' };\n    }\n    TextEdit.del = del;\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate)\n            && Is.string(candidate.newText)\n            && Range.is(candidate.range);\n    }\n    TextEdit.is = is;\n})(TextEdit || (TextEdit = {}));\nexport var ChangeAnnotation;\n(function (ChangeAnnotation) {\n    function create(label, needsConfirmation, description) {\n        var result = { label: label };\n        if (needsConfirmation !== undefined) {\n            result.needsConfirmation = needsConfirmation;\n        }\n        if (description !== undefined) {\n            result.description = description;\n        }\n        return result;\n    }\n    ChangeAnnotation.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate !== undefined && Is.objectLiteral(candidate) && Is.string(candidate.label) &&\n            (Is.boolean(candidate.needsConfirmation) || candidate.needsConfirmation === undefined) &&\n            (Is.string(candidate.description) || candidate.description === undefined);\n    }\n    ChangeAnnotation.is = is;\n})(ChangeAnnotation || (ChangeAnnotation = {}));\nexport var ChangeAnnotationIdentifier;\n(function (ChangeAnnotationIdentifier) {\n    function is(value) {\n        var candidate = value;\n        return typeof candidate === 'string';\n    }\n    ChangeAnnotationIdentifier.is = is;\n})(ChangeAnnotationIdentifier || (ChangeAnnotationIdentifier = {}));\nexport var AnnotatedTextEdit;\n(function (AnnotatedTextEdit) {\n    /**\n     * Creates an annotated replace text edit.\n     *\n     * @param range The range of text to be replaced.\n     * @param newText The new text.\n     * @param annotation The annotation.\n     */\n    function replace(range, newText, annotation) {\n        return { range: range, newText: newText, annotationId: annotation };\n    }\n    AnnotatedTextEdit.replace = replace;\n    /**\n     * Creates an annotated insert text edit.\n     *\n     * @param position The position to insert the text at.\n     * @param newText The text to be inserted.\n     * @param annotation The annotation.\n     */\n    function insert(position, newText, annotation) {\n        return { range: { start: position, end: position }, newText: newText, annotationId: annotation };\n    }\n    AnnotatedTextEdit.insert = insert;\n    /**\n     * Creates an annotated delete text edit.\n     *\n     * @param range The range of text to be deleted.\n     * @param annotation The annotation.\n     */\n    function del(range, annotation) {\n        return { range: range, newText: '', annotationId: annotation };\n    }\n    AnnotatedTextEdit.del = del;\n    function is(value) {\n        var candidate = value;\n        return TextEdit.is(candidate) && (ChangeAnnotation.is(candidate.annotationId) || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    AnnotatedTextEdit.is = is;\n})(AnnotatedTextEdit || (AnnotatedTextEdit = {}));\n/**\n * The TextDocumentEdit namespace provides helper function to create\n * an edit that manipulates a text document.\n */\nexport var TextDocumentEdit;\n(function (TextDocumentEdit) {\n    /**\n     * Creates a new `TextDocumentEdit`\n     */\n    function create(textDocument, edits) {\n        return { textDocument: textDocument, edits: edits };\n    }\n    TextDocumentEdit.create = create;\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate)\n            && OptionalVersionedTextDocumentIdentifier.is(candidate.textDocument)\n            && Array.isArray(candidate.edits);\n    }\n    TextDocumentEdit.is = is;\n})(TextDocumentEdit || (TextDocumentEdit = {}));\nexport var CreateFile;\n(function (CreateFile) {\n    function create(uri, options, annotation) {\n        var result = {\n            kind: 'create',\n            uri: uri\n        };\n        if (options !== undefined && (options.overwrite !== undefined || options.ignoreIfExists !== undefined)) {\n            result.options = options;\n        }\n        if (annotation !== undefined) {\n            result.annotationId = annotation;\n        }\n        return result;\n    }\n    CreateFile.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate && candidate.kind === 'create' && Is.string(candidate.uri) && (candidate.options === undefined ||\n            ((candidate.options.overwrite === undefined || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === undefined || Is.boolean(candidate.options.ignoreIfExists)))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    CreateFile.is = is;\n})(CreateFile || (CreateFile = {}));\nexport var RenameFile;\n(function (RenameFile) {\n    function create(oldUri, newUri, options, annotation) {\n        var result = {\n            kind: 'rename',\n            oldUri: oldUri,\n            newUri: newUri\n        };\n        if (options !== undefined && (options.overwrite !== undefined || options.ignoreIfExists !== undefined)) {\n            result.options = options;\n        }\n        if (annotation !== undefined) {\n            result.annotationId = annotation;\n        }\n        return result;\n    }\n    RenameFile.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate && candidate.kind === 'rename' && Is.string(candidate.oldUri) && Is.string(candidate.newUri) && (candidate.options === undefined ||\n            ((candidate.options.overwrite === undefined || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === undefined || Is.boolean(candidate.options.ignoreIfExists)))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    RenameFile.is = is;\n})(RenameFile || (RenameFile = {}));\nexport var DeleteFile;\n(function (DeleteFile) {\n    function create(uri, options, annotation) {\n        var result = {\n            kind: 'delete',\n            uri: uri\n        };\n        if (options !== undefined && (options.recursive !== undefined || options.ignoreIfNotExists !== undefined)) {\n            result.options = options;\n        }\n        if (annotation !== undefined) {\n            result.annotationId = annotation;\n        }\n        return result;\n    }\n    DeleteFile.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate && candidate.kind === 'delete' && Is.string(candidate.uri) && (candidate.options === undefined ||\n            ((candidate.options.recursive === undefined || Is.boolean(candidate.options.recursive)) && (candidate.options.ignoreIfNotExists === undefined || Is.boolean(candidate.options.ignoreIfNotExists)))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    DeleteFile.is = is;\n})(DeleteFile || (DeleteFile = {}));\nexport var WorkspaceEdit;\n(function (WorkspaceEdit) {\n    function is(value) {\n        var candidate = value;\n        return candidate &&\n            (candidate.changes !== undefined || candidate.documentChanges !== undefined) &&\n            (candidate.documentChanges === undefined || candidate.documentChanges.every(function (change) {\n                if (Is.string(change.kind)) {\n                    return CreateFile.is(change) || RenameFile.is(change) || DeleteFile.is(change);\n                }\n                else {\n                    return TextDocumentEdit.is(change);\n                }\n            }));\n    }\n    WorkspaceEdit.is = is;\n})(WorkspaceEdit || (WorkspaceEdit = {}));\nvar TextEditChangeImpl = /** @class */ (function () {\n    function TextEditChangeImpl(edits, changeAnnotations) {\n        this.edits = edits;\n        this.changeAnnotations = changeAnnotations;\n    }\n    TextEditChangeImpl.prototype.insert = function (position, newText, annotation) {\n        var edit;\n        var id;\n        if (annotation === undefined) {\n            edit = TextEdit.insert(position, newText);\n        }\n        else if (ChangeAnnotationIdentifier.is(annotation)) {\n            id = annotation;\n            edit = AnnotatedTextEdit.insert(position, newText, annotation);\n        }\n        else {\n            this.assertChangeAnnotations(this.changeAnnotations);\n            id = this.changeAnnotations.manage(annotation);\n            edit = AnnotatedTextEdit.insert(position, newText, id);\n        }\n        this.edits.push(edit);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    TextEditChangeImpl.prototype.replace = function (range, newText, annotation) {\n        var edit;\n        var id;\n        if (annotation === undefined) {\n            edit = TextEdit.replace(range, newText);\n        }\n        else if (ChangeAnnotationIdentifier.is(annotation)) {\n            id = annotation;\n            edit = AnnotatedTextEdit.replace(range, newText, annotation);\n        }\n        else {\n            this.assertChangeAnnotations(this.changeAnnotations);\n            id = this.changeAnnotations.manage(annotation);\n            edit = AnnotatedTextEdit.replace(range, newText, id);\n        }\n        this.edits.push(edit);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    TextEditChangeImpl.prototype.delete = function (range, annotation) {\n        var edit;\n        var id;\n        if (annotation === undefined) {\n            edit = TextEdit.del(range);\n        }\n        else if (ChangeAnnotationIdentifier.is(annotation)) {\n            id = annotation;\n            edit = AnnotatedTextEdit.del(range, annotation);\n        }\n        else {\n            this.assertChangeAnnotations(this.changeAnnotations);\n            id = this.changeAnnotations.manage(annotation);\n            edit = AnnotatedTextEdit.del(range, id);\n        }\n        this.edits.push(edit);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    TextEditChangeImpl.prototype.add = function (edit) {\n        this.edits.push(edit);\n    };\n    TextEditChangeImpl.prototype.all = function () {\n        return this.edits;\n    };\n    TextEditChangeImpl.prototype.clear = function () {\n        this.edits.splice(0, this.edits.length);\n    };\n    TextEditChangeImpl.prototype.assertChangeAnnotations = function (value) {\n        if (value === undefined) {\n            throw new Error(\"Text edit change is not configured to manage change annotations.\");\n        }\n    };\n    return TextEditChangeImpl;\n}());\n/**\n * A helper class\n */\nvar ChangeAnnotations = /** @class */ (function () {\n    function ChangeAnnotations(annotations) {\n        this._annotations = annotations === undefined ? Object.create(null) : annotations;\n        this._counter = 0;\n        this._size = 0;\n    }\n    ChangeAnnotations.prototype.all = function () {\n        return this._annotations;\n    };\n    Object.defineProperty(ChangeAnnotations.prototype, \"size\", {\n        get: function () {\n            return this._size;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    ChangeAnnotations.prototype.manage = function (idOrAnnotation, annotation) {\n        var id;\n        if (ChangeAnnotationIdentifier.is(idOrAnnotation)) {\n            id = idOrAnnotation;\n        }\n        else {\n            id = this.nextId();\n            annotation = idOrAnnotation;\n        }\n        if (this._annotations[id] !== undefined) {\n            throw new Error(\"Id \" + id + \" is already in use.\");\n        }\n        if (annotation === undefined) {\n            throw new Error(\"No annotation provided for id \" + id);\n        }\n        this._annotations[id] = annotation;\n        this._size++;\n        return id;\n    };\n    ChangeAnnotations.prototype.nextId = function () {\n        this._counter++;\n        return this._counter.toString();\n    };\n    return ChangeAnnotations;\n}());\n/**\n * A workspace change helps constructing changes to a workspace.\n */\nvar WorkspaceChange = /** @class */ (function () {\n    function WorkspaceChange(workspaceEdit) {\n        var _this = this;\n        this._textEditChanges = Object.create(null);\n        if (workspaceEdit !== undefined) {\n            this._workspaceEdit = workspaceEdit;\n            if (workspaceEdit.documentChanges) {\n                this._changeAnnotations = new ChangeAnnotations(workspaceEdit.changeAnnotations);\n                workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n                workspaceEdit.documentChanges.forEach(function (change) {\n                    if (TextDocumentEdit.is(change)) {\n                        var textEditChange = new TextEditChangeImpl(change.edits, _this._changeAnnotations);\n                        _this._textEditChanges[change.textDocument.uri] = textEditChange;\n                    }\n                });\n            }\n            else if (workspaceEdit.changes) {\n                Object.keys(workspaceEdit.changes).forEach(function (key) {\n                    var textEditChange = new TextEditChangeImpl(workspaceEdit.changes[key]);\n                    _this._textEditChanges[key] = textEditChange;\n                });\n            }\n        }\n        else {\n            this._workspaceEdit = {};\n        }\n    }\n    Object.defineProperty(WorkspaceChange.prototype, \"edit\", {\n        /**\n         * Returns the underlying [WorkspaceEdit](#WorkspaceEdit) literal\n         * use to be returned from a workspace edit operation like rename.\n         */\n        get: function () {\n            this.initDocumentChanges();\n            if (this._changeAnnotations !== undefined) {\n                if (this._changeAnnotations.size === 0) {\n                    this._workspaceEdit.changeAnnotations = undefined;\n                }\n                else {\n                    this._workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n                }\n            }\n            return this._workspaceEdit;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    WorkspaceChange.prototype.getTextEditChange = function (key) {\n        if (OptionalVersionedTextDocumentIdentifier.is(key)) {\n            this.initDocumentChanges();\n            if (this._workspaceEdit.documentChanges === undefined) {\n                throw new Error('Workspace edit is not configured for document changes.');\n            }\n            var textDocument = { uri: key.uri, version: key.version };\n            var result = this._textEditChanges[textDocument.uri];\n            if (!result) {\n                var edits = [];\n                var textDocumentEdit = {\n                    textDocument: textDocument,\n                    edits: edits\n                };\n                this._workspaceEdit.documentChanges.push(textDocumentEdit);\n                result = new TextEditChangeImpl(edits, this._changeAnnotations);\n                this._textEditChanges[textDocument.uri] = result;\n            }\n            return result;\n        }\n        else {\n            this.initChanges();\n            if (this._workspaceEdit.changes === undefined) {\n                throw new Error('Workspace edit is not configured for normal text edit changes.');\n            }\n            var result = this._textEditChanges[key];\n            if (!result) {\n                var edits = [];\n                this._workspaceEdit.changes[key] = edits;\n                result = new TextEditChangeImpl(edits);\n                this._textEditChanges[key] = result;\n            }\n            return result;\n        }\n    };\n    WorkspaceChange.prototype.initDocumentChanges = function () {\n        if (this._workspaceEdit.documentChanges === undefined && this._workspaceEdit.changes === undefined) {\n            this._changeAnnotations = new ChangeAnnotations();\n            this._workspaceEdit.documentChanges = [];\n            this._workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n        }\n    };\n    WorkspaceChange.prototype.initChanges = function () {\n        if (this._workspaceEdit.documentChanges === undefined && this._workspaceEdit.changes === undefined) {\n            this._workspaceEdit.changes = Object.create(null);\n        }\n    };\n    WorkspaceChange.prototype.createFile = function (uri, optionsOrAnnotation, options) {\n        this.initDocumentChanges();\n        if (this._workspaceEdit.documentChanges === undefined) {\n            throw new Error('Workspace edit is not configured for document changes.');\n        }\n        var annotation;\n        if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n            annotation = optionsOrAnnotation;\n        }\n        else {\n            options = optionsOrAnnotation;\n        }\n        var operation;\n        var id;\n        if (annotation === undefined) {\n            operation = CreateFile.create(uri, options);\n        }\n        else {\n            id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n            operation = CreateFile.create(uri, options, id);\n        }\n        this._workspaceEdit.documentChanges.push(operation);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    WorkspaceChange.prototype.renameFile = function (oldUri, newUri, optionsOrAnnotation, options) {\n        this.initDocumentChanges();\n        if (this._workspaceEdit.documentChanges === undefined) {\n            throw new Error('Workspace edit is not configured for document changes.');\n        }\n        var annotation;\n        if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n            annotation = optionsOrAnnotation;\n        }\n        else {\n            options = optionsOrAnnotation;\n        }\n        var operation;\n        var id;\n        if (annotation === undefined) {\n            operation = RenameFile.create(oldUri, newUri, options);\n        }\n        else {\n            id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n            operation = RenameFile.create(oldUri, newUri, options, id);\n        }\n        this._workspaceEdit.documentChanges.push(operation);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    WorkspaceChange.prototype.deleteFile = function (uri, optionsOrAnnotation, options) {\n        this.initDocumentChanges();\n        if (this._workspaceEdit.documentChanges === undefined) {\n            throw new Error('Workspace edit is not configured for document changes.');\n        }\n        var annotation;\n        if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n            annotation = optionsOrAnnotation;\n        }\n        else {\n            options = optionsOrAnnotation;\n        }\n        var operation;\n        var id;\n        if (annotation === undefined) {\n            operation = DeleteFile.create(uri, options);\n        }\n        else {\n            id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n            operation = DeleteFile.create(uri, options, id);\n        }\n        this._workspaceEdit.documentChanges.push(operation);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    return WorkspaceChange;\n}());\nexport { WorkspaceChange };\n/**\n * The TextDocumentIdentifier namespace provides helper functions to work with\n * [TextDocumentIdentifier](#TextDocumentIdentifier) literals.\n */\nexport var TextDocumentIdentifier;\n(function (TextDocumentIdentifier) {\n    /**\n     * Creates a new TextDocumentIdentifier literal.\n     * @param uri The document's uri.\n     */\n    function create(uri) {\n        return { uri: uri };\n    }\n    TextDocumentIdentifier.create = create;\n    /**\n     * Checks whether the given literal conforms to the [TextDocumentIdentifier](#TextDocumentIdentifier) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri);\n    }\n    TextDocumentIdentifier.is = is;\n})(TextDocumentIdentifier || (TextDocumentIdentifier = {}));\n/**\n * The VersionedTextDocumentIdentifier namespace provides helper functions to work with\n * [VersionedTextDocumentIdentifier](#VersionedTextDocumentIdentifier) literals.\n */\nexport var VersionedTextDocumentIdentifier;\n(function (VersionedTextDocumentIdentifier) {\n    /**\n     * Creates a new VersionedTextDocumentIdentifier literal.\n     * @param uri The document's uri.\n     * @param uri The document's text.\n     */\n    function create(uri, version) {\n        return { uri: uri, version: version };\n    }\n    VersionedTextDocumentIdentifier.create = create;\n    /**\n     * Checks whether the given literal conforms to the [VersionedTextDocumentIdentifier](#VersionedTextDocumentIdentifier) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && Is.integer(candidate.version);\n    }\n    VersionedTextDocumentIdentifier.is = is;\n})(VersionedTextDocumentIdentifier || (VersionedTextDocumentIdentifier = {}));\n/**\n * The OptionalVersionedTextDocumentIdentifier namespace provides helper functions to work with\n * [OptionalVersionedTextDocumentIdentifier](#OptionalVersionedTextDocumentIdentifier) literals.\n */\nexport var OptionalVersionedTextDocumentIdentifier;\n(function (OptionalVersionedTextDocumentIdentifier) {\n    /**\n     * Creates a new OptionalVersionedTextDocumentIdentifier literal.\n     * @param uri The document's uri.\n     * @param uri The document's text.\n     */\n    function create(uri, version) {\n        return { uri: uri, version: version };\n    }\n    OptionalVersionedTextDocumentIdentifier.create = create;\n    /**\n     * Checks whether the given literal conforms to the [OptionalVersionedTextDocumentIdentifier](#OptionalVersionedTextDocumentIdentifier) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && (candidate.version === null || Is.integer(candidate.version));\n    }\n    OptionalVersionedTextDocumentIdentifier.is = is;\n})(OptionalVersionedTextDocumentIdentifier || (OptionalVersionedTextDocumentIdentifier = {}));\n/**\n * The TextDocumentItem namespace provides helper functions to work with\n * [TextDocumentItem](#TextDocumentItem) literals.\n */\nexport var TextDocumentItem;\n(function (TextDocumentItem) {\n    /**\n     * Creates a new TextDocumentItem literal.\n     * @param uri The document's uri.\n     * @param languageId The document's language identifier.\n     * @param version The document's version number.\n     * @param text The document's text.\n     */\n    function create(uri, languageId, version, text) {\n        return { uri: uri, languageId: languageId, version: version, text: text };\n    }\n    TextDocumentItem.create = create;\n    /**\n     * Checks whether the given literal conforms to the [TextDocumentItem](#TextDocumentItem) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && Is.string(candidate.languageId) && Is.integer(candidate.version) && Is.string(candidate.text);\n    }\n    TextDocumentItem.is = is;\n})(TextDocumentItem || (TextDocumentItem = {}));\n/**\n * Describes the content type that a client supports in various\n * result literals like `Hover`, `ParameterInfo` or `CompletionItem`.\n *\n * Please note that `MarkupKinds` must not start with a `$`. This kinds\n * are reserved for internal usage.\n */\nexport var MarkupKind;\n(function (MarkupKind) {\n    /**\n     * Plain text is supported as a content format\n     */\n    MarkupKind.PlainText = 'plaintext';\n    /**\n     * Markdown is supported as a content format\n     */\n    MarkupKind.Markdown = 'markdown';\n})(MarkupKind || (MarkupKind = {}));\n(function (MarkupKind) {\n    /**\n     * Checks whether the given value is a value of the [MarkupKind](#MarkupKind) type.\n     */\n    function is(value) {\n        var candidate = value;\n        return candidate === MarkupKind.PlainText || candidate === MarkupKind.Markdown;\n    }\n    MarkupKind.is = is;\n})(MarkupKind || (MarkupKind = {}));\nexport var MarkupContent;\n(function (MarkupContent) {\n    /**\n     * Checks whether the given value conforms to the [MarkupContent](#MarkupContent) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(value) && MarkupKind.is(candidate.kind) && Is.string(candidate.value);\n    }\n    MarkupContent.is = is;\n})(MarkupContent || (MarkupContent = {}));\n/**\n * The kind of a completion entry.\n */\nexport var CompletionItemKind;\n(function (CompletionItemKind) {\n    CompletionItemKind.Text = 1;\n    CompletionItemKind.Method = 2;\n    CompletionItemKind.Function = 3;\n    CompletionItemKind.Constructor = 4;\n    CompletionItemKind.Field = 5;\n    CompletionItemKind.Variable = 6;\n    CompletionItemKind.Class = 7;\n    CompletionItemKind.Interface = 8;\n    CompletionItemKind.Module = 9;\n    CompletionItemKind.Property = 10;\n    CompletionItemKind.Unit = 11;\n    CompletionItemKind.Value = 12;\n    CompletionItemKind.Enum = 13;\n    CompletionItemKind.Keyword = 14;\n    CompletionItemKind.Snippet = 15;\n    CompletionItemKind.Color = 16;\n    CompletionItemKind.File = 17;\n    CompletionItemKind.Reference = 18;\n    CompletionItemKind.Folder = 19;\n    CompletionItemKind.EnumMember = 20;\n    CompletionItemKind.Constant = 21;\n    CompletionItemKind.Struct = 22;\n    CompletionItemKind.Event = 23;\n    CompletionItemKind.Operator = 24;\n    CompletionItemKind.TypeParameter = 25;\n})(CompletionItemKind || (CompletionItemKind = {}));\n/**\n * Defines whether the insert text in a completion item should be interpreted as\n * plain text or a snippet.\n */\nexport var InsertTextFormat;\n(function (InsertTextFormat) {\n    /**\n     * The primary text to be inserted is treated as a plain string.\n     */\n    InsertTextFormat.PlainText = 1;\n    /**\n     * The primary text to be inserted is treated as a snippet.\n     *\n     * A snippet can define tab stops and placeholders with `$1`, `$2`\n     * and `${3:foo}`. `$0` defines the final tab stop, it defaults to\n     * the end of the snippet. Placeholders with equal identifiers are linked,\n     * that is typing in one will update others too.\n     *\n     * See also: https://microsoft.github.io/language-server-protocol/specifications/specification-current/#snippet_syntax\n     */\n    InsertTextFormat.Snippet = 2;\n})(InsertTextFormat || (InsertTextFormat = {}));\n/**\n * Completion item tags are extra annotations that tweak the rendering of a completion\n * item.\n *\n * @since 3.15.0\n */\nexport var CompletionItemTag;\n(function (CompletionItemTag) {\n    /**\n     * Render a completion as obsolete, usually using a strike-out.\n     */\n    CompletionItemTag.Deprecated = 1;\n})(CompletionItemTag || (CompletionItemTag = {}));\n/**\n * The InsertReplaceEdit namespace provides functions to deal with insert / replace edits.\n *\n * @since 3.16.0\n */\nexport var InsertReplaceEdit;\n(function (InsertReplaceEdit) {\n    /**\n     * Creates a new insert / replace edit\n     */\n    function create(newText, insert, replace) {\n        return { newText: newText, insert: insert, replace: replace };\n    }\n    InsertReplaceEdit.create = create;\n    /**\n     * Checks whether the given literal conforms to the [InsertReplaceEdit](#InsertReplaceEdit) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return candidate && Is.string(candidate.newText) && Range.is(candidate.insert) && Range.is(candidate.replace);\n    }\n    InsertReplaceEdit.is = is;\n})(InsertReplaceEdit || (InsertReplaceEdit = {}));\n/**\n * How whitespace and indentation is handled during completion\n * item insertion.\n *\n * @since 3.16.0\n */\nexport var InsertTextMode;\n(function (InsertTextMode) {\n    /**\n     * The insertion or replace strings is taken as it is. If the\n     * value is multi line the lines below the cursor will be\n     * inserted using the indentation defined in the string value.\n     * The client will not apply any kind of adjustments to the\n     * string.\n     */\n    InsertTextMode.asIs = 1;\n    /**\n     * The editor adjusts leading whitespace of new lines so that\n     * they match the indentation up to the cursor of the line for\n     * which the item is accepted.\n     *\n     * Consider a line like this: <2tabs><cursor><3tabs>foo. Accepting a\n     * multi line completion item is indented using 2 tabs and all\n     * following lines inserted will be indented using 2 tabs as well.\n     */\n    InsertTextMode.adjustIndentation = 2;\n})(InsertTextMode || (InsertTextMode = {}));\n/**\n * The CompletionItem namespace provides functions to deal with\n * completion items.\n */\nexport var CompletionItem;\n(function (CompletionItem) {\n    /**\n     * Create a completion item and seed it with a label.\n     * @param label The completion item's label\n     */\n    function create(label) {\n        return { label: label };\n    }\n    CompletionItem.create = create;\n})(CompletionItem || (CompletionItem = {}));\n/**\n * The CompletionList namespace provides functions to deal with\n * completion lists.\n */\nexport var CompletionList;\n(function (CompletionList) {\n    /**\n     * Creates a new completion list.\n     *\n     * @param items The completion items.\n     * @param isIncomplete The list is not complete.\n     */\n    function create(items, isIncomplete) {\n        return { items: items ? items : [], isIncomplete: !!isIncomplete };\n    }\n    CompletionList.create = create;\n})(CompletionList || (CompletionList = {}));\nexport var MarkedString;\n(function (MarkedString) {\n    /**\n     * Creates a marked string from plain text.\n     *\n     * @param plainText The plain text.\n     */\n    function fromPlainText(plainText) {\n        return plainText.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, '\\\\$&'); // escape markdown syntax tokens: http://daringfireball.net/projects/markdown/syntax#backslash\n    }\n    MarkedString.fromPlainText = fromPlainText;\n    /**\n     * Checks whether the given value conforms to the [MarkedString](#MarkedString) type.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.string(candidate) || (Is.objectLiteral(candidate) && Is.string(candidate.language) && Is.string(candidate.value));\n    }\n    MarkedString.is = is;\n})(MarkedString || (MarkedString = {}));\nexport var Hover;\n(function (Hover) {\n    /**\n     * Checks whether the given value conforms to the [Hover](#Hover) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return !!candidate && Is.objectLiteral(candidate) && (MarkupContent.is(candidate.contents) ||\n            MarkedString.is(candidate.contents) ||\n            Is.typedArray(candidate.contents, MarkedString.is)) && (value.range === undefined || Range.is(value.range));\n    }\n    Hover.is = is;\n})(Hover || (Hover = {}));\n/**\n * The ParameterInformation namespace provides helper functions to work with\n * [ParameterInformation](#ParameterInformation) literals.\n */\nexport var ParameterInformation;\n(function (ParameterInformation) {\n    /**\n     * Creates a new parameter information literal.\n     *\n     * @param label A label string.\n     * @param documentation A doc string.\n     */\n    function create(label, documentation) {\n        return documentation ? { label: label, documentation: documentation } : { label: label };\n    }\n    ParameterInformation.create = create;\n})(ParameterInformation || (ParameterInformation = {}));\n/**\n * The SignatureInformation namespace provides helper functions to work with\n * [SignatureInformation](#SignatureInformation) literals.\n */\nexport var SignatureInformation;\n(function (SignatureInformation) {\n    function create(label, documentation) {\n        var parameters = [];\n        for (var _i = 2; _i < arguments.length; _i++) {\n            parameters[_i - 2] = arguments[_i];\n        }\n        var result = { label: label };\n        if (Is.defined(documentation)) {\n            result.documentation = documentation;\n        }\n        if (Is.defined(parameters)) {\n            result.parameters = parameters;\n        }\n        else {\n            result.parameters = [];\n        }\n        return result;\n    }\n    SignatureInformation.create = create;\n})(SignatureInformation || (SignatureInformation = {}));\n/**\n * A document highlight kind.\n */\nexport var DocumentHighlightKind;\n(function (DocumentHighlightKind) {\n    /**\n     * A textual occurrence.\n     */\n    DocumentHighlightKind.Text = 1;\n    /**\n     * Read-access of a symbol, like reading a variable.\n     */\n    DocumentHighlightKind.Read = 2;\n    /**\n     * Write-access of a symbol, like writing to a variable.\n     */\n    DocumentHighlightKind.Write = 3;\n})(DocumentHighlightKind || (DocumentHighlightKind = {}));\n/**\n * DocumentHighlight namespace to provide helper functions to work with\n * [DocumentHighlight](#DocumentHighlight) literals.\n */\nexport var DocumentHighlight;\n(function (DocumentHighlight) {\n    /**\n     * Create a DocumentHighlight object.\n     * @param range The range the highlight applies to.\n     */\n    function create(range, kind) {\n        var result = { range: range };\n        if (Is.number(kind)) {\n            result.kind = kind;\n        }\n        return result;\n    }\n    DocumentHighlight.create = create;\n})(DocumentHighlight || (DocumentHighlight = {}));\n/**\n * A symbol kind.\n */\nexport var SymbolKind;\n(function (SymbolKind) {\n    SymbolKind.File = 1;\n    SymbolKind.Module = 2;\n    SymbolKind.Namespace = 3;\n    SymbolKind.Package = 4;\n    SymbolKind.Class = 5;\n    SymbolKind.Method = 6;\n    SymbolKind.Property = 7;\n    SymbolKind.Field = 8;\n    SymbolKind.Constructor = 9;\n    SymbolKind.Enum = 10;\n    SymbolKind.Interface = 11;\n    SymbolKind.Function = 12;\n    SymbolKind.Variable = 13;\n    SymbolKind.Constant = 14;\n    SymbolKind.String = 15;\n    SymbolKind.Number = 16;\n    SymbolKind.Boolean = 17;\n    SymbolKind.Array = 18;\n    SymbolKind.Object = 19;\n    SymbolKind.Key = 20;\n    SymbolKind.Null = 21;\n    SymbolKind.EnumMember = 22;\n    SymbolKind.Struct = 23;\n    SymbolKind.Event = 24;\n    SymbolKind.Operator = 25;\n    SymbolKind.TypeParameter = 26;\n})(SymbolKind || (SymbolKind = {}));\n/**\n * Symbol tags are extra annotations that tweak the rendering of a symbol.\n * @since 3.16\n */\nexport var SymbolTag;\n(function (SymbolTag) {\n    /**\n     * Render a symbol as obsolete, usually using a strike-out.\n     */\n    SymbolTag.Deprecated = 1;\n})(SymbolTag || (SymbolTag = {}));\nexport var SymbolInformation;\n(function (SymbolInformation) {\n    /**\n     * Creates a new symbol information literal.\n     *\n     * @param name The name of the symbol.\n     * @param kind The kind of the symbol.\n     * @param range The range of the location of the symbol.\n     * @param uri The resource of the location of symbol, defaults to the current document.\n     * @param containerName The name of the symbol containing the symbol.\n     */\n    function create(name, kind, range, uri, containerName) {\n        var result = {\n            name: name,\n            kind: kind,\n            location: { uri: uri, range: range }\n        };\n        if (containerName) {\n            result.containerName = containerName;\n        }\n        return result;\n    }\n    SymbolInformation.create = create;\n})(SymbolInformation || (SymbolInformation = {}));\nexport var DocumentSymbol;\n(function (DocumentSymbol) {\n    /**\n     * Creates a new symbol information literal.\n     *\n     * @param name The name of the symbol.\n     * @param detail The detail of the symbol.\n     * @param kind The kind of the symbol.\n     * @param range The range of the symbol.\n     * @param selectionRange The selectionRange of the symbol.\n     * @param children Children of the symbol.\n     */\n    function create(name, detail, kind, range, selectionRange, children) {\n        var result = {\n            name: name,\n            detail: detail,\n            kind: kind,\n            range: range,\n            selectionRange: selectionRange\n        };\n        if (children !== undefined) {\n            result.children = children;\n        }\n        return result;\n    }\n    DocumentSymbol.create = create;\n    /**\n     * Checks whether the given literal conforms to the [DocumentSymbol](#DocumentSymbol) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return candidate &&\n            Is.string(candidate.name) && Is.number(candidate.kind) &&\n            Range.is(candidate.range) && Range.is(candidate.selectionRange) &&\n            (candidate.detail === undefined || Is.string(candidate.detail)) &&\n            (candidate.deprecated === undefined || Is.boolean(candidate.deprecated)) &&\n            (candidate.children === undefined || Array.isArray(candidate.children)) &&\n            (candidate.tags === undefined || Array.isArray(candidate.tags));\n    }\n    DocumentSymbol.is = is;\n})(DocumentSymbol || (DocumentSymbol = {}));\n/**\n * A set of predefined code action kinds\n */\nexport var CodeActionKind;\n(function (CodeActionKind) {\n    /**\n     * Empty kind.\n     */\n    CodeActionKind.Empty = '';\n    /**\n     * Base kind for quickfix actions: 'quickfix'\n     */\n    CodeActionKind.QuickFix = 'quickfix';\n    /**\n     * Base kind for refactoring actions: 'refactor'\n     */\n    CodeActionKind.Refactor = 'refactor';\n    /**\n     * Base kind for refactoring extraction actions: 'refactor.extract'\n     *\n     * Example extract actions:\n     *\n     * - Extract method\n     * - Extract function\n     * - Extract variable\n     * - Extract interface from class\n     * - ...\n     */\n    CodeActionKind.RefactorExtract = 'refactor.extract';\n    /**\n     * Base kind for refactoring inline actions: 'refactor.inline'\n     *\n     * Example inline actions:\n     *\n     * - Inline function\n     * - Inline variable\n     * - Inline constant\n     * - ...\n     */\n    CodeActionKind.RefactorInline = 'refactor.inline';\n    /**\n     * Base kind for refactoring rewrite actions: 'refactor.rewrite'\n     *\n     * Example rewrite actions:\n     *\n     * - Convert JavaScript function to class\n     * - Add or remove parameter\n     * - Encapsulate field\n     * - Make method static\n     * - Move method to base class\n     * - ...\n     */\n    CodeActionKind.RefactorRewrite = 'refactor.rewrite';\n    /**\n     * Base kind for source actions: `source`\n     *\n     * Source code actions apply to the entire file.\n     */\n    CodeActionKind.Source = 'source';\n    /**\n     * Base kind for an organize imports source action: `source.organizeImports`\n     */\n    CodeActionKind.SourceOrganizeImports = 'source.organizeImports';\n    /**\n     * Base kind for auto-fix source actions: `source.fixAll`.\n     *\n     * Fix all actions automatically fix errors that have a clear fix that do not require user input.\n     * They should not suppress errors or perform unsafe fixes such as generating new types or classes.\n     *\n     * @since 3.15.0\n     */\n    CodeActionKind.SourceFixAll = 'source.fixAll';\n})(CodeActionKind || (CodeActionKind = {}));\n/**\n * The CodeActionContext namespace provides helper functions to work with\n * [CodeActionContext](#CodeActionContext) literals.\n */\nexport var CodeActionContext;\n(function (CodeActionContext) {\n    /**\n     * Creates a new CodeActionContext literal.\n     */\n    function create(diagnostics, only) {\n        var result = { diagnostics: diagnostics };\n        if (only !== undefined && only !== null) {\n            result.only = only;\n        }\n        return result;\n    }\n    CodeActionContext.create = create;\n    /**\n     * Checks whether the given literal conforms to the [CodeActionContext](#CodeActionContext) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.typedArray(candidate.diagnostics, Diagnostic.is) && (candidate.only === undefined || Is.typedArray(candidate.only, Is.string));\n    }\n    CodeActionContext.is = is;\n})(CodeActionContext || (CodeActionContext = {}));\nexport var CodeAction;\n(function (CodeAction) {\n    function create(title, kindOrCommandOrEdit, kind) {\n        var result = { title: title };\n        var checkKind = true;\n        if (typeof kindOrCommandOrEdit === 'string') {\n            checkKind = false;\n            result.kind = kindOrCommandOrEdit;\n        }\n        else if (Command.is(kindOrCommandOrEdit)) {\n            result.command = kindOrCommandOrEdit;\n        }\n        else {\n            result.edit = kindOrCommandOrEdit;\n        }\n        if (checkKind && kind !== undefined) {\n            result.kind = kind;\n        }\n        return result;\n    }\n    CodeAction.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate && Is.string(candidate.title) &&\n            (candidate.diagnostics === undefined || Is.typedArray(candidate.diagnostics, Diagnostic.is)) &&\n            (candidate.kind === undefined || Is.string(candidate.kind)) &&\n            (candidate.edit !== undefined || candidate.command !== undefined) &&\n            (candidate.command === undefined || Command.is(candidate.command)) &&\n            (candidate.isPreferred === undefined || Is.boolean(candidate.isPreferred)) &&\n            (candidate.edit === undefined || WorkspaceEdit.is(candidate.edit));\n    }\n    CodeAction.is = is;\n})(CodeAction || (CodeAction = {}));\n/**\n * The CodeLens namespace provides helper functions to work with\n * [CodeLens](#CodeLens) literals.\n */\nexport var CodeLens;\n(function (CodeLens) {\n    /**\n     * Creates a new CodeLens literal.\n     */\n    function create(range, data) {\n        var result = { range: range };\n        if (Is.defined(data)) {\n            result.data = data;\n        }\n        return result;\n    }\n    CodeLens.create = create;\n    /**\n     * Checks whether the given literal conforms to the [CodeLens](#CodeLens) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.command) || Command.is(candidate.command));\n    }\n    CodeLens.is = is;\n})(CodeLens || (CodeLens = {}));\n/**\n * The FormattingOptions namespace provides helper functions to work with\n * [FormattingOptions](#FormattingOptions) literals.\n */\nexport var FormattingOptions;\n(function (FormattingOptions) {\n    /**\n     * Creates a new FormattingOptions literal.\n     */\n    function create(tabSize, insertSpaces) {\n        return { tabSize: tabSize, insertSpaces: insertSpaces };\n    }\n    FormattingOptions.create = create;\n    /**\n     * Checks whether the given literal conforms to the [FormattingOptions](#FormattingOptions) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.uinteger(candidate.tabSize) && Is.boolean(candidate.insertSpaces);\n    }\n    FormattingOptions.is = is;\n})(FormattingOptions || (FormattingOptions = {}));\n/**\n * The DocumentLink namespace provides helper functions to work with\n * [DocumentLink](#DocumentLink) literals.\n */\nexport var DocumentLink;\n(function (DocumentLink) {\n    /**\n     * Creates a new DocumentLink literal.\n     */\n    function create(range, target, data) {\n        return { range: range, target: target, data: data };\n    }\n    DocumentLink.create = create;\n    /**\n     * Checks whether the given literal conforms to the [DocumentLink](#DocumentLink) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.target) || Is.string(candidate.target));\n    }\n    DocumentLink.is = is;\n})(DocumentLink || (DocumentLink = {}));\n/**\n * The SelectionRange namespace provides helper function to work with\n * SelectionRange literals.\n */\nexport var SelectionRange;\n(function (SelectionRange) {\n    /**\n     * Creates a new SelectionRange\n     * @param range the range.\n     * @param parent an optional parent.\n     */\n    function create(range, parent) {\n        return { range: range, parent: parent };\n    }\n    SelectionRange.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate !== undefined && Range.is(candidate.range) && (candidate.parent === undefined || SelectionRange.is(candidate.parent));\n    }\n    SelectionRange.is = is;\n})(SelectionRange || (SelectionRange = {}));\nexport var EOL = ['\\n', '\\r\\n', '\\r'];\n/**\n * @deprecated Use the text document from the new vscode-languageserver-textdocument package.\n */\nexport var TextDocument;\n(function (TextDocument) {\n    /**\n     * Creates a new ITextDocument literal from the given uri and content.\n     * @param uri The document's uri.\n     * @param languageId  The document's language Id.\n     * @param content The document's content.\n     */\n    function create(uri, languageId, version, content) {\n        return new FullTextDocument(uri, languageId, version, content);\n    }\n    TextDocument.create = create;\n    /**\n     * Checks whether the given literal conforms to the [ITextDocument](#ITextDocument) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && (Is.undefined(candidate.languageId) || Is.string(candidate.languageId)) && Is.uinteger(candidate.lineCount)\n            && Is.func(candidate.getText) && Is.func(candidate.positionAt) && Is.func(candidate.offsetAt) ? true : false;\n    }\n    TextDocument.is = is;\n    function applyEdits(document, edits) {\n        var text = document.getText();\n        var sortedEdits = mergeSort(edits, function (a, b) {\n            var diff = a.range.start.line - b.range.start.line;\n            if (diff === 0) {\n                return a.range.start.character - b.range.start.character;\n            }\n            return diff;\n        });\n        var lastModifiedOffset = text.length;\n        for (var i = sortedEdits.length - 1; i >= 0; i--) {\n            var e = sortedEdits[i];\n            var startOffset = document.offsetAt(e.range.start);\n            var endOffset = document.offsetAt(e.range.end);\n            if (endOffset <= lastModifiedOffset) {\n                text = text.substring(0, startOffset) + e.newText + text.substring(endOffset, text.length);\n            }\n            else {\n                throw new Error('Overlapping edit');\n            }\n            lastModifiedOffset = startOffset;\n        }\n        return text;\n    }\n    TextDocument.applyEdits = applyEdits;\n    function mergeSort(data, compare) {\n        if (data.length <= 1) {\n            // sorted\n            return data;\n        }\n        var p = (data.length / 2) | 0;\n        var left = data.slice(0, p);\n        var right = data.slice(p);\n        mergeSort(left, compare);\n        mergeSort(right, compare);\n        var leftIdx = 0;\n        var rightIdx = 0;\n        var i = 0;\n        while (leftIdx < left.length && rightIdx < right.length) {\n            var ret = compare(left[leftIdx], right[rightIdx]);\n            if (ret <= 0) {\n                // smaller_equal -> take left to preserve order\n                data[i++] = left[leftIdx++];\n            }\n            else {\n                // greater -> take right\n                data[i++] = right[rightIdx++];\n            }\n        }\n        while (leftIdx < left.length) {\n            data[i++] = left[leftIdx++];\n        }\n        while (rightIdx < right.length) {\n            data[i++] = right[rightIdx++];\n        }\n        return data;\n    }\n})(TextDocument || (TextDocument = {}));\n/**\n * @deprecated Use the text document from the new vscode-languageserver-textdocument package.\n */\nvar FullTextDocument = /** @class */ (function () {\n    function FullTextDocument(uri, languageId, version, content) {\n        this._uri = uri;\n        this._languageId = languageId;\n        this._version = version;\n        this._content = content;\n        this._lineOffsets = undefined;\n    }\n    Object.defineProperty(FullTextDocument.prototype, \"uri\", {\n        get: function () {\n            return this._uri;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(FullTextDocument.prototype, \"languageId\", {\n        get: function () {\n            return this._languageId;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(FullTextDocument.prototype, \"version\", {\n        get: function () {\n            return this._version;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    FullTextDocument.prototype.getText = function (range) {\n        if (range) {\n            var start = this.offsetAt(range.start);\n            var end = this.offsetAt(range.end);\n            return this._content.substring(start, end);\n        }\n        return this._content;\n    };\n    FullTextDocument.prototype.update = function (event, version) {\n        this._content = event.text;\n        this._version = version;\n        this._lineOffsets = undefined;\n    };\n    FullTextDocument.prototype.getLineOffsets = function () {\n        if (this._lineOffsets === undefined) {\n            var lineOffsets = [];\n            var text = this._content;\n            var isLineStart = true;\n            for (var i = 0; i < text.length; i++) {\n                if (isLineStart) {\n                    lineOffsets.push(i);\n                    isLineStart = false;\n                }\n                var ch = text.charAt(i);\n                isLineStart = (ch === '\\r' || ch === '\\n');\n                if (ch === '\\r' && i + 1 < text.length && text.charAt(i + 1) === '\\n') {\n                    i++;\n                }\n            }\n            if (isLineStart && text.length > 0) {\n                lineOffsets.push(text.length);\n            }\n            this._lineOffsets = lineOffsets;\n        }\n        return this._lineOffsets;\n    };\n    FullTextDocument.prototype.positionAt = function (offset) {\n        offset = Math.max(Math.min(offset, this._content.length), 0);\n        var lineOffsets = this.getLineOffsets();\n        var low = 0, high = lineOffsets.length;\n        if (high === 0) {\n            return Position.create(0, offset);\n        }\n        while (low < high) {\n            var mid = Math.floor((low + high) / 2);\n            if (lineOffsets[mid] > offset) {\n                high = mid;\n            }\n            else {\n                low = mid + 1;\n            }\n        }\n        // low is the least x for which the line offset is larger than the current offset\n        // or array.length if no line offset is larger than the current offset\n        var line = low - 1;\n        return Position.create(line, offset - lineOffsets[line]);\n    };\n    FullTextDocument.prototype.offsetAt = function (position) {\n        var lineOffsets = this.getLineOffsets();\n        if (position.line >= lineOffsets.length) {\n            return this._content.length;\n        }\n        else if (position.line < 0) {\n            return 0;\n        }\n        var lineOffset = lineOffsets[position.line];\n        var nextLineOffset = (position.line + 1 < lineOffsets.length) ? lineOffsets[position.line + 1] : this._content.length;\n        return Math.max(Math.min(lineOffset + position.character, nextLineOffset), lineOffset);\n    };\n    Object.defineProperty(FullTextDocument.prototype, \"lineCount\", {\n        get: function () {\n            return this.getLineOffsets().length;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return FullTextDocument;\n}());\nvar Is;\n(function (Is) {\n    var toString = Object.prototype.toString;\n    function defined(value) {\n        return typeof value !== 'undefined';\n    }\n    Is.defined = defined;\n    function undefined(value) {\n        return typeof value === 'undefined';\n    }\n    Is.undefined = undefined;\n    function boolean(value) {\n        return value === true || value === false;\n    }\n    Is.boolean = boolean;\n    function string(value) {\n        return toString.call(value) === '[object String]';\n    }\n    Is.string = string;\n    function number(value) {\n        return toString.call(value) === '[object Number]';\n    }\n    Is.number = number;\n    function numberRange(value, min, max) {\n        return toString.call(value) === '[object Number]' && min <= value && value <= max;\n    }\n    Is.numberRange = numberRange;\n    function integer(value) {\n        return toString.call(value) === '[object Number]' && -********** <= value && value <= **********;\n    }\n    Is.integer = integer;\n    function uinteger(value) {\n        return toString.call(value) === '[object Number]' && 0 <= value && value <= **********;\n    }\n    Is.uinteger = uinteger;\n    function func(value) {\n        return toString.call(value) === '[object Function]';\n    }\n    Is.func = func;\n    function objectLiteral(value) {\n        // Strictly speaking class instances pass this check as well. Since the LSP\n        // doesn't use classes we ignore this for now. If we do we need to add something\n        // like this: `Object.getPrototypeOf(Object.getPrototypeOf(x)) === null`\n        return value !== null && typeof value === 'object';\n    }\n    Is.objectLiteral = objectLiteral;\n    function typedArray(value, check) {\n        return Array.isArray(value) && value.every(check);\n    }\n    Is.typedArray = typedArray;\n})(Is || (Is = {}));\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as lsTypes from './_deps/vscode-languageserver-types/main.js';\nimport { languages, editor, Uri, Range, MarkerSeverity } from './fillers/monaco-editor-core.js';\n// --- diagnostics --- ---\nvar DiagnosticsAdapter = /** @class */ (function () {\n    function DiagnosticsAdapter(_languageId, _worker, defaults) {\n        var _this = this;\n        this._languageId = _languageId;\n        this._worker = _worker;\n        this._disposables = [];\n        this._listener = Object.create(null);\n        var onModelAdd = function (model) {\n            var modeId = model.getLanguageId();\n            if (modeId !== _this._languageId) {\n                return;\n            }\n            var handle;\n            _this._listener[model.uri.toString()] = model.onDidChangeContent(function () {\n                window.clearTimeout(handle);\n                handle = window.setTimeout(function () { return _this._doValidate(model.uri, modeId); }, 500);\n            });\n            _this._doValidate(model.uri, modeId);\n        };\n        var onModelRemoved = function (model) {\n            editor.setModelMarkers(model, _this._languageId, []);\n            var uriStr = model.uri.toString();\n            var listener = _this._listener[uriStr];\n            if (listener) {\n                listener.dispose();\n                delete _this._listener[uriStr];\n            }\n        };\n        this._disposables.push(editor.onDidCreateModel(onModelAdd));\n        this._disposables.push(editor.onWillDisposeModel(onModelRemoved));\n        this._disposables.push(editor.onDidChangeModelLanguage(function (event) {\n            onModelRemoved(event.model);\n            onModelAdd(event.model);\n        }));\n        defaults.onDidChange(function (_) {\n            editor.getModels().forEach(function (model) {\n                if (model.getLanguageId() === _this._languageId) {\n                    onModelRemoved(model);\n                    onModelAdd(model);\n                }\n            });\n        });\n        this._disposables.push({\n            dispose: function () {\n                for (var key in _this._listener) {\n                    _this._listener[key].dispose();\n                }\n            }\n        });\n        editor.getModels().forEach(onModelAdd);\n    }\n    DiagnosticsAdapter.prototype.dispose = function () {\n        this._disposables.forEach(function (d) { return d && d.dispose(); });\n        this._disposables = [];\n    };\n    DiagnosticsAdapter.prototype._doValidate = function (resource, languageId) {\n        this._worker(resource)\n            .then(function (worker) {\n            return worker.doValidation(resource.toString());\n        })\n            .then(function (diagnostics) {\n            var markers = diagnostics.map(function (d) { return toDiagnostics(resource, d); });\n            var model = editor.getModel(resource);\n            if (model && model.getLanguageId() === languageId) {\n                editor.setModelMarkers(model, languageId, markers);\n            }\n        })\n            .then(undefined, function (err) {\n            console.error(err);\n        });\n    };\n    return DiagnosticsAdapter;\n}());\nexport { DiagnosticsAdapter };\nfunction toSeverity(lsSeverity) {\n    switch (lsSeverity) {\n        case lsTypes.DiagnosticSeverity.Error:\n            return MarkerSeverity.Error;\n        case lsTypes.DiagnosticSeverity.Warning:\n            return MarkerSeverity.Warning;\n        case lsTypes.DiagnosticSeverity.Information:\n            return MarkerSeverity.Info;\n        case lsTypes.DiagnosticSeverity.Hint:\n            return MarkerSeverity.Hint;\n        default:\n            return MarkerSeverity.Info;\n    }\n}\nfunction toDiagnostics(resource, diag) {\n    var code = typeof diag.code === 'number' ? String(diag.code) : diag.code;\n    return {\n        severity: toSeverity(diag.severity),\n        startLineNumber: diag.range.start.line + 1,\n        startColumn: diag.range.start.character + 1,\n        endLineNumber: diag.range.end.line + 1,\n        endColumn: diag.range.end.character + 1,\n        message: diag.message,\n        code: code,\n        source: diag.source\n    };\n}\n// --- completion ------\nfunction fromPosition(position) {\n    if (!position) {\n        return void 0;\n    }\n    return { character: position.column - 1, line: position.lineNumber - 1 };\n}\nfunction fromRange(range) {\n    if (!range) {\n        return void 0;\n    }\n    return {\n        start: {\n            line: range.startLineNumber - 1,\n            character: range.startColumn - 1\n        },\n        end: { line: range.endLineNumber - 1, character: range.endColumn - 1 }\n    };\n}\nfunction toRange(range) {\n    if (!range) {\n        return void 0;\n    }\n    return new Range(range.start.line + 1, range.start.character + 1, range.end.line + 1, range.end.character + 1);\n}\nfunction isInsertReplaceEdit(edit) {\n    return (typeof edit.insert !== 'undefined' &&\n        typeof edit.replace !== 'undefined');\n}\nfunction toCompletionItemKind(kind) {\n    var mItemKind = languages.CompletionItemKind;\n    switch (kind) {\n        case lsTypes.CompletionItemKind.Text:\n            return mItemKind.Text;\n        case lsTypes.CompletionItemKind.Method:\n            return mItemKind.Method;\n        case lsTypes.CompletionItemKind.Function:\n            return mItemKind.Function;\n        case lsTypes.CompletionItemKind.Constructor:\n            return mItemKind.Constructor;\n        case lsTypes.CompletionItemKind.Field:\n            return mItemKind.Field;\n        case lsTypes.CompletionItemKind.Variable:\n            return mItemKind.Variable;\n        case lsTypes.CompletionItemKind.Class:\n            return mItemKind.Class;\n        case lsTypes.CompletionItemKind.Interface:\n            return mItemKind.Interface;\n        case lsTypes.CompletionItemKind.Module:\n            return mItemKind.Module;\n        case lsTypes.CompletionItemKind.Property:\n            return mItemKind.Property;\n        case lsTypes.CompletionItemKind.Unit:\n            return mItemKind.Unit;\n        case lsTypes.CompletionItemKind.Value:\n            return mItemKind.Value;\n        case lsTypes.CompletionItemKind.Enum:\n            return mItemKind.Enum;\n        case lsTypes.CompletionItemKind.Keyword:\n            return mItemKind.Keyword;\n        case lsTypes.CompletionItemKind.Snippet:\n            return mItemKind.Snippet;\n        case lsTypes.CompletionItemKind.Color:\n            return mItemKind.Color;\n        case lsTypes.CompletionItemKind.File:\n            return mItemKind.File;\n        case lsTypes.CompletionItemKind.Reference:\n            return mItemKind.Reference;\n    }\n    return mItemKind.Property;\n}\nfunction toTextEdit(textEdit) {\n    if (!textEdit) {\n        return void 0;\n    }\n    return {\n        range: toRange(textEdit.range),\n        text: textEdit.newText\n    };\n}\nfunction toCommand(c) {\n    return c && c.command === 'editor.action.triggerSuggest'\n        ? { id: c.command, title: c.title, arguments: c.arguments }\n        : undefined;\n}\nvar CompletionAdapter = /** @class */ (function () {\n    function CompletionAdapter(_worker) {\n        this._worker = _worker;\n    }\n    Object.defineProperty(CompletionAdapter.prototype, \"triggerCharacters\", {\n        get: function () {\n            return ['/', '-', ':'];\n        },\n        enumerable: false,\n        configurable: true\n    });\n    CompletionAdapter.prototype.provideCompletionItems = function (model, position, context, token) {\n        var resource = model.uri;\n        return this._worker(resource)\n            .then(function (worker) {\n            return worker.doComplete(resource.toString(), fromPosition(position));\n        })\n            .then(function (info) {\n            if (!info) {\n                return;\n            }\n            var wordInfo = model.getWordUntilPosition(position);\n            var wordRange = new Range(position.lineNumber, wordInfo.startColumn, position.lineNumber, wordInfo.endColumn);\n            var items = info.items.map(function (entry) {\n                var item = {\n                    label: entry.label,\n                    insertText: entry.insertText || entry.label,\n                    sortText: entry.sortText,\n                    filterText: entry.filterText,\n                    documentation: entry.documentation,\n                    detail: entry.detail,\n                    command: toCommand(entry.command),\n                    range: wordRange,\n                    kind: toCompletionItemKind(entry.kind)\n                };\n                if (entry.textEdit) {\n                    if (isInsertReplaceEdit(entry.textEdit)) {\n                        item.range = {\n                            insert: toRange(entry.textEdit.insert),\n                            replace: toRange(entry.textEdit.replace)\n                        };\n                    }\n                    else {\n                        item.range = toRange(entry.textEdit.range);\n                    }\n                    item.insertText = entry.textEdit.newText;\n                }\n                if (entry.additionalTextEdits) {\n                    item.additionalTextEdits = entry.additionalTextEdits.map(toTextEdit);\n                }\n                if (entry.insertTextFormat === lsTypes.InsertTextFormat.Snippet) {\n                    item.insertTextRules = languages.CompletionItemInsertTextRule.InsertAsSnippet;\n                }\n                return item;\n            });\n            return {\n                isIncomplete: info.isIncomplete,\n                suggestions: items\n            };\n        });\n    };\n    return CompletionAdapter;\n}());\nexport { CompletionAdapter };\nfunction isMarkupContent(thing) {\n    return (thing && typeof thing === 'object' && typeof thing.kind === 'string');\n}\nfunction toMarkdownString(entry) {\n    if (typeof entry === 'string') {\n        return {\n            value: entry\n        };\n    }\n    if (isMarkupContent(entry)) {\n        if (entry.kind === 'plaintext') {\n            return {\n                value: entry.value.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, '\\\\$&')\n            };\n        }\n        return {\n            value: entry.value\n        };\n    }\n    return { value: '```' + entry.language + '\\n' + entry.value + '\\n```\\n' };\n}\nfunction toMarkedStringArray(contents) {\n    if (!contents) {\n        return void 0;\n    }\n    if (Array.isArray(contents)) {\n        return contents.map(toMarkdownString);\n    }\n    return [toMarkdownString(contents)];\n}\n// --- hover ------\nvar HoverAdapter = /** @class */ (function () {\n    function HoverAdapter(_worker) {\n        this._worker = _worker;\n    }\n    HoverAdapter.prototype.provideHover = function (model, position, token) {\n        var resource = model.uri;\n        return this._worker(resource)\n            .then(function (worker) {\n            return worker.doHover(resource.toString(), fromPosition(position));\n        })\n            .then(function (info) {\n            if (!info) {\n                return;\n            }\n            return {\n                range: toRange(info.range),\n                contents: toMarkedStringArray(info.contents)\n            };\n        });\n    };\n    return HoverAdapter;\n}());\nexport { HoverAdapter };\n// --- document highlights ------\nfunction toDocumentHighlightKind(kind) {\n    switch (kind) {\n        case lsTypes.DocumentHighlightKind.Read:\n            return languages.DocumentHighlightKind.Read;\n        case lsTypes.DocumentHighlightKind.Write:\n            return languages.DocumentHighlightKind.Write;\n        case lsTypes.DocumentHighlightKind.Text:\n            return languages.DocumentHighlightKind.Text;\n    }\n    return languages.DocumentHighlightKind.Text;\n}\nvar DocumentHighlightAdapter = /** @class */ (function () {\n    function DocumentHighlightAdapter(_worker) {\n        this._worker = _worker;\n    }\n    DocumentHighlightAdapter.prototype.provideDocumentHighlights = function (model, position, token) {\n        var resource = model.uri;\n        return this._worker(resource)\n            .then(function (worker) {\n            return worker.findDocumentHighlights(resource.toString(), fromPosition(position));\n        })\n            .then(function (entries) {\n            if (!entries) {\n                return;\n            }\n            return entries.map(function (entry) {\n                return {\n                    range: toRange(entry.range),\n                    kind: toDocumentHighlightKind(entry.kind)\n                };\n            });\n        });\n    };\n    return DocumentHighlightAdapter;\n}());\nexport { DocumentHighlightAdapter };\n// --- definition ------\nfunction toLocation(location) {\n    return {\n        uri: Uri.parse(location.uri),\n        range: toRange(location.range)\n    };\n}\nvar DefinitionAdapter = /** @class */ (function () {\n    function DefinitionAdapter(_worker) {\n        this._worker = _worker;\n    }\n    DefinitionAdapter.prototype.provideDefinition = function (model, position, token) {\n        var resource = model.uri;\n        return this._worker(resource)\n            .then(function (worker) {\n            return worker.findDefinition(resource.toString(), fromPosition(position));\n        })\n            .then(function (definition) {\n            if (!definition) {\n                return;\n            }\n            return [toLocation(definition)];\n        });\n    };\n    return DefinitionAdapter;\n}());\nexport { DefinitionAdapter };\n// --- references ------\nvar ReferenceAdapter = /** @class */ (function () {\n    function ReferenceAdapter(_worker) {\n        this._worker = _worker;\n    }\n    ReferenceAdapter.prototype.provideReferences = function (model, position, context, token) {\n        var resource = model.uri;\n        return this._worker(resource)\n            .then(function (worker) {\n            return worker.findReferences(resource.toString(), fromPosition(position));\n        })\n            .then(function (entries) {\n            if (!entries) {\n                return;\n            }\n            return entries.map(toLocation);\n        });\n    };\n    return ReferenceAdapter;\n}());\nexport { ReferenceAdapter };\n// --- rename ------\nfunction toWorkspaceEdit(edit) {\n    if (!edit || !edit.changes) {\n        return void 0;\n    }\n    var resourceEdits = [];\n    for (var uri in edit.changes) {\n        var _uri = Uri.parse(uri);\n        // let edits: languages.TextEdit[] = [];\n        for (var _i = 0, _a = edit.changes[uri]; _i < _a.length; _i++) {\n            var e = _a[_i];\n            resourceEdits.push({\n                resource: _uri,\n                edit: {\n                    range: toRange(e.range),\n                    text: e.newText\n                }\n            });\n        }\n    }\n    return {\n        edits: resourceEdits\n    };\n}\nvar RenameAdapter = /** @class */ (function () {\n    function RenameAdapter(_worker) {\n        this._worker = _worker;\n    }\n    RenameAdapter.prototype.provideRenameEdits = function (model, position, newName, token) {\n        var resource = model.uri;\n        return this._worker(resource)\n            .then(function (worker) {\n            return worker.doRename(resource.toString(), fromPosition(position), newName);\n        })\n            .then(function (edit) {\n            return toWorkspaceEdit(edit);\n        });\n    };\n    return RenameAdapter;\n}());\nexport { RenameAdapter };\n// --- document symbols ------\nfunction toSymbolKind(kind) {\n    var mKind = languages.SymbolKind;\n    switch (kind) {\n        case lsTypes.SymbolKind.File:\n            return mKind.Array;\n        case lsTypes.SymbolKind.Module:\n            return mKind.Module;\n        case lsTypes.SymbolKind.Namespace:\n            return mKind.Namespace;\n        case lsTypes.SymbolKind.Package:\n            return mKind.Package;\n        case lsTypes.SymbolKind.Class:\n            return mKind.Class;\n        case lsTypes.SymbolKind.Method:\n            return mKind.Method;\n        case lsTypes.SymbolKind.Property:\n            return mKind.Property;\n        case lsTypes.SymbolKind.Field:\n            return mKind.Field;\n        case lsTypes.SymbolKind.Constructor:\n            return mKind.Constructor;\n        case lsTypes.SymbolKind.Enum:\n            return mKind.Enum;\n        case lsTypes.SymbolKind.Interface:\n            return mKind.Interface;\n        case lsTypes.SymbolKind.Function:\n            return mKind.Function;\n        case lsTypes.SymbolKind.Variable:\n            return mKind.Variable;\n        case lsTypes.SymbolKind.Constant:\n            return mKind.Constant;\n        case lsTypes.SymbolKind.String:\n            return mKind.String;\n        case lsTypes.SymbolKind.Number:\n            return mKind.Number;\n        case lsTypes.SymbolKind.Boolean:\n            return mKind.Boolean;\n        case lsTypes.SymbolKind.Array:\n            return mKind.Array;\n    }\n    return mKind.Function;\n}\nvar DocumentSymbolAdapter = /** @class */ (function () {\n    function DocumentSymbolAdapter(_worker) {\n        this._worker = _worker;\n    }\n    DocumentSymbolAdapter.prototype.provideDocumentSymbols = function (model, token) {\n        var resource = model.uri;\n        return this._worker(resource)\n            .then(function (worker) { return worker.findDocumentSymbols(resource.toString()); })\n            .then(function (items) {\n            if (!items) {\n                return;\n            }\n            return items.map(function (item) { return ({\n                name: item.name,\n                detail: '',\n                containerName: item.containerName,\n                kind: toSymbolKind(item.kind),\n                tags: [],\n                range: toRange(item.location.range),\n                selectionRange: toRange(item.location.range)\n            }); });\n        });\n    };\n    return DocumentSymbolAdapter;\n}());\nexport { DocumentSymbolAdapter };\nvar DocumentColorAdapter = /** @class */ (function () {\n    function DocumentColorAdapter(_worker) {\n        this._worker = _worker;\n    }\n    DocumentColorAdapter.prototype.provideDocumentColors = function (model, token) {\n        var resource = model.uri;\n        return this._worker(resource)\n            .then(function (worker) { return worker.findDocumentColors(resource.toString()); })\n            .then(function (infos) {\n            if (!infos) {\n                return;\n            }\n            return infos.map(function (item) { return ({\n                color: item.color,\n                range: toRange(item.range)\n            }); });\n        });\n    };\n    DocumentColorAdapter.prototype.provideColorPresentations = function (model, info, token) {\n        var resource = model.uri;\n        return this._worker(resource)\n            .then(function (worker) {\n            return worker.getColorPresentations(resource.toString(), info.color, fromRange(info.range));\n        })\n            .then(function (presentations) {\n            if (!presentations) {\n                return;\n            }\n            return presentations.map(function (presentation) {\n                var item = {\n                    label: presentation.label\n                };\n                if (presentation.textEdit) {\n                    item.textEdit = toTextEdit(presentation.textEdit);\n                }\n                if (presentation.additionalTextEdits) {\n                    item.additionalTextEdits = presentation.additionalTextEdits.map(toTextEdit);\n                }\n                return item;\n            });\n        });\n    };\n    return DocumentColorAdapter;\n}());\nexport { DocumentColorAdapter };\nvar FoldingRangeAdapter = /** @class */ (function () {\n    function FoldingRangeAdapter(_worker) {\n        this._worker = _worker;\n    }\n    FoldingRangeAdapter.prototype.provideFoldingRanges = function (model, context, token) {\n        var resource = model.uri;\n        return this._worker(resource)\n            .then(function (worker) { return worker.getFoldingRanges(resource.toString(), context); })\n            .then(function (ranges) {\n            if (!ranges) {\n                return;\n            }\n            return ranges.map(function (range) {\n                var result = {\n                    start: range.startLine + 1,\n                    end: range.endLine + 1\n                };\n                if (typeof range.kind !== 'undefined') {\n                    result.kind = toFoldingRangeKind(range.kind);\n                }\n                return result;\n            });\n        });\n    };\n    return FoldingRangeAdapter;\n}());\nexport { FoldingRangeAdapter };\nfunction toFoldingRangeKind(kind) {\n    switch (kind) {\n        case lsTypes.FoldingRangeKind.Comment:\n            return languages.FoldingRangeKind.Comment;\n        case lsTypes.FoldingRangeKind.Imports:\n            return languages.FoldingRangeKind.Imports;\n        case lsTypes.FoldingRangeKind.Region:\n            return languages.FoldingRangeKind.Region;\n    }\n}\nvar SelectionRangeAdapter = /** @class */ (function () {\n    function SelectionRangeAdapter(_worker) {\n        this._worker = _worker;\n    }\n    SelectionRangeAdapter.prototype.provideSelectionRanges = function (model, positions, token) {\n        var resource = model.uri;\n        return this._worker(resource)\n            .then(function (worker) { return worker.getSelectionRanges(resource.toString(), positions.map(fromPosition)); })\n            .then(function (selectionRanges) {\n            if (!selectionRanges) {\n                return;\n            }\n            return selectionRanges.map(function (selectionRange) {\n                var result = [];\n                while (selectionRange) {\n                    result.push({ range: toRange(selectionRange.range) });\n                    selectionRange = selectionRange.parent;\n                }\n                return result;\n            });\n        });\n    };\n    return SelectionRangeAdapter;\n}());\nexport { SelectionRangeAdapter };\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { WorkerManager } from './workerManager.js';\nimport * as languageFeatures from './languageFeatures.js';\nimport { languages } from './fillers/monaco-editor-core.js';\nexport function setupMode(defaults) {\n    var disposables = [];\n    var providers = [];\n    var client = new WorkerManager(defaults);\n    disposables.push(client);\n    var worker = function () {\n        var uris = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            uris[_i] = arguments[_i];\n        }\n        return client.getLanguageServiceWorker.apply(client, uris);\n    };\n    function registerProviders() {\n        var languageId = defaults.languageId, modeConfiguration = defaults.modeConfiguration;\n        disposeAll(providers);\n        if (modeConfiguration.completionItems) {\n            providers.push(languages.registerCompletionItemProvider(languageId, new languageFeatures.CompletionAdapter(worker)));\n        }\n        if (modeConfiguration.hovers) {\n            providers.push(languages.registerHoverProvider(languageId, new languageFeatures.HoverAdapter(worker)));\n        }\n        if (modeConfiguration.documentHighlights) {\n            providers.push(languages.registerDocumentHighlightProvider(languageId, new languageFeatures.DocumentHighlightAdapter(worker)));\n        }\n        if (modeConfiguration.definitions) {\n            providers.push(languages.registerDefinitionProvider(languageId, new languageFeatures.DefinitionAdapter(worker)));\n        }\n        if (modeConfiguration.references) {\n            providers.push(languages.registerReferenceProvider(languageId, new languageFeatures.ReferenceAdapter(worker)));\n        }\n        if (modeConfiguration.documentSymbols) {\n            providers.push(languages.registerDocumentSymbolProvider(languageId, new languageFeatures.DocumentSymbolAdapter(worker)));\n        }\n        if (modeConfiguration.rename) {\n            providers.push(languages.registerRenameProvider(languageId, new languageFeatures.RenameAdapter(worker)));\n        }\n        if (modeConfiguration.colors) {\n            providers.push(languages.registerColorProvider(languageId, new languageFeatures.DocumentColorAdapter(worker)));\n        }\n        if (modeConfiguration.foldingRanges) {\n            providers.push(languages.registerFoldingRangeProvider(languageId, new languageFeatures.FoldingRangeAdapter(worker)));\n        }\n        if (modeConfiguration.diagnostics) {\n            providers.push(new languageFeatures.DiagnosticsAdapter(languageId, worker, defaults));\n        }\n        if (modeConfiguration.selectionRanges) {\n            providers.push(languages.registerSelectionRangeProvider(languageId, new languageFeatures.SelectionRangeAdapter(worker)));\n        }\n    }\n    registerProviders();\n    disposables.push(asDisposable(providers));\n    return asDisposable(disposables);\n}\nfunction asDisposable(disposables) {\n    return { dispose: function () { return disposeAll(disposables); } };\n}\nfunction disposeAll(disposables) {\n    while (disposables.length) {\n        disposables.pop().dispose();\n    }\n}\n"], "mappings": ";;;;;;;;;;;AAKA,IAAI,qBAAqB,IAAI,KAAK;AAClC,IAAI;AAAA;AAAA,EAA+B,WAAY;AAC3C,aAASA,eAAc,UAAU;AAC7B,UAAI,QAAQ;AACZ,WAAK,YAAY;AACjB,WAAK,UAAU;AACf,WAAK,qBAAqB,OAAO,YAAY,WAAY;AAAE,eAAO,MAAM,aAAa;AAAA,MAAG,GAAG,KAAK,GAAI;AACpG,WAAK,gBAAgB;AACrB,WAAK,wBAAwB,KAAK,UAAU,YAAY,WAAY;AAAE,eAAO,MAAM,YAAY;AAAA,MAAG,CAAC;AAAA,IACvG;AACA,IAAAA,eAAc,UAAU,cAAc,WAAY;AAC9C,UAAI,KAAK,SAAS;AACd,aAAK,QAAQ,QAAQ;AACrB,aAAK,UAAU;AAAA,MACnB;AACA,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,eAAc,UAAU,UAAU,WAAY;AAC1C,oBAAc,KAAK,kBAAkB;AACrC,WAAK,sBAAsB,QAAQ;AACnC,WAAK,YAAY;AAAA,IACrB;AACA,IAAAA,eAAc,UAAU,eAAe,WAAY;AAC/C,UAAI,CAAC,KAAK,SAAS;AACf;AAAA,MACJ;AACA,UAAI,0BAA0B,KAAK,IAAI,IAAI,KAAK;AAChD,UAAI,0BAA0B,oBAAoB;AAC9C,aAAK,YAAY;AAAA,MACrB;AAAA,IACJ;AACA,IAAAA,eAAc,UAAU,aAAa,WAAY;AAC7C,WAAK,gBAAgB,KAAK,IAAI;AAC9B,UAAI,CAAC,KAAK,SAAS;AACf,aAAK,UAAU,OAAO,gBAAgB;AAAA;AAAA,UAElC,UAAU;AAAA,UACV,OAAO,KAAK,UAAU;AAAA;AAAA,UAEtB,YAAY;AAAA,YACR,SAAS,KAAK,UAAU;AAAA,YACxB,YAAY,KAAK,UAAU;AAAA,UAC/B;AAAA,QACJ,CAAC;AACD,aAAK,UAAU,KAAK,QAAQ,SAAS;AAAA,MACzC;AACA,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,eAAc,UAAU,2BAA2B,WAAY;AAC3D,UAAI,QAAQ;AACZ,UAAI,YAAY,CAAC;AACjB,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,kBAAU,EAAE,IAAI,UAAU,EAAE;AAAA,MAChC;AACA,UAAI;AACJ,aAAO,KAAK,WAAW,EAClB,KAAK,SAAU,QAAQ;AACxB,kBAAU;AAAA,MACd,CAAC,EACI,KAAK,SAAU,GAAG;AACnB,eAAO,MAAM,QAAQ,oBAAoB,SAAS;AAAA,MACtD,CAAC,EACI,KAAK,SAAU,GAAG;AAAE,eAAO;AAAA,MAAS,CAAC;AAAA,IAC9C;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;;;ACjEK,IAAI;AAAA,CACV,SAAUC,UAAS;AAChB,EAAAA,SAAQ,YAAY;AACpB,EAAAA,SAAQ,YAAY;AACxB,GAAG,YAAY,UAAU,CAAC,EAAE;AACrB,IAAI;AAAA,CACV,SAAUC,WAAU;AACjB,EAAAA,UAAS,YAAY;AACrB,EAAAA,UAAS,YAAY;AACzB,GAAG,aAAa,WAAW,CAAC,EAAE;AAKvB,IAAI;AAAA,CACV,SAAUC,WAAU;AAMjB,WAAS,OAAO,MAAM,WAAW;AAC7B,QAAI,SAAS,OAAO,WAAW;AAC3B,aAAO,SAAS;AAAA,IACpB;AACA,QAAI,cAAc,OAAO,WAAW;AAChC,kBAAY,SAAS;AAAA,IACzB;AACA,WAAO,EAAE,MAAY,UAAqB;AAAA,EAC9C;AACA,EAAAA,UAAS,SAAS;AAIlB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,cAAc,SAAS,KAAK,GAAG,SAAS,UAAU,IAAI,KAAK,GAAG,SAAS,UAAU,SAAS;AAAA,EACxG;AACA,EAAAA,UAAS,KAAK;AAClB,GAAG,aAAa,WAAW,CAAC,EAAE;AAKvB,IAAIC;AAAA,CACV,SAAUA,QAAO;AACd,WAAS,OAAO,KAAK,KAAK,OAAO,MAAM;AACnC,QAAI,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,KAAK,KAAK,GAAG,SAAS,IAAI,GAAG;AACjF,aAAO,EAAE,OAAO,SAAS,OAAO,KAAK,GAAG,GAAG,KAAK,SAAS,OAAO,OAAO,IAAI,EAAE;AAAA,IACjF,WACS,SAAS,GAAG,GAAG,KAAK,SAAS,GAAG,GAAG,GAAG;AAC3C,aAAO,EAAE,OAAO,KAAK,KAAK,IAAI;AAAA,IAClC,OACK;AACD,YAAM,IAAI,MAAM,gDAAgD,MAAM,OAAO,MAAM,OAAO,QAAQ,OAAO,OAAO,GAAG;AAAA,IACvH;AAAA,EACJ;AACA,EAAAA,OAAM,SAAS;AAIf,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,cAAc,SAAS,KAAK,SAAS,GAAG,UAAU,KAAK,KAAK,SAAS,GAAG,UAAU,GAAG;AAAA,EACnG;AACA,EAAAA,OAAM,KAAK;AACf,GAAGA,WAAUA,SAAQ,CAAC,EAAE;AAKjB,IAAI;AAAA,CACV,SAAUC,WAAU;AAMjB,WAAS,OAAO,KAAK,OAAO;AACxB,WAAO,EAAE,KAAU,MAAa;AAAA,EACpC;AACA,EAAAA,UAAS,SAAS;AAIlB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAKD,OAAM,GAAG,UAAU,KAAK,MAAM,GAAG,OAAO,UAAU,GAAG,KAAK,GAAG,UAAU,UAAU,GAAG;AAAA,EACxH;AACA,EAAAC,UAAS,KAAK;AAClB,GAAG,aAAa,WAAW,CAAC,EAAE;AAKvB,IAAI;AAAA,CACV,SAAUC,eAAc;AAQrB,WAAS,OAAO,WAAW,aAAa,sBAAsB,sBAAsB;AAChF,WAAO,EAAE,WAAsB,aAA0B,sBAA4C,qBAA2C;AAAA,EACpJ;AACA,EAAAA,cAAa,SAAS;AAItB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAKF,OAAM,GAAG,UAAU,WAAW,KAAK,GAAG,OAAO,UAAU,SAAS,MACxFA,OAAM,GAAG,UAAU,oBAAoB,KAAK,GAAG,UAAU,UAAU,oBAAoB,OACvFA,OAAM,GAAG,UAAU,oBAAoB,KAAK,GAAG,UAAU,UAAU,oBAAoB;AAAA,EACnG;AACA,EAAAE,cAAa,KAAK;AACtB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAK/B,IAAI;AAAA,CACV,SAAUC,QAAO;AAId,WAAS,OAAO,KAAK,OAAO,MAAM,OAAO;AACrC,WAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,OAAM,SAAS;AAIf,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,YAAY,UAAU,KAAK,GAAG,CAAC,KAClC,GAAG,YAAY,UAAU,OAAO,GAAG,CAAC,KACpC,GAAG,YAAY,UAAU,MAAM,GAAG,CAAC,KACnC,GAAG,YAAY,UAAU,OAAO,GAAG,CAAC;AAAA,EAC/C;AACA,EAAAA,OAAM,KAAK;AACf,GAAG,UAAU,QAAQ,CAAC,EAAE;AAKjB,IAAI;AAAA,CACV,SAAUC,mBAAkB;AAIzB,WAAS,OAAO,OAAO,OAAO;AAC1B,WAAO;AAAA,MACH;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,kBAAiB,SAAS;AAI1B,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAOJ,OAAM,GAAG,UAAU,KAAK,KAAK,MAAM,GAAG,UAAU,KAAK;AAAA,EAChE;AACA,EAAAI,kBAAiB,KAAK;AAC1B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAKvC,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAI1B,WAAS,OAAO,OAAO,UAAU,qBAAqB;AAClD,WAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,mBAAkB,SAAS;AAI3B,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,OAAO,UAAU,KAAK,MACxB,GAAG,UAAU,UAAU,QAAQ,KAAK,SAAS,GAAG,SAAS,OACzD,GAAG,UAAU,UAAU,mBAAmB,KAAK,GAAG,WAAW,UAAU,qBAAqB,SAAS,EAAE;AAAA,EACnH;AACA,EAAAA,mBAAkB,KAAK;AAC3B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAIzC,IAAI;AAAA,CACV,SAAUC,mBAAkB;AAIzB,EAAAA,kBAAiB,SAAS,IAAI;AAI9B,EAAAA,kBAAiB,SAAS,IAAI;AAI9B,EAAAA,kBAAiB,QAAQ,IAAI;AACjC,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAKvC,IAAI;AAAA,CACV,SAAUC,eAAc;AAIrB,WAAS,OAAO,WAAW,SAAS,gBAAgB,cAAc,MAAM;AACpE,QAAI,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACJ;AACA,QAAI,GAAG,QAAQ,cAAc,GAAG;AAC5B,aAAO,iBAAiB;AAAA,IAC5B;AACA,QAAI,GAAG,QAAQ,YAAY,GAAG;AAC1B,aAAO,eAAe;AAAA,IAC1B;AACA,QAAI,GAAG,QAAQ,IAAI,GAAG;AAClB,aAAO,OAAO;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,cAAa,SAAS;AAItB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,SAAS,UAAU,SAAS,KAAK,GAAG,SAAS,UAAU,SAAS,MAClE,GAAG,UAAU,UAAU,cAAc,KAAK,GAAG,SAAS,UAAU,cAAc,OAC9E,GAAG,UAAU,UAAU,YAAY,KAAK,GAAG,SAAS,UAAU,YAAY,OAC1E,GAAG,UAAU,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,IAAI;AAAA,EACpE;AACA,EAAAA,cAAa,KAAK;AACtB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAK/B,IAAI;AAAA,CACV,SAAUC,+BAA8B;AAIrC,WAAS,OAAO,UAAU,SAAS;AAC/B,WAAO;AAAA,MACH;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,8BAA6B,SAAS;AAItC,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,SAAS,GAAG,UAAU,QAAQ,KAAK,GAAG,OAAO,UAAU,OAAO;AAAA,EAClG;AACA,EAAAA,8BAA6B,KAAK;AACtC,GAAG,iCAAiC,+BAA+B,CAAC,EAAE;AAI/D,IAAI;AAAA,CACV,SAAUC,qBAAoB;AAI3B,EAAAA,oBAAmB,QAAQ;AAI3B,EAAAA,oBAAmB,UAAU;AAI7B,EAAAA,oBAAmB,cAAc;AAIjC,EAAAA,oBAAmB,OAAO;AAC9B,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAM3C,IAAI;AAAA,CACV,SAAUC,gBAAe;AAOtB,EAAAA,eAAc,cAAc;AAM5B,EAAAA,eAAc,aAAa;AAC/B,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAMjC,IAAI;AAAA,CACV,SAAUC,kBAAiB;AACxB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,cAAc,UAAa,cAAc,QAAQ,GAAG,OAAO,UAAU,IAAI;AAAA,EACpF;AACA,EAAAA,iBAAgB,KAAK;AACzB,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAKrC,IAAI;AAAA,CACV,SAAUC,aAAY;AAInB,WAAS,OAAO,OAAO,SAAS,UAAU,MAAM,QAAQ,oBAAoB;AACxE,QAAI,SAAS,EAAE,OAAc,QAAiB;AAC9C,QAAI,GAAG,QAAQ,QAAQ,GAAG;AACtB,aAAO,WAAW;AAAA,IACtB;AACA,QAAI,GAAG,QAAQ,IAAI,GAAG;AAClB,aAAO,OAAO;AAAA,IAClB;AACA,QAAI,GAAG,QAAQ,MAAM,GAAG;AACpB,aAAO,SAAS;AAAA,IACpB;AACA,QAAI,GAAG,QAAQ,kBAAkB,GAAG;AAChC,aAAO,qBAAqB;AAAA,IAChC;AACA,WAAO;AAAA,EACX;AACA,EAAAA,YAAW,SAAS;AAIpB,WAAS,GAAG,OAAO;AACf,QAAI;AACJ,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KACpBZ,OAAM,GAAG,UAAU,KAAK,KACxB,GAAG,OAAO,UAAU,OAAO,MAC1B,GAAG,OAAO,UAAU,QAAQ,KAAK,GAAG,UAAU,UAAU,QAAQ,OAChE,GAAG,QAAQ,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,IAAI,KAAK,GAAG,UAAU,UAAU,IAAI,OACtF,GAAG,UAAU,UAAU,eAAe,KAAM,GAAG,QAAQ,KAAK,UAAU,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI,OACnI,GAAG,OAAO,UAAU,MAAM,KAAK,GAAG,UAAU,UAAU,MAAM,OAC5D,GAAG,UAAU,UAAU,kBAAkB,KAAK,GAAG,WAAW,UAAU,oBAAoB,6BAA6B,EAAE;AAAA,EACrI;AACA,EAAAY,YAAW,KAAK;AACpB,GAAG,eAAe,aAAa,CAAC,EAAE;AAK3B,IAAI;AAAA,CACV,SAAUC,UAAS;AAIhB,WAAS,OAAO,OAAO,SAAS;AAC5B,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,IAC/B;AACA,QAAI,SAAS,EAAE,OAAc,QAAiB;AAC9C,QAAI,GAAG,QAAQ,IAAI,KAAK,KAAK,SAAS,GAAG;AACrC,aAAO,YAAY;AAAA,IACvB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,SAAQ,SAAS;AAIjB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,KAAK,KAAK,GAAG,OAAO,UAAU,OAAO;AAAA,EAC7F;AACA,EAAAA,SAAQ,KAAK;AACjB,GAAG,YAAY,UAAU,CAAC,EAAE;AAKrB,IAAI;AAAA,CACV,SAAUC,WAAU;AAMjB,WAAS,QAAQ,OAAO,SAAS;AAC7B,WAAO,EAAE,OAAc,QAAiB;AAAA,EAC5C;AACA,EAAAA,UAAS,UAAU;AAMnB,WAAS,OAAO,UAAU,SAAS;AAC/B,WAAO,EAAE,OAAO,EAAE,OAAO,UAAU,KAAK,SAAS,GAAG,QAAiB;AAAA,EACzE;AACA,EAAAA,UAAS,SAAS;AAKlB,WAAS,IAAI,OAAO;AAChB,WAAO,EAAE,OAAc,SAAS,GAAG;AAAA,EACvC;AACA,EAAAA,UAAS,MAAM;AACf,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,cAAc,SAAS,KAC1B,GAAG,OAAO,UAAU,OAAO,KAC3Bd,OAAM,GAAG,UAAU,KAAK;AAAA,EACnC;AACA,EAAAc,UAAS,KAAK;AAClB,GAAG,aAAa,WAAW,CAAC,EAAE;AACvB,IAAI;AAAA,CACV,SAAUC,mBAAkB;AACzB,WAAS,OAAO,OAAO,mBAAmB,aAAa;AACnD,QAAI,SAAS,EAAE,MAAa;AAC5B,QAAI,sBAAsB,QAAW;AACjC,aAAO,oBAAoB;AAAA,IAC/B;AACA,QAAI,gBAAgB,QAAW;AAC3B,aAAO,cAAc;AAAA,IACzB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,kBAAiB,SAAS;AAC1B,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,cAAc,UAAa,GAAG,cAAc,SAAS,KAAK,GAAG,OAAO,UAAU,KAAK,MACrF,GAAG,QAAQ,UAAU,iBAAiB,KAAK,UAAU,sBAAsB,YAC3E,GAAG,OAAO,UAAU,WAAW,KAAK,UAAU,gBAAgB;AAAA,EACvE;AACA,EAAAA,kBAAiB,KAAK;AAC1B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AACvC,IAAI;AAAA,CACV,SAAUC,6BAA4B;AACnC,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,OAAO,cAAc;AAAA,EAChC;AACA,EAAAA,4BAA2B,KAAK;AACpC,GAAG,+BAA+B,6BAA6B,CAAC,EAAE;AAC3D,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAQ1B,WAAS,QAAQ,OAAO,SAAS,YAAY;AACzC,WAAO,EAAE,OAAc,SAAkB,cAAc,WAAW;AAAA,EACtE;AACA,EAAAA,mBAAkB,UAAU;AAQ5B,WAAS,OAAO,UAAU,SAAS,YAAY;AAC3C,WAAO,EAAE,OAAO,EAAE,OAAO,UAAU,KAAK,SAAS,GAAG,SAAkB,cAAc,WAAW;AAAA,EACnG;AACA,EAAAA,mBAAkB,SAAS;AAO3B,WAAS,IAAI,OAAO,YAAY;AAC5B,WAAO,EAAE,OAAc,SAAS,IAAI,cAAc,WAAW;AAAA,EACjE;AACA,EAAAA,mBAAkB,MAAM;AACxB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,SAAS,GAAG,SAAS,MAAM,iBAAiB,GAAG,UAAU,YAAY,KAAK,2BAA2B,GAAG,UAAU,YAAY;AAAA,EACzI;AACA,EAAAA,mBAAkB,KAAK;AAC3B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAKzC,IAAI;AAAA,CACV,SAAUC,mBAAkB;AAIzB,WAAS,OAAO,cAAc,OAAO;AACjC,WAAO,EAAE,cAA4B,MAAa;AAAA,EACtD;AACA,EAAAA,kBAAiB,SAAS;AAC1B,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KACpB,wCAAwC,GAAG,UAAU,YAAY,KACjE,MAAM,QAAQ,UAAU,KAAK;AAAA,EACxC;AACA,EAAAA,kBAAiB,KAAK;AAC1B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AACvC,IAAI;AAAA,CACV,SAAUC,aAAY;AACnB,WAAS,OAAO,KAAK,SAAS,YAAY;AACtC,QAAI,SAAS;AAAA,MACT,MAAM;AAAA,MACN;AAAA,IACJ;AACA,QAAI,YAAY,WAAc,QAAQ,cAAc,UAAa,QAAQ,mBAAmB,SAAY;AACpG,aAAO,UAAU;AAAA,IACrB;AACA,QAAI,eAAe,QAAW;AAC1B,aAAO,eAAe;AAAA,IAC1B;AACA,WAAO;AAAA,EACX;AACA,EAAAA,YAAW,SAAS;AACpB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,aAAa,UAAU,SAAS,YAAY,GAAG,OAAO,UAAU,GAAG,MAAM,UAAU,YAAY,WAChG,UAAU,QAAQ,cAAc,UAAa,GAAG,QAAQ,UAAU,QAAQ,SAAS,OAAO,UAAU,QAAQ,mBAAmB,UAAa,GAAG,QAAQ,UAAU,QAAQ,cAAc,QAAS,UAAU,iBAAiB,UAAa,2BAA2B,GAAG,UAAU,YAAY;AAAA,EACtS;AACA,EAAAA,YAAW,KAAK;AACpB,GAAG,eAAe,aAAa,CAAC,EAAE;AAC3B,IAAI;AAAA,CACV,SAAUC,aAAY;AACnB,WAAS,OAAO,QAAQ,QAAQ,SAAS,YAAY;AACjD,QAAI,SAAS;AAAA,MACT,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACJ;AACA,QAAI,YAAY,WAAc,QAAQ,cAAc,UAAa,QAAQ,mBAAmB,SAAY;AACpG,aAAO,UAAU;AAAA,IACrB;AACA,QAAI,eAAe,QAAW;AAC1B,aAAO,eAAe;AAAA,IAC1B;AACA,WAAO;AAAA,EACX;AACA,EAAAA,YAAW,SAAS;AACpB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,aAAa,UAAU,SAAS,YAAY,GAAG,OAAO,UAAU,MAAM,KAAK,GAAG,OAAO,UAAU,MAAM,MAAM,UAAU,YAAY,WAClI,UAAU,QAAQ,cAAc,UAAa,GAAG,QAAQ,UAAU,QAAQ,SAAS,OAAO,UAAU,QAAQ,mBAAmB,UAAa,GAAG,QAAQ,UAAU,QAAQ,cAAc,QAAS,UAAU,iBAAiB,UAAa,2BAA2B,GAAG,UAAU,YAAY;AAAA,EACtS;AACA,EAAAA,YAAW,KAAK;AACpB,GAAG,eAAe,aAAa,CAAC,EAAE;AAC3B,IAAI;AAAA,CACV,SAAUC,aAAY;AACnB,WAAS,OAAO,KAAK,SAAS,YAAY;AACtC,QAAI,SAAS;AAAA,MACT,MAAM;AAAA,MACN;AAAA,IACJ;AACA,QAAI,YAAY,WAAc,QAAQ,cAAc,UAAa,QAAQ,sBAAsB,SAAY;AACvG,aAAO,UAAU;AAAA,IACrB;AACA,QAAI,eAAe,QAAW;AAC1B,aAAO,eAAe;AAAA,IAC1B;AACA,WAAO;AAAA,EACX;AACA,EAAAA,YAAW,SAAS;AACpB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,aAAa,UAAU,SAAS,YAAY,GAAG,OAAO,UAAU,GAAG,MAAM,UAAU,YAAY,WAChG,UAAU,QAAQ,cAAc,UAAa,GAAG,QAAQ,UAAU,QAAQ,SAAS,OAAO,UAAU,QAAQ,sBAAsB,UAAa,GAAG,QAAQ,UAAU,QAAQ,iBAAiB,QAAS,UAAU,iBAAiB,UAAa,2BAA2B,GAAG,UAAU,YAAY;AAAA,EAC5S;AACA,EAAAA,YAAW,KAAK;AACpB,GAAG,eAAe,aAAa,CAAC,EAAE;AAC3B,IAAI;AAAA,CACV,SAAUC,gBAAe;AACtB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,cACF,UAAU,YAAY,UAAa,UAAU,oBAAoB,YACjE,UAAU,oBAAoB,UAAa,UAAU,gBAAgB,MAAM,SAAU,QAAQ;AAC1F,UAAI,GAAG,OAAO,OAAO,IAAI,GAAG;AACxB,eAAO,WAAW,GAAG,MAAM,KAAK,WAAW,GAAG,MAAM,KAAK,WAAW,GAAG,MAAM;AAAA,MACjF,OACK;AACD,eAAO,iBAAiB,GAAG,MAAM;AAAA,MACrC;AAAA,IACJ,CAAC;AAAA,EACT;AACA,EAAAA,eAAc,KAAK;AACvB,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AACxC,IAAI;AAAA;AAAA,EAAoC,WAAY;AAChD,aAASC,oBAAmB,OAAO,mBAAmB;AAClD,WAAK,QAAQ;AACb,WAAK,oBAAoB;AAAA,IAC7B;AACA,IAAAA,oBAAmB,UAAU,SAAS,SAAU,UAAU,SAAS,YAAY;AAC3E,UAAI;AACJ,UAAI;AACJ,UAAI,eAAe,QAAW;AAC1B,eAAO,SAAS,OAAO,UAAU,OAAO;AAAA,MAC5C,WACS,2BAA2B,GAAG,UAAU,GAAG;AAChD,aAAK;AACL,eAAO,kBAAkB,OAAO,UAAU,SAAS,UAAU;AAAA,MACjE,OACK;AACD,aAAK,wBAAwB,KAAK,iBAAiB;AACnD,aAAK,KAAK,kBAAkB,OAAO,UAAU;AAC7C,eAAO,kBAAkB,OAAO,UAAU,SAAS,EAAE;AAAA,MACzD;AACA,WAAK,MAAM,KAAK,IAAI;AACpB,UAAI,OAAO,QAAW;AAClB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,IAAAA,oBAAmB,UAAU,UAAU,SAAU,OAAO,SAAS,YAAY;AACzE,UAAI;AACJ,UAAI;AACJ,UAAI,eAAe,QAAW;AAC1B,eAAO,SAAS,QAAQ,OAAO,OAAO;AAAA,MAC1C,WACS,2BAA2B,GAAG,UAAU,GAAG;AAChD,aAAK;AACL,eAAO,kBAAkB,QAAQ,OAAO,SAAS,UAAU;AAAA,MAC/D,OACK;AACD,aAAK,wBAAwB,KAAK,iBAAiB;AACnD,aAAK,KAAK,kBAAkB,OAAO,UAAU;AAC7C,eAAO,kBAAkB,QAAQ,OAAO,SAAS,EAAE;AAAA,MACvD;AACA,WAAK,MAAM,KAAK,IAAI;AACpB,UAAI,OAAO,QAAW;AAClB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,IAAAA,oBAAmB,UAAU,SAAS,SAAU,OAAO,YAAY;AAC/D,UAAI;AACJ,UAAI;AACJ,UAAI,eAAe,QAAW;AAC1B,eAAO,SAAS,IAAI,KAAK;AAAA,MAC7B,WACS,2BAA2B,GAAG,UAAU,GAAG;AAChD,aAAK;AACL,eAAO,kBAAkB,IAAI,OAAO,UAAU;AAAA,MAClD,OACK;AACD,aAAK,wBAAwB,KAAK,iBAAiB;AACnD,aAAK,KAAK,kBAAkB,OAAO,UAAU;AAC7C,eAAO,kBAAkB,IAAI,OAAO,EAAE;AAAA,MAC1C;AACA,WAAK,MAAM,KAAK,IAAI;AACpB,UAAI,OAAO,QAAW;AAClB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,IAAAA,oBAAmB,UAAU,MAAM,SAAU,MAAM;AAC/C,WAAK,MAAM,KAAK,IAAI;AAAA,IACxB;AACA,IAAAA,oBAAmB,UAAU,MAAM,WAAY;AAC3C,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,oBAAmB,UAAU,QAAQ,WAAY;AAC7C,WAAK,MAAM,OAAO,GAAG,KAAK,MAAM,MAAM;AAAA,IAC1C;AACA,IAAAA,oBAAmB,UAAU,0BAA0B,SAAU,OAAO;AACpE,UAAI,UAAU,QAAW;AACrB,cAAM,IAAI,MAAM,kEAAkE;AAAA,MACtF;AAAA,IACJ;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAIF,IAAI;AAAA;AAAA,EAAmC,WAAY;AAC/C,aAASC,mBAAkB,aAAa;AACpC,WAAK,eAAe,gBAAgB,SAAY,uBAAO,OAAO,IAAI,IAAI;AACtE,WAAK,WAAW;AAChB,WAAK,QAAQ;AAAA,IACjB;AACA,IAAAA,mBAAkB,UAAU,MAAM,WAAY;AAC1C,aAAO,KAAK;AAAA,IAChB;AACA,WAAO,eAAeA,mBAAkB,WAAW,QAAQ;AAAA,MACvD,KAAK,WAAY;AACb,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,mBAAkB,UAAU,SAAS,SAAU,gBAAgB,YAAY;AACvE,UAAI;AACJ,UAAI,2BAA2B,GAAG,cAAc,GAAG;AAC/C,aAAK;AAAA,MACT,OACK;AACD,aAAK,KAAK,OAAO;AACjB,qBAAa;AAAA,MACjB;AACA,UAAI,KAAK,aAAa,EAAE,MAAM,QAAW;AACrC,cAAM,IAAI,MAAM,QAAQ,KAAK,qBAAqB;AAAA,MACtD;AACA,UAAI,eAAe,QAAW;AAC1B,cAAM,IAAI,MAAM,mCAAmC,EAAE;AAAA,MACzD;AACA,WAAK,aAAa,EAAE,IAAI;AACxB,WAAK;AACL,aAAO;AAAA,IACX;AACA,IAAAA,mBAAkB,UAAU,SAAS,WAAY;AAC7C,WAAK;AACL,aAAO,KAAK,SAAS,SAAS;AAAA,IAClC;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAIF,IAAI;AAAA;AAAA,EAAiC,WAAY;AAC7C,aAASC,iBAAgB,eAAe;AACpC,UAAI,QAAQ;AACZ,WAAK,mBAAmB,uBAAO,OAAO,IAAI;AAC1C,UAAI,kBAAkB,QAAW;AAC7B,aAAK,iBAAiB;AACtB,YAAI,cAAc,iBAAiB;AAC/B,eAAK,qBAAqB,IAAI,kBAAkB,cAAc,iBAAiB;AAC/E,wBAAc,oBAAoB,KAAK,mBAAmB,IAAI;AAC9D,wBAAc,gBAAgB,QAAQ,SAAU,QAAQ;AACpD,gBAAI,iBAAiB,GAAG,MAAM,GAAG;AAC7B,kBAAI,iBAAiB,IAAI,mBAAmB,OAAO,OAAO,MAAM,kBAAkB;AAClF,oBAAM,iBAAiB,OAAO,aAAa,GAAG,IAAI;AAAA,YACtD;AAAA,UACJ,CAAC;AAAA,QACL,WACS,cAAc,SAAS;AAC5B,iBAAO,KAAK,cAAc,OAAO,EAAE,QAAQ,SAAU,KAAK;AACtD,gBAAI,iBAAiB,IAAI,mBAAmB,cAAc,QAAQ,GAAG,CAAC;AACtE,kBAAM,iBAAiB,GAAG,IAAI;AAAA,UAClC,CAAC;AAAA,QACL;AAAA,MACJ,OACK;AACD,aAAK,iBAAiB,CAAC;AAAA,MAC3B;AAAA,IACJ;AACA,WAAO,eAAeA,iBAAgB,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAKrD,KAAK,WAAY;AACb,aAAK,oBAAoB;AACzB,YAAI,KAAK,uBAAuB,QAAW;AACvC,cAAI,KAAK,mBAAmB,SAAS,GAAG;AACpC,iBAAK,eAAe,oBAAoB;AAAA,UAC5C,OACK;AACD,iBAAK,eAAe,oBAAoB,KAAK,mBAAmB,IAAI;AAAA,UACxE;AAAA,QACJ;AACA,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,iBAAgB,UAAU,oBAAoB,SAAU,KAAK;AACzD,UAAI,wCAAwC,GAAG,GAAG,GAAG;AACjD,aAAK,oBAAoB;AACzB,YAAI,KAAK,eAAe,oBAAoB,QAAW;AACnD,gBAAM,IAAI,MAAM,wDAAwD;AAAA,QAC5E;AACA,YAAI,eAAe,EAAE,KAAK,IAAI,KAAK,SAAS,IAAI,QAAQ;AACxD,YAAI,SAAS,KAAK,iBAAiB,aAAa,GAAG;AACnD,YAAI,CAAC,QAAQ;AACT,cAAI,QAAQ,CAAC;AACb,cAAI,mBAAmB;AAAA,YACnB;AAAA,YACA;AAAA,UACJ;AACA,eAAK,eAAe,gBAAgB,KAAK,gBAAgB;AACzD,mBAAS,IAAI,mBAAmB,OAAO,KAAK,kBAAkB;AAC9D,eAAK,iBAAiB,aAAa,GAAG,IAAI;AAAA,QAC9C;AACA,eAAO;AAAA,MACX,OACK;AACD,aAAK,YAAY;AACjB,YAAI,KAAK,eAAe,YAAY,QAAW;AAC3C,gBAAM,IAAI,MAAM,gEAAgE;AAAA,QACpF;AACA,YAAI,SAAS,KAAK,iBAAiB,GAAG;AACtC,YAAI,CAAC,QAAQ;AACT,cAAI,QAAQ,CAAC;AACb,eAAK,eAAe,QAAQ,GAAG,IAAI;AACnC,mBAAS,IAAI,mBAAmB,KAAK;AACrC,eAAK,iBAAiB,GAAG,IAAI;AAAA,QACjC;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AACA,IAAAA,iBAAgB,UAAU,sBAAsB,WAAY;AACxD,UAAI,KAAK,eAAe,oBAAoB,UAAa,KAAK,eAAe,YAAY,QAAW;AAChG,aAAK,qBAAqB,IAAI,kBAAkB;AAChD,aAAK,eAAe,kBAAkB,CAAC;AACvC,aAAK,eAAe,oBAAoB,KAAK,mBAAmB,IAAI;AAAA,MACxE;AAAA,IACJ;AACA,IAAAA,iBAAgB,UAAU,cAAc,WAAY;AAChD,UAAI,KAAK,eAAe,oBAAoB,UAAa,KAAK,eAAe,YAAY,QAAW;AAChG,aAAK,eAAe,UAAU,uBAAO,OAAO,IAAI;AAAA,MACpD;AAAA,IACJ;AACA,IAAAA,iBAAgB,UAAU,aAAa,SAAU,KAAK,qBAAqB,SAAS;AAChF,WAAK,oBAAoB;AACzB,UAAI,KAAK,eAAe,oBAAoB,QAAW;AACnD,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC5E;AACA,UAAI;AACJ,UAAI,iBAAiB,GAAG,mBAAmB,KAAK,2BAA2B,GAAG,mBAAmB,GAAG;AAChG,qBAAa;AAAA,MACjB,OACK;AACD,kBAAU;AAAA,MACd;AACA,UAAI;AACJ,UAAI;AACJ,UAAI,eAAe,QAAW;AAC1B,oBAAY,WAAW,OAAO,KAAK,OAAO;AAAA,MAC9C,OACK;AACD,aAAK,2BAA2B,GAAG,UAAU,IAAI,aAAa,KAAK,mBAAmB,OAAO,UAAU;AACvG,oBAAY,WAAW,OAAO,KAAK,SAAS,EAAE;AAAA,MAClD;AACA,WAAK,eAAe,gBAAgB,KAAK,SAAS;AAClD,UAAI,OAAO,QAAW;AAClB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,IAAAA,iBAAgB,UAAU,aAAa,SAAU,QAAQ,QAAQ,qBAAqB,SAAS;AAC3F,WAAK,oBAAoB;AACzB,UAAI,KAAK,eAAe,oBAAoB,QAAW;AACnD,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC5E;AACA,UAAI;AACJ,UAAI,iBAAiB,GAAG,mBAAmB,KAAK,2BAA2B,GAAG,mBAAmB,GAAG;AAChG,qBAAa;AAAA,MACjB,OACK;AACD,kBAAU;AAAA,MACd;AACA,UAAI;AACJ,UAAI;AACJ,UAAI,eAAe,QAAW;AAC1B,oBAAY,WAAW,OAAO,QAAQ,QAAQ,OAAO;AAAA,MACzD,OACK;AACD,aAAK,2BAA2B,GAAG,UAAU,IAAI,aAAa,KAAK,mBAAmB,OAAO,UAAU;AACvG,oBAAY,WAAW,OAAO,QAAQ,QAAQ,SAAS,EAAE;AAAA,MAC7D;AACA,WAAK,eAAe,gBAAgB,KAAK,SAAS;AAClD,UAAI,OAAO,QAAW;AAClB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,IAAAA,iBAAgB,UAAU,aAAa,SAAU,KAAK,qBAAqB,SAAS;AAChF,WAAK,oBAAoB;AACzB,UAAI,KAAK,eAAe,oBAAoB,QAAW;AACnD,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC5E;AACA,UAAI;AACJ,UAAI,iBAAiB,GAAG,mBAAmB,KAAK,2BAA2B,GAAG,mBAAmB,GAAG;AAChG,qBAAa;AAAA,MACjB,OACK;AACD,kBAAU;AAAA,MACd;AACA,UAAI;AACJ,UAAI;AACJ,UAAI,eAAe,QAAW;AAC1B,oBAAY,WAAW,OAAO,KAAK,OAAO;AAAA,MAC9C,OACK;AACD,aAAK,2BAA2B,GAAG,UAAU,IAAI,aAAa,KAAK,mBAAmB,OAAO,UAAU;AACvG,oBAAY,WAAW,OAAO,KAAK,SAAS,EAAE;AAAA,MAClD;AACA,WAAK,eAAe,gBAAgB,KAAK,SAAS;AAClD,UAAI,OAAO,QAAW;AAClB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAMK,IAAI;AAAA,CACV,SAAUC,yBAAwB;AAK/B,WAAS,OAAO,KAAK;AACjB,WAAO,EAAE,IAAS;AAAA,EACtB;AACA,EAAAA,wBAAuB,SAAS;AAIhC,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG;AAAA,EAC3D;AACA,EAAAA,wBAAuB,KAAK;AAChC,GAAG,2BAA2B,yBAAyB,CAAC,EAAE;AAKnD,IAAI;AAAA,CACV,SAAUC,kCAAiC;AAMxC,WAAS,OAAO,KAAK,SAAS;AAC1B,WAAO,EAAE,KAAU,QAAiB;AAAA,EACxC;AACA,EAAAA,iCAAgC,SAAS;AAIzC,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,KAAK,GAAG,QAAQ,UAAU,OAAO;AAAA,EAC5F;AACA,EAAAA,iCAAgC,KAAK;AACzC,GAAG,oCAAoC,kCAAkC,CAAC,EAAE;AAKrE,IAAI;AAAA,CACV,SAAUC,0CAAyC;AAMhD,WAAS,OAAO,KAAK,SAAS;AAC1B,WAAO,EAAE,KAAU,QAAiB;AAAA,EACxC;AACA,EAAAA,yCAAwC,SAAS;AAIjD,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,MAAM,UAAU,YAAY,QAAQ,GAAG,QAAQ,UAAU,OAAO;AAAA,EAC3H;AACA,EAAAA,yCAAwC,KAAK;AACjD,GAAG,4CAA4C,0CAA0C,CAAC,EAAE;AAKrF,IAAI;AAAA,CACV,SAAUC,mBAAkB;AAQzB,WAAS,OAAO,KAAK,YAAY,SAAS,MAAM;AAC5C,WAAO,EAAE,KAAU,YAAwB,SAAkB,KAAW;AAAA,EAC5E;AACA,EAAAA,kBAAiB,SAAS;AAI1B,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,KAAK,GAAG,OAAO,UAAU,UAAU,KAAK,GAAG,QAAQ,UAAU,OAAO,KAAK,GAAG,OAAO,UAAU,IAAI;AAAA,EAC5J;AACA,EAAAA,kBAAiB,KAAK;AAC1B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAQvC,IAAI;AAAA,CACV,SAAUC,aAAY;AAInB,EAAAA,YAAW,YAAY;AAIvB,EAAAA,YAAW,WAAW;AAC1B,GAAG,eAAe,aAAa,CAAC,EAAE;AAAA,CACjC,SAAUA,aAAY;AAInB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,cAAcA,YAAW,aAAa,cAAcA,YAAW;AAAA,EAC1E;AACA,EAAAA,YAAW,KAAK;AACpB,GAAG,eAAe,aAAa,CAAC,EAAE;AAC3B,IAAI;AAAA,CACV,SAAUC,gBAAe;AAItB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,cAAc,KAAK,KAAK,WAAW,GAAG,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,KAAK;AAAA,EAChG;AACA,EAAAA,eAAc,KAAK;AACvB,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAIjC,IAAI;AAAA,CACV,SAAUC,qBAAoB;AAC3B,EAAAA,oBAAmB,OAAO;AAC1B,EAAAA,oBAAmB,SAAS;AAC5B,EAAAA,oBAAmB,WAAW;AAC9B,EAAAA,oBAAmB,cAAc;AACjC,EAAAA,oBAAmB,QAAQ;AAC3B,EAAAA,oBAAmB,WAAW;AAC9B,EAAAA,oBAAmB,QAAQ;AAC3B,EAAAA,oBAAmB,YAAY;AAC/B,EAAAA,oBAAmB,SAAS;AAC5B,EAAAA,oBAAmB,WAAW;AAC9B,EAAAA,oBAAmB,OAAO;AAC1B,EAAAA,oBAAmB,QAAQ;AAC3B,EAAAA,oBAAmB,OAAO;AAC1B,EAAAA,oBAAmB,UAAU;AAC7B,EAAAA,oBAAmB,UAAU;AAC7B,EAAAA,oBAAmB,QAAQ;AAC3B,EAAAA,oBAAmB,OAAO;AAC1B,EAAAA,oBAAmB,YAAY;AAC/B,EAAAA,oBAAmB,SAAS;AAC5B,EAAAA,oBAAmB,aAAa;AAChC,EAAAA,oBAAmB,WAAW;AAC9B,EAAAA,oBAAmB,SAAS;AAC5B,EAAAA,oBAAmB,QAAQ;AAC3B,EAAAA,oBAAmB,WAAW;AAC9B,EAAAA,oBAAmB,gBAAgB;AACvC,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAK3C,IAAI;AAAA,CACV,SAAUC,mBAAkB;AAIzB,EAAAA,kBAAiB,YAAY;AAW7B,EAAAA,kBAAiB,UAAU;AAC/B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAOvC,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAI1B,EAAAA,mBAAkB,aAAa;AACnC,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAMzC,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAI1B,WAAS,OAAO,SAAS,QAAQ,SAAS;AACtC,WAAO,EAAE,SAAkB,QAAgB,QAAiB;AAAA,EAChE;AACA,EAAAA,mBAAkB,SAAS;AAI3B,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,aAAa,GAAG,OAAO,UAAU,OAAO,KAAKC,OAAM,GAAG,UAAU,MAAM,KAAKA,OAAM,GAAG,UAAU,OAAO;AAAA,EAChH;AACA,EAAAD,mBAAkB,KAAK;AAC3B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAOzC,IAAI;AAAA,CACV,SAAUE,iBAAgB;AAQvB,EAAAA,gBAAe,OAAO;AAUtB,EAAAA,gBAAe,oBAAoB;AACvC,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAKnC,IAAI;AAAA,CACV,SAAUC,iBAAgB;AAKvB,WAAS,OAAO,OAAO;AACnB,WAAO,EAAE,MAAa;AAAA,EAC1B;AACA,EAAAA,gBAAe,SAAS;AAC5B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAKnC,IAAI;AAAA,CACV,SAAUC,iBAAgB;AAOvB,WAAS,OAAO,OAAO,cAAc;AACjC,WAAO,EAAE,OAAO,QAAQ,QAAQ,CAAC,GAAG,cAAc,CAAC,CAAC,aAAa;AAAA,EACrE;AACA,EAAAA,gBAAe,SAAS;AAC5B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AACnC,IAAI;AAAA,CACV,SAAUC,eAAc;AAMrB,WAAS,cAAc,WAAW;AAC9B,WAAO,UAAU,QAAQ,yBAAyB,MAAM;AAAA,EAC5D;AACA,EAAAA,cAAa,gBAAgB;AAI7B,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,OAAO,SAAS,KAAM,GAAG,cAAc,SAAS,KAAK,GAAG,OAAO,UAAU,QAAQ,KAAK,GAAG,OAAO,UAAU,KAAK;AAAA,EAC7H;AACA,EAAAA,cAAa,KAAK;AACtB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAC/B,IAAI;AAAA,CACV,SAAUC,QAAO;AAId,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,CAAC,CAAC,aAAa,GAAG,cAAc,SAAS,MAAM,cAAc,GAAG,UAAU,QAAQ,KACrF,aAAa,GAAG,UAAU,QAAQ,KAClC,GAAG,WAAW,UAAU,UAAU,aAAa,EAAE,OAAO,MAAM,UAAU,UAAaL,OAAM,GAAG,MAAM,KAAK;AAAA,EACjH;AACA,EAAAK,OAAM,KAAK;AACf,GAAG,UAAU,QAAQ,CAAC,EAAE;AAKjB,IAAI;AAAA,CACV,SAAUC,uBAAsB;AAO7B,WAAS,OAAO,OAAO,eAAe;AAClC,WAAO,gBAAgB,EAAE,OAAc,cAA6B,IAAI,EAAE,MAAa;AAAA,EAC3F;AACA,EAAAA,sBAAqB,SAAS;AAClC,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAK/C,IAAI;AAAA,CACV,SAAUC,uBAAsB;AAC7B,WAAS,OAAO,OAAO,eAAe;AAClC,QAAI,aAAa,CAAC;AAClB,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,iBAAW,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,IACrC;AACA,QAAI,SAAS,EAAE,MAAa;AAC5B,QAAI,GAAG,QAAQ,aAAa,GAAG;AAC3B,aAAO,gBAAgB;AAAA,IAC3B;AACA,QAAI,GAAG,QAAQ,UAAU,GAAG;AACxB,aAAO,aAAa;AAAA,IACxB,OACK;AACD,aAAO,aAAa,CAAC;AAAA,IACzB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,sBAAqB,SAAS;AAClC,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAI/C,IAAI;AAAA,CACV,SAAUC,wBAAuB;AAI9B,EAAAA,uBAAsB,OAAO;AAI7B,EAAAA,uBAAsB,OAAO;AAI7B,EAAAA,uBAAsB,QAAQ;AAClC,GAAG,0BAA0B,wBAAwB,CAAC,EAAE;AAKjD,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAK1B,WAAS,OAAO,OAAO,MAAM;AACzB,QAAI,SAAS,EAAE,MAAa;AAC5B,QAAI,GAAG,OAAO,IAAI,GAAG;AACjB,aAAO,OAAO;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,mBAAkB,SAAS;AAC/B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAIzC,IAAI;AAAA,CACV,SAAUC,aAAY;AACnB,EAAAA,YAAW,OAAO;AAClB,EAAAA,YAAW,SAAS;AACpB,EAAAA,YAAW,YAAY;AACvB,EAAAA,YAAW,UAAU;AACrB,EAAAA,YAAW,QAAQ;AACnB,EAAAA,YAAW,SAAS;AACpB,EAAAA,YAAW,WAAW;AACtB,EAAAA,YAAW,QAAQ;AACnB,EAAAA,YAAW,cAAc;AACzB,EAAAA,YAAW,OAAO;AAClB,EAAAA,YAAW,YAAY;AACvB,EAAAA,YAAW,WAAW;AACtB,EAAAA,YAAW,WAAW;AACtB,EAAAA,YAAW,WAAW;AACtB,EAAAA,YAAW,SAAS;AACpB,EAAAA,YAAW,SAAS;AACpB,EAAAA,YAAW,UAAU;AACrB,EAAAA,YAAW,QAAQ;AACnB,EAAAA,YAAW,SAAS;AACpB,EAAAA,YAAW,MAAM;AACjB,EAAAA,YAAW,OAAO;AAClB,EAAAA,YAAW,aAAa;AACxB,EAAAA,YAAW,SAAS;AACpB,EAAAA,YAAW,QAAQ;AACnB,EAAAA,YAAW,WAAW;AACtB,EAAAA,YAAW,gBAAgB;AAC/B,GAAG,eAAe,aAAa,CAAC,EAAE;AAK3B,IAAI;AAAA,CACV,SAAUC,YAAW;AAIlB,EAAAA,WAAU,aAAa;AAC3B,GAAG,cAAc,YAAY,CAAC,EAAE;AACzB,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAU1B,WAAS,OAAO,MAAM,MAAM,OAAO,KAAK,eAAe;AACnD,QAAI,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA,UAAU,EAAE,KAAU,MAAa;AAAA,IACvC;AACA,QAAI,eAAe;AACf,aAAO,gBAAgB;AAAA,IAC3B;AACA,WAAO;AAAA,EACX;AACA,EAAAA,mBAAkB,SAAS;AAC/B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AACzC,IAAI;AAAA,CACV,SAAUC,iBAAgB;AAWvB,WAAS,OAAO,MAAM,QAAQ,MAAM,OAAO,gBAAgB,UAAU;AACjE,QAAI,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,QAAI,aAAa,QAAW;AACxB,aAAO,WAAW;AAAA,IACtB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,gBAAe,SAAS;AAIxB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,aACH,GAAG,OAAO,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,IAAI,KACrDb,OAAM,GAAG,UAAU,KAAK,KAAKA,OAAM,GAAG,UAAU,cAAc,MAC7D,UAAU,WAAW,UAAa,GAAG,OAAO,UAAU,MAAM,OAC5D,UAAU,eAAe,UAAa,GAAG,QAAQ,UAAU,UAAU,OACrE,UAAU,aAAa,UAAa,MAAM,QAAQ,UAAU,QAAQ,OACpE,UAAU,SAAS,UAAa,MAAM,QAAQ,UAAU,IAAI;AAAA,EACrE;AACA,EAAAa,gBAAe,KAAK;AACxB,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAInC,IAAI;AAAA,CACV,SAAUC,iBAAgB;AAIvB,EAAAA,gBAAe,QAAQ;AAIvB,EAAAA,gBAAe,WAAW;AAI1B,EAAAA,gBAAe,WAAW;AAY1B,EAAAA,gBAAe,kBAAkB;AAWjC,EAAAA,gBAAe,iBAAiB;AAahC,EAAAA,gBAAe,kBAAkB;AAMjC,EAAAA,gBAAe,SAAS;AAIxB,EAAAA,gBAAe,wBAAwB;AASvC,EAAAA,gBAAe,eAAe;AAClC,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAKnC,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAI1B,WAAS,OAAO,aAAa,MAAM;AAC/B,QAAI,SAAS,EAAE,YAAyB;AACxC,QAAI,SAAS,UAAa,SAAS,MAAM;AACrC,aAAO,OAAO;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,mBAAkB,SAAS;AAI3B,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,WAAW,UAAU,aAAa,WAAW,EAAE,MAAM,UAAU,SAAS,UAAa,GAAG,WAAW,UAAU,MAAM,GAAG,MAAM;AAAA,EACnK;AACA,EAAAA,mBAAkB,KAAK;AAC3B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AACzC,IAAI;AAAA,CACV,SAAUC,aAAY;AACnB,WAAS,OAAO,OAAO,qBAAqB,MAAM;AAC9C,QAAI,SAAS,EAAE,MAAa;AAC5B,QAAI,YAAY;AAChB,QAAI,OAAO,wBAAwB,UAAU;AACzC,kBAAY;AACZ,aAAO,OAAO;AAAA,IAClB,WACS,QAAQ,GAAG,mBAAmB,GAAG;AACtC,aAAO,UAAU;AAAA,IACrB,OACK;AACD,aAAO,OAAO;AAAA,IAClB;AACA,QAAI,aAAa,SAAS,QAAW;AACjC,aAAO,OAAO;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,YAAW,SAAS;AACpB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,aAAa,GAAG,OAAO,UAAU,KAAK,MACxC,UAAU,gBAAgB,UAAa,GAAG,WAAW,UAAU,aAAa,WAAW,EAAE,OACzF,UAAU,SAAS,UAAa,GAAG,OAAO,UAAU,IAAI,OACxD,UAAU,SAAS,UAAa,UAAU,YAAY,YACtD,UAAU,YAAY,UAAa,QAAQ,GAAG,UAAU,OAAO,OAC/D,UAAU,gBAAgB,UAAa,GAAG,QAAQ,UAAU,WAAW,OACvE,UAAU,SAAS,UAAa,cAAc,GAAG,UAAU,IAAI;AAAA,EACxE;AACA,EAAAA,YAAW,KAAK;AACpB,GAAG,eAAe,aAAa,CAAC,EAAE;AAK3B,IAAI;AAAA,CACV,SAAUC,WAAU;AAIjB,WAAS,OAAO,OAAO,MAAM;AACzB,QAAI,SAAS,EAAE,MAAa;AAC5B,QAAI,GAAG,QAAQ,IAAI,GAAG;AAClB,aAAO,OAAO;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,UAAS,SAAS;AAIlB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAKjB,OAAM,GAAG,UAAU,KAAK,MAAM,GAAG,UAAU,UAAU,OAAO,KAAK,QAAQ,GAAG,UAAU,OAAO;AAAA,EACjI;AACA,EAAAiB,UAAS,KAAK;AAClB,GAAG,aAAa,WAAW,CAAC,EAAE;AAKvB,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAI1B,WAAS,OAAO,SAAS,cAAc;AACnC,WAAO,EAAE,SAAkB,aAA2B;AAAA,EAC1D;AACA,EAAAA,mBAAkB,SAAS;AAI3B,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,SAAS,UAAU,OAAO,KAAK,GAAG,QAAQ,UAAU,YAAY;AAAA,EACvG;AACA,EAAAA,mBAAkB,KAAK;AAC3B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAKzC,IAAI;AAAA,CACV,SAAUC,eAAc;AAIrB,WAAS,OAAO,OAAO,QAAQ,MAAM;AACjC,WAAO,EAAE,OAAc,QAAgB,KAAW;AAAA,EACtD;AACA,EAAAA,cAAa,SAAS;AAItB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAKnB,OAAM,GAAG,UAAU,KAAK,MAAM,GAAG,UAAU,UAAU,MAAM,KAAK,GAAG,OAAO,UAAU,MAAM;AAAA,EAC9H;AACA,EAAAmB,cAAa,KAAK;AACtB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAK/B,IAAI;AAAA,CACV,SAAUC,iBAAgB;AAMvB,WAAS,OAAO,OAAO,QAAQ;AAC3B,WAAO,EAAE,OAAc,OAAe;AAAA,EAC1C;AACA,EAAAA,gBAAe,SAAS;AACxB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,cAAc,UAAapB,OAAM,GAAG,UAAU,KAAK,MAAM,UAAU,WAAW,UAAaoB,gBAAe,GAAG,UAAU,MAAM;AAAA,EACxI;AACA,EAAAA,gBAAe,KAAK;AACxB,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAKnC,IAAI;AAAA,CACV,SAAUC,eAAc;AAOrB,WAAS,OAAO,KAAK,YAAY,SAAS,SAAS;AAC/C,WAAO,IAAI,iBAAiB,KAAK,YAAY,SAAS,OAAO;AAAA,EACjE;AACA,EAAAA,cAAa,SAAS;AAItB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,MAAM,GAAG,UAAU,UAAU,UAAU,KAAK,GAAG,OAAO,UAAU,UAAU,MAAM,GAAG,SAAS,UAAU,SAAS,KAC/J,GAAG,KAAK,UAAU,OAAO,KAAK,GAAG,KAAK,UAAU,UAAU,KAAK,GAAG,KAAK,UAAU,QAAQ,IAAI,OAAO;AAAA,EAC/G;AACA,EAAAA,cAAa,KAAK;AAClB,WAAS,WAAW,UAAU,OAAO;AACjC,QAAI,OAAO,SAAS,QAAQ;AAC5B,QAAI,cAAc,UAAU,OAAO,SAAU,GAAG,GAAG;AAC/C,UAAI,OAAO,EAAE,MAAM,MAAM,OAAO,EAAE,MAAM,MAAM;AAC9C,UAAI,SAAS,GAAG;AACZ,eAAO,EAAE,MAAM,MAAM,YAAY,EAAE,MAAM,MAAM;AAAA,MACnD;AACA,aAAO;AAAA,IACX,CAAC;AACD,QAAI,qBAAqB,KAAK;AAC9B,aAAS,IAAI,YAAY,SAAS,GAAG,KAAK,GAAG,KAAK;AAC9C,UAAI,IAAI,YAAY,CAAC;AACrB,UAAI,cAAc,SAAS,SAAS,EAAE,MAAM,KAAK;AACjD,UAAI,YAAY,SAAS,SAAS,EAAE,MAAM,GAAG;AAC7C,UAAI,aAAa,oBAAoB;AACjC,eAAO,KAAK,UAAU,GAAG,WAAW,IAAI,EAAE,UAAU,KAAK,UAAU,WAAW,KAAK,MAAM;AAAA,MAC7F,OACK;AACD,cAAM,IAAI,MAAM,kBAAkB;AAAA,MACtC;AACA,2BAAqB;AAAA,IACzB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,cAAa,aAAa;AAC1B,WAAS,UAAU,MAAM,SAAS;AAC9B,QAAI,KAAK,UAAU,GAAG;AAElB,aAAO;AAAA,IACX;AACA,QAAI,IAAK,KAAK,SAAS,IAAK;AAC5B,QAAI,OAAO,KAAK,MAAM,GAAG,CAAC;AAC1B,QAAI,QAAQ,KAAK,MAAM,CAAC;AACxB,cAAU,MAAM,OAAO;AACvB,cAAU,OAAO,OAAO;AACxB,QAAI,UAAU;AACd,QAAI,WAAW;AACf,QAAI,IAAI;AACR,WAAO,UAAU,KAAK,UAAU,WAAW,MAAM,QAAQ;AACrD,UAAI,MAAM,QAAQ,KAAK,OAAO,GAAG,MAAM,QAAQ,CAAC;AAChD,UAAI,OAAO,GAAG;AAEV,aAAK,GAAG,IAAI,KAAK,SAAS;AAAA,MAC9B,OACK;AAED,aAAK,GAAG,IAAI,MAAM,UAAU;AAAA,MAChC;AAAA,IACJ;AACA,WAAO,UAAU,KAAK,QAAQ;AAC1B,WAAK,GAAG,IAAI,KAAK,SAAS;AAAA,IAC9B;AACA,WAAO,WAAW,MAAM,QAAQ;AAC5B,WAAK,GAAG,IAAI,MAAM,UAAU;AAAA,IAChC;AACA,WAAO;AAAA,EACX;AACJ,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAItC,IAAI;AAAA;AAAA,EAAkC,WAAY;AAC9C,aAASC,kBAAiB,KAAK,YAAY,SAAS,SAAS;AACzD,WAAK,OAAO;AACZ,WAAK,cAAc;AACnB,WAAK,WAAW;AAChB,WAAK,WAAW;AAChB,WAAK,eAAe;AAAA,IACxB;AACA,WAAO,eAAeA,kBAAiB,WAAW,OAAO;AAAA,MACrD,KAAK,WAAY;AACb,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,kBAAiB,WAAW,cAAc;AAAA,MAC5D,KAAK,WAAY;AACb,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,kBAAiB,WAAW,WAAW;AAAA,MACzD,KAAK,WAAY;AACb,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,kBAAiB,UAAU,UAAU,SAAU,OAAO;AAClD,UAAI,OAAO;AACP,YAAI,QAAQ,KAAK,SAAS,MAAM,KAAK;AACrC,YAAI,MAAM,KAAK,SAAS,MAAM,GAAG;AACjC,eAAO,KAAK,SAAS,UAAU,OAAO,GAAG;AAAA,MAC7C;AACA,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,kBAAiB,UAAU,SAAS,SAAU,OAAO,SAAS;AAC1D,WAAK,WAAW,MAAM;AACtB,WAAK,WAAW;AAChB,WAAK,eAAe;AAAA,IACxB;AACA,IAAAA,kBAAiB,UAAU,iBAAiB,WAAY;AACpD,UAAI,KAAK,iBAAiB,QAAW;AACjC,YAAI,cAAc,CAAC;AACnB,YAAI,OAAO,KAAK;AAChB,YAAI,cAAc;AAClB,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,cAAI,aAAa;AACb,wBAAY,KAAK,CAAC;AAClB,0BAAc;AAAA,UAClB;AACA,cAAI,KAAK,KAAK,OAAO,CAAC;AACtB,wBAAe,OAAO,QAAQ,OAAO;AACrC,cAAI,OAAO,QAAQ,IAAI,IAAI,KAAK,UAAU,KAAK,OAAO,IAAI,CAAC,MAAM,MAAM;AACnE;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,eAAe,KAAK,SAAS,GAAG;AAChC,sBAAY,KAAK,KAAK,MAAM;AAAA,QAChC;AACA,aAAK,eAAe;AAAA,MACxB;AACA,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,kBAAiB,UAAU,aAAa,SAAU,QAAQ;AACtD,eAAS,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK,SAAS,MAAM,GAAG,CAAC;AAC3D,UAAI,cAAc,KAAK,eAAe;AACtC,UAAI,MAAM,GAAG,OAAO,YAAY;AAChC,UAAI,SAAS,GAAG;AACZ,eAAO,SAAS,OAAO,GAAG,MAAM;AAAA,MACpC;AACA,aAAO,MAAM,MAAM;AACf,YAAI,MAAM,KAAK,OAAO,MAAM,QAAQ,CAAC;AACrC,YAAI,YAAY,GAAG,IAAI,QAAQ;AAC3B,iBAAO;AAAA,QACX,OACK;AACD,gBAAM,MAAM;AAAA,QAChB;AAAA,MACJ;AAGA,UAAI,OAAO,MAAM;AACjB,aAAO,SAAS,OAAO,MAAM,SAAS,YAAY,IAAI,CAAC;AAAA,IAC3D;AACA,IAAAA,kBAAiB,UAAU,WAAW,SAAU,UAAU;AACtD,UAAI,cAAc,KAAK,eAAe;AACtC,UAAI,SAAS,QAAQ,YAAY,QAAQ;AACrC,eAAO,KAAK,SAAS;AAAA,MACzB,WACS,SAAS,OAAO,GAAG;AACxB,eAAO;AAAA,MACX;AACA,UAAI,aAAa,YAAY,SAAS,IAAI;AAC1C,UAAI,iBAAkB,SAAS,OAAO,IAAI,YAAY,SAAU,YAAY,SAAS,OAAO,CAAC,IAAI,KAAK,SAAS;AAC/G,aAAO,KAAK,IAAI,KAAK,IAAI,aAAa,SAAS,WAAW,cAAc,GAAG,UAAU;AAAA,IACzF;AACA,WAAO,eAAeA,kBAAiB,WAAW,aAAa;AAAA,MAC3D,KAAK,WAAY;AACb,eAAO,KAAK,eAAe,EAAE;AAAA,MACjC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,IAAI;AAAA,CACH,SAAUC,KAAI;AACX,MAAI,WAAW,OAAO,UAAU;AAChC,WAAS,QAAQ,OAAO;AACpB,WAAO,OAAO,UAAU;AAAA,EAC5B;AACA,EAAAA,IAAG,UAAU;AACb,WAASC,WAAU,OAAO;AACtB,WAAO,OAAO,UAAU;AAAA,EAC5B;AACA,EAAAD,IAAG,YAAYC;AACf,WAAS,QAAQ,OAAO;AACpB,WAAO,UAAU,QAAQ,UAAU;AAAA,EACvC;AACA,EAAAD,IAAG,UAAU;AACb,WAAS,OAAO,OAAO;AACnB,WAAO,SAAS,KAAK,KAAK,MAAM;AAAA,EACpC;AACA,EAAAA,IAAG,SAAS;AACZ,WAAS,OAAO,OAAO;AACnB,WAAO,SAAS,KAAK,KAAK,MAAM;AAAA,EACpC;AACA,EAAAA,IAAG,SAAS;AACZ,WAAS,YAAY,OAAO,KAAK,KAAK;AAClC,WAAO,SAAS,KAAK,KAAK,MAAM,qBAAqB,OAAO,SAAS,SAAS;AAAA,EAClF;AACA,EAAAA,IAAG,cAAc;AACjB,WAASE,SAAQ,OAAO;AACpB,WAAO,SAAS,KAAK,KAAK,MAAM,qBAAqB,eAAe,SAAS,SAAS;AAAA,EAC1F;AACA,EAAAF,IAAG,UAAUE;AACb,WAASC,UAAS,OAAO;AACrB,WAAO,SAAS,KAAK,KAAK,MAAM,qBAAqB,KAAK,SAAS,SAAS;AAAA,EAChF;AACA,EAAAH,IAAG,WAAWG;AACd,WAAS,KAAK,OAAO;AACjB,WAAO,SAAS,KAAK,KAAK,MAAM;AAAA,EACpC;AACA,EAAAH,IAAG,OAAO;AACV,WAAS,cAAc,OAAO;AAI1B,WAAO,UAAU,QAAQ,OAAO,UAAU;AAAA,EAC9C;AACA,EAAAA,IAAG,gBAAgB;AACnB,WAAS,WAAW,OAAO,OAAO;AAC9B,WAAO,MAAM,QAAQ,KAAK,KAAK,MAAM,MAAM,KAAK;AAAA,EACpD;AACA,EAAAA,IAAG,aAAa;AACpB,GAAG,OAAO,KAAK,CAAC,EAAE;;;AC52DlB,IAAI;AAAA;AAAA,EAAoC,WAAY;AAChD,aAASI,oBAAmB,aAAa,SAAS,UAAU;AACxD,UAAI,QAAQ;AACZ,WAAK,cAAc;AACnB,WAAK,UAAU;AACf,WAAK,eAAe,CAAC;AACrB,WAAK,YAAY,uBAAO,OAAO,IAAI;AACnC,UAAI,aAAa,SAAU,OAAO;AAC9B,YAAI,SAAS,MAAM,cAAc;AACjC,YAAI,WAAW,MAAM,aAAa;AAC9B;AAAA,QACJ;AACA,YAAI;AACJ,cAAM,UAAU,MAAM,IAAI,SAAS,CAAC,IAAI,MAAM,mBAAmB,WAAY;AACzE,iBAAO,aAAa,MAAM;AAC1B,mBAAS,OAAO,WAAW,WAAY;AAAE,mBAAO,MAAM,YAAY,MAAM,KAAK,MAAM;AAAA,UAAG,GAAG,GAAG;AAAA,QAChG,CAAC;AACD,cAAM,YAAY,MAAM,KAAK,MAAM;AAAA,MACvC;AACA,UAAI,iBAAiB,SAAU,OAAO;AAClC,eAAO,gBAAgB,OAAO,MAAM,aAAa,CAAC,CAAC;AACnD,YAAI,SAAS,MAAM,IAAI,SAAS;AAChC,YAAI,WAAW,MAAM,UAAU,MAAM;AACrC,YAAI,UAAU;AACV,mBAAS,QAAQ;AACjB,iBAAO,MAAM,UAAU,MAAM;AAAA,QACjC;AAAA,MACJ;AACA,WAAK,aAAa,KAAK,OAAO,iBAAiB,UAAU,CAAC;AAC1D,WAAK,aAAa,KAAK,OAAO,mBAAmB,cAAc,CAAC;AAChE,WAAK,aAAa,KAAK,OAAO,yBAAyB,SAAU,OAAO;AACpE,uBAAe,MAAM,KAAK;AAC1B,mBAAW,MAAM,KAAK;AAAA,MAC1B,CAAC,CAAC;AACF,eAAS,YAAY,SAAU,GAAG;AAC9B,eAAO,UAAU,EAAE,QAAQ,SAAU,OAAO;AACxC,cAAI,MAAM,cAAc,MAAM,MAAM,aAAa;AAC7C,2BAAe,KAAK;AACpB,uBAAW,KAAK;AAAA,UACpB;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AACD,WAAK,aAAa,KAAK;AAAA,QACnB,SAAS,WAAY;AACjB,mBAAS,OAAO,MAAM,WAAW;AAC7B,kBAAM,UAAU,GAAG,EAAE,QAAQ;AAAA,UACjC;AAAA,QACJ;AAAA,MACJ,CAAC;AACD,aAAO,UAAU,EAAE,QAAQ,UAAU;AAAA,IACzC;AACA,IAAAA,oBAAmB,UAAU,UAAU,WAAY;AAC/C,WAAK,aAAa,QAAQ,SAAU,GAAG;AAAE,eAAO,KAAK,EAAE,QAAQ;AAAA,MAAG,CAAC;AACnE,WAAK,eAAe,CAAC;AAAA,IACzB;AACA,IAAAA,oBAAmB,UAAU,cAAc,SAAU,UAAU,YAAY;AACvE,WAAK,QAAQ,QAAQ,EAChB,KAAK,SAAU,QAAQ;AACxB,eAAO,OAAO,aAAa,SAAS,SAAS,CAAC;AAAA,MAClD,CAAC,EACI,KAAK,SAAU,aAAa;AAC7B,YAAI,UAAU,YAAY,IAAI,SAAU,GAAG;AAAE,iBAAO,cAAc,UAAU,CAAC;AAAA,QAAG,CAAC;AACjF,YAAI,QAAQ,OAAO,SAAS,QAAQ;AACpC,YAAI,SAAS,MAAM,cAAc,MAAM,YAAY;AAC/C,iBAAO,gBAAgB,OAAO,YAAY,OAAO;AAAA,QACrD;AAAA,MACJ,CAAC,EACI,KAAK,QAAW,SAAU,KAAK;AAChC,gBAAQ,MAAM,GAAG;AAAA,MACrB,CAAC;AAAA,IACL;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,SAAS,WAAW,YAAY;AAC5B,UAAQ,YAAY;AAAA,IAChB,KAAa,mBAAmB;AAC5B,aAAO,eAAe;AAAA,IAC1B,KAAa,mBAAmB;AAC5B,aAAO,eAAe;AAAA,IAC1B,KAAa,mBAAmB;AAC5B,aAAO,eAAe;AAAA,IAC1B,KAAa,mBAAmB;AAC5B,aAAO,eAAe;AAAA,IAC1B;AACI,aAAO,eAAe;AAAA,EAC9B;AACJ;AACA,SAAS,cAAc,UAAU,MAAM;AACnC,MAAI,OAAO,OAAO,KAAK,SAAS,WAAW,OAAO,KAAK,IAAI,IAAI,KAAK;AACpE,SAAO;AAAA,IACH,UAAU,WAAW,KAAK,QAAQ;AAAA,IAClC,iBAAiB,KAAK,MAAM,MAAM,OAAO;AAAA,IACzC,aAAa,KAAK,MAAM,MAAM,YAAY;AAAA,IAC1C,eAAe,KAAK,MAAM,IAAI,OAAO;AAAA,IACrC,WAAW,KAAK,MAAM,IAAI,YAAY;AAAA,IACtC,SAAS,KAAK;AAAA,IACd;AAAA,IACA,QAAQ,KAAK;AAAA,EACjB;AACJ;AAEA,SAAS,aAAa,UAAU;AAC5B,MAAI,CAAC,UAAU;AACX,WAAO;AAAA,EACX;AACA,SAAO,EAAE,WAAW,SAAS,SAAS,GAAG,MAAM,SAAS,aAAa,EAAE;AAC3E;AACA,SAAS,UAAU,OAAO;AACtB,MAAI,CAAC,OAAO;AACR,WAAO;AAAA,EACX;AACA,SAAO;AAAA,IACH,OAAO;AAAA,MACH,MAAM,MAAM,kBAAkB;AAAA,MAC9B,WAAW,MAAM,cAAc;AAAA,IACnC;AAAA,IACA,KAAK,EAAE,MAAM,MAAM,gBAAgB,GAAG,WAAW,MAAM,YAAY,EAAE;AAAA,EACzE;AACJ;AACA,SAAS,QAAQ,OAAO;AACpB,MAAI,CAAC,OAAO;AACR,WAAO;AAAA,EACX;AACA,SAAO,IAAI,MAAM,MAAM,MAAM,OAAO,GAAG,MAAM,MAAM,YAAY,GAAG,MAAM,IAAI,OAAO,GAAG,MAAM,IAAI,YAAY,CAAC;AACjH;AACA,SAAS,oBAAoB,MAAM;AAC/B,SAAQ,OAAO,KAAK,WAAW,eAC3B,OAAO,KAAK,YAAY;AAChC;AACA,SAAS,qBAAqB,MAAM;AAChC,MAAI,YAAY,UAAU;AAC1B,UAAQ,MAAM;AAAA,IACV,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,EACzB;AACA,SAAO,UAAU;AACrB;AACA,SAAS,WAAW,UAAU;AAC1B,MAAI,CAAC,UAAU;AACX,WAAO;AAAA,EACX;AACA,SAAO;AAAA,IACH,OAAO,QAAQ,SAAS,KAAK;AAAA,IAC7B,MAAM,SAAS;AAAA,EACnB;AACJ;AACA,SAAS,UAAU,GAAG;AAClB,SAAO,KAAK,EAAE,YAAY,iCACpB,EAAE,IAAI,EAAE,SAAS,OAAO,EAAE,OAAO,WAAW,EAAE,UAAU,IACxD;AACV;AACA,IAAI;AAAA;AAAA,EAAmC,WAAY;AAC/C,aAASC,mBAAkB,SAAS;AAChC,WAAK,UAAU;AAAA,IACnB;AACA,WAAO,eAAeA,mBAAkB,WAAW,qBAAqB;AAAA,MACpE,KAAK,WAAY;AACb,eAAO,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,mBAAkB,UAAU,yBAAyB,SAAU,OAAO,UAAU,SAAS,OAAO;AAC5F,UAAI,WAAW,MAAM;AACrB,aAAO,KAAK,QAAQ,QAAQ,EACvB,KAAK,SAAU,QAAQ;AACxB,eAAO,OAAO,WAAW,SAAS,SAAS,GAAG,aAAa,QAAQ,CAAC;AAAA,MACxE,CAAC,EACI,KAAK,SAAU,MAAM;AACtB,YAAI,CAAC,MAAM;AACP;AAAA,QACJ;AACA,YAAI,WAAW,MAAM,qBAAqB,QAAQ;AAClD,YAAI,YAAY,IAAI,MAAM,SAAS,YAAY,SAAS,aAAa,SAAS,YAAY,SAAS,SAAS;AAC5G,YAAI,QAAQ,KAAK,MAAM,IAAI,SAAU,OAAO;AACxC,cAAI,OAAO;AAAA,YACP,OAAO,MAAM;AAAA,YACb,YAAY,MAAM,cAAc,MAAM;AAAA,YACtC,UAAU,MAAM;AAAA,YAChB,YAAY,MAAM;AAAA,YAClB,eAAe,MAAM;AAAA,YACrB,QAAQ,MAAM;AAAA,YACd,SAAS,UAAU,MAAM,OAAO;AAAA,YAChC,OAAO;AAAA,YACP,MAAM,qBAAqB,MAAM,IAAI;AAAA,UACzC;AACA,cAAI,MAAM,UAAU;AAChB,gBAAI,oBAAoB,MAAM,QAAQ,GAAG;AACrC,mBAAK,QAAQ;AAAA,gBACT,QAAQ,QAAQ,MAAM,SAAS,MAAM;AAAA,gBACrC,SAAS,QAAQ,MAAM,SAAS,OAAO;AAAA,cAC3C;AAAA,YACJ,OACK;AACD,mBAAK,QAAQ,QAAQ,MAAM,SAAS,KAAK;AAAA,YAC7C;AACA,iBAAK,aAAa,MAAM,SAAS;AAAA,UACrC;AACA,cAAI,MAAM,qBAAqB;AAC3B,iBAAK,sBAAsB,MAAM,oBAAoB,IAAI,UAAU;AAAA,UACvE;AACA,cAAI,MAAM,qBAA6B,iBAAiB,SAAS;AAC7D,iBAAK,kBAAkB,UAAU,6BAA6B;AAAA,UAClE;AACA,iBAAO;AAAA,QACX,CAAC;AACD,eAAO;AAAA,UACH,cAAc,KAAK;AAAA,UACnB,aAAa;AAAA,QACjB;AAAA,MACJ,CAAC;AAAA,IACL;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,SAAS,gBAAgB,OAAO;AAC5B,SAAQ,SAAS,OAAO,UAAU,YAAY,OAAO,MAAM,SAAS;AACxE;AACA,SAAS,iBAAiB,OAAO;AAC7B,MAAI,OAAO,UAAU,UAAU;AAC3B,WAAO;AAAA,MACH,OAAO;AAAA,IACX;AAAA,EACJ;AACA,MAAI,gBAAgB,KAAK,GAAG;AACxB,QAAI,MAAM,SAAS,aAAa;AAC5B,aAAO;AAAA,QACH,OAAO,MAAM,MAAM,QAAQ,yBAAyB,MAAM;AAAA,MAC9D;AAAA,IACJ;AACA,WAAO;AAAA,MACH,OAAO,MAAM;AAAA,IACjB;AAAA,EACJ;AACA,SAAO,EAAE,OAAO,QAAQ,MAAM,WAAW,OAAO,MAAM,QAAQ,UAAU;AAC5E;AACA,SAAS,oBAAoB,UAAU;AACnC,MAAI,CAAC,UAAU;AACX,WAAO;AAAA,EACX;AACA,MAAI,MAAM,QAAQ,QAAQ,GAAG;AACzB,WAAO,SAAS,IAAI,gBAAgB;AAAA,EACxC;AACA,SAAO,CAAC,iBAAiB,QAAQ,CAAC;AACtC;AAEA,IAAI;AAAA;AAAA,EAA8B,WAAY;AAC1C,aAASC,cAAa,SAAS;AAC3B,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,cAAa,UAAU,eAAe,SAAU,OAAO,UAAU,OAAO;AACpE,UAAI,WAAW,MAAM;AACrB,aAAO,KAAK,QAAQ,QAAQ,EACvB,KAAK,SAAU,QAAQ;AACxB,eAAO,OAAO,QAAQ,SAAS,SAAS,GAAG,aAAa,QAAQ,CAAC;AAAA,MACrE,CAAC,EACI,KAAK,SAAU,MAAM;AACtB,YAAI,CAAC,MAAM;AACP;AAAA,QACJ;AACA,eAAO;AAAA,UACH,OAAO,QAAQ,KAAK,KAAK;AAAA,UACzB,UAAU,oBAAoB,KAAK,QAAQ;AAAA,QAC/C;AAAA,MACJ,CAAC;AAAA,IACL;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAGF,SAAS,wBAAwB,MAAM;AACnC,UAAQ,MAAM;AAAA,IACV,KAAa,sBAAsB;AAC/B,aAAO,UAAU,sBAAsB;AAAA,IAC3C,KAAa,sBAAsB;AAC/B,aAAO,UAAU,sBAAsB;AAAA,IAC3C,KAAa,sBAAsB;AAC/B,aAAO,UAAU,sBAAsB;AAAA,EAC/C;AACA,SAAO,UAAU,sBAAsB;AAC3C;AACA,IAAI;AAAA;AAAA,EAA0C,WAAY;AACtD,aAASC,0BAAyB,SAAS;AACvC,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,0BAAyB,UAAU,4BAA4B,SAAU,OAAO,UAAU,OAAO;AAC7F,UAAI,WAAW,MAAM;AACrB,aAAO,KAAK,QAAQ,QAAQ,EACvB,KAAK,SAAU,QAAQ;AACxB,eAAO,OAAO,uBAAuB,SAAS,SAAS,GAAG,aAAa,QAAQ,CAAC;AAAA,MACpF,CAAC,EACI,KAAK,SAAU,SAAS;AACzB,YAAI,CAAC,SAAS;AACV;AAAA,QACJ;AACA,eAAO,QAAQ,IAAI,SAAU,OAAO;AAChC,iBAAO;AAAA,YACH,OAAO,QAAQ,MAAM,KAAK;AAAA,YAC1B,MAAM,wBAAwB,MAAM,IAAI;AAAA,UAC5C;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAGF,SAAS,WAAW,UAAU;AAC1B,SAAO;AAAA,IACH,KAAK,IAAI,MAAM,SAAS,GAAG;AAAA,IAC3B,OAAO,QAAQ,SAAS,KAAK;AAAA,EACjC;AACJ;AACA,IAAI;AAAA;AAAA,EAAmC,WAAY;AAC/C,aAASC,mBAAkB,SAAS;AAChC,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,mBAAkB,UAAU,oBAAoB,SAAU,OAAO,UAAU,OAAO;AAC9E,UAAI,WAAW,MAAM;AACrB,aAAO,KAAK,QAAQ,QAAQ,EACvB,KAAK,SAAU,QAAQ;AACxB,eAAO,OAAO,eAAe,SAAS,SAAS,GAAG,aAAa,QAAQ,CAAC;AAAA,MAC5E,CAAC,EACI,KAAK,SAAU,YAAY;AAC5B,YAAI,CAAC,YAAY;AACb;AAAA,QACJ;AACA,eAAO,CAAC,WAAW,UAAU,CAAC;AAAA,MAClC,CAAC;AAAA,IACL;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAGF,IAAI;AAAA;AAAA,EAAkC,WAAY;AAC9C,aAASC,kBAAiB,SAAS;AAC/B,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,kBAAiB,UAAU,oBAAoB,SAAU,OAAO,UAAU,SAAS,OAAO;AACtF,UAAI,WAAW,MAAM;AACrB,aAAO,KAAK,QAAQ,QAAQ,EACvB,KAAK,SAAU,QAAQ;AACxB,eAAO,OAAO,eAAe,SAAS,SAAS,GAAG,aAAa,QAAQ,CAAC;AAAA,MAC5E,CAAC,EACI,KAAK,SAAU,SAAS;AACzB,YAAI,CAAC,SAAS;AACV;AAAA,QACJ;AACA,eAAO,QAAQ,IAAI,UAAU;AAAA,MACjC,CAAC;AAAA,IACL;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAGF,SAAS,gBAAgB,MAAM;AAC3B,MAAI,CAAC,QAAQ,CAAC,KAAK,SAAS;AACxB,WAAO;AAAA,EACX;AACA,MAAI,gBAAgB,CAAC;AACrB,WAAS,OAAO,KAAK,SAAS;AAC1B,QAAI,OAAO,IAAI,MAAM,GAAG;AAExB,aAAS,KAAK,GAAG,KAAK,KAAK,QAAQ,GAAG,GAAG,KAAK,GAAG,QAAQ,MAAM;AAC3D,UAAI,IAAI,GAAG,EAAE;AACb,oBAAc,KAAK;AAAA,QACf,UAAU;AAAA,QACV,MAAM;AAAA,UACF,OAAO,QAAQ,EAAE,KAAK;AAAA,UACtB,MAAM,EAAE;AAAA,QACZ;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACA,SAAO;AAAA,IACH,OAAO;AAAA,EACX;AACJ;AACA,IAAI;AAAA;AAAA,EAA+B,WAAY;AAC3C,aAASC,eAAc,SAAS;AAC5B,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,eAAc,UAAU,qBAAqB,SAAU,OAAO,UAAU,SAAS,OAAO;AACpF,UAAI,WAAW,MAAM;AACrB,aAAO,KAAK,QAAQ,QAAQ,EACvB,KAAK,SAAU,QAAQ;AACxB,eAAO,OAAO,SAAS,SAAS,SAAS,GAAG,aAAa,QAAQ,GAAG,OAAO;AAAA,MAC/E,CAAC,EACI,KAAK,SAAU,MAAM;AACtB,eAAO,gBAAgB,IAAI;AAAA,MAC/B,CAAC;AAAA,IACL;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAGF,SAAS,aAAa,MAAM;AACxB,MAAI,QAAQ,UAAU;AACtB,UAAQ,MAAM;AAAA,IACV,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,EACrB;AACA,SAAO,MAAM;AACjB;AACA,IAAI;AAAA;AAAA,EAAuC,WAAY;AACnD,aAASC,uBAAsB,SAAS;AACpC,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,uBAAsB,UAAU,yBAAyB,SAAU,OAAO,OAAO;AAC7E,UAAI,WAAW,MAAM;AACrB,aAAO,KAAK,QAAQ,QAAQ,EACvB,KAAK,SAAU,QAAQ;AAAE,eAAO,OAAO,oBAAoB,SAAS,SAAS,CAAC;AAAA,MAAG,CAAC,EAClF,KAAK,SAAU,OAAO;AACvB,YAAI,CAAC,OAAO;AACR;AAAA,QACJ;AACA,eAAO,MAAM,IAAI,SAAU,MAAM;AAAE,iBAAQ;AAAA,YACvC,MAAM,KAAK;AAAA,YACX,QAAQ;AAAA,YACR,eAAe,KAAK;AAAA,YACpB,MAAM,aAAa,KAAK,IAAI;AAAA,YAC5B,MAAM,CAAC;AAAA,YACP,OAAO,QAAQ,KAAK,SAAS,KAAK;AAAA,YAClC,gBAAgB,QAAQ,KAAK,SAAS,KAAK;AAAA,UAC/C;AAAA,QAAI,CAAC;AAAA,MACT,CAAC;AAAA,IACL;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,IAAI;AAAA;AAAA,EAAsC,WAAY;AAClD,aAASC,sBAAqB,SAAS;AACnC,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,sBAAqB,UAAU,wBAAwB,SAAU,OAAO,OAAO;AAC3E,UAAI,WAAW,MAAM;AACrB,aAAO,KAAK,QAAQ,QAAQ,EACvB,KAAK,SAAU,QAAQ;AAAE,eAAO,OAAO,mBAAmB,SAAS,SAAS,CAAC;AAAA,MAAG,CAAC,EACjF,KAAK,SAAU,OAAO;AACvB,YAAI,CAAC,OAAO;AACR;AAAA,QACJ;AACA,eAAO,MAAM,IAAI,SAAU,MAAM;AAAE,iBAAQ;AAAA,YACvC,OAAO,KAAK;AAAA,YACZ,OAAO,QAAQ,KAAK,KAAK;AAAA,UAC7B;AAAA,QAAI,CAAC;AAAA,MACT,CAAC;AAAA,IACL;AACA,IAAAA,sBAAqB,UAAU,4BAA4B,SAAU,OAAO,MAAM,OAAO;AACrF,UAAI,WAAW,MAAM;AACrB,aAAO,KAAK,QAAQ,QAAQ,EACvB,KAAK,SAAU,QAAQ;AACxB,eAAO,OAAO,sBAAsB,SAAS,SAAS,GAAG,KAAK,OAAO,UAAU,KAAK,KAAK,CAAC;AAAA,MAC9F,CAAC,EACI,KAAK,SAAU,eAAe;AAC/B,YAAI,CAAC,eAAe;AAChB;AAAA,QACJ;AACA,eAAO,cAAc,IAAI,SAAU,cAAc;AAC7C,cAAI,OAAO;AAAA,YACP,OAAO,aAAa;AAAA,UACxB;AACA,cAAI,aAAa,UAAU;AACvB,iBAAK,WAAW,WAAW,aAAa,QAAQ;AAAA,UACpD;AACA,cAAI,aAAa,qBAAqB;AAClC,iBAAK,sBAAsB,aAAa,oBAAoB,IAAI,UAAU;AAAA,UAC9E;AACA,iBAAO;AAAA,QACX,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,IAAI;AAAA;AAAA,EAAqC,WAAY;AACjD,aAASC,qBAAoB,SAAS;AAClC,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,qBAAoB,UAAU,uBAAuB,SAAU,OAAO,SAAS,OAAO;AAClF,UAAI,WAAW,MAAM;AACrB,aAAO,KAAK,QAAQ,QAAQ,EACvB,KAAK,SAAU,QAAQ;AAAE,eAAO,OAAO,iBAAiB,SAAS,SAAS,GAAG,OAAO;AAAA,MAAG,CAAC,EACxF,KAAK,SAAU,QAAQ;AACxB,YAAI,CAAC,QAAQ;AACT;AAAA,QACJ;AACA,eAAO,OAAO,IAAI,SAAU,OAAO;AAC/B,cAAI,SAAS;AAAA,YACT,OAAO,MAAM,YAAY;AAAA,YACzB,KAAK,MAAM,UAAU;AAAA,UACzB;AACA,cAAI,OAAO,MAAM,SAAS,aAAa;AACnC,mBAAO,OAAO,mBAAmB,MAAM,IAAI;AAAA,UAC/C;AACA,iBAAO;AAAA,QACX,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,SAAS,mBAAmB,MAAM;AAC9B,UAAQ,MAAM;AAAA,IACV,KAAa,iBAAiB;AAC1B,aAAO,UAAU,iBAAiB;AAAA,IACtC,KAAa,iBAAiB;AAC1B,aAAO,UAAU,iBAAiB;AAAA,IACtC,KAAa,iBAAiB;AAC1B,aAAO,UAAU,iBAAiB;AAAA,EAC1C;AACJ;AACA,IAAI;AAAA;AAAA,EAAuC,WAAY;AACnD,aAASC,uBAAsB,SAAS;AACpC,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,uBAAsB,UAAU,yBAAyB,SAAU,OAAO,WAAW,OAAO;AACxF,UAAI,WAAW,MAAM;AACrB,aAAO,KAAK,QAAQ,QAAQ,EACvB,KAAK,SAAU,QAAQ;AAAE,eAAO,OAAO,mBAAmB,SAAS,SAAS,GAAG,UAAU,IAAI,YAAY,CAAC;AAAA,MAAG,CAAC,EAC9G,KAAK,SAAU,iBAAiB;AACjC,YAAI,CAAC,iBAAiB;AAClB;AAAA,QACJ;AACA,eAAO,gBAAgB,IAAI,SAAU,gBAAgB;AACjD,cAAI,SAAS,CAAC;AACd,iBAAO,gBAAgB;AACnB,mBAAO,KAAK,EAAE,OAAO,QAAQ,eAAe,KAAK,EAAE,CAAC;AACpD,6BAAiB,eAAe;AAAA,UACpC;AACA,iBAAO;AAAA,QACX,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;;;AC5lBK,SAAS,UAAU,UAAU;AAChC,MAAI,cAAc,CAAC;AACnB,MAAI,YAAY,CAAC;AACjB,MAAI,SAAS,IAAI,cAAc,QAAQ;AACvC,cAAY,KAAK,MAAM;AACvB,MAAI,SAAS,WAAY;AACrB,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IAC3B;AACA,WAAO,OAAO,yBAAyB,MAAM,QAAQ,IAAI;AAAA,EAC7D;AACA,WAAS,oBAAoB;AACzB,QAAI,aAAa,SAAS,YAAY,oBAAoB,SAAS;AACnE,eAAW,SAAS;AACpB,QAAI,kBAAkB,iBAAiB;AACnC,gBAAU,KAAK,UAAU,+BAA+B,YAAY,IAAqB,kBAAkB,MAAM,CAAC,CAAC;AAAA,IACvH;AACA,QAAI,kBAAkB,QAAQ;AAC1B,gBAAU,KAAK,UAAU,sBAAsB,YAAY,IAAqB,aAAa,MAAM,CAAC,CAAC;AAAA,IACzG;AACA,QAAI,kBAAkB,oBAAoB;AACtC,gBAAU,KAAK,UAAU,kCAAkC,YAAY,IAAqB,yBAAyB,MAAM,CAAC,CAAC;AAAA,IACjI;AACA,QAAI,kBAAkB,aAAa;AAC/B,gBAAU,KAAK,UAAU,2BAA2B,YAAY,IAAqB,kBAAkB,MAAM,CAAC,CAAC;AAAA,IACnH;AACA,QAAI,kBAAkB,YAAY;AAC9B,gBAAU,KAAK,UAAU,0BAA0B,YAAY,IAAqB,iBAAiB,MAAM,CAAC,CAAC;AAAA,IACjH;AACA,QAAI,kBAAkB,iBAAiB;AACnC,gBAAU,KAAK,UAAU,+BAA+B,YAAY,IAAqB,sBAAsB,MAAM,CAAC,CAAC;AAAA,IAC3H;AACA,QAAI,kBAAkB,QAAQ;AAC1B,gBAAU,KAAK,UAAU,uBAAuB,YAAY,IAAqB,cAAc,MAAM,CAAC,CAAC;AAAA,IAC3G;AACA,QAAI,kBAAkB,QAAQ;AAC1B,gBAAU,KAAK,UAAU,sBAAsB,YAAY,IAAqB,qBAAqB,MAAM,CAAC,CAAC;AAAA,IACjH;AACA,QAAI,kBAAkB,eAAe;AACjC,gBAAU,KAAK,UAAU,6BAA6B,YAAY,IAAqB,oBAAoB,MAAM,CAAC,CAAC;AAAA,IACvH;AACA,QAAI,kBAAkB,aAAa;AAC/B,gBAAU,KAAK,IAAqB,mBAAmB,YAAY,QAAQ,QAAQ,CAAC;AAAA,IACxF;AACA,QAAI,kBAAkB,iBAAiB;AACnC,gBAAU,KAAK,UAAU,+BAA+B,YAAY,IAAqB,sBAAsB,MAAM,CAAC,CAAC;AAAA,IAC3H;AAAA,EACJ;AACA,oBAAkB;AAClB,cAAY,KAAK,aAAa,SAAS,CAAC;AACxC,SAAO,aAAa,WAAW;AACnC;AACA,SAAS,aAAa,aAAa;AAC/B,SAAO,EAAE,SAAS,WAAY;AAAE,WAAO,WAAW,WAAW;AAAA,EAAG,EAAE;AACtE;AACA,SAAS,WAAW,aAAa;AAC7B,SAAO,YAAY,QAAQ;AACvB,gBAAY,IAAI,EAAE,QAAQ;AAAA,EAC9B;AACJ;", "names": ["WorkerManager", "integer", "<PERSON><PERSON><PERSON><PERSON>", "Position", "Range", "Location", "LocationLink", "Color", "ColorInformation", "ColorPresentation", "FoldingRangeKind", "FoldingRange", "DiagnosticRelatedInformation", "DiagnosticSeverity", "DiagnosticTag", "CodeDescription", "Diagnostic", "Command", "TextEdit", "ChangeAnnotation", "ChangeAnnotationIdentifier", "AnnotatedTextEdit", "TextDocumentEdit", "CreateFile", "RenameFile", "DeleteFile", "WorkspaceEdit", "TextEditChangeImpl", "ChangeAnnotations", "WorkspaceChange", "TextDocumentIdentifier", "VersionedTextDocumentIdentifier", "OptionalVersionedTextDocumentIdentifier", "TextDocumentItem", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CompletionItemKind", "InsertTextFormat", "CompletionItemTag", "InsertReplaceEdit", "Range", "InsertTextMode", "CompletionItem", "CompletionList", "MarkedString", "Hover", "ParameterInformation", "SignatureInformation", "DocumentHighlightKind", "DocumentHighlight", "SymbolKind", "SymbolTag", "SymbolInformation", "DocumentSymbol", "CodeActionKind", "CodeActionContext", "CodeAction", "CodeLens", "FormattingOptions", "DocumentLink", "SelectionRange", "TextDocument", "FullTextDocument", "Is", "undefined", "integer", "<PERSON><PERSON><PERSON><PERSON>", "DiagnosticsAdapter", "CompletionAdapter", "HoverAdapter", "DocumentHighlightAdapter", "DefinitionAdapter", "ReferenceAdapter", "RenameAdapter", "DocumentSymbolAdapter", "DocumentColorAdapter", "FoldingRangeAdapter", "SelectionRangeAdapter"]}