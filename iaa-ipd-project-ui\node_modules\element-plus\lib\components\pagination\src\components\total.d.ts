import type Total from './total.vue';
import type { ExtractPropTypes } from 'vue';
export declare const paginationTotalProps: {
    readonly total: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 1000, boolean>;
};
export declare type PaginationTotalProps = ExtractPropTypes<typeof paginationTotalProps>;
export declare type TotalInstance = InstanceType<typeof Total>;
