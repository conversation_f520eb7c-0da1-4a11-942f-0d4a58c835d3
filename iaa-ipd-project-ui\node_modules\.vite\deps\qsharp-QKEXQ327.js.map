{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/qsharp/qsharp.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    comments: {\n        lineComment: '//'\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"', notIn: ['string', 'comment'] }\n    ],\n    surroundingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' }\n    ]\n};\nexport var language = {\n    // Set defaultToken to invalid to see what you do not tokenize yet\n    keywords: [\n        'namespace',\n        'open',\n        'as',\n        'operation',\n        'function',\n        'body',\n        'adjoint',\n        'newtype',\n        'controlled',\n        'if',\n        'elif',\n        'else',\n        'repeat',\n        'until',\n        'fixup',\n        'for',\n        'in',\n        'while',\n        'return',\n        'fail',\n        'within',\n        'apply',\n        'Adjoint',\n        'Controlled',\n        'Adj',\n        'Ctl',\n        'is',\n        'self',\n        'auto',\n        'distribute',\n        'invert',\n        'intrinsic',\n        'let',\n        'set',\n        'w/',\n        'new',\n        'not',\n        'and',\n        'or',\n        'use',\n        'borrow',\n        'using',\n        'borrowing',\n        'mutable'\n    ],\n    typeKeywords: [\n        'Unit',\n        'Int',\n        'BigInt',\n        'Double',\n        'Bool',\n        'String',\n        'Qubit',\n        'Result',\n        'Pauli',\n        'Range'\n    ],\n    invalidKeywords: [\n        'abstract',\n        'base',\n        'bool',\n        'break',\n        'byte',\n        'case',\n        'catch',\n        'char',\n        'checked',\n        'class',\n        'const',\n        'continue',\n        'decimal',\n        'default',\n        'delegate',\n        'do',\n        'double',\n        'enum',\n        'event',\n        'explicit',\n        'extern',\n        'finally',\n        'fixed',\n        'float',\n        'foreach',\n        'goto',\n        'implicit',\n        'int',\n        'interface',\n        'lock',\n        'long',\n        'null',\n        'object',\n        'operator',\n        'out',\n        'override',\n        'params',\n        'private',\n        'protected',\n        'public',\n        'readonly',\n        'ref',\n        'sbyte',\n        'sealed',\n        'short',\n        'sizeof',\n        'stackalloc',\n        'static',\n        'string',\n        'struct',\n        'switch',\n        'this',\n        'throw',\n        'try',\n        'typeof',\n        'unit',\n        'ulong',\n        'unchecked',\n        'unsafe',\n        'ushort',\n        'virtual',\n        'void',\n        'volatile'\n    ],\n    constants: ['true', 'false', 'PauliI', 'PauliX', 'PauliY', 'PauliZ', 'One', 'Zero'],\n    builtin: [\n        'X',\n        'Y',\n        'Z',\n        'H',\n        'HY',\n        'S',\n        'T',\n        'SWAP',\n        'CNOT',\n        'CCNOT',\n        'MultiX',\n        'R',\n        'RFrac',\n        'Rx',\n        'Ry',\n        'Rz',\n        'R1',\n        'R1Frac',\n        'Exp',\n        'ExpFrac',\n        'Measure',\n        'M',\n        'MultiM',\n        'Message',\n        'Length',\n        'Assert',\n        'AssertProb',\n        'AssertEqual'\n    ],\n    operators: [\n        'and=',\n        '<-',\n        '->',\n        '*',\n        '*=',\n        '@',\n        '!',\n        '^',\n        '^=',\n        ':',\n        '::',\n        '..',\n        '==',\n        '...',\n        '=',\n        '=>',\n        '>',\n        '>=',\n        '<',\n        '<=',\n        '-',\n        '-=',\n        '!=',\n        'or=',\n        '%',\n        '%=',\n        '|',\n        '+',\n        '+=',\n        '?',\n        '/',\n        '/=',\n        '&&&',\n        '&&&=',\n        '^^^',\n        '^^^=',\n        '>>>',\n        '>>>=',\n        '<<<',\n        '<<<=',\n        '|||',\n        '|||=',\n        '~~~',\n        '_',\n        'w/',\n        'w/='\n    ],\n    namespaceFollows: ['namespace', 'open'],\n    symbols: /[=><!~?:&|+\\-*\\/\\^%@._]+/,\n    escapes: /\\\\[\\s\\S]/,\n    // The main tokenizer for our languages\n    tokenizer: {\n        root: [\n            // identifiers and keywords\n            [\n                /[a-zA-Z_$][\\w$]*/,\n                {\n                    cases: {\n                        '@namespaceFollows': {\n                            token: 'keyword.$0',\n                            next: '@namespace'\n                        },\n                        '@typeKeywords': 'type',\n                        '@keywords': 'keyword',\n                        '@constants': 'constant',\n                        '@builtin': 'keyword',\n                        '@invalidKeywords': 'invalid',\n                        '@default': 'identifier'\n                    }\n                }\n            ],\n            // whitespace\n            { include: '@whitespace' },\n            // delimiters and operators\n            [/[{}()\\[\\]]/, '@brackets'],\n            [/@symbols/, { cases: { '@operators': 'operator', '@default': '' } }],\n            // numbers\n            [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, 'number.float'],\n            [/\\d+/, 'number'],\n            // delimiter: after number because of .\\d floats\n            [/[;,.]/, 'delimiter'],\n            // strings\n            //[/\"([^\"\\\\]|\\\\.)*$/, 'string.invalid' ],  // non-teminated string\n            [/\"/, { token: 'string.quote', bracket: '@open', next: '@string' }]\n        ],\n        string: [\n            [/[^\\\\\"]+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/\"/, { token: 'string.quote', bracket: '@close', next: '@pop' }]\n        ],\n        namespace: [\n            { include: '@whitespace' },\n            [/[A-Za-z]\\w*/, 'namespace'],\n            [/[\\.=]/, 'delimiter'],\n            ['', '', '@pop']\n        ],\n        whitespace: [\n            [/[ \\t\\r\\n]+/, 'white'],\n            [/(\\/\\/).*/, 'comment']\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,aAAa;AAAA,EACjB;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EAC1D;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AACJ;AACO,IAAI,WAAW;AAAA;AAAA,EAElB,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,cAAc;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,iBAAiB;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,WAAW,CAAC,QAAQ,SAAS,UAAU,UAAU,UAAU,UAAU,OAAO,MAAM;AAAA,EAClF,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,WAAW;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,kBAAkB,CAAC,aAAa,MAAM;AAAA,EACtC,SAAS;AAAA,EACT,SAAS;AAAA;AAAA,EAET,WAAW;AAAA,IACP,MAAM;AAAA;AAAA,MAEF;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,qBAAqB;AAAA,cACjB,OAAO;AAAA,cACP,MAAM;AAAA,YACV;AAAA,YACA,iBAAiB;AAAA,YACjB,aAAa;AAAA,YACb,cAAc;AAAA,YACd,YAAY;AAAA,YACZ,oBAAoB;AAAA,YACpB,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,YAAY,EAAE,OAAO,EAAE,cAAc,YAAY,YAAY,GAAG,EAAE,CAAC;AAAA;AAAA,MAEpE,CAAC,4BAA4B,cAAc;AAAA,MAC3C,CAAC,OAAO,QAAQ;AAAA;AAAA,MAEhB,CAAC,SAAS,WAAW;AAAA;AAAA;AAAA,MAGrB,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,UAAU,CAAC;AAAA,IACtE;AAAA,IACA,QAAQ;AAAA,MACJ,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IACpE;AAAA,IACA,WAAW;AAAA,MACP,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,eAAe,WAAW;AAAA,MAC3B,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,IAAI,IAAI,MAAM;AAAA,IACnB;AAAA,IACA,YAAY;AAAA,MACR,CAAC,cAAc,OAAO;AAAA,MACtB,CAAC,YAAY,SAAS;AAAA,IAC1B;AAAA,EACJ;AACJ;", "names": []}