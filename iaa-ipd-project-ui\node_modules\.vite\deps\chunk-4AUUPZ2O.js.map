{"version": 3, "sources": ["../../amis/esm/renderers/Table/index.js", "../../amis/esm/renderers/Table/TableCell.js", "../../amis/esm/renderers/QuickEdit.js", "../../amis/esm/renderers/Copyable.js", "../../amis/esm/renderers/PopOver.js", "../../amis/node_modules/mobx-react/dist/mobxreact.esm.js", "../../amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/assertEnvironment.js", "../../amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/utils/reactBatchedUpdates.js", "../../amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/utils.js", "../../amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/observerBatching.js", "../../amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/staticRendering.js", "../../amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/observer.js", "../../amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/useObserver.js", "../../amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/printDebugValue.js", "../../amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/reactionCleanupTracking.js", "../../amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/useQueuedForceUpdate.js", "../../amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/ObserverComponent.js", "../../amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/useAsObservableSource.js", "../../amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/useLocalStore.js", "../../amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/index.js", "../../amis/esm/renderers/Table/HeadCellFilterDropdown.js", "../../amis/esm/renderers/Table/HeadCellSearchDropdown.js", "../../amis/esm/renderers/Table/TableContent.js", "../../amis/esm/renderers/Table/TableBody.js", "../../amis/esm/renderers/Table/TableRow.js", "../../amis/esm/renderers/Table/ItemActionsWrapper.js", "../../amis/esm/renderers/Table/ColGroup.js", "../../amis/esm/renderers/Table/exportExcel.js", "../../amis/esm/renderers/Table/AutoFilterForm.js", "../../amis/esm/renderers/Table/Cell.js"], "sourcesContent": ["/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __awaiter, __generator, __assign, __read, __rest, __decorate, __metadata } from 'tslib';\nimport React from 'react';\nimport { isAlive } from 'mobx-state-tree';\nimport { reaction } from 'mobx';\nimport Sortable from 'sortablejs';\nimport isEqual from 'lodash/isEqual';\nimport find from 'lodash/find';\nimport debounce from 'lodash/debounce';\nimport intersection from 'lodash/intersection';\nimport isPlainObject from 'lodash/isPlainObject';\nimport { resolveVariableAndFilter, getPropValue, loopTooMuch, resizeSensor, isEffectiveApi, offset, getScrollParent, position, getStyleNumber, isObject, changedEffect, anyChanged, isPureVariable, isArrayChildrenModified, createObject, difference, eachTree, animation, resolveVariable, filter, ScopedContext, autobind, getMatchedEventTargets, Renderer, TableStore } from 'amis-core';\nimport { Icon, Checkbox, Button, Spinner } from 'amis-ui';\nexport { TableCell } from './TableCell.js';\nimport { HeadCellFilterDropDown } from './HeadCellFilterDropdown.js';\nimport { HeadCellSearchDropDown } from './HeadCellSearchDropdown.js';\nimport TableContent, { renderItemActions } from './TableContent.js';\nimport ColumnToggler from './ColumnToggler.js';\nimport { exportExcel } from './exportExcel.js';\nimport AutoFilterForm from './AutoFilterForm.js';\nimport Cell from './Cell.js';\n\nvar Table = /** @class */ (function (_super) {\n    __extends(Table, _super);\n    function Table(props, context) {\n        var _this = _super.call(this, props) || this;\n        _this.dom = React.createRef();\n        _this.renderedToolbars = [];\n        _this.subForms = {};\n        _this.toDispose = [];\n        _this.updateTableInfoLazy = debounce(_this.updateTableInfo.bind(_this), 250, {\n            trailing: true,\n            leading: false\n        });\n        _this.updateAutoFillHeightLazy = debounce(_this.updateAutoFillHeight.bind(_this), 250, {\n            trailing: true,\n            leading: false\n        });\n        var scoped = context;\n        scoped.registerComponent(_this);\n        _this.handleOutterScroll = _this.handleOutterScroll.bind(_this);\n        _this.tableRef = _this.tableRef.bind(_this);\n        _this.affixedTableRef = _this.affixedTableRef.bind(_this);\n        _this.updateTableInfo = _this.updateTableInfo.bind(_this);\n        _this.handleAction = _this.handleAction.bind(_this);\n        _this.handleCheck = _this.handleCheck.bind(_this);\n        _this.handleCheckAll = _this.handleCheckAll.bind(_this);\n        _this.handleQuickChange = _this.handleQuickChange.bind(_this);\n        _this.handleSave = _this.handleSave.bind(_this);\n        _this.handleSaveOrder = _this.handleSaveOrder.bind(_this);\n        _this.reset = _this.reset.bind(_this);\n        _this.dragTipRef = _this.dragTipRef.bind(_this);\n        _this.getPopOverContainer = _this.getPopOverContainer.bind(_this);\n        _this.renderCell = _this.renderCell.bind(_this);\n        _this.renderHeadCell = _this.renderHeadCell.bind(_this);\n        _this.renderToolbar = _this.renderToolbar.bind(_this);\n        _this.handleMouseMove = _this.handleMouseMove.bind(_this);\n        _this.handleMouseLeave = _this.handleMouseLeave.bind(_this);\n        _this.subFormRef = _this.subFormRef.bind(_this);\n        _this.handleColumnToggle = _this.handleColumnToggle.bind(_this);\n        _this.handleRowClick = _this.handleRowClick.bind(_this);\n        _this.handleRowDbClick = _this.handleRowDbClick.bind(_this);\n        _this.handleRowMouseEnter = _this.handleRowMouseEnter.bind(_this);\n        _this.handleRowMouseLeave = _this.handleRowMouseLeave.bind(_this);\n        _this.updateAutoFillHeight = _this.updateAutoFillHeight.bind(_this);\n        var store = props.store, columns = props.columns, selectable = props.selectable, columnsTogglable = props.columnsTogglable, draggable = props.draggable, orderBy = props.orderBy, orderDir = props.orderDir, multiple = props.multiple, footable = props.footable, primaryField = props.primaryField, itemCheckableOn = props.itemCheckableOn, itemDraggableOn = props.itemDraggableOn, hideCheckToggler = props.hideCheckToggler, combineFromIndex = props.combineFromIndex, expandConfig = props.expandConfig, formItem = props.formItem, keepItemSelectionOnPageChange = props.keepItemSelectionOnPageChange, maxKeepItemSelectionLength = props.maxKeepItemSelectionLength, maxItemSelectionLength = props.maxItemSelectionLength, onQuery = props.onQuery, autoGenerateFilter = props.autoGenerateFilter, loading = props.loading, canAccessSuperData = props.canAccessSuperData, lazyRenderAfter = props.lazyRenderAfter, tableLayout = props.tableLayout, resolveDefinitions = props.resolveDefinitions, showIndex = props.showIndex;\n        var combineNum = props.combineNum;\n        if (typeof combineNum === 'string') {\n            combineNum = parseInt(resolveVariableAndFilter(combineNum, props.data, '| raw'), 10);\n        }\n        store.update({\n            selectable: selectable,\n            draggable: draggable,\n            columns: columns,\n            columnsTogglable: columnsTogglable,\n            orderBy: onQuery ? orderBy : undefined,\n            orderDir: orderDir,\n            multiple: multiple,\n            footable: footable,\n            expandConfig: expandConfig,\n            primaryField: primaryField,\n            itemCheckableOn: itemCheckableOn,\n            itemDraggableOn: itemDraggableOn,\n            hideCheckToggler: hideCheckToggler,\n            combineNum: combineNum,\n            combineFromIndex: combineFromIndex,\n            keepItemSelectionOnPageChange: keepItemSelectionOnPageChange,\n            maxKeepItemSelectionLength: maxKeepItemSelectionLength,\n            maxItemSelectionLength: maxItemSelectionLength,\n            loading: loading,\n            canAccessSuperData: canAccessSuperData,\n            lazyRenderAfter: lazyRenderAfter,\n            tableLayout: tableLayout,\n            showIndex: showIndex\n        }, {\n            resolveDefinitions: resolveDefinitions\n        });\n        if (isPlainObject(autoGenerateFilter) &&\n            autoGenerateFilter.defaultCollapsed === false) {\n            store.setSearchFormExpanded(true);\n        }\n        formItem && isAlive(formItem) && formItem.setSubStore(store);\n        Table.syncRows(store, _this.props, undefined) && _this.syncSelected();\n        _this.toDispose.push(reaction(function () {\n            return store\n                .getExpandedRows()\n                .filter(function (row) { return row.defer && !row.loaded && !row.loading && !row.error; });\n        }, function (rows) { return rows.forEach(_this.loadDeferredRow); }));\n        return _this;\n    }\n    Table.syncRows = function (store, props, prevProps) {\n        var source = props.source;\n        var value = getPropValue(props, function (props) { return props.items; });\n        var rows = [];\n        var updateRows = false;\n        // 要严格比较前后的value值，否则某些情况下会导致循环update无限渲染\n        if (Array.isArray(value)) {\n            if (!prevProps ||\n                !isEqual(getPropValue(prevProps, function (props) { return props.items; }), value)) {\n                updateRows = true;\n                rows = value;\n            }\n        }\n        else if (typeof source === 'string') {\n            var resolved = resolveVariableAndFilter(source, props.data, '| raw');\n            var prev = prevProps\n                ? resolveVariableAndFilter(source, prevProps.data, '| raw')\n                : null;\n            if (prev === resolved) {\n                updateRows = false;\n            }\n            else if (loopTooMuch(\"Table.syncRows\".concat(store.id)) &&\n                isEqual(prev, resolved)) {\n                updateRows = false;\n            }\n            else {\n                updateRows = true;\n                rows = Array.isArray(resolved) ? resolved : [];\n            }\n        }\n        if (updateRows) {\n            store.initRows(rows, props.getEntryId, props.reUseRow, props.fullItems, props.selected);\n        }\n        else if (props.reUseRow === false) {\n            /**\n             * 在reUseRow为false情况下，支持强制刷新表格行状态\n             * 适用的情况：用户每次刷新，调用接口，返回的数据都是一样的，导致updateRows为false，故针对每次返回数据一致的情况，需要强制表格更新\n             */\n            updateRows = true;\n            store.initRows(value, props.getEntryId, props.reUseRow, props.fullItems, props.selected);\n        }\n        Array.isArray(props.selected) &&\n            store.updateSelected(props.selected, props.valueField);\n        return updateRows;\n    };\n    Table.prototype.componentDidMount = function () {\n        var currentNode = this.dom.current;\n        this.initAutoFillHeight();\n        // todo 因为没有监控里面内容的宽度变化，所以单元格内容变化撑开时可能看不到 fixed 的阴影\n        // 应该加上 table 的宽度检测\n        this.toDispose.push(resizeSensor(currentNode, this.updateTableInfoLazy, false, 'width'));\n        var table = this.table;\n        var _a = this.props, store = _a.store, autoGenerateFilter = _a.autoGenerateFilter, onSearchableFromInit = _a.onSearchableFromInit;\n        // autoGenerateFilter 开启后\n        // 如果没有一个 searchable 的 column crud 就不会初始化加载\n        // 所以这里加个判断默认初始加载一次\n        if (autoGenerateFilter &&\n            !store.searchableColumns.length &&\n            onSearchableFromInit) {\n            onSearchableFromInit({});\n        }\n    };\n    Table.prototype.loadDeferredRow = function (row) {\n        return __awaiter(this, void 0, void 0, function () {\n            var env, deferApi, response, e_1;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        env = this.props.env;\n                        deferApi = row.data.deferApi || this.props.deferApi;\n                        if (!isEffectiveApi(deferApi)) {\n                            throw new Error('deferApi is required');\n                        }\n                        _a.label = 1;\n                    case 1:\n                        _a.trys.push([1, 3, 4, 5]);\n                        row.markLoading(true);\n                        return [4 /*yield*/, env.fetcher(deferApi, row.locals)];\n                    case 2:\n                        response = _a.sent();\n                        if (!response.ok) {\n                            throw new Error(response.msg);\n                        }\n                        row.updateData(response.data);\n                        row.markLoaded(true);\n                        row.setError('');\n                        return [3 /*break*/, 5];\n                    case 3:\n                        e_1 = _a.sent();\n                        row.setError(e_1.message);\n                        env.notify('error', e_1.message);\n                        return [3 /*break*/, 5];\n                    case 4:\n                        row.markLoading(false);\n                        return [7 /*endfinally*/];\n                    case 5: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    Table.prototype.initAutoFillHeight = function () {\n        var props = this.props;\n        var currentNode = this.dom.current;\n        if (props.autoFillHeight) {\n            this.autoFillHeightDispose = resizeSensor(currentNode.parentElement, this.updateAutoFillHeightLazy, false, 'height');\n            this.toDispose.push(this.autoFillHeightDispose);\n            this.updateAutoFillHeight();\n        }\n    };\n    /**\n     * 自动设置表格高度占满界面剩余区域\n     * 用 css 实现有点麻烦，要改很多结构，所以先用 dom hack 了，避免对之前的功能有影响\n     */\n    Table.prototype.updateAutoFillHeight = function () {\n        var _this = this;\n        var _a = this.props, autoFillHeight = _a.autoFillHeight, footerToolbar = _a.footerToolbar, ns = _a.classPrefix;\n        if (!autoFillHeight) {\n            return;\n        }\n        var table = this.table;\n        var tableContent = table.parentElement;\n        if (!tableContent) {\n            return;\n        }\n        // 可能数据还没到，没有渲染 footer\n        // 也可能是弹窗中，弹窗还在动画中，等一下再执行\n        if (!tableContent.offsetHeight ||\n            tableContent.getBoundingClientRect().height / tableContent.offsetHeight <\n                0.8) {\n            this.timer = setTimeout(function () {\n                _this.updateAutoFillHeight();\n            }, 100);\n            return;\n        }\n        // 计算 table-content 在 dom 中的位置\n        var viewportHeight = window.innerHeight;\n        var tableContentTop = offset(tableContent).top;\n        var parent = getScrollParent(tableContent.parentElement);\n        if (parent && parent !== document.body) {\n            viewportHeight = parent.clientHeight - 1;\n            tableContentTop = position(tableContent, parent).top;\n        }\n        var tableContentBottom = 0;\n        var selfNode = tableContent;\n        var parentNode = selfNode.parentElement;\n        while (parentNode) {\n            var paddingBottom = getStyleNumber(parentNode, 'padding-bottom');\n            var borderBottom = getStyleNumber(parentNode, 'border-bottom-width');\n            var nextSiblingHeight = 0;\n            var nextSibling = selfNode.nextElementSibling;\n            while (nextSibling) {\n                var positon = getComputedStyle(nextSibling).position;\n                if (positon !== 'absolute' && positon !== 'fixed') {\n                    var rect1 = selfNode.getBoundingClientRect();\n                    var rect2 = nextSibling.getBoundingClientRect();\n                    if (rect1.bottom <= rect2.top) {\n                        nextSiblingHeight +=\n                            nextSibling.offsetHeight +\n                                getStyleNumber(nextSibling, 'margin-bottom');\n                    }\n                }\n                nextSibling = nextSibling.nextElementSibling;\n            }\n            var marginBottom = getStyleNumber(selfNode, 'margin-bottom');\n            tableContentBottom +=\n                paddingBottom + borderBottom + marginBottom + nextSiblingHeight;\n            selfNode = parentNode;\n            parentNode = selfNode.parentElement;\n            if (parent && parent !== document.body && parent === selfNode) {\n                break;\n            }\n        }\n        var heightField = autoFillHeight && autoFillHeight.maxHeight\n            ? 'maxHeight'\n            : 'height';\n        var heightValue = isObject(autoFillHeight)\n            ? autoFillHeight[heightField]\n            : 0;\n        var tableContentHeight = heightValue\n            ? \"\".concat(heightValue, \"px\")\n            : \"\".concat(Math.round(viewportHeight - tableContentTop - tableContentBottom - 1), \"px\");\n        tableContent.style[heightField] = tableContentHeight;\n        tableContent.style.setProperty(\"--Table-content-\".concat(heightField), tableContentHeight);\n    };\n    Table.prototype.componentDidUpdate = function (prevProps) {\n        var _a;\n        var props = this.props;\n        var store = props.store;\n        changedEffect([\n            'selectable',\n            'columnsTogglable',\n            'draggable',\n            'orderBy',\n            'orderDir',\n            'multiple',\n            'footable',\n            'primaryField',\n            'itemCheckableOn',\n            'itemDraggableOn',\n            'hideCheckToggler',\n            'combineNum',\n            'combineFromIndex',\n            'expandConfig',\n            'columns',\n            'loading',\n            'canAccessSuperData',\n            'lazyRenderAfter',\n            'tableLayout',\n            'showIndex'\n        ], prevProps, props, function (changes) {\n            if (changes.hasOwnProperty('combineNum') &&\n                typeof changes.combineNum === 'string') {\n                changes.combineNum = parseInt(resolveVariableAndFilter(changes.combineNum, props.data, '| raw'), 10);\n            }\n            if (changes.orderBy && !props.onQuery) {\n                delete changes.orderBy;\n            }\n            store.update(changes, {\n                resolveDefinitions: props.resolveDefinitions\n            });\n        });\n        if (anyChanged(['source', 'value', 'items'], prevProps, props) ||\n            (!props.value &&\n                !props.items &&\n                (props.data !== prevProps.data ||\n                    (typeof props.source === 'string' && isPureVariable(props.source))))) {\n            Table.syncRows(store, props, prevProps) && this.syncSelected();\n        }\n        else if (isArrayChildrenModified(prevProps.selected, props.selected)) {\n            var prevSelectedRows = store.selectedRows\n                .map(function (item) { return item.id; })\n                .join(',');\n            store.updateSelected(props.selected || [], props.valueField);\n            if (Array.isArray(props.selected) &&\n                Array.isArray(prevProps.selected) &&\n                props.selected.length === prevProps.selected.length) {\n                // 只有长度一样才检测具体的值是否变了\n                var selectedRows = store.selectedRows.map(function (item) { return item.id; }).join(',');\n                prevSelectedRows !== selectedRows && this.syncSelected();\n            }\n            else {\n                this.syncSelected();\n            }\n        }\n        // 检测属性变化，来切换功能\n        if (props.autoFillHeight !== prevProps.autoFillHeight) {\n            if (this.autoFillHeightDispose) {\n                var idx = this.toDispose.indexOf(this.autoFillHeightDispose);\n                if (idx !== -1) {\n                    this.toDispose.splice(idx, 1);\n                }\n                this.autoFillHeightDispose();\n                delete this.autoFillHeightDispose;\n                var tableContent = (_a = this.table) === null || _a === void 0 ? void 0 : _a.parentElement;\n                if (tableContent) {\n                    tableContent.style.height = '';\n                }\n            }\n            this.initAutoFillHeight();\n        }\n    };\n    Table.prototype.componentWillUnmount = function () {\n        var formItem = this.props.formItem;\n        this.toDispose.forEach(function (fn) { return fn(); });\n        this.toDispose = [];\n        delete this.autoFillHeightDispose;\n        this.updateTableInfoLazy.cancel();\n        this.updateAutoFillHeightLazy.cancel();\n        formItem && isAlive(formItem) && formItem.setSubStore(null);\n        clearTimeout(this.timer);\n        var scoped = this.context;\n        scoped.unRegisterComponent(this);\n    };\n    Table.prototype.scrollToTop = function () {\n        var _a, _b;\n        (_a = this.dom.current) === null || _a === void 0 ? void 0 : _a.scrollIntoView();\n        if (this.props.autoFillHeight) {\n            (_b = this.table) === null || _b === void 0 ? void 0 : _b.scrollIntoView();\n        }\n        var scrolledY = window.scrollY;\n        scrolledY && window.scroll(0, scrolledY);\n    };\n    Table.prototype.subFormRef = function (form, x, y) {\n        var quickEditFormRef = this.props.quickEditFormRef;\n        quickEditFormRef && quickEditFormRef(form, x, y);\n        this.subForms[\"\".concat(x, \"-\").concat(y)] = form;\n        form && this.props.store.addForm(form.props.store, y);\n    };\n    Table.prototype.handleAction = function (e, action, ctx) {\n        var onAction = this.props.onAction;\n        // todo\n        return onAction(e, action, ctx);\n    };\n    Table.prototype.handleCheck = function (item, value, shift) {\n        return __awaiter(this, void 0, void 0, function () {\n            var _a, store, data, dispatchEvent, selectable;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        _a = this.props, store = _a.store, data = _a.data, dispatchEvent = _a.dispatchEvent, selectable = _a.selectable;\n                        if (!selectable) {\n                            return [2 /*return*/];\n                        }\n                        value = value !== undefined ? value : !item.checked;\n                        if (shift) {\n                            store.toggleShift(item, value);\n                        }\n                        else {\n                            // 如果picker的value是绑定的上层数量变量\n                            // 那么用户只能通过事件动作来更新上层变量来实现选中\n                            item.toggle(value);\n                        }\n                        this.syncSelected();\n                        return [4 /*yield*/, dispatchEvent('selectedChange', createObject(data, __assign(__assign({}, store.eventContext), { item: item.data })))];\n                    case 1:\n                        _b.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    Table.prototype.handleRowClick = function (item, index) {\n        var _a, _b;\n        var _c = this.props, dispatchEvent = _c.dispatchEvent, filterItemIndex = _c.filterItemIndex, store = _c.store, data = _c.data;\n        return dispatchEvent('rowClick', createObject(data, {\n            rowItem: item.data,\n            item: item.data,\n            index: parseInt(\"\".concat((_a = filterItemIndex === null || filterItemIndex === void 0 ? void 0 : filterItemIndex(item.index, item)) !== null && _a !== void 0 ? _a : item.index), 10),\n            indexPath: (_b = filterItemIndex === null || filterItemIndex === void 0 ? void 0 : filterItemIndex(item.path, item)) !== null && _b !== void 0 ? _b : item.path\n        }));\n    };\n    Table.prototype.handleRowDbClick = function (item, index) {\n        var _a, _b;\n        var _c = this.props, dispatchEvent = _c.dispatchEvent, filterItemIndex = _c.filterItemIndex, store = _c.store, data = _c.data;\n        return dispatchEvent('rowDbClick', createObject(data, {\n            item: item.data,\n            index: parseInt(\"\".concat((_a = filterItemIndex === null || filterItemIndex === void 0 ? void 0 : filterItemIndex(item.index, item)) !== null && _a !== void 0 ? _a : item.index), 10),\n            indexPath: (_b = filterItemIndex === null || filterItemIndex === void 0 ? void 0 : filterItemIndex(item.path, item)) !== null && _b !== void 0 ? _b : item.path\n        }));\n    };\n    Table.prototype.handleRowMouseEnter = function (item, index) {\n        var _a, _b;\n        var _c = this.props, dispatchEvent = _c.dispatchEvent, filterItemIndex = _c.filterItemIndex, store = _c.store, data = _c.data;\n        return dispatchEvent('rowMouseEnter', createObject(data, {\n            item: item.data,\n            index: parseInt(\"\".concat((_a = filterItemIndex === null || filterItemIndex === void 0 ? void 0 : filterItemIndex(item.index, item)) !== null && _a !== void 0 ? _a : item.index), 10),\n            indexPath: (_b = filterItemIndex === null || filterItemIndex === void 0 ? void 0 : filterItemIndex(item.path, item)) !== null && _b !== void 0 ? _b : item.path\n        }));\n    };\n    Table.prototype.handleRowMouseLeave = function (item, index) {\n        var _a, _b;\n        var _c = this.props, dispatchEvent = _c.dispatchEvent, filterItemIndex = _c.filterItemIndex, store = _c.store, data = _c.data;\n        return dispatchEvent('rowMouseLeave', createObject(data, {\n            item: item.data,\n            index: parseInt(\"\".concat((_a = filterItemIndex === null || filterItemIndex === void 0 ? void 0 : filterItemIndex(item.index, item)) !== null && _a !== void 0 ? _a : item.index), 10),\n            indexPath: (_b = filterItemIndex === null || filterItemIndex === void 0 ? void 0 : filterItemIndex(item.path, item)) !== null && _b !== void 0 ? _b : item.path\n        }));\n    };\n    Table.prototype.handleCheckAll = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var _a, store, data, dispatchEvent;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        _a = this.props, store = _a.store, data = _a.data, dispatchEvent = _a.dispatchEvent;\n                        store.toggleAll();\n                        this.syncSelected();\n                        return [4 /*yield*/, dispatchEvent('selectedChange', createObject(data, __assign({}, store.eventContext)))];\n                    case 1:\n                        _b.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    Table.prototype.handleQuickChange = function (item, values, saveImmediately, savePristine, options) {\n        if (!isAlive(item)) {\n            return;\n        }\n        var _a = this.props, onSave = _a.onSave, onPristineChange = _a.onPristineChange, propsSaveImmediately = _a.saveImmediately, primaryField = _a.primaryField, onItemChange = _a.onItemChange;\n        item.change(values, savePristine);\n        // 依然解决不了问题，所以先注释掉\n        // 预期是，这个表党项修改的时候，把其他还没运算公式的表单更新最新值\n        // 好让公式计算触发的值是最新的\n        // 但是事与愿违，应该是修改了 store.data 但是 props.data 还没变过来\n        // 即便如此，但是最终还是会算正确，只是会多触发几次 onChange :(\n        // const y = item.index;\n        // const str = `-${y}`;\n        // Object.keys(this.subForms).forEach(key => {\n        //   if (key.endsWith(str)) {\n        //     this.subForms[key].props.store.updateData(values);\n        //   }\n        // });\n        // 值发生变化了，需要通过 onSelect 通知到外面，否则会出现数据不同步的问题\n        item.modified && this.syncSelected();\n        if (savePristine) {\n            onPristineChange === null || onPristineChange === void 0 ? void 0 : onPristineChange(item.data, item.path);\n            return;\n        }\n        onItemChange === null || onItemChange === void 0 ? void 0 : onItemChange(item.data, difference(item.data, item.pristine, ['id', primaryField]), item.path);\n        if (!saveImmediately && !propsSaveImmediately) {\n            return;\n        }\n        else if (saveImmediately && saveImmediately.api) {\n            this.props.onAction(null, {\n                actionType: 'ajax',\n                api: saveImmediately.api,\n                reload: options === null || options === void 0 ? void 0 : options.reload\n            }, item.locals);\n            return;\n        }\n        if (!onSave) {\n            return;\n        }\n        onSave(item.data, difference(item.data, item.pristine, ['id', primaryField]), item.path, undefined, item.pristine, options);\n    };\n    Table.prototype.handleSave = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var _a, store, onSave, primaryField, subForms, result, subFormItems, result, rows, rowIndexes, diff, unModifiedRows;\n            var _this = this;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        _a = this.props, store = _a.store, onSave = _a.onSave, primaryField = _a.primaryField;\n                        if (!onSave || !store.modifiedRows.length) {\n                            return [2 /*return*/];\n                        }\n                        subForms = [];\n                        Object.keys(this.subForms).forEach(function (key) { return _this.subForms[key] && subForms.push(_this.subForms[key]); });\n                        if (!subForms.length) return [3 /*break*/, 2];\n                        return [4 /*yield*/, Promise.all(subForms.map(function (item) { return item.validate(); }))];\n                    case 1:\n                        result = _b.sent();\n                        if (~result.indexOf(false)) {\n                            return [2 /*return*/];\n                        }\n                        _b.label = 2;\n                    case 2:\n                        subFormItems = store.children.filter(function (item) { return (item === null || item === void 0 ? void 0 : item.storeType) === 'FormItemStore'; });\n                        if (!subFormItems.length) return [3 /*break*/, 4];\n                        return [4 /*yield*/, Promise.all(subFormItems.map(function (item) {\n                                var ctx = {};\n                                if (item.rowIndex && store.rows[item.rowIndex]) {\n                                    ctx = store.rows[item.rowIndex].data;\n                                }\n                                return item.validate(ctx);\n                            }))];\n                    case 3:\n                        result = _b.sent();\n                        if (~result.indexOf(false)) {\n                            return [2 /*return*/];\n                        }\n                        _b.label = 4;\n                    case 4:\n                        rows = store.modifiedRows.map(function (item) { return item.data; });\n                        rowIndexes = store.modifiedRows.map(function (item) { return item.path; });\n                        diff = store.modifiedRows.map(function (item) {\n                            return difference(item.data, item.pristine, ['id', primaryField]);\n                        });\n                        unModifiedRows = store.rows\n                            .filter(function (item) { return !item.modified; })\n                            .map(function (item) { return item.data; });\n                        return [2 /*return*/, onSave(rows, diff, rowIndexes, unModifiedRows, store.modifiedRows.map(function (item) { return item.pristine; }))];\n                }\n            });\n        });\n    };\n    Table.prototype.handleSaveOrder = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var _a, store, onSaveOrder, data, dispatchEvent, movedItems, items, rendererEvent;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        _a = this.props, store = _a.store, onSaveOrder = _a.onSaveOrder, data = _a.data, dispatchEvent = _a.dispatchEvent;\n                        movedItems = store.movedRows.map(function (item) { return item.data; });\n                        items = store.rows.map(function (item) { return item.getDataWithModifiedChilden(); });\n                        return [4 /*yield*/, dispatchEvent('orderChange', createObject(data, { movedItems: movedItems }))];\n                    case 1:\n                        rendererEvent = _b.sent();\n                        if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {\n                            return [2 /*return*/];\n                        }\n                        if (!onSaveOrder || !store.movedRows.length) {\n                            return [2 /*return*/];\n                        }\n                        onSaveOrder(movedItems, items);\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    Table.prototype.syncSelected = function () {\n        var _a = this.props, store = _a.store, onSelect = _a.onSelect;\n        onSelect &&\n            onSelect(store.selectedRows.map(function (item) { return item.data; }), store.unSelectedRows.map(function (item) { return item.data; }));\n    };\n    Table.prototype.reset = function () {\n        var _this = this;\n        var store = this.props.store;\n        store.reset();\n        var subForms = [];\n        Object.keys(this.subForms).forEach(function (key) { return _this.subForms[key] && subForms.push(_this.subForms[key]); });\n        subForms.forEach(function (item) { return item.clearErrors(); });\n        // 去掉错误提示\n        var subFormItems = store.children.filter(function (item) { return (item === null || item === void 0 ? void 0 : item.storeType) === 'FormItemStore'; });\n        if (subFormItems.length) {\n            subFormItems.map(function (item) { return item.reset(); });\n        }\n    };\n    Table.prototype.bulkUpdate = function (value, items) {\n        var _a = this.props, store = _a.store, primaryField = _a.primaryField;\n        if (primaryField && value.ids) {\n            var ids_1 = value.ids.split(',');\n            var rows = store.rows.filter(function (item) {\n                return find(ids_1, function (id) { return id && id == item.data[primaryField]; });\n            });\n            var newValue_1 = __assign(__assign({}, value), { ids: undefined });\n            rows.forEach(function (row) { return row.change(newValue_1); });\n        }\n        else if (Array.isArray(items)) {\n            var rows = store.rows.filter(function (item) { return ~items.indexOf(item.pristine); });\n            rows.forEach(function (row) { return row.change(value); });\n        }\n    };\n    Table.prototype.getSelected = function () {\n        var store = this.props.store;\n        return store.selectedRows.map(function (item) { return item.data; });\n    };\n    Table.prototype.updateTableInfo = function (callback) {\n        if (this.resizeLine) {\n            return;\n        }\n        this.props.store.initTableWidth();\n        this.props.store.syncTableWidth();\n        this.handleOutterScroll();\n        callback && setTimeout(callback, 20);\n    };\n    // 当表格滚动是，需要让 affixHeader 部分的表格也滚动\n    Table.prototype.handleOutterScroll = function () {\n        var table = this.table;\n        if (!table) {\n            return;\n        }\n        var outter = table === null || table === void 0 ? void 0 : table.parentNode;\n        var scrollLeft = outter.scrollLeft;\n        if (this.affixedTable) {\n            this.affixedTable.parentElement.scrollLeft = scrollLeft;\n        }\n        if (this.props.store.filteredColumns.some(function (column) { return column.fixed; })) {\n            var leading_1 = scrollLeft === 0;\n            var trailing_1 = Math.ceil(scrollLeft) + outter.offsetWidth >= table.scrollWidth;\n            [table, this.affixedTable]\n                .filter(function (item) { return item; })\n                .forEach(function (table) {\n                table.classList.remove('table-fixed-left', 'table-fixed-right');\n                leading_1 || table.classList.add('table-fixed-left');\n                trailing_1 || table.classList.add('table-fixed-right');\n            });\n        }\n    };\n    Table.prototype.tableRef = function (ref) {\n        var _this = this;\n        var _a;\n        this.table = ref;\n        isAlive(this.props.store) && this.props.store.setTable(ref);\n        (_a = this.tableUnWatchResize) === null || _a === void 0 ? void 0 : _a.call(this);\n        if (ref) {\n            this.handleOutterScroll();\n            this.tableUnWatchResize = resizeSensor(ref, function () {\n                _this.handleOutterScroll();\n            });\n        }\n    };\n    Table.prototype.dragTipRef = function (ref) {\n        if (!this.dragTip && ref) {\n            this.initDragging();\n        }\n        else if (this.dragTip && !ref) {\n            this.destroyDragging();\n        }\n        this.dragTip = ref;\n    };\n    Table.prototype.affixedTableRef = function (ref) {\n        this.affixedTable = ref;\n        ref && this.handleOutterScroll();\n    };\n    Table.prototype.initDragging = function () {\n        var _this = this;\n        var _a = this.props, store = _a.store, ns = _a.classPrefix;\n        this.sortable = new Sortable(this.table.querySelector(':scope>tbody'), {\n            group: 'table',\n            animation: 150,\n            handle: \".\".concat(ns, \"Table-dragCell\"),\n            filter: \".\".concat(ns, \"Table-dragCell.is-dragDisabled\"),\n            ghostClass: 'is-dragging',\n            onEnd: function (e) { return __awaiter(_this, void 0, void 0, function () {\n                var parent;\n                return __generator(this, function (_a) {\n                    // 没有移动\n                    if (e.newIndex === e.oldIndex) {\n                        return [2 /*return*/];\n                    }\n                    parent = e.to;\n                    if (e.oldIndex < parent.childNodes.length - 1) {\n                        parent.insertBefore(e.item, parent.childNodes[e.oldIndex > e.newIndex ? e.oldIndex + 1 : e.oldIndex]);\n                    }\n                    else {\n                        parent.appendChild(e.item);\n                    }\n                    store.exchange(e.oldIndex, e.newIndex);\n                    return [2 /*return*/];\n                });\n            }); }\n        });\n    };\n    Table.prototype.destroyDragging = function () {\n        this.sortable && this.sortable.destroy();\n    };\n    Table.prototype.getPopOverContainer = function () {\n        return this.dom.current;\n    };\n    Table.prototype.handleMouseMove = function (e) {\n        var tr = e.target.closest('tr[data-id]');\n        if (!tr) {\n            return;\n        }\n        var _a = this.props, store = _a.store, affixColumns = _a.affixColumns, itemActions = _a.itemActions;\n        // if (\n        //   (affixColumns === false ||\n        //     (store.leftFixedColumns.length === 0 &&\n        //       store.rightFixedColumns.length === 0)) &&\n        //   (!itemActions || !itemActions.filter(item => !item.hiddenOnHover).length)\n        // ) {\n        //   return;\n        // }\n        var id = tr.getAttribute('data-id');\n        var row = store.hoverRow;\n        if ((row === null || row === void 0 ? void 0 : row.id) === id) {\n            return;\n        }\n        eachTree(store.rows, function (item) { return item.setIsHover(item.id === id); });\n    };\n    Table.prototype.handleMouseLeave = function () {\n        var store = this.props.store;\n        var row = store.hoverRow;\n        row === null || row === void 0 ? void 0 : row.setIsHover(false);\n    };\n    Table.prototype.handleDragStart = function (e) {\n        var store = this.props.store;\n        var target = e.currentTarget;\n        var tr = (this.draggingTr = target.closest('tr'));\n        var id = tr.getAttribute('data-id');\n        var tbody = tr.parentNode;\n        this.originIndex = Array.prototype.indexOf.call(tbody.childNodes, tr);\n        tbody.classList.add('is-dragging');\n        tr.classList.add('is-dragging');\n        e.dataTransfer.effectAllowed = 'move';\n        e.dataTransfer.setData('text/plain', id);\n        e.dataTransfer.setDragImage(tr, 0, 0);\n        var item = store.getRowById(id);\n        store.collapseAllAtDepth(item.depth);\n        var siblings = store.rows;\n        if (item.parentId) {\n            var parent_1 = store.getRowById(item.parentId);\n            siblings = parent_1.children;\n        }\n        siblings = siblings.filter(function (sibling) { return sibling !== item; });\n        tbody.addEventListener('dragover', this.handleDragOver);\n        tbody.addEventListener('drop', this.handleDrop);\n        this.draggingSibling = siblings.map(function (item) {\n            var tr = tbody.querySelector(\":scope>tr[data-id=\\\"\".concat(item.id, \"\\\"]\"));\n            tr.classList.add('is-drop-allowed');\n            return tr;\n        });\n        tr.addEventListener('dragend', this.handleDragEnd);\n    };\n    Table.prototype.handleDragOver = function (e) {\n        if (!e.target) {\n            return;\n        }\n        e.preventDefault();\n        e.dataTransfer.dropEffect = 'move';\n        var overTr = e.target.closest('tr');\n        if (!overTr ||\n            !~overTr.className.indexOf('is-drop-allowed') ||\n            overTr === this.draggingTr ||\n            animation.animating) {\n            return;\n        }\n        var tbody = overTr.parentElement;\n        var tRect = overTr.getBoundingClientRect();\n        var next = (e.clientY - tRect.top) / (tRect.bottom - tRect.top) > 0.5;\n        animation.capture(tbody);\n        var before = next ? overTr.nextSibling : overTr;\n        before\n            ? tbody.insertBefore(this.draggingTr, before)\n            : tbody.appendChild(this.draggingTr);\n        animation.animateAll();\n    };\n    Table.prototype.handleDrop = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var store, tr, tbody, index, item;\n            return __generator(this, function (_a) {\n                store = this.props.store;\n                tr = this.draggingTr;\n                tbody = tr.parentElement;\n                index = Array.prototype.indexOf.call(tbody.childNodes, tr);\n                item = store.getRowById(tr.getAttribute('data-id'));\n                // destroy\n                this.handleDragEnd();\n                store.exchange(this.originIndex, index, item);\n                return [2 /*return*/];\n            });\n        });\n    };\n    Table.prototype.handleDragEnd = function () {\n        var tr = this.draggingTr;\n        var tbody = tr.parentElement;\n        var index = Array.prototype.indexOf.call(tbody.childNodes, tr);\n        tbody.insertBefore(tr, tbody.childNodes[index < this.originIndex ? this.originIndex + 1 : this.originIndex]);\n        tr.classList.remove('is-dragging');\n        tbody.classList.remove('is-dragging');\n        tr.removeEventListener('dragend', this.handleDragEnd);\n        tbody.removeEventListener('dragover', this.handleDragOver);\n        tbody.removeEventListener('drop', this.handleDrop);\n        this.draggingSibling.forEach(function (item) {\n            return item.classList.remove('is-drop-allowed');\n        });\n    };\n    Table.prototype.handleImageEnlarge = function (info, target) {\n        var onImageEnlarge = this.props.onImageEnlarge;\n        // 如果已经是多张了，直接跳过\n        if ((Array.isArray(info.list) && info.enlargeWithGallary !== true) ||\n            info.enlargeWithGallary === false) {\n            return onImageEnlarge && onImageEnlarge(info, target);\n        }\n        // 从列表中收集所有图片，然后作为一个图片集合派送出去。\n        var store = this.props.store;\n        var column = store.columns[target.colIndex].pristine;\n        var index = target.rowIndex;\n        var list = [];\n        store.rows.forEach(function (row, i) {\n            var src = resolveVariable(column.name, row.data);\n            if (!src) {\n                if (i < target.rowIndex) {\n                    index--;\n                }\n                return;\n            }\n            var images = Array.isArray(src) ? src : [src];\n            list = list.concat(images.map(function (item) { return ({\n                src: item,\n                originalSrc: column.originalSrc\n                    ? filter(column.originalSrc, row.data)\n                    : item,\n                title: column.enlargeTitle\n                    ? filter(column.enlargeTitle, row.data)\n                    : column.title\n                        ? filter(column.title, row.data)\n                        : undefined,\n                caption: column.enlargeCaption\n                    ? filter(column.enlargeCaption, row.data)\n                    : column.caption\n                        ? filter(column.caption, row.data)\n                        : undefined\n            }); }));\n        });\n        if (list.length > 1) {\n            onImageEnlarge &&\n                onImageEnlarge(__assign(__assign({}, info), { list: list, index: index }), target);\n        }\n        else {\n            onImageEnlarge && onImageEnlarge(info, target);\n        }\n    };\n    // 开始列宽度调整\n    Table.prototype.handleColResizeMouseDown = function (e) {\n        this.lineStartX = e.clientX;\n        var currentTarget = e.currentTarget;\n        this.resizeLine = currentTarget;\n        var store = this.props.store;\n        var index = parseInt(this.resizeLine.getAttribute('data-index'), 10);\n        var column = store.columns[index];\n        this.lineStartWidth = column.realWidth || column.width;\n        this.resizeLine.classList.add('is-resizing');\n        document.addEventListener('mousemove', this.handleColResizeMouseMove);\n        document.addEventListener('mouseup', this.handleColResizeMouseUp);\n        // 防止选中文本\n        e.preventDefault();\n        e.stopPropagation();\n    };\n    // 垂直线拖拽移动\n    Table.prototype.handleColResizeMouseMove = function (e) {\n        var moveX = e.clientX - this.lineStartX;\n        var store = this.props.store;\n        var index = parseInt(this.resizeLine.getAttribute('data-index'), 10);\n        var column = store.columns[index];\n        column.setWidth(Math.max(this.lineStartWidth + moveX, 30, column.minWidth));\n    };\n    // 垂直线拖拽结束\n    Table.prototype.handleColResizeMouseUp = function (e) {\n        this.resizeLine.classList.remove('is-resizing');\n        delete this.resizeLine;\n        document.removeEventListener('mousemove', this.handleColResizeMouseMove);\n        document.removeEventListener('mouseup', this.handleColResizeMouseUp);\n    };\n    Table.prototype.handleColumnToggle = function (columns) {\n        var store = this.props.store;\n        store.updateColumns(columns);\n        store.persistSaveToggledColumns();\n    };\n    Table.prototype.renderAutoFilterForm = function () {\n        var _a = this.props, render = _a.render, store = _a.store, onSearchableFromReset = _a.onSearchableFromReset, onSearchableFromSubmit = _a.onSearchableFromSubmit, onSearchableFromInit = _a.onSearchableFromInit, cx = _a.classnames, __ = _a.translate, query = _a.query, data = _a.data, autoGenerateFilter = _a.autoGenerateFilter, testIdBuilder = _a.testIdBuilder, _b = _a.filterCanAccessSuperData, filterCanAccessSuperData = _b === void 0 ? true : _b;\n        var searchableColumns = store.searchableColumns;\n        if (!searchableColumns.length) {\n            return null;\n        }\n        return (React.createElement(AutoFilterForm, { store: store, query: query, data: data, translate: __, classnames: cx, render: render, canAccessSuperData: filterCanAccessSuperData, autoGenerateFilter: autoGenerateFilter, onSearchableFromReset: onSearchableFromReset, onSearchableFromSubmit: onSearchableFromSubmit, onSearchableFromInit: onSearchableFromInit, popOverContainer: this.getPopOverContainer, testIdBuilder: testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild('filter') }));\n    };\n    Table.prototype.renderHeading = function () {\n        var _a = this.props, title = _a.title, store = _a.store, hideQuickSaveBtn = _a.hideQuickSaveBtn, data = _a.data, cx = _a.classnames, saveImmediately = _a.saveImmediately, headingClassName = _a.headingClassName, quickSaveApi = _a.quickSaveApi, __ = _a.translate, columns = _a.columns;\n        // 当被修改列的 column 开启 quickEdit.saveImmediately 时，不展示提交、放弃按钮\n        var isModifiedColumnSaveImmediately = false;\n        if (store.modifiedRows.length === 1) {\n            var saveImmediatelyColumnNames = (columns === null || columns === void 0 ? void 0 : columns.map(function (column) { var _a; return ((_a = column === null || column === void 0 ? void 0 : column.quickEdit) === null || _a === void 0 ? void 0 : _a.saveImmediately) ? column === null || column === void 0 ? void 0 : column.name : ''; }).filter(function (a) { return a; })) || [];\n            var item = store.modifiedRows[0];\n            var diff = difference(item.data, item.pristine);\n            if (intersection(saveImmediatelyColumnNames, Object.keys(diff)).length) {\n                isModifiedColumnSaveImmediately = true;\n            }\n        }\n        if (title ||\n            (quickSaveApi &&\n                !saveImmediately &&\n                !isModifiedColumnSaveImmediately &&\n                store.modified &&\n                !hideQuickSaveBtn) ||\n            store.moved) {\n            return (React.createElement(\"div\", { className: cx('Table-heading', headingClassName), key: \"heading\" }, !saveImmediately &&\n                store.modified &&\n                !hideQuickSaveBtn &&\n                !isModifiedColumnSaveImmediately ? (React.createElement(\"span\", null,\n                __('Table.modified', {\n                    modified: store.modified\n                }),\n                React.createElement(\"button\", { type: \"button\", className: cx('Button Button--size-xs Button--success m-l-sm'), onClick: this.handleSave },\n                    React.createElement(Icon, { icon: \"check\", className: \"icon m-r-xs\" }),\n                    __('Form.submit')),\n                React.createElement(\"button\", { type: \"button\", className: cx('Button Button--size-xs Button--danger m-l-sm'), onClick: this.reset },\n                    React.createElement(Icon, { icon: \"close\", className: \"icon m-r-xs\" }),\n                    __('Table.discard')))) : store.moved ? (React.createElement(\"span\", null,\n                __('Table.moved', {\n                    moved: store.moved\n                }),\n                React.createElement(\"button\", { type: \"button\", className: cx('Button Button--xs Button--success m-l-sm'), onClick: this.handleSaveOrder },\n                    React.createElement(Icon, { icon: \"check\", className: \"icon m-r-xs\" }),\n                    __('Form.submit')),\n                React.createElement(\"button\", { type: \"button\", className: cx('Button Button--xs Button--danger m-l-sm'), onClick: this.reset },\n                    React.createElement(Icon, { icon: \"close\", className: \"icon m-r-xs\" }),\n                    __('Table.discard')))) : title ? (filter(title, data)) : ('')));\n        }\n        return null;\n    };\n    Table.prototype.renderHeadCell = function (column, props) {\n        var _this = this;\n        var _a, _b;\n        var _c = this.props, store = _c.store, query = _c.query, onQuery = _c.onQuery, render = _c.render, ns = _c.classPrefix, resizable = _c.resizable, cx = _c.classnames, autoGenerateFilter = _c.autoGenerateFilter, dispatchEvent = _c.dispatchEvent, data = _c.data, testIdBuilder = _c.testIdBuilder, __ = _c.translate;\n        // 注意，这里用关了哪些 store 里面的东西，TableContent 里面得也用一下\n        // 因为 renderHeadCell 是 TableContent 回调的，tableContent 不重新渲染，这里面也不会重新渲染\n        var tIdCell = testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild(\"head-cell-\".concat(column.name));\n        var style = __assign({}, props.style);\n        var _d = __read(store.getStickyStyles(column, store.filteredColumns), 2), stickyStyle = _d[0], stickyClassName = _d[1];\n        Object.assign(style, stickyStyle);\n        var resizeLine = (React.createElement(\"div\", { className: cx('Table-content-colDragLine'), key: \"resize-\".concat(column.id), \"data-index\": column.index, onMouseDown: this.handleColResizeMouseDown }));\n        // th 里面不应该设置\n        if (style === null || style === void 0 ? void 0 : style.width) {\n            delete style.width;\n        }\n        if (column.pristine.headerAlign) {\n            style.textAlign = column.pristine.headerAlign;\n        }\n        else if (column.pristine.align) {\n            style.textAlign = column.pristine.align;\n        }\n        var key = props.key, restProps = __rest(props, [\"key\"]);\n        if (column.type === '__checkme') {\n            return (React.createElement(\"th\", __assign({}, restProps, { key: key, style: style, className: cx(column.pristine.className, stickyClassName) }),\n                store.rows.length && store.multiple ? (React.createElement(Checkbox, { classPrefix: ns, partial: store.someChecked && !store.allChecked, checked: store.someChecked, disabled: store.isSelectionThresholdReached && !store.someChecked, onChange: this.handleCheckAll })) : ('\\u00A0'),\n                resizable === false ? null : resizeLine));\n        }\n        else if (column.type === '__dragme') {\n            return (React.createElement(\"th\", __assign({}, restProps, { key: key, style: style, className: cx(column.pristine.className, stickyClassName) })));\n        }\n        else if (column.type === '__expandme') {\n            return (React.createElement(\"th\", __assign({}, restProps, { key: key, style: style, className: cx(column.pristine.className, stickyClassName) }),\n                (store.footable &&\n                    (store.footable.expandAll === false || store.footable.accordion)) ||\n                    (store.expandConfig &&\n                        (store.expandConfig.expandAll === false ||\n                            store.expandConfig.accordion)) ? null : (React.createElement(\"a\", { className: cx('Table-expandBtn', store.allExpanded ? 'is-active' : ''), \n                    // data-tooltip=\"展开/收起全部\"\n                    // data-position=\"top\"\n                    onClick: store.toggleExpandAll },\n                    React.createElement(Icon, { icon: \"right-arrow-bold\", className: \"icon\" }))),\n                resizable === false ? null : resizeLine));\n        }\n        else if (column.type === '__index') {\n            return (React.createElement(\"th\", __assign({}, restProps, { key: key, style: style, className: cx(column.pristine.className, stickyClassName) }),\n                __('Table.index'),\n                resizable === false ? null : resizeLine));\n        }\n        var prefix = [];\n        var affix = [];\n        if (column.isPrimary && store.isNested) {\n            (store.footable &&\n                (store.footable.expandAll === false || store.footable.accordion)) ||\n                (store.expandConfig &&\n                    (store.expandConfig.expandAll === false ||\n                        store.expandConfig.accordion)) ||\n                prefix.push(React.createElement(\"a\", { key: \"expandBtn\", className: cx('Table-expandBtn2', store.allExpanded ? 'is-active' : ''), \n                    // data-tooltip=\"展开/收起全部\"\n                    // data-position=\"top\"\n                    onClick: store.toggleExpandAll },\n                    React.createElement(Icon, { icon: \"right-arrow-bold\", className: \"icon\" })));\n        }\n        if (column.searchable && column.name && !autoGenerateFilter) {\n            affix.push(React.createElement(HeadCellSearchDropDown, __assign({}, restProps, { key: \"table-head-search\" }, this.props, { onQuery: onQuery, name: column.name, searchable: column.searchable, type: column.type, data: query, testIdBuilder: tIdCell === null || tIdCell === void 0 ? void 0 : tIdCell.getChild('search'), popOverContainer: this.getPopOverContainer })));\n        }\n        if (column.sortable && column.name) {\n            affix.push(React.createElement(\"span\", __assign({}, restProps, { key: \"table-head-sort\", className: cx('TableCell-sortBtn'), onClick: function () { return __awaiter(_this, void 0, void 0, function () {\n                    var orderBy, orderDir, order, rendererEvent;\n                    return __generator(this, function (_a) {\n                        switch (_a.label) {\n                            case 0:\n                                orderBy = '';\n                                orderDir = '';\n                                if (column.name === store.orderBy) {\n                                    if (store.orderDir !== 'desc') {\n                                        // 升序之后降序\n                                        orderBy = column.name;\n                                        orderDir = 'desc';\n                                    }\n                                }\n                                else {\n                                    orderBy = column.name;\n                                }\n                                order = orderBy ? (orderDir ? 'desc' : 'asc') : '';\n                                return [4 /*yield*/, dispatchEvent('columnSort', createObject(data, {\n                                        orderBy: orderBy,\n                                        orderDir: order\n                                    }))];\n                            case 1:\n                                rendererEvent = _a.sent();\n                                if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {\n                                    return [2 /*return*/];\n                                }\n                                if (!onQuery ||\n                                    onQuery({\n                                        orderBy: orderBy,\n                                        orderDir: order\n                                    }) === false) {\n                                    store.changeOrder(orderBy, order);\n                                }\n                                return [2 /*return*/];\n                        }\n                    });\n                }); } }),\n                React.createElement(\"i\", { className: cx('TableCell-sortBtn--down', store.orderBy === column.name && store.orderDir === 'desc'\n                        ? 'is-active'\n                        : '') },\n                    React.createElement(Icon, { icon: \"sort-desc\", className: \"icon\" })),\n                React.createElement(\"i\", { className: cx('TableCell-sortBtn--up', store.orderBy === column.name && store.orderDir === 'asc'\n                        ? 'is-active'\n                        : '') },\n                    React.createElement(Icon, { icon: \"sort-asc\", className: \"icon\" })),\n                React.createElement(\"i\", { className: cx('TableCell-sortBtn--default', store.orderBy === column.name ? '' : 'is-active') },\n                    React.createElement(Icon, { icon: \"sort-default\", className: \"icon\" }))));\n        }\n        if (!column.searchable && column.filterable && column.name && onQuery) {\n            affix.push(React.createElement(HeadCellFilterDropDown, __assign({ key: \"table-head-filter\" }, this.props, { onQuery: onQuery, name: column.name, type: column.type, data: query, superData: createObject(data, query), filterable: column.filterable, popOverContainer: this.getPopOverContainer, testIdBuilder: tIdCell === null || tIdCell === void 0 ? void 0 : tIdCell.getChild('filter') })));\n        }\n        return (React.createElement(\"th\", __assign({}, restProps, { key: key, style: style, className: cx(props ? props.className : '', stickyClassName, {\n                'TableCell--sortable': column.sortable,\n                'TableCell--searchable': column.searchable,\n                'TableCell--filterable': column.filterable,\n                'Table-operationCell': column.type === 'operation'\n            }) }, tIdCell === null || tIdCell === void 0 ? void 0 : tIdCell.getTestId()),\n            prefix,\n            React.createElement(\"div\", { key: \"content\", className: cx(\"TableCell--title\", column.pristine.className, column.pristine.labelClassName), style: props.style },\n                ((_a = props.label) !== null && _a !== void 0 ? _a : column.label)\n                    ? render('tpl', (_b = props.label) !== null && _b !== void 0 ? _b : column.label)\n                    : null,\n                column.remark\n                    ? render('remark', {\n                        type: 'remark',\n                        tooltip: column.remark,\n                        container: this.getPopOverContainer\n                    })\n                    : null),\n            affix,\n            resizable === false ? null : resizeLine));\n    };\n    Table.prototype.renderCell = function (region, column, item, props, ignoreDrag) {\n        if (ignoreDrag === void 0) { ignoreDrag = false; }\n        var _a = this.props, render = _a.render, store = _a.store, ns = _a.classPrefix, cx = _a.classnames, canAccessSuperData = _a.canAccessSuperData, itemBadge = _a.itemBadge, translate = _a.translate, testIdBuilder = _a.testIdBuilder, filterItemIndex = _a.filterItemIndex;\n        return (React.createElement(Cell, { key: props.key, region: region, column: column, item: item, props: props, ignoreDrag: ignoreDrag, render: render, filterItemIndex: filterItemIndex, store: store, multiple: store.multiple, canAccessSuperData: canAccessSuperData, classnames: cx, classPrefix: ns, itemBadge: itemBadge, onCheck: this.handleCheck, onDragStart: this.handleDragStart, popOverContainer: this.getPopOverContainer, quickEditFormRef: this.subFormRef, onImageEnlarge: this.handleImageEnlarge, translate: translate, testIdBuilder: testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild(\"cell-\".concat(props.rowPath, \"-\").concat(column.index)) }));\n    };\n    Table.prototype.renderAffixHeader = function (tableClassName) {\n        var _this = this;\n        var _a = this.props, store = _a.store, affixHeader = _a.affixHeader, render = _a.render, cx = _a.classnames, autoFillHeight = _a.autoFillHeight, env = _a.env;\n        var hideHeader = store.filteredColumns.every(function (column) { return !column.label; });\n        var columnsGroup = store.columnGroup;\n        return affixHeader && !autoFillHeight ? (React.createElement(React.Fragment, null,\n            React.createElement(\"div\", { className: cx('Table-fixedTop', {\n                    'is-fakeHide': hideHeader\n                }) },\n                this.renderHeader(false),\n                this.renderHeading(),\n                store.columnWidthReady ? (React.createElement(\"div\", { className: cx('Table-wrapper') },\n                    React.createElement(\"table\", { ref: this.affixedTableRef, className: cx(tableClassName, store.tableLayout === 'fixed' ? 'is-layout-fixed' : '') },\n                        React.createElement(\"colgroup\", null, store.filteredColumns.map(function (column) {\n                            var style = {\n                                width: \"var(--Table-column-\".concat(column.index, \"-width)\")\n                            };\n                            if (store.tableLayout === 'auto') {\n                                style.minWidth = style.width;\n                            }\n                            return (React.createElement(\"col\", { \"data-index\": column.index, style: style, key: column.id }));\n                        })),\n                        React.createElement(\"thead\", null,\n                            columnsGroup.length ? (React.createElement(\"tr\", null, columnsGroup.map(function (item, index) {\n                                var _a = __read(store.getStickyStyles(item, columnsGroup), 2), stickyStyle = _a[0], stickyClassName = _a[1];\n                                return item.rowSpan === 1 ? ( // 如果是分组自己，则用 th 渲染\n                                React.createElement(\"th\", { key: index, \"data-index\": item.index, colSpan: item.colSpan, rowSpan: item.rowSpan, style: stickyStyle, className: stickyClassName }, item.label ? render('tpl', item.label) : null)) : (\n                                // 否则走 renderCell 因为不走的话，排序按钮不会渲染\n                                _this.renderHeadCell(item.has[0], {\n                                    'label': item.label,\n                                    'key': index,\n                                    'data-index': item.index,\n                                    'colSpan': item.colSpan,\n                                    'rowSpan': item.rowSpan,\n                                    'style': stickyStyle,\n                                    'className': stickyClassName\n                                }));\n                            }))) : null,\n                            React.createElement(\"tr\", null, store.filteredColumns.map(function (column) {\n                                var _a;\n                                return ((_a = columnsGroup.find(function (group) { return ~group.has.indexOf(column); })) === null || _a === void 0 ? void 0 : _a.rowSpan) === 2\n                                    ? null\n                                    : _this.renderHeadCell(column, {\n                                        'key': column.index,\n                                        'data-index': column.index\n                                    });\n                            })))))) : null))) : null;\n    };\n    Table.prototype.renderToolbar = function (toolbar) {\n        var type = toolbar.type || toolbar;\n        if (type === 'columns-toggler') {\n            this.renderedToolbars.push(type);\n            return this.renderColumnsToggler(toolbar);\n        }\n        else if (type === 'drag-toggler') {\n            this.renderedToolbars.push(type);\n            return this.renderDragToggler();\n        }\n        else if (type === 'export-excel') {\n            this.renderedToolbars.push(type);\n            return this.renderExportExcel(toolbar);\n        }\n        else if (type === 'export-excel-template') {\n            this.renderedToolbars.push(type);\n            return this.renderExportExcelTemplate(toolbar);\n        }\n        return void 0;\n    };\n    Table.prototype.renderColumnsToggler = function (config) {\n        var _this = this;\n        var _a;\n        var _b = this.props, className = _b.className, store = _b.store, ns = _b.classPrefix, cx = _b.classnames, affixRow = _b.affixRow, rest = __rest(_b, [\"className\", \"store\", \"classPrefix\", \"classnames\", \"affixRow\"]);\n        var __ = rest.translate;\n        var env = rest.env;\n        var render = this.props.render;\n        if (!store.columnsTogglable) {\n            return null;\n        }\n        return (React.createElement(ColumnToggler, __assign({}, rest, (isObject(config) ? config : {}), { tooltip: {\n                content: (config === null || config === void 0 ? void 0 : config.tooltip) || __('Table.columnsVisibility'),\n                placement: 'bottom'\n            }, tooltipContainer: rest.popOverContainer || env.getModalContainer, align: (_a = config === null || config === void 0 ? void 0 : config.align) !== null && _a !== void 0 ? _a : 'left', isActived: store.hasColumnHidden(), classnames: cx, classPrefix: ns, key: \"columns-toggable\", size: (config === null || config === void 0 ? void 0 : config.size) || 'sm', icon: config === null || config === void 0 ? void 0 : config.icon, label: config === null || config === void 0 ? void 0 : config.label, draggable: config === null || config === void 0 ? void 0 : config.draggable, columns: store.columnsData, activeToggaleColumns: store.activeToggaleColumns, onColumnToggle: this.handleColumnToggle }),\n            store.toggableColumns.length ? (React.createElement(\"li\", { className: cx('ColumnToggler-menuItem'), key: 'selectAll', onClick: function () { return __awaiter(_this, void 0, void 0, function () {\n                    var _a, data, dispatchEvent, allToggled, rendererEvent;\n                    return __generator(this, function (_b) {\n                        switch (_b.label) {\n                            case 0:\n                                _a = this.props, data = _a.data, dispatchEvent = _a.dispatchEvent;\n                                allToggled = !(store.activeToggaleColumns.length ===\n                                    store.toggableColumns.length);\n                                return [4 /*yield*/, dispatchEvent('columnToggled', createObject(data, {\n                                        columns: allToggled\n                                            ? store.toggableColumns.map(function (column) { return column.pristine; })\n                                            : []\n                                    }))];\n                            case 1:\n                                rendererEvent = _b.sent();\n                                if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {\n                                    return [2 /*return*/];\n                                }\n                                store.toggleAllColumns();\n                                return [2 /*return*/];\n                        }\n                    });\n                }); } },\n                React.createElement(Checkbox, { size: \"sm\", classPrefix: ns, key: \"checkall\", checked: !!store.activeToggaleColumns.length, partial: !!(store.activeToggaleColumns.length &&\n                        store.activeToggaleColumns.length !==\n                            store.toggableColumns.length) }, __('Select.checkAll')))) : null,\n            !(config === null || config === void 0 ? void 0 : config.draggable) &&\n                store.toggableColumns.map(function (column) { return (React.createElement(\"li\", { className: cx('ColumnToggler-menuItem'), key: column.index, onClick: function () { return __awaiter(_this, void 0, void 0, function () {\n                        var _a, data, dispatchEvent, columns, rendererEvent;\n                        return __generator(this, function (_b) {\n                            switch (_b.label) {\n                                case 0:\n                                    _a = this.props, data = _a.data, dispatchEvent = _a.dispatchEvent;\n                                    columns = store.activeToggaleColumns.map(function (item) { return item.pristine; });\n                                    if (!column.toggled) {\n                                        columns.push(column.pristine);\n                                    }\n                                    else {\n                                        columns = columns.filter(function (c) { return c.name !== column.pristine.name; });\n                                    }\n                                    return [4 /*yield*/, dispatchEvent('columnToggled', createObject(data, {\n                                            columns: columns\n                                        }))];\n                                case 1:\n                                    rendererEvent = _b.sent();\n                                    if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {\n                                        return [2 /*return*/];\n                                    }\n                                    column.toggleToggle();\n                                    return [2 /*return*/];\n                            }\n                        });\n                    }); } },\n                    React.createElement(Checkbox, { size: \"sm\", classPrefix: ns, checked: column.toggled }, column.label ? render('tpl', column.label) : null))); })));\n    };\n    Table.prototype.renderDragToggler = function () {\n        var _a = this.props, store = _a.store, env = _a.env, draggable = _a.draggable, ns = _a.classPrefix, __ = _a.translate, popOverContainer = _a.popOverContainer;\n        if (!draggable || store.isNested) {\n            return null;\n        }\n        return (React.createElement(Button, { disabled: !!store.modified, classPrefix: ns, key: \"dragging-toggle\", tooltip: { content: __('Table.startSort'), placement: 'bottom' }, tooltipContainer: popOverContainer || env.getModalContainer, size: \"sm\", active: store.dragging, onClick: function (e) {\n                e.preventDefault();\n                store.toggleDragging();\n                store.dragging && store.clear();\n            }, iconOnly: true },\n            React.createElement(Icon, { icon: \"exchange\", className: \"icon\" })));\n    };\n    Table.prototype.renderExportExcel = function (toolbar) {\n        var _this = this;\n        var _a = this.props, store = _a.store, __ = _a.translate, render = _a.render;\n        var columns = store.filteredColumns || [];\n        if (!columns) {\n            return null;\n        }\n        return render('exportExcel', __assign(__assign({ label: __('CRUD.exportExcel') }, toolbar), { type: 'button' }), {\n            loading: store.exportExcelLoading,\n            onAction: function () {\n                store.update({ exportExcelLoading: true });\n                import('exceljs').then(function (E) { return __awaiter(_this, void 0, void 0, function () {\n                    var ExcelJS, error_1;\n                    return __generator(this, function (_a) {\n                        switch (_a.label) {\n                            case 0:\n                                ExcelJS = E.default || E;\n                                _a.label = 1;\n                            case 1:\n                                _a.trys.push([1, 3, 4, 5]);\n                                return [4 /*yield*/, exportExcel(ExcelJS, this.props, toolbar)];\n                            case 2:\n                                _a.sent();\n                                return [3 /*break*/, 5];\n                            case 3:\n                                error_1 = _a.sent();\n                                console.error(error_1);\n                                return [3 /*break*/, 5];\n                            case 4:\n                                store.update({ exportExcelLoading: false });\n                                return [7 /*endfinally*/];\n                            case 5: return [2 /*return*/];\n                        }\n                    });\n                }); });\n            }\n        });\n    };\n    /**\n     * 导出 Excel 模板\n     */\n    Table.prototype.renderExportExcelTemplate = function (toolbar) {\n        var _this = this;\n        var _a = this.props, store = _a.store, __ = _a.translate, render = _a.render;\n        var columns = store.filteredColumns || [];\n        if (!columns) {\n            return null;\n        }\n        return render('exportExcelTemplate', __assign(__assign({ label: __('CRUD.exportExcelTemplate') }, toolbar), { type: 'button' }), {\n            onAction: function () {\n                import('exceljs').then(function (E) { return __awaiter(_this, void 0, void 0, function () {\n                    var ExcelJS, error_2;\n                    return __generator(this, function (_a) {\n                        switch (_a.label) {\n                            case 0:\n                                ExcelJS = E.default || E;\n                                _a.label = 1;\n                            case 1:\n                                _a.trys.push([1, 3, , 4]);\n                                return [4 /*yield*/, exportExcel(ExcelJS, this.props, toolbar, true)];\n                            case 2:\n                                _a.sent();\n                                return [3 /*break*/, 4];\n                            case 3:\n                                error_2 = _a.sent();\n                                console.error(error_2);\n                                return [3 /*break*/, 4];\n                            case 4: return [2 /*return*/];\n                        }\n                    });\n                }); });\n            }\n        });\n    };\n    Table.prototype.renderActions = function (region) {\n        var _this = this;\n        var _a = this.props, actions = _a.actions, render = _a.render, store = _a.store, cx = _a.classnames, data = _a.data;\n        actions = Array.isArray(actions) ? actions.concat() : [];\n        if (store.toggable &&\n            region === 'header' &&\n            !~this.renderedToolbars.indexOf('columns-toggler')) {\n            actions.push({\n                type: 'button',\n                children: this.renderColumnsToggler()\n            });\n        }\n        if (store.draggable &&\n            !store.isNested &&\n            region === 'header' &&\n            store.rows.length > 1 &&\n            !~this.renderedToolbars.indexOf('drag-toggler')) {\n            actions.push({\n                type: 'button',\n                children: this.renderDragToggler()\n            });\n        }\n        return Array.isArray(actions) && actions.length ? (React.createElement(\"div\", { className: cx('Table-actions') }, actions.map(function (action, key) {\n            return render(\"action/\".concat(key), __assign({ type: 'button' }, action), {\n                onAction: _this.handleAction,\n                key: key,\n                btnDisabled: store.dragging,\n                data: store.getData(data)\n            });\n        }))) : null;\n    };\n    Table.prototype.renderHeader = function (editable) {\n        var _a = this.props, header = _a.header, headerClassName = _a.headerClassName, toolbarClassName = _a.toolbarClassName, headerToolbarClassName = _a.headerToolbarClassName, headerToolbarRender = _a.headerToolbarRender, render = _a.render, showHeader = _a.showHeader, store = _a.store, cx = _a.classnames, data = _a.data, __ = _a.translate;\n        if (showHeader === false) {\n            return null;\n        }\n        var otherProps = {};\n        // editable === false && (otherProps.$$editable = false);\n        var child = headerToolbarRender\n            ? headerToolbarRender(__assign(__assign(__assign({}, this.props), store.eventContext), otherProps), this.renderToolbar)\n            : null;\n        var actions = this.renderActions('header');\n        var toolbarNode = actions || child || store.dragging ? (React.createElement(\"div\", { className: cx('Table-toolbar Table-headToolbar', toolbarClassName, headerToolbarClassName), key: \"header-toolbar\" },\n            actions,\n            child,\n            store.dragging ? (React.createElement(\"div\", { className: cx('Table-dragTip'), ref: this.dragTipRef }, __('Table.dragTip'))) : null)) : null;\n        var headerNode = header && (!Array.isArray(header) || header.length) ? (React.createElement(\"div\", { className: cx('Table-header', headerClassName), key: \"header\" }, render('header', header, __assign(__assign({}, (editable === false ? otherProps : null)), { data: store.getData(data) })))) : null;\n        return headerNode && toolbarNode\n            ? [headerNode, toolbarNode]\n            : headerNode || toolbarNode || null;\n    };\n    Table.prototype.renderFooter = function () {\n        var _a = this.props, footer = _a.footer, toolbarClassName = _a.toolbarClassName, footerToolbarClassName = _a.footerToolbarClassName, footerClassName = _a.footerClassName, footerToolbarRender = _a.footerToolbarRender, render = _a.render, showFooter = _a.showFooter, store = _a.store, data = _a.data, cx = _a.classnames, affixFooter = _a.affixFooter;\n        if (showFooter === false) {\n            return null;\n        }\n        var child = footerToolbarRender\n            ? footerToolbarRender(__assign(__assign({}, this.props), store.eventContext), this.renderToolbar)\n            : null;\n        var actions = this.renderActions('footer');\n        var footerNode = footer && (!Array.isArray(footer) || footer.length) ? (React.createElement(\"div\", { className: cx('Table-footer', footerClassName, affixFooter ? 'Table-footer--affix' : ''), key: \"footer\" }, render('footer', footer, {\n            data: store.getData(data)\n        }))) : null;\n        var toolbarNode = actions || child ? (React.createElement(\"div\", { className: cx('Table-toolbar Table-footToolbar', toolbarClassName, footerToolbarClassName, !footerNode && affixFooter ? 'Table-footToolbar--affix' : ''), key: \"footer-toolbar\" },\n            actions,\n            child)) : null;\n        return footerNode && toolbarNode\n            ? [toolbarNode, footerNode]\n            : footerNode || toolbarNode || null;\n    };\n    Table.prototype.renderTableContent = function () {\n        var _a = this.props, cx = _a.classnames, tableClassName = _a.tableClassName, store = _a.store, placeholder = _a.placeholder, render = _a.render, checkOnItemClick = _a.checkOnItemClick, buildItemProps = _a.buildItemProps, rowClassNameExpr = _a.rowClassNameExpr, rowClassName = _a.rowClassName, prefixRow = _a.prefixRow, locale = _a.locale, affixRow = _a.affixRow, tableContentClassName = _a.tableContentClassName, translate = _a.translate, itemAction = _a.itemAction, affixRowClassNameExpr = _a.affixRowClassNameExpr, affixRowClassName = _a.affixRowClassName, prefixRowClassNameExpr = _a.prefixRowClassNameExpr, prefixRowClassName = _a.prefixRowClassName, autoFillHeight = _a.autoFillHeight, affixHeader = _a.affixHeader, itemActions = _a.itemActions, dispatchEvent = _a.dispatchEvent, onEvent = _a.onEvent, loadingConfig = _a.loadingConfig, testIdBuilder = _a.testIdBuilder, data = _a.data;\n        // 理论上来说 store.rows 应该也行啊\n        // 不过目前看来只有这样写它才会重新更新视图\n        store.rows.length;\n        return (React.createElement(React.Fragment, null,\n            React.createElement(TableContent, { testIdBuilder: testIdBuilder, tableClassName: cx({\n                    'Table-table--checkOnItemClick': checkOnItemClick,\n                    'Table-table--withCombine': store.combineNum > 0,\n                    'Table-table--affixHeader': affixHeader && !autoFillHeight && store.columnWidthReady,\n                    'Table-table--tableFillHeight': autoFillHeight && !store.items.length\n                }, tableClassName), className: tableContentClassName, itemActions: itemActions, itemAction: itemAction, store: store, classnames: cx, columns: store.filteredColumns, columnsGroup: store.columnGroup, rows: store.items, placeholder: placeholder, render: render, onMouseMove: \n                // 如果没有 itemActions, 那么就不需要处理了。\n                Array.isArray(itemActions) && itemActions.length\n                    ? this.handleMouseMove\n                    : undefined, onScroll: this.handleOutterScroll, tableRef: this.tableRef, renderHeadCell: this.renderHeadCell, renderCell: this.renderCell, onCheck: this.handleCheck, onRowClick: this.handleRowClick, onRowDbClick: this.handleRowDbClick, onRowMouseEnter: this.handleRowMouseEnter, onRowMouseLeave: this.handleRowMouseLeave, onQuickChange: store.dragging ? undefined : this.handleQuickChange, footable: store.footable, footableColumns: store.footableColumns, checkOnItemClick: checkOnItemClick, buildItemProps: buildItemProps, onAction: this.handleAction, rowClassNameExpr: rowClassNameExpr, rowClassName: rowClassName, data: store.data, prefixRow: prefixRow, affixRow: affixRow, prefixRowClassName: prefixRowClassName, affixRowClassName: affixRowClassName, locale: locale, translate: translate, dispatchEvent: dispatchEvent, onEvent: onEvent, loading: store.loading }, renderItemActions({\n                store: store,\n                classnames: cx,\n                render: render,\n                itemActions: itemActions\n            })),\n            React.createElement(Spinner, { loadingConfig: loadingConfig, overlay: true, show: store.loading })));\n    };\n    Table.prototype.render = function () {\n        var _a = this.props, className = _a.className, style = _a.style, store = _a.store, cx = _a.classnames, affixColumns = _a.affixColumns, affixHeader = _a.affixHeader, autoFillHeight = _a.autoFillHeight, autoGenerateFilter = _a.autoGenerateFilter, mobileUI = _a.mobileUI, testIdBuilder = _a.testIdBuilder, id = _a.id;\n        this.renderedToolbars = []; // 用来记录哪些 toolbar 已经渲染了，已经渲染了就不重复渲染了。\n        var heading = affixHeader && !autoFillHeight ? null : this.renderHeading();\n        var header = affixHeader && !autoFillHeight ? null : this.renderHeader();\n        var footer = this.renderFooter();\n        var tableClassName = cx('Table-table', this.props.tableClassName, {\n            'Table-table--withCombine': store.combineNum > 0\n        });\n        return (React.createElement(\"div\", __assign({ ref: this.dom, className: cx('Table', { 'is-mobile': mobileUI }, className, {\n                'Table--unsaved': !!store.modified || !!store.moved,\n                'Table--autoFillHeight': autoFillHeight\n            }), style: store.buildStyles(style), \"data-id\": id }, testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getTestId()),\n            autoGenerateFilter ? this.renderAutoFilterForm() : null,\n            this.renderAffixHeader(tableClassName),\n            header,\n            heading,\n            React.createElement(\"div\", { className: cx('Table-contentWrap'), onMouseLeave: this.handleMouseLeave }, this.renderTableContent()),\n            footer));\n    };\n    Table.contextType = ScopedContext;\n    Table.propsList = [\n        'header',\n        'headerToolbarRender',\n        'footer',\n        'footerToolbarRender',\n        'footable',\n        'expandConfig',\n        'placeholder',\n        'tableClassName',\n        'headingClassName',\n        'source',\n        'selectable',\n        'columnsTogglable',\n        'affixHeader',\n        'affixColumns',\n        'headerClassName',\n        'footerClassName',\n        'selected',\n        'multiple',\n        'primaryField',\n        'hideQuickSaveBtn',\n        'itemCheckableOn',\n        'itemDraggableOn',\n        'draggable',\n        'checkOnItemClick',\n        'hideCheckToggler',\n        'itemAction',\n        'itemActions',\n        'combineNum',\n        'combineFromIndex',\n        'items',\n        'columns',\n        'valueField',\n        'saveImmediately',\n        'rowClassName',\n        'rowClassNameExpr',\n        'affixRowClassNameExpr',\n        'prefixRowClassNameExpr',\n        'popOverContainer',\n        'headerToolbarClassName',\n        'toolbarClassName',\n        'footerToolbarClassName',\n        'itemBadge',\n        'autoFillHeight',\n        'onSelect',\n        'keepItemSelectionOnPageChange',\n        'maxKeepItemSelectionLength',\n        'maxItemSelectionLength',\n        'autoGenerateFilter'\n    ];\n    Table.defaultProps = {\n        className: '',\n        placeholder: 'placeholder.noData',\n        tableClassName: '',\n        source: '$items',\n        selectable: false,\n        columnsTogglable: 'auto',\n        affixHeader: true,\n        headerClassName: '',\n        footerClassName: '',\n        toolbarClassName: '',\n        headerToolbarClassName: '',\n        footerToolbarClassName: '',\n        primaryField: 'id',\n        itemCheckableOn: '',\n        itemDraggableOn: '',\n        hideCheckToggler: false,\n        canAccessSuperData: false,\n        resizable: true\n    };\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object]),\n        __metadata(\"design:returntype\", Promise)\n    ], Table.prototype, \"loadDeferredRow\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], Table.prototype, \"handleDragStart\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], Table.prototype, \"handleDragOver\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", []),\n        __metadata(\"design:returntype\", Promise)\n    ], Table.prototype, \"handleDrop\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", []),\n        __metadata(\"design:returntype\", void 0)\n    ], Table.prototype, \"handleDragEnd\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object, Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], Table.prototype, \"handleImageEnlarge\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], Table.prototype, \"handleColResizeMouseDown\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [MouseEvent]),\n        __metadata(\"design:returntype\", void 0)\n    ], Table.prototype, \"handleColResizeMouseMove\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [MouseEvent]),\n        __metadata(\"design:returntype\", void 0)\n    ], Table.prototype, \"handleColResizeMouseUp\", null);\n    return Table;\n}(React.Component));\nvar TableRendererBase = /** @class */ (function (_super) {\n    __extends(TableRendererBase, _super);\n    function TableRendererBase() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    TableRendererBase.prototype.receive = function (values, subPath) {\n        var _a, _b, _c;\n        var scoped = this.context;\n        /**\n         * 因为Table在scope上注册，导致getComponentByName查询组件时会优先找到Table，和CRUD联动的动作都会失效\n         * 这里先做兼容处理，把动作交给上层的CRUD处理\n         */\n        if ((_a = this.props) === null || _a === void 0 ? void 0 : _a.host) {\n            // CRUD会把自己透传给Table，这样可以保证找到CRUD\n            return (_c = (_b = this.props.host).receive) === null || _c === void 0 ? void 0 : _c.call(_b, values, subPath);\n        }\n        if (subPath) {\n            return scoped.send(subPath, values);\n        }\n    };\n    /**\n     * 通过 index 或者 condition 获取需要处理的目标\n     *\n     * - index 支持数字\n     * - index 支持逗号分隔的数字列表\n     * - index 支持路径比如 0.1.2,0.1.3\n     * - index 支持表达式，比如 0.1.2,${index}\n     *\n     * - condition 上下文为当前行的数据\n     *\n     * @param ctx\n     * @param index\n     * @param condition\n     * @returns\n     */\n    TableRendererBase.prototype.getEventTargets = function (ctx, index, condition, oldCondition) {\n        return __awaiter(this, void 0, void 0, function () {\n            var store;\n            return __generator(this, function (_a) {\n                store = this.props.store;\n                return [2 /*return*/, getMatchedEventTargets(store.rows, ctx || this.props.data, index, condition, oldCondition)];\n            });\n        });\n    };\n    TableRendererBase.prototype.reload = function (subPath, query, ctx, silent, replace, args) {\n        var _a, _b, _c;\n        return __awaiter(this, void 0, void 0, function () {\n            var targets, scoped;\n            var _this = this;\n            return __generator(this, function (_d) {\n                switch (_d.label) {\n                    case 0:\n                        if (!((args === null || args === void 0 ? void 0 : args.index) || (args === null || args === void 0 ? void 0 : args.condition))) return [3 /*break*/, 3];\n                        return [4 /*yield*/, this.getEventTargets(ctx || this.props.data, args.index, args === null || args === void 0 ? void 0 : args.condition)];\n                    case 1:\n                        targets = _d.sent();\n                        return [4 /*yield*/, Promise.all(targets.map(function (target) { return _this.loadDeferredRow(target); }))];\n                    case 2:\n                        _d.sent();\n                        return [2 /*return*/];\n                    case 3:\n                        scoped = this.context;\n                        if ((_a = this.props) === null || _a === void 0 ? void 0 : _a.host) {\n                            // CRUD会把自己透传给Table，这样可以保证找到CRUD\n                            return [2 /*return*/, (_c = (_b = this.props.host).reload) === null || _c === void 0 ? void 0 : _c.call(_b, subPath, query, ctx)];\n                        }\n                        if (subPath) {\n                            return [2 /*return*/, scoped.reload(subPath, ctx)];\n                        }\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    TableRendererBase.prototype.setData = function (values, replace, index, condition) {\n        var _a, _b, _c, _d;\n        return __awaiter(this, void 0, void 0, function () {\n            var store, targets, data;\n            return __generator(this, function (_e) {\n                switch (_e.label) {\n                    case 0:\n                        store = this.props.store;\n                        if (!(index !== undefined || condition !== undefined)) return [3 /*break*/, 2];\n                        return [4 /*yield*/, this.getEventTargets(this.props.data, index, condition)];\n                    case 1:\n                        targets = _e.sent();\n                        targets.forEach(function (target) {\n                            target.updateData(values);\n                        });\n                        return [3 /*break*/, 3];\n                    case 2:\n                        if ((_a = this.props) === null || _a === void 0 ? void 0 : _a.host) {\n                            // 如果在 CRUD 里面，优先让 CRUD 去更新状态\n                            return [2 /*return*/, (_c = (_b = this.props.host).setData) === null || _c === void 0 ? void 0 : _c.call(_b, values, replace, index, condition)];\n                        }\n                        else {\n                            data = __assign(__assign({}, values), { rows: (_d = values.rows) !== null && _d !== void 0 ? _d : values.items // 做个兼容\n                             });\n                            return [2 /*return*/, store.updateData(data, undefined, replace)];\n                        }\n                        _e.label = 3;\n                    case 3: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    TableRendererBase.prototype.getData = function () {\n        var _a = this.props, store = _a.store, data = _a.data;\n        return store.getData(data);\n    };\n    TableRendererBase.prototype.hasModifiedItems = function () {\n        return this.props.store.modified;\n    };\n    TableRendererBase.prototype.doAction = function (action, ctx, throwErrors, args) {\n        return __awaiter(this, void 0, void 0, function () {\n            var _a, store, valueField, data, actionType, _b, rows, targets, targets2;\n            return __generator(this, function (_c) {\n                switch (_c.label) {\n                    case 0:\n                        _a = this.props, store = _a.store, valueField = _a.valueField, data = _a.data;\n                        actionType = action === null || action === void 0 ? void 0 : action.actionType;\n                        _b = actionType;\n                        switch (_b) {\n                            case 'selectAll': return [3 /*break*/, 1];\n                            case 'clearAll': return [3 /*break*/, 2];\n                            case 'select': return [3 /*break*/, 3];\n                            case 'initDrag': return [3 /*break*/, 5];\n                            case 'cancelDrag': return [3 /*break*/, 6];\n                            case 'submitQuickEdit': return [3 /*break*/, 7];\n                            case 'toggleExpanded': return [3 /*break*/, 8];\n                            case 'setExpanded': return [3 /*break*/, 10];\n                        }\n                        return [3 /*break*/, 12];\n                    case 1:\n                        store.clear();\n                        store.toggleAll();\n                        this.syncSelected();\n                        return [3 /*break*/, 13];\n                    case 2:\n                        store.clear();\n                        this.syncSelected();\n                        return [3 /*break*/, 13];\n                    case 3: return [4 /*yield*/, this.getEventTargets(ctx, args.index, args.condition, args.selected)];\n                    case 4:\n                        rows = _c.sent();\n                        store.updateSelected(rows.map(function (item) { return item.data; }), valueField);\n                        this.syncSelected();\n                        return [3 /*break*/, 13];\n                    case 5:\n                        store.startDragging();\n                        return [3 /*break*/, 13];\n                    case 6:\n                        store.stopDragging();\n                        return [3 /*break*/, 13];\n                    case 7:\n                        this.handleSave();\n                        return [3 /*break*/, 13];\n                    case 8: return [4 /*yield*/, this.getEventTargets(ctx, args.index, args.condition)];\n                    case 9:\n                        targets = _c.sent();\n                        targets.forEach(function (target) {\n                            store.toggleExpanded(target);\n                        });\n                        return [3 /*break*/, 13];\n                    case 10: return [4 /*yield*/, this.getEventTargets(ctx, args.index, args.condition)];\n                    case 11:\n                        targets2 = _c.sent();\n                        targets2.forEach(function (target) {\n                            store.setExpanded(target, !!args.value);\n                        });\n                        return [3 /*break*/, 13];\n                    case 12: return [2 /*return*/, this.handleAction(undefined, action, data)];\n                    case 13: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    return TableRendererBase;\n}(Table));\nvar TableRenderer = /** @class */ (function (_super) {\n    __extends(TableRenderer, _super);\n    function TableRenderer() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    TableRenderer = __decorate([\n        Renderer({\n            type: 'table',\n            storeType: TableStore.name,\n            name: 'table'\n        })\n    ], TableRenderer);\n    return TableRenderer;\n}(TableRendererBase));\n\nexport { TableRenderer, TableRendererBase, Table as default };\n", "/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __rest, __assign, __spreadArray, __read, __decorate } from 'tslib';\nimport React from 'react';\nimport { isPureVariable, resolveVariableAndFilter, ColorScale, Renderer } from 'amis-core';\nimport { HocQuickEdit } from '../QuickEdit.js';\nimport { HocCopyable } from '../Copyable.js';\nimport { HocPopOver } from '../PopOver.js';\nimport { observer } from 'mobx-react';\nimport omit from 'lodash/omit';\nimport { Badge } from 'amis-ui';\n\nvar TableCell = /** @class */ (function (_super) {\n    __extends(TableCell, _super);\n    function TableCell() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.propsNeedRemove = [];\n        return _this;\n    }\n    TableCell.prototype.render = function () {\n        var _a = this.props, cx = _a.classnames, className = _a.className, classNameExpr = _a.classNameExpr, render = _a.render, _b = _a.style, style = _b === void 0 ? {} : _b, Component = _a.wrapperComponent, contentsOnly = _a.contentsOnly, column = _a.column, value = _a.value, data = _a.data, children = _a.children, width = _a.width, align = _a.align, vAlign = _a.vAlign, innerClassName = _a.innerClassName, label = _a.label, tabIndex = _a.tabIndex, onKeyUp = _a.onKeyUp, rowSpan = _a.rowSpan, _body = _a.body, tpl = _a.tpl, remark = _a.remark, cellPrefix = _a.cellPrefix, cellAffix = _a.cellAffix, isHead = _a.isHead, colIndex = _a.colIndex, row = _a.row, showBadge = _a.showBadge, itemBadge = _a.itemBadge, textOverflow = _a.textOverflow, testIdBuilder = _a.testIdBuilder, rest = __rest(_a, [\"classnames\", \"className\", \"classNameExpr\", \"render\", \"style\", \"wrapperComponent\", \"contentsOnly\", \"column\", \"value\", \"data\", \"children\", \"width\", \"align\", \"vAlign\", \"innerClassName\", \"label\", \"tabIndex\", \"onKeyUp\", \"rowSpan\", \"body\", \"tpl\", \"remark\", \"cellPrefix\", \"cellAffix\", \"isHead\", \"colIndex\", \"row\", \"showBadge\", \"itemBadge\", \"textOverflow\", \"testIdBuilder\"]);\n        if (isHead) {\n            Component = 'th';\n        }\n        else {\n            Component = Component || 'td';\n        }\n        var isTableCell = Component === 'td' || Component === 'th';\n        var schema = __assign(__assign({}, column), { \n            // 因为列本身已经做过显隐判断了，单元格不应该再处理\n            visibleOn: '', hiddenOn: '', visible: true, hidden: false, style: column.innerStyle, className: innerClassName, type: (column && column.type) || 'plain' });\n        // 如果本来就是 type 为 button，不要删除，其他情况下都应该删除。\n        if (schema.type !== 'button' && schema.type !== 'dropdown-button') {\n            delete schema.label;\n        }\n        var body = children\n            ? children\n            : render('field', schema, __assign(__assign({}, omit(rest, Object.keys(schema), this.propsNeedRemove)), { \n                // inputOnly 属性不能传递给子组件，在 SchemaRenderer.renderChild 中处理掉了\n                inputOnly: true, value: value, data: data }));\n        if (isTableCell) {\n            // table Cell 会用 colGroup 来设置宽度，这里不需要再设置\n            // 同时剔除style中的定位相关样式，避免表格样式异常\n            style = omit(style, ['width', 'position', 'display']);\n        }\n        else if (width) {\n            style = __assign(__assign({}, style), { width: (style && style.width) || width });\n        }\n        if (align) {\n            style = __assign(__assign({}, style), { textAlign: align });\n        }\n        if (vAlign) {\n            style = __assign(__assign({}, style), { verticalAlign: vAlign });\n        }\n        if (column.backgroundScale) {\n            var backgroundScale = column.backgroundScale;\n            var min = backgroundScale.min;\n            var max = backgroundScale.max;\n            if (isPureVariable(min)) {\n                min = resolveVariableAndFilter(min, data, '| raw');\n            }\n            if (isPureVariable(max)) {\n                max = resolveVariableAndFilter(max, data, '| raw');\n            }\n            if (typeof min === 'undefined') {\n                min = Math.min.apply(Math, __spreadArray([], __read(data.rows.map(function (r) { return r[column.name]; })), false));\n            }\n            if (typeof max === 'undefined') {\n                max = Math.max.apply(Math, __spreadArray([], __read(data.rows.map(function (r) { return r[column.name]; })), false));\n            }\n            var colorScale = new ColorScale(min, max, backgroundScale.colors || ['#FFEF9C', '#FF7127']);\n            var value_1 = data[column.name];\n            if (isPureVariable(backgroundScale.source)) {\n                value_1 = resolveVariableAndFilter(backgroundScale.source, data, '| raw');\n            }\n            var color = colorScale.getColor(Number(value_1)).toHexString();\n            style.background = color;\n        }\n        if (contentsOnly) {\n            return body;\n        }\n        return (React.createElement(Component, __assign({ rowSpan: rowSpan > 1 ? rowSpan : undefined, style: style, className: cx(className), tabIndex: tabIndex, onKeyUp: onKeyUp }, testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild('cell').getTestId()),\n            showBadge ? (React.createElement(Badge, { classnames: cx, badge: __assign(__assign({}, itemBadge), { className: cx(\"Table-badge\", itemBadge === null || itemBadge === void 0 ? void 0 : itemBadge.className) }), data: row.data })) : null,\n            cellPrefix,\n            textOverflow === 'ellipsis' && width ? (React.createElement(\"div\", { className: cx(\"TableCell-ellipsis\") }, body)) : (body),\n            cellAffix));\n    };\n    TableCell.defaultProps = {\n        wrapperComponent: 'td'\n    };\n    TableCell.propsList = [\n        'type',\n        'label',\n        'column',\n        'body',\n        'tpl',\n        'rowSpan',\n        'remark',\n        'contentsOnly'\n    ];\n    return TableCell;\n}(React.Component));\nvar TableCellRenderer = /** @class */ (function (_super) {\n    __extends(TableCellRenderer, _super);\n    function TableCellRenderer() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    TableCellRenderer.propsList = __spreadArray([\n        'quickEdit',\n        'quickEditEnabledOn',\n        'popOver',\n        'copyable',\n        'inline'\n    ], __read(TableCell.propsList), false);\n    TableCellRenderer = __decorate([\n        Renderer({\n            type: 'cell',\n            name: 'table-cell'\n        }),\n        HocPopOver({\n            targetOutter: true\n        }),\n        HocQuickEdit(),\n        HocCopyable(),\n        observer\n    ], TableCellRenderer);\n    return TableCellRenderer;\n}(TableCell));\nvar FieldRenderer = /** @class */ (function (_super) {\n    __extends(FieldRenderer, _super);\n    function FieldRenderer() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    FieldRenderer.defaultProps = __assign(__assign({}, TableCell.defaultProps), { wrapperComponent: 'div' });\n    FieldRenderer = __decorate([\n        Renderer({\n            type: 'field',\n            name: 'field'\n        }),\n        HocPopOver(),\n        HocCopyable()\n    ], FieldRenderer);\n    return FieldRenderer;\n}(TableCell));\n\nexport { FieldRenderer, TableCell, TableCellRenderer };\n", "/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __assign, __rest } from 'tslib';\nimport React from 'react';\nimport { findDOMNode } from 'react-dom';\nimport { difference, setVariable, Overlay, PopOver, getRendererByName, noop, getPropValue } from 'amis-core';\nimport hoistNonReactStatic from 'hoist-non-react-statics';\nimport keycode from 'keycode';\nimport omit from 'lodash/omit';\n\n/**\n * @file scoped.jsx.\n * <AUTHOR>\n */\nvar inited = false;\nvar currentOpened;\nvar HocQuickEdit = function (config) {\n    if (config === void 0) { config = {}; }\n    return function (Component) {\n        var QuickEditComponent = /** @class */ (function (_super) {\n            __extends(QuickEditComponent, _super);\n            function QuickEditComponent(props) {\n                var _this = _super.call(this, props) || this;\n                _this.openQuickEdit = _this.openQuickEdit.bind(_this);\n                _this.closeQuickEdit = _this.closeQuickEdit.bind(_this);\n                _this.handleAction = _this.handleAction.bind(_this);\n                _this.handleSubmit = _this.handleSubmit.bind(_this);\n                _this.handleKeyUp = _this.handleKeyUp.bind(_this);\n                _this.overlayRef = _this.overlayRef.bind(_this);\n                _this.handleWindowKeyPress = _this.handleWindowKeyPress.bind(_this);\n                _this.handleWindowKeyDown = _this.handleWindowKeyDown.bind(_this);\n                _this.formRef = _this.formRef.bind(_this);\n                _this.formItemRef = _this.formItemRef.bind(_this);\n                _this.handleInit = _this.handleInit.bind(_this);\n                _this.handleChange = _this.handleChange.bind(_this);\n                _this.handleFormItemChange = _this.handleFormItemChange.bind(_this);\n                _this.handleBulkChange = _this.handleBulkChange.bind(_this);\n                _this.state = {\n                    isOpened: false\n                };\n                return _this;\n            }\n            QuickEditComponent.prototype.componentDidMount = function () {\n                this.target = findDOMNode(this);\n                if (inited) {\n                    return;\n                }\n                inited = true;\n                document.body.addEventListener('keypress', this.handleWindowKeyPress);\n                document.body.addEventListener('keydown', this.handleWindowKeyDown);\n            };\n            QuickEditComponent.prototype.formRef = function (ref) {\n                var _a = this.props, quickEditFormRef = _a.quickEditFormRef, rowIndex = _a.rowIndex, colIndex = _a.colIndex;\n                while (ref && ref.getWrappedInstance) {\n                    ref = ref.getWrappedInstance();\n                }\n                this.form = ref;\n                quickEditFormRef === null || quickEditFormRef === void 0 ? void 0 : quickEditFormRef(ref, colIndex, rowIndex);\n            };\n            QuickEditComponent.prototype.formItemRef = function (ref) {\n                var _a = this.props, quickEditFormItemRef = _a.quickEditFormItemRef, rowIndex = _a.rowIndex, colIndex = _a.colIndex;\n                while (ref && ref.getWrappedInstance) {\n                    ref = ref.getWrappedInstance();\n                }\n                this.formItem = ref;\n                quickEditFormItemRef === null || quickEditFormItemRef === void 0 ? void 0 : quickEditFormItemRef(ref, colIndex, rowIndex);\n            };\n            QuickEditComponent.prototype.handleWindowKeyPress = function (e) {\n                var ns = this.props.classPrefix;\n                var el = e.target.closest(\".\".concat(ns, \"Field--quickEditable\"));\n                if (!el) {\n                    return;\n                }\n                var table = el.closest('table');\n                if (!table) {\n                    return;\n                }\n                if (keycode(e) === 'space' &&\n                    !~['INPUT', 'TEXTAREA'].indexOf(el.tagName)) {\n                    e.preventDefault();\n                    e.stopPropagation();\n                }\n            };\n            QuickEditComponent.prototype.handleWindowKeyDown = function (e) {\n                var code = keycode(e);\n                if (code === 'esc' && currentOpened) {\n                    currentOpened.closeQuickEdit();\n                }\n                else if (~['INPUT', 'TEXTAREA'].indexOf(e.target.tagName) ||\n                    e.target.contentEditable === 'true' ||\n                    !~['up', 'down', 'left', 'right'].indexOf(code)) {\n                    return;\n                }\n                e.preventDefault();\n                var ns = this.props.classPrefix;\n                var el = e.target.closest(\".\".concat(ns, \"Field--quickEditable\")) ||\n                    document.querySelector(\".\".concat(ns, \"Field--quickEditable\"));\n                if (!el) {\n                    return;\n                }\n                var table = el.closest('table');\n                if (!table) {\n                    return;\n                }\n                var current = table.querySelector(\".\".concat(ns, \"Field--quickEditable:focus\"));\n                if (!current) {\n                    var dom = table.querySelector(\".\".concat(ns, \"Field--quickEditable[tabindex]\"));\n                    dom && dom.focus();\n                }\n                else {\n                    var prevTr = void 0, nextTr = void 0, prevTd = void 0, nextTd = void 0;\n                    switch (code) {\n                        case 'up':\n                            prevTr = current.parentNode\n                                .previousSibling;\n                            if (prevTr) {\n                                var index = current.cellIndex;\n                                prevTr.children[index].focus();\n                            }\n                            break;\n                        case 'down':\n                            nextTr = current.parentNode\n                                .nextSibling;\n                            if (nextTr) {\n                                var index = current.cellIndex;\n                                nextTr.children[index].focus();\n                            }\n                            break;\n                        case 'left':\n                            prevTd = current.previousElementSibling;\n                            while (prevTd) {\n                                if (prevTd.matches(\".\".concat(ns, \"Field--quickEditable[tabindex]\"))) {\n                                    break;\n                                }\n                                prevTd = prevTd.previousElementSibling;\n                            }\n                            if (prevTd) {\n                                prevTd.focus();\n                            }\n                            else if (current.parentNode.previousSibling) {\n                                var tds = current.parentNode\n                                    .previousSibling.querySelectorAll(\".\".concat(ns, \"Field--quickEditable[tabindex]\"));\n                                if (tds.length) {\n                                    tds[tds.length - 1].focus();\n                                }\n                            }\n                            break;\n                        case 'right':\n                            nextTd = current.nextSibling;\n                            while (nextTd) {\n                                if (nextTd.matches(\".\".concat(ns, \"Field--quickEditable[tabindex]\"))) {\n                                    break;\n                                }\n                                nextTd = nextTd.nextSibling;\n                            }\n                            if (nextTd) {\n                                nextTd.focus();\n                            }\n                            else if (current.parentNode.nextSibling) {\n                                nextTd = current.parentNode.nextSibling.querySelector(\".\".concat(ns, \"Field--quickEditable[tabindex]\"));\n                                if (nextTd) {\n                                    nextTd.focus();\n                                }\n                            }\n                            break;\n                    }\n                }\n            };\n            // handleClickOutside() {\n            //     this.closeQuickEdit();\n            // }\n            QuickEditComponent.prototype.overlayRef = function (ref) {\n                this.overlay = ref;\n            };\n            QuickEditComponent.prototype.handleAction = function (e, action, ctx) {\n                var onAction = this.props.onAction;\n                if (action.actionType === 'cancel' || action.actionType === 'close') {\n                    this.closeQuickEdit();\n                    return;\n                }\n                onAction && onAction(e, action, ctx);\n            };\n            QuickEditComponent.prototype.handleSubmit = function (values) {\n                var _a = this.props, onQuickChange = _a.onQuickChange, quickEdit = _a.quickEdit;\n                this.closeQuickEdit();\n                onQuickChange(values, quickEdit.saveImmediately, false, quickEdit);\n                return false;\n            };\n            QuickEditComponent.prototype.handleInit = function (values) {\n                var _a = this.props, onQuickChange = _a.onQuickChange, data = _a.data;\n                var diff = difference(values, data);\n                Object.keys(diff).length && onQuickChange(diff, false, true);\n            };\n            QuickEditComponent.prototype.handleChange = function (values, diff) {\n                var _a = this.props, onQuickChange = _a.onQuickChange, quickEdit = _a.quickEdit;\n                Object.keys(diff).length &&\n                    onQuickChange(diff, // 只变化差异部分，其他值有可能是旧的\n                    quickEdit.saveImmediately, false, quickEdit);\n            };\n            QuickEditComponent.prototype.handleFormItemChange = function (value) {\n                var _a = this.props, onQuickChange = _a.onQuickChange, quickEdit = _a.quickEdit, name = _a.name;\n                var data = {};\n                setVariable(data, name, value);\n                onQuickChange(data, quickEdit.saveImmediately, false, quickEdit);\n            };\n            // autoFill 是通过 onBulkChange 触发的\n            // quickEdit 需要拦截这个，否则修改的数据就是错的\n            QuickEditComponent.prototype.handleBulkChange = function (values) {\n                var _a = this.props, onQuickChange = _a.onQuickChange, quickEdit = _a.quickEdit;\n                onQuickChange(values, quickEdit.saveImmediately, false, quickEdit);\n            };\n            QuickEditComponent.prototype.openQuickEdit = function () {\n                currentOpened = this;\n                this.setState({\n                    isOpened: true\n                });\n            };\n            QuickEditComponent.prototype.closeQuickEdit = function () {\n                var _this = this;\n                if (!this.state.isOpened) {\n                    return;\n                }\n                currentOpened = null;\n                var ns = this.props.classPrefix;\n                this.setState({\n                    isOpened: false\n                }, function () {\n                    var el = findDOMNode(_this);\n                    var table = el.closest('table');\n                    ((table &&\n                        table.querySelectorAll(\"td.\".concat(ns, \"Field--quickEditable:focus\"))\n                            .length) ||\n                        el) &&\n                        el.focus();\n                });\n            };\n            QuickEditComponent.prototype.buildSchema = function () {\n                var _a = this.props, quickEdit = _a.quickEdit, name = _a.name, label = _a.label, __ = _a.translate, id = _a.id;\n                var schema;\n                var isline = quickEdit.mode === 'inline';\n                if (quickEdit === true) {\n                    schema = {\n                        type: 'form',\n                        title: '',\n                        autoFocus: true,\n                        body: [\n                            {\n                                type: 'input-text',\n                                name: name,\n                                placeholder: label,\n                                label: false\n                            }\n                        ]\n                    };\n                }\n                else if (quickEdit) {\n                    if (quickEdit === null || quickEdit === void 0 ? void 0 : quickEdit.isFormMode) {\n                        schema = {\n                            mode: 'normal',\n                            type: 'form',\n                            wrapWithPanel: false,\n                            body: [\n                                __assign(__assign({}, omit(quickEdit, 'isFormMode')), { label: false })\n                            ]\n                        };\n                    }\n                    else if (quickEdit.body &&\n                        !~['combo', 'group', 'panel', 'fieldSet', 'fieldset'].indexOf(quickEdit.type)) {\n                        schema = __assign(__assign({ title: '', autoFocus: !isline }, quickEdit), { mode: 'normal', type: 'form' });\n                    }\n                    else {\n                        schema = {\n                            title: '',\n                            className: quickEdit.formClassName,\n                            type: 'form',\n                            autoFocus: !isline,\n                            mode: 'normal',\n                            body: [\n                                __assign(__assign(__assign({ type: quickEdit.type || 'input-text', name: quickEdit.name || name }, (isline ? { id: id } : {})), quickEdit), { mode: undefined })\n                            ]\n                        };\n                    }\n                }\n                var isFormMode = quickEdit === null || quickEdit === void 0 ? void 0 : quickEdit.isFormMode;\n                if (schema) {\n                    schema = __assign(__assign({}, schema), { wrapWithPanel: !(isline || isFormMode), actions: isline || isFormMode\n                            ? []\n                            : [\n                                {\n                                    type: 'button',\n                                    label: __('cancel'),\n                                    actionType: 'cancel'\n                                },\n                                {\n                                    label: __('confirm'),\n                                    type: 'submit',\n                                    primary: true\n                                }\n                            ] });\n                }\n                return schema || 'error';\n            };\n            QuickEditComponent.prototype.handleKeyUp = function (e) {\n                var _a, _b, _c, _d;\n                var code = keycode(e);\n                if (code === 'space' &&\n                    !~['INPUT', 'TEXTAREA'].indexOf(e.target.tagName)) {\n                    e.preventDefault();\n                    e.stopPropagation();\n                    if (this.formItem) {\n                        (_b = (_a = this.formItem) === null || _a === void 0 ? void 0 : _a.focus) === null || _b === void 0 ? void 0 : _b.call(_a);\n                    }\n                    else if (this.form) {\n                        (_d = (_c = this.form) === null || _c === void 0 ? void 0 : _c.focus) === null || _d === void 0 ? void 0 : _d.call(_c);\n                    }\n                    else {\n                        this.openQuickEdit();\n                    }\n                }\n            };\n            QuickEditComponent.prototype.renderPopOver = function () {\n                var _this = this;\n                var _a = this.props, quickEdit = _a.quickEdit, render = _a.render, popOverContainer = _a.popOverContainer, ns = _a.classPrefix, cx = _a.classnames, canAccessSuperData = _a.canAccessSuperData;\n                var content = (React.createElement(\"div\", { ref: this.overlayRef, className: cx(quickEdit.className) }, render('quick-edit-form', this.buildSchema(), {\n                    value: undefined,\n                    defaultStatic: false,\n                    onSubmit: this.handleSubmit,\n                    onAction: this.handleAction,\n                    onChange: null,\n                    formLazyChange: false,\n                    ref: this.formRef,\n                    popOverContainer: function () { return _this.overlay; },\n                    canAccessSuperData: canAccessSuperData,\n                    formStore: undefined\n                })));\n                popOverContainer = popOverContainer || (function () { return findDOMNode(_this); });\n                return (React.createElement(Overlay, { container: popOverContainer, target: function () { return _this.target; }, onHide: this.closeQuickEdit, placement: \"left-top right-top left-bottom right-bottom left-top-right-top left-bottom-right-bottom left-top\", show: true },\n                    React.createElement(PopOver, { classPrefix: ns, className: cx(\"\".concat(ns, \"QuickEdit-popover\"), quickEdit.popOverClassName), onHide: this.closeQuickEdit, overlay: true }, content)));\n            };\n            QuickEditComponent.prototype.renderInlineForm = function () {\n                var _a, _b;\n                var _c = this.props, render = _c.render, cx = _c.classnames, canAccessSuperData = _c.canAccessSuperData, disabled = _c.disabled, value = _c.value, name = _c.name;\n                var schema = this.buildSchema();\n                // 有且只有一个表单项时，直接渲染表单项\n                if (Array.isArray(schema.body) &&\n                    schema.body.length === 1 &&\n                    !schema.body[0].unique && // 唯一模式还不支持\n                    !schema.body[0].value && // 不能有默认值表达式什么的情况\n                    !((_a = schema.body[0]) === null || _a === void 0 ? void 0 : _a.extraName) &&\n                    schema.body[0].name &&\n                    schema.body[0].name === name &&\n                    schema.body[0].type &&\n                    ((_b = getRendererByName(schema.body[0].type)) === null || _b === void 0 ? void 0 : _b.isFormItem)) {\n                    return (React.createElement(InlineFormItem, __assign({}, this.props, { schema: schema.body[0], onChange: this.handleFormItemChange, onBulkChange: this.handleBulkChange, formItemRef: this.formItemRef })));\n                }\n                return render('inline-form', schema, {\n                    value: undefined,\n                    wrapperComponent: 'div',\n                    className: cx('Form--quickEdit'),\n                    ref: this.formRef,\n                    simpleMode: true,\n                    onInit: this.handleInit,\n                    onChange: this.handleChange,\n                    onBulkChange: this.handleBulkChange,\n                    formLazyChange: false,\n                    canAccessSuperData: canAccessSuperData,\n                    disabled: disabled,\n                    defaultStatic: false,\n                    // 不下发这下面的属性，否则当使用表格类型的 Picker 时（或其他会用到 Table 的自定义组件），会导致一些异常行为\n                    buildItemProps: null,\n                    // quickEditFormRef: null,\n                    // ^ 不知道为什么，这里不能阻挡下发，否则单测 Renderer:input-table formula 过不了\n                    quickEditFormItemRef: null\n                });\n            };\n            QuickEditComponent.prototype.render = function () {\n                var _a = this.props, onQuickChange = _a.onQuickChange, quickEdit = _a.quickEdit, quickEditEnabled = _a.quickEditEnabled, className = _a.className, cx = _a.classnames, render = _a.render, noHoc = _a.noHoc, canAccessSuperData = _a.canAccessSuperData, disabled = _a.disabled, isStatic = _a.static;\n                // 静态渲染等情况也把 InputTable 相关的回调函数剔除，防止嵌套渲染表格时出问题\n                var _b = this.props, buildItemProps = _b.buildItemProps, quickEditFormRef = _b.quickEditFormRef, quickEditFormItemRef = _b.quickEditFormItemRef, restProps = __rest(_b, [\"buildItemProps\", \"quickEditFormRef\", \"quickEditFormItemRef\"]);\n                if (isStatic ||\n                    !quickEdit ||\n                    !onQuickChange ||\n                    (!(typeof quickEdit === 'object' && (quickEdit === null || quickEdit === void 0 ? void 0 : quickEdit.isQuickEditFormMode)) &&\n                        quickEditEnabled === false) ||\n                    noHoc\n                // 此处的readOnly会导致组件值无法传递出去，如 value: \"${a + b}\" 这样的 value 变化需要同步到数据域\n                // || readOnly\n                ) {\n                    return React.createElement(Component, __assign({}, restProps, { formItemRef: this.formItemRef }));\n                }\n                if (quickEdit.mode === 'inline' ||\n                    quickEdit.isFormMode) {\n                    return (React.createElement(Component, __assign({}, restProps, { className: cx(\"Field--quickEditable\", className), tabIndex: quickEdit.focusable === false\n                            ? undefined\n                            : '0', onKeyUp: disabled ? noop : this.handleKeyUp }), this.renderInlineForm()));\n                }\n                else {\n                    return (React.createElement(Component, __assign({}, restProps, { className: cx(\"Field--quickEditable\", className, {\n                            in: this.state.isOpened\n                        }), tabIndex: quickEdit.focusable === false\n                            ? undefined\n                            : '0', onKeyUp: disabled ? noop : this.handleKeyUp }),\n                        React.createElement(Component, __assign({}, restProps, { contentsOnly: true, noHoc: true })),\n                        disabled\n                            ? null\n                            : render('quick-edit-button', {\n                                type: 'button',\n                                tabIndex: '-1',\n                                onClick: this.openQuickEdit,\n                                className: 'Field-quickEditBtn',\n                                icon: quickEdit.icon || 'edit',\n                                level: 'link'\n                            }),\n                        this.state.isOpened ? this.renderPopOver() : null));\n                }\n            };\n            QuickEditComponent.ComposedComponent = Component;\n            return QuickEditComponent;\n        }(React.PureComponent));\n        hoistNonReactStatic(QuickEditComponent, Component);\n        return QuickEditComponent;\n    };\n};\nfunction InlineFormItem(props) {\n    var _a;\n    var render = props.render, schema = props.schema, data = props.data, onChange = props.onChange, onBulkChange = props.onBulkChange, formItemRef = props.formItemRef, canAccessSuperData = props.canAccessSuperData;\n    canAccessSuperData &&\n        React.useEffect(function () {\n            var value = getPropValue(props);\n            if (value &&\n                value !== getPropValue(__assign(__assign({}, props), { canAccessSuperData: false }))) {\n                onChange(value);\n            }\n        }, []);\n    return render('inline-form-item', schema, {\n        mode: 'normal',\n        value: (_a = getPropValue(props)) !== null && _a !== void 0 ? _a : '',\n        onChange: onChange,\n        onBulkChange: onBulkChange,\n        formItemRef: formItemRef,\n        defaultStatic: false,\n        // 不下发下面的属性，否则当使用表格类型的 Picker 时（或其他会用到 Table 的自定义组件），会导致一些异常行为\n        buildItemProps: null,\n        quickEditFormRef: null,\n        quickEditFormItemRef: null\n    });\n}\n\nexport { HocQuickEdit, InlineFormItem, HocQuickEdit as default };\n", "/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __assign } from 'tslib';\nimport React from 'react';\nimport hoistNonReactStatic from 'hoist-non-react-statics';\nimport { filter } from 'amis-core';\nimport { TooltipWrapper, Icon } from 'amis-ui';\n\n/**\n * @file scoped.jsx.\n * <AUTHOR>\n */\nvar HocCopyable = function () {\n    return function (Component) {\n        var QuickEditComponent = /** @class */ (function (_super) {\n            __extends(QuickEditComponent, _super);\n            function QuickEditComponent() {\n                return _super !== null && _super.apply(this, arguments) || this;\n            }\n            QuickEditComponent.prototype.handleClick = function (content) {\n                var _a = this.props, env = _a.env, copyFormat = _a.copyFormat;\n                env.copy && env.copy(content, { format: copyFormat });\n            };\n            QuickEditComponent.prototype.render = function () {\n                var _a = this.props, name = _a.name, className = _a.className, data = _a.data, noHoc = _a.noHoc, cx = _a.classnames, __ = _a.translate, env = _a.env, tooltipContainer = _a.tooltipContainer;\n                var copyable = this.props.copyable;\n                if (copyable && !noHoc) {\n                    var content = filter(copyable.content || '${' + name + ' | raw }', data);\n                    var tooltip = (copyable === null || copyable === void 0 ? void 0 : copyable.tooltip) != null\n                        ? filter(copyable.tooltip, data)\n                        : copyable === null || copyable === void 0 ? void 0 : copyable.tooltip;\n                    if (content) {\n                        return (React.createElement(Component, __assign({}, this.props, { className: cx(\"Field--copyable\", className) }),\n                            React.createElement(Component, __assign({}, this.props, { contentsOnly: true, noHoc: true })),\n                            React.createElement(TooltipWrapper, { placement: \"right\", tooltip: tooltip !== null && tooltip !== void 0 ? tooltip : __('Copyable.tip'), trigger: \"hover\", container: tooltipContainer || (env === null || env === void 0 ? void 0 : env.getModalContainer) },\n                                React.createElement(\"a\", { key: \"edit-btn\", className: cx('Field-copyBtn'), onClick: this.handleClick.bind(this, content) },\n                                    React.createElement(Icon, { icon: \"copy\", className: \"icon\" })))));\n                    }\n                }\n                return React.createElement(Component, __assign({}, this.props));\n            };\n            QuickEditComponent.ComposedComponent = Component;\n            return QuickEditComponent;\n        }(React.PureComponent));\n        hoistNonReactStatic(QuickEditComponent, Component);\n        return QuickEditComponent;\n    };\n};\n\nexport { HocCopyable, HocCopyable as default };\n", "/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __assign } from 'tslib';\nimport React from 'react';\nimport { findDOMNode } from 'react-dom';\nimport hoistNonReactStatic from 'hoist-non-react-statics';\nimport { RootClose, Overlay, PopOver } from 'amis-core';\nimport { Icon } from 'amis-ui';\n\n/**\n * @file scoped.jsx.\n * <AUTHOR>\n */\nvar HocPopOver = function (config) {\n    if (config === void 0) { config = {}; }\n    return function (Component) {\n        var lastOpenedInstance = null;\n        var PopOverComponent = /** @class */ (function (_super) {\n            __extends(PopOverComponent, _super);\n            function PopOverComponent(props) {\n                var _this = _super.call(this, props) || this;\n                _this.openPopOver = _this.openPopOver.bind(_this);\n                _this.closePopOver = _this.closePopOver.bind(_this);\n                _this.closePopOverLater = _this.closePopOverLater.bind(_this);\n                _this.clearCloseTimer = _this.clearCloseTimer.bind(_this);\n                _this.targetRef = _this.targetRef.bind(_this);\n                // this.handleClickOutside = this.handleClickOutside.bind(this);\n                _this.state = {\n                    isOpened: false\n                };\n                return _this;\n            }\n            PopOverComponent.prototype.targetRef = function (ref) {\n                this.target = ref;\n            };\n            PopOverComponent.prototype.openPopOver = function (event) {\n                var _this = this;\n                var onPopOverOpened = this.props.onPopOverOpened;\n                lastOpenedInstance === null || lastOpenedInstance === void 0 ? void 0 : lastOpenedInstance.closePopOver();\n                lastOpenedInstance = this;\n                var e = event.currentTarget;\n                // 如果内容不超出，不需要弹出\n                if ((this.getClassName() === 'ellipsis' &&\n                    e &&\n                    e.clientHeight >= e.scrollHeight) ||\n                    this.getClassName() === 'noWrap') {\n                    return;\n                }\n                this.setState({\n                    isOpened: true\n                }, function () { return onPopOverOpened && onPopOverOpened(_this.props.popOver); });\n            };\n            PopOverComponent.prototype.closePopOver = function () {\n                var _this = this;\n                clearTimeout(this.timer);\n                if (!this.state.isOpened) {\n                    return;\n                }\n                lastOpenedInstance = null;\n                var onPopOverClosed = this.props.onPopOverClosed;\n                this.setState({\n                    isOpened: false\n                }, function () { return onPopOverClosed && onPopOverClosed(_this.props.popOver); });\n            };\n            PopOverComponent.prototype.closePopOverLater = function () {\n                // 5s 后自动关闭。\n                this.timer = setTimeout(this.closePopOver, 500);\n            };\n            PopOverComponent.prototype.clearCloseTimer = function () {\n                clearTimeout(this.timer);\n            };\n            PopOverComponent.prototype.buildSchema = function () {\n                var _a = this.props, popOver = _a.popOver, name = _a.name, label = _a.label, __ = _a.translate;\n                var schema;\n                if (popOver === true) {\n                    schema = {\n                        type: 'panel',\n                        body: \"${\".concat(name, \"}\")\n                    };\n                }\n                else if (popOver &&\n                    (popOver.mode === 'dialog' || popOver.mode === 'drawer')) {\n                    schema = __assign(__assign({ actions: [\n                            {\n                                label: __('Dialog.close'),\n                                type: 'button',\n                                actionType: 'cancel'\n                            }\n                        ] }, popOver), { type: popOver.mode });\n                }\n                else if (typeof popOver === 'string') {\n                    schema = {\n                        type: 'panel',\n                        body: popOver\n                    };\n                }\n                else if (popOver) {\n                    schema = __assign({ type: 'panel' }, popOver);\n                }\n                else if (this.getClassName() === 'ellipsis') {\n                    schema = {\n                        type: 'panel',\n                        body: \"${\".concat(name, \"}\")\n                    };\n                }\n                return schema || 'error';\n            };\n            PopOverComponent.prototype.getOffset = function () {\n                var popOver = this.props.popOver;\n                if (!popOver || typeof popOver === 'boolean' || !popOver.offset) {\n                    return undefined;\n                }\n                // PopOver 组件接收的 offset 格式为 { x: number, y: number }\n                return {\n                    x: popOver.offset.left || 0,\n                    y: popOver.offset.top || 0\n                };\n            };\n            PopOverComponent.prototype.renderPopOver = function () {\n                var _this = this;\n                var _a = this.props, popOver = _a.popOver, render = _a.render, popOverContainer = _a.popOverContainer, cx = _a.classnames, ns = _a.classPrefix;\n                if (popOver &&\n                    (popOver.mode === 'dialog' ||\n                        popOver.mode === 'drawer')) {\n                    return render('popover-detail', this.buildSchema(), {\n                        show: true,\n                        onClose: this.closePopOver,\n                        onConfirm: this.closePopOver\n                    });\n                }\n                var content = render('popover-detail', this.buildSchema(), {\n                    className: cx(popOver && popOver.className)\n                });\n                if (!popOverContainer) {\n                    popOverContainer = function () { return findDOMNode(_this); };\n                }\n                var selectClassName = this.getClassName();\n                var defaultPositon = selectClassName === 'ellipsis' && !popOver\n                    ? 'right-top-center-bottom'\n                    : 'center';\n                var position = (popOver && popOver.position) || '';\n                var isFixed = /^fixed\\-/.test(position);\n                return isFixed ? (React.createElement(RootClose, { disabled: !this.state.isOpened, onRootClose: this.closePopOver }, function (ref) {\n                    return (React.createElement(\"div\", { className: cx(\"PopOverAble--fixed PopOverAble--\".concat(position)), onMouseLeave: (popOver === null || popOver === void 0 ? void 0 : popOver.trigger) === 'hover'\n                            ? _this.closePopOver\n                            : undefined, onMouseEnter: (popOver === null || popOver === void 0 ? void 0 : popOver.trigger) === 'hover'\n                            ? _this.clearCloseTimer\n                            : undefined, ref: ref }, content));\n                })) : (React.createElement(Overlay, { container: popOverContainer, placement: position || config.position || defaultPositon, target: function () { return _this.target; }, onHide: this.closePopOver, rootClose: true, show: true },\n                    React.createElement(PopOver, { classPrefix: ns, className: cx('PopOverAble-popover', popOver && popOver.popOverClassName), offset: this.getOffset(), onMouseLeave: (popOver === null || popOver === void 0 ? void 0 : popOver.trigger) === 'hover' ||\n                            selectClassName\n                            ? this.closePopOver\n                            : undefined, onMouseEnter: (popOver === null || popOver === void 0 ? void 0 : popOver.trigger) === 'hover' ||\n                            selectClassName\n                            ? this.clearCloseTimer\n                            : undefined }, content)));\n            };\n            PopOverComponent.prototype.getClassName = function () {\n                var textOverflow = this.props.textOverflow;\n                return textOverflow === 'default' ? '' : textOverflow;\n            };\n            PopOverComponent.prototype.render = function () {\n                var _a = this.props, popOver = _a.popOver, popOverEnabled = _a.popOverEnabled, popOverEnable = _a.popOverEnable, className = _a.className, noHoc = _a.noHoc, width = _a.width, cx = _a.classnames, showIcon = _a.showIcon;\n                var selectClassName = this.getClassName();\n                if ((!popOver && !selectClassName) ||\n                    popOverEnabled === false ||\n                    noHoc ||\n                    popOverEnable === false) {\n                    return React.createElement(Component, __assign({}, this.props));\n                }\n                var triggerProps = {};\n                var trigger = popOver === null || popOver === void 0 ? void 0 : popOver.trigger;\n                if (trigger === 'hover' ||\n                    (selectClassName === 'ellipsis' && !popOver)) {\n                    triggerProps.onMouseEnter = this.openPopOver;\n                    triggerProps.onMouseLeave = this.closePopOverLater;\n                }\n                else {\n                    triggerProps.onClick = this.openPopOver;\n                }\n                return (React.createElement(Component, __assign({}, this.props, { className: cx(\"Field--popOverAble\", className, {\n                        in: this.state.isOpened\n                    }), ref: config.targetOutter ? this.targetRef : undefined }), (popOver === null || popOver === void 0 ? void 0 : popOver.showIcon) !== false && popOver ? (React.createElement(React.Fragment, null,\n                    React.createElement(Component, __assign({}, this.props, { contentsOnly: true, noHoc: true })),\n                    React.createElement(\"span\", __assign({ key: \"popover-btn\", className: cx('Field-popOverBtn') }, triggerProps, { ref: config.targetOutter ? undefined : this.targetRef }),\n                        React.createElement(Icon, { icon: \"zoom-in\", className: \"icon\" })),\n                    this.state.isOpened ? this.renderPopOver() : null)) : (React.createElement(React.Fragment, null,\n                    React.createElement(\"div\", __assign({ className: cx('Field-popOverWrap', selectClassName\n                            ? 'Field-popOverWrap-' + selectClassName\n                            : '') }, triggerProps, { ref: config.targetOutter ? undefined : this.targetRef }),\n                        React.createElement(Component, __assign({}, this.props, { contentsOnly: true, noHoc: true }))),\n                    this.state.isOpened ? this.renderPopOver() : null))));\n            };\n            PopOverComponent.ComposedComponent = Component;\n            return PopOverComponent;\n        }(React.Component));\n        hoistNonReactStatic(PopOverComponent, Component);\n        return PopOverComponent;\n    };\n};\n\nexport { HocPopOver, HocPopOver as default };\n", "import { Reaction, _allowStateChanges, _allowStateReadsStart, _allowStateReadsEnd, $mobx, createAtom, untracked, isObservableMap, isObservableObject, isObservableArray, observable } from 'mobx';\nimport React__default, { PureComponent, Component, forwardRef, memo, createElement } from 'react';\nimport { isUsingStaticRendering, Observer, observer as observer$1 } from 'mobx-react-lite';\nexport { Observer, isUsingStaticRendering, observerBatching, useAsObservableSource, useLocalStore, useObserver, useStaticRendering } from 'mobx-react-lite';\n\nvar symbolId = 0;\n\nfunction createSymbol(name) {\n  if (typeof Symbol === \"function\") {\n    return Symbol(name);\n  }\n\n  var symbol = \"__$mobx-react \" + name + \" (\" + symbolId + \")\";\n  symbolId++;\n  return symbol;\n}\n\nvar createdSymbols = {};\nfunction newSymbol(name) {\n  if (!createdSymbols[name]) {\n    createdSymbols[name] = createSymbol(name);\n  }\n\n  return createdSymbols[name];\n}\nfunction shallowEqual(objA, objB) {\n  //From: https://github.com/facebook/fbjs/blob/c69904a511b900266935168223063dd8772dfc40/packages/fbjs/src/core/shallowEqual.js\n  if (is(objA, objB)) return true;\n\n  if (typeof objA !== \"object\" || objA === null || typeof objB !== \"object\" || objB === null) {\n    return false;\n  }\n\n  var keysA = Object.keys(objA);\n  var keysB = Object.keys(objB);\n  if (keysA.length !== keysB.length) return false;\n\n  for (var i = 0; i < keysA.length; i++) {\n    if (!Object.hasOwnProperty.call(objB, keysA[i]) || !is(objA[keysA[i]], objB[keysA[i]])) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction is(x, y) {\n  // From: https://github.com/facebook/fbjs/blob/c69904a511b900266935168223063dd8772dfc40/packages/fbjs/src/core/shallowEqual.js\n  if (x === y) {\n    return x !== 0 || 1 / x === 1 / y;\n  } else {\n    return x !== x && y !== y;\n  }\n} // based on https://github.com/mridgway/hoist-non-react-statics/blob/master/src/index.js\n\n\nvar hoistBlackList = {\n  $$typeof: 1,\n  render: 1,\n  compare: 1,\n  type: 1,\n  childContextTypes: 1,\n  contextType: 1,\n  contextTypes: 1,\n  defaultProps: 1,\n  getDefaultProps: 1,\n  getDerivedStateFromError: 1,\n  getDerivedStateFromProps: 1,\n  mixins: 1,\n  propTypes: 1\n};\nfunction copyStaticProperties(base, target) {\n  var protoProps = Object.getOwnPropertyNames(Object.getPrototypeOf(base));\n  Object.getOwnPropertyNames(base).forEach(function (key) {\n    if (!hoistBlackList[key] && protoProps.indexOf(key) === -1) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(base, key));\n    }\n  });\n}\n/**\r\n * Helper to set `prop` to `this` as non-enumerable (hidden prop)\r\n * @param target\r\n * @param prop\r\n * @param value\r\n */\n\nfunction setHiddenProp(target, prop, value) {\n  if (!Object.hasOwnProperty.call(target, prop)) {\n    Object.defineProperty(target, prop, {\n      enumerable: false,\n      configurable: true,\n      writable: true,\n      value: value\n    });\n  } else {\n    target[prop] = value;\n  }\n}\n/**\r\n * Utilities for patching componentWillUnmount, to make sure @disposeOnUnmount works correctly icm with user defined hooks\r\n * and the handler provided by mobx-react\r\n */\n\nvar mobxMixins =\n/*#__PURE__*/\nnewSymbol(\"patchMixins\");\nvar mobxPatchedDefinition =\n/*#__PURE__*/\nnewSymbol(\"patchedDefinition\");\n\nfunction getMixins(target, methodName) {\n  var mixins = target[mobxMixins] = target[mobxMixins] || {};\n  var methodMixins = mixins[methodName] = mixins[methodName] || {};\n  methodMixins.locks = methodMixins.locks || 0;\n  methodMixins.methods = methodMixins.methods || [];\n  return methodMixins;\n}\n\nfunction wrapper(realMethod, mixins) {\n  var _this = this;\n\n  for (var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n    args[_key - 2] = arguments[_key];\n  }\n\n  // locks are used to ensure that mixins are invoked only once per invocation, even on recursive calls\n  mixins.locks++;\n\n  try {\n    var retVal;\n\n    if (realMethod !== undefined && realMethod !== null) {\n      retVal = realMethod.apply(this, args);\n    }\n\n    return retVal;\n  } finally {\n    mixins.locks--;\n\n    if (mixins.locks === 0) {\n      mixins.methods.forEach(function (mx) {\n        mx.apply(_this, args);\n      });\n    }\n  }\n}\n\nfunction wrapFunction(realMethod, mixins) {\n  var fn = function fn() {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n\n    wrapper.call.apply(wrapper, [this, realMethod, mixins].concat(args));\n  };\n\n  return fn;\n}\n\nfunction patch(target, methodName, mixinMethod) {\n  var mixins = getMixins(target, methodName);\n\n  if (mixins.methods.indexOf(mixinMethod) < 0) {\n    mixins.methods.push(mixinMethod);\n  }\n\n  var oldDefinition = Object.getOwnPropertyDescriptor(target, methodName);\n\n  if (oldDefinition && oldDefinition[mobxPatchedDefinition]) {\n    // already patched definition, do not repatch\n    return;\n  }\n\n  var originalMethod = target[methodName];\n  var newDefinition = createDefinition(target, methodName, oldDefinition ? oldDefinition.enumerable : undefined, mixins, originalMethod);\n  Object.defineProperty(target, methodName, newDefinition);\n}\n\nfunction createDefinition(target, methodName, enumerable, mixins, originalMethod) {\n  var _ref;\n\n  var wrappedFunc = wrapFunction(originalMethod, mixins);\n  return _ref = {}, _ref[mobxPatchedDefinition] = true, _ref.get = function get() {\n    return wrappedFunc;\n  }, _ref.set = function set(value) {\n    if (this === target) {\n      wrappedFunc = wrapFunction(value, mixins);\n    } else {\n      // when it is an instance of the prototype/a child prototype patch that particular case again separately\n      // since we need to store separate values depending on wether it is the actual instance, the prototype, etc\n      // e.g. the method for super might not be the same as the method for the prototype which might be not the same\n      // as the method for the instance\n      var newDefinition = createDefinition(this, methodName, enumerable, mixins, value);\n      Object.defineProperty(this, methodName, newDefinition);\n    }\n  }, _ref.configurable = true, _ref.enumerable = enumerable, _ref;\n}\n\nvar mobxAdminProperty = $mobx || \"$mobx\";\nvar mobxObserverProperty =\n/*#__PURE__*/\nnewSymbol(\"isMobXReactObserver\");\nvar mobxIsUnmounted =\n/*#__PURE__*/\nnewSymbol(\"isUnmounted\");\nvar skipRenderKey =\n/*#__PURE__*/\nnewSymbol(\"skipRender\");\nvar isForcingUpdateKey =\n/*#__PURE__*/\nnewSymbol(\"isForcingUpdate\");\nfunction makeClassComponentObserver(componentClass) {\n  var target = componentClass.prototype;\n\n  if (componentClass[mobxObserverProperty]) {\n    var displayName = getDisplayName(target);\n    console.warn(\"The provided component class (\" + displayName + \") \\n                has already been declared as an observer component.\");\n  } else {\n    componentClass[mobxObserverProperty] = true;\n  }\n\n  if (target.componentWillReact) throw new Error(\"The componentWillReact life-cycle event is no longer supported\");\n\n  if (componentClass[\"__proto__\"] !== PureComponent) {\n    if (!target.shouldComponentUpdate) target.shouldComponentUpdate = observerSCU;else if (target.shouldComponentUpdate !== observerSCU) // n.b. unequal check, instead of existence check, as @observer might be on superclass as well\n      throw new Error(\"It is not allowed to use shouldComponentUpdate in observer based components.\");\n  } // this.props and this.state are made observable, just to make sure @computed fields that\n  // are defined inside the component, and which rely on state or props, re-compute if state or props change\n  // (otherwise the computed wouldn't update and become stale on props change, since props are not observable)\n  // However, this solution is not without it's own problems: https://github.com/mobxjs/mobx-react/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3Aobservable-props-or-not+\n\n\n  makeObservableProp(target, \"props\");\n  makeObservableProp(target, \"state\");\n  var baseRender = target.render;\n\n  target.render = function () {\n    return makeComponentReactive.call(this, baseRender);\n  };\n\n  patch(target, \"componentWillUnmount\", function () {\n    var _this$render$mobxAdmi;\n\n    if (isUsingStaticRendering() === true) return;\n    (_this$render$mobxAdmi = this.render[mobxAdminProperty]) === null || _this$render$mobxAdmi === void 0 ? void 0 : _this$render$mobxAdmi.dispose();\n    this[mobxIsUnmounted] = true;\n\n    if (!this.render[mobxAdminProperty]) {\n      // Render may have been hot-swapped and/or overriden by a subclass.\n      var _displayName = getDisplayName(this);\n\n      console.warn(\"The reactive render of an observer class component (\" + _displayName + \") \\n                was overriden after MobX attached. This may result in a memory leak if the \\n                overriden reactive render was not properly disposed.\");\n    }\n  });\n  return componentClass;\n} // Generates a friendly name for debugging\n\nfunction getDisplayName(comp) {\n  return comp.displayName || comp.name || comp.constructor && (comp.constructor.displayName || comp.constructor.name) || \"<component>\";\n}\n\nfunction makeComponentReactive(render) {\n  var _this = this;\n\n  if (isUsingStaticRendering() === true) return render.call(this);\n  /**\r\n   * If props are shallowly modified, react will render anyway,\r\n   * so atom.reportChanged() should not result in yet another re-render\r\n   */\n\n  setHiddenProp(this, skipRenderKey, false);\n  /**\r\n   * forceUpdate will re-assign this.props. We don't want that to cause a loop,\r\n   * so detect these changes\r\n   */\n\n  setHiddenProp(this, isForcingUpdateKey, false);\n  var initialName = getDisplayName(this);\n  var baseRender = render.bind(this);\n  var isRenderingPending = false;\n  var reaction = new Reaction(initialName + \".render()\", function () {\n    if (!isRenderingPending) {\n      // N.B. Getting here *before mounting* means that a component constructor has side effects (see the relevant test in misc.js)\n      // This unidiomatic React usage but React will correctly warn about this so we continue as usual\n      // See #85 / Pull #44\n      isRenderingPending = true;\n\n      if (_this[mobxIsUnmounted] !== true) {\n        var hasError = true;\n\n        try {\n          setHiddenProp(_this, isForcingUpdateKey, true);\n          if (!_this[skipRenderKey]) Component.prototype.forceUpdate.call(_this);\n          hasError = false;\n        } finally {\n          setHiddenProp(_this, isForcingUpdateKey, false);\n          if (hasError) reaction.dispose();\n        }\n      }\n    }\n  });\n  reaction[\"reactComponent\"] = this;\n  reactiveRender[mobxAdminProperty] = reaction;\n  this.render = reactiveRender;\n\n  function reactiveRender() {\n    isRenderingPending = false;\n    var exception = undefined;\n    var rendering = undefined;\n    reaction.track(function () {\n      try {\n        rendering = _allowStateChanges(false, baseRender);\n      } catch (e) {\n        exception = e;\n      }\n    });\n\n    if (exception) {\n      throw exception;\n    }\n\n    return rendering;\n  }\n\n  return reactiveRender.call(this);\n}\n\nfunction observerSCU(nextProps, nextState) {\n  if (isUsingStaticRendering()) {\n    console.warn(\"[mobx-react] It seems that a re-rendering of a React component is triggered while in static (server-side) mode. Please make sure components are rendered only once server-side.\");\n  } // update on any state changes (as is the default)\n\n\n  if (this.state !== nextState) {\n    return true;\n  } // update if props are shallowly not equal, inspired by PureRenderMixin\n  // we could return just 'false' here, and avoid the `skipRender` checks etc\n  // however, it is nicer if lifecycle events are triggered like usually,\n  // so we return true here if props are shallowly modified.\n\n\n  return !shallowEqual(this.props, nextProps);\n}\n\nfunction makeObservableProp(target, propName) {\n  var valueHolderKey = newSymbol(\"reactProp_\" + propName + \"_valueHolder\");\n  var atomHolderKey = newSymbol(\"reactProp_\" + propName + \"_atomHolder\");\n\n  function getAtom() {\n    if (!this[atomHolderKey]) {\n      setHiddenProp(this, atomHolderKey, createAtom(\"reactive \" + propName));\n    }\n\n    return this[atomHolderKey];\n  }\n\n  Object.defineProperty(target, propName, {\n    configurable: true,\n    enumerable: true,\n    get: function get() {\n      var prevReadState = false;\n\n      if (_allowStateReadsStart && _allowStateReadsEnd) {\n        prevReadState = _allowStateReadsStart(true);\n      }\n\n      getAtom.call(this).reportObserved();\n\n      if (_allowStateReadsStart && _allowStateReadsEnd) {\n        _allowStateReadsEnd(prevReadState);\n      }\n\n      return this[valueHolderKey];\n    },\n    set: function set(v) {\n      if (!this[isForcingUpdateKey] && !shallowEqual(this[valueHolderKey], v)) {\n        setHiddenProp(this, valueHolderKey, v);\n        setHiddenProp(this, skipRenderKey, true);\n        getAtom.call(this).reportChanged();\n        setHiddenProp(this, skipRenderKey, false);\n      } else {\n        setHiddenProp(this, valueHolderKey, v);\n      }\n    }\n  });\n}\n\nvar hasSymbol = typeof Symbol === \"function\" && Symbol.for; // Using react-is had some issues (and operates on elements, not on types), see #608 / #609\n\nvar ReactForwardRefSymbol = hasSymbol ?\n/*#__PURE__*/\nSymbol.for(\"react.forward_ref\") : typeof forwardRef === \"function\" &&\n/*#__PURE__*/\nforwardRef(function (props) {\n  return null;\n})[\"$$typeof\"];\nvar ReactMemoSymbol = hasSymbol ?\n/*#__PURE__*/\nSymbol.for(\"react.memo\") : typeof memo === \"function\" &&\n/*#__PURE__*/\nmemo(function (props) {\n  return null;\n})[\"$$typeof\"];\n/**\r\n * Observer function / decorator\r\n */\n\nfunction observer(component) {\n  if (component[\"isMobxInjector\"] === true) {\n    console.warn(\"Mobx observer: You are trying to use 'observer' on a component that already has 'inject'. Please apply 'observer' before applying 'inject'\");\n  }\n\n  if (ReactMemoSymbol && component[\"$$typeof\"] === ReactMemoSymbol) {\n    throw new Error(\"Mobx observer: You are trying to use 'observer' on a function component wrapped in either another observer or 'React.memo'. The observer already applies 'React.memo' for you.\");\n  } // Unwrap forward refs into `<Observer>` component\n  // we need to unwrap the render, because it is the inner render that needs to be tracked,\n  // not the ForwardRef HoC\n\n\n  if (ReactForwardRefSymbol && component[\"$$typeof\"] === ReactForwardRefSymbol) {\n    var baseRender = component[\"render\"];\n    if (typeof baseRender !== \"function\") throw new Error(\"render property of ForwardRef was not a function\");\n    return forwardRef(function ObserverForwardRef() {\n      var args = arguments;\n      return createElement(Observer, null, function () {\n        return baseRender.apply(undefined, args);\n      });\n    });\n  } // Function component\n\n\n  if (typeof component === \"function\" && (!component.prototype || !component.prototype.render) && !component[\"isReactClass\"] && !Object.prototype.isPrototypeOf.call(Component, component)) {\n    return observer$1(component);\n  }\n\n  return makeClassComponentObserver(component);\n}\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nvar MobXProviderContext =\n/*#__PURE__*/\nReact__default.createContext({});\nfunction Provider(props) {\n  var children = props.children,\n      stores = _objectWithoutPropertiesLoose(props, [\"children\"]);\n\n  var parentValue = React__default.useContext(MobXProviderContext);\n  var mutableProviderRef = React__default.useRef(_extends({}, parentValue, stores));\n  var value = mutableProviderRef.current;\n\n  if (process.env.NODE_ENV !== \"production\") {\n    var newValue = _extends({}, value, stores); // spread in previous state for the context based stores\n\n\n    if (!shallowEqual(value, newValue)) {\n      throw new Error(\"MobX Provider: The set of provided stores has changed. See: https://github.com/mobxjs/mobx-react#the-set-of-provided-stores-has-changed-error.\");\n    }\n  }\n\n  return React__default.createElement(MobXProviderContext.Provider, {\n    value: value\n  }, children);\n}\nProvider.displayName = \"MobXProvider\";\n\n/**\r\n * Store Injection\r\n */\n\nfunction createStoreInjector(grabStoresFn, component, injectNames, makeReactive) {\n  // Support forward refs\n  var Injector = React__default.forwardRef(function (props, ref) {\n    var newProps = _extends({}, props);\n\n    var context = React__default.useContext(MobXProviderContext);\n    Object.assign(newProps, grabStoresFn(context || {}, newProps) || {});\n\n    if (ref) {\n      newProps.ref = ref;\n    }\n\n    return React__default.createElement(component, newProps);\n  });\n  if (makeReactive) Injector = observer(Injector);\n  Injector[\"isMobxInjector\"] = true; // assigned late to suppress observer warning\n  // Static fields from component should be visible on the generated Injector\n\n  copyStaticProperties(component, Injector);\n  Injector[\"wrappedComponent\"] = component;\n  Injector.displayName = getInjectName(component, injectNames);\n  return Injector;\n}\n\nfunction getInjectName(component, injectNames) {\n  var displayName;\n  var componentName = component.displayName || component.name || component.constructor && component.constructor.name || \"Component\";\n  if (injectNames) displayName = \"inject-with-\" + injectNames + \"(\" + componentName + \")\";else displayName = \"inject(\" + componentName + \")\";\n  return displayName;\n}\n\nfunction grabStoresByName(storeNames) {\n  return function (baseStores, nextProps) {\n    storeNames.forEach(function (storeName) {\n      if (storeName in nextProps // prefer props over stores\n      ) return;\n      if (!(storeName in baseStores)) throw new Error(\"MobX injector: Store '\" + storeName + \"' is not available! Make sure it is provided by some Provider\");\n      nextProps[storeName] = baseStores[storeName];\n    });\n    return nextProps;\n  };\n}\n/**\r\n * higher order component that injects stores to a child.\r\n * takes either a varargs list of strings, which are stores read from the context,\r\n * or a function that manually maps the available stores from the context to props:\r\n * storesToProps(mobxStores, props, context) => newProps\r\n */\n\n\nfunction inject() {\n  for (var _len = arguments.length, storeNames = new Array(_len), _key = 0; _key < _len; _key++) {\n    storeNames[_key] = arguments[_key];\n  }\n\n  if (typeof arguments[0] === \"function\") {\n    var grabStoresFn = arguments[0];\n    return function (componentClass) {\n      return createStoreInjector(grabStoresFn, componentClass, grabStoresFn.name, true);\n    };\n  } else {\n    return function (componentClass) {\n      return createStoreInjector(grabStoresByName(storeNames), componentClass, storeNames.join(\"-\"), false);\n    };\n  }\n}\n\nvar protoStoreKey =\n/*#__PURE__*/\nnewSymbol(\"disposeOnUnmountProto\");\nvar instStoreKey =\n/*#__PURE__*/\nnewSymbol(\"disposeOnUnmountInst\");\n\nfunction runDisposersOnWillUnmount() {\n  var _this = this;\n  [].concat(this[protoStoreKey] || [], this[instStoreKey] || []).forEach(function (propKeyOrFunction) {\n    var prop = typeof propKeyOrFunction === \"string\" ? _this[propKeyOrFunction] : propKeyOrFunction;\n\n    if (prop !== undefined && prop !== null) {\n      if (Array.isArray(prop)) prop.map(function (f) {\n        return f();\n      });else prop();\n    }\n  });\n}\n\nfunction disposeOnUnmount(target, propertyKeyOrFunction) {\n  if (Array.isArray(propertyKeyOrFunction)) {\n    return propertyKeyOrFunction.map(function (fn) {\n      return disposeOnUnmount(target, fn);\n    });\n  }\n\n  var c = Object.getPrototypeOf(target).constructor;\n  var c2 = Object.getPrototypeOf(target.constructor); // Special case for react-hot-loader\n\n  var c3 = Object.getPrototypeOf(Object.getPrototypeOf(target));\n\n  if (!(c === React__default.Component || c === React__default.PureComponent || c2 === React__default.Component || c2 === React__default.PureComponent || c3 === React__default.Component || c3 === React__default.PureComponent)) {\n    throw new Error(\"[mobx-react] disposeOnUnmount only supports direct subclasses of React.Component or React.PureComponent.\");\n  }\n\n  if (typeof propertyKeyOrFunction !== \"string\" && typeof propertyKeyOrFunction !== \"function\" && !Array.isArray(propertyKeyOrFunction)) {\n    throw new Error(\"[mobx-react] disposeOnUnmount only works if the parameter is either a property key or a function.\");\n  } // decorator's target is the prototype, so it doesn't have any instance properties like props\n\n\n  var isDecorator = typeof propertyKeyOrFunction === \"string\"; // add property key / function we want run (disposed) to the store\n\n  var componentWasAlreadyModified = !!target[protoStoreKey] || !!target[instStoreKey];\n  var store = isDecorator ? // decorators are added to the prototype store\n  target[protoStoreKey] || (target[protoStoreKey] = []) : // functions are added to the instance store\n  target[instStoreKey] || (target[instStoreKey] = []);\n  store.push(propertyKeyOrFunction); // tweak the component class componentWillUnmount if not done already\n\n  if (!componentWasAlreadyModified) {\n    patch(target, \"componentWillUnmount\", runDisposersOnWillUnmount);\n  } // return the disposer as is if invoked as a non decorator\n\n\n  if (typeof propertyKeyOrFunction !== \"string\") {\n    return propertyKeyOrFunction;\n  }\n}\n\nfunction createChainableTypeChecker(validator) {\n  function checkType(isRequired, props, propName, componentName, location, propFullName) {\n    for (var _len = arguments.length, rest = new Array(_len > 6 ? _len - 6 : 0), _key = 6; _key < _len; _key++) {\n      rest[_key - 6] = arguments[_key];\n    }\n\n    return untracked(function () {\n      componentName = componentName || \"<<anonymous>>\";\n      propFullName = propFullName || propName;\n\n      if (props[propName] == null) {\n        if (isRequired) {\n          var actual = props[propName] === null ? \"null\" : \"undefined\";\n          return new Error(\"The \" + location + \" `\" + propFullName + \"` is marked as required \" + \"in `\" + componentName + \"`, but its value is `\" + actual + \"`.\");\n        }\n\n        return null;\n      } else {\n        // @ts-ignore rest arg is necessary for some React internals - fails tests otherwise\n        return validator.apply(void 0, [props, propName, componentName, location, propFullName].concat(rest));\n      }\n    });\n  }\n\n  var chainedCheckType = checkType.bind(null, false); // Add isRequired to satisfy Requirable\n\n  chainedCheckType.isRequired = checkType.bind(null, true);\n  return chainedCheckType;\n} // Copied from React.PropTypes\n\n\nfunction isSymbol(propType, propValue) {\n  // Native Symbol.\n  if (propType === \"symbol\") {\n    return true;\n  } // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n\n\n  if (propValue[\"@@toStringTag\"] === \"Symbol\") {\n    return true;\n  } // Fallback for non-spec compliant Symbols which are polyfilled.\n\n\n  if (typeof Symbol === \"function\" && propValue instanceof Symbol) {\n    return true;\n  }\n\n  return false;\n} // Copied from React.PropTypes\n\n\nfunction getPropType(propValue) {\n  var propType = typeof propValue;\n\n  if (Array.isArray(propValue)) {\n    return \"array\";\n  }\n\n  if (propValue instanceof RegExp) {\n    // Old webkits (at least until Android 4.0) return 'function' rather than\n    // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n    // passes PropTypes.object.\n    return \"object\";\n  }\n\n  if (isSymbol(propType, propValue)) {\n    return \"symbol\";\n  }\n\n  return propType;\n} // This handles more types than `getPropType`. Only used for error messages.\n// Copied from React.PropTypes\n\n\nfunction getPreciseType(propValue) {\n  var propType = getPropType(propValue);\n\n  if (propType === \"object\") {\n    if (propValue instanceof Date) {\n      return \"date\";\n    } else if (propValue instanceof RegExp) {\n      return \"regexp\";\n    }\n  }\n\n  return propType;\n}\n\nfunction createObservableTypeCheckerCreator(allowNativeType, mobxType) {\n  return createChainableTypeChecker(function (props, propName, componentName, location, propFullName) {\n    return untracked(function () {\n      if (allowNativeType) {\n        if (getPropType(props[propName]) === mobxType.toLowerCase()) return null;\n      }\n\n      var mobxChecker;\n\n      switch (mobxType) {\n        case \"Array\":\n          mobxChecker = isObservableArray;\n          break;\n\n        case \"Object\":\n          mobxChecker = isObservableObject;\n          break;\n\n        case \"Map\":\n          mobxChecker = isObservableMap;\n          break;\n\n        default:\n          throw new Error(\"Unexpected mobxType: \" + mobxType);\n      }\n\n      var propValue = props[propName];\n\n      if (!mobxChecker(propValue)) {\n        var preciseType = getPreciseType(propValue);\n        var nativeTypeExpectationMessage = allowNativeType ? \" or javascript `\" + mobxType.toLowerCase() + \"`\" : \"\";\n        return new Error(\"Invalid prop `\" + propFullName + \"` of type `\" + preciseType + \"` supplied to\" + \" `\" + componentName + \"`, expected `mobx.Observable\" + mobxType + \"`\" + nativeTypeExpectationMessage + \".\");\n      }\n\n      return null;\n    });\n  });\n}\n\nfunction createObservableArrayOfTypeChecker(allowNativeType, typeChecker) {\n  return createChainableTypeChecker(function (props, propName, componentName, location, propFullName) {\n    for (var _len2 = arguments.length, rest = new Array(_len2 > 5 ? _len2 - 5 : 0), _key2 = 5; _key2 < _len2; _key2++) {\n      rest[_key2 - 5] = arguments[_key2];\n    }\n\n    return untracked(function () {\n      if (typeof typeChecker !== \"function\") {\n        return new Error(\"Property `\" + propFullName + \"` of component `\" + componentName + \"` has \" + \"invalid PropType notation.\");\n      } else {\n        var error = createObservableTypeCheckerCreator(allowNativeType, \"Array\")(props, propName, componentName, location, propFullName);\n        if (error instanceof Error) return error;\n        var propValue = props[propName];\n\n        for (var i = 0; i < propValue.length; i++) {\n          error = typeChecker.apply(void 0, [propValue, i, componentName, location, propFullName + \"[\" + i + \"]\"].concat(rest));\n          if (error instanceof Error) return error;\n        }\n\n        return null;\n      }\n    });\n  });\n}\n\nvar observableArray =\n/*#__PURE__*/\ncreateObservableTypeCheckerCreator(false, \"Array\");\nvar observableArrayOf =\n/*#__PURE__*/\ncreateObservableArrayOfTypeChecker.bind(null, false);\nvar observableMap =\n/*#__PURE__*/\ncreateObservableTypeCheckerCreator(false, \"Map\");\nvar observableObject =\n/*#__PURE__*/\ncreateObservableTypeCheckerCreator(false, \"Object\");\nvar arrayOrObservableArray =\n/*#__PURE__*/\ncreateObservableTypeCheckerCreator(true, \"Array\");\nvar arrayOrObservableArrayOf =\n/*#__PURE__*/\ncreateObservableArrayOfTypeChecker.bind(null, true);\nvar objectOrObservableObject =\n/*#__PURE__*/\ncreateObservableTypeCheckerCreator(true, \"Object\");\nvar PropTypes = {\n  observableArray: observableArray,\n  observableArrayOf: observableArrayOf,\n  observableMap: observableMap,\n  observableObject: observableObject,\n  arrayOrObservableArray: arrayOrObservableArray,\n  arrayOrObservableArrayOf: arrayOrObservableArrayOf,\n  objectOrObservableObject: objectOrObservableObject\n};\n\nif (!Component) throw new Error(\"mobx-react requires React to be available\");\nif (!observable) throw new Error(\"mobx-react requires mobx to be available\");\n\nexport { MobXProviderContext, PropTypes, Provider, disposeOnUnmount, inject, observer };\n//# sourceMappingURL=mobxreact.esm.js.map\n", "import { spy } from \"mobx\";\nimport { useState } from \"react\";\nif (!useState) {\n    throw new Error(\"mobx-react-lite requires React with Hooks support\");\n}\nif (!spy) {\n    throw new Error(\"mobx-react-lite requires mobx at least version 4 to be available\");\n}\n", "export { unstable_batchedUpdates } from \"react-dom\";\n", "var __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nimport { useCallback, useEffect, useState } from \"react\";\nvar EMPTY_ARRAY = [];\nexport function useUnmount(fn) {\n    useEffect(function () { return fn; }, EMPTY_ARRAY);\n}\nexport function useForceUpdate() {\n    var _a = __read(useState(0), 2), setTick = _a[1];\n    var update = useCallback(function () {\n        setTick(function (tick) { return tick + 1; });\n    }, []);\n    return update;\n}\nexport function isPlainObject(value) {\n    if (!value || typeof value !== \"object\") {\n        return false;\n    }\n    var proto = Object.getPrototypeOf(value);\n    return !proto || proto === Object.prototype;\n}\nexport function getSymbol(name) {\n    if (typeof Symbol === \"function\") {\n        return Symbol.for(name);\n    }\n    return \"__$mobx-react \" + name + \"__\";\n}\nvar mockGlobal = {};\nexport function getGlobal() {\n    if (typeof window !== \"undefined\") {\n        return window;\n    }\n    if (typeof global !== \"undefined\") {\n        return global;\n    }\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    return mockGlobal;\n}\n", "import { configure } from \"mobx\";\nimport { getGlobal, getSymbol } from \"./utils\";\nvar observerBatchingConfiguredSymbol = getSymbol(\"observerBatching\");\nexport function defaultNoopBatch(callback) {\n    callback();\n}\nexport function observerBatching(reactionScheduler) {\n    if (!reactionScheduler) {\n        reactionScheduler = defaultNoopBatch;\n        if (\"production\" !== process.env.NODE_ENV) {\n            console.warn(\"[MobX] Failed to get unstable_batched updates from react-dom / react-native\");\n        }\n    }\n    configure({ reactionScheduler: reactionScheduler });\n    getGlobal()[observerBatchingConfiguredSymbol] = true;\n}\nexport var isObserverBatched = function () { return !!getGlobal()[observerBatchingConfiguredSymbol]; };\n", "var globalIsUsingStaticRendering = false;\nexport function useStaticRendering(enable) {\n    globalIsUsingStaticRendering = enable;\n}\nexport function isUsingStaticRendering() {\n    return globalIsUsingStaticRendering;\n}\n", "var __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nimport { forwardRef, memo } from \"react\";\nimport { isUsingStaticRendering } from \"./staticRendering\";\nimport { useObserver } from \"./useObserver\";\n// n.b. base case is not used for actual typings or exported in the typing files\nexport function observer(baseComponent, options) {\n    // The working of observer is explained step by step in this talk: https://www.youtube.com/watch?v=cPF4iBedoF0&feature=youtu.be&t=1307\n    if (isUsingStaticRendering()) {\n        return baseComponent;\n    }\n    var realOptions = __assign({ forwardRef: false }, options);\n    var baseComponentName = baseComponent.displayName || baseComponent.name;\n    var wrappedComponent = function (props, ref) {\n        return useObserver(function () { return baseComponent(props, ref); }, baseComponentName);\n    };\n    wrappedComponent.displayName = baseComponentName;\n    // memo; we are not interested in deep updates\n    // in props; we assume that if deep objects are changed,\n    // this is in observables, which would have been tracked anyway\n    var memoComponent;\n    if (realOptions.forwardRef) {\n        // we have to use forwardRef here because:\n        // 1. it cannot go before memo, only after it\n        // 2. forwardRef converts the function into an actual component, so we can't let the baseComponent do it\n        //    since it wouldn't be a callable function anymore\n        memoComponent = memo(forwardRef(wrappedComponent));\n    }\n    else {\n        memoComponent = memo(wrappedComponent);\n    }\n    copyStaticProperties(baseComponent, memoComponent);\n    memoComponent.displayName = baseComponentName;\n    return memoComponent;\n}\n// based on https://github.com/mridgway/hoist-non-react-statics/blob/master/src/index.js\nvar hoistBlackList = {\n    $$typeof: true,\n    render: true,\n    compare: true,\n    type: true\n};\nfunction copyStaticProperties(base, target) {\n    Object.keys(base).forEach(function (key) {\n        if (!hoistBlackList[key]) {\n            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(base, key));\n        }\n    });\n}\n", "import { Reaction } from \"mobx\";\nimport React from \"react\";\nimport { printDebugValue } from \"./printDebugValue\";\nimport { createTrackingData, recordReactionAsCommitted, scheduleCleanupOfReactionIfLeaked } from \"./reactionCleanupTracking\";\nimport { isUsingStaticRendering } from \"./staticRendering\";\nimport { useForceUpdate } from \"./utils\";\nimport { useQueuedForceUpdate, useQueuedForceUpdateBlock } from \"./useQueuedForceUpdate\";\nvar EMPTY_OBJECT = {};\nfunction observerComponentNameFor(baseComponentName) {\n    return \"observer\" + baseComponentName;\n}\nexport function useObserver(fn, baseComponentName, options) {\n    if (baseComponentName === void 0) { baseComponentName = \"observed\"; }\n    if (options === void 0) { options = EMPTY_OBJECT; }\n    if (isUsingStaticRendering()) {\n        return fn();\n    }\n    var wantedForceUpdateHook = options.useForceUpdate || useForceUpdate;\n    var forceUpdate = wantedForceUpdateHook();\n    var queuedForceUpdate = useQueuedForceUpdate(forceUpdate);\n    // StrictMode/ConcurrentMode/Suspense may mean that our component is\n    // rendered and abandoned multiple times, so we need to track leaked\n    // Reactions.\n    var reactionTrackingRef = React.useRef(null);\n    if (!reactionTrackingRef.current) {\n        // First render for this component (or first time since a previous\n        // reaction from an abandoned render was disposed).\n        var newReaction_1 = new Reaction(observerComponentNameFor(baseComponentName), function () {\n            // Observable has changed, meaning we want to re-render\n            // BUT if we're a component that hasn't yet got to the useEffect()\n            // stage, we might be a component that _started_ to render, but\n            // got dropped, and we don't want to make state changes then.\n            // (It triggers warnings in StrictMode, for a start.)\n            if (trackingData_1.mounted) {\n                // We have reached useEffect(), so we're mounted, and can trigger an update\n                queuedForceUpdate();\n            }\n            else {\n                // We haven't yet reached useEffect(), so we'll need to trigger a re-render\n                // when (and if) useEffect() arrives.  The easiest way to do that is just to\n                // drop our current reaction and allow useEffect() to recreate it.\n                newReaction_1.dispose();\n                reactionTrackingRef.current = null;\n            }\n        });\n        var trackingData_1 = createTrackingData(newReaction_1);\n        reactionTrackingRef.current = trackingData_1;\n        scheduleCleanupOfReactionIfLeaked(reactionTrackingRef);\n    }\n    var reaction = reactionTrackingRef.current.reaction;\n    React.useDebugValue(reaction, printDebugValue);\n    React.useEffect(function () {\n        // Called on first mount only\n        recordReactionAsCommitted(reactionTrackingRef);\n        if (reactionTrackingRef.current) {\n            // Great. We've already got our reaction from our render;\n            // all we need to do is to record that it's now mounted,\n            // to allow future observable changes to trigger re-renders\n            reactionTrackingRef.current.mounted = true;\n        }\n        else {\n            // The reaction we set up in our render has been disposed.\n            // This is either due to bad timings of renderings, e.g. our\n            // component was paused for a _very_ long time, and our\n            // reaction got cleaned up, or we got a observable change\n            // between render and useEffect\n            // Re-create the reaction\n            reactionTrackingRef.current = {\n                reaction: new Reaction(observerComponentNameFor(baseComponentName), function () {\n                    // We've definitely already been mounted at this point\n                    queuedForceUpdate();\n                }),\n                cleanAt: Infinity\n            };\n            queuedForceUpdate();\n        }\n        return function () {\n            reactionTrackingRef.current.reaction.dispose();\n            reactionTrackingRef.current = null;\n        };\n    }, []);\n    // delay all force-update calls after rendering of this component\n    return useQueuedForceUpdateBlock(function () {\n        // render the original component, but have the\n        // reaction track the observables, so that rendering\n        // can be invalidated (see above) once a dependency changes\n        var rendering;\n        var exception;\n        reaction.track(function () {\n            try {\n                rendering = fn();\n            }\n            catch (e) {\n                exception = e;\n            }\n        });\n        if (exception) {\n            throw exception; // re-throw any exceptions caught during rendering\n        }\n        return rendering;\n    });\n}\n", "import { getDependencyTree } from \"mobx\";\nexport function printDebugValue(v) {\n    return getDependencyTree(v);\n}\n", "export function createTrackingData(reaction) {\n    var trackingData = {\n        cleanAt: Date.now() + CLEANUP_LEAKED_REACTIONS_AFTER_MILLIS,\n        reaction: reaction\n    };\n    return trackingData;\n}\n/**\n * The minimum time before we'll clean up a Reaction created in a render\n * for a component that hasn't managed to run its effects. This needs to\n * be big enough to ensure that a component won't turn up and have its\n * effects run without being re-rendered.\n */\nexport var CLEANUP_LEAKED_REACTIONS_AFTER_MILLIS = 10000;\n/**\n * The frequency with which we'll check for leaked reactions.\n */\nexport var CLEANUP_TIMER_LOOP_MILLIS = 10000;\n/**\n * Reactions created by components that have yet to be fully mounted.\n */\nvar uncommittedReactionRefs = new Set();\n/**\n * Latest 'uncommitted reactions' cleanup timer handle.\n */\nvar reactionCleanupHandle;\nfunction ensureCleanupTimerRunning() {\n    if (reactionCleanupHandle === undefined) {\n        reactionCleanupHandle = setTimeout(cleanUncommittedReactions, CLEANUP_TIMER_LOOP_MILLIS);\n    }\n}\nexport function scheduleCleanupOfReactionIfLeaked(ref) {\n    uncommittedReactionRefs.add(ref);\n    ensureCleanupTimerRunning();\n}\nexport function recordReactionAsCommitted(reactionRef) {\n    uncommittedReactionRefs.delete(reactionRef);\n}\n/**\n * Run by the cleanup timer to dispose any outstanding reactions\n */\nfunction cleanUncommittedReactions() {\n    reactionCleanupHandle = undefined;\n    // Loop through all the candidate leaked reactions; those older\n    // than CLEANUP_LEAKED_REACTIONS_AFTER_MILLIS get tidied.\n    var now = Date.now();\n    uncommittedReactionRefs.forEach(function (ref) {\n        var tracking = ref.current;\n        if (tracking) {\n            if (now >= tracking.cleanAt) {\n                // It's time to tidy up this leaked reaction.\n                tracking.reaction.dispose();\n                ref.current = null;\n                uncommittedReactionRefs.delete(ref);\n            }\n        }\n    });\n    if (uncommittedReactionRefs.size > 0) {\n        // We've just finished a round of cleanups but there are still\n        // some leak candidates outstanding.\n        ensureCleanupTimerRunning();\n    }\n}\n/* istanbul ignore next */\n/**\n * Only to be used by test functions; do not export outside of mobx-react-lite\n */\nexport function forceCleanupTimerToRunNowForTests() {\n    // This allows us to control the execution of the cleanup timer\n    // to force it to run at awkward times in unit tests.\n    if (reactionCleanupHandle) {\n        clearTimeout(reactionCleanupHandle);\n        cleanUncommittedReactions();\n    }\n}\n/* istanbul ignore next */\nexport function resetCleanupScheduleForTests() {\n    if (reactionCleanupHandle) {\n        clearTimeout(reactionCleanupHandle);\n        reactionCleanupHandle = undefined;\n    }\n    uncommittedReactionRefs.clear();\n}\n", "import React from \"react\";\nvar insideRender = false;\nvar forceUpdateQueue = [];\nexport function useQueuedForceUpdate(forceUpdate) {\n    return function () {\n        if (insideRender) {\n            forceUpdateQueue.push(forceUpdate);\n        }\n        else {\n            forceUpdate();\n        }\n    };\n}\nexport function useQueuedForceUpdateBlock(callback) {\n    // start intercepting force-update calls\n    insideRender = true;\n    forceUpdateQueue = [];\n    try {\n        var result = callback();\n        // stop intercepting force-update\n        insideRender = false;\n        // store queue or nothing if it was empty to execute useLayoutEffect only when necessary\n        var queue_1 = forceUpdateQueue.length > 0 ? forceUpdateQueue : undefined;\n        // run force-update queue in useLayoutEffect\n        React.useLayoutEffect(function () {\n            if (queue_1) {\n                queue_1.forEach(function (x) { return x(); });\n            }\n        }, [queue_1]);\n        return result;\n    }\n    finally {\n        insideRender = false;\n    }\n}\n", "import { useObserver } from \"./useObserver\";\nfunction ObserverComponent(_a) {\n    var children = _a.children, render = _a.render;\n    var component = children || render;\n    if (typeof component !== \"function\") {\n        return null;\n    }\n    return useObserver(component);\n}\nObserverComponent.propTypes = {\n    children: ObserverPropsCheck,\n    render: ObserverPropsCheck\n};\nObserverComponent.displayName = \"Observer\";\nexport { ObserverComponent as Observer };\nfunction ObserverPropsCheck(props, key, componentName, location, propFullName) {\n    var extraKey = key === \"children\" ? \"render\" : \"children\";\n    var hasProp = typeof props[key] === \"function\";\n    var hasExtraProp = typeof props[extraKey] === \"function\";\n    if (hasProp && hasExtraProp) {\n        return new Error(\"MobX Observer: Do not use children and render in the same time in`\" + componentName);\n    }\n    if (hasProp || hasExtraProp) {\n        return null;\n    }\n    return new Error(\"Invalid prop `\" +\n        propFullName +\n        \"` of type `\" +\n        typeof props[key] +\n        \"` supplied to\" +\n        \" `\" +\n        componentName +\n        \"`, expected `function`.\");\n}\n", "var __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nimport { observable, runInAction } from \"mobx\";\nimport React from \"react\";\nimport { isPlainObject } from \"./utils\";\nexport function useAsObservableSourceInternal(current, usedByLocalStore) {\n    var culprit = usedByLocalStore ? \"useLocalStore\" : \"useAsObservableSource\";\n    if (\"production\" !== process.env.NODE_ENV && usedByLocalStore) {\n        var _a = __read(React.useState(current), 1), initialSource = _a[0];\n        if ((initialSource !== undefined && current === undefined) ||\n            (initialSource === undefined && current !== undefined)) {\n            throw new Error(\"make sure you never pass `undefined` to \" + culprit);\n        }\n    }\n    if (usedByLocalStore && current === undefined) {\n        return undefined;\n    }\n    if (\"production\" !== process.env.NODE_ENV && !isPlainObject(current)) {\n        throw new Error(culprit + \" expects a plain object as \" + (usedByLocalStore ? \"second\" : \"first\") + \" argument\");\n    }\n    var _b = __read(React.useState(function () { return observable(current, {}, { deep: false }); }), 1), res = _b[0];\n    if (\"production\" !== process.env.NODE_ENV &&\n        Object.keys(res).length !== Object.keys(current).length) {\n        throw new Error(\"the shape of objects passed to \" + culprit + \" should be stable\");\n    }\n    runInAction(function () {\n        Object.assign(res, current);\n    });\n    return res;\n}\nexport function useAsObservableSource(current) {\n    return useAsObservableSourceInternal(current, false);\n}\n", "import { observable, runInAction, transaction } from \"mobx\";\nimport React from \"react\";\nimport { useAsObservableSourceInternal } from \"./useAsObservableSource\";\nimport { isPlainObject } from \"./utils\";\nexport function useLocalStore(initializer, current) {\n    var source = useAsObservableSourceInternal(current, true);\n    return React.useState(function () {\n        var local = observable(initializer(source));\n        if (isPlainObject(local)) {\n            runInAction(function () {\n                Object.keys(local).forEach(function (key) {\n                    var value = local[key];\n                    if (typeof value === \"function\") {\n                        // @ts-ignore No idea why ts2536 is popping out here\n                        local[key] = wrapInTransaction(value, local);\n                    }\n                });\n            });\n        }\n        return local;\n    })[0];\n}\n// tslint:disable-next-line: ban-types\nfunction wrapInTransaction(fn, context) {\n    return function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return transaction(function () { return fn.apply(context, args); });\n    };\n}\n", "import \"./assertEnvironment\";\nimport { unstable_batchedUpdates as batch } from \"./utils/reactBatchedUpdates\";\nimport { observerBatching } from \"./observerBatching\";\nobserverBatching(batch);\nexport { isUsingStaticRendering, useStaticRendering } from \"./staticRendering\";\nexport { observer } from \"./observer\";\nexport { useObserver } from \"./useObserver\";\nexport { Observer } from \"./ObserverComponent\";\nexport { useForceUpdate } from \"./utils\";\nexport { useAsObservableSource } from \"./useAsObservableSource\";\nexport { useLocalStore } from \"./useLocalStore\";\nexport { useQueuedForceUpdate, useQueuedForceUpdateBlock } from \"./useQueuedForceUpdate\";\nexport { isObserverBatched, observerBatching } from \"./observerBatching\";\n", "/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __awaiter, __generator, __assign, __decorate, __metadata } from 'tslib';\nimport React from 'react';\nimport xor from 'lodash/xor';\nimport { findDOMNode } from 'react-dom';\nimport { isPureVariable, resolveVariableAndFilter, isEffectiveApi, isApiOutdated, getVariable, normalizeApi, normalizeOptions, isNumeric, createObject, Overlay, PopOver, noop, autobind } from 'amis-core';\nimport { Icon, SearchBox, Checkbox } from 'amis-ui';\nimport { matchSorter } from 'match-sorter';\n\nvar HeadCellFilterDropDown = /** @class */ (function (_super) {\n    __extends(HeadCellFilterDropDown, _super);\n    function HeadCellFilterDropDown(props) {\n        var _this = _super.call(this, props) || this;\n        _this.state = {\n            isOpened: false,\n            keyword: '',\n            filterOptions: []\n        };\n        _this.sourceInvalid = false;\n        _this.open = _this.open.bind(_this);\n        _this.close = _this.close.bind(_this);\n        _this.handleClick = _this.handleClick.bind(_this);\n        _this.handleCheck = _this.handleCheck.bind(_this);\n        return _this;\n    }\n    HeadCellFilterDropDown.prototype.componentDidMount = function () {\n        var _a = this.props, filterable = _a.filterable, data = _a.data;\n        var _b = filterable || {}, source = _b.source, options = _b.options;\n        if (source && isPureVariable(source)) {\n            var datasource = resolveVariableAndFilter(source, this.props.superData, '| raw');\n            this.setState({\n                filterOptions: this.alterOptions(datasource)\n            });\n        }\n        else if (source && isEffectiveApi(source, data)) {\n            this.fetchOptions();\n        }\n        else if ((options === null || options === void 0 ? void 0 : options.length) > 0) {\n            this.setState({\n                filterOptions: this.alterOptions(filterable.options)\n            });\n        }\n    };\n    HeadCellFilterDropDown.prototype.componentDidUpdate = function (prevProps, prevState) {\n        var _a, _b, _c, _d;\n        var name = this.props.name;\n        var props = this.props;\n        this.sourceInvalid = false;\n        if (prevProps.name !== props.name ||\n            prevProps.filterable !== props.filterable ||\n            prevProps.data !== props.data) {\n            if (props.filterable.source) {\n                this.sourceInvalid = isApiOutdated(prevProps.filterable.source, props.filterable.source, prevProps.data, props.data);\n            }\n            else if (props.filterable.options) {\n                this.setState({\n                    filterOptions: this.alterOptions(props.filterable.options || [])\n                });\n            }\n            else if (name &&\n                !this.state.filterOptions.length &&\n                (Array.isArray((_a = props.store) === null || _a === void 0 ? void 0 : _a.data.itemsRaw) ||\n                    Array.isArray((_b = props.store) === null || _b === void 0 ? void 0 : _b.data.items))) {\n                var itemsRaw = ((_c = props.store) === null || _c === void 0 ? void 0 : _c.data.itemsRaw) || ((_d = props.store) === null || _d === void 0 ? void 0 : _d.data.items);\n                var values_1 = [];\n                itemsRaw.forEach(function (item) {\n                    var value = getVariable(item, name);\n                    if (!~values_1.indexOf(value)) {\n                        values_1.push(value);\n                    }\n                });\n                if (values_1.length) {\n                    this.setState({\n                        filterOptions: this.alterOptions(values_1)\n                    });\n                }\n            }\n        }\n        var value = this.props.data ? this.props.data[name] : undefined;\n        var prevValue = prevProps.data ? prevProps.data[name] : undefined;\n        if (value !== prevValue &&\n            this.state.filterOptions.length &&\n            prevState.filterOptions !== this.props.filterOptions) {\n            this.setState({\n                filterOptions: this.alterOptions(this.state.filterOptions)\n            });\n        }\n        this.sourceInvalid && this.fetchOptions();\n    };\n    HeadCellFilterDropDown.prototype.fetchOptions = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var _a, env, filterable, data, api, ret, options;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        _a = this.props, env = _a.env, filterable = _a.filterable, data = _a.data;\n                        if (!isEffectiveApi(filterable.source, data)) {\n                            return [2 /*return*/];\n                        }\n                        api = normalizeApi(filterable.source);\n                        api.cache = 3000; // 开启 3s 缓存，因为固顶位置渲染1次会额外多次请求。\n                        return [4 /*yield*/, env.fetcher(api, data)];\n                    case 1:\n                        ret = _b.sent();\n                        options = (ret.data && ret.data.options) || [];\n                        this.setState({\n                            filterOptions: ret && ret.data && this.alterOptions(options)\n                        });\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    HeadCellFilterDropDown.prototype.alterOptions = function (options, keyword) {\n        var _this = this;\n        if (keyword === void 0) { keyword = ''; }\n        var _a = this.props, data = _a.data, filterable = _a.filterable, name = _a.name;\n        var labelField = filterable.labelField, valueField = filterable.valueField;\n        var filterValue = data && typeof data[name] !== 'undefined' ? data[name] : '';\n        options = normalizeOptions(options);\n        //增加搜索功能\n        options = options.map(function (option) {\n            option.visible = !!matchSorter([option], keyword, {\n                keys: [labelField || 'label', valueField || 'value'],\n                threshold: matchSorter.rankings.CONTAINS\n            }).length;\n            return option;\n        });\n        if (filterable.multiple) {\n            options = options.map(function (option) { return (__assign(__assign({}, option), { selected: filterValue.split(',').indexOf(option.value) > -1 })); });\n        }\n        else {\n            options = options.map(function (option) { return (__assign(__assign({}, option), { selected: _this.optionComparator(option, filterValue) })); });\n        }\n        return options;\n    };\n    HeadCellFilterDropDown.prototype.optionComparator = function (option, selected) {\n        var filterable = this.props.filterable;\n        /**\n         * 无论是否严格模式，需要考虑CRUD开启syncLocation后，参数值会被转化为string的情况：\n         * 数字类需要特殊处理，如果两边都为数字类时才进行比较，否则不相等，排除 1 == true 这种情况\n         */\n        if (isNumeric(option.value)) {\n            return isNumeric(selected) ? option.value == selected : false;\n        }\n        return (filterable === null || filterable === void 0 ? void 0 : filterable.strictMode) === true\n            ? option.value === selected\n            : option.value == selected;\n    };\n    HeadCellFilterDropDown.prototype.handleClickOutside = function () {\n        this.close();\n    };\n    HeadCellFilterDropDown.prototype.open = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var _a, filterable, source, datasource;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        _a = this.props, filterable = _a.filterable, source = _a.source;\n                        if (!(filterable.refreshOnOpen && filterable.source)) return [3 /*break*/, 3];\n                        if (!(source && isPureVariable(source))) return [3 /*break*/, 1];\n                        datasource = resolveVariableAndFilter(source, this.props.superData, '| raw');\n                        this.setState({\n                            filterOptions: this.alterOptions(datasource)\n                        });\n                        return [3 /*break*/, 3];\n                    case 1: return [4 /*yield*/, this.fetchOptions()];\n                    case 2:\n                        _b.sent();\n                        _b.label = 3;\n                    case 3:\n                        this.setState({\n                            isOpened: true\n                        });\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    HeadCellFilterDropDown.prototype.close = function () {\n        this.setState({\n            isOpened: false\n        });\n    };\n    HeadCellFilterDropDown.prototype.handleClick = function (value) {\n        return __awaiter(this, void 0, void 0, function () {\n            var _a, onQuery, name, data, dispatchEvent, rendererEvent;\n            var _b;\n            return __generator(this, function (_c) {\n                switch (_c.label) {\n                    case 0:\n                        _a = this.props, onQuery = _a.onQuery, name = _a.name, data = _a.data, dispatchEvent = _a.dispatchEvent;\n                        return [4 /*yield*/, dispatchEvent('columnFilter', createObject(data, {\n                                filterName: name,\n                                filterValue: value\n                            }))];\n                    case 1:\n                        rendererEvent = _c.sent();\n                        if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {\n                            return [2 /*return*/];\n                        }\n                        onQuery((_b = {},\n                            _b[name] = value,\n                            _b), false, false, true);\n                        this.close();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    HeadCellFilterDropDown.prototype.handleCheck = function (value) {\n        return __awaiter(this, void 0, void 0, function () {\n            var _a, data, name, onQuery, dispatchEvent, query, rendererEvent;\n            var _b;\n            return __generator(this, function (_c) {\n                switch (_c.label) {\n                    case 0:\n                        _a = this.props, data = _a.data, name = _a.name, onQuery = _a.onQuery, dispatchEvent = _a.dispatchEvent;\n                        if (data[name] && data[name] === value) {\n                            query = '';\n                        }\n                        else {\n                            query =\n                                (data[name] && xor(data[name].split(','), [value]).join(',')) || value;\n                        }\n                        return [4 /*yield*/, dispatchEvent('columnFilter', createObject(data, {\n                                filterName: name,\n                                filterValue: query\n                            }))];\n                    case 1:\n                        rendererEvent = _c.sent();\n                        if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {\n                            return [2 /*return*/];\n                        }\n                        onQuery((_b = {},\n                            _b[name] = query,\n                            _b));\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    HeadCellFilterDropDown.prototype.handleReset = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var _a, name, dispatchEvent, data, onQuery, rendererEvent;\n            var _b;\n            return __generator(this, function (_c) {\n                switch (_c.label) {\n                    case 0:\n                        _a = this.props, name = _a.name, dispatchEvent = _a.dispatchEvent, data = _a.data, onQuery = _a.onQuery;\n                        return [4 /*yield*/, dispatchEvent('columnFilter', createObject(data, {\n                                filterName: name,\n                                filterValue: undefined\n                            }))];\n                    case 1:\n                        rendererEvent = _c.sent();\n                        if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {\n                            return [2 /*return*/];\n                        }\n                        onQuery((_b = {},\n                            _b[name] = undefined,\n                            _b), false, false, true);\n                        this.close();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    HeadCellFilterDropDown.prototype.handleSearch = function (keyword) {\n        var filterOptions = this.state.filterOptions;\n        this.setState({\n            keyword: keyword,\n            filterOptions: this.alterOptions(filterOptions, keyword)\n        });\n    };\n    HeadCellFilterDropDown.prototype.render = function () {\n        var _this = this;\n        var _a, _b, _c, _d;\n        var _e = this.state, isOpened = _e.isOpened, filterOptions = _e.filterOptions;\n        var _f = this.props, data = _f.data, name = _f.name, filterable = _f.filterable, popOverContainer = _f.popOverContainer, ns = _f.classPrefix, cx = _f.classnames, __ = _f.translate;\n        var searchConfig = (filterable === null || filterable === void 0 ? void 0 : filterable.searchConfig) || {};\n        return (React.createElement(\"span\", { className: cx(\"\".concat(ns, \"TableCell-filterBtn\"), data && typeof data[name] !== 'undefined' ? 'is-active' : '') },\n            React.createElement(\"span\", { onClick: this.open },\n                React.createElement(Icon, { icon: \"column-filter\", className: \"icon\" })),\n            isOpened ? (React.createElement(Overlay, { container: popOverContainer || (function () { return findDOMNode(_this); }), placement: \"left-bottom-left-top right-bottom-right-top\", target: popOverContainer ? function () { return findDOMNode(_this).parentNode; } : null, show: true },\n                React.createElement(PopOver, { classPrefix: ns, onHide: this.close, className: cx(\"\".concat(ns, \"TableCell-filterPopOver\"), filterable.className), overlay: true }, filterOptions && filterOptions.length > 0 ? (React.createElement(React.Fragment, null,\n                    (filterable === null || filterable === void 0 ? void 0 : filterable.searchable) ? (React.createElement(SearchBox, { className: cx('TableCell-filterPopOver-SearchBox', searchConfig === null || searchConfig === void 0 ? void 0 : searchConfig.className), mini: (_a = searchConfig.mini) !== null && _a !== void 0 ? _a : false, enhance: (_b = searchConfig.enhance) !== null && _b !== void 0 ? _b : false, clearable: (_c = searchConfig.clearable) !== null && _c !== void 0 ? _c : true, searchImediately: searchConfig.searchImediately, placeholder: searchConfig.placeholder, defaultValue: '', value: (_d = this.state.keyword) !== null && _d !== void 0 ? _d : '', onSearch: this.handleSearch, onChange: /** 为了消除react报错 */ noop })) : null,\n                    React.createElement(\"ul\", { className: cx('DropDown-menu') },\n                        !filterable.multiple\n                            ? filterOptions.map(function (option, index) {\n                                return option.visible && (React.createElement(\"li\", { key: index, className: cx({\n                                        'is-active': option.selected\n                                    }), onClick: _this.handleClick.bind(_this, option.value) }, option.label));\n                            })\n                            : filterOptions.map(function (option, index) {\n                                return option.visible && (React.createElement(\"li\", { key: index },\n                                    React.createElement(Checkbox, { size: \"sm\", classPrefix: ns, onChange: _this.handleCheck.bind(_this, option.value), checked: option.selected }, option.label)));\n                            }),\n                        filterOptions.some(function (item) { return item.selected; }) ? (React.createElement(\"li\", { key: \"DropDown-menu-reset\", onClick: this.handleReset.bind(this) }, __('reset'))) : null))) : null))) : null));\n    };\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [String]),\n        __metadata(\"design:returntype\", void 0)\n    ], HeadCellFilterDropDown.prototype, \"handleSearch\", null);\n    return HeadCellFilterDropDown;\n}(React.Component));\n\nexport { HeadCellFilterDropDown };\n", "/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __read, __assign, __awaiter, __generator } from 'tslib';\nimport React from 'react';\nimport { Icon } from 'amis-ui';\nimport { createObject, setVariable, Overlay, PopOver } from 'amis-core';\n\nfunction HeadCellSearchDropDown(_a) {\n    var _this = this;\n    var searchable = _a.searchable, name = _a.name, label = _a.label, onQuery = _a.onQuery, data = _a.data, dispatchEvent = _a.dispatchEvent, onAction = _a.onAction, cx = _a.classnames, __ = _a.translate, ns = _a.classPrefix, popOverContainer = _a.popOverContainer, render = _a.render, testIdBuilder = _a.testIdBuilder;\n    var ref = React.createRef();\n    var _b = __read(React.useMemo(function () {\n        var schema;\n        var formItems = [];\n        if (searchable === true) {\n            schema = {\n                title: '',\n                body: [\n                    {\n                        type: 'input-text',\n                        name: name,\n                        placeholder: label,\n                        clearable: true\n                    }\n                ]\n            };\n        }\n        else if (searchable) {\n            if (!searchable.type &&\n                (searchable.body || searchable.tabs || searchable.fieldSet)) {\n                // todo 删除此处代码，这些都是不推荐的用法\n                schema = __assign(__assign({ title: '' }, searchable), { body: Array.isArray(searchable.body)\n                        ? searchable.body.concat()\n                        : undefined });\n            }\n            else {\n                schema = {\n                    title: '',\n                    className: searchable.formClassName,\n                    body: [\n                        __assign({ type: searchable.type || 'input-text', name: searchable.name || name, placeholder: label }, searchable)\n                    ]\n                };\n            }\n        }\n        function findFormItems(schema) {\n            Array.isArray(schema.body) &&\n                schema.body.forEach(function (item) {\n                    item.name && formItems.push(item.name);\n                    item.extraName &&\n                        typeof item.extraName === 'string' &&\n                        formItems.push(item.extraName);\n                    findFormItems(item);\n                });\n        }\n        if (schema) {\n            // schema有可能配置为{type: 'form', body[]} 所以真正的formItem需要到form的body里去找\n            findFormItems(schema);\n            schema = __assign(__assign({}, schema), { type: 'form', wrapperComponent: 'div', canAccessSuperData: false, actions: [\n                    {\n                        type: 'button',\n                        label: __('reset'),\n                        actionType: 'clear-and-submit'\n                    },\n                    {\n                        type: 'button',\n                        label: __('cancel'),\n                        actionType: 'cancel'\n                    },\n                    {\n                        label: __('search'),\n                        type: 'submit',\n                        primary: true\n                    }\n                ] });\n        }\n        return [schema || 'error', formItems];\n    }, [searchable, name, label]), 2), formSchema = _b[0], formItems = _b[1];\n    var _c = __read(React.useState(false), 2), isOpened = _c[0], setIsOpened = _c[1];\n    var open = React.useCallback(function () { return setIsOpened(true); }, []);\n    var close = React.useCallback(function () { return setIsOpened(false); }, []);\n    var handleSubmit = React.useCallback(function (values) { return __awaiter(_this, void 0, void 0, function () {\n        var rendererEvent;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0: return [4 /*yield*/, dispatchEvent('columnSearch', createObject(data, {\n                        searchName: name,\n                        searchValue: values\n                    }))];\n                case 1:\n                    rendererEvent = _a.sent();\n                    if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {\n                        return [2 /*return*/];\n                    }\n                    close();\n                    onQuery(values);\n                    return [2 /*return*/];\n            }\n        });\n    }); }, []);\n    var handleAction = React.useCallback(function (e, action, ctx) {\n        if (action.actionType === 'cancel' || action.actionType === 'close') {\n            close();\n            return;\n        }\n        if (action.actionType === 'reset') {\n            close();\n            handleReset();\n            return;\n        }\n        onAction && onAction(e, action, ctx);\n    }, []);\n    var handleReset = React.useCallback(function () {\n        var values = __assign({}, data);\n        // todo 这里不精准，如果表单项有容器嵌套，这里将不正确\n        formItems.forEach(function (key) { return setVariable(values, key, undefined); });\n        onQuery(values);\n    }, [data]);\n    var isActive = React.useMemo(function () {\n        // todo 这里不精准，如果表单项有容器嵌套，这里将不正确\n        return formItems.some(function (key) { return data === null || data === void 0 ? void 0 : data[key]; });\n    }, [data]);\n    return (React.createElement(\"span\", __assign({ ref: ref, className: cx(\"\".concat(ns, \"TableCell-searchBtn\"), isActive ? 'is-active' : '', isOpened ? 'is-opened' : '') }, testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getTestId()),\n        React.createElement(\"span\", { onClick: open },\n            React.createElement(Icon, { icon: \"search\", className: \"icon\" })),\n        isOpened ? (React.createElement(Overlay, { container: popOverContainer || (function () { return ref.current; }), placement: \"left-bottom-left-top right-bottom-right-top\", target: popOverContainer ? function () { var _a; return (_a = ref.current) === null || _a === void 0 ? void 0 : _a.parentNode; } : null, show: true },\n            React.createElement(PopOver, { classPrefix: ns, onHide: close, className: cx(\"\".concat(ns, \"TableCell-searchPopOver\"), searchable.className), overlay: true }, render('quick-search-form', formSchema, {\n                popOverContainer: popOverContainer,\n                data: data,\n                onSubmit: handleSubmit,\n                onAction: handleAction\n            })))) : null));\n}\n\nexport { HeadCellSearchDropDown };\n", "/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __assign, __extends, __read } from 'tslib';\nimport React from 'react';\nimport { TableBody } from './TableBody.js';\nimport { observer } from 'mobx-react';\nimport ItemActionsWrapper from './ItemActionsWrapper.js';\nimport { Icon } from 'amis-ui';\nimport ColGroup from './ColGroup.js';\n\nfunction renderItemActions(props) {\n    var itemActions = props.itemActions, render = props.render, store = props.store, cx = props.classnames;\n    if (!store.hoverRow) {\n        return null;\n    }\n    var finalActions = Array.isArray(itemActions)\n        ? itemActions.filter(function (action) { return !action.hiddenOnHover; })\n        : [];\n    if (!finalActions.length) {\n        return null;\n    }\n    return (React.createElement(ItemActionsWrapper, { store: store, classnames: cx },\n        React.createElement(\"div\", { className: cx('Table-itemActions') }, finalActions.map(function (action, index) {\n            return render(\"itemAction/\".concat(index), __assign(__assign({}, action), { isMenuItem: true }), {\n                key: index,\n                item: store.hoverRow,\n                data: store.hoverRow.locals,\n                rowIndex: store.hoverRow.index\n            });\n        }))));\n}\nvar TableContent = /** @class */ (function (_super) {\n    __extends(TableContent, _super);\n    function TableContent() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    TableContent.prototype.render = function () {\n        var _a = this.props, placeholder = _a.placeholder, cx = _a.classnames, render = _a.render, className = _a.className, columns = _a.columns, columnsGroup = _a.columnsGroup, onMouseMove = _a.onMouseMove, onScroll = _a.onScroll, tableRef = _a.tableRef, rows = _a.rows, renderHeadCell = _a.renderHeadCell, renderCell = _a.renderCell, onCheck = _a.onCheck, onRowClick = _a.onRowClick, onRowDbClick = _a.onRowDbClick, onRowMouseEnter = _a.onRowMouseEnter, onRowMouseLeave = _a.onRowMouseLeave, rowClassName = _a.rowClassName, onQuickChange = _a.onQuickChange, footable = _a.footable, footableColumns = _a.footableColumns, checkOnItemClick = _a.checkOnItemClick, buildItemProps = _a.buildItemProps, onAction = _a.onAction, rowClassNameExpr = _a.rowClassNameExpr, affixRowClassName = _a.affixRowClassName, prefixRowClassName = _a.prefixRowClassName, data = _a.data, prefixRow = _a.prefixRow, locale = _a.locale, translate = _a.translate, itemAction = _a.itemAction, affixRow = _a.affixRow, store = _a.store, dispatchEvent = _a.dispatchEvent, onEvent = _a.onEvent, loading = _a.loading, testIdBuilder = _a.testIdBuilder, children = _a.children;\n        var tableClassName = cx('Table-table', this.props.tableClassName);\n        var hideHeader = columns.every(function (column) { return !column.label; });\n        return (React.createElement(\"div\", { onMouseMove: onMouseMove, className: cx('Table-content', className), onScroll: onScroll },\n            React.createElement(\"table\", { ref: tableRef, className: cx(tableClassName, store.tableLayout === 'fixed' ? 'is-layout-fixed' : undefined) },\n                React.createElement(ColGroup, { columns: columns, store: store }),\n                React.createElement(\"thead\", null,\n                    columnsGroup.length ? (React.createElement(\"tr\", null, columnsGroup.map(function (item, index) {\n                        var _a = __read(store.getStickyStyles(item, columnsGroup), 2), stickyStyle = _a[0], stickyClassName = _a[1];\n                        /**\n                         * 勾选列和展开列的表头单独成列\n                         * 如果分组列只有一个元素且未分组时，也要执行表头合并\n                         */\n                        return !!~['__checkme', '__expandme'].indexOf(item.has[0].type) ||\n                            (item.has.length === 1 &&\n                                !/^__/.test(item.has[0].type) &&\n                                !item.has[0].groupName) ? (renderHeadCell(item.has[0], {\n                            'data-index': item.has[0].index,\n                            'key': index,\n                            'colSpan': item.colSpan,\n                            'rowSpan': item.rowSpan,\n                            'style': stickyStyle,\n                            'className': stickyClassName\n                        })) : (React.createElement(\"th\", { key: index, \"data-index\": item.index, colSpan: item.colSpan, rowSpan: item.rowSpan, style: stickyStyle, className: stickyClassName }, item.label ? render('tpl', item.label) : null));\n                    }))) : null,\n                    React.createElement(\"tr\", { className: hideHeader ? 'fake-hide' : '' }, columns.map(function (column) {\n                        var _a;\n                        return ((_a = columnsGroup.find(function (group) { return ~group.has.indexOf(column); })) === null || _a === void 0 ? void 0 : _a.rowSpan) === 2\n                            ? null\n                            : renderHeadCell(column, {\n                                'data-index': column.index,\n                                'key': column.index\n                            });\n                    }))),\n                !rows.length ? (React.createElement(\"tbody\", null,\n                    React.createElement(\"tr\", { className: cx('Table-placeholder') }, !loading ? (React.createElement(\"td\", { colSpan: columns.length }, typeof placeholder === 'string' ? (React.createElement(React.Fragment, null,\n                        React.createElement(Icon, { icon: \"desk-empty\", className: cx('Table-placeholder-empty-icon', 'icon') }),\n                        translate(placeholder || 'placeholder.noData'))) : (render('placeholder', translate(placeholder || 'placeholder.noData'))))) : null))) : (React.createElement(TableBody, { store: store, itemAction: itemAction, classnames: cx, render: render, renderCell: renderCell, onCheck: onCheck, onRowClick: onRowClick, onRowDbClick: onRowDbClick, onRowMouseEnter: onRowMouseEnter, onRowMouseLeave: onRowMouseLeave, onQuickChange: onQuickChange, footable: footable, footableColumns: footableColumns, checkOnItemClick: checkOnItemClick, buildItemProps: buildItemProps, onAction: onAction, rowClassNameExpr: rowClassNameExpr, rowClassName: rowClassName, prefixRowClassName: prefixRowClassName, affixRowClassName: affixRowClassName, rows: rows, columns: columns, locale: locale, translate: translate, prefixRow: prefixRow, affixRow: affixRow, data: data, testIdBuilder: testIdBuilder, rowsProps: {\n                        dispatchEvent: dispatchEvent,\n                        onEvent: onEvent\n                    } }))),\n            children));\n    };\n    return TableContent;\n}(React.PureComponent));\nvar TableContent$1 = observer(function (props) {\n    var store = props.store;\n    // 分析 table/index.tsx 中的 renderHeadCell 依赖了以下属性\n    // store.someChecked;\n    // store.allChecked;\n    // store.isSelectionThresholdReached;\n    // store.allExpanded;\n    // store.orderBy\n    // store.orderDir\n    return (React.createElement(TableContent, __assign({}, props, { columnWidthReady: store.columnWidthReady, someChecked: store.someChecked, allChecked: store.allChecked, isSelectionThresholdReached: store.isSelectionThresholdReached, orderBy: store.orderBy, orderDir: store.orderDir })));\n});\n\nexport { TableContent, TableContent$1 as default, renderItemActions };\n", "/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __assign, __spreadArray, __read, __decorate, __metadata } from 'tslib';\nimport React from 'react';\nimport { filter, createObject, autobind } from 'amis-core';\nimport TableRow from './TableRow.js';\nimport { observer } from 'mobx-react';\n\nvar TableBody = /** @class */ (function (_super) {\n    __extends(TableBody, _super);\n    function TableBody() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    TableBody.prototype.componentDidMount = function () {\n        this.props.store.initTableWidth();\n    };\n    TableBody.prototype.testIdBuilder = function (rowPath) {\n        var _a;\n        return (_a = this.props.testIdBuilder) === null || _a === void 0 ? void 0 : _a.getChild(\"row-\".concat(rowPath));\n    };\n    TableBody.prototype.renderRows = function (rows, columns, rowProps, indexPath) {\n        var _this = this;\n        if (columns === void 0) { columns = this.props.columns; }\n        if (rowProps === void 0) { rowProps = {}; }\n        var _a = this.props, rowClassName = _a.rowClassName, rowClassNameExpr = _a.rowClassNameExpr, onAction = _a.onAction, buildItemProps = _a.buildItemProps, checkOnItemClick = _a.checkOnItemClick, cx = _a.classnames, render = _a.render, renderCell = _a.renderCell, onCheck = _a.onCheck, onQuickChange = _a.onQuickChange, footable = _a.footable, ignoreFootableContent = _a.ignoreFootableContent, footableColumns = _a.footableColumns, itemAction = _a.itemAction, onRowClick = _a.onRowClick, onRowDbClick = _a.onRowDbClick, onRowMouseEnter = _a.onRowMouseEnter, onRowMouseLeave = _a.onRowMouseLeave, store = _a.store;\n        return rows.map(function (item, rowIndex) {\n            var itemProps = buildItemProps ? buildItemProps(item, rowIndex) : null;\n            var rowPath = \"\".concat(indexPath ? indexPath + '/' : '').concat(rowIndex);\n            var doms = [\n                React.createElement(TableRow, __assign({}, itemProps, { testIdBuilder: _this.testIdBuilder, store: store, itemAction: itemAction, classnames: cx, checkOnItemClick: checkOnItemClick, key: item.id, itemIndex: rowIndex, rowPath: rowPath, item: item, itemClassName: cx(rowClassNameExpr\n                        ? filter(rowClassNameExpr, item.locals)\n                        : rowClassName, {\n                        'is-last': item.depth > 1 &&\n                            rowIndex === rows.length - 1 &&\n                            !item.children.length\n                    }), columns: columns, renderCell: renderCell, render: render, onAction: onAction, onCheck: onCheck, \n                    // todo 先注释 quickEditEnabled={item.depth === 1}\n                    onQuickChange: onQuickChange, onRowClick: onRowClick, onRowDbClick: onRowDbClick, onRowMouseEnter: onRowMouseEnter, onRowMouseLeave: onRowMouseLeave }, rowProps))\n            ];\n            if (footable && footableColumns.length) {\n                if (item.depth === 1) {\n                    doms.push(React.createElement(TableRow, __assign({}, itemProps, { store: store, itemAction: itemAction, classnames: cx, checkOnItemClick: checkOnItemClick, key: \"foot-\".concat(item.id), itemIndex: rowIndex, rowPath: rowPath, item: item, itemClassName: cx(rowClassNameExpr\n                            ? filter(rowClassNameExpr, item.locals)\n                            : rowClassName), columns: footableColumns, renderCell: renderCell, render: render, onAction: onAction, onCheck: onCheck, onRowClick: onRowClick, onRowDbClick: onRowDbClick, onRowMouseEnter: onRowMouseEnter, onRowMouseLeave: onRowMouseLeave, footableMode: true, footableColSpan: columns.length, onQuickChange: onQuickChange, ignoreFootableContent: ignoreFootableContent }, rowProps, { testIdBuilder: _this.testIdBuilder })));\n                }\n            }\n            else if (item.children.length && item.expanded) {\n                // 嵌套表格\n                doms.push.apply(doms, __spreadArray([], __read(_this.renderRows(item.children, columns, __assign(__assign({}, rowProps), { parent: item }), rowPath)), false));\n            }\n            return doms;\n        });\n    };\n    TableBody.prototype.renderSummaryRow = function (position, items, rowIndex) {\n        var _a, _b;\n        var _c = this.props, columns = _c.columns, render = _c.render, data = _c.data, cx = _c.classnames, rows = _c.rows, prefixRowClassName = _c.prefixRowClassName, affixRowClassName = _c.affixRowClassName, store = _c.store;\n        if (!(Array.isArray(items) && items.length)) {\n            return null;\n        }\n        var offset = 0;\n        // 将列的隐藏对应的把总结行也隐藏起来\n        var result = items\n            .map(function (item, index) {\n            var colIdxs = [offset + index];\n            if (item.colSpan > 1) {\n                for (var i = 1; i < item.colSpan; i++) {\n                    colIdxs.push(offset + index + i);\n                }\n                offset += item.colSpan - 1;\n            }\n            var matchedColumns = colIdxs\n                .map(function (idx) { return columns.find(function (col) { return col.rawIndex === idx; }); })\n                .filter(function (item) { return item; });\n            return __assign(__assign({}, item), { colSpan: matchedColumns.length, firstColumn: matchedColumns[0], lastColumn: matchedColumns[matchedColumns.length - 1] });\n        })\n            .filter(function (item) { return item.colSpan; });\n        //  如果是勾选栏，或者是展开栏，或者是拖拽栏，让它和下一列合并。\n        if (result[0] &&\n            typeof ((_a = columns[0]) === null || _a === void 0 ? void 0 : _a.type) === 'string' &&\n            ((_b = columns[0]) === null || _b === void 0 ? void 0 : _b.type.substring(0, 2)) === '__') {\n            result[0].firstColumn = columns[0];\n            result[0].colSpan = (result[0].colSpan || 1) + 1;\n        }\n        // 缺少的单元格补齐\n        var resultLen = result.reduce(function (p, c) { return p + (c.colSpan || 1); }, 0);\n        var appendLen = columns.length - resultLen;\n        // 多了则干掉一些\n        while (appendLen < 0) {\n            var item = result.pop();\n            if (!item) {\n                break;\n            }\n            appendLen += item.colSpan || 1;\n        }\n        // 少了则补个空的\n        // 只补空的时，当存在fixed:right时会导致样式有问题 会把其他列的盖住\n        if (appendLen) {\n            var item = {\n                type: 'html',\n                html: '&nbsp;'\n            };\n            for (var i = resultLen; i < store.filteredColumns.length; i++) {\n                var matchedColumns = [i]\n                    .map(function (idx) { return store.filteredColumns.find(function (col) { return col.rawIndex === idx; }); })\n                    .filter(function (item) { return item; });\n                result.push(__assign(__assign({}, item), { colSpan: matchedColumns.length, firstColumn: matchedColumns[0], lastColumn: matchedColumns[matchedColumns.length - 1] }));\n            }\n        }\n        var ctx = createObject(data, {\n            items: rows.map(function (row) { return row.locals; })\n        });\n        return (React.createElement(\"tr\", { className: cx('Table-table-tr', 'is-summary', position === 'prefix' ? prefixRowClassName : '', position === 'affix' ? affixRowClassName : ''), key: \"summary-\".concat(position, \"-\").concat(rowIndex || 0) }, result.map(function (item, index) {\n            var Com = item.isHead ? 'th' : 'td';\n            var firstColumn = item.firstColumn;\n            var lastColumn = item.lastColumn;\n            var style = __assign({}, item.style);\n            if (item.align) {\n                style.textAlign = item.align;\n            }\n            if (item.vAlign) {\n                style.verticalAlign = item.vAlign;\n            }\n            var _a = __read(store.getStickyStyles(lastColumn.fixed === 'right' ? lastColumn : firstColumn, store.filteredColumns, item.colSpan), 2), stickyStyle = _a[0], stickyClassName = _a[1];\n            Object.assign(style, stickyStyle);\n            return (React.createElement(Com, { key: index, colSpan: item.colSpan == 1 ? undefined : item.colSpan, style: style, className: (item.cellClassName || '') + ' ' + stickyClassName }, render(\"summary-row/\".concat(index), item, {\n                data: ctx\n            })));\n        })));\n    };\n    TableBody.prototype.renderSummary = function (position, items) {\n        var _this = this;\n        return Array.isArray(items)\n            ? items.some(function (i) { return Array.isArray(i); })\n                ? items.map(function (i, rowIndex) {\n                    return _this.renderSummaryRow(position, Array.isArray(i) ? i : [i], rowIndex);\n                })\n                : this.renderSummaryRow(position, items)\n            : null;\n    };\n    TableBody.prototype.render = function () {\n        var _a = this.props, cx = _a.classnames, className = _a.className, render = _a.render, rows = _a.rows, columns = _a.columns, rowsProps = _a.rowsProps, prefixRow = _a.prefixRow, affixRow = _a.affixRow, __ = _a.translate;\n        return (React.createElement(\"tbody\", { className: className }, rows.length ? (React.createElement(React.Fragment, null,\n            this.renderSummary('prefix', prefixRow),\n            this.renderRows(rows, columns, rowsProps),\n            this.renderSummary('affix', affixRow))) : null));\n    };\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [String]),\n        __metadata(\"design:returntype\", void 0)\n    ], TableBody.prototype, \"testIdBuilder\", null);\n    TableBody = __decorate([\n        observer\n    ], TableBody);\n    return TableBody;\n}(React.Component));\n\nexport { TableBody };\n", "/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __awaiter, __generator, __assign, __rest, __decorate, __metadata } from 'tslib';\nimport { observer } from 'mobx-react';\nimport React from 'react';\nimport { isClickOnInput, keyToPath, setVariable, autobind } from 'amis-core';\nimport { Action } from '../Action.js';\nimport { useInView } from 'react-intersection-observer';\n\nvar TableRow = /** @class */ (function (_super) {\n    __extends(TableRow, _super);\n    function TableRow() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    TableRow.prototype.handleMouseEnter = function (e) {\n        var _a = this.props, item = _a.item, itemIndex = _a.itemIndex, onRowMouseEnter = _a.onRowMouseEnter;\n        onRowMouseEnter === null || onRowMouseEnter === void 0 ? void 0 : onRowMouseEnter(item, itemIndex);\n    };\n    TableRow.prototype.handleMouseLeave = function (e) {\n        var _a = this.props, item = _a.item, itemIndex = _a.itemIndex, onRowMouseLeave = _a.onRowMouseLeave;\n        onRowMouseLeave === null || onRowMouseLeave === void 0 ? void 0 : onRowMouseLeave(item, itemIndex);\n    };\n    // 定义点击一行的行为，通过 itemAction配置\n    TableRow.prototype.handleItemClick = function (e) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function () {\n            var shiftKey, _b, itemAction, onAction, item, itemIndex, onCheck, onRowClick, checkOnItemClick, rendererEvent;\n            return __generator(this, function (_c) {\n                switch (_c.label) {\n                    case 0:\n                        if (isClickOnInput(e)) {\n                            return [2 /*return*/];\n                        }\n                        shiftKey = (_a = e.nativeEvent) === null || _a === void 0 ? void 0 : _a.shiftKey;\n                        e.preventDefault();\n                        e.stopPropagation();\n                        _b = this.props, itemAction = _b.itemAction, onAction = _b.onAction, item = _b.item, itemIndex = _b.itemIndex, onCheck = _b.onCheck, onRowClick = _b.onRowClick, checkOnItemClick = _b.checkOnItemClick;\n                        return [4 /*yield*/, (onRowClick === null || onRowClick === void 0 ? void 0 : onRowClick(item, itemIndex))];\n                    case 1:\n                        rendererEvent = _c.sent();\n                        if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {\n                            return [2 /*return*/];\n                        }\n                        if (itemAction) {\n                            onAction && onAction(e, itemAction, item === null || item === void 0 ? void 0 : item.locals);\n                            // item.toggle();\n                        }\n                        else {\n                            if (item.checkable && item.isCheckAvaiableOnClick && checkOnItemClick) {\n                                onCheck === null || onCheck === void 0 ? void 0 : onCheck(item, !item.checked, shiftKey);\n                            }\n                        }\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    TableRow.prototype.handleDbClick = function (e) {\n        var _a = this.props, item = _a.item, itemIndex = _a.itemIndex, onRowDbClick = _a.onRowDbClick;\n        onRowDbClick === null || onRowDbClick === void 0 ? void 0 : onRowDbClick(item, itemIndex);\n    };\n    TableRow.prototype.handleAction = function (e, action, ctx) {\n        var _a = this.props, onAction = _a.onAction, item = _a.item;\n        return onAction && onAction(e, action, ctx || item.locals);\n    };\n    TableRow.prototype.handleQuickChange = function (values, saveImmediately, savePristine, options) {\n        var _a = this.props, onQuickChange = _a.onQuickChange, item = _a.item;\n        onQuickChange &&\n            onQuickChange(item, values, saveImmediately, savePristine, options);\n    };\n    TableRow.prototype.handleChange = function (value, name, submit, changePristine) {\n        if (!name || typeof name !== 'string') {\n            return;\n        }\n        var _a = this.props, item = _a.item, onQuickChange = _a.onQuickChange;\n        var data = {};\n        var keyPath = keyToPath(name);\n        // 如果是带路径的值变化，最好是能保留原来的对象的其他属性\n        if (keyPath.length > 1) {\n            data[keyPath[0]] = __assign({}, item.data[keyPath[0]]);\n        }\n        setVariable(data, name, value);\n        onQuickChange === null || onQuickChange === void 0 ? void 0 : onQuickChange(item, data, submit, changePristine);\n    };\n    TableRow.prototype.render = function () {\n        var _a, _b;\n        var _this = this;\n        var _c;\n        var _d = this.props, itemClassName = _d.itemClassName, itemIndex = _d.itemIndex, item = _d.item, columns = _d.columns, renderCell = _d.renderCell, children = _d.children, footableMode = _d.footableMode, ignoreFootableContent = _d.ignoreFootableContent, footableColSpan = _d.footableColSpan, regionPrefix = _d.regionPrefix, checkOnItemClick = _d.checkOnItemClick, ns = _d.classPrefix, render = _d.render, cx = _d.classnames, parent = _d.parent, itemAction = _d.itemAction, onEvent = _d.onEvent, expanded = _d.expanded, parentExpanded = _d.parentExpanded, id = _d.id, newIndex = _d.newIndex, isHover = _d.isHover, checked = _d.checked, modified = _d.modified, moved = _d.moved, depth = _d.depth, expandable = _d.expandable, appeard = _d.appeard, checkdisable = _d.checkdisable, trRef = _d.trRef, isNested = _d.isNested, testIdBuilder = _d.testIdBuilder, rowPath = _d.rowPath, rest = __rest(_d, [\"itemClassName\", \"itemIndex\", \"item\", \"columns\", \"renderCell\", \"children\", \"footableMode\", \"ignoreFootableContent\", \"footableColSpan\", \"regionPrefix\", \"checkOnItemClick\", \"classPrefix\", \"render\", \"classnames\", \"parent\", \"itemAction\", \"onEvent\", \"expanded\", \"parentExpanded\", \"id\", \"newIndex\", \"isHover\", \"checked\", \"modified\", \"moved\", \"depth\", \"expandable\", \"appeard\", \"checkdisable\", \"trRef\", \"isNested\", \"testIdBuilder\", \"rowPath\"]);\n        if (footableMode) {\n            if (!expanded) {\n                return null;\n            }\n            return (React.createElement(\"tr\", { ref: trRef, \"data-id\": id, \"data-index\": newIndex, onClick: checkOnItemClick || itemAction || (onEvent === null || onEvent === void 0 ? void 0 : onEvent.rowClick)\n                    ? this.handleItemClick\n                    : undefined, onDoubleClick: this.handleDbClick, onMouseEnter: this.handleMouseEnter, onMouseLeave: this.handleMouseLeave, className: cx('Table-table-tr', itemClassName, (_a = {\n                        'is-hovered': isHover,\n                        'is-checked': checked,\n                        'is-modified': modified,\n                        'is-moved': moved\n                    },\n                    _a[\"Table-tr--hasItemAction\"] = itemAction,\n                    _a[\"Table-tr--odd\"] = itemIndex % 2 === 0,\n                    _a[\"Table-tr--even\"] = itemIndex % 2 === 1,\n                    _a)) },\n                React.createElement(\"td\", { className: cx(\"Table-foot\"), colSpan: footableColSpan },\n                    React.createElement(\"table\", { className: cx(\"Table-footTable\") },\n                        React.createElement(\"tbody\", null, ignoreFootableContent\n                            ? columns.map(function (column) { return (React.createElement(\"tr\", { key: column.id },\n                                column.label !== false ? React.createElement(\"th\", null) : null,\n                                React.createElement(\"td\", null))); })\n                            : columns.map(function (column) { return (React.createElement(\"tr\", { key: column.id },\n                                column.label !== false ? (React.createElement(\"th\", null, render(\"\".concat(regionPrefix).concat(itemIndex, \"/\").concat(column.index, \"/tpl\"), column.label))) : null,\n                                appeard ? (renderCell(\"\".concat(regionPrefix).concat(itemIndex, \"/\").concat(column.index), column, item, __assign(__assign({}, rest), { width: null, rowIndex: itemIndex, rowIndexPath: item.path, colIndex: column.index, rowPath: rowPath, key: column.id, onAction: _this.handleAction, onQuickChange: _this.handleQuickChange, onChange: _this.handleChange }))) : (React.createElement(\"td\", { key: column.id },\n                                    React.createElement(\"div\", { className: cx('Table-emptyBlock') }, \"\\u00A0\"))))); }))))));\n        }\n        if (parent && !parent.expanded) {\n            return null;\n        }\n        return (React.createElement(\"tr\", __assign({ ref: trRef, onClick: checkOnItemClick || itemAction || (onEvent === null || onEvent === void 0 ? void 0 : onEvent.rowClick)\n                ? this.handleItemClick\n                : undefined, onDoubleClick: this.handleDbClick, onMouseEnter: this.handleMouseEnter, onMouseLeave: this.handleMouseLeave, \"data-index\": depth === 1 ? newIndex : undefined, \"data-id\": id, className: cx('Table-table-tr', itemClassName, (_b = {\n                    'is-hovered': isHover,\n                    'is-checked': checked,\n                    'is-modified': modified,\n                    'is-moved': moved,\n                    'is-expanded': expanded && expandable,\n                    'is-expandable': expandable\n                },\n                _b[\"Table-tr--hasItemAction\"] = itemAction,\n                _b[\"Table-tr--odd\"] = itemIndex % 2 === 0,\n                _b[\"Table-tr--even\"] = itemIndex % 2 === 1,\n                _b), \"Table-tr--\".concat(depth, \"th\")) }, (_c = testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder(rowPath)) === null || _c === void 0 ? void 0 : _c.getTestId()), columns.map(function (column) {\n            return appeard ? (renderCell(\"\".concat(itemIndex, \"/\").concat(column.index), column, item, __assign(__assign({}, rest), { rowIndex: itemIndex, colIndex: column.index, rowIndexPath: item.path, rowPath: rowPath, key: column.id, onAction: _this.handleAction, onQuickChange: _this.handleQuickChange, onChange: _this.handleChange }))) : column.name && item.rowSpans[column.name] === 0 ? null : (React.createElement(\"td\", { key: column.id },\n                React.createElement(\"div\", { className: cx('Table-emptyBlock') }, \"\\u00A0\")));\n        })));\n    };\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], TableRow.prototype, \"handleMouseEnter\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], TableRow.prototype, \"handleMouseLeave\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object]),\n        __metadata(\"design:returntype\", Promise)\n    ], TableRow.prototype, \"handleItemClick\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], TableRow.prototype, \"handleDbClick\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object, Action, Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], TableRow.prototype, \"handleAction\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object, Boolean, Boolean, Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], TableRow.prototype, \"handleQuickChange\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object, String, Boolean, Boolean]),\n        __metadata(\"design:returntype\", void 0)\n    ], TableRow.prototype, \"handleChange\", null);\n    return TableRow;\n}(React.PureComponent));\n// 换成 mobx-react-lite 模式\nvar TableRow$1 = observer(function (props) {\n    var item = props.item;\n    var parent = props.parent;\n    var store = props.store;\n    var columns = props.columns;\n    var canAccessSuperData = store.canAccessSuperData ||\n        columns.some(function (item) { return item.pristine.canAccessSuperData; });\n    var _a = useInView({\n        threshold: 0,\n        onChange: item.markAppeared,\n        skip: !item.lazyRender\n    }), ref = _a.ref, inView = _a.inView;\n    return (React.createElement(TableRow, __assign({}, props, { trRef: ref, expanded: item.expanded, parentExpanded: parent === null || parent === void 0 ? void 0 : parent.expanded, id: item.id, newIndex: item.newIndex, isHover: item.isHover, partial: item.partial, checked: item.checked, modified: item.modified, moved: item.moved, depth: item.depth, expandable: item.expandable, checkdisable: item.checkdisable, loading: item.loading, error: item.error, \n        // data 在 TableRow 里面没有使用，这里写上是为了当列数据变化的时候 TableRow 重新渲染，\n        // 不是 item.locals 的原因是 item.locals 会变化多次，比如父级上下文变化也会进来，但是 item.data 只会变化一次。\n        data: canAccessSuperData ? item.locals : item.data, appeard: item.lazyRender ? item.appeared || inView : true, isNested: store.isNested })));\n});\n\nexport { TableRow, TableRow$1 as default };\n", "/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { observer } from 'mobx-react';\nimport React, { useRef, useEffect } from 'react';\n\nfunction ItemActionsWrapper(props) {\n    var _a;\n    var cx = props.classnames;\n    var children = props.children;\n    var store = props.store;\n    var divRef = useRef(null);\n    var updatePosition = React.useCallback(function (id) {\n        var _a, _b;\n        if (id === void 0) { id = ((_a = store.hoverRow) === null || _a === void 0 ? void 0 : _a.id) || ''; }\n        var frame = (_b = divRef.current.parentElement) === null || _b === void 0 ? void 0 : _b.querySelector('table');\n        var dom = frame === null || frame === void 0 ? void 0 : frame.querySelector(\"tr[data-id=\\\"\".concat(id, \"\\\"]\"));\n        if (!dom) {\n            return;\n        }\n        var rect = dom.getBoundingClientRect();\n        var height = rect.height;\n        var top = rect.top -\n            frame.getBoundingClientRect().top +\n            parseInt(getComputedStyle(frame)['marginTop'], 10);\n        divRef.current.style.cssText += \"top: \".concat(top, \"px;height: \").concat(height, \"px; left: \").concat(frame.parentElement.scrollLeft, \"px;\");\n    }, []);\n    useEffect(function () {\n        var row = store.hoverRow;\n        if (!row) {\n            return;\n        }\n        updatePosition(row.id);\n    }, [(_a = store.hoverRow) === null || _a === void 0 ? void 0 : _a.id]);\n    useEffect(function () {\n        var frame = divRef.current.parentElement;\n        if (!frame) {\n            return;\n        }\n        var onScroll = function () {\n            var _a;\n            updatePosition((_a = store.hoverRow) === null || _a === void 0 ? void 0 : _a.id);\n        };\n        frame.addEventListener('scroll', onScroll);\n        return function () {\n            frame.removeEventListener('scroll', onScroll);\n        };\n    });\n    return (React.createElement(\"div\", { className: cx('Table-itemActions-wrap'), ref: divRef }, children));\n}\nvar ItemActionsWrapper$1 = observer(ItemActionsWrapper);\n\nexport { ItemActionsWrapper$1 as default };\n", "/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport React from 'react';\nimport { isSafari, chromeVersion } from 'amis-core';\nimport { observer } from 'mobx-react';\n\nfunction ColGroup(_a) {\n    var columns = _a.columns, store = _a.store;\n    var domRef = React.createRef();\n    React.useEffect(function () {\n        var table = domRef.current.parentElement;\n        var trs = [];\n        function reConnect() {\n            // 整体监听不准，因为整体可能不会宽度变化\n            // 监控 thead 下面所有的 th 的 resize 变化\n            // 如果变化了，需要重新更新表格宽度计算\n            var doms = [].slice.call(table.querySelectorAll(':scope > thead > tr > *'));\n            // 先看 th 本身有没有变化，如果没变化，就不要重新监听了\n            if (doms.some(function (d, index) { return trs[index] !== d; })) {\n                observer.disconnect();\n                trs = doms;\n                doms.forEach(function (dom) {\n                    observer.observe(dom);\n                });\n            }\n        }\n        var observer = new ResizeObserver(function () {\n            reConnect();\n            store.syncTableWidth();\n        });\n        store.initTableWidth();\n        store.syncTableWidth();\n        reConnect();\n        return function () {\n            observer.disconnect();\n        };\n    }, [columns.length]);\n    // 解决 chrome 91 以下版本的设置 colgroup>col 的 width 属性无效的问题\n    // 低版本同时设置 thead>th\n    // The problem is min-width CSS property.\n    // Before Chrome 91, min-width was ignored on COL elements. 91 no longer ignores it.\n    //\n    // 同时 safari 也存在类似问题，设置 colgroup>col 的 width 属性无效\n    if (isSafari || (typeof chromeVersion === 'number' && chromeVersion < 91)) {\n        React.useEffect(function () {\n            if (domRef.current) {\n                var ths = [].slice.call(domRef.current.parentElement.querySelectorAll(':scope > thead > tr > th[data-index]'));\n                ths.forEach(function (th) {\n                    var index = parseInt(th.getAttribute('data-index'), 10);\n                    var column = store.columns[index];\n                    var style = '';\n                    var width = -1;\n                    if (store.columnWidthReady && column.width) {\n                        width = column.width;\n                    }\n                    else if (column.pristine.width) {\n                        width = column.pristine.width;\n                    }\n                    if (width === -1) {\n                        return;\n                    }\n                    style += \"width: \".concat(\n                    // 有可能是百分比\n                    typeof width === 'number' ? \"\".concat(width, \"px\") : width, \";\");\n                    if (store.tableLayout === 'auto') {\n                        style += \"min-width: \".concat(typeof width === 'number' ? \"\".concat(width, \"px\") : width, \";\");\n                    }\n                    th.style.cssText = style;\n                });\n            }\n        }, columns.map(function (column) { return column.width; }).concat(store.columnWidthReady));\n    }\n    return (React.createElement(\"colgroup\", { ref: domRef }, columns.map(function (column) {\n        var style = {};\n        if (store.columnWidthReady && column.width) {\n            style.width = column.width;\n        }\n        else if (column.pristine.width) {\n            style.width = column.pristine.width;\n        }\n        if (store.tableLayout === 'auto' && style.width) {\n            style.minWidth = style.width;\n        }\n        return React.createElement(\"col\", { \"data-index\": column.index, style: style, key: column.id });\n    })));\n}\nvar ColGroup$1 = observer(ColGroup);\n\nexport { ColGroup, ColGroup$1 as default };\n", "/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __values, __assign, __awaiter, __generator } from 'tslib';\nimport { filter, removeHTMLTag, decodeEntity, isEffectiveApi, isPureVariable, resolveVariableAndFilter, createObject, isObject, getImageDimensions, toDataURL, getVariable, arraySlice, flattenTree, TableStore } from 'amis-core';\nimport './ColumnToggler.js';\nimport { saveAs } from 'file-saver';\nimport memoize from 'lodash/memoize';\nimport { getSnapshot } from 'mobx-state-tree';\nimport moment from 'moment';\n\n/**\n * 导出 Excel 功能\n */\nvar loadDb = function () {\n    // @ts-ignore\n    return import('amis-ui/lib/components/CityDB');\n};\n/**\n * 将 url 转成绝对地址\n */\nvar getAbsoluteUrl = (function () {\n    var link;\n    return function (url) {\n        if (!link)\n            link = document.createElement('a');\n        link.href = url;\n        return link.href;\n    };\n})();\n/**\n * 将 computedStyle 的 rgba 转成 argb hex\n */\nvar rgba2argb = memoize(function (rgba) {\n    var color = \"\".concat(rgba\n        .match(/^rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)(?:,\\s*(\\d+\\.{0,1}\\d*))?\\)$/)\n        .slice(1)\n        .map(function (n, i) {\n        return (i === 3 ? Math.round(parseFloat(n) * 255) : parseFloat(n))\n            .toString(16)\n            .padStart(2, '0')\n            .replace('NaN', '');\n    })\n        .join(''));\n    if (color.length === 6) {\n        return 'FF' + color;\n    }\n    return color;\n});\n/**\n * 将 classname 转成对应的 excel 样式，只支持字体颜色、粗细、背景色\n */\nvar getCellStyleByClassName = memoize(function (className) {\n    if (!className)\n        return {};\n    var classNameElm = document.getElementsByClassName(className).item(0);\n    if (classNameElm) {\n        var computedStyle = getComputedStyle(classNameElm);\n        var font = {};\n        var fill = {};\n        if (computedStyle.color && computedStyle.color.indexOf('rgb') !== -1) {\n            var color = rgba2argb(computedStyle.color);\n            // 似乎不支持完全透明的情况，所以就不设置\n            if (!color.startsWith('00')) {\n                font['color'] = { argb: color };\n            }\n        }\n        if (computedStyle.fontWeight && parseInt(computedStyle.fontWeight) >= 700) {\n            font['bold'] = true;\n        }\n        if (computedStyle.backgroundColor &&\n            computedStyle.backgroundColor.indexOf('rgb') !== -1) {\n            var color = rgba2argb(computedStyle.backgroundColor);\n            if (!color.startsWith('00')) {\n                fill = {\n                    type: 'pattern',\n                    pattern: 'solid',\n                    fgColor: { argb: color }\n                };\n            }\n        }\n        return { font: font, fill: fill };\n    }\n    return {};\n});\n/**\n * 设置单元格样式\n */\nvar applyCellStyle = function (sheetRow, columIndex, schema, data) {\n    var e_1, _a, e_2, _b;\n    var cellStyle = {};\n    if (schema.className) {\n        try {\n            for (var _c = __values(schema.className.split(/\\s+/)), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var className = _d.value;\n                var style = getCellStyleByClassName(className);\n                if (style) {\n                    cellStyle = __assign(__assign({}, cellStyle), style);\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    }\n    if (schema.classNameExpr) {\n        var classNames = filter(schema.classNameExpr, data);\n        if (classNames) {\n            try {\n                for (var _e = __values(classNames.split(/\\s+/)), _f = _e.next(); !_f.done; _f = _e.next()) {\n                    var className = _f.value;\n                    var style = getCellStyleByClassName(className);\n                    if (style) {\n                        cellStyle = __assign(__assign({}, cellStyle), style);\n                    }\n                }\n            }\n            catch (e_2_1) { e_2 = { error: e_2_1 }; }\n            finally {\n                try {\n                    if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n                }\n                finally { if (e_2) throw e_2.error; }\n            }\n        }\n    }\n    if (cellStyle.font && Object.keys(cellStyle.font).length > 0) {\n        sheetRow.getCell(columIndex).font = cellStyle.font;\n    }\n    if (cellStyle.fill && Object.keys(cellStyle.fill).length > 0) {\n        sheetRow.getCell(columIndex).fill = cellStyle.fill;\n    }\n};\n/**\n * 输出总结行\n */\nvar renderSummary = function (worksheet, data, summarySchema, rowIndex) {\n    var e_3, _a, e_4, _b;\n    if (summarySchema && summarySchema.length > 0) {\n        var firstSchema = summarySchema[0];\n        // 总结行支持二维数组，所以统一转成二维数组来方便操作\n        var affixRows = summarySchema;\n        if (!Array.isArray(firstSchema)) {\n            affixRows = [summarySchema];\n        }\n        try {\n            for (var affixRows_1 = __values(affixRows), affixRows_1_1 = affixRows_1.next(); !affixRows_1_1.done; affixRows_1_1 = affixRows_1.next()) {\n                var affix = affixRows_1_1.value;\n                rowIndex += 1;\n                var sheetRow = worksheet.getRow(rowIndex);\n                var columIndex = 0;\n                try {\n                    for (var affix_1 = (e_4 = void 0, __values(affix)), affix_1_1 = affix_1.next(); !affix_1_1.done; affix_1_1 = affix_1.next()) {\n                        var col = affix_1_1.value;\n                        columIndex += 1;\n                        // 文档示例中只有这两种，所以主要支持这两种，没法支持太多，因为没法用 react 渲染结果\n                        if (col.text) {\n                            sheetRow.getCell(columIndex).value = col.text;\n                        }\n                        if (col.tpl) {\n                            sheetRow.getCell(columIndex).value = removeHTMLTag(decodeEntity(filter(col.tpl, data)));\n                        }\n                        // 处理合并行\n                        if (col.colSpan) {\n                            worksheet.mergeCells(rowIndex, columIndex, rowIndex, columIndex + col.colSpan - 1);\n                            columIndex += col.colSpan - 1;\n                        }\n                    }\n                }\n                catch (e_4_1) { e_4 = { error: e_4_1 }; }\n                finally {\n                    try {\n                        if (affix_1_1 && !affix_1_1.done && (_b = affix_1.return)) _b.call(affix_1);\n                    }\n                    finally { if (e_4) throw e_4.error; }\n                }\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (affixRows_1_1 && !affixRows_1_1.done && (_a = affixRows_1.return)) _a.call(affixRows_1);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n    }\n    return rowIndex;\n};\n/**\n * 获取 map 的映射数据\n * @param remoteMappingCache 缓存\n * @param env mobx env\n * @param column 列配置\n * @param data 上下文数据\n * @param rowData 当前行数据\n * @returns\n */\nfunction getMap(remoteMappingCache, env, column, data, rowData) {\n    return __awaiter(this, void 0, void 0, function () {\n        var map, source, sourceValue, mapKey, res;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    map = column.pristine.map;\n                    source = column.pristine.source;\n                    if (!source) return [3 /*break*/, 4];\n                    sourceValue = source;\n                    if (!isPureVariable(source)) return [3 /*break*/, 1];\n                    map = resolveVariableAndFilter(source, rowData, '| raw');\n                    return [3 /*break*/, 4];\n                case 1:\n                    if (!isEffectiveApi(source, data)) return [3 /*break*/, 4];\n                    mapKey = JSON.stringify(source);\n                    if (!(mapKey in remoteMappingCache)) return [3 /*break*/, 2];\n                    map = remoteMappingCache[mapKey];\n                    return [3 /*break*/, 4];\n                case 2: return [4 /*yield*/, env.fetcher(sourceValue, rowData)];\n                case 3:\n                    res = _a.sent();\n                    if (res.data) {\n                        remoteMappingCache[mapKey] = res.data;\n                        map = res.data;\n                    }\n                    _a.label = 4;\n                case 4: return [2 /*return*/, map];\n            }\n        });\n    });\n}\n/**\n * 导出 Excel\n * @param ExcelJS ExcelJS 对象\n * @param props Table 组件的 props\n * @param toolbar 导出 Excel 的 toolbar 配置\n * @param withoutData 如果为 true 就不导出数据，只导出表头\n */\nfunction exportExcel(ExcelJS, props, toolbar, withoutData) {\n    var _a, _b, _c, _d;\n    if (withoutData === void 0) { withoutData = false; }\n    return __awaiter(this, void 0, void 0, function () {\n        var store, env, cx, __, data, prefixRow, affixRow, columns, rows, tmpStore, filename, pageField, perPageField, ctx, res, _e, _f, key, workbook, worksheet, exportColumnNames, hasCustomExportColumns, columns_1, columns_1_1, column, filteredColumns, firstRowLabels, firstRow, remoteMappingCache, rowIndex, rows_1, rows_1_1, row, rowData, sheetRow, columIndex, _loop_1, filteredColumns_1, filteredColumns_1_1, column, e_5_1, e_6_1;\n        var _g, e_7, _h, e_8, _j, e_6, _k, e_5, _l;\n        return __generator(this, function (_m) {\n            switch (_m.label) {\n                case 0:\n                    store = props.store, env = props.env, cx = props.classnames, __ = props.translate, data = props.data, prefixRow = props.prefixRow, affixRow = props.affixRow;\n                    columns = store.exportColumns || [];\n                    rows = [];\n                    filename = 'data';\n                    if (!(typeof toolbar === 'object' && toolbar.api)) return [3 /*break*/, 2];\n                    pageField = toolbar.pageField || 'page';\n                    perPageField = toolbar.perPageField || 'perPage';\n                    ctx = createObject(data, __assign(__assign({}, props.query), (_g = {}, _g[pageField] = data.page || 1, _g[perPageField] = data.perPage || 10, _g)));\n                    return [4 /*yield*/, env.fetcher(toolbar.api, ctx, {\n                            autoAppend: true,\n                            pageField: pageField,\n                            perPageField: perPageField\n                        })];\n                case 1:\n                    res = _m.sent();\n                    if (!res.data) {\n                        env.notify('warning', __('placeholder.noData'));\n                        return [2 /*return*/];\n                    }\n                    /**\n                     * 优先找items和rows，找不到就拿第一个值为数组的字段\n                     * 和CRUD中的处理逻辑保持一致，避免能渲染和导出的不一致\n                     */\n                    if (Array.isArray(res.data)) {\n                        rows = res.data;\n                    }\n                    else if (Array.isArray((_a = res.data) === null || _a === void 0 ? void 0 : _a.rows)) {\n                        rows = res.data.rows;\n                    }\n                    else if (Array.isArray((_b = res.data) === null || _b === void 0 ? void 0 : _b.items)) {\n                        rows = res.data.items;\n                    }\n                    else {\n                        try {\n                            for (_e = __values(Object.keys(res.data)), _f = _e.next(); !_f.done; _f = _e.next()) {\n                                key = _f.value;\n                                if (res.data.hasOwnProperty(key) && Array.isArray(res.data[key])) {\n                                    rows = res.data[key];\n                                    break;\n                                }\n                            }\n                        }\n                        catch (e_7_1) { e_7 = { error: e_7_1 }; }\n                        finally {\n                            try {\n                                if (_f && !_f.done && (_h = _e.return)) _h.call(_e);\n                            }\n                            finally { if (e_7) throw e_7.error; }\n                        }\n                    }\n                    // 因为很多方法是 store 里的，所以需要构建 store 来处理\n                    tmpStore = TableStore.create(getSnapshot(store));\n                    tmpStore.initRows(rows);\n                    rows = tmpStore.rows;\n                    return [3 /*break*/, 3];\n                case 2:\n                    rows = store.rows;\n                    _m.label = 3;\n                case 3:\n                    if (typeof toolbar === 'object' && toolbar.filename) {\n                        filename = filter(toolbar.filename, data, '| raw');\n                    }\n                    if (rows.length === 0) {\n                        env.notify('warning', __('placeholder.noData'));\n                        return [2 /*return*/];\n                    }\n                    workbook = new ExcelJS.Workbook();\n                    worksheet = workbook.addWorksheet('sheet', {\n                        properties: { defaultColWidth: 15 }\n                    });\n                    worksheet.views = [{ state: 'frozen', xSplit: 0, ySplit: 1 }];\n                    exportColumnNames = toolbar.columns;\n                    if (isPureVariable(exportColumnNames)) {\n                        exportColumnNames = resolveVariableAndFilter(exportColumnNames, data, '| raw');\n                    }\n                    hasCustomExportColumns = toolbar.exportColumns && Array.isArray(toolbar.exportColumns);\n                    if (hasCustomExportColumns) {\n                        columns = toolbar.exportColumns;\n                        try {\n                            // 因为后面列 props 都是从 pristine 里获取，所以这里归一一下\n                            for (columns_1 = __values(columns), columns_1_1 = columns_1.next(); !columns_1_1.done; columns_1_1 = columns_1.next()) {\n                                column = columns_1_1.value;\n                                column.pristine = column;\n                            }\n                        }\n                        catch (e_8_1) { e_8 = { error: e_8_1 }; }\n                        finally {\n                            try {\n                                if (columns_1_1 && !columns_1_1.done && (_j = columns_1.return)) _j.call(columns_1);\n                            }\n                            finally { if (e_8) throw e_8.error; }\n                        }\n                    }\n                    filteredColumns = exportColumnNames\n                        ? columns.filter(function (column) {\n                            var filterColumnsNames = exportColumnNames;\n                            if (column.name && filterColumnsNames.indexOf(column.name) !== -1) {\n                                return hasCustomExportColumns ? true : (column === null || column === void 0 ? void 0 : column.type) !== 'operation';\n                            }\n                            return false;\n                        })\n                        : columns.filter(function (column) { return (column === null || column === void 0 ? void 0 : column.type) !== 'operation'; });\n                    firstRowLabels = filteredColumns.map(function (column) {\n                        return filter(column.label, data);\n                    });\n                    firstRow = worksheet.getRow(1);\n                    firstRow.values = firstRowLabels;\n                    worksheet.autoFilter = {\n                        from: {\n                            row: 1,\n                            column: 1\n                        },\n                        to: {\n                            row: 1,\n                            column: firstRowLabels.length\n                        }\n                    };\n                    if (withoutData) {\n                        return [2 /*return*/, exportExcelWithoutData(workbook, worksheet, filteredColumns, filename, env, data)];\n                    }\n                    remoteMappingCache = {};\n                    rowIndex = 1;\n                    if (toolbar.rowSlice) {\n                        rows = arraySlice(rows, toolbar.rowSlice);\n                    }\n                    // 前置总结行\n                    rowIndex = renderSummary(worksheet, data, prefixRow, rowIndex);\n                    // children 展开\n                    rows = flattenTree(rows, function (item) { return item; });\n                    _m.label = 4;\n                case 4:\n                    _m.trys.push([4, 15, 16, 17]);\n                    rows_1 = __values(rows), rows_1_1 = rows_1.next();\n                    _m.label = 5;\n                case 5:\n                    if (!!rows_1_1.done) return [3 /*break*/, 14];\n                    row = rows_1_1.value;\n                    rowData = createObject(data, row.data);\n                    rowIndex += 1;\n                    sheetRow = worksheet.getRow(rowIndex);\n                    columIndex = 0;\n                    _loop_1 = function (column) {\n                        var name_1, value, type, imageData, imageDimensions, imageWidth, imageHeight, imageMaxSize, imageMatch, imageExt, imageId, linkURL, e_9, href, linkURL, body, text, absoluteURL, map, valueField_1, labelField, viewValue, label, text, viewValue, _o, fromNow, _p, format, _q, valueFormat, ISODate, NormalDate, db, cellValue;\n                        return __generator(this, function (_r) {\n                            switch (_r.label) {\n                                case 0:\n                                    columIndex += 1;\n                                    name_1 = column.name;\n                                    value = getVariable(rowData, name_1);\n                                    if (typeof value === 'undefined' && !column.pristine.tpl) {\n                                        return [2 /*return*/, \"continue\"];\n                                    }\n                                    // 处理合并单元格\n                                    if (name_1 in row.rowSpans) {\n                                        if (row.rowSpans[name_1] === 0) {\n                                            return [2 /*return*/, \"continue\"];\n                                        }\n                                        else {\n                                            // start row, start column, end row, end column\n                                            worksheet.mergeCells(rowIndex, columIndex, rowIndex + row.rowSpans[name_1] - 1, columIndex);\n                                        }\n                                    }\n                                    applyCellStyle(sheetRow, columIndex, column.pristine, rowData);\n                                    type = column.type || 'plain';\n                                    if (!((type === 'image' || type === 'static-image') && value)) return [3 /*break*/, 6];\n                                    _r.label = 1;\n                                case 1:\n                                    _r.trys.push([1, 4, , 5]);\n                                    return [4 /*yield*/, toDataURL(value)];\n                                case 2:\n                                    imageData = _r.sent();\n                                    return [4 /*yield*/, getImageDimensions(imageData)];\n                                case 3:\n                                    imageDimensions = _r.sent();\n                                    imageWidth = imageDimensions.width;\n                                    imageHeight = imageDimensions.height;\n                                    imageMaxSize = 100;\n                                    if (imageWidth > imageHeight) {\n                                        if (imageWidth > imageMaxSize) {\n                                            imageHeight = (imageMaxSize * imageHeight) / imageWidth;\n                                            imageWidth = imageMaxSize;\n                                        }\n                                    }\n                                    else {\n                                        if (imageHeight > imageMaxSize) {\n                                            imageWidth = (imageMaxSize * imageWidth) / imageHeight;\n                                            imageHeight = imageMaxSize;\n                                        }\n                                    }\n                                    imageMatch = imageData.match(/data:image\\/(.*);/);\n                                    imageExt = 'png';\n                                    if (imageMatch) {\n                                        imageExt = imageMatch[1];\n                                    }\n                                    // 目前 excel 只支持这些格式，所以其它格式直接输出 url\n                                    if (imageExt != 'png' && imageExt != 'jpeg' && imageExt != 'gif') {\n                                        sheetRow.getCell(columIndex).value = value;\n                                        return [2 /*return*/, \"continue\"];\n                                    }\n                                    imageId = workbook.addImage({\n                                        base64: imageData,\n                                        extension: imageExt\n                                    });\n                                    linkURL = getAbsoluteUrl(value);\n                                    worksheet.addImage(imageId, {\n                                        // 这里坐标位置是从 0 开始的，所以要减一\n                                        tl: { col: columIndex - 1, row: rowIndex - 1 },\n                                        ext: {\n                                            width: imageWidth,\n                                            height: imageHeight\n                                        },\n                                        hyperlinks: {\n                                            tooltip: linkURL\n                                        }\n                                    });\n                                    return [3 /*break*/, 5];\n                                case 4:\n                                    e_9 = _r.sent();\n                                    console.warn(e_9);\n                                    return [3 /*break*/, 5];\n                                case 5: return [3 /*break*/, 13];\n                                case 6:\n                                    if (!(type == 'link' || type === 'static-link')) return [3 /*break*/, 7];\n                                    href = column.pristine.href;\n                                    linkURL = (typeof href === 'string' && href\n                                        ? filter(href, rowData, '| raw')\n                                        : undefined) || value;\n                                    body = column.pristine.body;\n                                    text = typeof body === 'string' && body\n                                        ? filter(body, rowData, '| raw')\n                                        : undefined;\n                                    absoluteURL = getAbsoluteUrl(linkURL);\n                                    sheetRow.getCell(columIndex).value = {\n                                        text: text || absoluteURL,\n                                        hyperlink: absoluteURL\n                                    };\n                                    return [3 /*break*/, 13];\n                                case 7:\n                                    if (!(type === 'mapping' || type === 'static-mapping')) return [3 /*break*/, 9];\n                                    return [4 /*yield*/, getMap(remoteMappingCache, env, column, data, rowData)];\n                                case 8:\n                                    map = _r.sent();\n                                    valueField_1 = column.pristine.valueField || 'value';\n                                    labelField = column.pristine.labelField || 'label';\n                                    if (Array.isArray(map)) {\n                                        map = map.reduce(function (res, now) {\n                                            if (now == null) {\n                                                return res;\n                                            }\n                                            else if (isObject(now)) {\n                                                var keys = Object.keys(now);\n                                                if (keys.length === 1 ||\n                                                    (keys.length == 2 && keys.includes('$$id'))) {\n                                                    // 针对amis-editor的特殊处理\n                                                    keys = keys.filter(function (key) { return key !== '$$id'; });\n                                                    // 单key 数组对象\n                                                    res[keys[0]] = now[keys[0]];\n                                                }\n                                                else if (keys.length > 1) {\n                                                    // 多key 数组对象\n                                                    res[now[valueField_1]] = now;\n                                                }\n                                            }\n                                            return res;\n                                        }, {});\n                                    }\n                                    if (typeof value !== 'undefined' && map && ((_c = map[value]) !== null && _c !== void 0 ? _c : map['*'])) {\n                                        viewValue = (_d = map[value]) !== null && _d !== void 0 ? _d : (value === true && map['1']\n                                            ? map['1']\n                                            : value === false && map['0']\n                                                ? map['0']\n                                                : map['*']);\n                                        label = viewValue;\n                                        if (isObject(viewValue)) {\n                                            if (labelField === undefined || labelField === '') {\n                                                if (!viewValue.hasOwnProperty('type')) {\n                                                    // 映射值是object\n                                                    // 没配置labelField\n                                                    // object 也没有 type，不能作为schema渲染\n                                                    // 默认取 label 字段\n                                                    label = viewValue['label'];\n                                                }\n                                            }\n                                            else {\n                                                label = viewValue[labelField || 'label'];\n                                            }\n                                        }\n                                        text = removeHTMLTag(label);\n                                        /** map可能会使用比较复杂的html结构，富文本也无法完全支持，直接把里面的变量解析出来即可 */\n                                        if (isPureVariable(text)) {\n                                            text = resolveVariableAndFilter(text, rowData, '| raw');\n                                        }\n                                        else {\n                                            text = filter(text, rowData);\n                                        }\n                                        sheetRow.getCell(columIndex).value = text;\n                                    }\n                                    else {\n                                        sheetRow.getCell(columIndex).value = removeHTMLTag(value);\n                                    }\n                                    return [3 /*break*/, 13];\n                                case 9:\n                                    if (!(type === 'date' || type === 'static-date')) return [3 /*break*/, 10];\n                                    viewValue = void 0;\n                                    _o = column.pristine, fromNow = _o.fromNow, _p = _o.format, format = _p === void 0 ? 'YYYY-MM-DD' : _p, _q = _o.valueFormat, valueFormat = _q === void 0 ? 'X' : _q;\n                                    if (value) {\n                                        ISODate = moment(value, moment.ISO_8601);\n                                        NormalDate = moment(value, valueFormat);\n                                        viewValue = ISODate.isValid()\n                                            ? ISODate.format(format)\n                                            : NormalDate.isValid()\n                                                ? NormalDate.format(format)\n                                                : false;\n                                    }\n                                    if (fromNow) {\n                                        viewValue = moment(value).fromNow();\n                                    }\n                                    if (viewValue) {\n                                        sheetRow.getCell(columIndex).value = viewValue;\n                                    }\n                                    return [3 /*break*/, 13];\n                                case 10:\n                                    if (!(type === 'input-city')) return [3 /*break*/, 12];\n                                    return [4 /*yield*/, loadDb()];\n                                case 11:\n                                    db = _r.sent();\n                                    if (db.default && value && value in db.default) {\n                                        sheetRow.getCell(columIndex).value = db.default[value];\n                                    }\n                                    return [3 /*break*/, 13];\n                                case 12:\n                                    if (column.pristine.tpl) {\n                                        sheetRow.getCell(columIndex).value = removeHTMLTag(decodeEntity(filter(column.pristine.tpl, rowData)));\n                                    }\n                                    else {\n                                        sheetRow.getCell(columIndex).value = value;\n                                    }\n                                    _r.label = 13;\n                                case 13:\n                                    cellValue = sheetRow.getCell(columIndex).value;\n                                    if (Number.isInteger(cellValue)) {\n                                        sheetRow.getCell(columIndex).numFmt = '0';\n                                    }\n                                    return [2 /*return*/];\n                            }\n                        });\n                    };\n                    _m.label = 6;\n                case 6:\n                    _m.trys.push([6, 11, 12, 13]);\n                    filteredColumns_1 = (e_5 = void 0, __values(filteredColumns)), filteredColumns_1_1 = filteredColumns_1.next();\n                    _m.label = 7;\n                case 7:\n                    if (!!filteredColumns_1_1.done) return [3 /*break*/, 10];\n                    column = filteredColumns_1_1.value;\n                    return [5 /*yield**/, _loop_1(column)];\n                case 8:\n                    _m.sent();\n                    _m.label = 9;\n                case 9:\n                    filteredColumns_1_1 = filteredColumns_1.next();\n                    return [3 /*break*/, 7];\n                case 10: return [3 /*break*/, 13];\n                case 11:\n                    e_5_1 = _m.sent();\n                    e_5 = { error: e_5_1 };\n                    return [3 /*break*/, 13];\n                case 12:\n                    try {\n                        if (filteredColumns_1_1 && !filteredColumns_1_1.done && (_l = filteredColumns_1.return)) _l.call(filteredColumns_1);\n                    }\n                    finally { if (e_5) throw e_5.error; }\n                    return [7 /*endfinally*/];\n                case 13:\n                    rows_1_1 = rows_1.next();\n                    return [3 /*break*/, 5];\n                case 14: return [3 /*break*/, 17];\n                case 15:\n                    e_6_1 = _m.sent();\n                    e_6 = { error: e_6_1 };\n                    return [3 /*break*/, 17];\n                case 16:\n                    try {\n                        if (rows_1_1 && !rows_1_1.done && (_k = rows_1.return)) _k.call(rows_1);\n                    }\n                    finally { if (e_6) throw e_6.error; }\n                    return [7 /*endfinally*/];\n                case 17:\n                    // 后置总结行\n                    renderSummary(worksheet, data, affixRow, rowIndex);\n                    downloadFile(workbook, filename);\n                    return [2 /*return*/];\n            }\n        });\n    });\n}\nfunction downloadFile(workbook, filename) {\n    return __awaiter(this, void 0, void 0, function () {\n        var buffer, blob;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0: return [4 /*yield*/, workbook.xlsx.writeBuffer()];\n                case 1:\n                    buffer = _a.sent();\n                    if (buffer) {\n                        blob = new Blob([buffer], {\n                            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n                        });\n                        saveAs(blob, filename + '.xlsx');\n                    }\n                    return [2 /*return*/];\n            }\n        });\n    });\n}\nfunction numberToLetters(num) {\n    var letters = '';\n    while (num >= 0) {\n        letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[num % 26] + letters;\n        num = Math.floor(num / 26) - 1;\n    }\n    return letters;\n}\n/**\n * 只导出表头\n */\nfunction exportExcelWithoutData(workbook, worksheet, filteredColumns, filename, env, data) {\n    var _a;\n    return __awaiter(this, void 0, void 0, function () {\n        var index, rowNumber, mapCache, filteredColumns_2, filteredColumns_2_1, column, map, keys, rowIndex, e_10_1;\n        var e_10, _b;\n        return __generator(this, function (_c) {\n            switch (_c.label) {\n                case 0:\n                    index = 0;\n                    rowNumber = 100;\n                    mapCache = {};\n                    _c.label = 1;\n                case 1:\n                    _c.trys.push([1, 6, 7, 8]);\n                    filteredColumns_2 = __values(filteredColumns), filteredColumns_2_1 = filteredColumns_2.next();\n                    _c.label = 2;\n                case 2:\n                    if (!!filteredColumns_2_1.done) return [3 /*break*/, 5];\n                    column = filteredColumns_2_1.value;\n                    index += 1;\n                    if (!(((_a = column.pristine) === null || _a === void 0 ? void 0 : _a.type) === 'mapping')) return [3 /*break*/, 4];\n                    return [4 /*yield*/, getMap(mapCache, env, column, data, {})];\n                case 3:\n                    map = _c.sent();\n                    if (map && isObject(map)) {\n                        keys = Object.keys(map);\n                        for (rowIndex = 1; rowIndex < rowNumber; rowIndex++) {\n                            worksheet.getCell(numberToLetters(index) + rowIndex).dataValidation =\n                                {\n                                    type: 'list',\n                                    allowBlank: true,\n                                    formulae: [\"\\\"\".concat(keys.join(','), \"\\\"\")]\n                                };\n                        }\n                    }\n                    _c.label = 4;\n                case 4:\n                    filteredColumns_2_1 = filteredColumns_2.next();\n                    return [3 /*break*/, 2];\n                case 5: return [3 /*break*/, 8];\n                case 6:\n                    e_10_1 = _c.sent();\n                    e_10 = { error: e_10_1 };\n                    return [3 /*break*/, 8];\n                case 7:\n                    try {\n                        if (filteredColumns_2_1 && !filteredColumns_2_1.done && (_b = filteredColumns_2.return)) _b.call(filteredColumns_2);\n                    }\n                    finally { if (e_10) throw e_10.error; }\n                    return [7 /*endfinally*/];\n                case 8:\n                    downloadFile(workbook, filename);\n                    return [2 /*return*/];\n            }\n        });\n    });\n}\n\nexport { exportExcel };\n", "/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __assign, __rest } from 'tslib';\nimport { padArr, createObject } from 'amis-core';\nimport { Icon } from 'amis-ui';\nimport { observer } from 'mobx-react';\nimport React from 'react';\n\nfunction AutoFilterForm(_a) {\n    var autoGenerateFilter = _a.autoGenerateFilter, searchFormExpanded = _a.searchFormExpanded, activedSearchableColumns = _a.activedSearchableColumns, searchableColumns = _a.searchableColumns, onItemToggleExpanded = _a.onItemToggleExpanded, onToggleExpanded = _a.onToggleExpanded, cx = _a.classnames, __ = _a.translate, render = _a.render, data = _a.data, onSearchableFromReset = _a.onSearchableFromReset, onSearchableFromSubmit = _a.onSearchableFromSubmit, onSearchableFromInit = _a.onSearchableFromInit, popOverContainer = _a.popOverContainer, testIdBuilder = _a.testIdBuilder, canAccessSuperData = _a.canAccessSuperData;\n    var schema = React.useMemo(function () {\n        var _a = typeof autoGenerateFilter === 'boolean'\n            ? {\n                columnsNum: 3,\n                showBtnToolbar: true\n            }\n            : autoGenerateFilter, columnsNum = _a.columnsNum, showBtnToolbar = _a.showBtnToolbar;\n        var body = padArr(activedSearchableColumns, columnsNum).map(function (group) { return ({\n            type: 'group',\n            body: group.map(function (column) {\n                var _a, _b, _c, _d;\n                return (__assign(__assign({}, (column.searchable === true\n                    ? {\n                        type: 'input-text',\n                        name: column.name,\n                        label: column.label,\n                        testIdBuilder: testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild(column.name)\n                    }\n                    : __assign({ type: 'input-text', name: column.name, testIdBuilder: testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild(column.name) }, column.searchable))), { name: (_b = (_a = column.searchable) === null || _a === void 0 ? void 0 : _a.name) !== null && _b !== void 0 ? _b : column.name, label: (_d = (_c = column.searchable) === null || _c === void 0 ? void 0 : _c.label) !== null && _d !== void 0 ? _d : column.label }));\n            })\n        }); });\n        var showExpander = activedSearchableColumns.length >= columnsNum;\n        // todo 以后做动画\n        if (!searchFormExpanded && body.length) {\n            body.splice(1, body.length - 1);\n            body[0].body.splice(columnsNum - 1, body[0].body.length - columnsNum + 1);\n        }\n        var lastGroup = body[body.length - 1];\n        if (!Array.isArray(lastGroup === null || lastGroup === void 0 ? void 0 : lastGroup.body) ||\n            lastGroup.body.length >= columnsNum) {\n            lastGroup = {\n                type: 'group',\n                body: []\n            };\n            body.push(lastGroup);\n        }\n        var count = Math.max(columnsNum - lastGroup.body.length - 1);\n        while (count-- > 0) {\n            lastGroup.body.push({\n                type: 'tpl',\n                tpl: ''\n            });\n        }\n        var moreTestIdBuilder = testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild('more');\n        lastGroup.body.push({\n            type: 'container',\n            className: 'AutoFilterToolbar',\n            wrapperBody: false,\n            body: [\n                {\n                    type: 'dropdown-button',\n                    label: __('Table.searchFields'),\n                    className: cx('Table-searchableForm-dropdown', 'mr-2'),\n                    level: 'link',\n                    trigger: 'click',\n                    size: 'sm',\n                    align: 'right',\n                    visible: showBtnToolbar,\n                    testIdBuilder: moreTestIdBuilder,\n                    buttons: searchableColumns.map(function (column) {\n                        return {\n                            children: function (_a) {\n                                var _b, _c;\n                                var render = _a.render;\n                                return render(\"column-search-toggler-\".concat(column.id), {\n                                    type: 'checkbox',\n                                    label: false,\n                                    className: cx('Table-searchableForm-checkbox'),\n                                    inputClassName: cx('Table-searchableForm-checkbox-inner'),\n                                    name: \"__whatever_name\",\n                                    option: (_c = (_b = column.searchable) === null || _b === void 0 ? void 0 : _b.label) !== null && _c !== void 0 ? _c : column.label,\n                                    testIdBuilder: moreTestIdBuilder === null || moreTestIdBuilder === void 0 ? void 0 : moreTestIdBuilder.getChild(column.name + ''),\n                                    badge: {\n                                        offset: [-10, 5],\n                                        visibleOn: \"\".concat(column.toggable &&\n                                            !column.toggled &&\n                                            column.enableSearch)\n                                    }\n                                }, {\n                                    value: activedSearchableColumns.includes(column),\n                                    onChange: function (value) {\n                                        return onItemToggleExpanded === null || onItemToggleExpanded === void 0 ? void 0 : onItemToggleExpanded(column, value);\n                                    }\n                                });\n                            }\n                        };\n                    })\n                },\n                {\n                    type: 'submit',\n                    label: __('search'),\n                    size: 'sm',\n                    level: 'primary',\n                    className: 'w-18 mr-2'\n                },\n                {\n                    type: 'reset',\n                    label: __('reset'),\n                    size: 'sm',\n                    className: 'w-18',\n                    actionType: 'clear-and-submit'\n                },\n                {\n                    children: function () {\n                        return showExpander ? (React.createElement(\"a\", { className: cx('Table-SFToggler', searchFormExpanded ? 'is-expanded' : ''), onClick: onToggleExpanded },\n                            __(searchFormExpanded ? 'collapse' : 'expand'),\n                            React.createElement(\"span\", { className: cx('Table-SFToggler-arrow') },\n                                React.createElement(Icon, { icon: \"right-arrow-bold\", className: \"icon\" })))) : null;\n                    }\n                }\n            ]\n        });\n        return {\n            type: 'form',\n            api: null,\n            title: '',\n            mode: 'horizontal',\n            submitText: __('search'),\n            body: body,\n            actions: [],\n            canAccessSuperData: canAccessSuperData\n        };\n    }, [\n        autoGenerateFilter,\n        activedSearchableColumns,\n        searchableColumns,\n        searchFormExpanded,\n        canAccessSuperData,\n        __ // 保证语言更新后能重新渲染\n    ]);\n    return render('searchable-form', schema, {\n        key: 'searchable-form',\n        panelClassName: cx('Table-searchableForm'),\n        actionsClassName: cx('Table-searchableForm-footer'),\n        onReset: onSearchableFromReset,\n        onSubmit: onSearchableFromSubmit,\n        onInit: onSearchableFromInit,\n        formStore: undefined,\n        data: data,\n        popOverContainer: popOverContainer\n    });\n}\nvar AutoFilterForm$1 = observer(function (_a) {\n    var store = _a.store, query = _a.query, data = _a.data, rest = __rest(_a, [\"store\", \"query\", \"data\"]);\n    var onItemToggleExpanded = React.useCallback(function (column, value) {\n        column.setEnableSearch(value);\n        value && store.setSearchFormExpanded(true);\n    }, []);\n    var onToggleExpanded = React.useCallback(function () {\n        store.toggleSearchFormExpanded();\n    }, []);\n    var ctx = React.useMemo(function () { return (query ? createObject(data, query) : data); }, [query, data]);\n    return (React.createElement(AutoFilterForm, __assign({}, rest, { activedSearchableColumns: store.activedSearchableColumns, searchableColumns: store.searchableColumns, searchFormExpanded: store.searchFormExpanded, onItemToggleExpanded: onItemToggleExpanded, onToggleExpanded: onToggleExpanded, data: ctx })));\n});\n\nexport { AutoFilterForm, AutoFilterForm$1 as default };\n", "/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __read, __assign } from 'tslib';\nimport { buildTrackExpression, evalTrackExpression } from 'amis-core';\nimport { Spinner, Icon, Checkbox } from 'amis-ui';\nimport React from 'react';\n\nfunction Cell(_a) {\n    var _b;\n    var region = _a.region, column = _a.column, item = _a.item, props = _a.props, ignoreDrag = _a.ignoreDrag, render = _a.render, filterItemIndex = _a.filterItemIndex, store = _a.store, multiple = _a.multiple, itemBadge = _a.itemBadge, cx = _a.classnames, ns = _a.classPrefix, canAccessSuperData = _a.canAccessSuperData, onCheck = _a.onCheck, onDragStart = _a.onDragStart, popOverContainer = _a.popOverContainer, quickEditFormRef = _a.quickEditFormRef, onImageEnlarge = _a.onImageEnlarge, __ = _a.translate, testIdBuilder = _a.testIdBuilder;\n    if (column.name && item.rowSpans[column.name] === 0) {\n        return null;\n    }\n    var _c = __read(React.useMemo(function () {\n        var style = __assign({}, column.pristine.style);\n        var _a = __read(store.getStickyStyles(column, store.filteredColumns), 2), stickyStyle = _a[0], stickyClassName = _a[1];\n        return [Object.assign(style, stickyStyle), stickyClassName];\n    }, []), 2), style = _c[0], stickyClassName = _c[1];\n    var onCheckboxChange = React.useCallback(function () {\n        onCheck === null || onCheck === void 0 ? void 0 : onCheck(item);\n    }, []);\n    var _d = __read(React.useMemo(function () {\n        var prefix = [];\n        var affix = [];\n        var addtionalClassName = '';\n        if (column.isPrimary && store.isNested) {\n            addtionalClassName = 'Table-primayCell';\n            prefix.push(React.createElement(\"span\", { key: \"indent\", className: cx('Table-indent'), style: item.indentStyle }));\n            prefix.push(item.loading ? (React.createElement(Spinner, { key: \"loading\", size: \"sm\", show: true })) : item.error ? (React.createElement(\"a\", __assign({ className: cx('Table-retryBtn'), key: \"retryBtn\", onClick: item.resetDefered, \"data-tooltip\": __('Options.retry', { reason: item.error }) }, testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild('retry').getTestId()),\n                React.createElement(Icon, { icon: \"retry\", className: \"icon\" }))) : item.expandable ? (React.createElement(\"a\", __assign({ key: \"expandBtn2\", className: cx('Table-expandBtn2', item.expanded ? 'is-active' : ''), \n                // data-tooltip=\"展开/收起\"\n                // data-position=\"top\"\n                onClick: item.toggleExpanded }, testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild(item.expanded ? 'fold' : 'expand').getTestId()),\n                React.createElement(Icon, { icon: \"right-arrow-bold\", className: \"icon\" }))) : (React.createElement(\"span\", { key: \"expandSpace\", className: cx('Table-expandSpace') })));\n        }\n        if (!ignoreDrag &&\n            column.isPrimary &&\n            store.isNested &&\n            store.draggable &&\n            item.draggable) {\n            affix.push(React.createElement(\"a\", __assign({ key: \"dragBtn\", draggable: true, onDragStart: onDragStart, className: cx('Table-dragBtn') }, testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild('drag').getTestId()),\n                React.createElement(Icon, { icon: \"drag\", className: \"icon\" })));\n        }\n        return [prefix, affix, addtionalClassName];\n    }, [\n        item.expandable,\n        item.expanded,\n        item.error,\n        item.loading,\n        column.isPrimary,\n        store.isNested\n    ]), 3), prefix = _d[0], affix = _d[1], addtionalClassName = _d[2];\n    // 根据条件缓存 data，避免孩子重复渲染\n    var hasCustomTrackExpression = typeof column.pristine.trackExpression !== 'undefined';\n    var trackExpression = hasCustomTrackExpression\n        ? column.pristine.trackExpression\n        : React.useMemo(function () { return buildTrackExpression(column.pristine); }, []);\n    var data = React.useMemo(function () { return item.locals; }, [\n        hasCustomTrackExpression ? '' : JSON.stringify(item.locals),\n        evalTrackExpression(trackExpression, item.locals)\n    ]);\n    var finalCanAccessSuperData = (_b = column.pristine.canAccessSuperData) !== null && _b !== void 0 ? _b : canAccessSuperData;\n    var subProps = __assign(__assign({}, props), { \n        // 操作列不下发loading，否则会导致操作栏里面的所有按钮都出现loading\n        loading: column.type === 'operation' ? false : props.loading, btnDisabled: store.dragging, data: data, \n        // 不要下发 value，组件基本上都会自己取\n        // 如果下发了表单项会认为是 controlled value\n        // 就不会去跑 extraName 之类的逻辑了\n        // value: column.name\n        //   ? resolveVariable(\n        //       column.name,\n        //       finalCanAccessSuperData ? item.locals : item.data\n        //     )\n        //   : column.value,\n        popOverContainer: popOverContainer, rowSpan: item.rowSpans[column.name], quickEditFormRef: quickEditFormRef, cellPrefix: prefix, cellAffix: affix, onImageEnlarge: onImageEnlarge, canAccessSuperData: finalCanAccessSuperData, row: item, itemBadge: itemBadge, showBadge: !props.isHead &&\n            itemBadge &&\n            store.firstToggledColumnIndex === props.colIndex, onQuery: undefined, style: style, className: cx(column.pristine.className, stickyClassName, addtionalClassName), testIdBuilder: testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild(column.name || column.value) });\n    delete subProps.label;\n    if (column.type === '__checkme') {\n        return (React.createElement(\"td\", __assign({ style: style, className: cx(column.pristine.className, stickyClassName) }, testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getTestId()),\n            React.createElement(Checkbox, { classPrefix: ns, type: multiple ? 'checkbox' : 'radio', partial: multiple ? item.partial : false, checked: item.checked || (multiple ? item.partial : false), disabled: item.checkdisable || !item.checkable, onChange: onCheckboxChange, testIdBuilder: testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild('chekbx') })));\n    }\n    else if (column.type === '__dragme') {\n        return (React.createElement(\"td\", __assign({ style: style, className: cx(column.pristine.className, stickyClassName, {\n                'is-dragDisabled': !item.draggable\n            }) }, testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild('drag').getTestId()), item.draggable ? React.createElement(Icon, { icon: \"drag\", className: \"icon\" }) : null));\n    }\n    else if (column.type === '__expandme') {\n        return (React.createElement(\"td\", { style: style, className: cx(column.pristine.className, stickyClassName) }, item.expandable ? (React.createElement(\"a\", __assign({ className: cx('Table-expandBtn', item.expanded ? 'is-active' : ''), \n            // data-tooltip=\"展开/收起\"\n            // data-position=\"top\"\n            onClick: item.toggleExpanded }, testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild(item.expanded ? 'fold' : 'expand').getTestId()),\n            React.createElement(Icon, { icon: \"right-arrow-bold\", className: \"icon\" }))) : null));\n    }\n    else if (column.type === '__index') {\n        return (React.createElement(\"td\", { style: style, className: cx(column.pristine.className, stickyClassName) }, \"\".concat(filterItemIndex ? filterItemIndex(item.path, item) : item.path)\n            .split('.')\n            .map(function (a) { return parseInt(a, 10) + 1; })\n            .join('.')));\n    }\n    return render(region, __assign(__assign({}, column.pristine), { \n        // 因为列本身已经做过显隐判断了，单元格不应该再处理\n        visibleOn: '', hiddenOn: '', visible: true, hidden: false, column: column.pristine, type: 'cell' }), subProps);\n}\n\nexport { Cell as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAAA,iBAAkB;AAIlB,qBAAoB;AACpB,kBAAiB;AACjB,sBAAqB;AACrB,0BAAyB;AACzB,2BAA0B;;;ACR1B,IAAAC,iBAAkB;;;ACAlB,mBAAkB;AAClB,uBAA4B;AAE5B,qCAAgC;AAChC,qBAAoB;AACpB,kBAAiB;AAMjB,IAAI,SAAS;AACb,IAAI;AACJ,IAAI,eAAe,SAAU,QAAQ;AACjC,MAAI,WAAW,QAAQ;AAAE,aAAS,CAAC;AAAA,EAAG;AACtC,SAAO,SAAUC,YAAW;AACxB,QAAI;AAAA;AAAA,MAAoC,SAAU,QAAQ;AACtD,kBAAUC,qBAAoB,MAAM;AACpC,iBAASA,oBAAmB,OAAO;AAC/B,cAAI,QAAQ,OAAO,KAAK,MAAM,KAAK,KAAK;AACxC,gBAAM,gBAAgB,MAAM,cAAc,KAAK,KAAK;AACpD,gBAAM,iBAAiB,MAAM,eAAe,KAAK,KAAK;AACtD,gBAAM,eAAe,MAAM,aAAa,KAAK,KAAK;AAClD,gBAAM,eAAe,MAAM,aAAa,KAAK,KAAK;AAClD,gBAAM,cAAc,MAAM,YAAY,KAAK,KAAK;AAChD,gBAAM,aAAa,MAAM,WAAW,KAAK,KAAK;AAC9C,gBAAM,uBAAuB,MAAM,qBAAqB,KAAK,KAAK;AAClE,gBAAM,sBAAsB,MAAM,oBAAoB,KAAK,KAAK;AAChE,gBAAM,UAAU,MAAM,QAAQ,KAAK,KAAK;AACxC,gBAAM,cAAc,MAAM,YAAY,KAAK,KAAK;AAChD,gBAAM,aAAa,MAAM,WAAW,KAAK,KAAK;AAC9C,gBAAM,eAAe,MAAM,aAAa,KAAK,KAAK;AAClD,gBAAM,uBAAuB,MAAM,qBAAqB,KAAK,KAAK;AAClE,gBAAM,mBAAmB,MAAM,iBAAiB,KAAK,KAAK;AAC1D,gBAAM,QAAQ;AAAA,YACV,UAAU;AAAA,UACd;AACA,iBAAO;AAAA,QACX;AACA,QAAAA,oBAAmB,UAAU,oBAAoB,WAAY;AACzD,eAAK,aAAS,8BAAY,IAAI;AAC9B,cAAI,QAAQ;AACR;AAAA,UACJ;AACA,mBAAS;AACT,mBAAS,KAAK,iBAAiB,YAAY,KAAK,oBAAoB;AACpE,mBAAS,KAAK,iBAAiB,WAAW,KAAK,mBAAmB;AAAA,QACtE;AACA,QAAAA,oBAAmB,UAAU,UAAU,SAAU,KAAK;AAClD,cAAI,KAAK,KAAK,OAAO,mBAAmB,GAAG,kBAAkB,WAAW,GAAG,UAAU,WAAW,GAAG;AACnG,iBAAO,OAAO,IAAI,oBAAoB;AAClC,kBAAM,IAAI,mBAAmB;AAAA,UACjC;AACA,eAAK,OAAO;AACZ,+BAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,KAAK,UAAU,QAAQ;AAAA,QAChH;AACA,QAAAA,oBAAmB,UAAU,cAAc,SAAU,KAAK;AACtD,cAAI,KAAK,KAAK,OAAO,uBAAuB,GAAG,sBAAsB,WAAW,GAAG,UAAU,WAAW,GAAG;AAC3G,iBAAO,OAAO,IAAI,oBAAoB;AAClC,kBAAM,IAAI,mBAAmB;AAAA,UACjC;AACA,eAAK,WAAW;AAChB,mCAAyB,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,KAAK,UAAU,QAAQ;AAAA,QAC5H;AACA,QAAAA,oBAAmB,UAAU,uBAAuB,SAAU,GAAG;AAC7D,cAAI,KAAK,KAAK,MAAM;AACpB,cAAI,KAAK,EAAE,OAAO,QAAQ,IAAI,OAAO,IAAI,sBAAsB,CAAC;AAChE,cAAI,CAAC,IAAI;AACL;AAAA,UACJ;AACA,cAAI,QAAQ,GAAG,QAAQ,OAAO;AAC9B,cAAI,CAAC,OAAO;AACR;AAAA,UACJ;AACA,kBAAI,eAAAC,SAAQ,CAAC,MAAM,WACf,CAAC,CAAC,CAAC,SAAS,UAAU,EAAE,QAAQ,GAAG,OAAO,GAAG;AAC7C,cAAE,eAAe;AACjB,cAAE,gBAAgB;AAAA,UACtB;AAAA,QACJ;AACA,QAAAD,oBAAmB,UAAU,sBAAsB,SAAU,GAAG;AAC5D,cAAI,WAAO,eAAAC,SAAQ,CAAC;AACpB,cAAI,SAAS,SAAS,eAAe;AACjC,0BAAc,eAAe;AAAA,UACjC,WACS,CAAC,CAAC,SAAS,UAAU,EAAE,QAAQ,EAAE,OAAO,OAAO,KACpD,EAAE,OAAO,oBAAoB,UAC7B,CAAC,CAAC,CAAC,MAAM,QAAQ,QAAQ,OAAO,EAAE,QAAQ,IAAI,GAAG;AACjD;AAAA,UACJ;AACA,YAAE,eAAe;AACjB,cAAI,KAAK,KAAK,MAAM;AACpB,cAAI,KAAK,EAAE,OAAO,QAAQ,IAAI,OAAO,IAAI,sBAAsB,CAAC,KAC5D,SAAS,cAAc,IAAI,OAAO,IAAI,sBAAsB,CAAC;AACjE,cAAI,CAAC,IAAI;AACL;AAAA,UACJ;AACA,cAAI,QAAQ,GAAG,QAAQ,OAAO;AAC9B,cAAI,CAAC,OAAO;AACR;AAAA,UACJ;AACA,cAAI,UAAU,MAAM,cAAc,IAAI,OAAO,IAAI,4BAA4B,CAAC;AAC9E,cAAI,CAAC,SAAS;AACV,gBAAI,MAAM,MAAM,cAAc,IAAI,OAAO,IAAI,gCAAgC,CAAC;AAC9E,mBAAO,IAAI,MAAM;AAAA,UACrB,OACK;AACD,gBAAI,SAAS,QAAQ,SAAS,QAAQ,SAAS,QAAQ,SAAS;AAChE,oBAAQ,MAAM;AAAA,cACV,KAAK;AACD,yBAAS,QAAQ,WACZ;AACL,oBAAI,QAAQ;AACR,sBAAI,QAAQ,QAAQ;AACpB,yBAAO,SAAS,KAAK,EAAE,MAAM;AAAA,gBACjC;AACA;AAAA,cACJ,KAAK;AACD,yBAAS,QAAQ,WACZ;AACL,oBAAI,QAAQ;AACR,sBAAI,QAAQ,QAAQ;AACpB,yBAAO,SAAS,KAAK,EAAE,MAAM;AAAA,gBACjC;AACA;AAAA,cACJ,KAAK;AACD,yBAAS,QAAQ;AACjB,uBAAO,QAAQ;AACX,sBAAI,OAAO,QAAQ,IAAI,OAAO,IAAI,gCAAgC,CAAC,GAAG;AAClE;AAAA,kBACJ;AACA,2BAAS,OAAO;AAAA,gBACpB;AACA,oBAAI,QAAQ;AACR,yBAAO,MAAM;AAAA,gBACjB,WACS,QAAQ,WAAW,iBAAiB;AACzC,sBAAI,MAAM,QAAQ,WACb,gBAAgB,iBAAiB,IAAI,OAAO,IAAI,gCAAgC,CAAC;AACtF,sBAAI,IAAI,QAAQ;AACZ,wBAAI,IAAI,SAAS,CAAC,EAAE,MAAM;AAAA,kBAC9B;AAAA,gBACJ;AACA;AAAA,cACJ,KAAK;AACD,yBAAS,QAAQ;AACjB,uBAAO,QAAQ;AACX,sBAAI,OAAO,QAAQ,IAAI,OAAO,IAAI,gCAAgC,CAAC,GAAG;AAClE;AAAA,kBACJ;AACA,2BAAS,OAAO;AAAA,gBACpB;AACA,oBAAI,QAAQ;AACR,yBAAO,MAAM;AAAA,gBACjB,WACS,QAAQ,WAAW,aAAa;AACrC,2BAAS,QAAQ,WAAW,YAAY,cAAc,IAAI,OAAO,IAAI,gCAAgC,CAAC;AACtG,sBAAI,QAAQ;AACR,2BAAO,MAAM;AAAA,kBACjB;AAAA,gBACJ;AACA;AAAA,YACR;AAAA,UACJ;AAAA,QACJ;AAIA,QAAAD,oBAAmB,UAAU,aAAa,SAAU,KAAK;AACrD,eAAK,UAAU;AAAA,QACnB;AACA,QAAAA,oBAAmB,UAAU,eAAe,SAAU,GAAG,QAAQ,KAAK;AAClE,cAAI,WAAW,KAAK,MAAM;AAC1B,cAAI,OAAO,eAAe,YAAY,OAAO,eAAe,SAAS;AACjE,iBAAK,eAAe;AACpB;AAAA,UACJ;AACA,sBAAY,SAAS,GAAG,QAAQ,GAAG;AAAA,QACvC;AACA,QAAAA,oBAAmB,UAAU,eAAe,SAAU,QAAQ;AAC1D,cAAI,KAAK,KAAK,OAAO,gBAAgB,GAAG,eAAe,YAAY,GAAG;AACtE,eAAK,eAAe;AACpB,wBAAc,QAAQ,UAAU,iBAAiB,OAAO,SAAS;AACjE,iBAAO;AAAA,QACX;AACA,QAAAA,oBAAmB,UAAU,aAAa,SAAU,QAAQ;AACxD,cAAI,KAAK,KAAK,OAAO,gBAAgB,GAAG,eAAe,OAAO,GAAG;AACjE,cAAI,OAAO,WAAW,QAAQ,IAAI;AAClC,iBAAO,KAAK,IAAI,EAAE,UAAU,cAAc,MAAM,OAAO,IAAI;AAAA,QAC/D;AACA,QAAAA,oBAAmB,UAAU,eAAe,SAAU,QAAQ,MAAM;AAChE,cAAI,KAAK,KAAK,OAAO,gBAAgB,GAAG,eAAe,YAAY,GAAG;AACtE,iBAAO,KAAK,IAAI,EAAE,UACd;AAAA,YAAc;AAAA;AAAA,YACd,UAAU;AAAA,YAAiB;AAAA,YAAO;AAAA,UAAS;AAAA,QACnD;AACA,QAAAA,oBAAmB,UAAU,uBAAuB,SAAU,OAAO;AACjE,cAAI,KAAK,KAAK,OAAO,gBAAgB,GAAG,eAAe,YAAY,GAAG,WAAW,OAAO,GAAG;AAC3F,cAAI,OAAO,CAAC;AACZ,sBAAY,MAAM,MAAM,KAAK;AAC7B,wBAAc,MAAM,UAAU,iBAAiB,OAAO,SAAS;AAAA,QACnE;AAGA,QAAAA,oBAAmB,UAAU,mBAAmB,SAAU,QAAQ;AAC9D,cAAI,KAAK,KAAK,OAAO,gBAAgB,GAAG,eAAe,YAAY,GAAG;AACtE,wBAAc,QAAQ,UAAU,iBAAiB,OAAO,SAAS;AAAA,QACrE;AACA,QAAAA,oBAAmB,UAAU,gBAAgB,WAAY;AACrD,0BAAgB;AAChB,eAAK,SAAS;AAAA,YACV,UAAU;AAAA,UACd,CAAC;AAAA,QACL;AACA,QAAAA,oBAAmB,UAAU,iBAAiB,WAAY;AACtD,cAAI,QAAQ;AACZ,cAAI,CAAC,KAAK,MAAM,UAAU;AACtB;AAAA,UACJ;AACA,0BAAgB;AAChB,cAAI,KAAK,KAAK,MAAM;AACpB,eAAK,SAAS;AAAA,YACV,UAAU;AAAA,UACd,GAAG,WAAY;AACX,gBAAI,SAAK,8BAAY,KAAK;AAC1B,gBAAI,QAAQ,GAAG,QAAQ,OAAO;AAC9B,aAAE,SACE,MAAM,iBAAiB,MAAM,OAAO,IAAI,4BAA4B,CAAC,EAChE,UACL,OACA,GAAG,MAAM;AAAA,UACjB,CAAC;AAAA,QACL;AACA,QAAAA,oBAAmB,UAAU,cAAc,WAAY;AACnD,cAAI,KAAK,KAAK,OAAO,YAAY,GAAG,WAAW,OAAO,GAAG,MAAM,QAAQ,GAAG,OAAO,KAAK,GAAG,WAAW,KAAK,GAAG;AAC5G,cAAI;AACJ,cAAI,SAAS,UAAU,SAAS;AAChC,cAAI,cAAc,MAAM;AACpB,qBAAS;AAAA,cACL,MAAM;AAAA,cACN,OAAO;AAAA,cACP,WAAW;AAAA,cACX,MAAM;AAAA,gBACF;AAAA,kBACI,MAAM;AAAA,kBACN;AAAA,kBACA,aAAa;AAAA,kBACb,OAAO;AAAA,gBACX;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ,WACS,WAAW;AAChB,gBAAI,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,YAAY;AAC5E,uBAAS;AAAA,gBACL,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,eAAe;AAAA,gBACf,MAAM;AAAA,kBACF,SAAS,SAAS,CAAC,OAAG,YAAAE,SAAK,WAAW,YAAY,CAAC,GAAG,EAAE,OAAO,MAAM,CAAC;AAAA,gBAC1E;AAAA,cACJ;AAAA,YACJ,WACS,UAAU,QACf,CAAC,CAAC,CAAC,SAAS,SAAS,SAAS,YAAY,UAAU,EAAE,QAAQ,UAAU,IAAI,GAAG;AAC/E,uBAAS,SAAS,SAAS,EAAE,OAAO,IAAI,WAAW,CAAC,OAAO,GAAG,SAAS,GAAG,EAAE,MAAM,UAAU,MAAM,OAAO,CAAC;AAAA,YAC9G,OACK;AACD,uBAAS;AAAA,gBACL,OAAO;AAAA,gBACP,WAAW,UAAU;AAAA,gBACrB,MAAM;AAAA,gBACN,WAAW,CAAC;AAAA,gBACZ,MAAM;AAAA,gBACN,MAAM;AAAA,kBACF,SAAS,SAAS,SAAS,EAAE,MAAM,UAAU,QAAQ,cAAc,MAAM,UAAU,QAAQ,KAAK,GAAI,SAAS,EAAE,GAAO,IAAI,CAAC,CAAE,GAAG,SAAS,GAAG,EAAE,MAAM,OAAU,CAAC;AAAA,gBACnK;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AACA,cAAI,aAAa,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU;AACjF,cAAI,QAAQ;AACR,qBAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,eAAe,EAAE,UAAU,aAAa,SAAS,UAAU,aAC3F,CAAC,IACD;AAAA,cACE;AAAA,gBACI,MAAM;AAAA,gBACN,OAAO,GAAG,QAAQ;AAAA,gBAClB,YAAY;AAAA,cAChB;AAAA,cACA;AAAA,gBACI,OAAO,GAAG,SAAS;AAAA,gBACnB,MAAM;AAAA,gBACN,SAAS;AAAA,cACb;AAAA,YACJ,EAAE,CAAC;AAAA,UACf;AACA,iBAAO,UAAU;AAAA,QACrB;AACA,QAAAF,oBAAmB,UAAU,cAAc,SAAU,GAAG;AACpD,cAAI,IAAI,IAAI,IAAI;AAChB,cAAI,WAAO,eAAAC,SAAQ,CAAC;AACpB,cAAI,SAAS,WACT,CAAC,CAAC,CAAC,SAAS,UAAU,EAAE,QAAQ,EAAE,OAAO,OAAO,GAAG;AACnD,cAAE,eAAe;AACjB,cAAE,gBAAgB;AAClB,gBAAI,KAAK,UAAU;AACf,eAAC,MAAM,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,EAAE;AAAA,YAC7H,WACS,KAAK,MAAM;AAChB,eAAC,MAAM,KAAK,KAAK,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,EAAE;AAAA,YACzH,OACK;AACD,mBAAK,cAAc;AAAA,YACvB;AAAA,UACJ;AAAA,QACJ;AACA,QAAAD,oBAAmB,UAAU,gBAAgB,WAAY;AACrD,cAAI,QAAQ;AACZ,cAAI,KAAK,KAAK,OAAO,YAAY,GAAG,WAAW,SAAS,GAAG,QAAQ,mBAAmB,GAAG,kBAAkB,KAAK,GAAG,aAAa,KAAK,GAAG,YAAY,qBAAqB,GAAG;AAC5K,cAAI,UAAW,aAAAG,QAAM,cAAc,OAAO,EAAE,KAAK,KAAK,YAAY,WAAW,GAAG,UAAU,SAAS,EAAE,GAAG,OAAO,mBAAmB,KAAK,YAAY,GAAG;AAAA,YAClJ,OAAO;AAAA,YACP,eAAe;AAAA,YACf,UAAU,KAAK;AAAA,YACf,UAAU,KAAK;AAAA,YACf,UAAU;AAAA,YACV,gBAAgB;AAAA,YAChB,KAAK,KAAK;AAAA,YACV,kBAAkB,WAAY;AAAE,qBAAO,MAAM;AAAA,YAAS;AAAA,YACtD;AAAA,YACA,WAAW;AAAA,UACf,CAAC,CAAC;AACF,6BAAmB,oBAAqB,WAAY;AAAE,uBAAO,8BAAY,KAAK;AAAA,UAAG;AACjF,iBAAQ,aAAAA,QAAM;AAAA,YAAc;AAAA,YAAS,EAAE,WAAW,kBAAkB,QAAQ,WAAY;AAAE,qBAAO,MAAM;AAAA,YAAQ,GAAG,QAAQ,KAAK,gBAAgB,WAAW,oGAAoG,MAAM,KAAK;AAAA,YACrQ,aAAAA,QAAM,cAAc,WAAS,EAAE,aAAa,IAAI,WAAW,GAAG,GAAG,OAAO,IAAI,mBAAmB,GAAG,UAAU,gBAAgB,GAAG,QAAQ,KAAK,gBAAgB,SAAS,KAAK,GAAG,OAAO;AAAA,UAAC;AAAA,QAC7L;AACA,QAAAH,oBAAmB,UAAU,mBAAmB,WAAY;AACxD,cAAI,IAAI;AACR,cAAI,KAAK,KAAK,OAAO,SAAS,GAAG,QAAQ,KAAK,GAAG,YAAY,qBAAqB,GAAG,oBAAoB,WAAW,GAAG,UAAU,QAAQ,GAAG,OAAO,OAAO,GAAG;AAC7J,cAAI,SAAS,KAAK,YAAY;AAE9B,cAAI,MAAM,QAAQ,OAAO,IAAI,KACzB,OAAO,KAAK,WAAW,KACvB,CAAC,OAAO,KAAK,CAAC,EAAE;AAAA,UAChB,CAAC,OAAO,KAAK,CAAC,EAAE;AAAA,UAChB,GAAG,KAAK,OAAO,KAAK,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,cAChE,OAAO,KAAK,CAAC,EAAE,QACf,OAAO,KAAK,CAAC,EAAE,SAAS,QACxB,OAAO,KAAK,CAAC,EAAE,UACb,KAAK,kBAAkB,OAAO,KAAK,CAAC,EAAE,IAAI,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa;AACpG,mBAAQ,aAAAG,QAAM,cAAc,gBAAgB,SAAS,CAAC,GAAG,KAAK,OAAO,EAAE,QAAQ,OAAO,KAAK,CAAC,GAAG,UAAU,KAAK,sBAAsB,cAAc,KAAK,kBAAkB,aAAa,KAAK,YAAY,CAAC,CAAC;AAAA,UAC7M;AACA,iBAAO,OAAO,eAAe,QAAQ;AAAA,YACjC,OAAO;AAAA,YACP,kBAAkB;AAAA,YAClB,WAAW,GAAG,iBAAiB;AAAA,YAC/B,KAAK,KAAK;AAAA,YACV,YAAY;AAAA,YACZ,QAAQ,KAAK;AAAA,YACb,UAAU,KAAK;AAAA,YACf,cAAc,KAAK;AAAA,YACnB,gBAAgB;AAAA,YAChB;AAAA,YACA;AAAA,YACA,eAAe;AAAA;AAAA,YAEf,gBAAgB;AAAA;AAAA;AAAA,YAGhB,sBAAsB;AAAA,UAC1B,CAAC;AAAA,QACL;AACA,QAAAH,oBAAmB,UAAU,SAAS,WAAY;AAC9C,cAAI,KAAK,KAAK,OAAO,gBAAgB,GAAG,eAAe,YAAY,GAAG,WAAW,mBAAmB,GAAG,kBAAkB,YAAY,GAAG,WAAW,KAAK,GAAG,YAAY,SAAS,GAAG,QAAQ,QAAQ,GAAG,OAAO,qBAAqB,GAAG,oBAAoB,WAAW,GAAG,UAAU,WAAW,GAAG;AAE/R,cAAI,KAAK,KAAK,OAAO,iBAAiB,GAAG,gBAAgB,mBAAmB,GAAG,kBAAkB,uBAAuB,GAAG,sBAAsB,YAAY,OAAO,IAAI,CAAC,kBAAkB,oBAAoB,sBAAsB,CAAC;AACtO,cAAI,YACA,CAAC,aACD,CAAC,iBACA,EAAE,OAAO,cAAc,aAAa,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,yBACjG,qBAAqB,SACzB,OAGF;AACE,mBAAO,aAAAG,QAAM,cAAcJ,YAAW,SAAS,CAAC,GAAG,WAAW,EAAE,aAAa,KAAK,YAAY,CAAC,CAAC;AAAA,UACpG;AACA,cAAI,UAAU,SAAS,YACnB,UAAU,YAAY;AACtB,mBAAQ,aAAAI,QAAM,cAAcJ,YAAW,SAAS,CAAC,GAAG,WAAW,EAAE,WAAW,GAAG,wBAAwB,SAAS,GAAG,UAAU,UAAU,cAAc,QAC3I,SACA,KAAK,SAAS,WAAW,OAAO,KAAK,YAAY,CAAC,GAAG,KAAK,iBAAiB,CAAC;AAAA,UAC1F,OACK;AACD,mBAAQ,aAAAI,QAAM;AAAA,cAAcJ;AAAA,cAAW,SAAS,CAAC,GAAG,WAAW,EAAE,WAAW,GAAG,wBAAwB,WAAW;AAAA,gBAC1G,IAAI,KAAK,MAAM;AAAA,cACnB,CAAC,GAAG,UAAU,UAAU,cAAc,QAChC,SACA,KAAK,SAAS,WAAW,OAAO,KAAK,YAAY,CAAC;AAAA,cACxD,aAAAI,QAAM,cAAcJ,YAAW,SAAS,CAAC,GAAG,WAAW,EAAE,cAAc,MAAM,OAAO,KAAK,CAAC,CAAC;AAAA,cAC3F,WACM,OACA,OAAO,qBAAqB;AAAA,gBAC1B,MAAM;AAAA,gBACN,UAAU;AAAA,gBACV,SAAS,KAAK;AAAA,gBACd,WAAW;AAAA,gBACX,MAAM,UAAU,QAAQ;AAAA,gBACxB,OAAO;AAAA,cACX,CAAC;AAAA,cACL,KAAK,MAAM,WAAW,KAAK,cAAc,IAAI;AAAA,YAAI;AAAA,UACzD;AAAA,QACJ;AACA,QAAAC,oBAAmB,oBAAoBD;AACvC,eAAOC;AAAA,MACX,EAAE,aAAAG,QAAM,aAAa;AAAA;AACrB,uCAAAC,SAAoB,oBAAoBL,UAAS;AACjD,WAAO;AAAA,EACX;AACJ;AACA,SAAS,eAAe,OAAO;AAC3B,MAAI;AACJ,MAAI,SAAS,MAAM,QAAQ,SAAS,MAAM,QAAQ,OAAO,MAAM,MAAM,WAAW,MAAM,UAAU,eAAe,MAAM,cAAc,cAAc,MAAM,aAAa,qBAAqB,MAAM;AAC/L,wBACI,aAAAI,QAAM,UAAU,WAAY;AACxB,QAAI,QAAQ,aAAa,KAAK;AAC9B,QAAI,SACA,UAAU,aAAa,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG,EAAE,oBAAoB,MAAM,CAAC,CAAC,GAAG;AACtF,eAAS,KAAK;AAAA,IAClB;AAAA,EACJ,GAAG,CAAC,CAAC;AACT,SAAO,OAAO,oBAAoB,QAAQ;AAAA,IACtC,MAAM;AAAA,IACN,QAAQ,KAAK,aAAa,KAAK,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,IACnE;AAAA,IACA;AAAA,IACA;AAAA,IACA,eAAe;AAAA;AAAA,IAEf,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,EAC1B,CAAC;AACL;;;AC3bA,IAAAE,gBAAkB;AAClB,IAAAC,kCAAgC;AAQhC,IAAI,cAAc,WAAY;AAC1B,SAAO,SAAUC,YAAW;AACxB,QAAI;AAAA;AAAA,MAAoC,SAAU,QAAQ;AACtD,kBAAUC,qBAAoB,MAAM;AACpC,iBAASA,sBAAqB;AAC1B,iBAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,QAC/D;AACA,QAAAA,oBAAmB,UAAU,cAAc,SAAU,SAAS;AAC1D,cAAI,KAAK,KAAK,OAAO,MAAM,GAAG,KAAK,aAAa,GAAG;AACnD,cAAI,QAAQ,IAAI,KAAK,SAAS,EAAE,QAAQ,WAAW,CAAC;AAAA,QACxD;AACA,QAAAA,oBAAmB,UAAU,SAAS,WAAY;AAC9C,cAAI,KAAK,KAAK,OAAO,OAAO,GAAG,MAAM,YAAY,GAAG,WAAW,OAAO,GAAG,MAAM,QAAQ,GAAG,OAAO,KAAK,GAAG,YAAY,KAAK,GAAG,WAAW,MAAM,GAAG,KAAK,mBAAmB,GAAG;AAC5K,cAAI,WAAW,KAAK,MAAM;AAC1B,cAAI,YAAY,CAAC,OAAO;AACpB,gBAAI,UAAU,OAAO,SAAS,WAAW,OAAO,OAAO,YAAY,IAAI;AACvE,gBAAI,WAAW,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,YAAY,OAClF,OAAO,SAAS,SAAS,IAAI,IAC7B,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS;AACnE,gBAAI,SAAS;AACT,qBAAQ,cAAAC,QAAM;AAAA,gBAAcF;AAAA,gBAAW,SAAS,CAAC,GAAG,KAAK,OAAO,EAAE,WAAW,GAAG,mBAAmB,SAAS,EAAE,CAAC;AAAA,gBAC3G,cAAAE,QAAM,cAAcF,YAAW,SAAS,CAAC,GAAG,KAAK,OAAO,EAAE,cAAc,MAAM,OAAO,KAAK,CAAC,CAAC;AAAA,gBAC5F,cAAAE,QAAM;AAAA,kBAAc;AAAA,kBAAgB,EAAE,WAAW,SAAS,SAAS,YAAY,QAAQ,YAAY,SAAS,UAAU,GAAG,cAAc,GAAG,SAAS,SAAS,WAAW,qBAAqB,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,mBAAmB;AAAA,kBACzP,cAAAA,QAAM;AAAA,oBAAc;AAAA,oBAAK,EAAE,KAAK,YAAY,WAAW,GAAG,eAAe,GAAG,SAAS,KAAK,YAAY,KAAK,MAAM,OAAO,EAAE;AAAA,oBACtH,cAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,QAAQ,WAAW,OAAO,CAAC;AAAA,kBAAC;AAAA,gBAAC;AAAA,cAAC;AAAA,YAChF;AAAA,UACJ;AACA,iBAAO,cAAAA,QAAM,cAAcF,YAAW,SAAS,CAAC,GAAG,KAAK,KAAK,CAAC;AAAA,QAClE;AACA,QAAAC,oBAAmB,oBAAoBD;AACvC,eAAOC;AAAA,MACX,EAAE,cAAAC,QAAM,aAAa;AAAA;AACrB,wCAAAC,SAAoB,oBAAoBH,UAAS;AACjD,WAAO;AAAA,EACX;AACJ;;;AC5CA,IAAAI,gBAAkB;AAClB,IAAAC,oBAA4B;AAC5B,IAAAC,kCAAgC;AAQhC,IAAI,aAAa,SAAU,QAAQ;AAC/B,MAAI,WAAW,QAAQ;AAAE,aAAS,CAAC;AAAA,EAAG;AACtC,SAAO,SAAUC,YAAW;AACxB,QAAI,qBAAqB;AACzB,QAAI;AAAA;AAAA,MAAkC,SAAU,QAAQ;AACpD,kBAAUC,mBAAkB,MAAM;AAClC,iBAASA,kBAAiB,OAAO;AAC7B,cAAI,QAAQ,OAAO,KAAK,MAAM,KAAK,KAAK;AACxC,gBAAM,cAAc,MAAM,YAAY,KAAK,KAAK;AAChD,gBAAM,eAAe,MAAM,aAAa,KAAK,KAAK;AAClD,gBAAM,oBAAoB,MAAM,kBAAkB,KAAK,KAAK;AAC5D,gBAAM,kBAAkB,MAAM,gBAAgB,KAAK,KAAK;AACxD,gBAAM,YAAY,MAAM,UAAU,KAAK,KAAK;AAE5C,gBAAM,QAAQ;AAAA,YACV,UAAU;AAAA,UACd;AACA,iBAAO;AAAA,QACX;AACA,QAAAA,kBAAiB,UAAU,YAAY,SAAU,KAAK;AAClD,eAAK,SAAS;AAAA,QAClB;AACA,QAAAA,kBAAiB,UAAU,cAAc,SAAU,OAAO;AACtD,cAAI,QAAQ;AACZ,cAAI,kBAAkB,KAAK,MAAM;AACjC,iCAAuB,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,aAAa;AACxG,+BAAqB;AACrB,cAAI,IAAI,MAAM;AAEd,cAAK,KAAK,aAAa,MAAM,cACzB,KACA,EAAE,gBAAgB,EAAE,gBACpB,KAAK,aAAa,MAAM,UAAU;AAClC;AAAA,UACJ;AACA,eAAK,SAAS;AAAA,YACV,UAAU;AAAA,UACd,GAAG,WAAY;AAAE,mBAAO,mBAAmB,gBAAgB,MAAM,MAAM,OAAO;AAAA,UAAG,CAAC;AAAA,QACtF;AACA,QAAAA,kBAAiB,UAAU,eAAe,WAAY;AAClD,cAAI,QAAQ;AACZ,uBAAa,KAAK,KAAK;AACvB,cAAI,CAAC,KAAK,MAAM,UAAU;AACtB;AAAA,UACJ;AACA,+BAAqB;AACrB,cAAI,kBAAkB,KAAK,MAAM;AACjC,eAAK,SAAS;AAAA,YACV,UAAU;AAAA,UACd,GAAG,WAAY;AAAE,mBAAO,mBAAmB,gBAAgB,MAAM,MAAM,OAAO;AAAA,UAAG,CAAC;AAAA,QACtF;AACA,QAAAA,kBAAiB,UAAU,oBAAoB,WAAY;AAEvD,eAAK,QAAQ,WAAW,KAAK,cAAc,GAAG;AAAA,QAClD;AACA,QAAAA,kBAAiB,UAAU,kBAAkB,WAAY;AACrD,uBAAa,KAAK,KAAK;AAAA,QAC3B;AACA,QAAAA,kBAAiB,UAAU,cAAc,WAAY;AACjD,cAAI,KAAK,KAAK,OAAO,UAAU,GAAG,SAAS,OAAO,GAAG,MAAM,QAAQ,GAAG,OAAO,KAAK,GAAG;AACrF,cAAI;AACJ,cAAI,YAAY,MAAM;AAClB,qBAAS;AAAA,cACL,MAAM;AAAA,cACN,MAAM,KAAK,OAAO,MAAM,GAAG;AAAA,YAC/B;AAAA,UACJ,WACS,YACJ,QAAQ,SAAS,YAAY,QAAQ,SAAS,WAAW;AAC1D,qBAAS,SAAS,SAAS,EAAE,SAAS;AAAA,cAC9B;AAAA,gBACI,OAAO,GAAG,cAAc;AAAA,gBACxB,MAAM;AAAA,gBACN,YAAY;AAAA,cAChB;AAAA,YACJ,EAAE,GAAG,OAAO,GAAG,EAAE,MAAM,QAAQ,KAAK,CAAC;AAAA,UAC7C,WACS,OAAO,YAAY,UAAU;AAClC,qBAAS;AAAA,cACL,MAAM;AAAA,cACN,MAAM;AAAA,YACV;AAAA,UACJ,WACS,SAAS;AACd,qBAAS,SAAS,EAAE,MAAM,QAAQ,GAAG,OAAO;AAAA,UAChD,WACS,KAAK,aAAa,MAAM,YAAY;AACzC,qBAAS;AAAA,cACL,MAAM;AAAA,cACN,MAAM,KAAK,OAAO,MAAM,GAAG;AAAA,YAC/B;AAAA,UACJ;AACA,iBAAO,UAAU;AAAA,QACrB;AACA,QAAAA,kBAAiB,UAAU,YAAY,WAAY;AAC/C,cAAI,UAAU,KAAK,MAAM;AACzB,cAAI,CAAC,WAAW,OAAO,YAAY,aAAa,CAAC,QAAQ,QAAQ;AAC7D,mBAAO;AAAA,UACX;AAEA,iBAAO;AAAA,YACH,GAAG,QAAQ,OAAO,QAAQ;AAAA,YAC1B,GAAG,QAAQ,OAAO,OAAO;AAAA,UAC7B;AAAA,QACJ;AACA,QAAAA,kBAAiB,UAAU,gBAAgB,WAAY;AACnD,cAAI,QAAQ;AACZ,cAAI,KAAK,KAAK,OAAO,UAAU,GAAG,SAAS,SAAS,GAAG,QAAQ,mBAAmB,GAAG,kBAAkB,KAAK,GAAG,YAAY,KAAK,GAAG;AACnI,cAAI,YACC,QAAQ,SAAS,YACd,QAAQ,SAAS,WAAW;AAChC,mBAAO,OAAO,kBAAkB,KAAK,YAAY,GAAG;AAAA,cAChD,MAAM;AAAA,cACN,SAAS,KAAK;AAAA,cACd,WAAW,KAAK;AAAA,YACpB,CAAC;AAAA,UACL;AACA,cAAI,UAAU,OAAO,kBAAkB,KAAK,YAAY,GAAG;AAAA,YACvD,WAAW,GAAG,WAAW,QAAQ,SAAS;AAAA,UAC9C,CAAC;AACD,cAAI,CAAC,kBAAkB;AACnB,+BAAmB,WAAY;AAAE,yBAAO,+BAAY,KAAK;AAAA,YAAG;AAAA,UAChE;AACA,cAAI,kBAAkB,KAAK,aAAa;AACxC,cAAI,iBAAiB,oBAAoB,cAAc,CAAC,UAClD,4BACA;AACN,cAAIC,YAAY,WAAW,QAAQ,YAAa;AAChD,cAAI,UAAU,WAAW,KAAKA,SAAQ;AACtC,iBAAO,UAAW,cAAAC,QAAM,cAAc,WAAW,EAAE,UAAU,CAAC,KAAK,MAAM,UAAU,aAAa,KAAK,aAAa,GAAG,SAAU,KAAK;AAChI,mBAAQ,cAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,mCAAmC,OAAOD,SAAQ,CAAC,GAAG,eAAe,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,aAAa,UACrL,MAAM,eACN,QAAW,eAAe,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,aAAa,UACjG,MAAM,kBACN,QAAW,IAAS,GAAG,OAAO;AAAA,UAC5C,CAAC,IAAM,cAAAC,QAAM;AAAA,YAAc;AAAA,YAAS,EAAE,WAAW,kBAAkB,WAAWD,aAAY,OAAO,YAAY,gBAAgB,QAAQ,WAAY;AAAE,qBAAO,MAAM;AAAA,YAAQ,GAAG,QAAQ,KAAK,cAAc,WAAW,MAAM,MAAM,KAAK;AAAA,YAC9N,cAAAC,QAAM,cAAc,WAAS,EAAE,aAAa,IAAI,WAAW,GAAG,uBAAuB,WAAW,QAAQ,gBAAgB,GAAG,QAAQ,KAAK,UAAU,GAAG,eAAe,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,aAAa,WACnO,kBACE,KAAK,eACL,QAAW,eAAe,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,aAAa,WACnG,kBACE,KAAK,kBACL,OAAU,GAAG,OAAO;AAAA,UAAC;AAAA,QACvC;AACA,QAAAF,kBAAiB,UAAU,eAAe,WAAY;AAClD,cAAI,eAAe,KAAK,MAAM;AAC9B,iBAAO,iBAAiB,YAAY,KAAK;AAAA,QAC7C;AACA,QAAAA,kBAAiB,UAAU,SAAS,WAAY;AAC5C,cAAI,KAAK,KAAK,OAAO,UAAU,GAAG,SAAS,iBAAiB,GAAG,gBAAgB,gBAAgB,GAAG,eAAe,YAAY,GAAG,WAAW,QAAQ,GAAG,OAAO,QAAQ,GAAG,OAAO,KAAK,GAAG,YAAY,WAAW,GAAG;AACjN,cAAI,kBAAkB,KAAK,aAAa;AACxC,cAAK,CAAC,WAAW,CAAC,mBACd,mBAAmB,SACnB,SACA,kBAAkB,OAAO;AACzB,mBAAO,cAAAE,QAAM,cAAcH,YAAW,SAAS,CAAC,GAAG,KAAK,KAAK,CAAC;AAAA,UAClE;AACA,cAAI,eAAe,CAAC;AACpB,cAAI,UAAU,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AACxE,cAAI,YAAY,WACX,oBAAoB,cAAc,CAAC,SAAU;AAC9C,yBAAa,eAAe,KAAK;AACjC,yBAAa,eAAe,KAAK;AAAA,UACrC,OACK;AACD,yBAAa,UAAU,KAAK;AAAA,UAChC;AACA,iBAAQ,cAAAG,QAAM,cAAcH,YAAW,SAAS,CAAC,GAAG,KAAK,OAAO,EAAE,WAAW,GAAG,sBAAsB,WAAW;AAAA,YACzG,IAAI,KAAK,MAAM;AAAA,UACnB,CAAC,GAAG,KAAK,OAAO,eAAe,KAAK,YAAY,OAAU,CAAC,IAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,cAAc,SAAS,UAAW,cAAAG,QAAM;AAAA,YAAc,cAAAA,QAAM;AAAA,YAAU;AAAA,YAC/L,cAAAA,QAAM,cAAcH,YAAW,SAAS,CAAC,GAAG,KAAK,OAAO,EAAE,cAAc,MAAM,OAAO,KAAK,CAAC,CAAC;AAAA,YAC5F,cAAAG,QAAM;AAAA,cAAc;AAAA,cAAQ,SAAS,EAAE,KAAK,eAAe,WAAW,GAAG,kBAAkB,EAAE,GAAG,cAAc,EAAE,KAAK,OAAO,eAAe,SAAY,KAAK,UAAU,CAAC;AAAA,cACnK,cAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,WAAW,WAAW,OAAO,CAAC;AAAA,YAAC;AAAA,YACrE,KAAK,MAAM,WAAW,KAAK,cAAc,IAAI;AAAA,UAAI,IAAM,cAAAA,QAAM;AAAA,YAAc,cAAAA,QAAM;AAAA,YAAU;AAAA,YAC3F,cAAAA,QAAM;AAAA,cAAc;AAAA,cAAO,SAAS,EAAE,WAAW,GAAG,qBAAqB,kBAC/D,uBAAuB,kBACvB,EAAE,EAAE,GAAG,cAAc,EAAE,KAAK,OAAO,eAAe,SAAY,KAAK,UAAU,CAAC;AAAA,cACpF,cAAAA,QAAM,cAAcH,YAAW,SAAS,CAAC,GAAG,KAAK,OAAO,EAAE,cAAc,MAAM,OAAO,KAAK,CAAC,CAAC;AAAA,YAAC;AAAA,YACjG,KAAK,MAAM,WAAW,KAAK,cAAc,IAAI;AAAA,UAAI,CAAE;AAAA,QAC3D;AACA,QAAAC,kBAAiB,oBAAoBD;AACrC,eAAOC;AAAA,MACX,EAAE,cAAAE,QAAM,SAAS;AAAA;AACjB,wCAAAC,SAAoB,kBAAkBJ,UAAS;AAC/C,WAAO;AAAA,EACX;AACJ;;;AC1MA,IAAAK,iBAA0F;;;ACA1F,IAAAC,gBAAyB;AACzB,IAAI,CAAC,wBAAU;AACX,QAAM,IAAI,MAAM,mDAAmD;AACvE;AACA,IAAI,CAAC,KAAK;AACN,QAAM,IAAI,MAAM,kEAAkE;AACtF;;;ACPA,IAAAC,oBAAwC;;;ACgBxC,IAAAC,gBAAiD;AAhBjD,IAAIC,UAAkC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,OAAO,WAAW,cAAc,EAAE,OAAO,QAAQ;AACzD,MAAI,CAAC;AAAG,WAAO;AACf,MAAI,IAAI,EAAE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG;AAC/B,MAAI;AACA,YAAQ,MAAM,UAAU,MAAM,MAAM,EAAE,IAAI,EAAE,KAAK,GAAG;AAAM,SAAG,KAAK,EAAE,KAAK;AAAA,EAC7E,SACO,OAAO;AAAE,QAAI,EAAE,MAAa;AAAA,EAAG,UACtC;AACI,QAAI;AACA,UAAI,KAAK,CAAC,EAAE,SAAS,IAAI,EAAE,QAAQ;AAAI,UAAE,KAAK,CAAC;AAAA,IACnD,UACA;AAAU,UAAI;AAAG,cAAM,EAAE;AAAA,IAAO;AAAA,EACpC;AACA,SAAO;AACX;AAMO,SAAS,iBAAiB;AAC7B,MAAI,KAAKC,YAAO,wBAAS,CAAC,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC;AAC/C,MAAI,aAAS,2BAAY,WAAY;AACjC,YAAQ,SAAU,MAAM;AAAE,aAAO,OAAO;AAAA,IAAG,CAAC;AAAA,EAChD,GAAG,CAAC,CAAC;AACL,SAAO;AACX;AAQO,SAAS,UAAU,MAAM;AAC5B,MAAI,OAAO,WAAW,YAAY;AAC9B,WAAO,OAAO,IAAI,IAAI;AAAA,EAC1B;AACA,SAAO,mBAAmB,OAAO;AACrC;AACA,IAAI,aAAa,CAAC;AACX,SAAS,YAAY;AACxB,MAAI,OAAO,WAAW,aAAa;AAC/B,WAAO;AAAA,EACX;AACA,MAAI,OAAO,WAAW,aAAa;AAC/B,WAAO;AAAA,EACX;AACA,MAAI,OAAO,SAAS,aAAa;AAC7B,WAAO;AAAA,EACX;AACA,SAAO;AACX;;;ACnDA,IAAI,mCAAmC,UAAU,kBAAkB;AAC5D,SAAS,iBAAiB,UAAU;AACvC,WAAS;AACb;AACO,SAAS,iBAAiB,mBAAmB;AAChD,MAAI,CAAC,mBAAmB;AACpB,wBAAoB;AACpB,QAAI,MAAuC;AACvC,cAAQ,KAAK,6EAA6E;AAAA,IAC9F;AAAA,EACJ;AACA,YAAU,EAAE,kBAAqC,CAAC;AAClD,YAAU,EAAE,gCAAgC,IAAI;AACpD;;;ACfA,IAAI,+BAA+B;AAI5B,SAAS,yBAAyB;AACrC,SAAO;AACX;;;ACKA,IAAAC,gBAAiC;;;ACVjC,IAAAC,gBAAkB;;;ACAX,SAAS,gBAAgB,GAAG;AAC/B,SAAO,kBAAkB,CAAC;AAC9B;;;ACHO,SAAS,mBAAmBC,WAAU;AACzC,MAAI,eAAe;AAAA,IACf,SAAS,KAAK,IAAI,IAAI;AAAA,IACtB,UAAUA;AAAA,EACd;AACA,SAAO;AACX;AAOO,IAAI,wCAAwC;AAI5C,IAAI,4BAA4B;AAIvC,IAAI,0BAA0B,oBAAI,IAAI;AAItC,IAAI;AACJ,SAAS,4BAA4B;AACjC,MAAI,0BAA0B,QAAW;AACrC,4BAAwB,WAAW,2BAA2B,yBAAyB;AAAA,EAC3F;AACJ;AACO,SAAS,kCAAkC,KAAK;AACnD,0BAAwB,IAAI,GAAG;AAC/B,4BAA0B;AAC9B;AACO,SAAS,0BAA0B,aAAa;AACnD,0BAAwB,OAAO,WAAW;AAC9C;AAIA,SAAS,4BAA4B;AACjC,0BAAwB;AAGxB,MAAI,MAAM,KAAK,IAAI;AACnB,0BAAwB,QAAQ,SAAU,KAAK;AAC3C,QAAI,WAAW,IAAI;AACnB,QAAI,UAAU;AACV,UAAI,OAAO,SAAS,SAAS;AAEzB,iBAAS,SAAS,QAAQ;AAC1B,YAAI,UAAU;AACd,gCAAwB,OAAO,GAAG;AAAA,MACtC;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,MAAI,wBAAwB,OAAO,GAAG;AAGlC,8BAA0B;AAAA,EAC9B;AACJ;;;AC9DA,IAAAC,gBAAkB;AAClB,IAAI,eAAe;AACnB,IAAI,mBAAmB,CAAC;AACjB,SAAS,qBAAqB,aAAa;AAC9C,SAAO,WAAY;AACf,QAAI,cAAc;AACd,uBAAiB,KAAK,WAAW;AAAA,IACrC,OACK;AACD,kBAAY;AAAA,IAChB;AAAA,EACJ;AACJ;AACO,SAAS,0BAA0B,UAAU;AAEhD,iBAAe;AACf,qBAAmB,CAAC;AACpB,MAAI;AACA,QAAI,SAAS,SAAS;AAEtB,mBAAe;AAEf,QAAI,UAAU,iBAAiB,SAAS,IAAI,mBAAmB;AAE/D,kBAAAC,QAAM,gBAAgB,WAAY;AAC9B,UAAI,SAAS;AACT,gBAAQ,QAAQ,SAAU,GAAG;AAAE,iBAAO,EAAE;AAAA,QAAG,CAAC;AAAA,MAChD;AAAA,IACJ,GAAG,CAAC,OAAO,CAAC;AACZ,WAAO;AAAA,EACX,UACA;AACI,mBAAe;AAAA,EACnB;AACJ;;;AH3BA,IAAI,eAAe,CAAC;AACpB,SAAS,yBAAyB,mBAAmB;AACjD,SAAO,aAAa;AACxB;AACO,SAAS,YAAY,IAAI,mBAAmB,SAAS;AACxD,MAAI,sBAAsB,QAAQ;AAAE,wBAAoB;AAAA,EAAY;AACpE,MAAI,YAAY,QAAQ;AAAE,cAAU;AAAA,EAAc;AAClD,MAAI,uBAAuB,GAAG;AAC1B,WAAO,GAAG;AAAA,EACd;AACA,MAAI,wBAAwB,QAAQ,kBAAkB;AACtD,MAAI,cAAc,sBAAsB;AACxC,MAAI,oBAAoB,qBAAqB,WAAW;AAIxD,MAAI,sBAAsB,cAAAC,QAAM,OAAO,IAAI;AAC3C,MAAI,CAAC,oBAAoB,SAAS;AAG9B,QAAI,gBAAgB,IAAI,SAAS,yBAAyB,iBAAiB,GAAG,WAAY;AAMtF,UAAI,eAAe,SAAS;AAExB,0BAAkB;AAAA,MACtB,OACK;AAID,sBAAc,QAAQ;AACtB,4BAAoB,UAAU;AAAA,MAClC;AAAA,IACJ,CAAC;AACD,QAAI,iBAAiB,mBAAmB,aAAa;AACrD,wBAAoB,UAAU;AAC9B,sCAAkC,mBAAmB;AAAA,EACzD;AACA,MAAIC,YAAW,oBAAoB,QAAQ;AAC3C,gBAAAD,QAAM,cAAcC,WAAU,eAAe;AAC7C,gBAAAD,QAAM,UAAU,WAAY;AAExB,8BAA0B,mBAAmB;AAC7C,QAAI,oBAAoB,SAAS;AAI7B,0BAAoB,QAAQ,UAAU;AAAA,IAC1C,OACK;AAOD,0BAAoB,UAAU;AAAA,QAC1B,UAAU,IAAI,SAAS,yBAAyB,iBAAiB,GAAG,WAAY;AAE5E,4BAAkB;AAAA,QACtB,CAAC;AAAA,QACD,SAAS;AAAA,MACb;AACA,wBAAkB;AAAA,IACtB;AACA,WAAO,WAAY;AACf,0BAAoB,QAAQ,SAAS,QAAQ;AAC7C,0BAAoB,UAAU;AAAA,IAClC;AAAA,EACJ,GAAG,CAAC,CAAC;AAEL,SAAO,0BAA0B,WAAY;AAIzC,QAAI;AACJ,QAAI;AACJ,IAAAC,UAAS,MAAM,WAAY;AACvB,UAAI;AACA,oBAAY,GAAG;AAAA,MACnB,SACO,GAAG;AACN,oBAAY;AAAA,MAChB;AAAA,IACJ,CAAC;AACD,QAAI,WAAW;AACX,YAAM;AAAA,IACV;AACA,WAAO;AAAA,EACX,CAAC;AACL;;;ADrGA,IAAIC,YAAsC,WAAY;AAClD,EAAAA,YAAW,OAAO,UAAU,SAAS,GAAG;AACpC,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK;AAAG,YAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,YAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,SAAOA,UAAS,MAAM,MAAM,SAAS;AACzC;AAKO,SAAS,SAAS,eAAe,SAAS;AAE7C,MAAI,uBAAuB,GAAG;AAC1B,WAAO;AAAA,EACX;AACA,MAAI,cAAcA,UAAS,EAAE,YAAY,MAAM,GAAG,OAAO;AACzD,MAAI,oBAAoB,cAAc,eAAe,cAAc;AACnE,MAAI,mBAAmB,SAAU,OAAO,KAAK;AACzC,WAAO,YAAY,WAAY;AAAE,aAAO,cAAc,OAAO,GAAG;AAAA,IAAG,GAAG,iBAAiB;AAAA,EAC3F;AACA,mBAAiB,cAAc;AAI/B,MAAI;AACJ,MAAI,YAAY,YAAY;AAKxB,wBAAgB,wBAAK,0BAAW,gBAAgB,CAAC;AAAA,EACrD,OACK;AACD,wBAAgB,oBAAK,gBAAgB;AAAA,EACzC;AACA,uBAAqB,eAAe,aAAa;AACjD,gBAAc,cAAc;AAC5B,SAAO;AACX;AAEA,IAAI,iBAAiB;AAAA,EACjB,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,MAAM;AACV;AACA,SAAS,qBAAqB,MAAM,QAAQ;AACxC,SAAO,KAAK,IAAI,EAAE,QAAQ,SAAU,KAAK;AACrC,QAAI,CAAC,eAAe,GAAG,GAAG;AACtB,aAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,MAAM,GAAG,CAAC;AAAA,IACjF;AAAA,EACJ,CAAC;AACL;;;AKxDA,SAAS,kBAAkB,IAAI;AAC3B,MAAI,WAAW,GAAG,UAAU,SAAS,GAAG;AACxC,MAAI,YAAY,YAAY;AAC5B,MAAI,OAAO,cAAc,YAAY;AACjC,WAAO;AAAA,EACX;AACA,SAAO,YAAY,SAAS;AAChC;AACA,kBAAkB,YAAY;AAAA,EAC1B,UAAU;AAAA,EACV,QAAQ;AACZ;AACA,kBAAkB,cAAc;AAEhC,SAAS,mBAAmB,OAAO,KAAK,eAAe,UAAU,cAAc;AAC3E,MAAI,WAAW,QAAQ,aAAa,WAAW;AAC/C,MAAI,UAAU,OAAO,MAAM,GAAG,MAAM;AACpC,MAAI,eAAe,OAAO,MAAM,QAAQ,MAAM;AAC9C,MAAI,WAAW,cAAc;AACzB,WAAO,IAAI,MAAM,uEAAuE,aAAa;AAAA,EACzG;AACA,MAAI,WAAW,cAAc;AACzB,WAAO;AAAA,EACX;AACA,SAAO,IAAI,MAAM,mBACb,eACA,gBACA,OAAO,MAAM,GAAG,IAChB,oBAEA,gBACA,yBAAyB;AACjC;;;AChBA,IAAAC,gBAAkB;;;AChBlB,IAAAC,iBAAkB;;;ACElB,iBAAiB,yCAAK;;;AdEtB,IAAI,WAAW;AAEf,SAAS,aAAa,MAAM;AAC1B,MAAI,OAAO,WAAW,YAAY;AAChC,WAAO,OAAO,IAAI;AAAA,EACpB;AAEA,MAAI,SAAS,mBAAmB,OAAO,OAAO,WAAW;AACzD;AACA,SAAO;AACT;AAEA,IAAI,iBAAiB,CAAC;AACtB,SAAS,UAAU,MAAM;AACvB,MAAI,CAAC,eAAe,IAAI,GAAG;AACzB,mBAAe,IAAI,IAAI,aAAa,IAAI;AAAA,EAC1C;AAEA,SAAO,eAAe,IAAI;AAC5B;AACA,SAAS,aAAa,MAAM,MAAM;AAEhC,MAAI,GAAG,MAAM,IAAI;AAAG,WAAO;AAE3B,MAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,OAAO,SAAS,YAAY,SAAS,MAAM;AAC1F,WAAO;AAAA,EACT;AAEA,MAAI,QAAQ,OAAO,KAAK,IAAI;AAC5B,MAAI,QAAQ,OAAO,KAAK,IAAI;AAC5B,MAAI,MAAM,WAAW,MAAM;AAAQ,WAAO;AAE1C,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,CAAC,OAAO,eAAe,KAAK,MAAM,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC,GAAG;AACtF,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,GAAG,GAAG,GAAG;AAEhB,MAAI,MAAM,GAAG;AACX,WAAO,MAAM,KAAK,IAAI,MAAM,IAAI;AAAA,EAClC,OAAO;AACL,WAAO,MAAM,KAAK,MAAM;AAAA,EAC1B;AACF;AAiCA,SAAS,cAAc,QAAQ,MAAM,OAAO;AAC1C,MAAI,CAAC,OAAO,eAAe,KAAK,QAAQ,IAAI,GAAG;AAC7C,WAAO,eAAe,QAAQ,MAAM;AAAA,MAClC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH,OAAO;AACL,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;AAMA,IAAI,aAEJ,UAAU,aAAa;AACvB,IAAI,wBAEJ,UAAU,mBAAmB;AAE7B,SAAS,UAAU,QAAQ,YAAY;AACrC,MAAI,SAAS,OAAO,UAAU,IAAI,OAAO,UAAU,KAAK,CAAC;AACzD,MAAI,eAAe,OAAO,UAAU,IAAI,OAAO,UAAU,KAAK,CAAC;AAC/D,eAAa,QAAQ,aAAa,SAAS;AAC3C,eAAa,UAAU,aAAa,WAAW,CAAC;AAChD,SAAO;AACT;AAEA,SAAS,QAAQ,YAAY,QAAQ;AACnC,MAAI,QAAQ;AAEZ,WAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,SAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,EACjC;AAGA,SAAO;AAEP,MAAI;AACF,QAAI;AAEJ,QAAI,eAAe,UAAa,eAAe,MAAM;AACnD,eAAS,WAAW,MAAM,MAAM,IAAI;AAAA,IACtC;AAEA,WAAO;AAAA,EACT,UAAE;AACA,WAAO;AAEP,QAAI,OAAO,UAAU,GAAG;AACtB,aAAO,QAAQ,QAAQ,SAAU,IAAI;AACnC,WAAG,MAAM,OAAO,IAAI;AAAA,MACtB,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,SAAS,aAAa,YAAY,QAAQ;AACxC,MAAI,KAAK,SAASC,MAAK;AACrB,aAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,WAAK,KAAK,IAAI,UAAU,KAAK;AAAA,IAC/B;AAEA,YAAQ,KAAK,MAAM,SAAS,CAAC,MAAM,YAAY,MAAM,EAAE,OAAO,IAAI,CAAC;AAAA,EACrE;AAEA,SAAO;AACT;AAEA,SAAS,MAAM,QAAQ,YAAY,aAAa;AAC9C,MAAI,SAAS,UAAU,QAAQ,UAAU;AAEzC,MAAI,OAAO,QAAQ,QAAQ,WAAW,IAAI,GAAG;AAC3C,WAAO,QAAQ,KAAK,WAAW;AAAA,EACjC;AAEA,MAAI,gBAAgB,OAAO,yBAAyB,QAAQ,UAAU;AAEtE,MAAI,iBAAiB,cAAc,qBAAqB,GAAG;AAEzD;AAAA,EACF;AAEA,MAAI,iBAAiB,OAAO,UAAU;AACtC,MAAI,gBAAgB,iBAAiB,QAAQ,YAAY,gBAAgB,cAAc,aAAa,QAAW,QAAQ,cAAc;AACrI,SAAO,eAAe,QAAQ,YAAY,aAAa;AACzD;AAEA,SAAS,iBAAiB,QAAQ,YAAY,YAAY,QAAQ,gBAAgB;AAChF,MAAI;AAEJ,MAAI,cAAc,aAAa,gBAAgB,MAAM;AACrD,SAAO,OAAO,CAAC,GAAG,KAAK,qBAAqB,IAAI,MAAM,KAAK,MAAM,SAAS,MAAM;AAC9E,WAAO;AAAA,EACT,GAAG,KAAK,MAAM,SAAS,IAAI,OAAO;AAChC,QAAI,SAAS,QAAQ;AACnB,oBAAc,aAAa,OAAO,MAAM;AAAA,IAC1C,OAAO;AAKL,UAAI,gBAAgB,iBAAiB,MAAM,YAAY,YAAY,QAAQ,KAAK;AAChF,aAAO,eAAe,MAAM,YAAY,aAAa;AAAA,IACvD;AAAA,EACF,GAAG,KAAK,eAAe,MAAM,KAAK,aAAa,YAAY;AAC7D;AAEA,IAAI,oBAAoB,SAAS;AACjC,IAAI,uBAEJ,UAAU,qBAAqB;AAC/B,IAAI,kBAEJ,UAAU,aAAa;AACvB,IAAI,gBAEJ,UAAU,YAAY;AACtB,IAAI,qBAEJ,UAAU,iBAAiB;AAC3B,SAAS,2BAA2B,gBAAgB;AAClD,MAAI,SAAS,eAAe;AAE5B,MAAI,eAAe,oBAAoB,GAAG;AACxC,QAAI,cAAc,eAAe,MAAM;AACvC,YAAQ,KAAK,mCAAmC,cAAc,yEAAyE;AAAA,EACzI,OAAO;AACL,mBAAe,oBAAoB,IAAI;AAAA,EACzC;AAEA,MAAI,OAAO;AAAoB,UAAM,IAAI,MAAM,gEAAgE;AAE/G,MAAI,eAAe,WAAW,MAAM,8BAAe;AACjD,QAAI,CAAC,OAAO;AAAuB,aAAO,wBAAwB;AAAA,aAAqB,OAAO,0BAA0B;AACtH,YAAM,IAAI,MAAM,8EAA8E;AAAA,EAClG;AAMA,qBAAmB,QAAQ,OAAO;AAClC,qBAAmB,QAAQ,OAAO;AAClC,MAAI,aAAa,OAAO;AAExB,SAAO,SAAS,WAAY;AAC1B,WAAO,sBAAsB,KAAK,MAAM,UAAU;AAAA,EACpD;AAEA,QAAM,QAAQ,wBAAwB,WAAY;AAChD,QAAI;AAEJ,QAAI,uBAAuB,MAAM;AAAM;AACvC,KAAC,wBAAwB,KAAK,OAAO,iBAAiB,OAAO,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,QAAQ;AAC/I,SAAK,eAAe,IAAI;AAExB,QAAI,CAAC,KAAK,OAAO,iBAAiB,GAAG;AAEnC,UAAI,eAAe,eAAe,IAAI;AAEtC,cAAQ,KAAK,yDAAyD,eAAe,uKAAuK;AAAA,IAC9P;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,SAAS,eAAe,MAAM;AAC5B,SAAO,KAAK,eAAe,KAAK,QAAQ,KAAK,gBAAgB,KAAK,YAAY,eAAe,KAAK,YAAY,SAAS;AACzH;AAEA,SAAS,sBAAsB,QAAQ;AACrC,MAAI,QAAQ;AAEZ,MAAI,uBAAuB,MAAM;AAAM,WAAO,OAAO,KAAK,IAAI;AAM9D,gBAAc,MAAM,eAAe,KAAK;AAMxC,gBAAc,MAAM,oBAAoB,KAAK;AAC7C,MAAI,cAAc,eAAe,IAAI;AACrC,MAAI,aAAa,OAAO,KAAK,IAAI;AACjC,MAAI,qBAAqB;AACzB,MAAIC,YAAW,IAAI,SAAS,cAAc,aAAa,WAAY;AACjE,QAAI,CAAC,oBAAoB;AAIvB,2BAAqB;AAErB,UAAI,MAAM,eAAe,MAAM,MAAM;AACnC,YAAI,WAAW;AAEf,YAAI;AACF,wBAAc,OAAO,oBAAoB,IAAI;AAC7C,cAAI,CAAC,MAAM,aAAa;AAAG,qCAAU,UAAU,YAAY,KAAK,KAAK;AACrE,qBAAW;AAAA,QACb,UAAE;AACA,wBAAc,OAAO,oBAAoB,KAAK;AAC9C,cAAI;AAAU,YAAAA,UAAS,QAAQ;AAAA,QACjC;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,EAAAA,UAAS,gBAAgB,IAAI;AAC7B,iBAAe,iBAAiB,IAAIA;AACpC,OAAK,SAAS;AAEd,WAAS,iBAAiB;AACxB,yBAAqB;AACrB,QAAI,YAAY;AAChB,QAAI,YAAY;AAChB,IAAAA,UAAS,MAAM,WAAY;AACzB,UAAI;AACF,oBAAY,kBAAmB,OAAO,UAAU;AAAA,MAClD,SAAS,GAAG;AACV,oBAAY;AAAA,MACd;AAAA,IACF,CAAC;AAED,QAAI,WAAW;AACb,YAAM;AAAA,IACR;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,eAAe,KAAK,IAAI;AACjC;AAEA,SAAS,YAAY,WAAW,WAAW;AACzC,MAAI,uBAAuB,GAAG;AAC5B,YAAQ,KAAK,iLAAiL;AAAA,EAChM;AAGA,MAAI,KAAK,UAAU,WAAW;AAC5B,WAAO;AAAA,EACT;AAMA,SAAO,CAAC,aAAa,KAAK,OAAO,SAAS;AAC5C;AAEA,SAAS,mBAAmB,QAAQ,UAAU;AAC5C,MAAI,iBAAiB,UAAU,eAAe,WAAW,cAAc;AACvE,MAAI,gBAAgB,UAAU,eAAe,WAAW,aAAa;AAErE,WAAS,UAAU;AACjB,QAAI,CAAC,KAAK,aAAa,GAAG;AACxB,oBAAc,MAAM,eAAe,WAAW,cAAc,QAAQ,CAAC;AAAA,IACvE;AAEA,WAAO,KAAK,aAAa;AAAA,EAC3B;AAEA,SAAO,eAAe,QAAQ,UAAU;AAAA,IACtC,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,KAAK,SAAS,MAAM;AAClB,UAAI,gBAAgB;AAEpB,UAAI,wBAAyB,oBAAqB;AAChD,wBAAgB,qBAAsB,IAAI;AAAA,MAC5C;AAEA,cAAQ,KAAK,IAAI,EAAE,eAAe;AAElC,UAAI,wBAAyB,oBAAqB;AAChD,2BAAoB,aAAa;AAAA,MACnC;AAEA,aAAO,KAAK,cAAc;AAAA,IAC5B;AAAA,IACA,KAAK,SAAS,IAAI,GAAG;AACnB,UAAI,CAAC,KAAK,kBAAkB,KAAK,CAAC,aAAa,KAAK,cAAc,GAAG,CAAC,GAAG;AACvE,sBAAc,MAAM,gBAAgB,CAAC;AACrC,sBAAc,MAAM,eAAe,IAAI;AACvC,gBAAQ,KAAK,IAAI,EAAE,cAAc;AACjC,sBAAc,MAAM,eAAe,KAAK;AAAA,MAC1C,OAAO;AACL,sBAAc,MAAM,gBAAgB,CAAC;AAAA,MACvC;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAEA,IAAI,YAAY,OAAO,WAAW,cAAc,OAAO;AAEvD,IAAI,wBAAwB,YAE5B,OAAO,IAAI,mBAAmB,IAAI,OAAO,8BAAe,kBAExD,2BAAW,SAAU,OAAO;AAC1B,SAAO;AACT,CAAC,EAAE,UAAU;AACb,IAAI,kBAAkB,YAEtB,OAAO,IAAI,YAAY,IAAI,OAAO,wBAAS,kBAE3C,qBAAK,SAAU,OAAO;AACpB,SAAO;AACT,CAAC,EAAE,UAAU;AAKb,SAASC,UAAS,WAAW;AAC3B,MAAI,UAAU,gBAAgB,MAAM,MAAM;AACxC,YAAQ,KAAK,4IAA4I;AAAA,EAC3J;AAEA,MAAI,mBAAmB,UAAU,UAAU,MAAM,iBAAiB;AAChE,UAAM,IAAI,MAAM,gLAAgL;AAAA,EAClM;AAKA,MAAI,yBAAyB,UAAU,UAAU,MAAM,uBAAuB;AAC5E,QAAI,aAAa,UAAU,QAAQ;AACnC,QAAI,OAAO,eAAe;AAAY,YAAM,IAAI,MAAM,kDAAkD;AACxG,eAAO,2BAAW,SAAS,qBAAqB;AAC9C,UAAI,OAAO;AACX,iBAAO,8BAAc,mBAAU,MAAM,WAAY;AAC/C,eAAO,WAAW,MAAM,QAAW,IAAI;AAAA,MACzC,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAGA,MAAI,OAAO,cAAc,eAAe,CAAC,UAAU,aAAa,CAAC,UAAU,UAAU,WAAW,CAAC,UAAU,cAAc,KAAK,CAAC,OAAO,UAAU,cAAc,KAAK,0BAAW,SAAS,GAAG;AACxL,WAAO,SAAW,SAAS;AAAA,EAC7B;AAEA,SAAO,2BAA2B,SAAS;AAC7C;AAEA,SAAS,WAAW;AAClB,aAAW,OAAO,UAAU,SAAU,QAAQ;AAC5C,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AAExB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;AAEA,SAAS,8BAA8B,QAAQ,UAAU;AACvD,MAAI,UAAU;AAAM,WAAO,CAAC;AAC5B,MAAI,SAAS,CAAC;AACd,MAAI,aAAa,OAAO,KAAK,MAAM;AACnC,MAAI,KAAK;AAET,OAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACtC,UAAM,WAAW,CAAC;AAClB,QAAI,SAAS,QAAQ,GAAG,KAAK;AAAG;AAChC,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAC1B;AAEA,SAAO;AACT;AAEA,IAAI,sBAEJ,eAAAC,QAAe,cAAc,CAAC,CAAC;AAC/B,SAAS,SAAS,OAAO;AACvB,MAAI,WAAW,MAAM,UACjB,SAAS,8BAA8B,OAAO,CAAC,UAAU,CAAC;AAE9D,MAAI,cAAc,eAAAA,QAAe,WAAW,mBAAmB;AAC/D,MAAI,qBAAqB,eAAAA,QAAe,OAAO,SAAS,CAAC,GAAG,aAAa,MAAM,CAAC;AAChF,MAAI,QAAQ,mBAAmB;AAE/B,MAAI,MAAuC;AACzC,QAAI,WAAW,SAAS,CAAC,GAAG,OAAO,MAAM;AAGzC,QAAI,CAAC,aAAa,OAAO,QAAQ,GAAG;AAClC,YAAM,IAAI,MAAM,gJAAgJ;AAAA,IAClK;AAAA,EACF;AAEA,SAAO,eAAAA,QAAe,cAAc,oBAAoB,UAAU;AAAA,IAChE;AAAA,EACF,GAAG,QAAQ;AACb;AACA,SAAS,cAAc;AAyEvB,IAAI,gBAEJ,UAAU,uBAAuB;AACjC,IAAI,eAEJ,UAAU,sBAAsB;AAsDhC,SAAS,2BAA2B,WAAW;AAC7C,WAAS,UAAU,YAAY,OAAO,UAAU,eAAe,UAAU,cAAc;AACrF,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,WAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,IACjC;AAEA,WAAO,UAAU,WAAY;AAC3B,sBAAgB,iBAAiB;AACjC,qBAAe,gBAAgB;AAE/B,UAAI,MAAM,QAAQ,KAAK,MAAM;AAC3B,YAAI,YAAY;AACd,cAAI,SAAS,MAAM,QAAQ,MAAM,OAAO,SAAS;AACjD,iBAAO,IAAI,MAAM,SAAS,WAAW,OAAO,eAAe,iCAAsC,gBAAgB,0BAA0B,SAAS,IAAI;AAAA,QAC1J;AAEA,eAAO;AAAA,MACT,OAAO;AAEL,eAAO,UAAU,MAAM,QAAQ,CAAC,OAAO,UAAU,eAAe,UAAU,YAAY,EAAE,OAAO,IAAI,CAAC;AAAA,MACtG;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAI,mBAAmB,UAAU,KAAK,MAAM,KAAK;AAEjD,mBAAiB,aAAa,UAAU,KAAK,MAAM,IAAI;AACvD,SAAO;AACT;AAGA,SAAS,SAAS,UAAU,WAAW;AAErC,MAAI,aAAa,UAAU;AACzB,WAAO;AAAA,EACT;AAGA,MAAI,UAAU,eAAe,MAAM,UAAU;AAC3C,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,WAAW,cAAc,qBAAqB,QAAQ;AAC/D,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAGA,SAAS,YAAY,WAAW;AAC9B,MAAI,WAAW,OAAO;AAEtB,MAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,WAAO;AAAA,EACT;AAEA,MAAI,qBAAqB,QAAQ;AAI/B,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,UAAU,SAAS,GAAG;AACjC,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAIA,SAAS,eAAe,WAAW;AACjC,MAAI,WAAW,YAAY,SAAS;AAEpC,MAAI,aAAa,UAAU;AACzB,QAAI,qBAAqB,MAAM;AAC7B,aAAO;AAAA,IACT,WAAW,qBAAqB,QAAQ;AACtC,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,mCAAmC,iBAAiB,UAAU;AACrE,SAAO,2BAA2B,SAAU,OAAO,UAAU,eAAe,UAAU,cAAc;AAClG,WAAO,UAAU,WAAY;AAC3B,UAAI,iBAAiB;AACnB,YAAI,YAAY,MAAM,QAAQ,CAAC,MAAM,SAAS,YAAY;AAAG,iBAAO;AAAA,MACtE;AAEA,UAAI;AAEJ,cAAQ,UAAU;AAAA,QAChB,KAAK;AACH,wBAAc;AACd;AAAA,QAEF,KAAK;AACH,wBAAc;AACd;AAAA,QAEF,KAAK;AACH,wBAAc;AACd;AAAA,QAEF;AACE,gBAAM,IAAI,MAAM,0BAA0B,QAAQ;AAAA,MACtD;AAEA,UAAI,YAAY,MAAM,QAAQ;AAE9B,UAAI,CAAC,YAAY,SAAS,GAAG;AAC3B,YAAI,cAAc,eAAe,SAAS;AAC1C,YAAI,+BAA+B,kBAAkB,qBAAqB,SAAS,YAAY,IAAI,MAAM;AACzG,eAAO,IAAI,MAAM,mBAAmB,eAAe,gBAAgB,cAAc,oBAAyB,gBAAgB,iCAAiC,WAAW,MAAM,+BAA+B,GAAG;AAAA,MAChN;AAEA,aAAO;AAAA,IACT,CAAC;AAAA,EACH,CAAC;AACH;AAEA,SAAS,mCAAmC,iBAAiB,aAAa;AACxE,SAAO,2BAA2B,SAAU,OAAO,UAAU,eAAe,UAAU,cAAc;AAClG,aAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjH,WAAK,QAAQ,CAAC,IAAI,UAAU,KAAK;AAAA,IACnC;AAEA,WAAO,UAAU,WAAY;AAC3B,UAAI,OAAO,gBAAgB,YAAY;AACrC,eAAO,IAAI,MAAM,eAAe,eAAe,qBAAqB,gBAAgB,kCAAuC;AAAA,MAC7H,OAAO;AACL,YAAI,QAAQ,mCAAmC,iBAAiB,OAAO,EAAE,OAAO,UAAU,eAAe,UAAU,YAAY;AAC/H,YAAI,iBAAiB;AAAO,iBAAO;AACnC,YAAI,YAAY,MAAM,QAAQ;AAE9B,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,kBAAQ,YAAY,MAAM,QAAQ,CAAC,WAAW,GAAG,eAAe,UAAU,eAAe,MAAM,IAAI,GAAG,EAAE,OAAO,IAAI,CAAC;AACpH,cAAI,iBAAiB;AAAO,mBAAO;AAAA,QACrC;AAEA,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAEA,IAAI,kBAEJ,mCAAmC,OAAO,OAAO;AACjD,IAAI,oBAEJ,mCAAmC,KAAK,MAAM,KAAK;AACnD,IAAI,gBAEJ,mCAAmC,OAAO,KAAK;AAC/C,IAAI,mBAEJ,mCAAmC,OAAO,QAAQ;AAClD,IAAI,yBAEJ,mCAAmC,MAAM,OAAO;AAChD,IAAI,2BAEJ,mCAAmC,KAAK,MAAM,IAAI;AAClD,IAAI,2BAEJ,mCAAmC,MAAM,QAAQ;AAWjD,IAAI,CAAC;AAAW,QAAM,IAAI,MAAM,2CAA2C;AAC3E,IAAI,CAAC;AAAY,QAAM,IAAI,MAAM,0CAA0C;;;AJ9xB3E,IAAAC,eAAiB;AAGjB,IAAI;AAAA;AAAA,EAA2B,SAAU,QAAQ;AAC7C,cAAUC,YAAW,MAAM;AAC3B,aAASA,aAAY;AACjB,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,kBAAkB,CAAC;AACzB,aAAO;AAAA,IACX;AACA,IAAAA,WAAU,UAAU,SAAS,WAAY;AACrC,UAAI,KAAK,KAAK,OAAO,KAAK,GAAG,YAAY,YAAY,GAAG,WAAW,gBAAgB,GAAG,eAAe,SAAS,GAAG,QAAQ,KAAK,GAAG,OAAO,QAAQ,OAAO,SAAS,CAAC,IAAI,IAAIC,aAAY,GAAG,kBAAkB,eAAe,GAAG,cAAc,SAAS,GAAG,QAAQ,QAAQ,GAAG,OAAO,OAAO,GAAG,MAAM,WAAW,GAAG,UAAU,QAAQ,GAAG,OAAO,QAAQ,GAAG,OAAO,SAAS,GAAG,QAAQ,iBAAiB,GAAG,gBAAgB,QAAQ,GAAG,OAAO,WAAW,GAAG,UAAU,UAAU,GAAG,SAAS,UAAU,GAAG,SAAS,QAAQ,GAAG,MAAM,MAAM,GAAG,KAAK,SAAS,GAAG,QAAQ,aAAa,GAAG,YAAY,YAAY,GAAG,WAAW,SAAS,GAAG,QAAQ,WAAW,GAAG,UAAU,MAAM,GAAG,KAAK,YAAY,GAAG,WAAW,YAAY,GAAG,WAAW,eAAe,GAAG,cAAc,gBAAgB,GAAG,eAAe,OAAO,OAAO,IAAI,CAAC,cAAc,aAAa,iBAAiB,UAAU,SAAS,oBAAoB,gBAAgB,UAAU,SAAS,QAAQ,YAAY,SAAS,SAAS,UAAU,kBAAkB,SAAS,YAAY,WAAW,WAAW,QAAQ,OAAO,UAAU,cAAc,aAAa,UAAU,YAAY,OAAO,aAAa,aAAa,gBAAgB,eAAe,CAAC;AACpoC,UAAI,QAAQ;AACR,QAAAA,aAAY;AAAA,MAChB,OACK;AACD,QAAAA,aAAYA,cAAa;AAAA,MAC7B;AACA,UAAI,cAAcA,eAAc,QAAQA,eAAc;AACtD,UAAI,SAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG;AAAA;AAAA,QAExC,WAAW;AAAA,QAAI,UAAU;AAAA,QAAI,SAAS;AAAA,QAAM,QAAQ;AAAA,QAAO,OAAO,OAAO;AAAA,QAAY,WAAW;AAAA,QAAgB,MAAO,UAAU,OAAO,QAAS;AAAA,MAAQ,CAAC;AAE9J,UAAI,OAAO,SAAS,YAAY,OAAO,SAAS,mBAAmB;AAC/D,eAAO,OAAO;AAAA,MAClB;AACA,UAAI,OAAO,WACL,WACA,OAAO,SAAS,QAAQ,SAAS,SAAS,CAAC,OAAG,aAAAC,SAAK,MAAM,OAAO,KAAK,MAAM,GAAG,KAAK,eAAe,CAAC,GAAG;AAAA;AAAA,QAEpG,WAAW;AAAA,QAAM;AAAA,QAAc;AAAA,MAAW,CAAC,CAAC;AACpD,UAAI,aAAa;AAGb,oBAAQ,aAAAA,SAAK,OAAO,CAAC,SAAS,YAAY,SAAS,CAAC;AAAA,MACxD,WACS,OAAO;AACZ,gBAAQ,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG,EAAE,OAAQ,SAAS,MAAM,SAAU,MAAM,CAAC;AAAA,MACpF;AACA,UAAI,OAAO;AACP,gBAAQ,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG,EAAE,WAAW,MAAM,CAAC;AAAA,MAC9D;AACA,UAAI,QAAQ;AACR,gBAAQ,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG,EAAE,eAAe,OAAO,CAAC;AAAA,MACnE;AACA,UAAI,OAAO,iBAAiB;AACxB,YAAI,kBAAkB,OAAO;AAC7B,YAAI,MAAM,gBAAgB;AAC1B,YAAI,MAAM,gBAAgB;AAC1B,YAAI,eAAe,GAAG,GAAG;AACrB,gBAAM,yBAAyB,KAAK,MAAM,OAAO;AAAA,QACrD;AACA,YAAI,eAAe,GAAG,GAAG;AACrB,gBAAM,yBAAyB,KAAK,MAAM,OAAO;AAAA,QACrD;AACA,YAAI,OAAO,QAAQ,aAAa;AAC5B,gBAAM,KAAK,IAAI,MAAM,MAAM,cAAc,CAAC,GAAG,OAAO,KAAK,KAAK,IAAI,SAAU,GAAG;AAAE,mBAAO,EAAE,OAAO,IAAI;AAAA,UAAG,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,QACvH;AACA,YAAI,OAAO,QAAQ,aAAa;AAC5B,gBAAM,KAAK,IAAI,MAAM,MAAM,cAAc,CAAC,GAAG,OAAO,KAAK,KAAK,IAAI,SAAU,GAAG;AAAE,mBAAO,EAAE,OAAO,IAAI;AAAA,UAAG,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,QACvH;AACA,YAAI,aAAa,IAAI,WAAW,KAAK,KAAK,gBAAgB,UAAU,CAAC,WAAW,SAAS,CAAC;AAC1F,YAAI,UAAU,KAAK,OAAO,IAAI;AAC9B,YAAI,eAAe,gBAAgB,MAAM,GAAG;AACxC,oBAAU,yBAAyB,gBAAgB,QAAQ,MAAM,OAAO;AAAA,QAC5E;AACA,YAAI,QAAQ,WAAW,SAAS,OAAO,OAAO,CAAC,EAAE,YAAY;AAC7D,cAAM,aAAa;AAAA,MACvB;AACA,UAAI,cAAc;AACd,eAAO;AAAA,MACX;AACA,aAAQ,eAAAC,QAAM;AAAA,QAAcF;AAAA,QAAW,SAAS,EAAE,SAAS,UAAU,IAAI,UAAU,QAAW,OAAc,WAAW,GAAG,SAAS,GAAG,UAAoB,QAAiB,GAAG,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,SAAS,MAAM,EAAE,UAAU,CAAC;AAAA,QAClR,YAAa,eAAAE,QAAM,cAAc,OAAO,EAAE,YAAY,IAAI,OAAO,SAAS,SAAS,CAAC,GAAG,SAAS,GAAG,EAAE,WAAW,GAAG,eAAe,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,SAAS,EAAE,CAAC,GAAG,MAAM,IAAI,KAAK,CAAC,IAAK;AAAA,QACtO;AAAA,QACA,iBAAiB,cAAc,QAAS,eAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,oBAAoB,EAAE,GAAG,IAAI,IAAM;AAAA,QACtH;AAAA,MAAS;AAAA,IACjB;AACA,IAAAH,WAAU,eAAe;AAAA,MACrB,kBAAkB;AAAA,IACtB;AACA,IAAAA,WAAU,YAAY;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,WAAOA;AAAA,EACX,EAAE,eAAAG,QAAM,SAAS;AAAA;AACjB,IAAI;AAAA;AAAA,EAAmC,SAAU,QAAQ;AACrD,cAAUC,oBAAmB,MAAM;AACnC,aAASA,qBAAoB;AACzB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,mBAAkB,YAAY,cAAc;AAAA,MACxC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,GAAG,OAAO,UAAU,SAAS,GAAG,KAAK;AACrC,IAAAA,qBAAoB,WAAW;AAAA,MAC3B,SAAS;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,MACV,CAAC;AAAA,MACD,WAAW;AAAA,QACP,cAAc;AAAA,MAClB,CAAC;AAAA,MACD,aAAa;AAAA,MACb,YAAY;AAAA,MACZC;AAAA,IACJ,GAAGD,kBAAiB;AACpB,WAAOA;AAAA,EACX,EAAE,SAAS;AAAA;AACX,IAAI;AAAA;AAAA,EAA+B,SAAU,QAAQ;AACjD,cAAUE,gBAAe,MAAM;AAC/B,aAASA,iBAAgB;AACrB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,eAAc,eAAe,SAAS,SAAS,CAAC,GAAG,UAAU,YAAY,GAAG,EAAE,kBAAkB,MAAM,CAAC;AACvG,IAAAA,iBAAgB,WAAW;AAAA,MACvB,SAAS;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,MACV,CAAC;AAAA,MACD,WAAW;AAAA,MACX,YAAY;AAAA,IAChB,GAAGA,cAAa;AAChB,WAAOA;AAAA,EACX,EAAE,SAAS;AAAA;;;AmB5IX,IAAAC,iBAAkB;AAClB,iBAAgB;AAChB,IAAAC,oBAA4B;AAK5B,IAAI;AAAA;AAAA,EAAwC,SAAU,QAAQ;AAC1D,cAAUC,yBAAwB,MAAM;AACxC,aAASA,wBAAuB,OAAO;AACnC,UAAI,QAAQ,OAAO,KAAK,MAAM,KAAK,KAAK;AACxC,YAAM,QAAQ;AAAA,QACV,UAAU;AAAA,QACV,SAAS;AAAA,QACT,eAAe,CAAC;AAAA,MACpB;AACA,YAAM,gBAAgB;AACtB,YAAM,OAAO,MAAM,KAAK,KAAK,KAAK;AAClC,YAAM,QAAQ,MAAM,MAAM,KAAK,KAAK;AACpC,YAAM,cAAc,MAAM,YAAY,KAAK,KAAK;AAChD,YAAM,cAAc,MAAM,YAAY,KAAK,KAAK;AAChD,aAAO;AAAA,IACX;AACA,IAAAA,wBAAuB,UAAU,oBAAoB,WAAY;AAC7D,UAAI,KAAK,KAAK,OAAO,aAAa,GAAG,YAAY,OAAO,GAAG;AAC3D,UAAI,KAAK,cAAc,CAAC,GAAG,SAAS,GAAG,QAAQ,UAAU,GAAG;AAC5D,UAAI,UAAU,eAAe,MAAM,GAAG;AAClC,YAAI,aAAa,yBAAyB,QAAQ,KAAK,MAAM,WAAW,OAAO;AAC/E,aAAK,SAAS;AAAA,UACV,eAAe,KAAK,aAAa,UAAU;AAAA,QAC/C,CAAC;AAAA,MACL,WACS,UAAU,eAAe,QAAQ,IAAI,GAAG;AAC7C,aAAK,aAAa;AAAA,MACtB,YACU,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU,GAAG;AAC7E,aAAK,SAAS;AAAA,UACV,eAAe,KAAK,aAAa,WAAW,OAAO;AAAA,QACvD,CAAC;AAAA,MACL;AAAA,IACJ;AACA,IAAAA,wBAAuB,UAAU,qBAAqB,SAAU,WAAW,WAAW;AAClF,UAAI,IAAI,IAAI,IAAI;AAChB,UAAI,OAAO,KAAK,MAAM;AACtB,UAAI,QAAQ,KAAK;AACjB,WAAK,gBAAgB;AACrB,UAAI,UAAU,SAAS,MAAM,QACzB,UAAU,eAAe,MAAM,cAC/B,UAAU,SAAS,MAAM,MAAM;AAC/B,YAAI,MAAM,WAAW,QAAQ;AACzB,eAAK,gBAAgB,cAAc,UAAU,WAAW,QAAQ,MAAM,WAAW,QAAQ,UAAU,MAAM,MAAM,IAAI;AAAA,QACvH,WACS,MAAM,WAAW,SAAS;AAC/B,eAAK,SAAS;AAAA,YACV,eAAe,KAAK,aAAa,MAAM,WAAW,WAAW,CAAC,CAAC;AAAA,UACnE,CAAC;AAAA,QACL,WACS,QACL,CAAC,KAAK,MAAM,cAAc,WACzB,MAAM,SAAS,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,QAAQ,KACnF,MAAM,SAAS,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,IAAI;AAC3F,cAAI,aAAa,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,eAAe,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAC9J,cAAI,WAAW,CAAC;AAChB,mBAAS,QAAQ,SAAU,MAAM;AAC7B,gBAAIC,SAAQ,YAAY,MAAM,IAAI;AAClC,gBAAI,CAAC,CAAC,SAAS,QAAQA,MAAK,GAAG;AAC3B,uBAAS,KAAKA,MAAK;AAAA,YACvB;AAAA,UACJ,CAAC;AACD,cAAI,SAAS,QAAQ;AACjB,iBAAK,SAAS;AAAA,cACV,eAAe,KAAK,aAAa,QAAQ;AAAA,YAC7C,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,QAAQ,KAAK,MAAM,OAAO,KAAK,MAAM,KAAK,IAAI,IAAI;AACtD,UAAI,YAAY,UAAU,OAAO,UAAU,KAAK,IAAI,IAAI;AACxD,UAAI,UAAU,aACV,KAAK,MAAM,cAAc,UACzB,UAAU,kBAAkB,KAAK,MAAM,eAAe;AACtD,aAAK,SAAS;AAAA,UACV,eAAe,KAAK,aAAa,KAAK,MAAM,aAAa;AAAA,QAC7D,CAAC;AAAA,MACL;AACA,WAAK,iBAAiB,KAAK,aAAa;AAAA,IAC5C;AACA,IAAAD,wBAAuB,UAAU,eAAe,WAAY;AACxD,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,IAAI,KAAK,YAAY,MAAM,KAAK,KAAK;AACzC,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,mBAAK,KAAK,OAAO,MAAM,GAAG,KAAK,aAAa,GAAG,YAAY,OAAO,GAAG;AACrE,kBAAI,CAAC,eAAe,WAAW,QAAQ,IAAI,GAAG;AAC1C,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,oBAAM,aAAa,WAAW,MAAM;AACpC,kBAAI,QAAQ;AACZ,qBAAO,CAAC,GAAa,IAAI,QAAQ,KAAK,IAAI,CAAC;AAAA,YAC/C,KAAK;AACD,oBAAM,GAAG,KAAK;AACd,wBAAW,IAAI,QAAQ,IAAI,KAAK,WAAY,CAAC;AAC7C,mBAAK,SAAS;AAAA,gBACV,eAAe,OAAO,IAAI,QAAQ,KAAK,aAAa,OAAO;AAAA,cAC/D,CAAC;AACD,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,wBAAuB,UAAU,eAAe,SAAU,SAAS,SAAS;AACxE,UAAI,QAAQ;AACZ,UAAI,YAAY,QAAQ;AAAE,kBAAU;AAAA,MAAI;AACxC,UAAI,KAAK,KAAK,OAAO,OAAO,GAAG,MAAM,aAAa,GAAG,YAAY,OAAO,GAAG;AAC3E,UAAI,aAAa,WAAW,YAAY,aAAa,WAAW;AAChE,UAAI,cAAc,QAAQ,OAAO,KAAK,IAAI,MAAM,cAAc,KAAK,IAAI,IAAI;AAC3E,gBAAU,iBAAiB,OAAO;AAElC,gBAAU,QAAQ,IAAI,SAAU,QAAQ;AACpC,eAAO,UAAU,CAAC,CAAC,YAAY,CAAC,MAAM,GAAG,SAAS;AAAA,UAC9C,MAAM,CAAC,cAAc,SAAS,cAAc,OAAO;AAAA,UACnD,WAAW,YAAY,SAAS;AAAA,QACpC,CAAC,EAAE;AACH,eAAO;AAAA,MACX,CAAC;AACD,UAAI,WAAW,UAAU;AACrB,kBAAU,QAAQ,IAAI,SAAU,QAAQ;AAAE,iBAAQ,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,UAAU,YAAY,MAAM,GAAG,EAAE,QAAQ,OAAO,KAAK,IAAI,GAAG,CAAC;AAAA,QAAI,CAAC;AAAA,MACzJ,OACK;AACD,kBAAU,QAAQ,IAAI,SAAU,QAAQ;AAAE,iBAAQ,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,UAAU,MAAM,iBAAiB,QAAQ,WAAW,EAAE,CAAC;AAAA,QAAI,CAAC;AAAA,MACnJ;AACA,aAAO;AAAA,IACX;AACA,IAAAA,wBAAuB,UAAU,mBAAmB,SAAU,QAAQ,UAAU;AAC5E,UAAI,aAAa,KAAK,MAAM;AAK5B,UAAI,UAAU,OAAO,KAAK,GAAG;AACzB,eAAO,UAAU,QAAQ,IAAI,OAAO,SAAS,WAAW;AAAA,MAC5D;AACA,cAAQ,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,gBAAgB,OACrF,OAAO,UAAU,WACjB,OAAO,SAAS;AAAA,IAC1B;AACA,IAAAA,wBAAuB,UAAU,qBAAqB,WAAY;AAC9D,WAAK,MAAM;AAAA,IACf;AACA,IAAAA,wBAAuB,UAAU,OAAO,WAAY;AAChD,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,IAAI,YAAY,QAAQ;AAC5B,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,mBAAK,KAAK,OAAO,aAAa,GAAG,YAAY,SAAS,GAAG;AACzD,kBAAI,EAAE,WAAW,iBAAiB,WAAW;AAAS,uBAAO,CAAC,GAAa,CAAC;AAC5E,kBAAI,EAAE,UAAU,eAAe,MAAM;AAAI,uBAAO,CAAC,GAAa,CAAC;AAC/D,2BAAa,yBAAyB,QAAQ,KAAK,MAAM,WAAW,OAAO;AAC3E,mBAAK,SAAS;AAAA,gBACV,eAAe,KAAK,aAAa,UAAU;AAAA,cAC/C,CAAC;AACD,qBAAO,CAAC,GAAa,CAAC;AAAA,YAC1B,KAAK;AAAG,qBAAO,CAAC,GAAa,KAAK,aAAa,CAAC;AAAA,YAChD,KAAK;AACD,iBAAG,KAAK;AACR,iBAAG,QAAQ;AAAA,YACf,KAAK;AACD,mBAAK,SAAS;AAAA,gBACV,UAAU;AAAA,cACd,CAAC;AACD,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,wBAAuB,UAAU,QAAQ,WAAY;AACjD,WAAK,SAAS;AAAA,QACV,UAAU;AAAA,MACd,CAAC;AAAA,IACL;AACA,IAAAA,wBAAuB,UAAU,cAAc,SAAU,OAAO;AAC5D,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,IAAI,SAAS,MAAM,MAAM,eAAe;AAC5C,YAAI;AACJ,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,mBAAK,KAAK,OAAO,UAAU,GAAG,SAAS,OAAO,GAAG,MAAM,OAAO,GAAG,MAAM,gBAAgB,GAAG;AAC1F,qBAAO,CAAC,GAAa,cAAc,gBAAgB,aAAa,MAAM;AAAA,gBAC9D,YAAY;AAAA,gBACZ,aAAa;AAAA,cACjB,CAAC,CAAC,CAAC;AAAA,YACX,KAAK;AACD,8BAAgB,GAAG,KAAK;AACxB,kBAAI,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,WAAW;AACvF,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,uBAAS,KAAK,CAAC,GACX,GAAG,IAAI,IAAI,OACX,KAAK,OAAO,OAAO,IAAI;AAC3B,mBAAK,MAAM;AACX,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,wBAAuB,UAAU,cAAc,SAAU,OAAO;AAC5D,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,IAAI,MAAM,MAAM,SAAS,eAAe,OAAO;AACnD,YAAI;AACJ,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,mBAAK,KAAK,OAAO,OAAO,GAAG,MAAM,OAAO,GAAG,MAAM,UAAU,GAAG,SAAS,gBAAgB,GAAG;AAC1F,kBAAI,KAAK,IAAI,KAAK,KAAK,IAAI,MAAM,OAAO;AACpC,wBAAQ;AAAA,cACZ,OACK;AACD,wBACK,KAAK,IAAI,SAAK,WAAAE,SAAI,KAAK,IAAI,EAAE,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,KAAK,GAAG,KAAM;AAAA,cACzE;AACA,qBAAO,CAAC,GAAa,cAAc,gBAAgB,aAAa,MAAM;AAAA,gBAC9D,YAAY;AAAA,gBACZ,aAAa;AAAA,cACjB,CAAC,CAAC,CAAC;AAAA,YACX,KAAK;AACD,8BAAgB,GAAG,KAAK;AACxB,kBAAI,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,WAAW;AACvF,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,uBAAS,KAAK,CAAC,GACX,GAAG,IAAI,IAAI,OACX,GAAG;AACP,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAF,wBAAuB,UAAU,cAAc,WAAY;AACvD,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,IAAI,MAAM,eAAe,MAAM,SAAS;AAC5C,YAAI;AACJ,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,mBAAK,KAAK,OAAO,OAAO,GAAG,MAAM,gBAAgB,GAAG,eAAe,OAAO,GAAG,MAAM,UAAU,GAAG;AAChG,qBAAO,CAAC,GAAa,cAAc,gBAAgB,aAAa,MAAM;AAAA,gBAC9D,YAAY;AAAA,gBACZ,aAAa;AAAA,cACjB,CAAC,CAAC,CAAC;AAAA,YACX,KAAK;AACD,8BAAgB,GAAG,KAAK;AACxB,kBAAI,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,WAAW;AACvF,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,uBAAS,KAAK,CAAC,GACX,GAAG,IAAI,IAAI,QACX,KAAK,OAAO,OAAO,IAAI;AAC3B,mBAAK,MAAM;AACX,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,wBAAuB,UAAU,eAAe,SAAU,SAAS;AAC/D,UAAI,gBAAgB,KAAK,MAAM;AAC/B,WAAK,SAAS;AAAA,QACV;AAAA,QACA,eAAe,KAAK,aAAa,eAAe,OAAO;AAAA,MAC3D,CAAC;AAAA,IACL;AACA,IAAAA,wBAAuB,UAAU,SAAS,WAAY;AAClD,UAAI,QAAQ;AACZ,UAAI,IAAI,IAAI,IAAI;AAChB,UAAI,KAAK,KAAK,OAAO,WAAW,GAAG,UAAU,gBAAgB,GAAG;AAChE,UAAI,KAAK,KAAK,OAAO,OAAO,GAAG,MAAM,OAAO,GAAG,MAAM,aAAa,GAAG,YAAY,mBAAmB,GAAG,kBAAkB,KAAK,GAAG,aAAa,KAAK,GAAG,YAAY,KAAK,GAAG;AAC1K,UAAI,gBAAgB,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,iBAAiB,CAAC;AACzG,aAAQ,eAAAG,QAAM;AAAA,QAAc;AAAA,QAAQ,EAAE,WAAW,GAAG,GAAG,OAAO,IAAI,qBAAqB,GAAG,QAAQ,OAAO,KAAK,IAAI,MAAM,cAAc,cAAc,EAAE,EAAE;AAAA,QACpJ,eAAAA,QAAM;AAAA,UAAc;AAAA,UAAQ,EAAE,SAAS,KAAK,KAAK;AAAA,UAC7C,eAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,iBAAiB,WAAW,OAAO,CAAC;AAAA,QAAC;AAAA,QAC3E,WAAY,eAAAA,QAAM;AAAA,UAAc;AAAA,UAAS,EAAE,WAAW,oBAAqB,WAAY;AAAE,uBAAO,+BAAY,KAAK;AAAA,UAAG,GAAI,WAAW,+CAA+C,QAAQ,mBAAmB,WAAY;AAAE,uBAAO,+BAAY,KAAK,EAAE;AAAA,UAAY,IAAI,MAAM,MAAM,KAAK;AAAA,UAClR,eAAAA,QAAM,cAAc,WAAS,EAAE,aAAa,IAAI,QAAQ,KAAK,OAAO,WAAW,GAAG,GAAG,OAAO,IAAI,yBAAyB,GAAG,WAAW,SAAS,GAAG,SAAS,KAAK,GAAG,iBAAiB,cAAc,SAAS,IAAK,eAAAA,QAAM;AAAA,YAAc,eAAAA,QAAM;AAAA,YAAU;AAAA,aAChP,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,cAAe,eAAAA,QAAM,cAAc,aAAW,EAAE,WAAW,GAAG,qCAAqC,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,SAAS,GAAG,OAAO,KAAK,aAAa,UAAU,QAAQ,OAAO,SAAS,KAAK,OAAO,UAAU,KAAK,aAAa,aAAa,QAAQ,OAAO,SAAS,KAAK,OAAO,YAAY,KAAK,aAAa,eAAe,QAAQ,OAAO,SAAS,KAAK,MAAM,kBAAkB,aAAa,kBAAkB,aAAa,aAAa,aAAa,cAAc,IAAI,QAAQ,KAAK,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,KAAK,IAAI,UAAU,KAAK,cAAc;AAAA;AAAA,cAA6B;AAAA,cAAK,CAAC,IAAK;AAAA,YACrtB,eAAAA,QAAM;AAAA,cAAc;AAAA,cAAM,EAAE,WAAW,GAAG,eAAe,EAAE;AAAA,cACvD,CAAC,WAAW,WACN,cAAc,IAAI,SAAU,QAAQ,OAAO;AACzC,uBAAO,OAAO,WAAY,eAAAA,QAAM,cAAc,MAAM,EAAE,KAAK,OAAO,WAAW,GAAG;AAAA,kBACxE,aAAa,OAAO;AAAA,gBACxB,CAAC,GAAG,SAAS,MAAM,YAAY,KAAK,OAAO,OAAO,KAAK,EAAE,GAAG,OAAO,KAAK;AAAA,cAChF,CAAC,IACC,cAAc,IAAI,SAAU,QAAQ,OAAO;AACzC,uBAAO,OAAO,WAAY,eAAAA,QAAM;AAAA,kBAAc;AAAA,kBAAM,EAAE,KAAK,MAAM;AAAA,kBAC7D,eAAAA,QAAM,cAAc,YAAU,EAAE,MAAM,MAAM,aAAa,IAAI,UAAU,MAAM,YAAY,KAAK,OAAO,OAAO,KAAK,GAAG,SAAS,OAAO,SAAS,GAAG,OAAO,KAAK;AAAA,gBAAC;AAAA,cACrK,CAAC;AAAA,cACL,cAAc,KAAK,SAAU,MAAM;AAAE,uBAAO,KAAK;AAAA,cAAU,CAAC,IAAK,eAAAA,QAAM,cAAc,MAAM,EAAE,KAAK,uBAAuB,SAAS,KAAK,YAAY,KAAK,IAAI,EAAE,GAAG,GAAG,OAAO,CAAC,IAAK;AAAA,YAAI;AAAA,UAAC,IAAK,IAAI;AAAA,QAAC,IAAK;AAAA,MAAI;AAAA,IAC7N;AACA,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGH,wBAAuB,WAAW,gBAAgB,IAAI;AACzD,WAAOA;AAAA,EACX,EAAE,eAAAG,QAAM,SAAS;AAAA;;;ACjTjB,IAAAC,iBAAkB;AAIlB,SAAS,uBAAuB,IAAI;AAChC,MAAI,QAAQ;AACZ,MAAI,aAAa,GAAG,YAAY,OAAO,GAAG,MAAM,QAAQ,GAAG,OAAO,UAAU,GAAG,SAAS,OAAO,GAAG,MAAM,gBAAgB,GAAG,eAAe,WAAW,GAAG,UAAU,KAAK,GAAG,YAAY,KAAK,GAAG,WAAW,KAAK,GAAG,aAAa,mBAAmB,GAAG,kBAAkB,SAAS,GAAG,QAAQ,gBAAgB,GAAG;AAC7S,MAAI,MAAM,eAAAC,QAAM,UAAU;AAC1B,MAAI,KAAK,OAAO,eAAAA,QAAM,QAAQ,WAAY;AACtC,QAAI;AACJ,QAAIC,aAAY,CAAC;AACjB,QAAI,eAAe,MAAM;AACrB,eAAS;AAAA,QACL,OAAO;AAAA,QACP,MAAM;AAAA,UACF;AAAA,YACI,MAAM;AAAA,YACN;AAAA,YACA,aAAa;AAAA,YACb,WAAW;AAAA,UACf;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,WACS,YAAY;AACjB,UAAI,CAAC,WAAW,SACX,WAAW,QAAQ,WAAW,QAAQ,WAAW,WAAW;AAE7D,iBAAS,SAAS,SAAS,EAAE,OAAO,GAAG,GAAG,UAAU,GAAG,EAAE,MAAM,MAAM,QAAQ,WAAW,IAAI,IAClF,WAAW,KAAK,OAAO,IACvB,OAAU,CAAC;AAAA,MACzB,OACK;AACD,iBAAS;AAAA,UACL,OAAO;AAAA,UACP,WAAW,WAAW;AAAA,UACtB,MAAM;AAAA,YACF,SAAS,EAAE,MAAM,WAAW,QAAQ,cAAc,MAAM,WAAW,QAAQ,MAAM,aAAa,MAAM,GAAG,UAAU;AAAA,UACrH;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,aAAS,cAAcC,SAAQ;AAC3B,YAAM,QAAQA,QAAO,IAAI,KACrBA,QAAO,KAAK,QAAQ,SAAU,MAAM;AAChC,aAAK,QAAQD,WAAU,KAAK,KAAK,IAAI;AACrC,aAAK,aACD,OAAO,KAAK,cAAc,YAC1BA,WAAU,KAAK,KAAK,SAAS;AACjC,sBAAc,IAAI;AAAA,MACtB,CAAC;AAAA,IACT;AACA,QAAI,QAAQ;AAER,oBAAc,MAAM;AACpB,eAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,MAAM,QAAQ,kBAAkB,OAAO,oBAAoB,OAAO,SAAS;AAAA,QAC7G;AAAA,UACI,MAAM;AAAA,UACN,OAAO,GAAG,OAAO;AAAA,UACjB,YAAY;AAAA,QAChB;AAAA,QACA;AAAA,UACI,MAAM;AAAA,UACN,OAAO,GAAG,QAAQ;AAAA,UAClB,YAAY;AAAA,QAChB;AAAA,QACA;AAAA,UACI,OAAO,GAAG,QAAQ;AAAA,UAClB,MAAM;AAAA,UACN,SAAS;AAAA,QACb;AAAA,MACJ,EAAE,CAAC;AAAA,IACX;AACA,WAAO,CAAC,UAAU,SAASA,UAAS;AAAA,EACxC,GAAG,CAAC,YAAY,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC;AACvE,MAAI,KAAK,OAAO,eAAAD,QAAM,SAAS,KAAK,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC;AAC/E,MAAI,OAAO,eAAAA,QAAM,YAAY,WAAY;AAAE,WAAO,YAAY,IAAI;AAAA,EAAG,GAAG,CAAC,CAAC;AAC1E,MAAI,QAAQ,eAAAA,QAAM,YAAY,WAAY;AAAE,WAAO,YAAY,KAAK;AAAA,EAAG,GAAG,CAAC,CAAC;AAC5E,MAAI,eAAe,eAAAA,QAAM,YAAY,SAAU,QAAQ;AAAE,WAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AACzG,UAAI;AACJ,aAAO,YAAY,MAAM,SAAUG,KAAI;AACnC,gBAAQA,IAAG,OAAO;AAAA,UACd,KAAK;AAAG,mBAAO,CAAC,GAAa,cAAc,gBAAgB,aAAa,MAAM;AAAA,cACtE,YAAY;AAAA,cACZ,aAAa;AAAA,YACjB,CAAC,CAAC,CAAC;AAAA,UACP,KAAK;AACD,4BAAgBA,IAAG,KAAK;AACxB,gBAAI,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,WAAW;AACvF,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,YACxB;AACA,kBAAM;AACN,oBAAQ,MAAM;AACd,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAY;AAAA,QAC5B;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EAAG,GAAG,CAAC,CAAC;AACT,MAAI,eAAe,eAAAH,QAAM,YAAY,SAAU,GAAG,QAAQ,KAAK;AAC3D,QAAI,OAAO,eAAe,YAAY,OAAO,eAAe,SAAS;AACjE,YAAM;AACN;AAAA,IACJ;AACA,QAAI,OAAO,eAAe,SAAS;AAC/B,YAAM;AACN,kBAAY;AACZ;AAAA,IACJ;AACA,gBAAY,SAAS,GAAG,QAAQ,GAAG;AAAA,EACvC,GAAG,CAAC,CAAC;AACL,MAAI,cAAc,eAAAA,QAAM,YAAY,WAAY;AAC5C,QAAI,SAAS,SAAS,CAAC,GAAG,IAAI;AAE9B,cAAU,QAAQ,SAAU,KAAK;AAAE,aAAO,YAAY,QAAQ,KAAK,MAAS;AAAA,IAAG,CAAC;AAChF,YAAQ,MAAM;AAAA,EAClB,GAAG,CAAC,IAAI,CAAC;AACT,MAAI,WAAW,eAAAA,QAAM,QAAQ,WAAY;AAErC,WAAO,UAAU,KAAK,SAAU,KAAK;AAAE,aAAO,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,GAAG;AAAA,IAAG,CAAC;AAAA,EAC1G,GAAG,CAAC,IAAI,CAAC;AACT,SAAQ,eAAAA,QAAM;AAAA,IAAc;AAAA,IAAQ,SAAS,EAAE,KAAU,WAAW,GAAG,GAAG,OAAO,IAAI,qBAAqB,GAAG,WAAW,cAAc,IAAI,WAAW,cAAc,EAAE,EAAE,GAAG,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,UAAU,CAAC;AAAA,IAC7P,eAAAA,QAAM;AAAA,MAAc;AAAA,MAAQ,EAAE,SAAS,KAAK;AAAA,MACxC,eAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,UAAU,WAAW,OAAO,CAAC;AAAA,IAAC;AAAA,IACpE,WAAY,eAAAA,QAAM;AAAA,MAAc;AAAA,MAAS,EAAE,WAAW,oBAAqB,WAAY;AAAE,eAAO,IAAI;AAAA,MAAS,GAAI,WAAW,+CAA+C,QAAQ,mBAAmB,WAAY;AAAE,YAAIG;AAAI,gBAAQA,MAAK,IAAI,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG;AAAA,MAAY,IAAI,MAAM,MAAM,KAAK;AAAA,MAC3T,eAAAH,QAAM,cAAc,WAAS,EAAE,aAAa,IAAI,QAAQ,OAAO,WAAW,GAAG,GAAG,OAAO,IAAI,yBAAyB,GAAG,WAAW,SAAS,GAAG,SAAS,KAAK,GAAG,OAAO,qBAAqB,YAAY;AAAA,QACnM;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV,UAAU;AAAA,MACd,CAAC,CAAC;AAAA,IAAC,IAAK;AAAA,EAAI;AACxB;;;ACjIA,IAAAI,iBAAkB;;;ACAlB,IAAAC,iBAAkB;;;ACClB,IAAAC,iBAAkB;AAKlB,IAAI;AAAA;AAAA,EAA0B,SAAU,QAAQ;AAC5C,cAAUC,WAAU,MAAM;AAC1B,aAASA,YAAW;AAChB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,UAAS,UAAU,mBAAmB,SAAU,GAAG;AAC/C,UAAI,KAAK,KAAK,OAAO,OAAO,GAAG,MAAM,YAAY,GAAG,WAAW,kBAAkB,GAAG;AACpF,0BAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,MAAM,SAAS;AAAA,IACrG;AACA,IAAAA,UAAS,UAAU,mBAAmB,SAAU,GAAG;AAC/C,UAAI,KAAK,KAAK,OAAO,OAAO,GAAG,MAAM,YAAY,GAAG,WAAW,kBAAkB,GAAG;AACpF,0BAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,MAAM,SAAS;AAAA,IACrG;AAEA,IAAAA,UAAS,UAAU,kBAAkB,SAAU,GAAG;AAC9C,UAAI;AACJ,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,UAAU,IAAI,YAAY,UAAU,MAAM,WAAW,SAAS,YAAY,kBAAkB;AAChG,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,eAAe,CAAC,GAAG;AACnB,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,0BAAY,KAAK,EAAE,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG;AACxE,gBAAE,eAAe;AACjB,gBAAE,gBAAgB;AAClB,mBAAK,KAAK,OAAO,aAAa,GAAG,YAAY,WAAW,GAAG,UAAU,OAAO,GAAG,MAAM,YAAY,GAAG,WAAW,UAAU,GAAG,SAAS,aAAa,GAAG,YAAY,mBAAmB,GAAG;AACvL,qBAAO,CAAC,GAAc,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,MAAM,SAAS,CAAE;AAAA,YAC9G,KAAK;AACD,8BAAgB,GAAG,KAAK;AACxB,kBAAI,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,WAAW;AACvF,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,kBAAI,YAAY;AACZ,4BAAY,SAAS,GAAG,YAAY,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,MAAM;AAAA,cAE/F,OACK;AACD,oBAAI,KAAK,aAAa,KAAK,0BAA0B,kBAAkB;AACnE,8BAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,MAAM,CAAC,KAAK,SAAS,QAAQ;AAAA,gBAC3F;AAAA,cACJ;AACA,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,UAAS,UAAU,gBAAgB,SAAU,GAAG;AAC5C,UAAI,KAAK,KAAK,OAAO,OAAO,GAAG,MAAM,YAAY,GAAG,WAAW,eAAe,GAAG;AACjF,uBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,MAAM,SAAS;AAAA,IAC5F;AACA,IAAAA,UAAS,UAAU,eAAe,SAAU,GAAG,QAAQ,KAAK;AACxD,UAAI,KAAK,KAAK,OAAO,WAAW,GAAG,UAAU,OAAO,GAAG;AACvD,aAAO,YAAY,SAAS,GAAG,QAAQ,OAAO,KAAK,MAAM;AAAA,IAC7D;AACA,IAAAA,UAAS,UAAU,oBAAoB,SAAU,QAAQ,iBAAiB,cAAc,SAAS;AAC7F,UAAI,KAAK,KAAK,OAAO,gBAAgB,GAAG,eAAe,OAAO,GAAG;AACjE,uBACI,cAAc,MAAM,QAAQ,iBAAiB,cAAc,OAAO;AAAA,IAC1E;AACA,IAAAA,UAAS,UAAU,eAAe,SAAU,OAAO,MAAM,QAAQ,gBAAgB;AAC7E,UAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;AACnC;AAAA,MACJ;AACA,UAAI,KAAK,KAAK,OAAO,OAAO,GAAG,MAAM,gBAAgB,GAAG;AACxD,UAAI,OAAO,CAAC;AACZ,UAAI,UAAU,UAAU,IAAI;AAE5B,UAAI,QAAQ,SAAS,GAAG;AACpB,aAAK,QAAQ,CAAC,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC;AAAA,MACzD;AACA,kBAAY,MAAM,MAAM,KAAK;AAC7B,wBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,MAAM,MAAM,QAAQ,cAAc;AAAA,IAClH;AACA,IAAAA,UAAS,UAAU,SAAS,WAAY;AACpC,UAAI,IAAI;AACR,UAAI,QAAQ;AACZ,UAAI;AACJ,UAAI,KAAK,KAAK,OAAO,gBAAgB,GAAG,eAAe,YAAY,GAAG,WAAW,OAAO,GAAG,MAAM,UAAU,GAAG,SAAS,aAAa,GAAG,YAAY,WAAW,GAAG,UAAU,eAAe,GAAG,cAAc,wBAAwB,GAAG,uBAAuB,kBAAkB,GAAG,iBAAiB,eAAe,GAAG,cAAc,mBAAmB,GAAG,kBAAkB,KAAK,GAAG,aAAa,SAAS,GAAG,QAAQ,KAAK,GAAG,YAAY,SAAS,GAAG,QAAQ,aAAa,GAAG,YAAY,UAAU,GAAG,SAAS,WAAW,GAAG,UAAU,iBAAiB,GAAG,gBAAgB,KAAK,GAAG,IAAI,WAAW,GAAG,UAAU,UAAU,GAAG,SAAS,UAAU,GAAG,SAAS,WAAW,GAAG,UAAU,QAAQ,GAAG,OAAO,QAAQ,GAAG,OAAO,aAAa,GAAG,YAAY,UAAU,GAAG,SAAS,eAAe,GAAG,cAAc,QAAQ,GAAG,OAAO,WAAW,GAAG,UAAU,gBAAgB,GAAG,eAAe,UAAU,GAAG,SAAS,OAAO,OAAO,IAAI,CAAC,iBAAiB,aAAa,QAAQ,WAAW,cAAc,YAAY,gBAAgB,yBAAyB,mBAAmB,gBAAgB,oBAAoB,eAAe,UAAU,cAAc,UAAU,cAAc,WAAW,YAAY,kBAAkB,MAAM,YAAY,WAAW,WAAW,YAAY,SAAS,SAAS,cAAc,WAAW,gBAAgB,SAAS,YAAY,iBAAiB,SAAS,CAAC;AAC/yC,UAAI,cAAc;AACd,YAAI,CAAC,UAAU;AACX,iBAAO;AAAA,QACX;AACA,eAAQ,eAAAC,QAAM;AAAA,UAAc;AAAA,UAAM,EAAE,KAAK,OAAO,WAAW,IAAI,cAAc,UAAU,SAAS,oBAAoB,eAAe,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YACnL,KAAK,kBACL,QAAW,eAAe,KAAK,eAAe,cAAc,KAAK,kBAAkB,cAAc,KAAK,kBAAkB,WAAW,GAAG,kBAAkB,gBAAgB,KAAK;AAAA,YAC3K,cAAc;AAAA,YACd,cAAc;AAAA,YACd,eAAe;AAAA,YACf,YAAY;AAAA,UAChB,GACA,GAAG,yBAAyB,IAAI,YAChC,GAAG,eAAe,IAAI,YAAY,MAAM,GACxC,GAAG,gBAAgB,IAAI,YAAY,MAAM,GACzC,GAAG,EAAE;AAAA,UACT,eAAAA,QAAM;AAAA,YAAc;AAAA,YAAM,EAAE,WAAW,GAAG,YAAY,GAAG,SAAS,gBAAgB;AAAA,YAC9E,eAAAA,QAAM;AAAA,cAAc;AAAA,cAAS,EAAE,WAAW,GAAG,iBAAiB,EAAE;AAAA,cAC5D,eAAAA,QAAM,cAAc,SAAS,MAAM,wBAC7B,QAAQ,IAAI,SAAU,QAAQ;AAAE,uBAAQ,eAAAA,QAAM;AAAA,kBAAc;AAAA,kBAAM,EAAE,KAAK,OAAO,GAAG;AAAA,kBACjF,OAAO,UAAU,QAAQ,eAAAA,QAAM,cAAc,MAAM,IAAI,IAAI;AAAA,kBAC3D,eAAAA,QAAM,cAAc,MAAM,IAAI;AAAA,gBAAC;AAAA,cAAI,CAAC,IACtC,QAAQ,IAAI,SAAU,QAAQ;AAAE,uBAAQ,eAAAA,QAAM;AAAA,kBAAc;AAAA,kBAAM,EAAE,KAAK,OAAO,GAAG;AAAA,kBACjF,OAAO,UAAU,QAAS,eAAAA,QAAM,cAAc,MAAM,MAAM,OAAO,GAAG,OAAO,YAAY,EAAE,OAAO,WAAW,GAAG,EAAE,OAAO,OAAO,OAAO,MAAM,GAAG,OAAO,KAAK,CAAC,IAAK;AAAA,kBAChK,UAAW,WAAW,GAAG,OAAO,YAAY,EAAE,OAAO,WAAW,GAAG,EAAE,OAAO,OAAO,KAAK,GAAG,QAAQ,MAAM,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG,EAAE,OAAO,MAAM,UAAU,WAAW,cAAc,KAAK,MAAM,UAAU,OAAO,OAAO,SAAkB,KAAK,OAAO,IAAI,UAAU,MAAM,cAAc,eAAe,MAAM,mBAAmB,UAAU,MAAM,aAAa,CAAC,CAAC,IAAM,eAAAA,QAAM;AAAA,oBAAc;AAAA,oBAAM,EAAE,KAAK,OAAO,GAAG;AAAA,oBAC/Y,eAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,kBAAkB,EAAE,GAAG,GAAQ;AAAA,kBAAC;AAAA,gBAAE;AAAA,cAAI,CAAC,CAAC;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAClH;AACA,UAAI,UAAU,CAAC,OAAO,UAAU;AAC5B,eAAO;AAAA,MACX;AACA,aAAQ,eAAAA,QAAM,cAAc,MAAM,SAAS,EAAE,KAAK,OAAO,SAAS,oBAAoB,eAAe,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YACrJ,KAAK,kBACL,QAAW,eAAe,KAAK,eAAe,cAAc,KAAK,kBAAkB,cAAc,KAAK,kBAAkB,cAAc,UAAU,IAAI,WAAW,QAAW,WAAW,IAAI,WAAW,GAAG,kBAAkB,gBAAgB,KAAK;AAAA,QAC5O,cAAc;AAAA,QACd,cAAc;AAAA,QACd,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,eAAe,YAAY;AAAA,QAC3B,iBAAiB;AAAA,MACrB,GACA,GAAG,yBAAyB,IAAI,YAChC,GAAG,eAAe,IAAI,YAAY,MAAM,GACxC,GAAG,gBAAgB,IAAI,YAAY,MAAM,GACzC,KAAK,aAAa,OAAO,OAAO,IAAI,CAAC,EAAE,IAAI,KAAK,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,OAAO,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,CAAC,GAAG,QAAQ,IAAI,SAAU,QAAQ;AAC9N,eAAO,UAAW,WAAW,GAAG,OAAO,WAAW,GAAG,EAAE,OAAO,OAAO,KAAK,GAAG,QAAQ,MAAM,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG,EAAE,UAAU,WAAW,UAAU,OAAO,OAAO,cAAc,KAAK,MAAM,SAAkB,KAAK,OAAO,IAAI,UAAU,MAAM,cAAc,eAAe,MAAM,mBAAmB,UAAU,MAAM,aAAa,CAAC,CAAC,IAAK,OAAO,QAAQ,KAAK,SAAS,OAAO,IAAI,MAAM,IAAI,OAAQ,eAAAA,QAAM;AAAA,UAAc;AAAA,UAAM,EAAE,KAAK,OAAO,GAAG;AAAA,UAC7a,eAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,kBAAkB,EAAE,GAAG,GAAQ;AAAA,QAAC;AAAA,MACnF,CAAC,CAAC;AAAA,IACN;AACA,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGD,UAAS,WAAW,oBAAoB,IAAI;AAC/C,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,UAAS,WAAW,oBAAoB,IAAI;AAC/C,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,OAAO;AAAA,IAC3C,GAAGA,UAAS,WAAW,mBAAmB,IAAI;AAC9C,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,UAAS,WAAW,iBAAiB,IAAI;AAC5C,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,QAAQ,QAAQ,MAAM,CAAC;AAAA,MACxD,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,UAAS,WAAW,gBAAgB,IAAI;AAC3C,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,QAAQ,SAAS,SAAS,MAAM,CAAC;AAAA,MAClE,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,UAAS,WAAW,qBAAqB,IAAI;AAChD,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,QAAQ,QAAQ,SAAS,OAAO,CAAC;AAAA,MAClE,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,UAAS,WAAW,gBAAgB,IAAI;AAC3C,WAAOA;AAAA,EACX,EAAE,eAAAC,QAAM,aAAa;AAAA;AAErB,IAAI,aAAaC,UAAS,SAAU,OAAO;AACvC,MAAI,OAAO,MAAM;AACjB,MAAI,SAAS,MAAM;AACnB,MAAI,QAAQ,MAAM;AAClB,MAAI,UAAU,MAAM;AACpB,MAAI,qBAAqB,MAAM,sBAC3B,QAAQ,KAAK,SAAUC,OAAM;AAAE,WAAOA,MAAK,SAAS;AAAA,EAAoB,CAAC;AAC7E,MAAI,KAAK,UAAU;AAAA,IACf,WAAW;AAAA,IACX,UAAU,KAAK;AAAA,IACf,MAAM,CAAC,KAAK;AAAA,EAChB,CAAC,GAAG,MAAM,GAAG,KAAK,SAAS,GAAG;AAC9B,SAAQ,eAAAF,QAAM,cAAc,UAAU,SAAS,CAAC,GAAG,OAAO;AAAA,IAAE,OAAO;AAAA,IAAK,UAAU,KAAK;AAAA,IAAU,gBAAgB,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO;AAAA,IAAU,IAAI,KAAK;AAAA,IAAI,UAAU,KAAK;AAAA,IAAU,SAAS,KAAK;AAAA,IAAS,SAAS,KAAK;AAAA,IAAS,SAAS,KAAK;AAAA,IAAS,UAAU,KAAK;AAAA,IAAU,OAAO,KAAK;AAAA,IAAO,OAAO,KAAK;AAAA,IAAO,YAAY,KAAK;AAAA,IAAY,cAAc,KAAK;AAAA,IAAc,SAAS,KAAK;AAAA,IAAS,OAAO,KAAK;AAAA;AAAA;AAAA,IAGzb,MAAM,qBAAqB,KAAK,SAAS,KAAK;AAAA,IAAM,SAAS,KAAK,aAAa,KAAK,YAAY,SAAS;AAAA,IAAM,UAAU,MAAM;AAAA,EAAS,CAAC,CAAC;AAClJ,CAAC;;;AD9LD,IAAI;AAAA;AAAA,EAA2B,SAAU,QAAQ;AAC7C,cAAUG,YAAW,MAAM;AAC3B,aAASA,aAAY;AACjB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,WAAU,UAAU,oBAAoB,WAAY;AAChD,WAAK,MAAM,MAAM,eAAe;AAAA,IACpC;AACA,IAAAA,WAAU,UAAU,gBAAgB,SAAU,SAAS;AACnD,UAAI;AACJ,cAAQ,KAAK,KAAK,MAAM,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,OAAO,OAAO,OAAO,CAAC;AAAA,IAClH;AACA,IAAAA,WAAU,UAAU,aAAa,SAAU,MAAM,SAAS,UAAU,WAAW;AAC3E,UAAI,QAAQ;AACZ,UAAI,YAAY,QAAQ;AAAE,kBAAU,KAAK,MAAM;AAAA,MAAS;AACxD,UAAI,aAAa,QAAQ;AAAE,mBAAW,CAAC;AAAA,MAAG;AAC1C,UAAI,KAAK,KAAK,OAAO,eAAe,GAAG,cAAc,mBAAmB,GAAG,kBAAkB,WAAW,GAAG,UAAU,iBAAiB,GAAG,gBAAgB,mBAAmB,GAAG,kBAAkB,KAAK,GAAG,YAAY,SAAS,GAAG,QAAQ,aAAa,GAAG,YAAY,UAAU,GAAG,SAAS,gBAAgB,GAAG,eAAe,WAAW,GAAG,UAAU,wBAAwB,GAAG,uBAAuB,kBAAkB,GAAG,iBAAiB,aAAa,GAAG,YAAY,aAAa,GAAG,YAAY,eAAe,GAAG,cAAc,kBAAkB,GAAG,iBAAiB,kBAAkB,GAAG,iBAAiB,QAAQ,GAAG;AAC5lB,aAAO,KAAK,IAAI,SAAU,MAAM,UAAU;AACtC,YAAI,YAAY,iBAAiB,eAAe,MAAM,QAAQ,IAAI;AAClE,YAAI,UAAU,GAAG,OAAO,YAAY,YAAY,MAAM,EAAE,EAAE,OAAO,QAAQ;AACzE,YAAI,OAAO;AAAA,UACP,eAAAC,QAAM,cAAc,YAAU,SAAS,CAAC,GAAG,WAAW;AAAA,YAAE,eAAe,MAAM;AAAA,YAAe;AAAA,YAAc;AAAA,YAAwB,YAAY;AAAA,YAAI;AAAA,YAAoC,KAAK,KAAK;AAAA,YAAI,WAAW;AAAA,YAAU;AAAA,YAAkB;AAAA,YAAY,eAAe,GAAG,mBAC/P,OAAO,kBAAkB,KAAK,MAAM,IACpC,cAAc;AAAA,cAChB,WAAW,KAAK,QAAQ,KACpB,aAAa,KAAK,SAAS,KAC3B,CAAC,KAAK,SAAS;AAAA,YACvB,CAAC;AAAA,YAAG;AAAA,YAAkB;AAAA,YAAwB;AAAA,YAAgB;AAAA,YAAoB;AAAA;AAAA,YAElF;AAAA,YAA8B;AAAA,YAAwB;AAAA,YAA4B;AAAA,YAAkC;AAAA,UAAiC,GAAG,QAAQ,CAAC;AAAA,QACzK;AACA,YAAI,YAAY,gBAAgB,QAAQ;AACpC,cAAI,KAAK,UAAU,GAAG;AAClB,iBAAK,KAAK,eAAAA,QAAM,cAAc,YAAU,SAAS,CAAC,GAAG,WAAW,EAAE,OAAc,YAAwB,YAAY,IAAI,kBAAoC,KAAK,QAAQ,OAAO,KAAK,EAAE,GAAG,WAAW,UAAU,SAAkB,MAAY,eAAe,GAAG,mBACrP,OAAO,kBAAkB,KAAK,MAAM,IACpC,YAAY,GAAG,SAAS,iBAAiB,YAAwB,QAAgB,UAAoB,SAAkB,YAAwB,cAA4B,iBAAkC,iBAAkC,cAAc,MAAM,iBAAiB,QAAQ,QAAQ,eAA8B,sBAA6C,GAAG,UAAU,EAAE,eAAe,MAAM,cAAc,CAAC,CAAC,CAAC;AAAA,UAClb;AAAA,QACJ,WACS,KAAK,SAAS,UAAU,KAAK,UAAU;AAE5C,eAAK,KAAK,MAAM,MAAM,cAAc,CAAC,GAAG,OAAO,MAAM,WAAW,KAAK,UAAU,SAAS,SAAS,SAAS,CAAC,GAAG,QAAQ,GAAG,EAAE,QAAQ,KAAK,CAAC,GAAG,OAAO,CAAC,GAAG,KAAK,CAAC;AAAA,QACjK;AACA,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AACA,IAAAD,WAAU,UAAU,mBAAmB,SAAUE,WAAU,OAAO,UAAU;AACxE,UAAI,IAAI;AACR,UAAI,KAAK,KAAK,OAAO,UAAU,GAAG,SAAS,SAAS,GAAG,QAAQ,OAAO,GAAG,MAAM,KAAK,GAAG,YAAY,OAAO,GAAG,MAAM,qBAAqB,GAAG,oBAAoB,oBAAoB,GAAG,mBAAmB,QAAQ,GAAG;AACpN,UAAI,EAAE,MAAM,QAAQ,KAAK,KAAK,MAAM,SAAS;AACzC,eAAO;AAAA,MACX;AACA,UAAIC,UAAS;AAEb,UAAI,SAAS,MACR,IAAI,SAAUC,OAAM,OAAO;AAC5B,YAAI,UAAU,CAACD,UAAS,KAAK;AAC7B,YAAIC,MAAK,UAAU,GAAG;AAClB,mBAASC,KAAI,GAAGA,KAAID,MAAK,SAASC,MAAK;AACnC,oBAAQ,KAAKF,UAAS,QAAQE,EAAC;AAAA,UACnC;AACA,UAAAF,WAAUC,MAAK,UAAU;AAAA,QAC7B;AACA,YAAIE,kBAAiB,QAChB,IAAI,SAAU,KAAK;AAAE,iBAAO,QAAQ,KAAK,SAAU,KAAK;AAAE,mBAAO,IAAI,aAAa;AAAA,UAAK,CAAC;AAAA,QAAG,CAAC,EAC5F,OAAO,SAAUF,OAAM;AAAE,iBAAOA;AAAA,QAAM,CAAC;AAC5C,eAAO,SAAS,SAAS,CAAC,GAAGA,KAAI,GAAG,EAAE,SAASE,gBAAe,QAAQ,aAAaA,gBAAe,CAAC,GAAG,YAAYA,gBAAeA,gBAAe,SAAS,CAAC,EAAE,CAAC;AAAA,MACjK,CAAC,EACI,OAAO,SAAUF,OAAM;AAAE,eAAOA,MAAK;AAAA,MAAS,CAAC;AAEpD,UAAI,OAAO,CAAC,KACR,SAAS,KAAK,QAAQ,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,cAC1E,KAAK,QAAQ,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,UAAU,GAAG,CAAC,OAAO,MAAM;AAC3F,eAAO,CAAC,EAAE,cAAc,QAAQ,CAAC;AACjC,eAAO,CAAC,EAAE,WAAW,OAAO,CAAC,EAAE,WAAW,KAAK;AAAA,MACnD;AAEA,UAAI,YAAY,OAAO,OAAO,SAAU,GAAG,GAAG;AAAE,eAAO,KAAK,EAAE,WAAW;AAAA,MAAI,GAAG,CAAC;AACjF,UAAI,YAAY,QAAQ,SAAS;AAEjC,aAAO,YAAY,GAAG;AAClB,YAAI,OAAO,OAAO,IAAI;AACtB,YAAI,CAAC,MAAM;AACP;AAAA,QACJ;AACA,qBAAa,KAAK,WAAW;AAAA,MACjC;AAGA,UAAI,WAAW;AACX,YAAI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,QACV;AACA,iBAAS,IAAI,WAAW,IAAI,MAAM,gBAAgB,QAAQ,KAAK;AAC3D,cAAI,iBAAiB,CAAC,CAAC,EAClB,IAAI,SAAU,KAAK;AAAE,mBAAO,MAAM,gBAAgB,KAAK,SAAU,KAAK;AAAE,qBAAO,IAAI,aAAa;AAAA,YAAK,CAAC;AAAA,UAAG,CAAC,EAC1G,OAAO,SAAUA,OAAM;AAAE,mBAAOA;AAAA,UAAM,CAAC;AAC5C,iBAAO,KAAK,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG,EAAE,SAAS,eAAe,QAAQ,aAAa,eAAe,CAAC,GAAG,YAAY,eAAe,eAAe,SAAS,CAAC,EAAE,CAAC,CAAC;AAAA,QACvK;AAAA,MACJ;AACA,UAAI,MAAM,aAAa,MAAM;AAAA,QACzB,OAAO,KAAK,IAAI,SAAU,KAAK;AAAE,iBAAO,IAAI;AAAA,QAAQ,CAAC;AAAA,MACzD,CAAC;AACD,aAAQ,eAAAH,QAAM,cAAc,MAAM,EAAE,WAAW,GAAG,kBAAkB,cAAcC,cAAa,WAAW,qBAAqB,IAAIA,cAAa,UAAU,oBAAoB,EAAE,GAAG,KAAK,WAAW,OAAOA,WAAU,GAAG,EAAE,OAAO,YAAY,CAAC,EAAE,GAAG,OAAO,IAAI,SAAUE,OAAM,OAAO;AAChR,YAAI,MAAMA,MAAK,SAAS,OAAO;AAC/B,YAAI,cAAcA,MAAK;AACvB,YAAI,aAAaA,MAAK;AACtB,YAAI,QAAQ,SAAS,CAAC,GAAGA,MAAK,KAAK;AACnC,YAAIA,MAAK,OAAO;AACZ,gBAAM,YAAYA,MAAK;AAAA,QAC3B;AACA,YAAIA,MAAK,QAAQ;AACb,gBAAM,gBAAgBA,MAAK;AAAA,QAC/B;AACA,YAAIG,MAAK,OAAO,MAAM,gBAAgB,WAAW,UAAU,UAAU,aAAa,aAAa,MAAM,iBAAiBH,MAAK,OAAO,GAAG,CAAC,GAAG,cAAcG,IAAG,CAAC,GAAG,kBAAkBA,IAAG,CAAC;AACpL,eAAO,OAAO,OAAO,WAAW;AAChC,eAAQ,eAAAN,QAAM,cAAc,KAAK,EAAE,KAAK,OAAO,SAASG,MAAK,WAAW,IAAI,SAAYA,MAAK,SAAS,OAAc,YAAYA,MAAK,iBAAiB,MAAM,MAAM,gBAAgB,GAAG,OAAO,eAAe,OAAO,KAAK,GAAGA,OAAM;AAAA,UAC5N,MAAM;AAAA,QACV,CAAC,CAAC;AAAA,MACN,CAAC,CAAC;AAAA,IACN;AACA,IAAAJ,WAAU,UAAU,gBAAgB,SAAUE,WAAU,OAAO;AAC3D,UAAI,QAAQ;AACZ,aAAO,MAAM,QAAQ,KAAK,IACpB,MAAM,KAAK,SAAU,GAAG;AAAE,eAAO,MAAM,QAAQ,CAAC;AAAA,MAAG,CAAC,IAChD,MAAM,IAAI,SAAU,GAAG,UAAU;AAC/B,eAAO,MAAM,iBAAiBA,WAAU,MAAM,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,QAAQ;AAAA,MAChF,CAAC,IACC,KAAK,iBAAiBA,WAAU,KAAK,IACzC;AAAA,IACV;AACA,IAAAF,WAAU,UAAU,SAAS,WAAY;AACrC,UAAI,KAAK,KAAK,OAAO,KAAK,GAAG,YAAY,YAAY,GAAG,WAAW,SAAS,GAAG,QAAQ,OAAO,GAAG,MAAM,UAAU,GAAG,SAAS,YAAY,GAAG,WAAW,YAAY,GAAG,WAAW,WAAW,GAAG,UAAU,KAAK,GAAG;AACjN,aAAQ,eAAAC,QAAM,cAAc,SAAS,EAAE,UAAqB,GAAG,KAAK,SAAU,eAAAA,QAAM;AAAA,QAAc,eAAAA,QAAM;AAAA,QAAU;AAAA,QAC9G,KAAK,cAAc,UAAU,SAAS;AAAA,QACtC,KAAK,WAAW,MAAM,SAAS,SAAS;AAAA,QACxC,KAAK,cAAc,SAAS,QAAQ;AAAA,MAAC,IAAK,IAAI;AAAA,IACtD;AACA,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGD,WAAU,WAAW,iBAAiB,IAAI;AAC7C,IAAAA,aAAY,WAAW;AAAA,MACnBQ;AAAA,IACJ,GAAGR,UAAS;AACZ,WAAOA;AAAA,EACX,EAAE,eAAAC,QAAM,SAAS;AAAA;;;AEzJjB,IAAAQ,iBAAyC;AAEzC,SAAS,mBAAmB,OAAO;AAC/B,MAAI;AACJ,MAAI,KAAK,MAAM;AACf,MAAI,WAAW,MAAM;AACrB,MAAI,QAAQ,MAAM;AAClB,MAAI,aAAS,uBAAO,IAAI;AACxB,MAAI,iBAAiB,eAAAC,QAAM,YAAY,SAAU,IAAI;AACjD,QAAIC,KAAI;AACR,QAAI,OAAO,QAAQ;AAAE,aAAOA,MAAK,MAAM,cAAc,QAAQA,QAAO,SAAS,SAASA,IAAG,OAAO;AAAA,IAAI;AACpG,QAAI,SAAS,KAAK,OAAO,QAAQ,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,OAAO;AAC7G,QAAI,MAAM,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,cAAc,eAAgB,OAAO,IAAI,IAAK,CAAC;AAC7G,QAAI,CAAC,KAAK;AACN;AAAA,IACJ;AACA,QAAI,OAAO,IAAI,sBAAsB;AACrC,QAAI,SAAS,KAAK;AAClB,QAAI,MAAM,KAAK,MACX,MAAM,sBAAsB,EAAE,MAC9B,SAAS,iBAAiB,KAAK,EAAE,WAAW,GAAG,EAAE;AACrD,WAAO,QAAQ,MAAM,WAAW,QAAQ,OAAO,KAAK,aAAa,EAAE,OAAO,QAAQ,YAAY,EAAE,OAAO,MAAM,cAAc,YAAY,KAAK;AAAA,EAChJ,GAAG,CAAC,CAAC;AACL,gCAAU,WAAY;AAClB,QAAI,MAAM,MAAM;AAChB,QAAI,CAAC,KAAK;AACN;AAAA,IACJ;AACA,mBAAe,IAAI,EAAE;AAAA,EACzB,GAAG,EAAE,KAAK,MAAM,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,EAAE,CAAC;AACrE,gCAAU,WAAY;AAClB,QAAI,QAAQ,OAAO,QAAQ;AAC3B,QAAI,CAAC,OAAO;AACR;AAAA,IACJ;AACA,QAAI,WAAW,WAAY;AACvB,UAAIA;AACJ,sBAAgBA,MAAK,MAAM,cAAc,QAAQA,QAAO,SAAS,SAASA,IAAG,EAAE;AAAA,IACnF;AACA,UAAM,iBAAiB,UAAU,QAAQ;AACzC,WAAO,WAAY;AACf,YAAM,oBAAoB,UAAU,QAAQ;AAAA,IAChD;AAAA,EACJ,CAAC;AACD,SAAQ,eAAAD,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,wBAAwB,GAAG,KAAK,OAAO,GAAG,QAAQ;AACzG;AACA,IAAI,uBAAuBE,UAAS,kBAAkB;;;AC/CtD,IAAAC,iBAAkB;AAIlB,SAAS,SAAS,IAAI;AAClB,MAAI,UAAU,GAAG,SAAS,QAAQ,GAAG;AACrC,MAAI,SAAS,eAAAC,QAAM,UAAU;AAC7B,iBAAAA,QAAM,UAAU,WAAY;AACxB,QAAI,QAAQ,OAAO,QAAQ;AAC3B,QAAI,MAAM,CAAC;AACX,aAAS,YAAY;AAIjB,UAAI,OAAO,CAAC,EAAE,MAAM,KAAK,MAAM,iBAAiB,yBAAyB,CAAC;AAE1E,UAAI,KAAK,KAAK,SAAU,GAAG,OAAO;AAAE,eAAO,IAAI,KAAK,MAAM;AAAA,MAAG,CAAC,GAAG;AAC7D,QAAAC,UAAS,WAAW;AACpB,cAAM;AACN,aAAK,QAAQ,SAAU,KAAK;AACxB,UAAAA,UAAS,QAAQ,GAAG;AAAA,QACxB,CAAC;AAAA,MACL;AAAA,IACJ;AACA,QAAIA,YAAW,IAAI,eAAe,WAAY;AAC1C,gBAAU;AACV,YAAM,eAAe;AAAA,IACzB,CAAC;AACD,UAAM,eAAe;AACrB,UAAM,eAAe;AACrB,cAAU;AACV,WAAO,WAAY;AACf,MAAAA,UAAS,WAAW;AAAA,IACxB;AAAA,EACJ,GAAG,CAAC,QAAQ,MAAM,CAAC;AAOnB,MAAI,YAAa,OAAO,kBAAkB,YAAY,gBAAgB,IAAK;AACvE,mBAAAD,QAAM,UAAU,WAAY;AACxB,UAAI,OAAO,SAAS;AAChB,YAAI,MAAM,CAAC,EAAE,MAAM,KAAK,OAAO,QAAQ,cAAc,iBAAiB,sCAAsC,CAAC;AAC7G,YAAI,QAAQ,SAAU,IAAI;AACtB,cAAI,QAAQ,SAAS,GAAG,aAAa,YAAY,GAAG,EAAE;AACtD,cAAI,SAAS,MAAM,QAAQ,KAAK;AAChC,cAAI,QAAQ;AACZ,cAAI,QAAQ;AACZ,cAAI,MAAM,oBAAoB,OAAO,OAAO;AACxC,oBAAQ,OAAO;AAAA,UACnB,WACS,OAAO,SAAS,OAAO;AAC5B,oBAAQ,OAAO,SAAS;AAAA,UAC5B;AACA,cAAI,UAAU,IAAI;AACd;AAAA,UACJ;AACA,mBAAS,UAAU;AAAA;AAAA,YAEnB,OAAO,UAAU,WAAW,GAAG,OAAO,OAAO,IAAI,IAAI;AAAA,YAAO;AAAA,UAAG;AAC/D,cAAI,MAAM,gBAAgB,QAAQ;AAC9B,qBAAS,cAAc,OAAO,OAAO,UAAU,WAAW,GAAG,OAAO,OAAO,IAAI,IAAI,OAAO,GAAG;AAAA,UACjG;AACA,aAAG,MAAM,UAAU;AAAA,QACvB,CAAC;AAAA,MACL;AAAA,IACJ,GAAG,QAAQ,IAAI,SAAU,QAAQ;AAAE,aAAO,OAAO;AAAA,IAAO,CAAC,EAAE,OAAO,MAAM,gBAAgB,CAAC;AAAA,EAC7F;AACA,SAAQ,eAAAA,QAAM,cAAc,YAAY,EAAE,KAAK,OAAO,GAAG,QAAQ,IAAI,SAAU,QAAQ;AACnF,QAAI,QAAQ,CAAC;AACb,QAAI,MAAM,oBAAoB,OAAO,OAAO;AACxC,YAAM,QAAQ,OAAO;AAAA,IACzB,WACS,OAAO,SAAS,OAAO;AAC5B,YAAM,QAAQ,OAAO,SAAS;AAAA,IAClC;AACA,QAAI,MAAM,gBAAgB,UAAU,MAAM,OAAO;AAC7C,YAAM,WAAW,MAAM;AAAA,IAC3B;AACA,WAAO,eAAAA,QAAM,cAAc,OAAO,EAAE,cAAc,OAAO,OAAO,OAAc,KAAK,OAAO,GAAG,CAAC;AAAA,EAClG,CAAC,CAAC;AACN;AACA,IAAI,aAAaC,UAAS,QAAQ;;;AJ5ElC,SAAS,kBAAkB,OAAO;AAC9B,MAAI,cAAc,MAAM,aAAa,SAAS,MAAM,QAAQ,QAAQ,MAAM,OAAO,KAAK,MAAM;AAC5F,MAAI,CAAC,MAAM,UAAU;AACjB,WAAO;AAAA,EACX;AACA,MAAI,eAAe,MAAM,QAAQ,WAAW,IACtC,YAAY,OAAO,SAAU,QAAQ;AAAE,WAAO,CAAC,OAAO;AAAA,EAAe,CAAC,IACtE,CAAC;AACP,MAAI,CAAC,aAAa,QAAQ;AACtB,WAAO;AAAA,EACX;AACA,SAAQ,eAAAC,QAAM;AAAA,IAAc;AAAA,IAAoB,EAAE,OAAc,YAAY,GAAG;AAAA,IAC3E,eAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,mBAAmB,EAAE,GAAG,aAAa,IAAI,SAAU,QAAQ,OAAO;AACzG,aAAO,OAAO,cAAc,OAAO,KAAK,GAAG,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,YAAY,KAAK,CAAC,GAAG;AAAA,QAC7F,KAAK;AAAA,QACL,MAAM,MAAM;AAAA,QACZ,MAAM,MAAM,SAAS;AAAA,QACrB,UAAU,MAAM,SAAS;AAAA,MAC7B,CAAC;AAAA,IACL,CAAC,CAAC;AAAA,EAAC;AACX;AACA,IAAI;AAAA;AAAA,EAA8B,SAAU,QAAQ;AAChD,cAAUC,eAAc,MAAM;AAC9B,aAASA,gBAAe;AACpB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,cAAa,UAAU,SAAS,WAAY;AACxC,UAAI,KAAK,KAAK,OAAO,cAAc,GAAG,aAAa,KAAK,GAAG,YAAY,SAAS,GAAG,QAAQ,YAAY,GAAG,WAAW,UAAU,GAAG,SAAS,eAAe,GAAG,cAAc,cAAc,GAAG,aAAa,WAAW,GAAG,UAAU,WAAW,GAAG,UAAU,OAAO,GAAG,MAAM,iBAAiB,GAAG,gBAAgB,aAAa,GAAG,YAAY,UAAU,GAAG,SAAS,aAAa,GAAG,YAAY,eAAe,GAAG,cAAc,kBAAkB,GAAG,iBAAiB,kBAAkB,GAAG,iBAAiB,eAAe,GAAG,cAAc,gBAAgB,GAAG,eAAe,WAAW,GAAG,UAAU,kBAAkB,GAAG,iBAAiB,mBAAmB,GAAG,kBAAkB,iBAAiB,GAAG,gBAAgB,WAAW,GAAG,UAAU,mBAAmB,GAAG,kBAAkB,oBAAoB,GAAG,mBAAmB,qBAAqB,GAAG,oBAAoB,OAAO,GAAG,MAAM,YAAY,GAAG,WAAW,SAAS,GAAG,QAAQ,YAAY,GAAG,WAAW,aAAa,GAAG,YAAY,WAAW,GAAG,UAAU,QAAQ,GAAG,OAAO,gBAAgB,GAAG,eAAe,UAAU,GAAG,SAAS,UAAU,GAAG,SAAS,gBAAgB,GAAG,eAAe,WAAW,GAAG;AACrmC,UAAI,iBAAiB,GAAG,eAAe,KAAK,MAAM,cAAc;AAChE,UAAI,aAAa,QAAQ,MAAM,SAAU,QAAQ;AAAE,eAAO,CAAC,OAAO;AAAA,MAAO,CAAC;AAC1E,aAAQ,eAAAD,QAAM;AAAA,QAAc;AAAA,QAAO,EAAE,aAA0B,WAAW,GAAG,iBAAiB,SAAS,GAAG,SAAmB;AAAA,QACzH,eAAAA,QAAM;AAAA,UAAc;AAAA,UAAS,EAAE,KAAK,UAAU,WAAW,GAAG,gBAAgB,MAAM,gBAAgB,UAAU,oBAAoB,MAAS,EAAE;AAAA,UACvI,eAAAA,QAAM,cAAc,YAAU,EAAE,SAAkB,MAAa,CAAC;AAAA,UAChE,eAAAA,QAAM;AAAA,YAAc;AAAA,YAAS;AAAA,YACzB,aAAa,SAAU,eAAAA,QAAM,cAAc,MAAM,MAAM,aAAa,IAAI,SAAU,MAAM,OAAO;AAC3F,kBAAIE,MAAK,OAAO,MAAM,gBAAgB,MAAM,YAAY,GAAG,CAAC,GAAG,cAAcA,IAAG,CAAC,GAAG,kBAAkBA,IAAG,CAAC;AAK1G,qBAAO,CAAC,CAAC,CAAC,CAAC,aAAa,YAAY,EAAE,QAAQ,KAAK,IAAI,CAAC,EAAE,IAAI,KACzD,KAAK,IAAI,WAAW,KACjB,CAAC,MAAM,KAAK,KAAK,IAAI,CAAC,EAAE,IAAI,KAC5B,CAAC,KAAK,IAAI,CAAC,EAAE,YAAc,eAAe,KAAK,IAAI,CAAC,GAAG;AAAA,gBAC3D,cAAc,KAAK,IAAI,CAAC,EAAE;AAAA,gBAC1B,OAAO;AAAA,gBACP,WAAW,KAAK;AAAA,gBAChB,WAAW,KAAK;AAAA,gBAChB,SAAS;AAAA,gBACT,aAAa;AAAA,cACjB,CAAC,IAAM,eAAAF,QAAM,cAAc,MAAM,EAAE,KAAK,OAAO,cAAc,KAAK,OAAO,SAAS,KAAK,SAAS,SAAS,KAAK,SAAS,OAAO,aAAa,WAAW,gBAAgB,GAAG,KAAK,QAAQ,OAAO,OAAO,KAAK,KAAK,IAAI,IAAI;AAAA,YAC1N,CAAC,CAAC,IAAK;AAAA,YACP,eAAAA,QAAM,cAAc,MAAM,EAAE,WAAW,aAAa,cAAc,GAAG,GAAG,QAAQ,IAAI,SAAU,QAAQ;AAClG,kBAAIE;AACJ,uBAASA,MAAK,aAAa,KAAK,SAAU,OAAO;AAAE,uBAAO,CAAC,MAAM,IAAI,QAAQ,MAAM;AAAA,cAAG,CAAC,OAAO,QAAQA,QAAO,SAAS,SAASA,IAAG,aAAa,IACzI,OACA,eAAe,QAAQ;AAAA,gBACrB,cAAc,OAAO;AAAA,gBACrB,OAAO,OAAO;AAAA,cAClB,CAAC;AAAA,YACT,CAAC,CAAC;AAAA,UAAC;AAAA,UACP,CAAC,KAAK,SAAU,eAAAF,QAAM;AAAA,YAAc;AAAA,YAAS;AAAA,YACzC,eAAAA,QAAM,cAAc,MAAM,EAAE,WAAW,GAAG,mBAAmB,EAAE,GAAG,CAAC,UAAW,eAAAA,QAAM,cAAc,MAAM,EAAE,SAAS,QAAQ,OAAO,GAAG,OAAO,gBAAgB,WAAY,eAAAA,QAAM;AAAA,cAAc,eAAAA,QAAM;AAAA,cAAU;AAAA,cACxM,eAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,cAAc,WAAW,GAAG,gCAAgC,MAAM,EAAE,CAAC;AAAA,cACvG,UAAU,eAAe,oBAAoB;AAAA,YAAC,IAAM,OAAO,eAAe,UAAU,eAAe,oBAAoB,CAAC,CAAE,IAAK,IAAI;AAAA,UAAC,IAAM,eAAAA,QAAM,cAAc,WAAW,EAAE,OAAc,YAAwB,YAAY,IAAI,QAAgB,YAAwB,SAAkB,YAAwB,cAA4B,iBAAkC,iBAAkC,eAA8B,UAAoB,iBAAkC,kBAAoC,gBAAgC,UAAoB,kBAAoC,cAA4B,oBAAwC,mBAAsC,MAAY,SAAkB,QAAgB,WAAsB,WAAsB,UAAoB,MAAY,eAA8B,WAAW;AAAA,YACh3B;AAAA,YACA;AAAA,UACJ,EAAE,CAAC;AAAA,QAAE;AAAA,QACb;AAAA,MAAQ;AAAA,IAChB;AACA,WAAOC;AAAA,EACX,EAAE,eAAAD,QAAM,aAAa;AAAA;AACrB,IAAI,iBAAiBG,UAAS,SAAU,OAAO;AAC3C,MAAI,QAAQ,MAAM;AAQlB,SAAQ,eAAAH,QAAM,cAAc,cAAc,SAAS,CAAC,GAAG,OAAO,EAAE,kBAAkB,MAAM,kBAAkB,aAAa,MAAM,aAAa,YAAY,MAAM,YAAY,6BAA6B,MAAM,6BAA6B,SAAS,MAAM,SAAS,UAAU,MAAM,SAAS,CAAC,CAAC;AAC/R,CAAC;;;AKvFD,wBAAuB;AACvB,qBAAoB;AAEpB;AAKA,IAAI,SAAS,WAAY;AAErB,SAAO,OAAO,sBAA+B;AACjD;AAIA,IAAI,iBAAkB,2BAAY;AAC9B,MAAI;AACJ,SAAO,SAAU,KAAK;AAClB,QAAI,CAAC;AACD,aAAO,SAAS,cAAc,GAAG;AACrC,SAAK,OAAO;AACZ,WAAO,KAAK;AAAA,EAChB;AACJ,EAAG;AAIH,IAAI,gBAAY,eAAAI,SAAQ,SAAU,MAAM;AACpC,MAAI,QAAQ,GAAG,OAAO,KACjB,MAAM,4DAA4D,EAClE,MAAM,CAAC,EACP,IAAI,SAAU,GAAG,GAAG;AACrB,YAAQ,MAAM,IAAI,KAAK,MAAM,WAAW,CAAC,IAAI,GAAG,IAAI,WAAW,CAAC,GAC3D,SAAS,EAAE,EACX,SAAS,GAAG,GAAG,EACf,QAAQ,OAAO,EAAE;AAAA,EAC1B,CAAC,EACI,KAAK,EAAE,CAAC;AACb,MAAI,MAAM,WAAW,GAAG;AACpB,WAAO,OAAO;AAAA,EAClB;AACA,SAAO;AACX,CAAC;AAID,IAAI,8BAA0B,eAAAA,SAAQ,SAAU,WAAW;AACvD,MAAI,CAAC;AACD,WAAO,CAAC;AACZ,MAAI,eAAe,SAAS,uBAAuB,SAAS,EAAE,KAAK,CAAC;AACpE,MAAI,cAAc;AACd,QAAI,gBAAgB,iBAAiB,YAAY;AACjD,QAAI,OAAO,CAAC;AACZ,QAAI,OAAO,CAAC;AACZ,QAAI,cAAc,SAAS,cAAc,MAAM,QAAQ,KAAK,MAAM,IAAI;AAClE,UAAI,QAAQ,UAAU,cAAc,KAAK;AAEzC,UAAI,CAAC,MAAM,WAAW,IAAI,GAAG;AACzB,aAAK,OAAO,IAAI,EAAE,MAAM,MAAM;AAAA,MAClC;AAAA,IACJ;AACA,QAAI,cAAc,cAAc,SAAS,cAAc,UAAU,KAAK,KAAK;AACvE,WAAK,MAAM,IAAI;AAAA,IACnB;AACA,QAAI,cAAc,mBACd,cAAc,gBAAgB,QAAQ,KAAK,MAAM,IAAI;AACrD,UAAI,QAAQ,UAAU,cAAc,eAAe;AACnD,UAAI,CAAC,MAAM,WAAW,IAAI,GAAG;AACzB,eAAO;AAAA,UACH,MAAM;AAAA,UACN,SAAS;AAAA,UACT,SAAS,EAAE,MAAM,MAAM;AAAA,QAC3B;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,EAAE,MAAY,KAAW;AAAA,EACpC;AACA,SAAO,CAAC;AACZ,CAAC;AAID,IAAI,iBAAiB,SAAU,UAAU,YAAY,QAAQ,MAAM;AAC/D,MAAI,KAAK,IAAI,KAAK;AAClB,MAAI,YAAY,CAAC;AACjB,MAAI,OAAO,WAAW;AAClB,QAAI;AACA,eAAS,KAAK,SAAS,OAAO,UAAU,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,KAAK,GAAG,KAAK,GAAG;AAC7F,YAAI,YAAY,GAAG;AACnB,YAAI,QAAQ,wBAAwB,SAAS;AAC7C,YAAI,OAAO;AACP,sBAAY,SAAS,SAAS,CAAC,GAAG,SAAS,GAAG,KAAK;AAAA,QACvD;AAAA,MACJ;AAAA,IACJ,SACO,OAAO;AAAE,YAAM,EAAE,OAAO,MAAM;AAAA,IAAG,UACxC;AACI,UAAI;AACA,YAAI,MAAM,CAAC,GAAG,SAAS,KAAK,GAAG;AAAS,aAAG,KAAK,EAAE;AAAA,MACtD,UACA;AAAU,YAAI;AAAK,gBAAM,IAAI;AAAA,MAAO;AAAA,IACxC;AAAA,EACJ;AACA,MAAI,OAAO,eAAe;AACtB,QAAI,aAAa,OAAO,OAAO,eAAe,IAAI;AAClD,QAAI,YAAY;AACZ,UAAI;AACA,iBAAS,KAAK,SAAS,WAAW,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,KAAK,GAAG,KAAK,GAAG;AACvF,cAAI,YAAY,GAAG;AACnB,cAAI,QAAQ,wBAAwB,SAAS;AAC7C,cAAI,OAAO;AACP,wBAAY,SAAS,SAAS,CAAC,GAAG,SAAS,GAAG,KAAK;AAAA,UACvD;AAAA,QACJ;AAAA,MACJ,SACO,OAAO;AAAE,cAAM,EAAE,OAAO,MAAM;AAAA,MAAG,UACxC;AACI,YAAI;AACA,cAAI,MAAM,CAAC,GAAG,SAAS,KAAK,GAAG;AAAS,eAAG,KAAK,EAAE;AAAA,QACtD,UACA;AAAU,cAAI;AAAK,kBAAM,IAAI;AAAA,QAAO;AAAA,MACxC;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,UAAU,QAAQ,OAAO,KAAK,UAAU,IAAI,EAAE,SAAS,GAAG;AAC1D,aAAS,QAAQ,UAAU,EAAE,OAAO,UAAU;AAAA,EAClD;AACA,MAAI,UAAU,QAAQ,OAAO,KAAK,UAAU,IAAI,EAAE,SAAS,GAAG;AAC1D,aAAS,QAAQ,UAAU,EAAE,OAAO,UAAU;AAAA,EAClD;AACJ;AAIA,IAAI,gBAAgB,SAAU,WAAW,MAAM,eAAe,UAAU;AACpE,MAAI,KAAK,IAAI,KAAK;AAClB,MAAI,iBAAiB,cAAc,SAAS,GAAG;AAC3C,QAAI,cAAc,cAAc,CAAC;AAEjC,QAAI,YAAY;AAChB,QAAI,CAAC,MAAM,QAAQ,WAAW,GAAG;AAC7B,kBAAY,CAAC,aAAa;AAAA,IAC9B;AACA,QAAI;AACA,eAAS,cAAc,SAAS,SAAS,GAAG,gBAAgB,YAAY,KAAK,GAAG,CAAC,cAAc,MAAM,gBAAgB,YAAY,KAAK,GAAG;AACrI,YAAI,QAAQ,cAAc;AAC1B,oBAAY;AACZ,YAAI,WAAW,UAAU,OAAO,QAAQ;AACxC,YAAI,aAAa;AACjB,YAAI;AACA,mBAAS,WAAW,MAAM,QAAQ,SAAS,KAAK,IAAI,YAAY,QAAQ,KAAK,GAAG,CAAC,UAAU,MAAM,YAAY,QAAQ,KAAK,GAAG;AACzH,gBAAI,MAAM,UAAU;AACpB,0BAAc;AAEd,gBAAI,IAAI,MAAM;AACV,uBAAS,QAAQ,UAAU,EAAE,QAAQ,IAAI;AAAA,YAC7C;AACA,gBAAI,IAAI,KAAK;AACT,uBAAS,QAAQ,UAAU,EAAE,QAAQ,cAAc,aAAa,OAAO,IAAI,KAAK,IAAI,CAAC,CAAC;AAAA,YAC1F;AAEA,gBAAI,IAAI,SAAS;AACb,wBAAU,WAAW,UAAU,YAAY,UAAU,aAAa,IAAI,UAAU,CAAC;AACjF,4BAAc,IAAI,UAAU;AAAA,YAChC;AAAA,UACJ;AAAA,QACJ,SACO,OAAO;AAAE,gBAAM,EAAE,OAAO,MAAM;AAAA,QAAG,UACxC;AACI,cAAI;AACA,gBAAI,aAAa,CAAC,UAAU,SAAS,KAAK,QAAQ;AAAS,iBAAG,KAAK,OAAO;AAAA,UAC9E,UACA;AAAU,gBAAI;AAAK,oBAAM,IAAI;AAAA,UAAO;AAAA,QACxC;AAAA,MACJ;AAAA,IACJ,SACO,OAAO;AAAE,YAAM,EAAE,OAAO,MAAM;AAAA,IAAG,UACxC;AACI,UAAI;AACA,YAAI,iBAAiB,CAAC,cAAc,SAAS,KAAK,YAAY;AAAS,aAAG,KAAK,WAAW;AAAA,MAC9F,UACA;AAAU,YAAI;AAAK,gBAAM,IAAI;AAAA,MAAO;AAAA,IACxC;AAAA,EACJ;AACA,SAAO;AACX;AAUA,SAAS,OAAO,oBAAoB,KAAK,QAAQ,MAAM,SAAS;AAC5D,SAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,QAAI,KAAK,QAAQ,aAAa,QAAQ;AACtC,WAAO,YAAY,MAAM,SAAU,IAAI;AACnC,cAAQ,GAAG,OAAO;AAAA,QACd,KAAK;AACD,gBAAM,OAAO,SAAS;AACtB,mBAAS,OAAO,SAAS;AACzB,cAAI,CAAC;AAAQ,mBAAO,CAAC,GAAa,CAAC;AACnC,wBAAc;AACd,cAAI,CAAC,eAAe,MAAM;AAAG,mBAAO,CAAC,GAAa,CAAC;AACnD,gBAAM,yBAAyB,QAAQ,SAAS,OAAO;AACvD,iBAAO,CAAC,GAAa,CAAC;AAAA,QAC1B,KAAK;AACD,cAAI,CAAC,eAAe,QAAQ,IAAI;AAAG,mBAAO,CAAC,GAAa,CAAC;AACzD,mBAAS,KAAK,UAAU,MAAM;AAC9B,cAAI,EAAE,UAAU;AAAqB,mBAAO,CAAC,GAAa,CAAC;AAC3D,gBAAM,mBAAmB,MAAM;AAC/B,iBAAO,CAAC,GAAa,CAAC;AAAA,QAC1B,KAAK;AAAG,iBAAO,CAAC,GAAa,IAAI,QAAQ,aAAa,OAAO,CAAC;AAAA,QAC9D,KAAK;AACD,gBAAM,GAAG,KAAK;AACd,cAAI,IAAI,MAAM;AACV,+BAAmB,MAAM,IAAI,IAAI;AACjC,kBAAM,IAAI;AAAA,UACd;AACA,aAAG,QAAQ;AAAA,QACf,KAAK;AAAG,iBAAO,CAAC,GAAc,GAAG;AAAA,MACrC;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACL;AAQA,SAAS,YAAY,SAAS,OAAO,SAAS,aAAa;AACvD,MAAI,IAAI,IAAI,IAAI;AAChB,MAAI,gBAAgB,QAAQ;AAAE,kBAAc;AAAA,EAAO;AACnD,SAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,QAAI,OAAO,KAAK,IAAI,IAAI,MAAM,WAAW,UAAU,SAAS,MAAM,UAAU,UAAU,WAAW,cAAc,KAAK,KAAK,IAAI,IAAI,KAAK,UAAU,WAAW,mBAAmB,wBAAwB,WAAW,aAAa,QAAQ,iBAAiB,gBAAgB,UAAU,oBAAoB,UAAU,QAAQ,UAAU,KAAK,SAAS,UAAU,YAAY,SAAS,mBAAmB,qBAAqB,QAAQ,OAAO;AACra,QAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK;AACxC,WAAO,YAAY,MAAM,SAAU,IAAI;AACnC,cAAQ,GAAG,OAAO;AAAA,QACd,KAAK;AACD,kBAAQ,MAAM,OAAO,MAAM,MAAM,KAAK,KAAK,MAAM,YAAY,KAAK,MAAM,WAAW,OAAO,MAAM,MAAM,YAAY,MAAM,WAAW,WAAW,MAAM;AACpJ,oBAAU,MAAM,iBAAiB,CAAC;AAClC,iBAAO,CAAC;AACR,qBAAW;AACX,cAAI,EAAE,OAAO,YAAY,YAAY,QAAQ;AAAM,mBAAO,CAAC,GAAa,CAAC;AACzE,sBAAY,QAAQ,aAAa;AACjC,yBAAe,QAAQ,gBAAgB;AACvC,gBAAM,aAAa,MAAM,SAAS,SAAS,CAAC,GAAG,MAAM,KAAK,IAAI,KAAK,CAAC,GAAG,GAAG,SAAS,IAAI,KAAK,QAAQ,GAAG,GAAG,YAAY,IAAI,KAAK,WAAW,IAAI,GAAG,CAAC;AAClJ,iBAAO,CAAC,GAAa,IAAI,QAAQ,QAAQ,KAAK,KAAK;AAAA,YAC3C,YAAY;AAAA,YACZ;AAAA,YACA;AAAA,UACJ,CAAC,CAAC;AAAA,QACV,KAAK;AACD,gBAAM,GAAG,KAAK;AACd,cAAI,CAAC,IAAI,MAAM;AACX,gBAAI,OAAO,WAAW,GAAG,oBAAoB,CAAC;AAC9C,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAY;AAAA,UACxB;AAKA,cAAI,MAAM,QAAQ,IAAI,IAAI,GAAG;AACzB,mBAAO,IAAI;AAAA,UACf,WACS,MAAM,SAAS,KAAK,IAAI,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI,GAAG;AAClF,mBAAO,IAAI,KAAK;AAAA,UACpB,WACS,MAAM,SAAS,KAAK,IAAI,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,GAAG;AACnF,mBAAO,IAAI,KAAK;AAAA,UACpB,OACK;AACD,gBAAI;AACA,mBAAK,KAAK,SAAS,OAAO,KAAK,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,KAAK,GAAG,KAAK,GAAG;AACjF,sBAAM,GAAG;AACT,oBAAI,IAAI,KAAK,eAAe,GAAG,KAAK,MAAM,QAAQ,IAAI,KAAK,GAAG,CAAC,GAAG;AAC9D,yBAAO,IAAI,KAAK,GAAG;AACnB;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ,SACO,OAAO;AAAE,oBAAM,EAAE,OAAO,MAAM;AAAA,YAAG,UACxC;AACI,kBAAI;AACA,oBAAI,MAAM,CAAC,GAAG,SAAS,KAAK,GAAG;AAAS,qBAAG,KAAK,EAAE;AAAA,cACtD,UACA;AAAU,oBAAI;AAAK,wBAAM,IAAI;AAAA,cAAO;AAAA,YACxC;AAAA,UACJ;AAEA,qBAAW,WAAW,OAAO,YAAY,KAAK,CAAC;AAC/C,mBAAS,SAAS,IAAI;AACtB,iBAAO,SAAS;AAChB,iBAAO,CAAC,GAAa,CAAC;AAAA,QAC1B,KAAK;AACD,iBAAO,MAAM;AACb,aAAG,QAAQ;AAAA,QACf,KAAK;AACD,cAAI,OAAO,YAAY,YAAY,QAAQ,UAAU;AACjD,uBAAW,OAAO,QAAQ,UAAU,MAAM,OAAO;AAAA,UACrD;AACA,cAAI,KAAK,WAAW,GAAG;AACnB,gBAAI,OAAO,WAAW,GAAG,oBAAoB,CAAC;AAC9C,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAY;AAAA,UACxB;AACA,qBAAW,IAAI,QAAQ,SAAS;AAChC,sBAAY,SAAS,aAAa,SAAS;AAAA,YACvC,YAAY,EAAE,iBAAiB,GAAG;AAAA,UACtC,CAAC;AACD,oBAAU,QAAQ,CAAC,EAAE,OAAO,UAAU,QAAQ,GAAG,QAAQ,EAAE,CAAC;AAC5D,8BAAoB,QAAQ;AAC5B,cAAI,eAAe,iBAAiB,GAAG;AACnC,gCAAoB,yBAAyB,mBAAmB,MAAM,OAAO;AAAA,UACjF;AACA,mCAAyB,QAAQ,iBAAiB,MAAM,QAAQ,QAAQ,aAAa;AACrF,cAAI,wBAAwB;AACxB,sBAAU,QAAQ;AAClB,gBAAI;AAEA,mBAAK,YAAY,SAAS,OAAO,GAAG,cAAc,UAAU,KAAK,GAAG,CAAC,YAAY,MAAM,cAAc,UAAU,KAAK,GAAG;AACnH,yBAAS,YAAY;AACrB,uBAAO,WAAW;AAAA,cACtB;AAAA,YACJ,SACO,OAAO;AAAE,oBAAM,EAAE,OAAO,MAAM;AAAA,YAAG,UACxC;AACI,kBAAI;AACA,oBAAI,eAAe,CAAC,YAAY,SAAS,KAAK,UAAU;AAAS,qBAAG,KAAK,SAAS;AAAA,cACtF,UACA;AAAU,oBAAI;AAAK,wBAAM,IAAI;AAAA,cAAO;AAAA,YACxC;AAAA,UACJ;AACA,4BAAkB,oBACZ,QAAQ,OAAO,SAAUC,SAAQ;AAC/B,gBAAI,qBAAqB;AACzB,gBAAIA,QAAO,QAAQ,mBAAmB,QAAQA,QAAO,IAAI,MAAM,IAAI;AAC/D,qBAAO,yBAAyB,QAAQA,YAAW,QAAQA,YAAW,SAAS,SAASA,QAAO,UAAU;AAAA,YAC7G;AACA,mBAAO;AAAA,UACX,CAAC,IACC,QAAQ,OAAO,SAAUA,SAAQ;AAAE,oBAAQA,YAAW,QAAQA,YAAW,SAAS,SAASA,QAAO,UAAU;AAAA,UAAa,CAAC;AAChI,2BAAiB,gBAAgB,IAAI,SAAUA,SAAQ;AACnD,mBAAO,OAAOA,QAAO,OAAO,IAAI;AAAA,UACpC,CAAC;AACD,qBAAW,UAAU,OAAO,CAAC;AAC7B,mBAAS,SAAS;AAClB,oBAAU,aAAa;AAAA,YACnB,MAAM;AAAA,cACF,KAAK;AAAA,cACL,QAAQ;AAAA,YACZ;AAAA,YACA,IAAI;AAAA,cACA,KAAK;AAAA,cACL,QAAQ,eAAe;AAAA,YAC3B;AAAA,UACJ;AACA,cAAI,aAAa;AACb,mBAAO,CAAC,GAAc,uBAAuB,UAAU,WAAW,iBAAiB,UAAU,KAAK,IAAI,CAAC;AAAA,UAC3G;AACA,+BAAqB,CAAC;AACtB,qBAAW;AACX,cAAI,QAAQ,UAAU;AAClB,mBAAO,WAAW,MAAM,QAAQ,QAAQ;AAAA,UAC5C;AAEA,qBAAW,cAAc,WAAW,MAAM,WAAW,QAAQ;AAE7D,iBAAO,YAAY,MAAM,SAAU,MAAM;AAAE,mBAAO;AAAA,UAAM,CAAC;AACzD,aAAG,QAAQ;AAAA,QACf,KAAK;AACD,aAAG,KAAK,KAAK,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;AAC5B,mBAAS,SAAS,IAAI,GAAG,WAAW,OAAO,KAAK;AAChD,aAAG,QAAQ;AAAA,QACf,KAAK;AACD,cAAI,CAAC,CAAC,SAAS;AAAM,mBAAO,CAAC,GAAa,EAAE;AAC5C,gBAAM,SAAS;AACf,oBAAU,aAAa,MAAM,IAAI,IAAI;AACrC,sBAAY;AACZ,qBAAW,UAAU,OAAO,QAAQ;AACpC,uBAAa;AACb,oBAAU,SAAUA,SAAQ;AACxB,gBAAI,QAAQ,OAAO,MAAM,WAAW,iBAAiB,YAAY,aAAa,cAAc,YAAY,UAAU,SAAS,SAAS,KAAK,MAAM,SAAS,MAAM,MAAM,aAAa,KAAK,cAAc,YAAY,WAAW,OAAO,MAAM,WAAW,IAAI,SAAS,IAAI,QAAQ,IAAI,aAAa,SAAS,YAAY,IAAI;AACtT,mBAAO,YAAY,MAAM,SAAU,IAAI;AACnC,sBAAQ,GAAG,OAAO;AAAA,gBACd,KAAK;AACD,gCAAc;AACd,2BAASA,QAAO;AAChB,0BAAQ,YAAY,SAAS,MAAM;AACnC,sBAAI,OAAO,UAAU,eAAe,CAACA,QAAO,SAAS,KAAK;AACtD,2BAAO,CAAC,GAAc,UAAU;AAAA,kBACpC;AAEA,sBAAI,UAAU,IAAI,UAAU;AACxB,wBAAI,IAAI,SAAS,MAAM,MAAM,GAAG;AAC5B,6BAAO,CAAC,GAAc,UAAU;AAAA,oBACpC,OACK;AAED,gCAAU,WAAW,UAAU,YAAY,WAAW,IAAI,SAAS,MAAM,IAAI,GAAG,UAAU;AAAA,oBAC9F;AAAA,kBACJ;AACA,iCAAe,UAAU,YAAYA,QAAO,UAAU,OAAO;AAC7D,yBAAOA,QAAO,QAAQ;AACtB,sBAAI,GAAG,SAAS,WAAW,SAAS,mBAAmB;AAAQ,2BAAO,CAAC,GAAa,CAAC;AACrF,qBAAG,QAAQ;AAAA,gBACf,KAAK;AACD,qBAAG,KAAK,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;AACxB,yBAAO,CAAC,GAAa,UAAU,KAAK,CAAC;AAAA,gBACzC,KAAK;AACD,8BAAY,GAAG,KAAK;AACpB,yBAAO,CAAC,GAAa,mBAAmB,SAAS,CAAC;AAAA,gBACtD,KAAK;AACD,oCAAkB,GAAG,KAAK;AAC1B,+BAAa,gBAAgB;AAC7B,gCAAc,gBAAgB;AAC9B,iCAAe;AACf,sBAAI,aAAa,aAAa;AAC1B,wBAAI,aAAa,cAAc;AAC3B,oCAAe,eAAe,cAAe;AAC7C,mCAAa;AAAA,oBACjB;AAAA,kBACJ,OACK;AACD,wBAAI,cAAc,cAAc;AAC5B,mCAAc,eAAe,aAAc;AAC3C,oCAAc;AAAA,oBAClB;AAAA,kBACJ;AACA,+BAAa,UAAU,MAAM,mBAAmB;AAChD,6BAAW;AACX,sBAAI,YAAY;AACZ,+BAAW,WAAW,CAAC;AAAA,kBAC3B;AAEA,sBAAI,YAAY,SAAS,YAAY,UAAU,YAAY,OAAO;AAC9D,6BAAS,QAAQ,UAAU,EAAE,QAAQ;AACrC,2BAAO,CAAC,GAAc,UAAU;AAAA,kBACpC;AACA,4BAAU,SAAS,SAAS;AAAA,oBACxB,QAAQ;AAAA,oBACR,WAAW;AAAA,kBACf,CAAC;AACD,4BAAU,eAAe,KAAK;AAC9B,4BAAU,SAAS,SAAS;AAAA;AAAA,oBAExB,IAAI,EAAE,KAAK,aAAa,GAAG,KAAK,WAAW,EAAE;AAAA,oBAC7C,KAAK;AAAA,sBACD,OAAO;AAAA,sBACP,QAAQ;AAAA,oBACZ;AAAA,oBACA,YAAY;AAAA,sBACR,SAAS;AAAA,oBACb;AAAA,kBACJ,CAAC;AACD,yBAAO,CAAC,GAAa,CAAC;AAAA,gBAC1B,KAAK;AACD,wBAAM,GAAG,KAAK;AACd,0BAAQ,KAAK,GAAG;AAChB,yBAAO,CAAC,GAAa,CAAC;AAAA,gBAC1B,KAAK;AAAG,yBAAO,CAAC,GAAa,EAAE;AAAA,gBAC/B,KAAK;AACD,sBAAI,EAAE,QAAQ,UAAU,SAAS;AAAgB,2BAAO,CAAC,GAAa,CAAC;AACvE,yBAAOA,QAAO,SAAS;AACvB,6BAAW,OAAO,SAAS,YAAY,OACjC,OAAO,MAAM,SAAS,OAAO,IAC7B,WAAc;AACpB,yBAAOA,QAAO,SAAS;AACvB,yBAAO,OAAO,SAAS,YAAY,OAC7B,OAAO,MAAM,SAAS,OAAO,IAC7B;AACN,gCAAc,eAAe,OAAO;AACpC,2BAAS,QAAQ,UAAU,EAAE,QAAQ;AAAA,oBACjC,MAAM,QAAQ;AAAA,oBACd,WAAW;AAAA,kBACf;AACA,yBAAO,CAAC,GAAa,EAAE;AAAA,gBAC3B,KAAK;AACD,sBAAI,EAAE,SAAS,aAAa,SAAS;AAAmB,2BAAO,CAAC,GAAa,CAAC;AAC9E,yBAAO,CAAC,GAAa,OAAO,oBAAoB,KAAKA,SAAQ,MAAM,OAAO,CAAC;AAAA,gBAC/E,KAAK;AACD,wBAAM,GAAG,KAAK;AACd,iCAAeA,QAAO,SAAS,cAAc;AAC7C,+BAAaA,QAAO,SAAS,cAAc;AAC3C,sBAAI,MAAM,QAAQ,GAAG,GAAG;AACpB,0BAAM,IAAI,OAAO,SAAUC,MAAK,KAAK;AACjC,0BAAI,OAAO,MAAM;AACb,+BAAOA;AAAA,sBACX,WACS,SAAS,GAAG,GAAG;AACpB,4BAAI,OAAO,OAAO,KAAK,GAAG;AAC1B,4BAAI,KAAK,WAAW,KACf,KAAK,UAAU,KAAK,KAAK,SAAS,MAAM,GAAI;AAE7C,iCAAO,KAAK,OAAO,SAAUC,MAAK;AAAE,mCAAOA,SAAQ;AAAA,0BAAQ,CAAC;AAE5D,0BAAAD,KAAI,KAAK,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC;AAAA,wBAC9B,WACS,KAAK,SAAS,GAAG;AAEtB,0BAAAA,KAAI,IAAI,YAAY,CAAC,IAAI;AAAA,wBAC7B;AAAA,sBACJ;AACA,6BAAOA;AAAA,oBACX,GAAG,CAAC,CAAC;AAAA,kBACT;AACA,sBAAI,OAAO,UAAU,eAAe,SAAS,KAAK,IAAI,KAAK,OAAO,QAAQ,OAAO,SAAS,KAAK,IAAI,GAAG,IAAI;AACtG,iCAAa,KAAK,IAAI,KAAK,OAAO,QAAQ,OAAO,SAAS,KAAM,UAAU,QAAQ,IAAI,GAAG,IACnF,IAAI,GAAG,IACP,UAAU,SAAS,IAAI,GAAG,IACtB,IAAI,GAAG,IACP,IAAI,GAAG;AACjB,4BAAQ;AACR,wBAAI,SAAS,SAAS,GAAG;AACrB,0BAAI,eAAe,UAAa,eAAe,IAAI;AAC/C,4BAAI,CAAC,UAAU,eAAe,MAAM,GAAG;AAKnC,kCAAQ,UAAU,OAAO;AAAA,wBAC7B;AAAA,sBACJ,OACK;AACD,gCAAQ,UAAU,cAAc,OAAO;AAAA,sBAC3C;AAAA,oBACJ;AACA,2BAAO,cAAc,KAAK;AAE1B,wBAAI,eAAe,IAAI,GAAG;AACtB,6BAAO,yBAAyB,MAAM,SAAS,OAAO;AAAA,oBAC1D,OACK;AACD,6BAAO,OAAO,MAAM,OAAO;AAAA,oBAC/B;AACA,6BAAS,QAAQ,UAAU,EAAE,QAAQ;AAAA,kBACzC,OACK;AACD,6BAAS,QAAQ,UAAU,EAAE,QAAQ,cAAc,KAAK;AAAA,kBAC5D;AACA,yBAAO,CAAC,GAAa,EAAE;AAAA,gBAC3B,KAAK;AACD,sBAAI,EAAE,SAAS,UAAU,SAAS;AAAgB,2BAAO,CAAC,GAAa,EAAE;AACzE,8BAAY;AACZ,uBAAKD,QAAO,UAAU,UAAU,GAAG,SAAS,KAAK,GAAG,QAAQ,SAAS,OAAO,SAAS,eAAe,IAAI,KAAK,GAAG,aAAa,cAAc,OAAO,SAAS,MAAM;AACjK,sBAAI,OAAO;AACP,8BAAU,eAAO,OAAO,eAAO,QAAQ;AACvC,iCAAa,eAAO,OAAO,WAAW;AACtC,gCAAY,QAAQ,QAAQ,IACtB,QAAQ,OAAO,MAAM,IACrB,WAAW,QAAQ,IACf,WAAW,OAAO,MAAM,IACxB;AAAA,kBACd;AACA,sBAAI,SAAS;AACT,gCAAY,eAAO,KAAK,EAAE,QAAQ;AAAA,kBACtC;AACA,sBAAI,WAAW;AACX,6BAAS,QAAQ,UAAU,EAAE,QAAQ;AAAA,kBACzC;AACA,yBAAO,CAAC,GAAa,EAAE;AAAA,gBAC3B,KAAK;AACD,sBAAI,EAAE,SAAS;AAAe,2BAAO,CAAC,GAAa,EAAE;AACrD,yBAAO,CAAC,GAAa,OAAO,CAAC;AAAA,gBACjC,KAAK;AACD,uBAAK,GAAG,KAAK;AACb,sBAAI,GAAG,WAAW,SAAS,SAAS,GAAG,SAAS;AAC5C,6BAAS,QAAQ,UAAU,EAAE,QAAQ,GAAG,QAAQ,KAAK;AAAA,kBACzD;AACA,yBAAO,CAAC,GAAa,EAAE;AAAA,gBAC3B,KAAK;AACD,sBAAIA,QAAO,SAAS,KAAK;AACrB,6BAAS,QAAQ,UAAU,EAAE,QAAQ,cAAc,aAAa,OAAOA,QAAO,SAAS,KAAK,OAAO,CAAC,CAAC;AAAA,kBACzG,OACK;AACD,6BAAS,QAAQ,UAAU,EAAE,QAAQ;AAAA,kBACzC;AACA,qBAAG,QAAQ;AAAA,gBACf,KAAK;AACD,8BAAY,SAAS,QAAQ,UAAU,EAAE;AACzC,sBAAI,OAAO,UAAU,SAAS,GAAG;AAC7B,6BAAS,QAAQ,UAAU,EAAE,SAAS;AAAA,kBAC1C;AACA,yBAAO;AAAA,oBAAC;AAAA;AAAA,kBAAY;AAAA,cAC5B;AAAA,YACJ,CAAC;AAAA,UACL;AACA,aAAG,QAAQ;AAAA,QACf,KAAK;AACD,aAAG,KAAK,KAAK,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;AAC5B,+BAAqB,MAAM,QAAQ,SAAS,eAAe,IAAI,sBAAsB,kBAAkB,KAAK;AAC5G,aAAG,QAAQ;AAAA,QACf,KAAK;AACD,cAAI,CAAC,CAAC,oBAAoB;AAAM,mBAAO,CAAC,GAAa,EAAE;AACvD,mBAAS,oBAAoB;AAC7B,iBAAO,CAAC,GAAc,QAAQ,MAAM,CAAC;AAAA,QACzC,KAAK;AACD,aAAG,KAAK;AACR,aAAG,QAAQ;AAAA,QACf,KAAK;AACD,gCAAsB,kBAAkB,KAAK;AAC7C,iBAAO,CAAC,GAAa,CAAC;AAAA,QAC1B,KAAK;AAAI,iBAAO,CAAC,GAAa,EAAE;AAAA,QAChC,KAAK;AACD,kBAAQ,GAAG,KAAK;AAChB,gBAAM,EAAE,OAAO,MAAM;AACrB,iBAAO,CAAC,GAAa,EAAE;AAAA,QAC3B,KAAK;AACD,cAAI;AACA,gBAAI,uBAAuB,CAAC,oBAAoB,SAAS,KAAK,kBAAkB;AAAS,iBAAG,KAAK,iBAAiB;AAAA,UACtH,UACA;AAAU,gBAAI;AAAK,oBAAM,IAAI;AAAA,UAAO;AACpC,iBAAO;AAAA,YAAC;AAAA;AAAA,UAAgB;AAAA,QAC5B,KAAK;AACD,qBAAW,OAAO,KAAK;AACvB,iBAAO,CAAC,GAAa,CAAC;AAAA,QAC1B,KAAK;AAAI,iBAAO,CAAC,GAAa,EAAE;AAAA,QAChC,KAAK;AACD,kBAAQ,GAAG,KAAK;AAChB,gBAAM,EAAE,OAAO,MAAM;AACrB,iBAAO,CAAC,GAAa,EAAE;AAAA,QAC3B,KAAK;AACD,cAAI;AACA,gBAAI,YAAY,CAAC,SAAS,SAAS,KAAK,OAAO;AAAS,iBAAG,KAAK,MAAM;AAAA,UAC1E,UACA;AAAU,gBAAI;AAAK,oBAAM,IAAI;AAAA,UAAO;AACpC,iBAAO;AAAA,YAAC;AAAA;AAAA,UAAgB;AAAA,QAC5B,KAAK;AAED,wBAAc,WAAW,MAAM,UAAU,QAAQ;AACjD,uBAAa,UAAU,QAAQ;AAC/B,iBAAO;AAAA,YAAC;AAAA;AAAA,UAAY;AAAA,MAC5B;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACL;AACA,SAAS,aAAa,UAAU,UAAU;AACtC,SAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,QAAI,QAAQ;AACZ,WAAO,YAAY,MAAM,SAAU,IAAI;AACnC,cAAQ,GAAG,OAAO;AAAA,QACd,KAAK;AAAG,iBAAO,CAAC,GAAa,SAAS,KAAK,YAAY,CAAC;AAAA,QACxD,KAAK;AACD,mBAAS,GAAG,KAAK;AACjB,cAAI,QAAQ;AACR,mBAAO,IAAI,KAAK,CAAC,MAAM,GAAG;AAAA,cACtB,MAAM;AAAA,YACV,CAAC;AACD,0CAAO,MAAM,WAAW,OAAO;AAAA,UACnC;AACA,iBAAO;AAAA,YAAC;AAAA;AAAA,UAAY;AAAA,MAC5B;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACL;AACA,SAAS,gBAAgB,KAAK;AAC1B,MAAI,UAAU;AACd,SAAO,OAAO,GAAG;AACb,cAAU,6BAA6B,MAAM,EAAE,IAAI;AACnD,UAAM,KAAK,MAAM,MAAM,EAAE,IAAI;AAAA,EACjC;AACA,SAAO;AACX;AAIA,SAAS,uBAAuB,UAAU,WAAW,iBAAiB,UAAU,KAAK,MAAM;AACvF,MAAI;AACJ,SAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,QAAI,OAAO,WAAW,UAAU,mBAAmB,qBAAqB,QAAQ,KAAK,MAAM,UAAU;AACrG,QAAI,MAAM;AACV,WAAO,YAAY,MAAM,SAAU,IAAI;AACnC,cAAQ,GAAG,OAAO;AAAA,QACd,KAAK;AACD,kBAAQ;AACR,sBAAY;AACZ,qBAAW,CAAC;AACZ,aAAG,QAAQ;AAAA,QACf,KAAK;AACD,aAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACzB,8BAAoB,SAAS,eAAe,GAAG,sBAAsB,kBAAkB,KAAK;AAC5F,aAAG,QAAQ;AAAA,QACf,KAAK;AACD,cAAI,CAAC,CAAC,oBAAoB;AAAM,mBAAO,CAAC,GAAa,CAAC;AACtD,mBAAS,oBAAoB;AAC7B,mBAAS;AACT,cAAI,IAAI,KAAK,OAAO,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU;AAAY,mBAAO,CAAC,GAAa,CAAC;AAClH,iBAAO,CAAC,GAAa,OAAO,UAAU,KAAK,QAAQ,MAAM,CAAC,CAAC,CAAC;AAAA,QAChE,KAAK;AACD,gBAAM,GAAG,KAAK;AACd,cAAI,OAAO,SAAS,GAAG,GAAG;AACtB,mBAAO,OAAO,KAAK,GAAG;AACtB,iBAAK,WAAW,GAAG,WAAW,WAAW,YAAY;AACjD,wBAAU,QAAQ,gBAAgB,KAAK,IAAI,QAAQ,EAAE,iBACjD;AAAA,gBACI,MAAM;AAAA,gBACN,YAAY;AAAA,gBACZ,UAAU,CAAC,IAAK,OAAO,KAAK,KAAK,GAAG,GAAG,GAAI,CAAC;AAAA,cAChD;AAAA,YACR;AAAA,UACJ;AACA,aAAG,QAAQ;AAAA,QACf,KAAK;AACD,gCAAsB,kBAAkB,KAAK;AAC7C,iBAAO,CAAC,GAAa,CAAC;AAAA,QAC1B,KAAK;AAAG,iBAAO,CAAC,GAAa,CAAC;AAAA,QAC9B,KAAK;AACD,mBAAS,GAAG,KAAK;AACjB,iBAAO,EAAE,OAAO,OAAO;AACvB,iBAAO,CAAC,GAAa,CAAC;AAAA,QAC1B,KAAK;AACD,cAAI;AACA,gBAAI,uBAAuB,CAAC,oBAAoB,SAAS,KAAK,kBAAkB;AAAS,iBAAG,KAAK,iBAAiB;AAAA,UACtH,UACA;AAAU,gBAAI;AAAM,oBAAM,KAAK;AAAA,UAAO;AACtC,iBAAO;AAAA,YAAC;AAAA;AAAA,UAAgB;AAAA,QAC5B,KAAK;AACD,uBAAa,UAAU,QAAQ;AAC/B,iBAAO;AAAA,YAAC;AAAA;AAAA,UAAY;AAAA,MAC5B;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACL;;;ACptBA,IAAAG,iBAAkB;AAElB,SAAS,eAAe,IAAI;AACxB,MAAI,qBAAqB,GAAG,oBAAoB,qBAAqB,GAAG,oBAAoB,2BAA2B,GAAG,0BAA0B,oBAAoB,GAAG,mBAAmB,uBAAuB,GAAG,sBAAsB,mBAAmB,GAAG,kBAAkB,KAAK,GAAG,YAAY,KAAK,GAAG,WAAW,SAAS,GAAG,QAAQ,OAAO,GAAG,MAAM,wBAAwB,GAAG,uBAAuB,yBAAyB,GAAG,wBAAwB,uBAAuB,GAAG,sBAAsB,mBAAmB,GAAG,kBAAkB,gBAAgB,GAAG,eAAe,qBAAqB,GAAG;AACzlB,MAAI,SAAS,eAAAC,QAAM,QAAQ,WAAY;AACnC,QAAIC,MAAK,OAAO,uBAAuB,YACjC;AAAA,MACE,YAAY;AAAA,MACZ,gBAAgB;AAAA,IACpB,IACE,oBAAoB,aAAaA,IAAG,YAAY,iBAAiBA,IAAG;AAC1E,QAAI,OAAO,OAAO,0BAA0B,UAAU,EAAE,IAAI,SAAU,OAAO;AAAE,aAAQ;AAAA,QACnF,MAAM;AAAA,QACN,MAAM,MAAM,IAAI,SAAU,QAAQ;AAC9B,cAAIA,KAAI,IAAI,IAAI;AAChB,iBAAQ,SAAS,SAAS,CAAC,GAAI,OAAO,eAAe,OAC/C;AAAA,YACE,MAAM;AAAA,YACN,MAAM,OAAO;AAAA,YACb,OAAO,OAAO;AAAA,YACd,eAAe,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,SAAS,OAAO,IAAI;AAAA,UACnH,IACE,SAAS,EAAE,MAAM,cAAc,MAAM,OAAO,MAAM,eAAe,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,SAAS,OAAO,IAAI,EAAE,GAAG,OAAO,UAAU,CAAE,GAAG,EAAE,OAAO,MAAMA,MAAK,OAAO,gBAAgB,QAAQA,QAAO,SAAS,SAASA,IAAG,UAAU,QAAQ,OAAO,SAAS,KAAK,OAAO,MAAM,QAAQ,MAAM,KAAK,OAAO,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,QAAQ,OAAO,SAAS,KAAK,OAAO,MAAM,CAAC;AAAA,QAC7c,CAAC;AAAA,MACL;AAAA,IAAI,CAAC;AACL,QAAI,eAAe,yBAAyB,UAAU;AAEtD,QAAI,CAAC,sBAAsB,KAAK,QAAQ;AACpC,WAAK,OAAO,GAAG,KAAK,SAAS,CAAC;AAC9B,WAAK,CAAC,EAAE,KAAK,OAAO,aAAa,GAAG,KAAK,CAAC,EAAE,KAAK,SAAS,aAAa,CAAC;AAAA,IAC5E;AACA,QAAI,YAAY,KAAK,KAAK,SAAS,CAAC;AACpC,QAAI,CAAC,MAAM,QAAQ,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,IAAI,KACnF,UAAU,KAAK,UAAU,YAAY;AACrC,kBAAY;AAAA,QACR,MAAM;AAAA,QACN,MAAM,CAAC;AAAA,MACX;AACA,WAAK,KAAK,SAAS;AAAA,IACvB;AACA,QAAI,QAAQ,KAAK,IAAI,aAAa,UAAU,KAAK,SAAS,CAAC;AAC3D,WAAO,UAAU,GAAG;AAChB,gBAAU,KAAK,KAAK;AAAA,QAChB,MAAM;AAAA,QACN,KAAK;AAAA,MACT,CAAC;AAAA,IACL;AACA,QAAI,oBAAoB,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,SAAS,MAAM;AACnH,cAAU,KAAK,KAAK;AAAA,MAChB,MAAM;AAAA,MACN,WAAW;AAAA,MACX,aAAa;AAAA,MACb,MAAM;AAAA,QACF;AAAA,UACI,MAAM;AAAA,UACN,OAAO,GAAG,oBAAoB;AAAA,UAC9B,WAAW,GAAG,iCAAiC,MAAM;AAAA,UACrD,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,UACN,OAAO;AAAA,UACP,SAAS;AAAA,UACT,eAAe;AAAA,UACf,SAAS,kBAAkB,IAAI,SAAU,QAAQ;AAC7C,mBAAO;AAAA,cACH,UAAU,SAAUA,KAAI;AACpB,oBAAI,IAAI;AACR,oBAAIC,UAASD,IAAG;AAChB,uBAAOC,QAAO,yBAAyB,OAAO,OAAO,EAAE,GAAG;AAAA,kBACtD,MAAM;AAAA,kBACN,OAAO;AAAA,kBACP,WAAW,GAAG,+BAA+B;AAAA,kBAC7C,gBAAgB,GAAG,qCAAqC;AAAA,kBACxD,MAAM;AAAA,kBACN,SAAS,MAAM,KAAK,OAAO,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,QAAQ,OAAO,SAAS,KAAK,OAAO;AAAA,kBAC9H,eAAe,sBAAsB,QAAQ,sBAAsB,SAAS,SAAS,kBAAkB,SAAS,OAAO,OAAO,EAAE;AAAA,kBAChI,OAAO;AAAA,oBACH,QAAQ,CAAC,KAAK,CAAC;AAAA,oBACf,WAAW,GAAG,OAAO,OAAO,YACxB,CAAC,OAAO,WACR,OAAO,YAAY;AAAA,kBAC3B;AAAA,gBACJ,GAAG;AAAA,kBACC,OAAO,yBAAyB,SAAS,MAAM;AAAA,kBAC/C,UAAU,SAAU,OAAO;AACvB,2BAAO,yBAAyB,QAAQ,yBAAyB,SAAS,SAAS,qBAAqB,QAAQ,KAAK;AAAA,kBACzH;AAAA,gBACJ,CAAC;AAAA,cACL;AAAA,YACJ;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,QACA;AAAA,UACI,MAAM;AAAA,UACN,OAAO,GAAG,QAAQ;AAAA,UAClB,MAAM;AAAA,UACN,OAAO;AAAA,UACP,WAAW;AAAA,QACf;AAAA,QACA;AAAA,UACI,MAAM;AAAA,UACN,OAAO,GAAG,OAAO;AAAA,UACjB,MAAM;AAAA,UACN,WAAW;AAAA,UACX,YAAY;AAAA,QAChB;AAAA,QACA;AAAA,UACI,UAAU,WAAY;AAClB,mBAAO,eAAgB,eAAAF,QAAM;AAAA,cAAc;AAAA,cAAK,EAAE,WAAW,GAAG,mBAAmB,qBAAqB,gBAAgB,EAAE,GAAG,SAAS,iBAAiB;AAAA,cACnJ,GAAG,qBAAqB,aAAa,QAAQ;AAAA,cAC7C,eAAAA,QAAM;AAAA,gBAAc;AAAA,gBAAQ,EAAE,WAAW,GAAG,uBAAuB,EAAE;AAAA,gBACjE,eAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,oBAAoB,WAAW,OAAO,CAAC;AAAA,cAAC;AAAA,YAAC,IAAK;AAAA,UAC5F;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,MACH,MAAM;AAAA,MACN,KAAK;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,MACN,YAAY,GAAG,QAAQ;AAAA,MACvB;AAAA,MACA,SAAS,CAAC;AAAA,MACV;AAAA,IACJ;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,EACJ,CAAC;AACD,SAAO,OAAO,mBAAmB,QAAQ;AAAA,IACrC,KAAK;AAAA,IACL,gBAAgB,GAAG,sBAAsB;AAAA,IACzC,kBAAkB,GAAG,6BAA6B;AAAA,IAClD,SAAS;AAAA,IACT,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACJ,CAAC;AACL;AACA,IAAI,mBAAmBG,UAAS,SAAU,IAAI;AAC1C,MAAI,QAAQ,GAAG,OAAO,QAAQ,GAAG,OAAO,OAAO,GAAG,MAAM,OAAO,OAAO,IAAI,CAAC,SAAS,SAAS,MAAM,CAAC;AACpG,MAAI,uBAAuB,eAAAH,QAAM,YAAY,SAAU,QAAQ,OAAO;AAClE,WAAO,gBAAgB,KAAK;AAC5B,aAAS,MAAM,sBAAsB,IAAI;AAAA,EAC7C,GAAG,CAAC,CAAC;AACL,MAAI,mBAAmB,eAAAA,QAAM,YAAY,WAAY;AACjD,UAAM,yBAAyB;AAAA,EACnC,GAAG,CAAC,CAAC;AACL,MAAI,MAAM,eAAAA,QAAM,QAAQ,WAAY;AAAE,WAAQ,QAAQ,aAAa,MAAM,KAAK,IAAI;AAAA,EAAO,GAAG,CAAC,OAAO,IAAI,CAAC;AACzG,SAAQ,eAAAA,QAAM,cAAc,gBAAgB,SAAS,CAAC,GAAG,MAAM,EAAE,0BAA0B,MAAM,0BAA0B,mBAAmB,MAAM,mBAAmB,oBAAoB,MAAM,oBAAoB,sBAA4C,kBAAoC,MAAM,IAAI,CAAC,CAAC;AACrT,CAAC;;;AC9JD,IAAAI,iBAAkB;AAElB,SAAS,KAAK,IAAI;AACd,MAAI;AACJ,MAAI,SAAS,GAAG,QAAQ,SAAS,GAAG,QAAQ,OAAO,GAAG,MAAM,QAAQ,GAAG,OAAO,aAAa,GAAG,YAAY,SAAS,GAAG,QAAQ,kBAAkB,GAAG,iBAAiB,QAAQ,GAAG,OAAO,WAAW,GAAG,UAAU,YAAY,GAAG,WAAW,KAAK,GAAG,YAAY,KAAK,GAAG,aAAa,qBAAqB,GAAG,oBAAoB,UAAU,GAAG,SAAS,cAAc,GAAG,aAAa,mBAAmB,GAAG,kBAAkB,mBAAmB,GAAG,kBAAkB,iBAAiB,GAAG,gBAAgB,KAAK,GAAG,WAAW,gBAAgB,GAAG;AAC3gB,MAAI,OAAO,QAAQ,KAAK,SAAS,OAAO,IAAI,MAAM,GAAG;AACjD,WAAO;AAAA,EACX;AACA,MAAI,KAAK,OAAO,eAAAC,QAAM,QAAQ,WAAY;AACtC,QAAIC,SAAQ,SAAS,CAAC,GAAG,OAAO,SAAS,KAAK;AAC9C,QAAIC,MAAK,OAAO,MAAM,gBAAgB,QAAQ,MAAM,eAAe,GAAG,CAAC,GAAG,cAAcA,IAAG,CAAC,GAAGC,mBAAkBD,IAAG,CAAC;AACrH,WAAO,CAAC,OAAO,OAAOD,QAAO,WAAW,GAAGE,gBAAe;AAAA,EAC9D,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC;AACjD,MAAI,mBAAmB,eAAAH,QAAM,YAAY,WAAY;AACjD,gBAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,IAAI;AAAA,EAClE,GAAG,CAAC,CAAC;AACL,MAAI,KAAK,OAAO,eAAAA,QAAM,QAAQ,WAAY;AACtC,QAAII,UAAS,CAAC;AACd,QAAIC,SAAQ,CAAC;AACb,QAAIC,sBAAqB;AACzB,QAAI,OAAO,aAAa,MAAM,UAAU;AACpC,MAAAA,sBAAqB;AACrB,MAAAF,QAAO,KAAK,eAAAJ,QAAM,cAAc,QAAQ,EAAE,KAAK,UAAU,WAAW,GAAG,cAAc,GAAG,OAAO,KAAK,YAAY,CAAC,CAAC;AAClH,MAAAI,QAAO,KAAK,KAAK,UAAW,eAAAJ,QAAM,cAAc,WAAS,EAAE,KAAK,WAAW,MAAM,MAAM,MAAM,KAAK,CAAC,IAAK,KAAK,QAAS,eAAAA,QAAM;AAAA,QAAc;AAAA,QAAK,SAAS,EAAE,WAAW,GAAG,gBAAgB,GAAG,KAAK,YAAY,SAAS,KAAK,cAAc,gBAAgB,GAAG,iBAAiB,EAAE,QAAQ,KAAK,MAAM,CAAC,EAAE,GAAG,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,SAAS,OAAO,EAAE,UAAU,CAAC;AAAA,QAC5Y,eAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,SAAS,WAAW,OAAO,CAAC;AAAA,MAAC,IAAK,KAAK,aAAc,eAAAA,QAAM;AAAA,QAAc;AAAA,QAAK,SAAS;AAAA,UAAE,KAAK;AAAA,UAAc,WAAW,GAAG,oBAAoB,KAAK,WAAW,cAAc,EAAE;AAAA;AAAA;AAAA,UAGhN,SAAS,KAAK;AAAA,QAAe,GAAG,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,SAAS,KAAK,WAAW,SAAS,QAAQ,EAAE,UAAU,CAAC;AAAA,QACnK,eAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,oBAAoB,WAAW,OAAO,CAAC;AAAA,MAAC,IAAM,eAAAA,QAAM,cAAc,QAAQ,EAAE,KAAK,eAAe,WAAW,GAAG,mBAAmB,EAAE,CAAC,CAAE;AAAA,IAChL;AACA,QAAI,CAAC,cACD,OAAO,aACP,MAAM,YACN,MAAM,aACN,KAAK,WAAW;AAChB,MAAAK,OAAM,KAAK,eAAAL,QAAM;AAAA,QAAc;AAAA,QAAK,SAAS,EAAE,KAAK,WAAW,WAAW,MAAM,aAA0B,WAAW,GAAG,eAAe,EAAE,GAAG,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,SAAS,MAAM,EAAE,UAAU,CAAC;AAAA,QAChP,eAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,QAAQ,WAAW,OAAO,CAAC;AAAA,MAAC,CAAC;AAAA,IACvE;AACA,WAAO,CAACI,SAAQC,QAAOC,mBAAkB;AAAA,EAC7C,GAAG;AAAA,IACC,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,IACP,MAAM;AAAA,EACV,CAAC,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC;AAEhE,MAAI,2BAA2B,OAAO,OAAO,SAAS,oBAAoB;AAC1E,MAAI,kBAAkB,2BAChB,OAAO,SAAS,kBAChB,eAAAN,QAAM,QAAQ,WAAY;AAAE,WAAO,qBAAqB,OAAO,QAAQ;AAAA,EAAG,GAAG,CAAC,CAAC;AACrF,MAAI,OAAO,eAAAA,QAAM,QAAQ,WAAY;AAAE,WAAO,KAAK;AAAA,EAAQ,GAAG;AAAA,IAC1D,2BAA2B,KAAK,KAAK,UAAU,KAAK,MAAM;AAAA,IAC1D,oBAAoB,iBAAiB,KAAK,MAAM;AAAA,EACpD,CAAC;AACD,MAAI,2BAA2B,KAAK,OAAO,SAAS,wBAAwB,QAAQ,OAAO,SAAS,KAAK;AACzG,MAAI,WAAW,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG;AAAA;AAAA,IAEzC,SAAS,OAAO,SAAS,cAAc,QAAQ,MAAM;AAAA,IAAS,aAAa,MAAM;AAAA,IAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAU3F;AAAA,IAAoC,SAAS,KAAK,SAAS,OAAO,IAAI;AAAA,IAAG;AAAA,IAAoC,YAAY;AAAA,IAAQ,WAAW;AAAA,IAAO;AAAA,IAAgC,oBAAoB;AAAA,IAAyB,KAAK;AAAA,IAAM;AAAA,IAAsB,WAAW,CAAC,MAAM,UAC/Q,aACA,MAAM,4BAA4B,MAAM;AAAA,IAAU,SAAS;AAAA,IAAW;AAAA,IAAc,WAAW,GAAG,OAAO,SAAS,WAAW,iBAAiB,kBAAkB;AAAA,IAAG,eAAe,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,SAAS,OAAO,QAAQ,OAAO,KAAK;AAAA,EAAE,CAAC;AAC7S,SAAO,SAAS;AAChB,MAAI,OAAO,SAAS,aAAa;AAC7B,WAAQ,eAAAA,QAAM;AAAA,MAAc;AAAA,MAAM,SAAS,EAAE,OAAc,WAAW,GAAG,OAAO,SAAS,WAAW,eAAe,EAAE,GAAG,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,UAAU,CAAC;AAAA,MAC3M,eAAAA,QAAM,cAAc,YAAU,EAAE,aAAa,IAAI,MAAM,WAAW,aAAa,SAAS,SAAS,WAAW,KAAK,UAAU,OAAO,SAAS,KAAK,YAAY,WAAW,KAAK,UAAU,QAAQ,UAAU,KAAK,gBAAgB,CAAC,KAAK,WAAW,UAAU,kBAAkB,eAAe,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,SAAS,QAAQ,EAAE,CAAC;AAAA,IAAC;AAAA,EAClY,WACS,OAAO,SAAS,YAAY;AACjC,WAAQ,eAAAA,QAAM,cAAc,MAAM,SAAS,EAAE,OAAc,WAAW,GAAG,OAAO,SAAS,WAAW,iBAAiB;AAAA,MAC7G,mBAAmB,CAAC,KAAK;AAAA,IAC7B,CAAC,EAAE,GAAG,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,SAAS,MAAM,EAAE,UAAU,CAAC,GAAG,KAAK,YAAY,eAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,QAAQ,WAAW,OAAO,CAAC,IAAI,IAAI;AAAA,EAC/M,WACS,OAAO,SAAS,cAAc;AACnC,WAAQ,eAAAA,QAAM,cAAc,MAAM,EAAE,OAAc,WAAW,GAAG,OAAO,SAAS,WAAW,eAAe,EAAE,GAAG,KAAK,aAAc,eAAAA,QAAM;AAAA,MAAc;AAAA,MAAK,SAAS;AAAA,QAAE,WAAW,GAAG,mBAAmB,KAAK,WAAW,cAAc,EAAE;AAAA;AAAA;AAAA,QAGnO,SAAS,KAAK;AAAA,MAAe,GAAG,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,SAAS,KAAK,WAAW,SAAS,QAAQ,EAAE,UAAU,CAAC;AAAA,MACnK,eAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,oBAAoB,WAAW,OAAO,CAAC;AAAA,IAAC,IAAK,IAAI;AAAA,EAC3F,WACS,OAAO,SAAS,WAAW;AAChC,WAAQ,eAAAA,QAAM,cAAc,MAAM,EAAE,OAAc,WAAW,GAAG,OAAO,SAAS,WAAW,eAAe,EAAE,GAAG,GAAG,OAAO,kBAAkB,gBAAgB,KAAK,MAAM,IAAI,IAAI,KAAK,IAAI,EAClL,MAAM,GAAG,EACT,IAAI,SAAU,GAAG;AAAE,aAAO,SAAS,GAAG,EAAE,IAAI;AAAA,IAAG,CAAC,EAChD,KAAK,GAAG,CAAC;AAAA,EAClB;AACA,SAAO,OAAO,QAAQ,SAAS,SAAS,CAAC,GAAG,OAAO,QAAQ,GAAG;AAAA;AAAA,IAE1D,WAAW;AAAA,IAAI,UAAU;AAAA,IAAI,SAAS;AAAA,IAAM,QAAQ;AAAA,IAAO,QAAQ,OAAO;AAAA,IAAU,MAAM;AAAA,EAAO,CAAC,GAAG,QAAQ;AACrH;;;A7BhFA,IAAI;AAAA;AAAA,EAAuB,SAAU,QAAQ;AACzC,cAAUO,QAAO,MAAM;AACvB,aAASA,OAAM,OAAO,SAAS;AAC3B,UAAI,QAAQ,OAAO,KAAK,MAAM,KAAK,KAAK;AACxC,YAAM,MAAM,eAAAC,QAAM,UAAU;AAC5B,YAAM,mBAAmB,CAAC;AAC1B,YAAM,WAAW,CAAC;AAClB,YAAM,YAAY,CAAC;AACnB,YAAM,0BAAsB,gBAAAC,SAAS,MAAM,gBAAgB,KAAK,KAAK,GAAG,KAAK;AAAA,QACzE,UAAU;AAAA,QACV,SAAS;AAAA,MACb,CAAC;AACD,YAAM,+BAA2B,gBAAAA,SAAS,MAAM,qBAAqB,KAAK,KAAK,GAAG,KAAK;AAAA,QACnF,UAAU;AAAA,QACV,SAAS;AAAA,MACb,CAAC;AACD,UAAI,SAAS;AACb,aAAO,kBAAkB,KAAK;AAC9B,YAAM,qBAAqB,MAAM,mBAAmB,KAAK,KAAK;AAC9D,YAAM,WAAW,MAAM,SAAS,KAAK,KAAK;AAC1C,YAAM,kBAAkB,MAAM,gBAAgB,KAAK,KAAK;AACxD,YAAM,kBAAkB,MAAM,gBAAgB,KAAK,KAAK;AACxD,YAAM,eAAe,MAAM,aAAa,KAAK,KAAK;AAClD,YAAM,cAAc,MAAM,YAAY,KAAK,KAAK;AAChD,YAAM,iBAAiB,MAAM,eAAe,KAAK,KAAK;AACtD,YAAM,oBAAoB,MAAM,kBAAkB,KAAK,KAAK;AAC5D,YAAM,aAAa,MAAM,WAAW,KAAK,KAAK;AAC9C,YAAM,kBAAkB,MAAM,gBAAgB,KAAK,KAAK;AACxD,YAAM,QAAQ,MAAM,MAAM,KAAK,KAAK;AACpC,YAAM,aAAa,MAAM,WAAW,KAAK,KAAK;AAC9C,YAAM,sBAAsB,MAAM,oBAAoB,KAAK,KAAK;AAChE,YAAM,aAAa,MAAM,WAAW,KAAK,KAAK;AAC9C,YAAM,iBAAiB,MAAM,eAAe,KAAK,KAAK;AACtD,YAAM,gBAAgB,MAAM,cAAc,KAAK,KAAK;AACpD,YAAM,kBAAkB,MAAM,gBAAgB,KAAK,KAAK;AACxD,YAAM,mBAAmB,MAAM,iBAAiB,KAAK,KAAK;AAC1D,YAAM,aAAa,MAAM,WAAW,KAAK,KAAK;AAC9C,YAAM,qBAAqB,MAAM,mBAAmB,KAAK,KAAK;AAC9D,YAAM,iBAAiB,MAAM,eAAe,KAAK,KAAK;AACtD,YAAM,mBAAmB,MAAM,iBAAiB,KAAK,KAAK;AAC1D,YAAM,sBAAsB,MAAM,oBAAoB,KAAK,KAAK;AAChE,YAAM,sBAAsB,MAAM,oBAAoB,KAAK,KAAK;AAChE,YAAM,uBAAuB,MAAM,qBAAqB,KAAK,KAAK;AAClE,UAAI,QAAQ,MAAM,OAAO,UAAU,MAAM,SAAS,aAAa,MAAM,YAAY,mBAAmB,MAAM,kBAAkB,YAAY,MAAM,WAAW,UAAU,MAAM,SAAS,WAAW,MAAM,UAAU,WAAW,MAAM,UAAU,WAAW,MAAM,UAAU,eAAe,MAAM,cAAc,kBAAkB,MAAM,iBAAiB,kBAAkB,MAAM,iBAAiB,mBAAmB,MAAM,kBAAkB,mBAAmB,MAAM,kBAAkB,eAAe,MAAM,cAAc,WAAW,MAAM,UAAU,gCAAgC,MAAM,+BAA+B,6BAA6B,MAAM,4BAA4B,yBAAyB,MAAM,wBAAwB,UAAU,MAAM,SAAS,qBAAqB,MAAM,oBAAoB,UAAU,MAAM,SAAS,qBAAqB,MAAM,oBAAoB,kBAAkB,MAAM,iBAAiB,cAAc,MAAM,aAAa,qBAAqB,MAAM,oBAAoB,YAAY,MAAM;AACl+B,UAAI,aAAa,MAAM;AACvB,UAAI,OAAO,eAAe,UAAU;AAChC,qBAAa,SAAS,yBAAyB,YAAY,MAAM,MAAM,OAAO,GAAG,EAAE;AAAA,MACvF;AACA,YAAM,OAAO;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS,UAAU,UAAU;AAAA,QAC7B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,GAAG;AAAA,QACC;AAAA,MACJ,CAAC;AACD,cAAI,qBAAAC,SAAc,kBAAkB,KAChC,mBAAmB,qBAAqB,OAAO;AAC/C,cAAM,sBAAsB,IAAI;AAAA,MACpC;AACA,kBAAY,QAAQ,QAAQ,KAAK,SAAS,YAAY,KAAK;AAC3D,MAAAH,OAAM,SAAS,OAAO,MAAM,OAAO,MAAS,KAAK,MAAM,aAAa;AACpE,YAAM,UAAU,KAAK,SAAS,WAAY;AACtC,eAAO,MACF,gBAAgB,EAChB,OAAO,SAAU,KAAK;AAAE,iBAAO,IAAI,SAAS,CAAC,IAAI,UAAU,CAAC,IAAI,WAAW,CAAC,IAAI;AAAA,QAAO,CAAC;AAAA,MACjG,GAAG,SAAU,MAAM;AAAE,eAAO,KAAK,QAAQ,MAAM,eAAe;AAAA,MAAG,CAAC,CAAC;AACnE,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,WAAW,SAAU,OAAO,OAAO,WAAW;AAChD,UAAI,SAAS,MAAM;AACnB,UAAI,QAAQ,aAAa,OAAO,SAAUI,QAAO;AAAE,eAAOA,OAAM;AAAA,MAAO,CAAC;AACxE,UAAI,OAAO,CAAC;AACZ,UAAI,aAAa;AAEjB,UAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,YAAI,CAAC,aACD,KAAC,eAAAC,SAAQ,aAAa,WAAW,SAAUD,QAAO;AAAE,iBAAOA,OAAM;AAAA,QAAO,CAAC,GAAG,KAAK,GAAG;AACpF,uBAAa;AACb,iBAAO;AAAA,QACX;AAAA,MACJ,WACS,OAAO,WAAW,UAAU;AACjC,YAAI,WAAW,yBAAyB,QAAQ,MAAM,MAAM,OAAO;AACnE,YAAI,OAAO,YACL,yBAAyB,QAAQ,UAAU,MAAM,OAAO,IACxD;AACN,YAAI,SAAS,UAAU;AACnB,uBAAa;AAAA,QACjB,WACS,YAAY,iBAAiB,OAAO,MAAM,EAAE,CAAC,SAClD,eAAAC,SAAQ,MAAM,QAAQ,GAAG;AACzB,uBAAa;AAAA,QACjB,OACK;AACD,uBAAa;AACb,iBAAO,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAC;AAAA,QACjD;AAAA,MACJ;AACA,UAAI,YAAY;AACZ,cAAM,SAAS,MAAM,MAAM,YAAY,MAAM,UAAU,MAAM,WAAW,MAAM,QAAQ;AAAA,MAC1F,WACS,MAAM,aAAa,OAAO;AAK/B,qBAAa;AACb,cAAM,SAAS,OAAO,MAAM,YAAY,MAAM,UAAU,MAAM,WAAW,MAAM,QAAQ;AAAA,MAC3F;AACA,YAAM,QAAQ,MAAM,QAAQ,KACxB,MAAM,eAAe,MAAM,UAAU,MAAM,UAAU;AACzD,aAAO;AAAA,IACX;AACA,IAAAL,OAAM,UAAU,oBAAoB,WAAY;AAC5C,UAAI,cAAc,KAAK,IAAI;AAC3B,WAAK,mBAAmB;AAGxB,WAAK,UAAU,KAAK,aAAa,aAAa,KAAK,qBAAqB,OAAO,OAAO,CAAC;AACvF,UAAI,QAAQ,KAAK;AACjB,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,qBAAqB,GAAG,oBAAoB,uBAAuB,GAAG;AAI7G,UAAI,sBACA,CAAC,MAAM,kBAAkB,UACzB,sBAAsB;AACtB,6BAAqB,CAAC,CAAC;AAAA,MAC3B;AAAA,IACJ;AACA,IAAAA,OAAM,UAAU,kBAAkB,SAAU,KAAK;AAC7C,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,KAAK,UAAU,UAAU;AAC7B,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,oBAAM,KAAK,MAAM;AACjB,yBAAW,IAAI,KAAK,YAAY,KAAK,MAAM;AAC3C,kBAAI,CAAC,eAAe,QAAQ,GAAG;AAC3B,sBAAM,IAAI,MAAM,sBAAsB;AAAA,cAC1C;AACA,iBAAG,QAAQ;AAAA,YACf,KAAK;AACD,iBAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACzB,kBAAI,YAAY,IAAI;AACpB,qBAAO,CAAC,GAAa,IAAI,QAAQ,UAAU,IAAI,MAAM,CAAC;AAAA,YAC1D,KAAK;AACD,yBAAW,GAAG,KAAK;AACnB,kBAAI,CAAC,SAAS,IAAI;AACd,sBAAM,IAAI,MAAM,SAAS,GAAG;AAAA,cAChC;AACA,kBAAI,WAAW,SAAS,IAAI;AAC5B,kBAAI,WAAW,IAAI;AACnB,kBAAI,SAAS,EAAE;AACf,qBAAO,CAAC,GAAa,CAAC;AAAA,YAC1B,KAAK;AACD,oBAAM,GAAG,KAAK;AACd,kBAAI,SAAS,IAAI,OAAO;AACxB,kBAAI,OAAO,SAAS,IAAI,OAAO;AAC/B,qBAAO,CAAC,GAAa,CAAC;AAAA,YAC1B,KAAK;AACD,kBAAI,YAAY,KAAK;AACrB,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAgB;AAAA,YAC5B,KAAK;AAAG,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAChC;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,OAAM,UAAU,qBAAqB,WAAY;AAC7C,UAAI,QAAQ,KAAK;AACjB,UAAI,cAAc,KAAK,IAAI;AAC3B,UAAI,MAAM,gBAAgB;AACtB,aAAK,wBAAwB,aAAa,YAAY,eAAe,KAAK,0BAA0B,OAAO,QAAQ;AACnH,aAAK,UAAU,KAAK,KAAK,qBAAqB;AAC9C,aAAK,qBAAqB;AAAA,MAC9B;AAAA,IACJ;AAKA,IAAAA,OAAM,UAAU,uBAAuB,WAAY;AAC/C,UAAI,QAAQ;AACZ,UAAI,KAAK,KAAK,OAAO,iBAAiB,GAAG,gBAAgB,gBAAgB,GAAG,eAAe,KAAK,GAAG;AACnG,UAAI,CAAC,gBAAgB;AACjB;AAAA,MACJ;AACA,UAAI,QAAQ,KAAK;AACjB,UAAI,eAAe,MAAM;AACzB,UAAI,CAAC,cAAc;AACf;AAAA,MACJ;AAGA,UAAI,CAAC,aAAa,gBACd,aAAa,sBAAsB,EAAE,SAAS,aAAa,eACvD,KAAK;AACT,aAAK,QAAQ,WAAW,WAAY;AAChC,gBAAM,qBAAqB;AAAA,QAC/B,GAAG,GAAG;AACN;AAAA,MACJ;AAEA,UAAI,iBAAiB,OAAO;AAC5B,UAAI,kBAAkB,OAAO,YAAY,EAAE;AAC3C,UAAI,SAAS,gBAAgB,aAAa,aAAa;AACvD,UAAI,UAAU,WAAW,SAAS,MAAM;AACpC,yBAAiB,OAAO,eAAe;AACvC,0BAAkB,SAAS,cAAc,MAAM,EAAE;AAAA,MACrD;AACA,UAAI,qBAAqB;AACzB,UAAI,WAAW;AACf,UAAI,aAAa,SAAS;AAC1B,aAAO,YAAY;AACf,YAAI,gBAAgB,eAAe,YAAY,gBAAgB;AAC/D,YAAI,eAAe,eAAe,YAAY,qBAAqB;AACnE,YAAI,oBAAoB;AACxB,YAAI,cAAc,SAAS;AAC3B,eAAO,aAAa;AAChB,cAAI,UAAU,iBAAiB,WAAW,EAAE;AAC5C,cAAI,YAAY,cAAc,YAAY,SAAS;AAC/C,gBAAI,QAAQ,SAAS,sBAAsB;AAC3C,gBAAI,QAAQ,YAAY,sBAAsB;AAC9C,gBAAI,MAAM,UAAU,MAAM,KAAK;AAC3B,mCACI,YAAY,eACR,eAAe,aAAa,eAAe;AAAA,YACvD;AAAA,UACJ;AACA,wBAAc,YAAY;AAAA,QAC9B;AACA,YAAI,eAAe,eAAe,UAAU,eAAe;AAC3D,8BACI,gBAAgB,eAAe,eAAe;AAClD,mBAAW;AACX,qBAAa,SAAS;AACtB,YAAI,UAAU,WAAW,SAAS,QAAQ,WAAW,UAAU;AAC3D;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,cAAc,kBAAkB,eAAe,YAC7C,cACA;AACN,UAAI,cAAc,SAAS,cAAc,IACnC,eAAe,WAAW,IAC1B;AACN,UAAI,qBAAqB,cACnB,GAAG,OAAO,aAAa,IAAI,IAC3B,GAAG,OAAO,KAAK,MAAM,iBAAiB,kBAAkB,qBAAqB,CAAC,GAAG,IAAI;AAC3F,mBAAa,MAAM,WAAW,IAAI;AAClC,mBAAa,MAAM,YAAY,mBAAmB,OAAO,WAAW,GAAG,kBAAkB;AAAA,IAC7F;AACA,IAAAA,OAAM,UAAU,qBAAqB,SAAU,WAAW;AACtD,UAAI;AACJ,UAAI,QAAQ,KAAK;AACjB,UAAI,QAAQ,MAAM;AAClB,oBAAc;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,GAAG,WAAW,OAAO,SAAU,SAAS;AACpC,YAAI,QAAQ,eAAe,YAAY,KACnC,OAAO,QAAQ,eAAe,UAAU;AACxC,kBAAQ,aAAa,SAAS,yBAAyB,QAAQ,YAAY,MAAM,MAAM,OAAO,GAAG,EAAE;AAAA,QACvG;AACA,YAAI,QAAQ,WAAW,CAAC,MAAM,SAAS;AACnC,iBAAO,QAAQ;AAAA,QACnB;AACA,cAAM,OAAO,SAAS;AAAA,UAClB,oBAAoB,MAAM;AAAA,QAC9B,CAAC;AAAA,MACL,CAAC;AACD,UAAI,WAAW,CAAC,UAAU,SAAS,OAAO,GAAG,WAAW,KAAK,KACxD,CAAC,MAAM,SACJ,CAAC,MAAM,UACN,MAAM,SAAS,UAAU,QACrB,OAAO,MAAM,WAAW,YAAY,eAAe,MAAM,MAAM,IAAM;AAC9E,QAAAA,OAAM,SAAS,OAAO,OAAO,SAAS,KAAK,KAAK,aAAa;AAAA,MACjE,WACS,wBAAwB,UAAU,UAAU,MAAM,QAAQ,GAAG;AAClE,YAAI,mBAAmB,MAAM,aACxB,IAAI,SAAU,MAAM;AAAE,iBAAO,KAAK;AAAA,QAAI,CAAC,EACvC,KAAK,GAAG;AACb,cAAM,eAAe,MAAM,YAAY,CAAC,GAAG,MAAM,UAAU;AAC3D,YAAI,MAAM,QAAQ,MAAM,QAAQ,KAC5B,MAAM,QAAQ,UAAU,QAAQ,KAChC,MAAM,SAAS,WAAW,UAAU,SAAS,QAAQ;AAErD,cAAI,eAAe,MAAM,aAAa,IAAI,SAAU,MAAM;AAAE,mBAAO,KAAK;AAAA,UAAI,CAAC,EAAE,KAAK,GAAG;AACvF,+BAAqB,gBAAgB,KAAK,aAAa;AAAA,QAC3D,OACK;AACD,eAAK,aAAa;AAAA,QACtB;AAAA,MACJ;AAEA,UAAI,MAAM,mBAAmB,UAAU,gBAAgB;AACnD,YAAI,KAAK,uBAAuB;AAC5B,cAAI,MAAM,KAAK,UAAU,QAAQ,KAAK,qBAAqB;AAC3D,cAAI,QAAQ,IAAI;AACZ,iBAAK,UAAU,OAAO,KAAK,CAAC;AAAA,UAChC;AACA,eAAK,sBAAsB;AAC3B,iBAAO,KAAK;AACZ,cAAI,gBAAgB,KAAK,KAAK,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC7E,cAAI,cAAc;AACd,yBAAa,MAAM,SAAS;AAAA,UAChC;AAAA,QACJ;AACA,aAAK,mBAAmB;AAAA,MAC5B;AAAA,IACJ;AACA,IAAAA,OAAM,UAAU,uBAAuB,WAAY;AAC/C,UAAI,WAAW,KAAK,MAAM;AAC1B,WAAK,UAAU,QAAQ,SAAU,IAAI;AAAE,eAAO,GAAG;AAAA,MAAG,CAAC;AACrD,WAAK,YAAY,CAAC;AAClB,aAAO,KAAK;AACZ,WAAK,oBAAoB,OAAO;AAChC,WAAK,yBAAyB,OAAO;AACrC,kBAAY,QAAQ,QAAQ,KAAK,SAAS,YAAY,IAAI;AAC1D,mBAAa,KAAK,KAAK;AACvB,UAAI,SAAS,KAAK;AAClB,aAAO,oBAAoB,IAAI;AAAA,IACnC;AACA,IAAAA,OAAM,UAAU,cAAc,WAAY;AACtC,UAAI,IAAI;AACR,OAAC,KAAK,KAAK,IAAI,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe;AAC/E,UAAI,KAAK,MAAM,gBAAgB;AAC3B,SAAC,KAAK,KAAK,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe;AAAA,MAC7E;AACA,UAAI,YAAY,OAAO;AACvB,mBAAa,OAAO,OAAO,GAAG,SAAS;AAAA,IAC3C;AACA,IAAAA,OAAM,UAAU,aAAa,SAAU,MAAM,GAAG,GAAG;AAC/C,UAAI,mBAAmB,KAAK,MAAM;AAClC,0BAAoB,iBAAiB,MAAM,GAAG,CAAC;AAC/C,WAAK,SAAS,GAAG,OAAO,GAAG,GAAG,EAAE,OAAO,CAAC,CAAC,IAAI;AAC7C,cAAQ,KAAK,MAAM,MAAM,QAAQ,KAAK,MAAM,OAAO,CAAC;AAAA,IACxD;AACA,IAAAA,OAAM,UAAU,eAAe,SAAU,GAAG,QAAQ,KAAK;AACrD,UAAI,WAAW,KAAK,MAAM;AAE1B,aAAO,SAAS,GAAG,QAAQ,GAAG;AAAA,IAClC;AACA,IAAAA,OAAM,UAAU,cAAc,SAAU,MAAM,OAAO,OAAO;AACxD,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,IAAI,OAAO,MAAM,eAAe;AACpC,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,mBAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,OAAO,GAAG,MAAM,gBAAgB,GAAG,eAAe,aAAa,GAAG;AACrG,kBAAI,CAAC,YAAY;AACb,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,sBAAQ,UAAU,SAAY,QAAQ,CAAC,KAAK;AAC5C,kBAAI,OAAO;AACP,sBAAM,YAAY,MAAM,KAAK;AAAA,cACjC,OACK;AAGD,qBAAK,OAAO,KAAK;AAAA,cACrB;AACA,mBAAK,aAAa;AAClB,qBAAO,CAAC,GAAa,cAAc,kBAAkB,aAAa,MAAM,SAAS,SAAS,CAAC,GAAG,MAAM,YAAY,GAAG,EAAE,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC;AAAA,YAC7I,KAAK;AACD,iBAAG,KAAK;AACR,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,OAAM,UAAU,iBAAiB,SAAU,MAAM,OAAO;AACpD,UAAI,IAAI;AACR,UAAI,KAAK,KAAK,OAAO,gBAAgB,GAAG,eAAe,kBAAkB,GAAG,iBAAiB,QAAQ,GAAG,OAAO,OAAO,GAAG;AACzH,aAAO,cAAc,YAAY,aAAa,MAAM;AAAA,QAChD,SAAS,KAAK;AAAA,QACd,MAAM,KAAK;AAAA,QACX,OAAO,SAAS,GAAG,QAAQ,KAAK,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,KAAK,OAAO,IAAI,OAAO,QAAQ,OAAO,SAAS,KAAK,KAAK,KAAK,GAAG,EAAE;AAAA,QACrL,YAAY,KAAK,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,KAAK,MAAM,IAAI,OAAO,QAAQ,OAAO,SAAS,KAAK,KAAK;AAAA,MAC/J,CAAC,CAAC;AAAA,IACN;AACA,IAAAA,OAAM,UAAU,mBAAmB,SAAU,MAAM,OAAO;AACtD,UAAI,IAAI;AACR,UAAI,KAAK,KAAK,OAAO,gBAAgB,GAAG,eAAe,kBAAkB,GAAG,iBAAiB,QAAQ,GAAG,OAAO,OAAO,GAAG;AACzH,aAAO,cAAc,cAAc,aAAa,MAAM;AAAA,QAClD,MAAM,KAAK;AAAA,QACX,OAAO,SAAS,GAAG,QAAQ,KAAK,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,KAAK,OAAO,IAAI,OAAO,QAAQ,OAAO,SAAS,KAAK,KAAK,KAAK,GAAG,EAAE;AAAA,QACrL,YAAY,KAAK,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,KAAK,MAAM,IAAI,OAAO,QAAQ,OAAO,SAAS,KAAK,KAAK;AAAA,MAC/J,CAAC,CAAC;AAAA,IACN;AACA,IAAAA,OAAM,UAAU,sBAAsB,SAAU,MAAM,OAAO;AACzD,UAAI,IAAI;AACR,UAAI,KAAK,KAAK,OAAO,gBAAgB,GAAG,eAAe,kBAAkB,GAAG,iBAAiB,QAAQ,GAAG,OAAO,OAAO,GAAG;AACzH,aAAO,cAAc,iBAAiB,aAAa,MAAM;AAAA,QACrD,MAAM,KAAK;AAAA,QACX,OAAO,SAAS,GAAG,QAAQ,KAAK,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,KAAK,OAAO,IAAI,OAAO,QAAQ,OAAO,SAAS,KAAK,KAAK,KAAK,GAAG,EAAE;AAAA,QACrL,YAAY,KAAK,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,KAAK,MAAM,IAAI,OAAO,QAAQ,OAAO,SAAS,KAAK,KAAK;AAAA,MAC/J,CAAC,CAAC;AAAA,IACN;AACA,IAAAA,OAAM,UAAU,sBAAsB,SAAU,MAAM,OAAO;AACzD,UAAI,IAAI;AACR,UAAI,KAAK,KAAK,OAAO,gBAAgB,GAAG,eAAe,kBAAkB,GAAG,iBAAiB,QAAQ,GAAG,OAAO,OAAO,GAAG;AACzH,aAAO,cAAc,iBAAiB,aAAa,MAAM;AAAA,QACrD,MAAM,KAAK;AAAA,QACX,OAAO,SAAS,GAAG,QAAQ,KAAK,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,KAAK,OAAO,IAAI,OAAO,QAAQ,OAAO,SAAS,KAAK,KAAK,KAAK,GAAG,EAAE;AAAA,QACrL,YAAY,KAAK,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,KAAK,MAAM,IAAI,OAAO,QAAQ,OAAO,SAAS,KAAK,KAAK;AAAA,MAC/J,CAAC,CAAC;AAAA,IACN;AACA,IAAAA,OAAM,UAAU,iBAAiB,WAAY;AACzC,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,IAAI,OAAO,MAAM;AACrB,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,mBAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,OAAO,GAAG,MAAM,gBAAgB,GAAG;AACtE,oBAAM,UAAU;AAChB,mBAAK,aAAa;AAClB,qBAAO,CAAC,GAAa,cAAc,kBAAkB,aAAa,MAAM,SAAS,CAAC,GAAG,MAAM,YAAY,CAAC,CAAC,CAAC;AAAA,YAC9G,KAAK;AACD,iBAAG,KAAK;AACR,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,OAAM,UAAU,oBAAoB,SAAU,MAAM,QAAQ,iBAAiB,cAAc,SAAS;AAChG,UAAI,CAAC,QAAQ,IAAI,GAAG;AAChB;AAAA,MACJ;AACA,UAAI,KAAK,KAAK,OAAO,SAAS,GAAG,QAAQ,mBAAmB,GAAG,kBAAkB,uBAAuB,GAAG,iBAAiB,eAAe,GAAG,cAAc,eAAe,GAAG;AAC9K,WAAK,OAAO,QAAQ,YAAY;AAchC,WAAK,YAAY,KAAK,aAAa;AACnC,UAAI,cAAc;AACd,6BAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,KAAK,MAAM,KAAK,IAAI;AACzG;AAAA,MACJ;AACA,uBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,KAAK,MAAM,WAAW,KAAK,MAAM,KAAK,UAAU,CAAC,MAAM,YAAY,CAAC,GAAG,KAAK,IAAI;AACzJ,UAAI,CAAC,mBAAmB,CAAC,sBAAsB;AAC3C;AAAA,MACJ,WACS,mBAAmB,gBAAgB,KAAK;AAC7C,aAAK,MAAM,SAAS,MAAM;AAAA,UACtB,YAAY;AAAA,UACZ,KAAK,gBAAgB;AAAA,UACrB,QAAQ,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,QACtE,GAAG,KAAK,MAAM;AACd;AAAA,MACJ;AACA,UAAI,CAAC,QAAQ;AACT;AAAA,MACJ;AACA,aAAO,KAAK,MAAM,WAAW,KAAK,MAAM,KAAK,UAAU,CAAC,MAAM,YAAY,CAAC,GAAG,KAAK,MAAM,QAAW,KAAK,UAAU,OAAO;AAAA,IAC9H;AACA,IAAAA,OAAM,UAAU,aAAa,WAAY;AACrC,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,IAAI,OAAO,QAAQ,cAAc,UAAU,QAAQ,cAAc,QAAQ,MAAM,YAAY,MAAM;AACrG,YAAI,QAAQ;AACZ,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,mBAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,SAAS,GAAG,QAAQ,eAAe,GAAG;AACzE,kBAAI,CAAC,UAAU,CAAC,MAAM,aAAa,QAAQ;AACvC,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,yBAAW,CAAC;AACZ,qBAAO,KAAK,KAAK,QAAQ,EAAE,QAAQ,SAAU,KAAK;AAAE,uBAAO,MAAM,SAAS,GAAG,KAAK,SAAS,KAAK,MAAM,SAAS,GAAG,CAAC;AAAA,cAAG,CAAC;AACvH,kBAAI,CAAC,SAAS;AAAQ,uBAAO,CAAC,GAAa,CAAC;AAC5C,qBAAO,CAAC,GAAa,QAAQ,IAAI,SAAS,IAAI,SAAU,MAAM;AAAE,uBAAO,KAAK,SAAS;AAAA,cAAG,CAAC,CAAC,CAAC;AAAA,YAC/F,KAAK;AACD,uBAAS,GAAG,KAAK;AACjB,kBAAI,CAAC,OAAO,QAAQ,KAAK,GAAG;AACxB,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,iBAAG,QAAQ;AAAA,YACf,KAAK;AACD,6BAAe,MAAM,SAAS,OAAO,SAAU,MAAM;AAAE,wBAAQ,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,eAAe;AAAA,cAAiB,CAAC;AACjJ,kBAAI,CAAC,aAAa;AAAQ,uBAAO,CAAC,GAAa,CAAC;AAChD,qBAAO,CAAC,GAAa,QAAQ,IAAI,aAAa,IAAI,SAAU,MAAM;AAC1D,oBAAI,MAAM,CAAC;AACX,oBAAI,KAAK,YAAY,MAAM,KAAK,KAAK,QAAQ,GAAG;AAC5C,wBAAM,MAAM,KAAK,KAAK,QAAQ,EAAE;AAAA,gBACpC;AACA,uBAAO,KAAK,SAAS,GAAG;AAAA,cAC5B,CAAC,CAAC,CAAC;AAAA,YACX,KAAK;AACD,uBAAS,GAAG,KAAK;AACjB,kBAAI,CAAC,OAAO,QAAQ,KAAK,GAAG;AACxB,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,iBAAG,QAAQ;AAAA,YACf,KAAK;AACD,qBAAO,MAAM,aAAa,IAAI,SAAU,MAAM;AAAE,uBAAO,KAAK;AAAA,cAAM,CAAC;AACnE,2BAAa,MAAM,aAAa,IAAI,SAAU,MAAM;AAAE,uBAAO,KAAK;AAAA,cAAM,CAAC;AACzE,qBAAO,MAAM,aAAa,IAAI,SAAU,MAAM;AAC1C,uBAAO,WAAW,KAAK,MAAM,KAAK,UAAU,CAAC,MAAM,YAAY,CAAC;AAAA,cACpE,CAAC;AACD,+BAAiB,MAAM,KAClB,OAAO,SAAU,MAAM;AAAE,uBAAO,CAAC,KAAK;AAAA,cAAU,CAAC,EACjD,IAAI,SAAU,MAAM;AAAE,uBAAO,KAAK;AAAA,cAAM,CAAC;AAC9C,qBAAO,CAAC,GAAc,OAAO,MAAM,MAAM,YAAY,gBAAgB,MAAM,aAAa,IAAI,SAAU,MAAM;AAAE,uBAAO,KAAK;AAAA,cAAU,CAAC,CAAC,CAAC;AAAA,UAC/I;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,OAAM,UAAU,kBAAkB,WAAY;AAC1C,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,IAAI,OAAO,aAAa,MAAM,eAAe,YAAY,OAAO;AACpE,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,mBAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,cAAc,GAAG,aAAa,OAAO,GAAG,MAAM,gBAAgB,GAAG;AACpG,2BAAa,MAAM,UAAU,IAAI,SAAU,MAAM;AAAE,uBAAO,KAAK;AAAA,cAAM,CAAC;AACtE,sBAAQ,MAAM,KAAK,IAAI,SAAU,MAAM;AAAE,uBAAO,KAAK,2BAA2B;AAAA,cAAG,CAAC;AACpF,qBAAO,CAAC,GAAa,cAAc,eAAe,aAAa,MAAM,EAAE,WAAuB,CAAC,CAAC,CAAC;AAAA,YACrG,KAAK;AACD,8BAAgB,GAAG,KAAK;AACxB,kBAAI,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,WAAW;AACvF,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,kBAAI,CAAC,eAAe,CAAC,MAAM,UAAU,QAAQ;AACzC,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,0BAAY,YAAY,KAAK;AAC7B,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,OAAM,UAAU,eAAe,WAAY;AACvC,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,WAAW,GAAG;AACrD,kBACI,SAAS,MAAM,aAAa,IAAI,SAAU,MAAM;AAAE,eAAO,KAAK;AAAA,MAAM,CAAC,GAAG,MAAM,eAAe,IAAI,SAAU,MAAM;AAAE,eAAO,KAAK;AAAA,MAAM,CAAC,CAAC;AAAA,IAC/I;AACA,IAAAA,OAAM,UAAU,QAAQ,WAAY;AAChC,UAAI,QAAQ;AACZ,UAAI,QAAQ,KAAK,MAAM;AACvB,YAAM,MAAM;AACZ,UAAI,WAAW,CAAC;AAChB,aAAO,KAAK,KAAK,QAAQ,EAAE,QAAQ,SAAU,KAAK;AAAE,eAAO,MAAM,SAAS,GAAG,KAAK,SAAS,KAAK,MAAM,SAAS,GAAG,CAAC;AAAA,MAAG,CAAC;AACvH,eAAS,QAAQ,SAAU,MAAM;AAAE,eAAO,KAAK,YAAY;AAAA,MAAG,CAAC;AAE/D,UAAI,eAAe,MAAM,SAAS,OAAO,SAAU,MAAM;AAAE,gBAAQ,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,eAAe;AAAA,MAAiB,CAAC;AACrJ,UAAI,aAAa,QAAQ;AACrB,qBAAa,IAAI,SAAU,MAAM;AAAE,iBAAO,KAAK,MAAM;AAAA,QAAG,CAAC;AAAA,MAC7D;AAAA,IACJ;AACA,IAAAA,OAAM,UAAU,aAAa,SAAU,OAAO,OAAO;AACjD,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,eAAe,GAAG;AACzD,UAAI,gBAAgB,MAAM,KAAK;AAC3B,YAAI,QAAQ,MAAM,IAAI,MAAM,GAAG;AAC/B,YAAI,OAAO,MAAM,KAAK,OAAO,SAAU,MAAM;AACzC,qBAAO,YAAAM,SAAK,OAAO,SAAU,IAAI;AAAE,mBAAO,MAAM,MAAM,KAAK,KAAK,YAAY;AAAA,UAAG,CAAC;AAAA,QACpF,CAAC;AACD,YAAI,aAAa,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG,EAAE,KAAK,OAAU,CAAC;AACjE,aAAK,QAAQ,SAAU,KAAK;AAAE,iBAAO,IAAI,OAAO,UAAU;AAAA,QAAG,CAAC;AAAA,MAClE,WACS,MAAM,QAAQ,KAAK,GAAG;AAC3B,YAAI,OAAO,MAAM,KAAK,OAAO,SAAU,MAAM;AAAE,iBAAO,CAAC,MAAM,QAAQ,KAAK,QAAQ;AAAA,QAAG,CAAC;AACtF,aAAK,QAAQ,SAAU,KAAK;AAAE,iBAAO,IAAI,OAAO,KAAK;AAAA,QAAG,CAAC;AAAA,MAC7D;AAAA,IACJ;AACA,IAAAN,OAAM,UAAU,cAAc,WAAY;AACtC,UAAI,QAAQ,KAAK,MAAM;AACvB,aAAO,MAAM,aAAa,IAAI,SAAU,MAAM;AAAE,eAAO,KAAK;AAAA,MAAM,CAAC;AAAA,IACvE;AACA,IAAAA,OAAM,UAAU,kBAAkB,SAAU,UAAU;AAClD,UAAI,KAAK,YAAY;AACjB;AAAA,MACJ;AACA,WAAK,MAAM,MAAM,eAAe;AAChC,WAAK,MAAM,MAAM,eAAe;AAChC,WAAK,mBAAmB;AACxB,kBAAY,WAAW,UAAU,EAAE;AAAA,IACvC;AAEA,IAAAA,OAAM,UAAU,qBAAqB,WAAY;AAC7C,UAAI,QAAQ,KAAK;AACjB,UAAI,CAAC,OAAO;AACR;AAAA,MACJ;AACA,UAAI,SAAS,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM;AACjE,UAAI,aAAa,OAAO;AACxB,UAAI,KAAK,cAAc;AACnB,aAAK,aAAa,cAAc,aAAa;AAAA,MACjD;AACA,UAAI,KAAK,MAAM,MAAM,gBAAgB,KAAK,SAAU,QAAQ;AAAE,eAAO,OAAO;AAAA,MAAO,CAAC,GAAG;AACnF,YAAI,YAAY,eAAe;AAC/B,YAAI,aAAa,KAAK,KAAK,UAAU,IAAI,OAAO,eAAe,MAAM;AACrE,SAAC,OAAO,KAAK,YAAY,EACpB,OAAO,SAAU,MAAM;AAAE,iBAAO;AAAA,QAAM,CAAC,EACvC,QAAQ,SAAUO,QAAO;AAC1B,UAAAA,OAAM,UAAU,OAAO,oBAAoB,mBAAmB;AAC9D,uBAAaA,OAAM,UAAU,IAAI,kBAAkB;AACnD,wBAAcA,OAAM,UAAU,IAAI,mBAAmB;AAAA,QACzD,CAAC;AAAA,MACL;AAAA,IACJ;AACA,IAAAP,OAAM,UAAU,WAAW,SAAU,KAAK;AACtC,UAAI,QAAQ;AACZ,UAAI;AACJ,WAAK,QAAQ;AACb,cAAQ,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM,SAAS,GAAG;AAC1D,OAAC,KAAK,KAAK,wBAAwB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI;AAChF,UAAI,KAAK;AACL,aAAK,mBAAmB;AACxB,aAAK,qBAAqB,aAAa,KAAK,WAAY;AACpD,gBAAM,mBAAmB;AAAA,QAC7B,CAAC;AAAA,MACL;AAAA,IACJ;AACA,IAAAA,OAAM,UAAU,aAAa,SAAU,KAAK;AACxC,UAAI,CAAC,KAAK,WAAW,KAAK;AACtB,aAAK,aAAa;AAAA,MACtB,WACS,KAAK,WAAW,CAAC,KAAK;AAC3B,aAAK,gBAAgB;AAAA,MACzB;AACA,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,OAAM,UAAU,kBAAkB,SAAU,KAAK;AAC7C,WAAK,eAAe;AACpB,aAAO,KAAK,mBAAmB;AAAA,IACnC;AACA,IAAAA,OAAM,UAAU,eAAe,WAAY;AACvC,UAAI,QAAQ;AACZ,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,KAAK,GAAG;AAC/C,WAAK,WAAW,IAAI,qBAAS,KAAK,MAAM,cAAc,cAAc,GAAG;AAAA,QACnE,OAAO;AAAA,QACP,WAAW;AAAA,QACX,QAAQ,IAAI,OAAO,IAAI,gBAAgB;AAAA,QACvC,QAAQ,IAAI,OAAO,IAAI,gCAAgC;AAAA,QACvD,YAAY;AAAA,QACZ,OAAO,SAAU,GAAG;AAAE,iBAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AACtE,gBAAI;AACJ,mBAAO,YAAY,MAAM,SAAUQ,KAAI;AAEnC,kBAAI,EAAE,aAAa,EAAE,UAAU;AAC3B,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,uBAAS,EAAE;AACX,kBAAI,EAAE,WAAW,OAAO,WAAW,SAAS,GAAG;AAC3C,uBAAO,aAAa,EAAE,MAAM,OAAO,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,IAAI,EAAE,QAAQ,CAAC;AAAA,cACxG,OACK;AACD,uBAAO,YAAY,EAAE,IAAI;AAAA,cAC7B;AACA,oBAAM,SAAS,EAAE,UAAU,EAAE,QAAQ;AACrC,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,YACxB,CAAC;AAAA,UACL,CAAC;AAAA,QAAG;AAAA,MACR,CAAC;AAAA,IACL;AACA,IAAAR,OAAM,UAAU,kBAAkB,WAAY;AAC1C,WAAK,YAAY,KAAK,SAAS,QAAQ;AAAA,IAC3C;AACA,IAAAA,OAAM,UAAU,sBAAsB,WAAY;AAC9C,aAAO,KAAK,IAAI;AAAA,IACpB;AACA,IAAAA,OAAM,UAAU,kBAAkB,SAAU,GAAG;AAC3C,UAAI,KAAK,EAAE,OAAO,QAAQ,aAAa;AACvC,UAAI,CAAC,IAAI;AACL;AAAA,MACJ;AACA,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,eAAe,GAAG,cAAc,cAAc,GAAG;AASxF,UAAI,KAAK,GAAG,aAAa,SAAS;AAClC,UAAI,MAAM,MAAM;AAChB,WAAK,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,QAAQ,IAAI;AAC3D;AAAA,MACJ;AACA,eAAS,MAAM,MAAM,SAAU,MAAM;AAAE,eAAO,KAAK,WAAW,KAAK,OAAO,EAAE;AAAA,MAAG,CAAC;AAAA,IACpF;AACA,IAAAA,OAAM,UAAU,mBAAmB,WAAY;AAC3C,UAAI,QAAQ,KAAK,MAAM;AACvB,UAAI,MAAM,MAAM;AAChB,cAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,WAAW,KAAK;AAAA,IAClE;AACA,IAAAA,OAAM,UAAU,kBAAkB,SAAU,GAAG;AAC3C,UAAI,QAAQ,KAAK,MAAM;AACvB,UAAI,SAAS,EAAE;AACf,UAAI,KAAM,KAAK,aAAa,OAAO,QAAQ,IAAI;AAC/C,UAAI,KAAK,GAAG,aAAa,SAAS;AAClC,UAAI,QAAQ,GAAG;AACf,WAAK,cAAc,MAAM,UAAU,QAAQ,KAAK,MAAM,YAAY,EAAE;AACpE,YAAM,UAAU,IAAI,aAAa;AACjC,SAAG,UAAU,IAAI,aAAa;AAC9B,QAAE,aAAa,gBAAgB;AAC/B,QAAE,aAAa,QAAQ,cAAc,EAAE;AACvC,QAAE,aAAa,aAAa,IAAI,GAAG,CAAC;AACpC,UAAI,OAAO,MAAM,WAAW,EAAE;AAC9B,YAAM,mBAAmB,KAAK,KAAK;AACnC,UAAI,WAAW,MAAM;AACrB,UAAI,KAAK,UAAU;AACf,YAAI,WAAW,MAAM,WAAW,KAAK,QAAQ;AAC7C,mBAAW,SAAS;AAAA,MACxB;AACA,iBAAW,SAAS,OAAO,SAAU,SAAS;AAAE,eAAO,YAAY;AAAA,MAAM,CAAC;AAC1E,YAAM,iBAAiB,YAAY,KAAK,cAAc;AACtD,YAAM,iBAAiB,QAAQ,KAAK,UAAU;AAC9C,WAAK,kBAAkB,SAAS,IAAI,SAAUS,OAAM;AAChD,YAAIC,MAAK,MAAM,cAAc,sBAAuB,OAAOD,MAAK,IAAI,IAAK,CAAC;AAC1E,QAAAC,IAAG,UAAU,IAAI,iBAAiB;AAClC,eAAOA;AAAA,MACX,CAAC;AACD,SAAG,iBAAiB,WAAW,KAAK,aAAa;AAAA,IACrD;AACA,IAAAV,OAAM,UAAU,iBAAiB,SAAU,GAAG;AAC1C,UAAI,CAAC,EAAE,QAAQ;AACX;AAAA,MACJ;AACA,QAAE,eAAe;AACjB,QAAE,aAAa,aAAa;AAC5B,UAAI,SAAS,EAAE,OAAO,QAAQ,IAAI;AAClC,UAAI,CAAC,UACD,CAAC,CAAC,OAAO,UAAU,QAAQ,iBAAiB,KAC5C,WAAW,KAAK,cAChB,UAAU,WAAW;AACrB;AAAA,MACJ;AACA,UAAI,QAAQ,OAAO;AACnB,UAAI,QAAQ,OAAO,sBAAsB;AACzC,UAAI,QAAQ,EAAE,UAAU,MAAM,QAAQ,MAAM,SAAS,MAAM,OAAO;AAClE,gBAAU,QAAQ,KAAK;AACvB,UAAI,SAAS,OAAO,OAAO,cAAc;AACzC,eACM,MAAM,aAAa,KAAK,YAAY,MAAM,IAC1C,MAAM,YAAY,KAAK,UAAU;AACvC,gBAAU,WAAW;AAAA,IACzB;AACA,IAAAA,OAAM,UAAU,aAAa,WAAY;AACrC,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,OAAO,IAAI,OAAO,OAAO;AAC7B,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,KAAK,MAAM;AACnB,eAAK,KAAK;AACV,kBAAQ,GAAG;AACX,kBAAQ,MAAM,UAAU,QAAQ,KAAK,MAAM,YAAY,EAAE;AACzD,iBAAO,MAAM,WAAW,GAAG,aAAa,SAAS,CAAC;AAElD,eAAK,cAAc;AACnB,gBAAM,SAAS,KAAK,aAAa,OAAO,IAAI;AAC5C,iBAAO;AAAA,YAAC;AAAA;AAAA,UAAY;AAAA,QACxB,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,OAAM,UAAU,gBAAgB,WAAY;AACxC,UAAI,KAAK,KAAK;AACd,UAAI,QAAQ,GAAG;AACf,UAAI,QAAQ,MAAM,UAAU,QAAQ,KAAK,MAAM,YAAY,EAAE;AAC7D,YAAM,aAAa,IAAI,MAAM,WAAW,QAAQ,KAAK,cAAc,KAAK,cAAc,IAAI,KAAK,WAAW,CAAC;AAC3G,SAAG,UAAU,OAAO,aAAa;AACjC,YAAM,UAAU,OAAO,aAAa;AACpC,SAAG,oBAAoB,WAAW,KAAK,aAAa;AACpD,YAAM,oBAAoB,YAAY,KAAK,cAAc;AACzD,YAAM,oBAAoB,QAAQ,KAAK,UAAU;AACjD,WAAK,gBAAgB,QAAQ,SAAU,MAAM;AACzC,eAAO,KAAK,UAAU,OAAO,iBAAiB;AAAA,MAClD,CAAC;AAAA,IACL;AACA,IAAAA,OAAM,UAAU,qBAAqB,SAAU,MAAM,QAAQ;AACzD,UAAI,iBAAiB,KAAK,MAAM;AAEhC,UAAK,MAAM,QAAQ,KAAK,IAAI,KAAK,KAAK,uBAAuB,QACzD,KAAK,uBAAuB,OAAO;AACnC,eAAO,kBAAkB,eAAe,MAAM,MAAM;AAAA,MACxD;AAEA,UAAI,QAAQ,KAAK,MAAM;AACvB,UAAI,SAAS,MAAM,QAAQ,OAAO,QAAQ,EAAE;AAC5C,UAAI,QAAQ,OAAO;AACnB,UAAI,OAAO,CAAC;AACZ,YAAM,KAAK,QAAQ,SAAU,KAAK,GAAG;AACjC,YAAI,MAAM,gBAAgB,OAAO,MAAM,IAAI,IAAI;AAC/C,YAAI,CAAC,KAAK;AACN,cAAI,IAAI,OAAO,UAAU;AACrB;AAAA,UACJ;AACA;AAAA,QACJ;AACA,YAAI,SAAS,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,GAAG;AAC5C,eAAO,KAAK,OAAO,OAAO,IAAI,SAAU,MAAM;AAAE,iBAAQ;AAAA,YACpD,KAAK;AAAA,YACL,aAAa,OAAO,cACd,OAAO,OAAO,aAAa,IAAI,IAAI,IACnC;AAAA,YACN,OAAO,OAAO,eACR,OAAO,OAAO,cAAc,IAAI,IAAI,IACpC,OAAO,QACH,OAAO,OAAO,OAAO,IAAI,IAAI,IAC7B;AAAA,YACV,SAAS,OAAO,iBACV,OAAO,OAAO,gBAAgB,IAAI,IAAI,IACtC,OAAO,UACH,OAAO,OAAO,SAAS,IAAI,IAAI,IAC/B;AAAA,UACd;AAAA,QAAI,CAAC,CAAC;AAAA,MACV,CAAC;AACD,UAAI,KAAK,SAAS,GAAG;AACjB,0BACI,eAAe,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG,EAAE,MAAY,MAAa,CAAC,GAAG,MAAM;AAAA,MACzF,OACK;AACD,0BAAkB,eAAe,MAAM,MAAM;AAAA,MACjD;AAAA,IACJ;AAEA,IAAAA,OAAM,UAAU,2BAA2B,SAAU,GAAG;AACpD,WAAK,aAAa,EAAE;AACpB,UAAI,gBAAgB,EAAE;AACtB,WAAK,aAAa;AAClB,UAAI,QAAQ,KAAK,MAAM;AACvB,UAAI,QAAQ,SAAS,KAAK,WAAW,aAAa,YAAY,GAAG,EAAE;AACnE,UAAI,SAAS,MAAM,QAAQ,KAAK;AAChC,WAAK,iBAAiB,OAAO,aAAa,OAAO;AACjD,WAAK,WAAW,UAAU,IAAI,aAAa;AAC3C,eAAS,iBAAiB,aAAa,KAAK,wBAAwB;AACpE,eAAS,iBAAiB,WAAW,KAAK,sBAAsB;AAEhE,QAAE,eAAe;AACjB,QAAE,gBAAgB;AAAA,IACtB;AAEA,IAAAA,OAAM,UAAU,2BAA2B,SAAU,GAAG;AACpD,UAAI,QAAQ,EAAE,UAAU,KAAK;AAC7B,UAAI,QAAQ,KAAK,MAAM;AACvB,UAAI,QAAQ,SAAS,KAAK,WAAW,aAAa,YAAY,GAAG,EAAE;AACnE,UAAI,SAAS,MAAM,QAAQ,KAAK;AAChC,aAAO,SAAS,KAAK,IAAI,KAAK,iBAAiB,OAAO,IAAI,OAAO,QAAQ,CAAC;AAAA,IAC9E;AAEA,IAAAA,OAAM,UAAU,yBAAyB,SAAU,GAAG;AAClD,WAAK,WAAW,UAAU,OAAO,aAAa;AAC9C,aAAO,KAAK;AACZ,eAAS,oBAAoB,aAAa,KAAK,wBAAwB;AACvE,eAAS,oBAAoB,WAAW,KAAK,sBAAsB;AAAA,IACvE;AACA,IAAAA,OAAM,UAAU,qBAAqB,SAAU,SAAS;AACpD,UAAI,QAAQ,KAAK,MAAM;AACvB,YAAM,cAAc,OAAO;AAC3B,YAAM,0BAA0B;AAAA,IACpC;AACA,IAAAA,OAAM,UAAU,uBAAuB,WAAY;AAC/C,UAAI,KAAK,KAAK,OAAO,SAAS,GAAG,QAAQ,QAAQ,GAAG,OAAO,wBAAwB,GAAG,uBAAuB,yBAAyB,GAAG,wBAAwB,uBAAuB,GAAG,sBAAsB,KAAK,GAAG,YAAY,KAAK,GAAG,WAAW,QAAQ,GAAG,OAAO,OAAO,GAAG,MAAM,qBAAqB,GAAG,oBAAoB,gBAAgB,GAAG,eAAe,KAAK,GAAG,0BAA0B,2BAA2B,OAAO,SAAS,OAAO;AAC5b,UAAI,oBAAoB,MAAM;AAC9B,UAAI,CAAC,kBAAkB,QAAQ;AAC3B,eAAO;AAAA,MACX;AACA,aAAQ,eAAAC,QAAM,cAAc,kBAAgB,EAAE,OAAc,OAAc,MAAY,WAAW,IAAI,YAAY,IAAI,QAAgB,oBAAoB,0BAA0B,oBAAwC,uBAA8C,wBAAgD,sBAA4C,kBAAkB,KAAK,qBAAqB,eAAe,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,SAAS,QAAQ,EAAE,CAAC;AAAA,IACpgB;AACA,IAAAD,OAAM,UAAU,gBAAgB,WAAY;AACxC,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,QAAQ,GAAG,OAAO,mBAAmB,GAAG,kBAAkB,OAAO,GAAG,MAAM,KAAK,GAAG,YAAY,kBAAkB,GAAG,iBAAiB,mBAAmB,GAAG,kBAAkB,eAAe,GAAG,cAAc,KAAK,GAAG,WAAW,UAAU,GAAG;AAEnR,UAAI,kCAAkC;AACtC,UAAI,MAAM,aAAa,WAAW,GAAG;AACjC,YAAI,8BAA8B,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,IAAI,SAAU,QAAQ;AAAE,cAAIQ;AAAI,mBAASA,MAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,eAAe,QAAQA,QAAO,SAAS,SAASA,IAAG,mBAAmB,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,OAAO;AAAA,QAAI,CAAC,EAAE,OAAO,SAAU,GAAG;AAAE,iBAAO;AAAA,QAAG,CAAC,MAAM,CAAC;AACpX,YAAI,OAAO,MAAM,aAAa,CAAC;AAC/B,YAAI,OAAO,WAAW,KAAK,MAAM,KAAK,QAAQ;AAC9C,gBAAI,oBAAAG,SAAa,4BAA4B,OAAO,KAAK,IAAI,CAAC,EAAE,QAAQ;AACpE,4CAAkC;AAAA,QACtC;AAAA,MACJ;AACA,UAAI,SACC,gBACG,CAAC,mBACD,CAAC,mCACD,MAAM,YACN,CAAC,oBACL,MAAM,OAAO;AACb,eAAQ,eAAAV,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,iBAAiB,gBAAgB,GAAG,KAAK,UAAU,GAAG,CAAC,mBACtG,MAAM,YACN,CAAC,oBACD,CAAC,kCAAmC,eAAAA,QAAM;AAAA,UAAc;AAAA,UAAQ;AAAA,UAChE,GAAG,kBAAkB;AAAA,YACjB,UAAU,MAAM;AAAA,UACpB,CAAC;AAAA,UACD,eAAAA,QAAM;AAAA,YAAc;AAAA,YAAU,EAAE,MAAM,UAAU,WAAW,GAAG,+CAA+C,GAAG,SAAS,KAAK,WAAW;AAAA,YACrI,eAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,SAAS,WAAW,cAAc,CAAC;AAAA,YACrE,GAAG,aAAa;AAAA,UAAC;AAAA,UACrB,eAAAA,QAAM;AAAA,YAAc;AAAA,YAAU,EAAE,MAAM,UAAU,WAAW,GAAG,8CAA8C,GAAG,SAAS,KAAK,MAAM;AAAA,YAC/H,eAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,SAAS,WAAW,cAAc,CAAC;AAAA,YACrE,GAAG,eAAe;AAAA,UAAC;AAAA,QAAC,IAAK,MAAM,QAAS,eAAAA,QAAM;AAAA,UAAc;AAAA,UAAQ;AAAA,UACxE,GAAG,eAAe;AAAA,YACd,OAAO,MAAM;AAAA,UACjB,CAAC;AAAA,UACD,eAAAA,QAAM;AAAA,YAAc;AAAA,YAAU,EAAE,MAAM,UAAU,WAAW,GAAG,0CAA0C,GAAG,SAAS,KAAK,gBAAgB;AAAA,YACrI,eAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,SAAS,WAAW,cAAc,CAAC;AAAA,YACrE,GAAG,aAAa;AAAA,UAAC;AAAA,UACrB,eAAAA,QAAM;AAAA,YAAc;AAAA,YAAU,EAAE,MAAM,UAAU,WAAW,GAAG,yCAAyC,GAAG,SAAS,KAAK,MAAM;AAAA,YAC1H,eAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,SAAS,WAAW,cAAc,CAAC;AAAA,YACrE,GAAG,eAAe;AAAA,UAAC;AAAA,QAAC,IAAK,QAAS,OAAO,OAAO,IAAI,IAAM,EAAG;AAAA,MACzE;AACA,aAAO;AAAA,IACX;AACA,IAAAD,OAAM,UAAU,iBAAiB,SAAU,QAAQ,OAAO;AACtD,UAAI,QAAQ;AACZ,UAAI,IAAI;AACR,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,QAAQ,GAAG,OAAO,UAAU,GAAG,SAAS,SAAS,GAAG,QAAQ,KAAK,GAAG,aAAa,YAAY,GAAG,WAAW,KAAK,GAAG,YAAY,qBAAqB,GAAG,oBAAoB,gBAAgB,GAAG,eAAe,OAAO,GAAG,MAAM,gBAAgB,GAAG,eAAe,KAAK,GAAG;AAG9S,UAAI,UAAU,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,SAAS,aAAa,OAAO,OAAO,IAAI,CAAC;AACnI,UAAI,QAAQ,SAAS,CAAC,GAAG,MAAM,KAAK;AACpC,UAAI,KAAK,OAAO,MAAM,gBAAgB,QAAQ,MAAM,eAAe,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC;AACrH,aAAO,OAAO,OAAO,WAAW;AAChC,UAAI,aAAc,eAAAC,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,2BAA2B,GAAG,KAAK,UAAU,OAAO,OAAO,EAAE,GAAG,cAAc,OAAO,OAAO,aAAa,KAAK,yBAAyB,CAAC;AAErM,UAAI,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,OAAO;AAC3D,eAAO,MAAM;AAAA,MACjB;AACA,UAAI,OAAO,SAAS,aAAa;AAC7B,cAAM,YAAY,OAAO,SAAS;AAAA,MACtC,WACS,OAAO,SAAS,OAAO;AAC5B,cAAM,YAAY,OAAO,SAAS;AAAA,MACtC;AACA,UAAI,MAAM,MAAM,KAAK,YAAY,OAAO,OAAO,CAAC,KAAK,CAAC;AACtD,UAAI,OAAO,SAAS,aAAa;AAC7B,eAAQ,eAAAA,QAAM;AAAA,UAAc;AAAA,UAAM,SAAS,CAAC,GAAG,WAAW,EAAE,KAAU,OAAc,WAAW,GAAG,OAAO,SAAS,WAAW,eAAe,EAAE,CAAC;AAAA,UAC3I,MAAM,KAAK,UAAU,MAAM,WAAY,eAAAA,QAAM,cAAc,YAAU,EAAE,aAAa,IAAI,SAAS,MAAM,eAAe,CAAC,MAAM,YAAY,SAAS,MAAM,aAAa,UAAU,MAAM,+BAA+B,CAAC,MAAM,aAAa,UAAU,KAAK,eAAe,CAAC,IAAM;AAAA,UAC7Q,cAAc,QAAQ,OAAO;AAAA,QAAU;AAAA,MAC/C,WACS,OAAO,SAAS,YAAY;AACjC,eAAQ,eAAAA,QAAM,cAAc,MAAM,SAAS,CAAC,GAAG,WAAW,EAAE,KAAU,OAAc,WAAW,GAAG,OAAO,SAAS,WAAW,eAAe,EAAE,CAAC,CAAC;AAAA,MACpJ,WACS,OAAO,SAAS,cAAc;AACnC,eAAQ,eAAAA,QAAM;AAAA,UAAc;AAAA,UAAM,SAAS,CAAC,GAAG,WAAW,EAAE,KAAU,OAAc,WAAW,GAAG,OAAO,SAAS,WAAW,eAAe,EAAE,CAAC;AAAA,UAC1I,MAAM,aACF,MAAM,SAAS,cAAc,SAAS,MAAM,SAAS,cACrD,MAAM,iBACF,MAAM,aAAa,cAAc,SAC9B,MAAM,aAAa,aAAc,OAAQ,eAAAA,QAAM;AAAA,YAAc;AAAA,YAAK;AAAA,cAAE,WAAW,GAAG,mBAAmB,MAAM,cAAc,cAAc,EAAE;AAAA;AAAA;AAAA,cAGjJ,SAAS,MAAM;AAAA,YAAgB;AAAA,YAC/B,eAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,oBAAoB,WAAW,OAAO,CAAC;AAAA,UAAC;AAAA,UAC9E,cAAc,QAAQ,OAAO;AAAA,QAAU;AAAA,MAC/C,WACS,OAAO,SAAS,WAAW;AAChC,eAAQ,eAAAA,QAAM;AAAA,UAAc;AAAA,UAAM,SAAS,CAAC,GAAG,WAAW,EAAE,KAAU,OAAc,WAAW,GAAG,OAAO,SAAS,WAAW,eAAe,EAAE,CAAC;AAAA,UAC3I,GAAG,aAAa;AAAA,UAChB,cAAc,QAAQ,OAAO;AAAA,QAAU;AAAA,MAC/C;AACA,UAAI,SAAS,CAAC;AACd,UAAI,QAAQ,CAAC;AACb,UAAI,OAAO,aAAa,MAAM,UAAU;AACpC,QAAC,MAAM,aACF,MAAM,SAAS,cAAc,SAAS,MAAM,SAAS,cACrD,MAAM,iBACF,MAAM,aAAa,cAAc,SAC9B,MAAM,aAAa,cAC3B,OAAO,KAAK,eAAAA,QAAM;AAAA,UAAc;AAAA,UAAK;AAAA,YAAE,KAAK;AAAA,YAAa,WAAW,GAAG,oBAAoB,MAAM,cAAc,cAAc,EAAE;AAAA;AAAA;AAAA,YAG3H,SAAS,MAAM;AAAA,UAAgB;AAAA,UAC/B,eAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,oBAAoB,WAAW,OAAO,CAAC;AAAA,QAAC,CAAC;AAAA,MACvF;AACA,UAAI,OAAO,cAAc,OAAO,QAAQ,CAAC,oBAAoB;AACzD,cAAM,KAAK,eAAAA,QAAM,cAAc,wBAAwB,SAAS,CAAC,GAAG,WAAW,EAAE,KAAK,oBAAoB,GAAG,KAAK,OAAO,EAAE,SAAkB,MAAM,OAAO,MAAM,YAAY,OAAO,YAAY,MAAM,OAAO,MAAM,MAAM,OAAO,eAAe,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,SAAS,QAAQ,GAAG,kBAAkB,KAAK,oBAAoB,CAAC,CAAC,CAAC;AAAA,MAC9W;AACA,UAAI,OAAO,YAAY,OAAO,MAAM;AAChC,cAAM,KAAK,eAAAA,QAAM;AAAA,UAAc;AAAA,UAAQ,SAAS,CAAC,GAAG,WAAW,EAAE,KAAK,mBAAmB,WAAW,GAAG,mBAAmB,GAAG,SAAS,WAAY;AAAE,mBAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AAChM,kBAAI,SAAS,UAAU,OAAO;AAC9B,qBAAO,YAAY,MAAM,SAAUO,KAAI;AACnC,wBAAQA,IAAG,OAAO;AAAA,kBACd,KAAK;AACD,8BAAU;AACV,+BAAW;AACX,wBAAI,OAAO,SAAS,MAAM,SAAS;AAC/B,0BAAI,MAAM,aAAa,QAAQ;AAE3B,kCAAU,OAAO;AACjB,mCAAW;AAAA,sBACf;AAAA,oBACJ,OACK;AACD,gCAAU,OAAO;AAAA,oBACrB;AACA,4BAAQ,UAAW,WAAW,SAAS,QAAS;AAChD,2BAAO,CAAC,GAAa,cAAc,cAAc,aAAa,MAAM;AAAA,sBAC5D;AAAA,sBACA,UAAU;AAAA,oBACd,CAAC,CAAC,CAAC;AAAA,kBACX,KAAK;AACD,oCAAgBA,IAAG,KAAK;AACxB,wBAAI,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,WAAW;AACvF,6BAAO;AAAA,wBAAC;AAAA;AAAA,sBAAY;AAAA,oBACxB;AACA,wBAAI,CAAC,WACD,QAAQ;AAAA,sBACJ;AAAA,sBACA,UAAU;AAAA,oBACd,CAAC,MAAM,OAAO;AACd,4BAAM,YAAY,SAAS,KAAK;AAAA,oBACpC;AACA,2BAAO;AAAA,sBAAC;AAAA;AAAA,oBAAY;AAAA,gBAC5B;AAAA,cACJ,CAAC;AAAA,YACL,CAAC;AAAA,UAAG,EAAE,CAAC;AAAA,UACP,eAAAP,QAAM;AAAA,YAAc;AAAA,YAAK,EAAE,WAAW,GAAG,2BAA2B,MAAM,YAAY,OAAO,QAAQ,MAAM,aAAa,SAC9G,cACA,EAAE,EAAE;AAAA,YACV,eAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,aAAa,WAAW,OAAO,CAAC;AAAA,UAAC;AAAA,UACvE,eAAAA,QAAM;AAAA,YAAc;AAAA,YAAK,EAAE,WAAW,GAAG,yBAAyB,MAAM,YAAY,OAAO,QAAQ,MAAM,aAAa,QAC5G,cACA,EAAE,EAAE;AAAA,YACV,eAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,YAAY,WAAW,OAAO,CAAC;AAAA,UAAC;AAAA,UACtE,eAAAA,QAAM;AAAA,YAAc;AAAA,YAAK,EAAE,WAAW,GAAG,8BAA8B,MAAM,YAAY,OAAO,OAAO,KAAK,WAAW,EAAE;AAAA,YACrH,eAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,gBAAgB,WAAW,OAAO,CAAC;AAAA,UAAC;AAAA,QAAC,CAAC;AAAA,MACpF;AACA,UAAI,CAAC,OAAO,cAAc,OAAO,cAAc,OAAO,QAAQ,SAAS;AACnE,cAAM,KAAK,eAAAA,QAAM,cAAc,wBAAwB,SAAS,EAAE,KAAK,oBAAoB,GAAG,KAAK,OAAO,EAAE,SAAkB,MAAM,OAAO,MAAM,MAAM,OAAO,MAAM,MAAM,OAAO,WAAW,aAAa,MAAM,KAAK,GAAG,YAAY,OAAO,YAAY,kBAAkB,KAAK,qBAAqB,eAAe,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,SAAS,QAAQ,EAAE,CAAC,CAAC,CAAC;AAAA,MACrY;AACA,aAAQ,eAAAA,QAAM;AAAA,QAAc;AAAA,QAAM,SAAS,CAAC,GAAG,WAAW,EAAE,KAAU,OAAc,WAAW,GAAG,QAAQ,MAAM,YAAY,IAAI,iBAAiB;AAAA,UACzI,uBAAuB,OAAO;AAAA,UAC9B,yBAAyB,OAAO;AAAA,UAChC,yBAAyB,OAAO;AAAA,UAChC,uBAAuB,OAAO,SAAS;AAAA,QAC3C,CAAC,EAAE,GAAG,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU,CAAC;AAAA,QAC3E;AAAA,QACA,eAAAA,QAAM;AAAA,UAAc;AAAA,UAAO,EAAE,KAAK,WAAW,WAAW,GAAG,oBAAoB,OAAO,SAAS,WAAW,OAAO,SAAS,cAAc,GAAG,OAAO,MAAM,MAAM;AAAA,YACxJ,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,KAAK,OAAO,SACtD,OAAO,QAAQ,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,KAAK,OAAO,KAAK,IAC9E;AAAA,UACN,OAAO,SACD,OAAO,UAAU;AAAA,YACf,MAAM;AAAA,YACN,SAAS,OAAO;AAAA,YAChB,WAAW,KAAK;AAAA,UACpB,CAAC,IACC;AAAA,QAAI;AAAA,QACd;AAAA,QACA,cAAc,QAAQ,OAAO;AAAA,MAAU;AAAA,IAC/C;AACA,IAAAD,OAAM,UAAU,aAAa,SAAU,QAAQ,QAAQ,MAAM,OAAO,YAAY;AAC5E,UAAI,eAAe,QAAQ;AAAE,qBAAa;AAAA,MAAO;AACjD,UAAI,KAAK,KAAK,OAAO,SAAS,GAAG,QAAQ,QAAQ,GAAG,OAAO,KAAK,GAAG,aAAa,KAAK,GAAG,YAAY,qBAAqB,GAAG,oBAAoB,YAAY,GAAG,WAAW,YAAY,GAAG,WAAW,gBAAgB,GAAG,eAAe,kBAAkB,GAAG;AAC3P,aAAQ,eAAAC,QAAM,cAAc,MAAM,EAAE,KAAK,MAAM,KAAK,QAAgB,QAAgB,MAAY,OAAc,YAAwB,QAAgB,iBAAkC,OAAc,UAAU,MAAM,UAAU,oBAAwC,YAAY,IAAI,aAAa,IAAI,WAAsB,SAAS,KAAK,aAAa,aAAa,KAAK,iBAAiB,kBAAkB,KAAK,qBAAqB,kBAAkB,KAAK,YAAY,gBAAgB,KAAK,oBAAoB,WAAsB,eAAe,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,SAAS,QAAQ,OAAO,MAAM,SAAS,GAAG,EAAE,OAAO,OAAO,KAAK,CAAC,EAAE,CAAC;AAAA,IAC7qB;AACA,IAAAD,OAAM,UAAU,oBAAoB,SAAU,gBAAgB;AAC1D,UAAI,QAAQ;AACZ,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,cAAc,GAAG,aAAa,SAAS,GAAG,QAAQ,KAAK,GAAG,YAAY,iBAAiB,GAAG,gBAAgB,MAAM,GAAG;AAC1J,UAAI,aAAa,MAAM,gBAAgB,MAAM,SAAU,QAAQ;AAAE,eAAO,CAAC,OAAO;AAAA,MAAO,CAAC;AACxF,UAAI,eAAe,MAAM;AACzB,aAAO,eAAe,CAAC,iBAAkB,eAAAC,QAAM;AAAA,QAAc,eAAAA,QAAM;AAAA,QAAU;AAAA,QACzE,eAAAA,QAAM;AAAA,UAAc;AAAA,UAAO,EAAE,WAAW,GAAG,kBAAkB;AAAA,YACrD,eAAe;AAAA,UACnB,CAAC,EAAE;AAAA,UACH,KAAK,aAAa,KAAK;AAAA,UACvB,KAAK,cAAc;AAAA,UACnB,MAAM,mBAAoB,eAAAA,QAAM;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,GAAG,eAAe,EAAE;AAAA,YAClF,eAAAA,QAAM;AAAA,cAAc;AAAA,cAAS,EAAE,KAAK,KAAK,iBAAiB,WAAW,GAAG,gBAAgB,MAAM,gBAAgB,UAAU,oBAAoB,EAAE,EAAE;AAAA,cAC5I,eAAAA,QAAM,cAAc,YAAY,MAAM,MAAM,gBAAgB,IAAI,SAAU,QAAQ;AAC9E,oBAAI,QAAQ;AAAA,kBACR,OAAO,sBAAsB,OAAO,OAAO,OAAO,SAAS;AAAA,gBAC/D;AACA,oBAAI,MAAM,gBAAgB,QAAQ;AAC9B,wBAAM,WAAW,MAAM;AAAA,gBAC3B;AACA,uBAAQ,eAAAA,QAAM,cAAc,OAAO,EAAE,cAAc,OAAO,OAAO,OAAc,KAAK,OAAO,GAAG,CAAC;AAAA,cACnG,CAAC,CAAC;AAAA,cACF,eAAAA,QAAM;AAAA,gBAAc;AAAA,gBAAS;AAAA,gBACzB,aAAa,SAAU,eAAAA,QAAM,cAAc,MAAM,MAAM,aAAa,IAAI,SAAU,MAAM,OAAO;AAC3F,sBAAIO,MAAK,OAAO,MAAM,gBAAgB,MAAM,YAAY,GAAG,CAAC,GAAG,cAAcA,IAAG,CAAC,GAAG,kBAAkBA,IAAG,CAAC;AAC1G,yBAAO,KAAK,YAAY;AAAA;AAAA,oBACxB,eAAAP,QAAM,cAAc,MAAM,EAAE,KAAK,OAAO,cAAc,KAAK,OAAO,SAAS,KAAK,SAAS,SAAS,KAAK,SAAS,OAAO,aAAa,WAAW,gBAAgB,GAAG,KAAK,QAAQ,OAAO,OAAO,KAAK,KAAK,IAAI,IAAI;AAAA;AAAA;AAAA,oBAE/M,MAAM,eAAe,KAAK,IAAI,CAAC,GAAG;AAAA,sBAC9B,SAAS,KAAK;AAAA,sBACd,OAAO;AAAA,sBACP,cAAc,KAAK;AAAA,sBACnB,WAAW,KAAK;AAAA,sBAChB,WAAW,KAAK;AAAA,sBAChB,SAAS;AAAA,sBACT,aAAa;AAAA,oBACjB,CAAC;AAAA;AAAA,gBACL,CAAC,CAAC,IAAK;AAAA,gBACP,eAAAA,QAAM,cAAc,MAAM,MAAM,MAAM,gBAAgB,IAAI,SAAU,QAAQ;AACxE,sBAAIO;AACJ,2BAASA,MAAK,aAAa,KAAK,SAAU,OAAO;AAAE,2BAAO,CAAC,MAAM,IAAI,QAAQ,MAAM;AAAA,kBAAG,CAAC,OAAO,QAAQA,QAAO,SAAS,SAASA,IAAG,aAAa,IACzI,OACA,MAAM,eAAe,QAAQ;AAAA,oBAC3B,OAAO,OAAO;AAAA,oBACd,cAAc,OAAO;AAAA,kBACzB,CAAC;AAAA,gBACT,CAAC,CAAC;AAAA,cAAC;AAAA,YAAC;AAAA,UAAC,IAAK;AAAA,QAAI;AAAA,MAAC,IAAK;AAAA,IAC5C;AACA,IAAAR,OAAM,UAAU,gBAAgB,SAAU,SAAS;AAC/C,UAAI,OAAO,QAAQ,QAAQ;AAC3B,UAAI,SAAS,mBAAmB;AAC5B,aAAK,iBAAiB,KAAK,IAAI;AAC/B,eAAO,KAAK,qBAAqB,OAAO;AAAA,MAC5C,WACS,SAAS,gBAAgB;AAC9B,aAAK,iBAAiB,KAAK,IAAI;AAC/B,eAAO,KAAK,kBAAkB;AAAA,MAClC,WACS,SAAS,gBAAgB;AAC9B,aAAK,iBAAiB,KAAK,IAAI;AAC/B,eAAO,KAAK,kBAAkB,OAAO;AAAA,MACzC,WACS,SAAS,yBAAyB;AACvC,aAAK,iBAAiB,KAAK,IAAI;AAC/B,eAAO,KAAK,0BAA0B,OAAO;AAAA,MACjD;AACA,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,uBAAuB,SAAU,QAAQ;AACrD,UAAI,QAAQ;AACZ,UAAI;AACJ,UAAI,KAAK,KAAK,OAAO,YAAY,GAAG,WAAW,QAAQ,GAAG,OAAO,KAAK,GAAG,aAAa,KAAK,GAAG,YAAY,WAAW,GAAG,UAAU,OAAO,OAAO,IAAI,CAAC,aAAa,SAAS,eAAe,cAAc,UAAU,CAAC;AACnN,UAAI,KAAK,KAAK;AACd,UAAI,MAAM,KAAK;AACf,UAAI,SAAS,KAAK,MAAM;AACxB,UAAI,CAAC,MAAM,kBAAkB;AACzB,eAAO;AAAA,MACX;AACA,aAAQ,eAAAC,QAAM;AAAA,QAAc;AAAA,QAAe,SAAS,CAAC,GAAG,MAAO,SAAS,MAAM,IAAI,SAAS,CAAC,GAAI,EAAE,SAAS;AAAA,UACnG,UAAU,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,YAAY,GAAG,yBAAyB;AAAA,UACzG,WAAW;AAAA,QACf,GAAG,kBAAkB,KAAK,oBAAoB,IAAI,mBAAmB,QAAQ,KAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,WAAW,QAAQ,OAAO,SAAS,KAAK,QAAQ,WAAW,MAAM,gBAAgB,GAAG,YAAY,IAAI,aAAa,IAAI,KAAK,oBAAoB,OAAO,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,SAAS,MAAM,MAAM,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,MAAM,OAAO,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,OAAO,WAAW,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,WAAW,SAAS,MAAM,aAAa,sBAAsB,MAAM,sBAAsB,gBAAgB,KAAK,mBAAmB,CAAC;AAAA,QAChrB,MAAM,gBAAgB,SAAU,eAAAA,QAAM;AAAA,UAAc;AAAA,UAAM,EAAE,WAAW,GAAG,wBAAwB,GAAG,KAAK,aAAa,SAAS,WAAY;AAAE,mBAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AAC1L,kBAAIO,KAAI,MAAM,eAAe,YAAY;AACzC,qBAAO,YAAY,MAAM,SAAUI,KAAI;AACnC,wBAAQA,IAAG,OAAO;AAAA,kBACd,KAAK;AACD,oBAAAJ,MAAK,KAAK,OAAO,OAAOA,IAAG,MAAM,gBAAgBA,IAAG;AACpD,iCAAa,EAAE,MAAM,qBAAqB,WACtC,MAAM,gBAAgB;AAC1B,2BAAO,CAAC,GAAa,cAAc,iBAAiB,aAAa,MAAM;AAAA,sBAC/D,SAAS,aACH,MAAM,gBAAgB,IAAI,SAAU,QAAQ;AAAE,+BAAO,OAAO;AAAA,sBAAU,CAAC,IACvE,CAAC;AAAA,oBACX,CAAC,CAAC,CAAC;AAAA,kBACX,KAAK;AACD,oCAAgBI,IAAG,KAAK;AACxB,wBAAI,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,WAAW;AACvF,6BAAO;AAAA,wBAAC;AAAA;AAAA,sBAAY;AAAA,oBACxB;AACA,0BAAM,iBAAiB;AACvB,2BAAO;AAAA,sBAAC;AAAA;AAAA,oBAAY;AAAA,gBAC5B;AAAA,cACJ,CAAC;AAAA,YACL,CAAC;AAAA,UAAG,EAAE;AAAA,UACN,eAAAX,QAAM,cAAc,YAAU,EAAE,MAAM,MAAM,aAAa,IAAI,KAAK,YAAY,SAAS,CAAC,CAAC,MAAM,qBAAqB,QAAQ,SAAS,CAAC,EAAE,MAAM,qBAAqB,UAC3J,MAAM,qBAAqB,WACvB,MAAM,gBAAgB,QAAQ,GAAG,GAAG,iBAAiB,CAAC;AAAA,QAAC,IAAK;AAAA,QAC5E,EAAE,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,cACrD,MAAM,gBAAgB,IAAI,SAAU,QAAQ;AAAE,iBAAQ,eAAAA,QAAM;AAAA,YAAc;AAAA,YAAM,EAAE,WAAW,GAAG,wBAAwB,GAAG,KAAK,OAAO,OAAO,SAAS,WAAY;AAAE,qBAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AACjN,oBAAIO,KAAI,MAAM,eAAe,SAAS;AACtC,uBAAO,YAAY,MAAM,SAAUI,KAAI;AACnC,0BAAQA,IAAG,OAAO;AAAA,oBACd,KAAK;AACD,sBAAAJ,MAAK,KAAK,OAAO,OAAOA,IAAG,MAAM,gBAAgBA,IAAG;AACpD,gCAAU,MAAM,qBAAqB,IAAI,SAAU,MAAM;AAAE,+BAAO,KAAK;AAAA,sBAAU,CAAC;AAClF,0BAAI,CAAC,OAAO,SAAS;AACjB,gCAAQ,KAAK,OAAO,QAAQ;AAAA,sBAChC,OACK;AACD,kCAAU,QAAQ,OAAO,SAAU,GAAG;AAAE,iCAAO,EAAE,SAAS,OAAO,SAAS;AAAA,wBAAM,CAAC;AAAA,sBACrF;AACA,6BAAO,CAAC,GAAa,cAAc,iBAAiB,aAAa,MAAM;AAAA,wBAC/D;AAAA,sBACJ,CAAC,CAAC,CAAC;AAAA,oBACX,KAAK;AACD,sCAAgBI,IAAG,KAAK;AACxB,0BAAI,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,WAAW;AACvF,+BAAO;AAAA,0BAAC;AAAA;AAAA,wBAAY;AAAA,sBACxB;AACA,6BAAO,aAAa;AACpB,6BAAO;AAAA,wBAAC;AAAA;AAAA,sBAAY;AAAA,kBAC5B;AAAA,gBACJ,CAAC;AAAA,cACL,CAAC;AAAA,YAAG,EAAE;AAAA,YACN,eAAAX,QAAM,cAAc,YAAU,EAAE,MAAM,MAAM,aAAa,IAAI,SAAS,OAAO,QAAQ,GAAG,OAAO,QAAQ,OAAO,OAAO,OAAO,KAAK,IAAI,IAAI;AAAA,UAAC;AAAA,QAAI,CAAC;AAAA,MAAC;AAAA,IAChK;AACA,IAAAD,OAAM,UAAU,oBAAoB,WAAY;AAC5C,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,MAAM,GAAG,KAAK,YAAY,GAAG,WAAW,KAAK,GAAG,aAAa,KAAK,GAAG,WAAW,mBAAmB,GAAG;AAC7I,UAAI,CAAC,aAAa,MAAM,UAAU;AAC9B,eAAO;AAAA,MACX;AACA,aAAQ,eAAAC,QAAM;AAAA,QAAc;AAAA,QAAQ,EAAE,UAAU,CAAC,CAAC,MAAM,UAAU,aAAa,IAAI,KAAK,mBAAmB,SAAS,EAAE,SAAS,GAAG,iBAAiB,GAAG,WAAW,SAAS,GAAG,kBAAkB,oBAAoB,IAAI,mBAAmB,MAAM,MAAM,QAAQ,MAAM,UAAU,SAAS,SAAU,GAAG;AAC5R,YAAE,eAAe;AACjB,gBAAM,eAAe;AACrB,gBAAM,YAAY,MAAM,MAAM;AAAA,QAClC,GAAG,UAAU,KAAK;AAAA,QAClB,eAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,YAAY,WAAW,OAAO,CAAC;AAAA,MAAC;AAAA,IAC1E;AACA,IAAAD,OAAM,UAAU,oBAAoB,SAAU,SAAS;AACnD,UAAI,QAAQ;AACZ,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,KAAK,GAAG,WAAW,SAAS,GAAG;AACtE,UAAI,UAAU,MAAM,mBAAmB,CAAC;AACxC,UAAI,CAAC,SAAS;AACV,eAAO;AAAA,MACX;AACA,aAAO,OAAO,eAAe,SAAS,SAAS,EAAE,OAAO,GAAG,kBAAkB,EAAE,GAAG,OAAO,GAAG,EAAE,MAAM,SAAS,CAAC,GAAG;AAAA,QAC7G,SAAS,MAAM;AAAA,QACf,UAAU,WAAY;AAClB,gBAAM,OAAO,EAAE,oBAAoB,KAAK,CAAC;AACzC,iBAAO,cAAS,EAAE,KAAK,SAAU,GAAG;AAAE,mBAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AACtF,kBAAI,SAAS;AACb,qBAAO,YAAY,MAAM,SAAUQ,KAAI;AACnC,wBAAQA,IAAG,OAAO;AAAA,kBACd,KAAK;AACD,8BAAU,EAAE,WAAW;AACvB,oBAAAA,IAAG,QAAQ;AAAA,kBACf,KAAK;AACD,oBAAAA,IAAG,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACzB,2BAAO,CAAC,GAAa,YAAY,SAAS,KAAK,OAAO,OAAO,CAAC;AAAA,kBAClE,KAAK;AACD,oBAAAA,IAAG,KAAK;AACR,2BAAO,CAAC,GAAa,CAAC;AAAA,kBAC1B,KAAK;AACD,8BAAUA,IAAG,KAAK;AAClB,4BAAQ,MAAM,OAAO;AACrB,2BAAO,CAAC,GAAa,CAAC;AAAA,kBAC1B,KAAK;AACD,0BAAM,OAAO,EAAE,oBAAoB,MAAM,CAAC;AAC1C,2BAAO;AAAA,sBAAC;AAAA;AAAA,oBAAgB;AAAA,kBAC5B,KAAK;AAAG,2BAAO;AAAA,sBAAC;AAAA;AAAA,oBAAY;AAAA,gBAChC;AAAA,cACJ,CAAC;AAAA,YACL,CAAC;AAAA,UAAG,CAAC;AAAA,QACT;AAAA,MACJ,CAAC;AAAA,IACL;AAIA,IAAAR,OAAM,UAAU,4BAA4B,SAAU,SAAS;AAC3D,UAAI,QAAQ;AACZ,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,KAAK,GAAG,WAAW,SAAS,GAAG;AACtE,UAAI,UAAU,MAAM,mBAAmB,CAAC;AACxC,UAAI,CAAC,SAAS;AACV,eAAO;AAAA,MACX;AACA,aAAO,OAAO,uBAAuB,SAAS,SAAS,EAAE,OAAO,GAAG,0BAA0B,EAAE,GAAG,OAAO,GAAG,EAAE,MAAM,SAAS,CAAC,GAAG;AAAA,QAC7H,UAAU,WAAY;AAClB,iBAAO,cAAS,EAAE,KAAK,SAAU,GAAG;AAAE,mBAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AACtF,kBAAI,SAAS;AACb,qBAAO,YAAY,MAAM,SAAUQ,KAAI;AACnC,wBAAQA,IAAG,OAAO;AAAA,kBACd,KAAK;AACD,8BAAU,EAAE,WAAW;AACvB,oBAAAA,IAAG,QAAQ;AAAA,kBACf,KAAK;AACD,oBAAAA,IAAG,KAAK,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;AACxB,2BAAO,CAAC,GAAa,YAAY,SAAS,KAAK,OAAO,SAAS,IAAI,CAAC;AAAA,kBACxE,KAAK;AACD,oBAAAA,IAAG,KAAK;AACR,2BAAO,CAAC,GAAa,CAAC;AAAA,kBAC1B,KAAK;AACD,8BAAUA,IAAG,KAAK;AAClB,4BAAQ,MAAM,OAAO;AACrB,2BAAO,CAAC,GAAa,CAAC;AAAA,kBAC1B,KAAK;AAAG,2BAAO;AAAA,sBAAC;AAAA;AAAA,oBAAY;AAAA,gBAChC;AAAA,cACJ,CAAC;AAAA,YACL,CAAC;AAAA,UAAG,CAAC;AAAA,QACT;AAAA,MACJ,CAAC;AAAA,IACL;AACA,IAAAR,OAAM,UAAU,gBAAgB,SAAU,QAAQ;AAC9C,UAAI,QAAQ;AACZ,UAAI,KAAK,KAAK,OAAO,UAAU,GAAG,SAAS,SAAS,GAAG,QAAQ,QAAQ,GAAG,OAAO,KAAK,GAAG,YAAY,OAAO,GAAG;AAC/G,gBAAU,MAAM,QAAQ,OAAO,IAAI,QAAQ,OAAO,IAAI,CAAC;AACvD,UAAI,MAAM,YACN,WAAW,YACX,CAAC,CAAC,KAAK,iBAAiB,QAAQ,iBAAiB,GAAG;AACpD,gBAAQ,KAAK;AAAA,UACT,MAAM;AAAA,UACN,UAAU,KAAK,qBAAqB;AAAA,QACxC,CAAC;AAAA,MACL;AACA,UAAI,MAAM,aACN,CAAC,MAAM,YACP,WAAW,YACX,MAAM,KAAK,SAAS,KACpB,CAAC,CAAC,KAAK,iBAAiB,QAAQ,cAAc,GAAG;AACjD,gBAAQ,KAAK;AAAA,UACT,MAAM;AAAA,UACN,UAAU,KAAK,kBAAkB;AAAA,QACrC,CAAC;AAAA,MACL;AACA,aAAO,MAAM,QAAQ,OAAO,KAAK,QAAQ,SAAU,eAAAC,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,eAAe,EAAE,GAAG,QAAQ,IAAI,SAAU,QAAQ,KAAK;AACjJ,eAAO,OAAO,UAAU,OAAO,GAAG,GAAG,SAAS,EAAE,MAAM,SAAS,GAAG,MAAM,GAAG;AAAA,UACvE,UAAU,MAAM;AAAA,UAChB;AAAA,UACA,aAAa,MAAM;AAAA,UACnB,MAAM,MAAM,QAAQ,IAAI;AAAA,QAC5B,CAAC;AAAA,MACL,CAAC,CAAC,IAAK;AAAA,IACX;AACA,IAAAD,OAAM,UAAU,eAAe,SAAU,UAAU;AAC/C,UAAI,KAAK,KAAK,OAAO,SAAS,GAAG,QAAQ,kBAAkB,GAAG,iBAAiB,mBAAmB,GAAG,kBAAkB,yBAAyB,GAAG,wBAAwB,sBAAsB,GAAG,qBAAqB,SAAS,GAAG,QAAQ,aAAa,GAAG,YAAY,QAAQ,GAAG,OAAO,KAAK,GAAG,YAAY,OAAO,GAAG,MAAM,KAAK,GAAG;AACvU,UAAI,eAAe,OAAO;AACtB,eAAO;AAAA,MACX;AACA,UAAI,aAAa,CAAC;AAElB,UAAI,QAAQ,sBACN,oBAAoB,SAAS,SAAS,SAAS,CAAC,GAAG,KAAK,KAAK,GAAG,MAAM,YAAY,GAAG,UAAU,GAAG,KAAK,aAAa,IACpH;AACN,UAAI,UAAU,KAAK,cAAc,QAAQ;AACzC,UAAI,cAAc,WAAW,SAAS,MAAM,WAAY,eAAAC,QAAM;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,GAAG,mCAAmC,kBAAkB,sBAAsB,GAAG,KAAK,iBAAiB;AAAA,QACnM;AAAA,QACA;AAAA,QACA,MAAM,WAAY,eAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,eAAe,GAAG,KAAK,KAAK,WAAW,GAAG,GAAG,eAAe,CAAC,IAAK;AAAA,MAAI,IAAK;AAC5I,UAAI,aAAa,WAAW,CAAC,MAAM,QAAQ,MAAM,KAAK,OAAO,UAAW,eAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,gBAAgB,eAAe,GAAG,KAAK,SAAS,GAAG,OAAO,UAAU,QAAQ,SAAS,SAAS,CAAC,GAAI,aAAa,QAAQ,aAAa,IAAK,GAAG,EAAE,MAAM,MAAM,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC,IAAK;AACpS,aAAO,cAAc,cACf,CAAC,YAAY,WAAW,IACxB,cAAc,eAAe;AAAA,IACvC;AACA,IAAAD,OAAM,UAAU,eAAe,WAAY;AACvC,UAAI,KAAK,KAAK,OAAO,SAAS,GAAG,QAAQ,mBAAmB,GAAG,kBAAkB,yBAAyB,GAAG,wBAAwB,kBAAkB,GAAG,iBAAiB,sBAAsB,GAAG,qBAAqB,SAAS,GAAG,QAAQ,aAAa,GAAG,YAAY,QAAQ,GAAG,OAAO,OAAO,GAAG,MAAM,KAAK,GAAG,YAAY,cAAc,GAAG;AAChV,UAAI,eAAe,OAAO;AACtB,eAAO;AAAA,MACX;AACA,UAAI,QAAQ,sBACN,oBAAoB,SAAS,SAAS,CAAC,GAAG,KAAK,KAAK,GAAG,MAAM,YAAY,GAAG,KAAK,aAAa,IAC9F;AACN,UAAI,UAAU,KAAK,cAAc,QAAQ;AACzC,UAAI,aAAa,WAAW,CAAC,MAAM,QAAQ,MAAM,KAAK,OAAO,UAAW,eAAAC,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,gBAAgB,iBAAiB,cAAc,wBAAwB,EAAE,GAAG,KAAK,SAAS,GAAG,OAAO,UAAU,QAAQ;AAAA,QACrO,MAAM,MAAM,QAAQ,IAAI;AAAA,MAC5B,CAAC,CAAC,IAAK;AACP,UAAI,cAAc,WAAW,QAAS,eAAAA,QAAM;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,GAAG,mCAAmC,kBAAkB,wBAAwB,CAAC,cAAc,cAAc,6BAA6B,EAAE,GAAG,KAAK,iBAAiB;AAAA,QAC/O;AAAA,QACA;AAAA,MAAK,IAAK;AACd,aAAO,cAAc,cACf,CAAC,aAAa,UAAU,IACxB,cAAc,eAAe;AAAA,IACvC;AACA,IAAAD,OAAM,UAAU,qBAAqB,WAAY;AAC7C,UAAI,KAAK,KAAK,OAAO,KAAK,GAAG,YAAY,iBAAiB,GAAG,gBAAgB,QAAQ,GAAG,OAAO,cAAc,GAAG,aAAa,SAAS,GAAG,QAAQ,mBAAmB,GAAG,kBAAkB,iBAAiB,GAAG,gBAAgB,mBAAmB,GAAG,kBAAkB,eAAe,GAAG,cAAc,YAAY,GAAG,WAAW,SAAS,GAAG,QAAQ,WAAW,GAAG,UAAU,wBAAwB,GAAG,uBAAuB,YAAY,GAAG,WAAW,aAAa,GAAG,YAAY,wBAAwB,GAAG,uBAAuB,oBAAoB,GAAG,mBAAmB,yBAAyB,GAAG,wBAAwB,qBAAqB,GAAG,oBAAoB,iBAAiB,GAAG,gBAAgB,cAAc,GAAG,aAAa,cAAc,GAAG,aAAa,gBAAgB,GAAG,eAAe,UAAU,GAAG,SAAS,gBAAgB,GAAG,eAAe,gBAAgB,GAAG,eAAe,OAAO,GAAG;AAGr3B,YAAM,KAAK;AACX,aAAQ,eAAAC,QAAM;AAAA,QAAc,eAAAA,QAAM;AAAA,QAAU;AAAA,QACxC,eAAAA,QAAM,cAAc,gBAAc,EAAE,eAA8B,gBAAgB,GAAG;AAAA,UAC7E,iCAAiC;AAAA,UACjC,4BAA4B,MAAM,aAAa;AAAA,UAC/C,4BAA4B,eAAe,CAAC,kBAAkB,MAAM;AAAA,UACpE,gCAAgC,kBAAkB,CAAC,MAAM,MAAM;AAAA,QACnE,GAAG,cAAc,GAAG,WAAW,uBAAuB,aAA0B,YAAwB,OAAc,YAAY,IAAI,SAAS,MAAM,iBAAiB,cAAc,MAAM,aAAa,MAAM,MAAM,OAAO,aAA0B,QAAgB;AAAA;AAAA,UAEpQ,MAAM,QAAQ,WAAW,KAAK,YAAY,SACpC,KAAK,kBACL;AAAA,WAAW,UAAU,KAAK,oBAAoB,UAAU,KAAK,UAAU,gBAAgB,KAAK,gBAAgB,YAAY,KAAK,YAAY,SAAS,KAAK,aAAa,YAAY,KAAK,gBAAgB,cAAc,KAAK,kBAAkB,iBAAiB,KAAK,qBAAqB,iBAAiB,KAAK,qBAAqB,eAAe,MAAM,WAAW,SAAY,KAAK,mBAAmB,UAAU,MAAM,UAAU,iBAAiB,MAAM,iBAAiB,kBAAoC,gBAAgC,UAAU,KAAK,cAAc,kBAAoC,cAA4B,MAAM,MAAM,MAAM,WAAsB,UAAoB,oBAAwC,mBAAsC,QAAgB,WAAsB,eAA8B,SAAkB,SAAS,MAAM,QAAQ,GAAG,kBAAkB;AAAA,UACz3B;AAAA,UACA,YAAY;AAAA,UACZ;AAAA,UACA;AAAA,QACJ,CAAC,CAAC;AAAA,QACF,eAAAA,QAAM,cAAc,WAAS,EAAE,eAA8B,SAAS,MAAM,MAAM,MAAM,QAAQ,CAAC;AAAA,MAAC;AAAA,IAC1G;AACA,IAAAD,OAAM,UAAU,SAAS,WAAY;AACjC,UAAI,KAAK,KAAK,OAAO,YAAY,GAAG,WAAW,QAAQ,GAAG,OAAO,QAAQ,GAAG,OAAO,KAAK,GAAG,YAAY,eAAe,GAAG,cAAc,cAAc,GAAG,aAAa,iBAAiB,GAAG,gBAAgB,qBAAqB,GAAG,oBAAoB,WAAW,GAAG,UAAU,gBAAgB,GAAG,eAAe,KAAK,GAAG;AACvT,WAAK,mBAAmB,CAAC;AACzB,UAAI,UAAU,eAAe,CAAC,iBAAiB,OAAO,KAAK,cAAc;AACzE,UAAI,SAAS,eAAe,CAAC,iBAAiB,OAAO,KAAK,aAAa;AACvE,UAAI,SAAS,KAAK,aAAa;AAC/B,UAAI,iBAAiB,GAAG,eAAe,KAAK,MAAM,gBAAgB;AAAA,QAC9D,4BAA4B,MAAM,aAAa;AAAA,MACnD,CAAC;AACD,aAAQ,eAAAC,QAAM;AAAA,QAAc;AAAA,QAAO,SAAS,EAAE,KAAK,KAAK,KAAK,WAAW,GAAG,SAAS,EAAE,aAAa,SAAS,GAAG,WAAW;AAAA,UAClH,kBAAkB,CAAC,CAAC,MAAM,YAAY,CAAC,CAAC,MAAM;AAAA,UAC9C,yBAAyB;AAAA,QAC7B,CAAC,GAAG,OAAO,MAAM,YAAY,KAAK,GAAG,WAAW,GAAG,GAAG,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,UAAU,CAAC;AAAA,QAC7I,qBAAqB,KAAK,qBAAqB,IAAI;AAAA,QACnD,KAAK,kBAAkB,cAAc;AAAA,QACrC;AAAA,QACA;AAAA,QACA,eAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,mBAAmB,GAAG,cAAc,KAAK,iBAAiB,GAAG,KAAK,mBAAmB,CAAC;AAAA,QACjI;AAAA,MAAM;AAAA,IACd;AACA,IAAAD,OAAM,cAAc;AACpB,IAAAA,OAAM,YAAY;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,IAAAA,OAAM,eAAe;AAAA,MACjB,WAAW;AAAA,MACX,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,kBAAkB;AAAA,MAClB,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,MACpB,WAAW;AAAA,IACf;AACA,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,OAAO;AAAA,IAC3C,GAAGA,OAAM,WAAW,mBAAmB,IAAI;AAC3C,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,mBAAmB,IAAI;AAC3C,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,kBAAkB,IAAI;AAC1C,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,CAAC;AAAA,MAClC,WAAW,qBAAqB,OAAO;AAAA,IAC3C,GAAGA,OAAM,WAAW,cAAc,IAAI;AACtC,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,CAAC;AAAA,MAClC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,iBAAiB,IAAI;AACzC,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,QAAQ,MAAM,CAAC;AAAA,MAChD,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,sBAAsB,IAAI;AAC9C,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,4BAA4B,IAAI;AACpD,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,UAAU,CAAC;AAAA,MAC5C,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,4BAA4B,IAAI;AACpD,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,UAAU,CAAC;AAAA,MAC5C,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,0BAA0B,IAAI;AAClD,WAAOA;AAAA,EACX,EAAE,eAAAC,QAAM,SAAS;AAAA;AACjB,IAAI;AAAA;AAAA,EAAmC,SAAU,QAAQ;AACrD,cAAUY,oBAAmB,MAAM;AACnC,aAASA,qBAAoB;AACzB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,mBAAkB,UAAU,UAAU,SAAU,QAAQ,SAAS;AAC7D,UAAI,IAAI,IAAI;AACZ,UAAI,SAAS,KAAK;AAKlB,WAAK,KAAK,KAAK,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAEhE,gBAAQ,MAAM,KAAK,KAAK,MAAM,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,QAAQ,OAAO;AAAA,MACjH;AACA,UAAI,SAAS;AACT,eAAO,OAAO,KAAK,SAAS,MAAM;AAAA,MACtC;AAAA,IACJ;AAgBA,IAAAA,mBAAkB,UAAU,kBAAkB,SAAU,KAAK,OAAO,WAAW,cAAc;AACzF,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI;AACJ,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,KAAK,MAAM;AACnB,iBAAO,CAAC,GAAc,uBAAuB,MAAM,MAAM,OAAO,KAAK,MAAM,MAAM,OAAO,WAAW,YAAY,CAAC;AAAA,QACpH,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,mBAAkB,UAAU,SAAS,SAAU,SAAS,OAAO,KAAK,QAAQ,SAAS,MAAM;AACvF,UAAI,IAAI,IAAI;AACZ,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,SAAS;AACb,YAAI,QAAQ;AACZ,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,GAAG,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,WAAW,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK;AAAa,uBAAO,CAAC,GAAa,CAAC;AACvJ,qBAAO,CAAC,GAAa,KAAK,gBAAgB,OAAO,KAAK,MAAM,MAAM,KAAK,OAAO,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,SAAS,CAAC;AAAA,YAC7I,KAAK;AACD,wBAAU,GAAG,KAAK;AAClB,qBAAO,CAAC,GAAa,QAAQ,IAAI,QAAQ,IAAI,SAAU,QAAQ;AAAE,uBAAO,MAAM,gBAAgB,MAAM;AAAA,cAAG,CAAC,CAAC,CAAC;AAAA,YAC9G,KAAK;AACD,iBAAG,KAAK;AACR,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,YACxB,KAAK;AACD,uBAAS,KAAK;AACd,mBAAK,KAAK,KAAK,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAEhE,uBAAO,CAAC,IAAe,MAAM,KAAK,KAAK,MAAM,MAAM,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,SAAS,OAAO,GAAG,CAAC;AAAA,cACpI;AACA,kBAAI,SAAS;AACT,uBAAO,CAAC,GAAc,OAAO,OAAO,SAAS,GAAG,CAAC;AAAA,cACrD;AACA,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,mBAAkB,UAAU,UAAU,SAAU,QAAQ,SAAS,OAAO,WAAW;AAC/E,UAAI,IAAI,IAAI,IAAI;AAChB,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,OAAO,SAAS;AACpB,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,sBAAQ,KAAK,MAAM;AACnB,kBAAI,EAAE,UAAU,UAAa,cAAc;AAAY,uBAAO,CAAC,GAAa,CAAC;AAC7E,qBAAO,CAAC,GAAa,KAAK,gBAAgB,KAAK,MAAM,MAAM,OAAO,SAAS,CAAC;AAAA,YAChF,KAAK;AACD,wBAAU,GAAG,KAAK;AAClB,sBAAQ,QAAQ,SAAU,QAAQ;AAC9B,uBAAO,WAAW,MAAM;AAAA,cAC5B,CAAC;AACD,qBAAO,CAAC,GAAa,CAAC;AAAA,YAC1B,KAAK;AACD,mBAAK,KAAK,KAAK,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAEhE,uBAAO,CAAC,IAAe,MAAM,KAAK,KAAK,MAAM,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,QAAQ,SAAS,OAAO,SAAS,CAAC;AAAA,cACnJ,OACK;AACD,uBAAO,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG;AAAA,kBAAE,OAAO,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,KAAK,OAAO;AAAA;AAAA,gBACxG,CAAC;AACF,uBAAO,CAAC,GAAc,MAAM,WAAW,MAAM,QAAW,OAAO,CAAC;AAAA,cACpE;AACA,iBAAG,QAAQ;AAAA,YACf,KAAK;AAAG,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAChC;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,mBAAkB,UAAU,UAAU,WAAY;AAC9C,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,OAAO,GAAG;AACjD,aAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AACA,IAAAA,mBAAkB,UAAU,mBAAmB,WAAY;AACvD,aAAO,KAAK,MAAM,MAAM;AAAA,IAC5B;AACA,IAAAA,mBAAkB,UAAU,WAAW,SAAU,QAAQ,KAAK,aAAa,MAAM;AAC7E,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,IAAI,OAAO,YAAY,MAAM,YAAY,IAAI,MAAM,SAAS;AAChE,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,mBAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,aAAa,GAAG,YAAY,OAAO,GAAG;AACzE,2BAAa,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO;AACpE,mBAAK;AACL,sBAAQ,IAAI;AAAA,gBACR,KAAK;AAAa,yBAAO,CAAC,GAAa,CAAC;AAAA,gBACxC,KAAK;AAAY,yBAAO,CAAC,GAAa,CAAC;AAAA,gBACvC,KAAK;AAAU,yBAAO,CAAC,GAAa,CAAC;AAAA,gBACrC,KAAK;AAAY,yBAAO,CAAC,GAAa,CAAC;AAAA,gBACvC,KAAK;AAAc,yBAAO,CAAC,GAAa,CAAC;AAAA,gBACzC,KAAK;AAAmB,yBAAO,CAAC,GAAa,CAAC;AAAA,gBAC9C,KAAK;AAAkB,yBAAO,CAAC,GAAa,CAAC;AAAA,gBAC7C,KAAK;AAAe,yBAAO,CAAC,GAAa,EAAE;AAAA,cAC/C;AACA,qBAAO,CAAC,GAAa,EAAE;AAAA,YAC3B,KAAK;AACD,oBAAM,MAAM;AACZ,oBAAM,UAAU;AAChB,mBAAK,aAAa;AAClB,qBAAO,CAAC,GAAa,EAAE;AAAA,YAC3B,KAAK;AACD,oBAAM,MAAM;AACZ,mBAAK,aAAa;AAClB,qBAAO,CAAC,GAAa,EAAE;AAAA,YAC3B,KAAK;AAAG,qBAAO,CAAC,GAAa,KAAK,gBAAgB,KAAK,KAAK,OAAO,KAAK,WAAW,KAAK,QAAQ,CAAC;AAAA,YACjG,KAAK;AACD,qBAAO,GAAG,KAAK;AACf,oBAAM,eAAe,KAAK,IAAI,SAAU,MAAM;AAAE,uBAAO,KAAK;AAAA,cAAM,CAAC,GAAG,UAAU;AAChF,mBAAK,aAAa;AAClB,qBAAO,CAAC,GAAa,EAAE;AAAA,YAC3B,KAAK;AACD,oBAAM,cAAc;AACpB,qBAAO,CAAC,GAAa,EAAE;AAAA,YAC3B,KAAK;AACD,oBAAM,aAAa;AACnB,qBAAO,CAAC,GAAa,EAAE;AAAA,YAC3B,KAAK;AACD,mBAAK,WAAW;AAChB,qBAAO,CAAC,GAAa,EAAE;AAAA,YAC3B,KAAK;AAAG,qBAAO,CAAC,GAAa,KAAK,gBAAgB,KAAK,KAAK,OAAO,KAAK,SAAS,CAAC;AAAA,YAClF,KAAK;AACD,wBAAU,GAAG,KAAK;AAClB,sBAAQ,QAAQ,SAAU,QAAQ;AAC9B,sBAAM,eAAe,MAAM;AAAA,cAC/B,CAAC;AACD,qBAAO,CAAC,GAAa,EAAE;AAAA,YAC3B,KAAK;AAAI,qBAAO,CAAC,GAAa,KAAK,gBAAgB,KAAK,KAAK,OAAO,KAAK,SAAS,CAAC;AAAA,YACnF,KAAK;AACD,yBAAW,GAAG,KAAK;AACnB,uBAAS,QAAQ,SAAU,QAAQ;AAC/B,sBAAM,YAAY,QAAQ,CAAC,CAAC,KAAK,KAAK;AAAA,cAC1C,CAAC;AACD,qBAAO,CAAC,GAAa,EAAE;AAAA,YAC3B,KAAK;AAAI,qBAAO,CAAC,GAAc,KAAK,aAAa,QAAW,QAAQ,IAAI,CAAC;AAAA,YACzE,KAAK;AAAI,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UACjC;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOA;AAAA,EACX,EAAE,KAAK;AAAA;AACP,IAAI;AAAA;AAAA,EAA+B,SAAU,QAAQ;AACjD,cAAUC,gBAAe,MAAM;AAC/B,aAASA,iBAAgB;AACrB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,iBAAgB,WAAW;AAAA,MACvB,SAAS;AAAA,QACL,MAAM;AAAA,QACN,WAAW,WAAW;AAAA,QACtB,MAAM;AAAA,MACV,CAAC;AAAA,IACL,GAAGA,cAAa;AAChB,WAAOA;AAAA,EACX,EAAE,iBAAiB;AAAA;", "names": ["import_react", "import_react", "Component", "QuickEditComponent", "keycode", "omit", "React", "hoistNonReactStatic", "import_react", "import_hoist_non_react_statics", "Component", "QuickEditComponent", "React", "hoistNonReactStatic", "import_react", "import_react_dom", "import_hoist_non_react_statics", "Component", "PopOverComponent", "position", "React", "hoistNonReactStatic", "import_react", "import_react", "import_react_dom", "import_react", "__read", "__read", "import_react", "import_react", "reaction", "import_react", "React", "React", "reaction", "__assign", "import_react", "import_react", "fn", "reaction", "observer", "React__default", "import_omit", "TableCell", "Component", "omit", "React", "Table<PERSON><PERSON><PERSON><PERSON><PERSON>", "observer", "<PERSON><PERSON><PERSON><PERSON>", "import_react", "import_react_dom", "HeadCellFilterDropDown", "value", "xor", "React", "import_react", "React", "formItems", "schema", "_a", "import_react", "import_react", "import_react", "TableRow", "React", "observer", "item", "TableBody", "React", "position", "offset", "item", "i", "matchedColumns", "_a", "observer", "import_react", "React", "_a", "observer", "import_react", "React", "observer", "React", "TableContent", "_a", "observer", "memoize", "column", "res", "key", "import_react", "React", "_a", "render", "observer", "import_react", "React", "style", "_a", "stickyClassName", "prefix", "affix", "addtionalClassName", "Table", "React", "debounce", "isPlainObject", "props", "isEqual", "find", "table", "_a", "item", "tr", "intersection", "_b", "TableRendererBase", "<PERSON><PERSON><PERSON><PERSON>"]}