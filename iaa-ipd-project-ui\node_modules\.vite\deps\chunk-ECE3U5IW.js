// node_modules/amis/node_modules/mobx/lib/mobx.module.js
var OBFUSCATED_ERROR = "An invariant failed, however the error is obfuscated because this is an production build.";
var EMPTY_ARRAY = [];
Object.freeze(EMPTY_ARRAY);
var EMPTY_OBJECT = {};
Object.freeze(EMPTY_OBJECT);
var mockGlobal = {};
function getGlobal() {
  if (typeof window !== "undefined") {
    return window;
  }
  if (typeof global !== "undefined") {
    return global;
  }
  if (typeof self !== "undefined") {
    return self;
  }
  return mockGlobal;
}
function getNextId() {
  return ++globalState.mobxGuid;
}
function fail(message) {
  invariant(false, message);
  throw "X";
}
function invariant(check, message) {
  if (!check)
    throw new Error("[mobx] " + (message || OBFUSCATED_ERROR));
}
var deprecatedMessages = [];
function deprecated(msg, thing) {
  if (false)
    return false;
  if (thing) {
    return deprecated("'" + msg + "', use '" + thing + "' instead.");
  }
  if (deprecatedMessages.indexOf(msg) !== -1)
    return false;
  deprecatedMessages.push(msg);
  console.error("[mobx] Deprecated: " + msg);
  return true;
}
function once(func) {
  var invoked = false;
  return function() {
    if (invoked)
      return;
    invoked = true;
    return func.apply(this, arguments);
  };
}
var noop = function() {
};
function unique(list) {
  var res = [];
  list.forEach(function(item) {
    if (res.indexOf(item) === -1)
      res.push(item);
  });
  return res;
}
function isObject(value) {
  return value !== null && typeof value === "object";
}
function isPlainObject(value) {
  if (value === null || typeof value !== "object")
    return false;
  var proto = Object.getPrototypeOf(value);
  return proto === Object.prototype || proto === null;
}
function convertToMap(dataStructure) {
  if (isES6Map(dataStructure) || isObservableMap(dataStructure)) {
    return dataStructure;
  } else if (Array.isArray(dataStructure)) {
    return new Map(dataStructure);
  } else if (isPlainObject(dataStructure)) {
    var map = /* @__PURE__ */ new Map();
    for (var key in dataStructure) {
      map.set(key, dataStructure[key]);
    }
    return map;
  } else {
    return fail("Cannot convert to map from '" + dataStructure + "'");
  }
}
function makeNonEnumerable(object, propNames) {
  for (var i = 0; i < propNames.length; i++) {
    addHiddenProp(object, propNames[i], object[propNames[i]]);
  }
}
function addHiddenProp(object, propName, value) {
  Object.defineProperty(object, propName, {
    enumerable: false,
    writable: true,
    configurable: true,
    value
  });
}
function addHiddenFinalProp(object, propName, value) {
  Object.defineProperty(object, propName, {
    enumerable: false,
    writable: false,
    configurable: true,
    value
  });
}
function isPropertyConfigurable(object, prop) {
  var descriptor = Object.getOwnPropertyDescriptor(object, prop);
  return !descriptor || descriptor.configurable !== false && descriptor.writable !== false;
}
function assertPropertyConfigurable(object, prop) {
  if (!isPropertyConfigurable(object, prop))
    fail("Cannot make property '" + prop + "' observable, it is not configurable and writable in the target object");
}
function createInstanceofPredicate(name, clazz) {
  var propName = "isMobX" + name;
  clazz.prototype[propName] = true;
  return function(x) {
    return isObject(x) && x[propName] === true;
  };
}
function areBothNaN(a, b) {
  return typeof a === "number" && typeof b === "number" && isNaN(a) && isNaN(b);
}
function isES6Map(thing) {
  if (getGlobal().Map !== void 0 && thing instanceof getGlobal().Map)
    return true;
  return false;
}
function isES6Set(thing) {
  return thing instanceof Set;
}
function iteratorToArray(it) {
  var res = [];
  while (true) {
    var r = it.next();
    if (r.done)
      break;
    res.push(r.value);
  }
  return res;
}
function primitiveSymbol() {
  return typeof Symbol === "function" && Symbol.toPrimitive || "@@toPrimitive";
}
function toPrimitive(value) {
  return value === null ? null : typeof value === "object" ? "" + value : value;
}
function forOf(iter, callback) {
  var next = iter.next();
  while (!next.done) {
    callback(next.value);
    next = iter.next();
  }
}
function iteratorSymbol() {
  return typeof Symbol === "function" && Symbol.iterator || "@@iterator";
}
function declareIterator(prototType, iteratorFactory) {
  addHiddenFinalProp(prototType, iteratorSymbol(), iteratorFactory);
}
function makeIterable(iterator) {
  iterator[iteratorSymbol()] = getSelf;
  return iterator;
}
function toStringTagSymbol() {
  return typeof Symbol === "function" && Symbol.toStringTag || "@@toStringTag";
}
function getSelf() {
  return this;
}
var Atom = (
  /** @class */
  function() {
    function Atom2(name) {
      if (name === void 0) {
        name = "Atom@" + getNextId();
      }
      this.name = name;
      this.isPendingUnobservation = false;
      this.isBeingObserved = false;
      this.observers = [];
      this.observersIndexes = {};
      this.diffValue = 0;
      this.lastAccessedBy = 0;
      this.lowestObserverState = IDerivationState.NOT_TRACKING;
    }
    Atom2.prototype.onBecomeUnobserved = function() {
    };
    Atom2.prototype.onBecomeObserved = function() {
    };
    Atom2.prototype.reportObserved = function() {
      return reportObserved(this);
    };
    Atom2.prototype.reportChanged = function() {
      startBatch();
      propagateChanged(this);
      endBatch();
    };
    Atom2.prototype.toString = function() {
      return this.name;
    };
    return Atom2;
  }()
);
var isAtom = createInstanceofPredicate("Atom", Atom);
function createAtom(name, onBecomeObservedHandler, onBecomeUnobservedHandler) {
  if (onBecomeObservedHandler === void 0) {
    onBecomeObservedHandler = noop;
  }
  if (onBecomeUnobservedHandler === void 0) {
    onBecomeUnobservedHandler = noop;
  }
  var atom = new Atom(name);
  onBecomeObserved(atom, onBecomeObservedHandler);
  onBecomeUnobserved(atom, onBecomeUnobservedHandler);
  return atom;
}
function identityComparer(a, b) {
  return a === b;
}
function structuralComparer(a, b) {
  return deepEqual(a, b);
}
function shallowComparer(a, b) {
  return deepEqual(a, b, 1);
}
function defaultComparer(a, b) {
  return areBothNaN(a, b) || identityComparer(a, b);
}
var comparer = {
  identity: identityComparer,
  structural: structuralComparer,
  default: defaultComparer,
  shallow: shallowComparer
};
var extendStatics = function(d, b) {
  extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
    d2.__proto__ = b2;
  } || function(d2, b2) {
    for (var p in b2)
      if (b2.hasOwnProperty(p))
        d2[p] = b2[p];
  };
  return extendStatics(d, b);
};
function __extends(d, b) {
  extendStatics(d, b);
  function __() {
    this.constructor = d;
  }
  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
}
var __assign = function() {
  __assign = Object.assign || function __assign2(t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];
      for (var p in s)
        if (Object.prototype.hasOwnProperty.call(s, p))
          t[p] = s[p];
    }
    return t;
  };
  return __assign.apply(this, arguments);
};
function __read(o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m)
    return o;
  var i = m.call(o), r, ar = [], e;
  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done)
      ar.push(r.value);
  } catch (error) {
    e = { error };
  } finally {
    try {
      if (r && !r.done && (m = i["return"]))
        m.call(i);
    } finally {
      if (e)
        throw e.error;
    }
  }
  return ar;
}
function __spread() {
  for (var ar = [], i = 0; i < arguments.length; i++)
    ar = ar.concat(__read(arguments[i]));
  return ar;
}
var enumerableDescriptorCache = {};
var nonEnumerableDescriptorCache = {};
function createPropertyInitializerDescriptor(prop, enumerable) {
  var cache = enumerable ? enumerableDescriptorCache : nonEnumerableDescriptorCache;
  return cache[prop] || (cache[prop] = {
    configurable: true,
    enumerable,
    get: function() {
      initializeInstance(this);
      return this[prop];
    },
    set: function(value) {
      initializeInstance(this);
      this[prop] = value;
    }
  });
}
function initializeInstance(target) {
  if (target.__mobxDidRunLazyInitializers === true)
    return;
  var decorators = target.__mobxDecorators;
  if (decorators) {
    addHiddenProp(target, "__mobxDidRunLazyInitializers", true);
    for (var key in decorators) {
      var d = decorators[key];
      d.propertyCreator(target, d.prop, d.descriptor, d.decoratorTarget, d.decoratorArguments);
    }
  }
}
function createPropDecorator(propertyInitiallyEnumerable, propertyCreator) {
  return function decoratorFactory() {
    var decoratorArguments;
    var decorator = function decorate(target, prop, descriptor, applyImmediately) {
      if (applyImmediately === true) {
        propertyCreator(target, prop, descriptor, target, decoratorArguments);
        return null;
      }
      if (!quacksLikeADecorator(arguments))
        fail("This function is a decorator, but it wasn't invoked like a decorator");
      if (!Object.prototype.hasOwnProperty.call(target, "__mobxDecorators")) {
        var inheritedDecorators = target.__mobxDecorators;
        addHiddenProp(target, "__mobxDecorators", __assign({}, inheritedDecorators));
      }
      target.__mobxDecorators[prop] = {
        prop,
        propertyCreator,
        descriptor,
        decoratorTarget: target,
        decoratorArguments
      };
      return createPropertyInitializerDescriptor(prop, propertyInitiallyEnumerable);
    };
    if (quacksLikeADecorator(arguments)) {
      decoratorArguments = EMPTY_ARRAY;
      return decorator.apply(null, arguments);
    } else {
      decoratorArguments = Array.prototype.slice.call(arguments);
      return decorator;
    }
  };
}
function quacksLikeADecorator(args) {
  return (args.length === 2 || args.length === 3) && typeof args[1] === "string" || args.length === 4 && args[3] === true;
}
function deepEnhancer(v, _, name) {
  if (isObservable(v))
    return v;
  if (Array.isArray(v))
    return observable.array(v, { name });
  if (isPlainObject(v))
    return observable.object(v, void 0, { name });
  if (isES6Map(v))
    return observable.map(v, { name });
  if (isES6Set(v))
    return observable.set(v, { name });
  return v;
}
function shallowEnhancer(v, _, name) {
  if (v === void 0 || v === null)
    return v;
  if (isObservableObject(v) || isObservableArray(v) || isObservableMap(v) || isObservableSet(v))
    return v;
  if (Array.isArray(v))
    return observable.array(v, { name, deep: false });
  if (isPlainObject(v))
    return observable.object(v, void 0, { name, deep: false });
  if (isES6Map(v))
    return observable.map(v, { name, deep: false });
  if (isES6Set(v))
    return observable.set(v, { name, deep: false });
  return fail("The shallow modifier / decorator can only used in combination with arrays, objects, maps and sets");
}
function referenceEnhancer(newValue) {
  return newValue;
}
function refStructEnhancer(v, oldValue, name) {
  if (isObservable(v))
    throw "observable.struct should not be used with observable values";
  if (deepEqual(v, oldValue))
    return oldValue;
  return v;
}
function createDecoratorForEnhancer(enhancer) {
  invariant(enhancer);
  var decorator = createPropDecorator(true, function(target, propertyName, descriptor, _decoratorTarget, decoratorArgs) {
    if (true) {
      invariant(!descriptor || !descriptor.get, '@observable cannot be used on getter (property "' + propertyName + '"), use @computed instead.');
    }
    var initialValue = descriptor ? descriptor.initializer ? descriptor.initializer.call(target) : descriptor.value : void 0;
    defineObservableProperty(target, propertyName, initialValue, enhancer);
  });
  var res = (
    // Extra process checks, as this happens during module initialization
    typeof process !== "undefined" && process.env && true ? function observableDecorator() {
      if (arguments.length < 2)
        return fail("Incorrect decorator invocation. @observable decorator doesn't expect any arguments");
      return decorator.apply(null, arguments);
    } : decorator
  );
  res.enhancer = enhancer;
  return res;
}
var defaultCreateObservableOptions = {
  deep: true,
  name: void 0,
  defaultDecorator: void 0
};
var shallowCreateObservableOptions = {
  deep: false,
  name: void 0,
  defaultDecorator: void 0
};
Object.freeze(defaultCreateObservableOptions);
Object.freeze(shallowCreateObservableOptions);
function assertValidOption(key) {
  if (!/^(deep|name|equals|defaultDecorator)$/.test(key))
    fail("invalid option for (extend)observable: " + key);
}
function asCreateObservableOptions(thing) {
  if (thing === null || thing === void 0)
    return defaultCreateObservableOptions;
  if (typeof thing === "string")
    return { name: thing, deep: true };
  if (true) {
    if (typeof thing !== "object")
      return fail("expected options object");
    Object.keys(thing).forEach(assertValidOption);
  }
  return thing;
}
function getEnhancerFromOptions(options) {
  return options.defaultDecorator ? options.defaultDecorator.enhancer : options.deep === false ? referenceEnhancer : deepEnhancer;
}
var deepDecorator = createDecoratorForEnhancer(deepEnhancer);
var shallowDecorator = createDecoratorForEnhancer(shallowEnhancer);
var refDecorator = createDecoratorForEnhancer(referenceEnhancer);
var refStructDecorator = createDecoratorForEnhancer(refStructEnhancer);
function createObservable(v, arg2, arg3) {
  if (typeof arguments[1] === "string") {
    return deepDecorator.apply(null, arguments);
  }
  if (isObservable(v))
    return v;
  var res = isPlainObject(v) ? observable.object(v, arg2, arg3) : Array.isArray(v) ? observable.array(v, arg2) : isES6Map(v) ? observable.map(v, arg2) : isES6Set(v) ? observable.set(v, arg2) : v;
  if (res !== v)
    return res;
  fail("The provided value could not be converted into an observable. If you want just create an observable reference to the object use 'observable.box(value)'");
}
var observableFactories = {
  box: function(value, options) {
    if (arguments.length > 2)
      incorrectlyUsedAsDecorator("box");
    var o = asCreateObservableOptions(options);
    return new ObservableValue(value, getEnhancerFromOptions(o), o.name, true, o.equals);
  },
  shallowBox: function(value, name) {
    if (arguments.length > 2)
      incorrectlyUsedAsDecorator("shallowBox");
    deprecated("observable.shallowBox", "observable.box(value, { deep: false })");
    return observable.box(value, { name, deep: false });
  },
  array: function(initialValues, options) {
    if (arguments.length > 2)
      incorrectlyUsedAsDecorator("array");
    var o = asCreateObservableOptions(options);
    return new ObservableArray(initialValues, getEnhancerFromOptions(o), o.name);
  },
  shallowArray: function(initialValues, name) {
    if (arguments.length > 2)
      incorrectlyUsedAsDecorator("shallowArray");
    deprecated("observable.shallowArray", "observable.array(values, { deep: false })");
    return observable.array(initialValues, { name, deep: false });
  },
  map: function(initialValues, options) {
    if (arguments.length > 2)
      incorrectlyUsedAsDecorator("map");
    var o = asCreateObservableOptions(options);
    return new ObservableMap(initialValues, getEnhancerFromOptions(o), o.name);
  },
  shallowMap: function(initialValues, name) {
    if (arguments.length > 2)
      incorrectlyUsedAsDecorator("shallowMap");
    deprecated("observable.shallowMap", "observable.map(values, { deep: false })");
    return observable.map(initialValues, { name, deep: false });
  },
  set: function(initialValues, options) {
    if (arguments.length > 2)
      incorrectlyUsedAsDecorator("set");
    var o = asCreateObservableOptions(options);
    return new ObservableSet(initialValues, getEnhancerFromOptions(o), o.name);
  },
  object: function(props, decorators, options) {
    if (typeof arguments[1] === "string")
      incorrectlyUsedAsDecorator("object");
    var o = asCreateObservableOptions(options);
    return extendObservable({}, props, decorators, o);
  },
  shallowObject: function(props, name) {
    if (typeof arguments[1] === "string")
      incorrectlyUsedAsDecorator("shallowObject");
    deprecated("observable.shallowObject", "observable.object(values, {}, { deep: false })");
    return observable.object(props, {}, { name, deep: false });
  },
  ref: refDecorator,
  shallow: shallowDecorator,
  deep: deepDecorator,
  struct: refStructDecorator
};
var observable = createObservable;
Object.keys(observableFactories).forEach(function(name) {
  return observable[name] = observableFactories[name];
});
function incorrectlyUsedAsDecorator(methodName) {
  fail(
    // process.env.NODE_ENV !== "production" &&
    "Expected one or two arguments to observable." + methodName + ". Did you accidentally try to use observable." + methodName + " as decorator?"
  );
}
var computedDecorator = createPropDecorator(false, function(instance, propertyName, descriptor, decoratorTarget, decoratorArgs) {
  if (true) {
    invariant(descriptor && descriptor.get, "Trying to declare a computed value for unspecified getter '" + propertyName + "'");
  }
  var get = descriptor.get, set2 = descriptor.set;
  var options = decoratorArgs[0] || {};
  defineComputedProperty(instance, propertyName, __assign({ get, set: set2 }, options));
});
var computedStructDecorator = computedDecorator({ equals: comparer.structural });
var computed = function computed2(arg1, arg2, arg3) {
  if (typeof arg2 === "string") {
    return computedDecorator.apply(null, arguments);
  }
  if (arg1 !== null && typeof arg1 === "object" && arguments.length === 1) {
    return computedDecorator.apply(null, arguments);
  }
  if (true) {
    invariant(typeof arg1 === "function", "First argument to `computed` should be an expression.");
    invariant(arguments.length < 3, "Computed takes one or two arguments if used as function");
  }
  var opts = typeof arg2 === "object" ? arg2 : {};
  opts.get = arg1;
  opts.set = typeof arg2 === "function" ? arg2 : opts.set;
  opts.name = opts.name || arg1.name || "";
  return new ComputedValue(opts);
};
computed.struct = computedStructDecorator;
var IDerivationState;
(function(IDerivationState2) {
  IDerivationState2[IDerivationState2["NOT_TRACKING"] = -1] = "NOT_TRACKING";
  IDerivationState2[IDerivationState2["UP_TO_DATE"] = 0] = "UP_TO_DATE";
  IDerivationState2[IDerivationState2["POSSIBLY_STALE"] = 1] = "POSSIBLY_STALE";
  IDerivationState2[IDerivationState2["STALE"] = 2] = "STALE";
})(IDerivationState || (IDerivationState = {}));
var TraceMode;
(function(TraceMode2) {
  TraceMode2[TraceMode2["NONE"] = 0] = "NONE";
  TraceMode2[TraceMode2["LOG"] = 1] = "LOG";
  TraceMode2[TraceMode2["BREAK"] = 2] = "BREAK";
})(TraceMode || (TraceMode = {}));
var CaughtException = (
  /** @class */
  /* @__PURE__ */ function() {
    function CaughtException2(cause) {
      this.cause = cause;
    }
    return CaughtException2;
  }()
);
function isCaughtException(e) {
  return e instanceof CaughtException;
}
function shouldCompute(derivation) {
  switch (derivation.dependenciesState) {
    case IDerivationState.UP_TO_DATE:
      return false;
    case IDerivationState.NOT_TRACKING:
    case IDerivationState.STALE:
      return true;
    case IDerivationState.POSSIBLY_STALE: {
      var prevAllowStateReads = allowStateReadsStart(true);
      var prevUntracked = untrackedStart();
      var obs = derivation.observing, l = obs.length;
      for (var i = 0; i < l; i++) {
        var obj = obs[i];
        if (isComputedValue(obj)) {
          if (globalState.disableErrorBoundaries) {
            obj.get();
          } else {
            try {
              obj.get();
            } catch (e) {
              untrackedEnd(prevUntracked);
              allowStateReadsEnd(prevAllowStateReads);
              return true;
            }
          }
          if (derivation.dependenciesState === IDerivationState.STALE) {
            untrackedEnd(prevUntracked);
            allowStateReadsEnd(prevAllowStateReads);
            return true;
          }
        }
      }
      changeDependenciesStateTo0(derivation);
      untrackedEnd(prevUntracked);
      allowStateReadsEnd(prevAllowStateReads);
      return false;
    }
  }
}
function checkIfStateModificationsAreAllowed(atom) {
  var hasObservers = atom.observers.length > 0;
  if (globalState.computationDepth > 0 && hasObservers)
    fail("Computed values are not allowed to cause side effects by changing observables that are already being observed. Tried to modify: " + atom.name);
  if (!globalState.allowStateChanges && (hasObservers || globalState.enforceActions === "strict"))
    fail((globalState.enforceActions ? "Since strict-mode is enabled, changing observed observable values outside actions is not allowed. Please wrap the code in an `action` if this change is intended. Tried to modify: " : "Side effects like changing state are not allowed at this point. Are you trying to modify state from, for example, the render function of a React component? Tried to modify: ") + atom.name);
}
function checkIfStateReadsAreAllowed(observable2) {
  if (!globalState.allowStateReads && globalState.observableRequiresReaction) {
    console.warn("[mobx] Observable " + observable2.name + " being read outside a reactive context");
  }
}
function trackDerivedFunction(derivation, f, context) {
  var prevAllowStateReads = allowStateReadsStart(true);
  changeDependenciesStateTo0(derivation);
  derivation.newObserving = new Array(derivation.observing.length + 100);
  derivation.unboundDepsCount = 0;
  derivation.runId = ++globalState.runId;
  var prevTracking = globalState.trackingDerivation;
  globalState.trackingDerivation = derivation;
  var result;
  if (globalState.disableErrorBoundaries === true) {
    result = f.call(context);
  } else {
    try {
      result = f.call(context);
    } catch (e) {
      result = new CaughtException(e);
    }
  }
  globalState.trackingDerivation = prevTracking;
  bindDependencies(derivation);
  if (derivation.observing.length === 0) {
    warnAboutDerivationWithoutDependencies(derivation);
  }
  allowStateReadsEnd(prevAllowStateReads);
  return result;
}
function warnAboutDerivationWithoutDependencies(derivation) {
  if (false)
    return;
  if (globalState.reactionRequiresObservable || derivation.requiresObservable) {
    console.warn("[mobx] Derivation " + derivation.name + " is created/updated without reading any observable value");
  }
}
function bindDependencies(derivation) {
  var prevObserving = derivation.observing;
  var observing = derivation.observing = derivation.newObserving;
  var lowestNewObservingDerivationState = IDerivationState.UP_TO_DATE;
  var i0 = 0, l = derivation.unboundDepsCount;
  for (var i = 0; i < l; i++) {
    var dep = observing[i];
    if (dep.diffValue === 0) {
      dep.diffValue = 1;
      if (i0 !== i)
        observing[i0] = dep;
      i0++;
    }
    if (dep.dependenciesState > lowestNewObservingDerivationState) {
      lowestNewObservingDerivationState = dep.dependenciesState;
    }
  }
  observing.length = i0;
  derivation.newObserving = null;
  l = prevObserving.length;
  while (l--) {
    var dep = prevObserving[l];
    if (dep.diffValue === 0) {
      removeObserver(dep, derivation);
    }
    dep.diffValue = 0;
  }
  while (i0--) {
    var dep = observing[i0];
    if (dep.diffValue === 1) {
      dep.diffValue = 0;
      addObserver(dep, derivation);
    }
  }
  if (lowestNewObservingDerivationState !== IDerivationState.UP_TO_DATE) {
    derivation.dependenciesState = lowestNewObservingDerivationState;
    derivation.onBecomeStale();
  }
}
function clearObserving(derivation) {
  var obs = derivation.observing;
  derivation.observing = [];
  var i = obs.length;
  while (i--)
    removeObserver(obs[i], derivation);
  derivation.dependenciesState = IDerivationState.NOT_TRACKING;
}
function untracked(action3) {
  var prev = untrackedStart();
  var res = action3();
  untrackedEnd(prev);
  return res;
}
function untrackedStart() {
  var prev = globalState.trackingDerivation;
  globalState.trackingDerivation = null;
  return prev;
}
function untrackedEnd(prev) {
  globalState.trackingDerivation = prev;
}
function allowStateReadsStart(allowStateReads) {
  var prev = globalState.allowStateReads;
  globalState.allowStateReads = allowStateReads;
  return prev;
}
function allowStateReadsEnd(prev) {
  globalState.allowStateReads = prev;
}
function changeDependenciesStateTo0(derivation) {
  if (derivation.dependenciesState === IDerivationState.UP_TO_DATE)
    return;
  derivation.dependenciesState = IDerivationState.UP_TO_DATE;
  var obs = derivation.observing;
  var i = obs.length;
  while (i--)
    obs[i].lowestObserverState = IDerivationState.UP_TO_DATE;
}
var currentActionId = 0;
var nextActionId = 1;
var functionNameDescriptor = Object.getOwnPropertyDescriptor(function() {
}, "name");
var isFunctionNameConfigurable = functionNameDescriptor && functionNameDescriptor.configurable;
function createAction(actionName, fn) {
  if (true) {
    invariant(typeof fn === "function", "`action` can only be invoked on functions");
    if (typeof actionName !== "string" || !actionName)
      fail("actions should have valid names, got: '" + actionName + "'");
  }
  var res = function() {
    return executeAction(actionName, fn, this, arguments);
  };
  if (true) {
    if (isFunctionNameConfigurable) {
      Object.defineProperty(res, "name", { value: actionName });
    }
  }
  res.isMobxAction = true;
  return res;
}
function executeAction(actionName, fn, scope, args) {
  var runInfo = _startAction(actionName, scope, args);
  try {
    return fn.apply(scope, args);
  } catch (err) {
    runInfo.error = err;
    throw err;
  } finally {
    _endAction(runInfo);
  }
}
function _startAction(actionName, scope, args) {
  var notifySpy = isSpyEnabled() && !!actionName;
  var startTime = 0;
  if (notifySpy) {
    startTime = Date.now();
    var l = args && args.length || 0;
    var flattendArgs = new Array(l);
    if (l > 0)
      for (var i = 0; i < l; i++)
        flattendArgs[i] = args[i];
    spyReportStart({
      type: "action",
      name: actionName,
      object: scope,
      arguments: flattendArgs
    });
  }
  var prevDerivation = untrackedStart();
  startBatch();
  var prevAllowStateChanges = allowStateChangesStart(true);
  var prevAllowStateReads = allowStateReadsStart(true);
  var runInfo = {
    prevDerivation,
    prevAllowStateChanges,
    prevAllowStateReads,
    notifySpy,
    startTime,
    actionId: nextActionId++,
    parentActionId: currentActionId
  };
  currentActionId = runInfo.actionId;
  return runInfo;
}
function _endAction(runInfo) {
  if (currentActionId !== runInfo.actionId) {
    fail("invalid action stack. did you forget to finish an action?");
  }
  currentActionId = runInfo.parentActionId;
  if (runInfo.error !== void 0) {
    globalState.suppressReactionErrors = true;
  }
  allowStateChangesEnd(runInfo.prevAllowStateChanges);
  allowStateReadsEnd(runInfo.prevAllowStateReads);
  endBatch();
  untrackedEnd(runInfo.prevDerivation);
  if (runInfo.notifySpy) {
    spyReportEnd({ time: Date.now() - runInfo.startTime });
  }
  globalState.suppressReactionErrors = false;
}
function allowStateChanges(allowStateChanges2, func) {
  var prev = allowStateChangesStart(allowStateChanges2);
  var res;
  try {
    res = func();
  } finally {
    allowStateChangesEnd(prev);
  }
  return res;
}
function allowStateChangesStart(allowStateChanges2) {
  var prev = globalState.allowStateChanges;
  globalState.allowStateChanges = allowStateChanges2;
  return prev;
}
function allowStateChangesEnd(prev) {
  globalState.allowStateChanges = prev;
}
function allowStateChangesInsideComputed(func) {
  var prev = globalState.computationDepth;
  globalState.computationDepth = 0;
  var res;
  try {
    res = func();
  } finally {
    globalState.computationDepth = prev;
  }
  return res;
}
var ObservableValue = (
  /** @class */
  function(_super) {
    __extends(ObservableValue2, _super);
    function ObservableValue2(value, enhancer, name, notifySpy, equals) {
      if (name === void 0) {
        name = "ObservableValue@" + getNextId();
      }
      if (notifySpy === void 0) {
        notifySpy = true;
      }
      if (equals === void 0) {
        equals = comparer.default;
      }
      var _this = _super.call(this, name) || this;
      _this.enhancer = enhancer;
      _this.name = name;
      _this.equals = equals;
      _this.hasUnreportedChange = false;
      _this.value = enhancer(value, void 0, name);
      if (notifySpy && isSpyEnabled()) {
        spyReport({ type: "create", name: _this.name, newValue: "" + _this.value });
      }
      return _this;
    }
    ObservableValue2.prototype.dehanceValue = function(value) {
      if (this.dehancer !== void 0)
        return this.dehancer(value);
      return value;
    };
    ObservableValue2.prototype.set = function(newValue) {
      var oldValue = this.value;
      newValue = this.prepareNewValue(newValue);
      if (newValue !== globalState.UNCHANGED) {
        var notifySpy = isSpyEnabled();
        if (notifySpy) {
          spyReportStart({
            type: "update",
            name: this.name,
            newValue,
            oldValue
          });
        }
        this.setNewValue(newValue);
        if (notifySpy)
          spyReportEnd();
      }
    };
    ObservableValue2.prototype.prepareNewValue = function(newValue) {
      checkIfStateModificationsAreAllowed(this);
      if (hasInterceptors(this)) {
        var change = interceptChange(this, {
          object: this,
          type: "update",
          newValue
        });
        if (!change)
          return globalState.UNCHANGED;
        newValue = change.newValue;
      }
      newValue = this.enhancer(newValue, this.value, this.name);
      return this.equals(this.value, newValue) ? globalState.UNCHANGED : newValue;
    };
    ObservableValue2.prototype.setNewValue = function(newValue) {
      var oldValue = this.value;
      this.value = newValue;
      this.reportChanged();
      if (hasListeners(this)) {
        notifyListeners(this, {
          type: "update",
          object: this,
          newValue,
          oldValue
        });
      }
    };
    ObservableValue2.prototype.get = function() {
      this.reportObserved();
      return this.dehanceValue(this.value);
    };
    ObservableValue2.prototype.intercept = function(handler) {
      return registerInterceptor(this, handler);
    };
    ObservableValue2.prototype.observe = function(listener, fireImmediately) {
      if (fireImmediately)
        listener({
          object: this,
          type: "update",
          newValue: this.value,
          oldValue: void 0
        });
      return registerListener(this, listener);
    };
    ObservableValue2.prototype.toJSON = function() {
      return this.get();
    };
    ObservableValue2.prototype.toString = function() {
      return this.name + "[" + this.value + "]";
    };
    ObservableValue2.prototype.valueOf = function() {
      return toPrimitive(this.get());
    };
    return ObservableValue2;
  }(Atom)
);
ObservableValue.prototype[primitiveSymbol()] = ObservableValue.prototype.valueOf;
var isObservableValue = createInstanceofPredicate("ObservableValue", ObservableValue);
var ComputedValue = (
  /** @class */
  function() {
    function ComputedValue2(options) {
      this.dependenciesState = IDerivationState.NOT_TRACKING;
      this.observing = [];
      this.newObserving = null;
      this.isBeingObserved = false;
      this.isPendingUnobservation = false;
      this.observers = [];
      this.observersIndexes = {};
      this.diffValue = 0;
      this.runId = 0;
      this.lastAccessedBy = 0;
      this.lowestObserverState = IDerivationState.UP_TO_DATE;
      this.unboundDepsCount = 0;
      this.__mapid = "#" + getNextId();
      this.value = new CaughtException(null);
      this.isComputing = false;
      this.isRunningSetter = false;
      this.isTracing = TraceMode.NONE;
      invariant(options.get, "missing option for computed: get");
      this.derivation = options.get;
      this.name = options.name || "ComputedValue@" + getNextId();
      if (options.set)
        this.setter = createAction(this.name + "-setter", options.set);
      this.equals = options.equals || (options.compareStructural || options.struct ? comparer.structural : comparer.default);
      this.scope = options.context;
      this.requiresReaction = !!options.requiresReaction;
      this.keepAlive = !!options.keepAlive;
    }
    ComputedValue2.prototype.onBecomeStale = function() {
      propagateMaybeChanged(this);
    };
    ComputedValue2.prototype.onBecomeUnobserved = function() {
    };
    ComputedValue2.prototype.onBecomeObserved = function() {
    };
    ComputedValue2.prototype.get = function() {
      if (this.isComputing)
        fail("Cycle detected in computation " + this.name + ": " + this.derivation);
      if (globalState.inBatch === 0 && this.observers.length === 0 && !this.keepAlive) {
        if (shouldCompute(this)) {
          this.warnAboutUntrackedRead();
          startBatch();
          this.value = this.computeValue(false);
          endBatch();
        }
      } else {
        reportObserved(this);
        if (shouldCompute(this)) {
          if (this.trackAndCompute())
            propagateChangeConfirmed(this);
        }
      }
      var result = this.value;
      if (isCaughtException(result))
        throw result.cause;
      return result;
    };
    ComputedValue2.prototype.peek = function() {
      var res = this.computeValue(false);
      if (isCaughtException(res))
        throw res.cause;
      return res;
    };
    ComputedValue2.prototype.set = function(value) {
      if (this.setter) {
        invariant(!this.isRunningSetter, "The setter of computed value '" + this.name + "' is trying to update itself. Did you intend to update an _observable_ value, instead of the computed property?");
        this.isRunningSetter = true;
        try {
          this.setter.call(this.scope, value);
        } finally {
          this.isRunningSetter = false;
        }
      } else
        invariant(false, "[ComputedValue '" + this.name + "'] It is not possible to assign a new value to a computed value.");
    };
    ComputedValue2.prototype.trackAndCompute = function() {
      if (isSpyEnabled()) {
        spyReport({
          object: this.scope,
          type: "compute",
          name: this.name
        });
      }
      var oldValue = this.value;
      var wasSuspended = (
        /* see #1208 */
        this.dependenciesState === IDerivationState.NOT_TRACKING
      );
      var newValue = this.computeValue(true);
      var changed = wasSuspended || isCaughtException(oldValue) || isCaughtException(newValue) || !this.equals(oldValue, newValue);
      if (changed) {
        this.value = newValue;
      }
      return changed;
    };
    ComputedValue2.prototype.computeValue = function(track) {
      this.isComputing = true;
      globalState.computationDepth++;
      var res;
      if (track) {
        res = trackDerivedFunction(this, this.derivation, this.scope);
      } else {
        if (globalState.disableErrorBoundaries === true) {
          res = this.derivation.call(this.scope);
        } else {
          try {
            res = this.derivation.call(this.scope);
          } catch (e) {
            res = new CaughtException(e);
          }
        }
      }
      globalState.computationDepth--;
      this.isComputing = false;
      return res;
    };
    ComputedValue2.prototype.suspend = function() {
      if (!this.keepAlive) {
        clearObserving(this);
        this.value = void 0;
      }
    };
    ComputedValue2.prototype.observe = function(listener, fireImmediately) {
      var _this = this;
      var firstTime = true;
      var prevValue = void 0;
      return autorun(function() {
        var newValue = _this.get();
        if (!firstTime || fireImmediately) {
          var prevU = untrackedStart();
          listener({
            type: "update",
            object: _this,
            newValue,
            oldValue: prevValue
          });
          untrackedEnd(prevU);
        }
        firstTime = false;
        prevValue = newValue;
      });
    };
    ComputedValue2.prototype.warnAboutUntrackedRead = function() {
      if (false)
        return;
      if (this.requiresReaction === true) {
        fail("[mobx] Computed value " + this.name + " is read outside a reactive context");
      }
      if (this.isTracing !== TraceMode.NONE) {
        console.log("[mobx.trace] '" + this.name + "' is being read outside a reactive context. Doing a full recompute");
      }
      if (globalState.computedRequiresReaction) {
        console.warn("[mobx] Computed value " + this.name + " is being read outside a reactive context. Doing a full recompute");
      }
    };
    ComputedValue2.prototype.toJSON = function() {
      return this.get();
    };
    ComputedValue2.prototype.toString = function() {
      return this.name + "[" + this.derivation.toString() + "]";
    };
    ComputedValue2.prototype.valueOf = function() {
      return toPrimitive(this.get());
    };
    return ComputedValue2;
  }()
);
ComputedValue.prototype[primitiveSymbol()] = ComputedValue.prototype.valueOf;
var isComputedValue = createInstanceofPredicate("ComputedValue", ComputedValue);
var MobXGlobals = (
  /** @class */
  /* @__PURE__ */ function() {
    function MobXGlobals2() {
      this.version = 5;
      this.UNCHANGED = {};
      this.trackingDerivation = null;
      this.computationDepth = 0;
      this.runId = 0;
      this.mobxGuid = 0;
      this.inBatch = 0;
      this.pendingUnobservations = [];
      this.pendingReactions = [];
      this.isRunningReactions = false;
      this.allowStateChanges = true;
      this.allowStateReads = true;
      this.enforceActions = false;
      this.spyListeners = [];
      this.globalReactionErrorHandlers = [];
      this.computedRequiresReaction = false;
      this.reactionRequiresObservable = false;
      this.observableRequiresReaction = false;
      this.computedConfigurable = false;
      this.disableErrorBoundaries = false;
      this.suppressReactionErrors = false;
    }
    return MobXGlobals2;
  }()
);
var canMergeGlobalState = true;
var isolateCalled = false;
var globalState = function() {
  var global2 = getGlobal();
  if (global2.__mobxInstanceCount > 0 && !global2.__mobxGlobals)
    canMergeGlobalState = false;
  if (global2.__mobxGlobals && global2.__mobxGlobals.version !== new MobXGlobals().version)
    canMergeGlobalState = false;
  if (!canMergeGlobalState) {
    setTimeout(function() {
      if (!isolateCalled) {
        fail("There are multiple, different versions of MobX active. Make sure MobX is loaded only once or use `configure({ isolateGlobalState: true })`");
      }
    }, 1);
    return new MobXGlobals();
  } else if (global2.__mobxGlobals) {
    global2.__mobxInstanceCount += 1;
    if (!global2.__mobxGlobals.UNCHANGED)
      global2.__mobxGlobals.UNCHANGED = {};
    return global2.__mobxGlobals;
  } else {
    global2.__mobxInstanceCount = 1;
    return global2.__mobxGlobals = new MobXGlobals();
  }
}();
function isolateGlobalState() {
  if (globalState.pendingReactions.length || globalState.inBatch || globalState.isRunningReactions)
    fail("isolateGlobalState should be called before MobX is running any reactions");
  isolateCalled = true;
  if (canMergeGlobalState) {
    if (--getGlobal().__mobxInstanceCount === 0)
      getGlobal().__mobxGlobals = void 0;
    globalState = new MobXGlobals();
  }
}
function addObserver(observable2, node) {
  var l = observable2.observers.length;
  if (l) {
    observable2.observersIndexes[node.__mapid] = l;
  }
  observable2.observers[l] = node;
  if (observable2.lowestObserverState > node.dependenciesState)
    observable2.lowestObserverState = node.dependenciesState;
}
function removeObserver(observable2, node) {
  if (observable2.observers.length === 1) {
    observable2.observers.length = 0;
    queueForUnobservation(observable2);
  } else {
    var list = observable2.observers;
    var map = observable2.observersIndexes;
    var filler = list.pop();
    if (filler !== node) {
      var index = map[node.__mapid] || 0;
      if (index) {
        map[filler.__mapid] = index;
      } else {
        delete map[filler.__mapid];
      }
      list[index] = filler;
    }
    delete map[node.__mapid];
  }
}
function queueForUnobservation(observable2) {
  if (observable2.isPendingUnobservation === false) {
    observable2.isPendingUnobservation = true;
    globalState.pendingUnobservations.push(observable2);
  }
}
function startBatch() {
  globalState.inBatch++;
}
function endBatch() {
  if (--globalState.inBatch === 0) {
    runReactions();
    var list = globalState.pendingUnobservations;
    for (var i = 0; i < list.length; i++) {
      var observable2 = list[i];
      observable2.isPendingUnobservation = false;
      if (observable2.observers.length === 0) {
        if (observable2.isBeingObserved) {
          observable2.isBeingObserved = false;
          observable2.onBecomeUnobserved();
        }
        if (observable2 instanceof ComputedValue) {
          observable2.suspend();
        }
      }
    }
    globalState.pendingUnobservations = [];
  }
}
function reportObserved(observable2) {
  checkIfStateReadsAreAllowed(observable2);
  var derivation = globalState.trackingDerivation;
  if (derivation !== null) {
    if (derivation.runId !== observable2.lastAccessedBy) {
      observable2.lastAccessedBy = derivation.runId;
      derivation.newObserving[derivation.unboundDepsCount++] = observable2;
      if (!observable2.isBeingObserved) {
        observable2.isBeingObserved = true;
        observable2.onBecomeObserved();
      }
    }
    return true;
  } else if (observable2.observers.length === 0 && globalState.inBatch > 0) {
    queueForUnobservation(observable2);
  }
  return false;
}
function propagateChanged(observable2) {
  if (observable2.lowestObserverState === IDerivationState.STALE)
    return;
  observable2.lowestObserverState = IDerivationState.STALE;
  var observers = observable2.observers;
  var i = observers.length;
  while (i--) {
    var d = observers[i];
    if (d.dependenciesState === IDerivationState.UP_TO_DATE) {
      if (d.isTracing !== TraceMode.NONE) {
        logTraceInfo(d, observable2);
      }
      d.onBecomeStale();
    }
    d.dependenciesState = IDerivationState.STALE;
  }
}
function propagateChangeConfirmed(observable2) {
  if (observable2.lowestObserverState === IDerivationState.STALE)
    return;
  observable2.lowestObserverState = IDerivationState.STALE;
  var observers = observable2.observers;
  var i = observers.length;
  while (i--) {
    var d = observers[i];
    if (d.dependenciesState === IDerivationState.POSSIBLY_STALE)
      d.dependenciesState = IDerivationState.STALE;
    else if (d.dependenciesState === IDerivationState.UP_TO_DATE)
      observable2.lowestObserverState = IDerivationState.UP_TO_DATE;
  }
}
function propagateMaybeChanged(observable2) {
  if (observable2.lowestObserverState !== IDerivationState.UP_TO_DATE)
    return;
  observable2.lowestObserverState = IDerivationState.POSSIBLY_STALE;
  var observers = observable2.observers;
  var i = observers.length;
  while (i--) {
    var d = observers[i];
    if (d.dependenciesState === IDerivationState.UP_TO_DATE) {
      d.dependenciesState = IDerivationState.POSSIBLY_STALE;
      if (d.isTracing !== TraceMode.NONE) {
        logTraceInfo(d, observable2);
      }
      d.onBecomeStale();
    }
  }
}
function logTraceInfo(derivation, observable2) {
  console.log("[mobx.trace] '" + derivation.name + "' is invalidated due to a change in: '" + observable2.name + "'");
  if (derivation.isTracing === TraceMode.BREAK) {
    var lines = [];
    printDepTree(getDependencyTree(derivation), lines, 1);
    new Function("debugger;\n/*\nTracing '" + derivation.name + "'\n\nYou are entering this break point because derivation '" + derivation.name + "' is being traced and '" + observable2.name + "' is now forcing it to update.\nJust follow the stacktrace you should now see in the devtools to see precisely what piece of your code is causing this update\nThe stackframe you are looking for is at least ~6-8 stack-frames up.\n\n" + (derivation instanceof ComputedValue ? derivation.derivation.toString().replace(/[*]\//g, "/") : "") + "\n\nThe dependencies for this derivation are:\n\n" + lines.join("\n") + "\n*/\n    ")();
  }
}
function printDepTree(tree, lines, depth) {
  if (lines.length >= 1e3) {
    lines.push("(and many more)");
    return;
  }
  lines.push("" + new Array(depth).join("	") + tree.name);
  if (tree.dependencies)
    tree.dependencies.forEach(function(child) {
      return printDepTree(child, lines, depth + 1);
    });
}
var Reaction = (
  /** @class */
  function() {
    function Reaction2(name, onInvalidate, errorHandler, requiresObservable) {
      if (name === void 0) {
        name = "Reaction@" + getNextId();
      }
      if (requiresObservable === void 0) {
        requiresObservable = false;
      }
      this.name = name;
      this.onInvalidate = onInvalidate;
      this.errorHandler = errorHandler;
      this.requiresObservable = requiresObservable;
      this.observing = [];
      this.newObserving = [];
      this.dependenciesState = IDerivationState.NOT_TRACKING;
      this.diffValue = 0;
      this.runId = 0;
      this.unboundDepsCount = 0;
      this.__mapid = "#" + getNextId();
      this.isDisposed = false;
      this._isScheduled = false;
      this._isTrackPending = false;
      this._isRunning = false;
      this.isTracing = TraceMode.NONE;
    }
    Reaction2.prototype.onBecomeStale = function() {
      this.schedule();
    };
    Reaction2.prototype.schedule = function() {
      if (!this._isScheduled) {
        this._isScheduled = true;
        globalState.pendingReactions.push(this);
        runReactions();
      }
    };
    Reaction2.prototype.isScheduled = function() {
      return this._isScheduled;
    };
    Reaction2.prototype.runReaction = function() {
      if (!this.isDisposed) {
        startBatch();
        this._isScheduled = false;
        if (shouldCompute(this)) {
          this._isTrackPending = true;
          try {
            this.onInvalidate();
            if (this._isTrackPending && isSpyEnabled()) {
              spyReport({
                name: this.name,
                type: "scheduled-reaction"
              });
            }
          } catch (e) {
            this.reportExceptionInDerivation(e);
          }
        }
        endBatch();
      }
    };
    Reaction2.prototype.track = function(fn) {
      startBatch();
      var notify = isSpyEnabled();
      var startTime;
      if (notify) {
        startTime = Date.now();
        spyReportStart({
          name: this.name,
          type: "reaction"
        });
      }
      this._isRunning = true;
      var result = trackDerivedFunction(this, fn, void 0);
      this._isRunning = false;
      this._isTrackPending = false;
      if (this.isDisposed) {
        clearObserving(this);
      }
      if (isCaughtException(result))
        this.reportExceptionInDerivation(result.cause);
      if (notify) {
        spyReportEnd({
          time: Date.now() - startTime
        });
      }
      endBatch();
    };
    Reaction2.prototype.reportExceptionInDerivation = function(error) {
      var _this = this;
      if (this.errorHandler) {
        this.errorHandler(error, this);
        return;
      }
      if (globalState.disableErrorBoundaries)
        throw error;
      var message = "[mobx] Encountered an uncaught exception that was thrown by a reaction or observer component, in: '" + this + "'";
      if (globalState.suppressReactionErrors) {
        console.warn("[mobx] (error in reaction '" + this.name + "' suppressed, fix error of causing action below)");
      } else {
        console.error(message, error);
      }
      if (isSpyEnabled()) {
        spyReport({
          type: "error",
          name: this.name,
          message,
          error: "" + error
        });
      }
      globalState.globalReactionErrorHandlers.forEach(function(f) {
        return f(error, _this);
      });
    };
    Reaction2.prototype.dispose = function() {
      if (!this.isDisposed) {
        this.isDisposed = true;
        if (!this._isRunning) {
          startBatch();
          clearObserving(this);
          endBatch();
        }
      }
    };
    Reaction2.prototype.getDisposer = function() {
      var r = this.dispose.bind(this);
      r.$mobx = this;
      return r;
    };
    Reaction2.prototype.toString = function() {
      return "Reaction[" + this.name + "]";
    };
    Reaction2.prototype.trace = function(enterBreakPoint) {
      if (enterBreakPoint === void 0) {
        enterBreakPoint = false;
      }
      trace(this, enterBreakPoint);
    };
    return Reaction2;
  }()
);
var MAX_REACTION_ITERATIONS = 100;
var reactionScheduler = function(f) {
  return f();
};
function runReactions() {
  if (globalState.inBatch > 0 || globalState.isRunningReactions)
    return;
  reactionScheduler(runReactionsHelper);
}
function runReactionsHelper() {
  globalState.isRunningReactions = true;
  var allReactions = globalState.pendingReactions;
  var iterations = 0;
  while (allReactions.length > 0) {
    if (++iterations === MAX_REACTION_ITERATIONS) {
      console.error("Reaction doesn't converge to a stable state after " + MAX_REACTION_ITERATIONS + " iterations." + (" Probably there is a cycle in the reactive function: " + allReactions[0]));
      allReactions.splice(0);
    }
    var remainingReactions = allReactions.splice(0);
    for (var i = 0, l = remainingReactions.length; i < l; i++)
      remainingReactions[i].runReaction();
  }
  globalState.isRunningReactions = false;
}
var isReaction = createInstanceofPredicate("Reaction", Reaction);
function setReactionScheduler(fn) {
  var baseScheduler = reactionScheduler;
  reactionScheduler = function(f) {
    return fn(function() {
      return baseScheduler(f);
    });
  };
}
function isSpyEnabled() {
  return !!globalState.spyListeners.length;
}
function spyReport(event) {
  if (!globalState.spyListeners.length)
    return;
  var listeners = globalState.spyListeners;
  for (var i = 0, l = listeners.length; i < l; i++)
    listeners[i](event);
}
function spyReportStart(event) {
  var change = __assign(__assign({}, event), { spyReportStart: true });
  spyReport(change);
}
var END_EVENT = { spyReportEnd: true };
function spyReportEnd(change) {
  if (change)
    spyReport(__assign(__assign({}, change), { spyReportEnd: true }));
  else
    spyReport(END_EVENT);
}
function spy(listener) {
  globalState.spyListeners.push(listener);
  return once(function() {
    globalState.spyListeners = globalState.spyListeners.filter(function(l) {
      return l !== listener;
    });
  });
}
function dontReassignFields() {
  fail("@action fields are not reassignable");
}
function namedActionDecorator(name) {
  return function(target, prop, descriptor) {
    if (descriptor) {
      if (descriptor.get !== void 0) {
        return fail("@action cannot be used with getters");
      }
      if (descriptor.value) {
        return {
          value: createAction(name, descriptor.value),
          enumerable: false,
          configurable: true,
          writable: true
          // for typescript, this must be writable, otherwise it cannot inherit :/ (see inheritable actions test)
        };
      }
      var initializer_1 = descriptor.initializer;
      return {
        enumerable: false,
        configurable: true,
        writable: true,
        initializer: function() {
          return createAction(name, initializer_1.call(this));
        }
      };
    }
    return actionFieldDecorator(name).apply(this, arguments);
  };
}
function actionFieldDecorator(name) {
  return function(target, prop, descriptor) {
    Object.defineProperty(target, prop, {
      configurable: true,
      enumerable: false,
      get: function() {
        return void 0;
      },
      set: function(value) {
        addHiddenProp(this, prop, action(name, value));
      }
    });
  };
}
function boundActionDecorator(target, propertyName, descriptor, applyToInstance) {
  if (applyToInstance === true) {
    defineBoundAction(target, propertyName, descriptor.value);
    return null;
  }
  if (descriptor) {
    return {
      configurable: true,
      enumerable: false,
      get: function() {
        defineBoundAction(this, propertyName, descriptor.value || descriptor.initializer.call(this));
        return this[propertyName];
      },
      set: dontReassignFields
    };
  }
  return {
    enumerable: false,
    configurable: true,
    set: function(v) {
      defineBoundAction(this, propertyName, v);
    },
    get: function() {
      return void 0;
    }
  };
}
var action = function action2(arg1, arg2, arg3, arg4) {
  if (arguments.length === 1 && typeof arg1 === "function")
    return createAction(arg1.name || "<unnamed action>", arg1);
  if (arguments.length === 2 && typeof arg2 === "function")
    return createAction(arg1, arg2);
  if (arguments.length === 1 && typeof arg1 === "string")
    return namedActionDecorator(arg1);
  if (arg4 === true) {
    arg1[arg2] = createAction(arg1.name || arg2, arg3.value);
  } else {
    return namedActionDecorator(arg2).apply(null, arguments);
  }
};
action.bound = boundActionDecorator;
function isAction(thing) {
  return typeof thing === "function" && thing.isMobxAction === true;
}
function defineBoundAction(target, propertyName, fn) {
  addHiddenProp(target, propertyName, createAction(propertyName, fn.bind(target)));
}
function autorun(view, opts) {
  if (opts === void 0) {
    opts = EMPTY_OBJECT;
  }
  if (true) {
    invariant(typeof view === "function", "Autorun expects a function as first argument");
    invariant(isAction(view) === false, "Autorun does not accept actions since actions are untrackable");
  }
  var name = opts && opts.name || view.name || "Autorun@" + getNextId();
  var runSync = !opts.scheduler && !opts.delay;
  var reaction2;
  if (runSync) {
    reaction2 = new Reaction(name, function() {
      this.track(reactionRunner);
    }, opts.onError, opts.requiresObservable);
  } else {
    var scheduler_1 = createSchedulerFromOptions(opts);
    var isScheduled_1 = false;
    reaction2 = new Reaction(name, function() {
      if (!isScheduled_1) {
        isScheduled_1 = true;
        scheduler_1(function() {
          isScheduled_1 = false;
          if (!reaction2.isDisposed)
            reaction2.track(reactionRunner);
        });
      }
    }, opts.onError, opts.requiresObservable);
  }
  function reactionRunner() {
    view(reaction2);
  }
  reaction2.schedule();
  return reaction2.getDisposer();
}
var run = function(f) {
  return f();
};
function createSchedulerFromOptions(opts) {
  return opts.scheduler ? opts.scheduler : opts.delay ? function(f) {
    return setTimeout(f, opts.delay);
  } : run;
}
function reaction(expression, effect, opts) {
  if (opts === void 0) {
    opts = EMPTY_OBJECT;
  }
  if (typeof opts === "boolean") {
    opts = { fireImmediately: opts };
    deprecated("Using fireImmediately as argument is deprecated. Use '{ fireImmediately: true }' instead");
  }
  if (true) {
    invariant(typeof expression === "function", "First argument to reaction should be a function");
    invariant(typeof opts === "object", "Third argument of reactions should be an object");
  }
  var name = opts.name || "Reaction@" + getNextId();
  var effectAction = action(name, opts.onError ? wrapErrorHandler(opts.onError, effect) : effect);
  var runSync = !opts.scheduler && !opts.delay;
  var scheduler = createSchedulerFromOptions(opts);
  var firstTime = true;
  var isScheduled = false;
  var value;
  var equals = opts.compareStructural ? comparer.structural : opts.equals || comparer.default;
  var r = new Reaction(name, function() {
    if (firstTime || runSync) {
      reactionRunner();
    } else if (!isScheduled) {
      isScheduled = true;
      scheduler(reactionRunner);
    }
  }, opts.onError, opts.requiresObservable);
  function reactionRunner() {
    isScheduled = false;
    if (r.isDisposed)
      return;
    var changed = false;
    r.track(function() {
      var nextValue = expression(r);
      changed = firstTime || !equals(value, nextValue);
      value = nextValue;
    });
    if (firstTime && opts.fireImmediately)
      effectAction(value, r);
    if (!firstTime && changed === true)
      effectAction(value, r);
    if (firstTime)
      firstTime = false;
  }
  r.schedule();
  return r.getDisposer();
}
function wrapErrorHandler(errorHandler, baseFn) {
  return function() {
    try {
      return baseFn.apply(this, arguments);
    } catch (e) {
      errorHandler.call(this, e);
    }
  };
}
function onBecomeObserved(thing, arg2, arg3) {
  return interceptHook("onBecomeObserved", thing, arg2, arg3);
}
function onBecomeUnobserved(thing, arg2, arg3) {
  return interceptHook("onBecomeUnobserved", thing, arg2, arg3);
}
function interceptHook(hook, thing, arg2, arg3) {
  var atom = typeof arg3 === "function" ? getAtom(thing, arg2) : getAtom(thing);
  var cb = typeof arg3 === "function" ? arg3 : arg2;
  var orig = atom[hook];
  if (typeof orig !== "function")
    return fail("Not an atom that can be (un)observed");
  atom[hook] = function() {
    orig.call(this);
    cb.call(this);
  };
  return function() {
    atom[hook] = orig;
  };
}
function configure(options) {
  var enforceActions = options.enforceActions, computedRequiresReaction = options.computedRequiresReaction, computedConfigurable = options.computedConfigurable, disableErrorBoundaries = options.disableErrorBoundaries, arrayBuffer = options.arrayBuffer, reactionScheduler2 = options.reactionScheduler, reactionRequiresObservable = options.reactionRequiresObservable, observableRequiresReaction = options.observableRequiresReaction;
  if (options.isolateGlobalState === true) {
    isolateGlobalState();
  }
  if (enforceActions !== void 0) {
    if (typeof enforceActions === "boolean" || enforceActions === "strict")
      deprecated(`Deprecated value for 'enforceActions', use 'false' => '"never"', 'true' => '"observed"', '"strict"' => "'always'" instead`);
    var ea = void 0;
    switch (enforceActions) {
      case true:
      case "observed":
        ea = true;
        break;
      case false:
      case "never":
        ea = false;
        break;
      case "strict":
      case "always":
        ea = "strict";
        break;
      default:
        fail("Invalid value for 'enforceActions': '" + enforceActions + "', expected 'never', 'always' or 'observed'");
    }
    globalState.enforceActions = ea;
    globalState.allowStateChanges = ea === true || ea === "strict" ? false : true;
  }
  if (computedRequiresReaction !== void 0) {
    globalState.computedRequiresReaction = !!computedRequiresReaction;
  }
  if (reactionRequiresObservable !== void 0) {
    globalState.reactionRequiresObservable = !!reactionRequiresObservable;
  }
  if (observableRequiresReaction !== void 0) {
    globalState.observableRequiresReaction = !!observableRequiresReaction;
    globalState.allowStateReads = !globalState.observableRequiresReaction;
  }
  if (computedConfigurable !== void 0) {
    globalState.computedConfigurable = !!computedConfigurable;
  }
  if (disableErrorBoundaries !== void 0) {
    if (disableErrorBoundaries === true)
      console.warn("WARNING: Debug feature only. MobX will NOT recover from errors if this is on.");
    globalState.disableErrorBoundaries = !!disableErrorBoundaries;
  }
  if (typeof arrayBuffer === "number") {
    reserveArrayBuffer(arrayBuffer);
  }
  if (reactionScheduler2) {
    setReactionScheduler(reactionScheduler2);
  }
}
function extendObservable(target, properties, decorators, options) {
  if (true) {
    invariant(arguments.length >= 2 && arguments.length <= 4, "'extendObservable' expected 2-4 arguments");
    invariant(typeof target === "object", "'extendObservable' expects an object as first argument");
    invariant(!isObservableMap(target), "'extendObservable' should not be used on maps, use map.merge instead");
    invariant(!isObservable(properties), "Extending an object with another observable (object) is not supported. Please construct an explicit propertymap, using `toJS` if need. See issue #540");
    if (decorators) {
      for (var key in decorators)
        if (!(key in properties))
          fail("Trying to declare a decorator for unspecified property '" + key + "'");
    }
  }
  options = asCreateObservableOptions(options);
  var defaultDecorator = options.defaultDecorator || (options.deep === false ? refDecorator : deepDecorator);
  initializeInstance(target);
  asObservableObject(target, options.name, defaultDecorator.enhancer);
  startBatch();
  try {
    var keys2 = Object.getOwnPropertyNames(properties);
    for (var i = 0, l = keys2.length; i < l; i++) {
      var key = keys2[i];
      var descriptor = Object.getOwnPropertyDescriptor(properties, key);
      if (true) {
        if (isComputed(descriptor.value))
          fail("Passing a 'computed' as initial property value is no longer supported by extendObservable. Use a getter or decorator instead");
      }
      var decorator = decorators && key in decorators ? decorators[key] : descriptor.get ? computedDecorator : defaultDecorator;
      if (typeof decorator !== "function")
        return fail("Not a valid decorator for '" + key + "', got: " + decorator);
      var resultDescriptor = decorator(target, key, descriptor, true);
      if (resultDescriptor)
        Object.defineProperty(target, key, resultDescriptor);
    }
  } finally {
    endBatch();
  }
  return target;
}
function getDependencyTree(thing, property) {
  return nodeToDependencyTree(getAtom(thing, property));
}
function nodeToDependencyTree(node) {
  var result = {
    name: node.name
  };
  if (node.observing && node.observing.length > 0)
    result.dependencies = unique(node.observing).map(nodeToDependencyTree);
  return result;
}
function FlowCancellationError() {
  this.message = "FLOW_CANCELLED";
}
FlowCancellationError.prototype = Object.create(Error.prototype);
function interceptReads(thing, propOrHandler, handler) {
  var target;
  if (isObservableMap(thing) || isObservableArray(thing) || isObservableValue(thing)) {
    target = getAdministration(thing);
  } else if (isObservableObject(thing)) {
    if (typeof propOrHandler !== "string")
      return fail("InterceptReads can only be used with a specific property, not with an object in general");
    target = getAdministration(thing, propOrHandler);
  } else {
    return fail("Expected observable map, object or array as first array");
  }
  if (target.dehancer !== void 0)
    return fail("An intercept reader was already established");
  target.dehancer = typeof propOrHandler === "function" ? propOrHandler : handler;
  return function() {
    target.dehancer = void 0;
  };
}
function intercept(thing, propOrHandler, handler) {
  if (typeof handler === "function")
    return interceptProperty(thing, propOrHandler, handler);
  else
    return interceptInterceptable(thing, propOrHandler);
}
function interceptInterceptable(thing, handler) {
  return getAdministration(thing).intercept(handler);
}
function interceptProperty(thing, property, handler) {
  return getAdministration(thing, property).intercept(handler);
}
function _isComputed(value, property) {
  if (value === null || value === void 0)
    return false;
  if (property !== void 0) {
    if (isObservableObject(value) === false)
      return false;
    if (!value.$mobx.values[property])
      return false;
    var atom = getAtom(value, property);
    return isComputedValue(atom);
  }
  return isComputedValue(value);
}
function isComputed(value) {
  if (arguments.length > 1)
    return fail("isComputed expects only 1 argument. Use isObservableProp to inspect the observability of a property");
  return _isComputed(value);
}
function isComputedProp(value, propName) {
  if (typeof propName !== "string")
    return fail("isComputed expected a property name as second argument");
  return _isComputed(value, propName);
}
function _isObservable(value, property) {
  if (value === null || value === void 0)
    return false;
  if (property !== void 0) {
    if (isObservableMap(value) || isObservableArray(value))
      return fail("isObservable(object, propertyName) is not supported for arrays and maps. Use map.has or array.length instead.");
    if (isObservableObject(value)) {
      var o = value.$mobx;
      return o.values && !!o.values[property];
    }
    return false;
  }
  return isObservableObject(value) || !!value.$mobx || isAtom(value) || isReaction(value) || isComputedValue(value);
}
function isObservable(value) {
  if (arguments.length !== 1)
    fail("isObservable expects only 1 argument. Use isObservableProp to inspect the observability of a property");
  return _isObservable(value);
}
function keys(obj) {
  if (isObservableObject(obj)) {
    return obj.$mobx.getKeys();
  }
  if (isObservableMap(obj)) {
    return iteratorToArray(obj.keys());
  }
  if (isObservableSet(obj)) {
    return iteratorToArray(obj.keys());
  }
  if (isObservableArray(obj)) {
    return obj.map(function(_, index) {
      return index;
    });
  }
  return fail("'keys()' can only be used on observable objects, arrays, sets and maps");
}
function values(obj) {
  if (isObservableObject(obj)) {
    return keys(obj).map(function(key) {
      return obj[key];
    });
  }
  if (isObservableMap(obj)) {
    return keys(obj).map(function(key) {
      return obj.get(key);
    });
  }
  if (isObservableSet(obj)) {
    return iteratorToArray(obj.values());
  }
  if (isObservableArray(obj)) {
    return obj.slice();
  }
  return fail("'values()' can only be used on observable objects, arrays, sets and maps");
}
function entries(obj) {
  if (isObservableObject(obj)) {
    return keys(obj).map(function(key) {
      return [key, obj[key]];
    });
  }
  if (isObservableMap(obj)) {
    return keys(obj).map(function(key) {
      return [key, obj.get(key)];
    });
  }
  if (isObservableSet(obj)) {
    return iteratorToArray(obj.entries());
  }
  if (isObservableArray(obj)) {
    return obj.map(function(key, index) {
      return [index, key];
    });
  }
  return fail("'entries()' can only be used on observable objects, arrays and maps");
}
function set(obj, key, value) {
  if (arguments.length === 2 && !isObservableSet(obj)) {
    startBatch();
    var values_1 = key;
    try {
      for (var key_1 in values_1)
        set(obj, key_1, values_1[key_1]);
    } finally {
      endBatch();
    }
    return;
  }
  if (isObservableObject(obj)) {
    var adm = obj.$mobx;
    var existingObservable = adm.values[key];
    if (existingObservable) {
      adm.write(obj, key, value);
    } else {
      defineObservableProperty(obj, key, value, adm.defaultEnhancer);
    }
  } else if (isObservableMap(obj)) {
    obj.set(key, value);
  } else if (isObservableSet(obj)) {
    obj.add(key);
  } else if (isObservableArray(obj)) {
    if (typeof key !== "number")
      key = parseInt(key, 10);
    invariant(key >= 0, "Not a valid index: '" + key + "'");
    startBatch();
    if (key >= obj.length)
      obj.length = key + 1;
    obj[key] = value;
    endBatch();
  } else {
    return fail("'set()' can only be used on observable objects, arrays and maps");
  }
}
function observe(thing, propOrCb, cbOrFire, fireImmediately) {
  if (typeof cbOrFire === "function")
    return observeObservableProperty(thing, propOrCb, cbOrFire, fireImmediately);
  else
    return observeObservable(thing, propOrCb, cbOrFire);
}
function observeObservable(thing, listener, fireImmediately) {
  return getAdministration(thing).observe(listener, fireImmediately);
}
function observeObservableProperty(thing, property, listener, fireImmediately) {
  return getAdministration(thing, property).observe(listener, fireImmediately);
}
function trace() {
  var args = [];
  for (var _i = 0; _i < arguments.length; _i++) {
    args[_i] = arguments[_i];
  }
  var enterBreakPoint = false;
  if (typeof args[args.length - 1] === "boolean")
    enterBreakPoint = args.pop();
  var derivation = getAtomFromArgs(args);
  if (!derivation) {
    return fail("'trace(break?)' can only be used inside a tracked computed value or a Reaction. Consider passing in the computed value or reaction explicitly");
  }
  if (derivation.isTracing === TraceMode.NONE) {
    console.log("[mobx.trace] '" + derivation.name + "' tracing enabled");
  }
  derivation.isTracing = enterBreakPoint ? TraceMode.BREAK : TraceMode.LOG;
}
function getAtomFromArgs(args) {
  switch (args.length) {
    case 0:
      return globalState.trackingDerivation;
    case 1:
      return getAtom(args[0]);
    case 2:
      return getAtom(args[0], args[1]);
  }
}
function transaction(action3, thisArg) {
  if (thisArg === void 0) {
    thisArg = void 0;
  }
  startBatch();
  try {
    return action3.apply(thisArg);
  } finally {
    endBatch();
  }
}
function hasInterceptors(interceptable) {
  return interceptable.interceptors !== void 0 && interceptable.interceptors.length > 0;
}
function registerInterceptor(interceptable, handler) {
  var interceptors = interceptable.interceptors || (interceptable.interceptors = []);
  interceptors.push(handler);
  return once(function() {
    var idx = interceptors.indexOf(handler);
    if (idx !== -1)
      interceptors.splice(idx, 1);
  });
}
function interceptChange(interceptable, change) {
  var prevU = untrackedStart();
  try {
    var interceptors = interceptable.interceptors;
    if (interceptors)
      for (var i = 0, l = interceptors.length; i < l; i++) {
        change = interceptors[i](change);
        invariant(!change || change.type, "Intercept handlers should return nothing or a change object");
        if (!change)
          break;
      }
    return change;
  } finally {
    untrackedEnd(prevU);
  }
}
function hasListeners(listenable) {
  return listenable.changeListeners !== void 0 && listenable.changeListeners.length > 0;
}
function registerListener(listenable, handler) {
  var listeners = listenable.changeListeners || (listenable.changeListeners = []);
  listeners.push(handler);
  return once(function() {
    var idx = listeners.indexOf(handler);
    if (idx !== -1)
      listeners.splice(idx, 1);
  });
}
function notifyListeners(listenable, change) {
  var prevU = untrackedStart();
  var listeners = listenable.changeListeners;
  if (!listeners)
    return;
  listeners = listeners.slice();
  for (var i = 0, l = listeners.length; i < l; i++) {
    listeners[i](change);
  }
  untrackedEnd(prevU);
}
var MAX_SPLICE_SIZE = 1e4;
var safariPrototypeSetterInheritanceBug = function() {
  var v = false;
  var p = {};
  Object.defineProperty(p, "0", {
    set: function() {
      v = true;
    }
  });
  Object.create(p)["0"] = 1;
  return v === false;
}();
var OBSERVABLE_ARRAY_BUFFER_SIZE = 0;
var StubArray = (
  /** @class */
  /* @__PURE__ */ function() {
    function StubArray2() {
    }
    return StubArray2;
  }()
);
function inherit(ctor, proto) {
  if (typeof Object["setPrototypeOf"] !== "undefined") {
    Object["setPrototypeOf"](ctor.prototype, proto);
  } else if (typeof ctor.prototype.__proto__ !== "undefined") {
    ctor.prototype.__proto__ = proto;
  } else {
    ctor["prototype"] = proto;
  }
}
inherit(StubArray, Array.prototype);
if (Object.isFrozen(Array)) {
  [
    "constructor",
    "push",
    "shift",
    "concat",
    "pop",
    "unshift",
    "replace",
    "find",
    "findIndex",
    "splice",
    "reverse",
    "sort"
  ].forEach(function(key) {
    Object.defineProperty(StubArray.prototype, key, {
      configurable: true,
      writable: true,
      value: Array.prototype[key]
    });
  });
}
var ObservableArrayAdministration = (
  /** @class */
  function() {
    function ObservableArrayAdministration2(name, enhancer, array, owned) {
      this.array = array;
      this.owned = owned;
      this.values = [];
      this.lastKnownLength = 0;
      this.atom = new Atom(name || "ObservableArray@" + getNextId());
      this.enhancer = function(newV, oldV) {
        return enhancer(newV, oldV, name + "[..]");
      };
    }
    ObservableArrayAdministration2.prototype.dehanceValue = function(value) {
      if (this.dehancer !== void 0)
        return this.dehancer(value);
      return value;
    };
    ObservableArrayAdministration2.prototype.dehanceValues = function(values2) {
      if (this.dehancer !== void 0 && values2.length > 0)
        return values2.map(this.dehancer);
      return values2;
    };
    ObservableArrayAdministration2.prototype.intercept = function(handler) {
      return registerInterceptor(this, handler);
    };
    ObservableArrayAdministration2.prototype.observe = function(listener, fireImmediately) {
      if (fireImmediately === void 0) {
        fireImmediately = false;
      }
      if (fireImmediately) {
        listener({
          object: this.array,
          type: "splice",
          index: 0,
          added: this.values.slice(),
          addedCount: this.values.length,
          removed: [],
          removedCount: 0
        });
      }
      return registerListener(this, listener);
    };
    ObservableArrayAdministration2.prototype.getArrayLength = function() {
      this.atom.reportObserved();
      return this.values.length;
    };
    ObservableArrayAdministration2.prototype.setArrayLength = function(newLength) {
      if (typeof newLength !== "number" || newLength < 0)
        throw new Error("[mobx.array] Out of range: " + newLength);
      var currentLength = this.values.length;
      if (newLength === currentLength)
        return;
      else if (newLength > currentLength) {
        var newItems = new Array(newLength - currentLength);
        for (var i = 0; i < newLength - currentLength; i++)
          newItems[i] = void 0;
        this.spliceWithArray(currentLength, 0, newItems);
      } else
        this.spliceWithArray(newLength, currentLength - newLength);
    };
    ObservableArrayAdministration2.prototype.updateArrayLength = function(oldLength, delta) {
      if (oldLength !== this.lastKnownLength)
        throw new Error("[mobx] Modification exception: the internal structure of an observable array was changed. Did you use peek() to change it?");
      this.lastKnownLength += delta;
      if (delta > 0 && oldLength + delta + 1 > OBSERVABLE_ARRAY_BUFFER_SIZE)
        reserveArrayBuffer(oldLength + delta + 1);
    };
    ObservableArrayAdministration2.prototype.spliceWithArray = function(index, deleteCount, newItems) {
      var _this = this;
      checkIfStateModificationsAreAllowed(this.atom);
      var length = this.values.length;
      if (index === void 0)
        index = 0;
      else if (index > length)
        index = length;
      else if (index < 0)
        index = Math.max(0, length + index);
      if (arguments.length === 1)
        deleteCount = length - index;
      else if (deleteCount === void 0 || deleteCount === null)
        deleteCount = 0;
      else
        deleteCount = Math.max(0, Math.min(deleteCount, length - index));
      if (newItems === void 0)
        newItems = EMPTY_ARRAY;
      if (hasInterceptors(this)) {
        var change = interceptChange(this, {
          object: this.array,
          type: "splice",
          index,
          removedCount: deleteCount,
          added: newItems
        });
        if (!change)
          return EMPTY_ARRAY;
        deleteCount = change.removedCount;
        newItems = change.added;
      }
      newItems = newItems.length === 0 ? newItems : newItems.map(function(v) {
        return _this.enhancer(v, void 0);
      });
      var lengthDelta = newItems.length - deleteCount;
      this.updateArrayLength(length, lengthDelta);
      var res = this.spliceItemsIntoValues(index, deleteCount, newItems);
      if (deleteCount !== 0 || newItems.length !== 0)
        this.notifyArraySplice(index, newItems, res);
      return this.dehanceValues(res);
    };
    ObservableArrayAdministration2.prototype.spliceItemsIntoValues = function(index, deleteCount, newItems) {
      var _a;
      if (newItems.length < MAX_SPLICE_SIZE) {
        return (_a = this.values).splice.apply(_a, __spread([index, deleteCount], newItems));
      } else {
        var res = this.values.slice(index, index + deleteCount);
        this.values = this.values.slice(0, index).concat(newItems, this.values.slice(index + deleteCount));
        return res;
      }
    };
    ObservableArrayAdministration2.prototype.notifyArrayChildUpdate = function(index, newValue, oldValue) {
      var notifySpy = !this.owned && isSpyEnabled();
      var notify = hasListeners(this);
      var change = notify || notifySpy ? {
        object: this.array,
        type: "update",
        index,
        newValue,
        oldValue
      } : null;
      if (notifySpy)
        spyReportStart(__assign(__assign({}, change), { name: this.atom.name }));
      this.atom.reportChanged();
      if (notify)
        notifyListeners(this, change);
      if (notifySpy)
        spyReportEnd();
    };
    ObservableArrayAdministration2.prototype.notifyArraySplice = function(index, added, removed) {
      var notifySpy = !this.owned && isSpyEnabled();
      var notify = hasListeners(this);
      var change = notify || notifySpy ? {
        object: this.array,
        type: "splice",
        index,
        removed,
        added,
        removedCount: removed.length,
        addedCount: added.length
      } : null;
      if (notifySpy)
        spyReportStart(__assign(__assign({}, change), { name: this.atom.name }));
      this.atom.reportChanged();
      if (notify)
        notifyListeners(this, change);
      if (notifySpy)
        spyReportEnd();
    };
    return ObservableArrayAdministration2;
  }()
);
var ObservableArray = (
  /** @class */
  function(_super) {
    __extends(ObservableArray2, _super);
    function ObservableArray2(initialValues, enhancer, name, owned) {
      if (name === void 0) {
        name = "ObservableArray@" + getNextId();
      }
      if (owned === void 0) {
        owned = false;
      }
      var _this = _super.call(this) || this;
      var adm = new ObservableArrayAdministration(name, enhancer, _this, owned);
      addHiddenFinalProp(_this, "$mobx", adm);
      if (initialValues && initialValues.length) {
        var prev = allowStateChangesStart(true);
        _this.spliceWithArray(0, 0, initialValues);
        allowStateChangesEnd(prev);
      }
      if (safariPrototypeSetterInheritanceBug) {
        Object.defineProperty(adm.array, "0", ENTRY_0);
      }
      return _this;
    }
    ObservableArray2.prototype.intercept = function(handler) {
      return this.$mobx.intercept(handler);
    };
    ObservableArray2.prototype.observe = function(listener, fireImmediately) {
      if (fireImmediately === void 0) {
        fireImmediately = false;
      }
      return this.$mobx.observe(listener, fireImmediately);
    };
    ObservableArray2.prototype.clear = function() {
      return this.splice(0);
    };
    ObservableArray2.prototype.concat = function() {
      var arrays = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        arrays[_i] = arguments[_i];
      }
      this.$mobx.atom.reportObserved();
      return Array.prototype.concat.apply(this.peek(), arrays.map(function(a) {
        return isObservableArray(a) ? a.peek() : a;
      }));
    };
    ObservableArray2.prototype.replace = function(newItems) {
      return this.$mobx.spliceWithArray(0, this.$mobx.values.length, newItems);
    };
    ObservableArray2.prototype.toJS = function() {
      return this.slice();
    };
    ObservableArray2.prototype.toJSON = function() {
      return this.toJS();
    };
    ObservableArray2.prototype.peek = function() {
      this.$mobx.atom.reportObserved();
      return this.$mobx.dehanceValues(this.$mobx.values);
    };
    ObservableArray2.prototype.find = function(predicate, thisArg, fromIndex) {
      if (fromIndex === void 0) {
        fromIndex = 0;
      }
      if (arguments.length === 3)
        deprecated("The array.find fromIndex argument to find will not be supported anymore in the next major");
      var idx = this.findIndex.apply(this, arguments);
      return idx === -1 ? void 0 : this.get(idx);
    };
    ObservableArray2.prototype.findIndex = function(predicate, thisArg, fromIndex) {
      if (fromIndex === void 0) {
        fromIndex = 0;
      }
      if (arguments.length === 3)
        deprecated("The array.findIndex fromIndex argument to find will not be supported anymore in the next major");
      var items = this.peek(), l = items.length;
      for (var i = fromIndex; i < l; i++)
        if (predicate.call(thisArg, items[i], i, this))
          return i;
      return -1;
    };
    ObservableArray2.prototype.splice = function(index, deleteCount) {
      var newItems = [];
      for (var _i = 2; _i < arguments.length; _i++) {
        newItems[_i - 2] = arguments[_i];
      }
      switch (arguments.length) {
        case 0:
          return [];
        case 1:
          return this.$mobx.spliceWithArray(index);
        case 2:
          return this.$mobx.spliceWithArray(index, deleteCount);
      }
      return this.$mobx.spliceWithArray(index, deleteCount, newItems);
    };
    ObservableArray2.prototype.spliceWithArray = function(index, deleteCount, newItems) {
      return this.$mobx.spliceWithArray(index, deleteCount, newItems);
    };
    ObservableArray2.prototype.push = function() {
      var items = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        items[_i] = arguments[_i];
      }
      var adm = this.$mobx;
      adm.spliceWithArray(adm.values.length, 0, items);
      return adm.values.length;
    };
    ObservableArray2.prototype.pop = function() {
      return this.splice(Math.max(this.$mobx.values.length - 1, 0), 1)[0];
    };
    ObservableArray2.prototype.shift = function() {
      return this.splice(0, 1)[0];
    };
    ObservableArray2.prototype.unshift = function() {
      var items = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        items[_i] = arguments[_i];
      }
      var adm = this.$mobx;
      adm.spliceWithArray(0, 0, items);
      return adm.values.length;
    };
    ObservableArray2.prototype.reverse = function() {
      var clone = this.slice();
      return clone.reverse.apply(clone, arguments);
    };
    ObservableArray2.prototype.sort = function(compareFn) {
      var clone = this.slice();
      return clone.sort.apply(clone, arguments);
    };
    ObservableArray2.prototype.remove = function(value) {
      var idx = this.$mobx.dehanceValues(this.$mobx.values).indexOf(value);
      if (idx > -1) {
        this.splice(idx, 1);
        return true;
      }
      return false;
    };
    ObservableArray2.prototype.move = function(fromIndex, toIndex) {
      deprecated("observableArray.move is deprecated, use .slice() & .replace() instead");
      function checkIndex(index) {
        if (index < 0) {
          throw new Error("[mobx.array] Index out of bounds: " + index + " is negative");
        }
        var length = this.$mobx.values.length;
        if (index >= length) {
          throw new Error("[mobx.array] Index out of bounds: " + index + " is not smaller than " + length);
        }
      }
      checkIndex.call(this, fromIndex);
      checkIndex.call(this, toIndex);
      if (fromIndex === toIndex) {
        return;
      }
      var oldItems = this.$mobx.values;
      var newItems;
      if (fromIndex < toIndex) {
        newItems = __spread(oldItems.slice(0, fromIndex), oldItems.slice(fromIndex + 1, toIndex + 1), [
          oldItems[fromIndex]
        ], oldItems.slice(toIndex + 1));
      } else {
        newItems = __spread(oldItems.slice(0, toIndex), [
          oldItems[fromIndex]
        ], oldItems.slice(toIndex, fromIndex), oldItems.slice(fromIndex + 1));
      }
      this.replace(newItems);
    };
    ObservableArray2.prototype.get = function(index) {
      var impl = this.$mobx;
      if (impl) {
        if (index < impl.values.length) {
          impl.atom.reportObserved();
          return impl.dehanceValue(impl.values[index]);
        }
        console.warn("[mobx.array] Attempt to read an array index (" + index + ") that is out of bounds (" + impl.values.length + "). Please check length first. Out of bound indices will not be tracked by MobX");
      }
      return void 0;
    };
    ObservableArray2.prototype.set = function(index, newValue) {
      var adm = this.$mobx;
      var values2 = adm.values;
      if (index < values2.length) {
        checkIfStateModificationsAreAllowed(adm.atom);
        var oldValue = values2[index];
        if (hasInterceptors(adm)) {
          var change = interceptChange(adm, {
            type: "update",
            object: this,
            index,
            newValue
          });
          if (!change)
            return;
          newValue = change.newValue;
        }
        newValue = adm.enhancer(newValue, oldValue);
        var changed = newValue !== oldValue;
        if (changed) {
          values2[index] = newValue;
          adm.notifyArrayChildUpdate(index, newValue, oldValue);
        }
      } else if (index === values2.length) {
        adm.spliceWithArray(index, 0, [newValue]);
      } else {
        throw new Error("[mobx.array] Index out of bounds, " + index + " is larger than " + values2.length);
      }
    };
    return ObservableArray2;
  }(StubArray)
);
declareIterator(ObservableArray.prototype, function() {
  this.$mobx.atom.reportObserved();
  var self2 = this;
  var nextIndex = 0;
  return makeIterable({
    next: function() {
      return nextIndex < self2.length ? { value: self2[nextIndex++], done: false } : { done: true, value: void 0 };
    }
  });
});
Object.defineProperty(ObservableArray.prototype, "length", {
  enumerable: false,
  configurable: true,
  get: function() {
    return this.$mobx.getArrayLength();
  },
  set: function(newLength) {
    this.$mobx.setArrayLength(newLength);
  }
});
addHiddenProp(ObservableArray.prototype, toStringTagSymbol(), "Array");
["indexOf", "join", "lastIndexOf", "slice", "toString", "toLocaleString"].forEach(function(funcName) {
  var baseFunc = Array.prototype[funcName];
  invariant(typeof baseFunc === "function", "Base function not defined on Array prototype: '" + funcName + "'");
  addHiddenProp(ObservableArray.prototype, funcName, function() {
    return baseFunc.apply(this.peek(), arguments);
  });
});
[
  "every",
  "filter",
  //"find", // implemented individually (IE support)
  //"findIndex", // implemented individually (IE support)
  //"flatMap", // not supported
  "forEach",
  "map",
  "some"
].forEach(function(funcName) {
  var baseFunc = Array.prototype[funcName];
  invariant(typeof baseFunc === "function", "Base function not defined on Array prototype: '" + funcName + "'");
  addHiddenProp(ObservableArray.prototype, funcName, function(callback, thisArg) {
    var _this = this;
    var adm = this.$mobx;
    adm.atom.reportObserved();
    var dehancedValues = adm.dehanceValues(adm.values);
    return dehancedValues[funcName](function(element, index) {
      return callback.call(thisArg, element, index, _this);
    }, thisArg);
  });
});
["reduce", "reduceRight"].forEach(function(funcName) {
  addHiddenProp(ObservableArray.prototype, funcName, function() {
    var _this = this;
    var adm = this.$mobx;
    adm.atom.reportObserved();
    var callback = arguments[0];
    arguments[0] = function(accumulator, currentValue, index) {
      currentValue = adm.dehanceValue(currentValue);
      return callback(accumulator, currentValue, index, _this);
    };
    return adm.values[funcName].apply(adm.values, arguments);
  });
});
makeNonEnumerable(ObservableArray.prototype, [
  "constructor",
  "intercept",
  "observe",
  "clear",
  "concat",
  "get",
  "replace",
  "toJS",
  "toJSON",
  "peek",
  "find",
  "findIndex",
  "splice",
  "spliceWithArray",
  "push",
  "pop",
  "set",
  "shift",
  "unshift",
  "reverse",
  "sort",
  "remove",
  "move",
  "toString",
  "toLocaleString"
]);
var ENTRY_0 = createArrayEntryDescriptor(0);
function createArrayEntryDescriptor(index) {
  return {
    enumerable: false,
    configurable: false,
    get: function() {
      return this.get(index);
    },
    set: function(value) {
      this.set(index, value);
    }
  };
}
function createArrayBufferItem(index) {
  Object.defineProperty(ObservableArray.prototype, "" + index, createArrayEntryDescriptor(index));
}
function reserveArrayBuffer(max) {
  for (var index = OBSERVABLE_ARRAY_BUFFER_SIZE; index < max; index++)
    createArrayBufferItem(index);
  OBSERVABLE_ARRAY_BUFFER_SIZE = max;
}
reserveArrayBuffer(1e3);
var isObservableArrayAdministration = createInstanceofPredicate("ObservableArrayAdministration", ObservableArrayAdministration);
function isObservableArray(thing) {
  return isObject(thing) && isObservableArrayAdministration(thing.$mobx);
}
var ObservableMapMarker = {};
var ObservableMap = (
  /** @class */
  function() {
    function ObservableMap2(initialData, enhancer, name) {
      if (enhancer === void 0) {
        enhancer = deepEnhancer;
      }
      if (name === void 0) {
        name = "ObservableMap@" + getNextId();
      }
      this.enhancer = enhancer;
      this.name = name;
      this.$mobx = ObservableMapMarker;
      this._keysAtom = createAtom(this.name + ".keys()");
      if (typeof Map !== "function") {
        throw new Error("mobx.map requires Map polyfill for the current browser. Check babel-polyfill or core-js/es6/map.js");
      }
      this._data = /* @__PURE__ */ new Map();
      this._hasMap = /* @__PURE__ */ new Map();
      this.merge(initialData);
    }
    ObservableMap2.prototype._has = function(key) {
      return this._data.has(key);
    };
    ObservableMap2.prototype.has = function(key) {
      var _this = this;
      if (!globalState.trackingDerivation)
        return this._has(key);
      var entry = this._hasMap.get(key);
      if (!entry) {
        var newEntry = entry = new ObservableValue(this._has(key), referenceEnhancer, this.name + "." + stringifyKey(key) + "?", false);
        this._hasMap.set(key, newEntry);
        onBecomeUnobserved(newEntry, function() {
          return _this._hasMap.delete(key);
        });
      }
      return entry.get();
    };
    ObservableMap2.prototype.set = function(key, value) {
      var hasKey = this._has(key);
      if (hasInterceptors(this)) {
        var change = interceptChange(this, {
          type: hasKey ? "update" : "add",
          object: this,
          newValue: value,
          name: key
        });
        if (!change)
          return this;
        value = change.newValue;
      }
      if (hasKey) {
        this._updateValue(key, value);
      } else {
        this._addValue(key, value);
      }
      return this;
    };
    ObservableMap2.prototype.delete = function(key) {
      var _this = this;
      checkIfStateModificationsAreAllowed(this._keysAtom);
      if (hasInterceptors(this)) {
        var change = interceptChange(this, {
          type: "delete",
          object: this,
          name: key
        });
        if (!change)
          return false;
      }
      if (this._has(key)) {
        var notifySpy = isSpyEnabled();
        var notify = hasListeners(this);
        var change = notify || notifySpy ? {
          type: "delete",
          object: this,
          oldValue: this._data.get(key).value,
          name: key
        } : null;
        if (notifySpy)
          spyReportStart(__assign(__assign({}, change), { name: this.name, key }));
        transaction(function() {
          _this._keysAtom.reportChanged();
          _this._updateHasMapEntry(key, false);
          var observable2 = _this._data.get(key);
          observable2.setNewValue(void 0);
          _this._data.delete(key);
        });
        if (notify)
          notifyListeners(this, change);
        if (notifySpy)
          spyReportEnd();
        return true;
      }
      return false;
    };
    ObservableMap2.prototype._updateHasMapEntry = function(key, value) {
      var entry = this._hasMap.get(key);
      if (entry) {
        entry.setNewValue(value);
      }
    };
    ObservableMap2.prototype._updateValue = function(key, newValue) {
      var observable2 = this._data.get(key);
      newValue = observable2.prepareNewValue(newValue);
      if (newValue !== globalState.UNCHANGED) {
        var notifySpy = isSpyEnabled();
        var notify = hasListeners(this);
        var change = notify || notifySpy ? {
          type: "update",
          object: this,
          oldValue: observable2.value,
          name: key,
          newValue
        } : null;
        if (notifySpy)
          spyReportStart(__assign(__assign({}, change), { name: this.name, key }));
        observable2.setNewValue(newValue);
        if (notify)
          notifyListeners(this, change);
        if (notifySpy)
          spyReportEnd();
      }
    };
    ObservableMap2.prototype._addValue = function(key, newValue) {
      var _this = this;
      checkIfStateModificationsAreAllowed(this._keysAtom);
      transaction(function() {
        var observable2 = new ObservableValue(newValue, _this.enhancer, _this.name + "." + stringifyKey(key), false);
        _this._data.set(key, observable2);
        newValue = observable2.value;
        _this._updateHasMapEntry(key, true);
        _this._keysAtom.reportChanged();
      });
      var notifySpy = isSpyEnabled();
      var notify = hasListeners(this);
      var change = notify || notifySpy ? {
        type: "add",
        object: this,
        name: key,
        newValue
      } : null;
      if (notifySpy)
        spyReportStart(__assign(__assign({}, change), { name: this.name, key }));
      if (notify)
        notifyListeners(this, change);
      if (notifySpy)
        spyReportEnd();
    };
    ObservableMap2.prototype.get = function(key) {
      if (this.has(key))
        return this.dehanceValue(this._data.get(key).get());
      return this.dehanceValue(void 0);
    };
    ObservableMap2.prototype.dehanceValue = function(value) {
      if (this.dehancer !== void 0) {
        return this.dehancer(value);
      }
      return value;
    };
    ObservableMap2.prototype.keys = function() {
      this._keysAtom.reportObserved();
      return this._data.keys();
    };
    ObservableMap2.prototype.values = function() {
      var self2 = this;
      var keys2 = this.keys();
      return makeIterable({
        next: function() {
          var _a = keys2.next(), done = _a.done, value = _a.value;
          return {
            done,
            value: done ? void 0 : self2.get(value)
          };
        }
      });
    };
    ObservableMap2.prototype.entries = function() {
      var self2 = this;
      var keys2 = this.keys();
      return makeIterable({
        next: function() {
          var _a = keys2.next(), done = _a.done, value = _a.value;
          return {
            done,
            value: done ? void 0 : [value, self2.get(value)]
          };
        }
      });
    };
    ObservableMap2.prototype.forEach = function(callback, thisArg) {
      var _this = this;
      this._keysAtom.reportObserved();
      this._data.forEach(function(_, key) {
        return callback.call(thisArg, _this.get(key), key, _this);
      });
    };
    ObservableMap2.prototype.merge = function(other) {
      var _this = this;
      if (isObservableMap(other)) {
        other = other.toJS();
      }
      transaction(function() {
        var prev = allowStateChangesStart(true);
        try {
          if (isPlainObject(other))
            Object.keys(other).forEach(function(key) {
              return _this.set(key, other[key]);
            });
          else if (Array.isArray(other))
            other.forEach(function(_a) {
              var _b = __read(_a, 2), key = _b[0], value = _b[1];
              return _this.set(key, value);
            });
          else if (isES6Map(other)) {
            if (other.constructor !== Map)
              fail("Cannot initialize from classes that inherit from Map: " + other.constructor.name);
            else
              other.forEach(function(value, key) {
                return _this.set(key, value);
              });
          } else if (other !== null && other !== void 0)
            fail("Cannot initialize map from " + other);
        } finally {
          allowStateChangesEnd(prev);
        }
      });
      return this;
    };
    ObservableMap2.prototype.clear = function() {
      var _this = this;
      transaction(function() {
        untracked(function() {
          _this._data.forEach(function(_, key) {
            return _this.delete(key);
          });
        });
      });
    };
    ObservableMap2.prototype.replace = function(values2) {
      var _this = this;
      transaction(function() {
        var replacementMap = convertToMap(values2);
        var orderedData = /* @__PURE__ */ new Map();
        var keysReportChangedCalled = false;
        forOf(_this._data.keys(), function(key) {
          if (!replacementMap.has(key)) {
            var deleted = _this.delete(key);
            if (deleted) {
              keysReportChangedCalled = true;
            } else {
              var value = _this._data.get(key);
              orderedData.set(key, value);
            }
          }
        });
        forOf(replacementMap.entries(), function(_a) {
          var _b = __read(_a, 2), key = _b[0], value = _b[1];
          var keyExisted = _this._data.has(key);
          _this.set(key, value);
          if (_this._data.has(key)) {
            var value_1 = _this._data.get(key);
            orderedData.set(key, value_1);
            if (!keyExisted) {
              keysReportChangedCalled = true;
            }
          }
        });
        if (!keysReportChangedCalled) {
          if (_this._data.size !== orderedData.size) {
            _this._keysAtom.reportChanged();
          } else {
            var iter1 = _this._data.keys();
            var iter2 = orderedData.keys();
            var next1 = iter1.next();
            var next2 = iter2.next();
            while (!next1.done) {
              if (next1.value !== next2.value) {
                _this._keysAtom.reportChanged();
                break;
              }
              next1 = iter1.next();
              next2 = iter2.next();
            }
          }
        }
        _this._data = orderedData;
      });
      return this;
    };
    Object.defineProperty(ObservableMap2.prototype, "size", {
      get: function() {
        this._keysAtom.reportObserved();
        return this._data.size;
      },
      enumerable: true,
      configurable: true
    });
    ObservableMap2.prototype.toPOJO = function() {
      var _this = this;
      var res = {};
      this.forEach(function(_, key) {
        return res[typeof key === "symbol" ? key : stringifyKey(key)] = _this.get(key);
      });
      return res;
    };
    ObservableMap2.prototype.toJS = function() {
      return new Map(this);
    };
    ObservableMap2.prototype.toJSON = function() {
      return this.toPOJO();
    };
    ObservableMap2.prototype.toString = function() {
      var _this = this;
      return this.name + "[{ " + iteratorToArray(this.keys()).map(function(key) {
        return stringifyKey(key) + ": " + ("" + _this.get(key));
      }).join(", ") + " }]";
    };
    ObservableMap2.prototype.observe = function(listener, fireImmediately) {
      invariant(fireImmediately !== true, "`observe` doesn't support fireImmediately=true in combination with maps.");
      return registerListener(this, listener);
    };
    ObservableMap2.prototype.intercept = function(handler) {
      return registerInterceptor(this, handler);
    };
    return ObservableMap2;
  }()
);
function stringifyKey(key) {
  if (key && key.toString)
    return key.toString();
  else
    return new String(key).toString();
}
declareIterator(ObservableMap.prototype, function() {
  return this.entries();
});
addHiddenFinalProp(ObservableMap.prototype, toStringTagSymbol(), "Map");
var isObservableMap = createInstanceofPredicate("ObservableMap", ObservableMap);
var ObservableSetMarker = {};
var ObservableSet = (
  /** @class */
  function() {
    function ObservableSet2(initialData, enhancer, name) {
      if (enhancer === void 0) {
        enhancer = deepEnhancer;
      }
      if (name === void 0) {
        name = "ObservableSet@" + getNextId();
      }
      this.name = name;
      this.$mobx = ObservableSetMarker;
      this._data = /* @__PURE__ */ new Set();
      this._atom = createAtom(this.name);
      if (typeof Set !== "function") {
        throw new Error("mobx.set requires Set polyfill for the current browser. Check babel-polyfill or core-js/es6/set.js");
      }
      this.enhancer = function(newV, oldV) {
        return enhancer(newV, oldV, name);
      };
      if (initialData) {
        this.replace(initialData);
      }
    }
    ObservableSet2.prototype.dehanceValue = function(value) {
      if (this.dehancer !== void 0) {
        return this.dehancer(value);
      }
      return value;
    };
    ObservableSet2.prototype.clear = function() {
      var _this = this;
      transaction(function() {
        untracked(function() {
          _this._data.forEach(function(value) {
            _this.delete(value);
          });
        });
      });
    };
    ObservableSet2.prototype.forEach = function(callbackFn, thisArg) {
      var _this = this;
      this._atom.reportObserved();
      this._data.forEach(function(value) {
        callbackFn.call(thisArg, value, value, _this);
      });
    };
    Object.defineProperty(ObservableSet2.prototype, "size", {
      get: function() {
        this._atom.reportObserved();
        return this._data.size;
      },
      enumerable: true,
      configurable: true
    });
    ObservableSet2.prototype.add = function(value) {
      var _this = this;
      checkIfStateModificationsAreAllowed(this._atom);
      if (hasInterceptors(this)) {
        var change = interceptChange(this, {
          type: "add",
          object: this,
          newValue: value
        });
        if (!change)
          return this;
      }
      if (!this.has(value)) {
        transaction(function() {
          _this._data.add(_this.enhancer(value, void 0));
          _this._atom.reportChanged();
        });
        var notifySpy = isSpyEnabled();
        var notify = hasListeners(this);
        var change = notify || notifySpy ? {
          type: "add",
          object: this,
          newValue: value
        } : null;
        if (notifySpy && true)
          spyReportStart(change);
        if (notify)
          notifyListeners(this, change);
        if (notifySpy && true)
          spyReportEnd();
      }
      return this;
    };
    ObservableSet2.prototype.delete = function(value) {
      var _this = this;
      if (hasInterceptors(this)) {
        var change = interceptChange(this, {
          type: "delete",
          object: this,
          oldValue: value
        });
        if (!change)
          return false;
      }
      if (this.has(value)) {
        var notifySpy = isSpyEnabled();
        var notify = hasListeners(this);
        var change = notify || notifySpy ? {
          type: "delete",
          object: this,
          oldValue: value
        } : null;
        if (notifySpy && true)
          spyReportStart(__assign(__assign({}, change), { name: this.name }));
        transaction(function() {
          _this._atom.reportChanged();
          _this._data.delete(value);
        });
        if (notify)
          notifyListeners(this, change);
        if (notifySpy && true)
          spyReportEnd();
        return true;
      }
      return false;
    };
    ObservableSet2.prototype.has = function(value) {
      this._atom.reportObserved();
      return this._data.has(this.dehanceValue(value));
    };
    ObservableSet2.prototype.entries = function() {
      var nextIndex = 0;
      var keys2 = iteratorToArray(this.keys());
      var values2 = iteratorToArray(this.values());
      return makeIterable({
        next: function() {
          var index = nextIndex;
          nextIndex += 1;
          return index < values2.length ? { value: [keys2[index], values2[index]], done: false } : { done: true };
        }
      });
    };
    ObservableSet2.prototype.keys = function() {
      return this.values();
    };
    ObservableSet2.prototype.values = function() {
      this._atom.reportObserved();
      var self2 = this;
      var nextIndex = 0;
      var observableValues;
      if (this._data.values !== void 0) {
        observableValues = iteratorToArray(this._data.values());
      } else {
        observableValues = [];
        this._data.forEach(function(e) {
          return observableValues.push(e);
        });
      }
      return makeIterable({
        next: function() {
          return nextIndex < observableValues.length ? { value: self2.dehanceValue(observableValues[nextIndex++]), done: false } : { done: true };
        }
      });
    };
    ObservableSet2.prototype.replace = function(other) {
      var _this = this;
      if (isObservableSet(other)) {
        other = other.toJS();
      }
      transaction(function() {
        var prev = allowStateChangesStart(true);
        try {
          if (Array.isArray(other)) {
            _this.clear();
            other.forEach(function(value) {
              return _this.add(value);
            });
          } else if (isES6Set(other)) {
            _this.clear();
            other.forEach(function(value) {
              return _this.add(value);
            });
          } else if (other !== null && other !== void 0) {
            fail("Cannot initialize set from " + other);
          }
        } finally {
          allowStateChangesEnd(prev);
        }
      });
      return this;
    };
    ObservableSet2.prototype.observe = function(listener, fireImmediately) {
      invariant(fireImmediately !== true, "`observe` doesn't support fireImmediately=true in combination with sets.");
      return registerListener(this, listener);
    };
    ObservableSet2.prototype.intercept = function(handler) {
      return registerInterceptor(this, handler);
    };
    ObservableSet2.prototype.toJS = function() {
      return new Set(this);
    };
    ObservableSet2.prototype.toString = function() {
      return this.name + "[ " + iteratorToArray(this.keys()).join(", ") + " ]";
    };
    return ObservableSet2;
  }()
);
declareIterator(ObservableSet.prototype, function() {
  return this.values();
});
addHiddenFinalProp(ObservableSet.prototype, toStringTagSymbol(), "Set");
var isObservableSet = createInstanceofPredicate("ObservableSet", ObservableSet);
var ObservableObjectAdministration = (
  /** @class */
  function() {
    function ObservableObjectAdministration2(target, name, defaultEnhancer) {
      this.target = target;
      this.name = name;
      this.defaultEnhancer = defaultEnhancer;
      this.values = {};
    }
    ObservableObjectAdministration2.prototype.read = function(owner, key) {
      if (false) {
        this.illegalAccess(owner, key);
        if (!this.values[key])
          return void 0;
      }
      return this.values[key].get();
    };
    ObservableObjectAdministration2.prototype.write = function(owner, key, newValue) {
      var instance = this.target;
      if (false) {
        this.illegalAccess(owner, key);
      }
      var observable2 = this.values[key];
      if (observable2 instanceof ComputedValue) {
        observable2.set(newValue);
        return;
      }
      if (hasInterceptors(this)) {
        var change = interceptChange(this, {
          type: "update",
          object: instance,
          name: key,
          newValue
        });
        if (!change)
          return;
        newValue = change.newValue;
      }
      newValue = observable2.prepareNewValue(newValue);
      if (newValue !== globalState.UNCHANGED) {
        var notify = hasListeners(this);
        var notifySpy = isSpyEnabled();
        var change = notify || notifySpy ? {
          type: "update",
          object: instance,
          oldValue: observable2.value,
          name: key,
          newValue
        } : null;
        if (notifySpy)
          spyReportStart(__assign(__assign({}, change), { name: this.name, key }));
        observable2.setNewValue(newValue);
        if (notify)
          notifyListeners(this, change);
        if (notifySpy)
          spyReportEnd();
      }
    };
    ObservableObjectAdministration2.prototype.remove = function(key) {
      if (!this.values[key])
        return;
      var target = this.target;
      if (hasInterceptors(this)) {
        var change = interceptChange(this, {
          object: target,
          name: key,
          type: "remove"
        });
        if (!change)
          return;
      }
      try {
        startBatch();
        var notify = hasListeners(this);
        var notifySpy = isSpyEnabled();
        var oldValue = this.values[key].get();
        if (this.keys)
          this.keys.remove(key);
        delete this.values[key];
        delete this.target[key];
        var change = notify || notifySpy ? {
          type: "remove",
          object: target,
          oldValue,
          name: key
        } : null;
        if (notifySpy)
          spyReportStart(__assign(__assign({}, change), { name: this.name, key }));
        if (notify)
          notifyListeners(this, change);
        if (notifySpy)
          spyReportEnd();
      } finally {
        endBatch();
      }
    };
    ObservableObjectAdministration2.prototype.illegalAccess = function(owner, propName) {
      console.warn("Property '" + propName + "' of '" + owner + "' was accessed through the prototype chain. Use 'decorate' instead to declare the prop or access it statically through it's owner");
    };
    ObservableObjectAdministration2.prototype.observe = function(callback, fireImmediately) {
      invariant(fireImmediately !== true, "`observe` doesn't support the fire immediately property for observable objects.");
      return registerListener(this, callback);
    };
    ObservableObjectAdministration2.prototype.intercept = function(handler) {
      return registerInterceptor(this, handler);
    };
    ObservableObjectAdministration2.prototype.getKeys = function() {
      var _this = this;
      if (this.keys === void 0) {
        this.keys = new ObservableArray(Object.keys(this.values).filter(function(key) {
          return _this.values[key] instanceof ObservableValue;
        }), referenceEnhancer, "keys(" + this.name + ")", true);
      }
      return this.keys.slice();
    };
    return ObservableObjectAdministration2;
  }()
);
function asObservableObject(target, name, defaultEnhancer) {
  if (name === void 0) {
    name = "";
  }
  if (defaultEnhancer === void 0) {
    defaultEnhancer = deepEnhancer;
  }
  var adm = target.$mobx;
  if (adm)
    return adm;
  invariant(Object.isExtensible(target), "Cannot make the designated object observable; it is not extensible");
  if (!isPlainObject(target))
    name = (target.constructor.name || "ObservableObject") + "@" + getNextId();
  if (!name)
    name = "ObservableObject@" + getNextId();
  adm = new ObservableObjectAdministration(target, name, defaultEnhancer);
  addHiddenFinalProp(target, "$mobx", adm);
  return adm;
}
function defineObservableProperty(target, propName, newValue, enhancer) {
  var adm = asObservableObject(target);
  assertPropertyConfigurable(target, propName);
  if (hasInterceptors(adm)) {
    var change = interceptChange(adm, {
      object: target,
      name: propName,
      type: "add",
      newValue
    });
    if (!change)
      return;
    newValue = change.newValue;
  }
  var observable2 = adm.values[propName] = new ObservableValue(newValue, enhancer, adm.name + "." + propName, false);
  newValue = observable2.value;
  Object.defineProperty(target, propName, generateObservablePropConfig(propName));
  if (adm.keys)
    adm.keys.push(propName);
  notifyPropertyAddition(adm, target, propName, newValue);
}
function defineComputedProperty(target, propName, options) {
  var adm = asObservableObject(target);
  options.name = adm.name + "." + propName;
  options.context = target;
  adm.values[propName] = new ComputedValue(options);
  Object.defineProperty(target, propName, generateComputedPropConfig(propName));
}
var observablePropertyConfigs = /* @__PURE__ */ Object.create(null);
var computedPropertyConfigs = /* @__PURE__ */ Object.create(null);
function generateObservablePropConfig(propName) {
  return observablePropertyConfigs[propName] || (observablePropertyConfigs[propName] = {
    configurable: true,
    enumerable: true,
    get: function() {
      return this.$mobx.read(this, propName);
    },
    set: function(v) {
      this.$mobx.write(this, propName, v);
    }
  });
}
function getAdministrationForComputedPropOwner(owner) {
  var adm = owner.$mobx;
  if (!adm) {
    initializeInstance(owner);
    return owner.$mobx;
  }
  return adm;
}
function generateComputedPropConfig(propName) {
  return computedPropertyConfigs[propName] || (computedPropertyConfigs[propName] = {
    configurable: globalState.computedConfigurable,
    enumerable: false,
    get: function() {
      return getAdministrationForComputedPropOwner(this).read(this, propName);
    },
    set: function(v) {
      getAdministrationForComputedPropOwner(this).write(this, propName, v);
    }
  });
}
function notifyPropertyAddition(adm, object, key, newValue) {
  var notify = hasListeners(adm);
  var notifySpy = isSpyEnabled();
  var change = notify || notifySpy ? {
    type: "add",
    object,
    name: key,
    newValue
  } : null;
  if (notifySpy)
    spyReportStart(__assign(__assign({}, change), { name: adm.name, key }));
  if (notify)
    notifyListeners(adm, change);
  if (notifySpy)
    spyReportEnd();
}
var isObservableObjectAdministration = createInstanceofPredicate("ObservableObjectAdministration", ObservableObjectAdministration);
function isObservableObject(thing) {
  if (isObject(thing)) {
    initializeInstance(thing);
    return isObservableObjectAdministration(thing.$mobx);
  }
  return false;
}
function getAtom(thing, property) {
  if (typeof thing === "object" && thing !== null) {
    if (isObservableArray(thing)) {
      if (property !== void 0)
        fail("It is not possible to get index atoms from arrays");
      return thing.$mobx.atom;
    }
    if (isObservableSet(thing)) {
      return thing.$mobx;
    }
    if (isObservableMap(thing)) {
      var anyThing = thing;
      if (property === void 0)
        return anyThing._keysAtom;
      var observable2 = anyThing._data.get(property) || anyThing._hasMap.get(property);
      if (!observable2)
        fail("the entry '" + property + "' does not exist in the observable map '" + getDebugName(thing) + "'");
      return observable2;
    }
    initializeInstance(thing);
    if (property && !thing.$mobx)
      thing[property];
    if (isObservableObject(thing)) {
      if (!property)
        return fail("please specify a property");
      var observable2 = thing.$mobx.values[property];
      if (!observable2)
        fail("no observable property '" + property + "' found on the observable object '" + getDebugName(thing) + "'");
      return observable2;
    }
    if (isAtom(thing) || isComputedValue(thing) || isReaction(thing)) {
      return thing;
    }
  } else if (typeof thing === "function") {
    if (isReaction(thing.$mobx)) {
      return thing.$mobx;
    }
  }
  return fail("Cannot obtain atom from " + thing);
}
function getAdministration(thing, property) {
  if (!thing)
    fail("Expecting some object");
  if (property !== void 0)
    return getAdministration(getAtom(thing, property));
  if (isAtom(thing) || isComputedValue(thing) || isReaction(thing))
    return thing;
  if (isObservableMap(thing) || isObservableSet(thing))
    return thing;
  initializeInstance(thing);
  if (thing.$mobx)
    return thing.$mobx;
  fail("Cannot obtain administration from " + thing);
}
function getDebugName(thing, property) {
  var named;
  if (property !== void 0)
    named = getAtom(thing, property);
  else if (isObservableObject(thing) || isObservableMap(thing) || isObservableSet(thing))
    named = getAdministration(thing);
  else
    named = getAtom(thing);
  return named.name;
}
var toString = Object.prototype.toString;
function deepEqual(a, b, depth) {
  if (depth === void 0) {
    depth = -1;
  }
  return eq(a, b, depth);
}
function eq(a, b, depth, aStack, bStack) {
  if (a === b)
    return a !== 0 || 1 / a === 1 / b;
  if (a == null || b == null)
    return false;
  if (a !== a)
    return b !== b;
  var type = typeof a;
  if (type !== "function" && type !== "object" && typeof b != "object")
    return false;
  a = unwrap(a);
  b = unwrap(b);
  var className = toString.call(a);
  if (className !== toString.call(b))
    return false;
  switch (className) {
    case "[object RegExp]":
    case "[object String]":
      return "" + a === "" + b;
    case "[object Number]":
      if (+a !== +a)
        return +b !== +b;
      return +a === 0 ? 1 / +a === 1 / b : +a === +b;
    case "[object Date]":
    case "[object Boolean]":
      return +a === +b;
    case "[object Symbol]":
      return (
        // eslint-disable-next-line
        typeof Symbol !== "undefined" && Symbol.valueOf.call(a) === Symbol.valueOf.call(b)
      );
  }
  var areArrays = className === "[object Array]";
  if (!areArrays) {
    if (typeof a != "object" || typeof b != "object")
      return false;
    var aCtor = a.constructor, bCtor = b.constructor;
    if (aCtor !== bCtor && !(typeof aCtor === "function" && aCtor instanceof aCtor && typeof bCtor === "function" && bCtor instanceof bCtor) && ("constructor" in a && "constructor" in b)) {
      return false;
    }
  }
  if (depth === 0) {
    return false;
  } else if (depth < 0) {
    depth = -1;
  }
  aStack = aStack || [];
  bStack = bStack || [];
  var length = aStack.length;
  while (length--) {
    if (aStack[length] === a)
      return bStack[length] === b;
  }
  aStack.push(a);
  bStack.push(b);
  if (areArrays) {
    length = a.length;
    if (length !== b.length)
      return false;
    while (length--) {
      if (!eq(a[length], b[length], depth - 1, aStack, bStack))
        return false;
    }
  } else {
    var keys2 = Object.keys(a);
    var key = void 0;
    length = keys2.length;
    if (Object.keys(b).length !== length)
      return false;
    while (length--) {
      key = keys2[length];
      if (!(has$1(b, key) && eq(a[key], b[key], depth - 1, aStack, bStack)))
        return false;
    }
  }
  aStack.pop();
  bStack.pop();
  return true;
}
function unwrap(a) {
  if (isObservableArray(a))
    return a.peek();
  if (isES6Map(a) || isObservableMap(a))
    return iteratorToArray(a.entries());
  if (isES6Set(a) || isObservableSet(a))
    return iteratorToArray(a.entries());
  return a;
}
function has$1(a, key) {
  return Object.prototype.hasOwnProperty.call(a, key);
}
(function() {
  function testCodeMinification() {
  }
  if (testCodeMinification.name !== "testCodeMinification" && true && typeof process !== "undefined" && process.env.IGNORE_MOBX_MINIFY_WARNING !== "true") {
    var varName = ["process", "env", "NODE_ENV"].join(".");
    console.warn("[mobx] you are running a minified build, but '" + varName + "' was not set to 'production' in your bundler. This results in an unnecessarily large and slow bundle");
  }
})();
var $mobx = "$mobx";
if (typeof __MOBX_DEVTOOLS_GLOBAL_HOOK__ === "object") {
  __MOBX_DEVTOOLS_GLOBAL_HOOK__.injectMobx({
    spy,
    extras: {
      getDebugName
    },
    $mobx
  });
}
if (typeof module !== "undefined" && typeof module.exports !== "undefined") {
  warnedAboutDefaultExport_1 = false;
  Object.defineProperty(module.exports, "default", {
    enumerable: false,
    get: function() {
      if (!warnedAboutDefaultExport_1) {
        warnedAboutDefaultExport_1 = true;
        console.warn(`The MobX package does not have a default export. Use 'import { thing } from "mobx"' (recommended) or 'import * as mobx from "mobx"' instead."`);
      }
      return void 0;
    }
  });
  [
    "extras",
    "Atom",
    "BaseAtom",
    "asFlat",
    "asMap",
    "asReference",
    "asStructure",
    "autorunAsync",
    "createTranformer",
    "expr",
    "isModifierDescriptor",
    "isStrictModeEnabled",
    "map",
    "useStrict",
    "whyRun"
  ].forEach(function(prop) {
    Object.defineProperty(module.exports, prop, {
      enumerable: false,
      get: function() {
        fail("'" + prop + "' is no longer part of the public MobX api. Please consult the changelog to find out where this functionality went");
      },
      set: function() {
      }
    });
  });
}
var warnedAboutDefaultExport_1;

export {
  createAtom,
  observable,
  computed,
  untracked,
  allowStateReadsStart,
  allowStateReadsEnd,
  allowStateChanges,
  allowStateChangesInsideComputed,
  Reaction,
  spy,
  action,
  reaction,
  configure,
  getDependencyTree,
  interceptReads,
  intercept,
  isComputedProp,
  values,
  entries,
  set,
  observe,
  isObservableArray,
  ObservableMap,
  isObservableMap,
  isObservableObject,
  getAtom,
  getAdministration,
  $mobx
};
/*! Bundled license information:

mobx/lib/mobx.module.js:
  (*! *****************************************************************************
  Copyright (c) Microsoft Corporation. All rights reserved.
  Licensed under the Apache License, Version 2.0 (the "License"); you may not use
  this file except in compliance with the License. You may obtain a copy of the
  License at http://www.apache.org/licenses/LICENSE-2.0
  
  THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
  WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
  MERCHANTABLITY OR NON-INFRINGEMENT.
  
  See the Apache Version 2.0 License for specific language governing permissions
  and limitations under the License.
  ***************************************************************************** *)
*/
//# sourceMappingURL=chunk-ECE3U5IW.js.map
