Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var _vue=require("vue"),_comp=require("../../../ui/src/comp"),_ui=require("../../../ui"),_xeUtils=_interopRequireDefault(require("xe-utils")),_utils=require("../../../ui/src/utils"),_log=require("../../../ui/src/log");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let{getI18n,getIcon}=_ui.VxeUI;var _default=exports.default=(0,_comp.defineVxeComponent)({name:"VxeTableExportPanel",props:{defaultOptions:Object,storeData:Object},setup(I){let f=_ui.VxeUI.getComponent("VxeModal"),T=_ui.VxeUI.getComponent("VxeButton"),k=_ui.VxeUI.getComponent("VxeSelect"),O=_ui.VxeUI.getComponent("VxeInput"),E=_ui.VxeUI.getComponent("VxeCheckbox"),N=(0,_vue.inject)("$xeTable",{}),{computeExportOpts:l,computePrintOpts:o}=N.getComputeMaps(),U=(0,_vue.reactive)({isAll:!1,isIndeterminate:!1,loading:!1}),y=(0,_vue.ref)(),D=(0,_vue.ref)(),A=(0,_vue.ref)(),M=(0,_vue.computed)(()=>{var e=I.storeData;return e.columns.every(e=>e.checked)}),H=(0,_vue.computed)(()=>{var e=I.defaultOptions;return-1<["html","xml","xlsx","pdf"].indexOf(e.type)}),L=(0,_vue.computed)(()=>{var{storeData:e,defaultOptions:t}=I;return!t.original&&"current"===t.mode&&(e.isPrint||-1<["html","xlsx"].indexOf(t.type))}),q=(0,_vue.computed)(()=>{var e=I.defaultOptions;return!e.original&&-1<["xlsx"].indexOf(e.type)}),B=t=>{var e=I.storeData,e=_xeUtils.default.findTree(e.columns,e=>e===t);e&&e.parent&&(e=e.parent,e.children)&&e.children.length&&(e.checked=e.children.every(e=>e.checked),e.halfChecked=!e.checked&&e.children.some(e=>e.checked||e.halfChecked),B(e))},S=()=>{var e=I.storeData,e=e.columns;U.isAll=e.every(e=>e.disabled||e.checked),U.isIndeterminate=!U.isAll&&e.some(e=>!e.disabled&&(e.checked||e.halfChecked))},w=()=>{var e=I.storeData;let t=!U.isAll;_xeUtils.default.eachTree(e.columns,e=>{e.disabled||(e.checked=t,e.halfChecked=!1)}),U.isAll=t,S()},K=()=>{(0,_vue.nextTick)(()=>{var e=D.value,t=A.value,l=y.value,e=e||t||l;e&&e.focus()}),S()},a=()=>{var{storeData:e,defaultOptions:t}=I,{hasMerge:e,columns:l}=e,o=M.value,a=L.value,l=_xeUtils.default.searchTree(l,e=>e.checked,{children:"children",mapChildren:"childNodes",original:!0});return Object.assign({},t,{columns:l,isMerge:!!(e&&a&&o)&&t.isMerge})},t=()=>{var e=I.storeData,t=o.value;e.visible=!1,N.print(Object.assign({},t,a()))},r=()=>{let e=I.storeData;var t=l.value;U.loading=!0,N.exportData(Object.assign({},t,a())).then(()=>{U.loading=!1,e.visible=!1}).catch(()=>{U.loading=!1})},P=()=>{var e=I.storeData;e.visible=!1},X=()=>{var e=I.storeData;(e.isPrint?t:r)()};return(0,_vue.nextTick)(()=>{f||(0,_log.errLog)("vxe.error.reqComp",["vxe-modal"]),T||(0,_log.errLog)("vxe.error.reqComp",["vxe-button"]),k||(0,_log.errLog)("vxe.error.reqComp",["vxe-select"]),O||(0,_log.errLog)("vxe.error.reqComp",["vxe-input"]),E||(0,_log.errLog)("vxe.error.reqComp",["vxe-checkbox"])}),()=>{let l=N.xeGrid,{defaultOptions:o,storeData:a}=I,{isAll:r,isIndeterminate:n}=U,{hasTree:i,hasMerge:u,isPrint:p,hasColgroup:d,columns:v}=a,s=o.isHeader,x=[],c=M.value,m=H.value,_=L.value,h=q.value;var e=o.slots||{};let g=e.top,C=e.bottom,b=e.default,t=e.footer,V=e.parameter;return _xeUtils.default.eachTree(v,e=>{var t=(0,_utils.formatText)(e.getTitle(),1),l=e.children&&e.children.length,o=e.checked,a=e.halfChecked,r="html"===e.type;x.push((0,_vue.h)("li",{key:e.id,class:["vxe-table-export--panel-column-option","level--"+e.level,{"is--group":l,"is--checked":o,"is--indeterminate":a,"is--disabled":e.disabled}],title:r?"":t,onClick:()=>{e.disabled||(e=>{let t=!e.checked;_xeUtils.default.eachTree([e],e=>{e.checked=t,e.halfChecked=!1}),B(e),S()})(e)}},[(0,_vue.h)("span",{class:["vxe-checkbox--icon",a?getIcon().TABLE_CHECKBOX_INDETERMINATE:o?getIcon().TABLE_CHECKBOX_CHECKED:getIcon().TABLE_CHECKBOX_UNCHECKED]}),r?(0,_vue.h)("span",{key:"1",class:"vxe-checkbox--label",innerHTML:t}):(0,_vue.h)("span",{key:"0",class:"vxe-checkbox--label"},t)]))}),f?(0,_vue.h)(f,{id:"VXE_EXPORT_MODAL",modelValue:a.visible,title:getI18n(p?"vxe.export.printTitle":"vxe.export.expTitle"),className:"vxe-table-export-popup-wrapper",width:660,minWidth:500,minHeight:400,mask:!0,lockView:!0,showFooter:!0,escClosable:!0,maskClosable:!0,showMaximize:!0,resize:!0,loading:U.loading,"onUpdate:modelValue"(e){a.visible=e},onShow:K},{default:()=>{var e={$table:N,$grid:l,options:o,columns:v,params:o.params},t="empty"===o.mode;return(0,_vue.h)("div",{class:"vxe-table-export--panel"},[g?(0,_vue.h)("div",{class:"vxe-table-export--panel-top"},N.callSlot(g,e)):(0,_vue.createCommentVNode)(),(0,_vue.h)("div",{class:"vxe-table-export--panel-body"},b?N.callSlot(b,e):[(0,_vue.h)("table",{class:"vxe-table-export--panel-table",cellspacing:0,cellpadding:0,border:0},[(0,_vue.h)("tbody",[[p?(0,_vue.createCommentVNode)():(0,_vue.h)("tr",[(0,_vue.h)("td",getI18n("vxe.export.expName")),(0,_vue.h)("td",[O?(0,_vue.h)(O,{ref:D,modelValue:o.filename,type:"text",clearable:!0,placeholder:getI18n("vxe.export.expNamePlaceholder"),"onUpdate:modelValue"(e){o.filename=e}}):(0,_vue.createCommentVNode)()])]),p?(0,_vue.createCommentVNode)():(0,_vue.h)("tr",[(0,_vue.h)("td",getI18n("vxe.export.expType")),(0,_vue.h)("td",[k?(0,_vue.h)(k,{modelValue:o.type,options:a.typeList,"onUpdate:modelValue"(e){o.type=e}}):(0,_vue.createCommentVNode)()])]),p||m?(0,_vue.h)("tr",[(0,_vue.h)("td",getI18n("vxe.export.expSheetName")),(0,_vue.h)("td",[O?(0,_vue.h)(O,{ref:A,modelValue:o.sheetName,type:"text",clearable:!0,placeholder:getI18n("vxe.export.expSheetNamePlaceholder"),"onUpdate:modelValue"(e){o.sheetName=e}}):(0,_vue.createCommentVNode)()])]):(0,_vue.createCommentVNode)(),(0,_vue.h)("tr",[(0,_vue.h)("td",getI18n("vxe.export.expMode")),(0,_vue.h)("td",[k?(0,_vue.h)(k,{modelValue:o.mode,options:a.modeList.map(e=>({value:e.value,label:getI18n(e.label)})),"onUpdate:modelValue"(e){o.mode=e}}):(0,_vue.createCommentVNode)()])]),(0,_vue.h)("tr",[(0,_vue.h)("td",[getI18n("vxe.export.expColumn")]),(0,_vue.h)("td",[(0,_vue.h)("div",{class:"vxe-table-export--panel-column"},[(0,_vue.h)("ul",{class:"vxe-table-export--panel-column-header"},[(0,_vue.h)("li",{class:["vxe-table-export--panel-column-option",{"is--checked":r,"is--indeterminate":n}],title:getI18n("vxe.table.allTitle"),onClick:w},[(0,_vue.h)("span",{class:["vxe-checkbox--icon",n?getIcon().TABLE_CHECKBOX_INDETERMINATE:r?getIcon().TABLE_CHECKBOX_CHECKED:getIcon().TABLE_CHECKBOX_UNCHECKED]}),(0,_vue.h)("span",{class:"vxe-checkbox--label"},getI18n("vxe.export.expCurrentColumn"))])]),(0,_vue.h)("ul",{class:"vxe-table-export--panel-column-body"},x)])])]),(0,_vue.h)("tr",[(0,_vue.h)("td",getI18n("vxe.export.expOpts")),V?(0,_vue.h)("td",[(0,_vue.h)("div",{class:"vxe-table-export--panel-option-row"},N.callSlot(V,e))]):(0,_vue.h)("td",[(0,_vue.h)("div",{class:"vxe-table-export--panel-option-row"},[E?(0,_vue.h)(E,{modelValue:t||s,disabled:t,title:getI18n("vxe.export.expHeaderTitle"),content:getI18n("vxe.export.expOptHeader"),"onUpdate:modelValue"(e){o.isHeader=e}}):(0,_vue.createCommentVNode)(),E?(0,_vue.h)(E,{modelValue:!!s&&o.isTitle,disabled:!s,title:getI18n("vxe.export.expTitleTitle"),content:getI18n("vxe.export.expOptTitle"),"onUpdate:modelValue"(e){o.isTitle=e}}):(0,_vue.createCommentVNode)(),E?(0,_vue.h)(E,{modelValue:!!(s&&d&&_)&&o.isColgroup,title:getI18n("vxe.export.expColgroupTitle"),disabled:!s||!d||!_,content:getI18n("vxe.export.expOptColgroup"),"onUpdate:modelValue"(e){o.isColgroup=e}}):(0,_vue.createCommentVNode)()]),(0,_vue.h)("div",{class:"vxe-table-export--panel-option-row"},[E?(0,_vue.h)(E,{modelValue:!t&&o.original,disabled:t,title:getI18n("vxe.export.expOriginalTitle"),content:getI18n("vxe.export.expOptOriginal"),"onUpdate:modelValue"(e){o.original=e}}):(0,_vue.createCommentVNode)(),E?(0,_vue.h)(E,{modelValue:!!(u&&_&&c)&&o.isMerge,title:getI18n("vxe.export.expMergeTitle"),disabled:t||!u||!_||!c,content:getI18n("vxe.export.expOptMerge"),"onUpdate:modelValue"(e){o.isMerge=e}}):(0,_vue.createCommentVNode)(),p||!E?(0,_vue.createCommentVNode)():(0,_vue.h)(E,{modelValue:!!h&&o.useStyle,disabled:!h,title:getI18n("vxe.export.expUseStyleTitle"),content:getI18n("vxe.export.expOptUseStyle"),"onUpdate:modelValue"(e){o.useStyle=e}}),E?(0,_vue.h)(E,{modelValue:!!i&&o.isAllExpand,disabled:t||!i,title:getI18n("vxe.export.expAllExpandTitle"),content:getI18n("vxe.export.expOptAllExpand"),"onUpdate:modelValue"(e){o.isAllExpand=e}}):(0,_vue.createCommentVNode)()]),(0,_vue.h)("div",{class:"vxe-table-export--panel-option-row"},[E?(0,_vue.h)(E,{modelValue:o.isFooter,disabled:!a.hasFooter,title:getI18n("vxe.export.expFooterTitle"),content:getI18n("vxe.export.expOptFooter"),"onUpdate:modelValue"(e){o.isFooter=e}}):(0,_vue.createCommentVNode)()])])])]])])]),C?(0,_vue.h)("div",{class:"vxe-table-export--panel-bottom"},N.callSlot(C,e)):(0,_vue.createCommentVNode)()])},footer(){var e={$table:N,$grid:l,options:o,columns:v,params:o.params};return(0,_vue.h)("div",{class:"vxe-table-export--panel-footer"},t?N.callSlot(t,e):[(0,_vue.h)("div",{class:"vxe-table-export--panel-btns"},[T?(0,_vue.h)(T,{content:getI18n("vxe.export.expCancel"),onClick:P}):(0,_vue.createCommentVNode)(),T?(0,_vue.h)(T,{ref:y,status:"primary",content:getI18n(p?"vxe.export.expPrint":"vxe.export.expConfirm"),onClick:X}):(0,_vue.createCommentVNode)()])])}}):(0,_vue.createCommentVNode)()}}});