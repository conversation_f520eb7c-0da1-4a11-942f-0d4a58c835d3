{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/elixir/elixir.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    comments: {\n        lineComment: '#'\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')']\n    ],\n    surroundingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: \"'\", close: \"'\" },\n        { open: '\"', close: '\"' }\n    ],\n    autoClosingPairs: [\n        { open: \"'\", close: \"'\", notIn: ['string', 'comment'] },\n        { open: '\"', close: '\"', notIn: ['comment'] },\n        { open: '\"\"\"', close: '\"\"\"' },\n        { open: '`', close: '`', notIn: ['string', 'comment'] },\n        { open: '(', close: ')' },\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '<<', close: '>>' }\n    ],\n    indentationRules: {\n        increaseIndentPattern: /^\\s*(after|else|catch|rescue|fn|[^#]*(do|<\\-|\\->|\\{|\\[|\\=))\\s*$/,\n        decreaseIndentPattern: /^\\s*((\\}|\\])\\s*$|(after|else|catch|rescue|end)\\b)/\n    }\n};\n/**\n * A Monarch lexer for the Elixir language.\n *\n * References:\n *\n * * Monarch documentation - https://microsoft.github.io/monaco-editor/monarch.html\n * * Elixir lexer - https://github.com/elixir-makeup/makeup_elixir/blob/master/lib/makeup/lexers/elixir_lexer.ex\n * * TextMate lexer (elixir-tmbundle) - https://github.com/elixir-editors/elixir-tmbundle/blob/master/Syntaxes/Elixir.tmLanguage\n * * TextMate lexer (vscode-elixir-ls) - https://github.com/elixir-lsp/vscode-elixir-ls/blob/master/syntaxes/elixir.json\n */\nexport var language = {\n    defaultToken: 'source',\n    tokenPostfix: '.elixir',\n    brackets: [\n        { open: '[', close: ']', token: 'delimiter.square' },\n        { open: '(', close: ')', token: 'delimiter.parenthesis' },\n        { open: '{', close: '}', token: 'delimiter.curly' },\n        { open: '<<', close: '>>', token: 'delimiter.angle.special' }\n    ],\n    // Below are lists/regexps to which we reference later.\n    declarationKeywords: [\n        'def',\n        'defp',\n        'defn',\n        'defnp',\n        'defguard',\n        'defguardp',\n        'defmacro',\n        'defmacrop',\n        'defdelegate',\n        'defcallback',\n        'defmacrocallback',\n        'defmodule',\n        'defprotocol',\n        'defexception',\n        'defimpl',\n        'defstruct'\n    ],\n    operatorKeywords: ['and', 'in', 'not', 'or', 'when'],\n    namespaceKeywords: ['alias', 'import', 'require', 'use'],\n    otherKeywords: [\n        'after',\n        'case',\n        'catch',\n        'cond',\n        'do',\n        'else',\n        'end',\n        'fn',\n        'for',\n        'if',\n        'quote',\n        'raise',\n        'receive',\n        'rescue',\n        'super',\n        'throw',\n        'try',\n        'unless',\n        'unquote_splicing',\n        'unquote',\n        'with'\n    ],\n    constants: ['true', 'false', 'nil'],\n    nameBuiltin: ['__MODULE__', '__DIR__', '__ENV__', '__CALLER__', '__STACKTRACE__'],\n    // Matches any of the operator names:\n    // <<< >>> ||| &&& ^^^ ~~~ === !== ~>> <~> |~> <|> == != <= >= && || \\\\ <> ++ -- |> =~ -> <- ~> <~ :: .. = < > + - * / | . ^ & !\n    operator: /-[->]?|!={0,2}|\\*|\\/|\\\\\\\\|&{1,3}|\\.\\.?|\\^(?:\\^\\^)?|\\+\\+?|<(?:-|<<|=|>|\\|>|~>?)?|=~|={1,3}|>(?:=|>>)?|\\|~>|\\|>|\\|{1,3}|~>>?|~~~|::/,\n    // See https://hexdocs.pm/elixir/syntax-reference.html#variables\n    variableName: /[a-z_][a-zA-Z0-9_]*[?!]?/,\n    // See https://hexdocs.pm/elixir/syntax-reference.html#atoms\n    atomName: /[a-zA-Z_][a-zA-Z0-9_@]*[?!]?|@specialAtomName|@operator/,\n    specialAtomName: /\\.\\.\\.|<<>>|%\\{\\}|%|\\{\\}/,\n    aliasPart: /[A-Z][a-zA-Z0-9_]*/,\n    moduleName: /@aliasPart(?:\\.@aliasPart)*/,\n    // Sigil pairs are: \"\"\" \"\"\", ''' ''', \" \", ' ', / /, | |, < >, { }, [ ], ( )\n    sigilSymmetricDelimiter: /\"\"\"|'''|\"|'|\\/|\\|/,\n    sigilStartDelimiter: /@sigilSymmetricDelimiter|<|\\{|\\[|\\(/,\n    sigilEndDelimiter: /@sigilSymmetricDelimiter|>|\\}|\\]|\\)/,\n    decimal: /\\d(?:_?\\d)*/,\n    hex: /[0-9a-fA-F](_?[0-9a-fA-F])*/,\n    octal: /[0-7](_?[0-7])*/,\n    binary: /[01](_?[01])*/,\n    // See https://hexdocs.pm/elixir/master/String.html#module-escape-characters\n    escape: /\\\\u[0-9a-fA-F]{4}|\\\\x[0-9a-fA-F]{2}|\\\\./,\n    // The keys below correspond to tokenizer states.\n    // We start from the root state and match against its rules\n    // until we explicitly transition into another state.\n    // The `include` simply brings in all operations from the given state\n    // and is useful for improving readability.\n    tokenizer: {\n        root: [\n            { include: '@whitespace' },\n            { include: '@comments' },\n            // Keywords start as either an identifier or a string,\n            // but end with a : so it's important to match this first.\n            { include: '@keywordsShorthand' },\n            { include: '@numbers' },\n            { include: '@identifiers' },\n            { include: '@strings' },\n            { include: '@atoms' },\n            { include: '@sigils' },\n            { include: '@attributes' },\n            { include: '@symbols' }\n        ],\n        // Whitespace\n        whitespace: [[/\\s+/, 'white']],\n        // Comments\n        comments: [[/(#)(.*)/, ['comment.punctuation', 'comment']]],\n        // Keyword list shorthand\n        keywordsShorthand: [\n            [/(@atomName)(:)/, ['constant', 'constant.punctuation']],\n            // Use positive look-ahead to ensure the string is followed by :\n            // and should be considered a keyword.\n            [\n                /\"(?=([^\"]|#\\{.*?\\}|\\\\\")*\":)/,\n                { token: 'constant.delimiter', next: '@doubleQuotedStringKeyword' }\n            ],\n            [\n                /'(?=([^']|#\\{.*?\\}|\\\\')*':)/,\n                { token: 'constant.delimiter', next: '@singleQuotedStringKeyword' }\n            ]\n        ],\n        doubleQuotedStringKeyword: [\n            [/\":/, { token: 'constant.delimiter', next: '@pop' }],\n            { include: '@stringConstantContentInterpol' }\n        ],\n        singleQuotedStringKeyword: [\n            [/':/, { token: 'constant.delimiter', next: '@pop' }],\n            { include: '@stringConstantContentInterpol' }\n        ],\n        // Numbers\n        numbers: [\n            [/0b@binary/, 'number.binary'],\n            [/0o@octal/, 'number.octal'],\n            [/0x@hex/, 'number.hex'],\n            [/@decimal\\.@decimal([eE]-?@decimal)?/, 'number.float'],\n            [/@decimal/, 'number']\n        ],\n        // Identifiers\n        identifiers: [\n            // Tokenize identifier name in function-like definitions.\n            // Note: given `def a + b, do: nil`, `a` is not a function name,\n            // so we use negative look-ahead to ensure there's no operator.\n            [\n                /\\b(defp?|defnp?|defmacrop?|defguardp?|defdelegate)(\\s+)(@variableName)(?!\\s+@operator)/,\n                [\n                    'keyword.declaration',\n                    'white',\n                    {\n                        cases: {\n                            unquote: 'keyword',\n                            '@default': 'function'\n                        }\n                    }\n                ]\n            ],\n            // Tokenize function calls\n            [\n                // In-scope call - an identifier followed by ( or .(\n                /(@variableName)(?=\\s*\\.?\\s*\\()/,\n                {\n                    cases: {\n                        // Tokenize as keyword in cases like `if(..., do: ..., else: ...)`\n                        '@declarationKeywords': 'keyword.declaration',\n                        '@namespaceKeywords': 'keyword',\n                        '@otherKeywords': 'keyword',\n                        '@default': 'function.call'\n                    }\n                }\n            ],\n            [\n                // Referencing function in a module\n                /(@moduleName)(\\s*)(\\.)(\\s*)(@variableName)/,\n                ['type.identifier', 'white', 'operator', 'white', 'function.call']\n            ],\n            [\n                // Referencing function in an Erlang module\n                /(:)(@atomName)(\\s*)(\\.)(\\s*)(@variableName)/,\n                ['constant.punctuation', 'constant', 'white', 'operator', 'white', 'function.call']\n            ],\n            [\n                // Piping into a function (tokenized separately as it may not have parentheses)\n                /(\\|>)(\\s*)(@variableName)/,\n                [\n                    'operator',\n                    'white',\n                    {\n                        cases: {\n                            '@otherKeywords': 'keyword',\n                            '@default': 'function.call'\n                        }\n                    }\n                ]\n            ],\n            [\n                // Function reference passed to another function\n                /(&)(\\s*)(@variableName)/,\n                ['operator', 'white', 'function.call']\n            ],\n            // Language keywords, builtins, constants and variables\n            [\n                /@variableName/,\n                {\n                    cases: {\n                        '@declarationKeywords': 'keyword.declaration',\n                        '@operatorKeywords': 'keyword.operator',\n                        '@namespaceKeywords': 'keyword',\n                        '@otherKeywords': 'keyword',\n                        '@constants': 'constant.language',\n                        '@nameBuiltin': 'variable.language',\n                        '_.*': 'comment.unused',\n                        '@default': 'identifier'\n                    }\n                }\n            ],\n            // Module names\n            [/@moduleName/, 'type.identifier']\n        ],\n        // Strings\n        strings: [\n            [/\"\"\"/, { token: 'string.delimiter', next: '@doubleQuotedHeredoc' }],\n            [/'''/, { token: 'string.delimiter', next: '@singleQuotedHeredoc' }],\n            [/\"/, { token: 'string.delimiter', next: '@doubleQuotedString' }],\n            [/'/, { token: 'string.delimiter', next: '@singleQuotedString' }]\n        ],\n        doubleQuotedHeredoc: [\n            [/\"\"\"/, { token: 'string.delimiter', next: '@pop' }],\n            { include: '@stringContentInterpol' }\n        ],\n        singleQuotedHeredoc: [\n            [/'''/, { token: 'string.delimiter', next: '@pop' }],\n            { include: '@stringContentInterpol' }\n        ],\n        doubleQuotedString: [\n            [/\"/, { token: 'string.delimiter', next: '@pop' }],\n            { include: '@stringContentInterpol' }\n        ],\n        singleQuotedString: [\n            [/'/, { token: 'string.delimiter', next: '@pop' }],\n            { include: '@stringContentInterpol' }\n        ],\n        // Atoms\n        atoms: [\n            [/(:)(@atomName)/, ['constant.punctuation', 'constant']],\n            [/:\"/, { token: 'constant.delimiter', next: '@doubleQuotedStringAtom' }],\n            [/:'/, { token: 'constant.delimiter', next: '@singleQuotedStringAtom' }]\n        ],\n        doubleQuotedStringAtom: [\n            [/\"/, { token: 'constant.delimiter', next: '@pop' }],\n            { include: '@stringConstantContentInterpol' }\n        ],\n        singleQuotedStringAtom: [\n            [/'/, { token: 'constant.delimiter', next: '@pop' }],\n            { include: '@stringConstantContentInterpol' }\n        ],\n        // Sigils\n        // See https://elixir-lang.org/getting-started/sigils.html\n        // Sigils allow for typing values using their textual representation.\n        // All sigils start with ~ followed by a letter indicating sigil type\n        // and then a delimiter pair enclosing the textual representation.\n        // Optional modifiers are allowed after the closing delimiter.\n        // For instance a regular expressions can be written as:\n        // ~r/foo|bar/ ~r{foo|bar} ~r/foo|bar/g\n        //\n        // In general lowercase sigils allow for interpolation\n        // and escaped characters, whereas uppercase sigils don't\n        //\n        // During tokenization we want to distinguish some\n        // specific sigil types, namely string and regexp,\n        // so that they cen be themed separately.\n        //\n        // To reasonably handle all those combinations we leverage\n        // dot-separated states, so if we transition to @sigilStart.interpol.s.{.}\n        // then \"sigilStart.interpol.s\" state will match and also all\n        // the individual dot-separated parameters can be accessed.\n        sigils: [\n            [/~[a-z]@sigilStartDelimiter/, { token: '@rematch', next: '@sigil.interpol' }],\n            [/~[A-Z]@sigilStartDelimiter/, { token: '@rematch', next: '@sigil.noInterpol' }]\n        ],\n        sigil: [\n            [/~([a-zA-Z])\\{/, { token: '@rematch', switchTo: '@sigilStart.$S2.$1.{.}' }],\n            [/~([a-zA-Z])\\[/, { token: '@rematch', switchTo: '@sigilStart.$S2.$1.[.]' }],\n            [/~([a-zA-Z])\\(/, { token: '@rematch', switchTo: '@sigilStart.$S2.$1.(.)' }],\n            [/~([a-zA-Z])\\</, { token: '@rematch', switchTo: '@sigilStart.$S2.$1.<.>' }],\n            [\n                /~([a-zA-Z])(@sigilSymmetricDelimiter)/,\n                { token: '@rematch', switchTo: '@sigilStart.$S2.$1.$2.$2' }\n            ]\n        ],\n        // The definitions below expect states to be of the form:\n        //\n        // sigilStart.<interpol-or-noInterpol>.<sigil-letter>.<start-delimiter>.<end-delimiter>\n        // sigilContinue.<interpol-or-noInterpol>.<sigil-letter>.<start-delimiter>.<end-delimiter>\n        //\n        // The sigilStart state is used only to properly classify the token (as string/regex/sigil)\n        // and immediately switches to the sigilContinue sate, which handles the actual content\n        // and waits for the corresponding end delimiter.\n        'sigilStart.interpol.s': [\n            [\n                /~s@sigilStartDelimiter/,\n                {\n                    token: 'string.delimiter',\n                    switchTo: '@sigilContinue.$S2.$S3.$S4.$S5'\n                }\n            ]\n        ],\n        'sigilContinue.interpol.s': [\n            [\n                /(@sigilEndDelimiter)[a-zA-Z]*/,\n                {\n                    cases: {\n                        '$1==$S5': { token: 'string.delimiter', next: '@pop' },\n                        '@default': 'string'\n                    }\n                }\n            ],\n            { include: '@stringContentInterpol' }\n        ],\n        'sigilStart.noInterpol.S': [\n            [\n                /~S@sigilStartDelimiter/,\n                {\n                    token: 'string.delimiter',\n                    switchTo: '@sigilContinue.$S2.$S3.$S4.$S5'\n                }\n            ]\n        ],\n        'sigilContinue.noInterpol.S': [\n            // Ignore escaped sigil end\n            [/(^|[^\\\\])\\\\@sigilEndDelimiter/, 'string'],\n            [\n                /(@sigilEndDelimiter)[a-zA-Z]*/,\n                {\n                    cases: {\n                        '$1==$S5': { token: 'string.delimiter', next: '@pop' },\n                        '@default': 'string'\n                    }\n                }\n            ],\n            { include: '@stringContent' }\n        ],\n        'sigilStart.interpol.r': [\n            [\n                /~r@sigilStartDelimiter/,\n                {\n                    token: 'regexp.delimiter',\n                    switchTo: '@sigilContinue.$S2.$S3.$S4.$S5'\n                }\n            ]\n        ],\n        'sigilContinue.interpol.r': [\n            [\n                /(@sigilEndDelimiter)[a-zA-Z]*/,\n                {\n                    cases: {\n                        '$1==$S5': { token: 'regexp.delimiter', next: '@pop' },\n                        '@default': 'regexp'\n                    }\n                }\n            ],\n            { include: '@regexpContentInterpol' }\n        ],\n        'sigilStart.noInterpol.R': [\n            [\n                /~R@sigilStartDelimiter/,\n                {\n                    token: 'regexp.delimiter',\n                    switchTo: '@sigilContinue.$S2.$S3.$S4.$S5'\n                }\n            ]\n        ],\n        'sigilContinue.noInterpol.R': [\n            // Ignore escaped sigil end\n            [/(^|[^\\\\])\\\\@sigilEndDelimiter/, 'regexp'],\n            [\n                /(@sigilEndDelimiter)[a-zA-Z]*/,\n                {\n                    cases: {\n                        '$1==$S5': { token: 'regexp.delimiter', next: '@pop' },\n                        '@default': 'regexp'\n                    }\n                }\n            ],\n            { include: '@regexpContent' }\n        ],\n        // Fallback to the generic sigil by default\n        'sigilStart.interpol': [\n            [\n                /~([a-zA-Z])@sigilStartDelimiter/,\n                {\n                    token: 'sigil.delimiter',\n                    switchTo: '@sigilContinue.$S2.$S3.$S4.$S5'\n                }\n            ]\n        ],\n        'sigilContinue.interpol': [\n            [\n                /(@sigilEndDelimiter)[a-zA-Z]*/,\n                {\n                    cases: {\n                        '$1==$S5': { token: 'sigil.delimiter', next: '@pop' },\n                        '@default': 'sigil'\n                    }\n                }\n            ],\n            { include: '@sigilContentInterpol' }\n        ],\n        'sigilStart.noInterpol': [\n            [\n                /~([a-zA-Z])@sigilStartDelimiter/,\n                {\n                    token: 'sigil.delimiter',\n                    switchTo: '@sigilContinue.$S2.$S3.$S4.$S5'\n                }\n            ]\n        ],\n        'sigilContinue.noInterpol': [\n            // Ignore escaped sigil end\n            [/(^|[^\\\\])\\\\@sigilEndDelimiter/, 'sigil'],\n            [\n                /(@sigilEndDelimiter)[a-zA-Z]*/,\n                {\n                    cases: {\n                        '$1==$S5': { token: 'sigil.delimiter', next: '@pop' },\n                        '@default': 'sigil'\n                    }\n                }\n            ],\n            { include: '@sigilContent' }\n        ],\n        // Attributes\n        attributes: [\n            // Module @doc* attributes - tokenized as comments\n            [\n                /\\@(module|type)?doc (~[sS])?\"\"\"/,\n                {\n                    token: 'comment.block.documentation',\n                    next: '@doubleQuotedHeredocDocstring'\n                }\n            ],\n            [\n                /\\@(module|type)?doc (~[sS])?\"/,\n                {\n                    token: 'comment.block.documentation',\n                    next: '@doubleQuotedStringDocstring'\n                }\n            ],\n            [/\\@(module|type)?doc false/, 'comment.block.documentation'],\n            // Module attributes\n            [/\\@(@variableName)/, 'variable']\n        ],\n        doubleQuotedHeredocDocstring: [\n            [/\"\"\"/, { token: 'comment.block.documentation', next: '@pop' }],\n            { include: '@docstringContent' }\n        ],\n        doubleQuotedStringDocstring: [\n            [/\"/, { token: 'comment.block.documentation', next: '@pop' }],\n            { include: '@docstringContent' }\n        ],\n        // Operators, punctuation, brackets\n        symbols: [\n            // Code point operator (either with regular character ?a or an escaped one ?\\n)\n            [/\\?(\\\\.|[^\\\\\\s])/, 'number.constant'],\n            // Anonymous function arguments\n            [/&\\d+/, 'operator'],\n            // Bitshift operators (must go before delimiters, so that << >> don't match first)\n            [/<<<|>>>/, 'operator'],\n            // Delimiter pairs\n            [/[()\\[\\]\\{\\}]|<<|>>/, '@brackets'],\n            // Triple dot is a valid name (must go before operators, so that .. doesn't match instead)\n            [/\\.\\.\\./, 'identifier'],\n            // Punctuation => (must go before operators, so it's not tokenized as = then >)\n            [/=>/, 'punctuation'],\n            // Operators\n            [/@operator/, 'operator'],\n            // Punctuation\n            [/[:;,.%]/, 'punctuation']\n        ],\n        // Generic helpers\n        stringContentInterpol: [\n            { include: '@interpolation' },\n            { include: '@escapeChar' },\n            { include: '@stringContent' }\n        ],\n        stringContent: [[/./, 'string']],\n        stringConstantContentInterpol: [\n            { include: '@interpolation' },\n            { include: '@escapeChar' },\n            { include: '@stringConstantContent' }\n        ],\n        stringConstantContent: [[/./, 'constant']],\n        regexpContentInterpol: [\n            { include: '@interpolation' },\n            { include: '@escapeChar' },\n            { include: '@regexpContent' }\n        ],\n        regexpContent: [\n            // # may be a regular regexp char, so we use a heuristic\n            // assuming a # surrounded by whitespace is actually a comment.\n            [/(\\s)(#)(\\s.*)$/, ['white', 'comment.punctuation', 'comment']],\n            [/./, 'regexp']\n        ],\n        sigilContentInterpol: [\n            { include: '@interpolation' },\n            { include: '@escapeChar' },\n            { include: '@sigilContent' }\n        ],\n        sigilContent: [[/./, 'sigil']],\n        docstringContent: [[/./, 'comment.block.documentation']],\n        escapeChar: [[/@escape/, 'constant.character.escape']],\n        interpolation: [[/#{/, { token: 'delimiter.bracket.embed', next: '@interpolationContinue' }]],\n        interpolationContinue: [\n            [/}/, { token: 'delimiter.bracket.embed', next: '@pop' }],\n            // Interpolation brackets may contain arbitrary code,\n            // so we simply match against all the root rules,\n            // until we reach interpolation end (the above matches).\n            { include: '@root' }\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,aAAa;AAAA,EACjB;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,SAAS,EAAE;AAAA,IAC5C,EAAE,MAAM,OAAO,OAAO,MAAM;AAAA,IAC5B,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,MAAM,OAAO,KAAK;AAAA,EAC9B;AAAA,EACA,kBAAkB;AAAA,IACd,uBAAuB;AAAA,IACvB,uBAAuB;AAAA,EAC3B;AACJ;AAWO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACN,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,MAAM,OAAO,MAAM,OAAO,0BAA0B;AAAA,EAChE;AAAA;AAAA,EAEA,qBAAqB;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,kBAAkB,CAAC,OAAO,MAAM,OAAO,MAAM,MAAM;AAAA,EACnD,mBAAmB,CAAC,SAAS,UAAU,WAAW,KAAK;AAAA,EACvD,eAAe;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,WAAW,CAAC,QAAQ,SAAS,KAAK;AAAA,EAClC,aAAa,CAAC,cAAc,WAAW,WAAW,cAAc,gBAAgB;AAAA;AAAA;AAAA,EAGhF,UAAU;AAAA;AAAA,EAEV,cAAc;AAAA;AAAA,EAEd,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,YAAY;AAAA;AAAA,EAEZ,yBAAyB;AAAA,EACzB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,SAAS;AAAA,EACT,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA;AAAA,EAER,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMR,WAAW;AAAA,IACP,MAAM;AAAA,MACF,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,YAAY;AAAA;AAAA;AAAA,MAGvB,EAAE,SAAS,qBAAqB;AAAA,MAChC,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,eAAe;AAAA,MAC1B,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,SAAS;AAAA,MACpB,EAAE,SAAS,UAAU;AAAA,MACrB,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA,IAC1B;AAAA;AAAA,IAEA,YAAY,CAAC,CAAC,OAAO,OAAO,CAAC;AAAA;AAAA,IAE7B,UAAU,CAAC,CAAC,WAAW,CAAC,uBAAuB,SAAS,CAAC,CAAC;AAAA;AAAA,IAE1D,mBAAmB;AAAA,MACf,CAAC,kBAAkB,CAAC,YAAY,sBAAsB,CAAC;AAAA;AAAA;AAAA,MAGvD;AAAA,QACI;AAAA,QACA,EAAE,OAAO,sBAAsB,MAAM,6BAA6B;AAAA,MACtE;AAAA,MACA;AAAA,QACI;AAAA,QACA,EAAE,OAAO,sBAAsB,MAAM,6BAA6B;AAAA,MACtE;AAAA,IACJ;AAAA,IACA,2BAA2B;AAAA,MACvB,CAAC,MAAM,EAAE,OAAO,sBAAsB,MAAM,OAAO,CAAC;AAAA,MACpD,EAAE,SAAS,iCAAiC;AAAA,IAChD;AAAA,IACA,2BAA2B;AAAA,MACvB,CAAC,MAAM,EAAE,OAAO,sBAAsB,MAAM,OAAO,CAAC;AAAA,MACpD,EAAE,SAAS,iCAAiC;AAAA,IAChD;AAAA;AAAA,IAEA,SAAS;AAAA,MACL,CAAC,aAAa,eAAe;AAAA,MAC7B,CAAC,YAAY,cAAc;AAAA,MAC3B,CAAC,UAAU,YAAY;AAAA,MACvB,CAAC,uCAAuC,cAAc;AAAA,MACtD,CAAC,YAAY,QAAQ;AAAA,IACzB;AAAA;AAAA,IAEA,aAAa;AAAA;AAAA;AAAA;AAAA,MAIT;AAAA,QACI;AAAA,QACA;AAAA,UACI;AAAA,UACA;AAAA,UACA;AAAA,YACI,OAAO;AAAA,cACH,SAAS;AAAA,cACT,YAAY;AAAA,YAChB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA;AAAA;AAAA,QAEI;AAAA,QACA;AAAA,UACI,OAAO;AAAA;AAAA,YAEH,wBAAwB;AAAA,YACxB,sBAAsB;AAAA,YACtB,kBAAkB;AAAA,YAClB,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA;AAAA;AAAA,QAEI;AAAA,QACA,CAAC,mBAAmB,SAAS,YAAY,SAAS,eAAe;AAAA,MACrE;AAAA,MACA;AAAA;AAAA,QAEI;AAAA,QACA,CAAC,wBAAwB,YAAY,SAAS,YAAY,SAAS,eAAe;AAAA,MACtF;AAAA,MACA;AAAA;AAAA,QAEI;AAAA,QACA;AAAA,UACI;AAAA,UACA;AAAA,UACA;AAAA,YACI,OAAO;AAAA,cACH,kBAAkB;AAAA,cAClB,YAAY;AAAA,YAChB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,MACA;AAAA;AAAA,QAEI;AAAA,QACA,CAAC,YAAY,SAAS,eAAe;AAAA,MACzC;AAAA;AAAA,MAEA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,wBAAwB;AAAA,YACxB,qBAAqB;AAAA,YACrB,sBAAsB;AAAA,YACtB,kBAAkB;AAAA,YAClB,cAAc;AAAA,YACd,gBAAgB;AAAA,YAChB,OAAO;AAAA,YACP,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,CAAC,eAAe,iBAAiB;AAAA,IACrC;AAAA;AAAA,IAEA,SAAS;AAAA,MACL,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,uBAAuB,CAAC;AAAA,MACnE,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,uBAAuB,CAAC;AAAA,MACnE,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,sBAAsB,CAAC;AAAA,MAChE,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,sBAAsB,CAAC;AAAA,IACpE;AAAA,IACA,qBAAqB;AAAA,MACjB,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,MACnD,EAAE,SAAS,yBAAyB;AAAA,IACxC;AAAA,IACA,qBAAqB;AAAA,MACjB,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,MACnD,EAAE,SAAS,yBAAyB;AAAA,IACxC;AAAA,IACA,oBAAoB;AAAA,MAChB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,MACjD,EAAE,SAAS,yBAAyB;AAAA,IACxC;AAAA,IACA,oBAAoB;AAAA,MAChB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,MACjD,EAAE,SAAS,yBAAyB;AAAA,IACxC;AAAA;AAAA,IAEA,OAAO;AAAA,MACH,CAAC,kBAAkB,CAAC,wBAAwB,UAAU,CAAC;AAAA,MACvD,CAAC,MAAM,EAAE,OAAO,sBAAsB,MAAM,0BAA0B,CAAC;AAAA,MACvE,CAAC,MAAM,EAAE,OAAO,sBAAsB,MAAM,0BAA0B,CAAC;AAAA,IAC3E;AAAA,IACA,wBAAwB;AAAA,MACpB,CAAC,KAAK,EAAE,OAAO,sBAAsB,MAAM,OAAO,CAAC;AAAA,MACnD,EAAE,SAAS,iCAAiC;AAAA,IAChD;AAAA,IACA,wBAAwB;AAAA,MACpB,CAAC,KAAK,EAAE,OAAO,sBAAsB,MAAM,OAAO,CAAC;AAAA,MACnD,EAAE,SAAS,iCAAiC;AAAA,IAChD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAqBA,QAAQ;AAAA,MACJ,CAAC,8BAA8B,EAAE,OAAO,YAAY,MAAM,kBAAkB,CAAC;AAAA,MAC7E,CAAC,8BAA8B,EAAE,OAAO,YAAY,MAAM,oBAAoB,CAAC;AAAA,IACnF;AAAA,IACA,OAAO;AAAA,MACH,CAAC,iBAAiB,EAAE,OAAO,YAAY,UAAU,yBAAyB,CAAC;AAAA,MAC3E,CAAC,iBAAiB,EAAE,OAAO,YAAY,UAAU,yBAAyB,CAAC;AAAA,MAC3E,CAAC,iBAAiB,EAAE,OAAO,YAAY,UAAU,yBAAyB,CAAC;AAAA,MAC3E,CAAC,iBAAiB,EAAE,OAAO,YAAY,UAAU,yBAAyB,CAAC;AAAA,MAC3E;AAAA,QACI;AAAA,QACA,EAAE,OAAO,YAAY,UAAU,2BAA2B;AAAA,MAC9D;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,yBAAyB;AAAA,MACrB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,4BAA4B;AAAA,MACxB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,WAAW,EAAE,OAAO,oBAAoB,MAAM,OAAO;AAAA,YACrD,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,EAAE,SAAS,yBAAyB;AAAA,IACxC;AAAA,IACA,2BAA2B;AAAA,MACvB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,8BAA8B;AAAA;AAAA,MAE1B,CAAC,iCAAiC,QAAQ;AAAA,MAC1C;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,WAAW,EAAE,OAAO,oBAAoB,MAAM,OAAO;AAAA,YACrD,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,EAAE,SAAS,iBAAiB;AAAA,IAChC;AAAA,IACA,yBAAyB;AAAA,MACrB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,4BAA4B;AAAA,MACxB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,WAAW,EAAE,OAAO,oBAAoB,MAAM,OAAO;AAAA,YACrD,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,EAAE,SAAS,yBAAyB;AAAA,IACxC;AAAA,IACA,2BAA2B;AAAA,MACvB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,8BAA8B;AAAA;AAAA,MAE1B,CAAC,iCAAiC,QAAQ;AAAA,MAC1C;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,WAAW,EAAE,OAAO,oBAAoB,MAAM,OAAO;AAAA,YACrD,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,EAAE,SAAS,iBAAiB;AAAA,IAChC;AAAA;AAAA,IAEA,uBAAuB;AAAA,MACnB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,0BAA0B;AAAA,MACtB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,WAAW,EAAE,OAAO,mBAAmB,MAAM,OAAO;AAAA,YACpD,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,EAAE,SAAS,wBAAwB;AAAA,IACvC;AAAA,IACA,yBAAyB;AAAA,MACrB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,4BAA4B;AAAA;AAAA,MAExB,CAAC,iCAAiC,OAAO;AAAA,MACzC;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,WAAW,EAAE,OAAO,mBAAmB,MAAM,OAAO;AAAA,YACpD,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,EAAE,SAAS,gBAAgB;AAAA,IAC/B;AAAA;AAAA,IAEA,YAAY;AAAA;AAAA,MAER;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,QACV;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,QACV;AAAA,MACJ;AAAA,MACA,CAAC,6BAA6B,6BAA6B;AAAA;AAAA,MAE3D,CAAC,qBAAqB,UAAU;AAAA,IACpC;AAAA,IACA,8BAA8B;AAAA,MAC1B,CAAC,OAAO,EAAE,OAAO,+BAA+B,MAAM,OAAO,CAAC;AAAA,MAC9D,EAAE,SAAS,oBAAoB;AAAA,IACnC;AAAA,IACA,6BAA6B;AAAA,MACzB,CAAC,KAAK,EAAE,OAAO,+BAA+B,MAAM,OAAO,CAAC;AAAA,MAC5D,EAAE,SAAS,oBAAoB;AAAA,IACnC;AAAA;AAAA,IAEA,SAAS;AAAA;AAAA,MAEL,CAAC,mBAAmB,iBAAiB;AAAA;AAAA,MAErC,CAAC,QAAQ,UAAU;AAAA;AAAA,MAEnB,CAAC,WAAW,UAAU;AAAA;AAAA,MAEtB,CAAC,sBAAsB,WAAW;AAAA;AAAA,MAElC,CAAC,UAAU,YAAY;AAAA;AAAA,MAEvB,CAAC,MAAM,aAAa;AAAA;AAAA,MAEpB,CAAC,aAAa,UAAU;AAAA;AAAA,MAExB,CAAC,WAAW,aAAa;AAAA,IAC7B;AAAA;AAAA,IAEA,uBAAuB;AAAA,MACnB,EAAE,SAAS,iBAAiB;AAAA,MAC5B,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,iBAAiB;AAAA,IAChC;AAAA,IACA,eAAe,CAAC,CAAC,KAAK,QAAQ,CAAC;AAAA,IAC/B,+BAA+B;AAAA,MAC3B,EAAE,SAAS,iBAAiB;AAAA,MAC5B,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,yBAAyB;AAAA,IACxC;AAAA,IACA,uBAAuB,CAAC,CAAC,KAAK,UAAU,CAAC;AAAA,IACzC,uBAAuB;AAAA,MACnB,EAAE,SAAS,iBAAiB;AAAA,MAC5B,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,iBAAiB;AAAA,IAChC;AAAA,IACA,eAAe;AAAA;AAAA;AAAA,MAGX,CAAC,kBAAkB,CAAC,SAAS,uBAAuB,SAAS,CAAC;AAAA,MAC9D,CAAC,KAAK,QAAQ;AAAA,IAClB;AAAA,IACA,sBAAsB;AAAA,MAClB,EAAE,SAAS,iBAAiB;AAAA,MAC5B,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,gBAAgB;AAAA,IAC/B;AAAA,IACA,cAAc,CAAC,CAAC,KAAK,OAAO,CAAC;AAAA,IAC7B,kBAAkB,CAAC,CAAC,KAAK,6BAA6B,CAAC;AAAA,IACvD,YAAY,CAAC,CAAC,WAAW,2BAA2B,CAAC;AAAA,IACrD,eAAe,CAAC,CAAC,MAAM,EAAE,OAAO,2BAA2B,MAAM,yBAAyB,CAAC,CAAC;AAAA,IAC5F,uBAAuB;AAAA,MACnB,CAAC,KAAK,EAAE,OAAO,2BAA2B,MAAM,OAAO,CAAC;AAAA;AAAA;AAAA;AAAA,MAIxD,EAAE,SAAS,QAAQ;AAAA,IACvB;AAAA,EACJ;AACJ;", "names": []}