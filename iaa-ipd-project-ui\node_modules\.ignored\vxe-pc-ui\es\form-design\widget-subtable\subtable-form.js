import { defineComponent, h, inject } from 'vue';
import { getI18n } from '@vxe-ui/core';
import { useWidgetName } from '../../form-design/src/use';
import VxeFormComponent from '../../form/src/form';
import VxeFormItemComponent from '../../form/src/form-item';
import VxeInputComponent from '../../input/src/input';
import VxeSwitchComponent from '../../switch/src/switch';
export const WidgetSubtableFormComponent = defineComponent({
    props: {
        renderOpts: {
            type: Object,
            default: () => ({})
        },
        renderParams: {
            type: Object,
            default: () => ({})
        }
    },
    emits: [],
    setup(props) {
        const $xeFormDesign = inject('$xeFormDesign', null);
        if (!$xeFormDesign) {
            return () => [];
        }
        const { computeKebabCaseName } = useWidgetName(props);
        return () => {
            const { renderParams } = props;
            const { widget } = renderParams;
            const { options } = widget;
            const kebabCaseName = computeKebabCaseName.value;
            return h(VxeFormComponent, {
                class: `vxe-form-design--widget-${kebabCaseName}-form`,
                vertical: true,
                span: 24,
                titleBold: true,
                titleOverflow: true,
                data: options
            }, {
                default() {
                    return [
                        h(VxeFormItemComponent, {
                            title: getI18n('vxe.formDesign.widgetProp.name')
                        }, {
                            default() {
                                return h(VxeInputComponent, {
                                    modelValue: widget.title,
                                    'onUpdate:modelValue'(val) {
                                        widget.title = val;
                                    }
                                });
                            }
                        }),
                        h(VxeFormItemComponent, {
                            title: getI18n('vxe.formDesign.widgetProp.subtableProp.showCheckbox'),
                            field: 'showCheckbox'
                        }, {
                            default() {
                                return h(VxeSwitchComponent, {
                                    modelValue: options.showCheckbox,
                                    'onUpdate:modelValue'(val) {
                                        options.showCheckbox = val;
                                    }
                                });
                            }
                        })
                    ];
                }
            });
        };
    }
});
