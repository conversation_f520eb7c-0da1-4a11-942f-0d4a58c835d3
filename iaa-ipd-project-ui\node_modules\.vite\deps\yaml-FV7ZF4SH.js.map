{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/yaml/yaml.js"], "sourcesContent": ["export var conf = {\n    comments: {\n        lineComment: '#'\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" }\n    ],\n    surroundingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" }\n    ],\n    folding: {\n        offSide: true\n    }\n};\nexport var language = {\n    tokenPostfix: '.yaml',\n    brackets: [\n        { token: 'delimiter.bracket', open: '{', close: '}' },\n        { token: 'delimiter.square', open: '[', close: ']' }\n    ],\n    keywords: ['true', 'True', 'TRUE', 'false', 'False', 'FALSE', 'null', 'Null', 'Null', '~'],\n    numberInteger: /(?:0|[+-]?[0-9]+)/,\n    numberFloat: /(?:0|[+-]?[0-9]+)(?:\\.[0-9]+)?(?:e[-+][1-9][0-9]*)?/,\n    numberOctal: /0o[0-7]+/,\n    numberHex: /0x[0-9a-fA-F]+/,\n    numberInfinity: /[+-]?\\.(?:inf|Inf|INF)/,\n    numberNaN: /\\.(?:nan|Nan|NAN)/,\n    numberDate: /\\d{4}-\\d\\d-\\d\\d([Tt ]\\d\\d:\\d\\d:\\d\\d(\\.\\d+)?(( ?[+-]\\d\\d?(:\\d\\d)?)|Z)?)?/,\n    escapes: /\\\\(?:[btnfr\\\\\"']|[0-7][0-7]?|[0-3][0-7]{2})/,\n    tokenizer: {\n        root: [\n            { include: '@whitespace' },\n            { include: '@comment' },\n            // Directive\n            [/%[^ ]+.*$/, 'meta.directive'],\n            // Document Markers\n            [/---/, 'operators.directivesEnd'],\n            [/\\.{3}/, 'operators.documentEnd'],\n            // Block Structure Indicators\n            [/[-?:](?= )/, 'operators'],\n            { include: '@anchor' },\n            { include: '@tagHandle' },\n            { include: '@flowCollections' },\n            { include: '@blockStyle' },\n            // Numbers\n            [/@numberInteger(?![ \\t]*\\S+)/, 'number'],\n            [/@numberFloat(?![ \\t]*\\S+)/, 'number.float'],\n            [/@numberOctal(?![ \\t]*\\S+)/, 'number.octal'],\n            [/@numberHex(?![ \\t]*\\S+)/, 'number.hex'],\n            [/@numberInfinity(?![ \\t]*\\S+)/, 'number.infinity'],\n            [/@numberNaN(?![ \\t]*\\S+)/, 'number.nan'],\n            [/@numberDate(?![ \\t]*\\S+)/, 'number.date'],\n            // Key:Value pair\n            [/(\".*?\"|'.*?'|.*?)([ \\t]*)(:)( |$)/, ['type', 'white', 'operators', 'white']],\n            { include: '@flowScalars' },\n            // String nodes\n            [\n                /[^#]+/,\n                {\n                    cases: {\n                        '@keywords': 'keyword',\n                        '@default': 'string'\n                    }\n                }\n            ]\n        ],\n        // Flow Collection: Flow Mapping\n        object: [\n            { include: '@whitespace' },\n            { include: '@comment' },\n            // Flow Mapping termination\n            [/\\}/, '@brackets', '@pop'],\n            // Flow Mapping delimiter\n            [/,/, 'delimiter.comma'],\n            // Flow Mapping Key:Value delimiter\n            [/:(?= )/, 'operators'],\n            // Flow Mapping Key:Value key\n            [/(?:\".*?\"|'.*?'|[^,\\{\\[]+?)(?=: )/, 'type'],\n            // Start Flow Style\n            { include: '@flowCollections' },\n            { include: '@flowScalars' },\n            // Scalar Data types\n            { include: '@tagHandle' },\n            { include: '@anchor' },\n            { include: '@flowNumber' },\n            // Other value (keyword or string)\n            [\n                /[^\\},]+/,\n                {\n                    cases: {\n                        '@keywords': 'keyword',\n                        '@default': 'string'\n                    }\n                }\n            ]\n        ],\n        // Flow Collection: Flow Sequence\n        array: [\n            { include: '@whitespace' },\n            { include: '@comment' },\n            // Flow Sequence termination\n            [/\\]/, '@brackets', '@pop'],\n            // Flow Sequence delimiter\n            [/,/, 'delimiter.comma'],\n            // Start Flow Style\n            { include: '@flowCollections' },\n            { include: '@flowScalars' },\n            // Scalar Data types\n            { include: '@tagHandle' },\n            { include: '@anchor' },\n            { include: '@flowNumber' },\n            // Other value (keyword or string)\n            [\n                /[^\\],]+/,\n                {\n                    cases: {\n                        '@keywords': 'keyword',\n                        '@default': 'string'\n                    }\n                }\n            ]\n        ],\n        // First line of a Block Style\n        multiString: [[/^( +).+$/, 'string', '@multiStringContinued.$1']],\n        // Further lines of a Block Style\n        //   Workaround for indentation detection\n        multiStringContinued: [\n            [\n                /^( *).+$/,\n                {\n                    cases: {\n                        '$1==$S2': 'string',\n                        '@default': { token: '@rematch', next: '@popall' }\n                    }\n                }\n            ]\n        ],\n        whitespace: [[/[ \\t\\r\\n]+/, 'white']],\n        // Only line comments\n        comment: [[/#.*$/, 'comment']],\n        // Start Flow Collections\n        flowCollections: [\n            [/\\[/, '@brackets', '@array'],\n            [/\\{/, '@brackets', '@object']\n        ],\n        // Start Flow Scalars (quoted strings)\n        flowScalars: [\n            [/\"([^\"\\\\]|\\\\.)*$/, 'string.invalid'],\n            [/'([^'\\\\]|\\\\.)*$/, 'string.invalid'],\n            [/'[^']*'/, 'string'],\n            [/\"/, 'string', '@doubleQuotedString']\n        ],\n        doubleQuotedString: [\n            [/[^\\\\\"]+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/\\\\./, 'string.escape.invalid'],\n            [/\"/, 'string', '@pop']\n        ],\n        // Start Block Scalar\n        blockStyle: [[/[>|][0-9]*[+-]?$/, 'operators', '@multiString']],\n        // Numbers in Flow Collections (terminate with ,]})\n        flowNumber: [\n            [/@numberInteger(?=[ \\t]*[,\\]\\}])/, 'number'],\n            [/@numberFloat(?=[ \\t]*[,\\]\\}])/, 'number.float'],\n            [/@numberOctal(?=[ \\t]*[,\\]\\}])/, 'number.octal'],\n            [/@numberHex(?=[ \\t]*[,\\]\\}])/, 'number.hex'],\n            [/@numberInfinity(?=[ \\t]*[,\\]\\}])/, 'number.infinity'],\n            [/@numberNaN(?=[ \\t]*[,\\]\\}])/, 'number.nan'],\n            [/@numberDate(?=[ \\t]*[,\\]\\}])/, 'number.date']\n        ],\n        tagHandle: [[/\\![^ ]*/, 'tag']],\n        anchor: [[/[&*][^ ]+/, 'namespace']]\n    }\n};\n"], "mappings": ";;;AAAO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,aAAa;AAAA,EACjB;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,SAAS;AAAA,IACL,SAAS;AAAA,EACb;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,UAAU;AAAA,IACN,EAAE,OAAO,qBAAqB,MAAM,KAAK,OAAO,IAAI;AAAA,IACpD,EAAE,OAAO,oBAAoB,MAAM,KAAK,OAAO,IAAI;AAAA,EACvD;AAAA,EACA,UAAU,CAAC,QAAQ,QAAQ,QAAQ,SAAS,SAAS,SAAS,QAAQ,QAAQ,QAAQ,GAAG;AAAA,EACzF,eAAe;AAAA,EACf,aAAa;AAAA,EACb,aAAa;AAAA,EACb,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA,IACP,MAAM;AAAA,MACF,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA;AAAA,MAEtB,CAAC,aAAa,gBAAgB;AAAA;AAAA,MAE9B,CAAC,OAAO,yBAAyB;AAAA,MACjC,CAAC,SAAS,uBAAuB;AAAA;AAAA,MAEjC,CAAC,cAAc,WAAW;AAAA,MAC1B,EAAE,SAAS,UAAU;AAAA,MACrB,EAAE,SAAS,aAAa;AAAA,MACxB,EAAE,SAAS,mBAAmB;AAAA,MAC9B,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB,CAAC,+BAA+B,QAAQ;AAAA,MACxC,CAAC,6BAA6B,cAAc;AAAA,MAC5C,CAAC,6BAA6B,cAAc;AAAA,MAC5C,CAAC,2BAA2B,YAAY;AAAA,MACxC,CAAC,gCAAgC,iBAAiB;AAAA,MAClD,CAAC,2BAA2B,YAAY;AAAA,MACxC,CAAC,4BAA4B,aAAa;AAAA;AAAA,MAE1C,CAAC,qCAAqC,CAAC,QAAQ,SAAS,aAAa,OAAO,CAAC;AAAA,MAC7E,EAAE,SAAS,eAAe;AAAA;AAAA,MAE1B;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,aAAa;AAAA,YACb,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA,IAEA,QAAQ;AAAA,MACJ,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA;AAAA,MAEtB,CAAC,MAAM,aAAa,MAAM;AAAA;AAAA,MAE1B,CAAC,KAAK,iBAAiB;AAAA;AAAA,MAEvB,CAAC,UAAU,WAAW;AAAA;AAAA,MAEtB,CAAC,oCAAoC,MAAM;AAAA;AAAA,MAE3C,EAAE,SAAS,mBAAmB;AAAA,MAC9B,EAAE,SAAS,eAAe;AAAA;AAAA,MAE1B,EAAE,SAAS,aAAa;AAAA,MACxB,EAAE,SAAS,UAAU;AAAA,MACrB,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,aAAa;AAAA,YACb,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA,IAEA,OAAO;AAAA,MACH,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA;AAAA,MAEtB,CAAC,MAAM,aAAa,MAAM;AAAA;AAAA,MAE1B,CAAC,KAAK,iBAAiB;AAAA;AAAA,MAEvB,EAAE,SAAS,mBAAmB;AAAA,MAC9B,EAAE,SAAS,eAAe;AAAA;AAAA,MAE1B,EAAE,SAAS,aAAa;AAAA,MACxB,EAAE,SAAS,UAAU;AAAA,MACrB,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,aAAa;AAAA,YACb,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA,IAEA,aAAa,CAAC,CAAC,YAAY,UAAU,0BAA0B,CAAC;AAAA;AAAA;AAAA,IAGhE,sBAAsB;AAAA,MAClB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,WAAW;AAAA,YACX,YAAY,EAAE,OAAO,YAAY,MAAM,UAAU;AAAA,UACrD;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,YAAY,CAAC,CAAC,cAAc,OAAO,CAAC;AAAA;AAAA,IAEpC,SAAS,CAAC,CAAC,QAAQ,SAAS,CAAC;AAAA;AAAA,IAE7B,iBAAiB;AAAA,MACb,CAAC,MAAM,aAAa,QAAQ;AAAA,MAC5B,CAAC,MAAM,aAAa,SAAS;AAAA,IACjC;AAAA;AAAA,IAEA,aAAa;AAAA,MACT,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,KAAK,UAAU,qBAAqB;AAAA,IACzC;AAAA,IACA,oBAAoB;AAAA,MAChB,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,UAAU,MAAM;AAAA,IAC1B;AAAA;AAAA,IAEA,YAAY,CAAC,CAAC,oBAAoB,aAAa,cAAc,CAAC;AAAA;AAAA,IAE9D,YAAY;AAAA,MACR,CAAC,mCAAmC,QAAQ;AAAA,MAC5C,CAAC,iCAAiC,cAAc;AAAA,MAChD,CAAC,iCAAiC,cAAc;AAAA,MAChD,CAAC,+BAA+B,YAAY;AAAA,MAC5C,CAAC,oCAAoC,iBAAiB;AAAA,MACtD,CAAC,+BAA+B,YAAY;AAAA,MAC5C,CAAC,gCAAgC,aAAa;AAAA,IAClD;AAAA,IACA,WAAW,CAAC,CAAC,WAAW,KAAK,CAAC;AAAA,IAC9B,QAAQ,CAAC,CAAC,aAAa,WAAW,CAAC;AAAA,EACvC;AACJ;", "names": []}