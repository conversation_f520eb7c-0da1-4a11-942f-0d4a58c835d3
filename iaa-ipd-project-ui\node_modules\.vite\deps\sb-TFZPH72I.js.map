{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/sb/sb.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    comments: {\n        lineComment: \"'\"\n    },\n    brackets: [\n        ['(', ')'],\n        ['[', ']'],\n        ['If', 'EndIf'],\n        ['While', 'EndWhile'],\n        ['For', 'EndFor'],\n        ['Sub', 'EndSub']\n    ],\n    autoClosingPairs: [\n        { open: '\"', close: '\"', notIn: ['string', 'comment'] },\n        { open: '(', close: ')', notIn: ['string', 'comment'] },\n        { open: '[', close: ']', notIn: ['string', 'comment'] }\n    ]\n};\nexport var language = {\n    defaultToken: '',\n    tokenPostfix: '.sb',\n    ignoreCase: true,\n    brackets: [\n        { token: 'delimiter.array', open: '[', close: ']' },\n        { token: 'delimiter.parenthesis', open: '(', close: ')' },\n        // Special bracket statement pairs\n        { token: 'keyword.tag-if', open: 'If', close: 'EndIf' },\n        { token: 'keyword.tag-while', open: 'While', close: 'EndWhile' },\n        { token: 'keyword.tag-for', open: 'For', close: 'EndFor' },\n        { token: 'keyword.tag-sub', open: 'Sub', close: 'EndSub' }\n    ],\n    keywords: [\n        'Else',\n        'ElseIf',\n        'EndFor',\n        'EndIf',\n        'EndSub',\n        'EndWhile',\n        'For',\n        'Goto',\n        'If',\n        'Step',\n        'Sub',\n        'Then',\n        'To',\n        'While'\n    ],\n    tagwords: ['If', 'Sub', 'While', 'For'],\n    operators: ['>', '<', '<>', '<=', '>=', 'And', 'Or', '+', '-', '*', '/', '='],\n    // we include these common regular expressions\n    identifier: /[a-zA-Z_][\\w]*/,\n    symbols: /[=><:+\\-*\\/%\\.,]+/,\n    escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n    // The main tokenizer for our languages\n    tokenizer: {\n        root: [\n            // whitespace\n            { include: '@whitespace' },\n            // classes\n            [/(@identifier)(?=[.])/, 'type'],\n            // identifiers, tagwords, and keywords\n            [\n                /@identifier/,\n                {\n                    cases: {\n                        '@keywords': { token: 'keyword.$0' },\n                        '@operators': 'operator',\n                        '@default': 'variable.name'\n                    }\n                }\n            ],\n            // methods, properties, and events\n            [\n                /([.])(@identifier)/,\n                {\n                    cases: {\n                        $2: ['delimiter', 'type.member'],\n                        '@default': ''\n                    }\n                }\n            ],\n            // numbers\n            [/\\d*\\.\\d+/, 'number.float'],\n            [/\\d+/, 'number'],\n            // delimiters and operators\n            [/[()\\[\\]]/, '@brackets'],\n            [\n                /@symbols/,\n                {\n                    cases: {\n                        '@operators': 'operator',\n                        '@default': 'delimiter'\n                    }\n                }\n            ],\n            // strings\n            [/\"([^\"\\\\]|\\\\.)*$/, 'string.invalid'],\n            [/\"/, 'string', '@string']\n        ],\n        whitespace: [\n            [/[ \\t\\r\\n]+/, ''],\n            [/(\\').*$/, 'comment']\n        ],\n        string: [\n            [/[^\\\\\"]+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/\\\\./, 'string.escape.invalid'],\n            [/\"C?/, 'string', '@pop']\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,aAAa;AAAA,EACjB;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,MAAM,OAAO;AAAA,IACd,CAAC,SAAS,UAAU;AAAA,IACpB,CAAC,OAAO,QAAQ;AAAA,IAChB,CAAC,OAAO,QAAQ;AAAA,EACpB;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EAC1D;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,UAAU;AAAA,IACN,EAAE,OAAO,mBAAmB,MAAM,KAAK,OAAO,IAAI;AAAA,IAClD,EAAE,OAAO,yBAAyB,MAAM,KAAK,OAAO,IAAI;AAAA;AAAA,IAExD,EAAE,OAAO,kBAAkB,MAAM,MAAM,OAAO,QAAQ;AAAA,IACtD,EAAE,OAAO,qBAAqB,MAAM,SAAS,OAAO,WAAW;AAAA,IAC/D,EAAE,OAAO,mBAAmB,MAAM,OAAO,OAAO,SAAS;AAAA,IACzD,EAAE,OAAO,mBAAmB,MAAM,OAAO,OAAO,SAAS;AAAA,EAC7D;AAAA,EACA,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,UAAU,CAAC,MAAM,OAAO,SAAS,KAAK;AAAA,EACtC,WAAW,CAAC,KAAK,KAAK,MAAM,MAAM,MAAM,OAAO,MAAM,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA;AAAA,EAE5E,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,SAAS;AAAA;AAAA,EAET,WAAW;AAAA,IACP,MAAM;AAAA;AAAA,MAEF,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB,CAAC,wBAAwB,MAAM;AAAA;AAAA,MAE/B;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,aAAa,EAAE,OAAO,aAAa;AAAA,YACnC,cAAc;AAAA,YACd,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,IAAI,CAAC,aAAa,aAAa;AAAA,YAC/B,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,CAAC,YAAY,cAAc;AAAA,MAC3B,CAAC,OAAO,QAAQ;AAAA;AAAA,MAEhB,CAAC,YAAY,WAAW;AAAA,MACxB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,cAAc;AAAA,YACd,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,KAAK,UAAU,SAAS;AAAA,IAC7B;AAAA,IACA,YAAY;AAAA,MACR,CAAC,cAAc,EAAE;AAAA,MACjB,CAAC,WAAW,SAAS;AAAA,IACzB;AAAA,IACA,QAAQ;AAAA,MACJ,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,OAAO,UAAU,MAAM;AAAA,IAC5B;AAAA,EACJ;AACJ;", "names": []}