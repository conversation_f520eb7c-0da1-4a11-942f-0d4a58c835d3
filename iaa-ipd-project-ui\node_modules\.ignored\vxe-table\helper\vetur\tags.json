{"vxe-table": {"attributes": ["id", "data", "height", "min-height", "max-height", "auto-resize", "sync-resize", "resizable", "stripe", "border", "padding", "round", "size", "loading", "align", "header-align", "footer-align", "show-header", "highlight-current-row", "highlight-hover-row", "highlight-current-column", "highlight-hover-column", "row-class-name", "cell-class-name", "header-row-class-name", "header-cell-class-name", "footer-row-class-name", "footer-cell-class-name", "cell-style", "header-cell-style", "footer-cell-style", "row-style", "header-row-style", "footer-row-style", "show-footer", "footer-data", "footer-method", "merge-cells", "merge-footer-items", "span-method", "footer-span-method", "show-overflow", "show-header-overflow", "show-footer-overflow", "column-key", "row-key", "row-id", "keep-source", "column-config", "current-column-config", "cell-config", "header-cell-config", "footer-cell-config", "row-config", "current-row-config", "row-group-config", "resize-config", "resizable-config", "seq-config", "sort-config", "drag-config", "row-drag-config", "column-drag-config", "filter-config", "export-config", "import-config", "print-config", "radio-config", "checkbox-config", "tooltip-config", "expand-config", "tree-config", "menu-config", "clip-config", "fnr-config", "mouse-config", "area-config", "keyboard-config", "edit-config", "valid-config", "edit-rules", "empty-text", "empty-render", "loading-config", "custom-config", "scroll-x", "scroll-y", "virtual-x-config", "virtual-y-config", "scrollbar-config", "params"], "subtags": ["vxe-colgroup", "vxe-column"], "description": "基础表格"}, "vxe-colgroup": {"attributes": ["field", "title", "width", "min-width", "resizable", "visible", "fixed", "align", "header-align", "show-overflow", "show-header-overflow", "header-class-name"], "subtags": ["vxe-column"], "description": "基础表格 - 分组列"}, "vxe-column": {"attributes": ["type", "field", "title", "width", "min-width", "resizable", "visible", "fixed", "align", "header-align", "footer-align", "show-overflow", "show-header-overflow", "show-footer-overflow", "class-name", "header-class-name", "footer-class-name", "padding", "vertical-align", "formatter", "sortable", "sort-by", "sort-type", "filters", "filter-multiple", "filter-method", "filter-reset-method", "filter-recover-method", "filter-render", "header-export-method", "export-method", "footer-export-method", "title-help", "title-prefix", "title-suffix", "cell-type", "cell-render", "edit-render", "content-render", "tree-node", "params", "col-id"], "description": "基础表格 - 列"}, "vxe-grid": {"attributes": ["id", "columns", "data", "height", "min-height", "max-height", "auto-resize", "sync-resize", "resizable", "stripe", "border", "padding", "round", "size", "loading", "align", "header-align", "footer-align", "show-header", "highlight-current-row", "highlight-hover-row", "highlight-current-column", "highlight-hover-column", "row-class-name", "cell-class-name", "header-row-class-name", "header-cell-class-name", "footer-row-class-name", "footer-cell-class-name", "cell-style", "header-cell-style", "footer-cell-style", "row-style", "header-row-style", "footer-row-style", "show-footer", "footer-data", "footer-method", "merge-cells", "merge-footer-items", "span-method", "footer-span-method", "show-overflow", "show-header-overflow", "show-footer-overflow", "column-key", "row-key", "row-id", "keep-source", "column-config", "current-column-config", "cell-config", "header-cell-config", "footer-cell-config", "row-config", "current-row-config", "row-group-config", "resize-config", "resizable-config", "seq-config", "sort-config", "drag-config", "row-drag-config", "column-drag-config", "filter-config", "export-config", "import-config", "print-config", "radio-config", "checkbox-config", "tooltip-config", "expand-config", "tree-config", "menu-config", "clip-config", "fnr-config", "mouse-config", "area-config", "keyboard-config", "edit-config", "valid-config", "edit-rules", "empty-text", "empty-render", "loading-config", "custom-config", "scroll-x", "scroll-y", "virtual-x-config", "virtual-y-config", "scrollbar-config", "params", "form-config", "toolbar-config", "pager-config", "proxy-config", "zoom-config", "layouts"], "description": "配置式表格"}, "vxe-toolbar": {"attributes": ["size", "loading", "class-name", "import", "export", "print", "refresh", "custom", "buttons", "tools"], "description": "工具栏"}}