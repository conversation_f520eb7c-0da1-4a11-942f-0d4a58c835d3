Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=exports.VxeColumn=exports.Column=void 0;var _ui=require("../ui"),_column=_interopRequireDefault(require("../table/src/column"));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let VxeColumn=exports.VxeColumn=Object.assign({},_column.default,{install(e){e.component(_column.default.name,_column.default),e.component("VxeTableColumn",_column.default)}}),Column=(_ui.VxeUI.dynamicApp&&(_ui.VxeUI.dynamicApp.component(_column.default.name,_column.default),_ui.VxeUI.dynamicApp.component("VxeTableColumn",_column.default)),_ui.VxeUI.component(_column.default),exports.Column=VxeColumn);var _default=exports.default=VxeColumn;