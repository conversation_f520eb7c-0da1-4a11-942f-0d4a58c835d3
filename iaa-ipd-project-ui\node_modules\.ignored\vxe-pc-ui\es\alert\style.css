.vxe-alert {
  position: relative;
  display: flex;
  flex-direction: row;
  color: var(--vxe-ui-font-color);
  padding: var(--vxe-ui-layout-padding-default);
  margin-bottom: var(--vxe-ui-layout-padding-default);
  font-size: var(--vxe-ui-font-size-default);
  border-radius: var(--vxe-ui-base-border-radius);
}
.vxe-alert.theme--primary {
  color: var(--vxe-ui-font-primary-color);
  border: 1px solid var(--vxe-ui-font-primary-lighten-color);
  background-color: var(--vxe-ui-font-primary-tinge-color);
}
.vxe-alert.theme--primary::after {
  background-color: var(--vxe-ui-font-primary-color);
}
.vxe-alert.theme--primary .vxe-alert--icon {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-alert.theme--primary .vxe-alert--close-btn:hover {
  color: var(--vxe-ui-font-primary-lighten-color);
}
.vxe-alert.theme--success {
  color: var(--vxe-ui-status-success-color);
  border: 1px solid var(--vxe-ui-status-success-lighten-color);
  background-color: var(--vxe-ui-status-success-tinge-color);
}
.vxe-alert.theme--success::after {
  background-color: var(--vxe-ui-status-success-color);
}
.vxe-alert.theme--success .vxe-alert--icon {
  color: var(--vxe-ui-status-success-color);
}
.vxe-alert.theme--success .vxe-alert--close-btn:hover {
  color: var(--vxe-ui-status-success-lighten-color);
}
.vxe-alert.theme--info {
  color: var(--vxe-ui-status-info-color);
  border: 1px solid var(--vxe-ui-status-info-lighten-color);
  background-color: var(--vxe-ui-status-info-tinge-color);
}
.vxe-alert.theme--info::after {
  background-color: var(--vxe-ui-status-info-color);
}
.vxe-alert.theme--info .vxe-alert--icon {
  color: var(--vxe-ui-status-info-color);
}
.vxe-alert.theme--info .vxe-alert--close-btn:hover {
  color: var(--vxe-ui-status-info-lighten-color);
}
.vxe-alert.theme--warning {
  color: var(--vxe-ui-status-warning-color);
  border: 1px solid var(--vxe-ui-status-warning-lighten-color);
  background-color: var(--vxe-ui-status-warning-tinge-color);
}
.vxe-alert.theme--warning::after {
  background-color: var(--vxe-ui-status-warning-color);
}
.vxe-alert.theme--warning .vxe-alert--icon {
  color: var(--vxe-ui-status-warning-color);
}
.vxe-alert.theme--warning .vxe-alert--close-btn:hover {
  color: var(--vxe-ui-status-warning-lighten-color);
}
.vxe-alert.theme--danger {
  color: var(--vxe-ui-status-danger-color);
  border: 1px solid var(--vxe-ui-status-danger-lighten-color);
  background-color: var(--vxe-ui-status-danger-tinge-color);
}
.vxe-alert.theme--danger::after {
  background-color: var(--vxe-ui-status-danger-color);
}
.vxe-alert.theme--danger .vxe-alert--icon {
  color: var(--vxe-ui-status-danger-color);
}
.vxe-alert.theme--danger .vxe-alert--close-btn:hover {
  color: var(--vxe-ui-status-danger-lighten-color);
}
.vxe-alert.theme--error {
  color: var(--vxe-ui-status-error-color);
  border: 1px solid var(--vxe-ui-status-error-lighten-color);
  background-color: var(--vxe-ui-status-error-tinge-color);
}
.vxe-alert.theme--error::after {
  background-color: var(--vxe-ui-status-error-color);
}
.vxe-alert.theme--error .vxe-alert--icon {
  color: var(--vxe-ui-status-error-color);
}
.vxe-alert.theme--error .vxe-alert--close-btn:hover {
  color: var(--vxe-ui-status-error-lighten-color);
}
.vxe-alert.size--medium {
  font-size: var(--vxe-ui-font-size-medium);
}
.vxe-alert.size--small {
  font-size: var(--vxe-ui-font-size-small);
}
.vxe-alert.size--mini {
  font-size: var(--vxe-ui-font-size-mini);
}

.vxe-alert--icon {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 1.5em;
  padding: 0 var(--vxe-ui-layout-padding-default);
}

.vxe-alert--body {
  flex-grow: 1;
}

.vxe-alert--title {
  font-size: 1.1em;
  font-weight: 700;
  line-height: 2em;
}

.vxe-alert--close-btn {
  padding: 0 0.4em;
  line-height: 2.2em;
  cursor: pointer;
}