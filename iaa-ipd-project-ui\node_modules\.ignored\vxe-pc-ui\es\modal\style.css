html[data-vxe-lock-scroll] {
  overflow: hidden;
}
html[data-vxe-lock-scroll] body {
  overflow: hidden;
  width: var(--vxe-ui-modal-lock-scroll-view-width);
}

.vxe-modal--box {
  visibility: hidden;
  width: 420px;
  background-color: var(--vxe-ui-layout-background-color);
  border-radius: var(--vxe-ui-base-border-radius);
  border: 1px solid var(--vxe-ui-base-popup-border-color);
  text-align: left;
  pointer-events: auto;
  opacity: 0;
  outline: 0;
}
.vxe-modal--box.is--drag {
  cursor: move;
}
.vxe-modal--box.is--drag .vxe-modal--body:after,
.vxe-modal--box.is--drag .vxe-modal--footer:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.vxe-modal--box.is--drag .vxe-modal--body {
  overflow: hidden;
}
.vxe-modal--box.is--drag .vxe-modal--body .vxe-modal--content {
  overflow: hidden;
}

.vxe-modal--aside {
  flex-shrink: 0;
  overflow: auto;
}

.vxe-modal--container {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  overflow: hidden;
}

.vxe-modal--status-wrapper {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  font-size: 1.6em;
}

.vxe-modal--content {
  flex-grow: 1;
  white-space: pre-line;
}

.vxe-modal--header,
.vxe-modal--body,
.vxe-modal--footer {
  position: relative;
}

.vxe-modal--body {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  outline: 0;
}

.vxe-modal--body-left,
.vxe-modal--body-right {
  flex-shrink: 0;
  overflow: auto;
  outline: 0;
}

.vxe-modal--body-default {
  display: flex;
  flex-grow: 1;
  overflow: auto;
  outline: 0;
}

.vxe-modal--header {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 2.8em;
  flex-shrink: 0;
  font-size: 1.1em;
  font-weight: 700;
  border-radius: var(--vxe-ui-base-border-radius) var(--vxe-ui-base-border-radius) 0 0;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-modal--header.is--ellipsis .vxe-modal--header-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.vxe-modal--footer-wrapper {
  display: flex;
  flex-direction: row;
}
.vxe-modal--footer-wrapper .vxe-modal--footer-left {
  flex-grow: 1;
  text-align: left;
}
.vxe-modal--footer-wrapper .vxe-modal--footer-right {
  flex-shrink: 0;
}

.vxe-modal--header-title {
  flex-grow: 1;
  padding: 0.8em 0 0.8em 0.6em;
}

.vxe-modal--header-right {
  display: flex;
  flex-direction: row;
  flex-shrink: 0;
  padding: 0 0.6em 0 0;
}

.vxe-modal--zoom-btn,
.vxe-modal--close-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.6em;
  height: 1.6em;
  margin-left: 0.5em;
  cursor: pointer;
}
.vxe-modal--zoom-btn:hover,
.vxe-modal--close-btn:hover {
  color: var(--vxe-ui-font-primary-color);
}

.vxe-modal--footer {
  flex-shrink: 0;
  text-align: right;
  padding: 0.4em 1em 0.8em 1em;
}

.vxe-modal--resize .wl-resize,
.vxe-modal--resize .wr-resize,
.vxe-modal--resize .swst-resize,
.vxe-modal--resize .sest-resize,
.vxe-modal--resize .st-resize,
.vxe-modal--resize .swlb-resize,
.vxe-modal--resize .selb-resize,
.vxe-modal--resize .sb-resize {
  position: absolute;
  z-index: 100;
}
.vxe-modal--resize .wl-resize,
.vxe-modal--resize .wr-resize {
  width: 8px;
  height: 100%;
  top: 0;
  cursor: w-resize;
}
.vxe-modal--resize .wl-resize {
  left: -5px;
}
.vxe-modal--resize .wr-resize {
  right: -5px;
}
.vxe-modal--resize .swst-resize,
.vxe-modal--resize .sest-resize,
.vxe-modal--resize .swlb-resize,
.vxe-modal--resize .selb-resize {
  width: 10px;
  height: 10px;
  z-index: 101;
}
.vxe-modal--resize .swst-resize,
.vxe-modal--resize .sest-resize {
  top: -8px;
}
.vxe-modal--resize .swlb-resize,
.vxe-modal--resize .selb-resize {
  bottom: -8px;
}
.vxe-modal--resize .sest-resize,
.vxe-modal--resize .swlb-resize {
  cursor: sw-resize;
}
.vxe-modal--resize .swst-resize,
.vxe-modal--resize .selb-resize {
  cursor: se-resize;
}
.vxe-modal--resize .swst-resize,
.vxe-modal--resize .swlb-resize {
  left: -8px;
}
.vxe-modal--resize .sest-resize,
.vxe-modal--resize .selb-resize {
  right: -8px;
}
.vxe-modal--resize .st-resize,
.vxe-modal--resize .sb-resize {
  width: 100%;
  height: 8px;
  left: 0;
  cursor: s-resize;
}
.vxe-modal--resize .st-resize {
  top: -5px;
}
.vxe-modal--resize .sb-resize {
  bottom: -5px;
}

.vxe-modal--wrapper {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  line-height: 1.5;
  width: calc(100% + 18px);
  height: calc(100% + 18px);
  color: var(--vxe-ui-font-color);
  font-family: var(--vxe-ui-font-family);
  outline: 0;
}
.vxe-modal--wrapper.is--active {
  display: block;
}
.vxe-modal--wrapper.is--visible.is--mask:before {
  background-color: rgba(0, 0, 0, 0.5);
}
.vxe-modal--wrapper.is--visible .vxe-modal--box {
  opacity: 1;
  visibility: visible;
}
.vxe-modal--wrapper:not(.lock--view) {
  pointer-events: none;
}
.vxe-modal--wrapper.is--draggable.zoom--revert .vxe-modal--header-title, .vxe-modal--wrapper.is--draggable.zoom--minimize .vxe-modal--header-title {
  cursor: move;
}
.vxe-modal--wrapper.zoom--minimize > .vxe-modal--box > .vxe-modal--container > .vxe-modal--resize .wl-resize,
.vxe-modal--wrapper.zoom--minimize > .vxe-modal--box > .vxe-modal--container > .vxe-modal--resize .wr-resize,
.vxe-modal--wrapper.zoom--minimize > .vxe-modal--box > .vxe-modal--container > .vxe-modal--resize .swst-resize,
.vxe-modal--wrapper.zoom--minimize > .vxe-modal--box > .vxe-modal--container > .vxe-modal--resize .sest-resize,
.vxe-modal--wrapper.zoom--minimize > .vxe-modal--box > .vxe-modal--container > .vxe-modal--resize .st-resize,
.vxe-modal--wrapper.zoom--minimize > .vxe-modal--box > .vxe-modal--container > .vxe-modal--resize .swlb-resize,
.vxe-modal--wrapper.zoom--minimize > .vxe-modal--box > .vxe-modal--container > .vxe-modal--resize .selb-resize,
.vxe-modal--wrapper.zoom--minimize > .vxe-modal--box > .vxe-modal--container > .vxe-modal--resize .sb-resize, .vxe-modal--wrapper.zoom--maximize > .vxe-modal--box > .vxe-modal--container > .vxe-modal--resize .wl-resize,
.vxe-modal--wrapper.zoom--maximize > .vxe-modal--box > .vxe-modal--container > .vxe-modal--resize .wr-resize,
.vxe-modal--wrapper.zoom--maximize > .vxe-modal--box > .vxe-modal--container > .vxe-modal--resize .swst-resize,
.vxe-modal--wrapper.zoom--maximize > .vxe-modal--box > .vxe-modal--container > .vxe-modal--resize .sest-resize,
.vxe-modal--wrapper.zoom--maximize > .vxe-modal--box > .vxe-modal--container > .vxe-modal--resize .st-resize,
.vxe-modal--wrapper.zoom--maximize > .vxe-modal--box > .vxe-modal--container > .vxe-modal--resize .swlb-resize,
.vxe-modal--wrapper.zoom--maximize > .vxe-modal--box > .vxe-modal--container > .vxe-modal--resize .selb-resize,
.vxe-modal--wrapper.zoom--maximize > .vxe-modal--box > .vxe-modal--container > .vxe-modal--resize .sb-resize {
  display: none;
}
.vxe-modal--wrapper.zoom--maximize > .vxe-modal--box {
  border-radius: 0;
}
.vxe-modal--wrapper.zoom--maximize > .vxe-modal--box > .vxe-modal--container > .vxe-modal--header {
  border-radius: 0;
  cursor: default;
}
.vxe-modal--wrapper.zoom--minimize > .vxe-modal--box > .vxe-modal--container > .vxe-modal--body, .vxe-modal--wrapper.zoom--minimize > .vxe-modal--box > .vxe-modal--container > .vxe-modal--footer {
  display: none;
}
.vxe-modal--wrapper.type--modal.lock--scroll, .vxe-modal--wrapper.type--alert.lock--scroll, .vxe-modal--wrapper.type--confirm.lock--scroll {
  overflow: hidden;
}
.vxe-modal--wrapper.type--modal:not(.lock--scroll), .vxe-modal--wrapper.type--alert:not(.lock--scroll), .vxe-modal--wrapper.type--confirm:not(.lock--scroll) {
  overflow: auto;
}
.vxe-modal--wrapper.lock--view:before, .vxe-modal--wrapper.is--mask:before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  pointer-events: auto;
}
.vxe-modal--wrapper.is--mask:before {
  background-color: rgba(0, 0, 0, 0);
}
.vxe-modal--wrapper.is--animat.is--mask:before {
  transition: background-color 0.2s ease-in-out;
}
.vxe-modal--wrapper.type--message .vxe-modal--body, .vxe-modal--wrapper.type--notification .vxe-modal--body, .vxe-modal--wrapper.type--alert .vxe-modal--body, .vxe-modal--wrapper.type--confirm .vxe-modal--body {
  white-space: normal;
  word-break: break-word;
}
.vxe-modal--wrapper.type--message .vxe-modal--box {
  opacity: 0;
  transform: translate(0, -100%);
}
.vxe-modal--wrapper.type--message.is--visible .vxe-modal--box {
  opacity: 1;
  transform: translate(0, 0);
}
.vxe-modal--wrapper.type--notification.pos--top-right .vxe-modal--box {
  opacity: 0;
  transform: translate(100%, 0);
}
.vxe-modal--wrapper.type--notification.pos--top-right.is--visible .vxe-modal--box {
  opacity: 1;
  transform: translate(0, 0);
}
.vxe-modal--wrapper.type--message, .vxe-modal--wrapper.type--notification {
  width: 100%;
  height: 100%;
  padding: 0 var(--vxe-ui-layout-padding-double);
  text-align: center;
  transition: top 0.3s ease-in-out;
}
.vxe-modal--wrapper.type--message.pos--top-left, .vxe-modal--wrapper.type--notification.pos--top-left {
  text-align: left;
}
.vxe-modal--wrapper.type--message.pos--top-right, .vxe-modal--wrapper.type--notification.pos--top-right {
  text-align: right;
}
.vxe-modal--wrapper.type--message .vxe-modal--box, .vxe-modal--wrapper.type--notification .vxe-modal--box {
  display: inline-flex;
  flex-direction: row;
  margin-top: 0;
  width: auto;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-out;
}
.vxe-modal--wrapper.type--message .vxe-modal--box .vxe-modal--body:after, .vxe-modal--wrapper.type--notification .vxe-modal--box .vxe-modal--body:after {
  content: "";
  display: block;
  clear: both;
  height: 0;
  overflow: hidden;
  visibility: hidden;
}
.vxe-modal--wrapper.type--message .vxe-modal--box .vxe-modal--content, .vxe-modal--wrapper.type--notification .vxe-modal--box .vxe-modal--content {
  max-width: 800px;
  float: left;
}
.vxe-modal--wrapper.type--modal .vxe-modal--box, .vxe-modal--wrapper.type--alert .vxe-modal--box, .vxe-modal--wrapper.type--confirm .vxe-modal--box {
  display: flex;
  flex-direction: row;
  position: fixed;
  left: 50%;
  top: 0;
  box-shadow: var(--vxe-ui-base-popup-box-shadow);
}
.vxe-modal--wrapper.type--modal .vxe-modal--header, .vxe-modal--wrapper.type--alert .vxe-modal--header, .vxe-modal--wrapper.type--confirm .vxe-modal--header {
  border-bottom: 1px solid var(--vxe-ui-input-border-color);
  background-color: var(--vxe-ui-modal-header-background-color);
}
.vxe-modal--wrapper.type--modal .vxe-modal--body, .vxe-modal--wrapper.type--alert .vxe-modal--body, .vxe-modal--wrapper.type--confirm .vxe-modal--body {
  overflow: auto;
}
.vxe-modal--wrapper.type--modal .vxe-modal--body .vxe-modal--content, .vxe-modal--wrapper.type--alert .vxe-modal--body .vxe-modal--content, .vxe-modal--wrapper.type--confirm .vxe-modal--body .vxe-modal--content {
  overflow: auto;
}
.vxe-modal--wrapper.status--info .vxe-modal--status-wrapper {
  color: var(--vxe-ui-status-info-color);
}
.vxe-modal--wrapper.status--warning .vxe-modal--status-wrapper, .vxe-modal--wrapper.status--question .vxe-modal--status-wrapper {
  color: var(--vxe-ui-status-warning-color);
}
.vxe-modal--wrapper.status--success .vxe-modal--status-wrapper {
  color: var(--vxe-ui-status-success-color);
}
.vxe-modal--wrapper.status--error .vxe-modal--status-wrapper {
  color: var(--vxe-ui-status-danger-color);
}
.vxe-modal--wrapper.status--loading .vxe-modal--status-wrapper {
  color: var(--vxe-ui-font-disabled-color);
}
.vxe-modal--wrapper.is--padding .vxe-modal--aside {
  padding: 0.8em 0.6em;
}
.vxe-modal--wrapper.is--padding .vxe-modal--body-default {
  padding: 0.8em 0.6em;
}
.vxe-modal--wrapper.is--padding .vxe-modal--body-default .vxe-modal--status-wrapper {
  padding-right: 0.6em;
}
.vxe-modal--wrapper.is--padding.type--notification .vxe-modal--body-default {
  padding-top: 0;
}
.vxe-modal--wrapper.type--message .vxe-modal--body-default {
  padding-right: 1.2em;
}

.vxe-modal--wrapper {
  font-size: var(--vxe-ui-font-size-default);
}
.vxe-modal--wrapper.size--medium {
  font-size: var(--vxe-ui-font-size-medium);
}
.vxe-modal--wrapper.size--small {
  font-size: var(--vxe-ui-font-size-small);
}
.vxe-modal--wrapper.size--mini {
  font-size: var(--vxe-ui-font-size-mini);
}