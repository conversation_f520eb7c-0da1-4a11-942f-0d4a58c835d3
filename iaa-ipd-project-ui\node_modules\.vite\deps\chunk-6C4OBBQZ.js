import {
  _defineProperty,
  init_defineProperty
} from "./chunk-LZQZ2OHM.js";
import {
  __esm
} from "./chunk-GFT2G5UO.js";

// node_modules/@babel/runtime/helpers/esm/objectSpread2.js
function ownKeys(e, r) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var o = Object.getOwnPropertySymbols(e);
    r && (o = o.filter(function(r2) {
      return Object.getOwnPropertyDescriptor(e, r2).enumerable;
    })), t.push.apply(t, o);
  }
  return t;
}
function _objectSpread2(e) {
  for (var r = 1; r < arguments.length; r++) {
    var t = null != arguments[r] ? arguments[r] : {};
    r % 2 ? ownKeys(Object(t), true).forEach(function(r2) {
      _defineProperty(e, r2, t[r2]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r2) {
      Object.defineProperty(e, r2, Object.getOwnPropertyDescriptor(t, r2));
    });
  }
  return e;
}
var init_objectSpread2 = __esm({
  "node_modules/@babel/runtime/helpers/esm/objectSpread2.js"() {
    init_defineProperty();
  }
});

export {
  _objectSpread2,
  init_objectSpread2
};
//# sourceMappingURL=chunk-6C4OBBQZ.js.map
