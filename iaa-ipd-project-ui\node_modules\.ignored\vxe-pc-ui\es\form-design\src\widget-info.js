import { ref } from 'vue';
import { renderer } from '@vxe-ui/core';
import { getNewWidgetId } from './util';
import { errLog } from '../../ui/src/log';
import XEUtils from 'xe-utils';
// 控件原始配置信息，带响应
const refWidgetReactConfigMaps = ref({});
export const getWidgetConfig = (name) => {
    const widgetReactConfigMaps = refWidgetReactConfigMaps.value;
    return widgetReactConfigMaps[name] || {};
};
export function getWidgetConfigTitle(name, $xeFormDesign) {
    const widgetConf = getWidgetConfig(name);
    if (widgetConf) {
        const configTitle = widgetConf.title;
        const params = { name, $formDesign: $xeFormDesign };
        return XEUtils.toValueString(XEUtils.isFunction(configTitle) ? configTitle(params) : configTitle);
    }
    return name;
}
export function getWidgetConfigCustomGroup(name, $xeFormDesign) {
    const widgetConf = getWidgetConfig(name);
    if (widgetConf) {
        const configCustomGroup = widgetConf.customGroup;
        const params = { name, $formDesign: $xeFormDesign };
        return XEUtils.toValueString(XEUtils.isFunction(configCustomGroup) ? configCustomGroup(params) : configCustomGroup);
    }
    return name;
}
const validWidgetUniqueField = (field, widgetObjList) => {
    return !XEUtils.findTree(widgetObjList, item => item.field === field, { children: 'children' });
};
export class FormDesignWidgetInfo {
    constructor($xeFormDesign, name, widgetObjList) {
        Object.defineProperty(this, "id", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: 0
        });
        Object.defineProperty(this, "field", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: ''
        });
        Object.defineProperty(this, "title", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: ''
        });
        Object.defineProperty(this, "name", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: ''
        });
        Object.defineProperty(this, "required", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: false
        });
        Object.defineProperty(this, "hidden", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: false
        });
        Object.defineProperty(this, "options", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: {}
        });
        Object.defineProperty(this, "children", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: []
        });
        Object.defineProperty(this, "model", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: {
                update: false,
                value: ''
            }
        });
        let customField = '';
        if (name) {
            const compConf = renderer.get(name) || {};
            if (compConf) {
                const widgetReactConfigMaps = refWidgetReactConfigMaps.value;
                const createWidgetFormConfig = compConf.createFormDesignWidgetConfig;
                if (createWidgetFormConfig) {
                    const params = { name, $formDesign: $xeFormDesign };
                    const widgetConfig = createWidgetFormConfig(params) || {};
                    const titleConf = widgetConfig.title;
                    const fieldConf = widgetConfig.field;
                    this.title = XEUtils.toValueString(XEUtils.isFunction(titleConf) ? titleConf(params) : titleConf);
                    this.options = widgetConfig.options || {};
                    this.children = widgetConfig.children || [];
                    if (fieldConf) {
                        if (XEUtils.isFunction(fieldConf)) {
                            customField = fieldConf({ name, $formDesign: $xeFormDesign });
                        }
                        else {
                            customField = fieldConf;
                        }
                    }
                    if (!widgetReactConfigMaps[name]) {
                        widgetReactConfigMaps[name] = Object.assign({}, widgetConfig);
                        refWidgetReactConfigMaps.value = Object.assign({}, widgetReactConfigMaps);
                    }
                }
            }
        }
        const widgetId = getNewWidgetId(widgetObjList);
        if (customField) {
            // 如果使用了自定义字段，验证字段名是否唯一
            if (!validWidgetUniqueField(customField, widgetObjList)) {
                errLog('vxe.error.uniField', [customField]);
            }
        }
        this.id = widgetId;
        this.field = customField || `${name}${widgetId}`;
        this.name = name;
    }
}
export function configToWidget(conf) {
    const widget = new FormDesignWidgetInfo(null, conf.name, []);
    widget.id = conf.id;
    widget.title = conf.title || '';
    widget.field = conf.field || '';
    widget.required = conf.required || false;
    widget.hidden = conf.hidden || false;
    widget.options = Object.assign({}, widget.options, conf.options);
    widget.children = conf.children ? conf.children.map(item => configToWidget(item)) : [];
    return widget;
}
