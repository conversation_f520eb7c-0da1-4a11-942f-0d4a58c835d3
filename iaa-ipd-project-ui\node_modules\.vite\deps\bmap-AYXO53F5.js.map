{"version": 3, "sources": ["../../echarts/extension/bmap/BMapCoordSys.js", "../../echarts/extension/bmap/BMapModel.js", "../../echarts/extension/bmap/BMapView.js", "../../echarts/extension/bmap/bmap.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// @ts-nocheck\n/* global BMap */\nimport { util as zrUtil, graphic, matrix } from 'echarts';\nfunction BMapCoordSys(bmap, api) {\n  this._bmap = bmap;\n  this.dimensions = ['lng', 'lat'];\n  this._mapOffset = [0, 0];\n  this._api = api;\n  this._projection = new BMap.MercatorProjection();\n}\nBMapCoordSys.prototype.type = 'bmap';\nBMapCoordSys.prototype.dimensions = ['lng', 'lat'];\nBMapCoordSys.prototype.setZoom = function (zoom) {\n  this._zoom = zoom;\n};\nBMapCoordSys.prototype.setCenter = function (center) {\n  this._center = this._projection.lngLatToPoint(new BMap.Point(center[0], center[1]));\n};\nBMapCoordSys.prototype.setMapOffset = function (mapOffset) {\n  this._mapOffset = mapOffset;\n};\nBMapCoordSys.prototype.getBMap = function () {\n  return this._bmap;\n};\nBMapCoordSys.prototype.dataToPoint = function (data) {\n  var point = new BMap.Point(data[0], data[1]);\n  // TODO mercator projection is toooooooo slow\n  // let mercatorPoint = this._projection.lngLatToPoint(point);\n  // let width = this._api.getZr().getWidth();\n  // let height = this._api.getZr().getHeight();\n  // let divider = Math.pow(2, 18 - 10);\n  // return [\n  //     Math.round((mercatorPoint.x - this._center.x) / divider + width / 2),\n  //     Math.round((this._center.y - mercatorPoint.y) / divider + height / 2)\n  // ];\n  var px = this._bmap.pointToOverlayPixel(point);\n  var mapOffset = this._mapOffset;\n  return [px.x - mapOffset[0], px.y - mapOffset[1]];\n};\nBMapCoordSys.prototype.pointToData = function (pt) {\n  var mapOffset = this._mapOffset;\n  pt = this._bmap.overlayPixelToPoint({\n    x: pt[0] + mapOffset[0],\n    y: pt[1] + mapOffset[1]\n  });\n  return [pt.lng, pt.lat];\n};\nBMapCoordSys.prototype.getViewRect = function () {\n  var api = this._api;\n  return new graphic.BoundingRect(0, 0, api.getWidth(), api.getHeight());\n};\nBMapCoordSys.prototype.getRoamTransform = function () {\n  return matrix.create();\n};\nBMapCoordSys.prototype.prepareCustoms = function () {\n  var rect = this.getViewRect();\n  return {\n    coordSys: {\n      // The name exposed to user is always 'cartesian2d' but not 'grid'.\n      type: 'bmap',\n      x: rect.x,\n      y: rect.y,\n      width: rect.width,\n      height: rect.height\n    },\n    api: {\n      coord: zrUtil.bind(this.dataToPoint, this),\n      size: zrUtil.bind(dataToCoordSize, this)\n    }\n  };\n};\nBMapCoordSys.prototype.convertToPixel = function (ecModel, finder, value) {\n  // here we ignore finder as only one bmap component is allowed\n  return this.dataToPoint(value);\n};\nBMapCoordSys.prototype.convertFromPixel = function (ecModel, finder, value) {\n  return this.pointToData(value);\n};\nfunction dataToCoordSize(dataSize, dataItem) {\n  dataItem = dataItem || [0, 0];\n  return zrUtil.map([0, 1], function (dimIdx) {\n    var val = dataItem[dimIdx];\n    var halfSize = dataSize[dimIdx] / 2;\n    var p1 = [];\n    var p2 = [];\n    p1[dimIdx] = val - halfSize;\n    p2[dimIdx] = val + halfSize;\n    p1[1 - dimIdx] = p2[1 - dimIdx] = dataItem[1 - dimIdx];\n    return Math.abs(this.dataToPoint(p1)[dimIdx] - this.dataToPoint(p2)[dimIdx]);\n  }, this);\n}\nvar Overlay;\n// For deciding which dimensions to use when creating list data\nBMapCoordSys.dimensions = BMapCoordSys.prototype.dimensions;\nfunction createOverlayCtor() {\n  function Overlay(root) {\n    this._root = root;\n  }\n  Overlay.prototype = new BMap.Overlay();\n  /**\n   * 初始化\n   *\n   * @param {BMap.Map} map\n   * @override\n   */\n  Overlay.prototype.initialize = function (map) {\n    map.getPanes().labelPane.appendChild(this._root);\n    return this._root;\n  };\n  /**\n   * @override\n   */\n  Overlay.prototype.draw = function () {};\n  return Overlay;\n}\nBMapCoordSys.create = function (ecModel, api) {\n  var bmapCoordSys;\n  var root = api.getDom();\n  // TODO Dispose\n  ecModel.eachComponent('bmap', function (bmapModel) {\n    var painter = api.getZr().painter;\n    var viewportRoot = painter.getViewportRoot();\n    if (typeof BMap === 'undefined') {\n      throw new Error('BMap api is not loaded');\n    }\n    Overlay = Overlay || createOverlayCtor();\n    if (bmapCoordSys) {\n      throw new Error('Only one bmap component can exist');\n    }\n    var bmap;\n    if (!bmapModel.__bmap) {\n      // Not support IE8\n      var bmapRoot = root.querySelector('.ec-extension-bmap');\n      if (bmapRoot) {\n        // Reset viewport left and top, which will be changed\n        // in moving handler in BMapView\n        viewportRoot.style.left = '0px';\n        viewportRoot.style.top = '0px';\n        root.removeChild(bmapRoot);\n      }\n      bmapRoot = document.createElement('div');\n      bmapRoot.className = 'ec-extension-bmap';\n      // fix #13424\n      bmapRoot.style.cssText = 'position:absolute;width:100%;height:100%';\n      root.appendChild(bmapRoot);\n      // initializes bmap\n      var mapOptions = bmapModel.get('mapOptions');\n      if (mapOptions) {\n        mapOptions = zrUtil.clone(mapOptions);\n        // Not support `mapType`, use `bmap.setMapType(MapType)` instead.\n        delete mapOptions.mapType;\n      }\n      bmap = bmapModel.__bmap = new BMap.Map(bmapRoot, mapOptions);\n      var overlay = new Overlay(viewportRoot);\n      bmap.addOverlay(overlay);\n      // Override\n      painter.getViewportRootOffset = function () {\n        return {\n          offsetLeft: 0,\n          offsetTop: 0\n        };\n      };\n    }\n    bmap = bmapModel.__bmap;\n    // Set bmap options\n    // centerAndZoom before layout and render\n    var center = bmapModel.get('center');\n    var zoom = bmapModel.get('zoom');\n    if (center && zoom) {\n      var bmapCenter = bmap.getCenter();\n      var bmapZoom = bmap.getZoom();\n      var centerOrZoomChanged = bmapModel.centerOrZoomChanged([bmapCenter.lng, bmapCenter.lat], bmapZoom);\n      if (centerOrZoomChanged) {\n        var pt = new BMap.Point(center[0], center[1]);\n        bmap.centerAndZoom(pt, zoom);\n      }\n    }\n    bmapCoordSys = new BMapCoordSys(bmap, api);\n    bmapCoordSys.setMapOffset(bmapModel.__mapOffset || [0, 0]);\n    bmapCoordSys.setZoom(zoom);\n    bmapCoordSys.setCenter(center);\n    bmapModel.coordinateSystem = bmapCoordSys;\n  });\n  ecModel.eachSeries(function (seriesModel) {\n    if (seriesModel.get('coordinateSystem') === 'bmap') {\n      seriesModel.coordinateSystem = bmapCoordSys;\n    }\n  });\n  // return created coordinate systems\n  return bmapCoordSys && [bmapCoordSys];\n};\nexport default BMapCoordSys;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// @ts-nocheck\nimport * as echarts from 'echarts';\nfunction v2Equal(a, b) {\n  return a && b && a[0] === b[0] && a[1] === b[1];\n}\nexport default echarts.extendComponentModel({\n  type: 'bmap',\n  getBMap: function () {\n    // __bmap is injected when creating BMapCoordSys\n    return this.__bmap;\n  },\n  setCenterAndZoom: function (center, zoom) {\n    this.option.center = center;\n    this.option.zoom = zoom;\n  },\n  centerOrZoomChanged: function (center, zoom) {\n    var option = this.option;\n    return !(v2Equal(center, option.center) && zoom === option.zoom);\n  },\n  defaultOption: {\n    center: [104.114129, 37.550339],\n    zoom: 5,\n    // 2.0 https://lbsyun.baidu.com/custom/index.htm\n    mapStyle: {},\n    // 3.0 https://lbsyun.baidu.com/index.php?title=open/custom\n    mapStyleV2: {},\n    // See https://lbsyun.baidu.com/cms/jsapi/reference/jsapi_reference.html#a0b1\n    mapOptions: {},\n    roam: false\n  }\n});", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// @ts-nocheck\nimport * as echarts from 'echarts';\nfunction isEmptyObject(obj) {\n  for (var key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      return false;\n    }\n  }\n  return true;\n}\nexport default echarts.extendComponentView({\n  type: 'bmap',\n  render: function (bMapModel, ecModel, api) {\n    var rendering = true;\n    var bmap = bMapModel.getBMap();\n    var viewportRoot = api.getZr().painter.getViewportRoot();\n    var coordSys = bMapModel.coordinateSystem;\n    var moveHandler = function (type, target) {\n      if (rendering) {\n        return;\n      }\n      var offsetEl = viewportRoot.parentNode.parentNode.parentNode;\n      var mapOffset = [-parseInt(offsetEl.style.left, 10) || 0, -parseInt(offsetEl.style.top, 10) || 0];\n      // only update style when map offset changed\n      var viewportRootStyle = viewportRoot.style;\n      var offsetLeft = mapOffset[0] + 'px';\n      var offsetTop = mapOffset[1] + 'px';\n      if (viewportRootStyle.left !== offsetLeft) {\n        viewportRootStyle.left = offsetLeft;\n      }\n      if (viewportRootStyle.top !== offsetTop) {\n        viewportRootStyle.top = offsetTop;\n      }\n      coordSys.setMapOffset(mapOffset);\n      bMapModel.__mapOffset = mapOffset;\n      api.dispatchAction({\n        type: 'bmapRoam',\n        animation: {\n          duration: 0\n        }\n      });\n    };\n    function zoomEndHandler() {\n      if (rendering) {\n        return;\n      }\n      api.dispatchAction({\n        type: 'bmapRoam',\n        animation: {\n          duration: 0\n        }\n      });\n    }\n    bmap.removeEventListener('moving', this._oldMoveHandler);\n    bmap.removeEventListener('moveend', this._oldMoveHandler);\n    bmap.removeEventListener('zoomend', this._oldZoomEndHandler);\n    bmap.addEventListener('moving', moveHandler);\n    bmap.addEventListener('moveend', moveHandler);\n    bmap.addEventListener('zoomend', zoomEndHandler);\n    this._oldMoveHandler = moveHandler;\n    this._oldZoomEndHandler = zoomEndHandler;\n    var roam = bMapModel.get('roam');\n    if (roam && roam !== 'scale') {\n      bmap.enableDragging();\n    } else {\n      bmap.disableDragging();\n    }\n    if (roam && roam !== 'move') {\n      bmap.enableScrollWheelZoom();\n      bmap.enableDoubleClickZoom();\n      bmap.enablePinchToZoom();\n    } else {\n      bmap.disableScrollWheelZoom();\n      bmap.disableDoubleClickZoom();\n      bmap.disablePinchToZoom();\n    }\n    /* map 2.0 */\n    var originalStyle = bMapModel.__mapStyle;\n    var newMapStyle = bMapModel.get('mapStyle') || {};\n    // FIXME, Not use JSON methods\n    var mapStyleStr = JSON.stringify(newMapStyle);\n    if (JSON.stringify(originalStyle) !== mapStyleStr) {\n      // FIXME May have blank tile when dragging if setMapStyle\n      if (!isEmptyObject(newMapStyle)) {\n        bmap.setMapStyle(echarts.util.clone(newMapStyle));\n      }\n      bMapModel.__mapStyle = JSON.parse(mapStyleStr);\n    }\n    /* map 3.0 */\n    var originalStyle2 = bMapModel.__mapStyle2;\n    var newMapStyle2 = bMapModel.get('mapStyleV2') || {};\n    // FIXME, Not use JSON methods\n    var mapStyleStr2 = JSON.stringify(newMapStyle2);\n    if (JSON.stringify(originalStyle2) !== mapStyleStr2) {\n      // FIXME May have blank tile when dragging if setMapStyle\n      if (!isEmptyObject(newMapStyle2)) {\n        bmap.setMapStyleV2(echarts.util.clone(newMapStyle2));\n      }\n      bMapModel.__mapStyle2 = JSON.parse(mapStyleStr2);\n    }\n    rendering = false;\n  }\n});", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n// @ts-nocheck\n/**\n * BMap component extension\n */\nimport * as echarts from 'echarts';\nimport BMapCoordSys from './BMapCoordSys.js';\nimport './BMapModel.js';\nimport './BMapView.js';\necharts.registerCoordinateSystem('bmap', BMapCoordSys);\n// Action\necharts.registerAction({\n  type: 'bmapRoam',\n  event: 'bmapRoam',\n  update: 'updateLayout'\n}, function (payload, ecModel) {\n  ecModel.eachComponent('bmap', function (bMapModel) {\n    var bmap = bMapModel.getBMap();\n    var center = bmap.getCenter();\n    bMapModel.setCenterAndZoom([center.lng, center.lat], bmap.getZoom());\n  });\n});\nexport var version = '1.0.0';"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AA8CA,SAAS,aAAa,MAAM,KAAK;AAC/B,OAAK,QAAQ;AACb,OAAK,aAAa,CAAC,OAAO,KAAK;AAC/B,OAAK,aAAa,CAAC,GAAG,CAAC;AACvB,OAAK,OAAO;AACZ,OAAK,cAAc,IAAI,KAAK,mBAAmB;AACjD;AACA,aAAa,UAAU,OAAO;AAC9B,aAAa,UAAU,aAAa,CAAC,OAAO,KAAK;AACjD,aAAa,UAAU,UAAU,SAAU,MAAM;AAC/C,OAAK,QAAQ;AACf;AACA,aAAa,UAAU,YAAY,SAAU,QAAQ;AACnD,OAAK,UAAU,KAAK,YAAY,cAAc,IAAI,KAAK,MAAM,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;AACpF;AACA,aAAa,UAAU,eAAe,SAAU,WAAW;AACzD,OAAK,aAAa;AACpB;AACA,aAAa,UAAU,UAAU,WAAY;AAC3C,SAAO,KAAK;AACd;AACA,aAAa,UAAU,cAAc,SAAU,MAAM;AACnD,MAAI,QAAQ,IAAI,KAAK,MAAM,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAU3C,MAAI,KAAK,KAAK,MAAM,oBAAoB,KAAK;AAC7C,MAAI,YAAY,KAAK;AACrB,SAAO,CAAC,GAAG,IAAI,UAAU,CAAC,GAAG,GAAG,IAAI,UAAU,CAAC,CAAC;AAClD;AACA,aAAa,UAAU,cAAc,SAAU,IAAI;AACjD,MAAI,YAAY,KAAK;AACrB,OAAK,KAAK,MAAM,oBAAoB;AAAA,IAClC,GAAG,GAAG,CAAC,IAAI,UAAU,CAAC;AAAA,IACtB,GAAG,GAAG,CAAC,IAAI,UAAU,CAAC;AAAA,EACxB,CAAC;AACD,SAAO,CAAC,GAAG,KAAK,GAAG,GAAG;AACxB;AACA,aAAa,UAAU,cAAc,WAAY;AAC/C,MAAI,MAAM,KAAK;AACf,SAAO,IAAI,gBAAQ,aAAa,GAAG,GAAG,IAAI,SAAS,GAAG,IAAI,UAAU,CAAC;AACvE;AACA,aAAa,UAAU,mBAAmB,WAAY;AACpD,SAAO,eAAO,OAAO;AACvB;AACA,aAAa,UAAU,iBAAiB,WAAY;AAClD,MAAI,OAAO,KAAK,YAAY;AAC5B,SAAO;AAAA,IACL,UAAU;AAAA;AAAA,MAER,MAAM;AAAA,MACN,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,IACf;AAAA,IACA,KAAK;AAAA,MACH,OAAO,aAAO,KAAK,KAAK,aAAa,IAAI;AAAA,MACzC,MAAM,aAAO,KAAK,iBAAiB,IAAI;AAAA,IACzC;AAAA,EACF;AACF;AACA,aAAa,UAAU,iBAAiB,SAAU,SAAS,QAAQ,OAAO;AAExE,SAAO,KAAK,YAAY,KAAK;AAC/B;AACA,aAAa,UAAU,mBAAmB,SAAU,SAAS,QAAQ,OAAO;AAC1E,SAAO,KAAK,YAAY,KAAK;AAC/B;AACA,SAAS,gBAAgB,UAAU,UAAU;AAC3C,aAAW,YAAY,CAAC,GAAG,CAAC;AAC5B,SAAO,aAAO,IAAI,CAAC,GAAG,CAAC,GAAG,SAAU,QAAQ;AAC1C,QAAI,MAAM,SAAS,MAAM;AACzB,QAAI,WAAW,SAAS,MAAM,IAAI;AAClC,QAAI,KAAK,CAAC;AACV,QAAI,KAAK,CAAC;AACV,OAAG,MAAM,IAAI,MAAM;AACnB,OAAG,MAAM,IAAI,MAAM;AACnB,OAAG,IAAI,MAAM,IAAI,GAAG,IAAI,MAAM,IAAI,SAAS,IAAI,MAAM;AACrD,WAAO,KAAK,IAAI,KAAK,YAAY,EAAE,EAAE,MAAM,IAAI,KAAK,YAAY,EAAE,EAAE,MAAM,CAAC;AAAA,EAC7E,GAAG,IAAI;AACT;AACA,IAAI;AAEJ,aAAa,aAAa,aAAa,UAAU;AACjD,SAAS,oBAAoB;AAC3B,WAASA,SAAQ,MAAM;AACrB,SAAK,QAAQ;AAAA,EACf;AACA,EAAAA,SAAQ,YAAY,IAAI,KAAK,QAAQ;AAOrC,EAAAA,SAAQ,UAAU,aAAa,SAAU,KAAK;AAC5C,QAAI,SAAS,EAAE,UAAU,YAAY,KAAK,KAAK;AAC/C,WAAO,KAAK;AAAA,EACd;AAIA,EAAAA,SAAQ,UAAU,OAAO,WAAY;AAAA,EAAC;AACtC,SAAOA;AACT;AACA,aAAa,SAAS,SAAU,SAAS,KAAK;AAC5C,MAAI;AACJ,MAAI,OAAO,IAAI,OAAO;AAEtB,UAAQ,cAAc,QAAQ,SAAU,WAAW;AACjD,QAAI,UAAU,IAAI,MAAM,EAAE;AAC1B,QAAI,eAAe,QAAQ,gBAAgB;AAC3C,QAAI,OAAO,SAAS,aAAa;AAC/B,YAAM,IAAI,MAAM,wBAAwB;AAAA,IAC1C;AACA,cAAU,WAAW,kBAAkB;AACvC,QAAI,cAAc;AAChB,YAAM,IAAI,MAAM,mCAAmC;AAAA,IACrD;AACA,QAAI;AACJ,QAAI,CAAC,UAAU,QAAQ;AAErB,UAAI,WAAW,KAAK,cAAc,oBAAoB;AACtD,UAAI,UAAU;AAGZ,qBAAa,MAAM,OAAO;AAC1B,qBAAa,MAAM,MAAM;AACzB,aAAK,YAAY,QAAQ;AAAA,MAC3B;AACA,iBAAW,SAAS,cAAc,KAAK;AACvC,eAAS,YAAY;AAErB,eAAS,MAAM,UAAU;AACzB,WAAK,YAAY,QAAQ;AAEzB,UAAI,aAAa,UAAU,IAAI,YAAY;AAC3C,UAAI,YAAY;AACd,qBAAa,aAAO,MAAM,UAAU;AAEpC,eAAO,WAAW;AAAA,MACpB;AACA,aAAO,UAAU,SAAS,IAAI,KAAK,IAAI,UAAU,UAAU;AAC3D,UAAI,UAAU,IAAI,QAAQ,YAAY;AACtC,WAAK,WAAW,OAAO;AAEvB,cAAQ,wBAAwB,WAAY;AAC1C,eAAO;AAAA,UACL,YAAY;AAAA,UACZ,WAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF;AACA,WAAO,UAAU;AAGjB,QAAI,SAAS,UAAU,IAAI,QAAQ;AACnC,QAAI,OAAO,UAAU,IAAI,MAAM;AAC/B,QAAI,UAAU,MAAM;AAClB,UAAI,aAAa,KAAK,UAAU;AAChC,UAAI,WAAW,KAAK,QAAQ;AAC5B,UAAI,sBAAsB,UAAU,oBAAoB,CAAC,WAAW,KAAK,WAAW,GAAG,GAAG,QAAQ;AAClG,UAAI,qBAAqB;AACvB,YAAI,KAAK,IAAI,KAAK,MAAM,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAC5C,aAAK,cAAc,IAAI,IAAI;AAAA,MAC7B;AAAA,IACF;AACA,mBAAe,IAAI,aAAa,MAAM,GAAG;AACzC,iBAAa,aAAa,UAAU,eAAe,CAAC,GAAG,CAAC,CAAC;AACzD,iBAAa,QAAQ,IAAI;AACzB,iBAAa,UAAU,MAAM;AAC7B,cAAU,mBAAmB;AAAA,EAC/B,CAAC;AACD,UAAQ,WAAW,SAAU,aAAa;AACxC,QAAI,YAAY,IAAI,kBAAkB,MAAM,QAAQ;AAClD,kBAAY,mBAAmB;AAAA,IACjC;AAAA,EACF,CAAC;AAED,SAAO,gBAAgB,CAAC,YAAY;AACtC;AACA,IAAO,uBAAQ;;;AC7Lf,SAAS,QAAQ,GAAG,GAAG;AACrB,SAAO,KAAK,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC;AAChD;AACA,IAAO,oBAAgB,qBAAqB;AAAA,EAC1C,MAAM;AAAA,EACN,SAAS,WAAY;AAEnB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB,SAAU,QAAQ,MAAM;AACxC,SAAK,OAAO,SAAS;AACrB,SAAK,OAAO,OAAO;AAAA,EACrB;AAAA,EACA,qBAAqB,SAAU,QAAQ,MAAM;AAC3C,QAAI,SAAS,KAAK;AAClB,WAAO,EAAE,QAAQ,QAAQ,OAAO,MAAM,KAAK,SAAS,OAAO;AAAA,EAC7D;AAAA,EACA,eAAe;AAAA,IACb,QAAQ,CAAC,YAAY,SAAS;AAAA,IAC9B,MAAM;AAAA;AAAA,IAEN,UAAU,CAAC;AAAA;AAAA,IAEX,YAAY,CAAC;AAAA;AAAA,IAEb,YAAY,CAAC;AAAA,IACb,MAAM;AAAA,EACR;AACF,CAAC;;;AC5BD,SAAS,cAAc,KAAK;AAC1B,WAAS,OAAO,KAAK;AACnB,QAAI,IAAI,eAAe,GAAG,GAAG;AAC3B,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAO,mBAAgB,oBAAoB;AAAA,EACzC,MAAM;AAAA,EACN,QAAQ,SAAU,WAAW,SAAS,KAAK;AACzC,QAAI,YAAY;AAChB,QAAI,OAAO,UAAU,QAAQ;AAC7B,QAAI,eAAe,IAAI,MAAM,EAAE,QAAQ,gBAAgB;AACvD,QAAI,WAAW,UAAU;AACzB,QAAI,cAAc,SAAU,MAAM,QAAQ;AACxC,UAAI,WAAW;AACb;AAAA,MACF;AACA,UAAI,WAAW,aAAa,WAAW,WAAW;AAClD,UAAI,YAAY,CAAC,CAAC,SAAS,SAAS,MAAM,MAAM,EAAE,KAAK,GAAG,CAAC,SAAS,SAAS,MAAM,KAAK,EAAE,KAAK,CAAC;AAEhG,UAAI,oBAAoB,aAAa;AACrC,UAAI,aAAa,UAAU,CAAC,IAAI;AAChC,UAAI,YAAY,UAAU,CAAC,IAAI;AAC/B,UAAI,kBAAkB,SAAS,YAAY;AACzC,0BAAkB,OAAO;AAAA,MAC3B;AACA,UAAI,kBAAkB,QAAQ,WAAW;AACvC,0BAAkB,MAAM;AAAA,MAC1B;AACA,eAAS,aAAa,SAAS;AAC/B,gBAAU,cAAc;AACxB,UAAI,eAAe;AAAA,QACjB,MAAM;AAAA,QACN,WAAW;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,MACF,CAAC;AAAA,IACH;AACA,aAAS,iBAAiB;AACxB,UAAI,WAAW;AACb;AAAA,MACF;AACA,UAAI,eAAe;AAAA,QACjB,MAAM;AAAA,QACN,WAAW;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,MACF,CAAC;AAAA,IACH;AACA,SAAK,oBAAoB,UAAU,KAAK,eAAe;AACvD,SAAK,oBAAoB,WAAW,KAAK,eAAe;AACxD,SAAK,oBAAoB,WAAW,KAAK,kBAAkB;AAC3D,SAAK,iBAAiB,UAAU,WAAW;AAC3C,SAAK,iBAAiB,WAAW,WAAW;AAC5C,SAAK,iBAAiB,WAAW,cAAc;AAC/C,SAAK,kBAAkB;AACvB,SAAK,qBAAqB;AAC1B,QAAI,OAAO,UAAU,IAAI,MAAM;AAC/B,QAAI,QAAQ,SAAS,SAAS;AAC5B,WAAK,eAAe;AAAA,IACtB,OAAO;AACL,WAAK,gBAAgB;AAAA,IACvB;AACA,QAAI,QAAQ,SAAS,QAAQ;AAC3B,WAAK,sBAAsB;AAC3B,WAAK,sBAAsB;AAC3B,WAAK,kBAAkB;AAAA,IACzB,OAAO;AACL,WAAK,uBAAuB;AAC5B,WAAK,uBAAuB;AAC5B,WAAK,mBAAmB;AAAA,IAC1B;AAEA,QAAI,gBAAgB,UAAU;AAC9B,QAAI,cAAc,UAAU,IAAI,UAAU,KAAK,CAAC;AAEhD,QAAI,cAAc,KAAK,UAAU,WAAW;AAC5C,QAAI,KAAK,UAAU,aAAa,MAAM,aAAa;AAEjD,UAAI,CAAC,cAAc,WAAW,GAAG;AAC/B,aAAK,YAAoB,aAAK,MAAM,WAAW,CAAC;AAAA,MAClD;AACA,gBAAU,aAAa,KAAK,MAAM,WAAW;AAAA,IAC/C;AAEA,QAAI,iBAAiB,UAAU;AAC/B,QAAI,eAAe,UAAU,IAAI,YAAY,KAAK,CAAC;AAEnD,QAAI,eAAe,KAAK,UAAU,YAAY;AAC9C,QAAI,KAAK,UAAU,cAAc,MAAM,cAAc;AAEnD,UAAI,CAAC,cAAc,YAAY,GAAG;AAChC,aAAK,cAAsB,aAAK,MAAM,YAAY,CAAC;AAAA,MACrD;AACA,gBAAU,cAAc,KAAK,MAAM,YAAY;AAAA,IACjD;AACA,gBAAY;AAAA,EACd;AACF,CAAC;;;AC9FO,yBAAyB,QAAQ,oBAAY;AAE7C,eAAe;AAAA,EACrB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AACV,GAAG,SAAU,SAAS,SAAS;AAC7B,UAAQ,cAAc,QAAQ,SAAU,WAAW;AACjD,QAAI,OAAO,UAAU,QAAQ;AAC7B,QAAI,SAAS,KAAK,UAAU;AAC5B,cAAU,iBAAiB,CAAC,OAAO,KAAK,OAAO,GAAG,GAAG,KAAK,QAAQ,CAAC;AAAA,EACrE,CAAC;AACH,CAAC;AACM,IAAI,UAAU;", "names": ["Overlay"]}