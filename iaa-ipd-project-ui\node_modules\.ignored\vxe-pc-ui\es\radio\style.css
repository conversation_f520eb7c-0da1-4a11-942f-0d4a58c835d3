/*radio-group*/
.vxe-radio-group {
  display: inline-block;
  vertical-align: middle;
  line-height: 1;
  font-size: 0;
}
.vxe-radio-group + .vxe-radio-group {
  margin-left: 10px;
}

.vxe-radio--readonly {
  color: var(--vxe-ui-font-color);
  display: inline-flex;
}

/*radio*/
.vxe-radio {
  display: inline-block;
  vertical-align: middle;
  white-space: nowrap;
  line-height: 1;
  cursor: pointer;
}
.vxe-radio .vxe-radio--icon {
  font-size: 1.26em;
}
.vxe-radio .vxe-radio--icon {
  color: var(--vxe-ui-input-border-color);
  vertical-align: middle;
  font-weight: 700;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-radio.is--checked {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-radio.is--checked .vxe-radio--icon {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-radio:not(.is--disabled) {
  cursor: pointer;
}
.vxe-radio:not(.is--disabled):hover .vxe-radio--icon {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-radio.is--disabled {
  color: var(--vxe-ui-font-disabled-color);
  cursor: not-allowed;
}
.vxe-radio.is--disabled .vxe-radio--icon {
  color: var(--vxe-ui-input-disabled-color);
}
.vxe-radio .vxe-radio--label {
  padding-left: 0.5em;
  vertical-align: middle;
}
.vxe-radio > input[type=radio] {
  position: absolute;
  width: 0;
  height: 0;
  border: 0;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.vxe-radio .vxe-radio--label {
  vertical-align: middle;
  display: inline-block;
  max-width: 50em;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.vxe-radio:not(.is--disabled) > input:focus + .vxe-radio--icon {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-radio:not(.vxe-radio-button) + .vxe-radio {
  margin-left: 10px;
}

.vxe-radio-button .vxe-radio--label {
  background-color: var(--vxe-ui-layout-background-color);
}
.vxe-radio-button:first-child .vxe-radio--label {
  border-left: 1px solid var(--vxe-ui-input-border-color);
  border-radius: var(--vxe-ui-base-border-radius) 0 0 var(--vxe-ui-base-border-radius);
}
.vxe-radio-button:last-child .vxe-radio--label {
  border-radius: 0 var(--vxe-ui-base-border-radius) var(--vxe-ui-base-border-radius) 0;
}
.vxe-radio-button > input:checked + .vxe-radio--label {
  color: #fff;
  background-color: var(--vxe-ui-font-primary-color);
  border-color: var(--vxe-ui-font-primary-color);
}
.vxe-radio-button .vxe-radio--label {
  padding: 0 1em;
  line-height: calc(var(--vxe-ui-button-height-default) - 2px);
  display: inline-block;
  border-style: solid;
  border-color: var(--vxe-ui-input-border-color);
  border-width: 1px 1px 1px 0;
  max-width: 50em;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.vxe-radio-button.is--disabled {
  cursor: not-allowed;
}
.vxe-radio-button.is--disabled > input:not(:checked) + .vxe-radio--label {
  color: var(--vxe-ui-input-disabled-color);
}
.vxe-radio-button.is--disabled > input:checked + .vxe-radio--label {
  border-color: var(--vxe-ui-font-primary-lighten-color);
  background-color: var(--vxe-ui-font-primary-lighten-color);
}
.vxe-radio-button:not(.is--disabled) > input:focus + .vxe-radio--label {
  border-color: var(--vxe-ui-font-primary-color);
  box-shadow: 0 0 0.2em 0 var(--vxe-ui-font-primary-color);
}
.vxe-radio-button:not(.is--disabled):hover > input:not(:checked) + .vxe-radio--label {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-radio-button.size--medium .vxe-radio--label {
  line-height: calc(var(--vxe-ui-button-height-medium) - 2px);
}
.vxe-radio-button.size--small .vxe-radio--label {
  line-height: calc(var(--vxe-ui-button-height-small) - 2px);
}
.vxe-radio-button.size--mini .vxe-radio--label {
  line-height: calc(var(--vxe-ui-button-height-mini) - 2px);
}

.vxe-radio {
  font-size: var(--vxe-ui-font-size-default);
}
.vxe-radio.size--medium {
  font-size: var(--vxe-ui-font-size-medium);
}
.vxe-radio.size--small {
  font-size: var(--vxe-ui-font-size-small);
}
.vxe-radio.size--mini {
  font-size: var(--vxe-ui-font-size-mini);
}