Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var _vue=require("vue"),_comp=require("../../ui/src/comp"),_xeUtils=_interopRequireDefault(require("xe-utils")),_dom=require("../../ui/src/dom"),_utils=require("../../ui/src/utils"),_ui=require("../../ui"),_cell=_interopRequireDefault(require("./cell")),_body=_interopRequireDefault(require("./body")),_header=_interopRequireDefault(require("./header")),_footer=_interopRequireDefault(require("./footer")),_props=_interopRequireDefault(require("./props")),_emits=_interopRequireDefault(require("./emits")),_util=require("./util"),_vn=require("../../ui/src/vn"),_log=require("../../ui/src/log"),_panel=_interopRequireDefault(require("../module/custom/panel")),_panel2=_interopRequireDefault(require("../module/filter/panel")),_importPanel=_interopRequireDefault(require("../module/export/import-panel")),_exportPanel=_interopRequireDefault(require("../module/export/export-panel")),_panel3=_interopRequireDefault(require("../module/menu/panel"));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let{getConfig,getIcon,getI18n,renderer,formats,createEvent,globalResize,interceptor,hooks,globalEvents,GLOBAL_EVENT_KEYS,useFns,renderEmptyElement}=_ui.VxeUI,supportMaxRow=5e6,customStorageKey="VXE_CUSTOM_STORE",maxYHeight=5e6,maxXWidth=5e6;var _default=exports.default=(0,_comp.defineVxeComponent)({name:"VxeTable",props:_props.default,emits:_emits.default,setup(W,e){let{slots:ue,emit:r}=e,se=_xeUtils.default.uniqueId(),ce=_xeUtils.default.browse(),ge=_ui.VxeUI.getComponent("VxeLoading"),ve=_ui.VxeUI.getComponent("VxeTooltip"),l=(0,_vue.inject)("$xeTabs",null),he=useFns.useSize(W).computeSize,N=(0,_vue.reactive)({staticColumns:[],tableGroupColumn:[],tableColumn:[],tableData:[],scrollXLoad:!1,scrollYLoad:!1,overflowY:!0,overflowX:!1,scrollbarWidth:0,scrollbarHeight:0,lastScrollTime:0,rowHeight:0,parentHeight:0,isGroup:!1,isAllOverflow:!1,isAllSelected:!1,isIndeterminate:!1,currentRow:null,currentColumn:null,selectRadioRow:null,footerTableData:[],rowGroupColumn:null,expandColumn:null,treeNodeColumn:null,hasFixedColumn:!1,upDataFlag:0,reColumnFlag:0,initStore:{filter:!1,import:!1,export:!1,custom:!1},customStore:{btnEl:null,isAll:!1,isIndeterminate:!1,activeBtn:!1,activeWrapper:!1,visible:!1,maxHeight:0,oldSortMaps:{},oldFixedMaps:{},oldVisibleMaps:{}},customColumnList:[],filterStore:{isAllSelected:!1,isIndeterminate:!1,style:null,options:[],column:null,multiple:!1,visible:!1,maxHeight:null},columnStore:{leftList:[],centerList:[],rightList:[],resizeList:[],pxList:[],pxMinList:[],autoMinList:[],scaleList:[],scaleMinList:[],autoList:[],remainList:[]},ctxMenuStore:{selected:null,visible:!1,showChild:!1,selectChild:null,list:[],style:null},editStore:{indexs:{columns:[]},titles:{columns:[]},selected:{row:null,column:null},copyed:{cut:!1,rows:[],columns:[]},actived:{row:null,column:null},focused:{row:null,column:null}},tooltipStore:{row:null,column:null,content:null,visible:!1,currOpts:{}},validStore:{visible:!1},validErrorMaps:{},importStore:{inited:!1,file:null,type:"",modeList:[],typeList:[],filename:"",visible:!1},importParams:{mode:"",types:null,message:!0},exportStore:{inited:!1,name:"",modeList:[],typeList:[],columns:[],isPrint:!1,hasFooter:!1,hasMerge:!1,hasTree:!1,hasColgroup:!1,visible:!1},exportParams:{filename:"",sheetName:"",mode:"",type:"",isColgroup:!1,isMerge:!1,isAllExpand:!1,useStyle:!1,original:!1,message:!0,isHeader:!1,isTitle:!1,isFooter:!1},visiblwRowsFlag:1,isRowGroupStatus:!1,rowGroupList:[],aggHandleFields:[],rowGroupExpandedFlag:1,rowExpandedFlag:1,treeExpandedFlag:1,updateCheckboxFlag:1,pendingRowFlag:1,insertRowFlag:1,removeRowFlag:1,mergeBodyFlag:1,mergeFootFlag:1,rowHeightStore:{large:52,default:48,medium:44,small:40,mini:36},scrollVMLoading:!1,scrollYHeight:0,scrollYTop:0,isScrollYBig:!1,scrollXLeft:0,scrollXWidth:0,isScrollXBig:!1,rowExpandHeightFlag:1,calcCellHeightFlag:1,resizeHeightFlag:1,resizeWidthFlag:1,isCustomStatus:!1,isDragRowMove:!1,dragRow:null,isDragColMove:!1,dragCol:null,dragTipText:"",isDragResize:!1,isRowLoading:!1,isColLoading:!1}),G={tZindex:0,currKeyField:"",isCurrDeepKey:!1,elemStore:{},scrollXStore:{preloadSize:0,offsetSize:0,visibleSize:0,visibleStartIndex:0,visibleEndIndex:0,startIndex:0,endIndex:0},scrollYStore:{preloadSize:0,offsetSize:0,visibleSize:0,visibleStartIndex:0,visibleEndIndex:0,startIndex:0,endIndex:0},tableWidth:0,tableHeight:0,headerHeight:0,footerHeight:0,customHeight:0,customMinHeight:0,customMaxHeight:0,hoverRow:null,lastScrollLeft:0,lastScrollTop:0,radioReserveRow:null,checkboxReserveRowMap:{},rowExpandedReserveRowMap:{},treeExpandedReserveRowMap:{},treeIndeterminateRowMaps:{},tableFullData:[],afterFullData:[],afterTreeFullData:[],afterGroupFullData:[],afterFullRowMaps:{},tableFullTreeData:[],tableFullGroupData:[],tableSynchData:[],tableSourceData:[],collectColumn:[],tableFullColumn:[],visibleColumn:[],fullAllDataRowIdData:{},fullDataRowIdData:{},visibleDataRowIdData:{},sourceDataRowIdData:{},fullColumnIdData:{},fullColumnFieldData:{},mergeBodyList:[],mergeBodyMaps:{},mergeFooterList:[],mergeFooterMaps:{},mergeBodyCellMaps:{},mergeFooterCellMaps:{},rowExpandedMaps:{},rowExpandLazyLoadedMaps:{},rowGroupExpandedMaps:{},treeExpandedMaps:{},treeExpandLazyLoadedMaps:{},selectCheckboxMaps:{},pendingRowMaps:{},insertRowMaps:{},removeRowMaps:{},cvCacheMaps:{},inited:!1,tooltipTimeout:null,initStatus:!1,isActivated:!1},$={},V={},Y=(0,_vue.ref)(),fe=(0,_vue.ref)(),q=(0,_vue.ref)(),pe=(0,_vue.ref)(),me=(0,_vue.ref)(),xe=(0,_vue.ref)(),_e=(0,_vue.ref)(),we=(0,_vue.ref)(),u=(0,_vue.ref)(),s=(0,_vue.ref)(),m=(0,_vue.ref)(),c=(0,_vue.ref)(),g=(0,_vue.ref)(),v=(0,_vue.ref)(),h=(0,_vue.ref)(),f=(0,_vue.ref)(),p=(0,_vue.ref)(),x=(0,_vue.ref)(),P=(0,_vue.ref)(),X=(0,_vue.ref)(),Ce=(0,_vue.ref)(),be=(0,_vue.ref)(),Re=(0,_vue.ref)(),Ee=(0,_vue.ref)(),ye=(0,_vue.ref)(),Te=(0,_vue.ref)(),j=(0,_vue.ref)(),A=(0,_vue.ref)(),Se=(0,_vue.ref)(),De=(0,_vue.ref)(),T=(0,_vue.ref)(),Fe=(0,_vue.ref)(),Ie=(0,_vue.ref)(),S=(0,_vue.ref)(),Me=(0,_vue.ref)(),Le=(0,_vue.ref)(),ke=(0,_vue.ref)(),Ue=(0,_vue.ref)(),w=(0,_vue.ref)(),H=(0,_vue.ref)(),K=(0,_vue.inject)("$xeGrid",null),b,R=(0,_vue.computed)(()=>{var e=W.id;return e?_xeUtils.default.isFunction(e)?""+(e({$table:ne,$grid:K})||""):""+e:""}),t=(0,_vue.computed)(()=>{var e=J.value;return""+(W.rowId||e.keyField||"_X_ROW_KEY")}),Ae=(0,_vue.computed)(()=>Object.assign({},getConfig().table.validConfig,W.validConfig));var a=(0,_vue.computed)(()=>C.value);let O=(0,_vue.computed)(()=>{var e=C.value.threshold;return e?_xeUtils.default.toNumber(e):0});var o=(0,_vue.computed)(()=>y.value);let C=(0,_vue.computed)(()=>Object.assign({},getConfig().table.virtualXConfig||getConfig().table.scrollX,W.virtualXConfig||W.scrollX)),y=(0,_vue.computed)(()=>Object.assign({},getConfig().table.virtualYConfig||getConfig().table.scrollY,W.virtualYConfig||W.scrollY)),E=(0,_vue.computed)(()=>Object.assign({},getConfig().table.scrollbarConfig,W.scrollbarConfig)),He=(0,_vue.computed)(()=>{var e=E.value;return!(!e.x||"top"!==e.x.position)}),Oe=(0,_vue.computed)(()=>{var e=E.value;return!(!e.y||"left"!==e.y.position)}),z=(0,_vue.computed)(()=>{var e=y.value.threshold;return e?_xeUtils.default.toNumber(e):0}),i=(0,_vue.computed)(()=>N.rowHeightStore),D=(0,_vue.computed)(()=>{var e=he.value;return i.value[e||"default"]||18}),Z=(0,_vue.computed)(()=>Object.assign({},getConfig().table.columnConfig,W.columnConfig)),B=(0,_vue.computed)(()=>Object.assign({},getConfig().table.currentColumnConfig,W.currentColumnConfig)),F=(0,_vue.computed)(()=>{var e=Object.assign({},getConfig().table.cellConfig,W.cellConfig);return e.height&&(e.height=_xeUtils.default.toNumber(e.height)),e});var d=(0,_vue.computed)(()=>{var e=Object.assign({},getConfig().table.headerCellConfig,W.headerCellConfig),l=F.value;return e.height=_xeUtils.default.toNumber((0,_util.getCellHeight)(e.height||l.height)),e}),ze=(0,_vue.computed)(()=>{var e=Object.assign({},getConfig().table.footerCellConfig,W.footerCellConfig),l=F.value;return e.height=_xeUtils.default.toNumber((0,_util.getCellHeight)(e.height||l.height)),e});let J=(0,_vue.computed)(()=>Object.assign({},getConfig().table.rowConfig,W.rowConfig)),I=(0,_vue.computed)(()=>Object.assign({},getConfig().table.aggregateConfig||getConfig().table.rowGroupConfig,W.aggregateConfig||W.rowGroupConfig)),Be=(0,_vue.computed)(()=>I.value),Pe=(0,_vue.computed)(()=>Object.assign({},getConfig().table.currentRowConfig,W.currentRowConfig)),Q=(0,_vue.computed)(()=>Object.assign({},getConfig().table.rowDragConfig,W.rowDragConfig)),ee=(0,_vue.computed)(()=>Object.assign({},getConfig().table.columnDragConfig,W.columnDragConfig)),Ge=(0,_vue.computed)(()=>Object.assign({},getConfig().table.resizeConfig,W.resizeConfig)),$e=(0,_vue.computed)(()=>Object.assign({},getConfig().table.resizableConfig,W.resizableConfig));var We=(0,_vue.computed)(()=>Object.assign({startIndex:0},getConfig().table.seqConfig,W.seqConfig));let M=(0,_vue.computed)(()=>Object.assign({},getConfig().table.radioConfig,W.radioConfig)),le=(0,_vue.computed)(()=>Object.assign({},getConfig().table.checkboxConfig,W.checkboxConfig)),Ne=(0,_vue.computed)(()=>Object.assign({},getConfig().tooltip,getConfig().table.tooltipConfig,W.tooltipConfig)),Ve=(0,_vue.computed)(()=>{var e=N.tooltipStore,l=Ne.value;return Object.assign({},l,e.currOpts)}),Ye=(0,_vue.computed)(()=>{var e=Ne.value;return Object.assign({},e)}),te=(0,_vue.computed)(()=>Object.assign({},getConfig().table.editConfig,W.editConfig)),L=(0,_vue.computed)(()=>Object.assign({orders:["asc","desc",null]},getConfig().table.sortConfig,W.sortConfig)),qe=(0,_vue.computed)(()=>Object.assign({},getConfig().table.filterConfig,W.filterConfig)),re=(0,_vue.computed)(()=>Object.assign({},getConfig().table.mouseConfig,W.mouseConfig)),Xe=(0,_vue.computed)(()=>Object.assign({},getConfig().table.areaConfig,W.areaConfig)),ae=(0,_vue.computed)(()=>Object.assign({},getConfig().table.keyboardConfig,W.keyboardConfig));var je=(0,_vue.computed)(()=>Object.assign({},getConfig().table.clipConfig,W.clipConfig)),Ke=(0,_vue.computed)(()=>Object.assign({},getConfig().table.fnrConfig,W.fnrConfig));let Ze=(0,_vue.computed)(()=>Object.assign({},getConfig().table.menuConfig,W.menuConfig)),Je=(0,_vue.computed)(()=>{var e=N.columnStore,l=e.leftList;let t=0;for(let e=0;e<l.length;e++){var r=l[e];t+=r.renderWidth}return t}),Qe=(0,_vue.computed)(()=>{var e=N.columnStore,l=e.rightList;let t=0;for(let e=0;e<l.length;e++){var r=l[e];t+=r.renderWidth}return t}),el=(0,_vue.computed)(()=>{var e=Ze.value.header;return e&&e.options?e.options:[]}),ll=(0,_vue.computed)(()=>{var e=Ze.value.body;return e&&e.options?e.options:[]}),tl=(0,_vue.computed)(()=>{var e=Ze.value.footer;return e&&e.options?e.options:[]}),rl=(0,_vue.computed)(()=>{var e=Ze.value,l=el.value,t=ll.value,r=tl.value;return!!(W.menuConfig&&(0,_utils.isEnableConf)(e)&&(l.length||t.length||r.length))}),al=(0,_vue.computed)(()=>{var e=N.ctxMenuStore;let l=[];return e.list.forEach(e=>{e.forEach(e=>{l.push(e)})}),l}),ol=(0,_vue.computed)(()=>Object.assign({},getConfig().table.exportConfig,W.exportConfig)),il=(0,_vue.computed)(()=>Object.assign({},getConfig().table.importConfig,W.importConfig));var nl=(0,_vue.computed)(()=>Object.assign({},getConfig().table.printConfig,W.printConfig));let oe=(0,_vue.computed)(()=>Object.assign({},getConfig().table.expandConfig,W.expandConfig)),ie=(0,_vue.computed)(()=>Object.assign({},getConfig().table.treeConfig,W.treeConfig)),dl=(0,_vue.computed)(()=>Object.assign({},getConfig().table.emptyRender,W.emptyRender)),ul=(0,_vue.computed)(()=>Object.assign({},getConfig().table.loadingConfig,W.loadingConfig));var sl=(0,_vue.computed)(()=>W.border?Math.max(2,Math.ceil(N.scrollbarWidth/N.tableColumn.length)):1);let cl=(0,_vue.computed)(()=>Object.assign({},getConfig().table.customConfig,W.customConfig)),gl=(0,_vue.computed)(()=>{var{rowExpandedFlag:e,expandColumn:l,rowGroupExpandedFlag:t,treeExpandedFlag:r}=N;let{visibleDataRowIdData:a,rowExpandedMaps:o}=G,i=[];return l&&e&&t&&r&&_xeUtils.default.each(o,(e,l)=>{a[l]&&i.push(e)}),i}),vl=(0,_vue.computed)(()=>{var e=G.visibleColumn,l=N.tableColumn;return l.length||e.length?e.filter(e=>"auto"===e.width||"auto"===e.minWidth):[]}),hl=(0,_vue.computed)(()=>{var e=N.tableColumn,l=G.collectColumn;let t=0;return e.length&&l.length&&l.forEach(e=>{e.renderFixed&&t++}),t}),fl=(0,_vue.computed)(()=>{var e=hl.value,l=Z.value.maxFixedSize;return!!l&&l<=e}),pl=(0,_vue.computed)(()=>{var e=W.border;return!0===e?"full":e||"default"});var ml=(0,_vue.computed)(()=>{var{}=W,e=N.tableData,l=G.tableFullData;let{strict:t,checkMethod:r}=le.value;return!!t&&(!e.length&&!l.length||!!r&&l.every(e=>!r({$table:ne,row:e})))}),xl=(0,_vue.computed)(()=>{var{overflowX:e,scrollXLoad:l,overflowY:t,scrollYLoad:r}=N;return{x:e&&l,y:t&&r}}),_l=(0,_vue.computed)(()=>Be.value.groupFields),wl=(0,_vue.computed)(()=>{var e=N.rowGroupList;let l=G.fullColumnFieldData,t=[];return e.forEach(e=>{e=l[e.field];e&&t.push(e.column)}),t});let Cl={refElem:Y,refTooltip:q,refValidTooltip:me,refTableFilter:_e,refTableCustom:we,refTableMenu:xe,refTableHeader:s,refTableBody:m,refTableFooter:c,refTableLeftHeader:g,refTableLeftBody:v,refTableLeftFooter:h,refTableRightHeader:f,refTableRightBody:p,refTableRightFooter:x,refLeftContainer:P,refRightContainer:X,refColResizeBar:Ce,refRowResizeBar:be,refScrollXVirtualElem:Se,refScrollYVirtualElem:De,refScrollXHandleElem:T,refScrollYHandleElem:S,refScrollXSpaceElem:w,refScrollYSpaceElem:H},bl={computeSize:he,computeTableId:R,computeValidOpts:Ae,computeRowField:t,computeVirtualXOpts:C,computeVirtualYOpts:y,computeScrollbarOpts:E,computeScrollbarXToTop:He,computeScrollbarYToLeft:Oe,computeColumnOpts:Z,computeCurrentColumnOpts:B,computeScrollXThreshold:O,computeScrollYThreshold:z,computeRowHeightMaps:i,computeDefaultRowHeight:D,computeCellOpts:F,computeHeaderCellOpts:d,computeFooterCellOpts:ze,computeRowOpts:J,computeAggregateOpts:I,computeRowGroupOpts:Be,computeCurrentRowOpts:Pe,computeRowDragOpts:Q,computeColumnDragOpts:ee,computeResizeOpts:Ge,computeResizableOpts:$e,computeSeqOpts:We,computeRadioOpts:M,computeCheckboxOpts:le,computeTooltipOpts:Ne,computeEditOpts:te,computeSortOpts:L,computeFilterOpts:qe,computeMouseOpts:re,computeAreaOpts:Xe,computeKeyboardOpts:ae,computeClipOpts:je,computeFNROpts:Ke,computeHeaderMenu:el,computeBodyMenu:ll,computeFooterMenu:tl,computeIsMenu:rl,computeMenuList:al,computeMenuOpts:Ze,computeExportOpts:ol,computeImportOpts:il,computePrintOpts:nl,computeExpandOpts:oe,computeTreeOpts:ie,computeEmptyOpts:dl,computeLoadingOpts:ul,computeCellOffsetWidth:sl,computeCustomOpts:cl,computeLeftFixedWidth:Je,computeRightFixedWidth:Qe,computeFixedColumnSize:hl,computeIsMaxFixedColumn:fl,computeIsAllCheckboxDisabled:ml,computeVirtualScrollBars:xl,computeRowGroupFields:_l,computeRowGroupColumns:wl,computeSXOpts:a,computeSYOpts:o},ne={xID:se,props:W,context:e,reactData:N,internalData:G,getRefMaps:()=>Cl,getComputeMaps:()=>bl,xeGrid:K,xegrid:K},Rl=(e,l,t)=>{e=_xeUtils.default.get(e,t),l=_xeUtils.default.get(l,t);return!(!(0,_utils.eqEmptyValue)(e)||!(0,_utils.eqEmptyValue)(l))||(_xeUtils.default.isString(e)||_xeUtils.default.isNumber(e)?""+e==""+l:_xeUtils.default.isEqual(e,l))},El=()=>{var e=t.value;G.currKeyField=e,G.isCurrDeepKey=(0,_util.hasDeepKey)(e)},_=(e,l)=>_xeUtils.default.isBoolean(e)?e:l,yl=e=>{var{orders:l=[]}=L.value,e=e.order||null,e=l.indexOf(e)+1;return l[e<l.length?e:0]},Tl=e=>{var l=getConfig().version,t=_xeUtils.default.toStringJSON(localStorage.getItem(customStorageKey)||""),t=t&&t._v===l?t:{_v:l};return(e?t[e]:t)||{}},Sl=e=>{let t=G.fullAllDataRowIdData,r={};return _xeUtils.default.each(e,(e,l)=>{t[l]&&(r[l]=e)}),r},Dl=e=>{let t=G.fullDataRowIdData,r=[];return _xeUtils.default.each(e,(e,l)=>{t[l]&&-1===ne.findRowIndexOf(r,t[l].row)&&r.push(t[l].row)}),r},Fl=()=>{var{isScrollXBig:a,scrollXWidth:o}=N,{elemStore:i,visibleColumn:n,fullColumnIdData:d}=G,u=Je.value,s=Qe.value,i=(0,_util.getRefElem)(i["main-body-scroll"]);if(i){var c=i.clientWidth;let e=i.scrollLeft;var g=(e=a?Math.ceil((o-c)*Math.min(1,e/(maxXWidth-c))):e)+u,v=e+c-s;let l=0,t=n.length;for(;l<t;){var h=Math.floor((l+t)/2);(d[n[h].id]||{}).oLeft<=g?l=h+1:t=h}let r=0;i=l===n.length?l:Math.max(0,l<n.length?l-2:0);for(let e=i,l=n.length;e<l;e++){var f=d[n[e].id]||{};if(r++,f.oLeft>v||60<=r)break}return{toVisibleIndex:Math.max(0,i),visibleSize:Math.max(1,r)}}return{toVisibleIndex:0,visibleSize:6}},Il=(e,l)=>{var t=N.rowHeightStore;l&&l.clientHeight&&(t[e]=l.clientHeight)},Ml=()=>{var l=N.isAllOverflow,t=s.value,r=m.value,r=r?r.$el:null,e=D.value;let a=0;if(l){if(r){l=t?t.$el:null;let e;(e=!(e=r.querySelector("tr"))&&l?l.querySelector("tr"):e)&&(a=e.clientHeight)}a=a||e}else a=e;return Math.max(18,a)},Ll=()=>{var{isAllOverflow:l,expandColumn:t,isScrollYBig:o,scrollYHeight:i}=N,{elemStore:n,isResizeCellHeight:d,afterFullData:u,fullAllDataRowIdData:s}=G,c=J.value,g=F.value,v=D.value,n=(0,_util.getRefElem)(n["main-body-scroll"]);if(n){var h=n.clientHeight;let e=n.scrollTop;var f=e=o?Math.ceil((i-h)*Math.min(1,e/(maxYHeight-h))):e,p=e+h;let r=-1,a=0;if(d||g.height||c.height||t||!l){var m=(0,_util.createHandleGetRowId)(ne).handleGetRowId;let t=0,e=u.length;for(;t<e;){var x=Math.floor((t+e)/2);(s[m(u[x])]||{}).oTop<=f?t=x+1:e=x}for(let e=r=t===u.length?t:Math.max(0,t<u.length?t-2:0),l=u.length;e<l;e++){var _=s[m(u[e])]||{};if(a++,_.oTop>p||100<=a)break}}else r=Math.floor(f/v)-1,a=Math.ceil(h/v)+1;return{toVisibleIndex:Math.max(0,r),visibleSize:Math.max(6,a)}}return{toVisibleIndex:0,visibleSize:6}},kl=(t,r,a)=>{for(let e=0,l=t.length;e<l;e++){var o=t[e],{startIndex:i,endIndex:n}=r,d=o[a],o=d+o[a+"span"];d<i&&i<o&&(r.startIndex=d),d<n&&n<o&&(r.endIndex=o),r.startIndex===i&&r.endIndex===n||(e=-1)}};function Ul(l){var t={};if(l&&l.length)for(let e=0;e<l.length;e++){var{row:r,col:a,rowspan:o,colspan:i}=l[e];for(let l=0;l<o;l++)for(let e=0;e<i;e++)t[r+l+":"+(a+e)]=l||e?{rowspan:0,colspan:0}:{rowspan:o,colspan:i}}return t}let Al=(e,l,t)=>{let{multiple:r,remote:a,orders:i}=L.value;if((l=_xeUtils.default.isArray(l)?l:[l])&&l.length){r||(l=[l[0]],Hl());let o=null;return l.forEach((e,l)=>{let{field:t,order:r}=e,a=t;_xeUtils.default.isString(t)&&(a=ne.getColumnByField(t)),o=o||a,a&&a.sortable&&(i&&-1===i.indexOf(r)&&(r=yl(a)),a.order!==r&&(a.order=r),a.sortTime=Date.now()+l)}),t&&!a&&ne.handleTableData(!0),e&&ne.handleColumnSortEvent(e,o),(0,_vue.nextTick)().then(()=>(U(),ne.updateCellAreas(),k()))}return(0,_vue.nextTick)()},Hl=()=>{var e=G.tableFullColumn;e.forEach(e=>{e.order=null})},Ol=e=>{var l,t=N.parentHeight,e=W[e];let r=0;return r=e?"100%"===e||"auto"===e?t:(l=ne.getExcludeHeight(),r=(0,_dom.isScale)(e)?Math.floor((_xeUtils.default.toInteger(e)||1)/100*t):_xeUtils.default.toNumber(e),Math.max(40,r-l)):r},zl=e=>{var l=G.collectColumn;let{resizableData:i,sortData:n,visibleData:d,fixedData:u}=e,s=!1;i||n||d||u?(_xeUtils.default.eachTree(l,(e,l,t,r,a)=>{var o=e.getKey();a||(u&&void 0!==u[o]&&(e.fixed=u[o]),n&&_xeUtils.default.isNumber(n[o])&&(s=!0,e.renderSortNumber=n[o])),i&&_xeUtils.default.isNumber(i[o])&&(e.resizeWidth=i[o]),d&&_xeUtils.default.isBoolean(d[o])&&(e.visible=d[o])}),s&&(l=_xeUtils.default.orderBy(l,"renderSortNumber"),G.collectColumn=l,G.tableFullColumn=mt(l)),N.isCustomStatus=!0):N.isCustomStatus=!1},Bl=()=>{var{tableFullColumn:e,collectColumn:l}=G;let c=G.fullColumnIdData={},g=G.fullColumnFieldData={};var t=re.value,r=oe.value;let v=Z.value;var a=ee.value,o=y.value;let{isCrossDrag:h,isSelfToChildDrag:f}=a,p=cl.value.storage;var a=J.value,i=l.some(_utils.hasChildrenList);let m=!!W.showOverflow,x,_,w,C,b,R,E,n=(e,l,t,r,a)=>{var{id:o,field:i,fixed:n,type:d,treeNode:u,rowGroupNode:s}=e,l={$index:-1,_index:-1,column:e,colid:o,index:l,items:t,parent:a||null,width:0,oLeft:0};i?(g[i]&&(0,_log.errLog)("vxe.error.colRepet",["field",i]),g[i]=l):(p&&!d||v.drag&&(h||f))&&(0,_log.errLog)("vxe.error.reqProp",[`${e.getTitle()||d||""} -> column.field=?`]),!E&&n&&(E=n),R||"html"!==d||(R=e),u&&(w&&(0,_log.warnLog)("vxe.error.colRepet",["tree-node",u]),w=w||e),s&&(w&&(0,_log.warnLog)("vxe.error.colRepet",["row-group-node",s]),x=x||e),"expand"===d&&(_&&(0,_log.warnLog)("vxe.error.colRepet",["type",d]),_=_||e),"checkbox"===d?(C&&(0,_log.warnLog)("vxe.error.colRepet",["type",d]),C=C||e):"radio"===d&&(b&&(0,_log.warnLog)("vxe.error.colRepet",["type",d]),b=b||e),m&&!1===e.showOverflow&&(m=!1),c[o]&&(0,_log.errLog)("vxe.error.colRepet",["colId",o]),c[o]=l};i?_xeUtils.default.eachTree(l,(e,l,t,r,a,o)=>{e.level=o.length,n(e,l,t,0,a)}):e.forEach(n),_&&"fixed"!==r.mode&&o.enabled&&(0,_log.warnLog)("vxe.error.notConflictProp",['column.type="expand',"virtual-y-config.enabled=false"]),_&&"fixed"!==r.mode&&t.area&&(0,_log.errLog)("vxe.error.errConflicts",["mouse-config.area","column.type=expand"]),R&&(v.useKey||(0,_log.errLog)("vxe.error.reqProp",["column-config.useKey & column.type=html"]),a.useKey||(0,_log.errLog)("vxe.error.reqProp",["row-config.useKey & column.type=html"])),N.isGroup=i,N.rowGroupColumn=x,N.treeNodeColumn=w,N.expandColumn=_,N.isAllOverflow=m},Pl=()=>{G.customHeight=Ol("height"),G.customMinHeight=Ol("minHeight"),G.customMaxHeight=Ol("maxHeight"),!N.scrollYLoad||G.customHeight||G.customMinHeight||(G.customHeight=300)},Gl=(e,l)=>{var t=l.querySelectorAll(`.vxe-cell--wrapper[colid="${e.id}"]`);let r=0;var l=t[0];l&&l.parentElement&&(l=getComputedStyle(l.parentElement),r=Math.ceil(_xeUtils.default.toNumber(l.paddingLeft)+_xeUtils.default.toNumber(l.paddingRight)));let a=e.renderAutoWidth-r;for(let e=0;e<t.length;e++){var o=t[e];a=Math.max(a,o?Math.ceil(o.scrollWidth)+4:0)}return a+r},$l=()=>{var e=vl.value;let r=G.fullColumnIdData,a=Y.value;a&&(a.setAttribute("data-calc-col","Y"),e.forEach(e=>{var l=e.id,l=r[l],t=Gl(e,a);l&&(l.width=Math.max(t,l.width)),e.renderAutoWidth=t}),ne.analyColumnWidth(),a.removeAttribute("data-calc-col"))},Wl=()=>{var e=G.elemStore,e=(0,_util.getRefElem)(e["main-body-wrapper"]);if(e){var a=S.value;if(a)if(T.value){let t=0;a=e.clientWidth,e=a;let r=e/100;var l=W.fit,o=N.columnStore,{resizeList:o,pxMinList:i,autoMinList:n,pxList:d,scaleList:u,scaleMinList:s,autoList:c,remainList:g}=o;if(i.forEach(e=>{var l=_xeUtils.default.toInteger(e.minWidth);t+=l,e.renderWidth=l}),n.forEach(e=>{var l=Math.max(60,_xeUtils.default.toInteger(e.renderAutoWidth));t+=l,e.renderWidth=l}),s.forEach(e=>{var l=Math.floor(_xeUtils.default.toInteger(e.minWidth)*r);t+=l,e.renderWidth=l}),u.forEach(e=>{var l=Math.floor(_xeUtils.default.toInteger(e.width)*r);t+=l,e.renderWidth=l}),d.forEach(e=>{var l=_xeUtils.default.toInteger(e.width);t+=l,e.renderWidth=l}),c.forEach(e=>{var l=Math.max(60,_xeUtils.default.toInteger(e.renderAutoWidth));t+=l,e.renderWidth=l}),o.forEach(e=>{var l=_xeUtils.default.toInteger(e.resizeWidth);t+=l,e.renderWidth=l}),e-=t,r=0<e?Math.floor(e/(s.length+i.length+n.length+g.length)):0,l?0<e&&s.concat(i).concat(n).forEach(e=>{t+=r,e.renderWidth+=r}):r=40,g.forEach(e=>{var l=Math.max(r,40);e.renderWidth=l,t+=l}),l){var v=u.concat(s).concat(i).concat(n).concat(g);let l=v.length-1;if(0<l){let e=a-t;if(0<e){for(;0<e&&0<=l;)e--,v[l--].renderWidth++;t=a}}}N.scrollXWidth=t,N.resizeWidthFlag++,Ft(),Pl()}}},Nl=(e,l)=>{var t=l.querySelectorAll(`.vxe-cell--wrapper[rowid="${e.rowid}"]`);let r=e.height;for(let e=0;e<t.length;e++){var a=t[e],o=a.parentElement,o=Math.ceil(_xeUtils.default.toNumber(o.style.paddingTop)+_xeUtils.default.toNumber(o.style.paddingBottom)),a=a?a.clientHeight:0;r=Math.max(r-o,Math.ceil(a))}return r},Vl=()=>{let{tableData:e,isAllOverflow:l,scrollYLoad:t,scrollXLoad:r}=N,a=G.fullAllDataRowIdData,o=D.value,i=Y.value;if(!l&&t&&i){let t=(0,_util.createHandleGetRowId)(ne).handleGetRowId;i.setAttribute("data-calc-row","Y"),e.forEach(e=>{var l,e=t(e),e=a[e];e&&(l=Nl(e,i),e.height=Math.max(o,r?Math.max(e.height,l):l)),i.removeAttribute("data-calc-row")}),N.calcCellHeightFlag++}},Yl=t=>{let{sortBy:r,sortType:a}=t;return e=>{let l;return l=r?_xeUtils.default.isFunction(r)?r({row:e,column:t}):_xeUtils.default.get(e,r):$.getCellLabel(e,t),a&&"auto"!==a?"number"===a?_xeUtils.default.toNumber(l):"string"===a?_xeUtils.default.toValueString(l):l:isNaN(l)?l:_xeUtils.default.toNumber(l)}},ql=()=>{let r=W.treeConfig,{fullDataRowIdData:n,fullAllDataRowIdData:d,afterFullData:e,afterTreeFullData:l}=G;var t=ie.value,a=t.transform,o=t.children||t.childrenField;let u={};if(r){let i=(0,_util.createHandleGetRowId)(ne).handleGetRowId;_xeUtils.default.eachTree(l,(e,l,t,r)=>{var a=i(e),o=d[a],r=r.map((e,l)=>l%2==0?Number(e)+1:".").join("");o?(o.seq=r,o.treeIndex=l):(o={row:e,rowid:a,seq:r,index:-1,$index:-1,_index:-1,treeIndex:-1,items:[],parent:null,level:0,height:0,resizeHeight:0,oTop:0,expandHeight:0},d[a]=o,n[a]=o),u[a]=e},{children:a?t.mapChildrenField:o}),a&&e.forEach((e,l)=>{var e=i(e),e=d[e],t=l+1;e&&(r||(e.seq=t),e._index=l)}),G.afterFullRowMaps=u}else(()=>{let o=W.treeConfig,{afterFullData:e,fullDataRowIdData:i,fullAllDataRowIdData:n}=G,d=(0,_util.createHandleGetRowId)(ne).handleGetRowId,u={};e.forEach((e,l)=>{var t=d(e),r=n[t],a=l+1;r?(o||(r.seq=a),r._index=l):(r={row:e,rowid:t,seq:a,index:-1,$index:-1,_index:l,treeIndex:-1,items:[],parent:null,level:0,height:0,resizeHeight:0,oTop:0,expandHeight:0},n[t]=r,i[t]=r),u[t]=e}),G.afterFullRowMaps=u})()},n=()=>{var e=W.treeConfig,l=N.isRowGroupStatus;let{fullAllDataRowIdData:n,treeExpandedMaps:d,rowGroupExpandedMaps:u}=G;var t=I.value,r=ie.value;let s=(0,_util.createHandleGetRowId)(ne).handleGetRowId,c=[],g={};return e&&r.transform?(e=r.children||r.childrenField,_xeUtils.default.eachTree(G.afterTreeFullData,(e,l,t,r,a)=>{var o=s(e),i=s(a);(!a||g[i]&&d[i])&&((a=n[o])&&(a._index=c.length),g[o]=1,c.push(e))},{children:e}),G.afterFullData=c,Ct(c),c):l?(r=t.childrenField,_xeUtils.default.eachTree(G.afterGroupFullData,(e,l,t,r,a)=>{var o=s(e),i=s(a);(!a||g[i]&&u[i])&&((a=n[o])&&(a._index=c.length),g[o]=1,c.push(e))},{children:r}),G.afterFullData=c,Ct(c),c):G.afterFullData},k=()=>{let{showHeaderOverflow:x,showFooterOverflow:_,mouseConfig:e,spanMethod:w,footerSpanMethod:C}=W,{isGroup:b,currentRow:l,tableColumn:R,scrollXLoad:E,scrollYLoad:y,overflowX:T,scrollbarWidth:t,overflowY:r,scrollbarHeight:a,scrollXWidth:S,columnStore:o,editStore:i,isAllOverflow:D,expandColumn:F,isColLoading:I}=N,{visibleColumn:M,tableHeight:L,headerHeight:k,footerHeight:U,elemStore:A,customHeight:H,customMinHeight:n,customMaxHeight:O}=G;var d=Y.value;if(d){let g=r?t:0,v=T?a:0;var u=Re.value,s=re.value;let h=oe.value;var c=(0,_util.getRefElem)(A["main-body-wrapper"]),z=(0,_util.getRefElem)(A["main-body-table"]);u&&(u.style.top=k+"px",u.style.height=c?c.offsetHeight-v+"px":"");let f=0,p=0,m=n-k-U-v;O&&(p=Math.max(m,O-k-U-v)),(f=H?H-k-U-v:f)||z&&(f=z.clientHeight),f&&(p&&(f=Math.min(p,f)),f=Math.max(m,f));var u=He.value,c=Fe.value,z=Ie.value,B=Se.value,B=(B&&(B.style.height=v+"px",B.style.visibility=T?"visible":"hidden"),Le.value),B=(B&&(B.style.left=u?g+"px":"",B.style.width=d.clientWidth-g+"px"),c&&(c.style.width=u?g+"px":"",c.style.display=u&&T&&v?"block":""),z&&(z.style.width=u?"":g+"px",z.style.display=!u&&T&&v?"block":""),De.value),d=(B&&(B.style.width=g+"px",B.style.height=f+k+U+"px",B.style.visibility=r?"visible":"hidden"),Me.value),c=(d&&(d.style.height=k+"px",d.style.display=r&&k?"block":""),ke.value),z=(c&&(c.style.height=f+"px",c.style.top=k+"px"),Ue.value),u=(z&&(z.style.height=U+"px",z.style.top=k+f+"px",z.style.display=r&&U?"block":""),j.value);return u&&(u.style.height=f+"px",u.style.top=k+"px"),["main","left","right"].forEach((d,e)=>{let u=0<e?d:"";e="left"===u;let s=[],c;u&&(s=e?o.leftList:o.rightList,c=(e?P:X).value),["header","body","footer"].forEach(t=>{var r=(0,_util.getRefElem)(A[d+`-${t}-wrapper`]),a=(0,_util.getRefElem)(A[d+`-${t}-scroll`]),o=(0,_util.getRefElem)(A[d+`-${t}-table`]);if("header"===t){let e=R,l=!1;b?e=M:(E&&x&&(w||C||(l=!0)),l&&(I||!u&&T)||(e=M),u&&l&&(e=s||[]));var i=e.reduce((e,l)=>e+l.renderWidth,0);u&&(!b&&l?r&&(r.style.width=i?i+"px":""):r&&(r.style.width=S?S+"px":"")),a&&(a.style.height=k+"px"),o&&(o.style.width=i?i+"px":"")}else if("body"===t){a&&(a.style.maxHeight=O?p+"px":"",a.style.height=H?f+"px":"",a.style.minHeight=m+"px"),c&&(r&&(r.style.top=k+"px"),c.style.height=`${0<H?H:L+k+U+v}px`,c.style.width=s.reduce((e,l)=>e+l.renderWidth,0)+"px");let e=R,l=!1;!(E||y||D)||F&&"fixed"!==h.mode||w||C||(l=!0);var i=(e=u&&(e=M,l)?s||[]:e).reduce((e,l)=>e+l.renderWidth,0),n=(u&&(l?r&&(r.style.width=i?i+"px":""):r&&(r.style.width=S?S+"px":"")),o&&(o.style.width=i?i+"px":"",o.style.paddingRight=g&&u&&(ce.firefox||ce.safari)?g+"px":""),(0,_util.getRefElem)(A[d+`-${t}-emptyBlock`]));n&&(n.style.width=i?i+"px":"")}else if("footer"===t){let e=R,l=!1;E&&_&&(w||C||(l=!0)),l&&(I||!u&&T)||(e=M);n=(e=u&&l?s||[]:e).reduce((e,l)=>e+l.renderWidth,0);u&&(l?r&&(r.style.width=n?n+"px":""):r&&(r.style.width=S?S+"px":"")),a&&(a.style.height=U+"px",c)&&r&&(r.style.top=`${0<H?H-U-v:L+k}px`),o&&(o.style.width=n?n+"px":"")}})}),l&&ne.setCurrentRow(l),e&&s.selected&&i.selected.row&&i.selected.column&&ne.addCellSelectedClass(),(0,_vue.nextTick)()}},Xl=e=>ne.triggerValidate?ne.triggerValidate(e):(0,_vue.nextTick)(),jl=(e,l)=>{Xl("blur").catch(e=>e).then(()=>{ne.handleEdit(l,e).then(()=>Xl("change")).catch(e=>e)})},Kl=e=>{M.value.reserve&&(G.radioReserveRow=e)},Zl=(e,l)=>{var t,r=G.checkboxReserveRowMap;le.value.reserve&&(t=(0,_util.getRowid)(ne,e),l?r[t]=e:r[t]&&delete r[t])},Jl=(e,l)=>{var t=M.value.checkMethod;return e&&(l||!t||t({$table:ne,row:e}))&&(N.selectRadioRow=e,Kl(e)),(0,_vue.nextTick)()},Ql=(e,l,t)=>(e&&!_xeUtils.default.isArray(e)&&(e=[e]),ne.handleBatchSelectRows(e,!!l,t),ne.checkSelectionStatus(),(0,_vue.nextTick)()),et=(l,t)=>{let r=W.treeConfig,a=N.isRowGroupStatus,{afterFullData:e,afterTreeFullData:o,afterGroupFullData:i,checkboxReserveRowMap:n,selectCheckboxMaps:d}=G;var u=ie.value,s=I.value,u=u.children||u.childrenField,c=le.value;let{checkField:g,reserve:v,checkMethod:h}=c,f=(0,_util.createHandleGetRowId)(ne).handleGetRowId,p=c.indeterminateField||c.halfField,m={};return g?(c=e=>{!t&&h&&!h({$table:ne,row:e})||(l&&(m[f(e)]=e),_xeUtils.default.set(e,g,l)),(r||a)&&p&&_xeUtils.default.set(e,p,!1)},r||a?_xeUtils.default.eachTree(e,c,{children:u}):e.forEach(c)):a?l?_xeUtils.default.eachTree(i,e=>{var l;!t&&h&&!h({$table:ne,row:e})||(l=f(e),m[l]=e)},{children:s.mapChildrenField}):!t&&h&&_xeUtils.default.eachTree(i,e=>{var l=f(e);!h({$table:ne,row:e})&&d[l]&&(m[l]=e)},{children:s.mapChildrenField}):r?l?_xeUtils.default.eachTree(o,e=>{var l;!t&&h&&!h({$table:ne,row:e})||(l=f(e),m[l]=e)},{children:u}):!t&&h&&_xeUtils.default.eachTree(o,e=>{var l=f(e);!h({$table:ne,row:e})&&d[l]&&(m[l]=e)},{children:u}):l?!t&&h?e.forEach(e=>{var l=f(e);(d[l]||h({$table:ne,row:e}))&&(m[l]=e)}):e.forEach(e=>{var l=f(e);m[l]=e}):!t&&h&&e.forEach(e=>{var l=f(e);!h({$table:ne,row:e})&&d[l]&&(m[l]=e)}),v&&(l?_xeUtils.default.each(m,(e,l)=>{n[l]=e}):e.forEach(e=>Zl(e,!1))),N.updateCheckboxFlag++,G.selectCheckboxMaps=g?{}:m,N.isAllSelected=l,N.isIndeterminate=!1,G.treeIndeterminateRowMaps={},ne.checkSelectionStatus(),(0,_vue.nextTick)()},lt=o=>{var e=ie.value,l=le.value;let{transform:i,loadMethod:n}=e,d=l.checkStrictly;return new Promise(a=>{if(n){let{fullAllDataRowIdData:e,treeExpandLazyLoadedMaps:l}=G,t=(0,_util.getRowid)(ne,o),r=e[t];l[t]=o,Promise.resolve(n({$table:ne,row:o})).then(e=>{if(r&&(r.treeLoaded=!0),l[t]&&delete l[t],e=_xeUtils.default.isArray(e)?e:[])return ne.loadTreeChildren(o,e).then(e=>{var l=G.treeExpandedMaps;return e.length&&!l[t]&&(l[t]=o),N.treeExpandedFlag++,!d&&ne.isCheckedByCheckboxRow(o)&&Ql(e,!0),(0,_vue.nextTick)().then(()=>{if(i)return ne.handleTableData(),ql(),(0,_vue.nextTick)()})})}).catch(()=>{var e=G.treeExpandLazyLoadedMaps;r&&(r.treeLoaded=!1),e[t]&&delete e[t]}).finally(()=>{N.treeExpandedFlag++,(0,_vue.nextTick)().then(()=>ne.recalculate()).then(()=>a())})}else a()})},tt=(e,l)=>{var t,r=G.treeExpandedReserveRowMap;ie.value.reserve&&(t=(0,_util.getRowid)(ne,e),l?r[t]=e:r[t]&&delete r[t])},rt=i=>new Promise(r=>{var e=oe.value.loadMethod;if(e){var{fullAllDataRowIdData:a,rowExpandLazyLoadedMaps:o}=G;let l=(0,_util.getRowid)(ne,i),t=a[l];o[l]=i,e({$table:ne,row:i,rowIndex:ne.getRowIndex(i),$rowIndex:ne.getVMRowIndex(i)}).then(()=>{var e=G.rowExpandedMaps;t&&(t.expandLoaded=!0),e[l]=i,N.rowExpandedFlag++}).catch(()=>{t&&(t.expandLoaded=!1)}).finally(()=>{var e=G.rowExpandLazyLoadedMaps;e[l]&&delete e[l],N.rowExpandedFlag++,(0,_vue.nextTick)().then(()=>ne.recalculate()).then(()=>ne.updateCellAreas()).then(()=>r())})}else r()}),at=(e,l)=>{var t,r=G.rowExpandedReserveRowMap;oe.value.reserve&&(t=(0,_util.getRowid)(ne,e),l?r[t]=e:r[t]&&delete r[t])},ot=()=>(0,_vue.nextTick)().then(()=>{var e,{scrollXLoad:l,scrollYLoad:t}=N,{scrollXStore:r,scrollYStore:a}=G,o=y.value,i=C.value,i=(l?({toVisibleIndex:l,visibleSize:e}=Fl(),n=Math.max(0,i.oSize?_xeUtils.default.toNumber(i.oSize):0),r.preloadSize=_xeUtils.default.toNumber(i.preSize),r.offsetSize=n,r.visibleSize=e,r.endIndex=Math.max(r.startIndex+r.visibleSize+n,r.endIndex),r.visibleStartIndex=Math.max(r.startIndex,l),r.visibleEndIndex=Math.min(r.endIndex,l+e),ne.updateScrollXData().then(()=>{pt()})):ne.updateScrollXSpace(),Ml()),{toVisibleIndex:n,visibleSize:r}=(a.rowHeight=i,N.rowHeight=i,Ll());t?(l=Math.max(0,o.oSize?_xeUtils.default.toNumber(o.oSize):0),a.preloadSize=_xeUtils.default.toNumber(o.preSize),a.offsetSize=l,a.visibleSize=r,a.endIndex=Math.max(a.startIndex+r+l,a.endIndex),a.visibleStartIndex=Math.max(a.startIndex,n),a.visibleEndIndex=Math.min(a.endIndex,n+r),ne.updateScrollYData().then(()=>{yt()})):ne.updateScrollYSpace()}),it=()=>{var{scrollXWidth:e,scrollYHeight:l}=N,t=G.elemStore,r=E.value,a=(0,_util.getRefElem)(t["main-body-wrapper"]),o=(0,_util.getRefElem)(t["main-header-table"]),t=(0,_util.getRefElem)(t["main-footer-table"]),i=T.value,n=S.value;let d=!1;a&&(l=l>a.clientHeight,n&&(N.scrollbarWidth=r.width||n.offsetWidth-n.clientWidth||14),N.overflowY=l,d=e>a.clientWidth,i&&(N.scrollbarHeight=r.height||i.offsetHeight-i.clientHeight||14),n=o?o.clientHeight:0,l=t?t.clientHeight:0,G.tableHeight=a.offsetHeight,G.headerHeight=n,G.footerHeight=l,N.overflowX=d,N.parentHeight=Math.max(G.headerHeight+l+20,ne.getParentHeight())),d&&ne.checkScrolling()},nt=e=>{var l,t,r,a=Y.value;return G.rceRunTime=Date.now(),a&&a.clientWidth?((a=fe.value)&&([a,l,t,r]=a.children,Il("default",a),Il("medium",l),Il("small",t),Il("mini",r)),$l(),Wl(),it(),k(),It(),ot().then(()=>{if($l(),e&&Wl(),it(),k(),e&&U(),It(),e)return ot()})):(0,_vue.nextTick)()},dt=e=>{let l=[],t=[];e&&(_xeUtils.default.isArray(e)?e:[e]).forEach(e=>{l.push(e),t.push({field:e})}),N.rowGroupList=t,N.aggHandleFields=l},ut=e=>{let t=I.value.mapChildrenField;t&&(_xeUtils.default.lastEach(e,e=>{let l=0;_xeUtils.default.each(e[t],e=>{e.isAggregate?l+=e.childCount||0:l++}),e.childCount=l}),ne.handlePivotTableAggregateData)&&ne.handlePivotTableAggregateData(e)},st=(e,h)=>{let f=e,p=e;if(h){let{rowField:d,parentField:u,childrenField:s,mapChildrenField:c}=I.value;var l=le.value;let g=l.checkField,v=l.indeterminateField||l.halfField;l=h[0];if(l&&d&&u&&s&&c){f=[],p=[];let r=l.field,a=ne.getColumnByField(r),o={},i=[],n=(0,_util.getRowkey)(ne);e.forEach(e=>{var l=a?ne.getCellLabel(e,a):_xeUtils.default.get(e,r),l=_xeUtils.default.eqNull(l)?"":l;let t=o[l];t||(t=[],o[l]=t),e.isAggregate&&(e.isAggregate=void 0),t.push(e)}),_xeUtils.default.objectEach(o,(e,l)=>{var{fullData:e,treeData:t}=st(e,h.slice(1)),l={isAggregate:!0,aggData:{},groupContent:l,groupField:r,childCount:0,[d]:(0,_util.getRowUniqueId)(),[u]:null,[n]:(0,_util.getRowUniqueId)(),[s]:t,[c]:t};g&&(l[g]=!1),v&&(l[v]=!1),i.push(l),p.push(l),f.push(l),e.length&&f.push(...e)}),ut(i)}}return{treeData:p,fullData:f}},ct=(e,l)=>{var{keepSource:t,treeConfig:r,rowGroupConfig:a,aggregateConfig:o}=W;let{rowGroupList:i,scrollYLoad:n}=N,{scrollYStore:d,scrollXStore:u,lastScrollLeft:s,lastScrollTop:c}=G;var g=J.value,v=ie.value;let h=oe.value;var f=v.transform,p=v.children||v.childrenField;let m=[],x=(0,_vue.reactive)(e?e.slice(0):[]);if(x.length>supportMaxRow&&(0,_log.errLog)("vxe.error.errMaxRow",[supportMaxRow]),r&&i.length)return(0,_log.errLog)("vxe.error.noTree",["aggregate-config"]),(0,_vue.nextTick)();if(g.drag&&i.length)return(0,_log.errLog)("vxe.error.errConflicts",["row-config.drag","aggregate-config"]),(0,_vue.nextTick)();let _=!1,w=(r?f?(v.rowField||(0,_log.errLog)("vxe.error.reqProp",["tree-config.rowField"]),v.parentField||(0,_log.errLog)("vxe.error.reqProp",["tree-config.parentField"]),p||(0,_log.errLog)("vxe.error.reqProp",["tree-config.childrenField"]),v.mapChildrenField||(0,_log.errLog)("vxe.error.reqProp",["tree-config.mapChildrenField"]),p===v.mapChildrenField&&(0,_log.errLog)("vxe.error.errConflicts",["tree-config.childrenField","tree-config.mapChildrenField"]),m=_xeUtils.default.toArrayTree(x,{key:v.rowField,parentKey:v.parentField,children:p,mapChildren:v.mapChildrenField}),x=m.slice(0)):m=x.slice(0):(o||a)&&i.length&&(g=st(x,i),m=g.treeData,x=g.fullData,_=!0),N.isRowGroupStatus=_,d.startIndex=0,d.endIndex=1,u.startIndex=0,u.endIndex=1,G.cvCacheMaps={},N.isRowLoading=!0,N.scrollVMLoading=!1,N.treeExpandedFlag++,N.rowExpandedFlag++,G.insertRowMaps={},N.insertRowFlag++,G.removeRowMaps={},N.removeRowFlag++,Ct(x));return N.isDragColMove=!1,N.isDragRowMove=!1,G.tableFullData=x,G.tableFullTreeData=_?[]:m,G.tableFullGroupData=_?m:[],ne.cacheRowMap(l),G.tableSynchData=e,l&&(G.isResizeCellHeight=!1),t&&ne.cacheSourceMap(x),ne.clearCellAreas&&W.mouseConfig&&(ne.clearCellAreas(),ne.clearCopyCellArea()),ne.clearMergeCells(),ne.clearMergeFooterItems(),ne.handleTableData(!0),ne.updateFooter(),ne.handleUpdateBodyMerge(),(0,_vue.nextTick)().then(()=>{Pl(),k()}).then(()=>{ot()}).then(()=>(w&&(d.endIndex=d.visibleSize),w&&(N.expandColumn&&"fixed"!==h.mode&&(0,_log.errLog)("vxe.error.notConflictProp",['column.type="expand','expand-config.mode="fixed"']),W.height||W.maxHeight||(0,_log.errLog)("vxe.error.reqProp",["height | max-height | virtual-y-config={enabled: false}"]),W.spanMethod)&&(0,_log.errLog)("vxe.error.scrollErrProp",["table.span-method"]),(()=>{var e=W.treeConfig,{expandColumn:l,currentRow:t,selectRadioRow:r}=N,{fullDataRowIdData:a,fullAllDataRowIdData:o,radioReserveRow:i,selectCheckboxMaps:n,treeExpandedMaps:d,rowExpandedMaps:u}=G,s=oe.value,c=ie.value,g=M.value,v=le.value;r&&!o[(0,_util.getRowid)(ne,r)]&&(N.selectRadioRow=null),g.reserve&&i&&a[r=(0,_util.getRowid)(ne,i)]&&Jl(a[r].row,!0),G.selectCheckboxMaps=Sl(n),N.updateCheckboxFlag++,v.reserve&&Ql(Dl(G.checkboxReserveRowMap),!0,!0),t&&!o[(0,_util.getRowid)(ne,t)]&&(N.currentRow=null),G.rowExpandedMaps=l?Sl(u):{},N.rowExpandedFlag++,l&&s.reserve&&ne.setRowExpand(Dl(G.rowExpandedReserveRowMap),!0),G.treeExpandedMaps=e?Sl(d):{},N.treeExpandedFlag++,e&&c.reserve&&ne.setTreeExpand(Dl(G.treeExpandedReserveRowMap),!0)})(),ne.checkSelectionStatus(),new Promise(a=>{(0,_vue.nextTick)().then(()=>nt(!1)).then(()=>(Vl(),U(),nt(!1))).then(()=>{let e=s,l=c;var t=C.value,r=y.value;t.scrollToLeftOnChange&&(e=0),r.scrollToTopOnChange&&(l=0),N.isRowLoading=!1,nt(!1),n===w?(0,_util.restoreScrollLocation)(ne,e,l).then(()=>{Vl(),U(),a()}):setTimeout(()=>{(0,_util.restoreScrollLocation)(ne,e,l).then(()=>{Vl(),U(),a()})})})})))},gt=()=>{var e,l,t;(()=>{var e=W.checkboxConfig;if(e){let t=G.fullDataRowIdData;var{checkAll:e,checkRowKeys:r}=le.value;if(e)et(!0,!0);else if(r){let l=[];r.forEach(e=>{t[e]&&l.push(t[e].row)}),Ql(l,!0,!0)}}})(),(t=W.radioConfig)&&(t=G.fullDataRowIdData,{checkRowKey:e,reserve:l}=M.value,e)&&(t[e]&&Jl(t[e].row,!0),l)&&(t=(0,_util.getRowkey)(ne),G.radioReserveRow={[t]:e}),(()=>{var e=W.expandConfig;if(e){let t=G.fullDataRowIdData;var{expandAll:e,expandRowKeys:r}=oe.value;if(e)ne.setAllRowExpand(!0);else if(r){let l=[];r.forEach(e=>{t[e]&&l.push(t[e].row)}),ne.setRowExpand(l,!0)}}})(),(()=>{var e=W.treeConfig;if(e){let a=G.tableFullData;var e=ie.value,{expandAll:l,expandRowKeys:i}=e;let o=e.children||e.childrenField;if(l)ne.setAllTreeExpand(!0);else if(i){let t=[],r=(0,_util.getRowkey)(ne);i.forEach(l=>{var e=_xeUtils.default.findTree(a,e=>l===_xeUtils.default.get(e,r),{children:o});e&&t.push(e.item)}),ne.setTreeExpand(t,!0)}}})(),(l=W.mergeCells)&&ne.setMergeCells(l),(t=W.mergeFooterItems)&&ne.setMergeFooterItems(t),(0,_vue.nextTick)(()=>setTimeout(()=>ne.recalculate()))},vt=()=>{(()=>{var l=W.sortConfig;if(l){var t=L.value;let e=t.defaultSort;e&&(e=_xeUtils.default.isArray(e)?e:[e]).length&&((l.multiple?e:e.slice(0,1)).forEach((e,l)=>{var{field:e,order:t}=e;e&&t&&(e=ne.getColumnByField(e))&&e.sortable&&(e.order=t,e.sortTime=Date.now()+l)}),t.remote||ne.handleTableData(!0).then(k))}})()},ht=()=>{var e=N.scrollXLoad;let{visibleColumn:l,scrollXStore:t,fullColumnIdData:r}=G;e=e?l.slice(t.startIndex,t.endIndex):l.slice(0);e.forEach((e,l)=>{e=e.id,e=r[e];e&&(e.$index=l)}),N.tableColumn=e},ft=()=>{var e=_xeUtils.default.orderBy(G.collectColumn,"renderSortNumber"),e=(G.collectColumn=e,mt(e));G.tableFullColumn=e,Bl()},pt=()=>{var e=N.isScrollXBig,{mergeBodyList:l,mergeFooterList:t,scrollXStore:r}=G,{preloadSize:a,startIndex:o,endIndex:i,offsetSize:n}=r,{toVisibleIndex:d,visibleSize:u}=Fl(),e={startIndex:Math.max(0,e?d-1:d-1-n-a),endIndex:e?d+u:d+u+n+a},{startIndex:n,endIndex:a}=(r.visibleStartIndex=d-1,r.visibleEndIndex=d+u+1,kl(l.concat(t),e,"col"),e);!(d<=o||i-u-1<=d)||o===n&&i===a||(r.startIndex=n,r.endIndex=a,ne.updateScrollXData()),ne.closeTooltip()},mt=e=>{let l=[];return e.forEach(e=>{l.push(...e.children&&e.children.length?mt(e.children):[e])}),l},xt=e=>{let i=[],n=[],d=[];var{isGroup:l,columnStore:t}=N;let{collectColumn:a,tableFullColumn:r,scrollXStore:o,fullColumnIdData:u}=G;if(l){let l=[],t=[],r=[];_xeUtils.default.eachTree(a,(e,l,t,r,a)=>{var o=(0,_utils.hasChildrenList)(e);a&&a.fixed&&(e.fixed=a.fixed),a&&e.fixed!==a.fixed&&(0,_log.errLog)("vxe.error.groupFixed"),o?e.visible=!!_xeUtils.default.findTree(e.children,e=>!(0,_utils.hasChildrenList)(e)&&e.visible):e.visible&&("left"===e.fixed?i:"right"===e.fixed?d:n).push(e)}),a.forEach(e=>{e.visible&&("left"===e.fixed?l:"right"===e.fixed?r:t).push(e)}),N.tableGroupColumn=l.concat(t).concat(r)}else r.forEach(e=>{e.visible&&("left"===e.fixed?i:"right"===e.fixed?d:n).push(e)});let s=i.concat(n).concat(d);G.visibleColumn=s,Ft();l=wt();return N.hasFixedColumn=0<i.length||0<d.length,Object.assign(t,{leftList:i,centerList:n,rightList:d}),l&&(W.spanMethod&&(0,_log.warnLog)("vxe.error.scrollErrProp",["span-method"]),W.footerSpanMethod&&(0,_log.warnLog)("vxe.error.scrollErrProp",["footer-span-method"]),e)&&(t=Fl().visibleSize,o.startIndex=0,o.endIndex=t,o.visibleSize=t,o.visibleStartIndex=0,o.visibleEndIndex=t),s.length===G.visibleColumn.length&&G.visibleColumn.every((e,l)=>e===s[l])||(ne.clearMergeCells(),ne.clearMergeFooterItems()),s.forEach((e,l)=>{e=e.id,e=u[e];e&&(e._index=l)}),ht(),e?(Ft(),ne.updateFooter().then(()=>ne.recalculate()).then(()=>(ne.updateCellAreas(),ne.recalculate()))):ne.updateFooter()},_t=e=>{let r=oe.value;G.collectColumn=e;e=mt(e);return G.tableFullColumn=e,N.isColLoading=!0,N.isDragColMove=!1,G.collectColumn.forEach((e,l)=>{l+=1;e.sortNumber=l,e.renderSortNumber=l}),Promise.resolve((()=>{var e=W.customConfig,l=R.value,t=cl.value,{storage:r,restoreStore:a,storeOptions:o}=t,i=!0===r,r=i?{}:Object.assign({},r||{},o),o=_(r.resizable,i),n=_(r.visible,i),d=_(r.fixed,i),r=_(r.sort,i);if((e?(0,_utils.isEnableConf)(t):t.enabled)&&(o||n||d||r)){if(l)return i=Tl(l),a?Promise.resolve(a({$table:ne,id:l,type:"restore",storeData:i})).then(e=>{if(e)return zl(e)}).catch(e=>e):zl(i);(0,_log.errLog)("vxe.error.reqProp",["id"])}})()).then(()=>{var{scrollXLoad:e,scrollYLoad:l,expandColumn:t}=N;return Bl(),xt(!0).then(()=>{N.scrollXLoad&&pt()}),ne.clearMergeCells(),ne.clearMergeFooterItems(),ne.handleTableData(!0),ne.handleAggregateSummaryData(),(e||l)&&t&&"fixed"!==r.mode&&(0,_log.warnLog)("vxe.error.scrollErrProp",["column.type=expand"]),(0,_vue.nextTick)().then(()=>(b&&b.syncUpdate({collectColumn:G.collectColumn,$table:ne}),ne.handleUpdateCustomColumn&&ne.handleUpdateCustomColumn(),N.isColLoading=!1,ne.recalculate()))})},wt=e=>{var l=C.value,e=e||G.tableFullColumn,l=!!l.enabled&&-1<l.gt&&(0===l.gt||l.gt<e.length);return N.scrollXLoad=l},Ct=e=>{var l=W.treeConfig,t=y.value,r=ie.value.transform,e=e||G.tableFullData,r=(r||!l)&&!!t.enabled&&-1<t.gt&&(0===t.gt||t.gt<e.length);return N.scrollYLoad=r},bt=(e,l)=>{let t=N.treeNodeColumn,{fullAllDataRowIdData:r,tableFullTreeData:a,treeExpandedMaps:o,treeExpandLazyLoadedMaps:i}=G;var n=ie.value;let{reserve:d,lazy:u,accordion:s,toggleMethod:c}=n,g=n.children||n.childrenField,v=n.hasChild||n.hasChildField,h=[],f=ne.getColumnIndex(t),p=ne.getVMColumnIndex(t),m=(0,_util.createHandleGetRowId)(ne).handleGetRowId,x=c?e.filter(e=>c({$table:ne,expanded:l,column:t,columnIndex:f,$columnIndex:p,row:e})):e;return s&&(x=x.length?[x[x.length-1]]:[],n=_xeUtils.default.findTree(a,e=>e===x[0],{children:g}))&&n.items.forEach(e=>{e=m(e);o[e]&&delete o[e]}),l?x.forEach(e=>{var l,t=m(e);o[t]||(l=r[t])&&(u&&e[v]&&!l.treeLoaded&&!i[t]?h.push(lt(e)):e[g]&&e[g].length&&(o[t]=e))}):x.forEach(e=>{e=m(e);o[e]&&delete o[e]}),d&&x.forEach(e=>tt(e,l)),N.treeExpandedFlag++,Promise.all(h).then(()=>ne.recalculate())},Rt=(e,l)=>((e,l)=>{let{fullAllDataRowIdData:t,tableFullGroupData:r,rowGroupExpandedMaps:a}=G;let{mapChildrenField:o,accordion:i}=I.value,n=(0,_util.createHandleGetRowId)(ne).handleGetRowId,d=e;return o&&(i&&(d=d.length?[d[d.length-1]]:[],e=_xeUtils.default.findTree(r,e=>(0,_util.getRowid)(ne,e)===(0,_util.getRowid)(ne,d[0]),{children:o}))&&e.items.forEach(e=>{e=n(e);a[e]&&delete a[e]}),l?d.forEach(e=>{var l=n(e);a[l]||t[l]&&e[o]&&e[o].length&&(a[l]=e)}):d.forEach(e=>{e=n(e);a[e]&&delete a[e]})),N.rowGroupExpandedFlag++,ne.recalculate()})(e,l).then(()=>(n(),ne.handleTableData(),N.rowGroupExpandedFlag++,ql(),(0,_vue.nextTick)())).then(()=>ne.recalculate(!0)).then(()=>{setTimeout(()=>{ne.updateCellAreas()},30)}),Et=(e,l)=>{et(l),e&&de("checkbox-all",{records:()=>ne.getCheckboxRecords(),reserves:()=>ne.getCheckboxReserveRecords(),indeterminates:()=>ne.getCheckboxIndeterminateRecords(),checked:l},e)},yt=()=>{var{isAllOverflow:e,isScrollYBig:l}=N,{mergeBodyList:t,scrollYStore:r}=G,{preloadSize:a,startIndex:o,endIndex:i,offsetSize:n}=r,e=e?n:n+1,{toVisibleIndex:d,visibleSize:u}=Ll(),n={startIndex:Math.max(0,l?d-1:d-1-n-a),endIndex:l?d+u:d+u+e+a},{startIndex:l,endIndex:e}=(r.visibleStartIndex=d-1,r.visibleEndIndex=d+u+1,kl(t,n,"row"),n);!(d<=o||i-u-1<=d)||o===l&&i===e||(r.startIndex=l,r.endIndex=e,ne.updateScrollYData())};d=t=>function(e){var l=G.fullAllDataRowIdData;if(e){l=l[(0,_util.getRowid)(ne,e)];if(l)return l[t]}return-1},ze=t=>function(e){var l=G.fullColumnIdData;if(e){l=l[e.id];if(l)return l[t]}return-1};let de=(e,l,t)=>{r(e,createEvent(t,{$table:ne,$grid:K},l))},Tt=()=>{var e=Y.value;e&&e.clientWidth&&e.clientHeight&&ne.recalculate()},St=(e,l)=>{ne.analyColumnWidth(),ne.recalculate().then(()=>{ne.saveCustomStore("update:width"),ne.updateCellAreas(),ne.dispatchEvent("column-resizable-change",l,e),ne.dispatchEvent("resizable-change",l,e),setTimeout(()=>ne.recalculate(!0),300)})},Dt=(e,l)=>{N.resizeHeightFlag++,ne.recalculate().then(()=>{ne.updateCellAreas(),ne.dispatchEvent("row-resizable-change",l,e),setTimeout(()=>ne.recalculate(!0),300)})},Ft=()=>{var{visibleColumn:t,fullColumnIdData:r}=G;let a=0;for(let e=0,l=t.length;e<l;e++){var o=t[e],i=r[o.id];i&&(i.oLeft=a),a+=o.renderWidth}},U=()=>{var t=N.expandColumn,{afterFullData:r,fullAllDataRowIdData:a,rowExpandedMaps:o}=G,i=oe.value,n=J.value,d=F.value,u=D.value,s=(0,_util.createHandleGetRowId)(ne).handleGetRowId;let c=0;for(let e=0,l=r.length;e<l;e++){var g=s(r[e]),v=a[g]||{};v.oTop=c,c+=v.resizeHeight||d.height||n.height||v.height||u,t&&o[g]&&(c+=v.expandHeight||i.height||0)}},It=()=>{let{expandColumn:e,scrollYLoad:d,scrollYTop:u,isScrollYBig:s}=N;var l=oe.value;let c=J.value,g=F.value,v=D.value;l=l.mode;if(e&&"fixed"===l){let{elemStore:e,fullAllDataRowIdData:i}=G;l=j.value;let n=(0,_util.getRefElem)(e["main-body-scroll"]);if(l&&n){let o=!1;_xeUtils.default.arrayEach(l.children,l=>{var t=l.getAttribute("rowid")||"",r=i[t];if(r){var a=l.offsetHeight+1,t=n.querySelector(`.vxe-body--row[rowid="${t}"]`);let e=0;d?e=s&&t?t.offsetTop+t.offsetHeight:r.oTop+(r.resizeHeight||g.height||c.height||r.height||v):t&&(e=t.offsetTop+t.offsetHeight),s&&(e+=u),l.style.top=(0,_dom.toCssUnit)(e),o||r.expandHeight!==a&&(o=!0),r.expandHeight=a}}),o&&(N.rowExpandHeightFlag++,(0,_vue.nextTick)(()=>{U()}))}}},Mt=()=>{var e=G.elemStore,l=j.value,e=(0,_util.getRefElem)(e["main-body-scroll"]);l&&e&&(l.scrollTop=e.scrollTop)},Lt=($={dispatchEvent:de,getEl(){return Y.value},clearAll(){return(0,_util.clearTableAllStatus)(ne)},syncData(){return(0,_log.errLog)("vxe.error.delFunc",["syncData","getData"]),(0,_vue.nextTick)().then(()=>(N.tableData=[],r("update:data",G.tableFullData),(0,_vue.nextTick)()))},updateData(){let{scrollXLoad:e,scrollYLoad:l}=N;return V.handleTableData(!0).then(()=>{if($.updateFooter(),e||l)return e&&V.updateScrollXSpace(),l&&V.updateScrollYSpace(),$.refreshScroll()}).then(()=>($.updateCellAreas(),$.recalculate(!0))).then(()=>{setTimeout(()=>ne.recalculate(),50)})},loadData(e){let l=G.initStatus;return ct(e,!1).then(()=>(G.inited=!0,G.initStatus=!0,l||gt(),$.recalculate()))},reloadData(e){return $.clearAll().then(()=>(G.inited=!0,G.initStatus=!0,ct(e,!0))).then(()=>(gt(),$.recalculate()))},setRow(l,a){if(l&&a){let e=l,r=(_xeUtils.default.isArray(l)||(e=[l]),(0,_util.getRowkey)(ne));e.forEach(e=>{var l=(0,_util.getRowid)(ne,e),t=_xeUtils.default.clone(Object.assign({},a),!0);_xeUtils.default.set(t,r,l),Object.assign(e,t)})}return(0,_vue.nextTick)()},reloadRow(e,l,t){var r=W.keepSource,a=N.tableData,o=G.sourceDataRowIdData;if(r){if(ne.isAggregateRecord(e))return(0,_vue.nextTick)();r=o[(0,_util.getRowid)(ne,e)];r&&e&&(t?(o=_xeUtils.default.clone(_xeUtils.default.get(l||e,t),!0),_xeUtils.default.set(e,t,o),_xeUtils.default.set(r,t,o)):(t=(0,_util.getRowkey)(ne),o=(0,_util.getRowid)(ne,e),l=_xeUtils.default.clone(Object.assign({},l),!0),_xeUtils.default.set(l,t,o),_xeUtils.default.destructuring(r,Object.assign(e,l)))),N.tableData=a.slice(0)}else(0,_log.errLog)("vxe.error.reqProp",["keep-source"]);return(0,_vue.nextTick)()},getParams(){return W.params},loadTreeChildren(r,e){let l=W.keepSource,{tableSourceData:a,fullDataRowIdData:n,fullAllDataRowIdData:d,sourceDataRowIdData:o}=G;var t=ie.value;let{transform:i,mapChildrenField:u}=t,s=t.children||t.childrenField,c=d[(0,_util.getRowid)(ne,r)],g=c?c.level:0;return $.createData(e).then(e=>{if(l){let l=(0,_util.getRowid)(ne,r);var t=_xeUtils.default.findTree(a,e=>l===(0,_util.getRowid)(ne,e),{children:s});t&&(t.item[s]=_xeUtils.default.clone(e,!0)),e.forEach(e=>{var l=(0,_util.getRowid)(ne,e);o[l]=_xeUtils.default.clone(e,!0)})}return _xeUtils.default.eachTree(e,(e,l,t,r,a,o)=>{var i=(0,_util.getRowid)(ne,e),e={row:e,rowid:i,seq:-1,index:l,_index:-1,$index:-1,treeIndex:-1,items:t,parent:a||c.row,level:g+o.length,height:0,resizeHeight:0,oTop:0,expandHeight:0};n[i]=e,d[i]=e},{children:s}),r[s]=e,i&&(r[u]=_xeUtils.default.clone(e,!1)),ql(),e})},loadColumn(e){let{lastScrollLeft:a,lastScrollTop:o}=G;e=_xeUtils.default.mapTree(e,e=>(0,_vue.reactive)(_cell.default.createColumn(ne,e)));return _t(e).then(()=>{let e=a,l=o;var t=C.value,r=y.value;t.scrollToLeftOnChange&&(e=0),r.scrollToTopOnChange&&(l=0),(0,_util.restoreScrollLocation)(ne,e,l)})},reloadColumn(e){return $.clearAll().then(()=>$.loadColumn(e))},getRowNode(e){if(e){var l=G.fullAllDataRowIdData,e=e.getAttribute("rowid");if(e){l=l[e];if(l)return{rowid:l.rowid,item:l.row,index:l.index,items:l.items,parent:l.parent}}}return null},getColumnNode(e){if(e){var l=G.fullColumnIdData,e=e.getAttribute("colid");if(e){l=l[e];if(l)return{colid:l.colid,item:l.column,index:l.index,items:l.items,parent:l.parent}}}return null},getRowSeq:d("seq"),getRowIndex:d("index"),getVTRowIndex:d("_index"),getVMRowIndex:d("$index"),getColumnIndex:ze("index"),getVTColumnIndex:ze("_index"),getVMColumnIndex:ze("$index"),createData(e){return(0,_vue.nextTick)().then(()=>(0,_vue.reactive)(V.defineField(e)))},createRow(e){let l=_xeUtils.default.isArray(e);return l||(e=[e||{}]),$.createData(e).then(e=>l?e:e[0])},revertData(e,r){var{keepSource:l,treeConfig:t}=W;let{fullAllDataRowIdData:a,fullDataRowIdData:o,tableSourceData:i,sourceDataRowIdData:n,tableFullData:d,afterFullData:u,removeRowMaps:s}=G;var c=ie.value.transform;let g=(0,_util.createHandleGetRowId)(ne).handleGetRowId;if(!l)return(0,_log.errLog)("vxe.error.reqProp",["keep-source"]),(0,_vue.nextTick)();let v=e,h=(e?_xeUtils.default.isArray(e)||(v=[e]):v=_xeUtils.default.toArray(ne.getUpdateRecords()),!1);return v.length&&v.forEach(e=>{var l,e=g(e),t=a[e];t&&(t=t.row,ne.isInsertByRow(t)||(l=n[e])&&t&&(r?_xeUtils.default.set(t,r,_xeUtils.default.clone(_xeUtils.default.get(l,r),!0)):_xeUtils.default.destructuring(t,_xeUtils.default.clone(l,!0)),!o[e])&&ne.isRemoveByRow(t)&&(s[e]&&delete s[e],d.unshift(t),u.unshift(t),h=!0))}),e?(h&&(N.removeRowFlag++,ne.updateFooter(),ne.cacheRowMap(!1),ne.handleTableData(t&&c),t&&c||ne.updateAfterDataIndex(),ne.checkSelectionStatus(),N.scrollYLoad)&&ne.updateScrollYSpace(),(0,_vue.nextTick)().then(()=>(ne.updateCellAreas(),ne.recalculate()))):ne.reloadData(i)},clearData(e,l){let{tableFullData:t,visibleColumn:r}=G;return arguments.length?e&&!_xeUtils.default.isArray(e)&&(e=[e]):e=t,l?e.forEach(e=>_xeUtils.default.set(e,l,null)):e.forEach(l=>{r.forEach(e=>{e.field&&(0,_util.setCellValue)(l,e,null)})}),(0,_vue.nextTick)()},getCellElement(e,l){var t=G.elemStore,l=(0,_util.handleFieldOrColumn)(ne,l);if(!l)return null;var e=(0,_util.getRowid)(ne,e),r=(0,_util.getRefElem)(t["main-body-scroll"]),a=(0,_util.getRefElem)(t["left-body-scroll"]),t=(0,_util.getRefElem)(t["right-body-scroll"]);let o;return l&&(l.fixed&&("left"===l.fixed?a&&(o=a):t&&(o=t)),o=o||r)?o.querySelector(`.vxe-body--row[rowid="${e}"] .`+l.id):null},getCellLabel(l,t){t=(0,_util.handleFieldOrColumn)(ne,t);if(!t)return null;var r=t.formatter,a=(0,_util.getCellValue)(l,t);let o=a;if(r){let e;var i=G.fullAllDataRowIdData,n=(0,_util.getRowid)(ne,l),d=t.id,u=i[n];if(u&&(e=(e=u.formatData)||(i[n].formatData={}),u)&&e[d]&&e[d].value===a)return e[d].label;i={cellValue:a,row:l,rowIndex:$.getRowIndex(l),column:t,columnIndex:$.getColumnIndex(t)};o=_xeUtils.default.isString(r)?(u=(n=formats.get(r))?n.tableCellFormatMethod||n.cellFormatMethod:null)?u(i):"":_xeUtils.default.isArray(r)?(t=(l=formats.get(r[0]))?l.tableCellFormatMethod||l.cellFormatMethod:null)?t(i,...r.slice(1)):"":r(i),e&&(e[d]={value:a,label:o})}return o},isInsertByRow(e){e=(0,_util.getRowid)(ne,e);return!!N.insertRowFlag&&!!G.insertRowMaps[e]},isRemoveByRow(e){e=(0,_util.getRowid)(ne,e);return!!N.removeRowFlag&&!!G.removeRowMaps[e]},removeInsertRow(){var e=G.insertRowMaps;return ne.remove(_xeUtils.default.values(e))},isUpdateByRow(e,l){var t=W.keepSource,{tableFullColumn:r,fullDataRowIdData:a,sourceDataRowIdData:o}=G;if(t){t=_xeUtils.default.isString(e)||_xeUtils.default.isNumber(e)?e:(0,_util.getRowid)(ne,e),e=a[t];if(!e)return!1;var i=e.row,n=o[t];if(n){if(1<arguments.length)return!Rl(n,i,l);for(let e=0,l=r.length;e<l;e++){var d=r[e].field;if(d&&!Rl(n,i,d))return!0}}}return!1},getColumns(e){var l=G.visibleColumn;return _xeUtils.default.isUndefined(e)?l.slice(0):l[e]},getColid(e){e=(0,_util.handleFieldOrColumn)(ne,e);return e?e.id:null},getColumnById(e){var l=G.fullColumnIdData;return e&&l[e]?l[e].column:null},getColumnByField(e){var l=G.fullColumnFieldData;return e&&l[e]?l[e].column:null},getParentColumn(e){var l=G.fullColumnIdData,e=(0,_util.handleFieldOrColumn)(ne,e);return e&&e.parentId&&l[e.parentId]?l[e.parentId].column:null},getTableColumn(){return{collectColumn:G.collectColumn.slice(0),fullColumn:G.tableFullColumn.slice(0),visibleColumn:G.visibleColumn.slice(0),tableColumn:N.tableColumn.slice(0)}},moveColumnTo(e,t,l){var{fullColumnIdData:r,visibleColumn:a}=G,{dragToChild:l,dragPos:o,isCrossDrag:i}=Object.assign({},l),e=(0,_util.handleFieldOrColumn)(ne,e);let n=null;var d=e?r[e.id]:null;let u="left";if(_xeUtils.default.isNumber(t)){if(d&&t){let e=d.items,l=d._index+t;i&&(e=a,l=d._index+t),0<l&&l<e.length-1&&(n=e[l]),0<t&&(u="right")}}else{i=(n=(0,_util.handleFieldOrColumn)(ne,t))?r[n.id]:null;d&&i&&i._index>d._index&&(u="right")}return ne.handleColDragSwapEvent(null,!0,e,n,o||u,!0===l)},moveRowTo(e,t,l){var r=W.treeConfig,{fullAllDataRowIdData:a,afterFullData:o}=G,{dragToChild:l,dragPos:i,isCrossDrag:n}=Object.assign({},l),d=ie.value,e=(0,_util.handleRowidOrRow)(ne,e);let u=null,s="top";var c=e?a[(0,_util.getRowid)(ne,e)]:null;if(_xeUtils.default.isNumber(t)){if(c&&t){let e=o,l=c._index+t;r&&(e=c.items,d.transform)&&(l=c.treeIndex+t,n)&&(e=o,l=c._index+t),0<=l&&l<=e.length-1&&(u=e[l]),0<t&&(s="bottom")}}else{r=(u=(0,_util.handleRowidOrRow)(ne,t))?a[(0,_util.getRowid)(ne,u)]:null;c&&r&&r._index>c._index&&(s="bottom")}return ne.handleRowDragSwapEvent(null,!0,e,u,i||s,!0===l)},getFullColumns(){var e=G.collectColumn;return e.slice(0)},getData(e){var l=W.data||G.tableSynchData;return _xeUtils.default.isUndefined(e)?l.slice(0):l[e]},getCheckboxRecords(e){var l=W.treeConfig,t=N.updateCheckboxFlag;let{tableFullData:r,afterFullData:a,tableFullTreeData:o,fullDataRowIdData:i,afterFullRowMaps:n,selectCheckboxMaps:d}=G;var u=ie.value,{transform:s,mapChildrenField:c}=u;let g=le.value.checkField;u=u.children||u.childrenField;let v=[];if(t)if(g)v=l?_xeUtils.default.filterTree(e?s?o:r:s?o:a,e=>_xeUtils.default.get(e,g),{children:s?c:u}):(e?r:a).filter(e=>_xeUtils.default.get(e,g));else{let t=e||l&&!s?i:n;_xeUtils.default.each(d,(e,l)=>{t[l]&&v.push(i[l].row)})}return v},getTreeRowChildren(l){var t=W.treeConfig,r=G.fullAllDataRowIdData,a=ie.value,{transform:o,mapChildrenField:i}=a,a=a.children||a.childrenField;if(l&&t){let e;if(e=_xeUtils.default.isString(l)?l:(0,_util.getRowid)(ne,l)){t=r[e],l=t?t.row:null;if(l)return l[o?i:a]||[]}}return[]},getTreeParentRow(l){var t=W.treeConfig,r=G.fullAllDataRowIdData;if(l&&t){let e;if(e=_xeUtils.default.isString(l)?l:(0,_util.getRowid)(ne,l))return(t=r[e])?t.parent:null}return null},getParentRow(e){return(0,_log.warnLog)("vxe.error.delFunc",["getParentRow","getTreeParentRow"]),ne.getTreeParentRow(e)},getRowById(e){var l=G.fullAllDataRowIdData,e=_xeUtils.default.eqNull(e)?"":encodeURIComponent(e||"");return l[e]?l[e].row:null},getRowid(e){return(0,_util.getRowid)(ne,e)},getTableData(){var{tableData:e,footerTableData:l}=N,{tableFullData:t,afterFullData:r,tableFullTreeData:a}=G;return{fullData:(W.treeConfig?a:t).slice(0),visibleData:r.slice(0),tableData:e.slice(0),footerData:l.slice(0)}},getFullData(){var e,l,t,r,a=W.treeConfig,{tableFullData:o,tableFullTreeData:i}=G;return a?({transform:e,mapChildrenField:l,rowField:t,parentField:r}=a=ie.value,a=a.children||a.childrenField,e?_xeUtils.default.toArrayTree(_xeUtils.default.toTreeArray(i,{children:l}),{key:t,parentKey:r,children:a,mapChildren:l}):i.slice(0)):o.slice(0)},setColumnFixed(e,l){let t=!1;var r=_xeUtils.default.isArray(e)?e:[e],a=Z.value,o=fl.value;for(let e=0;e<r.length;e++){var i=r[e],i=(0,_util.handleFieldOrColumn)(ne,i),i=(0,_util.getRootColumn)(ne,i);if(i&&i.fixed!==l){if(!i.fixed&&o)return _ui.VxeUI.modal&&_ui.VxeUI.modal.message({status:"error",content:getI18n("vxe.table.maxFixedCol",[a.maxFixedSize])}),(0,_vue.nextTick)();_xeUtils.default.eachTree([i],e=>{e.fixed=l,e.renderFixed=l}),V.saveCustomStore("update:fixed"),t=t||!0}}return t?$.refreshColumn():(0,_vue.nextTick)()},clearColumnFixed(e){let l=!1;return(_xeUtils.default.isArray(e)?e:[e]).forEach(e=>{e=(0,_util.handleFieldOrColumn)(ne,e),e=(0,_util.getRootColumn)(ne,e);e&&e.fixed&&(_xeUtils.default.eachTree([e],e=>{e.fixed=null,e.renderFixed=null}),V.saveCustomStore("update:fixed"),l=l||!0)}),l?$.refreshColumn():(0,_vue.nextTick)()},hideColumn(e){let l=!1;return(_xeUtils.default.isArray(e)?e:[e]).forEach(e=>{e=(0,_util.handleFieldOrColumn)(ne,e);e&&e.visible&&(e.visible=!1,l=l||!0)}),l?V.handleCustom():(0,_vue.nextTick)()},showColumn(e){let l=!1;return(_xeUtils.default.isArray(e)?e:[e]).forEach(e=>{e=(0,_util.handleFieldOrColumn)(ne,e);e&&!e.visible&&(e.visible=!0,l=l||!0)}),l?V.handleCustom():(0,_vue.nextTick)()},setColumnWidth(e,l){var t=G.elemStore;let r=!1;e=_xeUtils.default.isArray(e)?e:[e];let a=_xeUtils.default.toInteger(l);return(0,_dom.isScale)(l)&&(t=(l=(0,_util.getRefElem)(t["main-body-scroll"]))?l.clientWidth-1:0,a=Math.floor(a*t)),(a&&(e.forEach(e=>{e=(0,_util.handleFieldOrColumn)(ne,e);e&&(e.resizeWidth=a,r=r||!0)}),r)?ne.refreshColumn():(0,_vue.nextTick)()).then(()=>({status:r}))},getColumnWidth(e){e=(0,_util.handleFieldOrColumn)(ne,e);return e?e.renderWidth:0},resetColumn(e){return(0,_log.warnLog)("vxe.error.delFunc",["resetColumn","resetCustom"]),ne.resetCustom(e)},refreshColumn(e){return e&&ft(),xt(!0).then(()=>$.refreshScroll()).then(()=>$.recalculate())},setRowHeightConf(e){let t=G.fullAllDataRowIdData,r=!1;return e&&(_xeUtils.default.each(e,(e,l)=>{l=t[l];l&&(e=_xeUtils.default.toInteger(e))&&(l.resizeHeight=e,r=r||!0)}),r)&&(G.isResizeCellHeight=!0,N.resizeHeightFlag++),(0,_vue.nextTick)().then(()=>(U(),{status:r}))},getRowHeightConf(r){let{fullAllDataRowIdData:a,afterFullData:e}=G,o=(0,_util.createHandleGetRowId)(ne).handleGetRowId,i=J.value,n=F.value,d=D.value,u={};return e.forEach(e=>{var l,e=o(e),t=a[e];t&&((l=t.resizeHeight)||r)&&(l=l||n.height||i.height||t.height||d,u[e]=l)}),u},setRowHeight(e,l){let t=G.fullAllDataRowIdData,r=!1;e=_xeUtils.default.isArray(e)?e:[e];let a=_xeUtils.default.toInteger(l);if((0,_dom.isScale)(l)&&(l=(l=(l=m.value)?l.$el:null)?l.clientHeight-1:0,a=Math.floor(a*l)),a){let l=(0,_util.createHandleGetRowId)(ne).handleGetRowId;e.forEach(e=>{e=_xeUtils.default.isString(e)||_xeUtils.default.isNumber(e)?e:l(e),e=t[e];e&&(e.resizeHeight=a,r=r||!0)}),r&&(G.isResizeCellHeight=!0,N.resizeHeightFlag++)}return(0,_vue.nextTick)().then(()=>({status:r}))},getRowHeight(e){var l=G.fullAllDataRowIdData,t=J.value,r=F.value,a=D.value,l=l[_xeUtils.default.isString(e)||_xeUtils.default.isNumber(e)?e:(0,_util.getRowid)(ne,e)];return l?l.resizeHeight||r.height||t.height||l.height||a:0},refreshScroll(){let{elemStore:e,lastScrollLeft:l,lastScrollTop:t}=G,r=(0,_util.getRefElem)(e["main-header-scroll"]),a=(0,_util.getRefElem)(e["main-body-scroll"]),o=(0,_util.getRefElem)(e["main-footer-scroll"]),i=(0,_util.getRefElem)(e["left-body-scroll"]),n=(0,_util.getRefElem)(e["right-body-scroll"]),d=T.value,u=S.value;return new Promise(e=>{if(l||t)return(0,_util.restoreScrollLocation)(ne,l,t).then(()=>{setTimeout(e,10)});G.intoRunScroll=!0,(0,_dom.setScrollTop)(u,t),(0,_dom.setScrollTop)(a,t),(0,_dom.setScrollTop)(i,t),(0,_dom.setScrollTop)(n,t),(0,_dom.setScrollLeft)(d,l),(0,_dom.setScrollLeft)(a,l),(0,_dom.setScrollLeft)(r,l),(0,_dom.setScrollLeft)(o,l),setTimeout(()=>{G.intoRunScroll=!1,e()},10)})},recalculate(o){return new Promise(e=>{var{rceTimeout:l,rceRunTime:t}=G,r=Ge.value.refreshDelay||20,a=Y.value;a&&a.clientWidth&&(Wl(),It()),!l||(clearTimeout(l),t&&t+(r-5)<Date.now())?e(nt(!!o)):(0,_vue.nextTick)(()=>{e()}),G.rceTimeout=setTimeout(()=>{G.rceTimeout=void 0,nt(!!o)},r)})},openTooltip(e,l){var t=pe.value;return t&&t.open?t.open(e,l):(0,_vue.nextTick)()},closeTooltip(){var e=N.tooltipStore,l=q.value,t=pe.value;return e.visible&&(Object.assign(e,{row:null,column:null,content:null,visible:!1,currOpts:{}}),l)&&l.close&&l.close(),t&&t.close&&t.close(),(0,_vue.nextTick)()},isAllCheckboxChecked(){return N.isAllSelected},isAllCheckboxIndeterminate(){return!N.isAllSelected&&N.isIndeterminate},getCheckboxIndeterminateRecords(e){var l=W.treeConfig;let{fullDataRowIdData:a,treeIndeterminateRowMaps:o}=G;if(l){let t=[],r=[];return _xeUtils.default.each(o,(e,l)=>{e&&(t.push(e),a[l])&&r.push(e)}),e?t:r}return[]},setCheckboxRow(e,l){return e&&!_xeUtils.default.isArray(e)&&(e=[e]),Ql(e,l,!0)},setCheckboxRowKey(e,l){let t=G.fullAllDataRowIdData,r=(_xeUtils.default.isArray(e)||(e=[e]),[]);return e.forEach(e=>{e=t[e];e&&r.push(e.row)}),Ql(r,l,!0)},isCheckedByCheckboxRow(e){var l=N.updateCheckboxFlag,t=G.selectCheckboxMaps,r=le.value.checkField;return r?_xeUtils.default.get(e,r):!!l&&!!t[(0,_util.getRowid)(ne,e)]},isCheckedByCheckboxRowKey(e){var l=N.updateCheckboxFlag,{fullAllDataRowIdData:t,selectCheckboxMaps:r}=G,a=le.value.checkField;return a?!!(t=t[e])&&_xeUtils.default.get(t.row,a):!!l&&!!r[e]},isIndeterminateByCheckboxRow(e){var l=G.treeIndeterminateRowMaps;return!!l[(0,_util.getRowid)(ne,e)]&&!ne.isCheckedByCheckboxRow(e)},isIndeterminateByCheckboxRowKey(e){var l=G.treeIndeterminateRowMaps;return!!l[e]&&!ne.isCheckedByCheckboxRowKey(e)},toggleCheckboxRow(e){var l=G.selectCheckboxMaps,t=le.value.checkField,t=t?!_xeUtils.default.get(e,t):!l[(0,_util.getRowid)(ne,e)];return V.handleBatchSelectRows([e],t,!0),V.checkSelectionStatus(),(0,_vue.nextTick)()},setAllCheckboxRow(e){return et(e,!0)},getRadioReserveRecord(e){var r=W.treeConfig,{fullDataRowIdData:l,radioReserveRow:a,afterFullData:o}=G,t=M.value,i=ie.value,i=i.children||i.childrenField;if(t.reserve&&a){let t=(0,_util.getRowid)(ne,a);if(e){if(!l[t])return a}else{let l=(0,_util.getRowkey)(ne);if(r){if(_xeUtils.default.findTree(o,e=>t===_xeUtils.default.get(e,l),{children:i}))return a}else if(!o.some(e=>t===_xeUtils.default.get(e,l)))return a}}return null},clearRadioReserve(){return(G.radioReserveRow=null,_vue.nextTick)()},getCheckboxReserveRecords(r){var e=W.treeConfig;let{afterFullData:a,fullDataRowIdData:o,checkboxReserveRowMap:i}=G;var l=le.value,n=ie.value,n=n.children||n.childrenField;let d=[];if(l.reserve){let l=(0,_util.createHandleGetRowId)(ne).handleGetRowId,t={};e?_xeUtils.default.eachTree(a,e=>{t[l(e)]=1},{children:n}):a.forEach(e=>{t[l(e)]=1}),_xeUtils.default.each(i,(e,l)=>{e&&(r?o[l]||d.push(e):t[l]||d.push(e))})}return d},clearCheckboxReserve(){return G.checkboxReserveRowMap={},(0,_vue.nextTick)()},toggleAllCheckboxRow(){return Et(null,!N.isAllSelected),(0,_vue.nextTick)()},clearCheckboxRow(){let l=W.treeConfig;var e=G.tableFullData,t=ie.value,t=t.children||t.childrenField,r=le.value;let{checkField:a,reserve:o}=r,i=r.indeterminateField||r.halfField;return a&&(r=e=>{l&&i&&_xeUtils.default.set(e,i,!1),_xeUtils.default.set(e,a,!1)},l?_xeUtils.default.eachTree(e,r,{children:t}):e.forEach(r)),o&&e.forEach(e=>Zl(e,!1)),N.isAllSelected=!1,N.isIndeterminate=!1,G.selectCheckboxMaps={},G.treeIndeterminateRowMaps={},N.updateCheckboxFlag++,(0,_vue.nextTick)()},setCurrentRow(e){var l=J.value,t=Y.value;return $.clearCurrentRow(),N.currentRow=e,(l.isCurrent||W.highlightCurrentRow)&&t&&_xeUtils.default.arrayEach(t.querySelectorAll(`[rowid="${(0,_util.getRowid)(ne,e)}"]`),e=>(0,_dom.addClass)(e,"row--current")),(0,_vue.nextTick)()},isCheckedByRadioRow(e){var l=N.selectRadioRow;return!(!e||!l)&&ne.eqRow(l,e)},isCheckedByRadioRowKey(e){var l=N.selectRadioRow;return!!l&&e===(0,_util.getRowid)(ne,l)},setRadioRow(e){return Jl(e,!0)},setRadioRowKey(e){var l=G.fullAllDataRowIdData,l=l[e];return l?Jl(l.row,!0):(0,_vue.nextTick)()},clearCurrentRow(){var e=Y.value;return N.currentRow=null,G.hoverRow=null,e&&_xeUtils.default.arrayEach(e.querySelectorAll(".row--current"),e=>(0,_dom.removeClass)(e,"row--current")),(0,_vue.nextTick)()},clearRadioRow(){return(N.selectRadioRow=null,_vue.nextTick)()},getCurrentRecord(){return J.value.isCurrent||W.highlightCurrentRow?N.currentRow:null},getRadioRecord(e){var{fullDataRowIdData:l,afterFullRowMaps:t}=G,r=N.selectRadioRow;if(r){var a=(0,_util.getRowid)(ne,r);if(e){if(l[a])return r}else if(t[a])return r}return null},getCurrentColumn(){return Z.value.isCurrent||W.highlightCurrentColumn?N.currentColumn:null},setCurrentColumn(e){var l=W.mouseConfig,t=re.value;let r=l&&t.selected;l=(0,_util.handleFieldOrColumn)(ne,e);return l&&(ne.clearCurrentColumn(),N.currentColumn=l),(0,_vue.nextTick)().then(()=>{r&&ne.addCellSelectedClass()})},clearCurrentColumn(){return(N.currentColumn=null,_vue.nextTick)()},setPendingRow(e,l){let t=(0,_util.createHandleGetRowId)(ne).handleGetRowId,r=G.pendingRowMaps;return e&&!_xeUtils.default.isArray(e)&&(e=[e]),l?e.forEach(e=>{var l=t(e);l&&!r[l]&&(r[l]=e)}):e.forEach(e=>{e=t(e);e&&r[e]&&delete r[e]}),N.pendingRowFlag++,(0,_vue.nextTick)()},togglePendingRow(e){let t=(0,_util.createHandleGetRowId)(ne).handleGetRowId,r=G.pendingRowMaps;return(e=e&&!_xeUtils.default.isArray(e)?[e]:e).forEach(e=>{var l=t(e);l&&(r[l]?delete r[l]:r[l]=e)}),N.pendingRowFlag++,(0,_vue.nextTick)()},hasPendingByRow(e){return $.isPendingByRow(e)},isPendingByRow(e){var l=G.pendingRowMaps;return!!l[(0,_util.getRowid)(ne,e)]},getPendingRecords(){let{fullAllDataRowIdData:t,pendingRowMaps:e}=G,r=[];return _xeUtils.default.each(e,(e,l)=>{t[l]&&r.push(e)}),r},clearPendingRow(){return G.pendingRowMaps={},N.pendingRowFlag++,(0,_vue.nextTick)()},sort(e,l){let{multiple:t,remote:r,orders:o}=L.value;return e&&_xeUtils.default.isString(e)&&(e=[{field:e,order:l}]),(e=_xeUtils.default.isArray(e)?e:[e]).length?(t||Hl(),(t?e:[e[0]]).forEach((e,l)=>{let{field:t,order:r}=e,a=t;(a=_xeUtils.default.isString(t)?$.getColumnByField(t):a)&&a.sortable&&(o&&-1===o.indexOf(r)&&(r=yl(a)),a.order!==r&&(a.order=r),a.sortTime=Date.now()+l)}),r||V.handleTableData(!0),(0,_vue.nextTick)().then(()=>(U(),$.updateCellAreas(),k()))):(0,_vue.nextTick)()},setSort(e,l){return Al(new Event("click"),e,l)},setSortByEvent(e,l,t){return Al(e,l,t)},clearSort(e){var l=L.value;return e?(e=(0,_util.handleFieldOrColumn)(ne,e))&&(e.order=null):Hl(),l.remote||ne.handleTableData(!0),(0,_vue.nextTick)().then(()=>(U(),k()))},clearSortByEvent(e,l){var t=G.tableFullColumn,r=L.value;let a=[],o=null;return e&&(l?(o=(0,_util.handleFieldOrColumn)(ne,l))&&(o.order=null):t.forEach(e=>{e.order&&(e.order=null,a.push(e))}),r.remote||ne.handleTableData(!0),a.length?(l={$table:ne,$event:e,cols:a,sortList:[]},de("clear-all-sort",l,e)):o&&ne.handleColumnSortEvent(e,o)),(0,_vue.nextTick)().then(()=>(U(),k()))},isSort(e){return e?!!(e=(0,_util.handleFieldOrColumn)(ne,e))&&e.sortable&&!!e.order:0<$.getSortColumns().length},getSortColumns(){var{multiple:e,chronological:l}=L.value;let r=[];var t=G.tableFullColumn;return t.forEach(e=>{var{field:l,order:t}=e;e.sortable&&t&&r.push({column:e,field:l,property:l,order:t,sortTime:e.sortTime})}),e&&l&&1<r.length?_xeUtils.default.orderBy(r,"sortTime"):r},setFilterByEvent(e,l,t,r){l=(0,_util.handleFieldOrColumn)(ne,l);return l&&l.filters&&(l.filters=(0,_util.toFilters)(t||[]),r)?ne.handleColumnConfirmFilter(l,e):(0,_vue.nextTick)()},closeFilter(){var e=N.filterStore,{column:l,visible:t}=e;return e.isAllSelected=!1,e.isIndeterminate=!1,e.options=[],e.visible=!1,t&&de("filter-visible",{column:l,property:l.field,field:l.field,filterList:()=>ne.getCheckedFilters(),visible:!1},null),(0,_vue.nextTick)()},isActiveFilterByColumn(e){e=(0,_util.handleFieldOrColumn)(ne,e);return e?e.filters&&e.filters.some(e=>e.checked):0<ne.getCheckedFilters().length},isFilter(e){return $.isActiveFilterByColumn(e)},clearFilterByEvent(e,l){var t=N.filterStore,r=G.tableFullColumn,a=qe.value;let o=[],i=null;return l?(i=(0,_util.handleFieldOrColumn)(ne,l))&&ne.handleClearFilter(i):r.forEach(e=>{e.filters&&(o.push(e),ne.handleClearFilter(e))}),l&&i===t.column||Object.assign(t,{isAllSelected:!1,isIndeterminate:!1,style:null,options:[],column:null,multiple:!1,visible:!1}),a.remote||ne.updateData(),o.length?(r={$table:ne,$event:e,cols:o,filterList:[]},de("clear-all-filter",r,e)):i&&ne.dispatchEvent("clear-filter",{filterList:()=>ne.getCheckedFilters()},e),(0,_vue.nextTick)()},isRowExpandLoaded(e){var l=G.fullAllDataRowIdData,l=l[(0,_util.getRowid)(ne,e)];return l&&!!l.expandLoaded},clearRowExpandLoaded(e){var{fullAllDataRowIdData:l,rowExpandLazyLoadedMaps:t}=G,r=oe.value.lazy,e=(0,_util.getRowid)(ne,e),l=l[e];return r&&l&&(l.expandLoaded=!1,delete t[e]),N.rowExpandedFlag++,(0,_vue.nextTick)()},reloadRowExpand(e){var l=G.rowExpandLazyLoadedMaps,t=oe.value.lazy,r=(0,_util.getRowid)(ne,e);return t&&!l[r]&&ne.clearRowExpandLoaded(e).then(()=>rt(e)),(0,_vue.nextTick)()},reloadExpandContent(e){return(0,_log.warnLog)("vxe.error.delFunc",["reloadExpandContent","reloadRowExpand"]),ne.reloadRowExpand(e)},toggleRowExpand(e){return ne.setRowExpand(e,!ne.isRowExpandByRow(e))},setAllRowExpand(e){var l=ie.value,{tableFullData:t,tableFullTreeData:r}=G,l=l.children||l.childrenField;let a=[];return W.treeConfig?_xeUtils.default.eachTree(r,e=>{a.push(e)},{children:l}):a=t,$.setRowExpand(a,e)},setRowExpand(e,l){let t=N.expandColumn,{fullAllDataRowIdData:r,rowExpandedMaps:a,rowExpandLazyLoadedMaps:o}=G,i=(0,_util.createHandleGetRowId)(ne).handleGetRowId;let{reserve:n,lazy:d,accordion:u,toggleMethod:s}=oe.value,c=[],g=t?ne.getColumnIndex(t):-1,v=t?ne.getVMColumnIndex(t):-1;return e&&(_xeUtils.default.isArray(e)||(e=[e]),u&&(a={},G.rowExpandedMaps=a,e=e.slice(e.length-1,e.length)),e=s?e.filter(e=>s({$table:ne,expanded:l,column:t,columnIndex:g,$columnIndex:v,row:e,rowIndex:ne.getRowIndex(e),$rowIndex:ne.getVMRowIndex(e)})):e,l?e.forEach(e=>{var l,t=i(e);a[t]||(l=r[t],d&&!l.expandLoaded&&!o[t]?c.push(rt(e)):a[t]=e)}):e.forEach(e=>{e=i(e);a[e]&&delete a[e]}),n)&&e.forEach(e=>at(e,l)),N.rowExpandedFlag++,Promise.all(c).then(()=>(0,_vue.nextTick)()).then(()=>ne.recalculate(!0)).then(()=>(U(),It(),Mt(),ne.updateCellAreas()))},isRowExpandByRow(e){var l=N.rowExpandedFlag,t=G.rowExpandedMaps,e=(0,_util.getRowid)(ne,e);return!!l&&!!t[e]},isExpandByRow(e){return(0,_log.warnLog)("vxe.error.delFunc",["isExpandByRow","isRowExpandByRow"]),$.isRowExpandByRow(e)},clearRowExpand(){var e=G.tableFullData,l=oe.value.reserve;let t=ne.getRowExpandRecords();return G.rowExpandedMaps={},N.rowExpandedFlag++,l&&e.forEach(e=>at(e,!1)),(0,_vue.nextTick)().then(()=>{if(t.length)return ne.recalculate(!0)}).then(()=>(U(),It(),Mt(),ne.updateCellAreas()))},clearRowExpandReserve(){return G.rowExpandedReserveRowMap={},(0,_vue.nextTick)()},getRowExpandRecords(){let l=[];return _xeUtils.default.each(G.rowExpandedMaps,e=>{e&&l.push(e)}),l},setRowGroups(e){var{aggregateConfig:l,rowGroupConfig:t}=W;return l||t?e?(dt((_xeUtils.default.isArray(e)?e:[e]).map(e=>_xeUtils.default.isString(e)?e:e.field)),ct(G.tableSynchData,!0)):(0,_vue.nextTick)():((0,_log.errLog)("vxe.error.reqProp",["aggregate-config"]),(0,_vue.nextTick)())},clearRowGroups(){var{aggregateConfig:e,rowGroupConfig:l}=W;return e||l?(dt([]),ct(G.tableSynchData,!0)):((0,_log.errLog)("vxe.error.reqProp",["aggregate-config"]),(0,_vue.nextTick)())},isRowGroupRecord(e){return(0,_log.warnLog)("vxe.error.delFunc",["isRowGroupRecord","isAggregateRecord"]),ne.isAggregateRecord(e)},isRowGroupExpandByRow(e){return(0,_log.warnLog)("vxe.error.delFunc",["isRowGroupExpandByRow","isAggregateExpandByRow"]),ne.isAggregateExpandByRow(e)},isAggregateRecord(e){var l=N.isRowGroupStatus;return l&&e.isAggregate},isAggregateExpandByRow(e){var l=N.rowGroupExpandedFlag,t=G.rowGroupExpandedMaps;return!!l&&!!t[(0,_util.getRowid)(ne,e)]},setRowGroupExpand(e,l){return e?(_xeUtils.default.isArray(e)||(e=[e]),Rt(e,l)):(0,_vue.nextTick)()},setAllRowGroupExpand(e){var l=G.tableFullGroupData;let t=I.value.mapChildrenField,r={};return e&&t&&_xeUtils.default.eachTree(l,e=>{e[t]&&e[t].length&&(r[(0,_util.getRowid)(ne,e)]=e)},{children:t}),G.rowGroupExpandedMaps=r,n(),N.rowGroupExpandedFlag++,ne.handleTableData()},clearRowGroupExpand(){return G.rowGroupExpandedMaps={},n(),N.rowGroupExpandedFlag++,ne.handleTableData()},getTreeExpandRecords(){let l=[];return _xeUtils.default.each(G.treeExpandedMaps,e=>{e&&l.push(e)}),l},isTreeExpandLoaded(e){var l=G.fullAllDataRowIdData,l=l[(0,_util.getRowid)(ne,e)];return l&&!!l.treeLoaded},clearTreeExpandLoaded(e){let{fullAllDataRowIdData:t,treeExpandedMaps:r}=G;var l=ie.value.transform;return e?(e=_xeUtils.default.isArray(e)?e:[e]).forEach(e=>{var e=(0,_util.getRowid)(ne,e),l=t[e];l&&(l.treeLoaded=!1,r[e])&&delete r[e]}):_xeUtils.default.each(t,e=>{e.treeLoaded=!1}),G.treeExpandedMaps={},l&&(n(),ne.handleTableData()),N.treeExpandedFlag++,(0,_vue.nextTick)()},reloadTreeExpand(e){var l=G.treeExpandLazyLoadedMaps,t=ie.value,r=t.hasChild||t.hasChildField;let{transform:a,lazy:o}=t;t=(0,_util.getRowid)(ne,e);return o&&e[r]&&!l[t]?ne.clearTreeExpandLoaded(e).then(()=>lt(e)).then(()=>{a&&(n(),ne.handleTableData()),N.treeExpandedFlag++}).then(()=>ne.recalculate()):(0,_vue.nextTick)()},reloadTreeChilds(e){return(0,_log.warnLog)("vxe.error.delFunc",["reloadTreeChilds","reloadTreeExpand"]),ne.reloadTreeExpand(e)},toggleTreeExpand(e){return ne.setTreeExpand(e,!ne.isTreeExpandByRow(e))},setAllTreeExpand(e){var l=G.tableFullData,t=ie.value;let{transform:r,lazy:a}=t,o=t.children||t.childrenField,i=[];return _xeUtils.default.eachTree(l,e=>{var l=e[o];(a||l&&l.length)&&i.push(e)},{children:o}),ne.setTreeExpand(i,e).then(()=>{if(r)return n(),N.treeExpandedFlag++,ne.recalculate()})},setTreeExpand(e,l){var t,r=ie.value.transform;return e&&(e=_xeUtils.default.isArray(e)?e:[e]).length?r?(r=e,t=l,bt(r,t).then(()=>(n(),ne.handleTableData(),N.treeExpandedFlag++,ql(),(0,_vue.nextTick)())).then(()=>ne.recalculate(!0)).then(()=>{setTimeout(()=>{ne.updateCellAreas()},30)})):bt(e,l):(0,_vue.nextTick)()},isTreeExpandByRow(e){var l=N.treeExpandedFlag,t=G.treeExpandedMaps;return!!l&&!!t[(0,_util.getRowid)(ne,e)]},clearTreeExpand(){var e=G.tableFullTreeData,l=ie.value,t=l.children||l.childrenField;let{transform:r,reserve:a}=l,o=ne.getTreeExpandRecords();return G.treeExpandedMaps={},a&&_xeUtils.default.eachTree(e,e=>tt(e,!1),{children:t}),ne.handleTableData().then(()=>{r&&(n(),ne.handleTableData()),N.treeExpandedFlag++}).then(()=>{if(o.length)return ne.recalculate()})},clearTreeExpandReserve(){return G.treeExpandedReserveRowMap={},(0,_vue.nextTick)()},getScroll(){var{scrollXLoad:e,scrollYLoad:l}=N,t=G.elemStore,t=(0,_util.getRefElem)(t["main-body-scroll"]);return{virtualX:e,virtualY:l,scrollTop:t?t.scrollTop:0,scrollLeft:t?t.scrollLeft:0}},scrollTo(e,l){var t=G.elemStore,r=(0,_util.getRefElem)(t["main-header-scroll"]),a=(0,_util.getRefElem)(t["main-body-scroll"]),o=(0,_util.getRefElem)(t["main-footer-scroll"]),i=(0,_util.getRefElem)(t["left-body-scroll"]),t=(0,_util.getRefElem)(t["right-body-scroll"]),n=T.value,d=S.value;return G.intoRunScroll=!0,_xeUtils.default.isNumber(e)&&((0,_dom.setScrollLeft)(n,e),(0,_dom.setScrollLeft)(a,e),(0,_dom.setScrollLeft)(r,e),(0,_dom.setScrollLeft)(o,e),pt()),_xeUtils.default.isNumber(l)&&((0,_dom.setScrollTop)(d,l),(0,_dom.setScrollTop)(a,l),(0,_dom.setScrollTop)(i,l),(0,_dom.setScrollTop)(t,l),yt()),N.scrollXLoad||N.scrollYLoad?new Promise(e=>{setTimeout(()=>{(0,_vue.nextTick)(()=>{G.intoRunScroll=!1,e()})},30)}):(0,_vue.nextTick)().then(()=>{G.intoRunScroll=!1})},scrollToRow(e,l){let{isAllOverflow:t,scrollYLoad:r,scrollXLoad:a}=N;var o,i,n=[];return e&&(W.treeConfig?n.push(ne.scrollToTreeRow(e)):n.push((0,_util.rowToVisible)(ne,e))),l&&n.push((l=l,o=e,i=G.fullColumnIdData,(l=(0,_util.handleFieldOrColumn)(ne,l))&&i[l.id]?(0,_util.colToVisible)(ne,l,o):(0,_vue.nextTick)())),Promise.all(n).then(()=>{if(e)return t||!r&&!a||(Vl(),$l()),(0,_vue.nextTick)()})},scrollToColumn(e){var l=G.fullColumnIdData,e=(0,_util.handleFieldOrColumn)(ne,e);return e&&l[e.id]?(0,_util.colToVisible)(ne,e):(0,_vue.nextTick)()},clearScroll(){var{elemStore:e,scrollXStore:l,scrollYStore:t}=G,r=(0,_util.getRefElem)(e["main-header-scroll"]),a=(0,_util.getRefElem)(e["main-body-scroll"]),o=(0,_util.getRefElem)(e["main-footer-scroll"]),i=(0,_util.getRefElem)(e["left-body-scroll"]),e=(0,_util.getRefElem)(e["right-body-scroll"]),n=T.value,d=S.value;return G.intoRunScroll=!0,(0,_dom.setScrollLeft)(n,0),(0,_dom.setScrollLeft)(a,0),(0,_dom.setScrollLeft)(r,0),(0,_dom.setScrollLeft)(o,0),(0,_dom.setScrollTop)(d,0),(0,_dom.setScrollTop)(a,0),(0,_dom.setScrollTop)(i,0),(0,_dom.setScrollTop)(e,0),l.startIndex=0,l.visibleStartIndex=0,l.endIndex=l.visibleSize,l.visibleEndIndex=l.visibleSize,t.startIndex=0,t.visibleStartIndex=0,t.endIndex=t.visibleSize,t.visibleEndIndex=t.visibleSize,(0,_vue.nextTick)().then(()=>{G.intoRunScroll=!1})},updateFooter(){var{showFooter:e,footerData:l,footerMethod:t}=W,{visibleColumn:r,afterFullData:a}=G;let o=[];return e&&l&&l.length?o=l.slice(0):e&&t&&(o=r.length?t({columns:r,data:a,$table:ne,$grid:K}):[]),N.footerTableData=o,ne.handleUpdateFooterMerge(),(0,_vue.nextTick)()},updateStatus(l,t){return(0,_vue.nextTick)().then(()=>{var e=W.editRules;if(l&&e)return ne.handleCellRuleUpdateStatus("change",l,t)})},setMergeCells(e){return W.spanMethod&&(0,_log.errLog)("vxe.error.errConflicts",["merge-cells","span-method"]),(l=>{let{fullAllDataRowIdData:n,fullColumnIdData:d,visibleColumn:u,afterFullData:s,mergeBodyList:c,mergeBodyMaps:g}=G;if(l){let e=(0,_util.createHandleGetRowId)(ne).handleGetRowId;(l=_xeUtils.default.isArray(l)?l:[l]).forEach(l=>{var{row:l,col:t,rowspan:r,colspan:a}=l;let o=-1,i=-1;if(_xeUtils.default.isNumber(l)?o=l:(l=(l=l?e(l):null)?n[l]:null)&&(o=l._index),_xeUtils.default.isNumber(t)?i=t:(t=(l=t?t.id:null)?d[l]:null)&&(i=t._index),-1<o&&-1<i&&(r||a)&&(r=_xeUtils.default.toNumber(r)||1,a=_xeUtils.default.toNumber(a)||1,1<r||1<a)){l=s[o],t=u[i];let e=g[o+":"+i];e?(e.rowspan=r,e.colspan=a,e._rowspan=r,e._colspan=a):(e={row:o,col:i,rowspan:r,colspan:a,_row:l,_col:t,_rowspan:r,_colspan:a},g[o+":"+i]=e,c.push(e))}})}})(e),ne.handleUpdateBodyMerge(),(0,_vue.nextTick)().then(()=>(ne.updateCellAreas(),k()))},removeMergeCells(e){W.spanMethod&&(0,_log.errLog)("vxe.error.errConflicts",["merge-cells","span-method"]);let l=(e=>{let{mergeBodyList:o,fullColumnIdData:i,fullAllDataRowIdData:n,mergeBodyMaps:d}=G,u=[];if(e){let a=(0,_util.createHandleGetRowId)(ne).handleGetRowId;(e=_xeUtils.default.isArray(e)?e:[e]).forEach(e=>{var{row:e,col:l}=e;let t=-1,r=-1;_xeUtils.default.isNumber(e)?t=e:(e=(e=e?a(e):null)?n[e]:null)&&(t=e._index),_xeUtils.default.isNumber(l)?r=l:(l=(e=l?l.id:null)?i[e]:null)&&(r=l._index);l=_xeUtils.default.findIndexOf(o,e=>e.row===t&&e.col===r);if(-1<l){l=o.splice(l,1);let e=l[0];e&&(u.push(l[0]),d[t+":"+r])&&delete d[t+":"+r]}})}return u})(e);return ne.handleUpdateBodyMerge(),(0,_vue.nextTick)().then(()=>(ne.updateCellAreas(),k(),l))},getMergeCells(){return G.mergeBodyList.slice(0)},clearMergeCells(){return G.mergeBodyList=[],G.mergeBodyMaps={},G.mergeBodyCellMaps={},N.mergeBodyFlag++,(0,_vue.nextTick)().then(()=>k())},setMergeFooterItems(e){return W.footerSpanMethod&&(0,_log.errLog)("vxe.error.errConflicts",["merge-footer-items","footer-span-method"]),(e=>{let d=N.footerTableData,{mergeFooterList:u,mergeFooterMaps:s,fullColumnIdData:c}=G;if(e){let n=G.visibleColumn;(e=_xeUtils.default.isArray(e)?e:[e]).forEach(l=>{var{row:l,col:t,rowspan:r,colspan:a}=l,l=_xeUtils.default.isNumber(l)?l:-1;let o=-1;if(_xeUtils.default.isNumber(t)?o=t:(t=(t=t?t.id:null)?c[t]:null)&&(o=t._index),-1<l&&-1<o&&(r||a)&&(r=_xeUtils.default.toNumber(r)||1,a=_xeUtils.default.toNumber(a)||1,1<r||1<a)){var t=d[l],i=n[o];let e=s[l+":"+o];e?(e.rowspan=r,e.colspan=a,e._rowspan=r,e._colspan=a):(e={row:l,col:o,rowspan:r,colspan:a,_row:t,_col:i,_rowspan:r,_colspan:a},s[l+":"+o]=e,u.push(e))}})}})(e),ne.handleUpdateFooterMerge(),(0,_vue.nextTick)().then(()=>($.updateCellAreas(),k()))},removeMergeFooterItems(e){W.footerSpanMethod&&(0,_log.errLog)("vxe.error.errConflicts",["merge-footer-items","footer-span-method"]);let l=(e=>{let{mergeFooterList:a,fullColumnIdData:o,mergeFooterMaps:i}=G,n=[];return e&&(e=_xeUtils.default.isArray(e)?e:[e]).forEach(e=>{var{row:e,col:l}=e;let t=_xeUtils.default.isNumber(e)?e:-1,r=-1;_xeUtils.default.isNumber(l)?r=l:(l=(e=l?l.id:null)?o[e]:null)&&(r=l._index);l=_xeUtils.default.findIndexOf(a,e=>e.row===t&&e.col===r);if(-1<l){let e=a.splice(l,1)[0];e&&(n.push(e),i[t+":"+r])&&delete i[t+":"+r]}}),n})(e);return ne.handleUpdateFooterMerge(),(0,_vue.nextTick)().then(()=>($.updateCellAreas(),k(),l))},getMergeFooterItems(){return G.mergeFooterList.slice(0)},clearMergeFooterItems(){return G.mergeFooterList=[],G.mergeFooterMaps={},G.mergeFooterCellMaps={},N.mergeFootFlag++,(0,_vue.nextTick)().then(()=>k())},updateCellAreas(){var e=W.mouseConfig,l=re.value;return e&&l.area&&ne.handleRecalculateCellAreaEvent?ne.handleRecalculateCellAreaEvent():(0,_vue.nextTick)()},getCustomStoreData(){var e=W.id,l=cl.value,t=G.collectColumn;let{storage:r,checkMethod:s,storeOptions:a}=l;var l=!0===r,o=l?{}:Object.assign({},r||{},a);let c=_(o.resizable,l),g=_(o.visible,l),v=_(o.fixed,l),h=_(o.sort,l),f={},p={},m={},x={};o={resizableData:void 0,sortData:void 0,visibleData:void 0,fixedData:void 0};if(e){let i=0,n=0,d=0,u=0;_xeUtils.default.eachTree(t,(e,l,t,r,a)=>{var o=e.getKey();o?(a||(h&&(n=1,p[o]=e.renderSortNumber),v&&e.fixed!==e.defaultFixed&&(d=1,x[o]=e.fixed)),c&&e.resizeWidth&&(i=1,f[o]=e.renderWidth),!g||s&&!s({$table:ne,column:e})||(!e.visible&&e.defaultVisible?(u=1,m[o]=!1):e.visible&&!e.defaultVisible&&(u=1,m[o]=!0))):(0,_log.errLog)("vxe.error.reqProp",[`${e.getTitle()||e.type||""} -> column.field=?`])}),i&&(o.resizableData=f),n&&(o.sortData=p),d&&(o.fixedData=x),u&&(o.visibleData=m)}else(0,_log.errLog)("vxe.error.reqProp",["id"]);return o},focus(){return G.isActivated=!0,(0,_vue.nextTick)()},blur(){return(G.isActivated=!1,_vue.nextTick)()},connect(e){return e?(b=e).syncUpdate({collectColumn:G.collectColumn,$table:ne}):(0,_log.errLog)("vxe.error.barUnableLink"),(0,_vue.nextTick)()}},t=>{var{editStore:e,ctxMenuStore:l,filterStore:r,customStore:a}=N;let{mouseConfig:o,editRules:i}=W,n=Y.value,d=te.value,u=Ae.value;var s=Xe.value;let c=e.actived;var e=me.value,g=_e.value,v=we.value,h=xe.value;if(!g||(0,_dom.getEventTargetNode)(t,n,"vxe-cell--filter").flag||(0,_dom.getEventTargetNode)(t,g.getRefMaps().refElem.value).flag||(0,_dom.getEventTargetNode)(t,document.body,"vxe-table--ignore-clear").flag||V.preventEvent(t,"event.clearFilter",r.args,$.closeFilter),!v||a.btnEl===t.target||(0,_dom.getEventTargetNode)(t,document.body,"vxe-toolbar-custom-target").flag||(0,_dom.getEventTargetNode)(t,v.$el).flag||(0,_dom.getEventTargetNode)(t,document.body,"vxe-table--ignore-clear").flag||V.preventEvent(t,"event.clearCustom",{},()=>{ne.closeCustom&&ne.closeCustom()}),c.row)!1===d.autoClear||(g=c.args.cell)&&(0,_dom.getEventTargetNode)(t,g).flag||e&&(0,_dom.getEventTargetNode)(t,e.$el).flag||G._lastCallTime&&!(G._lastCallTime+50<Date.now())||(0,_dom.getEventTargetNode)(t,document.body,"vxe-table--ignore-clear").flag||V.preventEvent(t,"event.clearEdit",c.args,()=>{let e;var l;(e=(e=(e="row"===d.mode?!!(l=(l=(0,_dom.getEventTargetNode)(t,n,"vxe-body--row")).flag?$.getRowNode(l.targetElem):null)&&!ne.eqRow(l.item,c.args.row):!(0,_dom.getEventTargetNode)(t,n,"col--edit").flag)||(0,_dom.getEventTargetNode)(t,n,"vxe-header--row").flag)||(0,_dom.getEventTargetNode)(t,n,"vxe-footer--row").flag)||!W.height||N.overflowY||(l=t.target,(0,_dom.hasClass)(l,"vxe-table--body-wrapper")&&(e=t.offsetY<l.clientHeight)),!e&&(0,_dom.getEventTargetNode)(t,n).flag||setTimeout(()=>{ne.handleClearEdit(t).then(()=>{!G.isActivated&&i&&u.autoClear&&(N.validErrorMaps={})})})});else if(o&&!((0,_dom.getEventTargetNode)(t,n).flag||K&&(0,_dom.getEventTargetNode)(t,K.getRefMaps().refElem.value).flag||h&&(0,_dom.getEventTargetNode)(t,h.getRefMaps().refElem.value).flag||b&&(0,_dom.getEventTargetNode)(t,b.getRefMaps().refElem.value).flag)&&(ne.clearSelected&&ne.clearSelected(),s.autoClear)&&ne.getCellAreas){let e=ne.getCellAreas();e&&e.length&&!(0,_dom.getEventTargetNode)(t,document.body,"vxe-table--ignore-areas-clear").flag&&V.preventEvent(t,"event.clearAreas",{},()=>{ne.clearCellAreas(),ne.clearCopyCellArea(),de("clear-cell-area-selection",{cellAreas:e},t)})}ne.closeMenu&&l.visible&&h&&!(0,_dom.getEventTargetNode)(t,h.getRefMaps().refElem.value).flag&&ne.closeMenu();r=(0,_dom.getEventTargetNode)(t,K?K.getRefMaps().refElem.value:n).flag;!r&&i&&u.autoClear&&(N.validErrorMaps={}),G.isActivated=r}),kt=()=>{$.closeFilter(),ne.closeMenu&&ne.closeMenu()},Ut=()=>{$.closeTooltip(),ne.closeMenu&&ne.closeMenu()},At=l=>{let{mouseConfig:e,keyboardConfig:t}=W,{filterStore:r,ctxMenuStore:a,editStore:o}=N,i=re.value,n=ae.value,d=o.actived;globalEvents.hasKey(l,GLOBAL_EVENT_KEYS.ESCAPE)&&V.preventEvent(l,"event.keydown",null,()=>{if(de("keydown-start",{},l),t&&e&&i.area&&ne.handleKeyboardCellAreaEvent)ne.handleKeyboardCellAreaEvent(l);else if((d.row||r.visible||a.visible)&&(l.stopPropagation(),ne.closeMenu&&ne.closeMenu(),$.closeFilter(),t)&&n.isEsc&&d.row){let e=d.args;ne.handleClearEdit(l),i.selected&&(0,_vue.nextTick)(()=>ne.handleSelected(e,l))}de("keydown",{},l),de("keydown-end",{},l)})},Ht=P=>{G.isActivated&&ne.preventEvent(P,"event.keydown",null,()=>{var{mouseConfig:e,keyboardConfig:l,treeConfig:t,editConfig:r,highlightCurrentRow:a,highlightCurrentColumn:o}=W;let{ctxMenuStore:i,editStore:H,currentRow:n}=N;var d=G.afterFullData,u=rl.value,s=ll.value,c=ae.value,g=re.value,v=te.value,h=ie.value,f=al.value,p=J.value,O=Z.value,{selected:m,actived:x}=H,h=h.children||h.childrenField,_=P.keyCode,w=globalEvents.hasKey(P,GLOBAL_EVENT_KEYS.ESCAPE),C=globalEvents.hasKey(P,GLOBAL_EVENT_KEYS.BACKSPACE),z=globalEvents.hasKey(P,GLOBAL_EVENT_KEYS.TAB),b=globalEvents.hasKey(P,GLOBAL_EVENT_KEYS.ENTER),R=globalEvents.hasKey(P,GLOBAL_EVENT_KEYS.SPACEBAR),E=globalEvents.hasKey(P,GLOBAL_EVENT_KEYS.ARROW_LEFT),y=globalEvents.hasKey(P,GLOBAL_EVENT_KEYS.ARROW_UP),T=globalEvents.hasKey(P,GLOBAL_EVENT_KEYS.ARROW_RIGHT),S=globalEvents.hasKey(P,GLOBAL_EVENT_KEYS.ARROW_DOWN),B=globalEvents.hasKey(P,GLOBAL_EVENT_KEYS.DELETE),D=globalEvents.hasKey(P,GLOBAL_EVENT_KEYS.F2),F=globalEvents.hasKey(P,GLOBAL_EVENT_KEYS.CONTEXT_MENU),I=(0,_dom.hasControlKey)(P),M=P.shiftKey,L=P.altKey,k=E||y||T||S,u=u&&i.visible&&(b||R||k),U=(0,_utils.isEnableConf)(r)&&x.column&&x.row;let A=v.beforeEditMethod||v.activeMethod;if(u)P.preventDefault(),i.showChild&&(0,_utils.hasChildrenList)(i.selected)?ne.moveCtxMenu(P,i,"selectChild",E,!1,i.selected.children):ne.moveCtxMenu(P,i,"selected",T,!0,f);else if(l&&e&&g.area&&ne.handleKeyboardCellAreaEvent)ne.handleKeyboardCellAreaEvent(P);else if(w){if(ne.closeMenu&&ne.closeMenu(),ne.closeFilter(),l&&c.isEsc&&x.row){let e=x.args;ne.handleClearEdit(P),g.selected&&(0,_vue.nextTick)(()=>ne.handleSelected(e,P))}}else if(R&&l&&c.isChecked&&m.row&&m.column&&("checkbox"===m.column.type||"radio"===m.column.type))P.preventDefault(),"checkbox"===m.column.type?V.handleToggleCheckRowEvent(P,m.args):V.triggerRadioRowEvent(P,m.args);else if(D&&(0,_utils.isEnableConf)(r))U||m.row&&m.column&&(P.preventDefault(),ne.handleEdit(m.args,P));else if(F)G._keyCtx=m.row&&m.column&&s.length,clearTimeout(G.keyCtxTimeout),G.keyCtxTimeout=setTimeout(()=>{G._keyCtx=!1},1e3);else if(b&&!L&&l&&c.isEnter&&(m.row||x.row||t&&(p.isCurrent||a)&&n)){var{isLastEnterAppendRow:u,beforeEnterMethod:f,enterMethod:e}=c;if(I){if(x.row){let e=x.args;ne.handleClearEdit(P),g.selected&&(0,_vue.nextTick)(()=>{ne.handleSelected(e,P)})}}else if(m.row||x.row){let t=(m.row?m:x).args;if(M)c.enterToTab?ne.moveTabSelected(t,M,P):ne.moveEnterSelected(t,E,!0,T,!1,P);else if(c.enterToTab)ne.moveTabSelected(t,M,P);else{var w=m.row||x.row;let l=m.column||x.column;D=ne.getVTRowIndex(w),F={row:w,rowIndex:ne.getRowIndex(w),$rowIndex:ne.getVMRowIndex(w),_rowIndex:D,column:l,columnIndex:ne.getColumnIndex(l),$columnIndex:ne.getVMColumnIndex(l),_columnIndex:ne.getVTColumnIndex(l),$table:ne};if(!f||!1!==f(F)){if(u&&D>=d.length-1)return ne.insertAt({},-1).then(({row:e})=>{ne.scrollToRow(e,l),ne.handleSelected(Object.assign(Object.assign({},t),{row:e}),P)}),void ne.dispatchEvent("enter-append-row",F,P);ne.moveEnterSelected(t,E,!1,T,!0,P),e&&e(F)}}}else if(t&&(p.isCurrent||a)&&n){s=n[h];if(s&&s.length){P.preventDefault();let e=s[0],l={$table:ne,row:e,rowIndex:ne.getRowIndex(e),$rowIndex:ne.getVMRowIndex(e)};ne.setTreeExpand(n,!0).then(()=>ne.scrollToRow(e)).then(()=>ne.triggerCurrentRowEvent(P,l))}}}else if(k&&l&&c.isArrow)U||(g.selected&&m.row&&m.column?ne.moveArrowSelected(m.args,E,y,T,S,P):((y||S)&&(p.isCurrent||a)&&ne.moveCurrentRow(y,S,P),(E||T)&&(O.isCurrent||o)&&ne.moveCurrentColumn(E,T,P)));else if(z&&l&&c.isTab)m.row||m.column?ne.moveTabSelected(m.args,M,P):(x.row||x.column)&&ne.moveTabSelected(x.args,M,P);else if(l&&c.isDel&&B&&(0,_utils.isEnableConf)(r)&&(m.row||m.column))U||(b=c.delMethod,L={row:m.row,rowIndex:$.getRowIndex(m.row),column:m.column,columnIndex:$.getColumnIndex(m.column),$table:ne,$grid:K},A&&!A(L))||(b?b(L):(0,_util.setCellValue)(m.row,m.column,null),$.updateFooter(),de("cell-delete-value",L,P));else if(C&&l&&c.isBack&&(0,_utils.isEnableConf)(r)&&(m.row||m.column))U||(w=c.backMethod,c.isDel&&(0,_utils.isEnableConf)(r)&&(m.row||m.column)&&(f={row:m.row,rowIndex:ne.getRowIndex(m.row),column:m.column,columnIndex:ne.getColumnIndex(m.column),$table:ne,$grid:K},A&&!A(f)||(w?w(f):((0,_util.setCellValue)(m.row,m.column,null),ne.handleEdit(m.args,P)),de("cell-backspace-value",f,P))));else if(C&&l&&t&&c.isBack&&(p.isCurrent||a)&&n){let l=_xeUtils.default.findTree(G.afterTreeFullData,e=>e===n,{children:h}).parent;if(l){P.preventDefault();let e={row:l,rowIndex:ne.getRowIndex(l),$rowIndex:ne.getVMRowIndex(l),$table:ne,$grid:K};ne.setTreeExpand(l,!1).then(()=>ne.scrollToRow(l)).then(()=>ne.triggerCurrentRowEvent(P,e))}}else if(l&&(0,_utils.isEnableConf)(r)&&c.isEdit&&!I&&(R||48<=_&&_<=57||65<=_&&_<=90||96<=_&&_<=111||186<=_&&_<=192||219<=_&&_<=222)){var{editMode:u,editMethod:D}=c;if(m.column&&m.row&&(0,_utils.isEnableConf)(m.column.editRender)){let e=v.beforeEditMethod||v.activeMethod;d={row:m.row,rowIndex:ne.getRowIndex(m.row),column:m.column,columnIndex:ne.getColumnIndex(m.column),$table:ne,$grid:K};e&&!e(Object.assign(Object.assign({},m.args),{$table:ne,$grid:K}))||(D?D(d):("insert"!==u&&(0,_util.setCellValue)(m.row,m.column,null),ne.handleEdit(m.args,P)))}}de("keydown",{},P)})},Ot=e=>{var{keyboardConfig:l,mouseConfig:t}=W,{editStore:r,filterStore:a}=N,o=G.isActivated,i=re.value,n=ae.value,r=r.actived;o&&!a.visible&&(r.row||r.column||l&&n.isClip&&t&&i.area&&ne.handlePasteCellAreaEvent&&ne.handlePasteCellAreaEvent(e),de("paste",{},e))},zt=e=>{var{keyboardConfig:l,mouseConfig:t}=W,{editStore:r,filterStore:a}=N,o=G.isActivated,i=re.value,n=ae.value,r=r.actived;o&&!a.visible&&(r.row||r.column||l&&n.isClip&&t&&i.area&&ne.handleCopyCellAreaEvent&&ne.handleCopyCellAreaEvent(e),de("copy",{},e))},Bt=e=>{var{keyboardConfig:l,mouseConfig:t}=W,{editStore:r,filterStore:a}=N,o=G.isActivated,i=re.value,n=ae.value,r=r.actived;o&&!a.visible&&(r.row||r.column||l&&n.isClip&&t&&i.area&&ne.handleCutCellAreaEvent&&ne.handleCutCellAreaEvent(e),de("cut",{},e))},Pt=()=>{ne.closeMenu&&ne.closeMenu();var e=Y.value;if(!e||!e.clientWidth)return(0,_vue.nextTick)();$.recalculate(!0),$.updateCellAreas()},Gt=e=>{var l=q.value;clearTimeout(G.tooltipTimeout),e?$.closeTooltip():l&&l.setActived&&l.setActived(!0)},$t=()=>{var{dragRow:e,dragCol:l}=N;(e||l)&&(Nt(),Wt(),Yt(),N.dragRow=null,N.dragCol=null,N.isDragColMove=!1,N.isDragRowMove=!1)},Wt=()=>{var e=Y.value;if(e){let l="row--drag-origin";_xeUtils.default.arrayEach(e.querySelectorAll("."+l),e=>{(e.draggable=!1,_dom.removeClass)(e,l)})}},Nt=()=>{var e=Y.value;if(e){let l="col--drag-origin";_xeUtils.default.arrayEach(e.querySelectorAll("."+l),e=>{(e.draggable=!1,_dom.removeClass)(e,l)})}},Vt=(e,t,r,l,a)=>{var o=Y.value;if(o){var{overflowX:i,scrollbarWidth:n,overflowY:d,scrollbarHeight:u}=N,s=G.prevDragToChild,c=o.getBoundingClientRect(),d=d?n:0,n=i?u:0,i=o.clientWidth,u=o.clientHeight;if(t){var g=ye.value;if(g)if(l){var v=Oe.value,h=t.getBoundingClientRect();let e=t.clientHeight;t=Math.max(1,h.y-c.y);t+e>u-n&&(e=u-t-n),g.style.display="block",g.style.left=`${v?d:0}px`,g.style.top=t+"px",g.style.height=e+"px",g.style.width=i-d+"px",g.setAttribute("drag-pos",a),g.setAttribute("drag-to-child",s?"y":"n")}else g.style.display=""}else if(r){h=Te.value;if(h)if(l){var v=He.value,t=P.value,g=t?t.clientWidth:0,t=X.value,t=t?t.clientWidth:0,f=r.getBoundingClientRect();let e=r.clientWidth;r=Math.max(0,f.y-c.y);let l=f.x-c.x;l<g&&(e-=g-l,l=g);g=i-t-(t?0:d);l+e>g&&(e=g-l),h.style.display="block",h.style.top=r+"px",h.style.left=l+"px",h.style.width=e+"px",h.style.height=s?f.height+"px":u-r-(v?0:n)+"px",h.setAttribute("drag-pos",a),h.setAttribute("drag-to-child",s?"y":"n")}else h.style.display=""}i=Ee.value;i&&(i.style.display="block",i.style.top=Math.min(o.clientHeight-o.scrollTop-i.clientHeight,e.clientY-c.y)+"px",i.style.left=Math.min(o.clientWidth-o.scrollLeft-i.clientWidth-16,e.clientX-c.x)+"px",i.setAttribute("drag-status",l?s?"sub":"normal":"disabled"))}},Yt=()=>{var e=Ee.value,l=ye.value,t=Te.value;e&&(e.style.display=""),l&&(l.style.display=""),t&&(t.style.display="")},qt=(e,r,a,o,i)=>{let n=a||r;if(n){i.cell=r;var a=N.tooltipStore,{column:r,row:d}=i,{showAll:u,contentMethod:s}=Ne.value,i=s?s(i):null,s=s&&!_xeUtils.default.eqNull(i);let l=s?i:_xeUtils.default.toString("html"===r.type?n.innerText:n.textContent).trim(),t=n.scrollWidth>n.clientWidth;l&&(u||s||t)&&(Object.assign(a,{row:d,column:r,visible:!0,currOpts:{}}),(0,_vue.nextTick)(()=>{var e=q.value;e&&e.open&&e.open(t?n:o,(0,_utils.formatText)(l))}))}return(0,_vue.nextTick)()},Xt=(e,l)=>{if(e){if(K)return K.callSlot(e,l);if(_xeUtils.default.isFunction(e))return(0,_vn.getSlotVNs)(e(l))}return[]},jt=(V={getSetupOptions(){return getConfig()},updateAfterDataIndex:ql,callSlot:Xt,getParentElem(){var e,l=Y.value;return K?(e=K.getRefMaps().refElem.value)?e.parentNode:null:l?l.parentNode:null},getParentHeight(){var l=W.height,t=Y.value;if(t){t=t.parentNode,l="100%"===l||"auto"===l?(0,_dom.getPaddingTopBottomSize)(t):0;let e=0;return t&&(e=K&&(0,_dom.hasClass)(t,"vxe-grid--table-wrapper")?K.getParentHeight():t.clientHeight),Math.floor(e-l)}return 0},getExcludeHeight(){return K?K.getExcludeHeight():0},defineField(e){let l=W.treeConfig,t=oe.value,r=ie.value,o=M.value,i=le.value,n=r.children||r.childrenField,d=(0,_util.getRowkey)(ne);return(e=_xeUtils.default.isArray(e)?e:[e]).map(a=>(G.tableFullColumn.forEach(l=>{var{field:t,editRender:r}=l;if(t&&!_xeUtils.default.has(a,t)&&!a[t]){let e=null;r&&(r=r.defaultValue,_xeUtils.default.isFunction(r)?e=r({column:l}):_xeUtils.default.isUndefined(r)||(e=r)),_xeUtils.default.set(a,t,e)}}),[o.labelField,i.checkField,i.labelField,t.labelField].forEach(e=>{e&&(0,_utils.eqEmptyValue)(_xeUtils.default.get(a,e))&&_xeUtils.default.set(a,e,null)}),l&&r.lazy&&_xeUtils.default.isUndefined(a[n])&&(a[n]=null),(0,_utils.eqEmptyValue)(_xeUtils.default.get(a,d))&&_xeUtils.default.set(a,d,(0,_util.getRowUniqueId)()),a))},handleTableData(e){var l=N.scrollYLoad;let{scrollYStore:t,fullDataRowIdData:a}=G,r=G.afterFullData;e&&((()=>{var e=W.treeConfig,l=N.isRowGroupStatus,{tableFullColumn:t,tableFullData:r,tableFullTreeData:a,tableFullGroupData:n}=G,d=qe.value,u=L.value,s=I.value,c=ie.value,g=c.children||c.childrenField,{transform:c,rowField:v,parentField:h,mapChildrenField:f}=c;let{isEvery:p,remote:m,filterMethod:x}=d,{remote:_,sortMethod:w,multiple:C,chronological:b}=u,R=[],E=[];if(m&&_)l?(E=_xeUtils.default.searchTree(n,()=>!0,{original:!0,isEvery:!0,children:s.mapChildrenField,mapChildren:s.childrenField}),R=E):e&&c?(E=_xeUtils.default.searchTree(a,()=>!0,{original:!0,isEvery:p,children:f,mapChildren:g}),R=E):(R=(e?a:r).slice(0),E=R);else{let o=[],i=[];t.forEach(e=>{var{field:l,sortable:t,order:r,filters:a}=e;if(!m&&a&&a.length){let l=[],t=[];a.forEach(e=>{e.checked&&(t.push(e),l.push(e.value))}),t.length&&o.push({column:e,valueList:l,itemList:t})}!_&&t&&r&&i.push({column:e,field:l,property:l,order:r,sortTime:e.sortTime})}),C&&b&&1<i.length&&(i=_xeUtils.default.orderBy(i,"sortTime")),!m&&o.length?(d=u=>o.every(({column:l,valueList:e,itemList:t})=>{let{filterMethod:r,filterRender:a}=l;var o=(0,_utils.isEnableConf)(a)?renderer.get(a.name):null;let i=o?o.tableFilterMethod||o.filterMethod:null,n=o?o.tableFilterDefaultMethod||o.defaultTableFilterMethod||o.defaultFilterMethod:null,d=(0,_util.getCellValue)(u,l);return r?t.some(e=>r({value:e.value,option:e,cellValue:d,row:u,column:l,$table:ne})):i?t.some(e=>i({value:e.value,option:e,cellValue:d,row:u,column:l,$table:ne})):x?x({$table:ne,options:t,values:e,cellValue:d,row:u,column:l}):n?t.some(e=>n({value:e.value,option:e,cellValue:d,row:u,column:l,$table:ne})):-1<e.indexOf(_xeUtils.default.get(u,l.field))}),l?(E=_xeUtils.default.searchTree(n,d,{original:!0,isEvery:!0,children:s.mapChildrenField,mapChildren:s.childrenField}),R=E):e&&c?(E=_xeUtils.default.searchTree(a,d,{original:!0,isEvery:p,children:f,mapChildren:g}),R=E):(R=(e?a:r).filter(d),E=R)):l?(E=_xeUtils.default.searchTree(n,()=>!0,{original:!0,isEvery:!0,children:s.mapChildrenField,mapChildren:s.childrenField}),R=E):e&&c?(E=_xeUtils.default.searchTree(a,()=>!0,{original:!0,isEvery:p,children:f,mapChildren:g}),R=E):(R=(e?a:r).slice(0),E=R),!_&&i.length&&(l?(E=w?(u=w({data:E,sortList:i,$table:ne}),_xeUtils.default.isArray(u)?u:E):(t=_xeUtils.default.toTreeArray(E,{key:s.rowField,parentKey:s.parentField,children:s.mapChildrenField}),_xeUtils.default.toArrayTree(_xeUtils.default.orderBy(t,i.map(({column:e,order:l})=>[Yl(e),l])),{key:s.rowField,parentKey:s.parentField,children:s.childrenField,mapChildren:s.mapChildrenField})),R=E):e&&c?(E=w?(d=w({data:E,sortList:i,$table:ne}),_xeUtils.default.isArray(d)?d:E):(n=_xeUtils.default.toTreeArray(E,{children:f}),_xeUtils.default.toArrayTree(_xeUtils.default.orderBy(n,i.map(({column:e,order:l})=>[Yl(e),l])),{key:v,parentKey:h,children:g,mapChildren:f})),R=E):(R=w?(a=w({data:R,sortList:i,$table:ne}),_xeUtils.default.isArray(a)?a:R):_xeUtils.default.orderBy(R,i.map(({column:e,order:l})=>[Yl(e),l])),E=R))}G.afterFullData=R,G.afterTreeFullData=E,G.afterGroupFullData=E,ql()})(),r=n());e=l?r.slice(t.startIndex,t.endIndex):r.slice(0);let o={};return e.forEach((e,l)=>{var t=(0,_util.getRowid)(ne,e),r=a[t];r&&(r.$index=l),o[t]=e}),N.tableData=e,G.visibleDataRowIdData=o,(0,_vue.nextTick)()},cacheRowMap(e){let s=W.treeConfig;var l=N.isRowGroupStatus;let{fullAllDataRowIdData:t,tableFullData:r,tableFullTreeData:a,tableFullGroupData:o,treeExpandedMaps:c}=G,u=e?{}:Object.assign({},t),g={},v=(0,_util.createHandleUpdateRowId)(ne).handleUpdateRowId,h=(e,l,t,r,a,o,i,n)=>{let d=u[o];d||(d={row:e,rowid:o,seq:n,index:-1,_index:-1,$index:-1,treeIndex:l,items:t,parent:a,level:i,height:0,resizeHeight:0,oTop:0,expandHeight:0},g[o]=d,u[o]=d),d.treeLoaded=!1,d.expandLoaded=!1,d.row=e,d.items=t,d.parent=a,d.level=i,d.index=r,d.treeIndex=l,g[o]=d,u[o]=d};if(s){var e=ie.value;let n=e.lazy,d=e.children||e.childrenField,u=e.hasChild||e.hasChildField;_xeUtils.default.eachTree(a,(e,l,t,r,a,o)=>{var i=v(e);s&&n&&(e[u]&&void 0===e[d]&&(e[d]=null),!c[i]||e[d]&&e[d].length||delete c[i]),h(e,l,t,a?-1:l,a,i,o.length-1,(0,_util.toTreePathSeq)(r))},{children:d})}else l?(e=I.value.mapChildrenField,_xeUtils.default.eachTree(o,(e,l,t,r,a,o)=>{var i=v(e);h(e,l,t,a?-1:l,a,i,o.length-1,(0,_util.toTreePathSeq)(r))},{children:e})):r.forEach((e,l,t)=>{h(e,l,t,l,null,v(e),0,l+1)});G.fullDataRowIdData=g,G.fullAllDataRowIdData=u,N.treeExpandedFlag++},cacheSourceMap(e){var l=W.treeConfig,t=ie.value,e=_xeUtils.default.clone(e,!0);let r=(0,_util.createHandleUpdateRowId)(ne).handleUpdateRowId,a={};var o=e=>{var l=r(e);a[l]=e};l?(l=t.children||t.childrenField,_xeUtils.default.eachTree(e,o,{children:t.transform?t.mapChildrenField:l})):e.forEach(o),G.sourceDataRowIdData=a,G.tableSourceData=e},analyColumnWidth(){var e=G.tableFullColumn;let{width:l,minWidth:t}=Z.value,r=[],a=[],o=[],i=[],n=[],d=[],u=[],s=[];e.forEach(e=>{l&&!e.width&&(e.width=l),t&&!e.minWidth&&(e.minWidth=t),e.visible&&(e.resizeWidth?r:"auto"===e.width?u:(0,_dom.isPx)(e.width)?a:(0,_dom.isScale)(e.width)?n:(0,_dom.isPx)(e.minWidth)?o:"auto"===e.minWidth?i:(0,_dom.isScale)(e.minWidth)?d:s).push(e)}),Object.assign(N.columnStore,{resizeList:r,pxList:a,pxMinList:o,autoMinList:i,scaleList:n,scaleMinList:d,autoList:u,remainList:s})},handleColResizeMousedownEvent(e,l,r){e.stopPropagation(),e.preventDefault();var t=r.column,{columnStore:a,overflowX:o,scrollbarHeight:i}=N;let{elemStore:n,visibleColumn:R}=G;var{leftList:a,rightList:E}=a;let y=$e.value,T=o?i:0,S=Y.value,D=P.value;o=X.value;let F=Ce.value;if(F){let p="left"===l,m="right"===l,x=F.firstElementChild,_=He.value,w=e.clientX;i=e.target;let C=t;t.children&&t.children.length&&_xeUtils.default.eachTree(t.children,e=>{C=e});l=i.parentNode,t=Object.assign(r,{cell:l});let b=0;if((0,_util.getRefElem)(n["main-body-scroll"])){let d=S.getBoundingClientRect(),u=o?o.getBoundingClientRect():null;var o=l.getBoundingClientRect(),I=i.getBoundingClientRect(),i=i.clientWidth,M=Math.floor(i/2);let s=I.x-d.x+M,c=(0,_util.getColReMinWidth)(t)-M,g=m?0:o.x-d.x+i+c,v=o.x-d.x+l.clientWidth-c,h=0,f=0;if(p||m){let l=!1;var L=p?a:E;for(let e=0;e<L.length;e++){var k=L[e];l?h+=k.renderWidth:(l=k.id===C.id)||(f+=k.renderWidth)}}I=l=>{l.stopPropagation(),l.preventDefault();var t=S.clientHeight,r=l.clientX-w;let e=s+r;p?u&&(e=Math.min(e,u.x-d.x-h-c)):m&&(D&&(e=Math.max(e,D.clientWidth+f+c)),e=Math.min(e,v)),b=Math.max(e,g);r=Math.max(1,b);if(F.style.left=r+"px",F.style.top=`${_?T:0}px`,F.style.height=`${_?t-T:t}px`,y.showDragTip&&x){x.textContent=getI18n("vxe.table.resizeColTip",[Math.floor(C.renderWidth+(m?s-b:b-s))]);var a=S.clientWidth,o=F.clientWidth,i=x.clientWidth,n=x.clientHeight;let e=-i;r<i+o?e=0:a<r&&(e+=a-r),x.style.left=e+"px",x.style.top=Math.min(t-n,Math.max(0,l.clientY-d.y-n/2))+"px"}N.isDragResize=!0};N.isDragResize=!0,(0,_dom.addClass)(S,"col-drag--resize"),F.style.display="block",document.onmousemove=I,document.onmouseup=function(e){document.onmousemove=null,document.onmouseup=null,F.style.display="none",G._lastResizeTime=Date.now(),setTimeout(()=>{N.isDragResize=!1},50);var l=C.renderWidth+(m?s-b:b-s),t=Object.assign(Object.assign({},r),{resizeWidth:l,resizeColumn:C});"fixed"===y.dragMode&&R.forEach(e=>{e.id===C.id||e.resizeWidth||(e.resizeWidth=e.renderWidth)}),ne.handleColResizeCellAreaEvent?ne.handleColResizeCellAreaEvent(e,t):(C.resizeWidth=l,St(e,t)),(0,_dom.removeClass)(S,"col-drag--resize")},I(e),ne.closeMenu&&ne.closeMenu()}}},handleColResizeDblclickEvent(t,r){var a=$e.value.isDblclickAutoWidth,o=Y.value;if(a&&o){t.stopPropagation(),t.preventDefault();var a=G.fullColumnIdData,i=r.column;let l=i;i.children&&i.children.length&&_xeUtils.default.eachTree(i.children,e=>{l=e});i=a[l.id],a=t.target.parentNode,a=Object.assign(r,{cell:a}),a=(0,_util.getColReMinWidth)(a);o.setAttribute("data-calc-col","Y");let e=Gl(l,o);o.removeAttribute("data-calc-col"),i&&(e=Math.max(e,i.width)),e=Math.max(a,e);o=Object.assign(Object.assign({},r),{resizeWidth:e,resizeColumn:l});N.isDragResize=!1,G._lastResizeTime=Date.now(),ne.handleColResizeDblclickCellAreaEvent?ne.handleColResizeDblclickCellAreaEvent(t,o):(l.resizeWidth=e,St(t,o))}},handleRowResizeMousedownEvent(r,a){r.stopPropagation(),r.preventDefault();let o=a.row;var{overflowX:i,scrollbarWidth:n,overflowY:h,scrollbarHeight:f}=N,{elemStore:p,fullAllDataRowIdData:m}=G;let x=h?n:0,_=i?f:0,w=Oe.value,C=$e.value;h=J.value,n=F.value;let b=Y.value,R=be.value;if(R){let g=r.clientY,v=R.firstElementChild;i=r.currentTarget.parentNode,f=i.parentNode;if((0,_util.getRefElem)(p["main-body-scroll"])){let t=m[(0,_util.getRowid)(ne,o)];if(t){p=D.value;let d=t.resizeHeight||n.height||h.height||t.height||p,u=b.getBoundingClientRect();m=f.getBoundingClientRect();let e=g-m.y-f.clientHeight,s=d;n=i.querySelector(".vxe-cell");let l=0,c=(n&&(h=getComputedStyle(n),l=Math.max(1,Math.ceil(_xeUtils.default.toNumber(h.paddingTop)+_xeUtils.default.toNumber(h.paddingBottom)))),m.y-u.y+l);p=t=>{t.stopPropagation(),t.preventDefault();var r=b.clientWidth-x,a=b.clientHeight-_;let o=t.clientY-u.y-e;if(o<c?o=c:s=Math.max(l,d+t.clientY-g),R.style.left=`${w?x:0}px`,R.style.top=o+"px",R.style.width=r+"px",C.showDragTip&&v){v.textContent=getI18n("vxe.table.resizeRowTip",[s]);var i=v.clientWidth,n=v.clientHeight;let e=Math.max(2,t.clientX-u.x),l=0;e+i>=r-2&&(e=r-i-2),o+n>=a&&(l=a-(o+n)),v.style.left=e+"px",v.style.top=l+"px"}N.isDragResize=!0};N.isDragResize=!0,(0,_dom.addClass)(b,"row-drag--resize"),R.style.display="block",document.onmousemove=p,document.onmouseup=function(e){var l;document.onmousemove=null,document.onmouseup=null,R.style.display="none",G._lastResizeTime=Date.now(),setTimeout(()=>{N.isDragResize=!1},50),s!==d&&(l=Object.assign(Object.assign({},a),{resizeHeight:s,resizeRow:o}),G.isResizeCellHeight=!0,ne.handleRowResizeCellAreaEvent?ne.handleRowResizeCellAreaEvent(e,l):(t.resizeHeight=s,Dt(e,l),U())),(0,_dom.removeClass)(b,"row-drag--resize")},p(r)}}}},handleRowResizeDblclickEvent(a,o){var e=$e.value.isDblclickAutoHeight;let i=Y.value;if(e&&i){a.stopPropagation(),a.preventDefault();var e=N.editStore,l=G.fullAllDataRowIdData,e=e.actived;let t=o.row,r=l[(0,_util.getRowid)(ne,t)];r&&(l=()=>{i.setAttribute("data-calc-row","Y");var e=Nl(r,i),l=(i.removeAttribute("data-calc-row"),Object.assign(Object.assign({},o),{resizeHeight:e,resizeRow:t}));N.isDragResize=!1,G._lastResizeTime=Date.now(),ne.handleRowResizeDblclickCellAreaEvent?ne.handleRowResizeDblclickCellAreaEvent(a,l):(r.resizeHeight=e,Dt(a,l))},e.row||e.column?ne.clearEdit().then(l):l())}},saveCustomStore(e){var l=W.customConfig,t=R.value,r=cl.value,{updateStore:a,storage:o,storeOptions:i}=r,n=!0===o,o=n?{}:Object.assign({},o||{},i),i=_(o.resizable,n),d=_(o.visible,n),u=_(o.fixed,n),o=_(o.sort,n);if("reset"!==e&&(N.isCustomStatus=!0),(l?(0,_utils.isEnableConf)(r):r.enabled)&&(i||d||u||o)){if(!t)return(0,_log.errLog)("vxe.error.reqProp",["id"]),(0,_vue.nextTick)();n="reset"===e?{resizableData:{},sortData:{},visibleData:{},fixedData:{}}:$.getCustomStoreData();if(a)return a({$table:ne,id:t,type:e,storeData:n});l=t,r="reset"===e?null:n,i=getConfig().version,(d=Tl())[l]=r||void 0,d._v=i,localStorage.setItem(customStorageKey,_xeUtils.default.toJSONString(d))}return(0,_vue.nextTick)()},handleCustom(){var e=W.mouseConfig;return e&&(ne.clearSelected&&ne.clearSelected(),ne.clearCellAreas)&&(ne.clearCellAreas(),ne.clearCopyCellArea()),V.analyColumnWidth(),$.refreshColumn(!0)},handleUpdateDataQueue(){N.upDataFlag++},handleRefreshColumnQueue(){N.reColumnFlag++},preventEvent(l,e,t,r,a){let o=interceptor.get(e),i=(o.length||"event.clearEdit"!==e||(o=interceptor.get("event.clearActived")).length&&(0,_log.warnLog)("vxe.error.delEvent",["event.clearActived","event.clearEdit"]),null),n=!1;for(let e=0;e<o.length;e++){var d=(0,o[e])(Object.assign({$grid:K,$table:ne,$event:l},t));if(!1===d){n=!0;break}if(d&&!1===d.status){i=d.result,n=!0;break}}return n||r&&(i=r()),a&&a(),i},updateCheckboxStatus(){var e=W.treeConfig,l=N.isRowGroupStatus;let{afterTreeFullData:t,afterGroupFullData:a,selectCheckboxMaps:d,treeIndeterminateRowMaps:u}=G;var s=I.value,c=ie.value;let g=c.children||c.childrenField,{checkField:v,checkStrictly:r,checkMethod:h}=le.value;if(!r){if(l||e){let n=(0,_util.createHandleGetRowId)(ne).handleGetRowId,o={},i=[];if(l){let r=s.mapChildrenField;r&&_xeUtils.default.eachTree(a,e=>{var l=n(e),t=e[r];t&&t.length&&!o[l]&&(o[l]=1,i.unshift([e,l,t]))},{children:r})}else if(e){let{transform:r,mapChildrenField:a}=c;_xeUtils.default.eachTree(t,e=>{var l=n(e),t=e[r?a:g];t&&t.length&&!o[l]&&(o[l]=1,i.unshift([e,l,t]))},{children:r?a:g})}i.forEach(e=>{var l=e[0],t=e[1];let r=0,a=0,o=0;e[2].forEach(h?e=>{var l=n(e),t=v?_xeUtils.default.get(e,v):d[l];h({$table:ne,row:e})?(t?r++:u[l]&&a++,o++):t?r++:u[l]&&a++}:e=>{var l=n(e);(v?_xeUtils.default.get(e,v):d[l])?r++:u[l]&&a++,o++});var e=r>=o,i=!e&&(1<=r||1<=a);v&&_xeUtils.default.set(l,v,e),e?(v||(d[t]=l),u[t]&&delete u[t]):(v||d[t]&&delete d[t],i?u[t]=l:u[t]&&delete u[t])})}N.updateCheckboxFlag++}},updateAllCheckboxStatus(){var e=W.treeConfig,l=N.isRowGroupStatus;let{afterFullData:t,afterTreeFullData:r,afterGroupFullData:a,checkboxReserveRowMap:o,selectCheckboxMaps:i,treeIndeterminateRowMaps:n}=G,{checkField:d,checkMethod:u,showReserveStatus:s}=le.value,c=(0,_util.createHandleGetRowId)(ne).handleGetRowId,g=0,v=0,h=0;e=e?r:l?a:t,e.forEach(u?e=>{var l=c(e),t=d?_xeUtils.default.get(e,d):i[l];u({$table:ne,row:e})?(t?g++:n[l]&&v++,h++):t?g++:n[l]&&v++}:e=>{var l=c(e);(d?_xeUtils.default.get(e,d):i[l])?g++:n[l]&&v++,h++}),l=0<e.length&&(0<h?g>=h:g>=e.length);let f=!l&&(1<=g||1<=v);l||f||!s||(f=!_xeUtils.default.isEmpty(o)),N.isAllSelected=l,N.isIndeterminate=f},checkSelectionStatus(){ne.updateCheckboxStatus(),ne.updateAllCheckboxStatus()},handleBatchSelectRows(e,t,r){var l=W.treeConfig,a=N.isRowGroupStatus;let o=G.selectCheckboxMaps;var i=I.value,n=ie.value,{transform:d,mapChildrenField:u}=n,n=n.children||n.childrenField,s=le.value;let{checkField:c,checkStrictly:g,checkMethod:v}=s,h=(0,_util.createHandleGetRowId)(ne).handleGetRowId,f=s.indeterminateField||s.halfField;if(c)!l&&!a||g?e.forEach(e=>{!r&&v&&!v({$table:ne,row:e})||(_xeUtils.default.set(e,c,t),Zl(e,t))}):_xeUtils.default.eachTree(e,e=>{!r&&v&&!v({$table:ne,row:e})||(_xeUtils.default.set(e,c,t),f&&_xeUtils.default.set(e,f,!1),Zl(e,t))},{children:d?u:n}),N.updateCheckboxFlag++;else{if(!g){if(a)return _xeUtils.default.eachTree(e,e=>{var l=h(e);!r&&v&&!v({$table:ne,row:e})||(t?o[l]=e:o[l]&&delete o[l],Zl(e,t))},{children:i.mapChildrenField}),void N.updateCheckboxFlag++;if(l)return _xeUtils.default.eachTree(e,e=>{var l=h(e);!r&&v&&!v({$table:ne,row:e})||(t?o[l]=e:o[l]&&delete o[l],Zl(e,t))},{children:d?u:n}),void N.updateCheckboxFlag++}e.forEach(e=>{var l=h(e);!r&&v&&!v({$table:ne,row:e})||(t?o[l]||(o[l]=e):o[l]&&delete o[l],Zl(e,t),N.updateCheckboxFlag++)})}},handleSelectRow({row:e},l,t){ne.handleBatchSelectRows([e],l,t)},handleUpdateBodyMerge(){var e=G.mergeBodyList;G.mergeBodyCellMaps=Ul(e),N.mergeBodyFlag++},handleUpdateFooterMerge(){var e=G.mergeFooterList;G.mergeFooterCellMaps=Ul(e),N.mergeFootFlag++},handleAggregateSummaryData(){return(()=>{var{aggregateConfig:e,rowGroupConfig:l}=W,t=N.isRowGroupStatus,r=G.tableFullGroupData,a=I.value.mapChildrenField;if((e||l)&&t){let l=[];_xeUtils.default.eachTree(r,e=>{e.isAggregate&&l.push(e)},{children:a}),ut(l)}})()},triggerHeaderTitleEvent(t,e,r){var a=e.content||e.message;if(a){var o=N.tooltipStore,r=r.column;let l=(0,_utils.getFuncText)(a);Gt(!0),o.row=null,o.column=r,o.visible=!0,o.currOpts=e,(0,_vue.nextTick)(()=>{var e=q.value;e&&e.open&&e.open(t.currentTarget,l)})}},triggerHeaderTooltipEvent(e,l){var t,r,a=N.tooltipStore,o=l.column,e=(Gt(!0),e.currentTarget);(e=e&&e.parentElement)&&(t=e.parentElement)&&(r=t.parentElement)&&(a.column!==o||!a.visible)&&(o=r.querySelector(".vxe-cell--title"),qt(0,r,((0,_dom.hasClass)(r,"col--ellipsis")?o:e)||e,o||t,l))},triggerBodyTooltipEvent(t,r){var a=W.editConfig,e=N.editStore,l=N.tooltipStore,o=te.value,e=e.actived,{row:i,column:n}=r,t=t.currentTarget;if(Gt(l.column!==n||l.row!==i),n.editRender&&(0,_utils.isEnableConf)(a)){if("row"===o.mode&&e.row===i)return;if(e.row===i&&e.column===n)return}if(l.column!==n||l.row!==i||!l.visible){a=t.querySelector(".vxe-cell--wrapper");let e=null,l=t.querySelector("html"===n.type?".vxe-cell--html":".vxe-cell--label");n.treeNode&&(e=t.querySelector(".vxe-tree-cell")),l=l||a,qt(0,t,e||a,l,r)}},triggerFooterTooltipEvent(t,r){var a=r.column,o=N.tooltipStore,t=t.currentTarget;if(Gt(o.column!==a||!!o.row),o.column!==a||!o.visible){o=t.querySelector(".vxe-cell--wrapper");let e=null,l=t.querySelector("html"===a.type?".vxe-cell--html":".vxe-cell--label");"html"===a.type&&(e=t.querySelector(".vxe-cell--html")),l=l||o,qt(0,t,e||o,l,r)}},handleTargetLeaveEvent(){var e=Ne.value;let l=q.value;l&&l.setActived&&l.setActived(!1),e.enterable?G.tooltipTimeout=setTimeout(()=>{(l=q.value)&&l.isActived&&!l.isActived()&&ne.closeTooltip()},e.leaveDelay):ne.closeTooltip()},triggerHeaderCellClickEvent(e,l){var t=G._lastResizeTime,r=L.value,a=Z.value,o=B.value,i=l.column,n=e.currentTarget,t=t&&t>Date.now()-300,d=(0,_dom.getEventTargetNode)(e,n,"vxe-cell--sort").flag,u=(0,_dom.getEventTargetNode)(e,n,"vxe-cell--filter").flag;"cell"!==r.trigger||t||d||u||ne.triggerSortEvent(e,i,yl(i)),de("header-cell-click",Object.assign({triggerResizable:t,triggerSort:d,triggerFilter:u,cell:n},l),e),!a.isCurrent&&!W.highlightCurrentColumn||o.trigger&&!["header","default"].includes(o.trigger)||ne.triggerCurrentColumnEvent(e,l)},triggerHeaderCellDblclickEvent(e,l){de("header-cell-dblclick",Object.assign({cell:e.currentTarget},l),e)},triggerCellClickEvent(e,l){var t,r,a,o,i,n,d,u,s,c,g,v,h,f,p,m,x,_,w,C,b,R,E,{highlightCurrentRow:y,highlightCurrentColumn:T,editConfig:S}=W,{editStore:D,isDragResize:F}=N;F||(F=oe.value,t=te.value,r=ie.value,a=M.value,o=le.value,i=ae.value,n=I.value,d=J.value,u=Z.value,s=B.value,{actived:D,focused:c}=D,{row:g,column:v}=l,{type:p,treeNode:h,rowGroupNode:f}=v,m="checkbox"===p,x="expand"===p,_=e.currentTarget,w=(p="radio"===p)&&(0,_dom.getEventTargetNode)(e,_,"vxe-cell--radio").flag,C=m&&(0,_dom.getEventTargetNode)(e,_,"vxe-cell--checkbox").flag,b=h&&(0,_dom.getEventTargetNode)(e,_,"vxe-cell--tree-btn").flag,R=x&&(0,_dom.getEventTargetNode)(e,_,"vxe-table--expanded").flag,E=x&&(0,_dom.getEventTargetNode)(e,_,"vxe-row-group--node-btn").flag,l=Object.assign({cell:_,triggerRadio:w,triggerCheckbox:C,triggerTreeNode:b,triggerExpandNode:R},l),!C&&!w&&(!R&&("row"===F.trigger||x&&"cell"===F.trigger)&&ne.triggerRowExpandEvent(e,l),("row"===r.trigger||h&&"cell"===r.trigger)&&ne.triggerTreeExpandEvent(e,l),"row"===n.trigger||f&&"cell"===n.trigger)&&ne.triggerRowGroupExpandEvent(e,l),b||(R||E||(!d.isCurrent&&!y||C||w||ne.triggerCurrentRowEvent(e,l),!u.isCurrent&&!T||s.trigger&&!["cell","default"].includes(s.trigger)||C||w||ne.triggerCurrentColumnEvent(e,l),!w&&("row"===a.trigger||p&&"cell"===a.trigger)&&ne.triggerRadioRowEvent(e,l),!C&&("row"===o.trigger||m&&"cell"===o.trigger)&&ne.handleToggleCheckRowEvent(e,l)),(0,_utils.isEnableConf)(S)&&(i.arrowCursorLock&&e&&"cell"===t.mode&&e.target&&/^input|textarea$/i.test(e.target.tagName)&&(c.column=v,c.row=g),"manual"===t.trigger?D.args&&D.row===g&&v!==D.column&&jl(e,l):D.args&&g===D.row&&v===D.column||("click"===t.trigger||"dblclick"===t.trigger&&"row"===t.mode&&D.row===g)&&jl(e,l))),(0,_utils.isEnableConf)(S)&&"dblclick"===t.trigger&&D.row&&D.column&&("row"===t.mode?ne.eqRow(D.row,g)||ne.handleClearEdit(e):"cell"!==t.mode||ne.eqRow(D.row,g)&&D.column.id===v.id||ne.handleClearEdit(e)),de("cell-click",l,e))},triggerCellDblclickEvent(e,l){var t,r=W.editConfig,{editStore:a,isDragResize:o}=N;o||(o=te.value,a=a.actived,t=e.currentTarget,l=Object.assign({cell:t},l),!(0,_utils.isEnableConf)(r)||"dblclick"!==o.trigger||a.args&&e.currentTarget===a.args.cell||("row"===o.mode?Xl("blur").catch(e=>e).then(()=>{ne.handleEdit(l,e).then(()=>Xl("change")).catch(e=>e)}):"cell"===o.mode&&ne.handleEdit(l,e).then(()=>Xl("change")).catch(e=>e)),de("cell-dblclick",l,e))},handleToggleCheckRowEvent(l,t){var r=G.selectCheckboxMaps,{checkField:a,trigger:e}=le.value,o=t.row;if("manual"!==e){let e=!1;e=a?!_xeUtils.default.get(o,a):!r[(0,_util.getRowid)(ne,o)],l?ne.triggerCheckRowEvent(l,t,e):(ne.handleBatchSelectRows([o],e),ne.checkSelectionStatus())}},triggerCheckRowEvent(l,t,e){var r=W.treeConfig,a=t.row,o=N.isRowGroupStatus,i=G.afterFullData,n=le.value,{checkMethod:d,trigger:u}=n;if("manual"!==u){if(l.stopPropagation(),n.isShiftKey&&l.shiftKey&&!r&&!o){u=ne.getCheckboxRecords();if(u.length){n=u[0],r=ne.getVTRowIndex(a),o=ne.getVTRowIndex(n);if(r!==o){ne.setAllCheckboxRow(!1);let e=r<o?i.slice(r,o+1):i.slice(o,r+1);return(0,_vue.nextTick)(()=>{Ql(e,!0,!1)}),void de("checkbox-range-select",Object.assign({rangeRecords:e},t),l)}}}d&&!d({$table:ne,row:a})||(ne.handleBatchSelectRows([a],e),ne.checkSelectionStatus(),de("checkbox-change",Object.assign({records:()=>ne.getCheckboxRecords(),reserves:()=>ne.getCheckboxReserveRecords(),indeterminates:()=>ne.getCheckboxIndeterminateRecords(),checked:e},t),l))}},triggerCheckAllEvent(e,l){var t=le.value.trigger;"manual"!==t&&(e&&e.stopPropagation(),Et(e,l))},triggerRadioRowEvent(t,r){var a=N.selectRadioRow,o=r.row,i=M.value,{trigger:e,checkMethod:l}=i;if("manual"!==e&&(t.stopPropagation(),!l||l({$table:ne,row:o}))){let e=o,l=a!==e;l?Jl(e):i.strict||(l=a===e)&&(e=null,ne.clearRadioRow()),l&&de("radio-change",Object.assign({oldValue:a,newValue:e},r),t)}},triggerCurrentColumnEvent(e,l){var t=N.currentColumn,r=Z.value,a=B.value,r=a.beforeSelectMethod||r.currentMethod,o=l.column,a=a.trigger;"manual"!==a&&(a=t!==o,!r||r({column:o,$table:ne})?(ne.setCurrentColumn(o),a&&de("current-column-change",Object.assign({oldValue:t,newValue:o},l),e)):de("current-column-disabled",l,e))},triggerCurrentRowEvent(e,l){var t=N.currentRow,r=J.value,a=Pe.value,r=a.beforeSelectMethod||r.currentMethod,o=l.row,a=a.trigger;"manual"!==a&&(a=t!==o,!r||r({row:o,$table:ne})?(ne.setCurrentRow(o),a&&(de("current-row-change",Object.assign({oldValue:t,newValue:o},l),e),de("current-change",Object.assign({oldValue:t,newValue:o},l),e))):de("current-row-disabled",l,e))},triggerRowExpandEvent(e,l){var t=N.expandColumn,r=G.rowExpandLazyLoadedMaps,l=l.row,{lazy:a,trigger:o}=oe.value;"manual"===o||(e.stopPropagation(),o=(0,_util.getRowid)(ne,l),a&&r[o])||(a=!ne.isRowExpandByRow(l),r=t?ne.getColumnIndex(t):-1,o=t?ne.getVMColumnIndex(t):-1,ne.setRowExpand(l,a),de("toggle-row-expand",{expanded:a,column:t,columnIndex:r,$columnIndex:o,row:l,rowIndex:ne.getRowIndex(l),$rowIndex:ne.getVMRowIndex(l)},e))},triggerRowGroupExpandEvent(e,l){var t,r=G.rowGroupExpandedMaps,{row:l,column:a}=l,o=I.value.trigger;"manual"!==o&&(e.stopPropagation(),o=!r[(0,_util.getRowid)(ne,l)],r=ne.getColumnIndex(a),t=ne.getVMColumnIndex(a),ne.setRowGroupExpand(l,o),de("toggle-row-group-expand",{expanded:o,column:a,columnIndex:r,$columnIndex:t,row:l},e))},triggerTreeExpandEvent(e,l){var{treeExpandLazyLoadedMaps:t,treeEATime:r}=G,a=ie.value;let{row:o,column:i}=l,{lazy:n,trigger:d,accordion:u}=a;"manual"===d||(e.stopPropagation(),l=(0,_util.getRowid)(ne,o),n&&t[l])||(a=!ne.isTreeExpandByRow(o),t=ne.getColumnIndex(i),l=ne.getVMColumnIndex(i),r&&clearTimeout(r),ne.setTreeExpand(o,a).then(()=>{u&&(G.treeEATime=setTimeout(()=>{G.treeEATime=void 0,ne.scrollToRow(o)},30))}),de("toggle-tree-expand",{expanded:a,column:i,columnIndex:t,$columnIndex:l,row:o},e))},handleColumnSortEvent(e,l){var t=W.mouseConfig,r=re.value,{field:a,sortable:o,order:i}=l;o&&(o={$table:ne,$event:e,column:l,field:a,property:a,order:i,sortList:$.getSortColumns(),sortTime:l.sortTime},t&&r.area&&ne.handleSortEvent&&ne.handleSortEvent(e,o),i||de("clear-sort",o,e),de("sort-change",o,e))},triggerSortEvent(e,l,t){var{multiple:r,allowClear:a}=L.value,{field:o,sortable:i}=l;i&&(t&&l.order!==t?ne.sort({field:o,order:t}):a&&ne.clearSort(r?l:null),ne.handleColumnSortEvent(e,l))},handleCellRuleUpdateStatus(e,l,r){let a=N.validStore,{row:o,column:i}=l;if(ne.hasCellRules&&ne.hasCellRules(e,o,i)){let t=ne.getCellElement(o,i);if(t){let l=!_xeUtils.default.isUndefined(r);return ne.validCellRules(e,o,i,r).then(()=>{l&&a.visible&&(0,_util.setCellValue)(o,i,r),ne.clearValidate(o,i)}).catch(({rule:e})=>{l&&(0,_util.setCellValue)(o,i,r),ne.showValidTooltip({rule:e,row:o,column:i,cell:t})})}}return(0,_vue.nextTick)()},triggerHeaderCellMousedownEvent(e,l){var t=W.mouseConfig,r=re.value,a=Z.value,{trigger:o,isCrossDrag:i,isPeerDrag:n,disabledMethod:d}=ee.value,u=e.currentTarget,s=u&&u.tagName&&"input"===u.tagName.toLowerCase(),c=(0,_dom.getEventTargetNode)(e,u,"vxe-cell--checkbox").flag,g=(0,_dom.getEventTargetNode)(e,u,"vxe-cell--sort").flag,v=(0,_dom.getEventTargetNode)(e,u,"vxe-cell--filter").flag;let h=!1;a=a.drag&&"cell"===o;s||c||g||v||(o=l.column,!a)||o.fixed||!i&&!n&&o.parentId||d&&d(l)||(h=!0,ne.handleHeaderCellDragMousedownEvent(e,l)),!h&&t&&r.area&&ne.handleHeaderCellAreaEvent&&ne.handleHeaderCellAreaEvent(e,Object.assign({cell:u,triggerSort:g,triggerFilter:v},l)),ne.focus(),ne.closeMenu&&ne.closeMenu()},triggerCellMousedownEvent(e,l){var t=l.column,{type:r,treeNode:a}=t,o="radio"===r,i="checkbox"===r,r="expand"===r,n=J.value,{trigger:d,isCrossDrag:u,isPeerDrag:s,disabledMethod:c}=Q.value,g=e.currentTarget,v=(l.cell=g)&&g.tagName&&"input"===g.tagName.toLowerCase(),o=o&&(0,_dom.getEventTargetNode)(e,g,"vxe-cell--radio").flag,i=i&&(0,_dom.getEventTargetNode)(e,g,"vxe-cell--checkbox").flag,a=a&&(0,_dom.getEventTargetNode)(e,g,"vxe-cell--tree-btn").flag,r=r&&(0,_dom.getEventTargetNode)(e,g,"vxe-table--expanded").flag;let h=!1,f=(n.drag&&(h="row"===d||t.dragSort&&"cell"===d),!1);v||o||i||a||r||!h||!u&&!s&&l.level||c&&c(l)||(f=!0,ne.handleCellDragMousedownEvent(e,l)),!f&&ne.handleCellMousedownEvent&&ne.handleCellMousedownEvent(e,l),ne.focus(),ne.closeFilter(),ne.closeMenu&&ne.closeMenu()},triggerCellMouseupEvent(){$t()},handleRowDragDragstartEvent(e){e.dataTransfer&&e.dataTransfer.setDragImage((0,_dom.getTpImg)(),0,0)},handleRowDragSwapEvent(d,e,u,s,c,l){let{treeConfig:g,dragConfig:t}=W;var r=Q.value;let{afterFullData:v,tableFullData:h,fullAllDataRowIdData:f}=G,{isPeerDrag:p,isCrossDrag:m,isSelfToChildDrag:x,dragEndMethod:a,dragToChildMethod:o}=r;r=ie.value;let{transform:_,rowField:w,mapChildrenField:C,parentField:b}=r,R=r.children||r.childrenField;r=a||(t?t.dragEndMethod:null);let E="bottom"===c?1:0,y={status:!1};if(s&&u&&s!==u){var i={oldRow:u,newRow:s,dragRow:u,dragPos:c,dragToChild:!!l,offsetIndex:E};let n=x&&o?o(i):l;return Promise.resolve(!r||r(i)).then(t=>{if(!t)return y;let e=-1,l=-1;if(g){if(_){var t=(0,_util.getRowid)(ne,u),t=f[t],r=(0,_util.getRowid)(ne,s),a=f[r];if(t&&a){var o=t.level,i=a.level;let l={},e=(_xeUtils.default.eachTree([u],e=>{l[(0,_util.getRowid)(ne,e)]=e},{children:C}),!1);if(o&&i)if(p&&!m){if(t.row[b]!==a.row[b])return y}else{if(!m)return y;if(l[r]&&(e=!0,!m||!x))return _ui.VxeUI.modal&&_ui.VxeUI.modal.message({status:"error",content:getI18n("vxe.error.treeDragChild")}),y}else if(o){if(!m)return y}else if(i){if(!m)return y;if(l[r]&&(e=!0,!m||!x))return _ui.VxeUI.modal&&_ui.VxeUI.modal.message({status:"error",content:getI18n("vxe.error.treeDragChild")}),y}t=_xeUtils.default.toTreeArray(G.afterTreeFullData,{key:w,parentKey:b,children:C}),a=ne.findRowIndexOf(t,u),o=(t.splice(a,1),ne.findRowIndexOf(t,s)),i=o+E;t.splice(i,0,u),e&&m&&x&&_xeUtils.default.each(u[R],e=>{e[b]=u[b]}),u[b]=n?s[w]:s[b],G.tableFullTreeData=_xeUtils.default.toArrayTree(t,{key:w,parentKey:b,children:R,mapChildren:C})}}}else{e=ne.findRowIndexOf(v,u);r=ne.findRowIndexOf(h,u),a=(v.splice(e,1),h.splice(r,1),ne.findRowIndexOf(v,s)),o=ne.findRowIndexOf(h,s),i=(l=a+E,o+E);v.splice(l,0,u),h.splice(i,0,u)}return N.isDragRowMove=!0,ne.handleTableData(g&&_),ne.cacheRowMap(!1),Ct(),g&&_||ne.updateAfterDataIndex(),ne.checkSelectionStatus(),N.scrollYLoad&&ne.updateScrollYSpace(),d&&de("row-dragend",{oldRow:u,newRow:s,dragRow:u,dragPos:c,dragToChild:n,offsetIndex:E,_index:{newIndex:l,oldIndex:e}},d),(0,_vue.nextTick)().then(()=>{ne.updateCellAreas(),ne.recalculate()}).then(()=>({status:!0}))}).catch(()=>y)}return Promise.resolve(y)},handleRowDragDragendEvent(e){var l=W.treeConfig,{fullAllDataRowIdData:t,prevDragToChild:r}=G,a=N.dragRow,o=ie.value,i=o.lazy,o=o.hasChild||o.hasChildField,{prevDragRow:n,prevDragPos:d}=G;l&&i&&r&&(l=t[(0,_util.getRowid)(ne,n)],n[o])&&(!l||!l.treeLoaded)||ne.handleRowDragSwapEvent(e,!0,a,n,d,r),Yt(),Wt(),G.prevDragToChild=!1,N.dragRow=null,N.dragCol=null,setTimeout(()=>{N.isDragRowMove=!1},500)},handleRowDragDragoverEvent(t){var r=W.treeConfig,a=G.fullAllDataRowIdData;let e=N.dragRow;var o=ie.value,{lazy:i,transform:n,parentField:d}=o,o=o.hasChild||o.hasChildField,{isPeerDrag:u,isCrossDrag:s,isToChildDrag:c}=Q.value;if(e){var g=(0,_dom.hasControlKey)(t),v=t.currentTarget;let e=v.getAttribute("rowid")||"";var h=a[e];if(h){var f=h.row;let e=(0,_util.getRowid)(ne,f);a=a[e];t.preventDefault();let l=N.dragRow;var p=t.clientY-v.getBoundingClientRect().y<v.clientHeight/2?"top":"bottom";G.prevDragToChild=!!(r&&n&&s&&c&&g),G.prevDragRow=f,G.prevDragPos=p,ne.eqRow(l,f)||g&&r&&i&&f[o]&&a&&!a.treeLoaded||!s&&r&&n&&(u?l[d]!==f[d]:h.level)?Vt(t,v,null,!1,p):(Vt(t,v,null,!0,p),de("row-dragover",{oldRow:l,targetRow:f,dragPos:p},t))}}else t.preventDefault()},handleCellDragMousedownEvent(e,l){var t=(e.stopPropagation(),W).dragConfig,{trigger:r,dragStartMethod:a}=Q.value,o=l.row,i=e.currentTarget,i="cell"===r||"row"===r?i:null==(r=i.parentElement)?void 0:r.parentElement,r=i.parentElement,a=a||(t?t.dragStartMethod:null);Wt(),a&&!a(l)?(r.draggable=!1,N.dragRow=null,N.dragCol=null,Yt()):(N.dragRow=o,N.dragCol=null,r.draggable=!0,(e=>{var l=Y.value;if(l){e=(0,_util.getRowid)(ne,e);_xeUtils.default.arrayEach(l.querySelectorAll(`[rowid="${e}"]`),e=>{(0,_dom.addClass)(e,"row--drag-origin")})}})(o),(e=>{var l=W.dragConfig,t=N.dragRow,r=Q.value.tooltipMethod,r=r||(l?l.rowTooltipMethod:null);let a="";a=r?""+(r({$table:ne,row:t})||""):getI18n("vxe.table.dragTip",[e.textContent||""]),N.dragTipText=a})(i),de("row-dragstart",l,e))},handleCellDragMouseupEvent(){$t()},handleHeaderCellDragDragstartEvent(e){e.dataTransfer&&e.dataTransfer.setDragImage((0,_dom.getTpImg)(),0,0)},handleColDragSwapColumn(){ft(),xt(!1).then(()=>{ne.updateCellAreas(),ne.saveCustomStore("update:sort")})},handleColDragSwapEvent(c,g,e,l,v,t){let h=W.mouseConfig;let{isPeerDrag:f,isCrossDrag:p,isSelfToChildDrag:m,isToChildDrag:x,dragEndMethod:r,dragToChildMethod:a}=ee.value,_=G.collectColumn,w="right"===v?1:0,C={status:!1};if(l&&e&&l!==e){let d=e,u=l;e={oldColumn:d,newColumn:u,dragColumn:d,dragPos:v,dragToChild:!!t,offsetIndex:w};let s=m&&a?a(e):t;return Promise.resolve(!r||r(e)).then(e=>{if(!e)return C;let l=-1,t=-1,r={},a=(_xeUtils.default.eachTree([d],e=>{r[e.id]=e}),!1);if(d.parentId&&u.parentId)if(f&&!p){if(d.parentId!==u.parentId)return C}else{if(!p)return C;if(r[u.id]&&(a=!0,!p||!m))return _ui.VxeUI.modal&&_ui.VxeUI.modal.message({status:"error",content:getI18n("vxe.error.treeDragChild")}),C}else if(d.parentId){if(!p)return C}else if(u.parentId){if(!p)return C;if(r[u.id]&&(a=!0,!p||!m))return _ui.VxeUI.modal&&_ui.VxeUI.modal.message({status:"error",content:getI18n("vxe.error.treeDragChild")}),C}var o,i,n,e=_xeUtils.default.findTree(_,e=>e.id===d.id),e=(a&&p&&m?e&&({items:o,index:n}=e,(i=d.children||[]).forEach(e=>{e.parentId=d.parentId}),o.splice(n,1,...i),d.children=[]):e&&({items:o,index:n,parent:i}=e,o.splice(n,1),i||(l=n)),_xeUtils.default.findTree(_,e=>e.id===u.id));return e&&({items:o,index:i,parent:n}=e,p&&x&&s?(d.parentId=u.id,u.children=(u.children||[]).concat([d])):(d.parentId=u.parentId,o.splice(i+w,0,d)),n||(t=i)),_xeUtils.default.eachTree(_,(e,l,t,r,a)=>{a||(e.renderSortNumber=l+1)}),N.isDragColMove=!0,h&&(ne.clearSelected&&ne.clearSelected(),ne.clearCellAreas)&&(ne.clearCellAreas(),ne.clearCopyCellArea()),c&&de("column-dragend",{oldColumn:d,newColumn:u,dragColumn:d,dragPos:v,dragToChild:s,offsetIndex:w,_index:{newIndex:t,oldIndex:l}},c),g&&ne.handleColDragSwapColumn(),{status:!0}}).catch(()=>C)}return Promise.resolve(C)},handleHeaderCellDragDragendEvent(e){var l=N.dragCol,{prevDragCol:t,prevDragPos:r,prevDragToChild:a}=G;ne.handleColDragSwapEvent(e,!0,l,t,r,a),Yt(),Nt(),G.prevDragToChild=!1,N.dragRow=null,N.dragCol=null,setTimeout(()=>{N.isDragColMove=!1,ne.recalculate().then(()=>{pt()})},500)},handleHeaderCellDragDragoverEvent(e){var l,t,r,a,o,i=N.dragCol,{isToChildDrag:n,isPeerDrag:d,isCrossDrag:u}=ee.value;i?(o=(0,_dom.hasControlKey)(e),r=(t=e.currentTarget).getAttribute("colid"),(r=ne.getColumnById(r))&&(e.preventDefault(),l=e.clientX,a=l-t.getBoundingClientRect().x<t.clientWidth/2?"left":"right",G.prevDragToChild=!!(u&&n&&o),G.prevDragCol=r,G.prevDragPos=a,r.fixed||i&&i.id===r.id||!u&&(d?i.parentId!==r.parentId:r.parentId)?Vt(e,null,t,!1,a):(Vt(e,null,t,!0,a),de("column-dragover",{oldColumn:i,targetColumn:r,dragPos:a},e),(n=Y.value)&&(o=T.value,d=(u=m.value)?u.$el:null,t=o||d)&&(i=n.getBoundingClientRect(),r=n.clientWidth,u=(a=P.value)?a.clientWidth:0,d=(o=X.value)?o.clientWidth:0,n=l-(i.x+u),a=i.x+r-d-l,0<n&&n<=28?(o=Math.floor(r/(14<n?240:120)),t.scrollLeft-=o*(28-n)):0<a&&a<=28&&(u=Math.floor(r/(14<a?240:120)),t.scrollLeft+=u*(28-a)))))):e.preventDefault()},handleHeaderCellDragMousedownEvent(e,l){e.stopPropagation();var t=ee.value,{trigger:t,dragStartMethod:r}=t,a=l.column,o=e.currentTarget,o="cell"===t?o:null==(t=o.parentElement)?void 0:t.parentElement;N.isDragColMove=!1,Nt(),r&&!r(l)?(o.draggable=!1,N.dragRow=null,N.dragCol=null,Yt()):(N.dragCol=a,N.dragRow=null,o.draggable=!0,(e=>{var t=Y.value;if(t){let l=[];_xeUtils.default.eachTree([e],e=>{l.push(`[colid="${e.id}"]`)});_xeUtils.default.arrayEach(t.querySelectorAll(l.join(",")),e=>{(0,_dom.addClass)(e,"col--drag-origin")})}})(a),(e=>{var l=N.dragCol,t=ee.value.tooltipMethod;let r="";r=t?""+(t({$table:ne,column:l})||""):getI18n("vxe.table.dragTip",[e.textContent||""]),N.dragTipText=r})(o),de("column-dragstart",l,e))},handleHeaderCellDragMouseupEvent(){Nt(),Yt(),N.dragRow=null,N.dragCol=null,N.isDragColMove=!1},handleScrollEvent(u,s,c,g,v,h){var f=W.highlightHoverRow,{lastScrollLeft:p,lastScrollTop:m}=G,x=T.value,_=S.value;if(x&&_){var w=J.value,C=me.value,b=q.value,R=_.clientHeight,E=x.clientWidth,_=_.scrollHeight,x=x.scrollWidth;let e=!1,l=!1,t=!1,r=!1,a="",o=!1,i=!1,n=!1,d=!1;c&&(y=O.value,(t=v<=0)||(r=x<=v+E),p<v?(a="right",x-y<=v+E&&(d=!0)):(a="left",v<=y&&(n=!0)),ne.checkScrolling(),G.lastScrollLeft=v),s&&(p=z.value,(e=g<=0)||(l=_<=g+R),m<g?(a="bottom",_-p<=g+R&&(i=!0)):(a="top",g<=p&&(o=!0)),G.lastScrollTop=g),N.isDragColMove=!1,N.isDragRowMove=!1,N.lastScrollTime=Date.now();var y=Object.assign({scrollTop:g,scrollLeft:v,bodyHeight:R,bodyWidth:E,scrollHeight:_,scrollWidth:x,isX:c,isY:s,isTop:e,isBottom:l,isLeft:t,isRight:r,direction:a},h);It(),((e,l)=>{let{scrollXLoad:t,scrollYLoad:r,isAllOverflow:a}=N;var o=G.lcsTimeout;o&&clearTimeout(o),G.lcsTimeout=setTimeout(()=>{G.lcsRunTime=Date.now(),G.lcsTimeout=void 0,G.intoRunScroll=!1,G.inVirtualScroll=!1,G.inWheelScroll=!1,G.inHeaderScroll=!1,G.inBodyScroll=!1,G.inFooterScroll=!1,G.scrollRenderType="",a||(Vl(),U()),e&&t&&ne.updateScrollXData(),l&&r&&ne.updateScrollYData().then(()=>{a||(Vl(),U()),ne.updateScrollYSpace()}),It(),ne.updateCellAreas()},200)})(c,s),c&&ne.closeFilter(),(w.isHover||f)&&ne.clearHoverRow(),C&&C.reactData.visible&&C.close(),b&&b.reactData.visible&&b.close(),(i||o||d||n)&&de("scroll-boundary",y,u),de("scroll",y,u)}},triggerScrollXEvent(){(C.value.immediate?pt:()=>{var{lxTimeout:e,lxRunTime:l,scrollXStore:t}=G,t=t.visibleSize,t=26<t?26:16<t?14:6;e&&clearTimeout(e),(!l||l+t<Date.now())&&(G.lxRunTime=Date.now(),pt()),G.lxTimeout=setTimeout(()=>{G.lxTimeout=void 0,G.lxRunTime=void 0,pt()},t)})()},triggerScrollYEvent(){(y.value.immediate?yt:()=>{var{lyTimeout:e,lyRunTime:l,scrollYStore:t}=G,t=t.visibleSize,t=30<t?32:20<t?18:8;e&&clearTimeout(e),(!l||l+t<Date.now())&&(G.lyRunTime=Date.now(),yt()),G.lyTimeout=setTimeout(()=>{G.lyTimeout=void 0,G.lyRunTime=void 0,yt()},t)})()},triggerBodyScrollEvent(t,r){var{scrollYLoad:a,scrollXLoad:o}=N,{elemStore:i,intoRunScroll:n,lastScrollTop:d,lastScrollLeft:u,inWheelScroll:s,inVirtualScroll:c,inHeaderScroll:g,inBodyScroll:v,scrollRenderType:e,inFooterScroll:h}=G;if(!(s||c||g||h)){var s=T.value,c=S.value,g=(0,_util.getRefElem)(i["left-body-scroll"]),h=(0,_util.getRefElem)(i["main-body-scroll"]),f=(0,_util.getRefElem)(i["right-body-scroll"]),p=(0,_util.getRefElem)(i["main-header-scroll"]),i=(0,_util.getRefElem)(i["main-footer-scroll"]),m=j.value;if(!n&&h&&s&&c&&(!v||e===r)){let e=c.scrollTop,l=s.scrollLeft;g&&"left"===r?e=g.scrollTop:f&&"right"===r?e=f.scrollTop:(e=h.scrollTop,l=h.scrollLeft);n=l!==u,v=e!==d;G.inBodyScroll=!0,G.scrollRenderType=r,v&&("left"===r?((0,_dom.setScrollTop)(h,e),(0,_dom.setScrollTop)(f,e)):"right"===r?((0,_dom.setScrollTop)(h,e),(0,_dom.setScrollTop)(g,e)):((0,_dom.setScrollTop)(g,e),(0,_dom.setScrollTop)(f,e)),(0,_dom.setScrollTop)(c,e),(0,_dom.setScrollTop)(m,e),a)&&ne.triggerScrollYEvent(t),n&&((0,_dom.setScrollLeft)(s,l),(0,_dom.setScrollLeft)(p,l),(0,_dom.setScrollLeft)(i,l),o)&&ne.triggerScrollXEvent(t),ne.handleScrollEvent(t,v,n,e,l,{type:"body",fixed:r})}}},triggerHeaderScrollEvent(e,l){var t=N.scrollXLoad,{elemStore:r,intoRunScroll:a,inWheelScroll:o,inVirtualScroll:i,inBodyScroll:n,inFooterScroll:d}=G;o||i||n||d||(o=S.value,i=T.value,n=(0,_util.getRefElem)(r["main-body-scroll"]),d=(0,_util.getRefElem)(r["main-header-scroll"]),r=(0,_util.getRefElem)(r["main-footer-scroll"]),a)||d&&i&&o&&(a=o.scrollTop,o=d.scrollLeft,G.inHeaderScroll=!0,(0,_dom.setScrollLeft)(i,o),(0,_dom.setScrollLeft)(r,o),(0,_dom.setScrollLeft)(n,o),t&&ne.triggerScrollXEvent(e),ne.handleScrollEvent(e,!1,!0,a,o,{type:"header",fixed:l}))},triggerFooterScrollEvent(e,l){var t=N.scrollXLoad,{elemStore:r,intoRunScroll:a,inWheelScroll:o,inVirtualScroll:i,inHeaderScroll:n,inBodyScroll:d}=G;o||i||n||d||(o=S.value,i=T.value,n=(0,_util.getRefElem)(r["main-body-scroll"]),d=(0,_util.getRefElem)(r["main-header-scroll"]),r=(0,_util.getRefElem)(r["main-footer-scroll"]),a)||r&&i&&o&&(a=o.scrollTop,o=r.scrollLeft,G.inFooterScroll=!0,(0,_dom.setScrollLeft)(i,o),(0,_dom.setScrollLeft)(d,o),(0,_dom.setScrollLeft)(n,o),t&&ne.triggerScrollXEvent(e),ne.handleScrollEvent(e,!1,!0,a,o,{type:"footer",fixed:l}))},triggerBodyWheelEvent(v){var{target:h,deltaY:f,deltaX:p,shiftKey:m}=v;if(!h||!/^textarea$/i.test(h.tagName)){h=_props.default.highlightHoverRow;let{scrollXLoad:c,scrollYLoad:g,expandColumn:e}=N;var x=Je.value,_=Qe.value;if(x||_||e){var{elemStore:x,lastScrollTop:_,lastScrollLeft:w}=G,C=J.value;let r=T.value,a=S.value,o=(0,_util.getRefElem)(x["left-body-scroll"]),i=(0,_util.getRefElem)(x["main-header-scroll"]),n=(0,_util.getRefElem)(x["main-body-scroll"]),d=(0,_util.getRefElem)(x["main-footer-scroll"]),u=(0,_util.getRefElem)(x["right-body-scroll"]),s=j.value;if(r&&a&&n){var x=(e=>{let l=1;var t=Date.now();return t<e+25?l=1.18:t<e+30?l=1.15:t<e+40?l=1.12:t<e+55?l=1.09:t<e+75?l=1.06:t<e+100&&(l=1.03),l})(N.lastScrollTime),b=m?0:Math.ceil(f*x),m=m?Math.ceil((m&&f||p)*x):0,f=b<0,p=n.scrollTop;if(!(f?p<=0:p>=n.scrollHeight-n.clientHeight)){var R,E,x=p+b,f=n.scrollLeft+m;let l=f!==w,t=x!==_;(C.isHover||h)&&ne.clearHoverRow(),l&&(v.preventDefault(),G.inWheelScroll=!0,ce.firefox||ce.safari?((0,_dom.setScrollLeft)(r,b=f),(0,_dom.setScrollLeft)(n,b),(0,_dom.setScrollLeft)(i,b),(0,_dom.setScrollLeft)(d,b),c&&ne.triggerScrollXEvent(v),ne.handleScrollEvent(v,t,l,n.scrollTop,b,{type:"table",fixed:""})):(R=f,E=e=>{G.inWheelScroll=!0;(0,_dom.setScrollLeft)(r,e),(0,_dom.setScrollLeft)(n,e),(0,_dom.setScrollLeft)(i,e),(0,_dom.setScrollLeft)(d,e),c&&ne.triggerScrollXEvent(v),ne.handleScrollEvent(v,t,l,n.scrollTop,e,{type:"table",fixed:""})},requestAnimationFrame(()=>{E(R)}))),t&&(v.preventDefault(),G.inWheelScroll=!0,ce.firefox||ce.safari?((0,_dom.setScrollTop)(a,m=x),(0,_dom.setScrollTop)(n,m),(0,_dom.setScrollTop)(o,m),(0,_dom.setScrollTop)(u,m),(0,_dom.setScrollTop)(s,m),g&&ne.triggerScrollYEvent(v),ne.handleScrollEvent(v,t,l,m,n.scrollLeft,{type:"table",fixed:""})):((t,r)=>{let a=Math.abs(t),o=performance.now(),i=0,n=e=>{let l=(e-o)/a;1<l&&(l=1);e=Math.pow(l,2),e=Math.floor(t*e)-i;i+=e,r(e),l<1&&requestAnimationFrame(n)};requestAnimationFrame(n)})(x-p,e=>{G.inWheelScroll=!0;e=n.scrollTop+e;(0,_dom.setScrollTop)(a,e),(0,_dom.setScrollTop)(n,e),(0,_dom.setScrollTop)(o,e),(0,_dom.setScrollTop)(u,e),(0,_dom.setScrollTop)(s,e),g&&ne.triggerScrollYEvent(v),ne.handleScrollEvent(v,t,l,e,n.scrollLeft,{type:"table",fixed:""})}))}}}}},triggerVirtualScrollXEvent(l){var t=N.scrollXLoad,{elemStore:r,inWheelScroll:a,lastScrollTop:o,inHeaderScroll:i,inBodyScroll:n,inFooterScroll:d}=G;if(!(i||n||d||a)){i=(0,_util.getRefElem)(r["main-header-scroll"]),n=(0,_util.getRefElem)(r["main-body-scroll"]),d=(0,_util.getRefElem)(r["main-footer-scroll"]),a=S.value,r=l.currentTarget.scrollLeft,a=a||n;let e=0;a=(e=a?a.scrollTop:e)!==o;G.inVirtualScroll=!0,(0,_dom.setScrollLeft)(n,r),(0,_dom.setScrollLeft)(i,r),(0,_dom.setScrollLeft)(d,r),t&&ne.triggerScrollXEvent(l),ne.handleScrollEvent(l,a,!0,e,r,{type:"table",fixed:""})}},triggerVirtualScrollYEvent(l){var t=N.scrollYLoad,{elemStore:r,inWheelScroll:a,lastScrollLeft:o,inHeaderScroll:i,inBodyScroll:n,inFooterScroll:d}=G;if(!(i||n||d||a)){var i=(0,_util.getRefElem)(r["left-body-scroll"]),n=(0,_util.getRefElem)(r["main-body-scroll"]),d=(0,_util.getRefElem)(r["right-body-scroll"]),a=j.value,r=T.value,u=l.currentTarget.scrollTop,r=r||n;let e=0;r=(e=r?r.scrollLeft:e)!==o;G.inVirtualScroll=!0,(0,_dom.setScrollTop)(n,u),(0,_dom.setScrollTop)(i,u),(0,_dom.setScrollTop)(d,u),(0,_dom.setScrollTop)(a,u),t&&ne.triggerScrollYEvent(l),ne.handleScrollEvent(l,!0,r,u,e,{type:"table",fixed:""})}},scrollToTreeRow(l){var e=W.treeConfig,t=N.isRowGroupStatus,r=G.tableFullData;let a=[];if(e||t){var e=I.value,o=ie.value,o=o.children||o.childrenField,r=_xeUtils.default.findTree(r,e=>ne.eqRow(e,l),{children:t?e.mapChildrenField:o});if(r){let t=r.nodes;t.forEach((e,l)=>{l<t.length-1&&!ne.isTreeExpandByRow(e)&&a.push(ne.setTreeExpand(e,!0))})}}return Promise.all(a).then(()=>(0,_util.rowToVisible)(ne,l))},updateScrollYStatus:Ct,updateScrollXSpace(){let{scrollXLoad:a,overflowX:o,scrollXWidth:i}=N,{visibleColumn:n,scrollXStore:d,elemStore:u,fullColumnIdData:s}=G;var c=re.value,g=m.value;if(g?g.$el:null){var g=(0,_util.getRefElem)(u["main-body-scroll"]),v=(0,_util.getRefElem)(u["main-body-table"]),h=(0,_util.getRefElem)(u["main-header-table"]),f=(0,_util.getRefElem)(u["main-footer-table"]);let e=0;var p=n[d.startIndex];p&&(p=s[p.id]||{},e=p.oLeft);let l=0,t=(g&&(l=g.clientWidth),!1),r=i;i>maxXWidth&&(e=g&&v&&g.scrollLeft+l>=maxXWidth?maxXWidth-v.clientWidth:(maxXWidth-l)*(e/(i-l)),r=maxXWidth,t=!0),a&&o||(e=0),h&&(h.style.transform=h.getAttribute("xvm")?`translate(${e}px, 0px)`:""),v&&(v.style.transform=`translate(${e}px, ${N.scrollYTop||0}px)`),f&&(f.style.transform=f.getAttribute("xvm")?`translate(${e}px, 0px)`:"");["main"].forEach(l=>{["header","body","footer"].forEach(e=>{e=(0,_util.getRefElem)(u[l+`-${e}-xSpace`]);e&&(e.style.width=a?r+"px":"")})}),N.scrollXLeft=e,N.scrollXWidth=r,N.isScrollXBig=t;p=w.value;return p&&(p.style.width=r+"px"),t&&c.area&&(0,_log.errLog)("vxe.error.notProp",["mouse-config.area"]),it(),(0,_vue.nextTick)().then(()=>{k()})}},updateScrollYSpace(){var{isAllOverflow:e,overflowY:l,scrollYLoad:t,expandColumn:r}=N;let{scrollYStore:a,elemStore:o,isResizeCellHeight:i,afterFullData:n,fullAllDataRowIdData:d,rowExpandedMaps:u}=G;var s,c=a.startIndex,g=re.value,v=oe.value,h=J.value,f=F.value,p=D.value,m=(0,_util.getRefElem)(o["main-body-scroll"]),x=(0,_util.getRefElem)(o["main-body-table"]),_=(0,_util.getRefElem)(o["left-body-table"]),w=(0,_util.getRefElem)(o["right-body-table"]);let C=0,b=0,R=!1,E=(t?i||f.height||h.height||r||!e?(e=n[c],e=d[(0,_util.getRowid)(ne,e)]||{},C=e.oTop||0,s=n[n.length-1],e=d[s=(0,_util.getRowid)(ne,s)]||{},b=(e.oTop||0)+(e.resizeHeight||f.height||h.height||e.height||p),r&&u[s]&&(b+=e.expandHeight||v.height||0),b>maxYHeight&&(R=!0)):((b=n.length*p)>maxYHeight&&(R=!0),C=Math.max(0,c*p)):x&&(b=x.clientHeight),0),y=(m&&(E=m.clientHeight),b),T=C;R&&(T=m&&x&&m.scrollTop+E>=maxYHeight?maxYHeight-x.clientHeight:(maxYHeight-E)*(C/(b-E)),y=maxYHeight),t&&l||(T=0),_&&(_.style.transform=`translate(0px, ${T}px)`),x&&(x.style.transform=`translate(${N.scrollXLeft||0}px, ${T}px)`),w&&(w.style.transform=`translate(0px, ${T}px)`),["main","left","right"].forEach(l=>{["header","body","footer"].forEach(e=>{e=(0,_util.getRefElem)(o[l+`-${e}-ySpace`]);e&&(e.style.height=y?y+"px":"")})});f=H.value,f&&(f.style.height=y?y+"px":""),h=A.value;return h&&(h.style.height=y?y+"px":""),N.scrollYTop=T,N.scrollYHeight=b,(N.isScrollYBig=R)&&g.area&&(0,_log.errLog)("vxe.error.notProp",["mouse-config.area"]),it(),(0,_vue.nextTick)().then(()=>{k()})},updateScrollXData(){let e=N.isAllOverflow;return ht(),ne.updateScrollXSpace(),(0,_vue.nextTick)().then(()=>{ht(),ne.updateScrollXSpace(),e||ne.updateScrollYSpace()})},updateScrollYData(){return ne.handleTableData(),ne.updateScrollYSpace(),(0,_vue.nextTick)().then(()=>{ne.handleTableData(),ne.updateScrollYSpace()})},checkScrolling(){var e=G.elemStore,e=(0,_util.getRefElem)(e["main-body-scroll"]),l=P.value,t=X.value,e=T.value||e;e&&(l&&(0<e.scrollLeft?(0,_dom.addClass):(0,_dom.removeClass))(l,"scrolling--middle"),t)&&(e.clientWidth<e.scrollWidth-Math.ceil(e.scrollLeft)?(0,_dom.addClass):(0,_dom.removeClass))(t,"scrolling--middle")},updateZindex(){W.zIndex?G.tZindex=W.zIndex:G.tZindex<(0,_utils.getLastZIndex)()&&(G.tZindex=(0,_utils.nextZIndex)())},handleCheckedCheckboxRow:Ql,triggerHoverEvent(e,{row:l}){V.setHoverRow(l)},setHoverRow(e){var l=(0,_util.getRowid)(ne,e),t=Y.value;V.clearHoverRow(),t&&_xeUtils.default.arrayEach(t.querySelectorAll(`.vxe-body--row[rowid="${l}"]`),e=>(0,_dom.addClass)(e,"row--hover")),G.hoverRow=e},clearHoverRow(){var e=Y.value;e&&_xeUtils.default.arrayEach(e.querySelectorAll(".vxe-body--row.row--hover"),e=>(0,_dom.removeClass)(e,"row--hover")),G.hoverRow=null},getCell(e,l){return $.getCellElement(e,l)},findRowIndexOf(e,l){return l?_xeUtils.default.findIndexOf(e,e=>ne.eqRow(e,l)):-1},eqRow(e,l){return!(!e||!l||e!==l&&(0,_util.getRowid)(ne,e)!==(0,_util.getRowid)(ne,l))}},"openExport,openPrint,exportData,openImport,importData,saveFile,readFile,importByFile,print".split(",").forEach(e=>{ne[e]=function(){(0,_log.errLog)("vxe.error.reqModule",["Export"])}}),"clearValidate,fullValidate,validate".split(",").forEach(e=>{ne[e]=function(){(0,_log.errLog)("vxe.error.reqModule",["Validator"])}}),Object.assign(ne,$,V),e=>{var{showHeader:l,showFooter:t}=W,{tableData:r,tableColumn:a,tableGroupColumn:o,columnStore:i,footerTableData:n}=N,d="left"===e,i=d?i.leftList:i.rightList;return(0,_vue.h)("div",{ref:d?P:X,class:`vxe-table--fixed-${e}-wrapper`},[l?(0,_vue.h)(_header.default,{ref:d?g:f,fixedType:e,tableData:r,tableColumn:a,tableGroupColumn:o,fixedColumn:i}):renderEmptyElement(ne),(0,_vue.h)(_body.default,{ref:d?v:p,fixedType:e,tableData:r,tableColumn:a,fixedColumn:i}),t?(0,_vue.h)(_footer.default,{ref:d?h:x,footerTableData:n,tableColumn:a,fixedColumn:i,fixedType:e}):renderEmptyElement(ne)])}),Kt=()=>{var e=W.dragConfig,{dragRow:l,dragCol:t,dragTipText:r}=N,a=ee.value,e=(Q.value.slots||{}).tip||(e&&e.slots?e.slots.rowTip:null),a=(a.slots||{}).tip;return l&&e?Xt(e,{row:l}):t&&a?Xt(a,{column:t}):[(0,_vue.h)("span",r)]},Zt=()=>{let w=W.treeConfig,{expandColumn:C,isRowGroupStatus:b}=N;var e=gl.value,l=oe.value.mode;if("fixed"!==l)return renderEmptyElement(ne);let R=[(0,_vue.h)("div",{key:"repY",ref:A})];if(C){let _=(0,_util.createHandleGetRowId)(ne).handleGetRowId;e.forEach(e=>{var{height:l,padding:t,indent:r}=oe.value,{fullAllDataRowIdData:a,fullColumnIdData:o}=G,i=ie.value,{transform:n,seqMode:d}=i,u={},s=_(e),a=a[s],o=o[C.id]||{};let c=0,g=-1,v=-1,h=-1,f=-1,p=(a&&(c=a.level,g=b||w&&n&&"increasing"===d?a._index+1:a.seq,h=a.index,f=a.$index,v=a._index),l&&(u.height=l+"px"),(b||w)&&(u.paddingLeft=c*(_xeUtils.default.isNumber(r)?r:i.indent)+30+"px"),-1),m=-1,x=-1;o&&(p=o.index,m=o.$index,x=o._index);n={$grid:K,$table:ne,seq:g,column:C,columnIndex:p,$columnIndex:m,_columnIndex:x,fixed:"",type:"body",level:c,rowid:s,row:e,rowIndex:h,$rowIndex:f,_rowIndex:v,isHidden:!1,isEdit:!1,visibleData:[],data:[],items:[]};R.push((0,_vue.h)("div",{key:s,class:["vxe-body--row-expanded-cell",{"is--padding":t,"is--ellipsis":l}],rowid:s,style:u},C.renderData(n)))})}return(0,_vue.h)("div",{ref:j,class:"vxe-table--row-expanded-wrapper"},R)},Jt=()=>(0,_vue.h)("div",{key:"vsx",ref:Se,class:"vxe-table--scroll-x-virtual"},[(0,_vue.h)("div",{ref:Fe,class:"vxe-table--scroll-x-left-corner"}),(0,_vue.h)("div",{ref:Le,class:"vxe-table--scroll-x-wrapper"},[(0,_vue.h)("div",{ref:T,class:"vxe-table--scroll-x-handle",onScroll:ne.triggerVirtualScrollXEvent},[(0,_vue.h)("div",{ref:w,class:"vxe-table--scroll-x-space"})])]),(0,_vue.h)("div",{ref:Ie,class:"vxe-table--scroll-x-right-corner"})]),Qt=()=>(0,_vue.h)("div",{ref:De,class:"vxe-table--scroll-y-virtual"},[(0,_vue.h)("div",{ref:Me,class:"vxe-table--scroll-y-top-corner"}),(0,_vue.h)("div",{ref:ke,class:"vxe-table--scroll-y-wrapper"},[(0,_vue.h)("div",{ref:S,class:"vxe-table--scroll-y-handle",onScroll:ne.triggerVirtualScrollYEvent},[(0,_vue.h)("div",{ref:H,class:"vxe-table--scroll-y-space"})])]),(0,_vue.h)("div",{ref:Ue,class:"vxe-table--scroll-y-bottom-corner"})]),er=()=>{var{showHeader:e,showFooter:l}=W,{overflowX:t,tableData:r,tableColumn:a,tableGroupColumn:o,footerTableData:i,columnStore:n}=N,{leftList:n,rightList:d}=n;return(0,_vue.h)("div",{ref:u,class:"vxe-table--viewport-wrapper"},[(0,_vue.h)("div",{class:"vxe-table--main-wrapper"},[e?(0,_vue.h)(_header.default,{ref:s,tableData:r,tableColumn:a,tableGroupColumn:o}):renderEmptyElement(ne),(0,_vue.h)(_body.default,{ref:m,tableData:r,tableColumn:a}),l?(0,_vue.h)(_footer.default,{ref:c,footerTableData:i,tableColumn:a}):renderEmptyElement(ne)]),(0,_vue.h)("div",{class:"vxe-table--fixed-wrapper"},[n&&n.length&&t?jt("left"):renderEmptyElement(ne),d&&d.length&&t?jt("right"):renderEmptyElement(ne)]),Zt()])},lr=()=>{var e=Oe.value;return(0,_vue.h)("div",{class:"vxe-table--layout-wrapper"},e?[Qt(),er()]:[er(),Qt()])};let tr=(0,_vue.ref)(0),rr=((0,_vue.watch)(()=>W.data?W.data.length:-1,()=>{tr.value++}),(0,_vue.watch)(()=>W.data,()=>{tr.value++}),(0,_vue.watch)(tr,()=>{let a=G.initStatus;var e=W.data||[];e&&5e4<=e.length&&(0,_log.warnLog)("vxe.error.errLargeData",["loadData(data), reloadData(data)"]),ct(e,!1).then(()=>{var{scrollXLoad:e,scrollYLoad:l,expandColumn:t}=N,r=oe.value;return G.inited=!0,G.initStatus=!0,a||gt(),(e||l)&&t&&"fixed"!==r.mode&&(0,_log.warnLog)("vxe.error.scrollErrProp",["column.type=expand"]),$.recalculate()})}),(0,_vue.ref)(0)),ar=((0,_vue.watch)(()=>N.staticColumns.length,()=>{rr.value++}),(0,_vue.watch)(()=>N.staticColumns,()=>{rr.value++}),(0,_vue.watch)(rr,()=>{_t(_xeUtils.default.clone(N.staticColumns))}),(0,_vue.ref)(0)),or=((0,_vue.watch)(()=>N.tableColumn.length,()=>{ar.value++}),(0,_vue.watch)(()=>N.tableColumn,()=>{ar.value++}),(0,_vue.watch)(ar,()=>{V.analyColumnWidth()}),(0,_vue.watch)(()=>N.upDataFlag,()=>{(0,_vue.nextTick)(()=>{$.updateData()})}),(0,_vue.watch)(()=>N.reColumnFlag,()=>{(0,_vue.nextTick)(()=>{$.refreshColumn()})}),(0,_vue.ref)(0)),ir=((0,_vue.watch)(he,()=>{or.value++}),(0,_vue.watch)(()=>W.showHeader,()=>{or.value++}),(0,_vue.watch)(()=>W.showFooter,()=>{or.value++}),(0,_vue.watch)(()=>N.overflowX,()=>{or.value++}),(0,_vue.watch)(()=>N.overflowY,()=>{or.value++}),(0,_vue.watch)(or,()=>{(0,_vue.nextTick)(()=>{$.recalculate(!0).then(()=>$.refreshScroll())})}),(0,_vue.ref)(0)),nr=((0,_vue.watch)(()=>W.height,()=>{ir.value++}),(0,_vue.watch)(()=>W.maxHeight,()=>{ir.value++}),(0,_vue.watch)(He,()=>{ir.value++}),(0,_vue.watch)(Oe,()=>{ir.value++}),(0,_vue.watch)(()=>_ui.VxeUI.getLanguage(),()=>{ir.value++}),(0,_vue.watch)(ir,()=>{(0,_vue.nextTick)(()=>$.recalculate(!0))}),(0,_vue.ref)(0)),dr=((0,_vue.watch)(()=>W.footerData?W.footerData.length:-1,()=>{nr.value++}),(0,_vue.watch)(()=>W.footerData,()=>{nr.value++}),(0,_vue.watch)(nr,()=>{$.updateFooter()}),(0,_vue.watch)(()=>W.syncResize,e=>{e&&(Tt(),(0,_vue.nextTick)(()=>{Tt(),setTimeout(()=>Tt())}))}),(0,_vue.ref)(0)),ur=((0,_vue.watch)(()=>W.mergeCells?W.mergeCells.length:-1,()=>{dr.value++}),(0,_vue.watch)(()=>W.mergeCells,()=>{dr.value++}),(0,_vue.watch)(dr,()=>{$.clearMergeCells(),(0,_vue.nextTick)(()=>{W.mergeCells&&$.setMergeCells(W.mergeCells)})}),(0,_vue.ref)(0));(0,_vue.watch)(()=>W.mergeFooterItems?W.mergeFooterItems.length:-1,()=>{ur.value++}),(0,_vue.watch)(()=>W.mergeFooterItems,()=>{ur.value++}),(0,_vue.watch)(ur,()=>{$.clearMergeFooterItems(),(0,_vue.nextTick)(()=>{W.mergeFooterItems&&$.setMergeFooterItems(W.mergeFooterItems)})}),(0,_vue.watch)(_l,e=>{dt(e)}),(0,_vue.watch)(t,()=>{let{inited:e,tableFullData:l}=G;e&&(El(),N.tableData=[],(0,_vue.nextTick)(()=>{ne.reloadData(l)}))}),l&&(0,_vue.watch)(()=>l?l.reactData.resizeFlag:null,()=>{Pt()}),El(),hooks.forEach(e=>{var e=e.setupTable;e&&(e=e(ne))&&_xeUtils.default.isObject(e)&&Object.assign(ne,e)}),V.preventEvent(null,"created",{$table:ne});let sr;return(0,_vue.onActivated)(()=>{$.recalculate().then(()=>$.refreshScroll()),V.preventEvent(null,"activated",{$table:ne})}),(0,_vue.onDeactivated)(()=>{G.isActivated=!1,V.preventEvent(null,"deactivated",{$table:ne})}),(0,_vue.onMounted)(()=>{let R=Z.value;var e=J.value,l=cl.value,t=I.value,r=y.value,t=t.groupFields;(R.drag||e.drag||l.allowSort)&&(0,_dom.initTpImg)(),dt(t),(0,_vue.nextTick)(()=>{let{data:e,exportConfig:l,importConfig:t,treeConfig:r,showOverflow:a,highlightCurrentRow:o,highlightCurrentColumn:i}=W;var{scrollXStore:n,scrollYStore:d}=G,u=te.value,s=ie.value,c=M.value,g=le.value,v=oe.value,h=J.value,f=cl.value,p=re.value;let m=ol.value,x=il.value;var _=Pe.value,w=B.value,C=ae.value,b=I.value;W.rowId&&(0,_log.warnLog)("vxe.error.delProp",["row-id","row-config.keyField"]),W.rowKey&&(0,_log.warnLog)("vxe.error.delProp",["row-key","row-config.useKey"]),W.columnKey&&(0,_log.warnLog)("vxe.error.delProp",["column-id","column-config.useKey"]),W.rowId||h.keyField||!(g.reserve||g.checkRowKeys||c.reserve||c.checkRowKey||v.expandRowKeys||s.expandRowKeys)||(0,_log.warnLog)("vxe.error.reqProp",["row-config.keyField"]),W.editConfig&&(u.showStatus||u.showUpdateStatus||u.showInsertStatus)&&!W.keepSource&&(0,_log.warnLog)("vxe.error.reqProp",["keep-source"]),r&&(s.showLine||s.line)&&!a&&(0,_log.warnLog)("vxe.error.reqProp",["show-overflow"]),r&&!s.transform&&W.stripe&&(0,_log.warnLog)("vxe.error.noTree",["stripe"]),!W.showFooter||W.footerMethod||W.footerData||(0,_log.warnLog)("vxe.error.reqProp",["footer-data | footer-method"]),h.height&&(0,_log.warnLog)("vxe.error.delProp",["row-config.height","cell-config.height"]),W.highlightCurrentRow&&(0,_log.warnLog)("vxe.error.delProp",["highlight-current-row","row-config.isCurrent"]),W.highlightHoverRow&&(0,_log.warnLog)("vxe.error.delProp",["highlight-hover-row","row-config.isHover"]),W.highlightCurrentColumn&&(0,_log.warnLog)("vxe.error.delProp",["highlight-current-column","column-config.isCurrent"]),W.highlightHoverColumn&&(0,_log.warnLog)("vxe.error.delProp",["highlight-hover-column","column-config.isHover"]),W.resizable&&(0,_log.warnLog)("vxe.error.delProp",["resizable","column-config.resizable"]),t&&x.types&&!x.importMethod&&!_xeUtils.default.includeArrays(_xeUtils.default.keys(x._typeMaps),x.types)&&(0,_log.warnLog)("vxe.error.errProp",["export-config.types="+x.types.join(","),x.types.filter(e=>_xeUtils.default.includes(_xeUtils.default.keys(x._typeMaps),e)).join(",")||_xeUtils.default.keys(x._typeMaps).join(",")]),l&&m.types&&!m.exportMethod&&!_xeUtils.default.includeArrays(_xeUtils.default.keys(m._typeMaps),m.types)&&(0,_log.warnLog)("vxe.error.errProp",["export-config.types="+m.types.join(","),m.types.filter(e=>_xeUtils.default.includes(_xeUtils.default.keys(m._typeMaps),e)).join(",")||_xeUtils.default.keys(m._typeMaps).join(",")]),W.id||(W.customConfig?(0,_utils.isEnableConf)(f):f.enabled)&&f.storage&&(0,_log.errLog)("vxe.error.reqProp",["id"]),W.treeConfig&&g.range&&(0,_log.errLog)("vxe.error.noTree",["checkbox-config.range"]),h.height&&!W.showOverflow&&(0,_log.warnLog)("vxe.error.notProp",["table.show-overflow"]),!ne.triggerCellAreaMousedownEvent&&(W.areaConfig&&(0,_log.warnLog)("vxe.error.notProp",["area-config"]),W.clipConfig&&(0,_log.warnLog)("vxe.error.notProp",["clip-config"]),W.fnrConfig&&(0,_log.warnLog)("vxe.error.notProp",["fnr-config"]),p.area)?(0,_log.errLog)("vxe.error.notProp",["mouse-config.area"]):(r&&h.drag&&!s.transform&&(0,_log.errLog)("vxe.error.notSupportProp",["column-config.drag","tree-config.transform=false","tree-config.transform=true"]),W.dragConfig&&(0,_log.warnLog)("vxe.error.delProp",["drag-config","row-drag-config"]),W.rowGroupConfig&&(0,_log.warnLog)("vxe.error.delProp",["row-group-config","aggregate-config"]),b.countFields&&(0,_log.warnLog)("vxe.error.delProp",["row-group-config.countFields","column.agg-func"]),b.countMethod&&(0,_log.warnLog)("vxe.error.delProp",["row-group-config.countMethod","aggregate-config.aggregateMethod"]),W.treeConfig&&s.children&&(0,_log.warnLog)("vxe.error.delProp",["tree-config.children","tree-config.childrenField"]),W.treeConfig&&s.line&&(0,_log.warnLog)("vxe.error.delProp",["tree-config.line","tree-config.showLine"]),p.area&&p.selected&&(0,_log.warnLog)("vxe.error.errConflicts",["mouse-config.area","mouse-config.selected"]),p.area&&W.treeConfig&&!s.transform&&(0,_log.errLog)("vxe.error.noTree",["mouse-config.area"]),W.editConfig&&u.activeMethod&&(0,_log.warnLog)("vxe.error.delProp",["edit-config.activeMethod","edit-config.beforeEditMethod"]),W.treeConfig&&g.isShiftKey&&(0,_log.errLog)("vxe.error.errConflicts",["tree-config","checkbox-config.isShiftKey"]),g.halfField&&(0,_log.warnLog)("vxe.error.delProp",["checkbox-config.halfField","checkbox-config.indeterminateField"]),h.currentMethod&&(0,_log.warnLog)("vxe.error.delProp",["row-config.currentMethod","current-row-config.beforeSelectMethod"]),R.currentMethod&&(0,_log.warnLog)("vxe.error.delProp",["row-config.currentMethod","current-column-config.beforeSelectMethod"]),(h.isCurrent||o)&&W.keyboardConfig&&C.isArrow&&!_xeUtils.default.isBoolean(_.isFollowSelected)&&(0,_log.warnLog)("vxe.error.notConflictProp",["row-config.isCurrent","current-row-config.isFollowSelected"]),(R.isCurrent||i)&&W.keyboardConfig&&C.isArrow&&!_xeUtils.default.isBoolean(w.isFollowSelected)&&(0,_log.warnLog)("vxe.error.notConflictProp",["column-config.isCurrent","current-column-config.isFollowSelected"]),W.editConfig&&!ne.insert&&(0,_log.errLog)("vxe.error.reqModule",["Edit"]),W.editRules&&!ne.validate&&(0,_log.errLog)("vxe.error.reqModule",["Validator"]),(g.range||W.keyboardConfig||W.mouseConfig)&&!ne.handleCellMousedownEvent&&(0,_log.errLog)("vxe.error.reqModule",["Keyboard"]),(W.printConfig||W.importConfig||W.exportConfig)&&!ne.exportData&&(0,_log.errLog)("vxe.error.reqModule",["Export"]),Object.assign(d,{startIndex:0,endIndex:0,visibleSize:0}),Object.assign(n,{startIndex:0,endIndex:0,visibleSize:0}),ct(e||[],!0).then(()=>{e&&e.length&&(G.inited=!0,G.initStatus=!0,gt()),vt(),k()}),W.autoResize&&(c=Y.value,v=V.getParentElem(),sr=globalResize.create(()=>{W.autoResize&&$.recalculate(!0)}),c&&sr.observe(c),v)&&sr.observe(v))}),"scroll"!==r.mode&&(e=u.value)&&e.addEventListener("wheel",ne.triggerBodyWheelEvent,{passive:!1}),globalEvents.on(ne,"paste",Ot),globalEvents.on(ne,"copy",zt),globalEvents.on(ne,"cut",Bt),globalEvents.on(ne,"mousedown",Lt),globalEvents.on(ne,"blur",kt),globalEvents.on(ne,"mousewheel",Ut),globalEvents.on(ne,"keydown",Ht),globalEvents.on(ne,"resize",Pt),globalEvents.on(ne,"contextmenu",ne.handleGlobalContextmenuEvent),V.preventEvent(null,"mounted",{$table:ne})}),(0,_vue.onBeforeUnmount)(()=>{var e=u.value;e&&e.removeEventListener("wheel",ne.triggerBodyWheelEvent),G.cvCacheMaps={},G.prevDragRow=null,G.prevDragCol=null,sr&&sr.disconnect(),$.closeFilter(),ne.closeMenu&&ne.closeMenu(),V.preventEvent(null,"beforeUnmount",{$table:ne})}),(0,_vue.onUnmounted)(()=>{globalEvents.off(ne,"paste"),globalEvents.off(ne,"copy"),globalEvents.off(ne,"cut"),globalEvents.off(ne,"mousedown"),globalEvents.off(ne,"blur"),globalEvents.off(ne,"mousewheel"),globalEvents.off(ne,"keydown"),globalEvents.off(ne,"resize"),globalEvents.off(ne,"contextmenu"),V.preventEvent(null,"unmounted",{$table:ne})}),(0,_vue.nextTick)(()=>{!W.loading||ge||ue.loading||(0,_log.errLog)("vxe.error.reqComp",["vxe-loading"]),!0!==W.showOverflow&&"tooltip"!==W.showOverflow&&!0!==W.showHeaderOverflow&&"tooltip"!==W.showHeaderOverflow&&!0!==W.showFooterOverflow&&"tooltip"!==W.showFooterOverflow&&!W.tooltipConfig&&!W.editRules||ve||(0,_log.errLog)("vxe.error.reqComp",["vxe-tooltip"])}),(0,_vue.provide)("$xeColgroup",null),(0,_vue.provide)("$xeTable",ne),ne.renderVN=()=>{var{loading:e,stripe:l,showHeader:t,height:r,treeConfig:a,mouseConfig:o,showFooter:i,highlightCell:n,highlightHoverRow:d,highlightHoverColumn:u,editConfig:s,editRules:c}=W,{isGroup:g,overflowX:v,overflowY:h,scrollXLoad:f,scrollYLoad:p,tableData:m,initStore:x,isRowGroupStatus:_,columnStore:w,filterStore:H,customStore:O}=N,{leftList:w,rightList:z}=w;let C=ue.loading;var b=Ve.value,R=Ye.value,E=Ae.value,B=le.value,y=ie.value,P=J.value,T=Z.value,S=he.value,D=pl.value,F=re.value,I=Xe.value,M=ul.value,G=rl.value;let L=N.isColLoading||N.isRowLoading||e;var e=$e.value,k=o&&F.area,$=ee.value,U=He.value,A=Oe.value;return(0,_vue.h)("div",{ref:Y,class:["vxe-table","vxe-table--render-default","tid_"+se,"border--"+D,"sx-pos--"+(U?"top":"bottom"),"sy-pos--"+(A?"left":"right"),{["size--"+S]:S,["valid-msg--"+E.msgMode]:!!c,"vxe-editable":!!s,"old-cell-valid":c&&"obsolete"===getConfig().cellVaildMode,"cell--highlight":n,"cell--selected":o&&F.selected,"cell--area":k,"header-cell--area":k&&I.selectCellByHeader,"body-cell--area":k&&I.selectCellByBody,"row--highlight":P.isHover||d,"column--highlight":T.isHover||u,"checkbox--range":B.range,"col--drag-cell":T.drag&&"cell"===$.trigger,"is--header":t,"is--footer":i,"is--group":g,"is-row-group":_,"is--tree-line":a&&(y.showLine||y.line),"is--fixed-left":w.length,"is--fixed-right":z.length,"is--animat":!!W.animat,"is--round":W.round,"is--stripe":!a&&l,"is--loading":L,"is--empty":!L&&!m.length,"is--scroll-y":h,"is--scroll-x":v,"is--virtual-x":f,"is--virtual-y":p}],spellcheck:!1,onKeydown:At},[(0,_vue.h)("div",{class:"vxe-table-slots"},ue.default?ue.default({}):[]),(0,_vue.h)("div",{ref:fe,class:"vxe-table-vars"},[(0,_vue.h)("div",{class:"vxe-table-var-default"}),(0,_vue.h)("div",{class:"vxe-table-var-medium"}),(0,_vue.h)("div",{class:"vxe-table-var-small"}),(0,_vue.h)("div",{class:"vxe-table-var-mini"})]),(0,_vue.h)("div",{key:"tw",class:"vxe-table--render-wrapper"},U?[Jt(),lr()]:[lr(),Jt()]),(0,_vue.h)("div",{key:"tn",ref:Re,class:"vxe-table--empty-placeholder"},[(0,_vue.h)("div",{class:"vxe-table--empty-content"},(D=dl.value,A=ue.empty,S={$table:ne,$grid:K},A?A(S):(A=(A=D.name?renderer.get(D.name):null)?A.renderTableEmpty||A.renderTableEmptyView||A.renderEmpty:null)?(0,_vn.getSlotVNs)(A(D,S)):(0,_utils.getFuncText)(W.emptyText)||getI18n("vxe.table.emptyText")))]),(0,_vue.h)("div",{key:"tl",class:"vxe-table--border-line"}),(0,_vue.h)("div",{key:"tcl",ref:Ce,class:"vxe-table--resizable-col-bar"},e.showDragTip?[(0,_vue.h)("div",{class:"vxe-table--resizable-number-tip"})]:[]),(0,_vue.h)("div",{key:"trl",ref:be,class:"vxe-table--resizable-row-bar"},e.showDragTip?[(0,_vue.h)("div",{class:"vxe-table--resizable-number-tip"})]:[]),ge?(0,_vue.h)(ge,{key:"lg",class:"vxe-table--loading",modelValue:L,icon:M.icon,text:M.text},C?{default:()=>Xt(C,{$table:ne,$grid:K,loading:L})}:{}):C?(0,_vue.h)("div",{class:["vxe-loading--custom-wrapper",{"is--visible":L}]},Xt(C,{$table:ne,$grid:K,loading:L})):renderEmptyElement(ne),x.custom?(0,_vue.h)(_panel.default,{key:"cs",ref:we,customStore:O}):renderEmptyElement(ne),x.filter?(0,_vue.h)(_panel2.default,{key:"tf",ref:_e,filterStore:H}):renderEmptyElement(ne),x.import&&W.importConfig?(0,_vue.h)(_importPanel.default,{key:"it",defaultOptions:N.importParams,storeData:N.importStore}):renderEmptyElement(ne),x.export&&(W.exportConfig||W.printConfig)?(0,_vue.h)(_exportPanel.default,{key:"et",defaultOptions:N.exportParams,storeData:N.exportStore}):renderEmptyElement(ne),G?(0,_vue.h)(_panel3.default,{key:"tm",ref:xe}):renderEmptyElement(ne),(()=>{var{dragRow:e,dragCol:l}=N,t=J.value,r=Z.value,a=Q.value,o=ee.value;return t.drag||r.drag?(0,_vue.h)("div",{class:"vxe-table--drag-wrapper"},[(0,_vue.h)("div",{ref:ye,class:["vxe-table--drag-row-line",{"is--guides":a.showGuidesStatus}]}),(0,_vue.h)("div",{ref:Te,class:["vxe-table--drag-col-line",{"is--guides":o.showGuidesStatus}]}),e&&a.showDragTip||l&&o.showDragTip?(0,_vue.h)("div",{ref:Ee,class:"vxe-table--drag-sort-tip"},[(0,_vue.h)("div",{class:"vxe-table--drag-sort-tip-wrapper"},[(0,_vue.h)("div",{class:"vxe-table--drag-sort-tip-status"},[(0,_vue.h)("span",{class:["vxe-table--drag-sort-tip-normal-status",e?getIcon().TABLE_DRAG_STATUS_ROW:getIcon().TABLE_DRAG_STATUS_COLUMN]}),(0,_vue.h)("span",{class:["vxe-table--drag-sort-tip-sub-status",getIcon().TABLE_DRAG_STATUS_SUB_ROW]}),(0,_vue.h)("span",{class:["vxe-table--drag-sort-tip-disabled-status",getIcon().TABLE_DRAG_DISABLED]})]),(0,_vue.h)("div",{class:"vxe-table--drag-sort-tip-content"},Kt())])]):renderEmptyElement(ne)]):renderEmptyElement(ne)})(),ve?(0,_vue.h)("div",{},[(0,_vue.h)(ve,{key:"ctp",ref:pe,isArrow:!1,enterable:!1}),(0,_vue.h)(ve,{key:"btp",ref:q,theme:b.theme,enterable:b.enterable,enterDelay:b.enterDelay,leaveDelay:b.leaveDelay,useHTML:b.useHTML}),W.editRules&&E.showMessage&&("default"===E.message?!r:"tooltip"===E.message)?(0,_vue.h)(ve,{key:"vtp",ref:me,class:[{"old-cell-valid":c&&"obsolete"===getConfig().cellVaildMode},"vxe-table--valid-error"],theme:R.theme,enterable:R.enterable,enterDelay:R.enterDelay,leaveDelay:R.leaveDelay}):renderEmptyElement(ne)]):renderEmptyElement(ne)])},ne},render(){return this.renderVN()}});