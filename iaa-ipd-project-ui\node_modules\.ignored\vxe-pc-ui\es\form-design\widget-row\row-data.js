import XEUtils from 'xe-utils';
import { handleGetFormDesignWidgetName } from '../render/util';
export const getWidgetRowConfig = (params) => {
    const { $formDesign } = params;
    const defaultColSize = 2;
    return {
        title: handleGetFormDesignWidgetName,
        group: 'layout',
        icon: 'vxe-icon-row-col',
        options: {
            colSize: defaultColSize,
            colSpan: '12,12'
        },
        children: $formDesign
            ? XEUtils.range(0, defaultColSize).map(() => {
                return $formDesign.createEmptyWidget();
            })
            : []
    };
};
