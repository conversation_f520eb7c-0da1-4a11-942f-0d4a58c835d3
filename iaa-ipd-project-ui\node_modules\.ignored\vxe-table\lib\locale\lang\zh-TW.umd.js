(function (global, factory) {
  if (typeof define === "function" && define.amd) {
    define("vxe-table-lang.zh-TW", ["exports", "./zh-CHT"], factory);
  } else if (typeof exports !== "undefined") {
    factory(exports, require("./zh-CHT"));
  } else {
    var mod = {
      exports: {}
    };
    factory(mod.exports, global.zhCHT);
    global.vxeTableLangZhTW = mod.exports;
  }
})(typeof globalThis !== "undefined" ? globalThis : typeof self !== "undefined" ? self : this, function (_exports, _zhCHT) {
  "use strict";

  Object.defineProperty(_exports, "__esModule", {
    value: true
  });
  _exports.default = void 0;
  _zhCHT = _interopRequireDefault(_zhCHT);
  function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
  var _default = _exports.default = _zhCHT.default;
});