import { computed } from 'vue';
import XEUtils from 'xe-utils';
export function useSubtableView(props) {
    const currWidget = computed(() => {
        const { renderParams } = props;
        return renderParams.widget;
    });
    const currColumn = computed(() => {
        const { renderParams } = props;
        return renderParams.column;
    });
    const currRow = computed(() => {
        const { renderParams } = props;
        return renderParams.row;
    });
    const widgetOptions = computed(() => {
        const { renderParams } = props;
        const { widget } = renderParams;
        return widget ? widget.options : {};
    });
    const cellModel = computed({
        get() {
            const { renderParams } = props;
            const { row, column } = renderParams;
            return XEUtils.get(row, column.field);
        },
        set(value) {
            const { renderParams } = props;
            const { row, column } = renderParams;
            return XEUtils.set(row, column.field, value);
        }
    });
    return {
        currColumn,
        currRow,
        currWidget,
        widgetOptions,
        cellModel
    };
}
