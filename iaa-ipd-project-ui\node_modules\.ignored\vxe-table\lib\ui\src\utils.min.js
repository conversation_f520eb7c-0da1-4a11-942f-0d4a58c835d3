Object.defineProperty(exports,"__esModule",{value:!0}),exports.eqEmptyValue=eqEmptyValue,exports.formatText=formatText,exports.getFuncText=getFuncText,exports.getLastZIndex=getLastZIndex,exports.hasChildrenList=hasChildrenList,exports.isEmptyValue=isEmptyValue,exports.isEnableConf=isEnableConf,exports.nextZIndex=nextZIndex,exports.parseFile=parseFile;var _xeUtils=_interopRequireDefault(require("xe-utils")),_core=require("@vxe-ui/core"),_domZindex=_interopRequireDefault(require("dom-zindex"));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function isEnableConf(e){return e&&!1!==e.enabled}function isEmptyValue(e){return null==e||""===e}function parseFile(e){var e=e.name,t=_xeUtils.default.lastIndexOf(e,"."),n=e.substring(t+1,e.length).toLowerCase();return{filename:e.substring(0,t),type:n}}function nextZIndex(){return _domZindex.default.getNext()}function getLastZIndex(){return _domZindex.default.getCurrent()}function hasChildrenList(e){return e&&e.children&&0<e.children.length}function getFuncText(e,t){var n;return e?(n=_core.VxeUI.getConfig().translate,_xeUtils.default.toValueString(n?n(""+e,t):e)):""}function formatText(e,t){return""+(isEmptyValue(e)?t?_core.VxeUI.getConfig().emptyCell:"":e)}function eqEmptyValue(e){return""===e||_xeUtils.default.eqNull(e)}