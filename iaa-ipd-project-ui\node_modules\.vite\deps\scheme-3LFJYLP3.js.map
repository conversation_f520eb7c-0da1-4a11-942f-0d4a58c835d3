{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/scheme/scheme.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    comments: {\n        lineComment: ';',\n        blockComment: ['#|', '|#']\n    },\n    brackets: [\n        ['(', ')'],\n        ['{', '}'],\n        ['[', ']']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' }\n    ],\n    surroundingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' }\n    ]\n};\nexport var language = {\n    defaultToken: '',\n    ignoreCase: true,\n    tokenPostfix: '.scheme',\n    brackets: [\n        { open: '(', close: ')', token: 'delimiter.parenthesis' },\n        { open: '{', close: '}', token: 'delimiter.curly' },\n        { open: '[', close: ']', token: 'delimiter.square' }\n    ],\n    keywords: [\n        'case',\n        'do',\n        'let',\n        'loop',\n        'if',\n        'else',\n        'when',\n        'cons',\n        'car',\n        'cdr',\n        'cond',\n        'lambda',\n        'lambda*',\n        'syntax-rules',\n        'format',\n        'set!',\n        'quote',\n        'eval',\n        'append',\n        'list',\n        'list?',\n        'member?',\n        'load'\n    ],\n    constants: ['#t', '#f'],\n    operators: ['eq?', 'eqv?', 'equal?', 'and', 'or', 'not', 'null?'],\n    tokenizer: {\n        root: [\n            [/#[xXoObB][0-9a-fA-F]+/, 'number.hex'],\n            [/[+-]?\\d+(?:(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)?/, 'number.float'],\n            [\n                /(?:\\b(?:(define|define-syntax|define-macro))\\b)(\\s+)((?:\\w|\\-|\\!|\\?)*)/,\n                ['keyword', 'white', 'variable']\n            ],\n            { include: '@whitespace' },\n            { include: '@strings' },\n            [\n                /[a-zA-Z_#][a-zA-Z0-9_\\-\\?\\!\\*]*/,\n                {\n                    cases: {\n                        '@keywords': 'keyword',\n                        '@constants': 'constant',\n                        '@operators': 'operators',\n                        '@default': 'identifier'\n                    }\n                }\n            ]\n        ],\n        comment: [\n            [/[^\\|#]+/, 'comment'],\n            [/#\\|/, 'comment', '@push'],\n            [/\\|#/, 'comment', '@pop'],\n            [/[\\|#]/, 'comment']\n        ],\n        whitespace: [\n            [/[ \\t\\r\\n]+/, 'white'],\n            [/#\\|/, 'comment', '@comment'],\n            [/;.*$/, 'comment']\n        ],\n        strings: [\n            [/\"$/, 'string', '@popall'],\n            [/\"(?=.)/, 'string', '@multiLineString']\n        ],\n        multiLineString: [\n            [/[^\\\\\"]+$/, 'string', '@popall'],\n            [/[^\\\\\"]+/, 'string'],\n            [/\\\\./, 'string.escape'],\n            [/\"/, 'string', '@popall'],\n            [/\\\\$/, 'string']\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC7B;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,UAAU;AAAA,IACN,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,EACvD;AAAA,EACA,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,WAAW,CAAC,MAAM,IAAI;AAAA,EACtB,WAAW,CAAC,OAAO,QAAQ,UAAU,OAAO,MAAM,OAAO,OAAO;AAAA,EAChE,WAAW;AAAA,IACP,MAAM;AAAA,MACF,CAAC,yBAAyB,YAAY;AAAA,MACtC,CAAC,4CAA4C,cAAc;AAAA,MAC3D;AAAA,QACI;AAAA,QACA,CAAC,WAAW,SAAS,UAAU;AAAA,MACnC;AAAA,MACA,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA,MACtB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,aAAa;AAAA,YACb,cAAc;AAAA,YACd,cAAc;AAAA,YACd,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,OAAO,WAAW,OAAO;AAAA,MAC1B,CAAC,OAAO,WAAW,MAAM;AAAA,MACzB,CAAC,SAAS,SAAS;AAAA,IACvB;AAAA,IACA,YAAY;AAAA,MACR,CAAC,cAAc,OAAO;AAAA,MACtB,CAAC,OAAO,WAAW,UAAU;AAAA,MAC7B,CAAC,QAAQ,SAAS;AAAA,IACtB;AAAA,IACA,SAAS;AAAA,MACL,CAAC,MAAM,UAAU,SAAS;AAAA,MAC1B,CAAC,UAAU,UAAU,kBAAkB;AAAA,IAC3C;AAAA,IACA,iBAAiB;AAAA,MACb,CAAC,YAAY,UAAU,SAAS;AAAA,MAChC,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,OAAO,eAAe;AAAA,MACvB,CAAC,KAAK,UAAU,SAAS;AAAA,MACzB,CAAC,OAAO,QAAQ;AAAA,IACpB;AAAA,EACJ;AACJ;", "names": []}