{"version": 3, "sources": ["../../markmap-common/dist/index.mjs"], "sourcesContent": ["const testPath = \"npm2url/dist/index.cjs\";\nconst defaultProviders = {\n  jsdelivr: (path) => `https://cdn.jsdelivr.net/npm/${path}`,\n  unpkg: (path) => `https://unpkg.com/${path}`\n};\nasync function checkUrl(url, signal) {\n  const res = await fetch(url, {\n    signal\n  });\n  if (!res.ok) {\n    throw res;\n  }\n  await res.text();\n}\nclass UrlBuilder {\n  constructor() {\n    this.providers = { ...defaultProviders };\n    this.provider = \"jsdelivr\";\n  }\n  /**\n   * Get the fastest provider name.\n   * If none of the providers returns a valid response within `timeout`, an error will be thrown.\n   */\n  async getFastestProvider(timeout = 5e3, path = testPath) {\n    const controller = new AbortController();\n    let timer = 0;\n    try {\n      return await new Promise((resolve, reject) => {\n        Promise.all(\n          Object.entries(this.providers).map(async ([name, factory]) => {\n            try {\n              await checkUrl(factory(path), controller.signal);\n              resolve(name);\n            } catch {\n            }\n          })\n        ).then(() => reject(new Error(\"All providers failed\")));\n        timer = setTimeout(reject, timeout, new Error(\"Timed out\"));\n      });\n    } finally {\n      controller.abort();\n      clearTimeout(timer);\n    }\n  }\n  /**\n   * Set the current provider to the fastest provider found by `getFastestProvider`.\n   */\n  async findFastestProvider(timeout, path) {\n    this.provider = await this.getFastestProvider(timeout, path);\n    return this.provider;\n  }\n  setProvider(name, factory) {\n    if (factory) {\n      this.providers[name] = factory;\n    } else {\n      delete this.providers[name];\n    }\n  }\n  getFullUrl(path, provider = this.provider) {\n    if (path.includes(\"://\")) {\n      return path;\n    }\n    const factory = this.providers[provider];\n    if (!factory) {\n      throw new Error(`Provider ${provider} not found`);\n    }\n    return factory(path);\n  }\n}\nconst urlBuilder = new UrlBuilder();\nclass Hook {\n  constructor() {\n    this.listeners = [];\n  }\n  tap(fn) {\n    this.listeners.push(fn);\n    return () => this.revoke(fn);\n  }\n  revoke(fn) {\n    const i = this.listeners.indexOf(fn);\n    if (i >= 0)\n      this.listeners.splice(i, 1);\n  }\n  revokeAll() {\n    this.listeners.splice(0);\n  }\n  call(...args) {\n    for (const fn of this.listeners) {\n      fn(...args);\n    }\n  }\n}\nconst escapeChars = {\n  \"&\": \"&amp;\",\n  \"<\": \"&lt;\",\n  '\"': \"&quot;\"\n};\nfunction escapeHtml(html) {\n  return html.replace(/[&<\"]/g, (m) => escapeChars[m]);\n}\nfunction escapeScript(content) {\n  return content.replace(/<(\\/script>)/g, \"\\\\x3c$2\");\n}\nfunction htmlOpen(tagName, attrs) {\n  const attrStr = attrs ? Object.entries(attrs).map(([key, value]) => {\n    if (value == null || value === false)\n      return;\n    key = ` ${escapeHtml(key)}`;\n    if (value === true)\n      return key;\n    return `${key}=\"${escapeHtml(value)}\"`;\n  }).filter(Boolean).join(\"\") : \"\";\n  return `<${tagName}${attrStr}>`;\n}\nfunction htmlClose(tagName) {\n  return `</${tagName}>`;\n}\nfunction wrapHtml(tagName, content, attrs) {\n  if (content == null)\n    return htmlOpen(tagName, attrs);\n  return htmlOpen(tagName, attrs) + (content || \"\") + htmlClose(tagName);\n}\nfunction buildCode(fn, args) {\n  const params = args.map((arg) => {\n    if (typeof arg === \"function\")\n      return arg.toString();\n    return JSON.stringify(arg ?? null);\n  }).join(\",\");\n  return `(${fn.toString()})(${params})`;\n}\nfunction persistJS(items, context) {\n  return items.map((item) => {\n    if (item.type === \"script\") {\n      const { textContent, ...rest } = item.data;\n      return wrapHtml(\n        \"script\",\n        textContent || \"\",\n        rest\n      );\n    }\n    if (item.type === \"iife\") {\n      const { fn, getParams } = item.data;\n      return wrapHtml(\n        \"script\",\n        escapeScript(buildCode(fn, (getParams == null ? void 0 : getParams(context)) || []))\n      );\n    }\n    return \"\";\n  });\n}\nfunction persistCSS(items) {\n  return items.map((item) => {\n    if (item.type === \"stylesheet\") {\n      return wrapHtml(\"link\", null, {\n        rel: \"stylesheet\",\n        ...item.data\n      });\n    }\n    return wrapHtml(\"style\", item.data);\n  });\n}\nconst uniqId = Math.random().toString(36).slice(2, 8);\nlet globalIndex = 0;\nfunction getId() {\n  globalIndex += 1;\n  return `mm-${uniqId}-${globalIndex}`;\n}\nfunction noop() {\n}\nfunction walkTree(tree, callback) {\n  const walk = (item, parent) => callback(\n    item,\n    () => {\n      var _a;\n      return (_a = item.children) == null ? void 0 : _a.map((child) => walk(child, item));\n    },\n    parent\n  );\n  return walk(tree);\n}\nfunction addClass(className, ...rest) {\n  const classList = (className || \"\").split(\" \").filter(Boolean);\n  rest.forEach((item) => {\n    if (item && classList.indexOf(item) < 0)\n      classList.push(item);\n  });\n  return classList.join(\" \");\n}\nfunction childSelector(filter) {\n  if (typeof filter === \"string\") {\n    const tagName = filter;\n    filter = (el) => el.tagName === tagName;\n  }\n  const filterFn = filter;\n  return function selector() {\n    let nodes = Array.from(this.childNodes);\n    if (filterFn)\n      nodes = nodes.filter((node) => filterFn(node));\n    return nodes;\n  };\n}\nfunction wrapFunction(fn, wrapper) {\n  return (...args) => wrapper(fn, ...args);\n}\nfunction defer() {\n  const obj = {};\n  obj.promise = new Promise((resolve, reject) => {\n    obj.resolve = resolve;\n    obj.reject = reject;\n  });\n  return obj;\n}\nfunction memoize(fn) {\n  const cache = {};\n  return function memoized(...args) {\n    const key = `${args[0]}`;\n    let data = cache[key];\n    if (!data) {\n      data = {\n        value: fn(...args)\n      };\n      cache[key] = data;\n    }\n    return data.value;\n  };\n}\nfunction debounce(fn, time) {\n  const state = {\n    timer: 0\n  };\n  function reset() {\n    if (state.timer) {\n      window.clearTimeout(state.timer);\n      state.timer = 0;\n    }\n  }\n  function run() {\n    reset();\n    if (state.args)\n      state.result = fn(...state.args);\n  }\n  return function debounced(...args) {\n    reset();\n    state.args = args;\n    state.timer = window.setTimeout(run, time);\n    return state.result;\n  };\n}\n/*! @gera2ld/jsx-dom v2.2.2 | ISC License */\nconst VTYPE_ELEMENT = 1;\nconst VTYPE_FUNCTION = 2;\nconst SVG_NS = \"http://www.w3.org/2000/svg\";\nconst XLINK_NS = \"http://www.w3.org/1999/xlink\";\nconst NS_ATTRS = {\n  show: XLINK_NS,\n  actuate: XLINK_NS,\n  href: XLINK_NS\n};\nconst isLeaf = (c) => typeof c === \"string\" || typeof c === \"number\";\nconst isElement = (c) => (c == null ? void 0 : c.vtype) === VTYPE_ELEMENT;\nconst isRenderFunction = (c) => (c == null ? void 0 : c.vtype) === VTYPE_FUNCTION;\nfunction h(type, props, ...children) {\n  props = Object.assign({}, props, {\n    children: children.length === 1 ? children[0] : children\n  });\n  return jsx(type, props);\n}\nfunction jsx(type, props) {\n  let vtype;\n  if (typeof type === \"string\")\n    vtype = VTYPE_ELEMENT;\n  else if (typeof type === \"function\")\n    vtype = VTYPE_FUNCTION;\n  else\n    throw new Error(\"Invalid VNode type\");\n  return {\n    vtype,\n    type,\n    props\n  };\n}\nfunction Fragment(props) {\n  return props.children;\n}\nconst DEFAULT_ENV = {\n  isSvg: false\n};\nfunction insertDom(parent, nodes) {\n  if (!Array.isArray(nodes))\n    nodes = [nodes];\n  nodes = nodes.filter(Boolean);\n  if (nodes.length)\n    parent.append(...nodes);\n}\nfunction mountAttributes(domElement, props, env) {\n  for (const key in props) {\n    if (key === \"key\" || key === \"children\" || key === \"ref\")\n      continue;\n    if (key === \"dangerouslySetInnerHTML\") {\n      domElement.innerHTML = props[key].__html;\n    } else if (key === \"innerHTML\" || key === \"textContent\" || key === \"innerText\" || key === \"value\" && [\"textarea\", \"select\"].includes(domElement.tagName)) {\n      const value = props[key];\n      if (value != null)\n        domElement[key] = value;\n    } else if (key.startsWith(\"on\")) {\n      domElement[key.toLowerCase()] = props[key];\n    } else {\n      setDOMAttribute(domElement, key, props[key], env.isSvg);\n    }\n  }\n}\nconst attrMap = {\n  className: \"class\",\n  labelFor: \"for\"\n};\nfunction setDOMAttribute(el, attr, value, isSVG) {\n  attr = attrMap[attr] || attr;\n  if (value === true) {\n    el.setAttribute(attr, \"\");\n  } else if (value === false) {\n    el.removeAttribute(attr);\n  } else {\n    const namespace = isSVG ? NS_ATTRS[attr] : void 0;\n    if (namespace !== void 0) {\n      el.setAttributeNS(namespace, attr, value);\n    } else {\n      el.setAttribute(attr, value);\n    }\n  }\n}\nfunction flatten(arr) {\n  return arr.reduce((prev, item) => prev.concat(item), []);\n}\nfunction mountChildren(children, env) {\n  return Array.isArray(children) ? flatten(children.map((child) => mountChildren(child, env))) : mount(children, env);\n}\nfunction mount(vnode, env = DEFAULT_ENV) {\n  if (vnode == null || typeof vnode === \"boolean\") {\n    return null;\n  }\n  if (vnode instanceof Node) {\n    return vnode;\n  }\n  if (isRenderFunction(vnode)) {\n    const {\n      type,\n      props\n    } = vnode;\n    if (type === Fragment) {\n      const node = document.createDocumentFragment();\n      if (props.children) {\n        const children = mountChildren(props.children, env);\n        insertDom(node, children);\n      }\n      return node;\n    }\n    const childVNode = type(props);\n    return mount(childVNode, env);\n  }\n  if (isLeaf(vnode)) {\n    return document.createTextNode(`${vnode}`);\n  }\n  if (isElement(vnode)) {\n    let node;\n    const {\n      type,\n      props\n    } = vnode;\n    if (!env.isSvg && type === \"svg\") {\n      env = Object.assign({}, env, {\n        isSvg: true\n      });\n    }\n    if (!env.isSvg) {\n      node = document.createElement(type);\n    } else {\n      node = document.createElementNS(SVG_NS, type);\n    }\n    mountAttributes(node, props, env);\n    if (props.children) {\n      let childEnv = env;\n      if (env.isSvg && type === \"foreignObject\") {\n        childEnv = Object.assign({}, childEnv, {\n          isSvg: false\n        });\n      }\n      const children = mountChildren(props.children, childEnv);\n      if (children != null)\n        insertDom(node, children);\n    }\n    const {\n      ref\n    } = props;\n    if (typeof ref === \"function\")\n      ref(node);\n    return node;\n  }\n  throw new Error(\"mount: Invalid Vnode!\");\n}\nfunction mountDom(vnode) {\n  return mount(vnode);\n}\nfunction hm(...args) {\n  return mountDom(h(...args));\n}\nconst memoizedPreloadJS = memoize((url) => {\n  document.head.append(\n    hm(\"link\", {\n      rel: \"preload\",\n      as: \"script\",\n      href: url\n    })\n  );\n});\nconst jsCache = {};\nconst cssCache = {};\nasync function loadJSItem(item, context) {\n  var _a;\n  const src = item.type === \"script\" && ((_a = item.data) == null ? void 0 : _a.src) || \"\";\n  item.loaded || (item.loaded = jsCache[src]);\n  if (!item.loaded) {\n    const deferred = defer();\n    item.loaded = deferred.promise;\n    if (item.type === \"script\") {\n      document.head.append(\n        hm(\"script\", {\n          ...item.data,\n          onLoad: () => deferred.resolve(),\n          onError: deferred.reject\n        })\n      );\n      if (!src) {\n        deferred.resolve();\n      } else {\n        jsCache[src] = item.loaded;\n      }\n    }\n    if (item.type === \"iife\") {\n      const { fn, getParams } = item.data;\n      fn(...(getParams == null ? void 0 : getParams(context)) || []);\n      deferred.resolve();\n    }\n  }\n  await item.loaded;\n}\nasync function loadCSSItem(item) {\n  const url = item.type === \"stylesheet\" && item.data.href || \"\";\n  item.loaded || (item.loaded = cssCache[url]);\n  if (!item.loaded) {\n    const deferred = defer();\n    item.loaded = deferred.promise;\n    if (url)\n      cssCache[url] = item.loaded;\n    if (item.type === \"style\") {\n      document.head.append(\n        hm(\"style\", {\n          textContent: item.data\n        })\n      );\n      deferred.resolve();\n    } else if (url) {\n      document.head.append(\n        hm(\"link\", {\n          rel: \"stylesheet\",\n          ...item.data\n        })\n      );\n      fetch(url).then((res) => {\n        if (res.ok)\n          return res.text();\n        throw res;\n      }).then(() => deferred.resolve(), deferred.reject);\n    }\n  }\n  await item.loaded;\n}\nasync function loadJS(items, context) {\n  items.forEach((item) => {\n    var _a;\n    if (item.type === \"script\" && ((_a = item.data) == null ? void 0 : _a.src)) {\n      memoizedPreloadJS(item.data.src);\n    }\n  });\n  context = {\n    getMarkmap: () => window.markmap,\n    ...context\n  };\n  for (const item of items) {\n    await loadJSItem(item, context);\n  }\n}\nasync function loadCSS(items) {\n  await Promise.all(items.map((item) => loadCSSItem(item)));\n}\nfunction buildJSItem(path) {\n  return {\n    type: \"script\",\n    data: {\n      src: path\n    }\n  };\n}\nfunction buildCSSItem(path) {\n  return {\n    type: \"stylesheet\",\n    data: {\n      href: path\n    }\n  };\n}\nexport {\n  Hook,\n  UrlBuilder,\n  addClass,\n  buildCSSItem,\n  buildCode,\n  buildJSItem,\n  childSelector,\n  debounce,\n  defer,\n  escapeHtml,\n  escapeScript,\n  getId,\n  htmlClose,\n  htmlOpen,\n  loadCSS,\n  loadJS,\n  memoize,\n  noop,\n  persistCSS,\n  persistJS,\n  urlBuilder,\n  walkTree,\n  wrapFunction,\n  wrapHtml\n};\n"], "mappings": ";AAAA,IAAM,WAAW;AACjB,IAAM,mBAAmB;AAAA,EACvB,UAAU,CAAC,SAAS,gCAAgC,IAAI;AAAA,EACxD,OAAO,CAAC,SAAS,qBAAqB,IAAI;AAC5C;AACA,eAAe,SAAS,KAAK,QAAQ;AACnC,QAAM,MAAM,MAAM,MAAM,KAAK;AAAA,IAC3B;AAAA,EACF,CAAC;AACD,MAAI,CAAC,IAAI,IAAI;AACX,UAAM;AAAA,EACR;AACA,QAAM,IAAI,KAAK;AACjB;AACA,IAAM,aAAN,MAAiB;AAAA,EACf,cAAc;AACZ,SAAK,YAAY,EAAE,GAAG,iBAAiB;AACvC,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,mBAAmB,UAAU,KAAK,OAAO,UAAU;AACvD,UAAM,aAAa,IAAI,gBAAgB;AACvC,QAAI,QAAQ;AACZ,QAAI;AACF,aAAO,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC5C,gBAAQ;AAAA,UACN,OAAO,QAAQ,KAAK,SAAS,EAAE,IAAI,OAAO,CAAC,MAAM,OAAO,MAAM;AAC5D,gBAAI;AACF,oBAAM,SAAS,QAAQ,IAAI,GAAG,WAAW,MAAM;AAC/C,sBAAQ,IAAI;AAAA,YACd,QAAQ;AAAA,YACR;AAAA,UACF,CAAC;AAAA,QACH,EAAE,KAAK,MAAM,OAAO,IAAI,MAAM,sBAAsB,CAAC,CAAC;AACtD,gBAAQ,WAAW,QAAQ,SAAS,IAAI,MAAM,WAAW,CAAC;AAAA,MAC5D,CAAC;AAAA,IACH,UAAE;AACA,iBAAW,MAAM;AACjB,mBAAa,KAAK;AAAA,IACpB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,oBAAoB,SAAS,MAAM;AACvC,SAAK,WAAW,MAAM,KAAK,mBAAmB,SAAS,IAAI;AAC3D,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,MAAM,SAAS;AACzB,QAAI,SAAS;AACX,WAAK,UAAU,IAAI,IAAI;AAAA,IACzB,OAAO;AACL,aAAO,KAAK,UAAU,IAAI;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,WAAW,MAAM,WAAW,KAAK,UAAU;AACzC,QAAI,KAAK,SAAS,KAAK,GAAG;AACxB,aAAO;AAAA,IACT;AACA,UAAM,UAAU,KAAK,UAAU,QAAQ;AACvC,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,MAAM,YAAY,QAAQ,YAAY;AAAA,IAClD;AACA,WAAO,QAAQ,IAAI;AAAA,EACrB;AACF;AACA,IAAM,aAAa,IAAI,WAAW;AAClC,IAAM,OAAN,MAAW;AAAA,EACT,cAAc;AACZ,SAAK,YAAY,CAAC;AAAA,EACpB;AAAA,EACA,IAAI,IAAI;AACN,SAAK,UAAU,KAAK,EAAE;AACtB,WAAO,MAAM,KAAK,OAAO,EAAE;AAAA,EAC7B;AAAA,EACA,OAAO,IAAI;AACT,UAAM,IAAI,KAAK,UAAU,QAAQ,EAAE;AACnC,QAAI,KAAK;AACP,WAAK,UAAU,OAAO,GAAG,CAAC;AAAA,EAC9B;AAAA,EACA,YAAY;AACV,SAAK,UAAU,OAAO,CAAC;AAAA,EACzB;AAAA,EACA,QAAQ,MAAM;AACZ,eAAW,MAAM,KAAK,WAAW;AAC/B,SAAG,GAAG,IAAI;AAAA,IACZ;AAAA,EACF;AACF;AAsEA,IAAM,SAAS,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,MAAM,GAAG,CAAC;AACpD,IAAI,cAAc;AAClB,SAAS,QAAQ;AACf,iBAAe;AACf,SAAO,MAAM,MAAM,IAAI,WAAW;AACpC;AACA,SAAS,OAAO;AAChB;AACA,SAAS,SAAS,MAAM,UAAU;AAChC,QAAM,OAAO,CAAC,MAAM,WAAW;AAAA,IAC7B;AAAA,IACA,MAAM;AACJ,UAAI;AACJ,cAAQ,KAAK,KAAK,aAAa,OAAO,SAAS,GAAG,IAAI,CAAC,UAAU,KAAK,OAAO,IAAI,CAAC;AAAA,IACpF;AAAA,IACA;AAAA,EACF;AACA,SAAO,KAAK,IAAI;AAClB;AACA,SAAS,SAAS,cAAc,MAAM;AACpC,QAAM,aAAa,aAAa,IAAI,MAAM,GAAG,EAAE,OAAO,OAAO;AAC7D,OAAK,QAAQ,CAAC,SAAS;AACrB,QAAI,QAAQ,UAAU,QAAQ,IAAI,IAAI;AACpC,gBAAU,KAAK,IAAI;AAAA,EACvB,CAAC;AACD,SAAO,UAAU,KAAK,GAAG;AAC3B;AACA,SAAS,cAAc,QAAQ;AAC7B,MAAI,OAAO,WAAW,UAAU;AAC9B,UAAM,UAAU;AAChB,aAAS,CAAC,OAAO,GAAG,YAAY;AAAA,EAClC;AACA,QAAM,WAAW;AACjB,SAAO,SAAS,WAAW;AACzB,QAAI,QAAQ,MAAM,KAAK,KAAK,UAAU;AACtC,QAAI;AACF,cAAQ,MAAM,OAAO,CAAC,SAAS,SAAS,IAAI,CAAC;AAC/C,WAAO;AAAA,EACT;AACF;AACA,SAAS,aAAa,IAAI,SAAS;AACjC,SAAO,IAAI,SAAS,QAAQ,IAAI,GAAG,IAAI;AACzC;AACA,SAAS,QAAQ;AACf,QAAM,MAAM,CAAC;AACb,MAAI,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7C,QAAI,UAAU;AACd,QAAI,SAAS;AAAA,EACf,CAAC;AACD,SAAO;AACT;AACA,SAAS,QAAQ,IAAI;AACnB,QAAM,QAAQ,CAAC;AACf,SAAO,SAAS,YAAY,MAAM;AAChC,UAAM,MAAM,GAAG,KAAK,CAAC,CAAC;AACtB,QAAI,OAAO,MAAM,GAAG;AACpB,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,QACL,OAAO,GAAG,GAAG,IAAI;AAAA,MACnB;AACA,YAAM,GAAG,IAAI;AAAA,IACf;AACA,WAAO,KAAK;AAAA,EACd;AACF;AACA,SAAS,SAAS,IAAI,MAAM;AAC1B,QAAM,QAAQ;AAAA,IACZ,OAAO;AAAA,EACT;AACA,WAAS,QAAQ;AACf,QAAI,MAAM,OAAO;AACf,aAAO,aAAa,MAAM,KAAK;AAC/B,YAAM,QAAQ;AAAA,IAChB;AAAA,EACF;AACA,WAAS,MAAM;AACb,UAAM;AACN,QAAI,MAAM;AACR,YAAM,SAAS,GAAG,GAAG,MAAM,IAAI;AAAA,EACnC;AACA,SAAO,SAAS,aAAa,MAAM;AACjC,UAAM;AACN,UAAM,OAAO;AACb,UAAM,QAAQ,OAAO,WAAW,KAAK,IAAI;AACzC,WAAO,MAAM;AAAA,EACf;AACF;AAEA,IAAM,gBAAgB;AACtB,IAAM,iBAAiB;AACvB,IAAM,SAAS;AACf,IAAM,WAAW;AACjB,IAAM,WAAW;AAAA,EACf,MAAM;AAAA,EACN,SAAS;AAAA,EACT,MAAM;AACR;AACA,IAAM,SAAS,CAAC,MAAM,OAAO,MAAM,YAAY,OAAO,MAAM;AAC5D,IAAM,YAAY,CAAC,OAAO,KAAK,OAAO,SAAS,EAAE,WAAW;AAC5D,IAAM,mBAAmB,CAAC,OAAO,KAAK,OAAO,SAAS,EAAE,WAAW;AACnE,SAAS,EAAE,MAAM,UAAU,UAAU;AACnC,UAAQ,OAAO,OAAO,CAAC,GAAG,OAAO;AAAA,IAC/B,UAAU,SAAS,WAAW,IAAI,SAAS,CAAC,IAAI;AAAA,EAClD,CAAC;AACD,SAAO,IAAI,MAAM,KAAK;AACxB;AACA,SAAS,IAAI,MAAM,OAAO;AACxB,MAAI;AACJ,MAAI,OAAO,SAAS;AAClB,YAAQ;AAAA,WACD,OAAO,SAAS;AACvB,YAAQ;AAAA;AAER,UAAM,IAAI,MAAM,oBAAoB;AACtC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,SAAS,OAAO;AACvB,SAAO,MAAM;AACf;AACA,IAAM,cAAc;AAAA,EAClB,OAAO;AACT;AACA,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,CAAC,MAAM,QAAQ,KAAK;AACtB,YAAQ,CAAC,KAAK;AAChB,UAAQ,MAAM,OAAO,OAAO;AAC5B,MAAI,MAAM;AACR,WAAO,OAAO,GAAG,KAAK;AAC1B;AACA,SAAS,gBAAgB,YAAY,OAAO,KAAK;AAC/C,aAAW,OAAO,OAAO;AACvB,QAAI,QAAQ,SAAS,QAAQ,cAAc,QAAQ;AACjD;AACF,QAAI,QAAQ,2BAA2B;AACrC,iBAAW,YAAY,MAAM,GAAG,EAAE;AAAA,IACpC,WAAW,QAAQ,eAAe,QAAQ,iBAAiB,QAAQ,eAAe,QAAQ,WAAW,CAAC,YAAY,QAAQ,EAAE,SAAS,WAAW,OAAO,GAAG;AACxJ,YAAM,QAAQ,MAAM,GAAG;AACvB,UAAI,SAAS;AACX,mBAAW,GAAG,IAAI;AAAA,IACtB,WAAW,IAAI,WAAW,IAAI,GAAG;AAC/B,iBAAW,IAAI,YAAY,CAAC,IAAI,MAAM,GAAG;AAAA,IAC3C,OAAO;AACL,sBAAgB,YAAY,KAAK,MAAM,GAAG,GAAG,IAAI,KAAK;AAAA,IACxD;AAAA,EACF;AACF;AACA,IAAM,UAAU;AAAA,EACd,WAAW;AAAA,EACX,UAAU;AACZ;AACA,SAAS,gBAAgB,IAAI,MAAM,OAAO,OAAO;AAC/C,SAAO,QAAQ,IAAI,KAAK;AACxB,MAAI,UAAU,MAAM;AAClB,OAAG,aAAa,MAAM,EAAE;AAAA,EAC1B,WAAW,UAAU,OAAO;AAC1B,OAAG,gBAAgB,IAAI;AAAA,EACzB,OAAO;AACL,UAAM,YAAY,QAAQ,SAAS,IAAI,IAAI;AAC3C,QAAI,cAAc,QAAQ;AACxB,SAAG,eAAe,WAAW,MAAM,KAAK;AAAA,IAC1C,OAAO;AACL,SAAG,aAAa,MAAM,KAAK;AAAA,IAC7B;AAAA,EACF;AACF;AACA,SAAS,QAAQ,KAAK;AACpB,SAAO,IAAI,OAAO,CAAC,MAAM,SAAS,KAAK,OAAO,IAAI,GAAG,CAAC,CAAC;AACzD;AACA,SAAS,cAAc,UAAU,KAAK;AACpC,SAAO,MAAM,QAAQ,QAAQ,IAAI,QAAQ,SAAS,IAAI,CAAC,UAAU,cAAc,OAAO,GAAG,CAAC,CAAC,IAAI,MAAM,UAAU,GAAG;AACpH;AACA,SAAS,MAAM,OAAO,MAAM,aAAa;AACvC,MAAI,SAAS,QAAQ,OAAO,UAAU,WAAW;AAC/C,WAAO;AAAA,EACT;AACA,MAAI,iBAAiB,MAAM;AACzB,WAAO;AAAA,EACT;AACA,MAAI,iBAAiB,KAAK,GAAG;AAC3B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,SAAS,UAAU;AACrB,YAAM,OAAO,SAAS,uBAAuB;AAC7C,UAAI,MAAM,UAAU;AAClB,cAAM,WAAW,cAAc,MAAM,UAAU,GAAG;AAClD,kBAAU,MAAM,QAAQ;AAAA,MAC1B;AACA,aAAO;AAAA,IACT;AACA,UAAM,aAAa,KAAK,KAAK;AAC7B,WAAO,MAAM,YAAY,GAAG;AAAA,EAC9B;AACA,MAAI,OAAO,KAAK,GAAG;AACjB,WAAO,SAAS,eAAe,GAAG,KAAK,EAAE;AAAA,EAC3C;AACA,MAAI,UAAU,KAAK,GAAG;AACpB,QAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,IAAI,SAAS,SAAS,OAAO;AAChC,YAAM,OAAO,OAAO,CAAC,GAAG,KAAK;AAAA,QAC3B,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AACA,QAAI,CAAC,IAAI,OAAO;AACd,aAAO,SAAS,cAAc,IAAI;AAAA,IACpC,OAAO;AACL,aAAO,SAAS,gBAAgB,QAAQ,IAAI;AAAA,IAC9C;AACA,oBAAgB,MAAM,OAAO,GAAG;AAChC,QAAI,MAAM,UAAU;AAClB,UAAI,WAAW;AACf,UAAI,IAAI,SAAS,SAAS,iBAAiB;AACzC,mBAAW,OAAO,OAAO,CAAC,GAAG,UAAU;AAAA,UACrC,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AACA,YAAM,WAAW,cAAc,MAAM,UAAU,QAAQ;AACvD,UAAI,YAAY;AACd,kBAAU,MAAM,QAAQ;AAAA,IAC5B;AACA,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,OAAO,QAAQ;AACjB,UAAI,IAAI;AACV,WAAO;AAAA,EACT;AACA,QAAM,IAAI,MAAM,uBAAuB;AACzC;AACA,SAAS,SAAS,OAAO;AACvB,SAAO,MAAM,KAAK;AACpB;AACA,SAAS,MAAM,MAAM;AACnB,SAAO,SAAS,EAAE,GAAG,IAAI,CAAC;AAC5B;AACA,IAAM,oBAAoB,QAAQ,CAAC,QAAQ;AACzC,WAAS,KAAK;AAAA,IACZ,GAAG,QAAQ;AAAA,MACT,KAAK;AAAA,MACL,IAAI;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AACF,CAAC;AACD,IAAM,UAAU,CAAC;AACjB,IAAM,WAAW,CAAC;AAClB,eAAe,WAAW,MAAM,SAAS;AACvC,MAAI;AACJ,QAAM,MAAM,KAAK,SAAS,cAAc,KAAK,KAAK,SAAS,OAAO,SAAS,GAAG,QAAQ;AACtF,OAAK,WAAW,KAAK,SAAS,QAAQ,GAAG;AACzC,MAAI,CAAC,KAAK,QAAQ;AAChB,UAAM,WAAW,MAAM;AACvB,SAAK,SAAS,SAAS;AACvB,QAAI,KAAK,SAAS,UAAU;AAC1B,eAAS,KAAK;AAAA,QACZ,GAAG,UAAU;AAAA,UACX,GAAG,KAAK;AAAA,UACR,QAAQ,MAAM,SAAS,QAAQ;AAAA,UAC/B,SAAS,SAAS;AAAA,QACpB,CAAC;AAAA,MACH;AACA,UAAI,CAAC,KAAK;AACR,iBAAS,QAAQ;AAAA,MACnB,OAAO;AACL,gBAAQ,GAAG,IAAI,KAAK;AAAA,MACtB;AAAA,IACF;AACA,QAAI,KAAK,SAAS,QAAQ;AACxB,YAAM,EAAE,IAAI,UAAU,IAAI,KAAK;AAC/B,SAAG,IAAI,aAAa,OAAO,SAAS,UAAU,OAAO,MAAM,CAAC,CAAC;AAC7D,eAAS,QAAQ;AAAA,IACnB;AAAA,EACF;AACA,QAAM,KAAK;AACb;AACA,eAAe,YAAY,MAAM;AAC/B,QAAM,MAAM,KAAK,SAAS,gBAAgB,KAAK,KAAK,QAAQ;AAC5D,OAAK,WAAW,KAAK,SAAS,SAAS,GAAG;AAC1C,MAAI,CAAC,KAAK,QAAQ;AAChB,UAAM,WAAW,MAAM;AACvB,SAAK,SAAS,SAAS;AACvB,QAAI;AACF,eAAS,GAAG,IAAI,KAAK;AACvB,QAAI,KAAK,SAAS,SAAS;AACzB,eAAS,KAAK;AAAA,QACZ,GAAG,SAAS;AAAA,UACV,aAAa,KAAK;AAAA,QACpB,CAAC;AAAA,MACH;AACA,eAAS,QAAQ;AAAA,IACnB,WAAW,KAAK;AACd,eAAS,KAAK;AAAA,QACZ,GAAG,QAAQ;AAAA,UACT,KAAK;AAAA,UACL,GAAG,KAAK;AAAA,QACV,CAAC;AAAA,MACH;AACA,YAAM,GAAG,EAAE,KAAK,CAAC,QAAQ;AACvB,YAAI,IAAI;AACN,iBAAO,IAAI,KAAK;AAClB,cAAM;AAAA,MACR,CAAC,EAAE,KAAK,MAAM,SAAS,QAAQ,GAAG,SAAS,MAAM;AAAA,IACnD;AAAA,EACF;AACA,QAAM,KAAK;AACb;AACA,eAAe,OAAO,OAAO,SAAS;AACpC,QAAM,QAAQ,CAAC,SAAS;AACtB,QAAI;AACJ,QAAI,KAAK,SAAS,cAAc,KAAK,KAAK,SAAS,OAAO,SAAS,GAAG,MAAM;AAC1E,wBAAkB,KAAK,KAAK,GAAG;AAAA,IACjC;AAAA,EACF,CAAC;AACD,YAAU;AAAA,IACR,YAAY,MAAM,OAAO;AAAA,IACzB,GAAG;AAAA,EACL;AACA,aAAW,QAAQ,OAAO;AACxB,UAAM,WAAW,MAAM,OAAO;AAAA,EAChC;AACF;AACA,eAAe,QAAQ,OAAO;AAC5B,QAAM,QAAQ,IAAI,MAAM,IAAI,CAAC,SAAS,YAAY,IAAI,CAAC,CAAC;AAC1D;AACA,SAAS,YAAY,MAAM;AACzB,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,MACJ,KAAK;AAAA,IACP;AAAA,EACF;AACF;AACA,SAAS,aAAa,MAAM;AAC1B,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,MACJ,MAAM;AAAA,IACR;AAAA,EACF;AACF;", "names": []}