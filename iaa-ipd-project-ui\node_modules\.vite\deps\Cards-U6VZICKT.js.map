{"version": 3, "sources": ["../../amis/esm/renderers/Cards.js"], "sourcesContent": ["/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __assign, __awaiter, __generator, __decorate, __metadata } from 'tslib';\nimport React from 'react';\nimport { findDOMNode } from 'react-dom';\nimport { getPropValue, resolveVariableAndFilter, anyChanged, isPureVariable, createObject, difference, filter, filterClassNameObject, ucFirst, setThemeClassName, buildStyle, CustomStyle, getMatchedEventTargets, ScopedContext, Renderer, ListStore } from 'amis-core';\nimport { Icon, But<PERSON>, Spinner } from 'amis-ui';\nimport Sortable from 'sortablejs';\nimport find from 'lodash/find';\n\nvar Cards = /** @class */ (function (_super) {\n    __extends(Cards, _super);\n    function Cards(props) {\n        var _this = _super.call(this, props) || this;\n        _this.handleAction = _this.handleAction.bind(_this);\n        _this.handleCheck = _this.handleCheck.bind(_this);\n        _this.handleClick = _this.handleClick.bind(_this);\n        _this.handleCheckAll = _this.handleCheckAll.bind(_this);\n        _this.handleQuickChange = _this.handleQuickChange.bind(_this);\n        _this.handleSave = _this.handleSave.bind(_this);\n        _this.handleSaveOrder = _this.handleSaveOrder.bind(_this);\n        _this.reset = _this.reset.bind(_this);\n        _this.dragTipRef = _this.dragTipRef.bind(_this);\n        _this.bodyRef = _this.bodyRef.bind(_this);\n        _this.renderToolbar = _this.renderToolbar.bind(_this);\n        var store = props.store, selectable = props.selectable, draggable = props.draggable, orderBy = props.orderBy, orderDir = props.orderDir, multiple = props.multiple, hideCheckToggler = props.hideCheckToggler, itemCheckableOn = props.itemCheckableOn, itemDraggableOn = props.itemDraggableOn;\n        store.update({\n            selectable: selectable,\n            draggable: draggable,\n            orderBy: orderBy,\n            orderDir: orderDir,\n            multiple: multiple,\n            hideCheckToggler: hideCheckToggler,\n            itemCheckableOn: itemCheckableOn,\n            itemDraggableOn: itemDraggableOn\n        });\n        Cards.syncItems(store, _this.props) && _this.syncSelected();\n        return _this;\n    }\n    Cards.syncItems = function (store, props, prevProps) {\n        var source = props.source;\n        var value = getPropValue(props, function (props) { return props.items; });\n        var items = [];\n        var updateItems = false;\n        if (Array.isArray(value) &&\n            (!prevProps ||\n                getPropValue(prevProps, function (props) { return props.items; }) !== value)) {\n            items = value;\n            updateItems = true;\n        }\n        else if (typeof source === 'string') {\n            var resolved = resolveVariableAndFilter(source, props.data, '| raw');\n            var prev = prevProps\n                ? resolveVariableAndFilter(source, prevProps.data, '| raw')\n                : null;\n            if (prev === resolved) {\n                updateItems = false;\n            }\n            else {\n                items = Array.isArray(resolved) ? resolved : [];\n                updateItems = true;\n            }\n        }\n        updateItems && store.initItems(items, props.fullItems, props.selected);\n        Array.isArray(props.selected) &&\n            store.updateSelected(props.selected, props.valueField);\n        return updateItems;\n    };\n    Cards.prototype.componentDidUpdate = function (prevProps) {\n        var props = this.props;\n        var store = props.store;\n        if (anyChanged([\n            'selectable',\n            'draggable',\n            'orderBy',\n            'orderDir',\n            'multiple',\n            'hideCheckToggler',\n            'itemCheckableOn',\n            'itemDraggableOn'\n        ], prevProps, props)) {\n            store.update({\n                selectable: props.selectable,\n                draggable: props.draggable,\n                orderBy: props.orderBy,\n                orderDir: props.orderDir,\n                multiple: props.multiple,\n                hideCheckToggler: props.hideCheckToggler,\n                itemCheckableOn: props.itemCheckableOn,\n                itemDraggableOn: props.itemDraggableOn\n            });\n        }\n        if (anyChanged(['source', 'value', 'items'], prevProps, props) ||\n            (!props.value &&\n                !props.items &&\n                (props.data !== prevProps.data ||\n                    (typeof props.source === 'string' && isPureVariable(props.source))))) {\n            Cards.syncItems(store, props, prevProps) && this.syncSelected();\n        }\n        else if (prevProps.selected !== props.selected) {\n            store.updateSelected(props.selected || [], props.valueField);\n        }\n    };\n    Cards.prototype.bodyRef = function (ref) {\n        this.body = ref;\n    };\n    Cards.prototype.handleAction = function (e, action, ctx) {\n        var onAction = this.props.onAction;\n        // 需要支持特殊事件吗？\n        return onAction === null || onAction === void 0 ? void 0 : onAction(e, action, ctx);\n    };\n    Cards.prototype.handleCheck = function (item) {\n        item.toggle();\n        this.syncSelected();\n        var _a = this.props, store = _a.store, dispatchEvent = _a.dispatchEvent;\n        dispatchEvent(\n        //增删改查卡片模式选择表格项\n        'selectedChange', createObject(store.data, __assign(__assign({}, store.eventContext), { item: item.data })));\n    };\n    Cards.prototype.handleClick = function (item) {\n        var _a = this.props, dispatchEvent = _a.dispatchEvent, data = _a.data;\n        return dispatchEvent(\n        //增删改查卡片模式单击卡片\n        'rowClick', createObject(data, {\n            item: item.data,\n            index: item.index\n        }));\n    };\n    Cards.prototype.handleCheckAll = function () {\n        var store = this.props.store;\n        store.toggleAll();\n        this.syncSelected();\n    };\n    Cards.prototype.handleSelectAll = function () {\n        var store = this.props.store;\n        store.selectAll();\n        this.syncSelected();\n    };\n    Cards.prototype.handleClearAll = function () {\n        var store = this.props.store;\n        store.clearAll();\n        this.syncSelected();\n    };\n    Cards.prototype.syncSelected = function () {\n        var _a = this.props, store = _a.store, onSelect = _a.onSelect, dispatchEvent = _a.dispatchEvent;\n        var selectItems = store.selectedItems.map(function (item) { return item.data; });\n        var unSelectItems = store.unSelectedItems.map(function (item) { return item.data; });\n        dispatchEvent('selected', createObject(store.data, {\n            selectItems: selectItems,\n            unSelectItems: unSelectItems\n        }));\n        onSelect && onSelect(selectItems, unSelectItems);\n    };\n    Cards.prototype.handleQuickChange = function (item, values, saveImmediately, savePristine, options) {\n        item.change(values, savePristine);\n        var _a = this.props, onSave = _a.onSave, onItemChange = _a.onItemChange, primaryField = _a.primaryField;\n        if (savePristine) {\n            return;\n        }\n        onItemChange === null || onItemChange === void 0 ? void 0 : onItemChange(item.data, difference(item.data, item.pristine, ['id', primaryField]), item.index);\n        if (saveImmediately && saveImmediately.api) {\n            this.props.onAction(null, {\n                actionType: 'ajax',\n                api: saveImmediately.api,\n                reload: options === null || options === void 0 ? void 0 : options.reload\n            }, item.locals);\n            return;\n        }\n        if (!saveImmediately || !onSave) {\n            return;\n        }\n        onSave(item.data, difference(item.data, item.pristine, ['id', primaryField]), item.index, undefined, item.pristine, options);\n    };\n    Cards.prototype.handleSave = function () {\n        var _a = this.props, store = _a.store, onSave = _a.onSave, primaryField = _a.primaryField;\n        if (!onSave || !store.modifiedItems.length) {\n            return;\n        }\n        var items = store.modifiedItems.map(function (item) { return item.data; });\n        var itemIndexes = store.modifiedItems.map(function (item) { return item.index; });\n        var diff = store.modifiedItems.map(function (item) {\n            return difference(item.data, item.pristine, ['id', primaryField]);\n        });\n        var unModifiedItems = store.items\n            .filter(function (item) { return !item.modified; })\n            .map(function (item) { return item.data; });\n        onSave(items, diff, itemIndexes, unModifiedItems, store.modifiedItems.map(function (item) { return item.pristine; }));\n    };\n    Cards.prototype.handleSaveOrder = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var _a, store, onSaveOrder, data, dispatchEvent, movedItems, rendererEvent;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        _a = this.props, store = _a.store, onSaveOrder = _a.onSaveOrder, data = _a.data, dispatchEvent = _a.dispatchEvent;\n                        movedItems = store.movedItems.map(function (item) { return item.data; });\n                        return [4 /*yield*/, dispatchEvent('orderChange', createObject(data, { movedItems: movedItems }))];\n                    case 1:\n                        rendererEvent = _b.sent();\n                        if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {\n                            return [2 /*return*/];\n                        }\n                        if (!onSaveOrder || !store.movedItems.length) {\n                            return [2 /*return*/];\n                        }\n                        onSaveOrder(store.movedItems.map(function (item) { return item.data; }), store.items.map(function (item) { return item.data; }));\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    Cards.prototype.reset = function () {\n        var store = this.props.store;\n        store.reset();\n    };\n    Cards.prototype.bulkUpdate = function (value, items) {\n        // const {store} = this.props;\n        // const items2 = store.items.filter(item => ~items.indexOf(item.pristine));\n        // items2.forEach(item => item.change(value));\n        var _a = this.props, store = _a.store, primaryField = _a.primaryField;\n        if (primaryField && value.ids) {\n            var ids_1 = value.ids.split(',');\n            var rows = store.items.filter(function (item) {\n                return find(ids_1, function (id) { return id && id == item.data[primaryField]; });\n            });\n            var newValue_1 = __assign(__assign({}, value), { ids: undefined });\n            rows.forEach(function (item) { return item.change(newValue_1); });\n        }\n        else if (Array.isArray(items)) {\n            var rows = store.items.filter(function (item) { return ~items.indexOf(item.pristine); });\n            rows.forEach(function (item) { return item.change(value); });\n        }\n    };\n    Cards.prototype.getSelected = function () {\n        var store = this.props.store;\n        return store.selectedItems.map(function (item) { return item.data; });\n    };\n    Cards.prototype.dragTipRef = function (ref) {\n        if (!this.dragTip && ref) {\n            this.initDragging();\n        }\n        else if (this.dragTip && !ref) {\n            this.destroyDragging();\n        }\n        this.dragTip = ref;\n    };\n    Cards.prototype.initDragging = function () {\n        if (this.sortable)\n            return;\n        var store = this.props.store;\n        var dom = findDOMNode(this);\n        var ns = this.props.classPrefix;\n        this.sortable = new Sortable(dom.querySelector(\".\".concat(ns, \"Cards-body\")), {\n            group: 'table',\n            animation: 150,\n            handle: \".\".concat(ns, \"Card-dragBtn\"),\n            ghostClass: \"is-dragging\",\n            onEnd: function (e) {\n                // 没有移动\n                if (e.newIndex === e.oldIndex) {\n                    return;\n                }\n                var parent = e.to;\n                if (e.oldIndex < parent.childNodes.length - 1) {\n                    parent.insertBefore(e.item, parent.childNodes[e.oldIndex > e.newIndex ? e.oldIndex + 1 : e.oldIndex]);\n                }\n                else {\n                    parent.appendChild(e.item);\n                }\n                store.exchange(e.oldIndex, e.newIndex);\n            }\n        });\n    };\n    Cards.prototype.destroyDragging = function () {\n        this.sortable && this.sortable.destroy();\n        this.sortable = undefined;\n    };\n    Cards.prototype.renderActions = function (region) {\n        var _this = this;\n        var _a = this.props, actions = _a.actions, render = _a.render, store = _a.store, cx = _a.classnames;\n        var btn;\n        actions = Array.isArray(actions) ? actions.concat() : [];\n        if (!~this.renderedToolbars.indexOf('check-all') &&\n            (btn = this.renderCheckAll())) {\n            actions.unshift({\n                type: 'button',\n                children: btn\n            });\n        }\n        if (region === 'header' &&\n            !~this.renderedToolbars.indexOf('drag-toggler') &&\n            (btn = this.renderDragToggler())) {\n            actions.unshift({\n                type: 'button',\n                children: btn\n            });\n        }\n        return Array.isArray(actions) && actions.length ? (React.createElement(\"div\", { className: cx('Cards-actions') }, actions.map(function (action, key) {\n            return render(\"action/\".concat(key), __assign({ type: 'button' }, action), {\n                onAction: _this.handleAction,\n                key: key,\n                btnDisabled: store.dragging\n            });\n        }))) : null;\n    };\n    Cards.prototype.renderHeading = function () {\n        var _a = this.props, title = _a.title, store = _a.store, hideQuickSaveBtn = _a.hideQuickSaveBtn, cx = _a.classnames, data = _a.data;\n        if (title || (store.modified && !hideQuickSaveBtn) || store.moved) {\n            return (React.createElement(\"div\", { className: cx('Cards-heading') }, store.modified && !hideQuickSaveBtn ? (React.createElement(\"span\", null, \"\\u5F53\\u524D\\u6709 \".concat(store.modified, \" \\u6761\\u8BB0\\u5F55\\u4FEE\\u6539\\u4E86\\u5185\\u5BB9, \\u4F46\\u5E76\\u6CA1\\u6709\\u63D0\\u4EA4\\u3002\\u8BF7\\u9009\\u62E9:\"),\n                React.createElement(\"button\", { type: \"button\", className: cx('Button Button--xs Button--success m-l-sm'), onClick: this.handleSave },\n                    React.createElement(Icon, { icon: \"check\", className: \"icon m-r-xs\" }),\n                    \"\\u63D0\\u4EA4\"),\n                React.createElement(\"button\", { type: \"button\", className: cx('Button Button--xs Button--danger m-l-sm'), onClick: this.reset },\n                    React.createElement(Icon, { icon: \"close\", className: \"icon m-r-xs\" }),\n                    \"\\u653E\\u5F03\"))) : store.moved ? (React.createElement(\"span\", null, \"\\u5F53\\u524D\\u6709 \".concat(store.moved, \" \\u6761\\u8BB0\\u5F55\\u4FEE\\u6539\\u4E86\\u987A\\u5E8F, \\u4F46\\u5E76\\u6CA1\\u6709\\u63D0\\u4EA4\\u3002\\u8BF7\\u9009\\u62E9:\"),\n                React.createElement(\"button\", { type: \"button\", className: cx('Button Button--xs Button--success m-l-sm'), onClick: this.handleSaveOrder },\n                    React.createElement(Icon, { icon: \"check\", className: \"icon m-r-xs\" }),\n                    \"\\u63D0\\u4EA4\"),\n                React.createElement(\"button\", { type: \"button\", className: cx('Button Button--xs Button--danger m-l-sm'), onClick: this.reset },\n                    React.createElement(Icon, { icon: \"close\", className: \"icon m-r-xs\" }),\n                    \"\\u653E\\u5F03\"))) : title ? (filter(title, data)) : ('')));\n        }\n        return null;\n    };\n    Cards.prototype.renderHeader = function () {\n        var _a = this.props, header = _a.header, headerClassName = _a.headerClassName, headerToolbar = _a.headerToolbar, headerToolbarRender = _a.headerToolbarRender, showHeader = _a.showHeader, render = _a.render, store = _a.store, cx = _a.classnames, __ = _a.translate;\n        if (showHeader === false) {\n            return null;\n        }\n        var child = headerToolbarRender\n            ? headerToolbarRender(__assign(__assign({}, this.props), store.eventContext), this.renderToolbar)\n            : null;\n        var actions = this.renderActions('header');\n        var toolbarNode = actions || child || store.dragging ? (React.createElement(\"div\", { className: cx('Cards-toolbar'), key: \"header-toolbar\" },\n            actions,\n            child,\n            store.dragging ? (React.createElement(\"div\", { className: cx('Cards-dragTip'), ref: this.dragTipRef }, __('Card.dragTip'))) : null)) : null;\n        var headerNode = header ? (React.createElement(\"div\", { className: cx('Cards-header', headerClassName), key: \"header\" }, render('header', header))) : null;\n        return headerNode && toolbarNode\n            ? [headerNode, toolbarNode]\n            : headerNode || toolbarNode || null;\n    };\n    Cards.prototype.renderFooter = function () {\n        var _a = this.props, footer = _a.footer, footerClassName = _a.footerClassName, footerToolbar = _a.footerToolbar, footerToolbarRender = _a.footerToolbarRender, render = _a.render, showFooter = _a.showFooter, store = _a.store, cx = _a.classnames, affixFooter = _a.affixFooter;\n        if (showFooter === false) {\n            return null;\n        }\n        var child = footerToolbarRender\n            ? footerToolbarRender(__assign(__assign({}, this.props), store.eventContext), this.renderToolbar)\n            : null;\n        var actions = this.renderActions('footer');\n        var footerNode = footer ? (React.createElement(\"div\", { className: cx('Cards-footer', footerClassName, affixFooter ? 'Cards-footer--affix' : ''), key: \"footer\" }, render('footer', footer))) : null;\n        var toolbarNode = actions || child ? (React.createElement(\"div\", { className: cx('Cards-toolbar', !footerNode && affixFooter ? 'Cards-footToolbar--affix' : ''), key: \"footer-toolbar\" },\n            actions,\n            child)) : null;\n        return footerNode && toolbarNode\n            ? [toolbarNode, footerNode]\n            : footerNode || toolbarNode || null;\n    };\n    Cards.prototype.renderCheckAll = function () {\n        var _a = this.props, store = _a.store, multiple = _a.multiple, selectable = _a.selectable, checkAll = _a.checkAll;\n        if (!store.selectable ||\n            !multiple ||\n            !selectable ||\n            store.dragging ||\n            !store.items.length ||\n            !checkAll) {\n            return null;\n        }\n        return (React.createElement(Button, { key: \"checkall\", tooltip: \"\\u5207\\u6362\\u5168\\u9009\", onClick: this.handleCheckAll, size: \"sm\", level: store.allChecked ? 'info' : 'default' }, \"\\u5168\\u9009\"));\n    };\n    Cards.prototype.renderDragToggler = function () {\n        var _this = this;\n        var _a = this.props, store = _a.store, multiple = _a.multiple, selectable = _a.selectable, popOverContainer = _a.popOverContainer, env = _a.env, __ = _a.translate, dragIcon = _a.dragIcon;\n        if (!store.draggable || store.items.length < 2) {\n            return null;\n        }\n        return (React.createElement(Button, { iconOnly: true, key: \"dragging-toggle\", tooltip: __('Card.toggleDrag'), tooltipContainer: popOverContainer || (env === null || env === void 0 ? void 0 : env.getModalContainer), size: \"sm\", active: store.dragging, onClick: function (e) {\n                e.preventDefault();\n                store.toggleDragging();\n                store.dragging && store.clear();\n                store.dragging ? _this.initDragging() : undefined;\n            } }, React.isValidElement(dragIcon) ? (dragIcon) : (React.createElement(Icon, { icon: \"exchange\", className: \"icon r90\" }))));\n    };\n    Cards.prototype.renderToolbar = function (toolbar, index) {\n        var type = toolbar.type || toolbar;\n        if (type === 'drag-toggler') {\n            this.renderedToolbars.push(type);\n            return this.renderDragToggler();\n        }\n        else if (type === 'check-all') {\n            this.renderedToolbars.push(type);\n            return this.renderCheckAll();\n        }\n        return;\n    };\n    // editor中重写，请勿更改前两个参数\n    Cards.prototype.renderCard = function (index, card, item, itemClassName, style) {\n        var _this = this;\n        var _a = this.props, render = _a.render, cx = _a.classnames, store = _a.store, multiple = _a.multiple, checkOnItemClick = _a.checkOnItemClick, hideCheckToggler = _a.hideCheckToggler;\n        var cardProps = {\n            className: cx(filterClassNameObject((card && card.className) || '', item.locals), {\n                'is-checked': item.checked,\n                'is-modified': item.modified,\n                'is-moved': item.moved,\n                'is-dragging': store.dragging\n            }),\n            item: item,\n            key: index,\n            itemIndex: item.index,\n            multiple: multiple,\n            selectable: store.selectable,\n            checkable: item.checkable,\n            draggable: item.draggable,\n            selected: item.checked,\n            dragging: store.dragging,\n            data: item.locals,\n            onAction: this.handleAction,\n            onCheck: this.handleCheck,\n            onClick: this.handleClick,\n            onQuickChange: store.dragging ? null : this.handleQuickChange\n        };\n        // card2属性与card有区别\n        if ((card === null || card === void 0 ? void 0 : card.type) === 'card2') {\n            cardProps = __assign(__assign({}, cardProps), { item: item.locals, onCheck: function () {\n                    _this.handleCheck(item);\n                } });\n        }\n        return (React.createElement(\"div\", { key: item.index, className: cx(itemClassName), style: style }, render(\"card/\".concat(index), __assign({ \n            // @ts-ignore\n            type: card.type || 'card', hideCheckToggler: hideCheckToggler, checkOnItemClick: checkOnItemClick }, card), cardProps)));\n    };\n    Cards.prototype.render = function () {\n        var _this = this;\n        var _a = this.props, className = _a.className, style = _a.style, store = _a.store, columnsCount = _a.columnsCount, itemClassName = _a.itemClassName, placeholder = _a.placeholder, card = _a.card, data = _a.data, render = _a.render, affixHeader = _a.affixHeader, masonryLayout = _a.masonryLayout, itemsClassName = _a.itemsClassName, cx = _a.classnames, __ = _a.translate, _b = _a.loading, loading = _b === void 0 ? false : _b, loadingConfig = _a.loadingConfig, env = _a.env, id = _a.id, wrapperCustomStyle = _a.wrapperCustomStyle, themeCss = _a.themeCss, mobileUI = _a.mobileUI;\n        this.renderedToolbars = []; // 用来记录哪些 toolbar 已经渲染了，已经渲染了就不重复渲染了。\n        var itemFinalClassName = (function () {\n            // 移动端且非砖石布局时不使用网格类名\n            if (mobileUI && !masonryLayout) {\n                return '';\n            }\n            // 砖石布局且设置了固定列数时使用计算的网格类名\n            if (masonryLayout && columnsCount) {\n                var colWidth = Math.round(12 / columnsCount);\n                return \"Grid-col--xs\".concat(colWidth, \" Grid-col--sm\").concat(colWidth, \" Grid-col--md\").concat(colWidth, \" Grid-col--lg\").concat(colWidth);\n            }\n            // 其他情况使用配置的类名或空字符串\n            return itemClassName || '';\n        })();\n        var header = this.renderHeader();\n        var heading = this.renderHeading();\n        var footer = this.renderFooter();\n        var masonryClassName = '';\n        if (masonryLayout) {\n            masonryClassName =\n                'Cards--masonry ' +\n                    itemFinalClassName\n                        .split(/\\s/)\n                        .map(function (item) {\n                        if (/^Grid-col--(xs|sm|md|lg)(\\d+)/.test(item)) {\n                            return \"Cards--masonry\".concat(ucFirst(RegExp.$1)).concat(RegExp.$2);\n                        }\n                        return item;\n                    })\n                        .join(' ');\n        }\n        // 自定义行列间距\n        var wrapStyles = {};\n        var itemStyles = {};\n        if ((style === null || style === void 0 ? void 0 : style.gutterX) >= 0) {\n            wrapStyles.marginLeft = wrapStyles.marginRight =\n                -((style === null || style === void 0 ? void 0 : style.gutterX) / 2) + 'px';\n            itemStyles.paddingLeft = itemStyles.paddingRight =\n                (style === null || style === void 0 ? void 0 : style.gutterX) / 2 + 'px';\n        }\n        if ((style === null || style === void 0 ? void 0 : style.gutterY) >= 0) {\n            itemStyles.marginBottom = (style === null || style === void 0 ? void 0 : style.gutterY) + 'px';\n        }\n        return (React.createElement(\"div\", { ref: this.bodyRef, className: cx('Cards', className, {\n                'Cards--unsaved': !!store.modified || !!store.moved\n            }, setThemeClassName(__assign(__assign({}, this.props), { name: 'baseControlClassName', id: id, themeCss: themeCss })), setThemeClassName(__assign(__assign({}, this.props), { name: 'wrapperCustomStyle', id: id, themeCss: wrapperCustomStyle }))), style: buildStyle(style, data) },\n            affixHeader ? (React.createElement(\"div\", { className: cx('Cards-fixedTop') },\n                header,\n                heading)) : (React.createElement(React.Fragment, null,\n                header,\n                heading)),\n            store.items.length ? (React.createElement(\"div\", { className: cx('Cards-body Grid', itemsClassName, masonryClassName), style: wrapStyles }, store.items.map(function (item, index) {\n                return _this.renderCard(index, card, item, itemFinalClassName, itemStyles);\n            }))) : (React.createElement(\"div\", { className: cx('Cards-placeholder') }, render('placeholder', __(placeholder)))),\n            footer,\n            React.createElement(Spinner, { loadingConfig: loadingConfig, overlay: true, show: loading }),\n            React.createElement(CustomStyle, __assign({}, this.props, { config: {\n                    wrapperCustomStyle: wrapperCustomStyle,\n                    id: id,\n                    themeCss: themeCss,\n                    classNames: [\n                        {\n                            key: 'baseControlClassName'\n                        }\n                    ]\n                }, env: env }))));\n    };\n    Cards.propsList = [\n        'header',\n        'headerToolbarRender',\n        'footer',\n        'footerToolbarRender',\n        'placeholder',\n        'source',\n        'selectable',\n        'headerClassName',\n        'footerClassName',\n        'hideQuickSaveBtn',\n        'hideCheckToggler',\n        'itemCheckableOn',\n        'itemDraggableOn',\n        'masonryLayout',\n        'items',\n        'valueField'\n    ];\n    Cards.defaultProps = {\n        className: '',\n        placeholder: 'placeholder.noData',\n        source: '$items',\n        selectable: false,\n        headerClassName: '',\n        footerClassName: '',\n        itemClassName: 'Grid-col--xs12 Grid-col--sm6 Grid-col--md4 Grid-col--lg3',\n        hideCheckToggler: false,\n        masonryLayout: false,\n        affixHeader: true,\n        itemsClassName: '',\n        checkAll: true\n    };\n    return Cards;\n}(React.Component));\nvar CardsRenderer = /** @class */ (function (_super) {\n    __extends(CardsRenderer, _super);\n    function CardsRenderer(props, scoped) {\n        var _this = _super.call(this, props) || this;\n        scoped.registerComponent(_this);\n        return _this;\n    }\n    CardsRenderer.prototype.componentWillUnmount = function () {\n        var _a;\n        (_a = _super.prototype.componentWillUnmount) === null || _a === void 0 ? void 0 : _a.call(this);\n        this.context.unRegisterComponent(this);\n    };\n    CardsRenderer.prototype.receive = function (values, subPath) {\n        var _a, _b, _c, _d;\n        var scoped = this.context;\n        var parents = (_a = scoped === null || scoped === void 0 ? void 0 : scoped.parent) === null || _a === void 0 ? void 0 : _a.getComponents();\n        /**\n         * 因为Cards在scope上注册，导致getComponentByName查询组件时会优先找到Cards，和CRUD联动的动作都会失效\n         * 这里先做兼容处理，把动作交给上层的CRUD处理\n         */\n        if ((_b = this.props) === null || _b === void 0 ? void 0 : _b.host) {\n            // CRUD会把自己透传给Cards，这样可以保证找到CRUD\n            return (_d = (_c = this.props.host).receive) === null || _d === void 0 ? void 0 : _d.call(_c, values, subPath);\n        }\n        if (subPath) {\n            return scoped.send(subPath, values);\n        }\n    };\n    CardsRenderer.prototype.reload = function (subPath, query, ctx, silent, replace, args) {\n        var _a, _b, _c;\n        return __awaiter(this, void 0, void 0, function () {\n            var store, scoped;\n            return __generator(this, function (_d) {\n                store = this.props.store;\n                if ((args === null || args === void 0 ? void 0 : args.index) || (args === null || args === void 0 ? void 0 : args.condition)) {\n                    // 局部刷新\n                    // todo 后续考虑添加局部刷新\n                    // const targets = await getMatchedEventTargets<IItem>(\n                    //   store.items,\n                    //   ctx || this.props.data,\n                    //   args.index,\n                    //   args?.condition\n                    // );\n                    // await Promise.all(targets.map(target => this.loadDeferredRow(target)));\n                    return [2 /*return*/];\n                }\n                scoped = this.context;\n                if ((_a = this.props) === null || _a === void 0 ? void 0 : _a.host) {\n                    // CRUD会把自己透传给Cards，这样可以保证找到CRUD\n                    return [2 /*return*/, (_c = (_b = this.props.host).reload) === null || _c === void 0 ? void 0 : _c.call(_b, subPath, query, ctx)];\n                }\n                if (subPath) {\n                    return [2 /*return*/, scoped.reload(subPath, ctx)];\n                }\n                return [2 /*return*/];\n            });\n        });\n    };\n    CardsRenderer.prototype.setData = function (values, replace, index, condition) {\n        var _a, _b, _c;\n        return __awaiter(this, void 0, void 0, function () {\n            var store, targets;\n            return __generator(this, function (_d) {\n                switch (_d.label) {\n                    case 0:\n                        store = this.props.store;\n                        if (!(index !== undefined || condition !== undefined)) return [3 /*break*/, 2];\n                        return [4 /*yield*/, getMatchedEventTargets(store.items, this.props.data, index, condition)];\n                    case 1:\n                        targets = _d.sent();\n                        targets.forEach(function (target) {\n                            target.updateData(values);\n                        });\n                        return [3 /*break*/, 3];\n                    case 2:\n                        if ((_a = this.props) === null || _a === void 0 ? void 0 : _a.host) {\n                            // 如果在 CRUD 里面，优先让 CRUD 去更新状态\n                            return [2 /*return*/, (_c = (_b = this.props.host).setData) === null || _c === void 0 ? void 0 : _c.call(_b, values, replace, index, condition)];\n                        }\n                        else {\n                            return [2 /*return*/, store.updateData(values, undefined, replace)];\n                        }\n                        _d.label = 3;\n                    case 3: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    CardsRenderer.prototype.getData = function () {\n        var _a = this.props, store = _a.store, data = _a.data;\n        return store.getData(data);\n    };\n    CardsRenderer.prototype.hasModifiedItems = function () {\n        return this.props.store.modified;\n    };\n    CardsRenderer.prototype.doAction = function (action, ctx, throwErrors, args) {\n        return __awaiter(this, void 0, void 0, function () {\n            var _a, store, valueField, data, actionType, _b, rows;\n            return __generator(this, function (_c) {\n                switch (_c.label) {\n                    case 0:\n                        _a = this.props, store = _a.store, valueField = _a.valueField, data = _a.data;\n                        actionType = action === null || action === void 0 ? void 0 : action.actionType;\n                        _b = actionType;\n                        switch (_b) {\n                            case 'selectAll': return [3 /*break*/, 1];\n                            case 'clearAll': return [3 /*break*/, 2];\n                            case 'select': return [3 /*break*/, 3];\n                            case 'initDrag': return [3 /*break*/, 5];\n                            case 'cancelDrag': return [3 /*break*/, 6];\n                            case 'submitQuickEdit': return [3 /*break*/, 7];\n                        }\n                        return [3 /*break*/, 9];\n                    case 1:\n                        store.clear();\n                        store.toggleAll();\n                        this.syncSelected();\n                        return [3 /*break*/, 10];\n                    case 2:\n                        store.clear();\n                        this.syncSelected();\n                        return [3 /*break*/, 10];\n                    case 3: return [4 /*yield*/, getMatchedEventTargets(store.items, ctx || this.props.data, args.index, args.condition, args.selected)];\n                    case 4:\n                        rows = _c.sent();\n                        store.updateSelected(rows.map(function (item) { return item.data; }), valueField);\n                        this.syncSelected();\n                        return [3 /*break*/, 10];\n                    case 5:\n                        store.startDragging();\n                        return [3 /*break*/, 10];\n                    case 6:\n                        store.stopDragging();\n                        return [3 /*break*/, 10];\n                    case 7: return [4 /*yield*/, this.handleSave()];\n                    case 8:\n                        _c.sent();\n                        return [3 /*break*/, 10];\n                    case 9: return [2 /*return*/, this.handleAction(undefined, action, data)];\n                    case 10: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    CardsRenderer.contextType = ScopedContext;\n    CardsRenderer = __decorate([\n        Renderer({\n            name: 'cards',\n            type: 'cards',\n            storeType: ListStore.name,\n            weight: -100 // 默认的 grid 不是这样，这个只识别 crud 下面的 grid\n        }),\n        __metadata(\"design:paramtypes\", [Object, Object])\n    ], CardsRenderer);\n    return CardsRenderer;\n}(Cards));\n\nexport { CardsRenderer, Cards as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,mBAAkB;AAClB,uBAA4B;AAI5B,kBAAiB;AAEjB,IAAI;AAAA;AAAA,EAAuB,SAAU,QAAQ;AACzC,cAAUA,QAAO,MAAM;AACvB,aAASA,OAAM,OAAO;AAClB,UAAI,QAAQ,OAAO,KAAK,MAAM,KAAK,KAAK;AACxC,YAAM,eAAe,MAAM,aAAa,KAAK,KAAK;AAClD,YAAM,cAAc,MAAM,YAAY,KAAK,KAAK;AAChD,YAAM,cAAc,MAAM,YAAY,KAAK,KAAK;AAChD,YAAM,iBAAiB,MAAM,eAAe,KAAK,KAAK;AACtD,YAAM,oBAAoB,MAAM,kBAAkB,KAAK,KAAK;AAC5D,YAAM,aAAa,MAAM,WAAW,KAAK,KAAK;AAC9C,YAAM,kBAAkB,MAAM,gBAAgB,KAAK,KAAK;AACxD,YAAM,QAAQ,MAAM,MAAM,KAAK,KAAK;AACpC,YAAM,aAAa,MAAM,WAAW,KAAK,KAAK;AAC9C,YAAM,UAAU,MAAM,QAAQ,KAAK,KAAK;AACxC,YAAM,gBAAgB,MAAM,cAAc,KAAK,KAAK;AACpD,UAAI,QAAQ,MAAM,OAAO,aAAa,MAAM,YAAY,YAAY,MAAM,WAAW,UAAU,MAAM,SAAS,WAAW,MAAM,UAAU,WAAW,MAAM,UAAU,mBAAmB,MAAM,kBAAkB,kBAAkB,MAAM,iBAAiB,kBAAkB,MAAM;AAChR,YAAM,OAAO;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AACD,MAAAA,OAAM,UAAU,OAAO,MAAM,KAAK,KAAK,MAAM,aAAa;AAC1D,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,YAAY,SAAU,OAAO,OAAO,WAAW;AACjD,UAAI,SAAS,MAAM;AACnB,UAAI,QAAQ,aAAa,OAAO,SAAUC,QAAO;AAAE,eAAOA,OAAM;AAAA,MAAO,CAAC;AACxE,UAAI,QAAQ,CAAC;AACb,UAAI,cAAc;AAClB,UAAI,MAAM,QAAQ,KAAK,MAClB,CAAC,aACE,aAAa,WAAW,SAAUA,QAAO;AAAE,eAAOA,OAAM;AAAA,MAAO,CAAC,MAAM,QAAQ;AAClF,gBAAQ;AACR,sBAAc;AAAA,MAClB,WACS,OAAO,WAAW,UAAU;AACjC,YAAI,WAAW,yBAAyB,QAAQ,MAAM,MAAM,OAAO;AACnE,YAAI,OAAO,YACL,yBAAyB,QAAQ,UAAU,MAAM,OAAO,IACxD;AACN,YAAI,SAAS,UAAU;AACnB,wBAAc;AAAA,QAClB,OACK;AACD,kBAAQ,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAC;AAC9C,wBAAc;AAAA,QAClB;AAAA,MACJ;AACA,qBAAe,MAAM,UAAU,OAAO,MAAM,WAAW,MAAM,QAAQ;AACrE,YAAM,QAAQ,MAAM,QAAQ,KACxB,MAAM,eAAe,MAAM,UAAU,MAAM,UAAU;AACzD,aAAO;AAAA,IACX;AACA,IAAAD,OAAM,UAAU,qBAAqB,SAAU,WAAW;AACtD,UAAI,QAAQ,KAAK;AACjB,UAAI,QAAQ,MAAM;AAClB,UAAI,WAAW;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,GAAG,WAAW,KAAK,GAAG;AAClB,cAAM,OAAO;AAAA,UACT,YAAY,MAAM;AAAA,UAClB,WAAW,MAAM;AAAA,UACjB,SAAS,MAAM;AAAA,UACf,UAAU,MAAM;AAAA,UAChB,UAAU,MAAM;AAAA,UAChB,kBAAkB,MAAM;AAAA,UACxB,iBAAiB,MAAM;AAAA,UACvB,iBAAiB,MAAM;AAAA,QAC3B,CAAC;AAAA,MACL;AACA,UAAI,WAAW,CAAC,UAAU,SAAS,OAAO,GAAG,WAAW,KAAK,KACxD,CAAC,MAAM,SACJ,CAAC,MAAM,UACN,MAAM,SAAS,UAAU,QACrB,OAAO,MAAM,WAAW,YAAY,eAAe,MAAM,MAAM,IAAM;AAC9E,QAAAA,OAAM,UAAU,OAAO,OAAO,SAAS,KAAK,KAAK,aAAa;AAAA,MAClE,WACS,UAAU,aAAa,MAAM,UAAU;AAC5C,cAAM,eAAe,MAAM,YAAY,CAAC,GAAG,MAAM,UAAU;AAAA,MAC/D;AAAA,IACJ;AACA,IAAAA,OAAM,UAAU,UAAU,SAAU,KAAK;AACrC,WAAK,OAAO;AAAA,IAChB;AACA,IAAAA,OAAM,UAAU,eAAe,SAAU,GAAG,QAAQ,KAAK;AACrD,UAAI,WAAW,KAAK,MAAM;AAE1B,aAAO,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,GAAG,QAAQ,GAAG;AAAA,IACtF;AACA,IAAAA,OAAM,UAAU,cAAc,SAAU,MAAM;AAC1C,WAAK,OAAO;AACZ,WAAK,aAAa;AAClB,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,gBAAgB,GAAG;AAC1D;AAAA;AAAA,QAEA;AAAA,QAAkB,aAAa,MAAM,MAAM,SAAS,SAAS,CAAC,GAAG,MAAM,YAAY,GAAG,EAAE,MAAM,KAAK,KAAK,CAAC,CAAC;AAAA,MAAC;AAAA,IAC/G;AACA,IAAAA,OAAM,UAAU,cAAc,SAAU,MAAM;AAC1C,UAAI,KAAK,KAAK,OAAO,gBAAgB,GAAG,eAAe,OAAO,GAAG;AACjE,aAAO;AAAA;AAAA,QAEP;AAAA,QAAY,aAAa,MAAM;AAAA,UAC3B,MAAM,KAAK;AAAA,UACX,OAAO,KAAK;AAAA,QAChB,CAAC;AAAA,MAAC;AAAA,IACN;AACA,IAAAA,OAAM,UAAU,iBAAiB,WAAY;AACzC,UAAI,QAAQ,KAAK,MAAM;AACvB,YAAM,UAAU;AAChB,WAAK,aAAa;AAAA,IACtB;AACA,IAAAA,OAAM,UAAU,kBAAkB,WAAY;AAC1C,UAAI,QAAQ,KAAK,MAAM;AACvB,YAAM,UAAU;AAChB,WAAK,aAAa;AAAA,IACtB;AACA,IAAAA,OAAM,UAAU,iBAAiB,WAAY;AACzC,UAAI,QAAQ,KAAK,MAAM;AACvB,YAAM,SAAS;AACf,WAAK,aAAa;AAAA,IACtB;AACA,IAAAA,OAAM,UAAU,eAAe,WAAY;AACvC,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,WAAW,GAAG,UAAU,gBAAgB,GAAG;AAClF,UAAI,cAAc,MAAM,cAAc,IAAI,SAAU,MAAM;AAAE,eAAO,KAAK;AAAA,MAAM,CAAC;AAC/E,UAAI,gBAAgB,MAAM,gBAAgB,IAAI,SAAU,MAAM;AAAE,eAAO,KAAK;AAAA,MAAM,CAAC;AACnF,oBAAc,YAAY,aAAa,MAAM,MAAM;AAAA,QAC/C;AAAA,QACA;AAAA,MACJ,CAAC,CAAC;AACF,kBAAY,SAAS,aAAa,aAAa;AAAA,IACnD;AACA,IAAAA,OAAM,UAAU,oBAAoB,SAAU,MAAM,QAAQ,iBAAiB,cAAc,SAAS;AAChG,WAAK,OAAO,QAAQ,YAAY;AAChC,UAAI,KAAK,KAAK,OAAO,SAAS,GAAG,QAAQ,eAAe,GAAG,cAAc,eAAe,GAAG;AAC3F,UAAI,cAAc;AACd;AAAA,MACJ;AACA,uBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,KAAK,MAAM,WAAW,KAAK,MAAM,KAAK,UAAU,CAAC,MAAM,YAAY,CAAC,GAAG,KAAK,KAAK;AAC1J,UAAI,mBAAmB,gBAAgB,KAAK;AACxC,aAAK,MAAM,SAAS,MAAM;AAAA,UACtB,YAAY;AAAA,UACZ,KAAK,gBAAgB;AAAA,UACrB,QAAQ,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,QACtE,GAAG,KAAK,MAAM;AACd;AAAA,MACJ;AACA,UAAI,CAAC,mBAAmB,CAAC,QAAQ;AAC7B;AAAA,MACJ;AACA,aAAO,KAAK,MAAM,WAAW,KAAK,MAAM,KAAK,UAAU,CAAC,MAAM,YAAY,CAAC,GAAG,KAAK,OAAO,QAAW,KAAK,UAAU,OAAO;AAAA,IAC/H;AACA,IAAAA,OAAM,UAAU,aAAa,WAAY;AACrC,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,SAAS,GAAG,QAAQ,eAAe,GAAG;AAC7E,UAAI,CAAC,UAAU,CAAC,MAAM,cAAc,QAAQ;AACxC;AAAA,MACJ;AACA,UAAI,QAAQ,MAAM,cAAc,IAAI,SAAU,MAAM;AAAE,eAAO,KAAK;AAAA,MAAM,CAAC;AACzE,UAAI,cAAc,MAAM,cAAc,IAAI,SAAU,MAAM;AAAE,eAAO,KAAK;AAAA,MAAO,CAAC;AAChF,UAAI,OAAO,MAAM,cAAc,IAAI,SAAU,MAAM;AAC/C,eAAO,WAAW,KAAK,MAAM,KAAK,UAAU,CAAC,MAAM,YAAY,CAAC;AAAA,MACpE,CAAC;AACD,UAAI,kBAAkB,MAAM,MACvB,OAAO,SAAU,MAAM;AAAE,eAAO,CAAC,KAAK;AAAA,MAAU,CAAC,EACjD,IAAI,SAAU,MAAM;AAAE,eAAO,KAAK;AAAA,MAAM,CAAC;AAC9C,aAAO,OAAO,MAAM,aAAa,iBAAiB,MAAM,cAAc,IAAI,SAAU,MAAM;AAAE,eAAO,KAAK;AAAA,MAAU,CAAC,CAAC;AAAA,IACxH;AACA,IAAAA,OAAM,UAAU,kBAAkB,WAAY;AAC1C,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,IAAI,OAAO,aAAa,MAAM,eAAe,YAAY;AAC7D,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,mBAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,cAAc,GAAG,aAAa,OAAO,GAAG,MAAM,gBAAgB,GAAG;AACpG,2BAAa,MAAM,WAAW,IAAI,SAAU,MAAM;AAAE,uBAAO,KAAK;AAAA,cAAM,CAAC;AACvE,qBAAO,CAAC,GAAa,cAAc,eAAe,aAAa,MAAM,EAAE,WAAuB,CAAC,CAAC,CAAC;AAAA,YACrG,KAAK;AACD,8BAAgB,GAAG,KAAK;AACxB,kBAAI,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,WAAW;AACvF,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,kBAAI,CAAC,eAAe,CAAC,MAAM,WAAW,QAAQ;AAC1C,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,0BAAY,MAAM,WAAW,IAAI,SAAU,MAAM;AAAE,uBAAO,KAAK;AAAA,cAAM,CAAC,GAAG,MAAM,MAAM,IAAI,SAAU,MAAM;AAAE,uBAAO,KAAK;AAAA,cAAM,CAAC,CAAC;AAC/H,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,OAAM,UAAU,QAAQ,WAAY;AAChC,UAAI,QAAQ,KAAK,MAAM;AACvB,YAAM,MAAM;AAAA,IAChB;AACA,IAAAA,OAAM,UAAU,aAAa,SAAU,OAAO,OAAO;AAIjD,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,eAAe,GAAG;AACzD,UAAI,gBAAgB,MAAM,KAAK;AAC3B,YAAI,QAAQ,MAAM,IAAI,MAAM,GAAG;AAC/B,YAAI,OAAO,MAAM,MAAM,OAAO,SAAU,MAAM;AAC1C,qBAAO,YAAAE,SAAK,OAAO,SAAU,IAAI;AAAE,mBAAO,MAAM,MAAM,KAAK,KAAK,YAAY;AAAA,UAAG,CAAC;AAAA,QACpF,CAAC;AACD,YAAI,aAAa,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG,EAAE,KAAK,OAAU,CAAC;AACjE,aAAK,QAAQ,SAAU,MAAM;AAAE,iBAAO,KAAK,OAAO,UAAU;AAAA,QAAG,CAAC;AAAA,MACpE,WACS,MAAM,QAAQ,KAAK,GAAG;AAC3B,YAAI,OAAO,MAAM,MAAM,OAAO,SAAU,MAAM;AAAE,iBAAO,CAAC,MAAM,QAAQ,KAAK,QAAQ;AAAA,QAAG,CAAC;AACvF,aAAK,QAAQ,SAAU,MAAM;AAAE,iBAAO,KAAK,OAAO,KAAK;AAAA,QAAG,CAAC;AAAA,MAC/D;AAAA,IACJ;AACA,IAAAF,OAAM,UAAU,cAAc,WAAY;AACtC,UAAI,QAAQ,KAAK,MAAM;AACvB,aAAO,MAAM,cAAc,IAAI,SAAU,MAAM;AAAE,eAAO,KAAK;AAAA,MAAM,CAAC;AAAA,IACxE;AACA,IAAAA,OAAM,UAAU,aAAa,SAAU,KAAK;AACxC,UAAI,CAAC,KAAK,WAAW,KAAK;AACtB,aAAK,aAAa;AAAA,MACtB,WACS,KAAK,WAAW,CAAC,KAAK;AAC3B,aAAK,gBAAgB;AAAA,MACzB;AACA,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,OAAM,UAAU,eAAe,WAAY;AACvC,UAAI,KAAK;AACL;AACJ,UAAI,QAAQ,KAAK,MAAM;AACvB,UAAI,UAAM,8BAAY,IAAI;AAC1B,UAAI,KAAK,KAAK,MAAM;AACpB,WAAK,WAAW,IAAI,qBAAS,IAAI,cAAc,IAAI,OAAO,IAAI,YAAY,CAAC,GAAG;AAAA,QAC1E,OAAO;AAAA,QACP,WAAW;AAAA,QACX,QAAQ,IAAI,OAAO,IAAI,cAAc;AAAA,QACrC,YAAY;AAAA,QACZ,OAAO,SAAU,GAAG;AAEhB,cAAI,EAAE,aAAa,EAAE,UAAU;AAC3B;AAAA,UACJ;AACA,cAAI,SAAS,EAAE;AACf,cAAI,EAAE,WAAW,OAAO,WAAW,SAAS,GAAG;AAC3C,mBAAO,aAAa,EAAE,MAAM,OAAO,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,IAAI,EAAE,QAAQ,CAAC;AAAA,UACxG,OACK;AACD,mBAAO,YAAY,EAAE,IAAI;AAAA,UAC7B;AACA,gBAAM,SAAS,EAAE,UAAU,EAAE,QAAQ;AAAA,QACzC;AAAA,MACJ,CAAC;AAAA,IACL;AACA,IAAAA,OAAM,UAAU,kBAAkB,WAAY;AAC1C,WAAK,YAAY,KAAK,SAAS,QAAQ;AACvC,WAAK,WAAW;AAAA,IACpB;AACA,IAAAA,OAAM,UAAU,gBAAgB,SAAU,QAAQ;AAC9C,UAAI,QAAQ;AACZ,UAAI,KAAK,KAAK,OAAO,UAAU,GAAG,SAAS,SAAS,GAAG,QAAQ,QAAQ,GAAG,OAAO,KAAK,GAAG;AACzF,UAAI;AACJ,gBAAU,MAAM,QAAQ,OAAO,IAAI,QAAQ,OAAO,IAAI,CAAC;AACvD,UAAI,CAAC,CAAC,KAAK,iBAAiB,QAAQ,WAAW,MAC1C,MAAM,KAAK,eAAe,IAAI;AAC/B,gBAAQ,QAAQ;AAAA,UACZ,MAAM;AAAA,UACN,UAAU;AAAA,QACd,CAAC;AAAA,MACL;AACA,UAAI,WAAW,YACX,CAAC,CAAC,KAAK,iBAAiB,QAAQ,cAAc,MAC7C,MAAM,KAAK,kBAAkB,IAAI;AAClC,gBAAQ,QAAQ;AAAA,UACZ,MAAM;AAAA,UACN,UAAU;AAAA,QACd,CAAC;AAAA,MACL;AACA,aAAO,MAAM,QAAQ,OAAO,KAAK,QAAQ,SAAU,aAAAG,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,eAAe,EAAE,GAAG,QAAQ,IAAI,SAAU,QAAQ,KAAK;AACjJ,eAAO,OAAO,UAAU,OAAO,GAAG,GAAG,SAAS,EAAE,MAAM,SAAS,GAAG,MAAM,GAAG;AAAA,UACvE,UAAU,MAAM;AAAA,UAChB;AAAA,UACA,aAAa,MAAM;AAAA,QACvB,CAAC;AAAA,MACL,CAAC,CAAC,IAAK;AAAA,IACX;AACA,IAAAH,OAAM,UAAU,gBAAgB,WAAY;AACxC,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,QAAQ,GAAG,OAAO,mBAAmB,GAAG,kBAAkB,KAAK,GAAG,YAAY,OAAO,GAAG;AAC/H,UAAI,SAAU,MAAM,YAAY,CAAC,oBAAqB,MAAM,OAAO;AAC/D,eAAQ,aAAAG,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,eAAe,EAAE,GAAG,MAAM,YAAY,CAAC,mBAAoB,aAAAA,QAAM;AAAA,UAAc;AAAA,UAAQ;AAAA,UAAM,OAAsB,OAAO,MAAM,UAAU,wBAAkH;AAAA,UAC3S,aAAAA,QAAM;AAAA,YAAc;AAAA,YAAU,EAAE,MAAM,UAAU,WAAW,GAAG,0CAA0C,GAAG,SAAS,KAAK,WAAW;AAAA,YAChI,aAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,SAAS,WAAW,cAAc,CAAC;AAAA,YACrE;AAAA,UAAc;AAAA,UAClB,aAAAA,QAAM;AAAA,YAAc;AAAA,YAAU,EAAE,MAAM,UAAU,WAAW,GAAG,yCAAyC,GAAG,SAAS,KAAK,MAAM;AAAA,YAC1H,aAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,SAAS,WAAW,cAAc,CAAC;AAAA,YACrE;AAAA,UAAc;AAAA,QAAC,IAAK,MAAM,QAAS,aAAAA,QAAM;AAAA,UAAc;AAAA,UAAQ;AAAA,UAAM,OAAsB,OAAO,MAAM,OAAO,wBAAkH;AAAA,UACrO,aAAAA,QAAM;AAAA,YAAc;AAAA,YAAU,EAAE,MAAM,UAAU,WAAW,GAAG,0CAA0C,GAAG,SAAS,KAAK,gBAAgB;AAAA,YACrI,aAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,SAAS,WAAW,cAAc,CAAC;AAAA,YACrE;AAAA,UAAc;AAAA,UAClB,aAAAA,QAAM;AAAA,YAAc;AAAA,YAAU,EAAE,MAAM,UAAU,WAAW,GAAG,yCAAyC,GAAG,SAAS,KAAK,MAAM;AAAA,YAC1H,aAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,SAAS,WAAW,cAAc,CAAC;AAAA,YACrE;AAAA,UAAc;AAAA,QAAC,IAAK,QAAS,OAAO,OAAO,IAAI,IAAM,EAAG;AAAA,MACpE;AACA,aAAO;AAAA,IACX;AACA,IAAAH,OAAM,UAAU,eAAe,WAAY;AACvC,UAAI,KAAK,KAAK,OAAO,SAAS,GAAG,QAAQ,kBAAkB,GAAG,iBAAiB,gBAAgB,GAAG,eAAe,sBAAsB,GAAG,qBAAqB,aAAa,GAAG,YAAY,SAAS,GAAG,QAAQ,QAAQ,GAAG,OAAO,KAAK,GAAG,YAAY,KAAK,GAAG;AAC7P,UAAI,eAAe,OAAO;AACtB,eAAO;AAAA,MACX;AACA,UAAI,QAAQ,sBACN,oBAAoB,SAAS,SAAS,CAAC,GAAG,KAAK,KAAK,GAAG,MAAM,YAAY,GAAG,KAAK,aAAa,IAC9F;AACN,UAAI,UAAU,KAAK,cAAc,QAAQ;AACzC,UAAI,cAAc,WAAW,SAAS,MAAM,WAAY,aAAAG,QAAM;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,GAAG,eAAe,GAAG,KAAK,iBAAiB;AAAA,QACvI;AAAA,QACA;AAAA,QACA,MAAM,WAAY,aAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,eAAe,GAAG,KAAK,KAAK,WAAW,GAAG,GAAG,cAAc,CAAC,IAAK;AAAA,MAAI,IAAK;AAC3I,UAAI,aAAa,SAAU,aAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,gBAAgB,eAAe,GAAG,KAAK,SAAS,GAAG,OAAO,UAAU,MAAM,CAAC,IAAK;AACtJ,aAAO,cAAc,cACf,CAAC,YAAY,WAAW,IACxB,cAAc,eAAe;AAAA,IACvC;AACA,IAAAH,OAAM,UAAU,eAAe,WAAY;AACvC,UAAI,KAAK,KAAK,OAAO,SAAS,GAAG,QAAQ,kBAAkB,GAAG,iBAAiB,gBAAgB,GAAG,eAAe,sBAAsB,GAAG,qBAAqB,SAAS,GAAG,QAAQ,aAAa,GAAG,YAAY,QAAQ,GAAG,OAAO,KAAK,GAAG,YAAY,cAAc,GAAG;AACtQ,UAAI,eAAe,OAAO;AACtB,eAAO;AAAA,MACX;AACA,UAAI,QAAQ,sBACN,oBAAoB,SAAS,SAAS,CAAC,GAAG,KAAK,KAAK,GAAG,MAAM,YAAY,GAAG,KAAK,aAAa,IAC9F;AACN,UAAI,UAAU,KAAK,cAAc,QAAQ;AACzC,UAAI,aAAa,SAAU,aAAAG,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,gBAAgB,iBAAiB,cAAc,wBAAwB,EAAE,GAAG,KAAK,SAAS,GAAG,OAAO,UAAU,MAAM,CAAC,IAAK;AAChM,UAAI,cAAc,WAAW,QAAS,aAAAA,QAAM;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,GAAG,iBAAiB,CAAC,cAAc,cAAc,6BAA6B,EAAE,GAAG,KAAK,iBAAiB;AAAA,QACnL;AAAA,QACA;AAAA,MAAK,IAAK;AACd,aAAO,cAAc,cACf,CAAC,aAAa,UAAU,IACxB,cAAc,eAAe;AAAA,IACvC;AACA,IAAAH,OAAM,UAAU,iBAAiB,WAAY;AACzC,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,WAAW,GAAG,UAAU,aAAa,GAAG,YAAY,WAAW,GAAG;AACzG,UAAI,CAAC,MAAM,cACP,CAAC,YACD,CAAC,cACD,MAAM,YACN,CAAC,MAAM,MAAM,UACb,CAAC,UAAU;AACX,eAAO;AAAA,MACX;AACA,aAAQ,aAAAG,QAAM,cAAc,UAAQ,EAAE,KAAK,YAAY,SAAS,QAA4B,SAAS,KAAK,gBAAgB,MAAM,MAAM,OAAO,MAAM,aAAa,SAAS,UAAU,GAAG,IAAc;AAAA,IACxM;AACA,IAAAH,OAAM,UAAU,oBAAoB,WAAY;AAC5C,UAAI,QAAQ;AACZ,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,WAAW,GAAG,UAAU,aAAa,GAAG,YAAY,mBAAmB,GAAG,kBAAkB,MAAM,GAAG,KAAK,KAAK,GAAG,WAAW,WAAW,GAAG;AAClL,UAAI,CAAC,MAAM,aAAa,MAAM,MAAM,SAAS,GAAG;AAC5C,eAAO;AAAA,MACX;AACA,aAAQ,aAAAG,QAAM,cAAc,UAAQ,EAAE,UAAU,MAAM,KAAK,mBAAmB,SAAS,GAAG,iBAAiB,GAAG,kBAAkB,qBAAqB,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,oBAAoB,MAAM,MAAM,QAAQ,MAAM,UAAU,SAAS,SAAU,GAAG;AACzQ,UAAE,eAAe;AACjB,cAAM,eAAe;AACrB,cAAM,YAAY,MAAM,MAAM;AAC9B,cAAM,WAAW,MAAM,aAAa,IAAI;AAAA,MAC5C,EAAE,GAAG,aAAAA,QAAM,eAAe,QAAQ,IAAK,WAAa,aAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,YAAY,WAAW,WAAW,CAAC,CAAE;AAAA,IACnI;AACA,IAAAH,OAAM,UAAU,gBAAgB,SAAU,SAAS,OAAO;AACtD,UAAI,OAAO,QAAQ,QAAQ;AAC3B,UAAI,SAAS,gBAAgB;AACzB,aAAK,iBAAiB,KAAK,IAAI;AAC/B,eAAO,KAAK,kBAAkB;AAAA,MAClC,WACS,SAAS,aAAa;AAC3B,aAAK,iBAAiB,KAAK,IAAI;AAC/B,eAAO,KAAK,eAAe;AAAA,MAC/B;AACA;AAAA,IACJ;AAEA,IAAAA,OAAM,UAAU,aAAa,SAAU,OAAO,MAAM,MAAM,eAAe,OAAO;AAC5E,UAAI,QAAQ;AACZ,UAAI,KAAK,KAAK,OAAO,SAAS,GAAG,QAAQ,KAAK,GAAG,YAAY,QAAQ,GAAG,OAAO,WAAW,GAAG,UAAU,mBAAmB,GAAG,kBAAkB,mBAAmB,GAAG;AACrK,UAAI,YAAY;AAAA,QACZ,WAAW,GAAG,sBAAuB,QAAQ,KAAK,aAAc,IAAI,KAAK,MAAM,GAAG;AAAA,UAC9E,cAAc,KAAK;AAAA,UACnB,eAAe,KAAK;AAAA,UACpB,YAAY,KAAK;AAAA,UACjB,eAAe,MAAM;AAAA,QACzB,CAAC;AAAA,QACD;AAAA,QACA,KAAK;AAAA,QACL,WAAW,KAAK;AAAA,QAChB;AAAA,QACA,YAAY,MAAM;AAAA,QAClB,WAAW,KAAK;AAAA,QAChB,WAAW,KAAK;AAAA,QAChB,UAAU,KAAK;AAAA,QACf,UAAU,MAAM;AAAA,QAChB,MAAM,KAAK;AAAA,QACX,UAAU,KAAK;AAAA,QACf,SAAS,KAAK;AAAA,QACd,SAAS,KAAK;AAAA,QACd,eAAe,MAAM,WAAW,OAAO,KAAK;AAAA,MAChD;AAEA,WAAK,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,UAAU,SAAS;AACrE,oBAAY,SAAS,SAAS,CAAC,GAAG,SAAS,GAAG,EAAE,MAAM,KAAK,QAAQ,SAAS,WAAY;AAChF,gBAAM,YAAY,IAAI;AAAA,QAC1B,EAAE,CAAC;AAAA,MACX;AACA,aAAQ,aAAAG,QAAM,cAAc,OAAO,EAAE,KAAK,KAAK,OAAO,WAAW,GAAG,aAAa,GAAG,MAAa,GAAG,OAAO,QAAQ,OAAO,KAAK,GAAG,SAAS;AAAA;AAAA,QAEvI,MAAM,KAAK,QAAQ;AAAA,QAAQ;AAAA,QAAoC;AAAA,MAAmC,GAAG,IAAI,GAAG,SAAS,CAAC;AAAA,IAC9H;AACA,IAAAH,OAAM,UAAU,SAAS,WAAY;AACjC,UAAI,QAAQ;AACZ,UAAI,KAAK,KAAK,OAAO,YAAY,GAAG,WAAW,QAAQ,GAAG,OAAO,QAAQ,GAAG,OAAO,eAAe,GAAG,cAAc,gBAAgB,GAAG,eAAe,cAAc,GAAG,aAAa,OAAO,GAAG,MAAM,OAAO,GAAG,MAAM,SAAS,GAAG,QAAQ,cAAc,GAAG,aAAa,gBAAgB,GAAG,eAAe,iBAAiB,GAAG,gBAAgB,KAAK,GAAG,YAAY,KAAK,GAAG,WAAW,KAAK,GAAG,SAAS,UAAU,OAAO,SAAS,QAAQ,IAAI,gBAAgB,GAAG,eAAe,MAAM,GAAG,KAAK,KAAK,GAAG,IAAI,qBAAqB,GAAG,oBAAoB,WAAW,GAAG,UAAU,WAAW,GAAG;AACvjB,WAAK,mBAAmB,CAAC;AACzB,UAAI,qBAAsB,WAAY;AAElC,YAAI,YAAY,CAAC,eAAe;AAC5B,iBAAO;AAAA,QACX;AAEA,YAAI,iBAAiB,cAAc;AAC/B,cAAI,WAAW,KAAK,MAAM,KAAK,YAAY;AAC3C,iBAAO,eAAe,OAAO,UAAU,eAAe,EAAE,OAAO,UAAU,eAAe,EAAE,OAAO,UAAU,eAAe,EAAE,OAAO,QAAQ;AAAA,QAC/I;AAEA,eAAO,iBAAiB;AAAA,MAC5B,EAAG;AACH,UAAI,SAAS,KAAK,aAAa;AAC/B,UAAI,UAAU,KAAK,cAAc;AACjC,UAAI,SAAS,KAAK,aAAa;AAC/B,UAAI,mBAAmB;AACvB,UAAI,eAAe;AACf,2BACI,oBACI,mBACK,MAAM,IAAI,EACV,IAAI,SAAU,MAAM;AACrB,cAAI,gCAAgC,KAAK,IAAI,GAAG;AAC5C,mBAAO,iBAAiB,OAAO,QAAQ,OAAO,EAAE,CAAC,EAAE,OAAO,OAAO,EAAE;AAAA,UACvE;AACA,iBAAO;AAAA,QACX,CAAC,EACI,KAAK,GAAG;AAAA,MACzB;AAEA,UAAI,aAAa,CAAC;AAClB,UAAI,aAAa,CAAC;AAClB,WAAK,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,YAAY,GAAG;AACpE,mBAAW,aAAa,WAAW,cAC/B,GAAG,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,WAAW,KAAK;AAC3E,mBAAW,cAAc,WAAW,gBAC/B,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,WAAW,IAAI;AAAA,MAC5E;AACA,WAAK,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,YAAY,GAAG;AACpE,mBAAW,gBAAgB,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,WAAW;AAAA,MAC9F;AACA,aAAQ,aAAAG,QAAM;AAAA,QAAc;AAAA,QAAO,EAAE,KAAK,KAAK,SAAS,WAAW,GAAG,SAAS,WAAW;AAAA,UAClF,kBAAkB,CAAC,CAAC,MAAM,YAAY,CAAC,CAAC,MAAM;AAAA,QAClD,GAAG,kBAAkB,SAAS,SAAS,CAAC,GAAG,KAAK,KAAK,GAAG,EAAE,MAAM,wBAAwB,IAAQ,SAAmB,CAAC,CAAC,GAAG,kBAAkB,SAAS,SAAS,CAAC,GAAG,KAAK,KAAK,GAAG,EAAE,MAAM,sBAAsB,IAAQ,UAAU,mBAAmB,CAAC,CAAC,CAAC,GAAG,OAAO,WAAW,OAAO,IAAI,EAAE;AAAA,QACrR,cAAe,aAAAA,QAAM;AAAA,UAAc;AAAA,UAAO,EAAE,WAAW,GAAG,gBAAgB,EAAE;AAAA,UACxE;AAAA,UACA;AAAA,QAAO,IAAM,aAAAA,QAAM;AAAA,UAAc,aAAAA,QAAM;AAAA,UAAU;AAAA,UACjD;AAAA,UACA;AAAA,QAAO;AAAA,QACX,MAAM,MAAM,SAAU,aAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,mBAAmB,gBAAgB,gBAAgB,GAAG,OAAO,WAAW,GAAG,MAAM,MAAM,IAAI,SAAU,MAAM,OAAO;AAC/K,iBAAO,MAAM,WAAW,OAAO,MAAM,MAAM,oBAAoB,UAAU;AAAA,QAC7E,CAAC,CAAC,IAAM,aAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,mBAAmB,EAAE,GAAG,OAAO,eAAe,GAAG,WAAW,CAAC,CAAC;AAAA,QACjH;AAAA,QACA,aAAAA,QAAM,cAAc,WAAS,EAAE,eAA8B,SAAS,MAAM,MAAM,QAAQ,CAAC;AAAA,QAC3F,aAAAA,QAAM,cAAc,aAAa,SAAS,CAAC,GAAG,KAAK,OAAO,EAAE,QAAQ;AAAA,UAC5D;AAAA,UACA;AAAA,UACA;AAAA,UACA,YAAY;AAAA,YACR;AAAA,cACI,KAAK;AAAA,YACT;AAAA,UACJ;AAAA,QACJ,GAAG,IAAS,CAAC,CAAC;AAAA,MAAC;AAAA,IAC3B;AACA,IAAAH,OAAM,YAAY;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,IAAAA,OAAM,eAAe;AAAA,MACjB,WAAW;AAAA,MACX,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,eAAe;AAAA,MACf,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,UAAU;AAAA,IACd;AACA,WAAOA;AAAA,EACX,EAAE,aAAAG,QAAM,SAAS;AAAA;AACjB,IAAI;AAAA;AAAA,EAA+B,SAAU,QAAQ;AACjD,cAAUC,gBAAe,MAAM;AAC/B,aAASA,eAAc,OAAO,QAAQ;AAClC,UAAI,QAAQ,OAAO,KAAK,MAAM,KAAK,KAAK;AACxC,aAAO,kBAAkB,KAAK;AAC9B,aAAO;AAAA,IACX;AACA,IAAAA,eAAc,UAAU,uBAAuB,WAAY;AACvD,UAAI;AACJ,OAAC,KAAK,OAAO,UAAU,0BAA0B,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI;AAC9F,WAAK,QAAQ,oBAAoB,IAAI;AAAA,IACzC;AACA,IAAAA,eAAc,UAAU,UAAU,SAAU,QAAQ,SAAS;AACzD,UAAI,IAAI,IAAI,IAAI;AAChB,UAAI,SAAS,KAAK;AAClB,UAAI,WAAW,KAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc;AAKzI,WAAK,KAAK,KAAK,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAEhE,gBAAQ,MAAM,KAAK,KAAK,MAAM,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,QAAQ,OAAO;AAAA,MACjH;AACA,UAAI,SAAS;AACT,eAAO,OAAO,KAAK,SAAS,MAAM;AAAA,MACtC;AAAA,IACJ;AACA,IAAAA,eAAc,UAAU,SAAS,SAAU,SAAS,OAAO,KAAK,QAAQ,SAAS,MAAM;AACnF,UAAI,IAAI,IAAI;AACZ,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,OAAO;AACX,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,KAAK,MAAM;AACnB,eAAK,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,WAAW,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,YAAY;AAU1H,mBAAO;AAAA,cAAC;AAAA;AAAA,YAAY;AAAA,UACxB;AACA,mBAAS,KAAK;AACd,eAAK,KAAK,KAAK,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAEhE,mBAAO,CAAC,IAAe,MAAM,KAAK,KAAK,MAAM,MAAM,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,SAAS,OAAO,GAAG,CAAC;AAAA,UACpI;AACA,cAAI,SAAS;AACT,mBAAO,CAAC,GAAc,OAAO,OAAO,SAAS,GAAG,CAAC;AAAA,UACrD;AACA,iBAAO;AAAA,YAAC;AAAA;AAAA,UAAY;AAAA,QACxB,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,eAAc,UAAU,UAAU,SAAU,QAAQ,SAAS,OAAO,WAAW;AAC3E,UAAI,IAAI,IAAI;AACZ,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,OAAO;AACX,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,sBAAQ,KAAK,MAAM;AACnB,kBAAI,EAAE,UAAU,UAAa,cAAc;AAAY,uBAAO,CAAC,GAAa,CAAC;AAC7E,qBAAO,CAAC,GAAa,uBAAuB,MAAM,OAAO,KAAK,MAAM,MAAM,OAAO,SAAS,CAAC;AAAA,YAC/F,KAAK;AACD,wBAAU,GAAG,KAAK;AAClB,sBAAQ,QAAQ,SAAU,QAAQ;AAC9B,uBAAO,WAAW,MAAM;AAAA,cAC5B,CAAC;AACD,qBAAO,CAAC,GAAa,CAAC;AAAA,YAC1B,KAAK;AACD,mBAAK,KAAK,KAAK,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAEhE,uBAAO,CAAC,IAAe,MAAM,KAAK,KAAK,MAAM,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,QAAQ,SAAS,OAAO,SAAS,CAAC;AAAA,cACnJ,OACK;AACD,uBAAO,CAAC,GAAc,MAAM,WAAW,QAAQ,QAAW,OAAO,CAAC;AAAA,cACtE;AACA,iBAAG,QAAQ;AAAA,YACf,KAAK;AAAG,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAChC;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,eAAc,UAAU,UAAU,WAAY;AAC1C,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,OAAO,GAAG;AACjD,aAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AACA,IAAAA,eAAc,UAAU,mBAAmB,WAAY;AACnD,aAAO,KAAK,MAAM,MAAM;AAAA,IAC5B;AACA,IAAAA,eAAc,UAAU,WAAW,SAAU,QAAQ,KAAK,aAAa,MAAM;AACzE,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,IAAI,OAAO,YAAY,MAAM,YAAY,IAAI;AACjD,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,mBAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,aAAa,GAAG,YAAY,OAAO,GAAG;AACzE,2BAAa,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO;AACpE,mBAAK;AACL,sBAAQ,IAAI;AAAA,gBACR,KAAK;AAAa,yBAAO,CAAC,GAAa,CAAC;AAAA,gBACxC,KAAK;AAAY,yBAAO,CAAC,GAAa,CAAC;AAAA,gBACvC,KAAK;AAAU,yBAAO,CAAC,GAAa,CAAC;AAAA,gBACrC,KAAK;AAAY,yBAAO,CAAC,GAAa,CAAC;AAAA,gBACvC,KAAK;AAAc,yBAAO,CAAC,GAAa,CAAC;AAAA,gBACzC,KAAK;AAAmB,yBAAO,CAAC,GAAa,CAAC;AAAA,cAClD;AACA,qBAAO,CAAC,GAAa,CAAC;AAAA,YAC1B,KAAK;AACD,oBAAM,MAAM;AACZ,oBAAM,UAAU;AAChB,mBAAK,aAAa;AAClB,qBAAO,CAAC,GAAa,EAAE;AAAA,YAC3B,KAAK;AACD,oBAAM,MAAM;AACZ,mBAAK,aAAa;AAClB,qBAAO,CAAC,GAAa,EAAE;AAAA,YAC3B,KAAK;AAAG,qBAAO,CAAC,GAAa,uBAAuB,MAAM,OAAO,OAAO,KAAK,MAAM,MAAM,KAAK,OAAO,KAAK,WAAW,KAAK,QAAQ,CAAC;AAAA,YACnI,KAAK;AACD,qBAAO,GAAG,KAAK;AACf,oBAAM,eAAe,KAAK,IAAI,SAAU,MAAM;AAAE,uBAAO,KAAK;AAAA,cAAM,CAAC,GAAG,UAAU;AAChF,mBAAK,aAAa;AAClB,qBAAO,CAAC,GAAa,EAAE;AAAA,YAC3B,KAAK;AACD,oBAAM,cAAc;AACpB,qBAAO,CAAC,GAAa,EAAE;AAAA,YAC3B,KAAK;AACD,oBAAM,aAAa;AACnB,qBAAO,CAAC,GAAa,EAAE;AAAA,YAC3B,KAAK;AAAG,qBAAO,CAAC,GAAa,KAAK,WAAW,CAAC;AAAA,YAC9C,KAAK;AACD,iBAAG,KAAK;AACR,qBAAO,CAAC,GAAa,EAAE;AAAA,YAC3B,KAAK;AAAG,qBAAO,CAAC,GAAc,KAAK,aAAa,QAAW,QAAQ,IAAI,CAAC;AAAA,YACxE,KAAK;AAAI,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UACjC;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,eAAc,cAAc;AAC5B,IAAAA,iBAAgB,WAAW;AAAA,MACvB,SAAS;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,QACN,WAAW,UAAU;AAAA,QACrB,QAAQ;AAAA;AAAA,MACZ,CAAC;AAAA,MACD,WAAW,qBAAqB,CAAC,QAAQ,MAAM,CAAC;AAAA,IACpD,GAAGA,cAAa;AAChB,WAAOA;AAAA,EACX,EAAE,KAAK;AAAA;", "names": ["Cards", "props", "find", "React", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}