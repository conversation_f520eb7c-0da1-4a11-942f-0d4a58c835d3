import {
  Avatar$1,
  withBadge
} from "./chunk-GFI6XVUE.js";
import "./chunk-ZRTRFIWU.js";
import "./chunk-6C4OBBQZ.js";
import "./chunk-M5OFQAQB.js";
import "./chunk-YPPVVTGH.js";
import {
  __decorate,
  __extends,
  __metadata
} from "./chunk-F25BIIHK.js";
import {
  Renderer,
  autobind,
  isPureVariable,
  resolveVariableAndFilter
} from "./chunk-LZQZ2OHM.js";
import "./chunk-QMS2VRR4.js";
import "./chunk-5QW7M2DY.js";
import "./chunk-ZJNJ4ZV3.js";
import "./chunk-KEABM62F.js";
import "./chunk-CNJUQDSN.js";
import "./chunk-YZFIV7S4.js";
import "./chunk-7XBFYOTW.js";
import {
  require_react
} from "./chunk-ZUWHLQVA.js";
import "./chunk-KNGT5NUO.js";
import "./chunk-X3JSFYRQ.js";
import {
  __toESM
} from "./chunk-GFT2G5UO.js";

// node_modules/amis/esm/renderers/Avatar.js
var import_react = __toESM(require_react());
var AvatarField = (
  /** @class */
  function(_super) {
    __extends(AvatarField2, _super);
    function AvatarField2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    AvatarField2.prototype.handleClick = function(e) {
      var _a = this.props, dispatchEvent = _a.dispatchEvent, data = _a.data;
      dispatchEvent(e, data);
    };
    AvatarField2.prototype.handleMouseEnter = function(e) {
      var _a = this.props, dispatchEvent = _a.dispatchEvent, data = _a.data;
      dispatchEvent(e, data);
    };
    AvatarField2.prototype.handleMouseLeave = function(e) {
      var _a = this.props, dispatchEvent = _a.dispatchEvent, data = _a.data;
      dispatchEvent(e, data);
    };
    AvatarField2.prototype.render = function() {
      var _a = this.props, _b = _a.style, style = _b === void 0 ? {} : _b, className = _a.className, cx = _a.classnames, src = _a.src, defaultAvatar = _a.defaultAvatar, _c = _a.icon, icon = _c === void 0 ? "fa fa-user" : _c, fit = _a.fit, shape = _a.shape, size = _a.size, text = _a.text, gap = _a.gap, alt = _a.alt, draggable = _a.draggable, crossOrigin = _a.crossOrigin, onError = _a.onError, data = _a.data;
      var errHandler = function() {
        return false;
      };
      if (typeof onError === "string") {
        try {
          errHandler = new Function("event", onError);
        } catch (e) {
          console.warn(onError, e);
        }
      }
      if (isPureVariable(src)) {
        src = resolveVariableAndFilter(src, data, "| raw");
      }
      if (isPureVariable(text)) {
        text = resolveVariableAndFilter(text, data);
      }
      if (isPureVariable(icon)) {
        icon = resolveVariableAndFilter(icon, data);
      }
      return import_react.default.createElement(Avatar$1, { style, className, classnames: cx, src: src || defaultAvatar, icon, fit, shape, size, text, gap, alt, draggable, crossOrigin, onError: errHandler, onClick: this.handleClick, onMouseEnter: this.handleMouseEnter, onMouseLeave: this.handleMouseLeave });
    };
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], AvatarField2.prototype, "handleClick", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], AvatarField2.prototype, "handleMouseEnter", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], AvatarField2.prototype, "handleMouseLeave", null);
    return AvatarField2;
  }(import_react.default.Component)
);
var AvatarFieldRenderer = (
  /** @class */
  function(_super) {
    __extends(AvatarFieldRenderer2, _super);
    function AvatarFieldRenderer2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    AvatarFieldRenderer2 = __decorate([
      Renderer({
        type: "avatar"
      }),
      withBadge
    ], AvatarFieldRenderer2);
    return AvatarFieldRenderer2;
  }(AvatarField)
);
export {
  AvatarField,
  AvatarFieldRenderer
};
//# sourceMappingURL=Avatar-K3VDFZX6.js.map
