{"version": 3, "sources": ["../../@wangeditor/editor-for-vue/src/utils/create-info.ts", "../../@wangeditor/editor-for-vue/plugin-vue:export-helper", "../../@wangeditor/editor-for-vue/src/components/Editor.vue", "../../@wangeditor/editor-for-vue/src/components/Toolbar.vue"], "sourcesContent": ["/**\n * @description 错误提示信息\n * <AUTHOR>\n */\nexport function genErrorInfo(fnName: string): string {\n  let info = `请使用 '@${fnName}' 事件，不要放在 props 中`\n  info += `\\nPlease use '@${fnName}' event instead of props`\n  return info\n}", "\nexport default (sfc, props) => {\n  for (const [key, val] of props) {\n    sfc[key] = val\n  }\n  return sfc\n}\n", "<template>\n  <div ref=\"box\" style=\"height: 100%\"></div>\n</template>\n\n<script lang=\"ts\">\nimport { onMounted, defineComponent, ref, PropType, toRaw, watch, shallowRef } from 'vue'\nimport { createEditor, IEditorConfig, SlateDescendant, IDomEditor } from '@wangeditor/editor'\nimport { genErrorInfo } from '../utils/create-info'\n\nexport default defineComponent({\n  props: {\n    /** 编辑器模式 */\n    mode: {\n      type: String,\n      default: 'default',\n    },\n    /** 编辑器默认内容 */\n    defaultContent: {\n      type: Array as PropType<SlateDescendant[]>,\n      default: [],\n    },\n    defaultHtml: {\n      type: String,\n      default: '',\n    },\n    /** 编辑器默认配置 */\n    defaultConfig: {\n      type: Object as PropType<Partial<IEditorConfig>>,\n      default: {},\n    },\n    /* 自定义 v-model */\n    modelValue: {\n      type: String,\n      default: '',\n    },\n  },\n  setup(props, context) {\n    const box = ref(null) // 编辑器容器\n\n    const editorRef = shallowRef<null | IDomEditor>(null) // editor 实例，必须用 shallowRef\n\n    const curValue = ref('') // 记录 editor 当前 html 内容\n\n    /**\n     * 初始化编辑器\n     */\n    const initEditor = () => {\n      if (!box.value) return\n      // 获取原始数据，解除响应式特性\n      const defaultContent = toRaw(props.defaultContent)\n\n      createEditor({\n        selector: box.value! as Element,\n        mode: props.mode,\n        content: defaultContent || [],\n        html: props.defaultHtml || props.modelValue || '',\n        config: {\n          ...props.defaultConfig,\n          onCreated(editor) {\n            editorRef.value = editor // 记录 editor 实例\n\n            context.emit('onCreated', editor)\n\n            if (props.defaultConfig.onCreated) {\n              const info = genErrorInfo('onCreated')\n              throw new Error(info)\n            }\n          },\n          onChange(editor) {\n            const editorHtml = editor.getHtml()\n            curValue.value = editorHtml // 记录当前内容\n            context.emit('update:modelValue', editorHtml) // 触发 v-model 值变化\n\n            context.emit('onChange', editor)\n            if (props.defaultConfig.onChange) {\n              const info = genErrorInfo('onChange')\n              throw new Error(info)\n            }\n          },\n          onDestroyed(editor) {\n            context.emit('onDestroyed', editor)\n            if (props.defaultConfig.onDestroyed) {\n              const info = genErrorInfo('onDestroyed')\n              throw new Error(info)\n            }\n          },\n          onMaxLength(editor) {\n            context.emit('onMaxLength', editor)\n            if (props.defaultConfig.onMaxLength) {\n              const info = genErrorInfo('onMaxLength')\n              throw new Error(info)\n            }\n          },\n          onFocus(editor) {\n            context.emit('onFocus', editor)\n            if (props.defaultConfig.onFocus) {\n              const info = genErrorInfo('onFocus')\n              throw new Error(info)\n            }\n          },\n          onBlur(editor) {\n            context.emit('onBlur', editor)\n            if (props.defaultConfig.onBlur) {\n              const info = genErrorInfo('onBlur')\n              throw new Error(info)\n            }\n          },\n          customAlert(info, type) {\n            context.emit('customAlert', info, type)\n            // @ts-ignore\n            if (props.defaultConfig.customAlert) {\n              const info = genErrorInfo('customAlert')\n              throw new Error(info)\n            }\n          },\n          customPaste: (editor, event): any => {\n            if (props.defaultConfig.customPaste) {\n              const info = genErrorInfo('customPaste')\n              throw new Error(info)\n            }\n            let res\n            context.emit('customPaste', editor, event, (val: boolean) => {\n              res = val\n            })\n            return res\n          },\n        },\n      })\n    }\n\n    /**\n     * 设置 HTML\n     * @param newHtml new html\n     */\n    function setHtml(newHtml: string) {\n      const editor = editorRef.value\n      if (editor == null) return\n      editor.setHtml(newHtml)\n    }\n\n    /**\n     * 元素挂在后初始化编辑器\n     */\n    onMounted(() => {\n      initEditor()\n    })\n\n    // 监听 v-model 值变化\n    watch(\n      () => props.modelValue,\n      newVal => {\n        if (newVal === curValue.value) return // 和当前内容一样，则忽略\n\n        // 重新设置 HTML\n        setHtml(newVal)\n      }\n    )\n\n    return {\n      box,\n    }\n  },\n})\n</script>\n", "<template>\n  <div ref=\"selector\"></div>\n</template>\n<script lang=\"ts\">\nimport { defineComponent, ref, watchEffect, PropType } from 'vue'\nimport { createToolbar, IToolbarConfig, IDomEditor, DomEditor } from '@wangeditor/editor'\n\nexport default defineComponent({\n  props: {\n    // editor 实例\n    editor: {\n      type: Object as PropType<IDomEditor>\n    },\n    /** 编辑器模式 */\n    mode: {\n      type: String,\n      default: 'default',\n    },\n    /** 编辑器默认配置 */\n    defaultConfig: {\n      type: Object as PropType<Partial<IToolbarConfig>>,\n      default: {},\n    },\n  },\n  setup(props) {\n    // toolbar 容器\n    const selector = ref(null)\n\n    /**\n     * 初始化 toolbar\n     */\n    const create = (editor: IDomEditor) => {\n      if (!selector.value) return\n      if (editor == null) {\n        throw new Error('Not found instance of Editor when create <Toolbar/> component')\n      }\n      if (DomEditor.getToolbar(editor)) return // 不重复创建\n\n      createToolbar({\n        editor,\n        selector: (selector.value! as Element) || '<div></div>',\n        mode: props.mode,\n        config: props.defaultConfig,\n      })\n    }\n\n    watchEffect( () => {\n      const { editor } = props\n      if (editor == null) return\n      create(editor) // 初始化 toolbar\n    })\n\n    return {\n      selector,\n    }\n  },\n})\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sBAI6B,QAAwB;MAC/C,OAAO,SAAS,MAAA;UACZ;eAAkB,MAAA;SACnB;AAAA;ACNT,IAAA,cAAe,CAAC,KAAK,UAAU;AAC7B,aAAW,CAAC,KAAK,GAAA,KAAQ,OAAO;AAC9B,QAAI,GAAA,IAAO;EAAA;AAEb,SAAO;AAAA;ACIT,IAAKA,cAAa,gBAAa;EAC7B,OAAO;IAEL,MAAM;MACJ,MAAM;MACN,SAAS;IAAA;IAGX,gBAAgB;MACd,MAAM;MACN,SAAS,CAAA;IAAA;IAEX,aAAa;MACX,MAAM;MACN,SAAS;IAAA;IAGX,eAAe;MACb,MAAM;MACN,SAAS,CAAA;IAAA;IAGX,YAAY;MACV,MAAM;MACN,SAAS;IAAA;EAAA;EAGb,MAAM,OAAO,SAAS;UACd,MAAM,IAAI,IAAA;UAEV,YAAY,WAA8B,IAAA;UAE1C,WAAW,IAAI,EAAA;UAKf,aAAa,MAAM;UACnB,CAAC,IAAI;;YAEH,iBAAiB,MAAM,MAAM,cAAA;UAEtB;QACX,UAAU,IAAI;QACd,MAAM,MAAM;QACZ,SAAS,kBAAkB,CAAA;QAC3B,MAAM,MAAM,eAAe,MAAM,cAAc;QAC/C,QAAQ,cAAA,eAAA,CAAA,GACH,MAAM,aAAA,GADH;UAEN,UAAU,QAAQ;sBACN,QAAQ;oBAEV,KAAK,aAAa,MAAA;gBAEtB,MAAM,cAAc,WAAW;oBAC3B,OAAO,aAAa,WAAA;oBACpB,IAAI,MAAM,IAAA;YAAA;UAAA;UAGpB,SAAS,QAAQ;kBACT,aAAa,OAAO,QAAA;qBACjB,QAAQ;oBACT,KAAK,qBAAqB,UAAA;oBAE1B,KAAK,YAAY,MAAA;gBACrB,MAAM,cAAc,UAAU;oBAC1B,OAAO,aAAa,UAAA;oBACpB,IAAI,MAAM,IAAA;YAAA;UAAA;UAGpB,YAAY,QAAQ;oBACV,KAAK,eAAe,MAAA;gBACxB,MAAM,cAAc,aAAa;oBAC7B,OAAO,aAAa,aAAA;oBACpB,IAAI,MAAM,IAAA;YAAA;UAAA;UAGpB,YAAY,QAAQ;oBACV,KAAK,eAAe,MAAA;gBACxB,MAAM,cAAc,aAAa;oBAC7B,OAAO,aAAa,aAAA;oBACpB,IAAI,MAAM,IAAA;YAAA;UAAA;UAGpB,QAAQ,QAAQ;oBACN,KAAK,WAAW,MAAA;gBACpB,MAAM,cAAc,SAAS;oBACzB,OAAO,aAAa,SAAA;oBACpB,IAAI,MAAM,IAAA;YAAA;UAAA;UAGpB,OAAO,QAAQ;oBACL,KAAK,UAAU,MAAA;gBACnB,MAAM,cAAc,QAAQ;oBACxB,OAAO,aAAa,QAAA;oBACpB,IAAI,MAAM,IAAA;YAAA;UAAA;UAGpB,YAAY,MAAM,MAAM;oBACd,KAAK,eAAe,MAAM,IAAA;gBAE9B,MAAM,cAAc,aAAa;oBAC7B,QAAO,aAAa,aAAA;oBACpB,IAAI,MAAM,KAAA;YAAA;UAAA;UAGpB,aAAa,CAAC,QAAQ,UAAe;gBAC/B,MAAM,cAAc,aAAa;oBAC7B,OAAO,aAAa,aAAA;oBACpB,IAAI,MAAM,IAAA;YAAA;gBAEd;oBACI,KAAK,eAAe,QAAQ,OAAO,CAAC,QAAiB;oBACrD;YAAA,CAAA;mBAED;UAAA;QAAA,CAAA;MAAA,CAAA;IAAA;qBAUE,SAAiB;YAC1B,SAAS,UAAU;UACrB,UAAU;;aACP,QAAQ,OAAA;IAAA;cAMP,MAAM;;;UAMd,MAAM,MAAM,YACZ,CAAA,WAAU;UACJ,WAAW,SAAS;;cAGhB,MAAA;IAAA,CAAA;WAIL;MACL;IAAA;EAAA;AAAA,CAAA;IA9JIC,eAAM;EAAC,KAAoB;EAAA,OAAA,EAAA,UAAA,OAAA;;6BAAnC,QAAA,QAAA,QAAA,OAAA,UAA0C;;;;ACM5C,IAAK,YAAa,gBAAa;EAC7B,OAAO;IAEL,QAAQ;MACN,MAAM;IAAA;IAGR,MAAM;MACJ,MAAM;MACN,SAAS;IAAA;IAGX,eAAe;MACb,MAAM;MACN,SAAS,CAAA;IAAA;EAAA;EAGb,MAAM,OAAO;UAEL,WAAW,IAAI,IAAA;UAKf,SAAS,CAAC,WAAuB;UACjC,CAAC,SAAS;;UACV,UAAU,MAAM;cACZ,IAAI,MAAM,+DAAA;MAAA;UAEd,GAAU,WAAW,MAAA;;UAEX;QACZ;QACA,UAAW,SAAS,SAAsB;QAC1C,MAAM,MAAM;QACZ,QAAQ,MAAM;MAAA,CAAA;IAAA;gBAIL,MAAM;YACX,EAAE,OAAA,IAAW;UACf,UAAU;;aACP,MAAA;IAAA,CAAA;WAGF;MACL;IAAA;EAAA;AAAA,CAAA;;2BApDJ,QAAA,QAAA,QAAA,OAAA,UAA0B;;;;", "names": ["_sfc_main", "_hoisted_1"]}