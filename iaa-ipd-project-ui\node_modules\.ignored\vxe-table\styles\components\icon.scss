@use "sass:map";
@use "sass:list";
@use './old-icon.scss';

@font-face {
  font-family: "vxetableiconfont";
  src: 
       url('data:application/x-font-woff2;charset=utf-8;base64,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') format('woff2');
}

@keyframes rollCircle {
  0% { 
    transform: rotate(0deg);
  }
  100% { 
    transform: rotate(360deg);
  }
}

$btnThemeList: (
  (
    name: "primary",
    textColor: var(--vxe-ui-font-primary-color),
  ),
  (
    name: "success",
    textColor: var(--vxe-ui-status-success-color),
  ),
  (
    name: "info",
    textColor: var(--vxe-ui-status-info-color),
  ),
  (
    name: "warning",
    textColor: var(--vxe-ui-status-warning-color),
  ),
  (
    name: "danger",
    textColor: var(--vxe-ui-status-danger-color),
  ),
  (
    name: "error",
    textColor: var(--vxe-ui-status-error-color),
  )
);

[class*="vxe-table-icon-"] {
  font-family: "vxetableiconfont" !important;
  font-style: normal;
  font-weight: 400;
  font-size: 1.1em;
  line-height: 1em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  &.animat,
  &.roll {
    display: inline-block;
  }
  &.animat {
    transition: transform 0.25s ease-in-out;
  }
  &.rotate45 {
    transform: rotate(45deg);
  }
  &.rotate90 {
    transform: rotate(90deg);
  }
  &.rotate180 {
    transform: rotate(180deg);
  }
  &.roll {
    animation: rollCircle 1s infinite linear;
  }
  @for $index from 0 to list.length($btnThemeList) {
    $item: list.nth($btnThemeList, $index + 1);
    &.theme--#{map.get($item, name)} {
      color: map.get($item, textColor);
    }
  }
}

.vxe-table-icon-close:before {
  content: "\e6e9";
}

.vxe-table-icon-grouping:before {
  content: "\e66c";
}

.vxe-table-icon-values:before {
  content: "\e66f";
}

.vxe-table-icon-add-sub:before {
  content: "\e6bc";
}

.vxe-table-icon-swap:before {
  content: "\e7f3";
}

.vxe-table-icon-sort:before {
  content: "\e93e";
}

.vxe-table-icon-no-drop:before {
  content: "\e658";
}

.vxe-table-icon-edit:before {
  content: "\e66e";
}

.vxe-table-icon-question-circle-fill:before {
  content: "\e690";
}

.vxe-table-icon-radio-checked:before {
  content: "\e75b";
}

.vxe-table-icon-radio-checked-fill:before {
  content: "\e763";
}

.vxe-table-icon-print:before {
  content: "\eba0";
}

.vxe-table-icon-checkbox-checked-fill:before {
  content: "\e67d";
}

.vxe-table-icon-custom-column:before {
  content: "\e62d";
}

.vxe-table-icon-radio-unchecked:before {
  content: "\e7c9";
}

.vxe-table-icon-caret-down:before {
  content: "\e8ed";
}

.vxe-table-icon-caret-up:before {
  content: "\e8ee";
}

.vxe-table-icon-caret-right:before {
  content: "\e8ef";
}

.vxe-table-icon-caret-left:before {
  content: "\e8f0";
}

.vxe-table-icon-fullscreen:before {
  content: "\e70e";
}

.vxe-table-icon-minimize:before {
  content: "\e749";
}

.vxe-table-icon-checkbox-unchecked:before {
  content: "\e727";
}

.vxe-table-icon-funnel:before {
  content: "\e8ec";
}

.vxe-table-icon-download:before {
  content: "\e61a";
}

.vxe-table-icon-spinner:before {
  content: "\e601";
}

.vxe-table-icon-arrow-right:before {
  content: "\e743";
}

.vxe-table-icon-repeat:before {
  content: "\ea4a";
}

.vxe-table-icon-drag-handle:before {
  content: "\e64e";
}

.vxe-table-icon-checkbox-indeterminate-fill:before {
  content: "\e8c4";
}

.vxe-table-icon-upload:before {
  content: "\e683";
}

.vxe-table-icon-fixed-left-fill:before {
  content: "\e9b9";
}

.vxe-table-icon-fixed-left:before {
  content: "\e9ba";
}

.vxe-table-icon-fixed-right-fill:before {
  content: "\f290";
}

.vxe-table-icon-fixed-right:before {
  content: "\f291";
}

