import { getI18n } from '@vxe-ui/core';
export const getListDesignActionButtonName = (name) => {
    return getI18n(`vxe.listDesign.activeBtn.${name}`);
};
export const handleGetListDesignActionButtonName = (params) => {
    return getListDesignActionButtonName(params.name);
};
export const createListDesignActionButton = (btnObj) => {
    return Object.assign({
        name: '',
        icon: '',
        type: '',
        classify: '',
        code: '',
        status: '',
        permissionCode: ''
    }, btnObj);
};
