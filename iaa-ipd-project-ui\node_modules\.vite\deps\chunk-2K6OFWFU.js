import {
  <PERSON>umn<PERSON>og<PERSON>
} from "./chunk-KE3PEXQO.js";
import {
  Checkbox$1
} from "./chunk-GFI6XVUE.js";
import {
  __assign,
  __decorate,
  __extends,
  __rest
} from "./chunk-F25BIIHK.js";
import {
  Renderer,
  isVisible
} from "./chunk-LZQZ2OHM.js";
import {
  require_react
} from "./chunk-ZUWHLQVA.js";
import {
  __toESM
} from "./chunk-GFT2G5UO.js";

// node_modules/amis/esm/renderers/Table2/ColumnToggler.js
var import_react = __toESM(require_react());
var ColumnTogglerRenderer = (
  /** @class */
  function(_super) {
    __extends(ColumnTogglerRenderer2, _super);
    function ColumnTogglerRenderer2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    ColumnTogglerRenderer2.prototype.render = function() {
      var _a = this.props, className = _a.className, store = _a.store, render = _a.render, ns = _a.classPrefix, cx = _a.classnames, tooltip = _a.tooltip, align = _a.align, cols = _a.cols, toggleAllColumns = _a.toggleAllColumns, toggleToggle = _a.toggleToggle, data = _a.data, size = _a.size, popOverContainer = _a.popOverContainer, rest = __rest(_a, ["className", "store", "render", "classPrefix", "classnames", "tooltip", "align", "cols", "toggleAllColumns", "toggleToggle", "data", "size", "popOverContainer"]);
      var __ = rest.translate;
      var env = rest.env;
      if (!cols) {
        return null;
      }
      var toggableColumns = cols.filter(function(item) {
        return isVisible(item.pristine || item, data) && item.toggable !== false;
      });
      var activeToggaleColumns = toggableColumns.filter(function(item) {
        return item.toggled !== false;
      });
      return import_react.default.createElement(
        ColumnToggler,
        __assign({}, rest, { render, tooltip: tooltip || __("Table.columnsVisibility"), tooltipContainer: popOverContainer || env.getModalContainer, isActived: cols.findIndex(function(column) {
          return !column.toggled;
        }) !== -1, align: align !== null && align !== void 0 ? align : "right", size: size || "sm", classnames: cx, classPrefix: ns, key: "columns-toggable", columns: cols, activeToggaleColumns, data }),
        (toggableColumns === null || toggableColumns === void 0 ? void 0 : toggableColumns.length) ? import_react.default.createElement(
          "li",
          { className: cx("ColumnToggler-menuItem"), key: "selectAll", onClick: function() {
            toggleAllColumns && toggleAllColumns((activeToggaleColumns === null || activeToggaleColumns === void 0 ? void 0 : activeToggaleColumns.length) <= 0);
          } },
          import_react.default.createElement(Checkbox$1, { size: "sm", classPrefix: ns, key: "checkall", checked: !!(activeToggaleColumns === null || activeToggaleColumns === void 0 ? void 0 : activeToggaleColumns.length), partial: !!((activeToggaleColumns === null || activeToggaleColumns === void 0 ? void 0 : activeToggaleColumns.length) && (activeToggaleColumns === null || activeToggaleColumns === void 0 ? void 0 : activeToggaleColumns.length) !== (toggableColumns === null || toggableColumns === void 0 ? void 0 : toggableColumns.length)) }, __("Select.checkAll"))
        ) : null,
        toggableColumns === null || toggableColumns === void 0 ? void 0 : toggableColumns.map(function(column, index) {
          return import_react.default.createElement(
            "li",
            { className: cx("ColumnToggler-menuItem"), key: "item" + (column.index || index), onClick: function() {
              toggleToggle && toggleToggle(index);
            } },
            import_react.default.createElement(Checkbox$1, { size: "sm", classPrefix: ns, checked: column.toggled !== false }, column.title ? render("tpl", column.title) : column.label || null)
          );
        })
      );
    };
    ColumnTogglerRenderer2 = __decorate([
      Renderer({
        type: "column-toggler",
        name: "column-toggler"
      })
    ], ColumnTogglerRenderer2);
    return ColumnTogglerRenderer2;
  }(import_react.default.Component)
);

export {
  ColumnTogglerRenderer
};
//# sourceMappingURL=chunk-2K6OFWFU.js.map
