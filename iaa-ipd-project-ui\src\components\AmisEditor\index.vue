<template>
  <div style="height: 100%">
    <AmisEditor
      id="editorName"
      theme="cxd"
      className="test-amis-editor"
      :preview="previewModel"
      :isMobile="mobileModel"
      :value="schema"
      :onChange="editorChanged"
    />
  </div>
</template>

<script lang="ts" setup>
// 引入一些样式依赖
import 'amis-ui/lib/themes/default.css'
import 'amis-ui/lib/themes/cxd.css'
import 'amis-editor-core/lib/style.css'

import { applyReactInVue } from 'veaury'
import { Editor } from 'amis-editor'
import { ref, reactive } from 'vue'

// 使用veaury将React组件转换为Vue组件
const AmisEditor = applyReactInVue(Editor)

const previewModel = ref(false) //是否预览
const mobileModel = ref(false) //是否是手机模式

const schema = reactive({
  type: 'page',
  title: '表单页面',
  body: [
    {
      type: 'form',
      mode: 'horizontal',
      api: '/saveForm',
      body: [
        {
          label: '姓名',
          type: 'input-text',
          name: 'name'
        }
      ]
    }
  ]
})

const editorChanged = (value: any) => {
  console.log('编辑器内容变化了：', value)
  // 更新schema
  Object.assign(schema, value)
}
</script>

<style scoped>
:deep(.test-amis-editor) {
  height: 100% !important;
  overflow-y: auto;
}
</style>
