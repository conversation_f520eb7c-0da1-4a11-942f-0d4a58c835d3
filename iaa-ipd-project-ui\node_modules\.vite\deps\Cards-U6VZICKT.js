import {
  sortable_esm_default
} from "./chunk-EH2XFRG5.js";
import "./chunk-GFI6XVUE.js";
import "./chunk-ZRTRFIWU.js";
import "./chunk-6C4OBBQZ.js";
import {
  Button$1
} from "./chunk-M5OFQAQB.js";
import {
  Icon,
  Spinner$1
} from "./chunk-YPPVVTGH.js";
import {
  __assign,
  __awaiter,
  __decorate,
  __extends,
  __generator,
  __metadata
} from "./chunk-F25BIIHK.js";
import {
  CustomStyle,
  ListStore,
  Renderer,
  ScopedContext,
  anyChanged,
  buildStyle,
  createObject,
  difference,
  filter,
  filterClassNameObject,
  getMatchedEventTargets,
  getPropValue,
  isPureVariable,
  require_find,
  resolveVariableAndFilter,
  setThemeClassName,
  ucFirst
} from "./chunk-LZQZ2OHM.js";
import "./chunk-QMS2VRR4.js";
import "./chunk-5QW7M2DY.js";
import "./chunk-ZJNJ4ZV3.js";
import "./chunk-KEABM62F.js";
import "./chunk-CNJUQDSN.js";
import "./chunk-YZFIV7S4.js";
import {
  require_react_dom
} from "./chunk-7XBFYOTW.js";
import {
  require_react
} from "./chunk-ZUWHLQVA.js";
import "./chunk-KNGT5NUO.js";
import "./chunk-X3JSFYRQ.js";
import {
  __toESM
} from "./chunk-GFT2G5UO.js";

// node_modules/amis/esm/renderers/Cards.js
var import_react = __toESM(require_react());
var import_react_dom = __toESM(require_react_dom());
var import_find = __toESM(require_find());
var Cards = (
  /** @class */
  function(_super) {
    __extends(Cards2, _super);
    function Cards2(props) {
      var _this = _super.call(this, props) || this;
      _this.handleAction = _this.handleAction.bind(_this);
      _this.handleCheck = _this.handleCheck.bind(_this);
      _this.handleClick = _this.handleClick.bind(_this);
      _this.handleCheckAll = _this.handleCheckAll.bind(_this);
      _this.handleQuickChange = _this.handleQuickChange.bind(_this);
      _this.handleSave = _this.handleSave.bind(_this);
      _this.handleSaveOrder = _this.handleSaveOrder.bind(_this);
      _this.reset = _this.reset.bind(_this);
      _this.dragTipRef = _this.dragTipRef.bind(_this);
      _this.bodyRef = _this.bodyRef.bind(_this);
      _this.renderToolbar = _this.renderToolbar.bind(_this);
      var store = props.store, selectable = props.selectable, draggable = props.draggable, orderBy = props.orderBy, orderDir = props.orderDir, multiple = props.multiple, hideCheckToggler = props.hideCheckToggler, itemCheckableOn = props.itemCheckableOn, itemDraggableOn = props.itemDraggableOn;
      store.update({
        selectable,
        draggable,
        orderBy,
        orderDir,
        multiple,
        hideCheckToggler,
        itemCheckableOn,
        itemDraggableOn
      });
      Cards2.syncItems(store, _this.props) && _this.syncSelected();
      return _this;
    }
    Cards2.syncItems = function(store, props, prevProps) {
      var source = props.source;
      var value = getPropValue(props, function(props2) {
        return props2.items;
      });
      var items = [];
      var updateItems = false;
      if (Array.isArray(value) && (!prevProps || getPropValue(prevProps, function(props2) {
        return props2.items;
      }) !== value)) {
        items = value;
        updateItems = true;
      } else if (typeof source === "string") {
        var resolved = resolveVariableAndFilter(source, props.data, "| raw");
        var prev = prevProps ? resolveVariableAndFilter(source, prevProps.data, "| raw") : null;
        if (prev === resolved) {
          updateItems = false;
        } else {
          items = Array.isArray(resolved) ? resolved : [];
          updateItems = true;
        }
      }
      updateItems && store.initItems(items, props.fullItems, props.selected);
      Array.isArray(props.selected) && store.updateSelected(props.selected, props.valueField);
      return updateItems;
    };
    Cards2.prototype.componentDidUpdate = function(prevProps) {
      var props = this.props;
      var store = props.store;
      if (anyChanged([
        "selectable",
        "draggable",
        "orderBy",
        "orderDir",
        "multiple",
        "hideCheckToggler",
        "itemCheckableOn",
        "itemDraggableOn"
      ], prevProps, props)) {
        store.update({
          selectable: props.selectable,
          draggable: props.draggable,
          orderBy: props.orderBy,
          orderDir: props.orderDir,
          multiple: props.multiple,
          hideCheckToggler: props.hideCheckToggler,
          itemCheckableOn: props.itemCheckableOn,
          itemDraggableOn: props.itemDraggableOn
        });
      }
      if (anyChanged(["source", "value", "items"], prevProps, props) || !props.value && !props.items && (props.data !== prevProps.data || typeof props.source === "string" && isPureVariable(props.source))) {
        Cards2.syncItems(store, props, prevProps) && this.syncSelected();
      } else if (prevProps.selected !== props.selected) {
        store.updateSelected(props.selected || [], props.valueField);
      }
    };
    Cards2.prototype.bodyRef = function(ref) {
      this.body = ref;
    };
    Cards2.prototype.handleAction = function(e, action, ctx) {
      var onAction = this.props.onAction;
      return onAction === null || onAction === void 0 ? void 0 : onAction(e, action, ctx);
    };
    Cards2.prototype.handleCheck = function(item) {
      item.toggle();
      this.syncSelected();
      var _a = this.props, store = _a.store, dispatchEvent = _a.dispatchEvent;
      dispatchEvent(
        //增删改查卡片模式选择表格项
        "selectedChange",
        createObject(store.data, __assign(__assign({}, store.eventContext), { item: item.data }))
      );
    };
    Cards2.prototype.handleClick = function(item) {
      var _a = this.props, dispatchEvent = _a.dispatchEvent, data = _a.data;
      return dispatchEvent(
        //增删改查卡片模式单击卡片
        "rowClick",
        createObject(data, {
          item: item.data,
          index: item.index
        })
      );
    };
    Cards2.prototype.handleCheckAll = function() {
      var store = this.props.store;
      store.toggleAll();
      this.syncSelected();
    };
    Cards2.prototype.handleSelectAll = function() {
      var store = this.props.store;
      store.selectAll();
      this.syncSelected();
    };
    Cards2.prototype.handleClearAll = function() {
      var store = this.props.store;
      store.clearAll();
      this.syncSelected();
    };
    Cards2.prototype.syncSelected = function() {
      var _a = this.props, store = _a.store, onSelect = _a.onSelect, dispatchEvent = _a.dispatchEvent;
      var selectItems = store.selectedItems.map(function(item) {
        return item.data;
      });
      var unSelectItems = store.unSelectedItems.map(function(item) {
        return item.data;
      });
      dispatchEvent("selected", createObject(store.data, {
        selectItems,
        unSelectItems
      }));
      onSelect && onSelect(selectItems, unSelectItems);
    };
    Cards2.prototype.handleQuickChange = function(item, values, saveImmediately, savePristine, options) {
      item.change(values, savePristine);
      var _a = this.props, onSave = _a.onSave, onItemChange = _a.onItemChange, primaryField = _a.primaryField;
      if (savePristine) {
        return;
      }
      onItemChange === null || onItemChange === void 0 ? void 0 : onItemChange(item.data, difference(item.data, item.pristine, ["id", primaryField]), item.index);
      if (saveImmediately && saveImmediately.api) {
        this.props.onAction(null, {
          actionType: "ajax",
          api: saveImmediately.api,
          reload: options === null || options === void 0 ? void 0 : options.reload
        }, item.locals);
        return;
      }
      if (!saveImmediately || !onSave) {
        return;
      }
      onSave(item.data, difference(item.data, item.pristine, ["id", primaryField]), item.index, void 0, item.pristine, options);
    };
    Cards2.prototype.handleSave = function() {
      var _a = this.props, store = _a.store, onSave = _a.onSave, primaryField = _a.primaryField;
      if (!onSave || !store.modifiedItems.length) {
        return;
      }
      var items = store.modifiedItems.map(function(item) {
        return item.data;
      });
      var itemIndexes = store.modifiedItems.map(function(item) {
        return item.index;
      });
      var diff = store.modifiedItems.map(function(item) {
        return difference(item.data, item.pristine, ["id", primaryField]);
      });
      var unModifiedItems = store.items.filter(function(item) {
        return !item.modified;
      }).map(function(item) {
        return item.data;
      });
      onSave(items, diff, itemIndexes, unModifiedItems, store.modifiedItems.map(function(item) {
        return item.pristine;
      }));
    };
    Cards2.prototype.handleSaveOrder = function() {
      return __awaiter(this, void 0, void 0, function() {
        var _a, store, onSaveOrder, data, dispatchEvent, movedItems, rendererEvent;
        return __generator(this, function(_b) {
          switch (_b.label) {
            case 0:
              _a = this.props, store = _a.store, onSaveOrder = _a.onSaveOrder, data = _a.data, dispatchEvent = _a.dispatchEvent;
              movedItems = store.movedItems.map(function(item) {
                return item.data;
              });
              return [4, dispatchEvent("orderChange", createObject(data, { movedItems }))];
            case 1:
              rendererEvent = _b.sent();
              if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                return [
                  2
                  /*return*/
                ];
              }
              if (!onSaveOrder || !store.movedItems.length) {
                return [
                  2
                  /*return*/
                ];
              }
              onSaveOrder(store.movedItems.map(function(item) {
                return item.data;
              }), store.items.map(function(item) {
                return item.data;
              }));
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    Cards2.prototype.reset = function() {
      var store = this.props.store;
      store.reset();
    };
    Cards2.prototype.bulkUpdate = function(value, items) {
      var _a = this.props, store = _a.store, primaryField = _a.primaryField;
      if (primaryField && value.ids) {
        var ids_1 = value.ids.split(",");
        var rows = store.items.filter(function(item) {
          return (0, import_find.default)(ids_1, function(id) {
            return id && id == item.data[primaryField];
          });
        });
        var newValue_1 = __assign(__assign({}, value), { ids: void 0 });
        rows.forEach(function(item) {
          return item.change(newValue_1);
        });
      } else if (Array.isArray(items)) {
        var rows = store.items.filter(function(item) {
          return ~items.indexOf(item.pristine);
        });
        rows.forEach(function(item) {
          return item.change(value);
        });
      }
    };
    Cards2.prototype.getSelected = function() {
      var store = this.props.store;
      return store.selectedItems.map(function(item) {
        return item.data;
      });
    };
    Cards2.prototype.dragTipRef = function(ref) {
      if (!this.dragTip && ref) {
        this.initDragging();
      } else if (this.dragTip && !ref) {
        this.destroyDragging();
      }
      this.dragTip = ref;
    };
    Cards2.prototype.initDragging = function() {
      if (this.sortable)
        return;
      var store = this.props.store;
      var dom = (0, import_react_dom.findDOMNode)(this);
      var ns = this.props.classPrefix;
      this.sortable = new sortable_esm_default(dom.querySelector(".".concat(ns, "Cards-body")), {
        group: "table",
        animation: 150,
        handle: ".".concat(ns, "Card-dragBtn"),
        ghostClass: "is-dragging",
        onEnd: function(e) {
          if (e.newIndex === e.oldIndex) {
            return;
          }
          var parent = e.to;
          if (e.oldIndex < parent.childNodes.length - 1) {
            parent.insertBefore(e.item, parent.childNodes[e.oldIndex > e.newIndex ? e.oldIndex + 1 : e.oldIndex]);
          } else {
            parent.appendChild(e.item);
          }
          store.exchange(e.oldIndex, e.newIndex);
        }
      });
    };
    Cards2.prototype.destroyDragging = function() {
      this.sortable && this.sortable.destroy();
      this.sortable = void 0;
    };
    Cards2.prototype.renderActions = function(region) {
      var _this = this;
      var _a = this.props, actions = _a.actions, render = _a.render, store = _a.store, cx = _a.classnames;
      var btn;
      actions = Array.isArray(actions) ? actions.concat() : [];
      if (!~this.renderedToolbars.indexOf("check-all") && (btn = this.renderCheckAll())) {
        actions.unshift({
          type: "button",
          children: btn
        });
      }
      if (region === "header" && !~this.renderedToolbars.indexOf("drag-toggler") && (btn = this.renderDragToggler())) {
        actions.unshift({
          type: "button",
          children: btn
        });
      }
      return Array.isArray(actions) && actions.length ? import_react.default.createElement("div", { className: cx("Cards-actions") }, actions.map(function(action, key) {
        return render("action/".concat(key), __assign({ type: "button" }, action), {
          onAction: _this.handleAction,
          key,
          btnDisabled: store.dragging
        });
      })) : null;
    };
    Cards2.prototype.renderHeading = function() {
      var _a = this.props, title = _a.title, store = _a.store, hideQuickSaveBtn = _a.hideQuickSaveBtn, cx = _a.classnames, data = _a.data;
      if (title || store.modified && !hideQuickSaveBtn || store.moved) {
        return import_react.default.createElement("div", { className: cx("Cards-heading") }, store.modified && !hideQuickSaveBtn ? import_react.default.createElement(
          "span",
          null,
          "当前有 ".concat(store.modified, " 条记录修改了内容, 但并没有提交。请选择:"),
          import_react.default.createElement(
            "button",
            { type: "button", className: cx("Button Button--xs Button--success m-l-sm"), onClick: this.handleSave },
            import_react.default.createElement(Icon, { icon: "check", className: "icon m-r-xs" }),
            "提交"
          ),
          import_react.default.createElement(
            "button",
            { type: "button", className: cx("Button Button--xs Button--danger m-l-sm"), onClick: this.reset },
            import_react.default.createElement(Icon, { icon: "close", className: "icon m-r-xs" }),
            "放弃"
          )
        ) : store.moved ? import_react.default.createElement(
          "span",
          null,
          "当前有 ".concat(store.moved, " 条记录修改了顺序, 但并没有提交。请选择:"),
          import_react.default.createElement(
            "button",
            { type: "button", className: cx("Button Button--xs Button--success m-l-sm"), onClick: this.handleSaveOrder },
            import_react.default.createElement(Icon, { icon: "check", className: "icon m-r-xs" }),
            "提交"
          ),
          import_react.default.createElement(
            "button",
            { type: "button", className: cx("Button Button--xs Button--danger m-l-sm"), onClick: this.reset },
            import_react.default.createElement(Icon, { icon: "close", className: "icon m-r-xs" }),
            "放弃"
          )
        ) : title ? filter(title, data) : "");
      }
      return null;
    };
    Cards2.prototype.renderHeader = function() {
      var _a = this.props, header = _a.header, headerClassName = _a.headerClassName, headerToolbar = _a.headerToolbar, headerToolbarRender = _a.headerToolbarRender, showHeader = _a.showHeader, render = _a.render, store = _a.store, cx = _a.classnames, __ = _a.translate;
      if (showHeader === false) {
        return null;
      }
      var child = headerToolbarRender ? headerToolbarRender(__assign(__assign({}, this.props), store.eventContext), this.renderToolbar) : null;
      var actions = this.renderActions("header");
      var toolbarNode = actions || child || store.dragging ? import_react.default.createElement(
        "div",
        { className: cx("Cards-toolbar"), key: "header-toolbar" },
        actions,
        child,
        store.dragging ? import_react.default.createElement("div", { className: cx("Cards-dragTip"), ref: this.dragTipRef }, __("Card.dragTip")) : null
      ) : null;
      var headerNode = header ? import_react.default.createElement("div", { className: cx("Cards-header", headerClassName), key: "header" }, render("header", header)) : null;
      return headerNode && toolbarNode ? [headerNode, toolbarNode] : headerNode || toolbarNode || null;
    };
    Cards2.prototype.renderFooter = function() {
      var _a = this.props, footer = _a.footer, footerClassName = _a.footerClassName, footerToolbar = _a.footerToolbar, footerToolbarRender = _a.footerToolbarRender, render = _a.render, showFooter = _a.showFooter, store = _a.store, cx = _a.classnames, affixFooter = _a.affixFooter;
      if (showFooter === false) {
        return null;
      }
      var child = footerToolbarRender ? footerToolbarRender(__assign(__assign({}, this.props), store.eventContext), this.renderToolbar) : null;
      var actions = this.renderActions("footer");
      var footerNode = footer ? import_react.default.createElement("div", { className: cx("Cards-footer", footerClassName, affixFooter ? "Cards-footer--affix" : ""), key: "footer" }, render("footer", footer)) : null;
      var toolbarNode = actions || child ? import_react.default.createElement(
        "div",
        { className: cx("Cards-toolbar", !footerNode && affixFooter ? "Cards-footToolbar--affix" : ""), key: "footer-toolbar" },
        actions,
        child
      ) : null;
      return footerNode && toolbarNode ? [toolbarNode, footerNode] : footerNode || toolbarNode || null;
    };
    Cards2.prototype.renderCheckAll = function() {
      var _a = this.props, store = _a.store, multiple = _a.multiple, selectable = _a.selectable, checkAll = _a.checkAll;
      if (!store.selectable || !multiple || !selectable || store.dragging || !store.items.length || !checkAll) {
        return null;
      }
      return import_react.default.createElement(Button$1, { key: "checkall", tooltip: "切换全选", onClick: this.handleCheckAll, size: "sm", level: store.allChecked ? "info" : "default" }, "全选");
    };
    Cards2.prototype.renderDragToggler = function() {
      var _this = this;
      var _a = this.props, store = _a.store, multiple = _a.multiple, selectable = _a.selectable, popOverContainer = _a.popOverContainer, env = _a.env, __ = _a.translate, dragIcon = _a.dragIcon;
      if (!store.draggable || store.items.length < 2) {
        return null;
      }
      return import_react.default.createElement(Button$1, { iconOnly: true, key: "dragging-toggle", tooltip: __("Card.toggleDrag"), tooltipContainer: popOverContainer || (env === null || env === void 0 ? void 0 : env.getModalContainer), size: "sm", active: store.dragging, onClick: function(e) {
        e.preventDefault();
        store.toggleDragging();
        store.dragging && store.clear();
        store.dragging ? _this.initDragging() : void 0;
      } }, import_react.default.isValidElement(dragIcon) ? dragIcon : import_react.default.createElement(Icon, { icon: "exchange", className: "icon r90" }));
    };
    Cards2.prototype.renderToolbar = function(toolbar, index) {
      var type = toolbar.type || toolbar;
      if (type === "drag-toggler") {
        this.renderedToolbars.push(type);
        return this.renderDragToggler();
      } else if (type === "check-all") {
        this.renderedToolbars.push(type);
        return this.renderCheckAll();
      }
      return;
    };
    Cards2.prototype.renderCard = function(index, card, item, itemClassName, style) {
      var _this = this;
      var _a = this.props, render = _a.render, cx = _a.classnames, store = _a.store, multiple = _a.multiple, checkOnItemClick = _a.checkOnItemClick, hideCheckToggler = _a.hideCheckToggler;
      var cardProps = {
        className: cx(filterClassNameObject(card && card.className || "", item.locals), {
          "is-checked": item.checked,
          "is-modified": item.modified,
          "is-moved": item.moved,
          "is-dragging": store.dragging
        }),
        item,
        key: index,
        itemIndex: item.index,
        multiple,
        selectable: store.selectable,
        checkable: item.checkable,
        draggable: item.draggable,
        selected: item.checked,
        dragging: store.dragging,
        data: item.locals,
        onAction: this.handleAction,
        onCheck: this.handleCheck,
        onClick: this.handleClick,
        onQuickChange: store.dragging ? null : this.handleQuickChange
      };
      if ((card === null || card === void 0 ? void 0 : card.type) === "card2") {
        cardProps = __assign(__assign({}, cardProps), { item: item.locals, onCheck: function() {
          _this.handleCheck(item);
        } });
      }
      return import_react.default.createElement("div", { key: item.index, className: cx(itemClassName), style }, render("card/".concat(index), __assign({
        // @ts-ignore
        type: card.type || "card",
        hideCheckToggler,
        checkOnItemClick
      }, card), cardProps));
    };
    Cards2.prototype.render = function() {
      var _this = this;
      var _a = this.props, className = _a.className, style = _a.style, store = _a.store, columnsCount = _a.columnsCount, itemClassName = _a.itemClassName, placeholder = _a.placeholder, card = _a.card, data = _a.data, render = _a.render, affixHeader = _a.affixHeader, masonryLayout = _a.masonryLayout, itemsClassName = _a.itemsClassName, cx = _a.classnames, __ = _a.translate, _b = _a.loading, loading = _b === void 0 ? false : _b, loadingConfig = _a.loadingConfig, env = _a.env, id = _a.id, wrapperCustomStyle = _a.wrapperCustomStyle, themeCss = _a.themeCss, mobileUI = _a.mobileUI;
      this.renderedToolbars = [];
      var itemFinalClassName = function() {
        if (mobileUI && !masonryLayout) {
          return "";
        }
        if (masonryLayout && columnsCount) {
          var colWidth = Math.round(12 / columnsCount);
          return "Grid-col--xs".concat(colWidth, " Grid-col--sm").concat(colWidth, " Grid-col--md").concat(colWidth, " Grid-col--lg").concat(colWidth);
        }
        return itemClassName || "";
      }();
      var header = this.renderHeader();
      var heading = this.renderHeading();
      var footer = this.renderFooter();
      var masonryClassName = "";
      if (masonryLayout) {
        masonryClassName = "Cards--masonry " + itemFinalClassName.split(/\s/).map(function(item) {
          if (/^Grid-col--(xs|sm|md|lg)(\d+)/.test(item)) {
            return "Cards--masonry".concat(ucFirst(RegExp.$1)).concat(RegExp.$2);
          }
          return item;
        }).join(" ");
      }
      var wrapStyles = {};
      var itemStyles = {};
      if ((style === null || style === void 0 ? void 0 : style.gutterX) >= 0) {
        wrapStyles.marginLeft = wrapStyles.marginRight = -((style === null || style === void 0 ? void 0 : style.gutterX) / 2) + "px";
        itemStyles.paddingLeft = itemStyles.paddingRight = (style === null || style === void 0 ? void 0 : style.gutterX) / 2 + "px";
      }
      if ((style === null || style === void 0 ? void 0 : style.gutterY) >= 0) {
        itemStyles.marginBottom = (style === null || style === void 0 ? void 0 : style.gutterY) + "px";
      }
      return import_react.default.createElement(
        "div",
        { ref: this.bodyRef, className: cx("Cards", className, {
          "Cards--unsaved": !!store.modified || !!store.moved
        }, setThemeClassName(__assign(__assign({}, this.props), { name: "baseControlClassName", id, themeCss })), setThemeClassName(__assign(__assign({}, this.props), { name: "wrapperCustomStyle", id, themeCss: wrapperCustomStyle }))), style: buildStyle(style, data) },
        affixHeader ? import_react.default.createElement(
          "div",
          { className: cx("Cards-fixedTop") },
          header,
          heading
        ) : import_react.default.createElement(
          import_react.default.Fragment,
          null,
          header,
          heading
        ),
        store.items.length ? import_react.default.createElement("div", { className: cx("Cards-body Grid", itemsClassName, masonryClassName), style: wrapStyles }, store.items.map(function(item, index) {
          return _this.renderCard(index, card, item, itemFinalClassName, itemStyles);
        })) : import_react.default.createElement("div", { className: cx("Cards-placeholder") }, render("placeholder", __(placeholder))),
        footer,
        import_react.default.createElement(Spinner$1, { loadingConfig, overlay: true, show: loading }),
        import_react.default.createElement(CustomStyle, __assign({}, this.props, { config: {
          wrapperCustomStyle,
          id,
          themeCss,
          classNames: [
            {
              key: "baseControlClassName"
            }
          ]
        }, env }))
      );
    };
    Cards2.propsList = [
      "header",
      "headerToolbarRender",
      "footer",
      "footerToolbarRender",
      "placeholder",
      "source",
      "selectable",
      "headerClassName",
      "footerClassName",
      "hideQuickSaveBtn",
      "hideCheckToggler",
      "itemCheckableOn",
      "itemDraggableOn",
      "masonryLayout",
      "items",
      "valueField"
    ];
    Cards2.defaultProps = {
      className: "",
      placeholder: "placeholder.noData",
      source: "$items",
      selectable: false,
      headerClassName: "",
      footerClassName: "",
      itemClassName: "Grid-col--xs12 Grid-col--sm6 Grid-col--md4 Grid-col--lg3",
      hideCheckToggler: false,
      masonryLayout: false,
      affixHeader: true,
      itemsClassName: "",
      checkAll: true
    };
    return Cards2;
  }(import_react.default.Component)
);
var CardsRenderer = (
  /** @class */
  function(_super) {
    __extends(CardsRenderer2, _super);
    function CardsRenderer2(props, scoped) {
      var _this = _super.call(this, props) || this;
      scoped.registerComponent(_this);
      return _this;
    }
    CardsRenderer2.prototype.componentWillUnmount = function() {
      var _a;
      (_a = _super.prototype.componentWillUnmount) === null || _a === void 0 ? void 0 : _a.call(this);
      this.context.unRegisterComponent(this);
    };
    CardsRenderer2.prototype.receive = function(values, subPath) {
      var _a, _b, _c, _d;
      var scoped = this.context;
      var parents = (_a = scoped === null || scoped === void 0 ? void 0 : scoped.parent) === null || _a === void 0 ? void 0 : _a.getComponents();
      if ((_b = this.props) === null || _b === void 0 ? void 0 : _b.host) {
        return (_d = (_c = this.props.host).receive) === null || _d === void 0 ? void 0 : _d.call(_c, values, subPath);
      }
      if (subPath) {
        return scoped.send(subPath, values);
      }
    };
    CardsRenderer2.prototype.reload = function(subPath, query, ctx, silent, replace, args) {
      var _a, _b, _c;
      return __awaiter(this, void 0, void 0, function() {
        var store, scoped;
        return __generator(this, function(_d) {
          store = this.props.store;
          if ((args === null || args === void 0 ? void 0 : args.index) || (args === null || args === void 0 ? void 0 : args.condition)) {
            return [
              2
              /*return*/
            ];
          }
          scoped = this.context;
          if ((_a = this.props) === null || _a === void 0 ? void 0 : _a.host) {
            return [2, (_c = (_b = this.props.host).reload) === null || _c === void 0 ? void 0 : _c.call(_b, subPath, query, ctx)];
          }
          if (subPath) {
            return [2, scoped.reload(subPath, ctx)];
          }
          return [
            2
            /*return*/
          ];
        });
      });
    };
    CardsRenderer2.prototype.setData = function(values, replace, index, condition) {
      var _a, _b, _c;
      return __awaiter(this, void 0, void 0, function() {
        var store, targets;
        return __generator(this, function(_d) {
          switch (_d.label) {
            case 0:
              store = this.props.store;
              if (!(index !== void 0 || condition !== void 0))
                return [3, 2];
              return [4, getMatchedEventTargets(store.items, this.props.data, index, condition)];
            case 1:
              targets = _d.sent();
              targets.forEach(function(target) {
                target.updateData(values);
              });
              return [3, 3];
            case 2:
              if ((_a = this.props) === null || _a === void 0 ? void 0 : _a.host) {
                return [2, (_c = (_b = this.props.host).setData) === null || _c === void 0 ? void 0 : _c.call(_b, values, replace, index, condition)];
              } else {
                return [2, store.updateData(values, void 0, replace)];
              }
              _d.label = 3;
            case 3:
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    CardsRenderer2.prototype.getData = function() {
      var _a = this.props, store = _a.store, data = _a.data;
      return store.getData(data);
    };
    CardsRenderer2.prototype.hasModifiedItems = function() {
      return this.props.store.modified;
    };
    CardsRenderer2.prototype.doAction = function(action, ctx, throwErrors, args) {
      return __awaiter(this, void 0, void 0, function() {
        var _a, store, valueField, data, actionType, _b, rows;
        return __generator(this, function(_c) {
          switch (_c.label) {
            case 0:
              _a = this.props, store = _a.store, valueField = _a.valueField, data = _a.data;
              actionType = action === null || action === void 0 ? void 0 : action.actionType;
              _b = actionType;
              switch (_b) {
                case "selectAll":
                  return [3, 1];
                case "clearAll":
                  return [3, 2];
                case "select":
                  return [3, 3];
                case "initDrag":
                  return [3, 5];
                case "cancelDrag":
                  return [3, 6];
                case "submitQuickEdit":
                  return [3, 7];
              }
              return [3, 9];
            case 1:
              store.clear();
              store.toggleAll();
              this.syncSelected();
              return [3, 10];
            case 2:
              store.clear();
              this.syncSelected();
              return [3, 10];
            case 3:
              return [4, getMatchedEventTargets(store.items, ctx || this.props.data, args.index, args.condition, args.selected)];
            case 4:
              rows = _c.sent();
              store.updateSelected(rows.map(function(item) {
                return item.data;
              }), valueField);
              this.syncSelected();
              return [3, 10];
            case 5:
              store.startDragging();
              return [3, 10];
            case 6:
              store.stopDragging();
              return [3, 10];
            case 7:
              return [4, this.handleSave()];
            case 8:
              _c.sent();
              return [3, 10];
            case 9:
              return [2, this.handleAction(void 0, action, data)];
            case 10:
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    CardsRenderer2.contextType = ScopedContext;
    CardsRenderer2 = __decorate([
      Renderer({
        name: "cards",
        type: "cards",
        storeType: ListStore.name,
        weight: -100
        // 默认的 grid 不是这样，这个只识别 crud 下面的 grid
      }),
      __metadata("design:paramtypes", [Object, Object])
    ], CardsRenderer2);
    return CardsRenderer2;
  }(Cards)
);
export {
  CardsRenderer,
  Cards as default
};
//# sourceMappingURL=Cards-U6VZICKT.js.map
