{"version": 3, "sources": ["../../svg.js/dist/svg.js", "../../component-query/index.js", "../../bpmn-js-token-simulation/node_modules/min-dom/lib/query.js", "../../bpmn-js-token-simulation/lib/util/EventHelper.js", "../../bpmn-js-token-simulation/node_modules/min-dash/dist/index.esm.js", "../../bpmn-js-token-simulation/lib/util/ElementHelper.js", "../../bpmn-js-token-simulation/lib/util/GeometryUtil.js", "../../bpmn-js-token-simulation/lib/animation/Animation.js", "../../domify/index.js", "../../bpmn-js-token-simulation/node_modules/min-dom/lib/domify.js", "../../component-event/index.js", "../../bpmn-js-token-simulation/node_modules/min-dom/lib/event.js", "../../bpmn-js-token-simulation/lib/features/context-pads/handler/BoundaryEventHandler.js", "../../bpmn-js-token-simulation/lib/features/context-pads/handler/ExclusiveGatewayHandler.js", "../../bpmn-js-token-simulation/lib/features/context-pads/handler/IntermediateCatchEventHandler.js", "../../bpmn-js-token-simulation/lib/features/context-pads/handler/ProcessHandler.js", "../../bpmn-js-token-simulation/lib/features/context-pads/handler/StartEventHandler.js", "../../bpmn-js-token-simulation/lib/features/context-pads/ContextPads.js", "../../bpmn-js-token-simulation/lib/features/context-pads/index.js", "../../bpmn-js-token-simulation/lib/features/disable-modeling/DisableModeling.js", "../../bpmn-js-token-simulation/lib/features/disable-modeling/index.js", "../../bpmn-js-token-simulation/lib/features/element-notifications/ElementNotifications.js", "../../bpmn-js-token-simulation/lib/features/element-notifications/index.js", "../../component-indexof/index.js", "../../component-classes/index.js", "../../bpmn-js-token-simulation/node_modules/min-dom/lib/classes.js", "../../bpmn-js-token-simulation/lib/features/element-support/ElementSupport.js", "../../bpmn-js-token-simulation/lib/features/element-support/index.js", "../../bpmn-js-token-simulation/lib/features/exclusive-gateway-settings/ExclusiveGatewaySettings.js", "../../bpmn-js-token-simulation/lib/features/exclusive-gateway-settings/index.js", "../../bpmn-js-token-simulation/lib/features/log/Log.js", "../../bpmn-js-token-simulation/lib/features/log/index.js", "../../bpmn-js-token-simulation/lib/features/notifications/Notifications.js", "../../bpmn-js-token-simulation/lib/features/notifications/index.js", "../../bpmn-js-token-simulation/lib/features/pause-simulation/PauseSimulation.js", "../../bpmn-js-token-simulation/lib/features/pause-simulation/index.js", "../../bpmn-js-token-simulation/lib/features/preserve-element-colors/PreserveElementColors.js", "../../bpmn-js-token-simulation/lib/features/preserve-element-colors/index.js", "../../bpmn-js-token-simulation/lib/features/process-instance-ids/ProcessInstanceIds.js", "../../bpmn-js-token-simulation/lib/features/process-instance-ids/index.js", "../../bpmn-js-token-simulation/lib/features/process-instance-settings/ProcessInstanceSettings.js", "../../bpmn-js-token-simulation/lib/features/process-instance-settings/index.js", "../../bpmn-js-token-simulation/lib/features/process-instances/ProcessInstances.js", "../../bpmn-js-token-simulation/lib/features/process-instances/index.js", "../../bpmn-js-token-simulation/lib/features/reset-simulation/ResetSimulation.js", "../../bpmn-js-token-simulation/lib/features/reset-simulation/index.js", "../../bpmn-js-token-simulation/lib/features/set-animation-speed/SetAnimationSpeed.js", "../../bpmn-js-token-simulation/lib/features/set-animation-speed/index.js", "../../bpmn-js-token-simulation/node_modules/min-dom/lib/clear.js", "../../bpmn-js-token-simulation/lib/features/show-process-instance/ShowProcessInstance.js", "../../bpmn-js-token-simulation/lib/features/show-process-instance/index.js", "../../bpmn-js-token-simulation/lib/features/simulation-state/SimulationState.js", "../../bpmn-js-token-simulation/lib/features/simulation-state/index.js", "../../bpmn-js-token-simulation/lib/features/toggle-mode/modeler/ToggleMode.js", "../../bpmn-js-token-simulation/lib/features/toggle-mode/modeler/index.js", "../../bpmn-js-token-simulation/lib/features/token-count/TokenCount.js", "../../bpmn-js-token-simulation/lib/features/token-count/index.js", "../../bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/EndEventHandler.js", "../../bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/EventBasedGatewayHandler.js", "../../bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/ExclusiveGatewayHandler.js", "../../bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/IntermediateCatchEventHandler.js", "../../bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/IntermediateThrowEventHandler.js", "../../bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/ParallelGatewayHandler.js", "../../bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/StartEventHandler.js", "../../bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/SubProcessHandler.js", "../../bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/BoundaryEventHandler.js", "../../bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/TaskHandler.js", "../../bpmn-js-token-simulation/lib/features/token-simulation-behavior/TokenSimulationBehavior.js", "../../bpmn-js-token-simulation/lib/features/token-simulation-behavior/index.js", "../../bpmn-js-token-simulation/lib/features/editor-actions/EditorActions.js", "../../bpmn-js-token-simulation/lib/features/editor-actions/index.js", "../../bpmn-js-token-simulation/lib/features/keyboard-bindings/KeyboardBindings.js", "../../bpmn-js-token-simulation/lib/features/keyboard-bindings/index.js", "../../bpmn-js-token-simulation/lib/features/palette/Palette.js", "../../bpmn-js-token-simulation/lib/features/palette/index.js", "../../bpmn-js-token-simulation/lib/modeler.js", "../../bpmn-js-token-simulation/index.js"], "sourcesContent": ["/*!\n* svg.js - A lightweight library for manipulating and animating SVG.\n* @version 2.7.1\n* https://svgdotjs.github.io/\n*\n* @copyright Wout Fierens <<EMAIL>>\n* @license MIT\n*\n* BUILT: Fri Nov 30 2018 10:01:55 GMT+0100 (GMT+01:00)\n*/;\n(function(root, factory) {\r\n  /* istanbul ignore next */\r\n  if (typeof define === 'function' && define.amd) {\r\n    define(function(){\r\n      return factory(root, root.document)\r\n    })\r\n  } else if (typeof exports === 'object') {\r\n    module.exports = root.document ? factory(root, root.document) : function(w){ return factory(w, w.document) }\r\n  } else {\r\n    root.SVG = factory(root, root.document)\r\n  }\r\n}(typeof window !== \"undefined\" ? window : this, function(window, document) {\r\n\r\n// Find global reference - uses 'this' by default when available,\r\n// falls back to 'window' otherwise (for bundlers like Webpack)\r\nvar globalRef = (typeof this !== \"undefined\") ? this : window;\r\n\r\n// The main wrapping element\r\nvar SVG = globalRef.SVG = function(element) {\r\n  if (SVG.supported) {\r\n    element = new SVG.Doc(element)\r\n\r\n    if(!SVG.parser.draw)\r\n      SVG.prepare()\r\n\r\n    return element\r\n  }\r\n}\r\n\r\n// Default namespaces\r\nSVG.ns    = 'http://www.w3.org/2000/svg'\r\nSVG.xmlns = 'http://www.w3.org/2000/xmlns/'\r\nSVG.xlink = 'http://www.w3.org/1999/xlink'\r\nSVG.svgjs = 'http://svgjs.com/svgjs'\r\n\r\n// Svg support test\r\nSVG.supported = (function() {\r\n  return !! document.createElementNS &&\r\n         !! document.createElementNS(SVG.ns,'svg').createSVGRect\r\n})()\r\n\r\n// Don't bother to continue if SVG is not supported\r\nif (!SVG.supported) return false\r\n\r\n// Element id sequence\r\nSVG.did  = 1000\r\n\r\n// Get next named element id\r\nSVG.eid = function(name) {\r\n  return 'Svgjs' + capitalize(name) + (SVG.did++)\r\n}\r\n\r\n// Method for element creation\r\nSVG.create = function(name) {\r\n  // create element\r\n  var element = document.createElementNS(this.ns, name)\r\n\r\n  // apply unique id\r\n  element.setAttribute('id', this.eid(name))\r\n\r\n  return element\r\n}\r\n\r\n// Method for extending objects\r\nSVG.extend = function() {\r\n  var modules, methods, key, i\r\n\r\n  // Get list of modules\r\n  modules = [].slice.call(arguments)\r\n\r\n  // Get object with extensions\r\n  methods = modules.pop()\r\n\r\n  for (i = modules.length - 1; i >= 0; i--)\r\n    if (modules[i])\r\n      for (key in methods)\r\n        modules[i].prototype[key] = methods[key]\r\n\r\n  // Make sure SVG.Set inherits any newly added methods\r\n  if (SVG.Set && SVG.Set.inherit)\r\n    SVG.Set.inherit()\r\n}\r\n\r\n// Invent new element\r\nSVG.invent = function(config) {\r\n  // Create element initializer\r\n  var initializer = typeof config.create == 'function' ?\r\n    config.create :\r\n    function() {\r\n      this.constructor.call(this, SVG.create(config.create))\r\n    }\r\n\r\n  // Inherit prototype\r\n  if (config.inherit)\r\n    initializer.prototype = new config.inherit\r\n\r\n  // Extend with methods\r\n  if (config.extend)\r\n    SVG.extend(initializer, config.extend)\r\n\r\n  // Attach construct method to parent\r\n  if (config.construct)\r\n    SVG.extend(config.parent || SVG.Container, config.construct)\r\n\r\n  return initializer\r\n}\r\n\r\n// Adopt existing svg elements\r\nSVG.adopt = function(node) {\r\n  // check for presence of node\r\n  if (!node) return null\r\n\r\n  // make sure a node isn't already adopted\r\n  if (node.instance) return node.instance\r\n\r\n  // initialize variables\r\n  var element\r\n\r\n  // adopt with element-specific settings\r\n  if (node.nodeName == 'svg')\r\n    element = node.parentNode instanceof window.SVGElement ? new SVG.Nested : new SVG.Doc\r\n  else if (node.nodeName == 'linearGradient')\r\n    element = new SVG.Gradient('linear')\r\n  else if (node.nodeName == 'radialGradient')\r\n    element = new SVG.Gradient('radial')\r\n  else if (SVG[capitalize(node.nodeName)])\r\n    element = new SVG[capitalize(node.nodeName)]\r\n  else\r\n    element = new SVG.Element(node)\r\n\r\n  // ensure references\r\n  element.type  = node.nodeName\r\n  element.node  = node\r\n  node.instance = element\r\n\r\n  // SVG.Class specific preparations\r\n  if (element instanceof SVG.Doc)\r\n    element.namespace().defs()\r\n\r\n  // pull svgjs data from the dom (getAttributeNS doesn't work in html5)\r\n  element.setData(JSON.parse(node.getAttribute('svgjs:data')) || {})\r\n\r\n  return element\r\n}\r\n\r\n// Initialize parsing element\r\nSVG.prepare = function() {\r\n  // Select document body and create invisible svg element\r\n  var body = document.getElementsByTagName('body')[0]\r\n    , draw = (body ? new SVG.Doc(body) : SVG.adopt(document.documentElement).nested()).size(2, 0)\r\n\r\n  // Create parser object\r\n  SVG.parser = {\r\n    body: body || document.documentElement\r\n  , draw: draw.style('opacity:0;position:absolute;left:-100%;top:-100%;overflow:hidden').attr('focusable', 'false').node\r\n  , poly: draw.polyline().node\r\n  , path: draw.path().node\r\n  , native: SVG.create('svg')\r\n  }\r\n}\r\n\r\nSVG.parser = {\r\n  native: SVG.create('svg')\r\n}\r\n\r\ndocument.addEventListener('DOMContentLoaded', function() {\r\n  if(!SVG.parser.draw)\r\n    SVG.prepare()\r\n}, false)\r\n\n// Storage for regular expressions\r\nSVG.regex = {\r\n  // Parse unit value\r\n  numberAndUnit:    /^([+-]?(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?)([a-z%]*)$/i\r\n\r\n  // Parse hex value\r\n, hex:              /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i\r\n\r\n  // Parse rgb value\r\n, rgb:              /rgb\\((\\d+),(\\d+),(\\d+)\\)/\r\n\r\n  // Parse reference id\r\n, reference:        /#([a-z0-9\\-_]+)/i\r\n\r\n  // splits a transformation chain\r\n, transforms:       /\\)\\s*,?\\s*/\r\n\r\n  // Whitespace\r\n, whitespace:       /\\s/g\r\n\r\n  // Test hex value\r\n, isHex:            /^#[a-f0-9]{3,6}$/i\r\n\r\n  // Test rgb value\r\n, isRgb:            /^rgb\\(/\r\n\r\n  // Test css declaration\r\n, isCss:            /[^:]+:[^;]+;?/\r\n\r\n  // Test for blank string\r\n, isBlank:          /^(\\s+)?$/\r\n\r\n  // Test for numeric string\r\n, isNumber:         /^[+-]?(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i\r\n\r\n  // Test for percent value\r\n, isPercent:        /^-?[\\d\\.]+%$/\r\n\r\n  // Test for image url\r\n, isImage:          /\\.(jpg|jpeg|png|gif|svg)(\\?[^=]+.*)?/i\r\n\r\n  // split at whitespace and comma\r\n, delimiter:        /[\\s,]+/\r\n\r\n  // The following regex are used to parse the d attribute of a path\r\n\r\n  // Matches all hyphens which are not after an exponent\r\n, hyphen:           /([^e])\\-/gi\r\n\r\n  // Replaces and tests for all path letters\r\n, pathLetters:      /[MLHVCSQTAZ]/gi\r\n\r\n  // yes we need this one, too\r\n, isPathLetter:     /[MLHVCSQTAZ]/i\r\n\r\n  // matches 0.154.23.45\r\n, numbersWithDots:  /((\\d?\\.\\d+(?:e[+-]?\\d+)?)((?:\\.\\d+(?:e[+-]?\\d+)?)+))+/gi\r\n\r\n  // matches .\r\n, dots:             /\\./g\r\n}\r\n\nSVG.utils = {\r\n  // Map function\r\n  map: function(array, block) {\r\n    var i\r\n      , il = array.length\r\n      , result = []\r\n\r\n    for (i = 0; i < il; i++)\r\n      result.push(block(array[i]))\r\n\r\n    return result\r\n  }\r\n\r\n  // Filter function\r\n, filter: function(array, block) {\r\n    var i\r\n      , il = array.length\r\n      , result = []\r\n\r\n    for (i = 0; i < il; i++)\r\n      if (block(array[i]))\r\n        result.push(array[i])\r\n\r\n    return result\r\n  }\r\n\r\n  // Degrees to radians\r\n, radians: function(d) {\r\n    return d % 360 * Math.PI / 180\r\n  }\r\n\r\n  // Radians to degrees\r\n, degrees: function(r) {\r\n    return r * 180 / Math.PI % 360\r\n  }\r\n\r\n, filterSVGElements: function(nodes) {\r\n    return this.filter( nodes, function(el) { return el instanceof window.SVGElement })\r\n  }\r\n\r\n}\n\r\nSVG.defaults = {\r\n  // Default attribute values\r\n  attrs: {\r\n    // fill and stroke\r\n    'fill-opacity':     1\r\n  , 'stroke-opacity':   1\r\n  , 'stroke-width':     0\r\n  , 'stroke-linejoin':  'miter'\r\n  , 'stroke-linecap':   'butt'\r\n  , fill:               '#000000'\r\n  , stroke:             '#000000'\r\n  , opacity:            1\r\n    // position\r\n  , x:                  0\r\n  , y:                  0\r\n  , cx:                 0\r\n  , cy:                 0\r\n    // size\r\n  , width:              0\r\n  , height:             0\r\n    // radius\r\n  , r:                  0\r\n  , rx:                 0\r\n  , ry:                 0\r\n    // gradient\r\n  , offset:             0\r\n  , 'stop-opacity':     1\r\n  , 'stop-color':       '#000000'\r\n    // text\r\n  , 'font-size':        16\r\n  , 'font-family':      'Helvetica, Arial, sans-serif'\r\n  , 'text-anchor':      'start'\r\n  }\r\n\r\n}\n// Module for color convertions\r\nSVG.Color = function(color) {\r\n  var match\r\n\r\n  // initialize defaults\r\n  this.r = 0\r\n  this.g = 0\r\n  this.b = 0\r\n\r\n  if(!color) return\r\n\r\n  // parse color\r\n  if (typeof color === 'string') {\r\n    if (SVG.regex.isRgb.test(color)) {\r\n      // get rgb values\r\n      match = SVG.regex.rgb.exec(color.replace(SVG.regex.whitespace,''))\r\n\r\n      // parse numeric values\r\n      this.r = parseInt(match[1])\r\n      this.g = parseInt(match[2])\r\n      this.b = parseInt(match[3])\r\n\r\n    } else if (SVG.regex.isHex.test(color)) {\r\n      // get hex values\r\n      match = SVG.regex.hex.exec(fullHex(color))\r\n\r\n      // parse numeric values\r\n      this.r = parseInt(match[1], 16)\r\n      this.g = parseInt(match[2], 16)\r\n      this.b = parseInt(match[3], 16)\r\n\r\n    }\r\n\r\n  } else if (typeof color === 'object') {\r\n    this.r = color.r\r\n    this.g = color.g\r\n    this.b = color.b\r\n\r\n  }\r\n\r\n}\r\n\r\nSVG.extend(SVG.Color, {\r\n  // Default to hex conversion\r\n  toString: function() {\r\n    return this.toHex()\r\n  }\r\n  // Build hex value\r\n, toHex: function() {\r\n    return '#'\r\n      + compToHex(this.r)\r\n      + compToHex(this.g)\r\n      + compToHex(this.b)\r\n  }\r\n  // Build rgb value\r\n, toRgb: function() {\r\n    return 'rgb(' + [this.r, this.g, this.b].join() + ')'\r\n  }\r\n  // Calculate true brightness\r\n, brightness: function() {\r\n    return (this.r / 255 * 0.30)\r\n         + (this.g / 255 * 0.59)\r\n         + (this.b / 255 * 0.11)\r\n  }\r\n  // Make color morphable\r\n, morph: function(color) {\r\n    this.destination = new SVG.Color(color)\r\n\r\n    return this\r\n  }\r\n  // Get morphed color at given position\r\n, at: function(pos) {\r\n    // make sure a destination is defined\r\n    if (!this.destination) return this\r\n\r\n    // normalise pos\r\n    pos = pos < 0 ? 0 : pos > 1 ? 1 : pos\r\n\r\n    // generate morphed color\r\n    return new SVG.Color({\r\n      r: ~~(this.r + (this.destination.r - this.r) * pos)\r\n    , g: ~~(this.g + (this.destination.g - this.g) * pos)\r\n    , b: ~~(this.b + (this.destination.b - this.b) * pos)\r\n    })\r\n  }\r\n\r\n})\r\n\r\n// Testers\r\n\r\n// Test if given value is a color string\r\nSVG.Color.test = function(color) {\r\n  color += ''\r\n  return SVG.regex.isHex.test(color)\r\n      || SVG.regex.isRgb.test(color)\r\n}\r\n\r\n// Test if given value is a rgb object\r\nSVG.Color.isRgb = function(color) {\r\n  return color && typeof color.r == 'number'\r\n               && typeof color.g == 'number'\r\n               && typeof color.b == 'number'\r\n}\r\n\r\n// Test if given value is a color\r\nSVG.Color.isColor = function(color) {\r\n  return SVG.Color.isRgb(color) || SVG.Color.test(color)\r\n}\n// Module for array conversion\r\nSVG.Array = function(array, fallback) {\r\n  array = (array || []).valueOf()\r\n\r\n  // if array is empty and fallback is provided, use fallback\r\n  if (array.length == 0 && fallback)\r\n    array = fallback.valueOf()\r\n\r\n  // parse array\r\n  this.value = this.parse(array)\r\n}\r\n\r\nSVG.extend(SVG.Array, {\r\n  // Make array morphable\r\n  morph: function(array) {\r\n    this.destination = this.parse(array)\r\n\r\n    // normalize length of arrays\r\n    if (this.value.length != this.destination.length) {\r\n      var lastValue       = this.value[this.value.length - 1]\r\n        , lastDestination = this.destination[this.destination.length - 1]\r\n\r\n      while(this.value.length > this.destination.length)\r\n        this.destination.push(lastDestination)\r\n      while(this.value.length < this.destination.length)\r\n        this.value.push(lastValue)\r\n    }\r\n\r\n    return this\r\n  }\r\n  // Clean up any duplicate points\r\n, settle: function() {\r\n    // find all unique values\r\n    for (var i = 0, il = this.value.length, seen = []; i < il; i++)\r\n      if (seen.indexOf(this.value[i]) == -1)\r\n        seen.push(this.value[i])\r\n\r\n    // set new value\r\n    return this.value = seen\r\n  }\r\n  // Get morphed array at given position\r\n, at: function(pos) {\r\n    // make sure a destination is defined\r\n    if (!this.destination) return this\r\n\r\n    // generate morphed array\r\n    for (var i = 0, il = this.value.length, array = []; i < il; i++)\r\n      array.push(this.value[i] + (this.destination[i] - this.value[i]) * pos)\r\n\r\n    return new SVG.Array(array)\r\n  }\r\n  // Convert array to string\r\n, toString: function() {\r\n    return this.value.join(' ')\r\n  }\r\n  // Real value\r\n, valueOf: function() {\r\n    return this.value\r\n  }\r\n  // Parse whitespace separated string\r\n, parse: function(array) {\r\n    array = array.valueOf()\r\n\r\n    // if already is an array, no need to parse it\r\n    if (Array.isArray(array)) return array\r\n\r\n    return this.split(array)\r\n  }\r\n  // Strip unnecessary whitespace\r\n, split: function(string) {\r\n    return string.trim().split(SVG.regex.delimiter).map(parseFloat)\r\n  }\r\n  // Reverse array\r\n, reverse: function() {\r\n    this.value.reverse()\r\n\r\n    return this\r\n  }\r\n, clone: function() {\r\n    var clone = new this.constructor()\r\n    clone.value = array_clone(this.value)\r\n    return clone\r\n  }\r\n})\n// Poly points array\r\nSVG.PointArray = function(array, fallback) {\r\n  SVG.Array.call(this, array, fallback || [[0,0]])\r\n}\r\n\r\n// Inherit from SVG.Array\r\nSVG.PointArray.prototype = new SVG.Array\r\nSVG.PointArray.prototype.constructor = SVG.PointArray\r\n\r\nSVG.extend(SVG.PointArray, {\r\n  // Convert array to string\r\n  toString: function() {\r\n    // convert to a poly point string\r\n    for (var i = 0, il = this.value.length, array = []; i < il; i++)\r\n      array.push(this.value[i].join(','))\r\n\r\n    return array.join(' ')\r\n  }\r\n  // Convert array to line object\r\n, toLine: function() {\r\n    return {\r\n      x1: this.value[0][0]\r\n    , y1: this.value[0][1]\r\n    , x2: this.value[1][0]\r\n    , y2: this.value[1][1]\r\n    }\r\n  }\r\n  // Get morphed array at given position\r\n, at: function(pos) {\r\n    // make sure a destination is defined\r\n    if (!this.destination) return this\r\n\r\n    // generate morphed point string\r\n    for (var i = 0, il = this.value.length, array = []; i < il; i++)\r\n      array.push([\r\n        this.value[i][0] + (this.destination[i][0] - this.value[i][0]) * pos\r\n      , this.value[i][1] + (this.destination[i][1] - this.value[i][1]) * pos\r\n      ])\r\n\r\n    return new SVG.PointArray(array)\r\n  }\r\n  // Parse point string and flat array\r\n, parse: function(array) {\r\n    var points = []\r\n\r\n    array = array.valueOf()\r\n\r\n    // if it is an array\r\n    if (Array.isArray(array)) {\r\n      // and it is not flat, there is no need to parse it\r\n      if(Array.isArray(array[0])) {\r\n        // make sure to use a clone\r\n        return array.map(function (el) { return el.slice() })\r\n      } else if (array[0].x != null){\r\n        // allow point objects to be passed\r\n        return array.map(function (el) { return [el.x, el.y] })\r\n      }\r\n    } else { // Else, it is considered as a string\r\n      // parse points\r\n      array = array.trim().split(SVG.regex.delimiter).map(parseFloat)\r\n    }\r\n\r\n    // validate points - https://svgwg.org/svg2-draft/shapes.html#DataTypePoints\r\n    // Odd number of coordinates is an error. In such cases, drop the last odd coordinate.\r\n    if (array.length % 2 !== 0) array.pop()\r\n\r\n    // wrap points in two-tuples and parse points as floats\r\n    for(var i = 0, len = array.length; i < len; i = i + 2)\r\n      points.push([ array[i], array[i+1] ])\r\n\r\n    return points\r\n  }\r\n  // Move point string\r\n, move: function(x, y) {\r\n    var box = this.bbox()\r\n\r\n    // get relative offset\r\n    x -= box.x\r\n    y -= box.y\r\n\r\n    // move every point\r\n    if (!isNaN(x) && !isNaN(y))\r\n      for (var i = this.value.length - 1; i >= 0; i--)\r\n        this.value[i] = [this.value[i][0] + x, this.value[i][1] + y]\r\n\r\n    return this\r\n  }\r\n  // Resize poly string\r\n, size: function(width, height) {\r\n    var i, box = this.bbox()\r\n\r\n    // recalculate position of all points according to new size\r\n    for (i = this.value.length - 1; i >= 0; i--) {\r\n      if(box.width) this.value[i][0] = ((this.value[i][0] - box.x) * width)  / box.width  + box.x\r\n      if(box.height) this.value[i][1] = ((this.value[i][1] - box.y) * height) / box.height + box.y\r\n    }\r\n\r\n    return this\r\n  }\r\n  // Get bounding box of points\r\n, bbox: function() {\r\n    SVG.parser.poly.setAttribute('points', this.toString())\r\n\r\n    return SVG.parser.poly.getBBox()\r\n  }\r\n})\r\n\nvar pathHandlers = {\r\n  M: function(c, p, p0) {\r\n    p.x = p0.x = c[0]\r\n    p.y = p0.y = c[1]\r\n\r\n    return ['M', p.x, p.y]\r\n  },\r\n  L: function(c, p) {\r\n    p.x = c[0]\r\n    p.y = c[1]\r\n    return ['L', c[0], c[1]]\r\n  },\r\n  H: function(c, p) {\r\n    p.x = c[0]\r\n    return ['H', c[0]]\r\n  },\r\n  V: function(c, p) {\r\n    p.y = c[0]\r\n    return ['V', c[0]]\r\n  },\r\n  C: function(c, p) {\r\n    p.x = c[4]\r\n    p.y = c[5]\r\n    return ['C', c[0], c[1], c[2], c[3], c[4], c[5]]\r\n  },\r\n  S: function(c, p) {\r\n    p.x = c[2]\r\n    p.y = c[3]\r\n    return ['S', c[0], c[1], c[2], c[3]]\r\n  },\r\n  Q: function(c, p) {\r\n    p.x = c[2]\r\n    p.y = c[3]\r\n    return ['Q', c[0], c[1], c[2], c[3]]\r\n  },\r\n  T: function(c, p) {\r\n    p.x = c[0]\r\n    p.y = c[1]\r\n    return ['T', c[0], c[1]]\r\n  },\r\n  Z: function(c, p, p0) {\r\n    p.x = p0.x\r\n    p.y = p0.y\r\n    return ['Z']\r\n  },\r\n  A: function(c, p) {\r\n    p.x = c[5]\r\n    p.y = c[6]\r\n    return ['A', c[0], c[1], c[2], c[3], c[4], c[5], c[6]]\r\n  }\r\n}\r\n\r\nvar mlhvqtcsa = 'mlhvqtcsaz'.split('')\r\n\r\nfor(var i = 0, il = mlhvqtcsa.length; i < il; ++i){\r\n  pathHandlers[mlhvqtcsa[i]] = (function(i){\r\n    return function(c, p, p0) {\r\n      if(i == 'H') c[0] = c[0] + p.x\r\n      else if(i == 'V') c[0] = c[0] + p.y\r\n      else if(i == 'A'){\r\n        c[5] = c[5] + p.x,\r\n        c[6] = c[6] + p.y\r\n      }\r\n      else\r\n        for(var j = 0, jl = c.length; j < jl; ++j) {\r\n          c[j] = c[j] + (j%2 ? p.y : p.x)\r\n        }\r\n\r\n      return pathHandlers[i](c, p, p0)\r\n    }\r\n  })(mlhvqtcsa[i].toUpperCase())\r\n}\r\n\r\n// Path points array\r\nSVG.PathArray = function(array, fallback) {\r\n  SVG.Array.call(this, array, fallback || [['M', 0, 0]])\r\n}\r\n\r\n// Inherit from SVG.Array\r\nSVG.PathArray.prototype = new SVG.Array\r\nSVG.PathArray.prototype.constructor = SVG.PathArray\r\n\r\nSVG.extend(SVG.PathArray, {\r\n  // Convert array to string\r\n  toString: function() {\r\n    return arrayToString(this.value)\r\n  }\r\n  // Move path string\r\n, move: function(x, y) {\r\n    // get bounding box of current situation\r\n    var box = this.bbox()\r\n\r\n    // get relative offset\r\n    x -= box.x\r\n    y -= box.y\r\n\r\n    if (!isNaN(x) && !isNaN(y)) {\r\n      // move every point\r\n      for (var l, i = this.value.length - 1; i >= 0; i--) {\r\n        l = this.value[i][0]\r\n\r\n        if (l == 'M' || l == 'L' || l == 'T')  {\r\n          this.value[i][1] += x\r\n          this.value[i][2] += y\r\n\r\n        } else if (l == 'H')  {\r\n          this.value[i][1] += x\r\n\r\n        } else if (l == 'V')  {\r\n          this.value[i][1] += y\r\n\r\n        } else if (l == 'C' || l == 'S' || l == 'Q')  {\r\n          this.value[i][1] += x\r\n          this.value[i][2] += y\r\n          this.value[i][3] += x\r\n          this.value[i][4] += y\r\n\r\n          if (l == 'C')  {\r\n            this.value[i][5] += x\r\n            this.value[i][6] += y\r\n          }\r\n\r\n        } else if (l == 'A')  {\r\n          this.value[i][6] += x\r\n          this.value[i][7] += y\r\n        }\r\n\r\n      }\r\n    }\r\n\r\n    return this\r\n  }\r\n  // Resize path string\r\n, size: function(width, height) {\r\n    // get bounding box of current situation\r\n    var i, l, box = this.bbox()\r\n\r\n    // recalculate position of all points according to new size\r\n    for (i = this.value.length - 1; i >= 0; i--) {\r\n      l = this.value[i][0]\r\n\r\n      if (l == 'M' || l == 'L' || l == 'T')  {\r\n        this.value[i][1] = ((this.value[i][1] - box.x) * width)  / box.width  + box.x\r\n        this.value[i][2] = ((this.value[i][2] - box.y) * height) / box.height + box.y\r\n\r\n      } else if (l == 'H')  {\r\n        this.value[i][1] = ((this.value[i][1] - box.x) * width)  / box.width  + box.x\r\n\r\n      } else if (l == 'V')  {\r\n        this.value[i][1] = ((this.value[i][1] - box.y) * height) / box.height + box.y\r\n\r\n      } else if (l == 'C' || l == 'S' || l == 'Q')  {\r\n        this.value[i][1] = ((this.value[i][1] - box.x) * width)  / box.width  + box.x\r\n        this.value[i][2] = ((this.value[i][2] - box.y) * height) / box.height + box.y\r\n        this.value[i][3] = ((this.value[i][3] - box.x) * width)  / box.width  + box.x\r\n        this.value[i][4] = ((this.value[i][4] - box.y) * height) / box.height + box.y\r\n\r\n        if (l == 'C')  {\r\n          this.value[i][5] = ((this.value[i][5] - box.x) * width)  / box.width  + box.x\r\n          this.value[i][6] = ((this.value[i][6] - box.y) * height) / box.height + box.y\r\n        }\r\n\r\n      } else if (l == 'A')  {\r\n        // resize radii\r\n        this.value[i][1] = (this.value[i][1] * width)  / box.width\r\n        this.value[i][2] = (this.value[i][2] * height) / box.height\r\n\r\n        // move position values\r\n        this.value[i][6] = ((this.value[i][6] - box.x) * width)  / box.width  + box.x\r\n        this.value[i][7] = ((this.value[i][7] - box.y) * height) / box.height + box.y\r\n      }\r\n\r\n    }\r\n\r\n    return this\r\n  }\r\n  // Test if the passed path array use the same path data commands as this path array\r\n, equalCommands: function(pathArray) {\r\n    var i, il, equalCommands\r\n\r\n    pathArray = new SVG.PathArray(pathArray)\r\n\r\n    equalCommands = this.value.length === pathArray.value.length\r\n    for(i = 0, il = this.value.length; equalCommands && i < il; i++) {\r\n      equalCommands = this.value[i][0] === pathArray.value[i][0]\r\n    }\r\n\r\n    return equalCommands\r\n  }\r\n  // Make path array morphable\r\n, morph: function(pathArray) {\r\n    pathArray = new SVG.PathArray(pathArray)\r\n\r\n    if(this.equalCommands(pathArray)) {\r\n      this.destination = pathArray\r\n    } else {\r\n      this.destination = null\r\n    }\r\n\r\n    return this\r\n  }\r\n  // Get morphed path array at given position\r\n, at: function(pos) {\r\n    // make sure a destination is defined\r\n    if (!this.destination) return this\r\n\r\n    var sourceArray = this.value\r\n      , destinationArray = this.destination.value\r\n      , array = [], pathArray = new SVG.PathArray()\r\n      , i, il, j, jl\r\n\r\n    // Animate has specified in the SVG spec\r\n    // See: https://www.w3.org/TR/SVG11/paths.html#PathElement\r\n    for (i = 0, il = sourceArray.length; i < il; i++) {\r\n      array[i] = [sourceArray[i][0]]\r\n      for(j = 1, jl = sourceArray[i].length; j < jl; j++) {\r\n        array[i][j] = sourceArray[i][j] + (destinationArray[i][j] - sourceArray[i][j]) * pos\r\n      }\r\n      // For the two flags of the elliptical arc command, the SVG spec say:\r\n      // Flags and booleans are interpolated as fractions between zero and one, with any non-zero value considered to be a value of one/true\r\n      // Elliptical arc command as an array followed by corresponding indexes:\r\n      // ['A', rx, ry, x-axis-rotation, large-arc-flag, sweep-flag, x, y]\r\n      //   0    1   2        3                 4             5      6  7\r\n      if(array[i][0] === 'A') {\r\n        array[i][4] = +(array[i][4] != 0)\r\n        array[i][5] = +(array[i][5] != 0)\r\n      }\r\n    }\r\n\r\n    // Directly modify the value of a path array, this is done this way for performance\r\n    pathArray.value = array\r\n    return pathArray\r\n  }\r\n  // Absolutize and parse path to array\r\n, parse: function(array) {\r\n    // if it's already a patharray, no need to parse it\r\n    if (array instanceof SVG.PathArray) return array.valueOf()\r\n\r\n    // prepare for parsing\r\n    var i, x0, y0, s, seg, arr\r\n      , x = 0\r\n      , y = 0\r\n      , paramCnt = { 'M':2, 'L':2, 'H':1, 'V':1, 'C':6, 'S':4, 'Q':4, 'T':2, 'A':7, 'Z':0 }\r\n\r\n    if(typeof array == 'string'){\r\n\r\n      array = array\r\n        .replace(SVG.regex.numbersWithDots, pathRegReplace) // convert 45.123.123 to 45.123 .123\r\n        .replace(SVG.regex.pathLetters, ' $& ') // put some room between letters and numbers\r\n        .replace(SVG.regex.hyphen, '$1 -')      // add space before hyphen\r\n        .trim()                                 // trim\r\n        .split(SVG.regex.delimiter)   // split into array\r\n\r\n    }else{\r\n      array = array.reduce(function(prev, curr){\r\n        return [].concat.call(prev, curr)\r\n      }, [])\r\n    }\r\n\r\n    // array now is an array containing all parts of a path e.g. ['M', '0', '0', 'L', '30', '30' ...]\r\n    var arr = []\r\n      , p = new SVG.Point()\r\n      , p0 = new SVG.Point()\r\n      , index = 0\r\n      , len = array.length\r\n\r\n    do{\r\n      // Test if we have a path letter\r\n      if(SVG.regex.isPathLetter.test(array[index])){\r\n        s = array[index]\r\n        ++index\r\n      // If last letter was a move command and we got no new, it defaults to [L]ine\r\n      }else if(s == 'M'){\r\n        s = 'L'\r\n      }else if(s == 'm'){\r\n        s = 'l'\r\n      }\r\n\r\n      arr.push(pathHandlers[s].call(null,\r\n          array.slice(index, (index = index + paramCnt[s.toUpperCase()])).map(parseFloat),\r\n          p, p0\r\n        )\r\n      )\r\n\r\n    }while(len > index)\r\n\r\n    return arr\r\n\r\n  }\r\n  // Get bounding box of path\r\n, bbox: function() {\r\n    SVG.parser.path.setAttribute('d', this.toString())\r\n\r\n    return SVG.parser.path.getBBox()\r\n  }\r\n\r\n})\r\n\n// Module for unit convertions\r\nSVG.Number = SVG.invent({\r\n  // Initialize\r\n  create: function(value, unit) {\r\n    // initialize defaults\r\n    this.value = 0\r\n    this.unit  = unit || ''\r\n\r\n    // parse value\r\n    if (typeof value === 'number') {\r\n      // ensure a valid numeric value\r\n      this.value = isNaN(value) ? 0 : !isFinite(value) ? (value < 0 ? -3.4e+38 : +3.4e+38) : value\r\n\r\n    } else if (typeof value === 'string') {\r\n      unit = value.match(SVG.regex.numberAndUnit)\r\n\r\n      if (unit) {\r\n        // make value numeric\r\n        this.value = parseFloat(unit[1])\r\n\r\n        // normalize\r\n        if (unit[5] == '%')\r\n          this.value /= 100\r\n        else if (unit[5] == 's')\r\n          this.value *= 1000\r\n\r\n        // store unit\r\n        this.unit = unit[5]\r\n      }\r\n\r\n    } else {\r\n      if (value instanceof SVG.Number) {\r\n        this.value = value.valueOf()\r\n        this.unit  = value.unit\r\n      }\r\n    }\r\n\r\n  }\r\n  // Add methods\r\n, extend: {\r\n    // Stringalize\r\n    toString: function() {\r\n      return (\r\n        this.unit == '%' ?\r\n          ~~(this.value * 1e8) / 1e6:\r\n        this.unit == 's' ?\r\n          this.value / 1e3 :\r\n          this.value\r\n      ) + this.unit\r\n    }\r\n  , toJSON: function() {\r\n      return this.toString()\r\n    }\r\n  , // Convert to primitive\r\n    valueOf: function() {\r\n      return this.value\r\n    }\r\n    // Add number\r\n  , plus: function(number) {\r\n      number = new SVG.Number(number)\r\n      return new SVG.Number(this + number, this.unit || number.unit)\r\n    }\r\n    // Subtract number\r\n  , minus: function(number) {\r\n      number = new SVG.Number(number)\r\n      return new SVG.Number(this - number, this.unit || number.unit)\r\n    }\r\n    // Multiply number\r\n  , times: function(number) {\r\n      number = new SVG.Number(number)\r\n      return new SVG.Number(this * number, this.unit || number.unit)\r\n    }\r\n    // Divide number\r\n  , divide: function(number) {\r\n      number = new SVG.Number(number)\r\n      return new SVG.Number(this / number, this.unit || number.unit)\r\n    }\r\n    // Convert to different unit\r\n  , to: function(unit) {\r\n      var number = new SVG.Number(this)\r\n\r\n      if (typeof unit === 'string')\r\n        number.unit = unit\r\n\r\n      return number\r\n    }\r\n    // Make number morphable\r\n  , morph: function(number) {\r\n      this.destination = new SVG.Number(number)\r\n\r\n      if(number.relative) {\r\n        this.destination.value += this.value\r\n      }\r\n\r\n      return this\r\n    }\r\n    // Get morphed number at given position\r\n  , at: function(pos) {\r\n      // Make sure a destination is defined\r\n      if (!this.destination) return this\r\n\r\n      // Generate new morphed number\r\n      return new SVG.Number(this.destination)\r\n          .minus(this)\r\n          .times(pos)\r\n          .plus(this)\r\n    }\r\n\r\n  }\r\n})\r\n\n\r\nSVG.Element = SVG.invent({\r\n  // Initialize node\r\n  create: function(node) {\r\n    // make stroke value accessible dynamically\r\n    this._stroke = SVG.defaults.attrs.stroke\r\n    this._event = null\r\n    this._events = {}\r\n\r\n    // initialize data object\r\n    this.dom = {}\r\n\r\n    // create circular reference\r\n    if (this.node = node) {\r\n      this.type = node.nodeName\r\n      this.node.instance = this\r\n      this._events = node._events || {}\r\n\r\n      // store current attribute value\r\n      this._stroke = node.getAttribute('stroke') || this._stroke\r\n    }\r\n  }\r\n\r\n  // Add class methods\r\n, extend: {\r\n    // Move over x-axis\r\n    x: function(x) {\r\n      return this.attr('x', x)\r\n    }\r\n    // Move over y-axis\r\n  , y: function(y) {\r\n      return this.attr('y', y)\r\n    }\r\n    // Move by center over x-axis\r\n  , cx: function(x) {\r\n      return x == null ? this.x() + this.width() / 2 : this.x(x - this.width() / 2)\r\n    }\r\n    // Move by center over y-axis\r\n  , cy: function(y) {\r\n      return y == null ? this.y() + this.height() / 2 : this.y(y - this.height() / 2)\r\n    }\r\n    // Move element to given x and y values\r\n  , move: function(x, y) {\r\n      return this.x(x).y(y)\r\n    }\r\n    // Move element by its center\r\n  , center: function(x, y) {\r\n      return this.cx(x).cy(y)\r\n    }\r\n    // Set width of element\r\n  , width: function(width) {\r\n      return this.attr('width', width)\r\n    }\r\n    // Set height of element\r\n  , height: function(height) {\r\n      return this.attr('height', height)\r\n    }\r\n    // Set element size to given width and height\r\n  , size: function(width, height) {\r\n      var p = proportionalSize(this, width, height)\r\n\r\n      return this\r\n        .width(new SVG.Number(p.width))\r\n        .height(new SVG.Number(p.height))\r\n    }\r\n    // Clone element\r\n  , clone: function(parent) {\r\n      // write dom data to the dom so the clone can pickup the data\r\n      this.writeDataToDom()\r\n\r\n      // clone element and assign new id\r\n      var clone = assignNewId(this.node.cloneNode(true))\r\n\r\n      // insert the clone in the given parent or after myself\r\n      if(parent) parent.add(clone)\r\n      else this.after(clone)\r\n\r\n      return clone\r\n    }\r\n    // Remove element\r\n  , remove: function() {\r\n      if (this.parent())\r\n        this.parent().removeElement(this)\r\n\r\n      return this\r\n    }\r\n    // Replace element\r\n  , replace: function(element) {\r\n      this.after(element).remove()\r\n\r\n      return element\r\n    }\r\n    // Add element to given container and return self\r\n  , addTo: function(parent) {\r\n      return parent.put(this)\r\n    }\r\n    // Add element to given container and return container\r\n  , putIn: function(parent) {\r\n      return parent.add(this)\r\n    }\r\n    // Get / set id\r\n  , id: function(id) {\r\n      return this.attr('id', id)\r\n    }\r\n    // Checks whether the given point inside the bounding box of the element\r\n  , inside: function(x, y) {\r\n      var box = this.bbox()\r\n\r\n      return x > box.x\r\n          && y > box.y\r\n          && x < box.x + box.width\r\n          && y < box.y + box.height\r\n    }\r\n    // Show element\r\n  , show: function() {\r\n      return this.style('display', '')\r\n    }\r\n    // Hide element\r\n  , hide: function() {\r\n      return this.style('display', 'none')\r\n    }\r\n    // Is element visible?\r\n  , visible: function() {\r\n      return this.style('display') != 'none'\r\n    }\r\n    // Return id on string conversion\r\n  , toString: function() {\r\n      return this.attr('id')\r\n    }\r\n    // Return array of classes on the node\r\n  , classes: function() {\r\n      var attr = this.attr('class')\r\n\r\n      return attr == null ? [] : attr.trim().split(SVG.regex.delimiter)\r\n    }\r\n    // Return true if class exists on the node, false otherwise\r\n  , hasClass: function(name) {\r\n      return this.classes().indexOf(name) != -1\r\n    }\r\n    // Add class to the node\r\n  , addClass: function(name) {\r\n      if (!this.hasClass(name)) {\r\n        var array = this.classes()\r\n        array.push(name)\r\n        this.attr('class', array.join(' '))\r\n      }\r\n\r\n      return this\r\n    }\r\n    // Remove class from the node\r\n  , removeClass: function(name) {\r\n      if (this.hasClass(name)) {\r\n        this.attr('class', this.classes().filter(function(c) {\r\n          return c != name\r\n        }).join(' '))\r\n      }\r\n\r\n      return this\r\n    }\r\n    // Toggle the presence of a class on the node\r\n  , toggleClass: function(name) {\r\n      return this.hasClass(name) ? this.removeClass(name) : this.addClass(name)\r\n    }\r\n    // Get referenced element form attribute value\r\n  , reference: function(attr) {\r\n      return SVG.get(this.attr(attr))\r\n    }\r\n    // Returns the parent element instance\r\n  , parent: function(type) {\r\n      var parent = this\r\n\r\n      // check for parent\r\n      if(!parent.node.parentNode) return null\r\n\r\n      // get parent element\r\n      parent = SVG.adopt(parent.node.parentNode)\r\n\r\n      if(!type) return parent\r\n\r\n      // loop trough ancestors if type is given\r\n      while(parent && parent.node instanceof window.SVGElement){\r\n        if(typeof type === 'string' ? parent.matches(type) : parent instanceof type) return parent\r\n        if(!parent.node.parentNode || parent.node.parentNode.nodeName == '#document' || parent.node.parentNode.nodeName == '#document-fragment') return null // #759, #720\r\n        parent = SVG.adopt(parent.node.parentNode)\r\n      }\r\n    }\r\n    // Get parent document\r\n  , doc: function() {\r\n      return this instanceof SVG.Doc ? this : this.parent(SVG.Doc)\r\n    }\r\n    // return array of all ancestors of given type up to the root svg\r\n  , parents: function(type) {\r\n      var parents = [], parent = this\r\n\r\n      do{\r\n        parent = parent.parent(type)\r\n        if(!parent || !parent.node) break\r\n\r\n        parents.push(parent)\r\n      } while(parent.parent)\r\n\r\n      return parents\r\n    }\r\n    // matches the element vs a css selector\r\n  , matches: function(selector){\r\n      return matches(this.node, selector)\r\n    }\r\n    // Returns the svg node to call native svg methods on it\r\n  , native: function() {\r\n      return this.node\r\n    }\r\n    // Import raw svg\r\n  , svg: function(svg) {\r\n      // create temporary holder\r\n      var well = document.createElement('svg')\r\n\r\n      // act as a setter if svg is given\r\n      if (svg && this instanceof SVG.Parent) {\r\n        // dump raw svg\r\n        well.innerHTML = '<svg>' + svg.replace(/\\n/, '').replace(/<([\\w:-]+)([^<]+?)\\/>/g, '<$1$2></$1>') + '</svg>'\r\n\r\n        // transplant nodes\r\n        for (var i = 0, il = well.firstChild.childNodes.length; i < il; i++)\r\n          this.node.appendChild(well.firstChild.firstChild)\r\n\r\n      // otherwise act as a getter\r\n      } else {\r\n        // create a wrapping svg element in case of partial content\r\n        well.appendChild(svg = document.createElement('svg'))\r\n\r\n        // write svgjs data to the dom\r\n        this.writeDataToDom()\r\n\r\n        // insert a copy of this node\r\n        svg.appendChild(this.node.cloneNode(true))\r\n\r\n        // return target element\r\n        return well.innerHTML.replace(/^<svg>/, '').replace(/<\\/svg>$/, '')\r\n      }\r\n\r\n      return this\r\n    }\r\n  // write svgjs data to the dom\r\n  , writeDataToDom: function() {\r\n\r\n      // dump variables recursively\r\n      if(this.each || this.lines){\r\n        var fn = this.each ? this : this.lines();\r\n        fn.each(function(){\r\n          this.writeDataToDom()\r\n        })\r\n      }\r\n\r\n      // remove previously set data\r\n      this.node.removeAttribute('svgjs:data')\r\n\r\n      if(Object.keys(this.dom).length)\r\n        this.node.setAttribute('svgjs:data', JSON.stringify(this.dom)) // see #428\r\n\r\n      return this\r\n    }\r\n  // set given data to the elements data property\r\n  , setData: function(o){\r\n      this.dom = o\r\n      return this\r\n    }\r\n  , is: function(obj){\r\n      return is(this, obj)\r\n    }\r\n  }\r\n})\r\n\nSVG.easing = {\r\n  '-': function(pos){return pos}\r\n, '<>':function(pos){return -Math.cos(pos * Math.PI) / 2 + 0.5}\r\n, '>': function(pos){return  Math.sin(pos * Math.PI / 2)}\r\n, '<': function(pos){return -Math.cos(pos * Math.PI / 2) + 1}\r\n}\r\n\r\nSVG.morph = function(pos){\r\n  return function(from, to) {\r\n    return new SVG.MorphObj(from, to).at(pos)\r\n  }\r\n}\r\n\r\nSVG.Situation = SVG.invent({\r\n\r\n  create: function(o){\r\n    this.init = false\r\n    this.reversed = false\r\n    this.reversing = false\r\n\r\n    this.duration = new SVG.Number(o.duration).valueOf()\r\n    this.delay = new SVG.Number(o.delay).valueOf()\r\n\r\n    this.start = +new Date() + this.delay\r\n    this.finish = this.start + this.duration\r\n    this.ease = o.ease\r\n\r\n    // this.loop is incremented from 0 to this.loops\r\n    // it is also incremented when in an infinite loop (when this.loops is true)\r\n    this.loop = 0\r\n    this.loops = false\r\n\r\n    this.animations = {\r\n      // functionToCall: [list of morphable objects]\r\n      // e.g. move: [SVG.Number, SVG.Number]\r\n    }\r\n\r\n    this.attrs = {\r\n      // holds all attributes which are not represented from a function svg.js provides\r\n      // e.g. someAttr: SVG.Number\r\n    }\r\n\r\n    this.styles = {\r\n      // holds all styles which should be animated\r\n      // e.g. fill-color: SVG.Color\r\n    }\r\n\r\n    this.transforms = [\r\n      // holds all transformations as transformation objects\r\n      // e.g. [SVG.Rotate, SVG.Translate, SVG.Matrix]\r\n    ]\r\n\r\n    this.once = {\r\n      // functions to fire at a specific position\r\n      // e.g. \"0.5\": function foo(){}\r\n    }\r\n\r\n  }\r\n\r\n})\r\n\r\n\r\nSVG.FX = SVG.invent({\r\n\r\n  create: function(element) {\r\n    this._target = element\r\n    this.situations = []\r\n    this.active = false\r\n    this.situation = null\r\n    this.paused = false\r\n    this.lastPos = 0\r\n    this.pos = 0\r\n    // The absolute position of an animation is its position in the context of its complete duration (including delay and loops)\r\n    // When performing a delay, absPos is below 0 and when performing a loop, its value is above 1\r\n    this.absPos = 0\r\n    this._speed = 1\r\n  }\r\n\r\n, extend: {\r\n\r\n    /**\r\n     * sets or returns the target of this animation\r\n     * @param o object || number In case of Object it holds all parameters. In case of number its the duration of the animation\r\n     * @param ease function || string Function which should be used for easing or easing keyword\r\n     * @param delay Number indicating the delay before the animation starts\r\n     * @return target || this\r\n     */\r\n    animate: function(o, ease, delay){\r\n\r\n      if(typeof o == 'object'){\r\n        ease = o.ease\r\n        delay = o.delay\r\n        o = o.duration\r\n      }\r\n\r\n      var situation = new SVG.Situation({\r\n        duration: o || 1000,\r\n        delay: delay || 0,\r\n        ease: SVG.easing[ease || '-'] || ease\r\n      })\r\n\r\n      this.queue(situation)\r\n\r\n      return this\r\n    }\r\n\r\n    /**\r\n     * sets a delay before the next element of the queue is called\r\n     * @param delay Duration of delay in milliseconds\r\n     * @return this.target()\r\n     */\r\n  , delay: function(delay){\r\n      // The delay is performed by an empty situation with its duration\r\n      // attribute set to the duration of the delay\r\n      var situation = new SVG.Situation({\r\n        duration: delay,\r\n        delay: 0,\r\n        ease: SVG.easing['-']\r\n      })\r\n\r\n      return this.queue(situation)\r\n    }\r\n\r\n    /**\r\n     * sets or returns the target of this animation\r\n     * @param null || target SVG.Element which should be set as new target\r\n     * @return target || this\r\n     */\r\n  , target: function(target){\r\n      if(target && target instanceof SVG.Element){\r\n        this._target = target\r\n        return this\r\n      }\r\n\r\n      return this._target\r\n    }\r\n\r\n    // returns the absolute position at a given time\r\n  , timeToAbsPos: function(timestamp){\r\n      return (timestamp - this.situation.start) / (this.situation.duration/this._speed)\r\n    }\r\n\r\n    // returns the timestamp from a given absolute positon\r\n  , absPosToTime: function(absPos){\r\n      return this.situation.duration/this._speed * absPos + this.situation.start\r\n    }\r\n\r\n    // starts the animationloop\r\n  , startAnimFrame: function(){\r\n      this.stopAnimFrame()\r\n      this.animationFrame = window.requestAnimationFrame(function(){ this.step() }.bind(this))\r\n    }\r\n\r\n    // cancels the animationframe\r\n  , stopAnimFrame: function(){\r\n      window.cancelAnimationFrame(this.animationFrame)\r\n    }\r\n\r\n    // kicks off the animation - only does something when the queue is currently not active and at least one situation is set\r\n  , start: function(){\r\n      // dont start if already started\r\n      if(!this.active && this.situation){\r\n        this.active = true\r\n        this.startCurrent()\r\n      }\r\n\r\n      return this\r\n    }\r\n\r\n    // start the current situation\r\n  , startCurrent: function(){\r\n      this.situation.start = +new Date + this.situation.delay/this._speed\r\n      this.situation.finish = this.situation.start + this.situation.duration/this._speed\r\n      return this.initAnimations().step()\r\n    }\r\n\r\n    /**\r\n     * adds a function / Situation to the animation queue\r\n     * @param fn function / situation to add\r\n     * @return this\r\n     */\r\n  , queue: function(fn){\r\n      if(typeof fn == 'function' || fn instanceof SVG.Situation)\r\n        this.situations.push(fn)\r\n\r\n      if(!this.situation) this.situation = this.situations.shift()\r\n\r\n      return this\r\n    }\r\n\r\n    /**\r\n     * pulls next element from the queue and execute it\r\n     * @return this\r\n     */\r\n  , dequeue: function(){\r\n      // stop current animation\r\n      this.stop()\r\n\r\n      // get next animation from queue\r\n      this.situation = this.situations.shift()\r\n\r\n      if(this.situation){\r\n        if(this.situation instanceof SVG.Situation) {\r\n          this.start()\r\n        } else {\r\n          // If it is not a SVG.Situation, then it is a function, we execute it\r\n          this.situation.call(this)\r\n        }\r\n      }\r\n\r\n      return this\r\n    }\r\n\r\n    // updates all animations to the current state of the element\r\n    // this is important when one property could be changed from another property\r\n  , initAnimations: function() {\r\n      var i, j, source\r\n      var s = this.situation\r\n\r\n      if(s.init) return this\r\n\r\n      for(i in s.animations){\r\n        source = this.target()[i]()\r\n\r\n        if(!Array.isArray(source)) {\r\n          source = [source]\r\n        }\r\n\r\n        if(!Array.isArray(s.animations[i])) {\r\n          s.animations[i] = [s.animations[i]]\r\n        }\r\n\r\n        //if(s.animations[i].length > source.length) {\r\n        //  source.concat = source.concat(s.animations[i].slice(source.length, s.animations[i].length))\r\n        //}\r\n\r\n        for(j = source.length; j--;) {\r\n          // The condition is because some methods return a normal number instead\r\n          // of a SVG.Number\r\n          if(s.animations[i][j] instanceof SVG.Number)\r\n            source[j] = new SVG.Number(source[j])\r\n\r\n          s.animations[i][j] = source[j].morph(s.animations[i][j])\r\n        }\r\n      }\r\n\r\n      for(i in s.attrs){\r\n        s.attrs[i] = new SVG.MorphObj(this.target().attr(i), s.attrs[i])\r\n      }\r\n\r\n      for(i in s.styles){\r\n        s.styles[i] = new SVG.MorphObj(this.target().style(i), s.styles[i])\r\n      }\r\n\r\n      s.initialTransformation = this.target().matrixify()\r\n\r\n      s.init = true\r\n      return this\r\n    }\r\n  , clearQueue: function(){\r\n      this.situations = []\r\n      return this\r\n    }\r\n  , clearCurrent: function(){\r\n      this.situation = null\r\n      return this\r\n    }\r\n    /** stops the animation immediately\r\n     * @param jumpToEnd A Boolean indicating whether to complete the current animation immediately.\r\n     * @param clearQueue A Boolean indicating whether to remove queued animation as well.\r\n     * @return this\r\n     */\r\n  , stop: function(jumpToEnd, clearQueue){\r\n      var active = this.active\r\n      this.active = false\r\n\r\n      if(clearQueue){\r\n        this.clearQueue()\r\n      }\r\n\r\n      if(jumpToEnd && this.situation){\r\n        // initialize the situation if it was not\r\n        !active && this.startCurrent()\r\n        this.atEnd()\r\n      }\r\n\r\n      this.stopAnimFrame()\r\n\r\n      return this.clearCurrent()\r\n    }\r\n\r\n    /** resets the element to the state where the current element has started\r\n     * @return this\r\n     */\r\n  , reset: function(){\r\n      if(this.situation){\r\n        var temp = this.situation\r\n        this.stop()\r\n        this.situation = temp\r\n        this.atStart()\r\n      }\r\n      return this\r\n    }\r\n\r\n    // Stop the currently-running animation, remove all queued animations, and complete all animations for the element.\r\n  , finish: function(){\r\n\r\n      this.stop(true, false)\r\n\r\n      while(this.dequeue().situation && this.stop(true, false));\r\n\r\n      this.clearQueue().clearCurrent()\r\n\r\n      return this\r\n    }\r\n\r\n    // set the internal animation pointer at the start position, before any loops, and updates the visualisation\r\n  , atStart: function() {\r\n      return this.at(0, true)\r\n    }\r\n\r\n    // set the internal animation pointer at the end position, after all the loops, and updates the visualisation\r\n  , atEnd: function() {\r\n      if (this.situation.loops === true) {\r\n        // If in a infinite loop, we end the current iteration\r\n        this.situation.loops = this.situation.loop + 1\r\n      }\r\n\r\n      if(typeof this.situation.loops == 'number') {\r\n        // If performing a finite number of loops, we go after all the loops\r\n        return this.at(this.situation.loops, true)\r\n      } else {\r\n        // If no loops, we just go at the end\r\n        return this.at(1, true)\r\n      }\r\n    }\r\n\r\n    // set the internal animation pointer to the specified position and updates the visualisation\r\n    // if isAbsPos is true, pos is treated as an absolute position\r\n  , at: function(pos, isAbsPos){\r\n      var durDivSpd = this.situation.duration/this._speed\r\n\r\n      this.absPos = pos\r\n      // If pos is not an absolute position, we convert it into one\r\n      if (!isAbsPos) {\r\n        if (this.situation.reversed) this.absPos = 1 - this.absPos\r\n        this.absPos += this.situation.loop\r\n      }\r\n\r\n      this.situation.start = +new Date - this.absPos * durDivSpd\r\n      this.situation.finish = this.situation.start + durDivSpd\r\n\r\n      return this.step(true)\r\n    }\r\n\r\n    /**\r\n     * sets or returns the speed of the animations\r\n     * @param speed null || Number The new speed of the animations\r\n     * @return Number || this\r\n     */\r\n  , speed: function(speed){\r\n      if (speed === 0) return this.pause()\r\n\r\n      if (speed) {\r\n        this._speed = speed\r\n        // We use an absolute position here so that speed can affect the delay before the animation\r\n        return this.at(this.absPos, true)\r\n      } else return this._speed\r\n    }\r\n\r\n    // Make loopable\r\n  , loop: function(times, reverse) {\r\n      var c = this.last()\r\n\r\n      // store total loops\r\n      c.loops = (times != null) ? times : true\r\n      c.loop = 0\r\n\r\n      if(reverse) c.reversing = true\r\n      return this\r\n    }\r\n\r\n    // pauses the animation\r\n  , pause: function(){\r\n      this.paused = true\r\n      this.stopAnimFrame()\r\n\r\n      return this\r\n    }\r\n\r\n    // unpause the animation\r\n  , play: function(){\r\n      if(!this.paused) return this\r\n      this.paused = false\r\n      // We use an absolute position here so that the delay before the animation can be paused\r\n      return this.at(this.absPos, true)\r\n    }\r\n\r\n    /**\r\n     * toggle or set the direction of the animation\r\n     * true sets direction to backwards while false sets it to forwards\r\n     * @param reversed Boolean indicating whether to reverse the animation or not (default: toggle the reverse status)\r\n     * @return this\r\n     */\r\n  , reverse: function(reversed){\r\n      var c = this.last()\r\n\r\n      if(typeof reversed == 'undefined') c.reversed = !c.reversed\r\n      else c.reversed = reversed\r\n\r\n      return this\r\n    }\r\n\r\n\r\n    /**\r\n     * returns a float from 0-1 indicating the progress of the current animation\r\n     * @param eased Boolean indicating whether the returned position should be eased or not\r\n     * @return number\r\n     */\r\n  , progress: function(easeIt){\r\n      return easeIt ? this.situation.ease(this.pos) : this.pos\r\n    }\r\n\r\n    /**\r\n     * adds a callback function which is called when the current animation is finished\r\n     * @param fn Function which should be executed as callback\r\n     * @return number\r\n     */\r\n  , after: function(fn){\r\n      var c = this.last()\r\n        , wrapper = function wrapper(e){\r\n            if(e.detail.situation == c){\r\n              fn.call(this, c)\r\n              this.off('finished.fx', wrapper) // prevent memory leak\r\n            }\r\n          }\r\n\r\n      this.target().on('finished.fx', wrapper)\r\n\r\n      return this._callStart()\r\n    }\r\n\r\n    // adds a callback which is called whenever one animation step is performed\r\n  , during: function(fn){\r\n      var c = this.last()\r\n        , wrapper = function(e){\r\n            if(e.detail.situation == c){\r\n              fn.call(this, e.detail.pos, SVG.morph(e.detail.pos), e.detail.eased, c)\r\n            }\r\n          }\r\n\r\n      // see above\r\n      this.target().off('during.fx', wrapper).on('during.fx', wrapper)\r\n\r\n      this.after(function(){\r\n        this.off('during.fx', wrapper)\r\n      })\r\n\r\n      return this._callStart()\r\n    }\r\n\r\n    // calls after ALL animations in the queue are finished\r\n  , afterAll: function(fn){\r\n      var wrapper = function wrapper(e){\r\n            fn.call(this)\r\n            this.off('allfinished.fx', wrapper)\r\n          }\r\n\r\n      // see above\r\n      this.target().off('allfinished.fx', wrapper).on('allfinished.fx', wrapper)\r\n\r\n      return this._callStart()\r\n    }\r\n\r\n    // calls on every animation step for all animations\r\n  , duringAll: function(fn){\r\n      var wrapper = function(e){\r\n            fn.call(this, e.detail.pos, SVG.morph(e.detail.pos), e.detail.eased, e.detail.situation)\r\n          }\r\n\r\n      this.target().off('during.fx', wrapper).on('during.fx', wrapper)\r\n\r\n      this.afterAll(function(){\r\n        this.off('during.fx', wrapper)\r\n      })\r\n\r\n      return this._callStart()\r\n    }\r\n\r\n  , last: function(){\r\n      return this.situations.length ? this.situations[this.situations.length-1] : this.situation\r\n    }\r\n\r\n    // adds one property to the animations\r\n  , add: function(method, args, type){\r\n      this.last()[type || 'animations'][method] = args\r\n      return this._callStart()\r\n    }\r\n\r\n    /** perform one step of the animation\r\n     *  @param ignoreTime Boolean indicating whether to ignore time and use position directly or recalculate position based on time\r\n     *  @return this\r\n     */\r\n  , step: function(ignoreTime){\r\n\r\n      // convert current time to an absolute position\r\n      if(!ignoreTime) this.absPos = this.timeToAbsPos(+new Date)\r\n\r\n      // This part convert an absolute position to a position\r\n      if(this.situation.loops !== false) {\r\n        var absPos, absPosInt, lastLoop\r\n\r\n        // If the absolute position is below 0, we just treat it as if it was 0\r\n        absPos = Math.max(this.absPos, 0)\r\n        absPosInt = Math.floor(absPos)\r\n\r\n        if(this.situation.loops === true || absPosInt < this.situation.loops) {\r\n          this.pos = absPos - absPosInt\r\n          lastLoop = this.situation.loop\r\n          this.situation.loop = absPosInt\r\n        } else {\r\n          this.absPos = this.situation.loops\r\n          this.pos = 1\r\n          // The -1 here is because we don't want to toggle reversed when all the loops have been completed\r\n          lastLoop = this.situation.loop - 1\r\n          this.situation.loop = this.situation.loops\r\n        }\r\n\r\n        if(this.situation.reversing) {\r\n          // Toggle reversed if an odd number of loops as occured since the last call of step\r\n          this.situation.reversed = this.situation.reversed != Boolean((this.situation.loop - lastLoop) % 2)\r\n        }\r\n\r\n      } else {\r\n        // If there are no loop, the absolute position must not be above 1\r\n        this.absPos = Math.min(this.absPos, 1)\r\n        this.pos = this.absPos\r\n      }\r\n\r\n      // while the absolute position can be below 0, the position must not be below 0\r\n      if(this.pos < 0) this.pos = 0\r\n\r\n      if(this.situation.reversed) this.pos = 1 - this.pos\r\n\r\n\r\n      // apply easing\r\n      var eased = this.situation.ease(this.pos)\r\n\r\n      // call once-callbacks\r\n      for(var i in this.situation.once){\r\n        if(i > this.lastPos && i <= eased){\r\n          this.situation.once[i].call(this.target(), this.pos, eased)\r\n          delete this.situation.once[i]\r\n        }\r\n      }\r\n\r\n      // fire during callback with position, eased position and current situation as parameter\r\n      if(this.active) this.target().fire('during', {pos: this.pos, eased: eased, fx: this, situation: this.situation})\r\n\r\n      // the user may call stop or finish in the during callback\r\n      // so make sure that we still have a valid situation\r\n      if(!this.situation){\r\n        return this\r\n      }\r\n\r\n      // apply the actual animation to every property\r\n      this.eachAt()\r\n\r\n      // do final code when situation is finished\r\n      if((this.pos == 1 && !this.situation.reversed) || (this.situation.reversed && this.pos == 0)){\r\n\r\n        // stop animation callback\r\n        this.stopAnimFrame()\r\n\r\n        // fire finished callback with current situation as parameter\r\n        this.target().fire('finished', {fx:this, situation: this.situation})\r\n\r\n        if(!this.situations.length){\r\n          this.target().fire('allfinished')\r\n\r\n          // Recheck the length since the user may call animate in the afterAll callback\r\n          if(!this.situations.length){\r\n            this.target().off('.fx') // there shouldnt be any binding left, but to make sure...\r\n            this.active = false\r\n          }\r\n        }\r\n\r\n        // start next animation\r\n        if(this.active) this.dequeue()\r\n        else this.clearCurrent()\r\n\r\n      }else if(!this.paused && this.active){\r\n        // we continue animating when we are not at the end\r\n        this.startAnimFrame()\r\n      }\r\n\r\n      // save last eased position for once callback triggering\r\n      this.lastPos = eased\r\n      return this\r\n\r\n    }\r\n\r\n    // calculates the step for every property and calls block with it\r\n  , eachAt: function(){\r\n      var i, len, at, self = this, target = this.target(), s = this.situation\r\n\r\n      // apply animations which can be called trough a method\r\n      for(i in s.animations){\r\n\r\n        at = [].concat(s.animations[i]).map(function(el){\r\n          return typeof el !== 'string' && el.at ? el.at(s.ease(self.pos), self.pos) : el\r\n        })\r\n\r\n        target[i].apply(target, at)\r\n\r\n      }\r\n\r\n      // apply animation which has to be applied with attr()\r\n      for(i in s.attrs){\r\n\r\n        at = [i].concat(s.attrs[i]).map(function(el){\r\n          return typeof el !== 'string' && el.at ? el.at(s.ease(self.pos), self.pos) : el\r\n        })\r\n\r\n        target.attr.apply(target, at)\r\n\r\n      }\r\n\r\n      // apply animation which has to be applied with style()\r\n      for(i in s.styles){\r\n\r\n        at = [i].concat(s.styles[i]).map(function(el){\r\n          return typeof el !== 'string' && el.at ? el.at(s.ease(self.pos), self.pos) : el\r\n        })\r\n\r\n        target.style.apply(target, at)\r\n\r\n      }\r\n\r\n      // animate initialTransformation which has to be chained\r\n      if(s.transforms.length){\r\n\r\n        // get initial initialTransformation\r\n        at = s.initialTransformation\r\n        for(i = 0, len = s.transforms.length; i < len; i++){\r\n\r\n          // get next transformation in chain\r\n          var a = s.transforms[i]\r\n\r\n          // multiply matrix directly\r\n          if(a instanceof SVG.Matrix){\r\n\r\n            if(a.relative){\r\n              at = at.multiply(new SVG.Matrix().morph(a).at(s.ease(this.pos)))\r\n            }else{\r\n              at = at.morph(a).at(s.ease(this.pos))\r\n            }\r\n            continue\r\n          }\r\n\r\n          // when transformation is absolute we have to reset the needed transformation first\r\n          if(!a.relative)\r\n            a.undo(at.extract())\r\n\r\n          // and reapply it after\r\n          at = at.multiply(a.at(s.ease(this.pos)))\r\n\r\n        }\r\n\r\n        // set new matrix on element\r\n        target.matrix(at)\r\n      }\r\n\r\n      return this\r\n\r\n    }\r\n\r\n\r\n    // adds an once-callback which is called at a specific position and never again\r\n  , once: function(pos, fn, isEased){\r\n      var c = this.last()\r\n      if(!isEased) pos = c.ease(pos)\r\n\r\n      c.once[pos] = fn\r\n\r\n      return this\r\n    }\r\n\r\n  , _callStart: function() {\r\n      setTimeout(function(){this.start()}.bind(this), 0)\r\n      return this\r\n    }\r\n\r\n  }\r\n\r\n, parent: SVG.Element\r\n\r\n  // Add method to parent elements\r\n, construct: {\r\n    // Get fx module or create a new one, then animate with given duration and ease\r\n    animate: function(o, ease, delay) {\r\n      return (this.fx || (this.fx = new SVG.FX(this))).animate(o, ease, delay)\r\n    }\r\n  , delay: function(delay){\r\n      return (this.fx || (this.fx = new SVG.FX(this))).delay(delay)\r\n    }\r\n  , stop: function(jumpToEnd, clearQueue) {\r\n      if (this.fx)\r\n        this.fx.stop(jumpToEnd, clearQueue)\r\n\r\n      return this\r\n    }\r\n  , finish: function() {\r\n      if (this.fx)\r\n        this.fx.finish()\r\n\r\n      return this\r\n    }\r\n    // Pause current animation\r\n  , pause: function() {\r\n      if (this.fx)\r\n        this.fx.pause()\r\n\r\n      return this\r\n    }\r\n    // Play paused current animation\r\n  , play: function() {\r\n      if (this.fx)\r\n        this.fx.play()\r\n\r\n      return this\r\n    }\r\n    // Set/Get the speed of the animations\r\n  , speed: function(speed) {\r\n      if (this.fx)\r\n        if (speed == null)\r\n          return this.fx.speed()\r\n        else\r\n          this.fx.speed(speed)\r\n\r\n      return this\r\n    }\r\n  }\r\n\r\n})\r\n\r\n// MorphObj is used whenever no morphable object is given\r\nSVG.MorphObj = SVG.invent({\r\n\r\n  create: function(from, to){\r\n    // prepare color for morphing\r\n    if(SVG.Color.isColor(to)) return new SVG.Color(from).morph(to)\r\n    // check if we have a list of values\r\n    if(SVG.regex.delimiter.test(from)) {\r\n      // prepare path for morphing\r\n      if(SVG.regex.pathLetters.test(from)) return new SVG.PathArray(from).morph(to)\r\n      // prepare value list for morphing\r\n      else return new SVG.Array(from).morph(to)\r\n    }\r\n    // prepare number for morphing\r\n    if(SVG.regex.numberAndUnit.test(to)) return new SVG.Number(from).morph(to)\r\n\r\n    // prepare for plain morphing\r\n    this.value = from\r\n    this.destination = to\r\n  }\r\n\r\n, extend: {\r\n    at: function(pos, real){\r\n      return real < 1 ? this.value : this.destination\r\n    },\r\n\r\n    valueOf: function(){\r\n      return this.value\r\n    }\r\n  }\r\n\r\n})\r\n\r\nSVG.extend(SVG.FX, {\r\n  // Add animatable attributes\r\n  attr: function(a, v, relative) {\r\n    // apply attributes individually\r\n    if (typeof a == 'object') {\r\n      for (var key in a)\r\n        this.attr(key, a[key])\r\n\r\n    } else {\r\n      this.add(a, v, 'attrs')\r\n    }\r\n\r\n    return this\r\n  }\r\n  // Add animatable styles\r\n, style: function(s, v) {\r\n    if (typeof s == 'object')\r\n      for (var key in s)\r\n        this.style(key, s[key])\r\n\r\n    else\r\n      this.add(s, v, 'styles')\r\n\r\n    return this\r\n  }\r\n  // Animatable x-axis\r\n, x: function(x, relative) {\r\n    if(this.target() instanceof SVG.G){\r\n      this.transform({x:x}, relative)\r\n      return this\r\n    }\r\n\r\n    var num = new SVG.Number(x)\r\n    num.relative = relative\r\n    return this.add('x', num)\r\n  }\r\n  // Animatable y-axis\r\n, y: function(y, relative) {\r\n    if(this.target() instanceof SVG.G){\r\n      this.transform({y:y}, relative)\r\n      return this\r\n    }\r\n\r\n    var num = new SVG.Number(y)\r\n    num.relative = relative\r\n    return this.add('y', num)\r\n  }\r\n  // Animatable center x-axis\r\n, cx: function(x) {\r\n    return this.add('cx', new SVG.Number(x))\r\n  }\r\n  // Animatable center y-axis\r\n, cy: function(y) {\r\n    return this.add('cy', new SVG.Number(y))\r\n  }\r\n  // Add animatable move\r\n, move: function(x, y) {\r\n    return this.x(x).y(y)\r\n  }\r\n  // Add animatable center\r\n, center: function(x, y) {\r\n    return this.cx(x).cy(y)\r\n  }\r\n  // Add animatable size\r\n, size: function(width, height) {\r\n    if (this.target() instanceof SVG.Text) {\r\n      // animate font size for Text elements\r\n      this.attr('font-size', width)\r\n\r\n    } else {\r\n      // animate bbox based size for all other elements\r\n      var box\r\n\r\n      if(!width || !height){\r\n        box = this.target().bbox()\r\n      }\r\n\r\n      if(!width){\r\n        width = box.width / box.height  * height\r\n      }\r\n\r\n      if(!height){\r\n        height = box.height / box.width  * width\r\n      }\r\n\r\n      this.add('width' , new SVG.Number(width))\r\n          .add('height', new SVG.Number(height))\r\n\r\n    }\r\n\r\n    return this\r\n  }\r\n  // Add animatable width\r\n, width: function(width) {\r\n    return this.add('width', new SVG.Number(width))\r\n  }\r\n  // Add animatable height\r\n, height: function(height) {\r\n    return this.add('height', new SVG.Number(height))\r\n  }\r\n  // Add animatable plot\r\n, plot: function(a, b, c, d) {\r\n    // Lines can be plotted with 4 arguments\r\n    if(arguments.length == 4) {\r\n      return this.plot([a, b, c, d])\r\n    }\r\n\r\n    return this.add('plot', new (this.target().morphArray)(a))\r\n  }\r\n  // Add leading method\r\n, leading: function(value) {\r\n    return this.target().leading ?\r\n      this.add('leading', new SVG.Number(value)) :\r\n      this\r\n  }\r\n  // Add animatable viewbox\r\n, viewbox: function(x, y, width, height) {\r\n    if (this.target() instanceof SVG.Container) {\r\n      this.add('viewbox', new SVG.ViewBox(x, y, width, height))\r\n    }\r\n\r\n    return this\r\n  }\r\n, update: function(o) {\r\n    if (this.target() instanceof SVG.Stop) {\r\n      if (typeof o == 'number' || o instanceof SVG.Number) {\r\n        return this.update({\r\n          offset:  arguments[0]\r\n        , color:   arguments[1]\r\n        , opacity: arguments[2]\r\n        })\r\n      }\r\n\r\n      if (o.opacity != null) this.attr('stop-opacity', o.opacity)\r\n      if (o.color   != null) this.attr('stop-color', o.color)\r\n      if (o.offset  != null) this.attr('offset', o.offset)\r\n    }\r\n\r\n    return this\r\n  }\r\n})\r\n\nSVG.Box = SVG.invent({\r\n  create: function(x, y, width, height) {\r\n    if (typeof x == 'object' && !(x instanceof SVG.Element)) {\r\n      // chromes getBoundingClientRect has no x and y property\r\n      return SVG.Box.call(this, x.left != null ? x.left : x.x , x.top != null ? x.top : x.y, x.width, x.height)\r\n    } else if (arguments.length == 4) {\r\n      this.x = x\r\n      this.y = y\r\n      this.width = width\r\n      this.height = height\r\n    }\r\n\r\n    // add center, right, bottom...\r\n    fullBox(this)\r\n  }\r\n, extend: {\r\n    // Merge rect box with another, return a new instance\r\n    merge: function(box) {\r\n      var b = new this.constructor()\r\n\r\n      // merge boxes\r\n      b.x      = Math.min(this.x, box.x)\r\n      b.y      = Math.min(this.y, box.y)\r\n      b.width  = Math.max(this.x + this.width,  box.x + box.width)  - b.x\r\n      b.height = Math.max(this.y + this.height, box.y + box.height) - b.y\r\n\r\n      return fullBox(b)\r\n    }\r\n\r\n  , transform: function(m) {\r\n      var xMin = Infinity, xMax = -Infinity, yMin = Infinity, yMax = -Infinity, p, bbox\r\n\r\n      var pts = [\r\n        new SVG.Point(this.x, this.y),\r\n        new SVG.Point(this.x2, this.y),\r\n        new SVG.Point(this.x, this.y2),\r\n        new SVG.Point(this.x2, this.y2)\r\n      ]\r\n\r\n      pts.forEach(function(p) {\r\n        p = p.transform(m)\r\n        xMin = Math.min(xMin,p.x)\r\n        xMax = Math.max(xMax,p.x)\r\n        yMin = Math.min(yMin,p.y)\r\n        yMax = Math.max(yMax,p.y)\r\n      })\r\n\r\n      bbox = new this.constructor()\r\n      bbox.x = xMin\r\n      bbox.width = xMax-xMin\r\n      bbox.y = yMin\r\n      bbox.height = yMax-yMin\r\n\r\n      fullBox(bbox)\r\n\r\n      return bbox\r\n    }\r\n  }\r\n})\r\n\r\nSVG.BBox = SVG.invent({\r\n  // Initialize\r\n  create: function(element) {\r\n    SVG.Box.apply(this, [].slice.call(arguments))\r\n\r\n    // get values if element is given\r\n    if (element instanceof SVG.Element) {\r\n      var box\r\n\r\n      // yes this is ugly, but Firefox can be a pain when it comes to elements that are not yet rendered\r\n      try {\r\n\r\n        if (!document.documentElement.contains){\r\n          // This is IE - it does not support contains() for top-level SVGs\r\n          var topParent = element.node\r\n          while (topParent.parentNode){\r\n            topParent = topParent.parentNode\r\n          }\r\n          if (topParent != document) throw new Exception('Element not in the dom')\r\n        } else {\r\n          // the element is NOT in the dom, throw error\r\n          if(!document.documentElement.contains(element.node)) throw new Exception('Element not in the dom')\r\n        }\r\n\r\n        // find native bbox\r\n        box = element.node.getBBox()\r\n      } catch(e) {\r\n        if(element instanceof SVG.Shape){\r\n          var clone = element.clone(SVG.parser.draw.instance).show()\r\n          box = clone.node.getBBox()\r\n          clone.remove()\r\n        }else{\r\n          box = {\r\n            x:      element.node.clientLeft\r\n          , y:      element.node.clientTop\r\n          , width:  element.node.clientWidth\r\n          , height: element.node.clientHeight\r\n          }\r\n        }\r\n      }\r\n\r\n      SVG.Box.call(this, box)\r\n    }\r\n\r\n  }\r\n\r\n  // Define ancestor\r\n, inherit: SVG.Box\r\n\r\n  // Define Parent\r\n, parent: SVG.Element\r\n\r\n  // Constructor\r\n, construct: {\r\n    // Get bounding box\r\n    bbox: function() {\r\n      return new SVG.BBox(this)\r\n    }\r\n  }\r\n\r\n})\r\n\r\nSVG.BBox.prototype.constructor = SVG.BBox\r\n\r\n\r\nSVG.extend(SVG.Element, {\r\n  tbox: function(){\r\n    console.warn('Use of TBox is deprecated and mapped to RBox. Use .rbox() instead.')\r\n    return this.rbox(this.doc())\r\n  }\r\n})\r\n\r\nSVG.RBox = SVG.invent({\r\n  // Initialize\r\n  create: function(element) {\r\n    SVG.Box.apply(this, [].slice.call(arguments))\r\n\r\n    if (element instanceof SVG.Element) {\r\n      SVG.Box.call(this, element.node.getBoundingClientRect())\r\n    }\r\n  }\r\n\r\n, inherit: SVG.Box\r\n\r\n  // define Parent\r\n, parent: SVG.Element\r\n\r\n, extend: {\r\n    addOffset: function() {\r\n      // offset by window scroll position, because getBoundingClientRect changes when window is scrolled\r\n      this.x += window.pageXOffset\r\n      this.y += window.pageYOffset\r\n      return this\r\n    }\r\n  }\r\n\r\n  // Constructor\r\n, construct: {\r\n    // Get rect box\r\n    rbox: function(el) {\r\n      if (el) return new SVG.RBox(this).transform(el.screenCTM().inverse())\r\n      return new SVG.RBox(this).addOffset()\r\n    }\r\n  }\r\n\r\n})\r\n\r\nSVG.RBox.prototype.constructor = SVG.RBox\r\n\nSVG.Matrix = SVG.invent({\r\n  // Initialize\r\n  create: function(source) {\r\n    var i, base = arrayToMatrix([1, 0, 0, 1, 0, 0])\r\n\r\n    // ensure source as object\r\n    source = source instanceof SVG.Element ?\r\n      source.matrixify() :\r\n    typeof source === 'string' ?\r\n      arrayToMatrix(source.split(SVG.regex.delimiter).map(parseFloat)) :\r\n    arguments.length == 6 ?\r\n      arrayToMatrix([].slice.call(arguments)) :\r\n    Array.isArray(source) ?\r\n      arrayToMatrix(source) :\r\n    typeof source === 'object' ?\r\n      source : base\r\n\r\n    // merge source\r\n    for (i = abcdef.length - 1; i >= 0; --i)\r\n      this[abcdef[i]] = source[abcdef[i]] != null ?\r\n        source[abcdef[i]] : base[abcdef[i]]\r\n  }\r\n\r\n  // Add methods\r\n, extend: {\r\n    // Extract individual transformations\r\n    extract: function() {\r\n      // find delta transform points\r\n      var px    = deltaTransformPoint(this, 0, 1)\r\n        , py    = deltaTransformPoint(this, 1, 0)\r\n        , skewX = 180 / Math.PI * Math.atan2(px.y, px.x) - 90\r\n\r\n      return {\r\n        // translation\r\n        x:        this.e\r\n      , y:        this.f\r\n      , transformedX:(this.e * Math.cos(skewX * Math.PI / 180) + this.f * Math.sin(skewX * Math.PI / 180)) / Math.sqrt(this.a * this.a + this.b * this.b)\r\n      , transformedY:(this.f * Math.cos(skewX * Math.PI / 180) + this.e * Math.sin(-skewX * Math.PI / 180)) / Math.sqrt(this.c * this.c + this.d * this.d)\r\n        // skew\r\n      , skewX:    -skewX\r\n      , skewY:    180 / Math.PI * Math.atan2(py.y, py.x)\r\n        // scale\r\n      , scaleX:   Math.sqrt(this.a * this.a + this.b * this.b)\r\n      , scaleY:   Math.sqrt(this.c * this.c + this.d * this.d)\r\n        // rotation\r\n      , rotation: skewX\r\n      , a: this.a\r\n      , b: this.b\r\n      , c: this.c\r\n      , d: this.d\r\n      , e: this.e\r\n      , f: this.f\r\n      , matrix: new SVG.Matrix(this)\r\n      }\r\n    }\r\n    // Clone matrix\r\n  , clone: function() {\r\n      return new SVG.Matrix(this)\r\n    }\r\n    // Morph one matrix into another\r\n  , morph: function(matrix) {\r\n      // store new destination\r\n      this.destination = new SVG.Matrix(matrix)\r\n\r\n      return this\r\n    }\r\n    // Get morphed matrix at a given position\r\n  , at: function(pos) {\r\n      // make sure a destination is defined\r\n      if (!this.destination) return this\r\n\r\n      // calculate morphed matrix at a given position\r\n      var matrix = new SVG.Matrix({\r\n        a: this.a + (this.destination.a - this.a) * pos\r\n      , b: this.b + (this.destination.b - this.b) * pos\r\n      , c: this.c + (this.destination.c - this.c) * pos\r\n      , d: this.d + (this.destination.d - this.d) * pos\r\n      , e: this.e + (this.destination.e - this.e) * pos\r\n      , f: this.f + (this.destination.f - this.f) * pos\r\n      })\r\n\r\n      return matrix\r\n    }\r\n    // Multiplies by given matrix\r\n  , multiply: function(matrix) {\r\n      return new SVG.Matrix(this.native().multiply(parseMatrix(matrix).native()))\r\n    }\r\n    // Inverses matrix\r\n  , inverse: function() {\r\n      return new SVG.Matrix(this.native().inverse())\r\n    }\r\n    // Translate matrix\r\n  , translate: function(x, y) {\r\n      return new SVG.Matrix(this.native().translate(x || 0, y || 0))\r\n    }\r\n    // Scale matrix\r\n  , scale: function(x, y, cx, cy) {\r\n      // support uniformal scale\r\n      if (arguments.length == 1) {\r\n        y = x\r\n      } else if (arguments.length == 3) {\r\n        cy = cx\r\n        cx = y\r\n        y = x\r\n      }\r\n\r\n      return this.around(cx, cy, new SVG.Matrix(x, 0, 0, y, 0, 0))\r\n    }\r\n    // Rotate matrix\r\n  , rotate: function(r, cx, cy) {\r\n      // convert degrees to radians\r\n      r = SVG.utils.radians(r)\r\n\r\n      return this.around(cx, cy, new SVG.Matrix(Math.cos(r), Math.sin(r), -Math.sin(r), Math.cos(r), 0, 0))\r\n    }\r\n    // Flip matrix on x or y, at a given offset\r\n  , flip: function(a, o) {\r\n      return a == 'x' ?\r\n          this.scale(-1, 1, o, 0) :\r\n        a == 'y' ?\r\n          this.scale(1, -1, 0, o) :\r\n          this.scale(-1, -1, a, o != null ? o : a)\r\n    }\r\n    // Skew\r\n  , skew: function(x, y, cx, cy) {\r\n      // support uniformal skew\r\n      if (arguments.length == 1) {\r\n        y = x\r\n      } else if (arguments.length == 3) {\r\n        cy = cx\r\n        cx = y\r\n        y = x\r\n      }\r\n\r\n      // convert degrees to radians\r\n      x = SVG.utils.radians(x)\r\n      y = SVG.utils.radians(y)\r\n\r\n      return this.around(cx, cy, new SVG.Matrix(1, Math.tan(y), Math.tan(x), 1, 0, 0))\r\n    }\r\n    // SkewX\r\n  , skewX: function(x, cx, cy) {\r\n      return this.skew(x, 0, cx, cy)\r\n    }\r\n    // SkewY\r\n  , skewY: function(y, cx, cy) {\r\n      return this.skew(0, y, cx, cy)\r\n    }\r\n    // Transform around a center point\r\n  , around: function(cx, cy, matrix) {\r\n      return this\r\n        .multiply(new SVG.Matrix(1, 0, 0, 1, cx || 0, cy || 0))\r\n        .multiply(matrix)\r\n        .multiply(new SVG.Matrix(1, 0, 0, 1, -cx || 0, -cy || 0))\r\n    }\r\n    // Convert to native SVGMatrix\r\n  , native: function() {\r\n      // create new matrix\r\n      var matrix = SVG.parser.native.createSVGMatrix()\r\n\r\n      // update with current values\r\n      for (var i = abcdef.length - 1; i >= 0; i--)\r\n        matrix[abcdef[i]] = this[abcdef[i]]\r\n\r\n      return matrix\r\n    }\r\n    // Convert matrix to string\r\n  , toString: function() {\r\n      // Construct the matrix directly, avoid values that are too small\r\n      return 'matrix(' + float32String(this.a) + ',' + float32String(this.b)\r\n        + ',' + float32String(this.c) + ',' + float32String(this.d)\r\n        + ',' + float32String(this.e) + ',' + float32String(this.f)\r\n        + ')'\r\n    }\r\n  }\r\n\r\n  // Define parent\r\n, parent: SVG.Element\r\n\r\n  // Add parent method\r\n, construct: {\r\n    // Get current matrix\r\n    ctm: function() {\r\n      return new SVG.Matrix(this.node.getCTM())\r\n    },\r\n    // Get current screen matrix\r\n    screenCTM: function() {\r\n      /* https://bugzilla.mozilla.org/show_bug.cgi?id=1344537\r\n         This is needed because FF does not return the transformation matrix\r\n         for the inner coordinate system when getScreenCTM() is called on nested svgs.\r\n         However all other Browsers do that */\r\n      if(this instanceof SVG.Nested) {\r\n        var rect = this.rect(1,1)\r\n        var m = rect.node.getScreenCTM()\r\n        rect.remove()\r\n        return new SVG.Matrix(m)\r\n      }\r\n      return new SVG.Matrix(this.node.getScreenCTM())\r\n    }\r\n\r\n  }\r\n\r\n})\r\n\nSVG.Point = SVG.invent({\r\n  // Initialize\r\n  create: function(x,y) {\r\n    var i, source, base = {x:0, y:0}\r\n\r\n    // ensure source as object\r\n    source = Array.isArray(x) ?\r\n      {x:x[0], y:x[1]} :\r\n    typeof x === 'object' ?\r\n      {x:x.x, y:x.y} :\r\n    x != null ?\r\n      {x:x, y:(y != null ? y : x)} : base // If y has no value, then x is used has its value\r\n\r\n    // merge source\r\n    this.x = source.x\r\n    this.y = source.y\r\n  }\r\n\r\n  // Add methods\r\n, extend: {\r\n    // Clone point\r\n    clone: function() {\r\n      return new SVG.Point(this)\r\n    }\r\n    // Morph one point into another\r\n  , morph: function(x, y) {\r\n      // store new destination\r\n      this.destination = new SVG.Point(x, y)\r\n\r\n      return this\r\n    }\r\n    // Get morphed point at a given position\r\n  , at: function(pos) {\r\n      // make sure a destination is defined\r\n      if (!this.destination) return this\r\n\r\n      // calculate morphed matrix at a given position\r\n      var point = new SVG.Point({\r\n        x: this.x + (this.destination.x - this.x) * pos\r\n      , y: this.y + (this.destination.y - this.y) * pos\r\n      })\r\n\r\n      return point\r\n    }\r\n    // Convert to native SVGPoint\r\n  , native: function() {\r\n      // create new point\r\n      var point = SVG.parser.native.createSVGPoint()\r\n\r\n      // update with current values\r\n      point.x = this.x\r\n      point.y = this.y\r\n\r\n      return point\r\n    }\r\n    // transform point with matrix\r\n  , transform: function(matrix) {\r\n      return new SVG.Point(this.native().matrixTransform(matrix.native()))\r\n    }\r\n\r\n  }\r\n\r\n})\r\n\r\nSVG.extend(SVG.Element, {\r\n\r\n  // Get point\r\n  point: function(x, y) {\r\n    return new SVG.Point(x,y).transform(this.screenCTM().inverse());\r\n  }\r\n\r\n})\r\n\nSVG.extend(SVG.Element, {\r\n  // Set svg element attribute\r\n  attr: function(a, v, n) {\r\n    // act as full getter\r\n    if (a == null) {\r\n      // get an object of attributes\r\n      a = {}\r\n      v = this.node.attributes\r\n      for (n = v.length - 1; n >= 0; n--)\r\n        a[v[n].nodeName] = SVG.regex.isNumber.test(v[n].nodeValue) ? parseFloat(v[n].nodeValue) : v[n].nodeValue\r\n\r\n      return a\r\n\r\n    } else if (typeof a == 'object') {\r\n      // apply every attribute individually if an object is passed\r\n      for (v in a) this.attr(v, a[v])\r\n\r\n    } else if (v === null) {\r\n        // remove value\r\n        this.node.removeAttribute(a)\r\n\r\n    } else if (v == null) {\r\n      // act as a getter if the first and only argument is not an object\r\n      v = this.node.getAttribute(a)\r\n      return v == null ?\r\n        SVG.defaults.attrs[a] :\r\n      SVG.regex.isNumber.test(v) ?\r\n        parseFloat(v) : v\r\n\r\n    } else {\r\n      // BUG FIX: some browsers will render a stroke if a color is given even though stroke width is 0\r\n      if (a == 'stroke-width')\r\n        this.attr('stroke', parseFloat(v) > 0 ? this._stroke : null)\r\n      else if (a == 'stroke')\r\n        this._stroke = v\r\n\r\n      // convert image fill and stroke to patterns\r\n      if (a == 'fill' || a == 'stroke') {\r\n        if (SVG.regex.isImage.test(v))\r\n          v = this.doc().defs().image(v, 0, 0)\r\n\r\n        if (v instanceof SVG.Image)\r\n          v = this.doc().defs().pattern(0, 0, function() {\r\n            this.add(v)\r\n          })\r\n      }\r\n\r\n      // ensure correct numeric values (also accepts NaN and Infinity)\r\n      if (typeof v === 'number')\r\n        v = new SVG.Number(v)\r\n\r\n      // ensure full hex color\r\n      else if (SVG.Color.isColor(v))\r\n        v = new SVG.Color(v)\r\n\r\n      // parse array values\r\n      else if (Array.isArray(v))\r\n        v = new SVG.Array(v)\r\n\r\n      // if the passed attribute is leading...\r\n      if (a == 'leading') {\r\n        // ... call the leading method instead\r\n        if (this.leading)\r\n          this.leading(v)\r\n      } else {\r\n        // set given attribute on node\r\n        typeof n === 'string' ?\r\n          this.node.setAttributeNS(n, a, v.toString()) :\r\n          this.node.setAttribute(a, v.toString())\r\n      }\r\n\r\n      // rebuild if required\r\n      if (this.rebuild && (a == 'font-size' || a == 'x'))\r\n        this.rebuild(a, v)\r\n    }\r\n\r\n    return this\r\n  }\r\n})\nSVG.extend(SVG.Element, {\r\n  // Add transformations\r\n  transform: function(o, relative) {\r\n    // get target in case of the fx module, otherwise reference this\r\n    var target = this\r\n      , matrix, bbox\r\n\r\n    // act as a getter\r\n    if (typeof o !== 'object') {\r\n      // get current matrix\r\n      matrix = new SVG.Matrix(target).extract()\r\n\r\n      return typeof o === 'string' ? matrix[o] : matrix\r\n    }\r\n\r\n    // get current matrix\r\n    matrix = new SVG.Matrix(target)\r\n\r\n    // ensure relative flag\r\n    relative = !!relative || !!o.relative\r\n\r\n    // act on matrix\r\n    if (o.a != null) {\r\n      matrix = relative ?\r\n        // relative\r\n        matrix.multiply(new SVG.Matrix(o)) :\r\n        // absolute\r\n        new SVG.Matrix(o)\r\n\r\n    // act on rotation\r\n    } else if (o.rotation != null) {\r\n      // ensure centre point\r\n      ensureCentre(o, target)\r\n\r\n      // apply transformation\r\n      matrix = relative ?\r\n        // relative\r\n        matrix.rotate(o.rotation, o.cx, o.cy) :\r\n        // absolute\r\n        matrix.rotate(o.rotation - matrix.extract().rotation, o.cx, o.cy)\r\n\r\n    // act on scale\r\n    } else if (o.scale != null || o.scaleX != null || o.scaleY != null) {\r\n      // ensure centre point\r\n      ensureCentre(o, target)\r\n\r\n      // ensure scale values on both axes\r\n      o.scaleX = o.scale != null ? o.scale : o.scaleX != null ? o.scaleX : 1\r\n      o.scaleY = o.scale != null ? o.scale : o.scaleY != null ? o.scaleY : 1\r\n\r\n      if (!relative) {\r\n        // absolute; multiply inversed values\r\n        var e = matrix.extract()\r\n        o.scaleX = o.scaleX * 1 / e.scaleX\r\n        o.scaleY = o.scaleY * 1 / e.scaleY\r\n      }\r\n\r\n      matrix = matrix.scale(o.scaleX, o.scaleY, o.cx, o.cy)\r\n\r\n    // act on skew\r\n    } else if (o.skew != null || o.skewX != null || o.skewY != null) {\r\n      // ensure centre point\r\n      ensureCentre(o, target)\r\n\r\n      // ensure skew values on both axes\r\n      o.skewX = o.skew != null ? o.skew : o.skewX != null ? o.skewX : 0\r\n      o.skewY = o.skew != null ? o.skew : o.skewY != null ? o.skewY : 0\r\n\r\n      if (!relative) {\r\n        // absolute; reset skew values\r\n        var e = matrix.extract()\r\n        matrix = matrix.multiply(new SVG.Matrix().skew(e.skewX, e.skewY, o.cx, o.cy).inverse())\r\n      }\r\n\r\n      matrix = matrix.skew(o.skewX, o.skewY, o.cx, o.cy)\r\n\r\n    // act on flip\r\n    } else if (o.flip) {\r\n      if(o.flip == 'x' || o.flip == 'y') {\r\n        o.offset = o.offset == null ? target.bbox()['c' + o.flip] : o.offset\r\n      } else {\r\n        if(o.offset == null) {\r\n          bbox = target.bbox()\r\n          o.flip = bbox.cx\r\n          o.offset = bbox.cy\r\n        } else {\r\n          o.flip = o.offset\r\n        }\r\n      }\r\n\r\n      matrix = new SVG.Matrix().flip(o.flip, o.offset)\r\n\r\n    // act on translate\r\n    } else if (o.x != null || o.y != null) {\r\n      if (relative) {\r\n        // relative\r\n        matrix = matrix.translate(o.x, o.y)\r\n      } else {\r\n        // absolute\r\n        if (o.x != null) matrix.e = o.x\r\n        if (o.y != null) matrix.f = o.y\r\n      }\r\n    }\r\n\r\n    return this.attr('transform', matrix)\r\n  }\r\n})\r\n\r\nSVG.extend(SVG.FX, {\r\n  transform: function(o, relative) {\r\n    // get target in case of the fx module, otherwise reference this\r\n    var target = this.target()\r\n      , matrix, bbox\r\n\r\n    // act as a getter\r\n    if (typeof o !== 'object') {\r\n      // get current matrix\r\n      matrix = new SVG.Matrix(target).extract()\r\n\r\n      return typeof o === 'string' ? matrix[o] : matrix\r\n    }\r\n\r\n    // ensure relative flag\r\n    relative = !!relative || !!o.relative\r\n\r\n    // act on matrix\r\n    if (o.a != null) {\r\n      matrix = new SVG.Matrix(o)\r\n\r\n    // act on rotation\r\n    } else if (o.rotation != null) {\r\n      // ensure centre point\r\n      ensureCentre(o, target)\r\n\r\n      // apply transformation\r\n      matrix = new SVG.Rotate(o.rotation, o.cx, o.cy)\r\n\r\n    // act on scale\r\n    } else if (o.scale != null || o.scaleX != null || o.scaleY != null) {\r\n      // ensure centre point\r\n      ensureCentre(o, target)\r\n\r\n      // ensure scale values on both axes\r\n      o.scaleX = o.scale != null ? o.scale : o.scaleX != null ? o.scaleX : 1\r\n      o.scaleY = o.scale != null ? o.scale : o.scaleY != null ? o.scaleY : 1\r\n\r\n      matrix = new SVG.Scale(o.scaleX, o.scaleY, o.cx, o.cy)\r\n\r\n    // act on skew\r\n    } else if (o.skewX != null || o.skewY != null) {\r\n      // ensure centre point\r\n      ensureCentre(o, target)\r\n\r\n      // ensure skew values on both axes\r\n      o.skewX = o.skewX != null ? o.skewX : 0\r\n      o.skewY = o.skewY != null ? o.skewY : 0\r\n\r\n      matrix = new SVG.Skew(o.skewX, o.skewY, o.cx, o.cy)\r\n\r\n    // act on flip\r\n    } else if (o.flip) {\r\n      if(o.flip == 'x' || o.flip == 'y') {\r\n        o.offset = o.offset == null ? target.bbox()['c' + o.flip] : o.offset\r\n      } else {\r\n        if(o.offset == null) {\r\n          bbox = target.bbox()\r\n          o.flip = bbox.cx\r\n          o.offset = bbox.cy\r\n        } else {\r\n          o.flip = o.offset\r\n        }\r\n      }\r\n\r\n      matrix = new SVG.Matrix().flip(o.flip, o.offset)\r\n\r\n    // act on translate\r\n    } else if (o.x != null || o.y != null) {\r\n      matrix = new SVG.Translate(o.x, o.y)\r\n    }\r\n\r\n    if(!matrix) return this\r\n\r\n    matrix.relative = relative\r\n\r\n    this.last().transforms.push(matrix)\r\n\r\n    return this._callStart()\r\n  }\r\n})\r\n\r\nSVG.extend(SVG.Element, {\r\n  // Reset all transformations\r\n  untransform: function() {\r\n    return this.attr('transform', null)\r\n  },\r\n  // merge the whole transformation chain into one matrix and returns it\r\n  matrixify: function() {\r\n\r\n    var matrix = (this.attr('transform') || '')\r\n      // split transformations\r\n      .split(SVG.regex.transforms).slice(0,-1).map(function(str){\r\n        // generate key => value pairs\r\n        var kv = str.trim().split('(')\r\n        return [kv[0], kv[1].split(SVG.regex.delimiter).map(function(str){ return parseFloat(str) })]\r\n      })\r\n      // merge every transformation into one matrix\r\n      .reduce(function(matrix, transform){\r\n\r\n        if(transform[0] == 'matrix') return matrix.multiply(arrayToMatrix(transform[1]))\r\n        return matrix[transform[0]].apply(matrix, transform[1])\r\n\r\n      }, new SVG.Matrix())\r\n\r\n    return matrix\r\n  },\r\n  // add an element to another parent without changing the visual representation on the screen\r\n  toParent: function(parent) {\r\n    if(this == parent) return this\r\n    var ctm = this.screenCTM()\r\n    var pCtm = parent.screenCTM().inverse()\r\n\r\n    this.addTo(parent).untransform().transform(pCtm.multiply(ctm))\r\n\r\n    return this\r\n  },\r\n  // same as above with parent equals root-svg\r\n  toDoc: function() {\r\n    return this.toParent(this.doc())\r\n  }\r\n\r\n})\r\n\r\nSVG.Transformation = SVG.invent({\r\n\r\n  create: function(source, inversed){\r\n\r\n    if(arguments.length > 1 && typeof inversed != 'boolean'){\r\n      return this.constructor.call(this, [].slice.call(arguments))\r\n    }\r\n\r\n    if(Array.isArray(source)){\r\n      for(var i = 0, len = this.arguments.length; i < len; ++i){\r\n        this[this.arguments[i]] = source[i]\r\n      }\r\n    } else if(typeof source == 'object'){\r\n      for(var i = 0, len = this.arguments.length; i < len; ++i){\r\n        this[this.arguments[i]] = source[this.arguments[i]]\r\n      }\r\n    }\r\n\r\n    this.inversed = false\r\n\r\n    if(inversed === true){\r\n      this.inversed = true\r\n    }\r\n\r\n  }\r\n\r\n, extend: {\r\n\r\n    arguments: []\r\n  , method: ''\r\n\r\n  , at: function(pos){\r\n\r\n      var params = []\r\n\r\n      for(var i = 0, len = this.arguments.length; i < len; ++i){\r\n        params.push(this[this.arguments[i]])\r\n      }\r\n\r\n      var m = this._undo || new SVG.Matrix()\r\n\r\n      m = new SVG.Matrix().morph(SVG.Matrix.prototype[this.method].apply(m, params)).at(pos)\r\n\r\n      return this.inversed ? m.inverse() : m\r\n\r\n    }\r\n\r\n  , undo: function(o){\r\n      for(var i = 0, len = this.arguments.length; i < len; ++i){\r\n        o[this.arguments[i]] = typeof this[this.arguments[i]] == 'undefined' ? 0 : o[this.arguments[i]]\r\n      }\r\n\r\n      // The method SVG.Matrix.extract which was used before calling this\r\n      // method to obtain a value for the parameter o doesn't return a cx and\r\n      // a cy so we use the ones that were provided to this object at its creation\r\n      o.cx = this.cx\r\n      o.cy = this.cy\r\n\r\n      this._undo = new SVG[capitalize(this.method)](o, true).at(1)\r\n\r\n      return this\r\n    }\r\n\r\n  }\r\n\r\n})\r\n\r\nSVG.Translate = SVG.invent({\r\n\r\n  parent: SVG.Matrix\r\n, inherit: SVG.Transformation\r\n\r\n, create: function(source, inversed){\r\n    this.constructor.apply(this, [].slice.call(arguments))\r\n  }\r\n\r\n, extend: {\r\n    arguments: ['transformedX', 'transformedY']\r\n  , method: 'translate'\r\n  }\r\n\r\n})\r\n\r\nSVG.Rotate = SVG.invent({\r\n\r\n  parent: SVG.Matrix\r\n, inherit: SVG.Transformation\r\n\r\n, create: function(source, inversed){\r\n    this.constructor.apply(this, [].slice.call(arguments))\r\n  }\r\n\r\n, extend: {\r\n    arguments: ['rotation', 'cx', 'cy']\r\n  , method: 'rotate'\r\n  , at: function(pos){\r\n      var m = new SVG.Matrix().rotate(new SVG.Number().morph(this.rotation - (this._undo ? this._undo.rotation : 0)).at(pos), this.cx, this.cy)\r\n      return this.inversed ? m.inverse() : m\r\n    }\r\n  , undo: function(o){\r\n      this._undo = o\r\n      return this\r\n    }\r\n  }\r\n\r\n})\r\n\r\nSVG.Scale = SVG.invent({\r\n\r\n  parent: SVG.Matrix\r\n, inherit: SVG.Transformation\r\n\r\n, create: function(source, inversed){\r\n    this.constructor.apply(this, [].slice.call(arguments))\r\n  }\r\n\r\n, extend: {\r\n    arguments: ['scaleX', 'scaleY', 'cx', 'cy']\r\n  , method: 'scale'\r\n  }\r\n\r\n})\r\n\r\nSVG.Skew = SVG.invent({\r\n\r\n  parent: SVG.Matrix\r\n, inherit: SVG.Transformation\r\n\r\n, create: function(source, inversed){\r\n    this.constructor.apply(this, [].slice.call(arguments))\r\n  }\r\n\r\n, extend: {\r\n    arguments: ['skewX', 'skewY', 'cx', 'cy']\r\n  , method: 'skew'\r\n  }\r\n\r\n})\r\n\nSVG.extend(SVG.Element, {\r\n  // Dynamic style generator\r\n  style: function(s, v) {\r\n    if (arguments.length == 0) {\r\n      // get full style\r\n      return this.node.style.cssText || ''\r\n\r\n    } else if (arguments.length < 2) {\r\n      // apply every style individually if an object is passed\r\n      if (typeof s == 'object') {\r\n        for (v in s) this.style(v, s[v])\r\n\r\n      } else if (SVG.regex.isCss.test(s)) {\r\n        // parse css string\r\n        s = s.split(/\\s*;\\s*/)\r\n          // filter out suffix ; and stuff like ;;\r\n          .filter(function(e) { return !!e })\r\n          .map(function(e){ return e.split(/\\s*:\\s*/) })\r\n\r\n        // apply every definition individually\r\n        while (v = s.pop()) {\r\n          this.style(v[0], v[1])\r\n        }\r\n      } else {\r\n        // act as a getter if the first and only argument is not an object\r\n        return this.node.style[camelCase(s)]\r\n      }\r\n\r\n    } else {\r\n      this.node.style[camelCase(s)] = v === null || SVG.regex.isBlank.test(v) ? '' : v\r\n    }\r\n\r\n    return this\r\n  }\r\n})\nSVG.Parent = SVG.invent({\r\n  // Initialize node\r\n  create: function(element) {\r\n    this.constructor.call(this, element)\r\n  }\r\n\r\n  // Inherit from\r\n, inherit: SVG.Element\r\n\r\n  // Add class methods\r\n, extend: {\r\n    // Returns all child elements\r\n    children: function() {\r\n      return SVG.utils.map(SVG.utils.filterSVGElements(this.node.childNodes), function(node) {\r\n        return SVG.adopt(node)\r\n      })\r\n    }\r\n    // Add given element at a position\r\n  , add: function(element, i) {\r\n      if (i == null)\r\n        this.node.appendChild(element.node)\r\n      else if (element.node != this.node.childNodes[i])\r\n        this.node.insertBefore(element.node, this.node.childNodes[i])\r\n\r\n      return this\r\n    }\r\n    // Basically does the same as `add()` but returns the added element instead\r\n  , put: function(element, i) {\r\n      this.add(element, i)\r\n      return element\r\n    }\r\n    // Checks if the given element is a child\r\n  , has: function(element) {\r\n      return this.index(element) >= 0\r\n    }\r\n    // Gets index of given element\r\n  , index: function(element) {\r\n      return [].slice.call(this.node.childNodes).indexOf(element.node)\r\n    }\r\n    // Get a element at the given index\r\n  , get: function(i) {\r\n      return SVG.adopt(this.node.childNodes[i])\r\n    }\r\n    // Get first child\r\n  , first: function() {\r\n      return this.get(0)\r\n    }\r\n    // Get the last child\r\n  , last: function() {\r\n      return this.get(this.node.childNodes.length - 1)\r\n    }\r\n    // Iterates over all children and invokes a given block\r\n  , each: function(block, deep) {\r\n      var i, il\r\n        , children = this.children()\r\n\r\n      for (i = 0, il = children.length; i < il; i++) {\r\n        if (children[i] instanceof SVG.Element)\r\n          block.apply(children[i], [i, children])\r\n\r\n        if (deep && (children[i] instanceof SVG.Container))\r\n          children[i].each(block, deep)\r\n      }\r\n\r\n      return this\r\n    }\r\n    // Remove a given child\r\n  , removeElement: function(element) {\r\n      this.node.removeChild(element.node)\r\n\r\n      return this\r\n    }\r\n    // Remove all elements in this container\r\n  , clear: function() {\r\n      // remove children\r\n      while(this.node.hasChildNodes())\r\n        this.node.removeChild(this.node.lastChild)\r\n\r\n      // remove defs reference\r\n      delete this._defs\r\n\r\n      return this\r\n    }\r\n  , // Get defs\r\n    defs: function() {\r\n      return this.doc().defs()\r\n    }\r\n  }\r\n\r\n})\r\n\nSVG.extend(SVG.Parent, {\r\n\r\n  ungroup: function(parent, depth) {\r\n    if(depth === 0 || this instanceof SVG.Defs || this.node == SVG.parser.draw) return this\r\n\r\n    parent = parent || (this instanceof SVG.Doc ? this : this.parent(SVG.Parent))\r\n    depth = depth || Infinity\r\n\r\n    this.each(function(){\r\n      if(this instanceof SVG.Defs) return this\r\n      if(this instanceof SVG.Parent) return this.ungroup(parent, depth-1)\r\n      return this.toParent(parent)\r\n    })\r\n\r\n    this.node.firstChild || this.remove()\r\n\r\n    return this\r\n  },\r\n\r\n  flatten: function(parent, depth) {\r\n    return this.ungroup(parent, depth)\r\n  }\r\n\r\n})\nSVG.Container = SVG.invent({\r\n  // Initialize node\r\n  create: function(element) {\r\n    this.constructor.call(this, element)\r\n  }\r\n\r\n  // Inherit from\r\n, inherit: SVG.Parent\r\n\r\n})\n\r\nSVG.ViewBox = SVG.invent({\r\n\r\n  create: function(source) {\r\n    var i, base = [0, 0, 0, 0]\r\n\r\n    var x, y, width, height, box, view, we, he\r\n      , wm   = 1 // width multiplier\r\n      , hm   = 1 // height multiplier\r\n      , reg  = /[+-]?(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?/gi\r\n\r\n    if(source instanceof SVG.Element){\r\n\r\n      we = source\r\n      he = source\r\n      view = (source.attr('viewBox') || '').match(reg)\r\n      box = source.bbox\r\n\r\n      // get dimensions of current node\r\n      width  = new SVG.Number(source.width())\r\n      height = new SVG.Number(source.height())\r\n\r\n      // find nearest non-percentual dimensions\r\n      while (width.unit == '%') {\r\n        wm *= width.value\r\n        width = new SVG.Number(we instanceof SVG.Doc ? we.parent().offsetWidth : we.parent().width())\r\n        we = we.parent()\r\n      }\r\n      while (height.unit == '%') {\r\n        hm *= height.value\r\n        height = new SVG.Number(he instanceof SVG.Doc ? he.parent().offsetHeight : he.parent().height())\r\n        he = he.parent()\r\n      }\r\n\r\n      // ensure defaults\r\n      this.x      = 0\r\n      this.y      = 0\r\n      this.width  = width  * wm\r\n      this.height = height * hm\r\n      this.zoom   = 1\r\n\r\n      if (view) {\r\n        // get width and height from viewbox\r\n        x      = parseFloat(view[0])\r\n        y      = parseFloat(view[1])\r\n        width  = parseFloat(view[2])\r\n        height = parseFloat(view[3])\r\n\r\n        // calculate zoom accoring to viewbox\r\n        this.zoom = ((this.width / this.height) > (width / height)) ?\r\n          this.height / height :\r\n          this.width  / width\r\n\r\n        // calculate real pixel dimensions on parent SVG.Doc element\r\n        this.x      = x\r\n        this.y      = y\r\n        this.width  = width\r\n        this.height = height\r\n\r\n      }\r\n\r\n    }else{\r\n\r\n      // ensure source as object\r\n      source = typeof source === 'string' ?\r\n        source.match(reg).map(function(el){ return parseFloat(el) }) :\r\n      Array.isArray(source) ?\r\n        source :\r\n      typeof source == 'object' ?\r\n        [source.x, source.y, source.width, source.height] :\r\n      arguments.length == 4 ?\r\n        [].slice.call(arguments) :\r\n        base\r\n\r\n      this.x = source[0]\r\n      this.y = source[1]\r\n      this.width = source[2]\r\n      this.height = source[3]\r\n    }\r\n\r\n\r\n  }\r\n\r\n, extend: {\r\n\r\n    toString: function() {\r\n      return this.x + ' ' + this.y + ' ' + this.width + ' ' + this.height\r\n    }\r\n  , morph: function(x, y, width, height){\r\n      this.destination = new SVG.ViewBox(x, y, width, height)\r\n      return this\r\n    }\r\n\r\n  , at: function(pos) {\r\n\r\n      if(!this.destination) return this\r\n\r\n      return new SVG.ViewBox([\r\n          this.x + (this.destination.x - this.x) * pos\r\n        , this.y + (this.destination.y - this.y) * pos\r\n        , this.width + (this.destination.width - this.width) * pos\r\n        , this.height + (this.destination.height - this.height) * pos\r\n      ])\r\n\r\n    }\r\n\r\n  }\r\n\r\n  // Define parent\r\n, parent: SVG.Container\r\n\r\n  // Add parent method\r\n, construct: {\r\n\r\n    // get/set viewbox\r\n    viewbox: function(x, y, width, height) {\r\n      if (arguments.length == 0)\r\n        // act as a getter if there are no arguments\r\n        return new SVG.ViewBox(this)\r\n\r\n      // otherwise act as a setter\r\n      return this.attr('viewBox', new SVG.ViewBox(x, y, width, height))\r\n    }\r\n\r\n  }\r\n\r\n})\n// Add events to elements\r\n\r\n;[ 'click',\r\n  'dblclick',\r\n  'mousedown',\r\n  'mouseup',\r\n  'mouseover',\r\n  'mouseout',\r\n  'mousemove',\r\n  'mouseenter',\r\n  'mouseleave',\r\n  'touchstart',\r\n  'touchmove',\r\n  'touchleave',\r\n  'touchend',\r\n  'touchcancel' ].forEach(function (event) {\r\n    // add event to SVG.Element\r\n    SVG.Element.prototype[event] = function (f) {\r\n      // bind event to element rather than element node\r\n      if (f == null) {\r\n        SVG.off(this, event)\r\n      } else {\r\n        SVG.on(this, event, f)\r\n      }\r\n      return this\r\n    }\r\n  })\r\n\r\nSVG.listenerId = 0\r\n\r\n// Add event binder in the SVG namespace\r\nSVG.on = function (node, events, listener, binding, options) {\r\n  var l = listener.bind(binding || node)\r\n  var n = node instanceof SVG.Element ? node.node : node\r\n\r\n  // ensure instance object for nodes which are not adopted\r\n  n.instance = n.instance || {_events: {}}\r\n\r\n  var bag = n.instance._events\r\n\r\n  // add id to listener\r\n  if (!listener._svgjsListenerId) { listener._svgjsListenerId = ++SVG.listenerId }\r\n\r\n  events.split(SVG.regex.delimiter).forEach(function (event) {\r\n    var ev = event.split('.')[0]\r\n    var ns = event.split('.')[1] || '*'\r\n\r\n    // ensure valid object\r\n    bag[ev] = bag[ev] || {}\r\n    bag[ev][ns] = bag[ev][ns] || {}\r\n\r\n    // reference listener\r\n    bag[ev][ns][listener._svgjsListenerId] = l\r\n\r\n    // add listener\r\n    n.addEventListener(ev, l, options || false)\r\n  })\r\n}\r\n\r\n// Add event unbinder in the SVG namespace\r\nSVG.off = function (node, events, listener, options) {\r\n  var n = node instanceof SVG.Element ? node.node : node\r\n  if (!n.instance) return\r\n\r\n  // listener can be a function or a number\r\n  if (typeof listener === 'function') {\r\n    listener = listener._svgjsListenerId\r\n    if (!listener) return\r\n  }\r\n\r\n  var bag = n.instance._events\r\n\r\n  ;(events || '').split(SVG.regex.delimiter).forEach(function (event) {\r\n    var ev = event && event.split('.')[0]\r\n    var ns = event && event.split('.')[1]\r\n    var namespace, l\r\n\r\n    if (listener) {\r\n      // remove listener reference\r\n      if (bag[ev] && bag[ev][ns || '*']) {\r\n        // removeListener\r\n        n.removeEventListener(ev, bag[ev][ns || '*'][listener], options || false)\r\n\r\n        delete bag[ev][ns || '*'][listener]\r\n      }\r\n    } else if (ev && ns) {\r\n      // remove all listeners for a namespaced event\r\n      if (bag[ev] && bag[ev][ns]) {\r\n        for (l in bag[ev][ns]) { SVG.off(n, [ev, ns].join('.'), l) }\r\n\r\n        delete bag[ev][ns]\r\n      }\r\n    } else if (ns) {\r\n      // remove all listeners for a specific namespace\r\n      for (event in bag) {\r\n        for (namespace in bag[event]) {\r\n          if (ns === namespace) { SVG.off(n, [event, ns].join('.')) }\r\n        }\r\n      }\r\n    } else if (ev) {\r\n      // remove all listeners for the event\r\n      if (bag[ev]) {\r\n        for (namespace in bag[ev]) { SVG.off(n, [ev, namespace].join('.')) }\r\n\r\n        delete bag[ev]\r\n      }\r\n    } else {\r\n      // remove all listeners on a given node\r\n      for (event in bag) { SVG.off(n, event) }\r\n\r\n      n.instance._events = {}\r\n    }\r\n  })\r\n}\r\n\r\nSVG.extend(SVG.Element, {\r\n  // Bind given event to listener\r\n  on: function (event, listener, binding, options) {\r\n    SVG.on(this, event, listener, binding, options)\r\n    return this\r\n  },\r\n  // Unbind event from listener\r\n  off: function (event, listener) {\r\n    SVG.off(this.node, event, listener)\r\n    return this\r\n  },\r\n  fire: function (event, data) {\r\n    // Dispatch event\r\n    if (event instanceof window.Event) {\r\n      this.node.dispatchEvent(event)\r\n    } else {\r\n      this.node.dispatchEvent(event = new SVG.CustomEvent(event, {detail: data, cancelable: true}))\r\n    }\r\n    this._event = event\r\n    return this\r\n  },\r\n  event: function() {\r\n    return this._event\r\n  }\r\n})\r\n\n\r\nSVG.Defs = SVG.invent({\r\n  // Initialize node\r\n  create: 'defs'\r\n\r\n  // Inherit from\r\n, inherit: SVG.Container\r\n\r\n})\nSVG.G = SVG.invent({\r\n  // Initialize node\r\n  create: 'g'\r\n\r\n  // Inherit from\r\n, inherit: SVG.Container\r\n\r\n  // Add class methods\r\n, extend: {\r\n    // Move over x-axis\r\n    x: function(x) {\r\n      return x == null ? this.transform('x') : this.transform({ x: x - this.x() }, true)\r\n    }\r\n    // Move over y-axis\r\n  , y: function(y) {\r\n      return y == null ? this.transform('y') : this.transform({ y: y - this.y() }, true)\r\n    }\r\n    // Move by center over x-axis\r\n  , cx: function(x) {\r\n      return x == null ? this.gbox().cx : this.x(x - this.gbox().width / 2)\r\n    }\r\n    // Move by center over y-axis\r\n  , cy: function(y) {\r\n      return y == null ? this.gbox().cy : this.y(y - this.gbox().height / 2)\r\n    }\r\n  , gbox: function() {\r\n\r\n      var bbox  = this.bbox()\r\n        , trans = this.transform()\r\n\r\n      bbox.x  += trans.x\r\n      bbox.x2 += trans.x\r\n      bbox.cx += trans.x\r\n\r\n      bbox.y  += trans.y\r\n      bbox.y2 += trans.y\r\n      bbox.cy += trans.y\r\n\r\n      return bbox\r\n    }\r\n  }\r\n\r\n  // Add parent method\r\n, construct: {\r\n    // Create a group element\r\n    group: function() {\r\n      return this.put(new SVG.G)\r\n    }\r\n  }\r\n})\r\n\nSVG.Doc = SVG.invent({\r\n  // Initialize node\r\n  create: function(element) {\r\n    if (element) {\r\n      // ensure the presence of a dom element\r\n      element = typeof element == 'string' ?\r\n        document.getElementById(element) :\r\n        element\r\n\r\n      // If the target is an svg element, use that element as the main wrapper.\r\n      // This allows svg.js to work with svg documents as well.\r\n      if (element.nodeName == 'svg') {\r\n        this.constructor.call(this, element)\r\n      } else {\r\n        this.constructor.call(this, SVG.create('svg'))\r\n        element.appendChild(this.node)\r\n        this.size('100%', '100%')\r\n      }\r\n\r\n      // set svg element attributes and ensure defs node\r\n      this.namespace().defs()\r\n    }\r\n  }\r\n\r\n  // Inherit from\r\n, inherit: SVG.Container\r\n\r\n  // Add class methods\r\n, extend: {\r\n    // Add namespaces\r\n    namespace: function() {\r\n      return this\r\n        .attr({ xmlns: SVG.ns, version: '1.1' })\r\n        .attr('xmlns:xlink', SVG.xlink, SVG.xmlns)\r\n        .attr('xmlns:svgjs', SVG.svgjs, SVG.xmlns)\r\n    }\r\n    // Creates and returns defs element\r\n  , defs: function() {\r\n      if (!this._defs) {\r\n        var defs\r\n\r\n        // Find or create a defs element in this instance\r\n        if (defs = this.node.getElementsByTagName('defs')[0])\r\n          this._defs = SVG.adopt(defs)\r\n        else\r\n          this._defs = new SVG.Defs\r\n\r\n        // Make sure the defs node is at the end of the stack\r\n        this.node.appendChild(this._defs.node)\r\n      }\r\n\r\n      return this._defs\r\n    }\r\n    // custom parent method\r\n  , parent: function() {\r\n      if(!this.node.parentNode || this.node.parentNode.nodeName == '#document' || this.node.parentNode.nodeName == '#document-fragment') return null\r\n      return this.node.parentNode\r\n    }\r\n    // Fix for possible sub-pixel offset. See:\r\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=608812\r\n  , spof: function() {\r\n      var pos = this.node.getScreenCTM()\r\n\r\n      if (pos)\r\n        this\r\n          .style('left', (-pos.e % 1) + 'px')\r\n          .style('top',  (-pos.f % 1) + 'px')\r\n\r\n      return this\r\n    }\r\n\r\n      // Removes the doc from the DOM\r\n  , remove: function() {\r\n      if(this.parent()) {\r\n        this.parent().removeChild(this.node)\r\n      }\r\n\r\n      return this\r\n    }\r\n  , clear: function() {\r\n      // remove children\r\n      while(this.node.hasChildNodes())\r\n        this.node.removeChild(this.node.lastChild)\r\n\r\n      // remove defs reference\r\n      delete this._defs\r\n\r\n      // add back parser\r\n      if(!SVG.parser.draw.parentNode)\r\n        this.node.appendChild(SVG.parser.draw)\r\n\r\n      return this\r\n    }\r\n  , clone: function (parent) {\r\n      // write dom data to the dom so the clone can pickup the data\r\n      this.writeDataToDom()\r\n\r\n      // get reference to node\r\n      var node = this.node\r\n\r\n      // clone element and assign new id\r\n      var clone = assignNewId(node.cloneNode(true))\r\n\r\n      // insert the clone in the given parent or after myself\r\n      if(parent) {\r\n        (parent.node || parent).appendChild(clone.node)\r\n      } else {\r\n        node.parentNode.insertBefore(clone.node, node.nextSibling)\r\n      }\r\n\r\n      return clone\r\n    }\r\n  }\r\n\r\n})\r\n\n// ### This module adds backward / forward functionality to elements.\r\n\r\n//\r\nSVG.extend(SVG.Element, {\r\n  // Get all siblings, including myself\r\n  siblings: function() {\r\n    return this.parent().children()\r\n  }\r\n  // Get the curent position siblings\r\n, position: function() {\r\n    return this.parent().index(this)\r\n  }\r\n  // Get the next element (will return null if there is none)\r\n, next: function() {\r\n    return this.siblings()[this.position() + 1]\r\n  }\r\n  // Get the next element (will return null if there is none)\r\n, previous: function() {\r\n    return this.siblings()[this.position() - 1]\r\n  }\r\n  // Send given element one step forward\r\n, forward: function() {\r\n    var i = this.position() + 1\r\n      , p = this.parent()\r\n\r\n    // move node one step forward\r\n    p.removeElement(this).add(this, i)\r\n\r\n    // make sure defs node is always at the top\r\n    if (p instanceof SVG.Doc)\r\n      p.node.appendChild(p.defs().node)\r\n\r\n    return this\r\n  }\r\n  // Send given element one step backward\r\n, backward: function() {\r\n    var i = this.position()\r\n\r\n    if (i > 0)\r\n      this.parent().removeElement(this).add(this, i - 1)\r\n\r\n    return this\r\n  }\r\n  // Send given element all the way to the front\r\n, front: function() {\r\n    var p = this.parent()\r\n\r\n    // Move node forward\r\n    p.node.appendChild(this.node)\r\n\r\n    // Make sure defs node is always at the top\r\n    if (p instanceof SVG.Doc)\r\n      p.node.appendChild(p.defs().node)\r\n\r\n    return this\r\n  }\r\n  // Send given element all the way to the back\r\n, back: function() {\r\n    if (this.position() > 0)\r\n      this.parent().removeElement(this).add(this, 0)\r\n\r\n    return this\r\n  }\r\n  // Inserts a given element before the targeted element\r\n, before: function(element) {\r\n    element.remove()\r\n\r\n    var i = this.position()\r\n\r\n    this.parent().add(element, i)\r\n\r\n    return this\r\n  }\r\n  // Insters a given element after the targeted element\r\n, after: function(element) {\r\n    element.remove()\r\n\r\n    var i = this.position()\r\n\r\n    this.parent().add(element, i + 1)\r\n\r\n    return this\r\n  }\r\n\r\n})\nSVG.Mask = SVG.invent({\r\n  // Initialize node\r\n  create: function() {\r\n    this.constructor.call(this, SVG.create('mask'))\r\n\r\n    // keep references to masked elements\r\n    this.targets = []\r\n  }\r\n\r\n  // Inherit from\r\n, inherit: SVG.Container\r\n\r\n  // Add class methods\r\n, extend: {\r\n    // Unmask all masked elements and remove itself\r\n    remove: function() {\r\n      // unmask all targets\r\n      for (var i = this.targets.length - 1; i >= 0; i--)\r\n        if (this.targets[i])\r\n          this.targets[i].unmask()\r\n      this.targets = []\r\n\r\n      // remove mask from parent\r\n      SVG.Element.prototype.remove.call(this)\r\n\r\n      return this\r\n    }\r\n  }\r\n\r\n  // Add parent method\r\n, construct: {\r\n    // Create masking element\r\n    mask: function() {\r\n      return this.defs().put(new SVG.Mask)\r\n    }\r\n  }\r\n})\r\n\r\n\r\nSVG.extend(SVG.Element, {\r\n  // Distribute mask to svg element\r\n  maskWith: function(element) {\r\n    // use given mask or create a new one\r\n    this.masker = element instanceof SVG.Mask ? element : this.parent().mask().add(element)\r\n\r\n    // store reverence on self in mask\r\n    this.masker.targets.push(this)\r\n\r\n    // apply mask\r\n    return this.attr('mask', 'url(\"#' + this.masker.attr('id') + '\")')\r\n  }\r\n  // Unmask element\r\n, unmask: function() {\r\n    delete this.masker\r\n    return this.attr('mask', null)\r\n  }\r\n\r\n})\r\n\nSVG.ClipPath = SVG.invent({\r\n  // Initialize node\r\n  create: function() {\r\n    this.constructor.call(this, SVG.create('clipPath'))\r\n\r\n    // keep references to clipped elements\r\n    this.targets = []\r\n  }\r\n\r\n  // Inherit from\r\n, inherit: SVG.Container\r\n\r\n  // Add class methods\r\n, extend: {\r\n    // Unclip all clipped elements and remove itself\r\n    remove: function() {\r\n      // unclip all targets\r\n      for (var i = this.targets.length - 1; i >= 0; i--)\r\n        if (this.targets[i])\r\n          this.targets[i].unclip()\r\n      this.targets = []\r\n\r\n      // remove clipPath from parent\r\n      this.parent().removeElement(this)\r\n\r\n      return this\r\n    }\r\n  }\r\n\r\n  // Add parent method\r\n, construct: {\r\n    // Create clipping element\r\n    clip: function() {\r\n      return this.defs().put(new SVG.ClipPath)\r\n    }\r\n  }\r\n})\r\n\r\n//\r\nSVG.extend(SVG.Element, {\r\n  // Distribute clipPath to svg element\r\n  clipWith: function(element) {\r\n    // use given clip or create a new one\r\n    this.clipper = element instanceof SVG.ClipPath ? element : this.parent().clip().add(element)\r\n\r\n    // store reverence on self in mask\r\n    this.clipper.targets.push(this)\r\n\r\n    // apply mask\r\n    return this.attr('clip-path', 'url(\"#' + this.clipper.attr('id') + '\")')\r\n  }\r\n  // Unclip element\r\n, unclip: function() {\r\n    delete this.clipper\r\n    return this.attr('clip-path', null)\r\n  }\r\n\r\n})\nSVG.Gradient = SVG.invent({\r\n  // Initialize node\r\n  create: function(type) {\r\n    this.constructor.call(this, SVG.create(type + 'Gradient'))\r\n\r\n    // store type\r\n    this.type = type\r\n  }\r\n\r\n  // Inherit from\r\n, inherit: SVG.Container\r\n\r\n  // Add class methods\r\n, extend: {\r\n    // Add a color stop\r\n    at: function(offset, color, opacity) {\r\n      return this.put(new SVG.Stop).update(offset, color, opacity)\r\n    }\r\n    // Update gradient\r\n  , update: function(block) {\r\n      // remove all stops\r\n      this.clear()\r\n\r\n      // invoke passed block\r\n      if (typeof block == 'function')\r\n        block.call(this, this)\r\n\r\n      return this\r\n    }\r\n    // Return the fill id\r\n  , fill: function() {\r\n      return 'url(#' + this.id() + ')'\r\n    }\r\n    // Alias string convertion to fill\r\n  , toString: function() {\r\n      return this.fill()\r\n    }\r\n    // custom attr to handle transform\r\n  , attr: function(a, b, c) {\r\n      if(a == 'transform') a = 'gradientTransform'\r\n      return SVG.Container.prototype.attr.call(this, a, b, c)\r\n    }\r\n  }\r\n\r\n  // Add parent method\r\n, construct: {\r\n    // Create gradient element in defs\r\n    gradient: function(type, block) {\r\n      return this.defs().gradient(type, block)\r\n    }\r\n  }\r\n})\r\n\r\n// Add animatable methods to both gradient and fx module\r\nSVG.extend(SVG.Gradient, SVG.FX, {\r\n  // From position\r\n  from: function(x, y) {\r\n    return (this._target || this).type == 'radial' ?\r\n      this.attr({ fx: new SVG.Number(x), fy: new SVG.Number(y) }) :\r\n      this.attr({ x1: new SVG.Number(x), y1: new SVG.Number(y) })\r\n  }\r\n  // To position\r\n, to: function(x, y) {\r\n    return (this._target || this).type == 'radial' ?\r\n      this.attr({ cx: new SVG.Number(x), cy: new SVG.Number(y) }) :\r\n      this.attr({ x2: new SVG.Number(x), y2: new SVG.Number(y) })\r\n  }\r\n})\r\n\r\n// Base gradient generation\r\nSVG.extend(SVG.Defs, {\r\n  // define gradient\r\n  gradient: function(type, block) {\r\n    return this.put(new SVG.Gradient(type)).update(block)\r\n  }\r\n\r\n})\r\n\r\nSVG.Stop = SVG.invent({\r\n  // Initialize node\r\n  create: 'stop'\r\n\r\n  // Inherit from\r\n, inherit: SVG.Element\r\n\r\n  // Add class methods\r\n, extend: {\r\n    // add color stops\r\n    update: function(o) {\r\n      if (typeof o == 'number' || o instanceof SVG.Number) {\r\n        o = {\r\n          offset:  arguments[0]\r\n        , color:   arguments[1]\r\n        , opacity: arguments[2]\r\n        }\r\n      }\r\n\r\n      // set attributes\r\n      if (o.opacity != null) this.attr('stop-opacity', o.opacity)\r\n      if (o.color   != null) this.attr('stop-color', o.color)\r\n      if (o.offset  != null) this.attr('offset', new SVG.Number(o.offset))\r\n\r\n      return this\r\n    }\r\n  }\r\n\r\n})\r\n\nSVG.Pattern = SVG.invent({\r\n  // Initialize node\r\n  create: 'pattern'\r\n\r\n  // Inherit from\r\n, inherit: SVG.Container\r\n\r\n  // Add class methods\r\n, extend: {\r\n    // Return the fill id\r\n    fill: function() {\r\n      return 'url(#' + this.id() + ')'\r\n    }\r\n    // Update pattern by rebuilding\r\n  , update: function(block) {\r\n      // remove content\r\n      this.clear()\r\n\r\n      // invoke passed block\r\n      if (typeof block == 'function')\r\n        block.call(this, this)\r\n\r\n      return this\r\n    }\r\n    // Alias string convertion to fill\r\n  , toString: function() {\r\n      return this.fill()\r\n    }\r\n    // custom attr to handle transform\r\n  , attr: function(a, b, c) {\r\n      if(a == 'transform') a = 'patternTransform'\r\n      return SVG.Container.prototype.attr.call(this, a, b, c)\r\n    }\r\n\r\n  }\r\n\r\n  // Add parent method\r\n, construct: {\r\n    // Create pattern element in defs\r\n    pattern: function(width, height, block) {\r\n      return this.defs().pattern(width, height, block)\r\n    }\r\n  }\r\n})\r\n\r\nSVG.extend(SVG.Defs, {\r\n  // Define gradient\r\n  pattern: function(width, height, block) {\r\n    return this.put(new SVG.Pattern).update(block).attr({\r\n      x:            0\r\n    , y:            0\r\n    , width:        width\r\n    , height:       height\r\n    , patternUnits: 'userSpaceOnUse'\r\n    })\r\n  }\r\n\r\n})\nSVG.Shape = SVG.invent({\r\n  // Initialize node\r\n  create: function(element) {\r\n    this.constructor.call(this, element)\r\n  }\r\n\r\n  // Inherit from\r\n, inherit: SVG.Element\r\n\r\n})\n\r\nSVG.Bare = SVG.invent({\r\n  // Initialize\r\n  create: function(element, inherit) {\r\n    // construct element\r\n    this.constructor.call(this, SVG.create(element))\r\n\r\n    // inherit custom methods\r\n    if (inherit)\r\n      for (var method in inherit.prototype)\r\n        if (typeof inherit.prototype[method] === 'function')\r\n          this[method] = inherit.prototype[method]\r\n  }\r\n\r\n  // Inherit from\r\n, inherit: SVG.Element\r\n\r\n  // Add methods\r\n, extend: {\r\n    // Insert some plain text\r\n    words: function(text) {\r\n      // remove contents\r\n      while (this.node.hasChildNodes())\r\n        this.node.removeChild(this.node.lastChild)\r\n\r\n      // create text node\r\n      this.node.appendChild(document.createTextNode(text))\r\n\r\n      return this\r\n    }\r\n  }\r\n})\r\n\r\n\r\nSVG.extend(SVG.Parent, {\r\n  // Create an element that is not described by SVG.js\r\n  element: function(element, inherit) {\r\n    return this.put(new SVG.Bare(element, inherit))\r\n  }\r\n})\r\n\nSVG.Symbol = SVG.invent({\r\n  // Initialize node\r\n  create: 'symbol'\r\n\r\n  // Inherit from\r\n, inherit: SVG.Container\r\n\r\n, construct: {\r\n    // create symbol\r\n    symbol: function() {\r\n      return this.put(new SVG.Symbol)\r\n    }\r\n  }\r\n})\r\n\nSVG.Use = SVG.invent({\r\n  // Initialize node\r\n  create: 'use'\r\n\r\n  // Inherit from\r\n, inherit: SVG.Shape\r\n\r\n  // Add class methods\r\n, extend: {\r\n    // Use element as a reference\r\n    element: function(element, file) {\r\n      // Set lined element\r\n      return this.attr('href', (file || '') + '#' + element, SVG.xlink)\r\n    }\r\n  }\r\n\r\n  // Add parent method\r\n, construct: {\r\n    // Create a use element\r\n    use: function(element, file) {\r\n      return this.put(new SVG.Use).element(element, file)\r\n    }\r\n  }\r\n})\nSVG.Rect = SVG.invent({\r\n  // Initialize node\r\n  create: 'rect'\r\n\r\n  // Inherit from\r\n, inherit: SVG.Shape\r\n\r\n  // Add parent method\r\n, construct: {\r\n    // Create a rect element\r\n    rect: function(width, height) {\r\n      return this.put(new SVG.Rect()).size(width, height)\r\n    }\r\n  }\r\n})\nSVG.Circle = SVG.invent({\r\n  // Initialize node\r\n  create: 'circle'\r\n\r\n  // Inherit from\r\n, inherit: SVG.Shape\r\n\r\n  // Add parent method\r\n, construct: {\r\n    // Create circle element, based on ellipse\r\n    circle: function(size) {\r\n      return this.put(new SVG.Circle).rx(new SVG.Number(size).divide(2)).move(0, 0)\r\n    }\r\n  }\r\n})\r\n\r\nSVG.extend(SVG.Circle, SVG.FX, {\r\n  // Radius x value\r\n  rx: function(rx) {\r\n    return this.attr('r', rx)\r\n  }\r\n  // Alias radius x value\r\n, ry: function(ry) {\r\n    return this.rx(ry)\r\n  }\r\n})\r\n\r\nSVG.Ellipse = SVG.invent({\r\n  // Initialize node\r\n  create: 'ellipse'\r\n\r\n  // Inherit from\r\n, inherit: SVG.Shape\r\n\r\n  // Add parent method\r\n, construct: {\r\n    // Create an ellipse\r\n    ellipse: function(width, height) {\r\n      return this.put(new SVG.Ellipse).size(width, height).move(0, 0)\r\n    }\r\n  }\r\n})\r\n\r\nSVG.extend(SVG.Ellipse, SVG.Rect, SVG.FX, {\r\n  // Radius x value\r\n  rx: function(rx) {\r\n    return this.attr('rx', rx)\r\n  }\r\n  // Radius y value\r\n, ry: function(ry) {\r\n    return this.attr('ry', ry)\r\n  }\r\n})\r\n\r\n// Add common method\r\nSVG.extend(SVG.Circle, SVG.Ellipse, {\r\n    // Move over x-axis\r\n    x: function(x) {\r\n      return x == null ? this.cx() - this.rx() : this.cx(x + this.rx())\r\n    }\r\n    // Move over y-axis\r\n  , y: function(y) {\r\n      return y == null ? this.cy() - this.ry() : this.cy(y + this.ry())\r\n    }\r\n    // Move by center over x-axis\r\n  , cx: function(x) {\r\n      return x == null ? this.attr('cx') : this.attr('cx', x)\r\n    }\r\n    // Move by center over y-axis\r\n  , cy: function(y) {\r\n      return y == null ? this.attr('cy') : this.attr('cy', y)\r\n    }\r\n    // Set width of element\r\n  , width: function(width) {\r\n      return width == null ? this.rx() * 2 : this.rx(new SVG.Number(width).divide(2))\r\n    }\r\n    // Set height of element\r\n  , height: function(height) {\r\n      return height == null ? this.ry() * 2 : this.ry(new SVG.Number(height).divide(2))\r\n    }\r\n    // Custom size function\r\n  , size: function(width, height) {\r\n      var p = proportionalSize(this, width, height)\r\n\r\n      return this\r\n        .rx(new SVG.Number(p.width).divide(2))\r\n        .ry(new SVG.Number(p.height).divide(2))\r\n    }\r\n})\nSVG.Line = SVG.invent({\r\n  // Initialize node\r\n  create: 'line'\r\n\r\n  // Inherit from\r\n, inherit: SVG.Shape\r\n\r\n  // Add class methods\r\n, extend: {\r\n    // Get array\r\n    array: function() {\r\n      return new SVG.PointArray([\r\n        [ this.attr('x1'), this.attr('y1') ]\r\n      , [ this.attr('x2'), this.attr('y2') ]\r\n      ])\r\n    }\r\n    // Overwrite native plot() method\r\n  , plot: function(x1, y1, x2, y2) {\r\n      if (x1 == null)\r\n        return this.array()\r\n      else if (typeof y1 !== 'undefined')\r\n        x1 = { x1: x1, y1: y1, x2: x2, y2: y2 }\r\n      else\r\n        x1 = new SVG.PointArray(x1).toLine()\r\n\r\n      return this.attr(x1)\r\n    }\r\n    // Move by left top corner\r\n  , move: function(x, y) {\r\n      return this.attr(this.array().move(x, y).toLine())\r\n    }\r\n    // Set element size to given width and height\r\n  , size: function(width, height) {\r\n      var p = proportionalSize(this, width, height)\r\n\r\n      return this.attr(this.array().size(p.width, p.height).toLine())\r\n    }\r\n  }\r\n\r\n  // Add parent method\r\n, construct: {\r\n    // Create a line element\r\n    line: function(x1, y1, x2, y2) {\r\n      // make sure plot is called as a setter\r\n      // x1 is not necessarily a number, it can also be an array, a string and a SVG.PointArray\r\n      return SVG.Line.prototype.plot.apply(\r\n        this.put(new SVG.Line)\r\n      , x1 != null ? [x1, y1, x2, y2] : [0, 0, 0, 0]\r\n      )\r\n    }\r\n  }\r\n})\r\n\nSVG.Polyline = SVG.invent({\r\n  // Initialize node\r\n  create: 'polyline'\r\n\r\n  // Inherit from\r\n, inherit: SVG.Shape\r\n\r\n  // Add parent method\r\n, construct: {\r\n    // Create a wrapped polyline element\r\n    polyline: function(p) {\r\n      // make sure plot is called as a setter\r\n      return this.put(new SVG.Polyline).plot(p || new SVG.PointArray)\r\n    }\r\n  }\r\n})\r\n\r\nSVG.Polygon = SVG.invent({\r\n  // Initialize node\r\n  create: 'polygon'\r\n\r\n  // Inherit from\r\n, inherit: SVG.Shape\r\n\r\n  // Add parent method\r\n, construct: {\r\n    // Create a wrapped polygon element\r\n    polygon: function(p) {\r\n      // make sure plot is called as a setter\r\n      return this.put(new SVG.Polygon).plot(p || new SVG.PointArray)\r\n    }\r\n  }\r\n})\r\n\r\n// Add polygon-specific functions\r\nSVG.extend(SVG.Polyline, SVG.Polygon, {\r\n  // Get array\r\n  array: function() {\r\n    return this._array || (this._array = new SVG.PointArray(this.attr('points')))\r\n  }\r\n  // Plot new path\r\n, plot: function(p) {\r\n    return (p == null) ?\r\n      this.array() :\r\n      this.clear().attr('points', typeof p == 'string' ? p : (this._array = new SVG.PointArray(p)))\r\n  }\r\n  // Clear array cache\r\n, clear: function() {\r\n    delete this._array\r\n    return this\r\n  }\r\n  // Move by left top corner\r\n, move: function(x, y) {\r\n    return this.attr('points', this.array().move(x, y))\r\n  }\r\n  // Set element size to given width and height\r\n, size: function(width, height) {\r\n    var p = proportionalSize(this, width, height)\r\n\r\n    return this.attr('points', this.array().size(p.width, p.height))\r\n  }\r\n\r\n})\r\n\n// unify all point to point elements\r\nSVG.extend(SVG.Line, SVG.Polyline, SVG.Polygon, {\r\n  // Define morphable array\r\n  morphArray:  SVG.PointArray\r\n  // Move by left top corner over x-axis\r\n, x: function(x) {\r\n    return x == null ? this.bbox().x : this.move(x, this.bbox().y)\r\n  }\r\n  // Move by left top corner over y-axis\r\n, y: function(y) {\r\n    return y == null ? this.bbox().y : this.move(this.bbox().x, y)\r\n  }\r\n  // Set width of element\r\n, width: function(width) {\r\n    var b = this.bbox()\r\n\r\n    return width == null ? b.width : this.size(width, b.height)\r\n  }\r\n  // Set height of element\r\n, height: function(height) {\r\n    var b = this.bbox()\r\n\r\n    return height == null ? b.height : this.size(b.width, height)\r\n  }\r\n})\nSVG.Path = SVG.invent({\r\n  // Initialize node\r\n  create: 'path'\r\n\r\n  // Inherit from\r\n, inherit: SVG.Shape\r\n\r\n  // Add class methods\r\n, extend: {\r\n    // Define morphable array\r\n    morphArray:  SVG.PathArray\r\n    // Get array\r\n  , array: function() {\r\n      return this._array || (this._array = new SVG.PathArray(this.attr('d')))\r\n    }\r\n    // Plot new path\r\n  , plot: function(d) {\r\n      return (d == null) ?\r\n        this.array() :\r\n        this.clear().attr('d', typeof d == 'string' ? d : (this._array = new SVG.PathArray(d)))\r\n    }\r\n    // Clear array cache\r\n  , clear: function() {\r\n      delete this._array\r\n      return this\r\n    }\r\n    // Move by left top corner\r\n  , move: function(x, y) {\r\n      return this.attr('d', this.array().move(x, y))\r\n    }\r\n    // Move by left top corner over x-axis\r\n  , x: function(x) {\r\n      return x == null ? this.bbox().x : this.move(x, this.bbox().y)\r\n    }\r\n    // Move by left top corner over y-axis\r\n  , y: function(y) {\r\n      return y == null ? this.bbox().y : this.move(this.bbox().x, y)\r\n    }\r\n    // Set element size to given width and height\r\n  , size: function(width, height) {\r\n      var p = proportionalSize(this, width, height)\r\n\r\n      return this.attr('d', this.array().size(p.width, p.height))\r\n    }\r\n    // Set width of element\r\n  , width: function(width) {\r\n      return width == null ? this.bbox().width : this.size(width, this.bbox().height)\r\n    }\r\n    // Set height of element\r\n  , height: function(height) {\r\n      return height == null ? this.bbox().height : this.size(this.bbox().width, height)\r\n    }\r\n\r\n  }\r\n\r\n  // Add parent method\r\n, construct: {\r\n    // Create a wrapped path element\r\n    path: function(d) {\r\n      // make sure plot is called as a setter\r\n      return this.put(new SVG.Path).plot(d || new SVG.PathArray)\r\n    }\r\n  }\r\n})\r\n\nSVG.Image = SVG.invent({\r\n  // Initialize node\r\n  create: 'image'\r\n\r\n  // Inherit from\r\n, inherit: SVG.Shape\r\n\r\n  // Add class methods\r\n, extend: {\r\n    // (re)load image\r\n    load: function(url) {\r\n      if (!url) return this\r\n\r\n      var self = this\r\n        , img  = new window.Image()\r\n\r\n      // preload image\r\n      SVG.on(img, 'load', function() {\r\n        SVG.off(img)\r\n\r\n        var p = self.parent(SVG.Pattern)\r\n\r\n        if(p === null) return\r\n\r\n        // ensure image size\r\n        if (self.width() == 0 && self.height() == 0)\r\n          self.size(img.width, img.height)\r\n\r\n        // ensure pattern size if not set\r\n        if (p && p.width() == 0 && p.height() == 0)\r\n          p.size(self.width(), self.height())\r\n\r\n        // callback\r\n        if (typeof self._loaded === 'function')\r\n          self._loaded.call(self, {\r\n            width:  img.width\r\n          , height: img.height\r\n          , ratio:  img.width / img.height\r\n          , url:    url\r\n          })\r\n      })\r\n\r\n      SVG.on(img, 'error', function(e){\r\n        SVG.off(img)\r\n\r\n        if (typeof self._error === 'function'){\r\n            self._error.call(self, e)\r\n        }\r\n      })\r\n\r\n      return this.attr('href', (img.src = this.src = url), SVG.xlink)\r\n    }\r\n    // Add loaded callback\r\n  , loaded: function(loaded) {\r\n      this._loaded = loaded\r\n      return this\r\n    }\r\n\r\n  , error: function(error) {\r\n      this._error = error\r\n      return this\r\n    }\r\n  }\r\n\r\n  // Add parent method\r\n, construct: {\r\n    // create image element, load image and set its size\r\n    image: function(source, width, height) {\r\n      return this.put(new SVG.Image).load(source).size(width || 0, height || width || 0)\r\n    }\r\n  }\r\n\r\n})\nSVG.Text = SVG.invent({\r\n  // Initialize node\r\n  create: function() {\r\n    this.constructor.call(this, SVG.create('text'))\r\n\r\n    this.dom.leading = new SVG.Number(1.3)    // store leading value for rebuilding\r\n    this._rebuild = true                      // enable automatic updating of dy values\r\n    this._build   = false                     // disable build mode for adding multiple lines\r\n\r\n    // set default font\r\n    this.attr('font-family', SVG.defaults.attrs['font-family'])\r\n  }\r\n\r\n  // Inherit from\r\n, inherit: SVG.Shape\r\n\r\n  // Add class methods\r\n, extend: {\r\n    // Move over x-axis\r\n    x: function(x) {\r\n      // act as getter\r\n      if (x == null)\r\n        return this.attr('x')\r\n\r\n      return this.attr('x', x)\r\n    }\r\n    // Move over y-axis\r\n  , y: function(y) {\r\n      var oy = this.attr('y')\r\n        , o  = typeof oy === 'number' ? oy - this.bbox().y : 0\r\n\r\n      // act as getter\r\n      if (y == null)\r\n        return typeof oy === 'number' ? oy - o : oy\r\n\r\n      return this.attr('y', typeof y.valueOf() === 'number' ? y + o : y)\r\n    }\r\n    // Move center over x-axis\r\n  , cx: function(x) {\r\n      return x == null ? this.bbox().cx : this.x(x - this.bbox().width / 2)\r\n    }\r\n    // Move center over y-axis\r\n  , cy: function(y) {\r\n      return y == null ? this.bbox().cy : this.y(y - this.bbox().height / 2)\r\n    }\r\n    // Set the text content\r\n  , text: function(text) {\r\n      // act as getter\r\n      if (typeof text === 'undefined'){\r\n        var text = ''\r\n        var children = this.node.childNodes\r\n        for(var i = 0, len = children.length; i < len; ++i){\r\n\r\n          // add newline if its not the first child and newLined is set to true\r\n          if(i != 0 && children[i].nodeType != 3 && SVG.adopt(children[i]).dom.newLined == true){\r\n            text += '\\n'\r\n          }\r\n\r\n          // add content of this node\r\n          text += children[i].textContent\r\n        }\r\n\r\n        return text\r\n      }\r\n\r\n      // remove existing content\r\n      this.clear().build(true)\r\n\r\n      if (typeof text === 'function') {\r\n        // call block\r\n        text.call(this, this)\r\n\r\n      } else {\r\n        // store text and make sure text is not blank\r\n        text = text.split('\\n')\r\n\r\n        // build new lines\r\n        for (var i = 0, il = text.length; i < il; i++)\r\n          this.tspan(text[i]).newLine()\r\n      }\r\n\r\n      // disable build mode and rebuild lines\r\n      return this.build(false).rebuild()\r\n    }\r\n    // Set font size\r\n  , size: function(size) {\r\n      return this.attr('font-size', size).rebuild()\r\n    }\r\n    // Set / get leading\r\n  , leading: function(value) {\r\n      // act as getter\r\n      if (value == null)\r\n        return this.dom.leading\r\n\r\n      // act as setter\r\n      this.dom.leading = new SVG.Number(value)\r\n\r\n      return this.rebuild()\r\n    }\r\n    // Get all the first level lines\r\n  , lines: function() {\r\n      var node = (this.textPath && this.textPath() || this).node\r\n\r\n      // filter tspans and map them to SVG.js instances\r\n      var lines = SVG.utils.map(SVG.utils.filterSVGElements(node.childNodes), function(el){\r\n        return SVG.adopt(el)\r\n      })\r\n\r\n      // return an instance of SVG.set\r\n      return new SVG.Set(lines)\r\n    }\r\n    // Rebuild appearance type\r\n  , rebuild: function(rebuild) {\r\n      // store new rebuild flag if given\r\n      if (typeof rebuild == 'boolean')\r\n        this._rebuild = rebuild\r\n\r\n      // define position of all lines\r\n      if (this._rebuild) {\r\n        var self = this\r\n          , blankLineOffset = 0\r\n          , dy = this.dom.leading * new SVG.Number(this.attr('font-size'))\r\n\r\n        this.lines().each(function() {\r\n          if (this.dom.newLined) {\r\n            if (!self.textPath())\r\n              this.attr('x', self.attr('x'))\r\n            if(this.text() == '\\n') {\r\n              blankLineOffset += dy\r\n            }else{\r\n              this.attr('dy', dy + blankLineOffset)\r\n              blankLineOffset = 0\r\n            }\r\n          }\r\n        })\r\n\r\n        this.fire('rebuild')\r\n      }\r\n\r\n      return this\r\n    }\r\n    // Enable / disable build mode\r\n  , build: function(build) {\r\n      this._build = !!build\r\n      return this\r\n    }\r\n    // overwrite method from parent to set data properly\r\n  , setData: function(o){\r\n      this.dom = o\r\n      this.dom.leading = new SVG.Number(o.leading || 1.3)\r\n      return this\r\n    }\r\n  }\r\n\r\n  // Add parent method\r\n, construct: {\r\n    // Create text element\r\n    text: function(text) {\r\n      return this.put(new SVG.Text).text(text)\r\n    }\r\n    // Create plain text element\r\n  , plain: function(text) {\r\n      return this.put(new SVG.Text).plain(text)\r\n    }\r\n  }\r\n\r\n})\r\n\r\nSVG.Tspan = SVG.invent({\r\n  // Initialize node\r\n  create: 'tspan'\r\n\r\n  // Inherit from\r\n, inherit: SVG.Shape\r\n\r\n  // Add class methods\r\n, extend: {\r\n    // Set text content\r\n    text: function(text) {\r\n      if(text == null) return this.node.textContent + (this.dom.newLined ? '\\n' : '')\r\n\r\n      typeof text === 'function' ? text.call(this, this) : this.plain(text)\r\n\r\n      return this\r\n    }\r\n    // Shortcut dx\r\n  , dx: function(dx) {\r\n      return this.attr('dx', dx)\r\n    }\r\n    // Shortcut dy\r\n  , dy: function(dy) {\r\n      return this.attr('dy', dy)\r\n    }\r\n    // Create new line\r\n  , newLine: function() {\r\n      // fetch text parent\r\n      var t = this.parent(SVG.Text)\r\n\r\n      // mark new line\r\n      this.dom.newLined = true\r\n\r\n      // apply new hy¡n\r\n      return this.dy(t.dom.leading * t.attr('font-size')).attr('x', t.x())\r\n    }\r\n  }\r\n\r\n})\r\n\r\nSVG.extend(SVG.Text, SVG.Tspan, {\r\n  // Create plain text node\r\n  plain: function(text) {\r\n    // clear if build mode is disabled\r\n    if (this._build === false)\r\n      this.clear()\r\n\r\n    // create text node\r\n    this.node.appendChild(document.createTextNode(text))\r\n\r\n    return this\r\n  }\r\n  // Create a tspan\r\n, tspan: function(text) {\r\n    var node  = (this.textPath && this.textPath() || this).node\r\n      , tspan = new SVG.Tspan\r\n\r\n    // clear if build mode is disabled\r\n    if (this._build === false)\r\n      this.clear()\r\n\r\n    // add new tspan\r\n    node.appendChild(tspan.node)\r\n\r\n    return tspan.text(text)\r\n  }\r\n  // Clear all lines\r\n, clear: function() {\r\n    var node = (this.textPath && this.textPath() || this).node\r\n\r\n    // remove existing child nodes\r\n    while (node.hasChildNodes())\r\n      node.removeChild(node.lastChild)\r\n\r\n    return this\r\n  }\r\n  // Get length of text element\r\n, length: function() {\r\n    return this.node.getComputedTextLength()\r\n  }\r\n})\r\n\nSVG.TextPath = SVG.invent({\r\n  // Initialize node\r\n  create: 'textPath'\r\n\r\n  // Inherit from\r\n, inherit: SVG.Parent\r\n\r\n  // Define parent class\r\n, parent: SVG.Text\r\n\r\n  // Add parent method\r\n, construct: {\r\n    morphArray: SVG.PathArray\r\n    // Create path for text to run on\r\n  , path: function(d) {\r\n      // create textPath element\r\n      var path  = new SVG.TextPath\r\n        , track = this.doc().defs().path(d)\r\n\r\n      // move lines to textpath\r\n      while (this.node.hasChildNodes())\r\n        path.node.appendChild(this.node.firstChild)\r\n\r\n      // add textPath element as child node\r\n      this.node.appendChild(path.node)\r\n\r\n      // link textPath to path and add content\r\n      path.attr('href', '#' + track, SVG.xlink)\r\n\r\n      return this\r\n    }\r\n    // return the array of the path track element\r\n  , array: function() {\r\n      var track = this.track()\r\n\r\n      return track ? track.array() : null\r\n    }\r\n    // Plot path if any\r\n  , plot: function(d) {\r\n      var track = this.track()\r\n        , pathArray = null\r\n\r\n      if (track) {\r\n        pathArray = track.plot(d)\r\n      }\r\n\r\n      return (d == null) ? pathArray : this\r\n    }\r\n    // Get the path track element\r\n  , track: function() {\r\n      var path = this.textPath()\r\n\r\n      if (path)\r\n        return path.reference('href')\r\n    }\r\n    // Get the textPath child\r\n  , textPath: function() {\r\n      if (this.node.firstChild && this.node.firstChild.nodeName == 'textPath')\r\n        return SVG.adopt(this.node.firstChild)\r\n    }\r\n  }\r\n})\r\n\nSVG.Nested = SVG.invent({\r\n  // Initialize node\r\n  create: function() {\r\n    this.constructor.call(this, SVG.create('svg'))\r\n\r\n    this.style('overflow', 'visible')\r\n  }\r\n\r\n  // Inherit from\r\n, inherit: SVG.Container\r\n\r\n  // Add parent method\r\n, construct: {\r\n    // Create nested svg document\r\n    nested: function() {\r\n      return this.put(new SVG.Nested)\r\n    }\r\n  }\r\n})\nSVG.A = SVG.invent({\r\n  // Initialize node\r\n  create: 'a'\r\n\r\n  // Inherit from\r\n, inherit: SVG.Container\r\n\r\n  // Add class methods\r\n, extend: {\r\n    // Link url\r\n    to: function(url) {\r\n      return this.attr('href', url, SVG.xlink)\r\n    }\r\n    // Link show attribute\r\n  , show: function(target) {\r\n      return this.attr('show', target, SVG.xlink)\r\n    }\r\n    // Link target attribute\r\n  , target: function(target) {\r\n      return this.attr('target', target)\r\n    }\r\n  }\r\n\r\n  // Add parent method\r\n, construct: {\r\n    // Create a hyperlink element\r\n    link: function(url) {\r\n      return this.put(new SVG.A).to(url)\r\n    }\r\n  }\r\n})\r\n\r\nSVG.extend(SVG.Element, {\r\n  // Create a hyperlink element\r\n  linkTo: function(url) {\r\n    var link = new SVG.A\r\n\r\n    if (typeof url == 'function')\r\n      url.call(link, link)\r\n    else\r\n      link.to(url)\r\n\r\n    return this.parent().put(link).put(this)\r\n  }\r\n\r\n})\nSVG.Marker = SVG.invent({\r\n  // Initialize node\r\n  create: 'marker'\r\n\r\n  // Inherit from\r\n, inherit: SVG.Container\r\n\r\n  // Add class methods\r\n, extend: {\r\n    // Set width of element\r\n    width: function(width) {\r\n      return this.attr('markerWidth', width)\r\n    }\r\n    // Set height of element\r\n  , height: function(height) {\r\n      return this.attr('markerHeight', height)\r\n    }\r\n    // Set marker refX and refY\r\n  , ref: function(x, y) {\r\n      return this.attr('refX', x).attr('refY', y)\r\n    }\r\n    // Update marker\r\n  , update: function(block) {\r\n      // remove all content\r\n      this.clear()\r\n\r\n      // invoke passed block\r\n      if (typeof block == 'function')\r\n        block.call(this, this)\r\n\r\n      return this\r\n    }\r\n    // Return the fill id\r\n  , toString: function() {\r\n      return 'url(#' + this.id() + ')'\r\n    }\r\n  }\r\n\r\n  // Add parent method\r\n, construct: {\r\n    marker: function(width, height, block) {\r\n      // Create marker element in defs\r\n      return this.defs().marker(width, height, block)\r\n    }\r\n  }\r\n\r\n})\r\n\r\nSVG.extend(SVG.Defs, {\r\n  // Create marker\r\n  marker: function(width, height, block) {\r\n    // Set default viewbox to match the width and height, set ref to cx and cy and set orient to auto\r\n    return this.put(new SVG.Marker)\r\n      .size(width, height)\r\n      .ref(width / 2, height / 2)\r\n      .viewbox(0, 0, width, height)\r\n      .attr('orient', 'auto')\r\n      .update(block)\r\n  }\r\n\r\n})\r\n\r\nSVG.extend(SVG.Line, SVG.Polyline, SVG.Polygon, SVG.Path, {\r\n  // Create and attach markers\r\n  marker: function(marker, width, height, block) {\r\n    var attr = ['marker']\r\n\r\n    // Build attribute name\r\n    if (marker != 'all') attr.push(marker)\r\n    attr = attr.join('-')\r\n\r\n    // Set marker attribute\r\n    marker = arguments[1] instanceof SVG.Marker ?\r\n      arguments[1] :\r\n      this.doc().marker(width, height, block)\r\n\r\n    return this.attr(attr, marker)\r\n  }\r\n\r\n})\n// Define list of available attributes for stroke and fill\r\nvar sugar = {\r\n  stroke: ['color', 'width', 'opacity', 'linecap', 'linejoin', 'miterlimit', 'dasharray', 'dashoffset']\r\n, fill:   ['color', 'opacity', 'rule']\r\n, prefix: function(t, a) {\r\n    return a == 'color' ? t : t + '-' + a\r\n  }\r\n}\r\n\r\n// Add sugar for fill and stroke\r\n;['fill', 'stroke'].forEach(function(m) {\r\n  var i, extension = {}\r\n\r\n  extension[m] = function(o) {\r\n    if (typeof o == 'undefined')\r\n      return this\r\n    if (typeof o == 'string' || SVG.Color.isRgb(o) || (o && typeof o.fill === 'function'))\r\n      this.attr(m, o)\r\n\r\n    else\r\n      // set all attributes from sugar.fill and sugar.stroke list\r\n      for (i = sugar[m].length - 1; i >= 0; i--)\r\n        if (o[sugar[m][i]] != null)\r\n          this.attr(sugar.prefix(m, sugar[m][i]), o[sugar[m][i]])\r\n\r\n    return this\r\n  }\r\n\r\n  SVG.extend(SVG.Element, SVG.FX, extension)\r\n\r\n})\r\n\r\nSVG.extend(SVG.Element, SVG.FX, {\r\n  // Map rotation to transform\r\n  rotate: function(d, cx, cy) {\r\n    return this.transform({ rotation: d, cx: cx, cy: cy })\r\n  }\r\n  // Map skew to transform\r\n, skew: function(x, y, cx, cy) {\r\n    return arguments.length == 1  || arguments.length == 3 ?\r\n      this.transform({ skew: x, cx: y, cy: cx }) :\r\n      this.transform({ skewX: x, skewY: y, cx: cx, cy: cy })\r\n  }\r\n  // Map scale to transform\r\n, scale: function(x, y, cx, cy) {\r\n    return arguments.length == 1  || arguments.length == 3 ?\r\n      this.transform({ scale: x, cx: y, cy: cx }) :\r\n      this.transform({ scaleX: x, scaleY: y, cx: cx, cy: cy })\r\n  }\r\n  // Map translate to transform\r\n, translate: function(x, y) {\r\n    return this.transform({ x: x, y: y })\r\n  }\r\n  // Map flip to transform\r\n, flip: function(a, o) {\r\n    o = typeof a == 'number' ? a : o\r\n    return this.transform({ flip: a || 'both', offset: o })\r\n  }\r\n  // Map matrix to transform\r\n, matrix: function(m) {\r\n    return this.attr('transform', new SVG.Matrix(arguments.length == 6 ? [].slice.call(arguments) : m))\r\n  }\r\n  // Opacity\r\n, opacity: function(value) {\r\n    return this.attr('opacity', value)\r\n  }\r\n  // Relative move over x axis\r\n, dx: function(x) {\r\n    return this.x(new SVG.Number(x).plus(this instanceof SVG.FX ? 0 : this.x()), true)\r\n  }\r\n  // Relative move over y axis\r\n, dy: function(y) {\r\n    return this.y(new SVG.Number(y).plus(this instanceof SVG.FX ? 0 : this.y()), true)\r\n  }\r\n  // Relative move over x and y axes\r\n, dmove: function(x, y) {\r\n    return this.dx(x).dy(y)\r\n  }\r\n})\r\n\r\nSVG.extend(SVG.Rect, SVG.Ellipse, SVG.Circle, SVG.Gradient, SVG.FX, {\r\n  // Add x and y radius\r\n  radius: function(x, y) {\r\n    var type = (this._target || this).type;\r\n    return type == 'radial' || type == 'circle' ?\r\n      this.attr('r', new SVG.Number(x)) :\r\n      this.rx(x).ry(y == null ? x : y)\r\n  }\r\n})\r\n\r\nSVG.extend(SVG.Path, {\r\n  // Get path length\r\n  length: function() {\r\n    return this.node.getTotalLength()\r\n  }\r\n  // Get point at length\r\n, pointAt: function(length) {\r\n    return this.node.getPointAtLength(length)\r\n  }\r\n})\r\n\r\nSVG.extend(SVG.Parent, SVG.Text, SVG.Tspan, SVG.FX, {\r\n  // Set font\r\n  font: function(a, v) {\r\n    if (typeof a == 'object') {\r\n      for (v in a) this.font(v, a[v])\r\n    }\r\n\r\n    return a == 'leading' ?\r\n        this.leading(v) :\r\n      a == 'anchor' ?\r\n        this.attr('text-anchor', v) :\r\n      a == 'size' || a == 'family' || a == 'weight' || a == 'stretch' || a == 'variant' || a == 'style' ?\r\n        this.attr('font-'+ a, v) :\r\n        this.attr(a, v)\r\n  }\r\n})\r\n\nSVG.Set = SVG.invent({\r\n  // Initialize\r\n  create: function(members) {\r\n    if (members instanceof SVG.Set) {\r\n      this.members = members.members.slice()\r\n    } else {\r\n      Array.isArray(members) ? this.members = members : this.clear()\r\n    }\r\n  }\r\n\r\n  // Add class methods\r\n, extend: {\r\n    // Add element to set\r\n    add: function() {\r\n      var i, il, elements = [].slice.call(arguments)\r\n\r\n      for (i = 0, il = elements.length; i < il; i++)\r\n        this.members.push(elements[i])\r\n\r\n      return this\r\n    }\r\n    // Remove element from set\r\n  , remove: function(element) {\r\n      var i = this.index(element)\r\n\r\n      // remove given child\r\n      if (i > -1)\r\n        this.members.splice(i, 1)\r\n\r\n      return this\r\n    }\r\n    // Iterate over all members\r\n  , each: function(block) {\r\n      for (var i = 0, il = this.members.length; i < il; i++)\r\n        block.apply(this.members[i], [i, this.members])\r\n\r\n      return this\r\n    }\r\n    // Restore to defaults\r\n  , clear: function() {\r\n      // initialize store\r\n      this.members = []\r\n\r\n      return this\r\n    }\r\n    // Get the length of a set\r\n  , length: function() {\r\n      return this.members.length\r\n    }\r\n    // Checks if a given element is present in set\r\n  , has: function(element) {\r\n      return this.index(element) >= 0\r\n    }\r\n    // retuns index of given element in set\r\n  , index: function(element) {\r\n      return this.members.indexOf(element)\r\n    }\r\n    // Get member at given index\r\n  , get: function(i) {\r\n      return this.members[i]\r\n    }\r\n    // Get first member\r\n  , first: function() {\r\n      return this.get(0)\r\n    }\r\n    // Get last member\r\n  , last: function() {\r\n      return this.get(this.members.length - 1)\r\n    }\r\n    // Default value\r\n  , valueOf: function() {\r\n      return this.members\r\n    }\r\n    // Get the bounding box of all members included or empty box if set has no items\r\n  , bbox: function(){\r\n      // return an empty box of there are no members\r\n      if (this.members.length == 0)\r\n        return new SVG.RBox()\r\n\r\n      // get the first rbox and update the target bbox\r\n      var rbox = this.members[0].rbox(this.members[0].doc())\r\n\r\n      this.each(function() {\r\n        // user rbox for correct position and visual representation\r\n        rbox = rbox.merge(this.rbox(this.doc()))\r\n      })\r\n\r\n      return rbox\r\n    }\r\n  }\r\n\r\n  // Add parent method\r\n, construct: {\r\n    // Create a new set\r\n    set: function(members) {\r\n      return new SVG.Set(members)\r\n    }\r\n  }\r\n})\r\n\r\nSVG.FX.Set = SVG.invent({\r\n  // Initialize node\r\n  create: function(set) {\r\n    // store reference to set\r\n    this.set = set\r\n  }\r\n\r\n})\r\n\r\n// Alias methods\r\nSVG.Set.inherit = function() {\r\n  var m\r\n    , methods = []\r\n\r\n  // gather shape methods\r\n  for(var m in SVG.Shape.prototype)\r\n    if (typeof SVG.Shape.prototype[m] == 'function' && typeof SVG.Set.prototype[m] != 'function')\r\n      methods.push(m)\r\n\r\n  // apply shape aliasses\r\n  methods.forEach(function(method) {\r\n    SVG.Set.prototype[method] = function() {\r\n      for (var i = 0, il = this.members.length; i < il; i++)\r\n        if (this.members[i] && typeof this.members[i][method] == 'function')\r\n          this.members[i][method].apply(this.members[i], arguments)\r\n\r\n      return method == 'animate' ? (this.fx || (this.fx = new SVG.FX.Set(this))) : this\r\n    }\r\n  })\r\n\r\n  // clear methods for the next round\r\n  methods = []\r\n\r\n  // gather fx methods\r\n  for(var m in SVG.FX.prototype)\r\n    if (typeof SVG.FX.prototype[m] == 'function' && typeof SVG.FX.Set.prototype[m] != 'function')\r\n      methods.push(m)\r\n\r\n  // apply fx aliasses\r\n  methods.forEach(function(method) {\r\n    SVG.FX.Set.prototype[method] = function() {\r\n      for (var i = 0, il = this.set.members.length; i < il; i++)\r\n        this.set.members[i].fx[method].apply(this.set.members[i].fx, arguments)\r\n\r\n      return this\r\n    }\r\n  })\r\n}\r\n\n\r\nSVG.extend(SVG.Element, {\r\n  // Store data values on svg nodes\r\n  data: function(a, v, r) {\r\n    if (typeof a == 'object') {\r\n      for (v in a)\r\n        this.data(v, a[v])\r\n\r\n    } else if (arguments.length < 2) {\r\n      try {\r\n        return JSON.parse(this.attr('data-' + a))\r\n      } catch(e) {\r\n        return this.attr('data-' + a)\r\n      }\r\n\r\n    } else {\r\n      this.attr(\r\n        'data-' + a\r\n      , v === null ?\r\n          null :\r\n        r === true || typeof v === 'string' || typeof v === 'number' ?\r\n          v :\r\n          JSON.stringify(v)\r\n      )\r\n    }\r\n\r\n    return this\r\n  }\r\n})\nSVG.extend(SVG.Element, {\r\n  // Remember arbitrary data\r\n  remember: function(k, v) {\r\n    // remember every item in an object individually\r\n    if (typeof arguments[0] == 'object')\r\n      for (var v in k)\r\n        this.remember(v, k[v])\r\n\r\n    // retrieve memory\r\n    else if (arguments.length == 1)\r\n      return this.memory()[k]\r\n\r\n    // store memory\r\n    else\r\n      this.memory()[k] = v\r\n\r\n    return this\r\n  }\r\n\r\n  // Erase a given memory\r\n, forget: function() {\r\n    if (arguments.length == 0)\r\n      this._memory = {}\r\n    else\r\n      for (var i = arguments.length - 1; i >= 0; i--)\r\n        delete this.memory()[arguments[i]]\r\n\r\n    return this\r\n  }\r\n\r\n  // Initialize or return local memory object\r\n, memory: function() {\r\n    return this._memory || (this._memory = {})\r\n  }\r\n\r\n})\n// Method for getting an element by id\r\nSVG.get = function(id) {\r\n  var node = document.getElementById(idFromReference(id) || id)\r\n  return SVG.adopt(node)\r\n}\r\n\r\n// Select elements by query string\r\nSVG.select = function(query, parent) {\r\n  return new SVG.Set(\r\n    SVG.utils.map((parent || document).querySelectorAll(query), function(node) {\r\n      return SVG.adopt(node)\r\n    })\r\n  )\r\n}\r\n\r\nSVG.extend(SVG.Parent, {\r\n  // Scoped select method\r\n  select: function(query) {\r\n    return SVG.select(query, this.node)\r\n  }\r\n\r\n})\nfunction pathRegReplace(a, b, c, d) {\r\n  return c + d.replace(SVG.regex.dots, ' .')\r\n}\r\n\r\n// creates deep clone of array\r\nfunction array_clone(arr){\r\n  var clone = arr.slice(0)\r\n  for(var i = clone.length; i--;){\r\n    if(Array.isArray(clone[i])){\r\n      clone[i] = array_clone(clone[i])\r\n    }\r\n  }\r\n  return clone\r\n}\r\n\r\n// tests if a given element is instance of an object\r\nfunction is(el, obj){\r\n  return el instanceof obj\r\n}\r\n\r\n// tests if a given selector matches an element\r\nfunction matches(el, selector) {\r\n  return (el.matches || el.matchesSelector || el.msMatchesSelector || el.mozMatchesSelector || el.webkitMatchesSelector || el.oMatchesSelector).call(el, selector);\r\n}\r\n\r\n// Convert dash-separated-string to camelCase\r\nfunction camelCase(s) {\r\n  return s.toLowerCase().replace(/-(.)/g, function(m, g) {\r\n    return g.toUpperCase()\r\n  })\r\n}\r\n\r\n// Capitalize first letter of a string\r\nfunction capitalize(s) {\r\n  return s.charAt(0).toUpperCase() + s.slice(1)\r\n}\r\n\r\n// Ensure to six-based hex\r\nfunction fullHex(hex) {\r\n  return hex.length == 4 ?\r\n    [ '#',\r\n      hex.substring(1, 2), hex.substring(1, 2)\r\n    , hex.substring(2, 3), hex.substring(2, 3)\r\n    , hex.substring(3, 4), hex.substring(3, 4)\r\n    ].join('') : hex\r\n}\r\n\r\n// Component to hex value\r\nfunction compToHex(comp) {\r\n  var hex = comp.toString(16)\r\n  return hex.length == 1 ? '0' + hex : hex\r\n}\r\n\r\n// Calculate proportional width and height values when necessary\r\nfunction proportionalSize(element, width, height) {\r\n  if (width == null || height == null) {\r\n    var box = element.bbox()\r\n\r\n    if (width == null)\r\n      width = box.width / box.height * height\r\n    else if (height == null)\r\n      height = box.height / box.width * width\r\n  }\r\n\r\n  return {\r\n    width:  width\r\n  , height: height\r\n  }\r\n}\r\n\r\n// Delta transform point\r\nfunction deltaTransformPoint(matrix, x, y) {\r\n  return {\r\n    x: x * matrix.a + y * matrix.c + 0\r\n  , y: x * matrix.b + y * matrix.d + 0\r\n  }\r\n}\r\n\r\n// Map matrix array to object\r\nfunction arrayToMatrix(a) {\r\n  return { a: a[0], b: a[1], c: a[2], d: a[3], e: a[4], f: a[5] }\r\n}\r\n\r\n// Parse matrix if required\r\nfunction parseMatrix(matrix) {\r\n  if (!(matrix instanceof SVG.Matrix))\r\n    matrix = new SVG.Matrix(matrix)\r\n\r\n  return matrix\r\n}\r\n\r\n// Add centre point to transform object\r\nfunction ensureCentre(o, target) {\r\n  o.cx = o.cx == null ? target.bbox().cx : o.cx\r\n  o.cy = o.cy == null ? target.bbox().cy : o.cy\r\n}\r\n\r\n// PathArray Helpers\r\nfunction arrayToString(a) {\r\n  for (var i = 0, il = a.length, s = ''; i < il; i++) {\r\n    s += a[i][0]\r\n\r\n    if (a[i][1] != null) {\r\n      s += a[i][1]\r\n\r\n      if (a[i][2] != null) {\r\n        s += ' '\r\n        s += a[i][2]\r\n\r\n        if (a[i][3] != null) {\r\n          s += ' '\r\n          s += a[i][3]\r\n          s += ' '\r\n          s += a[i][4]\r\n\r\n          if (a[i][5] != null) {\r\n            s += ' '\r\n            s += a[i][5]\r\n            s += ' '\r\n            s += a[i][6]\r\n\r\n            if (a[i][7] != null) {\r\n              s += ' '\r\n              s += a[i][7]\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  return s + ' '\r\n}\r\n\r\n// Deep new id assignment\r\nfunction assignNewId(node) {\r\n  // do the same for SVG child nodes as well\r\n  for (var i = node.childNodes.length - 1; i >= 0; i--)\r\n    if (node.childNodes[i] instanceof window.SVGElement)\r\n      assignNewId(node.childNodes[i])\r\n\r\n  return SVG.adopt(node).id(SVG.eid(node.nodeName))\r\n}\r\n\r\n// Add more bounding box properties\r\nfunction fullBox(b) {\r\n  if (b.x == null) {\r\n    b.x      = 0\r\n    b.y      = 0\r\n    b.width  = 0\r\n    b.height = 0\r\n  }\r\n\r\n  b.w  = b.width\r\n  b.h  = b.height\r\n  b.x2 = b.x + b.width\r\n  b.y2 = b.y + b.height\r\n  b.cx = b.x + b.width / 2\r\n  b.cy = b.y + b.height / 2\r\n\r\n  return b\r\n}\r\n\r\n// Get id from reference string\r\nfunction idFromReference(url) {\r\n  var m = (url || '').toString().match(SVG.regex.reference)\r\n\r\n  if (m) return m[1]\r\n}\r\n\r\n// If values like 1e-88 are passed, this is not a valid 32 bit float,\r\n// but in those cases, we are so close to 0 that 0 works well!\r\nfunction float32String(v) {\r\n  return Math.abs(v) > 1e-37 ? v : 0\r\n}\r\n\r\n// Create matrix array for looping\r\nvar abcdef = 'abcdef'.split('')\r\n\n// Add CustomEvent to IE9 and IE10\r\nif (typeof window.CustomEvent !== 'function') {\r\n  // Code from: https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent\r\n  var CustomEventPoly = function(event, options) {\r\n    options = options || { bubbles: false, cancelable: false, detail: undefined }\r\n    var e = document.createEvent('CustomEvent')\r\n    e.initCustomEvent(event, options.bubbles, options.cancelable, options.detail)\r\n    return e\r\n  }\r\n\r\n  CustomEventPoly.prototype = window.Event.prototype\r\n\r\n  SVG.CustomEvent = CustomEventPoly\r\n} else {\r\n  SVG.CustomEvent = window.CustomEvent\r\n}\r\n\r\n// requestAnimationFrame / cancelAnimationFrame Polyfill with fallback based on Paul Irish\r\n(function(w) {\r\n  var lastTime = 0\r\n  var vendors = ['moz', 'webkit']\r\n\r\n  for(var x = 0; x < vendors.length && !window.requestAnimationFrame; ++x) {\r\n    w.requestAnimationFrame = w[vendors[x] + 'RequestAnimationFrame']\r\n    w.cancelAnimationFrame  = w[vendors[x] + 'CancelAnimationFrame'] ||\r\n                              w[vendors[x] + 'CancelRequestAnimationFrame']\r\n  }\r\n\r\n  w.requestAnimationFrame = w.requestAnimationFrame ||\r\n    function(callback) {\r\n      var currTime = new Date().getTime()\r\n      var timeToCall = Math.max(0, 16 - (currTime - lastTime))\r\n\r\n      var id = w.setTimeout(function() {\r\n        callback(currTime + timeToCall)\r\n      }, timeToCall)\r\n\r\n      lastTime = currTime + timeToCall\r\n      return id\r\n    }\r\n\r\n  w.cancelAnimationFrame = w.cancelAnimationFrame || w.clearTimeout;\r\n\r\n}(window))\r\n\r\nreturn SVG\r\n\r\n}));\r", "function one(selector, el) {\n  return el.querySelector(selector);\n}\n\nexports = module.exports = function(selector, el){\n  el = el || document;\n  return one(selector, el);\n};\n\nexports.all = function(selector, el){\n  el = el || document;\n  return el.querySelectorAll(selector);\n};\n\nexports.engine = function(obj){\n  if (!obj.one) throw new Error('.one callback required');\n  if (!obj.all) throw new Error('.all callback required');\n  one = obj.one;\n  exports.all = obj.all;\n  return exports;\n};\n", "module.exports = require('component-query');", "var prefix = 'tokenSimulation';\r\n\r\nmodule.exports = {\r\n  TOGGLE_MODE_EVENT: prefix + '.toggleMode',\r\n  GENERATE_TOKEN_EVENT: prefix + '.generateToken',\r\n  CONSUME_TOKEN_EVENT: prefix + '.consumeToken',\r\n  PLAY_SIMULATION_EVENT: prefix + '.playSimulation',\r\n  PAUSE_SIMULATION_EVENT: prefix + '.pauseSimulation',\r\n  RESET_SIMULATION_EVENT: prefix + '.resetSimulation',\r\n  TERMINATE_EVENT: prefix + '.terminateEvent',\r\n  UPDATE_ELEMENTS_EVENT: prefix + '.updateElements',\r\n  UPDATE_ELEMENT_EVENT: prefix + '.updateElement',\r\n  PROCESS_INSTANCE_CREATED_EVENT: prefix + '.processInstanceCreated',\r\n  PROCESS_INSTANCE_FINISHED_EVENT: prefix + '.processInstanceFinished',\r\n  PROCESS_INSTANCE_SHOWN_EVENT: prefix + '.processInstanceShown',\r\n  PROCESS_INSTANCE_HIDDEN_EVENT: prefix + '.processInstanceHidden',\r\n  ANIMATION_CREATED_EVENT: prefix + '.animationCreated'\r\n};", "/**\n * Flatten array, one level deep.\n *\n * @param {Array<?>} arr\n *\n * @return {Array<?>}\n */\nfunction flatten(arr) {\n  return Array.prototype.concat.apply([], arr);\n}\n\nvar nativeToString = Object.prototype.toString;\nvar nativeHasOwnProperty = Object.prototype.hasOwnProperty;\nfunction isUndefined(obj) {\n  return obj === undefined;\n}\nfunction isDefined(obj) {\n  return obj !== undefined;\n}\nfunction isNil(obj) {\n  return obj == null;\n}\nfunction isArray(obj) {\n  return nativeToString.call(obj) === '[object Array]';\n}\nfunction isObject(obj) {\n  return nativeToString.call(obj) === '[object Object]';\n}\nfunction isNumber(obj) {\n  return nativeToString.call(obj) === '[object Number]';\n}\nfunction isFunction(obj) {\n  var tag = nativeToString.call(obj);\n  return tag === '[object Function]' || tag === '[object AsyncFunction]' || tag === '[object GeneratorFunction]' || tag === '[object AsyncGeneratorFunction]' || tag === '[object Proxy]';\n}\nfunction isString(obj) {\n  return nativeToString.call(obj) === '[object String]';\n}\n/**\n * Ensure collection is an array.\n *\n * @param {Object} obj\n */\n\nfunction ensureArray(obj) {\n  if (isArray(obj)) {\n    return;\n  }\n\n  throw new Error('must supply array');\n}\n/**\n * Return true, if target owns a property with the given key.\n *\n * @param {Object} target\n * @param {String} key\n *\n * @return {Boolean}\n */\n\nfunction has(target, key) {\n  return nativeHasOwnProperty.call(target, key);\n}\n\n/**\n * Find element in collection.\n *\n * @param  {Array|Object} collection\n * @param  {Function|Object} matcher\n *\n * @return {Object}\n */\n\nfunction find(collection, matcher) {\n  matcher = toMatcher(matcher);\n  var match;\n  forEach(collection, function (val, key) {\n    if (matcher(val, key)) {\n      match = val;\n      return false;\n    }\n  });\n  return match;\n}\n/**\n * Find element index in collection.\n *\n * @param  {Array|Object} collection\n * @param  {Function} matcher\n *\n * @return {Object}\n */\n\nfunction findIndex(collection, matcher) {\n  matcher = toMatcher(matcher);\n  var idx = isArray(collection) ? -1 : undefined;\n  forEach(collection, function (val, key) {\n    if (matcher(val, key)) {\n      idx = key;\n      return false;\n    }\n  });\n  return idx;\n}\n/**\n * Find element in collection.\n *\n * @param  {Array|Object} collection\n * @param  {Function} matcher\n *\n * @return {Array} result\n */\n\nfunction filter(collection, matcher) {\n  var result = [];\n  forEach(collection, function (val, key) {\n    if (matcher(val, key)) {\n      result.push(val);\n    }\n  });\n  return result;\n}\n/**\n * Iterate over collection; returning something\n * (non-undefined) will stop iteration.\n *\n * @param  {Array|Object} collection\n * @param  {Function} iterator\n *\n * @return {Object} return result that stopped the iteration\n */\n\nfunction forEach(collection, iterator) {\n  var val, result;\n\n  if (isUndefined(collection)) {\n    return;\n  }\n\n  var convertKey = isArray(collection) ? toNum : identity;\n\n  for (var key in collection) {\n    if (has(collection, key)) {\n      val = collection[key];\n      result = iterator(val, convertKey(key));\n\n      if (result === false) {\n        return val;\n      }\n    }\n  }\n}\n/**\n * Return collection without element.\n *\n * @param  {Array} arr\n * @param  {Function} matcher\n *\n * @return {Array}\n */\n\nfunction without(arr, matcher) {\n  if (isUndefined(arr)) {\n    return [];\n  }\n\n  ensureArray(arr);\n  matcher = toMatcher(matcher);\n  return arr.filter(function (el, idx) {\n    return !matcher(el, idx);\n  });\n}\n/**\n * Reduce collection, returning a single result.\n *\n * @param  {Object|Array} collection\n * @param  {Function} iterator\n * @param  {Any} result\n *\n * @return {Any} result returned from last iterator\n */\n\nfunction reduce(collection, iterator, result) {\n  forEach(collection, function (value, idx) {\n    result = iterator(result, value, idx);\n  });\n  return result;\n}\n/**\n * Return true if every element in the collection\n * matches the criteria.\n *\n * @param  {Object|Array} collection\n * @param  {Function} matcher\n *\n * @return {Boolean}\n */\n\nfunction every(collection, matcher) {\n  return !!reduce(collection, function (matches, val, key) {\n    return matches && matcher(val, key);\n  }, true);\n}\n/**\n * Return true if some elements in the collection\n * match the criteria.\n *\n * @param  {Object|Array} collection\n * @param  {Function} matcher\n *\n * @return {Boolean}\n */\n\nfunction some(collection, matcher) {\n  return !!find(collection, matcher);\n}\n/**\n * Transform a collection into another collection\n * by piping each member through the given fn.\n *\n * @param  {Object|Array}   collection\n * @param  {Function} fn\n *\n * @return {Array} transformed collection\n */\n\nfunction map(collection, fn) {\n  var result = [];\n  forEach(collection, function (val, key) {\n    result.push(fn(val, key));\n  });\n  return result;\n}\n/**\n * Get the collections keys.\n *\n * @param  {Object|Array} collection\n *\n * @return {Array}\n */\n\nfunction keys(collection) {\n  return collection && Object.keys(collection) || [];\n}\n/**\n * Shorthand for `keys(o).length`.\n *\n * @param  {Object|Array} collection\n *\n * @return {Number}\n */\n\nfunction size(collection) {\n  return keys(collection).length;\n}\n/**\n * Get the values in the collection.\n *\n * @param  {Object|Array} collection\n *\n * @return {Array}\n */\n\nfunction values(collection) {\n  return map(collection, function (val) {\n    return val;\n  });\n}\n/**\n * Group collection members by attribute.\n *\n * @param  {Object|Array} collection\n * @param  {Function} extractor\n *\n * @return {Object} map with { attrValue => [ a, b, c ] }\n */\n\nfunction groupBy(collection, extractor) {\n  var grouped = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  extractor = toExtractor(extractor);\n  forEach(collection, function (val) {\n    var discriminator = extractor(val) || '_';\n    var group = grouped[discriminator];\n\n    if (!group) {\n      group = grouped[discriminator] = [];\n    }\n\n    group.push(val);\n  });\n  return grouped;\n}\nfunction uniqueBy(extractor) {\n  extractor = toExtractor(extractor);\n  var grouped = {};\n\n  for (var _len = arguments.length, collections = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    collections[_key - 1] = arguments[_key];\n  }\n\n  forEach(collections, function (c) {\n    return groupBy(c, extractor, grouped);\n  });\n  var result = map(grouped, function (val, key) {\n    return val[0];\n  });\n  return result;\n}\nvar unionBy = uniqueBy;\n/**\n * Sort collection by criteria.\n *\n * @param  {Object|Array} collection\n * @param  {String|Function} extractor\n *\n * @return {Array}\n */\n\nfunction sortBy(collection, extractor) {\n  extractor = toExtractor(extractor);\n  var sorted = [];\n  forEach(collection, function (value, key) {\n    var disc = extractor(value, key);\n    var entry = {\n      d: disc,\n      v: value\n    };\n\n    for (var idx = 0; idx < sorted.length; idx++) {\n      var d = sorted[idx].d;\n\n      if (disc < d) {\n        sorted.splice(idx, 0, entry);\n        return;\n      }\n    } // not inserted, append (!)\n\n\n    sorted.push(entry);\n  });\n  return map(sorted, function (e) {\n    return e.v;\n  });\n}\n/**\n * Create an object pattern matcher.\n *\n * @example\n *\n * const matcher = matchPattern({ id: 1 });\n *\n * let element = find(elements, matcher);\n *\n * @param  {Object} pattern\n *\n * @return {Function} matcherFn\n */\n\nfunction matchPattern(pattern) {\n  return function (el) {\n    return every(pattern, function (val, key) {\n      return el[key] === val;\n    });\n  };\n}\n\nfunction toExtractor(extractor) {\n  return isFunction(extractor) ? extractor : function (e) {\n    return e[extractor];\n  };\n}\n\nfunction toMatcher(matcher) {\n  return isFunction(matcher) ? matcher : function (e) {\n    return e === matcher;\n  };\n}\n\nfunction identity(arg) {\n  return arg;\n}\n\nfunction toNum(arg) {\n  return Number(arg);\n}\n\n/**\n * Debounce fn, calling it only once if the given time\n * elapsed between calls.\n *\n * Lodash-style the function exposes methods to `#clear`\n * and `#flush` to control internal behavior.\n *\n * @param  {Function} fn\n * @param  {Number} timeout\n *\n * @return {Function} debounced function\n */\nfunction debounce(fn, timeout) {\n  var timer;\n  var lastArgs;\n  var lastThis;\n  var lastNow;\n\n  function fire(force) {\n    var now = Date.now();\n    var scheduledDiff = force ? 0 : lastNow + timeout - now;\n\n    if (scheduledDiff > 0) {\n      return schedule(scheduledDiff);\n    }\n\n    fn.apply(lastThis, lastArgs);\n    clear();\n  }\n\n  function schedule(timeout) {\n    timer = setTimeout(fire, timeout);\n  }\n\n  function clear() {\n    if (timer) {\n      clearTimeout(timer);\n    }\n\n    timer = lastNow = lastArgs = lastThis = undefined;\n  }\n\n  function flush() {\n    if (timer) {\n      fire(true);\n    }\n\n    clear();\n  }\n\n  function callback() {\n    lastNow = Date.now();\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    lastArgs = args;\n    lastThis = this; // ensure an execution is scheduled\n\n    if (!timer) {\n      schedule(timeout);\n    }\n  }\n\n  callback.flush = flush;\n  callback.cancel = clear;\n  return callback;\n}\n/**\n * Throttle fn, calling at most once\n * in the given interval.\n *\n * @param  {Function} fn\n * @param  {Number} interval\n *\n * @return {Function} throttled function\n */\n\nfunction throttle(fn, interval) {\n  var throttling = false;\n  return function () {\n    if (throttling) {\n      return;\n    }\n\n    fn.apply(void 0, arguments);\n    throttling = true;\n    setTimeout(function () {\n      throttling = false;\n    }, interval);\n  };\n}\n/**\n * Bind function against target <this>.\n *\n * @param  {Function} fn\n * @param  {Object}   target\n *\n * @return {Function} bound function\n */\n\nfunction bind(fn, target) {\n  return fn.bind(target);\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\n/**\n * Convenience wrapper for `Object.assign`.\n *\n * @param {Object} target\n * @param {...Object} others\n *\n * @return {Object} the target\n */\n\nfunction assign(target) {\n  for (var _len = arguments.length, others = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    others[_key - 1] = arguments[_key];\n  }\n\n  return _extends.apply(void 0, [target].concat(others));\n}\n/**\n * Sets a nested property of a given object to the specified value.\n *\n * This mutates the object and returns it.\n *\n * @param {Object} target The target of the set operation.\n * @param {(string|number)[]} path The path to the nested value.\n * @param {any} value The value to set.\n */\n\nfunction set(target, path, value) {\n  var currentTarget = target;\n  forEach(path, function (key, idx) {\n    if (typeof key !== 'number' && typeof key !== 'string') {\n      throw new Error('illegal key type: ' + _typeof(key) + '. Key should be of type number or string.');\n    }\n\n    if (key === 'constructor') {\n      throw new Error('illegal key: constructor');\n    }\n\n    if (key === '__proto__') {\n      throw new Error('illegal key: __proto__');\n    }\n\n    var nextKey = path[idx + 1];\n    var nextTarget = currentTarget[key];\n\n    if (isDefined(nextKey) && isNil(nextTarget)) {\n      nextTarget = currentTarget[key] = isNaN(+nextKey) ? {} : [];\n    }\n\n    if (isUndefined(nextKey)) {\n      if (isUndefined(value)) {\n        delete currentTarget[key];\n      } else {\n        currentTarget[key] = value;\n      }\n    } else {\n      currentTarget = nextTarget;\n    }\n  });\n  return target;\n}\n/**\n * Gets a nested property of a given object.\n *\n * @param {Object} target The target of the get operation.\n * @param {(string|number)[]} path The path to the nested value.\n * @param {any} [defaultValue] The value to return if no value exists.\n */\n\nfunction get(target, path, defaultValue) {\n  var currentTarget = target;\n  forEach(path, function (key) {\n    // accessing nil property yields <undefined>\n    if (isNil(currentTarget)) {\n      currentTarget = undefined;\n      return false;\n    }\n\n    currentTarget = currentTarget[key];\n  });\n  return isUndefined(currentTarget) ? defaultValue : currentTarget;\n}\n/**\n * Pick given properties from the target object.\n *\n * @param {Object} target\n * @param {Array} properties\n *\n * @return {Object} target\n */\n\nfunction pick(target, properties) {\n  var result = {};\n  var obj = Object(target);\n  forEach(properties, function (prop) {\n    if (prop in obj) {\n      result[prop] = target[prop];\n    }\n  });\n  return result;\n}\n/**\n * Pick all target properties, excluding the given ones.\n *\n * @param {Object} target\n * @param {Array} properties\n *\n * @return {Object} target\n */\n\nfunction omit(target, properties) {\n  var result = {};\n  var obj = Object(target);\n  forEach(obj, function (prop, key) {\n    if (properties.indexOf(key) === -1) {\n      result[key] = prop;\n    }\n  });\n  return result;\n}\n/**\n * Recursively merge `...sources` into given target.\n *\n * Does support merging objects; does not support merging arrays.\n *\n * @param {Object} target\n * @param {...Object} sources\n *\n * @return {Object} the target\n */\n\nfunction merge(target) {\n  for (var _len2 = arguments.length, sources = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    sources[_key2 - 1] = arguments[_key2];\n  }\n\n  if (!sources.length) {\n    return target;\n  }\n\n  forEach(sources, function (source) {\n    // skip non-obj sources, i.e. null\n    if (!source || !isObject(source)) {\n      return;\n    }\n\n    forEach(source, function (sourceVal, key) {\n      if (key === '__proto__') {\n        return;\n      }\n\n      var targetVal = target[key];\n\n      if (isObject(sourceVal)) {\n        if (!isObject(targetVal)) {\n          // override target[key] with object\n          targetVal = {};\n        }\n\n        target[key] = merge(targetVal, sourceVal);\n      } else {\n        target[key] = sourceVal;\n      }\n    });\n  });\n  return target;\n}\n\nexport { assign, bind, debounce, ensureArray, every, filter, find, findIndex, flatten, forEach, get, groupBy, has, isArray, isDefined, isFunction, isNil, isNumber, isObject, isString, isUndefined, keys, map, matchPattern, merge, omit, pick, reduce, set, size, some, sortBy, throttle, unionBy, uniqueBy, values, without };\n", "'use strict';\n\nvar every = require('min-dash').every,\n    some = require('min-dash').some;\n\nmodule.exports.is = function(element, types) {\n  if (element.type === 'label') {\n    return;\n  }\n\n  if (!Array.isArray(types)) {\n    types = [ types ];\n  }\n\n  var isType = false;\n\n  types.forEach(function(type) {\n    if (type === element.type) {\n      isType = true;\n    }\n  });\n\n  return isType;\n};\n\nmodule.exports.isTypedEvent = function(event, eventDefinitionType, filter) {\n\n  function matches(definition, filter) {\n    return every(filter, function(val, key) {\n\n      // we want a == conversion here, to be able to catch\n      // undefined == false and friends\n      return definition[key] == val;\n    });\n  }\n\n  return some(event.eventDefinitions, function(definition) {\n    return definition.$type === eventDefinitionType && matches(event, filter);\n  });\n};\n\nmodule.exports.getBusinessObject = function(element) {\n  return (element && element.businessObject) || element;\n};\n\nfunction isAncestor(ancestor, descendant) {\n  var childParent = descendant.parent;\n\n  while (childParent) {\n    if (childParent === ancestor) {\n      return true;\n    }\n\n    childParent = childParent.parent;\n  }\n\n  return false;\n}\n\nmodule.exports.isAncestor = isAncestor;\n\nmodule.exports.getDescendants = function(elements, ancestor) {\n  return elements.filter(function(element) {\n    return isAncestor(ancestor, element);\n  });\n};\n\nmodule.exports.supportedElements = [\n  'bpmn:Association',\n  'bpmn:BoundaryEvent',\n  'bpmn:BusinessRuleTask',\n  'bpmn:CallActivity',\n  'bpmn:DataInputAssociation',\n  'bpmn:DataObjectReference',\n  'bpmn:DataOutputAssociation',\n  'bpmn:DataStoreReference',\n  'bpmn:EndEvent',\n  'bpmn:EventBasedGateway',\n  'bpmn:ExclusiveGateway',\n  'bpmn:IntermediateCatchEvent',\n  'bpmn:ManualTask',\n  'bpmn:ParallelGateway',\n  'bpmn:Process',\n  'bpmn:ScriptTask',\n  'bpmn:SequenceFlow',\n  'bpmn:ServiceTask',\n  'bpmn:StartEvent',\n  'bpmn:SubProcess',\n  'bpmn:Task',\n  'bpmn:TextAnnotation',\n  'bpmn:UserTask'\n];", "module.exports.getMid = function(element) {\n  var bbox = element.bbox();\n\n  return {\n    x: bbox.x + bbox.width / 2,\n    y: bbox.y + bbox.height / 2\n  };\n};\n\nmodule.exports.distance = function(a, b) {\n  return Math.sqrt(Math.pow(a.x - b.x, 2) + Math.pow(a.y - b.y, 2));\n};", "'use strict';\n\nvar SVG = require('svg.js');\n\nvar domQuery = require('min-dom/lib/query');\n\nvar events = require('../util/EventHelper'),\n    RESET_SIMULATION_EVENT = events.RESET_SIMULATION_EVENT,\n    PLAY_SIMULATION_EVENT = events.PLAY_SIMULATION_EVENT,\n    PAUSE_SIMULATION_EVENT = events.PAUSE_SIMULATION_EVENT,\n    TERMINATE_EVENT = events.TERMINATE_EVENT,\n    PROCESS_INSTANCE_FINISHED_EVENT = events.PROCESS_INSTANCE_FINISHED_EVENT,\n    ANIMATION_CREATED_EVENT = events.ANIMATION_CREATED_EVENT;\n\nvar isAncestor = require('../util/ElementHelper').isAncestor;\n\nvar geometryUtil = require('../util/GeometryUtil'),\n    distance = geometryUtil.distance;\n\nvar STROKE_COLOR = getComputedStyle(document.documentElement).getPropertyValue('--token-simulation-green-base-44');\n\nfunction isFirstSegment(index) {\n  return index === 1;\n}\n\nfunction isSingleSegment(waypoints) {\n  return waypoints.length == 2;\n}\n\nvar DELAY = 0;\n\nvar EASE_LINEAR = '-',\n    EASE_IN = '<',\n    EASE_IN_OUT = '<>';\n\nvar TOKEN_SIZE = 20;\n\nfunction Animation(canvas, eventBus) {\n  var self = window.animation = this;\n\n  this._eventBus = eventBus;\n  this.animations = [];\n  this.hiddenAnimations = [];\n\n  this.animationSpeed = 1;\n\n  eventBus.on('import.done', function() {\n    var draw = SVG(canvas._svg);\n\n    var viewport = domQuery('.viewport', canvas._svg);\n\n    var groupParent = SVG.adopt(viewport);\n\n    self.group = draw\n      .group()\n      .attr('id', 'token-simulation');\n\n    groupParent.put(self.group);\n  });\n\n  eventBus.on(TERMINATE_EVENT, function(context) {\n    var element = context.element,\n        parent = element.parent;\n\n    self.animations.forEach(function(animation) {\n      if (isAncestor(parent, animation.element)) {\n\n        // remove token\n        animation.animation.stop();\n\n        self.animations = self.animations.filter(function(a) {\n          return a !== animation;\n        });\n      }\n    });\n  });\n\n  eventBus.on(PROCESS_INSTANCE_FINISHED_EVENT, function(context) {\n    var parent = context.parent;\n\n    self.animations.forEach(function(animation) {\n      if (context.processInstanceId === animation.processInstanceId ||\n        isAncestor(parent, animation.element)) {\n\n        // remove token\n        animation.animation.stop();\n\n        self.animations = self.animations.filter(function(a) {\n          return a !== animation;\n        });\n      }\n    });\n  });\n\n  eventBus.on(RESET_SIMULATION_EVENT, function() {\n    self.animations.forEach(function(animation) {\n      animation.animation.stop();\n    });\n\n    self.animations = [];\n    self.hiddenAnimations = [];\n  });\n\n  eventBus.on(PAUSE_SIMULATION_EVENT, function() {\n    self.animations.forEach(function(animation) {\n      animation.animation.pause();\n    });\n  });\n\n  eventBus.on(PLAY_SIMULATION_EVENT, function() {\n    self.animations.forEach(function(animation) {\n      animation.animation.play();\n    });\n  });\n}\n\nAnimation.prototype.createAnimation = function(connection, processInstanceId, done) {\n  var self = this;\n\n  if (!this.group) {\n    return;\n  }\n\n  var tokenGfx = this._createTokenGfx(processInstanceId);\n\n  var animation;\n\n  animation = new _Animation(tokenGfx, connection.waypoints, function() {\n    self.animations = self.animations.filter(function(a) {\n      return a.animation !== animation;\n    });\n\n    if (done) {\n      done();\n    }\n  });\n\n  if (this.hiddenAnimations.includes(processInstanceId)) {\n    tokenGfx.hide();\n  }\n\n  tokenGfx.fx._speed = this.animationSpeed;\n\n  this.animations.push({\n    tokenGfx: tokenGfx,\n    animation: animation,\n    element: connection,\n    processInstanceId: processInstanceId\n  });\n\n  this._eventBus.fire(ANIMATION_CREATED_EVENT, {\n    tokenGfx: tokenGfx,\n    animation: animation,\n    element: connection,\n    processInstanceId: processInstanceId\n  });\n\n  return animation;\n};\n\nAnimation.prototype.setAnimationSpeed = function(speed) {\n  this.animations.forEach(function(animation) {\n    animation.tokenGfx.fx._speed = speed;\n  });\n\n  this.animationSpeed = speed;\n};\n\nAnimation.prototype._createTokenGfx = function(processInstanceId) {\n  var parent = this.group\n    .group()\n    .attr('class', 'token')\n    .hide();\n\n  parent\n    .circle(TOKEN_SIZE, TOKEN_SIZE)\n    .attr('fill', STROKE_COLOR)\n    .attr('class', 'circle');\n\n  parent\n    .text(processInstanceId.toString())\n    .attr('transform', 'translate(10, -7)')\n    .attr('text-anchor', 'middle')\n    .attr('class', 'text');\n\n  return parent;\n};\n\nAnimation.prototype.showProcessInstanceAnimations = function(processInstanceId) {\n  this.animations.forEach(function(animation) {\n    if (animation.processInstanceId === processInstanceId) {\n      animation.tokenGfx.show();\n    }\n  });\n\n  this.hiddenAnimations = this.hiddenAnimations.filter(function(id) {\n    return id !== processInstanceId;\n  });\n};\n\nAnimation.prototype.hideProcessInstanceAnimations = function(processInstanceId) {\n  this.animations.forEach(function(animation) {\n    if (animation.processInstanceId === processInstanceId) {\n      animation.tokenGfx.hide();\n    }\n  });\n\n  this.hiddenAnimations.push(processInstanceId);\n};\n\nAnimation.$inject = [ 'canvas', 'eventBus' ];\n\nmodule.exports = Animation;\n\nfunction _Animation(gfx, waypoints, done) {\n  this.gfx = this.fx = gfx;\n  this.waypoints = waypoints;\n  this.done = done;\n\n  this.create();\n}\n\n_Animation.prototype.create = function() {\n  var gfx = this.gfx,\n      waypoints = this.waypoints,\n      done = this.done,\n      fx = this.fx;\n\n  gfx\n    .show()\n    .move(waypoints[0].x - TOKEN_SIZE / 2, waypoints[0].y - TOKEN_SIZE / 2);\n\n  waypoints.forEach(function(waypoint, index) {\n    if (index > 0) {\n      var x = waypoint.x - TOKEN_SIZE / 2,\n          y = waypoint.y - TOKEN_SIZE / 2;\n\n      var ease = isFirstSegment(index) ? EASE_IN : EASE_LINEAR;\n\n      if (isSingleSegment(waypoints)) {\n        ease = EASE_IN_OUT;\n      }\n\n      var duration = distance(waypoints[index - 1], waypoint) * 20;\n\n      fx = fx\n        .animate(duration, ease, DELAY)\n        .move(x, y);\n    }\n  });\n\n  fx.after(function() {\n    gfx.remove();\n\n    done();\n  });\n};\n\n_Animation.prototype.play = function() {\n  this.gfx.play();\n};\n\n_Animation.prototype.pause = function() {\n  this.gfx.pause();\n};\n\n_Animation.prototype.stop = function() {\n  this.fx.stop();\n  this.gfx.remove();\n};", "\n/**\n * Expose `parse`.\n */\n\nmodule.exports = parse;\n\n/**\n * Tests for browser support.\n */\n\nvar innerHTMLBug = false;\nvar bugTestDiv;\nif (typeof document !== 'undefined') {\n  bugTestDiv = document.createElement('div');\n  // Setup\n  bugTestDiv.innerHTML = '  <link/><table></table><a href=\"/a\">a</a><input type=\"checkbox\"/>';\n  // Make sure that link elements get serialized correctly by innerHTML\n  // This requires a wrapper element in IE\n  innerHTMLBug = !bugTestDiv.getElementsByTagName('link').length;\n  bugTestDiv = undefined;\n}\n\n/**\n * Wrap map from jquery.\n */\n\nvar map = {\n  legend: [1, '<fieldset>', '</fieldset>'],\n  tr: [2, '<table><tbody>', '</tbody></table>'],\n  col: [2, '<table><tbody></tbody><colgroup>', '</colgroup></table>'],\n  // for script/link/style tags to work in IE6-8, you have to wrap\n  // in a div with a non-whitespace character in front, ha!\n  _default: innerHTMLBug ? [1, 'X<div>', '</div>'] : [0, '', '']\n};\n\nmap.td =\nmap.th = [3, '<table><tbody><tr>', '</tr></tbody></table>'];\n\nmap.option =\nmap.optgroup = [1, '<select multiple=\"multiple\">', '</select>'];\n\nmap.thead =\nmap.tbody =\nmap.colgroup =\nmap.caption =\nmap.tfoot = [1, '<table>', '</table>'];\n\nmap.polyline =\nmap.ellipse =\nmap.polygon =\nmap.circle =\nmap.text =\nmap.line =\nmap.path =\nmap.rect =\nmap.g = [1, '<svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">','</svg>'];\n\n/**\n * Parse `html` and return a DOM Node instance, which could be a TextNode,\n * HTML DOM Node of some kind (<div> for example), or a DocumentFragment\n * instance, depending on the contents of the `html` string.\n *\n * @param {String} html - HTML string to \"domify\"\n * @param {Document} doc - The `document` instance to create the Node for\n * @return {DOMNode} the TextNode, DOM Node, or DocumentFragment instance\n * @api private\n */\n\nfunction parse(html, doc) {\n  if ('string' != typeof html) throw new TypeError('String expected');\n\n  // default to the global `document` object\n  if (!doc) doc = document;\n\n  // tag name\n  var m = /<([\\w:]+)/.exec(html);\n  if (!m) return doc.createTextNode(html);\n\n  html = html.replace(/^\\s+|\\s+$/g, ''); // Remove leading/trailing whitespace\n\n  var tag = m[1];\n\n  // body support\n  if (tag == 'body') {\n    var el = doc.createElement('html');\n    el.innerHTML = html;\n    return el.removeChild(el.lastChild);\n  }\n\n  // wrap map\n  var wrap = Object.prototype.hasOwnProperty.call(map, tag) ? map[tag] : map._default;\n  var depth = wrap[0];\n  var prefix = wrap[1];\n  var suffix = wrap[2];\n  var el = doc.createElement('div');\n  el.innerHTML = prefix + html + suffix;\n  while (depth--) el = el.lastChild;\n\n  // one element\n  if (el.firstChild == el.lastChild) {\n    return el.removeChild(el.firstChild);\n  }\n\n  // several elements\n  var fragment = doc.createDocumentFragment();\n  while (el.firstChild) {\n    fragment.appendChild(el.removeChild(el.firstChild));\n  }\n\n  return fragment;\n}\n", "module.exports = require('domify');", "var bind = window.addEventListener ? 'addEventListener' : 'attachEvent',\n    unbind = window.removeEventListener ? 'removeEventListener' : 'detachEvent',\n    prefix = bind !== 'addEventListener' ? 'on' : '';\n\n/**\n * Bind `el` event `type` to `fn`.\n *\n * @param {Element} el\n * @param {String} type\n * @param {Function} fn\n * @param {Boolean} capture\n * @return {Function}\n * @api public\n */\n\nexports.bind = function(el, type, fn, capture){\n  el[bind](prefix + type, fn, capture || false);\n  return fn;\n};\n\n/**\n * Unbind `el` event `type`'s callback `fn`.\n *\n * @param {Element} el\n * @param {String} type\n * @param {Function} fn\n * @param {Boolean} capture\n * @return {Function}\n * @api public\n */\n\nexports.unbind = function(el, type, fn, capture){\n  el[unbind](prefix + type, fn, capture || false);\n  return fn;\n};", "module.exports = require('component-event');", "'use strict';\n\nvar domify = require('min-dom/lib/domify'),\n    domEvent = require('min-dom/lib/event');\n\nvar is = require('../../../util/ElementHelper').is;\n\nvar events = require('../../../util/EventHelper'),\n    GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT,\n    UPDATE_ELEMENT_EVENT = events.UPDATE_ELEMENT_EVENT;\n\nfunction BoundaryEventHandler(eventBus, processInstances, processInstanceSettings) {\n  this._eventBus = eventBus;\n  this._processInstances = processInstances;\n  this._processInstanceSettings = processInstanceSettings;\n}\n\nBoundaryEventHandler.prototype.createContextPads = function(element) {\n  if (!element.attachers.length) {\n    return;\n  }\n\n  if (!this._processInstances.getProcessInstances(element).length) {\n    return;\n  }\n\n  var incomingSequenceFlows = element.incoming.filter(function(incoming) {\n    return is(incoming, 'bpmn:SequenceFlow');\n  });\n\n  var self = this;\n\n  var contextPads = [];\n\n  element.attachers.forEach(function(attachedElement) {\n    var outgoingSequenceFlows = attachedElement.outgoing.filter(function(outgoing) {\n      return is(outgoing, 'bpmn:SequenceFlow');\n    });\n\n    if (!incomingSequenceFlows.length || !outgoingSequenceFlows.length) {\n      return;\n    }\n\n    var contextPad = domify('<div class=\"context-pad\" title=\"Trigger Event\"><i class=\"fa fa-play\"></i></div>');\n\n    contextPads.push({\n      element: attachedElement,\n      html: contextPad\n    });\n\n    domEvent.bind(contextPad, 'click', function() {\n\n      self._processInstances\n        .getProcessInstances(element)\n        .forEach(function(processInstance) {\n          var parentProcessInstanceId = processInstance.parentProcessInstanceId;\n\n          // interrupting\n          if (attachedElement.businessObject.cancelActivity) {\n            element.children.forEach(function(child) {\n              if (child.tokenCount && child.tokenCount[processInstance.processInstanceId]) {\n                child.tokenCount[processInstance.processInstanceId]--;\n              }\n            });\n\n            // finish but do NOT remove\n            self._processInstances.finish(processInstance.processInstanceId);\n\n            self._eventBus.fire(UPDATE_ELEMENT_EVENT, {\n              element: element\n            });\n          }\n\n          self._eventBus.fire(GENERATE_TOKEN_EVENT, {\n            element: attachedElement,\n            processInstanceId: parentProcessInstanceId\n          });\n        });\n\n    });\n  });\n\n  return contextPads;\n};\n\nBoundaryEventHandler.$inject = ['eventBus', 'processInstances', 'processInstanceSettings'];\n\nmodule.exports = BoundaryEventHandler;", "'use strict';\r\n\r\nvar is = require('../../../util/ElementHelper').is;\r\n\r\nvar domify = require('min-dom/lib/domify'),\r\n    domEvent = require('min-dom/lib/event');\r\n\r\nfunction ExclusiveGatewayHandler(exluciveGatewaySettings) {\r\n  this._exclusiveGatewaySettings = exluciveGatewaySettings;\r\n}\r\n\r\nExclusiveGatewayHandler.prototype.createContextPads = function(element) {\r\n  var self = this;\r\n\r\n  var outgoingSequenceFlows = element.outgoing.filter(function(outgoing) {\r\n    return is(outgoing, 'bpmn:SequenceFlow');\r\n  });\r\n\r\n  if (outgoingSequenceFlows.length < 2) {\r\n    return;\r\n  }\r\n\r\n  var contextPad = domify('<div class=\"context-pad\" title=\"Set Sequence Flow\"><i class=\"fa fa-code-fork\"></i></div>');\r\n\r\n  domEvent.bind(contextPad, 'click', function() {\r\n    self._exclusiveGatewaySettings.setSequenceFlow(element);\r\n  });\r\n\r\n  return [{\r\n    element: element,\r\n    html: contextPad\r\n  }];\r\n};\r\n\r\nExclusiveGatewayHandler.$inject = [ 'exclusiveGatewaySettings' ];\r\n\r\nmodule.exports = ExclusiveGatewayHandler;", "'use strict';\r\n\r\nvar domify = require('min-dom/lib/domify'),\r\n    domEvent = require('min-dom/lib/event');\r\n\r\nvar is = require('../../../util/ElementHelper').is;\r\n\r\nvar events = require('../../../util/EventHelper'),\r\n    GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT;\r\n\r\nfunction IntermeditateCatchEventHandler(eventBus) {\r\n  this._eventBus = eventBus;\r\n}\r\n\r\nIntermeditateCatchEventHandler.prototype.createContextPads = function(element) {\r\n  var processInstanceId = element.parent.shownProcessInstance;\r\n\r\n  var incomingSequenceFlows = element.incoming.filter(function(incoming) {\r\n    return is(incoming, 'bpmn:SequenceFlow');\r\n  });\r\n\r\n  var eventBasedGatewaysHaveTokens = [];\r\n\r\n  incomingSequenceFlows.forEach(function(incoming) {\r\n    var source = incoming.source;\r\n\r\n    if (is(source, 'bpmn:EventBasedGateway') && source.tokenCount && source.tokenCount[processInstanceId]) {\r\n      eventBasedGatewaysHaveTokens.push(source);\r\n    }\r\n  });\r\n\r\n  var outgoingSequenceFlows = element.outgoing.filter(function(outgoing) {\r\n    return is(outgoing, 'bpmn:SequenceFlow');\r\n  });\r\n\r\n  if (!incomingSequenceFlows.length || !outgoingSequenceFlows.length) {\r\n    return;\r\n  }\r\n\r\n  var self = this;\r\n\r\n  var contextPad;\r\n\r\n  if (element.tokenCount && element.tokenCount[processInstanceId]) {\r\n    contextPad = domify('<div class=\"context-pad\" title=\"Trigger Event\"><i class=\"fa fa-play\"></i></div>');\r\n\r\n    domEvent.bind(contextPad, 'click', function() {\r\n      element.tokenCount[processInstanceId]--;\r\n\r\n      self._eventBus.fire(GENERATE_TOKEN_EVENT, {\r\n        element: element,\r\n        processInstanceId: processInstanceId\r\n      });\r\n    });\r\n  } else if (eventBasedGatewaysHaveTokens.length) {\r\n    contextPad = domify('<div class=\"context-pad\" title=\"Trigger Event\"><i class=\"fa fa-play\"></i></div>');\r\n\r\n    domEvent.bind(contextPad, 'click', function() {\r\n      eventBasedGatewaysHaveTokens.forEach(function(eventBasedGateway) {\r\n        eventBasedGateway.tokenCount[processInstanceId]--;\r\n      });\r\n\r\n      self._eventBus.fire(GENERATE_TOKEN_EVENT, {\r\n        element: element,\r\n        processInstanceId: processInstanceId\r\n      });\r\n    });\r\n  } else {\r\n    return;\r\n  }\r\n\r\n  return [{\r\n    element: element,\r\n    html: contextPad\r\n  }];\r\n};\r\n\r\nIntermeditateCatchEventHandler.$inject = [ 'eventBus' ];\r\n\r\nmodule.exports = IntermeditateCatchEventHandler;", "'use strict';\n\nvar domify = require('min-dom/lib/domify'),\n    domEvent = require('min-dom/lib/event');\n\n/**\n * Is used for subprocesses and participants.\n */\nfunction ProcessHandler(processInstances, processInstanceSettings) {\n  this._processInstances = processInstances;\n  this._processInstanceSettings = processInstanceSettings;\n}\n\nProcessHandler.prototype.createContextPads = function(element) {\n  var self = this;\n\n  var processInstances = this._processInstances\n    .getProcessInstances(element)\n    .filter(function(processInstance) {\n      return !processInstance.isFinished;\n    });\n\n  if (processInstances.length < 2) {\n    return;\n  }\n\n  var contextPad = domify('<div class=\"context-pad\" title=\"View Process Instances\"><i class=\"fa fa-list-ol\"></i></div>');\n\n  domEvent.bind(contextPad, 'click', function() {\n    self._processInstanceSettings.showNext(element);\n  });\n\n  return [{\n    element: element,\n    html: contextPad\n  }];\n};\n\nProcessHandler.$inject = [ 'processInstances', 'processInstanceSettings' ];\n\nmodule.exports = ProcessHandler;", "'use strict';\r\n\r\nvar domify = require('min-dom/lib/domify'),\r\n    domEvent = require('min-dom/lib/event');\r\n\r\nvar is = require('../../../util/ElementHelper').is;\r\n\r\nvar events = require('../../../util/EventHelper'),\r\n    GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT;\r\n\r\nfunction StartEventHandler(eventBus, elementRegistry, animation) {\r\n  this._eventBus = eventBus;\r\n  this._elementRegistry = elementRegistry;\r\n  this._animation = animation;\r\n}\r\n\r\nStartEventHandler.prototype.createContextPads = function(element) {\r\n  var tokens = false;\r\n\r\n  this._elementRegistry.forEach(function(element) {\r\n    if (element.tokenCount) {\r\n      Object.values(element.tokenCount).forEach(function(tokenCount) {\r\n        if (tokenCount) {\r\n          tokens = true;\r\n        }\r\n      });\r\n    }\r\n  });\r\n\r\n  if (is(element.parent, 'bpmn:SubProcess') ||\r\n      tokens ||\r\n      this._animation.animations.length) {\r\n    return;\r\n  }\r\n\r\n  var self = this;\r\n\r\n  var contextPad = domify('<div class=\"context-pad\"><i class=\"fa fa-play\"></i></div>');\r\n\r\n  domEvent.bind(contextPad, 'click', function() {\r\n    self._eventBus.fire(GENERATE_TOKEN_EVENT, {\r\n      element: element\r\n    });\r\n  });\r\n\r\n  return [{\r\n    element: element,\r\n    html: contextPad\r\n  }];\r\n};\r\n\r\nStartEventHandler.$inject = [ 'eventBus', 'elementRegistry', 'animation' ];\r\n\r\nmodule.exports = StartEventHandler;", "'use strict';\r\n\r\nvar elementHelper = require('../../util/ElementHelper'),\r\n    isAncestor = elementHelper.isAncestor;\r\n\r\nvar events = require('../../util/EventHelper'),\r\n    TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT,\r\n    RESET_SIMULATION_EVENT = events.RESET_SIMULATION_EVENT,\r\n    TERMINATE_EVENT = events.TERMINATE_EVENT,\r\n    UPDATE_ELEMENTS_EVENT = events.UPDATE_ELEMENTS_EVENT,\r\n    UPDATE_ELEMENT_EVENT = events.UPDATE_ELEMENT_EVENT,\r\n    PROCESS_INSTANCE_SHOWN_EVENT = events.PROCESS_INSTANCE_SHOWN_EVENT;\r\n\r\nvar BoundaryEventHandler = require('./handler/BoundaryEventHandler'),\r\n    ExclusiveGatewayHandler = require('./handler/ExclusiveGatewayHandler'),\r\n    IntermediateCatchEventHandler = require('./handler/IntermediateCatchEventHandler'),\r\n    ProcessHandler = require('./handler/ProcessHandler'),\r\n    StartEventHandler = require('./handler/StartEventHandler');\r\n\r\nvar LOW_PRIORITY = 500;\r\n\r\nvar OFFSET_TOP = -15,\r\n    OFFSET_LEFT = -15;\r\n\r\nfunction ContextPads(eventBus, elementRegistry, overlays, injector, canvas, processInstances) {\r\n  var self = this;\r\n\r\n  this._elementRegistry = elementRegistry;\r\n  this._overlays = overlays;\r\n  this._injector = injector;\r\n  this._canvas = canvas;\r\n  this._processInstances = processInstances;\r\n\r\n  this.overlayIds = {};\r\n\r\n  this.handlers = {};\r\n\r\n  this.registerHandler('bpmn:ExclusiveGateway', ExclusiveGatewayHandler);\r\n  this.registerHandler('bpmn:IntermediateCatchEvent', IntermediateCatchEventHandler);\r\n  this.registerHandler('bpmn:SubProcess', ProcessHandler);\r\n  this.registerHandler('bpmn:SubProcess', BoundaryEventHandler);\r\n  this.registerHandler('bpmn:StartEvent', StartEventHandler);\r\n\r\n  eventBus.on(TOGGLE_MODE_EVENT, LOW_PRIORITY, function(context) {\r\n    var simulationModeActive = context.simulationModeActive;\r\n\r\n    if (simulationModeActive) {\r\n      self.openContextPads();\r\n    } else {\r\n      self.closeContextPads();\r\n    }\r\n  });\r\n\r\n  eventBus.on(RESET_SIMULATION_EVENT, LOW_PRIORITY, function() {\r\n    self.closeContextPads();\r\n    self.openContextPads();\r\n  });\r\n\r\n  eventBus.on(TERMINATE_EVENT, LOW_PRIORITY, function(context) {\r\n    var element = context.element,\r\n        parent = element.parent;\r\n\r\n    self.closeContextPads(parent);\r\n  });\r\n\r\n  eventBus.on(UPDATE_ELEMENTS_EVENT, LOW_PRIORITY, function(context) {\r\n    var elements = context.elements;\r\n\r\n    elements.forEach(function(element) {\r\n      self.closeElementContextPads(element);\r\n      self.openElementContextPads(element);\r\n    });\r\n  });\r\n\r\n  eventBus.on(UPDATE_ELEMENT_EVENT, LOW_PRIORITY, function(context) {\r\n    var element = context.element;\r\n\r\n    self.closeElementContextPads(element);\r\n    self.openElementContextPads(element);\r\n  });\r\n\r\n  eventBus.on(PROCESS_INSTANCE_SHOWN_EVENT, function(context) {\r\n    var processInstanceId = context.processInstanceId;\r\n\r\n    var processInstance = processInstances.getProcessInstance(processInstanceId),\r\n        parent = processInstance.parent;\r\n\r\n    self.closeContextPads(parent);\r\n    self.openContextPads(parent);\r\n  });\r\n}\r\n\r\n/**\r\n * Register a handler for an element type.\r\n * An element type can have multiple handlers.\r\n *\r\n * @param {String} type\r\n * @param {Object} handlerCls\r\n */\r\nContextPads.prototype.registerHandler = function(type, handlerCls) {\r\n  var handler = this._injector.instantiate(handlerCls);\r\n\r\n  if (!this.handlers[type]) {\r\n    this.handlers[type] = [];\r\n  }\r\n\r\n  this.handlers[type].push(handler);\r\n};\r\n\r\nContextPads.prototype.openContextPads = function(parent) {\r\n  var self = this;\r\n\r\n  if (!parent) {\r\n    parent = this._canvas.getRootElement();\r\n  }\r\n\r\n  this._elementRegistry.forEach(function(element) {\r\n    if (self.handlers[element.type]\r\n        && isAncestor(parent, element)) {\r\n      self.openElementContextPads(element);\r\n    }\r\n  });\r\n};\r\n\r\nContextPads.prototype.openElementContextPads = function(element) {\r\n  if (!this.handlers[element.type]) {\r\n    return;\r\n  }\r\n\r\n  var elementContextPads = [];\r\n\r\n  this.handlers[element.type].forEach(function(handler) {\r\n    var contextPads = handler.createContextPads(element);\r\n\r\n    if (contextPads) {\r\n      contextPads.forEach(function(contextPad) {\r\n        if (contextPad) {\r\n          elementContextPads.push(contextPad);\r\n        }\r\n      });\r\n    }\r\n  });\r\n\r\n  var self = this;\r\n\r\n  elementContextPads.forEach(function(contextPad) {\r\n    var position = { top: OFFSET_TOP, left: OFFSET_LEFT };\r\n\r\n    var overlayId = self._overlays.add(contextPad.element, 'context-menu', {\r\n      position: position,\r\n      html: contextPad.html,\r\n      show: {\r\n        minZoom: 0.5\r\n      }\r\n    });\r\n\r\n    self.overlayIds[contextPad.element.id] = overlayId;\r\n  });\r\n};\r\n\r\nContextPads.prototype.closeContextPads = function(parent) {\r\n  var self = this;\r\n\r\n  if (!parent) {\r\n    parent = this._canvas.getRootElement();\r\n  }\r\n\r\n  this._elementRegistry.forEach(function(element) {\r\n    if (isAncestor(parent, element)) {\r\n      self.closeElementContextPads(element);\r\n    }\r\n  });\r\n};\r\n\r\nContextPads.prototype.closeElementContextPads = function(element) {\r\n  var self = this;\r\n\r\n  if (element.attachers && element.attachers.length > 0) {\r\n    element.attachers.forEach(function(attachedElement) {\r\n      self.closeElementContextPads(attachedElement);\r\n    });\r\n  }\r\n  if (element.children && element.children.length > 0) {\r\n    element.children.forEach(function(child) {\r\n      self.closeElementContextPads(child);\r\n    });\r\n  }\r\n\r\n  var overlayId = this.overlayIds[element.id];\r\n\r\n  if (!overlayId) {\r\n    return;\r\n  }\r\n\r\n  this._overlays.remove(overlayId);\r\n\r\n  delete this.overlayIds[element.id];\r\n};\r\n\r\nContextPads.$inject = [ 'eventBus', 'elementRegistry', 'overlays', 'injector', 'canvas', 'processInstances' ];\r\n\r\nmodule.exports = ContextPads;", "module.exports = require('./ContextPads');", "'use strict';\r\n\r\nvar events = require('../../util/EventHelper'),\r\n    TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;\r\n\r\nvar HIGH_PRIORITY = 10001;\r\n\r\nfunction DisableModeling(\r\n    eventBus,\r\n    contextPad,\r\n    dragging,\r\n    directEditing,\r\n    editorActions,\r\n    modeling,\r\n    palette,\r\n    paletteProvider) {\r\n  var self = this;\r\n\r\n  this._eventBus = eventBus;\r\n\r\n  this.modelingDisabled = false;\r\n\r\n  eventBus.on(TOGGLE_MODE_EVENT, HIGH_PRIORITY, function(context) {\r\n    var simulationModeActive = context.simulationModeActive;\r\n\r\n    self.modelingDisabled = simulationModeActive;\r\n\r\n    if (self.modelingDisabled) {\r\n      directEditing.cancel();\r\n      contextPad.close();\r\n      dragging.cancel();\r\n    }\r\n\r\n    palette._update();\r\n  });\r\n\r\n  function intercept(obj, fnName, cb) {\r\n    var fn = obj[fnName];\r\n    obj[fnName] = function() {\r\n      return cb.call(this, fn, arguments);\r\n    };\r\n  }\r\n\r\n  function ignoreIfModelingDisabled(obj, fnName) {\r\n    intercept(obj, fnName, function(fn, args) {\r\n      if (self.modelingDisabled) {\r\n        return;\r\n      }\r\n\r\n      return fn.apply(this, args);\r\n    });\r\n  }\r\n\r\n  function throwIfModelingDisabled(obj, fnName) {\r\n    intercept(obj, fnName, function(fn, args) {\r\n      if (self.modelingDisabled) {\r\n        throw new Error('model is read-only');\r\n      }\r\n\r\n      return fn.apply(this, args);\r\n    });\r\n  }\r\n\r\n  ignoreIfModelingDisabled(contextPad, 'open');\r\n\r\n  ignoreIfModelingDisabled(dragging, 'init');\r\n\r\n  ignoreIfModelingDisabled(directEditing, 'activate');\r\n\r\n  ignoreIfModelingDisabled(dragging, 'init');\r\n\r\n  ignoreIfModelingDisabled(directEditing, 'activate');\r\n\r\n  throwIfModelingDisabled(modeling, 'moveShape');\r\n  throwIfModelingDisabled(modeling, 'updateAttachment');\r\n  throwIfModelingDisabled(modeling, 'moveElements');\r\n  throwIfModelingDisabled(modeling, 'moveConnection');\r\n  throwIfModelingDisabled(modeling, 'layoutConnection');\r\n  throwIfModelingDisabled(modeling, 'createConnection');\r\n  throwIfModelingDisabled(modeling, 'createShape');\r\n  throwIfModelingDisabled(modeling, 'createLabel');\r\n  throwIfModelingDisabled(modeling, 'appendShape');\r\n  throwIfModelingDisabled(modeling, 'removeElements');\r\n  throwIfModelingDisabled(modeling, 'distributeElements');\r\n  throwIfModelingDisabled(modeling, 'removeShape');\r\n  throwIfModelingDisabled(modeling, 'removeConnection');\r\n  throwIfModelingDisabled(modeling, 'replaceShape');\r\n  throwIfModelingDisabled(modeling, 'pasteElements');\r\n  throwIfModelingDisabled(modeling, 'alignElements');\r\n  throwIfModelingDisabled(modeling, 'resizeShape');\r\n  throwIfModelingDisabled(modeling, 'createSpace');\r\n  throwIfModelingDisabled(modeling, 'updateWaypoints');\r\n  throwIfModelingDisabled(modeling, 'reconnectStart');\r\n  throwIfModelingDisabled(modeling, 'reconnectEnd');\r\n\r\n  intercept(editorActions, 'trigger', function(fn, args) {\r\n    var action = args[0];\r\n\r\n    if (self.modelingDisabled && isAnyAction([\r\n      'undo',\r\n      'redo',\r\n      'copy',\r\n      'paste',\r\n      'removeSelection',\r\n      'spaceTool',\r\n      'lassoTool',\r\n      'globalConnectTool',\r\n      'distributeElements',\r\n      'alignElements',\r\n      'directEditing',\r\n    ], action)) {\r\n      return;\r\n    }\r\n\r\n    return fn.apply(this, args);\r\n  });\r\n}\r\n\r\nDisableModeling.$inject = [\r\n  'eventBus',\r\n  'contextPad',\r\n  'dragging',\r\n  'directEditing',\r\n  'editorActions',\r\n  'modeling',\r\n  'palette',\r\n  'paletteProvider',\r\n];\r\n\r\nmodule.exports = DisableModeling;\r\n\r\n// helpers //////////\r\n\r\nfunction isAnyAction(actions, action) {\r\n  return actions.indexOf(action) > -1;\r\n}", "module.exports = require('./DisableModeling');", "'use strict';\r\n\r\nvar domify = require('min-dom/lib/domify');\r\n\r\nvar events = require('../../util/EventHelper'),\r\n    TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT,\r\n    RESET_SIMULATION_EVENT = events.RESET_SIMULATION_EVENT,\r\n    GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT;\r\n\r\nvar OFFSET_TOP = -15,\r\n    OFFSET_RIGHT = 15;\r\n\r\nfunction ElementNotifications(overlays, eventBus) {\r\n  var self = this;\r\n\r\n  this._overlays = overlays;\r\n\r\n  eventBus.on(TOGGLE_MODE_EVENT, function(context) {\r\n    var simulationModeActive = context.simulationModeActive;\r\n\r\n    if (!simulationModeActive) {\r\n      self.removeElementNotifications();\r\n    }\r\n  });\r\n\r\n  eventBus.on([\r\n    RESET_SIMULATION_EVENT,\r\n    GENERATE_TOKEN_EVENT\r\n  ], function() {\r\n    self.removeElementNotifications();\r\n  });\r\n}\r\n\r\nElementNotifications.prototype.addElementNotifications = function(elements, options) {\r\n  var self = this;\r\n\r\n  elements.forEach(function(element) {\r\n    self.addElementNotification(element, options);\r\n  });\r\n};\r\n\r\nElementNotifications.prototype.addElementNotification = function(element, options) {\r\n  var position = {\r\n    top: OFFSET_TOP,\r\n    right: OFFSET_RIGHT\r\n  };\r\n\r\n  var markup =\r\n    '<div class=\"element-notification ' + (options.type || '') + '\">' +\r\n      (options.icon ? '<i class=\"fa ' + options.icon + '\"></i>' : '') +\r\n      ('<span class=\"text\">' + options.text + '</span>' || '') +\r\n    '</div>';\r\n\r\n  var html = domify(markup);\r\n\r\n  this._overlays.add(element, 'element-notification', {\r\n    position: position,\r\n    html: html,\r\n    show: {\r\n      minZoom: 0.5\r\n    }\r\n  });\r\n};\r\n\r\nElementNotifications.prototype.removeElementNotifications = function(elements) {\r\n  var self = this;\r\n\r\n  if (!elements) {\r\n    this._overlays.remove({ type: 'element-notification' });\r\n  } else {\r\n    elements.forEach(function(element) {\r\n      self.removeElementNotification(element);\r\n    });\r\n  }\r\n};\r\n\r\nElementNotifications.prototype.removeElementNotification = function(element) {\r\n  this._overlays.remove({ element: element });\r\n};\r\n\r\nElementNotifications.$inject = [ 'overlays', 'eventBus' ];\r\n\r\nmodule.exports = ElementNotifications;", "module.exports = require('./ElementNotifications');", "module.exports = function(arr, obj){\n  if (arr.indexOf) return arr.indexOf(obj);\n  for (var i = 0; i < arr.length; ++i) {\n    if (arr[i] === obj) return i;\n  }\n  return -1;\n};", "/**\n * Module dependencies.\n */\n\ntry {\n  var index = require('indexof');\n} catch (err) {\n  var index = require('component-indexof');\n}\n\n/**\n * Whitespace regexp.\n */\n\nvar re = /\\s+/;\n\n/**\n * toString reference.\n */\n\nvar toString = Object.prototype.toString;\n\n/**\n * Wrap `el` in a `ClassList`.\n *\n * @param {Element} el\n * @return {ClassList}\n * @api public\n */\n\nmodule.exports = function(el){\n  return new ClassList(el);\n};\n\n/**\n * Initialize a new ClassList for `el`.\n *\n * @param {Element} el\n * @api private\n */\n\nfunction ClassList(el) {\n  if (!el || !el.nodeType) {\n    throw new Error('A DOM element reference is required');\n  }\n  this.el = el;\n  this.list = el.classList;\n}\n\n/**\n * Add class `name` if not already present.\n *\n * @param {String} name\n * @return {ClassList}\n * @api public\n */\n\nClassList.prototype.add = function(name){\n  // classList\n  if (this.list) {\n    this.list.add(name);\n    return this;\n  }\n\n  // fallback\n  var arr = this.array();\n  var i = index(arr, name);\n  if (!~i) arr.push(name);\n  this.el.className = arr.join(' ');\n  return this;\n};\n\n/**\n * Remove class `name` when present, or\n * pass a regular expression to remove\n * any which match.\n *\n * @param {String|RegExp} name\n * @return {ClassList}\n * @api public\n */\n\nClassList.prototype.remove = function(name){\n  if ('[object RegExp]' == toString.call(name)) {\n    return this.removeMatching(name);\n  }\n\n  // classList\n  if (this.list) {\n    this.list.remove(name);\n    return this;\n  }\n\n  // fallback\n  var arr = this.array();\n  var i = index(arr, name);\n  if (~i) arr.splice(i, 1);\n  this.el.className = arr.join(' ');\n  return this;\n};\n\n/**\n * Remove all classes matching `re`.\n *\n * @param {RegExp} re\n * @return {ClassList}\n * @api private\n */\n\nClassList.prototype.removeMatching = function(re){\n  var arr = this.array();\n  for (var i = 0; i < arr.length; i++) {\n    if (re.test(arr[i])) {\n      this.remove(arr[i]);\n    }\n  }\n  return this;\n};\n\n/**\n * Toggle class `name`, can force state via `force`.\n *\n * For browsers that support classList, but do not support `force` yet,\n * the mistake will be detected and corrected.\n *\n * @param {String} name\n * @param {Boolean} force\n * @return {ClassList}\n * @api public\n */\n\nClassList.prototype.toggle = function(name, force){\n  // classList\n  if (this.list) {\n    if (\"undefined\" !== typeof force) {\n      if (force !== this.list.toggle(name, force)) {\n        this.list.toggle(name); // toggle again to correct\n      }\n    } else {\n      this.list.toggle(name);\n    }\n    return this;\n  }\n\n  // fallback\n  if (\"undefined\" !== typeof force) {\n    if (!force) {\n      this.remove(name);\n    } else {\n      this.add(name);\n    }\n  } else {\n    if (this.has(name)) {\n      this.remove(name);\n    } else {\n      this.add(name);\n    }\n  }\n\n  return this;\n};\n\n/**\n * Return an array of classes.\n *\n * @return {Array}\n * @api public\n */\n\nClassList.prototype.array = function(){\n  var className = this.el.getAttribute('class') || '';\n  var str = className.replace(/^\\s+|\\s+$/g, '');\n  var arr = str.split(re);\n  if ('' === arr[0]) arr.shift();\n  return arr;\n};\n\n/**\n * Check if class `name` is present.\n *\n * @param {String} name\n * @return {ClassList}\n * @api public\n */\n\nClassList.prototype.has =\nClassList.prototype.contains = function(name){\n  return this.list\n    ? this.list.contains(name)\n    : !! ~index(this.array(), name);\n};\n", "module.exports = require('component-classes');", "'use strict';\r\n\r\nvar domClasses = require('min-dom/lib/classes');\r\n\r\nvar elementHelper = require('../../util/ElementHelper'),\r\n    is = elementHelper.is,\r\n    SUPPORTED_ELEMENTS = elementHelper.supportedElements;\r\n\r\nvar events = require('../../util/EventHelper'),\r\n    TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT,\r\n    GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT;\r\n\r\nvar IGNORED_ELEMENTS = [\r\n  'bpmn:Process',\r\n  'bpmn:Collaboration',\r\n  'bpmn:Participant',\r\n  'bpmn:Lane',\r\n  'bpmn:TextAnnotation'\r\n];\r\n\r\nfunction isLabel(element) {\r\n  return element.labelTarget;\r\n}\r\n\r\nfunction ElementSupport(eventBus, elementRegistry, canvas, notifications, elementNotifications) {\r\n  var self = this;\r\n\r\n  this._eventBus = eventBus;\r\n  this._elementRegistry = elementRegistry;\r\n  this._elementNotifications = elementNotifications;\r\n  this._notifications = notifications;\r\n\r\n  this.canvasParent = canvas.getContainer().parentNode;\r\n\r\n  eventBus.on(GENERATE_TOKEN_EVENT, 20000, function(context) {\r\n    var element = context.element;\r\n\r\n    if (!is(element, 'bpmn:StartEvent')) {\r\n      return;\r\n    }\r\n\r\n    if (!self.allElementsSupported()) {\r\n      self.showWarnings();\r\n\r\n      domClasses(self.canvasParent).add('warning');\r\n\r\n      // cancel event\r\n      return true;\r\n    }\r\n  });\r\n\r\n  eventBus.on(TOGGLE_MODE_EVENT, function(context) {\r\n    var simulationModeActive = context.simulationModeActive;\r\n\r\n    if (!simulationModeActive) {\r\n      domClasses(self.canvasParent).remove('warning');\r\n    }\r\n  });\r\n}\r\n\r\nElementSupport.prototype.allElementsSupported = function() {\r\n  var allElementsSupported = true;\r\n\r\n  this._elementRegistry.forEach(function(element) {\r\n    if (!is(element, IGNORED_ELEMENTS)\r\n        && !is(element, SUPPORTED_ELEMENTS)\r\n        && !isLabel(element)\r\n    ) {\r\n      allElementsSupported = false;\r\n    }\r\n  });\r\n\r\n  return allElementsSupported;\r\n};\r\n\r\nElementSupport.prototype.showWarnings = function(elements) {\r\n  var self = this;\r\n\r\n  var warnings = [];\r\n\r\n  this._elementRegistry.forEach(function(element) {\r\n    if (!is(element, IGNORED_ELEMENTS)\r\n        && !is(element, SUPPORTED_ELEMENTS)\r\n        && !isLabel(element)\r\n    ) {\r\n      self.showWarning(element);\r\n\r\n      if (warnings.indexOf(element.type)) {\r\n        self._notifications.showNotification(element.type + ' not supported', 'warning');\r\n\r\n        warnings.push(element.type);\r\n      }\r\n    }\r\n  });\r\n};\r\n\r\nElementSupport.prototype.showWarning = function(element) {\r\n  this._elementNotifications.addElementNotification(element, {\r\n    type: 'warning',\r\n    icon: 'fa-exclamation-triangle',\r\n    text: 'Not supported'\r\n  });\r\n};\r\n\r\nElementSupport.$inject = [ 'eventBus', 'elementRegistry', 'canvas', 'notifications', 'elementNotifications' ];\r\n\r\nmodule.exports = ElementSupport;", "module.exports = require('./ElementSupport');", "'use strict';\r\n\r\nvar is = require('../../util/ElementHelper').is;\r\n\r\nvar events = require('../../util/EventHelper'),\r\n    TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;\r\n\r\nvar NOT_SELECTED_COLOR = getComputedStyle(document.documentElement).getPropertyValue('--token-simulation-grey-lighten-56'),\r\n    SELECTED_COLOR = getComputedStyle(document.documentElement).getPropertyValue('--token-simulation-grey-darken-30');\r\n\r\nfunction getNext(gateway) {\r\n  var outgoing = gateway.outgoing.filter(isSequenceFlow);\r\n\r\n  var index = outgoing.indexOf(gateway.sequenceFlow);\r\n\r\n  if (outgoing[index + 1]) {\r\n    return outgoing[index + 1];\r\n  } else {\r\n    return outgoing[0];\r\n  }\r\n}\r\n\r\nfunction isSequenceFlow(connection) {\r\n  return is(connection, 'bpmn:SequenceFlow');\r\n}\r\n\r\n\r\nfunction ExclusiveGatewaySettings(eventBus, elementRegistry, graphicsFactory) {\r\n  var self = this;\r\n\r\n  this._elementRegistry = elementRegistry;\r\n  this._graphicsFactory = graphicsFactory;\r\n\r\n  eventBus.on(TOGGLE_MODE_EVENT, function(context) {\r\n    var simulationModeActive = context.simulationModeActive;\r\n\r\n    if (!simulationModeActive) {\r\n      self.resetSequenceFlows();\r\n    } else {\r\n      self.setSequenceFlowsDefault();\r\n    }\r\n  });\r\n}\r\n\r\nExclusiveGatewaySettings.prototype.setSequenceFlowsDefault = function() {\r\n  var self = this;\r\n\r\n  var exclusiveGateways = this._elementRegistry.filter(function(element) {\r\n    return is(element, 'bpmn:ExclusiveGateway');\r\n  });\r\n\r\n  exclusiveGateways.forEach(function(exclusiveGateway) {\r\n    if (exclusiveGateway.outgoing.filter(isSequenceFlow).length) {\r\n      self.setSequenceFlow(\r\n        exclusiveGateway,\r\n        exclusiveGateway.outgoing.filter(isSequenceFlow)[0]\r\n      );\r\n    }\r\n  });\r\n};\r\n\r\nExclusiveGatewaySettings.prototype.resetSequenceFlows = function() {\r\n  var self = this;\r\n\r\n  var exclusiveGateways = this._elementRegistry.filter(function(element) {\r\n    return is(element, 'bpmn:ExclusiveGateway');\r\n  });\r\n\r\n  exclusiveGateways.forEach(function(exclusiveGateway) {\r\n    if (exclusiveGateway.outgoing.filter(isSequenceFlow).length) {\r\n      self.resetSequenceFlow(exclusiveGateway);\r\n    }\r\n  });\r\n};\r\n\r\nExclusiveGatewaySettings.prototype.resetSequenceFlow = function(gateway) {\r\n  if (gateway.sequenceFlow) {\r\n    delete gateway.sequenceFlow;\r\n  }\r\n};\r\n\r\nExclusiveGatewaySettings.prototype.setSequenceFlow = function(gateway) {\r\n  var self = this;\r\n\r\n  var outgoing = gateway.outgoing.filter(isSequenceFlow);\r\n\r\n  if (!outgoing.length) {\r\n    return;\r\n  }\r\n\r\n  var sequenceFlow = gateway.sequenceFlow;\r\n\r\n  if (sequenceFlow) {\r\n\r\n    // set next sequence flow\r\n    gateway.sequenceFlow = getNext(gateway);\r\n  } else {\r\n\r\n    // set first sequence flow\r\n    gateway.sequenceFlow = outgoing[0];\r\n  }\r\n\r\n  // set colors\r\n  gateway.outgoing.forEach(function(outgoing) {\r\n    if (outgoing === gateway.sequenceFlow) {\r\n      self.setColor(outgoing, SELECTED_COLOR);\r\n    } else {\r\n      self.setColor(outgoing, NOT_SELECTED_COLOR);\r\n    }\r\n  });\r\n};\r\n\r\nExclusiveGatewaySettings.prototype.setColor = function(sequenceFlow, color) {\r\n  var businessObject = sequenceFlow.businessObject;\r\n\r\n  businessObject.di.set('stroke', color);\r\n\r\n  var gfx = this._elementRegistry.getGraphics(sequenceFlow);\r\n\r\n  this._graphicsFactory.update('connection', sequenceFlow, gfx);\r\n};\r\n\r\nExclusiveGatewaySettings.$inject = [ 'eventBus', 'elementRegistry', 'graphicsFactory' ];\r\n\r\nmodule.exports = ExclusiveGatewaySettings;", "module.exports = require('./ExclusiveGatewaySettings');", "'use strict';\r\n\r\nvar domify = require('min-dom/lib/domify'),\r\n    domClasses = require('min-dom/lib/classes'),\r\n    domEvent = require('min-dom/lib/event'),\r\n    domQuery = require('min-dom/lib/query');\r\n\r\nvar elementHelper = require('../../util/ElementHelper'),\r\n    getBusinessObject = elementHelper.getBusinessObject,\r\n    is = elementHelper.is,\r\n    isTypedEvent = elementHelper.isTypedEvent;\r\n\r\nvar events = require('../../util/EventHelper'),\r\n    GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT,\r\n    CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT,\r\n    TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT,\r\n    RESET_SIMULATION_EVENT = events.RESET_SIMULATION_EVENT,\r\n    PROCESS_INSTANCE_CREATED_EVENT = events.PROCESS_INSTANCE_CREATED_EVENT;\r\n\r\nfunction getElementName(element) {\r\n  return (element && element.businessObject.name);\r\n}\r\n\r\nfunction Log(eventBus, notifications, tokenSimulationPalette, canvas) {\r\n  var self = this;\r\n\r\n  this._notifications = notifications;\r\n  this._tokenSimulationPalette = tokenSimulationPalette;\r\n  this._canvas = canvas;\r\n\r\n  this._init();\r\n\r\n  eventBus.on(GENERATE_TOKEN_EVENT, function(context) {\r\n    var element = context.element,\r\n        elementName = getElementName(element);\r\n\r\n    if (is(element, 'bpmn:BusinessRuleTask')) {\r\n      self.log(elementName || 'Business Rule Task', 'info', 'bpmn-icon-business-rule');\r\n    } else if (is(element, 'bpmn:CallActivity')) {\r\n      self.log(elementName || 'Call Activity', 'info', 'bpmn-icon-call-activity');\r\n    } else if (is(element, ['bpmn:IntermediateCatchEvent', 'bpmn:IntermediateThrowEvent'])) {\r\n      self.log(elementName || 'Intermediate Event', 'info', 'bpmn-icon-intermediate-event-none');\r\n    } else if (is(element, 'bpmn:ManualTask')) {\r\n      self.log(elementName || 'Manual Task', 'info', 'bpmn-icon-manual');\r\n    } else if (is(element, 'bpmn:ScriptTask')) {\r\n      self.log(elementName || 'Script Task', 'info', 'bpmn-icon-script');\r\n    } else if (is(element, 'bpmn:ServiceTask')) {\r\n      self.log(elementName || 'Service Task', 'info', 'bpmn-icon-service');\r\n    } else if (is(element, 'bpmn:StartEvent')) {\r\n      self.log(elementName || 'Start Event', 'info', 'bpmn-icon-start-event-none');\r\n    } else if (is(element, 'bpmn:Task')) {\r\n      self.log(elementName || 'Task', 'info', 'bpmn-icon-task');\r\n    } else if (is(element, 'bpmn:UserTask')) {\r\n      self.log(elementName || 'User Task', 'info', 'bpmn-icon-user');\r\n    } else if (is(element, 'bpmn:ExclusiveGateway')) {\r\n      if (element.outgoing.length < 2) {\r\n        return;\r\n      }\r\n\r\n      var sequenceFlowName = getElementName(element.sequenceFlow);\r\n\r\n      var text = elementName || 'Gateway';\r\n\r\n      if (sequenceFlowName) {\r\n        text = text.concat(' <i class=\"fa fa-angle-right\" aria-hidden=\"true\"></i> ' + sequenceFlowName);\r\n      }\r\n\r\n      self.log(text, 'info', 'bpmn-icon-gateway-xor');\r\n    }\r\n  });\r\n\r\n  eventBus.on(CONSUME_TOKEN_EVENT, function(context) {\r\n    var element = context.element,\r\n        elementName = getElementName(element);\r\n\r\n    if (is(element, 'bpmn:EndEvent')) {\r\n\r\n      if (isTypedEvent(getBusinessObject(element), 'bpmn:TerminateEventDefinition')) {\r\n        self.log(elementName || 'Terminate End Event', 'info', 'bpmn-icon-end-event-terminate');\r\n      } else {\r\n        self.log(elementName || 'End Event', 'info', 'bpmn-icon-end-event-none');\r\n      }\r\n    }\r\n  });\r\n\r\n  eventBus.on(PROCESS_INSTANCE_CREATED_EVENT, function(context) {\r\n    var processInstanceId = context.processInstanceId,\r\n        parent = context.parent;\r\n\r\n    if (is(parent, 'bpmn:Process')) {\r\n      self.log('Process ' + processInstanceId + ' started', 'success', 'fa-check');\r\n    } else {\r\n      self.log('Subprocess ' + processInstanceId + ' started', 'info', 'fa-check');\r\n    }\r\n  });\r\n\r\n  eventBus.on(TOGGLE_MODE_EVENT, function(context) {\r\n    var simulationModeActive = context.simulationModeActive;\r\n\r\n    if (!simulationModeActive) {\r\n      self.emptyLog();\r\n\r\n      domClasses(self.container).add('hidden');\r\n    }\r\n  });\r\n\r\n  eventBus.on(RESET_SIMULATION_EVENT, function(context) {\r\n    self.emptyLog();\r\n\r\n    domClasses(self.container).add('hidden');\r\n  });\r\n}\r\n\r\nLog.prototype._init = function() {\r\n  var self = this;\r\n\r\n  this.container = domify(\r\n    '<div class=\"token-simulation-log hidden\">' +\r\n      '<div class=\"header\">' +\r\n        '<i class=\"fa fa-align-left\"></i>' +\r\n        '<button class=\"close\">' +\r\n          '<i class=\"fa fa-times\" aria-hidden=\"true\"></i>' +\r\n        '</button>' +\r\n      '</div>' +\r\n      '<div class=\"content\">' +\r\n        '<p class=\"entry placeholder\">No Entries</p>' +\r\n      '</div>' +\r\n    '</div>'\r\n  );\r\n\r\n  this.placeholder = domQuery('.placeholder', this.container);\r\n\r\n  this.content = domQuery('.content', this.container);\r\n\r\n  domEvent.bind(this.content, 'wheel', function(e) {\r\n    e.stopPropagation();\r\n  });\r\n\r\n  domEvent.bind(this.content, 'mousedown', function(e) {\r\n    e.stopPropagation();\r\n  });\r\n\r\n  this.close = domQuery('.close', this.container);\r\n\r\n  domEvent.bind(this.close, 'click', function() {\r\n    domClasses(self.container).add('hidden');\r\n  });\r\n\r\n  this.icon = domQuery('.fa-align-left', this.container);\r\n\r\n  domEvent.bind(this.icon, 'click', function() {\r\n    domClasses(self.container).add('hidden');\r\n  });\r\n\r\n  this._canvas.getContainer().appendChild(this.container);\r\n\r\n  this.paletteEntry = domify('<div class=\"entry\" title=\"Show Simulation Log\"><i class=\"fa fa-align-left\"></i></div>');\r\n\r\n  domEvent.bind(this.paletteEntry, 'click', function() {\r\n    domClasses(self.container).remove('hidden');\r\n  });\r\n\r\n  this._tokenSimulationPalette.addEntry(this.paletteEntry, 3);\r\n};\r\n\r\nLog.prototype.toggle = function() {\r\n  var container = this.container;\r\n\r\n  if (domClasses(container).has('hidden')) {\r\n    domClasses(container).remove('hidden');\r\n  } else {\r\n    domClasses(container).add('hidden');\r\n  }\r\n};\r\n\r\nLog.prototype.log = function(text, type, icon) {\r\n  domClasses(this.placeholder).add('hidden');\r\n\r\n  var date = new Date();\r\n\r\n  var dateString = date.toLocaleTimeString() + ':' + date.getUTCMilliseconds();\r\n\r\n  this._notifications.showNotification(text, type, icon);\r\n\r\n  var iconMarkup;\r\n\r\n  if (!icon) {\r\n    icon = 'fa-info';\r\n  }\r\n\r\n  if (icon.includes('bpmn')) {\r\n    iconMarkup = '<span class=\"icon ' + icon + '\">';\r\n  } else {\r\n    iconMarkup = '<i class=\"icon fa ' + icon + '\"></i>';\r\n  }\r\n\r\n  var logEntry = domify('<p class=\"entry ' + type + '\"><span class=\"date\">' + dateString + '</span>' + iconMarkup + '</span>' + text + '</p>');\r\n\r\n  this.content.appendChild(logEntry);\r\n\r\n  this.content.scrollTop = this.content.scrollHeight;\r\n};\r\n\r\nLog.prototype.emptyLog = function() {\r\n  while (this.content.firstChild) {\r\n    this.content.removeChild(this.content.firstChild);\r\n  }\r\n\r\n  this.placeholder = domify('<p class=\"entry placeholder\">No Entries</p>');\r\n\r\n  this.content.appendChild(this.placeholder);\r\n};\r\n\r\nLog.$inject = [ 'eventBus', 'notifications', 'tokenSimulationPalette', 'canvas' ];\r\n\r\nmodule.exports = Log;", "module.exports = require('./Log');", "'use strict';\n\nvar domify = require('min-dom/lib/domify');\n\nvar events = require('../../util/EventHelper'),\n    TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;\n\nvar NOTIFICATION_TIME_TO_LIVE = 2000; // ms\n\nfunction Notifications(eventBus, canvas) {\n  var self = this;\n\n  this._eventBus = eventBus;\n  this._canvas = canvas;\n\n  this._init();\n\n  eventBus.on(TOGGLE_MODE_EVENT, function(context) {\n    var simulationModeActive = context.simulationModeActive;\n\n    if (!simulationModeActive) {\n      self.removeAll();\n    }\n  });\n}\n\nNotifications.prototype._init = function() {\n  this.container = domify('<div class=\"notifications\"></div>');\n\n  this._canvas.getContainer().appendChild(this.container);\n};\n\nNotifications.prototype.showNotification = function(text, type, icon) {\n  var iconMarkup;\n\n  if (!icon) {\n    icon = 'fa-info';\n  }\n\n  if (icon.includes('bpmn')) {\n    iconMarkup = '<i class=\"' + icon + '\"></i>';\n  } else {\n    iconMarkup = '<i class=\"fa ' + icon + '\"></i>';\n  }\n\n  var notification = domify(\n    '<div class=\"notification ' + type + '\">' +\n      '<span class=\"icon\">' + iconMarkup + '</span>' +\n      text +\n    '</div>'\n  );\n\n  this.container.appendChild(notification);\n\n  // prevent more than 5 notifications at once\n  while (this.container.children.length > 5) {\n    this.container.children[0].remove();\n  }\n\n  setTimeout(function() {\n    notification.remove();\n  }, NOTIFICATION_TIME_TO_LIVE);\n};\n\nNotifications.prototype.removeAll = function() {\n  while (this.container.children.length) {\n    this.container.children[0].remove();\n  }\n};\n\nNotifications.$inject = [ 'eventBus', 'canvas' ];\n\nmodule.exports = Notifications;", "module.exports = require('./Notifications');", "'use strict';\r\n\r\nvar domify = require('min-dom/lib/domify'),\r\n    domClasses = require('min-dom/lib/classes'),\r\n    domEvent = require('min-dom/lib/event');\r\n\r\nvar events = require('../../util/EventHelper'),\r\n    TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT,\r\n    PLAY_SIMULATION_EVENT = events.PLAY_SIMULATION_EVENT,\r\n    PAUSE_SIMULATION_EVENT = events.PAUSE_SIMULATION_EVENT,\r\n    RESET_SIMULATION_EVENT = events.RESET_SIMULATION_EVENT,\r\n    ANIMATION_CREATED_EVENT = events.ANIMATION_CREATED_EVENT,\r\n    PROCESS_INSTANCE_CREATED_EVENT = events.PROCESS_INSTANCE_CREATED_EVENT;\r\n\r\nvar PLAY_MARKUP = '<i class=\"fa fa-play\"></i>',\r\n    PAUSE_MARKUP = '<i class=\"fa fa-pause\"></i>';\r\n\r\nfunction PauseSimulation(eventBus, tokenSimulationPalette, notifications, canvas) {\r\n  var self = this;\r\n\r\n  this._eventBus = eventBus;\r\n  this._tokenSimulationPalette = tokenSimulationPalette;\r\n  this._notifications = notifications;\r\n\r\n  this.canvasParent = canvas.getContainer().parentNode;\r\n\r\n  this.isActive = false;\r\n  this.isPaused = false;\r\n\r\n  this._init();\r\n\r\n  // unpause on simulation start\r\n  eventBus.on(PROCESS_INSTANCE_CREATED_EVENT, function(context) {\r\n    var parent = context.parent;\r\n\r\n    if (!parent.parent) {\r\n      self.activate();\r\n      self.unpause();\r\n\r\n      notifications.showNotification('Start Simulation', 'info');\r\n    }\r\n  });\r\n\r\n  eventBus.on([\r\n    RESET_SIMULATION_EVENT,\r\n    TOGGLE_MODE_EVENT\r\n  ], function() {\r\n    self.deactivate();\r\n    self.unpause();\r\n  });\r\n\r\n  eventBus.on(ANIMATION_CREATED_EVENT, function(context) {\r\n    var animation = context.animation;\r\n\r\n    if (self.isPaused) {\r\n      animation.pause();\r\n    }\r\n  });\r\n}\r\n\r\nPauseSimulation.prototype._init = function() {\r\n  this.paletteEntry = domify(\r\n    '<div class=\"entry disabled\" title=\"Play/Pause Simulation\">' +\r\n      PLAY_MARKUP +\r\n    '</div>'\r\n  );\r\n\r\n  domEvent.bind(this.paletteEntry, 'click', this.toggle.bind(this));\r\n\r\n  this._tokenSimulationPalette.addEntry(this.paletteEntry, 1);\r\n};\r\n\r\nPauseSimulation.prototype.toggle = function() {\r\n  if (!this.isActive) {\r\n    return;\r\n  }\r\n\r\n  if (this.isPaused) {\r\n    this.unpause();\r\n  } else {\r\n    this.pause();\r\n  }\r\n};\r\n\r\nPauseSimulation.prototype.pause = function() {\r\n  if (!this.isActive) {\r\n    return;\r\n  }\r\n\r\n  domClasses(this.paletteEntry).remove('active');\r\n  domClasses(this.canvasParent).add('paused');\r\n\r\n  this.paletteEntry.innerHTML = PLAY_MARKUP;\r\n\r\n  this._eventBus.fire(PAUSE_SIMULATION_EVENT);\r\n\r\n  this._notifications.showNotification('Pause Simulation', 'info');\r\n\r\n  this.isPaused = true;\r\n};\r\n\r\nPauseSimulation.prototype.unpause = function() {\r\n  if (!this.isActive) {\r\n    return;\r\n  }\r\n\r\n  domClasses(this.paletteEntry).add('active');\r\n  domClasses(this.canvasParent).remove('paused');\r\n\r\n  this.paletteEntry.innerHTML = PAUSE_MARKUP;\r\n\r\n  this._eventBus.fire(PLAY_SIMULATION_EVENT);\r\n\r\n  this._notifications.showNotification('Play Simulation', 'info');\r\n\r\n  this.isPaused = false;\r\n};\r\n\r\nPauseSimulation.prototype.activate = function() {\r\n  this.isActive = true;\r\n\r\n  domClasses(this.paletteEntry).remove('disabled');\r\n};\r\n\r\nPauseSimulation.prototype.deactivate = function() {\r\n  this.isActive = false;\r\n\r\n  domClasses(this.paletteEntry).remove('active');\r\n  domClasses(this.paletteEntry).add('disabled');\r\n};\r\n\r\nPauseSimulation.$inject = [ 'eventBus', 'tokenSimulationPalette', 'notifications', 'canvas' ];\r\n\r\nmodule.exports = PauseSimulation;", "module.exports = require('./PauseSimulation');", "'use strict';\n\nvar events = require('../../util/EventHelper'),\n    TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;\n\nvar VERY_HIGH_PRIORITY = 50000;\n\nfunction PreserveElementColors(eventBus, elementRegistry, graphicsFactory) {\n  var self = this;\n\n  this._elementRegistry = elementRegistry;\n  this._graphicsFactory = graphicsFactory;\n\n  this.elementColors = {};\n\n  eventBus.on(TOGGLE_MODE_EVENT, VERY_HIGH_PRIORITY, function(context) {\n    var simulationModeActive = context.simulationModeActive;\n\n    if (!simulationModeActive) {\n      self.resetColors();\n    } else {\n      self.preserveColors();\n    }\n  });\n}\n\nPreserveElementColors.prototype.preserveColors = function() {\n  var self = this;\n\n  this._elementRegistry.forEach(function(element) {\n    self.elementColors[element.id] = {\n      stroke: element.businessObject.di.get('stroke'),\n      fill: element.businessObject.di.get('fill')\n    };\n\n    self.setColor(element, '#000', '#fff');\n  });\n};\n\nPreserveElementColors.prototype.resetColors = function() {\n  var self = this;\n\n  this._elementRegistry.forEach(function(element) {\n    if (self.elementColors[element.id]) {\n      self.setColor(element, self.elementColors[element.id].stroke, self.elementColors[element.id].fill);\n    }\n  });\n\n  this.elementColors = {};\n};\n\nPreserveElementColors.prototype.setColor = function(element, stroke, fill) {\n  var businessObject = element.businessObject;\n\n  businessObject.di.set('stroke', stroke);\n  businessObject.di.set('fill', fill);\n\n  var gfx = this._elementRegistry.getGraphics(element);\n\n  var type = element.waypoints ? 'connection' : 'shape';\n\n  this._graphicsFactory.update(type, element, gfx);\n};\n\nPreserveElementColors.$inject = [ 'eventBus', 'elementRegistry', 'graphicsFactory' ];\n\nmodule.exports = PreserveElementColors;", "module.exports = require('./PreserveElementColors');", "'use strict';\n\nvar events = require('../../util/EventHelper'),\n    TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT,\n    RESET_SIMULATION_EVENT = events.RESET_SIMULATION_EVENT;\n\nfunction ProcessInstanceIds(eventBus) {\n  this.nextProcessInstanceId = 1;\n\n  eventBus.on(TOGGLE_MODE_EVENT, this.reset.bind(this));\n\n  eventBus.on(RESET_SIMULATION_EVENT, this.reset.bind(this));\n}\n\nProcessInstanceIds.prototype.getNext = function() {\n  var processInstanceId = this.nextProcessInstanceId;\n\n  this.nextProcessInstanceId++;\n\n  return processInstanceId;\n};\n\nProcessInstanceIds.prototype.reset = function() {\n  this.nextProcessInstanceId = 1;\n};\n\nProcessInstanceIds.$inject = [ 'eventBus' ];\n\nmodule.exports = ProcessInstanceIds;", "module.exports = require('./ProcessInstanceIds');", "'use strict';\n\nvar events = require('../../util/EventHelper'),\n    TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT,\n    PROCESS_INSTANCE_CREATED_EVENT = events.PROCESS_INSTANCE_CREATED_EVENT,\n    PROCESS_INSTANCE_FINISHED_EVENT = events.PROCESS_INSTANCE_FINISHED_EVENT,\n    PROCESS_INSTANCE_SHOWN_EVENT = events.PROCESS_INSTANCE_SHOWN_EVENT,\n    PROCESS_INSTANCE_HIDDEN_EVENT = events.PROCESS_INSTANCE_HIDDEN_EVENT;\n\nvar LOW_PRIORITY = 500;\n\nfunction ProcessInstanceSettings(animation, eventBus, processInstances, elementRegistry) {\n  var self = this;\n\n  this._animation = animation;\n  this._eventBus = eventBus;\n  this._processInstances = processInstances;\n  this._elementRegistry = elementRegistry;\n\n  this._eventBus.on(PROCESS_INSTANCE_CREATED_EVENT, LOW_PRIORITY, function(context) {\n    var parent = context.parent,\n        processInstanceId = context.processInstanceId;\n\n    var processInstancesWithParent = processInstances.getProcessInstances(parent).filter(function(processInstance) {\n      return !processInstance.isFinished;\n    });\n\n    if (processInstancesWithParent.length === 1) {\n      self.showProcessInstance(processInstanceId, parent);\n    } else if (processInstancesWithParent.length > 1) {\n      self.hideProcessInstance(processInstanceId);\n    }\n  });\n\n  this._eventBus.on(PROCESS_INSTANCE_FINISHED_EVENT, LOW_PRIORITY, function(context) {\n    var parent = context.parent,\n        processInstanceId = context.processInstanceId;\n\n    var processInstancesWithParent = processInstances\n      .getProcessInstances(parent)\n      .filter(function(processInstance) {\n        return processInstanceId !== processInstance.processInstanceId && !processInstance.isFinished;\n      });\n\n    // show remaining process instance\n    if (processInstancesWithParent.length\n        && processInstanceId === parent.shownProcessInstance) {\n\n      self.showProcessInstance(processInstancesWithParent[0].processInstanceId, parent);\n\n    } else {\n      delete parent.shownProcessInstance;\n    }\n\n    // outer process is finished\n    if (!parent.parent) {\n      elementRegistry.forEach(function(element) {\n        delete element.shownProcessInstance;\n      });\n    }\n  });\n\n  eventBus.on(TOGGLE_MODE_EVENT, function() {\n    elementRegistry.forEach(function(element) {\n      delete element.shownProcessInstance;\n    });\n  });\n}\n\nProcessInstanceSettings.prototype.showProcessInstance = function(processInstanceId, parent) {\n  this._animation.showProcessInstanceAnimations(processInstanceId);\n\n  parent.shownProcessInstance = processInstanceId;\n\n  this._eventBus.fire(PROCESS_INSTANCE_SHOWN_EVENT, {\n    processInstanceId: processInstanceId\n  });\n};\n\nProcessInstanceSettings.prototype.hideProcessInstance = function(processInstanceId) {\n  this._animation.hideProcessInstanceAnimations(processInstanceId);\n\n  this._eventBus.fire(PROCESS_INSTANCE_HIDDEN_EVENT, {\n    processInstanceId: processInstanceId\n  });\n};\n\nProcessInstanceSettings.prototype.showNext = function(parent) {\n  var self = this;\n\n  var processInstancesWithParent = this._processInstances.getProcessInstances(parent);\n\n  var shownProcessInstance = parent.shownProcessInstance;\n\n  var indexOfShownProcessInstance = 0;\n\n  for (let i = 0; i < processInstancesWithParent.length; i++) {\n    if (processInstancesWithParent[i].processInstanceId === shownProcessInstance) {\n      break;\n    } else {\n      indexOfShownProcessInstance++;\n    }\n  }\n\n  processInstancesWithParent.forEach(function(processInstance) {\n    self.hideProcessInstance(processInstance.processInstanceId);\n  });\n\n  if (indexOfShownProcessInstance === processInstancesWithParent.length - 1) {\n\n    // last index\n    this.showProcessInstance(processInstancesWithParent[0].processInstanceId, parent);\n  } else {\n\n    // not last index\n    this.showProcessInstance(processInstancesWithParent[indexOfShownProcessInstance + 1].processInstanceId, parent);\n  }\n};\n\nProcessInstanceSettings.$inject = [ 'animation', 'eventBus', 'processInstances', 'elementRegistry' ];\n\nmodule.exports = ProcessInstanceSettings;", "module.exports = require('./ProcessInstanceSettings');", "'use strict';\r\n\r\nvar events = require('../../util/EventHelper'),\r\n    TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT,\r\n    RESET_SIMULATION_EVENT = events.RESET_SIMULATION_EVENT,\r\n    PROCESS_INSTANCE_CREATED_EVENT = events.PROCESS_INSTANCE_CREATED_EVENT,\r\n    PROCESS_INSTANCE_FINISHED_EVENT = events.PROCESS_INSTANCE_FINISHED_EVENT;\r\n\r\nfunction ProcessInstances(eventBus, processInstanceIds) {\r\n  var self = this;\r\n\r\n  this._eventBus = eventBus;\r\n  this._processInstanceIds = processInstanceIds;\r\n\r\n  this.processInstances = [];\r\n\r\n  // clear instances\r\n  eventBus.on([ TOGGLE_MODE_EVENT, RESET_SIMULATION_EVENT ], function() {\r\n    self.processInstances = [];\r\n  });\r\n}\r\n\r\n/**\r\n * Create a new process instance.\r\n *\r\n * @param {Object} parent - Parent element which contains all child elements of process definition.\r\n * @param {string} [parentProcessInstanceId] - Optional ID of parent process instance.\r\n */\r\nProcessInstances.prototype.create = function(parent, parentProcessInstanceId) {\r\n  var processInstanceId = this._processInstanceIds.getNext();\r\n\r\n  var processInstance = {\r\n    parent: parent,\r\n    processInstanceId: processInstanceId,\r\n    parentProcessInstanceId: parentProcessInstanceId\r\n  };\r\n\r\n  this.processInstances.push(processInstance);\r\n\r\n  this._eventBus.fire(PROCESS_INSTANCE_CREATED_EVENT, processInstance);\r\n\r\n  return processInstanceId;\r\n};\r\n\r\nProcessInstances.prototype.remove = function(processInstanceId) {\r\n  this.processInstances = this.processInstances.filter(function(processInstance) {\r\n    return processInstance.processInstanceId !== processInstanceId;\r\n  });\r\n};\r\n\r\n/**\r\n * Finish a process instance.\r\n *\r\n * @param {string} processInstanceId - ID of process instance.\r\n */\r\nProcessInstances.prototype.finish = function(processInstanceId) {\r\n  var processInstance = this.processInstances.find(function(processInstance) {\r\n    return processInstance.processInstanceId === processInstanceId;\r\n  });\r\n\r\n  this._eventBus.fire(PROCESS_INSTANCE_FINISHED_EVENT, processInstance);\r\n\r\n  processInstance.isFinished = true;\r\n};\r\n\r\n/**\r\n * @param {Object} [parent] - Optional parent.\r\n * @param {Object} [options] - Optional options.\r\n * @param {boolean} [options.includeFinished] - Wether to include finished process instance.\r\n */\r\nProcessInstances.prototype.getProcessInstances = function(parent, options) {\r\n  if (!parent) {\r\n    return this.processInstances;\r\n  }\r\n\r\n  var processInstances = this.processInstances.filter(function(processInstance) {\r\n    return processInstance.parent === parent;\r\n  });\r\n\r\n  if (options && options.includeFinished !== true) {\r\n    processInstances = processInstances.filter(function(processInstance) {\r\n      return !processInstance.isFinished;\r\n    });\r\n  }\r\n\r\n  return processInstances;\r\n};\r\n\r\nProcessInstances.prototype.getProcessInstance = function(processInstanceId) {\r\n  return this.processInstances.find(function(processInstance) {\r\n    return processInstance.processInstanceId === processInstanceId;\r\n  });\r\n};\r\n\r\nProcessInstances.$inject = [ 'eventBus', 'processInstanceIds' ];\r\n\r\nmodule.exports = ProcessInstances;", "module.exports = require('./ProcessInstances');", "'use strict';\r\n\r\nvar domify = require('min-dom/lib/domify'),\r\n    domClasses = require('min-dom/lib/classes'),\r\n    domEvent = require('min-dom/lib/event');\r\n\r\nvar is = require('../../util/ElementHelper').is;\r\n\r\nvar events = require('../../util/EventHelper'),\r\n    TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT,\r\n    GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT,\r\n    RESET_SIMULATION_EVENT = events.RESET_SIMULATION_EVENT;\r\n\r\nfunction ResetSimulation(eventBus, tokenSimulationPalette, notifications, elementRegistry) {\r\n  var self = this;\r\n\r\n  this._eventBus = eventBus;\r\n  this._tokenSimulationPalette = tokenSimulationPalette;\r\n  this._notifications = notifications;\r\n  this._elementRegistry = elementRegistry;\r\n\r\n  this._init();\r\n\r\n  eventBus.on(GENERATE_TOKEN_EVENT, function(context) {\r\n    if (!is(context.element, 'bpmn:StartEvent')) {\r\n      return;\r\n    }\r\n\r\n    domClasses(self.paletteEntry).remove('disabled');\r\n  });\r\n\r\n  eventBus.on(TOGGLE_MODE_EVENT, function(context) {\r\n    var simulationModeActive = context.simulationModeActive;\r\n\r\n    if (!simulationModeActive) {\r\n      self.resetSimulation();\r\n    }\r\n  });\r\n}\r\n\r\nResetSimulation.prototype._init = function() {\r\n  var self = this;\r\n\r\n  this.paletteEntry = domify('<div class=\"entry disabled\" title=\"Reset Simulation\"><i class=\"fa fa-refresh\"></i></div>');\r\n\r\n  domEvent.bind(this.paletteEntry, 'click', function() {\r\n    self.resetSimulation();\r\n\r\n    self._notifications.showNotification('Reset Simulation', 'info');\r\n  });\r\n\r\n  this._tokenSimulationPalette.addEntry(this.paletteEntry, 2);\r\n};\r\n\r\nResetSimulation.prototype.resetSimulation = function() {\r\n  domClasses(this.paletteEntry).add('disabled');\r\n\r\n  this._elementRegistry.forEach(function(element) {\r\n    if (element.tokenCount !== undefined) {\r\n      delete element.tokenCount;\r\n    }\r\n  });\r\n\r\n  this._eventBus.fire(RESET_SIMULATION_EVENT);\r\n};\r\n\r\nResetSimulation.$inject = [ 'eventBus', 'tokenSimulationPalette', 'notifications', 'elementRegistry' ];\r\n\r\nmodule.exports = ResetSimulation;", "module.exports = require('./ResetSimulation');", "'use strict';\n\nvar domify = require('min-dom/lib/domify'),\n    domClasses = require('min-dom/lib/classes'),\n    domEvent = require('min-dom/lib/event'),\n    domQuery = require('min-dom/lib/query');\n\nvar events = require('../../util/EventHelper'),\n    TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;\n\nfunction SetAnimationSpeed(canvas, animation, eventBus) {\n  var self = this;\n\n  this._canvas = canvas;\n  this._animation = animation;\n  this._eventBus = eventBus;\n\n  this._init();\n\n  eventBus.on(TOGGLE_MODE_EVENT, function(context) {\n    var simulationModeActive = context.simulationModeActive;\n\n    if (!simulationModeActive) {\n      domClasses(self.container).add('hidden');\n    } else {\n      domClasses(self.container).remove('hidden');\n    }\n  });\n}\n\nSetAnimationSpeed.prototype._init = function() {\n  var self = this;\n\n  this.container = domify(\n    '<div class=\"set-animation-speed hidden\">' +\n      '<i title=\"Set Animation Speed\" class=\"fa fa-tachometer\" aria-hidden=\"true\"></i>' +\n      '<div class=\"animation-speed-buttons\">' +\n        '<div title=\"Slow\" id=\"animation-speed-1\" class=\"animation-speed-button\"><i class=\"fa fa-angle-right\" aria-hidden=\"true\"></i></div>' +\n        '<div title=\"Normal\" id=\"animation-speed-2\" class=\"animation-speed-button active\"><i class=\"fa fa-angle-right\" aria-hidden=\"true\"></i><i class=\"fa fa-angle-right\" aria-hidden=\"true\"></i></div>' +\n        '<div title=\"Fast\" id=\"animation-speed-3\" class=\"animation-speed-button\"><i class=\"fa fa-angle-right\" aria-hidden=\"true\"></i><i class=\"fa fa-angle-right\" aria-hidden=\"true\"></i><i class=\"fa fa-angle-right\" aria-hidden=\"true\"></i></div>' +\n      '</div>' +\n    '</div>'\n  );\n\n  var speed1 = domQuery('#animation-speed-1', this.container),\n      speed2 = domQuery('#animation-speed-2', this.container),\n      speed3 = domQuery('#animation-speed-3', this.container);\n\n  domEvent.bind(speed1, 'click', function() {\n    self.setActive(speed1);\n\n    self._animation.setAnimationSpeed(0.5);\n  });\n\n  domEvent.bind(speed2, 'click', function() {\n    self.setActive(speed2);\n\n    self._animation.setAnimationSpeed(1);\n  });\n\n  domEvent.bind(speed3, 'click', function() {\n    self.setActive(speed3);\n\n    self._animation.setAnimationSpeed(1.5);\n  });\n\n  this._canvas.getContainer().appendChild(this.container);\n};\n\nSetAnimationSpeed.prototype.setActive = function(element) {\n  domQuery.all('.animation-speed-button', this.container).forEach(function(button) {\n    domClasses(button).remove('active');\n  });\n\n  domClasses(element).add('active');\n};\n\nSetAnimationSpeed.$inject = [ 'canvas', 'animation', 'eventBus' ];\n\nmodule.exports = SetAnimationSpeed;", "module.exports = require('./SetAnimationSpeed');", "module.exports = function(el) {\n\n  var c;\n\n  while (el.childNodes.length) {\n    c = el.childNodes[0];\n    el.removeChild(c);\n  }\n\n  return el;\n};", "'use strict';\n\nvar domify = require('min-dom/lib/domify'),\n    domClasses = require('min-dom/lib/classes'),\n    domEvent = require('min-dom/lib/event'),\n    domQuery = require('min-dom/lib/query'),\n    domClear = require('min-dom/lib/clear');\n\nvar events = require('../../util/EventHelper'),\n    TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT,\n    PROCESS_INSTANCE_CREATED_EVENT = events.PROCESS_INSTANCE_CREATED_EVENT,\n    PROCESS_INSTANCE_FINISHED_EVENT = events.PROCESS_INSTANCE_FINISHED_EVENT,\n    PROCESS_INSTANCE_SHOWN_EVENT = events.PROCESS_INSTANCE_SHOWN_EVENT,\n    PROCESS_INSTANCE_HIDDEN_EVENT = events.PROCESS_INSTANCE_HIDDEN_EVENT,\n    RESET_SIMULATION_EVENT = events.RESET_SIMULATION_EVENT;\n\nvar FILL_COLOR = getComputedStyle(document.documentElement).getPropertyValue('--token-simulation-silver-base-97'),\n    STROKE_COLOR = getComputedStyle(document.documentElement).getPropertyValue('--token-simulation-green-base-44');\n\nfunction isNull(value) {\n  return value === null;\n}\n\nfunction ShowProcessInstance(\n    eventBus,\n    canvas,\n    processInstanceSettings,\n    processInstances,\n    graphicsFactory,\n    elementRegistry\n) {\n  var self = this;\n\n  this._eventBus = eventBus;\n  this._canvas = canvas;\n  this._processInstanceSettings = processInstanceSettings;\n  this._processInstances = processInstances;\n  this._graphicsFactory = graphicsFactory;\n  this._elementRegistry = elementRegistry;\n\n  this.highlightedElement = null;\n\n  this._init();\n\n  eventBus.on(TOGGLE_MODE_EVENT, function(context) {\n    var simulationModeActive = context.simulationModeActive;\n\n    if (!simulationModeActive) {\n      domClasses(self.container).add('hidden');\n      domClear(self.container);\n\n      if (!isNull(self.highlightedElement)) {\n        self.removeHighlightFromProcess(self.highlightedElement.element);\n\n        self.highlightedElement = null;\n      }\n    } else {\n      domClasses(self.container).remove('hidden');\n    }\n  });\n\n  eventBus.on(PROCESS_INSTANCE_CREATED_EVENT, function(context) {\n    self.addInstance(context);\n  });\n\n  eventBus.on(PROCESS_INSTANCE_FINISHED_EVENT, function(context) {\n    self.removeInstance(context);\n  });\n\n  eventBus.on(PROCESS_INSTANCE_SHOWN_EVENT, function(context) {\n    self.setInstanceShown(context.processInstanceId);\n  });\n\n  eventBus.on(PROCESS_INSTANCE_HIDDEN_EVENT, function(context) {\n    self.setInstanceHidden(context.processInstanceId);\n  });\n\n  eventBus.on(RESET_SIMULATION_EVENT, function() {\n    self.removeAllInstances();\n  });\n}\n\nShowProcessInstance.prototype._init = function() {\n  this.container = domify('<div class=\"process-instances hidden\"></div>');\n\n  this._canvas.getContainer().appendChild(this.container);\n};\n\nShowProcessInstance.prototype.addInstance = function(context) {\n  var self = this;\n\n  var processInstanceId = context.processInstanceId,\n      parent = context.parent;\n\n  var element = domify(\n    '<div id=\"instance-' + processInstanceId + '\" class=\"process-instance\" title=\"View Process Instance ' + processInstanceId + '\">' +\n    processInstanceId +\n    '</div>'\n  );\n\n  domEvent.bind(element, 'click', function() {\n    var processInstancesWithParent = self._processInstances.getProcessInstances(parent);\n\n    processInstancesWithParent.forEach(function(processInstance) {\n      self._processInstanceSettings.hideProcessInstance(processInstance.processInstanceId);\n    });\n\n    self._processInstanceSettings.showProcessInstance(processInstanceId, parent);\n  });\n\n  domEvent.bind(element, 'mouseenter', function() {\n    self.highlightedElement = {\n      element: parent,\n      stroke: parent.businessObject.di.get('stroke'),\n      fill: parent.businessObject.di.get('fill')\n    };\n\n    self.addHighlightToProcess(parent);\n  });\n\n  domEvent.bind(element, 'mouseleave', function() {\n    self.removeHighlightFromProcess(parent);\n\n    self.highlightedElement = null;\n  });\n\n  this.container.appendChild(element);\n};\n\nShowProcessInstance.prototype.removeInstance = function(context) {\n  var processInstanceId = context.processInstanceId;\n\n  var element = domQuery('#instance-' + processInstanceId, this.container);\n\n  if (element) {\n    element.remove();\n  }\n};\n\nShowProcessInstance.prototype.removeAllInstances = function() {\n  this.container.innerHTML = '';\n};\n\nShowProcessInstance.prototype.setInstanceShown = function(processInstanceId) {\n  var element = domQuery('#instance-' + processInstanceId, this.container);\n\n  if (element) {\n    domClasses(element).add('active');\n  }\n};\n\nShowProcessInstance.prototype.setInstanceHidden = function(processInstanceId) {\n  var element = domQuery('#instance-' + processInstanceId, this.container);\n\n  if (element) {\n    domClasses(element).remove('active');\n  }\n};\n\nShowProcessInstance.prototype.addHighlightToProcess = function(element) {\n  this.setColor(element, STROKE_COLOR, FILL_COLOR);\n\n  if (!element.parent) {\n    domClasses(this._canvas.getContainer()).add('highlight');\n  }\n};\n\nShowProcessInstance.prototype.removeHighlightFromProcess = function(element) {\n  if (isNull(this.highlightedElement)) {\n    return;\n  }\n\n  this.setColor(element, this.highlightedElement.stroke, this.highlightedElement.fill);\n\n  if (!element.parent) {\n    domClasses(this._canvas.getContainer()).remove('highlight');\n  }\n};\n\nShowProcessInstance.prototype.setColor = function(element, stroke, fill) {\n  var businessObject = element.businessObject;\n\n  businessObject.di.set('stroke', stroke);\n  businessObject.di.set('fill', fill);\n\n  var gfx = this._elementRegistry.getGraphics(element);\n\n  this._graphicsFactory.update('connection', element, gfx);\n};\n\nShowProcessInstance.$inject = [\n  'eventBus',\n  'canvas',\n  'processInstanceSettings',\n  'processInstances',\n  'graphicsFactory',\n  'elementRegistry'\n];\n\nmodule.exports = ShowProcessInstance;", "module.exports = require('./ShowProcessInstance');", "'use strict';\r\n\r\nvar elementHelper = require('../../util/ElementHelper'),\r\n    getBusinessObject = elementHelper.getBusinessObject,\r\n    is = elementHelper.is,\r\n    isAncestor = elementHelper.isAncestor,\r\n    isTypedEvent = elementHelper.isTypedEvent;\r\n\r\nvar events = require('../../util/EventHelper'),\r\n    CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT;\r\n\r\nvar VERY_LOW_PRIORITY = 250;\r\n\r\nfunction SimulationState(\r\n    eventBus,\r\n    animation,\r\n    elementRegistry,\r\n    log,\r\n    elementNotifications,\r\n    canvas,\r\n    processInstances\r\n) {\r\n  // var self = this;\r\n\r\n  this._animation = animation;\r\n  this._elementRegistry = elementRegistry;\r\n  this._log = log;\r\n  this._elementNotifications = elementNotifications;\r\n  this._canvas = canvas;\r\n  this._processInstances = processInstances;\r\n\r\n  eventBus.on(CONSUME_TOKEN_EVENT, VERY_LOW_PRIORITY, function() {\r\n    // self.isDeadlock();\r\n  });\r\n}\r\n\r\n// TODO: refactor\r\nSimulationState.prototype.isDeadlock = function() {\r\n  var self = this;\r\n\r\n  var hasTokens = [];\r\n\r\n  this._elementRegistry.forEach(function(element) {\r\n    if (element.tokenCount) {\r\n      hasTokens.push(element);\r\n    }\r\n  });\r\n\r\n  var cannotContinue = [];\r\n  var hasTerminate = [];\r\n\r\n  hasTokens.forEach(function(element) {\r\n    var outgoingSequenceFlows = element.outgoing.filter(function(outgoing) {\r\n      return is(outgoing, 'bpmn:SequenceFlow');\r\n    });\r\n\r\n    // has tokens but no outgoing sequence flows\r\n    if (!outgoingSequenceFlows.length) {\r\n      cannotContinue.push(element);\r\n    }\r\n\r\n    // parallel gateway after exclusive gateway\r\n    if (is(element, 'bpmn:ParallelGateway')) {\r\n      var incomingSequenceFlows = element.incoming.filter(function(incoming) {\r\n        return is(incoming, 'bpmn:SequenceFlow');\r\n      });\r\n\r\n      if (incomingSequenceFlows.length > element.tokenCount) {\r\n        cannotContinue.push(element);\r\n      }\r\n    }\r\n\r\n    var visited = [];\r\n\r\n    // has terminate event\r\n    function checkIfHasTerminate(element) {\r\n      element.outgoing.forEach(function(outgoing) {\r\n        if (visited.indexOf(outgoing.target) !== -1) {\r\n          return;\r\n        }\r\n\r\n        visited.push(outgoing.target);\r\n\r\n        var isTerminate = isTypedEvent(getBusinessObject(outgoing.target), 'bpmn:TerminateEventDefinition');\r\n\r\n        if (isTerminate) {\r\n          hasTerminate.push(element);\r\n        }\r\n\r\n        checkIfHasTerminate(outgoing.target);\r\n      });\r\n    }\r\n\r\n    checkIfHasTerminate(element);\r\n  });\r\n\r\n  if (hasTokens.length\r\n      && !hasTerminate.length\r\n      && cannotContinue.length\r\n      && !this._animation.animations.length) {\r\n    self._log.log('Deadlock', 'warning', 'fa-exclamation-triangle');\r\n\r\n    cannotContinue.forEach(function(element) {\r\n      self._elementNotifications.addElementNotification(element, {\r\n        type: 'warning',\r\n        icon: 'fa-exclamation-triangle',\r\n        text: 'Deadlock'\r\n      });\r\n    });\r\n  }\r\n};\r\n\r\n/**\r\n * Check if process instance finished.\r\n * Element is necessary to display element notification if finished.\r\n */\r\nSimulationState.prototype.isFinished = function(element, processInstanceId) {\r\n  var processInstance = this._processInstances.getProcessInstance(processInstanceId);\r\n  var parent = processInstance.parent;\r\n\r\n  var hasTokens = false;\r\n\r\n  if (!parent) {\r\n    parent = this._canvas.getRootElement();\r\n  }\r\n\r\n  parent.children.forEach(function(element) {\r\n    if (element.tokenCount &&\r\n        element.tokenCount[processInstanceId] &&\r\n        element.tokenCount[processInstanceId].length\r\n    ) {\r\n      hasTokens = true;\r\n    }\r\n  });\r\n\r\n  var hasAnimations = false;\r\n\r\n  this._animation.animations.forEach(function(animation) {\r\n    if (isAncestor(parent, animation.element) &&\r\n        animation.processInstanceId === processInstanceId) {\r\n      hasAnimations = true;\r\n    }\r\n  });\r\n\r\n  if (!hasTokens && !hasAnimations) {\r\n    if (is(parent, 'bpmn:SubProcess')) {\r\n      this._log.log('Subprocess ' + processInstanceId + ' finished', 'info', 'fa-check-circle');\r\n    } else {\r\n      this._log.log('Process ' + processInstanceId + ' finished', 'success', 'fa-check-circle');\r\n\r\n      this._elementNotifications.addElementNotification(element, {\r\n        type: 'success',\r\n        icon: 'fa-check-circle',\r\n        text: 'Finished'\r\n      });\r\n    }\r\n\r\n    return true;\r\n  }\r\n};\r\n\r\nSimulationState.$inject = [\r\n  'eventBus',\r\n  'animation',\r\n  'elementRegistry',\r\n  'log',\r\n  'elementNotifications',\r\n  'canvas',\r\n  'processInstances'\r\n];\r\n\r\nmodule.exports = SimulationState;\r\n", "module.exports = require('./SimulationState');", "'use strict';\r\n\r\nvar domify = require('min-dom/lib/domify'),\r\n    domClasses = require('min-dom/lib/classes'),\r\n    domEvent = require('min-dom/lib/event'),\r\n    domQuery = require('min-dom/lib/query');\r\n\r\nvar events = require('../../../util/EventHelper'),\r\n    TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;\r\n\r\nfunction ToggleMode(eventBus, canvas, selection, contextPad) {\r\n  var self = this;\r\n\r\n  this._eventBus = eventBus;\r\n  this._canvas = canvas;\r\n  this._selection = selection;\r\n  this._contextPad = contextPad;\r\n\r\n  this.simulationModeActive = false;\r\n\r\n  eventBus.on('import.done', function() {\r\n    self.canvasParent = self._canvas.getContainer().parentNode;\r\n    self.palette = domQuery('.djs-palette', self._canvas.getContainer());\r\n\r\n    self._init();\r\n  });\r\n}\r\n\r\nToggleMode.prototype._init = function() {\r\n  this.container = domify(`\r\n    <div class=\"toggle-mode\">\r\n      Token Simulation <span class=\"toggle\"><i class=\"fa fa-toggle-off\"></i></span>\r\n    </div>\r\n  `);\r\n\r\n  domEvent.bind(this.container, 'click', this.toggleMode.bind(this));\r\n\r\n  this._canvas.getContainer().appendChild(this.container);\r\n};\r\n\r\nToggleMode.prototype.toggleMode = function() {\r\n  if (this.simulationModeActive) {\r\n    this.container.innerHTML = 'Token Simulation <span class=\"toggle\"><i class=\"fa fa-toggle-off\"></i></span>';\r\n\r\n    domClasses(this.canvasParent).remove('simulation');\r\n    domClasses(this.palette).remove('hidden');\r\n\r\n    this._eventBus.fire(TOGGLE_MODE_EVENT, {\r\n      simulationModeActive: false\r\n    });\r\n\r\n    var elements = this._selection.get();\r\n\r\n    if (elements.length === 1) {\r\n      this._contextPad.open(elements[0]);\r\n    }\r\n  } else {\r\n    this.container.innerHTML = 'Token Simulation <span class=\"toggle\"><i class=\"fa fa-toggle-on\"></i></span>';\r\n\r\n    domClasses(this.canvasParent).add('simulation');\r\n    domClasses(this.palette).add('hidden');\r\n\r\n    this._eventBus.fire(TOGGLE_MODE_EVENT, {\r\n      simulationModeActive: true\r\n    });\r\n  }\r\n\r\n  this.simulationModeActive = !this.simulationModeActive;\r\n};\r\n\r\nToggleMode.$inject = [ 'eventBus', 'canvas', 'selection', 'contextPad' ];\r\n\r\nmodule.exports = ToggleMode;", "module.exports = require('./ToggleMode.js');", "'use strict';\r\n\r\nvar domify = require('min-dom/lib/domify');\r\n\r\nvar elementHelper = require('../../util/ElementHelper'),\r\n    isAncestor = elementHelper.isAncestor;\r\n\r\nvar events = require('../../util/EventHelper'),\r\n    TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT,\r\n    GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT,\r\n    CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT,\r\n    RESET_SIMULATION_EVENT = events.RESET_SIMULATION_EVENT,\r\n    TERMINATE_EVENT = events.TERMINATE_EVENT,\r\n    PROCESS_INSTANCE_SHOWN_EVENT = events.PROCESS_INSTANCE_SHOWN_EVENT;\r\n\r\nvar OFFSET_BOTTOM = 10,\r\n    OFFSET_LEFT = -15;\r\n\r\nvar LOW_PRIORITY = 500;\r\n\r\nfunction TokenCount(eventBus, overlays, elementRegistry, canvas, processInstances) {\r\n  var self = this;\r\n\r\n  this._overlays = overlays;\r\n  this._elementRegistry = elementRegistry;\r\n  this._canvas = canvas;\r\n  this._processInstances = processInstances;\r\n\r\n  this.overlayIds = {};\r\n\r\n  eventBus.on(TOGGLE_MODE_EVENT, function(context) {\r\n    var simulationModeActive = context.simulationModeActive;\r\n\r\n    if (!simulationModeActive) {\r\n      self.removeTokenCounts();\r\n    }\r\n  });\r\n\r\n  eventBus.on(RESET_SIMULATION_EVENT, function() {\r\n    self.removeTokenCounts();\r\n  });\r\n\r\n  eventBus.on(TERMINATE_EVENT, function(context) {\r\n    var element = context.element,\r\n        parent = element.parent;\r\n\r\n    self.removeTokenCounts(parent);\r\n  });\r\n\r\n  eventBus.on([ GENERATE_TOKEN_EVENT, CONSUME_TOKEN_EVENT ], LOW_PRIORITY, function(context) {\r\n    var element = context.element,\r\n        parent = element.parent;\r\n\r\n    self.removeTokenCounts(parent);\r\n    self.addTokenCounts(parent);\r\n  });\r\n\r\n  eventBus.on(PROCESS_INSTANCE_SHOWN_EVENT, function(context) {\r\n    var processInstanceId = context.processInstanceId;\r\n\r\n    var processInstance = processInstances.getProcessInstance(processInstanceId),\r\n        parent = processInstance.parent;\r\n\r\n    self.removeTokenCounts(parent);\r\n    self.addTokenCounts(parent);\r\n  });\r\n}\r\n\r\nTokenCount.prototype.addTokenCounts = function(parent) {\r\n  var self = this;\r\n\r\n  if (!parent) {\r\n    parent = this._canvas.getRootElement();\r\n  }\r\n\r\n  var shownProcessInstance = parent.shownProcessInstance;\r\n\r\n  // choose default\r\n  if (!shownProcessInstance) {\r\n    var processInstancesWithParent = this._processInstances.getProcessInstances(parent);\r\n\r\n    // no instance\r\n    if (!processInstancesWithParent.length) {\r\n      return;\r\n    }\r\n\r\n    shownProcessInstance = processInstancesWithParent[0].processInstanceId;\r\n  }\r\n\r\n  this._elementRegistry.forEach(function(element) {\r\n    if (isAncestor(parent, element)) {\r\n      self.addTokenCount(element, shownProcessInstance);\r\n    }\r\n  });\r\n};\r\n\r\nTokenCount.prototype.addTokenCount = function(element, shownProcessInstance) {\r\n  var tokenCount = element.tokenCount && element.tokenCount[shownProcessInstance];\r\n\r\n  if (!tokenCount) {\r\n    return;\r\n  }\r\n\r\n  var html = this.createTokenCount(tokenCount);\r\n\r\n  var position = { bottom: OFFSET_BOTTOM, left: OFFSET_LEFT };\r\n\r\n  var overlayId = this._overlays.add(element, 'token-count', {\r\n    position: position,\r\n    html: html,\r\n    show: {\r\n      minZoom: 0.5\r\n    }\r\n  });\r\n\r\n  this.overlayIds[element.id] = overlayId;\r\n};\r\n\r\nTokenCount.prototype.createTokenCount = function(tokenCount) {\r\n  return domify('<div class=\"token-count waiting\">' + tokenCount + '</div>');\r\n};\r\n\r\nTokenCount.prototype.removeTokenCounts = function(parent) {\r\n  var self = this;\r\n\r\n  if (!parent) {\r\n    parent = this._canvas.getRootElement();\r\n  }\r\n\r\n  this._elementRegistry.forEach(function(element) {\r\n    if (isAncestor(parent, element)) {\r\n      self.removeTokenCount(element);\r\n    }\r\n  });\r\n};\r\n\r\nTokenCount.prototype.removeTokenCount = function(element) {\r\n  var overlayId = this.overlayIds[element.id];\r\n\r\n  if (!overlayId) {\r\n    return;\r\n  }\r\n\r\n  this._overlays.remove(overlayId);\r\n\r\n  delete this.overlayIds[element.id];\r\n};\r\n\r\nTokenCount.$inject = [ 'eventBus', 'overlays', 'elementRegistry', 'canvas', 'processInstances' ];\r\n\r\nmodule.exports = TokenCount;", "module.exports = require('./TokenCount');", "'use strict';\r\n\r\nvar elementHelper = require('../../../util/ElementHelper'),\r\n    getBusinessObject = elementHelper.getBusinessObject,\r\n    is = elementHelper.is,\r\n    isAncestor = elementHelper.isAncestor,\r\n    getDescendants = elementHelper.getDescendants,\r\n    isTypedEvent = elementHelper.isTypedEvent;\r\n\r\nvar events = require('../../../util/EventHelper'),\r\n    GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT,\r\n    TERMINATE_EVENT = events.TERMINATE_EVENT,\r\n    UPDATE_ELEMENTS_EVENT = events.UPDATE_ELEMENTS_EVENT;\r\n\r\nfunction EndEventHandler(animation, eventBus, log, simulationState, elementRegistry, processInstances) {\r\n  this._animation = animation;\r\n  this._eventBus = eventBus;\r\n  this._log = log;\r\n  this._simulationState = simulationState;\r\n  this._elementRegistry = elementRegistry;\r\n  this._processInstances = processInstances;\r\n}\r\n\r\nEndEventHandler.prototype.consume = function(context) {\r\n  var element = context.element,\r\n      processInstanceId = context.processInstanceId;\r\n\r\n  var isTerminate = isTypedEvent(getBusinessObject(element), 'bpmn:TerminateEventDefinition'),\r\n      isSubProcessChild = is(element.parent, 'bpmn:SubProcess');\r\n\r\n  if (isTerminate) {\r\n    this._eventBus.fire(TERMINATE_EVENT, context);\r\n\r\n    this._elementRegistry.forEach(function(e) {\r\n      if (isAncestor(element.parent, e) &&\r\n          e.tokenCount &&\r\n          e.tokenCount[processInstanceId]) {\r\n        delete e.tokenCount[processInstanceId];\r\n      }\r\n    });\r\n\r\n    // finish but do NOT remove\r\n    this._processInstances.finish(processInstanceId);\r\n  }\r\n\r\n  var isFinished = this._simulationState.isFinished(element, processInstanceId);\r\n\r\n  if (isFinished) {\r\n\r\n    // finish but do NOT remove\r\n    this._processInstances.finish(processInstanceId);\r\n  }\r\n\r\n  if ((isFinished || isTerminate) && isSubProcessChild) {\r\n    var processInstance = this._processInstances.getProcessInstance(processInstanceId);\r\n\r\n    // generate token on parent\r\n    this._eventBus.fire(GENERATE_TOKEN_EVENT, {\r\n      element: element.parent,\r\n      processInstanceId: processInstance.parentProcessInstanceId\r\n    });\r\n  }\r\n\r\n  this._eventBus.fire(UPDATE_ELEMENTS_EVENT, {\r\n    elements: getDescendants(this._elementRegistry.getAll(), element.parent)\r\n  });\r\n};\r\n\r\n/**\r\n * End event never generates.\r\n */\r\nEndEventHandler.prototype.generate = function(context) {};\r\n\r\nEndEventHandler.$inject = [ 'animation', 'eventBus', 'log', 'simulationState', 'elementRegistry', 'processInstances' ];\r\n\r\nmodule.exports = EndEventHandler;", "'use strict';\r\n\r\nvar is = require('../../../util/ElementHelper').is;\r\n\r\nvar events = require('../../../util/EventHelper'),\r\n    UPDATE_ELEMENTS_EVENT = events.UPDATE_ELEMENTS_EVENT;\r\n\r\nfunction ExclusiveGatewayHandler(eventBus, animation) {\r\n  this._eventBus = eventBus;\r\n  this._animation = animation;\r\n}\r\n\r\nExclusiveGatewayHandler.prototype.consume = function(context) {\r\n  var element = context.element,\r\n      processInstanceId = context.processInstanceId;\r\n\r\n  if (!element.tokenCount) {\r\n    element.tokenCount = {};\r\n  }\r\n\r\n  if (!element.tokenCount[processInstanceId]) {\r\n    element.tokenCount[processInstanceId] = 0;\r\n  }\r\n\r\n  element.tokenCount[processInstanceId]++;\r\n\r\n  var outgoing = element.outgoing,\r\n      events = [];\r\n\r\n  outgoing.forEach(function(outgoing) {\r\n    var target = outgoing.target;\r\n\r\n    if (is(target, 'bpmn:IntermediateCatchEvent')) {\r\n      events.push(target);\r\n    }\r\n  });\r\n\r\n  this._eventBus.fire(UPDATE_ELEMENTS_EVENT, {\r\n    elements: events\r\n  });\r\n};\r\n\r\nExclusiveGatewayHandler.prototype.generate = function() {};\r\n\r\nExclusiveGatewayHandler.$inject = [ 'eventBus', 'animation' ];\r\n\r\nmodule.exports = ExclusiveGatewayHandler;", "'use strict';\r\n\r\nvar events = require('../../../util/EventHelper'),\r\n    CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT,\r\n    GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT;\r\n\r\nfunction ExclusiveGatewayHandler(eventBus, animation, elementRegistry) {\r\n  this._eventBus = eventBus;\r\n  this._animation = animation;\r\n  this._elementRegistry = elementRegistry;\r\n}\r\n\r\nExclusiveGatewayHandler.prototype.consume = function(context) {\r\n  var element = context.element;\r\n\r\n  if (!element.sequenceFlow) {\r\n    throw new Error('no sequence flow configured for element ' + element.id);\r\n  }\r\n\r\n  this._eventBus.fire(GENERATE_TOKEN_EVENT, context);\r\n};\r\n\r\nExclusiveGatewayHandler.prototype.generate = function(context) {\r\n  var element = context.element,\r\n      processInstanceId = context.processInstanceId;\r\n\r\n  if (!element.sequenceFlow) {\r\n    throw new Error('no sequence flow configured for element ' + element.id);\r\n  }\r\n\r\n  var self = this;\r\n\r\n  // property could be changed during animation\r\n  // therefore element.sequenceFlow can't be used\r\n  var sequenceFlow = this._elementRegistry.get(element.sequenceFlow.id);\r\n\r\n  this._animation.createAnimation(sequenceFlow, processInstanceId, function() {\r\n    self._eventBus.fire(CONSUME_TOKEN_EVENT, {\r\n      element: sequenceFlow.target,\r\n      processInstanceId: processInstanceId\r\n    });\r\n  });\r\n};\r\n\r\nExclusiveGatewayHandler.$inject = [ 'eventBus', 'animation', 'elementRegistry' ];\r\n\r\nmodule.exports = ExclusiveGatewayHandler;", "'use strict';\r\n\r\nvar elementHelper = require('../../../util/ElementHelper'),\r\n    is = elementHelper.is;\r\n\r\nvar events = require('../../../util/EventHelper'),\r\n    CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT,\r\n    UPDATE_ELEMENT_EVENT = events.UPDATE_ELEMENT_EVENT,\r\n    UPDATE_ELEMENTS_EVENT = events.UPDATE_ELEMENTS_EVENT;\r\n\r\nfunction IntermediateCatchEventHandler(animation, eventBus, elementRegistry) {\r\n  this._animation = animation;\r\n  this._eventBus = eventBus;\r\n  this._elementRegistry = elementRegistry;\r\n}\r\n\r\nIntermediateCatchEventHandler.prototype.consume = function(context) {\r\n  var element = context.element,\r\n      processInstanceId = context.processInstanceId;\r\n\r\n  if (!element.tokenCount) {\r\n    element.tokenCount = {};\r\n  }\r\n\r\n  if (!element.tokenCount[processInstanceId]) {\r\n    element.tokenCount[processInstanceId] = 0;\r\n  }\r\n\r\n  element.tokenCount[processInstanceId]++;\r\n\r\n  this._eventBus.fire(UPDATE_ELEMENT_EVENT, {\r\n    element: element\r\n  });\r\n};\r\n\r\nIntermediateCatchEventHandler.prototype.generate = function(context) {\r\n  var self = this;\r\n\r\n  var element = context.element,\r\n      processInstanceId = context.processInstanceId;\r\n\r\n  var outgoingSequenceFlows = element.outgoing.filter(function(outgoing) {\r\n    return is(outgoing, 'bpmn:SequenceFlow');\r\n  });\r\n\r\n  outgoingSequenceFlows.forEach(function(connection) {\r\n    self._animation.createAnimation(connection, processInstanceId, function() {\r\n      self._eventBus.fire(CONSUME_TOKEN_EVENT, {\r\n        element: connection.target,\r\n        processInstanceId: processInstanceId\r\n      });\r\n    });\r\n  });\r\n\r\n  var parent = element.parent;\r\n\r\n  var events = this._elementRegistry.filter(function(element) {\r\n    return is(element, 'bpmn:IntermediateCatchEvent') &&\r\n           element.parent === parent;\r\n  });\r\n\r\n  this._eventBus.fire(UPDATE_ELEMENTS_EVENT, {\r\n    elements: events\r\n  });\r\n};\r\n\r\nIntermediateCatchEventHandler.$inject = [ 'animation', 'eventBus', 'elementRegistry' ];\r\n\r\nmodule.exports = IntermediateCatchEventHandler;", "'use strict';\r\n\r\nvar is = require('../../../util/ElementHelper').is;\r\n\r\nvar events = require('../../../util/EventHelper'),\r\n    CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT,\r\n    GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT;\r\n\r\nfunction IntermediateThrowEventHandler(animation, eventBus) {\r\n  this._animation = animation;\r\n  this._eventBus = eventBus;\r\n}\r\n\r\nIntermediateThrowEventHandler.prototype.consume = function(element) {\r\n  this._eventBus.fire(GENERATE_TOKEN_EVENT, {\r\n    element: element\r\n  });\r\n};\r\n\r\nIntermediateThrowEventHandler.prototype.generate = function(element) {\r\n  var self = this;\r\n\r\n  var outgoingSequenceFlows = element.outgoing.filter(function(outgoing) {\r\n    return is(outgoing, 'bpmn:SequenceFlow');\r\n  });\r\n\r\n  outgoingSequenceFlows.forEach(function(connection) {\r\n    self._animation.createAnimation(connection, function() {\r\n      self._eventBus.fire(CONSUME_TOKEN_EVENT, {\r\n        element: connection.target\r\n      });\r\n    });\r\n  });\r\n};\r\n\r\nIntermediateThrowEventHandler.$inject = [ 'animation', 'eventBus' ];\r\n\r\nmodule.exports = IntermediateThrowEventHandler;", "'use strict';\r\n\r\nvar is = require('../../../util/ElementHelper').is;\r\n\r\nvar events = require('../../../util/EventHelper'),\r\n    CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT,\r\n    GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT;\r\n\r\nfunction ParallelGatewayHandler(animation, eventBus) {\r\n  this._animation = animation;\r\n  this._eventBus = eventBus;\r\n}\r\n\r\nParallelGatewayHandler.prototype.consume = function(context) {\r\n  var element = context.element,\r\n      processInstanceId = context.processInstanceId;\r\n\r\n  if (!element.tokenCount) {\r\n    element.tokenCount = {};\r\n  }\r\n\r\n  if (!element.tokenCount[processInstanceId]) {\r\n    element.tokenCount[processInstanceId] = 0;\r\n  }\r\n\r\n  element.tokenCount[processInstanceId]++;\r\n\r\n  var incoming = element.incoming;\r\n\r\n  if (incoming.length === element.tokenCount[processInstanceId]) {\r\n    this._eventBus.fire(GENERATE_TOKEN_EVENT, context);\r\n\r\n    element.tokenCount[processInstanceId] = 0;\r\n  }\r\n};\r\n\r\nParallelGatewayHandler.prototype.generate = function(context) {\r\n  var self = this;\r\n\r\n  var element = context.element,\r\n      processInstanceId = context.processInstanceId;\r\n\r\n  var outgoingSequenceFlows = element.outgoing.filter(function(outgoing) {\r\n    return is(outgoing, 'bpmn:SequenceFlow');\r\n  });\r\n\r\n  outgoingSequenceFlows.forEach(function(outgoing) {\r\n    self._animation.createAnimation(outgoing, processInstanceId, function() {\r\n      self._eventBus.fire(CONSUME_TOKEN_EVENT, {\r\n        element: outgoing.target,\r\n        processInstanceId: processInstanceId\r\n      });\r\n    });\r\n  });\r\n};\r\n\r\nParallelGatewayHandler.$inject = [ 'animation', 'eventBus' ];\r\n\r\nmodule.exports = ParallelGatewayHandler;", "'use strict';\r\n\r\nvar is = require('../../../util/ElementHelper').is;\r\n\r\nvar events = require('../../../util/EventHelper'),\r\n    CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT,\r\n    UPDATE_ELEMENTS_EVENT = events.UPDATE_ELEMENTS_EVENT;\r\n\r\nfunction StartEventHandler(animation, eventBus, elementRegistry, processInstances) {\r\n  this._animation = animation;\r\n  this._eventBus = eventBus;\r\n  this._elementRegistry = elementRegistry;\r\n  this._processInstances = processInstances;\r\n}\r\n\r\n/**\r\n * Start event has no incoming sequence flows.\r\n * Therefore it can never consume.\r\n */\r\nStartEventHandler.prototype.consume = function() {};\r\n\r\n/**\r\n * Generate tokens for start event that was either\r\n * invoked by user or a parent process.\r\n *\r\n * @param {Object} context - The context.\r\n * @param {Object} context.element - The element.\r\n * @param {string} [context.parentProcessInstanceId] - Optional ID of parent process when invoked by parent process.\r\n *\r\n */\r\nStartEventHandler.prototype.generate = function(context) {\r\n  var self = this;\r\n\r\n  var element = context.element,\r\n      parentProcessInstanceId = context.parentProcessInstanceId;\r\n\r\n  var outgoingSequenceFlows = element.outgoing.filter(function(outgoing) {\r\n    return is(outgoing, 'bpmn:SequenceFlow');\r\n  });\r\n\r\n  // create new process instance\r\n  var parent = element.parent,\r\n      processInstanceId = this._processInstances.create(parent, parentProcessInstanceId);\r\n\r\n  outgoingSequenceFlows.forEach(function(connection) {\r\n    self._animation.createAnimation(connection, processInstanceId, function() {\r\n      self._eventBus.fire(CONSUME_TOKEN_EVENT, {\r\n        element: connection.target,\r\n        processInstanceId: processInstanceId\r\n      });\r\n    });\r\n  });\r\n\r\n  if (is(element.parent, 'bpmn:SubProcess')) {\r\n    return;\r\n  }\r\n\r\n  var startEvents = this._elementRegistry.filter(function(element) {\r\n    return is(element, 'bpmn:StartEvent');\r\n  });\r\n\r\n  this._eventBus.fire(UPDATE_ELEMENTS_EVENT, {\r\n    elements: startEvents\r\n  });\r\n};\r\n\r\nStartEventHandler.$inject = [ 'animation', 'eventBus', 'elementRegistry', 'processInstances' ];\r\n\r\nmodule.exports = StartEventHandler;", "'use strict';\r\n\r\nvar is = require('../../../util/ElementHelper').is;\r\n\r\nvar events = require('../../../util/EventHelper'),\r\n    CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT,\r\n    GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT,\r\n    UPDATE_ELEMENT_EVENT = events.UPDATE_ELEMENT_EVENT;\r\n\r\nfunction SubProcessHandler(animation, eventBus, log) {\r\n  this._animation = animation;\r\n  this._eventBus = eventBus;\r\n  this._log = log;\r\n}\r\n\r\nSubProcessHandler.prototype.consume = function(context) {\r\n  var element = context.element,\r\n      processInstanceId = context.processInstanceId;\r\n\r\n  var startEvent = element.children.filter(function(child) {\r\n    return is(child, 'bpmn:StartEvent');\r\n  })[0];\r\n\r\n  if (!startEvent) {\r\n    this._log.log('Skipping Subprocess', 'info', 'fa-angle-double-right');\r\n\r\n    // skip subprocess\r\n    this._eventBus.fire(GENERATE_TOKEN_EVENT, context);\r\n  } else {\r\n    this._log.log('Starting Subprocess', 'info', 'fa-sign-in');\r\n\r\n    // start subprocess with process instance ID as parent process instance ID\r\n    this._eventBus.fire(GENERATE_TOKEN_EVENT, {\r\n      element: startEvent,\r\n      parentProcessInstanceId: processInstanceId\r\n    });\r\n  }\r\n\r\n  this._eventBus.fire(UPDATE_ELEMENT_EVENT, {\r\n    element: element\r\n  });\r\n};\r\n\r\nSubProcessHandler.prototype.generate = function(context) {\r\n  var self = this;\r\n\r\n  var element = context.element,\r\n      processInstanceId = context.processInstanceId;\r\n\r\n  var outgoingSequenceFlows = element.outgoing.filter(function(outgoing) {\r\n    return is(outgoing, 'bpmn:SequenceFlow');\r\n  });\r\n\r\n  outgoingSequenceFlows.forEach(function(outgoing) {\r\n    self._animation.createAnimation(outgoing, processInstanceId, function() {\r\n      self._eventBus.fire(CONSUME_TOKEN_EVENT, {\r\n        element: outgoing.target,\r\n        processInstanceId: processInstanceId\r\n      });\r\n    });\r\n  });\r\n\r\n  this._eventBus.fire(UPDATE_ELEMENT_EVENT, {\r\n    element: element\r\n  });\r\n};\r\n\r\nSubProcessHandler.$inject = [ 'animation', 'eventBus', 'log' ];\r\n\r\nmodule.exports = SubProcessHandler;", "'use strict';\n\nvar elementHelper = require('../../../util/ElementHelper'),\n    is = elementHelper.is;\n\nvar events = require('../../../util/EventHelper'),\n    CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT,\n    UPDATE_ELEMENT_EVENT = events.UPDATE_ELEMENT_EVENT;\n\nfunction BoundaryEventHandler(animation, eventBus, elementRegistry) {\n  this._animation = animation;\n  this._eventBus = eventBus;\n  this._elementRegistry = elementRegistry;\n}\n\nBoundaryEventHandler.prototype.consume = function(context) {\n  var element = context.element,\n      processInstanceId = context.processInstanceId;\n\n  if (!element.tokenCount) {\n    element.tokenCount = {};\n  }\n\n  if (!element.tokenCount[processInstanceId]) {\n    element.tokenCount[processInstanceId] = 0;\n  }\n\n  element.tokenCount[processInstanceId]++;\n\n  this._eventBus.fire(UPDATE_ELEMENT_EVENT, {\n    element: element\n  });\n};\n\nBoundaryEventHandler.prototype.generate = function(context) {\n  var self = this;\n\n  var element = context.element,\n      processInstanceId = context.processInstanceId;\n\n  var outgoingSequenceFlows = element.outgoing.filter(function(outgoing) {\n    return is(outgoing, 'bpmn:SequenceFlow');\n  });\n\n  outgoingSequenceFlows.forEach(function(connection) {\n    self._animation.createAnimation(connection, processInstanceId, function() {\n      self._eventBus.fire(CONSUME_TOKEN_EVENT, {\n        element: connection.target,\n        processInstanceId: processInstanceId\n      });\n    });\n  });\n};\n\nBoundaryEventHandler.$inject = [ 'animation', 'eventBus', 'elementRegistry' ];\n\nmodule.exports = BoundaryEventHandler;", "'use strict';\r\n\r\nvar is = require('../../../util/ElementHelper').is;\r\n\r\nvar events = require('../../../util/EventHelper'),\r\n    CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT,\r\n    GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT;\r\n\r\nfunction TaskHandler(animation, eventBus) {\r\n  this._animation = animation;\r\n  this._eventBus = eventBus;\r\n}\r\n\r\nTaskHandler.prototype.consume = function(context) {\r\n\r\n  // fire to generate token on self\r\n  this._eventBus.fire(GENERATE_TOKEN_EVENT, context);\r\n};\r\n\r\nTaskHandler.prototype.generate = function(context) {\r\n  var self = this;\r\n\r\n  var element = context.element,\r\n      processInstanceId = context.processInstanceId;\r\n\r\n  var outgoingSequenceFlows = element.outgoing.filter(function(outgoing) {\r\n    return is(outgoing, 'bpmn:SequenceFlow');\r\n  });\r\n\r\n  outgoingSequenceFlows.forEach(function(outgoing) {\r\n    self._animation.createAnimation(outgoing, processInstanceId, function() {\r\n      self._eventBus.fire(CONSUME_TOKEN_EVENT, {\r\n        element: outgoing.target,\r\n        processInstanceId: processInstanceId\r\n      });\r\n    });\r\n  });\r\n};\r\n\r\nTaskHandler.$inject = [ 'animation', 'eventBus' ];\r\n\r\nmodule.exports = TaskHandler;", "'use strict';\r\n\r\nvar EndEventHandler = require('./handler/EndEventHandler'),\r\n    EventBasedGatewayHandler = require('./handler/EventBasedGatewayHandler'),\r\n    ExclusiveGatewayHandler = require('./handler/ExclusiveGatewayHandler'),\r\n    IntermediateCatchEventHandler = require('./handler/IntermediateCatchEventHandler'),\r\n    IntermediateThrowEventHandler = require('./handler/IntermediateThrowEventHandler'),\r\n    ParallelGatewayHandler = require('./handler/ParallelGatewayHandler'),\r\n    StartEventHandler = require('./handler/StartEventHandler'),\r\n    SubProcessHandler = require('./handler/SubProcessHandler'),\r\n    BoundaryEventHandler = require('./handler/BoundaryEventHandler'),\r\n    TaskHandler = require('./handler/TaskHandler');\r\n\r\nvar events = require('../../util/EventHelper'),\r\n    GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT,\r\n    CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT;\r\n\r\nfunction TokenSimulationBehavior(eventBus, animation, injector) {\r\n  var self = this;\r\n\r\n  this._injector = injector;\r\n\r\n  this.handlers = {};\r\n\r\n  this.registerHandler('bpmn:EndEvent', EndEventHandler);\r\n  this.registerHandler('bpmn:EventBasedGateway', EventBasedGatewayHandler);\r\n  this.registerHandler('bpmn:ExclusiveGateway', ExclusiveGatewayHandler);\r\n  this.registerHandler('bpmn:IntermediateCatchEvent', IntermediateCatchEventHandler);\r\n  this.registerHandler('bpmn:IntermediateThrowEvent', IntermediateThrowEventHandler);\r\n  this.registerHandler('bpmn:ParallelGateway', ParallelGatewayHandler);\r\n  this.registerHandler('bpmn:StartEvent', StartEventHandler);\r\n  this.registerHandler('bpmn:SubProcess', SubProcessHandler);\r\n  this.registerHandler('bpmn:BoundaryEvent', BoundaryEventHandler);\r\n  this.registerHandler([\r\n    'bpmn:BusinessRuleTask',\r\n    'bpmn:CallActivity',\r\n    'bpmn:ManualTask',\r\n    'bpmn:ScriptTask',\r\n    'bpmn:ServiceTask',\r\n    'bpmn:Task',\r\n    'bpmn:UserTask'\r\n  ], TaskHandler);\r\n\r\n  // create animations on generate token\r\n  eventBus.on(GENERATE_TOKEN_EVENT, function(context) {\r\n    var element = context.element;\r\n\r\n    if (!self.handlers[element.type]) {\r\n      throw new Error('no handler for type ' + element.type);\r\n    }\r\n\r\n    self.handlers[element.type].generate(context);\r\n  });\r\n\r\n  // call handler on consume token\r\n  eventBus.on(CONSUME_TOKEN_EVENT, function(context) {\r\n    var element = context.element;\r\n\r\n    if (!self.handlers[element.type]) {\r\n      throw new Error('no handler for type ' + element.type);\r\n    }\r\n\r\n    self.handlers[element.type].consume(context);\r\n  });\r\n}\r\n\r\nTokenSimulationBehavior.prototype.registerHandler = function(types, handlerCls) {\r\n  var self = this;\r\n\r\n  var handler = this._injector.instantiate(handlerCls);\r\n\r\n  if (!Array.isArray(types)) {\r\n    types = [ types ];\r\n  }\r\n\r\n  types.forEach(function(type) {\r\n    self.handlers[type] = handler;\r\n  });\r\n};\r\n\r\nTokenSimulationBehavior.$inject = [ 'eventBus', 'animation', 'injector' ];\r\n\r\nmodule.exports = TokenSimulationBehavior;", "module.exports = require('./TokenSimulationBehavior');", "'use strict';\n\nfunction EditorActions(\n    eventBus,\n    toggleMode,\n    pauseSimulation,\n    log,\n    resetSimulation,\n    editorActions\n) {\n  editorActions.register({\n    toggleTokenSimulation: function() {\n      toggleMode.toggleMode();\n    }\n  });\n\n  editorActions.register({\n    togglePauseTokenSimulation: function() {\n      pauseSimulation.toggle();\n    }\n  });\n\n  editorActions.register({\n    resetTokenSimulation: function() {\n      resetSimulation.resetSimulation();\n    }\n  });\n\n  editorActions.register({\n    toggleTokenSimulationLog: function() {\n      log.toggle();\n    }\n  });\n}\n\nEditorActions.$inject = [\n  'eventBus',\n  'toggleMode',\n  'pauseSimulation',\n  'log',\n  'resetSimulation',\n  'editorActions'\n];\n\nmodule.exports = EditorActions;", "module.exports = require('./EditorActions');", "'use strict';\n\nvar events = require('../../util/EventHelper'),\n    TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT,\n    VERY_HIGH_PRIORITY = 10000;\n\n\nfunction KeyboardBindings(eventBus, injector) {\n\n  var editorActions = injector.get('editorActions', false),\n      keyboard = injector.get('keyboard', false);\n\n  if (!keyboard || !editorActions) {\n    return;\n  }\n\n\n  var isActive = false;\n\n\n  function handleKeyEvent(keyEvent) {\n    if (isKey([ 't', 'T' ], keyEvent)) {\n      editorActions.trigger('toggleTokenSimulation');\n\n      return true;\n    }\n\n    if (!isActive) {\n      return;\n    }\n\n    if (isKey([ 'l', 'L' ], keyEvent)) {\n      editorActions.trigger('toggleTokenSimulationLog');\n\n      return true;\n    }\n\n    // see https://developer.mozilla.org/de/docs/Web/API/KeyboardEvent/key/Key_Values#Whitespace_keys\n    if (isKey([ ' ', 'Spacebar' ], keyEvent)) {\n      editorActions.trigger('togglePauseTokenSimulation');\n\n      return true;\n    }\n\n    if (is<PERSON>ey([ 'r', 'R' ], keyEvent)) {\n      editorActions.trigger('resetTokenSimulation');\n\n      return true;\n    }\n  }\n\n\n  eventBus.on('keyboard.init', function() {\n\n    keyboard.addListener(VERY_HIGH_PRIORITY, function(event) {\n      var keyEvent = event.keyEvent;\n\n      handleKeyEvent(keyEvent);\n    });\n\n  });\n\n  eventBus.on(TOGGLE_MODE_EVENT, function(context) {\n    var simulationModeActive = context.simulationModeActive;\n\n    if (simulationModeActive) {\n      isActive = true;\n    } else {\n      isActive = false;\n    }\n  });\n\n}\n\nKeyboardBindings.$inject = [ 'eventBus', 'injector' ];\n\nmodule.exports = KeyboardBindings;\n\n\n// helpers //////////\n\nfunction isKey(keys, event) {\n  return keys.indexOf(event.key) > -1;\n}", "module.exports = require('./KeyboardBindings');", "'use strict';\r\n\r\nvar domify = require('min-dom/lib/domify'),\r\n    domClasses = require('min-dom/lib/classes');\r\n\r\nvar events = require('../../util/EventHelper'),\r\n    TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;\r\n\r\nfunction Palette(eventBus, canvas) {\r\n  var self = this;\r\n\r\n  this._canvas = canvas;\r\n\r\n  this.entries = [];\r\n\r\n  this._init();\r\n\r\n  eventBus.on(TOGGLE_MODE_EVENT, function(context) {\r\n    var simulationModeActive = context.simulationModeActive;\r\n\r\n    if (simulationModeActive) {\r\n      domClasses(self.container).remove('hidden');\r\n    } else {\r\n      domClasses(self.container).add('hidden');\r\n    }\r\n  });\r\n}\r\n\r\nPalette.prototype._init = function() {\r\n  this.container = domify('<div class=\"token-simulation-palette hidden\"></div>');\r\n\r\n  this._canvas.getContainer().appendChild(this.container);\r\n};\r\n\r\nPalette.prototype.addEntry = function(entry, index) {\r\n  var childIndex = 0;\r\n\r\n  this.entries.forEach(function(entry) {\r\n    if (index >= entry.index) {\r\n      childIndex++;\r\n    }\r\n  });\r\n\r\n  this.container.insertBefore(entry, this.container.childNodes[childIndex]);\r\n\r\n  this.entries.push({\r\n    entry: entry,\r\n    index: index\r\n  });\r\n};\r\n\r\nPalette.$inject = [ 'eventBus', 'canvas' ];\r\n\r\nmodule.exports = Palette;", "module.exports = require('./Palette');", "module.exports = {\n  __init__: [\n    'animation',\n    'contextPads',\n    'disableModeling',\n    'elementNotifications',\n    'elementSupport',\n    'exclusiveGatewaySettings',\n    'log',\n    'notifications',\n    'pauseSimulation',\n    'preserveElementColors',\n    'processInstanceIds',\n    'processInstanceSettings',\n    'processInstances',\n    'resetSimulation',\n    'setAnimationSpeed',\n    'showProcessInstance',\n    'simulationState',\n    'toggleMode',\n    'tokenCount',\n    'tokenSimulationBehavior',\n    'tokenSimulationEditorActions',\n    'tokenSimulationKeyboardBindings',\n    'tokenSimulationPalette'\n  ],\n  'animation': [ 'type', require('./animation/Animation') ],\n  'contextPads': [ 'type', require('./features/context-pads') ],\n  'disableModeling': [ 'type', require('./features/disable-modeling') ],\n  'elementNotifications': [ 'type', require('./features/element-notifications') ],\n  'elementSupport': [ 'type', require('./features/element-support') ],\n  'exclusiveGatewaySettings': [ 'type', require('./features/exclusive-gateway-settings') ],\n  'log': [ 'type', require('./features/log') ],\n  'notifications': [ 'type', require('./features/notifications') ],\n  'pauseSimulation': [ 'type', require('./features/pause-simulation') ],\n  'preserveElementColors': [ 'type', require('./features/preserve-element-colors') ],\n  'processInstanceIds': [ 'type', require('./features/process-instance-ids') ],\n  'processInstanceSettings': [ 'type', require('./features/process-instance-settings') ],\n  'processInstances': [ 'type', require('./features/process-instances') ],\n  'resetSimulation': [ 'type', require('./features/reset-simulation') ],\n  'setAnimationSpeed': [ 'type', require('./features/set-animation-speed') ],\n  'showProcessInstance': [ 'type', require('./features/show-process-instance') ],\n  'simulationState': [ 'type', require('./features/simulation-state') ],\n  'toggleMode': [ 'type', require('./features/toggle-mode/modeler') ],\n  'tokenCount': [ 'type', require('./features/token-count') ],\n  'tokenSimulationBehavior': [ 'type', require('./features/token-simulation-behavior') ],\n  'tokenSimulationEditorActions': [ 'type', require('./features/editor-actions') ],\n  'tokenSimulationKeyboardBindings': [ 'type', require('./features/keyboard-bindings') ],\n  'tokenSimulationPalette': [ 'type', require('./features/palette') ]\n};", "module.exports = require('./lib/modeler');\r\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAUA,KAAC,SAAS,MAAM,SAAS;AAEvB,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAC9C,eAAO,WAAU;AACf,iBAAO,QAAQ,MAAM,KAAK,QAAQ;AAAA,QACpC,CAAC;AAAA,MACH,WAAW,OAAO,YAAY,UAAU;AACtC,eAAO,UAAU,KAAK,WAAW,QAAQ,MAAM,KAAK,QAAQ,IAAI,SAAS,GAAE;AAAE,iBAAO,QAAQ,GAAG,EAAE,QAAQ;AAAA,QAAE;AAAA,MAC7G,OAAO;AACL,aAAK,MAAM,QAAQ,MAAM,KAAK,QAAQ;AAAA,MACxC;AAAA,IACF,GAAE,OAAO,WAAW,cAAc,SAAS,SAAM,SAASA,SAAQC,WAAU;AAI5E,UAAI,YAAa,OAAO,SAAS,cAAe,OAAOD;AAGvD,UAAI,MAAM,UAAU,MAAM,SAAS,SAAS;AAC1C,YAAI,IAAI,WAAW;AACjB,oBAAU,IAAI,IAAI,IAAI,OAAO;AAE7B,cAAG,CAAC,IAAI,OAAO;AACb,gBAAI,QAAQ;AAEd,iBAAO;AAAA,QACT;AAAA,MACF;AAGA,UAAI,KAAQ;AACZ,UAAI,QAAQ;AACZ,UAAI,QAAQ;AACZ,UAAI,QAAQ;AAGZ,UAAI,YAAa,WAAW;AAC1B,eAAO,CAAC,CAAEC,UAAS,mBACZ,CAAC,CAAEA,UAAS,gBAAgB,IAAI,IAAG,KAAK,EAAE;AAAA,MACnD,EAAG;AAGH,UAAI,CAAC,IAAI;AAAW,eAAO;AAG3B,UAAI,MAAO;AAGX,UAAI,MAAM,SAAS,MAAM;AACvB,eAAO,UAAU,WAAW,IAAI,IAAK,IAAI;AAAA,MAC3C;AAGA,UAAI,SAAS,SAAS,MAAM;AAE1B,YAAI,UAAUA,UAAS,gBAAgB,KAAK,IAAI,IAAI;AAGpD,gBAAQ,aAAa,MAAM,KAAK,IAAI,IAAI,CAAC;AAEzC,eAAO;AAAA,MACT;AAGA,UAAI,SAAS,WAAW;AACtB,YAAI,SAAS,SAAS,KAAKC;AAG3B,kBAAU,CAAC,EAAE,MAAM,KAAK,SAAS;AAGjC,kBAAU,QAAQ,IAAI;AAEtB,aAAKA,KAAI,QAAQ,SAAS,GAAGA,MAAK,GAAGA;AACnC,cAAI,QAAQA,EAAC;AACX,iBAAK,OAAO;AACV,sBAAQA,EAAC,EAAE,UAAU,GAAG,IAAI,QAAQ,GAAG;AAG7C,YAAI,IAAI,OAAO,IAAI,IAAI;AACrB,cAAI,IAAI,QAAQ;AAAA,MACpB;AAGA,UAAI,SAAS,SAAS,QAAQ;AAE5B,YAAI,cAAc,OAAO,OAAO,UAAU,aACxC,OAAO,SACP,WAAW;AACT,eAAK,YAAY,KAAK,MAAM,IAAI,OAAO,OAAO,MAAM,CAAC;AAAA,QACvD;AAGF,YAAI,OAAO;AACT,sBAAY,YAAY,IAAI,OAAO;AAGrC,YAAI,OAAO;AACT,cAAI,OAAO,aAAa,OAAO,MAAM;AAGvC,YAAI,OAAO;AACT,cAAI,OAAO,OAAO,UAAU,IAAI,WAAW,OAAO,SAAS;AAE7D,eAAO;AAAA,MACT;AAGA,UAAI,QAAQ,SAAS,MAAM;AAEzB,YAAI,CAAC;AAAM,iBAAO;AAGlB,YAAI,KAAK;AAAU,iBAAO,KAAK;AAG/B,YAAI;AAGJ,YAAI,KAAK,YAAY;AACnB,oBAAU,KAAK,sBAAsBF,QAAO,aAAa,IAAI,IAAI,WAAS,IAAI,IAAI;AAAA,iBAC3E,KAAK,YAAY;AACxB,oBAAU,IAAI,IAAI,SAAS,QAAQ;AAAA,iBAC5B,KAAK,YAAY;AACxB,oBAAU,IAAI,IAAI,SAAS,QAAQ;AAAA,iBAC5B,IAAI,WAAW,KAAK,QAAQ,CAAC;AACpC,oBAAU,IAAI,IAAI,WAAW,KAAK,QAAQ,CAAC;AAAA;AAE3C,oBAAU,IAAI,IAAI,QAAQ,IAAI;AAGhC,gBAAQ,OAAQ,KAAK;AACrB,gBAAQ,OAAQ;AAChB,aAAK,WAAW;AAGhB,YAAI,mBAAmB,IAAI;AACzB,kBAAQ,UAAU,EAAE,KAAK;AAG3B,gBAAQ,QAAQ,KAAK,MAAM,KAAK,aAAa,YAAY,CAAC,KAAK,CAAC,CAAC;AAEjE,eAAO;AAAA,MACT;AAGA,UAAI,UAAU,WAAW;AAEvB,YAAI,OAAOC,UAAS,qBAAqB,MAAM,EAAE,CAAC,GAC9C,QAAQ,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAMA,UAAS,eAAe,EAAE,OAAO,GAAG,KAAK,GAAG,CAAC;AAG9F,YAAI,SAAS;AAAA,UACX,MAAM,QAAQA,UAAS;AAAA,UACvB,MAAM,KAAK,MAAM,kEAAkE,EAAE,KAAK,aAAa,OAAO,EAAE;AAAA,UAChH,MAAM,KAAK,SAAS,EAAE;AAAA,UACtB,MAAM,KAAK,KAAK,EAAE;AAAA,UAClB,QAAQ,IAAI,OAAO,KAAK;AAAA,QAC1B;AAAA,MACF;AAEA,UAAI,SAAS;AAAA,QACX,QAAQ,IAAI,OAAO,KAAK;AAAA,MAC1B;AAEA,MAAAA,UAAS,iBAAiB,oBAAoB,WAAW;AACvD,YAAG,CAAC,IAAI,OAAO;AACb,cAAI,QAAQ;AAAA,MAChB,GAAG,KAAK;AAGR,UAAI,QAAQ;AAAA;AAAA,QAEV,eAAkB;AAAA,QAGlB,KAAkB;AAAA,QAGlB,KAAkB;AAAA,QAGlB,WAAkB;AAAA,QAGlB,YAAkB;AAAA,QAGlB,YAAkB;AAAA,QAGlB,OAAkB;AAAA,QAGlB,OAAkB;AAAA,QAGlB,OAAkB;AAAA,QAGlB,SAAkB;AAAA,QAGlB,UAAkB;AAAA,QAGlB,WAAkB;AAAA,QAGlB,SAAkB;AAAA,QAGlB,WAAkB;AAAA,QAKlB,QAAkB;AAAA,QAGlB,aAAkB;AAAA,QAGlB,cAAkB;AAAA,QAGlB,iBAAkB;AAAA,QAGlB,MAAkB;AAAA,MACpB;AAEA,UAAI,QAAQ;AAAA;AAAA,QAEV,KAAK,SAAS,OAAO,OAAO;AAC1B,cAAIC,IACAC,MAAK,MAAM,QACX,SAAS,CAAC;AAEd,eAAKD,KAAI,GAAGA,KAAIC,KAAID;AAClB,mBAAO,KAAK,MAAM,MAAMA,EAAC,CAAC,CAAC;AAE7B,iBAAO;AAAA,QACT;AAAA,QAGA,QAAQ,SAAS,OAAO,OAAO;AAC7B,cAAIA,IACAC,MAAK,MAAM,QACX,SAAS,CAAC;AAEd,eAAKD,KAAI,GAAGA,KAAIC,KAAID;AAClB,gBAAI,MAAM,MAAMA,EAAC,CAAC;AAChB,qBAAO,KAAK,MAAMA,EAAC,CAAC;AAExB,iBAAO;AAAA,QACT;AAAA,QAGA,SAAS,SAAS,GAAG;AACnB,iBAAO,IAAI,MAAM,KAAK,KAAK;AAAA,QAC7B;AAAA,QAGA,SAAS,SAAS,GAAG;AACnB,iBAAO,IAAI,MAAM,KAAK,KAAK;AAAA,QAC7B;AAAA,QAEA,mBAAmB,SAAS,OAAO;AACjC,iBAAO,KAAK,OAAQ,OAAO,SAAS,IAAI;AAAE,mBAAO,cAAcF,QAAO;AAAA,UAAW,CAAC;AAAA,QACpF;AAAA,MAEF;AAEA,UAAI,WAAW;AAAA;AAAA,QAEb,OAAO;AAAA;AAAA,UAEL,gBAAoB;AAAA,UACpB,kBAAoB;AAAA,UACpB,gBAAoB;AAAA,UACpB,mBAAoB;AAAA,UACpB,kBAAoB;AAAA,UACpB,MAAoB;AAAA,UACpB,QAAoB;AAAA,UACpB,SAAoB;AAAA,UAEpB,GAAoB;AAAA,UACpB,GAAoB;AAAA,UACpB,IAAoB;AAAA,UACpB,IAAoB;AAAA,UAEpB,OAAoB;AAAA,UACpB,QAAoB;AAAA,UAEpB,GAAoB;AAAA,UACpB,IAAoB;AAAA,UACpB,IAAoB;AAAA,UAEpB,QAAoB;AAAA,UACpB,gBAAoB;AAAA,UACpB,cAAoB;AAAA,UAEpB,aAAoB;AAAA,UACpB,eAAoB;AAAA,UACpB,eAAoB;AAAA,QACtB;AAAA,MAEF;AAEA,UAAI,QAAQ,SAAS,OAAO;AAC1B,YAAI;AAGJ,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,IAAI;AAET,YAAG,CAAC;AAAO;AAGX,YAAI,OAAO,UAAU,UAAU;AAC7B,cAAI,IAAI,MAAM,MAAM,KAAK,KAAK,GAAG;AAE/B,oBAAQ,IAAI,MAAM,IAAI,KAAK,MAAM,QAAQ,IAAI,MAAM,YAAW,EAAE,CAAC;AAGjE,iBAAK,IAAI,SAAS,MAAM,CAAC,CAAC;AAC1B,iBAAK,IAAI,SAAS,MAAM,CAAC,CAAC;AAC1B,iBAAK,IAAI,SAAS,MAAM,CAAC,CAAC;AAAA,UAE5B,WAAW,IAAI,MAAM,MAAM,KAAK,KAAK,GAAG;AAEtC,oBAAQ,IAAI,MAAM,IAAI,KAAK,QAAQ,KAAK,CAAC;AAGzC,iBAAK,IAAI,SAAS,MAAM,CAAC,GAAG,EAAE;AAC9B,iBAAK,IAAI,SAAS,MAAM,CAAC,GAAG,EAAE;AAC9B,iBAAK,IAAI,SAAS,MAAM,CAAC,GAAG,EAAE;AAAA,UAEhC;AAAA,QAEF,WAAW,OAAO,UAAU,UAAU;AACpC,eAAK,IAAI,MAAM;AACf,eAAK,IAAI,MAAM;AACf,eAAK,IAAI,MAAM;AAAA,QAEjB;AAAA,MAEF;AAEA,UAAI,OAAO,IAAI,OAAO;AAAA;AAAA,QAEpB,UAAU,WAAW;AACnB,iBAAO,KAAK,MAAM;AAAA,QACpB;AAAA,QAEA,OAAO,WAAW;AAChB,iBAAO,MACH,UAAU,KAAK,CAAC,IAChB,UAAU,KAAK,CAAC,IAChB,UAAU,KAAK,CAAC;AAAA,QACtB;AAAA,QAEA,OAAO,WAAW;AAChB,iBAAO,SAAS,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,EAAE,KAAK,IAAI;AAAA,QACpD;AAAA,QAEA,YAAY,WAAW;AACrB,iBAAQ,KAAK,IAAI,MAAM,MACf,KAAK,IAAI,MAAM,OACf,KAAK,IAAI,MAAM;AAAA,QACzB;AAAA,QAEA,OAAO,SAAS,OAAO;AACrB,eAAK,cAAc,IAAI,IAAI,MAAM,KAAK;AAEtC,iBAAO;AAAA,QACT;AAAA,QAEA,IAAI,SAAS,KAAK;AAEhB,cAAI,CAAC,KAAK;AAAa,mBAAO;AAG9B,gBAAM,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI;AAGlC,iBAAO,IAAI,IAAI,MAAM;AAAA,YACnB,GAAG,CAAC,EAAE,KAAK,KAAK,KAAK,YAAY,IAAI,KAAK,KAAK;AAAA,YAC/C,GAAG,CAAC,EAAE,KAAK,KAAK,KAAK,YAAY,IAAI,KAAK,KAAK;AAAA,YAC/C,GAAG,CAAC,EAAE,KAAK,KAAK,KAAK,YAAY,IAAI,KAAK,KAAK;AAAA,UACjD,CAAC;AAAA,QACH;AAAA,MAEF,CAAC;AAKD,UAAI,MAAM,OAAO,SAAS,OAAO;AAC/B,iBAAS;AACT,eAAO,IAAI,MAAM,MAAM,KAAK,KAAK,KAC1B,IAAI,MAAM,MAAM,KAAK,KAAK;AAAA,MACnC;AAGA,UAAI,MAAM,QAAQ,SAAS,OAAO;AAChC,eAAO,SAAS,OAAO,MAAM,KAAK,YAClB,OAAO,MAAM,KAAK,YAClB,OAAO,MAAM,KAAK;AAAA,MACpC;AAGA,UAAI,MAAM,UAAU,SAAS,OAAO;AAClC,eAAO,IAAI,MAAM,MAAM,KAAK,KAAK,IAAI,MAAM,KAAK,KAAK;AAAA,MACvD;AAEA,UAAI,QAAQ,SAAS,OAAO,UAAU;AACpC,iBAAS,SAAS,CAAC,GAAG,QAAQ;AAG9B,YAAI,MAAM,UAAU,KAAK;AACvB,kBAAQ,SAAS,QAAQ;AAG3B,aAAK,QAAQ,KAAK,MAAM,KAAK;AAAA,MAC/B;AAEA,UAAI,OAAO,IAAI,OAAO;AAAA;AAAA,QAEpB,OAAO,SAAS,OAAO;AACrB,eAAK,cAAc,KAAK,MAAM,KAAK;AAGnC,cAAI,KAAK,MAAM,UAAU,KAAK,YAAY,QAAQ;AAChD,gBAAI,YAAkB,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC,GAClD,kBAAkB,KAAK,YAAY,KAAK,YAAY,SAAS,CAAC;AAElE,mBAAM,KAAK,MAAM,SAAS,KAAK,YAAY;AACzC,mBAAK,YAAY,KAAK,eAAe;AACvC,mBAAM,KAAK,MAAM,SAAS,KAAK,YAAY;AACzC,mBAAK,MAAM,KAAK,SAAS;AAAA,UAC7B;AAEA,iBAAO;AAAA,QACT;AAAA,QAEA,QAAQ,WAAW;AAEjB,mBAASE,KAAI,GAAGC,MAAK,KAAK,MAAM,QAAQ,OAAO,CAAC,GAAGD,KAAIC,KAAID;AACzD,gBAAI,KAAK,QAAQ,KAAK,MAAMA,EAAC,CAAC,KAAK;AACjC,mBAAK,KAAK,KAAK,MAAMA,EAAC,CAAC;AAG3B,iBAAO,KAAK,QAAQ;AAAA,QACtB;AAAA,QAEA,IAAI,SAAS,KAAK;AAEhB,cAAI,CAAC,KAAK;AAAa,mBAAO;AAG9B,mBAASA,KAAI,GAAGC,MAAK,KAAK,MAAM,QAAQ,QAAQ,CAAC,GAAGD,KAAIC,KAAID;AAC1D,kBAAM,KAAK,KAAK,MAAMA,EAAC,KAAK,KAAK,YAAYA,EAAC,IAAI,KAAK,MAAMA,EAAC,KAAK,GAAG;AAExE,iBAAO,IAAI,IAAI,MAAM,KAAK;AAAA,QAC5B;AAAA,QAEA,UAAU,WAAW;AACnB,iBAAO,KAAK,MAAM,KAAK,GAAG;AAAA,QAC5B;AAAA,QAEA,SAAS,WAAW;AAClB,iBAAO,KAAK;AAAA,QACd;AAAA,QAEA,OAAO,SAAS,OAAO;AACrB,kBAAQ,MAAM,QAAQ;AAGtB,cAAI,MAAM,QAAQ,KAAK;AAAG,mBAAO;AAEjC,iBAAO,KAAK,MAAM,KAAK;AAAA,QACzB;AAAA,QAEA,OAAO,SAAS,QAAQ;AACtB,iBAAO,OAAO,KAAK,EAAE,MAAM,IAAI,MAAM,SAAS,EAAE,IAAI,UAAU;AAAA,QAChE;AAAA,QAEA,SAAS,WAAW;AAClB,eAAK,MAAM,QAAQ;AAEnB,iBAAO;AAAA,QACT;AAAA,QACA,OAAO,WAAW;AAChB,cAAI,QAAQ,IAAI,KAAK,YAAY;AACjC,gBAAM,QAAQ,YAAY,KAAK,KAAK;AACpC,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAED,UAAI,aAAa,SAAS,OAAO,UAAU;AACzC,YAAI,MAAM,KAAK,MAAM,OAAO,YAAY,CAAC,CAAC,GAAE,CAAC,CAAC,CAAC;AAAA,MACjD;AAGA,UAAI,WAAW,YAAY,IAAI,IAAI;AACnC,UAAI,WAAW,UAAU,cAAc,IAAI;AAE3C,UAAI,OAAO,IAAI,YAAY;AAAA;AAAA,QAEzB,UAAU,WAAW;AAEnB,mBAASA,KAAI,GAAGC,MAAK,KAAK,MAAM,QAAQ,QAAQ,CAAC,GAAGD,KAAIC,KAAID;AAC1D,kBAAM,KAAK,KAAK,MAAMA,EAAC,EAAE,KAAK,GAAG,CAAC;AAEpC,iBAAO,MAAM,KAAK,GAAG;AAAA,QACvB;AAAA,QAEA,QAAQ,WAAW;AACjB,iBAAO;AAAA,YACL,IAAI,KAAK,MAAM,CAAC,EAAE,CAAC;AAAA,YACnB,IAAI,KAAK,MAAM,CAAC,EAAE,CAAC;AAAA,YACnB,IAAI,KAAK,MAAM,CAAC,EAAE,CAAC;AAAA,YACnB,IAAI,KAAK,MAAM,CAAC,EAAE,CAAC;AAAA,UACrB;AAAA,QACF;AAAA,QAEA,IAAI,SAAS,KAAK;AAEhB,cAAI,CAAC,KAAK;AAAa,mBAAO;AAG9B,mBAASA,KAAI,GAAGC,MAAK,KAAK,MAAM,QAAQ,QAAQ,CAAC,GAAGD,KAAIC,KAAID;AAC1D,kBAAM,KAAK;AAAA,cACT,KAAK,MAAMA,EAAC,EAAE,CAAC,KAAK,KAAK,YAAYA,EAAC,EAAE,CAAC,IAAI,KAAK,MAAMA,EAAC,EAAE,CAAC,KAAK;AAAA,cACjE,KAAK,MAAMA,EAAC,EAAE,CAAC,KAAK,KAAK,YAAYA,EAAC,EAAE,CAAC,IAAI,KAAK,MAAMA,EAAC,EAAE,CAAC,KAAK;AAAA,YACnE,CAAC;AAEH,iBAAO,IAAI,IAAI,WAAW,KAAK;AAAA,QACjC;AAAA,QAEA,OAAO,SAAS,OAAO;AACrB,cAAI,SAAS,CAAC;AAEd,kBAAQ,MAAM,QAAQ;AAGtB,cAAI,MAAM,QAAQ,KAAK,GAAG;AAExB,gBAAG,MAAM,QAAQ,MAAM,CAAC,CAAC,GAAG;AAE1B,qBAAO,MAAM,IAAI,SAAU,IAAI;AAAE,uBAAO,GAAG,MAAM;AAAA,cAAE,CAAC;AAAA,YACtD,WAAW,MAAM,CAAC,EAAE,KAAK,MAAK;AAE5B,qBAAO,MAAM,IAAI,SAAU,IAAI;AAAE,uBAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,cAAE,CAAC;AAAA,YACxD;AAAA,UACF,OAAO;AAEL,oBAAQ,MAAM,KAAK,EAAE,MAAM,IAAI,MAAM,SAAS,EAAE,IAAI,UAAU;AAAA,UAChE;AAIA,cAAI,MAAM,SAAS,MAAM;AAAG,kBAAM,IAAI;AAGtC,mBAAQA,KAAI,GAAG,MAAM,MAAM,QAAQA,KAAI,KAAKA,KAAIA,KAAI;AAClD,mBAAO,KAAK,CAAE,MAAMA,EAAC,GAAG,MAAMA,KAAE,CAAC,CAAE,CAAC;AAEtC,iBAAO;AAAA,QACT;AAAA,QAEA,MAAM,SAAS,GAAG,GAAG;AACnB,cAAI,MAAM,KAAK,KAAK;AAGpB,eAAK,IAAI;AACT,eAAK,IAAI;AAGT,cAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;AACvB,qBAASA,KAAI,KAAK,MAAM,SAAS,GAAGA,MAAK,GAAGA;AAC1C,mBAAK,MAAMA,EAAC,IAAI,CAAC,KAAK,MAAMA,EAAC,EAAE,CAAC,IAAI,GAAG,KAAK,MAAMA,EAAC,EAAE,CAAC,IAAI,CAAC;AAE/D,iBAAO;AAAA,QACT;AAAA,QAEA,MAAM,SAAS,OAAO,QAAQ;AAC5B,cAAIA,IAAG,MAAM,KAAK,KAAK;AAGvB,eAAKA,KAAI,KAAK,MAAM,SAAS,GAAGA,MAAK,GAAGA,MAAK;AAC3C,gBAAG,IAAI;AAAO,mBAAK,MAAMA,EAAC,EAAE,CAAC,KAAM,KAAK,MAAMA,EAAC,EAAE,CAAC,IAAI,IAAI,KAAK,QAAU,IAAI,QAAS,IAAI;AAC1F,gBAAG,IAAI;AAAQ,mBAAK,MAAMA,EAAC,EAAE,CAAC,KAAM,KAAK,MAAMA,EAAC,EAAE,CAAC,IAAI,IAAI,KAAK,SAAU,IAAI,SAAS,IAAI;AAAA,UAC7F;AAEA,iBAAO;AAAA,QACT;AAAA,QAEA,MAAM,WAAW;AACf,cAAI,OAAO,KAAK,aAAa,UAAU,KAAK,SAAS,CAAC;AAEtD,iBAAO,IAAI,OAAO,KAAK,QAAQ;AAAA,QACjC;AAAA,MACF,CAAC;AAED,UAAI,eAAe;AAAA,QACjB,GAAG,SAAS,GAAG,GAAG,IAAI;AACpB,YAAE,IAAI,GAAG,IAAI,EAAE,CAAC;AAChB,YAAE,IAAI,GAAG,IAAI,EAAE,CAAC;AAEhB,iBAAO,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;AAAA,QACvB;AAAA,QACA,GAAG,SAAS,GAAG,GAAG;AAChB,YAAE,IAAI,EAAE,CAAC;AACT,YAAE,IAAI,EAAE,CAAC;AACT,iBAAO,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,QACzB;AAAA,QACA,GAAG,SAAS,GAAG,GAAG;AAChB,YAAE,IAAI,EAAE,CAAC;AACT,iBAAO,CAAC,KAAK,EAAE,CAAC,CAAC;AAAA,QACnB;AAAA,QACA,GAAG,SAAS,GAAG,GAAG;AAChB,YAAE,IAAI,EAAE,CAAC;AACT,iBAAO,CAAC,KAAK,EAAE,CAAC,CAAC;AAAA,QACnB;AAAA,QACA,GAAG,SAAS,GAAG,GAAG;AAChB,YAAE,IAAI,EAAE,CAAC;AACT,YAAE,IAAI,EAAE,CAAC;AACT,iBAAO,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,QACjD;AAAA,QACA,GAAG,SAAS,GAAG,GAAG;AAChB,YAAE,IAAI,EAAE,CAAC;AACT,YAAE,IAAI,EAAE,CAAC;AACT,iBAAO,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,QACrC;AAAA,QACA,GAAG,SAAS,GAAG,GAAG;AAChB,YAAE,IAAI,EAAE,CAAC;AACT,YAAE,IAAI,EAAE,CAAC;AACT,iBAAO,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,QACrC;AAAA,QACA,GAAG,SAAS,GAAG,GAAG;AAChB,YAAE,IAAI,EAAE,CAAC;AACT,YAAE,IAAI,EAAE,CAAC;AACT,iBAAO,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,QACzB;AAAA,QACA,GAAG,SAAS,GAAG,GAAG,IAAI;AACpB,YAAE,IAAI,GAAG;AACT,YAAE,IAAI,GAAG;AACT,iBAAO,CAAC,GAAG;AAAA,QACb;AAAA,QACA,GAAG,SAAS,GAAG,GAAG;AAChB,YAAE,IAAI,EAAE,CAAC;AACT,YAAE,IAAI,EAAE,CAAC;AACT,iBAAO,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,QACvD;AAAA,MACF;AAEA,UAAI,YAAY,aAAa,MAAM,EAAE;AAErC,eAAQ,IAAI,GAAG,KAAK,UAAU,QAAQ,IAAI,IAAI,EAAE,GAAE;AAChD,qBAAa,UAAU,CAAC,CAAC,IAAK,yBAASA,IAAE;AACvC,iBAAO,SAAS,GAAG,GAAG,IAAI;AACxB,gBAAGA,MAAK;AAAK,gBAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE;AAAA,qBACrBA,MAAK;AAAK,gBAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE;AAAA,qBAC1BA,MAAK,KAAI;AACf,gBAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,GAChB,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE;AAAA,YAClB;AAEE,uBAAQ,IAAI,GAAG,KAAK,EAAE,QAAQ,IAAI,IAAI,EAAE,GAAG;AACzC,kBAAE,CAAC,IAAI,EAAE,CAAC,KAAK,IAAE,IAAI,EAAE,IAAI,EAAE;AAAA,cAC/B;AAEF,mBAAO,aAAaA,EAAC,EAAE,GAAG,GAAG,EAAE;AAAA,UACjC;AAAA,QACF,EAAG,UAAU,CAAC,EAAE,YAAY,CAAC;AAAA,MAC/B;AAGA,UAAI,YAAY,SAAS,OAAO,UAAU;AACxC,YAAI,MAAM,KAAK,MAAM,OAAO,YAAY,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AAAA,MACvD;AAGA,UAAI,UAAU,YAAY,IAAI,IAAI;AAClC,UAAI,UAAU,UAAU,cAAc,IAAI;AAE1C,UAAI,OAAO,IAAI,WAAW;AAAA;AAAA,QAExB,UAAU,WAAW;AACnB,iBAAO,cAAc,KAAK,KAAK;AAAA,QACjC;AAAA,QAEA,MAAM,SAAS,GAAG,GAAG;AAEnB,cAAI,MAAM,KAAK,KAAK;AAGpB,eAAK,IAAI;AACT,eAAK,IAAI;AAET,cAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG;AAE1B,qBAAS,GAAGA,KAAI,KAAK,MAAM,SAAS,GAAGA,MAAK,GAAGA,MAAK;AAClD,kBAAI,KAAK,MAAMA,EAAC,EAAE,CAAC;AAEnB,kBAAI,KAAK,OAAO,KAAK,OAAO,KAAK,KAAM;AACrC,qBAAK,MAAMA,EAAC,EAAE,CAAC,KAAK;AACpB,qBAAK,MAAMA,EAAC,EAAE,CAAC,KAAK;AAAA,cAEtB,WAAW,KAAK,KAAM;AACpB,qBAAK,MAAMA,EAAC,EAAE,CAAC,KAAK;AAAA,cAEtB,WAAW,KAAK,KAAM;AACpB,qBAAK,MAAMA,EAAC,EAAE,CAAC,KAAK;AAAA,cAEtB,WAAW,KAAK,OAAO,KAAK,OAAO,KAAK,KAAM;AAC5C,qBAAK,MAAMA,EAAC,EAAE,CAAC,KAAK;AACpB,qBAAK,MAAMA,EAAC,EAAE,CAAC,KAAK;AACpB,qBAAK,MAAMA,EAAC,EAAE,CAAC,KAAK;AACpB,qBAAK,MAAMA,EAAC,EAAE,CAAC,KAAK;AAEpB,oBAAI,KAAK,KAAM;AACb,uBAAK,MAAMA,EAAC,EAAE,CAAC,KAAK;AACpB,uBAAK,MAAMA,EAAC,EAAE,CAAC,KAAK;AAAA,gBACtB;AAAA,cAEF,WAAW,KAAK,KAAM;AACpB,qBAAK,MAAMA,EAAC,EAAE,CAAC,KAAK;AACpB,qBAAK,MAAMA,EAAC,EAAE,CAAC,KAAK;AAAA,cACtB;AAAA,YAEF;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA,QAEA,MAAM,SAAS,OAAO,QAAQ;AAE5B,cAAIA,IAAG,GAAG,MAAM,KAAK,KAAK;AAG1B,eAAKA,KAAI,KAAK,MAAM,SAAS,GAAGA,MAAK,GAAGA,MAAK;AAC3C,gBAAI,KAAK,MAAMA,EAAC,EAAE,CAAC;AAEnB,gBAAI,KAAK,OAAO,KAAK,OAAO,KAAK,KAAM;AACrC,mBAAK,MAAMA,EAAC,EAAE,CAAC,KAAM,KAAK,MAAMA,EAAC,EAAE,CAAC,IAAI,IAAI,KAAK,QAAU,IAAI,QAAS,IAAI;AAC5E,mBAAK,MAAMA,EAAC,EAAE,CAAC,KAAM,KAAK,MAAMA,EAAC,EAAE,CAAC,IAAI,IAAI,KAAK,SAAU,IAAI,SAAS,IAAI;AAAA,YAE9E,WAAW,KAAK,KAAM;AACpB,mBAAK,MAAMA,EAAC,EAAE,CAAC,KAAM,KAAK,MAAMA,EAAC,EAAE,CAAC,IAAI,IAAI,KAAK,QAAU,IAAI,QAAS,IAAI;AAAA,YAE9E,WAAW,KAAK,KAAM;AACpB,mBAAK,MAAMA,EAAC,EAAE,CAAC,KAAM,KAAK,MAAMA,EAAC,EAAE,CAAC,IAAI,IAAI,KAAK,SAAU,IAAI,SAAS,IAAI;AAAA,YAE9E,WAAW,KAAK,OAAO,KAAK,OAAO,KAAK,KAAM;AAC5C,mBAAK,MAAMA,EAAC,EAAE,CAAC,KAAM,KAAK,MAAMA,EAAC,EAAE,CAAC,IAAI,IAAI,KAAK,QAAU,IAAI,QAAS,IAAI;AAC5E,mBAAK,MAAMA,EAAC,EAAE,CAAC,KAAM,KAAK,MAAMA,EAAC,EAAE,CAAC,IAAI,IAAI,KAAK,SAAU,IAAI,SAAS,IAAI;AAC5E,mBAAK,MAAMA,EAAC,EAAE,CAAC,KAAM,KAAK,MAAMA,EAAC,EAAE,CAAC,IAAI,IAAI,KAAK,QAAU,IAAI,QAAS,IAAI;AAC5E,mBAAK,MAAMA,EAAC,EAAE,CAAC,KAAM,KAAK,MAAMA,EAAC,EAAE,CAAC,IAAI,IAAI,KAAK,SAAU,IAAI,SAAS,IAAI;AAE5E,kBAAI,KAAK,KAAM;AACb,qBAAK,MAAMA,EAAC,EAAE,CAAC,KAAM,KAAK,MAAMA,EAAC,EAAE,CAAC,IAAI,IAAI,KAAK,QAAU,IAAI,QAAS,IAAI;AAC5E,qBAAK,MAAMA,EAAC,EAAE,CAAC,KAAM,KAAK,MAAMA,EAAC,EAAE,CAAC,IAAI,IAAI,KAAK,SAAU,IAAI,SAAS,IAAI;AAAA,cAC9E;AAAA,YAEF,WAAW,KAAK,KAAM;AAEpB,mBAAK,MAAMA,EAAC,EAAE,CAAC,IAAK,KAAK,MAAMA,EAAC,EAAE,CAAC,IAAI,QAAU,IAAI;AACrD,mBAAK,MAAMA,EAAC,EAAE,CAAC,IAAK,KAAK,MAAMA,EAAC,EAAE,CAAC,IAAI,SAAU,IAAI;AAGrD,mBAAK,MAAMA,EAAC,EAAE,CAAC,KAAM,KAAK,MAAMA,EAAC,EAAE,CAAC,IAAI,IAAI,KAAK,QAAU,IAAI,QAAS,IAAI;AAC5E,mBAAK,MAAMA,EAAC,EAAE,CAAC,KAAM,KAAK,MAAMA,EAAC,EAAE,CAAC,IAAI,IAAI,KAAK,SAAU,IAAI,SAAS,IAAI;AAAA,YAC9E;AAAA,UAEF;AAEA,iBAAO;AAAA,QACT;AAAA,QAEA,eAAe,SAAS,WAAW;AACjC,cAAIA,IAAGC,KAAI;AAEX,sBAAY,IAAI,IAAI,UAAU,SAAS;AAEvC,0BAAgB,KAAK,MAAM,WAAW,UAAU,MAAM;AACtD,eAAID,KAAI,GAAGC,MAAK,KAAK,MAAM,QAAQ,iBAAiBD,KAAIC,KAAID,MAAK;AAC/D,4BAAgB,KAAK,MAAMA,EAAC,EAAE,CAAC,MAAM,UAAU,MAAMA,EAAC,EAAE,CAAC;AAAA,UAC3D;AAEA,iBAAO;AAAA,QACT;AAAA,QAEA,OAAO,SAAS,WAAW;AACzB,sBAAY,IAAI,IAAI,UAAU,SAAS;AAEvC,cAAG,KAAK,cAAc,SAAS,GAAG;AAChC,iBAAK,cAAc;AAAA,UACrB,OAAO;AACL,iBAAK,cAAc;AAAA,UACrB;AAEA,iBAAO;AAAA,QACT;AAAA,QAEA,IAAI,SAAS,KAAK;AAEhB,cAAI,CAAC,KAAK;AAAa,mBAAO;AAE9B,cAAI,cAAc,KAAK,OACnB,mBAAmB,KAAK,YAAY,OACpC,QAAQ,CAAC,GAAG,YAAY,IAAI,IAAI,UAAU,GAC1CA,IAAGC,KAAI,GAAG;AAId,eAAKD,KAAI,GAAGC,MAAK,YAAY,QAAQD,KAAIC,KAAID,MAAK;AAChD,kBAAMA,EAAC,IAAI,CAAC,YAAYA,EAAC,EAAE,CAAC,CAAC;AAC7B,iBAAI,IAAI,GAAG,KAAK,YAAYA,EAAC,EAAE,QAAQ,IAAI,IAAI,KAAK;AAClD,oBAAMA,EAAC,EAAE,CAAC,IAAI,YAAYA,EAAC,EAAE,CAAC,KAAK,iBAAiBA,EAAC,EAAE,CAAC,IAAI,YAAYA,EAAC,EAAE,CAAC,KAAK;AAAA,YACnF;AAMA,gBAAG,MAAMA,EAAC,EAAE,CAAC,MAAM,KAAK;AACtB,oBAAMA,EAAC,EAAE,CAAC,IAAI,EAAE,MAAMA,EAAC,EAAE,CAAC,KAAK;AAC/B,oBAAMA,EAAC,EAAE,CAAC,IAAI,EAAE,MAAMA,EAAC,EAAE,CAAC,KAAK;AAAA,YACjC;AAAA,UACF;AAGA,oBAAU,QAAQ;AAClB,iBAAO;AAAA,QACT;AAAA,QAEA,OAAO,SAAS,OAAO;AAErB,cAAI,iBAAiB,IAAI;AAAW,mBAAO,MAAM,QAAQ;AAGzD,cAAIA,IAAG,IAAI,IAAI,GAAG,KAAK,KACnB,IAAI,GACJ,IAAI,GACJ,WAAW,EAAE,KAAI,GAAG,KAAI,GAAG,KAAI,GAAG,KAAI,GAAG,KAAI,GAAG,KAAI,GAAG,KAAI,GAAG,KAAI,GAAG,KAAI,GAAG,KAAI,EAAE;AAEtF,cAAG,OAAO,SAAS,UAAS;AAE1B,oBAAQ,MACL,QAAQ,IAAI,MAAM,iBAAiB,cAAc,EACjD,QAAQ,IAAI,MAAM,aAAa,MAAM,EACrC,QAAQ,IAAI,MAAM,QAAQ,MAAM,EAChC,KAAK,EACL,MAAM,IAAI,MAAM,SAAS;AAAA,UAE9B,OAAK;AACH,oBAAQ,MAAM,OAAO,SAAS,MAAM,MAAK;AACvC,qBAAO,CAAC,EAAE,OAAO,KAAK,MAAM,IAAI;AAAA,YAClC,GAAG,CAAC,CAAC;AAAA,UACP;AAGA,cAAI,MAAM,CAAC,GACP,IAAI,IAAI,IAAI,MAAM,GAClB,KAAK,IAAI,IAAI,MAAM,GACnB,QAAQ,GACR,MAAM,MAAM;AAEhB,aAAE;AAEA,gBAAG,IAAI,MAAM,aAAa,KAAK,MAAM,KAAK,CAAC,GAAE;AAC3C,kBAAI,MAAM,KAAK;AACf,gBAAE;AAAA,YAEJ,WAAS,KAAK,KAAI;AAChB,kBAAI;AAAA,YACN,WAAS,KAAK,KAAI;AAChB,kBAAI;AAAA,YACN;AAEA,gBAAI;AAAA,cAAK,aAAa,CAAC,EAAE;AAAA,gBAAK;AAAA,gBAC1B,MAAM,MAAM,OAAQ,QAAQ,QAAQ,SAAS,EAAE,YAAY,CAAC,CAAE,EAAE,IAAI,UAAU;AAAA,gBAC9E;AAAA,gBAAG;AAAA,cACL;AAAA,YACF;AAAA,UAEF,SAAO,MAAM;AAEb,iBAAO;AAAA,QAET;AAAA,QAEA,MAAM,WAAW;AACf,cAAI,OAAO,KAAK,aAAa,KAAK,KAAK,SAAS,CAAC;AAEjD,iBAAO,IAAI,OAAO,KAAK,QAAQ;AAAA,QACjC;AAAA,MAEF,CAAC;AAGD,UAAI,SAAS,IAAI,OAAO;AAAA;AAAA,QAEtB,QAAQ,SAAS,OAAO,MAAM;AAE5B,eAAK,QAAQ;AACb,eAAK,OAAQ,QAAQ;AAGrB,cAAI,OAAO,UAAU,UAAU;AAE7B,iBAAK,QAAQ,MAAM,KAAK,IAAI,IAAI,CAAC,SAAS,KAAK,IAAK,QAAQ,IAAI,SAAW,QAAY;AAAA,UAEzF,WAAW,OAAO,UAAU,UAAU;AACpC,mBAAO,MAAM,MAAM,IAAI,MAAM,aAAa;AAE1C,gBAAI,MAAM;AAER,mBAAK,QAAQ,WAAW,KAAK,CAAC,CAAC;AAG/B,kBAAI,KAAK,CAAC,KAAK;AACb,qBAAK,SAAS;AAAA,uBACP,KAAK,CAAC,KAAK;AAClB,qBAAK,SAAS;AAGhB,mBAAK,OAAO,KAAK,CAAC;AAAA,YACpB;AAAA,UAEF,OAAO;AACL,gBAAI,iBAAiB,IAAI,QAAQ;AAC/B,mBAAK,QAAQ,MAAM,QAAQ;AAC3B,mBAAK,OAAQ,MAAM;AAAA,YACrB;AAAA,UACF;AAAA,QAEF;AAAA,QAEA,QAAQ;AAAA;AAAA,UAEN,UAAU,WAAW;AACnB,oBACE,KAAK,QAAQ,MACX,CAAC,EAAE,KAAK,QAAQ,OAAO,MACzB,KAAK,QAAQ,MACX,KAAK,QAAQ,MACb,KAAK,SACL,KAAK;AAAA,UACX;AAAA,UACA,QAAQ,WAAW;AACjB,mBAAO,KAAK,SAAS;AAAA,UACvB;AAAA;AAAA,UAEA,SAAS,WAAW;AAClB,mBAAO,KAAK;AAAA,UACd;AAAA,UAEA,MAAM,SAAS,QAAQ;AACrB,qBAAS,IAAI,IAAI,OAAO,MAAM;AAC9B,mBAAO,IAAI,IAAI,OAAO,OAAO,QAAQ,KAAK,QAAQ,OAAO,IAAI;AAAA,UAC/D;AAAA,UAEA,OAAO,SAAS,QAAQ;AACtB,qBAAS,IAAI,IAAI,OAAO,MAAM;AAC9B,mBAAO,IAAI,IAAI,OAAO,OAAO,QAAQ,KAAK,QAAQ,OAAO,IAAI;AAAA,UAC/D;AAAA,UAEA,OAAO,SAAS,QAAQ;AACtB,qBAAS,IAAI,IAAI,OAAO,MAAM;AAC9B,mBAAO,IAAI,IAAI,OAAO,OAAO,QAAQ,KAAK,QAAQ,OAAO,IAAI;AAAA,UAC/D;AAAA,UAEA,QAAQ,SAAS,QAAQ;AACvB,qBAAS,IAAI,IAAI,OAAO,MAAM;AAC9B,mBAAO,IAAI,IAAI,OAAO,OAAO,QAAQ,KAAK,QAAQ,OAAO,IAAI;AAAA,UAC/D;AAAA,UAEA,IAAI,SAAS,MAAM;AACjB,gBAAI,SAAS,IAAI,IAAI,OAAO,IAAI;AAEhC,gBAAI,OAAO,SAAS;AAClB,qBAAO,OAAO;AAEhB,mBAAO;AAAA,UACT;AAAA,UAEA,OAAO,SAAS,QAAQ;AACtB,iBAAK,cAAc,IAAI,IAAI,OAAO,MAAM;AAExC,gBAAG,OAAO,UAAU;AAClB,mBAAK,YAAY,SAAS,KAAK;AAAA,YACjC;AAEA,mBAAO;AAAA,UACT;AAAA,UAEA,IAAI,SAAS,KAAK;AAEhB,gBAAI,CAAC,KAAK;AAAa,qBAAO;AAG9B,mBAAO,IAAI,IAAI,OAAO,KAAK,WAAW,EACjC,MAAM,IAAI,EACV,MAAM,GAAG,EACT,KAAK,IAAI;AAAA,UAChB;AAAA,QAEF;AAAA,MACF,CAAC;AAGD,UAAI,UAAU,IAAI,OAAO;AAAA;AAAA,QAEvB,QAAQ,SAAS,MAAM;AAErB,eAAK,UAAU,IAAI,SAAS,MAAM;AAClC,eAAK,SAAS;AACd,eAAK,UAAU,CAAC;AAGhB,eAAK,MAAM,CAAC;AAGZ,cAAI,KAAK,OAAO,MAAM;AACpB,iBAAK,OAAO,KAAK;AACjB,iBAAK,KAAK,WAAW;AACrB,iBAAK,UAAU,KAAK,WAAW,CAAC;AAGhC,iBAAK,UAAU,KAAK,aAAa,QAAQ,KAAK,KAAK;AAAA,UACrD;AAAA,QACF;AAAA,QAGA,QAAQ;AAAA;AAAA,UAEN,GAAG,SAAS,GAAG;AACb,mBAAO,KAAK,KAAK,KAAK,CAAC;AAAA,UACzB;AAAA,UAEA,GAAG,SAAS,GAAG;AACb,mBAAO,KAAK,KAAK,KAAK,CAAC;AAAA,UACzB;AAAA,UAEA,IAAI,SAAS,GAAG;AACd,mBAAO,KAAK,OAAO,KAAK,EAAE,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,IAAI,KAAK,MAAM,IAAI,CAAC;AAAA,UAC9E;AAAA,UAEA,IAAI,SAAS,GAAG;AACd,mBAAO,KAAK,OAAO,KAAK,EAAE,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,EAAE,IAAI,KAAK,OAAO,IAAI,CAAC;AAAA,UAChF;AAAA,UAEA,MAAM,SAAS,GAAG,GAAG;AACnB,mBAAO,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;AAAA,UACtB;AAAA,UAEA,QAAQ,SAAS,GAAG,GAAG;AACrB,mBAAO,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC;AAAA,UACxB;AAAA,UAEA,OAAO,SAAS,OAAO;AACrB,mBAAO,KAAK,KAAK,SAAS,KAAK;AAAA,UACjC;AAAA,UAEA,QAAQ,SAAS,QAAQ;AACvB,mBAAO,KAAK,KAAK,UAAU,MAAM;AAAA,UACnC;AAAA,UAEA,MAAM,SAAS,OAAO,QAAQ;AAC5B,gBAAI,IAAI,iBAAiB,MAAM,OAAO,MAAM;AAE5C,mBAAO,KACJ,MAAM,IAAI,IAAI,OAAO,EAAE,KAAK,CAAC,EAC7B,OAAO,IAAI,IAAI,OAAO,EAAE,MAAM,CAAC;AAAA,UACpC;AAAA,UAEA,OAAO,SAAS,QAAQ;AAEtB,iBAAK,eAAe;AAGpB,gBAAI,QAAQ,YAAY,KAAK,KAAK,UAAU,IAAI,CAAC;AAGjD,gBAAG;AAAQ,qBAAO,IAAI,KAAK;AAAA;AACtB,mBAAK,MAAM,KAAK;AAErB,mBAAO;AAAA,UACT;AAAA,UAEA,QAAQ,WAAW;AACjB,gBAAI,KAAK,OAAO;AACd,mBAAK,OAAO,EAAE,cAAc,IAAI;AAElC,mBAAO;AAAA,UACT;AAAA,UAEA,SAAS,SAAS,SAAS;AACzB,iBAAK,MAAM,OAAO,EAAE,OAAO;AAE3B,mBAAO;AAAA,UACT;AAAA,UAEA,OAAO,SAAS,QAAQ;AACtB,mBAAO,OAAO,IAAI,IAAI;AAAA,UACxB;AAAA,UAEA,OAAO,SAAS,QAAQ;AACtB,mBAAO,OAAO,IAAI,IAAI;AAAA,UACxB;AAAA,UAEA,IAAI,SAAS,IAAI;AACf,mBAAO,KAAK,KAAK,MAAM,EAAE;AAAA,UAC3B;AAAA,UAEA,QAAQ,SAAS,GAAG,GAAG;AACrB,gBAAI,MAAM,KAAK,KAAK;AAEpB,mBAAO,IAAI,IAAI,KACR,IAAI,IAAI,KACR,IAAI,IAAI,IAAI,IAAI,SAChB,IAAI,IAAI,IAAI,IAAI;AAAA,UACzB;AAAA,UAEA,MAAM,WAAW;AACf,mBAAO,KAAK,MAAM,WAAW,EAAE;AAAA,UACjC;AAAA,UAEA,MAAM,WAAW;AACf,mBAAO,KAAK,MAAM,WAAW,MAAM;AAAA,UACrC;AAAA,UAEA,SAAS,WAAW;AAClB,mBAAO,KAAK,MAAM,SAAS,KAAK;AAAA,UAClC;AAAA,UAEA,UAAU,WAAW;AACnB,mBAAO,KAAK,KAAK,IAAI;AAAA,UACvB;AAAA,UAEA,SAAS,WAAW;AAClB,gBAAI,OAAO,KAAK,KAAK,OAAO;AAE5B,mBAAO,QAAQ,OAAO,CAAC,IAAI,KAAK,KAAK,EAAE,MAAM,IAAI,MAAM,SAAS;AAAA,UAClE;AAAA,UAEA,UAAU,SAAS,MAAM;AACvB,mBAAO,KAAK,QAAQ,EAAE,QAAQ,IAAI,KAAK;AAAA,UACzC;AAAA,UAEA,UAAU,SAAS,MAAM;AACvB,gBAAI,CAAC,KAAK,SAAS,IAAI,GAAG;AACxB,kBAAI,QAAQ,KAAK,QAAQ;AACzB,oBAAM,KAAK,IAAI;AACf,mBAAK,KAAK,SAAS,MAAM,KAAK,GAAG,CAAC;AAAA,YACpC;AAEA,mBAAO;AAAA,UACT;AAAA,UAEA,aAAa,SAAS,MAAM;AAC1B,gBAAI,KAAK,SAAS,IAAI,GAAG;AACvB,mBAAK,KAAK,SAAS,KAAK,QAAQ,EAAE,OAAO,SAAS,GAAG;AACnD,uBAAO,KAAK;AAAA,cACd,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA,YACd;AAEA,mBAAO;AAAA,UACT;AAAA,UAEA,aAAa,SAAS,MAAM;AAC1B,mBAAO,KAAK,SAAS,IAAI,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,SAAS,IAAI;AAAA,UAC1E;AAAA,UAEA,WAAW,SAAS,MAAM;AACxB,mBAAO,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC;AAAA,UAChC;AAAA,UAEA,QAAQ,SAAS,MAAM;AACrB,gBAAI,SAAS;AAGb,gBAAG,CAAC,OAAO,KAAK;AAAY,qBAAO;AAGnC,qBAAS,IAAI,MAAM,OAAO,KAAK,UAAU;AAEzC,gBAAG,CAAC;AAAM,qBAAO;AAGjB,mBAAM,UAAU,OAAO,gBAAgBF,QAAO,YAAW;AACvD,kBAAG,OAAO,SAAS,WAAW,OAAO,QAAQ,IAAI,IAAI,kBAAkB;AAAM,uBAAO;AACpF,kBAAG,CAAC,OAAO,KAAK,cAAc,OAAO,KAAK,WAAW,YAAY,eAAe,OAAO,KAAK,WAAW,YAAY;AAAsB,uBAAO;AAChJ,uBAAS,IAAI,MAAM,OAAO,KAAK,UAAU;AAAA,YAC3C;AAAA,UACF;AAAA,UAEA,KAAK,WAAW;AACd,mBAAO,gBAAgB,IAAI,MAAM,OAAO,KAAK,OAAO,IAAI,GAAG;AAAA,UAC7D;AAAA,UAEA,SAAS,SAAS,MAAM;AACtB,gBAAI,UAAU,CAAC,GAAG,SAAS;AAE3B,eAAE;AACA,uBAAS,OAAO,OAAO,IAAI;AAC3B,kBAAG,CAAC,UAAU,CAAC,OAAO;AAAM;AAE5B,sBAAQ,KAAK,MAAM;AAAA,YACrB,SAAQ,OAAO;AAEf,mBAAO;AAAA,UACT;AAAA,UAEA,SAAS,SAAS,UAAS;AACzB,mBAAO,QAAQ,KAAK,MAAM,QAAQ;AAAA,UACpC;AAAA,UAEA,QAAQ,WAAW;AACjB,mBAAO,KAAK;AAAA,UACd;AAAA,UAEA,KAAK,SAAS,KAAK;AAEjB,gBAAI,OAAOC,UAAS,cAAc,KAAK;AAGvC,gBAAI,OAAO,gBAAgB,IAAI,QAAQ;AAErC,mBAAK,YAAY,UAAU,IAAI,QAAQ,MAAM,EAAE,EAAE,QAAQ,0BAA0B,aAAa,IAAI;AAGpG,uBAASC,KAAI,GAAGC,MAAK,KAAK,WAAW,WAAW,QAAQD,KAAIC,KAAID;AAC9D,qBAAK,KAAK,YAAY,KAAK,WAAW,UAAU;AAAA,YAGpD,OAAO;AAEL,mBAAK,YAAY,MAAMD,UAAS,cAAc,KAAK,CAAC;AAGpD,mBAAK,eAAe;AAGpB,kBAAI,YAAY,KAAK,KAAK,UAAU,IAAI,CAAC;AAGzC,qBAAO,KAAK,UAAU,QAAQ,UAAU,EAAE,EAAE,QAAQ,YAAY,EAAE;AAAA,YACpE;AAEA,mBAAO;AAAA,UACT;AAAA,UAEA,gBAAgB,WAAW;AAGzB,gBAAG,KAAK,QAAQ,KAAK,OAAM;AACzB,kBAAI,KAAK,KAAK,OAAO,OAAO,KAAK,MAAM;AACvC,iBAAG,KAAK,WAAU;AAChB,qBAAK,eAAe;AAAA,cACtB,CAAC;AAAA,YACH;AAGA,iBAAK,KAAK,gBAAgB,YAAY;AAEtC,gBAAG,OAAO,KAAK,KAAK,GAAG,EAAE;AACvB,mBAAK,KAAK,aAAa,cAAc,KAAK,UAAU,KAAK,GAAG,CAAC;AAE/D,mBAAO;AAAA,UACT;AAAA,UAEA,SAAS,SAAS,GAAE;AAClB,iBAAK,MAAM;AACX,mBAAO;AAAA,UACT;AAAA,UACA,IAAI,SAAS,KAAI;AACf,mBAAO,GAAG,MAAM,GAAG;AAAA,UACrB;AAAA,QACF;AAAA,MACF,CAAC;AAED,UAAI,SAAS;AAAA,QACX,KAAK,SAAS,KAAI;AAAC,iBAAO;AAAA,QAAG;AAAA,QAC7B,MAAK,SAAS,KAAI;AAAC,iBAAO,CAAC,KAAK,IAAI,MAAM,KAAK,EAAE,IAAI,IAAI;AAAA,QAAG;AAAA,QAC5D,KAAK,SAAS,KAAI;AAAC,iBAAQ,KAAK,IAAI,MAAM,KAAK,KAAK,CAAC;AAAA,QAAC;AAAA,QACtD,KAAK,SAAS,KAAI;AAAC,iBAAO,CAAC,KAAK,IAAI,MAAM,KAAK,KAAK,CAAC,IAAI;AAAA,QAAC;AAAA,MAC5D;AAEA,UAAI,QAAQ,SAAS,KAAI;AACvB,eAAO,SAAS,MAAM,IAAI;AACxB,iBAAO,IAAI,IAAI,SAAS,MAAM,EAAE,EAAE,GAAG,GAAG;AAAA,QAC1C;AAAA,MACF;AAEA,UAAI,YAAY,IAAI,OAAO;AAAA,QAEzB,QAAQ,SAAS,GAAE;AACjB,eAAK,OAAO;AACZ,eAAK,WAAW;AAChB,eAAK,YAAY;AAEjB,eAAK,WAAW,IAAI,IAAI,OAAO,EAAE,QAAQ,EAAE,QAAQ;AACnD,eAAK,QAAQ,IAAI,IAAI,OAAO,EAAE,KAAK,EAAE,QAAQ;AAE7C,eAAK,QAAQ,CAAC,oBAAI,KAAK,IAAI,KAAK;AAChC,eAAK,SAAS,KAAK,QAAQ,KAAK;AAChC,eAAK,OAAO,EAAE;AAId,eAAK,OAAO;AACZ,eAAK,QAAQ;AAEb,eAAK,aAAa;AAAA;AAAA;AAAA,UAGlB;AAEA,eAAK,QAAQ;AAAA;AAAA;AAAA,UAGb;AAEA,eAAK,SAAS;AAAA;AAAA;AAAA,UAGd;AAEA,eAAK,aAAa;AAAA;AAAA;AAAA,UAGlB;AAEA,eAAK,OAAO;AAAA;AAAA;AAAA,UAGZ;AAAA,QAEF;AAAA,MAEF,CAAC;AAGD,UAAI,KAAK,IAAI,OAAO;AAAA,QAElB,QAAQ,SAAS,SAAS;AACxB,eAAK,UAAU;AACf,eAAK,aAAa,CAAC;AACnB,eAAK,SAAS;AACd,eAAK,YAAY;AACjB,eAAK,SAAS;AACd,eAAK,UAAU;AACf,eAAK,MAAM;AAGX,eAAK,SAAS;AACd,eAAK,SAAS;AAAA,QAChB;AAAA,QAEA,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UASN,SAAS,SAAS,GAAG,MAAM,OAAM;AAE/B,gBAAG,OAAO,KAAK,UAAS;AACtB,qBAAO,EAAE;AACT,sBAAQ,EAAE;AACV,kBAAI,EAAE;AAAA,YACR;AAEA,gBAAI,YAAY,IAAI,IAAI,UAAU;AAAA,cAChC,UAAU,KAAK;AAAA,cACf,OAAO,SAAS;AAAA,cAChB,MAAM,IAAI,OAAO,QAAQ,GAAG,KAAK;AAAA,YACnC,CAAC;AAED,iBAAK,MAAM,SAAS;AAEpB,mBAAO;AAAA,UACT;AAAA,UAOA,OAAO,SAAS,OAAM;AAGpB,gBAAI,YAAY,IAAI,IAAI,UAAU;AAAA,cAChC,UAAU;AAAA,cACV,OAAO;AAAA,cACP,MAAM,IAAI,OAAO,GAAG;AAAA,YACtB,CAAC;AAED,mBAAO,KAAK,MAAM,SAAS;AAAA,UAC7B;AAAA,UAOA,QAAQ,SAAS,QAAO;AACtB,gBAAG,UAAU,kBAAkB,IAAI,SAAQ;AACzC,mBAAK,UAAU;AACf,qBAAO;AAAA,YACT;AAEA,mBAAO,KAAK;AAAA,UACd;AAAA,UAGA,cAAc,SAAS,WAAU;AAC/B,oBAAQ,YAAY,KAAK,UAAU,UAAU,KAAK,UAAU,WAAS,KAAK;AAAA,UAC5E;AAAA,UAGA,cAAc,SAAS,QAAO;AAC5B,mBAAO,KAAK,UAAU,WAAS,KAAK,SAAS,SAAS,KAAK,UAAU;AAAA,UACvE;AAAA,UAGA,gBAAgB,WAAU;AACxB,iBAAK,cAAc;AACnB,iBAAK,iBAAiBD,QAAO,sBAAsB,WAAU;AAAE,mBAAK,KAAK;AAAA,YAAE,EAAE,KAAK,IAAI,CAAC;AAAA,UACzF;AAAA,UAGA,eAAe,WAAU;AACvB,YAAAA,QAAO,qBAAqB,KAAK,cAAc;AAAA,UACjD;AAAA,UAGA,OAAO,WAAU;AAEf,gBAAG,CAAC,KAAK,UAAU,KAAK,WAAU;AAChC,mBAAK,SAAS;AACd,mBAAK,aAAa;AAAA,YACpB;AAEA,mBAAO;AAAA,UACT;AAAA,UAGA,cAAc,WAAU;AACtB,iBAAK,UAAU,QAAQ,CAAC,oBAAI,SAAO,KAAK,UAAU,QAAM,KAAK;AAC7D,iBAAK,UAAU,SAAS,KAAK,UAAU,QAAQ,KAAK,UAAU,WAAS,KAAK;AAC5E,mBAAO,KAAK,eAAe,EAAE,KAAK;AAAA,UACpC;AAAA,UAOA,OAAO,SAAS,IAAG;AACjB,gBAAG,OAAO,MAAM,cAAc,cAAc,IAAI;AAC9C,mBAAK,WAAW,KAAK,EAAE;AAEzB,gBAAG,CAAC,KAAK;AAAW,mBAAK,YAAY,KAAK,WAAW,MAAM;AAE3D,mBAAO;AAAA,UACT;AAAA,UAMA,SAAS,WAAU;AAEjB,iBAAK,KAAK;AAGV,iBAAK,YAAY,KAAK,WAAW,MAAM;AAEvC,gBAAG,KAAK,WAAU;AAChB,kBAAG,KAAK,qBAAqB,IAAI,WAAW;AAC1C,qBAAK,MAAM;AAAA,cACb,OAAO;AAEL,qBAAK,UAAU,KAAK,IAAI;AAAA,cAC1B;AAAA,YACF;AAEA,mBAAO;AAAA,UACT;AAAA,UAIA,gBAAgB,WAAW;AACzB,gBAAIE,IAAG,GAAG;AACV,gBAAI,IAAI,KAAK;AAEb,gBAAG,EAAE;AAAM,qBAAO;AAElB,iBAAIA,MAAK,EAAE,YAAW;AACpB,uBAAS,KAAK,OAAO,EAAEA,EAAC,EAAE;AAE1B,kBAAG,CAAC,MAAM,QAAQ,MAAM,GAAG;AACzB,yBAAS,CAAC,MAAM;AAAA,cAClB;AAEA,kBAAG,CAAC,MAAM,QAAQ,EAAE,WAAWA,EAAC,CAAC,GAAG;AAClC,kBAAE,WAAWA,EAAC,IAAI,CAAC,EAAE,WAAWA,EAAC,CAAC;AAAA,cACpC;AAMA,mBAAI,IAAI,OAAO,QAAQ,OAAM;AAG3B,oBAAG,EAAE,WAAWA,EAAC,EAAE,CAAC,aAAa,IAAI;AACnC,yBAAO,CAAC,IAAI,IAAI,IAAI,OAAO,OAAO,CAAC,CAAC;AAEtC,kBAAE,WAAWA,EAAC,EAAE,CAAC,IAAI,OAAO,CAAC,EAAE,MAAM,EAAE,WAAWA,EAAC,EAAE,CAAC,CAAC;AAAA,cACzD;AAAA,YACF;AAEA,iBAAIA,MAAK,EAAE,OAAM;AACf,gBAAE,MAAMA,EAAC,IAAI,IAAI,IAAI,SAAS,KAAK,OAAO,EAAE,KAAKA,EAAC,GAAG,EAAE,MAAMA,EAAC,CAAC;AAAA,YACjE;AAEA,iBAAIA,MAAK,EAAE,QAAO;AAChB,gBAAE,OAAOA,EAAC,IAAI,IAAI,IAAI,SAAS,KAAK,OAAO,EAAE,MAAMA,EAAC,GAAG,EAAE,OAAOA,EAAC,CAAC;AAAA,YACpE;AAEA,cAAE,wBAAwB,KAAK,OAAO,EAAE,UAAU;AAElD,cAAE,OAAO;AACT,mBAAO;AAAA,UACT;AAAA,UACA,YAAY,WAAU;AACpB,iBAAK,aAAa,CAAC;AACnB,mBAAO;AAAA,UACT;AAAA,UACA,cAAc,WAAU;AACtB,iBAAK,YAAY;AACjB,mBAAO;AAAA,UACT;AAAA,UAMA,MAAM,SAAS,WAAW,YAAW;AACnC,gBAAI,SAAS,KAAK;AAClB,iBAAK,SAAS;AAEd,gBAAG,YAAW;AACZ,mBAAK,WAAW;AAAA,YAClB;AAEA,gBAAG,aAAa,KAAK,WAAU;AAE7B,eAAC,UAAU,KAAK,aAAa;AAC7B,mBAAK,MAAM;AAAA,YACb;AAEA,iBAAK,cAAc;AAEnB,mBAAO,KAAK,aAAa;AAAA,UAC3B;AAAA,UAKA,OAAO,WAAU;AACf,gBAAG,KAAK,WAAU;AAChB,kBAAI,OAAO,KAAK;AAChB,mBAAK,KAAK;AACV,mBAAK,YAAY;AACjB,mBAAK,QAAQ;AAAA,YACf;AACA,mBAAO;AAAA,UACT;AAAA,UAGA,QAAQ,WAAU;AAEhB,iBAAK,KAAK,MAAM,KAAK;AAErB,mBAAM,KAAK,QAAQ,EAAE,aAAa,KAAK,KAAK,MAAM,KAAK;AAAE;AAEzD,iBAAK,WAAW,EAAE,aAAa;AAE/B,mBAAO;AAAA,UACT;AAAA,UAGA,SAAS,WAAW;AAClB,mBAAO,KAAK,GAAG,GAAG,IAAI;AAAA,UACxB;AAAA,UAGA,OAAO,WAAW;AAChB,gBAAI,KAAK,UAAU,UAAU,MAAM;AAEjC,mBAAK,UAAU,QAAQ,KAAK,UAAU,OAAO;AAAA,YAC/C;AAEA,gBAAG,OAAO,KAAK,UAAU,SAAS,UAAU;AAE1C,qBAAO,KAAK,GAAG,KAAK,UAAU,OAAO,IAAI;AAAA,YAC3C,OAAO;AAEL,qBAAO,KAAK,GAAG,GAAG,IAAI;AAAA,YACxB;AAAA,UACF;AAAA,UAIA,IAAI,SAAS,KAAK,UAAS;AACzB,gBAAI,YAAY,KAAK,UAAU,WAAS,KAAK;AAE7C,iBAAK,SAAS;AAEd,gBAAI,CAAC,UAAU;AACb,kBAAI,KAAK,UAAU;AAAU,qBAAK,SAAS,IAAI,KAAK;AACpD,mBAAK,UAAU,KAAK,UAAU;AAAA,YAChC;AAEA,iBAAK,UAAU,QAAQ,CAAC,oBAAI,SAAO,KAAK,SAAS;AACjD,iBAAK,UAAU,SAAS,KAAK,UAAU,QAAQ;AAE/C,mBAAO,KAAK,KAAK,IAAI;AAAA,UACvB;AAAA,UAOA,OAAO,SAAS,OAAM;AACpB,gBAAI,UAAU;AAAG,qBAAO,KAAK,MAAM;AAEnC,gBAAI,OAAO;AACT,mBAAK,SAAS;AAEd,qBAAO,KAAK,GAAG,KAAK,QAAQ,IAAI;AAAA,YAClC;AAAO,qBAAO,KAAK;AAAA,UACrB;AAAA,UAGA,MAAM,SAAS,OAAO,SAAS;AAC7B,gBAAI,IAAI,KAAK,KAAK;AAGlB,cAAE,QAAS,SAAS,OAAQ,QAAQ;AACpC,cAAE,OAAO;AAET,gBAAG;AAAS,gBAAE,YAAY;AAC1B,mBAAO;AAAA,UACT;AAAA,UAGA,OAAO,WAAU;AACf,iBAAK,SAAS;AACd,iBAAK,cAAc;AAEnB,mBAAO;AAAA,UACT;AAAA,UAGA,MAAM,WAAU;AACd,gBAAG,CAAC,KAAK;AAAQ,qBAAO;AACxB,iBAAK,SAAS;AAEd,mBAAO,KAAK,GAAG,KAAK,QAAQ,IAAI;AAAA,UAClC;AAAA,UAQA,SAAS,SAAS,UAAS;AACzB,gBAAI,IAAI,KAAK,KAAK;AAElB,gBAAG,OAAO,YAAY;AAAa,gBAAE,WAAW,CAAC,EAAE;AAAA;AAC9C,gBAAE,WAAW;AAElB,mBAAO;AAAA,UACT;AAAA,UAQA,UAAU,SAAS,QAAO;AACxB,mBAAO,SAAS,KAAK,UAAU,KAAK,KAAK,GAAG,IAAI,KAAK;AAAA,UACvD;AAAA,UAOA,OAAO,SAAS,IAAG;AACjB,gBAAI,IAAI,KAAK,KAAK,GACd,UAAU,SAASE,SAAQ,GAAE;AAC3B,kBAAG,EAAE,OAAO,aAAa,GAAE;AACzB,mBAAG,KAAK,MAAM,CAAC;AACf,qBAAK,IAAI,eAAeA,QAAO;AAAA,cACjC;AAAA,YACF;AAEJ,iBAAK,OAAO,EAAE,GAAG,eAAe,OAAO;AAEvC,mBAAO,KAAK,WAAW;AAAA,UACzB;AAAA,UAGA,QAAQ,SAAS,IAAG;AAClB,gBAAI,IAAI,KAAK,KAAK,GACd,UAAU,SAAS,GAAE;AACnB,kBAAG,EAAE,OAAO,aAAa,GAAE;AACzB,mBAAG,KAAK,MAAM,EAAE,OAAO,KAAK,IAAI,MAAM,EAAE,OAAO,GAAG,GAAG,EAAE,OAAO,OAAO,CAAC;AAAA,cACxE;AAAA,YACF;AAGJ,iBAAK,OAAO,EAAE,IAAI,aAAa,OAAO,EAAE,GAAG,aAAa,OAAO;AAE/D,iBAAK,MAAM,WAAU;AACnB,mBAAK,IAAI,aAAa,OAAO;AAAA,YAC/B,CAAC;AAED,mBAAO,KAAK,WAAW;AAAA,UACzB;AAAA,UAGA,UAAU,SAAS,IAAG;AACpB,gBAAI,UAAU,SAASA,SAAQ,GAAE;AAC3B,iBAAG,KAAK,IAAI;AACZ,mBAAK,IAAI,kBAAkBA,QAAO;AAAA,YACpC;AAGJ,iBAAK,OAAO,EAAE,IAAI,kBAAkB,OAAO,EAAE,GAAG,kBAAkB,OAAO;AAEzE,mBAAO,KAAK,WAAW;AAAA,UACzB;AAAA,UAGA,WAAW,SAAS,IAAG;AACrB,gBAAI,UAAU,SAAS,GAAE;AACnB,iBAAG,KAAK,MAAM,EAAE,OAAO,KAAK,IAAI,MAAM,EAAE,OAAO,GAAG,GAAG,EAAE,OAAO,OAAO,EAAE,OAAO,SAAS;AAAA,YACzF;AAEJ,iBAAK,OAAO,EAAE,IAAI,aAAa,OAAO,EAAE,GAAG,aAAa,OAAO;AAE/D,iBAAK,SAAS,WAAU;AACtB,mBAAK,IAAI,aAAa,OAAO;AAAA,YAC/B,CAAC;AAED,mBAAO,KAAK,WAAW;AAAA,UACzB;AAAA,UAEA,MAAM,WAAU;AACd,mBAAO,KAAK,WAAW,SAAS,KAAK,WAAW,KAAK,WAAW,SAAO,CAAC,IAAI,KAAK;AAAA,UACnF;AAAA,UAGA,KAAK,SAAS,QAAQ,MAAM,MAAK;AAC/B,iBAAK,KAAK,EAAE,QAAQ,YAAY,EAAE,MAAM,IAAI;AAC5C,mBAAO,KAAK,WAAW;AAAA,UACzB;AAAA,UAMA,MAAM,SAAS,YAAW;AAGxB,gBAAG,CAAC;AAAY,mBAAK,SAAS,KAAK,aAAa,CAAC,oBAAI,MAAI;AAGzD,gBAAG,KAAK,UAAU,UAAU,OAAO;AACjC,kBAAI,QAAQ,WAAW;AAGvB,uBAAS,KAAK,IAAI,KAAK,QAAQ,CAAC;AAChC,0BAAY,KAAK,MAAM,MAAM;AAE7B,kBAAG,KAAK,UAAU,UAAU,QAAQ,YAAY,KAAK,UAAU,OAAO;AACpE,qBAAK,MAAM,SAAS;AACpB,2BAAW,KAAK,UAAU;AAC1B,qBAAK,UAAU,OAAO;AAAA,cACxB,OAAO;AACL,qBAAK,SAAS,KAAK,UAAU;AAC7B,qBAAK,MAAM;AAEX,2BAAW,KAAK,UAAU,OAAO;AACjC,qBAAK,UAAU,OAAO,KAAK,UAAU;AAAA,cACvC;AAEA,kBAAG,KAAK,UAAU,WAAW;AAE3B,qBAAK,UAAU,WAAW,KAAK,UAAU,YAAY,SAAS,KAAK,UAAU,OAAO,YAAY,CAAC;AAAA,cACnG;AAAA,YAEF,OAAO;AAEL,mBAAK,SAAS,KAAK,IAAI,KAAK,QAAQ,CAAC;AACrC,mBAAK,MAAM,KAAK;AAAA,YAClB;AAGA,gBAAG,KAAK,MAAM;AAAG,mBAAK,MAAM;AAE5B,gBAAG,KAAK,UAAU;AAAU,mBAAK,MAAM,IAAI,KAAK;AAIhD,gBAAI,QAAQ,KAAK,UAAU,KAAK,KAAK,GAAG;AAGxC,qBAAQF,MAAK,KAAK,UAAU,MAAK;AAC/B,kBAAGA,KAAI,KAAK,WAAWA,MAAK,OAAM;AAChC,qBAAK,UAAU,KAAKA,EAAC,EAAE,KAAK,KAAK,OAAO,GAAG,KAAK,KAAK,KAAK;AAC1D,uBAAO,KAAK,UAAU,KAAKA,EAAC;AAAA,cAC9B;AAAA,YACF;AAGA,gBAAG,KAAK;AAAQ,mBAAK,OAAO,EAAE,KAAK,UAAU,EAAC,KAAK,KAAK,KAAK,OAAc,IAAI,MAAM,WAAW,KAAK,UAAS,CAAC;AAI/G,gBAAG,CAAC,KAAK,WAAU;AACjB,qBAAO;AAAA,YACT;AAGA,iBAAK,OAAO;AAGZ,gBAAI,KAAK,OAAO,KAAK,CAAC,KAAK,UAAU,YAAc,KAAK,UAAU,YAAY,KAAK,OAAO,GAAG;AAG3F,mBAAK,cAAc;AAGnB,mBAAK,OAAO,EAAE,KAAK,YAAY,EAAC,IAAG,MAAM,WAAW,KAAK,UAAS,CAAC;AAEnE,kBAAG,CAAC,KAAK,WAAW,QAAO;AACzB,qBAAK,OAAO,EAAE,KAAK,aAAa;AAGhC,oBAAG,CAAC,KAAK,WAAW,QAAO;AACzB,uBAAK,OAAO,EAAE,IAAI,KAAK;AACvB,uBAAK,SAAS;AAAA,gBAChB;AAAA,cACF;AAGA,kBAAG,KAAK;AAAQ,qBAAK,QAAQ;AAAA;AACxB,qBAAK,aAAa;AAAA,YAEzB,WAAS,CAAC,KAAK,UAAU,KAAK,QAAO;AAEnC,mBAAK,eAAe;AAAA,YACtB;AAGA,iBAAK,UAAU;AACf,mBAAO;AAAA,UAET;AAAA,UAGA,QAAQ,WAAU;AAChB,gBAAIA,IAAG,KAAK,IAAI,OAAO,MAAM,SAAS,KAAK,OAAO,GAAG,IAAI,KAAK;AAG9D,iBAAIA,MAAK,EAAE,YAAW;AAEpB,mBAAK,CAAC,EAAE,OAAO,EAAE,WAAWA,EAAC,CAAC,EAAE,IAAI,SAAS,IAAG;AAC9C,uBAAO,OAAO,OAAO,YAAY,GAAG,KAAK,GAAG,GAAG,EAAE,KAAK,KAAK,GAAG,GAAG,KAAK,GAAG,IAAI;AAAA,cAC/E,CAAC;AAED,qBAAOA,EAAC,EAAE,MAAM,QAAQ,EAAE;AAAA,YAE5B;AAGA,iBAAIA,MAAK,EAAE,OAAM;AAEf,mBAAK,CAACA,EAAC,EAAE,OAAO,EAAE,MAAMA,EAAC,CAAC,EAAE,IAAI,SAAS,IAAG;AAC1C,uBAAO,OAAO,OAAO,YAAY,GAAG,KAAK,GAAG,GAAG,EAAE,KAAK,KAAK,GAAG,GAAG,KAAK,GAAG,IAAI;AAAA,cAC/E,CAAC;AAED,qBAAO,KAAK,MAAM,QAAQ,EAAE;AAAA,YAE9B;AAGA,iBAAIA,MAAK,EAAE,QAAO;AAEhB,mBAAK,CAACA,EAAC,EAAE,OAAO,EAAE,OAAOA,EAAC,CAAC,EAAE,IAAI,SAAS,IAAG;AAC3C,uBAAO,OAAO,OAAO,YAAY,GAAG,KAAK,GAAG,GAAG,EAAE,KAAK,KAAK,GAAG,GAAG,KAAK,GAAG,IAAI;AAAA,cAC/E,CAAC;AAED,qBAAO,MAAM,MAAM,QAAQ,EAAE;AAAA,YAE/B;AAGA,gBAAG,EAAE,WAAW,QAAO;AAGrB,mBAAK,EAAE;AACP,mBAAIA,KAAI,GAAG,MAAM,EAAE,WAAW,QAAQA,KAAI,KAAKA,MAAI;AAGjD,oBAAI,IAAI,EAAE,WAAWA,EAAC;AAGtB,oBAAG,aAAa,IAAI,QAAO;AAEzB,sBAAG,EAAE,UAAS;AACZ,yBAAK,GAAG,SAAS,IAAI,IAAI,OAAO,EAAE,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,KAAK,GAAG,CAAC,CAAC;AAAA,kBACjE,OAAK;AACH,yBAAK,GAAG,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,KAAK,GAAG,CAAC;AAAA,kBACtC;AACA;AAAA,gBACF;AAGA,oBAAG,CAAC,EAAE;AACJ,oBAAE,KAAK,GAAG,QAAQ,CAAC;AAGrB,qBAAK,GAAG,SAAS,EAAE,GAAG,EAAE,KAAK,KAAK,GAAG,CAAC,CAAC;AAAA,cAEzC;AAGA,qBAAO,OAAO,EAAE;AAAA,YAClB;AAEA,mBAAO;AAAA,UAET;AAAA,UAIA,MAAM,SAAS,KAAK,IAAI,SAAQ;AAC9B,gBAAI,IAAI,KAAK,KAAK;AAClB,gBAAG,CAAC;AAAS,oBAAM,EAAE,KAAK,GAAG;AAE7B,cAAE,KAAK,GAAG,IAAI;AAEd,mBAAO;AAAA,UACT;AAAA,UAEA,YAAY,WAAW;AACrB,uBAAW,WAAU;AAAC,mBAAK,MAAM;AAAA,YAAC,EAAE,KAAK,IAAI,GAAG,CAAC;AACjD,mBAAO;AAAA,UACT;AAAA,QAEF;AAAA,QAEA,QAAQ,IAAI;AAAA,QAGZ,WAAW;AAAA;AAAA,UAET,SAAS,SAAS,GAAG,MAAM,OAAO;AAChC,oBAAQ,KAAK,OAAO,KAAK,KAAK,IAAI,IAAI,GAAG,IAAI,IAAI,QAAQ,GAAG,MAAM,KAAK;AAAA,UACzE;AAAA,UACA,OAAO,SAAS,OAAM;AACpB,oBAAQ,KAAK,OAAO,KAAK,KAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,KAAK;AAAA,UAC9D;AAAA,UACA,MAAM,SAAS,WAAW,YAAY;AACpC,gBAAI,KAAK;AACP,mBAAK,GAAG,KAAK,WAAW,UAAU;AAEpC,mBAAO;AAAA,UACT;AAAA,UACA,QAAQ,WAAW;AACjB,gBAAI,KAAK;AACP,mBAAK,GAAG,OAAO;AAEjB,mBAAO;AAAA,UACT;AAAA,UAEA,OAAO,WAAW;AAChB,gBAAI,KAAK;AACP,mBAAK,GAAG,MAAM;AAEhB,mBAAO;AAAA,UACT;AAAA,UAEA,MAAM,WAAW;AACf,gBAAI,KAAK;AACP,mBAAK,GAAG,KAAK;AAEf,mBAAO;AAAA,UACT;AAAA,UAEA,OAAO,SAAS,OAAO;AACrB,gBAAI,KAAK;AACP,kBAAI,SAAS;AACX,uBAAO,KAAK,GAAG,MAAM;AAAA;AAErB,qBAAK,GAAG,MAAM,KAAK;AAEvB,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MAEF,CAAC;AAGD,UAAI,WAAW,IAAI,OAAO;AAAA,QAExB,QAAQ,SAAS,MAAM,IAAG;AAExB,cAAG,IAAI,MAAM,QAAQ,EAAE;AAAG,mBAAO,IAAI,IAAI,MAAM,IAAI,EAAE,MAAM,EAAE;AAE7D,cAAG,IAAI,MAAM,UAAU,KAAK,IAAI,GAAG;AAEjC,gBAAG,IAAI,MAAM,YAAY,KAAK,IAAI;AAAG,qBAAO,IAAI,IAAI,UAAU,IAAI,EAAE,MAAM,EAAE;AAAA;AAEvE,qBAAO,IAAI,IAAI,MAAM,IAAI,EAAE,MAAM,EAAE;AAAA,UAC1C;AAEA,cAAG,IAAI,MAAM,cAAc,KAAK,EAAE;AAAG,mBAAO,IAAI,IAAI,OAAO,IAAI,EAAE,MAAM,EAAE;AAGzE,eAAK,QAAQ;AACb,eAAK,cAAc;AAAA,QACrB;AAAA,QAEA,QAAQ;AAAA,UACN,IAAI,SAAS,KAAK,MAAK;AACrB,mBAAO,OAAO,IAAI,KAAK,QAAQ,KAAK;AAAA,UACtC;AAAA,UAEA,SAAS,WAAU;AACjB,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAAA,MAEF,CAAC;AAED,UAAI,OAAO,IAAI,IAAI;AAAA;AAAA,QAEjB,MAAM,SAAS,GAAG,GAAG,UAAU;AAE7B,cAAI,OAAO,KAAK,UAAU;AACxB,qBAAS,OAAO;AACd,mBAAK,KAAK,KAAK,EAAE,GAAG,CAAC;AAAA,UAEzB,OAAO;AACL,iBAAK,IAAI,GAAG,GAAG,OAAO;AAAA,UACxB;AAEA,iBAAO;AAAA,QACT;AAAA,QAEA,OAAO,SAAS,GAAG,GAAG;AACpB,cAAI,OAAO,KAAK;AACd,qBAAS,OAAO;AACd,mBAAK,MAAM,KAAK,EAAE,GAAG,CAAC;AAAA;AAGxB,iBAAK,IAAI,GAAG,GAAG,QAAQ;AAEzB,iBAAO;AAAA,QACT;AAAA,QAEA,GAAG,SAAS,GAAG,UAAU;AACvB,cAAG,KAAK,OAAO,aAAa,IAAI,GAAE;AAChC,iBAAK,UAAU,EAAC,EAAG,GAAG,QAAQ;AAC9B,mBAAO;AAAA,UACT;AAEA,cAAI,MAAM,IAAI,IAAI,OAAO,CAAC;AAC1B,cAAI,WAAW;AACf,iBAAO,KAAK,IAAI,KAAK,GAAG;AAAA,QAC1B;AAAA,QAEA,GAAG,SAAS,GAAG,UAAU;AACvB,cAAG,KAAK,OAAO,aAAa,IAAI,GAAE;AAChC,iBAAK,UAAU,EAAC,EAAG,GAAG,QAAQ;AAC9B,mBAAO;AAAA,UACT;AAEA,cAAI,MAAM,IAAI,IAAI,OAAO,CAAC;AAC1B,cAAI,WAAW;AACf,iBAAO,KAAK,IAAI,KAAK,GAAG;AAAA,QAC1B;AAAA,QAEA,IAAI,SAAS,GAAG;AACd,iBAAO,KAAK,IAAI,MAAM,IAAI,IAAI,OAAO,CAAC,CAAC;AAAA,QACzC;AAAA,QAEA,IAAI,SAAS,GAAG;AACd,iBAAO,KAAK,IAAI,MAAM,IAAI,IAAI,OAAO,CAAC,CAAC;AAAA,QACzC;AAAA,QAEA,MAAM,SAAS,GAAG,GAAG;AACnB,iBAAO,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;AAAA,QACtB;AAAA,QAEA,QAAQ,SAAS,GAAG,GAAG;AACrB,iBAAO,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC;AAAA,QACxB;AAAA,QAEA,MAAM,SAAS,OAAO,QAAQ;AAC5B,cAAI,KAAK,OAAO,aAAa,IAAI,MAAM;AAErC,iBAAK,KAAK,aAAa,KAAK;AAAA,UAE9B,OAAO;AAEL,gBAAI;AAEJ,gBAAG,CAAC,SAAS,CAAC,QAAO;AACnB,oBAAM,KAAK,OAAO,EAAE,KAAK;AAAA,YAC3B;AAEA,gBAAG,CAAC,OAAM;AACR,sBAAQ,IAAI,QAAQ,IAAI,SAAU;AAAA,YACpC;AAEA,gBAAG,CAAC,QAAO;AACT,uBAAS,IAAI,SAAS,IAAI,QAAS;AAAA,YACrC;AAEA,iBAAK,IAAI,SAAU,IAAI,IAAI,OAAO,KAAK,CAAC,EACnC,IAAI,UAAU,IAAI,IAAI,OAAO,MAAM,CAAC;AAAA,UAE3C;AAEA,iBAAO;AAAA,QACT;AAAA,QAEA,OAAO,SAAS,OAAO;AACrB,iBAAO,KAAK,IAAI,SAAS,IAAI,IAAI,OAAO,KAAK,CAAC;AAAA,QAChD;AAAA,QAEA,QAAQ,SAAS,QAAQ;AACvB,iBAAO,KAAK,IAAI,UAAU,IAAI,IAAI,OAAO,MAAM,CAAC;AAAA,QAClD;AAAA,QAEA,MAAM,SAAS,GAAG,GAAG,GAAG,GAAG;AAEzB,cAAG,UAAU,UAAU,GAAG;AACxB,mBAAO,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,UAC/B;AAEA,iBAAO,KAAK,IAAI,QAAQ,KAAK,KAAK,OAAO,GAAE,WAAY,CAAC,CAAC;AAAA,QAC3D;AAAA,QAEA,SAAS,SAAS,OAAO;AACvB,iBAAO,KAAK,OAAO,EAAE,UACnB,KAAK,IAAI,WAAW,IAAI,IAAI,OAAO,KAAK,CAAC,IACzC;AAAA,QACJ;AAAA,QAEA,SAAS,SAAS,GAAG,GAAG,OAAO,QAAQ;AACrC,cAAI,KAAK,OAAO,aAAa,IAAI,WAAW;AAC1C,iBAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,GAAG,GAAG,OAAO,MAAM,CAAC;AAAA,UAC1D;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,QAAQ,SAAS,GAAG;AAClB,cAAI,KAAK,OAAO,aAAa,IAAI,MAAM;AACrC,gBAAI,OAAO,KAAK,YAAY,aAAa,IAAI,QAAQ;AACnD,qBAAO,KAAK,OAAO;AAAA,gBACjB,QAAS,UAAU,CAAC;AAAA,gBACpB,OAAS,UAAU,CAAC;AAAA,gBACpB,SAAS,UAAU,CAAC;AAAA,cACtB,CAAC;AAAA,YACH;AAEA,gBAAI,EAAE,WAAW;AAAM,mBAAK,KAAK,gBAAgB,EAAE,OAAO;AAC1D,gBAAI,EAAE,SAAW;AAAM,mBAAK,KAAK,cAAc,EAAE,KAAK;AACtD,gBAAI,EAAE,UAAW;AAAM,mBAAK,KAAK,UAAU,EAAE,MAAM;AAAA,UACrD;AAEA,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAED,UAAI,MAAM,IAAI,OAAO;AAAA,QACnB,QAAQ,SAAS,GAAG,GAAG,OAAO,QAAQ;AACpC,cAAI,OAAO,KAAK,YAAY,EAAE,aAAa,IAAI,UAAU;AAEvD,mBAAO,IAAI,IAAI,KAAK,MAAM,EAAE,QAAQ,OAAO,EAAE,OAAO,EAAE,GAAI,EAAE,OAAO,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM;AAAA,UAC1G,WAAW,UAAU,UAAU,GAAG;AAChC,iBAAK,IAAI;AACT,iBAAK,IAAI;AACT,iBAAK,QAAQ;AACb,iBAAK,SAAS;AAAA,UAChB;AAGA,kBAAQ,IAAI;AAAA,QACd;AAAA,QACA,QAAQ;AAAA;AAAA,UAEN,OAAO,SAAS,KAAK;AACnB,gBAAI,IAAI,IAAI,KAAK,YAAY;AAG7B,cAAE,IAAS,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC;AACjC,cAAE,IAAS,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC;AACjC,cAAE,QAAS,KAAK,IAAI,KAAK,IAAI,KAAK,OAAQ,IAAI,IAAI,IAAI,KAAK,IAAK,EAAE;AAClE,cAAE,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,QAAQ,IAAI,IAAI,IAAI,MAAM,IAAI,EAAE;AAElE,mBAAO,QAAQ,CAAC;AAAA,UAClB;AAAA,UAEA,WAAW,SAAS,GAAG;AACrB,gBAAI,OAAO,UAAU,OAAO,WAAW,OAAO,UAAU,OAAO,WAAW,GAAG;AAE7E,gBAAI,MAAM;AAAA,cACR,IAAI,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC;AAAA,cAC5B,IAAI,IAAI,MAAM,KAAK,IAAI,KAAK,CAAC;AAAA,cAC7B,IAAI,IAAI,MAAM,KAAK,GAAG,KAAK,EAAE;AAAA,cAC7B,IAAI,IAAI,MAAM,KAAK,IAAI,KAAK,EAAE;AAAA,YAChC;AAEA,gBAAI,QAAQ,SAASG,IAAG;AACtB,cAAAA,KAAIA,GAAE,UAAU,CAAC;AACjB,qBAAO,KAAK,IAAI,MAAKA,GAAE,CAAC;AACxB,qBAAO,KAAK,IAAI,MAAKA,GAAE,CAAC;AACxB,qBAAO,KAAK,IAAI,MAAKA,GAAE,CAAC;AACxB,qBAAO,KAAK,IAAI,MAAKA,GAAE,CAAC;AAAA,YAC1B,CAAC;AAED,mBAAO,IAAI,KAAK,YAAY;AAC5B,iBAAK,IAAI;AACT,iBAAK,QAAQ,OAAK;AAClB,iBAAK,IAAI;AACT,iBAAK,SAAS,OAAK;AAEnB,oBAAQ,IAAI;AAEZ,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF,CAAC;AAED,UAAI,OAAO,IAAI,OAAO;AAAA;AAAA,QAEpB,QAAQ,SAAS,SAAS;AACxB,cAAI,IAAI,MAAM,MAAM,CAAC,EAAE,MAAM,KAAK,SAAS,CAAC;AAG5C,cAAI,mBAAmB,IAAI,SAAS;AAClC,gBAAI;AAGJ,gBAAI;AAEF,kBAAI,CAACJ,UAAS,gBAAgB,UAAS;AAErC,oBAAI,YAAY,QAAQ;AACxB,uBAAO,UAAU,YAAW;AAC1B,8BAAY,UAAU;AAAA,gBACxB;AACA,oBAAI,aAAaA;AAAU,wBAAM,IAAI,UAAU,wBAAwB;AAAA,cACzE,OAAO;AAEL,oBAAG,CAACA,UAAS,gBAAgB,SAAS,QAAQ,IAAI;AAAG,wBAAM,IAAI,UAAU,wBAAwB;AAAA,cACnG;AAGA,oBAAM,QAAQ,KAAK,QAAQ;AAAA,YAC7B,SAAQ,GAAG;AACT,kBAAG,mBAAmB,IAAI,OAAM;AAC9B,oBAAI,QAAQ,QAAQ,MAAM,IAAI,OAAO,KAAK,QAAQ,EAAE,KAAK;AACzD,sBAAM,MAAM,KAAK,QAAQ;AACzB,sBAAM,OAAO;AAAA,cACf,OAAK;AACH,sBAAM;AAAA,kBACJ,GAAQ,QAAQ,KAAK;AAAA,kBACrB,GAAQ,QAAQ,KAAK;AAAA,kBACrB,OAAQ,QAAQ,KAAK;AAAA,kBACrB,QAAQ,QAAQ,KAAK;AAAA,gBACvB;AAAA,cACF;AAAA,YACF;AAEA,gBAAI,IAAI,KAAK,MAAM,GAAG;AAAA,UACxB;AAAA,QAEF;AAAA,QAGA,SAAS,IAAI;AAAA,QAGb,QAAQ,IAAI;AAAA,QAGZ,WAAW;AAAA;AAAA,UAET,MAAM,WAAW;AACf,mBAAO,IAAI,IAAI,KAAK,IAAI;AAAA,UAC1B;AAAA,QACF;AAAA,MAEF,CAAC;AAED,UAAI,KAAK,UAAU,cAAc,IAAI;AAGrC,UAAI,OAAO,IAAI,SAAS;AAAA,QACtB,MAAM,WAAU;AACd,kBAAQ,KAAK,oEAAoE;AACjF,iBAAO,KAAK,KAAK,KAAK,IAAI,CAAC;AAAA,QAC7B;AAAA,MACF,CAAC;AAED,UAAI,OAAO,IAAI,OAAO;AAAA;AAAA,QAEpB,QAAQ,SAAS,SAAS;AACxB,cAAI,IAAI,MAAM,MAAM,CAAC,EAAE,MAAM,KAAK,SAAS,CAAC;AAE5C,cAAI,mBAAmB,IAAI,SAAS;AAClC,gBAAI,IAAI,KAAK,MAAM,QAAQ,KAAK,sBAAsB,CAAC;AAAA,UACzD;AAAA,QACF;AAAA,QAEA,SAAS,IAAI;AAAA,QAGb,QAAQ,IAAI;AAAA,QAEZ,QAAQ;AAAA,UACN,WAAW,WAAW;AAEpB,iBAAK,KAAKD,QAAO;AACjB,iBAAK,KAAKA,QAAO;AACjB,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,QAGA,WAAW;AAAA;AAAA,UAET,MAAM,SAAS,IAAI;AACjB,gBAAI;AAAI,qBAAO,IAAI,IAAI,KAAK,IAAI,EAAE,UAAU,GAAG,UAAU,EAAE,QAAQ,CAAC;AACpE,mBAAO,IAAI,IAAI,KAAK,IAAI,EAAE,UAAU;AAAA,UACtC;AAAA,QACF;AAAA,MAEF,CAAC;AAED,UAAI,KAAK,UAAU,cAAc,IAAI;AAErC,UAAI,SAAS,IAAI,OAAO;AAAA;AAAA,QAEtB,QAAQ,SAAS,QAAQ;AACvB,cAAIE,IAAG,OAAO,cAAc,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AAG9C,mBAAS,kBAAkB,IAAI,UAC7B,OAAO,UAAU,IACnB,OAAO,WAAW,WAChB,cAAc,OAAO,MAAM,IAAI,MAAM,SAAS,EAAE,IAAI,UAAU,CAAC,IACjE,UAAU,UAAU,IAClB,cAAc,CAAC,EAAE,MAAM,KAAK,SAAS,CAAC,IACxC,MAAM,QAAQ,MAAM,IAClB,cAAc,MAAM,IACtB,OAAO,WAAW,WAChB,SAAS;AAGX,eAAKA,KAAI,OAAO,SAAS,GAAGA,MAAK,GAAG,EAAEA;AACpC,iBAAK,OAAOA,EAAC,CAAC,IAAI,OAAO,OAAOA,EAAC,CAAC,KAAK,OACrC,OAAO,OAAOA,EAAC,CAAC,IAAI,KAAK,OAAOA,EAAC,CAAC;AAAA,QACxC;AAAA,QAGA,QAAQ;AAAA;AAAA,UAEN,SAAS,WAAW;AAElB,gBAAI,KAAQ,oBAAoB,MAAM,GAAG,CAAC,GACtC,KAAQ,oBAAoB,MAAM,GAAG,CAAC,GACtC,QAAQ,MAAM,KAAK,KAAK,KAAK,MAAM,GAAG,GAAG,GAAG,CAAC,IAAI;AAErD,mBAAO;AAAA;AAAA,cAEL,GAAU,KAAK;AAAA,cACf,GAAU,KAAK;AAAA,cACf,eAAc,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK,KAAK,GAAG,IAAI,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC;AAAA,cAChJ,eAAc,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK,KAAK,GAAG,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,QAAQ,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC;AAAA,cAEjJ,OAAU,CAAC;AAAA,cACX,OAAU,MAAM,KAAK,KAAK,KAAK,MAAM,GAAG,GAAG,GAAG,CAAC;AAAA,cAE/C,QAAU,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC;AAAA,cACrD,QAAU,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC;AAAA,cAErD,UAAU;AAAA,cACV,GAAG,KAAK;AAAA,cACR,GAAG,KAAK;AAAA,cACR,GAAG,KAAK;AAAA,cACR,GAAG,KAAK;AAAA,cACR,GAAG,KAAK;AAAA,cACR,GAAG,KAAK;AAAA,cACR,QAAQ,IAAI,IAAI,OAAO,IAAI;AAAA,YAC7B;AAAA,UACF;AAAA,UAEA,OAAO,WAAW;AAChB,mBAAO,IAAI,IAAI,OAAO,IAAI;AAAA,UAC5B;AAAA,UAEA,OAAO,SAAS,QAAQ;AAEtB,iBAAK,cAAc,IAAI,IAAI,OAAO,MAAM;AAExC,mBAAO;AAAA,UACT;AAAA,UAEA,IAAI,SAAS,KAAK;AAEhB,gBAAI,CAAC,KAAK;AAAa,qBAAO;AAG9B,gBAAI,SAAS,IAAI,IAAI,OAAO;AAAA,cAC1B,GAAG,KAAK,KAAK,KAAK,YAAY,IAAI,KAAK,KAAK;AAAA,cAC5C,GAAG,KAAK,KAAK,KAAK,YAAY,IAAI,KAAK,KAAK;AAAA,cAC5C,GAAG,KAAK,KAAK,KAAK,YAAY,IAAI,KAAK,KAAK;AAAA,cAC5C,GAAG,KAAK,KAAK,KAAK,YAAY,IAAI,KAAK,KAAK;AAAA,cAC5C,GAAG,KAAK,KAAK,KAAK,YAAY,IAAI,KAAK,KAAK;AAAA,cAC5C,GAAG,KAAK,KAAK,KAAK,YAAY,IAAI,KAAK,KAAK;AAAA,YAC9C,CAAC;AAED,mBAAO;AAAA,UACT;AAAA,UAEA,UAAU,SAAS,QAAQ;AACzB,mBAAO,IAAI,IAAI,OAAO,KAAK,OAAO,EAAE,SAAS,YAAY,MAAM,EAAE,OAAO,CAAC,CAAC;AAAA,UAC5E;AAAA,UAEA,SAAS,WAAW;AAClB,mBAAO,IAAI,IAAI,OAAO,KAAK,OAAO,EAAE,QAAQ,CAAC;AAAA,UAC/C;AAAA,UAEA,WAAW,SAAS,GAAG,GAAG;AACxB,mBAAO,IAAI,IAAI,OAAO,KAAK,OAAO,EAAE,UAAU,KAAK,GAAG,KAAK,CAAC,CAAC;AAAA,UAC/D;AAAA,UAEA,OAAO,SAAS,GAAG,GAAG,IAAI,IAAI;AAE5B,gBAAI,UAAU,UAAU,GAAG;AACzB,kBAAI;AAAA,YACN,WAAW,UAAU,UAAU,GAAG;AAChC,mBAAK;AACL,mBAAK;AACL,kBAAI;AAAA,YACN;AAEA,mBAAO,KAAK,OAAO,IAAI,IAAI,IAAI,IAAI,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,UAC7D;AAAA,UAEA,QAAQ,SAAS,GAAG,IAAI,IAAI;AAE1B,gBAAI,IAAI,MAAM,QAAQ,CAAC;AAEvB,mBAAO,KAAK,OAAO,IAAI,IAAI,IAAI,IAAI,OAAO,KAAK,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,UACtG;AAAA,UAEA,MAAM,SAAS,GAAG,GAAG;AACnB,mBAAO,KAAK,MACR,KAAK,MAAM,IAAI,GAAG,GAAG,CAAC,IACxB,KAAK,MACH,KAAK,MAAM,GAAG,IAAI,GAAG,CAAC,IACtB,KAAK,MAAM,IAAI,IAAI,GAAG,KAAK,OAAO,IAAI,CAAC;AAAA,UAC7C;AAAA,UAEA,MAAM,SAAS,GAAG,GAAG,IAAI,IAAI;AAE3B,gBAAI,UAAU,UAAU,GAAG;AACzB,kBAAI;AAAA,YACN,WAAW,UAAU,UAAU,GAAG;AAChC,mBAAK;AACL,mBAAK;AACL,kBAAI;AAAA,YACN;AAGA,gBAAI,IAAI,MAAM,QAAQ,CAAC;AACvB,gBAAI,IAAI,MAAM,QAAQ,CAAC;AAEvB,mBAAO,KAAK,OAAO,IAAI,IAAI,IAAI,IAAI,OAAO,GAAG,KAAK,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,UACjF;AAAA,UAEA,OAAO,SAAS,GAAG,IAAI,IAAI;AACzB,mBAAO,KAAK,KAAK,GAAG,GAAG,IAAI,EAAE;AAAA,UAC/B;AAAA,UAEA,OAAO,SAAS,GAAG,IAAI,IAAI;AACzB,mBAAO,KAAK,KAAK,GAAG,GAAG,IAAI,EAAE;AAAA,UAC/B;AAAA,UAEA,QAAQ,SAAS,IAAI,IAAI,QAAQ;AAC/B,mBAAO,KACJ,SAAS,IAAI,IAAI,OAAO,GAAG,GAAG,GAAG,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC,EACrD,SAAS,MAAM,EACf,SAAS,IAAI,IAAI,OAAO,GAAG,GAAG,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC;AAAA,UAC5D;AAAA,UAEA,QAAQ,WAAW;AAEjB,gBAAI,SAAS,IAAI,OAAO,OAAO,gBAAgB;AAG/C,qBAASA,KAAI,OAAO,SAAS,GAAGA,MAAK,GAAGA;AACtC,qBAAO,OAAOA,EAAC,CAAC,IAAI,KAAK,OAAOA,EAAC,CAAC;AAEpC,mBAAO;AAAA,UACT;AAAA,UAEA,UAAU,WAAW;AAEnB,mBAAO,YAAY,cAAc,KAAK,CAAC,IAAI,MAAM,cAAc,KAAK,CAAC,IACjE,MAAM,cAAc,KAAK,CAAC,IAAI,MAAM,cAAc,KAAK,CAAC,IACxD,MAAM,cAAc,KAAK,CAAC,IAAI,MAAM,cAAc,KAAK,CAAC,IACxD;AAAA,UACN;AAAA,QACF;AAAA,QAGA,QAAQ,IAAI;AAAA,QAGZ,WAAW;AAAA;AAAA,UAET,KAAK,WAAW;AACd,mBAAO,IAAI,IAAI,OAAO,KAAK,KAAK,OAAO,CAAC;AAAA,UAC1C;AAAA;AAAA,UAEA,WAAW,WAAW;AAKpB,gBAAG,gBAAgB,IAAI,QAAQ;AAC7B,kBAAI,OAAO,KAAK,KAAK,GAAE,CAAC;AACxB,kBAAI,IAAI,KAAK,KAAK,aAAa;AAC/B,mBAAK,OAAO;AACZ,qBAAO,IAAI,IAAI,OAAO,CAAC;AAAA,YACzB;AACA,mBAAO,IAAI,IAAI,OAAO,KAAK,KAAK,aAAa,CAAC;AAAA,UAChD;AAAA,QAEF;AAAA,MAEF,CAAC;AAED,UAAI,QAAQ,IAAI,OAAO;AAAA;AAAA,QAErB,QAAQ,SAAS,GAAE,GAAG;AACpB,cAAIA,IAAG,QAAQ,OAAO,EAAC,GAAE,GAAG,GAAE,EAAC;AAG/B,mBAAS,MAAM,QAAQ,CAAC,IACtB,EAAC,GAAE,EAAE,CAAC,GAAG,GAAE,EAAE,CAAC,EAAC,IACjB,OAAO,MAAM,WACX,EAAC,GAAE,EAAE,GAAG,GAAE,EAAE,EAAC,IACf,KAAK,OACH,EAAC,GAAK,GAAG,KAAK,OAAO,IAAI,EAAE,IAAI;AAGjC,eAAK,IAAI,OAAO;AAChB,eAAK,IAAI,OAAO;AAAA,QAClB;AAAA,QAGA,QAAQ;AAAA;AAAA,UAEN,OAAO,WAAW;AAChB,mBAAO,IAAI,IAAI,MAAM,IAAI;AAAA,UAC3B;AAAA,UAEA,OAAO,SAAS,GAAG,GAAG;AAEpB,iBAAK,cAAc,IAAI,IAAI,MAAM,GAAG,CAAC;AAErC,mBAAO;AAAA,UACT;AAAA,UAEA,IAAI,SAAS,KAAK;AAEhB,gBAAI,CAAC,KAAK;AAAa,qBAAO;AAG9B,gBAAI,QAAQ,IAAI,IAAI,MAAM;AAAA,cACxB,GAAG,KAAK,KAAK,KAAK,YAAY,IAAI,KAAK,KAAK;AAAA,cAC5C,GAAG,KAAK,KAAK,KAAK,YAAY,IAAI,KAAK,KAAK;AAAA,YAC9C,CAAC;AAED,mBAAO;AAAA,UACT;AAAA,UAEA,QAAQ,WAAW;AAEjB,gBAAI,QAAQ,IAAI,OAAO,OAAO,eAAe;AAG7C,kBAAM,IAAI,KAAK;AACf,kBAAM,IAAI,KAAK;AAEf,mBAAO;AAAA,UACT;AAAA,UAEA,WAAW,SAAS,QAAQ;AAC1B,mBAAO,IAAI,IAAI,MAAM,KAAK,OAAO,EAAE,gBAAgB,OAAO,OAAO,CAAC,CAAC;AAAA,UACrE;AAAA,QAEF;AAAA,MAEF,CAAC;AAED,UAAI,OAAO,IAAI,SAAS;AAAA;AAAA,QAGtB,OAAO,SAAS,GAAG,GAAG;AACpB,iBAAO,IAAI,IAAI,MAAM,GAAE,CAAC,EAAE,UAAU,KAAK,UAAU,EAAE,QAAQ,CAAC;AAAA,QAChE;AAAA,MAEF,CAAC;AAED,UAAI,OAAO,IAAI,SAAS;AAAA;AAAA,QAEtB,MAAM,SAAS,GAAG,GAAG,GAAG;AAEtB,cAAI,KAAK,MAAM;AAEb,gBAAI,CAAC;AACL,gBAAI,KAAK,KAAK;AACd,iBAAK,IAAI,EAAE,SAAS,GAAG,KAAK,GAAG;AAC7B,gBAAE,EAAE,CAAC,EAAE,QAAQ,IAAI,IAAI,MAAM,SAAS,KAAK,EAAE,CAAC,EAAE,SAAS,IAAI,WAAW,EAAE,CAAC,EAAE,SAAS,IAAI,EAAE,CAAC,EAAE;AAEjG,mBAAO;AAAA,UAET,WAAW,OAAO,KAAK,UAAU;AAE/B,iBAAK,KAAK;AAAG,mBAAK,KAAK,GAAG,EAAE,CAAC,CAAC;AAAA,UAEhC,WAAW,MAAM,MAAM;AAEnB,iBAAK,KAAK,gBAAgB,CAAC;AAAA,UAE/B,WAAW,KAAK,MAAM;AAEpB,gBAAI,KAAK,KAAK,aAAa,CAAC;AAC5B,mBAAO,KAAK,OACV,IAAI,SAAS,MAAM,CAAC,IACtB,IAAI,MAAM,SAAS,KAAK,CAAC,IACvB,WAAW,CAAC,IAAI;AAAA,UAEpB,OAAO;AAEL,gBAAI,KAAK;AACP,mBAAK,KAAK,UAAU,WAAW,CAAC,IAAI,IAAI,KAAK,UAAU,IAAI;AAAA,qBACpD,KAAK;AACZ,mBAAK,UAAU;AAGjB,gBAAI,KAAK,UAAU,KAAK,UAAU;AAChC,kBAAI,IAAI,MAAM,QAAQ,KAAK,CAAC;AAC1B,oBAAI,KAAK,IAAI,EAAE,KAAK,EAAE,MAAM,GAAG,GAAG,CAAC;AAErC,kBAAI,aAAa,IAAI;AACnB,oBAAI,KAAK,IAAI,EAAE,KAAK,EAAE,QAAQ,GAAG,GAAG,WAAW;AAC7C,uBAAK,IAAI,CAAC;AAAA,gBACZ,CAAC;AAAA,YACL;AAGA,gBAAI,OAAO,MAAM;AACf,kBAAI,IAAI,IAAI,OAAO,CAAC;AAAA,qBAGb,IAAI,MAAM,QAAQ,CAAC;AAC1B,kBAAI,IAAI,IAAI,MAAM,CAAC;AAAA,qBAGZ,MAAM,QAAQ,CAAC;AACtB,kBAAI,IAAI,IAAI,MAAM,CAAC;AAGrB,gBAAI,KAAK,WAAW;AAElB,kBAAI,KAAK;AACP,qBAAK,QAAQ,CAAC;AAAA,YAClB,OAAO;AAEL,qBAAO,MAAM,WACX,KAAK,KAAK,eAAe,GAAG,GAAG,EAAE,SAAS,CAAC,IAC3C,KAAK,KAAK,aAAa,GAAG,EAAE,SAAS,CAAC;AAAA,YAC1C;AAGA,gBAAI,KAAK,YAAY,KAAK,eAAe,KAAK;AAC5C,mBAAK,QAAQ,GAAG,CAAC;AAAA,UACrB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AACD,UAAI,OAAO,IAAI,SAAS;AAAA;AAAA,QAEtB,WAAW,SAAS,GAAG,UAAU;AAE/B,cAAI,SAAS,MACT,QAAQ;AAGZ,cAAI,OAAO,MAAM,UAAU;AAEzB,qBAAS,IAAI,IAAI,OAAO,MAAM,EAAE,QAAQ;AAExC,mBAAO,OAAO,MAAM,WAAW,OAAO,CAAC,IAAI;AAAA,UAC7C;AAGA,mBAAS,IAAI,IAAI,OAAO,MAAM;AAG9B,qBAAW,CAAC,CAAC,YAAY,CAAC,CAAC,EAAE;AAG7B,cAAI,EAAE,KAAK,MAAM;AACf,qBAAS;AAAA;AAAA,cAEP,OAAO,SAAS,IAAI,IAAI,OAAO,CAAC,CAAC;AAAA;AAAA;AAAA,cAEjC,IAAI,IAAI,OAAO,CAAC;AAAA;AAAA,UAGpB,WAAW,EAAE,YAAY,MAAM;AAE7B,yBAAa,GAAG,MAAM;AAGtB,qBAAS;AAAA;AAAA,cAEP,OAAO,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE;AAAA;AAAA;AAAA,cAEpC,OAAO,OAAO,EAAE,WAAW,OAAO,QAAQ,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE;AAAA;AAAA,UAGpE,WAAW,EAAE,SAAS,QAAQ,EAAE,UAAU,QAAQ,EAAE,UAAU,MAAM;AAElE,yBAAa,GAAG,MAAM;AAGtB,cAAE,SAAS,EAAE,SAAS,OAAO,EAAE,QAAQ,EAAE,UAAU,OAAO,EAAE,SAAS;AACrE,cAAE,SAAS,EAAE,SAAS,OAAO,EAAE,QAAQ,EAAE,UAAU,OAAO,EAAE,SAAS;AAErE,gBAAI,CAAC,UAAU;AAEb,kBAAI,IAAI,OAAO,QAAQ;AACvB,gBAAE,SAAS,EAAE,SAAS,IAAI,EAAE;AAC5B,gBAAE,SAAS,EAAE,SAAS,IAAI,EAAE;AAAA,YAC9B;AAEA,qBAAS,OAAO,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE;AAAA,UAGtD,WAAW,EAAE,QAAQ,QAAQ,EAAE,SAAS,QAAQ,EAAE,SAAS,MAAM;AAE/D,yBAAa,GAAG,MAAM;AAGtB,cAAE,QAAQ,EAAE,QAAQ,OAAO,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE,QAAQ;AAChE,cAAE,QAAQ,EAAE,QAAQ,OAAO,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE,QAAQ;AAEhE,gBAAI,CAAC,UAAU;AAEb,kBAAI,IAAI,OAAO,QAAQ;AACvB,uBAAS,OAAO,SAAS,IAAI,IAAI,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,CAAC;AAAA,YACxF;AAEA,qBAAS,OAAO,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE;AAAA,UAGnD,WAAW,EAAE,MAAM;AACjB,gBAAG,EAAE,QAAQ,OAAO,EAAE,QAAQ,KAAK;AACjC,gBAAE,SAAS,EAAE,UAAU,OAAO,OAAO,KAAK,EAAE,MAAM,EAAE,IAAI,IAAI,EAAE;AAAA,YAChE,OAAO;AACL,kBAAG,EAAE,UAAU,MAAM;AACnB,uBAAO,OAAO,KAAK;AACnB,kBAAE,OAAO,KAAK;AACd,kBAAE,SAAS,KAAK;AAAA,cAClB,OAAO;AACL,kBAAE,OAAO,EAAE;AAAA,cACb;AAAA,YACF;AAEA,qBAAS,IAAI,IAAI,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM;AAAA,UAGjD,WAAW,EAAE,KAAK,QAAQ,EAAE,KAAK,MAAM;AACrC,gBAAI,UAAU;AAEZ,uBAAS,OAAO,UAAU,EAAE,GAAG,EAAE,CAAC;AAAA,YACpC,OAAO;AAEL,kBAAI,EAAE,KAAK;AAAM,uBAAO,IAAI,EAAE;AAC9B,kBAAI,EAAE,KAAK;AAAM,uBAAO,IAAI,EAAE;AAAA,YAChC;AAAA,UACF;AAEA,iBAAO,KAAK,KAAK,aAAa,MAAM;AAAA,QACtC;AAAA,MACF,CAAC;AAED,UAAI,OAAO,IAAI,IAAI;AAAA,QACjB,WAAW,SAAS,GAAG,UAAU;AAE/B,cAAI,SAAS,KAAK,OAAO,GACrB,QAAQ;AAGZ,cAAI,OAAO,MAAM,UAAU;AAEzB,qBAAS,IAAI,IAAI,OAAO,MAAM,EAAE,QAAQ;AAExC,mBAAO,OAAO,MAAM,WAAW,OAAO,CAAC,IAAI;AAAA,UAC7C;AAGA,qBAAW,CAAC,CAAC,YAAY,CAAC,CAAC,EAAE;AAG7B,cAAI,EAAE,KAAK,MAAM;AACf,qBAAS,IAAI,IAAI,OAAO,CAAC;AAAA,UAG3B,WAAW,EAAE,YAAY,MAAM;AAE7B,yBAAa,GAAG,MAAM;AAGtB,qBAAS,IAAI,IAAI,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE;AAAA,UAGhD,WAAW,EAAE,SAAS,QAAQ,EAAE,UAAU,QAAQ,EAAE,UAAU,MAAM;AAElE,yBAAa,GAAG,MAAM;AAGtB,cAAE,SAAS,EAAE,SAAS,OAAO,EAAE,QAAQ,EAAE,UAAU,OAAO,EAAE,SAAS;AACrE,cAAE,SAAS,EAAE,SAAS,OAAO,EAAE,QAAQ,EAAE,UAAU,OAAO,EAAE,SAAS;AAErE,qBAAS,IAAI,IAAI,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE;AAAA,UAGvD,WAAW,EAAE,SAAS,QAAQ,EAAE,SAAS,MAAM;AAE7C,yBAAa,GAAG,MAAM;AAGtB,cAAE,QAAQ,EAAE,SAAS,OAAO,EAAE,QAAQ;AACtC,cAAE,QAAQ,EAAE,SAAS,OAAO,EAAE,QAAQ;AAEtC,qBAAS,IAAI,IAAI,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE;AAAA,UAGpD,WAAW,EAAE,MAAM;AACjB,gBAAG,EAAE,QAAQ,OAAO,EAAE,QAAQ,KAAK;AACjC,gBAAE,SAAS,EAAE,UAAU,OAAO,OAAO,KAAK,EAAE,MAAM,EAAE,IAAI,IAAI,EAAE;AAAA,YAChE,OAAO;AACL,kBAAG,EAAE,UAAU,MAAM;AACnB,uBAAO,OAAO,KAAK;AACnB,kBAAE,OAAO,KAAK;AACd,kBAAE,SAAS,KAAK;AAAA,cAClB,OAAO;AACL,kBAAE,OAAO,EAAE;AAAA,cACb;AAAA,YACF;AAEA,qBAAS,IAAI,IAAI,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM;AAAA,UAGjD,WAAW,EAAE,KAAK,QAAQ,EAAE,KAAK,MAAM;AACrC,qBAAS,IAAI,IAAI,UAAU,EAAE,GAAG,EAAE,CAAC;AAAA,UACrC;AAEA,cAAG,CAAC;AAAQ,mBAAO;AAEnB,iBAAO,WAAW;AAElB,eAAK,KAAK,EAAE,WAAW,KAAK,MAAM;AAElC,iBAAO,KAAK,WAAW;AAAA,QACzB;AAAA,MACF,CAAC;AAED,UAAI,OAAO,IAAI,SAAS;AAAA;AAAA,QAEtB,aAAa,WAAW;AACtB,iBAAO,KAAK,KAAK,aAAa,IAAI;AAAA,QACpC;AAAA;AAAA,QAEA,WAAW,WAAW;AAEpB,cAAI,UAAU,KAAK,KAAK,WAAW,KAAK,IAErC,MAAM,IAAI,MAAM,UAAU,EAAE,MAAM,GAAE,EAAE,EAAE,IAAI,SAAS,KAAI;AAExD,gBAAI,KAAK,IAAI,KAAK,EAAE,MAAM,GAAG;AAC7B,mBAAO,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE,MAAM,IAAI,MAAM,SAAS,EAAE,IAAI,SAASI,MAAI;AAAE,qBAAO,WAAWA,IAAG;AAAA,YAAE,CAAC,CAAC;AAAA,UAC9F,CAAC,EAEA,OAAO,SAASC,SAAQ,WAAU;AAEjC,gBAAG,UAAU,CAAC,KAAK;AAAU,qBAAOA,QAAO,SAAS,cAAc,UAAU,CAAC,CAAC,CAAC;AAC/E,mBAAOA,QAAO,UAAU,CAAC,CAAC,EAAE,MAAMA,SAAQ,UAAU,CAAC,CAAC;AAAA,UAExD,GAAG,IAAI,IAAI,OAAO,CAAC;AAErB,iBAAO;AAAA,QACT;AAAA;AAAA,QAEA,UAAU,SAAS,QAAQ;AACzB,cAAG,QAAQ;AAAQ,mBAAO;AAC1B,cAAI,MAAM,KAAK,UAAU;AACzB,cAAI,OAAO,OAAO,UAAU,EAAE,QAAQ;AAEtC,eAAK,MAAM,MAAM,EAAE,YAAY,EAAE,UAAU,KAAK,SAAS,GAAG,CAAC;AAE7D,iBAAO;AAAA,QACT;AAAA;AAAA,QAEA,OAAO,WAAW;AAChB,iBAAO,KAAK,SAAS,KAAK,IAAI,CAAC;AAAA,QACjC;AAAA,MAEF,CAAC;AAED,UAAI,iBAAiB,IAAI,OAAO;AAAA,QAE9B,QAAQ,SAAS,QAAQ,UAAS;AAEhC,cAAG,UAAU,SAAS,KAAK,OAAO,YAAY,WAAU;AACtD,mBAAO,KAAK,YAAY,KAAK,MAAM,CAAC,EAAE,MAAM,KAAK,SAAS,CAAC;AAAA,UAC7D;AAEA,cAAG,MAAM,QAAQ,MAAM,GAAE;AACvB,qBAAQL,KAAI,GAAG,MAAM,KAAK,UAAU,QAAQA,KAAI,KAAK,EAAEA,IAAE;AACvD,mBAAK,KAAK,UAAUA,EAAC,CAAC,IAAI,OAAOA,EAAC;AAAA,YACpC;AAAA,UACF,WAAU,OAAO,UAAU,UAAS;AAClC,qBAAQA,KAAI,GAAG,MAAM,KAAK,UAAU,QAAQA,KAAI,KAAK,EAAEA,IAAE;AACvD,mBAAK,KAAK,UAAUA,EAAC,CAAC,IAAI,OAAO,KAAK,UAAUA,EAAC,CAAC;AAAA,YACpD;AAAA,UACF;AAEA,eAAK,WAAW;AAEhB,cAAG,aAAa,MAAK;AACnB,iBAAK,WAAW;AAAA,UAClB;AAAA,QAEF;AAAA,QAEA,QAAQ;AAAA,UAEN,WAAW,CAAC;AAAA,UACZ,QAAQ;AAAA,UAER,IAAI,SAAS,KAAI;AAEf,gBAAI,SAAS,CAAC;AAEd,qBAAQA,KAAI,GAAG,MAAM,KAAK,UAAU,QAAQA,KAAI,KAAK,EAAEA,IAAE;AACvD,qBAAO,KAAK,KAAK,KAAK,UAAUA,EAAC,CAAC,CAAC;AAAA,YACrC;AAEA,gBAAI,IAAI,KAAK,SAAS,IAAI,IAAI,OAAO;AAErC,gBAAI,IAAI,IAAI,OAAO,EAAE,MAAM,IAAI,OAAO,UAAU,KAAK,MAAM,EAAE,MAAM,GAAG,MAAM,CAAC,EAAE,GAAG,GAAG;AAErF,mBAAO,KAAK,WAAW,EAAE,QAAQ,IAAI;AAAA,UAEvC;AAAA,UAEA,MAAM,SAAS,GAAE;AACf,qBAAQA,KAAI,GAAG,MAAM,KAAK,UAAU,QAAQA,KAAI,KAAK,EAAEA,IAAE;AACvD,gBAAE,KAAK,UAAUA,EAAC,CAAC,IAAI,OAAO,KAAK,KAAK,UAAUA,EAAC,CAAC,KAAK,cAAc,IAAI,EAAE,KAAK,UAAUA,EAAC,CAAC;AAAA,YAChG;AAKA,cAAE,KAAK,KAAK;AACZ,cAAE,KAAK,KAAK;AAEZ,iBAAK,QAAQ,IAAI,IAAI,WAAW,KAAK,MAAM,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,CAAC;AAE3D,mBAAO;AAAA,UACT;AAAA,QAEF;AAAA,MAEF,CAAC;AAED,UAAI,YAAY,IAAI,OAAO;AAAA,QAEzB,QAAQ,IAAI;AAAA,QACZ,SAAS,IAAI;AAAA,QAEb,QAAQ,SAAS,QAAQ,UAAS;AAChC,eAAK,YAAY,MAAM,MAAM,CAAC,EAAE,MAAM,KAAK,SAAS,CAAC;AAAA,QACvD;AAAA,QAEA,QAAQ;AAAA,UACN,WAAW,CAAC,gBAAgB,cAAc;AAAA,UAC1C,QAAQ;AAAA,QACV;AAAA,MAEF,CAAC;AAED,UAAI,SAAS,IAAI,OAAO;AAAA,QAEtB,QAAQ,IAAI;AAAA,QACZ,SAAS,IAAI;AAAA,QAEb,QAAQ,SAAS,QAAQ,UAAS;AAChC,eAAK,YAAY,MAAM,MAAM,CAAC,EAAE,MAAM,KAAK,SAAS,CAAC;AAAA,QACvD;AAAA,QAEA,QAAQ;AAAA,UACN,WAAW,CAAC,YAAY,MAAM,IAAI;AAAA,UAClC,QAAQ;AAAA,UACR,IAAI,SAAS,KAAI;AACf,gBAAI,IAAI,IAAI,IAAI,OAAO,EAAE,OAAO,IAAI,IAAI,OAAO,EAAE,MAAM,KAAK,YAAY,KAAK,QAAQ,KAAK,MAAM,WAAW,EAAE,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,KAAK,EAAE;AACxI,mBAAO,KAAK,WAAW,EAAE,QAAQ,IAAI;AAAA,UACvC;AAAA,UACA,MAAM,SAAS,GAAE;AACf,iBAAK,QAAQ;AACb,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MAEF,CAAC;AAED,UAAI,QAAQ,IAAI,OAAO;AAAA,QAErB,QAAQ,IAAI;AAAA,QACZ,SAAS,IAAI;AAAA,QAEb,QAAQ,SAAS,QAAQ,UAAS;AAChC,eAAK,YAAY,MAAM,MAAM,CAAC,EAAE,MAAM,KAAK,SAAS,CAAC;AAAA,QACvD;AAAA,QAEA,QAAQ;AAAA,UACN,WAAW,CAAC,UAAU,UAAU,MAAM,IAAI;AAAA,UAC1C,QAAQ;AAAA,QACV;AAAA,MAEF,CAAC;AAED,UAAI,OAAO,IAAI,OAAO;AAAA,QAEpB,QAAQ,IAAI;AAAA,QACZ,SAAS,IAAI;AAAA,QAEb,QAAQ,SAAS,QAAQ,UAAS;AAChC,eAAK,YAAY,MAAM,MAAM,CAAC,EAAE,MAAM,KAAK,SAAS,CAAC;AAAA,QACvD;AAAA,QAEA,QAAQ;AAAA,UACN,WAAW,CAAC,SAAS,SAAS,MAAM,IAAI;AAAA,UACxC,QAAQ;AAAA,QACV;AAAA,MAEF,CAAC;AAED,UAAI,OAAO,IAAI,SAAS;AAAA;AAAA,QAEtB,OAAO,SAAS,GAAG,GAAG;AACpB,cAAI,UAAU,UAAU,GAAG;AAEzB,mBAAO,KAAK,KAAK,MAAM,WAAW;AAAA,UAEpC,WAAW,UAAU,SAAS,GAAG;AAE/B,gBAAI,OAAO,KAAK,UAAU;AACxB,mBAAK,KAAK;AAAG,qBAAK,MAAM,GAAG,EAAE,CAAC,CAAC;AAAA,YAEjC,WAAW,IAAI,MAAM,MAAM,KAAK,CAAC,GAAG;AAElC,kBAAI,EAAE,MAAM,SAAS,EAElB,OAAO,SAAS,GAAG;AAAE,uBAAO,CAAC,CAAC;AAAA,cAAE,CAAC,EACjC,IAAI,SAAS,GAAE;AAAE,uBAAO,EAAE,MAAM,SAAS;AAAA,cAAE,CAAC;AAG/C,qBAAO,IAAI,EAAE,IAAI,GAAG;AAClB,qBAAK,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,cACvB;AAAA,YACF,OAAO;AAEL,qBAAO,KAAK,KAAK,MAAM,UAAU,CAAC,CAAC;AAAA,YACrC;AAAA,UAEF,OAAO;AACL,iBAAK,KAAK,MAAM,UAAU,CAAC,CAAC,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,KAAK,CAAC,IAAI,KAAK;AAAA,UACjF;AAEA,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AACD,UAAI,SAAS,IAAI,OAAO;AAAA;AAAA,QAEtB,QAAQ,SAAS,SAAS;AACxB,eAAK,YAAY,KAAK,MAAM,OAAO;AAAA,QACrC;AAAA,QAGA,SAAS,IAAI;AAAA,QAGb,QAAQ;AAAA;AAAA,UAEN,UAAU,WAAW;AACnB,mBAAO,IAAI,MAAM,IAAI,IAAI,MAAM,kBAAkB,KAAK,KAAK,UAAU,GAAG,SAAS,MAAM;AACrF,qBAAO,IAAI,MAAM,IAAI;AAAA,YACvB,CAAC;AAAA,UACH;AAAA,UAEA,KAAK,SAAS,SAASA,IAAG;AACxB,gBAAIA,MAAK;AACP,mBAAK,KAAK,YAAY,QAAQ,IAAI;AAAA,qBAC3B,QAAQ,QAAQ,KAAK,KAAK,WAAWA,EAAC;AAC7C,mBAAK,KAAK,aAAa,QAAQ,MAAM,KAAK,KAAK,WAAWA,EAAC,CAAC;AAE9D,mBAAO;AAAA,UACT;AAAA,UAEA,KAAK,SAAS,SAASA,IAAG;AACxB,iBAAK,IAAI,SAASA,EAAC;AACnB,mBAAO;AAAA,UACT;AAAA,UAEA,KAAK,SAAS,SAAS;AACrB,mBAAO,KAAK,MAAM,OAAO,KAAK;AAAA,UAChC;AAAA,UAEA,OAAO,SAAS,SAAS;AACvB,mBAAO,CAAC,EAAE,MAAM,KAAK,KAAK,KAAK,UAAU,EAAE,QAAQ,QAAQ,IAAI;AAAA,UACjE;AAAA,UAEA,KAAK,SAASA,IAAG;AACf,mBAAO,IAAI,MAAM,KAAK,KAAK,WAAWA,EAAC,CAAC;AAAA,UAC1C;AAAA,UAEA,OAAO,WAAW;AAChB,mBAAO,KAAK,IAAI,CAAC;AAAA,UACnB;AAAA,UAEA,MAAM,WAAW;AACf,mBAAO,KAAK,IAAI,KAAK,KAAK,WAAW,SAAS,CAAC;AAAA,UACjD;AAAA,UAEA,MAAM,SAAS,OAAO,MAAM;AAC1B,gBAAIA,IAAGC,KACH,WAAW,KAAK,SAAS;AAE7B,iBAAKD,KAAI,GAAGC,MAAK,SAAS,QAAQD,KAAIC,KAAID,MAAK;AAC7C,kBAAI,SAASA,EAAC,aAAa,IAAI;AAC7B,sBAAM,MAAM,SAASA,EAAC,GAAG,CAACA,IAAG,QAAQ,CAAC;AAExC,kBAAI,QAAS,SAASA,EAAC,aAAa,IAAI;AACtC,yBAASA,EAAC,EAAE,KAAK,OAAO,IAAI;AAAA,YAChC;AAEA,mBAAO;AAAA,UACT;AAAA,UAEA,eAAe,SAAS,SAAS;AAC/B,iBAAK,KAAK,YAAY,QAAQ,IAAI;AAElC,mBAAO;AAAA,UACT;AAAA,UAEA,OAAO,WAAW;AAEhB,mBAAM,KAAK,KAAK,cAAc;AAC5B,mBAAK,KAAK,YAAY,KAAK,KAAK,SAAS;AAG3C,mBAAO,KAAK;AAEZ,mBAAO;AAAA,UACT;AAAA;AAAA,UAEA,MAAM,WAAW;AACf,mBAAO,KAAK,IAAI,EAAE,KAAK;AAAA,UACzB;AAAA,QACF;AAAA,MAEF,CAAC;AAED,UAAI,OAAO,IAAI,QAAQ;AAAA,QAErB,SAAS,SAAS,QAAQ,OAAO;AAC/B,cAAG,UAAU,KAAK,gBAAgB,IAAI,QAAQ,KAAK,QAAQ,IAAI,OAAO;AAAM,mBAAO;AAEnF,mBAAS,WAAW,gBAAgB,IAAI,MAAM,OAAO,KAAK,OAAO,IAAI,MAAM;AAC3E,kBAAQ,SAAS;AAEjB,eAAK,KAAK,WAAU;AAClB,gBAAG,gBAAgB,IAAI;AAAM,qBAAO;AACpC,gBAAG,gBAAgB,IAAI;AAAQ,qBAAO,KAAK,QAAQ,QAAQ,QAAM,CAAC;AAClE,mBAAO,KAAK,SAAS,MAAM;AAAA,UAC7B,CAAC;AAED,eAAK,KAAK,cAAc,KAAK,OAAO;AAEpC,iBAAO;AAAA,QACT;AAAA,QAEA,SAAS,SAAS,QAAQ,OAAO;AAC/B,iBAAO,KAAK,QAAQ,QAAQ,KAAK;AAAA,QACnC;AAAA,MAEF,CAAC;AACD,UAAI,YAAY,IAAI,OAAO;AAAA;AAAA,QAEzB,QAAQ,SAAS,SAAS;AACxB,eAAK,YAAY,KAAK,MAAM,OAAO;AAAA,QACrC;AAAA,QAGA,SAAS,IAAI;AAAA,MAEf,CAAC;AAED,UAAI,UAAU,IAAI,OAAO;AAAA,QAEvB,QAAQ,SAAS,QAAQ;AACvB,cAAIA,IAAG,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAEzB,cAAI,GAAG,GAAG,OAAO,QAAQ,KAAK,MAAM,IAAI,IACpC,KAAO,GACP,KAAO,GACP,MAAO;AAEX,cAAG,kBAAkB,IAAI,SAAQ;AAE/B,iBAAK;AACL,iBAAK;AACL,oBAAQ,OAAO,KAAK,SAAS,KAAK,IAAI,MAAM,GAAG;AAC/C,kBAAM,OAAO;AAGb,oBAAS,IAAI,IAAI,OAAO,OAAO,MAAM,CAAC;AACtC,qBAAS,IAAI,IAAI,OAAO,OAAO,OAAO,CAAC;AAGvC,mBAAO,MAAM,QAAQ,KAAK;AACxB,oBAAM,MAAM;AACZ,sBAAQ,IAAI,IAAI,OAAO,cAAc,IAAI,MAAM,GAAG,OAAO,EAAE,cAAc,GAAG,OAAO,EAAE,MAAM,CAAC;AAC5F,mBAAK,GAAG,OAAO;AAAA,YACjB;AACA,mBAAO,OAAO,QAAQ,KAAK;AACzB,oBAAM,OAAO;AACb,uBAAS,IAAI,IAAI,OAAO,cAAc,IAAI,MAAM,GAAG,OAAO,EAAE,eAAe,GAAG,OAAO,EAAE,OAAO,CAAC;AAC/F,mBAAK,GAAG,OAAO;AAAA,YACjB;AAGA,iBAAK,IAAS;AACd,iBAAK,IAAS;AACd,iBAAK,QAAS,QAAS;AACvB,iBAAK,SAAS,SAAS;AACvB,iBAAK,OAAS;AAEd,gBAAI,MAAM;AAER,kBAAS,WAAW,KAAK,CAAC,CAAC;AAC3B,kBAAS,WAAW,KAAK,CAAC,CAAC;AAC3B,sBAAS,WAAW,KAAK,CAAC,CAAC;AAC3B,uBAAS,WAAW,KAAK,CAAC,CAAC;AAG3B,mBAAK,OAAS,KAAK,QAAQ,KAAK,SAAW,QAAQ,SACjD,KAAK,SAAS,SACd,KAAK,QAAS;AAGhB,mBAAK,IAAS;AACd,mBAAK,IAAS;AACd,mBAAK,QAAS;AACd,mBAAK,SAAS;AAAA,YAEhB;AAAA,UAEF,OAAK;AAGH,qBAAS,OAAO,WAAW,WACzB,OAAO,MAAM,GAAG,EAAE,IAAI,SAAS,IAAG;AAAE,qBAAO,WAAW,EAAE;AAAA,YAAE,CAAC,IAC7D,MAAM,QAAQ,MAAM,IAClB,SACF,OAAO,UAAU,WACf,CAAC,OAAO,GAAG,OAAO,GAAG,OAAO,OAAO,OAAO,MAAM,IAClD,UAAU,UAAU,IAClB,CAAC,EAAE,MAAM,KAAK,SAAS,IACvB;AAEF,iBAAK,IAAI,OAAO,CAAC;AACjB,iBAAK,IAAI,OAAO,CAAC;AACjB,iBAAK,QAAQ,OAAO,CAAC;AACrB,iBAAK,SAAS,OAAO,CAAC;AAAA,UACxB;AAAA,QAGF;AAAA,QAEA,QAAQ;AAAA,UAEN,UAAU,WAAW;AACnB,mBAAO,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,QAAQ,MAAM,KAAK;AAAA,UAC/D;AAAA,UACA,OAAO,SAAS,GAAG,GAAG,OAAO,QAAO;AAClC,iBAAK,cAAc,IAAI,IAAI,QAAQ,GAAG,GAAG,OAAO,MAAM;AACtD,mBAAO;AAAA,UACT;AAAA,UAEA,IAAI,SAAS,KAAK;AAEhB,gBAAG,CAAC,KAAK;AAAa,qBAAO;AAE7B,mBAAO,IAAI,IAAI,QAAQ;AAAA,cACnB,KAAK,KAAK,KAAK,YAAY,IAAI,KAAK,KAAK;AAAA,cACzC,KAAK,KAAK,KAAK,YAAY,IAAI,KAAK,KAAK;AAAA,cACzC,KAAK,SAAS,KAAK,YAAY,QAAQ,KAAK,SAAS;AAAA,cACrD,KAAK,UAAU,KAAK,YAAY,SAAS,KAAK,UAAU;AAAA,YAC5D,CAAC;AAAA,UAEH;AAAA,QAEF;AAAA,QAGA,QAAQ,IAAI;AAAA,QAGZ,WAAW;AAAA;AAAA,UAGT,SAAS,SAAS,GAAG,GAAG,OAAO,QAAQ;AACrC,gBAAI,UAAU,UAAU;AAEtB,qBAAO,IAAI,IAAI,QAAQ,IAAI;AAG7B,mBAAO,KAAK,KAAK,WAAW,IAAI,IAAI,QAAQ,GAAG,GAAG,OAAO,MAAM,CAAC;AAAA,UAClE;AAAA,QAEF;AAAA,MAEF,CAAC;AAGA;AAAA,QAAE;AAAA,QACD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAc,EAAE,QAAQ,SAAU,OAAO;AAEvC,YAAI,QAAQ,UAAU,KAAK,IAAI,SAAU,GAAG;AAE1C,cAAI,KAAK,MAAM;AACb,gBAAI,IAAI,MAAM,KAAK;AAAA,UACrB,OAAO;AACL,gBAAI,GAAG,MAAM,OAAO,CAAC;AAAA,UACvB;AACA,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAEH,UAAI,aAAa;AAGjB,UAAI,KAAK,SAAU,MAAM,QAAQ,UAAU,SAAS,SAAS;AAC3D,YAAI,IAAI,SAAS,KAAK,WAAW,IAAI;AACrC,YAAI,IAAI,gBAAgB,IAAI,UAAU,KAAK,OAAO;AAGlD,UAAE,WAAW,EAAE,YAAY,EAAC,SAAS,CAAC,EAAC;AAEvC,YAAI,MAAM,EAAE,SAAS;AAGrB,YAAI,CAAC,SAAS,kBAAkB;AAAE,mBAAS,mBAAmB,EAAE,IAAI;AAAA,QAAW;AAE/E,eAAO,MAAM,IAAI,MAAM,SAAS,EAAE,QAAQ,SAAU,OAAO;AACzD,cAAI,KAAK,MAAM,MAAM,GAAG,EAAE,CAAC;AAC3B,cAAI,KAAK,MAAM,MAAM,GAAG,EAAE,CAAC,KAAK;AAGhC,cAAI,EAAE,IAAI,IAAI,EAAE,KAAK,CAAC;AACtB,cAAI,EAAE,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE,EAAE,KAAK,CAAC;AAG9B,cAAI,EAAE,EAAE,EAAE,EAAE,SAAS,gBAAgB,IAAI;AAGzC,YAAE,iBAAiB,IAAI,GAAG,WAAW,KAAK;AAAA,QAC5C,CAAC;AAAA,MACH;AAGA,UAAI,MAAM,SAAU,MAAM,QAAQ,UAAU,SAAS;AACnD,YAAI,IAAI,gBAAgB,IAAI,UAAU,KAAK,OAAO;AAClD,YAAI,CAAC,EAAE;AAAU;AAGjB,YAAI,OAAO,aAAa,YAAY;AAClC,qBAAW,SAAS;AACpB,cAAI,CAAC;AAAU;AAAA,QACjB;AAEA,YAAI,MAAM,EAAE,SAAS;AAEpB,SAAC,UAAU,IAAI,MAAM,IAAI,MAAM,SAAS,EAAE,QAAQ,SAAU,OAAO;AAClE,cAAI,KAAK,SAAS,MAAM,MAAM,GAAG,EAAE,CAAC;AACpC,cAAI,KAAK,SAAS,MAAM,MAAM,GAAG,EAAE,CAAC;AACpC,cAAI,WAAW;AAEf,cAAI,UAAU;AAEZ,gBAAI,IAAI,EAAE,KAAK,IAAI,EAAE,EAAE,MAAM,GAAG,GAAG;AAEjC,gBAAE,oBAAoB,IAAI,IAAI,EAAE,EAAE,MAAM,GAAG,EAAE,QAAQ,GAAG,WAAW,KAAK;AAExE,qBAAO,IAAI,EAAE,EAAE,MAAM,GAAG,EAAE,QAAQ;AAAA,YACpC;AAAA,UACF,WAAW,MAAM,IAAI;AAEnB,gBAAI,IAAI,EAAE,KAAK,IAAI,EAAE,EAAE,EAAE,GAAG;AAC1B,mBAAK,KAAK,IAAI,EAAE,EAAE,EAAE,GAAG;AAAE,oBAAI,IAAI,GAAG,CAAC,IAAI,EAAE,EAAE,KAAK,GAAG,GAAG,CAAC;AAAA,cAAE;AAE3D,qBAAO,IAAI,EAAE,EAAE,EAAE;AAAA,YACnB;AAAA,UACF,WAAW,IAAI;AAEb,iBAAK,SAAS,KAAK;AACjB,mBAAK,aAAa,IAAI,KAAK,GAAG;AAC5B,oBAAI,OAAO,WAAW;AAAE,sBAAI,IAAI,GAAG,CAAC,OAAO,EAAE,EAAE,KAAK,GAAG,CAAC;AAAA,gBAAE;AAAA,cAC5D;AAAA,YACF;AAAA,UACF,WAAW,IAAI;AAEb,gBAAI,IAAI,EAAE,GAAG;AACX,mBAAK,aAAa,IAAI,EAAE,GAAG;AAAE,oBAAI,IAAI,GAAG,CAAC,IAAI,SAAS,EAAE,KAAK,GAAG,CAAC;AAAA,cAAE;AAEnE,qBAAO,IAAI,EAAE;AAAA,YACf;AAAA,UACF,OAAO;AAEL,iBAAK,SAAS,KAAK;AAAE,kBAAI,IAAI,GAAG,KAAK;AAAA,YAAE;AAEvC,cAAE,SAAS,UAAU,CAAC;AAAA,UACxB;AAAA,QACF,CAAC;AAAA,MACH;AAEA,UAAI,OAAO,IAAI,SAAS;AAAA;AAAA,QAEtB,IAAI,SAAU,OAAO,UAAU,SAAS,SAAS;AAC/C,cAAI,GAAG,MAAM,OAAO,UAAU,SAAS,OAAO;AAC9C,iBAAO;AAAA,QACT;AAAA;AAAA,QAEA,KAAK,SAAU,OAAO,UAAU;AAC9B,cAAI,IAAI,KAAK,MAAM,OAAO,QAAQ;AAClC,iBAAO;AAAA,QACT;AAAA,QACA,MAAM,SAAU,OAAO,MAAM;AAE3B,cAAI,iBAAiBF,QAAO,OAAO;AACjC,iBAAK,KAAK,cAAc,KAAK;AAAA,UAC/B,OAAO;AACL,iBAAK,KAAK,cAAc,QAAQ,IAAI,IAAI,YAAY,OAAO,EAAC,QAAQ,MAAM,YAAY,KAAI,CAAC,CAAC;AAAA,UAC9F;AACA,eAAK,SAAS;AACd,iBAAO;AAAA,QACT;AAAA,QACA,OAAO,WAAW;AAChB,iBAAO,KAAK;AAAA,QACd;AAAA,MACF,CAAC;AAGD,UAAI,OAAO,IAAI,OAAO;AAAA;AAAA,QAEpB,QAAQ;AAAA,QAGR,SAAS,IAAI;AAAA,MAEf,CAAC;AACD,UAAI,IAAI,IAAI,OAAO;AAAA;AAAA,QAEjB,QAAQ;AAAA,QAGR,SAAS,IAAI;AAAA,QAGb,QAAQ;AAAA;AAAA,UAEN,GAAG,SAAS,GAAG;AACb,mBAAO,KAAK,OAAO,KAAK,UAAU,GAAG,IAAI,KAAK,UAAU,EAAE,GAAG,IAAI,KAAK,EAAE,EAAE,GAAG,IAAI;AAAA,UACnF;AAAA,UAEA,GAAG,SAAS,GAAG;AACb,mBAAO,KAAK,OAAO,KAAK,UAAU,GAAG,IAAI,KAAK,UAAU,EAAE,GAAG,IAAI,KAAK,EAAE,EAAE,GAAG,IAAI;AAAA,UACnF;AAAA,UAEA,IAAI,SAAS,GAAG;AACd,mBAAO,KAAK,OAAO,KAAK,KAAK,EAAE,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,EAAE,QAAQ,CAAC;AAAA,UACtE;AAAA,UAEA,IAAI,SAAS,GAAG;AACd,mBAAO,KAAK,OAAO,KAAK,KAAK,EAAE,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,EAAE,SAAS,CAAC;AAAA,UACvE;AAAA,UACA,MAAM,WAAW;AAEf,gBAAI,OAAQ,KAAK,KAAK,GAClB,QAAQ,KAAK,UAAU;AAE3B,iBAAK,KAAM,MAAM;AACjB,iBAAK,MAAM,MAAM;AACjB,iBAAK,MAAM,MAAM;AAEjB,iBAAK,KAAM,MAAM;AACjB,iBAAK,MAAM,MAAM;AACjB,iBAAK,MAAM,MAAM;AAEjB,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,QAGA,WAAW;AAAA;AAAA,UAET,OAAO,WAAW;AAChB,mBAAO,KAAK,IAAI,IAAI,IAAI,GAAC;AAAA,UAC3B;AAAA,QACF;AAAA,MACF,CAAC;AAED,UAAI,MAAM,IAAI,OAAO;AAAA;AAAA,QAEnB,QAAQ,SAAS,SAAS;AACxB,cAAI,SAAS;AAEX,sBAAU,OAAO,WAAW,WAC1BC,UAAS,eAAe,OAAO,IAC/B;AAIF,gBAAI,QAAQ,YAAY,OAAO;AAC7B,mBAAK,YAAY,KAAK,MAAM,OAAO;AAAA,YACrC,OAAO;AACL,mBAAK,YAAY,KAAK,MAAM,IAAI,OAAO,KAAK,CAAC;AAC7C,sBAAQ,YAAY,KAAK,IAAI;AAC7B,mBAAK,KAAK,QAAQ,MAAM;AAAA,YAC1B;AAGA,iBAAK,UAAU,EAAE,KAAK;AAAA,UACxB;AAAA,QACF;AAAA,QAGA,SAAS,IAAI;AAAA,QAGb,QAAQ;AAAA;AAAA,UAEN,WAAW,WAAW;AACpB,mBAAO,KACJ,KAAK,EAAE,OAAO,IAAI,IAAI,SAAS,MAAM,CAAC,EACtC,KAAK,eAAe,IAAI,OAAO,IAAI,KAAK,EACxC,KAAK,eAAe,IAAI,OAAO,IAAI,KAAK;AAAA,UAC7C;AAAA,UAEA,MAAM,WAAW;AACf,gBAAI,CAAC,KAAK,OAAO;AACf,kBAAI;AAGJ,kBAAI,OAAO,KAAK,KAAK,qBAAqB,MAAM,EAAE,CAAC;AACjD,qBAAK,QAAQ,IAAI,MAAM,IAAI;AAAA;AAE3B,qBAAK,QAAQ,IAAI,IAAI;AAGvB,mBAAK,KAAK,YAAY,KAAK,MAAM,IAAI;AAAA,YACvC;AAEA,mBAAO,KAAK;AAAA,UACd;AAAA,UAEA,QAAQ,WAAW;AACjB,gBAAG,CAAC,KAAK,KAAK,cAAc,KAAK,KAAK,WAAW,YAAY,eAAe,KAAK,KAAK,WAAW,YAAY;AAAsB,qBAAO;AAC1I,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,UAGA,MAAM,WAAW;AACf,gBAAI,MAAM,KAAK,KAAK,aAAa;AAEjC,gBAAI;AACF,mBACG,MAAM,QAAS,CAAC,IAAI,IAAI,IAAK,IAAI,EACjC,MAAM,OAAS,CAAC,IAAI,IAAI,IAAK,IAAI;AAEtC,mBAAO;AAAA,UACT;AAAA,UAGA,QAAQ,WAAW;AACjB,gBAAG,KAAK,OAAO,GAAG;AAChB,mBAAK,OAAO,EAAE,YAAY,KAAK,IAAI;AAAA,YACrC;AAEA,mBAAO;AAAA,UACT;AAAA,UACA,OAAO,WAAW;AAEhB,mBAAM,KAAK,KAAK,cAAc;AAC5B,mBAAK,KAAK,YAAY,KAAK,KAAK,SAAS;AAG3C,mBAAO,KAAK;AAGZ,gBAAG,CAAC,IAAI,OAAO,KAAK;AAClB,mBAAK,KAAK,YAAY,IAAI,OAAO,IAAI;AAEvC,mBAAO;AAAA,UACT;AAAA,UACA,OAAO,SAAU,QAAQ;AAEvB,iBAAK,eAAe;AAGpB,gBAAI,OAAO,KAAK;AAGhB,gBAAI,QAAQ,YAAY,KAAK,UAAU,IAAI,CAAC;AAG5C,gBAAG,QAAQ;AACT,eAAC,OAAO,QAAQ,QAAQ,YAAY,MAAM,IAAI;AAAA,YAChD,OAAO;AACL,mBAAK,WAAW,aAAa,MAAM,MAAM,KAAK,WAAW;AAAA,YAC3D;AAEA,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MAEF,CAAC;AAKD,UAAI,OAAO,IAAI,SAAS;AAAA;AAAA,QAEtB,UAAU,WAAW;AACnB,iBAAO,KAAK,OAAO,EAAE,SAAS;AAAA,QAChC;AAAA,QAEA,UAAU,WAAW;AACnB,iBAAO,KAAK,OAAO,EAAE,MAAM,IAAI;AAAA,QACjC;AAAA,QAEA,MAAM,WAAW;AACf,iBAAO,KAAK,SAAS,EAAE,KAAK,SAAS,IAAI,CAAC;AAAA,QAC5C;AAAA,QAEA,UAAU,WAAW;AACnB,iBAAO,KAAK,SAAS,EAAE,KAAK,SAAS,IAAI,CAAC;AAAA,QAC5C;AAAA,QAEA,SAAS,WAAW;AAClB,cAAIC,KAAI,KAAK,SAAS,IAAI,GACtB,IAAI,KAAK,OAAO;AAGpB,YAAE,cAAc,IAAI,EAAE,IAAI,MAAMA,EAAC;AAGjC,cAAI,aAAa,IAAI;AACnB,cAAE,KAAK,YAAY,EAAE,KAAK,EAAE,IAAI;AAElC,iBAAO;AAAA,QACT;AAAA,QAEA,UAAU,WAAW;AACnB,cAAIA,KAAI,KAAK,SAAS;AAEtB,cAAIA,KAAI;AACN,iBAAK,OAAO,EAAE,cAAc,IAAI,EAAE,IAAI,MAAMA,KAAI,CAAC;AAEnD,iBAAO;AAAA,QACT;AAAA,QAEA,OAAO,WAAW;AAChB,cAAI,IAAI,KAAK,OAAO;AAGpB,YAAE,KAAK,YAAY,KAAK,IAAI;AAG5B,cAAI,aAAa,IAAI;AACnB,cAAE,KAAK,YAAY,EAAE,KAAK,EAAE,IAAI;AAElC,iBAAO;AAAA,QACT;AAAA,QAEA,MAAM,WAAW;AACf,cAAI,KAAK,SAAS,IAAI;AACpB,iBAAK,OAAO,EAAE,cAAc,IAAI,EAAE,IAAI,MAAM,CAAC;AAE/C,iBAAO;AAAA,QACT;AAAA,QAEA,QAAQ,SAAS,SAAS;AACxB,kBAAQ,OAAO;AAEf,cAAIA,KAAI,KAAK,SAAS;AAEtB,eAAK,OAAO,EAAE,IAAI,SAASA,EAAC;AAE5B,iBAAO;AAAA,QACT;AAAA,QAEA,OAAO,SAAS,SAAS;AACvB,kBAAQ,OAAO;AAEf,cAAIA,KAAI,KAAK,SAAS;AAEtB,eAAK,OAAO,EAAE,IAAI,SAASA,KAAI,CAAC;AAEhC,iBAAO;AAAA,QACT;AAAA,MAEF,CAAC;AACD,UAAI,OAAO,IAAI,OAAO;AAAA;AAAA,QAEpB,QAAQ,WAAW;AACjB,eAAK,YAAY,KAAK,MAAM,IAAI,OAAO,MAAM,CAAC;AAG9C,eAAK,UAAU,CAAC;AAAA,QAClB;AAAA,QAGA,SAAS,IAAI;AAAA,QAGb,QAAQ;AAAA;AAAA,UAEN,QAAQ,WAAW;AAEjB,qBAASA,KAAI,KAAK,QAAQ,SAAS,GAAGA,MAAK,GAAGA;AAC5C,kBAAI,KAAK,QAAQA,EAAC;AAChB,qBAAK,QAAQA,EAAC,EAAE,OAAO;AAC3B,iBAAK,UAAU,CAAC;AAGhB,gBAAI,QAAQ,UAAU,OAAO,KAAK,IAAI;AAEtC,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,QAGA,WAAW;AAAA;AAAA,UAET,MAAM,WAAW;AACf,mBAAO,KAAK,KAAK,EAAE,IAAI,IAAI,IAAI,MAAI;AAAA,UACrC;AAAA,QACF;AAAA,MACF,CAAC;AAGD,UAAI,OAAO,IAAI,SAAS;AAAA;AAAA,QAEtB,UAAU,SAAS,SAAS;AAE1B,eAAK,SAAS,mBAAmB,IAAI,OAAO,UAAU,KAAK,OAAO,EAAE,KAAK,EAAE,IAAI,OAAO;AAGtF,eAAK,OAAO,QAAQ,KAAK,IAAI;AAG7B,iBAAO,KAAK,KAAK,QAAQ,WAAW,KAAK,OAAO,KAAK,IAAI,IAAI,IAAI;AAAA,QACnE;AAAA,QAEA,QAAQ,WAAW;AACjB,iBAAO,KAAK;AACZ,iBAAO,KAAK,KAAK,QAAQ,IAAI;AAAA,QAC/B;AAAA,MAEF,CAAC;AAED,UAAI,WAAW,IAAI,OAAO;AAAA;AAAA,QAExB,QAAQ,WAAW;AACjB,eAAK,YAAY,KAAK,MAAM,IAAI,OAAO,UAAU,CAAC;AAGlD,eAAK,UAAU,CAAC;AAAA,QAClB;AAAA,QAGA,SAAS,IAAI;AAAA,QAGb,QAAQ;AAAA;AAAA,UAEN,QAAQ,WAAW;AAEjB,qBAASA,KAAI,KAAK,QAAQ,SAAS,GAAGA,MAAK,GAAGA;AAC5C,kBAAI,KAAK,QAAQA,EAAC;AAChB,qBAAK,QAAQA,EAAC,EAAE,OAAO;AAC3B,iBAAK,UAAU,CAAC;AAGhB,iBAAK,OAAO,EAAE,cAAc,IAAI;AAEhC,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,QAGA,WAAW;AAAA;AAAA,UAET,MAAM,WAAW;AACf,mBAAO,KAAK,KAAK,EAAE,IAAI,IAAI,IAAI,UAAQ;AAAA,UACzC;AAAA,QACF;AAAA,MACF,CAAC;AAGD,UAAI,OAAO,IAAI,SAAS;AAAA;AAAA,QAEtB,UAAU,SAAS,SAAS;AAE1B,eAAK,UAAU,mBAAmB,IAAI,WAAW,UAAU,KAAK,OAAO,EAAE,KAAK,EAAE,IAAI,OAAO;AAG3F,eAAK,QAAQ,QAAQ,KAAK,IAAI;AAG9B,iBAAO,KAAK,KAAK,aAAa,WAAW,KAAK,QAAQ,KAAK,IAAI,IAAI,IAAI;AAAA,QACzE;AAAA,QAEA,QAAQ,WAAW;AACjB,iBAAO,KAAK;AACZ,iBAAO,KAAK,KAAK,aAAa,IAAI;AAAA,QACpC;AAAA,MAEF,CAAC;AACD,UAAI,WAAW,IAAI,OAAO;AAAA;AAAA,QAExB,QAAQ,SAAS,MAAM;AACrB,eAAK,YAAY,KAAK,MAAM,IAAI,OAAO,OAAO,UAAU,CAAC;AAGzD,eAAK,OAAO;AAAA,QACd;AAAA,QAGA,SAAS,IAAI;AAAA,QAGb,QAAQ;AAAA;AAAA,UAEN,IAAI,SAAS,QAAQ,OAAO,SAAS;AACnC,mBAAO,KAAK,IAAI,IAAI,IAAI,MAAI,EAAE,OAAO,QAAQ,OAAO,OAAO;AAAA,UAC7D;AAAA,UAEA,QAAQ,SAAS,OAAO;AAEtB,iBAAK,MAAM;AAGX,gBAAI,OAAO,SAAS;AAClB,oBAAM,KAAK,MAAM,IAAI;AAEvB,mBAAO;AAAA,UACT;AAAA,UAEA,MAAM,WAAW;AACf,mBAAO,UAAU,KAAK,GAAG,IAAI;AAAA,UAC/B;AAAA,UAEA,UAAU,WAAW;AACnB,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,UAEA,MAAM,SAAS,GAAG,GAAG,GAAG;AACtB,gBAAG,KAAK;AAAa,kBAAI;AACzB,mBAAO,IAAI,UAAU,UAAU,KAAK,KAAK,MAAM,GAAG,GAAG,CAAC;AAAA,UACxD;AAAA,QACF;AAAA,QAGA,WAAW;AAAA;AAAA,UAET,UAAU,SAAS,MAAM,OAAO;AAC9B,mBAAO,KAAK,KAAK,EAAE,SAAS,MAAM,KAAK;AAAA,UACzC;AAAA,QACF;AAAA,MACF,CAAC;AAGD,UAAI,OAAO,IAAI,UAAU,IAAI,IAAI;AAAA;AAAA,QAE/B,MAAM,SAAS,GAAG,GAAG;AACnB,kBAAQ,KAAK,WAAW,MAAM,QAAQ,WACpC,KAAK,KAAK,EAAE,IAAI,IAAI,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,IAAI,OAAO,CAAC,EAAE,CAAC,IAC1D,KAAK,KAAK,EAAE,IAAI,IAAI,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,IAAI,OAAO,CAAC,EAAE,CAAC;AAAA,QAC9D;AAAA,QAEA,IAAI,SAAS,GAAG,GAAG;AACjB,kBAAQ,KAAK,WAAW,MAAM,QAAQ,WACpC,KAAK,KAAK,EAAE,IAAI,IAAI,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,IAAI,OAAO,CAAC,EAAE,CAAC,IAC1D,KAAK,KAAK,EAAE,IAAI,IAAI,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,IAAI,OAAO,CAAC,EAAE,CAAC;AAAA,QAC9D;AAAA,MACF,CAAC;AAGD,UAAI,OAAO,IAAI,MAAM;AAAA;AAAA,QAEnB,UAAU,SAAS,MAAM,OAAO;AAC9B,iBAAO,KAAK,IAAI,IAAI,IAAI,SAAS,IAAI,CAAC,EAAE,OAAO,KAAK;AAAA,QACtD;AAAA,MAEF,CAAC;AAED,UAAI,OAAO,IAAI,OAAO;AAAA;AAAA,QAEpB,QAAQ;AAAA,QAGR,SAAS,IAAI;AAAA,QAGb,QAAQ;AAAA;AAAA,UAEN,QAAQ,SAAS,GAAG;AAClB,gBAAI,OAAO,KAAK,YAAY,aAAa,IAAI,QAAQ;AACnD,kBAAI;AAAA,gBACF,QAAS,UAAU,CAAC;AAAA,gBACpB,OAAS,UAAU,CAAC;AAAA,gBACpB,SAAS,UAAU,CAAC;AAAA,cACtB;AAAA,YACF;AAGA,gBAAI,EAAE,WAAW;AAAM,mBAAK,KAAK,gBAAgB,EAAE,OAAO;AAC1D,gBAAI,EAAE,SAAW;AAAM,mBAAK,KAAK,cAAc,EAAE,KAAK;AACtD,gBAAI,EAAE,UAAW;AAAM,mBAAK,KAAK,UAAU,IAAI,IAAI,OAAO,EAAE,MAAM,CAAC;AAEnE,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MAEF,CAAC;AAED,UAAI,UAAU,IAAI,OAAO;AAAA;AAAA,QAEvB,QAAQ;AAAA,QAGR,SAAS,IAAI;AAAA,QAGb,QAAQ;AAAA;AAAA,UAEN,MAAM,WAAW;AACf,mBAAO,UAAU,KAAK,GAAG,IAAI;AAAA,UAC/B;AAAA,UAEA,QAAQ,SAAS,OAAO;AAEtB,iBAAK,MAAM;AAGX,gBAAI,OAAO,SAAS;AAClB,oBAAM,KAAK,MAAM,IAAI;AAEvB,mBAAO;AAAA,UACT;AAAA,UAEA,UAAU,WAAW;AACnB,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,UAEA,MAAM,SAAS,GAAG,GAAG,GAAG;AACtB,gBAAG,KAAK;AAAa,kBAAI;AACzB,mBAAO,IAAI,UAAU,UAAU,KAAK,KAAK,MAAM,GAAG,GAAG,CAAC;AAAA,UACxD;AAAA,QAEF;AAAA,QAGA,WAAW;AAAA;AAAA,UAET,SAAS,SAAS,OAAO,QAAQ,OAAO;AACtC,mBAAO,KAAK,KAAK,EAAE,QAAQ,OAAO,QAAQ,KAAK;AAAA,UACjD;AAAA,QACF;AAAA,MACF,CAAC;AAED,UAAI,OAAO,IAAI,MAAM;AAAA;AAAA,QAEnB,SAAS,SAAS,OAAO,QAAQ,OAAO;AACtC,iBAAO,KAAK,IAAI,IAAI,IAAI,SAAO,EAAE,OAAO,KAAK,EAAE,KAAK;AAAA,YAClD,GAAc;AAAA,YACd,GAAc;AAAA,YACd;AAAA,YACA;AAAA,YACA,cAAc;AAAA,UAChB,CAAC;AAAA,QACH;AAAA,MAEF,CAAC;AACD,UAAI,QAAQ,IAAI,OAAO;AAAA;AAAA,QAErB,QAAQ,SAAS,SAAS;AACxB,eAAK,YAAY,KAAK,MAAM,OAAO;AAAA,QACrC;AAAA,QAGA,SAAS,IAAI;AAAA,MAEf,CAAC;AAED,UAAI,OAAO,IAAI,OAAO;AAAA;AAAA,QAEpB,QAAQ,SAAS,SAAS,SAAS;AAEjC,eAAK,YAAY,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC;AAG/C,cAAI;AACF,qBAAS,UAAU,QAAQ;AACzB,kBAAI,OAAO,QAAQ,UAAU,MAAM,MAAM;AACvC,qBAAK,MAAM,IAAI,QAAQ,UAAU,MAAM;AAAA;AAAA,QAC/C;AAAA,QAGA,SAAS,IAAI;AAAA,QAGb,QAAQ;AAAA;AAAA,UAEN,OAAO,SAAS,MAAM;AAEpB,mBAAO,KAAK,KAAK,cAAc;AAC7B,mBAAK,KAAK,YAAY,KAAK,KAAK,SAAS;AAG3C,iBAAK,KAAK,YAAYD,UAAS,eAAe,IAAI,CAAC;AAEnD,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF,CAAC;AAGD,UAAI,OAAO,IAAI,QAAQ;AAAA;AAAA,QAErB,SAAS,SAAS,SAAS,SAAS;AAClC,iBAAO,KAAK,IAAI,IAAI,IAAI,KAAK,SAAS,OAAO,CAAC;AAAA,QAChD;AAAA,MACF,CAAC;AAED,UAAI,SAAS,IAAI,OAAO;AAAA;AAAA,QAEtB,QAAQ;AAAA,QAGR,SAAS,IAAI;AAAA,QAEb,WAAW;AAAA;AAAA,UAET,QAAQ,WAAW;AACjB,mBAAO,KAAK,IAAI,IAAI,IAAI,QAAM;AAAA,UAChC;AAAA,QACF;AAAA,MACF,CAAC;AAED,UAAI,MAAM,IAAI,OAAO;AAAA;AAAA,QAEnB,QAAQ;AAAA,QAGR,SAAS,IAAI;AAAA,QAGb,QAAQ;AAAA;AAAA,UAEN,SAAS,SAAS,SAAS,MAAM;AAE/B,mBAAO,KAAK,KAAK,SAAS,QAAQ,MAAM,MAAM,SAAS,IAAI,KAAK;AAAA,UAClE;AAAA,QACF;AAAA,QAGA,WAAW;AAAA;AAAA,UAET,KAAK,SAAS,SAAS,MAAM;AAC3B,mBAAO,KAAK,IAAI,IAAI,IAAI,KAAG,EAAE,QAAQ,SAAS,IAAI;AAAA,UACpD;AAAA,QACF;AAAA,MACF,CAAC;AACD,UAAI,OAAO,IAAI,OAAO;AAAA;AAAA,QAEpB,QAAQ;AAAA,QAGR,SAAS,IAAI;AAAA,QAGb,WAAW;AAAA;AAAA,UAET,MAAM,SAAS,OAAO,QAAQ;AAC5B,mBAAO,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,EAAE,KAAK,OAAO,MAAM;AAAA,UACpD;AAAA,QACF;AAAA,MACF,CAAC;AACD,UAAI,SAAS,IAAI,OAAO;AAAA;AAAA,QAEtB,QAAQ;AAAA,QAGR,SAAS,IAAI;AAAA,QAGb,WAAW;AAAA;AAAA,UAET,QAAQ,SAASO,OAAM;AACrB,mBAAO,KAAK,IAAI,IAAI,IAAI,QAAM,EAAE,GAAG,IAAI,IAAI,OAAOA,KAAI,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA,UAC9E;AAAA,QACF;AAAA,MACF,CAAC;AAED,UAAI,OAAO,IAAI,QAAQ,IAAI,IAAI;AAAA;AAAA,QAE7B,IAAI,SAAS,IAAI;AACf,iBAAO,KAAK,KAAK,KAAK,EAAE;AAAA,QAC1B;AAAA,QAEA,IAAI,SAAS,IAAI;AACf,iBAAO,KAAK,GAAG,EAAE;AAAA,QACnB;AAAA,MACF,CAAC;AAED,UAAI,UAAU,IAAI,OAAO;AAAA;AAAA,QAEvB,QAAQ;AAAA,QAGR,SAAS,IAAI;AAAA,QAGb,WAAW;AAAA;AAAA,UAET,SAAS,SAAS,OAAO,QAAQ;AAC/B,mBAAO,KAAK,IAAI,IAAI,IAAI,SAAO,EAAE,KAAK,OAAO,MAAM,EAAE,KAAK,GAAG,CAAC;AAAA,UAChE;AAAA,QACF;AAAA,MACF,CAAC;AAED,UAAI,OAAO,IAAI,SAAS,IAAI,MAAM,IAAI,IAAI;AAAA;AAAA,QAExC,IAAI,SAAS,IAAI;AACf,iBAAO,KAAK,KAAK,MAAM,EAAE;AAAA,QAC3B;AAAA,QAEA,IAAI,SAAS,IAAI;AACf,iBAAO,KAAK,KAAK,MAAM,EAAE;AAAA,QAC3B;AAAA,MACF,CAAC;AAGD,UAAI,OAAO,IAAI,QAAQ,IAAI,SAAS;AAAA;AAAA,QAEhC,GAAG,SAAS,GAAG;AACb,iBAAO,KAAK,OAAO,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC;AAAA,QAClE;AAAA,QAEA,GAAG,SAAS,GAAG;AACb,iBAAO,KAAK,OAAO,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC;AAAA,QAClE;AAAA,QAEA,IAAI,SAAS,GAAG;AACd,iBAAO,KAAK,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,MAAM,CAAC;AAAA,QACxD;AAAA,QAEA,IAAI,SAAS,GAAG;AACd,iBAAO,KAAK,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,MAAM,CAAC;AAAA,QACxD;AAAA,QAEA,OAAO,SAAS,OAAO;AACrB,iBAAO,SAAS,OAAO,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,OAAO,KAAK,EAAE,OAAO,CAAC,CAAC;AAAA,QAChF;AAAA,QAEA,QAAQ,SAAS,QAAQ;AACvB,iBAAO,UAAU,OAAO,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,OAAO,MAAM,EAAE,OAAO,CAAC,CAAC;AAAA,QAClF;AAAA,QAEA,MAAM,SAAS,OAAO,QAAQ;AAC5B,cAAI,IAAI,iBAAiB,MAAM,OAAO,MAAM;AAE5C,iBAAO,KACJ,GAAG,IAAI,IAAI,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,EACpC,GAAG,IAAI,IAAI,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAAA,QAC1C;AAAA,MACJ,CAAC;AACD,UAAI,OAAO,IAAI,OAAO;AAAA;AAAA,QAEpB,QAAQ;AAAA,QAGR,SAAS,IAAI;AAAA,QAGb,QAAQ;AAAA;AAAA,UAEN,OAAO,WAAW;AAChB,mBAAO,IAAI,IAAI,WAAW;AAAA,cACxB,CAAE,KAAK,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,CAAE;AAAA,cACnC,CAAE,KAAK,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,CAAE;AAAA,YACrC,CAAC;AAAA,UACH;AAAA,UAEA,MAAM,SAAS,IAAI,IAAI,IAAI,IAAI;AAC7B,gBAAI,MAAM;AACR,qBAAO,KAAK,MAAM;AAAA,qBACX,OAAO,OAAO;AACrB,mBAAK,EAAE,IAAQ,IAAQ,IAAQ,GAAO;AAAA;AAEtC,mBAAK,IAAI,IAAI,WAAW,EAAE,EAAE,OAAO;AAErC,mBAAO,KAAK,KAAK,EAAE;AAAA,UACrB;AAAA,UAEA,MAAM,SAAS,GAAG,GAAG;AACnB,mBAAO,KAAK,KAAK,KAAK,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,OAAO,CAAC;AAAA,UACnD;AAAA,UAEA,MAAM,SAAS,OAAO,QAAQ;AAC5B,gBAAI,IAAI,iBAAiB,MAAM,OAAO,MAAM;AAE5C,mBAAO,KAAK,KAAK,KAAK,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;AAAA,UAChE;AAAA,QACF;AAAA,QAGA,WAAW;AAAA;AAAA,UAET,MAAM,SAAS,IAAI,IAAI,IAAI,IAAI;AAG7B,mBAAO,IAAI,KAAK,UAAU,KAAK;AAAA,cAC7B,KAAK,IAAI,IAAI,IAAI,MAAI;AAAA,cACrB,MAAM,OAAO,CAAC,IAAI,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,YAC7C;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAED,UAAI,WAAW,IAAI,OAAO;AAAA;AAAA,QAExB,QAAQ;AAAA,QAGR,SAAS,IAAI;AAAA,QAGb,WAAW;AAAA;AAAA,UAET,UAAU,SAAS,GAAG;AAEpB,mBAAO,KAAK,IAAI,IAAI,IAAI,UAAQ,EAAE,KAAK,KAAK,IAAI,IAAI,YAAU;AAAA,UAChE;AAAA,QACF;AAAA,MACF,CAAC;AAED,UAAI,UAAU,IAAI,OAAO;AAAA;AAAA,QAEvB,QAAQ;AAAA,QAGR,SAAS,IAAI;AAAA,QAGb,WAAW;AAAA;AAAA,UAET,SAAS,SAAS,GAAG;AAEnB,mBAAO,KAAK,IAAI,IAAI,IAAI,SAAO,EAAE,KAAK,KAAK,IAAI,IAAI,YAAU;AAAA,UAC/D;AAAA,QACF;AAAA,MACF,CAAC;AAGD,UAAI,OAAO,IAAI,UAAU,IAAI,SAAS;AAAA;AAAA,QAEpC,OAAO,WAAW;AAChB,iBAAO,KAAK,WAAW,KAAK,SAAS,IAAI,IAAI,WAAW,KAAK,KAAK,QAAQ,CAAC;AAAA,QAC7E;AAAA,QAEA,MAAM,SAAS,GAAG;AAChB,iBAAQ,KAAK,OACX,KAAK,MAAM,IACX,KAAK,MAAM,EAAE,KAAK,UAAU,OAAO,KAAK,WAAW,IAAK,KAAK,SAAS,IAAI,IAAI,WAAW,CAAC,CAAE;AAAA,QAChG;AAAA,QAEA,OAAO,WAAW;AAChB,iBAAO,KAAK;AACZ,iBAAO;AAAA,QACT;AAAA,QAEA,MAAM,SAAS,GAAG,GAAG;AACnB,iBAAO,KAAK,KAAK,UAAU,KAAK,MAAM,EAAE,KAAK,GAAG,CAAC,CAAC;AAAA,QACpD;AAAA,QAEA,MAAM,SAAS,OAAO,QAAQ;AAC5B,cAAI,IAAI,iBAAiB,MAAM,OAAO,MAAM;AAE5C,iBAAO,KAAK,KAAK,UAAU,KAAK,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC;AAAA,QACjE;AAAA,MAEF,CAAC;AAGD,UAAI,OAAO,IAAI,MAAM,IAAI,UAAU,IAAI,SAAS;AAAA;AAAA,QAE9C,YAAa,IAAI;AAAA,QAEjB,GAAG,SAAS,GAAG;AACb,iBAAO,KAAK,OAAO,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,EAAE,CAAC;AAAA,QAC/D;AAAA,QAEA,GAAG,SAAS,GAAG;AACb,iBAAO,KAAK,OAAO,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK,KAAK,EAAE,GAAG,CAAC;AAAA,QAC/D;AAAA,QAEA,OAAO,SAAS,OAAO;AACrB,cAAI,IAAI,KAAK,KAAK;AAElB,iBAAO,SAAS,OAAO,EAAE,QAAQ,KAAK,KAAK,OAAO,EAAE,MAAM;AAAA,QAC5D;AAAA,QAEA,QAAQ,SAAS,QAAQ;AACvB,cAAI,IAAI,KAAK,KAAK;AAElB,iBAAO,UAAU,OAAO,EAAE,SAAS,KAAK,KAAK,EAAE,OAAO,MAAM;AAAA,QAC9D;AAAA,MACF,CAAC;AACD,UAAI,OAAO,IAAI,OAAO;AAAA;AAAA,QAEpB,QAAQ;AAAA,QAGR,SAAS,IAAI;AAAA,QAGb,QAAQ;AAAA;AAAA,UAEN,YAAa,IAAI;AAAA,UAEjB,OAAO,WAAW;AAChB,mBAAO,KAAK,WAAW,KAAK,SAAS,IAAI,IAAI,UAAU,KAAK,KAAK,GAAG,CAAC;AAAA,UACvE;AAAA,UAEA,MAAM,SAAS,GAAG;AAChB,mBAAQ,KAAK,OACX,KAAK,MAAM,IACX,KAAK,MAAM,EAAE,KAAK,KAAK,OAAO,KAAK,WAAW,IAAK,KAAK,SAAS,IAAI,IAAI,UAAU,CAAC,CAAE;AAAA,UAC1F;AAAA,UAEA,OAAO,WAAW;AAChB,mBAAO,KAAK;AACZ,mBAAO;AAAA,UACT;AAAA,UAEA,MAAM,SAAS,GAAG,GAAG;AACnB,mBAAO,KAAK,KAAK,KAAK,KAAK,MAAM,EAAE,KAAK,GAAG,CAAC,CAAC;AAAA,UAC/C;AAAA,UAEA,GAAG,SAAS,GAAG;AACb,mBAAO,KAAK,OAAO,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,EAAE,CAAC;AAAA,UAC/D;AAAA,UAEA,GAAG,SAAS,GAAG;AACb,mBAAO,KAAK,OAAO,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK,KAAK,EAAE,GAAG,CAAC;AAAA,UAC/D;AAAA,UAEA,MAAM,SAAS,OAAO,QAAQ;AAC5B,gBAAI,IAAI,iBAAiB,MAAM,OAAO,MAAM;AAE5C,mBAAO,KAAK,KAAK,KAAK,KAAK,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC;AAAA,UAC5D;AAAA,UAEA,OAAO,SAAS,OAAO;AACrB,mBAAO,SAAS,OAAO,KAAK,KAAK,EAAE,QAAQ,KAAK,KAAK,OAAO,KAAK,KAAK,EAAE,MAAM;AAAA,UAChF;AAAA,UAEA,QAAQ,SAAS,QAAQ;AACvB,mBAAO,UAAU,OAAO,KAAK,KAAK,EAAE,SAAS,KAAK,KAAK,KAAK,KAAK,EAAE,OAAO,MAAM;AAAA,UAClF;AAAA,QAEF;AAAA,QAGA,WAAW;AAAA;AAAA,UAET,MAAM,SAAS,GAAG;AAEhB,mBAAO,KAAK,IAAI,IAAI,IAAI,MAAI,EAAE,KAAK,KAAK,IAAI,IAAI,WAAS;AAAA,UAC3D;AAAA,QACF;AAAA,MACF,CAAC;AAED,UAAI,QAAQ,IAAI,OAAO;AAAA;AAAA,QAErB,QAAQ;AAAA,QAGR,SAAS,IAAI;AAAA,QAGb,QAAQ;AAAA;AAAA,UAEN,MAAM,SAAS,KAAK;AAClB,gBAAI,CAAC;AAAK,qBAAO;AAEjB,gBAAI,OAAO,MACP,MAAO,IAAIR,QAAO,MAAM;AAG5B,gBAAI,GAAG,KAAK,QAAQ,WAAW;AAC7B,kBAAI,IAAI,GAAG;AAEX,kBAAI,IAAI,KAAK,OAAO,IAAI,OAAO;AAE/B,kBAAG,MAAM;AAAM;AAGf,kBAAI,KAAK,MAAM,KAAK,KAAK,KAAK,OAAO,KAAK;AACxC,qBAAK,KAAK,IAAI,OAAO,IAAI,MAAM;AAGjC,kBAAI,KAAK,EAAE,MAAM,KAAK,KAAK,EAAE,OAAO,KAAK;AACvC,kBAAE,KAAK,KAAK,MAAM,GAAG,KAAK,OAAO,CAAC;AAGpC,kBAAI,OAAO,KAAK,YAAY;AAC1B,qBAAK,QAAQ,KAAK,MAAM;AAAA,kBACtB,OAAQ,IAAI;AAAA,kBACZ,QAAQ,IAAI;AAAA,kBACZ,OAAQ,IAAI,QAAQ,IAAI;AAAA,kBACxB;AAAA,gBACF,CAAC;AAAA,YACL,CAAC;AAED,gBAAI,GAAG,KAAK,SAAS,SAAS,GAAE;AAC9B,kBAAI,IAAI,GAAG;AAEX,kBAAI,OAAO,KAAK,WAAW,YAAW;AAClC,qBAAK,OAAO,KAAK,MAAM,CAAC;AAAA,cAC5B;AAAA,YACF,CAAC;AAED,mBAAO,KAAK,KAAK,QAAS,IAAI,MAAM,KAAK,MAAM,KAAM,IAAI,KAAK;AAAA,UAChE;AAAA,UAEA,QAAQ,SAAS,QAAQ;AACvB,iBAAK,UAAU;AACf,mBAAO;AAAA,UACT;AAAA,UAEA,OAAO,SAAS,OAAO;AACrB,iBAAK,SAAS;AACd,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,QAGA,WAAW;AAAA;AAAA,UAET,OAAO,SAAS,QAAQ,OAAO,QAAQ;AACrC,mBAAO,KAAK,IAAI,IAAI,IAAI,OAAK,EAAE,KAAK,MAAM,EAAE,KAAK,SAAS,GAAG,UAAU,SAAS,CAAC;AAAA,UACnF;AAAA,QACF;AAAA,MAEF,CAAC;AACD,UAAI,OAAO,IAAI,OAAO;AAAA;AAAA,QAEpB,QAAQ,WAAW;AACjB,eAAK,YAAY,KAAK,MAAM,IAAI,OAAO,MAAM,CAAC;AAE9C,eAAK,IAAI,UAAU,IAAI,IAAI,OAAO,GAAG;AACrC,eAAK,WAAW;AAChB,eAAK,SAAW;AAGhB,eAAK,KAAK,eAAe,IAAI,SAAS,MAAM,aAAa,CAAC;AAAA,QAC5D;AAAA,QAGA,SAAS,IAAI;AAAA,QAGb,QAAQ;AAAA;AAAA,UAEN,GAAG,SAAS,GAAG;AAEb,gBAAI,KAAK;AACP,qBAAO,KAAK,KAAK,GAAG;AAEtB,mBAAO,KAAK,KAAK,KAAK,CAAC;AAAA,UACzB;AAAA,UAEA,GAAG,SAAS,GAAG;AACb,gBAAI,KAAK,KAAK,KAAK,GAAG,GAClB,IAAK,OAAO,OAAO,WAAW,KAAK,KAAK,KAAK,EAAE,IAAI;AAGvD,gBAAI,KAAK;AACP,qBAAO,OAAO,OAAO,WAAW,KAAK,IAAI;AAE3C,mBAAO,KAAK,KAAK,KAAK,OAAO,EAAE,QAAQ,MAAM,WAAW,IAAI,IAAI,CAAC;AAAA,UACnE;AAAA,UAEA,IAAI,SAAS,GAAG;AACd,mBAAO,KAAK,OAAO,KAAK,KAAK,EAAE,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,EAAE,QAAQ,CAAC;AAAA,UACtE;AAAA,UAEA,IAAI,SAAS,GAAG;AACd,mBAAO,KAAK,OAAO,KAAK,KAAK,EAAE,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,EAAE,SAAS,CAAC;AAAA,UACvE;AAAA,UAEA,MAAM,SAAS,MAAM;AAEnB,gBAAI,OAAO,SAAS,aAAY;AAC9B,kBAAI,OAAO;AACX,kBAAI,WAAW,KAAK,KAAK;AACzB,uBAAQE,KAAI,GAAG,MAAM,SAAS,QAAQA,KAAI,KAAK,EAAEA,IAAE;AAGjD,oBAAGA,MAAK,KAAK,SAASA,EAAC,EAAE,YAAY,KAAK,IAAI,MAAM,SAASA,EAAC,CAAC,EAAE,IAAI,YAAY,MAAK;AACpF,0BAAQ;AAAA,gBACV;AAGA,wBAAQ,SAASA,EAAC,EAAE;AAAA,cACtB;AAEA,qBAAO;AAAA,YACT;AAGA,iBAAK,MAAM,EAAE,MAAM,IAAI;AAEvB,gBAAI,OAAO,SAAS,YAAY;AAE9B,mBAAK,KAAK,MAAM,IAAI;AAAA,YAEtB,OAAO;AAEL,qBAAO,KAAK,MAAM,IAAI;AAGtB,uBAASA,KAAI,GAAGC,MAAK,KAAK,QAAQD,KAAIC,KAAID;AACxC,qBAAK,MAAM,KAAKA,EAAC,CAAC,EAAE,QAAQ;AAAA,YAChC;AAGA,mBAAO,KAAK,MAAM,KAAK,EAAE,QAAQ;AAAA,UACnC;AAAA,UAEA,MAAM,SAASM,OAAM;AACnB,mBAAO,KAAK,KAAK,aAAaA,KAAI,EAAE,QAAQ;AAAA,UAC9C;AAAA,UAEA,SAAS,SAAS,OAAO;AAEvB,gBAAI,SAAS;AACX,qBAAO,KAAK,IAAI;AAGlB,iBAAK,IAAI,UAAU,IAAI,IAAI,OAAO,KAAK;AAEvC,mBAAO,KAAK,QAAQ;AAAA,UACtB;AAAA,UAEA,OAAO,WAAW;AAChB,gBAAI,QAAQ,KAAK,YAAY,KAAK,SAAS,KAAK,MAAM;AAGtD,gBAAI,QAAQ,IAAI,MAAM,IAAI,IAAI,MAAM,kBAAkB,KAAK,UAAU,GAAG,SAAS,IAAG;AAClF,qBAAO,IAAI,MAAM,EAAE;AAAA,YACrB,CAAC;AAGD,mBAAO,IAAI,IAAI,IAAI,KAAK;AAAA,UAC1B;AAAA,UAEA,SAAS,SAAS,SAAS;AAEzB,gBAAI,OAAO,WAAW;AACpB,mBAAK,WAAW;AAGlB,gBAAI,KAAK,UAAU;AACjB,kBAAI,OAAO,MACP,kBAAkB,GAClB,KAAK,KAAK,IAAI,UAAU,IAAI,IAAI,OAAO,KAAK,KAAK,WAAW,CAAC;AAEjE,mBAAK,MAAM,EAAE,KAAK,WAAW;AAC3B,oBAAI,KAAK,IAAI,UAAU;AACrB,sBAAI,CAAC,KAAK,SAAS;AACjB,yBAAK,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC;AAC/B,sBAAG,KAAK,KAAK,KAAK,MAAM;AACtB,uCAAmB;AAAA,kBACrB,OAAK;AACH,yBAAK,KAAK,MAAM,KAAK,eAAe;AACpC,sCAAkB;AAAA,kBACpB;AAAA,gBACF;AAAA,cACF,CAAC;AAED,mBAAK,KAAK,SAAS;AAAA,YACrB;AAEA,mBAAO;AAAA,UACT;AAAA,UAEA,OAAO,SAAS,OAAO;AACrB,iBAAK,SAAS,CAAC,CAAC;AAChB,mBAAO;AAAA,UACT;AAAA,UAEA,SAAS,SAAS,GAAE;AAClB,iBAAK,MAAM;AACX,iBAAK,IAAI,UAAU,IAAI,IAAI,OAAO,EAAE,WAAW,GAAG;AAClD,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,QAGA,WAAW;AAAA;AAAA,UAET,MAAM,SAAS,MAAM;AACnB,mBAAO,KAAK,IAAI,IAAI,IAAI,MAAI,EAAE,KAAK,IAAI;AAAA,UACzC;AAAA,UAEA,OAAO,SAAS,MAAM;AACpB,mBAAO,KAAK,IAAI,IAAI,IAAI,MAAI,EAAE,MAAM,IAAI;AAAA,UAC1C;AAAA,QACF;AAAA,MAEF,CAAC;AAED,UAAI,QAAQ,IAAI,OAAO;AAAA;AAAA,QAErB,QAAQ;AAAA,QAGR,SAAS,IAAI;AAAA,QAGb,QAAQ;AAAA;AAAA,UAEN,MAAM,SAAS,MAAM;AACnB,gBAAG,QAAQ;AAAM,qBAAO,KAAK,KAAK,eAAe,KAAK,IAAI,WAAW,OAAO;AAE5E,mBAAO,SAAS,aAAa,KAAK,KAAK,MAAM,IAAI,IAAI,KAAK,MAAM,IAAI;AAEpE,mBAAO;AAAA,UACT;AAAA,UAEA,IAAI,SAAS,IAAI;AACf,mBAAO,KAAK,KAAK,MAAM,EAAE;AAAA,UAC3B;AAAA,UAEA,IAAI,SAAS,IAAI;AACf,mBAAO,KAAK,KAAK,MAAM,EAAE;AAAA,UAC3B;AAAA,UAEA,SAAS,WAAW;AAElB,gBAAI,IAAI,KAAK,OAAO,IAAI,IAAI;AAG5B,iBAAK,IAAI,WAAW;AAGpB,mBAAO,KAAK,GAAG,EAAE,IAAI,UAAU,EAAE,KAAK,WAAW,CAAC,EAAE,KAAK,KAAK,EAAE,EAAE,CAAC;AAAA,UACrE;AAAA,QACF;AAAA,MAEF,CAAC;AAED,UAAI,OAAO,IAAI,MAAM,IAAI,OAAO;AAAA;AAAA,QAE9B,OAAO,SAAS,MAAM;AAEpB,cAAI,KAAK,WAAW;AAClB,iBAAK,MAAM;AAGb,eAAK,KAAK,YAAYP,UAAS,eAAe,IAAI,CAAC;AAEnD,iBAAO;AAAA,QACT;AAAA,QAEA,OAAO,SAAS,MAAM;AACpB,cAAI,QAAS,KAAK,YAAY,KAAK,SAAS,KAAK,MAAM,MACnD,QAAQ,IAAI,IAAI;AAGpB,cAAI,KAAK,WAAW;AAClB,iBAAK,MAAM;AAGb,eAAK,YAAY,MAAM,IAAI;AAE3B,iBAAO,MAAM,KAAK,IAAI;AAAA,QACxB;AAAA,QAEA,OAAO,WAAW;AAChB,cAAI,QAAQ,KAAK,YAAY,KAAK,SAAS,KAAK,MAAM;AAGtD,iBAAO,KAAK,cAAc;AACxB,iBAAK,YAAY,KAAK,SAAS;AAEjC,iBAAO;AAAA,QACT;AAAA,QAEA,QAAQ,WAAW;AACjB,iBAAO,KAAK,KAAK,sBAAsB;AAAA,QACzC;AAAA,MACF,CAAC;AAED,UAAI,WAAW,IAAI,OAAO;AAAA;AAAA,QAExB,QAAQ;AAAA,QAGR,SAAS,IAAI;AAAA,QAGb,QAAQ,IAAI;AAAA,QAGZ,WAAW;AAAA,UACT,YAAY,IAAI;AAAA,UAEhB,MAAM,SAAS,GAAG;AAEhB,gBAAI,OAAQ,IAAI,IAAI,YAChB,QAAQ,KAAK,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;AAGpC,mBAAO,KAAK,KAAK,cAAc;AAC7B,mBAAK,KAAK,YAAY,KAAK,KAAK,UAAU;AAG5C,iBAAK,KAAK,YAAY,KAAK,IAAI;AAG/B,iBAAK,KAAK,QAAQ,MAAM,OAAO,IAAI,KAAK;AAExC,mBAAO;AAAA,UACT;AAAA,UAEA,OAAO,WAAW;AAChB,gBAAI,QAAQ,KAAK,MAAM;AAEvB,mBAAO,QAAQ,MAAM,MAAM,IAAI;AAAA,UACjC;AAAA,UAEA,MAAM,SAAS,GAAG;AAChB,gBAAI,QAAQ,KAAK,MAAM,GACnB,YAAY;AAEhB,gBAAI,OAAO;AACT,0BAAY,MAAM,KAAK,CAAC;AAAA,YAC1B;AAEA,mBAAQ,KAAK,OAAQ,YAAY;AAAA,UACnC;AAAA,UAEA,OAAO,WAAW;AAChB,gBAAI,OAAO,KAAK,SAAS;AAEzB,gBAAI;AACF,qBAAO,KAAK,UAAU,MAAM;AAAA,UAChC;AAAA,UAEA,UAAU,WAAW;AACnB,gBAAI,KAAK,KAAK,cAAc,KAAK,KAAK,WAAW,YAAY;AAC3D,qBAAO,IAAI,MAAM,KAAK,KAAK,UAAU;AAAA,UACzC;AAAA,QACF;AAAA,MACF,CAAC;AAED,UAAI,SAAS,IAAI,OAAO;AAAA;AAAA,QAEtB,QAAQ,WAAW;AACjB,eAAK,YAAY,KAAK,MAAM,IAAI,OAAO,KAAK,CAAC;AAE7C,eAAK,MAAM,YAAY,SAAS;AAAA,QAClC;AAAA,QAGA,SAAS,IAAI;AAAA,QAGb,WAAW;AAAA;AAAA,UAET,QAAQ,WAAW;AACjB,mBAAO,KAAK,IAAI,IAAI,IAAI,QAAM;AAAA,UAChC;AAAA,QACF;AAAA,MACF,CAAC;AACD,UAAI,IAAI,IAAI,OAAO;AAAA;AAAA,QAEjB,QAAQ;AAAA,QAGR,SAAS,IAAI;AAAA,QAGb,QAAQ;AAAA;AAAA,UAEN,IAAI,SAAS,KAAK;AAChB,mBAAO,KAAK,KAAK,QAAQ,KAAK,IAAI,KAAK;AAAA,UACzC;AAAA,UAEA,MAAM,SAAS,QAAQ;AACrB,mBAAO,KAAK,KAAK,QAAQ,QAAQ,IAAI,KAAK;AAAA,UAC5C;AAAA,UAEA,QAAQ,SAAS,QAAQ;AACvB,mBAAO,KAAK,KAAK,UAAU,MAAM;AAAA,UACnC;AAAA,QACF;AAAA,QAGA,WAAW;AAAA;AAAA,UAET,MAAM,SAAS,KAAK;AAClB,mBAAO,KAAK,IAAI,IAAI,IAAI,GAAC,EAAE,GAAG,GAAG;AAAA,UACnC;AAAA,QACF;AAAA,MACF,CAAC;AAED,UAAI,OAAO,IAAI,SAAS;AAAA;AAAA,QAEtB,QAAQ,SAAS,KAAK;AACpB,cAAI,OAAO,IAAI,IAAI;AAEnB,cAAI,OAAO,OAAO;AAChB,gBAAI,KAAK,MAAM,IAAI;AAAA;AAEnB,iBAAK,GAAG,GAAG;AAEb,iBAAO,KAAK,OAAO,EAAE,IAAI,IAAI,EAAE,IAAI,IAAI;AAAA,QACzC;AAAA,MAEF,CAAC;AACD,UAAI,SAAS,IAAI,OAAO;AAAA;AAAA,QAEtB,QAAQ;AAAA,QAGR,SAAS,IAAI;AAAA,QAGb,QAAQ;AAAA;AAAA,UAEN,OAAO,SAAS,OAAO;AACrB,mBAAO,KAAK,KAAK,eAAe,KAAK;AAAA,UACvC;AAAA,UAEA,QAAQ,SAAS,QAAQ;AACvB,mBAAO,KAAK,KAAK,gBAAgB,MAAM;AAAA,UACzC;AAAA,UAEA,KAAK,SAAS,GAAG,GAAG;AAClB,mBAAO,KAAK,KAAK,QAAQ,CAAC,EAAE,KAAK,QAAQ,CAAC;AAAA,UAC5C;AAAA,UAEA,QAAQ,SAAS,OAAO;AAEtB,iBAAK,MAAM;AAGX,gBAAI,OAAO,SAAS;AAClB,oBAAM,KAAK,MAAM,IAAI;AAEvB,mBAAO;AAAA,UACT;AAAA,UAEA,UAAU,WAAW;AACnB,mBAAO,UAAU,KAAK,GAAG,IAAI;AAAA,UAC/B;AAAA,QACF;AAAA,QAGA,WAAW;AAAA,UACT,QAAQ,SAAS,OAAO,QAAQ,OAAO;AAErC,mBAAO,KAAK,KAAK,EAAE,OAAO,OAAO,QAAQ,KAAK;AAAA,UAChD;AAAA,QACF;AAAA,MAEF,CAAC;AAED,UAAI,OAAO,IAAI,MAAM;AAAA;AAAA,QAEnB,QAAQ,SAAS,OAAO,QAAQ,OAAO;AAErC,iBAAO,KAAK,IAAI,IAAI,IAAI,QAAM,EAC3B,KAAK,OAAO,MAAM,EAClB,IAAI,QAAQ,GAAG,SAAS,CAAC,EACzB,QAAQ,GAAG,GAAG,OAAO,MAAM,EAC3B,KAAK,UAAU,MAAM,EACrB,OAAO,KAAK;AAAA,QACjB;AAAA,MAEF,CAAC;AAED,UAAI,OAAO,IAAI,MAAM,IAAI,UAAU,IAAI,SAAS,IAAI,MAAM;AAAA;AAAA,QAExD,QAAQ,SAAS,QAAQ,OAAO,QAAQ,OAAO;AAC7C,cAAI,OAAO,CAAC,QAAQ;AAGpB,cAAI,UAAU;AAAO,iBAAK,KAAK,MAAM;AACrC,iBAAO,KAAK,KAAK,GAAG;AAGpB,mBAAS,UAAU,CAAC,aAAa,IAAI,SACnC,UAAU,CAAC,IACX,KAAK,IAAI,EAAE,OAAO,OAAO,QAAQ,KAAK;AAExC,iBAAO,KAAK,KAAK,MAAM,MAAM;AAAA,QAC/B;AAAA,MAEF,CAAC;AAED,UAAI,QAAQ;AAAA,QACV,QAAQ,CAAC,SAAS,SAAS,WAAW,WAAW,YAAY,cAAc,aAAa,YAAY;AAAA,QACpG,MAAQ,CAAC,SAAS,WAAW,MAAM;AAAA,QACnC,QAAQ,SAAS,GAAG,GAAG;AACrB,iBAAO,KAAK,UAAU,IAAI,IAAI,MAAM;AAAA,QACtC;AAAA,MACF;AAGC,OAAC,QAAQ,QAAQ,EAAE,QAAQ,SAAS,GAAG;AACtC,YAAIC,IAAG,YAAY,CAAC;AAEpB,kBAAU,CAAC,IAAI,SAAS,GAAG;AACzB,cAAI,OAAO,KAAK;AACd,mBAAO;AACT,cAAI,OAAO,KAAK,YAAY,IAAI,MAAM,MAAM,CAAC,KAAM,KAAK,OAAO,EAAE,SAAS;AACxE,iBAAK,KAAK,GAAG,CAAC;AAAA;AAId,iBAAKA,KAAI,MAAM,CAAC,EAAE,SAAS,GAAGA,MAAK,GAAGA;AACpC,kBAAI,EAAE,MAAM,CAAC,EAAEA,EAAC,CAAC,KAAK;AACpB,qBAAK,KAAK,MAAM,OAAO,GAAG,MAAM,CAAC,EAAEA,EAAC,CAAC,GAAG,EAAE,MAAM,CAAC,EAAEA,EAAC,CAAC,CAAC;AAE5D,iBAAO;AAAA,QACT;AAEA,YAAI,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS;AAAA,MAE3C,CAAC;AAED,UAAI,OAAO,IAAI,SAAS,IAAI,IAAI;AAAA;AAAA,QAE9B,QAAQ,SAAS,GAAG,IAAI,IAAI;AAC1B,iBAAO,KAAK,UAAU,EAAE,UAAU,GAAG,IAAQ,GAAO,CAAC;AAAA,QACvD;AAAA,QAEA,MAAM,SAAS,GAAG,GAAG,IAAI,IAAI;AAC3B,iBAAO,UAAU,UAAU,KAAM,UAAU,UAAU,IACnD,KAAK,UAAU,EAAE,MAAM,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,IACzC,KAAK,UAAU,EAAE,OAAO,GAAG,OAAO,GAAG,IAAQ,GAAO,CAAC;AAAA,QACzD;AAAA,QAEA,OAAO,SAAS,GAAG,GAAG,IAAI,IAAI;AAC5B,iBAAO,UAAU,UAAU,KAAM,UAAU,UAAU,IACnD,KAAK,UAAU,EAAE,OAAO,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,IAC1C,KAAK,UAAU,EAAE,QAAQ,GAAG,QAAQ,GAAG,IAAQ,GAAO,CAAC;AAAA,QAC3D;AAAA,QAEA,WAAW,SAAS,GAAG,GAAG;AACxB,iBAAO,KAAK,UAAU,EAAE,GAAM,EAAK,CAAC;AAAA,QACtC;AAAA,QAEA,MAAM,SAAS,GAAG,GAAG;AACnB,cAAI,OAAO,KAAK,WAAW,IAAI;AAC/B,iBAAO,KAAK,UAAU,EAAE,MAAM,KAAK,QAAQ,QAAQ,EAAE,CAAC;AAAA,QACxD;AAAA,QAEA,QAAQ,SAAS,GAAG;AAClB,iBAAO,KAAK,KAAK,aAAa,IAAI,IAAI,OAAO,UAAU,UAAU,IAAI,CAAC,EAAE,MAAM,KAAK,SAAS,IAAI,CAAC,CAAC;AAAA,QACpG;AAAA,QAEA,SAAS,SAAS,OAAO;AACvB,iBAAO,KAAK,KAAK,WAAW,KAAK;AAAA,QACnC;AAAA,QAEA,IAAI,SAAS,GAAG;AACd,iBAAO,KAAK,EAAE,IAAI,IAAI,OAAO,CAAC,EAAE,KAAK,gBAAgB,IAAI,KAAK,IAAI,KAAK,EAAE,CAAC,GAAG,IAAI;AAAA,QACnF;AAAA,QAEA,IAAI,SAAS,GAAG;AACd,iBAAO,KAAK,EAAE,IAAI,IAAI,OAAO,CAAC,EAAE,KAAK,gBAAgB,IAAI,KAAK,IAAI,KAAK,EAAE,CAAC,GAAG,IAAI;AAAA,QACnF;AAAA,QAEA,OAAO,SAAS,GAAG,GAAG;AACpB,iBAAO,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC;AAAA,QACxB;AAAA,MACF,CAAC;AAED,UAAI,OAAO,IAAI,MAAM,IAAI,SAAS,IAAI,QAAQ,IAAI,UAAU,IAAI,IAAI;AAAA;AAAA,QAElE,QAAQ,SAAS,GAAG,GAAG;AACrB,cAAI,QAAQ,KAAK,WAAW,MAAM;AAClC,iBAAO,QAAQ,YAAY,QAAQ,WACjC,KAAK,KAAK,KAAK,IAAI,IAAI,OAAO,CAAC,CAAC,IAChC,KAAK,GAAG,CAAC,EAAE,GAAG,KAAK,OAAO,IAAI,CAAC;AAAA,QACnC;AAAA,MACF,CAAC;AAED,UAAI,OAAO,IAAI,MAAM;AAAA;AAAA,QAEnB,QAAQ,WAAW;AACjB,iBAAO,KAAK,KAAK,eAAe;AAAA,QAClC;AAAA,QAEA,SAAS,SAAS,QAAQ;AACxB,iBAAO,KAAK,KAAK,iBAAiB,MAAM;AAAA,QAC1C;AAAA,MACF,CAAC;AAED,UAAI,OAAO,IAAI,QAAQ,IAAI,MAAM,IAAI,OAAO,IAAI,IAAI;AAAA;AAAA,QAElD,MAAM,SAAS,GAAG,GAAG;AACnB,cAAI,OAAO,KAAK,UAAU;AACxB,iBAAK,KAAK;AAAG,mBAAK,KAAK,GAAG,EAAE,CAAC,CAAC;AAAA,UAChC;AAEA,iBAAO,KAAK,YACR,KAAK,QAAQ,CAAC,IAChB,KAAK,WACH,KAAK,KAAK,eAAe,CAAC,IAC5B,KAAK,UAAU,KAAK,YAAY,KAAK,YAAY,KAAK,aAAa,KAAK,aAAa,KAAK,UACxF,KAAK,KAAK,UAAS,GAAG,CAAC,IACvB,KAAK,KAAK,GAAG,CAAC;AAAA,QACpB;AAAA,MACF,CAAC;AAED,UAAI,MAAM,IAAI,OAAO;AAAA;AAAA,QAEnB,QAAQ,SAAS,SAAS;AACxB,cAAI,mBAAmB,IAAI,KAAK;AAC9B,iBAAK,UAAU,QAAQ,QAAQ,MAAM;AAAA,UACvC,OAAO;AACL,kBAAM,QAAQ,OAAO,IAAI,KAAK,UAAU,UAAU,KAAK,MAAM;AAAA,UAC/D;AAAA,QACF;AAAA,QAGA,QAAQ;AAAA;AAAA,UAEN,KAAK,WAAW;AACd,gBAAIA,IAAGC,KAAI,WAAW,CAAC,EAAE,MAAM,KAAK,SAAS;AAE7C,iBAAKD,KAAI,GAAGC,MAAK,SAAS,QAAQD,KAAIC,KAAID;AACxC,mBAAK,QAAQ,KAAK,SAASA,EAAC,CAAC;AAE/B,mBAAO;AAAA,UACT;AAAA,UAEA,QAAQ,SAAS,SAAS;AACxB,gBAAIA,KAAI,KAAK,MAAM,OAAO;AAG1B,gBAAIA,KAAI;AACN,mBAAK,QAAQ,OAAOA,IAAG,CAAC;AAE1B,mBAAO;AAAA,UACT;AAAA,UAEA,MAAM,SAAS,OAAO;AACpB,qBAASA,KAAI,GAAGC,MAAK,KAAK,QAAQ,QAAQD,KAAIC,KAAID;AAChD,oBAAM,MAAM,KAAK,QAAQA,EAAC,GAAG,CAACA,IAAG,KAAK,OAAO,CAAC;AAEhD,mBAAO;AAAA,UACT;AAAA,UAEA,OAAO,WAAW;AAEhB,iBAAK,UAAU,CAAC;AAEhB,mBAAO;AAAA,UACT;AAAA,UAEA,QAAQ,WAAW;AACjB,mBAAO,KAAK,QAAQ;AAAA,UACtB;AAAA,UAEA,KAAK,SAAS,SAAS;AACrB,mBAAO,KAAK,MAAM,OAAO,KAAK;AAAA,UAChC;AAAA,UAEA,OAAO,SAAS,SAAS;AACvB,mBAAO,KAAK,QAAQ,QAAQ,OAAO;AAAA,UACrC;AAAA,UAEA,KAAK,SAASA,IAAG;AACf,mBAAO,KAAK,QAAQA,EAAC;AAAA,UACvB;AAAA,UAEA,OAAO,WAAW;AAChB,mBAAO,KAAK,IAAI,CAAC;AAAA,UACnB;AAAA,UAEA,MAAM,WAAW;AACf,mBAAO,KAAK,IAAI,KAAK,QAAQ,SAAS,CAAC;AAAA,UACzC;AAAA,UAEA,SAAS,WAAW;AAClB,mBAAO,KAAK;AAAA,UACd;AAAA,UAEA,MAAM,WAAU;AAEd,gBAAI,KAAK,QAAQ,UAAU;AACzB,qBAAO,IAAI,IAAI,KAAK;AAGtB,gBAAI,OAAO,KAAK,QAAQ,CAAC,EAAE,KAAK,KAAK,QAAQ,CAAC,EAAE,IAAI,CAAC;AAErD,iBAAK,KAAK,WAAW;AAEnB,qBAAO,KAAK,MAAM,KAAK,KAAK,KAAK,IAAI,CAAC,CAAC;AAAA,YACzC,CAAC;AAED,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,QAGA,WAAW;AAAA;AAAA,UAET,KAAK,SAAS,SAAS;AACrB,mBAAO,IAAI,IAAI,IAAI,OAAO;AAAA,UAC5B;AAAA,QACF;AAAA,MACF,CAAC;AAED,UAAI,GAAG,MAAM,IAAI,OAAO;AAAA;AAAA,QAEtB,QAAQ,SAASO,MAAK;AAEpB,eAAK,MAAMA;AAAA,QACb;AAAA,MAEF,CAAC;AAGD,UAAI,IAAI,UAAU,WAAW;AAC3B,YAAI,GACA,UAAU,CAAC;AAGf,iBAAQ,KAAK,IAAI,MAAM;AACrB,cAAI,OAAO,IAAI,MAAM,UAAU,CAAC,KAAK,cAAc,OAAO,IAAI,IAAI,UAAU,CAAC,KAAK;AAChF,oBAAQ,KAAK,CAAC;AAGlB,gBAAQ,QAAQ,SAAS,QAAQ;AAC/B,cAAI,IAAI,UAAU,MAAM,IAAI,WAAW;AACrC,qBAASP,KAAI,GAAGC,MAAK,KAAK,QAAQ,QAAQD,KAAIC,KAAID;AAChD,kBAAI,KAAK,QAAQA,EAAC,KAAK,OAAO,KAAK,QAAQA,EAAC,EAAE,MAAM,KAAK;AACvD,qBAAK,QAAQA,EAAC,EAAE,MAAM,EAAE,MAAM,KAAK,QAAQA,EAAC,GAAG,SAAS;AAE5D,mBAAO,UAAU,YAAa,KAAK,OAAO,KAAK,KAAK,IAAI,IAAI,GAAG,IAAI,IAAI,KAAM;AAAA,UAC/E;AAAA,QACF,CAAC;AAGD,kBAAU,CAAC;AAGX,iBAAQ,KAAK,IAAI,GAAG;AAClB,cAAI,OAAO,IAAI,GAAG,UAAU,CAAC,KAAK,cAAc,OAAO,IAAI,GAAG,IAAI,UAAU,CAAC,KAAK;AAChF,oBAAQ,KAAK,CAAC;AAGlB,gBAAQ,QAAQ,SAAS,QAAQ;AAC/B,cAAI,GAAG,IAAI,UAAU,MAAM,IAAI,WAAW;AACxC,qBAASA,KAAI,GAAGC,MAAK,KAAK,IAAI,QAAQ,QAAQD,KAAIC,KAAID;AACpD,mBAAK,IAAI,QAAQA,EAAC,EAAE,GAAG,MAAM,EAAE,MAAM,KAAK,IAAI,QAAQA,EAAC,EAAE,IAAI,SAAS;AAExE,mBAAO;AAAA,UACT;AAAA,QACF,CAAC;AAAA,MACH;AAGA,UAAI,OAAO,IAAI,SAAS;AAAA;AAAA,QAEtB,MAAM,SAAS,GAAG,GAAG,GAAG;AACtB,cAAI,OAAO,KAAK,UAAU;AACxB,iBAAK,KAAK;AACR,mBAAK,KAAK,GAAG,EAAE,CAAC,CAAC;AAAA,UAErB,WAAW,UAAU,SAAS,GAAG;AAC/B,gBAAI;AACF,qBAAO,KAAK,MAAM,KAAK,KAAK,UAAU,CAAC,CAAC;AAAA,YAC1C,SAAQ,GAAG;AACT,qBAAO,KAAK,KAAK,UAAU,CAAC;AAAA,YAC9B;AAAA,UAEF,OAAO;AACL,iBAAK;AAAA,cACH,UAAU;AAAA,cACV,MAAM,OACJ,OACF,MAAM,QAAQ,OAAO,MAAM,YAAY,OAAO,MAAM,WAClD,IACA,KAAK,UAAU,CAAC;AAAA,YACpB;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AACD,UAAI,OAAO,IAAI,SAAS;AAAA;AAAA,QAEtB,UAAU,SAAS,GAAG,GAAG;AAEvB,cAAI,OAAO,UAAU,CAAC,KAAK;AACzB,qBAAS,KAAK;AACZ,mBAAK,SAAS,GAAG,EAAE,CAAC,CAAC;AAAA,mBAGhB,UAAU,UAAU;AAC3B,mBAAO,KAAK,OAAO,EAAE,CAAC;AAAA;AAItB,iBAAK,OAAO,EAAE,CAAC,IAAI;AAErB,iBAAO;AAAA,QACT;AAAA,QAGA,QAAQ,WAAW;AACjB,cAAI,UAAU,UAAU;AACtB,iBAAK,UAAU,CAAC;AAAA;AAEhB,qBAASA,KAAI,UAAU,SAAS,GAAGA,MAAK,GAAGA;AACzC,qBAAO,KAAK,OAAO,EAAE,UAAUA,EAAC,CAAC;AAErC,iBAAO;AAAA,QACT;AAAA,QAGA,QAAQ,WAAW;AACjB,iBAAO,KAAK,YAAY,KAAK,UAAU,CAAC;AAAA,QAC1C;AAAA,MAEF,CAAC;AAED,UAAI,MAAM,SAAS,IAAI;AACrB,YAAI,OAAOD,UAAS,eAAe,gBAAgB,EAAE,KAAK,EAAE;AAC5D,eAAO,IAAI,MAAM,IAAI;AAAA,MACvB;AAGA,UAAI,SAAS,SAAS,OAAO,QAAQ;AACnC,eAAO,IAAI,IAAI;AAAA,UACb,IAAI,MAAM,KAAK,UAAUA,WAAU,iBAAiB,KAAK,GAAG,SAAS,MAAM;AACzE,mBAAO,IAAI,MAAM,IAAI;AAAA,UACvB,CAAC;AAAA,QACH;AAAA,MACF;AAEA,UAAI,OAAO,IAAI,QAAQ;AAAA;AAAA,QAErB,QAAQ,SAAS,OAAO;AACtB,iBAAO,IAAI,OAAO,OAAO,KAAK,IAAI;AAAA,QACpC;AAAA,MAEF,CAAC;AACD,eAAS,eAAe,GAAG,GAAG,GAAG,GAAG;AAClC,eAAO,IAAI,EAAE,QAAQ,IAAI,MAAM,MAAM,IAAI;AAAA,MAC3C;AAGA,eAAS,YAAY,KAAI;AACvB,YAAI,QAAQ,IAAI,MAAM,CAAC;AACvB,iBAAQC,KAAI,MAAM,QAAQA,QAAK;AAC7B,cAAG,MAAM,QAAQ,MAAMA,EAAC,CAAC,GAAE;AACzB,kBAAMA,EAAC,IAAI,YAAY,MAAMA,EAAC,CAAC;AAAA,UACjC;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAGA,eAAS,GAAG,IAAI,KAAI;AAClB,eAAO,cAAc;AAAA,MACvB;AAGA,eAAS,QAAQ,IAAI,UAAU;AAC7B,gBAAQ,GAAG,WAAW,GAAG,mBAAmB,GAAG,qBAAqB,GAAG,sBAAsB,GAAG,yBAAyB,GAAG,kBAAkB,KAAK,IAAI,QAAQ;AAAA,MACjK;AAGA,eAAS,UAAU,GAAG;AACpB,eAAO,EAAE,YAAY,EAAE,QAAQ,SAAS,SAAS,GAAG,GAAG;AACrD,iBAAO,EAAE,YAAY;AAAA,QACvB,CAAC;AAAA,MACH;AAGA,eAAS,WAAW,GAAG;AACrB,eAAO,EAAE,OAAO,CAAC,EAAE,YAAY,IAAI,EAAE,MAAM,CAAC;AAAA,MAC9C;AAGA,eAAS,QAAQ,KAAK;AACpB,eAAO,IAAI,UAAU,IACnB;AAAA,UAAE;AAAA,UACA,IAAI,UAAU,GAAG,CAAC;AAAA,UAAG,IAAI,UAAU,GAAG,CAAC;AAAA,UACvC,IAAI,UAAU,GAAG,CAAC;AAAA,UAAG,IAAI,UAAU,GAAG,CAAC;AAAA,UACvC,IAAI,UAAU,GAAG,CAAC;AAAA,UAAG,IAAI,UAAU,GAAG,CAAC;AAAA,QACzC,EAAE,KAAK,EAAE,IAAI;AAAA,MACjB;AAGA,eAAS,UAAU,MAAM;AACvB,YAAI,MAAM,KAAK,SAAS,EAAE;AAC1B,eAAO,IAAI,UAAU,IAAI,MAAM,MAAM;AAAA,MACvC;AAGA,eAAS,iBAAiB,SAAS,OAAO,QAAQ;AAChD,YAAI,SAAS,QAAQ,UAAU,MAAM;AACnC,cAAI,MAAM,QAAQ,KAAK;AAEvB,cAAI,SAAS;AACX,oBAAQ,IAAI,QAAQ,IAAI,SAAS;AAAA,mBAC1B,UAAU;AACjB,qBAAS,IAAI,SAAS,IAAI,QAAQ;AAAA,QACtC;AAEA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAGA,eAAS,oBAAoB,QAAQ,GAAG,GAAG;AACzC,eAAO;AAAA,UACL,GAAG,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI;AAAA,UACjC,GAAG,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI;AAAA,QACnC;AAAA,MACF;AAGA,eAAS,cAAc,GAAG;AACxB,eAAO,EAAE,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;AAAA,MAChE;AAGA,eAAS,YAAY,QAAQ;AAC3B,YAAI,EAAE,kBAAkB,IAAI;AAC1B,mBAAS,IAAI,IAAI,OAAO,MAAM;AAEhC,eAAO;AAAA,MACT;AAGA,eAAS,aAAa,GAAG,QAAQ;AAC/B,UAAE,KAAK,EAAE,MAAM,OAAO,OAAO,KAAK,EAAE,KAAK,EAAE;AAC3C,UAAE,KAAK,EAAE,MAAM,OAAO,OAAO,KAAK,EAAE,KAAK,EAAE;AAAA,MAC7C;AAGA,eAAS,cAAc,GAAG;AACxB,iBAASA,KAAI,GAAGC,MAAK,EAAE,QAAQ,IAAI,IAAID,KAAIC,KAAID,MAAK;AAClD,eAAK,EAAEA,EAAC,EAAE,CAAC;AAEX,cAAI,EAAEA,EAAC,EAAE,CAAC,KAAK,MAAM;AACnB,iBAAK,EAAEA,EAAC,EAAE,CAAC;AAEX,gBAAI,EAAEA,EAAC,EAAE,CAAC,KAAK,MAAM;AACnB,mBAAK;AACL,mBAAK,EAAEA,EAAC,EAAE,CAAC;AAEX,kBAAI,EAAEA,EAAC,EAAE,CAAC,KAAK,MAAM;AACnB,qBAAK;AACL,qBAAK,EAAEA,EAAC,EAAE,CAAC;AACX,qBAAK;AACL,qBAAK,EAAEA,EAAC,EAAE,CAAC;AAEX,oBAAI,EAAEA,EAAC,EAAE,CAAC,KAAK,MAAM;AACnB,uBAAK;AACL,uBAAK,EAAEA,EAAC,EAAE,CAAC;AACX,uBAAK;AACL,uBAAK,EAAEA,EAAC,EAAE,CAAC;AAEX,sBAAI,EAAEA,EAAC,EAAE,CAAC,KAAK,MAAM;AACnB,yBAAK;AACL,yBAAK,EAAEA,EAAC,EAAE,CAAC;AAAA,kBACb;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,eAAO,IAAI;AAAA,MACb;AAGA,eAAS,YAAY,MAAM;AAEzB,iBAASA,KAAI,KAAK,WAAW,SAAS,GAAGA,MAAK,GAAGA;AAC/C,cAAI,KAAK,WAAWA,EAAC,aAAaF,QAAO;AACvC,wBAAY,KAAK,WAAWE,EAAC,CAAC;AAElC,eAAO,IAAI,MAAM,IAAI,EAAE,GAAG,IAAI,IAAI,KAAK,QAAQ,CAAC;AAAA,MAClD;AAGA,eAAS,QAAQ,GAAG;AAClB,YAAI,EAAE,KAAK,MAAM;AACf,YAAE,IAAS;AACX,YAAE,IAAS;AACX,YAAE,QAAS;AACX,YAAE,SAAS;AAAA,QACb;AAEA,UAAE,IAAK,EAAE;AACT,UAAE,IAAK,EAAE;AACT,UAAE,KAAK,EAAE,IAAI,EAAE;AACf,UAAE,KAAK,EAAE,IAAI,EAAE;AACf,UAAE,KAAK,EAAE,IAAI,EAAE,QAAQ;AACvB,UAAE,KAAK,EAAE,IAAI,EAAE,SAAS;AAExB,eAAO;AAAA,MACT;AAGA,eAAS,gBAAgB,KAAK;AAC5B,YAAI,KAAK,OAAO,IAAI,SAAS,EAAE,MAAM,IAAI,MAAM,SAAS;AAExD,YAAI;AAAG,iBAAO,EAAE,CAAC;AAAA,MACnB;AAIA,eAAS,cAAc,GAAG;AACxB,eAAO,KAAK,IAAI,CAAC,IAAI,QAAQ,IAAI;AAAA,MACnC;AAGA,UAAI,SAAS,SAAS,MAAM,EAAE;AAG9B,UAAI,OAAOF,QAAO,gBAAgB,YAAY;AAE5C,YAAI,kBAAkB,SAAS,OAAO,SAAS;AAC7C,oBAAU,WAAW,EAAE,SAAS,OAAO,YAAY,OAAO,QAAQ,OAAU;AAC5E,cAAI,IAAIC,UAAS,YAAY,aAAa;AAC1C,YAAE,gBAAgB,OAAO,QAAQ,SAAS,QAAQ,YAAY,QAAQ,MAAM;AAC5E,iBAAO;AAAA,QACT;AAEA,wBAAgB,YAAYD,QAAO,MAAM;AAEzC,YAAI,cAAc;AAAA,MACpB,OAAO;AACL,YAAI,cAAcA,QAAO;AAAA,MAC3B;AAGA,OAAC,SAAS,GAAG;AACX,YAAI,WAAW;AACf,YAAI,UAAU,CAAC,OAAO,QAAQ;AAE9B,iBAAQ,IAAI,GAAG,IAAI,QAAQ,UAAU,CAACA,QAAO,uBAAuB,EAAE,GAAG;AACvE,YAAE,wBAAwB,EAAE,QAAQ,CAAC,IAAI,uBAAuB;AAChE,YAAE,uBAAwB,EAAE,QAAQ,CAAC,IAAI,sBAAsB,KACrC,EAAE,QAAQ,CAAC,IAAI,6BAA6B;AAAA,QACxE;AAEA,UAAE,wBAAwB,EAAE,yBAC1B,SAAS,UAAU;AACjB,cAAI,YAAW,oBAAI,KAAK,GAAE,QAAQ;AAClC,cAAI,aAAa,KAAK,IAAI,GAAG,MAAM,WAAW,SAAS;AAEvD,cAAI,KAAK,EAAE,WAAW,WAAW;AAC/B,qBAAS,WAAW,UAAU;AAAA,UAChC,GAAG,UAAU;AAEb,qBAAW,WAAW;AACtB,iBAAO;AAAA,QACT;AAEF,UAAE,uBAAuB,EAAE,wBAAwB,EAAE;AAAA,MAEvD,GAAEA,OAAM;AAER,aAAO;AAAA,IAEP,CAAC;AAAA;AAAA;;;ACh+KD;AAAA;AAAA,aAAS,IAAI,UAAU,IAAI;AACzB,aAAO,GAAG,cAAc,QAAQ;AAAA,IAClC;AAEA,cAAU,OAAO,UAAU,SAAS,UAAU,IAAG;AAC/C,WAAK,MAAM;AACX,aAAO,IAAI,UAAU,EAAE;AAAA,IACzB;AAEA,YAAQ,MAAM,SAAS,UAAU,IAAG;AAClC,WAAK,MAAM;AACX,aAAO,GAAG,iBAAiB,QAAQ;AAAA,IACrC;AAEA,YAAQ,SAAS,SAAS,KAAI;AAC5B,UAAI,CAAC,IAAI;AAAK,cAAM,IAAI,MAAM,wBAAwB;AACtD,UAAI,CAAC,IAAI;AAAK,cAAM,IAAI,MAAM,wBAAwB;AACtD,YAAM,IAAI;AACV,cAAQ,MAAM,IAAI;AAClB,aAAO;AAAA,IACT;AAAA;AAAA;;;ACpBA;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA,QAAI,SAAS;AAEb,WAAO,UAAU;AAAA,MACf,mBAAmB,SAAS;AAAA,MAC5B,sBAAsB,SAAS;AAAA,MAC/B,qBAAqB,SAAS;AAAA,MAC9B,uBAAuB,SAAS;AAAA,MAChC,wBAAwB,SAAS;AAAA,MACjC,wBAAwB,SAAS;AAAA,MACjC,iBAAiB,SAAS;AAAA,MAC1B,uBAAuB,SAAS;AAAA,MAChC,sBAAsB,SAAS;AAAA,MAC/B,gCAAgC,SAAS;AAAA,MACzC,iCAAiC,SAAS;AAAA,MAC1C,8BAA8B,SAAS;AAAA,MACvC,+BAA+B,SAAS;AAAA,MACxC,yBAAyB,SAAS;AAAA,IACpC;AAAA;AAAA;;;ACjBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,SAAS,QAAQ,KAAK;AACpB,SAAO,MAAM,UAAU,OAAO,MAAM,CAAC,GAAG,GAAG;AAC7C;AAIA,SAAS,YAAY,KAAK;AACxB,SAAO,QAAQ;AACjB;AACA,SAAS,UAAU,KAAK;AACtB,SAAO,QAAQ;AACjB;AACA,SAAS,MAAM,KAAK;AAClB,SAAO,OAAO;AAChB;AACA,SAAS,QAAQ,KAAK;AACpB,SAAO,eAAe,KAAK,GAAG,MAAM;AACtC;AACA,SAAS,SAAS,KAAK;AACrB,SAAO,eAAe,KAAK,GAAG,MAAM;AACtC;AACA,SAAS,SAAS,KAAK;AACrB,SAAO,eAAe,KAAK,GAAG,MAAM;AACtC;AACA,SAAS,WAAW,KAAK;AACvB,MAAI,MAAM,eAAe,KAAK,GAAG;AACjC,SAAO,QAAQ,uBAAuB,QAAQ,4BAA4B,QAAQ,gCAAgC,QAAQ,qCAAqC,QAAQ;AACzK;AACA,SAAS,SAAS,KAAK;AACrB,SAAO,eAAe,KAAK,GAAG,MAAM;AACtC;AAOA,SAAS,YAAY,KAAK;AACxB,MAAI,QAAQ,GAAG,GAAG;AAChB;AAAA,EACF;AAEA,QAAM,IAAI,MAAM,mBAAmB;AACrC;AAUA,SAAS,IAAI,QAAQ,KAAK;AACxB,SAAO,qBAAqB,KAAK,QAAQ,GAAG;AAC9C;AAWA,SAAS,KAAK,YAAY,SAAS;AACjC,YAAU,UAAU,OAAO;AAC3B,MAAI;AACJ,UAAQ,YAAY,SAAU,KAAK,KAAK;AACtC,QAAI,QAAQ,KAAK,GAAG,GAAG;AACrB,cAAQ;AACR,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAUA,SAAS,UAAU,YAAY,SAAS;AACtC,YAAU,UAAU,OAAO;AAC3B,MAAI,MAAM,QAAQ,UAAU,IAAI,KAAK;AACrC,UAAQ,YAAY,SAAU,KAAK,KAAK;AACtC,QAAI,QAAQ,KAAK,GAAG,GAAG;AACrB,YAAM;AACN,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAUA,SAAS,OAAO,YAAY,SAAS;AACnC,MAAI,SAAS,CAAC;AACd,UAAQ,YAAY,SAAU,KAAK,KAAK;AACtC,QAAI,QAAQ,KAAK,GAAG,GAAG;AACrB,aAAO,KAAK,GAAG;AAAA,IACjB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAWA,SAAS,QAAQ,YAAY,UAAU;AACrC,MAAI,KAAK;AAET,MAAI,YAAY,UAAU,GAAG;AAC3B;AAAA,EACF;AAEA,MAAI,aAAa,QAAQ,UAAU,IAAI,QAAQ;AAE/C,WAAS,OAAO,YAAY;AAC1B,QAAI,IAAI,YAAY,GAAG,GAAG;AACxB,YAAM,WAAW,GAAG;AACpB,eAAS,SAAS,KAAK,WAAW,GAAG,CAAC;AAEtC,UAAI,WAAW,OAAO;AACpB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AAUA,SAAS,QAAQ,KAAK,SAAS;AAC7B,MAAI,YAAY,GAAG,GAAG;AACpB,WAAO,CAAC;AAAA,EACV;AAEA,cAAY,GAAG;AACf,YAAU,UAAU,OAAO;AAC3B,SAAO,IAAI,OAAO,SAAU,IAAI,KAAK;AACnC,WAAO,CAAC,QAAQ,IAAI,GAAG;AAAA,EACzB,CAAC;AACH;AAWA,SAAS,OAAO,YAAY,UAAU,QAAQ;AAC5C,UAAQ,YAAY,SAAU,OAAO,KAAK;AACxC,aAAS,SAAS,QAAQ,OAAO,GAAG;AAAA,EACtC,CAAC;AACD,SAAO;AACT;AAWA,SAAS,MAAM,YAAY,SAAS;AAClC,SAAO,CAAC,CAAC,OAAO,YAAY,SAAU,SAAS,KAAK,KAAK;AACvD,WAAO,WAAW,QAAQ,KAAK,GAAG;AAAA,EACpC,GAAG,IAAI;AACT;AAWA,SAAS,KAAK,YAAY,SAAS;AACjC,SAAO,CAAC,CAAC,KAAK,YAAY,OAAO;AACnC;AAWA,SAAS,IAAI,YAAY,IAAI;AAC3B,MAAI,SAAS,CAAC;AACd,UAAQ,YAAY,SAAU,KAAK,KAAK;AACtC,WAAO,KAAK,GAAG,KAAK,GAAG,CAAC;AAAA,EAC1B,CAAC;AACD,SAAO;AACT;AASA,SAAS,KAAK,YAAY;AACxB,SAAO,cAAc,OAAO,KAAK,UAAU,KAAK,CAAC;AACnD;AASA,SAAS,KAAK,YAAY;AACxB,SAAO,KAAK,UAAU,EAAE;AAC1B;AASA,SAAS,OAAO,YAAY;AAC1B,SAAO,IAAI,YAAY,SAAU,KAAK;AACpC,WAAO;AAAA,EACT,CAAC;AACH;AAUA,SAAS,QAAQ,YAAY,WAAW;AACtC,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,cAAY,YAAY,SAAS;AACjC,UAAQ,YAAY,SAAU,KAAK;AACjC,QAAI,gBAAgB,UAAU,GAAG,KAAK;AACtC,QAAI,QAAQ,QAAQ,aAAa;AAEjC,QAAI,CAAC,OAAO;AACV,cAAQ,QAAQ,aAAa,IAAI,CAAC;AAAA,IACpC;AAEA,UAAM,KAAK,GAAG;AAAA,EAChB,CAAC;AACD,SAAO;AACT;AACA,SAAS,SAAS,WAAW;AAC3B,cAAY,YAAY,SAAS;AACjC,MAAI,UAAU,CAAC;AAEf,WAAS,OAAO,UAAU,QAAQ,cAAc,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACjH,gBAAY,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,EACxC;AAEA,UAAQ,aAAa,SAAU,GAAG;AAChC,WAAO,QAAQ,GAAG,WAAW,OAAO;AAAA,EACtC,CAAC;AACD,MAAI,SAAS,IAAI,SAAS,SAAU,KAAK,KAAK;AAC5C,WAAO,IAAI,CAAC;AAAA,EACd,CAAC;AACD,SAAO;AACT;AAWA,SAAS,OAAO,YAAY,WAAW;AACrC,cAAY,YAAY,SAAS;AACjC,MAAI,SAAS,CAAC;AACd,UAAQ,YAAY,SAAU,OAAO,KAAK;AACxC,QAAI,OAAO,UAAU,OAAO,GAAG;AAC/B,QAAI,QAAQ;AAAA,MACV,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAEA,aAAS,MAAM,GAAG,MAAM,OAAO,QAAQ,OAAO;AAC5C,UAAI,IAAI,OAAO,GAAG,EAAE;AAEpB,UAAI,OAAO,GAAG;AACZ,eAAO,OAAO,KAAK,GAAG,KAAK;AAC3B;AAAA,MACF;AAAA,IACF;AAGA,WAAO,KAAK,KAAK;AAAA,EACnB,CAAC;AACD,SAAO,IAAI,QAAQ,SAAU,GAAG;AAC9B,WAAO,EAAE;AAAA,EACX,CAAC;AACH;AAeA,SAAS,aAAa,SAAS;AAC7B,SAAO,SAAU,IAAI;AACnB,WAAO,MAAM,SAAS,SAAU,KAAK,KAAK;AACxC,aAAO,GAAG,GAAG,MAAM;AAAA,IACrB,CAAC;AAAA,EACH;AACF;AAEA,SAAS,YAAY,WAAW;AAC9B,SAAO,WAAW,SAAS,IAAI,YAAY,SAAU,GAAG;AACtD,WAAO,EAAE,SAAS;AAAA,EACpB;AACF;AAEA,SAAS,UAAU,SAAS;AAC1B,SAAO,WAAW,OAAO,IAAI,UAAU,SAAU,GAAG;AAClD,WAAO,MAAM;AAAA,EACf;AACF;AAEA,SAAS,SAAS,KAAK;AACrB,SAAO;AACT;AAEA,SAAS,MAAM,KAAK;AAClB,SAAO,OAAO,GAAG;AACnB;AAcA,SAAS,SAAS,IAAI,SAAS;AAC7B,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,WAAS,KAAK,OAAO;AACnB,QAAI,MAAM,KAAK,IAAI;AACnB,QAAI,gBAAgB,QAAQ,IAAI,UAAU,UAAU;AAEpD,QAAI,gBAAgB,GAAG;AACrB,aAAO,SAAS,aAAa;AAAA,IAC/B;AAEA,OAAG,MAAM,UAAU,QAAQ;AAC3B,UAAM;AAAA,EACR;AAEA,WAAS,SAASU,UAAS;AACzB,YAAQ,WAAW,MAAMA,QAAO;AAAA,EAClC;AAEA,WAAS,QAAQ;AACf,QAAI,OAAO;AACT,mBAAa,KAAK;AAAA,IACpB;AAEA,YAAQ,UAAU,WAAW,WAAW;AAAA,EAC1C;AAEA,WAAS,QAAQ;AACf,QAAI,OAAO;AACT,WAAK,IAAI;AAAA,IACX;AAEA,UAAM;AAAA,EACR;AAEA,WAAS,WAAW;AAClB,cAAU,KAAK,IAAI;AAEnB,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AAEA,eAAW;AACX,eAAW;AAEX,QAAI,CAAC,OAAO;AACV,eAAS,OAAO;AAAA,IAClB;AAAA,EACF;AAEA,WAAS,QAAQ;AACjB,WAAS,SAAS;AAClB,SAAO;AACT;AAWA,SAAS,SAAS,IAAI,UAAU;AAC9B,MAAI,aAAa;AACjB,SAAO,WAAY;AACjB,QAAI,YAAY;AACd;AAAA,IACF;AAEA,OAAG,MAAM,QAAQ,SAAS;AAC1B,iBAAa;AACb,eAAW,WAAY;AACrB,mBAAa;AAAA,IACf,GAAG,QAAQ;AAAA,EACb;AACF;AAUA,SAAS,KAAK,IAAI,QAAQ;AACxB,SAAO,GAAG,KAAK,MAAM;AACvB;AAEA,SAAS,QAAQ,KAAK;AACpB;AAEA,MAAI,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,UAAU;AACvE,cAAU,SAAUC,MAAK;AACvB,aAAO,OAAOA;AAAA,IAChB;AAAA,EACF,OAAO;AACL,cAAU,SAAUA,MAAK;AACvB,aAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,IAC3H;AAAA,EACF;AAEA,SAAO,QAAQ,GAAG;AACpB;AAEA,SAAS,WAAW;AAClB,aAAW,OAAO,UAAU,SAAU,QAAQ;AAC5C,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AAExB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;AAWA,SAAS,OAAO,QAAQ;AACtB,WAAS,OAAO,UAAU,QAAQ,SAAS,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC5G,WAAO,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,EACnC;AAEA,SAAO,SAAS,MAAM,QAAQ,CAAC,MAAM,EAAE,OAAO,MAAM,CAAC;AACvD;AAWA,SAAS,IAAI,QAAQ,MAAM,OAAO;AAChC,MAAI,gBAAgB;AACpB,UAAQ,MAAM,SAAU,KAAK,KAAK;AAChC,QAAI,OAAO,QAAQ,YAAY,OAAO,QAAQ,UAAU;AACtD,YAAM,IAAI,MAAM,uBAAuB,QAAQ,GAAG,IAAI,2CAA2C;AAAA,IACnG;AAEA,QAAI,QAAQ,eAAe;AACzB,YAAM,IAAI,MAAM,0BAA0B;AAAA,IAC5C;AAEA,QAAI,QAAQ,aAAa;AACvB,YAAM,IAAI,MAAM,wBAAwB;AAAA,IAC1C;AAEA,QAAI,UAAU,KAAK,MAAM,CAAC;AAC1B,QAAI,aAAa,cAAc,GAAG;AAElC,QAAI,UAAU,OAAO,KAAK,MAAM,UAAU,GAAG;AAC3C,mBAAa,cAAc,GAAG,IAAI,MAAM,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC;AAAA,IAC5D;AAEA,QAAI,YAAY,OAAO,GAAG;AACxB,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,cAAc,GAAG;AAAA,MAC1B,OAAO;AACL,sBAAc,GAAG,IAAI;AAAA,MACvB;AAAA,IACF,OAAO;AACL,sBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AASA,SAAS,IAAI,QAAQ,MAAM,cAAc;AACvC,MAAI,gBAAgB;AACpB,UAAQ,MAAM,SAAU,KAAK;AAE3B,QAAI,MAAM,aAAa,GAAG;AACxB,sBAAgB;AAChB,aAAO;AAAA,IACT;AAEA,oBAAgB,cAAc,GAAG;AAAA,EACnC,CAAC;AACD,SAAO,YAAY,aAAa,IAAI,eAAe;AACrD;AAUA,SAAS,KAAK,QAAQ,YAAY;AAChC,MAAI,SAAS,CAAC;AACd,MAAI,MAAM,OAAO,MAAM;AACvB,UAAQ,YAAY,SAAU,MAAM;AAClC,QAAI,QAAQ,KAAK;AACf,aAAO,IAAI,IAAI,OAAO,IAAI;AAAA,IAC5B;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAUA,SAAS,KAAK,QAAQ,YAAY;AAChC,MAAI,SAAS,CAAC;AACd,MAAI,MAAM,OAAO,MAAM;AACvB,UAAQ,KAAK,SAAU,MAAM,KAAK;AAChC,QAAI,WAAW,QAAQ,GAAG,MAAM,IAAI;AAClC,aAAO,GAAG,IAAI;AAAA,IAChB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAYA,SAAS,MAAM,QAAQ;AACrB,WAAS,QAAQ,UAAU,QAAQ,UAAU,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACpH,YAAQ,QAAQ,CAAC,IAAI,UAAU,KAAK;AAAA,EACtC;AAEA,MAAI,CAAC,QAAQ,QAAQ;AACnB,WAAO;AAAA,EACT;AAEA,UAAQ,SAAS,SAAU,QAAQ;AAEjC,QAAI,CAAC,UAAU,CAAC,SAAS,MAAM,GAAG;AAChC;AAAA,IACF;AAEA,YAAQ,QAAQ,SAAU,WAAW,KAAK;AACxC,UAAI,QAAQ,aAAa;AACvB;AAAA,MACF;AAEA,UAAI,YAAY,OAAO,GAAG;AAE1B,UAAI,SAAS,SAAS,GAAG;AACvB,YAAI,CAAC,SAAS,SAAS,GAAG;AAExB,sBAAY,CAAC;AAAA,QACf;AAEA,eAAO,GAAG,IAAI,MAAM,WAAW,SAAS;AAAA,MAC1C,OAAO;AACL,eAAO,GAAG,IAAI;AAAA,MAChB;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,SAAO;AACT;AAnrBA,IAWI,gBACA,sBAwSA;AApTJ;AAAA;AAWA,IAAI,iBAAiB,OAAO,UAAU;AACtC,IAAI,uBAAuB,OAAO,UAAU;AAwS5C,IAAI,UAAU;AAAA;AAAA;;;ACpTd;AAAA;AAAA;AAEA,QAAIC,SAAQ,oDAAoB;AAAhC,QACIC,QAAO,oDAAoB;AAE/B,WAAO,QAAQ,KAAK,SAAS,SAAS,OAAO;AAC3C,UAAI,QAAQ,SAAS,SAAS;AAC5B;AAAA,MACF;AAEA,UAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,gBAAQ,CAAE,KAAM;AAAA,MAClB;AAEA,UAAI,SAAS;AAEb,YAAM,QAAQ,SAAS,MAAM;AAC3B,YAAI,SAAS,QAAQ,MAAM;AACzB,mBAAS;AAAA,QACX;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAEA,WAAO,QAAQ,eAAe,SAAS,OAAO,qBAAqBC,SAAQ;AAEzE,eAAS,QAAQ,YAAYA,SAAQ;AACnC,eAAOF,OAAME,SAAQ,SAAS,KAAK,KAAK;AAItC,iBAAO,WAAW,GAAG,KAAK;AAAA,QAC5B,CAAC;AAAA,MACH;AAEA,aAAOD,MAAK,MAAM,kBAAkB,SAAS,YAAY;AACvD,eAAO,WAAW,UAAU,uBAAuB,QAAQ,OAAOC,OAAM;AAAA,MAC1E,CAAC;AAAA,IACH;AAEA,WAAO,QAAQ,oBAAoB,SAAS,SAAS;AACnD,aAAQ,WAAW,QAAQ,kBAAmB;AAAA,IAChD;AAEA,aAAS,WAAW,UAAU,YAAY;AACxC,UAAI,cAAc,WAAW;AAE7B,aAAO,aAAa;AAClB,YAAI,gBAAgB,UAAU;AAC5B,iBAAO;AAAA,QACT;AAEA,sBAAc,YAAY;AAAA,MAC5B;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,QAAQ,aAAa;AAE5B,WAAO,QAAQ,iBAAiB,SAAS,UAAU,UAAU;AAC3D,aAAO,SAAS,OAAO,SAAS,SAAS;AACvC,eAAO,WAAW,UAAU,OAAO;AAAA,MACrC,CAAC;AAAA,IACH;AAEA,WAAO,QAAQ,oBAAoB;AAAA,MACjC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;AC3FA;AAAA;AAAA,WAAO,QAAQ,SAAS,SAAS,SAAS;AACxC,UAAI,OAAO,QAAQ,KAAK;AAExB,aAAO;AAAA,QACL,GAAG,KAAK,IAAI,KAAK,QAAQ;AAAA,QACzB,GAAG,KAAK,IAAI,KAAK,SAAS;AAAA,MAC5B;AAAA,IACF;AAEA,WAAO,QAAQ,WAAW,SAAS,GAAG,GAAG;AACvC,aAAO,KAAK,KAAK,KAAK,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;AAAA,IAClE;AAAA;AAAA;;;ACXA;AAAA;AAAA;AAEA,QAAI,MAAM;AAEV,QAAI,WAAW;AAEf,QAAI,SAAS;AAAb,QACI,yBAAyB,OAAO;AADpC,QAEI,wBAAwB,OAAO;AAFnC,QAGI,yBAAyB,OAAO;AAHpC,QAII,kBAAkB,OAAO;AAJ7B,QAKI,kCAAkC,OAAO;AAL7C,QAMI,0BAA0B,OAAO;AAErC,QAAI,aAAa,wBAAiC;AAElD,QAAI,eAAe;AAAnB,QACI,WAAW,aAAa;AAE5B,QAAI,eAAe,iBAAiB,SAAS,eAAe,EAAE,iBAAiB,kCAAkC;AAEjH,aAAS,eAAe,OAAO;AAC7B,aAAO,UAAU;AAAA,IACnB;AAEA,aAAS,gBAAgB,WAAW;AAClC,aAAO,UAAU,UAAU;AAAA,IAC7B;AAEA,QAAI,QAAQ;AAEZ,QAAI,cAAc;AAAlB,QACI,UAAU;AADd,QAEI,cAAc;AAElB,QAAI,aAAa;AAEjB,aAAS,UAAU,QAAQ,UAAU;AACnC,UAAI,OAAO,OAAO,YAAY;AAE9B,WAAK,YAAY;AACjB,WAAK,aAAa,CAAC;AACnB,WAAK,mBAAmB,CAAC;AAEzB,WAAK,iBAAiB;AAEtB,eAAS,GAAG,eAAe,WAAW;AACpC,YAAI,OAAO,IAAI,OAAO,IAAI;AAE1B,YAAI,WAAW,SAAS,aAAa,OAAO,IAAI;AAEhD,YAAI,cAAc,IAAI,MAAM,QAAQ;AAEpC,aAAK,QAAQ,KACV,MAAM,EACN,KAAK,MAAM,kBAAkB;AAEhC,oBAAY,IAAI,KAAK,KAAK;AAAA,MAC5B,CAAC;AAED,eAAS,GAAG,iBAAiB,SAAS,SAAS;AAC7C,YAAI,UAAU,QAAQ,SAClB,SAAS,QAAQ;AAErB,aAAK,WAAW,QAAQ,SAAS,WAAW;AAC1C,cAAI,WAAW,QAAQ,UAAU,OAAO,GAAG;AAGzC,sBAAU,UAAU,KAAK;AAEzB,iBAAK,aAAa,KAAK,WAAW,OAAO,SAAS,GAAG;AACnD,qBAAO,MAAM;AAAA,YACf,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAED,eAAS,GAAG,iCAAiC,SAAS,SAAS;AAC7D,YAAI,SAAS,QAAQ;AAErB,aAAK,WAAW,QAAQ,SAAS,WAAW;AAC1C,cAAI,QAAQ,sBAAsB,UAAU,qBAC1C,WAAW,QAAQ,UAAU,OAAO,GAAG;AAGvC,sBAAU,UAAU,KAAK;AAEzB,iBAAK,aAAa,KAAK,WAAW,OAAO,SAAS,GAAG;AACnD,qBAAO,MAAM;AAAA,YACf,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAED,eAAS,GAAG,wBAAwB,WAAW;AAC7C,aAAK,WAAW,QAAQ,SAAS,WAAW;AAC1C,oBAAU,UAAU,KAAK;AAAA,QAC3B,CAAC;AAED,aAAK,aAAa,CAAC;AACnB,aAAK,mBAAmB,CAAC;AAAA,MAC3B,CAAC;AAED,eAAS,GAAG,wBAAwB,WAAW;AAC7C,aAAK,WAAW,QAAQ,SAAS,WAAW;AAC1C,oBAAU,UAAU,MAAM;AAAA,QAC5B,CAAC;AAAA,MACH,CAAC;AAED,eAAS,GAAG,uBAAuB,WAAW;AAC5C,aAAK,WAAW,QAAQ,SAAS,WAAW;AAC1C,oBAAU,UAAU,KAAK;AAAA,QAC3B,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,cAAU,UAAU,kBAAkB,SAAS,YAAY,mBAAmB,MAAM;AAClF,UAAI,OAAO;AAEX,UAAI,CAAC,KAAK,OAAO;AACf;AAAA,MACF;AAEA,UAAI,WAAW,KAAK,gBAAgB,iBAAiB;AAErD,UAAI;AAEJ,kBAAY,IAAI,WAAW,UAAU,WAAW,WAAW,WAAW;AACpE,aAAK,aAAa,KAAK,WAAW,OAAO,SAAS,GAAG;AACnD,iBAAO,EAAE,cAAc;AAAA,QACzB,CAAC;AAED,YAAI,MAAM;AACR,eAAK;AAAA,QACP;AAAA,MACF,CAAC;AAED,UAAI,KAAK,iBAAiB,SAAS,iBAAiB,GAAG;AACrD,iBAAS,KAAK;AAAA,MAChB;AAEA,eAAS,GAAG,SAAS,KAAK;AAE1B,WAAK,WAAW,KAAK;AAAA,QACnB;AAAA,QACA;AAAA,QACA,SAAS;AAAA,QACT;AAAA,MACF,CAAC;AAED,WAAK,UAAU,KAAK,yBAAyB;AAAA,QAC3C;AAAA,QACA;AAAA,QACA,SAAS;AAAA,QACT;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAEA,cAAU,UAAU,oBAAoB,SAAS,OAAO;AACtD,WAAK,WAAW,QAAQ,SAAS,WAAW;AAC1C,kBAAU,SAAS,GAAG,SAAS;AAAA,MACjC,CAAC;AAED,WAAK,iBAAiB;AAAA,IACxB;AAEA,cAAU,UAAU,kBAAkB,SAAS,mBAAmB;AAChE,UAAI,SAAS,KAAK,MACf,MAAM,EACN,KAAK,SAAS,OAAO,EACrB,KAAK;AAER,aACG,OAAO,YAAY,UAAU,EAC7B,KAAK,QAAQ,YAAY,EACzB,KAAK,SAAS,QAAQ;AAEzB,aACG,KAAK,kBAAkB,SAAS,CAAC,EACjC,KAAK,aAAa,mBAAmB,EACrC,KAAK,eAAe,QAAQ,EAC5B,KAAK,SAAS,MAAM;AAEvB,aAAO;AAAA,IACT;AAEA,cAAU,UAAU,gCAAgC,SAAS,mBAAmB;AAC9E,WAAK,WAAW,QAAQ,SAAS,WAAW;AAC1C,YAAI,UAAU,sBAAsB,mBAAmB;AACrD,oBAAU,SAAS,KAAK;AAAA,QAC1B;AAAA,MACF,CAAC;AAED,WAAK,mBAAmB,KAAK,iBAAiB,OAAO,SAAS,IAAI;AAChE,eAAO,OAAO;AAAA,MAChB,CAAC;AAAA,IACH;AAEA,cAAU,UAAU,gCAAgC,SAAS,mBAAmB;AAC9E,WAAK,WAAW,QAAQ,SAAS,WAAW;AAC1C,YAAI,UAAU,sBAAsB,mBAAmB;AACrD,oBAAU,SAAS,KAAK;AAAA,QAC1B;AAAA,MACF,CAAC;AAED,WAAK,iBAAiB,KAAK,iBAAiB;AAAA,IAC9C;AAEA,cAAU,UAAU,CAAE,UAAU,UAAW;AAE3C,WAAO,UAAU;AAEjB,aAAS,WAAW,KAAK,WAAW,MAAM;AACxC,WAAK,MAAM,KAAK,KAAK;AACrB,WAAK,YAAY;AACjB,WAAK,OAAO;AAEZ,WAAK,OAAO;AAAA,IACd;AAEA,eAAW,UAAU,SAAS,WAAW;AACvC,UAAI,MAAM,KAAK,KACX,YAAY,KAAK,WACjB,OAAO,KAAK,MACZ,KAAK,KAAK;AAEd,UACG,KAAK,EACL,KAAK,UAAU,CAAC,EAAE,IAAI,aAAa,GAAG,UAAU,CAAC,EAAE,IAAI,aAAa,CAAC;AAExE,gBAAU,QAAQ,SAAS,UAAU,OAAO;AAC1C,YAAI,QAAQ,GAAG;AACb,cAAI,IAAI,SAAS,IAAI,aAAa,GAC9B,IAAI,SAAS,IAAI,aAAa;AAElC,cAAI,OAAO,eAAe,KAAK,IAAI,UAAU;AAE7C,cAAI,gBAAgB,SAAS,GAAG;AAC9B,mBAAO;AAAA,UACT;AAEA,cAAI,WAAW,SAAS,UAAU,QAAQ,CAAC,GAAG,QAAQ,IAAI;AAE1D,eAAK,GACF,QAAQ,UAAU,MAAM,KAAK,EAC7B,KAAK,GAAG,CAAC;AAAA,QACd;AAAA,MACF,CAAC;AAED,SAAG,MAAM,WAAW;AAClB,YAAI,OAAO;AAEX,aAAK;AAAA,MACP,CAAC;AAAA,IACH;AAEA,eAAW,UAAU,OAAO,WAAW;AACrC,WAAK,IAAI,KAAK;AAAA,IAChB;AAEA,eAAW,UAAU,QAAQ,WAAW;AACtC,WAAK,IAAI,MAAM;AAAA,IACjB;AAEA,eAAW,UAAU,OAAO,WAAW;AACrC,WAAK,GAAG,KAAK;AACb,WAAK,IAAI,OAAO;AAAA,IAClB;AAAA;AAAA;;;AC7QA;AAAA;AAKA,WAAO,UAAU;AAMjB,QAAI,eAAe;AACnB,QAAI;AACJ,QAAI,OAAO,aAAa,aAAa;AACnC,mBAAa,SAAS,cAAc,KAAK;AAEzC,iBAAW,YAAY;AAGvB,qBAAe,CAAC,WAAW,qBAAqB,MAAM,EAAE;AACxD,mBAAa;AAAA,IACf;AAMA,QAAIC,OAAM;AAAA,MACR,QAAQ,CAAC,GAAG,cAAc,aAAa;AAAA,MACvC,IAAI,CAAC,GAAG,kBAAkB,kBAAkB;AAAA,MAC5C,KAAK,CAAC,GAAG,oCAAoC,qBAAqB;AAAA;AAAA;AAAA,MAGlE,UAAU,eAAe,CAAC,GAAG,UAAU,QAAQ,IAAI,CAAC,GAAG,IAAI,EAAE;AAAA,IAC/D;AAEA,IAAAA,KAAI,KACJA,KAAI,KAAK,CAAC,GAAG,sBAAsB,uBAAuB;AAE1D,IAAAA,KAAI,SACJA,KAAI,WAAW,CAAC,GAAG,gCAAgC,WAAW;AAE9D,IAAAA,KAAI,QACJA,KAAI,QACJA,KAAI,WACJA,KAAI,UACJA,KAAI,QAAQ,CAAC,GAAG,WAAW,UAAU;AAErC,IAAAA,KAAI,WACJA,KAAI,UACJA,KAAI,UACJA,KAAI,SACJA,KAAI,OACJA,KAAI,OACJA,KAAI,OACJA,KAAI,OACJA,KAAI,IAAI,CAAC,GAAG,0DAAyD,QAAQ;AAa7E,aAAS,MAAM,MAAM,KAAK;AACxB,UAAI,YAAY,OAAO;AAAM,cAAM,IAAI,UAAU,iBAAiB;AAGlE,UAAI,CAAC;AAAK,cAAM;AAGhB,UAAI,IAAI,YAAY,KAAK,IAAI;AAC7B,UAAI,CAAC;AAAG,eAAO,IAAI,eAAe,IAAI;AAEtC,aAAO,KAAK,QAAQ,cAAc,EAAE;AAEpC,UAAI,MAAM,EAAE,CAAC;AAGb,UAAI,OAAO,QAAQ;AACjB,YAAI,KAAK,IAAI,cAAc,MAAM;AACjC,WAAG,YAAY;AACf,eAAO,GAAG,YAAY,GAAG,SAAS;AAAA,MACpC;AAGA,UAAI,OAAO,OAAO,UAAU,eAAe,KAAKA,MAAK,GAAG,IAAIA,KAAI,GAAG,IAAIA,KAAI;AAC3E,UAAI,QAAQ,KAAK,CAAC;AAClB,UAAI,SAAS,KAAK,CAAC;AACnB,UAAI,SAAS,KAAK,CAAC;AACnB,UAAI,KAAK,IAAI,cAAc,KAAK;AAChC,SAAG,YAAY,SAAS,OAAO;AAC/B,aAAO;AAAS,aAAK,GAAG;AAGxB,UAAI,GAAG,cAAc,GAAG,WAAW;AACjC,eAAO,GAAG,YAAY,GAAG,UAAU;AAAA,MACrC;AAGA,UAAI,WAAW,IAAI,uBAAuB;AAC1C,aAAO,GAAG,YAAY;AACpB,iBAAS,YAAY,GAAG,YAAY,GAAG,UAAU,CAAC;AAAA,MACpD;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;AC/GA,IAAAC,kBAAA;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA,QAAIC,QAAO,OAAO,mBAAmB,qBAAqB;AAA1D,QACI,SAAS,OAAO,sBAAsB,wBAAwB;AADlE,QAEI,SAASA,UAAS,qBAAqB,OAAO;AAalD,YAAQ,OAAO,SAAS,IAAI,MAAM,IAAI,SAAQ;AAC5C,SAAGA,KAAI,EAAE,SAAS,MAAM,IAAI,WAAW,KAAK;AAC5C,aAAO;AAAA,IACT;AAaA,YAAQ,SAAS,SAAS,IAAI,MAAM,IAAI,SAAQ;AAC9C,SAAG,MAAM,EAAE,SAAS,MAAM,IAAI,WAAW,KAAK;AAC9C,aAAO;AAAA,IACT;AAAA;AAAA;;;AClCA;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA;AAEA,QAAI,SAAS;AAAb,QACI,WAAW;AAEf,QAAI,KAAK,wBAAuC;AAEhD,QAAI,SAAS;AAAb,QACI,uBAAuB,OAAO;AADlC,QAEI,uBAAuB,OAAO;AAElC,aAAS,qBAAqB,UAAU,kBAAkB,yBAAyB;AACjF,WAAK,YAAY;AACjB,WAAK,oBAAoB;AACzB,WAAK,2BAA2B;AAAA,IAClC;AAEA,yBAAqB,UAAU,oBAAoB,SAAS,SAAS;AACnE,UAAI,CAAC,QAAQ,UAAU,QAAQ;AAC7B;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,kBAAkB,oBAAoB,OAAO,EAAE,QAAQ;AAC/D;AAAA,MACF;AAEA,UAAI,wBAAwB,QAAQ,SAAS,OAAO,SAAS,UAAU;AACrE,eAAO,GAAG,UAAU,mBAAmB;AAAA,MACzC,CAAC;AAED,UAAI,OAAO;AAEX,UAAI,cAAc,CAAC;AAEnB,cAAQ,UAAU,QAAQ,SAAS,iBAAiB;AAClD,YAAI,wBAAwB,gBAAgB,SAAS,OAAO,SAAS,UAAU;AAC7E,iBAAO,GAAG,UAAU,mBAAmB;AAAA,QACzC,CAAC;AAED,YAAI,CAAC,sBAAsB,UAAU,CAAC,sBAAsB,QAAQ;AAClE;AAAA,QACF;AAEA,YAAI,aAAa,OAAO,iFAAiF;AAEzG,oBAAY,KAAK;AAAA,UACf,SAAS;AAAA,UACT,MAAM;AAAA,QACR,CAAC;AAED,iBAAS,KAAK,YAAY,SAAS,WAAW;AAE5C,eAAK,kBACF,oBAAoB,OAAO,EAC3B,QAAQ,SAAS,iBAAiB;AACjC,gBAAI,0BAA0B,gBAAgB;AAG9C,gBAAI,gBAAgB,eAAe,gBAAgB;AACjD,sBAAQ,SAAS,QAAQ,SAAS,OAAO;AACvC,oBAAI,MAAM,cAAc,MAAM,WAAW,gBAAgB,iBAAiB,GAAG;AAC3E,wBAAM,WAAW,gBAAgB,iBAAiB;AAAA,gBACpD;AAAA,cACF,CAAC;AAGD,mBAAK,kBAAkB,OAAO,gBAAgB,iBAAiB;AAE/D,mBAAK,UAAU,KAAK,sBAAsB;AAAA,gBACxC;AAAA,cACF,CAAC;AAAA,YACH;AAEA,iBAAK,UAAU,KAAK,sBAAsB;AAAA,cACxC,SAAS;AAAA,cACT,mBAAmB;AAAA,YACrB,CAAC;AAAA,UACH,CAAC;AAAA,QAEL,CAAC;AAAA,MACH,CAAC;AAED,aAAO;AAAA,IACT;AAEA,yBAAqB,UAAU,CAAC,YAAY,oBAAoB,yBAAyB;AAEzF,WAAO,UAAU;AAAA;AAAA;;;ACvFjB;AAAA;AAAA;AAEA,QAAI,KAAK,wBAAuC;AAEhD,QAAI,SAAS;AAAb,QACI,WAAW;AAEf,aAAS,wBAAwB,yBAAyB;AACxD,WAAK,4BAA4B;AAAA,IACnC;AAEA,4BAAwB,UAAU,oBAAoB,SAAS,SAAS;AACtE,UAAI,OAAO;AAEX,UAAI,wBAAwB,QAAQ,SAAS,OAAO,SAAS,UAAU;AACrE,eAAO,GAAG,UAAU,mBAAmB;AAAA,MACzC,CAAC;AAED,UAAI,sBAAsB,SAAS,GAAG;AACpC;AAAA,MACF;AAEA,UAAI,aAAa,OAAO,0FAA0F;AAElH,eAAS,KAAK,YAAY,SAAS,WAAW;AAC5C,aAAK,0BAA0B,gBAAgB,OAAO;AAAA,MACxD,CAAC;AAED,aAAO,CAAC;AAAA,QACN;AAAA,QACA,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAEA,4BAAwB,UAAU,CAAE,0BAA2B;AAE/D,WAAO,UAAU;AAAA;AAAA;;;ACpCjB;AAAA;AAAA;AAEA,QAAI,SAAS;AAAb,QACI,WAAW;AAEf,QAAI,KAAK,wBAAuC;AAEhD,QAAI,SAAS;AAAb,QACI,uBAAuB,OAAO;AAElC,aAAS,+BAA+B,UAAU;AAChD,WAAK,YAAY;AAAA,IACnB;AAEA,mCAA+B,UAAU,oBAAoB,SAAS,SAAS;AAC7E,UAAI,oBAAoB,QAAQ,OAAO;AAEvC,UAAI,wBAAwB,QAAQ,SAAS,OAAO,SAAS,UAAU;AACrE,eAAO,GAAG,UAAU,mBAAmB;AAAA,MACzC,CAAC;AAED,UAAI,+BAA+B,CAAC;AAEpC,4BAAsB,QAAQ,SAAS,UAAU;AAC/C,YAAI,SAAS,SAAS;AAEtB,YAAI,GAAG,QAAQ,wBAAwB,KAAK,OAAO,cAAc,OAAO,WAAW,iBAAiB,GAAG;AACrG,uCAA6B,KAAK,MAAM;AAAA,QAC1C;AAAA,MACF,CAAC;AAED,UAAI,wBAAwB,QAAQ,SAAS,OAAO,SAAS,UAAU;AACrE,eAAO,GAAG,UAAU,mBAAmB;AAAA,MACzC,CAAC;AAED,UAAI,CAAC,sBAAsB,UAAU,CAAC,sBAAsB,QAAQ;AAClE;AAAA,MACF;AAEA,UAAI,OAAO;AAEX,UAAI;AAEJ,UAAI,QAAQ,cAAc,QAAQ,WAAW,iBAAiB,GAAG;AAC/D,qBAAa,OAAO,iFAAiF;AAErG,iBAAS,KAAK,YAAY,SAAS,WAAW;AAC5C,kBAAQ,WAAW,iBAAiB;AAEpC,eAAK,UAAU,KAAK,sBAAsB;AAAA,YACxC;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH,WAAW,6BAA6B,QAAQ;AAC9C,qBAAa,OAAO,iFAAiF;AAErG,iBAAS,KAAK,YAAY,SAAS,WAAW;AAC5C,uCAA6B,QAAQ,SAAS,mBAAmB;AAC/D,8BAAkB,WAAW,iBAAiB;AAAA,UAChD,CAAC;AAED,eAAK,UAAU,KAAK,sBAAsB;AAAA,YACxC;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH,OAAO;AACL;AAAA,MACF;AAEA,aAAO,CAAC;AAAA,QACN;AAAA,QACA,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAEA,mCAA+B,UAAU,CAAE,UAAW;AAEtD,WAAO,UAAU;AAAA;AAAA;;;AC/EjB;AAAA;AAAA;AAEA,QAAI,SAAS;AAAb,QACI,WAAW;AAKf,aAAS,eAAe,kBAAkB,yBAAyB;AACjE,WAAK,oBAAoB;AACzB,WAAK,2BAA2B;AAAA,IAClC;AAEA,mBAAe,UAAU,oBAAoB,SAAS,SAAS;AAC7D,UAAI,OAAO;AAEX,UAAI,mBAAmB,KAAK,kBACzB,oBAAoB,OAAO,EAC3B,OAAO,SAAS,iBAAiB;AAChC,eAAO,CAAC,gBAAgB;AAAA,MAC1B,CAAC;AAEH,UAAI,iBAAiB,SAAS,GAAG;AAC/B;AAAA,MACF;AAEA,UAAI,aAAa,OAAO,6FAA6F;AAErH,eAAS,KAAK,YAAY,SAAS,WAAW;AAC5C,aAAK,yBAAyB,SAAS,OAAO;AAAA,MAChD,CAAC;AAED,aAAO,CAAC;AAAA,QACN;AAAA,QACA,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAEA,mBAAe,UAAU,CAAE,oBAAoB,yBAA0B;AAEzE,WAAO,UAAU;AAAA;AAAA;;;ACxCjB;AAAA;AAAA;AAEA,QAAI,SAAS;AAAb,QACI,WAAW;AAEf,QAAI,KAAK,wBAAuC;AAEhD,QAAI,SAAS;AAAb,QACI,uBAAuB,OAAO;AAElC,aAAS,kBAAkB,UAAU,iBAAiB,WAAW;AAC/D,WAAK,YAAY;AACjB,WAAK,mBAAmB;AACxB,WAAK,aAAa;AAAA,IACpB;AAEA,sBAAkB,UAAU,oBAAoB,SAAS,SAAS;AAChE,UAAI,SAAS;AAEb,WAAK,iBAAiB,QAAQ,SAASC,UAAS;AAC9C,YAAIA,SAAQ,YAAY;AACtB,iBAAO,OAAOA,SAAQ,UAAU,EAAE,QAAQ,SAAS,YAAY;AAC7D,gBAAI,YAAY;AACd,uBAAS;AAAA,YACX;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAED,UAAI,GAAG,QAAQ,QAAQ,iBAAiB,KACpC,UACA,KAAK,WAAW,WAAW,QAAQ;AACrC;AAAA,MACF;AAEA,UAAI,OAAO;AAEX,UAAI,aAAa,OAAO,2DAA2D;AAEnF,eAAS,KAAK,YAAY,SAAS,WAAW;AAC5C,aAAK,UAAU,KAAK,sBAAsB;AAAA,UACxC;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAED,aAAO,CAAC;AAAA,QACN;AAAA,QACA,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAEA,sBAAkB,UAAU,CAAE,YAAY,mBAAmB,WAAY;AAEzE,WAAO,UAAU;AAAA;AAAA;;;ACrDjB;AAAA;AAAA;AAEA,QAAI,gBAAgB;AAApB,QACI,aAAa,cAAc;AAE/B,QAAI,SAAS;AAAb,QACI,oBAAoB,OAAO;AAD/B,QAEI,yBAAyB,OAAO;AAFpC,QAGI,kBAAkB,OAAO;AAH7B,QAII,wBAAwB,OAAO;AAJnC,QAKI,uBAAuB,OAAO;AALlC,QAMI,+BAA+B,OAAO;AAE1C,QAAI,uBAAuB;AAA3B,QACI,0BAA0B;AAD9B,QAEI,gCAAgC;AAFpC,QAGI,iBAAiB;AAHrB,QAII,oBAAoB;AAExB,QAAI,eAAe;AAEnB,QAAI,aAAa;AAAjB,QACI,cAAc;AAElB,aAAS,YAAY,UAAU,iBAAiB,UAAU,UAAU,QAAQ,kBAAkB;AAC5F,UAAI,OAAO;AAEX,WAAK,mBAAmB;AACxB,WAAK,YAAY;AACjB,WAAK,YAAY;AACjB,WAAK,UAAU;AACf,WAAK,oBAAoB;AAEzB,WAAK,aAAa,CAAC;AAEnB,WAAK,WAAW,CAAC;AAEjB,WAAK,gBAAgB,yBAAyB,uBAAuB;AACrE,WAAK,gBAAgB,+BAA+B,6BAA6B;AACjF,WAAK,gBAAgB,mBAAmB,cAAc;AACtD,WAAK,gBAAgB,mBAAmB,oBAAoB;AAC5D,WAAK,gBAAgB,mBAAmB,iBAAiB;AAEzD,eAAS,GAAG,mBAAmB,cAAc,SAAS,SAAS;AAC7D,YAAI,uBAAuB,QAAQ;AAEnC,YAAI,sBAAsB;AACxB,eAAK,gBAAgB;AAAA,QACvB,OAAO;AACL,eAAK,iBAAiB;AAAA,QACxB;AAAA,MACF,CAAC;AAED,eAAS,GAAG,wBAAwB,cAAc,WAAW;AAC3D,aAAK,iBAAiB;AACtB,aAAK,gBAAgB;AAAA,MACvB,CAAC;AAED,eAAS,GAAG,iBAAiB,cAAc,SAAS,SAAS;AAC3D,YAAI,UAAU,QAAQ,SAClB,SAAS,QAAQ;AAErB,aAAK,iBAAiB,MAAM;AAAA,MAC9B,CAAC;AAED,eAAS,GAAG,uBAAuB,cAAc,SAAS,SAAS;AACjE,YAAI,WAAW,QAAQ;AAEvB,iBAAS,QAAQ,SAAS,SAAS;AACjC,eAAK,wBAAwB,OAAO;AACpC,eAAK,uBAAuB,OAAO;AAAA,QACrC,CAAC;AAAA,MACH,CAAC;AAED,eAAS,GAAG,sBAAsB,cAAc,SAAS,SAAS;AAChE,YAAI,UAAU,QAAQ;AAEtB,aAAK,wBAAwB,OAAO;AACpC,aAAK,uBAAuB,OAAO;AAAA,MACrC,CAAC;AAED,eAAS,GAAG,8BAA8B,SAAS,SAAS;AAC1D,YAAI,oBAAoB,QAAQ;AAEhC,YAAI,kBAAkB,iBAAiB,mBAAmB,iBAAiB,GACvE,SAAS,gBAAgB;AAE7B,aAAK,iBAAiB,MAAM;AAC5B,aAAK,gBAAgB,MAAM;AAAA,MAC7B,CAAC;AAAA,IACH;AASA,gBAAY,UAAU,kBAAkB,SAAS,MAAM,YAAY;AACjE,UAAI,UAAU,KAAK,UAAU,YAAY,UAAU;AAEnD,UAAI,CAAC,KAAK,SAAS,IAAI,GAAG;AACxB,aAAK,SAAS,IAAI,IAAI,CAAC;AAAA,MACzB;AAEA,WAAK,SAAS,IAAI,EAAE,KAAK,OAAO;AAAA,IAClC;AAEA,gBAAY,UAAU,kBAAkB,SAAS,QAAQ;AACvD,UAAI,OAAO;AAEX,UAAI,CAAC,QAAQ;AACX,iBAAS,KAAK,QAAQ,eAAe;AAAA,MACvC;AAEA,WAAK,iBAAiB,QAAQ,SAAS,SAAS;AAC9C,YAAI,KAAK,SAAS,QAAQ,IAAI,KACvB,WAAW,QAAQ,OAAO,GAAG;AAClC,eAAK,uBAAuB,OAAO;AAAA,QACrC;AAAA,MACF,CAAC;AAAA,IACH;AAEA,gBAAY,UAAU,yBAAyB,SAAS,SAAS;AAC/D,UAAI,CAAC,KAAK,SAAS,QAAQ,IAAI,GAAG;AAChC;AAAA,MACF;AAEA,UAAI,qBAAqB,CAAC;AAE1B,WAAK,SAAS,QAAQ,IAAI,EAAE,QAAQ,SAAS,SAAS;AACpD,YAAI,cAAc,QAAQ,kBAAkB,OAAO;AAEnD,YAAI,aAAa;AACf,sBAAY,QAAQ,SAAS,YAAY;AACvC,gBAAI,YAAY;AACd,iCAAmB,KAAK,UAAU;AAAA,YACpC;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAED,UAAI,OAAO;AAEX,yBAAmB,QAAQ,SAAS,YAAY;AAC9C,YAAI,WAAW,EAAE,KAAK,YAAY,MAAM,YAAY;AAEpD,YAAI,YAAY,KAAK,UAAU,IAAI,WAAW,SAAS,gBAAgB;AAAA,UACrE;AAAA,UACA,MAAM,WAAW;AAAA,UACjB,MAAM;AAAA,YACJ,SAAS;AAAA,UACX;AAAA,QACF,CAAC;AAED,aAAK,WAAW,WAAW,QAAQ,EAAE,IAAI;AAAA,MAC3C,CAAC;AAAA,IACH;AAEA,gBAAY,UAAU,mBAAmB,SAAS,QAAQ;AACxD,UAAI,OAAO;AAEX,UAAI,CAAC,QAAQ;AACX,iBAAS,KAAK,QAAQ,eAAe;AAAA,MACvC;AAEA,WAAK,iBAAiB,QAAQ,SAAS,SAAS;AAC9C,YAAI,WAAW,QAAQ,OAAO,GAAG;AAC/B,eAAK,wBAAwB,OAAO;AAAA,QACtC;AAAA,MACF,CAAC;AAAA,IACH;AAEA,gBAAY,UAAU,0BAA0B,SAAS,SAAS;AAChE,UAAI,OAAO;AAEX,UAAI,QAAQ,aAAa,QAAQ,UAAU,SAAS,GAAG;AACrD,gBAAQ,UAAU,QAAQ,SAAS,iBAAiB;AAClD,eAAK,wBAAwB,eAAe;AAAA,QAC9C,CAAC;AAAA,MACH;AACA,UAAI,QAAQ,YAAY,QAAQ,SAAS,SAAS,GAAG;AACnD,gBAAQ,SAAS,QAAQ,SAAS,OAAO;AACvC,eAAK,wBAAwB,KAAK;AAAA,QACpC,CAAC;AAAA,MACH;AAEA,UAAI,YAAY,KAAK,WAAW,QAAQ,EAAE;AAE1C,UAAI,CAAC,WAAW;AACd;AAAA,MACF;AAEA,WAAK,UAAU,OAAO,SAAS;AAE/B,aAAO,KAAK,WAAW,QAAQ,EAAE;AAAA,IACnC;AAEA,gBAAY,UAAU,CAAE,YAAY,mBAAmB,YAAY,YAAY,UAAU,kBAAmB;AAE5G,WAAO,UAAU;AAAA;AAAA;;;ACzMjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA;AAEA,QAAI,SAAS;AAAb,QACI,oBAAoB,OAAO;AAE/B,QAAI,gBAAgB;AAEpB,aAAS,gBACL,UACA,YACA,UACA,eACA,eACA,UACA,SACA,iBAAiB;AACnB,UAAI,OAAO;AAEX,WAAK,YAAY;AAEjB,WAAK,mBAAmB;AAExB,eAAS,GAAG,mBAAmB,eAAe,SAAS,SAAS;AAC9D,YAAI,uBAAuB,QAAQ;AAEnC,aAAK,mBAAmB;AAExB,YAAI,KAAK,kBAAkB;AACzB,wBAAc,OAAO;AACrB,qBAAW,MAAM;AACjB,mBAAS,OAAO;AAAA,QAClB;AAEA,gBAAQ,QAAQ;AAAA,MAClB,CAAC;AAED,eAAS,UAAU,KAAK,QAAQ,IAAI;AAClC,YAAI,KAAK,IAAI,MAAM;AACnB,YAAI,MAAM,IAAI,WAAW;AACvB,iBAAO,GAAG,KAAK,MAAM,IAAI,SAAS;AAAA,QACpC;AAAA,MACF;AAEA,eAAS,yBAAyB,KAAK,QAAQ;AAC7C,kBAAU,KAAK,QAAQ,SAAS,IAAI,MAAM;AACxC,cAAI,KAAK,kBAAkB;AACzB;AAAA,UACF;AAEA,iBAAO,GAAG,MAAM,MAAM,IAAI;AAAA,QAC5B,CAAC;AAAA,MACH;AAEA,eAAS,wBAAwB,KAAK,QAAQ;AAC5C,kBAAU,KAAK,QAAQ,SAAS,IAAI,MAAM;AACxC,cAAI,KAAK,kBAAkB;AACzB,kBAAM,IAAI,MAAM,oBAAoB;AAAA,UACtC;AAEA,iBAAO,GAAG,MAAM,MAAM,IAAI;AAAA,QAC5B,CAAC;AAAA,MACH;AAEA,+BAAyB,YAAY,MAAM;AAE3C,+BAAyB,UAAU,MAAM;AAEzC,+BAAyB,eAAe,UAAU;AAElD,+BAAyB,UAAU,MAAM;AAEzC,+BAAyB,eAAe,UAAU;AAElD,8BAAwB,UAAU,WAAW;AAC7C,8BAAwB,UAAU,kBAAkB;AACpD,8BAAwB,UAAU,cAAc;AAChD,8BAAwB,UAAU,gBAAgB;AAClD,8BAAwB,UAAU,kBAAkB;AACpD,8BAAwB,UAAU,kBAAkB;AACpD,8BAAwB,UAAU,aAAa;AAC/C,8BAAwB,UAAU,aAAa;AAC/C,8BAAwB,UAAU,aAAa;AAC/C,8BAAwB,UAAU,gBAAgB;AAClD,8BAAwB,UAAU,oBAAoB;AACtD,8BAAwB,UAAU,aAAa;AAC/C,8BAAwB,UAAU,kBAAkB;AACpD,8BAAwB,UAAU,cAAc;AAChD,8BAAwB,UAAU,eAAe;AACjD,8BAAwB,UAAU,eAAe;AACjD,8BAAwB,UAAU,aAAa;AAC/C,8BAAwB,UAAU,aAAa;AAC/C,8BAAwB,UAAU,iBAAiB;AACnD,8BAAwB,UAAU,gBAAgB;AAClD,8BAAwB,UAAU,cAAc;AAEhD,gBAAU,eAAe,WAAW,SAAS,IAAI,MAAM;AACrD,YAAI,SAAS,KAAK,CAAC;AAEnB,YAAI,KAAK,oBAAoB,YAAY;AAAA,UACvC;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,GAAG,MAAM,GAAG;AACV;AAAA,QACF;AAEA,eAAO,GAAG,MAAM,MAAM,IAAI;AAAA,MAC5B,CAAC;AAAA,IACH;AAEA,oBAAgB,UAAU;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,WAAO,UAAU;AAIjB,aAAS,YAAY,SAAS,QAAQ;AACpC,aAAO,QAAQ,QAAQ,MAAM,IAAI;AAAA,IACnC;AAAA;AAAA;;;ACvIA;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA;AAEA,QAAI,SAAS;AAEb,QAAI,SAAS;AAAb,QACI,oBAAoB,OAAO;AAD/B,QAEI,yBAAyB,OAAO;AAFpC,QAGI,uBAAuB,OAAO;AAElC,QAAI,aAAa;AAAjB,QACI,eAAe;AAEnB,aAAS,qBAAqB,UAAU,UAAU;AAChD,UAAI,OAAO;AAEX,WAAK,YAAY;AAEjB,eAAS,GAAG,mBAAmB,SAAS,SAAS;AAC/C,YAAI,uBAAuB,QAAQ;AAEnC,YAAI,CAAC,sBAAsB;AACzB,eAAK,2BAA2B;AAAA,QAClC;AAAA,MACF,CAAC;AAED,eAAS,GAAG;AAAA,QACV;AAAA,QACA;AAAA,MACF,GAAG,WAAW;AACZ,aAAK,2BAA2B;AAAA,MAClC,CAAC;AAAA,IACH;AAEA,yBAAqB,UAAU,0BAA0B,SAAS,UAAU,SAAS;AACnF,UAAI,OAAO;AAEX,eAAS,QAAQ,SAAS,SAAS;AACjC,aAAK,uBAAuB,SAAS,OAAO;AAAA,MAC9C,CAAC;AAAA,IACH;AAEA,yBAAqB,UAAU,yBAAyB,SAAS,SAAS,SAAS;AACjF,UAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AAEA,UAAI,SACF,uCAAuC,QAAQ,QAAQ,MAAM,QAC1D,QAAQ,OAAO,kBAAkB,QAAQ,OAAO,WAAW,OAC3D,wBAAwB,QAAQ,OAAO,aAAa,MACvD;AAEF,UAAI,OAAO,OAAO,MAAM;AAExB,WAAK,UAAU,IAAI,SAAS,wBAAwB;AAAA,QAClD;AAAA,QACA;AAAA,QACA,MAAM;AAAA,UACJ,SAAS;AAAA,QACX;AAAA,MACF,CAAC;AAAA,IACH;AAEA,yBAAqB,UAAU,6BAA6B,SAAS,UAAU;AAC7E,UAAI,OAAO;AAEX,UAAI,CAAC,UAAU;AACb,aAAK,UAAU,OAAO,EAAE,MAAM,uBAAuB,CAAC;AAAA,MACxD,OAAO;AACL,iBAAS,QAAQ,SAAS,SAAS;AACjC,eAAK,0BAA0B,OAAO;AAAA,QACxC,CAAC;AAAA,MACH;AAAA,IACF;AAEA,yBAAqB,UAAU,4BAA4B,SAAS,SAAS;AAC3E,WAAK,UAAU,OAAO,EAAE,QAAiB,CAAC;AAAA,IAC5C;AAEA,yBAAqB,UAAU,CAAE,YAAY,UAAW;AAExD,WAAO,UAAU;AAAA;AAAA;;;AClFjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA,WAAO,UAAU,SAAS,KAAK,KAAI;AACjC,UAAI,IAAI;AAAS,eAAO,IAAI,QAAQ,GAAG;AACvC,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,YAAI,IAAI,CAAC,MAAM;AAAK,iBAAO;AAAA,MAC7B;AACA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACNA;AAAA;AAIA,QAAI;AACE,cAAQ;AAAA,IACd,SAAS,KAAK;AACR,cAAQ;AAAA,IACd;AAHM;AASN,QAAI,KAAK;AAMT,QAAI,WAAW,OAAO,UAAU;AAUhC,WAAO,UAAU,SAAS,IAAG;AAC3B,aAAO,IAAI,UAAU,EAAE;AAAA,IACzB;AASA,aAAS,UAAU,IAAI;AACrB,UAAI,CAAC,MAAM,CAAC,GAAG,UAAU;AACvB,cAAM,IAAI,MAAM,qCAAqC;AAAA,MACvD;AACA,WAAK,KAAK;AACV,WAAK,OAAO,GAAG;AAAA,IACjB;AAUA,cAAU,UAAU,MAAM,SAAS,MAAK;AAEtC,UAAI,KAAK,MAAM;AACb,aAAK,KAAK,IAAI,IAAI;AAClB,eAAO;AAAA,MACT;AAGA,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,IAAI,MAAM,KAAK,IAAI;AACvB,UAAI,CAAC,CAAC;AAAG,YAAI,KAAK,IAAI;AACtB,WAAK,GAAG,YAAY,IAAI,KAAK,GAAG;AAChC,aAAO;AAAA,IACT;AAYA,cAAU,UAAU,SAAS,SAAS,MAAK;AACzC,UAAI,qBAAqB,SAAS,KAAK,IAAI,GAAG;AAC5C,eAAO,KAAK,eAAe,IAAI;AAAA,MACjC;AAGA,UAAI,KAAK,MAAM;AACb,aAAK,KAAK,OAAO,IAAI;AACrB,eAAO;AAAA,MACT;AAGA,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,IAAI,MAAM,KAAK,IAAI;AACvB,UAAI,CAAC;AAAG,YAAI,OAAO,GAAG,CAAC;AACvB,WAAK,GAAG,YAAY,IAAI,KAAK,GAAG;AAChC,aAAO;AAAA,IACT;AAUA,cAAU,UAAU,iBAAiB,SAASC,KAAG;AAC/C,UAAI,MAAM,KAAK,MAAM;AACrB,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,YAAIA,IAAG,KAAK,IAAI,CAAC,CAAC,GAAG;AACnB,eAAK,OAAO,IAAI,CAAC,CAAC;AAAA,QACpB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAcA,cAAU,UAAU,SAAS,SAAS,MAAM,OAAM;AAEhD,UAAI,KAAK,MAAM;AACb,YAAI,gBAAgB,OAAO,OAAO;AAChC,cAAI,UAAU,KAAK,KAAK,OAAO,MAAM,KAAK,GAAG;AAC3C,iBAAK,KAAK,OAAO,IAAI;AAAA,UACvB;AAAA,QACF,OAAO;AACL,eAAK,KAAK,OAAO,IAAI;AAAA,QACvB;AACA,eAAO;AAAA,MACT;AAGA,UAAI,gBAAgB,OAAO,OAAO;AAChC,YAAI,CAAC,OAAO;AACV,eAAK,OAAO,IAAI;AAAA,QAClB,OAAO;AACL,eAAK,IAAI,IAAI;AAAA,QACf;AAAA,MACF,OAAO;AACL,YAAI,KAAK,IAAI,IAAI,GAAG;AAClB,eAAK,OAAO,IAAI;AAAA,QAClB,OAAO;AACL,eAAK,IAAI,IAAI;AAAA,QACf;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AASA,cAAU,UAAU,QAAQ,WAAU;AACpC,UAAI,YAAY,KAAK,GAAG,aAAa,OAAO,KAAK;AACjD,UAAI,MAAM,UAAU,QAAQ,cAAc,EAAE;AAC5C,UAAI,MAAM,IAAI,MAAM,EAAE;AACtB,UAAI,OAAO,IAAI,CAAC;AAAG,YAAI,MAAM;AAC7B,aAAO;AAAA,IACT;AAUA,cAAU,UAAU,MACpB,UAAU,UAAU,WAAW,SAAS,MAAK;AAC3C,aAAO,KAAK,OACR,KAAK,KAAK,SAAS,IAAI,IACvB,CAAC,CAAE,CAAC,MAAM,KAAK,MAAM,GAAG,IAAI;AAAA,IAClC;AAAA;AAAA;;;AC9LA;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA;AAEA,QAAI,aAAa;AAEjB,QAAI,gBAAgB;AAApB,QACI,KAAK,cAAc;AADvB,QAEI,qBAAqB,cAAc;AAEvC,QAAI,SAAS;AAAb,QACI,oBAAoB,OAAO;AAD/B,QAEI,uBAAuB,OAAO;AAElC,QAAI,mBAAmB;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,aAAS,QAAQ,SAAS;AACxB,aAAO,QAAQ;AAAA,IACjB;AAEA,aAAS,eAAe,UAAU,iBAAiB,QAAQ,eAAe,sBAAsB;AAC9F,UAAI,OAAO;AAEX,WAAK,YAAY;AACjB,WAAK,mBAAmB;AACxB,WAAK,wBAAwB;AAC7B,WAAK,iBAAiB;AAEtB,WAAK,eAAe,OAAO,aAAa,EAAE;AAE1C,eAAS,GAAG,sBAAsB,KAAO,SAAS,SAAS;AACzD,YAAI,UAAU,QAAQ;AAEtB,YAAI,CAAC,GAAG,SAAS,iBAAiB,GAAG;AACnC;AAAA,QACF;AAEA,YAAI,CAAC,KAAK,qBAAqB,GAAG;AAChC,eAAK,aAAa;AAElB,qBAAW,KAAK,YAAY,EAAE,IAAI,SAAS;AAG3C,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAED,eAAS,GAAG,mBAAmB,SAAS,SAAS;AAC/C,YAAI,uBAAuB,QAAQ;AAEnC,YAAI,CAAC,sBAAsB;AACzB,qBAAW,KAAK,YAAY,EAAE,OAAO,SAAS;AAAA,QAChD;AAAA,MACF,CAAC;AAAA,IACH;AAEA,mBAAe,UAAU,uBAAuB,WAAW;AACzD,UAAI,uBAAuB;AAE3B,WAAK,iBAAiB,QAAQ,SAAS,SAAS;AAC9C,YAAI,CAAC,GAAG,SAAS,gBAAgB,KAC1B,CAAC,GAAG,SAAS,kBAAkB,KAC/B,CAAC,QAAQ,OAAO,GACrB;AACA,iCAAuB;AAAA,QACzB;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAEA,mBAAe,UAAU,eAAe,SAAS,UAAU;AACzD,UAAI,OAAO;AAEX,UAAI,WAAW,CAAC;AAEhB,WAAK,iBAAiB,QAAQ,SAAS,SAAS;AAC9C,YAAI,CAAC,GAAG,SAAS,gBAAgB,KAC1B,CAAC,GAAG,SAAS,kBAAkB,KAC/B,CAAC,QAAQ,OAAO,GACrB;AACA,eAAK,YAAY,OAAO;AAExB,cAAI,SAAS,QAAQ,QAAQ,IAAI,GAAG;AAClC,iBAAK,eAAe,iBAAiB,QAAQ,OAAO,kBAAkB,SAAS;AAE/E,qBAAS,KAAK,QAAQ,IAAI;AAAA,UAC5B;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,mBAAe,UAAU,cAAc,SAAS,SAAS;AACvD,WAAK,sBAAsB,uBAAuB,SAAS;AAAA,QACzD,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAEA,mBAAe,UAAU,CAAE,YAAY,mBAAmB,UAAU,iBAAiB,sBAAuB;AAE5G,WAAO,UAAU;AAAA;AAAA;;;AC1GjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA;AAEA,QAAI,KAAK,wBAAoC;AAE7C,QAAI,SAAS;AAAb,QACI,oBAAoB,OAAO;AAE/B,QAAI,qBAAqB,iBAAiB,SAAS,eAAe,EAAE,iBAAiB,oCAAoC;AAAzH,QACI,iBAAiB,iBAAiB,SAAS,eAAe,EAAE,iBAAiB,mCAAmC;AAEpH,aAAS,QAAQ,SAAS;AACxB,UAAI,WAAW,QAAQ,SAAS,OAAO,cAAc;AAErD,UAAI,QAAQ,SAAS,QAAQ,QAAQ,YAAY;AAEjD,UAAI,SAAS,QAAQ,CAAC,GAAG;AACvB,eAAO,SAAS,QAAQ,CAAC;AAAA,MAC3B,OAAO;AACL,eAAO,SAAS,CAAC;AAAA,MACnB;AAAA,IACF;AAEA,aAAS,eAAe,YAAY;AAClC,aAAO,GAAG,YAAY,mBAAmB;AAAA,IAC3C;AAGA,aAAS,yBAAyB,UAAU,iBAAiB,iBAAiB;AAC5E,UAAI,OAAO;AAEX,WAAK,mBAAmB;AACxB,WAAK,mBAAmB;AAExB,eAAS,GAAG,mBAAmB,SAAS,SAAS;AAC/C,YAAI,uBAAuB,QAAQ;AAEnC,YAAI,CAAC,sBAAsB;AACzB,eAAK,mBAAmB;AAAA,QAC1B,OAAO;AACL,eAAK,wBAAwB;AAAA,QAC/B;AAAA,MACF,CAAC;AAAA,IACH;AAEA,6BAAyB,UAAU,0BAA0B,WAAW;AACtE,UAAI,OAAO;AAEX,UAAI,oBAAoB,KAAK,iBAAiB,OAAO,SAAS,SAAS;AACrE,eAAO,GAAG,SAAS,uBAAuB;AAAA,MAC5C,CAAC;AAED,wBAAkB,QAAQ,SAAS,kBAAkB;AACnD,YAAI,iBAAiB,SAAS,OAAO,cAAc,EAAE,QAAQ;AAC3D,eAAK;AAAA,YACH;AAAA,YACA,iBAAiB,SAAS,OAAO,cAAc,EAAE,CAAC;AAAA,UACpD;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,6BAAyB,UAAU,qBAAqB,WAAW;AACjE,UAAI,OAAO;AAEX,UAAI,oBAAoB,KAAK,iBAAiB,OAAO,SAAS,SAAS;AACrE,eAAO,GAAG,SAAS,uBAAuB;AAAA,MAC5C,CAAC;AAED,wBAAkB,QAAQ,SAAS,kBAAkB;AACnD,YAAI,iBAAiB,SAAS,OAAO,cAAc,EAAE,QAAQ;AAC3D,eAAK,kBAAkB,gBAAgB;AAAA,QACzC;AAAA,MACF,CAAC;AAAA,IACH;AAEA,6BAAyB,UAAU,oBAAoB,SAAS,SAAS;AACvE,UAAI,QAAQ,cAAc;AACxB,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF;AAEA,6BAAyB,UAAU,kBAAkB,SAAS,SAAS;AACrE,UAAI,OAAO;AAEX,UAAI,WAAW,QAAQ,SAAS,OAAO,cAAc;AAErD,UAAI,CAAC,SAAS,QAAQ;AACpB;AAAA,MACF;AAEA,UAAI,eAAe,QAAQ;AAE3B,UAAI,cAAc;AAGhB,gBAAQ,eAAe,QAAQ,OAAO;AAAA,MACxC,OAAO;AAGL,gBAAQ,eAAe,SAAS,CAAC;AAAA,MACnC;AAGA,cAAQ,SAAS,QAAQ,SAASC,WAAU;AAC1C,YAAIA,cAAa,QAAQ,cAAc;AACrC,eAAK,SAASA,WAAU,cAAc;AAAA,QACxC,OAAO;AACL,eAAK,SAASA,WAAU,kBAAkB;AAAA,QAC5C;AAAA,MACF,CAAC;AAAA,IACH;AAEA,6BAAyB,UAAU,WAAW,SAAS,cAAc,OAAO;AAC1E,UAAI,iBAAiB,aAAa;AAElC,qBAAe,GAAG,IAAI,UAAU,KAAK;AAErC,UAAI,MAAM,KAAK,iBAAiB,YAAY,YAAY;AAExD,WAAK,iBAAiB,OAAO,cAAc,cAAc,GAAG;AAAA,IAC9D;AAEA,6BAAyB,UAAU,CAAE,YAAY,mBAAmB,iBAAkB;AAEtF,WAAO,UAAU;AAAA;AAAA;;;AC5HjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA;AAEA,QAAI,SAAS;AAAb,QACI,aAAa;AADjB,QAEI,WAAW;AAFf,QAGI,WAAW;AAEf,QAAI,gBAAgB;AAApB,QACI,oBAAoB,cAAc;AADtC,QAEI,KAAK,cAAc;AAFvB,QAGI,eAAe,cAAc;AAEjC,QAAI,SAAS;AAAb,QACI,uBAAuB,OAAO;AADlC,QAEI,sBAAsB,OAAO;AAFjC,QAGI,oBAAoB,OAAO;AAH/B,QAII,yBAAyB,OAAO;AAJpC,QAKI,iCAAiC,OAAO;AAE5C,aAAS,eAAe,SAAS;AAC/B,aAAQ,WAAW,QAAQ,eAAe;AAAA,IAC5C;AAEA,aAAS,IAAI,UAAU,eAAe,wBAAwB,QAAQ;AACpE,UAAI,OAAO;AAEX,WAAK,iBAAiB;AACtB,WAAK,0BAA0B;AAC/B,WAAK,UAAU;AAEf,WAAK,MAAM;AAEX,eAAS,GAAG,sBAAsB,SAAS,SAAS;AAClD,YAAI,UAAU,QAAQ,SAClB,cAAc,eAAe,OAAO;AAExC,YAAI,GAAG,SAAS,uBAAuB,GAAG;AACxC,eAAK,IAAI,eAAe,sBAAsB,QAAQ,yBAAyB;AAAA,QACjF,WAAW,GAAG,SAAS,mBAAmB,GAAG;AAC3C,eAAK,IAAI,eAAe,iBAAiB,QAAQ,yBAAyB;AAAA,QAC5E,WAAW,GAAG,SAAS,CAAC,+BAA+B,6BAA6B,CAAC,GAAG;AACtF,eAAK,IAAI,eAAe,sBAAsB,QAAQ,mCAAmC;AAAA,QAC3F,WAAW,GAAG,SAAS,iBAAiB,GAAG;AACzC,eAAK,IAAI,eAAe,eAAe,QAAQ,kBAAkB;AAAA,QACnE,WAAW,GAAG,SAAS,iBAAiB,GAAG;AACzC,eAAK,IAAI,eAAe,eAAe,QAAQ,kBAAkB;AAAA,QACnE,WAAW,GAAG,SAAS,kBAAkB,GAAG;AAC1C,eAAK,IAAI,eAAe,gBAAgB,QAAQ,mBAAmB;AAAA,QACrE,WAAW,GAAG,SAAS,iBAAiB,GAAG;AACzC,eAAK,IAAI,eAAe,eAAe,QAAQ,4BAA4B;AAAA,QAC7E,WAAW,GAAG,SAAS,WAAW,GAAG;AACnC,eAAK,IAAI,eAAe,QAAQ,QAAQ,gBAAgB;AAAA,QAC1D,WAAW,GAAG,SAAS,eAAe,GAAG;AACvC,eAAK,IAAI,eAAe,aAAa,QAAQ,gBAAgB;AAAA,QAC/D,WAAW,GAAG,SAAS,uBAAuB,GAAG;AAC/C,cAAI,QAAQ,SAAS,SAAS,GAAG;AAC/B;AAAA,UACF;AAEA,cAAI,mBAAmB,eAAe,QAAQ,YAAY;AAE1D,cAAI,OAAO,eAAe;AAE1B,cAAI,kBAAkB;AACpB,mBAAO,KAAK,OAAO,2DAA2D,gBAAgB;AAAA,UAChG;AAEA,eAAK,IAAI,MAAM,QAAQ,uBAAuB;AAAA,QAChD;AAAA,MACF,CAAC;AAED,eAAS,GAAG,qBAAqB,SAAS,SAAS;AACjD,YAAI,UAAU,QAAQ,SAClB,cAAc,eAAe,OAAO;AAExC,YAAI,GAAG,SAAS,eAAe,GAAG;AAEhC,cAAI,aAAa,kBAAkB,OAAO,GAAG,+BAA+B,GAAG;AAC7E,iBAAK,IAAI,eAAe,uBAAuB,QAAQ,+BAA+B;AAAA,UACxF,OAAO;AACL,iBAAK,IAAI,eAAe,aAAa,QAAQ,0BAA0B;AAAA,UACzE;AAAA,QACF;AAAA,MACF,CAAC;AAED,eAAS,GAAG,gCAAgC,SAAS,SAAS;AAC5D,YAAI,oBAAoB,QAAQ,mBAC5B,SAAS,QAAQ;AAErB,YAAI,GAAG,QAAQ,cAAc,GAAG;AAC9B,eAAK,IAAI,aAAa,oBAAoB,YAAY,WAAW,UAAU;AAAA,QAC7E,OAAO;AACL,eAAK,IAAI,gBAAgB,oBAAoB,YAAY,QAAQ,UAAU;AAAA,QAC7E;AAAA,MACF,CAAC;AAED,eAAS,GAAG,mBAAmB,SAAS,SAAS;AAC/C,YAAI,uBAAuB,QAAQ;AAEnC,YAAI,CAAC,sBAAsB;AACzB,eAAK,SAAS;AAEd,qBAAW,KAAK,SAAS,EAAE,IAAI,QAAQ;AAAA,QACzC;AAAA,MACF,CAAC;AAED,eAAS,GAAG,wBAAwB,SAAS,SAAS;AACpD,aAAK,SAAS;AAEd,mBAAW,KAAK,SAAS,EAAE,IAAI,QAAQ;AAAA,MACzC,CAAC;AAAA,IACH;AAEA,QAAI,UAAU,QAAQ,WAAW;AAC/B,UAAI,OAAO;AAEX,WAAK,YAAY;AAAA,QACf;AAAA,MAWF;AAEA,WAAK,cAAc,SAAS,gBAAgB,KAAK,SAAS;AAE1D,WAAK,UAAU,SAAS,YAAY,KAAK,SAAS;AAElD,eAAS,KAAK,KAAK,SAAS,SAAS,SAAS,GAAG;AAC/C,UAAE,gBAAgB;AAAA,MACpB,CAAC;AAED,eAAS,KAAK,KAAK,SAAS,aAAa,SAAS,GAAG;AACnD,UAAE,gBAAgB;AAAA,MACpB,CAAC;AAED,WAAK,QAAQ,SAAS,UAAU,KAAK,SAAS;AAE9C,eAAS,KAAK,KAAK,OAAO,SAAS,WAAW;AAC5C,mBAAW,KAAK,SAAS,EAAE,IAAI,QAAQ;AAAA,MACzC,CAAC;AAED,WAAK,OAAO,SAAS,kBAAkB,KAAK,SAAS;AAErD,eAAS,KAAK,KAAK,MAAM,SAAS,WAAW;AAC3C,mBAAW,KAAK,SAAS,EAAE,IAAI,QAAQ;AAAA,MACzC,CAAC;AAED,WAAK,QAAQ,aAAa,EAAE,YAAY,KAAK,SAAS;AAEtD,WAAK,eAAe,OAAO,uFAAuF;AAElH,eAAS,KAAK,KAAK,cAAc,SAAS,WAAW;AACnD,mBAAW,KAAK,SAAS,EAAE,OAAO,QAAQ;AAAA,MAC5C,CAAC;AAED,WAAK,wBAAwB,SAAS,KAAK,cAAc,CAAC;AAAA,IAC5D;AAEA,QAAI,UAAU,SAAS,WAAW;AAChC,UAAI,YAAY,KAAK;AAErB,UAAI,WAAW,SAAS,EAAE,IAAI,QAAQ,GAAG;AACvC,mBAAW,SAAS,EAAE,OAAO,QAAQ;AAAA,MACvC,OAAO;AACL,mBAAW,SAAS,EAAE,IAAI,QAAQ;AAAA,MACpC;AAAA,IACF;AAEA,QAAI,UAAU,MAAM,SAAS,MAAM,MAAM,MAAM;AAC7C,iBAAW,KAAK,WAAW,EAAE,IAAI,QAAQ;AAEzC,UAAI,OAAO,oBAAI,KAAK;AAEpB,UAAI,aAAa,KAAK,mBAAmB,IAAI,MAAM,KAAK,mBAAmB;AAE3E,WAAK,eAAe,iBAAiB,MAAM,MAAM,IAAI;AAErD,UAAI;AAEJ,UAAI,CAAC,MAAM;AACT,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,SAAS,MAAM,GAAG;AACzB,qBAAa,uBAAuB,OAAO;AAAA,MAC7C,OAAO;AACL,qBAAa,uBAAuB,OAAO;AAAA,MAC7C;AAEA,UAAI,WAAW,OAAO,qBAAqB,OAAO,0BAA0B,aAAa,YAAY,aAAa,YAAY,OAAO,MAAM;AAE3I,WAAK,QAAQ,YAAY,QAAQ;AAEjC,WAAK,QAAQ,YAAY,KAAK,QAAQ;AAAA,IACxC;AAEA,QAAI,UAAU,WAAW,WAAW;AAClC,aAAO,KAAK,QAAQ,YAAY;AAC9B,aAAK,QAAQ,YAAY,KAAK,QAAQ,UAAU;AAAA,MAClD;AAEA,WAAK,cAAc,OAAO,6CAA6C;AAEvE,WAAK,QAAQ,YAAY,KAAK,WAAW;AAAA,IAC3C;AAEA,QAAI,UAAU,CAAE,YAAY,iBAAiB,0BAA0B,QAAS;AAEhF,WAAO,UAAU;AAAA;AAAA;;;ACvNjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA;AAEA,QAAI,SAAS;AAEb,QAAI,SAAS;AAAb,QACI,oBAAoB,OAAO;AAE/B,QAAI,4BAA4B;AAEhC,aAAS,cAAc,UAAU,QAAQ;AACvC,UAAI,OAAO;AAEX,WAAK,YAAY;AACjB,WAAK,UAAU;AAEf,WAAK,MAAM;AAEX,eAAS,GAAG,mBAAmB,SAAS,SAAS;AAC/C,YAAI,uBAAuB,QAAQ;AAEnC,YAAI,CAAC,sBAAsB;AACzB,eAAK,UAAU;AAAA,QACjB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,kBAAc,UAAU,QAAQ,WAAW;AACzC,WAAK,YAAY,OAAO,mCAAmC;AAE3D,WAAK,QAAQ,aAAa,EAAE,YAAY,KAAK,SAAS;AAAA,IACxD;AAEA,kBAAc,UAAU,mBAAmB,SAAS,MAAM,MAAM,MAAM;AACpE,UAAI;AAEJ,UAAI,CAAC,MAAM;AACT,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,SAAS,MAAM,GAAG;AACzB,qBAAa,eAAe,OAAO;AAAA,MACrC,OAAO;AACL,qBAAa,kBAAkB,OAAO;AAAA,MACxC;AAEA,UAAI,eAAe;AAAA,QACjB,8BAA8B,OAAO,0BACX,aAAa,YACrC,OACF;AAAA,MACF;AAEA,WAAK,UAAU,YAAY,YAAY;AAGvC,aAAO,KAAK,UAAU,SAAS,SAAS,GAAG;AACzC,aAAK,UAAU,SAAS,CAAC,EAAE,OAAO;AAAA,MACpC;AAEA,iBAAW,WAAW;AACpB,qBAAa,OAAO;AAAA,MACtB,GAAG,yBAAyB;AAAA,IAC9B;AAEA,kBAAc,UAAU,YAAY,WAAW;AAC7C,aAAO,KAAK,UAAU,SAAS,QAAQ;AACrC,aAAK,UAAU,SAAS,CAAC,EAAE,OAAO;AAAA,MACpC;AAAA,IACF;AAEA,kBAAc,UAAU,CAAE,YAAY,QAAS;AAE/C,WAAO,UAAU;AAAA;AAAA;;;ACxEjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA;AAEA,QAAI,SAAS;AAAb,QACI,aAAa;AADjB,QAEI,WAAW;AAEf,QAAI,SAAS;AAAb,QACI,oBAAoB,OAAO;AAD/B,QAEI,wBAAwB,OAAO;AAFnC,QAGI,yBAAyB,OAAO;AAHpC,QAII,yBAAyB,OAAO;AAJpC,QAKI,0BAA0B,OAAO;AALrC,QAMI,iCAAiC,OAAO;AAE5C,QAAI,cAAc;AAAlB,QACI,eAAe;AAEnB,aAAS,gBAAgB,UAAU,wBAAwB,eAAe,QAAQ;AAChF,UAAI,OAAO;AAEX,WAAK,YAAY;AACjB,WAAK,0BAA0B;AAC/B,WAAK,iBAAiB;AAEtB,WAAK,eAAe,OAAO,aAAa,EAAE;AAE1C,WAAK,WAAW;AAChB,WAAK,WAAW;AAEhB,WAAK,MAAM;AAGX,eAAS,GAAG,gCAAgC,SAAS,SAAS;AAC5D,YAAI,SAAS,QAAQ;AAErB,YAAI,CAAC,OAAO,QAAQ;AAClB,eAAK,SAAS;AACd,eAAK,QAAQ;AAEb,wBAAc,iBAAiB,oBAAoB,MAAM;AAAA,QAC3D;AAAA,MACF,CAAC;AAED,eAAS,GAAG;AAAA,QACV;AAAA,QACA;AAAA,MACF,GAAG,WAAW;AACZ,aAAK,WAAW;AAChB,aAAK,QAAQ;AAAA,MACf,CAAC;AAED,eAAS,GAAG,yBAAyB,SAAS,SAAS;AACrD,YAAI,YAAY,QAAQ;AAExB,YAAI,KAAK,UAAU;AACjB,oBAAU,MAAM;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,oBAAgB,UAAU,QAAQ,WAAW;AAC3C,WAAK,eAAe;AAAA,QAClB,+DACE,cACF;AAAA,MACF;AAEA,eAAS,KAAK,KAAK,cAAc,SAAS,KAAK,OAAO,KAAK,IAAI,CAAC;AAEhE,WAAK,wBAAwB,SAAS,KAAK,cAAc,CAAC;AAAA,IAC5D;AAEA,oBAAgB,UAAU,SAAS,WAAW;AAC5C,UAAI,CAAC,KAAK,UAAU;AAClB;AAAA,MACF;AAEA,UAAI,KAAK,UAAU;AACjB,aAAK,QAAQ;AAAA,MACf,OAAO;AACL,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AAEA,oBAAgB,UAAU,QAAQ,WAAW;AAC3C,UAAI,CAAC,KAAK,UAAU;AAClB;AAAA,MACF;AAEA,iBAAW,KAAK,YAAY,EAAE,OAAO,QAAQ;AAC7C,iBAAW,KAAK,YAAY,EAAE,IAAI,QAAQ;AAE1C,WAAK,aAAa,YAAY;AAE9B,WAAK,UAAU,KAAK,sBAAsB;AAE1C,WAAK,eAAe,iBAAiB,oBAAoB,MAAM;AAE/D,WAAK,WAAW;AAAA,IAClB;AAEA,oBAAgB,UAAU,UAAU,WAAW;AAC7C,UAAI,CAAC,KAAK,UAAU;AAClB;AAAA,MACF;AAEA,iBAAW,KAAK,YAAY,EAAE,IAAI,QAAQ;AAC1C,iBAAW,KAAK,YAAY,EAAE,OAAO,QAAQ;AAE7C,WAAK,aAAa,YAAY;AAE9B,WAAK,UAAU,KAAK,qBAAqB;AAEzC,WAAK,eAAe,iBAAiB,mBAAmB,MAAM;AAE9D,WAAK,WAAW;AAAA,IAClB;AAEA,oBAAgB,UAAU,WAAW,WAAW;AAC9C,WAAK,WAAW;AAEhB,iBAAW,KAAK,YAAY,EAAE,OAAO,UAAU;AAAA,IACjD;AAEA,oBAAgB,UAAU,aAAa,WAAW;AAChD,WAAK,WAAW;AAEhB,iBAAW,KAAK,YAAY,EAAE,OAAO,QAAQ;AAC7C,iBAAW,KAAK,YAAY,EAAE,IAAI,UAAU;AAAA,IAC9C;AAEA,oBAAgB,UAAU,CAAE,YAAY,0BAA0B,iBAAiB,QAAS;AAE5F,WAAO,UAAU;AAAA;AAAA;;;ACrIjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA;AAEA,QAAI,SAAS;AAAb,QACI,oBAAoB,OAAO;AAE/B,QAAI,qBAAqB;AAEzB,aAAS,sBAAsB,UAAU,iBAAiB,iBAAiB;AACzE,UAAI,OAAO;AAEX,WAAK,mBAAmB;AACxB,WAAK,mBAAmB;AAExB,WAAK,gBAAgB,CAAC;AAEtB,eAAS,GAAG,mBAAmB,oBAAoB,SAAS,SAAS;AACnE,YAAI,uBAAuB,QAAQ;AAEnC,YAAI,CAAC,sBAAsB;AACzB,eAAK,YAAY;AAAA,QACnB,OAAO;AACL,eAAK,eAAe;AAAA,QACtB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,0BAAsB,UAAU,iBAAiB,WAAW;AAC1D,UAAI,OAAO;AAEX,WAAK,iBAAiB,QAAQ,SAAS,SAAS;AAC9C,aAAK,cAAc,QAAQ,EAAE,IAAI;AAAA,UAC/B,QAAQ,QAAQ,eAAe,GAAG,IAAI,QAAQ;AAAA,UAC9C,MAAM,QAAQ,eAAe,GAAG,IAAI,MAAM;AAAA,QAC5C;AAEA,aAAK,SAAS,SAAS,QAAQ,MAAM;AAAA,MACvC,CAAC;AAAA,IACH;AAEA,0BAAsB,UAAU,cAAc,WAAW;AACvD,UAAI,OAAO;AAEX,WAAK,iBAAiB,QAAQ,SAAS,SAAS;AAC9C,YAAI,KAAK,cAAc,QAAQ,EAAE,GAAG;AAClC,eAAK,SAAS,SAAS,KAAK,cAAc,QAAQ,EAAE,EAAE,QAAQ,KAAK,cAAc,QAAQ,EAAE,EAAE,IAAI;AAAA,QACnG;AAAA,MACF,CAAC;AAED,WAAK,gBAAgB,CAAC;AAAA,IACxB;AAEA,0BAAsB,UAAU,WAAW,SAAS,SAAS,QAAQ,MAAM;AACzE,UAAI,iBAAiB,QAAQ;AAE7B,qBAAe,GAAG,IAAI,UAAU,MAAM;AACtC,qBAAe,GAAG,IAAI,QAAQ,IAAI;AAElC,UAAI,MAAM,KAAK,iBAAiB,YAAY,OAAO;AAEnD,UAAI,OAAO,QAAQ,YAAY,eAAe;AAE9C,WAAK,iBAAiB,OAAO,MAAM,SAAS,GAAG;AAAA,IACjD;AAEA,0BAAsB,UAAU,CAAE,YAAY,mBAAmB,iBAAkB;AAEnF,WAAO,UAAU;AAAA;AAAA;;;AClEjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA;AAEA,QAAI,SAAS;AAAb,QACI,oBAAoB,OAAO;AAD/B,QAEI,yBAAyB,OAAO;AAEpC,aAAS,mBAAmB,UAAU;AACpC,WAAK,wBAAwB;AAE7B,eAAS,GAAG,mBAAmB,KAAK,MAAM,KAAK,IAAI,CAAC;AAEpD,eAAS,GAAG,wBAAwB,KAAK,MAAM,KAAK,IAAI,CAAC;AAAA,IAC3D;AAEA,uBAAmB,UAAU,UAAU,WAAW;AAChD,UAAI,oBAAoB,KAAK;AAE7B,WAAK;AAEL,aAAO;AAAA,IACT;AAEA,uBAAmB,UAAU,QAAQ,WAAW;AAC9C,WAAK,wBAAwB;AAAA,IAC/B;AAEA,uBAAmB,UAAU,CAAE,UAAW;AAE1C,WAAO,UAAU;AAAA;AAAA;;;AC5BjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA;AAEA,QAAI,SAAS;AAAb,QACI,oBAAoB,OAAO;AAD/B,QAEI,iCAAiC,OAAO;AAF5C,QAGI,kCAAkC,OAAO;AAH7C,QAII,+BAA+B,OAAO;AAJ1C,QAKI,gCAAgC,OAAO;AAE3C,QAAI,eAAe;AAEnB,aAAS,wBAAwB,WAAW,UAAU,kBAAkB,iBAAiB;AACvF,UAAI,OAAO;AAEX,WAAK,aAAa;AAClB,WAAK,YAAY;AACjB,WAAK,oBAAoB;AACzB,WAAK,mBAAmB;AAExB,WAAK,UAAU,GAAG,gCAAgC,cAAc,SAAS,SAAS;AAChF,YAAI,SAAS,QAAQ,QACjB,oBAAoB,QAAQ;AAEhC,YAAI,6BAA6B,iBAAiB,oBAAoB,MAAM,EAAE,OAAO,SAAS,iBAAiB;AAC7G,iBAAO,CAAC,gBAAgB;AAAA,QAC1B,CAAC;AAED,YAAI,2BAA2B,WAAW,GAAG;AAC3C,eAAK,oBAAoB,mBAAmB,MAAM;AAAA,QACpD,WAAW,2BAA2B,SAAS,GAAG;AAChD,eAAK,oBAAoB,iBAAiB;AAAA,QAC5C;AAAA,MACF,CAAC;AAED,WAAK,UAAU,GAAG,iCAAiC,cAAc,SAAS,SAAS;AACjF,YAAI,SAAS,QAAQ,QACjB,oBAAoB,QAAQ;AAEhC,YAAI,6BAA6B,iBAC9B,oBAAoB,MAAM,EAC1B,OAAO,SAAS,iBAAiB;AAChC,iBAAO,sBAAsB,gBAAgB,qBAAqB,CAAC,gBAAgB;AAAA,QACrF,CAAC;AAGH,YAAI,2BAA2B,UACxB,sBAAsB,OAAO,sBAAsB;AAExD,eAAK,oBAAoB,2BAA2B,CAAC,EAAE,mBAAmB,MAAM;AAAA,QAElF,OAAO;AACL,iBAAO,OAAO;AAAA,QAChB;AAGA,YAAI,CAAC,OAAO,QAAQ;AAClB,0BAAgB,QAAQ,SAAS,SAAS;AACxC,mBAAO,QAAQ;AAAA,UACjB,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAED,eAAS,GAAG,mBAAmB,WAAW;AACxC,wBAAgB,QAAQ,SAAS,SAAS;AACxC,iBAAO,QAAQ;AAAA,QACjB,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,4BAAwB,UAAU,sBAAsB,SAAS,mBAAmB,QAAQ;AAC1F,WAAK,WAAW,8BAA8B,iBAAiB;AAE/D,aAAO,uBAAuB;AAE9B,WAAK,UAAU,KAAK,8BAA8B;AAAA,QAChD;AAAA,MACF,CAAC;AAAA,IACH;AAEA,4BAAwB,UAAU,sBAAsB,SAAS,mBAAmB;AAClF,WAAK,WAAW,8BAA8B,iBAAiB;AAE/D,WAAK,UAAU,KAAK,+BAA+B;AAAA,QACjD;AAAA,MACF,CAAC;AAAA,IACH;AAEA,4BAAwB,UAAU,WAAW,SAAS,QAAQ;AAC5D,UAAI,OAAO;AAEX,UAAI,6BAA6B,KAAK,kBAAkB,oBAAoB,MAAM;AAElF,UAAI,uBAAuB,OAAO;AAElC,UAAI,8BAA8B;AAElC,eAAS,IAAI,GAAG,IAAI,2BAA2B,QAAQ,KAAK;AAC1D,YAAI,2BAA2B,CAAC,EAAE,sBAAsB,sBAAsB;AAC5E;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF;AAEA,iCAA2B,QAAQ,SAAS,iBAAiB;AAC3D,aAAK,oBAAoB,gBAAgB,iBAAiB;AAAA,MAC5D,CAAC;AAED,UAAI,gCAAgC,2BAA2B,SAAS,GAAG;AAGzE,aAAK,oBAAoB,2BAA2B,CAAC,EAAE,mBAAmB,MAAM;AAAA,MAClF,OAAO;AAGL,aAAK,oBAAoB,2BAA2B,8BAA8B,CAAC,EAAE,mBAAmB,MAAM;AAAA,MAChH;AAAA,IACF;AAEA,4BAAwB,UAAU,CAAE,aAAa,YAAY,oBAAoB,iBAAkB;AAEnG,WAAO,UAAU;AAAA;AAAA;;;ACzHjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA;AAEA,QAAI,SAAS;AAAb,QACI,oBAAoB,OAAO;AAD/B,QAEI,yBAAyB,OAAO;AAFpC,QAGI,iCAAiC,OAAO;AAH5C,QAII,kCAAkC,OAAO;AAE7C,aAAS,iBAAiB,UAAU,oBAAoB;AACtD,UAAI,OAAO;AAEX,WAAK,YAAY;AACjB,WAAK,sBAAsB;AAE3B,WAAK,mBAAmB,CAAC;AAGzB,eAAS,GAAG,CAAE,mBAAmB,sBAAuB,GAAG,WAAW;AACpE,aAAK,mBAAmB,CAAC;AAAA,MAC3B,CAAC;AAAA,IACH;AAQA,qBAAiB,UAAU,SAAS,SAAS,QAAQ,yBAAyB;AAC5E,UAAI,oBAAoB,KAAK,oBAAoB,QAAQ;AAEzD,UAAI,kBAAkB;AAAA,QACpB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,WAAK,iBAAiB,KAAK,eAAe;AAE1C,WAAK,UAAU,KAAK,gCAAgC,eAAe;AAEnE,aAAO;AAAA,IACT;AAEA,qBAAiB,UAAU,SAAS,SAAS,mBAAmB;AAC9D,WAAK,mBAAmB,KAAK,iBAAiB,OAAO,SAAS,iBAAiB;AAC7E,eAAO,gBAAgB,sBAAsB;AAAA,MAC/C,CAAC;AAAA,IACH;AAOA,qBAAiB,UAAU,SAAS,SAAS,mBAAmB;AAC9D,UAAI,kBAAkB,KAAK,iBAAiB,KAAK,SAASC,kBAAiB;AACzE,eAAOA,iBAAgB,sBAAsB;AAAA,MAC/C,CAAC;AAED,WAAK,UAAU,KAAK,iCAAiC,eAAe;AAEpE,sBAAgB,aAAa;AAAA,IAC/B;AAOA,qBAAiB,UAAU,sBAAsB,SAAS,QAAQ,SAAS;AACzE,UAAI,CAAC,QAAQ;AACX,eAAO,KAAK;AAAA,MACd;AAEA,UAAI,mBAAmB,KAAK,iBAAiB,OAAO,SAAS,iBAAiB;AAC5E,eAAO,gBAAgB,WAAW;AAAA,MACpC,CAAC;AAED,UAAI,WAAW,QAAQ,oBAAoB,MAAM;AAC/C,2BAAmB,iBAAiB,OAAO,SAAS,iBAAiB;AACnE,iBAAO,CAAC,gBAAgB;AAAA,QAC1B,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IACT;AAEA,qBAAiB,UAAU,qBAAqB,SAAS,mBAAmB;AAC1E,aAAO,KAAK,iBAAiB,KAAK,SAAS,iBAAiB;AAC1D,eAAO,gBAAgB,sBAAsB;AAAA,MAC/C,CAAC;AAAA,IACH;AAEA,qBAAiB,UAAU,CAAE,YAAY,oBAAqB;AAE9D,WAAO,UAAU;AAAA;AAAA;;;AChGjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA;AAEA,QAAI,SAAS;AAAb,QACI,aAAa;AADjB,QAEI,WAAW;AAEf,QAAI,KAAK,wBAAoC;AAE7C,QAAI,SAAS;AAAb,QACI,oBAAoB,OAAO;AAD/B,QAEI,uBAAuB,OAAO;AAFlC,QAGI,yBAAyB,OAAO;AAEpC,aAAS,gBAAgB,UAAU,wBAAwB,eAAe,iBAAiB;AACzF,UAAI,OAAO;AAEX,WAAK,YAAY;AACjB,WAAK,0BAA0B;AAC/B,WAAK,iBAAiB;AACtB,WAAK,mBAAmB;AAExB,WAAK,MAAM;AAEX,eAAS,GAAG,sBAAsB,SAAS,SAAS;AAClD,YAAI,CAAC,GAAG,QAAQ,SAAS,iBAAiB,GAAG;AAC3C;AAAA,QACF;AAEA,mBAAW,KAAK,YAAY,EAAE,OAAO,UAAU;AAAA,MACjD,CAAC;AAED,eAAS,GAAG,mBAAmB,SAAS,SAAS;AAC/C,YAAI,uBAAuB,QAAQ;AAEnC,YAAI,CAAC,sBAAsB;AACzB,eAAK,gBAAgB;AAAA,QACvB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,oBAAgB,UAAU,QAAQ,WAAW;AAC3C,UAAI,OAAO;AAEX,WAAK,eAAe,OAAO,0FAA0F;AAErH,eAAS,KAAK,KAAK,cAAc,SAAS,WAAW;AACnD,aAAK,gBAAgB;AAErB,aAAK,eAAe,iBAAiB,oBAAoB,MAAM;AAAA,MACjE,CAAC;AAED,WAAK,wBAAwB,SAAS,KAAK,cAAc,CAAC;AAAA,IAC5D;AAEA,oBAAgB,UAAU,kBAAkB,WAAW;AACrD,iBAAW,KAAK,YAAY,EAAE,IAAI,UAAU;AAE5C,WAAK,iBAAiB,QAAQ,SAAS,SAAS;AAC9C,YAAI,QAAQ,eAAe,QAAW;AACpC,iBAAO,QAAQ;AAAA,QACjB;AAAA,MACF,CAAC;AAED,WAAK,UAAU,KAAK,sBAAsB;AAAA,IAC5C;AAEA,oBAAgB,UAAU,CAAE,YAAY,0BAA0B,iBAAiB,iBAAkB;AAErG,WAAO,UAAU;AAAA;AAAA;;;ACpEjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA;AAEA,QAAI,SAAS;AAAb,QACI,aAAa;AADjB,QAEI,WAAW;AAFf,QAGI,WAAW;AAEf,QAAI,SAAS;AAAb,QACI,oBAAoB,OAAO;AAE/B,aAAS,kBAAkB,QAAQ,WAAW,UAAU;AACtD,UAAI,OAAO;AAEX,WAAK,UAAU;AACf,WAAK,aAAa;AAClB,WAAK,YAAY;AAEjB,WAAK,MAAM;AAEX,eAAS,GAAG,mBAAmB,SAAS,SAAS;AAC/C,YAAI,uBAAuB,QAAQ;AAEnC,YAAI,CAAC,sBAAsB;AACzB,qBAAW,KAAK,SAAS,EAAE,IAAI,QAAQ;AAAA,QACzC,OAAO;AACL,qBAAW,KAAK,SAAS,EAAE,OAAO,QAAQ;AAAA,QAC5C;AAAA,MACF,CAAC;AAAA,IACH;AAEA,sBAAkB,UAAU,QAAQ,WAAW;AAC7C,UAAI,OAAO;AAEX,WAAK,YAAY;AAAA,QACf;AAAA,MAQF;AAEA,UAAI,SAAS,SAAS,sBAAsB,KAAK,SAAS,GACtD,SAAS,SAAS,sBAAsB,KAAK,SAAS,GACtD,SAAS,SAAS,sBAAsB,KAAK,SAAS;AAE1D,eAAS,KAAK,QAAQ,SAAS,WAAW;AACxC,aAAK,UAAU,MAAM;AAErB,aAAK,WAAW,kBAAkB,GAAG;AAAA,MACvC,CAAC;AAED,eAAS,KAAK,QAAQ,SAAS,WAAW;AACxC,aAAK,UAAU,MAAM;AAErB,aAAK,WAAW,kBAAkB,CAAC;AAAA,MACrC,CAAC;AAED,eAAS,KAAK,QAAQ,SAAS,WAAW;AACxC,aAAK,UAAU,MAAM;AAErB,aAAK,WAAW,kBAAkB,GAAG;AAAA,MACvC,CAAC;AAED,WAAK,QAAQ,aAAa,EAAE,YAAY,KAAK,SAAS;AAAA,IACxD;AAEA,sBAAkB,UAAU,YAAY,SAAS,SAAS;AACxD,eAAS,IAAI,2BAA2B,KAAK,SAAS,EAAE,QAAQ,SAAS,QAAQ;AAC/E,mBAAW,MAAM,EAAE,OAAO,QAAQ;AAAA,MACpC,CAAC;AAED,iBAAW,OAAO,EAAE,IAAI,QAAQ;AAAA,IAClC;AAEA,sBAAkB,UAAU,CAAE,UAAU,aAAa,UAAW;AAEhE,WAAO,UAAU;AAAA;AAAA;;;AC/EjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA,WAAO,UAAU,SAAS,IAAI;AAE5B,UAAI;AAEJ,aAAO,GAAG,WAAW,QAAQ;AAC3B,YAAI,GAAG,WAAW,CAAC;AACnB,WAAG,YAAY,CAAC;AAAA,MAClB;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACVA;AAAA;AAAA;AAEA,QAAI,SAAS;AAAb,QACI,aAAa;AADjB,QAEI,WAAW;AAFf,QAGI,WAAW;AAHf,QAII,WAAW;AAEf,QAAI,SAAS;AAAb,QACI,oBAAoB,OAAO;AAD/B,QAEI,iCAAiC,OAAO;AAF5C,QAGI,kCAAkC,OAAO;AAH7C,QAII,+BAA+B,OAAO;AAJ1C,QAKI,gCAAgC,OAAO;AAL3C,QAMI,yBAAyB,OAAO;AAEpC,QAAI,aAAa,iBAAiB,SAAS,eAAe,EAAE,iBAAiB,mCAAmC;AAAhH,QACI,eAAe,iBAAiB,SAAS,eAAe,EAAE,iBAAiB,kCAAkC;AAEjH,aAAS,OAAO,OAAO;AACrB,aAAO,UAAU;AAAA,IACnB;AAEA,aAAS,oBACL,UACA,QACA,yBACA,kBACA,iBACA,iBACF;AACA,UAAI,OAAO;AAEX,WAAK,YAAY;AACjB,WAAK,UAAU;AACf,WAAK,2BAA2B;AAChC,WAAK,oBAAoB;AACzB,WAAK,mBAAmB;AACxB,WAAK,mBAAmB;AAExB,WAAK,qBAAqB;AAE1B,WAAK,MAAM;AAEX,eAAS,GAAG,mBAAmB,SAAS,SAAS;AAC/C,YAAI,uBAAuB,QAAQ;AAEnC,YAAI,CAAC,sBAAsB;AACzB,qBAAW,KAAK,SAAS,EAAE,IAAI,QAAQ;AACvC,mBAAS,KAAK,SAAS;AAEvB,cAAI,CAAC,OAAO,KAAK,kBAAkB,GAAG;AACpC,iBAAK,2BAA2B,KAAK,mBAAmB,OAAO;AAE/D,iBAAK,qBAAqB;AAAA,UAC5B;AAAA,QACF,OAAO;AACL,qBAAW,KAAK,SAAS,EAAE,OAAO,QAAQ;AAAA,QAC5C;AAAA,MACF,CAAC;AAED,eAAS,GAAG,gCAAgC,SAAS,SAAS;AAC5D,aAAK,YAAY,OAAO;AAAA,MAC1B,CAAC;AAED,eAAS,GAAG,iCAAiC,SAAS,SAAS;AAC7D,aAAK,eAAe,OAAO;AAAA,MAC7B,CAAC;AAED,eAAS,GAAG,8BAA8B,SAAS,SAAS;AAC1D,aAAK,iBAAiB,QAAQ,iBAAiB;AAAA,MACjD,CAAC;AAED,eAAS,GAAG,+BAA+B,SAAS,SAAS;AAC3D,aAAK,kBAAkB,QAAQ,iBAAiB;AAAA,MAClD,CAAC;AAED,eAAS,GAAG,wBAAwB,WAAW;AAC7C,aAAK,mBAAmB;AAAA,MAC1B,CAAC;AAAA,IACH;AAEA,wBAAoB,UAAU,QAAQ,WAAW;AAC/C,WAAK,YAAY,OAAO,8CAA8C;AAEtE,WAAK,QAAQ,aAAa,EAAE,YAAY,KAAK,SAAS;AAAA,IACxD;AAEA,wBAAoB,UAAU,cAAc,SAAS,SAAS;AAC5D,UAAI,OAAO;AAEX,UAAI,oBAAoB,QAAQ,mBAC5B,SAAS,QAAQ;AAErB,UAAI,UAAU;AAAA,QACZ,uBAAuB,oBAAoB,6DAA6D,oBAAoB,OAC5H,oBACA;AAAA,MACF;AAEA,eAAS,KAAK,SAAS,SAAS,WAAW;AACzC,YAAI,6BAA6B,KAAK,kBAAkB,oBAAoB,MAAM;AAElF,mCAA2B,QAAQ,SAAS,iBAAiB;AAC3D,eAAK,yBAAyB,oBAAoB,gBAAgB,iBAAiB;AAAA,QACrF,CAAC;AAED,aAAK,yBAAyB,oBAAoB,mBAAmB,MAAM;AAAA,MAC7E,CAAC;AAED,eAAS,KAAK,SAAS,cAAc,WAAW;AAC9C,aAAK,qBAAqB;AAAA,UACxB,SAAS;AAAA,UACT,QAAQ,OAAO,eAAe,GAAG,IAAI,QAAQ;AAAA,UAC7C,MAAM,OAAO,eAAe,GAAG,IAAI,MAAM;AAAA,QAC3C;AAEA,aAAK,sBAAsB,MAAM;AAAA,MACnC,CAAC;AAED,eAAS,KAAK,SAAS,cAAc,WAAW;AAC9C,aAAK,2BAA2B,MAAM;AAEtC,aAAK,qBAAqB;AAAA,MAC5B,CAAC;AAED,WAAK,UAAU,YAAY,OAAO;AAAA,IACpC;AAEA,wBAAoB,UAAU,iBAAiB,SAAS,SAAS;AAC/D,UAAI,oBAAoB,QAAQ;AAEhC,UAAI,UAAU,SAAS,eAAe,mBAAmB,KAAK,SAAS;AAEvE,UAAI,SAAS;AACX,gBAAQ,OAAO;AAAA,MACjB;AAAA,IACF;AAEA,wBAAoB,UAAU,qBAAqB,WAAW;AAC5D,WAAK,UAAU,YAAY;AAAA,IAC7B;AAEA,wBAAoB,UAAU,mBAAmB,SAAS,mBAAmB;AAC3E,UAAI,UAAU,SAAS,eAAe,mBAAmB,KAAK,SAAS;AAEvE,UAAI,SAAS;AACX,mBAAW,OAAO,EAAE,IAAI,QAAQ;AAAA,MAClC;AAAA,IACF;AAEA,wBAAoB,UAAU,oBAAoB,SAAS,mBAAmB;AAC5E,UAAI,UAAU,SAAS,eAAe,mBAAmB,KAAK,SAAS;AAEvE,UAAI,SAAS;AACX,mBAAW,OAAO,EAAE,OAAO,QAAQ;AAAA,MACrC;AAAA,IACF;AAEA,wBAAoB,UAAU,wBAAwB,SAAS,SAAS;AACtE,WAAK,SAAS,SAAS,cAAc,UAAU;AAE/C,UAAI,CAAC,QAAQ,QAAQ;AACnB,mBAAW,KAAK,QAAQ,aAAa,CAAC,EAAE,IAAI,WAAW;AAAA,MACzD;AAAA,IACF;AAEA,wBAAoB,UAAU,6BAA6B,SAAS,SAAS;AAC3E,UAAI,OAAO,KAAK,kBAAkB,GAAG;AACnC;AAAA,MACF;AAEA,WAAK,SAAS,SAAS,KAAK,mBAAmB,QAAQ,KAAK,mBAAmB,IAAI;AAEnF,UAAI,CAAC,QAAQ,QAAQ;AACnB,mBAAW,KAAK,QAAQ,aAAa,CAAC,EAAE,OAAO,WAAW;AAAA,MAC5D;AAAA,IACF;AAEA,wBAAoB,UAAU,WAAW,SAAS,SAAS,QAAQ,MAAM;AACvE,UAAI,iBAAiB,QAAQ;AAE7B,qBAAe,GAAG,IAAI,UAAU,MAAM;AACtC,qBAAe,GAAG,IAAI,QAAQ,IAAI;AAElC,UAAI,MAAM,KAAK,iBAAiB,YAAY,OAAO;AAEnD,WAAK,iBAAiB,OAAO,cAAc,SAAS,GAAG;AAAA,IACzD;AAEA,wBAAoB,UAAU;AAAA,MAC5B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACvMjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA;AAEA,QAAI,gBAAgB;AAApB,QACI,oBAAoB,cAAc;AADtC,QAEI,KAAK,cAAc;AAFvB,QAGI,aAAa,cAAc;AAH/B,QAII,eAAe,cAAc;AAEjC,QAAI,SAAS;AAAb,QACI,sBAAsB,OAAO;AAEjC,QAAI,oBAAoB;AAExB,aAAS,gBACL,UACA,WACA,iBACA,KACA,sBACA,QACA,kBACF;AAGA,WAAK,aAAa;AAClB,WAAK,mBAAmB;AACxB,WAAK,OAAO;AACZ,WAAK,wBAAwB;AAC7B,WAAK,UAAU;AACf,WAAK,oBAAoB;AAEzB,eAAS,GAAG,qBAAqB,mBAAmB,WAAW;AAAA,MAE/D,CAAC;AAAA,IACH;AAGA,oBAAgB,UAAU,aAAa,WAAW;AAChD,UAAI,OAAO;AAEX,UAAI,YAAY,CAAC;AAEjB,WAAK,iBAAiB,QAAQ,SAAS,SAAS;AAC9C,YAAI,QAAQ,YAAY;AACtB,oBAAU,KAAK,OAAO;AAAA,QACxB;AAAA,MACF,CAAC;AAED,UAAI,iBAAiB,CAAC;AACtB,UAAI,eAAe,CAAC;AAEpB,gBAAU,QAAQ,SAAS,SAAS;AAClC,YAAI,wBAAwB,QAAQ,SAAS,OAAO,SAAS,UAAU;AACrE,iBAAO,GAAG,UAAU,mBAAmB;AAAA,QACzC,CAAC;AAGD,YAAI,CAAC,sBAAsB,QAAQ;AACjC,yBAAe,KAAK,OAAO;AAAA,QAC7B;AAGA,YAAI,GAAG,SAAS,sBAAsB,GAAG;AACvC,cAAI,wBAAwB,QAAQ,SAAS,OAAO,SAAS,UAAU;AACrE,mBAAO,GAAG,UAAU,mBAAmB;AAAA,UACzC,CAAC;AAED,cAAI,sBAAsB,SAAS,QAAQ,YAAY;AACrD,2BAAe,KAAK,OAAO;AAAA,UAC7B;AAAA,QACF;AAEA,YAAI,UAAU,CAAC;AAGf,iBAAS,oBAAoBC,UAAS;AACpC,UAAAA,SAAQ,SAAS,QAAQ,SAAS,UAAU;AAC1C,gBAAI,QAAQ,QAAQ,SAAS,MAAM,MAAM,IAAI;AAC3C;AAAA,YACF;AAEA,oBAAQ,KAAK,SAAS,MAAM;AAE5B,gBAAI,cAAc,aAAa,kBAAkB,SAAS,MAAM,GAAG,+BAA+B;AAElG,gBAAI,aAAa;AACf,2BAAa,KAAKA,QAAO;AAAA,YAC3B;AAEA,gCAAoB,SAAS,MAAM;AAAA,UACrC,CAAC;AAAA,QACH;AAEA,4BAAoB,OAAO;AAAA,MAC7B,CAAC;AAED,UAAI,UAAU,UACP,CAAC,aAAa,UACd,eAAe,UACf,CAAC,KAAK,WAAW,WAAW,QAAQ;AACzC,aAAK,KAAK,IAAI,YAAY,WAAW,yBAAyB;AAE9D,uBAAe,QAAQ,SAAS,SAAS;AACvC,eAAK,sBAAsB,uBAAuB,SAAS;AAAA,YACzD,MAAM;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,UACR,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAMA,oBAAgB,UAAU,aAAa,SAAS,SAAS,mBAAmB;AAC1E,UAAI,kBAAkB,KAAK,kBAAkB,mBAAmB,iBAAiB;AACjF,UAAI,SAAS,gBAAgB;AAE7B,UAAI,YAAY;AAEhB,UAAI,CAAC,QAAQ;AACX,iBAAS,KAAK,QAAQ,eAAe;AAAA,MACvC;AAEA,aAAO,SAAS,QAAQ,SAASA,UAAS;AACxC,YAAIA,SAAQ,cACRA,SAAQ,WAAW,iBAAiB,KACpCA,SAAQ,WAAW,iBAAiB,EAAE,QACxC;AACA,sBAAY;AAAA,QACd;AAAA,MACF,CAAC;AAED,UAAI,gBAAgB;AAEpB,WAAK,WAAW,WAAW,QAAQ,SAAS,WAAW;AACrD,YAAI,WAAW,QAAQ,UAAU,OAAO,KACpC,UAAU,sBAAsB,mBAAmB;AACrD,0BAAgB;AAAA,QAClB;AAAA,MACF,CAAC;AAED,UAAI,CAAC,aAAa,CAAC,eAAe;AAChC,YAAI,GAAG,QAAQ,iBAAiB,GAAG;AACjC,eAAK,KAAK,IAAI,gBAAgB,oBAAoB,aAAa,QAAQ,iBAAiB;AAAA,QAC1F,OAAO;AACL,eAAK,KAAK,IAAI,aAAa,oBAAoB,aAAa,WAAW,iBAAiB;AAExF,eAAK,sBAAsB,uBAAuB,SAAS;AAAA,YACzD,MAAM;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAEA,eAAO;AAAA,MACT;AAAA,IACF;AAEA,oBAAgB,UAAU;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC3KjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA;AAEA,QAAI,SAAS;AAAb,QACI,aAAa;AADjB,QAEI,WAAW;AAFf,QAGI,WAAW;AAEf,QAAI,SAAS;AAAb,QACI,oBAAoB,OAAO;AAE/B,aAAS,WAAW,UAAU,QAAQ,WAAW,YAAY;AAC3D,UAAI,OAAO;AAEX,WAAK,YAAY;AACjB,WAAK,UAAU;AACf,WAAK,aAAa;AAClB,WAAK,cAAc;AAEnB,WAAK,uBAAuB;AAE5B,eAAS,GAAG,eAAe,WAAW;AACpC,aAAK,eAAe,KAAK,QAAQ,aAAa,EAAE;AAChD,aAAK,UAAU,SAAS,gBAAgB,KAAK,QAAQ,aAAa,CAAC;AAEnE,aAAK,MAAM;AAAA,MACb,CAAC;AAAA,IACH;AAEA,eAAW,UAAU,QAAQ,WAAW;AACtC,WAAK,YAAY,OAAO;AAAA;AAAA;AAAA;AAAA,GAIvB;AAED,eAAS,KAAK,KAAK,WAAW,SAAS,KAAK,WAAW,KAAK,IAAI,CAAC;AAEjE,WAAK,QAAQ,aAAa,EAAE,YAAY,KAAK,SAAS;AAAA,IACxD;AAEA,eAAW,UAAU,aAAa,WAAW;AAC3C,UAAI,KAAK,sBAAsB;AAC7B,aAAK,UAAU,YAAY;AAE3B,mBAAW,KAAK,YAAY,EAAE,OAAO,YAAY;AACjD,mBAAW,KAAK,OAAO,EAAE,OAAO,QAAQ;AAExC,aAAK,UAAU,KAAK,mBAAmB;AAAA,UACrC,sBAAsB;AAAA,QACxB,CAAC;AAED,YAAI,WAAW,KAAK,WAAW,IAAI;AAEnC,YAAI,SAAS,WAAW,GAAG;AACzB,eAAK,YAAY,KAAK,SAAS,CAAC,CAAC;AAAA,QACnC;AAAA,MACF,OAAO;AACL,aAAK,UAAU,YAAY;AAE3B,mBAAW,KAAK,YAAY,EAAE,IAAI,YAAY;AAC9C,mBAAW,KAAK,OAAO,EAAE,IAAI,QAAQ;AAErC,aAAK,UAAU,KAAK,mBAAmB;AAAA,UACrC,sBAAsB;AAAA,QACxB,CAAC;AAAA,MACH;AAEA,WAAK,uBAAuB,CAAC,KAAK;AAAA,IACpC;AAEA,eAAW,UAAU,CAAE,YAAY,UAAU,aAAa,YAAa;AAEvE,WAAO,UAAU;AAAA;AAAA;;;ACxEjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA;AAEA,QAAI,SAAS;AAEb,QAAI,gBAAgB;AAApB,QACI,aAAa,cAAc;AAE/B,QAAI,SAAS;AAAb,QACI,oBAAoB,OAAO;AAD/B,QAEI,uBAAuB,OAAO;AAFlC,QAGI,sBAAsB,OAAO;AAHjC,QAII,yBAAyB,OAAO;AAJpC,QAKI,kBAAkB,OAAO;AAL7B,QAMI,+BAA+B,OAAO;AAE1C,QAAI,gBAAgB;AAApB,QACI,cAAc;AAElB,QAAI,eAAe;AAEnB,aAAS,WAAW,UAAU,UAAU,iBAAiB,QAAQ,kBAAkB;AACjF,UAAI,OAAO;AAEX,WAAK,YAAY;AACjB,WAAK,mBAAmB;AACxB,WAAK,UAAU;AACf,WAAK,oBAAoB;AAEzB,WAAK,aAAa,CAAC;AAEnB,eAAS,GAAG,mBAAmB,SAAS,SAAS;AAC/C,YAAI,uBAAuB,QAAQ;AAEnC,YAAI,CAAC,sBAAsB;AACzB,eAAK,kBAAkB;AAAA,QACzB;AAAA,MACF,CAAC;AAED,eAAS,GAAG,wBAAwB,WAAW;AAC7C,aAAK,kBAAkB;AAAA,MACzB,CAAC;AAED,eAAS,GAAG,iBAAiB,SAAS,SAAS;AAC7C,YAAI,UAAU,QAAQ,SAClB,SAAS,QAAQ;AAErB,aAAK,kBAAkB,MAAM;AAAA,MAC/B,CAAC;AAED,eAAS,GAAG,CAAE,sBAAsB,mBAAoB,GAAG,cAAc,SAAS,SAAS;AACzF,YAAI,UAAU,QAAQ,SAClB,SAAS,QAAQ;AAErB,aAAK,kBAAkB,MAAM;AAC7B,aAAK,eAAe,MAAM;AAAA,MAC5B,CAAC;AAED,eAAS,GAAG,8BAA8B,SAAS,SAAS;AAC1D,YAAI,oBAAoB,QAAQ;AAEhC,YAAI,kBAAkB,iBAAiB,mBAAmB,iBAAiB,GACvE,SAAS,gBAAgB;AAE7B,aAAK,kBAAkB,MAAM;AAC7B,aAAK,eAAe,MAAM;AAAA,MAC5B,CAAC;AAAA,IACH;AAEA,eAAW,UAAU,iBAAiB,SAAS,QAAQ;AACrD,UAAI,OAAO;AAEX,UAAI,CAAC,QAAQ;AACX,iBAAS,KAAK,QAAQ,eAAe;AAAA,MACvC;AAEA,UAAI,uBAAuB,OAAO;AAGlC,UAAI,CAAC,sBAAsB;AACzB,YAAI,6BAA6B,KAAK,kBAAkB,oBAAoB,MAAM;AAGlF,YAAI,CAAC,2BAA2B,QAAQ;AACtC;AAAA,QACF;AAEA,+BAAuB,2BAA2B,CAAC,EAAE;AAAA,MACvD;AAEA,WAAK,iBAAiB,QAAQ,SAAS,SAAS;AAC9C,YAAI,WAAW,QAAQ,OAAO,GAAG;AAC/B,eAAK,cAAc,SAAS,oBAAoB;AAAA,QAClD;AAAA,MACF,CAAC;AAAA,IACH;AAEA,eAAW,UAAU,gBAAgB,SAAS,SAAS,sBAAsB;AAC3E,UAAI,aAAa,QAAQ,cAAc,QAAQ,WAAW,oBAAoB;AAE9E,UAAI,CAAC,YAAY;AACf;AAAA,MACF;AAEA,UAAI,OAAO,KAAK,iBAAiB,UAAU;AAE3C,UAAI,WAAW,EAAE,QAAQ,eAAe,MAAM,YAAY;AAE1D,UAAI,YAAY,KAAK,UAAU,IAAI,SAAS,eAAe;AAAA,QACzD;AAAA,QACA;AAAA,QACA,MAAM;AAAA,UACJ,SAAS;AAAA,QACX;AAAA,MACF,CAAC;AAED,WAAK,WAAW,QAAQ,EAAE,IAAI;AAAA,IAChC;AAEA,eAAW,UAAU,mBAAmB,SAAS,YAAY;AAC3D,aAAO,OAAO,sCAAsC,aAAa,QAAQ;AAAA,IAC3E;AAEA,eAAW,UAAU,oBAAoB,SAAS,QAAQ;AACxD,UAAI,OAAO;AAEX,UAAI,CAAC,QAAQ;AACX,iBAAS,KAAK,QAAQ,eAAe;AAAA,MACvC;AAEA,WAAK,iBAAiB,QAAQ,SAAS,SAAS;AAC9C,YAAI,WAAW,QAAQ,OAAO,GAAG;AAC/B,eAAK,iBAAiB,OAAO;AAAA,QAC/B;AAAA,MACF,CAAC;AAAA,IACH;AAEA,eAAW,UAAU,mBAAmB,SAAS,SAAS;AACxD,UAAI,YAAY,KAAK,WAAW,QAAQ,EAAE;AAE1C,UAAI,CAAC,WAAW;AACd;AAAA,MACF;AAEA,WAAK,UAAU,OAAO,SAAS;AAE/B,aAAO,KAAK,WAAW,QAAQ,EAAE;AAAA,IACnC;AAEA,eAAW,UAAU,CAAE,YAAY,YAAY,mBAAmB,UAAU,kBAAmB;AAE/F,WAAO,UAAU;AAAA;AAAA;;;ACtJjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA;AAEA,QAAI,gBAAgB;AAApB,QACI,oBAAoB,cAAc;AADtC,QAEI,KAAK,cAAc;AAFvB,QAGI,aAAa,cAAc;AAH/B,QAII,iBAAiB,cAAc;AAJnC,QAKI,eAAe,cAAc;AAEjC,QAAI,SAAS;AAAb,QACI,uBAAuB,OAAO;AADlC,QAEI,kBAAkB,OAAO;AAF7B,QAGI,wBAAwB,OAAO;AAEnC,aAAS,gBAAgB,WAAW,UAAU,KAAK,iBAAiB,iBAAiB,kBAAkB;AACrG,WAAK,aAAa;AAClB,WAAK,YAAY;AACjB,WAAK,OAAO;AACZ,WAAK,mBAAmB;AACxB,WAAK,mBAAmB;AACxB,WAAK,oBAAoB;AAAA,IAC3B;AAEA,oBAAgB,UAAU,UAAU,SAAS,SAAS;AACpD,UAAI,UAAU,QAAQ,SAClB,oBAAoB,QAAQ;AAEhC,UAAI,cAAc,aAAa,kBAAkB,OAAO,GAAG,+BAA+B,GACtF,oBAAoB,GAAG,QAAQ,QAAQ,iBAAiB;AAE5D,UAAI,aAAa;AACf,aAAK,UAAU,KAAK,iBAAiB,OAAO;AAE5C,aAAK,iBAAiB,QAAQ,SAAS,GAAG;AACxC,cAAI,WAAW,QAAQ,QAAQ,CAAC,KAC5B,EAAE,cACF,EAAE,WAAW,iBAAiB,GAAG;AACnC,mBAAO,EAAE,WAAW,iBAAiB;AAAA,UACvC;AAAA,QACF,CAAC;AAGD,aAAK,kBAAkB,OAAO,iBAAiB;AAAA,MACjD;AAEA,UAAI,aAAa,KAAK,iBAAiB,WAAW,SAAS,iBAAiB;AAE5E,UAAI,YAAY;AAGd,aAAK,kBAAkB,OAAO,iBAAiB;AAAA,MACjD;AAEA,WAAK,cAAc,gBAAgB,mBAAmB;AACpD,YAAI,kBAAkB,KAAK,kBAAkB,mBAAmB,iBAAiB;AAGjF,aAAK,UAAU,KAAK,sBAAsB;AAAA,UACxC,SAAS,QAAQ;AAAA,UACjB,mBAAmB,gBAAgB;AAAA,QACrC,CAAC;AAAA,MACH;AAEA,WAAK,UAAU,KAAK,uBAAuB;AAAA,QACzC,UAAU,eAAe,KAAK,iBAAiB,OAAO,GAAG,QAAQ,MAAM;AAAA,MACzE,CAAC;AAAA,IACH;AAKA,oBAAgB,UAAU,WAAW,SAAS,SAAS;AAAA,IAAC;AAExD,oBAAgB,UAAU,CAAE,aAAa,YAAY,OAAO,mBAAmB,mBAAmB,kBAAmB;AAErH,WAAO,UAAU;AAAA;AAAA;;;AC3EjB;AAAA;AAAA;AAEA,QAAI,KAAK,wBAAuC;AAEhD,QAAI,SAAS;AAAb,QACI,wBAAwB,OAAO;AAEnC,aAAS,wBAAwB,UAAU,WAAW;AACpD,WAAK,YAAY;AACjB,WAAK,aAAa;AAAA,IACpB;AAEA,4BAAwB,UAAU,UAAU,SAAS,SAAS;AAC5D,UAAI,UAAU,QAAQ,SAClB,oBAAoB,QAAQ;AAEhC,UAAI,CAAC,QAAQ,YAAY;AACvB,gBAAQ,aAAa,CAAC;AAAA,MACxB;AAEA,UAAI,CAAC,QAAQ,WAAW,iBAAiB,GAAG;AAC1C,gBAAQ,WAAW,iBAAiB,IAAI;AAAA,MAC1C;AAEA,cAAQ,WAAW,iBAAiB;AAEpC,UAAI,WAAW,QAAQ,UACnBC,UAAS,CAAC;AAEd,eAAS,QAAQ,SAASC,WAAU;AAClC,YAAI,SAASA,UAAS;AAEtB,YAAI,GAAG,QAAQ,6BAA6B,GAAG;AAC7C,UAAAD,QAAO,KAAK,MAAM;AAAA,QACpB;AAAA,MACF,CAAC;AAED,WAAK,UAAU,KAAK,uBAAuB;AAAA,QACzC,UAAUA;AAAA,MACZ,CAAC;AAAA,IACH;AAEA,4BAAwB,UAAU,WAAW,WAAW;AAAA,IAAC;AAEzD,4BAAwB,UAAU,CAAE,YAAY,WAAY;AAE5D,WAAO,UAAU;AAAA;AAAA;;;AC9CjB,IAAAE,mCAAA;AAAA;AAAA;AAEA,QAAI,SAAS;AAAb,QACI,sBAAsB,OAAO;AADjC,QAEI,uBAAuB,OAAO;AAElC,aAAS,wBAAwB,UAAU,WAAW,iBAAiB;AACrE,WAAK,YAAY;AACjB,WAAK,aAAa;AAClB,WAAK,mBAAmB;AAAA,IAC1B;AAEA,4BAAwB,UAAU,UAAU,SAAS,SAAS;AAC5D,UAAI,UAAU,QAAQ;AAEtB,UAAI,CAAC,QAAQ,cAAc;AACzB,cAAM,IAAI,MAAM,6CAA6C,QAAQ,EAAE;AAAA,MACzE;AAEA,WAAK,UAAU,KAAK,sBAAsB,OAAO;AAAA,IACnD;AAEA,4BAAwB,UAAU,WAAW,SAAS,SAAS;AAC7D,UAAI,UAAU,QAAQ,SAClB,oBAAoB,QAAQ;AAEhC,UAAI,CAAC,QAAQ,cAAc;AACzB,cAAM,IAAI,MAAM,6CAA6C,QAAQ,EAAE;AAAA,MACzE;AAEA,UAAI,OAAO;AAIX,UAAI,eAAe,KAAK,iBAAiB,IAAI,QAAQ,aAAa,EAAE;AAEpE,WAAK,WAAW,gBAAgB,cAAc,mBAAmB,WAAW;AAC1E,aAAK,UAAU,KAAK,qBAAqB;AAAA,UACvC,SAAS,aAAa;AAAA,UACtB;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,4BAAwB,UAAU,CAAE,YAAY,aAAa,iBAAkB;AAE/E,WAAO,UAAU;AAAA;AAAA;;;AC9CjB,IAAAC,yCAAA;AAAA;AAAA;AAEA,QAAI,gBAAgB;AAApB,QACI,KAAK,cAAc;AAEvB,QAAI,SAAS;AAAb,QACI,sBAAsB,OAAO;AADjC,QAEI,uBAAuB,OAAO;AAFlC,QAGI,wBAAwB,OAAO;AAEnC,aAAS,8BAA8B,WAAW,UAAU,iBAAiB;AAC3E,WAAK,aAAa;AAClB,WAAK,YAAY;AACjB,WAAK,mBAAmB;AAAA,IAC1B;AAEA,kCAA8B,UAAU,UAAU,SAAS,SAAS;AAClE,UAAI,UAAU,QAAQ,SAClB,oBAAoB,QAAQ;AAEhC,UAAI,CAAC,QAAQ,YAAY;AACvB,gBAAQ,aAAa,CAAC;AAAA,MACxB;AAEA,UAAI,CAAC,QAAQ,WAAW,iBAAiB,GAAG;AAC1C,gBAAQ,WAAW,iBAAiB,IAAI;AAAA,MAC1C;AAEA,cAAQ,WAAW,iBAAiB;AAEpC,WAAK,UAAU,KAAK,sBAAsB;AAAA,QACxC;AAAA,MACF,CAAC;AAAA,IACH;AAEA,kCAA8B,UAAU,WAAW,SAAS,SAAS;AACnE,UAAI,OAAO;AAEX,UAAI,UAAU,QAAQ,SAClB,oBAAoB,QAAQ;AAEhC,UAAI,wBAAwB,QAAQ,SAAS,OAAO,SAAS,UAAU;AACrE,eAAO,GAAG,UAAU,mBAAmB;AAAA,MACzC,CAAC;AAED,4BAAsB,QAAQ,SAAS,YAAY;AACjD,aAAK,WAAW,gBAAgB,YAAY,mBAAmB,WAAW;AACxE,eAAK,UAAU,KAAK,qBAAqB;AAAA,YACvC,SAAS,WAAW;AAAA,YACpB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAED,UAAI,SAAS,QAAQ;AAErB,UAAIC,UAAS,KAAK,iBAAiB,OAAO,SAASC,UAAS;AAC1D,eAAO,GAAGA,UAAS,6BAA6B,KACzCA,SAAQ,WAAW;AAAA,MAC5B,CAAC;AAED,WAAK,UAAU,KAAK,uBAAuB;AAAA,QACzC,UAAUD;AAAA,MACZ,CAAC;AAAA,IACH;AAEA,kCAA8B,UAAU,CAAE,aAAa,YAAY,iBAAkB;AAErF,WAAO,UAAU;AAAA;AAAA;;;ACpEjB;AAAA;AAAA;AAEA,QAAI,KAAK,wBAAuC;AAEhD,QAAI,SAAS;AAAb,QACI,sBAAsB,OAAO;AADjC,QAEI,uBAAuB,OAAO;AAElC,aAAS,8BAA8B,WAAW,UAAU;AAC1D,WAAK,aAAa;AAClB,WAAK,YAAY;AAAA,IACnB;AAEA,kCAA8B,UAAU,UAAU,SAAS,SAAS;AAClE,WAAK,UAAU,KAAK,sBAAsB;AAAA,QACxC;AAAA,MACF,CAAC;AAAA,IACH;AAEA,kCAA8B,UAAU,WAAW,SAAS,SAAS;AACnE,UAAI,OAAO;AAEX,UAAI,wBAAwB,QAAQ,SAAS,OAAO,SAAS,UAAU;AACrE,eAAO,GAAG,UAAU,mBAAmB;AAAA,MACzC,CAAC;AAED,4BAAsB,QAAQ,SAAS,YAAY;AACjD,aAAK,WAAW,gBAAgB,YAAY,WAAW;AACrD,eAAK,UAAU,KAAK,qBAAqB;AAAA,YACvC,SAAS,WAAW;AAAA,UACtB,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,kCAA8B,UAAU,CAAE,aAAa,UAAW;AAElE,WAAO,UAAU;AAAA;AAAA;;;ACrCjB;AAAA;AAAA;AAEA,QAAI,KAAK,wBAAuC;AAEhD,QAAI,SAAS;AAAb,QACI,sBAAsB,OAAO;AADjC,QAEI,uBAAuB,OAAO;AAElC,aAAS,uBAAuB,WAAW,UAAU;AACnD,WAAK,aAAa;AAClB,WAAK,YAAY;AAAA,IACnB;AAEA,2BAAuB,UAAU,UAAU,SAAS,SAAS;AAC3D,UAAI,UAAU,QAAQ,SAClB,oBAAoB,QAAQ;AAEhC,UAAI,CAAC,QAAQ,YAAY;AACvB,gBAAQ,aAAa,CAAC;AAAA,MACxB;AAEA,UAAI,CAAC,QAAQ,WAAW,iBAAiB,GAAG;AAC1C,gBAAQ,WAAW,iBAAiB,IAAI;AAAA,MAC1C;AAEA,cAAQ,WAAW,iBAAiB;AAEpC,UAAI,WAAW,QAAQ;AAEvB,UAAI,SAAS,WAAW,QAAQ,WAAW,iBAAiB,GAAG;AAC7D,aAAK,UAAU,KAAK,sBAAsB,OAAO;AAEjD,gBAAQ,WAAW,iBAAiB,IAAI;AAAA,MAC1C;AAAA,IACF;AAEA,2BAAuB,UAAU,WAAW,SAAS,SAAS;AAC5D,UAAI,OAAO;AAEX,UAAI,UAAU,QAAQ,SAClB,oBAAoB,QAAQ;AAEhC,UAAI,wBAAwB,QAAQ,SAAS,OAAO,SAAS,UAAU;AACrE,eAAO,GAAG,UAAU,mBAAmB;AAAA,MACzC,CAAC;AAED,4BAAsB,QAAQ,SAAS,UAAU;AAC/C,aAAK,WAAW,gBAAgB,UAAU,mBAAmB,WAAW;AACtE,eAAK,UAAU,KAAK,qBAAqB;AAAA,YACvC,SAAS,SAAS;AAAA,YAClB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,2BAAuB,UAAU,CAAE,aAAa,UAAW;AAE3D,WAAO,UAAU;AAAA;AAAA;;;AC1DjB,IAAAE,6BAAA;AAAA;AAAA;AAEA,QAAI,KAAK,wBAAuC;AAEhD,QAAI,SAAS;AAAb,QACI,sBAAsB,OAAO;AADjC,QAEI,wBAAwB,OAAO;AAEnC,aAAS,kBAAkB,WAAW,UAAU,iBAAiB,kBAAkB;AACjF,WAAK,aAAa;AAClB,WAAK,YAAY;AACjB,WAAK,mBAAmB;AACxB,WAAK,oBAAoB;AAAA,IAC3B;AAMA,sBAAkB,UAAU,UAAU,WAAW;AAAA,IAAC;AAWlD,sBAAkB,UAAU,WAAW,SAAS,SAAS;AACvD,UAAI,OAAO;AAEX,UAAI,UAAU,QAAQ,SAClB,0BAA0B,QAAQ;AAEtC,UAAI,wBAAwB,QAAQ,SAAS,OAAO,SAAS,UAAU;AACrE,eAAO,GAAG,UAAU,mBAAmB;AAAA,MACzC,CAAC;AAGD,UAAI,SAAS,QAAQ,QACjB,oBAAoB,KAAK,kBAAkB,OAAO,QAAQ,uBAAuB;AAErF,4BAAsB,QAAQ,SAAS,YAAY;AACjD,aAAK,WAAW,gBAAgB,YAAY,mBAAmB,WAAW;AACxE,eAAK,UAAU,KAAK,qBAAqB;AAAA,YACvC,SAAS,WAAW;AAAA,YACpB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAED,UAAI,GAAG,QAAQ,QAAQ,iBAAiB,GAAG;AACzC;AAAA,MACF;AAEA,UAAI,cAAc,KAAK,iBAAiB,OAAO,SAASC,UAAS;AAC/D,eAAO,GAAGA,UAAS,iBAAiB;AAAA,MACtC,CAAC;AAED,WAAK,UAAU,KAAK,uBAAuB;AAAA,QACzC,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAEA,sBAAkB,UAAU,CAAE,aAAa,YAAY,mBAAmB,kBAAmB;AAE7F,WAAO,UAAU;AAAA;AAAA;;;ACpEjB;AAAA;AAAA;AAEA,QAAI,KAAK,wBAAuC;AAEhD,QAAI,SAAS;AAAb,QACI,sBAAsB,OAAO;AADjC,QAEI,uBAAuB,OAAO;AAFlC,QAGI,uBAAuB,OAAO;AAElC,aAAS,kBAAkB,WAAW,UAAU,KAAK;AACnD,WAAK,aAAa;AAClB,WAAK,YAAY;AACjB,WAAK,OAAO;AAAA,IACd;AAEA,sBAAkB,UAAU,UAAU,SAAS,SAAS;AACtD,UAAI,UAAU,QAAQ,SAClB,oBAAoB,QAAQ;AAEhC,UAAI,aAAa,QAAQ,SAAS,OAAO,SAAS,OAAO;AACvD,eAAO,GAAG,OAAO,iBAAiB;AAAA,MACpC,CAAC,EAAE,CAAC;AAEJ,UAAI,CAAC,YAAY;AACf,aAAK,KAAK,IAAI,uBAAuB,QAAQ,uBAAuB;AAGpE,aAAK,UAAU,KAAK,sBAAsB,OAAO;AAAA,MACnD,OAAO;AACL,aAAK,KAAK,IAAI,uBAAuB,QAAQ,YAAY;AAGzD,aAAK,UAAU,KAAK,sBAAsB;AAAA,UACxC,SAAS;AAAA,UACT,yBAAyB;AAAA,QAC3B,CAAC;AAAA,MACH;AAEA,WAAK,UAAU,KAAK,sBAAsB;AAAA,QACxC;AAAA,MACF,CAAC;AAAA,IACH;AAEA,sBAAkB,UAAU,WAAW,SAAS,SAAS;AACvD,UAAI,OAAO;AAEX,UAAI,UAAU,QAAQ,SAClB,oBAAoB,QAAQ;AAEhC,UAAI,wBAAwB,QAAQ,SAAS,OAAO,SAAS,UAAU;AACrE,eAAO,GAAG,UAAU,mBAAmB;AAAA,MACzC,CAAC;AAED,4BAAsB,QAAQ,SAAS,UAAU;AAC/C,aAAK,WAAW,gBAAgB,UAAU,mBAAmB,WAAW;AACtE,eAAK,UAAU,KAAK,qBAAqB;AAAA,YACvC,SAAS,SAAS;AAAA,YAClB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAED,WAAK,UAAU,KAAK,sBAAsB;AAAA,QACxC;AAAA,MACF,CAAC;AAAA,IACH;AAEA,sBAAkB,UAAU,CAAE,aAAa,YAAY,KAAM;AAE7D,WAAO,UAAU;AAAA;AAAA;;;ACrEjB,IAAAC,gCAAA;AAAA;AAAA;AAEA,QAAI,gBAAgB;AAApB,QACI,KAAK,cAAc;AAEvB,QAAI,SAAS;AAAb,QACI,sBAAsB,OAAO;AADjC,QAEI,uBAAuB,OAAO;AAElC,aAAS,qBAAqB,WAAW,UAAU,iBAAiB;AAClE,WAAK,aAAa;AAClB,WAAK,YAAY;AACjB,WAAK,mBAAmB;AAAA,IAC1B;AAEA,yBAAqB,UAAU,UAAU,SAAS,SAAS;AACzD,UAAI,UAAU,QAAQ,SAClB,oBAAoB,QAAQ;AAEhC,UAAI,CAAC,QAAQ,YAAY;AACvB,gBAAQ,aAAa,CAAC;AAAA,MACxB;AAEA,UAAI,CAAC,QAAQ,WAAW,iBAAiB,GAAG;AAC1C,gBAAQ,WAAW,iBAAiB,IAAI;AAAA,MAC1C;AAEA,cAAQ,WAAW,iBAAiB;AAEpC,WAAK,UAAU,KAAK,sBAAsB;AAAA,QACxC;AAAA,MACF,CAAC;AAAA,IACH;AAEA,yBAAqB,UAAU,WAAW,SAAS,SAAS;AAC1D,UAAI,OAAO;AAEX,UAAI,UAAU,QAAQ,SAClB,oBAAoB,QAAQ;AAEhC,UAAI,wBAAwB,QAAQ,SAAS,OAAO,SAAS,UAAU;AACrE,eAAO,GAAG,UAAU,mBAAmB;AAAA,MACzC,CAAC;AAED,4BAAsB,QAAQ,SAAS,YAAY;AACjD,aAAK,WAAW,gBAAgB,YAAY,mBAAmB,WAAW;AACxE,eAAK,UAAU,KAAK,qBAAqB;AAAA,YACvC,SAAS,WAAW;AAAA,YACpB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,yBAAqB,UAAU,CAAE,aAAa,YAAY,iBAAkB;AAE5E,WAAO,UAAU;AAAA;AAAA;;;ACxDjB;AAAA;AAAA;AAEA,QAAI,KAAK,wBAAuC;AAEhD,QAAI,SAAS;AAAb,QACI,sBAAsB,OAAO;AADjC,QAEI,uBAAuB,OAAO;AAElC,aAAS,YAAY,WAAW,UAAU;AACxC,WAAK,aAAa;AAClB,WAAK,YAAY;AAAA,IACnB;AAEA,gBAAY,UAAU,UAAU,SAAS,SAAS;AAGhD,WAAK,UAAU,KAAK,sBAAsB,OAAO;AAAA,IACnD;AAEA,gBAAY,UAAU,WAAW,SAAS,SAAS;AACjD,UAAI,OAAO;AAEX,UAAI,UAAU,QAAQ,SAClB,oBAAoB,QAAQ;AAEhC,UAAI,wBAAwB,QAAQ,SAAS,OAAO,SAAS,UAAU;AACrE,eAAO,GAAG,UAAU,mBAAmB;AAAA,MACzC,CAAC;AAED,4BAAsB,QAAQ,SAAS,UAAU;AAC/C,aAAK,WAAW,gBAAgB,UAAU,mBAAmB,WAAW;AACtE,eAAK,UAAU,KAAK,qBAAqB;AAAA,YACvC,SAAS,SAAS;AAAA,YAClB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,gBAAY,UAAU,CAAE,aAAa,UAAW;AAEhD,WAAO,UAAU;AAAA;AAAA;;;ACzCjB;AAAA;AAAA;AAEA,QAAI,kBAAkB;AAAtB,QACI,2BAA2B;AAD/B,QAEI,0BAA0B;AAF9B,QAGI,gCAAgC;AAHpC,QAII,gCAAgC;AAJpC,QAKI,yBAAyB;AAL7B,QAMI,oBAAoB;AANxB,QAOI,oBAAoB;AAPxB,QAQI,uBAAuB;AAR3B,QASI,cAAc;AAElB,QAAI,SAAS;AAAb,QACI,uBAAuB,OAAO;AADlC,QAEI,sBAAsB,OAAO;AAEjC,aAAS,wBAAwB,UAAU,WAAW,UAAU;AAC9D,UAAI,OAAO;AAEX,WAAK,YAAY;AAEjB,WAAK,WAAW,CAAC;AAEjB,WAAK,gBAAgB,iBAAiB,eAAe;AACrD,WAAK,gBAAgB,0BAA0B,wBAAwB;AACvE,WAAK,gBAAgB,yBAAyB,uBAAuB;AACrE,WAAK,gBAAgB,+BAA+B,6BAA6B;AACjF,WAAK,gBAAgB,+BAA+B,6BAA6B;AACjF,WAAK,gBAAgB,wBAAwB,sBAAsB;AACnE,WAAK,gBAAgB,mBAAmB,iBAAiB;AACzD,WAAK,gBAAgB,mBAAmB,iBAAiB;AACzD,WAAK,gBAAgB,sBAAsB,oBAAoB;AAC/D,WAAK,gBAAgB;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,GAAG,WAAW;AAGd,eAAS,GAAG,sBAAsB,SAAS,SAAS;AAClD,YAAI,UAAU,QAAQ;AAEtB,YAAI,CAAC,KAAK,SAAS,QAAQ,IAAI,GAAG;AAChC,gBAAM,IAAI,MAAM,yBAAyB,QAAQ,IAAI;AAAA,QACvD;AAEA,aAAK,SAAS,QAAQ,IAAI,EAAE,SAAS,OAAO;AAAA,MAC9C,CAAC;AAGD,eAAS,GAAG,qBAAqB,SAAS,SAAS;AACjD,YAAI,UAAU,QAAQ;AAEtB,YAAI,CAAC,KAAK,SAAS,QAAQ,IAAI,GAAG;AAChC,gBAAM,IAAI,MAAM,yBAAyB,QAAQ,IAAI;AAAA,QACvD;AAEA,aAAK,SAAS,QAAQ,IAAI,EAAE,QAAQ,OAAO;AAAA,MAC7C,CAAC;AAAA,IACH;AAEA,4BAAwB,UAAU,kBAAkB,SAAS,OAAO,YAAY;AAC9E,UAAI,OAAO;AAEX,UAAI,UAAU,KAAK,UAAU,YAAY,UAAU;AAEnD,UAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,gBAAQ,CAAE,KAAM;AAAA,MAClB;AAEA,YAAM,QAAQ,SAAS,MAAM;AAC3B,aAAK,SAAS,IAAI,IAAI;AAAA,MACxB,CAAC;AAAA,IACH;AAEA,4BAAwB,UAAU,CAAE,YAAY,aAAa,UAAW;AAExE,WAAO,UAAU;AAAA;AAAA;;;AClFjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA;AAEA,aAAS,cACL,UACA,YACA,iBACA,KACA,iBACA,eACF;AACA,oBAAc,SAAS;AAAA,QACrB,uBAAuB,WAAW;AAChC,qBAAW,WAAW;AAAA,QACxB;AAAA,MACF,CAAC;AAED,oBAAc,SAAS;AAAA,QACrB,4BAA4B,WAAW;AACrC,0BAAgB,OAAO;AAAA,QACzB;AAAA,MACF,CAAC;AAED,oBAAc,SAAS;AAAA,QACrB,sBAAsB,WAAW;AAC/B,0BAAgB,gBAAgB;AAAA,QAClC;AAAA,MACF,CAAC;AAED,oBAAc,SAAS;AAAA,QACrB,0BAA0B,WAAW;AACnC,cAAI,OAAO;AAAA,QACb;AAAA,MACF,CAAC;AAAA,IACH;AAEA,kBAAc,UAAU;AAAA,MACtB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC5CjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA;AAEA,QAAI,SAAS;AAAb,QACI,oBAAoB,OAAO;AAD/B,QAEI,qBAAqB;AAGzB,aAAS,iBAAiB,UAAU,UAAU;AAE5C,UAAI,gBAAgB,SAAS,IAAI,iBAAiB,KAAK,GACnD,WAAW,SAAS,IAAI,YAAY,KAAK;AAE7C,UAAI,CAAC,YAAY,CAAC,eAAe;AAC/B;AAAA,MACF;AAGA,UAAI,WAAW;AAGf,eAAS,eAAe,UAAU;AAChC,YAAI,MAAM,CAAE,KAAK,GAAI,GAAG,QAAQ,GAAG;AACjC,wBAAc,QAAQ,uBAAuB;AAE7C,iBAAO;AAAA,QACT;AAEA,YAAI,CAAC,UAAU;AACb;AAAA,QACF;AAEA,YAAI,MAAM,CAAE,KAAK,GAAI,GAAG,QAAQ,GAAG;AACjC,wBAAc,QAAQ,0BAA0B;AAEhD,iBAAO;AAAA,QACT;AAGA,YAAI,MAAM,CAAE,KAAK,UAAW,GAAG,QAAQ,GAAG;AACxC,wBAAc,QAAQ,4BAA4B;AAElD,iBAAO;AAAA,QACT;AAEA,YAAI,MAAM,CAAE,KAAK,GAAI,GAAG,QAAQ,GAAG;AACjC,wBAAc,QAAQ,sBAAsB;AAE5C,iBAAO;AAAA,QACT;AAAA,MACF;AAGA,eAAS,GAAG,iBAAiB,WAAW;AAEtC,iBAAS,YAAY,oBAAoB,SAAS,OAAO;AACvD,cAAI,WAAW,MAAM;AAErB,yBAAe,QAAQ;AAAA,QACzB,CAAC;AAAA,MAEH,CAAC;AAED,eAAS,GAAG,mBAAmB,SAAS,SAAS;AAC/C,YAAI,uBAAuB,QAAQ;AAEnC,YAAI,sBAAsB;AACxB,qBAAW;AAAA,QACb,OAAO;AACL,qBAAW;AAAA,QACb;AAAA,MACF,CAAC;AAAA,IAEH;AAEA,qBAAiB,UAAU,CAAE,YAAY,UAAW;AAEpD,WAAO,UAAU;AAKjB,aAAS,MAAMC,OAAM,OAAO;AAC1B,aAAOA,MAAK,QAAQ,MAAM,GAAG,IAAI;AAAA,IACnC;AAAA;AAAA;;;ACnFA;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA;AAEA,QAAI,SAAS;AAAb,QACI,aAAa;AAEjB,QAAI,SAAS;AAAb,QACI,oBAAoB,OAAO;AAE/B,aAAS,QAAQ,UAAU,QAAQ;AACjC,UAAI,OAAO;AAEX,WAAK,UAAU;AAEf,WAAK,UAAU,CAAC;AAEhB,WAAK,MAAM;AAEX,eAAS,GAAG,mBAAmB,SAAS,SAAS;AAC/C,YAAI,uBAAuB,QAAQ;AAEnC,YAAI,sBAAsB;AACxB,qBAAW,KAAK,SAAS,EAAE,OAAO,QAAQ;AAAA,QAC5C,OAAO;AACL,qBAAW,KAAK,SAAS,EAAE,IAAI,QAAQ;AAAA,QACzC;AAAA,MACF,CAAC;AAAA,IACH;AAEA,YAAQ,UAAU,QAAQ,WAAW;AACnC,WAAK,YAAY,OAAO,qDAAqD;AAE7E,WAAK,QAAQ,aAAa,EAAE,YAAY,KAAK,SAAS;AAAA,IACxD;AAEA,YAAQ,UAAU,WAAW,SAAS,OAAO,OAAO;AAClD,UAAI,aAAa;AAEjB,WAAK,QAAQ,QAAQ,SAASC,QAAO;AACnC,YAAI,SAASA,OAAM,OAAO;AACxB;AAAA,QACF;AAAA,MACF,CAAC;AAED,WAAK,UAAU,aAAa,OAAO,KAAK,UAAU,WAAW,UAAU,CAAC;AAExE,WAAK,QAAQ,KAAK;AAAA,QAChB;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAEA,YAAQ,UAAU,CAAE,YAAY,QAAS;AAEzC,WAAO,UAAU;AAAA;AAAA;;;ACrDjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB,IAAAC,mBAAA;AAAA;AAAA,WAAO,UAAU;AAAA,MACf,UAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,aAAa,CAAE,QAAQ,mBAAiC;AAAA,MACxD,eAAe,CAAE,QAAQ,sBAAmC;AAAA,MAC5D,mBAAmB,CAAE,QAAQ,0BAAuC;AAAA,MACpE,wBAAwB,CAAE,QAAQ,+BAA4C;AAAA,MAC9E,kBAAkB,CAAE,QAAQ,yBAAsC;AAAA,MAClE,4BAA4B,CAAE,QAAQ,oCAAiD;AAAA,MACvF,OAAO,CAAE,QAAQ,aAA0B;AAAA,MAC3C,iBAAiB,CAAE,QAAQ,uBAAoC;AAAA,MAC/D,mBAAmB,CAAE,QAAQ,0BAAuC;AAAA,MACpE,yBAAyB,CAAE,QAAQ,iCAA8C;AAAA,MACjF,sBAAsB,CAAE,QAAQ,8BAA2C;AAAA,MAC3E,2BAA2B,CAAE,QAAQ,mCAAgD;AAAA,MACrF,oBAAoB,CAAE,QAAQ,2BAAwC;AAAA,MACtE,mBAAmB,CAAE,QAAQ,0BAAuC;AAAA,MACpE,qBAAqB,CAAE,QAAQ,6BAA0C;AAAA,MACzE,uBAAuB,CAAE,QAAQ,+BAA4C;AAAA,MAC7E,mBAAmB,CAAE,QAAQ,0BAAuC;AAAA,MACpE,cAAc,CAAE,QAAQ,iBAA0C;AAAA,MAClE,cAAc,CAAE,QAAQ,qBAAkC;AAAA,MAC1D,2BAA2B,CAAE,QAAQ,mCAAgD;AAAA,MACrF,gCAAgC,CAAE,QAAQ,wBAAqC;AAAA,MAC/E,mCAAmC,CAAE,QAAQ,2BAAwC;AAAA,MACrF,0BAA0B,CAAE,QAAQ,iBAA8B;AAAA,IACpE;AAAA;AAAA;;;ACjDA;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;", "names": ["window", "document", "i", "il", "wrapper", "p", "str", "matrix", "size", "set", "timeout", "obj", "every", "some", "filter", "map", "require_domify", "bind", "element", "re", "outgoing", "processInstance", "element", "events", "outgoing", "require_ExclusiveGatewayHandler", "require_IntermediateCatchEventHandler", "events", "element", "require_StartEventHandler", "element", "require_BoundaryEventHandler", "keys", "entry", "require_modeler"]}