import { handleGetFormDesignWidgetName } from '../render/util';
export function getLimitSizeOptions() {
    const sizeOpts = [
        { label: '无限制', value: '' }
    ];
    const sizeList = [1, 2, 5, 10, 20, 50, 100, 200, 500];
    sizeList.forEach(num => {
        sizeOpts.push({ label: `${num}M`, value: num });
    });
    return sizeOpts;
}
export const getWidgetVxeUploadFileConfig = () => {
    return {
        title: handleGetFormDesignWidgetName,
        icon: 'vxe-icon-file',
        options: {
            limitCount: '',
            limitSize: 100,
            multiple: false
        }
    };
};
