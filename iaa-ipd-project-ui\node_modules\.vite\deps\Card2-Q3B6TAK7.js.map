{"version": 3, "sources": ["../../amis/esm/renderers/Card2.js"], "sourcesContent": ["/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __rest, __decorate, __metadata } from 'tslib';\nimport React from 'react';\nimport { Checkbox } from 'amis-ui';\nimport { buildStyle, autobind, Renderer } from 'amis-core';\n\nvar Card2 = /** @class */ (function (_super) {\n    __extends(Card2, _super);\n    function Card2() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Card2.prototype.handleClick = function (e) {\n        var _a = this.props, checkOnItemClick = _a.checkOnItemClick, selectable = _a.selectable;\n        // 控制选中\n        if (checkOnItemClick && selectable) {\n            this.handleCheck();\n        }\n        // TODO 触发事件动作\n    };\n    Card2.prototype.handleCheck = function () {\n        var _a, _b;\n        (_b = (_a = this.props).onCheck) === null || _b === void 0 ? void 0 : _b.call(_a);\n    };\n    Card2.prototype.renderCheckbox = function () {\n        var _a = this.props, selectable = _a.selectable, cx = _a.classnames, multiple = _a.multiple, disabled = _a.disabled, selected = _a.selected, hideCheckToggler = _a.hideCheckToggler, checkOnItemClick = _a.checkOnItemClick, checkboxClassname = _a.checkboxClassname;\n        if (!selectable || (checkOnItemClick && hideCheckToggler)) {\n            return null;\n        }\n        return (React.createElement(Checkbox, { className: cx('Card2-checkbox', checkboxClassname), type: multiple ? 'checkbox' : 'radio', disabled: disabled, checked: selected, onChange: this.handleCheck }));\n    };\n    /**\n     * 渲染内容区\n     */\n    Card2.prototype.renderBody = function () {\n        var _a = this.props, body = _a.body, render = _a.render, cx = _a.classnames, bodyClassName = _a.bodyClassName, rest = __rest(_a, [\"body\", \"render\", \"classnames\", \"bodyClassName\"]);\n        return (React.createElement(\"div\", { className: cx('Card2-body', bodyClassName), onClick: this.handleClick }, body ? render('body', body, rest) : null));\n    };\n    Card2.prototype.render = function () {\n        var _a = this.props, className = _a.className, wrapperComponent = _a.wrapperComponent, cx = _a.classnames, style = _a.style, item = _a.item, selected = _a.selected, checkOnItemClick = _a.checkOnItemClick;\n        var Component = wrapperComponent || 'div';\n        return (React.createElement(Component, { className: cx('Card2', className, {\n                'checkOnItem': checkOnItemClick,\n                'is-checked': selected\n            }), style: buildStyle(style, item) },\n            this.renderBody(),\n            this.renderCheckbox()));\n    };\n    Card2.propsList = ['body', 'className'];\n    Card2.defaultProps = {\n        className: ''\n    };\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], Card2.prototype, \"handleClick\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", []),\n        __metadata(\"design:returntype\", void 0)\n    ], Card2.prototype, \"handleCheck\", null);\n    return Card2;\n}(React.Component));\nvar Card2Renderer = /** @class */ (function (_super) {\n    __extends(Card2Renderer, _super);\n    function Card2Renderer() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Card2Renderer = __decorate([\n        Renderer({\n            type: 'card2'\n        })\n    ], Card2Renderer);\n    return Card2Renderer;\n}(Card2));\n\nexport { Card2Renderer, Card2 as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,mBAAkB;AAIlB,IAAI;AAAA;AAAA,EAAuB,SAAU,QAAQ;AACzC,cAAUA,QAAO,MAAM;AACvB,aAASA,SAAQ;AACb,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,OAAM,UAAU,cAAc,SAAU,GAAG;AACvC,UAAI,KAAK,KAAK,OAAO,mBAAmB,GAAG,kBAAkB,aAAa,GAAG;AAE7E,UAAI,oBAAoB,YAAY;AAChC,aAAK,YAAY;AAAA,MACrB;AAAA,IAEJ;AACA,IAAAA,OAAM,UAAU,cAAc,WAAY;AACtC,UAAI,IAAI;AACR,OAAC,MAAM,KAAK,KAAK,OAAO,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,EAAE;AAAA,IACpF;AACA,IAAAA,OAAM,UAAU,iBAAiB,WAAY;AACzC,UAAI,KAAK,KAAK,OAAO,aAAa,GAAG,YAAY,KAAK,GAAG,YAAY,WAAW,GAAG,UAAU,WAAW,GAAG,UAAU,WAAW,GAAG,UAAU,mBAAmB,GAAG,kBAAkB,mBAAmB,GAAG,kBAAkB,oBAAoB,GAAG;AACpP,UAAI,CAAC,cAAe,oBAAoB,kBAAmB;AACvD,eAAO;AAAA,MACX;AACA,aAAQ,aAAAC,QAAM,cAAc,YAAU,EAAE,WAAW,GAAG,kBAAkB,iBAAiB,GAAG,MAAM,WAAW,aAAa,SAAS,UAAoB,SAAS,UAAU,UAAU,KAAK,YAAY,CAAC;AAAA,IAC1M;AAIA,IAAAD,OAAM,UAAU,aAAa,WAAY;AACrC,UAAI,KAAK,KAAK,OAAO,OAAO,GAAG,MAAM,SAAS,GAAG,QAAQ,KAAK,GAAG,YAAY,gBAAgB,GAAG,eAAe,OAAO,OAAO,IAAI,CAAC,QAAQ,UAAU,cAAc,eAAe,CAAC;AAClL,aAAQ,aAAAC,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,cAAc,aAAa,GAAG,SAAS,KAAK,YAAY,GAAG,OAAO,OAAO,QAAQ,MAAM,IAAI,IAAI,IAAI;AAAA,IAC1J;AACA,IAAAD,OAAM,UAAU,SAAS,WAAY;AACjC,UAAI,KAAK,KAAK,OAAO,YAAY,GAAG,WAAW,mBAAmB,GAAG,kBAAkB,KAAK,GAAG,YAAY,QAAQ,GAAG,OAAO,OAAO,GAAG,MAAM,WAAW,GAAG,UAAU,mBAAmB,GAAG;AAC3L,UAAI,YAAY,oBAAoB;AACpC,aAAQ,aAAAC,QAAM;AAAA,QAAc;AAAA,QAAW,EAAE,WAAW,GAAG,SAAS,WAAW;AAAA,UACnE,eAAe;AAAA,UACf,cAAc;AAAA,QAClB,CAAC,GAAG,OAAO,WAAW,OAAO,IAAI,EAAE;AAAA,QACnC,KAAK,WAAW;AAAA,QAChB,KAAK,eAAe;AAAA,MAAC;AAAA,IAC7B;AACA,IAAAD,OAAM,YAAY,CAAC,QAAQ,WAAW;AACtC,IAAAA,OAAM,eAAe;AAAA,MACjB,WAAW;AAAA,IACf;AACA,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,eAAe,IAAI;AACvC,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,CAAC;AAAA,MAClC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,OAAM,WAAW,eAAe,IAAI;AACvC,WAAOA;AAAA,EACX,EAAE,aAAAC,QAAM,SAAS;AAAA;AACjB,IAAI;AAAA;AAAA,EAA+B,SAAU,QAAQ;AACjD,cAAUC,gBAAe,MAAM;AAC/B,aAASA,iBAAgB;AACrB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,iBAAgB,WAAW;AAAA,MACvB,SAAS;AAAA,QACL,MAAM;AAAA,MACV,CAAC;AAAA,IACL,GAAGA,cAAa;AAChB,WAAOA;AAAA,EACX,EAAE,KAAK;AAAA;", "names": ["Card2", "React", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}