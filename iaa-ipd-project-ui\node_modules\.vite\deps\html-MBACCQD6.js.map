{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/html/html.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { languages } from '../fillers/monaco-editor-core.js';\nvar EMPTY_ELEMENTS = [\n    'area',\n    'base',\n    'br',\n    'col',\n    'embed',\n    'hr',\n    'img',\n    'input',\n    'keygen',\n    'link',\n    'menuitem',\n    'meta',\n    'param',\n    'source',\n    'track',\n    'wbr'\n];\nexport var conf = {\n    wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\$\\^\\&\\*\\(\\)\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\s]+)/g,\n    comments: {\n        blockComment: ['<!--', '-->']\n    },\n    brackets: [\n        ['<!--', '-->'],\n        ['<', '>'],\n        ['{', '}'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" }\n    ],\n    surroundingPairs: [\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" },\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '<', close: '>' }\n    ],\n    onEnterRules: [\n        {\n            beforeText: new RegExp(\"<(?!(?:\" + EMPTY_ELEMENTS.join('|') + \"))([_:\\\\w][_:\\\\w-.\\\\d]*)([^/>]*(?!/)>)[^<]*$\", 'i'),\n            afterText: /^<\\/([_:\\w][_:\\w-.\\d]*)\\s*>$/i,\n            action: {\n                indentAction: languages.IndentAction.IndentOutdent\n            }\n        },\n        {\n            beforeText: new RegExp(\"<(?!(?:\" + EMPTY_ELEMENTS.join('|') + \"))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$\", 'i'),\n            action: { indentAction: languages.IndentAction.Indent }\n        }\n    ],\n    folding: {\n        markers: {\n            start: new RegExp('^\\\\s*<!--\\\\s*#region\\\\b.*-->'),\n            end: new RegExp('^\\\\s*<!--\\\\s*#endregion\\\\b.*-->')\n        }\n    }\n};\nexport var language = {\n    defaultToken: '',\n    tokenPostfix: '.html',\n    ignoreCase: true,\n    // The main tokenizer for our languages\n    tokenizer: {\n        root: [\n            [/<!DOCTYPE/, 'metatag', '@doctype'],\n            [/<!--/, 'comment', '@comment'],\n            [/(<)((?:[\\w\\-]+:)?[\\w\\-]+)(\\s*)(\\/>)/, ['delimiter', 'tag', '', 'delimiter']],\n            [/(<)(script)/, ['delimiter', { token: 'tag', next: '@script' }]],\n            [/(<)(style)/, ['delimiter', { token: 'tag', next: '@style' }]],\n            [/(<)((?:[\\w\\-]+:)?[\\w\\-]+)/, ['delimiter', { token: 'tag', next: '@otherTag' }]],\n            [/(<\\/)((?:[\\w\\-]+:)?[\\w\\-]+)/, ['delimiter', { token: 'tag', next: '@otherTag' }]],\n            [/</, 'delimiter'],\n            [/[^<]+/] // text\n        ],\n        doctype: [\n            [/[^>]+/, 'metatag.content'],\n            [/>/, 'metatag', '@pop']\n        ],\n        comment: [\n            [/-->/, 'comment', '@pop'],\n            [/[^-]+/, 'comment.content'],\n            [/./, 'comment.content']\n        ],\n        otherTag: [\n            [/\\/?>/, 'delimiter', '@pop'],\n            [/\"([^\"]*)\"/, 'attribute.value'],\n            [/'([^']*)'/, 'attribute.value'],\n            [/[\\w\\-]+/, 'attribute.name'],\n            [/=/, 'delimiter'],\n            [/[ \\t\\r\\n]+/] // whitespace\n        ],\n        // -- BEGIN <script> tags handling\n        // After <script\n        script: [\n            [/type/, 'attribute.name', '@scriptAfterType'],\n            [/\"([^\"]*)\"/, 'attribute.value'],\n            [/'([^']*)'/, 'attribute.value'],\n            [/[\\w\\-]+/, 'attribute.name'],\n            [/=/, 'delimiter'],\n            [\n                />/,\n                {\n                    token: 'delimiter',\n                    next: '@scriptEmbedded',\n                    nextEmbedded: 'text/javascript'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [/(<\\/)(script\\s*)(>)/, ['delimiter', 'tag', { token: 'delimiter', next: '@pop' }]]\n        ],\n        // After <script ... type\n        scriptAfterType: [\n            [/=/, 'delimiter', '@scriptAfterTypeEquals'],\n            [\n                />/,\n                {\n                    token: 'delimiter',\n                    next: '@scriptEmbedded',\n                    nextEmbedded: 'text/javascript'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/script\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        // After <script ... type =\n        scriptAfterTypeEquals: [\n            [\n                /\"([^\"]*)\"/,\n                {\n                    token: 'attribute.value',\n                    switchTo: '@scriptWithCustomType.$1'\n                }\n            ],\n            [\n                /'([^']*)'/,\n                {\n                    token: 'attribute.value',\n                    switchTo: '@scriptWithCustomType.$1'\n                }\n            ],\n            [\n                />/,\n                {\n                    token: 'delimiter',\n                    next: '@scriptEmbedded',\n                    nextEmbedded: 'text/javascript'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/script\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        // After <script ... type = $S2\n        scriptWithCustomType: [\n            [\n                />/,\n                {\n                    token: 'delimiter',\n                    next: '@scriptEmbedded.$S2',\n                    nextEmbedded: '$S2'\n                }\n            ],\n            [/\"([^\"]*)\"/, 'attribute.value'],\n            [/'([^']*)'/, 'attribute.value'],\n            [/[\\w\\-]+/, 'attribute.name'],\n            [/=/, 'delimiter'],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/script\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        scriptEmbedded: [\n            [/<\\/script/, { token: '@rematch', next: '@pop', nextEmbedded: '@pop' }],\n            [/[^<]+/, '']\n        ],\n        // -- END <script> tags handling\n        // -- BEGIN <style> tags handling\n        // After <style\n        style: [\n            [/type/, 'attribute.name', '@styleAfterType'],\n            [/\"([^\"]*)\"/, 'attribute.value'],\n            [/'([^']*)'/, 'attribute.value'],\n            [/[\\w\\-]+/, 'attribute.name'],\n            [/=/, 'delimiter'],\n            [\n                />/,\n                {\n                    token: 'delimiter',\n                    next: '@styleEmbedded',\n                    nextEmbedded: 'text/css'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [/(<\\/)(style\\s*)(>)/, ['delimiter', 'tag', { token: 'delimiter', next: '@pop' }]]\n        ],\n        // After <style ... type\n        styleAfterType: [\n            [/=/, 'delimiter', '@styleAfterTypeEquals'],\n            [\n                />/,\n                {\n                    token: 'delimiter',\n                    next: '@styleEmbedded',\n                    nextEmbedded: 'text/css'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/style\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        // After <style ... type =\n        styleAfterTypeEquals: [\n            [\n                /\"([^\"]*)\"/,\n                {\n                    token: 'attribute.value',\n                    switchTo: '@styleWithCustomType.$1'\n                }\n            ],\n            [\n                /'([^']*)'/,\n                {\n                    token: 'attribute.value',\n                    switchTo: '@styleWithCustomType.$1'\n                }\n            ],\n            [\n                />/,\n                {\n                    token: 'delimiter',\n                    next: '@styleEmbedded',\n                    nextEmbedded: 'text/css'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/style\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        // After <style ... type = $S2\n        styleWithCustomType: [\n            [\n                />/,\n                {\n                    token: 'delimiter',\n                    next: '@styleEmbedded.$S2',\n                    nextEmbedded: '$S2'\n                }\n            ],\n            [/\"([^\"]*)\"/, 'attribute.value'],\n            [/'([^']*)'/, 'attribute.value'],\n            [/[\\w\\-]+/, 'attribute.name'],\n            [/=/, 'delimiter'],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/style\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        styleEmbedded: [\n            [/<\\/style/, { token: '@rematch', next: '@pop', nextEmbedded: '@pop' }],\n            [/[^<]+/, '']\n        ]\n        // -- END <style> tags handling\n    }\n};\n// TESTED WITH:\n// <!DOCTYPE html>\n// <html>\n// <head>\n//   <title>Monarch Workbench</title>\n//   <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n//   <!----\n//   -- -- -- a comment -- -- --\n//   ---->\n//   <style bah=\"bah\">\n//     body { font-family: Consolas; } /* nice */\n//   </style>\n// </head\n// >\n// a = \"asd\"\n// <body>\n//   <br/>\n//   <div\n//   class\n//   =\n//   \"test\"\n//   >\n//     <script>\n//       function() {\n//         alert(\"hi </ script>\"); // javascript\n//       };\n//     </script>\n//     <script\n// \tbah=\"asdfg\"\n// \ttype=\"text/css\"\n// \t>\n//   .bar { text-decoration: underline; }\n//     </script>\n//   </div>\n// </body>\n// </html>\n"], "mappings": ";;;;;;;AAKA,IAAI,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACO,IAAI,OAAO;AAAA,EACd,aAAa;AAAA,EACb,UAAU;AAAA,IACN,cAAc,CAAC,QAAQ,KAAK;AAAA,EAChC;AAAA,EACA,UAAU;AAAA,IACN,CAAC,QAAQ,KAAK;AAAA,IACd,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,cAAc;AAAA,IACV;AAAA,MACI,YAAY,IAAI,OAAO,YAAY,eAAe,KAAK,GAAG,IAAI,gDAAgD,GAAG;AAAA,MACjH,WAAW;AAAA,MACX,QAAQ;AAAA,QACJ,cAAc,UAAU,aAAa;AAAA,MACzC;AAAA,IACJ;AAAA,IACA;AAAA,MACI,YAAY,IAAI,OAAO,YAAY,eAAe,KAAK,GAAG,IAAI,wCAAwC,GAAG;AAAA,MACzG,QAAQ,EAAE,cAAc,UAAU,aAAa,OAAO;AAAA,IAC1D;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL,SAAS;AAAA,MACL,OAAO,IAAI,OAAO,8BAA8B;AAAA,MAChD,KAAK,IAAI,OAAO,iCAAiC;AAAA,IACrD;AAAA,EACJ;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA;AAAA,EAEZ,WAAW;AAAA,IACP,MAAM;AAAA,MACF,CAAC,aAAa,WAAW,UAAU;AAAA,MACnC,CAAC,QAAQ,WAAW,UAAU;AAAA,MAC9B,CAAC,uCAAuC,CAAC,aAAa,OAAO,IAAI,WAAW,CAAC;AAAA,MAC7E,CAAC,eAAe,CAAC,aAAa,EAAE,OAAO,OAAO,MAAM,UAAU,CAAC,CAAC;AAAA,MAChE,CAAC,cAAc,CAAC,aAAa,EAAE,OAAO,OAAO,MAAM,SAAS,CAAC,CAAC;AAAA,MAC9D,CAAC,6BAA6B,CAAC,aAAa,EAAE,OAAO,OAAO,MAAM,YAAY,CAAC,CAAC;AAAA,MAChF,CAAC,+BAA+B,CAAC,aAAa,EAAE,OAAO,OAAO,MAAM,YAAY,CAAC,CAAC;AAAA,MAClF,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,OAAO;AAAA;AAAA,IACZ;AAAA,IACA,SAAS;AAAA,MACL,CAAC,SAAS,iBAAiB;AAAA,MAC3B,CAAC,KAAK,WAAW,MAAM;AAAA,IAC3B;AAAA,IACA,SAAS;AAAA,MACL,CAAC,OAAO,WAAW,MAAM;AAAA,MACzB,CAAC,SAAS,iBAAiB;AAAA,MAC3B,CAAC,KAAK,iBAAiB;AAAA,IAC3B;AAAA,IACA,UAAU;AAAA,MACN,CAAC,QAAQ,aAAa,MAAM;AAAA,MAC5B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY;AAAA;AAAA,IACjB;AAAA;AAAA;AAAA,IAGA,QAAQ;AAAA,MACJ,CAAC,QAAQ,kBAAkB,kBAAkB;AAAA,MAC7C,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,uBAAuB,CAAC,aAAa,OAAO,EAAE,OAAO,aAAa,MAAM,OAAO,CAAC,CAAC;AAAA,IACtF;AAAA;AAAA,IAEA,iBAAiB;AAAA,MACb,CAAC,KAAK,aAAa,wBAAwB;AAAA,MAC3C;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACzD;AAAA;AAAA,IAEA,uBAAuB;AAAA,MACnB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACzD;AAAA;AAAA,IAEA,sBAAsB;AAAA,MAClB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY;AAAA,MACb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACzD;AAAA,IACA,gBAAgB;AAAA,MACZ,CAAC,aAAa,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,MACvE,CAAC,SAAS,EAAE;AAAA,IAChB;AAAA;AAAA;AAAA;AAAA,IAIA,OAAO;AAAA,MACH,CAAC,QAAQ,kBAAkB,iBAAiB;AAAA,MAC5C,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,sBAAsB,CAAC,aAAa,OAAO,EAAE,OAAO,aAAa,MAAM,OAAO,CAAC,CAAC;AAAA,IACrF;AAAA;AAAA,IAEA,gBAAgB;AAAA,MACZ,CAAC,KAAK,aAAa,uBAAuB;AAAA,MAC1C;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACxD;AAAA;AAAA,IAEA,sBAAsB;AAAA,MAClB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACxD;AAAA;AAAA,IAEA,qBAAqB;AAAA,MACjB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY;AAAA,MACb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACxD;AAAA,IACA,eAAe;AAAA,MACX,CAAC,YAAY,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,MACtE,CAAC,SAAS,EAAE;AAAA,IAChB;AAAA;AAAA,EAEJ;AACJ;", "names": []}