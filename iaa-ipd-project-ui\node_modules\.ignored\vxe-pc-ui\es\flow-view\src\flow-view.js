import { defineComponent, ref, h, reactive } from 'vue';
import XEUtils from 'xe-utils';
export default defineComponent({
    name: 'VxeFlowView',
    props: {},
    emits: [],
    setup(props, context) {
        const { slots } = context;
        const xID = XEUtils.uniqueId();
        const refElem = ref();
        const reactData = reactive({});
        const refMaps = {
            refElem
        };
        const computeMaps = {};
        const $xeFlowView = {
            xID,
            props,
            context,
            reactData,
            getRefMaps: () => refMaps,
            getComputeMaps: () => computeMaps
        };
        const renderVN = () => {
            const defaultSlot = slots.default;
            return h('div', {
                ref: refElem,
                class: ['vxe-upload']
            }, defaultSlot ? defaultSlot({}) : []);
        };
        $xeFlowView.renderVN = renderVN;
        return $xeFlowView;
    },
    render() {
        return this.renderVN();
    }
});
