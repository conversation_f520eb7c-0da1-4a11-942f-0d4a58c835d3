Object.defineProperty(exports,"__esModule",{value:!0}),exports.assembleColumn=assembleColumn,exports.calcTreeLine=calcTreeLine,exports.clearTableAllStatus=clearTableAllStatus,exports.clearTableDefaultStatus=clearTableDefaultStatus,exports.colToVisible=colToVisible,exports.convertHeaderColumnToRows=void 0,exports.createColumn=createColumn,exports.createHandleGetRowId=createHandleGetRowId,exports.createHandleUpdateRowId=createHandleUpdateRowId,exports.destroyColumn=destroyColumn,exports.encodeRowid=encodeRowid,exports.getCellHeight=getCellHeight,exports.getCellRestHeight=getCellRestHeight,exports.getCellValue=getCellValue,exports.getColReMaxWidth=getColReMaxWidth,exports.getColReMinWidth=getColReMinWidth,exports.getOffsetSize=void 0,exports.getRefElem=getRefElem,exports.getRootColumn=getRootColumn,exports.getRowUniqueId=getRowUniqueId,exports.getRowid=getRowid,exports.getRowkey=getRowkey,exports.handleFieldOrColumn=handleFieldOrColumn,exports.handleRowidOrRow=handleRowidOrRow,exports.hasDeepKey=hasDeepKey,exports.isColumnInfo=isColumnInfo,exports.restoreScrollLocation=restoreScrollLocation,exports.rowToVisible=rowToVisible,exports.setCellValue=setCellValue,exports.toFilters=toFilters,exports.toTreePathSeq=toTreePathSeq,exports.updateDeepRowKey=updateDeepRowKey,exports.updateFastRowKey=updateFastRowKey,exports.watchColumn=watchColumn;var _vue=require("vue"),_xeUtils=_interopRequireDefault(require("xe-utils")),_columnInfo=require("./columnInfo"),_dom=require("../../ui/src/dom"),_utils=require("../../ui/src/utils");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let getAllConvertColumns=(e,t)=>{let l=[];return e.forEach(e=>{e.parentId=t?t.id:null,e.visible&&(e.children&&e.children.length&&e.children.some(e=>e.visible)?(l.push(e),l.push(...getAllConvertColumns(e.children,e))):l.push(e))}),l},convertHeaderColumnToRows=e=>{let t=1,r=(l,e)=>{if(e&&(l.level=e.level+1,t<l.level)&&(t=l.level),l.children&&l.children.length&&l.children.some(e=>e.visible)){let t=0;l.children.forEach(e=>{e.visible&&(r(e,l),t+=e.colSpan)}),l.colSpan=t}else l.colSpan=1},l=(e.forEach(e=>{e.level=1,r(e)}),[]);for(let e=0;e<t;e++)l.push([]);return getAllConvertColumns(e).forEach(e=>{e.children&&e.children.length&&e.children.some(e=>e.visible)?e.rowSpan=1:e.rowSpan=t-e.level+1,l[e.level-1].push(e)}),l};function restoreScrollLocation(e,t,l){var r=e.internalData;return t||l?(r.intoRunScroll=!1,r.inVirtualScroll=!1,r.inWheelScroll=!1,r.inHeaderScroll=!1,r.inBodyScroll=!1,r.inFooterScroll=!1,r.scrollRenderType="",e.scrollTo(t,l)):e.clearScroll()}function getRowUniqueId(){return _xeUtils.default.uniqueId("row_")}function hasDeepKey(e){return-1<e.indexOf(".")}function getRowkey(e){e=e.internalData.currKeyField;return e}function getRowid(e,t){var{isCurrDeepKey:e,currKeyField:l}=e.internalData;return t?encodeRowid((e?getDeepRowIdByKey:getFastRowIdByKey)(t,l)):""}function createHandleUpdateRowId(e){let{isCurrDeepKey:t,currKeyField:l}=e.internalData,r=t?updateDeepRowKey:updateFastRowKey;return{rowKey:l,handleUpdateRowId(e){return e?r(e,l):""}}}function createHandleGetRowId(e){let{isCurrDeepKey:t,currKeyField:l}=e.internalData,r=t?getDeepRowIdByKey:getFastRowIdByKey;return{rowKey:l,handleGetRowId(e){return e?encodeRowid(r(e,l)):""}}}function encodeRowid(e){return _xeUtils.default.eqNull(e)?"":encodeURIComponent(e)}function getDeepRowIdByKey(e,t){return _xeUtils.default.get(e,t)}function updateDeepRowKey(e,t){let l=encodeRowid(getDeepRowIdByKey(e,t));return(0,_utils.eqEmptyValue)(l)&&(l=getRowUniqueId(),_xeUtils.default.set(e,t,l)),l}function getFastRowIdByKey(e,t){return e[t]}function updateFastRowKey(e,t){let l=encodeRowid(getFastRowIdByKey(e,t));return(0,_utils.eqEmptyValue)(l)&&(l=getRowUniqueId(),e[t]=l),l}function handleFieldOrColumn(e,t){return t?_xeUtils.default.isString(t)||_xeUtils.default.isNumber(t)?e.getColumnByField(""+t):t:null}function handleRowidOrRow(e,t){return t?(t=_xeUtils.default.isString(t)||_xeUtils.default.isNumber(t)?t:getRowid(e,t),e.getRowById(t)):null}function getCellRestHeight(e,t,l,r){return e.resizeHeight||t.height||l.height||e.height||r}function getPaddingLeftRightSize(e){return e?(e=getComputedStyle(e),_xeUtils.default.toNumber(e.paddingLeft)+_xeUtils.default.toNumber(e.paddingRight)):0}function getElementMarginAndWidth(e){var t,l;return e?(l=getComputedStyle(e),t=_xeUtils.default.toNumber(l.marginLeft),l=_xeUtils.default.toNumber(l.marginRight),e.offsetWidth+t+l):0}function toFilters(e){return e&&_xeUtils.default.isArray(e)?e.map(({label:e,value:t,data:l,resetValue:r,checked:o})=>({label:e,value:t,data:l,resetValue:r,checked:!!o,_checked:!!o})):e}function toTreePathSeq(e){return e.map((e,t)=>t%2==0?Number(e)+1:".").join("")}function getCellValue(e,t){return _xeUtils.default.get(e,t.field)}function setCellValue(e,t,l){return _xeUtils.default.set(e,t.field,l)}function getRefElem(e){if(e){e=e.value;if(e)return e.$el||e}return null}function getCellHeight(e){return"unset"!==e&&e||0}function getColReMaxWidth(e){var t=e.$table,t=t.getComputeMaps().computeResizableOpts,t=t.value.maxWidth;if(t){e=_xeUtils.default.isFunction(t)?t(e):t;if("auto"!==e)return Math.max(1,_xeUtils.default.toNumber(e))}return-1}function getColReMinWidth(e){var{$table:t,column:l,cell:r}=e,o=t.props,i=t.internalData,t=t.getComputeMaps().computeResizableOpts,t=t.value.minWidth;if(t){e=_xeUtils.default.isFunction(t)?t(e):t;if("auto"!==e)return Math.max(1,_xeUtils.default.toNumber(e))}var n,a,u,d,t=i.elemStore,e=o.showHeaderOverflow,{showHeaderOverflow:i,minWidth:o}=l,l=_xeUtils.default.isUndefined(i)||_xeUtils.default.isNull(i)?e:i,e="title"===l||(!0===l||"tooltip"===l)||"ellipsis"===l;let s=_xeUtils.default.floor(1.8*(_xeUtils.default.toNumber(getComputedStyle(r).fontSize)||14))+(getPaddingLeftRightSize(r)+getPaddingLeftRightSize((0,_dom.queryElement)(r,".vxe-cell")));if(e&&(i=getElementMarginAndWidth((0,_dom.queryElement)(r,".vxe-cell--drag-handle")),l=getElementMarginAndWidth((0,_dom.queryElement)(r,".vxe-cell--checkbox")),e=getElementMarginAndWidth((0,_dom.queryElement)(r,".vxe-cell--required-icon")),n=getElementMarginAndWidth((0,_dom.queryElement)(r,".vxe-cell--edit-icon")),a=getElementMarginAndWidth((0,_dom.queryElement)(r,".vxe-cell-title-prefix-icon")),u=getElementMarginAndWidth((0,_dom.queryElement)(r,".vxe-cell-title-suffix-icon")),d=getElementMarginAndWidth((0,_dom.queryElement)(r,".vxe-cell--sort")),r=getElementMarginAndWidth((0,_dom.queryElement)(r,".vxe-cell--filter")),s+=i+l+e+n+a+u+r+d),o){i=getRefElem(t["main-body-scroll"]);if(i){if((0,_dom.isScale)(o))return l=(i.clientWidth-1)/100,Math.max(s,Math.floor(_xeUtils.default.toInteger(o)*l));if((0,_dom.isPx)(o))return Math.max(s,_xeUtils.default.toInteger(o))}}return s}function isColumnInfo(e){return e&&(e.constructor===_columnInfo.ColumnInfo||e instanceof _columnInfo.ColumnInfo)}function createColumn(e,t,l){return isColumnInfo(t)?t:(0,_vue.reactive)(new _columnInfo.ColumnInfo(e,t,l))}function watchColumn(l,e,r){Object.keys(e).forEach(t=>{(0,_vue.watch)(()=>e[t],e=>{r.update(t,e),l&&("filters"===t?(l.setFilter(r,e),l.handleUpdateDataQueue()):["visible","fixed","width","minWidth","maxWidth"].includes(t)&&l.handleRefreshColumnQueue())})})}function assembleColumn(e,t,l,r){var e=e.reactData,o=e.staticColumns,i=t.parentNode,r=r?r.columnConfig:null,r=r?r.children:o;i&&r&&(r.splice(_xeUtils.default.arrayIndexOf(i.children,t),0,l),e.staticColumns=o.slice(0))}function destroyColumn(e,t){var e=e.reactData,l=e.staticColumns,r=_xeUtils.default.findTree(l,e=>e.id===t.id,{children:"children"});r&&r.items.splice(r.index,1),e.staticColumns=l.slice(0)}function getRootColumn(e,t){var e=e.internalData,l=e.fullColumnIdData;if(!t)return null;let r=t.parentId;for(;l[r];){let e=l[r].column;if(!(r=e.parentId))return e}return t}exports.convertHeaderColumnToRows=convertHeaderColumnToRows;let lineOffsetSizes={mini:3,small:2,medium:1,large:0},countTreeExpand=(e,t)=>{let l=1;if(e){var r=t.$table,o=r.getComputeMaps().computeTreeOpts,o=o.value,{transform:i,mapChildrenField:n}=o,o=o.children||o.childrenField,a=e[i?n:o];if(a&&r.isTreeExpandByRow(e))for(let e=0;e<a.length;e++)l+=countTreeExpand(a[e],t)}return l},getOffsetSize=e=>{e=e.getComputeMaps().computeSize,e=e.value;return e&&lineOffsetSizes[e]||0};function calcTreeLine(e,t){var{$table:l,row:r}=e,o=l.props.showOverflow,i=l.reactData.scrollYLoad,n=l.internalData.fullAllDataRowIdData,{computeRowOpts:a,computeCellOpts:u,computeDefaultRowHeight:d}=l.getComputeMaps(),a=a.value,u=u.value,d=d.value,n=n[getRowid(l,r)],r=n.resizeHeight||u.height||a.height||n.height||d;let s=1,c=(t&&(s=countTreeExpand(t,e)),r);return(c=i&&!o?n.height||r:c)*s-(t?1:12-getOffsetSize(l))}function clearTableDefaultStatus(e){var{props:t,internalData:l}=e;return l.initStatus=!1,e.clearSort(),e.clearCurrentRow(),e.clearCurrentColumn(),e.clearRadioRow(),e.clearRadioReserve(),e.clearCheckboxRow(),e.clearCheckboxReserve(),e.clearRowExpand(),e.clearTreeExpand(),e.clearTreeExpandReserve(),e.clearPendingRow(),e.clearFilter&&e.clearFilter(),e.clearSelected&&(t.keyboardConfig||t.mouseConfig)&&e.clearSelected(),e.clearCellAreas&&t.mouseConfig&&(e.clearCellAreas(),e.clearCopyCellArea()),e.clearScroll()}function clearTableAllStatus(e){return e.clearFilter&&e.clearFilter(),clearTableDefaultStatus(e)}function rowToVisible(l,r){var e=l.props,t=l.reactData,o=l.internalData,{computeLeftFixedWidth:i,computeRightFixedWidth:n,computeRowOpts:a,computeCellOpts:u,computeDefaultRowHeight:d}=l.getComputeMaps(),e=e.showOverflow,{scrollYLoad:t,scrollYTop:s}=t,{elemStore:o,afterFullData:c,fullAllDataRowIdData:f,isResizeCellHeight:m}=o,h=a.value,p=u.value,g=d.value,a=i.value,u=n.value,d=getRefElem(o["main-body-scroll"]),R=getRowid(l,r);if(d){i=d.clientHeight,n=d.scrollTop,o=d.querySelector(`[rowid="${R}"]`);if(o){d=o.offsetTop+(t?s:0),s=o.clientHeight;if(d<n||n+i<d)return l.scrollTo(null,d);if(i+n<=d+s)return l.scrollTo(null,n+s)}else if(t){if(!(m||p.height||h.height)&&e)return l.scrollTo(null,(l.findRowIndexOf(c,r)-1)*g);let t=0;o=f[R]||{},d=o.resizeHeight||p.height||h.height||o.height||g;for(let e=0;e<c.length;e++){var x=c[e],w=getRowid(l,x);if(x===r||w===R)break;x=f[w]||{};t+=x.resizeHeight||p.height||h.height||x.height||g}return t<n?l.scrollTo(null,t-a-1):l.scrollTo(null,t+d-(i-u-1))}}return Promise.resolve()}function colToVisible(l,r,t){var o=l.reactData,i=l.internalData,{computeLeftFixedWidth:n,computeRightFixedWidth:a}=l.getComputeMaps(),{scrollXLoad:o,scrollXLeft:u}=o,{elemStore:i,visibleColumn:d}=i,n=n.value,a=a.value,i=getRefElem(i["main-body-scroll"]);if(!r.fixed&&i){var s=i.clientWidth,c=i.scrollLeft;let e=null;if(t&&(t=getRowid(l,t),e=i.querySelector(`[rowid="${t}"] .`+r.id)),e=e||i.querySelector("."+r.id)){t=e.offsetLeft+(o?u:0),i=e.clientWidth;if(t<c+n)return l.scrollTo(t-n-1);if(s-a<t+i-c)return l.scrollTo(t+i-(s-a-1))}else if(o){let t=0;u=r.renderWidth;for(let e=0;e<d.length;e++){var f=d[e];if(f===r||f.id===r.id)break;t+=f.renderWidth}return t<c?l.scrollTo(t-n-1):l.scrollTo(t+u-(s-a-1))}}return Promise.resolve()}exports.getOffsetSize=getOffsetSize;