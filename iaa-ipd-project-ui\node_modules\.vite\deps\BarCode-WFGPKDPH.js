import {
  __decorate,
  __extends
} from "./chunk-F25BIIHK.js";
import {
  <PERSON><PERSON><PERSON>,
  getPropValue
} from "./chunk-LZQZ2OHM.js";
import "./chunk-5QW7M2DY.js";
import "./chunk-ZJNJ4ZV3.js";
import "./chunk-CNJUQDSN.js";
import "./chunk-7XBFYOTW.js";
import {
  require_react
} from "./chunk-ZUWHLQVA.js";
import {
  __toESM
} from "./chunk-GFT2G5UO.js";

// node_modules/amis/esm/renderers/BarCode.js
var import_react = __toESM(require_react());
var BarCode = import_react.default.lazy(function() {
  return import("./BarCode-CSJWSHXK.js");
});
var BarCodeField = (
  /** @class */
  function(_super) {
    __extends(BarCodeField2, _super);
    function BarCodeField2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    BarCodeField2.prototype.render = function() {
      var _a = this.props, className = _a.className, style = _a.style, width = _a.width, height = _a.height, cx = _a.classnames, options = _a.options;
      var value = getPropValue(this.props);
      return import_react.default.createElement(
        import_react.Suspense,
        { fallback: import_react.default.createElement("div", null, "...") },
        import_react.default.createElement(
          "div",
          { "data-testid": "barcode", className: cx("BarCode", className), style },
          import_react.default.createElement(BarCode, { value, options })
        )
      );
    };
    return BarCodeField2;
  }(import_react.default.Component)
);
var BarCodeFieldRenderer = (
  /** @class */
  function(_super) {
    __extends(BarCodeFieldRenderer2, _super);
    function BarCodeFieldRenderer2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    BarCodeFieldRenderer2 = __decorate([
      Renderer({
        type: "barcode"
      })
    ], BarCodeFieldRenderer2);
    return BarCodeFieldRenderer2;
  }(BarCodeField)
);
export {
  BarCodeField,
  BarCodeFieldRenderer
};
//# sourceMappingURL=BarCode-WFGPKDPH.js.map
