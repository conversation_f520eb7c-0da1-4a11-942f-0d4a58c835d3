{"version": 3, "sources": ["../../bpmn-js/lib/util/ModelUtil.js"], "sourcesContent": ["/**\n * Is an element of the given BPMN type?\n *\n * @param  {djs.model.Base|ModdleElement} element\n * @param  {string} type\n *\n * @return {boolean}\n */\nexport function is(element, type) {\n  var bo = getBusinessObject(element);\n\n  return bo && (typeof bo.$instanceOf === 'function') && bo.$instanceOf(type);\n}\n\n\n/**\n * Return the business object for a given element.\n *\n * @param  {djs.model.Base|ModdleElement} element\n *\n * @return {ModdleElement}\n */\nexport function getBusinessObject(element) {\n  return (element && element.businessObject) || element;\n}"], "mappings": ";AAQO,SAAS,GAAG,SAAS,MAAM;AAChC,MAAI,KAAK,kBAAkB,OAAO;AAElC,SAAO,MAAO,OAAO,GAAG,gBAAgB,cAAe,GAAG,YAAY,IAAI;AAC5E;AAUO,SAAS,kBAAkB,SAAS;AACzC,SAAQ,WAAW,QAAQ,kBAAmB;AAChD;", "names": []}