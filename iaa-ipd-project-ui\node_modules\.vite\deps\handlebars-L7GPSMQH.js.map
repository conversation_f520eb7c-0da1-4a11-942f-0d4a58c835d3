{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/handlebars/handlebars.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { languages } from '../fillers/monaco-editor-core.js';\nvar EMPTY_ELEMENTS = [\n    'area',\n    'base',\n    'br',\n    'col',\n    'embed',\n    'hr',\n    'img',\n    'input',\n    'keygen',\n    'link',\n    'menuitem',\n    'meta',\n    'param',\n    'source',\n    'track',\n    'wbr'\n];\nexport var conf = {\n    wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\$\\^\\&\\*\\(\\)\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\s]+)/g,\n    comments: {\n        blockComment: ['{{!--', '--}}']\n    },\n    brackets: [\n        ['<!--', '-->'],\n        ['<', '>'],\n        ['{{', '}}'],\n        ['{', '}'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" }\n    ],\n    surroundingPairs: [\n        { open: '<', close: '>' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" }\n    ],\n    onEnterRules: [\n        {\n            beforeText: new RegExp(\"<(?!(?:\" + EMPTY_ELEMENTS.join('|') + \"))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$\", 'i'),\n            afterText: /^<\\/(\\w[\\w\\d]*)\\s*>$/i,\n            action: {\n                indentAction: languages.IndentAction.IndentOutdent\n            }\n        },\n        {\n            beforeText: new RegExp(\"<(?!(?:\" + EMPTY_ELEMENTS.join('|') + \"))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$\", 'i'),\n            action: { indentAction: languages.IndentAction.Indent }\n        }\n    ]\n};\nexport var language = {\n    defaultToken: '',\n    tokenPostfix: '',\n    // ignoreCase: true,\n    // The main tokenizer for our languages\n    tokenizer: {\n        root: [\n            [/\\{\\{!--/, 'comment.block.start.handlebars', '@commentBlock'],\n            [/\\{\\{!/, 'comment.start.handlebars', '@comment'],\n            [/\\{\\{/, { token: '@rematch', switchTo: '@handlebarsInSimpleState.root' }],\n            [/<!DOCTYPE/, 'metatag.html', '@doctype'],\n            [/<!--/, 'comment.html', '@commentHtml'],\n            [/(<)(\\w+)(\\/>)/, ['delimiter.html', 'tag.html', 'delimiter.html']],\n            [/(<)(script)/, ['delimiter.html', { token: 'tag.html', next: '@script' }]],\n            [/(<)(style)/, ['delimiter.html', { token: 'tag.html', next: '@style' }]],\n            [/(<)([:\\w]+)/, ['delimiter.html', { token: 'tag.html', next: '@otherTag' }]],\n            [/(<\\/)(\\w+)/, ['delimiter.html', { token: 'tag.html', next: '@otherTag' }]],\n            [/</, 'delimiter.html'],\n            [/\\{/, 'delimiter.html'],\n            [/[^<{]+/] // text\n        ],\n        doctype: [\n            [\n                /\\{\\{/,\n                {\n                    token: '@rematch',\n                    switchTo: '@handlebarsInSimpleState.comment'\n                }\n            ],\n            [/[^>]+/, 'metatag.content.html'],\n            [/>/, 'metatag.html', '@pop']\n        ],\n        comment: [\n            [/\\}\\}/, 'comment.end.handlebars', '@pop'],\n            [/./, 'comment.content.handlebars']\n        ],\n        commentBlock: [\n            [/--\\}\\}/, 'comment.block.end.handlebars', '@pop'],\n            [/./, 'comment.content.handlebars']\n        ],\n        commentHtml: [\n            [\n                /\\{\\{/,\n                {\n                    token: '@rematch',\n                    switchTo: '@handlebarsInSimpleState.comment'\n                }\n            ],\n            [/-->/, 'comment.html', '@pop'],\n            [/[^-]+/, 'comment.content.html'],\n            [/./, 'comment.content.html']\n        ],\n        otherTag: [\n            [\n                /\\{\\{/,\n                {\n                    token: '@rematch',\n                    switchTo: '@handlebarsInSimpleState.otherTag'\n                }\n            ],\n            [/\\/?>/, 'delimiter.html', '@pop'],\n            [/\"([^\"]*)\"/, 'attribute.value'],\n            [/'([^']*)'/, 'attribute.value'],\n            [/[\\w\\-]+/, 'attribute.name'],\n            [/=/, 'delimiter'],\n            [/[ \\t\\r\\n]+/] // whitespace\n        ],\n        // -- BEGIN <script> tags handling\n        // After <script\n        script: [\n            [\n                /\\{\\{/,\n                {\n                    token: '@rematch',\n                    switchTo: '@handlebarsInSimpleState.script'\n                }\n            ],\n            [/type/, 'attribute.name', '@scriptAfterType'],\n            [/\"([^\"]*)\"/, 'attribute.value'],\n            [/'([^']*)'/, 'attribute.value'],\n            [/[\\w\\-]+/, 'attribute.name'],\n            [/=/, 'delimiter'],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@scriptEmbedded.text/javascript',\n                    nextEmbedded: 'text/javascript'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [\n                /(<\\/)(script\\s*)(>)/,\n                ['delimiter.html', 'tag.html', { token: 'delimiter.html', next: '@pop' }]\n            ]\n        ],\n        // After <script ... type\n        scriptAfterType: [\n            [\n                /\\{\\{/,\n                {\n                    token: '@rematch',\n                    switchTo: '@handlebarsInSimpleState.scriptAfterType'\n                }\n            ],\n            [/=/, 'delimiter', '@scriptAfterTypeEquals'],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@scriptEmbedded.text/javascript',\n                    nextEmbedded: 'text/javascript'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/script\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        // After <script ... type =\n        scriptAfterTypeEquals: [\n            [\n                /\\{\\{/,\n                {\n                    token: '@rematch',\n                    switchTo: '@handlebarsInSimpleState.scriptAfterTypeEquals'\n                }\n            ],\n            [\n                /\"([^\"]*)\"/,\n                {\n                    token: 'attribute.value',\n                    switchTo: '@scriptWithCustomType.$1'\n                }\n            ],\n            [\n                /'([^']*)'/,\n                {\n                    token: 'attribute.value',\n                    switchTo: '@scriptWithCustomType.$1'\n                }\n            ],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@scriptEmbedded.text/javascript',\n                    nextEmbedded: 'text/javascript'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/script\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        // After <script ... type = $S2\n        scriptWithCustomType: [\n            [\n                /\\{\\{/,\n                {\n                    token: '@rematch',\n                    switchTo: '@handlebarsInSimpleState.scriptWithCustomType.$S2'\n                }\n            ],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@scriptEmbedded.$S2',\n                    nextEmbedded: '$S2'\n                }\n            ],\n            [/\"([^\"]*)\"/, 'attribute.value'],\n            [/'([^']*)'/, 'attribute.value'],\n            [/[\\w\\-]+/, 'attribute.name'],\n            [/=/, 'delimiter'],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/script\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        scriptEmbedded: [\n            [\n                /\\{\\{/,\n                {\n                    token: '@rematch',\n                    switchTo: '@handlebarsInEmbeddedState.scriptEmbedded.$S2',\n                    nextEmbedded: '@pop'\n                }\n            ],\n            [/<\\/script/, { token: '@rematch', next: '@pop', nextEmbedded: '@pop' }]\n        ],\n        // -- END <script> tags handling\n        // -- BEGIN <style> tags handling\n        // After <style\n        style: [\n            [\n                /\\{\\{/,\n                {\n                    token: '@rematch',\n                    switchTo: '@handlebarsInSimpleState.style'\n                }\n            ],\n            [/type/, 'attribute.name', '@styleAfterType'],\n            [/\"([^\"]*)\"/, 'attribute.value'],\n            [/'([^']*)'/, 'attribute.value'],\n            [/[\\w\\-]+/, 'attribute.name'],\n            [/=/, 'delimiter'],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@styleEmbedded.text/css',\n                    nextEmbedded: 'text/css'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [\n                /(<\\/)(style\\s*)(>)/,\n                ['delimiter.html', 'tag.html', { token: 'delimiter.html', next: '@pop' }]\n            ]\n        ],\n        // After <style ... type\n        styleAfterType: [\n            [\n                /\\{\\{/,\n                {\n                    token: '@rematch',\n                    switchTo: '@handlebarsInSimpleState.styleAfterType'\n                }\n            ],\n            [/=/, 'delimiter', '@styleAfterTypeEquals'],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@styleEmbedded.text/css',\n                    nextEmbedded: 'text/css'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/style\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        // After <style ... type =\n        styleAfterTypeEquals: [\n            [\n                /\\{\\{/,\n                {\n                    token: '@rematch',\n                    switchTo: '@handlebarsInSimpleState.styleAfterTypeEquals'\n                }\n            ],\n            [\n                /\"([^\"]*)\"/,\n                {\n                    token: 'attribute.value',\n                    switchTo: '@styleWithCustomType.$1'\n                }\n            ],\n            [\n                /'([^']*)'/,\n                {\n                    token: 'attribute.value',\n                    switchTo: '@styleWithCustomType.$1'\n                }\n            ],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@styleEmbedded.text/css',\n                    nextEmbedded: 'text/css'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/style\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        // After <style ... type = $S2\n        styleWithCustomType: [\n            [\n                /\\{\\{/,\n                {\n                    token: '@rematch',\n                    switchTo: '@handlebarsInSimpleState.styleWithCustomType.$S2'\n                }\n            ],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@styleEmbedded.$S2',\n                    nextEmbedded: '$S2'\n                }\n            ],\n            [/\"([^\"]*)\"/, 'attribute.value'],\n            [/'([^']*)'/, 'attribute.value'],\n            [/[\\w\\-]+/, 'attribute.name'],\n            [/=/, 'delimiter'],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/style\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        styleEmbedded: [\n            [\n                /\\{\\{/,\n                {\n                    token: '@rematch',\n                    switchTo: '@handlebarsInEmbeddedState.styleEmbedded.$S2',\n                    nextEmbedded: '@pop'\n                }\n            ],\n            [/<\\/style/, { token: '@rematch', next: '@pop', nextEmbedded: '@pop' }]\n        ],\n        // -- END <style> tags handling\n        handlebarsInSimpleState: [\n            [/\\{\\{\\{?/, 'delimiter.handlebars'],\n            [/\\}\\}\\}?/, { token: 'delimiter.handlebars', switchTo: '@$S2.$S3' }],\n            { include: 'handlebarsRoot' }\n        ],\n        handlebarsInEmbeddedState: [\n            [/\\{\\{\\{?/, 'delimiter.handlebars'],\n            [\n                /\\}\\}\\}?/,\n                {\n                    token: 'delimiter.handlebars',\n                    switchTo: '@$S2.$S3',\n                    nextEmbedded: '$S3'\n                }\n            ],\n            { include: 'handlebarsRoot' }\n        ],\n        handlebarsRoot: [\n            [/\"[^\"]*\"/, 'string.handlebars'],\n            [/[#/][^\\s}]+/, 'keyword.helper.handlebars'],\n            [/else\\b/, 'keyword.helper.handlebars'],\n            [/[\\s]+/],\n            [/[^}]/, 'variable.parameter.handlebars']\n        ]\n    }\n};\n"], "mappings": ";;;;;;;AAKA,IAAI,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACO,IAAI,OAAO;AAAA,EACd,aAAa;AAAA,EACb,UAAU;AAAA,IACN,cAAc,CAAC,SAAS,MAAM;AAAA,EAClC;AAAA,EACA,UAAU;AAAA,IACN,CAAC,QAAQ,KAAK;AAAA,IACd,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,MAAM,IAAI;AAAA,IACX,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,cAAc;AAAA,IACV;AAAA,MACI,YAAY,IAAI,OAAO,YAAY,eAAe,KAAK,GAAG,IAAI,wCAAwC,GAAG;AAAA,MACzG,WAAW;AAAA,MACX,QAAQ;AAAA,QACJ,cAAc,UAAU,aAAa;AAAA,MACzC;AAAA,IACJ;AAAA,IACA;AAAA,MACI,YAAY,IAAI,OAAO,YAAY,eAAe,KAAK,GAAG,IAAI,wCAAwC,GAAG;AAAA,MACzG,QAAQ,EAAE,cAAc,UAAU,aAAa,OAAO;AAAA,IAC1D;AAAA,EACJ;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA;AAAA;AAAA,EAGd,WAAW;AAAA,IACP,MAAM;AAAA,MACF,CAAC,WAAW,kCAAkC,eAAe;AAAA,MAC7D,CAAC,SAAS,4BAA4B,UAAU;AAAA,MAChD,CAAC,QAAQ,EAAE,OAAO,YAAY,UAAU,gCAAgC,CAAC;AAAA,MACzE,CAAC,aAAa,gBAAgB,UAAU;AAAA,MACxC,CAAC,QAAQ,gBAAgB,cAAc;AAAA,MACvC,CAAC,iBAAiB,CAAC,kBAAkB,YAAY,gBAAgB,CAAC;AAAA,MAClE,CAAC,eAAe,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,UAAU,CAAC,CAAC;AAAA,MAC1E,CAAC,cAAc,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,SAAS,CAAC,CAAC;AAAA,MACxE,CAAC,eAAe,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,YAAY,CAAC,CAAC;AAAA,MAC5E,CAAC,cAAc,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,YAAY,CAAC,CAAC;AAAA,MAC3E,CAAC,KAAK,gBAAgB;AAAA,MACtB,CAAC,MAAM,gBAAgB;AAAA,MACvB,CAAC,QAAQ;AAAA;AAAA,IACb;AAAA,IACA,SAAS;AAAA,MACL;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA,CAAC,SAAS,sBAAsB;AAAA,MAChC,CAAC,KAAK,gBAAgB,MAAM;AAAA,IAChC;AAAA,IACA,SAAS;AAAA,MACL,CAAC,QAAQ,0BAA0B,MAAM;AAAA,MACzC,CAAC,KAAK,4BAA4B;AAAA,IACtC;AAAA,IACA,cAAc;AAAA,MACV,CAAC,UAAU,gCAAgC,MAAM;AAAA,MACjD,CAAC,KAAK,4BAA4B;AAAA,IACtC;AAAA,IACA,aAAa;AAAA,MACT;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA,CAAC,OAAO,gBAAgB,MAAM;AAAA,MAC9B,CAAC,SAAS,sBAAsB;AAAA,MAChC,CAAC,KAAK,sBAAsB;AAAA,IAChC;AAAA,IACA,UAAU;AAAA,MACN;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA,CAAC,QAAQ,kBAAkB,MAAM;AAAA,MACjC,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY;AAAA;AAAA,IACjB;AAAA;AAAA;AAAA,IAGA,QAAQ;AAAA,MACJ;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA,CAAC,QAAQ,kBAAkB,kBAAkB;AAAA,MAC7C,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb;AAAA,QACI;AAAA,QACA,CAAC,kBAAkB,YAAY,EAAE,OAAO,kBAAkB,MAAM,OAAO,CAAC;AAAA,MAC5E;AAAA,IACJ;AAAA;AAAA,IAEA,iBAAiB;AAAA,MACb;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA,CAAC,KAAK,aAAa,wBAAwB;AAAA,MAC3C;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACzD;AAAA;AAAA,IAEA,uBAAuB;AAAA,MACnB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACzD;AAAA;AAAA,IAEA,sBAAsB;AAAA,MAClB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY;AAAA,MACb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACzD;AAAA,IACA,gBAAgB;AAAA,MACZ;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,UACV,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,aAAa,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,IAC3E;AAAA;AAAA;AAAA;AAAA,IAIA,OAAO;AAAA,MACH;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA,CAAC,QAAQ,kBAAkB,iBAAiB;AAAA,MAC5C,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb;AAAA,QACI;AAAA,QACA,CAAC,kBAAkB,YAAY,EAAE,OAAO,kBAAkB,MAAM,OAAO,CAAC;AAAA,MAC5E;AAAA,IACJ;AAAA;AAAA,IAEA,gBAAgB;AAAA,MACZ;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA,CAAC,KAAK,aAAa,uBAAuB;AAAA,MAC1C;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACxD;AAAA;AAAA,IAEA,sBAAsB;AAAA,MAClB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACxD;AAAA;AAAA,IAEA,qBAAqB;AAAA,MACjB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY;AAAA,MACb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACxD;AAAA,IACA,eAAe;AAAA,MACX;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,UACV,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,IAC1E;AAAA;AAAA,IAEA,yBAAyB;AAAA,MACrB,CAAC,WAAW,sBAAsB;AAAA,MAClC,CAAC,WAAW,EAAE,OAAO,wBAAwB,UAAU,WAAW,CAAC;AAAA,MACnE,EAAE,SAAS,iBAAiB;AAAA,IAChC;AAAA,IACA,2BAA2B;AAAA,MACvB,CAAC,WAAW,sBAAsB;AAAA,MAClC;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,UACV,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,EAAE,SAAS,iBAAiB;AAAA,IAChC;AAAA,IACA,gBAAgB;AAAA,MACZ,CAAC,WAAW,mBAAmB;AAAA,MAC/B,CAAC,eAAe,2BAA2B;AAAA,MAC3C,CAAC,UAAU,2BAA2B;AAAA,MACtC,CAAC,OAAO;AAAA,MACR,CAAC,QAAQ,+BAA+B;AAAA,IAC5C;AAAA,EACJ;AACJ;", "names": []}