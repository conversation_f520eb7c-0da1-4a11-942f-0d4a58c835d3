{"version": 3, "sources": ["../../echarts-stat/dist/ecStat.js", "../../echarts-stat/index.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ecStat\"] = factory();\n\telse\n\t\troot[\"ecStat\"] = factory();\n})(this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId])\n/******/ \t\t\treturn installedModules[moduleId].exports;\n\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\texports: {},\n/******/ \t\t\tid: moduleId,\n/******/ \t\t\tloaded: false\n/******/ \t\t};\n\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.loaded = true;\n\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n\n\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(0);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;!(__WEBPACK_AMD_DEFINE_RESULT__ = function (require) {\n\n\t    return {\n\n\t        clustering: __webpack_require__(1),\n\t        regression: __webpack_require__(5),\n\t        statistics: __webpack_require__(6),\n\t        histogram: __webpack_require__(15),\n\n\t        transform: {\n\t            regression: __webpack_require__(18),\n\t            histogram: __webpack_require__(21),\n\t            clustering: __webpack_require__(22)\n\t        }\n\n\t    };\n\n\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;!(__WEBPACK_AMD_DEFINE_RESULT__ = function (require) {\n\n\t    var dataProcess = __webpack_require__(2);\n\t    var dataPreprocess = dataProcess.dataPreprocess;\n\t    var normalizeDimensions = dataProcess.normalizeDimensions;\n\t    var arrayUtil = __webpack_require__(3);\n\t    var numberUtil = __webpack_require__(4);\n\t    var arraySize = arrayUtil.size;\n\t    var sumOfColumn = arrayUtil.sumOfColumn;\n\t    var arraySum = arrayUtil.sum;\n\t    var zeros = arrayUtil.zeros;\n\t    // var isArray = arrayUtil.isArray;\n\t    var numberUtil = __webpack_require__(4);\n\t    var isNumber = numberUtil.isNumber;\n\t    var mathPow = Math.pow;\n\n\t    var OutputType = {\n\t        /**\n\t         * Data are all in one. Cluster info are added as an attribute of data.\n\t         * ```ts\n\t         * type OutputDataSingle = {\n\t         *     // Each index of `data` is the index of the input data.\n\t         *     data: OutputDataItem[];\n\t         *     // The index of `centroids` is the cluster index.\n\t         *     centroids: [ValueOnX, ValueOnY][];\n\t         * };\n\t         * type InputDataItem = (ValueOnX | ValueOnY | OtherValue)[];\n\t         * type OutputDataItem = (...InputDataItem | ClusterIndex | SquareDistanceToCentroid)[];\n\t         * ```\n\t         */\n\t        SINGLE: 'single',\n\t        /**\n\t         * Data are separated by cluster. Suitable for retrieving data form each cluster.\n\t         * ```ts\n\t         * type OutputDataMultiple = {\n\t         *     // Each index of `clusterAssment` is the index of the input data.\n\t         *     clusterAssment: [ClusterIndex, SquareDistanceToCentroid][];\n\t         *     // The index of `centroids` is the cluster index.\n\t         *     centroids: [ValueOnX, ValueOnY][];\n\t         *     // The index of `pointsInCluster` is the cluster index.\n\t         *     pointsInCluster: DataItemListInOneCluster[];\n\t         * }\n\t         * type DataItemListInOneCluster = InputDataItem[];\n\t         * type InputDataItem = (ValueOnX | ValueOnY | OtherValue)[];\n\t         * type SquareDistanceToCentroid = number;\n\t         * type ClusterIndex = number;\n\t         * type ValueOnX = number;\n\t         * type ValueOnY = number;\n\t         * type OtherValue = unknown;\n\t         * ```\n\t         */\n\t        MULTIPLE: 'multiple'\n\t    }\n\n\t    /**\n\t     * KMeans of clustering algorithm.\n\t     * @param {Array.<Array.<number>>} data two-dimension array\n\t     * @param {number} k the number of clusters in a dataset\n\t     * @return {Object}\n\t     */\n\t    function kMeans(data, k, dataMeta) {\n\n\t        // create array to assign data points to centroids, also holds SE of each point\n\t        var clusterAssigned = zeros(data.length, 2);\n\t        var centroids = createRandCent(k, calcExtents(data, dataMeta.dimensions));\n\t        var clusterChanged = true;\n\t        var minDist;\n\t        var minIndex;\n\t        var distIJ;\n\t        var ptsInClust;\n\n\t        while (clusterChanged) {\n\t            clusterChanged = false;\n\t            for (var i = 0; i < data.length; i++) {\n\t                minDist = Infinity;\n\t                minIndex = -1;\n\t                for (var j = 0; j < k; j++) {\n\t                    distIJ = distEuclid(data[i], centroids[j], dataMeta);\n\t                    if (distIJ < minDist) {\n\t                        minDist = distIJ;\n\t                        minIndex = j;\n\t                    }\n\t                }\n\t                if (clusterAssigned[i][0] !== minIndex) {\n\t                    clusterChanged = true;\n\t                }\n\t                clusterAssigned[i][0] = minIndex;\n\t                clusterAssigned[i][1] = minDist;\n\t            }\n\t            //recalculate centroids\n\t            for (var i = 0; i < k; i++) {\n\t                ptsInClust = [];\n\t                for (var j = 0; j < clusterAssigned.length; j++) {\n\t                    if (clusterAssigned[j][0] === i) {\n\t                        ptsInClust.push(data[j]);\n\t                    }\n\t                }\n\t                centroids[i] = meanInColumns(ptsInClust, dataMeta);\n\t            }\n\t        }\n\n\t        var clusterWithKmeans = {\n\t            centroids: centroids,\n\t            clusterAssigned: clusterAssigned\n\t        };\n\t        return clusterWithKmeans;\n\t    }\n\n\t    /**\n\t     * Calculate the average of each column in a two-dimensional array\n\t     * and returns the values as an array.\n\t     */\n\t    function meanInColumns(dataList, dataMeta) {\n\t        var meanArray = [];\n\t        var sum;\n\t        var mean;\n\t        for (var j = 0; j < dataMeta.dimensions.length; j++) {\n\t            var dimIdx = dataMeta.dimensions[j];\n\t            sum = 0;\n\t            for (var i = 0; i < dataList.length; i++) {\n\t                sum += dataList[i][dimIdx];\n\t            }\n\t            mean = sum / dataList.length;\n\t            meanArray.push(mean);\n\t        }\n\t        return meanArray;\n\t    }\n\n\t    /**\n\t     * The combine of hierarchical clustering and k-means.\n\t     * @param {Array} data two-dimension array.\n\t     * @param {Object|number} [clusterCountOrConfig] config or clusterCountOrConfig.\n\t     * @param {number} clusterCountOrConfig.clusterCount Mandatory.\n\t     *        The number of clusters in a dataset. It has to be greater than 1.\n\t     * @param {boolean} [clusterCountOrConfig.stepByStep=false] Optional.\n\t     * @param {OutputType} [clusterCountOrConfig.outputType='multiple'] Optional.\n\t     *        See `OutputType`.\n\t     * @param {number} [clusterCountOrConfig.outputClusterIndexDimension] Mandatory.\n\t     *        Only work in `OutputType.SINGLE`.\n\t     * @param {number} [clusterCountOrConfig.outputCentroidDimensions] Optional.\n\t     *        If specified, the centroid will be set to those dimensions of the result data one by one.\n\t     *        By default not set centroid to result.\n\t     *        Only work in `OutputType.SINGLE`.\n\t     * @param {Array.<number>} [clusterCountOrConfig.dimensions] Optional.\n\t     *        Target dimensions to calculate the regression.\n\t     *        By default: use all of the data.\n\t     * @return {Array} See `OutputType`.\n\t     */\n\t    function hierarchicalKMeans(data, clusterCountOrConfig, stepByStep) {\n\t        var config = (\n\t            isNumber(clusterCountOrConfig)\n\t                ? {clusterCount: clusterCountOrConfig, stepByStep: stepByStep}\n\t                : clusterCountOrConfig\n\t        ) || {clusterCount: 2};\n\n\t        var k = config.clusterCount;\n\n\t        if (k < 2) {\n\t            return;\n\t        }\n\n\t        var dataMeta = parseDataMeta(data, config);\n\t        var isOutputTypeSingle = dataMeta.outputType === OutputType.SINGLE;\n\n\t        var dataSet = dataPreprocess(data, {dimensions: dataMeta.dimensions});\n\n\t        var clusterAssment = zeros(dataSet.length, 2);\n\t        var outputSingleData;\n\t        var setClusterIndex;\n\t        var getClusterIndex;\n\n\t        function setDistance(dataIndex, dist) {\n\t            clusterAssment[dataIndex][1] = dist;\n\t        }\n\t        function getDistance(dataIndex) {\n\t            return clusterAssment[dataIndex][1];\n\t        };\n\n\t        if (isOutputTypeSingle) {\n\t            outputSingleData = [];\n\t            var outputClusterIndexDimension = dataMeta.outputClusterIndexDimension;\n\n\t            setClusterIndex = function (dataIndex, clusterIndex) {\n\t                outputSingleData[dataIndex][outputClusterIndexDimension] = clusterIndex;\n\t            };\n\t            getClusterIndex = function (dataIndex) {\n\t                return outputSingleData[dataIndex][outputClusterIndexDimension];\n\t            };\n\n\t            for (var i = 0; i < dataSet.length; i++) {\n\t                outputSingleData.push(dataSet[i].slice());\n\t                setDistance(i, 0);\n\t                setClusterIndex(i, 0);\n\t            }\n\t        }\n\t        else {\n\t            setClusterIndex = function (dataIndex, clusterIndex) {\n\t                clusterAssment[dataIndex][0] = clusterIndex;\n\t            };\n\t            getClusterIndex = function (dataIndex) {\n\t                return clusterAssment[dataIndex][0];\n\t            };\n\t        }\n\n\t        // initial center point.\n\t        var centroid0 = meanInColumns(dataSet, dataMeta);\n\t        var centList = [centroid0];\n\t        for (var i = 0; i < dataSet.length; i++) {\n\t            var dist = distEuclid(dataSet[i], centroid0, dataMeta);\n\t            setDistance(i, dist);\n\t        }\n\n\t        var lowestSSE;\n\t        var ptsInClust;\n\t        var ptsNotClust;\n\t        var clusterInfo;\n\t        var sseSplit;\n\t        var sseNotSplit;\n\t        var index = 1;\n\t        var result = {\n\t            data: outputSingleData,\n\t            centroids: centList,\n\t            isEnd: false\n\t        };\n\t        if (!isOutputTypeSingle) {\n\t            // Only for backward compat.\n\t            result.clusterAssment = clusterAssment;\n\t        }\n\n\t        function oneStep() {\n\t            //the existing clusters are continuously divided\n\t            //until the number of clusters is k\n\t            if (index < k) {\n\t                lowestSSE = Infinity;\n\t                var centSplit;\n\t                var newCentroid;\n\t                var newClusterAss;\n\n\t                for (var j = 0; j < centList.length; j++) {\n\t                    ptsInClust = [];\n\t                    ptsNotClust = [];\n\t                    for (var i = 0; i < dataSet.length; i++) {\n\t                        if (getClusterIndex(i) === j) {\n\t                            ptsInClust.push(dataSet[i]);\n\t                        }\n\t                        else {\n\t                            ptsNotClust.push(getDistance(i));\n\t                        }\n\t                    }\n\t                    clusterInfo = kMeans(ptsInClust, 2, dataMeta);\n\t                    sseSplit = sumOfColumn(clusterInfo.clusterAssigned, 1);\n\t                    sseNotSplit = arraySum(ptsNotClust);\n\t                    if (sseSplit + sseNotSplit < lowestSSE) {\n\t                        lowestSSE = sseNotSplit + sseSplit;\n\t                        centSplit = j;\n\t                        newCentroid = clusterInfo.centroids;\n\t                        newClusterAss = clusterInfo.clusterAssigned;\n\t                    }\n\t                }\n\n\t                for (var i = 0; i < newClusterAss.length; i++) {\n\t                    if (newClusterAss[i][0] === 0) {\n\t                        newClusterAss[i][0] = centSplit;\n\t                    }\n\t                    else if (newClusterAss[i][0] === 1) {\n\t                        newClusterAss[i][0] = centList.length;\n\t                    }\n\t                }\n\n\t                centList[centSplit] = newCentroid[0];\n\t                centList.push(newCentroid[1]);\n\t                for (var i = 0, j = 0; i < dataSet.length && j < newClusterAss.length; i++) {\n\t                    if (getClusterIndex(i) === centSplit) {\n\t                        setClusterIndex(i, newClusterAss[j][0]);\n\t                        setDistance(i, newClusterAss[j++][1]);\n\t                    }\n\t                }\n\n\t                var pointInClust = [];\n\t                if (!isOutputTypeSingle) {\n\t                    for (var i = 0; i < centList.length; i++) {\n\t                        pointInClust[i] = [];\n\t                        for (var j = 0; j < dataSet.length; j++) {\n\t                            if (getClusterIndex(j) === i) {\n\t                                pointInClust[i].push(dataSet[j]);\n\t                            }\n\t                        }\n\t                    }\n\t                    result.pointsInCluster = pointInClust;\n\t                }\n\n\t                index++;\n\t            }\n\t            else {\n\t                result.isEnd = true;\n\t            }\n\t        }\n\n\t        if (!config.stepByStep) {\n\t            while (oneStep(), !result.isEnd);\n\t        }\n\t        else {\n\t            result.next = function () {\n\t                oneStep();\n\t                setCentroidToResultData(result, dataMeta);\n\t                return result;\n\t            };\n\t        }\n\t        setCentroidToResultData(result, dataMeta);\n\t        return result;\n\t    }\n\n\t    function setCentroidToResultData(result, dataMeta) {\n\t        var outputCentroidDimensions = dataMeta.outputCentroidDimensions;\n\t        if (dataMeta.outputType !== OutputType.SINGLE || outputCentroidDimensions == null) {\n\t            return;\n\t        }\n\t        var outputSingleData = result.data;\n\t        var centroids = result.centroids;\n\n\t        for (var i = 0; i < outputSingleData.length; i++) {\n\t            var line = outputSingleData[i];\n\t            var clusterIndex = line[dataMeta.outputClusterIndexDimension];\n\t            var centroid = centroids[clusterIndex];\n\t            var dimLen = Math.min(centroid.length, outputCentroidDimensions.length);\n\t            for (var j = 0; j < dimLen; j++) {\n\t                line[outputCentroidDimensions[j]] = centroid[j];\n\t            }\n\t        }\n\t    }\n\n\t    /**\n\t     * Create random centroid of kmeans.\n\t     */\n\t    function createRandCent(k, extents) {\n\t        //constructs a two-dimensional array with all values 0\n\t        var centroids = zeros(k, extents.length);\n\t        //create random cluster centers, within bounds of each dimension\n\t        for (var j = 0; j < extents.length; j++) {\n\t            var extentItem = extents[j];\n\t            for (var i = 0; i < k; i++) {\n\t                centroids[i][j] = extentItem.min + extentItem.span * Math.random();\n\t            }\n\t        }\n\t        return centroids;\n\t    }\n\n\t    /**\n\t     * Distance method for calculating similarity\n\t     */\n\t    function distEuclid(dataItem, centroid, dataMeta) {\n\t        // The distance should be normalized between different dimensions,\n\t        // otherwise they may provide different weight in the final distance.\n\t        // The greater weight offers more effect in the cluster determination.\n\n\t        var powerSum = 0;\n\t        var dimensions = dataMeta.dimensions;\n\t        var extents = dataMeta.rawExtents;\n\t        //subtract the corresponding elements in the vectors\n\t        for (var i = 0; i < dimensions.length; i++) {\n\t            var span = extents[i].span;\n\t            // If span is 0, do not count.\n\t            if (span) {\n\t                var dimIdx = dimensions[i];\n\t                var dist = (dataItem[dimIdx] - centroid[i]) / span;\n\t                powerSum += mathPow(dist, 2);\n\t            }\n\t        }\n\n\t        return powerSum;\n\t    }\n\n\t    function parseDataMeta(dataSet, config) {\n\t        var size = arraySize(dataSet);\n\t        if (size.length < 1) {\n\t            throw new Error('The input data of clustering should be two-dimension array.');\n\t        }\n\t        var colCount = size[1];\n\t        var defaultDimensions = [];\n\t        for (var i = 0; i < colCount; i++) {\n\t            defaultDimensions.push(i);\n\t        }\n\t        var dimensions = normalizeDimensions(config.dimensions, defaultDimensions);\n\t        var outputType = config.outputType || OutputType.MULTIPLE;\n\n\t        var outputClusterIndexDimension = config.outputClusterIndexDimension;\n\t        if (outputType === OutputType.SINGLE && !numberUtil.isNumber(outputClusterIndexDimension)) {\n\t            throw new Error('outputClusterIndexDimension is required as a number.');\n\t        }\n\t        var extents = calcExtents(dataSet, dimensions);\n\n\t        return {\n\t            dimensions: dimensions,\n\t            rawExtents: extents,\n\t            outputType: outputType,\n\t            outputClusterIndexDimension: outputClusterIndexDimension,\n\t            outputCentroidDimensions: config.outputCentroidDimensions,\n\t        };\n\t    }\n\n\t    function calcExtents(dataSet, dimensions) {\n\t        var extents = [];\n\t        var dimLen = dimensions.length;\n\t        for (var i = 0; i < dimLen; i++) {\n\t            extents.push({ min: Infinity, max: -Infinity });\n\t        }\n\t        for (var i = 0; i < dataSet.length; i++) {\n\t            var line = dataSet[i];\n\t            for (var j = 0; j < dimLen; j++) {\n\t                var extentItem = extents[j];\n\t                var val = line[dimensions[j]];\n\t                extentItem.min > val && (extentItem.min = val);\n\t                extentItem.max < val && (extentItem.max = val);\n\t            }\n\t        }\n\t        for (var i = 0; i < dimLen; i++) {\n\t            extents[i].span = extents[i].max - extents[i].min;\n\t        }\n\t        return extents;\n\t    }\n\n\t    return {\n\t        OutputType: OutputType,\n\t        hierarchicalKMeans: hierarchicalKMeans\n\t    };\n\n\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;!(__WEBPACK_AMD_DEFINE_RESULT__ = function (require) {\n\n\t    var array = __webpack_require__(3);\n\t    var isArray = array.isArray;\n\t    var size = array.size;\n\t    var number = __webpack_require__(4);\n\t    var isNumber = number.isNumber;\n\n\t    /**\n\t     * @param  {Array.<number>|number} dimensions like `[2, 4]` or `4`\n\t     * @param  {Array.<number>} [defaultDimensions=undefined] By default `undefined`.\n\t     * @return {Array.<number>} number like `4` is normalized to `[4]`,\n\t     *         `null`/`undefined` is normalized to `defaultDimensions`.\n\t     */\n\t    function normalizeDimensions(dimensions, defaultDimensions) {\n\t        return typeof dimensions === 'number'\n\t            ? [dimensions]\n\t            : dimensions == null\n\t            ? defaultDimensions\n\t            : dimensions;\n\t    }\n\n\t    /**\n\t     * Data preprocessing, filter the wrong data object.\n\t     *  for example [12,] --- missing y value\n\t     *              [,12] --- missing x value\n\t     *              [12, b] --- incorrect y value\n\t     *              ['a', 12] --- incorrect x value\n\t     * @param  {Array.<Array>} data\n\t     * @param  {Object?} [opt]\n\t     * @param  {Array.<number>} [opt.dimensions] Optional. Like [2, 4],\n\t     *         means that dimension index 2 and dimension index 4 need to be number.\n\t     *         If null/undefined (by default), all dimensions need to be number.\n\t     * @param  {boolean} [opt.toOneDimensionArray] Convert to one dimension array.\n\t     *         Each value is from `opt.dimensions[0]` or dimension 0.\n\t     * @return {Array.<Array.<number>>}\n\t     */\n\t    function dataPreprocess(data, opt) {\n\t        opt = opt || {};\n\t        var dimensions = opt.dimensions;\n\t        var numberDimensionMap = {};\n\t        if (dimensions != null) {\n\t            for (var i = 0; i < dimensions.length; i++) {\n\t                numberDimensionMap[dimensions[i]] = true;\n\t            }\n\t        }\n\t        var targetOneDim = opt.toOneDimensionArray\n\t            ? (dimensions ? dimensions[0] : 0)\n\t            : null;\n\n\t        function shouldBeNumberDimension(dimIdx) {\n\t            return !dimensions || numberDimensionMap.hasOwnProperty(dimIdx);\n\t        }\n\n\t        if (!isArray(data)) {\n\t            throw new Error('Invalid data type, you should input an array');\n\t        }\n\t        var predata = [];\n\t        var arraySize = size(data);\n\n\t        if (arraySize.length === 1) {\n\t            for (var i = 0; i < arraySize[0]; i++) {\n\t                var item = data[i];\n\t                if (isNumber(item)) {\n\t                    predata.push(item);\n\t                }\n\t            }\n\t        }\n\t        else if (arraySize.length === 2) {\n\t            for (var i = 0; i < arraySize[0]; i++) {\n\t                var isCorrect = true;\n\t                var item = data[i];\n\t                for (var j = 0; j < arraySize[1]; j++) {\n\t                    if (shouldBeNumberDimension(j) && !isNumber(item[j])) {\n\t                        isCorrect = false;\n\t                    }\n\t                }\n\t                if (isCorrect) {\n\t                    predata.push(\n\t                        targetOneDim != null\n\t                            ? item[targetOneDim]\n\t                            : item\n\t                    );\n\t                }\n\t            }\n\t        }\n\t        return predata;\n\t    }\n\n\t    /**\n\t     * @param {string|number} val\n\t     * @return {number}\n\t     */\n\t    function getPrecision(val) {\n\t        var str = val.toString();\n\t        // scientific notation is not considered\n\t        var dotIndex = str.indexOf('.');\n\t        return dotIndex < 0 ? 0 : str.length - 1 - dotIndex;\n\t    }\n\n\t    return {\n\t        normalizeDimensions: normalizeDimensions,\n\t        dataPreprocess: dataPreprocess,\n\t        getPrecision: getPrecision\n\t    };\n\n\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;!(__WEBPACK_AMD_DEFINE_RESULT__ = function (require) {\n\n\t    var objToString = Object.prototype.toString;\n\t    var arrayProto = Array.prototype;\n\t    var nativeMap = arrayProto.map;\n\n\t    /**\n\t     * Get the size of a array\n\t     * @param  {Array} data\n\t     * @return {Array}\n\t     */\n\t    function size(data) {\n\t        var s = [];\n\t        while (isArray(data)) {\n\t            s.push(data.length);\n\t            data = data[0];\n\t        }\n\t        return s;\n\t    }\n\n\t    /**\n\t     * @param {*}  value\n\t     * @return {boolean}\n\t     */\n\t    function isArray(value) {\n\t        return objToString.call(value) === '[object Array]';\n\t    }\n\n\t    /**\n\t     * constructs a (m x n) array with all values 0\n\t     * @param  {number} m  the row\n\t     * @param  {number} n  the column\n\t     * @return {Array}\n\t     */\n\t    function zeros(m, n) {\n\t        var zeroArray = [];\n\t        for (var i = 0; i < m ; i++) {\n\t            zeroArray[i] = [];\n\t            for (var j = 0; j < n; j++) {\n\t                zeroArray[i][j] = 0;\n\t            }\n\t        }\n\t        return zeroArray;\n\t    }\n\n\t    /**\n\t     * Sums each element in the array.\n\t     * Internal use, for performance considerations, to avoid\n\t     * unnecessary judgments and calculations.\n\t     * @param  {Array} vector\n\t     * @return {number}\n\t     */\n\t    function sum(vector) {\n\t        var sum = 0;\n\t        for (var i = 0; i < vector.length; i++) {\n\t            sum += vector[i];\n\t        }\n\t        return sum;\n\t    }\n\n\t    /**\n\t     * Computes the sum of the specified column elements in a two-dimensional array\n\t     * @param  {Array.<Array>} dataList  two-dimensional array\n\t     * @param  {number} n  the specified column, zero-based\n\t     * @return {number}\n\t     */\n\t    function sumOfColumn(dataList, n) {\n\t        var sum = 0;\n\t        for (var i = 0; i < dataList.length; i++) {\n\t            sum += dataList[i][n];\n\t        }\n\t        return sum;\n\t    }\n\n\n\t    function ascending(a, b) {\n\n\t        return a > b ? 1 : a < b ? -1 : a === b ? 0 : NaN;\n\n\t    }\n\n\t    /**\n\t     * Binary search algorithm --- this bisector is specidfied to histogram, which every bin like that [a, b),\n\t     * so the return value use to add 1.\n\t     * @param  {Array.<number>} array\n\t     * @param  {number} value\n\t     * @param  {number} start\n\t     * @param  {number} end\n\t     * @return {number}\n\t     */\n\t    function bisect(array, value, start, end) { //移出去\n\n\t        if (start == null) {\n\t            start = 0;\n\t        }\n\t        if (end == null) {\n\t            end = array.length;\n\t        }\n\t        while (start < end) {\n\t            var mid = Math.floor((start + end) / 2);\n\t            var compare = ascending(array[mid], value);\n\t            if (compare > 0) {\n\t                end = mid;\n\t            }\n\t            else if (compare < 0) {\n\t                start = mid + 1;\n\t            }\n\t            else {\n\t                return mid + 1;\n\t            }\n\t        }\n\t        return start;\n\t    }\n\n\t    /**\n\t     * 数组映射\n\t     * @memberOf module:zrender/core/util\n\t     * @param {Array} obj\n\t     * @param {Function} cb\n\t     * @param {*} [context]\n\t     * @return {Array}\n\t     */\n\t    function map(obj, cb, context) {\n\t        if (!(obj && cb)) {\n\t            return;\n\t        }\n\t        if (obj.map && obj.map === nativeMap) {\n\t            return obj.map(cb, context);\n\t        }\n\t        else {\n\t            var result = [];\n\t            for (var i = 0, len = obj.length; i < len; i++) {\n\t                result.push(cb.call(context, obj[i], i, obj));\n\t            }\n\t            return result;\n\t        }\n\t    }\n\n\t    return {\n\t        size: size,\n\t        isArray: isArray,\n\t        zeros: zeros,\n\t        sum: sum,\n\t        sumOfColumn: sumOfColumn,\n\t        ascending: ascending,\n\t        bisect: bisect,\n\t        map: map\n\t    };\n\n\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;!(__WEBPACK_AMD_DEFINE_RESULT__ = function (require) {\n\n\t    /**\n\t     * Test whether value is a number.\n\t     * @param  {*}  value\n\t     * @return {boolean}\n\t     */\n\t    function isNumber(value) {\n\n\t        value = value === null ? NaN : +value;\n\t        return typeof value === 'number' && !isNaN(value);\n\t    }\n\n\t    /**\n\t     * Test if a number is integer.\n\t     * @param  {number}  value\n\t     * @return {boolean}\n\t     */\n\t    function isInteger(value) {\n\t        return isFinite(value) && value === Math.round(value);\n\t    }\n\n\t    function quantityExponent(val) {\n\t        if (val === 0) {\n\t            return 0;\n\t        }\n\t        var exp = Math.floor(Math.log(val) / Math.LN10);\n\t        // Fix pricision loss.\n\t        if (val / Math.pow(10, exp) >= 10) {\n\t            exp++;\n\t        }\n\t        return exp;\n\t    }\n\n\t    return {\n\t        isNumber: isNumber,\n\t        isInteger: isInteger,\n\t        quantityExponent: quantityExponent\n\t    };\n\n\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;!(__WEBPACK_AMD_DEFINE_RESULT__ = function (require) {\n\n\t    var dataProcess = __webpack_require__(2);\n\t    var dataPreprocess = dataProcess.dataPreprocess;\n\t    var normalizeDimensions = dataProcess.normalizeDimensions;\n\n\t    var regreMethods = {\n\n\t        /**\n\t         * Common linear regression algorithm\n\t         */\n\t        linear: function (predata, opt) {\n\n\t            var xDimIdx = opt.dimensions[0];\n\t            var yDimIdx = opt.dimensions[1];\n\t            var sumX = 0;\n\t            var sumY = 0;\n\t            var sumXY = 0;\n\t            var sumXX = 0;\n\t            var len = predata.length;\n\n\t            for (var i = 0; i < len; i++) {\n\t                var rawItem = predata[i];\n\t                sumX += rawItem[xDimIdx];\n\t                sumY += rawItem[yDimIdx];\n\t                sumXY += rawItem[xDimIdx] * rawItem[yDimIdx];\n\t                sumXX += rawItem[xDimIdx] * rawItem[xDimIdx];\n\t            }\n\n\t            var gradient = ((len * sumXY) - (sumX * sumY)) / ((len * sumXX) - (sumX * sumX));\n\t            var intercept = (sumY / len) - ((gradient * sumX) / len);\n\n\t            var result = [];\n\t            for (var j = 0; j < predata.length; j++) {\n\t                var rawItem = predata[j];\n\t                var resultItem = rawItem.slice();\n\t                resultItem[xDimIdx] = rawItem[xDimIdx];\n\t                resultItem[yDimIdx] = gradient * rawItem[xDimIdx] + intercept;\n\t                result.push(resultItem);\n\t            }\n\n\t            var expression = 'y = ' + Math.round(gradient * 100) / 100 + 'x + ' + Math.round(intercept * 100) / 100;\n\n\t            return {\n\t                points: result,\n\t                parameter: {\n\t                    gradient: gradient,\n\t                    intercept: intercept\n\t                },\n\t                expression: expression\n\t            };\n\t        },\n\n\t        /**\n\t         * If the raw data include [0,0] point, we should choose linearThroughOrigin\n\t         *   instead of linear.\n\t         */\n\t        linearThroughOrigin: function (predata, opt) {\n\n\t            var xDimIdx = opt.dimensions[0];\n\t            var yDimIdx = opt.dimensions[1];\n\t            var sumXX = 0;\n\t            var sumXY = 0;\n\n\t            for (var i = 0; i < predata.length; i++) {\n\t                var rawItem = predata[i];\n\t                sumXX += rawItem[xDimIdx] * rawItem[xDimIdx];\n\t                sumXY += rawItem[xDimIdx] * rawItem[yDimIdx];\n\t            }\n\n\t            var gradient = sumXY / sumXX;\n\t            var result = [];\n\n\t            for (var j = 0; j < predata.length; j++) {\n\t                var rawItem = predata[j];\n\t                var resultItem = rawItem.slice();\n\t                resultItem[xDimIdx] = rawItem[xDimIdx];\n\t                resultItem[yDimIdx] = rawItem[xDimIdx] * gradient;\n\t                result.push(resultItem);\n\t            }\n\n\t            var expression = 'y = ' + Math.round(gradient * 100) / 100 + 'x';\n\n\t            return {\n\t                points: result,\n\t                parameter: {\n\t                    gradient: gradient\n\t                },\n\t                expression: expression\n\t            };\n\t        },\n\n\t        /**\n\t         * Exponential regression\n\t         */\n\t        exponential: function (predata, opt) {\n\n\t            var xDimIdx = opt.dimensions[0];\n\t            var yDimIdx = opt.dimensions[1];\n\t            var sumX = 0;\n\t            var sumY = 0;\n\t            var sumXXY = 0;\n\t            var sumYlny = 0;\n\t            var sumXYlny = 0;\n\t            var sumXY = 0;\n\n\t            for (var i = 0; i < predata.length; i++) {\n\t                var rawItem = predata[i];\n\t                sumX += rawItem[xDimIdx];\n\t                sumY += rawItem[yDimIdx];\n\t                sumXY += rawItem[xDimIdx] * rawItem[yDimIdx];\n\t                sumXXY += rawItem[xDimIdx] * rawItem[xDimIdx] * rawItem[yDimIdx];\n\t                sumYlny += rawItem[yDimIdx] * Math.log(rawItem[yDimIdx]);\n\t                sumXYlny += rawItem[xDimIdx] * rawItem[yDimIdx] * Math.log(rawItem[yDimIdx]);\n\t            }\n\n\t            var denominator = (sumY * sumXXY) - (sumXY * sumXY);\n\t            var coefficient = Math.pow(Math.E, (sumXXY * sumYlny - sumXY * sumXYlny) / denominator);\n\t            var index = (sumY * sumXYlny - sumXY * sumYlny) / denominator;\n\t            var result = [];\n\n\t            for (var j = 0; j < predata.length; j++) {\n\t                var rawItem = predata[j];\n\t                var resultItem = rawItem.slice();\n\t                resultItem[xDimIdx] = rawItem[xDimIdx];\n\t                resultItem[yDimIdx] = coefficient * Math.pow(Math.E, index * rawItem[xDimIdx]);\n\t                result.push(resultItem);\n\t            }\n\n\t            var expression = 'y = ' + Math.round(coefficient * 100) / 100 + 'e^(' + Math.round(index * 100) / 100 + 'x)';\n\n\t            return {\n\t                points: result,\n\t                parameter: {\n\t                    coefficient: coefficient,\n\t                    index: index\n\t                },\n\t                expression: expression\n\t            };\n\n\t        },\n\n\t        /**\n\t         * Logarithmic regression\n\t         */\n\t        logarithmic: function (predata, opt) {\n\n\t            var xDimIdx = opt.dimensions[0];\n\t            var yDimIdx = opt.dimensions[1];\n\t            var sumlnx = 0;\n\t            var sumYlnx = 0;\n\t            var sumY = 0;\n\t            var sumlnxlnx = 0;\n\n\t            for (var i = 0; i < predata.length; i++) {\n\t                var rawItem = predata[i];\n\t                sumlnx += Math.log(rawItem[xDimIdx]);\n\t                sumYlnx += rawItem[yDimIdx] * Math.log(rawItem[xDimIdx]);\n\t                sumY += rawItem[yDimIdx];\n\t                sumlnxlnx += Math.pow(Math.log(rawItem[xDimIdx]), 2);\n\t            }\n\n\t            var gradient = (i * sumYlnx - sumY * sumlnx) / (i * sumlnxlnx - sumlnx * sumlnx);\n\t            var intercept = (sumY - gradient * sumlnx) / i;\n\t            var result = [];\n\n\t            for (var j = 0; j < predata.length; j++) {\n\t                var rawItem = predata[j];\n\t                var resultItem = rawItem.slice();\n\t                resultItem[xDimIdx] = rawItem[xDimIdx];\n\t                resultItem[yDimIdx] = gradient * Math.log(rawItem[xDimIdx]) + intercept;\n\t                result.push(resultItem);\n\t            }\n\n\t            var expression =\n\t                'y = '\n\t                + Math.round(intercept * 100) / 100\n\t                + ' + '\n\t                + Math.round(gradient * 100) / 100 + 'ln(x)';\n\n\t            return {\n\t                points: result,\n\t                parameter: {\n\t                    gradient: gradient,\n\t                    intercept: intercept\n\t                },\n\t                expression: expression\n\t            };\n\n\t        },\n\n\t        /**\n\t         * Polynomial regression\n\t         */\n\t        polynomial: function (predata, opt) {\n\n\t            var xDimIdx = opt.dimensions[0];\n\t            var yDimIdx = opt.dimensions[1];\n\t            var order = opt.order;\n\n\t            if (order == null) {\n\t                order = 2;\n\t            }\n\t            //coefficient matrix\n\t            var coeMatrix = [];\n\t            var lhs = [];\n\t            var k = order + 1;\n\n\t            for (var i = 0; i < k; i++) {\n\t                var sumA = 0;\n\t                for (var n = 0; n < predata.length; n++) {\n\t                    var rawItem = predata[n];\n\t                    sumA += rawItem[yDimIdx] * Math.pow(rawItem[xDimIdx], i);\n\t                }\n\t                lhs.push(sumA);\n\n\t                var temp = [];\n\t                for (var j = 0; j < k; j++) {\n\t                    var sumB = 0;\n\t                    for (var m = 0; m < predata.length; m++) {\n\t                        sumB += Math.pow(predata[m][xDimIdx], i + j);\n\t                    }\n\t                    temp.push(sumB);\n\t                }\n\t                coeMatrix.push(temp);\n\t            }\n\t            coeMatrix.push(lhs);\n\n\t            var coeArray = gaussianElimination(coeMatrix, k);\n\n\t            var result = [];\n\n\t            for (var i = 0; i < predata.length; i++) {\n\t                var value = 0;\n\t                var rawItem = predata[i];\n\t                for (var n = 0; n < coeArray.length; n++) {\n\t                    value += coeArray[n] * Math.pow(rawItem[xDimIdx], n);\n\t                }\n\t                var resultItem = rawItem.slice();\n\t                resultItem[xDimIdx] = rawItem[xDimIdx];\n\t                resultItem[yDimIdx] = value;\n\t                result.push(resultItem);\n\t            }\n\n\t            var expression = 'y = ';\n\t            for (var i = coeArray.length - 1; i >= 0; i--) {\n\t                if (i > 1) {\n\t                    expression += Math.round(coeArray[i] * Math.pow(10, i + 1)) / Math.pow(10, i + 1) + 'x^' + i + ' + ';\n\t                }\n\t                else if (i === 1) {\n\t                    expression += Math.round(coeArray[i] * 100) / 100 + 'x' + ' + ';\n\t                }\n\t                else {\n\t                    expression += Math.round(coeArray[i] * 100) / 100;\n\t                }\n\t            }\n\n\t            return {\n\t                points: result,\n\t                parameter: coeArray,\n\t                expression: expression\n\t            };\n\n\t        }\n\n\t    };\n\n\t    /**\n\t     * Gaussian elimination\n\t     * @param  {Array.<Array.<number>>} matrix   two-dimensional number array\n\t     * @param  {number} number\n\t     * @return {Array}\n\t     */\n\t    function gaussianElimination(matrix, number) {\n\n\t        for (var i = 0; i < matrix.length - 1; i++) {\n\t            var maxColumn = i;\n\t            for (var j = i + 1; j < matrix.length - 1; j++) {\n\t                if (Math.abs(matrix[i][j]) > Math.abs(matrix[i][maxColumn])) {\n\t                    maxColumn = j;\n\t                }\n\t            }\n\t            // the matrix here is the transpose of the common Augmented matrix.\n\t            //  so the can perform the primary column transform, in fact, equivalent\n\t            //  to the primary line changes\n\t            for (var k = i; k < matrix.length; k++) {\n\t                var temp = matrix[k][i];\n\t                matrix[k][i] = matrix[k][maxColumn];\n\t                matrix[k][maxColumn] = temp;\n\t            }\n\t            for (var n = i + 1; n < matrix.length - 1; n++) {\n\t                for (var m = matrix.length - 1; m >= i; m--) {\n\t                    matrix[m][n] -= matrix[m][i] / matrix[i][i] * matrix[i][n];\n\t                }\n\t            }\n\t        }\n\n\t        var data = new Array(number);\n\t        var len = matrix.length - 1;\n\t        for (var j = matrix.length - 2; j >= 0; j--) {\n\t            var temp = 0;\n\t            for (var i = j + 1; i < matrix.length - 1; i++) {\n\t                temp += matrix[i][j] * data[i];\n\t            }\n\t            data[j] = (matrix[len][j] - temp) / matrix[j][j];\n\n\t        }\n\n\t        return data;\n\t    }\n\n\t    /**\n\t     * @param  {string} regreMethod\n\t     * @param  {Array.<Array.<number>>} data   two-dimensional number array\n\t     * @param  {Object|number} [optOrOrder]  opt or order\n\t     * @param  {number} [optOrOrder.order]  order of polynomials\n\t     * @param  {Array.<number>|number} [optOrOrder.dimensions=[0, 1]]  Target dimensions to calculate the regression.\n\t     *         By defualt: use [0, 1] as [x, y].\n\t     * @return {Array}\n\t     */\n\t    var regression = function (regreMethod, data, optOrOrder) {\n\t        var opt = typeof optOrOrder === 'number'\n\t            ? { order: optOrOrder }\n\t            : (optOrOrder || {});\n\n\t        var dimensions = normalizeDimensions(opt.dimensions, [0, 1]);\n\n\t        var predata = dataPreprocess(data, { dimensions: dimensions });\n\t        var result = regreMethods[regreMethod](predata, {\n\t            order: opt.order,\n\t            dimensions: dimensions\n\t        });\n\n\t        // Sort for line chart.\n\t        var xDimIdx = dimensions[0];\n\t        result.points.sort(function (itemA, itemB) {\n\t            return itemA[xDimIdx] - itemB[xDimIdx];\n\t        });\n\n\t        return result;\n\t    };\n\n\t    return regression;\n\n\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;!(__WEBPACK_AMD_DEFINE_RESULT__ = function (require) {\n\n\t    var statistics = {};\n\n\t    statistics.max = __webpack_require__(7);\n\t    statistics.deviation = __webpack_require__(8);\n\t    statistics.mean = __webpack_require__(10);\n\t    statistics.median = __webpack_require__(12);\n\t    statistics.min = __webpack_require__(14);\n\t    statistics.quantile = __webpack_require__(13);\n\t    statistics.sampleVariance = __webpack_require__(9);\n\t    statistics.sum = __webpack_require__(11);\n\n\t    return statistics;\n\n\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;!(__WEBPACK_AMD_DEFINE_RESULT__ = function (require) {\n\n\t    var number = __webpack_require__(4);\n\t    var isNumber = number.isNumber;\n\n\t    /**\n\t     * Is a method for computing the max value of a list of numbers,\n\t     * which will filter other data types.\n\t     * @param  {Array.<number>} data\n\t     * @return {number}\n\t     */\n\t    function max(data) {\n\n\t        var maxData = -Infinity;\n\t        for (var i = 0; i < data.length; i++) {\n\t            if (isNumber(data[i]) && data[i] > maxData) {\n\t                maxData = data[i];\n\t            }\n\t        }\n\t        return maxData;\n\t    }\n\n\t    return max;\n\n\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n/***/ }),\n/* 8 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;!(__WEBPACK_AMD_DEFINE_RESULT__ = function (require) {\n\n\t    var variance = __webpack_require__(9);\n\n\t    /**\n\t     * Computing the deviation\n\t     * @param  {Array.<number>} data\n\t     * @return {number}\n\t     */\n\t    return function (data) {\n\n\t        var squaredDeviation = variance(data);\n\n\t        return squaredDeviation ? Math.sqrt(squaredDeviation) : squaredDeviation;\n\t    };\n\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;!(__WEBPACK_AMD_DEFINE_RESULT__ = function (require) {\n\n\t    var number = __webpack_require__(4);\n\t    var isNumber = number.isNumber;\n\t    var mean = __webpack_require__(10);\n\n\t    /**\n\t     * Computing the variance of list of sample\n\t     * @param  {Array.<number>} data\n\t     * @return {number}\n\t     */\n\t    function sampleVariance(data) {\n\n\t        var len = data.length;\n\t        if (!len || len < 2) {\n\t            return 0;\n\t        }\n\t        if (data.length >= 2) {\n\n\t            var meanValue = mean(data);\n\t            var sum = 0;\n\t            var temple;\n\n\t            for (var i = 0; i < data.length; i++) {\n\t                if (isNumber(data[i])) {\n\t                    temple = data[i] - meanValue;\n\t                    sum += temple * temple;\n\t                }\n\t            }\n\t            return sum / (data.length - 1);\n\t        }\n\t    }\n\n\t    return sampleVariance;\n\n\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n\n/***/ }),\n/* 10 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;!(__WEBPACK_AMD_DEFINE_RESULT__ = function (require) {\n\n\t    var sum = __webpack_require__(11);\n\n\t    /**\n\t     * Is a method for computing the mean value of a list of numbers,\n\t     * which will filter other data types.\n\t     * @param  {Array.<number>} data\n\t     * @return {number}\n\t     */\n\t    function mean(data) {\n\n\t        var len = data.length;\n\n\t        if (!len) {\n\t            return 0;\n\t        }\n\n\t        return sum(data) / data.length;\n\n\t    }\n\n\t    return mean;\n\n\n\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n/***/ }),\n/* 11 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;!(__WEBPACK_AMD_DEFINE_RESULT__ = function (require) {\n\n\t    var number = __webpack_require__(4);\n\t    var isNumber = number.isNumber;\n\n\t    /**\n\t     * Is a method for computing the sum of a list of numbers,\n\t     * which will filter other data types.\n\t     * @param  {Array.<number>} data\n\t     * @return {number}\n\t     */\n\t    function sum(data) {\n\n\t        var len = data.length;\n\n\t        if (!len) {\n\t            return 0;\n\t        }\n\t        var sumData = 0;\n\t        for (var i = 0; i < len; i++) {\n\t            if (isNumber(data[i])) {\n\t                sumData += data[i];\n\t            }\n\t        }\n\t        return sumData;\n\t    }\n\n\t    return sum;\n\n\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n/***/ }),\n/* 12 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;!(__WEBPACK_AMD_DEFINE_RESULT__ = function (require) {\n\n\t    var quantile = __webpack_require__(13);\n\n\t    /**\n\t     * Is a method for computing the median value of a sorted array of numbers\n\t     * @param  {Array.<number>} data\n\t     * @return {number}\n\t     */\n\t    function median(data) {\n\n\t        return quantile(data, 0.5);\n\t    }\n\n\t    return median;\n\n\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n/***/ }),\n/* 13 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;!(__WEBPACK_AMD_DEFINE_RESULT__ = function (require) {\n\n\t    /**\n\t     * Estimating quantiles from a sorted sample of numbers\n\t     * @see https://en.wikipedia.org/wiki/Quantile#Estimating_quantiles_from_a_sample\n\t     * R-7 method\n\t     * @param  {Array.<number>} data  sorted array\n\t     * @param  {number} p\n\t     */\n\t    return function (data, p) {\n\n\t        var len = data.length;\n\n\t        if (!len) {\n\t            return 0;\n\t        }\n\t        if (p <= 0 || len < 2) {\n\t            return data[0];\n\t        }\n\t        if (p >= 1) {\n\t            return data[len -1];\n\t        }\n\t        // in the wikipedia's R-7 method h = (N - 1)p + 1, but here array index start from 0\n\t        var h = (len - 1) * p;\n\t        var i = Math.floor(h);\n\t        var a = data[i];\n\t        var b = data[i + 1];\n\t        return a + (b - a) * (h - i);\n\t    };\n\n\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n/***/ }),\n/* 14 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;!(__WEBPACK_AMD_DEFINE_RESULT__ = function (require) {\n\n\t    var number = __webpack_require__(4);\n\t    var isNumber = number.isNumber;\n\n\t    /**\n\t     * Is a method for computing the min value of a list of numbers,\n\t     * which will filter other data types.\n\t     * @param  {Array.<number>} data\n\t     * @return {number}\n\t     */\n\t    function min(data) {\n\n\t        var minData = Infinity;\n\t        for (var i = 0; i < data.length; i++) {\n\t            if (isNumber(data[i]) && data[i] < minData) {\n\t                minData = data[i];\n\t            }\n\t        }\n\t        return minData;\n\t    }\n\n\t    return min;\n\n\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n/***/ }),\n/* 15 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;!(__WEBPACK_AMD_DEFINE_RESULT__ = function (require) {\n\n\t    var max = __webpack_require__(7);\n\t    var min = __webpack_require__(14);\n\t    var quantile = __webpack_require__(13);\n\t    var deviation = __webpack_require__(8);\n\t    var dataProcess = __webpack_require__(2);\n\t    var dataPreprocess = dataProcess.dataPreprocess;\n\t    var normalizeDimensions = dataProcess.normalizeDimensions;\n\t    var array = __webpack_require__(3);\n\t    var ascending = array.ascending;\n\t    var map = array.map;\n\t    var range = __webpack_require__(16);\n\t    var bisect = array.bisect;\n\t    var tickStep = __webpack_require__(17);\n\n\t    /**\n\t     * Compute bins for histogram\n\t     * @param  {Array.<number>} data\n\t     * @param  {Object|string} optOrMethod Optional settings or `method`.\n\t     * @param  {Object|string} optOrMethod.method 'squareRoot' | 'scott' | 'freedmanDiaconis' | 'sturges'\n\t     * @param  {Array.<number>|number} optOrMethod.dimensions If data is a 2-d array,\n\t     *         which dimension will be used to calculate histogram.\n\t     * @return {Object}\n\t     */\n\t    function computeBins(data, optOrMethod) {\n\t        var opt = typeof optOrMethod === 'string'\n\t            ? { method: optOrMethod }\n\t            : (optOrMethod || {});\n\n\t        var threshold = opt.method == null\n\t            ? thresholdMethod.squareRoot\n\t            : thresholdMethod[opt.method];\n\t        var dimensions = normalizeDimensions(opt.dimensions);\n\n\t        var values = dataPreprocess(data, {\n\t            dimensions: dimensions,\n\t            toOneDimensionArray: true\n\t        });\n\t        var maxValue = max(values);\n\t        var minValue = min(values);\n\t        var binsNumber = threshold(values, minValue, maxValue);\n\t        var tickStepResult = tickStep(minValue, maxValue, binsNumber);\n\t        var step = tickStepResult.step;\n\t        var toFixedPrecision = tickStepResult.toFixedPrecision;\n\n\t        // return the xAxis coordinate for each bins, except the end point of the value\n\t        var rangeArray = range(\n\t            // use function toFixed() to avoid data like '0.700000001'\n\t            +((Math.ceil(minValue / step) * step).toFixed(toFixedPrecision)),\n\t            +((Math.floor(maxValue / step) * step).toFixed(toFixedPrecision)),\n\t            step,\n\t            toFixedPrecision\n\t        );\n\n\t        var len = rangeArray.length;\n\n\t        var bins = new Array(len + 1);\n\n\t        for (var i = 0; i <= len; i++) {\n\t            bins[i] = {};\n\t            bins[i].sample = [];\n\t            bins[i].x0 = i > 0\n\t                ? rangeArray[i - 1]\n\t                : (rangeArray[i] - minValue) === step\n\t                ? minValue\n\t                : (rangeArray[i] - step);\n\t            bins[i].x1 = i < len\n\t                ? rangeArray[i]\n\t                : (maxValue - rangeArray[i-1]) === step\n\t                ? maxValue\n\t                : rangeArray[i - 1] + step;\n\t        }\n\n\t        for (var i = 0; i < values.length; i++) {\n\t            if (minValue <= values[i] && values[i] <= maxValue) {\n\t                bins[bisect(rangeArray, values[i], 0, len)].sample.push(values[i]);\n\t            }\n\t        }\n\n\t        var data = map(bins, function (bin) {\n\t            // use function toFixed() to avoid data like '6.5666638489'\n\t            return [\n\t                +((bin.x0 + bin.x1) / 2).toFixed(toFixedPrecision),\n\t                bin.sample.length,\n\t                bin.x0,\n\t                bin.x1,\n\t                bin.x0 + ' - ' + bin.x1\n\t            ];\n\t        });\n\n\t        var customData = map(bins, function (bin) {\n\t            return [bin.x0, bin.x1, bin.sample.length];\n\t        });\n\n\t        return {\n\t            bins: bins,\n\t            data: data,\n\t            customData: customData\n\t        };\n\t    }\n\n\t    /**\n\t     * Four kinds of threshold methods used to\n\t     * compute how much bins the histogram should be divided\n\t     * @see  https://en.wikipedia.org/wiki/Histogram\n\t     * @type {Object}\n\t     */\n\t    var thresholdMethod = {\n\n\t        squareRoot: function (data) {\n\n\t            var bins = Math.ceil(Math.sqrt(data.length));\n\n\t            return bins > 50 ? 50 : bins;\n\t        },\n\n\t        scott: function (data, min, max) {\n\n\t            return Math.ceil((max - min) / (3.5 * deviation(data) * Math.pow(data.length, -1 / 3)));\n\t        },\n\n\t        freedmanDiaconis: function (data, min, max) {\n\n\t            data.sort(ascending);\n\n\t            return Math.ceil(\n\t                (max - min) / (2 * (quantile(data, 0.75) - quantile(data, 0.25)) * Math.pow(data.length, -1 / 3))\n\t            );\n\t        },\n\n\t        sturges: function (data) {\n\n\t            return Math.ceil(Math.log(data.length) / Math.LN2) + 1;\n\n\t        }\n\t    };\n\n\t    return computeBins;\n\n\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n/***/ }),\n/* 16 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;!(__WEBPACK_AMD_DEFINE_RESULT__ = function (require) {\n\n\t    var dataProcess = __webpack_require__(2);\n\t    var getPrecision = dataProcess.getPrecision;\n\n\t    /**\n\t     * Computing range array.\n\t     * Adding param precision to fix range value, avoiding range[i] = 0.7000000001.\n\t     * @param  {number} start\n\t     * @param  {number} end\n\t     * @param  {number} step\n\t     * @param  {number} precision\n\t     * @return {Array.<number>}\n\t     */\n\t    return function (start, end, step, precision) {\n\n\t        var len = arguments.length;\n\n\t        if (len < 2) {\n\t            end = start;\n\t            start = 0;\n\t            step = 1;\n\t        }\n\t        else if (len < 3) {\n\t            step = 1;\n\t        }\n\t        else if (len < 4) {\n\t            step = +step;\n\t            precision = getPrecision(step);\n\t        }\n\t        else {\n\t            precision = +precision;\n\t        }\n\n\t        var n = Math.ceil(((end - start) / step).toFixed(precision));\n\t        var range = new Array(n + 1);\n\t        for (var i = 0; i < n + 1; i++) {\n\t            range[i] = +(start + i * step).toFixed(precision);\n\t        }\n\t        return range;\n\t    };\n\n\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n/***/ }),\n/* 17 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;!(__WEBPACK_AMD_DEFINE_RESULT__ = function (require) {\n\n\t    var numberUtil = __webpack_require__(4);\n\n\t    /**\n\t     * Computing the length of step\n\t     * @see  https://github.com/d3/d3-array/blob/master/src/ticks.js\n\t     * @param {number} start\n\t     * @param {number} stop\n\t     * @param {number} count\n\t     */\n\t    return function (start, stop, count) {\n\n\t        var step0 = Math.abs(stop - start) / count;\n\t        var precision = numberUtil.quantityExponent(step0);\n\n\t        var step1 = Math.pow(10, precision);\n\t        var error = step0 / step1;\n\n\t        if (error >= Math.sqrt(50)) {\n\t            step1 *= 10;\n\t        }\n\t        else if (error >= Math.sqrt(10)) {\n\t            step1 *= 5;\n\t        }\n\t        else if(error >= Math.sqrt(2)) {\n\t            step1 *= 2;\n\t        }\n\n\t        var toFixedPrecision = precision < 0 ? -precision : 0;\n\t        var resultStep = +(\n\t            (stop >= start ? step1 : -step1).toFixed(toFixedPrecision)\n\t        );\n\n\t        return {\n\t            step: resultStep,\n\t            toFixedPrecision: toFixedPrecision\n\t        };\n\t    };\n\n\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n\n/***/ }),\n/* 18 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;!(__WEBPACK_AMD_DEFINE_RESULT__ = function (require) {\n\n\t    var regression = __webpack_require__(5);\n\t    var transformHelper = __webpack_require__(19);\n\t    var FORMULA_DIMENSION = 2;\n\n\t    return {\n\n\t        type: 'ecStat:regression',\n\n\t        /**\n\t         * @param {Paramter<typeof regression>[0]} [params.config.method='linear'] 'linear' by default\n\t         * @param {Paramter<typeof regression>[2]} [params.config.order=2] Only work when method is `polynomial`.\n\t         * @param {DimensionLoose[]|DimensionLoose} [params.config.dimensions=[0, 1]] dimensions that used to calculate regression.\n\t         *        By default [0, 1].\n\t         * @param {'start' | 'end' | 'all'} params.config.formulaOn Include formula on the last (third) dimension of the:\n\t         *        'start': first data item.\n\t         *        'end': last data item (by default).\n\t         *        'all': all data items.\n\t         *        'none': no data item.\n\t         */\n\t        transform: function transform(params) {\n\t            var upstream = params.upstream;\n\t            var config = params.config || {};\n\t            var method = config.method || 'linear';\n\n\t            var result = regression(method, upstream.cloneRawData(), {\n\t                order: config.order,\n\t                dimensions: transformHelper.normalizeExistingDimensions(params, config.dimensions)\n\t            });\n\t            var points = result.points;\n\n\t            var formulaOn = config.formulaOn;\n\t            if (formulaOn == null) {\n\t                formulaOn = 'end';\n\t            }\n\n\t            var dimensions;\n\t            if (formulaOn !== 'none') {\n\t                for (var i = 0; i < points.length; i++) {\n\t                    points[i][FORMULA_DIMENSION] =\n\t                    (\n\t                        (formulaOn === 'start' && i === 0)\n\t                        || (formulaOn === 'all')\n\t                        || (formulaOn === 'end' && i === points.length - 1)\n\t                    ) ? result.expression : '';\n\t                }\n\t                dimensions = upstream.cloneAllDimensionInfo();\n\t                dimensions[FORMULA_DIMENSION] = {};\n\t            }\n\n\t            return [{\n\t                dimensions: dimensions,\n\t                data: points\n\t            }];\n\t        }\n\t    };\n\n\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n\n/***/ }),\n/* 19 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;!(__WEBPACK_AMD_DEFINE_RESULT__ = function (require) {\n\n\t    var arrayUtil = __webpack_require__(3);\n\t    var numberUtil = __webpack_require__(4);\n\t    var objectUtil = __webpack_require__(20);\n\n\t    /**\n\t     * type DimensionLoose = DimensionIndex | DimensionName;\n\t     * type DimensionIndex = number;\n\t     * type DimensionName = string;\n\t     *\n\t     * @param {object} transformParams The parameter of echarts transfrom.\n\t     * @param {DimensionLoose | DimensionLoose[]} dimensionsConfig\n\t     * @return {DimensionIndex | DimensionIndex[]}\n\t     */\n\t    function normalizeExistingDimensions(transformParams, dimensionsConfig) {\n\t        if (dimensionsConfig == null) {\n\t            return;\n\t        }\n\t        var upstream = transformParams.upstream;\n\n\t        if (arrayUtil.isArray(dimensionsConfig)) {\n\t            var result = [];\n\t            for (var i = 0; i < dimensionsConfig.length; i++) {\n\t                var dimInfo = upstream.getDimensionInfo(dimensionsConfig[i]);\n\t                validateDimensionExists(dimInfo, dimensionsConfig[i]);\n\t                result[i] = dimInfo.index;\n\t            }\n\t            return result;\n\t        }\n\t        else {\n\t            var dimInfo = upstream.getDimensionInfo(dimensionsConfig);\n\t            validateDimensionExists(dimInfo, dimensionsConfig);\n\t            return dimInfo.index;\n\t        }\n\n\t        function validateDimensionExists(dimInfo, dimConfig) {\n\t            if (!dimInfo) {\n\t                throw new Error('Can not find dimension by ' + dimConfig);\n\t            }\n\t        }\n\t    }\n\n\t    /**\n\t     * @param {object} transformParams The parameter of echarts transfrom.\n\t     * @param {(DimensionIndex | {name: DimensionName, index: DimensionIndex})[]} dimensionsConfig\n\t     * @param {{name: DimensionName | DimensionName[], index: DimensionIndex | DimensionIndex[]}}\n\t     */\n\t    function normalizeNewDimensions(dimensionsConfig) {\n\t        if (arrayUtil.isArray(dimensionsConfig)) {\n\t            var names = [];\n\t            var indices = [];\n\t            for (var i = 0; i < dimensionsConfig.length; i++) {\n\t                var item = parseDimensionNewItem(dimensionsConfig[i]);\n\t                names.push(item.name);\n\t                indices.push(item.index);\n\t            }\n\t            return {name: names, index: indices};\n\t        }\n\t        else if (dimensionsConfig != null) {\n\t            return parseDimensionNewItem(dimensionsConfig);\n\t        }\n\n\t        function parseDimensionNewItem(dimConfig) {\n\t            if (numberUtil.isNumber(dimConfig)) {\n\t                return { index: dimConfig };\n\t            }\n\t            else if (objectUtil.isObject(dimConfig) && numberUtil.isNumber(dimConfig.index)) {\n\t                return dimConfig;\n\t            }\n\t            throw new Error('Illegle new dimensions config. Expect `{ name: string, index: number }`.');\n\t        }\n\t    }\n\n\t    return {\n\t        normalizeExistingDimensions: normalizeExistingDimensions,\n\t        normalizeNewDimensions: normalizeNewDimensions\n\t    };\n\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n\n/***/ }),\n/* 20 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;!(__WEBPACK_AMD_DEFINE_RESULT__ = function (require) {\n\n\t    function extend(target, source) {\n\t        if (Object.assign) {\n\t            Object.assign(target, source);\n\t        }\n\t        else {\n\t            for (var key in source) {\n\t                if (source.hasOwnProperty(key)) {\n\t                    target[key] = source[key];\n\t                }\n\t            }\n\t        }\n\t        return target;\n\t    }\n\n\t    function isObject(value) {\n\t        const type = typeof value;\n\t        return type === 'function' || (!!value && type === 'object');\n\t    }\n\n\t    return {\n\t        extend: extend,\n\t        isObject: isObject\n\t    };\n\n\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n/***/ }),\n/* 21 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;!(__WEBPACK_AMD_DEFINE_RESULT__ = function (require) {\n\n\t    var histogram = __webpack_require__(15);\n\t    var transformHelper = __webpack_require__(19);\n\n\t    return {\n\n\t        type: 'ecStat:histogram',\n\n\t        /**\n\t         * @param {'squareRoot' | 'scott' | 'freedmanDiaconis' | 'sturges'} [params.config.method='squareRoot']\n\t         * @param {DimnensionLoose[]} [params.config.dimensions=[0, 1]] dimensions that used to calculate histogram.\n\t         *        By default [0].\n\t         */\n\t        transform: function transform(params) {\n\t            var upstream = params.upstream;\n\t            var config = params.config || {};\n\n\t            var result = histogram(upstream.cloneRawData(), {\n\t                method: config.method,\n\t                dimensions: transformHelper.normalizeExistingDimensions(params, config.dimensions)\n\t            });\n\n\t            return [{\n\t                dimensions: ['MeanOfV0V1', 'VCount', 'V0', 'V1', 'DisplayableName'],\n\t                data: result.data\n\t            }, {\n\t                data: result.customData\n\t            }];\n\t        }\n\t    };\n\n\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n\n/***/ }),\n/* 22 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar __WEBPACK_AMD_DEFINE_RESULT__;!(__WEBPACK_AMD_DEFINE_RESULT__ = function (require) {\n\n\t    var clustering = __webpack_require__(1);\n\t    var numberUtil = __webpack_require__(4);\n\t    var transformHelper = __webpack_require__(19);\n\n\t    var isNumber = numberUtil.isNumber;\n\n\t    return {\n\n\t        type: 'ecStat:clustering',\n\n\t        /**\n\t         * @param {number} params.config.clusterCount Mandatory.\n\t         *        The number of clusters in a dataset. It has to be greater than 1.\n\t         * @param {(DimensionName | DimensionIndex)[]} [params.config.dimensions] Optional.\n\t         *        Target dimensions to calculate the regression.\n\t         *        By default: use all of the data.\n\t         * @param {(DimensionIndex | {name?: DimensionName, index: DimensionIndex})} [params.config.outputClusterIndexDimension] Mandatory.\n\t         * @param {(DimensionIndex | {name?: DimensionName, index: DimensionIndex})[]} [params.config.outputCentroidDimensions] Optional.\n\t         *        If specified, the centroid will be set to those dimensions of the result data one by one.\n\t         *        By default not set centroid to result.\n\t         */\n\t        transform: function transform(params) {\n\t            var upstream = params.upstream;\n\t            var config = params.config || {};\n\t            var clusterCount = config.clusterCount;\n\n\t            if (!isNumber(clusterCount) || clusterCount <= 0) {\n\t                throw new Error('config param \"clusterCount\" need to be specified as an interger greater than 1.');\n\t            }\n\n\t            if (clusterCount === 1) {\n\t                return [{\n\t                }, {\n\t                    data: []\n\t                }];\n\t            }\n\n\t            var outputClusterIndexDimension = transformHelper.normalizeNewDimensions(\n\t                config.outputClusterIndexDimension\n\t            );\n\t            var outputCentroidDimensions = transformHelper.normalizeNewDimensions(\n\t                config.outputCentroidDimensions\n\t            );\n\n\t            if (outputClusterIndexDimension == null) {\n\t                throw new Error('outputClusterIndexDimension is required as a number.');\n\t            }\n\n\t            var result = clustering.hierarchicalKMeans(upstream.cloneRawData(), {\n\t                clusterCount: clusterCount,\n\t                stepByStep: false,\n\t                dimensions: transformHelper.normalizeExistingDimensions(params, config.dimensions),\n\t                outputType: clustering.OutputType.SINGLE,\n\t                outputClusterIndexDimension: outputClusterIndexDimension.index,\n\t                outputCentroidDimensions: (outputCentroidDimensions || {}).index\n\t            });\n\n\t            var sourceDimAll = upstream.cloneAllDimensionInfo();\n\t            var resultDimsDef = [];\n\t            for (var i = 0; i < sourceDimAll.length; i++) {\n\t                var sourceDimItem = sourceDimAll[i];\n\t                resultDimsDef.push(sourceDimItem.name);\n\t            }\n\n\t            // Always set to dims def even if name not exists, because the resultDimsDef.length\n\t            // need to be enlarged to tell echarts that there is \"cluster index dimension\" and \"dist dimension\".\n\t            resultDimsDef[outputClusterIndexDimension.index] = outputClusterIndexDimension.name;\n\n\t            if (outputCentroidDimensions) {\n\t                for (var i = 0; i < outputCentroidDimensions.index.length; i++) {\n\t                    if (outputCentroidDimensions.name[i] != null) {\n\t                        resultDimsDef[outputCentroidDimensions.index[i]] = outputCentroidDimensions.name[i];\n\t                    }\n\t                }\n\t            }\n\n\t            return [{\n\t                dimensions: resultDimsDef,\n\t                data: result.data\n\t            }, {\n\t                data: result.centroids\n\t            }];\n\t        }\n\t    };\n\n\t}.call(exports, __webpack_require__, exports, module), __WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\n\n/***/ })\n/******/ ])\n});\n;", "module.exports = require('./dist/ecStat.js');\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,KAAC,SAAS,iCAAiC,MAAM,SAAS;AACzD,UAAG,OAAO,YAAY,YAAY,OAAO,WAAW;AACnD,eAAO,UAAU,QAAQ;AAAA,eAClB,OAAO,WAAW,cAAc,OAAO;AAC9C,eAAO,CAAC,GAAG,OAAO;AAAA,eACX,OAAO,YAAY;AAC1B,gBAAQ,QAAQ,IAAI,QAAQ;AAAA;AAE5B,aAAK,QAAQ,IAAI,QAAQ;AAAA,IAC3B,GAAG,SAAM,WAAW;AACpB;AAAA;AAAA,QAAiB,SAAS,SAAS;AAEzB,cAAI,mBAAmB,CAAC;AAGxB,mBAAS,oBAAoB,UAAU;AAGtC,gBAAG,iBAAiB,QAAQ;AAC3B,qBAAO,iBAAiB,QAAQ,EAAE;AAGnC,gBAAIA,UAAS,iBAAiB,QAAQ,IAAI;AAAA;AAAA,cACzC,SAAS,CAAC;AAAA;AAAA,cACV,IAAI;AAAA;AAAA,cACJ,QAAQ;AAAA;AAAA,YACT;AAGA,oBAAQ,QAAQ,EAAE,KAAKA,QAAO,SAASA,SAAQA,QAAO,SAAS,mBAAmB;AAGlF,YAAAA,QAAO,SAAS;AAGhB,mBAAOA,QAAO;AAAA,UACf;AAIA,8BAAoB,IAAI;AAGxB,8BAAoB,IAAI;AAGxB,8BAAoB,IAAI;AAGxB,iBAAO,oBAAoB,CAAC;AAAA,QAC7B,EAEC;AAAA;AAAA;AAAA,UAEH,SAASA,SAAQC,UAAS,qBAAqB;AAErD,gBAAI;AAA8B,cAAE,gCAAgC,SAAUC,UAAS;AAEnF,qBAAO;AAAA,gBAEH,YAAY,oBAAoB,CAAC;AAAA,gBACjC,YAAY,oBAAoB,CAAC;AAAA,gBACjC,YAAY,oBAAoB,CAAC;AAAA,gBACjC,WAAW,oBAAoB,EAAE;AAAA,gBAEjC,WAAW;AAAA,kBACP,YAAY,oBAAoB,EAAE;AAAA,kBAClC,WAAW,oBAAoB,EAAE;AAAA,kBACjC,YAAY,oBAAoB,EAAE;AAAA,gBACtC;AAAA,cAEJ;AAAA,YAEJ,EAAE,KAAKD,UAAS,qBAAqBA,UAASD,OAAM,GAAG,kCAAkC,WAAcA,QAAO,UAAU;AAAA,UAEnH;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAErD,gBAAI;AAA8B,cAAE,gCAAgC,SAAUC,UAAS;AAEnF,kBAAI,cAAc,oBAAoB,CAAC;AACvC,kBAAI,iBAAiB,YAAY;AACjC,kBAAI,sBAAsB,YAAY;AACtC,kBAAI,YAAY,oBAAoB,CAAC;AACrC,kBAAI,aAAa,oBAAoB,CAAC;AACtC,kBAAI,YAAY,UAAU;AAC1B,kBAAI,cAAc,UAAU;AAC5B,kBAAI,WAAW,UAAU;AACzB,kBAAI,QAAQ,UAAU;AAEtB,kBAAI,aAAa,oBAAoB,CAAC;AACtC,kBAAI,WAAW,WAAW;AAC1B,kBAAI,UAAU,KAAK;AAEnB,kBAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAcb,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAqBR,UAAU;AAAA,cACd;AAQA,uBAAS,OAAO,MAAM,GAAG,UAAU;AAG/B,oBAAI,kBAAkB,MAAM,KAAK,QAAQ,CAAC;AAC1C,oBAAI,YAAY,eAAe,GAAG,YAAY,MAAM,SAAS,UAAU,CAAC;AACxE,oBAAI,iBAAiB;AACrB,oBAAI;AACJ,oBAAI;AACJ,oBAAI;AACJ,oBAAI;AAEJ,uBAAO,gBAAgB;AACnB,mCAAiB;AACjB,2BAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,8BAAU;AACV,+BAAW;AACX,6BAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,+BAAS,WAAW,KAAK,CAAC,GAAG,UAAU,CAAC,GAAG,QAAQ;AACnD,0BAAI,SAAS,SAAS;AAClB,kCAAU;AACV,mCAAW;AAAA,sBACf;AAAA,oBACJ;AACA,wBAAI,gBAAgB,CAAC,EAAE,CAAC,MAAM,UAAU;AACpC,uCAAiB;AAAA,oBACrB;AACA,oCAAgB,CAAC,EAAE,CAAC,IAAI;AACxB,oCAAgB,CAAC,EAAE,CAAC,IAAI;AAAA,kBAC5B;AAEA,2BAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,iCAAa,CAAC;AACd,6BAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC7C,0BAAI,gBAAgB,CAAC,EAAE,CAAC,MAAM,GAAG;AAC7B,mCAAW,KAAK,KAAK,CAAC,CAAC;AAAA,sBAC3B;AAAA,oBACJ;AACA,8BAAU,CAAC,IAAI,cAAc,YAAY,QAAQ;AAAA,kBACrD;AAAA,gBACJ;AAEA,oBAAI,oBAAoB;AAAA,kBACpB;AAAA,kBACA;AAAA,gBACJ;AACA,uBAAO;AAAA,cACX;AAMA,uBAAS,cAAc,UAAU,UAAU;AACvC,oBAAI,YAAY,CAAC;AACjB,oBAAI;AACJ,oBAAI;AACJ,yBAAS,IAAI,GAAG,IAAI,SAAS,WAAW,QAAQ,KAAK;AACjD,sBAAI,SAAS,SAAS,WAAW,CAAC;AAClC,wBAAM;AACN,2BAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,2BAAO,SAAS,CAAC,EAAE,MAAM;AAAA,kBAC7B;AACA,yBAAO,MAAM,SAAS;AACtB,4BAAU,KAAK,IAAI;AAAA,gBACvB;AACA,uBAAO;AAAA,cACX;AAsBA,uBAAS,mBAAmB,MAAM,sBAAsB,YAAY;AAChE,oBAAI,UACA,SAAS,oBAAoB,IACvB,EAAC,cAAc,sBAAsB,WAAsB,IAC3D,yBACL,EAAC,cAAc,EAAC;AAErB,oBAAI,IAAI,OAAO;AAEf,oBAAI,IAAI,GAAG;AACP;AAAA,gBACJ;AAEA,oBAAI,WAAW,cAAc,MAAM,MAAM;AACzC,oBAAI,qBAAqB,SAAS,eAAe,WAAW;AAE5D,oBAAI,UAAU,eAAe,MAAM,EAAC,YAAY,SAAS,WAAU,CAAC;AAEpE,oBAAI,iBAAiB,MAAM,QAAQ,QAAQ,CAAC;AAC5C,oBAAI;AACJ,oBAAI;AACJ,oBAAI;AAEJ,yBAAS,YAAY,WAAWC,OAAM;AAClC,iCAAe,SAAS,EAAE,CAAC,IAAIA;AAAA,gBACnC;AACA,yBAAS,YAAY,WAAW;AAC5B,yBAAO,eAAe,SAAS,EAAE,CAAC;AAAA,gBACtC;AAAC;AAED,oBAAI,oBAAoB;AACpB,qCAAmB,CAAC;AACpB,sBAAI,8BAA8B,SAAS;AAE3C,oCAAkB,SAAU,WAAW,cAAc;AACjD,qCAAiB,SAAS,EAAE,2BAA2B,IAAI;AAAA,kBAC/D;AACA,oCAAkB,SAAU,WAAW;AACnC,2BAAO,iBAAiB,SAAS,EAAE,2BAA2B;AAAA,kBAClE;AAEA,2BAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,qCAAiB,KAAK,QAAQ,CAAC,EAAE,MAAM,CAAC;AACxC,gCAAY,GAAG,CAAC;AAChB,oCAAgB,GAAG,CAAC;AAAA,kBACxB;AAAA,gBACJ,OACK;AACD,oCAAkB,SAAU,WAAW,cAAc;AACjD,mCAAe,SAAS,EAAE,CAAC,IAAI;AAAA,kBACnC;AACA,oCAAkB,SAAU,WAAW;AACnC,2BAAO,eAAe,SAAS,EAAE,CAAC;AAAA,kBACtC;AAAA,gBACJ;AAGA,oBAAI,YAAY,cAAc,SAAS,QAAQ;AAC/C,oBAAI,WAAW,CAAC,SAAS;AACzB,yBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,sBAAI,OAAO,WAAW,QAAQ,CAAC,GAAG,WAAW,QAAQ;AACrD,8BAAY,GAAG,IAAI;AAAA,gBACvB;AAEA,oBAAI;AACJ,oBAAI;AACJ,oBAAI;AACJ,oBAAI;AACJ,oBAAI;AACJ,oBAAI;AACJ,oBAAI,QAAQ;AACZ,oBAAI,SAAS;AAAA,kBACT,MAAM;AAAA,kBACN,WAAW;AAAA,kBACX,OAAO;AAAA,gBACX;AACA,oBAAI,CAAC,oBAAoB;AAErB,yBAAO,iBAAiB;AAAA,gBAC5B;AAEA,yBAAS,UAAU;AAGf,sBAAI,QAAQ,GAAG;AACX,gCAAY;AACZ,wBAAI;AACJ,wBAAI;AACJ,wBAAI;AAEJ,6BAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,mCAAa,CAAC;AACd,oCAAc,CAAC;AACf,+BAASC,KAAI,GAAGA,KAAI,QAAQ,QAAQA,MAAK;AACrC,4BAAI,gBAAgBA,EAAC,MAAM,GAAG;AAC1B,qCAAW,KAAK,QAAQA,EAAC,CAAC;AAAA,wBAC9B,OACK;AACD,sCAAY,KAAK,YAAYA,EAAC,CAAC;AAAA,wBACnC;AAAA,sBACJ;AACA,oCAAc,OAAO,YAAY,GAAG,QAAQ;AAC5C,iCAAW,YAAY,YAAY,iBAAiB,CAAC;AACrD,oCAAc,SAAS,WAAW;AAClC,0BAAI,WAAW,cAAc,WAAW;AACpC,oCAAY,cAAc;AAC1B,oCAAY;AACZ,sCAAc,YAAY;AAC1B,wCAAgB,YAAY;AAAA,sBAChC;AAAA,oBACJ;AAEA,6BAASA,KAAI,GAAGA,KAAI,cAAc,QAAQA,MAAK;AAC3C,0BAAI,cAAcA,EAAC,EAAE,CAAC,MAAM,GAAG;AAC3B,sCAAcA,EAAC,EAAE,CAAC,IAAI;AAAA,sBAC1B,WACS,cAAcA,EAAC,EAAE,CAAC,MAAM,GAAG;AAChC,sCAAcA,EAAC,EAAE,CAAC,IAAI,SAAS;AAAA,sBACnC;AAAA,oBACJ;AAEA,6BAAS,SAAS,IAAI,YAAY,CAAC;AACnC,6BAAS,KAAK,YAAY,CAAC,CAAC;AAC5B,6BAASA,KAAI,GAAG,IAAI,GAAGA,KAAI,QAAQ,UAAU,IAAI,cAAc,QAAQA,MAAK;AACxE,0BAAI,gBAAgBA,EAAC,MAAM,WAAW;AAClC,wCAAgBA,IAAG,cAAc,CAAC,EAAE,CAAC,CAAC;AACtC,oCAAYA,IAAG,cAAc,GAAG,EAAE,CAAC,CAAC;AAAA,sBACxC;AAAA,oBACJ;AAEA,wBAAI,eAAe,CAAC;AACpB,wBAAI,CAAC,oBAAoB;AACrB,+BAASA,KAAI,GAAGA,KAAI,SAAS,QAAQA,MAAK;AACtC,qCAAaA,EAAC,IAAI,CAAC;AACnB,iCAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,8BAAI,gBAAgB,CAAC,MAAMA,IAAG;AAC1B,yCAAaA,EAAC,EAAE,KAAK,QAAQ,CAAC,CAAC;AAAA,0BACnC;AAAA,wBACJ;AAAA,sBACJ;AACA,6BAAO,kBAAkB;AAAA,oBAC7B;AAEA;AAAA,kBACJ,OACK;AACD,2BAAO,QAAQ;AAAA,kBACnB;AAAA,gBACJ;AAEA,oBAAI,CAAC,OAAO,YAAY;AACpB,yBAAO,QAAQ,GAAG,CAAC,OAAO;AAAM;AAAA,gBACpC,OACK;AACD,yBAAO,OAAO,WAAY;AACtB,4BAAQ;AACR,4CAAwB,QAAQ,QAAQ;AACxC,2BAAO;AAAA,kBACX;AAAA,gBACJ;AACA,wCAAwB,QAAQ,QAAQ;AACxC,uBAAO;AAAA,cACX;AAEA,uBAAS,wBAAwB,QAAQ,UAAU;AAC/C,oBAAI,2BAA2B,SAAS;AACxC,oBAAI,SAAS,eAAe,WAAW,UAAU,4BAA4B,MAAM;AAC/E;AAAA,gBACJ;AACA,oBAAI,mBAAmB,OAAO;AAC9B,oBAAI,YAAY,OAAO;AAEvB,yBAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAC9C,sBAAI,OAAO,iBAAiB,CAAC;AAC7B,sBAAI,eAAe,KAAK,SAAS,2BAA2B;AAC5D,sBAAI,WAAW,UAAU,YAAY;AACrC,sBAAI,SAAS,KAAK,IAAI,SAAS,QAAQ,yBAAyB,MAAM;AACtE,2BAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,yBAAK,yBAAyB,CAAC,CAAC,IAAI,SAAS,CAAC;AAAA,kBAClD;AAAA,gBACJ;AAAA,cACJ;AAKA,uBAAS,eAAe,GAAG,SAAS;AAEhC,oBAAI,YAAY,MAAM,GAAG,QAAQ,MAAM;AAEvC,yBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,sBAAI,aAAa,QAAQ,CAAC;AAC1B,2BAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,8BAAU,CAAC,EAAE,CAAC,IAAI,WAAW,MAAM,WAAW,OAAO,KAAK,OAAO;AAAA,kBACrE;AAAA,gBACJ;AACA,uBAAO;AAAA,cACX;AAKA,uBAAS,WAAW,UAAU,UAAU,UAAU;AAK9C,oBAAI,WAAW;AACf,oBAAI,aAAa,SAAS;AAC1B,oBAAI,UAAU,SAAS;AAEvB,yBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,sBAAI,OAAO,QAAQ,CAAC,EAAE;AAEtB,sBAAI,MAAM;AACN,wBAAI,SAAS,WAAW,CAAC;AACzB,wBAAI,QAAQ,SAAS,MAAM,IAAI,SAAS,CAAC,KAAK;AAC9C,gCAAY,QAAQ,MAAM,CAAC;AAAA,kBAC/B;AAAA,gBACJ;AAEA,uBAAO;AAAA,cACX;AAEA,uBAAS,cAAc,SAAS,QAAQ;AACpC,oBAAI,OAAO,UAAU,OAAO;AAC5B,oBAAI,KAAK,SAAS,GAAG;AACjB,wBAAM,IAAI,MAAM,6DAA6D;AAAA,gBACjF;AACA,oBAAI,WAAW,KAAK,CAAC;AACrB,oBAAI,oBAAoB,CAAC;AACzB,yBAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AAC/B,oCAAkB,KAAK,CAAC;AAAA,gBAC5B;AACA,oBAAI,aAAa,oBAAoB,OAAO,YAAY,iBAAiB;AACzE,oBAAI,aAAa,OAAO,cAAc,WAAW;AAEjD,oBAAI,8BAA8B,OAAO;AACzC,oBAAI,eAAe,WAAW,UAAU,CAAC,WAAW,SAAS,2BAA2B,GAAG;AACvF,wBAAM,IAAI,MAAM,sDAAsD;AAAA,gBAC1E;AACA,oBAAI,UAAU,YAAY,SAAS,UAAU;AAE7C,uBAAO;AAAA,kBACH;AAAA,kBACA,YAAY;AAAA,kBACZ;AAAA,kBACA;AAAA,kBACA,0BAA0B,OAAO;AAAA,gBACrC;AAAA,cACJ;AAEA,uBAAS,YAAY,SAAS,YAAY;AACtC,oBAAI,UAAU,CAAC;AACf,oBAAI,SAAS,WAAW;AACxB,yBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,0BAAQ,KAAK,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC;AAAA,gBAClD;AACA,yBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,sBAAI,OAAO,QAAQ,CAAC;AACpB,2BAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,wBAAI,aAAa,QAAQ,CAAC;AAC1B,wBAAI,MAAM,KAAK,WAAW,CAAC,CAAC;AAC5B,+BAAW,MAAM,QAAQ,WAAW,MAAM;AAC1C,+BAAW,MAAM,QAAQ,WAAW,MAAM;AAAA,kBAC9C;AAAA,gBACJ;AACA,yBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,0BAAQ,CAAC,EAAE,OAAO,QAAQ,CAAC,EAAE,MAAM,QAAQ,CAAC,EAAE;AAAA,gBAClD;AACA,uBAAO;AAAA,cACX;AAEA,qBAAO;AAAA,gBACH;AAAA,gBACA;AAAA,cACJ;AAAA,YAEJ,EAAE,KAAKH,UAAS,qBAAqBA,UAASD,OAAM,GAAG,kCAAkC,WAAcA,QAAO,UAAU;AAAA,UAEnH;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAErD,gBAAI;AAA8B,cAAE,gCAAgC,SAAUC,UAAS;AAEnF,kBAAI,QAAQ,oBAAoB,CAAC;AACjC,kBAAI,UAAU,MAAM;AACpB,kBAAI,OAAO,MAAM;AACjB,kBAAI,SAAS,oBAAoB,CAAC;AAClC,kBAAI,WAAW,OAAO;AAQtB,uBAAS,oBAAoB,YAAY,mBAAmB;AACxD,uBAAO,OAAO,eAAe,WACvB,CAAC,UAAU,IACX,cAAc,OACd,oBACA;AAAA,cACV;AAiBA,uBAAS,eAAe,MAAM,KAAK;AAC/B,sBAAM,OAAO,CAAC;AACd,oBAAI,aAAa,IAAI;AACrB,oBAAI,qBAAqB,CAAC;AAC1B,oBAAI,cAAc,MAAM;AACpB,2BAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,uCAAmB,WAAW,CAAC,CAAC,IAAI;AAAA,kBACxC;AAAA,gBACJ;AACA,oBAAI,eAAe,IAAI,sBAChB,aAAa,WAAW,CAAC,IAAI,IAC9B;AAEN,yBAAS,wBAAwB,QAAQ;AACrC,yBAAO,CAAC,cAAc,mBAAmB,eAAe,MAAM;AAAA,gBAClE;AAEA,oBAAI,CAAC,QAAQ,IAAI,GAAG;AAChB,wBAAM,IAAI,MAAM,8CAA8C;AAAA,gBAClE;AACA,oBAAI,UAAU,CAAC;AACf,oBAAI,YAAY,KAAK,IAAI;AAEzB,oBAAI,UAAU,WAAW,GAAG;AACxB,2BAAS,IAAI,GAAG,IAAI,UAAU,CAAC,GAAG,KAAK;AACnC,wBAAI,OAAO,KAAK,CAAC;AACjB,wBAAI,SAAS,IAAI,GAAG;AAChB,8BAAQ,KAAK,IAAI;AAAA,oBACrB;AAAA,kBACJ;AAAA,gBACJ,WACS,UAAU,WAAW,GAAG;AAC7B,2BAAS,IAAI,GAAG,IAAI,UAAU,CAAC,GAAG,KAAK;AACnC,wBAAI,YAAY;AAChB,wBAAI,OAAO,KAAK,CAAC;AACjB,6BAAS,IAAI,GAAG,IAAI,UAAU,CAAC,GAAG,KAAK;AACnC,0BAAI,wBAAwB,CAAC,KAAK,CAAC,SAAS,KAAK,CAAC,CAAC,GAAG;AAClD,oCAAY;AAAA,sBAChB;AAAA,oBACJ;AACA,wBAAI,WAAW;AACX,8BAAQ;AAAA,wBACJ,gBAAgB,OACV,KAAK,YAAY,IACjB;AAAA,sBACV;AAAA,oBACJ;AAAA,kBACJ;AAAA,gBACJ;AACA,uBAAO;AAAA,cACX;AAMA,uBAAS,aAAa,KAAK;AACvB,oBAAI,MAAM,IAAI,SAAS;AAEvB,oBAAI,WAAW,IAAI,QAAQ,GAAG;AAC9B,uBAAO,WAAW,IAAI,IAAI,IAAI,SAAS,IAAI;AAAA,cAC/C;AAEA,qBAAO;AAAA,gBACH;AAAA,gBACA;AAAA,gBACA;AAAA,cACJ;AAAA,YAEJ,EAAE,KAAKD,UAAS,qBAAqBA,UAASD,OAAM,GAAG,kCAAkC,WAAcA,QAAO,UAAU;AAAA,UAEnH;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAErD,gBAAI;AAA8B,cAAE,gCAAgC,SAAUC,UAAS;AAEnF,kBAAI,cAAc,OAAO,UAAU;AACnC,kBAAI,aAAa,MAAM;AACvB,kBAAI,YAAY,WAAW;AAO3B,uBAAS,KAAK,MAAM;AAChB,oBAAI,IAAI,CAAC;AACT,uBAAO,QAAQ,IAAI,GAAG;AAClB,oBAAE,KAAK,KAAK,MAAM;AAClB,yBAAO,KAAK,CAAC;AAAA,gBACjB;AACA,uBAAO;AAAA,cACX;AAMA,uBAAS,QAAQ,OAAO;AACpB,uBAAO,YAAY,KAAK,KAAK,MAAM;AAAA,cACvC;AAQA,uBAAS,MAAM,GAAG,GAAG;AACjB,oBAAI,YAAY,CAAC;AACjB,yBAAS,IAAI,GAAG,IAAI,GAAI,KAAK;AACzB,4BAAU,CAAC,IAAI,CAAC;AAChB,2BAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,8BAAU,CAAC,EAAE,CAAC,IAAI;AAAA,kBACtB;AAAA,gBACJ;AACA,uBAAO;AAAA,cACX;AASA,uBAAS,IAAI,QAAQ;AACjB,oBAAIG,OAAM;AACV,yBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,kBAAAA,QAAO,OAAO,CAAC;AAAA,gBACnB;AACA,uBAAOA;AAAA,cACX;AAQA,uBAAS,YAAY,UAAU,GAAG;AAC9B,oBAAIA,OAAM;AACV,yBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,kBAAAA,QAAO,SAAS,CAAC,EAAE,CAAC;AAAA,gBACxB;AACA,uBAAOA;AAAA,cACX;AAGA,uBAAS,UAAU,GAAG,GAAG;AAErB,uBAAO,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI;AAAA,cAElD;AAWA,uBAAS,OAAO,OAAO,OAAO,OAAO,KAAK;AAEtC,oBAAI,SAAS,MAAM;AACf,0BAAQ;AAAA,gBACZ;AACA,oBAAI,OAAO,MAAM;AACb,wBAAM,MAAM;AAAA,gBAChB;AACA,uBAAO,QAAQ,KAAK;AAChB,sBAAI,MAAM,KAAK,OAAO,QAAQ,OAAO,CAAC;AACtC,sBAAI,UAAU,UAAU,MAAM,GAAG,GAAG,KAAK;AACzC,sBAAI,UAAU,GAAG;AACb,0BAAM;AAAA,kBACV,WACS,UAAU,GAAG;AAClB,4BAAQ,MAAM;AAAA,kBAClB,OACK;AACD,2BAAO,MAAM;AAAA,kBACjB;AAAA,gBACJ;AACA,uBAAO;AAAA,cACX;AAUA,uBAAS,IAAI,KAAK,IAAI,SAAS;AAC3B,oBAAI,EAAE,OAAO,KAAK;AACd;AAAA,gBACJ;AACA,oBAAI,IAAI,OAAO,IAAI,QAAQ,WAAW;AAClC,yBAAO,IAAI,IAAI,IAAI,OAAO;AAAA,gBAC9B,OACK;AACD,sBAAI,SAAS,CAAC;AACd,2BAAS,IAAI,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK;AAC5C,2BAAO,KAAK,GAAG,KAAK,SAAS,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,kBAChD;AACA,yBAAO;AAAA,gBACX;AAAA,cACJ;AAEA,qBAAO;AAAA,gBACH;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACJ;AAAA,YAEJ,EAAE,KAAKJ,UAAS,qBAAqBA,UAASD,OAAM,GAAG,kCAAkC,WAAcA,QAAO,UAAU;AAAA,UAEnH;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAErD,gBAAI;AAA8B,cAAE,gCAAgC,SAAUC,UAAS;AAOnF,uBAAS,SAAS,OAAO;AAErB,wBAAQ,UAAU,OAAO,MAAM,CAAC;AAChC,uBAAO,OAAO,UAAU,YAAY,CAAC,MAAM,KAAK;AAAA,cACpD;AAOA,uBAAS,UAAU,OAAO;AACtB,uBAAO,SAAS,KAAK,KAAK,UAAU,KAAK,MAAM,KAAK;AAAA,cACxD;AAEA,uBAAS,iBAAiB,KAAK;AAC3B,oBAAI,QAAQ,GAAG;AACX,yBAAO;AAAA,gBACX;AACA,oBAAI,MAAM,KAAK,MAAM,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI;AAE9C,oBAAI,MAAM,KAAK,IAAI,IAAI,GAAG,KAAK,IAAI;AAC/B;AAAA,gBACJ;AACA,uBAAO;AAAA,cACX;AAEA,qBAAO;AAAA,gBACH;AAAA,gBACA;AAAA,gBACA;AAAA,cACJ;AAAA,YAEJ,EAAE,KAAKD,UAAS,qBAAqBA,UAASD,OAAM,GAAG,kCAAkC,WAAcA,QAAO,UAAU;AAAA,UAEnH;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAErD,gBAAI;AAA8B,cAAE,gCAAgC,SAAUC,UAAS;AAEnF,kBAAI,cAAc,oBAAoB,CAAC;AACvC,kBAAI,iBAAiB,YAAY;AACjC,kBAAI,sBAAsB,YAAY;AAEtC,kBAAI,eAAe;AAAA;AAAA;AAAA;AAAA,gBAKf,QAAQ,SAAU,SAAS,KAAK;AAE5B,sBAAI,UAAU,IAAI,WAAW,CAAC;AAC9B,sBAAI,UAAU,IAAI,WAAW,CAAC;AAC9B,sBAAI,OAAO;AACX,sBAAI,OAAO;AACX,sBAAI,QAAQ;AACZ,sBAAI,QAAQ;AACZ,sBAAI,MAAM,QAAQ;AAElB,2BAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,wBAAI,UAAU,QAAQ,CAAC;AACvB,4BAAQ,QAAQ,OAAO;AACvB,4BAAQ,QAAQ,OAAO;AACvB,6BAAS,QAAQ,OAAO,IAAI,QAAQ,OAAO;AAC3C,6BAAS,QAAQ,OAAO,IAAI,QAAQ,OAAO;AAAA,kBAC/C;AAEA,sBAAI,YAAa,MAAM,QAAU,OAAO,SAAW,MAAM,QAAU,OAAO;AAC1E,sBAAI,YAAa,OAAO,MAAS,WAAW,OAAQ;AAEpD,sBAAI,SAAS,CAAC;AACd,2BAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,wBAAI,UAAU,QAAQ,CAAC;AACvB,wBAAI,aAAa,QAAQ,MAAM;AAC/B,+BAAW,OAAO,IAAI,QAAQ,OAAO;AACrC,+BAAW,OAAO,IAAI,WAAW,QAAQ,OAAO,IAAI;AACpD,2BAAO,KAAK,UAAU;AAAA,kBAC1B;AAEA,sBAAI,aAAa,SAAS,KAAK,MAAM,WAAW,GAAG,IAAI,MAAM,SAAS,KAAK,MAAM,YAAY,GAAG,IAAI;AAEpG,yBAAO;AAAA,oBACH,QAAQ;AAAA,oBACR,WAAW;AAAA,sBACP;AAAA,sBACA;AAAA,oBACJ;AAAA,oBACA;AAAA,kBACJ;AAAA,gBACJ;AAAA;AAAA;AAAA;AAAA;AAAA,gBAMA,qBAAqB,SAAU,SAAS,KAAK;AAEzC,sBAAI,UAAU,IAAI,WAAW,CAAC;AAC9B,sBAAI,UAAU,IAAI,WAAW,CAAC;AAC9B,sBAAI,QAAQ;AACZ,sBAAI,QAAQ;AAEZ,2BAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,wBAAI,UAAU,QAAQ,CAAC;AACvB,6BAAS,QAAQ,OAAO,IAAI,QAAQ,OAAO;AAC3C,6BAAS,QAAQ,OAAO,IAAI,QAAQ,OAAO;AAAA,kBAC/C;AAEA,sBAAI,WAAW,QAAQ;AACvB,sBAAI,SAAS,CAAC;AAEd,2BAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,wBAAI,UAAU,QAAQ,CAAC;AACvB,wBAAI,aAAa,QAAQ,MAAM;AAC/B,+BAAW,OAAO,IAAI,QAAQ,OAAO;AACrC,+BAAW,OAAO,IAAI,QAAQ,OAAO,IAAI;AACzC,2BAAO,KAAK,UAAU;AAAA,kBAC1B;AAEA,sBAAI,aAAa,SAAS,KAAK,MAAM,WAAW,GAAG,IAAI,MAAM;AAE7D,yBAAO;AAAA,oBACH,QAAQ;AAAA,oBACR,WAAW;AAAA,sBACP;AAAA,oBACJ;AAAA,oBACA;AAAA,kBACJ;AAAA,gBACJ;AAAA;AAAA;AAAA;AAAA,gBAKA,aAAa,SAAU,SAAS,KAAK;AAEjC,sBAAI,UAAU,IAAI,WAAW,CAAC;AAC9B,sBAAI,UAAU,IAAI,WAAW,CAAC;AAC9B,sBAAI,OAAO;AACX,sBAAI,OAAO;AACX,sBAAI,SAAS;AACb,sBAAI,UAAU;AACd,sBAAI,WAAW;AACf,sBAAI,QAAQ;AAEZ,2BAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,wBAAI,UAAU,QAAQ,CAAC;AACvB,4BAAQ,QAAQ,OAAO;AACvB,4BAAQ,QAAQ,OAAO;AACvB,6BAAS,QAAQ,OAAO,IAAI,QAAQ,OAAO;AAC3C,8BAAU,QAAQ,OAAO,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO;AAC/D,+BAAW,QAAQ,OAAO,IAAI,KAAK,IAAI,QAAQ,OAAO,CAAC;AACvD,gCAAY,QAAQ,OAAO,IAAI,QAAQ,OAAO,IAAI,KAAK,IAAI,QAAQ,OAAO,CAAC;AAAA,kBAC/E;AAEA,sBAAI,cAAe,OAAO,SAAW,QAAQ;AAC7C,sBAAI,cAAc,KAAK,IAAI,KAAK,IAAI,SAAS,UAAU,QAAQ,YAAY,WAAW;AACtF,sBAAI,SAAS,OAAO,WAAW,QAAQ,WAAW;AAClD,sBAAI,SAAS,CAAC;AAEd,2BAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,wBAAI,UAAU,QAAQ,CAAC;AACvB,wBAAI,aAAa,QAAQ,MAAM;AAC/B,+BAAW,OAAO,IAAI,QAAQ,OAAO;AACrC,+BAAW,OAAO,IAAI,cAAc,KAAK,IAAI,KAAK,GAAG,QAAQ,QAAQ,OAAO,CAAC;AAC7E,2BAAO,KAAK,UAAU;AAAA,kBAC1B;AAEA,sBAAI,aAAa,SAAS,KAAK,MAAM,cAAc,GAAG,IAAI,MAAM,QAAQ,KAAK,MAAM,QAAQ,GAAG,IAAI,MAAM;AAExG,yBAAO;AAAA,oBACH,QAAQ;AAAA,oBACR,WAAW;AAAA,sBACP;AAAA,sBACA;AAAA,oBACJ;AAAA,oBACA;AAAA,kBACJ;AAAA,gBAEJ;AAAA;AAAA;AAAA;AAAA,gBAKA,aAAa,SAAU,SAAS,KAAK;AAEjC,sBAAI,UAAU,IAAI,WAAW,CAAC;AAC9B,sBAAI,UAAU,IAAI,WAAW,CAAC;AAC9B,sBAAI,SAAS;AACb,sBAAI,UAAU;AACd,sBAAI,OAAO;AACX,sBAAI,YAAY;AAEhB,2BAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,wBAAI,UAAU,QAAQ,CAAC;AACvB,8BAAU,KAAK,IAAI,QAAQ,OAAO,CAAC;AACnC,+BAAW,QAAQ,OAAO,IAAI,KAAK,IAAI,QAAQ,OAAO,CAAC;AACvD,4BAAQ,QAAQ,OAAO;AACvB,iCAAa,KAAK,IAAI,KAAK,IAAI,QAAQ,OAAO,CAAC,GAAG,CAAC;AAAA,kBACvD;AAEA,sBAAI,YAAY,IAAI,UAAU,OAAO,WAAW,IAAI,YAAY,SAAS;AACzE,sBAAI,aAAa,OAAO,WAAW,UAAU;AAC7C,sBAAI,SAAS,CAAC;AAEd,2BAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,wBAAI,UAAU,QAAQ,CAAC;AACvB,wBAAI,aAAa,QAAQ,MAAM;AAC/B,+BAAW,OAAO,IAAI,QAAQ,OAAO;AACrC,+BAAW,OAAO,IAAI,WAAW,KAAK,IAAI,QAAQ,OAAO,CAAC,IAAI;AAC9D,2BAAO,KAAK,UAAU;AAAA,kBAC1B;AAEA,sBAAI,aACA,SACE,KAAK,MAAM,YAAY,GAAG,IAAI,MAC9B,QACA,KAAK,MAAM,WAAW,GAAG,IAAI,MAAM;AAEzC,yBAAO;AAAA,oBACH,QAAQ;AAAA,oBACR,WAAW;AAAA,sBACP;AAAA,sBACA;AAAA,oBACJ;AAAA,oBACA;AAAA,kBACJ;AAAA,gBAEJ;AAAA;AAAA;AAAA;AAAA,gBAKA,YAAY,SAAU,SAAS,KAAK;AAEhC,sBAAI,UAAU,IAAI,WAAW,CAAC;AAC9B,sBAAI,UAAU,IAAI,WAAW,CAAC;AAC9B,sBAAI,QAAQ,IAAI;AAEhB,sBAAI,SAAS,MAAM;AACf,4BAAQ;AAAA,kBACZ;AAEA,sBAAI,YAAY,CAAC;AACjB,sBAAI,MAAM,CAAC;AACX,sBAAI,IAAI,QAAQ;AAEhB,2BAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,wBAAI,OAAO;AACX,6BAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,0BAAI,UAAU,QAAQ,CAAC;AACvB,8BAAQ,QAAQ,OAAO,IAAI,KAAK,IAAI,QAAQ,OAAO,GAAG,CAAC;AAAA,oBAC3D;AACA,wBAAI,KAAK,IAAI;AAEb,wBAAI,OAAO,CAAC;AACZ,6BAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,0BAAI,OAAO;AACX,+BAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,gCAAQ,KAAK,IAAI,QAAQ,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;AAAA,sBAC/C;AACA,2BAAK,KAAK,IAAI;AAAA,oBAClB;AACA,8BAAU,KAAK,IAAI;AAAA,kBACvB;AACA,4BAAU,KAAK,GAAG;AAElB,sBAAI,WAAW,oBAAoB,WAAW,CAAC;AAE/C,sBAAI,SAAS,CAAC;AAEd,2BAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,wBAAI,QAAQ;AACZ,wBAAI,UAAU,QAAQ,CAAC;AACvB,6BAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,+BAAS,SAAS,CAAC,IAAI,KAAK,IAAI,QAAQ,OAAO,GAAG,CAAC;AAAA,oBACvD;AACA,wBAAI,aAAa,QAAQ,MAAM;AAC/B,+BAAW,OAAO,IAAI,QAAQ,OAAO;AACrC,+BAAW,OAAO,IAAI;AACtB,2BAAO,KAAK,UAAU;AAAA,kBAC1B;AAEA,sBAAI,aAAa;AACjB,2BAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,wBAAI,IAAI,GAAG;AACP,oCAAc,KAAK,MAAM,SAAS,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,OAAO,IAAI;AAAA,oBACnG,WACS,MAAM,GAAG;AACd,oCAAc,KAAK,MAAM,SAAS,CAAC,IAAI,GAAG,IAAI,MAAM;AAAA,oBACxD,OACK;AACD,oCAAc,KAAK,MAAM,SAAS,CAAC,IAAI,GAAG,IAAI;AAAA,oBAClD;AAAA,kBACJ;AAEA,yBAAO;AAAA,oBACH,QAAQ;AAAA,oBACR,WAAW;AAAA,oBACX;AAAA,kBACJ;AAAA,gBAEJ;AAAA,cAEJ;AAQA,uBAAS,oBAAoB,QAAQ,QAAQ;AAEzC,yBAAS,IAAI,GAAG,IAAI,OAAO,SAAS,GAAG,KAAK;AACxC,sBAAI,YAAY;AAChB,2BAAS,IAAI,IAAI,GAAG,IAAI,OAAO,SAAS,GAAG,KAAK;AAC5C,wBAAI,KAAK,IAAI,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC,EAAE,SAAS,CAAC,GAAG;AACzD,kCAAY;AAAA,oBAChB;AAAA,kBACJ;AAIA,2BAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,wBAAI,OAAO,OAAO,CAAC,EAAE,CAAC;AACtB,2BAAO,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,EAAE,SAAS;AAClC,2BAAO,CAAC,EAAE,SAAS,IAAI;AAAA,kBAC3B;AACA,2BAAS,IAAI,IAAI,GAAG,IAAI,OAAO,SAAS,GAAG,KAAK;AAC5C,6BAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,6BAAO,CAAC,EAAE,CAAC,KAAK,OAAO,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC;AAAA,oBAC7D;AAAA,kBACJ;AAAA,gBACJ;AAEA,oBAAI,OAAO,IAAI,MAAM,MAAM;AAC3B,oBAAI,MAAM,OAAO,SAAS;AAC1B,yBAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,sBAAI,OAAO;AACX,2BAAS,IAAI,IAAI,GAAG,IAAI,OAAO,SAAS,GAAG,KAAK;AAC5C,4BAAQ,OAAO,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC;AAAA,kBACjC;AACA,uBAAK,CAAC,KAAK,OAAO,GAAG,EAAE,CAAC,IAAI,QAAQ,OAAO,CAAC,EAAE,CAAC;AAAA,gBAEnD;AAEA,uBAAO;AAAA,cACX;AAWA,kBAAI,aAAa,SAAU,aAAa,MAAM,YAAY;AACtD,oBAAI,MAAM,OAAO,eAAe,WAC1B,EAAE,OAAO,WAAW,IACnB,cAAc,CAAC;AAEtB,oBAAI,aAAa,oBAAoB,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC;AAE3D,oBAAI,UAAU,eAAe,MAAM,EAAE,WAAuB,CAAC;AAC7D,oBAAI,SAAS,aAAa,WAAW,EAAE,SAAS;AAAA,kBAC5C,OAAO,IAAI;AAAA,kBACX;AAAA,gBACJ,CAAC;AAGD,oBAAI,UAAU,WAAW,CAAC;AAC1B,uBAAO,OAAO,KAAK,SAAU,OAAO,OAAO;AACvC,yBAAO,MAAM,OAAO,IAAI,MAAM,OAAO;AAAA,gBACzC,CAAC;AAED,uBAAO;AAAA,cACX;AAEA,qBAAO;AAAA,YAEX,EAAE,KAAKD,UAAS,qBAAqBA,UAASD,OAAM,GAAG,kCAAkC,WAAcA,QAAO,UAAU;AAAA,UAEnH;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAErD,gBAAI;AAA8B,cAAE,gCAAgC,SAAUC,UAAS;AAEnF,kBAAI,aAAa,CAAC;AAElB,yBAAW,MAAM,oBAAoB,CAAC;AACtC,yBAAW,YAAY,oBAAoB,CAAC;AAC5C,yBAAW,OAAO,oBAAoB,EAAE;AACxC,yBAAW,SAAS,oBAAoB,EAAE;AAC1C,yBAAW,MAAM,oBAAoB,EAAE;AACvC,yBAAW,WAAW,oBAAoB,EAAE;AAC5C,yBAAW,iBAAiB,oBAAoB,CAAC;AACjD,yBAAW,MAAM,oBAAoB,EAAE;AAEvC,qBAAO;AAAA,YAEX,EAAE,KAAKD,UAAS,qBAAqBA,UAASD,OAAM,GAAG,kCAAkC,WAAcA,QAAO,UAAU;AAAA,UAEnH;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAErD,gBAAI;AAA8B,cAAE,gCAAgC,SAAUC,UAAS;AAEnF,kBAAI,SAAS,oBAAoB,CAAC;AAClC,kBAAI,WAAW,OAAO;AAQtB,uBAAS,IAAI,MAAM;AAEf,oBAAI,UAAU;AACd,yBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,sBAAI,SAAS,KAAK,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,SAAS;AACxC,8BAAU,KAAK,CAAC;AAAA,kBACpB;AAAA,gBACJ;AACA,uBAAO;AAAA,cACX;AAEA,qBAAO;AAAA,YAEX,EAAE,KAAKD,UAAS,qBAAqBA,UAASD,OAAM,GAAG,kCAAkC,WAAcA,QAAO,UAAU;AAAA,UAEnH;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAErD,gBAAI;AAA8B,cAAE,gCAAgC,SAAUC,UAAS;AAEnF,kBAAI,WAAW,oBAAoB,CAAC;AAOpC,qBAAO,SAAU,MAAM;AAEnB,oBAAI,mBAAmB,SAAS,IAAI;AAEpC,uBAAO,mBAAmB,KAAK,KAAK,gBAAgB,IAAI;AAAA,cAC5D;AAAA,YACJ,EAAE,KAAKD,UAAS,qBAAqBA,UAASD,OAAM,GAAG,kCAAkC,WAAcA,QAAO,UAAU;AAAA,UAEnH;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAErD,gBAAI;AAA8B,cAAE,gCAAgC,SAAUC,UAAS;AAEnF,kBAAI,SAAS,oBAAoB,CAAC;AAClC,kBAAI,WAAW,OAAO;AACtB,kBAAI,OAAO,oBAAoB,EAAE;AAOjC,uBAAS,eAAe,MAAM;AAE1B,oBAAI,MAAM,KAAK;AACf,oBAAI,CAAC,OAAO,MAAM,GAAG;AACjB,yBAAO;AAAA,gBACX;AACA,oBAAI,KAAK,UAAU,GAAG;AAElB,sBAAI,YAAY,KAAK,IAAI;AACzB,sBAAI,MAAM;AACV,sBAAI;AAEJ,2BAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,wBAAI,SAAS,KAAK,CAAC,CAAC,GAAG;AACnB,+BAAS,KAAK,CAAC,IAAI;AACnB,6BAAO,SAAS;AAAA,oBACpB;AAAA,kBACJ;AACA,yBAAO,OAAO,KAAK,SAAS;AAAA,gBAChC;AAAA,cACJ;AAEA,qBAAO;AAAA,YAEX,EAAE,KAAKD,UAAS,qBAAqBA,UAASD,OAAM,GAAG,kCAAkC,WAAcA,QAAO,UAAU;AAAA,UAGnH;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAErD,gBAAI;AAA8B,cAAE,gCAAgC,SAAUC,UAAS;AAEnF,kBAAI,MAAM,oBAAoB,EAAE;AAQhC,uBAAS,KAAK,MAAM;AAEhB,oBAAI,MAAM,KAAK;AAEf,oBAAI,CAAC,KAAK;AACN,yBAAO;AAAA,gBACX;AAEA,uBAAO,IAAI,IAAI,IAAI,KAAK;AAAA,cAE5B;AAEA,qBAAO;AAAA,YAGX,EAAE,KAAKD,UAAS,qBAAqBA,UAASD,OAAM,GAAG,kCAAkC,WAAcA,QAAO,UAAU;AAAA,UAEnH;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAErD,gBAAI;AAA8B,cAAE,gCAAgC,SAAUC,UAAS;AAEnF,kBAAI,SAAS,oBAAoB,CAAC;AAClC,kBAAI,WAAW,OAAO;AAQtB,uBAAS,IAAI,MAAM;AAEf,oBAAI,MAAM,KAAK;AAEf,oBAAI,CAAC,KAAK;AACN,yBAAO;AAAA,gBACX;AACA,oBAAI,UAAU;AACd,yBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,sBAAI,SAAS,KAAK,CAAC,CAAC,GAAG;AACnB,+BAAW,KAAK,CAAC;AAAA,kBACrB;AAAA,gBACJ;AACA,uBAAO;AAAA,cACX;AAEA,qBAAO;AAAA,YAEX,EAAE,KAAKD,UAAS,qBAAqBA,UAASD,OAAM,GAAG,kCAAkC,WAAcA,QAAO,UAAU;AAAA,UAEnH;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAErD,gBAAI;AAA8B,cAAE,gCAAgC,SAAUC,UAAS;AAEnF,kBAAI,WAAW,oBAAoB,EAAE;AAOrC,uBAAS,OAAO,MAAM;AAElB,uBAAO,SAAS,MAAM,GAAG;AAAA,cAC7B;AAEA,qBAAO;AAAA,YAEX,EAAE,KAAKD,UAAS,qBAAqBA,UAASD,OAAM,GAAG,kCAAkC,WAAcA,QAAO,UAAU;AAAA,UAEnH;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAErD,gBAAI;AAA8B,cAAE,gCAAgC,SAAUC,UAAS;AASnF,qBAAO,SAAU,MAAM,GAAG;AAEtB,oBAAI,MAAM,KAAK;AAEf,oBAAI,CAAC,KAAK;AACN,yBAAO;AAAA,gBACX;AACA,oBAAI,KAAK,KAAK,MAAM,GAAG;AACnB,yBAAO,KAAK,CAAC;AAAA,gBACjB;AACA,oBAAI,KAAK,GAAG;AACR,yBAAO,KAAK,MAAK,CAAC;AAAA,gBACtB;AAEA,oBAAI,KAAK,MAAM,KAAK;AACpB,oBAAI,IAAI,KAAK,MAAM,CAAC;AACpB,oBAAI,IAAI,KAAK,CAAC;AACd,oBAAI,IAAI,KAAK,IAAI,CAAC;AAClB,uBAAO,KAAK,IAAI,MAAM,IAAI;AAAA,cAC9B;AAAA,YAEJ,EAAE,KAAKD,UAAS,qBAAqBA,UAASD,OAAM,GAAG,kCAAkC,WAAcA,QAAO,UAAU;AAAA,UAEnH;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAErD,gBAAI;AAA8B,cAAE,gCAAgC,SAAUC,UAAS;AAEnF,kBAAI,SAAS,oBAAoB,CAAC;AAClC,kBAAI,WAAW,OAAO;AAQtB,uBAAS,IAAI,MAAM;AAEf,oBAAI,UAAU;AACd,yBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,sBAAI,SAAS,KAAK,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,SAAS;AACxC,8BAAU,KAAK,CAAC;AAAA,kBACpB;AAAA,gBACJ;AACA,uBAAO;AAAA,cACX;AAEA,qBAAO;AAAA,YAEX,EAAE,KAAKD,UAAS,qBAAqBA,UAASD,OAAM,GAAG,kCAAkC,WAAcA,QAAO,UAAU;AAAA,UAEnH;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAErD,gBAAI;AAA8B,cAAE,gCAAgC,SAAUC,UAAS;AAEnF,kBAAI,MAAM,oBAAoB,CAAC;AAC/B,kBAAI,MAAM,oBAAoB,EAAE;AAChC,kBAAI,WAAW,oBAAoB,EAAE;AACrC,kBAAI,YAAY,oBAAoB,CAAC;AACrC,kBAAI,cAAc,oBAAoB,CAAC;AACvC,kBAAI,iBAAiB,YAAY;AACjC,kBAAI,sBAAsB,YAAY;AACtC,kBAAI,QAAQ,oBAAoB,CAAC;AACjC,kBAAI,YAAY,MAAM;AACtB,kBAAI,MAAM,MAAM;AAChB,kBAAI,QAAQ,oBAAoB,EAAE;AAClC,kBAAI,SAAS,MAAM;AACnB,kBAAI,WAAW,oBAAoB,EAAE;AAWrC,uBAAS,YAAY,MAAM,aAAa;AACpC,oBAAI,MAAM,OAAO,gBAAgB,WAC3B,EAAE,QAAQ,YAAY,IACrB,eAAe,CAAC;AAEvB,oBAAI,YAAY,IAAI,UAAU,OACxB,gBAAgB,aAChB,gBAAgB,IAAI,MAAM;AAChC,oBAAI,aAAa,oBAAoB,IAAI,UAAU;AAEnD,oBAAI,SAAS,eAAe,MAAM;AAAA,kBAC9B;AAAA,kBACA,qBAAqB;AAAA,gBACzB,CAAC;AACD,oBAAI,WAAW,IAAI,MAAM;AACzB,oBAAI,WAAW,IAAI,MAAM;AACzB,oBAAI,aAAa,UAAU,QAAQ,UAAU,QAAQ;AACrD,oBAAI,iBAAiB,SAAS,UAAU,UAAU,UAAU;AAC5D,oBAAI,OAAO,eAAe;AAC1B,oBAAI,mBAAmB,eAAe;AAGtC,oBAAI,aAAa;AAAA;AAAA,kBAEb,EAAG,KAAK,KAAK,WAAW,IAAI,IAAI,MAAM,QAAQ,gBAAgB;AAAA,kBAC9D,EAAG,KAAK,MAAM,WAAW,IAAI,IAAI,MAAM,QAAQ,gBAAgB;AAAA,kBAC/D;AAAA,kBACA;AAAA,gBACJ;AAEA,oBAAI,MAAM,WAAW;AAErB,oBAAI,OAAO,IAAI,MAAM,MAAM,CAAC;AAE5B,yBAAS,IAAI,GAAG,KAAK,KAAK,KAAK;AAC3B,uBAAK,CAAC,IAAI,CAAC;AACX,uBAAK,CAAC,EAAE,SAAS,CAAC;AAClB,uBAAK,CAAC,EAAE,KAAK,IAAI,IACX,WAAW,IAAI,CAAC,IACf,WAAW,CAAC,IAAI,aAAc,OAC/B,WACC,WAAW,CAAC,IAAI;AACvB,uBAAK,CAAC,EAAE,KAAK,IAAI,MACX,WAAW,CAAC,IACX,WAAW,WAAW,IAAE,CAAC,MAAO,OACjC,WACA,WAAW,IAAI,CAAC,IAAI;AAAA,gBAC9B;AAEA,yBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,sBAAI,YAAY,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,UAAU;AAChD,yBAAK,OAAO,YAAY,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,OAAO,KAAK,OAAO,CAAC,CAAC;AAAA,kBACrE;AAAA,gBACJ;AAEA,oBAAI,OAAO,IAAI,MAAM,SAAU,KAAK;AAEhC,yBAAO;AAAA,oBACH,GAAG,IAAI,KAAK,IAAI,MAAM,GAAG,QAAQ,gBAAgB;AAAA,oBACjD,IAAI,OAAO;AAAA,oBACX,IAAI;AAAA,oBACJ,IAAI;AAAA,oBACJ,IAAI,KAAK,QAAQ,IAAI;AAAA,kBACzB;AAAA,gBACJ,CAAC;AAED,oBAAI,aAAa,IAAI,MAAM,SAAU,KAAK;AACtC,yBAAO,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,OAAO,MAAM;AAAA,gBAC7C,CAAC;AAED,uBAAO;AAAA,kBACH;AAAA,kBACA;AAAA,kBACA;AAAA,gBACJ;AAAA,cACJ;AAQA,kBAAI,kBAAkB;AAAA,gBAElB,YAAY,SAAU,MAAM;AAExB,sBAAI,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,CAAC;AAE3C,yBAAO,OAAO,KAAK,KAAK;AAAA,gBAC5B;AAAA,gBAEA,OAAO,SAAU,MAAMI,MAAKC,MAAK;AAE7B,yBAAO,KAAK,MAAMA,OAAMD,SAAQ,MAAM,UAAU,IAAI,IAAI,KAAK,IAAI,KAAK,QAAQ,KAAK,CAAC,EAAE;AAAA,gBAC1F;AAAA,gBAEA,kBAAkB,SAAU,MAAMA,MAAKC,MAAK;AAExC,uBAAK,KAAK,SAAS;AAEnB,yBAAO,KAAK;AAAA,qBACPA,OAAMD,SAAQ,KAAK,SAAS,MAAM,IAAI,IAAI,SAAS,MAAM,IAAI,KAAK,KAAK,IAAI,KAAK,QAAQ,KAAK,CAAC;AAAA,kBACnG;AAAA,gBACJ;AAAA,gBAEA,SAAS,SAAU,MAAM;AAErB,yBAAO,KAAK,KAAK,KAAK,IAAI,KAAK,MAAM,IAAI,KAAK,GAAG,IAAI;AAAA,gBAEzD;AAAA,cACJ;AAEA,qBAAO;AAAA,YAEX,EAAE,KAAKL,UAAS,qBAAqBA,UAASD,OAAM,GAAG,kCAAkC,WAAcA,QAAO,UAAU;AAAA,UAEnH;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAErD,gBAAI;AAA8B,cAAE,gCAAgC,SAAUC,UAAS;AAEnF,kBAAI,cAAc,oBAAoB,CAAC;AACvC,kBAAI,eAAe,YAAY;AAW/B,qBAAO,SAAU,OAAO,KAAK,MAAM,WAAW;AAE1C,oBAAI,MAAM,UAAU;AAEpB,oBAAI,MAAM,GAAG;AACT,wBAAM;AACN,0BAAQ;AACR,yBAAO;AAAA,gBACX,WACS,MAAM,GAAG;AACd,yBAAO;AAAA,gBACX,WACS,MAAM,GAAG;AACd,yBAAO,CAAC;AACR,8BAAY,aAAa,IAAI;AAAA,gBACjC,OACK;AACD,8BAAY,CAAC;AAAA,gBACjB;AAEA,oBAAI,IAAI,KAAK,OAAO,MAAM,SAAS,MAAM,QAAQ,SAAS,CAAC;AAC3D,oBAAI,QAAQ,IAAI,MAAM,IAAI,CAAC;AAC3B,yBAAS,IAAI,GAAG,IAAI,IAAI,GAAG,KAAK;AAC5B,wBAAM,CAAC,IAAI,EAAE,QAAQ,IAAI,MAAM,QAAQ,SAAS;AAAA,gBACpD;AACA,uBAAO;AAAA,cACX;AAAA,YAEJ,EAAE,KAAKD,UAAS,qBAAqBA,UAASD,OAAM,GAAG,kCAAkC,WAAcA,QAAO,UAAU;AAAA,UAEnH;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAErD,gBAAI;AAA8B,cAAE,gCAAgC,SAAUC,UAAS;AAEnF,kBAAI,aAAa,oBAAoB,CAAC;AAStC,qBAAO,SAAU,OAAO,MAAM,OAAO;AAEjC,oBAAI,QAAQ,KAAK,IAAI,OAAO,KAAK,IAAI;AACrC,oBAAI,YAAY,WAAW,iBAAiB,KAAK;AAEjD,oBAAI,QAAQ,KAAK,IAAI,IAAI,SAAS;AAClC,oBAAI,QAAQ,QAAQ;AAEpB,oBAAI,SAAS,KAAK,KAAK,EAAE,GAAG;AACxB,2BAAS;AAAA,gBACb,WACS,SAAS,KAAK,KAAK,EAAE,GAAG;AAC7B,2BAAS;AAAA,gBACb,WACQ,SAAS,KAAK,KAAK,CAAC,GAAG;AAC3B,2BAAS;AAAA,gBACb;AAEA,oBAAI,mBAAmB,YAAY,IAAI,CAAC,YAAY;AACpD,oBAAI,aAAa,EACZ,QAAQ,QAAQ,QAAQ,CAAC,OAAO,QAAQ,gBAAgB;AAG7D,uBAAO;AAAA,kBACH,MAAM;AAAA,kBACN;AAAA,gBACJ;AAAA,cACJ;AAAA,YAEJ,EAAE,KAAKD,UAAS,qBAAqBA,UAASD,OAAM,GAAG,kCAAkC,WAAcA,QAAO,UAAU;AAAA,UAGnH;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAErD,gBAAI;AAA8B,cAAE,gCAAgC,SAAUC,UAAS;AAEnF,kBAAI,aAAa,oBAAoB,CAAC;AACtC,kBAAI,kBAAkB,oBAAoB,EAAE;AAC5C,kBAAI,oBAAoB;AAExB,qBAAO;AAAA,gBAEH,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAaN,WAAW,SAAS,UAAU,QAAQ;AAClC,sBAAI,WAAW,OAAO;AACtB,sBAAI,SAAS,OAAO,UAAU,CAAC;AAC/B,sBAAI,SAAS,OAAO,UAAU;AAE9B,sBAAI,SAAS,WAAW,QAAQ,SAAS,aAAa,GAAG;AAAA,oBACrD,OAAO,OAAO;AAAA,oBACd,YAAY,gBAAgB,4BAA4B,QAAQ,OAAO,UAAU;AAAA,kBACrF,CAAC;AACD,sBAAI,SAAS,OAAO;AAEpB,sBAAI,YAAY,OAAO;AACvB,sBAAI,aAAa,MAAM;AACnB,gCAAY;AAAA,kBAChB;AAEA,sBAAI;AACJ,sBAAI,cAAc,QAAQ;AACtB,6BAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,6BAAO,CAAC,EAAE,iBAAiB,IAEtB,cAAc,WAAW,MAAM,KAC5B,cAAc,SACd,cAAc,SAAS,MAAM,OAAO,SAAS,IACjD,OAAO,aAAa;AAAA,oBAC5B;AACA,iCAAa,SAAS,sBAAsB;AAC5C,+BAAW,iBAAiB,IAAI,CAAC;AAAA,kBACrC;AAEA,yBAAO,CAAC;AAAA,oBACJ;AAAA,oBACA,MAAM;AAAA,kBACV,CAAC;AAAA,gBACL;AAAA,cACJ;AAAA,YAEJ,EAAE,KAAKD,UAAS,qBAAqBA,UAASD,OAAM,GAAG,kCAAkC,WAAcA,QAAO,UAAU;AAAA,UAGnH;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAErD,gBAAI;AAA8B,cAAE,gCAAgC,SAAUC,UAAS;AAEnF,kBAAI,YAAY,oBAAoB,CAAC;AACrC,kBAAI,aAAa,oBAAoB,CAAC;AACtC,kBAAI,aAAa,oBAAoB,EAAE;AAWvC,uBAAS,4BAA4B,iBAAiB,kBAAkB;AACpE,oBAAI,oBAAoB,MAAM;AAC1B;AAAA,gBACJ;AACA,oBAAI,WAAW,gBAAgB;AAE/B,oBAAI,UAAU,QAAQ,gBAAgB,GAAG;AACrC,sBAAI,SAAS,CAAC;AACd,2BAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAC9C,wBAAI,UAAU,SAAS,iBAAiB,iBAAiB,CAAC,CAAC;AAC3D,4CAAwB,SAAS,iBAAiB,CAAC,CAAC;AACpD,2BAAO,CAAC,IAAI,QAAQ;AAAA,kBACxB;AACA,yBAAO;AAAA,gBACX,OACK;AACD,sBAAI,UAAU,SAAS,iBAAiB,gBAAgB;AACxD,0CAAwB,SAAS,gBAAgB;AACjD,yBAAO,QAAQ;AAAA,gBACnB;AAEA,yBAAS,wBAAwBM,UAAS,WAAW;AACjD,sBAAI,CAACA,UAAS;AACV,0BAAM,IAAI,MAAM,+BAA+B,SAAS;AAAA,kBAC5D;AAAA,gBACJ;AAAA,cACJ;AAOA,uBAAS,uBAAuB,kBAAkB;AAC9C,oBAAI,UAAU,QAAQ,gBAAgB,GAAG;AACrC,sBAAI,QAAQ,CAAC;AACb,sBAAI,UAAU,CAAC;AACf,2BAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAC9C,wBAAI,OAAO,sBAAsB,iBAAiB,CAAC,CAAC;AACpD,0BAAM,KAAK,KAAK,IAAI;AACpB,4BAAQ,KAAK,KAAK,KAAK;AAAA,kBAC3B;AACA,yBAAO,EAAC,MAAM,OAAO,OAAO,QAAO;AAAA,gBACvC,WACS,oBAAoB,MAAM;AAC/B,yBAAO,sBAAsB,gBAAgB;AAAA,gBACjD;AAEA,yBAAS,sBAAsB,WAAW;AACtC,sBAAI,WAAW,SAAS,SAAS,GAAG;AAChC,2BAAO,EAAE,OAAO,UAAU;AAAA,kBAC9B,WACS,WAAW,SAAS,SAAS,KAAK,WAAW,SAAS,UAAU,KAAK,GAAG;AAC7E,2BAAO;AAAA,kBACX;AACA,wBAAM,IAAI,MAAM,0EAA0E;AAAA,gBAC9F;AAAA,cACJ;AAEA,qBAAO;AAAA,gBACH;AAAA,gBACA;AAAA,cACJ;AAAA,YACJ,EAAE,KAAKP,UAAS,qBAAqBA,UAASD,OAAM,GAAG,kCAAkC,WAAcA,QAAO,UAAU;AAAA,UAGnH;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAErD,gBAAI;AAA8B,cAAE,gCAAgC,SAAUC,UAAS;AAEnF,uBAAS,OAAO,QAAQ,QAAQ;AAC5B,oBAAI,OAAO,QAAQ;AACf,yBAAO,OAAO,QAAQ,MAAM;AAAA,gBAChC,OACK;AACD,2BAAS,OAAO,QAAQ;AACpB,wBAAI,OAAO,eAAe,GAAG,GAAG;AAC5B,6BAAO,GAAG,IAAI,OAAO,GAAG;AAAA,oBAC5B;AAAA,kBACJ;AAAA,gBACJ;AACA,uBAAO;AAAA,cACX;AAEA,uBAAS,SAAS,OAAO;AACrB,sBAAM,OAAO,OAAO;AACpB,uBAAO,SAAS,cAAe,CAAC,CAAC,SAAS,SAAS;AAAA,cACvD;AAEA,qBAAO;AAAA,gBACH;AAAA,gBACA;AAAA,cACJ;AAAA,YAEJ,EAAE,KAAKD,UAAS,qBAAqBA,UAASD,OAAM,GAAG,kCAAkC,WAAcA,QAAO,UAAU;AAAA,UAEnH;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAErD,gBAAI;AAA8B,cAAE,gCAAgC,SAAUC,UAAS;AAEnF,kBAAI,YAAY,oBAAoB,EAAE;AACtC,kBAAI,kBAAkB,oBAAoB,EAAE;AAE5C,qBAAO;AAAA,gBAEH,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAON,WAAW,SAAS,UAAU,QAAQ;AAClC,sBAAI,WAAW,OAAO;AACtB,sBAAI,SAAS,OAAO,UAAU,CAAC;AAE/B,sBAAI,SAAS,UAAU,SAAS,aAAa,GAAG;AAAA,oBAC5C,QAAQ,OAAO;AAAA,oBACf,YAAY,gBAAgB,4BAA4B,QAAQ,OAAO,UAAU;AAAA,kBACrF,CAAC;AAED,yBAAO,CAAC;AAAA,oBACJ,YAAY,CAAC,cAAc,UAAU,MAAM,MAAM,iBAAiB;AAAA,oBAClE,MAAM,OAAO;AAAA,kBACjB,GAAG;AAAA,oBACC,MAAM,OAAO;AAAA,kBACjB,CAAC;AAAA,gBACL;AAAA,cACJ;AAAA,YAEJ,EAAE,KAAKD,UAAS,qBAAqBA,UAASD,OAAM,GAAG,kCAAkC,WAAcA,QAAO,UAAU;AAAA,UAGnH;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS,qBAAqB;AAErD,gBAAI;AAA8B,cAAE,gCAAgC,SAAUC,UAAS;AAEnF,kBAAI,aAAa,oBAAoB,CAAC;AACtC,kBAAI,aAAa,oBAAoB,CAAC;AACtC,kBAAI,kBAAkB,oBAAoB,EAAE;AAE5C,kBAAI,WAAW,WAAW;AAE1B,qBAAO;AAAA,gBAEH,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAaN,WAAW,SAAS,UAAU,QAAQ;AAClC,sBAAI,WAAW,OAAO;AACtB,sBAAI,SAAS,OAAO,UAAU,CAAC;AAC/B,sBAAI,eAAe,OAAO;AAE1B,sBAAI,CAAC,SAAS,YAAY,KAAK,gBAAgB,GAAG;AAC9C,0BAAM,IAAI,MAAM,iFAAiF;AAAA,kBACrG;AAEA,sBAAI,iBAAiB,GAAG;AACpB,2BAAO,CAAC,CACR,GAAG;AAAA,sBACC,MAAM,CAAC;AAAA,oBACX,CAAC;AAAA,kBACL;AAEA,sBAAI,8BAA8B,gBAAgB;AAAA,oBAC9C,OAAO;AAAA,kBACX;AACA,sBAAI,2BAA2B,gBAAgB;AAAA,oBAC3C,OAAO;AAAA,kBACX;AAEA,sBAAI,+BAA+B,MAAM;AACrC,0BAAM,IAAI,MAAM,sDAAsD;AAAA,kBAC1E;AAEA,sBAAI,SAAS,WAAW,mBAAmB,SAAS,aAAa,GAAG;AAAA,oBAChE;AAAA,oBACA,YAAY;AAAA,oBACZ,YAAY,gBAAgB,4BAA4B,QAAQ,OAAO,UAAU;AAAA,oBACjF,YAAY,WAAW,WAAW;AAAA,oBAClC,6BAA6B,4BAA4B;AAAA,oBACzD,2BAA2B,4BAA4B,CAAC,GAAG;AAAA,kBAC/D,CAAC;AAED,sBAAI,eAAe,SAAS,sBAAsB;AAClD,sBAAI,gBAAgB,CAAC;AACrB,2BAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,wBAAI,gBAAgB,aAAa,CAAC;AAClC,kCAAc,KAAK,cAAc,IAAI;AAAA,kBACzC;AAIA,gCAAc,4BAA4B,KAAK,IAAI,4BAA4B;AAE/E,sBAAI,0BAA0B;AAC1B,6BAAS,IAAI,GAAG,IAAI,yBAAyB,MAAM,QAAQ,KAAK;AAC5D,0BAAI,yBAAyB,KAAK,CAAC,KAAK,MAAM;AAC1C,sCAAc,yBAAyB,MAAM,CAAC,CAAC,IAAI,yBAAyB,KAAK,CAAC;AAAA,sBACtF;AAAA,oBACJ;AAAA,kBACJ;AAEA,yBAAO,CAAC;AAAA,oBACJ,YAAY;AAAA,oBACZ,MAAM,OAAO;AAAA,kBACjB,GAAG;AAAA,oBACC,MAAM,OAAO;AAAA,kBACjB,CAAC;AAAA,gBACL;AAAA,cACJ;AAAA,YAEJ,EAAE,KAAKD,UAAS,qBAAqBA,UAASD,OAAM,GAAG,kCAAkC,WAAcA,QAAO,UAAU;AAAA,UAGnH;AAAA;AAAA,QACG,CAAC;AAAA;AAAA,IACV,CAAC;AAAA;AAAA;;;AC58DD;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;", "names": ["module", "exports", "require", "dist", "i", "sum", "min", "max", "dimInfo"]}