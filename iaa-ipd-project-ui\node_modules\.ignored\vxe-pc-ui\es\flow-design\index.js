import { VxeUI } from '@vxe-ui/core';
import VxeFlowDesignComponent from './src/flow-design';
import { dynamicApp } from '../dynamics';
export const VxeFlowDesign = Object.assign({}, VxeFlowDesignComponent, {
    install(app) {
        app.component(VxeFlowDesignComponent.name, VxeFlowDesignComponent);
    }
});
dynamicApp.use(VxeFlowDesign);
VxeUI.component(VxeFlowDesignComponent);
export const FlowDesign = VxeFlowDesign;
export default VxeFlowDesign;
