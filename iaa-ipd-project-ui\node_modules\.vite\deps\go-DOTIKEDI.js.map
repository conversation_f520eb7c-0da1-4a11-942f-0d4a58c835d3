{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/go/go.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    comments: {\n        lineComment: '//',\n        blockComment: ['/*', '*/']\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '`', close: '`', notIn: ['string'] },\n        { open: '\"', close: '\"', notIn: ['string'] },\n        { open: \"'\", close: \"'\", notIn: ['string', 'comment'] }\n    ],\n    surroundingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '`', close: '`' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" }\n    ]\n};\nexport var language = {\n    defaultToken: '',\n    tokenPostfix: '.go',\n    keywords: [\n        'break',\n        'case',\n        'chan',\n        'const',\n        'continue',\n        'default',\n        'defer',\n        'else',\n        'fallthrough',\n        'for',\n        'func',\n        'go',\n        'goto',\n        'if',\n        'import',\n        'interface',\n        'map',\n        'package',\n        'range',\n        'return',\n        'select',\n        'struct',\n        'switch',\n        'type',\n        'var',\n        'bool',\n        'true',\n        'false',\n        'uint8',\n        'uint16',\n        'uint32',\n        'uint64',\n        'int8',\n        'int16',\n        'int32',\n        'int64',\n        'float32',\n        'float64',\n        'complex64',\n        'complex128',\n        'byte',\n        'rune',\n        'uint',\n        'int',\n        'uintptr',\n        'string',\n        'nil'\n    ],\n    operators: [\n        '+',\n        '-',\n        '*',\n        '/',\n        '%',\n        '&',\n        '|',\n        '^',\n        '<<',\n        '>>',\n        '&^',\n        '+=',\n        '-=',\n        '*=',\n        '/=',\n        '%=',\n        '&=',\n        '|=',\n        '^=',\n        '<<=',\n        '>>=',\n        '&^=',\n        '&&',\n        '||',\n        '<-',\n        '++',\n        '--',\n        '==',\n        '<',\n        '>',\n        '=',\n        '!',\n        '!=',\n        '<=',\n        '>=',\n        ':=',\n        '...',\n        '(',\n        ')',\n        '',\n        ']',\n        '{',\n        '}',\n        ',',\n        ';',\n        '.',\n        ':'\n    ],\n    // we include these common regular expressions\n    symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n    escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n    // The main tokenizer for our languages\n    tokenizer: {\n        root: [\n            // identifiers and keywords\n            [\n                /[a-zA-Z_]\\w*/,\n                {\n                    cases: {\n                        '@keywords': { token: 'keyword.$0' },\n                        '@default': 'identifier'\n                    }\n                }\n            ],\n            // whitespace\n            { include: '@whitespace' },\n            // [[ attributes ]].\n            [/\\[\\[.*\\]\\]/, 'annotation'],\n            // Preprocessor directive\n            [/^\\s*#\\w+/, 'keyword'],\n            // delimiters and operators\n            [/[{}()\\[\\]]/, '@brackets'],\n            [/[<>](?!@symbols)/, '@brackets'],\n            [\n                /@symbols/,\n                {\n                    cases: {\n                        '@operators': 'delimiter',\n                        '@default': ''\n                    }\n                }\n            ],\n            // numbers\n            [/\\d*\\d+[eE]([\\-+]?\\d+)?/, 'number.float'],\n            [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, 'number.float'],\n            [/0[xX][0-9a-fA-F']*[0-9a-fA-F]/, 'number.hex'],\n            [/0[0-7']*[0-7]/, 'number.octal'],\n            [/0[bB][0-1']*[0-1]/, 'number.binary'],\n            [/\\d[\\d']*/, 'number'],\n            [/\\d/, 'number'],\n            // delimiter: after number because of .\\d floats\n            [/[;,.]/, 'delimiter'],\n            // strings\n            [/\"([^\"\\\\]|\\\\.)*$/, 'string.invalid'],\n            [/\"/, 'string', '@string'],\n            [/`/, 'string', '@rawstring'],\n            // characters\n            [/'[^\\\\']'/, 'string'],\n            [/(')(@escapes)(')/, ['string', 'string.escape', 'string']],\n            [/'/, 'string.invalid']\n        ],\n        whitespace: [\n            [/[ \\t\\r\\n]+/, ''],\n            [/\\/\\*\\*(?!\\/)/, 'comment.doc', '@doccomment'],\n            [/\\/\\*/, 'comment', '@comment'],\n            [/\\/\\/.*$/, 'comment']\n        ],\n        comment: [\n            [/[^\\/*]+/, 'comment'],\n            // [/\\/\\*/, 'comment', '@push' ],    // nested comment not allowed :-(\n            // [/\\/\\*/,    'comment.invalid' ],    // this breaks block comments in the shape of /* //*/\n            [/\\*\\//, 'comment', '@pop'],\n            [/[\\/*]/, 'comment']\n        ],\n        //Identical copy of comment above, except for the addition of .doc\n        doccomment: [\n            [/[^\\/*]+/, 'comment.doc'],\n            // [/\\/\\*/, 'comment.doc', '@push' ],    // nested comment not allowed :-(\n            [/\\/\\*/, 'comment.doc.invalid'],\n            [/\\*\\//, 'comment.doc', '@pop'],\n            [/[\\/*]/, 'comment.doc']\n        ],\n        string: [\n            [/[^\\\\\"]+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/\\\\./, 'string.escape.invalid'],\n            [/\"/, 'string', '@pop']\n        ],\n        rawstring: [\n            [/[^\\`]/, 'string'],\n            [/`/, 'string', '@pop']\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC7B;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EAC1D;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,WAAW;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA;AAAA,EAEA,SAAS;AAAA,EACT,SAAS;AAAA;AAAA,EAET,WAAW;AAAA,IACP,MAAM;AAAA;AAAA,MAEF;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,aAAa,EAAE,OAAO,aAAa;AAAA,YACnC,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB,CAAC,cAAc,YAAY;AAAA;AAAA,MAE3B,CAAC,YAAY,SAAS;AAAA;AAAA,MAEtB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,oBAAoB,WAAW;AAAA,MAChC;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,cAAc;AAAA,YACd,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,CAAC,0BAA0B,cAAc;AAAA,MACzC,CAAC,4BAA4B,cAAc;AAAA,MAC3C,CAAC,iCAAiC,YAAY;AAAA,MAC9C,CAAC,iBAAiB,cAAc;AAAA,MAChC,CAAC,qBAAqB,eAAe;AAAA,MACrC,CAAC,YAAY,QAAQ;AAAA,MACrB,CAAC,MAAM,QAAQ;AAAA;AAAA,MAEf,CAAC,SAAS,WAAW;AAAA;AAAA,MAErB,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,KAAK,UAAU,SAAS;AAAA,MACzB,CAAC,KAAK,UAAU,YAAY;AAAA;AAAA,MAE5B,CAAC,YAAY,QAAQ;AAAA,MACrB,CAAC,oBAAoB,CAAC,UAAU,iBAAiB,QAAQ,CAAC;AAAA,MAC1D,CAAC,KAAK,gBAAgB;AAAA,IAC1B;AAAA,IACA,YAAY;AAAA,MACR,CAAC,cAAc,EAAE;AAAA,MACjB,CAAC,gBAAgB,eAAe,aAAa;AAAA,MAC7C,CAAC,QAAQ,WAAW,UAAU;AAAA,MAC9B,CAAC,WAAW,SAAS;AAAA,IACzB;AAAA,IACA,SAAS;AAAA,MACL,CAAC,WAAW,SAAS;AAAA;AAAA;AAAA,MAGrB,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,SAAS,SAAS;AAAA,IACvB;AAAA;AAAA,IAEA,YAAY;AAAA,MACR,CAAC,WAAW,aAAa;AAAA;AAAA,MAEzB,CAAC,QAAQ,qBAAqB;AAAA,MAC9B,CAAC,QAAQ,eAAe,MAAM;AAAA,MAC9B,CAAC,SAAS,aAAa;AAAA,IAC3B;AAAA,IACA,QAAQ;AAAA,MACJ,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,UAAU,MAAM;AAAA,IAC1B;AAAA,IACA,WAAW;AAAA,MACP,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,KAAK,UAAU,MAAM;AAAA,IAC1B;AAAA,EACJ;AACJ;", "names": []}