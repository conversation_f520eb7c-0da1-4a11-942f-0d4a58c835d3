<template>
  <Dialog v-model="visible" title="文件模板维护" width="60%">
    <el-form
      label-width="100px"
      :model="formData"
      :rules="formRules"
      class="custom-form custom-border-form"
    >
      <el-form-item label="模板编码" prop="templateCode">
        <el-input v-model="formData.templateCode" />
      </el-form-item>
      <el-form-item label="模板名称" prop="name">
        <el-input v-model="formData.name" />
      </el-form-item>
      <el-form-item label="内容">
        <el-button @click="jsonFormat" size="small" type="primary" plain>格式化</el-button>
        <v-ace-editor
          v-model:value="formData.content"
          lang="json"
          theme="github"
          :readonly="false"
          @init="editorInit"
          class="w-100% min-h-300px"
        />
      </el-form-item>
      <el-form-item label="模板文件">
        <vxe-upload
          class="!w-100%"
          v-model="attachment"
          size="small"
          :upload-method="uploadMethod"
          :remove-method="removeMethod"
          :preview-method="previewMethod"
          single-mode
          show-progress
        />
      </el-form-item>
    </el-form>
    <OfficeEditor ref="officeEditorRef" has-dialog :download="false" />
    <template #footer>
      <el-button type="primary" :loading="loading" @click="onSubmit"> 保存 </el-button>
    </template>
  </Dialog>
  <Dialog v-model="editorVisible" width="100%">
    <AmisEditor />
  </Dialog>
</template>

<script lang="ts" setup>
import { VAceEditor } from 'vue3-ace-editor'
import 'ace-builds/src-noconflict/mode-json' // Load the language definition file used below
import 'ace-builds/src-noconflict/theme-github' // Load the theme definition file used below
import { SpecificationApi } from '@/api/project/specification'
import * as FileApi from '@/api/infra/file'

const visible = ref(false)
const editorVisible = ref(true)
const loading = ref(false)
const formData = ref({
  id: undefined,
  templateCode: undefined,
  name: undefined,
  version: undefined,
  content: '',
  infraFileId: undefined
})
const formRules = reactive({
  templateCode: [{ required: true, message: '模板编码不能为空', trigger: 'blur' }],
  name: [{ required: true, message: '模板名称不能为空', trigger: 'blur' }]
})

const attachment = ref<any>(undefined)
const message = useMessage()
const officeEditorRef = ref()

const uploadMethod = ({ file }) => {
  // 处理文件名后缀是数字的情况，用于适配Creo文件小版本问题
  let processedFile = file
  if (file.name) {
    const fileName = file.name
    // 使用正则表达式匹配并移除文件名末尾的数字后缀
    const newName = fileName.replace(/(\.\d+)+$/, '')

    // 如果文件名发生了变化，则创建一个新的 File 对象
    if (newName !== fileName) {
      processedFile = new File([file], newName, {
        type: file.type,
        lastModified: file.lastModified
      })
    }
  }
  return FileApi.updateFile({ file: processedFile }).then((res) => {
    message.success('上传成功')
    return {
      infraFileId: res.data.id,
      name: res.data.name,
      url: res.data.url,
      creator: res.data.creator,
      createTime: res.data.createTime
    }
  })
}

const removeMethod = async ({ option }) => {
  await message.delConfirm()
  // await FileApi.deleteFile(option.infraFileId)
  message.success('删除成功')
}

const previewMethod = ({ option }) => {
  officeEditorRef.value?.open(option.infraFileId, option.name)
}

// JSON格式化
const jsonFormat = () => {
  try {
    formData.value.content = JSON.stringify(JSON.parse(formData.value.content), null, 2)
  } catch (e) {
    console.log(e)
  }
}

// 编辑器初始化函数
const editorInit = (editor) => {
  // 启用搜索功能
  editor.find('') // 初始化搜索框

  // 启用语法高亮增强功能
  editor.setHighlightActiveLine(true)
  editor.setHighlightSelectedWord(true)

  // 调用格式化函数
  jsonFormat()
}

const emits = defineEmits(['success'])

const openForm = async (row?: any) => {
  visible.value = true
  if (row) {
    formData.value = row
    if (formData.value.infraFileId) {
      const tempfile = await FileApi.get(formData.value.infraFileId)
      attachment.value = {
        infraFileId: tempfile.id,
        name: tempfile.name,
        url: tempfile.url,
        creator: tempfile?.creator,
        createTime: tempfile?.createTime
      }
    }
  } else {
    formData.value = {
      id: undefined,
      templateCode: undefined,
      name: undefined,
      version: undefined,
      content: '',
      infraFileId: undefined
    }
    attachment.value = undefined
  }
}

const onSubmit = async () => {
  loading.value = true
  try {
    formData.value.infraFileId = attachment.value?.infraFileId

    await SpecificationApi.createDocTemplate(formData.value)
    message.success('保存成功')
    visible.value = false
    emits('success')
  } finally {
    loading.value = false
  }
}

defineExpose({
  openForm
})
</script>

<style lang="scss" scoped>
:deep(.vxe-upload--file-item-name) {
  font-size: 1rem !important;
  color: var(--el-color-primary);
}
</style>
