{"version": 3, "sources": ["../../veaury/dist/veaury.esm.js"], "sourcesContent": ["import React__default,{createElement,Fragment,version,Component,forwardRef,lazy,createContext,useContext}from\"react\";import{reactive,getCurrentInstance,h,Fragment as Fragment$1,Teleport,createApp,defineAsyncComponent,provide,inject,Text,Comment}from\"vue\";import ReactD<PERSON>,{createPortal}from\"react-dom\";function ownKeys(t,e){var r,n=Object.keys(t);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(t),e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)),n}function _objectSpread2(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(r),!0).forEach(function(e){_defineProperty(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function _typeof(e){return(_typeof=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e})(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError(\"Cannot call a class as a function\")}function _defineProperties(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),Object.defineProperty(e,\"prototype\",{writable:!1}),e}function _defineProperty(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r,n=arguments[t];for(r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function _inherits(e,t){if(\"function\"!=typeof t&&null!==t)throw new TypeError(\"Super expression must either be null or a function\");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,\"prototype\",{writable:!1}),t&&_setPrototypeOf(e,t)}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function _isNativeReflectConstruct(){if(\"undefined\"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if(\"function\"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};for(var r,n={},o=Object.keys(e),a=0;a<o.length;a++)r=o[a],0<=t.indexOf(r)||(n[r]=e[r]);return n}function _objectWithoutProperties(e,t){if(null==e)return{};var r,n=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols)for(var o=Object.getOwnPropertySymbols(e),a=0;a<o.length;a++)r=o[a],0<=t.indexOf(r)||Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r]);return n}function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");return e}function _possibleConstructorReturn(e,t){if(t&&(\"object\"==typeof t||\"function\"==typeof t))return t;if(void 0!==t)throw new TypeError(\"Derived constructors may only return object or undefined\");return _assertThisInitialized(e)}function _createSuper(r){var n=_isNativeReflectConstruct();return function(){var e,t=_getPrototypeOf(r);return _possibleConstructorReturn(this,n?(e=_getPrototypeOf(this).constructor,Reflect.construct(t,arguments,e)):t.apply(this,arguments))}}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _arrayWithHoles(e){if(Array.isArray(e))return e}function _iterableToArray(e){if(\"undefined\"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e[\"@@iterator\"])return Array.from(e)}function _iterableToArrayLimit(e,t){var r=null==e?null:\"undefined\"!=typeof Symbol&&e[Symbol.iterator]||e[\"@@iterator\"];if(null!=r){var n,o,a=[],i=!0,u=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){u=!0,o=e}finally{try{i||null==r.return||r.return()}finally{if(u)throw o}}return a}}function _unsupportedIterableToArray(e,t){var r;if(e)return\"string\"==typeof e?_arrayLikeToArray(e,t):\"Map\"===(r=\"Object\"===(r=Object.prototype.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:r)||\"Set\"===r?Array.from(e):\"Arguments\"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray(e,t):void 0}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function _nonIterableSpread(){throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\")}function _nonIterableRest(){throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\")}function _toPrimitive(e,t){if(\"object\"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0===r)return(\"string\"===t?String:Number)(e);r=r.call(e,t||\"default\");if(\"object\"!=typeof r)return r;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}function _toPropertyKey(e){e=_toPrimitive(e,\"string\");return\"symbol\"==typeof e?e:String(e)}var originOptions={react:{componentWrap:\"div\",slotWrap:\"div\",componentWrapAttrs:{__use_react_component_wrap:\"\",style:{all:\"unset\"}},slotWrapAttrs:{__use_react_slot_wrap:\"\",style:{all:\"unset\"}},vueNamedSlotsKey:[\"node:\"]},vue:{componentWrapHOC:function(t){return function(){var e=(0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}).portals;return createElement(Fragment,null,t,(void 0===e?[]:e).map(function(e){var t=e.Portal,e=e.key;return createElement(t,{key:e})}))}},componentWrapAttrs:{\"data-use-vue-component-wrap\":\"\",style:{all:\"unset\"}},slotWrapAttrs:{\"data-use-vue-slot-wrap\":\"\",style:{all:\"unset\"}}}};function setOptions(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{react:{},vue:{}},t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:originOptions,r=2<arguments.length?arguments[2]:void 0,t=(e.vue||(e.vue={}),e.react||(e.react={}),[t,_objectSpread2(_objectSpread2({},e),{},{react:_objectSpread2(_objectSpread2(_objectSpread2({},t.react),e.react),{},{componentWrapAttrs:_objectSpread2(_objectSpread2({},t.react.componentWrapAttrs),e.react.componentWrapAttrs),slotWrapAttrs:_objectSpread2(_objectSpread2({},t.react.slotWrapAttrs),e.react.slotWrapAttrs)}),vue:_objectSpread2(_objectSpread2(_objectSpread2({},t.vue),e.vue),{},{componentWrapAttrs:_objectSpread2(_objectSpread2({},t.vue.componentWrapAttrs),e.vue.componentWrapAttrs),slotWrapAttrs:_objectSpread2(_objectSpread2({},t.vue.slotWrapAttrs),e.vue.slotWrapAttrs)})})]);return r&&t.unshift({}),Object.assign.apply(this,t)}var domMethods=[\"getElementById\",\"getElementsByClassName\",\"getElementsByTagName\",\"getElementsByTagNameNS\",\"querySelector\",\"querySelectorAll\"],domTopObject={Document:{},Element:{}};function overwriteDomMethods(i){Object.keys(domTopObject).forEach(function(e){domMethods.forEach(function(o){var a=window[e].prototype[o];domTopObject[e][o]=a,window[e].prototype[o]=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=a.apply(this,t);return n&&(n.constructor!==NodeList||n.constructor===NodeList&&0<n.length)?n:Element.prototype[o].apply(i,t)}})})}function recoverDomMethods(){Object.keys(domTopObject).forEach(function(t){domMethods.forEach(function(e){window[t].prototype[e]=domTopObject[t][e]})})}var _excluded=[\"ref\"],_excluded2=[\"key\"],_excluded3=[\"hashList\"],ReactMajorVersion=parseInt(version);function toRaws(e){return e}var FunctionComponentWrap=function(){_inherits(r,Component);var t=_createSuper(r);function r(e){return _classCallCheck(this,r),t.call(this,e)}return _createClass(r,[{key:\"render\",value:function(){var e=this.props.component,t=this.props.passedProps,t=(t.ref,_objectWithoutProperties(t,_excluded));return createElement(e,t,this.props.children)}}]),r}(),createReactContainer=function(p,_,f){var e=function(){_inherits(l,Component);var r=_createSuper(l);function l(e){var t;return _classCallCheck(this,l),(t=r.call(this,e)).state=_objectSpread2(_objectSpread2({},e),_.isSlots?{children:p}:{}),t.setRef=t.setRef.bind(_assertThisInitialized(t)),t.vueInReactCall=t.vueInReactCall.bind(_assertThisInitialized(t)),(t.__veauryVueWrapperRef__=f).__veauryVueInReactCall__=t.vueInReactCall,t}return _createClass(l,[{key:\"reactPropsLinkToVueInstance\",value:function(t){Object.keys(t).forEach(function(e){f[e]||(f[e]=t[e])}),Object.getOwnPropertyNames(t.__proto__).filter(function(e){return[\"constructor\",\"render\"].indexOf(e)<0}).forEach(function(e){f[e]||(f[e]=t[e])})}},{key:\"setRef\",value:function(e){var t=this;e&&(f.__veauryReactRef__=e,this.reactPropsLinkToVueInstance(e),Promise.resolve().then(function(){return t.reactPropsLinkToVueInstance(e)}),(this.setRef.current=e).__veauryVueWrapperRef__=f)}},{key:\"createSlot\",value:function(r){return{originVNode:r,inheritAttrs:!1,__fromReactSlot:!0,render:function(){var e,t;return 1===(null==(e=r=(r=(null==(t=this.$slots)||null==(e=t.default)?void 0:e.call(t))||r)instanceof Function?r(this):r)?void 0:e.length)&&null!=(t=r[0])&&t.data&&((e=this.$attrs).key,t=_objectWithoutProperties(e,_excluded2),r[0].props=_objectSpread2(_objectSpread2({},t),r[0].props)),r}}}},{key:\"componentWillUnmount\",value:function(){f.__veauryReactRef__&&(f.__veauryReactRef__.__veauryVueWrapperRef__=null,f.__veauryReactRef__=null)}},{key:\"vueInReactCall\",value:function(e){var r=this,n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};return(2<arguments.length?arguments[2]:void 0)&&e&&e[0]?e.map(function(e,t){return applyVueInReact(r.createSlot(e instanceof Function?e:[e]),_objectSpread2(_objectSpread2(_objectSpread2({},_),n),{},{isSlots:!0,wrapInstance:f})).render({key:(null==e||null==(e=e.data)?void 0:e.key)||t})}):applyVueInReact(this.createSlot(e),_objectSpread2(_objectSpread2(_objectSpread2({},_),n),{},{isSlots:!0,wrapInstance:f})).render()}},{key:\"render\",value:function(){var e,t,r,n=this,o=this.state,a=o.hashList,i=_objectWithoutProperties(o,_excluded3),u={},c={};for(e in i)t=e,r=void 0,i.hasOwnProperty(t)&&null!=i[t]&&(i[t].__slot?(i[t].reactSlot?i[t]=i[t].reactSlot:(r=i[t],_.defaultSlotsFormatter&&i[t].__trueChildren?(i[t].__top__=n.__veauryVueWrapperRef__,i[t]=_.defaultSlotsFormatter(i[t].__trueChildren,n.vueInReactCall,a),i[t]instanceof Array?i[t]=_toConsumableArray(i[t]):-1<[\"string\",\"number\"].indexOf(_typeof(i[t]))?i[t]=[i[t]]:\"object\"===_typeof(i[t])&&(i[t]=_objectSpread2({},i[t]))):i[t]=_objectSpread2({},applyVueInReact(n.createSlot(i[t]),_objectSpread2(_objectSpread2({},_),{},{isSlots:!0,wrapInstance:f})).render()),i[t].vueFunction=r),u[t]=i[t]):i[t].__scopedSlot&&(i[t]=i[t](n.createSlot),c[t]=i[t]));var s,o={};return o.ref=this.setRef,_.isSlots?this.state.children||this.props.children:(s=i,s=_objectSpread2(_objectSpread2(_objectSpread2({},s=_.defaultPropsFormatter?_.defaultPropsFormatter(i,this.vueInReactCall,a):s),u),c),Object.getPrototypeOf(p)!==Function.prototype&&(\"object\"!==_typeof(p)||p.render)||l.catchVueRefs()?(Object.getPrototypeOf(p)===Function.prototype&&delete o.ref,createElement(p,_extends({},s,o))):createElement(FunctionComponentWrap,_extends({passedProps:s,component:p},o),s.children))}}],[{key:\"catchVueRefs\",value:function(){if(f.$parent)for(var e in f.$parent.$refs)if(f.$parent.$refs[e]===f)return!0;return!1}}]),l}();return _defineProperty(e,\"displayName\",\"applyReact_\".concat(p.displayName||p.name||\"Component\")),e};function applyReactInVue(m){var b=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};return m.__esModule&&m.default&&(m=m.default),b.isSlots&&(m=m()),b=setOptions(b,void 0,!0),{originReactComponent:m,setup:function(e,t){var r,n,o,a;if(!b.isSlots)return r={},n=reactive({}),o=getCurrentInstance(),\"function\"==typeof(a=b.useInjectPropsFromWrapper||m.__veauryInjectPropsFromWrapper__)&&(\"function\"!=typeof(a=a.call(o.proxy,e))?(Object.assign(n,a),r.__veauryInjectedProps__=n):o.proxy.__veauryInjectedComputed__=a),r},data:function(){return{VEAURY_Portals:[]}},created:function(){this.__veauryPortalKeyPool__=[],this.__veauryMaxPortalCount__=0},computed:{__veauryInjectedProps__:function(){var e;return null==(e=this.__veauryInjectedComputed__)?void 0:e.call(this)}},render:function(){var e=h(b.react.componentWrap,_objectSpread2({ref:\"react\"},b.react.componentWrapAttrs||{}),this.VEAURY_Portals.map(function(e){var t=e.Portal,e=e.key;return t(h,e)}));return this.__veauryCheckReactSlot__(this.$slots),e},methods:{__veauryCheckReactSlot__:function(i){var u=this;function c(e,t,r){return t[r]&&(e[r]=t[r],1)}Object.keys(i).forEach(function(e){try{var t,r,n,o=i[e],a=o.apply(u,o.__reactArgs||[{}]);(o.__trueChildren=a).forEach(function(e){e.children&&u.__veauryCheckReactSlot__(e.children)}),1!==a.length||c(o,r=a[0],\"reactSlot\")||c(o,r,\"reactFunction\")||r.type!==Fragment$1||1!==(null==(t=r.children)?void 0:t.length)||c(o,n=r.children[0],\"reactSlot\")||c(o,n,\"reactFunction\")}catch(e){}})},__veauryPushVuePortal__:function(e){var t=this.__veauryPortalKeyPool__.shift()||this.__veauryMaxPortalCount__++;this.VEAURY_Portals.push({Portal:e,key:t})},__veauryRemoveVuePortal__:function(r){var n,e=this.VEAURY_Portals.find(function(e,t){if(e.Portal===r)return n=t,!0});this.__veauryPortalKeyPool__.push(e.key),this.VEAURY_Portals.splice(n,1)},__veauryGetScopeSlot__:function(i,u,t){var c=this;function e(a){function e(){for(var e,t=this,r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return i.reactFunction?i.reactFunction.apply(this,n):b.defaultSlotsFormatter?((e=i.apply(this,n)).__top__=c,(e=b.defaultSlotsFormatter(e,c.__veauryVueInReactCall__,u))instanceof Array||-1<_typeof(e).indexOf(\"string\",\"number\")?e=_toConsumableArray(e):\"object\"===_typeof(e)&&(e=_objectSpread2({},e)),e):applyVueInReact(a(function(){return i.apply(t,n)}),_objectSpread2(_objectSpread2({},b),{},{isSlots:!0,wrapInstance:c})).render()}return b.pureTransformer&&t?e.vueFunction=t:e.vueFunction=i,e}return e.__scopedSlot=!0,e},__veaurySyncUpdateProps__:function(e){this.__veauryReactInstance__&&this.__veauryReactInstance__.setState(e)},__veauryMountReactComponent__:function(e,t){var r,n,o=this,a=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},i={},u=[],c=this.$.vnode.scopeId,s=(c&&(i[c]=\"\",u.push(c)),{}),l={};if(!e||null!=t&&t.slot)for(var p in this.$slots||{})(function(t){var e;o.$slots.hasOwnProperty(t)&&null!=o.$slots[t]&&((e=b.react.vueNamedSlotsKey.find(function(e){return 0===t.indexOf(e)}))||\"default\"===t?(e=t.replace(new RegExp(\"^\".concat(e)),\"\"),s[e]=o.$slots[t],s[e].__slot=!0):l[t]=o.__veauryGetScopeSlot__(o.$slots[t],u,null==(e=o.$.vnode)||null==(e=e.children)?void 0:e[t]))})(p);(!e||null!=t&&t.slot)&&(n=_objectSpread2({},s),r=n.default,delete n.default),this.__veauryLast__=this.__veauryLast__||{},this.__veauryLast__.slot=this.__veauryLast__.slot||{},this.__veauryLast__.attrs=this.__veauryLast__.attrs||{};var _={slot:function(){o.__veauryLast__.slot=_objectSpread2(_objectSpread2(_objectSpread2({},r?{children:r}:{children:null}),n),l)},attrs:function(){o.__veauryLast__.attrs=o.$attrs}};if(t&&Object.keys(t).forEach(function(e){return _[e]()}),e){function f(){o.__veauryReactInstance__&&o.__veauryReactInstance__.setState(function(t){return Object.keys(t).forEach(function(e){b.isSlots&&\"children\"===e||delete t[e]}),_objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({},o.__veauryCache__),toRaws(o.__veauryInjectedProps__)),!b.isSlots&&o.__veauryLast__.slot),toRaws(o.__veauryLast__.attrs))}),o.__veauryCache__=null}!this.microTaskUpdate||this.__veauryCache__||this.$nextTick(function(){f(),o.microTaskUpdate=!1}),this.macroTaskUpdate&&(clearTimeout(this.updateTimer),this.updateTimer=setTimeout(function(){clearTimeout(o.updateTimer),f(),o.macroTaskUpdate=!1})),this.__veauryCache__=_objectSpread2(_objectSpread2({},this.__veauryCache__||{}),_objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({},a),this.$attrs.class?{className:this.$attrs.class}:{}),_objectSpread2({},i)),{},{hashList:u},this.$attrs.style?{style:this.$attrs.style}:{})),this.macroTaskUpdate||this.microTaskUpdate||f()}else{_.slot(),_.attrs();var c=createReactContainer(m,b,this),d=createElement(c,_extends({},toRaws(this.$attrs),toRaws(this.__veauryInjectedProps__),{children:r},n,l,this.$attrs.class?{className:this.$attrs.class}:{},i,{hashList:u},this.$attrs.style?{style:this.$attrs.style}:{},{ref:function(e){return o.__veauryReactInstance__=e}})),y=this.$refs.react,v=b.wrapInstance;if(v)(v=b.wrapInstance).__veauryVueWrapperRef__=this;else for(var h=this.$parent;h;){if(h.parentReactWrapperRef){v=h.parentReactWrapperRef;break}if(h.reactWrapperRef){v=h.reactWrapperRef;break}h=h.$parent}v?(this.parentReactWrapperRef=v,this.reactPortal=function(){return createPortal(d,y)},v.pushReactPortal(this.reactPortal)):17<ReactMajorVersion?(void 0!==ReactDOM.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED&&(ReactDOM.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.usingClientEntryPoint=!0),this.__veauryReactApp__=ReactDOM.createRoot(y),this.__veauryReactApp__.render(d)):ReactDOM.render(d,y)}}},mounted:function(){var e=this;this.__VEAURY_IGNORE_STRANGE_UPDATE__=!0,Promise.resolve().then(function(){e.__VEAURY_IGNORE_STRANGE_UPDATE__=!1}),clearTimeout(this.updateTimer),this.__veauryMountReactComponent__()},beforeUnmount:function(){var e;clearTimeout(this.updateTimer),this.reactPortal?(overwriteDomMethods(this.$refs.react),null!=(e=this.parentReactWrapperRef)&&e.removeReactPortal(this.reactPortal)):(overwriteDomMethods(this.$refs.react),17<ReactMajorVersion?this.__veauryReactApp__.unmount():ReactDOM.unmountComponentAtNode(this.$refs.react)),recoverDomMethods()},updated:function(){this.__VEAURY_IGNORE_STRANGE_UPDATE__||this.__veauryMountReactComponent__(!0,{slot:!0})},inheritAttrs:!1,watch:{$attrs:{handler:function(){this.__veauryMountReactComponent__(!0,{attrs:!0})},deep:!0},__veauryInjectedProps__:{handler:function(){this.__veauryMountReactComponent__(!0,{attrs:!0})},deep:!0}}}}var REACT_ALL_HANDLERS=new Set([\"onClick\",\"onContextMenu\",\"onDoubleClick\",\"onDrag\",\"onDragEnd\",\"onDragEnter\",\"onDragExit\",\"onDragLeave\",\"onDragOver\",\"onDragStart\",\"onDrop\",\"onMouseDown\",\"onMouseEnter\",\"onMouseLeave\",\"onMouseMove\",\"onMouseOut\",\"onMouseOver\",\"onMouseUp\",\"onChange\",\"onInput\",\"onInvalid\",\"onReset\",\"onSubmit\",\"onError\",\"onLoad\",\"onPointerDown\",\"onPointerMove\",\"onPointerUp\",\"onPointerCancel\",\"onGotPointerCapture\",\"onLostPointerCapture\",\"onPointerEnter\",\"onPointerLeave\",\"onPointerOver\",\"onPointerOut\",\"onSelect\",\"onTouchCancel\",\"onTouchEnd\",\"onTouchMove\",\"onTouchStart\",\"onScroll\",\"onWheel\",\"onAbort\",\"onCanPlay\",\"onCanPlayThrough\",\"onDurationChange\",\"onEmptied\",\"onEncrypted\",\"onEnded\",\"onError\",\"onLoadedData\",\"onLoadedMetadata\",\"onLoadStart\",\"onPause\",\"onPlay\",\"onPlaying\",\"onProgress\",\"onRateChange\",\"onSeeked\",\"onSeeking\",\"onStalled\",\"onSuspend\",\"onTimeUpdate\",\"onVolumeChange\",\"onWaiting\",\"onLoad\",\"onError\",\"onAnimationStart\",\"onAnimationEnd\",\"onAnimationIteration\",\"onTransitionEnd\",\"onToggle\"]);function lookupVueWrapperRef(e,t){for(var r=null==(e=t=(null==e?void 0:e._reactInternals)||(null==e?void 0:e._reactInternalFiber)||t)?void 0:e.return;r;){var n=r.stateNode;if(n=(null==n?void 0:n.parentVueWrapperRef)||(null==n?void 0:n.__veauryVueWrapperRef__))return n;r=r.return}}function createModifiers(e,t,r){var n={};return r.forEach(function(e){n[e]=!0}),e[(\"modelValue\"===t?\"model\":t)+\"Modifiers\"]=n}function setVModel(e,t,r){var n=this,o=3<arguments.length&&void 0!==arguments[3]?arguments[3]:\"v-model\",a=t;if(!(a instanceof Array))throw Error(\"[error:veaury] Parameter type error from '\".concat(o,\"', a single v-model is an array, such as [val, setter, argumentKey, modifiers] or [val, setter, modifiers]\"));if(\"function\"!=typeof a[1])throw Error(\"[error:veaury] Parameter type error from '\".concat(o,\"', a single v-model is an array, the second element of the array must be a setter function\"));var i=a[1],u=(\"string\"==typeof a[2]?(r=a[2],a[3]instanceof Array&&createModifiers(e,r,a[3])):a[2]instanceof Array&&createModifiers(e,r,a[2]),e[\"onUpdate:\"+r]);e[\"onUpdate:\"+r]=\"function\"==typeof u?function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];u.apply(n,t),i.apply(n,t)}:i,e[r]=a[0]}function parseVModel(a){var i=this,r={},u=_objectSpread2({},a);return Object.keys(a).forEach(function(n){var o,e=n.match(/^onUpdate-([^-]+)/);if(e)delete u[n],o=r[\"onUpdate:\".concat(e[1])],r[\"onUpdate:\".concat(e[1])]=\"function\"==typeof o?function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];o.apply(i,t),a[n].apply(i,t)}:a[n];else if(e=n.match(/^v-model($|:([^:]+)|-([^:]+))/))e=e[2]||e[3]||\"modelValue\",setVModel(r,a[n],e),delete u[n];else if(\"v-models\"===n){if(\"object\"!==_typeof(a[n])||a[n]instanceof Array)throw Error(\"[error:veaury] The parameter 'v-models' must be an object type, such as {[argumentKey]: singleVModel}\");var t=a[n];Object.keys(t).forEach(function(e){setVModel(r,t[e],e,\"v-models\")}),delete u[n]}}),_objectSpread2(_objectSpread2({},u),r)}var _default=function(){function e(){_classCallCheck(this,e),_defineProperty(this,\"pool\",new Set)}return _createClass(e,[{key:\"getRandomId\",value:function(e){var t=e+(Math.random()+\"\").substr(2);return this.pool.has(t)?this.getRandomId(e):(this.pool.add(t),t)}}]),e}();function RenderReactNode(e,t){var r,e=e.node;if(\"function\"==typeof e&&(e=e()),null!=(r=t)&&r.current||\"function\"==typeof t||null!=(r=t)&&r.toString().match(/^function/)||(t=null),-1<[\"string\",\"number\"].indexOf(_typeof(e)))return e;if(e instanceof Array){if(1!==e.length)return e;e=e[0]}return _objectSpread2(_objectSpread2({},e),{},{ref:t})}var Bridge=applyReactInVue(RenderReactNode);function WrapVue(e){return h(Bridge,{node:function(){return e.node}})}WrapVue.originReactComponent=forwardRef(RenderReactNode);var _excluded$1=[\"component\",\"node\"],_excluded2$1=[\"component\",\"$slots\",\"children\",\"class\",\"style\"],_excluded3$1=[\"className\",\"classname\"],optionsName=\"veaury-options\",random=new _default;function filterVueComponent(e,t){var r;return e=\"string\"==typeof e&&t?null==(t=t.$)||null==(t=t.appContext)||null==(t=t.app)||null==(r=t.component)?void 0:r.call(t,e):e}function transferSlots(r){if(r)return Object.keys(r).forEach(function(e){var t=r[e];null!=t&&(\"function\"==typeof t?(r[e]=t,r[e].reactFunction=t):(r[e]=function(){return t},r[e].reactSlot=t),t.vueFunction&&(r[e].vueFunction=t.vueFunction))}),r}function VNodeBridge(e){var t;return null==(t=e.node)?void 0:t.call(e)}var VueContainer=forwardRef(function(e,t){var r,n=e.component,o=e.node,e=_objectWithoutProperties(e,_excluded$1);if(null==n&&null==o)return null;if(null!=o){if(o.$$typeof||\"string\"==typeof o||\"number\"==typeof o)return o;\"function\"!=typeof o&&(r=o,o=function(){return r})}var a,n=n||VNodeBridge,i=setOptions(e[optionsName]||{},void 0,!0),u=i.useInjectPropsFromWrapper||n.__veauryInjectPropsFromWrapper__;return i.isSlots||\"function\"==typeof u&&(a=u(e)),createElement(VueComponentLoader,_extends({},_objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({component:n},o?{node:o}:{}),e),a),{},_defineProperty({},optionsName,i)),{ref:t}))}),VueComponentLoader=function(){_inherits(n,Component);var r=_createSuper(n);function n(e){var t;return _classCallCheck(this,n),(t=r.call(this,e)).state={portals:[]},t.__veauryPortalKeyPool__=[],t.__veauryMaxPortalCount__=0,t.__veauryCurrentVueComponent__=e.component,t.__veauryCreateVueInstance__=t.__veauryCreateVueInstance__.bind(_assertThisInitialized(t)),t.__veauryVueComponentContainer__=t.createVueComponentContainer(),t}return _createClass(n,[{key:\"pushReactPortal\",value:function(e){var t=this.state.portals,r=this.__veauryPortalKeyPool__.shift()||this.__veauryMaxPortalCount__++;t.push({Portal:e,key:r}),this.setState({portals:t})}},{key:\"removeReactPortal\",value:function(r){var n,e=this.state.portals,t=e.find(function(e,t){if(e.Portal===r)return n=t,!0});this.__veauryPortalKeyPool__.push(t.key),e.splice(n,1),this.__veauryVueRef__&&this.setState({portals:e})}},{key:\"createVueComponentContainer\",value:function(){var t=this,r={},e=this.props[optionsName];return e.isSlots?(Object.keys(this.props).forEach(function(e){REACT_ALL_HANDLERS.has(e)&&\"function\"==typeof t.props[e]&&(r[e]=t.props[e])}),e.vue.slotWrapAttrs&&(r=_objectSpread2(_objectSpread2({},r),e.vue.slotWrapAttrs))):e.vue.componentWrapAttrs&&(r=_objectSpread2(_objectSpread2({},r),e.vue.componentWrapAttrs)),e.vue.componentWrapHOC(createElement(\"div\",_extends({},e.vue.componentWrapAttrs,{ref:this.__veauryCreateVueInstance__,key:null})),r)}},{key:\"shouldComponentUpdate\",value:function(e,t,r){var n,o,a,i,u=this;return e===this.props||(n=e.component,e[optionsName],o=void 0===(o=e[\"v-slots\"])?null:o,a=e.children,e=_objectWithoutProperties(e,[\"component\",optionsName,\"v-slots\",\"children\"].map(_toPropertyKey)),this.__veauryCurrentVueComponent__!==n&&this.updateVueComponent(n),!!n.__fromReactSlot||(this.__veauryVueInstance__?(a&&(o=o||{},\"object\"!==_typeof(a)||a instanceof Array||a.$$typeof?o.default=a:o=a),(i=this.__veauryVueInstance__.$data.$slots)&&Object.keys(i).forEach(function(e){delete i[e]}),o&&(i||(this.__veauryVueInstance__.$data.$slots={}),Object.assign(this.__veauryVueInstance__.$data.$slots,transferSlots(o))),Object.keys(this.__veauryVueInstance__.$data).forEach(function(e){\"$slots\"!==e&&delete u.__veauryVueInstance__.$data[e]}),this.__veauryVueInstance__&&Object.assign(this.__veauryVueInstance__.$data,parseVModel(e)),!0):void 0))}},{key:\"componentWillUnmount\",value:function(){this.vuePortal?this.parentVueWrapperRef.__veauryRemoveVuePortal__(this.vuePortal):(this.__veauryVueInstance__&&this.__veauryVueInstance__.$.appContext.app.unmount(),random.pool.delete(this.__veauryVueTargetId__))}},{key:\"__veauryCreateVueInstance__\",value:function(e){var r=this,p=this,t=this.props,_=(t.component,t[optionsName]),n=t.children,o=t[\"v-slots\"],o=void 0===o?{}:o,t=_objectWithoutProperties(t,[\"component\",optionsName,\"children\",\"v-slots\"].map(_toPropertyKey));function a(e){this.__veauryVueInstance__||(this.__veauryVueInstance__=e)}n&&(\"object\"!==_typeof(n)||n instanceof Array||n.$$typeof?o.default=n:o=n),(o=transferSlots(o))&&(t.$slots=o),a=a.bind(this);var i,u=_objectSpread2({},parseVModel(t)),c={data:function(){return _.isSlots?{children:p.__veauryCurrentVueComponent__.originVNode}:u},created:function(){this.reactWrapperRef=p,a(this)},methods:{reactInVueCall:function(e){return(2<arguments.length?arguments[2]:void 0)&&e&&e[0]?e.map(function(e,t){return h(WrapVue,{node:e,key:(null==e||null==(e=e.data)?void 0:e.key)||t})}):h(WrapVue,{node:e})},getScopedSlots:function(s,e){var t,l=this,r=(this.getScopedSlots.__scopeSlots||(this.getScopedSlots.__scopeSlots={}),_objectSpread2({},e));for(t in r)(function(u){var e,c;!r.hasOwnProperty(u)||null==(e=r[u])||(r[u]=(c=e,function(){for(var e,t,r,n,o=arguments.length,a=new Array(o),i=0;i<o;i++)a[i]=arguments[i];return c.vueFunction?c.vueFunction.apply(l,a):(r=c.reactSlot,n=c.reactFunction,r=r||(null==n?void 0:n.apply(l,a)),n=_.defaultSlotsFormatter,null!=(e=l.getScopedSlots.__scopeSlots[u])&&null!=(e=e.component)&&null!=(e=e.ctx)&&e.__veauryReactInstance__?(t=l.getScopedSlots.__scopeSlots[u],Promise.resolve().then(function(){var e;null!=(e=t)&&null!=(e=e.component)&&null!=(e=e.ctx)&&null!=(e=e.__veauryReactInstance__)&&e.setState({children:c.apply(l,a)})})):(t=n&&r?[n(r,l.reactInVueCall)]:s(applyReactInVue(function(){return c.apply(l,a)},_objectSpread2(_objectSpread2({},_),{},{isSlots:!0,wrapInstance:p}))),l.getScopedSlots.__scopeSlots[u]=t),c.reactFunction?t.reactFunction=c.reactFunction:c.reactSlot&&(t.reactSlot=c.reactSlot),t)}),r[u].reactFunction=e)})(t);return r}},mounted:function(){e.removeAttribute(\"id\"),p.__veauryVueRef__=this.$refs.use_vue_wrapper,this.$refs.use_vue_wrapper.reactWrapperRef=p},beforeUnmount:function(){p.__veauryVueRef__=null,this.$refs.use_vue_wrapper.reactWrapperRef=null},render:function(){var e=this,t=this.$data,r=(t.component,t.$slots),n=(t.children,t.class),o=t.style,t=_objectWithoutProperties(t,_excluded2$1),a=this.getScopedSlots(h,_objectSpread2({},r)),r=t.className,i=t.classname,t=_objectWithoutProperties(t,_excluded3$1),u={};return Object.keys(a).forEach(function(e){var t=a[e];u[e]=\"function\"==typeof t?t:function(){return t}}),h(filterVueComponent(p.__veauryCurrentVueComponent__,this),_objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({},t),n||r||i?{class:n||r||i}:{}),o?{style:o}:{}),{},{ref:\"use_vue_wrapper\"}),_objectSpread2({},_.isSlots&&this.children?{default:\"function\"==typeof this.children?this.children:function(){return e.children}}:_objectSpread2({},u)))}};e&&(i=random.getRandomId(\"__vue_wrapper_container_\"),e.id=i,this.__veauryVueTargetId__=i,(n=_.wrapInstance)?(n=_.wrapInstance).reactWrapperRef=p:n=lookupVueWrapperRef(this),n&&document.getElementById(i)?(this.parentVueWrapperRef=n,this.vuePortal=function(e,t){return e(Teleport,{to:\"#\"+i,key:i},[e(Object.assign(c,{router:r._router}))])},n.__veauryPushVuePortal__(this.vuePortal)):(o=createApp(c),\"function\"==typeof _.beforeVueAppMount&&_.beforeVueAppMount(o),this.__veauryVueInstance__=o.mount(e)))}},{key:\"updateVueComponent\",value:function(e){this.__veauryVueInstance__&&(e.__fromReactSlot?this.__veauryVueInstance__.children=\"function\"==typeof e.originVNode?e.originVNode:function(){return e.originVNode}:(this.__veauryCurrentVueComponent__=e,this.__veauryVueInstance__.$forceUpdate()))}},{key:\"render\",value:function(){return createElement(this.__veauryVueComponentContainer__,{portals:this.state.portals})}}]),n}();function applyVueInReact(r){var n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},e=(r||console.warn(\"Component must be passed in applyVueInReact!\"),r.__esModule&&r.default&&(r=r.default),forwardRef(function(e,t){return createElement(VueContainer,_extends({},e,{component:r,ref:t},_defineProperty({},optionsName,n)))}));return e.originVueComponent=r,e}function lazyVueInReact(e,t){return lazy(function(){return e().then(function(e){return{default:applyVueInReact(e.default,t)}})})}function lazyReactInVue(e,t){function r(){return n().then(function(e){return applyReactInVue(e.default,t)})}var n=e;\"object\"===_typeof(e)&&(n=e.loader);return defineAsyncComponent(\"object\"===_typeof(e)?_objectSpread2(_objectSpread2({},e),{},{loader:r}):r)}function injectPropsFromWrapper(e,t){return console.warn(\"[veaury warn]: HOC injectPropsFromWrapper is deprecated! Try using 'useInjectPropsFromWrapper' in the options of 'applyReactInVue' or 'applyVueInReact'!\"),\"function\"!=typeof e?console.warn(\"[veaury warn]: parameter 'injectionHook' is not a function\"):t.__veauryInjectPropsFromWrapper__=e,t}var _excluded$2=[\"children\"];function createCrossingProviderForReactInVue(e){var r=createContext({});return[function(){return useContext(r)},applyReactInVue(function(e){var t=e.children,e=_objectWithoutProperties(e,_excluded$2);return createElement(r.Provider,{value:_objectSpread2({},e)},t)},{useInjectPropsFromWrapper:e}),r]}var random$1=new _default;function createCrossingProviderForVueInReact(e,r){return r=r||random$1.getRandomId(\"veauryCrossingProvide_\"),[function(){return inject(r)},applyVueInReact({setup:function(e,t){return provide(r,t.attrs),function(){return h(t.slots.default)}}},{useInjectPropsFromWrapper:e})]}function createReactMissVue(e){var t=e.useVueInjection,e=e.beforeVueAppMount,t=_slicedToArray(createCrossingProviderForReactInVue(t),3),r=t[0],n=t[1],t=t[2];return[r,applyVueInReact(n,{beforeVueAppMount:e}),t]}function transformer(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},r=t.globalName,n=t.combinedOption,o=(t.transparentApi,applyReactInVue(e,n||{}));return o.install=function(e){var t=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).globalName;return r&&e.component(t||r,o),o},o}function toCamelCase(e){return e.replace(/-(\\w)/g,function(e,t){return t.toUpperCase()})}function formatStyle(t){var r;return t?\"string\"==typeof t?(t=t.trim()).split(/\\s*;\\s*/).reduce(function(e,t){return t&&2===(t=t.split(/\\s*:\\s*/)).length&&Object.assign(e,_defineProperty({},toCamelCase(t[0]),t[1])),e},{}):\"object\"===_typeof(t)?(r={},Object.keys(t).forEach(function(e){r[toCamelCase(e)]=t[e]}),r):{}:{}}function formatClass(t){return t?t instanceof Array?t:\"string\"==typeof t?(t=t.trim()).split(/\\s+/):\"object\"===_typeof(t)?Object.keys(t).filter(function(e){return!!t[e]}):[]:[]}var _excluded$3=[\"ref\"];function getChildInfo(r,e,o,a,i){var t=r.props||{},t=(t.ref,_objectWithoutProperties(t,_excluded$3)),u={},n=(Object.keys(r.children||{}).forEach(function(t){var n=r.children[t],e=originOptions.react.vueNamedSlotsKey.find(function(e){return 0===t.indexOf(e)});e||\"default\"===t?(e=t.replace(new RegExp(\"^\".concat(e)),\"\").replace(/^default$/,\"children\"),u[e]=a(n(),o,i)):u[t]=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return n.__reactArgs=t,a(n.apply(this,t),o,i)}}),{}),c=formatStyle(t.style),s=Array.from(new Set(formatClass(t.class))).join(\" \");return 0<Object.keys(c).length&&(n.style=c),\"\"!==s&&(n.className=s),Object.assign(t,_objectSpread2(_objectSpread2({},n),u)),delete t.class,t}function isTextOwner(e){return e.type===Text}var random$2=new _default;function DirectiveHOC(e,t){var r;return 0<(null==(r=e.dirs)?void 0:r.length)?createElement(FakeDirective,{vnode:e},t):t}var FakeDirective=function(){_inherits(n,Component);var r=_createSuper(n);function n(e){var t;return _classCallCheck(this,n),(t=r.call(this,e)).state={prevVnode:null,savedDirectives:[],ref:null,prevProps:e},t}return _createClass(n,[{key:\"findDirectiveName\",value:function(e){var r=e.dir,n=-1;return[this.state.savedDirectives.find(function(e,t){if(e.dir===r)return n=t,!0}),n]}},{key:\"doDirective\",value:function(){var c=this,e=this.state,s=e.savedDirectives;if(!(l=e.ref)){for(var l=(this._reactInternals||this._reactInternalFiber).child;l&&5!==l.tag;)l=l.child;if(!l)return;l=l.stateNode}var p=this.props.vnode,e=p.dirs;e&&(e.forEach(function(e){var t,r,n,o,a,i,u;e&&(u=(t=_slicedToArray(c.findDirectiveName(e),2))[0],t=t[1],r=(a=e.dir).created,n=a.beforeMount,o=a.mounted,i=a.beforeUpdate,a=a.updated,u?(s[t]=_objectSpread2(_objectSpread2(_objectSpread2({},u),e),{},{oldValue:u.oldValue}),u=[l,s[t],p,c.state.prevVnode],null!=i&&i.apply(null,u),null!=a&&a.apply(null,u),s[t].oldValue=e.value):(s.push(e),i=[l,e,p,null],null!=r&&r.apply(null,i),null!=n&&n.apply(null,i),null!=o&&o.apply(null,i),e.oldValue=e.value))}),this.setState({prevVnode:_objectSpread2({},p),savedDirectives:s,ref:l}))}},{key:\"componentDidMount\",value:function(){this.doDirective()}},{key:\"componentDidUpdate\",value:function(e){e.vnode!==this.props.vnode&&this.doDirective()}},{key:\"componentWillUnmount\",value:function(){var a=this,i=this.props.vnode,e=this.state,u=e.savedDirectives,c=e.ref,s=e.prevVnode,e=i.dirs;e&&(e.forEach(function(e){var t,r,n,o;e&&(t=(o=_slicedToArray(a.findDirectiveName(e),2))[0],o=o[1],t&&(r=(n=e.dir).beforeUnmount,n=n.unmounted,u[o]=_objectSpread2(_objectSpread2({},t),e),o=[c,t,i,s],null!=r&&r.apply(null,o),null!=n&&n.apply(null,o)))}),this.setState({prevVnode:_objectSpread2({},i),savedDirectives:u}))}},{key:\"render\",value:function(){var e=this.props;e.vnode;return e.children}}]),n}();function couldBeClass(e,t){var r;return\"function\"==typeof e&&(r=e.toString(),void 0!==e.prototype&&(e.prototype.constructor===e&&(\"class\"==r.slice(0,5)||(2<=Object.getOwnPropertyNames(e.prototype).length||!/^function\\s+\\(|^function\\s+anonymous\\(/.test(r)&&(!(!t||!/^function\\s+[A-Z]/.test(r))||!!/\\b\\(this\\b|\\bthis[\\.\\[]\\b/.test(r)&&(!(t&&!/classCallCheck\\(this/.test(r))||/^function\\sdefault_\\d+\\s*\\(/.test(r)))))))}function resolveRef(o){var a,e;return\"function\"!=typeof(null==(e=o.type)?void 0:e.originReactComponent)||couldBeClass(null==(e=o.type)?void 0:e.originReactComponent)?((e=null==(e=o.ref)?void 0:e.r)&&\"string\"==typeof e&&(a=e,e=function(e){var t;e&&(o.ref.i.refs&&((t=_objectSpread2({},o.ref.i.refs))[a]=e,o.ref.i.refs=t),void 0!==(null==(t=o.ref.i.setupState)?void 0:t[a])&&(o.ref.i.setupState[a]=e))},e=new Proxy(e,{get:function(e,t){return e[t]},set:function(e,t,r){var n;return null!=(n=o.ref.i.refs)&&n[a]&&((n=_objectSpread2({},o.ref.i.refs))[t]=r,o.ref.i.refs=n),r}})),e):null}function addScopeId(t,e){return!e||e instanceof Array&&0===e.length||(\"string\"==typeof e&&(e=[e]),(t=_objectSpread2({},t)).props=_objectSpread2({},t.props),e.forEach(function(e){t.props[e]=\"\"})),t}var _excluded$4=[\"style\",\"class\"];function takeVueDomInReact(e,t,r,n,o,a,i){var u,c,s;return\"all\"===t||t instanceof Array||(t=t?[t]:[]),e.type===Fragment$1?o(e.children,r,a):\"string\"==typeof e.type&&(\"all\"===t||-1<t.indexOf(e.type))?(t=resolveRef(e),s=(c=e.props||{}).style,u=c.class,c=_objectSpread2(_objectSpread2({},_objectWithoutProperties(c,_excluded$4)),{},{style:formatStyle(s),className:Array.from(new Set(formatClass(u))).join(\" \")},t?{ref:t}:{}),(s=e.children||c.children)&&((s=-1<[\"string\",\"number\"].indexOf(_typeof(s))?[s]:_toConsumableArray(s)).__top__=i),DirectiveHOC(e,addScopeId(React__default.createElement(e.type,c,o(s,r,a)),e.scopeId))):r([e],null,n)}function pureInterceptProps(){return 0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}}function setChildKey(e,t,r){return!((e=e instanceof Array&&1===e.length?e[0]:e)instanceof Array)&&null==e.key&&1<t.length&&((e=_objectSpread2({},e)).key=\"_key_\".concat(r)),e}function getDistinguishReactOrVue(e){var l=e.reactComponents,p=e.domTags,e=e.division,_=void 0===e||e;return function a(i,u,c){var s;return i&&i.forEach?(s=[],i.forEach(function(e,t){if(e&&e.type!==Comment){if(null==(o=e.type)||!o.originReactComponent)return e.$$typeof||\"string\"==typeof e||\"number\"==typeof e?void s.push(e):isTextOwner(e)?void(\"\"!==e.children.trim()&&s.push(e.children.trim())):void(e.type&&(addScopeId(o=setChildKey(takeVueDomInReact(e,p,u,_,a,c,i.__top__),i,t),e.scopeId),s.push(o)));var r,n,o=e.type.originReactComponent;addScopeId(r=setChildKey(r=\"all\"===(l=\"all\"===l||l instanceof Array?l:[l])||-1<l.indexOf(o)?(e.__top__=i.__top__,r=getChildInfo(e,\"_key_\".concat(t),u,a,c),n=resolveRef(e),e.children&&(e.children.__top__=i.__top__),DirectiveHOC(e,React__default.createElement(o,_objectSpread2(_objectSpread2(_objectSpread2({},pureInterceptProps(r,e,o)),e.__extraData||{}),n?{ref:n}:{})))):isTextOwner(e)?e.text:takeVueDomInReact(e,p,u,_,a,c),i,t),e.scopeId),s.push(r)}}),1===s.length?s[0]:s):i}}var NoWrapFunction=getDistinguishReactOrVue({reactComponents:\"all\",domTags:\"all\"});function applyPureReactInVue(e,t){return transformer(e,{combinedOption:_objectSpread2({pureTransformer:!0,defaultSlotsFormatter:NoWrapFunction,defaultPropsFormatter:function(t,o,a){var r={};return Object.keys(t).forEach(function(e){var n=t[e];n&&(n.vueFunction?(r[e]=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return NoWrapFunction(n.vueFunction.apply(this,t),o,a)},Object.defineProperty(r[e],\"length\",{get:function(){return n.vueFunction.length}})):n.vueSlot&&(r[e]=NoWrapFunction(n.vueSlot,o,a)))}),Object.assign(t,r)}},t)})}var NoWrapFunction$1=getDistinguishReactOrVue({reactComponents:\"all\",domTags:\"all\"});function getReactNode(e){return e=(e=[e=\"function\"==typeof e?e():e]).flat(1/0),NoWrapFunction$1(e,function(e){return React__default.createElement(VueContainer,{node:e})})}function transformer$1(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},r=(t.globalName,t.combinedOption);t.transparentApi;return applyVueInReact(e,r||{})}var _excluded$5=[\"ref\",\"children\",\"v-slots\"];function getChildInfo$1(e,t,o,a,i){var e=e.props||{},r=(e.ref,e.children),n=e[\"v-slots\"],u=void 0===n?{}:n,n=_objectWithoutProperties(e,_excluded$5),c=(r&&(\"object\"!==_typeof(r)||r instanceof Array||r.$$typeof?u.default=r:u=r),null),e=(Object.keys(u||{}).forEach(function(e){var n=u[e];(c=c||{})[e]=function(){if(\"function\"==typeof n){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];n=n.apply(this,t)}return a(n,o,i)}}),{}),r=formatStyle(n.style),s=Array.from(new Set(formatClass(n.className))).join(\" \");return 0<Object.keys(r).length&&(e.style=r),\"\"!==s&&(e.class=s),Object.assign(n,_objectSpread2({},e)),delete n.className,{props:n=parseVModel(n),slots:c}}function resolveRef$1(t){var e=t.ref;if(e)return\"object\"===_typeof(e)?function(e){t.ref.current=e}:\"function\"==typeof e?e:void 0}var _excluded$6=[\"style\",\"class\",\"children\"];function takeReactDomInVue(e,t,r,n,o,a){var i,u,c,s;return\"all\"===t||t instanceof Array||(t=t?[t]:[]),e.type===Fragment?o(null==(i=e.props)?void 0:i.children,r):\"string\"==typeof e.type&&(\"all\"===t||-1<t.indexOf(e.type))?(i=resolveRef$1(e),s=(t=e.props||{}).style,c=t.class,u=t.children,t=_objectWithoutProperties(t,_excluded$6),c=Array.from(new Set(formatClass(c))).join(\" \"),s=formatStyle(s),t=_objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({},t),0===Object.keys(s).length?{}:{style:s}),c?{className:c}:{}),i?{ref:i}:{}),0===Object.keys(t).length&&(t=null),(s=u)&&((s=-1<[\"string\",\"number\"].indexOf(_typeof(s))?[s]:s instanceof Array?_toConsumableArray(s):_objectSpread2({},s)).__top__=a),h(e.type,t,o(s,r))):r([e],null,n)}function getDistinguishReactOrVue$1(e){var c=e.vueComponents,s=e.domTags,e=e.division,l=void 0===e||e;return function o(a,i){if(null==a)return a;a instanceof Array||(a=[a]);var u=[];return a.forEach(function(e,t){if((null==(r=e.type)||!r.originVueComponent)&&e.type!==VueContainer)return e.__v_isVNode||\"string\"==typeof e||\"number\"==typeof e?void u.push(e):void(e.type&&(r=takeReactDomInVue(e,s,i,l,o,a.__top__),u.push(r)));var r=e.type.originVueComponent;if(e.type===VueContainer){if(!e.props.component)return void u.push(e.props.node);r=e.props.component,e=_objectSpread2({},e);var n=_objectSpread2({},e.props);delete n.component,e.props=n}r=\"all\"===(c=\"all\"===c||c instanceof Array?c:[c])||-1<c.indexOf(r)?((e=_objectSpread2({},e)).__top__=a.__top__,t=(n=getChildInfo$1(e,\"_key_\".concat(t),i,o)).props,n=n.slots,resolveRef$1(e),e.children&&(e.children.__top__=a.__top__),h(r,_objectSpread2({},t),n)):takeReactDomInVue(e,s,i,l,o),u.push(r)}),1===(u=u.flat(1/0)).length?u[0]:u}}var NoWrapFunction$2=getDistinguishReactOrVue$1({vueComponents:\"all\",domTags:\"all\"});function applyPureVueInReact(e,t){return transformer$1(e,{combinedOption:_objectSpread2({pureTransformer:!0,defaultSlotsFormatter:NoWrapFunction$2},t)})}var NoWrapFunction$3=getDistinguishReactOrVue$1({reactComponents:\"all\",domTags:\"all\"});function getVNode(e){return 1===(e=(e=[e=\"function\"==typeof e?e():e]).flat(1/0)).length&&(e=e[0]),NoWrapFunction$3(e,function(e){return h(WrapVue,{node:e})})}function lazyReactInVue$1(e,t){function r(){return n().then(function(e){return applyPureReactInVue(e.default,t)})}var n=e;\"object\"===_typeof(e)&&(n=e.loader);return defineAsyncComponent(\"object\"===_typeof(e)?_objectSpread2(_objectSpread2({},e),{},{loader:r}):r)}function lazyVueInReact$1(e,t){return lazy(function(){return e().then(function(e){return{default:applyPureVueInReact(e.default,t)}})})}var _excluded$7=[\"children\"];function createCrossingProviderForReactInVue$1(e){var r=createContext({});return[function(){return useContext(r)},applyPureReactInVue(function(e){var t=e.children,e=_objectWithoutProperties(e,_excluded$7);return createElement(r.Provider,{value:_objectSpread2({},e)},t)},{useInjectPropsFromWrapper:e}),r]}var random$3=new _default;function createCrossingProviderForVueInReact$1(e,r){return r=r||random$3.getRandomId(\"veauryCrossingProvide_\"),[function(){return inject(r)},applyPureVueInReact({setup:function(e,t){return provide(r,t.attrs),function(){return h(t.slots.default)}}},{useInjectPropsFromWrapper:e})]}export{REACT_ALL_HANDLERS,WrapVue as RenderReactNode,VueContainer,applyPureReactInVue,applyPureVueInReact,applyReactInVue,applyVueInReact,createCrossingProviderForReactInVue$1 as createCrossingProviderForPureReactInVue,createCrossingProviderForVueInReact$1 as createCrossingProviderForPureVueInReact,createCrossingProviderForReactInVue,createCrossingProviderForVueInReact,createReactMissVue,getReactNode,getVNode,injectPropsFromWrapper,lazyReactInVue$1 as lazyPureReactInVue,lazyVueInReact$1 as lazyPureVueInReact,lazyReactInVue,lazyVueInReact,setOptions as setVeauryOptions,originOptions as veauryOptions};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,mBAA6G;AAAkJ,uBAAkC;AAAY,SAAS,QAAQ,GAAE,GAAE;AAAC,MAAI,GAAE,IAAE,OAAO,KAAK,CAAC;AAAE,SAAO,OAAO,0BAAwB,IAAE,OAAO,sBAAsB,CAAC,GAAE,MAAI,IAAE,EAAE,OAAO,SAASA,IAAE;AAAC,WAAO,OAAO,yBAAyB,GAAEA,EAAC,EAAE;AAAA,EAAU,CAAC,IAAG,EAAE,KAAK,MAAM,GAAE,CAAC,IAAG;AAAC;AAAC,SAAS,eAAe,GAAE;AAAC,WAAQ,IAAE,GAAE,IAAE,UAAU,QAAO,KAAI;AAAC,QAAI,IAAE,QAAM,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC;AAAE,QAAE,IAAE,QAAQ,OAAO,CAAC,GAAE,IAAE,EAAE,QAAQ,SAASA,IAAE;AAAC,sBAAgB,GAAEA,IAAE,EAAEA,EAAC,CAAC;AAAA,IAAC,CAAC,IAAE,OAAO,4BAA0B,OAAO,iBAAiB,GAAE,OAAO,0BAA0B,CAAC,CAAC,IAAE,QAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAASA,IAAE;AAAC,aAAO,eAAe,GAAEA,IAAE,OAAO,yBAAyB,GAAEA,EAAC,CAAC;AAAA,IAAC,CAAC;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,QAAQ,GAAE;AAAC,UAAO,UAAQ,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,WAAO,OAAOA;AAAA,EAAC,IAAE,SAASA,IAAE;AAAC,WAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,EAAC,GAAG,CAAC;AAAC;AAAC,SAAS,gBAAgB,GAAE,GAAE;AAAC,MAAG,EAAE,aAAa;AAAG,UAAM,IAAI,UAAU,mCAAmC;AAAC;AAAC,SAAS,kBAAkB,GAAE,GAAE;AAAC,WAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,QAAI,IAAE,EAAE,CAAC;AAAE,MAAE,aAAW,EAAE,cAAY,OAAG,EAAE,eAAa,MAAG,WAAU,MAAI,EAAE,WAAS,OAAI,OAAO,eAAe,GAAE,EAAE,KAAI,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,aAAa,GAAE,GAAE,GAAE;AAAC,SAAO,KAAG,kBAAkB,EAAE,WAAU,CAAC,GAAE,KAAG,kBAAkB,GAAE,CAAC,GAAE,OAAO,eAAe,GAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAE;AAAC;AAAC,SAAS,gBAAgB,GAAE,GAAE,GAAE;AAAC,SAAO,KAAK,IAAE,OAAO,eAAe,GAAE,GAAE,EAAC,OAAM,GAAE,YAAW,MAAG,cAAa,MAAG,UAAS,KAAE,CAAC,IAAE,EAAE,CAAC,IAAE,GAAE;AAAC;AAAC,SAAS,WAAU;AAAC,UAAO,WAAS,OAAO,SAAO,OAAO,OAAO,KAAK,IAAE,SAAS,GAAE;AAAC,aAAQ,IAAE,GAAE,IAAE,UAAU,QAAO,KAAI;AAAC,UAAI,GAAE,IAAE,UAAU,CAAC;AAAE,WAAI,KAAK;AAAE,eAAO,UAAU,eAAe,KAAK,GAAE,CAAC,MAAI,EAAE,CAAC,IAAE,EAAE,CAAC;AAAA,IAAE;AAAC,WAAO;AAAA,EAAC,GAAG,MAAM,MAAK,SAAS;AAAC;AAAC,SAAS,UAAU,GAAE,GAAE;AAAC,MAAG,cAAY,OAAO,KAAG,SAAO;AAAE,UAAM,IAAI,UAAU,oDAAoD;AAAE,IAAE,YAAU,OAAO,OAAO,KAAG,EAAE,WAAU,EAAC,aAAY,EAAC,OAAM,GAAE,UAAS,MAAG,cAAa,KAAE,EAAC,CAAC,GAAE,OAAO,eAAe,GAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAE,KAAG,gBAAgB,GAAE,CAAC;AAAC;AAAC,SAAS,gBAAgB,GAAE;AAAC,UAAO,kBAAgB,OAAO,iBAAe,OAAO,eAAe,KAAK,IAAE,SAASA,IAAE;AAAC,WAAOA,GAAE,aAAW,OAAO,eAAeA,EAAC;AAAA,EAAC,GAAG,CAAC;AAAC;AAAC,SAAS,gBAAgB,GAAE,GAAE;AAAC,UAAO,kBAAgB,OAAO,iBAAe,OAAO,eAAe,KAAK,IAAE,SAASA,IAAEC,IAAE;AAAC,WAAOD,GAAE,YAAUC,IAAED;AAAA,EAAC,GAAG,GAAE,CAAC;AAAC;AAAC,SAAS,4BAA2B;AAAC,MAAG,eAAa,OAAO,WAAS,CAAC,QAAQ;AAAU,WAAM;AAAG,MAAG,QAAQ,UAAU;AAAK,WAAM;AAAG,MAAG,cAAY,OAAO;AAAM,WAAM;AAAG,MAAG;AAAC,WAAO,QAAQ,UAAU,QAAQ,KAAK,QAAQ,UAAU,SAAQ,CAAC,GAAE,WAAU;AAAA,IAAC,CAAC,CAAC,GAAE;AAAA,EAAE,SAAO,GAAE;AAAC,WAAM;AAAA,EAAE;AAAC;AAAC,SAAS,8BAA8B,GAAE,GAAE;AAAC,MAAG,QAAM;AAAE,WAAM,CAAC;AAAE,WAAQ,GAAE,IAAE,CAAC,GAAE,IAAE,OAAO,KAAK,CAAC,GAAE,IAAE,GAAE,IAAE,EAAE,QAAO;AAAI,QAAE,EAAE,CAAC,GAAE,KAAG,EAAE,QAAQ,CAAC,MAAI,EAAE,CAAC,IAAE,EAAE,CAAC;AAAG,SAAO;AAAC;AAAC,SAAS,yBAAyB,GAAE,GAAE;AAAC,MAAG,QAAM;AAAE,WAAM,CAAC;AAAE,MAAI,GAAE,IAAE,8BAA8B,GAAE,CAAC;AAAE,MAAG,OAAO;AAAsB,aAAQ,IAAE,OAAO,sBAAsB,CAAC,GAAE,IAAE,GAAE,IAAE,EAAE,QAAO;AAAI,UAAE,EAAE,CAAC,GAAE,KAAG,EAAE,QAAQ,CAAC,KAAG,OAAO,UAAU,qBAAqB,KAAK,GAAE,CAAC,MAAI,EAAE,CAAC,IAAE,EAAE,CAAC;AAAG,SAAO;AAAC;AAAC,SAAS,uBAAuB,GAAE;AAAC,MAAG,WAAS;AAAE,UAAM,IAAI,eAAe,2DAA2D;AAAE,SAAO;AAAC;AAAC,SAAS,2BAA2B,GAAE,GAAE;AAAC,MAAG,MAAI,YAAU,OAAO,KAAG,cAAY,OAAO;AAAG,WAAO;AAAE,MAAG,WAAS;AAAE,UAAM,IAAI,UAAU,0DAA0D;AAAE,SAAO,uBAAuB,CAAC;AAAC;AAAC,SAAS,aAAa,GAAE;AAAC,MAAI,IAAE,0BAA0B;AAAE,SAAO,WAAU;AAAC,QAAI,GAAE,IAAE,gBAAgB,CAAC;AAAE,WAAO,2BAA2B,MAAK,KAAG,IAAE,gBAAgB,IAAI,EAAE,aAAY,QAAQ,UAAU,GAAE,WAAU,CAAC,KAAG,EAAE,MAAM,MAAK,SAAS,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,eAAe,GAAE,GAAE;AAAC,SAAO,gBAAgB,CAAC,KAAG,sBAAsB,GAAE,CAAC,KAAG,4BAA4B,GAAE,CAAC,KAAG,iBAAiB;AAAC;AAAC,SAAS,mBAAmB,GAAE;AAAC,SAAO,mBAAmB,CAAC,KAAG,iBAAiB,CAAC,KAAG,4BAA4B,CAAC,KAAG,mBAAmB;AAAC;AAAC,SAAS,mBAAmB,GAAE;AAAC,MAAG,MAAM,QAAQ,CAAC;AAAE,WAAO,kBAAkB,CAAC;AAAC;AAAC,SAAS,gBAAgB,GAAE;AAAC,MAAG,MAAM,QAAQ,CAAC;AAAE,WAAO;AAAC;AAAC,SAAS,iBAAiB,GAAE;AAAC,MAAG,eAAa,OAAO,UAAQ,QAAM,EAAE,OAAO,QAAQ,KAAG,QAAM,EAAE,YAAY;AAAE,WAAO,MAAM,KAAK,CAAC;AAAC;AAAC,SAAS,sBAAsB,GAAE,GAAE;AAAC,MAAI,IAAE,QAAM,IAAE,OAAK,eAAa,OAAO,UAAQ,EAAE,OAAO,QAAQ,KAAG,EAAE,YAAY;AAAE,MAAG,QAAM,GAAE;AAAC,QAAI,GAAE,GAAE,IAAE,CAAC,GAAE,IAAE,MAAG,IAAE;AAAG,QAAG;AAAC,WAAI,IAAE,EAAE,KAAK,CAAC,GAAE,EAAE,KAAG,IAAE,EAAE,KAAK,GAAG,UAAQ,EAAE,KAAK,EAAE,KAAK,GAAE,CAAC,KAAG,EAAE,WAAS,IAAG,IAAE;AAAG;AAAA,IAAC,SAAOA,IAAE;AAAC,UAAE,MAAG,IAAEA;AAAA,IAAC,UAAC;AAAQ,UAAG;AAAC,aAAG,QAAM,EAAE,UAAQ,EAAE,OAAO;AAAA,MAAC,UAAC;AAAQ,YAAG;AAAE,gBAAM;AAAA,MAAC;AAAA,IAAC;AAAC,WAAO;AAAA,EAAC;AAAC;AAAC,SAAS,4BAA4B,GAAE,GAAE;AAAC,MAAI;AAAE,MAAG;AAAE,WAAM,YAAU,OAAO,IAAE,kBAAkB,GAAE,CAAC,IAAE,WAAS,IAAE,cAAY,IAAE,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAE,EAAE,MAAI,EAAE,cAAY,EAAE,YAAY,OAAK,MAAI,UAAQ,IAAE,MAAM,KAAK,CAAC,IAAE,gBAAc,KAAG,2CAA2C,KAAK,CAAC,IAAE,kBAAkB,GAAE,CAAC,IAAE;AAAM;AAAC,SAAS,kBAAkB,GAAE,GAAE;AAAC,GAAC,QAAM,KAAG,IAAE,EAAE,YAAU,IAAE,EAAE;AAAQ,WAAQ,IAAE,GAAE,IAAE,IAAI,MAAM,CAAC,GAAE,IAAE,GAAE;AAAI,MAAE,CAAC,IAAE,EAAE,CAAC;AAAE,SAAO;AAAC;AAAC,SAAS,qBAAoB;AAAC,QAAM,IAAI,UAAU,sIAAsI;AAAC;AAAC,SAAS,mBAAkB;AAAC,QAAM,IAAI,UAAU,2IAA2I;AAAC;AAAC,SAAS,aAAa,GAAE,GAAE;AAAC,MAAG,YAAU,OAAO,KAAG,SAAO;AAAE,WAAO;AAAE,MAAI,IAAE,EAAE,OAAO,WAAW;AAAE,MAAG,WAAS;AAAE,YAAO,aAAW,IAAE,SAAO,QAAQ,CAAC;AAAE,MAAE,EAAE,KAAK,GAAE,KAAG,SAAS;AAAE,MAAG,YAAU,OAAO;AAAE,WAAO;AAAE,QAAM,IAAI,UAAU,8CAA8C;AAAC;AAAC,SAAS,eAAe,GAAE;AAAC,MAAE,aAAa,GAAE,QAAQ;AAAE,SAAM,YAAU,OAAO,IAAE,IAAE,OAAO,CAAC;AAAC;AAAC,IAAI,gBAAc,EAAC,OAAM,EAAC,eAAc,OAAM,UAAS,OAAM,oBAAmB,EAAC,4BAA2B,IAAG,OAAM,EAAC,KAAI,QAAO,EAAC,GAAE,eAAc,EAAC,uBAAsB,IAAG,OAAM,EAAC,KAAI,QAAO,EAAC,GAAE,kBAAiB,CAAC,OAAO,EAAC,GAAE,KAAI,EAAC,kBAAiB,SAAS,GAAE;AAAC,SAAO,WAAU;AAAC,QAAI,KAAG,IAAE,UAAU,UAAQ,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC,GAAG;AAAQ,eAAO,4BAAc,uBAAS,MAAK,IAAG,WAAS,IAAE,CAAC,IAAE,GAAG,IAAI,SAASA,IAAE;AAAC,UAAIC,KAAED,GAAE,QAAOA,KAAEA,GAAE;AAAI,iBAAO,4BAAcC,IAAE,EAAC,KAAID,GAAC,CAAC;AAAA,IAAC,CAAC,CAAC;AAAA,EAAC;AAAC,GAAE,oBAAmB,EAAC,+BAA8B,IAAG,OAAM,EAAC,KAAI,QAAO,EAAC,GAAE,eAAc,EAAC,0BAAyB,IAAG,OAAM,EAAC,KAAI,QAAO,EAAC,EAAC,EAAC;AAAE,SAAS,aAAY;AAAC,MAAI,IAAE,IAAE,UAAU,UAAQ,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,EAAC,OAAM,CAAC,GAAE,KAAI,CAAC,EAAC,GAAE,IAAE,IAAE,UAAU,UAAQ,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,eAAc,IAAE,IAAE,UAAU,SAAO,UAAU,CAAC,IAAE,QAAO,KAAG,EAAE,QAAM,EAAE,MAAI,CAAC,IAAG,EAAE,UAAQ,EAAE,QAAM,CAAC,IAAG,CAAC,GAAE,eAAe,eAAe,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAC,OAAM,eAAe,eAAe,eAAe,CAAC,GAAE,EAAE,KAAK,GAAE,EAAE,KAAK,GAAE,CAAC,GAAE,EAAC,oBAAmB,eAAe,eAAe,CAAC,GAAE,EAAE,MAAM,kBAAkB,GAAE,EAAE,MAAM,kBAAkB,GAAE,eAAc,eAAe,eAAe,CAAC,GAAE,EAAE,MAAM,aAAa,GAAE,EAAE,MAAM,aAAa,EAAC,CAAC,GAAE,KAAI,eAAe,eAAe,eAAe,CAAC,GAAE,EAAE,GAAG,GAAE,EAAE,GAAG,GAAE,CAAC,GAAE,EAAC,oBAAmB,eAAe,eAAe,CAAC,GAAE,EAAE,IAAI,kBAAkB,GAAE,EAAE,IAAI,kBAAkB,GAAE,eAAc,eAAe,eAAe,CAAC,GAAE,EAAE,IAAI,aAAa,GAAE,EAAE,IAAI,aAAa,EAAC,CAAC,EAAC,CAAC,CAAC;AAAG,SAAO,KAAG,EAAE,QAAQ,CAAC,CAAC,GAAE,OAAO,OAAO,MAAM,MAAK,CAAC;AAAC;AAAC,IAAI,aAAW,CAAC,kBAAiB,0BAAyB,wBAAuB,0BAAyB,iBAAgB,kBAAkB;AAA5I,IAA8I,eAAa,EAAC,UAAS,CAAC,GAAE,SAAQ,CAAC,EAAC;AAAE,SAAS,oBAAoB,GAAE;AAAC,SAAO,KAAK,YAAY,EAAE,QAAQ,SAAS,GAAE;AAAC,eAAW,QAAQ,SAAS,GAAE;AAAC,UAAI,IAAE,OAAO,CAAC,EAAE,UAAU,CAAC;AAAE,mBAAa,CAAC,EAAE,CAAC,IAAE,GAAE,OAAO,CAAC,EAAE,UAAU,CAAC,IAAE,WAAU;AAAC,iBAAQA,KAAE,UAAU,QAAO,IAAE,IAAI,MAAMA,EAAC,GAAE,IAAE,GAAE,IAAEA,IAAE;AAAI,YAAE,CAAC,IAAE,UAAU,CAAC;AAAE,YAAI,IAAE,EAAE,MAAM,MAAK,CAAC;AAAE,eAAO,MAAI,EAAE,gBAAc,YAAU,EAAE,gBAAc,YAAU,IAAE,EAAE,UAAQ,IAAE,QAAQ,UAAU,CAAC,EAAE,MAAM,GAAE,CAAC;AAAA,MAAC;AAAA,IAAC,CAAC;AAAA,EAAC,CAAC;AAAC;AAAC,SAAS,oBAAmB;AAAC,SAAO,KAAK,YAAY,EAAE,QAAQ,SAAS,GAAE;AAAC,eAAW,QAAQ,SAAS,GAAE;AAAC,aAAO,CAAC,EAAE,UAAU,CAAC,IAAE,aAAa,CAAC,EAAE,CAAC;AAAA,IAAC,CAAC;AAAA,EAAC,CAAC;AAAC;AAAC,IAAI,YAAU,CAAC,KAAK;AAApB,IAAsB,aAAW,CAAC,KAAK;AAAvC,IAAyC,aAAW,CAAC,UAAU;AAA/D,IAAiE,oBAAkB,SAAS,oBAAO;AAAE,SAAS,OAAO,GAAE;AAAC,SAAO;AAAC;AAAC,IAAI,wBAAsB,WAAU;AAAC,YAAU,GAAE,sBAAS;AAAE,MAAI,IAAE,aAAa,CAAC;AAAE,WAAS,EAAE,GAAE;AAAC,WAAO,gBAAgB,MAAK,CAAC,GAAE,EAAE,KAAK,MAAK,CAAC;AAAA,EAAC;AAAC,SAAO,aAAa,GAAE,CAAC,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,QAAI,IAAE,KAAK,MAAM,WAAUC,KAAE,KAAK,MAAM,aAAYA,MAAGA,GAAE,KAAI,yBAAyBA,IAAE,SAAS;AAAG,eAAO,4BAAc,GAAEA,IAAE,KAAK,MAAM,QAAQ;AAAA,EAAC,EAAC,CAAC,CAAC,GAAE;AAAC,EAAE;AAA7V,IAA+V,uBAAqB,SAAS,GAAE,GAAE,GAAE;AAAC,MAAI,IAAE,WAAU;AAAC,cAAU,GAAE,sBAAS;AAAE,QAAI,IAAE,aAAa,CAAC;AAAE,aAAS,EAAED,IAAE;AAAC,UAAI;AAAE,aAAO,gBAAgB,MAAK,CAAC,IAAG,IAAE,EAAE,KAAK,MAAKA,EAAC,GAAG,QAAM,eAAe,eAAe,CAAC,GAAEA,EAAC,GAAE,EAAE,UAAQ,EAAC,UAAS,EAAC,IAAE,CAAC,CAAC,GAAE,EAAE,SAAO,EAAE,OAAO,KAAK,uBAAuB,CAAC,CAAC,GAAE,EAAE,iBAAe,EAAE,eAAe,KAAK,uBAAuB,CAAC,CAAC,IAAG,EAAE,0BAAwB,GAAG,2BAAyB,EAAE,gBAAe;AAAA,IAAC;AAAC,WAAO,aAAa,GAAE,CAAC,EAAC,KAAI,+BAA8B,OAAM,SAAS,GAAE;AAAC,aAAO,KAAK,CAAC,EAAE,QAAQ,SAASA,IAAE;AAAC,UAAEA,EAAC,MAAI,EAAEA,EAAC,IAAE,EAAEA,EAAC;AAAA,MAAE,CAAC,GAAE,OAAO,oBAAoB,EAAE,SAAS,EAAE,OAAO,SAASA,IAAE;AAAC,eAAM,CAAC,eAAc,QAAQ,EAAE,QAAQA,EAAC,IAAE;AAAA,MAAC,CAAC,EAAE,QAAQ,SAASA,IAAE;AAAC,UAAEA,EAAC,MAAI,EAAEA,EAAC,IAAE,EAAEA,EAAC;AAAA,MAAE,CAAC;AAAA,IAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,SAASA,IAAE;AAAC,UAAI,IAAE;AAAK,MAAAA,OAAI,EAAE,qBAAmBA,IAAE,KAAK,4BAA4BA,EAAC,GAAE,QAAQ,QAAQ,EAAE,KAAK,WAAU;AAAC,eAAO,EAAE,4BAA4BA,EAAC;AAAA,MAAC,CAAC,IAAG,KAAK,OAAO,UAAQA,IAAG,0BAAwB;AAAA,IAAE,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,SAASE,IAAE;AAAC,aAAM,EAAC,aAAYA,IAAE,cAAa,OAAG,iBAAgB,MAAG,QAAO,WAAU;AAAC,YAAIF,IAAE;AAAE,eAAO,OAAK,SAAOA,KAAEE,MAAGA,MAAG,SAAO,IAAE,KAAK,WAAS,SAAOF,KAAE,EAAE,WAAS,SAAOA,GAAE,KAAK,CAAC,MAAIE,eAAa,WAASA,GAAE,IAAI,IAAEA,MAAG,SAAOF,GAAE,WAAS,SAAO,IAAEE,GAAE,CAAC,MAAI,EAAE,UAAQF,KAAE,KAAK,QAAQ,KAAI,IAAE,yBAAyBA,IAAE,UAAU,GAAEE,GAAE,CAAC,EAAE,QAAM,eAAe,eAAe,CAAC,GAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,KAAK,IAAGA;AAAA,MAAC,EAAC;AAAA,IAAC,EAAC,GAAE,EAAC,KAAI,wBAAuB,OAAM,WAAU;AAAC,QAAE,uBAAqB,EAAE,mBAAmB,0BAAwB,MAAK,EAAE,qBAAmB;AAAA,IAAK,EAAC,GAAE,EAAC,KAAI,kBAAiB,OAAM,SAASF,IAAE;AAAC,UAAIE,KAAE,MAAK,IAAE,IAAE,UAAU,UAAQ,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC;AAAE,cAAO,IAAE,UAAU,SAAO,UAAU,CAAC,IAAE,WAASF,MAAGA,GAAE,CAAC,IAAEA,GAAE,IAAI,SAASA,IAAE,GAAE;AAAC,eAAO,gBAAgBE,GAAE,WAAWF,cAAa,WAASA,KAAE,CAACA,EAAC,CAAC,GAAE,eAAe,eAAe,eAAe,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAC,SAAQ,MAAG,cAAa,EAAC,CAAC,CAAC,EAAE,OAAO,EAAC,MAAK,QAAMA,MAAG,SAAOA,KAAEA,GAAE,QAAM,SAAOA,GAAE,QAAM,EAAC,CAAC;AAAA,MAAC,CAAC,IAAE,gBAAgB,KAAK,WAAWA,EAAC,GAAE,eAAe,eAAe,eAAe,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAC,SAAQ,MAAG,cAAa,EAAC,CAAC,CAAC,EAAE,OAAO;AAAA,IAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,UAAIA,IAAE,GAAEE,IAAE,IAAE,MAAK,IAAE,KAAK,OAAM,IAAE,EAAE,UAAS,IAAE,yBAAyB,GAAE,UAAU,GAAE,IAAE,CAAC,GAAE,IAAE,CAAC;AAAE,WAAIF,MAAK;AAAE,YAAEA,IAAEE,KAAE,QAAO,EAAE,eAAe,CAAC,KAAG,QAAM,EAAE,CAAC,MAAI,EAAE,CAAC,EAAE,UAAQ,EAAE,CAAC,EAAE,YAAU,EAAE,CAAC,IAAE,EAAE,CAAC,EAAE,aAAWA,KAAE,EAAE,CAAC,GAAE,EAAE,yBAAuB,EAAE,CAAC,EAAE,kBAAgB,EAAE,CAAC,EAAE,UAAQ,EAAE,yBAAwB,EAAE,CAAC,IAAE,EAAE,sBAAsB,EAAE,CAAC,EAAE,gBAAe,EAAE,gBAAe,CAAC,GAAE,EAAE,CAAC,aAAY,QAAM,EAAE,CAAC,IAAE,mBAAmB,EAAE,CAAC,CAAC,IAAE,KAAG,CAAC,UAAS,QAAQ,EAAE,QAAQ,QAAQ,EAAE,CAAC,CAAC,CAAC,IAAE,EAAE,CAAC,IAAE,CAAC,EAAE,CAAC,CAAC,IAAE,aAAW,QAAQ,EAAE,CAAC,CAAC,MAAI,EAAE,CAAC,IAAE,eAAe,CAAC,GAAE,EAAE,CAAC,CAAC,MAAI,EAAE,CAAC,IAAE,eAAe,CAAC,GAAE,gBAAgB,EAAE,WAAW,EAAE,CAAC,CAAC,GAAE,eAAe,eAAe,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAC,SAAQ,MAAG,cAAa,EAAC,CAAC,CAAC,EAAE,OAAO,CAAC,GAAE,EAAE,CAAC,EAAE,cAAYA,KAAG,EAAE,CAAC,IAAE,EAAE,CAAC,KAAG,EAAE,CAAC,EAAE,iBAAe,EAAE,CAAC,IAAE,EAAE,CAAC,EAAE,EAAE,UAAU,GAAE,EAAE,CAAC,IAAE,EAAE,CAAC;AAAI,UAAI,GAAE,IAAE,CAAC;AAAE,aAAO,EAAE,MAAI,KAAK,QAAO,EAAE,UAAQ,KAAK,MAAM,YAAU,KAAK,MAAM,YAAU,IAAE,GAAE,IAAE,eAAe,eAAe,eAAe,CAAC,GAAE,IAAE,EAAE,wBAAsB,EAAE,sBAAsB,GAAE,KAAK,gBAAe,CAAC,IAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,OAAO,eAAe,CAAC,MAAI,SAAS,cAAY,aAAW,QAAQ,CAAC,KAAG,EAAE,WAAS,EAAE,aAAa,KAAG,OAAO,eAAe,CAAC,MAAI,SAAS,aAAW,OAAO,EAAE,SAAI,4BAAc,GAAE,SAAS,CAAC,GAAE,GAAE,CAAC,CAAC,SAAG,4BAAc,uBAAsB,SAAS,EAAC,aAAY,GAAE,WAAU,EAAC,GAAE,CAAC,GAAE,EAAE,QAAQ;AAAA,IAAE,EAAC,CAAC,GAAE,CAAC,EAAC,KAAI,gBAAe,OAAM,WAAU;AAAC,UAAG,EAAE;AAAQ,iBAAQF,MAAK,EAAE,QAAQ;AAAM,cAAG,EAAE,QAAQ,MAAMA,EAAC,MAAI;AAAE,mBAAM;AAAA;AAAG,aAAM;AAAA,IAAE,EAAC,CAAC,CAAC,GAAE;AAAA,EAAC,EAAE;AAAE,SAAO,gBAAgB,GAAE,eAAc,cAAc,OAAO,EAAE,eAAa,EAAE,QAAM,WAAW,CAAC,GAAE;AAAC;AAAE,SAAS,gBAAgB,GAAE;AAAC,MAAI,IAAE,IAAE,UAAU,UAAQ,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC;AAAE,SAAO,EAAE,cAAY,EAAE,YAAU,IAAE,EAAE,UAAS,EAAE,YAAU,IAAE,EAAE,IAAG,IAAE,WAAW,GAAE,QAAO,IAAE,GAAE,EAAC,sBAAqB,GAAE,OAAM,SAAS,GAAE,GAAE;AAAC,QAAI,GAAE,GAAE,GAAE;AAAE,QAAG,CAAC,EAAE;AAAQ,aAAO,IAAE,CAAC,GAAE,IAAE,SAAS,CAAC,CAAC,GAAE,IAAE,mBAAmB,GAAE,cAAY,QAAO,IAAE,EAAE,6BAA2B,EAAE,sCAAoC,cAAY,QAAO,IAAE,EAAE,KAAK,EAAE,OAAM,CAAC,MAAI,OAAO,OAAO,GAAE,CAAC,GAAE,EAAE,0BAAwB,KAAG,EAAE,MAAM,6BAA2B,IAAG;AAAA,EAAC,GAAE,MAAK,WAAU;AAAC,WAAM,EAAC,gBAAe,CAAC,EAAC;AAAA,EAAC,GAAE,SAAQ,WAAU;AAAC,SAAK,0BAAwB,CAAC,GAAE,KAAK,2BAAyB;AAAA,EAAC,GAAE,UAAS,EAAC,yBAAwB,WAAU;AAAC,QAAI;AAAE,WAAO,SAAO,IAAE,KAAK,8BAA4B,SAAO,EAAE,KAAK,IAAI;AAAA,EAAC,EAAC,GAAE,QAAO,WAAU;AAAC,QAAI,IAAE,EAAE,EAAE,MAAM,eAAc,eAAe,EAAC,KAAI,QAAO,GAAE,EAAE,MAAM,sBAAoB,CAAC,CAAC,GAAE,KAAK,eAAe,IAAI,SAASA,IAAE;AAAC,UAAI,IAAEA,GAAE,QAAOA,KAAEA,GAAE;AAAI,aAAO,EAAE,GAAEA,EAAC;AAAA,IAAC,CAAC,CAAC;AAAE,WAAO,KAAK,yBAAyB,KAAK,MAAM,GAAE;AAAA,EAAC,GAAE,SAAQ,EAAC,0BAAyB,SAAS,GAAE;AAAC,QAAI,IAAE;AAAK,aAAS,EAAE,GAAE,GAAE,GAAE;AAAC,aAAO,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,EAAE,CAAC,GAAE;AAAA,IAAE;AAAC,WAAO,KAAK,CAAC,EAAE,QAAQ,SAAS,GAAE;AAAC,UAAG;AAAC,YAAI,GAAE,GAAE,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,MAAM,GAAE,EAAE,eAAa,CAAC,CAAC,CAAC,CAAC;AAAE,SAAC,EAAE,iBAAe,GAAG,QAAQ,SAASA,IAAE;AAAC,UAAAA,GAAE,YAAU,EAAE,yBAAyBA,GAAE,QAAQ;AAAA,QAAC,CAAC,GAAE,MAAI,EAAE,UAAQ,EAAE,GAAE,IAAE,EAAE,CAAC,GAAE,WAAW,KAAG,EAAE,GAAE,GAAE,eAAe,KAAG,EAAE,SAAO,YAAY,OAAK,SAAO,IAAE,EAAE,YAAU,SAAO,EAAE,WAAS,EAAE,GAAE,IAAE,EAAE,SAAS,CAAC,GAAE,WAAW,KAAG,EAAE,GAAE,GAAE,eAAe;AAAA,MAAC,SAAOA,IAAE;AAAA,MAAC;AAAA,IAAC,CAAC;AAAA,EAAC,GAAE,yBAAwB,SAAS,GAAE;AAAC,QAAI,IAAE,KAAK,wBAAwB,MAAM,KAAG,KAAK;AAA2B,SAAK,eAAe,KAAK,EAAC,QAAO,GAAE,KAAI,EAAC,CAAC;AAAA,EAAC,GAAE,2BAA0B,SAAS,GAAE;AAAC,QAAI,GAAE,IAAE,KAAK,eAAe,KAAK,SAASA,IAAE,GAAE;AAAC,UAAGA,GAAE,WAAS;AAAE,eAAO,IAAE,GAAE;AAAA,IAAE,CAAC;AAAE,SAAK,wBAAwB,KAAK,EAAE,GAAG,GAAE,KAAK,eAAe,OAAO,GAAE,CAAC;AAAA,EAAC,GAAE,wBAAuB,SAAS,GAAE,GAAE,GAAE;AAAC,QAAI,IAAE;AAAK,aAAS,EAAE,GAAE;AAAC,eAASA,KAAG;AAAC,iBAAQA,IAAEC,KAAE,MAAK,IAAE,UAAU,QAAO,IAAE,IAAI,MAAM,CAAC,GAAE,IAAE,GAAE,IAAE,GAAE;AAAI,YAAE,CAAC,IAAE,UAAU,CAAC;AAAE,eAAO,EAAE,gBAAc,EAAE,cAAc,MAAM,MAAK,CAAC,IAAE,EAAE,0BAAwBD,KAAE,EAAE,MAAM,MAAK,CAAC,GAAG,UAAQ,IAAGA,KAAE,EAAE,sBAAsBA,IAAE,EAAE,0BAAyB,CAAC,cAAa,SAAO,KAAG,QAAQA,EAAC,EAAE,QAAQ,UAAS,QAAQ,IAAEA,KAAE,mBAAmBA,EAAC,IAAE,aAAW,QAAQA,EAAC,MAAIA,KAAE,eAAe,CAAC,GAAEA,EAAC,IAAGA,MAAG,gBAAgB,EAAE,WAAU;AAAC,iBAAO,EAAE,MAAMC,IAAE,CAAC;AAAA,QAAC,CAAC,GAAE,eAAe,eAAe,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAC,SAAQ,MAAG,cAAa,EAAC,CAAC,CAAC,EAAE,OAAO;AAAA,MAAC;AAAC,aAAO,EAAE,mBAAiB,IAAED,GAAE,cAAY,IAAEA,GAAE,cAAY,GAAEA;AAAA,IAAC;AAAC,WAAO,EAAE,eAAa,MAAG;AAAA,EAAC,GAAE,2BAA0B,SAAS,GAAE;AAAC,SAAK,2BAAyB,KAAK,wBAAwB,SAAS,CAAC;AAAA,EAAC,GAAE,+BAA8B,SAAS,GAAE,GAAE;AAAC,QAAI,GAAE,GAAE,IAAE,MAAK,IAAE,IAAE,UAAU,UAAQ,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,CAAC,GAAE,IAAE,KAAK,EAAE,MAAM,SAAQ,KAAG,MAAI,EAAE,CAAC,IAAE,IAAG,EAAE,KAAK,CAAC,IAAG,CAAC,IAAG,IAAE,CAAC;AAAE,QAAG,CAAC,KAAG,QAAM,KAAG,EAAE;AAAK,eAAQ,KAAK,KAAK,UAAQ,CAAC;AAAE,SAAC,SAASC,IAAE;AAAC,cAAID;AAAE,YAAE,OAAO,eAAeC,EAAC,KAAG,QAAM,EAAE,OAAOA,EAAC,OAAKD,KAAE,EAAE,MAAM,iBAAiB,KAAK,SAASA,IAAE;AAAC,mBAAO,MAAIC,GAAE,QAAQD,EAAC;AAAA,UAAC,CAAC,MAAI,cAAYC,MAAGD,KAAEC,GAAE,QAAQ,IAAI,OAAO,IAAI,OAAOD,EAAC,CAAC,GAAE,EAAE,GAAE,EAAEA,EAAC,IAAE,EAAE,OAAOC,EAAC,GAAE,EAAED,EAAC,EAAE,SAAO,QAAI,EAAEC,EAAC,IAAE,EAAE,uBAAuB,EAAE,OAAOA,EAAC,GAAE,GAAE,SAAOD,KAAE,EAAE,EAAE,UAAQ,SAAOA,KAAEA,GAAE,YAAU,SAAOA,GAAEC,EAAC,CAAC;AAAA,QAAE,GAAG,CAAC;AAAE,KAAC,CAAC,KAAG,QAAM,KAAG,EAAE,UAAQ,IAAE,eAAe,CAAC,GAAE,CAAC,GAAE,IAAE,EAAE,SAAQ,OAAO,EAAE,UAAS,KAAK,iBAAe,KAAK,kBAAgB,CAAC,GAAE,KAAK,eAAe,OAAK,KAAK,eAAe,QAAM,CAAC,GAAE,KAAK,eAAe,QAAM,KAAK,eAAe,SAAO,CAAC;AAAE,QAAI,IAAE,EAAC,MAAK,WAAU;AAAC,QAAE,eAAe,OAAK,eAAe,eAAe,eAAe,CAAC,GAAE,IAAE,EAAC,UAAS,EAAC,IAAE,EAAC,UAAS,KAAI,CAAC,GAAE,CAAC,GAAE,CAAC;AAAA,IAAC,GAAE,OAAM,WAAU;AAAC,QAAE,eAAe,QAAM,EAAE;AAAA,IAAM,EAAC;AAAE,QAAG,KAAG,OAAO,KAAK,CAAC,EAAE,QAAQ,SAASD,IAAE;AAAC,aAAO,EAAEA,EAAC,EAAE;AAAA,IAAC,CAAC,GAAE,GAAE;AAAC,UAAS,IAAT,WAAY;AAAC,UAAE,2BAAyB,EAAE,wBAAwB,SAAS,SAASC,IAAE;AAAC,iBAAO,OAAO,KAAKA,EAAC,EAAE,QAAQ,SAASD,IAAE;AAAC,cAAE,WAAS,eAAaA,MAAG,OAAOC,GAAED,EAAC;AAAA,UAAC,CAAC,GAAE,eAAe,eAAe,eAAe,eAAe,CAAC,GAAE,EAAE,eAAe,GAAE,OAAO,EAAE,uBAAuB,CAAC,GAAE,CAAC,EAAE,WAAS,EAAE,eAAe,IAAI,GAAE,OAAO,EAAE,eAAe,KAAK,CAAC;AAAA,QAAC,CAAC,GAAE,EAAE,kBAAgB;AAAA,MAAI;AAAC,OAAC,KAAK,mBAAiB,KAAK,mBAAiB,KAAK,UAAU,WAAU;AAAC,UAAE,GAAE,EAAE,kBAAgB;AAAA,MAAE,CAAC,GAAE,KAAK,oBAAkB,aAAa,KAAK,WAAW,GAAE,KAAK,cAAY,WAAW,WAAU;AAAC,qBAAa,EAAE,WAAW,GAAE,EAAE,GAAE,EAAE,kBAAgB;AAAA,MAAE,CAAC,IAAG,KAAK,kBAAgB,eAAe,eAAe,CAAC,GAAE,KAAK,mBAAiB,CAAC,CAAC,GAAE,eAAe,eAAe,eAAe,eAAe,CAAC,GAAE,CAAC,GAAE,KAAK,OAAO,QAAM,EAAC,WAAU,KAAK,OAAO,MAAK,IAAE,CAAC,CAAC,GAAE,eAAe,CAAC,GAAE,CAAC,CAAC,GAAE,CAAC,GAAE,EAAC,UAAS,EAAC,GAAE,KAAK,OAAO,QAAM,EAAC,OAAM,KAAK,OAAO,MAAK,IAAE,CAAC,CAAC,CAAC,GAAE,KAAK,mBAAiB,KAAK,mBAAiB,EAAE;AAAA,IAAC,OAAK;AAAC,QAAE,KAAK,GAAE,EAAE,MAAM;AAAE,UAAI,IAAE,qBAAqB,GAAE,GAAE,IAAI,GAAE,QAAE,4BAAc,GAAE,SAAS,CAAC,GAAE,OAAO,KAAK,MAAM,GAAE,OAAO,KAAK,uBAAuB,GAAE,EAAC,UAAS,EAAC,GAAE,GAAE,GAAE,KAAK,OAAO,QAAM,EAAC,WAAU,KAAK,OAAO,MAAK,IAAE,CAAC,GAAE,GAAE,EAAC,UAAS,EAAC,GAAE,KAAK,OAAO,QAAM,EAAC,OAAM,KAAK,OAAO,MAAK,IAAE,CAAC,GAAE,EAAC,KAAI,SAASA,IAAE;AAAC,eAAO,EAAE,0BAAwBA;AAAA,MAAC,EAAC,CAAC,CAAC,GAAE,IAAE,KAAK,MAAM,OAAM,IAAE,EAAE;AAAa,UAAG;AAAE,SAAC,IAAE,EAAE,cAAc,0BAAwB;AAAA;AAAU,iBAAQG,KAAE,KAAK,SAAQA,MAAG;AAAC,cAAGA,GAAE,uBAAsB;AAAC,gBAAEA,GAAE;AAAsB;AAAA,UAAK;AAAC,cAAGA,GAAE,iBAAgB;AAAC,gBAAEA,GAAE;AAAgB;AAAA,UAAK;AAAC,UAAAA,KAAEA,GAAE;AAAA,QAAO;AAAC,WAAG,KAAK,wBAAsB,GAAE,KAAK,cAAY,WAAU;AAAC,mBAAO,+BAAa,GAAE,CAAC;AAAA,MAAC,GAAE,EAAE,gBAAgB,KAAK,WAAW,KAAG,KAAG,qBAAmB,WAAS,iBAAAC,QAAS,uDAAqD,iBAAAA,QAAS,mDAAmD,wBAAsB,OAAI,KAAK,qBAAmB,iBAAAA,QAAS,WAAW,CAAC,GAAE,KAAK,mBAAmB,OAAO,CAAC,KAAG,iBAAAA,QAAS,OAAO,GAAE,CAAC;AAAA,IAAC;AAAA,EAAC,EAAC,GAAE,SAAQ,WAAU;AAAC,QAAI,IAAE;AAAK,SAAK,mCAAiC,MAAG,QAAQ,QAAQ,EAAE,KAAK,WAAU;AAAC,QAAE,mCAAiC;AAAA,IAAE,CAAC,GAAE,aAAa,KAAK,WAAW,GAAE,KAAK,8BAA8B;AAAA,EAAC,GAAE,eAAc,WAAU;AAAC,QAAI;AAAE,iBAAa,KAAK,WAAW,GAAE,KAAK,eAAa,oBAAoB,KAAK,MAAM,KAAK,GAAE,SAAO,IAAE,KAAK,0BAAwB,EAAE,kBAAkB,KAAK,WAAW,MAAI,oBAAoB,KAAK,MAAM,KAAK,GAAE,KAAG,oBAAkB,KAAK,mBAAmB,QAAQ,IAAE,iBAAAA,QAAS,uBAAuB,KAAK,MAAM,KAAK,IAAG,kBAAkB;AAAA,EAAC,GAAE,SAAQ,WAAU;AAAC,SAAK,oCAAkC,KAAK,8BAA8B,MAAG,EAAC,MAAK,KAAE,CAAC;AAAA,EAAC,GAAE,cAAa,OAAG,OAAM,EAAC,QAAO,EAAC,SAAQ,WAAU;AAAC,SAAK,8BAA8B,MAAG,EAAC,OAAM,KAAE,CAAC;AAAA,EAAC,GAAE,MAAK,KAAE,GAAE,yBAAwB,EAAC,SAAQ,WAAU;AAAC,SAAK,8BAA8B,MAAG,EAAC,OAAM,KAAE,CAAC;AAAA,EAAC,GAAE,MAAK,KAAE,EAAC,EAAC;AAAC;AAAC,IAAI,qBAAmB,oBAAI,IAAI,CAAC,WAAU,iBAAgB,iBAAgB,UAAS,aAAY,eAAc,cAAa,eAAc,cAAa,eAAc,UAAS,eAAc,gBAAe,gBAAe,eAAc,cAAa,eAAc,aAAY,YAAW,WAAU,aAAY,WAAU,YAAW,WAAU,UAAS,iBAAgB,iBAAgB,eAAc,mBAAkB,uBAAsB,wBAAuB,kBAAiB,kBAAiB,iBAAgB,gBAAe,YAAW,iBAAgB,cAAa,eAAc,gBAAe,YAAW,WAAU,WAAU,aAAY,oBAAmB,oBAAmB,aAAY,eAAc,WAAU,WAAU,gBAAe,oBAAmB,eAAc,WAAU,UAAS,aAAY,cAAa,gBAAe,YAAW,aAAY,aAAY,aAAY,gBAAe,kBAAiB,aAAY,UAAS,WAAU,oBAAmB,kBAAiB,wBAAuB,mBAAkB,UAAU,CAAC;AAAE,SAAS,oBAAoB,GAAE,GAAE;AAAC,WAAQ,IAAE,SAAO,IAAE,KAAG,QAAM,IAAE,SAAO,EAAE,qBAAmB,QAAM,IAAE,SAAO,EAAE,wBAAsB,KAAG,SAAO,EAAE,QAAO,KAAG;AAAC,QAAI,IAAE,EAAE;AAAU,QAAG,KAAG,QAAM,IAAE,SAAO,EAAE,yBAAuB,QAAM,IAAE,SAAO,EAAE;AAAyB,aAAO;AAAE,QAAE,EAAE;AAAA,EAAM;AAAC;AAAC,SAAS,gBAAgB,GAAE,GAAE,GAAE;AAAC,MAAI,IAAE,CAAC;AAAE,SAAO,EAAE,QAAQ,SAASJ,IAAE;AAAC,MAAEA,EAAC,IAAE;AAAA,EAAE,CAAC,GAAE,GAAG,iBAAe,IAAE,UAAQ,KAAG,WAAW,IAAE;AAAC;AAAC,SAAS,UAAU,GAAE,GAAE,GAAE;AAAC,MAAI,IAAE,MAAK,IAAE,IAAE,UAAU,UAAQ,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,WAAU,IAAE;AAAE,MAAG,EAAE,aAAa;AAAO,UAAM,MAAM,6CAA6C,OAAO,GAAE,4GAA4G,CAAC;AAAE,MAAG,cAAY,OAAO,EAAE,CAAC;AAAE,UAAM,MAAM,6CAA6C,OAAO,GAAE,4FAA4F,CAAC;AAAE,MAAI,IAAE,EAAE,CAAC,GAAE,KAAG,YAAU,OAAO,EAAE,CAAC,KAAG,IAAE,EAAE,CAAC,GAAE,EAAE,CAAC,aAAY,SAAO,gBAAgB,GAAE,GAAE,EAAE,CAAC,CAAC,KAAG,EAAE,CAAC,aAAY,SAAO,gBAAgB,GAAE,GAAE,EAAE,CAAC,CAAC,GAAE,EAAE,cAAY,CAAC;AAAG,IAAE,cAAY,CAAC,IAAE,cAAY,OAAO,IAAE,WAAU;AAAC,aAAQA,KAAE,UAAU,QAAOC,KAAE,IAAI,MAAMD,EAAC,GAAEE,KAAE,GAAEA,KAAEF,IAAEE;AAAI,MAAAD,GAAEC,EAAC,IAAE,UAAUA,EAAC;AAAE,MAAE,MAAM,GAAED,EAAC,GAAE,EAAE,MAAM,GAAEA,EAAC;AAAA,EAAC,IAAE,GAAE,EAAE,CAAC,IAAE,EAAE,CAAC;AAAC;AAAC,SAAS,YAAY,GAAE;AAAC,MAAI,IAAE,MAAK,IAAE,CAAC,GAAE,IAAE,eAAe,CAAC,GAAE,CAAC;AAAE,SAAO,OAAO,KAAK,CAAC,EAAE,QAAQ,SAAS,GAAE;AAAC,QAAI,GAAE,IAAE,EAAE,MAAM,mBAAmB;AAAE,QAAG;AAAE,aAAO,EAAE,CAAC,GAAE,IAAE,EAAE,YAAY,OAAO,EAAE,CAAC,CAAC,CAAC,GAAE,EAAE,YAAY,OAAO,EAAE,CAAC,CAAC,CAAC,IAAE,cAAY,OAAO,IAAE,WAAU;AAAC,iBAAQD,KAAE,UAAU,QAAOC,KAAE,IAAI,MAAMD,EAAC,GAAEE,KAAE,GAAEA,KAAEF,IAAEE;AAAI,UAAAD,GAAEC,EAAC,IAAE,UAAUA,EAAC;AAAE,UAAE,MAAM,GAAED,EAAC,GAAE,EAAE,CAAC,EAAE,MAAM,GAAEA,EAAC;AAAA,MAAC,IAAE,EAAE,CAAC;AAAA,aAAU,IAAE,EAAE,MAAM,+BAA+B;AAAE,UAAE,EAAE,CAAC,KAAG,EAAE,CAAC,KAAG,cAAa,UAAU,GAAE,EAAE,CAAC,GAAE,CAAC,GAAE,OAAO,EAAE,CAAC;AAAA,aAAU,eAAa,GAAE;AAAC,UAAG,aAAW,QAAQ,EAAE,CAAC,CAAC,KAAG,EAAE,CAAC,aAAY;AAAM,cAAM,MAAM,uGAAuG;AAAE,UAAI,IAAE,EAAE,CAAC;AAAE,aAAO,KAAK,CAAC,EAAE,QAAQ,SAASD,IAAE;AAAC,kBAAU,GAAE,EAAEA,EAAC,GAAEA,IAAE,UAAU;AAAA,MAAC,CAAC,GAAE,OAAO,EAAE,CAAC;AAAA,IAAC;AAAA,EAAC,CAAC,GAAE,eAAe,eAAe,CAAC,GAAE,CAAC,GAAE,CAAC;AAAC;AAAC,IAAI,WAAS,WAAU;AAAC,WAAS,IAAG;AAAC,oBAAgB,MAAK,CAAC,GAAE,gBAAgB,MAAK,QAAO,oBAAI,KAAG;AAAA,EAAC;AAAC,SAAO,aAAa,GAAE,CAAC,EAAC,KAAI,eAAc,OAAM,SAASA,IAAE;AAAC,QAAI,IAAEA,MAAG,KAAK,OAAO,IAAE,IAAI,OAAO,CAAC;AAAE,WAAO,KAAK,KAAK,IAAI,CAAC,IAAE,KAAK,YAAYA,EAAC,KAAG,KAAK,KAAK,IAAI,CAAC,GAAE;AAAA,EAAE,EAAC,CAAC,CAAC,GAAE;AAAC,EAAE;AAAE,SAAS,gBAAgB,GAAE,GAAE;AAAC,MAAI,GAAE,IAAE,EAAE;AAAK,MAAG,cAAY,OAAO,MAAI,IAAE,EAAE,IAAG,SAAO,IAAE,MAAI,EAAE,WAAS,cAAY,OAAO,KAAG,SAAO,IAAE,MAAI,EAAE,SAAS,EAAE,MAAM,WAAW,MAAI,IAAE,OAAM,KAAG,CAAC,UAAS,QAAQ,EAAE,QAAQ,QAAQ,CAAC,CAAC;AAAE,WAAO;AAAE,MAAG,aAAa,OAAM;AAAC,QAAG,MAAI,EAAE;AAAO,aAAO;AAAE,QAAE,EAAE,CAAC;AAAA,EAAC;AAAC,SAAO,eAAe,eAAe,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAC,KAAI,EAAC,CAAC;AAAC;AAAC,IAAI,SAAO,gBAAgB,eAAe;AAAE,SAAS,QAAQ,GAAE;AAAC,SAAO,EAAE,QAAO,EAAC,MAAK,WAAU;AAAC,WAAO,EAAE;AAAA,EAAI,EAAC,CAAC;AAAC;AAAC,QAAQ,2BAAqB,yBAAW,eAAe;AAAE,IAAI,cAAY,CAAC,aAAY,MAAM;AAAnC,IAAqC,eAAa,CAAC,aAAY,UAAS,YAAW,SAAQ,OAAO;AAAlG,IAAoG,eAAa,CAAC,aAAY,WAAW;AAAzI,IAA2I,cAAY;AAAvJ,IAAwK,SAAO,IAAI;AAAS,SAAS,mBAAmB,GAAE,GAAE;AAAC,MAAI;AAAE,SAAO,IAAE,YAAU,OAAO,KAAG,IAAE,SAAO,IAAE,EAAE,MAAI,SAAO,IAAE,EAAE,eAAa,SAAO,IAAE,EAAE,QAAM,SAAO,IAAE,EAAE,aAAW,SAAO,EAAE,KAAK,GAAE,CAAC,IAAE;AAAC;AAAC,SAAS,cAAc,GAAE;AAAC,MAAG;AAAE,WAAO,OAAO,KAAK,CAAC,EAAE,QAAQ,SAAS,GAAE;AAAC,UAAI,IAAE,EAAE,CAAC;AAAE,cAAM,MAAI,cAAY,OAAO,KAAG,EAAE,CAAC,IAAE,GAAE,EAAE,CAAC,EAAE,gBAAc,MAAI,EAAE,CAAC,IAAE,WAAU;AAAC,eAAO;AAAA,MAAC,GAAE,EAAE,CAAC,EAAE,YAAU,IAAG,EAAE,gBAAc,EAAE,CAAC,EAAE,cAAY,EAAE;AAAA,IAAa,CAAC,GAAE;AAAC;AAAC,SAAS,YAAY,GAAE;AAAC,MAAI;AAAE,SAAO,SAAO,IAAE,EAAE,QAAM,SAAO,EAAE,KAAK,CAAC;AAAC;AAAC,IAAI,mBAAa,yBAAW,SAAS,GAAE,GAAE;AAAC,MAAI,GAAE,IAAE,EAAE,WAAU,IAAE,EAAE,MAAK,IAAE,yBAAyB,GAAE,WAAW;AAAE,MAAG,QAAM,KAAG,QAAM;AAAE,WAAO;AAAK,MAAG,QAAM,GAAE;AAAC,QAAG,EAAE,YAAU,YAAU,OAAO,KAAG,YAAU,OAAO;AAAE,aAAO;AAAE,kBAAY,OAAO,MAAI,IAAE,GAAE,IAAE,WAAU;AAAC,aAAO;AAAA,IAAC;AAAA,EAAE;AAAC,MAAI,GAAE,IAAE,KAAG,aAAY,IAAE,WAAW,EAAE,WAAW,KAAG,CAAC,GAAE,QAAO,IAAE,GAAE,IAAE,EAAE,6BAA2B,EAAE;AAAiC,SAAO,EAAE,WAAS,cAAY,OAAO,MAAI,IAAE,EAAE,CAAC,QAAG,4BAAc,oBAAmB,SAAS,CAAC,GAAE,eAAe,eAAe,eAAe,eAAe,EAAC,WAAU,EAAC,GAAE,IAAE,EAAC,MAAK,EAAC,IAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,gBAAgB,CAAC,GAAE,aAAY,CAAC,CAAC,GAAE,EAAC,KAAI,EAAC,CAAC,CAAC;AAAC,CAAC;AAAhoB,IAAkoB,qBAAmB,WAAU;AAAC,YAAU,GAAE,sBAAS;AAAE,MAAI,IAAE,aAAa,CAAC;AAAE,WAAS,EAAE,GAAE;AAAC,QAAI;AAAE,WAAO,gBAAgB,MAAK,CAAC,IAAG,IAAE,EAAE,KAAK,MAAK,CAAC,GAAG,QAAM,EAAC,SAAQ,CAAC,EAAC,GAAE,EAAE,0BAAwB,CAAC,GAAE,EAAE,2BAAyB,GAAE,EAAE,gCAA8B,EAAE,WAAU,EAAE,8BAA4B,EAAE,4BAA4B,KAAK,uBAAuB,CAAC,CAAC,GAAE,EAAE,kCAAgC,EAAE,4BAA4B,GAAE;AAAA,EAAC;AAAC,SAAO,aAAa,GAAE,CAAC,EAAC,KAAI,mBAAkB,OAAM,SAAS,GAAE;AAAC,QAAI,IAAE,KAAK,MAAM,SAAQE,KAAE,KAAK,wBAAwB,MAAM,KAAG,KAAK;AAA2B,MAAE,KAAK,EAAC,QAAO,GAAE,KAAIA,GAAC,CAAC,GAAE,KAAK,SAAS,EAAC,SAAQ,EAAC,CAAC;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,qBAAoB,OAAM,SAASA,IAAE;AAAC,QAAIG,IAAE,IAAE,KAAK,MAAM,SAAQ,IAAE,EAAE,KAAK,SAASL,IAAEC,IAAE;AAAC,UAAGD,GAAE,WAASE;AAAE,eAAOG,KAAEJ,IAAE;AAAA,IAAE,CAAC;AAAE,SAAK,wBAAwB,KAAK,EAAE,GAAG,GAAE,EAAE,OAAOI,IAAE,CAAC,GAAE,KAAK,oBAAkB,KAAK,SAAS,EAAC,SAAQ,EAAC,CAAC;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,+BAA8B,OAAM,WAAU;AAAC,QAAI,IAAE,MAAKH,KAAE,CAAC,GAAE,IAAE,KAAK,MAAM,WAAW;AAAE,WAAO,EAAE,WAAS,OAAO,KAAK,KAAK,KAAK,EAAE,QAAQ,SAASF,IAAE;AAAC,yBAAmB,IAAIA,EAAC,KAAG,cAAY,OAAO,EAAE,MAAMA,EAAC,MAAIE,GAAEF,EAAC,IAAE,EAAE,MAAMA,EAAC;AAAA,IAAE,CAAC,GAAE,EAAE,IAAI,kBAAgBE,KAAE,eAAe,eAAe,CAAC,GAAEA,EAAC,GAAE,EAAE,IAAI,aAAa,MAAI,EAAE,IAAI,uBAAqBA,KAAE,eAAe,eAAe,CAAC,GAAEA,EAAC,GAAE,EAAE,IAAI,kBAAkB,IAAG,EAAE,IAAI,qBAAiB,4BAAc,OAAM,SAAS,CAAC,GAAE,EAAE,IAAI,oBAAmB,EAAC,KAAI,KAAK,6BAA4B,KAAI,KAAI,CAAC,CAAC,GAAEA,EAAC;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,yBAAwB,OAAM,SAAS,GAAE,GAAEA,IAAE;AAAC,QAAIG,IAAE,GAAE,GAAE,GAAE,IAAE;AAAK,WAAO,MAAI,KAAK,UAAQA,KAAE,EAAE,WAAU,EAAE,WAAW,GAAE,IAAE,YAAU,IAAE,EAAE,SAAS,KAAG,OAAK,GAAE,IAAE,EAAE,UAAS,IAAE,yBAAyB,GAAE,CAAC,aAAY,aAAY,WAAU,UAAU,EAAE,IAAI,cAAc,CAAC,GAAE,KAAK,kCAAgCA,MAAG,KAAK,mBAAmBA,EAAC,GAAE,CAAC,CAACA,GAAE,oBAAkB,KAAK,yBAAuB,MAAI,IAAE,KAAG,CAAC,GAAE,aAAW,QAAQ,CAAC,KAAG,aAAa,SAAO,EAAE,WAAS,EAAE,UAAQ,IAAE,IAAE,KAAI,IAAE,KAAK,sBAAsB,MAAM,WAAS,OAAO,KAAK,CAAC,EAAE,QAAQ,SAASL,IAAE;AAAC,aAAO,EAAEA,EAAC;AAAA,IAAC,CAAC,GAAE,MAAI,MAAI,KAAK,sBAAsB,MAAM,SAAO,CAAC,IAAG,OAAO,OAAO,KAAK,sBAAsB,MAAM,QAAO,cAAc,CAAC,CAAC,IAAG,OAAO,KAAK,KAAK,sBAAsB,KAAK,EAAE,QAAQ,SAASA,IAAE;AAAC,mBAAWA,MAAG,OAAO,EAAE,sBAAsB,MAAMA,EAAC;AAAA,IAAC,CAAC,GAAE,KAAK,yBAAuB,OAAO,OAAO,KAAK,sBAAsB,OAAM,YAAY,CAAC,CAAC,GAAE,QAAI;AAAA,EAAQ,EAAC,GAAE,EAAC,KAAI,wBAAuB,OAAM,WAAU;AAAC,SAAK,YAAU,KAAK,oBAAoB,0BAA0B,KAAK,SAAS,KAAG,KAAK,yBAAuB,KAAK,sBAAsB,EAAE,WAAW,IAAI,QAAQ,GAAE,OAAO,KAAK,OAAO,KAAK,qBAAqB;AAAA,EAAE,EAAC,GAAE,EAAC,KAAI,+BAA8B,OAAM,SAAS,GAAE;AAAC,QAAIE,KAAE,MAAK,IAAE,MAAK,IAAE,KAAK,OAAM,KAAG,EAAE,WAAU,EAAE,WAAW,IAAGG,KAAE,EAAE,UAAS,IAAE,EAAE,SAAS,GAAE,IAAE,WAAS,IAAE,CAAC,IAAE,GAAE,IAAE,yBAAyB,GAAE,CAAC,aAAY,aAAY,YAAW,SAAS,EAAE,IAAI,cAAc,CAAC;AAAE,aAAS,EAAEL,IAAE;AAAC,WAAK,0BAAwB,KAAK,wBAAsBA;AAAA,IAAE;AAAC,IAAAK,OAAI,aAAW,QAAQA,EAAC,KAAGA,cAAa,SAAOA,GAAE,WAAS,EAAE,UAAQA,KAAE,IAAEA,MAAI,IAAE,cAAc,CAAC,OAAK,EAAE,SAAO,IAAG,IAAE,EAAE,KAAK,IAAI;AAAE,QAAI,GAAE,IAAE,eAAe,CAAC,GAAE,YAAY,CAAC,CAAC,GAAE,IAAE,EAAC,MAAK,WAAU;AAAC,aAAO,EAAE,UAAQ,EAAC,UAAS,EAAE,8BAA8B,YAAW,IAAE;AAAA,IAAC,GAAE,SAAQ,WAAU;AAAC,WAAK,kBAAgB,GAAE,EAAE,IAAI;AAAA,IAAC,GAAE,SAAQ,EAAC,gBAAe,SAASL,IAAE;AAAC,cAAO,IAAE,UAAU,SAAO,UAAU,CAAC,IAAE,WAASA,MAAGA,GAAE,CAAC,IAAEA,GAAE,IAAI,SAASA,IAAEC,IAAE;AAAC,eAAO,EAAE,SAAQ,EAAC,MAAKD,IAAE,MAAK,QAAMA,MAAG,SAAOA,KAAEA,GAAE,QAAM,SAAOA,GAAE,QAAMC,GAAC,CAAC;AAAA,MAAC,CAAC,IAAE,EAAE,SAAQ,EAAC,MAAKD,GAAC,CAAC;AAAA,IAAC,GAAE,gBAAe,SAAS,GAAEA,IAAE;AAAC,UAAIC,IAAE,IAAE,MAAKC,MAAG,KAAK,eAAe,iBAAe,KAAK,eAAe,eAAa,CAAC,IAAG,eAAe,CAAC,GAAEF,EAAC;AAAG,WAAIC,MAAKC;AAAE,SAAC,SAASI,IAAE;AAAC,cAAIN,IAAEO;AAAE,WAACL,GAAE,eAAeI,EAAC,KAAG,SAAON,KAAEE,GAAEI,EAAC,OAAKJ,GAAEI,EAAC,KAAGC,KAAEP,IAAE,WAAU;AAAC,qBAAQA,IAAEC,IAAEC,IAAEG,IAAEG,KAAE,UAAU,QAAOC,KAAE,IAAI,MAAMD,EAAC,GAAEE,KAAE,GAAEA,KAAEF,IAAEE;AAAI,cAAAD,GAAEC,EAAC,IAAE,UAAUA,EAAC;AAAE,mBAAOH,GAAE,cAAYA,GAAE,YAAY,MAAM,GAAEE,EAAC,KAAGP,KAAEK,GAAE,WAAUF,KAAEE,GAAE,eAAcL,KAAEA,OAAI,QAAMG,KAAE,SAAOA,GAAE,MAAM,GAAEI,EAAC,IAAGJ,KAAE,EAAE,uBAAsB,SAAOL,KAAE,EAAE,eAAe,aAAaM,EAAC,MAAI,SAAON,KAAEA,GAAE,cAAY,SAAOA,KAAEA,GAAE,QAAMA,GAAE,2BAAyBC,KAAE,EAAE,eAAe,aAAaK,EAAC,GAAE,QAAQ,QAAQ,EAAE,KAAK,WAAU;AAAC,kBAAIN;AAAE,uBAAOA,KAAEC,OAAI,SAAOD,KAAEA,GAAE,cAAY,SAAOA,KAAEA,GAAE,QAAM,SAAOA,KAAEA,GAAE,4BAA0BA,GAAE,SAAS,EAAC,UAASO,GAAE,MAAM,GAAEE,EAAC,EAAC,CAAC;AAAA,YAAC,CAAC,MAAIR,KAAEI,MAAGH,KAAE,CAACG,GAAEH,IAAE,EAAE,cAAc,CAAC,IAAE,EAAE,gBAAgB,WAAU;AAAC,qBAAOK,GAAE,MAAM,GAAEE,EAAC;AAAA,YAAC,GAAE,eAAe,eAAe,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAC,SAAQ,MAAG,cAAa,EAAC,CAAC,CAAC,CAAC,GAAE,EAAE,eAAe,aAAaH,EAAC,IAAEL,KAAGM,GAAE,gBAAcN,GAAE,gBAAcM,GAAE,gBAAcA,GAAE,cAAYN,GAAE,YAAUM,GAAE,YAAWN;AAAA,UAAE,IAAGC,GAAEI,EAAC,EAAE,gBAAcN;AAAA,QAAE,GAAGC,EAAC;AAAE,aAAOC;AAAA,IAAC,EAAC,GAAE,SAAQ,WAAU;AAAC,QAAE,gBAAgB,IAAI,GAAE,EAAE,mBAAiB,KAAK,MAAM,iBAAgB,KAAK,MAAM,gBAAgB,kBAAgB;AAAA,IAAC,GAAE,eAAc,WAAU;AAAC,QAAE,mBAAiB,MAAK,KAAK,MAAM,gBAAgB,kBAAgB;AAAA,IAAI,GAAE,QAAO,WAAU;AAAC,UAAIF,KAAE,MAAKC,KAAE,KAAK,OAAMC,MAAGD,GAAE,WAAUA,GAAE,SAAQI,MAAGJ,GAAE,UAASA,GAAE,QAAOO,KAAEP,GAAE,OAAMA,KAAE,yBAAyBA,IAAE,YAAY,GAAEQ,KAAE,KAAK,eAAe,GAAE,eAAe,CAAC,GAAEP,EAAC,CAAC,GAAEA,KAAED,GAAE,WAAUS,KAAET,GAAE,WAAUA,KAAE,yBAAyBA,IAAE,YAAY,GAAEK,KAAE,CAAC;AAAE,aAAO,OAAO,KAAKG,EAAC,EAAE,QAAQ,SAAST,IAAE;AAAC,YAAIC,KAAEQ,GAAET,EAAC;AAAE,QAAAM,GAAEN,EAAC,IAAE,cAAY,OAAOC,KAAEA,KAAE,WAAU;AAAC,iBAAOA;AAAA,QAAC;AAAA,MAAC,CAAC,GAAE,EAAE,mBAAmB,EAAE,+BAA8B,IAAI,GAAE,eAAe,eAAe,eAAe,eAAe,CAAC,GAAEA,EAAC,GAAEI,MAAGH,MAAGQ,KAAE,EAAC,OAAML,MAAGH,MAAGQ,GAAC,IAAE,CAAC,CAAC,GAAEF,KAAE,EAAC,OAAMA,GAAC,IAAE,CAAC,CAAC,GAAE,CAAC,GAAE,EAAC,KAAI,kBAAiB,CAAC,GAAE,eAAe,CAAC,GAAE,EAAE,WAAS,KAAK,WAAS,EAAC,SAAQ,cAAY,OAAO,KAAK,WAAS,KAAK,WAAS,WAAU;AAAC,eAAOR,GAAE;AAAA,MAAQ,EAAC,IAAE,eAAe,CAAC,GAAEM,EAAC,CAAC,CAAC;AAAA,IAAC,EAAC;AAAE,UAAI,IAAE,OAAO,YAAY,0BAA0B,GAAE,EAAE,KAAG,GAAE,KAAK,wBAAsB,IAAGD,KAAE,EAAE,iBAAeA,KAAE,EAAE,cAAc,kBAAgB,IAAEA,KAAE,oBAAoB,IAAI,GAAEA,MAAG,SAAS,eAAe,CAAC,KAAG,KAAK,sBAAoBA,IAAE,KAAK,YAAU,SAASL,IAAEC,IAAE;AAAC,aAAOD,GAAE,UAAS,EAAC,IAAG,MAAI,GAAE,KAAI,EAAC,GAAE,CAACA,GAAE,OAAO,OAAO,GAAE,EAAC,QAAOE,GAAE,QAAO,CAAC,CAAC,CAAC,CAAC;AAAA,IAAC,GAAEG,GAAE,wBAAwB,KAAK,SAAS,MAAI,IAAE,UAAU,CAAC,GAAE,cAAY,OAAO,EAAE,qBAAmB,EAAE,kBAAkB,CAAC,GAAE,KAAK,wBAAsB,EAAE,MAAM,CAAC;AAAA,EAAG,EAAC,GAAE,EAAC,KAAI,sBAAqB,OAAM,SAAS,GAAE;AAAC,SAAK,0BAAwB,EAAE,kBAAgB,KAAK,sBAAsB,WAAS,cAAY,OAAO,EAAE,cAAY,EAAE,cAAY,WAAU;AAAC,aAAO,EAAE;AAAA,IAAW,KAAG,KAAK,gCAA8B,GAAE,KAAK,sBAAsB,aAAa;AAAA,EAAG,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,eAAO,4BAAc,KAAK,iCAAgC,EAAC,SAAQ,KAAK,MAAM,QAAO,CAAC;AAAA,EAAC,EAAC,CAAC,CAAC,GAAE;AAAC,EAAE;AAAE,SAAS,gBAAgB,GAAE;AAAC,MAAI,IAAE,IAAE,UAAU,UAAQ,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC,GAAE,KAAG,KAAG,QAAQ,KAAK,8CAA8C,GAAE,EAAE,cAAY,EAAE,YAAU,IAAE,EAAE,cAAS,yBAAW,SAASL,IAAE,GAAE;AAAC,eAAO,4BAAc,cAAa,SAAS,CAAC,GAAEA,IAAE,EAAC,WAAU,GAAE,KAAI,EAAC,GAAE,gBAAgB,CAAC,GAAE,aAAY,CAAC,CAAC,CAAC;AAAA,EAAC,CAAC;AAAG,SAAO,EAAE,qBAAmB,GAAE;AAAC;AAAC,SAAS,eAAe,GAAE,GAAE;AAAC,aAAO,mBAAK,WAAU;AAAC,WAAO,EAAE,EAAE,KAAK,SAASA,IAAE;AAAC,aAAM,EAAC,SAAQ,gBAAgBA,GAAE,SAAQ,CAAC,EAAC;AAAA,IAAC,CAAC;AAAA,EAAC,CAAC;AAAC;AAAC,SAAS,eAAe,GAAE,GAAE;AAAC,WAAS,IAAG;AAAC,WAAO,EAAE,EAAE,KAAK,SAASA,IAAE;AAAC,aAAO,gBAAgBA,GAAE,SAAQ,CAAC;AAAA,IAAC,CAAC;AAAA,EAAC;AAAC,MAAI,IAAE;AAAE,eAAW,QAAQ,CAAC,MAAI,IAAE,EAAE;AAAQ,SAAO,qBAAqB,aAAW,QAAQ,CAAC,IAAE,eAAe,eAAe,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAC,QAAO,EAAC,CAAC,IAAE,CAAC;AAAC;AAAC,SAAS,uBAAuB,GAAE,GAAE;AAAC,SAAO,QAAQ,KAAK,0JAA0J,GAAE,cAAY,OAAO,IAAE,QAAQ,KAAK,4DAA4D,IAAE,EAAE,mCAAiC,GAAE;AAAC;AAAC,IAAI,cAAY,CAAC,UAAU;AAAE,SAAS,oCAAoC,GAAE;AAAC,MAAI,QAAE,4BAAc,CAAC,CAAC;AAAE,SAAM,CAAC,WAAU;AAAC,eAAO,yBAAW,CAAC;AAAA,EAAC,GAAE,gBAAgB,SAASA,IAAE;AAAC,QAAI,IAAEA,GAAE,UAASA,KAAE,yBAAyBA,IAAE,WAAW;AAAE,eAAO,4BAAc,EAAE,UAAS,EAAC,OAAM,eAAe,CAAC,GAAEA,EAAC,EAAC,GAAE,CAAC;AAAA,EAAC,GAAE,EAAC,2BAA0B,EAAC,CAAC,GAAE,CAAC;AAAC;AAAC,IAAI,WAAS,IAAI;AAAS,SAAS,oCAAoC,GAAE,GAAE;AAAC,SAAO,IAAE,KAAG,SAAS,YAAY,wBAAwB,GAAE,CAAC,WAAU;AAAC,WAAO,OAAO,CAAC;AAAA,EAAC,GAAE,gBAAgB,EAAC,OAAM,SAASA,IAAE,GAAE;AAAC,WAAO,QAAQ,GAAE,EAAE,KAAK,GAAE,WAAU;AAAC,aAAO,EAAE,EAAE,MAAM,OAAO;AAAA,IAAC;AAAA,EAAC,EAAC,GAAE,EAAC,2BAA0B,EAAC,CAAC,CAAC;AAAC;AAAC,SAAS,mBAAmB,GAAE;AAAC,MAAI,IAAE,EAAE,iBAAgB,IAAE,EAAE,mBAAkB,IAAE,eAAe,oCAAoC,CAAC,GAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC;AAAE,SAAM,CAAC,GAAE,gBAAgB,GAAE,EAAC,mBAAkB,EAAC,CAAC,GAAE,CAAC;AAAC;AAAC,SAAS,YAAY,GAAE;AAAC,MAAI,IAAE,IAAE,UAAU,UAAQ,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC,GAAE,IAAE,EAAE,YAAW,IAAE,EAAE,gBAAe,KAAG,EAAE,gBAAe,gBAAgB,GAAE,KAAG,CAAC,CAAC;AAAG,SAAO,EAAE,UAAQ,SAASA,IAAE;AAAC,QAAIC,MAAG,IAAE,UAAU,UAAQ,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC,GAAG;AAAW,WAAO,KAAGD,GAAE,UAAUC,MAAG,GAAE,CAAC,GAAE;AAAA,EAAC,GAAE;AAAC;AAAC,SAAS,YAAY,GAAE;AAAC,SAAO,EAAE,QAAQ,UAAS,SAASD,IAAE,GAAE;AAAC,WAAO,EAAE,YAAY;AAAA,EAAC,CAAC;AAAC;AAAC,SAAS,YAAY,GAAE;AAAC,MAAI;AAAE,SAAO,IAAE,YAAU,OAAO,KAAG,IAAE,EAAE,KAAK,GAAG,MAAM,SAAS,EAAE,OAAO,SAAS,GAAEC,IAAE;AAAC,WAAOA,MAAG,OAAKA,KAAEA,GAAE,MAAM,SAAS,GAAG,UAAQ,OAAO,OAAO,GAAE,gBAAgB,CAAC,GAAE,YAAYA,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,CAAC,CAAC,GAAE;AAAA,EAAC,GAAE,CAAC,CAAC,IAAE,aAAW,QAAQ,CAAC,KAAG,IAAE,CAAC,GAAE,OAAO,KAAK,CAAC,EAAE,QAAQ,SAAS,GAAE;AAAC,MAAE,YAAY,CAAC,CAAC,IAAE,EAAE,CAAC;AAAA,EAAC,CAAC,GAAE,KAAG,CAAC,IAAE,CAAC;AAAC;AAAC,SAAS,YAAY,GAAE;AAAC,SAAO,IAAE,aAAa,QAAM,IAAE,YAAU,OAAO,KAAG,IAAE,EAAE,KAAK,GAAG,MAAM,KAAK,IAAE,aAAW,QAAQ,CAAC,IAAE,OAAO,KAAK,CAAC,EAAE,OAAO,SAAS,GAAE;AAAC,WAAM,CAAC,CAAC,EAAE,CAAC;AAAA,EAAC,CAAC,IAAE,CAAC,IAAE,CAAC;AAAC;AAAC,IAAI,cAAY,CAAC,KAAK;AAAE,SAAS,aAAa,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAO,CAAC,GAAE,KAAG,EAAE,KAAI,yBAAyB,GAAE,WAAW,IAAG,IAAE,CAAC,GAAE,KAAG,OAAO,KAAK,EAAE,YAAU,CAAC,CAAC,EAAE,QAAQ,SAASA,IAAE;AAAC,QAAII,KAAE,EAAE,SAASJ,EAAC,GAAED,KAAE,cAAc,MAAM,iBAAiB,KAAK,SAASA,IAAE;AAAC,aAAO,MAAIC,GAAE,QAAQD,EAAC;AAAA,IAAC,CAAC;AAAE,IAAAA,MAAG,cAAYC,MAAGD,KAAEC,GAAE,QAAQ,IAAI,OAAO,IAAI,OAAOD,EAAC,CAAC,GAAE,EAAE,EAAE,QAAQ,aAAY,UAAU,GAAE,EAAEA,EAAC,IAAE,EAAEK,GAAE,GAAE,GAAE,CAAC,KAAG,EAAEJ,EAAC,IAAE,WAAU;AAAC,eAAQD,KAAE,UAAU,QAAOC,KAAE,IAAI,MAAMD,EAAC,GAAEE,KAAE,GAAEA,KAAEF,IAAEE;AAAI,QAAAD,GAAEC,EAAC,IAAE,UAAUA,EAAC;AAAE,aAAOG,GAAE,cAAYJ,IAAE,EAAEI,GAAE,MAAM,MAAKJ,EAAC,GAAE,GAAE,CAAC;AAAA,IAAC;AAAA,EAAC,CAAC,GAAE,CAAC,IAAG,IAAE,YAAY,EAAE,KAAK,GAAE,IAAE,MAAM,KAAK,IAAI,IAAI,YAAY,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG;AAAE,SAAO,IAAE,OAAO,KAAK,CAAC,EAAE,WAAS,EAAE,QAAM,IAAG,OAAK,MAAI,EAAE,YAAU,IAAG,OAAO,OAAO,GAAE,eAAe,eAAe,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,GAAE,OAAO,EAAE,OAAM;AAAC;AAAC,SAAS,YAAY,GAAE;AAAC,SAAO,EAAE,SAAO;AAAI;AAAC,IAAI,WAAS,IAAI;AAAS,SAAS,aAAa,GAAE,GAAE;AAAC,MAAI;AAAE,SAAO,KAAG,SAAO,IAAE,EAAE,QAAM,SAAO,EAAE,cAAQ,4BAAc,eAAc,EAAC,OAAM,EAAC,GAAE,CAAC,IAAE;AAAC;AAAC,IAAI,gBAAc,WAAU;AAAC,YAAU,GAAE,sBAAS;AAAE,MAAI,IAAE,aAAa,CAAC;AAAE,WAAS,EAAE,GAAE;AAAC,QAAI;AAAE,WAAO,gBAAgB,MAAK,CAAC,IAAG,IAAE,EAAE,KAAK,MAAK,CAAC,GAAG,QAAM,EAAC,WAAU,MAAK,iBAAgB,CAAC,GAAE,KAAI,MAAK,WAAU,EAAC,GAAE;AAAA,EAAC;AAAC,SAAO,aAAa,GAAE,CAAC,EAAC,KAAI,qBAAoB,OAAM,SAAS,GAAE;AAAC,QAAIC,KAAE,EAAE,KAAIG,KAAE;AAAG,WAAM,CAAC,KAAK,MAAM,gBAAgB,KAAK,SAASL,IAAE,GAAE;AAAC,UAAGA,GAAE,QAAME;AAAE,eAAOG,KAAE,GAAE;AAAA,IAAE,CAAC,GAAEA,EAAC;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,eAAc,OAAM,WAAU;AAAC,QAAI,IAAE,MAAK,IAAE,KAAK,OAAM,IAAE,EAAE;AAAgB,QAAG,EAAE,IAAE,EAAE,MAAK;AAAC,eAAQ,KAAG,KAAK,mBAAiB,KAAK,qBAAqB,OAAM,KAAG,MAAI,EAAE;AAAK,YAAE,EAAE;AAAM,UAAG,CAAC;AAAE;AAAO,UAAE,EAAE;AAAA,IAAS;AAAC,QAAI,IAAE,KAAK,MAAM,OAAM,IAAE,EAAE;AAAK,UAAI,EAAE,QAAQ,SAASL,IAAE;AAAC,UAAI,GAAEE,IAAEG,IAAE,GAAE,GAAE,GAAE;AAAE,MAAAL,OAAI,KAAG,IAAE,eAAe,EAAE,kBAAkBA,EAAC,GAAE,CAAC,GAAG,CAAC,GAAE,IAAE,EAAE,CAAC,GAAEE,MAAG,IAAEF,GAAE,KAAK,SAAQK,KAAE,EAAE,aAAY,IAAE,EAAE,SAAQ,IAAE,EAAE,cAAa,IAAE,EAAE,SAAQ,KAAG,EAAE,CAAC,IAAE,eAAe,eAAe,eAAe,CAAC,GAAE,CAAC,GAAEL,EAAC,GAAE,CAAC,GAAE,EAAC,UAAS,EAAE,SAAQ,CAAC,GAAE,IAAE,CAAC,GAAE,EAAE,CAAC,GAAE,GAAE,EAAE,MAAM,SAAS,GAAE,QAAM,KAAG,EAAE,MAAM,MAAK,CAAC,GAAE,QAAM,KAAG,EAAE,MAAM,MAAK,CAAC,GAAE,EAAE,CAAC,EAAE,WAASA,GAAE,UAAQ,EAAE,KAAKA,EAAC,GAAE,IAAE,CAAC,GAAEA,IAAE,GAAE,IAAI,GAAE,QAAME,MAAGA,GAAE,MAAM,MAAK,CAAC,GAAE,QAAMG,MAAGA,GAAE,MAAM,MAAK,CAAC,GAAE,QAAM,KAAG,EAAE,MAAM,MAAK,CAAC,GAAEL,GAAE,WAASA,GAAE;AAAA,IAAO,CAAC,GAAE,KAAK,SAAS,EAAC,WAAU,eAAe,CAAC,GAAE,CAAC,GAAE,iBAAgB,GAAE,KAAI,EAAC,CAAC;AAAA,EAAE,EAAC,GAAE,EAAC,KAAI,qBAAoB,OAAM,WAAU;AAAC,SAAK,YAAY;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,sBAAqB,OAAM,SAAS,GAAE;AAAC,MAAE,UAAQ,KAAK,MAAM,SAAO,KAAK,YAAY;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,wBAAuB,OAAM,WAAU;AAAC,QAAI,IAAE,MAAK,IAAE,KAAK,MAAM,OAAM,IAAE,KAAK,OAAM,IAAE,EAAE,iBAAgB,IAAE,EAAE,KAAI,IAAE,EAAE,WAAU,IAAE,EAAE;AAAK,UAAI,EAAE,QAAQ,SAASA,IAAE;AAAC,UAAI,GAAEE,IAAEG,IAAE;AAAE,MAAAL,OAAI,KAAG,IAAE,eAAe,EAAE,kBAAkBA,EAAC,GAAE,CAAC,GAAG,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,MAAIE,MAAGG,KAAEL,GAAE,KAAK,eAAcK,KAAEA,GAAE,WAAU,EAAE,CAAC,IAAE,eAAe,eAAe,CAAC,GAAE,CAAC,GAAEL,EAAC,GAAE,IAAE,CAAC,GAAE,GAAE,GAAE,CAAC,GAAE,QAAME,MAAGA,GAAE,MAAM,MAAK,CAAC,GAAE,QAAMG,MAAGA,GAAE,MAAM,MAAK,CAAC;AAAA,IAAG,CAAC,GAAE,KAAK,SAAS,EAAC,WAAU,eAAe,CAAC,GAAE,CAAC,GAAE,iBAAgB,EAAC,CAAC;AAAA,EAAE,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,QAAI,IAAE,KAAK;AAAM,MAAE;AAAM,WAAO,EAAE;AAAA,EAAQ,EAAC,CAAC,CAAC,GAAE;AAAC,EAAE;AAAE,SAAS,aAAa,GAAE,GAAE;AAAC,MAAI;AAAE,SAAM,cAAY,OAAO,MAAI,IAAE,EAAE,SAAS,GAAE,WAAS,EAAE,cAAY,EAAE,UAAU,gBAAc,MAAI,WAAS,EAAE,MAAM,GAAE,CAAC,MAAI,KAAG,OAAO,oBAAoB,EAAE,SAAS,EAAE,UAAQ,CAAC,yCAAyC,KAAK,CAAC,MAAI,EAAE,CAAC,KAAG,CAAC,oBAAoB,KAAK,CAAC,MAAI,CAAC,CAAC,4BAA4B,KAAK,CAAC,MAAI,EAAE,KAAG,CAAC,uBAAuB,KAAK,CAAC,MAAI,8BAA8B,KAAK,CAAC;AAAO;AAAC,SAAS,WAAW,GAAE;AAAC,MAAI,GAAE;AAAE,SAAM,cAAY,QAAO,SAAO,IAAE,EAAE,QAAM,SAAO,EAAE,yBAAuB,aAAa,SAAO,IAAE,EAAE,QAAM,SAAO,EAAE,oBAAoB,MAAI,IAAE,SAAO,IAAE,EAAE,OAAK,SAAO,EAAE,MAAI,YAAU,OAAO,MAAI,IAAE,GAAE,IAAE,SAASL,IAAE;AAAC,QAAI;AAAE,IAAAA,OAAI,EAAE,IAAI,EAAE,UAAQ,IAAE,eAAe,CAAC,GAAE,EAAE,IAAI,EAAE,IAAI,GAAG,CAAC,IAAEA,IAAE,EAAE,IAAI,EAAE,OAAK,IAAG,YAAU,SAAO,IAAE,EAAE,IAAI,EAAE,cAAY,SAAO,EAAE,CAAC,OAAK,EAAE,IAAI,EAAE,WAAW,CAAC,IAAEA;AAAA,EAAG,GAAE,IAAE,IAAI,MAAM,GAAE,EAAC,KAAI,SAASA,IAAE,GAAE;AAAC,WAAOA,GAAE,CAAC;AAAA,EAAC,GAAE,KAAI,SAASA,IAAE,GAAE,GAAE;AAAC,QAAI;AAAE,WAAO,SAAO,IAAE,EAAE,IAAI,EAAE,SAAO,EAAE,CAAC,OAAK,IAAE,eAAe,CAAC,GAAE,EAAE,IAAI,EAAE,IAAI,GAAG,CAAC,IAAE,GAAE,EAAE,IAAI,EAAE,OAAK,IAAG;AAAA,EAAC,EAAC,CAAC,IAAG,KAAG;AAAI;AAAC,SAAS,WAAW,GAAE,GAAE;AAAC,SAAM,CAAC,KAAG,aAAa,SAAO,MAAI,EAAE,WAAS,YAAU,OAAO,MAAI,IAAE,CAAC,CAAC,KAAI,IAAE,eAAe,CAAC,GAAE,CAAC,GAAG,QAAM,eAAe,CAAC,GAAE,EAAE,KAAK,GAAE,EAAE,QAAQ,SAASA,IAAE;AAAC,MAAE,MAAMA,EAAC,IAAE;AAAA,EAAE,CAAC,IAAG;AAAC;AAAC,IAAI,cAAY,CAAC,SAAQ,OAAO;AAAE,SAAS,kBAAkB,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,MAAI,GAAE,GAAE;AAAE,SAAM,UAAQ,KAAG,aAAa,UAAQ,IAAE,IAAE,CAAC,CAAC,IAAE,CAAC,IAAG,EAAE,SAAO,WAAW,EAAE,EAAE,UAAS,GAAE,CAAC,IAAE,YAAU,OAAO,EAAE,SAAO,UAAQ,KAAG,KAAG,EAAE,QAAQ,EAAE,IAAI,MAAI,IAAE,WAAW,CAAC,GAAE,KAAG,IAAE,EAAE,SAAO,CAAC,GAAG,OAAM,IAAE,EAAE,OAAM,IAAE,eAAe,eAAe,CAAC,GAAE,yBAAyB,GAAE,WAAW,CAAC,GAAE,CAAC,GAAE,EAAC,OAAM,YAAY,CAAC,GAAE,WAAU,MAAM,KAAK,IAAI,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,EAAC,GAAE,IAAE,EAAC,KAAI,EAAC,IAAE,CAAC,CAAC,IAAG,IAAE,EAAE,YAAU,EAAE,eAAa,IAAE,KAAG,CAAC,UAAS,QAAQ,EAAE,QAAQ,QAAQ,CAAC,CAAC,IAAE,CAAC,CAAC,IAAE,mBAAmB,CAAC,GAAG,UAAQ,IAAG,aAAa,GAAE,WAAW,aAAAW,QAAe,cAAc,EAAE,MAAK,GAAE,EAAE,GAAE,GAAE,CAAC,CAAC,GAAE,EAAE,OAAO,CAAC,KAAG,EAAE,CAAC,CAAC,GAAE,MAAK,CAAC;AAAC;AAAC,SAAS,qBAAoB;AAAC,SAAO,IAAE,UAAU,UAAQ,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC;AAAC;AAAC,SAAS,YAAY,GAAE,GAAE,GAAE;AAAC,SAAM,GAAG,IAAE,aAAa,SAAO,MAAI,EAAE,SAAO,EAAE,CAAC,IAAE,cAAa,UAAQ,QAAM,EAAE,OAAK,IAAE,EAAE,YAAU,IAAE,eAAe,CAAC,GAAE,CAAC,GAAG,MAAI,QAAQ,OAAO,CAAC,IAAG;AAAC;AAAC,SAAS,yBAAyB,GAAE;AAAC,MAAI,IAAE,EAAE,iBAAgB,IAAE,EAAE,SAAQ,IAAE,EAAE,UAAS,IAAE,WAAS,KAAG;AAAE,SAAO,SAAS,EAAE,GAAE,GAAE,GAAE;AAAC,QAAI;AAAE,WAAO,KAAG,EAAE,WAAS,IAAE,CAAC,GAAE,EAAE,QAAQ,SAASX,IAAE,GAAE;AAAC,UAAGA,MAAGA,GAAE,SAAO,SAAQ;AAAC,YAAG,SAAO,IAAEA,GAAE,SAAO,CAAC,EAAE;AAAqB,iBAAOA,GAAE,YAAU,YAAU,OAAOA,MAAG,YAAU,OAAOA,KAAE,KAAK,EAAE,KAAKA,EAAC,IAAE,YAAYA,EAAC,IAAE,MAAK,OAAKA,GAAE,SAAS,KAAK,KAAG,EAAE,KAAKA,GAAE,SAAS,KAAK,CAAC,KAAG,MAAKA,GAAE,SAAO,WAAW,IAAE,YAAY,kBAAkBA,IAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAE,OAAO,GAAE,GAAE,CAAC,GAAEA,GAAE,OAAO,GAAE,EAAE,KAAK,CAAC;AAAI,YAAI,GAAE,GAAE,IAAEA,GAAE,KAAK;AAAqB,mBAAW,IAAE,YAAY,IAAE,WAAS,IAAE,UAAQ,KAAG,aAAa,QAAM,IAAE,CAAC,CAAC,MAAI,KAAG,EAAE,QAAQ,CAAC,KAAGA,GAAE,UAAQ,EAAE,SAAQ,IAAE,aAAaA,IAAE,QAAQ,OAAO,CAAC,GAAE,GAAE,GAAE,CAAC,GAAE,IAAE,WAAWA,EAAC,GAAEA,GAAE,aAAWA,GAAE,SAAS,UAAQ,EAAE,UAAS,aAAaA,IAAE,aAAAW,QAAe,cAAc,GAAE,eAAe,eAAe,eAAe,CAAC,GAAE,mBAAmB,GAAEX,IAAE,CAAC,CAAC,GAAEA,GAAE,eAAa,CAAC,CAAC,GAAE,IAAE,EAAC,KAAI,EAAC,IAAE,CAAC,CAAC,CAAC,CAAC,KAAG,YAAYA,EAAC,IAAEA,GAAE,OAAK,kBAAkBA,IAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,GAAE,CAAC,GAAEA,GAAE,OAAO,GAAE,EAAE,KAAK,CAAC;AAAA,MAAC;AAAA,IAAC,CAAC,GAAE,MAAI,EAAE,SAAO,EAAE,CAAC,IAAE,KAAG;AAAA,EAAC;AAAC;AAAC,IAAI,iBAAe,yBAAyB,EAAC,iBAAgB,OAAM,SAAQ,MAAK,CAAC;AAAE,SAAS,oBAAoB,GAAE,GAAE;AAAC,SAAO,YAAY,GAAE,EAAC,gBAAe,eAAe,EAAC,iBAAgB,MAAG,uBAAsB,gBAAe,uBAAsB,SAASC,IAAE,GAAE,GAAE;AAAC,QAAI,IAAE,CAAC;AAAE,WAAO,OAAO,KAAKA,EAAC,EAAE,QAAQ,SAASD,IAAE;AAAC,UAAI,IAAEC,GAAED,EAAC;AAAE,YAAI,EAAE,eAAa,EAAEA,EAAC,IAAE,WAAU;AAAC,iBAAQA,KAAE,UAAU,QAAOC,KAAE,IAAI,MAAMD,EAAC,GAAEE,KAAE,GAAEA,KAAEF,IAAEE;AAAI,UAAAD,GAAEC,EAAC,IAAE,UAAUA,EAAC;AAAE,eAAO,eAAe,EAAE,YAAY,MAAM,MAAKD,EAAC,GAAE,GAAE,CAAC;AAAA,MAAC,GAAE,OAAO,eAAe,EAAED,EAAC,GAAE,UAAS,EAAC,KAAI,WAAU;AAAC,eAAO,EAAE,YAAY;AAAA,MAAM,EAAC,CAAC,KAAG,EAAE,YAAU,EAAEA,EAAC,IAAE,eAAe,EAAE,SAAQ,GAAE,CAAC;AAAA,IAAG,CAAC,GAAE,OAAO,OAAOC,IAAE,CAAC;AAAA,EAAC,EAAC,GAAE,CAAC,EAAC,CAAC;AAAC;AAAC,IAAI,mBAAiB,yBAAyB,EAAC,iBAAgB,OAAM,SAAQ,MAAK,CAAC;AAAE,SAAS,aAAa,GAAE;AAAC,SAAO,KAAG,IAAE,CAAC,IAAE,cAAY,OAAO,IAAE,EAAE,IAAE,CAAC,GAAG,KAAK,IAAE,CAAC,GAAE,iBAAiB,GAAE,SAASD,IAAE;AAAC,WAAO,aAAAW,QAAe,cAAc,cAAa,EAAC,MAAKX,GAAC,CAAC;AAAA,EAAC,CAAC;AAAC;AAAC,SAAS,cAAc,GAAE;AAAC,MAAI,IAAE,IAAE,UAAU,UAAQ,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC,GAAE,KAAG,EAAE,YAAW,EAAE;AAAgB,IAAE;AAAe,SAAO,gBAAgB,GAAE,KAAG,CAAC,CAAC;AAAC;AAAC,IAAI,cAAY,CAAC,OAAM,YAAW,SAAS;AAAE,SAAS,eAAe,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAO,CAAC,GAAE,KAAG,EAAE,KAAI,EAAE,WAAU,IAAE,EAAE,SAAS,GAAE,IAAE,WAAS,IAAE,CAAC,IAAE,GAAE,IAAE,yBAAyB,GAAE,WAAW,GAAE,KAAG,MAAI,aAAW,QAAQ,CAAC,KAAG,aAAa,SAAO,EAAE,WAAS,EAAE,UAAQ,IAAE,IAAE,IAAG,OAAM,KAAG,OAAO,KAAK,KAAG,CAAC,CAAC,EAAE,QAAQ,SAASA,IAAE;AAAC,QAAIK,KAAE,EAAEL,EAAC;AAAE,KAAC,IAAE,KAAG,CAAC,GAAGA,EAAC,IAAE,WAAU;AAAC,UAAG,cAAY,OAAOK,IAAE;AAAC,iBAAQL,KAAE,UAAU,QAAOC,KAAE,IAAI,MAAMD,EAAC,GAAEE,KAAE,GAAEA,KAAEF,IAAEE;AAAI,UAAAD,GAAEC,EAAC,IAAE,UAAUA,EAAC;AAAE,QAAAG,KAAEA,GAAE,MAAM,MAAKJ,EAAC;AAAA,MAAC;AAAC,aAAO,EAAEI,IAAE,GAAE,CAAC;AAAA,IAAC;AAAA,EAAC,CAAC,GAAE,CAAC,IAAG,IAAE,YAAY,EAAE,KAAK,GAAE,IAAE,MAAM,KAAK,IAAI,IAAI,YAAY,EAAE,SAAS,CAAC,CAAC,EAAE,KAAK,GAAG;AAAE,SAAO,IAAE,OAAO,KAAK,CAAC,EAAE,WAAS,EAAE,QAAM,IAAG,OAAK,MAAI,EAAE,QAAM,IAAG,OAAO,OAAO,GAAE,eAAe,CAAC,GAAE,CAAC,CAAC,GAAE,OAAO,EAAE,WAAU,EAAC,OAAM,IAAE,YAAY,CAAC,GAAE,OAAM,EAAC;AAAC;AAAC,SAAS,aAAa,GAAE;AAAC,MAAI,IAAE,EAAE;AAAI,MAAG;AAAE,WAAM,aAAW,QAAQ,CAAC,IAAE,SAASL,IAAE;AAAC,QAAE,IAAI,UAAQA;AAAA,IAAC,IAAE,cAAY,OAAO,IAAE,IAAE;AAAM;AAAC,IAAI,cAAY,CAAC,SAAQ,SAAQ,UAAU;AAAE,SAAS,kBAAkB,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,MAAI,GAAE,GAAE,GAAE;AAAE,SAAM,UAAQ,KAAG,aAAa,UAAQ,IAAE,IAAE,CAAC,CAAC,IAAE,CAAC,IAAG,EAAE,SAAO,wBAAS,EAAE,SAAO,IAAE,EAAE,SAAO,SAAO,EAAE,UAAS,CAAC,IAAE,YAAU,OAAO,EAAE,SAAO,UAAQ,KAAG,KAAG,EAAE,QAAQ,EAAE,IAAI,MAAI,IAAE,aAAa,CAAC,GAAE,KAAG,IAAE,EAAE,SAAO,CAAC,GAAG,OAAM,IAAE,EAAE,OAAM,IAAE,EAAE,UAAS,IAAE,yBAAyB,GAAE,WAAW,GAAE,IAAE,MAAM,KAAK,IAAI,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,GAAE,IAAE,YAAY,CAAC,GAAE,IAAE,eAAe,eAAe,eAAe,eAAe,CAAC,GAAE,CAAC,GAAE,MAAI,OAAO,KAAK,CAAC,EAAE,SAAO,CAAC,IAAE,EAAC,OAAM,EAAC,CAAC,GAAE,IAAE,EAAC,WAAU,EAAC,IAAE,CAAC,CAAC,GAAE,IAAE,EAAC,KAAI,EAAC,IAAE,CAAC,CAAC,GAAE,MAAI,OAAO,KAAK,CAAC,EAAE,WAAS,IAAE,QAAO,IAAE,QAAM,IAAE,KAAG,CAAC,UAAS,QAAQ,EAAE,QAAQ,QAAQ,CAAC,CAAC,IAAE,CAAC,CAAC,IAAE,aAAa,QAAM,mBAAmB,CAAC,IAAE,eAAe,CAAC,GAAE,CAAC,GAAG,UAAQ,IAAG,EAAE,EAAE,MAAK,GAAE,EAAE,GAAE,CAAC,CAAC,KAAG,EAAE,CAAC,CAAC,GAAE,MAAK,CAAC;AAAC;AAAC,SAAS,2BAA2B,GAAE;AAAC,MAAI,IAAE,EAAE,eAAc,IAAE,EAAE,SAAQ,IAAE,EAAE,UAAS,IAAE,WAAS,KAAG;AAAE,SAAO,SAAS,EAAE,GAAE,GAAE;AAAC,QAAG,QAAM;AAAE,aAAO;AAAE,iBAAa,UAAQ,IAAE,CAAC,CAAC;AAAG,QAAI,IAAE,CAAC;AAAE,WAAO,EAAE,QAAQ,SAASA,IAAE,GAAE;AAAC,WAAI,SAAO,IAAEA,GAAE,SAAO,CAAC,EAAE,uBAAqBA,GAAE,SAAO;AAAa,eAAOA,GAAE,eAAa,YAAU,OAAOA,MAAG,YAAU,OAAOA,KAAE,KAAK,EAAE,KAAKA,EAAC,IAAE,MAAKA,GAAE,SAAO,IAAE,kBAAkBA,IAAE,GAAE,GAAE,GAAE,GAAE,EAAE,OAAO,GAAE,EAAE,KAAK,CAAC;AAAI,UAAI,IAAEA,GAAE,KAAK;AAAmB,UAAGA,GAAE,SAAO,cAAa;AAAC,YAAG,CAACA,GAAE,MAAM;AAAU,iBAAO,KAAK,EAAE,KAAKA,GAAE,MAAM,IAAI;AAAE,YAAEA,GAAE,MAAM,WAAUA,KAAE,eAAe,CAAC,GAAEA,EAAC;AAAE,YAAI,IAAE,eAAe,CAAC,GAAEA,GAAE,KAAK;AAAE,eAAO,EAAE,WAAUA,GAAE,QAAM;AAAA,MAAC;AAAC,UAAE,WAAS,IAAE,UAAQ,KAAG,aAAa,QAAM,IAAE,CAAC,CAAC,MAAI,KAAG,EAAE,QAAQ,CAAC,MAAIA,KAAE,eAAe,CAAC,GAAEA,EAAC,GAAG,UAAQ,EAAE,SAAQ,KAAG,IAAE,eAAeA,IAAE,QAAQ,OAAO,CAAC,GAAE,GAAE,CAAC,GAAG,OAAM,IAAE,EAAE,OAAM,aAAaA,EAAC,GAAEA,GAAE,aAAWA,GAAE,SAAS,UAAQ,EAAE,UAAS,EAAE,GAAE,eAAe,CAAC,GAAE,CAAC,GAAE,CAAC,KAAG,kBAAkBA,IAAE,GAAE,GAAE,GAAE,CAAC,GAAE,EAAE,KAAK,CAAC;AAAA,IAAC,CAAC,GAAE,OAAK,IAAE,EAAE,KAAK,IAAE,CAAC,GAAG,SAAO,EAAE,CAAC,IAAE;AAAA,EAAC;AAAC;AAAC,IAAI,mBAAiB,2BAA2B,EAAC,eAAc,OAAM,SAAQ,MAAK,CAAC;AAAE,SAAS,oBAAoB,GAAE,GAAE;AAAC,SAAO,cAAc,GAAE,EAAC,gBAAe,eAAe,EAAC,iBAAgB,MAAG,uBAAsB,iBAAgB,GAAE,CAAC,EAAC,CAAC;AAAC;AAAC,IAAI,mBAAiB,2BAA2B,EAAC,iBAAgB,OAAM,SAAQ,MAAK,CAAC;AAAE,SAAS,SAAS,GAAE;AAAC,SAAO,OAAK,KAAG,IAAE,CAAC,IAAE,cAAY,OAAO,IAAE,EAAE,IAAE,CAAC,GAAG,KAAK,IAAE,CAAC,GAAG,WAAS,IAAE,EAAE,CAAC,IAAG,iBAAiB,GAAE,SAASA,IAAE;AAAC,WAAO,EAAE,SAAQ,EAAC,MAAKA,GAAC,CAAC;AAAA,EAAC,CAAC;AAAC;AAAC,SAAS,iBAAiB,GAAE,GAAE;AAAC,WAAS,IAAG;AAAC,WAAO,EAAE,EAAE,KAAK,SAASA,IAAE;AAAC,aAAO,oBAAoBA,GAAE,SAAQ,CAAC;AAAA,IAAC,CAAC;AAAA,EAAC;AAAC,MAAI,IAAE;AAAE,eAAW,QAAQ,CAAC,MAAI,IAAE,EAAE;AAAQ,SAAO,qBAAqB,aAAW,QAAQ,CAAC,IAAE,eAAe,eAAe,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAC,QAAO,EAAC,CAAC,IAAE,CAAC;AAAC;AAAC,SAAS,iBAAiB,GAAE,GAAE;AAAC,aAAO,mBAAK,WAAU;AAAC,WAAO,EAAE,EAAE,KAAK,SAASA,IAAE;AAAC,aAAM,EAAC,SAAQ,oBAAoBA,GAAE,SAAQ,CAAC,EAAC;AAAA,IAAC,CAAC;AAAA,EAAC,CAAC;AAAC;AAAC,IAAI,cAAY,CAAC,UAAU;AAAE,SAAS,sCAAsC,GAAE;AAAC,MAAI,QAAE,4BAAc,CAAC,CAAC;AAAE,SAAM,CAAC,WAAU;AAAC,eAAO,yBAAW,CAAC;AAAA,EAAC,GAAE,oBAAoB,SAASA,IAAE;AAAC,QAAI,IAAEA,GAAE,UAASA,KAAE,yBAAyBA,IAAE,WAAW;AAAE,eAAO,4BAAc,EAAE,UAAS,EAAC,OAAM,eAAe,CAAC,GAAEA,EAAC,EAAC,GAAE,CAAC;AAAA,EAAC,GAAE,EAAC,2BAA0B,EAAC,CAAC,GAAE,CAAC;AAAC;AAAC,IAAI,WAAS,IAAI;AAAS,SAAS,sCAAsC,GAAE,GAAE;AAAC,SAAO,IAAE,KAAG,SAAS,YAAY,wBAAwB,GAAE,CAAC,WAAU;AAAC,WAAO,OAAO,CAAC;AAAA,EAAC,GAAE,oBAAoB,EAAC,OAAM,SAASA,IAAE,GAAE;AAAC,WAAO,QAAQ,GAAE,EAAE,KAAK,GAAE,WAAU;AAAC,aAAO,EAAE,EAAE,MAAM,OAAO;AAAA,IAAC;AAAA,EAAC,EAAC,GAAE,EAAC,2BAA0B,EAAC,CAAC,CAAC;AAAC;", "names": ["e", "t", "r", "h", "ReactDOM", "n", "u", "c", "o", "a", "i", "React__default"]}