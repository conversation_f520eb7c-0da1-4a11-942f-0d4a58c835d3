import {
  forEach
} from "./chunk-VRJCCLJE.js";
import {
  getBusinessObject,
  is
} from "./chunk-7XBT5GDQ.js";

// node_modules/bpmn-js/lib/util/DiUtil.js
function isExpanded(element) {
  if (is(element, "bpmn:CallActivity")) {
    return false;
  }
  if (is(element, "bpmn:SubProcess")) {
    return getBusinessObject(element).di && !!getBusinessObject(element).di.isExpanded;
  }
  if (is(element, "bpmn:Participant")) {
    return !!getBusinessObject(element).processRef;
  }
  return true;
}
function isInterrupting(element) {
  return element && getBusinessObject(element).isInterrupting !== false;
}
function isEventSubProcess(element) {
  return element && !!getBusinessObject(element).triggeredByEvent;
}
function hasEventDefinition(element, eventType) {
  var bo = getBusinessObject(element), hasEventDefinition2 = false;
  if (bo.eventDefinitions) {
    forEach(bo.eventDefinitions, function(event) {
      if (is(event, eventType)) {
        hasEventDefinition2 = true;
      }
    });
  }
  return hasEventDefinition2;
}
function hasErrorEventDefinition(element) {
  return hasEventDefinition(element, "bpmn:ErrorEventDefinition");
}
function hasEscalationEventDefinition(element) {
  return hasEventDefinition(element, "bpmn:EscalationEventDefinition");
}
function hasCompensateEventDefinition(element) {
  return hasEventDefinition(element, "bpmn:CompensateEventDefinition");
}

export {
  isExpanded,
  isInterrupting,
  isEventSubProcess,
  hasEventDefinition,
  hasErrorEventDefinition,
  hasEscalationEventDefinition,
  hasCompensateEventDefinition
};
//# sourceMappingURL=chunk-67BHQHBR.js.map
