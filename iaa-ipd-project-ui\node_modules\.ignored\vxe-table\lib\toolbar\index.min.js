Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=exports.VxeToolbar=exports.Toolbar=void 0;var _ui=require("../ui"),_toolbar=_interopRequireDefault(require("./src/toolbar"));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let VxeToolbar=exports.VxeToolbar=Object.assign({},_toolbar.default,{install(e){e.component(_toolbar.default.name,_toolbar.default)}}),Toolbar=(_ui.VxeUI.dynamicApp&&_ui.VxeUI.dynamicApp.component(_toolbar.default.name,_toolbar.default),_ui.VxeUI.component(_toolbar.default),exports.Toolbar=VxeToolbar);var _default=exports.default=VxeToolbar;