import {
  require_assignValue,
  require_copyObject,
  require_createAssigner
} from "./chunk-LZQZ2OHM.js";
import {
  require_isArrayLike,
  require_isPrototype,
  require_keys
} from "./chunk-ZJNJ4ZV3.js";
import {
  __commonJS
} from "./chunk-GFT2G5UO.js";

// node_modules/lodash/assign.js
var require_assign = __commonJS({
  "node_modules/lodash/assign.js"(exports, module) {
    var assignValue = require_assignValue();
    var copyObject = require_copyObject();
    var createAssigner = require_createAssigner();
    var isArrayLike = require_isArrayLike();
    var isPrototype = require_isPrototype();
    var keys = require_keys();
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    var assign = createAssigner(function(object, source) {
      if (isPrototype(source) || isArrayLike(source)) {
        copyObject(source, keys(source), object);
        return;
      }
      for (var key in source) {
        if (hasOwnProperty.call(source, key)) {
          assignValue(object, key, source[key]);
        }
      }
    });
    module.exports = assign;
  }
});

export {
  require_assign
};
//# sourceMappingURL=chunk-BEETIK3M.js.map
