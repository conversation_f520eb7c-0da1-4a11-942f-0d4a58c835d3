{"version": 3, "sources": ["../../cropperjs/dist/cropper.esm.js"], "sourcesContent": ["/*!\n * Cropper.js v1.6.2\n * https://fengyuanchen.github.io/cropperjs\n *\n * Copyright 2015-present <PERSON>\n * Released under the MIT license\n *\n * Date: 2024-04-21T07:43:05.335Z\n */\n\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar IS_BROWSER = typeof window !== 'undefined' && typeof window.document !== 'undefined';\nvar WINDOW = IS_BROWSER ? window : {};\nvar IS_TOUCH_DEVICE = IS_BROWSER && WINDOW.document.documentElement ? 'ontouchstart' in WINDOW.document.documentElement : false;\nvar HAS_POINTER_EVENT = IS_BROWSER ? 'PointerEvent' in WINDOW : false;\nvar NAMESPACE = 'cropper';\n\n// Actions\nvar ACTION_ALL = 'all';\nvar ACTION_CROP = 'crop';\nvar ACTION_MOVE = 'move';\nvar ACTION_ZOOM = 'zoom';\nvar ACTION_EAST = 'e';\nvar ACTION_WEST = 'w';\nvar ACTION_SOUTH = 's';\nvar ACTION_NORTH = 'n';\nvar ACTION_NORTH_EAST = 'ne';\nvar ACTION_NORTH_WEST = 'nw';\nvar ACTION_SOUTH_EAST = 'se';\nvar ACTION_SOUTH_WEST = 'sw';\n\n// Classes\nvar CLASS_CROP = \"\".concat(NAMESPACE, \"-crop\");\nvar CLASS_DISABLED = \"\".concat(NAMESPACE, \"-disabled\");\nvar CLASS_HIDDEN = \"\".concat(NAMESPACE, \"-hidden\");\nvar CLASS_HIDE = \"\".concat(NAMESPACE, \"-hide\");\nvar CLASS_INVISIBLE = \"\".concat(NAMESPACE, \"-invisible\");\nvar CLASS_MODAL = \"\".concat(NAMESPACE, \"-modal\");\nvar CLASS_MOVE = \"\".concat(NAMESPACE, \"-move\");\n\n// Data keys\nvar DATA_ACTION = \"\".concat(NAMESPACE, \"Action\");\nvar DATA_PREVIEW = \"\".concat(NAMESPACE, \"Preview\");\n\n// Drag modes\nvar DRAG_MODE_CROP = 'crop';\nvar DRAG_MODE_MOVE = 'move';\nvar DRAG_MODE_NONE = 'none';\n\n// Events\nvar EVENT_CROP = 'crop';\nvar EVENT_CROP_END = 'cropend';\nvar EVENT_CROP_MOVE = 'cropmove';\nvar EVENT_CROP_START = 'cropstart';\nvar EVENT_DBLCLICK = 'dblclick';\nvar EVENT_TOUCH_START = IS_TOUCH_DEVICE ? 'touchstart' : 'mousedown';\nvar EVENT_TOUCH_MOVE = IS_TOUCH_DEVICE ? 'touchmove' : 'mousemove';\nvar EVENT_TOUCH_END = IS_TOUCH_DEVICE ? 'touchend touchcancel' : 'mouseup';\nvar EVENT_POINTER_DOWN = HAS_POINTER_EVENT ? 'pointerdown' : EVENT_TOUCH_START;\nvar EVENT_POINTER_MOVE = HAS_POINTER_EVENT ? 'pointermove' : EVENT_TOUCH_MOVE;\nvar EVENT_POINTER_UP = HAS_POINTER_EVENT ? 'pointerup pointercancel' : EVENT_TOUCH_END;\nvar EVENT_READY = 'ready';\nvar EVENT_RESIZE = 'resize';\nvar EVENT_WHEEL = 'wheel';\nvar EVENT_ZOOM = 'zoom';\n\n// Mime types\nvar MIME_TYPE_JPEG = 'image/jpeg';\n\n// RegExps\nvar REGEXP_ACTIONS = /^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/;\nvar REGEXP_DATA_URL = /^data:/;\nvar REGEXP_DATA_URL_JPEG = /^data:image\\/jpeg;base64,/;\nvar REGEXP_TAG_NAME = /^img|canvas$/i;\n\n// Misc\n// Inspired by the default width and height of a canvas element.\nvar MIN_CONTAINER_WIDTH = 200;\nvar MIN_CONTAINER_HEIGHT = 100;\n\nvar DEFAULTS = {\n  // Define the view mode of the cropper\n  viewMode: 0,\n  // 0, 1, 2, 3\n\n  // Define the dragging mode of the cropper\n  dragMode: DRAG_MODE_CROP,\n  // 'crop', 'move' or 'none'\n\n  // Define the initial aspect ratio of the crop box\n  initialAspectRatio: NaN,\n  // Define the aspect ratio of the crop box\n  aspectRatio: NaN,\n  // An object with the previous cropping result data\n  data: null,\n  // A selector for adding extra containers to preview\n  preview: '',\n  // Re-render the cropper when resize the window\n  responsive: true,\n  // Restore the cropped area after resize the window\n  restore: true,\n  // Check if the current image is a cross-origin image\n  checkCrossOrigin: true,\n  // Check the current image's Exif Orientation information\n  checkOrientation: true,\n  // Show the black modal\n  modal: true,\n  // Show the dashed lines for guiding\n  guides: true,\n  // Show the center indicator for guiding\n  center: true,\n  // Show the white modal to highlight the crop box\n  highlight: true,\n  // Show the grid background\n  background: true,\n  // Enable to crop the image automatically when initialize\n  autoCrop: true,\n  // Define the percentage of automatic cropping area when initializes\n  autoCropArea: 0.8,\n  // Enable to move the image\n  movable: true,\n  // Enable to rotate the image\n  rotatable: true,\n  // Enable to scale the image\n  scalable: true,\n  // Enable to zoom the image\n  zoomable: true,\n  // Enable to zoom the image by dragging touch\n  zoomOnTouch: true,\n  // Enable to zoom the image by wheeling mouse\n  zoomOnWheel: true,\n  // Define zoom ratio when zoom the image by wheeling mouse\n  wheelZoomRatio: 0.1,\n  // Enable to move the crop box\n  cropBoxMovable: true,\n  // Enable to resize the crop box\n  cropBoxResizable: true,\n  // Toggle drag mode between \"crop\" and \"move\" when click twice on the cropper\n  toggleDragModeOnDblclick: true,\n  // Size limitation\n  minCanvasWidth: 0,\n  minCanvasHeight: 0,\n  minCropBoxWidth: 0,\n  minCropBoxHeight: 0,\n  minContainerWidth: MIN_CONTAINER_WIDTH,\n  minContainerHeight: MIN_CONTAINER_HEIGHT,\n  // Shortcuts of events\n  ready: null,\n  cropstart: null,\n  cropmove: null,\n  cropend: null,\n  crop: null,\n  zoom: null\n};\n\nvar TEMPLATE = '<div class=\"cropper-container\" touch-action=\"none\">' + '<div class=\"cropper-wrap-box\">' + '<div class=\"cropper-canvas\"></div>' + '</div>' + '<div class=\"cropper-drag-box\"></div>' + '<div class=\"cropper-crop-box\">' + '<span class=\"cropper-view-box\"></span>' + '<span class=\"cropper-dashed dashed-h\"></span>' + '<span class=\"cropper-dashed dashed-v\"></span>' + '<span class=\"cropper-center\"></span>' + '<span class=\"cropper-face\"></span>' + '<span class=\"cropper-line line-e\" data-cropper-action=\"e\"></span>' + '<span class=\"cropper-line line-n\" data-cropper-action=\"n\"></span>' + '<span class=\"cropper-line line-w\" data-cropper-action=\"w\"></span>' + '<span class=\"cropper-line line-s\" data-cropper-action=\"s\"></span>' + '<span class=\"cropper-point point-e\" data-cropper-action=\"e\"></span>' + '<span class=\"cropper-point point-n\" data-cropper-action=\"n\"></span>' + '<span class=\"cropper-point point-w\" data-cropper-action=\"w\"></span>' + '<span class=\"cropper-point point-s\" data-cropper-action=\"s\"></span>' + '<span class=\"cropper-point point-ne\" data-cropper-action=\"ne\"></span>' + '<span class=\"cropper-point point-nw\" data-cropper-action=\"nw\"></span>' + '<span class=\"cropper-point point-sw\" data-cropper-action=\"sw\"></span>' + '<span class=\"cropper-point point-se\" data-cropper-action=\"se\"></span>' + '</div>' + '</div>';\n\n/**\n * Check if the given value is not a number.\n */\nvar isNaN = Number.isNaN || WINDOW.isNaN;\n\n/**\n * Check if the given value is a number.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a number, else `false`.\n */\nfunction isNumber(value) {\n  return typeof value === 'number' && !isNaN(value);\n}\n\n/**\n * Check if the given value is a positive number.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a positive number, else `false`.\n */\nvar isPositiveNumber = function isPositiveNumber(value) {\n  return value > 0 && value < Infinity;\n};\n\n/**\n * Check if the given value is undefined.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is undefined, else `false`.\n */\nfunction isUndefined(value) {\n  return typeof value === 'undefined';\n}\n\n/**\n * Check if the given value is an object.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is an object, else `false`.\n */\nfunction isObject(value) {\n  return _typeof(value) === 'object' && value !== null;\n}\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\n/**\n * Check if the given value is a plain object.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a plain object, else `false`.\n */\nfunction isPlainObject(value) {\n  if (!isObject(value)) {\n    return false;\n  }\n  try {\n    var _constructor = value.constructor;\n    var prototype = _constructor.prototype;\n    return _constructor && prototype && hasOwnProperty.call(prototype, 'isPrototypeOf');\n  } catch (error) {\n    return false;\n  }\n}\n\n/**\n * Check if the given value is a function.\n * @param {*} value - The value to check.\n * @returns {boolean} Returns `true` if the given value is a function, else `false`.\n */\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\nvar slice = Array.prototype.slice;\n\n/**\n * Convert array-like or iterable object to an array.\n * @param {*} value - The value to convert.\n * @returns {Array} Returns a new array.\n */\nfunction toArray(value) {\n  return Array.from ? Array.from(value) : slice.call(value);\n}\n\n/**\n * Iterate the given data.\n * @param {*} data - The data to iterate.\n * @param {Function} callback - The process function for each element.\n * @returns {*} The original data.\n */\nfunction forEach(data, callback) {\n  if (data && isFunction(callback)) {\n    if (Array.isArray(data) || isNumber(data.length) /* array-like */) {\n      toArray(data).forEach(function (value, key) {\n        callback.call(data, value, key, data);\n      });\n    } else if (isObject(data)) {\n      Object.keys(data).forEach(function (key) {\n        callback.call(data, data[key], key, data);\n      });\n    }\n  }\n  return data;\n}\n\n/**\n * Extend the given object.\n * @param {*} target - The target object to extend.\n * @param {*} args - The rest objects for merging to the target object.\n * @returns {Object} The extended object.\n */\nvar assign = Object.assign || function assign(target) {\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  if (isObject(target) && args.length > 0) {\n    args.forEach(function (arg) {\n      if (isObject(arg)) {\n        Object.keys(arg).forEach(function (key) {\n          target[key] = arg[key];\n        });\n      }\n    });\n  }\n  return target;\n};\nvar REGEXP_DECIMALS = /\\.\\d*(?:0|9){12}\\d*$/;\n\n/**\n * Normalize decimal number.\n * Check out {@link https://0.30000000000000004.com/}\n * @param {number} value - The value to normalize.\n * @param {number} [times=100000000000] - The times for normalizing.\n * @returns {number} Returns the normalized number.\n */\nfunction normalizeDecimalNumber(value) {\n  var times = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 100000000000;\n  return REGEXP_DECIMALS.test(value) ? Math.round(value * times) / times : value;\n}\nvar REGEXP_SUFFIX = /^width|height|left|top|marginLeft|marginTop$/;\n\n/**\n * Apply styles to the given element.\n * @param {Element} element - The target element.\n * @param {Object} styles - The styles for applying.\n */\nfunction setStyle(element, styles) {\n  var style = element.style;\n  forEach(styles, function (value, property) {\n    if (REGEXP_SUFFIX.test(property) && isNumber(value)) {\n      value = \"\".concat(value, \"px\");\n    }\n    style[property] = value;\n  });\n}\n\n/**\n * Check if the given element has a special class.\n * @param {Element} element - The element to check.\n * @param {string} value - The class to search.\n * @returns {boolean} Returns `true` if the special class was found.\n */\nfunction hasClass(element, value) {\n  return element.classList ? element.classList.contains(value) : element.className.indexOf(value) > -1;\n}\n\n/**\n * Add classes to the given element.\n * @param {Element} element - The target element.\n * @param {string} value - The classes to be added.\n */\nfunction addClass(element, value) {\n  if (!value) {\n    return;\n  }\n  if (isNumber(element.length)) {\n    forEach(element, function (elem) {\n      addClass(elem, value);\n    });\n    return;\n  }\n  if (element.classList) {\n    element.classList.add(value);\n    return;\n  }\n  var className = element.className.trim();\n  if (!className) {\n    element.className = value;\n  } else if (className.indexOf(value) < 0) {\n    element.className = \"\".concat(className, \" \").concat(value);\n  }\n}\n\n/**\n * Remove classes from the given element.\n * @param {Element} element - The target element.\n * @param {string} value - The classes to be removed.\n */\nfunction removeClass(element, value) {\n  if (!value) {\n    return;\n  }\n  if (isNumber(element.length)) {\n    forEach(element, function (elem) {\n      removeClass(elem, value);\n    });\n    return;\n  }\n  if (element.classList) {\n    element.classList.remove(value);\n    return;\n  }\n  if (element.className.indexOf(value) >= 0) {\n    element.className = element.className.replace(value, '');\n  }\n}\n\n/**\n * Add or remove classes from the given element.\n * @param {Element} element - The target element.\n * @param {string} value - The classes to be toggled.\n * @param {boolean} added - Add only.\n */\nfunction toggleClass(element, value, added) {\n  if (!value) {\n    return;\n  }\n  if (isNumber(element.length)) {\n    forEach(element, function (elem) {\n      toggleClass(elem, value, added);\n    });\n    return;\n  }\n\n  // IE10-11 doesn't support the second parameter of `classList.toggle`\n  if (added) {\n    addClass(element, value);\n  } else {\n    removeClass(element, value);\n  }\n}\nvar REGEXP_CAMEL_CASE = /([a-z\\d])([A-Z])/g;\n\n/**\n * Transform the given string from camelCase to kebab-case\n * @param {string} value - The value to transform.\n * @returns {string} The transformed value.\n */\nfunction toParamCase(value) {\n  return value.replace(REGEXP_CAMEL_CASE, '$1-$2').toLowerCase();\n}\n\n/**\n * Get data from the given element.\n * @param {Element} element - The target element.\n * @param {string} name - The data key to get.\n * @returns {string} The data value.\n */\nfunction getData(element, name) {\n  if (isObject(element[name])) {\n    return element[name];\n  }\n  if (element.dataset) {\n    return element.dataset[name];\n  }\n  return element.getAttribute(\"data-\".concat(toParamCase(name)));\n}\n\n/**\n * Set data to the given element.\n * @param {Element} element - The target element.\n * @param {string} name - The data key to set.\n * @param {string} data - The data value.\n */\nfunction setData(element, name, data) {\n  if (isObject(data)) {\n    element[name] = data;\n  } else if (element.dataset) {\n    element.dataset[name] = data;\n  } else {\n    element.setAttribute(\"data-\".concat(toParamCase(name)), data);\n  }\n}\n\n/**\n * Remove data from the given element.\n * @param {Element} element - The target element.\n * @param {string} name - The data key to remove.\n */\nfunction removeData(element, name) {\n  if (isObject(element[name])) {\n    try {\n      delete element[name];\n    } catch (error) {\n      element[name] = undefined;\n    }\n  } else if (element.dataset) {\n    // #128 Safari not allows to delete dataset property\n    try {\n      delete element.dataset[name];\n    } catch (error) {\n      element.dataset[name] = undefined;\n    }\n  } else {\n    element.removeAttribute(\"data-\".concat(toParamCase(name)));\n  }\n}\nvar REGEXP_SPACES = /\\s\\s*/;\nvar onceSupported = function () {\n  var supported = false;\n  if (IS_BROWSER) {\n    var once = false;\n    var listener = function listener() {};\n    var options = Object.defineProperty({}, 'once', {\n      get: function get() {\n        supported = true;\n        return once;\n      },\n      /**\n       * This setter can fix a `TypeError` in strict mode\n       * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Errors/Getter_only}\n       * @param {boolean} value - The value to set\n       */\n      set: function set(value) {\n        once = value;\n      }\n    });\n    WINDOW.addEventListener('test', listener, options);\n    WINDOW.removeEventListener('test', listener, options);\n  }\n  return supported;\n}();\n\n/**\n * Remove event listener from the target element.\n * @param {Element} element - The event target.\n * @param {string} type - The event type(s).\n * @param {Function} listener - The event listener.\n * @param {Object} options - The event options.\n */\nfunction removeListener(element, type, listener) {\n  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  var handler = listener;\n  type.trim().split(REGEXP_SPACES).forEach(function (event) {\n    if (!onceSupported) {\n      var listeners = element.listeners;\n      if (listeners && listeners[event] && listeners[event][listener]) {\n        handler = listeners[event][listener];\n        delete listeners[event][listener];\n        if (Object.keys(listeners[event]).length === 0) {\n          delete listeners[event];\n        }\n        if (Object.keys(listeners).length === 0) {\n          delete element.listeners;\n        }\n      }\n    }\n    element.removeEventListener(event, handler, options);\n  });\n}\n\n/**\n * Add event listener to the target element.\n * @param {Element} element - The event target.\n * @param {string} type - The event type(s).\n * @param {Function} listener - The event listener.\n * @param {Object} options - The event options.\n */\nfunction addListener(element, type, listener) {\n  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  var _handler = listener;\n  type.trim().split(REGEXP_SPACES).forEach(function (event) {\n    if (options.once && !onceSupported) {\n      var _element$listeners = element.listeners,\n        listeners = _element$listeners === void 0 ? {} : _element$listeners;\n      _handler = function handler() {\n        delete listeners[event][listener];\n        element.removeEventListener(event, _handler, options);\n        for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n          args[_key2] = arguments[_key2];\n        }\n        listener.apply(element, args);\n      };\n      if (!listeners[event]) {\n        listeners[event] = {};\n      }\n      if (listeners[event][listener]) {\n        element.removeEventListener(event, listeners[event][listener], options);\n      }\n      listeners[event][listener] = _handler;\n      element.listeners = listeners;\n    }\n    element.addEventListener(event, _handler, options);\n  });\n}\n\n/**\n * Dispatch event on the target element.\n * @param {Element} element - The event target.\n * @param {string} type - The event type(s).\n * @param {Object} data - The additional event data.\n * @returns {boolean} Indicate if the event is default prevented or not.\n */\nfunction dispatchEvent(element, type, data) {\n  var event;\n\n  // Event and CustomEvent on IE9-11 are global objects, not constructors\n  if (isFunction(Event) && isFunction(CustomEvent)) {\n    event = new CustomEvent(type, {\n      detail: data,\n      bubbles: true,\n      cancelable: true\n    });\n  } else {\n    event = document.createEvent('CustomEvent');\n    event.initCustomEvent(type, true, true, data);\n  }\n  return element.dispatchEvent(event);\n}\n\n/**\n * Get the offset base on the document.\n * @param {Element} element - The target element.\n * @returns {Object} The offset data.\n */\nfunction getOffset(element) {\n  var box = element.getBoundingClientRect();\n  return {\n    left: box.left + (window.pageXOffset - document.documentElement.clientLeft),\n    top: box.top + (window.pageYOffset - document.documentElement.clientTop)\n  };\n}\nvar location = WINDOW.location;\nvar REGEXP_ORIGINS = /^(\\w+:)\\/\\/([^:/?#]*):?(\\d*)/i;\n\n/**\n * Check if the given URL is a cross origin URL.\n * @param {string} url - The target URL.\n * @returns {boolean} Returns `true` if the given URL is a cross origin URL, else `false`.\n */\nfunction isCrossOriginURL(url) {\n  var parts = url.match(REGEXP_ORIGINS);\n  return parts !== null && (parts[1] !== location.protocol || parts[2] !== location.hostname || parts[3] !== location.port);\n}\n\n/**\n * Add timestamp to the given URL.\n * @param {string} url - The target URL.\n * @returns {string} The result URL.\n */\nfunction addTimestamp(url) {\n  var timestamp = \"timestamp=\".concat(new Date().getTime());\n  return url + (url.indexOf('?') === -1 ? '?' : '&') + timestamp;\n}\n\n/**\n * Get transforms base on the given object.\n * @param {Object} obj - The target object.\n * @returns {string} A string contains transform values.\n */\nfunction getTransforms(_ref) {\n  var rotate = _ref.rotate,\n    scaleX = _ref.scaleX,\n    scaleY = _ref.scaleY,\n    translateX = _ref.translateX,\n    translateY = _ref.translateY;\n  var values = [];\n  if (isNumber(translateX) && translateX !== 0) {\n    values.push(\"translateX(\".concat(translateX, \"px)\"));\n  }\n  if (isNumber(translateY) && translateY !== 0) {\n    values.push(\"translateY(\".concat(translateY, \"px)\"));\n  }\n\n  // Rotate should come first before scale to match orientation transform\n  if (isNumber(rotate) && rotate !== 0) {\n    values.push(\"rotate(\".concat(rotate, \"deg)\"));\n  }\n  if (isNumber(scaleX) && scaleX !== 1) {\n    values.push(\"scaleX(\".concat(scaleX, \")\"));\n  }\n  if (isNumber(scaleY) && scaleY !== 1) {\n    values.push(\"scaleY(\".concat(scaleY, \")\"));\n  }\n  var transform = values.length ? values.join(' ') : 'none';\n  return {\n    WebkitTransform: transform,\n    msTransform: transform,\n    transform: transform\n  };\n}\n\n/**\n * Get the max ratio of a group of pointers.\n * @param {string} pointers - The target pointers.\n * @returns {number} The result ratio.\n */\nfunction getMaxZoomRatio(pointers) {\n  var pointers2 = _objectSpread2({}, pointers);\n  var maxRatio = 0;\n  forEach(pointers, function (pointer, pointerId) {\n    delete pointers2[pointerId];\n    forEach(pointers2, function (pointer2) {\n      var x1 = Math.abs(pointer.startX - pointer2.startX);\n      var y1 = Math.abs(pointer.startY - pointer2.startY);\n      var x2 = Math.abs(pointer.endX - pointer2.endX);\n      var y2 = Math.abs(pointer.endY - pointer2.endY);\n      var z1 = Math.sqrt(x1 * x1 + y1 * y1);\n      var z2 = Math.sqrt(x2 * x2 + y2 * y2);\n      var ratio = (z2 - z1) / z1;\n      if (Math.abs(ratio) > Math.abs(maxRatio)) {\n        maxRatio = ratio;\n      }\n    });\n  });\n  return maxRatio;\n}\n\n/**\n * Get a pointer from an event object.\n * @param {Object} event - The target event object.\n * @param {boolean} endOnly - Indicates if only returns the end point coordinate or not.\n * @returns {Object} The result pointer contains start and/or end point coordinates.\n */\nfunction getPointer(_ref2, endOnly) {\n  var pageX = _ref2.pageX,\n    pageY = _ref2.pageY;\n  var end = {\n    endX: pageX,\n    endY: pageY\n  };\n  return endOnly ? end : _objectSpread2({\n    startX: pageX,\n    startY: pageY\n  }, end);\n}\n\n/**\n * Get the center point coordinate of a group of pointers.\n * @param {Object} pointers - The target pointers.\n * @returns {Object} The center point coordinate.\n */\nfunction getPointersCenter(pointers) {\n  var pageX = 0;\n  var pageY = 0;\n  var count = 0;\n  forEach(pointers, function (_ref3) {\n    var startX = _ref3.startX,\n      startY = _ref3.startY;\n    pageX += startX;\n    pageY += startY;\n    count += 1;\n  });\n  pageX /= count;\n  pageY /= count;\n  return {\n    pageX: pageX,\n    pageY: pageY\n  };\n}\n\n/**\n * Get the max sizes in a rectangle under the given aspect ratio.\n * @param {Object} data - The original sizes.\n * @param {string} [type='contain'] - The adjust type.\n * @returns {Object} The result sizes.\n */\nfunction getAdjustedSizes(_ref4) {\n  var aspectRatio = _ref4.aspectRatio,\n    height = _ref4.height,\n    width = _ref4.width;\n  var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'contain';\n  var isValidWidth = isPositiveNumber(width);\n  var isValidHeight = isPositiveNumber(height);\n  if (isValidWidth && isValidHeight) {\n    var adjustedWidth = height * aspectRatio;\n    if (type === 'contain' && adjustedWidth > width || type === 'cover' && adjustedWidth < width) {\n      height = width / aspectRatio;\n    } else {\n      width = height * aspectRatio;\n    }\n  } else if (isValidWidth) {\n    height = width / aspectRatio;\n  } else if (isValidHeight) {\n    width = height * aspectRatio;\n  }\n  return {\n    width: width,\n    height: height\n  };\n}\n\n/**\n * Get the new sizes of a rectangle after rotated.\n * @param {Object} data - The original sizes.\n * @returns {Object} The result sizes.\n */\nfunction getRotatedSizes(_ref5) {\n  var width = _ref5.width,\n    height = _ref5.height,\n    degree = _ref5.degree;\n  degree = Math.abs(degree) % 180;\n  if (degree === 90) {\n    return {\n      width: height,\n      height: width\n    };\n  }\n  var arc = degree % 90 * Math.PI / 180;\n  var sinArc = Math.sin(arc);\n  var cosArc = Math.cos(arc);\n  var newWidth = width * cosArc + height * sinArc;\n  var newHeight = width * sinArc + height * cosArc;\n  return degree > 90 ? {\n    width: newHeight,\n    height: newWidth\n  } : {\n    width: newWidth,\n    height: newHeight\n  };\n}\n\n/**\n * Get a canvas which drew the given image.\n * @param {HTMLImageElement} image - The image for drawing.\n * @param {Object} imageData - The image data.\n * @param {Object} canvasData - The canvas data.\n * @param {Object} options - The options.\n * @returns {HTMLCanvasElement} The result canvas.\n */\nfunction getSourceCanvas(image, _ref6, _ref7, _ref8) {\n  var imageAspectRatio = _ref6.aspectRatio,\n    imageNaturalWidth = _ref6.naturalWidth,\n    imageNaturalHeight = _ref6.naturalHeight,\n    _ref6$rotate = _ref6.rotate,\n    rotate = _ref6$rotate === void 0 ? 0 : _ref6$rotate,\n    _ref6$scaleX = _ref6.scaleX,\n    scaleX = _ref6$scaleX === void 0 ? 1 : _ref6$scaleX,\n    _ref6$scaleY = _ref6.scaleY,\n    scaleY = _ref6$scaleY === void 0 ? 1 : _ref6$scaleY;\n  var aspectRatio = _ref7.aspectRatio,\n    naturalWidth = _ref7.naturalWidth,\n    naturalHeight = _ref7.naturalHeight;\n  var _ref8$fillColor = _ref8.fillColor,\n    fillColor = _ref8$fillColor === void 0 ? 'transparent' : _ref8$fillColor,\n    _ref8$imageSmoothingE = _ref8.imageSmoothingEnabled,\n    imageSmoothingEnabled = _ref8$imageSmoothingE === void 0 ? true : _ref8$imageSmoothingE,\n    _ref8$imageSmoothingQ = _ref8.imageSmoothingQuality,\n    imageSmoothingQuality = _ref8$imageSmoothingQ === void 0 ? 'low' : _ref8$imageSmoothingQ,\n    _ref8$maxWidth = _ref8.maxWidth,\n    maxWidth = _ref8$maxWidth === void 0 ? Infinity : _ref8$maxWidth,\n    _ref8$maxHeight = _ref8.maxHeight,\n    maxHeight = _ref8$maxHeight === void 0 ? Infinity : _ref8$maxHeight,\n    _ref8$minWidth = _ref8.minWidth,\n    minWidth = _ref8$minWidth === void 0 ? 0 : _ref8$minWidth,\n    _ref8$minHeight = _ref8.minHeight,\n    minHeight = _ref8$minHeight === void 0 ? 0 : _ref8$minHeight;\n  var canvas = document.createElement('canvas');\n  var context = canvas.getContext('2d');\n  var maxSizes = getAdjustedSizes({\n    aspectRatio: aspectRatio,\n    width: maxWidth,\n    height: maxHeight\n  });\n  var minSizes = getAdjustedSizes({\n    aspectRatio: aspectRatio,\n    width: minWidth,\n    height: minHeight\n  }, 'cover');\n  var width = Math.min(maxSizes.width, Math.max(minSizes.width, naturalWidth));\n  var height = Math.min(maxSizes.height, Math.max(minSizes.height, naturalHeight));\n\n  // Note: should always use image's natural sizes for drawing as\n  // imageData.naturalWidth === canvasData.naturalHeight when rotate % 180 === 90\n  var destMaxSizes = getAdjustedSizes({\n    aspectRatio: imageAspectRatio,\n    width: maxWidth,\n    height: maxHeight\n  });\n  var destMinSizes = getAdjustedSizes({\n    aspectRatio: imageAspectRatio,\n    width: minWidth,\n    height: minHeight\n  }, 'cover');\n  var destWidth = Math.min(destMaxSizes.width, Math.max(destMinSizes.width, imageNaturalWidth));\n  var destHeight = Math.min(destMaxSizes.height, Math.max(destMinSizes.height, imageNaturalHeight));\n  var params = [-destWidth / 2, -destHeight / 2, destWidth, destHeight];\n  canvas.width = normalizeDecimalNumber(width);\n  canvas.height = normalizeDecimalNumber(height);\n  context.fillStyle = fillColor;\n  context.fillRect(0, 0, width, height);\n  context.save();\n  context.translate(width / 2, height / 2);\n  context.rotate(rotate * Math.PI / 180);\n  context.scale(scaleX, scaleY);\n  context.imageSmoothingEnabled = imageSmoothingEnabled;\n  context.imageSmoothingQuality = imageSmoothingQuality;\n  context.drawImage.apply(context, [image].concat(_toConsumableArray(params.map(function (param) {\n    return Math.floor(normalizeDecimalNumber(param));\n  }))));\n  context.restore();\n  return canvas;\n}\nvar fromCharCode = String.fromCharCode;\n\n/**\n * Get string from char code in data view.\n * @param {DataView} dataView - The data view for read.\n * @param {number} start - The start index.\n * @param {number} length - The read length.\n * @returns {string} The read result.\n */\nfunction getStringFromCharCode(dataView, start, length) {\n  var str = '';\n  length += start;\n  for (var i = start; i < length; i += 1) {\n    str += fromCharCode(dataView.getUint8(i));\n  }\n  return str;\n}\nvar REGEXP_DATA_URL_HEAD = /^data:.*,/;\n\n/**\n * Transform Data URL to array buffer.\n * @param {string} dataURL - The Data URL to transform.\n * @returns {ArrayBuffer} The result array buffer.\n */\nfunction dataURLToArrayBuffer(dataURL) {\n  var base64 = dataURL.replace(REGEXP_DATA_URL_HEAD, '');\n  var binary = atob(base64);\n  var arrayBuffer = new ArrayBuffer(binary.length);\n  var uint8 = new Uint8Array(arrayBuffer);\n  forEach(uint8, function (value, i) {\n    uint8[i] = binary.charCodeAt(i);\n  });\n  return arrayBuffer;\n}\n\n/**\n * Transform array buffer to Data URL.\n * @param {ArrayBuffer} arrayBuffer - The array buffer to transform.\n * @param {string} mimeType - The mime type of the Data URL.\n * @returns {string} The result Data URL.\n */\nfunction arrayBufferToDataURL(arrayBuffer, mimeType) {\n  var chunks = [];\n\n  // Chunk Typed Array for better performance (#435)\n  var chunkSize = 8192;\n  var uint8 = new Uint8Array(arrayBuffer);\n  while (uint8.length > 0) {\n    // XXX: Babel's `toConsumableArray` helper will throw error in IE or Safari 9\n    // eslint-disable-next-line prefer-spread\n    chunks.push(fromCharCode.apply(null, toArray(uint8.subarray(0, chunkSize))));\n    uint8 = uint8.subarray(chunkSize);\n  }\n  return \"data:\".concat(mimeType, \";base64,\").concat(btoa(chunks.join('')));\n}\n\n/**\n * Get orientation value from given array buffer.\n * @param {ArrayBuffer} arrayBuffer - The array buffer to read.\n * @returns {number} The read orientation value.\n */\nfunction resetAndGetOrientation(arrayBuffer) {\n  var dataView = new DataView(arrayBuffer);\n  var orientation;\n\n  // Ignores range error when the image does not have correct Exif information\n  try {\n    var littleEndian;\n    var app1Start;\n    var ifdStart;\n\n    // Only handle JPEG image (start by 0xFFD8)\n    if (dataView.getUint8(0) === 0xFF && dataView.getUint8(1) === 0xD8) {\n      var length = dataView.byteLength;\n      var offset = 2;\n      while (offset + 1 < length) {\n        if (dataView.getUint8(offset) === 0xFF && dataView.getUint8(offset + 1) === 0xE1) {\n          app1Start = offset;\n          break;\n        }\n        offset += 1;\n      }\n    }\n    if (app1Start) {\n      var exifIDCode = app1Start + 4;\n      var tiffOffset = app1Start + 10;\n      if (getStringFromCharCode(dataView, exifIDCode, 4) === 'Exif') {\n        var endianness = dataView.getUint16(tiffOffset);\n        littleEndian = endianness === 0x4949;\n        if (littleEndian || endianness === 0x4D4D /* bigEndian */) {\n          if (dataView.getUint16(tiffOffset + 2, littleEndian) === 0x002A) {\n            var firstIFDOffset = dataView.getUint32(tiffOffset + 4, littleEndian);\n            if (firstIFDOffset >= 0x00000008) {\n              ifdStart = tiffOffset + firstIFDOffset;\n            }\n          }\n        }\n      }\n    }\n    if (ifdStart) {\n      var _length = dataView.getUint16(ifdStart, littleEndian);\n      var _offset;\n      var i;\n      for (i = 0; i < _length; i += 1) {\n        _offset = ifdStart + i * 12 + 2;\n        if (dataView.getUint16(_offset, littleEndian) === 0x0112 /* Orientation */) {\n          // 8 is the offset of the current tag's value\n          _offset += 8;\n\n          // Get the original orientation value\n          orientation = dataView.getUint16(_offset, littleEndian);\n\n          // Override the orientation with its default value\n          dataView.setUint16(_offset, 1, littleEndian);\n          break;\n        }\n      }\n    }\n  } catch (error) {\n    orientation = 1;\n  }\n  return orientation;\n}\n\n/**\n * Parse Exif Orientation value.\n * @param {number} orientation - The orientation to parse.\n * @returns {Object} The parsed result.\n */\nfunction parseOrientation(orientation) {\n  var rotate = 0;\n  var scaleX = 1;\n  var scaleY = 1;\n  switch (orientation) {\n    // Flip horizontal\n    case 2:\n      scaleX = -1;\n      break;\n\n    // Rotate left 180°\n    case 3:\n      rotate = -180;\n      break;\n\n    // Flip vertical\n    case 4:\n      scaleY = -1;\n      break;\n\n    // Flip vertical and rotate right 90°\n    case 5:\n      rotate = 90;\n      scaleY = -1;\n      break;\n\n    // Rotate right 90°\n    case 6:\n      rotate = 90;\n      break;\n\n    // Flip horizontal and rotate right 90°\n    case 7:\n      rotate = 90;\n      scaleX = -1;\n      break;\n\n    // Rotate left 90°\n    case 8:\n      rotate = -90;\n      break;\n  }\n  return {\n    rotate: rotate,\n    scaleX: scaleX,\n    scaleY: scaleY\n  };\n}\n\nvar render = {\n  render: function render() {\n    this.initContainer();\n    this.initCanvas();\n    this.initCropBox();\n    this.renderCanvas();\n    if (this.cropped) {\n      this.renderCropBox();\n    }\n  },\n  initContainer: function initContainer() {\n    var element = this.element,\n      options = this.options,\n      container = this.container,\n      cropper = this.cropper;\n    var minWidth = Number(options.minContainerWidth);\n    var minHeight = Number(options.minContainerHeight);\n    addClass(cropper, CLASS_HIDDEN);\n    removeClass(element, CLASS_HIDDEN);\n    var containerData = {\n      width: Math.max(container.offsetWidth, minWidth >= 0 ? minWidth : MIN_CONTAINER_WIDTH),\n      height: Math.max(container.offsetHeight, minHeight >= 0 ? minHeight : MIN_CONTAINER_HEIGHT)\n    };\n    this.containerData = containerData;\n    setStyle(cropper, {\n      width: containerData.width,\n      height: containerData.height\n    });\n    addClass(element, CLASS_HIDDEN);\n    removeClass(cropper, CLASS_HIDDEN);\n  },\n  // Canvas (image wrapper)\n  initCanvas: function initCanvas() {\n    var containerData = this.containerData,\n      imageData = this.imageData;\n    var viewMode = this.options.viewMode;\n    var rotated = Math.abs(imageData.rotate) % 180 === 90;\n    var naturalWidth = rotated ? imageData.naturalHeight : imageData.naturalWidth;\n    var naturalHeight = rotated ? imageData.naturalWidth : imageData.naturalHeight;\n    var aspectRatio = naturalWidth / naturalHeight;\n    var canvasWidth = containerData.width;\n    var canvasHeight = containerData.height;\n    if (containerData.height * aspectRatio > containerData.width) {\n      if (viewMode === 3) {\n        canvasWidth = containerData.height * aspectRatio;\n      } else {\n        canvasHeight = containerData.width / aspectRatio;\n      }\n    } else if (viewMode === 3) {\n      canvasHeight = containerData.width / aspectRatio;\n    } else {\n      canvasWidth = containerData.height * aspectRatio;\n    }\n    var canvasData = {\n      aspectRatio: aspectRatio,\n      naturalWidth: naturalWidth,\n      naturalHeight: naturalHeight,\n      width: canvasWidth,\n      height: canvasHeight\n    };\n    this.canvasData = canvasData;\n    this.limited = viewMode === 1 || viewMode === 2;\n    this.limitCanvas(true, true);\n    canvasData.width = Math.min(Math.max(canvasData.width, canvasData.minWidth), canvasData.maxWidth);\n    canvasData.height = Math.min(Math.max(canvasData.height, canvasData.minHeight), canvasData.maxHeight);\n    canvasData.left = (containerData.width - canvasData.width) / 2;\n    canvasData.top = (containerData.height - canvasData.height) / 2;\n    canvasData.oldLeft = canvasData.left;\n    canvasData.oldTop = canvasData.top;\n    this.initialCanvasData = assign({}, canvasData);\n  },\n  limitCanvas: function limitCanvas(sizeLimited, positionLimited) {\n    var options = this.options,\n      containerData = this.containerData,\n      canvasData = this.canvasData,\n      cropBoxData = this.cropBoxData;\n    var viewMode = options.viewMode;\n    var aspectRatio = canvasData.aspectRatio;\n    var cropped = this.cropped && cropBoxData;\n    if (sizeLimited) {\n      var minCanvasWidth = Number(options.minCanvasWidth) || 0;\n      var minCanvasHeight = Number(options.minCanvasHeight) || 0;\n      if (viewMode > 1) {\n        minCanvasWidth = Math.max(minCanvasWidth, containerData.width);\n        minCanvasHeight = Math.max(minCanvasHeight, containerData.height);\n        if (viewMode === 3) {\n          if (minCanvasHeight * aspectRatio > minCanvasWidth) {\n            minCanvasWidth = minCanvasHeight * aspectRatio;\n          } else {\n            minCanvasHeight = minCanvasWidth / aspectRatio;\n          }\n        }\n      } else if (viewMode > 0) {\n        if (minCanvasWidth) {\n          minCanvasWidth = Math.max(minCanvasWidth, cropped ? cropBoxData.width : 0);\n        } else if (minCanvasHeight) {\n          minCanvasHeight = Math.max(minCanvasHeight, cropped ? cropBoxData.height : 0);\n        } else if (cropped) {\n          minCanvasWidth = cropBoxData.width;\n          minCanvasHeight = cropBoxData.height;\n          if (minCanvasHeight * aspectRatio > minCanvasWidth) {\n            minCanvasWidth = minCanvasHeight * aspectRatio;\n          } else {\n            minCanvasHeight = minCanvasWidth / aspectRatio;\n          }\n        }\n      }\n      var _getAdjustedSizes = getAdjustedSizes({\n        aspectRatio: aspectRatio,\n        width: minCanvasWidth,\n        height: minCanvasHeight\n      });\n      minCanvasWidth = _getAdjustedSizes.width;\n      minCanvasHeight = _getAdjustedSizes.height;\n      canvasData.minWidth = minCanvasWidth;\n      canvasData.minHeight = minCanvasHeight;\n      canvasData.maxWidth = Infinity;\n      canvasData.maxHeight = Infinity;\n    }\n    if (positionLimited) {\n      if (viewMode > (cropped ? 0 : 1)) {\n        var newCanvasLeft = containerData.width - canvasData.width;\n        var newCanvasTop = containerData.height - canvasData.height;\n        canvasData.minLeft = Math.min(0, newCanvasLeft);\n        canvasData.minTop = Math.min(0, newCanvasTop);\n        canvasData.maxLeft = Math.max(0, newCanvasLeft);\n        canvasData.maxTop = Math.max(0, newCanvasTop);\n        if (cropped && this.limited) {\n          canvasData.minLeft = Math.min(cropBoxData.left, cropBoxData.left + (cropBoxData.width - canvasData.width));\n          canvasData.minTop = Math.min(cropBoxData.top, cropBoxData.top + (cropBoxData.height - canvasData.height));\n          canvasData.maxLeft = cropBoxData.left;\n          canvasData.maxTop = cropBoxData.top;\n          if (viewMode === 2) {\n            if (canvasData.width >= containerData.width) {\n              canvasData.minLeft = Math.min(0, newCanvasLeft);\n              canvasData.maxLeft = Math.max(0, newCanvasLeft);\n            }\n            if (canvasData.height >= containerData.height) {\n              canvasData.minTop = Math.min(0, newCanvasTop);\n              canvasData.maxTop = Math.max(0, newCanvasTop);\n            }\n          }\n        }\n      } else {\n        canvasData.minLeft = -canvasData.width;\n        canvasData.minTop = -canvasData.height;\n        canvasData.maxLeft = containerData.width;\n        canvasData.maxTop = containerData.height;\n      }\n    }\n  },\n  renderCanvas: function renderCanvas(changed, transformed) {\n    var canvasData = this.canvasData,\n      imageData = this.imageData;\n    if (transformed) {\n      var _getRotatedSizes = getRotatedSizes({\n          width: imageData.naturalWidth * Math.abs(imageData.scaleX || 1),\n          height: imageData.naturalHeight * Math.abs(imageData.scaleY || 1),\n          degree: imageData.rotate || 0\n        }),\n        naturalWidth = _getRotatedSizes.width,\n        naturalHeight = _getRotatedSizes.height;\n      var width = canvasData.width * (naturalWidth / canvasData.naturalWidth);\n      var height = canvasData.height * (naturalHeight / canvasData.naturalHeight);\n      canvasData.left -= (width - canvasData.width) / 2;\n      canvasData.top -= (height - canvasData.height) / 2;\n      canvasData.width = width;\n      canvasData.height = height;\n      canvasData.aspectRatio = naturalWidth / naturalHeight;\n      canvasData.naturalWidth = naturalWidth;\n      canvasData.naturalHeight = naturalHeight;\n      this.limitCanvas(true, false);\n    }\n    if (canvasData.width > canvasData.maxWidth || canvasData.width < canvasData.minWidth) {\n      canvasData.left = canvasData.oldLeft;\n    }\n    if (canvasData.height > canvasData.maxHeight || canvasData.height < canvasData.minHeight) {\n      canvasData.top = canvasData.oldTop;\n    }\n    canvasData.width = Math.min(Math.max(canvasData.width, canvasData.minWidth), canvasData.maxWidth);\n    canvasData.height = Math.min(Math.max(canvasData.height, canvasData.minHeight), canvasData.maxHeight);\n    this.limitCanvas(false, true);\n    canvasData.left = Math.min(Math.max(canvasData.left, canvasData.minLeft), canvasData.maxLeft);\n    canvasData.top = Math.min(Math.max(canvasData.top, canvasData.minTop), canvasData.maxTop);\n    canvasData.oldLeft = canvasData.left;\n    canvasData.oldTop = canvasData.top;\n    setStyle(this.canvas, assign({\n      width: canvasData.width,\n      height: canvasData.height\n    }, getTransforms({\n      translateX: canvasData.left,\n      translateY: canvasData.top\n    })));\n    this.renderImage(changed);\n    if (this.cropped && this.limited) {\n      this.limitCropBox(true, true);\n    }\n  },\n  renderImage: function renderImage(changed) {\n    var canvasData = this.canvasData,\n      imageData = this.imageData;\n    var width = imageData.naturalWidth * (canvasData.width / canvasData.naturalWidth);\n    var height = imageData.naturalHeight * (canvasData.height / canvasData.naturalHeight);\n    assign(imageData, {\n      width: width,\n      height: height,\n      left: (canvasData.width - width) / 2,\n      top: (canvasData.height - height) / 2\n    });\n    setStyle(this.image, assign({\n      width: imageData.width,\n      height: imageData.height\n    }, getTransforms(assign({\n      translateX: imageData.left,\n      translateY: imageData.top\n    }, imageData))));\n    if (changed) {\n      this.output();\n    }\n  },\n  initCropBox: function initCropBox() {\n    var options = this.options,\n      canvasData = this.canvasData;\n    var aspectRatio = options.aspectRatio || options.initialAspectRatio;\n    var autoCropArea = Number(options.autoCropArea) || 0.8;\n    var cropBoxData = {\n      width: canvasData.width,\n      height: canvasData.height\n    };\n    if (aspectRatio) {\n      if (canvasData.height * aspectRatio > canvasData.width) {\n        cropBoxData.height = cropBoxData.width / aspectRatio;\n      } else {\n        cropBoxData.width = cropBoxData.height * aspectRatio;\n      }\n    }\n    this.cropBoxData = cropBoxData;\n    this.limitCropBox(true, true);\n\n    // Initialize auto crop area\n    cropBoxData.width = Math.min(Math.max(cropBoxData.width, cropBoxData.minWidth), cropBoxData.maxWidth);\n    cropBoxData.height = Math.min(Math.max(cropBoxData.height, cropBoxData.minHeight), cropBoxData.maxHeight);\n\n    // The width/height of auto crop area must large than \"minWidth/Height\"\n    cropBoxData.width = Math.max(cropBoxData.minWidth, cropBoxData.width * autoCropArea);\n    cropBoxData.height = Math.max(cropBoxData.minHeight, cropBoxData.height * autoCropArea);\n    cropBoxData.left = canvasData.left + (canvasData.width - cropBoxData.width) / 2;\n    cropBoxData.top = canvasData.top + (canvasData.height - cropBoxData.height) / 2;\n    cropBoxData.oldLeft = cropBoxData.left;\n    cropBoxData.oldTop = cropBoxData.top;\n    this.initialCropBoxData = assign({}, cropBoxData);\n  },\n  limitCropBox: function limitCropBox(sizeLimited, positionLimited) {\n    var options = this.options,\n      containerData = this.containerData,\n      canvasData = this.canvasData,\n      cropBoxData = this.cropBoxData,\n      limited = this.limited;\n    var aspectRatio = options.aspectRatio;\n    if (sizeLimited) {\n      var minCropBoxWidth = Number(options.minCropBoxWidth) || 0;\n      var minCropBoxHeight = Number(options.minCropBoxHeight) || 0;\n      var maxCropBoxWidth = limited ? Math.min(containerData.width, canvasData.width, canvasData.width + canvasData.left, containerData.width - canvasData.left) : containerData.width;\n      var maxCropBoxHeight = limited ? Math.min(containerData.height, canvasData.height, canvasData.height + canvasData.top, containerData.height - canvasData.top) : containerData.height;\n\n      // The min/maxCropBoxWidth/Height must be less than container's width/height\n      minCropBoxWidth = Math.min(minCropBoxWidth, containerData.width);\n      minCropBoxHeight = Math.min(minCropBoxHeight, containerData.height);\n      if (aspectRatio) {\n        if (minCropBoxWidth && minCropBoxHeight) {\n          if (minCropBoxHeight * aspectRatio > minCropBoxWidth) {\n            minCropBoxHeight = minCropBoxWidth / aspectRatio;\n          } else {\n            minCropBoxWidth = minCropBoxHeight * aspectRatio;\n          }\n        } else if (minCropBoxWidth) {\n          minCropBoxHeight = minCropBoxWidth / aspectRatio;\n        } else if (minCropBoxHeight) {\n          minCropBoxWidth = minCropBoxHeight * aspectRatio;\n        }\n        if (maxCropBoxHeight * aspectRatio > maxCropBoxWidth) {\n          maxCropBoxHeight = maxCropBoxWidth / aspectRatio;\n        } else {\n          maxCropBoxWidth = maxCropBoxHeight * aspectRatio;\n        }\n      }\n\n      // The minWidth/Height must be less than maxWidth/Height\n      cropBoxData.minWidth = Math.min(minCropBoxWidth, maxCropBoxWidth);\n      cropBoxData.minHeight = Math.min(minCropBoxHeight, maxCropBoxHeight);\n      cropBoxData.maxWidth = maxCropBoxWidth;\n      cropBoxData.maxHeight = maxCropBoxHeight;\n    }\n    if (positionLimited) {\n      if (limited) {\n        cropBoxData.minLeft = Math.max(0, canvasData.left);\n        cropBoxData.minTop = Math.max(0, canvasData.top);\n        cropBoxData.maxLeft = Math.min(containerData.width, canvasData.left + canvasData.width) - cropBoxData.width;\n        cropBoxData.maxTop = Math.min(containerData.height, canvasData.top + canvasData.height) - cropBoxData.height;\n      } else {\n        cropBoxData.minLeft = 0;\n        cropBoxData.minTop = 0;\n        cropBoxData.maxLeft = containerData.width - cropBoxData.width;\n        cropBoxData.maxTop = containerData.height - cropBoxData.height;\n      }\n    }\n  },\n  renderCropBox: function renderCropBox() {\n    var options = this.options,\n      containerData = this.containerData,\n      cropBoxData = this.cropBoxData;\n    if (cropBoxData.width > cropBoxData.maxWidth || cropBoxData.width < cropBoxData.minWidth) {\n      cropBoxData.left = cropBoxData.oldLeft;\n    }\n    if (cropBoxData.height > cropBoxData.maxHeight || cropBoxData.height < cropBoxData.minHeight) {\n      cropBoxData.top = cropBoxData.oldTop;\n    }\n    cropBoxData.width = Math.min(Math.max(cropBoxData.width, cropBoxData.minWidth), cropBoxData.maxWidth);\n    cropBoxData.height = Math.min(Math.max(cropBoxData.height, cropBoxData.minHeight), cropBoxData.maxHeight);\n    this.limitCropBox(false, true);\n    cropBoxData.left = Math.min(Math.max(cropBoxData.left, cropBoxData.minLeft), cropBoxData.maxLeft);\n    cropBoxData.top = Math.min(Math.max(cropBoxData.top, cropBoxData.minTop), cropBoxData.maxTop);\n    cropBoxData.oldLeft = cropBoxData.left;\n    cropBoxData.oldTop = cropBoxData.top;\n    if (options.movable && options.cropBoxMovable) {\n      // Turn to move the canvas when the crop box is equal to the container\n      setData(this.face, DATA_ACTION, cropBoxData.width >= containerData.width && cropBoxData.height >= containerData.height ? ACTION_MOVE : ACTION_ALL);\n    }\n    setStyle(this.cropBox, assign({\n      width: cropBoxData.width,\n      height: cropBoxData.height\n    }, getTransforms({\n      translateX: cropBoxData.left,\n      translateY: cropBoxData.top\n    })));\n    if (this.cropped && this.limited) {\n      this.limitCanvas(true, true);\n    }\n    if (!this.disabled) {\n      this.output();\n    }\n  },\n  output: function output() {\n    this.preview();\n    dispatchEvent(this.element, EVENT_CROP, this.getData());\n  }\n};\n\nvar preview = {\n  initPreview: function initPreview() {\n    var element = this.element,\n      crossOrigin = this.crossOrigin;\n    var preview = this.options.preview;\n    var url = crossOrigin ? this.crossOriginUrl : this.url;\n    var alt = element.alt || 'The image to preview';\n    var image = document.createElement('img');\n    if (crossOrigin) {\n      image.crossOrigin = crossOrigin;\n    }\n    image.src = url;\n    image.alt = alt;\n    this.viewBox.appendChild(image);\n    this.viewBoxImage = image;\n    if (!preview) {\n      return;\n    }\n    var previews = preview;\n    if (typeof preview === 'string') {\n      previews = element.ownerDocument.querySelectorAll(preview);\n    } else if (preview.querySelector) {\n      previews = [preview];\n    }\n    this.previews = previews;\n    forEach(previews, function (el) {\n      var img = document.createElement('img');\n\n      // Save the original size for recover\n      setData(el, DATA_PREVIEW, {\n        width: el.offsetWidth,\n        height: el.offsetHeight,\n        html: el.innerHTML\n      });\n      if (crossOrigin) {\n        img.crossOrigin = crossOrigin;\n      }\n      img.src = url;\n      img.alt = alt;\n\n      /**\n       * Override img element styles\n       * Add `display:block` to avoid margin top issue\n       * Add `height:auto` to override `height` attribute on IE8\n       * (Occur only when margin-top <= -height)\n       */\n      img.style.cssText = 'display:block;' + 'width:100%;' + 'height:auto;' + 'min-width:0!important;' + 'min-height:0!important;' + 'max-width:none!important;' + 'max-height:none!important;' + 'image-orientation:0deg!important;\"';\n      el.innerHTML = '';\n      el.appendChild(img);\n    });\n  },\n  resetPreview: function resetPreview() {\n    forEach(this.previews, function (element) {\n      var data = getData(element, DATA_PREVIEW);\n      setStyle(element, {\n        width: data.width,\n        height: data.height\n      });\n      element.innerHTML = data.html;\n      removeData(element, DATA_PREVIEW);\n    });\n  },\n  preview: function preview() {\n    var imageData = this.imageData,\n      canvasData = this.canvasData,\n      cropBoxData = this.cropBoxData;\n    var cropBoxWidth = cropBoxData.width,\n      cropBoxHeight = cropBoxData.height;\n    var width = imageData.width,\n      height = imageData.height;\n    var left = cropBoxData.left - canvasData.left - imageData.left;\n    var top = cropBoxData.top - canvasData.top - imageData.top;\n    if (!this.cropped || this.disabled) {\n      return;\n    }\n    setStyle(this.viewBoxImage, assign({\n      width: width,\n      height: height\n    }, getTransforms(assign({\n      translateX: -left,\n      translateY: -top\n    }, imageData))));\n    forEach(this.previews, function (element) {\n      var data = getData(element, DATA_PREVIEW);\n      var originalWidth = data.width;\n      var originalHeight = data.height;\n      var newWidth = originalWidth;\n      var newHeight = originalHeight;\n      var ratio = 1;\n      if (cropBoxWidth) {\n        ratio = originalWidth / cropBoxWidth;\n        newHeight = cropBoxHeight * ratio;\n      }\n      if (cropBoxHeight && newHeight > originalHeight) {\n        ratio = originalHeight / cropBoxHeight;\n        newWidth = cropBoxWidth * ratio;\n        newHeight = originalHeight;\n      }\n      setStyle(element, {\n        width: newWidth,\n        height: newHeight\n      });\n      setStyle(element.getElementsByTagName('img')[0], assign({\n        width: width * ratio,\n        height: height * ratio\n      }, getTransforms(assign({\n        translateX: -left * ratio,\n        translateY: -top * ratio\n      }, imageData))));\n    });\n  }\n};\n\nvar events = {\n  bind: function bind() {\n    var element = this.element,\n      options = this.options,\n      cropper = this.cropper;\n    if (isFunction(options.cropstart)) {\n      addListener(element, EVENT_CROP_START, options.cropstart);\n    }\n    if (isFunction(options.cropmove)) {\n      addListener(element, EVENT_CROP_MOVE, options.cropmove);\n    }\n    if (isFunction(options.cropend)) {\n      addListener(element, EVENT_CROP_END, options.cropend);\n    }\n    if (isFunction(options.crop)) {\n      addListener(element, EVENT_CROP, options.crop);\n    }\n    if (isFunction(options.zoom)) {\n      addListener(element, EVENT_ZOOM, options.zoom);\n    }\n    addListener(cropper, EVENT_POINTER_DOWN, this.onCropStart = this.cropStart.bind(this));\n    if (options.zoomable && options.zoomOnWheel) {\n      addListener(cropper, EVENT_WHEEL, this.onWheel = this.wheel.bind(this), {\n        passive: false,\n        capture: true\n      });\n    }\n    if (options.toggleDragModeOnDblclick) {\n      addListener(cropper, EVENT_DBLCLICK, this.onDblclick = this.dblclick.bind(this));\n    }\n    addListener(element.ownerDocument, EVENT_POINTER_MOVE, this.onCropMove = this.cropMove.bind(this));\n    addListener(element.ownerDocument, EVENT_POINTER_UP, this.onCropEnd = this.cropEnd.bind(this));\n    if (options.responsive) {\n      addListener(window, EVENT_RESIZE, this.onResize = this.resize.bind(this));\n    }\n  },\n  unbind: function unbind() {\n    var element = this.element,\n      options = this.options,\n      cropper = this.cropper;\n    if (isFunction(options.cropstart)) {\n      removeListener(element, EVENT_CROP_START, options.cropstart);\n    }\n    if (isFunction(options.cropmove)) {\n      removeListener(element, EVENT_CROP_MOVE, options.cropmove);\n    }\n    if (isFunction(options.cropend)) {\n      removeListener(element, EVENT_CROP_END, options.cropend);\n    }\n    if (isFunction(options.crop)) {\n      removeListener(element, EVENT_CROP, options.crop);\n    }\n    if (isFunction(options.zoom)) {\n      removeListener(element, EVENT_ZOOM, options.zoom);\n    }\n    removeListener(cropper, EVENT_POINTER_DOWN, this.onCropStart);\n    if (options.zoomable && options.zoomOnWheel) {\n      removeListener(cropper, EVENT_WHEEL, this.onWheel, {\n        passive: false,\n        capture: true\n      });\n    }\n    if (options.toggleDragModeOnDblclick) {\n      removeListener(cropper, EVENT_DBLCLICK, this.onDblclick);\n    }\n    removeListener(element.ownerDocument, EVENT_POINTER_MOVE, this.onCropMove);\n    removeListener(element.ownerDocument, EVENT_POINTER_UP, this.onCropEnd);\n    if (options.responsive) {\n      removeListener(window, EVENT_RESIZE, this.onResize);\n    }\n  }\n};\n\nvar handlers = {\n  resize: function resize() {\n    if (this.disabled) {\n      return;\n    }\n    var options = this.options,\n      container = this.container,\n      containerData = this.containerData;\n    var ratioX = container.offsetWidth / containerData.width;\n    var ratioY = container.offsetHeight / containerData.height;\n    var ratio = Math.abs(ratioX - 1) > Math.abs(ratioY - 1) ? ratioX : ratioY;\n\n    // Resize when width changed or height changed\n    if (ratio !== 1) {\n      var canvasData;\n      var cropBoxData;\n      if (options.restore) {\n        canvasData = this.getCanvasData();\n        cropBoxData = this.getCropBoxData();\n      }\n      this.render();\n      if (options.restore) {\n        this.setCanvasData(forEach(canvasData, function (n, i) {\n          canvasData[i] = n * ratio;\n        }));\n        this.setCropBoxData(forEach(cropBoxData, function (n, i) {\n          cropBoxData[i] = n * ratio;\n        }));\n      }\n    }\n  },\n  dblclick: function dblclick() {\n    if (this.disabled || this.options.dragMode === DRAG_MODE_NONE) {\n      return;\n    }\n    this.setDragMode(hasClass(this.dragBox, CLASS_CROP) ? DRAG_MODE_MOVE : DRAG_MODE_CROP);\n  },\n  wheel: function wheel(event) {\n    var _this = this;\n    var ratio = Number(this.options.wheelZoomRatio) || 0.1;\n    var delta = 1;\n    if (this.disabled) {\n      return;\n    }\n    event.preventDefault();\n\n    // Limit wheel speed to prevent zoom too fast (#21)\n    if (this.wheeling) {\n      return;\n    }\n    this.wheeling = true;\n    setTimeout(function () {\n      _this.wheeling = false;\n    }, 50);\n    if (event.deltaY) {\n      delta = event.deltaY > 0 ? 1 : -1;\n    } else if (event.wheelDelta) {\n      delta = -event.wheelDelta / 120;\n    } else if (event.detail) {\n      delta = event.detail > 0 ? 1 : -1;\n    }\n    this.zoom(-delta * ratio, event);\n  },\n  cropStart: function cropStart(event) {\n    var buttons = event.buttons,\n      button = event.button;\n    if (this.disabled\n\n    // Handle mouse event and pointer event and ignore touch event\n    || (event.type === 'mousedown' || event.type === 'pointerdown' && event.pointerType === 'mouse') && (\n    // No primary button (Usually the left button)\n    isNumber(buttons) && buttons !== 1 || isNumber(button) && button !== 0\n\n    // Open context menu\n    || event.ctrlKey)) {\n      return;\n    }\n    var options = this.options,\n      pointers = this.pointers;\n    var action;\n    if (event.changedTouches) {\n      // Handle touch event\n      forEach(event.changedTouches, function (touch) {\n        pointers[touch.identifier] = getPointer(touch);\n      });\n    } else {\n      // Handle mouse event and pointer event\n      pointers[event.pointerId || 0] = getPointer(event);\n    }\n    if (Object.keys(pointers).length > 1 && options.zoomable && options.zoomOnTouch) {\n      action = ACTION_ZOOM;\n    } else {\n      action = getData(event.target, DATA_ACTION);\n    }\n    if (!REGEXP_ACTIONS.test(action)) {\n      return;\n    }\n    if (dispatchEvent(this.element, EVENT_CROP_START, {\n      originalEvent: event,\n      action: action\n    }) === false) {\n      return;\n    }\n\n    // This line is required for preventing page zooming in iOS browsers\n    event.preventDefault();\n    this.action = action;\n    this.cropping = false;\n    if (action === ACTION_CROP) {\n      this.cropping = true;\n      addClass(this.dragBox, CLASS_MODAL);\n    }\n  },\n  cropMove: function cropMove(event) {\n    var action = this.action;\n    if (this.disabled || !action) {\n      return;\n    }\n    var pointers = this.pointers;\n    event.preventDefault();\n    if (dispatchEvent(this.element, EVENT_CROP_MOVE, {\n      originalEvent: event,\n      action: action\n    }) === false) {\n      return;\n    }\n    if (event.changedTouches) {\n      forEach(event.changedTouches, function (touch) {\n        // The first parameter should not be undefined (#432)\n        assign(pointers[touch.identifier] || {}, getPointer(touch, true));\n      });\n    } else {\n      assign(pointers[event.pointerId || 0] || {}, getPointer(event, true));\n    }\n    this.change(event);\n  },\n  cropEnd: function cropEnd(event) {\n    if (this.disabled) {\n      return;\n    }\n    var action = this.action,\n      pointers = this.pointers;\n    if (event.changedTouches) {\n      forEach(event.changedTouches, function (touch) {\n        delete pointers[touch.identifier];\n      });\n    } else {\n      delete pointers[event.pointerId || 0];\n    }\n    if (!action) {\n      return;\n    }\n    event.preventDefault();\n    if (!Object.keys(pointers).length) {\n      this.action = '';\n    }\n    if (this.cropping) {\n      this.cropping = false;\n      toggleClass(this.dragBox, CLASS_MODAL, this.cropped && this.options.modal);\n    }\n    dispatchEvent(this.element, EVENT_CROP_END, {\n      originalEvent: event,\n      action: action\n    });\n  }\n};\n\nvar change = {\n  change: function change(event) {\n    var options = this.options,\n      canvasData = this.canvasData,\n      containerData = this.containerData,\n      cropBoxData = this.cropBoxData,\n      pointers = this.pointers;\n    var action = this.action;\n    var aspectRatio = options.aspectRatio;\n    var left = cropBoxData.left,\n      top = cropBoxData.top,\n      width = cropBoxData.width,\n      height = cropBoxData.height;\n    var right = left + width;\n    var bottom = top + height;\n    var minLeft = 0;\n    var minTop = 0;\n    var maxWidth = containerData.width;\n    var maxHeight = containerData.height;\n    var renderable = true;\n    var offset;\n\n    // Locking aspect ratio in \"free mode\" by holding shift key\n    if (!aspectRatio && event.shiftKey) {\n      aspectRatio = width && height ? width / height : 1;\n    }\n    if (this.limited) {\n      minLeft = cropBoxData.minLeft;\n      minTop = cropBoxData.minTop;\n      maxWidth = minLeft + Math.min(containerData.width, canvasData.width, canvasData.left + canvasData.width);\n      maxHeight = minTop + Math.min(containerData.height, canvasData.height, canvasData.top + canvasData.height);\n    }\n    var pointer = pointers[Object.keys(pointers)[0]];\n    var range = {\n      x: pointer.endX - pointer.startX,\n      y: pointer.endY - pointer.startY\n    };\n    var check = function check(side) {\n      switch (side) {\n        case ACTION_EAST:\n          if (right + range.x > maxWidth) {\n            range.x = maxWidth - right;\n          }\n          break;\n        case ACTION_WEST:\n          if (left + range.x < minLeft) {\n            range.x = minLeft - left;\n          }\n          break;\n        case ACTION_NORTH:\n          if (top + range.y < minTop) {\n            range.y = minTop - top;\n          }\n          break;\n        case ACTION_SOUTH:\n          if (bottom + range.y > maxHeight) {\n            range.y = maxHeight - bottom;\n          }\n          break;\n      }\n    };\n    switch (action) {\n      // Move crop box\n      case ACTION_ALL:\n        left += range.x;\n        top += range.y;\n        break;\n\n      // Resize crop box\n      case ACTION_EAST:\n        if (range.x >= 0 && (right >= maxWidth || aspectRatio && (top <= minTop || bottom >= maxHeight))) {\n          renderable = false;\n          break;\n        }\n        check(ACTION_EAST);\n        width += range.x;\n        if (width < 0) {\n          action = ACTION_WEST;\n          width = -width;\n          left -= width;\n        }\n        if (aspectRatio) {\n          height = width / aspectRatio;\n          top += (cropBoxData.height - height) / 2;\n        }\n        break;\n      case ACTION_NORTH:\n        if (range.y <= 0 && (top <= minTop || aspectRatio && (left <= minLeft || right >= maxWidth))) {\n          renderable = false;\n          break;\n        }\n        check(ACTION_NORTH);\n        height -= range.y;\n        top += range.y;\n        if (height < 0) {\n          action = ACTION_SOUTH;\n          height = -height;\n          top -= height;\n        }\n        if (aspectRatio) {\n          width = height * aspectRatio;\n          left += (cropBoxData.width - width) / 2;\n        }\n        break;\n      case ACTION_WEST:\n        if (range.x <= 0 && (left <= minLeft || aspectRatio && (top <= minTop || bottom >= maxHeight))) {\n          renderable = false;\n          break;\n        }\n        check(ACTION_WEST);\n        width -= range.x;\n        left += range.x;\n        if (width < 0) {\n          action = ACTION_EAST;\n          width = -width;\n          left -= width;\n        }\n        if (aspectRatio) {\n          height = width / aspectRatio;\n          top += (cropBoxData.height - height) / 2;\n        }\n        break;\n      case ACTION_SOUTH:\n        if (range.y >= 0 && (bottom >= maxHeight || aspectRatio && (left <= minLeft || right >= maxWidth))) {\n          renderable = false;\n          break;\n        }\n        check(ACTION_SOUTH);\n        height += range.y;\n        if (height < 0) {\n          action = ACTION_NORTH;\n          height = -height;\n          top -= height;\n        }\n        if (aspectRatio) {\n          width = height * aspectRatio;\n          left += (cropBoxData.width - width) / 2;\n        }\n        break;\n      case ACTION_NORTH_EAST:\n        if (aspectRatio) {\n          if (range.y <= 0 && (top <= minTop || right >= maxWidth)) {\n            renderable = false;\n            break;\n          }\n          check(ACTION_NORTH);\n          height -= range.y;\n          top += range.y;\n          width = height * aspectRatio;\n        } else {\n          check(ACTION_NORTH);\n          check(ACTION_EAST);\n          if (range.x >= 0) {\n            if (right < maxWidth) {\n              width += range.x;\n            } else if (range.y <= 0 && top <= minTop) {\n              renderable = false;\n            }\n          } else {\n            width += range.x;\n          }\n          if (range.y <= 0) {\n            if (top > minTop) {\n              height -= range.y;\n              top += range.y;\n            }\n          } else {\n            height -= range.y;\n            top += range.y;\n          }\n        }\n        if (width < 0 && height < 0) {\n          action = ACTION_SOUTH_WEST;\n          height = -height;\n          width = -width;\n          top -= height;\n          left -= width;\n        } else if (width < 0) {\n          action = ACTION_NORTH_WEST;\n          width = -width;\n          left -= width;\n        } else if (height < 0) {\n          action = ACTION_SOUTH_EAST;\n          height = -height;\n          top -= height;\n        }\n        break;\n      case ACTION_NORTH_WEST:\n        if (aspectRatio) {\n          if (range.y <= 0 && (top <= minTop || left <= minLeft)) {\n            renderable = false;\n            break;\n          }\n          check(ACTION_NORTH);\n          height -= range.y;\n          top += range.y;\n          width = height * aspectRatio;\n          left += cropBoxData.width - width;\n        } else {\n          check(ACTION_NORTH);\n          check(ACTION_WEST);\n          if (range.x <= 0) {\n            if (left > minLeft) {\n              width -= range.x;\n              left += range.x;\n            } else if (range.y <= 0 && top <= minTop) {\n              renderable = false;\n            }\n          } else {\n            width -= range.x;\n            left += range.x;\n          }\n          if (range.y <= 0) {\n            if (top > minTop) {\n              height -= range.y;\n              top += range.y;\n            }\n          } else {\n            height -= range.y;\n            top += range.y;\n          }\n        }\n        if (width < 0 && height < 0) {\n          action = ACTION_SOUTH_EAST;\n          height = -height;\n          width = -width;\n          top -= height;\n          left -= width;\n        } else if (width < 0) {\n          action = ACTION_NORTH_EAST;\n          width = -width;\n          left -= width;\n        } else if (height < 0) {\n          action = ACTION_SOUTH_WEST;\n          height = -height;\n          top -= height;\n        }\n        break;\n      case ACTION_SOUTH_WEST:\n        if (aspectRatio) {\n          if (range.x <= 0 && (left <= minLeft || bottom >= maxHeight)) {\n            renderable = false;\n            break;\n          }\n          check(ACTION_WEST);\n          width -= range.x;\n          left += range.x;\n          height = width / aspectRatio;\n        } else {\n          check(ACTION_SOUTH);\n          check(ACTION_WEST);\n          if (range.x <= 0) {\n            if (left > minLeft) {\n              width -= range.x;\n              left += range.x;\n            } else if (range.y >= 0 && bottom >= maxHeight) {\n              renderable = false;\n            }\n          } else {\n            width -= range.x;\n            left += range.x;\n          }\n          if (range.y >= 0) {\n            if (bottom < maxHeight) {\n              height += range.y;\n            }\n          } else {\n            height += range.y;\n          }\n        }\n        if (width < 0 && height < 0) {\n          action = ACTION_NORTH_EAST;\n          height = -height;\n          width = -width;\n          top -= height;\n          left -= width;\n        } else if (width < 0) {\n          action = ACTION_SOUTH_EAST;\n          width = -width;\n          left -= width;\n        } else if (height < 0) {\n          action = ACTION_NORTH_WEST;\n          height = -height;\n          top -= height;\n        }\n        break;\n      case ACTION_SOUTH_EAST:\n        if (aspectRatio) {\n          if (range.x >= 0 && (right >= maxWidth || bottom >= maxHeight)) {\n            renderable = false;\n            break;\n          }\n          check(ACTION_EAST);\n          width += range.x;\n          height = width / aspectRatio;\n        } else {\n          check(ACTION_SOUTH);\n          check(ACTION_EAST);\n          if (range.x >= 0) {\n            if (right < maxWidth) {\n              width += range.x;\n            } else if (range.y >= 0 && bottom >= maxHeight) {\n              renderable = false;\n            }\n          } else {\n            width += range.x;\n          }\n          if (range.y >= 0) {\n            if (bottom < maxHeight) {\n              height += range.y;\n            }\n          } else {\n            height += range.y;\n          }\n        }\n        if (width < 0 && height < 0) {\n          action = ACTION_NORTH_WEST;\n          height = -height;\n          width = -width;\n          top -= height;\n          left -= width;\n        } else if (width < 0) {\n          action = ACTION_SOUTH_WEST;\n          width = -width;\n          left -= width;\n        } else if (height < 0) {\n          action = ACTION_NORTH_EAST;\n          height = -height;\n          top -= height;\n        }\n        break;\n\n      // Move canvas\n      case ACTION_MOVE:\n        this.move(range.x, range.y);\n        renderable = false;\n        break;\n\n      // Zoom canvas\n      case ACTION_ZOOM:\n        this.zoom(getMaxZoomRatio(pointers), event);\n        renderable = false;\n        break;\n\n      // Create crop box\n      case ACTION_CROP:\n        if (!range.x || !range.y) {\n          renderable = false;\n          break;\n        }\n        offset = getOffset(this.cropper);\n        left = pointer.startX - offset.left;\n        top = pointer.startY - offset.top;\n        width = cropBoxData.minWidth;\n        height = cropBoxData.minHeight;\n        if (range.x > 0) {\n          action = range.y > 0 ? ACTION_SOUTH_EAST : ACTION_NORTH_EAST;\n        } else if (range.x < 0) {\n          left -= width;\n          action = range.y > 0 ? ACTION_SOUTH_WEST : ACTION_NORTH_WEST;\n        }\n        if (range.y < 0) {\n          top -= height;\n        }\n\n        // Show the crop box if is hidden\n        if (!this.cropped) {\n          removeClass(this.cropBox, CLASS_HIDDEN);\n          this.cropped = true;\n          if (this.limited) {\n            this.limitCropBox(true, true);\n          }\n        }\n        break;\n    }\n    if (renderable) {\n      cropBoxData.width = width;\n      cropBoxData.height = height;\n      cropBoxData.left = left;\n      cropBoxData.top = top;\n      this.action = action;\n      this.renderCropBox();\n    }\n\n    // Override\n    forEach(pointers, function (p) {\n      p.startX = p.endX;\n      p.startY = p.endY;\n    });\n  }\n};\n\nvar methods = {\n  // Show the crop box manually\n  crop: function crop() {\n    if (this.ready && !this.cropped && !this.disabled) {\n      this.cropped = true;\n      this.limitCropBox(true, true);\n      if (this.options.modal) {\n        addClass(this.dragBox, CLASS_MODAL);\n      }\n      removeClass(this.cropBox, CLASS_HIDDEN);\n      this.setCropBoxData(this.initialCropBoxData);\n    }\n    return this;\n  },\n  // Reset the image and crop box to their initial states\n  reset: function reset() {\n    if (this.ready && !this.disabled) {\n      this.imageData = assign({}, this.initialImageData);\n      this.canvasData = assign({}, this.initialCanvasData);\n      this.cropBoxData = assign({}, this.initialCropBoxData);\n      this.renderCanvas();\n      if (this.cropped) {\n        this.renderCropBox();\n      }\n    }\n    return this;\n  },\n  // Clear the crop box\n  clear: function clear() {\n    if (this.cropped && !this.disabled) {\n      assign(this.cropBoxData, {\n        left: 0,\n        top: 0,\n        width: 0,\n        height: 0\n      });\n      this.cropped = false;\n      this.renderCropBox();\n      this.limitCanvas(true, true);\n\n      // Render canvas after crop box rendered\n      this.renderCanvas();\n      removeClass(this.dragBox, CLASS_MODAL);\n      addClass(this.cropBox, CLASS_HIDDEN);\n    }\n    return this;\n  },\n  /**\n   * Replace the image's src and rebuild the cropper\n   * @param {string} url - The new URL.\n   * @param {boolean} [hasSameSize] - Indicate if the new image has the same size as the old one.\n   * @returns {Cropper} this\n   */\n  replace: function replace(url) {\n    var hasSameSize = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    if (!this.disabled && url) {\n      if (this.isImg) {\n        this.element.src = url;\n      }\n      if (hasSameSize) {\n        this.url = url;\n        this.image.src = url;\n        if (this.ready) {\n          this.viewBoxImage.src = url;\n          forEach(this.previews, function (element) {\n            element.getElementsByTagName('img')[0].src = url;\n          });\n        }\n      } else {\n        if (this.isImg) {\n          this.replaced = true;\n        }\n        this.options.data = null;\n        this.uncreate();\n        this.load(url);\n      }\n    }\n    return this;\n  },\n  // Enable (unfreeze) the cropper\n  enable: function enable() {\n    if (this.ready && this.disabled) {\n      this.disabled = false;\n      removeClass(this.cropper, CLASS_DISABLED);\n    }\n    return this;\n  },\n  // Disable (freeze) the cropper\n  disable: function disable() {\n    if (this.ready && !this.disabled) {\n      this.disabled = true;\n      addClass(this.cropper, CLASS_DISABLED);\n    }\n    return this;\n  },\n  /**\n   * Destroy the cropper and remove the instance from the image\n   * @returns {Cropper} this\n   */\n  destroy: function destroy() {\n    var element = this.element;\n    if (!element[NAMESPACE]) {\n      return this;\n    }\n    element[NAMESPACE] = undefined;\n    if (this.isImg && this.replaced) {\n      element.src = this.originalUrl;\n    }\n    this.uncreate();\n    return this;\n  },\n  /**\n   * Move the canvas with relative offsets\n   * @param {number} offsetX - The relative offset distance on the x-axis.\n   * @param {number} [offsetY=offsetX] - The relative offset distance on the y-axis.\n   * @returns {Cropper} this\n   */\n  move: function move(offsetX) {\n    var offsetY = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : offsetX;\n    var _this$canvasData = this.canvasData,\n      left = _this$canvasData.left,\n      top = _this$canvasData.top;\n    return this.moveTo(isUndefined(offsetX) ? offsetX : left + Number(offsetX), isUndefined(offsetY) ? offsetY : top + Number(offsetY));\n  },\n  /**\n   * Move the canvas to an absolute point\n   * @param {number} x - The x-axis coordinate.\n   * @param {number} [y=x] - The y-axis coordinate.\n   * @returns {Cropper} this\n   */\n  moveTo: function moveTo(x) {\n    var y = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : x;\n    var canvasData = this.canvasData;\n    var changed = false;\n    x = Number(x);\n    y = Number(y);\n    if (this.ready && !this.disabled && this.options.movable) {\n      if (isNumber(x)) {\n        canvasData.left = x;\n        changed = true;\n      }\n      if (isNumber(y)) {\n        canvasData.top = y;\n        changed = true;\n      }\n      if (changed) {\n        this.renderCanvas(true);\n      }\n    }\n    return this;\n  },\n  /**\n   * Zoom the canvas with a relative ratio\n   * @param {number} ratio - The target ratio.\n   * @param {Event} _originalEvent - The original event if any.\n   * @returns {Cropper} this\n   */\n  zoom: function zoom(ratio, _originalEvent) {\n    var canvasData = this.canvasData;\n    ratio = Number(ratio);\n    if (ratio < 0) {\n      ratio = 1 / (1 - ratio);\n    } else {\n      ratio = 1 + ratio;\n    }\n    return this.zoomTo(canvasData.width * ratio / canvasData.naturalWidth, null, _originalEvent);\n  },\n  /**\n   * Zoom the canvas to an absolute ratio\n   * @param {number} ratio - The target ratio.\n   * @param {Object} pivot - The zoom pivot point coordinate.\n   * @param {Event} _originalEvent - The original event if any.\n   * @returns {Cropper} this\n   */\n  zoomTo: function zoomTo(ratio, pivot, _originalEvent) {\n    var options = this.options,\n      canvasData = this.canvasData;\n    var width = canvasData.width,\n      height = canvasData.height,\n      naturalWidth = canvasData.naturalWidth,\n      naturalHeight = canvasData.naturalHeight;\n    ratio = Number(ratio);\n    if (ratio >= 0 && this.ready && !this.disabled && options.zoomable) {\n      var newWidth = naturalWidth * ratio;\n      var newHeight = naturalHeight * ratio;\n      if (dispatchEvent(this.element, EVENT_ZOOM, {\n        ratio: ratio,\n        oldRatio: width / naturalWidth,\n        originalEvent: _originalEvent\n      }) === false) {\n        return this;\n      }\n      if (_originalEvent) {\n        var pointers = this.pointers;\n        var offset = getOffset(this.cropper);\n        var center = pointers && Object.keys(pointers).length ? getPointersCenter(pointers) : {\n          pageX: _originalEvent.pageX,\n          pageY: _originalEvent.pageY\n        };\n\n        // Zoom from the triggering point of the event\n        canvasData.left -= (newWidth - width) * ((center.pageX - offset.left - canvasData.left) / width);\n        canvasData.top -= (newHeight - height) * ((center.pageY - offset.top - canvasData.top) / height);\n      } else if (isPlainObject(pivot) && isNumber(pivot.x) && isNumber(pivot.y)) {\n        canvasData.left -= (newWidth - width) * ((pivot.x - canvasData.left) / width);\n        canvasData.top -= (newHeight - height) * ((pivot.y - canvasData.top) / height);\n      } else {\n        // Zoom from the center of the canvas\n        canvasData.left -= (newWidth - width) / 2;\n        canvasData.top -= (newHeight - height) / 2;\n      }\n      canvasData.width = newWidth;\n      canvasData.height = newHeight;\n      this.renderCanvas(true);\n    }\n    return this;\n  },\n  /**\n   * Rotate the canvas with a relative degree\n   * @param {number} degree - The rotate degree.\n   * @returns {Cropper} this\n   */\n  rotate: function rotate(degree) {\n    return this.rotateTo((this.imageData.rotate || 0) + Number(degree));\n  },\n  /**\n   * Rotate the canvas to an absolute degree\n   * @param {number} degree - The rotate degree.\n   * @returns {Cropper} this\n   */\n  rotateTo: function rotateTo(degree) {\n    degree = Number(degree);\n    if (isNumber(degree) && this.ready && !this.disabled && this.options.rotatable) {\n      this.imageData.rotate = degree % 360;\n      this.renderCanvas(true, true);\n    }\n    return this;\n  },\n  /**\n   * Scale the image on the x-axis.\n   * @param {number} scaleX - The scale ratio on the x-axis.\n   * @returns {Cropper} this\n   */\n  scaleX: function scaleX(_scaleX) {\n    var scaleY = this.imageData.scaleY;\n    return this.scale(_scaleX, isNumber(scaleY) ? scaleY : 1);\n  },\n  /**\n   * Scale the image on the y-axis.\n   * @param {number} scaleY - The scale ratio on the y-axis.\n   * @returns {Cropper} this\n   */\n  scaleY: function scaleY(_scaleY) {\n    var scaleX = this.imageData.scaleX;\n    return this.scale(isNumber(scaleX) ? scaleX : 1, _scaleY);\n  },\n  /**\n   * Scale the image\n   * @param {number} scaleX - The scale ratio on the x-axis.\n   * @param {number} [scaleY=scaleX] - The scale ratio on the y-axis.\n   * @returns {Cropper} this\n   */\n  scale: function scale(scaleX) {\n    var scaleY = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : scaleX;\n    var imageData = this.imageData;\n    var transformed = false;\n    scaleX = Number(scaleX);\n    scaleY = Number(scaleY);\n    if (this.ready && !this.disabled && this.options.scalable) {\n      if (isNumber(scaleX)) {\n        imageData.scaleX = scaleX;\n        transformed = true;\n      }\n      if (isNumber(scaleY)) {\n        imageData.scaleY = scaleY;\n        transformed = true;\n      }\n      if (transformed) {\n        this.renderCanvas(true, true);\n      }\n    }\n    return this;\n  },\n  /**\n   * Get the cropped area position and size data (base on the original image)\n   * @param {boolean} [rounded=false] - Indicate if round the data values or not.\n   * @returns {Object} The result cropped data.\n   */\n  getData: function getData() {\n    var rounded = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var options = this.options,\n      imageData = this.imageData,\n      canvasData = this.canvasData,\n      cropBoxData = this.cropBoxData;\n    var data;\n    if (this.ready && this.cropped) {\n      data = {\n        x: cropBoxData.left - canvasData.left,\n        y: cropBoxData.top - canvasData.top,\n        width: cropBoxData.width,\n        height: cropBoxData.height\n      };\n      var ratio = imageData.width / imageData.naturalWidth;\n      forEach(data, function (n, i) {\n        data[i] = n / ratio;\n      });\n      if (rounded) {\n        // In case rounding off leads to extra 1px in right or bottom border\n        // we should round the top-left corner and the dimension (#343).\n        var bottom = Math.round(data.y + data.height);\n        var right = Math.round(data.x + data.width);\n        data.x = Math.round(data.x);\n        data.y = Math.round(data.y);\n        data.width = right - data.x;\n        data.height = bottom - data.y;\n      }\n    } else {\n      data = {\n        x: 0,\n        y: 0,\n        width: 0,\n        height: 0\n      };\n    }\n    if (options.rotatable) {\n      data.rotate = imageData.rotate || 0;\n    }\n    if (options.scalable) {\n      data.scaleX = imageData.scaleX || 1;\n      data.scaleY = imageData.scaleY || 1;\n    }\n    return data;\n  },\n  /**\n   * Set the cropped area position and size with new data\n   * @param {Object} data - The new data.\n   * @returns {Cropper} this\n   */\n  setData: function setData(data) {\n    var options = this.options,\n      imageData = this.imageData,\n      canvasData = this.canvasData;\n    var cropBoxData = {};\n    if (this.ready && !this.disabled && isPlainObject(data)) {\n      var transformed = false;\n      if (options.rotatable) {\n        if (isNumber(data.rotate) && data.rotate !== imageData.rotate) {\n          imageData.rotate = data.rotate;\n          transformed = true;\n        }\n      }\n      if (options.scalable) {\n        if (isNumber(data.scaleX) && data.scaleX !== imageData.scaleX) {\n          imageData.scaleX = data.scaleX;\n          transformed = true;\n        }\n        if (isNumber(data.scaleY) && data.scaleY !== imageData.scaleY) {\n          imageData.scaleY = data.scaleY;\n          transformed = true;\n        }\n      }\n      if (transformed) {\n        this.renderCanvas(true, true);\n      }\n      var ratio = imageData.width / imageData.naturalWidth;\n      if (isNumber(data.x)) {\n        cropBoxData.left = data.x * ratio + canvasData.left;\n      }\n      if (isNumber(data.y)) {\n        cropBoxData.top = data.y * ratio + canvasData.top;\n      }\n      if (isNumber(data.width)) {\n        cropBoxData.width = data.width * ratio;\n      }\n      if (isNumber(data.height)) {\n        cropBoxData.height = data.height * ratio;\n      }\n      this.setCropBoxData(cropBoxData);\n    }\n    return this;\n  },\n  /**\n   * Get the container size data.\n   * @returns {Object} The result container data.\n   */\n  getContainerData: function getContainerData() {\n    return this.ready ? assign({}, this.containerData) : {};\n  },\n  /**\n   * Get the image position and size data.\n   * @returns {Object} The result image data.\n   */\n  getImageData: function getImageData() {\n    return this.sized ? assign({}, this.imageData) : {};\n  },\n  /**\n   * Get the canvas position and size data.\n   * @returns {Object} The result canvas data.\n   */\n  getCanvasData: function getCanvasData() {\n    var canvasData = this.canvasData;\n    var data = {};\n    if (this.ready) {\n      forEach(['left', 'top', 'width', 'height', 'naturalWidth', 'naturalHeight'], function (n) {\n        data[n] = canvasData[n];\n      });\n    }\n    return data;\n  },\n  /**\n   * Set the canvas position and size with new data.\n   * @param {Object} data - The new canvas data.\n   * @returns {Cropper} this\n   */\n  setCanvasData: function setCanvasData(data) {\n    var canvasData = this.canvasData;\n    var aspectRatio = canvasData.aspectRatio;\n    if (this.ready && !this.disabled && isPlainObject(data)) {\n      if (isNumber(data.left)) {\n        canvasData.left = data.left;\n      }\n      if (isNumber(data.top)) {\n        canvasData.top = data.top;\n      }\n      if (isNumber(data.width)) {\n        canvasData.width = data.width;\n        canvasData.height = data.width / aspectRatio;\n      } else if (isNumber(data.height)) {\n        canvasData.height = data.height;\n        canvasData.width = data.height * aspectRatio;\n      }\n      this.renderCanvas(true);\n    }\n    return this;\n  },\n  /**\n   * Get the crop box position and size data.\n   * @returns {Object} The result crop box data.\n   */\n  getCropBoxData: function getCropBoxData() {\n    var cropBoxData = this.cropBoxData;\n    var data;\n    if (this.ready && this.cropped) {\n      data = {\n        left: cropBoxData.left,\n        top: cropBoxData.top,\n        width: cropBoxData.width,\n        height: cropBoxData.height\n      };\n    }\n    return data || {};\n  },\n  /**\n   * Set the crop box position and size with new data.\n   * @param {Object} data - The new crop box data.\n   * @returns {Cropper} this\n   */\n  setCropBoxData: function setCropBoxData(data) {\n    var cropBoxData = this.cropBoxData;\n    var aspectRatio = this.options.aspectRatio;\n    var widthChanged;\n    var heightChanged;\n    if (this.ready && this.cropped && !this.disabled && isPlainObject(data)) {\n      if (isNumber(data.left)) {\n        cropBoxData.left = data.left;\n      }\n      if (isNumber(data.top)) {\n        cropBoxData.top = data.top;\n      }\n      if (isNumber(data.width) && data.width !== cropBoxData.width) {\n        widthChanged = true;\n        cropBoxData.width = data.width;\n      }\n      if (isNumber(data.height) && data.height !== cropBoxData.height) {\n        heightChanged = true;\n        cropBoxData.height = data.height;\n      }\n      if (aspectRatio) {\n        if (widthChanged) {\n          cropBoxData.height = cropBoxData.width / aspectRatio;\n        } else if (heightChanged) {\n          cropBoxData.width = cropBoxData.height * aspectRatio;\n        }\n      }\n      this.renderCropBox();\n    }\n    return this;\n  },\n  /**\n   * Get a canvas drawn the cropped image.\n   * @param {Object} [options={}] - The config options.\n   * @returns {HTMLCanvasElement} - The result canvas.\n   */\n  getCroppedCanvas: function getCroppedCanvas() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (!this.ready || !window.HTMLCanvasElement) {\n      return null;\n    }\n    var canvasData = this.canvasData;\n    var source = getSourceCanvas(this.image, this.imageData, canvasData, options);\n\n    // Returns the source canvas if it is not cropped.\n    if (!this.cropped) {\n      return source;\n    }\n    var _this$getData = this.getData(options.rounded),\n      initialX = _this$getData.x,\n      initialY = _this$getData.y,\n      initialWidth = _this$getData.width,\n      initialHeight = _this$getData.height;\n    var ratio = source.width / Math.floor(canvasData.naturalWidth);\n    if (ratio !== 1) {\n      initialX *= ratio;\n      initialY *= ratio;\n      initialWidth *= ratio;\n      initialHeight *= ratio;\n    }\n    var aspectRatio = initialWidth / initialHeight;\n    var maxSizes = getAdjustedSizes({\n      aspectRatio: aspectRatio,\n      width: options.maxWidth || Infinity,\n      height: options.maxHeight || Infinity\n    });\n    var minSizes = getAdjustedSizes({\n      aspectRatio: aspectRatio,\n      width: options.minWidth || 0,\n      height: options.minHeight || 0\n    }, 'cover');\n    var _getAdjustedSizes = getAdjustedSizes({\n        aspectRatio: aspectRatio,\n        width: options.width || (ratio !== 1 ? source.width : initialWidth),\n        height: options.height || (ratio !== 1 ? source.height : initialHeight)\n      }),\n      width = _getAdjustedSizes.width,\n      height = _getAdjustedSizes.height;\n    width = Math.min(maxSizes.width, Math.max(minSizes.width, width));\n    height = Math.min(maxSizes.height, Math.max(minSizes.height, height));\n    var canvas = document.createElement('canvas');\n    var context = canvas.getContext('2d');\n    canvas.width = normalizeDecimalNumber(width);\n    canvas.height = normalizeDecimalNumber(height);\n    context.fillStyle = options.fillColor || 'transparent';\n    context.fillRect(0, 0, width, height);\n    var _options$imageSmoothi = options.imageSmoothingEnabled,\n      imageSmoothingEnabled = _options$imageSmoothi === void 0 ? true : _options$imageSmoothi,\n      imageSmoothingQuality = options.imageSmoothingQuality;\n    context.imageSmoothingEnabled = imageSmoothingEnabled;\n    if (imageSmoothingQuality) {\n      context.imageSmoothingQuality = imageSmoothingQuality;\n    }\n\n    // https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D.drawImage\n    var sourceWidth = source.width;\n    var sourceHeight = source.height;\n\n    // Source canvas parameters\n    var srcX = initialX;\n    var srcY = initialY;\n    var srcWidth;\n    var srcHeight;\n\n    // Destination canvas parameters\n    var dstX;\n    var dstY;\n    var dstWidth;\n    var dstHeight;\n    if (srcX <= -initialWidth || srcX > sourceWidth) {\n      srcX = 0;\n      srcWidth = 0;\n      dstX = 0;\n      dstWidth = 0;\n    } else if (srcX <= 0) {\n      dstX = -srcX;\n      srcX = 0;\n      srcWidth = Math.min(sourceWidth, initialWidth + srcX);\n      dstWidth = srcWidth;\n    } else if (srcX <= sourceWidth) {\n      dstX = 0;\n      srcWidth = Math.min(initialWidth, sourceWidth - srcX);\n      dstWidth = srcWidth;\n    }\n    if (srcWidth <= 0 || srcY <= -initialHeight || srcY > sourceHeight) {\n      srcY = 0;\n      srcHeight = 0;\n      dstY = 0;\n      dstHeight = 0;\n    } else if (srcY <= 0) {\n      dstY = -srcY;\n      srcY = 0;\n      srcHeight = Math.min(sourceHeight, initialHeight + srcY);\n      dstHeight = srcHeight;\n    } else if (srcY <= sourceHeight) {\n      dstY = 0;\n      srcHeight = Math.min(initialHeight, sourceHeight - srcY);\n      dstHeight = srcHeight;\n    }\n    var params = [srcX, srcY, srcWidth, srcHeight];\n\n    // Avoid \"IndexSizeError\"\n    if (dstWidth > 0 && dstHeight > 0) {\n      var scale = width / initialWidth;\n      params.push(dstX * scale, dstY * scale, dstWidth * scale, dstHeight * scale);\n    }\n\n    // All the numerical parameters should be integer for `drawImage`\n    // https://github.com/fengyuanchen/cropper/issues/476\n    context.drawImage.apply(context, [source].concat(_toConsumableArray(params.map(function (param) {\n      return Math.floor(normalizeDecimalNumber(param));\n    }))));\n    return canvas;\n  },\n  /**\n   * Change the aspect ratio of the crop box.\n   * @param {number} aspectRatio - The new aspect ratio.\n   * @returns {Cropper} this\n   */\n  setAspectRatio: function setAspectRatio(aspectRatio) {\n    var options = this.options;\n    if (!this.disabled && !isUndefined(aspectRatio)) {\n      // 0 -> NaN\n      options.aspectRatio = Math.max(0, aspectRatio) || NaN;\n      if (this.ready) {\n        this.initCropBox();\n        if (this.cropped) {\n          this.renderCropBox();\n        }\n      }\n    }\n    return this;\n  },\n  /**\n   * Change the drag mode.\n   * @param {string} mode - The new drag mode.\n   * @returns {Cropper} this\n   */\n  setDragMode: function setDragMode(mode) {\n    var options = this.options,\n      dragBox = this.dragBox,\n      face = this.face;\n    if (this.ready && !this.disabled) {\n      var croppable = mode === DRAG_MODE_CROP;\n      var movable = options.movable && mode === DRAG_MODE_MOVE;\n      mode = croppable || movable ? mode : DRAG_MODE_NONE;\n      options.dragMode = mode;\n      setData(dragBox, DATA_ACTION, mode);\n      toggleClass(dragBox, CLASS_CROP, croppable);\n      toggleClass(dragBox, CLASS_MOVE, movable);\n      if (!options.cropBoxMovable) {\n        // Sync drag mode to crop box when it is not movable\n        setData(face, DATA_ACTION, mode);\n        toggleClass(face, CLASS_CROP, croppable);\n        toggleClass(face, CLASS_MOVE, movable);\n      }\n    }\n    return this;\n  }\n};\n\nvar AnotherCropper = WINDOW.Cropper;\nvar Cropper = /*#__PURE__*/function () {\n  /**\n   * Create a new Cropper.\n   * @param {Element} element - The target element for cropping.\n   * @param {Object} [options={}] - The configuration options.\n   */\n  function Cropper(element) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    _classCallCheck(this, Cropper);\n    if (!element || !REGEXP_TAG_NAME.test(element.tagName)) {\n      throw new Error('The first argument is required and must be an <img> or <canvas> element.');\n    }\n    this.element = element;\n    this.options = assign({}, DEFAULTS, isPlainObject(options) && options);\n    this.cropped = false;\n    this.disabled = false;\n    this.pointers = {};\n    this.ready = false;\n    this.reloading = false;\n    this.replaced = false;\n    this.sized = false;\n    this.sizing = false;\n    this.init();\n  }\n  return _createClass(Cropper, [{\n    key: \"init\",\n    value: function init() {\n      var element = this.element;\n      var tagName = element.tagName.toLowerCase();\n      var url;\n      if (element[NAMESPACE]) {\n        return;\n      }\n      element[NAMESPACE] = this;\n      if (tagName === 'img') {\n        this.isImg = true;\n\n        // e.g.: \"img/picture.jpg\"\n        url = element.getAttribute('src') || '';\n        this.originalUrl = url;\n\n        // Stop when it's a blank image\n        if (!url) {\n          return;\n        }\n\n        // e.g.: \"https://example.com/img/picture.jpg\"\n        url = element.src;\n      } else if (tagName === 'canvas' && window.HTMLCanvasElement) {\n        url = element.toDataURL();\n      }\n      this.load(url);\n    }\n  }, {\n    key: \"load\",\n    value: function load(url) {\n      var _this = this;\n      if (!url) {\n        return;\n      }\n      this.url = url;\n      this.imageData = {};\n      var element = this.element,\n        options = this.options;\n      if (!options.rotatable && !options.scalable) {\n        options.checkOrientation = false;\n      }\n\n      // Only IE10+ supports Typed Arrays\n      if (!options.checkOrientation || !window.ArrayBuffer) {\n        this.clone();\n        return;\n      }\n\n      // Detect the mime type of the image directly if it is a Data URL\n      if (REGEXP_DATA_URL.test(url)) {\n        // Read ArrayBuffer from Data URL of JPEG images directly for better performance\n        if (REGEXP_DATA_URL_JPEG.test(url)) {\n          this.read(dataURLToArrayBuffer(url));\n        } else {\n          // Only a JPEG image may contains Exif Orientation information,\n          // the rest types of Data URLs are not necessary to check orientation at all.\n          this.clone();\n        }\n        return;\n      }\n\n      // 1. Detect the mime type of the image by a XMLHttpRequest.\n      // 2. Load the image as ArrayBuffer for reading orientation if its a JPEG image.\n      var xhr = new XMLHttpRequest();\n      var clone = this.clone.bind(this);\n      this.reloading = true;\n      this.xhr = xhr;\n\n      // 1. Cross origin requests are only supported for protocol schemes:\n      // http, https, data, chrome, chrome-extension.\n      // 2. Access to XMLHttpRequest from a Data URL will be blocked by CORS policy\n      // in some browsers as IE11 and Safari.\n      xhr.onabort = clone;\n      xhr.onerror = clone;\n      xhr.ontimeout = clone;\n      xhr.onprogress = function () {\n        // Abort the request directly if it not a JPEG image for better performance\n        if (xhr.getResponseHeader('content-type') !== MIME_TYPE_JPEG) {\n          xhr.abort();\n        }\n      };\n      xhr.onload = function () {\n        _this.read(xhr.response);\n      };\n      xhr.onloadend = function () {\n        _this.reloading = false;\n        _this.xhr = null;\n      };\n\n      // Bust cache when there is a \"crossOrigin\" property to avoid browser cache error\n      if (options.checkCrossOrigin && isCrossOriginURL(url) && element.crossOrigin) {\n        url = addTimestamp(url);\n      }\n\n      // The third parameter is required for avoiding side-effect (#682)\n      xhr.open('GET', url, true);\n      xhr.responseType = 'arraybuffer';\n      xhr.withCredentials = element.crossOrigin === 'use-credentials';\n      xhr.send();\n    }\n  }, {\n    key: \"read\",\n    value: function read(arrayBuffer) {\n      var options = this.options,\n        imageData = this.imageData;\n\n      // Reset the orientation value to its default value 1\n      // as some iOS browsers will render image with its orientation\n      var orientation = resetAndGetOrientation(arrayBuffer);\n      var rotate = 0;\n      var scaleX = 1;\n      var scaleY = 1;\n      if (orientation > 1) {\n        // Generate a new URL which has the default orientation value\n        this.url = arrayBufferToDataURL(arrayBuffer, MIME_TYPE_JPEG);\n        var _parseOrientation = parseOrientation(orientation);\n        rotate = _parseOrientation.rotate;\n        scaleX = _parseOrientation.scaleX;\n        scaleY = _parseOrientation.scaleY;\n      }\n      if (options.rotatable) {\n        imageData.rotate = rotate;\n      }\n      if (options.scalable) {\n        imageData.scaleX = scaleX;\n        imageData.scaleY = scaleY;\n      }\n      this.clone();\n    }\n  }, {\n    key: \"clone\",\n    value: function clone() {\n      var element = this.element,\n        url = this.url;\n      var crossOrigin = element.crossOrigin;\n      var crossOriginUrl = url;\n      if (this.options.checkCrossOrigin && isCrossOriginURL(url)) {\n        if (!crossOrigin) {\n          crossOrigin = 'anonymous';\n        }\n\n        // Bust cache when there is not a \"crossOrigin\" property (#519)\n        crossOriginUrl = addTimestamp(url);\n      }\n      this.crossOrigin = crossOrigin;\n      this.crossOriginUrl = crossOriginUrl;\n      var image = document.createElement('img');\n      if (crossOrigin) {\n        image.crossOrigin = crossOrigin;\n      }\n      image.src = crossOriginUrl || url;\n      image.alt = element.alt || 'The image to crop';\n      this.image = image;\n      image.onload = this.start.bind(this);\n      image.onerror = this.stop.bind(this);\n      addClass(image, CLASS_HIDE);\n      element.parentNode.insertBefore(image, element.nextSibling);\n    }\n  }, {\n    key: \"start\",\n    value: function start() {\n      var _this2 = this;\n      var image = this.image;\n      image.onload = null;\n      image.onerror = null;\n      this.sizing = true;\n\n      // Match all browsers that use WebKit as the layout engine in iOS devices,\n      // such as Safari for iOS, Chrome for iOS, and in-app browsers.\n      var isIOSWebKit = WINDOW.navigator && /(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(WINDOW.navigator.userAgent);\n      var done = function done(naturalWidth, naturalHeight) {\n        assign(_this2.imageData, {\n          naturalWidth: naturalWidth,\n          naturalHeight: naturalHeight,\n          aspectRatio: naturalWidth / naturalHeight\n        });\n        _this2.initialImageData = assign({}, _this2.imageData);\n        _this2.sizing = false;\n        _this2.sized = true;\n        _this2.build();\n      };\n\n      // Most modern browsers (excepts iOS WebKit)\n      if (image.naturalWidth && !isIOSWebKit) {\n        done(image.naturalWidth, image.naturalHeight);\n        return;\n      }\n      var sizingImage = document.createElement('img');\n      var body = document.body || document.documentElement;\n      this.sizingImage = sizingImage;\n      sizingImage.onload = function () {\n        done(sizingImage.width, sizingImage.height);\n        if (!isIOSWebKit) {\n          body.removeChild(sizingImage);\n        }\n      };\n      sizingImage.src = image.src;\n\n      // iOS WebKit will convert the image automatically\n      // with its orientation once append it into DOM (#279)\n      if (!isIOSWebKit) {\n        sizingImage.style.cssText = 'left:0;' + 'max-height:none!important;' + 'max-width:none!important;' + 'min-height:0!important;' + 'min-width:0!important;' + 'opacity:0;' + 'position:absolute;' + 'top:0;' + 'z-index:-1;';\n        body.appendChild(sizingImage);\n      }\n    }\n  }, {\n    key: \"stop\",\n    value: function stop() {\n      var image = this.image;\n      image.onload = null;\n      image.onerror = null;\n      image.parentNode.removeChild(image);\n      this.image = null;\n    }\n  }, {\n    key: \"build\",\n    value: function build() {\n      if (!this.sized || this.ready) {\n        return;\n      }\n      var element = this.element,\n        options = this.options,\n        image = this.image;\n\n      // Create cropper elements\n      var container = element.parentNode;\n      var template = document.createElement('div');\n      template.innerHTML = TEMPLATE;\n      var cropper = template.querySelector(\".\".concat(NAMESPACE, \"-container\"));\n      var canvas = cropper.querySelector(\".\".concat(NAMESPACE, \"-canvas\"));\n      var dragBox = cropper.querySelector(\".\".concat(NAMESPACE, \"-drag-box\"));\n      var cropBox = cropper.querySelector(\".\".concat(NAMESPACE, \"-crop-box\"));\n      var face = cropBox.querySelector(\".\".concat(NAMESPACE, \"-face\"));\n      this.container = container;\n      this.cropper = cropper;\n      this.canvas = canvas;\n      this.dragBox = dragBox;\n      this.cropBox = cropBox;\n      this.viewBox = cropper.querySelector(\".\".concat(NAMESPACE, \"-view-box\"));\n      this.face = face;\n      canvas.appendChild(image);\n\n      // Hide the original image\n      addClass(element, CLASS_HIDDEN);\n\n      // Inserts the cropper after to the current image\n      container.insertBefore(cropper, element.nextSibling);\n\n      // Show the hidden image\n      removeClass(image, CLASS_HIDE);\n      this.initPreview();\n      this.bind();\n      options.initialAspectRatio = Math.max(0, options.initialAspectRatio) || NaN;\n      options.aspectRatio = Math.max(0, options.aspectRatio) || NaN;\n      options.viewMode = Math.max(0, Math.min(3, Math.round(options.viewMode))) || 0;\n      addClass(cropBox, CLASS_HIDDEN);\n      if (!options.guides) {\n        addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-dashed\")), CLASS_HIDDEN);\n      }\n      if (!options.center) {\n        addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-center\")), CLASS_HIDDEN);\n      }\n      if (options.background) {\n        addClass(cropper, \"\".concat(NAMESPACE, \"-bg\"));\n      }\n      if (!options.highlight) {\n        addClass(face, CLASS_INVISIBLE);\n      }\n      if (options.cropBoxMovable) {\n        addClass(face, CLASS_MOVE);\n        setData(face, DATA_ACTION, ACTION_ALL);\n      }\n      if (!options.cropBoxResizable) {\n        addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-line\")), CLASS_HIDDEN);\n        addClass(cropBox.getElementsByClassName(\"\".concat(NAMESPACE, \"-point\")), CLASS_HIDDEN);\n      }\n      this.render();\n      this.ready = true;\n      this.setDragMode(options.dragMode);\n      if (options.autoCrop) {\n        this.crop();\n      }\n      this.setData(options.data);\n      if (isFunction(options.ready)) {\n        addListener(element, EVENT_READY, options.ready, {\n          once: true\n        });\n      }\n      dispatchEvent(element, EVENT_READY);\n    }\n  }, {\n    key: \"unbuild\",\n    value: function unbuild() {\n      if (!this.ready) {\n        return;\n      }\n      this.ready = false;\n      this.unbind();\n      this.resetPreview();\n      var parentNode = this.cropper.parentNode;\n      if (parentNode) {\n        parentNode.removeChild(this.cropper);\n      }\n      removeClass(this.element, CLASS_HIDDEN);\n    }\n  }, {\n    key: \"uncreate\",\n    value: function uncreate() {\n      if (this.ready) {\n        this.unbuild();\n        this.ready = false;\n        this.cropped = false;\n      } else if (this.sizing) {\n        this.sizingImage.onload = null;\n        this.sizing = false;\n        this.sized = false;\n      } else if (this.reloading) {\n        this.xhr.onabort = null;\n        this.xhr.abort();\n      } else if (this.image) {\n        this.stop();\n      }\n    }\n\n    /**\n     * Get the no conflict cropper class.\n     * @returns {Cropper} The cropper class.\n     */\n  }], [{\n    key: \"noConflict\",\n    value: function noConflict() {\n      window.Cropper = AnotherCropper;\n      return Cropper;\n    }\n\n    /**\n     * Change the default options.\n     * @param {Object} options - The new default options.\n     */\n  }, {\n    key: \"setDefaults\",\n    value: function setDefaults(options) {\n      assign(DEFAULTS, isPlainObject(options) && options);\n    }\n  }]);\n}();\nassign(Cropper.prototype, render, preview, events, handlers, change, methods);\n\nexport { Cropper as default };\n"], "mappings": ";AAUA,SAAS,QAAQ,GAAG,GAAG;AACrB,MAAI,IAAI,OAAO,KAAK,CAAC;AACrB,MAAI,OAAO,uBAAuB;AAChC,QAAI,IAAI,OAAO,sBAAsB,CAAC;AACtC,UAAM,IAAI,EAAE,OAAO,SAAUA,IAAG;AAC9B,aAAO,OAAO,yBAAyB,GAAGA,EAAC,EAAE;AAAA,IAC/C,CAAC,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,EACxB;AACA,SAAO;AACT;AACA,SAAS,eAAe,GAAG;AACzB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,IAAI,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AAC/C,QAAI,IAAI,QAAQ,OAAO,CAAC,GAAG,IAAE,EAAE,QAAQ,SAAUA,IAAG;AAClD,sBAAgB,GAAGA,IAAG,EAAEA,EAAC,CAAC;AAAA,IAC5B,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,CAAC,CAAC,IAAI,QAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAUA,IAAG;AAChJ,aAAO,eAAe,GAAGA,IAAG,OAAO,yBAAyB,GAAGA,EAAC,CAAC;AAAA,IACnE,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,aAAa,GAAG,GAAG;AAC1B,MAAI,YAAY,OAAO,KAAK,CAAC;AAAG,WAAO;AACvC,MAAI,IAAI,EAAE,OAAO,WAAW;AAC5B,MAAI,WAAW,GAAG;AAChB,QAAI,IAAI,EAAE,KAAK,GAAG,KAAK,SAAS;AAChC,QAAI,YAAY,OAAO;AAAG,aAAO;AACjC,UAAM,IAAI,UAAU,8CAA8C;AAAA,EACpE;AACA,UAAQ,aAAa,IAAI,SAAS,QAAQ,CAAC;AAC7C;AACA,SAAS,eAAe,GAAG;AACzB,MAAI,IAAI,aAAa,GAAG,QAAQ;AAChC,SAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AACxC;AACA,SAAS,QAAQ,GAAG;AAClB;AAEA,SAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,IAAG;AAChG,WAAO,OAAOA;AAAA,EAChB,IAAI,SAAUA,IAAG;AACf,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EACpH,GAAG,QAAQ,CAAC;AACd;AACA,SAAS,gBAAgB,UAAU,aAAa;AAC9C,MAAI,EAAE,oBAAoB,cAAc;AACtC,UAAM,IAAI,UAAU,mCAAmC;AAAA,EACzD;AACF;AACA,SAAS,kBAAkB,QAAQ,OAAO;AACxC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,aAAa,MAAM,CAAC;AACxB,eAAW,aAAa,WAAW,cAAc;AACjD,eAAW,eAAe;AAC1B,QAAI,WAAW;AAAY,iBAAW,WAAW;AACjD,WAAO,eAAe,QAAQ,eAAe,WAAW,GAAG,GAAG,UAAU;AAAA,EAC1E;AACF;AACA,SAAS,aAAa,aAAa,YAAY,aAAa;AAC1D,MAAI;AAAY,sBAAkB,YAAY,WAAW,UAAU;AACnE,MAAI;AAAa,sBAAkB,aAAa,WAAW;AAC3D,SAAO,eAAe,aAAa,aAAa;AAAA,IAC9C,UAAU;AAAA,EACZ,CAAC;AACD,SAAO;AACT;AACA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,QAAM,eAAe,GAAG;AACxB,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,KAAK;AAC/B,SAAO,mBAAmB,GAAG,KAAK,iBAAiB,GAAG,KAAK,4BAA4B,GAAG,KAAK,mBAAmB;AACpH;AACA,SAAS,mBAAmB,KAAK;AAC/B,MAAI,MAAM,QAAQ,GAAG;AAAG,WAAO,kBAAkB,GAAG;AACtD;AACA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,OAAO,WAAW,eAAe,KAAK,OAAO,QAAQ,KAAK,QAAQ,KAAK,YAAY,KAAK;AAAM,WAAO,MAAM,KAAK,IAAI;AAC1H;AACA,SAAS,4BAA4B,GAAG,QAAQ;AAC9C,MAAI,CAAC;AAAG;AACR,MAAI,OAAO,MAAM;AAAU,WAAO,kBAAkB,GAAG,MAAM;AAC7D,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,MAAI,MAAM,YAAY,EAAE;AAAa,QAAI,EAAE,YAAY;AACvD,MAAI,MAAM,SAAS,MAAM;AAAO,WAAO,MAAM,KAAK,CAAC;AACnD,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC;AAAG,WAAO,kBAAkB,GAAG,MAAM;AACjH;AACA,SAAS,kBAAkB,KAAK,KAAK;AACnC,MAAI,OAAO,QAAQ,MAAM,IAAI;AAAQ,UAAM,IAAI;AAC/C,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK;AAAK,SAAK,CAAC,IAAI,IAAI,CAAC;AACpE,SAAO;AACT;AACA,SAAS,qBAAqB;AAC5B,QAAM,IAAI,UAAU,sIAAsI;AAC5J;AAEA,IAAI,aAAa,OAAO,WAAW,eAAe,OAAO,OAAO,aAAa;AAC7E,IAAI,SAAS,aAAa,SAAS,CAAC;AACpC,IAAI,kBAAkB,cAAc,OAAO,SAAS,kBAAkB,kBAAkB,OAAO,SAAS,kBAAkB;AAC1H,IAAI,oBAAoB,aAAa,kBAAkB,SAAS;AAChE,IAAI,YAAY;AAGhB,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,IAAI,cAAc;AAClB,IAAI,cAAc;AAClB,IAAI,cAAc;AAClB,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAI,oBAAoB;AACxB,IAAI,oBAAoB;AACxB,IAAI,oBAAoB;AACxB,IAAI,oBAAoB;AAGxB,IAAI,aAAa,GAAG,OAAO,WAAW,OAAO;AAC7C,IAAI,iBAAiB,GAAG,OAAO,WAAW,WAAW;AACrD,IAAI,eAAe,GAAG,OAAO,WAAW,SAAS;AACjD,IAAI,aAAa,GAAG,OAAO,WAAW,OAAO;AAC7C,IAAI,kBAAkB,GAAG,OAAO,WAAW,YAAY;AACvD,IAAI,cAAc,GAAG,OAAO,WAAW,QAAQ;AAC/C,IAAI,aAAa,GAAG,OAAO,WAAW,OAAO;AAG7C,IAAI,cAAc,GAAG,OAAO,WAAW,QAAQ;AAC/C,IAAI,eAAe,GAAG,OAAO,WAAW,SAAS;AAGjD,IAAI,iBAAiB;AACrB,IAAI,iBAAiB;AACrB,IAAI,iBAAiB;AAGrB,IAAI,aAAa;AACjB,IAAI,iBAAiB;AACrB,IAAI,kBAAkB;AACtB,IAAI,mBAAmB;AACvB,IAAI,iBAAiB;AACrB,IAAI,oBAAoB,kBAAkB,eAAe;AACzD,IAAI,mBAAmB,kBAAkB,cAAc;AACvD,IAAI,kBAAkB,kBAAkB,yBAAyB;AACjE,IAAI,qBAAqB,oBAAoB,gBAAgB;AAC7D,IAAI,qBAAqB,oBAAoB,gBAAgB;AAC7D,IAAI,mBAAmB,oBAAoB,4BAA4B;AACvE,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,cAAc;AAClB,IAAI,aAAa;AAGjB,IAAI,iBAAiB;AAGrB,IAAI,iBAAiB;AACrB,IAAI,kBAAkB;AACtB,IAAI,uBAAuB;AAC3B,IAAI,kBAAkB;AAItB,IAAI,sBAAsB;AAC1B,IAAI,uBAAuB;AAE3B,IAAI,WAAW;AAAA;AAAA,EAEb,UAAU;AAAA;AAAA;AAAA,EAIV,UAAU;AAAA;AAAA;AAAA,EAIV,oBAAoB;AAAA;AAAA,EAEpB,aAAa;AAAA;AAAA,EAEb,MAAM;AAAA;AAAA,EAEN,SAAS;AAAA;AAAA,EAET,YAAY;AAAA;AAAA,EAEZ,SAAS;AAAA;AAAA,EAET,kBAAkB;AAAA;AAAA,EAElB,kBAAkB;AAAA;AAAA,EAElB,OAAO;AAAA;AAAA,EAEP,QAAQ;AAAA;AAAA,EAER,QAAQ;AAAA;AAAA,EAER,WAAW;AAAA;AAAA,EAEX,YAAY;AAAA;AAAA,EAEZ,UAAU;AAAA;AAAA,EAEV,cAAc;AAAA;AAAA,EAEd,SAAS;AAAA;AAAA,EAET,WAAW;AAAA;AAAA,EAEX,UAAU;AAAA;AAAA,EAEV,UAAU;AAAA;AAAA,EAEV,aAAa;AAAA;AAAA,EAEb,aAAa;AAAA;AAAA,EAEb,gBAAgB;AAAA;AAAA,EAEhB,gBAAgB;AAAA;AAAA,EAEhB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,OAAO;AAAA,EACP,WAAW;AAAA,EACX,UAAU;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,EACN,MAAM;AACR;AAEA,IAAI,WAAW;AAKf,IAAI,QAAQ,OAAO,SAAS,OAAO;AAOnC,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,UAAU,YAAY,CAAC,MAAM,KAAK;AAClD;AAOA,IAAI,mBAAmB,SAASC,kBAAiB,OAAO;AACtD,SAAO,QAAQ,KAAK,QAAQ;AAC9B;AAOA,SAAS,YAAY,OAAO;AAC1B,SAAO,OAAO,UAAU;AAC1B;AAOA,SAAS,SAAS,OAAO;AACvB,SAAO,QAAQ,KAAK,MAAM,YAAY,UAAU;AAClD;AACA,IAAI,iBAAiB,OAAO,UAAU;AAOtC,SAAS,cAAc,OAAO;AAC5B,MAAI,CAAC,SAAS,KAAK,GAAG;AACpB,WAAO;AAAA,EACT;AACA,MAAI;AACF,QAAI,eAAe,MAAM;AACzB,QAAI,YAAY,aAAa;AAC7B,WAAO,gBAAgB,aAAa,eAAe,KAAK,WAAW,eAAe;AAAA,EACpF,SAAS,OAAO;AACd,WAAO;AAAA,EACT;AACF;AAOA,SAAS,WAAW,OAAO;AACzB,SAAO,OAAO,UAAU;AAC1B;AACA,IAAI,QAAQ,MAAM,UAAU;AAO5B,SAAS,QAAQ,OAAO;AACtB,SAAO,MAAM,OAAO,MAAM,KAAK,KAAK,IAAI,MAAM,KAAK,KAAK;AAC1D;AAQA,SAAS,QAAQ,MAAM,UAAU;AAC/B,MAAI,QAAQ,WAAW,QAAQ,GAAG;AAChC,QAAI,MAAM,QAAQ,IAAI,KAAK,SAAS,KAAK,MAAM,GAAoB;AACjE,cAAQ,IAAI,EAAE,QAAQ,SAAU,OAAO,KAAK;AAC1C,iBAAS,KAAK,MAAM,OAAO,KAAK,IAAI;AAAA,MACtC,CAAC;AAAA,IACH,WAAW,SAAS,IAAI,GAAG;AACzB,aAAO,KAAK,IAAI,EAAE,QAAQ,SAAU,KAAK;AACvC,iBAAS,KAAK,MAAM,KAAK,GAAG,GAAG,KAAK,IAAI;AAAA,MAC1C,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO;AACT;AAQA,IAAI,SAAS,OAAO,UAAU,SAASC,QAAO,QAAQ;AACpD,WAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,SAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,EACjC;AACA,MAAI,SAAS,MAAM,KAAK,KAAK,SAAS,GAAG;AACvC,SAAK,QAAQ,SAAU,KAAK;AAC1B,UAAI,SAAS,GAAG,GAAG;AACjB,eAAO,KAAK,GAAG,EAAE,QAAQ,SAAU,KAAK;AACtC,iBAAO,GAAG,IAAI,IAAI,GAAG;AAAA,QACvB,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,IAAI,kBAAkB;AAStB,SAAS,uBAAuB,OAAO;AACrC,MAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAChF,SAAO,gBAAgB,KAAK,KAAK,IAAI,KAAK,MAAM,QAAQ,KAAK,IAAI,QAAQ;AAC3E;AACA,IAAI,gBAAgB;AAOpB,SAAS,SAAS,SAAS,QAAQ;AACjC,MAAI,QAAQ,QAAQ;AACpB,UAAQ,QAAQ,SAAU,OAAO,UAAU;AACzC,QAAI,cAAc,KAAK,QAAQ,KAAK,SAAS,KAAK,GAAG;AACnD,cAAQ,GAAG,OAAO,OAAO,IAAI;AAAA,IAC/B;AACA,UAAM,QAAQ,IAAI;AAAA,EACpB,CAAC;AACH;AAQA,SAAS,SAAS,SAAS,OAAO;AAChC,SAAO,QAAQ,YAAY,QAAQ,UAAU,SAAS,KAAK,IAAI,QAAQ,UAAU,QAAQ,KAAK,IAAI;AACpG;AAOA,SAAS,SAAS,SAAS,OAAO;AAChC,MAAI,CAAC,OAAO;AACV;AAAA,EACF;AACA,MAAI,SAAS,QAAQ,MAAM,GAAG;AAC5B,YAAQ,SAAS,SAAU,MAAM;AAC/B,eAAS,MAAM,KAAK;AAAA,IACtB,CAAC;AACD;AAAA,EACF;AACA,MAAI,QAAQ,WAAW;AACrB,YAAQ,UAAU,IAAI,KAAK;AAC3B;AAAA,EACF;AACA,MAAI,YAAY,QAAQ,UAAU,KAAK;AACvC,MAAI,CAAC,WAAW;AACd,YAAQ,YAAY;AAAA,EACtB,WAAW,UAAU,QAAQ,KAAK,IAAI,GAAG;AACvC,YAAQ,YAAY,GAAG,OAAO,WAAW,GAAG,EAAE,OAAO,KAAK;AAAA,EAC5D;AACF;AAOA,SAAS,YAAY,SAAS,OAAO;AACnC,MAAI,CAAC,OAAO;AACV;AAAA,EACF;AACA,MAAI,SAAS,QAAQ,MAAM,GAAG;AAC5B,YAAQ,SAAS,SAAU,MAAM;AAC/B,kBAAY,MAAM,KAAK;AAAA,IACzB,CAAC;AACD;AAAA,EACF;AACA,MAAI,QAAQ,WAAW;AACrB,YAAQ,UAAU,OAAO,KAAK;AAC9B;AAAA,EACF;AACA,MAAI,QAAQ,UAAU,QAAQ,KAAK,KAAK,GAAG;AACzC,YAAQ,YAAY,QAAQ,UAAU,QAAQ,OAAO,EAAE;AAAA,EACzD;AACF;AAQA,SAAS,YAAY,SAAS,OAAO,OAAO;AAC1C,MAAI,CAAC,OAAO;AACV;AAAA,EACF;AACA,MAAI,SAAS,QAAQ,MAAM,GAAG;AAC5B,YAAQ,SAAS,SAAU,MAAM;AAC/B,kBAAY,MAAM,OAAO,KAAK;AAAA,IAChC,CAAC;AACD;AAAA,EACF;AAGA,MAAI,OAAO;AACT,aAAS,SAAS,KAAK;AAAA,EACzB,OAAO;AACL,gBAAY,SAAS,KAAK;AAAA,EAC5B;AACF;AACA,IAAI,oBAAoB;AAOxB,SAAS,YAAY,OAAO;AAC1B,SAAO,MAAM,QAAQ,mBAAmB,OAAO,EAAE,YAAY;AAC/D;AAQA,SAAS,QAAQ,SAAS,MAAM;AAC9B,MAAI,SAAS,QAAQ,IAAI,CAAC,GAAG;AAC3B,WAAO,QAAQ,IAAI;AAAA,EACrB;AACA,MAAI,QAAQ,SAAS;AACnB,WAAO,QAAQ,QAAQ,IAAI;AAAA,EAC7B;AACA,SAAO,QAAQ,aAAa,QAAQ,OAAO,YAAY,IAAI,CAAC,CAAC;AAC/D;AAQA,SAAS,QAAQ,SAAS,MAAM,MAAM;AACpC,MAAI,SAAS,IAAI,GAAG;AAClB,YAAQ,IAAI,IAAI;AAAA,EAClB,WAAW,QAAQ,SAAS;AAC1B,YAAQ,QAAQ,IAAI,IAAI;AAAA,EAC1B,OAAO;AACL,YAAQ,aAAa,QAAQ,OAAO,YAAY,IAAI,CAAC,GAAG,IAAI;AAAA,EAC9D;AACF;AAOA,SAAS,WAAW,SAAS,MAAM;AACjC,MAAI,SAAS,QAAQ,IAAI,CAAC,GAAG;AAC3B,QAAI;AACF,aAAO,QAAQ,IAAI;AAAA,IACrB,SAAS,OAAO;AACd,cAAQ,IAAI,IAAI;AAAA,IAClB;AAAA,EACF,WAAW,QAAQ,SAAS;AAE1B,QAAI;AACF,aAAO,QAAQ,QAAQ,IAAI;AAAA,IAC7B,SAAS,OAAO;AACd,cAAQ,QAAQ,IAAI,IAAI;AAAA,IAC1B;AAAA,EACF,OAAO;AACL,YAAQ,gBAAgB,QAAQ,OAAO,YAAY,IAAI,CAAC,CAAC;AAAA,EAC3D;AACF;AACA,IAAI,gBAAgB;AACpB,IAAI,gBAAgB,WAAY;AAC9B,MAAI,YAAY;AAChB,MAAI,YAAY;AACd,QAAI,OAAO;AACX,QAAI,WAAW,SAASC,YAAW;AAAA,IAAC;AACpC,QAAI,UAAU,OAAO,eAAe,CAAC,GAAG,QAAQ;AAAA,MAC9C,KAAK,SAAS,MAAM;AAClB,oBAAY;AACZ,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,KAAK,SAAS,IAAI,OAAO;AACvB,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD,WAAO,iBAAiB,QAAQ,UAAU,OAAO;AACjD,WAAO,oBAAoB,QAAQ,UAAU,OAAO;AAAA,EACtD;AACA,SAAO;AACT,EAAE;AASF,SAAS,eAAe,SAAS,MAAM,UAAU;AAC/C,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,MAAI,UAAU;AACd,OAAK,KAAK,EAAE,MAAM,aAAa,EAAE,QAAQ,SAAU,OAAO;AACxD,QAAI,CAAC,eAAe;AAClB,UAAI,YAAY,QAAQ;AACxB,UAAI,aAAa,UAAU,KAAK,KAAK,UAAU,KAAK,EAAE,QAAQ,GAAG;AAC/D,kBAAU,UAAU,KAAK,EAAE,QAAQ;AACnC,eAAO,UAAU,KAAK,EAAE,QAAQ;AAChC,YAAI,OAAO,KAAK,UAAU,KAAK,CAAC,EAAE,WAAW,GAAG;AAC9C,iBAAO,UAAU,KAAK;AAAA,QACxB;AACA,YAAI,OAAO,KAAK,SAAS,EAAE,WAAW,GAAG;AACvC,iBAAO,QAAQ;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AACA,YAAQ,oBAAoB,OAAO,SAAS,OAAO;AAAA,EACrD,CAAC;AACH;AASA,SAAS,YAAY,SAAS,MAAM,UAAU;AAC5C,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,MAAI,WAAW;AACf,OAAK,KAAK,EAAE,MAAM,aAAa,EAAE,QAAQ,SAAU,OAAO;AACxD,QAAI,QAAQ,QAAQ,CAAC,eAAe;AAClC,UAAI,qBAAqB,QAAQ,WAC/B,YAAY,uBAAuB,SAAS,CAAC,IAAI;AACnD,iBAAW,SAAS,UAAU;AAC5B,eAAO,UAAU,KAAK,EAAE,QAAQ;AAChC,gBAAQ,oBAAoB,OAAO,UAAU,OAAO;AACpD,iBAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,eAAK,KAAK,IAAI,UAAU,KAAK;AAAA,QAC/B;AACA,iBAAS,MAAM,SAAS,IAAI;AAAA,MAC9B;AACA,UAAI,CAAC,UAAU,KAAK,GAAG;AACrB,kBAAU,KAAK,IAAI,CAAC;AAAA,MACtB;AACA,UAAI,UAAU,KAAK,EAAE,QAAQ,GAAG;AAC9B,gBAAQ,oBAAoB,OAAO,UAAU,KAAK,EAAE,QAAQ,GAAG,OAAO;AAAA,MACxE;AACA,gBAAU,KAAK,EAAE,QAAQ,IAAI;AAC7B,cAAQ,YAAY;AAAA,IACtB;AACA,YAAQ,iBAAiB,OAAO,UAAU,OAAO;AAAA,EACnD,CAAC;AACH;AASA,SAAS,cAAc,SAAS,MAAM,MAAM;AAC1C,MAAI;AAGJ,MAAI,WAAW,KAAK,KAAK,WAAW,WAAW,GAAG;AAChD,YAAQ,IAAI,YAAY,MAAM;AAAA,MAC5B,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,YAAY;AAAA,IACd,CAAC;AAAA,EACH,OAAO;AACL,YAAQ,SAAS,YAAY,aAAa;AAC1C,UAAM,gBAAgB,MAAM,MAAM,MAAM,IAAI;AAAA,EAC9C;AACA,SAAO,QAAQ,cAAc,KAAK;AACpC;AAOA,SAAS,UAAU,SAAS;AAC1B,MAAI,MAAM,QAAQ,sBAAsB;AACxC,SAAO;AAAA,IACL,MAAM,IAAI,QAAQ,OAAO,cAAc,SAAS,gBAAgB;AAAA,IAChE,KAAK,IAAI,OAAO,OAAO,cAAc,SAAS,gBAAgB;AAAA,EAChE;AACF;AACA,IAAI,WAAW,OAAO;AACtB,IAAI,iBAAiB;AAOrB,SAAS,iBAAiB,KAAK;AAC7B,MAAI,QAAQ,IAAI,MAAM,cAAc;AACpC,SAAO,UAAU,SAAS,MAAM,CAAC,MAAM,SAAS,YAAY,MAAM,CAAC,MAAM,SAAS,YAAY,MAAM,CAAC,MAAM,SAAS;AACtH;AAOA,SAAS,aAAa,KAAK;AACzB,MAAI,YAAY,aAAa,QAAO,oBAAI,KAAK,GAAE,QAAQ,CAAC;AACxD,SAAO,OAAO,IAAI,QAAQ,GAAG,MAAM,KAAK,MAAM,OAAO;AACvD;AAOA,SAAS,cAAc,MAAM;AAC3B,MAAIC,UAAS,KAAK,QAChBC,UAAS,KAAK,QACdC,UAAS,KAAK,QACd,aAAa,KAAK,YAClB,aAAa,KAAK;AACpB,MAAI,SAAS,CAAC;AACd,MAAI,SAAS,UAAU,KAAK,eAAe,GAAG;AAC5C,WAAO,KAAK,cAAc,OAAO,YAAY,KAAK,CAAC;AAAA,EACrD;AACA,MAAI,SAAS,UAAU,KAAK,eAAe,GAAG;AAC5C,WAAO,KAAK,cAAc,OAAO,YAAY,KAAK,CAAC;AAAA,EACrD;AAGA,MAAI,SAASF,OAAM,KAAKA,YAAW,GAAG;AACpC,WAAO,KAAK,UAAU,OAAOA,SAAQ,MAAM,CAAC;AAAA,EAC9C;AACA,MAAI,SAASC,OAAM,KAAKA,YAAW,GAAG;AACpC,WAAO,KAAK,UAAU,OAAOA,SAAQ,GAAG,CAAC;AAAA,EAC3C;AACA,MAAI,SAASC,OAAM,KAAKA,YAAW,GAAG;AACpC,WAAO,KAAK,UAAU,OAAOA,SAAQ,GAAG,CAAC;AAAA,EAC3C;AACA,MAAI,YAAY,OAAO,SAAS,OAAO,KAAK,GAAG,IAAI;AACnD,SAAO;AAAA,IACL,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb;AAAA,EACF;AACF;AAOA,SAAS,gBAAgB,UAAU;AACjC,MAAI,YAAY,eAAe,CAAC,GAAG,QAAQ;AAC3C,MAAI,WAAW;AACf,UAAQ,UAAU,SAAU,SAAS,WAAW;AAC9C,WAAO,UAAU,SAAS;AAC1B,YAAQ,WAAW,SAAU,UAAU;AACrC,UAAI,KAAK,KAAK,IAAI,QAAQ,SAAS,SAAS,MAAM;AAClD,UAAI,KAAK,KAAK,IAAI,QAAQ,SAAS,SAAS,MAAM;AAClD,UAAI,KAAK,KAAK,IAAI,QAAQ,OAAO,SAAS,IAAI;AAC9C,UAAI,KAAK,KAAK,IAAI,QAAQ,OAAO,SAAS,IAAI;AAC9C,UAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AACpC,UAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AACpC,UAAI,SAAS,KAAK,MAAM;AACxB,UAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,QAAQ,GAAG;AACxC,mBAAW;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,SAAO;AACT;AAQA,SAAS,WAAW,OAAO,SAAS;AAClC,MAAI,QAAQ,MAAM,OAChB,QAAQ,MAAM;AAChB,MAAI,MAAM;AAAA,IACR,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AACA,SAAO,UAAU,MAAM,eAAe;AAAA,IACpC,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV,GAAG,GAAG;AACR;AAOA,SAAS,kBAAkB,UAAU;AACnC,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,UAAQ,UAAU,SAAU,OAAO;AACjC,QAAI,SAAS,MAAM,QACjB,SAAS,MAAM;AACjB,aAAS;AACT,aAAS;AACT,aAAS;AAAA,EACX,CAAC;AACD,WAAS;AACT,WAAS;AACT,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAQA,SAAS,iBAAiB,OAAO;AAC/B,MAAI,cAAc,MAAM,aACtB,SAAS,MAAM,QACf,QAAQ,MAAM;AAChB,MAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAC/E,MAAI,eAAe,iBAAiB,KAAK;AACzC,MAAI,gBAAgB,iBAAiB,MAAM;AAC3C,MAAI,gBAAgB,eAAe;AACjC,QAAI,gBAAgB,SAAS;AAC7B,QAAI,SAAS,aAAa,gBAAgB,SAAS,SAAS,WAAW,gBAAgB,OAAO;AAC5F,eAAS,QAAQ;AAAA,IACnB,OAAO;AACL,cAAQ,SAAS;AAAA,IACnB;AAAA,EACF,WAAW,cAAc;AACvB,aAAS,QAAQ;AAAA,EACnB,WAAW,eAAe;AACxB,YAAQ,SAAS;AAAA,EACnB;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAOA,SAAS,gBAAgB,OAAO;AAC9B,MAAI,QAAQ,MAAM,OAChB,SAAS,MAAM,QACf,SAAS,MAAM;AACjB,WAAS,KAAK,IAAI,MAAM,IAAI;AAC5B,MAAI,WAAW,IAAI;AACjB,WAAO;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,EACF;AACA,MAAI,MAAM,SAAS,KAAK,KAAK,KAAK;AAClC,MAAI,SAAS,KAAK,IAAI,GAAG;AACzB,MAAI,SAAS,KAAK,IAAI,GAAG;AACzB,MAAI,WAAW,QAAQ,SAAS,SAAS;AACzC,MAAI,YAAY,QAAQ,SAAS,SAAS;AAC1C,SAAO,SAAS,KAAK;AAAA,IACnB,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,IAAI;AAAA,IACF,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AACF;AAUA,SAAS,gBAAgB,OAAO,OAAO,OAAO,OAAO;AACnD,MAAI,mBAAmB,MAAM,aAC3B,oBAAoB,MAAM,cAC1B,qBAAqB,MAAM,eAC3B,eAAe,MAAM,QACrBF,UAAS,iBAAiB,SAAS,IAAI,cACvC,eAAe,MAAM,QACrBC,UAAS,iBAAiB,SAAS,IAAI,cACvC,eAAe,MAAM,QACrBC,UAAS,iBAAiB,SAAS,IAAI;AACzC,MAAI,cAAc,MAAM,aACtB,eAAe,MAAM,cACrB,gBAAgB,MAAM;AACxB,MAAI,kBAAkB,MAAM,WAC1B,YAAY,oBAAoB,SAAS,gBAAgB,iBACzD,wBAAwB,MAAM,uBAC9B,wBAAwB,0BAA0B,SAAS,OAAO,uBAClE,wBAAwB,MAAM,uBAC9B,wBAAwB,0BAA0B,SAAS,QAAQ,uBACnE,iBAAiB,MAAM,UACvB,WAAW,mBAAmB,SAAS,WAAW,gBAClD,kBAAkB,MAAM,WACxB,YAAY,oBAAoB,SAAS,WAAW,iBACpD,iBAAiB,MAAM,UACvB,WAAW,mBAAmB,SAAS,IAAI,gBAC3C,kBAAkB,MAAM,WACxB,YAAY,oBAAoB,SAAS,IAAI;AAC/C,MAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,MAAI,UAAU,OAAO,WAAW,IAAI;AACpC,MAAI,WAAW,iBAAiB;AAAA,IAC9B;AAAA,IACA,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,CAAC;AACD,MAAI,WAAW,iBAAiB;AAAA,IAC9B;AAAA,IACA,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,GAAG,OAAO;AACV,MAAI,QAAQ,KAAK,IAAI,SAAS,OAAO,KAAK,IAAI,SAAS,OAAO,YAAY,CAAC;AAC3E,MAAI,SAAS,KAAK,IAAI,SAAS,QAAQ,KAAK,IAAI,SAAS,QAAQ,aAAa,CAAC;AAI/E,MAAI,eAAe,iBAAiB;AAAA,IAClC,aAAa;AAAA,IACb,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,CAAC;AACD,MAAI,eAAe,iBAAiB;AAAA,IAClC,aAAa;AAAA,IACb,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,GAAG,OAAO;AACV,MAAI,YAAY,KAAK,IAAI,aAAa,OAAO,KAAK,IAAI,aAAa,OAAO,iBAAiB,CAAC;AAC5F,MAAI,aAAa,KAAK,IAAI,aAAa,QAAQ,KAAK,IAAI,aAAa,QAAQ,kBAAkB,CAAC;AAChG,MAAI,SAAS,CAAC,CAAC,YAAY,GAAG,CAAC,aAAa,GAAG,WAAW,UAAU;AACpE,SAAO,QAAQ,uBAAuB,KAAK;AAC3C,SAAO,SAAS,uBAAuB,MAAM;AAC7C,UAAQ,YAAY;AACpB,UAAQ,SAAS,GAAG,GAAG,OAAO,MAAM;AACpC,UAAQ,KAAK;AACb,UAAQ,UAAU,QAAQ,GAAG,SAAS,CAAC;AACvC,UAAQ,OAAOF,UAAS,KAAK,KAAK,GAAG;AACrC,UAAQ,MAAMC,SAAQC,OAAM;AAC5B,UAAQ,wBAAwB;AAChC,UAAQ,wBAAwB;AAChC,UAAQ,UAAU,MAAM,SAAS,CAAC,KAAK,EAAE,OAAO,mBAAmB,OAAO,IAAI,SAAU,OAAO;AAC7F,WAAO,KAAK,MAAM,uBAAuB,KAAK,CAAC;AAAA,EACjD,CAAC,CAAC,CAAC,CAAC;AACJ,UAAQ,QAAQ;AAChB,SAAO;AACT;AACA,IAAI,eAAe,OAAO;AAS1B,SAAS,sBAAsB,UAAU,OAAO,QAAQ;AACtD,MAAI,MAAM;AACV,YAAU;AACV,WAAS,IAAI,OAAO,IAAI,QAAQ,KAAK,GAAG;AACtC,WAAO,aAAa,SAAS,SAAS,CAAC,CAAC;AAAA,EAC1C;AACA,SAAO;AACT;AACA,IAAI,uBAAuB;AAO3B,SAAS,qBAAqB,SAAS;AACrC,MAAI,SAAS,QAAQ,QAAQ,sBAAsB,EAAE;AACrD,MAAI,SAAS,KAAK,MAAM;AACxB,MAAI,cAAc,IAAI,YAAY,OAAO,MAAM;AAC/C,MAAI,QAAQ,IAAI,WAAW,WAAW;AACtC,UAAQ,OAAO,SAAU,OAAO,GAAG;AACjC,UAAM,CAAC,IAAI,OAAO,WAAW,CAAC;AAAA,EAChC,CAAC;AACD,SAAO;AACT;AAQA,SAAS,qBAAqB,aAAa,UAAU;AACnD,MAAI,SAAS,CAAC;AAGd,MAAI,YAAY;AAChB,MAAI,QAAQ,IAAI,WAAW,WAAW;AACtC,SAAO,MAAM,SAAS,GAAG;AAGvB,WAAO,KAAK,aAAa,MAAM,MAAM,QAAQ,MAAM,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC;AAC3E,YAAQ,MAAM,SAAS,SAAS;AAAA,EAClC;AACA,SAAO,QAAQ,OAAO,UAAU,UAAU,EAAE,OAAO,KAAK,OAAO,KAAK,EAAE,CAAC,CAAC;AAC1E;AAOA,SAAS,uBAAuB,aAAa;AAC3C,MAAI,WAAW,IAAI,SAAS,WAAW;AACvC,MAAI;AAGJ,MAAI;AACF,QAAI;AACJ,QAAI;AACJ,QAAI;AAGJ,QAAI,SAAS,SAAS,CAAC,MAAM,OAAQ,SAAS,SAAS,CAAC,MAAM,KAAM;AAClE,UAAI,SAAS,SAAS;AACtB,UAAI,SAAS;AACb,aAAO,SAAS,IAAI,QAAQ;AAC1B,YAAI,SAAS,SAAS,MAAM,MAAM,OAAQ,SAAS,SAAS,SAAS,CAAC,MAAM,KAAM;AAChF,sBAAY;AACZ;AAAA,QACF;AACA,kBAAU;AAAA,MACZ;AAAA,IACF;AACA,QAAI,WAAW;AACb,UAAI,aAAa,YAAY;AAC7B,UAAI,aAAa,YAAY;AAC7B,UAAI,sBAAsB,UAAU,YAAY,CAAC,MAAM,QAAQ;AAC7D,YAAI,aAAa,SAAS,UAAU,UAAU;AAC9C,uBAAe,eAAe;AAC9B,YAAI,gBAAgB,eAAe,OAAwB;AACzD,cAAI,SAAS,UAAU,aAAa,GAAG,YAAY,MAAM,IAAQ;AAC/D,gBAAI,iBAAiB,SAAS,UAAU,aAAa,GAAG,YAAY;AACpE,gBAAI,kBAAkB,GAAY;AAChC,yBAAW,aAAa;AAAA,YAC1B;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,UAAU;AACZ,UAAI,UAAU,SAAS,UAAU,UAAU,YAAY;AACvD,UAAI;AACJ,UAAI;AACJ,WAAK,IAAI,GAAG,IAAI,SAAS,KAAK,GAAG;AAC/B,kBAAU,WAAW,IAAI,KAAK;AAC9B,YAAI,SAAS,UAAU,SAAS,YAAY,MAAM,KAA0B;AAE1E,qBAAW;AAGX,wBAAc,SAAS,UAAU,SAAS,YAAY;AAGtD,mBAAS,UAAU,SAAS,GAAG,YAAY;AAC3C;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,SAAS,OAAO;AACd,kBAAc;AAAA,EAChB;AACA,SAAO;AACT;AAOA,SAAS,iBAAiB,aAAa;AACrC,MAAIF,UAAS;AACb,MAAIC,UAAS;AACb,MAAIC,UAAS;AACb,UAAQ,aAAa;AAAA,IAEnB,KAAK;AACH,MAAAD,UAAS;AACT;AAAA,IAGF,KAAK;AACH,MAAAD,UAAS;AACT;AAAA,IAGF,KAAK;AACH,MAAAE,UAAS;AACT;AAAA,IAGF,KAAK;AACH,MAAAF,UAAS;AACT,MAAAE,UAAS;AACT;AAAA,IAGF,KAAK;AACH,MAAAF,UAAS;AACT;AAAA,IAGF,KAAK;AACH,MAAAA,UAAS;AACT,MAAAC,UAAS;AACT;AAAA,IAGF,KAAK;AACH,MAAAD,UAAS;AACT;AAAA,EACJ;AACA,SAAO;AAAA,IACL,QAAQA;AAAA,IACR,QAAQC;AAAA,IACR,QAAQC;AAAA,EACV;AACF;AAEA,IAAI,SAAS;AAAA,EACX,QAAQ,SAASC,UAAS;AACxB,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,QAAI,KAAK,SAAS;AAChB,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,eAAe,SAAS,gBAAgB;AACtC,QAAI,UAAU,KAAK,SACjB,UAAU,KAAK,SACf,YAAY,KAAK,WACjB,UAAU,KAAK;AACjB,QAAI,WAAW,OAAO,QAAQ,iBAAiB;AAC/C,QAAI,YAAY,OAAO,QAAQ,kBAAkB;AACjD,aAAS,SAAS,YAAY;AAC9B,gBAAY,SAAS,YAAY;AACjC,QAAI,gBAAgB;AAAA,MAClB,OAAO,KAAK,IAAI,UAAU,aAAa,YAAY,IAAI,WAAW,mBAAmB;AAAA,MACrF,QAAQ,KAAK,IAAI,UAAU,cAAc,aAAa,IAAI,YAAY,oBAAoB;AAAA,IAC5F;AACA,SAAK,gBAAgB;AACrB,aAAS,SAAS;AAAA,MAChB,OAAO,cAAc;AAAA,MACrB,QAAQ,cAAc;AAAA,IACxB,CAAC;AACD,aAAS,SAAS,YAAY;AAC9B,gBAAY,SAAS,YAAY;AAAA,EACnC;AAAA;AAAA,EAEA,YAAY,SAAS,aAAa;AAChC,QAAI,gBAAgB,KAAK,eACvB,YAAY,KAAK;AACnB,QAAI,WAAW,KAAK,QAAQ;AAC5B,QAAI,UAAU,KAAK,IAAI,UAAU,MAAM,IAAI,QAAQ;AACnD,QAAI,eAAe,UAAU,UAAU,gBAAgB,UAAU;AACjE,QAAI,gBAAgB,UAAU,UAAU,eAAe,UAAU;AACjE,QAAI,cAAc,eAAe;AACjC,QAAI,cAAc,cAAc;AAChC,QAAI,eAAe,cAAc;AACjC,QAAI,cAAc,SAAS,cAAc,cAAc,OAAO;AAC5D,UAAI,aAAa,GAAG;AAClB,sBAAc,cAAc,SAAS;AAAA,MACvC,OAAO;AACL,uBAAe,cAAc,QAAQ;AAAA,MACvC;AAAA,IACF,WAAW,aAAa,GAAG;AACzB,qBAAe,cAAc,QAAQ;AAAA,IACvC,OAAO;AACL,oBAAc,cAAc,SAAS;AAAA,IACvC;AACA,QAAI,aAAa;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AACA,SAAK,aAAa;AAClB,SAAK,UAAU,aAAa,KAAK,aAAa;AAC9C,SAAK,YAAY,MAAM,IAAI;AAC3B,eAAW,QAAQ,KAAK,IAAI,KAAK,IAAI,WAAW,OAAO,WAAW,QAAQ,GAAG,WAAW,QAAQ;AAChG,eAAW,SAAS,KAAK,IAAI,KAAK,IAAI,WAAW,QAAQ,WAAW,SAAS,GAAG,WAAW,SAAS;AACpG,eAAW,QAAQ,cAAc,QAAQ,WAAW,SAAS;AAC7D,eAAW,OAAO,cAAc,SAAS,WAAW,UAAU;AAC9D,eAAW,UAAU,WAAW;AAChC,eAAW,SAAS,WAAW;AAC/B,SAAK,oBAAoB,OAAO,CAAC,GAAG,UAAU;AAAA,EAChD;AAAA,EACA,aAAa,SAAS,YAAY,aAAa,iBAAiB;AAC9D,QAAI,UAAU,KAAK,SACjB,gBAAgB,KAAK,eACrB,aAAa,KAAK,YAClB,cAAc,KAAK;AACrB,QAAI,WAAW,QAAQ;AACvB,QAAI,cAAc,WAAW;AAC7B,QAAI,UAAU,KAAK,WAAW;AAC9B,QAAI,aAAa;AACf,UAAI,iBAAiB,OAAO,QAAQ,cAAc,KAAK;AACvD,UAAI,kBAAkB,OAAO,QAAQ,eAAe,KAAK;AACzD,UAAI,WAAW,GAAG;AAChB,yBAAiB,KAAK,IAAI,gBAAgB,cAAc,KAAK;AAC7D,0BAAkB,KAAK,IAAI,iBAAiB,cAAc,MAAM;AAChE,YAAI,aAAa,GAAG;AAClB,cAAI,kBAAkB,cAAc,gBAAgB;AAClD,6BAAiB,kBAAkB;AAAA,UACrC,OAAO;AACL,8BAAkB,iBAAiB;AAAA,UACrC;AAAA,QACF;AAAA,MACF,WAAW,WAAW,GAAG;AACvB,YAAI,gBAAgB;AAClB,2BAAiB,KAAK,IAAI,gBAAgB,UAAU,YAAY,QAAQ,CAAC;AAAA,QAC3E,WAAW,iBAAiB;AAC1B,4BAAkB,KAAK,IAAI,iBAAiB,UAAU,YAAY,SAAS,CAAC;AAAA,QAC9E,WAAW,SAAS;AAClB,2BAAiB,YAAY;AAC7B,4BAAkB,YAAY;AAC9B,cAAI,kBAAkB,cAAc,gBAAgB;AAClD,6BAAiB,kBAAkB;AAAA,UACrC,OAAO;AACL,8BAAkB,iBAAiB;AAAA,UACrC;AAAA,QACF;AAAA,MACF;AACA,UAAI,oBAAoB,iBAAiB;AAAA,QACvC;AAAA,QACA,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,CAAC;AACD,uBAAiB,kBAAkB;AACnC,wBAAkB,kBAAkB;AACpC,iBAAW,WAAW;AACtB,iBAAW,YAAY;AACvB,iBAAW,WAAW;AACtB,iBAAW,YAAY;AAAA,IACzB;AACA,QAAI,iBAAiB;AACnB,UAAI,YAAY,UAAU,IAAI,IAAI;AAChC,YAAI,gBAAgB,cAAc,QAAQ,WAAW;AACrD,YAAI,eAAe,cAAc,SAAS,WAAW;AACrD,mBAAW,UAAU,KAAK,IAAI,GAAG,aAAa;AAC9C,mBAAW,SAAS,KAAK,IAAI,GAAG,YAAY;AAC5C,mBAAW,UAAU,KAAK,IAAI,GAAG,aAAa;AAC9C,mBAAW,SAAS,KAAK,IAAI,GAAG,YAAY;AAC5C,YAAI,WAAW,KAAK,SAAS;AAC3B,qBAAW,UAAU,KAAK,IAAI,YAAY,MAAM,YAAY,QAAQ,YAAY,QAAQ,WAAW,MAAM;AACzG,qBAAW,SAAS,KAAK,IAAI,YAAY,KAAK,YAAY,OAAO,YAAY,SAAS,WAAW,OAAO;AACxG,qBAAW,UAAU,YAAY;AACjC,qBAAW,SAAS,YAAY;AAChC,cAAI,aAAa,GAAG;AAClB,gBAAI,WAAW,SAAS,cAAc,OAAO;AAC3C,yBAAW,UAAU,KAAK,IAAI,GAAG,aAAa;AAC9C,yBAAW,UAAU,KAAK,IAAI,GAAG,aAAa;AAAA,YAChD;AACA,gBAAI,WAAW,UAAU,cAAc,QAAQ;AAC7C,yBAAW,SAAS,KAAK,IAAI,GAAG,YAAY;AAC5C,yBAAW,SAAS,KAAK,IAAI,GAAG,YAAY;AAAA,YAC9C;AAAA,UACF;AAAA,QACF;AAAA,MACF,OAAO;AACL,mBAAW,UAAU,CAAC,WAAW;AACjC,mBAAW,SAAS,CAAC,WAAW;AAChC,mBAAW,UAAU,cAAc;AACnC,mBAAW,SAAS,cAAc;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc,SAAS,aAAa,SAAS,aAAa;AACxD,QAAI,aAAa,KAAK,YACpB,YAAY,KAAK;AACnB,QAAI,aAAa;AACf,UAAI,mBAAmB,gBAAgB;AAAA,QACnC,OAAO,UAAU,eAAe,KAAK,IAAI,UAAU,UAAU,CAAC;AAAA,QAC9D,QAAQ,UAAU,gBAAgB,KAAK,IAAI,UAAU,UAAU,CAAC;AAAA,QAChE,QAAQ,UAAU,UAAU;AAAA,MAC9B,CAAC,GACD,eAAe,iBAAiB,OAChC,gBAAgB,iBAAiB;AACnC,UAAI,QAAQ,WAAW,SAAS,eAAe,WAAW;AAC1D,UAAI,SAAS,WAAW,UAAU,gBAAgB,WAAW;AAC7D,iBAAW,SAAS,QAAQ,WAAW,SAAS;AAChD,iBAAW,QAAQ,SAAS,WAAW,UAAU;AACjD,iBAAW,QAAQ;AACnB,iBAAW,SAAS;AACpB,iBAAW,cAAc,eAAe;AACxC,iBAAW,eAAe;AAC1B,iBAAW,gBAAgB;AAC3B,WAAK,YAAY,MAAM,KAAK;AAAA,IAC9B;AACA,QAAI,WAAW,QAAQ,WAAW,YAAY,WAAW,QAAQ,WAAW,UAAU;AACpF,iBAAW,OAAO,WAAW;AAAA,IAC/B;AACA,QAAI,WAAW,SAAS,WAAW,aAAa,WAAW,SAAS,WAAW,WAAW;AACxF,iBAAW,MAAM,WAAW;AAAA,IAC9B;AACA,eAAW,QAAQ,KAAK,IAAI,KAAK,IAAI,WAAW,OAAO,WAAW,QAAQ,GAAG,WAAW,QAAQ;AAChG,eAAW,SAAS,KAAK,IAAI,KAAK,IAAI,WAAW,QAAQ,WAAW,SAAS,GAAG,WAAW,SAAS;AACpG,SAAK,YAAY,OAAO,IAAI;AAC5B,eAAW,OAAO,KAAK,IAAI,KAAK,IAAI,WAAW,MAAM,WAAW,OAAO,GAAG,WAAW,OAAO;AAC5F,eAAW,MAAM,KAAK,IAAI,KAAK,IAAI,WAAW,KAAK,WAAW,MAAM,GAAG,WAAW,MAAM;AACxF,eAAW,UAAU,WAAW;AAChC,eAAW,SAAS,WAAW;AAC/B,aAAS,KAAK,QAAQ,OAAO;AAAA,MAC3B,OAAO,WAAW;AAAA,MAClB,QAAQ,WAAW;AAAA,IACrB,GAAG,cAAc;AAAA,MACf,YAAY,WAAW;AAAA,MACvB,YAAY,WAAW;AAAA,IACzB,CAAC,CAAC,CAAC;AACH,SAAK,YAAY,OAAO;AACxB,QAAI,KAAK,WAAW,KAAK,SAAS;AAChC,WAAK,aAAa,MAAM,IAAI;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,aAAa,SAAS,YAAY,SAAS;AACzC,QAAI,aAAa,KAAK,YACpB,YAAY,KAAK;AACnB,QAAI,QAAQ,UAAU,gBAAgB,WAAW,QAAQ,WAAW;AACpE,QAAI,SAAS,UAAU,iBAAiB,WAAW,SAAS,WAAW;AACvE,WAAO,WAAW;AAAA,MAChB;AAAA,MACA;AAAA,MACA,OAAO,WAAW,QAAQ,SAAS;AAAA,MACnC,MAAM,WAAW,SAAS,UAAU;AAAA,IACtC,CAAC;AACD,aAAS,KAAK,OAAO,OAAO;AAAA,MAC1B,OAAO,UAAU;AAAA,MACjB,QAAQ,UAAU;AAAA,IACpB,GAAG,cAAc,OAAO;AAAA,MACtB,YAAY,UAAU;AAAA,MACtB,YAAY,UAAU;AAAA,IACxB,GAAG,SAAS,CAAC,CAAC,CAAC;AACf,QAAI,SAAS;AACX,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA,EACA,aAAa,SAAS,cAAc;AAClC,QAAI,UAAU,KAAK,SACjB,aAAa,KAAK;AACpB,QAAI,cAAc,QAAQ,eAAe,QAAQ;AACjD,QAAI,eAAe,OAAO,QAAQ,YAAY,KAAK;AACnD,QAAI,cAAc;AAAA,MAChB,OAAO,WAAW;AAAA,MAClB,QAAQ,WAAW;AAAA,IACrB;AACA,QAAI,aAAa;AACf,UAAI,WAAW,SAAS,cAAc,WAAW,OAAO;AACtD,oBAAY,SAAS,YAAY,QAAQ;AAAA,MAC3C,OAAO;AACL,oBAAY,QAAQ,YAAY,SAAS;AAAA,MAC3C;AAAA,IACF;AACA,SAAK,cAAc;AACnB,SAAK,aAAa,MAAM,IAAI;AAG5B,gBAAY,QAAQ,KAAK,IAAI,KAAK,IAAI,YAAY,OAAO,YAAY,QAAQ,GAAG,YAAY,QAAQ;AACpG,gBAAY,SAAS,KAAK,IAAI,KAAK,IAAI,YAAY,QAAQ,YAAY,SAAS,GAAG,YAAY,SAAS;AAGxG,gBAAY,QAAQ,KAAK,IAAI,YAAY,UAAU,YAAY,QAAQ,YAAY;AACnF,gBAAY,SAAS,KAAK,IAAI,YAAY,WAAW,YAAY,SAAS,YAAY;AACtF,gBAAY,OAAO,WAAW,QAAQ,WAAW,QAAQ,YAAY,SAAS;AAC9E,gBAAY,MAAM,WAAW,OAAO,WAAW,SAAS,YAAY,UAAU;AAC9E,gBAAY,UAAU,YAAY;AAClC,gBAAY,SAAS,YAAY;AACjC,SAAK,qBAAqB,OAAO,CAAC,GAAG,WAAW;AAAA,EAClD;AAAA,EACA,cAAc,SAAS,aAAa,aAAa,iBAAiB;AAChE,QAAI,UAAU,KAAK,SACjB,gBAAgB,KAAK,eACrB,aAAa,KAAK,YAClB,cAAc,KAAK,aACnB,UAAU,KAAK;AACjB,QAAI,cAAc,QAAQ;AAC1B,QAAI,aAAa;AACf,UAAI,kBAAkB,OAAO,QAAQ,eAAe,KAAK;AACzD,UAAI,mBAAmB,OAAO,QAAQ,gBAAgB,KAAK;AAC3D,UAAI,kBAAkB,UAAU,KAAK,IAAI,cAAc,OAAO,WAAW,OAAO,WAAW,QAAQ,WAAW,MAAM,cAAc,QAAQ,WAAW,IAAI,IAAI,cAAc;AAC3K,UAAI,mBAAmB,UAAU,KAAK,IAAI,cAAc,QAAQ,WAAW,QAAQ,WAAW,SAAS,WAAW,KAAK,cAAc,SAAS,WAAW,GAAG,IAAI,cAAc;AAG9K,wBAAkB,KAAK,IAAI,iBAAiB,cAAc,KAAK;AAC/D,yBAAmB,KAAK,IAAI,kBAAkB,cAAc,MAAM;AAClE,UAAI,aAAa;AACf,YAAI,mBAAmB,kBAAkB;AACvC,cAAI,mBAAmB,cAAc,iBAAiB;AACpD,+BAAmB,kBAAkB;AAAA,UACvC,OAAO;AACL,8BAAkB,mBAAmB;AAAA,UACvC;AAAA,QACF,WAAW,iBAAiB;AAC1B,6BAAmB,kBAAkB;AAAA,QACvC,WAAW,kBAAkB;AAC3B,4BAAkB,mBAAmB;AAAA,QACvC;AACA,YAAI,mBAAmB,cAAc,iBAAiB;AACpD,6BAAmB,kBAAkB;AAAA,QACvC,OAAO;AACL,4BAAkB,mBAAmB;AAAA,QACvC;AAAA,MACF;AAGA,kBAAY,WAAW,KAAK,IAAI,iBAAiB,eAAe;AAChE,kBAAY,YAAY,KAAK,IAAI,kBAAkB,gBAAgB;AACnE,kBAAY,WAAW;AACvB,kBAAY,YAAY;AAAA,IAC1B;AACA,QAAI,iBAAiB;AACnB,UAAI,SAAS;AACX,oBAAY,UAAU,KAAK,IAAI,GAAG,WAAW,IAAI;AACjD,oBAAY,SAAS,KAAK,IAAI,GAAG,WAAW,GAAG;AAC/C,oBAAY,UAAU,KAAK,IAAI,cAAc,OAAO,WAAW,OAAO,WAAW,KAAK,IAAI,YAAY;AACtG,oBAAY,SAAS,KAAK,IAAI,cAAc,QAAQ,WAAW,MAAM,WAAW,MAAM,IAAI,YAAY;AAAA,MACxG,OAAO;AACL,oBAAY,UAAU;AACtB,oBAAY,SAAS;AACrB,oBAAY,UAAU,cAAc,QAAQ,YAAY;AACxD,oBAAY,SAAS,cAAc,SAAS,YAAY;AAAA,MAC1D;AAAA,IACF;AAAA,EACF;AAAA,EACA,eAAe,SAAS,gBAAgB;AACtC,QAAI,UAAU,KAAK,SACjB,gBAAgB,KAAK,eACrB,cAAc,KAAK;AACrB,QAAI,YAAY,QAAQ,YAAY,YAAY,YAAY,QAAQ,YAAY,UAAU;AACxF,kBAAY,OAAO,YAAY;AAAA,IACjC;AACA,QAAI,YAAY,SAAS,YAAY,aAAa,YAAY,SAAS,YAAY,WAAW;AAC5F,kBAAY,MAAM,YAAY;AAAA,IAChC;AACA,gBAAY,QAAQ,KAAK,IAAI,KAAK,IAAI,YAAY,OAAO,YAAY,QAAQ,GAAG,YAAY,QAAQ;AACpG,gBAAY,SAAS,KAAK,IAAI,KAAK,IAAI,YAAY,QAAQ,YAAY,SAAS,GAAG,YAAY,SAAS;AACxG,SAAK,aAAa,OAAO,IAAI;AAC7B,gBAAY,OAAO,KAAK,IAAI,KAAK,IAAI,YAAY,MAAM,YAAY,OAAO,GAAG,YAAY,OAAO;AAChG,gBAAY,MAAM,KAAK,IAAI,KAAK,IAAI,YAAY,KAAK,YAAY,MAAM,GAAG,YAAY,MAAM;AAC5F,gBAAY,UAAU,YAAY;AAClC,gBAAY,SAAS,YAAY;AACjC,QAAI,QAAQ,WAAW,QAAQ,gBAAgB;AAE7C,cAAQ,KAAK,MAAM,aAAa,YAAY,SAAS,cAAc,SAAS,YAAY,UAAU,cAAc,SAAS,cAAc,UAAU;AAAA,IACnJ;AACA,aAAS,KAAK,SAAS,OAAO;AAAA,MAC5B,OAAO,YAAY;AAAA,MACnB,QAAQ,YAAY;AAAA,IACtB,GAAG,cAAc;AAAA,MACf,YAAY,YAAY;AAAA,MACxB,YAAY,YAAY;AAAA,IAC1B,CAAC,CAAC,CAAC;AACH,QAAI,KAAK,WAAW,KAAK,SAAS;AAChC,WAAK,YAAY,MAAM,IAAI;AAAA,IAC7B;AACA,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA,EACA,QAAQ,SAAS,SAAS;AACxB,SAAK,QAAQ;AACb,kBAAc,KAAK,SAAS,YAAY,KAAK,QAAQ,CAAC;AAAA,EACxD;AACF;AAEA,IAAI,UAAU;AAAA,EACZ,aAAa,SAAS,cAAc;AAClC,QAAI,UAAU,KAAK,SACjB,cAAc,KAAK;AACrB,QAAIC,WAAU,KAAK,QAAQ;AAC3B,QAAI,MAAM,cAAc,KAAK,iBAAiB,KAAK;AACnD,QAAI,MAAM,QAAQ,OAAO;AACzB,QAAI,QAAQ,SAAS,cAAc,KAAK;AACxC,QAAI,aAAa;AACf,YAAM,cAAc;AAAA,IACtB;AACA,UAAM,MAAM;AACZ,UAAM,MAAM;AACZ,SAAK,QAAQ,YAAY,KAAK;AAC9B,SAAK,eAAe;AACpB,QAAI,CAACA,UAAS;AACZ;AAAA,IACF;AACA,QAAI,WAAWA;AACf,QAAI,OAAOA,aAAY,UAAU;AAC/B,iBAAW,QAAQ,cAAc,iBAAiBA,QAAO;AAAA,IAC3D,WAAWA,SAAQ,eAAe;AAChC,iBAAW,CAACA,QAAO;AAAA,IACrB;AACA,SAAK,WAAW;AAChB,YAAQ,UAAU,SAAU,IAAI;AAC9B,UAAI,MAAM,SAAS,cAAc,KAAK;AAGtC,cAAQ,IAAI,cAAc;AAAA,QACxB,OAAO,GAAG;AAAA,QACV,QAAQ,GAAG;AAAA,QACX,MAAM,GAAG;AAAA,MACX,CAAC;AACD,UAAI,aAAa;AACf,YAAI,cAAc;AAAA,MACpB;AACA,UAAI,MAAM;AACV,UAAI,MAAM;AAQV,UAAI,MAAM,UAAU;AACpB,SAAG,YAAY;AACf,SAAG,YAAY,GAAG;AAAA,IACpB,CAAC;AAAA,EACH;AAAA,EACA,cAAc,SAAS,eAAe;AACpC,YAAQ,KAAK,UAAU,SAAU,SAAS;AACxC,UAAI,OAAO,QAAQ,SAAS,YAAY;AACxC,eAAS,SAAS;AAAA,QAChB,OAAO,KAAK;AAAA,QACZ,QAAQ,KAAK;AAAA,MACf,CAAC;AACD,cAAQ,YAAY,KAAK;AACzB,iBAAW,SAAS,YAAY;AAAA,IAClC,CAAC;AAAA,EACH;AAAA,EACA,SAAS,SAASA,WAAU;AAC1B,QAAI,YAAY,KAAK,WACnB,aAAa,KAAK,YAClB,cAAc,KAAK;AACrB,QAAI,eAAe,YAAY,OAC7B,gBAAgB,YAAY;AAC9B,QAAI,QAAQ,UAAU,OACpB,SAAS,UAAU;AACrB,QAAI,OAAO,YAAY,OAAO,WAAW,OAAO,UAAU;AAC1D,QAAI,MAAM,YAAY,MAAM,WAAW,MAAM,UAAU;AACvD,QAAI,CAAC,KAAK,WAAW,KAAK,UAAU;AAClC;AAAA,IACF;AACA,aAAS,KAAK,cAAc,OAAO;AAAA,MACjC;AAAA,MACA;AAAA,IACF,GAAG,cAAc,OAAO;AAAA,MACtB,YAAY,CAAC;AAAA,MACb,YAAY,CAAC;AAAA,IACf,GAAG,SAAS,CAAC,CAAC,CAAC;AACf,YAAQ,KAAK,UAAU,SAAU,SAAS;AACxC,UAAI,OAAO,QAAQ,SAAS,YAAY;AACxC,UAAI,gBAAgB,KAAK;AACzB,UAAI,iBAAiB,KAAK;AAC1B,UAAI,WAAW;AACf,UAAI,YAAY;AAChB,UAAI,QAAQ;AACZ,UAAI,cAAc;AAChB,gBAAQ,gBAAgB;AACxB,oBAAY,gBAAgB;AAAA,MAC9B;AACA,UAAI,iBAAiB,YAAY,gBAAgB;AAC/C,gBAAQ,iBAAiB;AACzB,mBAAW,eAAe;AAC1B,oBAAY;AAAA,MACd;AACA,eAAS,SAAS;AAAA,QAChB,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,CAAC;AACD,eAAS,QAAQ,qBAAqB,KAAK,EAAE,CAAC,GAAG,OAAO;AAAA,QACtD,OAAO,QAAQ;AAAA,QACf,QAAQ,SAAS;AAAA,MACnB,GAAG,cAAc,OAAO;AAAA,QACtB,YAAY,CAAC,OAAO;AAAA,QACpB,YAAY,CAAC,MAAM;AAAA,MACrB,GAAG,SAAS,CAAC,CAAC,CAAC;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAEA,IAAI,SAAS;AAAA,EACX,MAAM,SAAS,OAAO;AACpB,QAAI,UAAU,KAAK,SACjB,UAAU,KAAK,SACf,UAAU,KAAK;AACjB,QAAI,WAAW,QAAQ,SAAS,GAAG;AACjC,kBAAY,SAAS,kBAAkB,QAAQ,SAAS;AAAA,IAC1D;AACA,QAAI,WAAW,QAAQ,QAAQ,GAAG;AAChC,kBAAY,SAAS,iBAAiB,QAAQ,QAAQ;AAAA,IACxD;AACA,QAAI,WAAW,QAAQ,OAAO,GAAG;AAC/B,kBAAY,SAAS,gBAAgB,QAAQ,OAAO;AAAA,IACtD;AACA,QAAI,WAAW,QAAQ,IAAI,GAAG;AAC5B,kBAAY,SAAS,YAAY,QAAQ,IAAI;AAAA,IAC/C;AACA,QAAI,WAAW,QAAQ,IAAI,GAAG;AAC5B,kBAAY,SAAS,YAAY,QAAQ,IAAI;AAAA,IAC/C;AACA,gBAAY,SAAS,oBAAoB,KAAK,cAAc,KAAK,UAAU,KAAK,IAAI,CAAC;AACrF,QAAI,QAAQ,YAAY,QAAQ,aAAa;AAC3C,kBAAY,SAAS,aAAa,KAAK,UAAU,KAAK,MAAM,KAAK,IAAI,GAAG;AAAA,QACtE,SAAS;AAAA,QACT,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AACA,QAAI,QAAQ,0BAA0B;AACpC,kBAAY,SAAS,gBAAgB,KAAK,aAAa,KAAK,SAAS,KAAK,IAAI,CAAC;AAAA,IACjF;AACA,gBAAY,QAAQ,eAAe,oBAAoB,KAAK,aAAa,KAAK,SAAS,KAAK,IAAI,CAAC;AACjG,gBAAY,QAAQ,eAAe,kBAAkB,KAAK,YAAY,KAAK,QAAQ,KAAK,IAAI,CAAC;AAC7F,QAAI,QAAQ,YAAY;AACtB,kBAAY,QAAQ,cAAc,KAAK,WAAW,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAC1E;AAAA,EACF;AAAA,EACA,QAAQ,SAAS,SAAS;AACxB,QAAI,UAAU,KAAK,SACjB,UAAU,KAAK,SACf,UAAU,KAAK;AACjB,QAAI,WAAW,QAAQ,SAAS,GAAG;AACjC,qBAAe,SAAS,kBAAkB,QAAQ,SAAS;AAAA,IAC7D;AACA,QAAI,WAAW,QAAQ,QAAQ,GAAG;AAChC,qBAAe,SAAS,iBAAiB,QAAQ,QAAQ;AAAA,IAC3D;AACA,QAAI,WAAW,QAAQ,OAAO,GAAG;AAC/B,qBAAe,SAAS,gBAAgB,QAAQ,OAAO;AAAA,IACzD;AACA,QAAI,WAAW,QAAQ,IAAI,GAAG;AAC5B,qBAAe,SAAS,YAAY,QAAQ,IAAI;AAAA,IAClD;AACA,QAAI,WAAW,QAAQ,IAAI,GAAG;AAC5B,qBAAe,SAAS,YAAY,QAAQ,IAAI;AAAA,IAClD;AACA,mBAAe,SAAS,oBAAoB,KAAK,WAAW;AAC5D,QAAI,QAAQ,YAAY,QAAQ,aAAa;AAC3C,qBAAe,SAAS,aAAa,KAAK,SAAS;AAAA,QACjD,SAAS;AAAA,QACT,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AACA,QAAI,QAAQ,0BAA0B;AACpC,qBAAe,SAAS,gBAAgB,KAAK,UAAU;AAAA,IACzD;AACA,mBAAe,QAAQ,eAAe,oBAAoB,KAAK,UAAU;AACzE,mBAAe,QAAQ,eAAe,kBAAkB,KAAK,SAAS;AACtE,QAAI,QAAQ,YAAY;AACtB,qBAAe,QAAQ,cAAc,KAAK,QAAQ;AAAA,IACpD;AAAA,EACF;AACF;AAEA,IAAI,WAAW;AAAA,EACb,QAAQ,SAAS,SAAS;AACxB,QAAI,KAAK,UAAU;AACjB;AAAA,IACF;AACA,QAAI,UAAU,KAAK,SACjB,YAAY,KAAK,WACjB,gBAAgB,KAAK;AACvB,QAAI,SAAS,UAAU,cAAc,cAAc;AACnD,QAAI,SAAS,UAAU,eAAe,cAAc;AACpD,QAAI,QAAQ,KAAK,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,SAAS,CAAC,IAAI,SAAS;AAGnE,QAAI,UAAU,GAAG;AACf,UAAI;AACJ,UAAI;AACJ,UAAI,QAAQ,SAAS;AACnB,qBAAa,KAAK,cAAc;AAChC,sBAAc,KAAK,eAAe;AAAA,MACpC;AACA,WAAK,OAAO;AACZ,UAAI,QAAQ,SAAS;AACnB,aAAK,cAAc,QAAQ,YAAY,SAAU,GAAG,GAAG;AACrD,qBAAW,CAAC,IAAI,IAAI;AAAA,QACtB,CAAC,CAAC;AACF,aAAK,eAAe,QAAQ,aAAa,SAAU,GAAG,GAAG;AACvD,sBAAY,CAAC,IAAI,IAAI;AAAA,QACvB,CAAC,CAAC;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,SAAS,WAAW;AAC5B,QAAI,KAAK,YAAY,KAAK,QAAQ,aAAa,gBAAgB;AAC7D;AAAA,IACF;AACA,SAAK,YAAY,SAAS,KAAK,SAAS,UAAU,IAAI,iBAAiB,cAAc;AAAA,EACvF;AAAA,EACA,OAAO,SAAS,MAAM,OAAO;AAC3B,QAAI,QAAQ;AACZ,QAAI,QAAQ,OAAO,KAAK,QAAQ,cAAc,KAAK;AACnD,QAAI,QAAQ;AACZ,QAAI,KAAK,UAAU;AACjB;AAAA,IACF;AACA,UAAM,eAAe;AAGrB,QAAI,KAAK,UAAU;AACjB;AAAA,IACF;AACA,SAAK,WAAW;AAChB,eAAW,WAAY;AACrB,YAAM,WAAW;AAAA,IACnB,GAAG,EAAE;AACL,QAAI,MAAM,QAAQ;AAChB,cAAQ,MAAM,SAAS,IAAI,IAAI;AAAA,IACjC,WAAW,MAAM,YAAY;AAC3B,cAAQ,CAAC,MAAM,aAAa;AAAA,IAC9B,WAAW,MAAM,QAAQ;AACvB,cAAQ,MAAM,SAAS,IAAI,IAAI;AAAA,IACjC;AACA,SAAK,KAAK,CAAC,QAAQ,OAAO,KAAK;AAAA,EACjC;AAAA,EACA,WAAW,SAAS,UAAU,OAAO;AACnC,QAAI,UAAU,MAAM,SAClB,SAAS,MAAM;AACjB,QAAI,KAAK,aAGL,MAAM,SAAS,eAAe,MAAM,SAAS,iBAAiB,MAAM,gBAAgB;AAAA,KAExF,SAAS,OAAO,KAAK,YAAY,KAAK,SAAS,MAAM,KAAK,WAAW,KAGlE,MAAM,UAAU;AACjB;AAAA,IACF;AACA,QAAI,UAAU,KAAK,SACjB,WAAW,KAAK;AAClB,QAAI;AACJ,QAAI,MAAM,gBAAgB;AAExB,cAAQ,MAAM,gBAAgB,SAAU,OAAO;AAC7C,iBAAS,MAAM,UAAU,IAAI,WAAW,KAAK;AAAA,MAC/C,CAAC;AAAA,IACH,OAAO;AAEL,eAAS,MAAM,aAAa,CAAC,IAAI,WAAW,KAAK;AAAA,IACnD;AACA,QAAI,OAAO,KAAK,QAAQ,EAAE,SAAS,KAAK,QAAQ,YAAY,QAAQ,aAAa;AAC/E,eAAS;AAAA,IACX,OAAO;AACL,eAAS,QAAQ,MAAM,QAAQ,WAAW;AAAA,IAC5C;AACA,QAAI,CAAC,eAAe,KAAK,MAAM,GAAG;AAChC;AAAA,IACF;AACA,QAAI,cAAc,KAAK,SAAS,kBAAkB;AAAA,MAChD,eAAe;AAAA,MACf;AAAA,IACF,CAAC,MAAM,OAAO;AACZ;AAAA,IACF;AAGA,UAAM,eAAe;AACrB,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,QAAI,WAAW,aAAa;AAC1B,WAAK,WAAW;AAChB,eAAS,KAAK,SAAS,WAAW;AAAA,IACpC;AAAA,EACF;AAAA,EACA,UAAU,SAAS,SAAS,OAAO;AACjC,QAAI,SAAS,KAAK;AAClB,QAAI,KAAK,YAAY,CAAC,QAAQ;AAC5B;AAAA,IACF;AACA,QAAI,WAAW,KAAK;AACpB,UAAM,eAAe;AACrB,QAAI,cAAc,KAAK,SAAS,iBAAiB;AAAA,MAC/C,eAAe;AAAA,MACf;AAAA,IACF,CAAC,MAAM,OAAO;AACZ;AAAA,IACF;AACA,QAAI,MAAM,gBAAgB;AACxB,cAAQ,MAAM,gBAAgB,SAAU,OAAO;AAE7C,eAAO,SAAS,MAAM,UAAU,KAAK,CAAC,GAAG,WAAW,OAAO,IAAI,CAAC;AAAA,MAClE,CAAC;AAAA,IACH,OAAO;AACL,aAAO,SAAS,MAAM,aAAa,CAAC,KAAK,CAAC,GAAG,WAAW,OAAO,IAAI,CAAC;AAAA,IACtE;AACA,SAAK,OAAO,KAAK;AAAA,EACnB;AAAA,EACA,SAAS,SAAS,QAAQ,OAAO;AAC/B,QAAI,KAAK,UAAU;AACjB;AAAA,IACF;AACA,QAAI,SAAS,KAAK,QAChB,WAAW,KAAK;AAClB,QAAI,MAAM,gBAAgB;AACxB,cAAQ,MAAM,gBAAgB,SAAU,OAAO;AAC7C,eAAO,SAAS,MAAM,UAAU;AAAA,MAClC,CAAC;AAAA,IACH,OAAO;AACL,aAAO,SAAS,MAAM,aAAa,CAAC;AAAA,IACtC;AACA,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,UAAM,eAAe;AACrB,QAAI,CAAC,OAAO,KAAK,QAAQ,EAAE,QAAQ;AACjC,WAAK,SAAS;AAAA,IAChB;AACA,QAAI,KAAK,UAAU;AACjB,WAAK,WAAW;AAChB,kBAAY,KAAK,SAAS,aAAa,KAAK,WAAW,KAAK,QAAQ,KAAK;AAAA,IAC3E;AACA,kBAAc,KAAK,SAAS,gBAAgB;AAAA,MAC1C,eAAe;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEA,IAAI,SAAS;AAAA,EACX,QAAQ,SAASC,QAAO,OAAO;AAC7B,QAAI,UAAU,KAAK,SACjB,aAAa,KAAK,YAClB,gBAAgB,KAAK,eACrB,cAAc,KAAK,aACnB,WAAW,KAAK;AAClB,QAAI,SAAS,KAAK;AAClB,QAAI,cAAc,QAAQ;AAC1B,QAAI,OAAO,YAAY,MACrB,MAAM,YAAY,KAClB,QAAQ,YAAY,OACpB,SAAS,YAAY;AACvB,QAAI,QAAQ,OAAO;AACnB,QAAI,SAAS,MAAM;AACnB,QAAI,UAAU;AACd,QAAI,SAAS;AACb,QAAI,WAAW,cAAc;AAC7B,QAAI,YAAY,cAAc;AAC9B,QAAI,aAAa;AACjB,QAAI;AAGJ,QAAI,CAAC,eAAe,MAAM,UAAU;AAClC,oBAAc,SAAS,SAAS,QAAQ,SAAS;AAAA,IACnD;AACA,QAAI,KAAK,SAAS;AAChB,gBAAU,YAAY;AACtB,eAAS,YAAY;AACrB,iBAAW,UAAU,KAAK,IAAI,cAAc,OAAO,WAAW,OAAO,WAAW,OAAO,WAAW,KAAK;AACvG,kBAAY,SAAS,KAAK,IAAI,cAAc,QAAQ,WAAW,QAAQ,WAAW,MAAM,WAAW,MAAM;AAAA,IAC3G;AACA,QAAI,UAAU,SAAS,OAAO,KAAK,QAAQ,EAAE,CAAC,CAAC;AAC/C,QAAI,QAAQ;AAAA,MACV,GAAG,QAAQ,OAAO,QAAQ;AAAA,MAC1B,GAAG,QAAQ,OAAO,QAAQ;AAAA,IAC5B;AACA,QAAI,QAAQ,SAASC,OAAM,MAAM;AAC/B,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH,cAAI,QAAQ,MAAM,IAAI,UAAU;AAC9B,kBAAM,IAAI,WAAW;AAAA,UACvB;AACA;AAAA,QACF,KAAK;AACH,cAAI,OAAO,MAAM,IAAI,SAAS;AAC5B,kBAAM,IAAI,UAAU;AAAA,UACtB;AACA;AAAA,QACF,KAAK;AACH,cAAI,MAAM,MAAM,IAAI,QAAQ;AAC1B,kBAAM,IAAI,SAAS;AAAA,UACrB;AACA;AAAA,QACF,KAAK;AACH,cAAI,SAAS,MAAM,IAAI,WAAW;AAChC,kBAAM,IAAI,YAAY;AAAA,UACxB;AACA;AAAA,MACJ;AAAA,IACF;AACA,YAAQ,QAAQ;AAAA,MAEd,KAAK;AACH,gBAAQ,MAAM;AACd,eAAO,MAAM;AACb;AAAA,MAGF,KAAK;AACH,YAAI,MAAM,KAAK,MAAM,SAAS,YAAY,gBAAgB,OAAO,UAAU,UAAU,aAAa;AAChG,uBAAa;AACb;AAAA,QACF;AACA,cAAM,WAAW;AACjB,iBAAS,MAAM;AACf,YAAI,QAAQ,GAAG;AACb,mBAAS;AACT,kBAAQ,CAAC;AACT,kBAAQ;AAAA,QACV;AACA,YAAI,aAAa;AACf,mBAAS,QAAQ;AACjB,kBAAQ,YAAY,SAAS,UAAU;AAAA,QACzC;AACA;AAAA,MACF,KAAK;AACH,YAAI,MAAM,KAAK,MAAM,OAAO,UAAU,gBAAgB,QAAQ,WAAW,SAAS,YAAY;AAC5F,uBAAa;AACb;AAAA,QACF;AACA,cAAM,YAAY;AAClB,kBAAU,MAAM;AAChB,eAAO,MAAM;AACb,YAAI,SAAS,GAAG;AACd,mBAAS;AACT,mBAAS,CAAC;AACV,iBAAO;AAAA,QACT;AACA,YAAI,aAAa;AACf,kBAAQ,SAAS;AACjB,mBAAS,YAAY,QAAQ,SAAS;AAAA,QACxC;AACA;AAAA,MACF,KAAK;AACH,YAAI,MAAM,KAAK,MAAM,QAAQ,WAAW,gBAAgB,OAAO,UAAU,UAAU,aAAa;AAC9F,uBAAa;AACb;AAAA,QACF;AACA,cAAM,WAAW;AACjB,iBAAS,MAAM;AACf,gBAAQ,MAAM;AACd,YAAI,QAAQ,GAAG;AACb,mBAAS;AACT,kBAAQ,CAAC;AACT,kBAAQ;AAAA,QACV;AACA,YAAI,aAAa;AACf,mBAAS,QAAQ;AACjB,kBAAQ,YAAY,SAAS,UAAU;AAAA,QACzC;AACA;AAAA,MACF,KAAK;AACH,YAAI,MAAM,KAAK,MAAM,UAAU,aAAa,gBAAgB,QAAQ,WAAW,SAAS,YAAY;AAClG,uBAAa;AACb;AAAA,QACF;AACA,cAAM,YAAY;AAClB,kBAAU,MAAM;AAChB,YAAI,SAAS,GAAG;AACd,mBAAS;AACT,mBAAS,CAAC;AACV,iBAAO;AAAA,QACT;AACA,YAAI,aAAa;AACf,kBAAQ,SAAS;AACjB,mBAAS,YAAY,QAAQ,SAAS;AAAA,QACxC;AACA;AAAA,MACF,KAAK;AACH,YAAI,aAAa;AACf,cAAI,MAAM,KAAK,MAAM,OAAO,UAAU,SAAS,WAAW;AACxD,yBAAa;AACb;AAAA,UACF;AACA,gBAAM,YAAY;AAClB,oBAAU,MAAM;AAChB,iBAAO,MAAM;AACb,kBAAQ,SAAS;AAAA,QACnB,OAAO;AACL,gBAAM,YAAY;AAClB,gBAAM,WAAW;AACjB,cAAI,MAAM,KAAK,GAAG;AAChB,gBAAI,QAAQ,UAAU;AACpB,uBAAS,MAAM;AAAA,YACjB,WAAW,MAAM,KAAK,KAAK,OAAO,QAAQ;AACxC,2BAAa;AAAA,YACf;AAAA,UACF,OAAO;AACL,qBAAS,MAAM;AAAA,UACjB;AACA,cAAI,MAAM,KAAK,GAAG;AAChB,gBAAI,MAAM,QAAQ;AAChB,wBAAU,MAAM;AAChB,qBAAO,MAAM;AAAA,YACf;AAAA,UACF,OAAO;AACL,sBAAU,MAAM;AAChB,mBAAO,MAAM;AAAA,UACf;AAAA,QACF;AACA,YAAI,QAAQ,KAAK,SAAS,GAAG;AAC3B,mBAAS;AACT,mBAAS,CAAC;AACV,kBAAQ,CAAC;AACT,iBAAO;AACP,kBAAQ;AAAA,QACV,WAAW,QAAQ,GAAG;AACpB,mBAAS;AACT,kBAAQ,CAAC;AACT,kBAAQ;AAAA,QACV,WAAW,SAAS,GAAG;AACrB,mBAAS;AACT,mBAAS,CAAC;AACV,iBAAO;AAAA,QACT;AACA;AAAA,MACF,KAAK;AACH,YAAI,aAAa;AACf,cAAI,MAAM,KAAK,MAAM,OAAO,UAAU,QAAQ,UAAU;AACtD,yBAAa;AACb;AAAA,UACF;AACA,gBAAM,YAAY;AAClB,oBAAU,MAAM;AAChB,iBAAO,MAAM;AACb,kBAAQ,SAAS;AACjB,kBAAQ,YAAY,QAAQ;AAAA,QAC9B,OAAO;AACL,gBAAM,YAAY;AAClB,gBAAM,WAAW;AACjB,cAAI,MAAM,KAAK,GAAG;AAChB,gBAAI,OAAO,SAAS;AAClB,uBAAS,MAAM;AACf,sBAAQ,MAAM;AAAA,YAChB,WAAW,MAAM,KAAK,KAAK,OAAO,QAAQ;AACxC,2BAAa;AAAA,YACf;AAAA,UACF,OAAO;AACL,qBAAS,MAAM;AACf,oBAAQ,MAAM;AAAA,UAChB;AACA,cAAI,MAAM,KAAK,GAAG;AAChB,gBAAI,MAAM,QAAQ;AAChB,wBAAU,MAAM;AAChB,qBAAO,MAAM;AAAA,YACf;AAAA,UACF,OAAO;AACL,sBAAU,MAAM;AAChB,mBAAO,MAAM;AAAA,UACf;AAAA,QACF;AACA,YAAI,QAAQ,KAAK,SAAS,GAAG;AAC3B,mBAAS;AACT,mBAAS,CAAC;AACV,kBAAQ,CAAC;AACT,iBAAO;AACP,kBAAQ;AAAA,QACV,WAAW,QAAQ,GAAG;AACpB,mBAAS;AACT,kBAAQ,CAAC;AACT,kBAAQ;AAAA,QACV,WAAW,SAAS,GAAG;AACrB,mBAAS;AACT,mBAAS,CAAC;AACV,iBAAO;AAAA,QACT;AACA;AAAA,MACF,KAAK;AACH,YAAI,aAAa;AACf,cAAI,MAAM,KAAK,MAAM,QAAQ,WAAW,UAAU,YAAY;AAC5D,yBAAa;AACb;AAAA,UACF;AACA,gBAAM,WAAW;AACjB,mBAAS,MAAM;AACf,kBAAQ,MAAM;AACd,mBAAS,QAAQ;AAAA,QACnB,OAAO;AACL,gBAAM,YAAY;AAClB,gBAAM,WAAW;AACjB,cAAI,MAAM,KAAK,GAAG;AAChB,gBAAI,OAAO,SAAS;AAClB,uBAAS,MAAM;AACf,sBAAQ,MAAM;AAAA,YAChB,WAAW,MAAM,KAAK,KAAK,UAAU,WAAW;AAC9C,2BAAa;AAAA,YACf;AAAA,UACF,OAAO;AACL,qBAAS,MAAM;AACf,oBAAQ,MAAM;AAAA,UAChB;AACA,cAAI,MAAM,KAAK,GAAG;AAChB,gBAAI,SAAS,WAAW;AACtB,wBAAU,MAAM;AAAA,YAClB;AAAA,UACF,OAAO;AACL,sBAAU,MAAM;AAAA,UAClB;AAAA,QACF;AACA,YAAI,QAAQ,KAAK,SAAS,GAAG;AAC3B,mBAAS;AACT,mBAAS,CAAC;AACV,kBAAQ,CAAC;AACT,iBAAO;AACP,kBAAQ;AAAA,QACV,WAAW,QAAQ,GAAG;AACpB,mBAAS;AACT,kBAAQ,CAAC;AACT,kBAAQ;AAAA,QACV,WAAW,SAAS,GAAG;AACrB,mBAAS;AACT,mBAAS,CAAC;AACV,iBAAO;AAAA,QACT;AACA;AAAA,MACF,KAAK;AACH,YAAI,aAAa;AACf,cAAI,MAAM,KAAK,MAAM,SAAS,YAAY,UAAU,YAAY;AAC9D,yBAAa;AACb;AAAA,UACF;AACA,gBAAM,WAAW;AACjB,mBAAS,MAAM;AACf,mBAAS,QAAQ;AAAA,QACnB,OAAO;AACL,gBAAM,YAAY;AAClB,gBAAM,WAAW;AACjB,cAAI,MAAM,KAAK,GAAG;AAChB,gBAAI,QAAQ,UAAU;AACpB,uBAAS,MAAM;AAAA,YACjB,WAAW,MAAM,KAAK,KAAK,UAAU,WAAW;AAC9C,2BAAa;AAAA,YACf;AAAA,UACF,OAAO;AACL,qBAAS,MAAM;AAAA,UACjB;AACA,cAAI,MAAM,KAAK,GAAG;AAChB,gBAAI,SAAS,WAAW;AACtB,wBAAU,MAAM;AAAA,YAClB;AAAA,UACF,OAAO;AACL,sBAAU,MAAM;AAAA,UAClB;AAAA,QACF;AACA,YAAI,QAAQ,KAAK,SAAS,GAAG;AAC3B,mBAAS;AACT,mBAAS,CAAC;AACV,kBAAQ,CAAC;AACT,iBAAO;AACP,kBAAQ;AAAA,QACV,WAAW,QAAQ,GAAG;AACpB,mBAAS;AACT,kBAAQ,CAAC;AACT,kBAAQ;AAAA,QACV,WAAW,SAAS,GAAG;AACrB,mBAAS;AACT,mBAAS,CAAC;AACV,iBAAO;AAAA,QACT;AACA;AAAA,MAGF,KAAK;AACH,aAAK,KAAK,MAAM,GAAG,MAAM,CAAC;AAC1B,qBAAa;AACb;AAAA,MAGF,KAAK;AACH,aAAK,KAAK,gBAAgB,QAAQ,GAAG,KAAK;AAC1C,qBAAa;AACb;AAAA,MAGF,KAAK;AACH,YAAI,CAAC,MAAM,KAAK,CAAC,MAAM,GAAG;AACxB,uBAAa;AACb;AAAA,QACF;AACA,iBAAS,UAAU,KAAK,OAAO;AAC/B,eAAO,QAAQ,SAAS,OAAO;AAC/B,cAAM,QAAQ,SAAS,OAAO;AAC9B,gBAAQ,YAAY;AACpB,iBAAS,YAAY;AACrB,YAAI,MAAM,IAAI,GAAG;AACf,mBAAS,MAAM,IAAI,IAAI,oBAAoB;AAAA,QAC7C,WAAW,MAAM,IAAI,GAAG;AACtB,kBAAQ;AACR,mBAAS,MAAM,IAAI,IAAI,oBAAoB;AAAA,QAC7C;AACA,YAAI,MAAM,IAAI,GAAG;AACf,iBAAO;AAAA,QACT;AAGA,YAAI,CAAC,KAAK,SAAS;AACjB,sBAAY,KAAK,SAAS,YAAY;AACtC,eAAK,UAAU;AACf,cAAI,KAAK,SAAS;AAChB,iBAAK,aAAa,MAAM,IAAI;AAAA,UAC9B;AAAA,QACF;AACA;AAAA,IACJ;AACA,QAAI,YAAY;AACd,kBAAY,QAAQ;AACpB,kBAAY,SAAS;AACrB,kBAAY,OAAO;AACnB,kBAAY,MAAM;AAClB,WAAK,SAAS;AACd,WAAK,cAAc;AAAA,IACrB;AAGA,YAAQ,UAAU,SAAU,GAAG;AAC7B,QAAE,SAAS,EAAE;AACb,QAAE,SAAS,EAAE;AAAA,IACf,CAAC;AAAA,EACH;AACF;AAEA,IAAI,UAAU;AAAA;AAAA,EAEZ,MAAM,SAAS,OAAO;AACpB,QAAI,KAAK,SAAS,CAAC,KAAK,WAAW,CAAC,KAAK,UAAU;AACjD,WAAK,UAAU;AACf,WAAK,aAAa,MAAM,IAAI;AAC5B,UAAI,KAAK,QAAQ,OAAO;AACtB,iBAAS,KAAK,SAAS,WAAW;AAAA,MACpC;AACA,kBAAY,KAAK,SAAS,YAAY;AACtC,WAAK,eAAe,KAAK,kBAAkB;AAAA,IAC7C;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,OAAO,SAAS,QAAQ;AACtB,QAAI,KAAK,SAAS,CAAC,KAAK,UAAU;AAChC,WAAK,YAAY,OAAO,CAAC,GAAG,KAAK,gBAAgB;AACjD,WAAK,aAAa,OAAO,CAAC,GAAG,KAAK,iBAAiB;AACnD,WAAK,cAAc,OAAO,CAAC,GAAG,KAAK,kBAAkB;AACrD,WAAK,aAAa;AAClB,UAAI,KAAK,SAAS;AAChB,aAAK,cAAc;AAAA,MACrB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,OAAO,SAAS,QAAQ;AACtB,QAAI,KAAK,WAAW,CAAC,KAAK,UAAU;AAClC,aAAO,KAAK,aAAa;AAAA,QACvB,MAAM;AAAA,QACN,KAAK;AAAA,QACL,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,CAAC;AACD,WAAK,UAAU;AACf,WAAK,cAAc;AACnB,WAAK,YAAY,MAAM,IAAI;AAG3B,WAAK,aAAa;AAClB,kBAAY,KAAK,SAAS,WAAW;AACrC,eAAS,KAAK,SAAS,YAAY;AAAA,IACrC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,SAAS,QAAQ,KAAK;AAC7B,QAAI,cAAc,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACtF,QAAI,CAAC,KAAK,YAAY,KAAK;AACzB,UAAI,KAAK,OAAO;AACd,aAAK,QAAQ,MAAM;AAAA,MACrB;AACA,UAAI,aAAa;AACf,aAAK,MAAM;AACX,aAAK,MAAM,MAAM;AACjB,YAAI,KAAK,OAAO;AACd,eAAK,aAAa,MAAM;AACxB,kBAAQ,KAAK,UAAU,SAAU,SAAS;AACxC,oBAAQ,qBAAqB,KAAK,EAAE,CAAC,EAAE,MAAM;AAAA,UAC/C,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AACL,YAAI,KAAK,OAAO;AACd,eAAK,WAAW;AAAA,QAClB;AACA,aAAK,QAAQ,OAAO;AACpB,aAAK,SAAS;AACd,aAAK,KAAK,GAAG;AAAA,MACf;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,QAAQ,SAAS,SAAS;AACxB,QAAI,KAAK,SAAS,KAAK,UAAU;AAC/B,WAAK,WAAW;AAChB,kBAAY,KAAK,SAAS,cAAc;AAAA,IAC1C;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,SAAS,SAAS,UAAU;AAC1B,QAAI,KAAK,SAAS,CAAC,KAAK,UAAU;AAChC,WAAK,WAAW;AAChB,eAAS,KAAK,SAAS,cAAc;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,SAAS,UAAU;AAC1B,QAAI,UAAU,KAAK;AACnB,QAAI,CAAC,QAAQ,SAAS,GAAG;AACvB,aAAO;AAAA,IACT;AACA,YAAQ,SAAS,IAAI;AACrB,QAAI,KAAK,SAAS,KAAK,UAAU;AAC/B,cAAQ,MAAM,KAAK;AAAA,IACrB;AACA,SAAK,SAAS;AACd,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,SAAS,KAAK,SAAS;AAC3B,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAClF,QAAI,mBAAmB,KAAK,YAC1B,OAAO,iBAAiB,MACxB,MAAM,iBAAiB;AACzB,WAAO,KAAK,OAAO,YAAY,OAAO,IAAI,UAAU,OAAO,OAAO,OAAO,GAAG,YAAY,OAAO,IAAI,UAAU,MAAM,OAAO,OAAO,CAAC;AAAA,EACpI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,SAAS,OAAO,GAAG;AACzB,QAAI,IAAI,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAC5E,QAAI,aAAa,KAAK;AACtB,QAAI,UAAU;AACd,QAAI,OAAO,CAAC;AACZ,QAAI,OAAO,CAAC;AACZ,QAAI,KAAK,SAAS,CAAC,KAAK,YAAY,KAAK,QAAQ,SAAS;AACxD,UAAI,SAAS,CAAC,GAAG;AACf,mBAAW,OAAO;AAClB,kBAAU;AAAA,MACZ;AACA,UAAI,SAAS,CAAC,GAAG;AACf,mBAAW,MAAM;AACjB,kBAAU;AAAA,MACZ;AACA,UAAI,SAAS;AACX,aAAK,aAAa,IAAI;AAAA,MACxB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,SAAS,KAAK,OAAO,gBAAgB;AACzC,QAAI,aAAa,KAAK;AACtB,YAAQ,OAAO,KAAK;AACpB,QAAI,QAAQ,GAAG;AACb,cAAQ,KAAK,IAAI;AAAA,IACnB,OAAO;AACL,cAAQ,IAAI;AAAA,IACd;AACA,WAAO,KAAK,OAAO,WAAW,QAAQ,QAAQ,WAAW,cAAc,MAAM,cAAc;AAAA,EAC7F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,SAAS,OAAO,OAAO,OAAO,gBAAgB;AACpD,QAAI,UAAU,KAAK,SACjB,aAAa,KAAK;AACpB,QAAI,QAAQ,WAAW,OACrB,SAAS,WAAW,QACpB,eAAe,WAAW,cAC1B,gBAAgB,WAAW;AAC7B,YAAQ,OAAO,KAAK;AACpB,QAAI,SAAS,KAAK,KAAK,SAAS,CAAC,KAAK,YAAY,QAAQ,UAAU;AAClE,UAAI,WAAW,eAAe;AAC9B,UAAI,YAAY,gBAAgB;AAChC,UAAI,cAAc,KAAK,SAAS,YAAY;AAAA,QAC1C;AAAA,QACA,UAAU,QAAQ;AAAA,QAClB,eAAe;AAAA,MACjB,CAAC,MAAM,OAAO;AACZ,eAAO;AAAA,MACT;AACA,UAAI,gBAAgB;AAClB,YAAI,WAAW,KAAK;AACpB,YAAI,SAAS,UAAU,KAAK,OAAO;AACnC,YAAI,SAAS,YAAY,OAAO,KAAK,QAAQ,EAAE,SAAS,kBAAkB,QAAQ,IAAI;AAAA,UACpF,OAAO,eAAe;AAAA,UACtB,OAAO,eAAe;AAAA,QACxB;AAGA,mBAAW,SAAS,WAAW,WAAW,OAAO,QAAQ,OAAO,OAAO,WAAW,QAAQ;AAC1F,mBAAW,QAAQ,YAAY,YAAY,OAAO,QAAQ,OAAO,MAAM,WAAW,OAAO;AAAA,MAC3F,WAAW,cAAc,KAAK,KAAK,SAAS,MAAM,CAAC,KAAK,SAAS,MAAM,CAAC,GAAG;AACzE,mBAAW,SAAS,WAAW,WAAW,MAAM,IAAI,WAAW,QAAQ;AACvE,mBAAW,QAAQ,YAAY,YAAY,MAAM,IAAI,WAAW,OAAO;AAAA,MACzE,OAAO;AAEL,mBAAW,SAAS,WAAW,SAAS;AACxC,mBAAW,QAAQ,YAAY,UAAU;AAAA,MAC3C;AACA,iBAAW,QAAQ;AACnB,iBAAW,SAAS;AACpB,WAAK,aAAa,IAAI;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,SAAS,OAAO,QAAQ;AAC9B,WAAO,KAAK,UAAU,KAAK,UAAU,UAAU,KAAK,OAAO,MAAM,CAAC;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,SAAS,SAAS,QAAQ;AAClC,aAAS,OAAO,MAAM;AACtB,QAAI,SAAS,MAAM,KAAK,KAAK,SAAS,CAAC,KAAK,YAAY,KAAK,QAAQ,WAAW;AAC9E,WAAK,UAAU,SAAS,SAAS;AACjC,WAAK,aAAa,MAAM,IAAI;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,SAAS,OAAO,SAAS;AAC/B,QAAIJ,UAAS,KAAK,UAAU;AAC5B,WAAO,KAAK,MAAM,SAAS,SAASA,OAAM,IAAIA,UAAS,CAAC;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,SAAS,OAAO,SAAS;AAC/B,QAAID,UAAS,KAAK,UAAU;AAC5B,WAAO,KAAK,MAAM,SAASA,OAAM,IAAIA,UAAS,GAAG,OAAO;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,SAAS,MAAMA,SAAQ;AAC5B,QAAIC,UAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAID;AACjF,QAAI,YAAY,KAAK;AACrB,QAAI,cAAc;AAClB,IAAAA,UAAS,OAAOA,OAAM;AACtB,IAAAC,UAAS,OAAOA,OAAM;AACtB,QAAI,KAAK,SAAS,CAAC,KAAK,YAAY,KAAK,QAAQ,UAAU;AACzD,UAAI,SAASD,OAAM,GAAG;AACpB,kBAAU,SAASA;AACnB,sBAAc;AAAA,MAChB;AACA,UAAI,SAASC,OAAM,GAAG;AACpB,kBAAU,SAASA;AACnB,sBAAc;AAAA,MAChB;AACA,UAAI,aAAa;AACf,aAAK,aAAa,MAAM,IAAI;AAAA,MAC9B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,SAASK,WAAU;AAC1B,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAClF,QAAI,UAAU,KAAK,SACjB,YAAY,KAAK,WACjB,aAAa,KAAK,YAClB,cAAc,KAAK;AACrB,QAAI;AACJ,QAAI,KAAK,SAAS,KAAK,SAAS;AAC9B,aAAO;AAAA,QACL,GAAG,YAAY,OAAO,WAAW;AAAA,QACjC,GAAG,YAAY,MAAM,WAAW;AAAA,QAChC,OAAO,YAAY;AAAA,QACnB,QAAQ,YAAY;AAAA,MACtB;AACA,UAAI,QAAQ,UAAU,QAAQ,UAAU;AACxC,cAAQ,MAAM,SAAU,GAAG,GAAG;AAC5B,aAAK,CAAC,IAAI,IAAI;AAAA,MAChB,CAAC;AACD,UAAI,SAAS;AAGX,YAAI,SAAS,KAAK,MAAM,KAAK,IAAI,KAAK,MAAM;AAC5C,YAAI,QAAQ,KAAK,MAAM,KAAK,IAAI,KAAK,KAAK;AAC1C,aAAK,IAAI,KAAK,MAAM,KAAK,CAAC;AAC1B,aAAK,IAAI,KAAK,MAAM,KAAK,CAAC;AAC1B,aAAK,QAAQ,QAAQ,KAAK;AAC1B,aAAK,SAAS,SAAS,KAAK;AAAA,MAC9B;AAAA,IACF,OAAO;AACL,aAAO;AAAA,QACL,GAAG;AAAA,QACH,GAAG;AAAA,QACH,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,IACF;AACA,QAAI,QAAQ,WAAW;AACrB,WAAK,SAAS,UAAU,UAAU;AAAA,IACpC;AACA,QAAI,QAAQ,UAAU;AACpB,WAAK,SAAS,UAAU,UAAU;AAClC,WAAK,SAAS,UAAU,UAAU;AAAA,IACpC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,SAASC,SAAQ,MAAM;AAC9B,QAAI,UAAU,KAAK,SACjB,YAAY,KAAK,WACjB,aAAa,KAAK;AACpB,QAAI,cAAc,CAAC;AACnB,QAAI,KAAK,SAAS,CAAC,KAAK,YAAY,cAAc,IAAI,GAAG;AACvD,UAAI,cAAc;AAClB,UAAI,QAAQ,WAAW;AACrB,YAAI,SAAS,KAAK,MAAM,KAAK,KAAK,WAAW,UAAU,QAAQ;AAC7D,oBAAU,SAAS,KAAK;AACxB,wBAAc;AAAA,QAChB;AAAA,MACF;AACA,UAAI,QAAQ,UAAU;AACpB,YAAI,SAAS,KAAK,MAAM,KAAK,KAAK,WAAW,UAAU,QAAQ;AAC7D,oBAAU,SAAS,KAAK;AACxB,wBAAc;AAAA,QAChB;AACA,YAAI,SAAS,KAAK,MAAM,KAAK,KAAK,WAAW,UAAU,QAAQ;AAC7D,oBAAU,SAAS,KAAK;AACxB,wBAAc;AAAA,QAChB;AAAA,MACF;AACA,UAAI,aAAa;AACf,aAAK,aAAa,MAAM,IAAI;AAAA,MAC9B;AACA,UAAI,QAAQ,UAAU,QAAQ,UAAU;AACxC,UAAI,SAAS,KAAK,CAAC,GAAG;AACpB,oBAAY,OAAO,KAAK,IAAI,QAAQ,WAAW;AAAA,MACjD;AACA,UAAI,SAAS,KAAK,CAAC,GAAG;AACpB,oBAAY,MAAM,KAAK,IAAI,QAAQ,WAAW;AAAA,MAChD;AACA,UAAI,SAAS,KAAK,KAAK,GAAG;AACxB,oBAAY,QAAQ,KAAK,QAAQ;AAAA,MACnC;AACA,UAAI,SAAS,KAAK,MAAM,GAAG;AACzB,oBAAY,SAAS,KAAK,SAAS;AAAA,MACrC;AACA,WAAK,eAAe,WAAW;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,SAAS,mBAAmB;AAC5C,WAAO,KAAK,QAAQ,OAAO,CAAC,GAAG,KAAK,aAAa,IAAI,CAAC;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,SAAS,eAAe;AACpC,WAAO,KAAK,QAAQ,OAAO,CAAC,GAAG,KAAK,SAAS,IAAI,CAAC;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,SAAS,gBAAgB;AACtC,QAAI,aAAa,KAAK;AACtB,QAAI,OAAO,CAAC;AACZ,QAAI,KAAK,OAAO;AACd,cAAQ,CAAC,QAAQ,OAAO,SAAS,UAAU,gBAAgB,eAAe,GAAG,SAAU,GAAG;AACxF,aAAK,CAAC,IAAI,WAAW,CAAC;AAAA,MACxB,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,SAAS,cAAc,MAAM;AAC1C,QAAI,aAAa,KAAK;AACtB,QAAI,cAAc,WAAW;AAC7B,QAAI,KAAK,SAAS,CAAC,KAAK,YAAY,cAAc,IAAI,GAAG;AACvD,UAAI,SAAS,KAAK,IAAI,GAAG;AACvB,mBAAW,OAAO,KAAK;AAAA,MACzB;AACA,UAAI,SAAS,KAAK,GAAG,GAAG;AACtB,mBAAW,MAAM,KAAK;AAAA,MACxB;AACA,UAAI,SAAS,KAAK,KAAK,GAAG;AACxB,mBAAW,QAAQ,KAAK;AACxB,mBAAW,SAAS,KAAK,QAAQ;AAAA,MACnC,WAAW,SAAS,KAAK,MAAM,GAAG;AAChC,mBAAW,SAAS,KAAK;AACzB,mBAAW,QAAQ,KAAK,SAAS;AAAA,MACnC;AACA,WAAK,aAAa,IAAI;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,SAAS,iBAAiB;AACxC,QAAI,cAAc,KAAK;AACvB,QAAI;AACJ,QAAI,KAAK,SAAS,KAAK,SAAS;AAC9B,aAAO;AAAA,QACL,MAAM,YAAY;AAAA,QAClB,KAAK,YAAY;AAAA,QACjB,OAAO,YAAY;AAAA,QACnB,QAAQ,YAAY;AAAA,MACtB;AAAA,IACF;AACA,WAAO,QAAQ,CAAC;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,SAAS,eAAe,MAAM;AAC5C,QAAI,cAAc,KAAK;AACvB,QAAI,cAAc,KAAK,QAAQ;AAC/B,QAAI;AACJ,QAAI;AACJ,QAAI,KAAK,SAAS,KAAK,WAAW,CAAC,KAAK,YAAY,cAAc,IAAI,GAAG;AACvE,UAAI,SAAS,KAAK,IAAI,GAAG;AACvB,oBAAY,OAAO,KAAK;AAAA,MAC1B;AACA,UAAI,SAAS,KAAK,GAAG,GAAG;AACtB,oBAAY,MAAM,KAAK;AAAA,MACzB;AACA,UAAI,SAAS,KAAK,KAAK,KAAK,KAAK,UAAU,YAAY,OAAO;AAC5D,uBAAe;AACf,oBAAY,QAAQ,KAAK;AAAA,MAC3B;AACA,UAAI,SAAS,KAAK,MAAM,KAAK,KAAK,WAAW,YAAY,QAAQ;AAC/D,wBAAgB;AAChB,oBAAY,SAAS,KAAK;AAAA,MAC5B;AACA,UAAI,aAAa;AACf,YAAI,cAAc;AAChB,sBAAY,SAAS,YAAY,QAAQ;AAAA,QAC3C,WAAW,eAAe;AACxB,sBAAY,QAAQ,YAAY,SAAS;AAAA,QAC3C;AAAA,MACF;AACA,WAAK,cAAc;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,SAAS,mBAAmB;AAC5C,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,QAAI,CAAC,KAAK,SAAS,CAAC,OAAO,mBAAmB;AAC5C,aAAO;AAAA,IACT;AACA,QAAI,aAAa,KAAK;AACtB,QAAI,SAAS,gBAAgB,KAAK,OAAO,KAAK,WAAW,YAAY,OAAO;AAG5E,QAAI,CAAC,KAAK,SAAS;AACjB,aAAO;AAAA,IACT;AACA,QAAI,gBAAgB,KAAK,QAAQ,QAAQ,OAAO,GAC9C,WAAW,cAAc,GACzB,WAAW,cAAc,GACzB,eAAe,cAAc,OAC7B,gBAAgB,cAAc;AAChC,QAAI,QAAQ,OAAO,QAAQ,KAAK,MAAM,WAAW,YAAY;AAC7D,QAAI,UAAU,GAAG;AACf,kBAAY;AACZ,kBAAY;AACZ,sBAAgB;AAChB,uBAAiB;AAAA,IACnB;AACA,QAAI,cAAc,eAAe;AACjC,QAAI,WAAW,iBAAiB;AAAA,MAC9B;AAAA,MACA,OAAO,QAAQ,YAAY;AAAA,MAC3B,QAAQ,QAAQ,aAAa;AAAA,IAC/B,CAAC;AACD,QAAI,WAAW,iBAAiB;AAAA,MAC9B;AAAA,MACA,OAAO,QAAQ,YAAY;AAAA,MAC3B,QAAQ,QAAQ,aAAa;AAAA,IAC/B,GAAG,OAAO;AACV,QAAI,oBAAoB,iBAAiB;AAAA,MACrC;AAAA,MACA,OAAO,QAAQ,UAAU,UAAU,IAAI,OAAO,QAAQ;AAAA,MACtD,QAAQ,QAAQ,WAAW,UAAU,IAAI,OAAO,SAAS;AAAA,IAC3D,CAAC,GACD,QAAQ,kBAAkB,OAC1B,SAAS,kBAAkB;AAC7B,YAAQ,KAAK,IAAI,SAAS,OAAO,KAAK,IAAI,SAAS,OAAO,KAAK,CAAC;AAChE,aAAS,KAAK,IAAI,SAAS,QAAQ,KAAK,IAAI,SAAS,QAAQ,MAAM,CAAC;AACpE,QAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,QAAI,UAAU,OAAO,WAAW,IAAI;AACpC,WAAO,QAAQ,uBAAuB,KAAK;AAC3C,WAAO,SAAS,uBAAuB,MAAM;AAC7C,YAAQ,YAAY,QAAQ,aAAa;AACzC,YAAQ,SAAS,GAAG,GAAG,OAAO,MAAM;AACpC,QAAI,wBAAwB,QAAQ,uBAClC,wBAAwB,0BAA0B,SAAS,OAAO,uBAClE,wBAAwB,QAAQ;AAClC,YAAQ,wBAAwB;AAChC,QAAI,uBAAuB;AACzB,cAAQ,wBAAwB;AAAA,IAClC;AAGA,QAAI,cAAc,OAAO;AACzB,QAAI,eAAe,OAAO;AAG1B,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI;AACJ,QAAI;AAGJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,QAAQ,CAAC,gBAAgB,OAAO,aAAa;AAC/C,aAAO;AACP,iBAAW;AACX,aAAO;AACP,iBAAW;AAAA,IACb,WAAW,QAAQ,GAAG;AACpB,aAAO,CAAC;AACR,aAAO;AACP,iBAAW,KAAK,IAAI,aAAa,eAAe,IAAI;AACpD,iBAAW;AAAA,IACb,WAAW,QAAQ,aAAa;AAC9B,aAAO;AACP,iBAAW,KAAK,IAAI,cAAc,cAAc,IAAI;AACpD,iBAAW;AAAA,IACb;AACA,QAAI,YAAY,KAAK,QAAQ,CAAC,iBAAiB,OAAO,cAAc;AAClE,aAAO;AACP,kBAAY;AACZ,aAAO;AACP,kBAAY;AAAA,IACd,WAAW,QAAQ,GAAG;AACpB,aAAO,CAAC;AACR,aAAO;AACP,kBAAY,KAAK,IAAI,cAAc,gBAAgB,IAAI;AACvD,kBAAY;AAAA,IACd,WAAW,QAAQ,cAAc;AAC/B,aAAO;AACP,kBAAY,KAAK,IAAI,eAAe,eAAe,IAAI;AACvD,kBAAY;AAAA,IACd;AACA,QAAI,SAAS,CAAC,MAAM,MAAM,UAAU,SAAS;AAG7C,QAAI,WAAW,KAAK,YAAY,GAAG;AACjC,UAAIC,SAAQ,QAAQ;AACpB,aAAO,KAAK,OAAOA,QAAO,OAAOA,QAAO,WAAWA,QAAO,YAAYA,MAAK;AAAA,IAC7E;AAIA,YAAQ,UAAU,MAAM,SAAS,CAAC,MAAM,EAAE,OAAO,mBAAmB,OAAO,IAAI,SAAU,OAAO;AAC9F,aAAO,KAAK,MAAM,uBAAuB,KAAK,CAAC;AAAA,IACjD,CAAC,CAAC,CAAC,CAAC;AACJ,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,SAAS,eAAe,aAAa;AACnD,QAAI,UAAU,KAAK;AACnB,QAAI,CAAC,KAAK,YAAY,CAAC,YAAY,WAAW,GAAG;AAE/C,cAAQ,cAAc,KAAK,IAAI,GAAG,WAAW,KAAK;AAClD,UAAI,KAAK,OAAO;AACd,aAAK,YAAY;AACjB,YAAI,KAAK,SAAS;AAChB,eAAK,cAAc;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,SAAS,YAAY,MAAM;AACtC,QAAI,UAAU,KAAK,SACjB,UAAU,KAAK,SACf,OAAO,KAAK;AACd,QAAI,KAAK,SAAS,CAAC,KAAK,UAAU;AAChC,UAAI,YAAY,SAAS;AACzB,UAAI,UAAU,QAAQ,WAAW,SAAS;AAC1C,aAAO,aAAa,UAAU,OAAO;AACrC,cAAQ,WAAW;AACnB,cAAQ,SAAS,aAAa,IAAI;AAClC,kBAAY,SAAS,YAAY,SAAS;AAC1C,kBAAY,SAAS,YAAY,OAAO;AACxC,UAAI,CAAC,QAAQ,gBAAgB;AAE3B,gBAAQ,MAAM,aAAa,IAAI;AAC/B,oBAAY,MAAM,YAAY,SAAS;AACvC,oBAAY,MAAM,YAAY,OAAO;AAAA,MACvC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAI,iBAAiB,OAAO;AAC5B,IAAI,UAAuB,WAAY;AAMrC,WAASC,SAAQ,SAAS;AACxB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,oBAAgB,MAAMA,QAAO;AAC7B,QAAI,CAAC,WAAW,CAAC,gBAAgB,KAAK,QAAQ,OAAO,GAAG;AACtD,YAAM,IAAI,MAAM,0EAA0E;AAAA,IAC5F;AACA,SAAK,UAAU;AACf,SAAK,UAAU,OAAO,CAAC,GAAG,UAAU,cAAc,OAAO,KAAK,OAAO;AACrE,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,WAAW,CAAC;AACjB,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,KAAK;AAAA,EACZ;AACA,SAAO,aAAaA,UAAS,CAAC;AAAA,IAC5B,KAAK;AAAA,IACL,OAAO,SAAS,OAAO;AACrB,UAAI,UAAU,KAAK;AACnB,UAAI,UAAU,QAAQ,QAAQ,YAAY;AAC1C,UAAI;AACJ,UAAI,QAAQ,SAAS,GAAG;AACtB;AAAA,MACF;AACA,cAAQ,SAAS,IAAI;AACrB,UAAI,YAAY,OAAO;AACrB,aAAK,QAAQ;AAGb,cAAM,QAAQ,aAAa,KAAK,KAAK;AACrC,aAAK,cAAc;AAGnB,YAAI,CAAC,KAAK;AACR;AAAA,QACF;AAGA,cAAM,QAAQ;AAAA,MAChB,WAAW,YAAY,YAAY,OAAO,mBAAmB;AAC3D,cAAM,QAAQ,UAAU;AAAA,MAC1B;AACA,WAAK,KAAK,GAAG;AAAA,IACf;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,KAAK,KAAK;AACxB,UAAI,QAAQ;AACZ,UAAI,CAAC,KAAK;AACR;AAAA,MACF;AACA,WAAK,MAAM;AACX,WAAK,YAAY,CAAC;AAClB,UAAI,UAAU,KAAK,SACjB,UAAU,KAAK;AACjB,UAAI,CAAC,QAAQ,aAAa,CAAC,QAAQ,UAAU;AAC3C,gBAAQ,mBAAmB;AAAA,MAC7B;AAGA,UAAI,CAAC,QAAQ,oBAAoB,CAAC,OAAO,aAAa;AACpD,aAAK,MAAM;AACX;AAAA,MACF;AAGA,UAAI,gBAAgB,KAAK,GAAG,GAAG;AAE7B,YAAI,qBAAqB,KAAK,GAAG,GAAG;AAClC,eAAK,KAAK,qBAAqB,GAAG,CAAC;AAAA,QACrC,OAAO;AAGL,eAAK,MAAM;AAAA,QACb;AACA;AAAA,MACF;AAIA,UAAI,MAAM,IAAI,eAAe;AAC7B,UAAI,QAAQ,KAAK,MAAM,KAAK,IAAI;AAChC,WAAK,YAAY;AACjB,WAAK,MAAM;AAMX,UAAI,UAAU;AACd,UAAI,UAAU;AACd,UAAI,YAAY;AAChB,UAAI,aAAa,WAAY;AAE3B,YAAI,IAAI,kBAAkB,cAAc,MAAM,gBAAgB;AAC5D,cAAI,MAAM;AAAA,QACZ;AAAA,MACF;AACA,UAAI,SAAS,WAAY;AACvB,cAAM,KAAK,IAAI,QAAQ;AAAA,MACzB;AACA,UAAI,YAAY,WAAY;AAC1B,cAAM,YAAY;AAClB,cAAM,MAAM;AAAA,MACd;AAGA,UAAI,QAAQ,oBAAoB,iBAAiB,GAAG,KAAK,QAAQ,aAAa;AAC5E,cAAM,aAAa,GAAG;AAAA,MACxB;AAGA,UAAI,KAAK,OAAO,KAAK,IAAI;AACzB,UAAI,eAAe;AACnB,UAAI,kBAAkB,QAAQ,gBAAgB;AAC9C,UAAI,KAAK;AAAA,IACX;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,KAAK,aAAa;AAChC,UAAI,UAAU,KAAK,SACjB,YAAY,KAAK;AAInB,UAAI,cAAc,uBAAuB,WAAW;AACpD,UAAIV,UAAS;AACb,UAAIC,UAAS;AACb,UAAIC,UAAS;AACb,UAAI,cAAc,GAAG;AAEnB,aAAK,MAAM,qBAAqB,aAAa,cAAc;AAC3D,YAAI,oBAAoB,iBAAiB,WAAW;AACpD,QAAAF,UAAS,kBAAkB;AAC3B,QAAAC,UAAS,kBAAkB;AAC3B,QAAAC,UAAS,kBAAkB;AAAA,MAC7B;AACA,UAAI,QAAQ,WAAW;AACrB,kBAAU,SAASF;AAAA,MACrB;AACA,UAAI,QAAQ,UAAU;AACpB,kBAAU,SAASC;AACnB,kBAAU,SAASC;AAAA,MACrB;AACA,WAAK,MAAM;AAAA,IACb;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,QAAQ;AACtB,UAAI,UAAU,KAAK,SACjB,MAAM,KAAK;AACb,UAAI,cAAc,QAAQ;AAC1B,UAAI,iBAAiB;AACrB,UAAI,KAAK,QAAQ,oBAAoB,iBAAiB,GAAG,GAAG;AAC1D,YAAI,CAAC,aAAa;AAChB,wBAAc;AAAA,QAChB;AAGA,yBAAiB,aAAa,GAAG;AAAA,MACnC;AACA,WAAK,cAAc;AACnB,WAAK,iBAAiB;AACtB,UAAI,QAAQ,SAAS,cAAc,KAAK;AACxC,UAAI,aAAa;AACf,cAAM,cAAc;AAAA,MACtB;AACA,YAAM,MAAM,kBAAkB;AAC9B,YAAM,MAAM,QAAQ,OAAO;AAC3B,WAAK,QAAQ;AACb,YAAM,SAAS,KAAK,MAAM,KAAK,IAAI;AACnC,YAAM,UAAU,KAAK,KAAK,KAAK,IAAI;AACnC,eAAS,OAAO,UAAU;AAC1B,cAAQ,WAAW,aAAa,OAAO,QAAQ,WAAW;AAAA,IAC5D;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,QAAQ;AACtB,UAAI,SAAS;AACb,UAAI,QAAQ,KAAK;AACjB,YAAM,SAAS;AACf,YAAM,UAAU;AAChB,WAAK,SAAS;AAId,UAAI,cAAc,OAAO,aAAa,sCAAsC,KAAK,OAAO,UAAU,SAAS;AAC3G,UAAI,OAAO,SAASS,MAAK,cAAc,eAAe;AACpD,eAAO,OAAO,WAAW;AAAA,UACvB;AAAA,UACA;AAAA,UACA,aAAa,eAAe;AAAA,QAC9B,CAAC;AACD,eAAO,mBAAmB,OAAO,CAAC,GAAG,OAAO,SAAS;AACrD,eAAO,SAAS;AAChB,eAAO,QAAQ;AACf,eAAO,MAAM;AAAA,MACf;AAGA,UAAI,MAAM,gBAAgB,CAAC,aAAa;AACtC,aAAK,MAAM,cAAc,MAAM,aAAa;AAC5C;AAAA,MACF;AACA,UAAI,cAAc,SAAS,cAAc,KAAK;AAC9C,UAAI,OAAO,SAAS,QAAQ,SAAS;AACrC,WAAK,cAAc;AACnB,kBAAY,SAAS,WAAY;AAC/B,aAAK,YAAY,OAAO,YAAY,MAAM;AAC1C,YAAI,CAAC,aAAa;AAChB,eAAK,YAAY,WAAW;AAAA,QAC9B;AAAA,MACF;AACA,kBAAY,MAAM,MAAM;AAIxB,UAAI,CAAC,aAAa;AAChB,oBAAY,MAAM,UAAU;AAC5B,aAAK,YAAY,WAAW;AAAA,MAC9B;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,OAAO;AACrB,UAAI,QAAQ,KAAK;AACjB,YAAM,SAAS;AACf,YAAM,UAAU;AAChB,YAAM,WAAW,YAAY,KAAK;AAClC,WAAK,QAAQ;AAAA,IACf;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,QAAQ;AACtB,UAAI,CAAC,KAAK,SAAS,KAAK,OAAO;AAC7B;AAAA,MACF;AACA,UAAI,UAAU,KAAK,SACjB,UAAU,KAAK,SACf,QAAQ,KAAK;AAGf,UAAI,YAAY,QAAQ;AACxB,UAAI,WAAW,SAAS,cAAc,KAAK;AAC3C,eAAS,YAAY;AACrB,UAAI,UAAU,SAAS,cAAc,IAAI,OAAO,WAAW,YAAY,CAAC;AACxE,UAAI,SAAS,QAAQ,cAAc,IAAI,OAAO,WAAW,SAAS,CAAC;AACnE,UAAI,UAAU,QAAQ,cAAc,IAAI,OAAO,WAAW,WAAW,CAAC;AACtE,UAAI,UAAU,QAAQ,cAAc,IAAI,OAAO,WAAW,WAAW,CAAC;AACtE,UAAI,OAAO,QAAQ,cAAc,IAAI,OAAO,WAAW,OAAO,CAAC;AAC/D,WAAK,YAAY;AACjB,WAAK,UAAU;AACf,WAAK,SAAS;AACd,WAAK,UAAU;AACf,WAAK,UAAU;AACf,WAAK,UAAU,QAAQ,cAAc,IAAI,OAAO,WAAW,WAAW,CAAC;AACvE,WAAK,OAAO;AACZ,aAAO,YAAY,KAAK;AAGxB,eAAS,SAAS,YAAY;AAG9B,gBAAU,aAAa,SAAS,QAAQ,WAAW;AAGnD,kBAAY,OAAO,UAAU;AAC7B,WAAK,YAAY;AACjB,WAAK,KAAK;AACV,cAAQ,qBAAqB,KAAK,IAAI,GAAG,QAAQ,kBAAkB,KAAK;AACxE,cAAQ,cAAc,KAAK,IAAI,GAAG,QAAQ,WAAW,KAAK;AAC1D,cAAQ,WAAW,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,MAAM,QAAQ,QAAQ,CAAC,CAAC,KAAK;AAC7E,eAAS,SAAS,YAAY;AAC9B,UAAI,CAAC,QAAQ,QAAQ;AACnB,iBAAS,QAAQ,uBAAuB,GAAG,OAAO,WAAW,SAAS,CAAC,GAAG,YAAY;AAAA,MACxF;AACA,UAAI,CAAC,QAAQ,QAAQ;AACnB,iBAAS,QAAQ,uBAAuB,GAAG,OAAO,WAAW,SAAS,CAAC,GAAG,YAAY;AAAA,MACxF;AACA,UAAI,QAAQ,YAAY;AACtB,iBAAS,SAAS,GAAG,OAAO,WAAW,KAAK,CAAC;AAAA,MAC/C;AACA,UAAI,CAAC,QAAQ,WAAW;AACtB,iBAAS,MAAM,eAAe;AAAA,MAChC;AACA,UAAI,QAAQ,gBAAgB;AAC1B,iBAAS,MAAM,UAAU;AACzB,gBAAQ,MAAM,aAAa,UAAU;AAAA,MACvC;AACA,UAAI,CAAC,QAAQ,kBAAkB;AAC7B,iBAAS,QAAQ,uBAAuB,GAAG,OAAO,WAAW,OAAO,CAAC,GAAG,YAAY;AACpF,iBAAS,QAAQ,uBAAuB,GAAG,OAAO,WAAW,QAAQ,CAAC,GAAG,YAAY;AAAA,MACvF;AACA,WAAK,OAAO;AACZ,WAAK,QAAQ;AACb,WAAK,YAAY,QAAQ,QAAQ;AACjC,UAAI,QAAQ,UAAU;AACpB,aAAK,KAAK;AAAA,MACZ;AACA,WAAK,QAAQ,QAAQ,IAAI;AACzB,UAAI,WAAW,QAAQ,KAAK,GAAG;AAC7B,oBAAY,SAAS,aAAa,QAAQ,OAAO;AAAA,UAC/C,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AACA,oBAAc,SAAS,WAAW;AAAA,IACpC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU;AACxB,UAAI,CAAC,KAAK,OAAO;AACf;AAAA,MACF;AACA,WAAK,QAAQ;AACb,WAAK,OAAO;AACZ,WAAK,aAAa;AAClB,UAAI,aAAa,KAAK,QAAQ;AAC9B,UAAI,YAAY;AACd,mBAAW,YAAY,KAAK,OAAO;AAAA,MACrC;AACA,kBAAY,KAAK,SAAS,YAAY;AAAA,IACxC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,WAAW;AACzB,UAAI,KAAK,OAAO;AACd,aAAK,QAAQ;AACb,aAAK,QAAQ;AACb,aAAK,UAAU;AAAA,MACjB,WAAW,KAAK,QAAQ;AACtB,aAAK,YAAY,SAAS;AAC1B,aAAK,SAAS;AACd,aAAK,QAAQ;AAAA,MACf,WAAW,KAAK,WAAW;AACzB,aAAK,IAAI,UAAU;AACnB,aAAK,IAAI,MAAM;AAAA,MACjB,WAAW,KAAK,OAAO;AACrB,aAAK,KAAK;AAAA,MACZ;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMF,CAAC,GAAG,CAAC;AAAA,IACH,KAAK;AAAA,IACL,OAAO,SAAS,aAAa;AAC3B,aAAO,UAAU;AACjB,aAAOD;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY,SAAS;AACnC,aAAO,UAAU,cAAc,OAAO,KAAK,OAAO;AAAA,IACpD;AAAA,EACF,CAAC,CAAC;AACJ,EAAE;AACF,OAAO,QAAQ,WAAW,QAAQ,SAAS,QAAQ,UAAU,QAAQ,OAAO;", "names": ["r", "o", "isPositiveNumber", "assign", "listener", "rotate", "scaleX", "scaleY", "render", "preview", "change", "check", "getData", "setData", "scale", "C<PERSON>per", "done"]}