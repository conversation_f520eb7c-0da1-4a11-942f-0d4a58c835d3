{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/hcl/hcl.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    comments: {\n        lineComment: '#',\n        blockComment: ['/*', '*/']\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"', notIn: ['string'] }\n    ],\n    surroundingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' }\n    ]\n};\nexport var language = {\n    defaultToken: '',\n    tokenPostfix: '.hcl',\n    keywords: [\n        'var',\n        'local',\n        'path',\n        'for_each',\n        'any',\n        'string',\n        'number',\n        'bool',\n        'true',\n        'false',\n        'null',\n        'if ',\n        'else ',\n        'endif ',\n        'for ',\n        'in',\n        'endfor'\n    ],\n    operators: [\n        '=',\n        '>=',\n        '<=',\n        '==',\n        '!=',\n        '+',\n        '-',\n        '*',\n        '/',\n        '%',\n        '&&',\n        '||',\n        '!',\n        '<',\n        '>',\n        '?',\n        '...',\n        ':'\n    ],\n    symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n    escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n    terraformFunctions: /(abs|ceil|floor|log|max|min|pow|signum|chomp|format|formatlist|indent|join|lower|regex|regexall|replace|split|strrev|substr|title|trimspace|upper|chunklist|coalesce|coalescelist|compact|concat|contains|distinct|element|flatten|index|keys|length|list|lookup|map|matchkeys|merge|range|reverse|setintersection|setproduct|setunion|slice|sort|transpose|values|zipmap|base64decode|base64encode|base64gzip|csvdecode|jsondecode|jsonencode|urlencode|yamldecode|yamlencode|abspath|dirname|pathexpand|basename|file|fileexists|fileset|filebase64|templatefile|formatdate|timeadd|timestamp|base64sha256|base64sha512|bcrypt|filebase64sha256|filebase64sha512|filemd5|filemd1|filesha256|filesha512|md5|rsadecrypt|sha1|sha256|sha512|uuid|uuidv5|cidrhost|cidrnetmask|cidrsubnet|tobool|tolist|tomap|tonumber|toset|tostring)/,\n    terraformMainBlocks: /(module|data|terraform|resource|provider|variable|output|locals)/,\n    tokenizer: {\n        root: [\n            // highlight main blocks\n            [\n                /^@terraformMainBlocks([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)(\\{)/,\n                ['type', '', 'string', '', 'string', '', '@brackets']\n            ],\n            // highlight all the remaining blocks\n            [\n                /(\\w+[ \\t]+)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)(\\{)/,\n                ['identifier', '', 'string', '', 'string', '', '@brackets']\n            ],\n            // highlight block\n            [\n                /(\\w+[ \\t]+)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)(=)(\\{)/,\n                ['identifier', '', 'string', '', 'operator', '', '@brackets']\n            ],\n            // terraform general highlight - shared with expressions\n            { include: '@terraform' }\n        ],\n        terraform: [\n            // highlight terraform functions\n            [/@terraformFunctions(\\()/, ['type', '@brackets']],\n            // all other words are variables or keywords\n            [\n                /[a-zA-Z_]\\w*-*/,\n                {\n                    cases: {\n                        '@keywords': { token: 'keyword.$0' },\n                        '@default': 'variable'\n                    }\n                }\n            ],\n            { include: '@whitespace' },\n            { include: '@heredoc' },\n            // delimiters and operators\n            [/[{}()\\[\\]]/, '@brackets'],\n            [/[<>](?!@symbols)/, '@brackets'],\n            [\n                /@symbols/,\n                {\n                    cases: {\n                        '@operators': 'operator',\n                        '@default': ''\n                    }\n                }\n            ],\n            // numbers\n            [/\\d*\\d+[eE]([\\-+]?\\d+)?/, 'number.float'],\n            [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, 'number.float'],\n            [/\\d[\\d']*/, 'number'],\n            [/\\d/, 'number'],\n            [/[;,.]/, 'delimiter'],\n            // strings\n            [/\"/, 'string', '@string'],\n            [/'/, 'invalid']\n        ],\n        heredoc: [\n            [/<<[-]*\\s*[\"]?([\\w\\-]+)[\"]?/, { token: 'string.heredoc.delimiter', next: '@heredocBody.$1' }]\n        ],\n        heredocBody: [\n            [\n                /([\\w\\-]+)$/,\n                {\n                    cases: {\n                        '$1==$S2': [\n                            {\n                                token: 'string.heredoc.delimiter',\n                                next: '@popall'\n                            }\n                        ],\n                        '@default': 'string.heredoc'\n                    }\n                }\n            ],\n            [/./, 'string.heredoc']\n        ],\n        whitespace: [\n            [/[ \\t\\r\\n]+/, ''],\n            [/\\/\\*/, 'comment', '@comment'],\n            [/\\/\\/.*$/, 'comment'],\n            [/#.*$/, 'comment']\n        ],\n        comment: [\n            [/[^\\/*]+/, 'comment'],\n            [/\\*\\//, 'comment', '@pop'],\n            [/[\\/*]/, 'comment']\n        ],\n        string: [\n            [/\\$\\{/, { token: 'delimiter', next: '@stringExpression' }],\n            [/[^\\\\\"\\$]+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/\\\\./, 'string.escape.invalid'],\n            [/\"/, 'string', '@popall']\n        ],\n        stringInsideExpression: [\n            [/[^\\\\\"]+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/\\\\./, 'string.escape.invalid'],\n            [/\"/, 'string', '@pop']\n        ],\n        stringExpression: [\n            [/\\}/, { token: 'delimiter', next: '@pop' }],\n            [/\"/, 'string', '@stringInsideExpression'],\n            { include: '@terraform' }\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC7B;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,EAC/C;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,WAAW;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,EACT,SAAS;AAAA,EACT,oBAAoB;AAAA,EACpB,qBAAqB;AAAA,EACrB,WAAW;AAAA,IACP,MAAM;AAAA;AAAA,MAEF;AAAA,QACI;AAAA,QACA,CAAC,QAAQ,IAAI,UAAU,IAAI,UAAU,IAAI,WAAW;AAAA,MACxD;AAAA;AAAA,MAEA;AAAA,QACI;AAAA,QACA,CAAC,cAAc,IAAI,UAAU,IAAI,UAAU,IAAI,WAAW;AAAA,MAC9D;AAAA;AAAA,MAEA;AAAA,QACI;AAAA,QACA,CAAC,cAAc,IAAI,UAAU,IAAI,YAAY,IAAI,WAAW;AAAA,MAChE;AAAA;AAAA,MAEA,EAAE,SAAS,aAAa;AAAA,IAC5B;AAAA,IACA,WAAW;AAAA;AAAA,MAEP,CAAC,2BAA2B,CAAC,QAAQ,WAAW,CAAC;AAAA;AAAA,MAEjD;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,aAAa,EAAE,OAAO,aAAa;AAAA,YACnC,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA;AAAA,MAEtB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,oBAAoB,WAAW;AAAA,MAChC;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,cAAc;AAAA,YACd,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,CAAC,0BAA0B,cAAc;AAAA,MACzC,CAAC,4BAA4B,cAAc;AAAA,MAC3C,CAAC,YAAY,QAAQ;AAAA,MACrB,CAAC,MAAM,QAAQ;AAAA,MACf,CAAC,SAAS,WAAW;AAAA;AAAA,MAErB,CAAC,KAAK,UAAU,SAAS;AAAA,MACzB,CAAC,KAAK,SAAS;AAAA,IACnB;AAAA,IACA,SAAS;AAAA,MACL,CAAC,8BAA8B,EAAE,OAAO,4BAA4B,MAAM,kBAAkB,CAAC;AAAA,IACjG;AAAA,IACA,aAAa;AAAA,MACT;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,WAAW;AAAA,cACP;AAAA,gBACI,OAAO;AAAA,gBACP,MAAM;AAAA,cACV;AAAA,YACJ;AAAA,YACA,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,CAAC,KAAK,gBAAgB;AAAA,IAC1B;AAAA,IACA,YAAY;AAAA,MACR,CAAC,cAAc,EAAE;AAAA,MACjB,CAAC,QAAQ,WAAW,UAAU;AAAA,MAC9B,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,QAAQ,SAAS;AAAA,IACtB;AAAA,IACA,SAAS;AAAA,MACL,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,SAAS,SAAS;AAAA,IACvB;AAAA,IACA,QAAQ;AAAA,MACJ,CAAC,QAAQ,EAAE,OAAO,aAAa,MAAM,oBAAoB,CAAC;AAAA,MAC1D,CAAC,aAAa,QAAQ;AAAA,MACtB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,UAAU,SAAS;AAAA,IAC7B;AAAA,IACA,wBAAwB;AAAA,MACpB,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,UAAU,MAAM;AAAA,IAC1B;AAAA,IACA,kBAAkB;AAAA,MACd,CAAC,MAAM,EAAE,OAAO,aAAa,MAAM,OAAO,CAAC;AAAA,MAC3C,CAAC,KAAK,UAAU,yBAAyB;AAAA,MACzC,EAAE,SAAS,aAAa;AAAA,IAC5B;AAAA,EACJ;AACJ;", "names": []}