Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var _ui=require("../../ui");let getConfig=_ui.VxeUI.getConfig;var _default=exports.default={id:[String,Function],data:Array,height:[Number,String],minHeight:{type:[Number,String],default:()=>getConfig().table.minHeight},maxHeight:[Number,String],resizable:{type:Boolean,default:()=>getConfig().table.resizable},stripe:{type:Boolean,default:()=>getConfig().table.stripe},border:{type:[Boolean,String],default:()=>getConfig().table.border},padding:{type:Boolean,default:null},round:{type:Boolean,default:()=>getConfig().table.round},size:{type:String,default:()=>getConfig().table.size||getConfig().size},fit:{type:Boolean,default:()=>getConfig().table.fit},loading:Boolean,align:{type:String,default:()=>getConfig().table.align},headerAlign:{type:String,default:()=>getConfig().table.headerAlign},footerAlign:{type:String,default:()=>getConfig().table.footerAlign},showHeader:{type:Boolean,default:()=>getConfig().table.showHeader},highlightCurrentRow:{type:Boolean,default:()=>getConfig().table.highlightCurrentRow},highlightHoverRow:{type:Boolean,default:()=>getConfig().table.highlightHoverRow},highlightCurrentColumn:{type:Boolean,default:()=>getConfig().table.highlightCurrentColumn},highlightHoverColumn:{type:Boolean,default:()=>getConfig().table.highlightHoverColumn},highlightCell:Boolean,showFooter:Boolean,footerData:Array,footerMethod:Function,rowClassName:[String,Function],cellClassName:[String,Function],headerRowClassName:[String,Function],headerCellClassName:[String,Function],footerRowClassName:[String,Function],footerCellClassName:[String,Function],cellStyle:[Object,Function],headerCellStyle:[Object,Function],footerCellStyle:[Object,Function],rowStyle:[Object,Function],headerRowStyle:[Object,Function],footerRowStyle:[Object,Function],mergeCells:Array,mergeFooterItems:Array,spanMethod:Function,footerSpanMethod:Function,showOverflow:{type:[Boolean,String],default:()=>getConfig().table.showOverflow},showHeaderOverflow:{type:[Boolean,String],default:()=>getConfig().table.showHeaderOverflow},showFooterOverflow:{type:[Boolean,String],default:()=>getConfig().table.showFooterOverflow},columnKey:Boolean,rowKey:Boolean,rowId:{type:String,default:()=>getConfig().table.rowId},zIndex:Number,emptyText:{type:String,default:()=>getConfig().table.emptyText},keepSource:{type:Boolean,default:()=>getConfig().table.keepSource},autoResize:{type:Boolean,default:()=>getConfig().table.autoResize},syncResize:[Boolean,String,Number],resizeConfig:Object,columnConfig:Object,currentColumnConfig:Object,cellConfig:Object,headerCellConfig:Object,footerCellConfig:Object,rowConfig:Object,aggregateConfig:Object,rowGroupConfig:Object,currentRowConfig:Object,dragConfig:Object,rowDragConfig:Object,columnDragConfig:Object,resizableConfig:Object,seqConfig:Object,sortConfig:Object,filterConfig:Object,radioConfig:Object,checkboxConfig:Object,tooltipConfig:Object,exportConfig:Object,importConfig:Object,printConfig:Object,expandConfig:Object,treeConfig:Object,menuConfig:Object,mouseConfig:Object,areaConfig:Object,keyboardConfig:Object,clipConfig:Object,fnrConfig:Object,editConfig:Object,validConfig:Object,editRules:Object,loadingConfig:Object,emptyRender:Object,customConfig:Object,scrollX:Object,scrollY:Object,virtualXConfig:Object,virtualYConfig:Object,scrollbarConfig:Object,animat:{type:Boolean,default:()=>getConfig().table.animat},delayHover:{type:Number,default:()=>getConfig().table.delayHover},params:Object};