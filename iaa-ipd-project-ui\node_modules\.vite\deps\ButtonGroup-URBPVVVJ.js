import {
  ButtonGroupControl
} from "./chunk-2ZPAPTZ4.js";
import "./chunk-JHAOQP73.js";
import {
  __decorate,
  __extends
} from "./chunk-F25BIIHK.js";
import {
  Renderer
} from "./chunk-LZQZ2OHM.js";
import "./chunk-5QW7M2DY.js";
import "./chunk-ZJNJ4ZV3.js";
import "./chunk-CNJUQDSN.js";
import "./chunk-7XBFYOTW.js";
import "./chunk-ZUWHLQVA.js";
import "./chunk-GFT2G5UO.js";

// node_modules/amis/esm/renderers/ButtonGroup.js
var ButtonGroupRenderer = (
  /** @class */
  function(_super) {
    __extends(ButtonGroupRenderer2, _super);
    function ButtonGroupRenderer2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    ButtonGroupRenderer2 = __decorate([
      Renderer({
        type: "button-group"
      })
    ], ButtonGroupRenderer2);
    return ButtonGroupRenderer2;
  }(ButtonGroupControl)
);
export {
  ButtonGroupRenderer,
  ButtonGroupControl as default
};
//# sourceMappingURL=ButtonGroup-URBPVVVJ.js.map
