Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var _vue=require("vue"),_comp=require("../../ui/src/comp"),_xeUtils=_interopRequireDefault(require("xe-utils")),_ui=require("../../ui"),_vn=require("../../ui/src/vn"),_log=require("../../ui/src/log");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let{getConfig,getIcon,getI18n,renderer,commands,createEvent,useFns}=_ui.VxeUI;var _default=exports.default=(0,_comp.defineVxeComponent)({name:"VxeToolbar",props:{loading:Boolean,refresh:[Boolean,Object],refreshOptions:Object,import:[Boolean,Object],importOptions:Object,export:[Boolean,Object],exportOptions:Object,print:[Boolean,Object],printOptions:Object,zoom:[Boolean,Object],zoomOptions:Object,custom:[Boolean,Object],customOptions:Object,buttons:{type:Array,default:()=>getConfig().toolbar.buttons},tools:{type:Array,default:()=>getConfig().toolbar.tools},perfect:{type:Boolean,default:()=>getConfig().toolbar.perfect},size:{type:String,default:()=>getConfig().toolbar.size||getConfig().size},className:[String,Function]},emits:["button-click","tool-click"],setup(u,e){let{slots:p,emit:r}=e;var o=_xeUtils.default.uniqueId();let d=_ui.VxeUI.getComponent("VxeButton"),m=useFns.useSize(u).computeSize,v=(0,_vue.reactive)({isRefresh:!1,connectFlag:0,columns:[]}),g={connectTable:null},b=(0,_vue.ref)(),t={refElem:b},f={xID:o,props:u,context:e,reactData:v,internalData:g,getRefMaps:()=>t};let _=(0,_vue.inject)("$xeGrid",null),x=(0,_vue.computed)(()=>Object.assign({},_xeUtils.default.clone(getConfig().toolbar.refresh,!0),u.refreshOptions,u.refresh)),O=(0,_vue.computed)(()=>Object.assign({},_xeUtils.default.clone(getConfig().toolbar.import,!0),u.importOptions,u.import)),h=(0,_vue.computed)(()=>Object.assign({},_xeUtils.default.clone(getConfig().toolbar.export,!0),u.exportOptions,u.export)),T=(0,_vue.computed)(()=>Object.assign({},_xeUtils.default.clone(getConfig().toolbar.print,!0),u.printOptions,u.print)),C=(0,_vue.computed)(()=>Object.assign({},_xeUtils.default.clone(getConfig().toolbar.zoom,!0),u.zoomOptions,u.zoom)),L=(0,_vue.computed)(()=>Object.assign({},_xeUtils.default.clone(getConfig().toolbar.custom,!0),u.customOptions,u.custom)),n=(0,_vue.computed)(()=>{var e=g.connectTable;if((v.connectFlag||e)&&e)return e=e.getComputeMaps().computeCustomOpts,e.value;return{trigger:""}}),k=(0,_vue.computed)(()=>n.value.trigger),i=()=>{var e=g.connectTable;if(e)return!0;(0,_log.errLog)("vxe.error.barUnableLink")},y=({$event:e})=>{var o=g.connectTable;o&&o.triggerCustomEvent&&o.triggerCustomEvent(e)},$=({$event:e})=>{var o=g.connectTable;o&&o.customOpenEvent(e)},I=({$event:o})=>{var e=g.connectTable;let t=e;if(t){let e=t.reactData.customStore;e.activeBtn=!1,setTimeout(()=>{e.activeBtn||e.activeWrapper||t.customCloseEvent(o)},350)}},R=({$event:e})=>{var o=v.isRefresh,t=x.value;if(!o){o=t.queryMethod||t.query;if(o){v.isRefresh=!0;try{Promise.resolve(o({})).catch(e=>e).then(()=>{v.isRefresh=!1})}catch(e){v.isRefresh=!1}}else _&&(v.isRefresh=!0,_.triggerToolbarCommitEvent({code:t.code||"reload"},e).catch(e=>e).then(()=>{v.isRefresh=!1}))}},j=({$event:e})=>{_?_.triggerZoomEvent(e):(0,_log.warnLog)("vxe.error.notProp",["zoom"])},l=()=>{var e;i()&&(e=g.connectTable,e)&&e.importData()},E=()=>{var e;i()&&(e=g.connectTable,e)&&e.openImport()},a=()=>{var e;i()&&(e=g.connectTable,e)&&e.exportData()},P=()=>{var e;i()&&(e=g.connectTable,e)&&e.openExport()},c=()=>{var e;i()&&(e=g.connectTable,e)&&e.print()},S=()=>{var e;i()&&(e=g.connectTable,e)&&e.openPrint()},s=(e,o,t)=>{switch(o.code){case"print":c();break;case"open_print":S();break;case"custom":y(e);break;case"export":a();break;case"open_export":P();break;case"import":l();break;case"open_import":E();break;case"zoom":j(e);break;case"refresh":R(e);break;default:t()}},w=(e,t)=>{let r=e.$event;var o=g.connectTable;let n=o,i=t.code;i&&s(e,t,()=>{var e,o;_?_.triggerToolbarBtnEvent(t,r):(o=commands.get(i),e={code:i,button:t,$table:n,$grid:_,$event:r},o&&((o=o.tableCommandMethod||o.commandMethod)?o(e):(0,_log.errLog)("vxe.error.notCommands",[i])),f.dispatchEvent("button-click",e,r))})},N=(e,t)=>{let r=e.$event;var o=g.connectTable;let n=o,i=t.code;i&&s(e,t,()=>{var e,o;_?_.triggerToolbarTolEvent(t,r):(o=commands.get(i),e={code:i,button:null,tool:t,$table:n,$grid:_,$event:r},o&&((o=o.tableCommandMethod||o.commandMethod)?o(e):(0,_log.errLog)("vxe.error.notCommands",[i])),f.dispatchEvent("tool-click",e,r))})};Object.assign(f,{dispatchEvent:(e,o,t)=>{r(e,createEvent(t,{$toolbar:f},o))},syncUpdate(e){g.connectTable=e.$table,v.columns=e.collectColumn,v.connectFlag++}});let B=(e,t)=>{e=e.dropdowns;return e?e.map((o,e)=>!1!==o.visible&&d?(0,_vue.h)(d,{key:e,disabled:o.disabled,loading:o.loading,type:o.type,mode:o.mode,icon:o.icon,circle:o.circle,round:o.round,status:o.status,content:o.name,title:o.title,routerLink:o.routerLink,permissionCode:o.permissionCode,prefixTooltip:o.prefixTooltip,suffixTooltip:o.suffixTooltip,onClick:e=>(t?w:N)(e,o)}):(0,_vue.createCommentVNode)()):[]};return f.renderVN=()=>{var{perfect:e,loading:o,refresh:t,zoom:r,custom:n,className:i}=u,l=g.connectTable,a=m.value,s=p.tools,c=p.buttons;return(0,_vue.h)("div",{ref:b,class:["vxe-toolbar",i?_xeUtils.default.isFunction(i)?i({$toolbar:f}):i:"",{["size--"+a]:a,"is--perfect":e,"is--loading":o}]},[(0,_vue.h)("div",{class:"vxe-buttons--wrapper"},c?c({$grid:_,$table:l}):(()=>{var e=u.buttons,o=g.connectTable;let a=o;var o=p.buttonPrefix||p["button-prefix"],t=p.buttonSuffix||p["button-suffix"];let s=[];return o&&s.push(...(0,_vn.getSlotVNs)(o({buttons:e||[],$grid:_,$table:a}))),e&&e.forEach((o,e)=>{var t,r,n,{dropdowns:i,buttonRender:l}=o;!1!==o.visible&&(t=l?renderer.get(l.name):null,l&&t&&t.renderToolbarButton?(r=t.toolbarButtonClassName,n={$grid:_,$table:a,button:o},s.push((0,_vue.h)("span",{key:"br"+(o.code||e),class:["vxe-button--item",r?_xeUtils.default.isFunction(r)?r(n):r:""]},(0,_vn.getSlotVNs)(t.renderToolbarButton(l,n))))):d&&s.push((0,_vue.h)(d,{key:"bd"+(o.code||e),disabled:o.disabled,loading:o.loading,type:o.type,mode:o.mode,icon:o.icon,circle:o.circle,round:o.round,status:o.status,content:o.name,title:o.title,routerLink:o.routerLink,permissionCode:o.permissionCode,prefixTooltip:o.prefixTooltip,suffixTooltip:o.suffixTooltip,destroyOnClose:o.destroyOnClose,placement:o.placement,transfer:o.transfer,onClick:e=>w(e,o)},i&&i.length?{dropdowns:()=>B(o,!0)}:{})))}),t&&s.push(...(0,_vn.getSlotVNs)(t({buttons:e||[],$grid:_,$table:a}))),s})()),(0,_vue.h)("div",{class:"vxe-tools--wrapper"},s?s({$grid:_,$table:l}):(()=>{var e=u.tools,o=g.connectTable;let s=o;var o=p.toolPrefix||p["tool-prefix"],t=p.toolSuffix||p["tool-suffix"];let c=[];return o&&c.push(...(0,_vn.getSlotVNs)(o({tools:e||[],$grid:_,$table:s}))),e&&e.forEach((o,e)=>{var t,r,n,i,{dropdowns:l,toolRender:a}=o;!1!==o.visible&&(t=a?a.name:null,r=a?renderer.get(t):null,a&&r&&r.renderToolbarTool?(n=r.toolbarToolClassName,i={$grid:_,$table:s,tool:o},c.push((0,_vue.h)("span",{key:t,class:["vxe-tool--item",n?_xeUtils.default.isFunction(n)?n(i):n:""]},(0,_vn.getSlotVNs)(r.renderToolbarTool(a,i))))):d&&c.push((0,_vue.h)(d,{key:e,disabled:o.disabled,loading:o.loading,type:o.type,mode:o.mode,icon:o.icon,circle:o.circle,round:o.round,status:o.status,content:o.name,title:o.title,routerLink:o.routerLink,permissionCode:o.permissionCode,prefixTooltip:o.prefixTooltip,suffixTooltip:o.suffixTooltip,destroyOnClose:o.destroyOnClose,placement:o.placement,transfer:o.transfer,onClick:e=>N(e,o)},l&&l.length?{dropdowns:()=>B(o,!1)}:{})))}),t&&c.push(...(0,_vn.getSlotVNs)(t({tools:e||[],$grid:_,$table:s}))),c})()),(0,_vue.h)("div",{class:"vxe-tools--operate"},[u.import&&(i=O.value,d)?(0,_vue.h)(d,{key:"import",circle:!0,icon:i.icon||getIcon().TOOLBAR_TOOLS_IMPORT,title:getI18n("vxe.toolbar.import"),onClick:E}):(0,_vue.createCommentVNode)(),u.export&&(a=h.value,d)?(0,_vue.h)(d,{key:"export",circle:!0,icon:a.icon||getIcon().TOOLBAR_TOOLS_EXPORT,title:getI18n("vxe.toolbar.export"),onClick:P}):(0,_vue.createCommentVNode)(),u.print&&(e=T.value,d)?(0,_vue.h)(d,{key:"print",circle:!0,icon:e.icon||getIcon().TOOLBAR_TOOLS_PRINT,title:getI18n("vxe.toolbar.print"),onClick:S}):(0,_vue.createCommentVNode)(),t&&(o=x.value,d)?(0,_vue.h)(d,{key:"refresh",circle:!0,icon:v.isRefresh?o.iconLoading||getIcon().TOOLBAR_TOOLS_REFRESH_LOADING:o.icon||getIcon().TOOLBAR_TOOLS_REFRESH,title:getI18n("vxe.toolbar.refresh"),onClick:R}):(0,_vue.createCommentVNode)(),r&&_&&(c=C.value,_)&&d?(0,_vue.h)(d,{key:"zoom",circle:!0,icon:_.isMaximized()?c.iconOut||getIcon().TOOLBAR_TOOLS_MINIMIZE:c.iconIn||getIcon().TOOLBAR_TOOLS_FULLSCREEN,title:getI18n("vxe.toolbar.zoom"+(_.isMaximized()?"Out":"In")),onClick:j}):(0,_vue.createCommentVNode)(),n&&(s=L.value,l=k.value,i={},"manual"!==l&&("hover"===l?(i.onMouseenter=$,i.onMouseleave=I):i.onClick=y),d)?(0,_vue.h)(d,Object.assign({key:"custom",circle:!0,icon:s.icon||getIcon().TOOLBAR_TOOLS_CUSTOM,title:getI18n("vxe.toolbar.custom"),className:"vxe-toolbar-custom-target"},i)):(0,_vue.createCommentVNode)()])])},(0,_vue.nextTick)(()=>{var e=x.value,e=e.queryMethod||e.query,e=(!u.refresh||_||e||(0,_log.warnLog)("vxe.error.notFunc",["queryMethod"]),_xeUtils.default.isPlainObject(u.custom)&&(0,_log.warnLog)("vxe.error.delProp",["custom={...}","custom=boolean & custom-options={...}"]),_xeUtils.default.isPlainObject(u.print)&&(0,_log.warnLog)("vxe.error.delProp",["print={...}","print=boolean & print-options={...}"]),_xeUtils.default.isPlainObject(u.export)&&(0,_log.warnLog)("vxe.error.delProp",["export={...}","export=boolean & export-options={...}"]),_xeUtils.default.isPlainObject(u.import)&&(0,_log.warnLog)("vxe.error.delProp",["import={...}","import=boolean & import-options={...}"]),_xeUtils.default.isPlainObject(u.refresh)&&(0,_log.warnLog)("vxe.error.delProp",["refresh={...}","refresh=boolean & refresh-options={...}"]),_xeUtils.default.isPlainObject(u.refresh)&&(0,_log.warnLog)("vxe.error.delProp",["zoom={...}","zoom=boolean & zoom-options={...}"]),L.value);e.isFooter&&(0,_log.warnLog)("vxe.error.delProp",["toolbar.custom.isFooter","table.custom-config.showFooter"]),e.showFooter&&(0,_log.warnLog)("vxe.error.delProp",["toolbar.custom.showFooter","table.custom-config.showFooter"]),e.immediate&&(0,_log.warnLog)("vxe.error.delProp",["toolbar.custom.immediate","table.custom-config.immediate"]),e.trigger&&(0,_log.warnLog)("vxe.error.delProp",["toolbar.custom.trigger","table.custom-config.trigger"]),(u.refresh||u.import||u.export||u.print||u.zoom)&&!d&&(0,_log.errLog)("vxe.error.reqComp",["vxe-button"])}),f},render(){return this.renderVN()}});