{"version": 3, "sources": ["../../amis/esm/renderers/Breadcrumb.js"], "sourcesContent": ["/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __rest, __assign, __decorate } from 'tslib';\nimport React from 'react';\nimport { resolveVariableAndFilter, filter, Renderer } from 'amis-core';\nimport { Breadcrumb } from 'amis-ui';\n\nvar BreadcrumbField = /** @class */ (function (_super) {\n    __extends(BreadcrumbField, _super);\n    function BreadcrumbField() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    BreadcrumbField.prototype.render = function () {\n        var _a = this.props, items = _a.items, source = _a.source, data = _a.data, env = _a.env, restProps = __rest(_a, [\"items\", \"source\", \"data\", \"env\"]);\n        var crumbItems = items\n            ? items\n            : resolveVariableAndFilter(source, data, '| raw');\n        if (crumbItems) {\n            crumbItems = crumbItems.map(function (item) {\n                if (item.label) {\n                    item.label = filter(item.label, data);\n                }\n                if (item.href) {\n                    item.href = resolveVariableAndFilter(item.href, data, '| raw');\n                }\n                if (item.dropdown) {\n                    item.dropdown = item.dropdown.map(function (dropdownItem) {\n                        if (dropdownItem.label) {\n                            dropdownItem.label = filter(dropdownItem.label, data);\n                        }\n                        if (dropdownItem.href) {\n                            dropdownItem.href = resolveVariableAndFilter(dropdownItem.href, data, '| raw');\n                        }\n                        return dropdownItem;\n                    });\n                }\n                return item;\n            });\n        }\n        return (React.createElement(Breadcrumb, __assign({ items: crumbItems, tooltipContainer: env === null || env === void 0 ? void 0 : env.getModalContainer }, restProps)));\n    };\n    return BreadcrumbField;\n}(React.Component));\nvar BreadcrumbFieldRenderer = /** @class */ (function (_super) {\n    __extends(BreadcrumbFieldRenderer, _super);\n    function BreadcrumbFieldRenderer() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    BreadcrumbFieldRenderer = __decorate([\n        Renderer({\n            type: 'breadcrumb'\n        })\n    ], BreadcrumbFieldRenderer);\n    return BreadcrumbFieldRenderer;\n}(BreadcrumbField));\n\nexport { BreadcrumbField, BreadcrumbFieldRenderer };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,mBAAkB;AAIlB,IAAI;AAAA;AAAA,EAAiC,SAAU,QAAQ;AACnD,cAAUA,kBAAiB,MAAM;AACjC,aAASA,mBAAkB;AACvB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,iBAAgB,UAAU,SAAS,WAAY;AAC3C,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,SAAS,GAAG,QAAQ,OAAO,GAAG,MAAM,MAAM,GAAG,KAAK,YAAY,OAAO,IAAI,CAAC,SAAS,UAAU,QAAQ,KAAK,CAAC;AAClJ,UAAI,aAAa,QACX,QACA,yBAAyB,QAAQ,MAAM,OAAO;AACpD,UAAI,YAAY;AACZ,qBAAa,WAAW,IAAI,SAAU,MAAM;AACxC,cAAI,KAAK,OAAO;AACZ,iBAAK,QAAQ,OAAO,KAAK,OAAO,IAAI;AAAA,UACxC;AACA,cAAI,KAAK,MAAM;AACX,iBAAK,OAAO,yBAAyB,KAAK,MAAM,MAAM,OAAO;AAAA,UACjE;AACA,cAAI,KAAK,UAAU;AACf,iBAAK,WAAW,KAAK,SAAS,IAAI,SAAU,cAAc;AACtD,kBAAI,aAAa,OAAO;AACpB,6BAAa,QAAQ,OAAO,aAAa,OAAO,IAAI;AAAA,cACxD;AACA,kBAAI,aAAa,MAAM;AACnB,6BAAa,OAAO,yBAAyB,aAAa,MAAM,MAAM,OAAO;AAAA,cACjF;AACA,qBAAO;AAAA,YACX,CAAC;AAAA,UACL;AACA,iBAAO;AAAA,QACX,CAAC;AAAA,MACL;AACA,aAAQ,aAAAC,QAAM,cAAc,cAAY,SAAS,EAAE,OAAO,YAAY,kBAAkB,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,kBAAkB,GAAG,SAAS,CAAC;AAAA,IACzK;AACA,WAAOD;AAAA,EACX,EAAE,aAAAC,QAAM,SAAS;AAAA;AACjB,IAAI;AAAA;AAAA,EAAyC,SAAU,QAAQ;AAC3D,cAAUC,0BAAyB,MAAM;AACzC,aAASA,2BAA0B;AAC/B,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,2BAA0B,WAAW;AAAA,MACjC,SAAS;AAAA,QACL,MAAM;AAAA,MACV,CAAC;AAAA,IACL,GAAGA,wBAAuB;AAC1B,WAAOA;AAAA,EACX,EAAE,eAAe;AAAA;", "names": ["BreadcrumbField", "React", "<PERSON><PERSON>crumb<PERSON>ield<PERSON><PERSON><PERSON>"]}