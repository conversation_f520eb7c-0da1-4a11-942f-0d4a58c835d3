{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/liquid/liquid.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { languages } from '../fillers/monaco-editor-core.js';\nvar EMPTY_ELEMENTS = [\n    'area',\n    'base',\n    'br',\n    'col',\n    'embed',\n    'hr',\n    'img',\n    'input',\n    'keygen',\n    'link',\n    'menuitem',\n    'meta',\n    'param',\n    'source',\n    'track',\n    'wbr'\n];\nexport var conf = {\n    wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\$\\^\\&\\*\\(\\)\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\s]+)/g,\n    brackets: [\n        ['<!--', '-->'],\n        ['<', '>'],\n        ['{{', '}}'],\n        ['{%', '%}'],\n        ['{', '}'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '%', close: '%' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" }\n    ],\n    surroundingPairs: [\n        { open: '<', close: '>' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" }\n    ],\n    onEnterRules: [\n        {\n            beforeText: new RegExp(\"<(?!(?:\" + EMPTY_ELEMENTS.join('|') + \"))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$\", 'i'),\n            afterText: /^<\\/(\\w[\\w\\d]*)\\s*>$/i,\n            action: {\n                indentAction: languages.IndentAction.IndentOutdent\n            }\n        },\n        {\n            beforeText: new RegExp(\"<(?!(?:\" + EMPTY_ELEMENTS.join('|') + \"))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$\", 'i'),\n            action: { indentAction: languages.IndentAction.Indent }\n        }\n    ]\n};\nexport var language = {\n    defaultToken: '',\n    tokenPostfix: '',\n    builtinTags: [\n        'if',\n        'else',\n        'elseif',\n        'endif',\n        'render',\n        'assign',\n        'capture',\n        'endcapture',\n        'case',\n        'endcase',\n        'comment',\n        'endcomment',\n        'cycle',\n        'decrement',\n        'for',\n        'endfor',\n        'include',\n        'increment',\n        'layout',\n        'raw',\n        'endraw',\n        'render',\n        'tablerow',\n        'endtablerow',\n        'unless',\n        'endunless'\n    ],\n    builtinFilters: [\n        'abs',\n        'append',\n        'at_least',\n        'at_most',\n        'capitalize',\n        'ceil',\n        'compact',\n        'date',\n        'default',\n        'divided_by',\n        'downcase',\n        'escape',\n        'escape_once',\n        'first',\n        'floor',\n        'join',\n        'json',\n        'last',\n        'lstrip',\n        'map',\n        'minus',\n        'modulo',\n        'newline_to_br',\n        'plus',\n        'prepend',\n        'remove',\n        'remove_first',\n        'replace',\n        'replace_first',\n        'reverse',\n        'round',\n        'rstrip',\n        'size',\n        'slice',\n        'sort',\n        'sort_natural',\n        'split',\n        'strip',\n        'strip_html',\n        'strip_newlines',\n        'times',\n        'truncate',\n        'truncatewords',\n        'uniq',\n        'upcase',\n        'url_decode',\n        'url_encode',\n        'where'\n    ],\n    constants: ['true', 'false'],\n    operators: ['==', '!=', '>', '<', '>=', '<='],\n    symbol: /[=><!]+/,\n    identifier: /[a-zA-Z_][\\w]*/,\n    tokenizer: {\n        root: [\n            [/\\{\\%\\s*comment\\s*\\%\\}/, 'comment.start.liquid', '@comment'],\n            [/\\{\\{/, { token: '@rematch', switchTo: '@liquidState.root' }],\n            [/\\{\\%/, { token: '@rematch', switchTo: '@liquidState.root' }],\n            [/(<)([\\w\\-]+)(\\/>)/, ['delimiter.html', 'tag.html', 'delimiter.html']],\n            [/(<)([:\\w]+)/, ['delimiter.html', { token: 'tag.html', next: '@otherTag' }]],\n            [/(<\\/)([\\w\\-]+)/, ['delimiter.html', { token: 'tag.html', next: '@otherTag' }]],\n            [/</, 'delimiter.html'],\n            [/\\{/, 'delimiter.html'],\n            [/[^<{]+/] // text\n        ],\n        comment: [\n            [/\\{\\%\\s*endcomment\\s*\\%\\}/, 'comment.end.liquid', '@pop'],\n            [/./, 'comment.content.liquid']\n        ],\n        otherTag: [\n            [\n                /\\{\\{/,\n                {\n                    token: '@rematch',\n                    switchTo: '@liquidState.otherTag'\n                }\n            ],\n            [\n                /\\{\\%/,\n                {\n                    token: '@rematch',\n                    switchTo: '@liquidState.otherTag'\n                }\n            ],\n            [/\\/?>/, 'delimiter.html', '@pop'],\n            [/\"([^\"]*)\"/, 'attribute.value'],\n            [/'([^']*)'/, 'attribute.value'],\n            [/[\\w\\-]+/, 'attribute.name'],\n            [/=/, 'delimiter'],\n            [/[ \\t\\r\\n]+/] // whitespace\n        ],\n        liquidState: [\n            [/\\{\\{/, 'delimiter.output.liquid'],\n            [/\\}\\}/, { token: 'delimiter.output.liquid', switchTo: '@$S2.$S3' }],\n            [/\\{\\%/, 'delimiter.tag.liquid'],\n            [/raw\\s*\\%\\}/, 'delimiter.tag.liquid', '@liquidRaw'],\n            [/\\%\\}/, { token: 'delimiter.tag.liquid', switchTo: '@$S2.$S3' }],\n            { include: 'liquidRoot' }\n        ],\n        liquidRaw: [\n            [/^(?!\\{\\%\\s*endraw\\s*\\%\\}).+/],\n            [/\\{\\%/, 'delimiter.tag.liquid'],\n            [/@identifier/],\n            [/\\%\\}/, { token: 'delimiter.tag.liquid', next: '@root' }]\n        ],\n        liquidRoot: [\n            [/\\d+(\\.\\d+)?/, 'number.liquid'],\n            [/\"[^\"]*\"/, 'string.liquid'],\n            [/'[^']*'/, 'string.liquid'],\n            [/\\s+/],\n            [\n                /@symbol/,\n                {\n                    cases: {\n                        '@operators': 'operator.liquid',\n                        '@default': ''\n                    }\n                }\n            ],\n            [/\\./],\n            [\n                /@identifier/,\n                {\n                    cases: {\n                        '@constants': 'keyword.liquid',\n                        '@builtinFilters': 'predefined.liquid',\n                        '@builtinTags': 'predefined.liquid',\n                        '@default': 'variable.liquid'\n                    }\n                }\n            ],\n            [/[^}|%]/, 'variable.liquid']\n        ]\n    }\n};\n"], "mappings": ";;;;;;;AAKA,IAAI,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACO,IAAI,OAAO;AAAA,EACd,aAAa;AAAA,EACb,UAAU;AAAA,IACN,CAAC,QAAQ,KAAK;AAAA,IACd,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,MAAM,IAAI;AAAA,IACX,CAAC,MAAM,IAAI;AAAA,IACX,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,cAAc;AAAA,IACV;AAAA,MACI,YAAY,IAAI,OAAO,YAAY,eAAe,KAAK,GAAG,IAAI,wCAAwC,GAAG;AAAA,MACzG,WAAW;AAAA,MACX,QAAQ;AAAA,QACJ,cAAc,UAAU,aAAa;AAAA,MACzC;AAAA,IACJ;AAAA,IACA;AAAA,MACI,YAAY,IAAI,OAAO,YAAY,eAAe,KAAK,GAAG,IAAI,wCAAwC,GAAG;AAAA,MACzG,QAAQ,EAAE,cAAc,UAAU,aAAa,OAAO;AAAA,IAC1D;AAAA,EACJ;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,aAAa;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,gBAAgB;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,WAAW,CAAC,QAAQ,OAAO;AAAA,EAC3B,WAAW,CAAC,MAAM,MAAM,KAAK,KAAK,MAAM,IAAI;AAAA,EAC5C,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,WAAW;AAAA,IACP,MAAM;AAAA,MACF,CAAC,yBAAyB,wBAAwB,UAAU;AAAA,MAC5D,CAAC,QAAQ,EAAE,OAAO,YAAY,UAAU,oBAAoB,CAAC;AAAA,MAC7D,CAAC,QAAQ,EAAE,OAAO,YAAY,UAAU,oBAAoB,CAAC;AAAA,MAC7D,CAAC,qBAAqB,CAAC,kBAAkB,YAAY,gBAAgB,CAAC;AAAA,MACtE,CAAC,eAAe,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,YAAY,CAAC,CAAC;AAAA,MAC5E,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,YAAY,CAAC,CAAC;AAAA,MAC/E,CAAC,KAAK,gBAAgB;AAAA,MACtB,CAAC,MAAM,gBAAgB;AAAA,MACvB,CAAC,QAAQ;AAAA;AAAA,IACb;AAAA,IACA,SAAS;AAAA,MACL,CAAC,4BAA4B,sBAAsB,MAAM;AAAA,MACzD,CAAC,KAAK,wBAAwB;AAAA,IAClC;AAAA,IACA,UAAU;AAAA,MACN;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA,CAAC,QAAQ,kBAAkB,MAAM;AAAA,MACjC,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY;AAAA;AAAA,IACjB;AAAA,IACA,aAAa;AAAA,MACT,CAAC,QAAQ,yBAAyB;AAAA,MAClC,CAAC,QAAQ,EAAE,OAAO,2BAA2B,UAAU,WAAW,CAAC;AAAA,MACnE,CAAC,QAAQ,sBAAsB;AAAA,MAC/B,CAAC,cAAc,wBAAwB,YAAY;AAAA,MACnD,CAAC,QAAQ,EAAE,OAAO,wBAAwB,UAAU,WAAW,CAAC;AAAA,MAChE,EAAE,SAAS,aAAa;AAAA,IAC5B;AAAA,IACA,WAAW;AAAA,MACP,CAAC,6BAA6B;AAAA,MAC9B,CAAC,QAAQ,sBAAsB;AAAA,MAC/B,CAAC,aAAa;AAAA,MACd,CAAC,QAAQ,EAAE,OAAO,wBAAwB,MAAM,QAAQ,CAAC;AAAA,IAC7D;AAAA,IACA,YAAY;AAAA,MACR,CAAC,eAAe,eAAe;AAAA,MAC/B,CAAC,WAAW,eAAe;AAAA,MAC3B,CAAC,WAAW,eAAe;AAAA,MAC3B,CAAC,KAAK;AAAA,MACN;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,cAAc;AAAA,YACd,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,CAAC,IAAI;AAAA,MACL;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,cAAc;AAAA,YACd,mBAAmB;AAAA,YACnB,gBAAgB;AAAA,YAChB,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,CAAC,UAAU,iBAAiB;AAAA,IAChC;AAAA,EACJ;AACJ;", "names": []}