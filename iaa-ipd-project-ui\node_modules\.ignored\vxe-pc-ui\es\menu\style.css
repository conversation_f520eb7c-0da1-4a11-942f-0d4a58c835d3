.vxe-menu {
  position: relative;
}
.vxe-menu.is--collapsed .vxe-menu--item-link-title,
.vxe-menu.is--collapsed .vxe-menu--item-custom-title,
.vxe-menu.is--collapsed .vxe-menu--item-link-collapse {
  display: none;
}
.vxe-menu.is--loading {
  min-height: 4em;
}

.vxe-menu,
.vxe-menu--collapse-wrapper {
  font-size: var(--vxe-ui-font-size-default);
  color: var(--vxe-ui-font-color);
  font-family: var(--vxe-ui-font-family);
  background-color: var(--vxe-ui-layout-background-color);
}

.vxe-menu--collapse-wrapper {
  display: none;
  position: absolute;
  overflow: auto;
}
.vxe-menu--collapse-wrapper.is--collapsed {
  display: block;
  transition: all 0.25s ease-in-out;
}
.vxe-menu--collapse-wrapper:not(.is--enter) .vxe-menu--item-link-title,
.vxe-menu--collapse-wrapper:not(.is--enter) .vxe-menu--item-custom-title,
.vxe-menu--collapse-wrapper:not(.is--enter) .vxe-menu--item-link-collapse {
  display: none;
}
.vxe-menu--collapse-wrapper.is--enter {
  box-shadow: var(--vxe-ui-menu-collapse-wrapper-box-shadow);
}

.vxe-menu--item-link {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: var(--vxe-ui-menu-item-height);
  color: var(--vxe-ui-font-color);
  text-decoration: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-menu--item-link:hover {
  color: var(--vxe-ui-menu-item-hover-color);
  background-color: var(--vxe-ui-base-hover-background-color);
}

.vxe-menu--item-link,
.vxe-menu--item-link-title,
.vxe-menu--item-custom-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.vxe-menu--item-link-icon {
  flex-shrink: 0;
  width: var(--vxe-ui-menu-icon-width);
  text-align: center;
}

.vxe-menu--item-link-title,
.vxe-menu--item-custom-title {
  flex-grow: 1;
  padding-left: 0.2em;
}

.vxe-menu--item-link-collapse {
  flex-shrink: 0;
  padding: 0.5em;
}
.vxe-menu--item-link-collapse i {
  display: inline-block;
  transition: transform 0.25s ease-in-out;
}

.vxe-menu--item-wrapper.vxe-menu--item-level1 > .vxe-menu--item-link {
  padding: 0 var(--vxe-ui-layout-padding-double);
}
.vxe-menu--item-wrapper.vxe-menu--item-level1 > .vxe-menu--item-link {
  padding: 0 var(--vxe-ui-layout-padding-double) 0 calc(var(--vxe-ui-layout-padding-double) * 1);
}
.vxe-menu--item-wrapper.vxe-menu--item-level2 > .vxe-menu--item-link {
  padding: 0 var(--vxe-ui-layout-padding-double) 0 calc(var(--vxe-ui-layout-padding-double) * 2);
}
.vxe-menu--item-wrapper.vxe-menu--item-level3 > .vxe-menu--item-link {
  padding: 0 var(--vxe-ui-layout-padding-double) 0 calc(var(--vxe-ui-layout-padding-double) * 3);
}
.vxe-menu--item-wrapper.vxe-menu--item-level4 > .vxe-menu--item-link {
  padding: 0 var(--vxe-ui-layout-padding-double) 0 calc(var(--vxe-ui-layout-padding-double) * 4);
}
.vxe-menu--item-wrapper.vxe-menu--item-level5 > .vxe-menu--item-link {
  padding: 0 var(--vxe-ui-layout-padding-double) 0 calc(var(--vxe-ui-layout-padding-double) * 5);
}
.vxe-menu--item-wrapper.vxe-menu--item-level6 > .vxe-menu--item-link {
  padding: 0 var(--vxe-ui-layout-padding-double) 0 calc(var(--vxe-ui-layout-padding-double) * 6);
}
.vxe-menu--item-wrapper.vxe-menu--item-level7 > .vxe-menu--item-link {
  padding: 0 var(--vxe-ui-layout-padding-double) 0 calc(var(--vxe-ui-layout-padding-double) * 7);
}

.vxe-menu--item-level3 {
  font-size: 13px;
}

.vxe-menu--item-group {
  display: none;
}

.vxe-menu--item-wrapper.is--active > .vxe-menu--item-link {
  font-weight: 700;
}
.vxe-menu--item-wrapper.is--exact-active > .vxe-menu--item-link {
  color: var(--vxe-ui-font-primary-color);
  background-color: var(--vxe-ui-base-active-background-color);
}
.vxe-menu--item-wrapper.is--expand > .vxe-menu--item-group {
  display: block;
}