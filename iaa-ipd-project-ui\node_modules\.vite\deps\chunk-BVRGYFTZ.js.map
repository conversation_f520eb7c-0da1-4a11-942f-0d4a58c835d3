{"version": 3, "sources": ["../../amis/esm/renderers/Form/IconPickerIcons.js"], "sourcesContent": ["/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nvar ICONS = [\n    {\n        name: 'Font Awesome 4.7',\n        prefix: 'fa fa-',\n        icons: [\n            'slideshare',\n            'snapchat',\n            'snapchat-ghost',\n            'snapchat-square',\n            'soundcloud',\n            'spotify',\n            'stack-exchange',\n            'stack-overflow'\n        ]\n    }\n];\nfunction setIconVendor(icons) {\n    ICONS = icons;\n}\n\nexport { ICONS, setIconVendor };\n"], "mappings": ";AAMA,IAAI,QAAQ;AAAA,EACR;AAAA,IACI,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,OAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACJ;", "names": []}