var _vue=require("vue"),_ui=require("../../../ui"),_xeUtils=_interopRequireDefault(require("xe-utils"));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let tableCustomMethodKeys=["openCustom","closeCustom","saveCustom","cancelCustom","resetCustom","toggleCustomAllCheckbox","setCustomAllCheckbox"];_ui.VxeUI.hooks.add("tableCustomModule",{setupTable(n){let{reactData:m,internalData:r}=n,{computeCustomOpts:c,computeRowGroupFields:o}=n.getComputeMaps(),s=n.getRefMaps().refElem,l=n.xeGrid,i=()=>{var e=m.customStore,t=s.value;let l=0;t&&(l=t.clientHeight-28),e.maxHeight=Math.max(88,l)},a=()=>{var{initStore:e,customStore:t}=m;return t.visible=!0,e.custom=!0,u(),v(),i(),(0,_vue.nextTick)().then(()=>i())},u=()=>{var e=m.customStore,t=r.collectColumn;if(e.visible){let l={},s={},i={};_xeUtils.default.eachTree(t,e=>{var t=e.getKey();e.renderFixed=e.fixed,e.renderVisible=e.visible,e.renderResizeWidth=e.renderWidth,l[t]=e.renderSortNumber,s[t]=e.fixed,i[t]=e.visible}),e.oldSortMaps=l,e.oldFixedMaps=s,e.oldVisibleMaps=i,m.customColumnList=t.slice(0)}},d=()=>{var e=m.customStore,t=c.value;return e.visible&&(e.visible=!1,t.immediate||n.handleCustom()),(0,_vue.nextTick)()};let t=e=>{var t=m.customStore,l=m.customColumnList,s=c.value;let{checkMethod:i,visibleMethod:r}=s,o=!!e;return s.immediate?(_xeUtils.default.eachTree(l,e=>{r&&!r({$table:n,column:e})||i&&!i({$table:n,column:e})||(e.visible=o,e.renderVisible=o,e.halfVisible=!1)}),t.isAll=o,m.isCustomStatus=!0,n.handleCustom(),n.saveCustomStore("update:visible")):(_xeUtils.default.eachTree(l,e=>{r&&!r({$table:n,column:e})||i&&!i({$table:n,column:e})||(e.renderVisible=o,e.halfVisible=!1)}),t.isAll=o),n.checkCustomStatus(),(0,_vue.nextTick)()};var e={openCustom:a,closeCustom:d,saveCustom:()=>{let{customColumnList:e,aggHandleFields:l,rowGroupList:t}=m;let{allowVisible:r,allowSort:o,allowFixed:a,allowResizable:u}=c.value;return _xeUtils.default.eachTree(e,(e,t,l,s,i)=>{i?e.fixed=i.fixed:(o&&(e.renderSortNumber=t+1),a&&(e.fixed=e.renderFixed)),u&&e.renderVisible&&(!e.children||e.children.length)&&e.renderResizeWidth!==e.renderWidth&&(e.resizeWidth=e.renderResizeWidth,e.renderWidth=e.renderResizeWidth),r&&(e.visible=e.renderVisible)}),m.isCustomStatus=!0,m.isDragColMove=!0,setTimeout(()=>{m.isDragColMove=!1},1e3),n.saveCustomStore("confirm").then(()=>{!n.handlePivotTableAggregateData||t.length===l.length&&!t.some((e,t)=>e.field!==l[t])||(l.length?n.setRowGroups(l):n.clearRowGroups())})},cancelCustom:()=>{var{customColumnList:e,customStore:t}=m;let{oldSortMaps:i,oldFixedMaps:r,oldVisibleMaps:o}=t,{allowVisible:a,allowSort:u,allowFixed:n,allowResizable:d}=c.value;return _xeUtils.default.eachTree(e,e=>{var t=e.getKey(),l=!!o[t],s=r[t]||"";a&&(e.renderVisible=l,e.visible=l),n&&(e.renderFixed=s,e.fixed=s),u&&(e.renderSortNumber=i[t]||0),d&&(e.renderResizeWidth=e.renderWidth)},{children:"children"}),(0,_vue.nextTick)()},resetCustom(e){let t=m.rowGroupList;var l=r.collectColumn;let s=c.value.checkMethod,i=Object.assign({visible:!0,resizable:!0===e,fixed:!0===e,sort:!0===e},e);return _xeUtils.default.eachTree(l,e=>{i.resizable&&(e.resizeWidth=0),i.fixed&&(e.fixed=e.defaultFixed),i.sort&&(e.renderSortNumber=e.sortNumber),s&&!s({$table:n,column:e})||(e.visible=e.defaultVisible),e.renderResizeWidth=e.renderWidth}),m.isCustomStatus=!1,n.saveCustomStore("reset"),n.handleCustom().then(()=>{var e;n.handlePivotTableAggregateData&&((e=o.value)||t).length&&(e&&e.length?n.setRowGroups(e):n.clearRowGroups())})},toggleCustomAllCheckbox(){var e=m.customStore,e=!e.isAll;return t(e)},setCustomAllCheckbox:t};let v=()=>{var e=m.customStore,t=r.collectColumn;let l=c.value.checkMethod;e.isAll=t.every(e=>!!l&&!l({$table:n,column:e})||e.renderVisible),e.isIndeterminate=!e.isAll&&t.some(e=>(!l||l({$table:n,column:e}))&&(e.renderVisible||e.halfVisible))},b=(e,t)=>{(l||n).dispatchEvent("custom",{type:e},t)};var h={checkCustomStatus:v,emitCustomEvent:b,triggerCustomEvent(e){var t=n.reactData.customStore;t.visible?(d(),b("close",e)):(t.btnEl=e.target,a(),b("open",e))},customOpenEvent(e){var t=n.reactData.customStore;t.visible||(t.activeBtn=!0,t.btnEl=e.target,n.openCustom(),n.emitCustomEvent("open",e))},customCloseEvent(e){var t=n.reactData.customStore;t.visible&&(t.activeBtn=!1,n.closeCustom(),n.emitCustomEvent("close",e))},handleUpdateCustomColumn:u};return Object.assign(Object.assign({},e),h)},setupGrid(e){return e.extendTableMethods(tableCustomMethodKeys)}});