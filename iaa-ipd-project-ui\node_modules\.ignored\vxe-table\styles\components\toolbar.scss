/*toolbar*/
.vxe-toolbar {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0.6em 0;
  color: var(--vxe-ui-font-color);
  font-family: var(--vxe-ui-font-family);
  background-color: var(--vxe-ui-layout-background-color);
  &:after {
    content: "";
    display: block;
    clear: both;
    height: 0;
    overflow: hidden;
    visibility: hidden;
  }
  &.is--perfect {
    border: 1px solid var(--vxe-ui-table-border-color);
    border-bottom-width: 0;
    background-color: var(--vxe-ui-table-header-background-color);
  }
  &.is--loading {
    &:before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999;
      user-select: none;
      background-color: var(--vxe-ui-loading-background-color);
    }
  }
  .vxe-buttons--wrapper {
    flex-grow: 1;
    & > .vxe-button+.vxe-button--item,
    & > .vxe-button--item+.vxe-button,
    & > .vxe-button--item+.vxe-button--item {
      margin-left: 0.8em;
    }
    & > .vxe-button--item {
      display: inline-block;
    }
  }
  .vxe-tools--wrapper {
    & > .vxe-button+.vxe-tool--item,
    & > .vxe-tool--item+.vxe-button,
    & > .vxe-tool--item+.vxe-tool--item {
      margin-left: 0.8em;
    }
    & > .vxe-tool--item {
      display: inline-block;
    }
  }
  .vxe-tools--wrapper {
    & > .vxe-button {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .vxe-buttons--wrapper,
  .vxe-tools--wrapper,
  .vxe-tools--operate {
    display: flex;
    align-items: center;
  }
  .vxe-buttons--wrapper,
  .vxe-tools--wrapper {
    flex-wrap: wrap;
  }
  .vxe-tools--operate {
    flex-shrink: 0;
  }
  .vxe-custom--wrapper {
    position: relative;
    &.is--active {
      & > .vxe-button {
        background-color: var(--vxe-ui-toolbar-custom-active-background-color);
        border-radius: 50%;
      }
    }
  }
}

.vxe-toolbar {
  font-size: var(--vxe-ui-font-size-default);
  &.size--medium {
    font-size: var(--vxe-ui-font-size-medium);
  }
  &.size--small {
    font-size: var(--vxe-ui-font-size-small);
  }
  &.size--mini {
    font-size: var(--vxe-ui-font-size-mini);
  }
}