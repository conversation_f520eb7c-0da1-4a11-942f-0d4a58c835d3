import { VxeUI } from '@vxe-ui/core';
import VxeFormDesignComponent from './src/form-design';
import { useWidgetView, useWidgetName, useSubtableView, useWidgetPropDataSource } from './src/use';
import { dynamicApp } from '../dynamics';
import './render';
export const VxeFormDesign = Object.assign({}, VxeFormDesignComponent, {
    install(app) {
        app.component(VxeFormDesignComponent.name, VxeFormDesignComponent);
    }
});
const formDesignHandle = {
    useWidgetName,
    useWidgetView,
    useSubtableView,
    useWidgetPropDataSource
};
dynamicApp.use(VxeFormDesign);
VxeUI.component(VxeFormDesignComponent);
VxeUI.formDesignHandle = formDesignHandle;
/**
 * 已废弃，请使用 formDesignHandle
 * @deprecated
 */
VxeUI.formDesign = formDesignHandle;
export const FormDesign = VxeFormDesign;
export default VxeFormDesign;
