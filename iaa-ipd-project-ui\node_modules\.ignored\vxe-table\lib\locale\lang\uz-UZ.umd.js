(function (global, factory) {
  if (typeof define === "function" && define.amd) {
    define("vxe-table-lang.uz-UZ", ["exports"], factory);
  } else if (typeof exports !== "undefined") {
    factory(exports);
  } else {
    var mod = {
      exports: {}
    };
    factory(mod.exports);
    global.vxeTableLangUzUZ = mod.exports;
  }
})(typeof globalThis !== "undefined" ? globalThis : typeof self !== "undefined" ? self : this, function (_exports) {
  "use strict";

  Object.defineProperty(_exports, "__esModule", {
    value: true
  });
  _exports.default = void 0;
  var _default = _exports.default = {
    vxe: {
      base: {
        pleaseInput: 'Iltimos kiriting',
        pleaseSelect: 'Iltimos tanlang',
        comma: '，',
        fullStop: '。'
      },
      loading: {
        text: 'yuklash ...'
      },
      error: {
        downErr: 'Yuklab olinmadi',
        errLargeData: "Bog'langan ma'lumotlarning miqdori juda katta bo'lsa, {0} dan foydalaning, aks holda u kechikishiga olib kelishi mumkin",
        groupFixed: '<PERSON><PERSON>i sarlavhalardan foydalanib, muzlatilgan ustunni guruh bilan belgilash kerak',
        groupMouseRange: "Guruh sarlavhasi bir vaqtning o'zida \"{0}\" kabi ishlatilishi mumkin emas va bu xatoga olib kelishi mumkin",
        groupTag: "\"Lim li}\" o'rniga guruh sarlavhalarida \"{1}\" o'rniga \"{0}\" o'rniga \"{0}\" dan foydalanish kerak",
        scrollErrProp: "Virtual aylantirish yoqilgandan so'ng \"{0}\" ushbu parametr qo'llab-quvvatlanmaydi",
        errConflicts: '"{0}" parametr "{1}" bilan ziddiyatlar',
        notSupportProp: '"{1}" parametri yoqilganda, u "{2}" parametrlari yoqilgan, aks holda xatolik yuz beradi',
        notConflictProp: "\"{0}\", \"{1}\" dan foydalanish kerak bo'lganda, aks holda funktsional to'qnashuvlar bo'lishi mumkin",
        unableInsert: "Belgilangan joyda kiritilishi mumkin emas, parametrlar to'g'ri yoki yo'qligini tekshiring",
        useErr: "\"{0}\" modulini o'rnatishda xatolik yuz berdi. Buyurtma noto'g'ri bo'lishi mumkin. Qaram modul jadvaldan oldin o'rnatilishi kerak",
        barUnableLink: 'Asboblar paneli stollarni sotolmaydi',
        expandContent: "Kengaytirilgan chiziqning uyasi \"tarkib\" bo'lishi kerak, iltimos, u to'g'ri ekanligini tekshiring",
        reqComp: "\"{0}\" komponenti yo'q bo'lib, u to'g'ri o'rnatilganligini tekshiring. https://vxeui.com/#/start/usaglobal",
        reqModule: "Yo'qolgan \"{0}\" moduli",
        reqProp: "Kerakli \"{0} parametrlari\" parametr yo'q, bu xatoga olib kelishi mumkin",
        emptyProp: "\"{0}\" parametr bo'sh bo'lishi mumkin emas",
        errProp: "\"{0}\", ehtimol \"{1}\" parametrini qo'llab-quvvatlamaydi",
        colRepet: "ustun. {0} = \"{1}\" takrorlanmoqda, bu ba'zi funktsiyalarga yaroqsiz bo'lishiga olib kelishi mumkin",
        notFunc: '"{0}" usuli mavjud emas',
        errFunc: '"{0}" parametri bu usul emas',
        notValidators: 'Global Tasdiqlash "{0}" mavjud emas',
        notFormats: 'Global formatlash "{0}" mavjud emas',
        notCommands: "\"{0}\" global ko'rsatmasi mavjud emas",
        notSlot: '"{0}" uyasi mavjud emas',
        noTree: "\"{0}\" daraxt tuzilmasida qo'llab-quvvatlanmaydi",
        notProp: "\"{0}\" ni qo'llab-quvvatlamagan parametr",
        checkProp: "Ma'lumotlar balandligi juda katta bo'lsa, katagiga ko'tarilishi mumkin. Tezlikni yaxshilash uchun \"{0}\" parametrini belgilash tavsiya etiladi",
        coverProp: '"{1}" parametr "{0}" parametr bir necha bor aniqlanadi, bu xatoga olib kelishi mumkin',
        uniField: '"{0}" dagi "{0}" da bir necha bor aniqlanadi, bu xatoga olib kelishi mumkin',
        repeatKey: 'Birlamchi kalit {0} = "{1}" ni takrorlash mumkin, bu xatoga olib kelishi mumkin',
        delFunc: '"{0}" himoyalangan, iltimos "{1}" dan foydalaning',
        delProp: '"{0}" parametri bekor qilinadi, "{1}" dan foydalaning',
        delEvent: '"{0}" bekor qilinadi, iltimos "{1}" dan foydalaning',
        removeProp: '"{0}" parametri eskirgan va tavsiya etilmaydi, bu xatoga olib kelishi mumkin',
        errFormat: 'Global formatlangan tarkib "Vxetable.formatsiyalar" va "Forter = {0}" montaji usulida aniqlanishi kerak.',
        notType: "\"{0}\" faylini qo'llab-quvvatlamaydi",
        notExp: "Ushbu brauzer import / eksport funktsiyasini qo'llab-quvvatlamaydi",
        impFields: "Import muvaffaqiyatsiz tugadi. Iltimos, maydon nomi va ma'lumotlar formati to'g'ri yoki noto'g'ri ekanligini tekshiring.",
        treeNotImp: "Daraxt stollari importni qo'llab-quvvatlamaydi",
        treeCrossDrag: 'Faqat birinchi darajani torting',
        treeDragChild: "Ota-onalar o'z farzandlariga sudrab ketolmaydilar",
        reqPlugin: "\"{1}\" https://vxeui.com/oter{/#/#/#/install-da o'rnatilmagan",
        errMaxRow: "Maksimal qo'llab-quvvatlanadigan ma'lumotlar hajmi {0} qatorlar hajmidan oshib ketishi mumkin, bu xatoga olib kelishi mumkin"
      },
      table: {
        emptyText: "Hali ma'lumot yo'q",
        allTitle: 'Hamma / Bekor qilish-ni tanlang',
        seqTitle: 'Ishlab chiqarish raqami',
        actionTitle: 'ishlamoq',
        confirmFilter: 'filtrlamoq',
        resetFilter: "Qayta o'rnatmoq",
        allFilter: 'hamma',
        sortAsc: "Ko'tarish tartibi: eng yuqori darajaga qadar eng past",
        sortDesc: 'Kamayish tartibi: eng yuqori darajaga qadar',
        filter: 'Tanlangan ustunlar uchun filtrlashni yoqish',
        impSuccess: '{0} yozuvlar muvaffaqiyatli keltirildi',
        expLoading: 'Eksportlash',
        expSuccess: 'Muvaffaqiyatli eksport',
        expError: 'Eksport muvaffaqiyatsiz tugadi',
        expFilename: 'Eksport_ {0}',
        expOriginFilename: 'Eksport_Souras_ {0}',
        customTitle: 'Ustun sozlamalari',
        customAll: 'hamma',
        customConfirm: 'tasdiqlamoq',
        customClose: 'yopish',
        customCancel: 'Bekor qilmoq',
        customRestore: 'Standartni tiklash',
        maxFixedCol: 'Muzlatilgan ustunlarning maksimal soni {0} dan oshmaydi',
        dragTip: "Ko'chiring: {0}",
        resizeColTip: 'Kengligi: {0} piksel',
        resizeRowTip: 'Yuqori: {0} piksel',
        rowGroupContentTotal: '{0}（{1}）'
      },
      grid: {
        selectOneRecord: 'Iltimos, kamida bitta yozuvni tanlang!',
        deleteSelectRecord: "Tanlangan yozuvni o'chirmoqchi ekanligingizga ishonchingiz komilmi?",
        removeSelectRecord: 'Tanlangan yozuvni olib tashlamoqchimisiz?',
        dataUnchanged: "Ma'lumotlar o'zgartirilmagan!",
        delSuccess: "Tanlangan yozuv muvaffaqiyatli o'chirildi!",
        saveSuccess: 'Muvaffaqiyatli saqlang!',
        operError: "Xato yuz berdi va operatsiya muvaffaqiyatsiz bo'ldi!"
      },
      select: {
        search: 'qidirish',
        loadingText: 'yuklamoq',
        emptyText: "Hali ma'lumot yo'q"
      },
      pager: {
        goto: 'Bormoq',
        gotoTitle: 'Sahifalar soni',
        pagesize: '{0} mahsulotlar / sahifa',
        total: 'Jami {0} yozuvlar',
        pageClassifier: 'Saxiy',
        homePage: 'Old sahifa',
        homePageTitle: 'Old sahifa',
        prevPage: 'Oldingi sahifa',
        prevPageTitle: 'Oldingi sahifa',
        nextPage: 'Keyingi sahifa',
        nextPageTitle: 'Keyingi sahifa',
        prevJump: 'Sahifa sakrash sahifasi',
        prevJumpTitle: 'Sahifa sakrash sahifasi',
        nextJump: 'Sahifadan sakrash',
        nextJumpTitle: 'Sahifadan sakrash',
        endPage: "So'nggi sahifa",
        endPageTitle: "So'nggi sahifa"
      },
      alert: {
        title: 'Tizim talab qiladi'
      },
      button: {
        confirm: 'tasdiqlamoq',
        cancel: 'Bekor qilmoq',
        clear: 'Aniq'
      },
      filter: {
        search: 'qidirish'
      },
      custom: {
        cstmTitle: 'Ustun sozlamalari',
        cstmRestore: 'Standartni tiklash',
        cstmCancel: 'Bekor qilmoq',
        cstmConfirm: 'Ishonch hosil qilmoq',
        cstmConfirmRestore: 'Iltimos, u standart ustun konfiguratsiyasiga tiklanganligini tasdiqlang.',
        cstmDragTarget: "Ko'chiring: {0}",
        setting: {
          colSort: 'Tartib',
          sortHelpTip: 'Ustunlar turini sozlash uchun ikonkani bosing va torting',
          colTitle: 'Ustun unvon',
          colResizable: 'Ustun kengligi (piksellar)',
          colVisible: "Ko'rsatish kerakmi",
          colFixed: 'Muzlatish ustuni',
          colFixedMax: 'Ustunlarni muzlatgich ({0} ustunlargacha)',
          fixedLeft: 'Chap tomon',
          fixedUnset: "O'rnatilmagan",
          fixedRight: "O'ng tomon"
        }
      },
      import: {
        modes: {
          covering: "Qayta yozish usulini (to'g'ridan-to'g'ri jadval ma'lumotlarini qayta yozing)",
          insert: "Pastki qismida qo'shimcha (stolning pastki qismida yangi ma'lumotlarni ilova qiling)",
          insertTop: "Yuqoridagi ilova (jadvalning yuqori qismida yangi ma'lumotlarni ilova)",
          insertBottom: "Pastki qismida qo'shimcha (stolning pastki qismida yangi ma'lumotlarni ilova qiling)"
        },
        impTitle: "Import ma'lumotlari",
        impFile: 'Fayl nomi',
        impSelect: 'Faylni tanlang',
        impType: 'Fayl turi',
        impOpts: 'Parametr sozlamalari',
        impMode: 'Import rejimi',
        impConfirm: 'Import qilmoq',
        impCancel: 'Bekor qilmoq'
      },
      export: {
        types: {
          csv: 'CSV (vergul) (*. CSV)',
          html: 'Veb-sahifa (* .html)',
          xml: "XML ma'lumotlari (* .xml)",
          txt: 'Matn fayli (jadval ajratilgan) (*. TXT)',
          xls: 'Excel 97-2003 ishchi daftar (* .xls)',
          xlsx: 'Excel ish daftari (* .xlsx)',
          pdf: 'PDF (* .pdf)'
        },
        modes: {
          empty: "Bo'sh ma'lumotlar",
          current: "Joriy ma'lumotlar (joriy sahifada ma'lumotlar)",
          selected: "Tanlangan ma'lumotlar (joriy sahifada tanlangan ma'lumotlar)",
          all: "To'liq ma'lumotlar (barcha postlangan ma'lumotlar bilan birga)"
        },
        printTitle: "Ma'lumotlarni chop etish",
        expTitle: "Eksport ma'lumotlari",
        expName: 'Fayl nomi',
        expNamePlaceholder: 'Iltimos fayl nomini kiriting',
        expSheetName: 'sarlavha',
        expSheetNamePlaceholder: 'Iltimos sarlavha kiriting',
        expType: 'Qulfni saqlash',
        expMode: "Ma'lumotni tanlang",
        expCurrentColumn: 'Barcha maydonlar',
        expColumn: 'Maydonni tanlang',
        expOpts: 'Parametr sozlamalari',
        expOptHeader: 'Sarlavha',
        expHeaderTitle: 'Stol sarlavhasi talab qilinadi',
        expOptFooter: 'Jadval oxiri',
        expFooterTitle: 'Stolning oxiri kerakmi?',
        expOptColgroup: 'Guruh sarlavhasi',
        expOptTitle: 'Ustun unvon',
        expTitleTitle: "Bu ustun sarlavhasi bo'ladimi, aks holda u ustun maydonining nomi sifatida ko'rsatiladi",
        expColgroupTitle: "Agar hozirgi bo'lsa, guruh tuzilishi bilan sarlavha qo'llab-quvvatlansa",
        expOptMerge: 'birlashmoq',
        expMergeTitle: "Agar hozirgi tuzilmalar bo'lgan hujayralar qo'llab-quvvatlansa",
        expOptAllExpand: 'Daraxtni kengaytiring',
        expAllExpandTitle: "Agar mavjud bo'lsa, ierarxik tuzilmalar bilan barcha ma'lumotlarni kengaytirish qo'llab-quvvatlanadi",
        expOptUseStyle: 'uslub',
        expUseStyleTitle: "Agar mavjud bo'lsa, uslublar bo'lgan hujayralar qo'llab-quvvatlanadi",
        expOptOriginal: "Manba ma'lumotlari",
        expOriginalTitle: "Agar u manba ma'lumoti bo'lsa, stollarga olib kiring",
        expPrint: 'Bosib chiqarish',
        expConfirm: 'Eksport',
        expCancel: 'Bekor qilmoq'
      },
      modal: {
        errTitle: 'Xato xabari',
        zoomMin: 'Kamaytirmoq',
        zoomIn: 'maksimal darajada oshirmoq',
        zoomOut: 'kamaytirish',
        close: 'yopish',
        miniMaxSize: 'Minimallashtirilgan derazalar soni {0} dan osha olmaydi',
        footPropErr: "Ko'rsatkichni ko'rsatish faqat stol dumini yoqish uchun ishlatiladi va shou-tasdiqlash-tugmasi bilan ishlatilishi kerak | Bekor qilish-tugma | uyalar"
      },
      drawer: {
        close: 'yopish'
      },
      form: {
        folding: 'Yaqin',
        unfolding: 'Kengaytirmoq'
      },
      toolbar: {
        import: 'Import qilmoq',
        export: 'Eksport',
        print: 'Bosib chiqarish',
        refresh: 'yangilamoq',
        zoomIn: "To'liq ekran",
        zoomOut: 'kamaytirish',
        custom: 'Ustun sozlamalari',
        customAll: 'hamma',
        customConfirm: 'tasdiqlamoq',
        customRestore: "Qayta o'rnatmoq",
        fixedLeft: "Chapda muzlatib qo'ying",
        fixedRight: "O'ng tomonda muzlatib qo'ying",
        cancelFixed: "Urug'lar ustunini"
      },
      datePicker: {
        yearTitle: '{0} yillar'
      },
      dateRangePicker: {
        pleaseRange: '请选择开始日期与结束日期'
      },
      input: {
        date: {
          m1: '01 oy',
          m2: '02 oy',
          m3: '03 oy',
          m4: '04 oy',
          m5: '05 oy',
          m6: '06 oy',
          m7: '07 oy',
          m8: '08 oy',
          m9: '09 oy',
          m10: '10 oy',
          m11: '11 oy',
          m12: '12 oy',
          quarterLabel: '{0} yillar',
          monthLabel: '{0} yillar',
          dayLabel: '{0} yillar {1}',
          labelFormat: {
            date: 'yyyy-MM-dd',
            time: 'HH:mm:ss',
            datetime: 'yyyy-MM-dd HH:mm:ss',
            week: 'Week WW of year yyyy',
            month: 'yyyy-MM',
            quarter: 'quarter q of year yyyy',
            year: 'yyyy'
          },
          weeks: {
            w: '',
            w0: 'Yakshanba',
            w1: 'dushanba kuni',
            w2: 'Seshanba',
            w3: 'Chorshanba',
            w4: 'Payshanba',
            w5: 'Juma',
            w6: 'Shanba'
          },
          months: {
            m0: 'Yanvar',
            m1: 'Fevral',
            m2: 'Mart oyi',
            m3: 'Aprel',
            m4: 'Mayl',
            m5: 'Iyun',
            m6: 'Iyul',
            m7: 'Avgust',
            m8: 'Sentyabr',
            m9: 'Oktyabr',
            m10: 'Noyabr',
            m11: 'Dekabr'
          },
          quarters: {
            q1: 'First quarter',
            q2: 'Second quarter',
            q3: 'Third quarter',
            q4: 'Fourth quarter'
          }
        }
      },
      numberInput: {
        currencySymbol: '¥'
      },
      imagePreview: {
        popupTitle: "Oldindan ko'rish",
        operBtn: {
          zoomOut: 'Qisqarmoq',
          zoomIn: 'kattalashtirmoq',
          pctFull: "Bir xil o'lchash",
          pct11: "Asl hajmni ko'rsatish",
          rotateLeft: 'Chapni aylantiring',
          rotateRight: "O'ng tomonga buriling",
          print: 'Rasmni chop etish uchun bosing',
          download: 'Rasmni yuklab olish uchun bosing'
        }
      },
      upload: {
        fileBtnText: 'Yuklash uchun bosing yoki torting',
        imgBtnText: 'Yuklash uchun bosing yoki torting',
        dragPlaceholder: 'Iltimos, yuklash uchun ushbu hududga faylni torting va tashlang',
        imgSizeHint: 'Yagona {0}',
        imgCountHint: '{0} gacha',
        fileTypeHint: "Qo'llab-quvvatlash {0} Fayl turlari",
        fileSizeHint: 'Bitta fayl hajmi {0} dan oshmaydi',
        fileCountHint: '{0} fayllar yuklanishi mumkin',
        uploadTypeErr: 'Fayl turi mos kelmayapti!',
        overCountErr: "Faqat {0} fayllari ko'pini tanlab olish mumkin!",
        overCountExtraErr: "{0} maksimal soni oshib ketdi va ortiqcha {1} fayllari e'tiborga olinmaydi!",
        overSizeErr: 'Fayl hajmi {0} dan oshmasligi kerak!',
        reUpload: 'Qayta yuklamoq',
        uploadProgress: 'Yuklash {0}%',
        uploadErr: 'Yuklash muvaffaqiyatsiz tugadi',
        uploadSuccess: 'Muvaffaqiyatli yuklang',
        moreBtnText: "Ko'proq ({0})",
        viewItemTitle: "Ko'rish uchun bosing",
        morePopup: {
          readTitle: "Ro'yxatni ko'rish",
          imageTitle: 'Rasmlarni yuklang',
          fileTitle: 'Faylni yuklash'
        }
      },
      empty: {
        defText: "Hali ma'lumot yo'q"
      },
      colorPicker: {
        clear: 'Aniq',
        confirm: 'tasdiqlamoq',
        copySuccess: "Clipboardga nusxa ko'chirish: {0}"
      },
      formDesign: {
        formName: 'Forma nomi',
        defFormTitle: "Noma'lum shakl",
        widgetPropTab: 'Boshqaruv xususiyatlari',
        widgetFormTab: 'Forma xususiyatlari',
        error: {
          wdFormUni: "Ushbu turdagi boshqaruv faqat shakldagi bittasini qo'shishga ruxsat beriladi",
          wdSubUni: "Ushbu turdagi boshqarishning faqat bittasini subtable-da qo'shishga ruxsat beriladi"
        },
        styleSetting: {
          btn: 'Uslub sozlamalari',
          title: 'Uslub sozlamalari',
          layoutTitle: 'Boshqarish tartibi',
          verticalLayout: 'Yuqoriga va pastga tartib',
          horizontalLayout: 'Gorizontal tartib',
          styleTitle: 'Sarlavha uslubi',
          boldTitle: 'Qalin sarlavha',
          fontBold: 'Jasur',
          fontNormal: "an'anaviy",
          colonTitle: "Yo'g'on ichakni ko'rsating",
          colonVisible: "ko'rsatish",
          colonHidden: 'yashirmoq',
          alignTitle: 'Tekislash',
          widthTitle: 'Sarlavha kengligi',
          alignLeft: 'Chapda',
          alignRight: "O'ngda",
          unitPx: 'Piksel',
          unitPct: 'foiz'
        },
        widget: {
          group: {
            base: 'Asosiy boshqarish vositalari',
            layout: 'Tartibni boshqarish',
            system: 'Tizimni boshqarish',
            module: 'Modul nazorati',
            chart: 'Jadvalni boshqarish',
            advanced: 'Murakkab boshqarish vositalari'
          },
          copyTitle: 'Kuchli_ {0}',
          component: {
            input: 'Kiritish qutisi',
            textarea: 'Matn maydoni',
            select: 'Tanlash uchun pastga torting',
            row: 'Bir qator va bir nechta ustunlar',
            title: 'sarlavha',
            text: 'matn',
            subtable: 'Kichik stol',
            VxeSwitch: "yo'qmi",
            VxeInput: 'Kiritish qutisi',
            VxeNumberInput: 'raqam',
            VxeDatePicker: 'sana',
            VxeTextarea: 'Matn maydoni',
            VxeSelect: 'Tanlash uchun pastga torting',
            VxeTreeSelect: 'Daraxt tanlash',
            VxeRadioGroup: 'Radio qutisi',
            VxeCheckboxGroup: 'Katakchani belgilang',
            VxeUploadFile: 'hujjat',
            VxeUploadImage: 'rasm',
            VxeRate: 'Xol',
            VxeSlider: 'slayder'
          }
        },
        widgetProp: {
          name: 'Boshqarish nomi',
          placeholder: 'Tez',
          required: 'Kerakli tekshirish',
          multiple: 'Bir nechta tanlovga ruxsat beriladi',
          displaySetting: {
            name: 'Displey sozlamalari',
            pc: 'Kompyuter',
            mobile: 'Mobil',
            visible: "ko'rsatish",
            hidden: 'yashirmoq'
          },
          dataSource: {
            name: "Ma'lumot manbai",
            defValue: 'Variant {0}',
            addOption: "Variantlar qo'shing",
            batchEditOption: 'Partiyani tahrirlash',
            batchEditTip: "Har bir satr to'g'ridan-to'g'ri nusxadan, Excel va WPS-dan to'g'ridan-to'g'ri nusxa va pastroqni qo'llab-quvvatlaydigan variantga mos keladi.",
            batchEditSubTip: "Har bir qator parametrga mos keladi. Agar bu guruh bo'lsa, bola buyumlari bo'shliq yoki yorliq kaliti bilan boshlanishi mumkin va u to'g'ridan-to'g'ri nusxa va jadval, Excel va WPS-dan pastroq.",
            buildOption: 'Variantlar'
          },
          rowProp: {
            colSize: 'Ustunlar soni',
            col2: 'Ikki ustun',
            col3: 'Uchta ustun',
            col4: "To'rt ustun",
            col6: 'Olti ustun',
            layout: 'tartib'
          },
          textProp: {
            name: 'tarkib',
            alignTitle: 'Tekislash',
            alignLeft: 'Chapda',
            alignCenter: 'Markaz',
            alignRight: "O'ngda",
            colorTitle: 'Shrift rangi',
            sizeTitle: 'Shrift hajmi',
            boldTitle: 'Shrift qalin shrift',
            fontNormal: "an'anaviy",
            fontBold: 'Jasur'
          },
          subtableProp: {
            seqTitle: 'Ishlab chiqarish raqami',
            showSeq: "Seriya raqamini ko'rsating",
            showCheckbox: 'Bir nechta tanlovga ruxsat beriladi',
            errSubDrag: "So'rg'lantiriladigan bu nazoratni qo'llab-quvvatlamaydi, iltimos, boshqa boshqaruvlardan foydalaning",
            colPlace: 'Boshqarishni boshqaring'
          },
          uploadProp: {
            limitFileCount: 'Fayl miqdori chegarasi',
            limitFileSize: 'Fayl hajmi chegarasi',
            multiFile: 'Bir nechta fayllarni yuklashiga ruxsat bering',
            limitImgCount: 'Rasmlarning soni cheklangan',
            limitImgSize: 'Rasm hajmi cheklovi',
            multiImg: 'Yuklash uchun bir nechta rasmlarga ruxsat bering'
          }
        }
      },
      listDesign: {
        fieldSettingTab: 'Dala sozlamalari',
        listSettingTab: 'Parametr sozlamalari',
        searchTitle: "So'rov mezonlari",
        listTitle: "Ro'yxat maydon",
        searchField: "So'rov maydonlari",
        listField: "Ro'yxat maydon",
        activeBtn: {
          ActionButtonUpdate: 'tahrirlamoq',
          ActionButtonDelete: "o'chirmoq"
        },
        search: {
          addBtn: 'tahrirlamoq',
          emptyText: "So'rov shartlari sozlanmagan",
          editPopupTitle: "So'rov maydonlarini tahrirlash"
        },
        searchPopup: {
          colTitle: 'sarlavha',
          saveBtn: 'tejash'
        }
      },
      text: {
        copySuccess: "Clipboardga nusxa ko'chirildi",
        copyError: "Hozirgi muhit ushbu operatsiyani qo'llab-quvvatlamaydi"
      },
      countdown: {
        formats: {
          yyyy: 'Yil',
          MM: 'oy',
          dd: 'osmon',
          HH: 'soat',
          mm: 'nuqta',
          ss: 'Ikkinchi'
        }
      },
      plugins: {
        extendCellArea: {
          area: {
            mergeErr: "Ushbu operatsiyani birlashtirilgan hujayralarda o'tkazib bo'lmaydi",
            multiErr: "Ushbu operatsiyani ko'p tanlangan hududda bajarib bo'lmaydi",
            selectErr: 'Belgilangan hududda hujayralarni ishlata olmaslik',
            extendErr: "Agar kengaytirilgan hududda birlashtirilgan hujayralar bo'lsa, barcha birlashtirilgan hujayralar bir xil o'lchamda bo'lishi kerak",
            pasteMultiErr: "Bo'sh bo'lma, buni amalga oshirish uchun bir xil o'lchamdagi joylardan nusxa olish va joylashtirish kerak",
            cpInvalidErr: "Ushbu operatsiyani bajarib bo'lmaydi, siz tanlagan hududda taqiqlangan ustunlar mavjud ({0})"
          },
          fnr: {
            title: 'Toping va almashtiring',
            findLabel: 'Topmoq',
            replaceLabel: 'almashtirmoq',
            findTitle: 'Tarkibni toping:',
            replaceTitle: 'Bilan almashtiring:',
            tabs: {
              find: 'Topmoq',
              replace: 'almashtirmoq'
            },
            filter: {
              re: 'Doimiy ifodalar',
              whole: "To'liq so'z mos keladigan",
              sensitive: 'harflar katta-kichikligiga sezgir'
            },
            btns: {
              findNext: 'Keyingi birini toping',
              findAll: 'Hammasini toping',
              replace: 'almashtirmoq',
              replaceAll: 'Barchasini almashtiring',
              cancel: 'Bekor qilmoq'
            },
            header: {
              seq: '#',
              cell: 'Uyali',
              value: 'qiymati'
            },
            body: {
              row: 'Line: {0}',
              col: 'Ustun: {0}'
            },
            empty: '(Null qiymati)',
            reError: "Oddiy ifoda noto'g'ri",
            recordCount: '{0} hujayralar topildi',
            notCell: 'Mos keladigan hujayra topilmadi',
            replaceSuccess: '{0} hujayralar muvaffaqiyatli almashtirildi'
          }
        },
        extendPivotTable: {
          aggregation: {
            grouping: '分组',
            values: '值'
          }
        },
        filterComplexInput: {
          menus: {
            fixedColumn: 'Muzlatish ustuni',
            fixedGroup: 'Muzlatishni guruhlash',
            cancelFixed: 'Free Free ni bekor qiling',
            fixedLeft: "Chap tomonni muzlatib qo'ying",
            fixedRight: "O'ng tomonni muzlatib qo'ying"
          },
          cases: {
            equal: 'teng',
            gt: 'Undan katta',
            lt: 'Dan kichik; .. dan kamroq',
            begin: 'Boshlanishi',
            endin: 'Tugashi',
            include: "Qo'shmoq",
            isSensitive: 'harflar katta-kichikligiga sezgir'
          }
        },
        filterCombination: {
          menus: {
            sort: 'Tartib',
            clearSort: 'Tozalash',
            sortAsc: "Ko'tarish tartibi",
            sortDesc: 'Qabullash tartibi',
            fixedColumn: 'Muzlatish ustuni',
            fixedGroup: 'Muzlatishni guruhlash',
            cancelFixed: 'Free Free ni bekor qiling',
            fixedLeft: "Chap tomonni muzlatib qo'ying",
            fixedRight: "O'ng tomonni muzlatib qo'ying",
            clearFilter: 'Filtrni tozalash',
            textOption: 'Matnli filtrlash',
            numberOption: 'Raqamli filtrlash'
          },
          popup: {
            title: 'Maxsus filtrlash usullari',
            currColumnTitle: 'Joriy ustun:',
            and: 'va',
            or: 'yoki',
            describeHtml: 'Mavjudmi? Formatni bildiradi, <br/> FAQAT FOYDALANISH * Bir nechta belgilarni anglatadi'
          },
          cases: {
            equal: 'teng',
            unequal: 'Ga teng emas',
            gt: 'Undan katta',
            ge: 'Dan katta yoki teng',
            lt: 'Dan kichik; .. dan kamroq',
            le: 'Dan kam yoki unga teng',
            begin: 'Boshlanishi',
            notbegin: 'Bu boshida emas',
            endin: 'Tugashi',
            notendin: 'Tugashi emas',
            include: "Qo'shmoq",
            exclude: "Qo'shilmagan",
            between: 'Orasida',
            custom: 'Custom filtrlash',
            insensitive: 'Ishni sezgir',
            isSensitive: 'harflar katta-kichikligiga sezgir'
          },
          empty: "(bo'sh)",
          notData: "Uchrashuv yo'q"
        }
      },
      pro: {
        area: {
          mergeErr: "Ushbu operatsiyani birlashtirilgan hujayralarda o'tkazib bo'lmaydi",
          multiErr: "Ushbu operatsiyani ko'p tanlangan hududda bajarib bo'lmaydi",
          extendErr: "Agar kengaytirilgan hududda birlashtirilgan hujayralar bo'lsa, barcha birlashtirilgan hujayralar bir xil o'lchamda bo'lishi kerak",
          pasteMultiErr: "Bo'sh bo'lma, buni amalga oshirish uchun bir xil o'lchamdagi joylardan nusxa olish va joylashtirish kerak"
        },
        fnr: {
          title: 'Toping va almashtiring',
          findLabel: 'Topmoq',
          replaceLabel: 'almashtirmoq',
          findTitle: 'Tarkibni toping:',
          replaceTitle: 'Bilan almashtiring:',
          tabs: {
            find: 'Topmoq',
            replace: 'almashtirmoq'
          },
          filter: {
            re: 'Doimiy ifodalar',
            whole: "To'liq so'z mos keladigan",
            sensitive: 'harflar katta-kichikligiga sezgir'
          },
          btns: {
            findNext: 'Keyingi birini toping',
            findAll: 'Hammasini toping',
            replace: 'almashtirmoq',
            replaceAll: 'Barchasini almashtiring',
            cancel: 'Bekor qilmoq'
          },
          header: {
            seq: '#',
            cell: 'Uyali',
            value: 'qiymati'
          },
          empty: '(Null qiymati)',
          reError: "Oddiy ifoda noto'g'ri",
          recordCount: '{0} hujayralar topildi',
          notCell: 'Mos keladigan hujayra topilmadi',
          replaceSuccess: '{0} hujayralar muvaffaqiyatli almashtirildi'
        }
      },
      renderer: {
        search: 'qidirish',
        cases: {
          equal: 'teng',
          unequal: 'Ga teng emas',
          gt: 'Undan katta',
          ge: 'Dan katta yoki teng',
          lt: 'Dan kichik; .. dan kamroq',
          le: 'Dan kam yoki unga teng',
          begin: 'Boshlanishi',
          notbegin: 'Bu boshida emas',
          endin: 'Tugashi',
          notendin: 'Tugashi emas',
          include: "Qo'shmoq",
          exclude: "Qo'shilmagan",
          between: 'Orasida',
          custom: 'Custom filtrlash',
          insensitive: 'Ishni sezgir',
          isSensitive: 'harflar katta-kichikligiga sezgir'
        },
        combination: {
          menus: {
            sort: 'Tartib',
            clearSort: 'Tozalash',
            sortAsc: "Ko'tarish tartibi",
            sortDesc: 'Qabullash tartibi',
            fixedColumn: 'Muzlatish ustuni',
            fixedGroup: 'Muzlatishni guruhlash',
            cancelFixed: 'Free Free ni bekor qiling',
            fixedLeft: "Chapni muzlatib qo'ying",
            fixedRight: "O'ng tomonga muzlatib qo'ying",
            clearFilter: 'Filtrni tozalash',
            textOption: 'Matnli filtrlash',
            numberOption: 'Raqamli filtrlash'
          },
          popup: {
            title: 'Maxsus filtrlash usullari',
            currColumnTitle: 'Joriy ustun:',
            and: 'va',
            or: 'yoki',
            describeHtml: 'Mavjudmi? Formatni bildiradi, <br/> FAQAT FOYDALANISH * Bir nechta belgilarni anglatadi'
          },
          empty: "(bo'sh)",
          notData: "Uchrashuv yo'q"
        }
      }
    }
  };
});