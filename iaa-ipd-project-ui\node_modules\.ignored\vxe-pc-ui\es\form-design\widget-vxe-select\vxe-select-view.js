import { defineComponent, h, inject } from 'vue';
import { getI18n } from '@vxe-ui/core';
import { useWidgetName } from '../../form-design/src/use';
import VxeFormItemComponent from '../../form/src/form-item';
import VxeSelectComponent from '../../select/src/select';
export const WidgetVxeSelectViewComponent = defineComponent({
    props: {
        renderOpts: {
            type: Object,
            default: () => ({})
        },
        renderParams: {
            type: Object,
            default: () => ({})
        }
    },
    emits: [],
    setup(props) {
        const $xeFormView = inject('$xeFormView', null);
        const { computeKebabCaseName } = useWidgetName(props);
        const changeEvent = () => {
            const { renderParams } = props;
            const { widget } = renderParams;
            if ($xeFormView) {
                const itemValue = $xeFormView ? $xeFormView.getItemValue(widget) : null;
                $xeFormView.updateWidgetStatus(widget, itemValue);
            }
        };
        return () => {
            const { renderParams } = props;
            const { widget, isViewMode } = renderParams;
            const { options } = widget;
            const kebabCaseName = computeKebabCaseName.value;
            return h(VxeFormItemComponent, {
                class: ['vxe-form-design--widget-render-form-item', `widget-${kebabCaseName}`],
                title: widget.title,
                field: widget.field,
                itemRender: {}
            }, {
                default() {
                    return h(VxeSelectComponent, {
                        modelValue: $xeFormView ? $xeFormView.getItemValue(widget) : null,
                        placeholder: options.placeholder || getI18n('vxe.base.pleaseSelect'),
                        options: isViewMode ? options.options : [],
                        optionProps: { label: 'value', value: 'value' },
                        onChange: changeEvent,
                        'onUpdate:modelValue'(val) {
                            if ($xeFormView) {
                                $xeFormView.setItemValue(widget, val);
                            }
                        }
                    });
                }
            });
        };
    }
});
