{"version": 3, "sources": ["../../@onlyoffice/document-editor-vue/src/utils/loadScript.ts", "../../@onlyoffice/document-editor-vue/src/components/DocumentEditor.vue"], "sourcesContent": ["/*\r\n* (c) Copyright Ascensio System SIA 2024\r\n*\r\n* Licensed under the Apache License, Version 2.0 (the \"License\");\r\n* you may not use this file except in compliance with the License.\r\n* You may obtain a copy of the License at\r\n*\r\n*     http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing, software\r\n* distributed under the License is distributed on an \"AS IS\" BASIS,\r\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n* See the License for the specific language governing permissions and\r\n* limitations under the License.\r\n*/\r\n\r\nconst loadScript = async (url: string, id: string) => {\r\n  return new Promise((resolve, reject) => {\r\n    try {\r\n      // If DocsAPI is defined return resolve.\r\n      //@ts-ignore\r\n      if (window.DocsAPI) return resolve(null);\r\n\r\n      const existedScript = document.getElementById(id);\r\n\r\n      if (existedScript) {\r\n        // If the script element is found, wait for it to load.\r\n        let intervalHandler = setInterval(() => {\r\n          const loading = existedScript.getAttribute(\"loading\");\r\n          if (loading) {\r\n            // If the download is not completed, continue to wait.\r\n            return;\r\n          } else {\r\n            // If the download is completed, stop the wait.\r\n            clearInterval(intervalHandler);\r\n\r\n            // If DocsAPI is defined, after loading return resolve.\r\n            //@ts-ignore\r\n            if (window.DocsAPI) return resolve(null);\r\n\r\n            // If DocsAPI is not defined, delete the existing script and create a new one.\r\n            const script = _createScriptTag(id, url, resolve, reject);\r\n            existedScript.remove();\r\n            document.body.appendChild(script);\r\n          }\r\n        }, 500);\r\n      } else {\r\n        // If the script element is not found, create it.\r\n        const script = _createScriptTag(id, url, resolve, reject);\r\n        document.body.appendChild(script);\r\n      }\r\n    } catch (e) {\r\n      console.error(e);\r\n    }\r\n  });\r\n};\r\n\r\nconst _createScriptTag = (id: string, url: string, resolve: (value: unknown) => void, reject: (reason?: any) => void) => {\r\n  const script = document.createElement(\"script\");\r\n\r\n  script.id = id;\r\n  script.type = \"text/javascript\";\r\n  script.src = url;\r\n  script.async = true;\r\n\r\n  script.onload = () => {\r\n    // Remove attribute loading after loading is complete.\r\n    script.removeAttribute(\"loading\");\r\n    resolve(null);\r\n  };\r\n  script.onerror = (error: any) => {\r\n    // Remove attribute loading after loading is complete.\r\n    script.removeAttribute(\"loading\");\r\n    reject(error);\r\n  };\r\n\r\n  script.setAttribute(\"loading\", \"\");\r\n\r\n  return script;\r\n};\r\n\r\nexport default loadScript;\r\n", "/*\r\n* (c) Copyright Ascensio System SIA 2024\r\n*\r\n* Licensed under the Apache License, Version 2.0 (the \"License\");\r\n* you may not use this file except in compliance with the License.\r\n* You may obtain a copy of the License at\r\n*\r\n*     http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing, software\r\n* distributed under the License is distributed on an \"AS IS\" BASIS,\r\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n* See the License for the specific language governing permissions and\r\n* limitations under the License.\r\n*/\r\n\r\n<template>\r\n  <div :id=\"id\"></div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { defineComponent, PropType } from 'vue';\r\nimport loadScript from \"../utils/loadScript\";\r\nimport { IConfig } from \"../model/config\";\r\n\r\ndeclare global {\r\n  interface Window {\r\n    DocsAPI?: any;\r\n    DocEditor?: any;\r\n  }\r\n}\r\n\r\nexport default defineComponent({\r\n  name: 'DocumentEditor',\r\n  props: {\r\n    id: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    documentServerUrl: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    config: {\r\n      type: Object as PropType<IConfig>,\r\n      required: true\r\n    },\r\n    document_fileType: String,\r\n    document_title: String,\r\n    documentType: String,\r\n    editorConfig_lang: String,\r\n    height: String,\r\n    type: String,\r\n    width: String,\r\n\r\n    onLoadComponentError: Function,\r\n\r\n    events_onAppReady: Function,\r\n    events_onDocumentStateChange: Function,\r\n    events_onMetaChange: Function,\r\n    events_onDocumentReady: Function,\r\n    events_onInfo: Function,\r\n    events_onWarning: Function,\r\n    events_onError: Function,\r\n    events_onRequestSharingSettings: Function,\r\n    events_onRequestRename: Function,\r\n    events_onMakeActionLink: Function,\r\n    events_onRequestInsertImage: Function,\r\n    events_onRequestSaveAs: Function,\r\n    /**\r\n     * @deprecated Deprecated since version 7.5, please use events_onRequestSelectSpreadsheet instead.\r\n     */\r\n    events_onRequestMailMergeRecipients: Function,\r\n    /**\r\n     * @deprecated Deprecated since version 7.5, please use events_onRequestSelectDocument instead.\r\n     */\r\n    events_onRequestCompareFile: Function,\r\n    events_onRequestEditRights: Function,\r\n    events_onRequestHistory: Function,\r\n    events_onRequestHistoryClose: Function,\r\n    events_onRequestHistoryData: Function,\r\n    events_onRequestRestore: Function,\r\n    events_onRequestSelectSpreadsheet: Function,\r\n    events_onRequestSelectDocument: Function,\r\n  },\r\n  mounted() {\r\n    let url = this.documentServerUrl;\r\n    if (!url!.endsWith(\"/\")) url += \"/\";\r\n\r\n    const docApiUrl = `${url}web-apps/apps/api/documents/api.js`;\r\n    loadScript(docApiUrl, \"onlyoffice-api-script\")\r\n      .then(() => this.onLoad())\r\n      .catch(()=> {this.onError(-2)});\r\n  },\r\n  unmounted() {\r\n    const id = this.id || \"\";\r\n\r\n    if (window?.DocEditor?.instances[id]) {\r\n      window.DocEditor.instances[id].destroyEditor();\r\n      window.DocEditor.instances[id] = undefined;\r\n    }\r\n  },\r\n  watch: {\r\n    config: {\r\n      handler: function (newVal, oldVal) {\r\n        this.onChangeProps()     \r\n      },\r\n      deep: true\r\n    },\r\n    document_fileType: function(newVal, oldVal) { this.onChangeProps() },\r\n    document_title: function(newVal, oldVal) { this.onChangeProps() },\r\n    documentType: function(newVal, oldVal) { this.onChangeProps() },\r\n    editorConfig_lang: function(newVal, oldVal) { this.onChangeProps() },\r\n    height: function(newVal, oldVal) { this.onChangeProps() },\r\n    type: function(newVal, oldVal) { this.onChangeProps() },\r\n    width: function(newVal, oldVal) { this.onChangeProps() }\r\n  },\r\n  methods: {\r\n    onLoad () {\r\n       try {\r\n        const id = this.id || \"\";\r\n\r\n        if (!window.DocsAPI) this.onError(-3);\r\n        if (window?.DocEditor?.instances[id]) {\r\n          console.log(\"Skip loading. Instance already exists\", id);\r\n          return;\r\n        }\r\n\r\n        if (!window?.DocEditor?.instances) {\r\n          window.DocEditor = { instances: {} };\r\n        }\r\n\r\n        let initConfig = Object.assign({\r\n          document: {\r\n            fileType: this.document_fileType,\r\n            title: this.document_title,\r\n          },\r\n          documentType: this.documentType,\r\n          editorConfig: {\r\n            lang: this.editorConfig_lang,\r\n          },\r\n          events: {\r\n            onAppReady: this.onAppReady,\r\n            onDocumentStateChange: this.events_onDocumentStateChange,\r\n            onMetaChange: this.events_onMetaChange,\r\n            onDocumentReady: this.events_onDocumentReady,\r\n            onInfo: this.events_onInfo,\r\n            onWarning: this.events_onWarning,\r\n            onError: this.events_onError,\r\n            onRequestSharingSettings: this.events_onRequestSharingSettings,\r\n            onRequestRename: this.events_onRequestRename,\r\n            onMakeActionLink: this.events_onMakeActionLink,\r\n            onRequestInsertImage: this.events_onRequestInsertImage,\r\n            onRequestSaveAs: this.events_onRequestSaveAs,\r\n            onRequestMailMergeRecipients: this.events_onRequestMailMergeRecipients,\r\n            onRequestCompareFile: this.events_onRequestCompareFile,\r\n            onRequestEditRights: this.events_onRequestEditRights,\r\n            onRequestHistory: this.events_onRequestHistory,\r\n            onRequestHistoryClose: this.events_onRequestHistoryClose,\r\n            onRequestHistoryData: this.events_onRequestHistoryData,\r\n            onRequestRestore: this.events_onRequestRestore,\r\n            onRequestSelectSpreadsheet: this.events_onRequestSelectSpreadsheet,\r\n            onRequestSelectDocument: this.events_onRequestSelectDocument\r\n          },\r\n          height: this.height,\r\n          type: this.type,\r\n          width: this.width,\r\n        }, this.config || {});\r\n\r\n        const editor = window.DocsAPI.DocEditor(id, initConfig);\r\n        window.DocEditor.instances[id] = editor;\r\n      } catch (err: any) {\r\n        console.error(err);\r\n        this.onError(-1);\r\n      }\r\n    },\r\n    onError(errorCode: Number) {\r\n      let message;\r\n\r\n      switch(errorCode) {\r\n        case -2:\r\n          message = \"Error load DocsAPI from \" + this.documentServerUrl;\r\n          break;\r\n        case -3:\r\n          message = \"DocsAPI is not defined\";\r\n          break;\r\n        default:\r\n          message = \"Unknown error loading component\";\r\n          errorCode = -1;\r\n      }\r\n\r\n      if (typeof this.onLoadComponentError == \"undefined\") {\r\n        console.error(message);\r\n      } else {\r\n        this.onLoadComponentError(errorCode, message);\r\n      }\r\n    },\r\n    onAppReady() {\r\n      const id = this.id || \"\";\r\n      this.events_onAppReady!(window.DocEditor.instances[id]);\r\n    },\r\n    onChangeProps () {\r\n      const id = this.id || \"\";\r\n\r\n      if (window?.DocEditor?.instances[id]) {\r\n        window.DocEditor.instances[id].destroyEditor();\r\n        window.DocEditor.instances[id] = undefined;\r\n  \r\n        console.log(\"Important props have been changed. Load new Editor.\");\r\n        this.onLoad();\r\n      }\r\n    }\r\n  }\r\n});\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,IAAM,aAAa,SAAO,KAAa,IAAU;AAAA,SAAA,UAAA,QAAA,QAAA,QAAA,WAAA;;AAC/C,aAAA,CAAA,GAAO,IAAI,QAAQ,SAAC,SAAS,QAAM;AACjC,YAAI;AAGF,cAAI,OAAO;AAAS,mBAAO,QAAQ,IAAI;AAEvC,cAAM,kBAAgB,SAAS,eAAe,EAAE;AAEhD,cAAI,iBAAe;AAEjB,gBAAI,oBAAkB,YAAY,WAAA;AAChC,kBAAM,UAAU,gBAAc,aAAa,SAAS;AACpD,kBAAI,SAAS;AAEX;qBACK;AAEL,8BAAc,iBAAe;AAI7B,oBAAI,OAAO;AAAS,yBAAO,QAAQ,IAAI;AAGvC,oBAAMA,UAAS,iBAAiB,IAAI,KAAK,SAAS,MAAM;AACxD,gCAAc,OAAM;AACpB,yBAAS,KAAK,YAAYA,OAAM;;eAEjC,GAAG;iBACD;AAEL,gBAAMA,UAAS,iBAAiB,IAAI,KAAK,SAAS,MAAM;AACxD,qBAAS,KAAK,YAAYA,OAAM;;iBAE3B,GAAG;AACV,kBAAQ,MAAM,CAAC;;MAEnB,CAAC,CAAC;;;;AAGJ,IAAM,mBAAmB,SAAC,IAAY,KAAa,SAAmC,QAA8B;AAClH,MAAMA,UAAS,SAAS,cAAc,QAAQ;AAE9C,EAAAA,QAAO,KAAK;AACZ,EAAAA,QAAO,OAAO;AACd,EAAAA,QAAO,MAAM;AACb,EAAAA,QAAO,QAAQ;AAEf,EAAAA,QAAO,SAAS,WAAA;AAEd,IAAAA,QAAO,gBAAgB,SAAS;AAChC,YAAQ,IAAI;EACd;AACA,EAAAA,QAAO,UAAU,SAAC,OAAU;AAE1B,IAAAA,QAAO,gBAAgB,SAAS;AAChC,WAAO,KAAK;EACd;AAEA,EAAAA,QAAO,aAAa,WAAW,EAAE;AAEjC,SAAOA;AACT;AC/CA,IAAA,SAAe,gBAAgB;EAC7B,MAAM;EACN,OAAO;IACL,IAAI;MACF,MAAM;MACN,UAAU;IACX;IACD,mBAAmB;MACjB,MAAM;MACN,UAAU;IACX;IACD,QAAQ;MACN,MAAM;MACN,UAAU;IACX;IACD,mBAAmB;IACnB,gBAAgB;IAChB,cAAc;IACd,mBAAmB;IACnB,QAAQ;IACR,MAAM;IACN,OAAO;IAEP,sBAAsB;IAEtB,mBAAmB;IACnB,8BAA8B;IAC9B,qBAAqB;IACrB,wBAAwB;IACxB,eAAe;IACf,kBAAkB;IAClB,gBAAgB;IAChB,iCAAiC;IACjC,wBAAwB;IACxB,yBAAyB;IACzB,6BAA6B;IAC7B,wBAAwB;;;;IAIxB,qCAAqC;;;;IAIrC,6BAA6B;IAC7B,4BAA4B;IAC5B,yBAAyB;IACzB,8BAA8B;IAC9B,6BAA6B;IAC7B,yBAAyB;IACzB,mCAAmC;IACnC,gCAAgC;EACjC;EACD,SAAO,WAAA;AAAP,QAQC,QAAA;AAPC,QAAI,MAAM,KAAK;AACf,QAAI,CAAC,IAAK,SAAS,GAAG;AAAG,aAAO;AAEhC,QAAM,YAAY,GAAG,OAAA,KAAG,oCAAA;AACxB,eAAW,WAAW,uBAAuB,EAC1C,KAAK,WAAA;AAAM,aAAA,MAAK,OAAM;IAAX,CAAa,EACxB,MAAM,WAAA;AAAM,YAAK,QAAQ,EAAE;IAAC,CAAC;;EAElC,WAAS,WAAA;;AACP,QAAM,KAAK,KAAK,MAAM;AAEtB,SAAI,KAAA,WAAA,QAAA,WAAM,SAAA,SAAN,OAAQ,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,UAAU,EAAE,GAAG;AACpC,aAAO,UAAU,UAAU,EAAE,EAAE,cAAa;AAC5C,aAAO,UAAU,UAAU,EAAE,IAAI;;;EAGrC,OAAO;IACL,QAAQ;MACN,SAAS,SAAU,QAAQ,QAAM;AAC/B,aAAK,cAAa;;MAEpB,MAAM;IACP;IACD,mBAAmB,SAAS,QAAQ,QAAM;AAAI,WAAK,cAAa;IAAA;IAChE,gBAAgB,SAAS,QAAQ,QAAM;AAAI,WAAK,cAAa;IAAC;IAC9D,cAAc,SAAS,QAAQ,QAAM;AAAI,WAAK,cAAa;IAAC;IAC5D,mBAAmB,SAAS,QAAQ,QAAM;AAAI,WAAK,cAAa;IAAA;IAChE,QAAQ,SAAS,QAAQ,QAAM;AAAI,WAAK,cAAa;IAAC;IACtD,MAAM,SAAS,QAAQ,QAAM;AAAI,WAAK,cAAa;IAAC;IACpD,OAAO,SAAS,QAAQ,QAAM;AAAI,WAAK,cAAa;IAAG;EACxD;EACD,SAAS;IACP,QAAK,WAAA;;AACF,UAAI;AACH,YAAM,KAAK,KAAK,MAAM;AAEtB,YAAI,CAAC,OAAO;AAAS,eAAK,QAAQ,EAAE;AACpC,aAAI,KAAA,WAAA,QAAA,WAAM,SAAA,SAAN,OAAQ,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,UAAU,EAAE,GAAG;AACpC,kBAAQ,IAAI,yCAAyC,EAAE;AACvD;;AAGF,YAAI,GAAC,KAAA,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,YAAW;AACjC,iBAAO,YAAY,EAAE,WAAW,CAAA,EAAA;;AAGlC,YAAI,aAAa,OAAO,OAAO;UAC7B,UAAU;YACR,UAAU,KAAK;YACf,OAAO,KAAK;UACb;UACD,cAAc,KAAK;UACnB,cAAc;YACZ,MAAM,KAAK;UACZ;UACD,QAAQ;YACN,YAAY,KAAK;YACjB,uBAAuB,KAAK;YAC5B,cAAc,KAAK;YACnB,iBAAiB,KAAK;YACtB,QAAQ,KAAK;YACb,WAAW,KAAK;YAChB,SAAS,KAAK;YACd,0BAA0B,KAAK;YAC/B,iBAAiB,KAAK;YACtB,kBAAkB,KAAK;YACvB,sBAAsB,KAAK;YAC3B,iBAAiB,KAAK;YACtB,8BAA8B,KAAK;YACnC,sBAAsB,KAAK;YAC3B,qBAAqB,KAAK;YAC1B,kBAAkB,KAAK;YACvB,uBAAuB,KAAK;YAC5B,sBAAsB,KAAK;YAC3B,kBAAkB,KAAK;YACvB,4BAA4B,KAAK;YACjC,yBAAyB,KAAK;UAC/B;UACD,QAAQ,KAAK;UACb,MAAM,KAAK;UACX,OAAO,KAAK;QACb,GAAE,KAAK,UAAU,CAAA,CAAE;AAEpB,YAAM,SAAS,OAAO,QAAQ,UAAU,IAAI,UAAU;AACtD,eAAO,UAAU,UAAU,EAAE,IAAI;eAC1B,KAAU;AACjB,gBAAQ,MAAM,GAAG;AACjB,aAAK,QAAQ,EAAE;;;IAGnB,SAAO,SAAC,WAAiB;AACvB,UAAI;AAEJ,cAAO,WAAS;QACd,KAAK;AACH,oBAAU,6BAA6B,KAAK;AAC5C;QACF,KAAK;AACH,oBAAU;AACV;QACF;AACE,oBAAU;AACV,sBAAY;;AAGhB,UAAI,OAAO,KAAK,wBAAwB,aAAa;AACnD,gBAAQ,MAAM,OAAO;aAChB;AACL,aAAK,qBAAqB,WAAW,OAAO;;;IAGhD,YAAU,WAAA;AACR,UAAM,KAAK,KAAK,MAAM;AACtB,WAAK,kBAAmB,OAAO,UAAU,UAAU,EAAE,CAAC;;IAExD,eAAY,WAAA;;AACV,UAAM,KAAK,KAAK,MAAM;AAEtB,WAAI,KAAA,WAAA,QAAA,WAAM,SAAA,SAAN,OAAQ,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,UAAU,EAAE,GAAG;AACpC,eAAO,UAAU,UAAU,EAAE,EAAE,cAAa;AAC5C,eAAO,UAAU,UAAU,EAAE,IAAI;AAEjC,gBAAQ,IAAI,qDAAqD;AACjE,aAAK,OAAM;;;EAGjB;AACD,CAAA;;;;;;;", "names": ["script"]}