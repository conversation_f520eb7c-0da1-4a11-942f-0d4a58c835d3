import {
  hasCompensateEventDefinition,
  hasErrorEventDefinition,
  hasEscalationEventDefinition,
  hasEventDefinition,
  isEventSubProcess,
  isExpanded,
  isInterrupting
} from "./chunk-67BHQHBR.js";
import "./chunk-VRJCCLJE.js";
import "./chunk-7XBT5GDQ.js";
import "./chunk-GFT2G5UO.js";
export {
  hasCompensateEventDefinition,
  hasErrorEventDefinition,
  hasEscalationEventDefinition,
  hasEventDefinition,
  isEventSubProcess,
  isExpanded,
  isInterrupting
};
//# sourceMappingURL=bpmn-js_lib_util_DiUtil.js.map
