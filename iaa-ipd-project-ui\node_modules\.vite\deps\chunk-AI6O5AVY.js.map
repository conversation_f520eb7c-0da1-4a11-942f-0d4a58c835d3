{"version": 3, "sources": ["../../amis/esm/renderers/HBox.js"], "sourcesContent": ["/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __assign, __decorate } from 'tslib';\nimport React from 'react';\nimport { isVisible, uc<PERSON><PERSON><PERSON>, Renderer } from 'amis-core';\n\nvar HBox = /** @class */ (function (_super) {\n    __extends(HBox, _super);\n    function HBox() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    HBox.prototype.renderChild = function (region, node, props) {\n        if (props === void 0) { props = {}; }\n        var render = this.props.render;\n        return render(region, node, props);\n    };\n    HBox.prototype.renderColumn = function (column, key, length) {\n        var _a;\n        var _b = this.props, itemRender = _b.itemRender, data = _b.data, cx = _b.classnames, subFormMode = _b.subFormMode, subFormHorizontal = _b.subFormHorizontal, formMode = _b.formMode, formHorizontal = _b.formHorizontal;\n        if (!isVisible(column, data) || !column) {\n            return null;\n        }\n        var style = __assign({ width: column.width, height: column.height }, column.style);\n        return (React.createElement(\"div\", { key: key, className: cx(\"Hbox-col\", style.width === 'auto'\n                ? 'Hbox-col--auto'\n                : style.width\n                    ? 'Hbox-col--customWidth'\n                    : '', (_a = {},\n                _a[\"Hbox-col--v\".concat(ucFirst(column.valign))] = column.valign,\n                _a), column.columnClassName), style: style }, itemRender\n            ? itemRender(column, key, length, this.props)\n            : this.renderChild(\"column/\".concat(key), column.body, {\n                formMode: column.mode || subFormMode || formMode,\n                formHorizontal: column.horizontal || subFormHorizontal || formHorizontal\n            })));\n    };\n    HBox.prototype.renderColumns = function () {\n        var _this = this;\n        var columns = this.props.columns;\n        return columns.map(function (column, key) {\n            return _this.renderColumn(column, key, columns.length);\n        });\n    };\n    HBox.prototype.render = function () {\n        var _a;\n        var _b = this.props, className = _b.className, style = _b.style, cx = _b.classnames, gap = _b.gap, vAlign = _b.valign, hAlign = _b.align;\n        return (React.createElement(\"div\", { className: cx(\"Hbox\", className, (_a = {},\n                _a[\"Hbox--\".concat(gap)] = gap,\n                _a[\"Hbox--v\".concat(ucFirst(vAlign))] = vAlign,\n                _a[\"Hbox--h\".concat(ucFirst(hAlign))] = hAlign,\n                _a)), style: style }, this.renderColumns()));\n    };\n    HBox.propsList = ['columns'];\n    HBox.defaultProps = {\n        gap: 'xs'\n    };\n    return HBox;\n}(React.Component));\nvar HBoxRenderer = /** @class */ (function (_super) {\n    __extends(HBoxRenderer, _super);\n    function HBoxRenderer() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    HBoxRenderer = __decorate([\n        Renderer({\n            type: 'hbox'\n        })\n    ], HBoxRenderer);\n    return HBoxRenderer;\n}(HBox));\n\nexport { HBoxRenderer, HBox as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAOA,mBAAkB;AAGlB,IAAI;AAAA;AAAA,EAAsB,SAAU,QAAQ;AACxC,cAAUA,OAAM,MAAM;AACtB,aAASA,QAAO;AACZ,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,MAAK,UAAU,cAAc,SAAU,QAAQ,MAAM,OAAO;AACxD,UAAI,UAAU,QAAQ;AAAE,gBAAQ,CAAC;AAAA,MAAG;AACpC,UAAI,SAAS,KAAK,MAAM;AACxB,aAAO,OAAO,QAAQ,MAAM,KAAK;AAAA,IACrC;AACA,IAAAA,MAAK,UAAU,eAAe,SAAU,QAAQ,KAAK,QAAQ;AACzD,UAAI;AACJ,UAAI,KAAK,KAAK,OAAO,aAAa,GAAG,YAAY,OAAO,GAAG,MAAM,KAAK,GAAG,YAAY,cAAc,GAAG,aAAa,oBAAoB,GAAG,mBAAmB,WAAW,GAAG,UAAU,iBAAiB,GAAG;AACzM,UAAI,CAAC,UAAU,QAAQ,IAAI,KAAK,CAAC,QAAQ;AACrC,eAAO;AAAA,MACX;AACA,UAAI,QAAQ,SAAS,EAAE,OAAO,OAAO,OAAO,QAAQ,OAAO,OAAO,GAAG,OAAO,KAAK;AACjF,aAAQ,aAAAC,QAAM,cAAc,OAAO,EAAE,KAAU,WAAW,GAAG,YAAY,MAAM,UAAU,SAC/E,mBACA,MAAM,QACF,0BACA,KAAK,KAAK,CAAC,GACjB,GAAG,cAAc,OAAO,QAAQ,OAAO,MAAM,CAAC,CAAC,IAAI,OAAO,QAC1D,KAAK,OAAO,eAAe,GAAG,MAAa,GAAG,aAChD,WAAW,QAAQ,KAAK,QAAQ,KAAK,KAAK,IAC1C,KAAK,YAAY,UAAU,OAAO,GAAG,GAAG,OAAO,MAAM;AAAA,QACnD,UAAU,OAAO,QAAQ,eAAe;AAAA,QACxC,gBAAgB,OAAO,cAAc,qBAAqB;AAAA,MAC9D,CAAC,CAAC;AAAA,IACV;AACA,IAAAD,MAAK,UAAU,gBAAgB,WAAY;AACvC,UAAI,QAAQ;AACZ,UAAI,UAAU,KAAK,MAAM;AACzB,aAAO,QAAQ,IAAI,SAAU,QAAQ,KAAK;AACtC,eAAO,MAAM,aAAa,QAAQ,KAAK,QAAQ,MAAM;AAAA,MACzD,CAAC;AAAA,IACL;AACA,IAAAA,MAAK,UAAU,SAAS,WAAY;AAChC,UAAI;AACJ,UAAI,KAAK,KAAK,OAAO,YAAY,GAAG,WAAW,QAAQ,GAAG,OAAO,KAAK,GAAG,YAAY,MAAM,GAAG,KAAK,SAAS,GAAG,QAAQ,SAAS,GAAG;AACnI,aAAQ,aAAAC,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,QAAQ,YAAY,KAAK,CAAC,GACrE,GAAG,SAAS,OAAO,GAAG,CAAC,IAAI,KAC3B,GAAG,UAAU,OAAO,QAAQ,MAAM,CAAC,CAAC,IAAI,QACxC,GAAG,UAAU,OAAO,QAAQ,MAAM,CAAC,CAAC,IAAI,QACxC,GAAG,GAAG,MAAa,GAAG,KAAK,cAAc,CAAC;AAAA,IACtD;AACA,IAAAD,MAAK,YAAY,CAAC,SAAS;AAC3B,IAAAA,MAAK,eAAe;AAAA,MAChB,KAAK;AAAA,IACT;AACA,WAAOA;AAAA,EACX,EAAE,aAAAC,QAAM,SAAS;AAAA;AACjB,IAAI;AAAA;AAAA,EAA8B,SAAU,QAAQ;AAChD,cAAUC,eAAc,MAAM;AAC9B,aAASA,gBAAe;AACpB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,gBAAe,WAAW;AAAA,MACtB,SAAS;AAAA,QACL,MAAM;AAAA,MACV,CAAC;AAAA,IACL,GAAGA,aAAY;AACf,WAAOA;AAAA,EACX,EAAE,IAAI;AAAA;", "names": ["HBox", "React", "HBox<PERSON><PERSON><PERSON>"]}