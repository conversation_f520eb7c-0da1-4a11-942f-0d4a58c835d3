{"version": 3, "sources": ["../../mobx/src/errors.ts", "../../mobx/src/utils/global.ts", "../../mobx/src/utils/utils.ts", "../../mobx/src/api/decorators.ts", "../../mobx/src/core/atom.ts", "../../mobx/src/utils/comparer.ts", "../../mobx/src/types/modifiers.ts", "../../mobx/src/types/overrideannotation.ts", "../../mobx/src/types/actionannotation.ts", "../../mobx/src/types/flowannotation.ts", "../../mobx/src/types/computedannotation.ts", "../../mobx/src/types/observableannotation.ts", "../../mobx/src/types/autoannotation.ts", "../../mobx/src/api/observable.ts", "../../mobx/src/api/computed.ts", "../../mobx/src/core/action.ts", "../../mobx/src/types/observablevalue.ts", "../../mobx/src/core/computedvalue.ts", "../../mobx/src/core/derivation.ts", "../../mobx/src/core/globalstate.ts", "../../mobx/src/core/observable.ts", "../../mobx/src/core/reaction.ts", "../../mobx/src/core/spy.ts", "../../mobx/src/api/action.ts", "../../mobx/src/api/autorun.ts", "../../mobx/src/api/become-observed.ts", "../../mobx/src/api/configure.ts", "../../mobx/src/api/extendobservable.ts", "../../mobx/src/api/extras.ts", "../../mobx/src/api/flow.ts", "../../mobx/src/api/intercept-read.ts", "../../mobx/src/api/intercept.ts", "../../mobx/src/api/iscomputed.ts", "../../mobx/src/api/isobservable.ts", "../../mobx/src/api/object-api.ts", "../../mobx/src/api/observe.ts", "../../mobx/src/api/tojs.ts", "../../mobx/src/api/trace.ts", "../../mobx/src/api/transaction.ts", "../../mobx/src/api/when.ts", "../../mobx/src/types/dynamicobject.ts", "../../mobx/src/types/intercept-utils.ts", "../../mobx/src/types/listen-utils.ts", "../../mobx/src/api/makeObservable.ts", "../../mobx/src/types/observablearray.ts", "../../mobx/src/types/observablemap.ts", "../../mobx/src/types/observableset.ts", "../../mobx/src/types/observableobject.ts", "../../mobx/src/types/legacyobservablearray.ts", "../../mobx/src/types/type-utils.ts", "../../mobx/src/utils/eq.ts", "../../mobx/src/utils/iterable.ts", "../../mobx/src/api/annotation.ts", "../../mobx/src/mobx.ts"], "sourcesContent": ["const niceErrors = {\n    0: `Invalid value for configuration 'enforceActions', expected 'never', 'always' or 'observed'`,\n    1(annotationType, key: PropertyKey) {\n        return `Cannot apply '${annotationType}' to '${key.toString()}': Field not found.`\n    },\n    /*\n    2(prop) {\n        return `invalid decorator for '${prop.toString()}'`\n    },\n    3(prop) {\n        return `Cannot decorate '${prop.toString()}': action can only be used on properties with a function value.`\n    },\n    4(prop) {\n        return `Cannot decorate '${prop.toString()}': computed can only be used on getter properties.`\n    },\n    */\n    5: \"'keys()' can only be used on observable objects, arrays, sets and maps\",\n    6: \"'values()' can only be used on observable objects, arrays, sets and maps\",\n    7: \"'entries()' can only be used on observable objects, arrays and maps\",\n    8: \"'set()' can only be used on observable objects, arrays and maps\",\n    9: \"'remove()' can only be used on observable objects, arrays and maps\",\n    10: \"'has()' can only be used on observable objects, arrays and maps\",\n    11: \"'get()' can only be used on observable objects, arrays and maps\",\n    12: `Invalid annotation`,\n    13: `Dynamic observable objects cannot be frozen`,\n    14: \"Intercept handlers should return nothing or a change object\",\n    15: `Observable arrays cannot be frozen`,\n    16: `Modification exception: the internal structure of an observable array was changed.`,\n    17(index, length) {\n        return `[mobx.array] Index out of bounds, ${index} is larger than ${length}`\n    },\n    18: \"mobx.map requires Map polyfill for the current browser. Check babel-polyfill or core-js/es6/map.js\",\n    19(other) {\n        return \"Cannot initialize from classes that inherit from Map: \" + other.constructor.name\n    },\n    20(other) {\n        return \"Cannot initialize map from \" + other\n    },\n    21(dataStructure) {\n        return `Cannot convert to map from '${dataStructure}'`\n    },\n    22: \"mobx.set requires Set polyfill for the current browser. Check babel-polyfill or core-js/es6/set.js\",\n    23: \"It is not possible to get index atoms from arrays\",\n    24(thing) {\n        return \"Cannot obtain administration from \" + thing\n    },\n    25(property, name) {\n        return `the entry '${property}' does not exist in the observable map '${name}'`\n    },\n    26: \"please specify a property\",\n    27(property, name) {\n        return `no observable property '${property.toString()}' found on the observable object '${name}'`\n    },\n    28(thing) {\n        return \"Cannot obtain atom from \" + thing\n    },\n    29: \"Expecting some object\",\n    30: \"invalid action stack. did you forget to finish an action?\",\n    31: \"missing option for computed: get\",\n    32(name, derivation) {\n        return `Cycle detected in computation ${name}: ${derivation}`\n    },\n    33(name) {\n        return `The setter of computed value '${name}' is trying to update itself. Did you intend to update an _observable_ value, instead of the computed property?`\n    },\n    34(name) {\n        return `[ComputedValue '${name}'] It is not possible to assign a new value to a computed value.`\n    },\n    35: \"There are multiple, different versions of MobX active. Make sure MobX is loaded only once or use `configure({ isolateGlobalState: true })`\",\n    36: \"isolateGlobalState should be called before MobX is running any reactions\",\n    37(method) {\n        return `[mobx] \\`observableArray.${method}()\\` mutates the array in-place, which is not allowed inside a derivation. Use \\`array.slice().${method}()\\` instead`\n    },\n    38: \"'ownKeys()' can only be used on observable objects\",\n    39: \"'defineProperty()' can only be used on observable objects\"\n} as const\n\nconst errors: typeof niceErrors = __DEV__ ? niceErrors : ({} as any)\n\nexport function die(error: string | keyof typeof errors, ...args: any[]): never {\n    if (__DEV__) {\n        let e: any = typeof error === \"string\" ? error : errors[error]\n        if (typeof e === \"function\") e = e.apply(null, args as any)\n        throw new Error(`[MobX] ${e}`)\n    }\n    throw new Error(\n        typeof error === \"number\"\n            ? `[MobX] minified error nr: ${error}${\n                  args.length ? \" \" + args.map(String).join(\",\") : \"\"\n              }. Find the full error at: https://github.com/mobxjs/mobx/blob/main/packages/mobx/src/errors.ts`\n            : `[MobX] ${error}`\n    )\n}\n", "declare const window: any\ndeclare const self: any\n\nconst mockGlobal = {}\n\nexport function getGlobal() {\n    if (typeof globalThis !== \"undefined\") {\n        return globalThis\n    }\n    if (typeof window !== \"undefined\") {\n        return window\n    }\n    if (typeof global !== \"undefined\") {\n        return global\n    }\n    if (typeof self !== \"undefined\") {\n        return self\n    }\n    return mockGlobal\n}\n", "import { globalState, die } from \"../internal\"\n\n// We shorten anything used > 5 times\nexport const assign = Object.assign\nexport const getDescriptor = Object.getOwnPropertyDescriptor\nexport const defineProperty = Object.defineProperty\nexport const objectPrototype = Object.prototype\n\nexport const EMPTY_ARRAY = []\nObject.freeze(EMPTY_ARRAY)\n\nexport const EMPTY_OBJECT = {}\nObject.freeze(EMPTY_OBJECT)\n\nexport interface Lambda {\n    (): void\n    name?: string\n}\n\nconst hasProxy = typeof Proxy !== \"undefined\"\nconst plainObjectString = Object.toString()\n\nexport function assertProxies() {\n    if (!hasProxy) {\n        die(\n            __DEV__\n                ? \"`Proxy` objects are not available in the current environment. Please configure MobX to enable a fallback implementation.`\"\n                : \"Proxy not available\"\n        )\n    }\n}\n\nexport function warnAboutProxyRequirement(msg: string) {\n    if (__DEV__ && globalState.verifyProxies) {\n        die(\n            \"MobX is currently configured to be able to run in ES5 mode, but in ES5 MobX won't be able to \" +\n                msg\n        )\n    }\n}\n\nexport function getNextId() {\n    return ++globalState.mobxGuid\n}\n\n/**\n * Makes sure that the provided function is invoked at most once.\n */\nexport function once(func: Lambda): Lambda {\n    let invoked = false\n    return function () {\n        if (invoked) return\n        invoked = true\n        return (func as any).apply(this, arguments)\n    }\n}\n\nexport const noop = () => {}\n\nexport function isFunction(fn: any): fn is Function {\n    return typeof fn === \"function\"\n}\n\nexport function isString(value: any): value is string {\n    return typeof value === \"string\"\n}\n\nexport function isStringish(value: any): value is string | number | symbol {\n    const t = typeof value\n    switch (t) {\n        case \"string\":\n        case \"symbol\":\n        case \"number\":\n            return true\n    }\n    return false\n}\n\nexport function isObject(value: any): value is Object {\n    return value !== null && typeof value === \"object\"\n}\n\nexport function isPlainObject(value) {\n    if (!isObject(value)) return false\n    const proto = Object.getPrototypeOf(value)\n    if (proto == null) return true\n    return proto.constructor?.toString() === plainObjectString\n}\n\n// https://stackoverflow.com/a/37865170\nexport function isGenerator(obj: any): boolean {\n    const constructor = obj?.constructor\n    if (!constructor) return false\n    if (\"GeneratorFunction\" === constructor.name || \"GeneratorFunction\" === constructor.displayName)\n        return true\n    return false\n}\n\nexport function addHiddenProp(object: any, propName: PropertyKey, value: any) {\n    defineProperty(object, propName, {\n        enumerable: false,\n        writable: true,\n        configurable: true,\n        value\n    })\n}\n\nexport function addHiddenFinalProp(object: any, propName: PropertyKey, value: any) {\n    defineProperty(object, propName, {\n        enumerable: false,\n        writable: false,\n        configurable: true,\n        value\n    })\n}\n\nexport function createInstanceofPredicate<T>(\n    name: string,\n    theClass: new (...args: any[]) => T\n): (x: any) => x is T {\n    const propName = \"isMobX\" + name\n    theClass.prototype[propName] = true\n    return function (x) {\n        return isObject(x) && x[propName] === true\n    } as any\n}\n\nexport function isES6Map(thing): boolean {\n    return thing instanceof Map\n}\n\nexport function isES6Set(thing): thing is Set<any> {\n    return thing instanceof Set\n}\n\nconst hasGetOwnPropertySymbols = typeof Object.getOwnPropertySymbols !== \"undefined\"\n\n/**\n * Returns the following: own enumerable keys and symbols.\n */\nexport function getPlainObjectKeys(object) {\n    const keys = Object.keys(object)\n    // Not supported in IE, so there are not going to be symbol props anyway...\n    if (!hasGetOwnPropertySymbols) return keys\n    const symbols = Object.getOwnPropertySymbols(object)\n    if (!symbols.length) return keys\n    return [...keys, ...symbols.filter(s => objectPrototype.propertyIsEnumerable.call(object, s))]\n}\n\n// From Immer utils\n// Returns all own keys, including non-enumerable and symbolic\nexport const ownKeys: (target: any) => PropertyKey[] =\n    typeof Reflect !== \"undefined\" && Reflect.ownKeys\n        ? Reflect.ownKeys\n        : hasGetOwnPropertySymbols\n        ? obj => Object.getOwnPropertyNames(obj).concat(Object.getOwnPropertySymbols(obj) as any)\n        : /* istanbul ignore next */ Object.getOwnPropertyNames\n\nexport function stringifyKey(key: any): string {\n    if (typeof key === \"string\") return key\n    if (typeof key === \"symbol\") return key.toString()\n    return new String(key).toString()\n}\n\nexport function toPrimitive(value) {\n    return value === null ? null : typeof value === \"object\" ? \"\" + value : value\n}\n\nexport function hasProp(target: Object, prop: PropertyKey): boolean {\n    return objectPrototype.hasOwnProperty.call(target, prop)\n}\n\n// From Immer utils\nexport const getOwnPropertyDescriptors =\n    Object.getOwnPropertyDescriptors ||\n    function getOwnPropertyDescriptors(target: any) {\n        // Polyfill needed for Hermes and IE, see https://github.com/facebook/hermes/issues/274\n        const res: any = {}\n        // Note: without polyfill for ownKeys, symbols won't be picked up\n        ownKeys(target).forEach(key => {\n            res[key] = getDescriptor(target, key)\n        })\n        return res\n    }\n", "import { Annotation, addHiddenProp, AnnotationsMap, hasProp, die, isOverride } from \"../internal\"\n\nexport const storedAnnotationsSymbol = Symbol(\"mobx-stored-annotations\")\n\n/**\n * Creates a function that acts as\n * - decorator\n * - annotation object\n */\nexport function createDecoratorAnnotation(annotation: Annotation): PropertyDecorator & Annotation {\n    function decorator(target, property) {\n        storeAnnotation(target, property, annotation)\n    }\n    return Object.assign(decorator, annotation)\n}\n\n/**\n * Stores annotation to prototype,\n * so it can be inspected later by `makeObservable` called from constructor\n */\nexport function storeAnnotation(prototype: any, key: PropertyKey, annotation: Annotation) {\n    if (!hasProp(prototype, storedAnnotationsSymbol)) {\n        addHiddenProp(prototype, storedAnnotationsSymbol, {\n            // Inherit annotations\n            ...prototype[storedAnnotationsSymbol]\n        })\n    }\n    // @override must override something\n    if (__DEV__ && isOverride(annotation) && !hasProp(prototype[storedAnnotationsSymbol], key)) {\n        const fieldName = `${prototype.constructor.name}.prototype.${key.toString()}`\n        die(\n            `'${fieldName}' is decorated with 'override', ` +\n                `but no such decorated member was found on prototype.`\n        )\n    }\n    // Cannot re-decorate\n    assertNotDecorated(prototype, annotation, key)\n\n    // Ignore override\n    if (!isOverride(annotation)) {\n        prototype[storedAnnotationsSymbol][key] = annotation\n    }\n}\n\nfunction assertNotDecorated(prototype: object, annotation: Annotation, key: PropertyKey) {\n    if (__DEV__ && !isOverride(annotation) && hasProp(prototype[storedAnnotationsSymbol], key)) {\n        const fieldName = `${prototype.constructor.name}.prototype.${key.toString()}`\n        const currentAnnotationType = prototype[storedAnnotationsSymbol][key].annotationType_\n        const requestedAnnotationType = annotation.annotationType_\n        die(\n            `Cannot apply '@${requestedAnnotationType}' to '${fieldName}':` +\n                `\\nThe field is already decorated with '@${currentAnnotationType}'.` +\n                `\\nRe-decorating fields is not allowed.` +\n                `\\nUse '@override' decorator for methods overriden by subclass.`\n        )\n    }\n}\n\n/**\n * Collects annotations from prototypes and stores them on target (instance)\n */\nexport function collectStoredAnnotations(target): AnnotationsMap<any, any> {\n    if (!hasProp(target, storedAnnotationsSymbol)) {\n        if (__DEV__ && !target[storedAnnotationsSymbol]) {\n            die(\n                `No annotations were passed to makeObservable, but no decorated members have been found either`\n            )\n        }\n        // We need a copy as we will remove annotation from the list once it's applied.\n        addHiddenProp(target, storedAnnotationsSymbol, { ...target[storedAnnotationsSymbol] })\n    }\n    return target[storedAnnotationsSymbol]\n}\n", "import {\n    IDerivationState_,\n    IObservable,\n    IDerivation,\n    createInstanceofPredicate,\n    endBatch,\n    getNextId,\n    noop,\n    onBecomeObserved,\n    onBecomeUnobserved,\n    propagateChanged,\n    reportObserved,\n    startBatch,\n    Lambda\n} from \"../internal\"\n\nexport const $mobx = Symbol(\"mobx administration\")\n\nexport interface IAtom extends IObservable {\n    reportObserved()\n    reportChanged()\n}\n\nexport class Atom implements IAtom {\n    isPendingUnobservation_ = false // for effective unobserving. BaseAtom has true, for extra optimization, so its onBecomeUnobserved never gets called, because it's not needed\n    isBeingObserved_ = false\n    observers_ = new Set<IDerivation>()\n\n    diffValue_ = 0\n    lastAccessedBy_ = 0\n    lowestObserverState_ = IDerivationState_.NOT_TRACKING_\n    /**\n     * Create a new atom. For debugging purposes it is recommended to give it a name.\n     * The onBecomeObserved and onBecomeUnobserved callbacks can be used for resource management.\n     */\n    constructor(public name_ = __DEV__ ? \"Atom@\" + getNextId() : \"Atom\") {}\n\n    // onBecomeObservedListeners\n    public onBOL: Set<Lambda> | undefined\n    // onBecomeUnobservedListeners\n    public onBUOL: Set<Lambda> | undefined\n\n    public onBO() {\n        if (this.onBOL) {\n            this.onBOL.forEach(listener => listener())\n        }\n    }\n\n    public onBUO() {\n        if (this.onBUOL) {\n            this.onBUOL.forEach(listener => listener())\n        }\n    }\n\n    /**\n     * Invoke this method to notify mobx that your atom has been used somehow.\n     * Returns true if there is currently a reactive context.\n     */\n    public reportObserved(): boolean {\n        return reportObserved(this)\n    }\n\n    /**\n     * Invoke this method _after_ this method has changed to signal mobx that all its observers should invalidate.\n     */\n    public reportChanged() {\n        startBatch()\n        propagateChanged(this)\n        endBatch()\n    }\n\n    toString() {\n        return this.name_\n    }\n}\n\nexport const isAtom = createInstanceofPredicate(\"Atom\", Atom)\n\nexport function createAtom(\n    name: string,\n    onBecomeObservedHandler: () => void = noop,\n    onBecomeUnobservedHandler: () => void = noop\n): IAtom {\n    const atom = new Atom(name)\n    // default `noop` listener will not initialize the hook Set\n    if (onBecomeObservedHandler !== noop) {\n        onBecomeObserved(atom, onBecomeObservedHandler)\n    }\n\n    if (onBecomeUnobservedHandler !== noop) {\n        onBecomeUnobserved(atom, onBecomeUnobservedHandler)\n    }\n    return atom\n}\n", "import { deepEqual } from \"../internal\"\n\nexport interface IEqualsComparer<T> {\n    (a: T, b: T): boolean\n}\n\nfunction identityComparer(a: any, b: any): boolean {\n    return a === b\n}\n\nfunction structuralComparer(a: any, b: any): boolean {\n    return deepEqual(a, b)\n}\n\nfunction shallowComparer(a: any, b: any): boolean {\n    return deepEqual(a, b, 1)\n}\n\nfunction defaultComparer(a: any, b: any): boolean {\n    return Object.is(a, b)\n}\n\nexport const comparer = {\n    identity: identityComparer,\n    structural: structuralComparer,\n    default: defaultComparer,\n    shallow: shallowComparer\n}\n", "import {\n    deepEqual,\n    isES6Map,\n    isES6Set,\n    isObservable,\n    isObservableArray,\n    isObservableMap,\n    isObservableSet,\n    isObservableObject,\n    isPlainObject,\n    observable,\n    die,\n    isAction,\n    autoAction,\n    flow,\n    isFlow,\n    isGenerator\n} from \"../internal\"\n\nexport interface IEnhancer<T> {\n    (newValue: T, oldValue: T | undefined, name: string): T\n}\n\nexport function deepEnhancer(v, _, name) {\n    // it is an observable already, done\n    if (isObservable(v)) return v\n\n    // something that can be converted and mutated?\n    if (Array.isArray(v)) return observable.array(v, { name })\n    if (isPlainObject(v)) return observable.object(v, undefined, { name })\n    if (isES6Map(v)) return observable.map(v, { name })\n    if (isES6Set(v)) return observable.set(v, { name })\n    if (typeof v === \"function\" && !isAction(v) && !isFlow(v)) {\n        if (isGenerator(v)) {\n            return flow(v)\n        } else {\n            return autoAction(name, v)\n        }\n    }\n    return v\n}\n\nexport function shallowEnhancer(v, _, name): any {\n    if (v === undefined || v === null) return v\n    if (isObservableObject(v) || isObservableArray(v) || isObservableMap(v) || isObservableSet(v))\n        return v\n    if (Array.isArray(v)) return observable.array(v, { name, deep: false })\n    if (isPlainObject(v)) return observable.object(v, undefined, { name, deep: false })\n    if (isES6Map(v)) return observable.map(v, { name, deep: false })\n    if (isES6Set(v)) return observable.set(v, { name, deep: false })\n\n    if (__DEV__)\n        die(\n            \"The shallow modifier / decorator can only used in combination with arrays, objects, maps and sets\"\n        )\n}\n\nexport function referenceEnhancer(newValue?) {\n    // never turn into an observable\n    return newValue\n}\n\nexport function refStructEnhancer(v, oldValue): any {\n    if (__DEV__ && isObservable(v))\n        die(`observable.struct should not be used with observable values`)\n    if (deepEqual(v, oldValue)) return oldValue\n    return v\n}\n", "import {\n    die,\n    Annotation,\n    hasProp,\n    createDecoratorAnnotation,\n    ObservableObjectAdministration,\n    MakeResult\n} from \"../internal\"\n\nconst OVERRIDE = \"override\"\n\nexport const override: Annotation & PropertyDecorator = createDecoratorAnnotation({\n    annotationType_: OVERRIDE,\n    make_,\n    extend_\n})\n\nexport function isOverride(annotation: Annotation): boolean {\n    return annotation.annotationType_ === OVERRIDE\n}\n\nfunction make_(adm: ObservableObjectAdministration, key): MakeResult {\n    // Must not be plain object\n    if (__DEV__ && adm.isPlainObject_) {\n        die(\n            `Cannot apply '${this.annotationType_}' to '${adm.name_}.${key.toString()}':` +\n                `\\n'${this.annotationType_}' cannot be used on plain objects.`\n        )\n    }\n    // Must override something\n    if (__DEV__ && !hasProp(adm.appliedAnnotations_!, key)) {\n        die(\n            `'${adm.name_}.${key.toString()}' is annotated with '${this.annotationType_}', ` +\n                `but no such annotated member was found on prototype.`\n        )\n    }\n    return MakeResult.Cancel\n}\n\nfunction extend_(adm, key, descriptor, proxyTrap): boolean {\n    die(`'${this.annotationType_}' can only be used with 'makeObservable'`)\n}\n", "import {\n    ObservableObjectAdministration,\n    createAction,\n    isAction,\n    defineProperty,\n    die,\n    isFunction,\n    Annotation,\n    globalState,\n    MakeResult\n} from \"../internal\"\n\nexport function createActionAnnotation(name: string, options?: object): Annotation {\n    return {\n        annotationType_: name,\n        options_: options,\n        make_,\n        extend_\n    }\n}\n\nfunction make_(\n    adm: ObservableObjectAdministration,\n    key: PropertyKey,\n    descriptor: PropertyDescriptor,\n    source: object\n): MakeResult {\n    // bound\n    if (this.options_?.bound) {\n        return this.extend_(adm, key, descriptor, false) === null\n            ? MakeResult.Cancel\n            : MakeResult.Break\n    }\n    // own\n    if (source === adm.target_) {\n        return this.extend_(adm, key, descriptor, false) === null\n            ? MakeResult.Cancel\n            : MakeResult.Continue\n    }\n    // prototype\n    if (isAction(descriptor.value)) {\n        // A prototype could have been annotated already by other constructor,\n        // rest of the proto chain must be annotated already\n        return MakeResult.Break\n    }\n    const actionDescriptor = createActionDescriptor(adm, this, key, descriptor, false)\n    defineProperty(source, key, actionDescriptor)\n    return MakeResult.Continue\n}\n\nfunction extend_(\n    adm: ObservableObjectAdministration,\n    key: PropertyKey,\n    descriptor: PropertyDescriptor,\n    proxyTrap: boolean\n): boolean | null {\n    const actionDescriptor = createActionDescriptor(adm, this, key, descriptor)\n    return adm.defineProperty_(key, actionDescriptor, proxyTrap)\n}\n\nfunction assertActionDescriptor(\n    adm: ObservableObjectAdministration,\n    { annotationType_ }: Annotation,\n    key: PropertyKey,\n    { value }: PropertyDescriptor\n) {\n    if (__DEV__ && !isFunction(value)) {\n        die(\n            `Cannot apply '${annotationType_}' to '${adm.name_}.${key.toString()}':` +\n                `\\n'${annotationType_}' can only be used on properties with a function value.`\n        )\n    }\n}\n\nexport function createActionDescriptor(\n    adm: ObservableObjectAdministration,\n    annotation: Annotation,\n    key: PropertyKey,\n    descriptor: PropertyDescriptor,\n    // provides ability to disable safeDescriptors for prototypes\n    safeDescriptors: boolean = globalState.safeDescriptors\n) {\n    assertActionDescriptor(adm, annotation, key, descriptor)\n    let { value } = descriptor\n    if (annotation.options_?.bound) {\n        value = value.bind(adm.proxy_ ?? adm.target_)\n    }\n    return {\n        value: createAction(\n            annotation.options_?.name ?? key.toString(),\n            value,\n            annotation.options_?.autoAction ?? false\n        ),\n        // Non-configurable for classes\n        // prevents accidental field redefinition in subclass\n        configurable: safeDescriptors ? adm.isPlainObject_ : true,\n        // https://github.com/mobxjs/mobx/pull/2641#issuecomment-737292058\n        enumerable: false,\n        // Non-obsevable, therefore non-writable\n        // Also prevents rewriting in subclass constructor\n        writable: safeDescriptors ? false : true\n    }\n}\n", "import {\n    ObservableObjectAdministration,\n    Annotation,\n    defineProperty,\n    die,\n    flow,\n    isFlow,\n    isFunction,\n    globalState,\n    MakeResult\n} from \"../internal\"\n\nexport function createFlowAnnotation(name: string, options?: object): Annotation {\n    return {\n        annotationType_: name,\n        options_: options,\n        make_,\n        extend_\n    }\n}\n\nfunction make_(\n    adm: ObservableObjectAdministration,\n    key: PropertyKey,\n    descriptor: PropertyDescriptor,\n    source: object\n): MakeResult {\n    // own\n    if (source === adm.target_) {\n        return this.extend_(adm, key, descriptor, false) === null\n            ? MakeResult.Cancel\n            : MakeResult.Continue\n    }\n    // prototype\n    // bound - must annotate protos to support super.flow()\n    if (this.options_?.bound && !isFlow(adm.target_[key])) {\n        if (this.extend_(adm, key, descriptor, false) === null) return MakeResult.Cancel\n    }\n    if (isFlow(descriptor.value)) {\n        // A prototype could have been annotated already by other constructor,\n        // rest of the proto chain must be annotated already\n        return MakeResult.Break\n    }\n    const flowDescriptor = createFlowDescriptor(adm, this, key, descriptor, false, false)\n    defineProperty(source, key, flowDescriptor)\n    return MakeResult.Continue\n}\n\nfunction extend_(\n    adm: ObservableObjectAdministration,\n    key: PropertyKey,\n    descriptor: PropertyDescriptor,\n    proxyTrap: boolean\n): boolean | null {\n    const flowDescriptor = createFlowDescriptor(adm, this, key, descriptor, this.options_?.bound)\n    return adm.defineProperty_(key, flowDescriptor, proxyTrap)\n}\n\nfunction assertFlowDescriptor(\n    adm: ObservableObjectAdministration,\n    { annotationType_ }: Annotation,\n    key: PropertyKey,\n    { value }: PropertyDescriptor\n) {\n    if (__DEV__ && !isFunction(value)) {\n        die(\n            `Cannot apply '${annotationType_}' to '${adm.name_}.${key.toString()}':` +\n                `\\n'${annotationType_}' can only be used on properties with a generator function value.`\n        )\n    }\n}\n\nfunction createFlowDescriptor(\n    adm: ObservableObjectAdministration,\n    annotation: Annotation,\n    key: PropertyKey,\n    descriptor: PropertyDescriptor,\n    bound: boolean,\n    // provides ability to disable safeDescriptors for prototypes\n    safeDescriptors: boolean = globalState.safeDescriptors\n): PropertyDescriptor {\n    assertFlowDescriptor(adm, annotation, key, descriptor)\n    let { value } = descriptor\n    if (bound) {\n        value = value.bind(adm.proxy_ ?? adm.target_)\n    }\n    return {\n        value: flow(value),\n        // Non-configurable for classes\n        // prevents accidental field redefinition in subclass\n        configurable: safeDescriptors ? adm.isPlainObject_ : true,\n        // https://github.com/mobxjs/mobx/pull/2641#issuecomment-737292058\n        enumerable: false,\n        // Non-obsevable, therefore non-writable\n        // Also prevents rewriting in subclass constructor\n        writable: safeDescriptors ? false : true\n    }\n}\n", "import { ObservableObjectAdministration, die, Annotation, MakeResult } from \"../internal\"\n\nexport function createComputedAnnotation(name: string, options?: object): Annotation {\n    return {\n        annotationType_: name,\n        options_: options,\n        make_,\n        extend_\n    }\n}\n\nfunction make_(\n    adm: ObservableObjectAdministration,\n    key: <PERSON><PERSON><PERSON>,\n    descriptor: PropertyDescriptor\n): MakeResult {\n    return this.extend_(adm, key, descriptor, false) === null ? MakeResult.Cancel : MakeResult.Break\n}\n\nfunction extend_(\n    adm: ObservableObjectAdministration,\n    key: Property<PERSON><PERSON>,\n    descriptor: PropertyDescriptor,\n    proxyTrap: boolean\n): boolean | null {\n    assertComputedDescriptor(adm, this, key, descriptor)\n    return adm.defineComputedProperty_(\n        key,\n        {\n            ...this.options_,\n            get: descriptor.get,\n            set: descriptor.set\n        },\n        proxyTrap\n    )\n}\n\nfunction assertComputedDescriptor(\n    adm: ObservableObjectAdministration,\n    { annotationType_ }: Annotation,\n    key: <PERSON><PERSON><PERSON>,\n    { get }: PropertyDescriptor\n) {\n    if (__DEV__ && !get) {\n        die(\n            `Cannot apply '${annotationType_}' to '${adm.name_}.${key.toString()}':` +\n                `\\n'${annotationType_}' can only be used on getter(+setter) properties.`\n        )\n    }\n}\n", "import {\n    ObservableObjectAdministration,\n    deepEnhancer,\n    die,\n    Annotation,\n    MakeResult\n} from \"../internal\"\n\nexport function createObservableAnnotation(name: string, options?: object): Annotation {\n    return {\n        annotationType_: name,\n        options_: options,\n        make_,\n        extend_\n    }\n}\n\nfunction make_(\n    adm: ObservableObjectAdministration,\n    key: <PERSON><PERSON><PERSON>,\n    descriptor: PropertyDescriptor\n): MakeResult {\n    return this.extend_(adm, key, descriptor, false) === null ? MakeResult.Cancel : MakeResult.Break\n}\n\nfunction extend_(\n    adm: ObservableObjectAdministration,\n    key: Property<PERSON><PERSON>,\n    descriptor: PropertyDescriptor,\n    proxyTrap: boolean\n): boolean | null {\n    assertObservableDescriptor(adm, this, key, descriptor)\n    return adm.defineObservableProperty_(\n        key,\n        descriptor.value,\n        this.options_?.enhancer ?? deepEnhancer,\n        proxyTrap\n    )\n}\n\nfunction assertObservableDescriptor(\n    adm: ObservableObjectAdministration,\n    { annotationType_ }: Annotation,\n    key: <PERSON><PERSON><PERSON>,\n    descriptor: PropertyDescriptor\n) {\n    if (__DEV__ && !(\"value\" in descriptor)) {\n        die(\n            `Cannot apply '${annotationType_}' to '${adm.name_}.${key.toString()}':` +\n                `\\n'${annotationType_}' cannot be used on getter/setter properties`\n        )\n    }\n}\n", "import {\n    ObservableObjectAdministration,\n    observable,\n    Annotation,\n    defineProperty,\n    createAction,\n    globalState,\n    flow,\n    computed,\n    autoAction,\n    isGenerator,\n    MakeResult\n} from \"../internal\"\n\nconst AUTO = \"true\"\n\nexport const autoAnnotation: Annotation = createAutoAnnotation()\n\nexport function createAutoAnnotation(options?: object): Annotation {\n    return {\n        annotationType_: AUTO,\n        options_: options,\n        make_,\n        extend_\n    }\n}\n\nfunction make_(\n    adm: ObservableObjectAdministration,\n    key: PropertyKey,\n    descriptor: PropertyDescriptor,\n    source: object\n): MakeResult {\n    // getter -> computed\n    if (descriptor.get) {\n        return computed.make_(adm, key, descriptor, source)\n    }\n    // lone setter -> action setter\n    if (descriptor.set) {\n        // TODO make action applicable to setter and delegate to action.make_\n        const set = createAction(key.toString(), descriptor.set) as (v: any) => void\n        // own\n        if (source === adm.target_) {\n            return adm.defineProperty_(key, {\n                configurable: globalState.safeDescriptors ? adm.isPlainObject_ : true,\n                set\n            }) === null\n                ? MakeResult.Cancel\n                : MakeResult.Continue\n        }\n        // proto\n        defineProperty(source, key, {\n            configurable: true,\n            set\n        })\n        return MakeResult.Continue\n    }\n    // function on proto -> autoAction/flow\n    if (source !== adm.target_ && typeof descriptor.value === \"function\") {\n        if (isGenerator(descriptor.value)) {\n            const flowAnnotation = this.options_?.autoBind ? flow.bound : flow\n            return flowAnnotation.make_(adm, key, descriptor, source)\n        }\n        const actionAnnotation = this.options_?.autoBind ? autoAction.bound : autoAction\n        return actionAnnotation.make_(adm, key, descriptor, source)\n    }\n    // other -> observable\n    // Copy props from proto as well, see test:\n    // \"decorate should work with Object.create\"\n    let observableAnnotation = this.options_?.deep === false ? observable.ref : observable\n    // if function respect autoBind option\n    if (typeof descriptor.value === \"function\" && this.options_?.autoBind) {\n        descriptor.value = descriptor.value.bind(adm.proxy_ ?? adm.target_)\n    }\n    return observableAnnotation.make_(adm, key, descriptor, source)\n}\n\nfunction extend_(\n    adm: ObservableObjectAdministration,\n    key: PropertyKey,\n    descriptor: PropertyDescriptor,\n    proxyTrap: boolean\n): boolean | null {\n    // getter -> computed\n    if (descriptor.get) {\n        return computed.extend_(adm, key, descriptor, proxyTrap)\n    }\n    // lone setter -> action setter\n    if (descriptor.set) {\n        // TODO make action applicable to setter and delegate to action.extend_\n        return adm.defineProperty_(\n            key,\n            {\n                configurable: globalState.safeDescriptors ? adm.isPlainObject_ : true,\n                set: createAction(key.toString(), descriptor.set) as (v: any) => void\n            },\n            proxyTrap\n        )\n    }\n    // other -> observable\n    // if function respect autoBind option\n    if (typeof descriptor.value === \"function\" && this.options_?.autoBind) {\n        descriptor.value = descriptor.value.bind(adm.proxy_ ?? adm.target_)\n    }\n    let observableAnnotation = this.options_?.deep === false ? observable.ref : observable\n    return observableAnnotation.extend_(adm, key, descriptor, proxyTrap)\n}\n", "import {\n    IEnhancer,\n    IEqualsComparer,\n    IObservableArray,\n    IObservableMapInitialValues,\n    IObservableSetInitialValues,\n    IObservableValue,\n    ObservableMap,\n    ObservableSet,\n    ObservableValue,\n    asDynamicObservableObject,\n    createObservableArray,\n    deepEnhancer,\n    extendObservable,\n    isES6Map,\n    isES6Set,\n    isObservable,\n    isPlainObject,\n    referenceEnhancer,\n    Annotation,\n    shallowEnhancer,\n    refStructEnhancer,\n    AnnotationsMap,\n    asObservableObject,\n    storeAnnotation,\n    createDecoratorAnnotation,\n    createLegacyArray,\n    globalState,\n    assign,\n    isStringish,\n    createObservableAnnotation,\n    createAutoAnnotation\n} from \"../internal\"\n\nexport const OBSERVABLE = \"observable\"\nexport const OBSERVABLE_REF = \"observable.ref\"\nexport const OBSERVABLE_SHALLOW = \"observable.shallow\"\nexport const OBSERVABLE_STRUCT = \"observable.struct\"\n\nexport type CreateObservableOptions = {\n    name?: string\n    equals?: IEqualsComparer<any>\n    deep?: boolean\n    defaultDecorator?: Annotation\n    proxy?: boolean\n    autoBind?: boolean\n}\n\n// Predefined bags of create observable options, to avoid allocating temporarily option objects\n// in the majority of cases\nexport const defaultCreateObservableOptions: CreateObservableOptions = {\n    deep: true,\n    name: undefined,\n    defaultDecorator: undefined,\n    proxy: true\n}\nObject.freeze(defaultCreateObservableOptions)\n\nexport function asCreateObservableOptions(thing: any): CreateObservableOptions {\n    return thing || defaultCreateObservableOptions\n}\n\nconst observableAnnotation = createObservableAnnotation(\"observable\")\nconst observableRefAnnotation = createObservableAnnotation(\"observable.ref\", {\n    enhancer: referenceEnhancer\n})\nconst observableShallowAnnotation = createObservableAnnotation(\"observable.shallow\", {\n    enhancer: shallowEnhancer\n})\nconst observableStructAnnotation = createObservableAnnotation(\"observable.struct\", {\n    enhancer: refStructEnhancer\n})\nconst observableDecoratorAnnotation = createDecoratorAnnotation(observableAnnotation)\n\nexport function getEnhancerFromOptions(options: CreateObservableOptions): IEnhancer<any> {\n    return options.deep === true\n        ? deepEnhancer\n        : options.deep === false\n        ? referenceEnhancer\n        : getEnhancerFromAnnotation(options.defaultDecorator)\n}\n\nexport function getAnnotationFromOptions(\n    options?: CreateObservableOptions\n): Annotation | undefined {\n    return options ? options.defaultDecorator ?? createAutoAnnotation(options) : undefined\n}\n\nexport function getEnhancerFromAnnotation(annotation?: Annotation): IEnhancer<any> {\n    return !annotation ? deepEnhancer : annotation.options_?.enhancer ?? deepEnhancer\n}\n\n/**\n * Turns an object, array or function into a reactive structure.\n * @param v the value which should become observable.\n */\nfunction createObservable(v: any, arg2?: any, arg3?: any) {\n    // @observable someProp;\n    if (isStringish(arg2)) {\n        storeAnnotation(v, arg2, observableAnnotation)\n        return\n    }\n\n    // already observable - ignore\n    if (isObservable(v)) return v\n\n    // plain object\n    if (isPlainObject(v)) return observable.object(v, arg2, arg3)\n\n    // Array\n    if (Array.isArray(v)) return observable.array(v, arg2)\n\n    // Map\n    if (isES6Map(v)) return observable.map(v, arg2)\n\n    // Set\n    if (isES6Set(v)) return observable.set(v, arg2)\n\n    // other object - ignore\n    if (typeof v === \"object\" && v !== null) return v\n\n    // anything else\n    return observable.box(v, arg2)\n}\nObject.assign(createObservable, observableDecoratorAnnotation)\n\nexport interface IObservableFactory extends Annotation, PropertyDecorator {\n    <T = any>(value: T[], options?: CreateObservableOptions): IObservableArray<T>\n    <T = any>(value: Set<T>, options?: CreateObservableOptions): ObservableSet<T>\n    <K = any, V = any>(value: Map<K, V>, options?: CreateObservableOptions): ObservableMap<K, V>\n    <T extends object>(\n        value: T,\n        decorators?: AnnotationsMap<T, never>,\n        options?: CreateObservableOptions\n    ): T\n\n    box: <T = any>(value?: T, options?: CreateObservableOptions) => IObservableValue<T>\n    array: <T = any>(initialValues?: T[], options?: CreateObservableOptions) => IObservableArray<T>\n    set: <T = any>(\n        initialValues?: IObservableSetInitialValues<T>,\n        options?: CreateObservableOptions\n    ) => ObservableSet<T>\n    map: <K = any, V = any>(\n        initialValues?: IObservableMapInitialValues<K, V>,\n        options?: CreateObservableOptions\n    ) => ObservableMap<K, V>\n    object: <T = any>(\n        props: T,\n        decorators?: AnnotationsMap<T, never>,\n        options?: CreateObservableOptions\n    ) => T\n\n    /**\n     * Decorator that creates an observable that only observes the references, but doesn't try to turn the assigned value into an observable.ts.\n     */\n    ref: Annotation & PropertyDecorator\n    /**\n     * Decorator that creates an observable converts its value (objects, maps or arrays) into a shallow observable structure\n     */\n    shallow: Annotation & PropertyDecorator\n    deep: Annotation & PropertyDecorator\n    struct: Annotation & PropertyDecorator\n}\n\nconst observableFactories: IObservableFactory = {\n    box<T = any>(value?: T, options?: CreateObservableOptions): IObservableValue<T> {\n        const o = asCreateObservableOptions(options)\n        return new ObservableValue(value, getEnhancerFromOptions(o), o.name, true, o.equals)\n    },\n    array<T = any>(initialValues?: T[], options?: CreateObservableOptions): IObservableArray<T> {\n        const o = asCreateObservableOptions(options)\n        return (globalState.useProxies === false || o.proxy === false\n            ? createLegacyArray\n            : createObservableArray)(initialValues, getEnhancerFromOptions(o), o.name)\n    },\n    map<K = any, V = any>(\n        initialValues?: IObservableMapInitialValues<K, V>,\n        options?: CreateObservableOptions\n    ): ObservableMap<K, V> {\n        const o = asCreateObservableOptions(options)\n        return new ObservableMap<K, V>(initialValues, getEnhancerFromOptions(o), o.name)\n    },\n    set<T = any>(\n        initialValues?: IObservableSetInitialValues<T>,\n        options?: CreateObservableOptions\n    ): ObservableSet<T> {\n        const o = asCreateObservableOptions(options)\n        return new ObservableSet<T>(initialValues, getEnhancerFromOptions(o), o.name)\n    },\n    object<T = any>(\n        props: T,\n        decorators?: AnnotationsMap<T, never>,\n        options?: CreateObservableOptions\n    ): T {\n        return extendObservable(\n            globalState.useProxies === false || options?.proxy === false\n                ? asObservableObject({}, options)\n                : asDynamicObservableObject({}, options),\n            props,\n            decorators\n        )\n    },\n    ref: createDecoratorAnnotation(observableRefAnnotation),\n    shallow: createDecoratorAnnotation(observableShallowAnnotation),\n    deep: observableDecoratorAnnotation,\n    struct: createDecoratorAnnotation(observableStructAnnotation)\n} as any\n\n// eslint-disable-next-line\nexport var observable: IObservableFactory = assign(createObservable, observableFactories)\n", "import {\n    ComputedValue,\n    IComputedValueOptions,\n    Annotation,\n    storeAnnotation,\n    createDecoratorAnnotation,\n    isStringish,\n    isPlainObject,\n    isFunction,\n    die,\n    IComputedValue,\n    createComputedAnnotation,\n    comparer\n} from \"../internal\"\n\nexport const COMPUTED = \"computed\"\nexport const COMPUTED_STRUCT = \"computed.struct\"\n\nexport interface IComputedFactory extends Annotation, PropertyDecorator {\n    // @computed(opts)\n    <T>(options: IComputedValueOptions<T>): Annotation & PropertyDecorator\n    // computed(fn, opts)\n    <T>(func: () => T, options?: IComputedValueOptions<T>): IComputedValue<T>\n\n    struct: Annotation & PropertyDecorator\n}\n\nconst computedAnnotation = createComputedAnnotation(COMPUTED)\nconst computedStructAnnotation = createComputedAnnotation(COMPUTED_STRUCT, {\n    equals: comparer.structural\n})\n\n/**\n * Decorator for class properties: @computed get value() { return expr; }.\n * For legacy purposes also invokable as ES5 observable created: `computed(() => expr)`;\n */\nexport const computed: IComputedFactory = function computed(arg1, arg2) {\n    if (isStringish(arg2)) {\n        // @computed\n        return storeAnnotation(arg1, arg2, computedAnnotation)\n    }\n    if (isPlainObject(arg1)) {\n        // @computed({ options })\n        return createDecoratorAnnotation(createComputedAnnotation(COMPUTED, arg1))\n    }\n\n    // computed(expr, options?)\n    if (__DEV__) {\n        if (!isFunction(arg1)) die(\"First argument to `computed` should be an expression.\")\n        if (isFunction(arg2))\n            die(\n                \"A setter as second argument is no longer supported, use `{ set: fn }` option instead\"\n            )\n    }\n    const opts: IComputedValueOptions<any> = isPlainObject(arg2) ? arg2 : {}\n    opts.get = arg1\n    opts.name ||= arg1.name || \"\" /* for generated name */\n\n    return new ComputedValue(opts)\n} as any\n\nObject.assign(computed, computedAnnotation)\n\ncomputed.struct = createDecoratorAnnotation(computedStructAnnotation)\n", "import {\n    IDerivation,\n    endBatch,\n    globalState,\n    isSpyEnabled,\n    spyReportEnd,\n    spyReportStart,\n    startBatch,\n    untrackedEnd,\n    untrackedStart,\n    isFunction,\n    allowStateReadsStart,\n    allowStateReadsEnd,\n    ACTION,\n    EMPTY_ARRAY,\n    die,\n    getDescriptor\n} from \"../internal\"\n\n// we don't use globalState for these in order to avoid possible issues with multiple\n// mobx versions\nlet currentActionId = 0\nlet nextActionId = 1\nconst isFunctionNameConfigurable = getDescriptor(() => {}, \"name\")?.configurable ?? false\n\n// we can safely recycle this object\nconst tmpNameDescriptor: PropertyDescriptor = {\n    value: \"action\",\n    configurable: true,\n    writable: false,\n    enumerable: false\n}\n\nexport function createAction(\n    actionName: string,\n    fn: Function,\n    autoAction: boolean = false,\n    ref?: Object\n): Function {\n    if (__DEV__) {\n        if (!isFunction(fn)) die(\"`action` can only be invoked on functions\")\n        if (typeof actionName !== \"string\" || !actionName)\n            die(`actions should have valid names, got: '${actionName}'`)\n    }\n    function res() {\n        return executeAction(actionName, autoAction, fn, ref || this, arguments)\n    }\n    res.isMobxAction = true\n    if (isFunctionNameConfigurable) {\n        tmpNameDescriptor.value = actionName\n        Object.defineProperty(res, \"name\", tmpNameDescriptor)\n    }\n    return res\n}\n\nexport function executeAction(\n    actionName: string,\n    canRunAsDerivation: boolean,\n    fn: Function,\n    scope?: any,\n    args?: IArguments\n) {\n    const runInfo = _startAction(actionName, canRunAsDerivation, scope, args)\n    try {\n        return fn.apply(scope, args)\n    } catch (err) {\n        runInfo.error_ = err\n        throw err\n    } finally {\n        _endAction(runInfo)\n    }\n}\n\nexport interface IActionRunInfo {\n    prevDerivation_: IDerivation | null\n    prevAllowStateChanges_: boolean\n    prevAllowStateReads_: boolean\n    notifySpy_: boolean\n    startTime_: number\n    error_?: any\n    parentActionId_: number\n    actionId_: number\n    runAsAction_?: boolean\n}\n\nexport function _startAction(\n    actionName: string,\n    canRunAsDerivation: boolean, // true for autoAction\n    scope: any,\n    args?: IArguments\n): IActionRunInfo {\n    const notifySpy_ = __DEV__ && isSpyEnabled() && !!actionName\n    let startTime_: number = 0\n    if (__DEV__ && notifySpy_) {\n        startTime_ = Date.now()\n        const flattenedArgs = args ? Array.from(args) : EMPTY_ARRAY\n        spyReportStart({\n            type: ACTION,\n            name: actionName,\n            object: scope,\n            arguments: flattenedArgs\n        })\n    }\n    const prevDerivation_ = globalState.trackingDerivation\n    const runAsAction = !canRunAsDerivation || !prevDerivation_\n    startBatch()\n    let prevAllowStateChanges_ = globalState.allowStateChanges // by default preserve previous allow\n    if (runAsAction) {\n        untrackedStart()\n        prevAllowStateChanges_ = allowStateChangesStart(true)\n    }\n    const prevAllowStateReads_ = allowStateReadsStart(true)\n    const runInfo = {\n        runAsAction_: runAsAction,\n        prevDerivation_,\n        prevAllowStateChanges_,\n        prevAllowStateReads_,\n        notifySpy_,\n        startTime_,\n        actionId_: nextActionId++,\n        parentActionId_: currentActionId\n    }\n    currentActionId = runInfo.actionId_\n    return runInfo\n}\n\nexport function _endAction(runInfo: IActionRunInfo) {\n    if (currentActionId !== runInfo.actionId_) {\n        die(30)\n    }\n    currentActionId = runInfo.parentActionId_\n\n    if (runInfo.error_ !== undefined) {\n        globalState.suppressReactionErrors = true\n    }\n    allowStateChangesEnd(runInfo.prevAllowStateChanges_)\n    allowStateReadsEnd(runInfo.prevAllowStateReads_)\n    endBatch()\n    if (runInfo.runAsAction_) untrackedEnd(runInfo.prevDerivation_)\n    if (__DEV__ && runInfo.notifySpy_) {\n        spyReportEnd({ time: Date.now() - runInfo.startTime_ })\n    }\n    globalState.suppressReactionErrors = false\n}\n\nexport function allowStateChanges<T>(allowStateChanges: boolean, func: () => T): T {\n    const prev = allowStateChangesStart(allowStateChanges)\n    try {\n        return func()\n    } finally {\n        allowStateChangesEnd(prev)\n    }\n}\n\nexport function allowStateChangesStart(allowStateChanges: boolean) {\n    const prev = globalState.allowStateChanges\n    globalState.allowStateChanges = allowStateChanges\n    return prev\n}\n\nexport function allowStateChangesEnd(prev: boolean) {\n    globalState.allowStateChanges = prev\n}\n", "import {\n    Atom,\n    IEnhancer,\n    IInterceptable,\n    IEqualsComparer,\n    IInterceptor,\n    IListenable,\n    Lambda,\n    checkIfStateModificationsAreAllowed,\n    comparer,\n    createInstanceofPredicate,\n    getNextId,\n    hasInterceptors,\n    hasListeners,\n    interceptChange,\n    isSpyEnabled,\n    notifyListeners,\n    registerInterceptor,\n    registerListener,\n    spyReport,\n    spyReportEnd,\n    spyReportStart,\n    toPrimitive,\n    globalState,\n    IUNCHANGED,\n    UPDATE\n} from \"../internal\"\n\nexport interface IValueWillChange<T> {\n    object: IObservableValue<T>\n    type: \"update\"\n    newValue: T\n}\n\nexport type IValueDidChange<T = any> = {\n    type: \"update\"\n    observableKind: \"value\"\n    object: IObservableValue<T>\n    debugObjectName: string\n    newValue: unknown\n    oldValue: unknown\n}\nexport type IBoxDidChange<T = any> =\n    | {\n          type: \"create\"\n          observableKind: \"value\"\n          object: IObservableValue<T>\n          debugObjectName: string\n          newValue: unknown\n      }\n    | IValueDidChange<T>\n\nexport interface IObservableValue<T> {\n    get(): T\n    set(value: T): void\n    intercept_(handler: IInterceptor<IValueWillChange<T>>): Lambda\n    observe_(listener: (change: IValueDidChange<T>) => void, fireImmediately?: boolean): Lambda\n}\n\nconst CREATE = \"create\"\n\nexport class ObservableValue<T>\n    extends Atom\n    implements IObservableValue<T>, IInterceptable<IValueWillChange<T>>, IListenable {\n    hasUnreportedChange_ = false\n    interceptors_\n    changeListeners_\n    value_\n    dehancer: any\n\n    constructor(\n        value: T,\n        public enhancer: IEnhancer<T>,\n        public name_ = __DEV__ ? \"ObservableValue@\" + getNextId() : \"ObservableValue\",\n        notifySpy = true,\n        private equals: IEqualsComparer<any> = comparer.default\n    ) {\n        super(name_)\n        this.value_ = enhancer(value, undefined, name_)\n        if (__DEV__ && notifySpy && isSpyEnabled()) {\n            // only notify spy if this is a stand-alone observable\n            spyReport({\n                type: CREATE,\n                object: this,\n                observableKind: \"value\",\n                debugObjectName: this.name_,\n                newValue: \"\" + this.value_\n            })\n        }\n    }\n\n    private dehanceValue(value: T): T {\n        if (this.dehancer !== undefined) return this.dehancer(value)\n        return value\n    }\n\n    public set(newValue: T) {\n        const oldValue = this.value_\n        newValue = this.prepareNewValue_(newValue) as any\n        if (newValue !== globalState.UNCHANGED) {\n            const notifySpy = isSpyEnabled()\n            if (__DEV__ && notifySpy) {\n                spyReportStart({\n                    type: UPDATE,\n                    object: this,\n                    observableKind: \"value\",\n                    debugObjectName: this.name_,\n                    newValue,\n                    oldValue\n                })\n            }\n            this.setNewValue_(newValue)\n            if (__DEV__ && notifySpy) spyReportEnd()\n        }\n    }\n\n    private prepareNewValue_(newValue): T | IUNCHANGED {\n        checkIfStateModificationsAreAllowed(this)\n        if (hasInterceptors(this)) {\n            const change = interceptChange<IValueWillChange<T>>(this, {\n                object: this,\n                type: UPDATE,\n                newValue\n            })\n            if (!change) return globalState.UNCHANGED\n            newValue = change.newValue\n        }\n        // apply modifier\n        newValue = this.enhancer(newValue, this.value_, this.name_)\n        return this.equals(this.value_, newValue) ? globalState.UNCHANGED : newValue\n    }\n\n    setNewValue_(newValue: T) {\n        const oldValue = this.value_\n        this.value_ = newValue\n        this.reportChanged()\n        if (hasListeners(this)) {\n            notifyListeners(this, {\n                type: UPDATE,\n                object: this,\n                newValue,\n                oldValue\n            })\n        }\n    }\n\n    public get(): T {\n        this.reportObserved()\n        return this.dehanceValue(this.value_)\n    }\n\n    intercept_(handler: IInterceptor<IValueWillChange<T>>): Lambda {\n        return registerInterceptor(this, handler)\n    }\n\n    observe_(listener: (change: IValueDidChange<T>) => void, fireImmediately?: boolean): Lambda {\n        if (fireImmediately)\n            listener({\n                observableKind: \"value\",\n                debugObjectName: this.name_,\n                object: this,\n                type: UPDATE,\n                newValue: this.value_,\n                oldValue: undefined\n            })\n        return registerListener(this, listener)\n    }\n\n    raw() {\n        // used by MST ot get undehanced value\n        return this.value_\n    }\n\n    toJSON() {\n        return this.get()\n    }\n\n    toString() {\n        return `${this.name_}[${this.value_}]`\n    }\n\n    valueOf(): T {\n        return toPrimitive(this.get())\n    }\n\n    [Symbol.toPrimitive]() {\n        return this.valueOf()\n    }\n}\n\nexport const isObservableValue = createInstanceofPredicate(\"ObservableValue\", ObservableValue) as (\n    x: any\n) => x is IObservableValue<any>\n", "import {\n    CaughtException,\n    IDerivation,\n    IDerivationState_,\n    IE<PERSON>lsComparer,\n    IObservable,\n    Lambda,\n    TraceMode,\n    autorun,\n    clearObserving,\n    comparer,\n    createAction,\n    createInstanceofPredicate,\n    endBatch,\n    getNextId,\n    globalState,\n    isCaughtException,\n    isSpyEnabled,\n    propagateChangeConfirmed,\n    propagateMaybeChanged,\n    reportObserved,\n    shouldCompute,\n    spyReport,\n    startBatch,\n    toPrimitive,\n    trackDerivedFunction,\n    untrackedEnd,\n    untrackedStart,\n    UPDATE,\n    die,\n    allowStateChangesStart,\n    allowStateChangesEnd\n} from \"../internal\"\n\nexport interface IComputedValue<T> {\n    get(): T\n    set(value: T): void\n    observe_(listener: (change: IComputedDidChange<T>) => void, fireImmediately?: boolean): Lambda\n}\n\nexport interface IComputedValueOptions<T> {\n    get?: () => T\n    set?: (value: T) => void\n    name?: string\n    equals?: IEqualsComparer<T>\n    context?: any\n    requiresReaction?: boolean\n    keepAlive?: boolean\n}\n\nexport type IComputedDidChange<T = any> = {\n    type: \"update\"\n    observableKind: \"computed\"\n    object: unknown\n    debugObjectName: string\n    newValue: T\n    oldValue: T | undefined\n}\n\n/**\n * A node in the state dependency root that observes other nodes, and can be observed itself.\n *\n * ComputedValue will remember the result of the computation for the duration of the batch, or\n * while being observed.\n *\n * During this time it will recompute only when one of its direct dependencies changed,\n * but only when it is being accessed with `ComputedValue.get()`.\n *\n * Implementation description:\n * 1. First time it's being accessed it will compute and remember result\n *    give back remembered result until 2. happens\n * 2. First time any deep dependency change, propagate POSSIBLY_STALE to all observers, wait for 3.\n * 3. When it's being accessed, recompute if any shallow dependency changed.\n *    if result changed: propagate STALE to all observers, that were POSSIBLY_STALE from the last step.\n *    go to step 2. either way\n *\n * If at any point it's outside batch and it isn't observed: reset everything and go to 1.\n */\nexport class ComputedValue<T> implements IObservable, IComputedValue<T>, IDerivation {\n    dependenciesState_ = IDerivationState_.NOT_TRACKING_\n    observing_: IObservable[] = [] // nodes we are looking at. Our value depends on these nodes\n    newObserving_ = null // during tracking it's an array with new observed observers\n    isBeingObserved_ = false\n    isPendingUnobservation_: boolean = false\n    observers_ = new Set<IDerivation>()\n    diffValue_ = 0\n    runId_ = 0\n    lastAccessedBy_ = 0\n    lowestObserverState_ = IDerivationState_.UP_TO_DATE_\n    unboundDepsCount_ = 0\n    protected value_: T | undefined | CaughtException = new CaughtException(null)\n    name_: string\n    triggeredBy_?: string\n    isComputing_: boolean = false // to check for cycles\n    isRunningSetter_: boolean = false\n    derivation: () => T // N.B: unminified as it is used by MST\n    setter_?: (value: T) => void\n    isTracing_: TraceMode = TraceMode.NONE\n    scope_: Object | undefined\n    private equals_: IEqualsComparer<any>\n    private requiresReaction_: boolean\n    keepAlive_: boolean\n\n    /**\n     * Create a new computed value based on a function expression.\n     *\n     * The `name` property is for debug purposes only.\n     *\n     * The `equals` property specifies the comparer function to use to determine if a newly produced\n     * value differs from the previous value. Two comparers are provided in the library; `defaultComparer`\n     * compares based on identity comparison (===), and `structuralComparer` deeply compares the structure.\n     * Structural comparison can be convenient if you always produce a new aggregated object and\n     * don't want to notify observers if it is structurally the same.\n     * This is useful for working with vectors, mouse coordinates etc.\n     */\n    constructor(options: IComputedValueOptions<T>) {\n        if (!options.get) die(31)\n        this.derivation = options.get!\n        this.name_ = options.name || (__DEV__ ? \"ComputedValue@\" + getNextId() : \"ComputedValue\")\n        if (options.set) {\n            this.setter_ = createAction(\n                __DEV__ ? this.name_ + \"-setter\" : \"ComputedValue-setter\",\n                options.set\n            ) as any\n        }\n        this.equals_ =\n            options.equals ||\n            ((options as any).compareStructural || (options as any).struct\n                ? comparer.structural\n                : comparer.default)\n        this.scope_ = options.context\n        this.requiresReaction_ = !!options.requiresReaction\n        this.keepAlive_ = !!options.keepAlive\n    }\n\n    onBecomeStale_() {\n        propagateMaybeChanged(this)\n    }\n\n    public onBOL: Set<Lambda> | undefined\n    public onBUOL: Set<Lambda> | undefined\n\n    public onBO() {\n        if (this.onBOL) {\n            this.onBOL.forEach(listener => listener())\n        }\n    }\n\n    public onBUO() {\n        if (this.onBUOL) {\n            this.onBUOL.forEach(listener => listener())\n        }\n    }\n\n    /**\n     * Returns the current value of this computed value.\n     * Will evaluate its computation first if needed.\n     */\n    public get(): T {\n        if (this.isComputing_) die(32, this.name_, this.derivation)\n        if (\n            globalState.inBatch === 0 &&\n            // !globalState.trackingDerivatpion &&\n            this.observers_.size === 0 &&\n            !this.keepAlive_\n        ) {\n            if (shouldCompute(this)) {\n                this.warnAboutUntrackedRead_()\n                startBatch() // See perf test 'computed memoization'\n                this.value_ = this.computeValue_(false)\n                endBatch()\n            }\n        } else {\n            reportObserved(this)\n            if (shouldCompute(this)) {\n                let prevTrackingContext = globalState.trackingContext\n                if (this.keepAlive_ && !prevTrackingContext) globalState.trackingContext = this\n                if (this.trackAndCompute()) propagateChangeConfirmed(this)\n                globalState.trackingContext = prevTrackingContext\n            }\n        }\n        const result = this.value_!\n\n        if (isCaughtException(result)) throw result.cause\n        return result\n    }\n\n    public set(value: T) {\n        if (this.setter_) {\n            if (this.isRunningSetter_) die(33, this.name_)\n            this.isRunningSetter_ = true\n            try {\n                this.setter_.call(this.scope_, value)\n            } finally {\n                this.isRunningSetter_ = false\n            }\n        } else die(34, this.name_)\n    }\n\n    trackAndCompute(): boolean {\n        // N.B: unminified as it is used by MST\n        const oldValue = this.value_\n        const wasSuspended =\n            /* see #1208 */ this.dependenciesState_ === IDerivationState_.NOT_TRACKING_\n        const newValue = this.computeValue_(true)\n\n        if (__DEV__ && isSpyEnabled()) {\n            spyReport({\n                observableKind: \"computed\",\n                debugObjectName: this.name_,\n                object: this.scope_,\n                type: \"update\",\n                oldValue: this.value_,\n                newValue\n            } as IComputedDidChange)\n        }\n\n        const changed =\n            wasSuspended ||\n            isCaughtException(oldValue) ||\n            isCaughtException(newValue) ||\n            !this.equals_(oldValue, newValue)\n\n        if (changed) {\n            this.value_ = newValue\n        }\n\n        return changed\n    }\n\n    computeValue_(track: boolean) {\n        this.isComputing_ = true\n        // don't allow state changes during computation\n        const prev = allowStateChangesStart(false)\n        let res: T | CaughtException\n        if (track) {\n            res = trackDerivedFunction(this, this.derivation, this.scope_)\n        } else {\n            if (globalState.disableErrorBoundaries === true) {\n                res = this.derivation.call(this.scope_)\n            } else {\n                try {\n                    res = this.derivation.call(this.scope_)\n                } catch (e) {\n                    res = new CaughtException(e)\n                }\n            }\n        }\n        allowStateChangesEnd(prev)\n        this.isComputing_ = false\n        return res\n    }\n\n    suspend_() {\n        if (!this.keepAlive_) {\n            clearObserving(this)\n            this.value_ = undefined // don't hold on to computed value!\n        }\n    }\n\n    observe_(listener: (change: IComputedDidChange<T>) => void, fireImmediately?: boolean): Lambda {\n        let firstTime = true\n        let prevValue: T | undefined = undefined\n        return autorun(() => {\n            // TODO: why is this in a different place than the spyReport() function? in all other observables it's called in the same place\n            let newValue = this.get()\n            if (!firstTime || fireImmediately) {\n                const prevU = untrackedStart()\n                listener({\n                    observableKind: \"computed\",\n                    debugObjectName: this.name_,\n                    type: UPDATE,\n                    object: this,\n                    newValue,\n                    oldValue: prevValue\n                })\n                untrackedEnd(prevU)\n            }\n            firstTime = false\n            prevValue = newValue\n        })\n    }\n\n    warnAboutUntrackedRead_() {\n        if (!__DEV__) return\n        if (this.requiresReaction_ === true) {\n            die(`[mobx] Computed value ${this.name_} is read outside a reactive context`)\n        }\n        if (this.isTracing_ !== TraceMode.NONE) {\n            console.log(\n                `[mobx.trace] '${this.name_}' is being read outside a reactive context. Doing a full recompute`\n            )\n        }\n        if (globalState.computedRequiresReaction) {\n            console.warn(\n                `[mobx] Computed value ${this.name_} is being read outside a reactive context. Doing a full recompute`\n            )\n        }\n    }\n\n    toString() {\n        return `${this.name_}[${this.derivation.toString()}]`\n    }\n\n    valueOf(): T {\n        return toPrimitive(this.get())\n    }\n\n    [Symbol.toPrimitive]() {\n        return this.valueOf()\n    }\n}\n\nexport const isComputedValue = createInstanceofPredicate(\"ComputedValue\", ComputedValue)\n", "import {\n    <PERSON><PERSON><PERSON>,\n    IDepTreeNode,\n    IObservable,\n    addObserver,\n    globalState,\n    isComputedValue,\n    removeObserver\n} from \"../internal\"\n\nexport enum IDerivationState_ {\n    // before being run or (outside batch and not being observed)\n    // at this point derivation is not holding any data about dependency tree\n    NOT_TRACKING_ = -1,\n    // no shallow dependency changed since last computation\n    // won't recalculate derivation\n    // this is what makes mobx fast\n    UP_TO_DATE_ = 0,\n    // some deep dependency changed, but don't know if shallow dependency changed\n    // will require to check first if UP_TO_DATE or POSSIBLY_STALE\n    // currently only ComputedValue will propagate POSSIBLY_STALE\n    //\n    // having this state is second big optimization:\n    // don't have to recompute on every dependency change, but only when it's needed\n    POSSIBLY_STALE_ = 1,\n    // A shallow dependency has changed since last computation and the derivation\n    // will need to recompute when it's needed next.\n    STALE_ = 2\n}\n\nexport enum TraceMode {\n    NONE,\n    LOG,\n    BREAK\n}\n\n/**\n * A derivation is everything that can be derived from the state (all the atoms) in a pure manner.\n * See https://medium.com/@mweststrate/becoming-fully-reactive-an-in-depth-explanation-of-mobservable-55995262a254#.xvbh6qd74\n */\nexport interface IDerivation extends IDepTreeNode {\n    observing_: IObservable[]\n    newObserving_: null | IObservable[]\n    dependenciesState_: IDerivationState_\n    /**\n     * Id of the current run of a derivation. Each time the derivation is tracked\n     * this number is increased by one. This number is globally unique\n     */\n    runId_: number\n    /**\n     * amount of dependencies used by the derivation in this run, which has not been bound yet.\n     */\n    unboundDepsCount_: number\n    onBecomeStale_(): void\n    isTracing_: TraceMode\n\n    /**\n     *  warn if the derivation has no dependencies after creation/update\n     */\n    requiresObservable_?: boolean\n}\n\nexport class CaughtException {\n    constructor(public cause: any) {\n        // Empty\n    }\n}\n\nexport function isCaughtException(e: any): e is CaughtException {\n    return e instanceof CaughtException\n}\n\n/**\n * Finds out whether any dependency of the derivation has actually changed.\n * If dependenciesState is 1 then it will recalculate dependencies,\n * if any dependency changed it will propagate it by changing dependenciesState to 2.\n *\n * By iterating over the dependencies in the same order that they were reported and\n * stopping on the first change, all the recalculations are only called for ComputedValues\n * that will be tracked by derivation. That is because we assume that if the first x\n * dependencies of the derivation doesn't change then the derivation should run the same way\n * up until accessing x-th dependency.\n */\nexport function shouldCompute(derivation: IDerivation): boolean {\n    switch (derivation.dependenciesState_) {\n        case IDerivationState_.UP_TO_DATE_:\n            return false\n        case IDerivationState_.NOT_TRACKING_:\n        case IDerivationState_.STALE_:\n            return true\n        case IDerivationState_.POSSIBLY_STALE_: {\n            // state propagation can occur outside of action/reactive context #2195\n            const prevAllowStateReads = allowStateReadsStart(true)\n            const prevUntracked = untrackedStart() // no need for those computeds to be reported, they will be picked up in trackDerivedFunction.\n            const obs = derivation.observing_,\n                l = obs.length\n            for (let i = 0; i < l; i++) {\n                const obj = obs[i]\n                if (isComputedValue(obj)) {\n                    if (globalState.disableErrorBoundaries) {\n                        obj.get()\n                    } else {\n                        try {\n                            obj.get()\n                        } catch (e) {\n                            // we are not interested in the value *or* exception at this moment, but if there is one, notify all\n                            untrackedEnd(prevUntracked)\n                            allowStateReadsEnd(prevAllowStateReads)\n                            return true\n                        }\n                    }\n                    // if ComputedValue `obj` actually changed it will be computed and propagated to its observers.\n                    // and `derivation` is an observer of `obj`\n                    // invariantShouldCompute(derivation)\n                    if ((derivation.dependenciesState_ as any) === IDerivationState_.STALE_) {\n                        untrackedEnd(prevUntracked)\n                        allowStateReadsEnd(prevAllowStateReads)\n                        return true\n                    }\n                }\n            }\n            changeDependenciesStateTo0(derivation)\n            untrackedEnd(prevUntracked)\n            allowStateReadsEnd(prevAllowStateReads)\n            return false\n        }\n    }\n}\n\nexport function isComputingDerivation() {\n    return globalState.trackingDerivation !== null // filter out actions inside computations\n}\n\nexport function checkIfStateModificationsAreAllowed(atom: IAtom) {\n    if (!__DEV__) {\n        return\n    }\n    const hasObservers = atom.observers_.size > 0\n    // Should not be possible to change observed state outside strict mode, except during initialization, see #563\n    if (!globalState.allowStateChanges && (hasObservers || globalState.enforceActions === \"always\"))\n        console.warn(\n            \"[MobX] \" +\n                (globalState.enforceActions\n                    ? \"Since strict-mode is enabled, changing (observed) observable values without using an action is not allowed. Tried to modify: \"\n                    : \"Side effects like changing state are not allowed at this point. Are you trying to modify state from, for example, a computed value or the render function of a React component? You can wrap side effects in 'runInAction' (or decorate functions with 'action') if needed. Tried to modify: \") +\n                atom.name_\n        )\n}\n\nexport function checkIfStateReadsAreAllowed(observable: IObservable) {\n    if (__DEV__ && !globalState.allowStateReads && globalState.observableRequiresReaction) {\n        console.warn(`[mobx] Observable ${observable.name_} being read outside a reactive context`)\n    }\n}\n\n/**\n * Executes the provided function `f` and tracks which observables are being accessed.\n * The tracking information is stored on the `derivation` object and the derivation is registered\n * as observer of any of the accessed observables.\n */\nexport function trackDerivedFunction<T>(derivation: IDerivation, f: () => T, context: any) {\n    const prevAllowStateReads = allowStateReadsStart(true)\n    // pre allocate array allocation + room for variation in deps\n    // array will be trimmed by bindDependencies\n    changeDependenciesStateTo0(derivation)\n    derivation.newObserving_ = new Array(derivation.observing_.length + 100)\n    derivation.unboundDepsCount_ = 0\n    derivation.runId_ = ++globalState.runId\n    const prevTracking = globalState.trackingDerivation\n    globalState.trackingDerivation = derivation\n    globalState.inBatch++\n    let result\n    if (globalState.disableErrorBoundaries === true) {\n        result = f.call(context)\n    } else {\n        try {\n            result = f.call(context)\n        } catch (e) {\n            result = new CaughtException(e)\n        }\n    }\n    globalState.inBatch--\n    globalState.trackingDerivation = prevTracking\n    bindDependencies(derivation)\n\n    warnAboutDerivationWithoutDependencies(derivation)\n    allowStateReadsEnd(prevAllowStateReads)\n    return result\n}\n\nfunction warnAboutDerivationWithoutDependencies(derivation: IDerivation) {\n    if (!__DEV__) return\n\n    if (derivation.observing_.length !== 0) return\n\n    if (globalState.reactionRequiresObservable || derivation.requiresObservable_) {\n        console.warn(\n            `[mobx] Derivation ${derivation.name_} is created/updated without reading any observable value`\n        )\n    }\n}\n\n/**\n * diffs newObserving with observing.\n * update observing to be newObserving with unique observables\n * notify observers that become observed/unobserved\n */\nfunction bindDependencies(derivation: IDerivation) {\n    // invariant(derivation.dependenciesState !== IDerivationState.NOT_TRACKING, \"INTERNAL ERROR bindDependencies expects derivation.dependenciesState !== -1\");\n    const prevObserving = derivation.observing_\n    const observing = (derivation.observing_ = derivation.newObserving_!)\n    let lowestNewObservingDerivationState = IDerivationState_.UP_TO_DATE_\n\n    // Go through all new observables and check diffValue: (this list can contain duplicates):\n    //   0: first occurrence, change to 1 and keep it\n    //   1: extra occurrence, drop it\n    let i0 = 0,\n        l = derivation.unboundDepsCount_\n    for (let i = 0; i < l; i++) {\n        const dep = observing[i]\n        if (dep.diffValue_ === 0) {\n            dep.diffValue_ = 1\n            if (i0 !== i) observing[i0] = dep\n            i0++\n        }\n\n        // Upcast is 'safe' here, because if dep is IObservable, `dependenciesState` will be undefined,\n        // not hitting the condition\n        if (((dep as any) as IDerivation).dependenciesState_ > lowestNewObservingDerivationState) {\n            lowestNewObservingDerivationState = ((dep as any) as IDerivation).dependenciesState_\n        }\n    }\n    observing.length = i0\n\n    derivation.newObserving_ = null // newObserving shouldn't be needed outside tracking (statement moved down to work around FF bug, see #614)\n\n    // Go through all old observables and check diffValue: (it is unique after last bindDependencies)\n    //   0: it's not in new observables, unobserve it\n    //   1: it keeps being observed, don't want to notify it. change to 0\n    l = prevObserving.length\n    while (l--) {\n        const dep = prevObserving[l]\n        if (dep.diffValue_ === 0) {\n            removeObserver(dep, derivation)\n        }\n        dep.diffValue_ = 0\n    }\n\n    // Go through all new observables and check diffValue: (now it should be unique)\n    //   0: it was set to 0 in last loop. don't need to do anything.\n    //   1: it wasn't observed, let's observe it. set back to 0\n    while (i0--) {\n        const dep = observing[i0]\n        if (dep.diffValue_ === 1) {\n            dep.diffValue_ = 0\n            addObserver(dep, derivation)\n        }\n    }\n\n    // Some new observed derivations may become stale during this derivation computation\n    // so they have had no chance to propagate staleness (#916)\n    if (lowestNewObservingDerivationState !== IDerivationState_.UP_TO_DATE_) {\n        derivation.dependenciesState_ = lowestNewObservingDerivationState\n        derivation.onBecomeStale_()\n    }\n}\n\nexport function clearObserving(derivation: IDerivation) {\n    // invariant(globalState.inBatch > 0, \"INTERNAL ERROR clearObserving should be called only inside batch\");\n    const obs = derivation.observing_\n    derivation.observing_ = []\n    let i = obs.length\n    while (i--) removeObserver(obs[i], derivation)\n\n    derivation.dependenciesState_ = IDerivationState_.NOT_TRACKING_\n}\n\nexport function untracked<T>(action: () => T): T {\n    const prev = untrackedStart()\n    try {\n        return action()\n    } finally {\n        untrackedEnd(prev)\n    }\n}\n\nexport function untrackedStart(): IDerivation | null {\n    const prev = globalState.trackingDerivation\n    globalState.trackingDerivation = null\n    return prev\n}\n\nexport function untrackedEnd(prev: IDerivation | null) {\n    globalState.trackingDerivation = prev\n}\n\nexport function allowStateReadsStart(allowStateReads: boolean) {\n    const prev = globalState.allowStateReads\n    globalState.allowStateReads = allowStateReads\n    return prev\n}\n\nexport function allowStateReadsEnd(prev: boolean) {\n    globalState.allowStateReads = prev\n}\n\n/**\n * needed to keep `lowestObserverState` correct. when changing from (2 or 1) to 0\n *\n */\nexport function changeDependenciesStateTo0(derivation: IDerivation) {\n    if (derivation.dependenciesState_ === IDerivationState_.UP_TO_DATE_) return\n    derivation.dependenciesState_ = IDerivationState_.UP_TO_DATE_\n\n    const obs = derivation.observing_\n    let i = obs.length\n    while (i--) obs[i].lowestObserverState_ = IDerivationState_.UP_TO_DATE_\n}\n", "import { IDerivation, IObservable, Reaction, die, getGlobal } from \"../internal\"\nimport { ComputedValue } from \"./computedvalue\"\n\n/**\n * These values will persist if global state is reset\n */\nconst persistentKeys: (keyof MobXGlobals)[] = [\n    \"mobxGuid\",\n    \"spyListeners\",\n    \"enforceActions\",\n    \"computedRequiresReaction\",\n    \"reactionRequiresObservable\",\n    \"observableRequiresReaction\",\n    \"allowStateReads\",\n    \"disableErrorBoundaries\",\n    \"runId\",\n    \"UNCHANGED\",\n    \"useProxies\"\n]\n\nexport type IUNCHANGED = {}\n\nexport class MobXGlobals {\n    /**\n     * MobXGlobals version.\n     * MobX compatiblity with other versions loaded in memory as long as this version matches.\n     * It indicates that the global state still stores similar information\n     *\n     * N.B: this version is unrelated to the package version of MobX, and is only the version of the\n     * internal state storage of MobX, and can be the same across many different package versions\n     */\n    version = 6\n\n    /**\n     * globally unique token to signal unchanged\n     */\n    UNCHANGED: IUNCHANGED = {}\n\n    /**\n     * Currently running derivation\n     */\n    trackingDerivation: IDerivation | null = null\n\n    /**\n     * Currently running reaction. This determines if we currently have a reactive context.\n     * (Tracking derivation is also set for temporal tracking of computed values inside actions,\n     * but trackingReaction can only be set by a form of Reaction)\n     */\n    trackingContext: Reaction | ComputedValue<any> | null = null\n\n    /**\n     * Each time a derivation is tracked, it is assigned a unique run-id\n     */\n    runId = 0\n\n    /**\n     * 'guid' for general purpose. Will be persisted amongst resets.\n     */\n    mobxGuid = 0\n\n    /**\n     * Are we in a batch block? (and how many of them)\n     */\n    inBatch: number = 0\n\n    /**\n     * Observables that don't have observers anymore, and are about to be\n     * suspended, unless somebody else accesses it in the same batch\n     *\n     * @type {IObservable[]}\n     */\n    pendingUnobservations: IObservable[] = []\n\n    /**\n     * List of scheduled, not yet executed, reactions.\n     */\n    pendingReactions: Reaction[] = []\n\n    /**\n     * Are we currently processing reactions?\n     */\n    isRunningReactions = false\n\n    /**\n     * Is it allowed to change observables at this point?\n     * In general, MobX doesn't allow that when running computations and React.render.\n     * To ensure that those functions stay pure.\n     */\n    allowStateChanges = false\n\n    /**\n     * Is it allowed to read observables at this point?\n     * Used to hold the state needed for `observableRequiresReaction`\n     */\n    allowStateReads = true\n\n    /**\n     * If strict mode is enabled, state changes are by default not allowed\n     */\n    enforceActions: boolean | \"always\" = true\n\n    /**\n     * Spy callbacks\n     */\n    spyListeners: { (change: any): void }[] = []\n\n    /**\n     * Globally attached error handlers that react specifically to errors in reactions\n     */\n    globalReactionErrorHandlers: ((error: any, derivation: IDerivation) => void)[] = []\n\n    /**\n     * Warn if computed values are accessed outside a reactive context\n     */\n    computedRequiresReaction = false\n\n    /**\n     * (Experimental)\n     * Warn if you try to create to derivation / reactive context without accessing any observable.\n     */\n    reactionRequiresObservable = false\n\n    /**\n     * (Experimental)\n     * Warn if observables are accessed outside a reactive context\n     */\n    observableRequiresReaction = false\n\n    /*\n     * Don't catch and rethrow exceptions. This is useful for inspecting the state of\n     * the stack when an exception occurs while debugging.\n     */\n    disableErrorBoundaries = false\n\n    /*\n     * If true, we are already handling an exception in an action. Any errors in reactions should be suppressed, as\n     * they are not the cause, see: https://github.com/mobxjs/mobx/issues/1836\n     */\n    suppressReactionErrors = false\n\n    useProxies = true\n    /*\n     * print warnings about code that would fail if proxies weren't available\n     */\n    verifyProxies = false\n\n    /**\n     * False forces all object's descriptors to\n     * writable: true\n     * configurable: true\n     */\n    safeDescriptors = true\n}\n\nlet canMergeGlobalState = true\nlet isolateCalled = false\n\nexport let globalState: MobXGlobals = (function () {\n    let global = getGlobal()\n    if (global.__mobxInstanceCount > 0 && !global.__mobxGlobals) canMergeGlobalState = false\n    if (global.__mobxGlobals && global.__mobxGlobals.version !== new MobXGlobals().version)\n        canMergeGlobalState = false\n\n    if (!canMergeGlobalState) {\n        setTimeout(() => {\n            if (!isolateCalled) {\n                die(35)\n            }\n        }, 1)\n        return new MobXGlobals()\n    } else if (global.__mobxGlobals) {\n        global.__mobxInstanceCount += 1\n        if (!global.__mobxGlobals.UNCHANGED) global.__mobxGlobals.UNCHANGED = {} // make merge backward compatible\n        return global.__mobxGlobals\n    } else {\n        global.__mobxInstanceCount = 1\n        return (global.__mobxGlobals = new MobXGlobals())\n    }\n})()\n\nexport function isolateGlobalState() {\n    if (\n        globalState.pendingReactions.length ||\n        globalState.inBatch ||\n        globalState.isRunningReactions\n    )\n        die(36)\n    isolateCalled = true\n    if (canMergeGlobalState) {\n        let global = getGlobal()\n        if (--global.__mobxInstanceCount === 0) global.__mobxGlobals = undefined\n        globalState = new MobXGlobals()\n    }\n}\n\nexport function getGlobalState(): any {\n    return globalState\n}\n\n/**\n * For testing purposes only; this will break the internal state of existing observables,\n * but can be used to get back at a stable state after throwing errors\n */\nexport function resetGlobalState() {\n    const defaultGlobals = new MobXGlobals()\n    for (let key in defaultGlobals)\n        if (persistentKeys.indexOf(key as any) === -1) globalState[key] = defaultGlobals[key]\n    globalState.allowStateChanges = !globalState.enforceActions\n}\n", "import {\n    Lambda,\n    ComputedValue,\n    IDependencyTree,\n    IDerivation,\n    IDerivationState_,\n    TraceMode,\n    getDependencyTree,\n    globalState,\n    runReactions,\n    checkIfStateReadsAreAllowed\n} from \"../internal\"\n\nexport interface IDepTreeNode {\n    name_: string\n    observing_?: IObservable[]\n}\n\nexport interface IObservable extends IDepTreeNode {\n    diffValue_: number\n    /**\n     * Id of the derivation *run* that last accessed this observable.\n     * If this id equals the *run* id of the current derivation,\n     * the dependency is already established\n     */\n    lastAccessedBy_: number\n    isBeingObserved_: boolean\n\n    lowestObserverState_: IDerivationState_ // Used to avoid redundant propagations\n    isPendingUnobservation_: boolean // Used to push itself to global.pendingUnobservations at most once per batch.\n\n    observers_: Set<IDerivation>\n\n    onBUO(): void\n    onBO(): void\n\n    onBUOL: Set<Lambda> | undefined\n    onBOL: Set<Lambda> | undefined\n}\n\nexport function hasObservers(observable: IObservable): boolean {\n    return observable.observers_ && observable.observers_.size > 0\n}\n\nexport function getObservers(observable: IObservable): Set<IDerivation> {\n    return observable.observers_\n}\n\n// function invariantObservers(observable: IObservable) {\n//     const list = observable.observers\n//     const map = observable.observersIndexes\n//     const l = list.length\n//     for (let i = 0; i < l; i++) {\n//         const id = list[i].__mapid\n//         if (i) {\n//             invariant(map[id] === i, \"INTERNAL ERROR maps derivation.__mapid to index in list\") // for performance\n//         } else {\n//             invariant(!(id in map), \"INTERNAL ERROR observer on index 0 shouldn't be held in map.\") // for performance\n//         }\n//     }\n//     invariant(\n//         list.length === 0 || Object.keys(map).length === list.length - 1,\n//         \"INTERNAL ERROR there is no junk in map\"\n//     )\n// }\nexport function addObserver(observable: IObservable, node: IDerivation) {\n    // invariant(node.dependenciesState !== -1, \"INTERNAL ERROR, can add only dependenciesState !== -1\");\n    // invariant(observable._observers.indexOf(node) === -1, \"INTERNAL ERROR add already added node\");\n    // invariantObservers(observable);\n\n    observable.observers_.add(node)\n    if (observable.lowestObserverState_ > node.dependenciesState_)\n        observable.lowestObserverState_ = node.dependenciesState_\n\n    // invariantObservers(observable);\n    // invariant(observable._observers.indexOf(node) !== -1, \"INTERNAL ERROR didn't add node\");\n}\n\nexport function removeObserver(observable: IObservable, node: IDerivation) {\n    // invariant(globalState.inBatch > 0, \"INTERNAL ERROR, remove should be called only inside batch\");\n    // invariant(observable._observers.indexOf(node) !== -1, \"INTERNAL ERROR remove already removed node\");\n    // invariantObservers(observable);\n    observable.observers_.delete(node)\n    if (observable.observers_.size === 0) {\n        // deleting last observer\n        queueForUnobservation(observable)\n    }\n    // invariantObservers(observable);\n    // invariant(observable._observers.indexOf(node) === -1, \"INTERNAL ERROR remove already removed node2\");\n}\n\nexport function queueForUnobservation(observable: IObservable) {\n    if (observable.isPendingUnobservation_ === false) {\n        // invariant(observable._observers.length === 0, \"INTERNAL ERROR, should only queue for unobservation unobserved observables\");\n        observable.isPendingUnobservation_ = true\n        globalState.pendingUnobservations.push(observable)\n    }\n}\n\n/**\n * Batch starts a transaction, at least for purposes of memoizing ComputedValues when nothing else does.\n * During a batch `onBecomeUnobserved` will be called at most once per observable.\n * Avoids unnecessary recalculations.\n */\nexport function startBatch() {\n    globalState.inBatch++\n}\n\nexport function endBatch() {\n    if (--globalState.inBatch === 0) {\n        runReactions()\n        // the batch is actually about to finish, all unobserving should happen here.\n        const list = globalState.pendingUnobservations\n        for (let i = 0; i < list.length; i++) {\n            const observable = list[i]\n            observable.isPendingUnobservation_ = false\n            if (observable.observers_.size === 0) {\n                if (observable.isBeingObserved_) {\n                    // if this observable had reactive observers, trigger the hooks\n                    observable.isBeingObserved_ = false\n                    observable.onBUO()\n                }\n                if (observable instanceof ComputedValue) {\n                    // computed values are automatically teared down when the last observer leaves\n                    // this process happens recursively, this computed might be the last observabe of another, etc..\n                    observable.suspend_()\n                }\n            }\n        }\n        globalState.pendingUnobservations = []\n    }\n}\n\nexport function reportObserved(observable: IObservable): boolean {\n    checkIfStateReadsAreAllowed(observable)\n\n    const derivation = globalState.trackingDerivation\n    if (derivation !== null) {\n        /**\n         * Simple optimization, give each derivation run an unique id (runId)\n         * Check if last time this observable was accessed the same runId is used\n         * if this is the case, the relation is already known\n         */\n        if (derivation.runId_ !== observable.lastAccessedBy_) {\n            observable.lastAccessedBy_ = derivation.runId_\n            // Tried storing newObserving, or observing, or both as Set, but performance didn't come close...\n            derivation.newObserving_![derivation.unboundDepsCount_++] = observable\n            if (!observable.isBeingObserved_ && globalState.trackingContext) {\n                observable.isBeingObserved_ = true\n                observable.onBO()\n            }\n        }\n        return true\n    } else if (observable.observers_.size === 0 && globalState.inBatch > 0) {\n        queueForUnobservation(observable)\n    }\n\n    return false\n}\n\n// function invariantLOS(observable: IObservable, msg: string) {\n//     // it's expensive so better not run it in produciton. but temporarily helpful for testing\n//     const min = getObservers(observable).reduce((a, b) => Math.min(a, b.dependenciesState), 2)\n//     if (min >= observable.lowestObserverState) return // <- the only assumption about `lowestObserverState`\n//     throw new Error(\n//         \"lowestObserverState is wrong for \" +\n//             msg +\n//             \" because \" +\n//             min +\n//             \" < \" +\n//             observable.lowestObserverState\n//     )\n// }\n\n/**\n * NOTE: current propagation mechanism will in case of self reruning autoruns behave unexpectedly\n * It will propagate changes to observers from previous run\n * It's hard or maybe impossible (with reasonable perf) to get it right with current approach\n * Hopefully self reruning autoruns aren't a feature people should depend on\n * Also most basic use cases should be ok\n */\n\n// Called by Atom when its value changes\nexport function propagateChanged(observable: IObservable) {\n    // invariantLOS(observable, \"changed start\");\n    if (observable.lowestObserverState_ === IDerivationState_.STALE_) return\n    observable.lowestObserverState_ = IDerivationState_.STALE_\n\n    // Ideally we use for..of here, but the downcompiled version is really slow...\n    observable.observers_.forEach(d => {\n        if (d.dependenciesState_ === IDerivationState_.UP_TO_DATE_) {\n            if (__DEV__ && d.isTracing_ !== TraceMode.NONE) {\n                logTraceInfo(d, observable)\n            }\n            d.onBecomeStale_()\n        }\n        d.dependenciesState_ = IDerivationState_.STALE_\n    })\n    // invariantLOS(observable, \"changed end\");\n}\n\n// Called by ComputedValue when it recalculate and its value changed\nexport function propagateChangeConfirmed(observable: IObservable) {\n    // invariantLOS(observable, \"confirmed start\");\n    if (observable.lowestObserverState_ === IDerivationState_.STALE_) return\n    observable.lowestObserverState_ = IDerivationState_.STALE_\n\n    observable.observers_.forEach(d => {\n        if (d.dependenciesState_ === IDerivationState_.POSSIBLY_STALE_) {\n            d.dependenciesState_ = IDerivationState_.STALE_\n            if (__DEV__ && d.isTracing_ !== TraceMode.NONE) {\n                logTraceInfo(d, observable)\n            }\n        } else if (\n            d.dependenciesState_ === IDerivationState_.UP_TO_DATE_ // this happens during computing of `d`, just keep lowestObserverState up to date.\n        ) {\n            observable.lowestObserverState_ = IDerivationState_.UP_TO_DATE_\n        }\n    })\n    // invariantLOS(observable, \"confirmed end\");\n}\n\n// Used by computed when its dependency changed, but we don't wan't to immediately recompute.\nexport function propagateMaybeChanged(observable: IObservable) {\n    // invariantLOS(observable, \"maybe start\");\n    if (observable.lowestObserverState_ !== IDerivationState_.UP_TO_DATE_) return\n    observable.lowestObserverState_ = IDerivationState_.POSSIBLY_STALE_\n\n    observable.observers_.forEach(d => {\n        if (d.dependenciesState_ === IDerivationState_.UP_TO_DATE_) {\n            d.dependenciesState_ = IDerivationState_.POSSIBLY_STALE_\n            d.onBecomeStale_()\n        }\n    })\n    // invariantLOS(observable, \"maybe end\");\n}\n\nfunction logTraceInfo(derivation: IDerivation, observable: IObservable) {\n    console.log(\n        `[mobx.trace] '${derivation.name_}' is invalidated due to a change in: '${observable.name_}'`\n    )\n    if (derivation.isTracing_ === TraceMode.BREAK) {\n        const lines = []\n        printDepTree(getDependencyTree(derivation), lines, 1)\n\n        // prettier-ignore\n        new Function(\n`debugger;\n/*\nTracing '${derivation.name_}'\n\nYou are entering this break point because derivation '${derivation.name_}' is being traced and '${observable.name_}' is now forcing it to update.\nJust follow the stacktrace you should now see in the devtools to see precisely what piece of your code is causing this update\nThe stackframe you are looking for is at least ~6-8 stack-frames up.\n\n${derivation instanceof ComputedValue ? derivation.derivation.toString().replace(/[*]\\//g, \"/\") : \"\"}\n\nThe dependencies for this derivation are:\n\n${lines.join(\"\\n\")}\n*/\n    `)()\n    }\n}\n\nfunction printDepTree(tree: IDependencyTree, lines: string[], depth: number) {\n    if (lines.length >= 1000) {\n        lines.push(\"(and many more)\")\n        return\n    }\n    lines.push(`${new Array(depth).join(\"\\t\")}${tree.name}`) // MWE: not the fastest, but the easiest way :)\n    if (tree.dependencies) tree.dependencies.forEach(child => printDepTree(child, lines, depth + 1))\n}\n", "import {\n    $mobx,\n    IDerivation,\n    IDerivationState_,\n    IObservable,\n    Lambda,\n    TraceMode,\n    clearObserving,\n    createInstanceofPredicate,\n    endBatch,\n    getNextId,\n    globalState,\n    isCaughtException,\n    isSpyEnabled,\n    shouldCompute,\n    spyReport,\n    spyReportEnd,\n    spyReportStart,\n    startBatch,\n    trace,\n    trackDerivedFunction\n} from \"../internal\"\n\n/**\n * Reactions are a special kind of derivations. Several things distinguishes them from normal reactive computations\n *\n * 1) They will always run, whether they are used by other computations or not.\n * This means that they are very suitable for triggering side effects like logging, updating the DOM and making network requests.\n * 2) They are not observable themselves\n * 3) They will always run after any 'normal' derivations\n * 4) They are allowed to change the state and thereby triggering themselves again, as long as they make sure the state propagates to a stable state in a reasonable amount of iterations.\n *\n * The state machine of a Reaction is as follows:\n *\n * 1) after creating, the reaction should be started by calling `runReaction` or by scheduling it (see also `autorun`)\n * 2) the `onInvalidate` handler should somehow result in a call to `this.track(someFunction)`\n * 3) all observables accessed in `someFunction` will be observed by this reaction.\n * 4) as soon as some of the dependencies has changed the Reaction will be rescheduled for another run (after the current mutation or transaction). `isScheduled` will yield true once a dependency is stale and during this period\n * 5) `onInvalidate` will be called, and we are back at step 1.\n *\n */\n\nexport interface IReactionPublic {\n    dispose(): void\n    trace(enterBreakPoint?: boolean): void\n}\n\nexport interface IReactionDisposer {\n    (): void\n    $mobx: Reaction\n}\n\nexport class Reaction implements IDerivation, IReactionPublic {\n    observing_: IObservable[] = [] // nodes we are looking at. Our value depends on these nodes\n    newObserving_: IObservable[] = []\n    dependenciesState_ = IDerivationState_.NOT_TRACKING_\n    diffValue_ = 0\n    runId_ = 0\n    unboundDepsCount_ = 0\n    isDisposed_ = false\n    isScheduled_ = false\n    isTrackPending_ = false\n    isRunning_ = false\n    isTracing_: TraceMode = TraceMode.NONE\n\n    constructor(\n        public name_: string = __DEV__ ? \"Reaction@\" + getNextId() : \"Reaction\",\n        private onInvalidate_: () => void,\n        private errorHandler_?: (error: any, derivation: IDerivation) => void,\n        public requiresObservable_ = false\n    ) {}\n\n    onBecomeStale_() {\n        this.schedule_()\n    }\n\n    schedule_() {\n        if (!this.isScheduled_) {\n            this.isScheduled_ = true\n            globalState.pendingReactions.push(this)\n            runReactions()\n        }\n    }\n\n    isScheduled() {\n        return this.isScheduled_\n    }\n\n    /**\n     * internal, use schedule() if you intend to kick off a reaction\n     */\n    runReaction_() {\n        if (!this.isDisposed_) {\n            startBatch()\n            this.isScheduled_ = false\n            const prev = globalState.trackingContext\n            globalState.trackingContext = this\n            if (shouldCompute(this)) {\n                this.isTrackPending_ = true\n\n                try {\n                    this.onInvalidate_()\n                    if (__DEV__ && this.isTrackPending_ && isSpyEnabled()) {\n                        // onInvalidate didn't trigger track right away..\n                        spyReport({\n                            name: this.name_,\n                            type: \"scheduled-reaction\"\n                        })\n                    }\n                } catch (e) {\n                    this.reportExceptionInDerivation_(e)\n                }\n            }\n            globalState.trackingContext = prev\n            endBatch()\n        }\n    }\n\n    track(fn: () => void) {\n        if (this.isDisposed_) {\n            return\n            // console.warn(\"Reaction already disposed\") // Note: Not a warning / error in mobx 4 either\n        }\n        startBatch()\n        const notify = isSpyEnabled()\n        let startTime\n        if (__DEV__ && notify) {\n            startTime = Date.now()\n            spyReportStart({\n                name: this.name_,\n                type: \"reaction\"\n            })\n        }\n        this.isRunning_ = true\n        const prevReaction = globalState.trackingContext // reactions could create reactions...\n        globalState.trackingContext = this\n        const result = trackDerivedFunction(this, fn, undefined)\n        globalState.trackingContext = prevReaction\n        this.isRunning_ = false\n        this.isTrackPending_ = false\n        if (this.isDisposed_) {\n            // disposed during last run. Clean up everything that was bound after the dispose call.\n            clearObserving(this)\n        }\n        if (isCaughtException(result)) this.reportExceptionInDerivation_(result.cause)\n        if (__DEV__ && notify) {\n            spyReportEnd({\n                time: Date.now() - startTime\n            })\n        }\n        endBatch()\n    }\n\n    reportExceptionInDerivation_(error: any) {\n        if (this.errorHandler_) {\n            this.errorHandler_(error, this)\n            return\n        }\n\n        if (globalState.disableErrorBoundaries) throw error\n\n        const message = __DEV__\n            ? `[mobx] Encountered an uncaught exception that was thrown by a reaction or observer component, in: '${this}'`\n            : `[mobx] uncaught error in '${this}'`\n        if (!globalState.suppressReactionErrors) {\n            console.error(message, error)\n            /** If debugging brought you here, please, read the above message :-). Tnx! */\n        } else if (__DEV__) console.warn(`[mobx] (error in reaction '${this.name_}' suppressed, fix error of causing action below)`) // prettier-ignore\n\n        if (__DEV__ && isSpyEnabled()) {\n            spyReport({\n                type: \"error\",\n                name: this.name_,\n                message,\n                error: \"\" + error\n            })\n        }\n\n        globalState.globalReactionErrorHandlers.forEach(f => f(error, this))\n    }\n\n    dispose() {\n        if (!this.isDisposed_) {\n            this.isDisposed_ = true\n            if (!this.isRunning_) {\n                // if disposed while running, clean up later. Maybe not optimal, but rare case\n                startBatch()\n                clearObserving(this)\n                endBatch()\n            }\n        }\n    }\n\n    getDisposer_(): IReactionDisposer {\n        const r = this.dispose.bind(this) as IReactionDisposer\n        r[$mobx] = this\n        return r\n    }\n\n    toString() {\n        return `Reaction[${this.name_}]`\n    }\n\n    trace(enterBreakPoint: boolean = false) {\n        trace(this, enterBreakPoint)\n    }\n}\n\nexport function onReactionError(handler: (error: any, derivation: IDerivation) => void): Lambda {\n    globalState.globalReactionErrorHandlers.push(handler)\n    return () => {\n        const idx = globalState.globalReactionErrorHandlers.indexOf(handler)\n        if (idx >= 0) globalState.globalReactionErrorHandlers.splice(idx, 1)\n    }\n}\n\n/**\n * Magic number alert!\n * Defines within how many times a reaction is allowed to re-trigger itself\n * until it is assumed that this is gonna be a never ending loop...\n */\nconst MAX_REACTION_ITERATIONS = 100\n\nlet reactionScheduler: (fn: () => void) => void = f => f()\n\nexport function runReactions() {\n    // Trampolining, if runReactions are already running, new reactions will be picked up\n    if (globalState.inBatch > 0 || globalState.isRunningReactions) return\n    reactionScheduler(runReactionsHelper)\n}\n\nfunction runReactionsHelper() {\n    globalState.isRunningReactions = true\n    const allReactions = globalState.pendingReactions\n    let iterations = 0\n\n    // While running reactions, new reactions might be triggered.\n    // Hence we work with two variables and check whether\n    // we converge to no remaining reactions after a while.\n    while (allReactions.length > 0) {\n        if (++iterations === MAX_REACTION_ITERATIONS) {\n            console.error(\n                __DEV__\n                    ? `Reaction doesn't converge to a stable state after ${MAX_REACTION_ITERATIONS} iterations.` +\n                          ` Probably there is a cycle in the reactive function: ${allReactions[0]}`\n                    : `[mobx] cycle in reaction: ${allReactions[0]}`\n            )\n            allReactions.splice(0) // clear reactions\n        }\n        let remainingReactions = allReactions.splice(0)\n        for (let i = 0, l = remainingReactions.length; i < l; i++)\n            remainingReactions[i].runReaction_()\n    }\n    globalState.isRunningReactions = false\n}\n\nexport const isReaction = createInstanceofPredicate(\"Reaction\", Reaction)\n\nexport function setReactionScheduler(fn: (f: () => void) => void) {\n    const baseScheduler = reactionScheduler\n    reactionScheduler = f => fn(() => baseScheduler(f))\n}\n", "import { IComputedDidChange } from \"./computedvalue\"\nimport { IValueDid<PERSON>hange, IBoxDidChange } from \"./../types/observablevalue\"\nimport { IObjectDidChange } from \"./../types/observableobject\"\nimport { IArrayDidChange } from \"./../types/observablearray\"\nimport { Lambda, globalState, once, ISetDidChange, IMapDidChange } from \"../internal\"\n\nexport function isSpyEnabled() {\n    return __DEV__ && !!globalState.spyListeners.length\n}\n\nexport type PureSpyEvent =\n    | { type: \"action\"; name: string; object: unknown; arguments: unknown[] }\n    | { type: \"scheduled-reaction\"; name: string }\n    | { type: \"reaction\"; name: string }\n    | { type: \"error\"; name: string; message: string; error: string }\n    | IComputedDidChange<unknown>\n    | IObjectDidChange<unknown>\n    | IArrayDidChange<unknown>\n    | IMapDidChange<unknown, unknown>\n    | ISetDidChange<unknown>\n    | IValueDidChange<unknown>\n    | IBoxDidChange<unknown>\n    | { type: \"report-end\"; spyReportEnd: true; time?: number }\n\ntype SpyEvent = PureSpyEvent & { spyReportStart?: true }\n\nexport function spyReport(event: SpyEvent) {\n    if (!__DEV__) return // dead code elimination can do the rest\n    if (!globalState.spyListeners.length) return\n    const listeners = globalState.spyListeners\n    for (let i = 0, l = listeners.length; i < l; i++) listeners[i](event)\n}\n\nexport function spyReportStart(event: PureSpyEvent) {\n    if (!__DEV__) return\n    const change = { ...event, spyReportStart: true as const }\n    spyReport(change)\n}\n\nconst END_EVENT: SpyEvent = { type: \"report-end\", spyReportEnd: true }\n\nexport function spyReportEnd(change?: { time?: number }) {\n    if (!__DEV__) return\n    if (change) spyReport({ ...change, type: \"report-end\", spyReportEnd: true })\n    else spyReport(END_EVENT)\n}\n\nexport function spy(listener: (change: SpyEvent) => void): Lambda {\n    if (!__DEV__) {\n        console.warn(`[mobx.spy] Is a no-op in production builds`)\n        return function () {}\n    } else {\n        globalState.spyListeners.push(listener)\n        return once(() => {\n            globalState.spyListeners = globalState.spyListeners.filter(l => l !== listener)\n        })\n    }\n}\n", "import {\n    createAction,\n    executeAction,\n    Annotation,\n    storeAnnotation,\n    die,\n    isFunction,\n    isStringish,\n    createDecoratorAnnotation,\n    createActionAnnotation\n} from \"../internal\"\n\nexport const ACTION = \"action\"\nexport const ACTION_BOUND = \"action.bound\"\nexport const AUTOACTION = \"autoAction\"\nexport const AUTOACTION_BOUND = \"autoAction.bound\"\n\nconst DEFAULT_ACTION_NAME = \"<unnamed action>\"\n\nconst actionAnnotation = createActionAnnotation(ACTION)\nconst actionBoundAnnotation = createActionAnnotation(ACTION_BOUND, {\n    bound: true\n})\nconst autoActionAnnotation = createActionAnnotation(AUTOACTION, {\n    autoAction: true\n})\nconst autoActionBoundAnnotation = createActionAnnotation(AUTOACTION_BOUND, {\n    autoAction: true,\n    bound: true\n})\n\nexport interface IActionFactory extends Annotation, PropertyDecorator {\n    // nameless actions\n    <T extends Function | undefined | null>(fn: T): T\n    // named actions\n    <T extends Function | undefined | null>(name: string, fn: T): T\n\n    // named decorator\n    (customName: string): PropertyDecorator & Annotation\n\n    // decorator (name no longer supported)\n    bound: Annotation & PropertyDecorator\n}\n\nfunction createActionFactory(autoAction: boolean): IActionFactory {\n    const res: IActionFactory = function action(arg1, arg2?): any {\n        // action(fn() {})\n        if (isFunction(arg1))\n            return createAction(arg1.name || DEFAULT_ACTION_NAME, arg1, autoAction)\n        // action(\"name\", fn() {})\n        if (isFunction(arg2)) return createAction(arg1, arg2, autoAction)\n        // @action\n        if (isStringish(arg2)) {\n            return storeAnnotation(arg1, arg2, autoAction ? autoActionAnnotation : actionAnnotation)\n        }\n        // action(\"name\") & @action(\"name\")\n        if (isStringish(arg1)) {\n            return createDecoratorAnnotation(\n                createActionAnnotation(autoAction ? AUTOACTION : ACTION, {\n                    name: arg1,\n                    autoAction\n                })\n            )\n        }\n\n        if (__DEV__) die(\"Invalid arguments for `action`\")\n    } as IActionFactory\n    return res\n}\n\nexport const action: IActionFactory = createActionFactory(false)\nObject.assign(action, actionAnnotation)\nexport const autoAction: IActionFactory = createActionFactory(true)\nObject.assign(autoAction, autoActionAnnotation)\n\naction.bound = createDecoratorAnnotation(actionBoundAnnotation)\nautoAction.bound = createDecoratorAnnotation(autoActionBoundAnnotation)\n\nexport function runInAction<T>(fn: () => T): T {\n    return executeAction(fn.name || DEFAULT_ACTION_NAME, false, fn, this, undefined)\n}\n\nexport function isAction(thing: any) {\n    return isFunction(thing) && thing.isMobxAction === true\n}\n", "import {\n    EMPTY_OBJECT,\n    IEqualsComparer,\n    IReactionDisposer,\n    IReactionPublic,\n    Lambda,\n    Reaction,\n    action,\n    comparer,\n    getNextId,\n    isAction,\n    isFunction,\n    isPlainObject,\n    die,\n    allowStateChanges\n} from \"../internal\"\n\nexport interface IAutorunOptions {\n    delay?: number\n    name?: string\n    /**\n     * Experimental.\n     * Warns if the view doesn't track observables\n     */\n    requiresObservable?: boolean\n    scheduler?: (callback: () => void) => any\n    onError?: (error: any) => void\n}\n\n/**\n * Creates a named reactive view and keeps it alive, so that the view is always\n * updated if one of the dependencies changes, even when the view is not further used by something else.\n * @param view The reactive view\n * @returns disposer function, which can be used to stop the view from being updated in the future.\n */\nexport function autorun(\n    view: (r: IReactionPublic) => any,\n    opts: IAutorunOptions = EMPTY_OBJECT\n): IReactionDisposer {\n    if (__DEV__) {\n        if (!isFunction(view)) die(\"<PERSON>run expects a function as first argument\")\n        if (isAction(view)) die(\"Autorun does not accept actions since actions are untrackable\")\n    }\n\n    const name: string =\n        opts?.name ?? (__DEV__ ? (view as any).name || \"Autorun@\" + getNextId() : \"Autorun\")\n    const runSync = !opts.scheduler && !opts.delay\n    let reaction: Reaction\n\n    if (runSync) {\n        // normal autorun\n        reaction = new Reaction(\n            name,\n            function (this: Reaction) {\n                this.track(reactionRunner)\n            },\n            opts.onError,\n            opts.requiresObservable\n        )\n    } else {\n        const scheduler = createSchedulerFromOptions(opts)\n        // debounced autorun\n        let isScheduled = false\n\n        reaction = new Reaction(\n            name,\n            () => {\n                if (!isScheduled) {\n                    isScheduled = true\n                    scheduler(() => {\n                        isScheduled = false\n                        if (!reaction.isDisposed_) reaction.track(reactionRunner)\n                    })\n                }\n            },\n            opts.onError,\n            opts.requiresObservable\n        )\n    }\n\n    function reactionRunner() {\n        view(reaction)\n    }\n\n    reaction.schedule_()\n    return reaction.getDisposer_()\n}\n\nexport type IReactionOptions = IAutorunOptions & {\n    fireImmediately?: boolean\n    equals?: IEqualsComparer<any>\n}\n\nconst run = (f: Lambda) => f()\n\nfunction createSchedulerFromOptions(opts: IReactionOptions) {\n    return opts.scheduler\n        ? opts.scheduler\n        : opts.delay\n        ? (f: Lambda) => setTimeout(f, opts.delay!)\n        : run\n}\n\nexport function reaction<T>(\n    expression: (r: IReactionPublic) => T,\n    effect: (arg: T, prev: T, r: IReactionPublic) => void,\n    opts: IReactionOptions = EMPTY_OBJECT\n): IReactionDisposer {\n    if (__DEV__) {\n        if (!isFunction(expression) || !isFunction(effect))\n            die(\"First and second argument to reaction should be functions\")\n        if (!isPlainObject(opts)) die(\"Third argument of reactions should be an object\")\n    }\n    const name = opts.name ?? (__DEV__ ? \"Reaction@\" + getNextId() : \"Reaction\")\n    const effectAction = action(\n        name,\n        opts.onError ? wrapErrorHandler(opts.onError, effect) : effect\n    )\n    const runSync = !opts.scheduler && !opts.delay\n    const scheduler = createSchedulerFromOptions(opts)\n\n    let firstTime = true\n    let isScheduled = false\n    let value: T\n    let oldValue: T = undefined as any // only an issue with fireImmediately\n\n    const equals = (opts as any).compareStructural\n        ? comparer.structural\n        : opts.equals || comparer.default\n\n    const r = new Reaction(\n        name,\n        () => {\n            if (firstTime || runSync) {\n                reactionRunner()\n            } else if (!isScheduled) {\n                isScheduled = true\n                scheduler!(reactionRunner)\n            }\n        },\n        opts.onError,\n        opts.requiresObservable\n    )\n\n    function reactionRunner() {\n        isScheduled = false\n        if (r.isDisposed_) return\n        let changed: boolean = false\n        r.track(() => {\n            const nextValue = allowStateChanges(false, () => expression(r))\n            changed = firstTime || !equals(value, nextValue)\n            oldValue = value\n            value = nextValue\n        })\n        if (firstTime && opts.fireImmediately!) effectAction(value, oldValue, r)\n        else if (!firstTime && changed) effectAction(value, oldValue, r)\n        firstTime = false\n    }\n\n    r.schedule_()\n    return r.getDisposer_()\n}\n\nfunction wrapErrorHandler(errorHandler, baseFn) {\n    return function () {\n        try {\n            return baseFn.apply(this, arguments)\n        } catch (e) {\n            errorHandler.call(this, e)\n        }\n    }\n}\n", "import {\n    IComputedValue,\n    IObservable,\n    IObservableArray,\n    Lambda,\n    ObservableMap,\n    getAtom,\n    ObservableSet,\n    isFunction,\n    IObservableValue\n} from \"../internal\"\n\nconst ON_BECOME_OBSERVED = \"onBO\"\nconst ON_BECOME_UNOBSERVED = \"onBUO\"\n\nexport function onBecomeObserved(\n    value:\n        | IObservable\n        | IComputedValue<any>\n        | IObservableArray<any>\n        | ObservableMap<any, any>\n        | ObservableSet<any>\n        | IObservableValue<any>,\n    listener: Lambda\n): Lambda\nexport function onBecomeObserved<K, V = any>(\n    value: ObservableMap<K, V> | Object,\n    property: K,\n    listener: Lambda\n): Lambda\nexport function onBecomeObserved(thing, arg2, arg3?): Lambda {\n    return interceptHook(ON_BECOME_OBSERVED, thing, arg2, arg3)\n}\n\nexport function onBecomeUnobserved(\n    value:\n        | IObservable\n        | IComputedValue<any>\n        | IObservableArray<any>\n        | ObservableMap<any, any>\n        | ObservableSet<any>\n        | IObservableValue<any>,\n    listener: Lambda\n): Lambda\nexport function onBecomeUnobserved<K, V = any>(\n    value: ObservableMap<K, V> | Object,\n    property: K,\n    listener: Lambda\n): Lambda\nexport function onBecomeUnobserved(thing, arg2, arg3?): Lambda {\n    return interceptHook(ON_BECOME_UNOBSERVED, thing, arg2, arg3)\n}\n\nfunction interceptHook(hook: \"onBO\" | \"onBUO\", thing, arg2, arg3) {\n    const atom: IObservable =\n        typeof arg3 === \"function\" ? getAtom(thing, arg2) : (getAtom(thing) as any)\n    const cb = isFunction(arg3) ? arg3 : arg2\n    const listenersKey = `${hook}L` as \"onBOL\" | \"onBUOL\"\n\n    if (atom[listenersKey]) {\n        atom[listenersKey]!.add(cb)\n    } else {\n        atom[listenersKey] = new Set<Lambda>([cb])\n    }\n\n    return function () {\n        const hookListeners = atom[listenersKey]\n        if (hookListeners) {\n            hookListeners.delete(cb)\n            if (hookListeners.size === 0) {\n                delete atom[listenersKey]\n            }\n        }\n    }\n}\n", "import { globalState, isolateGlobalState, setReactionScheduler } from \"../internal\"\n\nconst NEVER = \"never\"\nconst ALWAYS = \"always\"\nconst OBSERVED = \"observed\"\n// const IF_AVAILABLE = \"ifavailable\"\n\nexport function configure(options: {\n    enforceActions?: \"never\" | \"always\" | \"observed\"\n    computedRequiresReaction?: boolean\n    /**\n     * Warn if you try to create to derivation / reactive context without accessing any observable.\n     */\n    reactionRequiresObservable?: boolean\n    /**\n     * Warn if observables are accessed outside a reactive context\n     */\n    observableRequiresReaction?: boolean\n    isolateGlobalState?: boolean\n    disableErrorBoundaries?: boolean\n    safeDescriptors?: boolean\n    reactionScheduler?: (f: () => void) => void\n    useProxies?: \"always\" | \"never\" | \"ifavailable\"\n}): void {\n    if (options.isolateGlobalState === true) {\n        isolateGlobalState()\n    }\n    const { useProxies, enforceActions } = options\n    if (useProxies !== undefined) {\n        globalState.useProxies =\n            useProxies === ALWAYS\n                ? true\n                : useProxies === NEVER\n                ? false\n                : typeof Proxy !== \"undefined\"\n    }\n    if (useProxies === \"ifavailable\") globalState.verifyProxies = true\n    if (enforceActions !== undefined) {\n        const ea = enforceActions === ALWAYS ? ALWAYS : enforceActions === OBSERVED\n        globalState.enforceActions = ea\n        globalState.allowStateChanges = ea === true || ea === ALWAYS ? false : true\n    }\n    ;[\n        \"computedRequiresReaction\",\n        \"reactionRequiresObservable\",\n        \"observableRequiresReaction\",\n        \"disableErrorBoundaries\",\n        \"safeDescriptors\"\n    ].forEach(key => {\n        if (key in options) globalState[key] = !!options[key]\n    })\n    globalState.allowStateReads = !globalState.observableRequiresReaction\n    if (__DEV__ && globalState.disableErrorBoundaries === true) {\n        console.warn(\n            \"WARNING: Debug feature only. MobX will NOT recover from errors when `disableErrorBoundaries` is enabled.\"\n        )\n    }\n    if (options.reactionScheduler) {\n        setReactionScheduler(options.reactionScheduler)\n    }\n}\n", "import {\n    CreateObservableOptions,\n    isObservableMap,\n    AnnotationsMap,\n    startBatch,\n    endBatch,\n    asObservableObject,\n    isPlainObject,\n    ObservableObjectAdministration,\n    isObservable,\n    die,\n    getOwnPropertyDescriptors,\n    $mobx,\n    ownKeys\n} from \"../internal\"\n\nexport function extendObservable<A extends Object, B extends Object>(\n    target: A,\n    properties: B,\n    annotations?: AnnotationsMap<B, never>,\n    options?: CreateObservableOptions\n): A & B {\n    if (__DEV__) {\n        if (arguments.length > 4) die(\"'extendObservable' expected 2-4 arguments\")\n        if (typeof target !== \"object\")\n            die(\"'extendObservable' expects an object as first argument\")\n        if (isObservableMap(target))\n            die(\"'extendObservable' should not be used on maps, use map.merge instead\")\n        if (!isPlainObject(properties))\n            die(`'extendObservabe' only accepts plain objects as second argument`)\n        if (isObservable(properties) || isObservable(annotations))\n            die(`Extending an object with another observable (object) is not supported`)\n    }\n    // Pull descriptors first, so we don't have to deal with props added by administration ($mobx)\n    const descriptors = getOwnPropertyDescriptors(properties)\n\n    const adm: ObservableObjectAdministration = asObservableObject(target, options)[$mobx]\n    startBatch()\n    try {\n        ownKeys(descriptors).forEach(key => {\n            adm.extend_(\n                key,\n                descriptors[key as any],\n                // must pass \"undefined\" for { key: undefined }\n                !annotations ? true : key in annotations ? annotations[key] : true\n            )\n        })\n    } finally {\n        endBatch()\n    }\n    return target as any\n}\n", "import { IDep<PERSON><PERSON>N<PERSON>, get<PERSON>tom, getObservers, hasObservers } from \"../internal\"\n\nexport interface IDependencyTree {\n    name: string\n    dependencies?: IDependencyTree[]\n}\n\nexport interface IObserverTree {\n    name: string\n    observers?: IObserverTree[]\n}\n\nexport function getDependencyTree(thing: any, property?: string): IDependencyTree {\n    return nodeToDependencyTree(getAtom(thing, property))\n}\n\nfunction nodeToDependencyTree(node: IDepTreeNode): IDependencyTree {\n    const result: IDependencyTree = {\n        name: node.name_\n    }\n    if (node.observing_ && node.observing_.length > 0)\n        result.dependencies = unique(node.observing_).map(nodeToDependencyTree)\n    return result\n}\n\nexport function getObserverTree(thing: any, property?: string): IObserverTree {\n    return nodeToObserverTree(getAtom(thing, property))\n}\n\nfunction nodeToObserverTree(node: IDepTreeNode): IObserverTree {\n    const result: IObserverTree = {\n        name: node.name_\n    }\n    if (hasObservers(node as any))\n        result.observers = Array.from(<any>getObservers(node as any)).map(<any>nodeToObserverTree)\n    return result\n}\n\nfunction unique<T>(list: T[]): T[] {\n    return Array.from(new Set(list))\n}\n", "import {\n    action,\n    noop,\n    die,\n    isFunction,\n    Annotation,\n    isStringish,\n    storeAnnotation,\n    createFlowAnnotation,\n    createDecoratorAnnotation\n} from \"../internal\"\n\nexport const FLOW = \"flow\"\n\nlet generatorId = 0\n\nexport function FlowCancellationError() {\n    this.message = \"FLOW_CANCELLED\"\n}\nFlowCancellationError.prototype = Object.create(Error.prototype)\n\nexport function isFlowCancellationError(error: Error) {\n    return error instanceof FlowCancellationError\n}\n\nexport type CancellablePromise<T> = Promise<T> & { cancel(): void }\n\ninterface Flow extends Annotation, PropertyDecorator {\n    <R, Args extends any[]>(\n        generator: (...args: Args) => Generator<any, R, any> | AsyncGenerator<any, R, any>\n    ): (...args: Args) => CancellablePromise<R>\n    bound: Annotation & PropertyDecorator\n}\n\nconst flowAnnotation = createFlowAnnotation(\"flow\")\nconst flowBoundAnnotation = createFlowAnnotation(\"flow.bound\", { bound: true })\n\nexport const flow: Flow = Object.assign(\n    function flow(arg1, arg2?) {\n        // @flow\n        if (isStringish(arg2)) {\n            return storeAnnotation(arg1, arg2, flowAnnotation)\n        }\n        // flow(fn)\n        if (__DEV__ && arguments.length !== 1)\n            die(`Flow expects single argument with generator function`)\n        const generator = arg1\n        const name = generator.name || \"<unnamed flow>\"\n\n        // Implementation based on https://github.com/tj/co/blob/master/index.js\n        const res = function () {\n            const ctx = this\n            const args = arguments\n            const runId = ++generatorId\n            const gen = action(`${name} - runid: ${runId} - init`, generator).apply(ctx, args)\n            let rejector: (error: any) => void\n            let pendingPromise: CancellablePromise<any> | undefined = undefined\n\n            const promise = new Promise(function (resolve, reject) {\n                let stepId = 0\n                rejector = reject\n\n                function onFulfilled(res: any) {\n                    pendingPromise = undefined\n                    let ret\n                    try {\n                        ret = action(\n                            `${name} - runid: ${runId} - yield ${stepId++}`,\n                            gen.next\n                        ).call(gen, res)\n                    } catch (e) {\n                        return reject(e)\n                    }\n\n                    next(ret)\n                }\n\n                function onRejected(err: any) {\n                    pendingPromise = undefined\n                    let ret\n                    try {\n                        ret = action(\n                            `${name} - runid: ${runId} - yield ${stepId++}`,\n                            gen.throw!\n                        ).call(gen, err)\n                    } catch (e) {\n                        return reject(e)\n                    }\n                    next(ret)\n                }\n\n                function next(ret: any) {\n                    if (isFunction(ret?.then)) {\n                        // an async iterator\n                        ret.then(next, reject)\n                        return\n                    }\n                    if (ret.done) return resolve(ret.value)\n                    pendingPromise = Promise.resolve(ret.value) as any\n                    return pendingPromise!.then(onFulfilled, onRejected)\n                }\n\n                onFulfilled(undefined) // kick off the process\n            }) as any\n\n            promise.cancel = action(`${name} - runid: ${runId} - cancel`, function () {\n                try {\n                    if (pendingPromise) cancelPromise(pendingPromise)\n                    // Finally block can return (or yield) stuff..\n                    const res = gen.return!(undefined as any)\n                    // eat anything that promise would do, it's cancelled!\n                    const yieldedPromise = Promise.resolve(res.value)\n                    yieldedPromise.then(noop, noop)\n                    cancelPromise(yieldedPromise) // maybe it can be cancelled :)\n                    // reject our original promise\n                    rejector(new FlowCancellationError())\n                } catch (e) {\n                    rejector(e) // there could be a throwing finally block\n                }\n            })\n            return promise\n        }\n        res.isMobXFlow = true\n        return res\n    } as any,\n    flowAnnotation\n)\n\nflow.bound = createDecoratorAnnotation(flowBoundAnnotation)\n\nfunction cancelPromise(promise) {\n    if (isFunction(promise.cancel)) promise.cancel()\n}\n\nexport function flowResult<T>(\n    result: T\n): T extends Generator<any, infer R, any>\n    ? CancellablePromise<R>\n    : T extends CancellablePromise<any>\n    ? T\n    : never {\n    return result as any // just tricking TypeScript :)\n}\n\nexport function isFlow(fn: any): boolean {\n    return fn?.isMobXFlow === true\n}\n", "import {\n    IObservableArray,\n    IObservableValue,\n    Lambda,\n    ObservableMap,\n    getAdministration,\n    isObservableArray,\n    isObservableMap,\n    isObservableObject,\n    isObservableValue,\n    ObservableSet,\n    die,\n    isStringish\n} from \"../internal\"\n\nexport type ReadInterceptor<T> = (value: any) => T\n\n/** Experimental feature right now, tested indirectly via Mobx-State-Tree */\nexport function interceptReads<T>(value: IObservableValue<T>, handler: ReadInterceptor<T>): Lambda\nexport function interceptReads<T>(\n    observableArray: IObservableArray<T>,\n    handler: ReadInterceptor<T>\n): Lambda\nexport function interceptReads<K, V>(\n    observableMap: ObservableMap<K, V>,\n    handler: ReadInterceptor<V>\n): Lambda\nexport function interceptReads<V>(\n    observableSet: ObservableSet<V>,\n    handler: ReadInterceptor<V>\n): Lambda\nexport function interceptReads(\n    object: Object,\n    property: string,\n    handler: ReadInterceptor<any>\n): Lambda\nexport function interceptReads(thing, propOrHandler?, handler?): Lambda {\n    let target\n    if (isObservableMap(thing) || isObservableArray(thing) || isObservableValue(thing)) {\n        target = getAdministration(thing)\n    } else if (isObservableObject(thing)) {\n        if (__DEV__ && !isStringish(propOrHandler))\n            return die(\n                `InterceptReads can only be used with a specific property, not with an object in general`\n            )\n        target = getAdministration(thing, propOrHandler)\n    } else if (__DEV__) {\n        return die(`Expected observable map, object or array as first array`)\n    }\n    if (__DEV__ && target.dehancer !== undefined)\n        return die(`An intercept reader was already established`)\n    target.dehancer = typeof propOrHandler === \"function\" ? propOrHandler : handler\n    return () => {\n        target.dehancer = undefined\n    }\n}\n", "import {\n    IArrayWillChange,\n    IArrayWillSplice,\n    IInterceptor,\n    IMapWillChange,\n    IObjectWillChange,\n    IObservableArray,\n    IObservableValue,\n    IValueWillChange,\n    Lambda,\n    ObservableMap,\n    getAdministration,\n    ObservableSet,\n    ISetWillChange,\n    isFunction\n} from \"../internal\"\n\nexport function intercept<T>(\n    value: IObservableValue<T>,\n    handler: IInterceptor<IValueWillChange<T>>\n): Lambda\nexport function intercept<T>(\n    observableArray: IObservableArray<T>,\n    handler: IInterceptor<IArrayWillChange<T> | IArrayWillSplice<T>>\n): Lambda\nexport function intercept<K, V>(\n    observableMap: ObservableMap<K, V>,\n    handler: IInterceptor<IMapWillChange<K, V>>\n): Lambda\nexport function intercept<V>(\n    observableMap: ObservableSet<V>,\n    handler: IInterceptor<ISetWillChange<V>>\n): Lambda\nexport function intercept<K, V>(\n    observableMap: ObservableMap<K, V>,\n    property: K,\n    handler: IInterceptor<IValueWillChange<V>>\n): Lambda\nexport function intercept(object: object, handler: IInterceptor<IObjectWillChange>): Lambda\nexport function intercept<T extends object, K extends keyof T>(\n    object: T,\n    property: K,\n    handler: IInterceptor<IValueWillChange<any>>\n): Lambda\nexport function intercept(thing, propOrHandler?, handler?): Lambda {\n    if (isFunction(handler)) return interceptProperty(thing, propOrHandler, handler)\n    else return interceptInterceptable(thing, propOrHandler)\n}\n\nfunction interceptInterceptable(thing, handler) {\n    return getAdministration(thing).intercept_(handler)\n}\n\nfunction interceptProperty(thing, property, handler) {\n    return getAdministration(thing, property).intercept_(handler)\n}\n", "import { $mobx, getAtom, isComputedValue, isObservableObject, die, isStringish } from \"../internal\"\n\nexport function _isComputed(value, property?: PropertyKey): boolean {\n    if (property !== undefined) {\n        if (isObservableObject(value) === false) return false\n        if (!value[$mobx].values_.has(property)) return false\n        const atom = getAtom(value, property)\n        return isComputedValue(atom)\n    }\n    return isComputedValue(value)\n}\n\nexport function isComputed(value: any): boolean {\n    if (__DEV__ && arguments.length > 1)\n        return die(\n            `isComputed expects only 1 argument. Use isComputedProp to inspect the observability of a property`\n        )\n    return _isComputed(value)\n}\n\nexport function isComputedProp(value: any, propName: PropertyKey): boolean {\n    if (__DEV__ && !isStringish(propName))\n        return die(`isComputed expected a property name as second argument`)\n    return _isComputed(value, propName)\n}\n", "import {\n    $mobx,\n    isAtom,\n    isComputedValue,\n    isObservableArray,\n    isObservableMap,\n    isObservableObject,\n    isReaction,\n    die,\n    isStringish\n} from \"../internal\"\n\nfunction _isObservable(value, property?: PropertyKey): boolean {\n    if (!value) return false\n    if (property !== undefined) {\n        if (__DEV__ && (isObservableMap(value) || isObservableArray(value)))\n            return die(\n                \"isObservable(object, propertyName) is not supported for arrays and maps. Use map.has or array.length instead.\"\n            )\n        if (isObservableObject(value)) {\n            return value[$mobx].values_.has(property)\n        }\n        return false\n    }\n    // For first check, see #701\n    return (\n        isObservableObject(value) ||\n        !!value[$mobx] ||\n        isAtom(value) ||\n        isReaction(value) ||\n        isComputedValue(value)\n    )\n}\n\nexport function isObservable(value: any): boolean {\n    if (__DEV__ && arguments.length !== 1)\n        die(\n            `isObservable expects only 1 argument. Use isObservableProp to inspect the observability of a property`\n        )\n    return _isObservable(value)\n}\n\nexport function isObservableProp(value: any, propName: PropertyKey): boolean {\n    if (__DEV__ && !isStringish(propName)) return die(`expected a property name as second argument`)\n    return _isObservable(value, propName)\n}\n", "import {\n    $mobx,\n    IIsObservableObject,\n    IObservableArray,\n    ObservableMap,\n    ObservableSet,\n    ObservableObjectAdministration,\n    endBatch,\n    isObservableArray,\n    isObservableMap,\n    isObservableSet,\n    isObservableObject,\n    startBatch,\n    die\n} from \"../internal\"\n\nexport function keys<K>(map: ObservableMap<K, any>): ReadonlyArray<K>\nexport function keys<T>(ar: IObservableArray<T>): ReadonlyArray<number>\nexport function keys<T>(set: ObservableSet<T>): ReadonlyArray<T>\nexport function keys<T extends Object>(obj: T): ReadonlyArray<PropertyKey>\nexport function keys(obj: any): any {\n    if (isObservableObject(obj)) {\n        return (((obj as any) as IIsObservableObject)[\n            $mobx\n        ] as ObservableObjectAdministration).keys_()\n    }\n    if (isObservableMap(obj) || isObservableSet(obj)) {\n        return Array.from(obj.keys())\n    }\n    if (isObservableArray(obj)) {\n        return obj.map((_, index) => index)\n    }\n    die(5)\n}\n\nexport function values<K, T>(map: ObservableMap<K, T>): ReadonlyArray<T>\nexport function values<T>(set: ObservableSet<T>): ReadonlyArray<T>\nexport function values<T>(ar: IObservableArray<T>): ReadonlyArray<T>\nexport function values<T = any>(obj: T): ReadonlyArray<T extends object ? T[keyof T] : any>\nexport function values(obj: any): string[] {\n    if (isObservableObject(obj)) {\n        return keys(obj).map(key => obj[key])\n    }\n    if (isObservableMap(obj)) {\n        return keys(obj).map(key => obj.get(key))\n    }\n    if (isObservableSet(obj)) {\n        return Array.from(obj.values())\n    }\n    if (isObservableArray(obj)) {\n        return obj.slice()\n    }\n    die(6)\n}\n\nexport function entries<K, T>(map: ObservableMap<K, T>): ReadonlyArray<[K, T]>\nexport function entries<T>(set: ObservableSet<T>): ReadonlyArray<[T, T]>\nexport function entries<T>(ar: IObservableArray<T>): ReadonlyArray<[number, T]>\nexport function entries<T = any>(\n    obj: T\n): ReadonlyArray<[string, T extends object ? T[keyof T] : any]>\nexport function entries(obj: any): any {\n    if (isObservableObject(obj)) {\n        return keys(obj).map(key => [key, obj[key]])\n    }\n    if (isObservableMap(obj)) {\n        return keys(obj).map(key => [key, obj.get(key)])\n    }\n    if (isObservableSet(obj)) {\n        return Array.from(obj.entries())\n    }\n    if (isObservableArray(obj)) {\n        return obj.map((key, index) => [index, key])\n    }\n    die(7)\n}\n\nexport function set<V>(obj: ObservableMap<PropertyKey, V>, values: { [key: string]: V })\nexport function set<K, V>(obj: ObservableMap<K, V>, key: K, value: V)\nexport function set<T>(obj: ObservableSet<T>, value: T)\nexport function set<T>(obj: IObservableArray<T>, index: number, value: T)\nexport function set<T extends Object>(obj: T, values: { [key: string]: any })\nexport function set<T extends Object>(obj: T, key: PropertyKey, value: any)\nexport function set(obj: any, key: any, value?: any): void {\n    if (arguments.length === 2 && !isObservableSet(obj)) {\n        startBatch()\n        const values = key\n        try {\n            for (let key in values) set(obj, key, values[key])\n        } finally {\n            endBatch()\n        }\n        return\n    }\n    if (isObservableObject(obj)) {\n        ;((obj as any) as IIsObservableObject)[$mobx].set_(key, value)\n    } else if (isObservableMap(obj)) {\n        obj.set(key, value)\n    } else if (isObservableSet(obj)) {\n        obj.add(key)\n    } else if (isObservableArray(obj)) {\n        if (typeof key !== \"number\") key = parseInt(key, 10)\n        if (key < 0) die(`Invalid index: '${key}'`)\n        startBatch()\n        if (key >= obj.length) obj.length = key + 1\n        obj[key] = value\n        endBatch()\n    } else die(8)\n}\n\nexport function remove<K, V>(obj: ObservableMap<K, V>, key: K)\nexport function remove<T>(obj: ObservableSet<T>, key: T)\nexport function remove<T>(obj: IObservableArray<T>, index: number)\nexport function remove<T extends Object>(obj: T, key: string)\nexport function remove(obj: any, key: any): void {\n    if (isObservableObject(obj)) {\n        ;((obj as any) as IIsObservableObject)[$mobx].delete_(key)\n    } else if (isObservableMap(obj)) {\n        obj.delete(key)\n    } else if (isObservableSet(obj)) {\n        obj.delete(key)\n    } else if (isObservableArray(obj)) {\n        if (typeof key !== \"number\") key = parseInt(key, 10)\n        obj.splice(key, 1)\n    } else {\n        die(9)\n    }\n}\n\nexport function has<K>(obj: ObservableMap<K, any>, key: K): boolean\nexport function has<T>(obj: ObservableSet<T>, key: T): boolean\nexport function has<T>(obj: IObservableArray<T>, index: number): boolean\nexport function has<T extends Object>(obj: T, key: string): boolean\nexport function has(obj: any, key: any): boolean {\n    if (isObservableObject(obj)) {\n        return ((obj as any) as IIsObservableObject)[$mobx].has_(key)\n    } else if (isObservableMap(obj)) {\n        return obj.has(key)\n    } else if (isObservableSet(obj)) {\n        return obj.has(key)\n    } else if (isObservableArray(obj)) {\n        return key >= 0 && key < obj.length\n    }\n    die(10)\n}\n\nexport function get<K, V>(obj: ObservableMap<K, V>, key: K): V | undefined\nexport function get<T>(obj: IObservableArray<T>, index: number): T | undefined\nexport function get<T extends Object>(obj: T, key: string): any\nexport function get(obj: any, key: any): any {\n    if (!has(obj, key)) return undefined\n    if (isObservableObject(obj)) {\n        return ((obj as any) as IIsObservableObject)[$mobx].get_(key)\n    } else if (isObservableMap(obj)) {\n        return obj.get(key)\n    } else if (isObservableArray(obj)) {\n        return obj[key]\n    }\n    die(11)\n}\n\nexport function apiDefineProperty(obj: Object, key: PropertyKey, descriptor: PropertyDescriptor) {\n    if (isObservableObject(obj)) {\n        return ((obj as any) as IIsObservableObject)[$mobx].defineProperty_(key, descriptor)\n    }\n    die(39)\n}\n\nexport function apiOwnKeys(obj: Object) {\n    if (isObservableObject(obj)) {\n        return ((obj as any) as IIsObservableObject)[$mobx].ownKeys_()\n    }\n    die(38)\n}\n", "import {\n    IArrayDidChange,\n    IComputedValue,\n    IMapDidChange,\n    IObjectDidChange,\n    IObservableArray,\n    IObservableValue,\n    IValueDidChange,\n    Lambda,\n    ObservableMap,\n    getAdministration,\n    ObservableSet,\n    ISetDidChange,\n    isFunction\n} from \"../internal\"\n\nexport function observe<T>(\n    value: IObservableValue<T> | IComputedValue<T>,\n    listener: (change: IValueDidChange<T>) => void,\n    fireImmediately?: boolean\n): Lambda\nexport function observe<T>(\n    observableArray: IObservableArray<T>,\n    listener: (change: IArrayDidChange<T>) => void,\n    fireImmediately?: boolean\n): Lambda\nexport function observe<V>(\n    observableMap: ObservableSet<V>,\n    listener: (change: ISetDidChange<V>) => void,\n    fireImmediately?: boolean\n): Lambda\nexport function observe<K, V>(\n    observableMap: ObservableMap<K, V>,\n    listener: (change: IMapDidChange<K, V>) => void,\n    fireImmediately?: boolean\n): Lambda\nexport function observe<K, V>(\n    observableMap: ObservableMap<K, V>,\n    property: K,\n    listener: (change: IValueDidChange<V>) => void,\n    fireImmediately?: boolean\n): Lambda\nexport function observe(\n    object: Object,\n    listener: (change: IObjectDidChange) => void,\n    fireImmediately?: boolean\n): Lambda\nexport function observe<T, K extends keyof T>(\n    object: T,\n    property: K,\n    listener: (change: IValueDidChange<T[K]>) => void,\n    fireImmediately?: boolean\n): Lambda\nexport function observe(thing, propOrCb?, cbOrFire?, fireImmediately?): Lambda {\n    if (isFunction(cbOrFire))\n        return observeObservableProperty(thing, propOrCb, cbOrFire, fireImmediately)\n    else return observeObservable(thing, propOrCb, cbOrFire)\n}\n\nfunction observeObservable(thing, listener, fireImmediately: boolean) {\n    return getAdministration(thing).observe_(listener, fireImmediately)\n}\n\nfunction observeObservableProperty(thing, property, listener, fireImmediately: boolean) {\n    return getAdministration(thing, property).observe_(listener, fireImmediately)\n}\n", "import {\n    isObservable,\n    isObservableArray,\n    isObservableValue,\n    isObservableMap,\n    isObservableSet,\n    isComputedValue,\n    die,\n    apiOwnKeys,\n    objectPrototype\n} from \"../internal\"\n\nfunction cache<K, V>(map: Map<any, any>, key: K, value: V): V {\n    map.set(key, value)\n    return value\n}\n\nfunction toJSHelper(source, __alreadySeen: Map<any, any>) {\n    if (\n        source == null ||\n        typeof source !== \"object\" ||\n        source instanceof Date ||\n        !isObservable(source)\n    )\n        return source\n\n    if (isObservableValue(source) || isComputedValue(source))\n        return toJSHelper(source.get(), __alreadySeen)\n    if (__alreadySeen.has(source)) {\n        return __alreadySeen.get(source)\n    }\n    if (isObservableArray(source)) {\n        const res = cache(__alreadySeen, source, new Array(source.length))\n        source.forEach((value, idx) => {\n            res[idx] = toJSHelper(value, __alreadySeen)\n        })\n        return res\n    }\n    if (isObservableSet(source)) {\n        const res = cache(__alreadySeen, source, new Set())\n        source.forEach(value => {\n            res.add(toJSHelper(value, __alreadySeen))\n        })\n        return res\n    }\n    if (isObservableMap(source)) {\n        const res = cache(__alreadySeen, source, new Map())\n        source.forEach((value, key) => {\n            res.set(key, toJSHelper(value, __alreadySeen))\n        })\n        return res\n    } else {\n        // must be observable object\n        const res = cache(__alreadySeen, source, {})\n        apiOwnKeys(source).forEach((key: any) => {\n            if (objectPrototype.propertyIsEnumerable.call(source, key)) {\n                res[key] = toJSHelper(source[key], __alreadySeen)\n            }\n        })\n        return res\n    }\n}\n\n/**\n * Basically, a deep clone, so that no reactive property will exist anymore.\n */\nexport function toJS<T>(source: T, options?: any): T {\n    if (__DEV__ && options) die(\"toJS no longer supports options\")\n    return toJSHelper(source, new Map())\n}\n", "import { TraceMode, die, getAtom, globalState } from \"../internal\"\n\nexport function trace(thing?: any, prop?: string, enterBreakPoint?: boolean): void\nexport function trace(thing?: any, enterBreakPoint?: boolean): void\nexport function trace(enterBreakPoint?: boolean): void\nexport function trace(...args: any[]): void {\n    if (!__DEV__) die(`trace() is not available in production builds`)\n    let enterBreakPoint = false\n    if (typeof args[args.length - 1] === \"boolean\") enterBreakPoint = args.pop()\n    const derivation = getAtomFromArgs(args)\n    if (!derivation) {\n        return die(\n            `'trace(break?)' can only be used inside a tracked computed value or a Reaction. Consider passing in the computed value or reaction explicitly`\n        )\n    }\n    if (derivation.isTracing_ === TraceMode.NONE) {\n        console.log(`[mobx.trace] '${derivation.name_}' tracing enabled`)\n    }\n    derivation.isTracing_ = enterBreakPoint ? TraceMode.BREAK : TraceMode.LOG\n}\n\nfunction getAtomFromArgs(args): any {\n    switch (args.length) {\n        case 0:\n            return globalState.trackingDerivation\n        case 1:\n            return getAtom(args[0])\n        case 2:\n            return getAtom(args[0], args[1])\n    }\n}\n", "import { endBatch, startBatch } from \"../internal\"\n\n/**\n * During a transaction no views are updated until the end of the transaction.\n * The transaction will be run synchronously nonetheless.\n *\n * @param action a function that updates some reactive state\n * @returns any value that was returned by the 'action' parameter.\n */\nexport function transaction<T>(action: () => T, thisArg = undefined): T {\n    startBatch()\n    try {\n        return action.apply(thisArg)\n    } finally {\n        endBatch()\n    }\n}\n", "import {\n    $mobx,\n    IReactionDisposer,\n    Lamb<PERSON>,\n    autorun,\n    createAction,\n    getNextId,\n    die,\n    allowStateChanges\n} from \"../internal\"\n\nexport interface IWhenOptions {\n    name?: string\n    timeout?: number\n    onError?: (error: any) => void\n}\n\nexport function when(\n    predicate: () => boolean,\n    opts?: IWhenOptions\n): Promise<void> & { cancel(): void }\nexport function when(\n    predicate: () => boolean,\n    effect: Lambda,\n    opts?: IWhenOptions\n): IReactionDisposer\nexport function when(predicate: any, arg1?: any, arg2?: any): any {\n    if (arguments.length === 1 || (arg1 && typeof arg1 === \"object\"))\n        return whenPromise(predicate, arg1)\n    return _when(predicate, arg1, arg2 || {})\n}\n\nfunction _when(predicate: () => boolean, effect: Lambda, opts: IWhenOptions): IReactionDisposer {\n    let timeoutHandle: any\n    if (typeof opts.timeout === \"number\") {\n        timeoutHandle = setTimeout(() => {\n            if (!disposer[$mobx].isDisposed_) {\n                disposer()\n                const error = new Error(\"WHEN_TIMEOUT\")\n                if (opts.onError) opts.onError(error)\n                else throw error\n            }\n        }, opts.timeout)\n    }\n\n    opts.name = __DEV__ ? opts.name || \"When@\" + getNextId() : \"When\"\n    const effectAction = createAction(\n        __DEV__ ? opts.name + \"-effect\" : \"When-effect\",\n        effect as Function\n    )\n    // eslint-disable-next-line\n    var disposer = autorun(r => {\n        // predicate should not change state\n        let cond = allowStateChanges(false, predicate)\n        if (cond) {\n            r.dispose()\n            if (timeoutHandle) clearTimeout(timeoutHandle)\n            effectAction()\n        }\n    }, opts)\n    return disposer\n}\n\nfunction whenPromise(\n    predicate: () => boolean,\n    opts?: IWhenOptions\n): Promise<void> & { cancel(): void } {\n    if (__DEV__ && opts && opts.onError)\n        return die(`the options 'onError' and 'promise' cannot be combined`)\n    let cancel\n    const res = new Promise((resolve, reject) => {\n        let disposer = _when(predicate, resolve, { ...opts, onError: reject })\n        cancel = () => {\n            disposer()\n            reject(\"WHEN_CANCELLED\")\n        }\n    })\n    ;(res as any).cancel = cancel\n    return res as any\n}\n", "import {\n    $mobx,\n    IIsObservableObject,\n    ObservableObjectAdministration,\n    warnAboutProxyRequirement,\n    assertProxies,\n    die,\n    isStringish,\n    globalState,\n    CreateObservableOptions,\n    asObservableObject\n} from \"../internal\"\n\nfunction getAdm(target): ObservableObjectAdministration {\n    return target[$mobx]\n}\n\n// Optimization: we don't need the intermediate objects and could have a completely custom administration for DynamicObjects,\n// and skip either the internal values map, or the base object with its property descriptors!\nconst objectProxyTraps: ProxyHandler<any> = {\n    has(target: IIsObservableObject, name: PropertyKey): boolean {\n        if (__DEV__ && globalState.trackingDerivation)\n            warnAboutProxyRequirement(\n                \"detect new properties using the 'in' operator. Use 'has' from 'mobx' instead.\"\n            )\n        return getAdm(target).has_(name)\n    },\n    get(target: IIsObservableObject, name: PropertyKey): any {\n        return getAdm(target).get_(name)\n    },\n    set(target: IIsObservableObject, name: Property<PERSON>ey, value: any): boolean {\n        if (!isStringish(name)) return false\n        if (__DEV__ && !getAdm(target).values_.has(name)) {\n            warnAboutProxyRequirement(\n                \"add a new observable property through direct assignment. Use 'set' from 'mobx' instead.\"\n            )\n        }\n        // null (intercepted) -> true (success)\n        return getAdm(target).set_(name, value, true) ?? true\n    },\n    deleteProperty(target: IIsObservableObject, name: PropertyKey): boolean {\n        if (__DEV__) {\n            warnAboutProxyRequirement(\n                \"delete properties from an observable object. Use 'remove' from 'mobx' instead.\"\n            )\n        }\n        if (!isStringish(name)) return false\n        // null (intercepted) -> true (success)\n        return getAdm(target).delete_(name, true) ?? true\n    },\n    defineProperty(\n        target: IIsObservableObject,\n        name: PropertyKey,\n        descriptor: PropertyDescriptor\n    ): boolean {\n        if (__DEV__) {\n            warnAboutProxyRequirement(\n                \"define property on an observable object. Use 'defineProperty' from 'mobx' instead.\"\n            )\n        }\n        // null (intercepted) -> true (success)\n        return getAdm(target).defineProperty_(name, descriptor) ?? true\n    },\n    ownKeys(target: IIsObservableObject): PropertyKey[] {\n        if (__DEV__ && globalState.trackingDerivation)\n            warnAboutProxyRequirement(\n                \"iterate keys to detect added / removed properties. Use 'keys' from 'mobx' instead.\"\n            )\n        return getAdm(target).ownKeys_()\n    },\n    preventExtensions(target) {\n        die(13)\n    }\n}\n\nexport function asDynamicObservableObject(\n    target: any,\n    options?: CreateObservableOptions\n): IIsObservableObject {\n    assertProxies()\n    target = asObservableObject(target, options)\n    return (target[$mobx].proxy_ ??= new Proxy(target, objectProxyTraps))\n}\n", "import { Lambda, once, untrackedEnd, untrackedStart, die } from \"../internal\"\n\nexport type IInterceptor<T> = (change: T) => T | null\n\nexport interface IInterceptable<T> {\n    interceptors_: IInterceptor<T>[] | undefined\n}\n\nexport function hasInterceptors(interceptable: IInterceptable<any>) {\n    return interceptable.interceptors_ !== undefined && interceptable.interceptors_.length > 0\n}\n\nexport function registerInterceptor<T>(\n    interceptable: IInterceptable<T>,\n    handler: IInterceptor<T>\n): Lambda {\n    const interceptors = interceptable.interceptors_ || (interceptable.interceptors_ = [])\n    interceptors.push(handler)\n    return once(() => {\n        const idx = interceptors.indexOf(handler)\n        if (idx !== -1) interceptors.splice(idx, 1)\n    })\n}\n\nexport function interceptChange<T>(\n    interceptable: IInterceptable<T | null>,\n    change: T | null\n): T | null {\n    const prevU = untrackedStart()\n    try {\n        // Interceptor can modify the array, copy it to avoid concurrent modification, see #1950\n        const interceptors = [...(interceptable.interceptors_ || [])]\n        for (let i = 0, l = interceptors.length; i < l; i++) {\n            change = interceptors[i](change)\n            if (change && !(change as any).type) die(14)\n            if (!change) break\n        }\n        return change\n    } finally {\n        untrackedEnd(prevU)\n    }\n}\n", "import { Lambda, once, untrackedEnd, untrackedStart } from \"../internal\"\n\nexport interface IListenable {\n    changeListeners_: Function[] | undefined\n}\n\nexport function hasListeners(listenable: IListenable) {\n    return listenable.changeListeners_ !== undefined && listenable.changeListeners_.length > 0\n}\n\nexport function registerListener(listenable: IListenable, handler: Function): Lambda {\n    const listeners = listenable.changeListeners_ || (listenable.changeListeners_ = [])\n    listeners.push(handler)\n    return once(() => {\n        const idx = listeners.indexOf(handler)\n        if (idx !== -1) listeners.splice(idx, 1)\n    })\n}\n\nexport function notifyListeners<T>(listenable: IListenable, change: T) {\n    const prevU = untrackedStart()\n    let listeners = listenable.changeListeners_\n    if (!listeners) return\n    listeners = listeners.slice()\n    for (let i = 0, l = listeners.length; i < l; i++) {\n        listeners[i](change)\n    }\n    untrackedEnd(prevU)\n}\n", "import {\n    $mobx,\n    asObservableObject,\n    AnnotationsMap,\n    endBatch,\n    startBatch,\n    CreateObservableOptions,\n    ObservableObjectAdministration,\n    collectStoredAnnotations,\n    isPlainObject,\n    isObservableObject,\n    die,\n    ownKeys,\n    extendObservable,\n    addHiddenProp\n} from \"../internal\"\n\n// Hack based on https://github.com/Microsoft/TypeScript/issues/14829#issuecomment-322267089\n// We need this, because otherwise, AdditionalKeys is going to be inferred to be any\n// set of superfluous keys. But, we rather want to get a compile error unless AdditionalKeys is\n// _explicity_ passed as generic argument\n// Fixes: https://github.com/mobxjs/mobx/issues/2325#issuecomment-691070022\ntype NoInfer<T> = [T][T extends any ? 0 : never]\n\nexport function makeObservable<T extends object, AdditionalKeys extends PropertyKey = never>(\n    target: T,\n    annotations?: AnnotationsMap<T, NoInfer<AdditionalKeys>>,\n    options?: CreateObservableOptions\n): T {\n    const adm: ObservableObjectAdministration = asObservableObject(target, options)[$mobx]\n    startBatch()\n    try {\n        // Default to decorators\n        annotations ??= collectStoredAnnotations(target)\n\n        // Annotate\n        ownKeys(annotations).forEach(key => adm.make_(key, annotations![key]))\n    } finally {\n        endBatch()\n    }\n    return target\n}\n\n// proto[keysSymbol] = new Set<PropertyKey>()\nconst keysSymbol = Symbol(\"mobx-keys\")\n\nexport function makeAutoObservable<T extends object, AdditionalKeys extends PropertyKey = never>(\n    target: T,\n    overrides?: AnnotationsMap<T, NoInfer<AdditionalKeys>>,\n    options?: CreateObservableOptions\n): T {\n    if (__DEV__) {\n        if (!isPlainObject(target) && !isPlainObject(Object.getPrototypeOf(target)))\n            die(`'makeAutoObservable' can only be used for classes that don't have a superclass`)\n        if (isObservableObject(target))\n            die(`makeAutoObservable can only be used on objects not already made observable`)\n    }\n\n    // Optimization: avoid visiting protos\n    // Assumes that annotation.make_/.extend_ works the same for plain objects\n    if (isPlainObject(target)) {\n        return extendObservable(target, target, overrides, options)\n    }\n\n    const adm: ObservableObjectAdministration = asObservableObject(target, options)[$mobx]\n\n    // Optimization: cache keys on proto\n    // Assumes makeAutoObservable can be called only once per object and can't be used in subclass\n    if (!target[keysSymbol]) {\n        const proto = Object.getPrototypeOf(target)\n        const keys = new Set([...ownKeys(target), ...ownKeys(proto)])\n        keys.delete(\"constructor\")\n        keys.delete($mobx)\n        addHiddenProp(proto, keysSymbol, keys)\n    }\n\n    startBatch()\n    try {\n        target[keysSymbol].forEach(key =>\n            adm.make_(\n                key,\n                // must pass \"undefined\" for { key: undefined }\n                !overrides ? true : key in overrides ? overrides[key] : true\n            )\n        )\n    } finally {\n        endBatch()\n    }\n    return target\n}\n", "import {\n    $mobx,\n    Atom,\n    EMPTY_ARRAY,\n    IAtom,\n    IEnhancer,\n    IInterceptable,\n    IInterceptor,\n    IListenable,\n    Lambda,\n    addHiddenFinalProp,\n    checkIfStateModificationsAreAllowed,\n    createInstanceofPredicate,\n    getNextId,\n    hasInterceptors,\n    hasListeners,\n    interceptChange,\n    isObject,\n    isSpyEnabled,\n    notifyListeners,\n    registerInterceptor,\n    registerListener,\n    spyReportEnd,\n    spyReportStart,\n    allowStateChangesStart,\n    allowStateChangesEnd,\n    assertProxies,\n    reserveArrayBuffer,\n    hasProp,\n    die,\n    globalState\n} from \"../internal\"\n\nconst SPLICE = \"splice\"\nexport const UPDATE = \"update\"\nexport const MAX_SPLICE_SIZE = 10000 // See e.g. https://github.com/mobxjs/mobx/issues/859\n\nexport interface IObservableArray<T = any> extends Array<T> {\n    spliceWithArray(index: number, deleteCount?: number, newItems?: T[]): T[]\n    clear(): T[]\n    replace(newItems: T[]): T[]\n    remove(value: T): boolean\n    toJSON(): T[]\n}\n\ninterface IArrayBaseChange<T> {\n    object: IObservableArray<T>\n    observableKind: \"array\"\n    debugObjectName: string\n    index: number\n}\n\nexport type IArrayDidChange<T = any> = IArrayUpdate<T> | IArraySplice<T>\n\nexport interface IArrayUpdate<T = any> extends IArrayBaseChange<T> {\n    type: \"update\"\n    newValue: T\n    oldValue: T\n}\n\nexport interface IArraySplice<T = any> extends IArrayBaseChange<T> {\n    type: \"splice\"\n    added: T[]\n    addedCount: number\n    removed: T[]\n    removedCount: number\n}\n\nexport interface IArrayWillChange<T = any> {\n    object: IObservableArray<T>\n    index: number\n    type: \"update\"\n    newValue: T\n}\n\nexport interface IArrayWillSplice<T = any> {\n    object: IObservableArray<T>\n    index: number\n    type: \"splice\"\n    added: T[]\n    removedCount: number\n}\n\nconst arrayTraps = {\n    get(target, name) {\n        const adm: ObservableArrayAdministration = target[$mobx]\n        if (name === $mobx) return adm\n        if (name === \"length\") return adm.getArrayLength_()\n        if (typeof name === \"string\" && !isNaN(name as any)) {\n            return adm.get_(parseInt(name))\n        }\n        if (hasProp(arrayExtensions, name)) {\n            return arrayExtensions[name]\n        }\n        return target[name]\n    },\n    set(target, name, value): boolean {\n        const adm: ObservableArrayAdministration = target[$mobx]\n        if (name === \"length\") {\n            adm.setArrayLength_(value)\n        }\n        if (typeof name === \"symbol\" || isNaN(name)) {\n            target[name] = value\n        } else {\n            // numeric string\n            adm.set_(parseInt(name), value)\n        }\n        return true\n    },\n    preventExtensions() {\n        die(15)\n    }\n}\n\nexport class ObservableArrayAdministration\n    implements IInterceptable<IArrayWillChange<any> | IArrayWillSplice<any>>, IListenable {\n    atom_: IAtom\n    readonly values_: any[] = [] // this is the prop that gets proxied, so can't replace it!\n    interceptors_\n    changeListeners_\n    enhancer_: (newV: any, oldV: any | undefined) => any\n    dehancer: any\n    proxy_!: IObservableArray<any>\n    lastKnownLength_ = 0\n\n    constructor(\n        name = __DEV__ ? \"ObservableArray@\" + getNextId() : \"ObservableArray\",\n        enhancer: IEnhancer<any>,\n        public owned_: boolean,\n        public legacyMode_: boolean\n    ) {\n        this.atom_ = new Atom(name)\n        this.enhancer_ = (newV, oldV) =>\n            enhancer(newV, oldV, __DEV__ ? name + \"[..]\" : \"ObservableArray[..]\")\n    }\n\n    dehanceValue_(value: any): any {\n        if (this.dehancer !== undefined) return this.dehancer(value)\n        return value\n    }\n\n    dehanceValues_(values: any[]): any[] {\n        if (this.dehancer !== undefined && values.length > 0)\n            return values.map(this.dehancer) as any\n        return values\n    }\n\n    intercept_(handler: IInterceptor<IArrayWillChange<any> | IArrayWillSplice<any>>): Lambda {\n        return registerInterceptor<IArrayWillChange<any> | IArrayWillSplice<any>>(this, handler)\n    }\n\n    observe_(\n        listener: (changeData: IArrayDidChange<any>) => void,\n        fireImmediately = false\n    ): Lambda {\n        if (fireImmediately) {\n            listener(<IArraySplice<any>>{\n                observableKind: \"array\",\n                object: this.proxy_ as any,\n                debugObjectName: this.atom_.name_,\n                type: \"splice\",\n                index: 0,\n                added: this.values_.slice(),\n                addedCount: this.values_.length,\n                removed: [],\n                removedCount: 0\n            })\n        }\n        return registerListener(this, listener)\n    }\n\n    getArrayLength_(): number {\n        this.atom_.reportObserved()\n        return this.values_.length\n    }\n\n    setArrayLength_(newLength: number) {\n        if (typeof newLength !== \"number\" || newLength < 0) die(\"Out of range: \" + newLength)\n        let currentLength = this.values_.length\n        if (newLength === currentLength) return\n        else if (newLength > currentLength) {\n            const newItems = new Array(newLength - currentLength)\n            for (let i = 0; i < newLength - currentLength; i++) newItems[i] = undefined // No Array.fill everywhere...\n            this.spliceWithArray_(currentLength, 0, newItems)\n        } else this.spliceWithArray_(newLength, currentLength - newLength)\n    }\n\n    updateArrayLength_(oldLength: number, delta: number) {\n        if (oldLength !== this.lastKnownLength_) die(16)\n        this.lastKnownLength_ += delta\n        if (this.legacyMode_ && delta > 0) reserveArrayBuffer(oldLength + delta + 1)\n    }\n\n    spliceWithArray_(index: number, deleteCount?: number, newItems?: any[]): any[] {\n        checkIfStateModificationsAreAllowed(this.atom_)\n        const length = this.values_.length\n\n        if (index === undefined) index = 0\n        else if (index > length) index = length\n        else if (index < 0) index = Math.max(0, length + index)\n\n        if (arguments.length === 1) deleteCount = length - index\n        else if (deleteCount === undefined || deleteCount === null) deleteCount = 0\n        else deleteCount = Math.max(0, Math.min(deleteCount, length - index))\n\n        if (newItems === undefined) newItems = EMPTY_ARRAY\n\n        if (hasInterceptors(this)) {\n            const change = interceptChange<IArrayWillSplice<any>>(this as any, {\n                object: this.proxy_ as any,\n                type: SPLICE,\n                index,\n                removedCount: deleteCount,\n                added: newItems\n            })\n            if (!change) return EMPTY_ARRAY\n            deleteCount = change.removedCount\n            newItems = change.added\n        }\n\n        newItems =\n            newItems.length === 0 ? newItems : newItems.map(v => this.enhancer_(v, undefined))\n        if (this.legacyMode_ || __DEV__) {\n            const lengthDelta = newItems.length - deleteCount\n            this.updateArrayLength_(length, lengthDelta) // checks if internal array wasn't modified\n        }\n        const res = this.spliceItemsIntoValues_(index, deleteCount, newItems)\n\n        if (deleteCount !== 0 || newItems.length !== 0)\n            this.notifyArraySplice_(index, newItems, res)\n        return this.dehanceValues_(res)\n    }\n\n    spliceItemsIntoValues_(index: number, deleteCount: number, newItems: any[]): any[] {\n        if (newItems.length < MAX_SPLICE_SIZE) {\n            return this.values_.splice(index, deleteCount, ...newItems)\n        } else {\n            const res = this.values_.slice(index, index + deleteCount)\n            let oldItems = this.values_.slice(index + deleteCount)\n            this.values_.length = index + newItems.length - deleteCount\n            for (let i = 0; i < newItems.length; i++) this.values_[index + i] = newItems[i]\n            for (let i = 0; i < oldItems.length; i++)\n                this.values_[index + newItems.length + i] = oldItems[i]\n            return res\n        }\n    }\n\n    notifyArrayChildUpdate_(index: number, newValue: any, oldValue: any) {\n        const notifySpy = !this.owned_ && isSpyEnabled()\n        const notify = hasListeners(this)\n        const change: IArrayDidChange | null =\n            notify || notifySpy\n                ? ({\n                      observableKind: \"array\",\n                      object: this.proxy_,\n                      type: UPDATE,\n                      debugObjectName: this.atom_.name_,\n                      index,\n                      newValue,\n                      oldValue\n                  } as const)\n                : null\n\n        // The reason why this is on right hand side here (and not above), is this way the uglifier will drop it, but it won't\n        // cause any runtime overhead in development mode without NODE_ENV set, unless spying is enabled\n        if (__DEV__ && notifySpy) spyReportStart(change!)\n        this.atom_.reportChanged()\n        if (notify) notifyListeners(this, change)\n        if (__DEV__ && notifySpy) spyReportEnd()\n    }\n\n    notifyArraySplice_(index: number, added: any[], removed: any[]) {\n        const notifySpy = !this.owned_ && isSpyEnabled()\n        const notify = hasListeners(this)\n        const change: IArraySplice | null =\n            notify || notifySpy\n                ? ({\n                      observableKind: \"array\",\n                      object: this.proxy_,\n                      debugObjectName: this.atom_.name_,\n                      type: SPLICE,\n                      index,\n                      removed,\n                      added,\n                      removedCount: removed.length,\n                      addedCount: added.length\n                  } as const)\n                : null\n\n        if (__DEV__ && notifySpy) spyReportStart(change!)\n        this.atom_.reportChanged()\n        // conform: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/observe\n        if (notify) notifyListeners(this, change)\n        if (__DEV__ && notifySpy) spyReportEnd()\n    }\n\n    get_(index: number): any | undefined {\n        if (index < this.values_.length) {\n            this.atom_.reportObserved()\n            return this.dehanceValue_(this.values_[index])\n        }\n        console.warn(\n            __DEV__\n                ? `[mobx] Out of bounds read: ${index}`\n                : `[mobx.array] Attempt to read an array index (${index}) that is out of bounds (${this.values_.length}). Please check length first. Out of bound indices will not be tracked by MobX`\n        )\n    }\n\n    set_(index: number, newValue: any) {\n        const values = this.values_\n        if (index < values.length) {\n            // update at index in range\n            checkIfStateModificationsAreAllowed(this.atom_)\n            const oldValue = values[index]\n            if (hasInterceptors(this)) {\n                const change = interceptChange<IArrayWillChange<any>>(this as any, {\n                    type: UPDATE,\n                    object: this.proxy_ as any, // since \"this\" is the real array we need to pass its proxy\n                    index,\n                    newValue\n                })\n                if (!change) return\n                newValue = change.newValue\n            }\n            newValue = this.enhancer_(newValue, oldValue)\n            const changed = newValue !== oldValue\n            if (changed) {\n                values[index] = newValue\n                this.notifyArrayChildUpdate_(index, newValue, oldValue)\n            }\n        } else if (index === values.length) {\n            // add a new item\n            this.spliceWithArray_(index, 0, [newValue])\n        } else {\n            // out of bounds\n            die(17, index, values.length)\n        }\n    }\n}\n\nexport function createObservableArray<T>(\n    initialValues: T[] | undefined,\n    enhancer: IEnhancer<T>,\n    name = __DEV__ ? \"ObservableArray@\" + getNextId() : \"ObservableArray\",\n    owned = false\n): IObservableArray<T> {\n    assertProxies()\n    const adm = new ObservableArrayAdministration(name, enhancer, owned, false)\n    addHiddenFinalProp(adm.values_, $mobx, adm)\n    const proxy = new Proxy(adm.values_, arrayTraps) as any\n    adm.proxy_ = proxy\n    if (initialValues && initialValues.length) {\n        const prev = allowStateChangesStart(true)\n        adm.spliceWithArray_(0, 0, initialValues)\n        allowStateChangesEnd(prev)\n    }\n    return proxy\n}\n\n// eslint-disable-next-line\nexport var arrayExtensions = {\n    clear(): any[] {\n        return this.splice(0)\n    },\n\n    replace(newItems: any[]) {\n        const adm: ObservableArrayAdministration = this[$mobx]\n        return adm.spliceWithArray_(0, adm.values_.length, newItems)\n    },\n\n    // Used by JSON.stringify\n    toJSON(): any[] {\n        return this.slice()\n    },\n\n    /*\n     * functions that do alter the internal structure of the array, (based on lib.es6.d.ts)\n     * since these functions alter the inner structure of the array, the have side effects.\n     * Because the have side effects, they should not be used in computed function,\n     * and for that reason the do not call dependencyState.notifyObserved\n     */\n    splice(index: number, deleteCount?: number, ...newItems: any[]): any[] {\n        const adm: ObservableArrayAdministration = this[$mobx]\n        switch (arguments.length) {\n            case 0:\n                return []\n            case 1:\n                return adm.spliceWithArray_(index)\n            case 2:\n                return adm.spliceWithArray_(index, deleteCount)\n        }\n        return adm.spliceWithArray_(index, deleteCount, newItems)\n    },\n\n    spliceWithArray(index: number, deleteCount?: number, newItems?: any[]): any[] {\n        return (this[$mobx] as ObservableArrayAdministration).spliceWithArray_(\n            index,\n            deleteCount,\n            newItems\n        )\n    },\n\n    push(...items: any[]): number {\n        const adm: ObservableArrayAdministration = this[$mobx]\n        adm.spliceWithArray_(adm.values_.length, 0, items)\n        return adm.values_.length\n    },\n\n    pop() {\n        return this.splice(Math.max(this[$mobx].values_.length - 1, 0), 1)[0]\n    },\n\n    shift() {\n        return this.splice(0, 1)[0]\n    },\n\n    unshift(...items: any[]): number {\n        const adm: ObservableArrayAdministration = this[$mobx]\n        adm.spliceWithArray_(0, 0, items)\n        return adm.values_.length\n    },\n\n    reverse(): any[] {\n        // reverse by default mutates in place before returning the result\n        // which makes it both a 'derivation' and a 'mutation'.\n        if (globalState.trackingDerivation) {\n            die(37, \"reverse\")\n        }\n        this.replace(this.slice().reverse())\n        return this\n    },\n\n    sort(): any[] {\n        // sort by default mutates in place before returning the result\n        // which goes against all good practices. Let's not change the array in place!\n        if (globalState.trackingDerivation) {\n            die(37, \"sort\")\n        }\n        const copy = this.slice()\n        copy.sort.apply(copy, arguments)\n        this.replace(copy)\n        return this\n    },\n\n    remove(value: any): boolean {\n        const adm: ObservableArrayAdministration = this[$mobx]\n        const idx = adm.dehanceValues_(adm.values_).indexOf(value)\n        if (idx > -1) {\n            this.splice(idx, 1)\n            return true\n        }\n        return false\n    }\n}\n\n/**\n * Wrap function from prototype\n * Without this, everything works as well, but this works\n * faster as everything works on unproxied values\n */\naddArrayExtension(\"concat\", simpleFunc)\naddArrayExtension(\"flat\", simpleFunc)\naddArrayExtension(\"includes\", simpleFunc)\naddArrayExtension(\"indexOf\", simpleFunc)\naddArrayExtension(\"join\", simpleFunc)\naddArrayExtension(\"lastIndexOf\", simpleFunc)\naddArrayExtension(\"slice\", simpleFunc)\naddArrayExtension(\"toString\", simpleFunc)\naddArrayExtension(\"toLocaleString\", simpleFunc)\n// map\naddArrayExtension(\"every\", mapLikeFunc)\naddArrayExtension(\"filter\", mapLikeFunc)\naddArrayExtension(\"find\", mapLikeFunc)\naddArrayExtension(\"findIndex\", mapLikeFunc)\naddArrayExtension(\"flatMap\", mapLikeFunc)\naddArrayExtension(\"forEach\", mapLikeFunc)\naddArrayExtension(\"map\", mapLikeFunc)\naddArrayExtension(\"some\", mapLikeFunc)\n// reduce\naddArrayExtension(\"reduce\", reduceLikeFunc)\naddArrayExtension(\"reduceRight\", reduceLikeFunc)\n\nfunction addArrayExtension(funcName, funcFactory) {\n    if (typeof Array.prototype[funcName] === \"function\") {\n        arrayExtensions[funcName] = funcFactory(funcName)\n    }\n}\n\n// Report and delegate to dehanced array\nfunction simpleFunc(funcName) {\n    return function () {\n        const adm: ObservableArrayAdministration = this[$mobx]\n        adm.atom_.reportObserved()\n        const dehancedValues = adm.dehanceValues_(adm.values_)\n        return dehancedValues[funcName].apply(dehancedValues, arguments)\n    }\n}\n\n// Make sure callbacks recieve correct array arg #2326\nfunction mapLikeFunc(funcName) {\n    return function (callback, thisArg) {\n        const adm: ObservableArrayAdministration = this[$mobx]\n        adm.atom_.reportObserved()\n        const dehancedValues = adm.dehanceValues_(adm.values_)\n        return dehancedValues[funcName]((element, index) => {\n            return callback.call(thisArg, element, index, this)\n        })\n    }\n}\n\n// Make sure callbacks recieve correct array arg #2326\nfunction reduceLikeFunc(funcName) {\n    return function () {\n        const adm: ObservableArrayAdministration = this[$mobx]\n        adm.atom_.reportObserved()\n        const dehancedValues = adm.dehanceValues_(adm.values_)\n        // #2432 - reduce behavior depends on arguments.length\n        const callback = arguments[0]\n        arguments[0] = (accumulator, currentValue, index) => {\n            return callback(accumulator, currentValue, index, this)\n        }\n        return dehancedValues[funcName].apply(dehancedValues, arguments)\n    }\n}\n\nconst isObservableArrayAdministration = createInstanceofPredicate(\n    \"ObservableArrayAdministration\",\n    ObservableArrayAdministration\n)\n\nexport function isObservableArray(thing): thing is IObservableArray<any> {\n    return isObject(thing) && isObservableArrayAdministration(thing[$mobx])\n}\n", "import {\n    $mobx,\n    IEnhancer,\n    IInterceptable,\n    IInterceptor,\n    IListenable,\n    Lambda,\n    ObservableValue,\n    checkIfStateModificationsAreAllowed,\n    createAtom,\n    createInstanceofPredicate,\n    deepEnhancer,\n    getNextId,\n    getPlainObjectKeys,\n    hasInterceptors,\n    hasListeners,\n    interceptChange,\n    isES6Map,\n    isPlainObject,\n    isSpyEnabled,\n    makeIterable,\n    notifyListeners,\n    referenceEnhancer,\n    registerInterceptor,\n    registerListener,\n    spyReportEnd,\n    spyReportStart,\n    stringifyKey,\n    transaction,\n    untracked,\n    onBecomeUnobserved,\n    globalState,\n    die,\n    isFunction,\n    UPDATE,\n    IAtom\n} from \"../internal\"\n\nexport interface IKeyValueMap<V = any> {\n    [key: string]: V\n}\n\nexport type IMapEntry<K = any, V = any> = [K, V]\nexport type IMapEntries<K = any, V = any> = IMapEntry<K, V>[]\n\nexport type IMapDidChange<K = any, V = any> = { observableKind: \"map\"; debugObjectName: string } & (\n    | {\n          object: ObservableMap<K, V>\n          name: K // actual the key or index, but this is based on the ancient .observe proposal for consistency\n          type: \"update\"\n          newValue: V\n          oldValue: V\n      }\n    | {\n          object: ObservableMap<K, V>\n          name: K\n          type: \"add\"\n          newValue: V\n      }\n    | {\n          object: ObservableMap<K, V>\n          name: K\n          type: \"delete\"\n          oldValue: V\n      }\n)\n\nexport interface IMapWillChange<K = any, V = any> {\n    object: ObservableMap<K, V>\n    type: \"update\" | \"add\" | \"delete\"\n    name: K\n    newValue?: V\n}\n\nconst ObservableMapMarker = {}\n\nexport const ADD = \"add\"\nexport const DELETE = \"delete\"\n\nexport type IObservableMapInitialValues<K = any, V = any> =\n    | IMapEntries<K, V>\n    | IKeyValueMap<V>\n    | Map<K, V>\n\n// just extend Map? See also https://gist.github.com/nestharus/13b4d74f2ef4a2f4357dbd3fc23c1e54\n// But: https://github.com/mobxjs/mobx/issues/1556\nexport class ObservableMap<K = any, V = any>\n    implements Map<K, V>, IInterceptable<IMapWillChange<K, V>>, IListenable {\n    [$mobx] = ObservableMapMarker\n    data_: Map<K, ObservableValue<V>>\n    hasMap_: Map<K, ObservableValue<boolean>> // hasMap, not hashMap >-).\n    keysAtom_: IAtom\n    interceptors_\n    changeListeners_\n    dehancer: any\n\n    constructor(\n        initialData?: IObservableMapInitialValues<K, V>,\n        public enhancer_: IEnhancer<V> = deepEnhancer,\n        public name_ = __DEV__ ? \"ObservableMap@\" + getNextId() : \"ObservableMap\"\n    ) {\n        if (!isFunction(Map)) {\n            die(18)\n        }\n        this.keysAtom_ = createAtom(__DEV__ ? `${this.name_}.keys()` : \"ObservableMap.keys()\")\n        this.data_ = new Map()\n        this.hasMap_ = new Map()\n        this.merge(initialData)\n    }\n\n    private has_(key: K): boolean {\n        return this.data_.has(key)\n    }\n\n    has(key: K): boolean {\n        if (!globalState.trackingDerivation) return this.has_(key)\n\n        let entry = this.hasMap_.get(key)\n        if (!entry) {\n            const newEntry = (entry = new ObservableValue(\n                this.has_(key),\n                referenceEnhancer,\n                __DEV__ ? `${this.name_}.${stringifyKey(key)}?` : \"ObservableMap.key?\",\n                false\n            ))\n            this.hasMap_.set(key, newEntry)\n            onBecomeUnobserved(newEntry, () => this.hasMap_.delete(key))\n        }\n\n        return entry.get()\n    }\n\n    set(key: K, value: V) {\n        const hasKey = this.has_(key)\n        if (hasInterceptors(this)) {\n            const change = interceptChange<IMapWillChange<K, V>>(this, {\n                type: hasKey ? UPDATE : ADD,\n                object: this,\n                newValue: value,\n                name: key\n            })\n            if (!change) return this\n            value = change.newValue!\n        }\n        if (hasKey) {\n            this.updateValue_(key, value)\n        } else {\n            this.addValue_(key, value)\n        }\n        return this\n    }\n\n    delete(key: K): boolean {\n        checkIfStateModificationsAreAllowed(this.keysAtom_)\n        if (hasInterceptors(this)) {\n            const change = interceptChange<IMapWillChange<K, V>>(this, {\n                type: DELETE,\n                object: this,\n                name: key\n            })\n            if (!change) return false\n        }\n        if (this.has_(key)) {\n            const notifySpy = isSpyEnabled()\n            const notify = hasListeners(this)\n            const change: IMapDidChange<K, V> | null =\n                notify || notifySpy\n                    ? {\n                          observableKind: \"map\",\n                          debugObjectName: this.name_,\n                          type: DELETE,\n                          object: this,\n                          oldValue: (<any>this.data_.get(key)).value_,\n                          name: key\n                      }\n                    : null\n\n            if (__DEV__ && notifySpy) spyReportStart(change!)\n            transaction(() => {\n                this.keysAtom_.reportChanged()\n                this.updateHasMapEntry_(key, false)\n                const observable = this.data_.get(key)!\n                observable.setNewValue_(undefined as any)\n                this.data_.delete(key)\n            })\n            if (notify) notifyListeners(this, change)\n            if (__DEV__ && notifySpy) spyReportEnd()\n            return true\n        }\n        return false\n    }\n\n    private updateHasMapEntry_(key: K, value: boolean) {\n        let entry = this.hasMap_.get(key)\n        if (entry) {\n            entry.setNewValue_(value)\n        }\n    }\n\n    private updateValue_(key: K, newValue: V | undefined) {\n        const observable = this.data_.get(key)!\n        newValue = (observable as any).prepareNewValue_(newValue) as V\n        if (newValue !== globalState.UNCHANGED) {\n            const notifySpy = isSpyEnabled()\n            const notify = hasListeners(this)\n            const change: IMapDidChange<K, V> | null =\n                notify || notifySpy\n                    ? {\n                          observableKind: \"map\",\n                          debugObjectName: this.name_,\n                          type: UPDATE,\n                          object: this,\n                          oldValue: (observable as any).value_,\n                          name: key,\n                          newValue\n                      }\n                    : null\n            if (__DEV__ && notifySpy) spyReportStart(change!)\n            observable.setNewValue_(newValue as V)\n            if (notify) notifyListeners(this, change)\n            if (__DEV__ && notifySpy) spyReportEnd()\n        }\n    }\n\n    private addValue_(key: K, newValue: V) {\n        checkIfStateModificationsAreAllowed(this.keysAtom_)\n        transaction(() => {\n            const observable = new ObservableValue(\n                newValue,\n                this.enhancer_,\n                __DEV__ ? `${this.name_}.${stringifyKey(key)}` : \"ObservableMap.key\",\n                false\n            )\n            this.data_.set(key, observable)\n            newValue = (observable as any).value_ // value might have been changed\n            this.updateHasMapEntry_(key, true)\n            this.keysAtom_.reportChanged()\n        })\n        const notifySpy = isSpyEnabled()\n        const notify = hasListeners(this)\n        const change: IMapDidChange<K, V> | null =\n            notify || notifySpy\n                ? {\n                      observableKind: \"map\",\n                      debugObjectName: this.name_,\n                      type: ADD,\n                      object: this,\n                      name: key,\n                      newValue\n                  }\n                : null\n        if (__DEV__ && notifySpy) spyReportStart(change!)\n        if (notify) notifyListeners(this, change)\n        if (__DEV__ && notifySpy) spyReportEnd()\n    }\n\n    get(key: K): V | undefined {\n        if (this.has(key)) return this.dehanceValue_(this.data_.get(key)!.get())\n        return this.dehanceValue_(undefined)\n    }\n\n    private dehanceValue_<X extends V | undefined>(value: X): X {\n        if (this.dehancer !== undefined) {\n            return this.dehancer(value)\n        }\n        return value\n    }\n\n    keys(): IterableIterator<K> {\n        this.keysAtom_.reportObserved()\n        return this.data_.keys()\n    }\n\n    values(): IterableIterator<V> {\n        const self = this\n        const keys = this.keys()\n        return makeIterable({\n            next() {\n                const { done, value } = keys.next()\n                return {\n                    done,\n                    value: done ? (undefined as any) : self.get(value)\n                }\n            }\n        })\n    }\n\n    entries(): IterableIterator<IMapEntry<K, V>> {\n        const self = this\n        const keys = this.keys()\n        return makeIterable({\n            next() {\n                const { done, value } = keys.next()\n                return {\n                    done,\n                    value: done ? (undefined as any) : ([value, self.get(value)!] as [K, V])\n                }\n            }\n        })\n    }\n\n    [Symbol.iterator]() {\n        return this.entries()\n    }\n\n    forEach(callback: (value: V, key: K, object: Map<K, V>) => void, thisArg?) {\n        for (const [key, value] of this) callback.call(thisArg, value, key, this)\n    }\n\n    /** Merge another object into this object, returns this. */\n    merge(other: ObservableMap<K, V> | IKeyValueMap<V> | any): ObservableMap<K, V> {\n        if (isObservableMap(other)) {\n            other = new Map(other)\n        }\n        transaction(() => {\n            if (isPlainObject(other))\n                getPlainObjectKeys(other).forEach((key: any) =>\n                    this.set((key as any) as K, other[key])\n                )\n            else if (Array.isArray(other)) other.forEach(([key, value]) => this.set(key, value))\n            else if (isES6Map(other)) {\n                if (other.constructor !== Map) die(19, other)\n                other.forEach((value, key) => this.set(key, value))\n            } else if (other !== null && other !== undefined) die(20, other)\n        })\n        return this\n    }\n\n    clear() {\n        transaction(() => {\n            untracked(() => {\n                for (const key of this.keys()) this.delete(key)\n            })\n        })\n    }\n\n    replace(values: ObservableMap<K, V> | IKeyValueMap<V> | any): ObservableMap<K, V> {\n        // Implementation requirements:\n        // - respect ordering of replacement map\n        // - allow interceptors to run and potentially prevent individual operations\n        // - don't recreate observables that already exist in original map (so we don't destroy existing subscriptions)\n        // - don't _keysAtom.reportChanged if the keys of resulting map are indentical (order matters!)\n        // - note that result map may differ from replacement map due to the interceptors\n        transaction(() => {\n            // Convert to map so we can do quick key lookups\n            const replacementMap = convertToMap(values)\n            const orderedData = new Map()\n            // Used for optimization\n            let keysReportChangedCalled = false\n            // Delete keys that don't exist in replacement map\n            // if the key deletion is prevented by interceptor\n            // add entry at the beginning of the result map\n            for (const key of this.data_.keys()) {\n                // Concurrently iterating/deleting keys\n                // iterator should handle this correctly\n                if (!replacementMap.has(key)) {\n                    const deleted = this.delete(key)\n                    // Was the key removed?\n                    if (deleted) {\n                        // _keysAtom.reportChanged() was already called\n                        keysReportChangedCalled = true\n                    } else {\n                        // Delete prevented by interceptor\n                        const value = this.data_.get(key)\n                        orderedData.set(key, value)\n                    }\n                }\n            }\n            // Merge entries\n            for (const [key, value] of replacementMap.entries()) {\n                // We will want to know whether a new key is added\n                const keyExisted = this.data_.has(key)\n                // Add or update value\n                this.set(key, value)\n                // The addition could have been prevent by interceptor\n                if (this.data_.has(key)) {\n                    // The update could have been prevented by interceptor\n                    // and also we want to preserve existing values\n                    // so use value from _data map (instead of replacement map)\n                    const value = this.data_.get(key)\n                    orderedData.set(key, value)\n                    // Was a new key added?\n                    if (!keyExisted) {\n                        // _keysAtom.reportChanged() was already called\n                        keysReportChangedCalled = true\n                    }\n                }\n            }\n            // Check for possible key order change\n            if (!keysReportChangedCalled) {\n                if (this.data_.size !== orderedData.size) {\n                    // If size differs, keys are definitely modified\n                    this.keysAtom_.reportChanged()\n                } else {\n                    const iter1 = this.data_.keys()\n                    const iter2 = orderedData.keys()\n                    let next1 = iter1.next()\n                    let next2 = iter2.next()\n                    while (!next1.done) {\n                        if (next1.value !== next2.value) {\n                            this.keysAtom_.reportChanged()\n                            break\n                        }\n                        next1 = iter1.next()\n                        next2 = iter2.next()\n                    }\n                }\n            }\n            // Use correctly ordered map\n            this.data_ = orderedData\n        })\n        return this\n    }\n\n    get size(): number {\n        this.keysAtom_.reportObserved()\n        return this.data_.size\n    }\n\n    toString(): string {\n        return \"[object ObservableMap]\"\n    }\n\n    toJSON(): [K, V][] {\n        return Array.from(this)\n    }\n\n    get [Symbol.toStringTag]() {\n        return \"Map\"\n    }\n\n    /**\n     * Observes this object. Triggers for the events 'add', 'update' and 'delete'.\n     * See: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/observe\n     * for callback details\n     */\n    observe_(listener: (changes: IMapDidChange<K, V>) => void, fireImmediately?: boolean): Lambda {\n        if (__DEV__ && fireImmediately === true)\n            die(\"`observe` doesn't support fireImmediately=true in combination with maps.\")\n        return registerListener(this, listener)\n    }\n\n    intercept_(handler: IInterceptor<IMapWillChange<K, V>>): Lambda {\n        return registerInterceptor(this, handler)\n    }\n}\n\n// eslint-disable-next-line\nexport var isObservableMap = createInstanceofPredicate(\"ObservableMap\", ObservableMap) as (\n    thing: any\n) => thing is ObservableMap<any, any>\n\nfunction convertToMap(dataStructure: any): Map<any, any> {\n    if (isES6Map(dataStructure) || isObservableMap(dataStructure)) {\n        return dataStructure\n    } else if (Array.isArray(dataStructure)) {\n        return new Map(dataStructure)\n    } else if (isPlainObject(dataStructure)) {\n        const map = new Map()\n        for (const key in dataStructure) {\n            map.set(key, dataStructure[key])\n        }\n        return map\n    } else {\n        return die(21, dataStructure)\n    }\n}\n", "import {\n    $mobx,\n    create<PERSON>tom,\n    deepEnhancer,\n    getNextId,\n    IEnhancer,\n    isSpyEnabled,\n    hasListeners,\n    IListenable,\n    registerListener,\n    Lambda,\n    spyReportStart,\n    notifyListeners,\n    spyReportEnd,\n    createInstanceofPredicate,\n    hasInterceptors,\n    interceptChange,\n    IInterceptable,\n    IInterceptor,\n    registerInterceptor,\n    checkIfStateModificationsAreAllowed,\n    untracked,\n    makeIterable,\n    transaction,\n    isES6Set,\n    IAtom,\n    DELETE,\n    ADD,\n    die,\n    isFunction\n} from \"../internal\"\n\nconst ObservableSetMarker = {}\n\nexport type IObservableSetInitialValues<T> = Set<T> | readonly T[]\n\nexport type ISetDidChange<T = any> =\n    | {\n          object: ObservableSet<T>\n          observableKind: \"set\"\n          debugObjectName: string\n          type: \"add\"\n          newValue: T\n      }\n    | {\n          object: ObservableSet<T>\n          observableKind: \"set\"\n          debugObjectName: string\n          type: \"delete\"\n          oldValue: T\n      }\n\nexport type ISetWillChange<T = any> =\n    | {\n          type: \"delete\"\n          object: ObservableSet<T>\n          oldValue: T\n      }\n    | {\n          type: \"add\"\n          object: ObservableSet<T>\n          newValue: T\n      }\n\nexport class ObservableSet<T = any> implements Set<T>, IInterceptable<ISetWillChange>, IListenable {\n    [$mobx] = ObservableSetMarker\n    private data_: Set<any> = new Set()\n    private atom_: IAtom\n    changeListeners_\n    interceptors_\n    dehancer: any\n    enhancer_: (newV: any, oldV: any | undefined) => any\n\n    constructor(\n        initialData?: IObservableSetInitialValues<T>,\n        enhancer: IEnhancer<T> = deepEnhancer,\n        public name_ = __DEV__ ? \"ObservableSet@\" + getNextId() : \"ObservableSet\"\n    ) {\n        if (!isFunction(Set)) {\n            die(22)\n        }\n        this.atom_ = createAtom(this.name_)\n        this.enhancer_ = (newV, oldV) => enhancer(newV, oldV, name_)\n        if (initialData) {\n            this.replace(initialData)\n        }\n    }\n\n    private dehanceValue_<X extends T | undefined>(value: X): X {\n        if (this.dehancer !== undefined) {\n            return this.dehancer(value)\n        }\n        return value\n    }\n\n    clear() {\n        transaction(() => {\n            untracked(() => {\n                for (const value of this.data_.values()) this.delete(value)\n            })\n        })\n    }\n\n    forEach(callbackFn: (value: T, value2: T, set: Set<T>) => void, thisArg?: any) {\n        for (const value of this) {\n            callbackFn.call(thisArg, value, value, this)\n        }\n    }\n\n    get size() {\n        this.atom_.reportObserved()\n        return this.data_.size\n    }\n\n    add(value: T) {\n        checkIfStateModificationsAreAllowed(this.atom_)\n        if (hasInterceptors(this)) {\n            const change = interceptChange<ISetWillChange<T>>(this, {\n                type: ADD,\n                object: this,\n                newValue: value\n            })\n            if (!change) return this\n            // ideally, value = change.value would be done here, so that values can be\n            // changed by interceptor. Same applies for other Set and Map api's.\n        }\n        if (!this.has(value)) {\n            transaction(() => {\n                this.data_.add(this.enhancer_(value, undefined))\n                this.atom_.reportChanged()\n            })\n            const notifySpy = __DEV__ && isSpyEnabled()\n            const notify = hasListeners(this)\n            const change =\n                notify || notifySpy\n                    ? <ISetDidChange<T>>{\n                          observableKind: \"set\",\n                          debugObjectName: this.name_,\n                          type: ADD,\n                          object: this,\n                          newValue: value\n                      }\n                    : null\n            if (notifySpy && __DEV__) spyReportStart(change!)\n            if (notify) notifyListeners(this, change)\n            if (notifySpy && __DEV__) spyReportEnd()\n        }\n\n        return this\n    }\n\n    delete(value: any) {\n        if (hasInterceptors(this)) {\n            const change = interceptChange<ISetWillChange<T>>(this, {\n                type: DELETE,\n                object: this,\n                oldValue: value\n            })\n            if (!change) return false\n        }\n        if (this.has(value)) {\n            const notifySpy = __DEV__ && isSpyEnabled()\n            const notify = hasListeners(this)\n            const change =\n                notify || notifySpy\n                    ? <ISetDidChange<T>>{\n                          observableKind: \"set\",\n                          debugObjectName: this.name_,\n                          type: DELETE,\n                          object: this,\n                          oldValue: value\n                      }\n                    : null\n\n            if (notifySpy && __DEV__) spyReportStart(change!)\n            transaction(() => {\n                this.atom_.reportChanged()\n                this.data_.delete(value)\n            })\n            if (notify) notifyListeners(this, change)\n            if (notifySpy && __DEV__) spyReportEnd()\n            return true\n        }\n        return false\n    }\n\n    has(value: any) {\n        this.atom_.reportObserved()\n        return this.data_.has(this.dehanceValue_(value))\n    }\n\n    entries() {\n        let nextIndex = 0\n        const keys = Array.from(this.keys())\n        const values = Array.from(this.values())\n        return makeIterable<[T, T]>({\n            next() {\n                const index = nextIndex\n                nextIndex += 1\n                return index < values.length\n                    ? { value: [keys[index], values[index]], done: false }\n                    : { done: true }\n            }\n        } as any)\n    }\n\n    keys(): IterableIterator<T> {\n        return this.values()\n    }\n\n    values(): IterableIterator<T> {\n        this.atom_.reportObserved()\n        const self = this\n        let nextIndex = 0\n        const observableValues = Array.from(this.data_.values())\n        return makeIterable<T>({\n            next() {\n                return nextIndex < observableValues.length\n                    ? { value: self.dehanceValue_(observableValues[nextIndex++]), done: false }\n                    : { done: true }\n            }\n        } as any)\n    }\n\n    replace(other: ObservableSet<T> | IObservableSetInitialValues<T>): ObservableSet<T> {\n        if (isObservableSet(other)) {\n            other = new Set(other)\n        }\n\n        transaction(() => {\n            if (Array.isArray(other)) {\n                this.clear()\n                other.forEach(value => this.add(value))\n            } else if (isES6Set(other)) {\n                this.clear()\n                other.forEach(value => this.add(value))\n            } else if (other !== null && other !== undefined) {\n                die(\"Cannot initialize set from \" + other)\n            }\n        })\n\n        return this\n    }\n    observe_(listener: (changes: ISetDidChange<T>) => void, fireImmediately?: boolean): Lambda {\n        // ... 'fireImmediately' could also be true?\n        if (__DEV__ && fireImmediately === true)\n            die(\"`observe` doesn't support fireImmediately=true in combination with sets.\")\n        return registerListener(this, listener)\n    }\n\n    intercept_(handler: IInterceptor<ISetWillChange<T>>): Lambda {\n        return registerInterceptor(this, handler)\n    }\n\n    toJSON(): T[] {\n        return Array.from(this)\n    }\n\n    toString(): string {\n        return \"[object ObservableSet]\"\n    }\n\n    [Symbol.iterator]() {\n        return this.values()\n    }\n\n    get [Symbol.toStringTag]() {\n        return \"Set\"\n    }\n}\n\n// eslint-disable-next-line\nexport var isObservableSet = createInstanceofPredicate(\"ObservableSet\", ObservableSet) as (\n    thing: any\n) => thing is ObservableSet<any>\n", "import {\n    CreateObservableOptions,\n    getAnnotationFromOptions,\n    propagateChanged,\n    isAnnotation,\n    $mobx,\n    Atom,\n    Annotation,\n    ComputedValue,\n    IAtom,\n    IComputedValueOptions,\n    IEnhancer,\n    IInterceptable,\n    IListenable,\n    Lambda,\n    ObservableValue,\n    addHiddenProp,\n    createInstanceofPredicate,\n    endBatch,\n    getNextId,\n    hasInterceptors,\n    hasListeners,\n    interceptChange,\n    isObject,\n    isPlainObject,\n    isSpyEnabled,\n    notifyListeners,\n    referenceEnhancer,\n    registerInterceptor,\n    registerListener,\n    spyReportEnd,\n    spyReportStart,\n    startBatch,\n    stringifyKey,\n    globalState,\n    ADD,\n    UPDATE,\n    die,\n    hasProp,\n    getDescriptor,\n    storedAnnotationsSymbol,\n    ownKeys,\n    isOverride,\n    defineProperty,\n    autoAnnotation,\n    getAdministration,\n    getDebugName,\n    objectPrototype,\n    MakeResult\n} from \"../internal\"\n\nconst descriptorCache = Object.create(null)\n\nexport type IObjectDidChange<T = any> = {\n    observableKind: \"object\"\n    name: PropertyKey\n    object: T\n    debugObjectName: string\n} & (\n    | {\n          type: \"add\"\n          newValue: any\n      }\n    | {\n          type: \"update\"\n          oldValue: any\n          newValue: any\n      }\n    | {\n          type: \"remove\"\n          oldValue: any\n      }\n)\n\nexport type IObjectWillChange<T = any> =\n    | {\n          object: T\n          type: \"update\" | \"add\"\n          name: PropertyKey\n          newValue: any\n      }\n    | {\n          object: T\n          type: \"remove\"\n          name: PropertyKey\n      }\n\nconst REMOVE = \"remove\"\n\nexport class ObservableObjectAdministration\n    implements IInterceptable<IObjectWillChange>, IListenable {\n    keysAtom_: IAtom\n    changeListeners_\n    interceptors_\n    proxy_: any\n    isPlainObject_: boolean\n    appliedAnnotations_?: object\n    private pendingKeys_: undefined | Map<PropertyKey, ObservableValue<boolean>>\n\n    constructor(\n        public target_: any,\n        public values_ = new Map<PropertyKey, ObservableValue<any> | ComputedValue<any>>(),\n        public name_: string,\n        // Used anytime annotation is not explicitely provided\n        public defaultAnnotation_: Annotation = autoAnnotation\n    ) {\n        this.keysAtom_ = new Atom(__DEV__ ? `${this.name_}.keys` : \"ObservableObject.keys\")\n        // Optimization: we use this frequently\n        this.isPlainObject_ = isPlainObject(this.target_)\n        if (__DEV__ && !isAnnotation(this.defaultAnnotation_)) {\n            die(`defaultAnnotation must be valid annotation`)\n        }\n        if (__DEV__) {\n            // Prepare structure for tracking which fields were already annotated\n            this.appliedAnnotations_ = {}\n        }\n    }\n\n    getObservablePropValue_(key: PropertyKey): any {\n        return this.values_.get(key)!.get()\n    }\n\n    setObservablePropValue_(key: PropertyKey, newValue): boolean | null {\n        const observable = this.values_.get(key)\n        if (observable instanceof ComputedValue) {\n            observable.set(newValue)\n            return true\n        }\n\n        // intercept\n        if (hasInterceptors(this)) {\n            const change = interceptChange<IObjectWillChange>(this, {\n                type: UPDATE,\n                object: this.proxy_ || this.target_,\n                name: key,\n                newValue\n            })\n            if (!change) return null\n            newValue = (change as any).newValue\n        }\n        newValue = (observable as any).prepareNewValue_(newValue)\n\n        // notify spy & observers\n        if (newValue !== globalState.UNCHANGED) {\n            const notify = hasListeners(this)\n            const notifySpy = __DEV__ && isSpyEnabled()\n            const change: IObjectDidChange | null =\n                notify || notifySpy\n                    ? {\n                          type: UPDATE,\n                          observableKind: \"object\",\n                          debugObjectName: this.name_,\n                          object: this.proxy_ || this.target_,\n                          oldValue: (observable as any).value_,\n                          name: key,\n                          newValue\n                      }\n                    : null\n\n            if (__DEV__ && notifySpy) spyReportStart(change!)\n            ;(observable as ObservableValue<any>).setNewValue_(newValue)\n            if (notify) notifyListeners(this, change)\n            if (__DEV__ && notifySpy) spyReportEnd()\n        }\n        return true\n    }\n\n    get_(key: PropertyKey): any {\n        if (globalState.trackingDerivation && !hasProp(this.target_, key)) {\n            // Key doesn't exist yet, subscribe for it in case it's added later\n            this.has_(key)\n        }\n        return this.target_[key]\n    }\n\n    /**\n     * @param {PropertyKey} key\n     * @param {any} value\n     * @param {Annotation|boolean} annotation true - use default annotation, false - copy as is\n     * @param {boolean} proxyTrap whether it's called from proxy trap\n     * @returns {boolean|null} true on success, false on failure (proxyTrap + non-configurable), null when cancelled by interceptor\n     */\n    set_(key: PropertyKey, value: any, proxyTrap: boolean = false): boolean | null {\n        // Don't use .has(key) - we care about own\n        if (hasProp(this.target_, key)) {\n            // Existing prop\n            if (this.values_.has(key)) {\n                // Observable (can be intercepted)\n                return this.setObservablePropValue_(key, value)\n            } else if (proxyTrap) {\n                // Non-observable - proxy\n                return Reflect.set(this.target_, key, value)\n            } else {\n                // Non-observable\n                this.target_[key] = value\n                return true\n            }\n        } else {\n            // New prop\n            return this.extend_(\n                key,\n                { value, enumerable: true, writable: true, configurable: true },\n                this.defaultAnnotation_,\n                proxyTrap\n            )\n        }\n    }\n\n    // Trap for \"in\"\n    has_(key: PropertyKey): boolean {\n        if (!globalState.trackingDerivation) {\n            // Skip key subscription outside derivation\n            return key in this.target_\n        }\n        this.pendingKeys_ ||= new Map()\n        let entry = this.pendingKeys_.get(key)\n        if (!entry) {\n            entry = new ObservableValue(\n                key in this.target_,\n                referenceEnhancer,\n                __DEV__ ? `${this.name_}.${stringifyKey(key)}?` : \"ObservableObject.key?\",\n                false\n            )\n            this.pendingKeys_.set(key, entry)\n        }\n        return entry.get()\n    }\n\n    /**\n     * @param {PropertyKey} key\n     * @param {Annotation|boolean} annotation true - use default annotation, false - ignore prop\n     */\n    make_(key: PropertyKey, annotation: Annotation | boolean): void {\n        if (annotation === true) {\n            annotation = this.defaultAnnotation_\n        }\n        if (annotation === false) {\n            return\n        }\n        assertAnnotable(this, annotation, key)\n        if (!(key in this.target_)) {\n            // Throw on missing key, except for decorators:\n            // Decorator annotations are collected from whole prototype chain.\n            // When called from super() some props may not exist yet.\n            // However we don't have to worry about missing prop,\n            // because the decorator must have been applied to something.\n            if (this.target_[storedAnnotationsSymbol]?.[key]) {\n                return // will be annotated by subclass constructor\n            } else {\n                die(1, annotation.annotationType_, `${this.name_}.${key.toString()}`)\n            }\n        }\n        let source = this.target_\n        while (source && source !== objectPrototype) {\n            const descriptor = getDescriptor(source, key)\n            if (descriptor) {\n                const outcome = annotation.make_(this, key, descriptor, source)\n                if (outcome === MakeResult.Cancel) return\n                if (outcome === MakeResult.Break) break\n            }\n            source = Object.getPrototypeOf(source)\n        }\n        recordAnnotationApplied(this, annotation, key)\n    }\n\n    /**\n     * @param {PropertyKey} key\n     * @param {PropertyDescriptor} descriptor\n     * @param {Annotation|boolean} annotation true - use default annotation, false - copy as is\n     * @param {boolean} proxyTrap whether it's called from proxy trap\n     * @returns {boolean|null} true on success, false on failure (proxyTrap + non-configurable), null when cancelled by interceptor\n     */\n    extend_(\n        key: PropertyKey,\n        descriptor: PropertyDescriptor,\n        annotation: Annotation | boolean,\n        proxyTrap: boolean = false\n    ): boolean | null {\n        if (annotation === true) {\n            annotation = this.defaultAnnotation_\n        }\n        if (annotation === false) {\n            return this.defineProperty_(key, descriptor, proxyTrap)\n        }\n        assertAnnotable(this, annotation, key)\n        const outcome = annotation.extend_(this, key, descriptor, proxyTrap)\n        if (outcome) {\n            recordAnnotationApplied(this, annotation, key)\n        }\n        return outcome\n    }\n\n    /**\n     * @param {PropertyKey} key\n     * @param {PropertyDescriptor} descriptor\n     * @param {boolean} proxyTrap whether it's called from proxy trap\n     * @returns {boolean|null} true on success, false on failure (proxyTrap + non-configurable), null when cancelled by interceptor\n     */\n    defineProperty_(\n        key: PropertyKey,\n        descriptor: PropertyDescriptor,\n        proxyTrap: boolean = false\n    ): boolean | null {\n        try {\n            startBatch()\n\n            // Delete\n            const deleteOutcome = this.delete_(key)\n            if (!deleteOutcome) {\n                // Failure or intercepted\n                return deleteOutcome\n            }\n\n            // ADD interceptor\n            if (hasInterceptors(this)) {\n                const change = interceptChange<IObjectWillChange>(this, {\n                    object: this.proxy_ || this.target_,\n                    name: key,\n                    type: ADD,\n                    newValue: descriptor.value\n                })\n                if (!change) return null\n                const { newValue } = change as any\n                if (descriptor.value !== newValue) {\n                    descriptor = {\n                        ...descriptor,\n                        value: newValue\n                    }\n                }\n            }\n\n            // Define\n            if (proxyTrap) {\n                if (!Reflect.defineProperty(this.target_, key, descriptor)) {\n                    return false\n                }\n            } else {\n                defineProperty(this.target_, key, descriptor)\n            }\n\n            // Notify\n            this.notifyPropertyAddition_(key, descriptor.value)\n        } finally {\n            endBatch()\n        }\n        return true\n    }\n\n    // If original descriptor becomes relevant, move this to annotation directly\n    defineObservableProperty_(\n        key: PropertyKey,\n        value: any,\n        enhancer: IEnhancer<any>,\n        proxyTrap: boolean = false\n    ): boolean | null {\n        try {\n            startBatch()\n\n            // Delete\n            const deleteOutcome = this.delete_(key)\n            if (!deleteOutcome) {\n                // Failure or intercepted\n                return deleteOutcome\n            }\n\n            // ADD interceptor\n            if (hasInterceptors(this)) {\n                const change = interceptChange<IObjectWillChange>(this, {\n                    object: this.proxy_ || this.target_,\n                    name: key,\n                    type: ADD,\n                    newValue: value\n                })\n                if (!change) return null\n                value = (change as any).newValue\n            }\n\n            const cachedDescriptor = getCachedObservablePropDescriptor(key)\n            const descriptor = {\n                configurable: globalState.safeDescriptors ? this.isPlainObject_ : true,\n                enumerable: true,\n                get: cachedDescriptor.get,\n                set: cachedDescriptor.set\n            }\n\n            // Define\n            if (proxyTrap) {\n                if (!Reflect.defineProperty(this.target_, key, descriptor)) {\n                    return false\n                }\n            } else {\n                defineProperty(this.target_, key, descriptor)\n            }\n\n            const observable = new ObservableValue(\n                value,\n                enhancer,\n                __DEV__ ? `${this.name_}.${key.toString()}` : \"ObservableObject.key\",\n                false\n            )\n\n            this.values_.set(key, observable)\n\n            // Notify (value possibly changed by ObservableValue)\n            this.notifyPropertyAddition_(key, observable.value_)\n        } finally {\n            endBatch()\n        }\n        return true\n    }\n\n    // If original descriptor becomes relevant, move this to annotation directly\n    defineComputedProperty_(\n        key: PropertyKey,\n        options: IComputedValueOptions<any>,\n        proxyTrap: boolean = false\n    ): boolean | null {\n        try {\n            startBatch()\n\n            // Delete\n            const deleteOutcome = this.delete_(key)\n            if (!deleteOutcome) {\n                // Failure or intercepted\n                return deleteOutcome\n            }\n\n            // ADD interceptor\n            if (hasInterceptors(this)) {\n                const change = interceptChange<IObjectWillChange>(this, {\n                    object: this.proxy_ || this.target_,\n                    name: key,\n                    type: ADD,\n                    newValue: undefined\n                })\n                if (!change) return null\n            }\n            options.name ||= __DEV__ ? `${this.name_}.${key.toString()}` : \"ObservableObject.key\"\n            options.context = this.proxy_ || this.target_\n            const cachedDescriptor = getCachedObservablePropDescriptor(key)\n            const descriptor = {\n                configurable: globalState.safeDescriptors ? this.isPlainObject_ : true,\n                enumerable: false,\n                get: cachedDescriptor.get,\n                set: cachedDescriptor.set\n            }\n\n            // Define\n            if (proxyTrap) {\n                if (!Reflect.defineProperty(this.target_, key, descriptor)) {\n                    return false\n                }\n            } else {\n                defineProperty(this.target_, key, descriptor)\n            }\n\n            this.values_.set(key, new ComputedValue(options))\n\n            // Notify\n            this.notifyPropertyAddition_(key, undefined)\n        } finally {\n            endBatch()\n        }\n        return true\n    }\n\n    /**\n     * @param {PropertyKey} key\n     * @param {PropertyDescriptor} descriptor\n     * @param {boolean} proxyTrap whether it's called from proxy trap\n     * @returns {boolean|null} true on success, false on failure (proxyTrap + non-configurable), null when cancelled by interceptor\n     */\n    delete_(key: PropertyKey, proxyTrap: boolean = false): boolean | null {\n        // No such prop\n        if (!hasProp(this.target_, key)) {\n            return true\n        }\n\n        // Intercept\n        if (hasInterceptors(this)) {\n            const change = interceptChange<IObjectWillChange>(this, {\n                object: this.proxy_ || this.target_,\n                name: key,\n                type: REMOVE\n            })\n            // Cancelled\n            if (!change) return null\n        }\n\n        // Delete\n        try {\n            startBatch()\n            const notify = hasListeners(this)\n            const notifySpy = __DEV__ && isSpyEnabled()\n            const observable = this.values_.get(key)\n            // Value needed for spies/listeners\n            let value = undefined\n            // Optimization: don't pull the value unless we will need it\n            if (!observable && (notify || notifySpy)) {\n                value = getDescriptor(this.target_, key)?.value\n            }\n            // delete prop (do first, may fail)\n            if (proxyTrap) {\n                if (!Reflect.deleteProperty(this.target_, key)) {\n                    return false\n                }\n            } else {\n                delete this.target_[key]\n            }\n            // Allow re-annotating this field\n            if (__DEV__) {\n                delete this.appliedAnnotations_![key]\n            }\n            // Clear observable\n            if (observable) {\n                this.values_.delete(key)\n                // for computed, value is undefined\n                if (observable instanceof ObservableValue) {\n                    value = observable.value_\n                }\n                // Notify: autorun(() => obj[key]), see #1796\n                propagateChanged(observable)\n            }\n            // Notify \"keys/entries/values\" observers\n            this.keysAtom_.reportChanged()\n\n            // Notify \"has\" observers\n            // \"in\" as it may still exist in proto\n            this.pendingKeys_?.get(key)?.set(key in this.target_)\n\n            // Notify spies/listeners\n            if (notify || notifySpy) {\n                const change: IObjectDidChange = {\n                    type: REMOVE,\n                    observableKind: \"object\",\n                    object: this.proxy_ || this.target_,\n                    debugObjectName: this.name_,\n                    oldValue: value,\n                    name: key\n                }\n                if (__DEV__ && notifySpy) spyReportStart(change!)\n                if (notify) notifyListeners(this, change)\n                if (__DEV__ && notifySpy) spyReportEnd()\n            }\n        } finally {\n            endBatch()\n        }\n        return true\n    }\n\n    /**\n     * Observes this object. Triggers for the events 'add', 'update' and 'delete'.\n     * See: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/observe\n     * for callback details\n     */\n    observe_(callback: (changes: IObjectDidChange) => void, fireImmediately?: boolean): Lambda {\n        if (__DEV__ && fireImmediately === true)\n            die(\"`observe` doesn't support the fire immediately property for observable objects.\")\n        return registerListener(this, callback)\n    }\n\n    intercept_(handler): Lambda {\n        return registerInterceptor(this, handler)\n    }\n\n    notifyPropertyAddition_(key: PropertyKey, value: any) {\n        const notify = hasListeners(this)\n        const notifySpy = __DEV__ && isSpyEnabled()\n        if (notify || notifySpy) {\n            const change: IObjectDidChange | null =\n                notify || notifySpy\n                    ? ({\n                          type: ADD,\n                          observableKind: \"object\",\n                          debugObjectName: this.name_,\n                          object: this.proxy_ || this.target_,\n                          name: key,\n                          newValue: value\n                      } as const)\n                    : null\n\n            if (__DEV__ && notifySpy) spyReportStart(change!)\n            if (notify) notifyListeners(this, change)\n            if (__DEV__ && notifySpy) spyReportEnd()\n        }\n\n        this.pendingKeys_?.get(key)?.set(true)\n\n        // Notify \"keys/entries/values\" observers\n        this.keysAtom_.reportChanged()\n    }\n\n    ownKeys_(): PropertyKey[] {\n        this.keysAtom_.reportObserved()\n        return ownKeys(this.target_)\n    }\n\n    keys_(): PropertyKey[] {\n        // Returns enumerable && own, but unfortunately keysAtom will report on ANY key change.\n        // There is no way to distinguish between Object.keys(object) and Reflect.ownKeys(object) - both are handled by ownKeys trap.\n        // We can either over-report in Object.keys(object) or under-report in Reflect.ownKeys(object)\n        // We choose to over-report in Object.keys(object), because:\n        // - typically it's used with simple data objects\n        // - when symbolic/non-enumerable keys are relevant Reflect.ownKeys works as expected\n        this.keysAtom_.reportObserved()\n        return Object.keys(this.target_)\n    }\n}\n\nexport interface IIsObservableObject {\n    $mobx: ObservableObjectAdministration\n}\n\nexport function asObservableObject(\n    target: any,\n    options?: CreateObservableOptions\n): IIsObservableObject {\n    if (__DEV__ && options && isObservableObject(target)) {\n        die(`Options can't be provided for already observable objects.`)\n    }\n\n    if (hasProp(target, $mobx)) {\n        if (__DEV__ && !(getAdministration(target) instanceof ObservableObjectAdministration)) {\n            die(\n                `Cannot convert '${getDebugName(target)}' into observable object:` +\n                    `\\nThe target is already observable of different type.` +\n                    `\\nExtending builtins is not supported.`\n            )\n        }\n        return target\n    }\n\n    if (__DEV__ && !Object.isExtensible(target))\n        die(\"Cannot make the designated object observable; it is not extensible\")\n\n    const name =\n        options?.name ??\n        (__DEV__\n            ? `${\n                  isPlainObject(target) ? \"ObservableObject\" : target.constructor.name\n              }@${getNextId()}`\n            : \"ObservableObject\")\n\n    const adm = new ObservableObjectAdministration(\n        target,\n        new Map(),\n        String(name),\n        getAnnotationFromOptions(options)\n    )\n\n    addHiddenProp(target, $mobx, adm)\n\n    return target\n}\n\nconst isObservableObjectAdministration = createInstanceofPredicate(\n    \"ObservableObjectAdministration\",\n    ObservableObjectAdministration\n)\n\nfunction getCachedObservablePropDescriptor(key) {\n    return (\n        descriptorCache[key] ||\n        (descriptorCache[key] = {\n            get() {\n                return this[$mobx].getObservablePropValue_(key)\n            },\n            set(value) {\n                return this[$mobx].setObservablePropValue_(key, value)\n            }\n        })\n    )\n}\n\nexport function isObservableObject(thing: any): boolean {\n    if (isObject(thing)) {\n        return isObservableObjectAdministration((thing as any)[$mobx])\n    }\n    return false\n}\n\nexport function recordAnnotationApplied(\n    adm: ObservableObjectAdministration,\n    annotation: Annotation,\n    key: PropertyKey\n) {\n    if (__DEV__) {\n        adm.appliedAnnotations_![key] = annotation\n    }\n    // Remove applied decorator annotation so we don't try to apply it again in subclass constructor\n    delete adm.target_[storedAnnotationsSymbol]?.[key]\n}\n\nfunction assertAnnotable(\n    adm: ObservableObjectAdministration,\n    annotation: Annotation,\n    key: PropertyKey\n) {\n    // Valid annotation\n    if (__DEV__ && !isAnnotation(annotation)) {\n        die(`Cannot annotate '${adm.name_}.${key.toString()}': Invalid annotation.`)\n    }\n\n    /*\n    // Configurable, not sealed, not frozen\n    // Possibly not needed, just a little better error then the one thrown by engine.\n    // Cases where this would be useful the most (subclass field initializer) are not interceptable by this.\n    if (__DEV__) {\n        const configurable = getDescriptor(adm.target_, key)?.configurable\n        const frozen = Object.isFrozen(adm.target_)\n        const sealed = Object.isSealed(adm.target_)\n        if (!configurable || frozen || sealed) {\n            const fieldName = `${adm.name_}.${key.toString()}`\n            const requestedAnnotationType = annotation.annotationType_\n            let error = `Cannot apply '${requestedAnnotationType}' to '${fieldName}':`\n            if (frozen) {\n                error += `\\nObject is frozen.`\n            }\n            if (sealed) {\n                error += `\\nObject is sealed.`\n            }\n            if (!configurable) {\n                error += `\\nproperty is not configurable.`\n                // Mention only if caused by us to avoid confusion\n                if (hasProp(adm.appliedAnnotations!, key)) {\n                    error += `\\nTo prevent accidental re-definition of a field by a subclass, `\n                    error += `all annotated fields of non-plain objects (classes) are not configurable.`\n                }\n            }\n            die(error)\n        }\n    }\n    */\n\n    // Not annotated\n    if (__DEV__ && !isOverride(annotation) && hasProp(adm.appliedAnnotations_!, key)) {\n        const fieldName = `${adm.name_}.${key.toString()}`\n        const currentAnnotationType = adm.appliedAnnotations_![key].annotationType_\n        const requestedAnnotationType = annotation.annotationType_\n        die(\n            `Cannot apply '${requestedAnnotationType}' to '${fieldName}':` +\n                `\\nThe field is already annotated with '${currentAnnotationType}'.` +\n                `\\nRe-annotating fields is not allowed.` +\n                `\\nUse 'override' annotation for methods overriden by subclass.`\n        )\n    }\n}\n", "import {\n    getNextId,\n    addHiddenFinalProp,\n    allowStateChangesStart,\n    allowStateChangesEnd,\n    makeIterable,\n    addHiddenProp,\n    ObservableArrayAdministration,\n    $mobx,\n    arrayExtensions,\n    IEnhancer,\n    isObservableArray,\n    IObservableArray,\n    defineProperty\n} from \"../internal\"\n\n/**\n * This array buffer contains two lists of properties, so that all arrays\n * can recycle their property definitions, which significantly improves performance of creating\n * properties on the fly.\n */\nlet OBSERVABLE_ARRAY_BUFFER_SIZE = 0\n\n// Typescript workaround to make sure ObservableArray extends Array\nclass StubArray {}\nfunction inherit(ctor, proto) {\n    if (Object.setPrototypeOf) {\n        Object.setPrototypeOf(ctor.prototype, proto)\n    } else if (ctor.prototype.__proto__ !== undefined) {\n        ctor.prototype.__proto__ = proto\n    } else {\n        ctor.prototype = proto\n    }\n}\ninherit(StubArray, Array.prototype)\n\n// Weex proto freeze protection was here,\n// but it is unclear why the hack is need as MobX never changed the prototype\n// anyway, so removed it in V6\n\nclass LegacyObservableArray<T> extends StubArray {\n    constructor(\n        initialValues: T[] | undefined,\n        enhancer: IEnhancer<T>,\n        name = __DEV__ ? \"ObservableArray@\" + getNextId() : \"ObservableArray\",\n        owned = false\n    ) {\n        super()\n\n        const adm = new ObservableArrayAdministration(name, enhancer, owned, true)\n        adm.proxy_ = this as any\n        addHiddenFinalProp(this, $mobx, adm)\n\n        if (initialValues && initialValues.length) {\n            const prev = allowStateChangesStart(true)\n            // @ts-ignore\n            this.spliceWithArray(0, 0, initialValues)\n            allowStateChangesEnd(prev)\n        }\n    }\n\n    concat(...arrays: T[][]): T[] {\n        ;(this[$mobx] as ObservableArrayAdministration).atom_.reportObserved()\n        return Array.prototype.concat.apply(\n            (this as any).slice(),\n            //@ts-ignore\n            arrays.map(a => (isObservableArray(a) ? a.slice() : a))\n        )\n    }\n\n    get length(): number {\n        return (this[$mobx] as ObservableArrayAdministration).getArrayLength_()\n    }\n\n    set length(newLength: number) {\n        ;(this[$mobx] as ObservableArrayAdministration).setArrayLength_(newLength)\n    }\n\n    get [Symbol.toStringTag]() {\n        return \"Array\"\n    }\n\n    [Symbol.iterator]() {\n        const self = this\n        let nextIndex = 0\n        return makeIterable({\n            next() {\n                // @ts-ignore\n                return nextIndex < self.length\n                    ? { value: self[nextIndex++], done: false }\n                    : { done: true, value: undefined }\n            }\n        })\n    }\n}\n\nObject.entries(arrayExtensions).forEach(([prop, fn]) => {\n    if (prop !== \"concat\") addHiddenProp(LegacyObservableArray.prototype, prop, fn)\n})\n\nfunction createArrayEntryDescriptor(index: number) {\n    return {\n        enumerable: false,\n        configurable: true,\n        get: function () {\n            return this[$mobx].get_(index)\n        },\n        set: function (value) {\n            this[$mobx].set_(index, value)\n        }\n    }\n}\n\nfunction createArrayBufferItem(index: number) {\n    defineProperty(LegacyObservableArray.prototype, \"\" + index, createArrayEntryDescriptor(index))\n}\n\nexport function reserveArrayBuffer(max: number) {\n    if (max > OBSERVABLE_ARRAY_BUFFER_SIZE) {\n        for (let index = OBSERVABLE_ARRAY_BUFFER_SIZE; index < max + 100; index++)\n            createArrayBufferItem(index)\n        OBSERVABLE_ARRAY_BUFFER_SIZE = max\n    }\n}\n\nreserveArrayBuffer(1000)\n\nexport function createLegacyArray<T>(\n    initialValues: T[] | undefined,\n    enhancer: IEnhancer<T>,\n    name?: string\n): IObservableArray<T> {\n    return new LegacyObservableArray(initialValues, enhancer, name) as any\n}\n", "import { isAction } from \"../api/action\"\nimport {\n    $mobx,\n    IDepT<PERSON>Node,\n    isAtom,\n    isComputedValue,\n    isObservableArray,\n    isObservableMap,\n    isObservableObject,\n    isReaction,\n    isObservableSet,\n    die,\n    isFunction\n} from \"../internal\"\n\nexport function getAtom(thing: any, property?: PropertyKey): IDepTreeNode {\n    if (typeof thing === \"object\" && thing !== null) {\n        if (isObservableArray(thing)) {\n            if (property !== undefined) die(23)\n            return (thing as any)[$mobx].atom_\n        }\n        if (isObservableSet(thing)) {\n            return (thing as any)[$mobx]\n        }\n        if (isObservableMap(thing)) {\n            if (property === undefined) return thing.keysAtom_\n            const observable = thing.data_.get(property) || thing.hasMap_.get(property)\n            if (!observable) die(25, property, getDebugName(thing))\n            return observable\n        }\n        if (property && !thing[$mobx]) thing[property] // See #1072\n        if (isObservableObject(thing)) {\n            if (!property) return die(26)\n            const observable = (thing as any)[$mobx].values_.get(property)\n            if (!observable) die(27, property, getDebugName(thing))\n            return observable\n        }\n        if (isAtom(thing) || isComputedValue(thing) || isReaction(thing)) {\n            return thing\n        }\n    } else if (isFunction(thing)) {\n        if (isReaction(thing[$mobx])) {\n            // disposer function\n            return thing[$mobx]\n        }\n    }\n    die(28)\n}\n\nexport function getAdministration(thing: any, property?: string) {\n    if (!thing) die(29)\n    if (property !== undefined) return getAdministration(getAtom(thing, property))\n    if (isAtom(thing) || isComputedValue(thing) || isReaction(thing)) return thing\n    if (isObservableMap(thing) || isObservableSet(thing)) return thing\n    if (thing[$mobx]) return thing[$mobx]\n    die(24, thing)\n}\n\nexport function getDebugName(thing: any, property?: string): string {\n    let named\n    if (property !== undefined) {\n        named = getAtom(thing, property)\n    } else if (isAction(thing)) {\n        return thing.name\n    } else if (isObservableObject(thing) || isObservableMap(thing) || isObservableSet(thing)) {\n        named = getAdministration(thing)\n    } else {\n        // valid for arrays as well\n        named = getAtom(thing)\n    }\n    return named.name_\n}\n", "import {\n    isES6Map,\n    isObservableArray,\n    isObservableMap,\n    isES6Set,\n    isObservableSet,\n    hasProp,\n    isFunction,\n    objectPrototype\n} from \"../internal\"\n\ndeclare const Symbol\nconst toString = objectPrototype.toString\n\nexport function deepEqual(a: any, b: any, depth: number = -1): boolean {\n    return eq(a, b, depth)\n}\n\n// Copied from https://github.com/jashkenas/underscore/blob/5c237a7c682fb68fd5378203f0bf22dce1624854/underscore.js#L1186-L1289\n// Internal recursive comparison function for `isEqual`.\nfunction eq(a: any, b: any, depth: number, aStack?: any[], bStack?: any[]) {\n    // Identical objects are equal. `0 === -0`, but they aren't identical.\n    // See the [Harmony `egal` proposal](http://wiki.ecmascript.org/doku.php?id=harmony:egal).\n    if (a === b) return a !== 0 || 1 / a === 1 / b\n    // `null` or `undefined` only equal to itself (strict comparison).\n    if (a == null || b == null) return false\n    // `NaN`s are equivalent, but non-reflexive.\n    if (a !== a) return b !== b\n    // Exhaust primitive checks\n    const type = typeof a\n    if (!isFunction(type) && type !== \"object\" && typeof b != \"object\") return false\n\n    // Compare `[[Class]]` names.\n    const className = toString.call(a)\n    if (className !== toString.call(b)) return false\n    switch (className) {\n        // Strings, numbers, regular expressions, dates, and booleans are compared by value.\n        case \"[object RegExp]\":\n        // RegExps are coerced to strings for comparison (Note: '' + /a/i === '/a/i')\n        case \"[object String]\":\n            // Primitives and their corresponding object wrappers are equivalent; thus, `\"5\"` is\n            // equivalent to `new String(\"5\")`.\n            return \"\" + a === \"\" + b\n        case \"[object Number]\":\n            // `NaN`s are equivalent, but non-reflexive.\n            // Object(NaN) is equivalent to NaN.\n            if (+a !== +a) return +b !== +b\n            // An `egal` comparison is performed for other numeric values.\n            return +a === 0 ? 1 / +a === 1 / b : +a === +b\n        case \"[object Date]\":\n        case \"[object Boolean]\":\n            // Coerce dates and booleans to numeric primitive values. Dates are compared by their\n            // millisecond representations. Note that invalid dates with millisecond representations\n            // of `NaN` are not equivalent.\n            return +a === +b\n        case \"[object Symbol]\":\n            return (\n                typeof Symbol !== \"undefined\" && Symbol.valueOf.call(a) === Symbol.valueOf.call(b)\n            )\n        case \"[object Map]\":\n        case \"[object Set]\":\n            // Maps and Sets are unwrapped to arrays of entry-pairs, adding an incidental level.\n            // Hide this extra level by increasing the depth.\n            if (depth >= 0) {\n                depth++\n            }\n            break\n    }\n    // Unwrap any wrapped objects.\n    a = unwrap(a)\n    b = unwrap(b)\n\n    const areArrays = className === \"[object Array]\"\n    if (!areArrays) {\n        if (typeof a != \"object\" || typeof b != \"object\") return false\n\n        // Objects with different constructors are not equivalent, but `Object`s or `Array`s\n        // from different frames are.\n        const aCtor = a.constructor,\n            bCtor = b.constructor\n        if (\n            aCtor !== bCtor &&\n            !(\n                isFunction(aCtor) &&\n                aCtor instanceof aCtor &&\n                isFunction(bCtor) &&\n                bCtor instanceof bCtor\n            ) &&\n            \"constructor\" in a &&\n            \"constructor\" in b\n        ) {\n            return false\n        }\n    }\n\n    if (depth === 0) {\n        return false\n    } else if (depth < 0) {\n        depth = -1\n    }\n\n    // Assume equality for cyclic structures. The algorithm for detecting cyclic\n    // structures is adapted from ES 5.1 section 15.12.3, abstract operation `JO`.\n\n    // Initializing stack of traversed objects.\n    // It's done here since we only need them for objects and arrays comparison.\n    aStack = aStack || []\n    bStack = bStack || []\n    let length = aStack.length\n    while (length--) {\n        // Linear search. Performance is inversely proportional to the number of\n        // unique nested structures.\n        if (aStack[length] === a) return bStack[length] === b\n    }\n\n    // Add the first object to the stack of traversed objects.\n    aStack.push(a)\n    bStack.push(b)\n\n    // Recursively compare objects and arrays.\n    if (areArrays) {\n        // Compare array lengths to determine if a deep comparison is necessary.\n        length = a.length\n        if (length !== b.length) return false\n        // Deep compare the contents, ignoring non-numeric properties.\n        while (length--) {\n            if (!eq(a[length], b[length], depth - 1, aStack, bStack)) return false\n        }\n    } else {\n        // Deep compare objects.\n        const keys = Object.keys(a)\n        let key\n        length = keys.length\n        // Ensure that both objects contain the same number of properties before comparing deep equality.\n        if (Object.keys(b).length !== length) return false\n        while (length--) {\n            // Deep compare each member\n            key = keys[length]\n            if (!(hasProp(b, key) && eq(a[key], b[key], depth - 1, aStack, bStack))) return false\n        }\n    }\n    // Remove the first object from the stack of traversed objects.\n    aStack.pop()\n    bStack.pop()\n    return true\n}\n\nfunction unwrap(a: any) {\n    if (isObservableArray(a)) return a.slice()\n    if (isES6Map(a) || isObservableMap(a)) return Array.from(a.entries())\n    if (isES6Set(a) || isObservableSet(a)) return Array.from(a.entries())\n    return a\n}\n", "export function makeIterable<T>(iterator: Iterator<T>): IterableIterator<T> {\n    iterator[Symbol.iterator] = getSelf\n    return iterator as any\n}\n\nfunction getSelf() {\n    return this\n}\n", "import { ObservableObjectAdministration, isFunction } from \"../internal\"\n\nexport const enum MakeResult {\n    Cancel,\n    Break,\n    Continue\n}\n\nexport type Annotation = {\n    annotationType_: string\n    make_(\n        adm: ObservableObjectAdministration,\n        key: <PERSON><PERSON><PERSON>,\n        descriptor: PropertyDescriptor,\n        source: object\n    ): MakeResult\n    extend_(\n        adm: ObservableObjectAdministration,\n        key: PropertyKey,\n        descriptor: PropertyDescriptor,\n        proxyTrap: boolean\n    ): boolean | null\n    options_?: any\n}\n\nexport type AnnotationMapEntry =\n    | Annotation\n    | true /* follow the default decorator, usually deep */\n    | false /* don't decorate this property */\n\n// AdditionalFields can be used to declare additional keys that can be used, for example to be able to\n// declare annotations for private/ protected members, see #2339\nexport type AnnotationsMap<T, AdditionalFields extends PropertyKey> = {\n    [P in Exclude<keyof T, \"toString\">]?: AnnotationMapEntry\n} &\n    Record<AdditionalFields, AnnotationMapEntry>\n\nexport function isAnnotation(thing: any) {\n    return (\n        // Can be function\n        thing instanceof Object &&\n        typeof thing.annotationType_ === \"string\" &&\n        isFunction(thing.make_) &&\n        isFunction(thing.extend_)\n    )\n}\n\nexport function isAnnotationMapEntry(thing: any) {\n    return typeof thing === \"boolean\" || isAnnotation(thing)\n}\n", "/**\n * (c) <PERSON> 2015 - 2020\n * MIT Licensed\n *\n * Welcome to the mobx sources! To get an global overview of how MobX internally works,\n * this is a good place to start:\n * https://medium.com/@mweststrate/becoming-fully-reactive-an-in-depth-explanation-of-mobservable-55995262a254#.xvbh6qd74\n *\n * Source folders:\n * ===============\n *\n * - api/     Most of the public static methods exposed by the module can be found here.\n * - core/    Implementation of the MobX algorithm; atoms, derivations, reactions, dependency trees, optimizations. Cool stuff can be found here.\n * - types/   All the magic that is need to have observable objects, arrays and values is in this folder. Including the modifiers like `asFlat`.\n * - utils/   Utility stuff.\n *\n */\nimport { die } from \"./errors\"\nimport { getGlobal } from \"./utils/global\"\n;[\"Symbol\", \"Map\", \"Set\", \"Symbol\"].forEach(m => {\n    let g = getGlobal()\n    if (typeof g[m] === \"undefined\") {\n        die(`MobX requires global '${m}' to be available or polyfilled`)\n    }\n})\n\nimport { spy, getDebugName, $mobx } from \"./internal\"\n\nexport {\n    IObservable,\n    IDepTreeNode,\n    Reaction,\n    IReactionPublic,\n    IReactionDisposer,\n    untracked,\n    IAtom,\n    createAtom,\n    spy,\n    IComputedValue,\n    IEqualsComparer,\n    comparer,\n    IEnhancer,\n    IInterceptable,\n    IInterceptor,\n    IListenable,\n    IObjectWillChange,\n    IObjectDidChange,\n    isObservableObject,\n    IValueDidChange,\n    IValueWillChange,\n    IObservableValue,\n    isObservableValue as isBoxedObservable,\n    IObservableArray,\n    IArrayWillChange,\n    IArrayWillSplice,\n    IArraySplice,\n    IArrayUpdate,\n    IArrayDidChange,\n    isObservableArray,\n    IKeyValueMap,\n    ObservableMap,\n    IMapEntries,\n    IMapEntry,\n    IMapWillChange,\n    IMapDidChange,\n    isObservableMap,\n    IObservableMapInitialValues,\n    ObservableSet,\n    isObservableSet,\n    ISetDidChange,\n    ISetWillChange,\n    IObservableSetInitialValues,\n    transaction,\n    observable,\n    IObservableFactory,\n    CreateObservableOptions,\n    computed,\n    IComputedFactory,\n    isObservable,\n    isObservableProp,\n    isComputed,\n    isComputedProp,\n    extendObservable,\n    observe,\n    intercept,\n    autorun,\n    IAutorunOptions,\n    reaction,\n    IReactionOptions,\n    when,\n    IWhenOptions,\n    action,\n    isAction,\n    runInAction,\n    IActionFactory,\n    keys,\n    values,\n    entries,\n    set,\n    remove,\n    has,\n    get,\n    apiOwnKeys as ownKeys,\n    apiDefineProperty as defineProperty,\n    configure,\n    onBecomeObserved,\n    onBecomeUnobserved,\n    flow,\n    isFlow,\n    flowResult,\n    FlowCancellationError,\n    isFlowCancellationError,\n    toJS,\n    trace,\n    IObserverTree,\n    IDependencyTree,\n    getDependencyTree,\n    getObserverTree,\n    resetGlobalState as _resetGlobalState,\n    getGlobalState as _getGlobalState,\n    getDebugName,\n    getAtom,\n    getAdministration as _getAdministration,\n    allowStateChanges as _allowStateChanges,\n    runInAction as _allowStateChangesInsideComputed, // This has become the default behavior in Mobx 6\n    Lambda,\n    $mobx,\n    isComputingDerivation as _isComputingDerivation,\n    onReactionError,\n    interceptReads as _interceptReads,\n    IComputedValueOptions,\n    IActionRunInfo,\n    _startAction,\n    _endAction,\n    allowStateReadsStart as _allowStateReadsStart,\n    allowStateReadsEnd as _allowStateReadsEnd,\n    makeObservable,\n    makeAutoObservable,\n    autoAction as _autoAction,\n    AnnotationsMap,\n    AnnotationMapEntry,\n    override\n} from \"./internal\"\n\n// Devtools support\ndeclare const __MOBX_DEVTOOLS_GLOBAL_HOOK__: { injectMobx: (any) => void }\nif (typeof __MOBX_DEVTOOLS_GLOBAL_HOOK__ === \"object\") {\n    // See: https://github.com/andykog/mobx-devtools/\n    __MOBX_DEVTOOLS_GLOBAL_HOOK__.injectMobx({\n        spy,\n        extras: {\n            getDebugName\n        },\n        $mobx\n    })\n}\n"], "mappings": ";AAAA,IAAMA,aAAa;EACf,GAAA;EACA,GAFe,SAAA,EAEbC,gBAAgBC,KAFH;AAGX,WAAA,mBAAwBD,iBAAxB,WAA+CC,IAAIC,SAAJ,IAA/C;EACH;;;;;;;;;;;;EAYD,GAAG;EACH,GAAG;EACH,GAAG;EACH,GAAG;EACH,GAAG;EACH,IAAI;EACJ,IAAI;EACJ,IAAA;EACA,IAAA;EACA,IAAI;EACJ,IAAA;EACA,IAAA;EACA,IA5Be,SAAAC,GA4BZC,OAAOC,QA5BK;AA6BX,WAAA,uCAA4CD,QAA5C,qBAAoEC;EACvE;EACD,IAAI;EACJ,IAhCe,SAAAF,GAgCZG,OAhCY;AAiCX,WAAO,2DAA2DA,MAAMC,YAAYC;EACvF;EACD,IAnCe,SAAAL,GAmCZG,OAnCY;AAoCX,WAAO,gCAAgCA;EAC1C;EACD,IAtCe,SAAAH,GAsCZM,eAtCY;AAuCX,WAAA,iCAAsCA,gBAAtC;EACH;EACD,IAAI;EACJ,IAAI;EACJ,IA3Ce,SAAAN,GA2CZO,OA3CY;AA4CX,WAAO,uCAAuCA;EACjD;EACD,IA9Ce,SAAAP,GA8CZQ,UAAUH,MA9CE;AA+CX,WAAA,gBAAqBG,WAArB,6CAAwEH,OAAxE;EACH;EACD,IAAI;EACJ,IAlDe,SAAAL,GAkDZQ,UAAUH,MAlDE;AAmDX,WAAA,6BAAkCG,SAAST,SAAT,IAAlC,uCAA0FM,OAA1F;EACH;EACD,IArDe,SAAAL,GAqDZO,OArDY;AAsDX,WAAO,6BAA6BA;EACvC;EACD,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IA3De,SAAAP,IA2DZK,MAAMI,YA3DM;AA4DX,WAAA,mCAAwCJ,OAAxC,OAAiDI;EACpD;EACD,IA9De,SAAAT,IA8DZK,MA9DY;AA+DX,WAAA,mCAAwCA,OAAxC;EACH;EACD,IAjEe,SAAAL,IAiEZK,MAjEY;AAkEX,WAAA,qBAA0BA,OAA1B;EACH;EACD,IAAI;EACJ,IAAI;EACJ,IAtEe,SAAAL,IAsEZU,QAtEY;AAuEX,WAAA,6BAAmCA,SAAnC,kGAA2IA,SAA3I;EACH;EACD,IAAI;EACJ,IAAI;AA1EW;AA6EnB,IAAMC,SAA4B,OAAUf,aAAc,CAAA;SAE1CgB,IAAIC,OAAAA;oCAAwCC,OAAAA,IAAAA,MAAAA,OAAAA,IAAAA,OAAAA,IAAAA,CAAAA,GAAAA,OAAAA,GAAAA,OAAAA,MAAAA,QAAAA;AAAAA,SAAAA,OAAAA,CAAAA,IAAAA,UAAAA,IAAAA;;AACxD,MAAA,MAAa;AACT,QAAIC,IAAS,OAAOF,UAAU,WAAWA,QAAQF,OAAOE,KAAD;AACvD,QAAI,OAAOE,MAAM;AAAYA,UAAIA,EAAEC,MAAM,MAAMF,IAAd;AACjC,UAAM,IAAIG,MAAJ,YAAoBF,CAApB;EACT;AACD,QAAM,IAAIE,MACN,OAAOJ,UAAU,WAAjB,+BACmCA,SACzBC,KAAKZ,SAAS,MAAMY,KAAKI,IAAIC,MAAT,EAAiBC,KAAK,GAAtB,IAA6B,MAF3D,mGAAA,YAIgBP,KALd;AAOT;ACzFD,IAAMQ,aAAa,CAAA;AAEnB,SAAgBC,YAAAA;AACZ,MAAI,OAAOC,eAAe,aAAa;AACnC,WAAOA;EACV;AACD,MAAI,OAAOC,WAAW,aAAa;AAC/B,WAAOA;EACV;AACD,MAAI,OAAOC,WAAW,aAAa;AAC/B,WAAOA;EACV;AACD,MAAI,OAAOC,SAAS,aAAa;AAC7B,WAAOA;EACV;AACD,SAAOL;AACV;AChBM,IAAMM,SAASC,OAAOD;AACtB,IAAME,gBAAgBD,OAAOE;AAC7B,IAAMC,iBAAiBH,OAAOG;AAC9B,IAAMC,kBAAkBJ,OAAOK;AAE/B,IAAMC,cAAc,CAAA;AAC3BN,OAAOO,OAAOD,WAAd;AAEO,IAAME,eAAe,CAAA;AAC5BR,OAAOO,OAAOC,YAAd;AAOA,IAAMC,WAAW,OAAOC,UAAU;AAClC,IAAMC,oBAAoBX,OAAO7B,SAAP;AAE1B,SAAgByC,gBAAAA;AACZ,MAAI,CAACH,UAAU;AACXzB,QACI,OACM,8HACA,qBAHP;EAKN;AACJ;AAED,SAAgB6B,0BAA0BC,KAAAA;AACtC,MAAeC,YAAYC,eAAe;AACtChC,QACI,kGACI8B,GAFL;EAIN;AACJ;AAED,SAAgBG,YAAAA;AACZ,SAAO,EAAEF,YAAYG;AACxB;AAKD,SAAgBC,KAAKC,MAAAA;AACjB,MAAIC,UAAU;AACd,SAAO,WAAA;AACH,QAAIA;AAAS;AACbA,cAAU;AACV,WAAQD,KAAahC,MAAM,MAAMkC,SAAzB;EACX;AACJ;AAEM,IAAMC,OAAO,SAAPA,QAAO;AAAA;AAEpB,SAAgBC,WAAWC,IAAAA;AACvB,SAAO,OAAOA,OAAO;AACxB;AAED,SAIgBC,YAAYC,OAAAA;AACxB,MAAMC,IAAI,OAAOD;AACjB,UAAQC,GAAR;IACI,KAAK;IACL,KAAK;IACL,KAAK;AACD,aAAO;EAJf;AAMA,SAAO;AACV;AAED,SAAgBC,SAASF,OAAAA;AACrB,SAAOA,UAAU,QAAQ,OAAOA,UAAU;AAC7C;AAED,SAAgBG,cAAcH,OAAAA;;AAC1B,MAAI,CAACE,SAASF,KAAD;AAAS,WAAO;AAC7B,MAAMI,QAAQ/B,OAAOgC,eAAeL,KAAtB;AACd,MAAII,SAAS;AAAM,WAAO;AAC1B,WAAO,qBAAAA,MAAMvD,gBAAN,OAAA,SAAA,mBAAmBL,SAAnB,OAAkCwC;AAC5C;AAGD,SAAgBsB,YAAYC,KAAAA;AACxB,MAAM1D,cAAc0D,OAAH,OAAA,SAAGA,IAAK1D;AACzB,MAAI,CAACA;AAAa,WAAO;AACzB,MAAI,wBAAwBA,YAAYC,QAAQ,wBAAwBD,YAAY2D;AAChF,WAAO;AACX,SAAO;AACV;AAED,SAAgBC,cAAcC,SAAaC,UAAuBX,OAAAA;AAC9DxB,iBAAekC,SAAQC,UAAU;IAC7BC,YAAY;IACZC,UAAU;IACVC,cAAc;IACdd;EAJ6B,CAAnB;AAMjB;AAED,SAAgBe,mBAAmBL,SAAaC,UAAuBX,OAAAA;AACnExB,iBAAekC,SAAQC,UAAU;IAC7BC,YAAY;IACZC,UAAU;IACVC,cAAc;IACdd;EAJ6B,CAAnB;AAMjB;AAED,SAAgBgB,0BACZlE,MACAmE,UAAAA;AAEA,MAAMN,WAAW,WAAW7D;AAC5BmE,WAASvC,UAAUiC,QAAnB,IAA+B;AAC/B,SAAO,SAAUO,GAAV;AACH,WAAOhB,SAASgB,CAAD,KAAOA,EAAEP,QAAD,MAAe;EAClC;AACX;AAED,SAAgBQ,SAASnE,OAAAA;AACrB,SAAOA,iBAAiBoE;AAC3B;AAED,SAAgBC,SAASrE,OAAAA;AACrB,SAAOA,iBAAiBsE;AAC3B;AAED,IAAMC,2BAA2B,OAAOlD,OAAOmD,0BAA0B;AAKzE,SAAgBC,mBAAmBf,SAAAA;AAC/B,MAAMgB,QAAOrD,OAAOqD,KAAKhB,OAAZ;AAEb,MAAI,CAACa;AAA0B,WAAOG;AACtC,MAAMC,UAAUtD,OAAOmD,sBAAsBd,OAA7B;AAChB,MAAI,CAACiB,QAAQhF;AAAQ,WAAO+E;AAC5B,SAAA,CAAA,EAAA,OAAWA,OAASC,QAAQC,OAAO,SAAAC,GAAC;AAAA,WAAIpD,gBAAgBqD,qBAAqBC,KAAKrB,SAAQmB,CAAlD;EAAJ,CAAhB,CAApB;AACH;AAIM,IAAMG,UACT,OAAOC,YAAY,eAAeA,QAAQD,UACpCC,QAAQD,UACRT,2BACA,SAAAhB,KAAG;AAAA,SAAIlC,OAAO6D,oBAAoB3B,GAA3B,EAAgC4B,OAAO9D,OAAOmD,sBAAsBjB,GAA7B,CAAvC;AAAJ;;EACwBlC,OAAO6D;;AAE5C,SAAgBE,aAAa7F,KAAAA;AACzB,MAAI,OAAOA,QAAQ;AAAU,WAAOA;AACpC,MAAI,OAAOA,QAAQ;AAAU,WAAOA,IAAIC,SAAJ;AACpC,SAAO,IAAIoB,OAAOrB,GAAX,EAAgBC,SAAhB;AACV;AAED,SAAgB6F,YAAYrC,OAAAA;AACxB,SAAOA,UAAU,OAAO,OAAO,OAAOA,UAAU,WAAW,KAAKA,QAAQA;AAC3E;AAED,SAAgBsC,QAAQC,QAAgBC,MAAAA;AACpC,SAAO/D,gBAAgBgE,eAAeV,KAAKQ,QAAQC,IAA5C;AACV;AAGM,IAAME,4BACTrE,OAAOqE,6BACP,SAASA,2BAA0BH,QAAnC;AAEI,MAAMI,MAAW,CAAA;AAEjBX,UAAQO,MAAD,EAASK,QAAQ,SAAArG,KAAG;AACvBoG,QAAIpG,GAAD,IAAQ+B,cAAciE,QAAQhG,GAAT;EAC3B,CAFD;AAGA,SAAOoG;AACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrLE,IAAME,0BAA0BC,OAAO,yBAAD;AAO7C,SAAgBC,0BAA0BC,YAAAA;AACtC,WAASC,UAAUV,QAAQtF,UAA3B;AACIiG,oBAAgBX,QAAQtF,UAAU+F,UAAnB;EAClB;AACD,SAAO3E,OAAOD,OAAO6E,WAAWD,UAAzB;AACV;AAMD,SAAgBE,gBAAgBxE,WAAgBnC,KAAkByG,YAAAA;AAC9D,MAAI,CAACV,QAAQ5D,WAAWmE,uBAAZ,GAAsC;AAC9CpC,kBAAc/B,WAAWmE,yBAAZ,SAAA,CAAA,GAENnE,UAAUmE,uBAAD,CAFH,CAAA;EAIhB;AAED,MAAeM,WAAWH,UAAD,KAAgB,CAACV,QAAQ5D,UAAUmE,uBAAD,GAA2BtG,GAArC,GAA2C;AACxF,QAAM6G,YAAe1E,UAAU7B,YAAYC,OAA5B,gBAA8CP,IAAIC,SAAJ;AAC7Da,QACI,MAAI+F,YAAJ,sFADD;EAIN;AAEDC,qBAAmB3E,WAAWsE,YAAYzG,GAAxB;AAGlB,MAAI,CAAC4G,WAAWH,UAAD,GAAc;AACzBtE,cAAUmE,uBAAD,EAA0BtG,GAAnC,IAA0CyG;EAC7C;AACJ;AAED,SAASK,mBAAmB3E,WAAmBsE,YAAwBzG,KAAvE;AACI,MAAe,CAAC4G,WAAWH,UAAD,KAAgBV,QAAQ5D,UAAUmE,uBAAD,GAA2BtG,GAArC,GAA2C;AACxF,QAAM6G,YAAe1E,UAAU7B,YAAYC,OAA5B,gBAA8CP,IAAIC,SAAJ;AAC7D,QAAM8G,wBAAwB5E,UAAUmE,uBAAD,EAA0BtG,GAAnC,EAAwCgH;AACtE,QAAMC,0BAA0BR,WAAWO;AAC3ClG,QACI,oBAAkBmG,0BAAlB,WAAkDJ,YAAlD,QAAA,6CAC+CE,wBAD/C,QAAA,sGADD;EAMN;AACJ;AAKD,SAAgBG,yBAAyBlB,QAAAA;AACrC,MAAI,CAACD,QAAQC,QAAQM,uBAAT,GAAmC;AAC3C,QAAe,CAACN,OAAOM,uBAAD,GAA2B;AAC7CxF,UAAG,+FAAA;IAGN;AAEDoD,kBAAc8B,QAAQM,yBAAT,SAAA,CAAA,GAAuCN,OAAOM,uBAAD,CAA7C,CAAA;EAChB;AACD,SAAON,OAAOM,uBAAD;AAChB;ICxDYa,QAAQZ,OAAO,qBAAD;AAO3B,IAAaa,OAAb,WAAA;AAYI,WAAAA,MAAmBC,OAAnB;QAAmBA,UAAAA,QAAAA;AAAAA,cAAQ,OAAU,UAAUtE,UAAS,IAAK;;SAA1CsE,QAAAA;SAXnBC,0BAA0B;SAC1BC,mBAAmB;SACnBC,aAAa,oBAAIzC,IAAJ;SAEb0C,aAAa;SACbC,kBAAkB;SAClBC,uBAAuBC,kBAAkBC;SAQlCC,QAAAA;SAEAC,SAAAA;AALY,SAAA,QAAAV;EAAoD;AAZ3E,MAAA,SAAAD,MAAA;AAAA,SAmBWY,OAAA,SAAA,OAAA;AACH,QAAI,KAAKF,OAAO;AACZ,WAAKA,MAAMzB,QAAQ,SAAA4B,UAAQ;AAAA,eAAIA,SAAQ;MAAZ,CAA3B;IACH;EACJ;AAvBL,SAyBWC,QAAA,SAAA,QAAA;AACH,QAAI,KAAKH,QAAQ;AACb,WAAKA,OAAO1B,QAAQ,SAAA4B,UAAQ;AAAA,eAAIA,SAAQ;MAAZ,CAA5B;IACH;EACJ;AA7BL,SAmCWE,iBAAA,SAAA,mBAAA;AACH,WAAOA,eAAe,IAAD;EACxB;AArCL,SA0CWC,gBAAA,SAAA,gBAAA;AACHC,eAAU;AACVC,qBAAiB,IAAD;AAChBC,aAAQ;EACX;AA9CL,SAgDItI,WAAA,SAAAA,YAAA;AACI,WAAO,KAAKoH;EACf;AAlDL,SAAAD;AAAA,EAAA;AAqDO,IAAMoB,SAAS/D,0BAA0B,QAAQ2C,IAAT;AAE/C,SAAgBqB,WACZlI,MACAmI,yBACAC,2BAAAA;MADAD,4BAAAA,QAAAA;AAAAA,8BAAsCrF;;MACtCsF,8BAAAA,QAAAA;AAAAA,gCAAwCtF;;AAExC,MAAMuF,OAAO,IAAIxB,KAAK7G,IAAT;AAEb,MAAImI,4BAA4BrF,MAAM;AAClCwF,qBAAiBD,MAAMF,uBAAP;EACnB;AAED,MAAIC,8BAA8BtF,MAAM;AACpCyF,uBAAmBF,MAAMD,yBAAP;EACrB;AACD,SAAOC;AACV;ACvFD,SAASG,iBAAiBC,GAAQC,GAAlC;AACI,SAAOD,MAAMC;AAChB;AAED,SAASC,mBAAmBF,GAAQC,GAApC;AACI,SAAOE,UAAUH,GAAGC,CAAJ;AACnB;AAED,SAASG,gBAAgBJ,GAAQC,GAAjC;AACI,SAAOE,UAAUH,GAAGC,GAAG,CAAP;AACnB;AAED,SAASI,gBAAgBL,GAAQC,GAAjC;AACI,SAAOnH,OAAOwH,GAAGN,GAAGC,CAAb;AACV;AAED,IAAaM,WAAW;EACpBC,UAAUT;EACVU,YAAYP;EACZ,WAASG;EACTK,SAASN;AAJW;SCCRO,aAAaC,GAAG1J,KAAGK,MAAAA;AAE/B,MAAIsJ,aAAaD,CAAD;AAAK,WAAOA;AAG5B,MAAIE,MAAMC,QAAQH,CAAd;AAAkB,WAAOI,WAAWC,MAAML,GAAG;MAAErJ;IAAF,CAApB;AAC7B,MAAIqD,cAAcgG,CAAD;AAAK,WAAOI,WAAW7F,OAAOyF,GAAGM,QAAW;MAAE3J;IAAF,CAAhC;AAC7B,MAAIqE,SAASgF,CAAD;AAAK,WAAOI,WAAW5I,IAAIwI,GAAG;MAAErJ;IAAF,CAAlB;AACxB,MAAIuE,SAAS8E,CAAD;AAAK,WAAOI,WAAWG,IAAIP,GAAG;MAAErJ;IAAF,CAAlB;AACxB,MAAI,OAAOqJ,MAAM,cAAc,CAACQ,SAASR,CAAD,KAAO,CAACS,OAAOT,CAAD,GAAK;AACvD,QAAI7F,YAAY6F,CAAD,GAAK;AAChB,aAAOU,KAAKV,CAAD;IACd,OAAM;AACH,aAAOW,WAAWhK,MAAMqJ,CAAP;IACpB;EACJ;AACD,SAAOA;AACV;AAED,SAAgBY,gBAAgBZ,GAAG1J,KAAGK,MAAAA;AAClC,MAAIqJ,MAAMM,UAAaN,MAAM;AAAM,WAAOA;AAC1C,MAAIa,mBAAmBb,CAAD,KAAOc,kBAAkBd,CAAD,KAAOe,gBAAgBf,CAAD,KAAOgB,gBAAgBhB,CAAD;AACtF,WAAOA;AACX,MAAIE,MAAMC,QAAQH,CAAd;AAAkB,WAAOI,WAAWC,MAAML,GAAG;MAAErJ;MAAMsK,MAAM;IAAd,CAApB;AAC7B,MAAIjH,cAAcgG,CAAD;AAAK,WAAOI,WAAW7F,OAAOyF,GAAGM,QAAW;MAAE3J;MAAMsK,MAAM;IAAd,CAAhC;AAC7B,MAAIjG,SAASgF,CAAD;AAAK,WAAOI,WAAW5I,IAAIwI,GAAG;MAAErJ;MAAMsK,MAAM;IAAd,CAAlB;AACxB,MAAI/F,SAAS8E,CAAD;AAAK,WAAOI,WAAWG,IAAIP,GAAG;MAAErJ;MAAMsK,MAAM;IAAd,CAAlB;AAExB,MAAA;AACI/J,QACI,mGADD;AAGV;AAED,SAAgBgK,kBAAkBC,UAAAA;AAE9B,SAAOA;AACV;AAED,SAAgBC,kBAAkBpB,GAAGqB,UAAAA;AACjC,MAAepB,aAAaD,CAAD;AACvB9I,QAAG,6DAAA;AACP,MAAIqI,UAAUS,GAAGqB,QAAJ;AAAe,WAAOA;AACnC,SAAOrB;AACV;AC1DD,IAAMsB,WAAW;AAEjB,IAAaC,WAA2C3E,0BAA0B;EAC9EQ,iBAAiBkE;EACjBE;EACAC;AAH8E,CAAD;AAMjF,SAAgBzE,WAAWH,YAAAA;AACvB,SAAOA,WAAWO,oBAAoBkE;AACzC;AAED,SAASE,MAAME,KAAqCtL,KAApD;AAEI,MAAesL,IAAIC,gBAAgB;AAC/BzK,QACI,mBAAiB,KAAKkG,kBAAtB,WAA8CsE,IAAIjE,QAAlD,MAA2DrH,IAAIC,SAAJ,IAA3D,QAAA,QACU,KAAK+G,kBADf,qCADD;EAIN;AAED,MAAe,CAACjB,QAAQuF,IAAIE,qBAAsBxL,GAA3B,GAAiC;AACpDc,QACI,MAAIwK,IAAIjE,QAAR,MAAiBrH,IAAIC,SAAJ,IAAjB,0BAAuD,KAAK+G,kBAA5D,yDADD;EAIN;AACD,SAAA;AACH;AAED,SAASqE,QAAQC,KAAKtL,KAAKyL,YAAYC,WAAvC;AACI5K,MAAG,MAAK,KAAKkG,kBAAV,0CAAA;AACN;SC7Be2E,uBAAuBpL,MAAcqL,SAAAA;AACjD,SAAO;IACH5E,iBAAiBzG;IACjBsL,UAAUD;IACVR,OAAAA;IACAC,SAAAA;EAJG;AAMV;AAED,SAASD,QACLE,KACAtL,KACAyL,YACAK,QAJJ;;AAOI,OAAA,iBAAI,KAAKD,aAAT,OAAA,SAAI,eAAeE,OAAO;AACtB,WAAO,KAAKV,QAAQC,KAAKtL,KAAKyL,YAAY,KAAnC,MAA8C,OAA9C,IAAA;EAGV;AAED,MAAIK,WAAWR,IAAIU,SAAS;AACxB,WAAO,KAAKX,QAAQC,KAAKtL,KAAKyL,YAAY,KAAnC,MAA8C,OAA9C,IAAA;EAGV;AAED,MAAIrB,SAASqB,WAAWhI,KAAZ,GAAoB;AAG5B,WAAA;EACH;AACD,MAAMwI,mBAAmBC,uBAAuBZ,KAAK,MAAMtL,KAAKyL,YAAY,KAA7B;AAC/CxJ,iBAAe6J,QAAQ9L,KAAKiM,gBAAd;AACd,SAAA;AACH;AAED,SAASZ,UACLC,KACAtL,KACAyL,YACAC,WAJJ;AAMI,MAAMO,mBAAmBC,uBAAuBZ,KAAK,MAAMtL,KAAKyL,UAAjB;AAC/C,SAAOH,IAAIa,gBAAgBnM,KAAKiM,kBAAkBP,SAA3C;AACV;AAED,SAASU,uBACLd,KADJ,MAGItL,KAHJ,OAAA;MAEMgH,kBAAAA,KAAAA;MAEAvD,QAAAA,MAAAA;AAEF,MAAe,CAACH,WAAWG,KAAD,GAAS;AAC/B3C,QACI,mBAAiBkG,kBAAjB,WAAyCsE,IAAIjE,QAA7C,MAAsDrH,IAAIC,SAAJ,IAAtD,QAAA,QACU+G,kBADV,0DADD;EAIN;AACJ;AAED,SAAgBkF,uBACZZ,KACA7E,YACAzG,KACAyL,YAEAY,iBAAAA;;MAAAA,oBAAAA,QAAAA;AAAAA,sBAA2BxJ,YAAYwJ;;AAEvCD,yBAAuBd,KAAK7E,YAAYzG,KAAKyL,UAAvB;MAChBhI,QAAUgI,WAAVhI;AACN,OAAA,uBAAIgD,WAAWoF,aAAf,OAAA,SAAI,qBAAqBE,OAAO;AAAA,QAAA;AAC5BtI,YAAQA,MAAM6I,MAAN,cAAWhB,IAAIiB,WAAf,OAAA,cAAyBjB,IAAIU,OAA7B;EACX;AACD,SAAO;IACHvI,OAAO+I,cAAY,yBAAA,wBACf/F,WAAWoF,aADI,OAAA,SACf,sBAAqBtL,SADN,OAAA,wBACcP,IAAIC,SAAJ,GAC7BwD,QAFe,0BAAA,wBAGfgD,WAAWoF,aAHI,OAAA,SAGf,sBAAqBtB,eAHN,OAAA,yBAGoB,KAHpB;;;IAOnBhG,cAAc8H,kBAAkBf,IAAIC,iBAAiB;;IAErDlH,YAAY;;;IAGZC,UAAU+H,kBAAkB,QAAQ;EAbjC;AAeV;SC1FeI,qBAAqBlM,MAAcqL,SAAAA;AAC/C,SAAO;IACH5E,iBAAiBzG;IACjBsL,UAAUD;IACVR,OAAAA;IACAC,SAAAA;EAJG;AAMV;AAED,SAASD,QACLE,KACAtL,KACAyL,YACAK,QAJJ;;AAOI,MAAIA,WAAWR,IAAIU,SAAS;AACxB,WAAO,KAAKX,QAAQC,KAAKtL,KAAKyL,YAAY,KAAnC,MAA8C,OAA9C,IAAA;EAGV;AAGD,QAAI,iBAAA,KAAKI,aAAL,OAAA,SAAA,eAAeE,UAAS,CAAC1B,OAAOiB,IAAIU,QAAQhM,GAAZ,CAAD,GAAoB;AACnD,QAAI,KAAKqL,QAAQC,KAAKtL,KAAKyL,YAAY,KAAnC,MAA8C;AAAM,aAAA;EAC3D;AACD,MAAIpB,OAAOoB,WAAWhI,KAAZ,GAAoB;AAG1B,WAAA;EACH;AACD,MAAMiJ,iBAAiBC,qBAAqBrB,KAAK,MAAMtL,KAAKyL,YAAY,OAAO,KAApC;AAC3CxJ,iBAAe6J,QAAQ9L,KAAK0M,cAAd;AACd,SAAA;AACH;AAED,SAASrB,UACLC,KACAtL,KACAyL,YACAC,WAJJ;;AAMI,MAAMgB,iBAAiBC,qBAAqBrB,KAAK,MAAMtL,KAAKyL,aAAjB,kBAA6B,KAAKI,aAAlC,OAAA,SAA6B,gBAAeE,KAA5C;AAC3C,SAAOT,IAAIa,gBAAgBnM,KAAK0M,gBAAgBhB,SAAzC;AACV;AAED,SAASkB,qBACLtB,KADJ,MAGItL,KAHJ,OAAA;MAEMgH,kBAAAA,KAAAA;MAEAvD,QAAAA,MAAAA;AAEF,MAAe,CAACH,WAAWG,KAAD,GAAS;AAC/B3C,QACI,mBAAiBkG,kBAAjB,WAAyCsE,IAAIjE,QAA7C,MAAsDrH,IAAIC,SAAJ,IAAtD,QAAA,QACU+G,kBADV,oEADD;EAIN;AACJ;AAED,SAAS2F,qBACLrB,KACA7E,YACAzG,KACAyL,YACAM,OAEAM,iBAPJ;MAOIA,oBAAAA,QAAAA;AAAAA,sBAA2BxJ,YAAYwJ;;AAEvCO,uBAAqBtB,KAAK7E,YAAYzG,KAAKyL,UAAvB;MACdhI,QAAUgI,WAAVhI;AACN,MAAIsI,OAAO;AAAA,QAAA;AACPtI,YAAQA,MAAM6I,MAAN,cAAWhB,IAAIiB,WAAf,OAAA,cAAyBjB,IAAIU,OAA7B;EACX;AACD,SAAO;IACHvI,OAAO6G,KAAK7G,KAAD;;;IAGXc,cAAc8H,kBAAkBf,IAAIC,iBAAiB;;IAErDlH,YAAY;;;IAGZC,UAAU+H,kBAAkB,QAAQ;EATjC;AAWV;SC/FeQ,yBAAyBtM,MAAcqL,SAAAA;AACnD,SAAO;IACH5E,iBAAiBzG;IACjBsL,UAAUD;IACVR,OAAAA;IACAC,SAAAA;EAJG;AAMV;AAED,SAASD,QACLE,KACAtL,KACAyL,YAHJ;AAKI,SAAO,KAAKJ,QAAQC,KAAKtL,KAAKyL,YAAY,KAAnC,MAA8C,OAA9C,IAAA;AACV;AAED,SAASJ,UACLC,KACAtL,KACAyL,YACAC,WAJJ;AAMIoB,2BAAyBxB,KAAK,MAAMtL,KAAKyL,UAAjB;AACxB,SAAOH,IAAIyB,wBACP/M,KADG,SAAA,CAAA,GAGI,KAAK6L,UAHT;IAICmB,KAAKvB,WAAWuB;IAChB7C,KAAKsB,WAAWtB;EALjB,CAAA,GAOHuB,SAPG;AASV;AAED,SAASoB,yBACLxB,KADJ,MAGItL,KAHJ,OAAA;MAEMgH,kBAAAA,KAAAA;MAEAgG,OAAAA,MAAAA;AAEF,MAAe,CAACA,MAAK;AACjBlM,QACI,mBAAiBkG,kBAAjB,WAAyCsE,IAAIjE,QAA7C,MAAsDrH,IAAIC,SAAJ,IAAtD,QAAA,QACU+G,kBADV,oDADD;EAIN;AACJ;SCzCeiG,2BAA2B1M,MAAcqL,SAAAA;AACrD,SAAO;IACH5E,iBAAiBzG;IACjBsL,UAAUD;IACVR,OAAAA;IACAC,SAAAA;EAJG;AAMV;AAED,SAASD,QACLE,KACAtL,KACAyL,YAHJ;AAKI,SAAO,KAAKJ,QAAQC,KAAKtL,KAAKyL,YAAY,KAAnC,MAA8C,OAA9C,IAAA;AACV;AAED,SAASJ,UACLC,KACAtL,KACAyL,YACAC,WAJJ;;AAMIwB,6BAA2B5B,KAAK,MAAMtL,KAAKyL,UAAjB;AAC1B,SAAOH,IAAI6B,0BACPnN,KACAyL,WAAWhI,QAFR,yBAAA,iBAGH,KAAKoI,aAHF,OAAA,SAGH,eAAeuB,aAHZ,OAAA,wBAGwBzD,cAC3B+B,SAJG;AAMV;AAED,SAASwB,2BACL5B,KADJ,MAGItL,KACAyL,YAJJ;MAEMzE,kBAAAA,KAAAA;AAIF,MAAe,EAAE,WAAWyE,aAAa;AACrC3K,QACI,mBAAiBkG,kBAAjB,WAAyCsE,IAAIjE,QAA7C,MAAsDrH,IAAIC,SAAJ,IAAtD,QAAA,QACU+G,kBADV,+CADD;EAIN;AACJ;ACtCD,IAAMqG,OAAO;AAEN,IAAMC,iBAA6BC,qBAAoB;AAE9D,SAAgBA,qBAAqB3B,SAAAA;AACjC,SAAO;IACH5E,iBAAiBqG;IACjBxB,UAAUD;IACVR,OAAAA;IACAC,SAAAA;EAJG;AAMV;AAED,SAASD,QACLE,KACAtL,KACAyL,YACAK,QAJJ;;AAOI,MAAIL,WAAWuB,KAAK;AAChB,WAAOQ,SAASpC,MAAME,KAAKtL,KAAKyL,YAAYK,MAArC;EACV;AAED,MAAIL,WAAWtB,KAAK;AAEhB,QAAMA,OAAMqC,aAAaxM,IAAIC,SAAJ,GAAgBwL,WAAWtB,GAA5B;AAExB,QAAI2B,WAAWR,IAAIU,SAAS;AACxB,aAAOV,IAAIa,gBAAgBnM,KAAK;QAC5BuE,cAAc1B,YAAYwJ,kBAAkBf,IAAIC,iBAAiB;QACjEpB,KAAAA;MAF4B,CAAzB,MAGA,OAHA,IAAA;IAMV;AAEDlI,mBAAe6J,QAAQ9L,KAAK;MACxBuE,cAAc;MACd4F,KAAAA;IAFwB,CAAd;AAId,WAAA;EACH;AAED,MAAI2B,WAAWR,IAAIU,WAAW,OAAOP,WAAWhI,UAAU,YAAY;AAAA,QAAA;AAClE,QAAIM,YAAY0H,WAAWhI,KAAZ,GAAoB;AAAA,UAAA;AAC/B,UAAMgK,oBAAiB,iBAAA,KAAK5B,aAAL,OAAA,SAAA,eAAe6B,YAAWpD,KAAKyB,QAAQzB;AAC9D,aAAOmD,gBAAerC,MAAME,KAAKtL,KAAKyL,YAAYK,MAA3C;IACV;AACD,QAAM6B,sBAAmB,kBAAA,KAAK9B,aAAL,OAAA,SAAA,gBAAe6B,YAAWnD,WAAWwB,QAAQxB;AACtE,WAAOoD,kBAAiBvC,MAAME,KAAKtL,KAAKyL,YAAYK,MAA7C;EACV;AAID,MAAI8B,0BAAuB,kBAAA,KAAK/B,aAAL,OAAA,SAAA,gBAAehB,UAAS,QAAQb,WAAW6D,MAAM7D;AAE5E,MAAI,OAAOyB,WAAWhI,UAAU,gBAA5B,kBAA0C,KAAKoI,aAA/C,OAAA,SAA0C,gBAAe6B,WAAU;AAAA,QAAA;AACnEjC,eAAWhI,QAAQgI,WAAWhI,MAAM6I,MAAjB,cAAsBhB,IAAIiB,WAA1B,OAAA,cAAoCjB,IAAIU,OAAxC;EACtB;AACD,SAAO4B,sBAAqBxC,MAAME,KAAKtL,KAAKyL,YAAYK,MAAjD;AACV;AAED,SAAST,UACLC,KACAtL,KACAyL,YACAC,WAJJ;;AAOI,MAAID,WAAWuB,KAAK;AAChB,WAAOQ,SAASnC,QAAQC,KAAKtL,KAAKyL,YAAYC,SAAvC;EACV;AAED,MAAID,WAAWtB,KAAK;AAEhB,WAAOmB,IAAIa,gBACPnM,KACA;MACIuE,cAAc1B,YAAYwJ,kBAAkBf,IAAIC,iBAAiB;MACjEpB,KAAKqC,aAAaxM,IAAIC,SAAJ,GAAgBwL,WAAWtB,GAA5B;IAFrB,GAIAuB,SANG;EAQV;AAGD,MAAI,OAAOD,WAAWhI,UAAU,gBAA5B,kBAA0C,KAAKoI,aAA/C,OAAA,SAA0C,gBAAe6B,WAAU;AAAA,QAAA;AACnEjC,eAAWhI,QAAQgI,WAAWhI,MAAM6I,MAAjB,eAAsBhB,IAAIiB,WAA1B,OAAA,eAAoCjB,IAAIU,OAAxC;EACtB;AACD,MAAI4B,0BAAuB,kBAAA,KAAK/B,aAAL,OAAA,SAAA,gBAAehB,UAAS,QAAQb,WAAW6D,MAAM7D;AAC5E,SAAO4D,sBAAqBvC,QAAQC,KAAKtL,KAAKyL,YAAYC,SAAnD;AACV;ACxDM,IAAMoC,iCAA0D;EACnEjD,MAAM;EACNtK,MAAM2J;EACN6D,kBAAkB7D;EAClB8D,OAAO;AAJ4D;AAMvElM,OAAOO,OAAOyL,8BAAd;AAEA,SAAgBG,0BAA0BxN,OAAAA;AACtC,SAAOA,SAASqN;AACnB;AAED,IAAMF,uBAAuBX,2BAA2B,YAAD;AACvD,IAAMiB,0BAA0BjB,2BAA2B,kBAAkB;EACzEG,UAAUtC;AAD+D,CAAnB;AAG1D,IAAMqD,8BAA8BlB,2BAA2B,sBAAsB;EACjFG,UAAU5C;AADuE,CAAvB;AAG9D,IAAM4D,6BAA6BnB,2BAA2B,qBAAqB;EAC/EG,UAAUpC;AADqE,CAAtB;AAG7D,IAAMqD,gCAAgC7H,0BAA0BoH,oBAAD;AAE/D,SAAgBU,uBAAuB1C,SAAAA;AACnC,SAAOA,QAAQf,SAAS,OAClBlB,eACAiC,QAAQf,SAAS,QACjBC,oBACAyD,0BAA0B3C,QAAQmC,gBAAT;AAClC;AAED,SAAgBS,yBACZ5C,SAAAA;;AAEA,SAAOA,WAAO,wBAAGA,QAAQmC,qBAAX,OAAA,wBAA+BR,qBAAqB3B,OAAD,IAAY1B;AAChF;AAED,SAAgBqE,0BAA0B9H,YAAAA;;AACtC,SAAO,CAACA,aAAakD,gBAAd,yBAAA,uBAA6BlD,WAAWoF,aAAxC,OAAA,SAA6B,qBAAqBuB,aAAlD,OAAA,wBAA8DzD;AACxE;AAMD,SAAS8E,iBAAiB7E,GAAQ8E,MAAYC,MAA9C;AAEI,MAAInL,YAAYkL,IAAD,GAAQ;AACnB/H,oBAAgBiD,GAAG8E,MAAMd,oBAAV;AACf;EACH;AAGD,MAAI/D,aAAaD,CAAD;AAAK,WAAOA;AAG5B,MAAIhG,cAAcgG,CAAD;AAAK,WAAOI,WAAW7F,OAAOyF,GAAG8E,MAAMC,IAA3B;AAG7B,MAAI7E,MAAMC,QAAQH,CAAd;AAAkB,WAAOI,WAAWC,MAAML,GAAG8E,IAApB;AAG7B,MAAI9J,SAASgF,CAAD;AAAK,WAAOI,WAAW5I,IAAIwI,GAAG8E,IAAlB;AAGxB,MAAI5J,SAAS8E,CAAD;AAAK,WAAOI,WAAWG,IAAIP,GAAG8E,IAAlB;AAGxB,MAAI,OAAO9E,MAAM,YAAYA,MAAM;AAAM,WAAOA;AAGhD,SAAOI,WAAW4E,IAAIhF,GAAG8E,IAAlB;AACV;AACD5M,OAAOD,OAAO4M,kBAAkBJ,6BAAhC;AAwCA,IAAMQ,sBAA0C;EAC5CD,KAD4C,SAAA,IAC/BnL,OAAWmI,SADoB;AAExC,QAAMkD,IAAIb,0BAA0BrC,OAAD;AACnC,WAAO,IAAImD,gBAAgBtL,OAAO6K,uBAAuBQ,CAAD,GAAKA,EAAEvO,MAAM,MAAMuO,EAAEE,MAAtE;EACV;EACD/E,OAL4C,SAAA,MAK7BgF,eAAqBrD,SALQ;AAMxC,QAAMkD,IAAIb,0BAA0BrC,OAAD;AACnC,YAAQ/I,YAAYqM,eAAe,SAASJ,EAAEd,UAAU,QAClDmB,oBACAC,uBAAuBH,eAAeX,uBAAuBQ,CAAD,GAAKA,EAAEvO,IAFlE;EAGV;EACDa,KAX4C,SAAA,IAYxC6N,eACArD,SAbwC;AAexC,QAAMkD,IAAIb,0BAA0BrC,OAAD;AACnC,WAAO,IAAIyD,cAAoBJ,eAAeX,uBAAuBQ,CAAD,GAAKA,EAAEvO,IAApE;EACV;EACD4J,KAlB4C,SAAA,IAmBxC8E,eACArD,SApBwC;AAsBxC,QAAMkD,IAAIb,0BAA0BrC,OAAD;AACnC,WAAO,IAAI0D,cAAiBL,eAAeX,uBAAuBQ,CAAD,GAAKA,EAAEvO,IAAjE;EACV;EACD4D,QAzB4C,SAAA,OA0BxCoL,OACAC,YACA5D,SA5BwC;AA8BxC,WAAO6D,iBACH5M,YAAYqM,eAAe,UAAStD,WAAO,OAAP,SAAAA,QAASoC,WAAU,QACjD0B,mBAAmB,CAAA,GAAI9D,OAAL,IAClB+D,0BAA0B,CAAA,GAAI/D,OAAL,GAC/B2D,OACAC,UALmB;EAO1B;EACD3B,KAAKrH,0BAA0B0H,uBAAD;EAC9BxE,SAASlD,0BAA0B2H,2BAAD;EAClCtD,MAAMwD;EACNuB,QAAQpJ,0BAA0B4H,0BAAD;AAzCW;AA6ChD,IAAWpE,aAAiCnI,OAAO4M,kBAAkBI,mBAAnB;AClM3C,IAAMgB,WAAW;AACjB,IAAMC,kBAAkB;AAW/B,IAAMC,qBAAqBlD,yBAAyBgD,QAAD;AACnD,IAAMG,2BAA2BnD,yBAAyBiD,iBAAiB;EACvEd,QAAQzF,SAASE;AADsD,CAAlB;AAQzD,IAAa+D,WAA6B,SAASA,UAASyC,MAAMvB,MAAxB;AACtC,MAAIlL,YAAYkL,IAAD,GAAQ;AAEnB,WAAO/H,gBAAgBsJ,MAAMvB,MAAMqB,kBAAb;EACzB;AACD,MAAInM,cAAcqM,IAAD,GAAQ;AAErB,WAAOzJ,0BAA0BqG,yBAAyBgD,UAAUI,IAAX,CAAzB;EACnC;AAGD,MAAA,MAAa;AACT,QAAI,CAAC3M,WAAW2M,IAAD;AAAQnP,UAAI,uDAAD;AAC1B,QAAIwC,WAAWoL,IAAD;AACV5N,UACI,sFADD;EAGV;AACD,MAAMoP,OAAmCtM,cAAc8K,IAAD,IAASA,OAAO,CAAA;AACtEwB,OAAKlD,MAAMiD;AACXC,OAAK3P,SAAL2P,KAAK3P,OAAS0P,KAAK1P,QAAQ;AAE3B,SAAO,IAAI4P,cAAcD,IAAlB;AACH;AAERpO,OAAOD,OAAO2L,UAAUuC,kBAAxB;AAEAvC,SAASoC,SAASpJ,0BAA0BwJ,wBAAD;;;AC1C3C,IAAII,kBAAkB;AACtB,IAAIC,eAAe;AACnB,IAAMC,8BAA0B,yBAAA,iBAAGvO,cAAc,WAAA;AAAA,GAAU,MAAX,MAAhB,OAAA,SAAG,eAAiCwC,iBAApC,OAAA,wBAAoD;AAGpF,IAAMgM,oBAAwC;EAC1C9M,OAAO;EACPc,cAAc;EACdD,UAAU;EACVD,YAAY;AAJ8B;AAO9C,SAAgBmI,aACZgE,YACAjN,IACAgH,aACAsD,KAAAA;MADAtD,gBAAAA,QAAAA;AAAAA,IAAAA,cAAsB;;AAGtB,MAAA,MAAa;AACT,QAAI,CAACjH,WAAWC,EAAD;AAAMzC,UAAI,2CAAD;AACxB,QAAI,OAAO0P,eAAe,YAAY,CAACA;AACnC1P,UAAG,4CAA2C0P,aAA3C,GAAA;EACV;AACD,WAASpK,MAAT;AACI,WAAOqK,cAAcD,YAAYjG,aAAYhH,IAAIsK,OAAO,MAAMzK,SAA1C;EACvB;AACDgD,MAAIsK,eAAe;AACnB,MAAIJ,4BAA4B;AAC5BC,sBAAkB9M,QAAQ+M;AAC1B1O,WAAOG,eAAemE,KAAK,QAAQmK,iBAAnC;EACH;AACD,SAAOnK;AACV;AAED,SAAgBqK,cACZD,YACAG,oBACApN,IACAqN,OACA5P,MAAAA;AAEA,MAAM6P,UAAUC,aAAaN,YAAYG,oBAAoBC,OAAO5P,IAAxC;AAC5B,MAAI;AACA,WAAOuC,GAAGrC,MAAM0P,OAAO5P,IAAhB;EACV,SAAQ+P,KAAK;AACVF,YAAQG,SAASD;AACjB,UAAMA;EACT,UALD;AAMIE,eAAWJ,OAAD;EACb;AACJ;AAcD,SAAgBC,aACZN,YACAG,oBACAC,OACA5P,MAAAA;AAEA,MAAMkQ,aAAwBC,aAAY,KAAM,CAAC,CAACX;AAClD,MAAIY,aAAqB;AACzB,MAAeF,YAAY;AACvBE,iBAAaC,KAAKC,IAAL;AACb,QAAMC,gBAAgBvQ,OAAO8I,MAAM0H,KAAKxQ,IAAX,IAAmBoB;AAChDqP,mBAAe;MACXC,MAAMC;MACNpR,MAAMiQ;MACNrM,QAAQyM;MACRxN,WAAWmO;IAJA,CAAD;EAMjB;AACD,MAAMK,kBAAkB/O,YAAYgP;AACpC,MAAMC,cAAc,CAACnB,sBAAsB,CAACiB;AAC5CvJ,aAAU;AACV,MAAI0J,yBAAyBlP,YAAYmP;AACzC,MAAIF,aAAa;AACbG,mBAAc;AACdF,6BAAyBG,uBAAuB,IAAD;EAClD;AACD,MAAMC,uBAAuBC,qBAAqB,IAAD;AACjD,MAAMvB,UAAU;IACZwB,cAAcP;IACdF;IACAG;IACAI;IACAjB;IACAE;IACAkB,WAAWjC;IACXkC,iBAAiBnC;EARL;AAUhBA,oBAAkBS,QAAQyB;AAC1B,SAAOzB;AACV;AAED,SAAgBI,WAAWJ,SAAAA;AACvB,MAAIT,oBAAoBS,QAAQyB,WAAW;AACvCxR,QAAI,EAAD;EACN;AACDsP,oBAAkBS,QAAQ0B;AAE1B,MAAI1B,QAAQG,WAAW9G,QAAW;AAC9BrH,gBAAY2P,yBAAyB;EACxC;AACDC,uBAAqB5B,QAAQkB,sBAAT;AACpBW,qBAAmB7B,QAAQsB,oBAAT;AAClB5J,WAAQ;AACR,MAAIsI,QAAQwB;AAAcM,iBAAa9B,QAAQe,eAAT;AACtC,MAAef,QAAQK,YAAY;AAC/B0B,iBAAa;MAAEC,MAAMxB,KAAKC,IAAL,IAAaT,QAAQO;IAA7B,CAAD;EACf;AACDvO,cAAY2P,yBAAyB;AACxC;AAED,SAAgBR,kBAAqBA,oBAA4B9O,MAAAA;AAC7D,MAAM4P,OAAOZ,uBAAuBF,kBAAD;AACnC,MAAI;AACA,WAAO9O,KAAI;EACd,UAFD;AAGIuP,yBAAqBK,IAAD;EACvB;AACJ;AAED,SAAgBZ,uBAAuBF,oBAAAA;AACnC,MAAMc,OAAOjQ,YAAYmP;AACzBnP,cAAYmP,oBAAoBA;AAChC,SAAOc;AACV;AAED,SAAgBL,qBAAqBK,MAAAA;AACjCjQ,cAAYmP,oBAAoBc;AACnC;;ACvGD,IAAMC,SAAS;sBA8HVxM,OAAOT;AA5HZ,IAAaiJ,kBAAb,SAAA,OAAA;AAAA,iBAAAA,kBAAA,KAAA;AASI,WAAAA,iBACItL,OACO2J,UACA/F,OACP2L,WACQhE,QALZ;;QAGW3H,UAAAA,QAAAA;AAAAA,cAAQ,OAAU,qBAAqBtE,UAAS,IAAK;;QAC5DiQ,cAAAA,QAAAA;AAAAA,kBAAY;;QACJhE,WAAAA,QAAAA;AAAAA,eAA+BzF,SAAQ,SAAA;;AAE/C,YAAA,MAAA,KAAA,MAAMlC,KAAN,KAAA;UALO+F,WAAAA;UACA/F,QAAAA;UAEC2H,SAAAA;UAXZiE,uBAAuB;UACvBC,gBAAAA;UACAC,mBAAAA;UACAC,SAAAA;UACAC,WAAAA;AAIW,UAAA,WAAAjG;AACA,UAAA,QAAA/F;AAEC,UAAA,SAAA2H;AAGR,UAAKoE,SAAShG,SAAS3J,OAAOyG,QAAW7C,KAAnB;AACtB,QAAe2L,aAAa7B,aAAY,GAAI;AAExCmC,gBAAU;QACN5B,MAAMqB;QACN5O,QAAM,uBAAA,KAAA;QACNoP,gBAAgB;QAChBC,iBAAiB,MAAKnM;QACtB0D,UAAU,KAAK,MAAKqI;MALd,CAAD;IAOZ;;EACJ;AA5BL,MAAA,SAAArE,iBAAA;AAAA,SA8BY0E,eAAA,SAAA,aAAahQ,OAAb;AACJ,QAAI,KAAK4P,aAAanJ;AAAW,aAAO,KAAKmJ,SAAS5P,KAAd;AACxC,WAAOA;EACV;AAjCL,SAmCW0G,MAAA,SAAAA,KAAIY,UAAJ;AACH,QAAME,WAAW,KAAKmI;AACtBrI,eAAW,KAAK2I,iBAAiB3I,QAAtB;AACX,QAAIA,aAAalI,YAAY8Q,WAAW;AACpC,UAAMX,YAAY7B,aAAY;AAC9B,UAAe6B,WAAW;AACtBvB,uBAAe;UACXC,MAAMkC;UACNzP,QAAQ;UACRoP,gBAAgB;UAChBC,iBAAiB,KAAKnM;UACtB0D;UACAE;QANW,CAAD;MAQjB;AACD,WAAK4I,aAAa9I,QAAlB;AACA,UAAeiI;AAAWJ,qBAAY;IACzC;EACJ;AArDL,SAuDYc,mBAAA,SAAA,iBAAiB3I,UAAjB;AACJ+I,wCAAoC,IAAD;AACnC,QAAIC,gBAAgB,IAAD,GAAQ;AACvB,UAAMC,SAASC,gBAAqC,MAAM;QACtD9P,QAAQ;QACRuN,MAAMkC;QACN7I;MAHsD,CAA5B;AAK9B,UAAI,CAACiJ;AAAQ,eAAOnR,YAAY8Q;AAChC5I,iBAAWiJ,OAAOjJ;IACrB;AAEDA,eAAW,KAAKqC,SAASrC,UAAU,KAAKqI,QAAQ,KAAK/L,KAA1C;AACX,WAAO,KAAK2H,OAAO,KAAKoE,QAAQrI,QAAzB,IAAqClI,YAAY8Q,YAAY5I;EACvE;AArEL,SAuEI8I,eAAA,SAAA,aAAa9I,UAAb;AACI,QAAME,WAAW,KAAKmI;AACtB,SAAKA,SAASrI;AACd,SAAK3C,cAAL;AACA,QAAI8L,aAAa,IAAD,GAAQ;AACpBC,sBAAgB,MAAM;QAClBzC,MAAMkC;QACNzP,QAAQ;QACR4G;QACAE;MAJkB,CAAP;IAMlB;EACJ;AAnFL,SAqFW+B,MAAA,SAAAA,OAAA;AACH,SAAK7E,eAAL;AACA,WAAO,KAAKsL,aAAa,KAAKL,MAAvB;EACV;AAxFL,SA0FIgB,aAAA,SAAA,WAAWC,SAAX;AACI,WAAOC,oBAAoB,MAAMD,OAAP;EAC7B;AA5FL,SA8FIE,WAAA,SAAA,SAAStM,UAAgDuM,iBAAzD;AACI,QAAIA;AACAvM,eAAS;QACLsL,gBAAgB;QAChBC,iBAAiB,KAAKnM;QACtBlD,QAAQ;QACRuN,MAAMkC;QACN7I,UAAU,KAAKqI;QACfnI,UAAUf;MANL,CAAD;AAQZ,WAAOuK,iBAAiB,MAAMxM,QAAP;EAC1B;AAzGL,SA2GIyM,MAAA,SAAA,MAAA;AAEI,WAAO,KAAKtB;EACf;AA9GL,SAgHIuB,SAAA,SAAAA,UAAA;AACI,WAAO,KAAK3H,IAAL;EACV;AAlHL,SAoHI/M,WAAA,SAAAA,YAAA;AACI,WAAU,KAAKoH,QAAf,MAAwB,KAAK+L,SAA7B;EACH;AAtHL,SAwHIwB,UAAA,SAAA,UAAA;AACI,WAAO9O,YAAY,KAAKkH,IAAL,CAAD;EACrB;AA1HL,SAAA,mBAAA,IA4HI,WAAA;AACI,WAAO,KAAK4H,QAAL;EACV;AA9HL,SAAA7F;AAAA,EACY3H,IADZ;AAiIA,IAAayN,oBAAoBpQ,0BAA0B,mBAAmBsK,eAApB;;wBCsHrDxI,OAAOT;AAtOZ,IAAaqK,gBAAb,WAAA;AAqCI,WAAAA,eAAYvE,SAAZ;SApCAkJ,qBAAqBlN,kBAAkBC;SACvCkN,aAA4B,CAAA;SAC5BC,gBAAgB;SAChBzN,mBAAmB;SACnBD,0BAAmC;SACnCE,aAAa,oBAAIzC,IAAJ;SACb0C,aAAa;SACbwN,SAAS;SACTvN,kBAAkB;SAClBC,uBAAuBC,kBAAkBsN;SACzCC,oBAAoB;SACV/B,SAA0C,IAAIgC,gBAAgB,IAApB;SACpD/N,QAAAA;SACAgO,eAAAA;SACAC,eAAwB;SACxBC,mBAA4B;SAC5B5U,aAAAA;SACA6U,UAAAA;SACAC,aAAwBC,UAAUC;SAClCC,SAAAA;SACQC,UAAAA;SACAC,oBAAAA;SACRC,aAAAA;SAsCOjO,QAAAA;SACAC,SAAAA;AAxBH,QAAI,CAAC6D,QAAQoB;AAAKlM,UAAI,EAAD;AACrB,SAAKH,aAAaiL,QAAQoB;AAC1B,SAAK3F,QAAQuE,QAAQrL,SAAS,OAAU,mBAAmBwC,UAAS,IAAK;AACzE,QAAI6I,QAAQzB,KAAK;AACb,WAAKqL,UAAUhJ,aACX,OAAU,KAAKnF,QAAQ,YAAY,wBACnCuE,QAAQzB,GAFe;IAI9B;AACD,SAAK0L,UACDjK,QAAQoD,WACNpD,QAAgBoK,qBAAsBpK,QAAgBgE,SAClDrG,SAASE,aACTF,SAAQ,SAAA;AAClB,SAAKqM,SAAShK,QAAQqK;AACtB,SAAKH,oBAAoB,CAAC,CAAClK,QAAQsK;AACnC,SAAKH,aAAa,CAAC,CAACnK,QAAQuK;EAC/B;AAvDL,MAAA,SAAAhG,eAAA;AAAA,SAyDIiG,iBAAA,SAAA,iBAAA;AACIC,0BAAsB,IAAD;EACxB;AA3DL,SAgEWrO,OAAA,SAAA,OAAA;AACH,QAAI,KAAKF,OAAO;AACZ,WAAKA,MAAMzB,QAAQ,SAAA4B,UAAQ;AAAA,eAAIA,SAAQ;MAAZ,CAA3B;IACH;EACJ;AApEL,SAsEWC,QAAA,SAAA,QAAA;AACH,QAAI,KAAKH,QAAQ;AACb,WAAKA,OAAO1B,QAAQ,SAAA4B,UAAQ;AAAA,eAAIA,SAAQ;MAAZ,CAA5B;IACH;EACJ;AA1EL,SAgFW+E,MAAA,SAAAA,OAAA;AACH,QAAI,KAAKsI;AAAcxU,UAAI,IAAI,KAAKuG,OAAO,KAAK1G,UAAtB;AAC1B,QACIkC,YAAYyT,YAAY;IAExB,KAAK9O,WAAW+O,SAAS,KACzB,CAAC,KAAKR,YACR;AACE,UAAIS,cAAc,IAAD,GAAQ;AACrB,aAAKC,wBAAL;AACApO,mBAAU;AACV,aAAK+K,SAAS,KAAKsD,cAAc,KAAnB;AACdnO,iBAAQ;MACX;IACJ,OAAM;AACHJ,qBAAe,IAAD;AACd,UAAIqO,cAAc,IAAD,GAAQ;AACrB,YAAIG,sBAAsB9T,YAAY+T;AACtC,YAAI,KAAKb,cAAc,CAACY;AAAqB9T,sBAAY+T,kBAAkB;AAC3E,YAAI,KAAKC,gBAAL;AAAwBC,mCAAyB,IAAD;AACpDjU,oBAAY+T,kBAAkBD;MACjC;IACJ;AACD,QAAMI,SAAS,KAAK3D;AAEpB,QAAI4D,kBAAkBD,MAAD;AAAU,YAAMA,OAAOE;AAC5C,WAAOF;EACV;AA3GL,SA6GW5M,MAAA,SAAAA,KAAI1G,OAAJ;AACH,QAAI,KAAK+R,SAAS;AACd,UAAI,KAAKD;AAAkBzU,YAAI,IAAI,KAAKuG,KAAV;AAC9B,WAAKkO,mBAAmB;AACxB,UAAI;AACA,aAAKC,QAAQhQ,KAAK,KAAKoQ,QAAQnS,KAA/B;MACH,UAFD;AAGI,aAAK8R,mBAAmB;MAC3B;IACJ;AAAMzU,UAAI,IAAI,KAAKuG,KAAV;EACb;AAvHL,SAyHIwP,kBAAA,SAAA,kBAAA;AAEI,QAAM5L,WAAW,KAAKmI;AACtB,QAAM8D;;MACc,KAAKpC,uBAAuBlN,kBAAkBC;;AAClE,QAAMkD,WAAW,KAAK2L,cAAc,IAAnB;AAEjB,QAAevF,aAAY,GAAI;AAC3BmC,gBAAU;QACNC,gBAAgB;QAChBC,iBAAiB,KAAKnM;QACtBlD,QAAQ,KAAKyR;QACblE,MAAM;QACNzG,UAAU,KAAKmI;QACfrI;MANM,CAAD;IAQZ;AAED,QAAMoM,UACFD,gBACAF,kBAAkB/L,QAAD,KACjB+L,kBAAkBjM,QAAD,KACjB,CAAC,KAAK8K,QAAQ5K,UAAUF,QAAvB;AAEL,QAAIoM,SAAS;AACT,WAAK/D,SAASrI;IACjB;AAED,WAAOoM;EACV;AAtJL,SAwJIT,gBAAA,SAAA,cAAcU,OAAd;AACI,SAAK9B,eAAe;AAEpB,QAAMxC,OAAOZ,uBAAuB,KAAD;AACnC,QAAI9L;AACJ,QAAIgR,OAAO;AACPhR,YAAMiR,qBAAqB,MAAM,KAAK1W,YAAY,KAAKiV,MAA7B;IAC7B,OAAM;AACH,UAAI/S,YAAYyU,2BAA2B,MAAM;AAC7ClR,cAAM,KAAKzF,WAAW6E,KAAK,KAAKoQ,MAA1B;MACT,OAAM;AACH,YAAI;AACAxP,gBAAM,KAAKzF,WAAW6E,KAAK,KAAKoQ,MAA1B;QACT,SAAQ3U,GAAG;AACRmF,gBAAM,IAAIgP,gBAAgBnU,CAApB;QACT;MACJ;IACJ;AACDwR,yBAAqBK,IAAD;AACpB,SAAKwC,eAAe;AACpB,WAAOlP;EACV;AA7KL,SA+KImR,WAAA,SAAA,WAAA;AACI,QAAI,CAAC,KAAKxB,YAAY;AAClByB,qBAAe,IAAD;AACd,WAAKpE,SAASlJ;IACjB;EACJ;AApLL,SAsLIqK,WAAA,SAAA,SAAStM,UAAmDuM,iBAA5D;;AACI,QAAIiD,YAAY;AAChB,QAAIC,YAA2BxN;AAC/B,WAAOyN,QAAQ,WAAA;AAEX,UAAI5M,WAAW,MAAKiC,IAAL;AACf,UAAI,CAACyK,aAAajD,iBAAiB;AAC/B,YAAMoD,QAAQ3F,eAAc;AAC5BhK,iBAAS;UACLsL,gBAAgB;UAChBC,iBAAiB,MAAKnM;UACtBqK,MAAMkC;UACNzP,QAAQ;UACR4G;UACAE,UAAUyM;QANL,CAAD;AAQR/E,qBAAaiF,KAAD;MACf;AACDH,kBAAY;AACZC,kBAAY3M;IACf,CAjBa;EAkBjB;AA3ML,SA6MI0L,0BAAA,SAAA,0BAAA;AACI,QAAI;AAAU;AACd,QAAI,KAAKX,sBAAsB,MAAM;AACjChV,UAAG,2BAA0B,KAAKuG,QAA/B,qCAAA;IACN;AACD,QAAI,KAAKoO,eAAeC,UAAUC,MAAM;AACpCkC,cAAQC,IAAR,mBACqB,KAAKzQ,QAD1B,oEAAA;IAGH;AACD,QAAIxE,YAAYkV,0BAA0B;AACtCF,cAAQG,KAAR,2BAC6B,KAAK3Q,QADlC,mEAAA;IAGH;EACJ;AA5NL,SA8NIpH,WAAA,SAAAA,YAAA;AACI,WAAU,KAAKoH,QAAf,MAAwB,KAAK1G,WAAWV,SAAhB,IAAxB;EACH;AAhOL,SAkOI2U,UAAA,SAAA,UAAA;AACI,WAAO9O,YAAY,KAAKkH,IAAL,CAAD;EACrB;AApOL,SAAA,qBAAA,IAsOI,WAAA;AACI,WAAO,KAAK4H,QAAL;EACV;AAxOL,SAAAzE;AAAA,EAAA;AA2OO,IAAM8H,kBAAkBxT,0BAA0B,iBAAiB0L,aAAlB;AC/SxD,IAAYvI;CAAZ,SAAYA,oBAAAA;AAGRA,EAAAA,mBAAAA,mBAAAA,eAAAA,IAAAA,EAAAA,IAAA;AAIAA,EAAAA,mBAAAA,mBAAAA,aAAAA,IAAAA,CAAAA,IAAA;AAOAA,EAAAA,mBAAAA,mBAAAA,iBAAAA,IAAAA,CAAAA,IAAA;AAGAA,EAAAA,mBAAAA,mBAAAA,QAAAA,IAAAA,CAAAA,IAAA;AACH,GAlBWA,sBAAAA,oBAAiB,CAAA,EAA7B;AAoBA,IAAY8N;CAAZ,SAAYA,YAAAA;AACRA,EAAAA,WAAAA,WAAAA,MAAAA,IAAAA,CAAAA,IAAA;AACAA,EAAAA,WAAAA,WAAAA,KAAAA,IAAAA,CAAAA,IAAA;AACAA,EAAAA,WAAAA,WAAAA,OAAAA,IAAAA,CAAAA,IAAA;AACH,GAJWA,cAAAA,YAAS,CAAA,EAArB;AAgCA,IAAaN,kBACT,SAAAA,iBAAmB6B,OAAnB;OAAmBA,QAAAA;AAAA,OAAA,QAAAA;AAElB;AAGL,SAAgBD,kBAAkB/V,GAAAA;AAC9B,SAAOA,aAAamU;AACvB;AAaD,SAAgBoB,cAAc7V,YAAAA;AAC1B,UAAQA,WAAWmU,oBAAnB;IACI,KAAKlN,kBAAkBsN;AACnB,aAAO;IACX,KAAKtN,kBAAkBC;IACvB,KAAKD,kBAAkBsQ;AACnB,aAAO;IACX,KAAKtQ,kBAAkBuQ,iBAAiB;AAEpC,UAAMC,sBAAsBhG,qBAAqB,IAAD;AAChD,UAAMiG,gBAAgBpG,eAAc;AACpC,UAAMqG,MAAM3X,WAAWoU,YACnBwD,IAAID,IAAIlY;AACZ,eAASoY,IAAI,GAAGA,IAAID,GAAGC,KAAK;AACxB,YAAMxU,MAAMsU,IAAIE,CAAD;AACf,YAAIP,gBAAgBjU,GAAD,GAAO;AACtB,cAAInB,YAAYyU,wBAAwB;AACpCtT,gBAAIgJ,IAAJ;UACH,OAAM;AACH,gBAAI;AACAhJ,kBAAIgJ,IAAJ;YACH,SAAQ/L,GAAG;AAER0R,2BAAa0F,aAAD;AACZ3F,iCAAmB0F,mBAAD;AAClB,qBAAO;YACV;UACJ;AAID,cAAKzX,WAAWmU,uBAA+BlN,kBAAkBsQ,QAAQ;AACrEvF,yBAAa0F,aAAD;AACZ3F,+BAAmB0F,mBAAD;AAClB,mBAAO;UACV;QACJ;MACJ;AACDK,iCAA2B9X,UAAD;AAC1BgS,mBAAa0F,aAAD;AACZ3F,yBAAmB0F,mBAAD;AAClB,aAAO;IACV;EAzCL;AA2CH;AAED,SAAgBM,wBAAAA;AACZ,SAAO7V,YAAYgP,uBAAuB;AAC7C;AAED,SAAgBiC,oCAAoClL,MAAAA;AAChD,MAAI,OAAU;AACV;EACH;AACD,MAAM+P,gBAAe/P,KAAKpB,WAAW+O,OAAO;AAE5C,MAAI,CAAC1T,YAAYmP,sBAAsB2G,iBAAgB9V,YAAY+V,mBAAmB;AAClFf,YAAQG,KACJ,aACKnV,YAAY+V,iBACP,kIACA,mSACNhQ,KAAKvB,KALb;AAOP;AAED,SAAgBwR,4BAA4B7O,aAAAA;AACxC,MAAe,CAACnH,YAAYiW,mBAAmBjW,YAAYkW,4BAA4B;AACnFlB,YAAQG,KAAR,uBAAkChO,YAAW3C,QAA7C,wCAAA;EACH;AACJ;AAOD,SAAgBgQ,qBAAwB1W,YAAyBqY,GAAY/C,SAAAA;AACzE,MAAMmC,sBAAsBhG,qBAAqB,IAAD;AAGhDqG,6BAA2B9X,UAAD;AAC1BA,aAAWqU,gBAAgB,IAAIlL,MAAMnJ,WAAWoU,WAAW3U,SAAS,GAAzC;AAC3BO,aAAWwU,oBAAoB;AAC/BxU,aAAWsU,SAAS,EAAEpS,YAAYoW;AAClC,MAAMC,eAAerW,YAAYgP;AACjChP,cAAYgP,qBAAqBlR;AACjCkC,cAAYyT;AACZ,MAAIS;AACJ,MAAIlU,YAAYyU,2BAA2B,MAAM;AAC7CP,aAASiC,EAAExT,KAAKyQ,OAAP;EACZ,OAAM;AACH,QAAI;AACAc,eAASiC,EAAExT,KAAKyQ,OAAP;IACZ,SAAQhV,GAAG;AACR8V,eAAS,IAAI3B,gBAAgBnU,CAApB;IACZ;EACJ;AACD4B,cAAYyT;AACZzT,cAAYgP,qBAAqBqH;AACjCC,mBAAiBxY,UAAD;AAEhByY,yCAAuCzY,UAAD;AACtC+R,qBAAmB0F,mBAAD;AAClB,SAAOrB;AACV;AAED,SAASqC,uCAAuCzY,YAAhD;AACI,MAAI;AAAU;AAEd,MAAIA,WAAWoU,WAAW3U,WAAW;AAAG;AAExC,MAAIyC,YAAYwW,8BAA8B1Y,WAAW2Y,qBAAqB;AAC1EzB,YAAQG,KAAR,uBACyBrX,WAAW0G,QADpC,0DAAA;EAGH;AACJ;AAOD,SAAS8R,iBAAiBxY,YAA1B;AAEI,MAAM4Y,gBAAgB5Y,WAAWoU;AACjC,MAAMyE,YAAa7Y,WAAWoU,aAAapU,WAAWqU;AACtD,MAAIyE,oCAAoC7R,kBAAkBsN;AAK1D,MAAIwE,KAAK,GACLnB,IAAI5X,WAAWwU;AACnB,WAASqD,IAAI,GAAGA,IAAID,GAAGC,KAAK;AACxB,QAAMmB,MAAMH,UAAUhB,CAAD;AACrB,QAAImB,IAAIlS,eAAe,GAAG;AACtBkS,UAAIlS,aAAa;AACjB,UAAIiS,OAAOlB;AAAGgB,kBAAUE,EAAD,IAAOC;AAC9BD;IACH;AAID,QAAMC,IAA4B7E,qBAAqB2E,mCAAmC;AACtFA,0CAAsCE,IAA4B7E;IACrE;EACJ;AACD0E,YAAUpZ,SAASsZ;AAEnB/Y,aAAWqU,gBAAgB;AAK3BuD,MAAIgB,cAAcnZ;AAClB,SAAOmY,KAAK;AACR,QAAMoB,OAAMJ,cAAchB,CAAD;AACzB,QAAIoB,KAAIlS,eAAe,GAAG;AACtBmS,qBAAeD,MAAKhZ,UAAN;IACjB;AACDgZ,SAAIlS,aAAa;EACpB;AAKD,SAAOiS,MAAM;AACT,QAAMC,QAAMH,UAAUE,EAAD;AACrB,QAAIC,MAAIlS,eAAe,GAAG;AACtBkS,YAAIlS,aAAa;AACjBoS,kBAAYF,OAAKhZ,UAAN;IACd;EACJ;AAID,MAAI8Y,sCAAsC7R,kBAAkBsN,aAAa;AACrEvU,eAAWmU,qBAAqB2E;AAChC9Y,eAAWyV,eAAX;EACH;AACJ;AAED,SAAgBoB,eAAe7W,YAAAA;AAE3B,MAAM2X,MAAM3X,WAAWoU;AACvBpU,aAAWoU,aAAa,CAAA;AACxB,MAAIyD,IAAIF,IAAIlY;AACZ,SAAOoY,KAAP;AAAYoB,mBAAetB,IAAIE,CAAD,GAAK7X,UAAT;EAA1B;AAEAA,aAAWmU,qBAAqBlN,kBAAkBC;AACrD;AAED,SAAgBiS,UAAaC,SAAAA;AACzB,MAAMjH,OAAOb,eAAc;AAC3B,MAAI;AACA,WAAO8H,QAAM;EAChB,UAFD;AAGIpH,iBAAaG,IAAD;EACf;AACJ;AAED,SAAgBb,iBAAAA;AACZ,MAAMa,OAAOjQ,YAAYgP;AACzBhP,cAAYgP,qBAAqB;AACjC,SAAOiB;AACV;AAED,SAAgBH,aAAaG,MAAAA;AACzBjQ,cAAYgP,qBAAqBiB;AACpC;AAED,SAAgBV,qBAAqB0G,iBAAAA;AACjC,MAAMhG,OAAOjQ,YAAYiW;AACzBjW,cAAYiW,kBAAkBA;AAC9B,SAAOhG;AACV;AAED,SAAgBJ,mBAAmBI,MAAAA;AAC/BjQ,cAAYiW,kBAAkBhG;AACjC;AAMD,SAAgB2F,2BAA2B9X,YAAAA;AACvC,MAAIA,WAAWmU,uBAAuBlN,kBAAkBsN;AAAa;AACrEvU,aAAWmU,qBAAqBlN,kBAAkBsN;AAElD,MAAMoD,MAAM3X,WAAWoU;AACvB,MAAIyD,IAAIF,IAAIlY;AACZ,SAAOoY,KAAP;AAAYF,QAAIE,CAAD,EAAI7Q,uBAAuBC,kBAAkBsN;EAA5D;AACH;ACvTD,IAAM8E,iBAAwC,CAC1C,YACA,gBACA,kBACA,4BACA,8BACA,8BACA,mBACA,0BACA,SACA,aACA,YAX0C;AAgB9C,IAAaC,cAAb,SAAAA,eAAA;AAAA,OASIC,UAAU;AATd,OAcIvG,YAAwB,CAAA;AAd5B,OAmBI9B,qBAAyC;AAnB7C,OA0BI+E,kBAAwD;AA1B5D,OA+BIqC,QAAQ;AA/BZ,OAoCIjW,WAAW;AApCf,OAyCIsT,UAAkB;AAzCtB,OAiDI6D,wBAAuC,CAAA;AAjD3C,OAsDIC,mBAA+B,CAAA;AAtDnC,OA2DIC,qBAAqB;AA3DzB,OAkEIrI,oBAAoB;AAlExB,OAwEI8G,kBAAkB;AAxEtB,OA6EIF,iBAAqC;AA7EzC,OAkFI0B,eAA0C,CAAA;AAlF9C,OAuFIC,8BAAiF,CAAA;AAvFrF,OA4FIxC,2BAA2B;AA5F/B,OAkGIsB,6BAA6B;AAlGjC,OAwGIN,6BAA6B;AAxGjC,OA8GIzB,yBAAyB;AA9G7B,OAoHI9E,yBAAyB;AApH7B,OAsHItD,aAAa;AAtHjB,OA0HIpM,gBAAgB;AA1HpB,OAiIIuJ,kBAAkB;AAjItB;AAoIA,IAAImO,sBAAsB;AAC1B,IAAIC,gBAAgB;AAEb,IAAI5X,cAA4B,WAAA;AACnC,MAAIlB,UAASH,UAAS;AACtB,MAAIG,QAAO+Y,sBAAsB,KAAK,CAAC/Y,QAAOgZ;AAAeH,0BAAsB;AACnF,MAAI7Y,QAAOgZ,iBAAiBhZ,QAAOgZ,cAAcT,YAAY,IAAID,YAAJ,EAAkBC;AAC3EM,0BAAsB;AAE1B,MAAI,CAACA,qBAAqB;AACtBI,eAAW,WAAA;AACP,UAAI,CAACH,eAAe;AAChB3Z,YAAI,EAAD;MACN;IACJ,GAAE,CAJO;AAKV,WAAO,IAAImZ,YAAJ;EACV,WAAUtY,QAAOgZ,eAAe;AAC7BhZ,IAAAA,QAAO+Y,uBAAuB;AAC9B,QAAI,CAAC/Y,QAAOgZ,cAAchH;AAAWhS,MAAAA,QAAOgZ,cAAchH,YAAY,CAAA;AACtE,WAAOhS,QAAOgZ;EACjB,OAAM;AACHhZ,IAAAA,QAAO+Y,sBAAsB;AAC7B,WAAQ/Y,QAAOgZ,gBAAgB,IAAIV,YAAJ;EAClC;AACJ,EArBqC;AAuBtC,SAAgBY,qBAAAA;AACZ,MACIhY,YAAYuX,iBAAiBha,UAC7ByC,YAAYyT,WACZzT,YAAYwX;AAEZvZ,QAAI,EAAD;AACP2Z,kBAAgB;AAChB,MAAID,qBAAqB;AACrB,QAAI7Y,UAASH,UAAS;AACtB,QAAI,EAAEG,QAAO+Y,wBAAwB;AAAG/Y,MAAAA,QAAOgZ,gBAAgBzQ;AAC/DrH,kBAAc,IAAIoX,YAAJ;EACjB;AACJ;AAED,SAAgBa,iBAAAA;AACZ,SAAOjY;AACV;AAMD,SAAgBkY,mBAAAA;AACZ,MAAMC,iBAAiB,IAAIf,YAAJ;AACvB,WAASja,OAAOgb,gBAAhB;AACI,QAAIhB,eAAeiB,QAAQjb,GAAvB,MAAuC;AAAI6C,kBAAY7C,GAAD,IAAQgb,eAAehb,GAAD;EADpF;AAEA6C,cAAYmP,oBAAoB,CAACnP,YAAY+V;AAChD;SCxKeD,aAAa3O,aAAAA;AACzB,SAAOA,YAAWxC,cAAcwC,YAAWxC,WAAW+O,OAAO;AAChE;AAED,SAAgB2E,aAAalR,aAAAA;AACzB,SAAOA,YAAWxC;AACrB;AAmBD,SAAgBqS,YAAY7P,aAAyBmR,MAAAA;AAKjDnR,EAAAA,YAAWxC,WAAW4T,IAAID,IAA1B;AACA,MAAInR,YAAWrC,uBAAuBwT,KAAKrG;AACvC9K,IAAAA,YAAWrC,uBAAuBwT,KAAKrG;AAI9C;AAED,SAAgB8E,eAAe5P,aAAyBmR,MAAAA;AAIpDnR,EAAAA,YAAWxC,WAAX,QAAA,EAA6B2T,IAA7B;AACA,MAAInR,YAAWxC,WAAW+O,SAAS,GAAG;AAElC8E,0BAAsBrR,WAAD;EACxB;AAGJ;AAED,SAAgBqR,sBAAsBrR,aAAAA;AAClC,MAAIA,YAAW1C,4BAA4B,OAAO;AAE9C0C,IAAAA,YAAW1C,0BAA0B;AACrCzE,gBAAYsX,sBAAsBmB,KAAKtR,WAAvC;EACH;AACJ;AAOD,SAAgB3B,aAAAA;AACZxF,cAAYyT;AACf;AAED,SAAgB/N,WAAAA;AACZ,MAAI,EAAE1F,YAAYyT,YAAY,GAAG;AAC7BiF,iBAAY;AAEZ,QAAMC,OAAO3Y,YAAYsX;AACzB,aAAS3B,IAAI,GAAGA,IAAIgD,KAAKpb,QAAQoY,KAAK;AAClC,UAAMxO,cAAawR,KAAKhD,CAAD;AACvBxO,MAAAA,YAAW1C,0BAA0B;AACrC,UAAI0C,YAAWxC,WAAW+O,SAAS,GAAG;AAClC,YAAIvM,YAAWzC,kBAAkB;AAE7ByC,UAAAA,YAAWzC,mBAAmB;AAC9ByC,UAAAA,YAAW9B,MAAX;QACH;AACD,YAAI8B,uBAAsBmG,eAAe;AAGrCnG,UAAAA,YAAWuN,SAAX;QACH;MACJ;IACJ;AACD1U,gBAAYsX,wBAAwB,CAAA;EACvC;AACJ;AAED,SAAgBhS,eAAe6B,aAAAA;AAC3B6O,8BAA4B7O,WAAD;AAE3B,MAAMrJ,aAAakC,YAAYgP;AAC/B,MAAIlR,eAAe,MAAM;AAMrB,QAAIA,WAAWsU,WAAWjL,YAAWtC,iBAAiB;AAClDsC,MAAAA,YAAWtC,kBAAkB/G,WAAWsU;AAExCtU,iBAAWqU,cAAerU,WAAWwU,mBAArC,IAA4DnL;AAC5D,UAAI,CAACA,YAAWzC,oBAAoB1E,YAAY+T,iBAAiB;AAC7D5M,QAAAA,YAAWzC,mBAAmB;AAC9ByC,QAAAA,YAAWhC,KAAX;MACH;IACJ;AACD,WAAO;EACV,WAAUgC,YAAWxC,WAAW+O,SAAS,KAAK1T,YAAYyT,UAAU,GAAG;AACpE+E,0BAAsBrR,WAAD;EACxB;AAED,SAAO;AACV;AAyBD,SAAgB1B,iBAAiB0B,aAAAA;AAE7B,MAAIA,YAAWrC,yBAAyBC,kBAAkBsQ;AAAQ;AAClElO,EAAAA,YAAWrC,uBAAuBC,kBAAkBsQ;AAGpDlO,EAAAA,YAAWxC,WAAWnB,QAAQ,SAAAoV,GAAC;AAC3B,QAAIA,EAAE3G,uBAAuBlN,kBAAkBsN,aAAa;AACxD,UAAeuG,EAAEhG,eAAeC,UAAUC,MAAM;AAC5C+F,qBAAaD,GAAGzR,WAAJ;MACf;AACDyR,QAAErF,eAAF;IACH;AACDqF,MAAE3G,qBAAqBlN,kBAAkBsQ;EAC5C,CARD;AAUH;AAGD,SAAgBpB,yBAAyB9M,aAAAA;AAErC,MAAIA,YAAWrC,yBAAyBC,kBAAkBsQ;AAAQ;AAClElO,EAAAA,YAAWrC,uBAAuBC,kBAAkBsQ;AAEpDlO,EAAAA,YAAWxC,WAAWnB,QAAQ,SAAAoV,GAAC;AAC3B,QAAIA,EAAE3G,uBAAuBlN,kBAAkBuQ,iBAAiB;AAC5DsD,QAAE3G,qBAAqBlN,kBAAkBsQ;AACzC,UAAeuD,EAAEhG,eAAeC,UAAUC,MAAM;AAC5C+F,qBAAaD,GAAGzR,WAAJ;MACf;IACJ,WACGyR,EAAE3G,uBAAuBlN,kBAAkBsN,aAC7C;AACElL,MAAAA,YAAWrC,uBAAuBC,kBAAkBsN;IACvD;EACJ,CAXD;AAaH;AAGD,SAAgBmB,sBAAsBrM,aAAAA;AAElC,MAAIA,YAAWrC,yBAAyBC,kBAAkBsN;AAAa;AACvElL,EAAAA,YAAWrC,uBAAuBC,kBAAkBuQ;AAEpDnO,EAAAA,YAAWxC,WAAWnB,QAAQ,SAAAoV,GAAC;AAC3B,QAAIA,EAAE3G,uBAAuBlN,kBAAkBsN,aAAa;AACxDuG,QAAE3G,qBAAqBlN,kBAAkBuQ;AACzCsD,QAAErF,eAAF;IACH;EACJ,CALD;AAOH;AAED,SAASsF,aAAa/a,YAAyBqJ,aAA/C;AACI6N,UAAQC,IAAR,mBACqBnX,WAAW0G,QADhC,2CAC8E2C,YAAW3C,QADzF,GAAA;AAGA,MAAI1G,WAAW8U,eAAeC,UAAUiG,OAAO;AAC3C,QAAMC,QAAQ,CAAA;AACdC,iBAAaC,kBAAkBnb,UAAD,GAAcib,OAAO,CAAvC;AAGZ,QAAIG,SAAJ,6BAGGpb,WAAW0G,QAHd,gEAKgD1G,WAAW0G,QAL3D,4BAK0F2C,YAAW3C,QALrG,6OASN1G,sBAAsBwP,gBAAgBxP,WAAWA,WAAWV,SAAtB,EAAiC+b,QAAQ,UAAU,GAAnD,IAA0D,MAT1F,sDAaNJ,MAAMta,KAAK,IAAX,IAbM,YAAA,EAAA;EAgBH;AACJ;AAED,SAASua,aAAaI,MAAuBL,OAAiBM,OAA9D;AACI,MAAIN,MAAMxb,UAAU,KAAM;AACtBwb,UAAMN,KAAK,iBAAX;AACA;EACH;AACDM,QAAMN,KAAN,KAAc,IAAIxR,MAAMoS,KAAV,EAAiB5a,KAAK,GAAtB,IAA8B2a,KAAK1b,IAAjD;AACA,MAAI0b,KAAKE;AAAcF,SAAKE,aAAa9V,QAAQ,SAAA+V,OAAK;AAAA,aAAIP,aAAaO,OAAOR,OAAOM,QAAQ,CAAvB;IAAhB,CAA/B;AAC1B;IC5NYG,WAAb,WAAA;AAaI,WAAAA,UACWhV,OACCiV,eACAC,eACDjD,qBAJX;QACWjS,UAAAA,QAAAA;AAAAA,cAAgB,OAAU,cAActE,UAAS,IAAK;;QAGtDuW,wBAAAA,QAAAA;AAAAA,4BAAsB;;SAHtBjS,QAAAA;SACCiV,gBAAAA;SACAC,gBAAAA;SACDjD,sBAAAA;SAhBXvE,aAA4B,CAAA;SAC5BC,gBAA+B,CAAA;SAC/BF,qBAAqBlN,kBAAkBC;SACvCJ,aAAa;SACbwN,SAAS;SACTE,oBAAoB;SACpBqH,cAAc;SACdC,eAAe;SACfC,kBAAkB;SAClBC,aAAa;SACblH,aAAwBC,UAAUC;AAGvB,SAAA,QAAAtO;AACC,SAAA,gBAAAiV;AACA,SAAA,gBAAAC;AACD,SAAA,sBAAAjD;EACP;AAlBR,MAAA,SAAA+C,UAAA;AAAA,SAoBIjG,iBAAA,SAAA,iBAAA;AACI,SAAKwG,UAAL;EACH;AAtBL,SAwBIA,YAAA,SAAA,YAAA;AACI,QAAI,CAAC,KAAKH,cAAc;AACpB,WAAKA,eAAe;AACpB5Z,kBAAYuX,iBAAiBkB,KAAK,IAAlC;AACAC,mBAAY;IACf;EACJ;AA9BL,SAgCIsB,cAAA,SAAA,cAAA;AACI,WAAO,KAAKJ;EACf;AAlCL,SAuCIK,eAAA,SAAA,eAAA;AACI,QAAI,CAAC,KAAKN,aAAa;AACnBnU,iBAAU;AACV,WAAKoU,eAAe;AACpB,UAAM3J,OAAOjQ,YAAY+T;AACzB/T,kBAAY+T,kBAAkB;AAC9B,UAAIJ,cAAc,IAAD,GAAQ;AACrB,aAAKkG,kBAAkB;AAEvB,YAAI;AACA,eAAKJ,cAAL;AACA,cAAe,KAAKI,mBAAmBvL,aAAY,GAAI;AAEnDmC,sBAAU;cACN/S,MAAM,KAAK8G;cACXqK,MAAM;YAFA,CAAD;UAIZ;QACJ,SAAQzQ,GAAG;AACR,eAAK8b,6BAA6B9b,CAAlC;QACH;MACJ;AACD4B,kBAAY+T,kBAAkB9D;AAC9BvK,eAAQ;IACX;EACJ;AAhEL,SAkEI6O,QAAA,SAAA,MAAM7T,IAAN;AACI,QAAI,KAAKiZ,aAAa;AAClB;IAEH;AACDnU,eAAU;AACV,QAAM2U,SAAS7L,aAAY;AAC3B,QAAI8L;AACJ,QAAeD,QAAQ;AACnBC,kBAAY5L,KAAKC,IAAL;AACZG,qBAAe;QACXlR,MAAM,KAAK8G;QACXqK,MAAM;MAFK,CAAD;IAIjB;AACD,SAAKiL,aAAa;AAClB,QAAMO,eAAera,YAAY+T;AACjC/T,gBAAY+T,kBAAkB;AAC9B,QAAMG,SAASM,qBAAqB,MAAM9T,IAAI2G,MAAX;AACnCrH,gBAAY+T,kBAAkBsG;AAC9B,SAAKP,aAAa;AAClB,SAAKD,kBAAkB;AACvB,QAAI,KAAKF,aAAa;AAElBhF,qBAAe,IAAD;IACjB;AACD,QAAIR,kBAAkBD,MAAD;AAAU,WAAKgG,6BAA6BhG,OAAOE,KAAzC;AAC/B,QAAe+F,QAAQ;AACnBpK,mBAAa;QACTC,MAAMxB,KAAKC,IAAL,IAAa2L;MADV,CAAD;IAGf;AACD1U,aAAQ;EACX;AAnGL,SAqGIwU,+BAAA,SAAA,6BAA6Bhc,OAA7B;;AACI,QAAI,KAAKwb,eAAe;AACpB,WAAKA,cAAcxb,OAAO,IAA1B;AACA;IACH;AAED,QAAI8B,YAAYyU;AAAwB,YAAMvW;AAE9C,QAAMoc,UAAU,OAAA,wGAC4F,OAD5F,MAAA,+BAEmB,OAFnB;AAGhB,QAAI,CAACta,YAAY2P,wBAAwB;AACrCqF,cAAQ9W,MAAMoc,SAASpc,KAAvB;IAEH,WAAM;AAAa8W,cAAQG,KAAR,gCAA2C,KAAK3Q,QAAhD,kDAAA;AAEpB,QAAe8J,aAAY,GAAI;AAC3BmC,gBAAU;QACN5B,MAAM;QACNnR,MAAM,KAAK8G;QACX8V;QACApc,OAAO,KAAKA;MAJN,CAAD;IAMZ;AAED8B,gBAAY0X,4BAA4BlU,QAAQ,SAAA2S,GAAC;AAAA,aAAIA,EAAEjY,OAAO,KAAR;IAAL,CAAjD;EACH;AA/HL,SAiIIqc,UAAA,SAAA,UAAA;AACI,QAAI,CAAC,KAAKZ,aAAa;AACnB,WAAKA,cAAc;AACnB,UAAI,CAAC,KAAKG,YAAY;AAElBtU,mBAAU;AACVmP,uBAAe,IAAD;AACdjP,iBAAQ;MACX;IACJ;EACJ;AA3IL,SA6II8U,eAAA,SAAA,eAAA;AACI,QAAMC,IAAI,KAAKF,QAAQ9Q,KAAK,IAAlB;AACVgR,MAAEnW,KAAD,IAAU;AACX,WAAOmW;EACV;AAjJL,SAmJIrd,WAAA,SAAAA,YAAA;AACI,WAAA,cAAmB,KAAKoH,QAAxB;EACH;AArJL,SAuJIkW,QAAA,SAAA,QAAMC,iBAAN;QAAMA,oBAAAA,QAAAA;AAAAA,wBAA2B;;AAC7BD,UAAM,MAAMC,eAAP;EACR;AAzJL,SAAAnB;AAAA,EAAA;AA4JA,SAAgBoB,gBAAgBpJ,SAAAA;AAC5BxR,cAAY0X,4BAA4Be,KAAKjH,OAA7C;AACA,SAAO,WAAA;AACH,QAAMqJ,MAAM7a,YAAY0X,4BAA4BU,QAAQ5G,OAAhD;AACZ,QAAIqJ,OAAO;AAAG7a,kBAAY0X,4BAA4BoD,OAAOD,KAAK,CAApD;EACjB;AACJ;AAOD,IAAME,0BAA0B;AAEhC,IAAIC,oBAA8C,SAAAA,mBAAA7E,GAAC;AAAA,SAAIA,EAAC;AAAL;AAEnD,SAAgBuC,eAAAA;AAEZ,MAAI1Y,YAAYyT,UAAU,KAAKzT,YAAYwX;AAAoB;AAC/DwD,oBAAkBC,kBAAD;AACpB;AAED,SAASA,qBAAT;AACIjb,cAAYwX,qBAAqB;AACjC,MAAM0D,eAAelb,YAAYuX;AACjC,MAAI4D,aAAa;AAKjB,SAAOD,aAAa3d,SAAS,GAAG;AAC5B,QAAI,EAAE4d,eAAeJ,yBAAyB;AAC1C/F,cAAQ9W,MACJ,OACM,uDAAqD6c,0BAArD,kBAAA,0DAC4DG,aAAa,CAAD,KAF9E,+BAGmCA,aAAa,CAAD,CAJnD;AAMAA,mBAAaJ,OAAO,CAApB;IACH;AACD,QAAIM,qBAAqBF,aAAaJ,OAAO,CAApB;AACzB,aAASnF,IAAI,GAAGD,IAAI0F,mBAAmB7d,QAAQoY,IAAID,GAAGC,KAAtD;AACIyF,yBAAmBzF,CAAD,EAAIsE,aAAtB;IADJ;EAEH;AACDja,cAAYwX,qBAAqB;AACpC;AAEM,IAAM6D,aAAazZ,0BAA0B,YAAY4X,QAAb;AAEnD,SAAgB8B,qBAAqB5a,IAAAA;AACjC,MAAM6a,gBAAgBP;AACtBA,sBAAoB,SAAAA,mBAAA7E,GAAC;AAAA,WAAIzV,GAAG,WAAA;AAAA,aAAM6a,cAAcpF,CAAD;IAAnB,CAAD;EAAN;AACxB;SC/Pe7H,eAAAA;AACZ,SAAkB,CAAC,CAACtO,YAAYyX,aAAala;AAChD;AAkBD,SAAgBkT,UAAU+K,OAAAA;AACtB,MAAI;AAAU;AACd,MAAI,CAACxb,YAAYyX,aAAala;AAAQ;AACtC,MAAMke,YAAYzb,YAAYyX;AAC9B,WAAS9B,IAAI,GAAGD,IAAI+F,UAAUle,QAAQoY,IAAID,GAAGC,KAA7C;AAAkD8F,cAAU9F,CAAD,EAAI6F,KAAb;EAAlD;AACH;AAED,SAAgB5M,eAAe4M,OAAAA;AAC3B,MAAI;AAAU;AACd,MAAMrK,SAAM,SAAA,CAAA,GAAQqK,OAAR;IAAe5M,gBAAgB;EAA/B,CAAA;AACZ6B,YAAUU,MAAD;AACZ;AAED,IAAMuK,YAAsB;EAAE7M,MAAM;EAAckB,cAAc;AAApC;AAE5B,SAAgBA,aAAaoB,QAAAA;AACzB,MAAI;AAAU;AACd,MAAIA;AAAQV,cAAS,SAAA,CAAA,GAAMU,QAAN;MAActC,MAAM;MAAckB,cAAc;IAAhD,CAAA,CAAA;;AAChBU,cAAUiL,SAAD;AACjB;AAED,SAAgBC,IAAIvW,UAAAA;AAChB,MAAI,OAAU;AACV4P,YAAQG,KAAR,4CAAA;AACA,WAAO,WAAA;IAAA;EACV,OAAM;AACHnV,gBAAYyX,aAAagB,KAAKrT,QAA9B;AACA,WAAOhF,KAAK,WAAA;AACRJ,kBAAYyX,eAAezX,YAAYyX,aAAajV,OAAO,SAAAkT,GAAC;AAAA,eAAIA,MAAMtQ;MAAV,CAAjC;IAC9B,CAFU;EAGd;AACJ;AC7CM,IAAM0J,SAAS;AACf,IAAM8M,eAAe;AACrB,IAAMC,aAAa;AACnB,IAAMC,mBAAmB;AAEhC,IAAMC,sBAAsB;AAE5B,IAAMjR,mBAAmBhC,uBAAuBgG,MAAD;AAC/C,IAAMkN,wBAAwBlT,uBAAuB8S,cAAc;EAC/D1S,OAAO;AADwD,CAAf;AAGpD,IAAM+S,uBAAuBnT,uBAAuB+S,YAAY;EAC5DnU,YAAY;AADgD,CAAb;AAGnD,IAAMwU,4BAA4BpT,uBAAuBgT,kBAAkB;EACvEpU,YAAY;EACZwB,OAAO;AAFgE,CAAnB;AAkBxD,SAASiT,oBAAoBzU,aAA7B;AACI,MAAMnE,MAAsB,SAAS2T,QAAO9J,MAAMvB,MAAtB;AAExB,QAAIpL,WAAW2M,IAAD;AACV,aAAOzD,aAAayD,KAAK1P,QAAQqe,qBAAqB3O,MAAM1F,WAAzC;AAEvB,QAAIjH,WAAWoL,IAAD;AAAQ,aAAOlC,aAAayD,MAAMvB,MAAMnE,WAAb;AAEzC,QAAI/G,YAAYkL,IAAD,GAAQ;AACnB,aAAO/H,gBAAgBsJ,MAAMvB,MAAMnE,cAAauU,uBAAuBnR,gBAAjD;IACzB;AAED,QAAInK,YAAYyM,IAAD,GAAQ;AACnB,aAAOzJ,0BACHmF,uBAAuBpB,cAAamU,aAAa/M,QAAQ;QACrDpR,MAAM0P;QACN1F,YAAAA;MAFqD,CAAnC,CADM;IAMnC;AAED,QAAA;AAAazJ,UAAI,gCAAD;EACD;AACnB,SAAOsF;AACV;AAED,IAAa2T,SAAyBiF,oBAAoB,KAAD;AACzDld,OAAOD,OAAOkY,QAAQpM,gBAAtB;AACA,IAAapD,aAA6ByU,oBAAoB,IAAD;AAC7Dld,OAAOD,OAAO0I,YAAYuU,oBAA1B;AAEA/E,OAAOhO,QAAQvF,0BAA0BqY,qBAAD;AACxCtU,WAAWwB,QAAQvF,0BAA0BuY,yBAAD;AAE5C,SAAgBE,YAAe1b,IAAAA;AAC3B,SAAOkN,cAAclN,GAAGhD,QAAQqe,qBAAqB,OAAOrb,IAAI,MAAM2G,MAAlD;AACvB;AAED,SAAgBE,SAAS3J,OAAAA;AACrB,SAAO6C,WAAW7C,KAAD,KAAWA,MAAMiQ,iBAAiB;AACtD;ACjDD,SAAgBiH,QACZuH,MACAhP,MAAAA;;MAAAA,SAAAA,QAAAA;AAAAA,WAAwB5N;;AAExB,MAAA,MAAa;AACT,QAAI,CAACgB,WAAW4b,IAAD;AAAQpe,UAAI,8CAAD;AAC1B,QAAIsJ,SAAS8U,IAAD;AAAQpe,UAAI,+DAAD;EAC1B;AAED,MAAMP,QAAI,cAAA,QACN2P,SADM,OAAA,SACN,MAAM3P,SADA,OAAA,aACS,OAAW2e,KAAa3e,QAAQ,aAAawC,UAAS,IAAK;AAC9E,MAAMoc,UAAU,CAACjP,KAAKkP,aAAa,CAAClP,KAAKmP;AACzC,MAAIC;AAEJ,MAAIH,SAAS;AAETG,IAAAA,YAAW,IAAIjD,SACX9b,MACA,WAAA;AACI,WAAK6W,MAAMmI,cAAX;IACH,GACDrP,KAAKsP,SACLtP,KAAKuP,kBANE;EAQd,OAAM;AACH,QAAML,YAAYM,2BAA2BxP,IAAD;AAE5C,QAAI2M,cAAc;AAElByC,IAAAA,YAAW,IAAIjD,SACX9b,MACA,WAAA;AACI,UAAI,CAACsc,aAAa;AACdA,sBAAc;AACduC,kBAAU,WAAA;AACNvC,wBAAc;AACd,cAAI,CAACyC,UAAS9C;AAAa8C,YAAAA,UAASlI,MAAMmI,cAAf;QAC9B,CAHQ;MAIZ;IACJ,GACDrP,KAAKsP,SACLtP,KAAKuP,kBAZE;EAcd;AAED,WAASF,iBAAT;AACIL,SAAKI,SAAD;EACP;AAEDA,EAAAA,UAAS1C,UAAT;AACA,SAAO0C,UAASjC,aAAT;AACV;AAOD,IAAMsC,MAAM,SAANA,KAAO3G,GAAD;AAAA,SAAeA,EAAC;AAAhB;AAEZ,SAAS0G,2BAA2BxP,MAApC;AACI,SAAOA,KAAKkP,YACNlP,KAAKkP,YACLlP,KAAKmP,QACL,SAACrG,GAAD;AAAA,WAAe4B,WAAW5B,GAAG9I,KAAKmP,KAAT;EAAzB,IACAM;AACT;AAED,SAAgBL,SACZM,YACAC,QACA3P,MAAAA;;MAAAA,SAAAA,QAAAA;AAAAA,WAAyB5N;;AAEzB,MAAA,MAAa;AACT,QAAI,CAACgB,WAAWsc,UAAD,KAAgB,CAACtc,WAAWuc,MAAD;AACtC/e,UAAI,2DAAD;AACP,QAAI,CAAC8C,cAAcsM,IAAD;AAAQpP,UAAI,iDAAD;EAChC;AACD,MAAMP,QAAI,cAAG2P,KAAK3P,SAAR,OAAA,cAAiB,OAAU,cAAcwC,UAAS,IAAK;AACjE,MAAM+c,eAAe/F,OACjBxZ,MACA2P,KAAKsP,UAAUO,iBAAiB7P,KAAKsP,SAASK,MAAf,IAAyBA,MAFjC;AAI3B,MAAMV,UAAU,CAACjP,KAAKkP,aAAa,CAAClP,KAAKmP;AACzC,MAAMD,YAAYM,2BAA2BxP,IAAD;AAE5C,MAAIuH,YAAY;AAChB,MAAIoF,cAAc;AAClB,MAAIpZ;AACJ,MAAIwH,WAAcf;AAElB,MAAM8E,SAAUkB,KAAa8F,oBACvBzM,SAASE,aACTyG,KAAKlB,UAAUzF,SAAQ,SAAA;AAE7B,MAAM+T,IAAI,IAAIjB,SACV9b,MACA,WAAA;AACI,QAAIkX,aAAa0H,SAAS;AACtBI,qBAAc;IACjB,WAAU,CAAC1C,aAAa;AACrBA,oBAAc;AACduC,gBAAWG,cAAD;IACb;EACJ,GACDrP,KAAKsP,SACLtP,KAAKuP,kBAXC;AAcV,WAASF,iBAAT;AACI1C,kBAAc;AACd,QAAIS,EAAEd;AAAa;AACnB,QAAIrF,UAAmB;AACvBmG,MAAElG,MAAM,WAAA;AACJ,UAAM4I,YAAYhO,kBAAkB,OAAO,WAAA;AAAA,eAAM4N,WAAWtC,CAAD;MAAhB,CAAR;AACnCnG,gBAAUM,aAAa,CAACzI,OAAOvL,OAAOuc,SAAR;AAC9B/U,iBAAWxH;AACXA,cAAQuc;IACX,CALD;AAMA,QAAIvI,aAAavH,KAAKsE;AAAkBsL,mBAAarc,OAAOwH,UAAUqS,CAAlB;aAC3C,CAAC7F,aAAaN;AAAS2I,mBAAarc,OAAOwH,UAAUqS,CAAlB;AAC5C7F,gBAAY;EACf;AAED6F,IAAEV,UAAF;AACA,SAAOU,EAAED,aAAF;AACV;AAED,SAAS0C,iBAAiBE,cAAcC,QAAxC;AACI,SAAO,WAAA;AACH,QAAI;AACA,aAAOA,OAAOhf,MAAM,MAAMkC,SAAnB;IACV,SAAQnC,GAAG;AACRgf,mBAAaza,KAAK,MAAMvE,CAAxB;IACH;EACJ;AACJ;AC/JD,IAAMkf,qBAAqB;AAC3B,IAAMC,uBAAuB;AAiB7B,SAAgBvX,iBAAiBpI,OAAOiO,MAAMC,MAAAA;AAC1C,SAAO0R,cAAcF,oBAAoB1f,OAAOiO,MAAMC,IAAlC;AACvB;AAiBD,SAAgB7F,mBAAmBrI,OAAOiO,MAAMC,MAAAA;AAC5C,SAAO0R,cAAcD,sBAAsB3f,OAAOiO,MAAMC,IAApC;AACvB;AAED,SAAS0R,cAAcC,MAAwB7f,OAAOiO,MAAMC,MAA5D;AACI,MAAM/F,OACF,OAAO+F,SAAS,aAAa4R,QAAQ9f,OAAOiO,IAAR,IAAiB6R,QAAQ9f,KAAD;AAChE,MAAM+f,KAAKld,WAAWqL,IAAD,IAASA,OAAOD;AACrC,MAAM+R,eAAkBH,OAAN;AAElB,MAAI1X,KAAK6X,YAAD,GAAgB;AACpB7X,SAAK6X,YAAD,EAAgBrF,IAAIoF,EAAxB;EACH,OAAM;AACH5X,SAAK6X,YAAD,IAAiB,oBAAI1b,IAAY,CAACyb,EAAD,CAAhB;EACxB;AAED,SAAO,WAAA;AACH,QAAME,gBAAgB9X,KAAK6X,YAAD;AAC1B,QAAIC,eAAe;AACfA,oBAAa,QAAA,EAAQF,EAArB;AACA,UAAIE,cAAcnK,SAAS,GAAG;AAC1B,eAAO3N,KAAK6X,YAAD;MACd;IACJ;EACJ;AACJ;ACxED,IAAME,QAAQ;AACd,IAAMC,SAAS;AACf,IAAMC,WAAW;AAGjB,SAAgBC,UAAUlV,SAAAA;AAiBtB,MAAIA,QAAQiP,uBAAuB,MAAM;AACrCA,uBAAkB;EACrB;MACO3L,aAA+BtD,QAA/BsD,YAAY0J,iBAAmBhN,QAAnBgN;AACpB,MAAI1J,eAAehF,QAAW;AAC1BrH,gBAAYqM,aACRA,eAAe0R,SACT,OACA1R,eAAeyR,QACf,QACA,OAAOne,UAAU;EAC9B;AACD,MAAI0M,eAAe;AAAerM,gBAAYC,gBAAgB;AAC9D,MAAI8V,mBAAmB1O,QAAW;AAC9B,QAAM6W,KAAKnI,mBAAmBgI,SAASA,SAAShI,mBAAmBiI;AACnEhe,gBAAY+V,iBAAiBmI;AAC7Ble,gBAAYmP,oBAAoB+O,OAAO,QAAQA,OAAOH,SAAS,QAAQ;EAC1E;AACA,GACG,4BACA,8BACA,8BACA,0BACA,iBALH,EAMCva,QAAQ,SAAArG,KAAG;AACT,QAAIA,OAAO4L;AAAS/I,kBAAY7C,GAAD,IAAQ,CAAC,CAAC4L,QAAQ5L,GAAD;EACnD,CARA;AASD6C,cAAYiW,kBAAkB,CAACjW,YAAYkW;AAC3C,MAAelW,YAAYyU,2BAA2B,MAAM;AACxDO,YAAQG,KACJ,0GADJ;EAGH;AACD,MAAIpM,QAAQiS,mBAAmB;AAC3BM,yBAAqBvS,QAAQiS,iBAAT;EACvB;AACJ;SC5CepO,iBACZzJ,QACAgb,YACAC,aACArV,SAAAA;AAEA,MAAA,MAAa;AACT,QAAIxI,UAAUhD,SAAS;AAAGU,UAAI,2CAAD;AAC7B,QAAI,OAAOkF,WAAW;AAClBlF,UAAI,wDAAD;AACP,QAAI6J,gBAAgB3E,MAAD;AACflF,UAAI,sEAAD;AACP,QAAI,CAAC8C,cAAcod,UAAD;AACdlgB,UAAG,iEAAA;AACP,QAAI+I,aAAamX,UAAD,KAAgBnX,aAAaoX,WAAD;AACxCngB,UAAG,uEAAA;EACV;AAED,MAAMogB,cAAc/a,0BAA0B6a,UAAD;AAE7C,MAAM1V,MAAsCoE,mBAAmB1J,QAAQ4F,OAAT,EAAkBzE,KAApC;AAC5CkB,aAAU;AACV,MAAI;AACA5C,YAAQyb,WAAD,EAAc7a,QAAQ,SAAArG,KAAG;AAC5BsL,UAAID;QACArL;QACAkhB,YAAYlhB,GAAD;;QAEX,CAACihB,cAAc,OAAOjhB,OAAOihB,cAAcA,YAAYjhB,GAAD,IAAQ;MAJlE;IAMH,CAPD;EAQH,UATD;AAUIuI,aAAQ;EACX;AACD,SAAOvC;AACV;SCvCe8V,kBAAkBrb,OAAYC,UAAAA;AAC1C,SAAOygB,qBAAqBZ,QAAQ9f,OAAOC,QAAR,CAAR;AAC9B;AAED,SAASygB,qBAAqBhG,MAA9B;AACI,MAAMpE,SAA0B;IAC5BxW,MAAM4a,KAAK9T;EADiB;AAGhC,MAAI8T,KAAKpG,cAAcoG,KAAKpG,WAAW3U,SAAS;AAC5C2W,WAAOoF,eAAeiF,OAAOjG,KAAKpG,UAAN,EAAkB3T,IAAI+f,oBAA5B;AAC1B,SAAOpK;AACV;AAED,SAAgBsK,gBAAgB5gB,OAAYC,UAAAA;AACxC,SAAO4gB,mBAAmBf,QAAQ9f,OAAOC,QAAR,CAAR;AAC5B;AAED,SAAS4gB,mBAAmBnG,MAA5B;AACI,MAAMpE,SAAwB;IAC1BxW,MAAM4a,KAAK9T;EADe;AAG9B,MAAIsR,aAAawC,IAAD;AACZpE,WAAOwK,YAAYzX,MAAM0H,KAAU0J,aAAaC,IAAD,CAA5B,EAA2C/Z,IAASkgB,kBAApD;AACvB,SAAOvK;AACV;AAED,SAASqK,OAAU5F,MAAnB;AACI,SAAO1R,MAAM0H,KAAK,IAAIzM,IAAIyW,IAAR,CAAX;AACV;AC1BD,IAAIgG,cAAc;AAElB,SAAgBC,wBAAAA;AACZ,OAAKtE,UAAU;AAClB;AACDsE,sBAAsBtf,YAAYL,OAAO4f,OAAOvgB,MAAMgB,SAApB;AAElC,SAAgBwf,wBAAwB5gB,OAAAA;AACpC,SAAOA,iBAAiB0gB;AAC3B;AAWD,IAAMhU,iBAAiBhB,qBAAqB,MAAD;AAC3C,IAAMmV,sBAAsBnV,qBAAqB,cAAc;EAAEV,OAAO;AAAT,CAAf;AAEhD,IAAazB,OAAaxI,OAAOD,OAC7B,SAASyI,MAAK2F,MAAMvB,MAApB;AAEI,MAAIlL,YAAYkL,IAAD,GAAQ;AACnB,WAAO/H,gBAAgBsJ,MAAMvB,MAAMjB,cAAb;EACzB;AAED,MAAerK,UAAUhD,WAAW;AAChCU,QAAG,sDAAA;AACP,MAAM+gB,YAAY5R;AAClB,MAAM1P,OAAOshB,UAAUthB,QAAQ;AAG/B,MAAM6F,MAAM,SAANA,OAAM;AACR,QAAM0b,MAAM;AACZ,QAAM9gB,OAAOoC;AACb,QAAM6V,QAAQ,EAAEuI;AAChB,QAAMO,MAAMhI,OAAUxZ,OAAJ,eAAqB0Y,QAArB,WAAqC4I,SAArC,EAAgD3gB,MAAM4gB,KAAK9gB,IAAjE;AACZ,QAAIghB;AACJ,QAAIC,iBAAsD/X;AAE1D,QAAMgY,UAAU,IAAIC,QAAQ,SAAUC,SAASC,QAAnB;AACxB,UAAIC,SAAS;AACbN,iBAAWK;AAEX,eAASE,YAAYnc,MAArB;AACI6b,yBAAiB/X;AACjB,YAAIsY;AACJ,YAAI;AACAA,gBAAMzI,OACCxZ,OADK,eACY0Y,QADZ,cAC6BqJ,UACrCP,IAAIU,IAFI,EAGVjd,KAAKuc,KAAK3b,IAHN;QAIT,SAAQnF,GAAG;AACR,iBAAOohB,OAAOphB,CAAD;QAChB;AAEDwhB,aAAKD,GAAD;MACP;AAED,eAASE,WAAW3R,KAApB;AACIkR,yBAAiB/X;AACjB,YAAIsY;AACJ,YAAI;AACAA,gBAAMzI,OACCxZ,OADK,eACY0Y,QADZ,cAC6BqJ,UACrCP,IAAG,OAAA,CAFK,EAGVvc,KAAKuc,KAAKhR,GAHN;QAIT,SAAQ9P,GAAG;AACR,iBAAOohB,OAAOphB,CAAD;QAChB;AACDwhB,aAAKD,GAAD;MACP;AAED,eAASC,KAAKD,KAAd;AACI,YAAIlf,WAAWkf,OAAD,OAAA,SAACA,IAAKG,IAAN,GAAa;AAEvBH,cAAIG,KAAKF,MAAMJ,MAAf;AACA;QACH;AACD,YAAIG,IAAII;AAAM,iBAAOR,QAAQI,IAAI/e,KAAL;AAC5Bwe,yBAAiBE,QAAQC,QAAQI,IAAI/e,KAApB;AACjB,eAAOwe,eAAgBU,KAAKJ,aAAaG,UAAlC;MACV;AAEDH,kBAAYrY,MAAD;IACd,CA7Ce;AA+ChBgY,YAAQW,SAAS9I,OAAUxZ,OAAJ,eAAqB0Y,QAArB,aAAuC,WAAA;AAC1D,UAAI;AACA,YAAIgJ;AAAgBa,wBAAcb,cAAD;AAEjC,YAAM7b,OAAM2b,IAAG,QAAA,EAAS7X,MAAZ;AAEZ,YAAM6Y,iBAAiBZ,QAAQC,QAAQhc,KAAI3C,KAApB;AACvBsf,uBAAeJ,KAAKtf,MAAMA,IAA1B;AACAyf,sBAAcC,cAAD;AAEbf,iBAAS,IAAIP,sBAAJ,CAAD;MACX,SAAQxgB,GAAG;AACR+gB,iBAAS/gB,CAAD;MACX;IACJ,CAdsB;AAevB,WAAOihB;EACV;AACD9b,MAAI4c,aAAa;AACjB,SAAO5c;AACH,GACRqH,cAxFsB;AA2F1BnD,KAAKyB,QAAQvF,0BAA0Bob,mBAAD;AAEtC,SAASkB,cAAcZ,SAAvB;AACI,MAAI5e,WAAW4e,QAAQW,MAAT;AAAkBX,YAAQW,OAAR;AACnC;AAED,SAAgBI,WACZlM,QAAAA;AAMA,SAAOA;AACV;AAED,SAAgB1M,OAAO9G,IAAAA;AACnB,UAAOA,MAAE,OAAF,SAAAA,GAAIyf,gBAAe;AAC7B;SC9GeE,eAAeziB,OAAO0iB,eAAgB9O,SAAAA;AAClD,MAAIrO;AACJ,MAAI2E,gBAAgBlK,KAAD,KAAWiK,kBAAkBjK,KAAD,KAAWoU,kBAAkBpU,KAAD,GAAS;AAChFuF,aAASod,kBAAkB3iB,KAAD;EAC7B,WAAUgK,mBAAmBhK,KAAD,GAAS;AAClC,QAAe,CAAC+C,YAAY2f,aAAD;AACvB,aAAOriB,IAAG,yFAAA;AAGdkF,aAASod,kBAAkB3iB,OAAO0iB,aAAR;EAC7B,WAAM,MAAa;AAChB,WAAOriB,IAAG,yDAAA;EACb;AACD,MAAekF,OAAOqN,aAAanJ;AAC/B,WAAOpJ,IAAG,6CAAA;AACdkF,SAAOqN,WAAW,OAAO8P,kBAAkB,aAAaA,gBAAgB9O;AACxE,SAAO,WAAA;AACHrO,WAAOqN,WAAWnJ;EACrB;AACJ;SCXemZ,UAAU5iB,OAAO0iB,eAAgB9O,SAAAA;AAC7C,MAAI/Q,WAAW+Q,OAAD;AAAW,WAAOiP,kBAAkB7iB,OAAO0iB,eAAe9O,OAAvB;;AAC5C,WAAOkP,uBAAuB9iB,OAAO0iB,aAAR;AACrC;AAED,SAASI,uBAAuB9iB,OAAO4T,SAAvC;AACI,SAAO+O,kBAAkB3iB,KAAD,EAAQ2T,WAAWC,OAApC;AACV;AAED,SAASiP,kBAAkB7iB,OAAOC,UAAU2T,SAA5C;AACI,SAAO+O,kBAAkB3iB,OAAOC,QAAR,EAAkB0T,WAAWC,OAA9C;AACV;SCrDemP,YAAY/f,OAAO/C,UAAAA;AAC/B,MAAIA,aAAawJ,QAAW;AACxB,QAAIO,mBAAmBhH,KAAD,MAAY;AAAO,aAAO;AAChD,QAAI,CAACA,MAAM0D,KAAD,EAAQsc,QAAQC,IAAIhjB,QAAzB;AAAoC,aAAO;AAChD,QAAMkI,OAAO2X,QAAQ9c,OAAO/C,QAAR;AACpB,WAAOuX,gBAAgBrP,IAAD;EACzB;AACD,SAAOqP,gBAAgBxU,KAAD;AACzB;AAED,SAAgBkgB,WAAWlgB,OAAAA;AACvB,MAAeL,UAAUhD,SAAS;AAC9B,WAAOU,IAAG,mGAAA;AAGd,SAAO0iB,YAAY/f,KAAD;AACrB;AAED,SAAgBmgB,eAAengB,OAAYW,UAAAA;AACvC,MAAe,CAACZ,YAAYY,QAAD;AACvB,WAAOtD,IAAG,wDAAA;AACd,SAAO0iB,YAAY/f,OAAOW,QAAR;AACrB;ACZD,SAASyf,cAAcpgB,OAAO/C,UAA9B;AACI,MAAI,CAAC+C;AAAO,WAAO;AACnB,MAAI/C,aAAawJ,QAAW;AACxB,QAAgBS,gBAAgBlH,KAAD,KAAWiH,kBAAkBjH,KAAD;AACvD,aAAO3C,IACH,+GADM;AAGd,QAAI2J,mBAAmBhH,KAAD,GAAS;AAC3B,aAAOA,MAAM0D,KAAD,EAAQsc,QAAQC,IAAIhjB,QAAzB;IACV;AACD,WAAO;EACV;AAED,SACI+J,mBAAmBhH,KAAD,KAClB,CAAC,CAACA,MAAM0D,KAAD,KACPqB,OAAO/E,KAAD,KACNya,WAAWza,KAAD,KACVwU,gBAAgBxU,KAAD;AAEtB;AAED,SAAgBoG,aAAapG,OAAAA;AACzB,MAAeL,UAAUhD,WAAW;AAChCU,QAAG,uGAAA;AAGP,SAAO+iB,cAAcpgB,KAAD;AACvB;AAED,SAAgBqgB,iBAAiBrgB,OAAYW,UAAAA;AACzC,MAAe,CAACZ,YAAYY,QAAD;AAAY,WAAOtD,IAAG,6CAAA;AACjD,SAAO+iB,cAAcpgB,OAAOW,QAAR;AACvB;SCzBee,KAAKnB,KAAAA;AACjB,MAAIyG,mBAAmBzG,GAAD,GAAO;AACzB,WAAUA,IACNmD,KADyC,EAER4c,MAF3B;EAGb;AACD,MAAIpZ,gBAAgB3G,GAAD,KAAS4G,gBAAgB5G,GAAD,GAAO;AAC9C,WAAO8F,MAAM0H,KAAKxN,IAAImB,KAAJ,CAAX;EACV;AACD,MAAIuF,kBAAkB1G,GAAD,GAAO;AACxB,WAAOA,IAAI5C,IAAI,SAAClB,KAAGC,OAAJ;AAAA,aAAcA;IAAd,CAAR;EACV;AACDW,MAAI,CAAD;AACN;AAMD,SAAgBkjB,OAAOhgB,KAAAA;AACnB,MAAIyG,mBAAmBzG,GAAD,GAAO;AACzB,WAAOmB,KAAKnB,GAAD,EAAM5C,IAAI,SAAApB,KAAG;AAAA,aAAIgE,IAAIhE,GAAD;IAAP,CAAjB;EACV;AACD,MAAI2K,gBAAgB3G,GAAD,GAAO;AACtB,WAAOmB,KAAKnB,GAAD,EAAM5C,IAAI,SAAApB,KAAG;AAAA,aAAIgE,IAAIgJ,IAAIhN,GAAR;IAAJ,CAAjB;EACV;AACD,MAAI4K,gBAAgB5G,GAAD,GAAO;AACtB,WAAO8F,MAAM0H,KAAKxN,IAAIggB,OAAJ,CAAX;EACV;AACD,MAAItZ,kBAAkB1G,GAAD,GAAO;AACxB,WAAOA,IAAIigB,MAAJ;EACV;AACDnjB,MAAI,CAAD;AACN;AAQD,SAAgBojB,QAAQlgB,KAAAA;AACpB,MAAIyG,mBAAmBzG,GAAD,GAAO;AACzB,WAAOmB,KAAKnB,GAAD,EAAM5C,IAAI,SAAApB,KAAG;AAAA,aAAI,CAACA,KAAKgE,IAAIhE,GAAD,CAAT;IAAJ,CAAjB;EACV;AACD,MAAI2K,gBAAgB3G,GAAD,GAAO;AACtB,WAAOmB,KAAKnB,GAAD,EAAM5C,IAAI,SAAApB,KAAG;AAAA,aAAI,CAACA,KAAKgE,IAAIgJ,IAAIhN,GAAR,CAAN;IAAJ,CAAjB;EACV;AACD,MAAI4K,gBAAgB5G,GAAD,GAAO;AACtB,WAAO8F,MAAM0H,KAAKxN,IAAIkgB,QAAJ,CAAX;EACV;AACD,MAAIxZ,kBAAkB1G,GAAD,GAAO;AACxB,WAAOA,IAAI5C,IAAI,SAACpB,KAAKG,OAAN;AAAA,aAAgB,CAACA,OAAOH,GAAR;IAAhB,CAAR;EACV;AACDc,MAAI,CAAD;AACN;AAQD,SAAgBqJ,KAAInG,KAAUhE,KAAUyD,OAAAA;AACpC,MAAIL,UAAUhD,WAAW,KAAK,CAACwK,gBAAgB5G,GAAD,GAAO;AACjDqE,eAAU;AACV,QAAM2b,UAAShkB;AACf,QAAI;AACA,eAASA,QAAOgkB,SAAhB;AAAwB7Z,QAAAA,KAAInG,KAAKhE,MAAKgkB,QAAOhkB,IAAD,CAAjB;MAA3B;IACH,UAFD;AAGIuI,eAAQ;IACX;AACD;EACH;AACD,MAAIkC,mBAAmBzG,GAAD,GAAO;AACtBA,QAAoCmD,KAAD,EAAQgd,KAAKnkB,KAAKyD,KAArD;EACN,WAAUkH,gBAAgB3G,GAAD,GAAO;AAC7BA,QAAImG,IAAInK,KAAKyD,KAAb;EACH,WAAUmH,gBAAgB5G,GAAD,GAAO;AAC7BA,QAAIoX,IAAIpb,GAAR;EACH,WAAU0K,kBAAkB1G,GAAD,GAAO;AAC/B,QAAI,OAAOhE,QAAQ;AAAUA,YAAMokB,SAASpkB,KAAK,EAAN;AAC3C,QAAIA,MAAM;AAAGc,UAAG,qBAAoBd,MAApB,GAAA;AAChBqI,eAAU;AACV,QAAIrI,OAAOgE,IAAI5D;AAAQ4D,UAAI5D,SAASJ,MAAM;AAC1CgE,QAAIhE,GAAD,IAAQyD;AACX8E,aAAQ;EACX;AAAMzH,QAAI,CAAD;AACb;AAMD,SAAgBujB,OAAOrgB,KAAUhE,KAAAA;AAC7B,MAAIyK,mBAAmBzG,GAAD,GAAO;AACtBA,QAAoCmD,KAAD,EAAQmd,QAAQtkB,GAAnD;EACN,WAAU2K,gBAAgB3G,GAAD,GAAO;AAC7BA,QAAG,QAAA,EAAQhE,GAAX;EACH,WAAU4K,gBAAgB5G,GAAD,GAAO;AAC7BA,QAAG,QAAA,EAAQhE,GAAX;EACH,WAAU0K,kBAAkB1G,GAAD,GAAO;AAC/B,QAAI,OAAOhE,QAAQ;AAAUA,YAAMokB,SAASpkB,KAAK,EAAN;AAC3CgE,QAAI2Z,OAAO3d,KAAK,CAAhB;EACH,OAAM;AACHc,QAAI,CAAD;EACN;AACJ;AAMD,SAAgB4iB,IAAI1f,KAAUhE,KAAAA;AAC1B,MAAIyK,mBAAmBzG,GAAD,GAAO;AACzB,WAASA,IAAoCmD,KAAD,EAAQod,KAAKvkB,GAAhD;EACZ,WAAU2K,gBAAgB3G,GAAD,GAAO;AAC7B,WAAOA,IAAI0f,IAAI1jB,GAAR;EACV,WAAU4K,gBAAgB5G,GAAD,GAAO;AAC7B,WAAOA,IAAI0f,IAAI1jB,GAAR;EACV,WAAU0K,kBAAkB1G,GAAD,GAAO;AAC/B,WAAOhE,OAAO,KAAKA,MAAMgE,IAAI5D;EAChC;AACDU,MAAI,EAAD;AACN;AAKD,SAAgBkM,IAAIhJ,KAAUhE,KAAAA;AAC1B,MAAI,CAAC0jB,IAAI1f,KAAKhE,GAAN;AAAY,WAAOkK;AAC3B,MAAIO,mBAAmBzG,GAAD,GAAO;AACzB,WAASA,IAAoCmD,KAAD,EAAQqd,KAAKxkB,GAAhD;EACZ,WAAU2K,gBAAgB3G,GAAD,GAAO;AAC7B,WAAOA,IAAIgJ,IAAIhN,GAAR;EACV,WAAU0K,kBAAkB1G,GAAD,GAAO;AAC/B,WAAOA,IAAIhE,GAAD;EACb;AACDc,MAAI,EAAD;AACN;AAED,SAAgB2jB,kBAAkBzgB,KAAahE,KAAkByL,YAAAA;AAC7D,MAAIhB,mBAAmBzG,GAAD,GAAO;AACzB,WAASA,IAAoCmD,KAAD,EAAQgF,gBAAgBnM,KAAKyL,UAAhE;EACZ;AACD3K,MAAI,EAAD;AACN;AAED,SAAgB4jB,WAAW1gB,KAAAA;AACvB,MAAIyG,mBAAmBzG,GAAD,GAAO;AACzB,WAASA,IAAoCmD,KAAD,EAAQwd,SAA3C;EACZ;AACD7jB,MAAI,EAAD;AACN;SCxHe8jB,QAAQnkB,OAAOokB,UAAWC,UAAWtQ,iBAAAA;AACjD,MAAIlR,WAAWwhB,QAAD;AACV,WAAOC,0BAA0BtkB,OAAOokB,UAAUC,UAAUtQ,eAA5B;;AAC/B,WAAOwQ,kBAAkBvkB,OAAOokB,UAAUC,QAAlB;AAChC;AAED,SAASE,kBAAkBvkB,OAAOwH,UAAUuM,iBAA5C;AACI,SAAO4O,kBAAkB3iB,KAAD,EAAQ8T,SAAStM,UAAUuM,eAA5C;AACV;AAED,SAASuQ,0BAA0BtkB,OAAOC,UAAUuH,UAAUuM,iBAA9D;AACI,SAAO4O,kBAAkB3iB,OAAOC,QAAR,EAAkB6T,SAAStM,UAAUuM,eAAtD;AACV;ACrDD,SAASyQ,MAAY7jB,MAAoBpB,KAAQyD,OAAjD;AACIrC,EAAAA,KAAI+I,IAAInK,KAAKyD,KAAb;AACA,SAAOA;AACV;AAED,SAASyhB,WAAWpZ,QAAQqZ,eAA5B;AACI,MACIrZ,UAAU,QACV,OAAOA,WAAW,YAClBA,kBAAkBuF,QAClB,CAACxH,aAAaiC,MAAD;AAEb,WAAOA;AAEX,MAAI+I,kBAAkB/I,MAAD,KAAYmM,gBAAgBnM,MAAD;AAC5C,WAAOoZ,WAAWpZ,OAAOkB,IAAP,GAAcmY,aAAf;AACrB,MAAIA,cAAczB,IAAI5X,MAAlB,GAA2B;AAC3B,WAAOqZ,cAAcnY,IAAIlB,MAAlB;EACV;AACD,MAAIpB,kBAAkBoB,MAAD,GAAU;AAC3B,QAAM1F,MAAM6e,MAAME,eAAerZ,QAAQ,IAAIhC,MAAMgC,OAAO1L,MAAjB,CAAxB;AACjB0L,WAAOzF,QAAQ,SAAC5C,OAAOia,KAAR;AACXtX,UAAIsX,GAAD,IAAQwH,WAAWzhB,OAAO0hB,aAAR;IACxB,CAFD;AAGA,WAAO/e;EACV;AACD,MAAIwE,gBAAgBkB,MAAD,GAAU;AACzB,QAAM1F,OAAM6e,MAAME,eAAerZ,QAAQ,oBAAI/G,IAAJ,CAAxB;AACjB+G,WAAOzF,QAAQ,SAAA5C,OAAK;AAChB2C,WAAIgV,IAAI8J,WAAWzhB,OAAO0hB,aAAR,CAAlB;IACH,CAFD;AAGA,WAAO/e;EACV;AACD,MAAIuE,gBAAgBmB,MAAD,GAAU;AACzB,QAAM1F,QAAM6e,MAAME,eAAerZ,QAAQ,oBAAIjH,IAAJ,CAAxB;AACjBiH,WAAOzF,QAAQ,SAAC5C,OAAOzD,KAAR;AACXoG,YAAI+D,IAAInK,KAAKklB,WAAWzhB,OAAO0hB,aAAR,CAAvB;IACH,CAFD;AAGA,WAAO/e;EACV,OAAM;AAEH,QAAMA,QAAM6e,MAAME,eAAerZ,QAAQ,CAAA,CAAxB;AACjB4Y,eAAW5Y,MAAD,EAASzF,QAAQ,SAACrG,KAAD;AACvB,UAAIkC,gBAAgBqD,qBAAqBC,KAAKsG,QAAQ9L,GAAlD,GAAwD;AACxDoG,cAAIpG,GAAD,IAAQklB,WAAWpZ,OAAO9L,GAAD,GAAOmlB,aAAd;MACxB;IACJ,CAJD;AAKA,WAAO/e;EACV;AACJ;AAKD,SAAgBgf,KAAQtZ,QAAWF,SAAAA;AAC/B,MAAeA;AAAS9K,QAAI,iCAAD;AAC3B,SAAOokB,WAAWpZ,QAAQ,oBAAIjH,IAAJ,CAAT;AACpB;SChEe0Y,QAAAA;AACZ,MAAI;AAAUzc,QAAG,+CAAA;AACjB,MAAI0c,kBAAkB;oCAFDxc,OAAAA,IAAAA,MAAAA,IAAAA,GAAAA,OAAAA,GAAAA,OAAAA,MAAAA,QAAAA;AAAAA,SAAAA,IAAAA,IAAAA,UAAAA,IAAAA;;AAGrB,MAAI,OAAOA,KAAKA,KAAKZ,SAAS,CAAf,MAAsB;AAAWod,sBAAkBxc,KAAKqkB,IAAL;AAClE,MAAM1kB,aAAa2kB,gBAAgBtkB,IAAD;AAClC,MAAI,CAACL,YAAY;AACb,WAAOG,IAAG,+IAAA;EAGb;AACD,MAAIH,WAAW8U,eAAeC,UAAUC,MAAM;AAC1CkC,YAAQC,IAAR,mBAA6BnX,WAAW0G,QAAxC,mBAAA;EACH;AACD1G,aAAW8U,aAAa+H,kBAAkB9H,UAAUiG,QAAQjG,UAAU6P;AACzE;AAED,SAASD,gBAAgBtkB,MAAzB;AACI,UAAQA,KAAKZ,QAAb;IACI,KAAK;AACD,aAAOyC,YAAYgP;IACvB,KAAK;AACD,aAAO0O,QAAQvf,KAAK,CAAD,CAAL;IAClB,KAAK;AACD,aAAOuf,QAAQvf,KAAK,CAAD,GAAKA,KAAK,CAAD,CAAd;EANtB;AAQH;ACrBD,SAAgBwkB,YAAezL,SAAiB0L,SAAAA;MAAAA,YAAAA,QAAAA;AAAAA,cAAUvb;;AACtD7B,aAAU;AACV,MAAI;AACA,WAAO0R,QAAO7Y,MAAMukB,OAAb;EACV,UAFD;AAGIld,aAAQ;EACX;AACJ;SCUemd,KAAKC,WAAgB1V,MAAYvB,MAAAA;AAC7C,MAAItL,UAAUhD,WAAW,KAAM6P,QAAQ,OAAOA,SAAS;AACnD,WAAO2V,YAAYD,WAAW1V,IAAZ;AACtB,SAAO4V,MAAMF,WAAW1V,MAAMvB,QAAQ,CAAA,CAA1B;AACf;AAED,SAASmX,MAAMF,WAA0B9F,QAAgB3P,MAAzD;AACI,MAAI4V;AACJ,MAAI,OAAO5V,KAAK6V,YAAY,UAAU;AAClCD,oBAAgBlL,WAAW,WAAA;AACvB,UAAI,CAACoL,SAAS7e,KAAD,EAAQqV,aAAa;AAC9BwJ,iBAAQ;AACR,YAAMjlB,QAAQ,IAAII,MAAM,cAAV;AACd,YAAI+O,KAAKsP;AAAStP,eAAKsP,QAAQze,KAAb;;AACb,gBAAMA;MACd;IACJ,GAAEmP,KAAK6V,OAPkB;EAQ7B;AAED7V,OAAK3P,OAAO,OAAU2P,KAAK3P,QAAQ,UAAUwC,UAAS,IAAK;AAC3D,MAAM+c,eAAetT,aACjB,OAAU0D,KAAK3P,OAAO,YAAY,eAClCsf,MAF6B;AAKjC,MAAImG,WAAWrO,QAAQ,SAAA2F,GAAC;AAEpB,QAAI2I,OAAOjU,kBAAkB,OAAO2T,SAAR;AAC5B,QAAIM,MAAM;AACN3I,QAAEF,QAAF;AACA,UAAI0I;AAAeI,qBAAaJ,aAAD;AAC/BhG,mBAAY;IACf;EACJ,GAAE5P,IARmB;AAStB,SAAO8V;AACV;AAED,SAASJ,YACLD,WACAzV,MAFJ;AAII,MAAeA,QAAQA,KAAKsP;AACxB,WAAO1e,IAAG,wDAAA;AACd,MAAI+hB;AACJ,MAAMzc,MAAM,IAAI+b,QAAQ,SAACC,SAASC,QAAV;AACpB,QAAI2D,WAAWH,MAAMF,WAAWvD,SAAZ,SAAA,CAAA,GAA0BlS,MAA1B;MAAgCsP,SAAS6C;IAAzC,CAAA,CAAA;AACpBQ,aAAS,SAAAA,UAAA;AACLmD,eAAQ;AACR3D,aAAO,gBAAD;IACT;EACJ,CANW;AAOVjc,MAAYyc,SAASA;AACvB,SAAOzc;AACV;AClED,SAAS+f,OAAOngB,QAAhB;AACI,SAAOA,OAAOmB,KAAD;AAChB;AAID,IAAMif,mBAAsC;EACxC1C,KADwC,SAAAA,KACpC1d,QAA6BzF,MADO;AAEpC,QAAesC,YAAYgP;AACvBlP,gCACI,+EADqB;AAG7B,WAAOwjB,OAAOngB,MAAD,EAASue,KAAKhkB,IAApB;EACV;EACDyM,KARwC,SAAAA,KAQpChH,QAA6BzF,MARO;AASpC,WAAO4lB,OAAOngB,MAAD,EAASwe,KAAKjkB,IAApB;EACV;EACD4J,KAXwC,SAAAA,KAWpCnE,QAA6BzF,MAAmBkD,OAXZ;;AAYpC,QAAI,CAACD,YAAYjD,IAAD;AAAQ,aAAO;AAC/B,QAAe,CAAC4lB,OAAOngB,MAAD,EAASyd,QAAQC,IAAInjB,IAA3B,GAAkC;AAC9CoC,gCACI,yFADqB;IAG5B;AAED,YAAA,eAAOwjB,OAAOngB,MAAD,EAASme,KAAK5jB,MAAMkD,OAAO,IAAjC,MAAP,OAAA,eAAiD;EACpD;EACD4iB,gBArBwC,SAAA,eAqBzBrgB,QAA6BzF,MArBJ;;AAsBpC,QAAA,MAAa;AACToC,gCACI,gFADqB;IAG5B;AACD,QAAI,CAACa,YAAYjD,IAAD;AAAQ,aAAO;AAE/B,YAAA,kBAAO4lB,OAAOngB,MAAD,EAASse,QAAQ/jB,MAAM,IAA7B,MAAP,OAAA,kBAA6C;EAChD;EACD0B,gBA/BwC,SAAAA,gBAgCpC+D,QACAzF,MACAkL,YAlCoC;;AAoCpC,QAAA,MAAa;AACT9I,gCACI,oFADqB;IAG5B;AAED,YAAA,wBAAOwjB,OAAOngB,MAAD,EAASmG,gBAAgB5L,MAAMkL,UAArC,MAAP,OAAA,wBAA2D;EAC9D;EACDhG,SA5CwC,SAAAA,SA4ChCO,QA5CgC;AA6CpC,QAAenD,YAAYgP;AACvBlP,gCACI,oFADqB;AAG7B,WAAOwjB,OAAOngB,MAAD,EAAS2e,SAAf;EACV;EACD2B,mBAnDwC,SAAA,kBAmDtBtgB,QAnDsB;AAoDpClF,QAAI,EAAD;EACN;AArDuC;AAwD5C,SAAgB6O,0BACZ3J,QACA4F,SAAAA;;AAEAlJ,gBAAa;AACbsD,WAAS0J,mBAAmB1J,QAAQ4F,OAAT;AAC3B,UAAA,wBAAQ,gBAAA5F,OAAOmB,KAAD,GAAQoF,WAAtB,OAAA,uBAAQ,cAAcA,SAAW,IAAI/J,MAAMwD,QAAQogB,gBAAlB;AACpC;SC1EerS,gBAAgBwS,eAAAA;AAC5B,SAAOA,cAAcrT,kBAAkBhJ,UAAaqc,cAAcrT,cAAc9S,SAAS;AAC5F;AAED,SAAgBkU,oBACZiS,eACAlS,SAAAA;AAEA,MAAMmS,eAAeD,cAAcrT,kBAAkBqT,cAAcrT,gBAAgB,CAAA;AACnFsT,eAAalL,KAAKjH,OAAlB;AACA,SAAOpR,KAAK,WAAA;AACR,QAAMya,MAAM8I,aAAavL,QAAQ5G,OAArB;AACZ,QAAIqJ,QAAQ;AAAI8I,mBAAa7I,OAAOD,KAAK,CAAzB;EACnB,CAHU;AAId;AAED,SAAgBzJ,gBACZsS,eACAvS,QAAAA;AAEA,MAAM4D,QAAQ3F,eAAc;AAC5B,MAAI;AAEA,QAAMuU,eAAY,CAAA,EAAA,OAAQD,cAAcrT,iBAAiB,CAAA,CAAvC;AAClB,aAASsF,IAAI,GAAGD,IAAIiO,aAAapmB,QAAQoY,IAAID,GAAGC,KAAK;AACjDxE,eAASwS,aAAahO,CAAD,EAAIxE,MAAhB;AACT,UAAIA,UAAU,CAAEA,OAAetC;AAAM5Q,YAAI,EAAD;AACxC,UAAI,CAACkT;AAAQ;IAChB;AACD,WAAOA;EACV,UATD;AAUIrB,iBAAaiF,KAAD;EACf;AACJ;SCnCe1D,aAAauS,YAAAA;AACzB,SAAOA,WAAWtT,qBAAqBjJ,UAAauc,WAAWtT,iBAAiB/S,SAAS;AAC5F;AAED,SAAgBqU,iBAAiBgS,YAAyBpS,SAAAA;AACtD,MAAMiK,YAAYmI,WAAWtT,qBAAqBsT,WAAWtT,mBAAmB,CAAA;AAChFmL,YAAUhD,KAAKjH,OAAf;AACA,SAAOpR,KAAK,WAAA;AACR,QAAMya,MAAMY,UAAUrD,QAAQ5G,OAAlB;AACZ,QAAIqJ,QAAQ;AAAIY,gBAAUX,OAAOD,KAAK,CAAtB;EACnB,CAHU;AAId;AAED,SAAgBvJ,gBAAmBsS,YAAyBzS,QAAAA;AACxD,MAAM4D,QAAQ3F,eAAc;AAC5B,MAAIqM,YAAYmI,WAAWtT;AAC3B,MAAI,CAACmL;AAAW;AAChBA,cAAYA,UAAU2F,MAAV;AACZ,WAASzL,IAAI,GAAGD,IAAI+F,UAAUle,QAAQoY,IAAID,GAAGC,KAAK;AAC9C8F,cAAU9F,CAAD,EAAIxE,MAAb;EACH;AACDrB,eAAaiF,KAAD;AACf;SCJe8O,eACZ1gB,QACAib,aACArV,SAAAA;AAEA,MAAMN,MAAsCoE,mBAAmB1J,QAAQ4F,OAAT,EAAkBzE,KAApC;AAC5CkB,aAAU;AACV,MAAI;AAAA,QAAA;AAEA,KAAA,eAAA4Y,gBAAW,OAAX,eAAAA,cAAgB/Z,yBAAyBlB,MAAD;AAGxCP,YAAQwb,WAAD,EAAc5a,QAAQ,SAAArG,KAAG;AAAA,aAAIsL,IAAIF,MAAMpL,KAAKihB,YAAajhB,GAAD,CAA3B;IAAJ,CAAhC;EACH,UAND;AAOIuI,aAAQ;EACX;AACD,SAAOvC;AACV;AAGD,IAAM2gB,aAAapgB,OAAO,WAAD;AAEzB,SAAgBqgB,mBACZ5gB,QACA6gB,WACAjb,SAAAA;AAEA,MAAA,MAAa;AACT,QAAI,CAAChI,cAAcoC,MAAD,KAAY,CAACpC,cAAc9B,OAAOgC,eAAekC,MAAtB,CAAD;AACxClF,UAAG,gFAAA;AACP,QAAI2J,mBAAmBzE,MAAD;AAClBlF,UAAG,4EAAA;EACV;AAID,MAAI8C,cAAcoC,MAAD,GAAU;AACvB,WAAOyJ,iBAAiBzJ,QAAQA,QAAQ6gB,WAAWjb,OAA5B;EAC1B;AAED,MAAMN,MAAsCoE,mBAAmB1J,QAAQ4F,OAAT,EAAkBzE,KAApC;AAI5C,MAAI,CAACnB,OAAO2gB,UAAD,GAAc;AACrB,QAAM9iB,QAAQ/B,OAAOgC,eAAekC,MAAtB;AACd,QAAMb,QAAO,IAAIJ,IAAJ,CAAA,EAAA,OAAYU,QAAQO,MAAD,GAAaP,QAAQ5B,KAAD,CAAvC,CAAA;AACbsB,IAAAA,MAAI,QAAA,EAAQ,aAAZ;AACAA,IAAAA,MAAI,QAAA,EAAQgC,KAAZ;AACAjD,kBAAcL,OAAO8iB,YAAYxhB,KAApB;EAChB;AAEDkD,aAAU;AACV,MAAI;AACArC,WAAO2gB,UAAD,EAAatgB,QAAQ,SAAArG,KAAG;AAAA,aAC1BsL,IAAIF;QACApL;;QAEA,CAAC6mB,YAAY,OAAO7mB,OAAO6mB,YAAYA,UAAU7mB,GAAD,IAAQ;MAH5D;IAD0B,CAA9B;EAOH,UARD;AASIuI,aAAQ;EACX;AACD,SAAOvC;AACV;ACxDD,IAAM8gB,SAAS;AACR,IAAMlT,SAAS;AACf,IAAMmT,kBAAkB;AAgD/B,IAAMC,aAAa;EACfha,KADe,SAAAA,KACXhH,QAAQzF,MADG;AAEX,QAAM+K,MAAqCtF,OAAOmB,KAAD;AACjD,QAAI5G,SAAS4G;AAAO,aAAOmE;AAC3B,QAAI/K,SAAS;AAAU,aAAO+K,IAAI2b,gBAAJ;AAC9B,QAAI,OAAO1mB,SAAS,YAAY,CAAC2mB,MAAM3mB,IAAD,GAAe;AACjD,aAAO+K,IAAIkZ,KAAKJ,SAAS7jB,IAAD,CAAjB;IACV;AACD,QAAIwF,QAAQohB,iBAAiB5mB,IAAlB,GAAyB;AAChC,aAAO4mB,gBAAgB5mB,IAAD;IACzB;AACD,WAAOyF,OAAOzF,IAAD;EAChB;EACD4J,KAbe,SAAAA,KAaXnE,QAAQzF,MAAMkD,OAbH;AAcX,QAAM6H,MAAqCtF,OAAOmB,KAAD;AACjD,QAAI5G,SAAS,UAAU;AACnB+K,UAAI8b,gBAAgB3jB,KAApB;IACH;AACD,QAAI,OAAOlD,SAAS,YAAY2mB,MAAM3mB,IAAD,GAAQ;AACzCyF,aAAOzF,IAAD,IAASkD;IAClB,OAAM;AAEH6H,UAAI6Y,KAAKC,SAAS7jB,IAAD,GAAQkD,KAAzB;IACH;AACD,WAAO;EACV;EACD6iB,mBA1Be,SAAAA,qBAAA;AA2BXxlB,QAAI,EAAD;EACN;AA5Bc;AA+BnB,IAAaumB,gCAAb,WAAA;AAWI,WAAAA,+BACI9mB,MACA6M,UACOka,QACAC,aAJX;QACIhnB,SAAAA,QAAAA;AAAAA,aAAO,OAAU,qBAAqBwC,UAAS,IAAK;;SAE7CukB,SAAAA;SACAC,cAAAA;SAbXC,QAAAA;SACS/D,UAAiB,CAAA;SAC1BvQ,gBAAAA;SACAC,mBAAAA;SACAsU,YAAAA;SACApU,WAAAA;SACA9G,SAAAA;SACAmb,mBAAmB;AAKR,SAAA,SAAAJ;AACA,SAAA,cAAAC;AAEP,SAAKC,QAAQ,IAAIpgB,KAAK7G,IAAT;AACb,SAAKknB,YAAY,SAACE,MAAMC,MAAP;AAAA,aACbxa,SAASua,MAAMC,MAAM,OAAUrnB,OAAO,SAAS,qBAAvC;IADK;EAEpB;AApBL,MAAA,SAAA8mB,+BAAA;AAAA,SAsBIQ,gBAAA,SAAA,cAAcpkB,OAAd;AACI,QAAI,KAAK4P,aAAanJ;AAAW,aAAO,KAAKmJ,SAAS5P,KAAd;AACxC,WAAOA;EACV;AAzBL,SA2BIqkB,iBAAA,SAAA,eAAe9D,SAAf;AACI,QAAI,KAAK3Q,aAAanJ,UAAa8Z,QAAO5jB,SAAS;AAC/C,aAAO4jB,QAAO5iB,IAAI,KAAKiS,QAAhB;AACX,WAAO2Q;EACV;AA/BL,SAiCI5P,aAAA,SAAA,WAAWC,SAAX;AACI,WAAOC,oBAAmE,MAAMD,OAAtD;EAC7B;AAnCL,SAqCIE,WAAA,SAAA,SACItM,UACAuM,iBAFJ;QAEIA,oBAAAA,QAAAA;AAAAA,wBAAkB;;AAElB,QAAIA,iBAAiB;AACjBvM,eAA4B;QACxBsL,gBAAgB;QAChBpP,QAAQ,KAAKoI;QACbiH,iBAAiB,KAAKgU,MAAMngB;QAC5BqK,MAAM;QACNvR,OAAO;QACP4nB,OAAO,KAAKtE,QAAQQ,MAAb;QACP+D,YAAY,KAAKvE,QAAQrjB;QACzB6nB,SAAS,CAAA;QACTC,cAAc;MATU,CAApB;IAWX;AACD,WAAOzT,iBAAiB,MAAMxM,QAAP;EAC1B;AAvDL,SAyDIgf,kBAAA,SAAA,kBAAA;AACI,SAAKO,MAAMrf,eAAX;AACA,WAAO,KAAKsb,QAAQrjB;EACvB;AA5DL,SA8DIgnB,kBAAA,SAAA,gBAAgBe,WAAhB;AACI,QAAI,OAAOA,cAAc,YAAYA,YAAY;AAAGrnB,UAAI,mBAAmBqnB,SAApB;AACvD,QAAIC,gBAAgB,KAAK3E,QAAQrjB;AACjC,QAAI+nB,cAAcC;AAAe;aACxBD,YAAYC,eAAe;AAChC,UAAMC,WAAW,IAAIve,MAAMqe,YAAYC,aAAtB;AACjB,eAAS5P,IAAI,GAAGA,IAAI2P,YAAYC,eAAe5P,KAA/C;AAAoD6P,iBAAS7P,CAAD,IAAMtO;MAAlE;AACA,WAAKoe,iBAAiBF,eAAe,GAAGC,QAAxC;IACH;AAAM,WAAKC,iBAAiBH,WAAWC,gBAAgBD,SAAjD;EACV;AAvEL,SAyEII,qBAAA,SAAA,mBAAmBC,WAAmBC,OAAtC;AACI,QAAID,cAAc,KAAKd;AAAkB5mB,UAAI,EAAD;AAC5C,SAAK4mB,oBAAoBe;AACzB,QAAI,KAAKlB,eAAekB,QAAQ;AAAGC,yBAAmBF,YAAYC,QAAQ,CAArB;EACxD;AA7EL,SA+EIH,mBAAA,SAAA,iBAAiBnoB,OAAewoB,aAAsBN,UAAtD;;AACIvU,wCAAoC,KAAK0T,KAAN;AACnC,QAAMpnB,SAAS,KAAKqjB,QAAQrjB;AAE5B,QAAID,UAAU+J;AAAW/J,cAAQ;aACxBA,QAAQC;AAAQD,cAAQC;aACxBD,QAAQ;AAAGA,cAAQyoB,KAAKC,IAAI,GAAGzoB,SAASD,KAArB;AAE5B,QAAIiD,UAAUhD,WAAW;AAAGuoB,oBAAcvoB,SAASD;aAC1CwoB,gBAAgBze,UAAaye,gBAAgB;AAAMA,oBAAc;;AACrEA,oBAAcC,KAAKC,IAAI,GAAGD,KAAKE,IAAIH,aAAavoB,SAASD,KAA/B,CAAZ;AAEnB,QAAIkoB,aAAane;AAAWme,iBAAWjmB;AAEvC,QAAI2R,gBAAgB,IAAD,GAAQ;AACvB,UAAMC,SAASC,gBAAuC,MAAa;QAC/D9P,QAAQ,KAAKoI;QACbmF,MAAMoV;QACN3mB;QACA+nB,cAAcS;QACdZ,OAAOM;MALwD,CAArC;AAO9B,UAAI,CAACrU;AAAQ,eAAO5R;AACpBumB,oBAAc3U,OAAOkU;AACrBG,iBAAWrU,OAAO+T;IACrB;AAEDM,eACIA,SAASjoB,WAAW,IAAIioB,WAAWA,SAASjnB,IAAI,SAAAwI,GAAC;AAAA,aAAI,MAAK6d,UAAU7d,GAAGM,MAAlB;IAAJ,CAAd;AACvC,QAAI,KAAKqd,eAAL,MAA6B;AAC7B,UAAMwB,cAAcV,SAASjoB,SAASuoB;AACtC,WAAKJ,mBAAmBnoB,QAAQ2oB,WAAhC;IACH;AACD,QAAM3iB,MAAM,KAAK4iB,uBAAuB7oB,OAAOwoB,aAAaN,QAAhD;AAEZ,QAAIM,gBAAgB,KAAKN,SAASjoB,WAAW;AACzC,WAAK6oB,mBAAmB9oB,OAAOkoB,UAAUjiB,GAAzC;AACJ,WAAO,KAAK0hB,eAAe1hB,GAApB;EACV;AArHL,SAuHI4iB,yBAAA,SAAA,uBAAuB7oB,OAAewoB,aAAqBN,UAA3D;AACI,QAAIA,SAASjoB,SAAS2mB,iBAAiB;AAAA,UAAA;AACnC,cAAO,gBAAA,KAAKtD,SAAQ9F,OAAb,MAAA,eAAA,CAAoBxd,OAAOwoB,WAA3B,EAAA,OAA2CN,QAA3C,CAAA;IACV,OAAM;AACH,UAAMjiB,MAAM,KAAKqd,QAAQQ,MAAM9jB,OAAOA,QAAQwoB,WAAlC;AACZ,UAAIO,WAAW,KAAKzF,QAAQQ,MAAM9jB,QAAQwoB,WAA3B;AACf,WAAKlF,QAAQrjB,SAASD,QAAQkoB,SAASjoB,SAASuoB;AAChD,eAASnQ,IAAI,GAAGA,IAAI6P,SAASjoB,QAAQoY,KAArC;AAA0C,aAAKiL,QAAQtjB,QAAQqY,CAArB,IAA0B6P,SAAS7P,CAAD;MAA5E;AACA,eAASA,KAAI,GAAGA,KAAI0Q,SAAS9oB,QAAQoY,MAArC;AACI,aAAKiL,QAAQtjB,QAAQkoB,SAASjoB,SAASoY,EAAvC,IAA4C0Q,SAAS1Q,EAAD;MADxD;AAEA,aAAOpS;IACV;EACJ;AAnIL,SAqII+iB,0BAAA,SAAA,wBAAwBhpB,OAAe4K,UAAeE,UAAtD;AACI,QAAM+H,YAAY,CAAC,KAAKsU,UAAUnW,aAAY;AAC9C,QAAM6L,SAAS9I,aAAa,IAAD;AAC3B,QAAMF,SACFgJ,UAAUhK,YACH;MACGO,gBAAgB;MAChBpP,QAAQ,KAAKoI;MACbmF,MAAMkC;MACNJ,iBAAiB,KAAKgU,MAAMngB;MAC5BlH;MACA4K;MACAE;IAPH,IASD;AAIV,QAAe+H;AAAWvB,qBAAeuC,MAAD;AACxC,SAAKwT,MAAMpf,cAAX;AACA,QAAI4U;AAAQ7I,sBAAgB,MAAMH,MAAP;AAC3B,QAAehB;AAAWJ,mBAAY;EACzC;AA3JL,SA6JIqW,qBAAA,SAAA,mBAAmB9oB,OAAe4nB,OAAcE,SAAhD;AACI,QAAMjV,YAAY,CAAC,KAAKsU,UAAUnW,aAAY;AAC9C,QAAM6L,SAAS9I,aAAa,IAAD;AAC3B,QAAMF,SACFgJ,UAAUhK,YACH;MACGO,gBAAgB;MAChBpP,QAAQ,KAAKoI;MACbiH,iBAAiB,KAAKgU,MAAMngB;MAC5BqK,MAAMoV;MACN3mB;MACA8nB;MACAF;MACAG,cAAcD,QAAQ7nB;MACtB4nB,YAAYD,MAAM3nB;IATrB,IAWD;AAEV,QAAe4S;AAAWvB,qBAAeuC,MAAD;AACxC,SAAKwT,MAAMpf,cAAX;AAEA,QAAI4U;AAAQ7I,sBAAgB,MAAMH,MAAP;AAC3B,QAAehB;AAAWJ,mBAAY;EACzC;AApLL,SAsLI4R,OAAA,SAAA,KAAKrkB,OAAL;AACI,QAAIA,QAAQ,KAAKsjB,QAAQrjB,QAAQ;AAC7B,WAAKonB,MAAMrf,eAAX;AACA,aAAO,KAAK0f,cAAc,KAAKpE,QAAQtjB,KAAb,CAAnB;IACV;AACD0X,YAAQG,KACJ,OAAA,gCACoC7X,QADpC,kDAEsDA,QAFtD,8BAEuF,KAAKsjB,QAAQrjB,SAFpG,gFADJ;EAKH;AAhML,SAkMI+jB,OAAA,SAAA,KAAKhkB,OAAe4K,UAApB;AACI,QAAMiZ,UAAS,KAAKP;AACpB,QAAItjB,QAAQ6jB,QAAO5jB,QAAQ;AAEvB0T,0CAAoC,KAAK0T,KAAN;AACnC,UAAMvc,WAAW+Y,QAAO7jB,KAAD;AACvB,UAAI4T,gBAAgB,IAAD,GAAQ;AACvB,YAAMC,SAASC,gBAAuC,MAAa;UAC/DvC,MAAMkC;UACNzP,QAAQ,KAAKoI;UACbpM;UACA4K;QAJ+D,CAArC;AAM9B,YAAI,CAACiJ;AAAQ;AACbjJ,mBAAWiJ,OAAOjJ;MACrB;AACDA,iBAAW,KAAK0c,UAAU1c,UAAUE,QAAzB;AACX,UAAMkM,UAAUpM,aAAaE;AAC7B,UAAIkM,SAAS;AACT6M,QAAAA,QAAO7jB,KAAD,IAAU4K;AAChB,aAAKoe,wBAAwBhpB,OAAO4K,UAAUE,QAA9C;MACH;IACJ,WAAU9K,UAAU6jB,QAAO5jB,QAAQ;AAEhC,WAAKkoB,iBAAiBnoB,OAAO,GAAG,CAAC4K,QAAD,CAAhC;IACH,OAAM;AAEHjK,UAAI,IAAIX,OAAO6jB,QAAO5jB,MAAnB;IACN;EACJ;AA/NL,SAAAinB;AAAA,EAAA;AAkOA,SAAgBjY,sBACZH,eACA7B,UACA7M,MACA6oB,OAAAA;MADA7oB,SAAAA,QAAAA;AAAAA,WAAO,OAAU,qBAAqBwC,UAAS,IAAK;;MACpDqmB,UAAAA,QAAAA;AAAAA,YAAQ;;AAER1mB,gBAAa;AACb,MAAM4I,MAAM,IAAI+b,8BAA8B9mB,MAAM6M,UAAUgc,OAAO,KAAzD;AACZ5kB,qBAAmB8G,IAAImY,SAAStc,OAAOmE,GAArB;AAClB,MAAM0C,QAAQ,IAAIxL,MAAM8I,IAAImY,SAASuD,UAAvB;AACd1b,MAAIiB,SAASyB;AACb,MAAIiB,iBAAiBA,cAAc7O,QAAQ;AACvC,QAAM0S,OAAOZ,uBAAuB,IAAD;AACnC5G,QAAIgd,iBAAiB,GAAG,GAAGrZ,aAA3B;AACAwD,yBAAqBK,IAAD;EACvB;AACD,SAAO9E;AACV;AAGM,IAAImZ,kBAAkB;EACzBkC,OADyB,SAAA,QAAA;AAErB,WAAO,KAAK1L,OAAO,CAAZ;EACV;EAED3B,SALyB,SAAA,QAKjBqM,UALiB;AAMrB,QAAM/c,MAAqC,KAAKnE,KAAL;AAC3C,WAAOmE,IAAIgd,iBAAiB,GAAGhd,IAAImY,QAAQrjB,QAAQioB,QAA5C;EACV;;EAGD1T,QAXyB,SAAA,SAAA;AAYrB,WAAO,KAAKsP,MAAL;EACV;;;;;;;EAQDtG,QArByB,SAAA,OAqBlBxd,OAAewoB,aArBG;sCAqBsBN,WAAAA,IAAAA,MAAAA,OAAAA,IAAAA,OAAAA,IAAAA,CAAAA,GAAAA,OAAAA,GAAAA,OAAAA,MAAAA,QAAAA;AAAAA,eAAAA,OAAAA,CAAAA,IAAAA,UAAAA,IAAAA;;AAC3C,QAAM/c,MAAqC,KAAKnE,KAAL;AAC3C,YAAQ/D,UAAUhD,QAAlB;MACI,KAAK;AACD,eAAO,CAAA;MACX,KAAK;AACD,eAAOkL,IAAIgd,iBAAiBnoB,KAArB;MACX,KAAK;AACD,eAAOmL,IAAIgd,iBAAiBnoB,OAAOwoB,WAA5B;IANf;AAQA,WAAOrd,IAAIgd,iBAAiBnoB,OAAOwoB,aAAaN,QAAzC;EACV;EAEDiB,iBAlCyB,SAAA,gBAkCTnpB,OAAewoB,aAAsBN,UAlC5B;AAmCrB,WAAQ,KAAKlhB,KAAL,EAA8CmhB,iBAClDnoB,OACAwoB,aACAN,QAHI;EAKX;EAED/M,MA1CyB,SAAA,OAAA;AA2CrB,QAAMhQ,MAAqC,KAAKnE,KAAL;uCADvCoiB,QAAAA,IAAAA,MAAAA,KAAAA,GAAAA,QAAAA,GAAAA,QAAAA,OAAAA,SAAAA;AAAAA,YAAAA,KAAAA,IAAAA,UAAAA,KAAAA;;AAEJje,QAAIgd,iBAAiBhd,IAAImY,QAAQrjB,QAAQ,GAAGmpB,KAA5C;AACA,WAAOje,IAAImY,QAAQrjB;EACtB;EAEDilB,KAhDyB,SAAA,MAAA;AAiDrB,WAAO,KAAK1H,OAAOiL,KAAKC,IAAI,KAAK1hB,KAAL,EAAYsc,QAAQrjB,SAAS,GAAG,CAAzC,GAA6C,CAAzD,EAA4D,CAA5D;EACV;EAEDopB,OApDyB,SAAA,QAAA;AAqDrB,WAAO,KAAK7L,OAAO,GAAG,CAAf,EAAkB,CAAlB;EACV;EAED8L,SAxDyB,SAAA,UAAA;AAyDrB,QAAMne,MAAqC,KAAKnE,KAAL;uCADpCoiB,QAAAA,IAAAA,MAAAA,KAAAA,GAAAA,QAAAA,GAAAA,QAAAA,OAAAA,SAAAA;AAAAA,YAAAA,KAAAA,IAAAA,UAAAA,KAAAA;;AAEPje,QAAIgd,iBAAiB,GAAG,GAAGiB,KAA3B;AACA,WAAOje,IAAImY,QAAQrjB;EACtB;EAEDspB,SA9DyB,SAAA,UAAA;AAiErB,QAAI7mB,YAAYgP,oBAAoB;AAChC/Q,UAAI,IAAI,SAAL;IACN;AACD,SAAKkb,QAAQ,KAAKiI,MAAL,EAAayF,QAAb,CAAb;AACA,WAAO;EACV;EAEDC,MAxEyB,SAAA,OAAA;AA2ErB,QAAI9mB,YAAYgP,oBAAoB;AAChC/Q,UAAI,IAAI,MAAL;IACN;AACD,QAAM8oB,OAAO,KAAK3F,MAAL;AACb2F,SAAKD,KAAKzoB,MAAM0oB,MAAMxmB,SAAtB;AACA,SAAK4Y,QAAQ4N,IAAb;AACA,WAAO;EACV;EAEDvF,QApFyB,SAAAA,QAoFlB5gB,OApFkB;AAqFrB,QAAM6H,MAAqC,KAAKnE,KAAL;AAC3C,QAAMuW,MAAMpS,IAAIwc,eAAexc,IAAImY,OAAvB,EAAgCxI,QAAQxX,KAAxC;AACZ,QAAIia,MAAM,IAAI;AACV,WAAKC,OAAOD,KAAK,CAAjB;AACA,aAAO;IACV;AACD,WAAO;EACV;AA5FwB;AAoG7BmM,kBAAkB,UAAUC,UAAX;AACjBD,kBAAkB,QAAQC,UAAT;AACjBD,kBAAkB,YAAYC,UAAb;AACjBD,kBAAkB,WAAWC,UAAZ;AACjBD,kBAAkB,QAAQC,UAAT;AACjBD,kBAAkB,eAAeC,UAAhB;AACjBD,kBAAkB,SAASC,UAAV;AACjBD,kBAAkB,YAAYC,UAAb;AACjBD,kBAAkB,kBAAkBC,UAAnB;AAEjBD,kBAAkB,SAASE,WAAV;AACjBF,kBAAkB,UAAUE,WAAX;AACjBF,kBAAkB,QAAQE,WAAT;AACjBF,kBAAkB,aAAaE,WAAd;AACjBF,kBAAkB,WAAWE,WAAZ;AACjBF,kBAAkB,WAAWE,WAAZ;AACjBF,kBAAkB,OAAOE,WAAR;AACjBF,kBAAkB,QAAQE,WAAT;AAEjBF,kBAAkB,UAAUG,cAAX;AACjBH,kBAAkB,eAAeG,cAAhB;AAEjB,SAASH,kBAAkBI,UAAUC,aAArC;AACI,MAAI,OAAOpgB,MAAM3H,UAAU8nB,QAAhB,MAA8B,YAAY;AACjD9C,oBAAgB8C,QAAD,IAAaC,YAAYD,QAAD;EAC1C;AACJ;AAGD,SAASH,WAAWG,UAApB;AACI,SAAO,WAAA;AACH,QAAM3e,MAAqC,KAAKnE,KAAL;AAC3CmE,QAAIkc,MAAMrf,eAAV;AACA,QAAMgiB,iBAAiB7e,IAAIwc,eAAexc,IAAImY,OAAvB;AACvB,WAAO0G,eAAeF,QAAD,EAAW/oB,MAAMipB,gBAAgB/mB,SAA/C;EACV;AACJ;AAGD,SAAS2mB,YAAYE,UAArB;AACI,SAAO,SAAUG,UAAU3E,SAApB;;AACH,QAAMna,MAAqC,KAAKnE,KAAL;AAC3CmE,QAAIkc,MAAMrf,eAAV;AACA,QAAMgiB,iBAAiB7e,IAAIwc,eAAexc,IAAImY,OAAvB;AACvB,WAAO0G,eAAeF,QAAD,EAAW,SAACI,SAASlqB,OAAV;AAC5B,aAAOiqB,SAAS5kB,KAAKigB,SAAS4E,SAASlqB,OAAO,MAAvC;IACV,CAFM;EAGV;AACJ;AAGD,SAAS6pB,eAAeC,UAAxB;AACI,SAAO,WAAA;;AACH,QAAM3e,MAAqC,KAAKnE,KAAL;AAC3CmE,QAAIkc,MAAMrf,eAAV;AACA,QAAMgiB,iBAAiB7e,IAAIwc,eAAexc,IAAImY,OAAvB;AAEvB,QAAM2G,WAAWhnB,UAAU,CAAD;AAC1BA,cAAU,CAAD,IAAM,SAACknB,aAAaC,cAAcpqB,OAA5B;AACX,aAAOiqB,SAASE,aAAaC,cAAcpqB,OAAO,MAAnC;IAClB;AACD,WAAOgqB,eAAeF,QAAD,EAAW/oB,MAAMipB,gBAAgB/mB,SAA/C;EACV;AACJ;AAED,IAAMonB,kCAAkC/lB,0BACpC,iCACA4iB,6BAF6D;AAKjE,SAAgB3c,kBAAkBjK,OAAAA;AAC9B,SAAOkD,SAASlD,KAAD,KAAW+pB,gCAAgC/pB,MAAM0G,KAAD,CAAN;AAC5D;;;AC1cD,IAAMsjB,sBAAsB,CAAA;AAErB,IAAMC,MAAM;AACZ,IAAMC,SAAS;mBAgOjBpkB,OAAOqkB;sBA8HHrkB,OAAOskB;AArVhB,IAAaxb,gBAAb,WAAA;AAUI,WAAAA,eACIyb,aACOrD,WACApgB,OAHX;QAEWogB,cAAAA,QAAAA;AAAAA,kBAA0B9d;;QAC1BtC,UAAAA,QAAAA;AAAAA,cAAQ,OAAU,mBAAmBtE,UAAS,IAAK;;SADnD0kB,YAAAA;SACApgB,QAAAA;SAXVF,KAAAA,IAASsjB;SACVM,QAAAA;SACAC,UAAAA;SACAC,YAAAA;SACA/X,gBAAAA;SACAC,mBAAAA;SACAE,WAAAA;AAIW,SAAA,YAAAoU;AACA,SAAA,QAAApgB;AAEP,QAAI,CAAC/D,WAAWuB,GAAD,GAAO;AAClB/D,UAAI,EAAD;IACN;AACD,SAAKmqB,YAAYxiB,WAAW,OAAa,KAAKpB,QAAlB,YAAmC,sBAApC;AAC3B,SAAK0jB,QAAQ,oBAAIlmB,IAAJ;AACb,SAAKmmB,UAAU,oBAAInmB,IAAJ;AACf,SAAKqmB,MAAMJ,WAAX;EACH;AAtBL,MAAA,SAAAzb,eAAA;AAAA,SAwBYkV,OAAA,SAAA,KAAKvkB,KAAL;AACJ,WAAO,KAAK+qB,MAAMrH,IAAI1jB,GAAf;EACV;AA1BL,SA4BI0jB,MAAA,SAAAA,KAAI1jB,KAAJ;;AACI,QAAI,CAAC6C,YAAYgP;AAAoB,aAAO,KAAK0S,KAAKvkB,GAAV;AAE5C,QAAImrB,QAAQ,KAAKH,QAAQhe,IAAIhN,GAAjB;AACZ,QAAI,CAACmrB,OAAO;AACR,UAAMC,WAAYD,QAAQ,IAAIpc,gBAC1B,KAAKwV,KAAKvkB,GAAV,GACA8K,mBACA,OAAa,KAAKzD,QAAlB,MAA2BxB,aAAa7F,GAAD,IAAvC,MAAkD,sBAClD,KAJsB;AAM1B,WAAKgrB,QAAQ7gB,IAAInK,KAAKorB,QAAtB;AACAtiB,yBAAmBsiB,UAAU,WAAA;AAAA,eAAM,MAAKJ,QAAL,QAAA,EAAoBhrB,GAApB;MAAN,CAAX;IACrB;AAED,WAAOmrB,MAAMne,IAAN;EACV;AA5CL,SA8CI7C,MAAA,SAAAA,KAAInK,KAAQyD,OAAZ;AACI,QAAM4nB,SAAS,KAAK9G,KAAKvkB,GAAV;AACf,QAAI+T,gBAAgB,IAAD,GAAQ;AACvB,UAAMC,SAASC,gBAAsC,MAAM;QACvDvC,MAAM2Z,SAASzX,SAAS8W;QACxBvmB,QAAQ;QACR4G,UAAUtH;QACVlD,MAAMP;MAJiD,CAA7B;AAM9B,UAAI,CAACgU;AAAQ,eAAO;AACpBvQ,cAAQuQ,OAAOjJ;IAClB;AACD,QAAIsgB,QAAQ;AACR,WAAKC,aAAatrB,KAAKyD,KAAvB;IACH,OAAM;AACH,WAAK8nB,UAAUvrB,KAAKyD,KAApB;IACH;AACD,WAAO;EACV;AAhEL,SAAA,QAAA,IAkEI,SAAA,QAAOzD,KAAP;;AACI8T,wCAAoC,KAAKmX,SAAN;AACnC,QAAIlX,gBAAgB,IAAD,GAAQ;AACvB,UAAMC,SAASC,gBAAsC,MAAM;QACvDvC,MAAMiZ;QACNxmB,QAAQ;QACR5D,MAAMP;MAHiD,CAA7B;AAK9B,UAAI,CAACgU;AAAQ,eAAO;IACvB;AACD,QAAI,KAAKuQ,KAAKvkB,GAAV,GAAgB;AAChB,UAAMgT,YAAY7B,aAAY;AAC9B,UAAM6L,SAAS9I,aAAa,IAAD;AAC3B,UAAMF,UACFgJ,UAAUhK,YACJ;QACIO,gBAAgB;QAChBC,iBAAiB,KAAKnM;QACtBqK,MAAMiZ;QACNxmB,QAAQ;QACR8G,UAAgB,KAAK8f,MAAM/d,IAAIhN,GAAf,EAAqBoT;QACrC7S,MAAMP;MANV,IAQA;AAEV,UAAegT;AAAWvB,uBAAeuC,OAAD;AACxCwR,kBAAY,WAAA;AACR,eAAKyF,UAAU7iB,cAAf;AACA,eAAKojB,mBAAmBxrB,KAAK,KAA7B;AACA,YAAMgK,cAAa,OAAK+gB,MAAM/d,IAAIhN,GAAf;AACnBgK,QAAAA,YAAW6J,aAAa3J,MAAxB;AACA,eAAK6gB,MAAL,QAAA,EAAkB/qB,GAAlB;MACH,CANU;AAOX,UAAIgd;AAAQ7I,wBAAgB,MAAMH,OAAP;AAC3B,UAAehB;AAAWJ,qBAAY;AACtC,aAAO;IACV;AACD,WAAO;EACV;AAxGL,SA0GY4Y,qBAAA,SAAA,mBAAmBxrB,KAAQyD,OAA3B;AACJ,QAAI0nB,QAAQ,KAAKH,QAAQhe,IAAIhN,GAAjB;AACZ,QAAImrB,OAAO;AACPA,YAAMtX,aAAapQ,KAAnB;IACH;EACJ;AA/GL,SAiHY6nB,eAAA,SAAA,aAAatrB,KAAQ+K,UAArB;AACJ,QAAMf,cAAa,KAAK+gB,MAAM/d,IAAIhN,GAAf;AACnB+K,eAAYf,YAAmB0J,iBAAiB3I,QAApC;AACZ,QAAIA,aAAalI,YAAY8Q,WAAW;AACpC,UAAMX,YAAY7B,aAAY;AAC9B,UAAM6L,SAAS9I,aAAa,IAAD;AAC3B,UAAMF,SACFgJ,UAAUhK,YACJ;QACIO,gBAAgB;QAChBC,iBAAiB,KAAKnM;QACtBqK,MAAMkC;QACNzP,QAAQ;QACR8G,UAAWjB,YAAmBoJ;QAC9B7S,MAAMP;QACN+K;MAPJ,IASA;AACV,UAAeiI;AAAWvB,uBAAeuC,MAAD;AACxChK,MAAAA,YAAW6J,aAAa9I,QAAxB;AACA,UAAIiS;AAAQ7I,wBAAgB,MAAMH,MAAP;AAC3B,UAAehB;AAAWJ,qBAAY;IACzC;EACJ;AAxIL,SA0IY2Y,YAAA,SAAA,UAAUvrB,KAAQ+K,UAAlB;;AACJ+I,wCAAoC,KAAKmX,SAAN;AACnCzF,gBAAY,WAAA;AACR,UAAMxb,cAAa,IAAI+E,gBACnBhE,UACA,OAAK0c,WACL,OAAa,OAAKpgB,QAAlB,MAA2BxB,aAAa7F,GAAD,IAAU,qBACjD,KAJe;AAMnB,aAAK+qB,MAAM5gB,IAAInK,KAAKgK,WAApB;AACAe,iBAAYf,YAAmBoJ;AAC/B,aAAKoY,mBAAmBxrB,KAAK,IAA7B;AACA,aAAKirB,UAAU7iB,cAAf;IACH,CAXU;AAYX,QAAM4K,YAAY7B,aAAY;AAC9B,QAAM6L,SAAS9I,aAAa,IAAD;AAC3B,QAAMF,SACFgJ,UAAUhK,YACJ;MACIO,gBAAgB;MAChBC,iBAAiB,KAAKnM;MACtBqK,MAAMgZ;MACNvmB,QAAQ;MACR5D,MAAMP;MACN+K;IANJ,IAQA;AACV,QAAeiI;AAAWvB,qBAAeuC,MAAD;AACxC,QAAIgJ;AAAQ7I,sBAAgB,MAAMH,MAAP;AAC3B,QAAehB;AAAWJ,mBAAY;EACzC;AAxKL,SA0KI5F,MAAA,SAAAA,KAAIhN,KAAJ;AACI,QAAI,KAAK0jB,IAAI1jB,GAAT;AAAe,aAAO,KAAK6nB,cAAc,KAAKkD,MAAM/d,IAAIhN,GAAf,EAAqBgN,IAArB,CAAnB;AAC1B,WAAO,KAAK6a,cAAc3d,MAAnB;EACV;AA7KL,SA+KY2d,gBAAA,SAAA,cAAuCpkB,OAAvC;AACJ,QAAI,KAAK4P,aAAanJ,QAAW;AAC7B,aAAO,KAAKmJ,SAAS5P,KAAd;IACV;AACD,WAAOA;EACV;AApLL,SAsLI0B,OAAA,SAAAA,QAAA;AACI,SAAK8lB,UAAU9iB,eAAf;AACA,WAAO,KAAK4iB,MAAM5lB,KAAX;EACV;AAzLL,SA2LI6e,SAAA,SAAAA,UAAA;AACI,QAAMpiB,QAAO;AACb,QAAMuD,QAAO,KAAKA,KAAL;AACb,WAAOsmB,aAAa;MAChBhJ,MADgB,SAAA,OAAA;yBAEYtd,MAAKsd,KAAL,GAAhBG,OAAAA,WAAAA,MAAMnf,QAAAA,WAAAA;AACd,eAAO;UACHmf;UACAnf,OAAOmf,OAAQ1Y,SAAoBtI,MAAKoL,IAAIvJ,KAAT;QAFhC;MAIV;IAPe,CAAD;EAStB;AAvML,SAyMIygB,UAAA,SAAAA,WAAA;AACI,QAAMtiB,QAAO;AACb,QAAMuD,QAAO,KAAKA,KAAL;AACb,WAAOsmB,aAAa;MAChBhJ,MADgB,SAAA,OAAA;0BAEYtd,MAAKsd,KAAL,GAAhBG,OAAAA,YAAAA,MAAMnf,QAAAA,YAAAA;AACd,eAAO;UACHmf;UACAnf,OAAOmf,OAAQ1Y,SAAqB,CAACzG,OAAO7B,MAAKoL,IAAIvJ,KAAT,CAAR;QAFjC;MAIV;IAPe,CAAD;EAStB;AArNL,SAAA,gBAAA,IAuNI,WAAA;AACI,WAAO,KAAKygB,QAAL;EACV;AAzNL,SA2NI7d,UAAA,SAAA,QAAQ+jB,UAAyD3E,SAAjE;AACI,aAAA,YAAA,gCAA2B,IAA3B,GAAA,OAAA,EAAA,QAAA,UAAA,GAAA,QAAA;AAAA,UAAA,cAAA,MAAA,OAAYzlB,MAAZ,YAAA,CAAA,GAAiByD,QAAjB,YAAA,CAAA;AAAiC2mB,eAAS5kB,KAAKigB,SAAShiB,OAAOzD,KAAK,IAAnC;IAAjC;EACH;AA7NL,SAgOIkrB,QAAA,SAAA,MAAM7qB,OAAN;;AACI,QAAIsK,gBAAgBtK,KAAD,GAAS;AACxBA,cAAQ,IAAIwE,IAAIxE,KAAR;IACX;AACDmlB,gBAAY,WAAA;AACR,UAAI5hB,cAAcvD,KAAD;AACb6E,2BAAmB7E,KAAD,EAAQgG,QAAQ,SAACrG,KAAD;AAAA,iBAC9B,OAAKmK,IAAKnK,KAAkBK,MAAML,GAAD,CAAjC;QAD8B,CAAlC;eAGK8J,MAAMC,QAAQ1J,KAAd;AAAsBA,cAAMgG,QAAQ,SAAA,MAAA;AAAA,cAAErG,MAAF,KAAA,CAAA,GAAOyD,QAAP,KAAA,CAAA;AAAA,iBAAkB,OAAK0G,IAAInK,KAAKyD,KAAd;QAAlB,CAAd;eACtBmB,SAASvE,KAAD,GAAS;AACtB,YAAIA,MAAMC,gBAAgBuE;AAAK/D,cAAI,IAAIT,KAAL;AAClCA,cAAMgG,QAAQ,SAAC5C,OAAOzD,KAAR;AAAA,iBAAgB,OAAKmK,IAAInK,KAAKyD,KAAd;QAAhB,CAAd;MACH,WAAUpD,UAAU,QAAQA,UAAU6J;AAAWpJ,YAAI,IAAIT,KAAL;IACxD,CAVU;AAWX,WAAO;EACV;AAhPL,SAkPIgpB,QAAA,SAAAA,SAAA;;AACI7D,gBAAY,WAAA;AACR1L,gBAAU,WAAA;AACN,iBAAA,aAAA,gCAAkB,OAAK3U,KAAL,CAAlB,GAAA,QAAA,EAAA,SAAA,WAAA,GAAA,QAAA;AAAA,cAAWnF,MAAX,OAAA;AAA+B,iBAAI,QAAA,EAAQA,GAAZ;QAA/B;MACH,CAFQ;IAGZ,CAJU;EAKd;AAxPL,SA0PIgc,UAAA,SAAAA,SAAQgI,SAAR;;AAOIwB,gBAAY,WAAA;AAER,UAAMkG,iBAAiBC,aAAa3H,OAAD;AACnC,UAAM4H,cAAc,oBAAI/mB,IAAJ;AAEpB,UAAIgnB,0BAA0B;AAI9B,eAAA,aAAA,gCAAkB,OAAKd,MAAM5lB,KAAX,CAAlB,GAAA,QAAA,EAAA,SAAA,WAAA,GAAA,QAAqC;AAAA,YAA1BnF,MAA0B,OAAA;AAGjC,YAAI,CAAC0rB,eAAehI,IAAI1jB,GAAnB,GAAyB;AAC1B,cAAM8rB,UAAU,OAAI,QAAA,EAAQ9rB,GAAZ;AAEhB,cAAI8rB,SAAS;AAETD,sCAA0B;UAC7B,OAAM;AAEH,gBAAMpoB,QAAQ,OAAKsnB,MAAM/d,IAAIhN,GAAf;AACd4rB,wBAAYzhB,IAAInK,KAAKyD,KAArB;UACH;QACJ;MACJ;AAED,eAAA,aAAA,gCAA2BioB,eAAexH,QAAf,CAA3B,GAAA,QAAA,EAAA,SAAA,WAAA,GAAA,QAAqD;AAAA,YAAA,eAAA,OAAA,OAAzClkB,OAAyC,aAAA,CAAA,GAApCyD,SAAoC,aAAA,CAAA;AAEjD,YAAMsoB,aAAa,OAAKhB,MAAMrH,IAAI1jB,IAAf;AAEnB,eAAKmK,IAAInK,MAAKyD,MAAd;AAEA,YAAI,OAAKsnB,MAAMrH,IAAI1jB,IAAf,GAAqB;AAIrB,cAAMyD,UAAQ,OAAKsnB,MAAM/d,IAAIhN,IAAf;AACd4rB,sBAAYzhB,IAAInK,MAAKyD,OAArB;AAEA,cAAI,CAACsoB,YAAY;AAEbF,sCAA0B;UAC7B;QACJ;MACJ;AAED,UAAI,CAACA,yBAAyB;AAC1B,YAAI,OAAKd,MAAMxU,SAASqV,YAAYrV,MAAM;AAEtC,iBAAK0U,UAAU7iB,cAAf;QACH,OAAM;AACH,cAAM4jB,QAAQ,OAAKjB,MAAM5lB,KAAX;AACd,cAAM8mB,QAAQL,YAAYzmB,KAAZ;AACd,cAAI+mB,QAAQF,MAAMvJ,KAAN;AACZ,cAAI0J,QAAQF,MAAMxJ,KAAN;AACZ,iBAAO,CAACyJ,MAAMtJ,MAAM;AAChB,gBAAIsJ,MAAMzoB,UAAU0oB,MAAM1oB,OAAO;AAC7B,qBAAKwnB,UAAU7iB,cAAf;AACA;YACH;AACD8jB,oBAAQF,MAAMvJ,KAAN;AACR0J,oBAAQF,MAAMxJ,KAAN;UACX;QACJ;MACJ;AAED,aAAKsI,QAAQa;IAChB,CAnEU;AAoEX,WAAO;EACV;AAtUL,SA6UI3rB,WAAA,SAAAA,YAAA;AACI,WAAO;EACV;AA/UL,SAiVI0U,SAAA,SAAAA,UAAA;AACI,WAAO7K,MAAM0H,KAAK,IAAX;EACV;AAnVL,SA8VI+C,WAAA,SAAA,SAAStM,UAAkDuM,iBAA3D;AACI,QAAeA,oBAAoB;AAC/B1T,UAAI,0EAAD;AACP,WAAO2T,iBAAiB,MAAMxM,QAAP;EAC1B;AAlWL,SAoWImM,aAAA,SAAA,WAAWC,SAAX;AACI,WAAOC,oBAAoB,MAAMD,OAAP;EAC7B;AAtWL,eAAAhF,gBAAA,CAAA;IAAA,KAAA;IAAA,KAAA,SAAArC,OAAA;AAyUQ,WAAKie,UAAU9iB,eAAf;AACA,aAAO,KAAK4iB,MAAMxU;IACrB;EA3UL,GAAA;IAAA,KAAA;IAAA,KAAA,SAAAvJ,OAAA;AAsVQ,aAAO;IACV;EAvVL,CAAA,CAAA;AAAA,SAAAqC;AAAA,EAAA;AA0WA,IAAW1E,kBAAkBlG,0BAA0B,iBAAiB4K,aAAlB;AAItD,SAASsc,aAAanrB,eAAtB;AACI,MAAIoE,SAASpE,aAAD,KAAmBmK,gBAAgBnK,aAAD,GAAiB;AAC3D,WAAOA;EACV,WAAUsJ,MAAMC,QAAQvJ,aAAd,GAA8B;AACrC,WAAO,IAAIqE,IAAIrE,aAAR;EACV,WAAUoD,cAAcpD,aAAD,GAAiB;AACrC,QAAMY,OAAM,oBAAIyD,IAAJ;AACZ,aAAW7E,OAAOQ,eAAe;AAC7BY,MAAAA,KAAI+I,IAAInK,KAAKQ,cAAcR,GAAD,CAA1B;IACH;AACD,WAAOoB;EACV,OAAM;AACH,WAAON,IAAI,IAAIN,aAAL;EACb;AACJ;;;AClbD,IAAM4rB,sBAAsB,CAAA;qBAsOvB7lB,OAAOqkB;wBAIHrkB,OAAOskB;AA1MhB,IAAavb,gBAAb,WAAA;AASI,WAAAA,eACIwb,aACA1d,UACO/F,OAHX;QAEI+F,aAAAA,QAAAA;AAAAA,iBAAyBzD;;QAClBtC,UAAAA,QAAAA;AAAAA,cAAQ,OAAU,mBAAmBtE,UAAS,IAAK;;SAAnDsE,QAAAA;SAXVF,KAAAA,IAASilB;SACFrB,QAAkB,oBAAIhmB,IAAJ;SAClByiB,QAAAA;SACRrU,mBAAAA;SACAD,gBAAAA;SACAG,WAAAA;SACAoU,YAAAA;AAKW,SAAA,QAAApgB;AAEP,QAAI,CAAC/D,WAAWyB,GAAD,GAAO;AAClBjE,UAAI,EAAD;IACN;AACD,SAAK0mB,QAAQ/e,WAAW,KAAKpB,KAAN;AACvB,SAAKogB,YAAY,SAACE,MAAMC,MAAP;AAAA,aAAgBxa,SAASua,MAAMC,MAAMvgB,KAAb;IAAxB;AACjB,QAAIyjB,aAAa;AACb,WAAK9O,QAAQ8O,WAAb;IACH;EACJ;AAtBL,MAAA,SAAAxb,eAAA;AAAA,SAwBYuY,gBAAA,SAAA,cAAuCpkB,OAAvC;AACJ,QAAI,KAAK4P,aAAanJ,QAAW;AAC7B,aAAO,KAAKmJ,SAAS5P,KAAd;IACV;AACD,WAAOA;EACV;AA7BL,SA+BI4lB,QAAA,SAAAA,SAAA;;AACI7D,gBAAY,WAAA;AACR1L,gBAAU,WAAA;AACN,iBAAA,YAAA,gCAAoB,MAAKiR,MAAM/G,OAAX,CAApB,GAAA,OAAA,EAAA,QAAA,UAAA,GAAA,QAAA;AAAA,cAAWvgB,QAAX,MAAA;AAAyC,gBAAI,QAAA,EAAQA,KAAZ;QAAzC;MACH,CAFQ;IAGZ,CAJU;EAKd;AArCL,SAuCI4C,UAAA,SAAA,QAAQgmB,YAAwD5G,SAAhE;AACI,aAAA,aAAA,gCAAoB,IAApB,GAAA,QAAA,EAAA,SAAA,WAAA,GAAA,QAA0B;AAAA,UAAfhiB,QAAe,OAAA;AACtB4oB,iBAAW7mB,KAAKigB,SAAShiB,OAAOA,OAAO,IAAvC;IACH;EACJ;AA3CL,SAkDI2X,MAAA,SAAA,IAAI3X,OAAJ;;AACIqQ,wCAAoC,KAAK0T,KAAN;AACnC,QAAIzT,gBAAgB,IAAD,GAAQ;AACvB,UAAMC,SAASC,gBAAmC,MAAM;QACpDvC,MAAMgZ;QACNvmB,QAAQ;QACR4G,UAAUtH;MAH0C,CAA1B;AAK9B,UAAI,CAACuQ;AAAQ,eAAO;IAGvB;AACD,QAAI,CAAC,KAAK0P,IAAIjgB,KAAT,GAAiB;AAClB+hB,kBAAY,WAAA;AACR,eAAKuF,MAAM3P,IAAI,OAAKqM,UAAUhkB,OAAOyG,MAAtB,CAAf;AACA,eAAKsd,MAAMpf,cAAX;MACH,CAHU;AAIX,UAAM4K,YAAuB7B,aAAY;AACzC,UAAM6L,SAAS9I,aAAa,IAAD;AAC3B,UAAMF,UACFgJ,UAAUhK,YACc;QACdO,gBAAgB;QAChBC,iBAAiB,KAAKnM;QACtBqK,MAAMgZ;QACNvmB,QAAQ;QACR4G,UAAUtH;MALI,IAOlB;AACV,UAAIuP,aAAS;AAAavB,uBAAeuC,OAAD;AACxC,UAAIgJ;AAAQ7I,wBAAgB,MAAMH,OAAP;AAC3B,UAAIhB,aAAS;AAAaJ,qBAAY;IACzC;AAED,WAAO;EACV;AArFL,SAAA,QAAA,IAuFI,SAAA,QAAOnP,OAAP;;AACI,QAAIsQ,gBAAgB,IAAD,GAAQ;AACvB,UAAMC,SAASC,gBAAmC,MAAM;QACpDvC,MAAMiZ;QACNxmB,QAAQ;QACR8G,UAAUxH;MAH0C,CAA1B;AAK9B,UAAI,CAACuQ;AAAQ,eAAO;IACvB;AACD,QAAI,KAAK0P,IAAIjgB,KAAT,GAAiB;AACjB,UAAMuP,YAAuB7B,aAAY;AACzC,UAAM6L,SAAS9I,aAAa,IAAD;AAC3B,UAAMF,WACFgJ,UAAUhK,YACc;QACdO,gBAAgB;QAChBC,iBAAiB,KAAKnM;QACtBqK,MAAMiZ;QACNxmB,QAAQ;QACR8G,UAAUxH;MALI,IAOlB;AAEV,UAAIuP,aAAS;AAAavB,uBAAeuC,QAAD;AACxCwR,kBAAY,WAAA;AACR,eAAKgC,MAAMpf,cAAX;AACA,eAAK2iB,MAAL,QAAA,EAAkBtnB,KAAlB;MACH,CAHU;AAIX,UAAIuZ;AAAQ7I,wBAAgB,MAAMH,QAAP;AAC3B,UAAIhB,aAAS;AAAaJ,qBAAY;AACtC,aAAO;IACV;AACD,WAAO;EACV;AAxHL,SA0HI8Q,MAAA,SAAAA,KAAIjgB,OAAJ;AACI,SAAK+jB,MAAMrf,eAAX;AACA,WAAO,KAAK4iB,MAAMrH,IAAI,KAAKmE,cAAcpkB,KAAnB,CAAf;EACV;AA7HL,SA+HIygB,UAAA,SAAAA,WAAA;AACI,QAAIoI,YAAY;AAChB,QAAMnnB,QAAO2E,MAAM0H,KAAK,KAAKrM,KAAL,CAAX;AACb,QAAM6e,UAASla,MAAM0H,KAAK,KAAKwS,OAAL,CAAX;AACf,WAAOyH,aAAqB;MACxBhJ,MADwB,SAAA,OAAA;AAEpB,YAAMtiB,QAAQmsB;AACdA,qBAAa;AACb,eAAOnsB,QAAQ6jB,QAAO5jB,SAChB;UAAEqD,OAAO,CAAC0B,MAAKhF,KAAD,GAAS6jB,QAAO7jB,KAAD,CAApB;UAA8ByiB,MAAM;QAA7C,IACA;UAAEA,MAAM;QAAR;MACT;IAPuB,CAAT;EAStB;AA5IL,SA8IIzd,OAAA,SAAAA,QAAA;AACI,WAAO,KAAK6e,OAAL;EACV;AAhJL,SAkJIA,SAAA,SAAAA,UAAA;AACI,SAAKwD,MAAMrf,eAAX;AACA,QAAMvG,QAAO;AACb,QAAI0qB,YAAY;AAChB,QAAMC,mBAAmBziB,MAAM0H,KAAK,KAAKuZ,MAAM/G,OAAX,CAAX;AACzB,WAAOyH,aAAgB;MACnBhJ,MADmB,SAAA,OAAA;AAEf,eAAO6J,YAAYC,iBAAiBnsB,SAC9B;UAAEqD,OAAO7B,MAAKimB,cAAc0E,iBAAiBD,WAAD,CAAnC;UAAmD1J,MAAM;QAAlE,IACA;UAAEA,MAAM;QAAR;MACT;IALkB,CAAJ;EAOtB;AA9JL,SAgKI5G,UAAA,SAAAA,SAAQ3b,OAAR;;AACI,QAAIuK,gBAAgBvK,KAAD,GAAS;AACxBA,cAAQ,IAAI0E,IAAI1E,KAAR;IACX;AAEDmlB,gBAAY,WAAA;AACR,UAAI1b,MAAMC,QAAQ1J,KAAd,GAAsB;AACtB,eAAKgpB,MAAL;AACAhpB,cAAMgG,QAAQ,SAAA5C,OAAK;AAAA,iBAAI,OAAK2X,IAAI3X,KAAT;QAAJ,CAAnB;MACH,WAAUqB,SAASzE,KAAD,GAAS;AACxB,eAAKgpB,MAAL;AACAhpB,cAAMgG,QAAQ,SAAA5C,OAAK;AAAA,iBAAI,OAAK2X,IAAI3X,KAAT;QAAJ,CAAnB;MACH,WAAUpD,UAAU,QAAQA,UAAU6J,QAAW;AAC9CpJ,YAAI,gCAAgCT,KAAjC;MACN;IACJ,CAVU;AAYX,WAAO;EACV;AAlLL,SAmLIkU,WAAA,SAAA,SAAStM,UAA+CuM,iBAAxD;AAEI,QAAeA,oBAAoB;AAC/B1T,UAAI,0EAAD;AACP,WAAO2T,iBAAiB,MAAMxM,QAAP;EAC1B;AAxLL,SA0LImM,aAAA,SAAA,WAAWC,SAAX;AACI,WAAOC,oBAAoB,MAAMD,OAAP;EAC7B;AA5LL,SA8LIM,SAAA,SAAAA,UAAA;AACI,WAAO7K,MAAM0H,KAAK,IAAX;EACV;AAhML,SAkMIvR,WAAA,SAAAA,YAAA;AACI,WAAO;EACV;AApML,SAAA,kBAAA,IAsMI,WAAA;AACI,WAAO,KAAK+jB,OAAL;EACV;AAxML,eAAA1U,gBAAA,CAAA;IAAA,KAAA;IAAA,KAAA,SAAAtC,OAAA;AA8CQ,WAAKwa,MAAMrf,eAAX;AACA,aAAO,KAAK4iB,MAAMxU;IACrB;EAhDL,GAAA;IAAA,KAAA;IAAA,KAAA,SAAAvJ,OAAA;AA2MQ,aAAO;IACV;EA5ML,CAAA,CAAA;AAAA,SAAAsC;AAAA,EAAA;AAgNA,IAAW1E,kBAAkBnG,0BAA0B,iBAAiB6K,aAAlB;AC7NtD,IAAMkd,kBAAkB1qB,uBAAO4f,OAAO,IAAd;AAoCxB,IAAM+K,SAAS;AAEf,IAAaC,iCAAb,WAAA;AAUI,WAAAA,gCACW1gB,SACAyX,SACApc,OAEAslB,oBALX;QAEWlJ,YAAAA,QAAAA;AAAAA,gBAAU,oBAAI5e,IAAJ;;QAGV8nB,uBAAAA,QAAAA;AAAAA,2BAAiCrf;;SAJjCtB,UAAAA;SACAyX,UAAAA;SACApc,QAAAA;SAEAslB,qBAAAA;SAbX1B,YAAAA;SACA9X,mBAAAA;SACAD,gBAAAA;SACA3G,SAAAA;SACAhB,iBAAAA;SACAC,sBAAAA;SACQohB,eAAAA;AAGG,SAAA,UAAA5gB;AACA,SAAA,UAAAyX;AACA,SAAA,QAAApc;AAEA,SAAA,qBAAAslB;AAEP,SAAK1B,YAAY,IAAI7jB,KAAK,OAAa,KAAKC,QAAlB,UAAiC,uBAA1C;AAEjB,SAAKkE,iBAAiB3H,cAAc,KAAKoI,OAAN;AACnC,QAAe,CAAC6gB,aAAa,KAAKF,kBAAN,GAA2B;AACnD7rB,UAAG,4CAAA;IACN;AACD,QAAA,MAAa;AAET,WAAK0K,sBAAsB,CAAA;IAC9B;EACJ;AA3BL,MAAA,SAAAkhB,gCAAA;AAAA,SA6BII,0BAAA,SAAA,wBAAwB9sB,KAAxB;AACI,WAAO,KAAKyjB,QAAQzW,IAAIhN,GAAjB,EAAuBgN,IAAvB;EACV;AA/BL,SAiCI+f,0BAAA,SAAA,wBAAwB/sB,KAAkB+K,UAA1C;AACI,QAAMf,cAAa,KAAKyZ,QAAQzW,IAAIhN,GAAjB;AACnB,QAAIgK,uBAAsBmG,eAAe;AACrCnG,MAAAA,YAAWG,IAAIY,QAAf;AACA,aAAO;IACV;AAGD,QAAIgJ,gBAAgB,IAAD,GAAQ;AACvB,UAAMC,SAASC,gBAAmC,MAAM;QACpDvC,MAAMkC;QACNzP,QAAQ,KAAKoI,UAAU,KAAKP;QAC5BzL,MAAMP;QACN+K;MAJoD,CAA1B;AAM9B,UAAI,CAACiJ;AAAQ,eAAO;AACpBjJ,iBAAYiJ,OAAejJ;IAC9B;AACDA,eAAYf,YAAmB0J,iBAAiB3I,QAApC;AAGZ,QAAIA,aAAalI,YAAY8Q,WAAW;AACpC,UAAMqJ,SAAS9I,aAAa,IAAD;AAC3B,UAAMlB,YAAuB7B,aAAY;AACzC,UAAM6C,UACFgJ,UAAUhK,YACJ;QACItB,MAAMkC;QACNL,gBAAgB;QAChBC,iBAAiB,KAAKnM;QACtBlD,QAAQ,KAAKoI,UAAU,KAAKP;QAC5Bf,UAAWjB,YAAmBoJ;QAC9B7S,MAAMP;QACN+K;MAPJ,IASA;AAEV,UAAeiI;AAAWvB,uBAAeuC,OAAD;AACtChK,MAAAA,YAAoC6J,aAAa9I,QAAjD;AACF,UAAIiS;AAAQ7I,wBAAgB,MAAMH,OAAP;AAC3B,UAAehB;AAAWJ,qBAAY;IACzC;AACD,WAAO;EACV;AA5EL,SA8EI4R,OAAA,SAAA,KAAKxkB,KAAL;AACI,QAAI6C,YAAYgP,sBAAsB,CAAC9L,QAAQ,KAAKiG,SAAShM,GAAf,GAAqB;AAE/D,WAAKukB,KAAKvkB,GAAV;IACH;AACD,WAAO,KAAKgM,QAAQhM,GAAb;EACV;AApFL,SA6FImkB,OAAA,SAAA,KAAKnkB,KAAkByD,OAAYiI,WAAnC;QAAmCA,cAAAA,QAAAA;AAAAA,kBAAqB;;AAEpD,QAAI3F,QAAQ,KAAKiG,SAAShM,GAAf,GAAqB;AAE5B,UAAI,KAAKyjB,QAAQC,IAAI1jB,GAAjB,GAAuB;AAEvB,eAAO,KAAK+sB,wBAAwB/sB,KAAKyD,KAAlC;MACV,WAAUiI,WAAW;AAElB,eAAOhG,QAAQyE,IAAI,KAAK6B,SAAShM,KAAKyD,KAA/B;MACV,OAAM;AAEH,aAAKuI,QAAQhM,GAAb,IAAoByD;AACpB,eAAO;MACV;IACJ,OAAM;AAEH,aAAO,KAAK4H,QACRrL,KACA;QAAEyD;QAAOY,YAAY;QAAMC,UAAU;QAAMC,cAAc;MAAzD,GACA,KAAKooB,oBACLjhB,SAJG;IAMV;EACJ;AArHL,SAwHI6Y,OAAA,SAAA,KAAKvkB,KAAL;AACI,QAAI,CAAC6C,YAAYgP,oBAAoB;AAEjC,aAAO7R,OAAO,KAAKgM;IACtB;AACD,SAAK4gB,iBAAL,KAAKA,eAAiB,oBAAI/nB,IAAJ;AACtB,QAAIsmB,QAAQ,KAAKyB,aAAa5f,IAAIhN,GAAtB;AACZ,QAAI,CAACmrB,OAAO;AACRA,cAAQ,IAAIpc,gBACR/O,OAAO,KAAKgM,SACZlB,mBACA,OAAa,KAAKzD,QAAlB,MAA2BxB,aAAa7F,GAAD,IAAvC,MAAkD,yBAClD,KAJI;AAMR,WAAK4sB,aAAaziB,IAAInK,KAAKmrB,KAA3B;IACH;AACD,WAAOA,MAAMne,IAAN;EACV;AAzIL,SA+II5B,QAAA,SAAAA,OAAMpL,KAAkByG,YAAxB;AACI,QAAIA,eAAe,MAAM;AACrBA,mBAAa,KAAKkmB;IACrB;AACD,QAAIlmB,eAAe,OAAO;AACtB;IACH;AACDumB,oBAAgB,MAAMvmB,YAAYzG,GAAnB;AACf,QAAI,EAAEA,OAAO,KAAKgM,UAAU;AAAA,UAAA;AAMxB,WAAA,wBAAI,KAAKA,QAAQ1F,uBAAb,MAAJ,OAAA,SAAI,sBAAwCtG,GAAxC,GAA8C;AAC9C;MACH,OAAM;AACHc,YAAI,GAAG2F,WAAWO,iBAAoB,KAAKK,QAAxC,MAAiDrH,IAAIC,SAAJ,CAAjD;MACN;IACJ;AACD,QAAI6L,SAAS,KAAKE;AAClB,WAAOF,UAAUA,WAAW5J,iBAAiB;AACzC,UAAMuJ,aAAa1J,cAAc+J,QAAQ9L,GAAT;AAChC,UAAIyL,YAAY;AACZ,YAAMwhB,UAAUxmB,WAAW2E,MAAM,MAAMpL,KAAKyL,YAAYK,MAAxC;AAChB,YAAImhB,YAAO;AAAwB;AACnC,YAAIA,YAAO;AAAuB;MACrC;AACDnhB,eAAShK,OAAOgC,eAAegI,MAAtB;IACZ;AACDohB,4BAAwB,MAAMzmB,YAAYzG,GAAnB;EAC1B;AA9KL,SAuLIqL,UAAA,SAAAA,SACIrL,KACAyL,YACAhF,YACAiF,WAJJ;QAIIA,cAAAA,QAAAA;AAAAA,kBAAqB;;AAErB,QAAIjF,eAAe,MAAM;AACrBA,mBAAa,KAAKkmB;IACrB;AACD,QAAIlmB,eAAe,OAAO;AACtB,aAAO,KAAK0F,gBAAgBnM,KAAKyL,YAAYC,SAAtC;IACV;AACDshB,oBAAgB,MAAMvmB,YAAYzG,GAAnB;AACf,QAAMitB,UAAUxmB,WAAW4E,QAAQ,MAAMrL,KAAKyL,YAAYC,SAA1C;AAChB,QAAIuhB,SAAS;AACTC,8BAAwB,MAAMzmB,YAAYzG,GAAnB;IAC1B;AACD,WAAOitB;EACV;AAzML,SAiNI9gB,kBAAA,SAAA,gBACInM,KACAyL,YACAC,WAHJ;QAGIA,cAAAA,QAAAA;AAAAA,kBAAqB;;AAErB,QAAI;AACArD,iBAAU;AAGV,UAAM8kB,gBAAgB,KAAK7I,QAAQtkB,GAAb;AACtB,UAAI,CAACmtB,eAAe;AAEhB,eAAOA;MACV;AAGD,UAAIpZ,gBAAgB,IAAD,GAAQ;AACvB,YAAMC,SAASC,gBAAmC,MAAM;UACpD9P,QAAQ,KAAKoI,UAAU,KAAKP;UAC5BzL,MAAMP;UACN0R,MAAMgZ;UACN3f,UAAUU,WAAWhI;QAJ+B,CAA1B;AAM9B,YAAI,CAACuQ;AAAQ,iBAAO;AAPG,YAQfjJ,WAAaiJ,OAAbjJ;AACR,YAAIU,WAAWhI,UAAUsH,UAAU;AAC/BU,uBAAU,SAAA,CAAA,GACHA,YADG;YAENhI,OAAOsH;UAFD,CAAA;QAIb;MACJ;AAGD,UAAIW,WAAW;AACX,YAAI,CAAChG,QAAQzD,eAAe,KAAK+J,SAAShM,KAAKyL,UAA1C,GAAuD;AACxD,iBAAO;QACV;MACJ,OAAM;AACHxJ,uBAAe,KAAK+J,SAAShM,KAAKyL,UAApB;MACjB;AAGD,WAAK2hB,wBAAwBptB,KAAKyL,WAAWhI,KAA7C;IACH,UAvCD;AAwCI8E,eAAQ;IACX;AACD,WAAO;EACV;AAjQL,SAoQI4E,4BAAA,SAAA,0BACInN,KACAyD,OACA2J,UACA1B,WAJJ;QAIIA,cAAAA,QAAAA;AAAAA,kBAAqB;;AAErB,QAAI;AACArD,iBAAU;AAGV,UAAM8kB,gBAAgB,KAAK7I,QAAQtkB,GAAb;AACtB,UAAI,CAACmtB,eAAe;AAEhB,eAAOA;MACV;AAGD,UAAIpZ,gBAAgB,IAAD,GAAQ;AACvB,YAAMC,SAASC,gBAAmC,MAAM;UACpD9P,QAAQ,KAAKoI,UAAU,KAAKP;UAC5BzL,MAAMP;UACN0R,MAAMgZ;UACN3f,UAAUtH;QAJ0C,CAA1B;AAM9B,YAAI,CAACuQ;AAAQ,iBAAO;AACpBvQ,gBAASuQ,OAAejJ;MAC3B;AAED,UAAMsiB,mBAAmBC,kCAAkCttB,GAAD;AAC1D,UAAMyL,aAAa;QACflH,cAAc1B,YAAYwJ,kBAAkB,KAAKd,iBAAiB;QAClElH,YAAY;QACZ2I,KAAKqgB,iBAAiBrgB;QACtB7C,KAAKkjB,iBAAiBljB;MAJP;AAQnB,UAAIuB,WAAW;AACX,YAAI,CAAChG,QAAQzD,eAAe,KAAK+J,SAAShM,KAAKyL,UAA1C,GAAuD;AACxD,iBAAO;QACV;MACJ,OAAM;AACHxJ,uBAAe,KAAK+J,SAAShM,KAAKyL,UAApB;MACjB;AAED,UAAMzB,cAAa,IAAI+E,gBACnBtL,OACA2J,UACA,OAAa,KAAK/F,QAAlB,MAA2BrH,IAAIC,SAAJ,IAAmB,wBAC9C,KAJe;AAOnB,WAAKwjB,QAAQtZ,IAAInK,KAAKgK,WAAtB;AAGA,WAAKojB,wBAAwBptB,KAAKgK,YAAWoJ,MAA7C;IACH,UAlDD;AAmDI7K,eAAQ;IACX;AACD,WAAO;EACV;AAhUL,SAmUIwE,0BAAA,SAAA,wBACI/M,KACA4L,SACAF,WAHJ;QAGIA,cAAAA,QAAAA;AAAAA,kBAAqB;;AAErB,QAAI;AACArD,iBAAU;AAGV,UAAM8kB,gBAAgB,KAAK7I,QAAQtkB,GAAb;AACtB,UAAI,CAACmtB,eAAe;AAEhB,eAAOA;MACV;AAGD,UAAIpZ,gBAAgB,IAAD,GAAQ;AACvB,YAAMC,SAASC,gBAAmC,MAAM;UACpD9P,QAAQ,KAAKoI,UAAU,KAAKP;UAC5BzL,MAAMP;UACN0R,MAAMgZ;UACN3f,UAAUb;QAJ0C,CAA1B;AAM9B,YAAI,CAAC8J;AAAQ,iBAAO;MACvB;AACDpI,cAAQrL,SAARqL,QAAQrL,OAAS,OAAa,KAAK8G,QAAlB,MAA2BrH,IAAIC,SAAJ,IAAmB;AAC/D2L,cAAQqK,UAAU,KAAK1J,UAAU,KAAKP;AACtC,UAAMqhB,mBAAmBC,kCAAkCttB,GAAD;AAC1D,UAAMyL,aAAa;QACflH,cAAc1B,YAAYwJ,kBAAkB,KAAKd,iBAAiB;QAClElH,YAAY;QACZ2I,KAAKqgB,iBAAiBrgB;QACtB7C,KAAKkjB,iBAAiBljB;MAJP;AAQnB,UAAIuB,WAAW;AACX,YAAI,CAAChG,QAAQzD,eAAe,KAAK+J,SAAShM,KAAKyL,UAA1C,GAAuD;AACxD,iBAAO;QACV;MACJ,OAAM;AACHxJ,uBAAe,KAAK+J,SAAShM,KAAKyL,UAApB;MACjB;AAED,WAAKgY,QAAQtZ,IAAInK,KAAK,IAAImQ,cAAcvE,OAAlB,CAAtB;AAGA,WAAKwhB,wBAAwBptB,KAAKkK,MAAlC;IACH,UA3CD;AA4CI3B,eAAQ;IACX;AACD,WAAO;EACV;AAvXL,SA+XI+b,UAAA,SAAA,QAAQtkB,KAAkB0L,WAA1B;QAA0BA,cAAAA,QAAAA;AAAAA,kBAAqB;;AAE3C,QAAI,CAAC3F,QAAQ,KAAKiG,SAAShM,GAAf,GAAqB;AAC7B,aAAO;IACV;AAGD,QAAI+T,gBAAgB,IAAD,GAAQ;AACvB,UAAMC,SAASC,gBAAmC,MAAM;QACpD9P,QAAQ,KAAKoI,UAAU,KAAKP;QAC5BzL,MAAMP;QACN0R,MAAM+a;MAH8C,CAA1B;AAM9B,UAAI,CAACzY;AAAQ,eAAO;IACvB;AAGD,QAAI;AAAA,UAAA,oBAAA;AACA3L,iBAAU;AACV,UAAM2U,SAAS9I,aAAa,IAAD;AAC3B,UAAMlB,YAAuB7B,aAAY;AACzC,UAAMnH,cAAa,KAAKyZ,QAAQzW,IAAIhN,GAAjB;AAEnB,UAAIyD,QAAQyG;AAEZ,UAAI,CAACF,gBAAegT,UAAUhK,YAAY;AAAA,YAAAua;AACtC9pB,iBAAK8pB,kBAAGxrB,cAAc,KAAKiK,SAAShM,GAAf,MAAhB,OAAA,SAAGutB,gBAAkC9pB;MAC7C;AAED,UAAIiI,WAAW;AACX,YAAI,CAAChG,QAAQ2gB,eAAe,KAAKra,SAAShM,GAArC,GAA2C;AAC5C,iBAAO;QACV;MACJ,OAAM;AACH,eAAO,KAAKgM,QAAQhM,GAAb;MACV;AAED,UAAA,MAAa;AACT,eAAO,KAAKwL,oBAAqBxL,GAA1B;MACV;AAED,UAAIgK,aAAY;AACZ,aAAKyZ,QAAL,QAAA,EAAoBzjB,GAApB;AAEA,YAAIgK,uBAAsB+E,iBAAiB;AACvCtL,kBAAQuG,YAAWoJ;QACtB;AAED9K,yBAAiB0B,WAAD;MACnB;AAED,WAAKihB,UAAU7iB,cAAf;AAIA,OAAA,qBAAA,KAAKwkB,iBAAL,OAAA,UAAA,wBAAA,mBAAmB5f,IAAIhN,GAAvB,MAAA,OAAA,SAAA,sBAA6BmK,IAAInK,OAAO,KAAKgM,OAA7C;AAGA,UAAIgR,UAAUhK,WAAW;AACrB,YAAMgB,WAA2B;UAC7BtC,MAAM+a;UACNlZ,gBAAgB;UAChBpP,QAAQ,KAAKoI,UAAU,KAAKP;UAC5BwH,iBAAiB,KAAKnM;UACtB4D,UAAUxH;UACVlD,MAAMP;QANuB;AAQjC,YAAegT;AAAWvB,yBAAeuC,QAAD;AACxC,YAAIgJ;AAAQ7I,0BAAgB,MAAMH,QAAP;AAC3B,YAAehB;AAAWJ,uBAAY;MACzC;IACJ,UAtDD;AAuDIrK,eAAQ;IACX;AACD,WAAO;EACV;AA3cL,SAkdIgM,WAAA,SAAA,SAAS6V,UAA+C5V,iBAAxD;AACI,QAAeA,oBAAoB;AAC/B1T,UAAI,iFAAD;AACP,WAAO2T,iBAAiB,MAAM2V,QAAP;EAC1B;AAtdL,SAwdIhW,aAAA,SAAA,WAAWC,SAAX;AACI,WAAOC,oBAAoB,MAAMD,OAAP;EAC7B;AA1dL,SA4dI+Y,0BAAA,SAAA,wBAAwBptB,KAAkByD,OAA1C;;AACI,QAAMuZ,SAAS9I,aAAa,IAAD;AAC3B,QAAMlB,YAAuB7B,aAAY;AACzC,QAAI6L,UAAUhK,WAAW;AACrB,UAAMgB,SACFgJ,UAAUhK,YACH;QACGtB,MAAMgZ;QACNnX,gBAAgB;QAChBC,iBAAiB,KAAKnM;QACtBlD,QAAQ,KAAKoI,UAAU,KAAKP;QAC5BzL,MAAMP;QACN+K,UAAUtH;MANb,IAQD;AAEV,UAAeuP;AAAWvB,uBAAeuC,MAAD;AACxC,UAAIgJ;AAAQ7I,wBAAgB,MAAMH,MAAP;AAC3B,UAAehB;AAAWJ,qBAAY;IACzC;AAED,KAAA,sBAAA,KAAKga,iBAAL,OAAA,UAAA,wBAAA,oBAAmB5f,IAAIhN,GAAvB,MAAA,OAAA,SAAA,sBAA6BmK,IAAI,IAAjC;AAGA,SAAK8gB,UAAU7iB,cAAf;EACH;AArfL,SAufIuc,WAAA,SAAA,WAAA;AACI,SAAKsG,UAAU9iB,eAAf;AACA,WAAO1C,QAAQ,KAAKuG,OAAN;EACjB;AA1fL,SA4fI+X,QAAA,SAAA,QAAA;AAOI,SAAKkH,UAAU9iB,eAAf;AACA,WAAOrG,OAAOqD,KAAK,KAAK6G,OAAjB;EACV;AArgBL,SAAA0gB;AAAA,EAAA;AA4gBA,SAAgBhd,mBACZ1J,QACA4F,SAAAA;;AAEA,MAAeA,WAAWnB,mBAAmBzE,MAAD,GAAU;AAClDlF,QAAG,2DAAA;EACN;AAED,MAAIiF,QAAQC,QAAQmB,KAAT,GAAiB;AACxB,QAAe,EAAEic,kBAAkBpd,MAAD,aAAoB0mB,iCAAiC;AACnF5rB,UACI,qBAAmB0sB,aAAaxnB,MAAD,IAA/B,sHADD;IAKN;AACD,WAAOA;EACV;AAED,MAAe,CAAClE,OAAO2rB,aAAaznB,MAApB;AACZlF,QAAI,oEAAD;AAEP,MAAMP,QAAI,gBACNqL,WADM,OAAA,SACNA,QAASrL,SADH,OAAA,gBAEL,QAESqD,cAAcoC,MAAD,IAAW,qBAAqBA,OAAO1F,YAAYC,QAFzE,MAGSwC,UAAS,IACb;AAEV,MAAMuI,MAAM,IAAIohB,+BACZ1mB,QACA,oBAAInB,IAAJ,GACAxD,OAAOd,IAAD,GACNiO,yBAAyB5C,OAAD,CAJhB;AAOZ1H,gBAAc8B,QAAQmB,OAAOmE,GAAhB;AAEb,SAAOtF;AACV;AAED,IAAM0nB,mCAAmCjpB,0BACrC,kCACAioB,8BAF8D;AAKlE,SAASY,kCAAkCttB,KAA3C;AACI,SACIwsB,gBAAgBxsB,GAAD,MACdwsB,gBAAgBxsB,GAAD,IAAQ;IACpBgN,KADoB,SAAAA,OAAA;AAEhB,aAAO,KAAK7F,KAAL,EAAY2lB,wBAAwB9sB,GAApC;IACV;IACDmK,KAJoB,SAAAA,KAIhB1G,OAJgB;AAKhB,aAAO,KAAK0D,KAAL,EAAY4lB,wBAAwB/sB,KAAKyD,KAAzC;IACV;EANmB;AAS/B;AAED,SAAgBgH,mBAAmBhK,OAAAA;AAC/B,MAAIkD,SAASlD,KAAD,GAAS;AACjB,WAAOitB,iCAAkCjtB,MAAc0G,KAAD,CAAf;EAC1C;AACD,SAAO;AACV;AAED,SAAgB+lB,wBACZ5hB,KACA7E,YACAzG,KAAAA;;AAEA,MAAA,MAAa;AACTsL,QAAIE,oBAAqBxL,GAAzB,IAAgCyG;EACnC;AAED,GAAA,wBAAO6E,IAAIU,QAAQ1F,uBAAZ,MAAP,OAAA,OAAA,OAAO,sBAAuCtG,GAAvC;AACV;AAED,SAASgtB,gBACL1hB,KACA7E,YACAzG,KAHJ;AAMI,MAAe,CAAC6sB,aAAapmB,UAAD,GAAc;AACtC3F,QAAG,sBAAqBwK,IAAIjE,QAAzB,MAAkCrH,IAAIC,SAAJ,IAAlC,wBAAA;EACN;AAkCD,MAAe,CAAC2G,WAAWH,UAAD,KAAgBV,QAAQuF,IAAIE,qBAAsBxL,GAA3B,GAAiC;AAC9E,QAAM6G,YAAeyE,IAAIjE,QAAV,MAAmBrH,IAAIC,SAAJ;AAClC,QAAM8G,wBAAwBuE,IAAIE,oBAAqBxL,GAAzB,EAA8BgH;AAC5D,QAAMC,0BAA0BR,WAAWO;AAC3ClG,QACI,mBAAiBmG,0BAAjB,WAAiDJ,YAAjD,QAAA,4CAC8CE,wBAD9C,QAAA,sGADD;EAMN;AACJ;ACrtBD,IAAI4mB,+BAA+B;IAG7BC,YAAAA,SAAAA,aAAAA;AAAAA;AACN,SAASC,QAAQC,MAAMjqB,OAAvB;AACI,MAAI/B,OAAOisB,gBAAgB;AACvBjsB,WAAOisB,eAAeD,KAAK3rB,WAAW0B,KAAtC;EACH,WAAUiqB,KAAK3rB,UAAU6rB,cAAc9jB,QAAW;AAC/C4jB,SAAK3rB,UAAU6rB,YAAYnqB;EAC9B,OAAM;AACHiqB,SAAK3rB,YAAY0B;EACpB;AACJ;AACDgqB,QAAQD,WAAW9jB,MAAM3H,SAAlB;IAMD8rB,wBAAAA,SAAAA,YAAAA;;AACF,WAAAA,uBACIhf,eACA7B,UACA7M,MACA6oB,OAJJ;;QAGI7oB,SAAAA,QAAAA;AAAAA,aAAO,OAAU,qBAAqBwC,UAAS,IAAK;;QACpDqmB,UAAAA,QAAAA;AAAAA,cAAQ;;AAER,YAAA,WAAA,KAAA,IAAA,KAAA;AAEA,QAAM9d,MAAM,IAAI+b,8BAA8B9mB,MAAM6M,UAAUgc,OAAO,IAAzD;AACZ9d,QAAIiB,SAAJ,uBAAA,KAAA;AACA/H,uBAAkB,uBAAA,KAAA,GAAO2C,OAAOmE,GAAd;AAElB,QAAI2D,iBAAiBA,cAAc7O,QAAQ;AACvC,UAAM0S,OAAOZ,uBAAuB,IAAD;AAEnC,YAAKoX,gBAAgB,GAAG,GAAGra,aAA3B;AACAwD,2BAAqBK,IAAD;IACvB;;EACJ;;SAEDlN,SAAA,SAAA,SAAA;AACM,SAAKuB,KAAL,EAA8CqgB,MAAMrf,eAApD;sCADI+lB,SAAAA,IAAAA,MAAAA,IAAAA,GAAAA,OAAAA,GAAAA,OAAAA,MAAAA,QAAAA;AAAAA,aAAAA,IAAAA,IAAAA,UAAAA,IAAAA;;AAEN,WAAOpkB,MAAM3H,UAAUyD,OAAO1E;MACzB,KAAa+iB,MAAb;;MAEDiK,OAAO9sB,IAAI,SAAA4H,GAAC;AAAA,eAAK0B,kBAAkB1B,CAAD,IAAMA,EAAEib,MAAF,IAAYjb;MAAxC,CAAZ;IAHG;EAKV;SAcAzC,OAAOqkB,QAAAA,IAAR,WAAA;AACI,QAAMhpB,QAAO;AACb,QAAI0qB,YAAY;AAChB,WAAOb,aAAa;MAChBhJ,MADgB,SAAA,OAAA;AAGZ,eAAO6J,YAAY1qB,MAAKxB,SAClB;UAAEqD,OAAO7B,MAAK0qB,WAAD;UAAe1J,MAAM;QAAlC,IACA;UAAEA,MAAM;UAAMnf,OAAOyG;QAArB;MACT;IANe,CAAD;EAQtB;;;;AAtBG,aAAQ,KAAK/C,KAAL,EAA8C8f,gBAA9C;IACX;uBAEUkB,WAAAA;AACL,WAAKhhB,KAAL,EAA8CigB,gBAAgBe,SAA9D;IACL;;SAEI5hB,OAAOskB;;AACR,aAAO;IACV;;;EAxCkC+C,SAAAA;AAwDvC9rB,OAAOoiB,QAAQiD,eAAf,EAAgC9gB,QAAQ,SAAA,MAAA;MAAEJ,OAAAA,KAAAA,CAAAA,GAAM1C,KAAAA,KAAAA,CAAAA;AAC5C,MAAI0C,SAAS;AAAU/B,kBAAc+pB,sBAAsB9rB,WAAW8D,MAAM1C,EAAxC;AACvC,CAFD;AAIA,SAAS4qB,2BAA2BhuB,OAApC;AACI,SAAO;IACHkE,YAAY;IACZE,cAAc;IACdyI,KAAK,SAAAA,OAAA;AACD,aAAO,KAAK7F,KAAL,EAAYqd,KAAKrkB,KAAjB;IACV;IACDgK,KAAK,SAAAA,KAAU1G,OAAV;AACD,WAAK0D,KAAL,EAAYgd,KAAKhkB,OAAOsD,KAAxB;IACH;EARE;AAUV;AAED,SAAS2qB,sBAAsBjuB,OAA/B;AACI8B,iBAAegsB,sBAAsB9rB,WAAW,KAAKhC,OAAOguB,2BAA2BhuB,KAAD,CAAxE;AACjB;AAED,SAAgBuoB,mBAAmBG,KAAAA;AAC/B,MAAIA,MAAM8E,8BAA8B;AACpC,aAASxtB,QAAQwtB,8BAA8BxtB,QAAQ0oB,MAAM,KAAK1oB,SAAlE;AACIiuB,4BAAsBjuB,KAAD;IADzB;AAEAwtB,mCAA+B9E;EAClC;AACJ;AAEDH,mBAAmB,GAAD;AAElB,SAAgBvZ,kBACZF,eACA7B,UACA7M,MAAAA;AAEA,SAAO,IAAI0tB,sBAAsBhf,eAAe7B,UAAU7M,IAAnD;AACV;SCtHeggB,QAAQ9f,OAAYC,UAAAA;AAChC,MAAI,OAAOD,UAAU,YAAYA,UAAU,MAAM;AAC7C,QAAIiK,kBAAkBjK,KAAD,GAAS;AAC1B,UAAIC,aAAawJ;AAAWpJ,YAAI,EAAD;AAC/B,aAAQL,MAAc0G,KAAD,EAAQqgB;IAChC;AACD,QAAI5c,gBAAgBnK,KAAD,GAAS;AACxB,aAAQA,MAAc0G,KAAD;IACxB;AACD,QAAIwD,gBAAgBlK,KAAD,GAAS;AACxB,UAAIC,aAAawJ;AAAW,eAAOzJ,MAAMwqB;AACzC,UAAMjhB,cAAavJ,MAAMsqB,MAAM/d,IAAItM,QAAhB,KAA6BD,MAAMuqB,QAAQhe,IAAItM,QAAlB;AAChD,UAAI,CAACsJ;AAAYlJ,YAAI,IAAIJ,UAAU8sB,aAAa/sB,KAAD,CAA3B;AACpB,aAAOuJ;IACV;AAED,QAAIS,mBAAmBhK,KAAD,GAAS;AAC3B,UAAI,CAACC;AAAU,eAAOI,IAAI,EAAD;AACzB,UAAMkJ,cAAcvJ,MAAc0G,KAAD,EAAQsc,QAAQzW,IAAItM,QAAjC;AACpB,UAAI,CAACsJ;AAAYlJ,YAAI,IAAIJ,UAAU8sB,aAAa/sB,KAAD,CAA3B;AACpB,aAAOuJ;IACV;AACD,QAAIxB,OAAO/H,KAAD,KAAWwX,gBAAgBxX,KAAD,KAAWyd,WAAWzd,KAAD,GAAS;AAC9D,aAAOA;IACV;EACJ,WAAU6C,WAAW7C,KAAD,GAAS;AAC1B,QAAIyd,WAAWzd,MAAM0G,KAAD,CAAN,GAAgB;AAE1B,aAAO1G,MAAM0G,KAAD;IACf;EACJ;AACDrG,MAAI,EAAD;AACN;AAED,SAAgBsiB,kBAAkB3iB,OAAYC,UAAAA;AAC1C,MAAI,CAACD;AAAOK,QAAI,EAAD;AACf,MAAIJ,aAAawJ;AAAW,WAAOkZ,kBAAkB7C,QAAQ9f,OAAOC,QAAR,CAAR;AACpD,MAAI8H,OAAO/H,KAAD,KAAWwX,gBAAgBxX,KAAD,KAAWyd,WAAWzd,KAAD;AAAS,WAAOA;AACzE,MAAIkK,gBAAgBlK,KAAD,KAAWmK,gBAAgBnK,KAAD;AAAS,WAAOA;AAC7D,MAAIA,MAAM0G,KAAD;AAAS,WAAO1G,MAAM0G,KAAD;AAC9BrG,MAAI,IAAIL,KAAL;AACN;AAED,SAAgB+sB,aAAa/sB,OAAYC,UAAAA;AACrC,MAAI2tB;AACJ,MAAI3tB,aAAawJ,QAAW;AACxBmkB,YAAQ9N,QAAQ9f,OAAOC,QAAR;EAClB,WAAU0J,SAAS3J,KAAD,GAAS;AACxB,WAAOA,MAAMF;EAChB,WAAUkK,mBAAmBhK,KAAD,KAAWkK,gBAAgBlK,KAAD,KAAWmK,gBAAgBnK,KAAD,GAAS;AACtF4tB,YAAQjL,kBAAkB3iB,KAAD;EAC5B,OAAM;AAEH4tB,YAAQ9N,QAAQ9f,KAAD;EAClB;AACD,SAAO4tB,MAAMhnB;AAChB;AC3DD,IAAMpH,WAAWiC,gBAAgBjC;AAEjC,SAAgBkJ,UAAUH,GAAQC,GAAQiT,OAAAA;MAAAA,UAAAA,QAAAA;AAAAA,YAAgB;;AACtD,SAAOoS,GAAGtlB,GAAGC,GAAGiT,KAAP;AACZ;AAID,SAASoS,GAAGtlB,GAAQC,GAAQiT,OAAeqS,QAAgBC,QAA3D;AAGI,MAAIxlB,MAAMC;AAAG,WAAOD,MAAM,KAAK,IAAIA,MAAM,IAAIC;AAE7C,MAAID,KAAK,QAAQC,KAAK;AAAM,WAAO;AAEnC,MAAID,MAAMA;AAAG,WAAOC,MAAMA;AAE1B,MAAMyI,OAAO,OAAO1I;AACpB,MAAI,CAAC1F,WAAWoO,IAAD,KAAUA,SAAS,YAAY,OAAOzI,KAAK;AAAU,WAAO;AAG3E,MAAMwlB,YAAYxuB,SAASuF,KAAKwD,CAAd;AAClB,MAAIylB,cAAcxuB,SAASuF,KAAKyD,CAAd;AAAkB,WAAO;AAC3C,UAAQwlB,WAAR;IAEI,KAAK;IAEL,KAAK;AAGD,aAAO,KAAKzlB,MAAM,KAAKC;IAC3B,KAAK;AAGD,UAAI,CAACD,MAAM,CAACA;AAAG,eAAO,CAACC,MAAM,CAACA;AAE9B,aAAO,CAACD,MAAM,IAAI,IAAI,CAACA,MAAM,IAAIC,IAAI,CAACD,MAAM,CAACC;IACjD,KAAK;IACL,KAAK;AAID,aAAO,CAACD,MAAM,CAACC;IACnB,KAAK;AACD,aACI,OAAO1C,WAAW,eAAeA,OAAOqO,QAAQpP,KAAKwD,CAApB,MAA2BzC,OAAOqO,QAAQpP,KAAKyD,CAApB;IAEpE,KAAK;IACL,KAAK;AAGD,UAAIiT,SAAS,GAAG;AACZA;MACH;AACD;EA/BR;AAkCAlT,MAAI0lB,OAAO1lB,CAAD;AACVC,MAAIylB,OAAOzlB,CAAD;AAEV,MAAM0lB,YAAYF,cAAc;AAChC,MAAI,CAACE,WAAW;AACZ,QAAI,OAAO3lB,KAAK,YAAY,OAAOC,KAAK;AAAU,aAAO;AAIzD,QAAM2lB,QAAQ5lB,EAAE1I,aACZuuB,QAAQ5lB,EAAE3I;AACd,QACIsuB,UAAUC,SACV,EACIvrB,WAAWsrB,KAAD,KACVA,iBAAiBA,SACjBtrB,WAAWurB,KAAD,KACVA,iBAAiBA,UAErB,iBAAiB7lB,KACjB,iBAAiBC,GACnB;AACE,aAAO;IACV;EACJ;AAED,MAAIiT,UAAU,GAAG;AACb,WAAO;EACV,WAAUA,QAAQ,GAAG;AAClBA,YAAQ;EACX;AAODqS,WAASA,UAAU,CAAA;AACnBC,WAASA,UAAU,CAAA;AACnB,MAAIpuB,SAASmuB,OAAOnuB;AACpB,SAAOA,UAAU;AAGb,QAAImuB,OAAOnuB,MAAD,MAAa4I;AAAG,aAAOwlB,OAAOpuB,MAAD,MAAa6I;EACvD;AAGDslB,SAAOjT,KAAKtS,CAAZ;AACAwlB,SAAOlT,KAAKrS,CAAZ;AAGA,MAAI0lB,WAAW;AAEXvuB,aAAS4I,EAAE5I;AACX,QAAIA,WAAW6I,EAAE7I;AAAQ,aAAO;AAEhC,WAAOA,UAAU;AACb,UAAI,CAACkuB,GAAGtlB,EAAE5I,MAAD,GAAU6I,EAAE7I,MAAD,GAAU8b,QAAQ,GAAGqS,QAAQC,MAA1C;AAAmD,eAAO;IACpE;EACJ,OAAM;AAEH,QAAMrpB,QAAOrD,OAAOqD,KAAK6D,CAAZ;AACb,QAAIhJ;AACJI,aAAS+E,MAAK/E;AAEd,QAAI0B,OAAOqD,KAAK8D,CAAZ,EAAe7I,WAAWA;AAAQ,aAAO;AAC7C,WAAOA,UAAU;AAEbJ,YAAMmF,MAAK/E,MAAD;AACV,UAAI,EAAE2F,QAAQkD,GAAGjJ,GAAJ,KAAYsuB,GAAGtlB,EAAEhJ,GAAD,GAAOiJ,EAAEjJ,GAAD,GAAOkc,QAAQ,GAAGqS,QAAQC,MAApC;AAA8C,eAAO;IACnF;EACJ;AAEDD,SAAOlJ,IAAP;AACAmJ,SAAOnJ,IAAP;AACA,SAAO;AACV;AAED,SAASqJ,OAAO1lB,GAAhB;AACI,MAAI0B,kBAAkB1B,CAAD;AAAK,WAAOA,EAAEib,MAAF;AACjC,MAAIrf,SAASoE,CAAD,KAAO2B,gBAAgB3B,CAAD;AAAK,WAAOc,MAAM0H,KAAKxI,EAAEkb,QAAF,CAAX;AAC9C,MAAIpf,SAASkE,CAAD,KAAO4B,gBAAgB5B,CAAD;AAAK,WAAOc,MAAM0H,KAAKxI,EAAEkb,QAAF,CAAX;AAC9C,SAAOlb;AACV;SCxJeyiB,aAAgBb,UAAAA;AAC5BA,WAASrkB,OAAOqkB,QAAR,IAAoBkE;AAC5B,SAAOlE;AACV;AAED,SAASkE,UAAT;AACI,SAAO;AACV;SC8BejC,aAAapsB,OAAAA;AACzB;;IAEIA,iBAAiBqB,UACjB,OAAOrB,MAAMuG,oBAAoB,YACjC1D,WAAW7C,MAAM2K,KAAP,KACV9H,WAAW7C,MAAM4K,OAAP;;AAEjB;AC1BA,CAAC,UAAU,OAAO,OAAO,QAAzB,EAAmChF,QAAQ,SAAA0oB,GAAC;AACzC,MAAIC,IAAIxtB,UAAS;AACjB,MAAI,OAAOwtB,EAAED,CAAD,MAAQ,aAAa;AAC7BjuB,QAAG,2BAA0BiuB,IAA1B,iCAAA;EACN;AACJ,CALA;AA+HD,IAAI,OAAOE,kCAAkC,UAAU;AAEnDA,gCAA8BC,WAAW;IACrC1Q;IACA2Q,QAAQ;MACJ3B;IADI;IAGRrmB;EALqC,CAAzC;AAOH;", "names": ["niceErrors", "annotationType", "key", "toString", "_", "index", "length", "other", "constructor", "name", "dataStructure", "thing", "property", "derivation", "method", "errors", "die", "error", "args", "e", "apply", "Error", "map", "String", "join", "mockGlobal", "getGlobal", "globalThis", "window", "global", "self", "assign", "Object", "getDescriptor", "getOwnPropertyDescriptor", "defineProperty", "objectPrototype", "prototype", "EMPTY_ARRAY", "freeze", "EMPTY_OBJECT", "hasProxy", "Proxy", "plainObjectString", "assertProxies", "warnAboutProxyRequirement", "msg", "globalState", "verifyProxies", "getNextId", "mobxGuid", "once", "func", "invoked", "arguments", "noop", "isFunction", "fn", "isStringish", "value", "t", "isObject", "isPlainObject", "proto", "getPrototypeOf", "isGenerator", "obj", "displayName", "addHiddenProp", "object", "propName", "enumerable", "writable", "configurable", "addHiddenFinalProp", "createInstanceofPredicate", "theClass", "x", "isES6Map", "Map", "isES6Set", "Set", "hasGetOwnPropertySymbols", "getOwnPropertySymbols", "getPlainObjectKeys", "keys", "symbols", "filter", "s", "propertyIsEnumerable", "call", "ownKeys", "Reflect", "getOwnPropertyNames", "concat", "stringify<PERSON>ey", "toPrimitive", "hasProp", "target", "prop", "hasOwnProperty", "getOwnPropertyDescriptors", "res", "for<PERSON>ach", "storedAnnotationsSymbol", "Symbol", "createDecoratorAnnotation", "annotation", "decorator", "storeAnnotation", "isOverride", "fieldName", "assertNotDecorated", "currentAnnotationType", "annotationType_", "requestedAnnotationType", "collectStoredAnnotations", "$mobx", "Atom", "name_", "isPendingUnobservation_", "isBeingObserved_", "observers_", "diffValue_", "lastAccessedBy_", "lowestObserverState_", "IDerivationState_", "NOT_TRACKING_", "onBOL", "onBUOL", "onBO", "listener", "onBUO", "reportObserved", "reportChanged", "startBatch", "propagateChanged", "endBatch", "isAtom", "createAtom", "onBecomeObservedHandler", "onBecomeUnobservedHandler", "atom", "onBecomeObserved", "onBecomeUnobserved", "identity<PERSON><PERSON><PERSON><PERSON>", "a", "b", "structuralComparer", "deepEqual", "<PERSON><PERSON><PERSON><PERSON>er", "defaultComparer", "is", "comparer", "identity", "structural", "shallow", "deepEnhancer", "v", "isObservable", "Array", "isArray", "observable", "array", "undefined", "set", "isAction", "isFlow", "flow", "autoAction", "shallowEnhancer", "isObservableObject", "isObservableArray", "isObservableMap", "isObservableSet", "deep", "referenceEnhancer", "newValue", "refStructEnhancer", "oldValue", "OVERRIDE", "override", "make_", "extend_", "adm", "isPlainObject_", "appliedAnnotations_", "descriptor", "proxyTrap", "createActionAnnotation", "options", "options_", "source", "bound", "target_", "actionDescriptor", "createActionDescriptor", "defineProperty_", "assertActionDescriptor", "safeDescriptors", "bind", "proxy_", "createAction", "createFlowAnnotation", "flowDescriptor", "createFlowDescriptor", "assertFlowDescriptor", "createComputedAnnotation", "assertComputedDescriptor", "defineComputedProperty_", "get", "createObservableAnnotation", "assertObservableDescriptor", "defineObservableProperty_", "enhancer", "AUTO", "autoAnnotation", "createAutoAnnotation", "computed", "flowAnnotation", "autoBind", "actionAnnotation", "observableAnnotation", "ref", "defaultCreateObservableOptions", "defaultDecorator", "proxy", "asCreateObservableOptions", "observableRefAnnotation", "observableShallowAnnotation", "observableStructAnnotation", "observableDecoratorAnnotation", "getEnhancerFromOptions", "getEnhancerFromAnnotation", "getAnnotationFromOptions", "createObservable", "arg2", "arg3", "box", "observableFactories", "o", "ObservableValue", "equals", "initialValues", "useProxies", "createLegacyArray", "createObservableArray", "ObservableMap", "ObservableSet", "props", "decorators", "extendObservable", "asObservableObject", "asDynamicObservableObject", "struct", "COMPUTED", "COMPUTED_STRUCT", "computedAnnotation", "computedStructAnnotation", "arg1", "opts", "ComputedValue", "currentActionId", "nextActionId", "isFunctionNameConfigurable", "tmpNameDescriptor", "actionName", "executeAction", "isMobxAction", "canRunAsDerivation", "scope", "runInfo", "_startAction", "err", "error_", "_endAction", "notifySpy_", "isSpyEnabled", "startTime_", "Date", "now", "<PERSON><PERSON><PERSON><PERSON>", "from", "spyReportStart", "type", "ACTION", "prevDerivation_", "trackingDerivation", "runAsAction", "prevAllowStateChanges_", "allowStateChanges", "untrackedStart", "allowStateChangesStart", "prevAllowStateReads_", "allowStateReadsStart", "runAsAction_", "actionId_", "parentActionId_", "suppressReactionErrors", "allowStateChangesEnd", "allowStateReadsEnd", "untrackedEnd", "spyReportEnd", "time", "prev", "CREATE", "notifySpy", "hasUnreportedChange_", "interceptors_", "changeListeners_", "value_", "dehancer", "spyReport", "observableKind", "debugObjectName", "dehanceV<PERSON>ue", "prepareNewValue_", "UNCHANGED", "UPDATE", "setNewValue_", "checkIfStateModificationsAreAllowed", "hasInterceptors", "change", "interceptChange", "hasListeners", "notifyListeners", "intercept_", "handler", "registerInterceptor", "observe_", "fireImmediately", "registerListener", "raw", "toJSON", "valueOf", "isObservableValue", "dependenciesState_", "observing_", "newObserving_", "runId_", "UP_TO_DATE_", "unboundDepsCount_", "CaughtException", "triggeredBy_", "isComputing_", "isRunningSetter_", "setter_", "isTracing_", "TraceMode", "NONE", "scope_", "equals_", "requiresReaction_", "keepAlive_", "compareStructural", "context", "requiresReaction", "keepAlive", "onBecomeStale_", "propagateMaybeChanged", "inBatch", "size", "shouldCompute", "warnAboutUntrackedRead_", "computeValue_", "prevTrackingContext", "trackingContext", "trackAndCompute", "propagateChangeConfirmed", "result", "isCaughtException", "cause", "wasSuspended", "changed", "track", "trackDerivedFunction", "disableErrorBoundaries", "suspend_", "clearObserving", "firstTime", "prevValue", "autorun", "prevU", "console", "log", "computedRequiresReaction", "warn", "isComputedValue", "STALE_", "POSSIBLY_STALE_", "prevAllowStateReads", "prevUntracked", "obs", "l", "i", "changeDependenciesStateTo0", "isComputingDerivation", "hasObservers", "enforceActions", "checkIfStateReadsAreAllowed", "allowStateReads", "observableRequiresReaction", "f", "runId", "prevTracking", "bindDependencies", "warnAboutDerivationWithoutDependencies", "reactionRequiresObservable", "requiresObservable_", "prevObserving", "observing", "lowestNewObservingDerivationState", "i0", "dep", "removeObserver", "addObserver", "untracked", "action", "<PERSON><PERSON><PERSON><PERSON>", "MobXGlobals", "version", "pendingUnobservations", "pendingReactions", "isRunningReactions", "spyListeners", "globalReactionErrorHandlers", "canMergeGlobalState", "isolateCalled", "__mobxInstanceCount", "__mobxGlobals", "setTimeout", "isolateGlobalState", "getGlobalState", "resetGlobalState", "defaultGlobals", "indexOf", "getObservers", "node", "add", "queueForUnobservation", "push", "runReactions", "list", "d", "logTraceInfo", "BREAK", "lines", "printDepTree", "getDependencyTree", "Function", "replace", "tree", "depth", "dependencies", "child", "Reaction", "onInvalidate_", "errorHandler_", "isDisposed_", "isScheduled_", "isTrackPending_", "isRunning_", "schedule_", "isScheduled", "runReaction_", "reportExceptionInDerivation_", "notify", "startTime", "prevReaction", "message", "dispose", "getDisposer_", "r", "trace", "enterBreakPoint", "onReactionError", "idx", "splice", "MAX_REACTION_ITERATIONS", "reactionScheduler", "runReactionsHelper", "allReactions", "iterations", "remainingReactions", "isReaction", "setReactionScheduler", "baseScheduler", "event", "listeners", "END_EVENT", "spy", "ACTION_BOUND", "AUTOACTION", "AUTOACTION_BOUND", "DEFAULT_ACTION_NAME", "actionBoundAnnotation", "autoActionAnnotation", "autoActionBoundAnnotation", "createActionFactory", "runInAction", "view", "runSync", "scheduler", "delay", "reaction", "reactionRunner", "onError", "requiresObservable", "createSchedulerFromOptions", "run", "expression", "effect", "effectAction", "wrapErrorHandler", "nextValue", "<PERSON><PERSON><PERSON><PERSON>", "baseFn", "ON_BECOME_OBSERVED", "ON_BECOME_UNOBSERVED", "interceptHook", "hook", "getAtom", "cb", "listenersKey", "hookListeners", "NEVER", "ALWAYS", "OBSERVED", "configure", "ea", "properties", "annotations", "descriptors", "nodeToDependencyTree", "unique", "getObserverTree", "nodeToObserverTree", "observers", "generatorId", "FlowCancellationError", "create", "isFlowCancellationError", "flowBoundAnnotation", "generator", "ctx", "gen", "rejector", "pendingPromise", "promise", "Promise", "resolve", "reject", "stepId", "onFulfilled", "ret", "next", "onRejected", "then", "done", "cancel", "cancelPromise", "yieldedPromise", "isMobXFlow", "flowResult", "interceptReads", "prop<PERSON>r<PERSON><PERSON><PERSON>", "getAdministration", "intercept", "interceptProperty", "interceptInterceptable", "_isComputed", "values_", "has", "isComputed", "isComputedProp", "_isObservable", "isObservableProp", "keys_", "values", "slice", "entries", "set_", "parseInt", "remove", "delete_", "has_", "get_", "apiDefineProperty", "apiOwnKeys", "ownKeys_", "observe", "propOrCb", "cbOr<PERSON>ire", "observeObservableProperty", "observeObservable", "cache", "toJSHelper", "__alreadySeen", "toJS", "pop", "getAtomFromArgs", "LOG", "transaction", "thisArg", "when", "predicate", "when<PERSON><PERSON>ise", "_when", "timeoutH<PERSON>le", "timeout", "disposer", "cond", "clearTimeout", "getAdm", "objectProxyTraps", "deleteProperty", "preventExtensions", "interceptable", "interceptors", "listenable", "makeObservable", "keysSymbol", "makeAutoObservable", "overrides", "SPLICE", "MAX_SPLICE_SIZE", "arrayTraps", "getArrayLength_", "isNaN", "arrayExtensions", "setArrayLength_", "ObservableArrayAdministration", "owned_", "legacyMode_", "atom_", "enhancer_", "lastKnownLength_", "newV", "oldV", "dehance<PERSON><PERSON><PERSON>_", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_", "added", "addedCount", "removed", "removedCount", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "newItems", "spliceWithArray_", "updateArrayLength_", "<PERSON><PERSON><PERSON><PERSON>", "delta", "reserve<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deleteCount", "Math", "max", "min", "<PERSON><PERSON><PERSON><PERSON>", "spliceItemsIntoValues_", "notifyArraySplice_", "oldItems", "notifyArrayChildUpdate_", "owned", "clear", "spliceWithArray", "items", "shift", "unshift", "reverse", "sort", "copy", "addArrayExtension", "simpleFunc", "mapLikeFunc", "reduceLikeFunc", "funcName", "funcFactory", "<PERSON>han<PERSON><PERSON><PERSON><PERSON>", "callback", "element", "accumulator", "currentValue", "isObservableArrayAdministration", "ObservableMapMarker", "ADD", "DELETE", "iterator", "toStringTag", "initialData", "data_", "hasMap_", "keysAtom_", "merge", "entry", "newEntry", "<PERSON><PERSON><PERSON>", "updateValue_", "addValue_", "updateHasMapEntry_", "makeIterable", "replacementMap", "convertToMap", "orderedData", "keysReportChangedCalled", "deleted", "keyExisted", "iter1", "iter2", "next1", "next2", "ObservableSetMarker", "callbackFn", "nextIndex", "observableValues", "descriptor<PERSON>ache", "REMOVE", "ObservableObjectAdministration", "defaultAnnotation_", "pendingKeys_", "isAnnotation", "getObservablePropValue_", "setObservablePropValue_", "assertAnnotable", "outcome", "recordAnnotationApplied", "deleteOutcome", "notifyPropertyAddition_", "cachedDescriptor", "getCachedObservablePropDescriptor", "_getDescriptor", "getDebugName", "isExtensible", "isObservableObjectAdministration", "OBSERVABLE_ARRAY_BUFFER_SIZE", "StubArray", "inherit", "ctor", "setPrototypeOf", "__proto__", "LegacyObservableArray", "arrays", "createArrayEntryDescriptor", "createArrayBufferItem", "named", "eq", "aStack", "bStack", "className", "unwrap", "areArrays", "aCtor", "bCtor", "getSelf", "m", "g", "__MOBX_DEVTOOLS_GLOBAL_HOOK__", "injectMobx", "extras"]}