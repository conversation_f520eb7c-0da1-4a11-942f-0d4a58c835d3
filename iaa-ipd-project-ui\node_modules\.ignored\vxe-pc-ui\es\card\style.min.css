.vxe-card{position:relative;display:inline-flex;flex-direction:column;font-size:var(--vxe-ui-font-size-default);color:var(--vxe-ui-font-color);font-family:var(--vxe-ui-font-family);background-color:var(--vxe-ui-layout-background-color);border-radius:var(--vxe-ui-base-border-radius)}.vxe-card.is--shadow{box-shadow:var(--vxe-ui-base-popup-box-shadow)}.vxe-card.is--padding .vxe-card--body-content,.vxe-card.is--padding .vxe-card--body-left,.vxe-card.is--padding .vxe-card--body-right{padding:var(--vxe-ui-card-padding)}.vxe-card.is--border{border:1px solid var(--vxe-ui-input-border-color)}.vxe-card.is--border .vxe-card--header{border-bottom:1px solid var(--vxe-ui-input-border-color)}.vxe-card.is--border .vxe-card--footer{border-top:1px solid var(--vxe-ui-input-border-color)}.vxe-card--body,.vxe-card--header{display:flex;flex-direction:row}.vxe-card--header{flex-shrink:0}.vxe-card--footer,.vxe-card--header{padding:var(--vxe-ui-card-padding)}.vxe-card--body{overflow:hidden;flex-grow:1}.vxe-card--body-content,.vxe-card--body-left,.vxe-card--body-right{overflow:auto}.vxe-card--body-left,.vxe-card--body-right,.vxe-card--footer,.vxe-card--header,.vxe-card--header-extra{flex-shrink:0}.vxe-card--body-content,.vxe-card--header-title{flex-grow:1}.vxe-card--header-title.is--ellipsis{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}