var _vue=require("vue"),_xeUtils=_interopRequireDefault(require("xe-utils")),_ui=require("../../../ui"),_utils=require("../../../ui/src/utils"),_util=require("../../src/util"),_dom=require("../../../ui/src/dom"),_log=require("../../../ui/src/log");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let{getConfig,renderer,hooks,getI18n}=_ui.VxeUI,tableEditMethodKeys=["insert","insertAt","insertNextAt","insertChild","insertChildAt","insertChildNextAt","remove","removeCheckboxRow","removeRadioRow","removeCurrentRow","getRecordset","getInsertRecords","getRemoveRecords","getUpdateRecords","getEditRecord","getActiveRecord","getSelectedCell","clearEdit","clearActived","clearSelected","isEditByRow","isActiveByRow","setEditRow","setActiveRow","setEditCell","setActiveCell","setSelectCell"];hooks.add("tableEditModule",{setupTable(p){let{props:R,reactData:m,internalData:C}=p,o=p.getRefMaps().refElem,{computeMouseOpts:c,computeEditOpts:x,computeCheckboxOpts:_,computeTreeOpts:E,computeValidOpts:n}=p.getComputeMaps(),s=_xeUtils.default.browse(),I={},g={},b=(e,t)=>{var{model:l,editRender:r}=t;r&&(l.value=(0,_util.getCellValue)(e,t),l.update=!1)},a=(e,t)=>{var{model:l,editRender:r}=t;r&&l.update&&((0,_util.setCellValue)(e,t,l.value),l.update=!1,l.value=null)},l=()=>{var e=o.value;e&&(e=e.querySelector(".col--selected"))&&(0,_dom.removeClass)(e,"col--selected")},d=()=>{var{editStore:e,tableColumn:t}=m,l=x.value,e=e.actived;let{row:r,column:o}=e;(r||o)&&("row"===l.mode?t.forEach(e=>a(r,e)):a(r,o))},F=(e,t)=>{let{tableFullTreeData:i,afterFullData:n,fullDataRowIdData:d,fullAllDataRowIdData:u}=C;var l=E.value;let{rowField:s,parentField:c,mapChildrenField:g}=l,w=l.children||l.childrenField,v=t?"push":"unshift";e.forEach(l=>{let t=l[c];var r=(0,_util.getRowid)(p,l),o=t?_xeUtils.default.findTree(i,e=>t===e[s],{children:g}):null;if(o){var o=o.item,a=u[(0,_util.getRowid)(p,o)],a=a?a.level:0;let e=o[w],t=o[g];_xeUtils.default.isArray(e)||(e=o[w]=[]),_xeUtils.default.isArray(t)||(t=o[w]=[]),e[v](l),t[v](l);o={row:l,rowid:r,seq:-1,index:-1,_index:-1,$index:-1,treeIndex:-1,items:e,parent:o,level:a+1,height:0,resizeHeight:0,oTop:0,expandHeight:0};d[r]=o,u[r]=o}else{t&&(0,_log.warnLog)("vxe.error.unableInsert"),n[v](l),i[v](l);a={row:l,rowid:r,seq:-1,index:-1,_index:-1,$index:-1,treeIndex:-1,items:i,parent:null,level:0,height:0,resizeHeight:0,oTop:0,expandHeight:0};d[r]=a,u[r]=a}})},u=(t,l,d)=>{let o=R.treeConfig,{tableFullTreeData:e,afterFullData:a,mergeBodyList:i,tableFullData:n,fullDataRowIdData:u,fullAllDataRowIdData:s,insertRowMaps:r}=C,c=E.value,{transform:g,rowField:w,mapChildrenField:v}=c,f=c.children||c.childrenField,h=(_xeUtils.default.isArray(t)||(t=[t]),(0,_vue.reactive)(p.defineField(t.map(e=>Object.assign(o&&g?{[v]:[],[f]:[]}:{},e)))));if(_xeUtils.default.eqNull(l))o&&g?F(h,!1):(h.forEach(e=>{var t=(0,_util.getRowid)(p,e),l={row:e,rowid:t,seq:-1,index:-1,_index:-1,$index:-1,treeIndex:-1,items:a,parent:null,level:0,height:0,resizeHeight:0,oTop:0,expandHeight:0};u[t]=l,s[t]=l,a.unshift(e),n.unshift(e)}),i.forEach(e=>{var t=e.row;0<t&&(e.row=t+h.length)}));else if(-1===l)o&&g?F(h,!0):(h.forEach(e=>{var t=(0,_util.getRowid)(p,e),l={row:e,rowid:t,seq:-1,index:-1,_index:-1,treeIndex:-1,$index:-1,items:a,parent:null,level:0,height:0,resizeHeight:0,oTop:0,expandHeight:0};u[t]=l,s[t]=l,a.push(e),n.push(e)}),i.forEach(e=>{var{row:t,rowspan:l}=e;t+l>a.length&&(e.rowspan=l+h.length)}));else if(o&&g){let n=_xeUtils.default.findTree(e,e=>l[w]===e[w],{children:v});if(n){let o=n.parent,a=o?o[v]:e;t=s[(0,_util.getRowid)(p,o)];let i=t?t.level:0;if(h.forEach((e,t)=>{var l=(0,_util.getRowid)(p,e);e[c.parentField]&&o&&e[c.parentField]!==o[w]&&(0,_log.errLog)("vxe.error.errProp",[c.parentField+"="+e[c.parentField],c.parentField+"="+o[w]]),o&&(e[c.parentField]=o[w]);let r=n.index+t;d&&(r+=1),a.splice(r,0,e);t={row:e,rowid:l,seq:-1,index:-1,_index:-1,$index:-1,treeIndex:-1,items:a,parent:o,level:i+1,height:0,resizeHeight:0,oTop:0,expandHeight:0};u[l]=t,s[l]=t}),o){t=_xeUtils.default.findTree(e,e=>l[w]===e[w],{children:f});if(t){var x=t.items;let e=t.index;d&&(e+=1),x.splice(e,0,...h)}}}else(0,_log.warnLog)("vxe.error.unableInsert"),F(h,!0)}else{if(o)throw new Error(getI18n("vxe.error.noTree",["insert"]));let r=-1;if(_xeUtils.default.isNumber(l)?l<a.length&&(r=l):r=p.findRowIndexOf(a,l),-1===(r=d?Math.min(a.length,r+1):r))throw new Error(getI18n("vxe.error.unableInsert"));a.splice(r,0,...h);t=p.findRowIndexOf(n,l);-1<t?n.splice(t+(d?1:0),0,...h):n.push(...h),i.forEach(e=>{var{row:t,rowspan:l}=e;t>r?e.row=t+h.length:t+l>r&&(e.rowspan=l+h.length)})}return h.forEach(e=>{var t=(0,_util.getRowid)(p,e);r[t]=e}),m.insertRowFlag++,p.cacheRowMap(!1),p.updateScrollYStatus(),p.handleTableData(o&&g),o&&g||p.updateAfterDataIndex(),p.updateFooter(),p.handleUpdateBodyMerge(),p.checkSelectionStatus(),m.scrollYLoad&&p.updateScrollYSpace(),(0,_vue.nextTick)().then(()=>(p.updateCellAreas(),p.recalculate(!0))).then(()=>({row:h.length?h[h.length-1]:null,rows:h}))},r=(e,t,l,r)=>{var o=R.treeConfig;let{transform:a,rowField:i,parentField:n}=E.value;return o&&a?(_xeUtils.default.isArray(e)||(e=[e]),u(e.map(e=>Object.assign({},e,{[n]:t[i]})),l,r)):((0,_log.errLog)("vxe.error.errProp",["tree-config.transform=false","tree-config.transform=true"]),Promise.resolve({row:null,rows:[]}))},A=(e,t)=>{var l=m.editStore,{actived:l,focused:r}=l,{row:o,column:a}=l,i=n.value;if(o||a){if(t&&(0,_util.getRowid)(p,t)!==(0,_util.getRowid)(p,o))return(0,_vue.nextTick)();d(),l.args=null,l.row=null,l.column=null,p.updateFooter(),p.dispatchEvent("edit-closed",{row:o,rowIndex:p.getRowIndex(o),$rowIndex:p.getVMRowIndex(o),column:a,columnIndex:p.getColumnIndex(a),$columnIndex:p.getVMColumnIndex(a)},e||null)}return r.row=null,r.column=null,i.autoClear&&("full"!==i.msgMode||"obsolete"===getConfig().cellVaildMode)&&p.clearValidate?p.clearValidate():(0,_vue.nextTick)().then(()=>p.updateCellAreas())},i=(l,r,o,e)=>{let a=p.xeGrid;var{editConfig:t,mouseConfig:i}=R,{editStore:n,tableColumn:d}=m,u=x.value,s=u.mode,{actived:n,focused:c}=n;let{row:g,column:w}=l;var v=w.editRender,f=l.cell||p.getCellElement(g,w),h=u.beforeEditMethod||u.activeMethod;if((l.cell=f)&&(0,_utils.isEnableConf)(t)&&(0,_utils.isEnableConf)(v)&&!p.isPendingByRow(g)&&!p.isAggregateRecord(g)){if(n.row!==g||"cell"===s&&n.column!==w){let t="edit-disabled";if(!h||h(Object.assign(Object.assign({},l),{$table:p,$grid:a}))){i&&(p.clearSelected(),p.clearCellAreas)&&(p.clearCellAreas(),p.clearCopyCellArea()),p.closeTooltip(),n.column&&A(r),t="edit-activated",w.renderHeight=f.offsetHeight,n.args=l,n.row=g,n.column=w,"row"===s?d.forEach(e=>b(g,e)):b(g,w);let e=u.afterEditMethod;(0,_vue.nextTick)(()=>{o&&p.handleFocus(l,r),e&&e(Object.assign(Object.assign({},l),{$table:p,$grid:a}))})}p.dispatchEvent(t,{row:g,rowIndex:p.getRowIndex(g),$rowIndex:p.getVMRowIndex(g),column:w,columnIndex:p.getColumnIndex(w),$columnIndex:p.getVMColumnIndex(w)},r),"edit-activated"===t&&p.dispatchEvent("edit-actived",{row:g,rowIndex:p.getRowIndex(g),$rowIndex:p.getVMRowIndex(g),column:w,columnIndex:p.getColumnIndex(w),$columnIndex:p.getVMColumnIndex(w)},r)}else{t=n.column;i&&(p.clearSelected(),p.clearCellAreas)&&(p.clearCellAreas(),p.clearCopyCellArea()),t!==w&&(v=t.model,v.update&&(0,_util.setCellValue)(g,t,v.value),p.clearValidate)&&p.clearValidate(g,w),w.renderHeight=f.offsetHeight,n.args=l,n.column=w,e&&setTimeout(()=>{p.handleFocus(l,r)})}c.column=null,c.row=null,p.focus()}return(0,_vue.nextTick)()},w=(t,e,l)=>{var r=R.editConfig;let o=_xeUtils.default.isString(e)?p.getColumnByField(e):e;return t&&o&&(0,_utils.isEnableConf)(r)&&(0,_utils.isEnableConf)(o.editRender)&&!p.isAggregateRecord(t)?Promise.resolve(l?p.scrollToRow(t,o):null).then(()=>{var e=p.getCellElement(t,o);return e&&(i({row:t,rowIndex:p.getRowIndex(t),column:o,columnIndex:p.getColumnIndex(o),cell:e,$table:p},null,l,l),C._lastCallTime=Date.now()),(0,_vue.nextTick)()}):(0,_vue.nextTick)()};return I={insert(e){return u(e,null)},insertAt(e,t){return u(e,t)},insertNextAt(e,t){return u(e,t,!0)},insertChild(e,t){return r(e,t,null)},insertChildAt(e,t,l){return r(e,t,l)},insertChildNextAt(e,t,l){return r(e,t,l,!0)},remove(e){var t=R.treeConfig,l=m.editStore;let{tableFullTreeData:r,selectCheckboxMaps:o,afterFullData:a,mergeBodyList:i,tableFullData:n,pendingRowMaps:d,insertRowMaps:u,removeRowMaps:s}=C;var c=_.value,g=E.value;let{transform:w,mapChildrenField:v}=g,f=g.children||g.childrenField;g=l.actived,l=c.checkField;let h=[];return e?_xeUtils.default.isArray(e)||(e=[e]):e=n,e.forEach(e=>{var t;p.isInsertByRow(e)||(t=(0,_util.getRowid)(p,e),s[t]=e)}),l||(e.forEach(e=>{e=(0,_util.getRowid)(p,e);o[e]&&delete o[e]}),m.updateCheckboxFlag++),n===e?(e=h=n.slice(0),C.tableFullData=[],C.afterFullData=[],p.clearMergeCells()):t&&w?e.forEach(e=>{let t=(0,_util.getRowid)(p,e);var l=_xeUtils.default.findTree(r,e=>t===(0,_util.getRowid)(p,e),{children:v}),l=(l&&(l=l.items.splice(l.index,1),h.push(l[0])),_xeUtils.default.findTree(r,e=>t===(0,_util.getRowid)(p,e),{children:f})),l=(l&&l.items.splice(l.index,1),p.findRowIndexOf(a,e));-1<l&&a.splice(l,1)}):e.forEach(e=>{var t=p.findRowIndexOf(n,e);-1<t&&(t=n.splice(t,1),h.push(t[0]));let r=p.findRowIndexOf(a,e);-1<r&&(i.forEach(e=>{var{row:t,rowspan:l}=e;t>r?e.row=t-1:t+l>r&&(e.rowspan=l-1)}),a.splice(r,1))}),g.row&&-1<p.findRowIndexOf(e,g.row)&&I.clearEdit(),e.forEach(e=>{e=(0,_util.getRowid)(p,e);u[e]&&delete u[e],d[e]&&delete d[e]}),m.removeRowFlag++,m.insertRowFlag++,m.pendingRowFlag++,p.cacheRowMap(!1),p.handleTableData(t&&w),p.updateFooter(),p.handleUpdateBodyMerge(),t&&w||p.updateAfterDataIndex(),p.checkSelectionStatus(),m.scrollYLoad&&p.updateScrollYSpace(),(0,_vue.nextTick)().then(()=>(p.updateCellAreas(),p.recalculate(!0))).then(()=>({row:h.length?h[h.length-1]:null,rows:h}))},removeCheckboxRow(){return I.remove(p.getCheckboxRecords()).then(e=>(p.clearCheckboxRow(),e))},removeRadioRow(){var e=p.getRadioRecord();return I.remove(e||[]).then(e=>(p.clearRadioRow(),e))},removeCurrentRow(){var e=p.getCurrentRecord();return I.remove(e||[]).then(e=>(p.clearCurrentRow(),e))},getRecordset(){var e=I.getRemoveRecords(),t=p.getPendingRecords();let l=e.concat(t);var r=I.getUpdateRecords().filter(t=>!l.some(e=>p.eqRow(e,t)));return{insertRecords:I.getInsertRecords(),removeRecords:e,updateRecords:r,pendingRecords:t}},getInsertRecords(){let{fullAllDataRowIdData:l,insertRowMaps:e}=C,r=[];return _xeUtils.default.each(e,(e,t)=>{l[t]&&r.push(e)}),r},getRemoveRecords(){var e=C.removeRowMaps;let t=[];return _xeUtils.default.each(e,e=>{t.push(e)}),t},getUpdateRecords(){var{keepSource:e,treeConfig:t}=R,l=C.tableFullData,r=E.value;return e?(d(),t?_xeUtils.default.filterTree(l,e=>p.isUpdateByRow(e),r):l.filter(e=>p.isUpdateByRow(e))):[]},getActiveRecord(){return(0,_log.warnLog)("vxe.error.delFunc",["getActiveRecord","getEditRecord"]),p.getEditRecord()},getEditRecord(){var e=m.editStore,t=C.afterFullData,l=o.value,{args:e,row:r}=e.actived;return e&&-1<p.findRowIndexOf(t,r)&&l.querySelectorAll(".vxe-body--column.col--active").length?Object.assign({},e):null},getSelectedCell(){var e=m.editStore,{args:e,column:t}=e.selected;return e&&t?Object.assign({},e):null},clearActived(e){return(0,_log.warnLog)("vxe.error.delFunc",["clearActived","clearEdit"]),p.clearEdit(e)},clearEdit(e){return A(null,e)},clearSelected(){var e=m.editStore,e=e.selected;return e.row=null,e.column=null,l(),(0,_vue.nextTick)()},isActiveByRow(e){return(0,_log.warnLog)("vxe.error.delFunc",["isActiveByRow","isEditByRow"]),p.isEditByRow(e)},isEditByRow(e){var t=m.editStore;return t.actived.row===e},setActiveRow(e){return(0,_log.warnLog)("vxe.error.delFunc",["setActiveRow","setEditRow"]),I.setEditRow(e)},setEditRow(e,t){var l=C.visibleColumn;let r=_xeUtils.default.find(l,e=>(0,_utils.isEnableConf)(e.editRender)),o=!1;return t&&(o=!0)!==t&&(r=_xeUtils.default.isString(t)?p.getColumnByField(t):t),w(e,r,o)},setActiveCell(e,t){return(0,_log.warnLog)("vxe.error.delFunc",["setActiveCell","setEditCell"]),I.setEditCell(e,t)},setEditCell(e,t){return w(e,t,!0)},setSelectCell(e,t){var l=m.tableData,r=x.value,t=_xeUtils.default.isString(t)?p.getColumnByField(t):t;return e&&t&&"manual"!==r.trigger&&-1<(r=p.findRowIndexOf(l,e))&&t&&(l=p.getCellElement(e,t),e={row:e,rowIndex:r,column:t,columnIndex:p.getColumnIndex(t),cell:l},p.handleSelected(e,{})),(0,_vue.nextTick)()}},g={handleEdit(e,t){return i(e,t,!0,!0)},handleActived(e,t){return g.handleEdit(e,t)},handleClearEdit:A,handleFocus(r){var{row:o,column:a,cell:i}=r,n=a.editRender,d=x.value;if((0,_utils.isEnableConf)(n)){var u=renderer.get(n.name);let e=n.autofocus||n.autoFocus,t=n.autoSelect||n.autoselect,l;d.autoFocus&&(!e&&u&&(e=u.tableAutoFocus||u.tableAutofocus||u.autofocus),!t&&u&&(t=u.tableAutoSelect||u.autoselect),_xeUtils.default.isFunction(e)?l=e(r):e&&(l=!0===e?i.querySelector("input,textarea"):i.querySelector(e))&&l.focus()),l?t?l.select():s.msie&&((n=l.createTextRange()).collapse(!1),n.select()):d.autoPos&&!a.fixed&&p.scrollToRow(o,a)}},handleSelected(e,t){var l=R.mouseConfig,r=m.editStore,o=c.value;let a=x.value,{actived:i,selected:n}=r,{row:d,column:u}=e,s=l&&o.selected;return!s||n.row===d&&n.column===u||(i.row!==d||"cell"===a.mode&&i.column!==u)&&(A(t),p.clearSelected(),p.clearCellAreas&&(p.clearCellAreas(),p.clearCopyCellArea()),n.args=e,n.row=d,n.column=u,s&&g.addCellSelectedClass(),p.focus(),t)&&p.dispatchEvent("cell-selected",e,t),(0,_vue.nextTick)()},addCellSelectedClass(){var e=m.editStore,e=e.selected,{row:e,column:t}=e;l(),e&&t&&(e=p.getCellElement(e,t))&&(0,_dom.addClass)(e,"col--selected")}},Object.assign(Object.assign({},I),g)},setupGrid(e){return e.extendTableMethods(tableEditMethodKeys)}});