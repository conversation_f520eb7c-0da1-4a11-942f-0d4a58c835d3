{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/razor/razor.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { languages } from '../fillers/monaco-editor-core.js';\nvar EMPTY_ELEMENTS = [\n    'area',\n    'base',\n    'br',\n    'col',\n    'embed',\n    'hr',\n    'img',\n    'input',\n    'keygen',\n    'link',\n    'menuitem',\n    'meta',\n    'param',\n    'source',\n    'track',\n    'wbr'\n];\nexport var conf = {\n    wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\$\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\s]+)/g,\n    comments: {\n        blockComment: ['<!--', '-->']\n    },\n    brackets: [\n        ['<!--', '-->'],\n        ['<', '>'],\n        ['{', '}'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" }\n    ],\n    surroundingPairs: [\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" },\n        { open: '<', close: '>' }\n    ],\n    onEnterRules: [\n        {\n            beforeText: new RegExp(\"<(?!(?:\" + EMPTY_ELEMENTS.join('|') + \"))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$\", 'i'),\n            afterText: /^<\\/(\\w[\\w\\d]*)\\s*>$/i,\n            action: {\n                indentAction: languages.IndentAction.IndentOutdent\n            }\n        },\n        {\n            beforeText: new RegExp(\"<(?!(?:\" + EMPTY_ELEMENTS.join('|') + \"))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$\", 'i'),\n            action: { indentAction: languages.IndentAction.Indent }\n        }\n    ]\n};\nexport var language = {\n    defaultToken: '',\n    tokenPostfix: '',\n    // ignoreCase: true,\n    // The main tokenizer for our languages\n    tokenizer: {\n        root: [\n            [/@@@@/],\n            [/@[^@]/, { token: '@rematch', switchTo: '@razorInSimpleState.root' }],\n            [/<!DOCTYPE/, 'metatag.html', '@doctype'],\n            [/<!--/, 'comment.html', '@comment'],\n            [/(<)([\\w\\-]+)(\\/>)/, ['delimiter.html', 'tag.html', 'delimiter.html']],\n            [/(<)(script)/, ['delimiter.html', { token: 'tag.html', next: '@script' }]],\n            [/(<)(style)/, ['delimiter.html', { token: 'tag.html', next: '@style' }]],\n            [/(<)([:\\w\\-]+)/, ['delimiter.html', { token: 'tag.html', next: '@otherTag' }]],\n            [/(<\\/)([\\w\\-]+)/, ['delimiter.html', { token: 'tag.html', next: '@otherTag' }]],\n            [/</, 'delimiter.html'],\n            [/[ \\t\\r\\n]+/],\n            [/[^<@]+/] // text\n        ],\n        doctype: [\n            [/@[^@]/, { token: '@rematch', switchTo: '@razorInSimpleState.comment' }],\n            [/[^>]+/, 'metatag.content.html'],\n            [/>/, 'metatag.html', '@pop']\n        ],\n        comment: [\n            [/@[^@]/, { token: '@rematch', switchTo: '@razorInSimpleState.comment' }],\n            [/-->/, 'comment.html', '@pop'],\n            [/[^-]+/, 'comment.content.html'],\n            [/./, 'comment.content.html']\n        ],\n        otherTag: [\n            [/@[^@]/, { token: '@rematch', switchTo: '@razorInSimpleState.otherTag' }],\n            [/\\/?>/, 'delimiter.html', '@pop'],\n            [/\"([^\"]*)\"/, 'attribute.value'],\n            [/'([^']*)'/, 'attribute.value'],\n            [/[\\w\\-]+/, 'attribute.name'],\n            [/=/, 'delimiter'],\n            [/[ \\t\\r\\n]+/] // whitespace\n        ],\n        // -- BEGIN <script> tags handling\n        // After <script\n        script: [\n            [/@[^@]/, { token: '@rematch', switchTo: '@razorInSimpleState.script' }],\n            [/type/, 'attribute.name', '@scriptAfterType'],\n            [/\"([^\"]*)\"/, 'attribute.value'],\n            [/'([^']*)'/, 'attribute.value'],\n            [/[\\w\\-]+/, 'attribute.name'],\n            [/=/, 'delimiter'],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@scriptEmbedded.text/javascript',\n                    nextEmbedded: 'text/javascript'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [\n                /(<\\/)(script\\s*)(>)/,\n                ['delimiter.html', 'tag.html', { token: 'delimiter.html', next: '@pop' }]\n            ]\n        ],\n        // After <script ... type\n        scriptAfterType: [\n            [\n                /@[^@]/,\n                {\n                    token: '@rematch',\n                    switchTo: '@razorInSimpleState.scriptAfterType'\n                }\n            ],\n            [/=/, 'delimiter', '@scriptAfterTypeEquals'],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@scriptEmbedded.text/javascript',\n                    nextEmbedded: 'text/javascript'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/script\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        // After <script ... type =\n        scriptAfterTypeEquals: [\n            [\n                /@[^@]/,\n                {\n                    token: '@rematch',\n                    switchTo: '@razorInSimpleState.scriptAfterTypeEquals'\n                }\n            ],\n            [\n                /\"([^\"]*)\"/,\n                {\n                    token: 'attribute.value',\n                    switchTo: '@scriptWithCustomType.$1'\n                }\n            ],\n            [\n                /'([^']*)'/,\n                {\n                    token: 'attribute.value',\n                    switchTo: '@scriptWithCustomType.$1'\n                }\n            ],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@scriptEmbedded.text/javascript',\n                    nextEmbedded: 'text/javascript'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/script\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        // After <script ... type = $S2\n        scriptWithCustomType: [\n            [\n                /@[^@]/,\n                {\n                    token: '@rematch',\n                    switchTo: '@razorInSimpleState.scriptWithCustomType.$S2'\n                }\n            ],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@scriptEmbedded.$S2',\n                    nextEmbedded: '$S2'\n                }\n            ],\n            [/\"([^\"]*)\"/, 'attribute.value'],\n            [/'([^']*)'/, 'attribute.value'],\n            [/[\\w\\-]+/, 'attribute.name'],\n            [/=/, 'delimiter'],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/script\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        scriptEmbedded: [\n            [\n                /@[^@]/,\n                {\n                    token: '@rematch',\n                    switchTo: '@razorInEmbeddedState.scriptEmbedded.$S2',\n                    nextEmbedded: '@pop'\n                }\n            ],\n            [/<\\/script/, { token: '@rematch', next: '@pop', nextEmbedded: '@pop' }]\n        ],\n        // -- END <script> tags handling\n        // -- BEGIN <style> tags handling\n        // After <style\n        style: [\n            [/@[^@]/, { token: '@rematch', switchTo: '@razorInSimpleState.style' }],\n            [/type/, 'attribute.name', '@styleAfterType'],\n            [/\"([^\"]*)\"/, 'attribute.value'],\n            [/'([^']*)'/, 'attribute.value'],\n            [/[\\w\\-]+/, 'attribute.name'],\n            [/=/, 'delimiter'],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@styleEmbedded.text/css',\n                    nextEmbedded: 'text/css'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [\n                /(<\\/)(style\\s*)(>)/,\n                ['delimiter.html', 'tag.html', { token: 'delimiter.html', next: '@pop' }]\n            ]\n        ],\n        // After <style ... type\n        styleAfterType: [\n            [\n                /@[^@]/,\n                {\n                    token: '@rematch',\n                    switchTo: '@razorInSimpleState.styleAfterType'\n                }\n            ],\n            [/=/, 'delimiter', '@styleAfterTypeEquals'],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@styleEmbedded.text/css',\n                    nextEmbedded: 'text/css'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/style\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        // After <style ... type =\n        styleAfterTypeEquals: [\n            [\n                /@[^@]/,\n                {\n                    token: '@rematch',\n                    switchTo: '@razorInSimpleState.styleAfterTypeEquals'\n                }\n            ],\n            [\n                /\"([^\"]*)\"/,\n                {\n                    token: 'attribute.value',\n                    switchTo: '@styleWithCustomType.$1'\n                }\n            ],\n            [\n                /'([^']*)'/,\n                {\n                    token: 'attribute.value',\n                    switchTo: '@styleWithCustomType.$1'\n                }\n            ],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@styleEmbedded.text/css',\n                    nextEmbedded: 'text/css'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/style\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        // After <style ... type = $S2\n        styleWithCustomType: [\n            [\n                /@[^@]/,\n                {\n                    token: '@rematch',\n                    switchTo: '@razorInSimpleState.styleWithCustomType.$S2'\n                }\n            ],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@styleEmbedded.$S2',\n                    nextEmbedded: '$S2'\n                }\n            ],\n            [/\"([^\"]*)\"/, 'attribute.value'],\n            [/'([^']*)'/, 'attribute.value'],\n            [/[\\w\\-]+/, 'attribute.name'],\n            [/=/, 'delimiter'],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/style\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        styleEmbedded: [\n            [\n                /@[^@]/,\n                {\n                    token: '@rematch',\n                    switchTo: '@razorInEmbeddedState.styleEmbedded.$S2',\n                    nextEmbedded: '@pop'\n                }\n            ],\n            [/<\\/style/, { token: '@rematch', next: '@pop', nextEmbedded: '@pop' }]\n        ],\n        // -- END <style> tags handling\n        razorInSimpleState: [\n            [/@\\*/, 'comment.cs', '@razorBlockCommentTopLevel'],\n            [/@[{(]/, 'metatag.cs', '@razorRootTopLevel'],\n            [/(@)(\\s*[\\w]+)/, ['metatag.cs', { token: 'identifier.cs', switchTo: '@$S2.$S3' }]],\n            [/[})]/, { token: 'metatag.cs', switchTo: '@$S2.$S3' }],\n            [/\\*@/, { token: 'comment.cs', switchTo: '@$S2.$S3' }]\n        ],\n        razorInEmbeddedState: [\n            [/@\\*/, 'comment.cs', '@razorBlockCommentTopLevel'],\n            [/@[{(]/, 'metatag.cs', '@razorRootTopLevel'],\n            [\n                /(@)(\\s*[\\w]+)/,\n                [\n                    'metatag.cs',\n                    {\n                        token: 'identifier.cs',\n                        switchTo: '@$S2.$S3',\n                        nextEmbedded: '$S3'\n                    }\n                ]\n            ],\n            [\n                /[})]/,\n                {\n                    token: 'metatag.cs',\n                    switchTo: '@$S2.$S3',\n                    nextEmbedded: '$S3'\n                }\n            ],\n            [\n                /\\*@/,\n                {\n                    token: 'comment.cs',\n                    switchTo: '@$S2.$S3',\n                    nextEmbedded: '$S3'\n                }\n            ]\n        ],\n        razorBlockCommentTopLevel: [\n            [/\\*@/, '@rematch', '@pop'],\n            [/[^*]+/, 'comment.cs'],\n            [/./, 'comment.cs']\n        ],\n        razorBlockComment: [\n            [/\\*@/, 'comment.cs', '@pop'],\n            [/[^*]+/, 'comment.cs'],\n            [/./, 'comment.cs']\n        ],\n        razorRootTopLevel: [\n            [/\\{/, 'delimiter.bracket.cs', '@razorRoot'],\n            [/\\(/, 'delimiter.parenthesis.cs', '@razorRoot'],\n            [/[})]/, '@rematch', '@pop'],\n            { include: 'razorCommon' }\n        ],\n        razorRoot: [\n            [/\\{/, 'delimiter.bracket.cs', '@razorRoot'],\n            [/\\(/, 'delimiter.parenthesis.cs', '@razorRoot'],\n            [/\\}/, 'delimiter.bracket.cs', '@pop'],\n            [/\\)/, 'delimiter.parenthesis.cs', '@pop'],\n            { include: 'razorCommon' }\n        ],\n        razorCommon: [\n            [\n                /[a-zA-Z_]\\w*/,\n                {\n                    cases: {\n                        '@razorKeywords': { token: 'keyword.cs' },\n                        '@default': 'identifier.cs'\n                    }\n                }\n            ],\n            // brackets\n            [/[\\[\\]]/, 'delimiter.array.cs'],\n            // whitespace\n            [/[ \\t\\r\\n]+/],\n            // comments\n            [/\\/\\/.*$/, 'comment.cs'],\n            [/@\\*/, 'comment.cs', '@razorBlockComment'],\n            // strings\n            [/\"([^\"]*)\"/, 'string.cs'],\n            [/'([^']*)'/, 'string.cs'],\n            // simple html\n            [/(<)([\\w\\-]+)(\\/>)/, ['delimiter.html', 'tag.html', 'delimiter.html']],\n            [/(<)([\\w\\-]+)(>)/, ['delimiter.html', 'tag.html', 'delimiter.html']],\n            [/(<\\/)([\\w\\-]+)(>)/, ['delimiter.html', 'tag.html', 'delimiter.html']],\n            // delimiters\n            [/[\\+\\-\\*\\%\\&\\|\\^\\~\\!\\=\\<\\>\\/\\?\\;\\:\\.\\,]/, 'delimiter.cs'],\n            // numbers\n            [/\\d*\\d+[eE]([\\-+]?\\d+)?/, 'number.float.cs'],\n            [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, 'number.float.cs'],\n            [/0[xX][0-9a-fA-F']*[0-9a-fA-F]/, 'number.hex.cs'],\n            [/0[0-7']*[0-7]/, 'number.octal.cs'],\n            [/0[bB][0-1']*[0-1]/, 'number.binary.cs'],\n            [/\\d[\\d']*/, 'number.cs'],\n            [/\\d/, 'number.cs']\n        ]\n    },\n    razorKeywords: [\n        'abstract',\n        'as',\n        'async',\n        'await',\n        'base',\n        'bool',\n        'break',\n        'by',\n        'byte',\n        'case',\n        'catch',\n        'char',\n        'checked',\n        'class',\n        'const',\n        'continue',\n        'decimal',\n        'default',\n        'delegate',\n        'do',\n        'double',\n        'descending',\n        'explicit',\n        'event',\n        'extern',\n        'else',\n        'enum',\n        'false',\n        'finally',\n        'fixed',\n        'float',\n        'for',\n        'foreach',\n        'from',\n        'goto',\n        'group',\n        'if',\n        'implicit',\n        'in',\n        'int',\n        'interface',\n        'internal',\n        'into',\n        'is',\n        'lock',\n        'long',\n        'nameof',\n        'new',\n        'null',\n        'namespace',\n        'object',\n        'operator',\n        'out',\n        'override',\n        'orderby',\n        'params',\n        'private',\n        'protected',\n        'public',\n        'readonly',\n        'ref',\n        'return',\n        'switch',\n        'struct',\n        'sbyte',\n        'sealed',\n        'short',\n        'sizeof',\n        'stackalloc',\n        'static',\n        'string',\n        'select',\n        'this',\n        'throw',\n        'true',\n        'try',\n        'typeof',\n        'uint',\n        'ulong',\n        'unchecked',\n        'unsafe',\n        'ushort',\n        'using',\n        'var',\n        'virtual',\n        'volatile',\n        'void',\n        'when',\n        'while',\n        'where',\n        'yield',\n        'model',\n        'inject' // Razor specific\n    ],\n    escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/\n};\n"], "mappings": ";;;;;;;AAKA,IAAI,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACO,IAAI,OAAO;AAAA,EACd,aAAa;AAAA,EACb,UAAU;AAAA,IACN,cAAc,CAAC,QAAQ,KAAK;AAAA,EAChC;AAAA,EACA,UAAU;AAAA,IACN,CAAC,QAAQ,KAAK;AAAA,IACd,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,cAAc;AAAA,IACV;AAAA,MACI,YAAY,IAAI,OAAO,YAAY,eAAe,KAAK,GAAG,IAAI,wCAAwC,GAAG;AAAA,MACzG,WAAW;AAAA,MACX,QAAQ;AAAA,QACJ,cAAc,UAAU,aAAa;AAAA,MACzC;AAAA,IACJ;AAAA,IACA;AAAA,MACI,YAAY,IAAI,OAAO,YAAY,eAAe,KAAK,GAAG,IAAI,wCAAwC,GAAG;AAAA,MACzG,QAAQ,EAAE,cAAc,UAAU,aAAa,OAAO;AAAA,IAC1D;AAAA,EACJ;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA;AAAA;AAAA,EAGd,WAAW;AAAA,IACP,MAAM;AAAA,MACF,CAAC,MAAM;AAAA,MACP,CAAC,SAAS,EAAE,OAAO,YAAY,UAAU,2BAA2B,CAAC;AAAA,MACrE,CAAC,aAAa,gBAAgB,UAAU;AAAA,MACxC,CAAC,QAAQ,gBAAgB,UAAU;AAAA,MACnC,CAAC,qBAAqB,CAAC,kBAAkB,YAAY,gBAAgB,CAAC;AAAA,MACtE,CAAC,eAAe,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,UAAU,CAAC,CAAC;AAAA,MAC1E,CAAC,cAAc,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,SAAS,CAAC,CAAC;AAAA,MACxE,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,YAAY,CAAC,CAAC;AAAA,MAC9E,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,YAAY,CAAC,CAAC;AAAA,MAC/E,CAAC,KAAK,gBAAgB;AAAA,MACtB,CAAC,YAAY;AAAA,MACb,CAAC,QAAQ;AAAA;AAAA,IACb;AAAA,IACA,SAAS;AAAA,MACL,CAAC,SAAS,EAAE,OAAO,YAAY,UAAU,8BAA8B,CAAC;AAAA,MACxE,CAAC,SAAS,sBAAsB;AAAA,MAChC,CAAC,KAAK,gBAAgB,MAAM;AAAA,IAChC;AAAA,IACA,SAAS;AAAA,MACL,CAAC,SAAS,EAAE,OAAO,YAAY,UAAU,8BAA8B,CAAC;AAAA,MACxE,CAAC,OAAO,gBAAgB,MAAM;AAAA,MAC9B,CAAC,SAAS,sBAAsB;AAAA,MAChC,CAAC,KAAK,sBAAsB;AAAA,IAChC;AAAA,IACA,UAAU;AAAA,MACN,CAAC,SAAS,EAAE,OAAO,YAAY,UAAU,+BAA+B,CAAC;AAAA,MACzE,CAAC,QAAQ,kBAAkB,MAAM;AAAA,MACjC,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY;AAAA;AAAA,IACjB;AAAA;AAAA;AAAA,IAGA,QAAQ;AAAA,MACJ,CAAC,SAAS,EAAE,OAAO,YAAY,UAAU,6BAA6B,CAAC;AAAA,MACvE,CAAC,QAAQ,kBAAkB,kBAAkB;AAAA,MAC7C,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb;AAAA,QACI;AAAA,QACA,CAAC,kBAAkB,YAAY,EAAE,OAAO,kBAAkB,MAAM,OAAO,CAAC;AAAA,MAC5E;AAAA,IACJ;AAAA;AAAA,IAEA,iBAAiB;AAAA,MACb;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA,CAAC,KAAK,aAAa,wBAAwB;AAAA,MAC3C;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACzD;AAAA;AAAA,IAEA,uBAAuB;AAAA,MACnB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACzD;AAAA;AAAA,IAEA,sBAAsB;AAAA,MAClB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY;AAAA,MACb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACzD;AAAA,IACA,gBAAgB;AAAA,MACZ;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,UACV,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,aAAa,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,IAC3E;AAAA;AAAA;AAAA;AAAA,IAIA,OAAO;AAAA,MACH,CAAC,SAAS,EAAE,OAAO,YAAY,UAAU,4BAA4B,CAAC;AAAA,MACtE,CAAC,QAAQ,kBAAkB,iBAAiB;AAAA,MAC5C,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb;AAAA,QACI;AAAA,QACA,CAAC,kBAAkB,YAAY,EAAE,OAAO,kBAAkB,MAAM,OAAO,CAAC;AAAA,MAC5E;AAAA,IACJ;AAAA;AAAA,IAEA,gBAAgB;AAAA,MACZ;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA,CAAC,KAAK,aAAa,uBAAuB;AAAA,MAC1C;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACxD;AAAA;AAAA,IAEA,sBAAsB;AAAA,MAClB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACxD;AAAA;AAAA,IAEA,qBAAqB;AAAA,MACjB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY;AAAA,MACb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACxD;AAAA,IACA,eAAe;AAAA,MACX;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,UACV,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,IAC1E;AAAA;AAAA,IAEA,oBAAoB;AAAA,MAChB,CAAC,OAAO,cAAc,4BAA4B;AAAA,MAClD,CAAC,SAAS,cAAc,oBAAoB;AAAA,MAC5C,CAAC,iBAAiB,CAAC,cAAc,EAAE,OAAO,iBAAiB,UAAU,WAAW,CAAC,CAAC;AAAA,MAClF,CAAC,QAAQ,EAAE,OAAO,cAAc,UAAU,WAAW,CAAC;AAAA,MACtD,CAAC,OAAO,EAAE,OAAO,cAAc,UAAU,WAAW,CAAC;AAAA,IACzD;AAAA,IACA,sBAAsB;AAAA,MAClB,CAAC,OAAO,cAAc,4BAA4B;AAAA,MAClD,CAAC,SAAS,cAAc,oBAAoB;AAAA,MAC5C;AAAA,QACI;AAAA,QACA;AAAA,UACI;AAAA,UACA;AAAA,YACI,OAAO;AAAA,YACP,UAAU;AAAA,YACV,cAAc;AAAA,UAClB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,UACV,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,UACV,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,2BAA2B;AAAA,MACvB,CAAC,OAAO,YAAY,MAAM;AAAA,MAC1B,CAAC,SAAS,YAAY;AAAA,MACtB,CAAC,KAAK,YAAY;AAAA,IACtB;AAAA,IACA,mBAAmB;AAAA,MACf,CAAC,OAAO,cAAc,MAAM;AAAA,MAC5B,CAAC,SAAS,YAAY;AAAA,MACtB,CAAC,KAAK,YAAY;AAAA,IACtB;AAAA,IACA,mBAAmB;AAAA,MACf,CAAC,MAAM,wBAAwB,YAAY;AAAA,MAC3C,CAAC,MAAM,4BAA4B,YAAY;AAAA,MAC/C,CAAC,QAAQ,YAAY,MAAM;AAAA,MAC3B,EAAE,SAAS,cAAc;AAAA,IAC7B;AAAA,IACA,WAAW;AAAA,MACP,CAAC,MAAM,wBAAwB,YAAY;AAAA,MAC3C,CAAC,MAAM,4BAA4B,YAAY;AAAA,MAC/C,CAAC,MAAM,wBAAwB,MAAM;AAAA,MACrC,CAAC,MAAM,4BAA4B,MAAM;AAAA,MACzC,EAAE,SAAS,cAAc;AAAA,IAC7B;AAAA,IACA,aAAa;AAAA,MACT;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,kBAAkB,EAAE,OAAO,aAAa;AAAA,YACxC,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,CAAC,UAAU,oBAAoB;AAAA;AAAA,MAE/B,CAAC,YAAY;AAAA;AAAA,MAEb,CAAC,WAAW,YAAY;AAAA,MACxB,CAAC,OAAO,cAAc,oBAAoB;AAAA;AAAA,MAE1C,CAAC,aAAa,WAAW;AAAA,MACzB,CAAC,aAAa,WAAW;AAAA;AAAA,MAEzB,CAAC,qBAAqB,CAAC,kBAAkB,YAAY,gBAAgB,CAAC;AAAA,MACtE,CAAC,mBAAmB,CAAC,kBAAkB,YAAY,gBAAgB,CAAC;AAAA,MACpE,CAAC,qBAAqB,CAAC,kBAAkB,YAAY,gBAAgB,CAAC;AAAA;AAAA,MAEtE,CAAC,0CAA0C,cAAc;AAAA;AAAA,MAEzD,CAAC,0BAA0B,iBAAiB;AAAA,MAC5C,CAAC,4BAA4B,iBAAiB;AAAA,MAC9C,CAAC,iCAAiC,eAAe;AAAA,MACjD,CAAC,iBAAiB,iBAAiB;AAAA,MACnC,CAAC,qBAAqB,kBAAkB;AAAA,MACxC,CAAC,YAAY,WAAW;AAAA,MACxB,CAAC,MAAM,WAAW;AAAA,IACtB;AAAA,EACJ;AAAA,EACA,eAAe;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,EACJ;AAAA,EACA,SAAS;AACb;", "names": []}