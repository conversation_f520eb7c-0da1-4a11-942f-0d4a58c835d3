import {
  $P,
  $_,
  Bf,
  <PERSON>_,
  Ff,
  HP,
  K_,
  MP,
  Mf,
  Nf,
  QO,
  S_,
  Sf,
  Vf,
  Zve,
  aye,
  ed,
  iye,
  md,
  qf,
  uye,
  x_,
  zP
} from "./chunk-52BNFIGJ.js";
import "./chunk-GFT2G5UO.js";
export {
  Zve as Boot,
  QO as DomEditor,
  Bf as SlateEditor,
  Sf as SlateElement,
  Ff as SlateLocation,
  Nf as SlateNode,
  Mf as SlatePath,
  Vf as SlatePoint,
  qf as SlateRange,
  ed as SlateText,
  md as SlateTransforms,
  $_ as Toolbar,
  iye as createEditor,
  aye as createToolbar,
  K_ as createUploader,
  uye as default,
  C_ as genModalButtonElems,
  S_ as genModalInputElems,
  x_ as genModalTextareaElems,
  MP as i18nAddResources,
  zP as i18nChangeLanguage,
  $P as i18nGetResources,
  HP as t
};
//# sourceMappingURL=@wangeditor_editor.js.map
