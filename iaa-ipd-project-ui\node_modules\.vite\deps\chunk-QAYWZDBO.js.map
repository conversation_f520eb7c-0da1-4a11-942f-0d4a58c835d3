{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/typescript/typescript.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { languages } from '../fillers/monaco-editor-core.js';\nexport var conf = {\n    wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n    comments: {\n        lineComment: '//',\n        blockComment: ['/*', '*/']\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')']\n    ],\n    onEnterRules: [\n        {\n            // e.g. /** | */\n            beforeText: /^\\s*\\/\\*\\*(?!\\/)([^\\*]|\\*(?!\\/))*$/,\n            afterText: /^\\s*\\*\\/$/,\n            action: {\n                indentAction: languages.IndentAction.IndentOutdent,\n                appendText: ' * '\n            }\n        },\n        {\n            // e.g. /** ...|\n            beforeText: /^\\s*\\/\\*\\*(?!\\/)([^\\*]|\\*(?!\\/))*$/,\n            action: {\n                indentAction: languages.IndentAction.None,\n                appendText: ' * '\n            }\n        },\n        {\n            // e.g.  * ...|\n            beforeText: /^(\\t|(\\ \\ ))*\\ \\*(\\ ([^\\*]|\\*(?!\\/))*)?$/,\n            action: {\n                indentAction: languages.IndentAction.None,\n                appendText: '* '\n            }\n        },\n        {\n            // e.g.  */|\n            beforeText: /^(\\t|(\\ \\ ))*\\ \\*\\/\\s*$/,\n            action: {\n                indentAction: languages.IndentAction.None,\n                removeText: 1\n            }\n        }\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"', notIn: ['string'] },\n        { open: \"'\", close: \"'\", notIn: ['string', 'comment'] },\n        { open: '`', close: '`', notIn: ['string', 'comment'] },\n        { open: '/**', close: ' */', notIn: ['string'] }\n    ],\n    folding: {\n        markers: {\n            start: new RegExp('^\\\\s*//\\\\s*#?region\\\\b'),\n            end: new RegExp('^\\\\s*//\\\\s*#?endregion\\\\b')\n        }\n    }\n};\nexport var language = {\n    // Set defaultToken to invalid to see what you do not tokenize yet\n    defaultToken: 'invalid',\n    tokenPostfix: '.ts',\n    keywords: [\n        // Should match the keys of textToKeywordObj in\n        // https://github.com/microsoft/TypeScript/blob/master/src/compiler/scanner.ts\n        'abstract',\n        'any',\n        'as',\n        'asserts',\n        'bigint',\n        'boolean',\n        'break',\n        'case',\n        'catch',\n        'class',\n        'continue',\n        'const',\n        'constructor',\n        'debugger',\n        'declare',\n        'default',\n        'delete',\n        'do',\n        'else',\n        'enum',\n        'export',\n        'extends',\n        'false',\n        'finally',\n        'for',\n        'from',\n        'function',\n        'get',\n        'if',\n        'implements',\n        'import',\n        'in',\n        'infer',\n        'instanceof',\n        'interface',\n        'is',\n        'keyof',\n        'let',\n        'module',\n        'namespace',\n        'never',\n        'new',\n        'null',\n        'number',\n        'object',\n        'package',\n        'private',\n        'protected',\n        'public',\n        'override',\n        'readonly',\n        'require',\n        'global',\n        'return',\n        'set',\n        'static',\n        'string',\n        'super',\n        'switch',\n        'symbol',\n        'this',\n        'throw',\n        'true',\n        'try',\n        'type',\n        'typeof',\n        'undefined',\n        'unique',\n        'unknown',\n        'var',\n        'void',\n        'while',\n        'with',\n        'yield',\n        'async',\n        'await',\n        'of'\n    ],\n    operators: [\n        '<=',\n        '>=',\n        '==',\n        '!=',\n        '===',\n        '!==',\n        '=>',\n        '+',\n        '-',\n        '**',\n        '*',\n        '/',\n        '%',\n        '++',\n        '--',\n        '<<',\n        '</',\n        '>>',\n        '>>>',\n        '&',\n        '|',\n        '^',\n        '!',\n        '~',\n        '&&',\n        '||',\n        '??',\n        '?',\n        ':',\n        '=',\n        '+=',\n        '-=',\n        '*=',\n        '**=',\n        '/=',\n        '%=',\n        '<<=',\n        '>>=',\n        '>>>=',\n        '&=',\n        '|=',\n        '^=',\n        '@'\n    ],\n    // we include these common regular expressions\n    symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n    escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n    digits: /\\d+(_+\\d+)*/,\n    octaldigits: /[0-7]+(_+[0-7]+)*/,\n    binarydigits: /[0-1]+(_+[0-1]+)*/,\n    hexdigits: /[[0-9a-fA-F]+(_+[0-9a-fA-F]+)*/,\n    regexpctl: /[(){}\\[\\]\\$\\^|\\-*+?\\.]/,\n    regexpesc: /\\\\(?:[bBdDfnrstvwWn0\\\\\\/]|@regexpctl|c[A-Z]|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4})/,\n    // The main tokenizer for our languages\n    tokenizer: {\n        root: [[/[{}]/, 'delimiter.bracket'], { include: 'common' }],\n        common: [\n            // identifiers and keywords\n            [\n                /[a-z_$][\\w$]*/,\n                {\n                    cases: {\n                        '@keywords': 'keyword',\n                        '@default': 'identifier'\n                    }\n                }\n            ],\n            [/[A-Z][\\w\\$]*/, 'type.identifier'],\n            // [/[A-Z][\\w\\$]*/, 'identifier'],\n            // whitespace\n            { include: '@whitespace' },\n            // regular expression: ensure it is terminated before beginning (otherwise it is an opeator)\n            [\n                /\\/(?=([^\\\\\\/]|\\\\.)+\\/([dgimsuy]*)(\\s*)(\\.|;|,|\\)|\\]|\\}|$))/,\n                { token: 'regexp', bracket: '@open', next: '@regexp' }\n            ],\n            // delimiters and operators\n            [/[()\\[\\]]/, '@brackets'],\n            [/[<>](?!@symbols)/, '@brackets'],\n            [/!(?=([^=]|$))/, 'delimiter'],\n            [\n                /@symbols/,\n                {\n                    cases: {\n                        '@operators': 'delimiter',\n                        '@default': ''\n                    }\n                }\n            ],\n            // numbers\n            [/(@digits)[eE]([\\-+]?(@digits))?/, 'number.float'],\n            [/(@digits)\\.(@digits)([eE][\\-+]?(@digits))?/, 'number.float'],\n            [/0[xX](@hexdigits)n?/, 'number.hex'],\n            [/0[oO]?(@octaldigits)n?/, 'number.octal'],\n            [/0[bB](@binarydigits)n?/, 'number.binary'],\n            [/(@digits)n?/, 'number'],\n            // delimiter: after number because of .\\d floats\n            [/[;,.]/, 'delimiter'],\n            // strings\n            [/\"([^\"\\\\]|\\\\.)*$/, 'string.invalid'],\n            [/'([^'\\\\]|\\\\.)*$/, 'string.invalid'],\n            [/\"/, 'string', '@string_double'],\n            [/'/, 'string', '@string_single'],\n            [/`/, 'string', '@string_backtick']\n        ],\n        whitespace: [\n            [/[ \\t\\r\\n]+/, ''],\n            [/\\/\\*\\*(?!\\/)/, 'comment.doc', '@jsdoc'],\n            [/\\/\\*/, 'comment', '@comment'],\n            [/\\/\\/.*$/, 'comment']\n        ],\n        comment: [\n            [/[^\\/*]+/, 'comment'],\n            [/\\*\\//, 'comment', '@pop'],\n            [/[\\/*]/, 'comment']\n        ],\n        jsdoc: [\n            [/[^\\/*]+/, 'comment.doc'],\n            [/\\*\\//, 'comment.doc', '@pop'],\n            [/[\\/*]/, 'comment.doc']\n        ],\n        // We match regular expression quite precisely\n        regexp: [\n            [\n                /(\\{)(\\d+(?:,\\d*)?)(\\})/,\n                ['regexp.escape.control', 'regexp.escape.control', 'regexp.escape.control']\n            ],\n            [\n                /(\\[)(\\^?)(?=(?:[^\\]\\\\\\/]|\\\\.)+)/,\n                ['regexp.escape.control', { token: 'regexp.escape.control', next: '@regexrange' }]\n            ],\n            [/(\\()(\\?:|\\?=|\\?!)/, ['regexp.escape.control', 'regexp.escape.control']],\n            [/[()]/, 'regexp.escape.control'],\n            [/@regexpctl/, 'regexp.escape.control'],\n            [/[^\\\\\\/]/, 'regexp'],\n            [/@regexpesc/, 'regexp.escape'],\n            [/\\\\\\./, 'regexp.invalid'],\n            [/(\\/)([dgimsuy]*)/, [{ token: 'regexp', bracket: '@close', next: '@pop' }, 'keyword.other']]\n        ],\n        regexrange: [\n            [/-/, 'regexp.escape.control'],\n            [/\\^/, 'regexp.invalid'],\n            [/@regexpesc/, 'regexp.escape'],\n            [/[^\\]]/, 'regexp'],\n            [\n                /\\]/,\n                {\n                    token: 'regexp.escape.control',\n                    next: '@pop',\n                    bracket: '@close'\n                }\n            ]\n        ],\n        string_double: [\n            [/[^\\\\\"]+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/\\\\./, 'string.escape.invalid'],\n            [/\"/, 'string', '@pop']\n        ],\n        string_single: [\n            [/[^\\\\']+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/\\\\./, 'string.escape.invalid'],\n            [/'/, 'string', '@pop']\n        ],\n        string_backtick: [\n            [/\\$\\{/, { token: 'delimiter.bracket', next: '@bracketCounting' }],\n            [/[^\\\\`$]+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/\\\\./, 'string.escape.invalid'],\n            [/`/, 'string', '@pop']\n        ],\n        bracketCounting: [\n            [/\\{/, 'delimiter.bracket', '@bracketCounting'],\n            [/\\}/, 'delimiter.bracket', '@pop'],\n            { include: 'common' }\n        ]\n    }\n};\n"], "mappings": ";;;;;AAKO,IAAI,OAAO;AAAA,EACd,aAAa;AAAA,EACb,UAAU;AAAA,IACN,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC7B;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,cAAc;AAAA,IACV;AAAA;AAAA,MAEI,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,QAAQ;AAAA,QACJ,cAAc,UAAU,aAAa;AAAA,QACrC,YAAY;AAAA,MAChB;AAAA,IACJ;AAAA,IACA;AAAA;AAAA,MAEI,YAAY;AAAA,MACZ,QAAQ;AAAA,QACJ,cAAc,UAAU,aAAa;AAAA,QACrC,YAAY;AAAA,MAChB;AAAA,IACJ;AAAA,IACA;AAAA;AAAA,MAEI,YAAY;AAAA,MACZ,QAAQ;AAAA,QACJ,cAAc,UAAU,aAAa;AAAA,QACrC,YAAY;AAAA,MAChB;AAAA,IACJ;AAAA,IACA;AAAA;AAAA,MAEI,YAAY;AAAA,MACZ,QAAQ;AAAA,QACJ,cAAc,UAAU,aAAa;AAAA,QACrC,YAAY;AAAA,MAChB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,OAAO,OAAO,OAAO,OAAO,CAAC,QAAQ,EAAE;AAAA,EACnD;AAAA,EACA,SAAS;AAAA,IACL,SAAS;AAAA,MACL,OAAO,IAAI,OAAO,wBAAwB;AAAA,MAC1C,KAAK,IAAI,OAAO,2BAA2B;AAAA,IAC/C;AAAA,EACJ;AACJ;AACO,IAAI,WAAW;AAAA;AAAA,EAElB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA;AAAA;AAAA,IAGN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,WAAW;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA;AAAA,EAEA,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,cAAc;AAAA,EACd,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA;AAAA,EAEX,WAAW;AAAA,IACP,MAAM,CAAC,CAAC,QAAQ,mBAAmB,GAAG,EAAE,SAAS,SAAS,CAAC;AAAA,IAC3D,QAAQ;AAAA;AAAA,MAEJ;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,aAAa;AAAA,YACb,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,CAAC,gBAAgB,iBAAiB;AAAA;AAAA;AAAA,MAGlC,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB;AAAA,QACI;AAAA,QACA,EAAE,OAAO,UAAU,SAAS,SAAS,MAAM,UAAU;AAAA,MACzD;AAAA;AAAA,MAEA,CAAC,YAAY,WAAW;AAAA,MACxB,CAAC,oBAAoB,WAAW;AAAA,MAChC,CAAC,iBAAiB,WAAW;AAAA,MAC7B;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,cAAc;AAAA,YACd,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,CAAC,mCAAmC,cAAc;AAAA,MAClD,CAAC,8CAA8C,cAAc;AAAA,MAC7D,CAAC,uBAAuB,YAAY;AAAA,MACpC,CAAC,0BAA0B,cAAc;AAAA,MACzC,CAAC,0BAA0B,eAAe;AAAA,MAC1C,CAAC,eAAe,QAAQ;AAAA;AAAA,MAExB,CAAC,SAAS,WAAW;AAAA;AAAA,MAErB,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,KAAK,UAAU,gBAAgB;AAAA,MAChC,CAAC,KAAK,UAAU,gBAAgB;AAAA,MAChC,CAAC,KAAK,UAAU,kBAAkB;AAAA,IACtC;AAAA,IACA,YAAY;AAAA,MACR,CAAC,cAAc,EAAE;AAAA,MACjB,CAAC,gBAAgB,eAAe,QAAQ;AAAA,MACxC,CAAC,QAAQ,WAAW,UAAU;AAAA,MAC9B,CAAC,WAAW,SAAS;AAAA,IACzB;AAAA,IACA,SAAS;AAAA,MACL,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,SAAS,SAAS;AAAA,IACvB;AAAA,IACA,OAAO;AAAA,MACH,CAAC,WAAW,aAAa;AAAA,MACzB,CAAC,QAAQ,eAAe,MAAM;AAAA,MAC9B,CAAC,SAAS,aAAa;AAAA,IAC3B;AAAA;AAAA,IAEA,QAAQ;AAAA,MACJ;AAAA,QACI;AAAA,QACA,CAAC,yBAAyB,yBAAyB,uBAAuB;AAAA,MAC9E;AAAA,MACA;AAAA,QACI;AAAA,QACA,CAAC,yBAAyB,EAAE,OAAO,yBAAyB,MAAM,cAAc,CAAC;AAAA,MACrF;AAAA,MACA,CAAC,qBAAqB,CAAC,yBAAyB,uBAAuB,CAAC;AAAA,MACxE,CAAC,QAAQ,uBAAuB;AAAA,MAChC,CAAC,cAAc,uBAAuB;AAAA,MACtC,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,cAAc,eAAe;AAAA,MAC9B,CAAC,QAAQ,gBAAgB;AAAA,MACzB,CAAC,oBAAoB,CAAC,EAAE,OAAO,UAAU,SAAS,UAAU,MAAM,OAAO,GAAG,eAAe,CAAC;AAAA,IAChG;AAAA,IACA,YAAY;AAAA,MACR,CAAC,KAAK,uBAAuB;AAAA,MAC7B,CAAC,MAAM,gBAAgB;AAAA,MACvB,CAAC,cAAc,eAAe;AAAA,MAC9B,CAAC,SAAS,QAAQ;AAAA,MAClB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,SAAS;AAAA,QACb;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,eAAe;AAAA,MACX,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,UAAU,MAAM;AAAA,IAC1B;AAAA,IACA,eAAe;AAAA,MACX,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,UAAU,MAAM;AAAA,IAC1B;AAAA,IACA,iBAAiB;AAAA,MACb,CAAC,QAAQ,EAAE,OAAO,qBAAqB,MAAM,mBAAmB,CAAC;AAAA,MACjE,CAAC,YAAY,QAAQ;AAAA,MACrB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,UAAU,MAAM;AAAA,IAC1B;AAAA,IACA,iBAAiB;AAAA,MACb,CAAC,MAAM,qBAAqB,kBAAkB;AAAA,MAC9C,CAAC,MAAM,qBAAqB,MAAM;AAAA,MAClC,EAAE,SAAS,SAAS;AAAA,IACxB;AAAA,EACJ;AACJ;", "names": []}