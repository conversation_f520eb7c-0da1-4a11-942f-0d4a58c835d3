Object.defineProperty(exports,"__esModule",{value:!0}),exports.VXETable=void 0,Object.defineProperty(exports,"VxeUI",{enumerable:!0,get:function(){return _core.VxeUI}}),exports.version=exports.validators=exports.use=exports.t=exports.setup=exports.setTheme=exports.setLanguage=exports.setIcon=exports.setI18n=exports.setConfig=exports.saveFile=exports.renderer=exports.readFile=exports.print=exports.modal=exports.menus=exports.log=exports.interceptor=exports.hooks=exports.globalResize=exports.globalEvents=exports.getTheme=exports.getIcon=exports.getI18n=exports.getConfig=exports.formats=exports.default=exports.config=exports.commands=exports.clipboard=exports._t=void 0;var _core=require("@vxe-ui/core"),_utils=require("./src/utils");let version=exports.version="4.13.41",iconPrefix=(_core.VxeUI.version=version,_core.VxeUI.tableVersion=version,_core.VxeUI.setConfig({emptyCell:"　",table:{fit:!0,showHeader:!0,animat:!0,delayHover:250,autoResize:!0,minHeight:144,resizeConfig:{},resizableConfig:{dragMode:"auto",showDragTip:!0,isSyncAutoHeight:!0,isSyncAutoWidth:!0,minHeight:18},radioConfig:{strict:!0},rowDragConfig:{showIcon:!0,animation:!0,showGuidesStatus:!0,showDragTip:!0},columnDragConfig:{showIcon:!0,animation:!0,showGuidesStatus:!0,showDragTip:!0},checkboxConfig:{strict:!0},tooltipConfig:{enterable:!0},validConfig:{showMessage:!0,autoClear:!0,autoPos:!0,message:"inline",msgMode:"single",theme:"beautify"},columnConfig:{maxFixedSize:4},cellConfig:{padding:!0},headerCellConfig:{height:"unset"},footerCellConfig:{height:"unset"},customConfig:{allowVisible:!0,allowResizable:!0,allowFixed:!0,allowSort:!0,showFooter:!0,placement:"top-right",storeOptions:{visible:!0,resizable:!0,sort:!0,fixed:!0},modalOptions:{showMaximize:!0,mask:!0,lockView:!0,resize:!0,escClosable:!0},drawerOptions:{mask:!0,lockView:!0,escClosable:!0,resize:!0}},sortConfig:{showIcon:!0,allowClear:!0,allowBtn:!0,iconLayout:"vertical"},filterConfig:{showIcon:!0},aggregateConfig:{padding:!0,rowField:"id",parentField:"_X_ROW_PARENT_KEY",childrenField:"_X_ROW_CHILDREN",mapChildrenField:"_X_ROW_CHILD_LIST",indent:20,showIcon:!0},treeConfig:{padding:!0,rowField:"id",parentField:"parentId",childrenField:"children",hasChildField:"hasChild",mapChildrenField:"_X_ROW_CHILD",indent:20,showIcon:!0},expandConfig:{showIcon:!0,mode:"fixed"},editConfig:{showIcon:!0,showAsterisk:!0,autoFocus:!0},importConfig:{_typeMaps:{csv:1,html:1,xml:1,txt:1}},exportConfig:{_typeMaps:{csv:1,html:1,xml:1,txt:1}},printConfig:{},mouseConfig:{extension:!0},keyboardConfig:{isAll:!0,isEsc:!0},areaConfig:{autoClear:!0,selectCellByHeader:!0,selectCellByBody:!0,extendDirection:{top:!0,left:!0,bottom:!0,right:!0}},clipConfig:{isCopy:!0,isCut:!0,isPaste:!0},fnrConfig:{isFind:!0,isReplace:!0},virtualXConfig:{gt:24,preSize:1,oSize:0},virtualYConfig:{gt:100,preSize:1,oSize:0},scrollbarConfig:{}},grid:{formConfig:{enabled:!0},pagerConfig:{enabled:!0},toolbarConfig:{enabled:!0},proxyConfig:{enabled:!0,autoLoad:!0,showResponseMsg:!0,showActiveMsg:!0,props:{list:null,result:"result",total:"page.total",message:"message"}}},toolbar:{}}),"vxe-table-icon-"),setTheme=(_core.VxeUI.setIcon({TABLE_SORT_ASC:iconPrefix+"caret-up",TABLE_SORT_DESC:iconPrefix+"caret-down",TABLE_FILTER_NONE:iconPrefix+"funnel",TABLE_FILTER_MATCH:iconPrefix+"funnel",TABLE_EDIT:iconPrefix+"edit",TABLE_TITLE_PREFIX:iconPrefix+"question-circle-fill",TABLE_TITLE_SUFFIX:iconPrefix+"question-circle-fill",TABLE_TREE_LOADED:iconPrefix+"spinner roll",TABLE_TREE_OPEN:iconPrefix+"caret-right rotate90",TABLE_TREE_CLOSE:iconPrefix+"caret-right",TABLE_EXPAND_LOADED:iconPrefix+"spinner roll",TABLE_EXPAND_OPEN:iconPrefix+"arrow-right rotate90",TABLE_EXPAND_CLOSE:iconPrefix+"arrow-right",TABLE_CHECKBOX_CHECKED:iconPrefix+"checkbox-checked-fill",TABLE_CHECKBOX_UNCHECKED:iconPrefix+"checkbox-unchecked",TABLE_CHECKBOX_INDETERMINATE:iconPrefix+"checkbox-indeterminate-fill",TABLE_RADIO_CHECKED:iconPrefix+"radio-checked-fill",TABLE_RADIO_UNCHECKED:iconPrefix+"radio-unchecked",TABLE_CUSTOM_SORT:iconPrefix+"drag-handle",TABLE_MENU_OPTIONS:iconPrefix+"arrow-right",TABLE_DRAG_ROW:iconPrefix+"drag-handle",TABLE_DRAG_COLUMN:iconPrefix+"drag-handle",TABLE_DRAG_STATUS_ROW:iconPrefix+"sort",TABLE_DRAG_STATUS_SUB_ROW:iconPrefix+"add-sub",TABLE_DRAG_STATUS_COLUMN:iconPrefix+"swap",TABLE_DRAG_DISABLED:iconPrefix+"no-drop",TABLE_ROW_GROUP_OPEN:iconPrefix+"arrow-right rotate90",TABLE_ROW_GROUP_CLOSE:iconPrefix+"arrow-right",TABLE_AGGREGATION_GROUPING:iconPrefix+"grouping",TABLE_AGGREGATION_VALUES:iconPrefix+"values",TABLE_AGGREGATION_DELETE:iconPrefix+"close",TOOLBAR_TOOLS_REFRESH:iconPrefix+"repeat",TOOLBAR_TOOLS_REFRESH_LOADING:iconPrefix+"repeat roll",TOOLBAR_TOOLS_IMPORT:iconPrefix+"upload",TOOLBAR_TOOLS_EXPORT:iconPrefix+"download",TOOLBAR_TOOLS_PRINT:iconPrefix+"print",TOOLBAR_TOOLS_FULLSCREEN:iconPrefix+"fullscreen",TOOLBAR_TOOLS_MINIMIZE:iconPrefix+"minimize",TOOLBAR_TOOLS_CUSTOM:iconPrefix+"custom-column",TOOLBAR_TOOLS_FIXED_LEFT:iconPrefix+"fixed-left",TOOLBAR_TOOLS_FIXED_LEFT_ACTIVE:iconPrefix+"fixed-left-fill",TOOLBAR_TOOLS_FIXED_RIGHT:iconPrefix+"fixed-right",TOOLBAR_TOOLS_FIXED_RIGHT_ACTIVE:iconPrefix+"fixed-right-fill"}),exports.setTheme=_core.VxeUI.setTheme),getTheme=exports.getTheme=_core.VxeUI.getTheme,setConfig=exports.setConfig=_core.VxeUI.setConfig,getConfig=exports.getConfig=_core.VxeUI.getConfig,setIcon=exports.setIcon=_core.VxeUI.setIcon,getIcon=exports.getIcon=_core.VxeUI.getIcon,setLanguage=exports.setLanguage=_core.VxeUI.setLanguage,setI18n=exports.setI18n=_core.VxeUI.setI18n,getI18n=exports.getI18n=_core.VxeUI.getI18n,globalEvents=exports.globalEvents=_core.VxeUI.globalEvents,globalResize=exports.globalResize=_core.VxeUI.globalResize,renderer=exports.renderer=_core.VxeUI.renderer,validators=exports.validators=_core.VxeUI.validators,menus=exports.menus=_core.VxeUI.menus,formats=exports.formats=_core.VxeUI.formats,commands=exports.commands=_core.VxeUI.commands,interceptor=exports.interceptor=_core.VxeUI.interceptor,clipboard=exports.clipboard=_core.VxeUI.clipboard,log=exports.log=_core.VxeUI.log,hooks=exports.hooks=_core.VxeUI.hooks,use=exports.use=_core.VxeUI.use,setup=e=>_core.VxeUI.setConfig(e),config=(exports.setup=setup,_core.VxeUI.setup=setup,e=>_core.VxeUI.setConfig(e)),t=(exports.config=config,_core.VxeUI.config=config,(e,o)=>_core.VxeUI.getI18n(e,o)),_t=(exports.t=t,_core.VxeUI.t=t,(e,o)=>(0,_utils.getFuncText)(e,o)),VXETable=(exports._t=_t,_core.VxeUI._t=_t,exports.VXETable=_core.VxeUI),saveFile=e=>_core.VxeUI.saveFile(e),readFile=(exports.saveFile=saveFile,e=>_core.VxeUI.readFile(e)),print=(exports.readFile=readFile,e=>_core.VxeUI.print(e)),modal=(exports.print=print,exports.modal={get(e){return _core.VxeUI.modal.get(e)},close(e){return _core.VxeUI.modal.close(e)},open(e){return _core.VxeUI.modal.open(e)},alert(e,o,r){return _core.VxeUI.modal.alert(e,o,r)},confirm(e,o,r){return _core.VxeUI.modal.confirm(e,o,r)},message(e,o){return _core.VxeUI.modal.message(e,o)},notification(e,o,r){return _core.VxeUI.modal.notification(e,o,r)}});var _default=exports.default=_core.VxeUI;