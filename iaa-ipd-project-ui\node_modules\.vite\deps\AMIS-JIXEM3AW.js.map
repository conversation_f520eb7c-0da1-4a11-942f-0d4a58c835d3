{"version": 3, "sources": ["../../amis/esm/renderers/AMIS.js"], "sourcesContent": ["/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __decorate } from 'tslib';\nimport { getPropValue, Renderer } from 'amis-core';\nimport React from 'react';\n\nvar AMISRenderer = /** @class */ (function (_super) {\n    __extends(AMISRenderer, _super);\n    function AMISRenderer() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    AMISRenderer.prototype.render = function () {\n        var _a = this.props, render = _a.render, props = _a.props, schema = _a.schema;\n        var value = getPropValue(this.props) || schema;\n        if (typeof value === 'string') {\n            try {\n                value = JSON.parse(value);\n            }\n            catch (e) {\n                console.warn('amis value must be json string', e);\n                value = null;\n            }\n        }\n        return render('amis', value, props);\n    };\n    AMISRenderer = __decorate([\n        Renderer({\n            type: 'amis'\n        })\n    ], AMISRenderer);\n    return AMISRenderer;\n}(React.Component));\n\nexport { <PERSON>IS<PERSON>enderer };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAQA,mBAAkB;AAElB,IAAI;AAAA;AAAA,EAA8B,SAAU,QAAQ;AAChD,cAAUA,eAAc,MAAM;AAC9B,aAASA,gBAAe;AACpB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,cAAa,UAAU,SAAS,WAAY;AACxC,UAAI,KAAK,KAAK,OAAO,SAAS,GAAG,QAAQ,QAAQ,GAAG,OAAO,SAAS,GAAG;AACvE,UAAI,QAAQ,aAAa,KAAK,KAAK,KAAK;AACxC,UAAI,OAAO,UAAU,UAAU;AAC3B,YAAI;AACA,kBAAQ,KAAK,MAAM,KAAK;AAAA,QAC5B,SACO,GAAG;AACN,kBAAQ,KAAK,kCAAkC,CAAC;AAChD,kBAAQ;AAAA,QACZ;AAAA,MACJ;AACA,aAAO,OAAO,QAAQ,OAAO,KAAK;AAAA,IACtC;AACA,IAAAA,gBAAe,WAAW;AAAA,MACtB,SAAS;AAAA,QACL,MAAM;AAAA,MACV,CAAC;AAAA,IACL,GAAGA,aAAY;AACf,WAAOA;AAAA,EACX,EAAE,aAAAC,QAAM,SAAS;AAAA;", "names": ["AMIS<PERSON><PERSON><PERSON>", "React"]}