import { defineComponent, h, provide, inject, computed, reactive } from 'vue';
import XEUtils from 'xe-utils';
import { getConfig, createEvent, useSize } from '../../ui';
import VxeRadioComponent from './radio';
import VxeRadioButtonComponent from './button';
export default defineComponent({
    name: 'VxeRadioGroup',
    props: {
        modelValue: [String, Number, Boolean],
        disabled: {
            type: Boolean,
            default: null
        },
        type: String,
        options: Array,
        optionProps: Object,
        strict: {
            type: <PERSON>olean,
            default: () => getConfig().radioGroup.strict
        },
        size: {
            type: String,
            default: () => getConfig().radioGroup.size || getConfig().size
        }
    },
    emits: [
        'update:modelValue',
        'change'
    ],
    setup(props, context) {
        const { slots, emit } = context;
        const $xeForm = inject('$xeForm', null);
        const formItemInfo = inject('xeFormItemInfo', null);
        const xID = XEUtils.uniqueId();
        useSize(props);
        const reactData = reactive({});
        const computeIsDisabled = computed(() => {
            const { disabled } = props;
            if (disabled === null) {
                if ($xeForm) {
                    return $xeForm.props.readonly || $xeForm.props.disabled;
                }
                return false;
            }
            return disabled;
        });
        const computeMaps = {
            computeIsDisabled
        };
        const $xeRadioGroup = {
            xID,
            props,
            context,
            reactData,
            name: XEUtils.uniqueId('xe_group_'),
            getComputeMaps: () => computeMaps
        };
        const computePropsOpts = computed(() => {
            return Object.assign({}, props.optionProps);
        });
        const computeLabelField = computed(() => {
            const propsOpts = computePropsOpts.value;
            return propsOpts.label || 'label';
        });
        const computeValueField = computed(() => {
            const propsOpts = computePropsOpts.value;
            return propsOpts.value || 'value';
        });
        const computeDisabledField = computed(() => {
            const propsOpts = computePropsOpts.value;
            return propsOpts.disabled || 'disabled';
        });
        const dispatchEvent = (type, params, evnt) => {
            emit(type, createEvent(evnt, { $radioGroup: $xeRadioGroup }, params));
        };
        const radioGroupMethods = {
            dispatchEvent
        };
        const radioGroupPrivateMethods = {
            handleChecked(params, evnt) {
                const value = params.label;
                emit('update:modelValue', value);
                dispatchEvent('change', params, evnt);
                // 自动更新校验状态
                if ($xeForm && formItemInfo) {
                    $xeForm.triggerItemEvent(evnt, formItemInfo.itemConfig.field, value);
                }
            }
        };
        Object.assign($xeRadioGroup, radioGroupMethods, radioGroupPrivateMethods);
        const renderVN = () => {
            const { options, type } = props;
            const defaultSlot = slots.default;
            const valueField = computeValueField.value;
            const labelField = computeLabelField.value;
            const disabledField = computeDisabledField.value;
            const btnComp = type === 'button' ? VxeRadioButtonComponent : VxeRadioComponent;
            return h('div', {
                class: 'vxe-radio-group'
            }, defaultSlot
                ? defaultSlot({})
                : (options
                    ? options.map(item => {
                        return h(btnComp, {
                            key: item[valueField],
                            label: item[valueField],
                            content: item[labelField],
                            disabled: item[disabledField]
                        });
                    })
                    : []));
        };
        provide('$xeRadioGroup', $xeRadioGroup);
        $xeRadioGroup.renderVN = renderVN;
        return $xeRadioGroup;
    },
    render() {
        return this.renderVN();
    }
});
