{"version": 3, "sources": ["../../amis/esm/renderers/Calendar.js"], "sourcesContent": ["/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __assign, __decorate } from 'tslib';\nimport { Renderer } from 'amis-core';\nimport { DateControlRenderer } from './Form/InputDate.js';\n\nvar CalendarRenderer = /** @class */ (function (_super) {\n    __extends(CalendarRenderer, _super);\n    function CalendarRenderer() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CalendarRenderer.defaultProps = __assign(__assign({}, DateControlRenderer.defaultProps), { embed: true });\n    CalendarRenderer = __decorate([\n        Renderer({\n            type: 'calendar'\n        })\n    ], CalendarRenderer);\n    return CalendarRenderer;\n}(DateControlRenderer));\n\nexport { CalendarRenderer };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAI;AAAA;AAAA,EAAkC,SAAU,QAAQ;AACpD,cAAUA,mBAAkB,MAAM;AAClC,aAASA,oBAAmB;AACxB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,kBAAiB,eAAe,SAAS,SAAS,CAAC,GAAG,oBAAoB,YAAY,GAAG,EAAE,OAAO,KAAK,CAAC;AACxG,IAAAA,oBAAmB,WAAW;AAAA,MAC1B,SAAS;AAAA,QACL,MAAM;AAAA,MACV,CAAC;AAAA,IACL,GAAGA,iBAAgB;AACnB,WAAOA;AAAA,EACX,EAAE,mBAAmB;AAAA;", "names": ["<PERSON><PERSON><PERSON><PERSON>"]}