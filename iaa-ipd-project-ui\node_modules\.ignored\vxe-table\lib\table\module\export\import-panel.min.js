Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var _vue=require("vue"),_comp=require("../../../ui/src/comp"),_ui=require("../../../ui"),_xeUtils=_interopRequireDefault(require("xe-utils")),_utils=require("../../../ui/src/utils"),_log=require("../../../ui/src/log");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let{getI18n,getIcon}=_ui.VxeUI;var _default=exports.default=(0,_comp.defineVxeComponent)({name:"VxeTableImportPanel",props:{defaultOptions:Object,storeData:Object},setup(p){let d=_ui.VxeUI.getComponent("VxeModal"),m=_ui.VxeUI.getComponent("VxeButton"),c=_ui.VxeUI.getComponent("VxeSelect"),_=(0,_vue.inject)("$xeTable",{}),a=_.getComputeMaps().computeImportOpts,x=(0,_vue.reactive)({loading:!1}),b=(0,_vue.ref)(),g=(0,_vue.computed)(()=>{var e=p.storeData;return e.filename+"."+e.type}),h=(0,_vue.computed)(()=>{var e=p.storeData;return e.file&&e.type}),f=(0,_vue.computed)(()=>{var e=p.storeData;let{type:t,typeList:o}=e;return t?(e=_xeUtils.default.find(o,e=>t===e.value))?e.label:"*.*":"*."+o.map(e=>e.value).join(", *.")}),C=()=>{var e=p.storeData;Object.assign(e,{filename:"",sheetName:"",type:""})},V=()=>{let{storeData:t,defaultOptions:e}=p;_.readFile(e).then(e=>{e=e.file;Object.assign(t,(0,_utils.parseFile)(e),{file:e})}).catch(e=>e)},I=()=>{(0,_vue.nextTick)(()=>{var e=b.value;e&&e.focus()})},O=()=>{var e=p.storeData;e.visible=!1},D=()=>{let{storeData:e,defaultOptions:t}=p;var o=a.value;x.loading=!0,_.importByFile(e.file,Object.assign({},o,t)).then(()=>{x.loading=!1,e.visible=!1}).catch(()=>{x.loading=!1})};return(0,_vue.nextTick)(()=>{d||(0,_log.errLog)("vxe.error.reqComp",["vxe-modal"]),m||(0,_log.errLog)("vxe.error.reqComp",["vxe-button"]),c||(0,_log.errLog)("vxe.error.reqComp",["vxe-select"])}),()=>{let t=_.xeGrid,{defaultOptions:o,storeData:a}=p,l=g.value,r=h.value,i=f.value;var e=o.slots||{};let u=e.top,v=e.bottom,n=e.default,s=e.footer;return d?(0,_vue.h)(d,{id:"VXE_IMPORT_MODAL",modelValue:a.visible,title:getI18n("vxe.import.impTitle"),className:"vxe-table-export-popup-wrapper",width:540,minWidth:360,minHeight:240,mask:!0,lockView:!0,showFooter:!0,escClosable:!0,maskClosable:!0,showMaximize:!0,resize:!0,loading:x.loading,"onUpdate:modelValue"(e){a.visible=e},onShow:I},{default:()=>{var e={$table:_,$grid:t,options:o,params:o.params};return(0,_vue.h)("div",{class:"vxe-table-export--panel"},[u?(0,_vue.h)("div",{class:"vxe-table-export--panel-top"},_.callSlot(u,e)):(0,_vue.createCommentVNode)(),(0,_vue.h)("div",{class:"vxe-table-export--panel-body"},n?_.callSlot(n,e):[(0,_vue.h)("table",{class:"vxe-table-export--panel-table",cellspacing:0,cellpadding:0,border:0},[(0,_vue.h)("tbody",[(0,_vue.h)("tr",[(0,_vue.h)("td",getI18n("vxe.import.impFile")),(0,_vue.h)("td",[r?(0,_vue.h)("div",{class:"vxe-table-export--selected--file",title:l},[(0,_vue.h)("span",l),(0,_vue.h)("i",{class:getIcon().INPUT_CLEAR,onClick:C})]):(0,_vue.h)("button",{ref:b,class:"vxe-table-export--select--file",onClick:V},getI18n("vxe.import.impSelect"))])]),(0,_vue.h)("tr",[(0,_vue.h)("td",getI18n("vxe.import.impType")),(0,_vue.h)("td",i)]),(0,_vue.h)("tr",[(0,_vue.h)("td",getI18n("vxe.import.impMode")),(0,_vue.h)("td",[c?(0,_vue.h)(c,{modelValue:o.mode,options:a.modeList,"onUpdate:modelValue"(e){o.mode=e}}):(0,_vue.createCommentVNode)()])])])])]),v?(0,_vue.h)("div",{class:"vxe-table-export--panel-bottom"},_.callSlot(v,e)):(0,_vue.createCommentVNode)()])},footer(){var e={$table:_,$grid:t,options:o,params:o.params};return(0,_vue.h)("div",{class:"vxe-table-export--panel-footer"},s?_.callSlot(s,e):[(0,_vue.h)("div",{class:"vxe-table-export--panel-btns"},[m?(0,_vue.h)(m,{content:getI18n("vxe.import.impCancel"),onClick:O}):(0,_vue.createCommentVNode)(),m?(0,_vue.h)(m,{status:"primary",disabled:!r||x.loading,content:getI18n("vxe.import.impConfirm"),onClick:D}):(0,_vue.createCommentVNode)()])])}}):(0,_vue.createCommentVNode)()}}});