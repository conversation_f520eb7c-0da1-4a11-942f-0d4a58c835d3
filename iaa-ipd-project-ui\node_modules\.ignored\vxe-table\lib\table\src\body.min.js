Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var _vue=require("vue"),_comp=require("../../ui/src/comp"),_xeUtils=_interopRequireDefault(require("xe-utils")),_ui=require("../../ui"),_util=require("./util"),_dom=require("../../ui/src/dom"),_utils=require("../../ui/src/utils"),_vn=require("../../ui/src/vn");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let{getI18n,renderer,renderEmptyElement}=_ui.VxeUI,renderType="body";var _default=exports.default=(0,_comp.defineVxeComponent)({name:"VxeTableBody",props:{tableData:Array,tableColumn:Array,fixedColumn:Array,fixedType:{type:String,default:""}},setup(k){let Pe=(0,_vue.inject)("$xeTable",{}),{xID:z,props:Xe,context:q,reactData:Ke,internalData:Ye}=Pe,{computeEditOpts:Je,computeMouseOpts:Qe,computeCellOffsetWidth:Ze,computeAreaOpts:el,computeDefaultRowHeight:ll,computeEmptyOpts:$,computeTooltipOpts:tl,computeRadioOpts:e,computeExpandOpts:re,computeTreeOpts:p,computeCheckboxOpts:al,computeCellOpts:ol,computeValidOpts:rl,computeRowOpts:il,computeColumnOpts:sl,computeRowDragOpts:nl,computeColumnDragOpts:l,computeResizableOpts:dl,computeVirtualXOpts:ul,computeVirtualYOpts:pl}=Pe.getComputeMaps(),A=(0,_vue.ref)(),H=(0,_vue.ref)(),F=(0,_vue.ref)(),L=(0,_vue.ref)(),j=(0,_vue.ref)(),U=(0,_vue.ref)(),N=(0,_vue.ref)(),B=(0,_vue.ref)(),cl=()=>{var e=Xe.delayHover,{lastScrollTime:l,isDragResize:t}=Ke;return!!(t||l&&Date.now()<l+e)},vl=(e,l,t)=>{var{row:a,column:o}=l,r=Ye.afterFullData,i=Xe.treeConfig,s=p.value,{slots:o,treeNode:n}=o,d=Ye.fullAllDataRowIdData;if(o&&o.line)return Pe.callSlot(o.line,l);o=d[e];let u=0,c=null;return o&&(u=o.level,c=o.items[o.treeIndex-1]),i&&n&&(s.showLine||s.line)?[(0,_vue.h)("div",{key:"tl",class:"vxe-tree--line-wrapper"},[(0,_vue.h)("div",{class:"vxe-tree--line",style:{height:`${Pe.eqRow(r[0],a)?1:(0,_util.calcTreeLine)(l,c)}px`,bottom:`-${Math.floor(t/2)}px`,left:u*s.indent+(u?2-(0,_util.getOffsetSize)(Pe):0)+16+"px"}})])]:[]},se=(A,e,l,H,F,t,L,j,a,o,U,N,r)=>{var i=Pe.xeGrid,{columnKey:B,resizable:s,showOverflow:n,border:G,height:d,treeConfig:W,cellClassName:V,cellStyle:P,align:X,spanMethod:K,mouseConfig:Y,editConfig:J,editRules:u,tooltipConfig:c,padding:p}=Xe,{tableData:v,dragRow:Q,overflowX:Z,currentColumn:ee,scrollXLoad:le,scrollYLoad:g,mergeBodyFlag:te,calcCellHeightFlag:x,resizeHeightFlag:h,resizeWidthFlag:ae,editStore:oe,isAllOverflow:re,validErrorMaps:m}=Ke,{fullAllDataRowIdData:ie,fullColumnIdData:w,mergeBodyCellMaps:se,visibleColumn:ne,afterFullData:de,mergeBodyList:ue,scrollXStore:ce,scrollYStore:pe}=Ye,_=ol.value,f=rl.value,ve=al.value,ge=Je.value,b=tl.value,xe=dl.value,he=ul.value,me=pl.value,{isAllColumnDrag:xe,isAllRowDrag:we}=xe,y=il.value,C=nl.value,_e=ll.value,x=x?_.height||y.height:0,{disabledMethod:R,isCrossDrag:fe,isPeerDrag:be}=C,ye=sl.value,Ce=Qe.value,Re=el.value,De=Ze.value,Re=Re.selectCellToRow,{type:Ee,cellRender:Oe,editRender:Ie,align:Se,showOverflow:Me,className:Te,treeNode:ke,rowResize:ze,padding:D,verticalAlign:E,slots:qe}=o,O=_.verticalAlign,oe=oe.actived,$e=ie[e]||{},I=o.id,w=w[I]||{},S=Ie||Oe,S=S?renderer.get(S.name):null,Ae=S?S.tableCellClassName||S.cellClassName:null,He=S?S.tableCellStyle||S.cellStyle:"";let Fe=b.showAll;var Le=w.index,b=w._index,w=(0,_utils.isEnableConf)(Ie),h=h?$e.resizeHeight:0;let M=l?o.fixed!==l:o.fixed&&Z;Z=_xeUtils.default.eqNull(D)?null===p?_.padding:p:D,p=_xeUtils.default.eqNull(Me)?n:Me,D="ellipsis"===p;let T="title"===p,k=!0===p||"tooltip"===p;n=re||T||k||D,Me=_xeUtils.default.isBoolean(o.resizable)?o.resizable:ye.resizable||s,p=!!x,s=0<h;let je;x={},h=Se||(S?S.tableCellAlign:"")||X,Se=_xeUtils.default.eqNull(E)?O:E,S=m[e+":"+I],X=u&&f.showMessage&&("default"===f.message?d||1<v.length:"inline"===f.message),O={colid:I};let z={$table:Pe,$grid:i,isEdit:!1,seq:A,rowid:e,row:t,rowIndex:L,$rowIndex:j,_rowIndex:a,column:o,columnIndex:Le,$columnIndex:U,_columnIndex:b,fixed:l,type:renderType,isHidden:!!M,level:F,visibleData:de,data:v,items:r},q=!1,Ue=!1,$=((q=y.drag?"row"===C.trigger||o.dragSort&&"cell"===C.trigger:q)&&(Ue=!(!R||!R(z))),(T||k||Fe||c)&&(x.onMouseenter=e=>{cl()||(T?(0,_dom.updateCellTitle)(e.currentTarget,o):(k||Fe)&&Pe.triggerBodyTooltipEvent(e,z),Pe.dispatchEvent("cell-mouseenter",Object.assign({cell:e.currentTarget},z),e))}),(k||Fe||c)&&(x.onMouseleave=e=>{cl()||((k||Fe)&&Pe.handleTargetLeaveEvent(e),Pe.dispatchEvent("cell-mouseleave",Object.assign({cell:e.currentTarget},z),e))}),(q||ve.range||Y)&&(x.onMousedown=e=>{Pe.triggerCellMousedownEvent(e,z)}),q&&(x.onMouseup=Pe.triggerCellMouseupEvent),x.onClick=e=>{Pe.triggerCellClickEvent(e,z)},!(x.onDblclick=e=>{Pe.triggerCellDblclickEvent(e,z)})),Ne=1;if(te&&ue.length){E=se[a+":"+b];if(E){var{rowspan:m,colspan:u}=E;if(!m||!u)return null;1<m&&($=!0,Ne=m,O.rowspan=m),1<u&&($=!0,O.colspan=u)}}else if(K){var{rowspan:d=1,colspan:i=1}=K(z)||{};if(!d||!i)return null;1<d&&($=!0,Ne=d,O.rowspan=d),1<i&&($=!0,O.colspan=i)}!(M=M&&$&&(1<O.colspan||1<O.rowspan)?!1:M)&&J&&(Ie||Oe)&&(ge.showStatus||ge.showUpdateStatus)&&(je=Pe.isUpdateByRow(t,o.field));A=g&&!n;let Be=(0,_util.getCellRestHeight)($e,_,y,_e);L=U===N.length-1,j=!o.resizeWidth&&("auto"===o.minWidth||"auto"===o.width);let Ge=!1;$||Q&&(0,_util.getRowid)(Pe,Q)===e||(g&&!W&&!me.immediate&&(a<pe.visibleStartIndex-pe.preloadSize||a>pe.visibleEndIndex+pe.preloadSize)||le&&!he.immediate&&!o.fixed&&(b<ce.visibleStartIndex-ce.preloadSize||b>ce.visibleEndIndex+ce.preloadSize))&&(Ge=!0),1<Ne&&(v=de[a+Ne-1])&&(r=ie[(0,_util.getRowid)(Pe,v)])&&(Be+=r.oTop+(0,_util.getCellRestHeight)(r,_,y,_e)-$e.oTop-(0,_util.getCellRestHeight)($e,_,y,_e));C={};if(n&&ae){let l=O.colspan||0;if(1<l)for(let e=1;e<l;e++){var We=ne[Le+e];We&&(l+=We.renderWidth)}C.width=o.renderWidth-De*l+"px"}g||n||p||s?C.height=Be+"px":C.minHeight=Be+"px";R=[];M&&re?R.push((0,_vue.h)("div",{key:"tc",class:["vxe-cell",{"c--title":T,"c--tooltip":k,"c--ellipsis":D}],style:C})):(W&&R.push(...vl(e,z,Be)),R.push((0,_vue.h)("div",{key:"tc",class:["vxe-cell",{"c--title":T,"c--tooltip":k,"c--ellipsis":D}],style:C,title:T?Pe.getCellLabel(t,o):null},Ge?[]:[(0,_vue.h)("div",{colid:I,rowid:e,class:"vxe-cell--wrapper"},o.renderCell(z))])),X&&S&&(c=S.rule,ve=qe?qe.valid:null,te=Object.assign(Object.assign(Object.assign({},z),S),{rule:S}),R.push((0,_vue.h)("div",{key:"tcv",class:["vxe-cell--valid-error-tip",(0,_dom.getPropClass)(f.className,te)],style:c&&c.maxWidth?{width:c.maxWidth+"px"}:null},[(0,_vue.h)("div",{class:"vxe-cell--valid-error-wrapper vxe-cell--valid-error-theme-"+(f.theme||"normal")},[ve?Pe.callSlot(ve,te):[(0,_vue.h)("span",{class:"vxe-cell--valid-error-msg"},S.content)]])]))));let Ve=!1;return Y&&Ce.area&&Re&&((b||!0!==Re)&&Re!==o.field||(Ve=!0)),!M&&Me&&xe&&R.push((0,_vue.h)("div",{key:"tcc",class:["vxe-cell--col-resizable",{"is--line":!G||"none"===G}],onMousedown:e=>Pe.handleColResizeMousedownEvent(e,l,z),onDblclick:e=>Pe.handleColResizeDblclickEvent(e,z)})),(ze||we)&&y.resizable&&R.push((0,_vue.h)("div",{key:"tcr",class:"vxe-cell--row-resizable",onMousedown:e=>Pe.handleRowResizeMousedownEvent(e,z),onDblclick:e=>Pe.handleRowResizeDblclickEvent(e,z)})),(0,_vue.h)("td",Object.assign(Object.assign(Object.assign({class:["vxe-body--column",I,Se?"col--vertical-"+Se:"",h?"col--"+h:"",Ee?"col--"+Ee:"",{"col--last":L,"col--tree-node":ke,"col--edit":w,"col--ellipsis":n,"col--cs-height":p,"col--rs-height":s,"col--to-row":Ve,"col--auto-height":A,"fixed--width":!j,"fixed--hidden":M,"is--padding":Z,"is--progress":M&&re||Ge,"is--drag-cell":q&&(fe||be||!F),"is--drag-disabled":Ue,"col--dirty":je,"col--active":J&&w&&oe.row===t&&(oe.column===o||"row"===ge.mode),"col--valid-error":!!S,"col--current":ee===o},(0,_dom.getPropClass)(Ae,z),(0,_dom.getPropClass)(Te,z),(0,_dom.getPropClass)(V,z)],key:B||le||g||ye.useKey||y.useKey||ye.drag?I:U},O),{style:Object.assign({},_xeUtils.default.isFunction(He)?He(z):He,_xeUtils.default.isFunction(P)?P(z):P)}),x),H&&M?[]:R)},ie=(h,m,w,_)=>{let f=Pe.xeGrid,{stripe:b,rowKey:y,highlightHoverRow:C,rowClassName:R,rowStyle:D,editConfig:E,treeConfig:O}=Xe,{hasFixedColumn:I,treeExpandedFlag:S,isColLoading:M,scrollXLoad:T,scrollYLoad:k,isAllOverflow:z,rowExpandedFlag:q,expandColumn:$,selectRadioRow:A,pendingRowFlag:H,isDragColMove:F,rowExpandHeightFlag:L,isRowGroupStatus:j}=Ke,{fullAllDataRowIdData:U,fullColumnIdData:N,treeExpandedMaps:B,pendingRowMaps:G,rowExpandedMaps:W}=Ye,Q=al.value,Z=e.value,V=p.value,P=Je.value,X=il.value,K=sl.value,ee=l.value,{transform:Y,seqMode:le}=V,te=V.children||V.childrenField,J=[],ae=(0,_util.createHandleGetRowId)(Pe).handleGetRowId,oe=O||j;return w.forEach((a,o)=>{let r=ae(a);var i=U[r]||{};let s=o,n=0,d=-1,u=-1;var c=j&&a.isAggregate,p={},v=((X.isHover||C)&&(p.onMouseenter=e=>{cl()||Pe.triggerHoverEvent(e,{row:a,rowIndex:s})},p.onMouseleave=()=>{cl()||Pe.clearHoverRow()}),i&&(n=i.level,d=c||O&&Y&&"increasing"===le?i._index+1:i.seq,s=i.index,u=i._index),{$table:Pe,seq:d,rowid:r,fixed:h,type:renderType,level:n,row:a,rowIndex:s,$rowIndex:o,_rowIndex:u}),g=$&&!!q&&!!W[r];let e=!1,l=[],t=!1;E&&(t=Pe.isInsertByRow(a)),!O||k||Y||(l=a[te],e=!!S&&l&&0<l.length&&!!B[r]),!X.drag||j||O&&!Y||(p.onDragstart=Pe.handleRowDragDragstartEvent,p.onDragend=Pe.handleRowDragDragendEvent,p.onDragover=Pe.handleRowDragDragoverEvent);c=["vxe-body--row",oe?"row--level-"+n:"",{"row--stripe":b&&(u+1)%2==0,"is--new":t,"is--expand-row":g,"is--expand-tree":e,"row--new":t&&(P.showStatus||P.showInsertStatus),"row--radio":Z.highlight&&Pe.eqRow(A,a),"row--checked":Q.highlight&&Pe.isCheckedByCheckboxRow(a),"row--pending":!!H&&!!G[r],"row--group":c},(0,_dom.getPropClass)(R,v)];let x=_.map((e,l)=>se(d,r,h,m,n,a,s,o,u,e,l,_,w));if(J.push(!M&&K.drag&&ee.animation?(0,_vue.h)(_vue.TransitionGroup,Object.assign({name:"vxe-header--col-list"+(F?"":"-disabled"),tag:"tr",class:c,rowid:r,style:D?_xeUtils.default.isFunction(D)?D(v):D:null,key:y||T||k||X.useKey||X.drag||K.drag||j||O?r:o},p),{default:()=>x}):(0,_vue.h)("tr",Object.assign({class:c,rowid:r,style:D?_xeUtils.default.isFunction(D)?D(v):D:null,key:y||T||k||X.useKey||X.drag||K.drag||j||O?r:o},p),x)),g){var{height:c,padding:v,mode:p}=re.value;if("fixed"===p)J.push((0,_vue.h)("tr",{class:"vxe-body--row-expanded-place",key:"expand_"+r,rowid:r},[(0,_vue.h)("td",{class:"vxe-body--row-expanded-place-column",colspan:_.length,style:{height:`${L?i.expandHeight||c:0}px`}})]));else{g={},p=(c&&(g.height=c+"px"),O&&(g.paddingLeft=n*V.indent+30+"px"),$||{}).showOverflow,i=$.id,i=N[i]||{},p=_xeUtils.default.eqNull(p)?z:p;let e=-1,l=-1,t=-1;i&&(e=i.index,l=i.$index,t=i._index);i={$grid:f,$table:Pe,seq:d,column:$,columnIndex:e,$columnIndex:l,_columnIndex:t,fixed:h,type:renderType,level:n,row:a,rowid:r,rowIndex:s,$rowIndex:o,_rowIndex:u,isHidden:!1,isEdit:!1,visibleData:[],data:[],items:[]};J.push((0,_vue.h)("tr",{class:["vxe-body--expanded-row",{"is--padding":v}],key:"expand_"+r},[(0,_vue.h)("td",{class:["vxe-body--expanded-column",{"fixed--hidden":h&&!I,"col--ellipsis":p}],colspan:_.length},[(0,_vue.h)("div",{class:["vxe-body--expanded-cell",{"is--ellipsis":c}],style:g},[$.renderData(i)])])]))}}e&&J.push(...ie(h,m,l,_))}),J};(0,_vue.onMounted)(()=>{(0,_vue.nextTick)(()=>{var e=k.fixedType,l=Ye.elemStore,e=`${e||"main"}-body-`;l[e+"wrapper"]=A,l[e+"scroll"]=H,l[e+"table"]=F,l[e+"colgroup"]=L,l[e+"list"]=j,l[e+"xSpace"]=U,l[e+"ySpace"]=N,l[e+"emptyBlock"]=B})}),(0,_vue.onUnmounted)(()=>{var e=k.fixedType,l=Ye.elemStore,e=`${e||"main"}-body-`;l[e+"wrapper"]=null,l[e+"scroll"]=null,l[e+"table"]=null,l[e+"colgroup"]=null,l[e+"list"]=null,l[e+"xSpace"]=null,l[e+"ySpace"]=null,l[e+"emptyBlock"]=null});return()=>{var e=q.slots,l=Pe.xeGrid;let{fixedColumn:t,fixedType:a,tableColumn:o}=k;var{spanMethod:r,footerSpanMethod:i,mouseConfig:s}=Xe,{isGroup:n,tableData:d,isRowLoading:u,isColLoading:c,overflowX:p,scrollXLoad:v,scrollYLoad:g,isAllOverflow:x,isDragRowMove:h,expandColumn:m,dragRow:w,dragCol:_}=Ke,{visibleColumn:f,fullAllDataRowIdData:b,fullColumnIdData:y}=Ye,C=il.value,R=$.value,D=Qe.value,E=nl.value,O=re.value;let I=d,S=o,M=!1;!(v||g||x)||m&&"fixed"!==O.mode||r||i||(M=!0),c||!a&&p||(S=f),a&&M&&(S=t||[]),g&&w&&2<I.length&&(d=b[(0,_util.getRowid)(Pe,w)])&&(x=d._index,m=I[0],O=I[I.length-1],r=b[(0,_util.getRowid)(Pe,m)],i=b[(0,_util.getRowid)(Pe,O)],r)&&i&&(p=r._index,f=i._index,x<p?I=[w].concat(I):f<x&&(I=I.concat([w]))),a||n||v&&_&&2<S.length&&(g=y[_.id])&&(d=g._index,m=S[0],b=S[S.length-1],O=y[m.id],r=y[b.id],O)&&r&&(i=O._index,p=r._index,d<i?S=[_].concat(S):p<d&&(S=S.concat([_])));let T;f=e?e.empty:null,x={$table:Pe,$grid:l},T=f?Pe.callSlot(f,x):(n=(w=R.name?renderer.get(R.name):null)?w.renderTableEmpty||w.renderTableEmptyView||w.renderEmpty:null)?(0,_vn.getSlotVNs)(n(R,x)):Xe.emptyText||getI18n("vxe.table.emptyText"),v={onScroll(e){Pe.triggerBodyScrollEvent(e,a)}};return(0,_vue.h)("div",{ref:A,class:["vxe-table--body-wrapper",a?`fixed-${a}--wrapper`:"body--wrapper"],xid:z},[(0,_vue.h)("div",Object.assign({ref:H,class:"vxe-table--body-inner-wrapper"},v),[a?renderEmptyElement(Pe):(0,_vue.h)("div",{ref:U,class:"vxe-body--x-space"}),(0,_vue.h)("div",{ref:N,class:"vxe-body--y-space"}),(0,_vue.h)("table",{ref:F,class:"vxe-table--body",xid:z,cellspacing:0,cellpadding:0,border:0,xvm:M?"1":null},[(0,_vue.h)("colgroup",{ref:L},S.map((e,l)=>(0,_vue.h)("col",{name:e.id,key:l,style:{width:e.renderWidth+"px"}}))),!u&&!c&&C.drag&&E.animation?(0,_vue.h)(_vue.TransitionGroup,{ref:j,name:"vxe-body--row-list"+(h?"":"-disabled"),tag:"tbody"},{default:()=>ie(a,M,I,S)}):(0,_vue.h)("tbody",{ref:j},ie(a,M,I,S))]),(0,_vue.h)("div",{class:"vxe-table--checkbox-range"}),s&&D.area?(0,_vue.h)("div",{class:"vxe-table--cell-area"},[(0,_vue.h)("span",{class:"vxe-table--cell-main-area"},D.extension?[(0,_vue.h)("span",{class:"vxe-table--cell-main-area-btn",onMousedown(e){Pe.triggerCellAreaExtendMousedownEvent&&Pe.triggerCellAreaExtendMousedownEvent(e,{$table:Pe,fixed:a,type:renderType})}})]:[]),(0,_vue.h)("span",{class:"vxe-table--cell-copy-area"}),(0,_vue.h)("span",{class:"vxe-table--cell-extend-area"}),(0,_vue.h)("span",{class:"vxe-table--cell-multi-area"}),(0,_vue.h)("span",{class:"vxe-table--cell-active-area"}),(0,_vue.h)("span",{class:"vxe-table--cell-row-status-area"})]):renderEmptyElement(Pe),a?renderEmptyElement(Pe):(0,_vue.h)("div",{class:"vxe-table--empty-block",ref:B},[(0,_vue.h)("div",{class:"vxe-table--empty-content"},T)])])])}}});