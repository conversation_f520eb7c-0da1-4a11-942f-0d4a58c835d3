var _vue=require("vue"),_xeUtils=_interopRequireDefault(require("xe-utils")),_ui=require("../../../ui"),_util=require("../../src/util"),_utils=require("../../../ui/src/utils"),_dom=require("../../../ui/src/dom"),_util2=require("./util"),_log=require("../../../ui/src/log");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let{getI18n,hooks,renderer}=_ui.VxeUI,htmlCellElem,csvBOM="\ufeff",enterSymbol="\r\n";function defaultFilterExportColumn(e){return!!e.field||-1===["seq","checkbox","radio"].indexOf(e.type||"")}let getConvertColumns=e=>{let t=[];return e.forEach(e=>{e.childNodes&&e.childNodes.length?(t.push(e),t.push(...getConvertColumns(e.childNodes))):t.push(e)}),t},convertToRows=e=>{let t=1,r=(l,e)=>{if(e&&(l._level=e._level+1,t<l._level)&&(t=l._level),l.childNodes&&l.childNodes.length){let t=0;l.childNodes.forEach(e=>{r(e,l),t+=e._colSpan}),l._colSpan=t}else l._colSpan=1},l=(e.forEach(e=>{e._level=1,r(e)}),[]);for(let e=0;e<t;e++)l.push([]);return getConvertColumns(e).forEach(e=>{e.childNodes&&e.childNodes.length?e._rowSpan=1:e._rowSpan=t-e._level+1,l[e._level-1].push(e)}),l};function toTableBorder(e){return!0===e?"full":e||"default"}function getBooleanValue(e){return"TRUE"===e||"true"===e||!0===e}function getFooterData(l,e,t){let r=e.footerFilterMethod;return r?t.filter((e,t)=>r({$table:l,items:e,$rowIndex:t})):t}function getCsvCellTypeLabel(e,t){if(t){if("seq"===e.type)return`	`+t;switch(e.cellType){case"string":if(isNaN(t))break;return`	`+t;case"number":break;default:if(12<=t.length&&!isNaN(t))return`	`+t}}return t}function toTxtCellLabel(e){return/[",\s\n]/.test(e)?`"${e.replace(/"/g,'""')}"`:e}function getElementsByTagName(e,t){return e.getElementsByTagName(t)}function getTxtCellKey(e){return`#${e}@`+_xeUtils.default.uniqueId()}function replaceTxtCell(e,t){return e.replace(/#\d+@\d+/g,e=>_xeUtils.default.hasOwnProp(t,e)?t[e]:e)}function getTxtCellValue(e,t){return replaceTxtCell(e,t).replace(/^"+$/g,e=>'"'.repeat(Math.ceil(e.length/2)))}function toExportField(e,t){var{fieldMaps:e,titleMaps:l}=e;return e[t]||(e=l[t])&&e.field&&(t=e.field),t}function parseCsvAndTxt(t,e,a){e=e.split(enterSymbol);let i=[],s=[];if(e.length){let r={},o=Date.now();e.forEach(e=>{if(e){let l={};e=(e=e.replace(/("")|(\n)/g,(e,t)=>{var l=getTxtCellKey(o);return r[l]=t?'"':"\n",l}).replace(/"(.*?)"/g,(e,t)=>{var l=getTxtCellKey(o);return r[l]=replaceTxtCell(t,r),l})).split(a);s.length?(e.forEach((e,t)=>{t<s.length&&(l[s[t]]=getTxtCellValue(e.trim(),r))}),i.push(l)):s=e.map(e=>toExportField(t,getTxtCellValue(e.trim(),r)))}})}return{fields:s,rows:i}}function parseCsv(e,t){return parseCsvAndTxt(e,t,",")}function parseTxt(e,t){return parseCsvAndTxt(e,t,"\t")}function parseHTML(t,e){var l,e=getElementsByTagName((new DOMParser).parseFromString(e,"text/html"),"body");let r=[],o=[];return e.length&&(e=getElementsByTagName(e[0],"table")).length&&(l=getElementsByTagName(e[0],"thead")).length&&(_xeUtils.default.arrayEach(getElementsByTagName(l[0],"tr"),e=>{_xeUtils.default.arrayEach(getElementsByTagName(e,"th"),e=>{o.push(toExportField(t,e.textContent||""))})}),(l=getElementsByTagName(e[0],"tbody")).length)&&_xeUtils.default.arrayEach(getElementsByTagName(l[0],"tr"),e=>{let l={};_xeUtils.default.arrayEach(getElementsByTagName(e,"td"),(e,t)=>{o[t]&&(l[o[t]]=e.textContent||"")}),r.push(l)}),{fields:o,rows:r}}function parseXML(t,e){var e=getElementsByTagName((new DOMParser).parseFromString(e,"application/xml"),"Worksheet");let r=[],o=[];return e.length&&(e=getElementsByTagName(e[0],"Table")).length&&(e=getElementsByTagName(e[0],"Row")).length&&(_xeUtils.default.arrayEach(getElementsByTagName(e[0],"Cell"),e=>{o.push(toExportField(t,e.textContent||""))}),_xeUtils.default.arrayEach(e,(e,t)=>{if(t){let l={};t=getElementsByTagName(e,"Cell");_xeUtils.default.arrayEach(t,(e,t)=>{o[t]&&(l[o[t]]=e.textContent)}),r.push(l)}})),{fields:o,rows:r}}function clearColumnConvert(e){_xeUtils.default.eachTree(e,e=>{delete e._level,delete e._colSpan,delete e._rowSpan,delete e._children,delete e.childNodes},{children:"children"})}let tableExportMethodKeys=["exportData","importByFile","importData","saveFile","readFile","print","getPrintHtml","openImport","closeImport","openExport","closeExport","openPrint","closePrint"];hooks.add("tableExportModule",{setupTable(j){let{props:I,reactData:D,internalData:O}=j,{computeTreeOpts:N,computePrintOpts:i,computeExportOpts:L,computeImportOpts:c,computeCustomOpts:E,computeSeqOpts:s,computeRadioOpts:r,computeCheckboxOpts:o,computeColumnOpts:n}=j.getComputeMaps(),R=(0,_vue.inject)("$xeGrid",null),g=e=>{var t=N.value,t=t.children||t.childrenField;return e[t]&&e[t].length},f=(e,t,l,r,o)=>{var a=s.value.seqMethod||r.seqMethod;return a?a({$table:j,row:t,rowIndex:j.getRowIndex(t),$rowIndex:l,column:r,columnIndex:j.getColumnIndex(r),$columnIndex:o}):e};function C(e,t){var l=n.value,l=t.headerExportMethod||l.headerExportMethod;return l?l({column:t,options:e,$table:j}):(e.isTitle?t.getTitle():t.field)||""}let v=e=>_xeUtils.default.isBoolean(e)?e?"TRUE":"FALSE":e,b=e=>(0,_utils.eqEmptyValue)(e)?"":""+e,d=(c,u,e)=>{let{isAllExpand:i,mode:p}=c;var t=I.treeConfig;let m=r.value,h=o.value;var l=N.value;let x=n.value;if(htmlCellElem=htmlCellElem||document.createElement("div"),t){t=l.children||l.childrenField;let o=[],a=new Map;return _xeUtils.default.eachTree(e,(e,s,t,n,l,r)=>{let d=e._row||e;e=l&&l._row?l._row:l;if(i||!e||a.has(e)&&j.isTreeExpandByRow(e)){l=g(d);let i={_row:d,_level:r.length-1,_hasChild:l,_expand:l&&j.isTreeExpandByRow(d)};u.forEach((e,t)=>{let l="";var r=e.editRender||e.cellRender;let o=e.exportMethod||x.exportMethod;if(o=(o=!o&&r&&r.name&&(r=renderer.get(r.name))?r.tableExportMethod||r.exportMethod:o)||x.exportMethod)l=o({$table:j,row:d,column:e,options:c});else switch(e.type){case"seq":var a=n.map((e,t)=>t%2==0?Number(e)+1:".").join("");l="all"===p?a:f(a,d,s,e,t);break;case"checkbox":l=v(j.isCheckedByCheckboxRow(d)),i._checkboxLabel=h.labelField?_xeUtils.default.get(d,h.labelField):"",i._checkboxDisabled=h.checkMethod&&!h.checkMethod({$table:j,row:d});break;case"radio":l=v(j.isCheckedByRadioRow(d)),i._radioLabel=m.labelField?_xeUtils.default.get(d,m.labelField):"",i._radioDisabled=m.checkMethod&&!m.checkMethod({$table:j,row:d});break;default:c.original?l=(0,_util.getCellValue)(d,e):(l=j.getCellLabel(d,e),"html"===e.type?(htmlCellElem.innerHTML=l,l=htmlCellElem.innerText.trim()):(a=j.getCellElement(d,e))&&!(0,_dom.hasClass)(a,"is--progress")&&(l=a.innerText.trim()))}i[e.id]=b(l)}),a.set(d,1),o.push(Object.assign(i,d))}},{children:t}),o}return e.map((i,s)=>{let n={_row:i};return u.forEach((e,t)=>{let l="";var r=e.editRender||e.cellRender;let o=e.exportMethod||x.exportMethod;if(o=!o&&r&&r.name&&(r=renderer.get(r.name))?r.tableExportMethod||r.exportMethod:o)l=o({$table:j,row:i,column:e,options:c});else switch(e.type){case"seq":var a=s+1;l="all"===p?a:f(a,i,s,e,t);break;case"checkbox":l=v(j.isCheckedByCheckboxRow(i)),n._checkboxLabel=h.labelField?_xeUtils.default.get(i,h.labelField):"",n._checkboxDisabled=h.checkMethod&&!h.checkMethod({$table:j,row:i});break;case"radio":l=v(j.isCheckedByRadioRow(i)),n._radioLabel=m.labelField?_xeUtils.default.get(i,m.labelField):"",n._radioDisabled=m.checkMethod&&!m.checkMethod({$table:j,row:i});break;default:c.original?l=(0,_util.getCellValue)(i,e):(l=j.getCellLabel(i,e),"html"===e.type?(htmlCellElem.innerHTML=l,l=htmlCellElem.innerText.trim()):(a=j.getCellElement(i,e))&&!(0,_dom.hasClass)(a,"is--progress")&&(l=a.innerText.trim()))}n[e.id]=b(l)}),n})},T=(e,t,l)=>{var r=n.value,o=l.editRender||l.cellRender;let a=l.footerExportMethod;a=(a=!a&&o&&o.name&&(o=renderer.get(o.name))?o.tableFooterExportMethod||o.footerExportMethod:a)||r.footerExportMethod;o=j.getVTColumnIndex(l);return a?a({$table:j,items:t,itemIndex:o,row:t,_columnIndex:o,column:l,options:e}):_xeUtils.default.isArray(t)?_xeUtils.default.toValueString(t[o]):_xeUtils.default.get(t,l.field)},u=(e,l,r,t)=>{let o=csvBOM;return l.isHeader&&(o+=r.map(e=>toTxtCellLabel(C(l,e))).join(",")+enterSymbol),t.forEach(t=>{o+=r.map(e=>toTxtCellLabel(getCsvCellTypeLabel(e,t[e.id]))).join(",")+enterSymbol}),l.isFooter&&(t=D.footerTableData,getFooterData(e,l,t).forEach(t=>{o+=r.map(e=>toTxtCellLabel(T(l,t,e))).join(",")+enterSymbol})),o},p=(e,l,r,t)=>{let o="";return l.isHeader&&(o+=r.map(e=>toTxtCellLabel(C(l,e))).join("\t")+enterSymbol),t.forEach(t=>{o+=r.map(e=>toTxtCellLabel(t[e.id])).join("\t")+enterSymbol}),l.isFooter&&(t=D.footerTableData,getFooterData(e,l,t).forEach(t=>{o+=r.map(e=>toTxtCellLabel(T(l,t,e))).join("\t")+enterSymbol})),o},k=(e,t,l)=>{e=e[t],t=_xeUtils.default.isUndefined(e)||_xeUtils.default.isNull(e)?l:e;let r="title"===t||(!0===t||"tooltip"===t)||"ellipsis"===t;var{scrollXLoad:l,scrollYLoad:e}=D;return r=l||e?r||!0:r},m=(s,e,t)=>{let{id:d,border:l,treeConfig:r,headerAlign:n,align:c,footerAlign:a,showOverflow:u,showHeaderOverflow:p}=I,{isAllSelected:m,isIndeterminate:o}=D,h=O.mergeBodyCellMaps,i=N.value,{print:x,isHeader:g,isFooter:f,isColgroup:v,isMerge:b,colgroups:_,original:y}=s,$="check-all";let w=[`<table class="${["vxe-table","border--"+toTableBorder(l),x?"is--print":"",g?"is--header":""].filter(e=>e).join(" ")}" border="0" cellspacing="0" cellpadding="0">`,`<colgroup>${e.map(e=>`<col style="width:${e.renderWidth}px">`).join("")}</colgroup>`];g&&(w.push("<thead>"),v&&!y?_.forEach(e=>{w.push(`<tr>${e.map(t=>{var e=t.headerAlign||t.align||n||c,l=k(t,"showHeaderOverflow",p)?["col--ellipsis"]:[],r=C(s,t);let o=0,a=0;_xeUtils.default.eachTree([t],e=>{e.childNodes&&t.childNodes.length||a++,o+=e.renderWidth},{children:"childNodes"});var i=o-a;return e&&l.push("col--"+e),"checkbox"===t.type?`<th class="${l.join(" ")}" colspan="${t._colSpan}" rowspan="${t._rowSpan}"><div ${x?"":`style="width: ${i}px"`}><input type="checkbox" class="${$}" ${m?"checked":""}><span>${r}</span></div></th>`:`<th class="${l.join(" ")}" colspan="${t._colSpan}" rowspan="${t._rowSpan}" title="${r}"><div ${x?"":`style="width: ${i}px"`}><span>${(0,_utils.formatText)(r,!0)}</span></div></th>`}).join("")}</tr>`)}):w.push(`<tr>${e.map(e=>{var t=e.headerAlign||e.align||n||c,l=k(e,"showHeaderOverflow",p)?["col--ellipsis"]:[],r=C(s,e);return t&&l.push("col--"+t),"checkbox"===e.type?`<th class="${l.join(" ")}"><div ${x?"":`style="width: ${e.renderWidth}px"`}><input type="checkbox" class="${$}" ${m?"checked":""}><span>${r}</span></div></th>`:`<th class="${l.join(" ")}" title="${r}"><div ${x?"":`style="width: ${e.renderWidth}px"`}><span>${(0,_utils.formatText)(r,!0)}</span></div></th>`}).join("")}</tr>`),w.push("</thead>")),t.length&&(w.push("<tbody>"),r?t.forEach(o=>{w.push("<tr>"+e.map(t=>{var l=t.id,e=t.align||c,r=k(t,"showOverflow",u)?["col--ellipsis"]:[],l=o[l];if(e&&r.push("col--"+e),t.treeNode){let e="";return o._hasChild&&(e=`<i class="${o._expand?"vxe-table--tree-fold-icon":"vxe-table--tree-unfold-icon"}"></i>`),r.push("vxe-table--tree-node"),"radio"===t.type?`<td class="${r.join(" ")}" title="${l}"><div ${x?"":`style="width: ${t.renderWidth}px"`}><div class="vxe-table--tree-node-wrapper" style="padding-left: ${o._level*i.indent}px"><div class="vxe-table--tree-icon-wrapper">${e}</div><div class="vxe-table--tree-cell"><input type="radio" name="radio_${d}" ${o._radioDisabled?"disabled ":""}${getBooleanValue(l)?"checked":""}><span>${o._radioLabel}</span></div></div></div></td>`:"checkbox"===t.type?`<td class="${r.join(" ")}" title="${l}"><div ${x?"":`style="width: ${t.renderWidth}px"`}><div class="vxe-table--tree-node-wrapper" style="padding-left: ${o._level*i.indent}px"><div class="vxe-table--tree-icon-wrapper">${e}</div><div class="vxe-table--tree-cell"><input type="checkbox" ${o._checkboxDisabled?"disabled ":""}${getBooleanValue(l)?"checked":""}><span>${o._checkboxLabel}</span></div></div></div></td>`:`<td class="${r.join(" ")}" title="${l}"><div ${x?"":`style="width: ${t.renderWidth}px"`}><div class="vxe-table--tree-node-wrapper" style="padding-left: ${o._level*i.indent}px"><div class="vxe-table--tree-icon-wrapper">${e}</div><div class="vxe-table--tree-cell">${l}</div></div></div></td>`}return"radio"===t.type?`<td class="${r.join(" ")}"><div ${x?"":`style="width: ${t.renderWidth}px"`}><input type="radio" name="radio_${d}" ${o._radioDisabled?"disabled ":""}${getBooleanValue(l)?"checked":""}><span>${o._radioLabel}</span></div></td>`:"checkbox"===t.type?`<td class="${r.join(" ")}"><div ${x?"":`style="width: ${t.renderWidth}px"`}><input type="checkbox" ${o._checkboxDisabled?"disabled ":""}${getBooleanValue(l)?"checked":""}><span>${o._checkboxLabel}</span></div></td>`:`<td class="${r.join(" ")}" title="${l}"><div ${x?"":`style="width: ${t.renderWidth}px"`}>${(0,_utils.formatText)(l,!0)}</div></td>`}).join("")+"</tr>")}):t.forEach(n=>{w.push("<tr>"+e.map(e=>{var t=e.align||c,l=k(e,"showOverflow",u)?["col--ellipsis"]:[],r=n[e.id];let o=1,a=1;if(b){var i=j.getVTRowIndex(n._row),s=j.getVTColumnIndex(e),i=h[i+":"+s];if(i){var{rowspan:s,colspan:i}=i;if(!s||!i)return"";1<s&&(o=s),1<i&&(a=i)}}return t&&l.push("col--"+t),"radio"===e.type?`<td class="${l.join(" ")}" rowspan="${o}" colspan="${a}"><div ${x?"":`style="width: ${e.renderWidth}px"`}><input type="radio" name="radio_${d}" ${n._radioDisabled?"disabled ":""}${getBooleanValue(r)?"checked":""}><span>${n._radioLabel}</span></div></td>`:"checkbox"===e.type?`<td class="${l.join(" ")}" rowspan="${o}" colspan="${a}"><div ${x?"":`style="width: ${e.renderWidth}px"`}><input type="checkbox" ${n._checkboxDisabled?"disabled ":""}${getBooleanValue(r)?"checked":""}><span>${n._checkboxLabel}</span></div></td>`:`<td class="${l.join(" ")}" rowspan="${o}" colspan="${a}" title="${r}"><div ${x?"":`style="width: ${e.renderWidth}px"`}>${(0,_utils.formatText)(r,!0)}</div></td>`}).join("")+"</tr>")}),w.push("</tbody>")),f&&(t=D.footerTableData,(t=getFooterData(j,s,t)).length)&&(w.push("<tfoot>"),t.forEach(o=>{w.push(`<tr>${e.map(e=>{var t=e.footerAlign||e.align||a||c,l=k(e,"showOverflow",u)?["col--ellipsis"]:[],r=T(s,o,e);return t&&l.push("col--"+t),`<td class="${l.join(" ")}" title="${r}"><div ${x?"":`style="width: ${e.renderWidth}px"`}>${(0,_utils.formatText)(r,!0)}</div></td>`}).join("")}</tr>`)}),w.push("</tfoot>"));t=!m&&o?`<script>(function(){var a=document.querySelector(".${$}");if(a){a.indeterminate=true}})()</script>`:"";return w.push("</table>",t),x?w.join(""):(0,_util2.createHtmlPage)(s,w.join(""))},h=(l,e,t)=>{let r=['<?xml version="1.0"?>','<?mso-application progid="Excel.Sheet"?>','<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40">','<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">',"<Version>16.00</Version>","</DocumentProperties>",'<ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">',"<WindowHeight>7920</WindowHeight>","<WindowWidth>21570</WindowWidth>","<WindowTopX>32767</WindowTopX>","<WindowTopY>32767</WindowTopY>","<ProtectStructure>False</ProtectStructure>","<ProtectWindows>False</ProtectWindows>","</ExcelWorkbook>",`<Worksheet ss:Name="${l.sheetName}">`,"<Table>",e.map(e=>`<Column ss:Width="${e.renderWidth}"/>`).join("")].join("");return l.isHeader&&(r+=`<Row>${e.map(e=>`<Cell><Data ss:Type="String">${C(l,e)}</Data></Cell>`).join("")}</Row>`),t.forEach(t=>{r+="<Row>"+e.map(e=>`<Cell><Data ss:Type="String">${t[e.id]}</Data></Cell>`).join("")+"</Row>"}),l.isFooter&&(t=D.footerTableData,getFooterData(j,l,t).forEach(t=>{r+=`<Row>${e.map(e=>`<Cell><Data ss:Type="String">${T(l,t,e)}</Data></Cell>`).join("")}</Row>`})),r+"</Table></Worksheet></Workbook>"},S=l=>{let{remote:r,columns:o,colgroups:a,exportMethod:i,afterExportMethod:t}=l;return new Promise(t=>{if(r){var e={options:l,$table:j,$grid:R};t(i?i(e):e)}else{let e=(e=>{let{columns:t,dataFilterMethod:l}=e,r=e.data;return l&&(r=r.filter((e,t)=>l({$table:j,row:e,$rowIndex:t}))),d(e,t,r)})(l);t(j.preventEvent(null,"event.export",{options:l,columns:o,colgroups:a,datas:e},()=>((e,t)=>{var{filename:l,type:r,download:o}=e;if(!o)return o=(0,_util2.getExportBlobByContent)(t,e),Promise.resolve({type:r,content:t,blob:o});_ui.VxeUI.saveFile&&_ui.VxeUI.saveFile({filename:l,type:r,content:t}).then(()=>{!1!==e.message&&_ui.VxeUI.modal&&_ui.VxeUI.modal.message({content:getI18n("vxe.table.expSuccess"),status:"success"})})})(l,((e,t,l,r)=>{if(l.length)switch(t.type){case"csv":return u(e,t,l,r);case"txt":return p(e,t,l,r);case"html":return m(t,l,r);case"xml":return h(t,l,r)}return""})(j,l,o,e))))}}).then(e=>(clearColumnConvert(o),l.print||t&&t({status:!0,options:l,$table:j,$grid:R}),Object.assign({status:!0},e))).catch(()=>{clearColumnConvert(o),l.print||t&&t({status:!1,options:l,$table:j,$grid:R});return Promise.reject({status:!1})})},a=(a,i)=>{let{importMethod:s,afterImportMethod:t}=i,{type:n,filename:d}=(0,_utils.parseFile)(a);var e=c.value;return s||_xeUtils.default.includes(_xeUtils.default.keys(e._typeMaps),n)?new Promise((t,l)=>{let e=e=>{t(e),O._importResolve=null,O._importReject=null},r=e=>{l(e),O._importResolve=null,O._importReject=null};if(O._importResolve=e,O._importReject=r,window.FileReader){let t=Object.assign({mode:"insertTop"},i,{type:n,filename:d});var o;t.remote?s?Promise.resolve(s({file:a,options:t,$table:j})).then(()=>{e({status:!0})}).catch(()=>{e({status:!0})}):e({status:!0}):(o=O.tableFullColumn,j.preventEvent(null,"event.import",{file:a,options:t,columns:o},()=>{var e=new FileReader;e.onerror=()=>{(0,_log.errLog)("vxe.error.notType",[n]),r({status:!1})},e.onload=e=>{((e,l)=>{let{tableFullColumn:t,_importResolve:r,_importReject:o}=O,a={fields:[],rows:[]},i={},s={};t.forEach(e=>{var t=e.field,l=e.getTitle();t&&(i[t]=e),l&&(s[e.getTitle()]=e)});var n={fieldMaps:i,titleMaps:s};switch(l.type){case"csv":a=parseCsv(n,e);break;case"txt":a=parseTxt(n,e);break;case"html":a=parseHTML(n,e);break;case"xml":a=parseXML(n,e)}let{fields:d,rows:c}=a;d.some(e=>i[e]||s[e])?j.createData(c).then(e=>{let t;return"insert"!==l.mode&&"insertBottom"!==l.mode||(t=j.insertAt(e,-1)),t="insertTop"===l.mode?j.insert(e):j.reloadData(e),!1!==l.message&&_ui.VxeUI.modal&&_ui.VxeUI.modal.message({content:getI18n("vxe.table.impSuccess",[c.length]),status:"success"}),t.then(()=>{r&&r({status:!0})})}):!1!==l.message&&(_ui.VxeUI.modal&&_ui.VxeUI.modal.message({content:getI18n("vxe.error.impFields"),status:"error"}),o)&&o({status:!1})})(e.target.result,t)},e.readAsText(a,t.encoding||"UTF-8")}))}else(0,_log.errLog)("vxe.error.notExp"),e({status:!0})}).then(()=>{t&&t({status:!0,options:i,$table:j})}).catch(e=>(t&&t({status:!1,options:i,$table:j}),Promise.reject(e))):(!1!==i.message&&_ui.VxeUI.modal&&_ui.VxeUI.modal.message({content:getI18n("vxe.error.notType",[n]),status:"error"}),Promise.reject({status:!1}))},B=(e,r,t)=>t.some(e=>{var t,l;return(0,_util.isColumnInfo)(e)?r.id===e.id:_xeUtils.default.isString(e)?r.field===e:(t=e.id||e.colId,l=e.type,e=e.field,t?r.id===t:e&&l?r.field===e&&r.type===l:e?r.field===e:!!l&&r.type===l)}),P=(e,t,l,r)=>(!r||!_xeUtils.default.includes(r,t.field))&&(l?!!_xeUtils.default.includes(l,t.field):e.original?!!t.field:defaultFilterExportColumn(t)),l=(e,t)=>{var{treeConfig:l,showHeader:r,showFooter:o}=I,{initStore:a,isGroup:i,footerTableData:s,exportStore:n,exportParams:d}=D,{collectColumn:c,mergeBodyList:u,mergeFooterList:p}=O,m=L.value,h=E.value,x=j.getCheckboxRecords(),g=R?R.getComputeMaps().computeProxyOpts.value:{},s=!!s.length,u=!(!u.length&&!p.length);let f=Object.assign({message:!0,isHeader:r,isTitle:r,isFooter:o,isColgroup:i,isMerge:u,useStyle:!0,current:"current",modes:(g.ajax&&g.ajax.queryAll?["all"]:[]).concat(["current","selected","empty"])},e);p=f.types||_xeUtils.default.keys(m._typeMaps),r=f.modes||[];let v=h.checkMethod;o=c.slice(0);let{columns:b,excludeFields:_,includeFields:y}=f;g=p.map(e=>({value:e,label:getI18n("vxe.export.types."+e)})),e=r.map(e=>e&&e.value?{value:e.value,label:e.label||e.value}:{value:e,label:getI18n("vxe.export.modes."+e)});_xeUtils.default.eachTree(o,(e,t,l,r,o)=>{var a=e.children&&0<e.children.length;let i=!1;i=b&&b.length?B(f,e,b):_||y?P(f,e,y,_):e.visible&&(a||defaultFilterExportColumn(e)),e.checked=i,e.halfChecked=!1,e.disabled=o&&o.disabled||!!v&&!v({$table:j,column:e})}),Object.assign(n,{columns:o,typeList:g,modeList:e,hasFooter:s,hasMerge:u,hasTree:l,isPrint:t,hasColgroup:i,visible:!0}),Object.assign(d,{mode:x.length?"selected":"current"},f);let{filename:$,sheetName:w,mode:C,type:T}=d;return $&&(_xeUtils.default.isFunction($)?d.filename=$({options:f,$table:j,$grid:R}):d.filename=""+$),w&&(_xeUtils.default.isFunction(w)?d.sheetName=w({options:f,$table:j,$grid:R}):d.sheetName=""+w),e.some(e=>e.value===C)||(d.mode=e[0].value),g.some(e=>e.value===T)||(d.type=g[0].value),a.export=!0,(0,_vue.nextTick)()};var e=()=>_ui.VxeUI.modal?_ui.VxeUI.modal.close("VXE_EXPORT_MODAL"):Promise.resolve();let x={exportData(s){var{treeConfig:e,showHeader:n,showFooter:d}=I,t=D.isGroup;let{tableFullColumn:a,afterFullData:l,collectColumn:r,mergeBodyList:o,mergeFooterList:i}=O;var c=L.value,u=N.value;let p=R?R.getComputeMaps().computeProxyOpts.value:{};var m=!(!o.length&&!i.length);let h=Object.assign({message:!0,isHeader:n,isTitle:n,isFooter:d,isColgroup:t,isMerge:m,useStyle:!0,current:"current",modes:(p.ajax&&p.ajax.queryAll?["all"]:[]).concat(["current","selected","empty"]),download:!0,type:"csv"},c,s),{filename:x,sheetName:g,type:f,mode:v,columns:b,original:_,columnFilterMethod:y,beforeExportMethod:$,includeFields:w,excludeFields:C}=h,T=[],E=j.getCheckboxRecords(),k=(v=v||(E.length?"selected":"current"),!1),M=[],F=(M=b&&b.length?(k=!0,b):_xeUtils.default.searchTree(r,e=>{var t=e.children&&0<e.children.length;let l=!1;return l=b&&b.length?B(h,e,b):C||w?P(h,e,w,C):e.visible&&(t||defaultFilterExportColumn(e))},{children:"children",mapChildren:"childNodes",original:!0}),Object.assign({},h,{filename:"",sheetName:""})),U=(k||y||(y=({column:e})=>(!C||!_xeUtils.default.includes(C,e.field))&&(w?!!_xeUtils.default.includes(w,e.field):_?!!e.field:defaultFilterExportColumn(e)),F.columnFilterMethod=y),T=M?(F._isCustomColumn=!0,_xeUtils.default.searchTree(_xeUtils.default.mapTree(M,e=>{let r;if(e){if((0,_util.isColumnInfo)(e))r=e;else if(_xeUtils.default.isString(e))r=j.getColumnByField(e);else{var o=e.id||e.colId;let t=e.type,l=e.field;o?r=j.getColumnById(o):l&&t?r=a.find(e=>e.field===l&&e.type===t):l?r=j.getColumnByField(l):t&&(r=a.find(e=>e.type===t))}return r||{}}},{children:"childNodes",mapChildren:"_children"}),(e,t)=>(0,_util.isColumnInfo)(e)&&(!y||y({$table:j,column:e,$columnIndex:t})),{children:"_children",mapChildren:"childNodes",original:!0})):_xeUtils.default.searchTree(t?r:a,(e,t)=>e.visible&&(!y||y({$table:j,column:e,$columnIndex:t})),{children:"children",mapChildren:"childNodes",original:!0}),[]);if(_xeUtils.default.eachTree(T,e=>{e.children&&e.children.length||U.push(e)},{children:"childNodes"}),F.columns=U,F.colgroups=convertToRows(T),x&&(_xeUtils.default.isFunction(x)?F.filename=x({options:h,$table:j,$grid:R}):F.filename=""+x),F.filename||(F.filename=getI18n(F.original?"vxe.table.expOriginFilename":"vxe.table.expFilename",[_xeUtils.default.toDateString(Date.now(),"yyyyMMddHHmmss")])),g&&(_xeUtils.default.isFunction(g)?F.sheetName=g({options:h,$table:j,$grid:R}):F.sheetName=""+g),F.sheetName||(F.sheetName=document.title||""),!F.exportMethod&&!_xeUtils.default.includes(_xeUtils.default.keys(c._typeMaps),f))return(0,_log.errLog)("vxe.error.notType",[f]),["xlsx","pdf"].includes(f)&&(0,_log.warnLog)("vxe.error.reqPlugin",[4,"plugin-export-xlsx"]),Promise.reject({status:!1});if(F.print||$&&$({options:F,$table:j,$grid:R}),!F.data){if(F.data=[],"selected"===v)-1<["html","pdf"].indexOf(f)&&e?F.data=_xeUtils.default.searchTree(j.getTableData().fullData,e=>-1<j.findRowIndexOf(E,e),Object.assign({},u,{data:"_row"})):F.data=E;else if("all"===v&&(R||(0,_log.errLog)("vxe.error.errProp",["all","mode=current,selected"]),R)&&!F.remote){n=R.reactData,d=R.getComputeMaps().computeProxyOpts;let e=d.value;m=n.sortData;let{beforeQueryAll:t,afterQueryAll:r,ajax:l={}}=e,o=e.response||e.props||{};s=l.queryAll;let a=l.queryAllSuccess,i=l.queryAllError;if(s||(0,_log.errLog)("vxe.error.notFunc",["proxy-config.ajax.queryAll"]),s){let l={$table:j,$grid:R,sort:m.length?m[0]:{},sorts:m,filters:n.filterData,form:n.formData,options:F};return Promise.resolve((t||s)(l)).then(e=>{var t=o.list;return F.data=(t?_xeUtils.default.isFunction(t)?t({data:e,$grid:R}):_xeUtils.default.get(e,t):e)||[],r&&r(l),a&&a(Object.assign(Object.assign({},l),{response:e})),S(F)}).catch(e=>{i&&i(Object.assign(Object.assign({},l),{response:e}))})}}"current"===v&&(F.data=l)}return S(F)},importByFile(e,t){var t=Object.assign({},t),l=t.beforeImportMethod;return l&&l({options:t,$table:j}),a(e,t)},importData(e){var t=c.value;let l=Object.assign({types:_xeUtils.default.keys(t._typeMaps)},t,e),{beforeImportMethod:r,afterImportMethod:o}=l;return r&&r({options:l,$table:j}),_ui.VxeUI.readFile(l).catch(e=>(o&&o({status:!1,options:l,$table:j}),Promise.reject(e))).then(e=>{e=e.file;return a(e,l)})},saveFile(e){return _ui.VxeUI.saveFile(e)},readFile(e){return _ui.VxeUI.readFile(e)},print(e){var t=i.value;let l=Object.assign({original:!1},t,e,{type:"html",download:!1,remote:!1,print:!0});t=l.sheetName;let r="",o=(r=(r=t?_xeUtils.default.isFunction(t)?t({options:l,$table:j,$grid:R}):""+t:r)||document.title||"",l.beforePrintMethod),a=l.html||l.content;return new Promise((e,t)=>{_ui.VxeUI.print?a?e(_ui.VxeUI.print({title:r,html:a,customStyle:l.style,beforeMethod:o?({html:e})=>o({html:e,content:e,options:l,$table:j}):void 0})):e(x.exportData(l).then(({content:e})=>_ui.VxeUI.print({title:r,html:e,customStyle:l.style,beforeMethod:o?({html:e})=>o({html:e,content:e,options:l,$table:j}):void 0}))):t({status:!1})})},getPrintHtml(e){var t=i.value,t=Object.assign({original:!1},t,e,{type:"html",download:!1,remote:!1,print:!0});return j.exportData(t).then(({content:e})=>({html:e}))},closeImport(){return _ui.VxeUI.modal?_ui.VxeUI.modal.close("VXE_IMPORT_MODAL"):Promise.resolve()},openImport(e){var{treeConfig:t,importConfig:l}=I;let{initStore:r,importStore:o,importParams:a}=D;var i=c.value,i=Object.assign({mode:"insertTop",message:!0,types:_xeUtils.default.keys(i._typeMaps),modes:["insertTop","covering"]},i,e),e=i.types||[],s=i.modes||[];!t?(l||(0,_log.errLog)("vxe.error.reqProp",["import-config"]),t=e.map(e=>({value:e,label:getI18n("vxe.export.types."+e)})),l=s.map(e=>e&&e.value?{value:e.value,label:e.label||e.value}:{value:e,label:getI18n("vxe.import.modes."+e)}),Object.assign(o,{file:null,type:"",filename:"",modeList:l,typeList:t,visible:!0}),Object.assign(a,i),l.some(e=>e.value===a.mode)||(a.mode=l[0].value),r.import=!0):i.message&&_ui.VxeUI.modal&&_ui.VxeUI.modal.message({content:getI18n("vxe.error.treeNotImp"),status:"error"})},closeExport:e,openExport(e){var t=L.value,t=Object.assign({message:!0,types:_xeUtils.default.keys(t._typeMaps)},t,e);return I.exportConfig||(0,_log.errLog)("vxe.error.reqProp",["export-config"]),l(t)},closePrint:e,openPrint(e){var t=i.value,t=Object.assign({message:!0},t,e);return I.printConfig||(0,_log.errLog)("vxe.error.reqProp",["print-config"]),l(t,!0)}};return x},setupGrid(e){return e.extendTableMethods(tableExportMethodKeys)}});