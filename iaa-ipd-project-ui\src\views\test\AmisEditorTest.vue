<template>
  <div class="amis-editor-test">
    <h2>Amis Editor 测试页面</h2>
    <div class="editor-container">
      <AmisEditor />
    </div>
  </div>
</template>

<script lang="ts" setup>
import AmisEditor from '@/components/AmisEditor/index.vue'
</script>

<style scoped>
.amis-editor-test {
  padding: 20px;
}

.editor-container {
  height: 600px;
  border: 1px solid #ddd;
  border-radius: 4px;
}
</style>
