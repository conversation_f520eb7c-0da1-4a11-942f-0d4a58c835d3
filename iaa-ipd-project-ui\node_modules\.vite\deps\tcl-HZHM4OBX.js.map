{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/tcl/tcl.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" }\n    ],\n    surroundingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" }\n    ]\n};\nexport var language = {\n    tokenPostfix: '.tcl',\n    specialFunctions: [\n        'set',\n        'unset',\n        'rename',\n        'variable',\n        'proc',\n        'coroutine',\n        'foreach',\n        'incr',\n        'append',\n        'lappend',\n        'linsert',\n        'lreplace'\n    ],\n    mainFunctions: [\n        'if',\n        'then',\n        'elseif',\n        'else',\n        'case',\n        'switch',\n        'while',\n        'for',\n        'break',\n        'continue',\n        'return',\n        'package',\n        'namespace',\n        'catch',\n        'exit',\n        'eval',\n        'expr',\n        'uplevel',\n        'upvar'\n    ],\n    builtinFunctions: [\n        'file',\n        'info',\n        'concat',\n        'join',\n        'lindex',\n        'list',\n        'llength',\n        'lrange',\n        'lsearch',\n        'lsort',\n        'split',\n        'array',\n        'parray',\n        'binary',\n        'format',\n        'regexp',\n        'regsub',\n        'scan',\n        'string',\n        'subst',\n        'dict',\n        'cd',\n        'clock',\n        'exec',\n        'glob',\n        'pid',\n        'pwd',\n        'close',\n        'eof',\n        'fblocked',\n        'fconfigure',\n        'fcopy',\n        'fileevent',\n        'flush',\n        'gets',\n        'open',\n        'puts',\n        'read',\n        'seek',\n        'socket',\n        'tell',\n        'interp',\n        'after',\n        'auto_execok',\n        'auto_load',\n        'auto_mkindex',\n        'auto_reset',\n        'bgerror',\n        'error',\n        'global',\n        'history',\n        'load',\n        'source',\n        'time',\n        'trace',\n        'unknown',\n        'unset',\n        'update',\n        'vwait',\n        'winfo',\n        'wm',\n        'bind',\n        'event',\n        'pack',\n        'place',\n        'grid',\n        'font',\n        'bell',\n        'clipboard',\n        'destroy',\n        'focus',\n        'grab',\n        'lower',\n        'option',\n        'raise',\n        'selection',\n        'send',\n        'tk',\n        'tkwait',\n        'tk_bisque',\n        'tk_focusNext',\n        'tk_focusPrev',\n        'tk_focusFollowsMouse',\n        'tk_popup',\n        'tk_setPalette'\n    ],\n    symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n    brackets: [\n        { open: '(', close: ')', token: 'delimiter.parenthesis' },\n        { open: '{', close: '}', token: 'delimiter.curly' },\n        { open: '[', close: ']', token: 'delimiter.square' }\n    ],\n    escapes: /\\\\(?:[abfnrtv\\\\\"'\\[\\]\\{\\};\\$]|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n    variables: /(?:\\$+(?:(?:\\:\\:?)?[a-zA-Z_]\\w*)+)/,\n    tokenizer: {\n        root: [\n            // identifiers and keywords\n            [\n                /[a-zA-Z_]\\w*/,\n                {\n                    cases: {\n                        '@specialFunctions': {\n                            token: 'keyword.flow',\n                            next: '@specialFunc'\n                        },\n                        '@mainFunctions': 'keyword',\n                        '@builtinFunctions': 'variable',\n                        '@default': 'operator.scss'\n                    }\n                }\n            ],\n            [/\\s+\\-+(?!\\d|\\.)\\w*|{\\*}/, 'metatag'],\n            // whitespace\n            { include: '@whitespace' },\n            // delimiters and operators\n            [/[{}()\\[\\]]/, '@brackets'],\n            [/@symbols/, 'operator'],\n            [/\\$+(?:\\:\\:)?\\{/, { token: 'identifier', next: '@nestedVariable' }],\n            [/@variables/, 'type.identifier'],\n            [/\\.(?!\\d|\\.)[\\w\\-]*/, 'operator.sql'],\n            // numbers\n            [/\\d+(\\.\\d+)?/, 'number'],\n            [/\\d+/, 'number'],\n            // delimiter\n            [/;/, 'delimiter'],\n            // strings\n            [/\"/, { token: 'string.quote', bracket: '@open', next: '@dstring' }],\n            [/'/, { token: 'string.quote', bracket: '@open', next: '@sstring' }]\n        ],\n        dstring: [\n            [/\\[/, { token: '@brackets', next: '@nestedCall' }],\n            [/\\$+(?:\\:\\:)?\\{/, { token: 'identifier', next: '@nestedVariable' }],\n            [/@variables/, 'type.identifier'],\n            [/[^\\\\$\\[\\]\"]+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/\"/, { token: 'string.quote', bracket: '@close', next: '@pop' }]\n        ],\n        sstring: [\n            [/\\[/, { token: '@brackets', next: '@nestedCall' }],\n            [/\\$+(?:\\:\\:)?\\{/, { token: 'identifier', next: '@nestedVariable' }],\n            [/@variables/, 'type.identifier'],\n            [/[^\\\\$\\[\\]']+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/'/, { token: 'string.quote', bracket: '@close', next: '@pop' }]\n        ],\n        whitespace: [\n            [/[ \\t\\r\\n]+/, 'white'],\n            [/#.*\\\\$/, { token: 'comment', next: '@newlineComment' }],\n            [/#.*(?!\\\\)$/, 'comment']\n        ],\n        newlineComment: [\n            [/.*\\\\$/, 'comment'],\n            [/.*(?!\\\\)$/, { token: 'comment', next: '@pop' }]\n        ],\n        nestedVariable: [\n            [/[^\\{\\}\\$]+/, 'type.identifier'],\n            [/\\}/, { token: 'identifier', next: '@pop' }]\n        ],\n        nestedCall: [\n            [/\\[/, { token: '@brackets', next: '@nestedCall' }],\n            [/\\]/, { token: '@brackets', next: '@pop' }],\n            { include: 'root' }\n        ],\n        specialFunc: [\n            [/\"/, { token: 'string', next: '@dstring' }],\n            [/'/, { token: 'string', next: '@sstring' }],\n            [/\\S+/, { token: 'type', next: '@pop' }]\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,kBAAkB;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,eAAe;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,kBAAkB;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,EACT,UAAU;AAAA,IACN,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,EACvD;AAAA,EACA,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,IACP,MAAM;AAAA;AAAA,MAEF;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,qBAAqB;AAAA,cACjB,OAAO;AAAA,cACP,MAAM;AAAA,YACV;AAAA,YACA,kBAAkB;AAAA,YAClB,qBAAqB;AAAA,YACrB,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,CAAC,2BAA2B,SAAS;AAAA;AAAA,MAErC,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,YAAY,UAAU;AAAA,MACvB,CAAC,kBAAkB,EAAE,OAAO,cAAc,MAAM,kBAAkB,CAAC;AAAA,MACnE,CAAC,cAAc,iBAAiB;AAAA,MAChC,CAAC,sBAAsB,cAAc;AAAA;AAAA,MAErC,CAAC,eAAe,QAAQ;AAAA,MACxB,CAAC,OAAO,QAAQ;AAAA;AAAA,MAEhB,CAAC,KAAK,WAAW;AAAA;AAAA,MAEjB,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,WAAW,CAAC;AAAA,MACnE,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,WAAW,CAAC;AAAA,IACvE;AAAA,IACA,SAAS;AAAA,MACL,CAAC,MAAM,EAAE,OAAO,aAAa,MAAM,cAAc,CAAC;AAAA,MAClD,CAAC,kBAAkB,EAAE,OAAO,cAAc,MAAM,kBAAkB,CAAC;AAAA,MACnE,CAAC,cAAc,iBAAiB;AAAA,MAChC,CAAC,gBAAgB,QAAQ;AAAA,MACzB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IACpE;AAAA,IACA,SAAS;AAAA,MACL,CAAC,MAAM,EAAE,OAAO,aAAa,MAAM,cAAc,CAAC;AAAA,MAClD,CAAC,kBAAkB,EAAE,OAAO,cAAc,MAAM,kBAAkB,CAAC;AAAA,MACnE,CAAC,cAAc,iBAAiB;AAAA,MAChC,CAAC,gBAAgB,QAAQ;AAAA,MACzB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IACpE;AAAA,IACA,YAAY;AAAA,MACR,CAAC,cAAc,OAAO;AAAA,MACtB,CAAC,UAAU,EAAE,OAAO,WAAW,MAAM,kBAAkB,CAAC;AAAA,MACxD,CAAC,cAAc,SAAS;AAAA,IAC5B;AAAA,IACA,gBAAgB;AAAA,MACZ,CAAC,SAAS,SAAS;AAAA,MACnB,CAAC,aAAa,EAAE,OAAO,WAAW,MAAM,OAAO,CAAC;AAAA,IACpD;AAAA,IACA,gBAAgB;AAAA,MACZ,CAAC,cAAc,iBAAiB;AAAA,MAChC,CAAC,MAAM,EAAE,OAAO,cAAc,MAAM,OAAO,CAAC;AAAA,IAChD;AAAA,IACA,YAAY;AAAA,MACR,CAAC,MAAM,EAAE,OAAO,aAAa,MAAM,cAAc,CAAC;AAAA,MAClD,CAAC,MAAM,EAAE,OAAO,aAAa,MAAM,OAAO,CAAC;AAAA,MAC3C,EAAE,SAAS,OAAO;AAAA,IACtB;AAAA,IACA,aAAa;AAAA,MACT,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,WAAW,CAAC;AAAA,MAC3C,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,WAAW,CAAC;AAAA,MAC3C,CAAC,OAAO,EAAE,OAAO,QAAQ,MAAM,OAAO,CAAC;AAAA,IAC3C;AAAA,EACJ;AACJ;", "names": []}