{"version": 3, "sources": ["../../echarts/lib/component/dataset/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\n/**\n * This module is imported by echarts directly.\n *\n * Notice:\n * Always keep this file exists for backward compatibility.\n * Because before 4.1.0, dataset is an optional component,\n * some users may import this module manually.\n */\nimport ComponentModel from '../../model/Component.js';\nimport ComponentView from '../../view/Component.js';\nimport { SERIES_LAYOUT_BY_COLUMN } from '../../util/types.js';\nimport { disableTransformOptionMerge, SourceManager } from '../../data/helper/sourceManager.js';\nvar DatasetModel = /** @class */function (_super) {\n  __extends(DatasetModel, _super);\n  function DatasetModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'dataset';\n    return _this;\n  }\n  DatasetModel.prototype.init = function (option, parentModel, ecModel) {\n    _super.prototype.init.call(this, option, parentModel, ecModel);\n    this._sourceManager = new SourceManager(this);\n    disableTransformOptionMerge(this);\n  };\n  DatasetModel.prototype.mergeOption = function (newOption, ecModel) {\n    _super.prototype.mergeOption.call(this, newOption, ecModel);\n    disableTransformOptionMerge(this);\n  };\n  DatasetModel.prototype.optionUpdated = function () {\n    this._sourceManager.dirty();\n  };\n  DatasetModel.prototype.getSourceManager = function () {\n    return this._sourceManager;\n  };\n  DatasetModel.type = 'dataset';\n  DatasetModel.defaultOption = {\n    seriesLayoutBy: SERIES_LAYOUT_BY_COLUMN\n  };\n  return DatasetModel;\n}(ComponentModel);\nexport { DatasetModel };\nvar DatasetView = /** @class */function (_super) {\n  __extends(DatasetView, _super);\n  function DatasetView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'dataset';\n    return _this;\n  }\n  DatasetView.type = 'dataset';\n  return DatasetView;\n}(ComponentView);\nexport function install(registers) {\n  registers.registerComponentModel(DatasetModel);\n  registers.registerComponentView(DatasetView);\n}"], "mappings": ";;;;;;;;;;;;AAwDA,IAAI;AAAA;AAAA,EAA4B,SAAU,QAAQ;AAChD,cAAUA,eAAc,MAAM;AAC9B,aAASA,gBAAe;AACtB,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,OAAO;AACb,aAAO;AAAA,IACT;AACA,IAAAA,cAAa,UAAU,OAAO,SAAU,QAAQ,aAAa,SAAS;AACpE,aAAO,UAAU,KAAK,KAAK,MAAM,QAAQ,aAAa,OAAO;AAC7D,WAAK,iBAAiB,IAAI,cAAc,IAAI;AAC5C,kCAA4B,IAAI;AAAA,IAClC;AACA,IAAAA,cAAa,UAAU,cAAc,SAAU,WAAW,SAAS;AACjE,aAAO,UAAU,YAAY,KAAK,MAAM,WAAW,OAAO;AAC1D,kCAA4B,IAAI;AAAA,IAClC;AACA,IAAAA,cAAa,UAAU,gBAAgB,WAAY;AACjD,WAAK,eAAe,MAAM;AAAA,IAC5B;AACA,IAAAA,cAAa,UAAU,mBAAmB,WAAY;AACpD,aAAO,KAAK;AAAA,IACd;AACA,IAAAA,cAAa,OAAO;AACpB,IAAAA,cAAa,gBAAgB;AAAA,MAC3B,gBAAgB;AAAA,IAClB;AACA,WAAOA;AAAA,EACT,EAAE,iBAAc;AAAA;AAEhB,IAAI;AAAA;AAAA,EAA2B,SAAU,QAAQ;AAC/C,cAAUC,cAAa,MAAM;AAC7B,aAASA,eAAc;AACrB,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,OAAO;AACb,aAAO;AAAA,IACT;AACA,IAAAA,aAAY,OAAO;AACnB,WAAOA;AAAA,EACT,EAAEC,kBAAa;AAAA;AACR,SAAS,QAAQ,WAAW;AACjC,YAAU,uBAAuB,YAAY;AAC7C,YAAU,sBAAsB,WAAW;AAC7C;", "names": ["DatasetModel", "DatasetView", "Component_default"]}