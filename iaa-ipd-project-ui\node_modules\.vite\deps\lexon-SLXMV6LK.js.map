{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/lexon/lexon.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    comments: {\n        lineComment: 'COMMENT'\n        // blockComment: ['COMMENT', '.'],\n    },\n    brackets: [['(', ')']],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: ':', close: '.' }\n    ],\n    surroundingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '`', close: '`' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" },\n        { open: ':', close: '.' }\n    ],\n    folding: {\n        markers: {\n            start: new RegExp('^\\\\s*(::\\\\s*|COMMENT\\\\s+)#region'),\n            end: new RegExp('^\\\\s*(::\\\\s*|COMMENT\\\\s+)#endregion')\n        }\n    }\n};\nexport var language = {\n    // Set defaultToken to invalid to see what you do not tokenize yet\n    // defaultToken: 'invalid',\n    tokenPostfix: '.lexon',\n    ignoreCase: true,\n    keywords: [\n        'lexon',\n        'lex',\n        'clause',\n        'terms',\n        'contracts',\n        'may',\n        'pay',\n        'pays',\n        'appoints',\n        'into',\n        'to'\n    ],\n    typeKeywords: ['amount', 'person', 'key', 'time', 'date', 'asset', 'text'],\n    operators: [\n        'less',\n        'greater',\n        'equal',\n        'le',\n        'gt',\n        'or',\n        'and',\n        'add',\n        'added',\n        'subtract',\n        'subtracted',\n        'multiply',\n        'multiplied',\n        'times',\n        'divide',\n        'divided',\n        'is',\n        'be',\n        'certified'\n    ],\n    // we include these common regular expressions\n    symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n    // The main tokenizer for our languages\n    tokenizer: {\n        root: [\n            // comment\n            [/^(\\s*)(comment:?(?:\\s.*|))$/, ['', 'comment']],\n            // special identifier cases\n            [\n                /\"/,\n                {\n                    token: 'identifier.quote',\n                    bracket: '@open',\n                    next: '@quoted_identifier'\n                }\n            ],\n            [\n                'LEX$',\n                {\n                    token: 'keyword',\n                    bracket: '@open',\n                    next: '@identifier_until_period'\n                }\n            ],\n            ['LEXON', { token: 'keyword', bracket: '@open', next: '@semver' }],\n            [\n                ':',\n                {\n                    token: 'delimiter',\n                    bracket: '@open',\n                    next: '@identifier_until_period'\n                }\n            ],\n            // identifiers and keywords\n            [\n                /[a-z_$][\\w$]*/,\n                {\n                    cases: {\n                        '@operators': 'operator',\n                        '@typeKeywords': 'keyword.type',\n                        '@keywords': 'keyword',\n                        '@default': 'identifier'\n                    }\n                }\n            ],\n            // whitespace\n            { include: '@whitespace' },\n            // delimiters and operators\n            [/[{}()\\[\\]]/, '@brackets'],\n            [/[<>](?!@symbols)/, '@brackets'],\n            [/@symbols/, 'delimiter'],\n            // numbers\n            [/\\d*\\.\\d*\\.\\d*/, 'number.semver'],\n            [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, 'number.float'],\n            [/0[xX][0-9a-fA-F]+/, 'number.hex'],\n            [/\\d+/, 'number'],\n            // delimiter: after number because of .\\d floats\n            [/[;,.]/, 'delimiter']\n        ],\n        quoted_identifier: [\n            [/[^\\\\\"]+/, 'identifier'],\n            [/\"/, { token: 'identifier.quote', bracket: '@close', next: '@pop' }]\n        ],\n        space_identifier_until_period: [\n            [':', 'delimiter'],\n            [' ', { token: 'white', next: '@identifier_rest' }]\n        ],\n        identifier_until_period: [\n            { include: '@whitespace' },\n            [':', { token: 'delimiter', next: '@identifier_rest' }],\n            [/[^\\\\.]+/, 'identifier'],\n            [/\\./, { token: 'delimiter', bracket: '@close', next: '@pop' }]\n        ],\n        identifier_rest: [\n            [/[^\\\\.]+/, 'identifier'],\n            [/\\./, { token: 'delimiter', bracket: '@close', next: '@pop' }]\n        ],\n        semver: [\n            { include: '@whitespace' },\n            [':', 'delimiter'],\n            [/\\d*\\.\\d*\\.\\d*/, { token: 'number.semver', bracket: '@close', next: '@pop' }]\n        ],\n        whitespace: [[/[ \\t\\r\\n]+/, 'white']]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,aAAa;AAAA;AAAA,EAEjB;AAAA,EACA,UAAU,CAAC,CAAC,KAAK,GAAG,CAAC;AAAA,EACrB,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,SAAS;AAAA,IACL,SAAS;AAAA,MACL,OAAO,IAAI,OAAO,kCAAkC;AAAA,MACpD,KAAK,IAAI,OAAO,qCAAqC;AAAA,IACzD;AAAA,EACJ;AACJ;AACO,IAAI,WAAW;AAAA;AAAA;AAAA,EAGlB,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,cAAc,CAAC,UAAU,UAAU,OAAO,QAAQ,QAAQ,SAAS,MAAM;AAAA,EACzE,WAAW;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA;AAAA,EAEA,SAAS;AAAA;AAAA,EAET,WAAW;AAAA,IACP,MAAM;AAAA;AAAA,MAEF,CAAC,+BAA+B,CAAC,IAAI,SAAS,CAAC;AAAA;AAAA,MAE/C;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QACV;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QACV;AAAA,MACJ;AAAA,MACA,CAAC,SAAS,EAAE,OAAO,WAAW,SAAS,SAAS,MAAM,UAAU,CAAC;AAAA,MACjE;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,SAAS;AAAA,UACT,MAAM;AAAA,QACV;AAAA,MACJ;AAAA;AAAA,MAEA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,cAAc;AAAA,YACd,iBAAiB;AAAA,YACjB,aAAa;AAAA,YACb,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,oBAAoB,WAAW;AAAA,MAChC,CAAC,YAAY,WAAW;AAAA;AAAA,MAExB,CAAC,iBAAiB,eAAe;AAAA,MACjC,CAAC,4BAA4B,cAAc;AAAA,MAC3C,CAAC,qBAAqB,YAAY;AAAA,MAClC,CAAC,OAAO,QAAQ;AAAA;AAAA,MAEhB,CAAC,SAAS,WAAW;AAAA,IACzB;AAAA,IACA,mBAAmB;AAAA,MACf,CAAC,WAAW,YAAY;AAAA,MACxB,CAAC,KAAK,EAAE,OAAO,oBAAoB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IACxE;AAAA,IACA,+BAA+B;AAAA,MAC3B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,KAAK,EAAE,OAAO,SAAS,MAAM,mBAAmB,CAAC;AAAA,IACtD;AAAA,IACA,yBAAyB;AAAA,MACrB,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,KAAK,EAAE,OAAO,aAAa,MAAM,mBAAmB,CAAC;AAAA,MACtD,CAAC,WAAW,YAAY;AAAA,MACxB,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAClE;AAAA,IACA,iBAAiB;AAAA,MACb,CAAC,WAAW,YAAY;AAAA,MACxB,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAClE;AAAA,IACA,QAAQ;AAAA,MACJ,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,iBAAiB,EAAE,OAAO,iBAAiB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IACjF;AAAA,IACA,YAAY,CAAC,CAAC,cAAc,OAAO,CAAC;AAAA,EACxC;AACJ;", "names": []}