{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/pla/pla.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    comments: {\n        lineComment: '#'\n    },\n    brackets: [\n        ['[', ']'],\n        ['<', '>'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: '[', close: ']' },\n        { open: '<', close: '>' },\n        { open: '(', close: ')' }\n    ],\n    surroundingPairs: [\n        { open: '[', close: ']' },\n        { open: '<', close: '>' },\n        { open: '(', close: ')' }\n    ]\n};\nexport var language = {\n    defaultToken: '',\n    tokenPostfix: '.pla',\n    brackets: [\n        { open: '[', close: ']', token: 'delimiter.square' },\n        { open: '<', close: '>', token: 'delimiter.angle' },\n        { open: '(', close: ')', token: 'delimiter.parenthesis' }\n    ],\n    keywords: [\n        '.i',\n        '.o',\n        '.mv',\n        '.ilb',\n        '.ob',\n        '.label',\n        '.type',\n        '.phase',\n        '.pair',\n        '.symbolic',\n        '.symbolic-output',\n        '.kiss',\n        '.p',\n        '.e',\n        '.end'\n    ],\n    // regular expressions\n    comment: /#.*$/,\n    identifier: /[a-zA-Z]+[a-zA-Z0-9_\\-]*/,\n    plaContent: /[01\\-~\\|]+/,\n    // The main tokenizer for our languages\n    tokenizer: {\n        root: [\n            // comments and whitespace\n            { include: '@whitespace' },\n            [/@comment/, 'comment'],\n            // keyword\n            [\n                /\\.([a-zA-Z_\\-]+)/,\n                {\n                    cases: {\n                        '@eos': { token: 'keyword.$1' },\n                        '@keywords': {\n                            cases: {\n                                '.type': { token: 'keyword.$1', next: '@type' },\n                                '@default': { token: 'keyword.$1', next: '@keywordArg' }\n                            }\n                        },\n                        '@default': { token: 'keyword.$1' }\n                    }\n                }\n            ],\n            // identifiers\n            [/@identifier/, 'identifier'],\n            // PLA row\n            [/@plaContent/, 'string']\n        ],\n        whitespace: [[/[ \\t\\r\\n]+/, '']],\n        type: [{ include: '@whitespace' }, [/\\w+/, { token: 'type', next: '@pop' }]],\n        keywordArg: [\n            // whitespace\n            [\n                /[ \\t\\r\\n]+/,\n                {\n                    cases: {\n                        '@eos': { token: '', next: '@pop' },\n                        '@default': ''\n                    }\n                }\n            ],\n            // comments\n            [/@comment/, 'comment', '@pop'],\n            // brackets\n            [\n                /[<>()\\[\\]]/,\n                {\n                    cases: {\n                        '@eos': { token: '@brackets', next: '@pop' },\n                        '@default': '@brackets'\n                    }\n                }\n            ],\n            // numbers\n            [\n                /\\-?\\d+/,\n                {\n                    cases: {\n                        '@eos': { token: 'number', next: '@pop' },\n                        '@default': 'number'\n                    }\n                }\n            ],\n            // identifiers\n            [\n                /@identifier/,\n                {\n                    cases: {\n                        '@eos': { token: 'identifier', next: '@pop' },\n                        '@default': 'identifier'\n                    }\n                }\n            ],\n            // delimiter\n            [\n                /[;=]/,\n                {\n                    cases: {\n                        '@eos': { token: 'delimiter', next: '@pop' },\n                        '@default': 'delimiter'\n                    }\n                }\n            ]\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,aAAa;AAAA,EACjB;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACN,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,EAC5D;AAAA,EACA,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA;AAAA,EAEA,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,YAAY;AAAA;AAAA,EAEZ,WAAW;AAAA,IACP,MAAM;AAAA;AAAA,MAEF,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,YAAY,SAAS;AAAA;AAAA,MAEtB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ,EAAE,OAAO,aAAa;AAAA,YAC9B,aAAa;AAAA,cACT,OAAO;AAAA,gBACH,SAAS,EAAE,OAAO,cAAc,MAAM,QAAQ;AAAA,gBAC9C,YAAY,EAAE,OAAO,cAAc,MAAM,cAAc;AAAA,cAC3D;AAAA,YACJ;AAAA,YACA,YAAY,EAAE,OAAO,aAAa;AAAA,UACtC;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,CAAC,eAAe,YAAY;AAAA;AAAA,MAE5B,CAAC,eAAe,QAAQ;AAAA,IAC5B;AAAA,IACA,YAAY,CAAC,CAAC,cAAc,EAAE,CAAC;AAAA,IAC/B,MAAM,CAAC,EAAE,SAAS,cAAc,GAAG,CAAC,OAAO,EAAE,OAAO,QAAQ,MAAM,OAAO,CAAC,CAAC;AAAA,IAC3E,YAAY;AAAA;AAAA,MAER;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ,EAAE,OAAO,IAAI,MAAM,OAAO;AAAA,YAClC,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,CAAC,YAAY,WAAW,MAAM;AAAA;AAAA,MAE9B;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ,EAAE,OAAO,aAAa,MAAM,OAAO;AAAA,YAC3C,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ,EAAE,OAAO,UAAU,MAAM,OAAO;AAAA,YACxC,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ,EAAE,OAAO,cAAc,MAAM,OAAO;AAAA,YAC5C,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ,EAAE,OAAO,aAAa,MAAM,OAAO;AAAA,YAC3C,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;", "names": []}