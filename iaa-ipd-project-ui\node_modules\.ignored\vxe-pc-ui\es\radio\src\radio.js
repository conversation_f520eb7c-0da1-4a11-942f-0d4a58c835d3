import { defineComponent, h, computed, inject, reactive } from 'vue';
import XEUtils from 'xe-utils';
import { getFuncText } from '../../ui/src/utils';
import { getConfig, createEvent, useSize, getIcon } from '../../ui';
export default defineComponent({
    name: 'VxeRadio',
    props: {
        modelValue: [String, Number, Boolean],
        label: {
            type: [String, Number, Boolean],
            default: null
        },
        title: [String, Number],
        content: [String, Number],
        disabled: {
            type: Boolean,
            default: null
        },
        name: String,
        strict: {
            type: Boolean,
            default: () => getConfig().radio.strict
        },
        size: {
            type: String,
            default: () => getConfig().radio.size || getConfig().size
        }
    },
    emits: [
        'update:modelValue',
        'change'
    ],
    setup(props, context) {
        const { slots, emit } = context;
        const $xeForm = inject('$xeForm', null);
        const formItemInfo = inject('xeFormItemInfo', null);
        const $xeRadioGroup = inject('$xeRadioGroup', null);
        const xID = XEUtils.uniqueId();
        const reactData = reactive({});
        const $xeRadio = {
            xID,
            props,
            context,
            reactData
        };
        const { computeSize } = useSize(props);
        const computeIsDisabled = computed(() => {
            const { disabled } = props;
            if (disabled === null) {
                if ($xeRadioGroup) {
                    const { computeIsDisabled } = $xeRadioGroup.getComputeMaps();
                    return computeIsDisabled.value;
                }
            }
            return disabled;
        });
        const computeName = computed(() => {
            return $xeRadioGroup ? $xeRadioGroup.name : props.name;
        });
        const computeStrict = computed(() => {
            return $xeRadioGroup ? $xeRadioGroup.props.strict : props.strict;
        });
        const computeChecked = computed(() => {
            const { label } = props;
            return $xeRadioGroup ? $xeRadioGroup.props.modelValue === label : props.modelValue === label;
        });
        const handleValue = (label, evnt) => {
            if ($xeRadioGroup) {
                $xeRadioGroup.handleChecked({ label }, evnt);
            }
            else {
                emit('update:modelValue', label);
                dispatchEvent('change', { label }, evnt);
                // 自动更新校验状态
                if ($xeForm && formItemInfo) {
                    $xeForm.triggerItemEvent(evnt, formItemInfo.itemConfig.field, label);
                }
            }
        };
        const changeEvent = (evnt) => {
            const isDisabled = computeIsDisabled.value;
            if (!isDisabled) {
                handleValue(props.label, evnt);
            }
        };
        const clickEvent = (evnt) => {
            const isDisabled = computeIsDisabled.value;
            const isStrict = computeStrict.value;
            if (!isDisabled && !isStrict) {
                if (props.label === ($xeRadioGroup ? $xeRadioGroup.props.modelValue : props.modelValue)) {
                    handleValue(null, evnt);
                }
            }
        };
        const dispatchEvent = (type, params, evnt) => {
            emit(type, createEvent(evnt, { $radio: $xeRadio }, params));
        };
        const radioMethods = {
            dispatchEvent
        };
        const radioPrivateMethods = {};
        Object.assign($xeRadio, radioMethods, radioPrivateMethods);
        const renderVN = () => {
            const { label } = props;
            const vSize = computeSize.value;
            const isDisabled = computeIsDisabled.value;
            const name = computeName.value;
            const isChecked = computeChecked.value;
            return h('label', {
                key: label,
                class: ['vxe-radio', {
                        [`size--${vSize}`]: vSize,
                        'is--checked': isChecked,
                        'is--disabled': isDisabled
                    }],
                title: props.title
            }, [
                h('input', {
                    class: 'vxe-radio--input',
                    type: 'radio',
                    name,
                    checked: isChecked,
                    disabled: isDisabled,
                    onChange: changeEvent,
                    onClick: clickEvent
                }),
                h('span', {
                    class: ['vxe-radio--icon', isChecked ? getIcon().RADIO_CHECKED : getIcon().RADIO_UNCHECKED]
                }),
                h('span', {
                    class: 'vxe-radio--label'
                }, slots.default ? slots.default({}) : getFuncText(props.content))
            ]);
        };
        $xeRadio.renderVN = renderVN;
        return $xeRadio;
    },
    render() {
        return this.renderVN();
    }
});
