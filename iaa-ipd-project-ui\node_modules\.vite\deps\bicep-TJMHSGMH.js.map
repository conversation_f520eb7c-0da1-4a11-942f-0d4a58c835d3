{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/bicep/bicep.js"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\nvar bounded = function (text) { return \"\\\\b\" + text + \"\\\\b\"; };\nvar identifierStart = '[_a-zA-Z]';\nvar identifierContinue = '[_a-zA-Z0-9]';\nvar identifier = bounded(\"\" + identifierStart + identifierContinue + \"*\");\nvar keywords = [\n    'targetScope',\n    'resource',\n    'module',\n    'param',\n    'var',\n    'output',\n    'for',\n    'in',\n    'if',\n    'existing'\n];\nvar namedLiterals = ['true', 'false', 'null'];\nvar nonCommentWs = \"[ \\\\t\\\\r\\\\n]\";\nvar numericLiteral = \"[0-9]+\";\nexport var conf = {\n    comments: {\n        lineComment: '//',\n        blockComment: ['/*', '*/']\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')']\n    ],\n    surroundingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: \"'\", close: \"'\" },\n        { open: \"'''\", close: \"'''\" }\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: \"'\", close: \"'\", notIn: ['string', 'comment'] },\n        { open: \"'''\", close: \"'''\", notIn: ['string', 'comment'] }\n    ],\n    autoCloseBefore: \":.,=}])' \\n\\t\",\n    indentationRules: {\n        increaseIndentPattern: new RegExp('^((?!\\\\/\\\\/).)*(\\\\{[^}\"\\'`]*|\\\\([^)\"\\'`]*|\\\\[[^\\\\]\"\\'`]*)$'),\n        decreaseIndentPattern: new RegExp('^((?!.*?\\\\/\\\\*).*\\\\*/)?\\\\s*[\\\\}\\\\]].*$')\n    }\n};\nexport var language = {\n    defaultToken: '',\n    tokenPostfix: '.bicep',\n    brackets: [\n        { open: '{', close: '}', token: 'delimiter.curly' },\n        { open: '[', close: ']', token: 'delimiter.square' },\n        { open: '(', close: ')', token: 'delimiter.parenthesis' }\n    ],\n    symbols: /[=><!~?:&|+\\-*/^%]+/,\n    keywords: keywords,\n    namedLiterals: namedLiterals,\n    escapes: \"\\\\\\\\(u{[0-9A-Fa-f]+}|n|r|t|\\\\\\\\|'|\\\\${)\",\n    tokenizer: {\n        root: [{ include: '@expression' }, { include: '@whitespace' }],\n        stringVerbatim: [\n            { regex: \"(|'|'')[^']\", action: { token: 'string' } },\n            { regex: \"'''\", action: { token: 'string.quote', next: '@pop' } }\n        ],\n        stringLiteral: [\n            { regex: \"\\\\${\", action: { token: 'delimiter.bracket', next: '@bracketCounting' } },\n            { regex: \"[^\\\\\\\\'$]+\", action: { token: 'string' } },\n            { regex: '@escapes', action: { token: 'string.escape' } },\n            { regex: \"\\\\\\\\.\", action: { token: 'string.escape.invalid' } },\n            { regex: \"'\", action: { token: 'string', next: '@pop' } }\n        ],\n        bracketCounting: [\n            { regex: \"{\", action: { token: 'delimiter.bracket', next: '@bracketCounting' } },\n            { regex: \"}\", action: { token: 'delimiter.bracket', next: '@pop' } },\n            { include: 'expression' }\n        ],\n        comment: [\n            { regex: \"[^\\\\*]+\", action: { token: 'comment' } },\n            { regex: \"\\\\*\\\\/\", action: { token: 'comment', next: '@pop' } },\n            { regex: \"[\\\\/*]\", action: { token: 'comment' } }\n        ],\n        whitespace: [\n            { regex: nonCommentWs },\n            { regex: \"\\\\/\\\\*\", action: { token: 'comment', next: '@comment' } },\n            { regex: \"\\\\/\\\\/.*$\", action: { token: 'comment' } }\n        ],\n        expression: [\n            { regex: \"'''\", action: { token: 'string.quote', next: '@stringVerbatim' } },\n            { regex: \"'\", action: { token: 'string.quote', next: '@stringLiteral' } },\n            { regex: numericLiteral, action: { token: 'number' } },\n            {\n                regex: identifier,\n                action: {\n                    cases: {\n                        '@keywords': { token: 'keyword' },\n                        '@namedLiterals': { token: 'keyword' },\n                        '@default': { token: 'identifier' }\n                    }\n                }\n            }\n        ]\n    }\n};\n"], "mappings": ";;;AAEA,IAAI,UAAU,SAAU,MAAM;AAAE,SAAO,QAAQ,OAAO;AAAO;AAC7D,IAAI,kBAAkB;AACtB,IAAI,qBAAqB;AACzB,IAAI,aAAa,QAAQ,KAAK,kBAAkB,qBAAqB,GAAG;AACxE,IAAI,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,IAAI,gBAAgB,CAAC,QAAQ,SAAS,MAAM;AAC5C,IAAI,eAAe;AACnB,IAAI,iBAAiB;AACd,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC7B;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,OAAO,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,OAAO,OAAO,OAAO,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EAC9D;AAAA,EACA,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,IACd,uBAAuB,IAAI,OAAO,4DAA4D;AAAA,IAC9F,uBAAuB,IAAI,OAAO,wCAAwC;AAAA,EAC9E;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACN,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,EAC5D;AAAA,EACA,SAAS;AAAA,EACT;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT,WAAW;AAAA,IACP,MAAM,CAAC,EAAE,SAAS,cAAc,GAAG,EAAE,SAAS,cAAc,CAAC;AAAA,IAC7D,gBAAgB;AAAA,MACZ,EAAE,OAAO,eAAe,QAAQ,EAAE,OAAO,SAAS,EAAE;AAAA,MACpD,EAAE,OAAO,OAAO,QAAQ,EAAE,OAAO,gBAAgB,MAAM,OAAO,EAAE;AAAA,IACpE;AAAA,IACA,eAAe;AAAA,MACX,EAAE,OAAO,QAAQ,QAAQ,EAAE,OAAO,qBAAqB,MAAM,mBAAmB,EAAE;AAAA,MAClF,EAAE,OAAO,cAAc,QAAQ,EAAE,OAAO,SAAS,EAAE;AAAA,MACnD,EAAE,OAAO,YAAY,QAAQ,EAAE,OAAO,gBAAgB,EAAE;AAAA,MACxD,EAAE,OAAO,SAAS,QAAQ,EAAE,OAAO,wBAAwB,EAAE;AAAA,MAC7D,EAAE,OAAO,KAAK,QAAQ,EAAE,OAAO,UAAU,MAAM,OAAO,EAAE;AAAA,IAC5D;AAAA,IACA,iBAAiB;AAAA,MACb,EAAE,OAAO,KAAK,QAAQ,EAAE,OAAO,qBAAqB,MAAM,mBAAmB,EAAE;AAAA,MAC/E,EAAE,OAAO,KAAK,QAAQ,EAAE,OAAO,qBAAqB,MAAM,OAAO,EAAE;AAAA,MACnE,EAAE,SAAS,aAAa;AAAA,IAC5B;AAAA,IACA,SAAS;AAAA,MACL,EAAE,OAAO,WAAW,QAAQ,EAAE,OAAO,UAAU,EAAE;AAAA,MACjD,EAAE,OAAO,UAAU,QAAQ,EAAE,OAAO,WAAW,MAAM,OAAO,EAAE;AAAA,MAC9D,EAAE,OAAO,UAAU,QAAQ,EAAE,OAAO,UAAU,EAAE;AAAA,IACpD;AAAA,IACA,YAAY;AAAA,MACR,EAAE,OAAO,aAAa;AAAA,MACtB,EAAE,OAAO,UAAU,QAAQ,EAAE,OAAO,WAAW,MAAM,WAAW,EAAE;AAAA,MAClE,EAAE,OAAO,aAAa,QAAQ,EAAE,OAAO,UAAU,EAAE;AAAA,IACvD;AAAA,IACA,YAAY;AAAA,MACR,EAAE,OAAO,OAAO,QAAQ,EAAE,OAAO,gBAAgB,MAAM,kBAAkB,EAAE;AAAA,MAC3E,EAAE,OAAO,KAAK,QAAQ,EAAE,OAAO,gBAAgB,MAAM,iBAAiB,EAAE;AAAA,MACxE,EAAE,OAAO,gBAAgB,QAAQ,EAAE,OAAO,SAAS,EAAE;AAAA,MACrD;AAAA,QACI,OAAO;AAAA,QACP,QAAQ;AAAA,UACJ,OAAO;AAAA,YACH,aAAa,EAAE,OAAO,UAAU;AAAA,YAChC,kBAAkB,EAAE,OAAO,UAAU;AAAA,YACrC,YAAY,EAAE,OAAO,aAAa;AAAA,UACtC;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;", "names": []}