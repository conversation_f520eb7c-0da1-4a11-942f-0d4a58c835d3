.vxe-anchor {
  position: relative;
  color: var(--vxe-ui-font-color);
  font-family: var(--vxe-ui-font-family);
}

.vxe-anchor--marker {
  position: absolute;
  left: 0;
  top: 0;
  width: 0.2em;
  height: 1.5em;
  padding: 0.25em 0;
  transition: all 0.35s ease-in-out;
}
.vxe-anchor--marker::before {
  display: block;
  content: "";
  height: 100%;
  width: 100%;
  border-radius: 1em;
  background-color: var(--vxe-ui-anchor-link-active-color);
}

.vxe-anchor-link {
  position: relative;
  padding-left: 0.6em;
}
.vxe-anchor-link.is--active > .vxe-anchor-link--item {
  color: var(--vxe-ui-anchor-link-active-color);
}

.vxe-anchor-link--item {
  display: block;
  height: 1.5em;
  line-height: 1.5em;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  color: var(--vxe-ui-anchor-link-color);
}