{"version": 3, "sources": ["../../amis/esm/renderers/Alert.js"], "sourcesContent": ["/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __rest, __assign, __decorate } from 'tslib';\nimport React from 'react';\nimport { isPureVariable, resolveVariableAndFilter, Renderer } from 'amis-core';\nimport { Alert2 } from 'amis-ui';\n\nvar AlertRenderer = /** @class */ (function (_super) {\n    __extends(AlertRenderer, _super);\n    function AlertRenderer() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    AlertRenderer.prototype.render = function () {\n        var _a = this.props, render = _a.render, body = _a.body, level = _a.level, icon = _a.icon, showIcon = _a.showIcon, actions = _a.actions, rest = __rest(_a, [\"render\", \"body\", \"level\", \"icon\", \"showIcon\", \"actions\"]);\n        if (isPureVariable(level)) {\n            level = resolveVariableAndFilter(level, this.props.data);\n        }\n        if (isPureVariable(icon)) {\n            icon = resolveVariableAndFilter(icon, this.props.data);\n        }\n        if (isPureVariable(showIcon)) {\n            showIcon = resolveVariableAndFilter(showIcon, this.props.data);\n        }\n        var actionsDom = actions\n            ? React.isValidElement(actions)\n                ? actions\n                : render('alert-actions', actions)\n            : null;\n        return (React.createElement(Alert2, __assign({}, rest, { level: level, icon: icon, showIcon: showIcon, actions: actionsDom }), render('body', body)));\n    };\n    AlertRenderer = __decorate([\n        Renderer({\n            type: 'alert'\n        })\n    ], AlertRenderer);\n    return AlertRenderer;\n}(React.Component));\n\nexport { AlertRenderer };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,mBAAkB;AAIlB,IAAI;AAAA;AAAA,EAA+B,SAAU,QAAQ;AACjD,cAAUA,gBAAe,MAAM;AAC/B,aAASA,iBAAgB;AACrB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,eAAc,UAAU,SAAS,WAAY;AACzC,UAAI,KAAK,KAAK,OAAO,SAAS,GAAG,QAAQ,OAAO,GAAG,MAAM,QAAQ,GAAG,OAAO,OAAO,GAAG,MAAM,WAAW,GAAG,UAAU,UAAU,GAAG,SAAS,OAAO,OAAO,IAAI,CAAC,UAAU,QAAQ,SAAS,QAAQ,YAAY,SAAS,CAAC;AACrN,UAAI,eAAe,KAAK,GAAG;AACvB,gBAAQ,yBAAyB,OAAO,KAAK,MAAM,IAAI;AAAA,MAC3D;AACA,UAAI,eAAe,IAAI,GAAG;AACtB,eAAO,yBAAyB,MAAM,KAAK,MAAM,IAAI;AAAA,MACzD;AACA,UAAI,eAAe,QAAQ,GAAG;AAC1B,mBAAW,yBAAyB,UAAU,KAAK,MAAM,IAAI;AAAA,MACjE;AACA,UAAI,aAAa,UACX,aAAAC,QAAM,eAAe,OAAO,IACxB,UACA,OAAO,iBAAiB,OAAO,IACnC;AACN,aAAQ,aAAAA,QAAM,cAAc,QAAQ,SAAS,CAAC,GAAG,MAAM,EAAE,OAAc,MAAY,UAAoB,SAAS,WAAW,CAAC,GAAG,OAAO,QAAQ,IAAI,CAAC;AAAA,IACvJ;AACA,IAAAD,iBAAgB,WAAW;AAAA,MACvB,SAAS;AAAA,QACL,MAAM;AAAA,MACV,CAAC;AAAA,IACL,GAAGA,cAAa;AAChB,WAAOA;AAAA,EACX,EAAE,aAAAC,QAAM,SAAS;AAAA;", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "React"]}