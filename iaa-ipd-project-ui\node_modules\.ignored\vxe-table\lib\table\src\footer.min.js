Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var _vue=require("vue"),_comp=require("../../ui/src/comp"),_xeUtils=_interopRequireDefault(require("xe-utils")),_ui=require("../../ui"),_dom=require("../../ui/src/dom"),_util=require("./util");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let{renderer,renderEmptyElement}=_ui.VxeUI,renderType="footer";var _default=exports.default=(0,_comp.defineVxeComponent)({name:"VxeTableFooter",props:{footerTableData:{type:Array,default:()=>[]},tableColumn:{type:Array,default:()=>[]},fixedColumn:{type:Array,default:()=>[]},fixedType:{type:String,default:null}},setup(x){let ee=(0,_vue.inject)("$xeTable",{}),{xID:m,props:_,reactData:g,internalData:h}=ee,{computeTooltipOpts:l,computeColumnOpts:b,computeColumnDragOpts:y,computeCellOpts:t,computeFooterCellOpts:o,computeDefaultRowHeight:r,computeResizableOpts:i,computeVirtualXOpts:a}=ee.getComputeMaps(),w=(0,_vue.ref)(),C=(0,_vue.ref)(),T=(0,_vue.ref)(),O=(0,_vue.ref)(),D=(0,_vue.ref)(),F=(0,_vue.ref)(),I=(w,C,T,O,D,F)=>{let I=ee.xeGrid,S=x.fixedType,{resizable:E,border:M,footerCellClassName:k,footerCellStyle:z,footerAlign:U,footerSpanMethod:R,align:$,columnKey:q,showFooterOverflow:A}=_,{scrollXLoad:j,scrollYLoad:L,overflowX:X,currentColumn:N}=g,{fullColumnIdData:V,mergeFooterList:G,mergeFooterCellMaps:H,scrollXStore:P}=h,W=a.value,B=l.value;let K=i.value.isAllColumnDrag,Y=b.value;var e=r.value;let J=t.value,Q=o.value,Z=(0,_util.getCellHeight)(Q.height)||e;return C.map((l,e)=>{var{type:t,showFooterOverflow:o,footerAlign:r,align:i,footerClassName:a,editRender:n,cellRender:u}=l,d=l.id,s=V[d]||{},n=n||u,u=n?renderer.get(n.name):null;let c=B.showAll;var n=l.children&&l.children.length,n=S?l.fixed!==S&&!n:l.fixed&&X,p=(_xeUtils.default.isBoolean(Q.padding)?Q:J).padding,o=_xeUtils.default.eqNull(o)?A:o,r=r||(u?u.tableFooterCellAlign:"")||U||i||(u?u.tableCellAlign:"")||$,i="ellipsis"===o;let f="title"===o,v=!0===o||"tooltip"===o;var u=f||v||i,o=_xeUtils.default.isBoolean(l.resizable)?l.resizable:Y.resizable||E,x={colid:d},m={},_=s.index,s=s._index,g=s;let h={$table:ee,$grid:I,row:O,rowIndex:F,_rowIndex:F,$rowIndex:D,column:l,columnIndex:_,$columnIndex:e,_columnIndex:s,itemIndex:g,items:O,fixed:S,type:renderType,data:T},b=((f||v||c)&&(m.onMouseenter=e=>{f?(0,_dom.updateCellTitle)(e.currentTarget,l):(v||c)&&ee.triggerFooterTooltipEvent(e,h)}),(v||c)&&(m.onMouseleave=e=>{(v||c)&&ee.handleTargetLeaveEvent(e)}),m.onClick=e=>{ee.dispatchEvent("footer-cell-click",Object.assign({cell:e.currentTarget},h),e)},!(m.onDblclick=e=>{ee.dispatchEvent("footer-cell-dblclick",Object.assign({cell:e.currentTarget},h),e)}));if(G.length){_=H[F+":"+s];if(_){var{rowspan:g,colspan:_}=_;if(!g||!_)return null;1<g&&(b=!0,x.rowspan=g),1<_&&(b=!0,x.colspan=_)}}else if(R){var{rowspan:g=1,colspan:_=1}=R(h)||{};if(!g||!_)return null;1<g&&(x.rowspan=g),1<_&&(x.colspan=_)}g=e===C.length-1,_=!l.resizeWidth&&("auto"===l.minWidth||"auto"===l.width);let y=!1;w&&!b&&j&&!l.fixed&&!W.immediate&&(s<P.visibleStartIndex-P.preloadSize||s>P.visibleEndIndex+P.preloadSize)&&(y=!0);s={};return u?s.height=Z+"px":s.minHeight=Z+"px",(0,_vue.h)("td",Object.assign(Object.assign(Object.assign(Object.assign({class:["vxe-footer--column",l.id,{["col--"+r]:r,["col--"+t]:t,"col--last":g,"fixed--width":!_,"fixed--hidden":n,"is--padding":p,"col--ellipsis":u,"col--current":N===l},(0,_dom.getPropClass)(a,h),(0,_dom.getPropClass)(k,h)]},x),{style:z?_xeUtils.default.isFunction(z)?z(h):z:null}),m),{key:q||j||L||Y.useKey||Y.drag?l.id:e}),[(0,_vue.h)("div",{class:["vxe-cell",{"c--title":f,"c--tooltip":v,"c--ellipsis":i}],style:s},y?[]:[(0,_vue.h)("div",{colid:d,class:"vxe-cell--wrapper"},l.renderFooter(h))]),!n&&o&&K?(0,_vue.h)("div",{class:["vxe-cell--col-resizable",{"is--line":!M||"none"===M}],onMousedown:e=>ee.handleColResizeMousedownEvent(e,S,h),onDblclick:e=>ee.handleColResizeDblclickEvent(e,h)}):renderEmptyElement(ee)])})};return(0,_vue.onMounted)(()=>{(0,_vue.nextTick)(()=>{var e=x.fixedType,l=h.elemStore,e=`${e||"main"}-footer-`;l[e+"wrapper"]=w,l[e+"scroll"]=C,l[e+"table"]=T,l[e+"colgroup"]=O,l[e+"list"]=D,l[e+"xSpace"]=F})}),(0,_vue.onUnmounted)(()=>{var e=x.fixedType,l=h.elemStore,e=`${e||"main"}-footer-`;l[e+"wrapper"]=null,l[e+"scroll"]=null,l[e+"table"]=null,l[e+"colgroup"]=null,l[e+"list"]=null,l[e+"xSpace"]=null}),()=>{let{fixedType:l,fixedColumn:e,tableColumn:t}=x;var{spanMethod:o,footerSpanMethod:r,showFooterOverflow:i}=_,{visibleColumn:a,fullColumnIdData:n}=h,{isGroup:u,isColLoading:d,overflowX:s,scrollXLoad:c,dragCol:p}=g;let f=t,v=!1;return c&&i&&(o||r||(v=!0)),v&&(d||!l&&s)||(f=a),l&&v&&(f=e||[]),l||u||c&&p&&2<f.length&&(i=n[p.id])&&(o=i._index,r=f[0],d=f[f.length-1],s=n[r.id],a=n[d.id],s)&&a&&(u=s._index,c=a._index,o<u?f=[p].concat(f):c<o&&(f=f.concat([p]))),(0,_vue.h)("div",{ref:w,class:["vxe-table--footer-wrapper",l?`fixed-${l}--wrapper`:"body--wrapper"],xid:m},[(0,_vue.h)("div",{ref:C,class:"vxe-table--footer-inner-wrapper",onScroll(e){ee.triggerFooterScrollEvent(e,l)}},[l?renderEmptyElement(ee):(0,_vue.h)("div",{ref:F,class:"vxe-body--x-space"}),(0,_vue.h)("table",{ref:T,class:"vxe-table--footer",xid:m,cellspacing:0,cellpadding:0,border:0,xvm:v?"1":null},[(0,_vue.h)("colgroup",{ref:O},f.map((e,l)=>(0,_vue.h)("col",{name:e.id,key:l,style:{width:e.renderWidth+"px"}}))),(0,_vue.h)("tfoot",{ref:D},((r,i)=>{let{fixedType:a,footerTableData:n}=x,{footerRowClassName:u,footerRowStyle:d}=_,{isColLoading:s,isDragColMove:c}=g,p=b.value,f=y.value;return n.map((e,l)=>{let t=l;var o={$table:ee,row:e,_rowIndex:t,$rowIndex:l,fixed:a,type:renderType};return!s&&p.drag&&f.animation?(0,_vue.h)(_vue.TransitionGroup,{key:l,name:"vxe-header--col-list"+(c?"":"-disabled"),tag:"tr",class:["vxe-footer--row",u?_xeUtils.default.isFunction(u)?u(o):u:""],style:d?_xeUtils.default.isFunction(d)?d(o):d:null},{default:()=>I(r,i,n,e,l,t)}):(0,_vue.h)("tr",{key:l,class:["vxe-footer--row",u?_xeUtils.default.isFunction(u)?u(o):u:""],style:d?_xeUtils.default.isFunction(d)?d(o):d:null},I(r,i,n,e,l,t))})})(v,f))])])])}}});