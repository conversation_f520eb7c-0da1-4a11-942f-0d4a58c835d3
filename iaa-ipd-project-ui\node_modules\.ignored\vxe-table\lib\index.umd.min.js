((e,t)=>{"object"==typeof exports&&"object"==typeof module?module.exports=t(require("vue"),require("@vxe-ui/core"),require("xe-utils")):"function"==typeof define&&define.amd?define([,"@vxe-ui/core","xe-utils"],t):"object"==typeof exports?exports.VxeUITable=t(require("vue"),require("@vxe-ui/core"),require("xe-utils")):e.VxeUITable=t(e.Vue,e.VxeUI,e.XEUtils)})("undefined"!=typeof self?self:this,function(Ht,Bt,Pt){{var Nt={9274:function(e){e.exports=Ht},4345:function(e){e.exports=Bt},8871:function(e){e.exports=Pt},9306:function(e,t,r){var l=r(4901),o=r(6823),a=TypeError;e.exports=function(e){if(l(e))return e;throw new a(o(e)+" is not a function")}},679:function(e,t,r){var l=r(1625),o=TypeError;e.exports=function(e,t){if(l(t,e))return e;throw new o("Incorrect invocation")}},8551:function(e,t,r){var l=r(34),o=String,a=TypeError;e.exports=function(e){if(l(e))return e;throw new a(o(e)+" is not an object")}},9617:function(e,t,r){function l(i){return function(e,t,r){var l=s(e),o=c(l);if(0!==o){var a,n=d(r,o);if(i&&t!=t){for(;n<o;)if((a=l[n++])!=a)return!0}else for(;n<o;n++)if((i||n in l)&&l[n]===t)return i||n||0}return!i&&-1}}var s=r(5397),d=r(5610),c=r(6198);e.exports={includes:l(!0),indexOf:l(!1)}},4527:function(e,t,r){var l=r(3724),o=r(4376),a=TypeError,n=Object.getOwnPropertyDescriptor,r=l&&!function(){if(void 0!==this)return 1;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(e){return e instanceof TypeError}}();e.exports=r?function(e,t){if(o(e)&&!n(e,"length").writable)throw new a("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t}},6319:function(e,t,r){var o=r(8551),a=r(9539);e.exports=function(t,e,r,l){try{return l?e(o(r)[0],r[1]):e(r)}catch(e){a(t,"throw",e)}}},2195:function(e,t,r){var r=r(9504),l=r({}.toString),o=r("".slice);e.exports=function(e){return o(l(e),8,-1)}},6955:function(e,t,r){var l=r(2140),o=r(4901),a=r(2195),n=r(8227)("toStringTag"),i=Object,s="Arguments"===a(function(){return arguments}());e.exports=l?a:function(e){var t;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(t=((e,t)=>{try{return e[t]}catch(e){}})(e=i(e),n))?t:s?a(e):"Object"===(t=a(e))&&o(e.callee)?"Arguments":t}},7740:function(e,t,r){var s=r(9297),d=r(5031),c=r(7347),u=r(4913);e.exports=function(e,t,r){for(var l=d(t),o=u.f,a=c.f,n=0;n<l.length;n++){var i=l[n];s(e,i)||r&&s(r,i)||o(e,i,a(t,i))}}},2211:function(e,t,r){r=r(9039);e.exports=!r(function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})},2529:function(e){e.exports=function(e,t){return{value:e,done:t}}},6699:function(e,t,r){var l=r(3724),o=r(4913),a=r(6980);e.exports=l?function(e,t,r){return o.f(e,t,a(1,r))}:function(e,t,r){return e[t]=r,e}},6980:function(e){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},4659:function(e,t,r){var l=r(3724),o=r(4913),a=r(6980);e.exports=function(e,t,r){l?o.f(e,t,a(0,r)):e[t]=r}},2106:function(e,t,r){var l=r(283),o=r(4913);e.exports=function(e,t,r){return r.get&&l(r.get,t,{getter:!0}),r.set&&l(r.set,t,{setter:!0}),o.f(e,t,r)}},6840:function(e,t,r){var n=r(4901),i=r(4913),s=r(283),d=r(9433);e.exports=function(e,t,r,l){var o=(l=l||{}).enumerable,a=void 0!==l.name?l.name:t;if(n(r)&&s(r,a,l),l.global)o?e[t]=r:d(t,r);else{try{l.unsafe?e[t]&&(o=!0):delete e[t]}catch(e){}o?e[t]=r:i.f(e,t,{value:r,enumerable:!1,configurable:!l.nonConfigurable,writable:!l.nonWritable})}return e}},6279:function(e,t,r){var o=r(6840);e.exports=function(e,t,r){for(var l in t)o(e,l,t[l],r);return e}},9433:function(e,t,r){var l=r(4576),o=Object.defineProperty;e.exports=function(t,r){try{o(l,t,{value:r,configurable:!0,writable:!0})}catch(e){l[t]=r}return r}},3724:function(e,t,r){r=r(9039);e.exports=!r(function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})},4055:function(e,t,r){var l=r(4576),r=r(34),o=l.document,a=r(o)&&r(o.createElement);e.exports=function(e){return a?o.createElement(e):{}}},6837:function(e){var t=TypeError;e.exports=function(e){if(9007199254740991<e)throw t("Maximum allowed index exceeded");return e}},8727:function(e){e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},2839:function(e,t,r){r=r(4576).navigator,r=r&&r.userAgent;e.exports=r?String(r):""},9519:function(e,t,r){var l,o,a=r(4576),r=r(2839),n=a.process,a=a.Deno,n=n&&n.versions||a&&a.version,a=n&&n.v8;!(o=a?0<(l=a.split("."))[0]&&l[0]<4?1:+(l[0]+l[1]):o)&&r&&(!(l=r.match(/Edge\/(\d+)/))||74<=l[1])&&(l=r.match(/Chrome\/(\d+)/))&&(o=+l[1]),e.exports=o},6518:function(e,t,r){var d=r(4576),c=r(7347).f,u=r(6699),p=r(6840),h=r(9433),g=r(7740),m=r(2796);e.exports=function(e,t){var r,l,o,a=e.target,n=e.global,i=e.stat,s=n?d:i?d[a]||h(a,{}):d[a]&&d[a].prototype;if(s)for(r in t){if(l=t[r],o=e.dontCallGetSet?(o=c(s,r))&&o.value:s[r],!m(n?r:a+(i?".":"#")+r,e.forced)&&void 0!==o){if(typeof l==typeof o)continue;g(l,o)}(e.sham||o&&o.sham)&&u(l,"sham",!0),p(s,r,l,e)}}},9039:function(e){e.exports=function(e){try{return!!e()}catch(e){return!0}}},6080:function(e,t,r){var l=r(7476),o=r(9306),a=r(616),n=l(l.bind);e.exports=function(e,t){return o(e),void 0===t?e:a?n(e,t):function(){return e.apply(t,arguments)}}},616:function(e,t,r){r=r(9039);e.exports=!r(function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")})},9565:function(e,t,r){var r=r(616),l=Function.prototype.call;e.exports=r?l.bind(l):function(){return l.apply(l,arguments)}},350:function(e,t,r){var l=r(3724),r=r(9297),o=Function.prototype,a=l&&Object.getOwnPropertyDescriptor,r=r(o,"name"),n=r&&"something"===function(){}.name,l=r&&(!l||a(o,"name").configurable);e.exports={EXISTS:r,PROPER:n,CONFIGURABLE:l}},7476:function(e,t,r){var l=r(2195),o=r(9504);e.exports=function(e){if("Function"===l(e))return o(e)}},9504:function(e,t,r){var r=r(616),l=Function.prototype,o=l.call,l=r&&l.bind.bind(o,o);e.exports=r?l:function(e){return function(){return o.apply(e,arguments)}}},7751:function(e,t,r){var l=r(4576),o=r(4901);e.exports=function(e,t){return arguments.length<2?(r=l[e],o(r)?r:void 0):l[e]&&l[e][t];var r}},1767:function(e){e.exports=function(e){return{iterator:e,next:e.next,done:!1}}},851:function(e,t,r){var l=r(6955),o=r(5966),a=r(4117),n=r(6269),i=r(8227)("iterator");e.exports=function(e){if(!a(e))return o(e,i)||o(e,"@@iterator")||n[l(e)]}},81:function(e,t,r){var l=r(9565),o=r(9306),a=r(8551),n=r(6823),i=r(851),s=TypeError;e.exports=function(e,t){t=arguments.length<2?i(e):t;if(o(t))return a(l(t,e));throw new s(n(e)+" is not iterable")}},5966:function(e,t,r){var l=r(9306),o=r(4117);e.exports=function(e,t){e=e[t];return o(e)?void 0:l(e)}},4576:function(e,t,r){function l(e){return e&&e.Math===Math&&e}e.exports=l("object"==typeof globalThis&&globalThis)||l("object"==typeof window&&window)||l("object"==typeof self&&self)||l("object"==typeof r.g&&r.g)||l("object"==typeof this&&this)||function(){return this}()||Function("return this")()},9297:function(e,t,r){var l=r(9504),o=r(8981),a=l({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return a(o(e),t)}},421:function(e){e.exports={}},397:function(e,t,r){r=r(7751);e.exports=r("document","documentElement")},5917:function(e,t,r){var l=r(3724),o=r(9039),a=r(4055);e.exports=!l&&!o(function(){return 7!==Object.defineProperty(a("div"),"a",{get:function(){return 7}}).a})},7055:function(e,t,r){var l=r(9504),o=r(9039),a=r(2195),n=Object,i=l("".split);e.exports=o(function(){return!n("z").propertyIsEnumerable(0)})?function(e){return"String"===a(e)?i(e,""):n(e)}:n},3706:function(e,t,r){var l=r(9504),o=r(4901),r=r(7629),a=l(Function.toString);o(r.inspectSource)||(r.inspectSource=function(e){return a(e)}),e.exports=r.inspectSource},1181:function(e,t,r){var l,o,a,n,i=r(8622),s=r(4576),d=r(34),c=r(6699),u=r(9297),p=r(7629),h=r(6119),r=r(421),g="Object already initialized",m=s.TypeError,s=s.WeakMap,v=i||p.state?((a=p.state||(p.state=new s)).get=a.get,a.has=a.has,a.set=a.set,l=function(e,t){if(a.has(e))throw new m(g);return t.facade=e,a.set(e,t),t},o=function(e){return a.get(e)||{}},function(e){return a.has(e)}):(r[n=h("state")]=!0,l=function(e,t){if(u(e,n))throw new m(g);return t.facade=e,c(e,n,t),t},o=function(e){return u(e,n)?e[n]:{}},function(e){return u(e,n)});e.exports={set:l,get:o,has:v,enforce:function(e){return v(e)?o(e):l(e,{})},getterFor:function(t){return function(e){if(d(e)&&(e=o(e)).type===t)return e;throw new m("Incompatible receiver, "+t+" required")}}}},4209:function(e,t,r){var l=r(8227),o=r(6269),a=l("iterator"),n=Array.prototype;e.exports=function(e){return void 0!==e&&(o.Array===e||n[a]===e)}},4376:function(e,t,r){var l=r(2195);e.exports=Array.isArray||function(e){return"Array"===l(e)}},4901:function(e){var t="object"==typeof document&&document.all;e.exports=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(e){return"function"==typeof e}},2796:function(e,t,r){function l(e,t){return(e=s[i(e)])===c||e!==d&&(a(t)?o(t):!!t)}var o=r(9039),a=r(4901),n=/#|\.prototype\./,i=l.normalize=function(e){return String(e).replace(n,".").toLowerCase()},s=l.data={},d=l.NATIVE="N",c=l.POLYFILL="P";e.exports=l},4117:function(e){e.exports=function(e){return null==e}},34:function(e,t,r){var l=r(4901);e.exports=function(e){return"object"==typeof e?null!==e:l(e)}},6395:function(e){e.exports=!1},757:function(e,t,r){var l=r(7751),o=r(4901),a=r(1625),r=r(7040),n=Object;e.exports=r?function(e){return"symbol"==typeof e}:function(e){var t=l("Symbol");return o(t)&&a(t.prototype,n(e))}},2652:function(e,t,r){function f(e,t){this.stopped=e,this.result=t}var x=r(6080),b=r(9565),C=r(8551),w=r(6823),y=r(4209),T=r(6198),E=r(1625),R=r(81),S=r(851),D=r(9539),I=TypeError,F=f.prototype;e.exports=function(e,t,r){function l(e){return a&&D(a,"normal",e),new f(!0,e)}function o(e){return p?(C(e),m?v(e[0],e[1],l):v(e[0],e[1])):m?v(e,l):v(e)}var a,n,i,s,d,c,u=r&&r.that,p=!(!r||!r.AS_ENTRIES),h=!(!r||!r.IS_RECORD),g=!(!r||!r.IS_ITERATOR),m=!(!r||!r.INTERRUPTED),v=x(t,u);if(h)a=e.iterator;else if(g)a=e;else{if(!(r=S(e)))throw new I(w(e)+" is not iterable");if(y(r)){for(n=0,i=T(e);n<i;n++)if((s=o(e[n]))&&E(F,s))return s;return new f(!1)}a=R(e,r)}for(d=(h?e:a).next;!(c=b(d,a)).done;){try{s=o(c.value)}catch(e){D(a,"throw",e)}if("object"==typeof s&&s&&E(F,s))return s}return new f(!1)}},9539:function(e,t,r){var a=r(9565),n=r(8551),i=r(5966);e.exports=function(e,t,r){var l,o;n(e);try{if(!(l=i(e,"return"))){if("throw"===t)throw r;return r}l=a(l,e)}catch(e){o=!0,l=e}if("throw"===t)throw r;if(o)throw l;return n(l),r}},9462:function(e,t,r){function l(l){var o=d.getterFor(l?m:g);return i(n(u),{next:function(){var t=o(this);if(l)return t.nextHandler();try{var e=t.done?void 0:t.nextHandler();return p(e,t.done)}catch(e){throw t.done=!0,e}},return:function(){var e,t=o(this),r=t.iterator;if(t.done=!0,l)return(e=c(r,"return"))?a(e,r):p(void 0,!0);if(t.inner)try{h(t.inner.iterator,"normal")}catch(e){return h(r,"throw",e)}return r&&h(r,"normal"),p(void 0,!0)}})}var a=r(9565),n=r(2360),o=r(6699),i=r(6279),s=r(8227),d=r(1181),c=r(5966),u=r(7657).IteratorPrototype,p=r(2529),h=r(9539),r=s("toStringTag"),g="IteratorHelper",m="WrapForValidIterator",v=d.set,f=l(!0),x=l(!1);o(x,r,"Iterator Helper"),e.exports=function(r,l){function e(e,t){t?(t.iterator=e.iterator,t.next=e.next):t=e,t.type=l?m:g,t.nextHandler=r,t.counter=0,t.done=!1,v(this,t)}return e.prototype=l?f:x,e}},713:function(e,t,r){var l=r(9565),o=r(9306),a=r(8551),n=r(1767),i=r(9462),s=r(6319),d=i(function(){var e=this.iterator,t=a(l(this.next,e));if(!(this.done=!!t.done))return s(e,this.mapper,[t.value,this.counter++],!0)});e.exports=function(e){return a(this),o(e),new d(n(this),{mapper:e})}},7657:function(e,t,r){var l,o,a=r(9039),n=r(4901),i=r(34),s=r(2360),d=r(2787),c=r(6840),u=r(8227),r=r(6395),p=u("iterator"),u=!1;[].keys&&("next"in(o=[].keys())?(d=d(d(o)))!==Object.prototype&&(l=d):u=!0),!i(l)||a(function(){var e={};return l[p].call(e)!==e})?l={}:r&&(l=s(l)),n(l[p])||c(l,p,function(){return this}),e.exports={IteratorPrototype:l,BUGGY_SAFARI_ITERATORS:u}},6269:function(e){e.exports={}},6198:function(e,t,r){var l=r(8014);e.exports=function(e){return l(e.length)}},283:function(e,t,r){var l=r(9504),o=r(9039),a=r(4901),n=r(9297),i=r(3724),s=r(350).CONFIGURABLE,d=r(3706),r=r(1181),c=r.enforce,u=r.get,p=String,h=Object.defineProperty,g=l("".slice),m=l("".replace),v=l([].join),f=i&&!o(function(){return 8!==h(function(){},"length",{value:8}).length}),x=String(String).split("String"),r=e.exports=function(e,t,r){"Symbol("===g(p(t),0,7)&&(t="["+m(p(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(t="get "+t),r&&r.setter&&(t="set "+t),(!n(e,"name")||s&&e.name!==t)&&(i?h(e,"name",{value:t,configurable:!0}):e.name=t),f&&r&&n(r,"arity")&&e.length!==r.arity&&h(e,"length",{value:r.arity});try{r&&n(r,"constructor")&&r.constructor?i&&h(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}r=c(e);return n(r,"source")||(r.source=v(x,"string"==typeof t?t:"")),e};Function.prototype.toString=r(function(){return a(this)&&u(this).source||d(this)},"toString")},741:function(e){var t=Math.ceil,r=Math.floor;e.exports=Math.trunc||function(e){e=+e;return(0<e?r:t)(e)}},2360:function(e,t,r){function l(){}function o(e){e.write(f("")),e.close();var t=e.parentWindow.Object;return e=null,t}var a,n=r(8551),i=r(6801),s=r(8727),d=r(421),c=r(397),u=r(4055),r=r(6119),p=">",h="<",g="prototype",m="script",v=r("IE_PROTO"),f=function(e){return h+m+p+e+h+"/"+m+p},x=function(){try{a=new ActiveXObject("htmlfile")}catch(e){}x="undefined"==typeof document||document.domain&&a?o(a):(e=u("iframe"),t="java"+m+":",e.style.display="none",c.appendChild(e),e.src=String(t),(t=e.contentWindow.document).open(),t.write(f("document.F=Object")),t.close(),t.F);for(var e,t,r=s.length;r--;)delete x[g][s[r]];return x()};d[v]=!0,e.exports=Object.create||function(e,t){var r;return null!==e?(l[g]=n(e),r=new l,l[g]=null,r[v]=e):r=x(),void 0===t?r:i.f(r,t)}},6801:function(e,t,r){var l=r(3724),o=r(8686),i=r(4913),s=r(8551),d=r(5397),c=r(1072);t.f=l&&!o?Object.defineProperties:function(e,t){s(e);for(var r,l=d(t),o=c(t),a=o.length,n=0;n<a;)i.f(e,r=o[n++],l[r]);return e}},4913:function(e,t,r){var l=r(3724),o=r(5917),a=r(8686),n=r(8551),i=r(6969),s=TypeError,d=Object.defineProperty,c=Object.getOwnPropertyDescriptor,u="enumerable",p="configurable",h="writable";t.f=l?a?function(e,t,r){var l;return n(e),t=i(t),n(r),"function"==typeof e&&"prototype"===t&&"value"in r&&h in r&&!r[h]&&(l=c(e,t))&&l[h]&&(e[t]=r.value,r={configurable:(p in r?r:l)[p],enumerable:(u in r?r:l)[u],writable:!1}),d(e,t,r)}:d:function(e,t,r){if(n(e),t=i(t),n(r),o)try{return d(e,t,r)}catch(e){}if("get"in r||"set"in r)throw new s("Accessors not supported");return"value"in r&&(e[t]=r.value),e}},7347:function(e,t,r){var l=r(3724),o=r(9565),a=r(8773),n=r(6980),i=r(5397),s=r(6969),d=r(9297),c=r(5917),u=Object.getOwnPropertyDescriptor;t.f=l?u:function(e,t){if(e=i(e),t=s(t),c)try{return u(e,t)}catch(e){}if(d(e,t))return n(!o(a.f,e,t),e[t])}},8480:function(e,t,r){var l=r(1828),o=r(8727).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return l(e,o)}},3717:function(e,t){t.f=Object.getOwnPropertySymbols},2787:function(e,t,r){var l=r(9297),o=r(4901),a=r(8981),n=r(6119),r=r(2211),i=n("IE_PROTO"),s=Object,d=s.prototype;e.exports=r?s.getPrototypeOf:function(e){var t,e=a(e);return l(e,i)?e[i]:(t=e.constructor,o(t)&&e instanceof t?t.prototype:e instanceof s?d:null)}},1625:function(e,t,r){r=r(9504);e.exports=r({}.isPrototypeOf)},1828:function(e,t,r){var l=r(9504),n=r(9297),i=r(5397),s=r(9617).indexOf,d=r(421),c=l([].push);e.exports=function(e,t){var r,l=i(e),o=0,a=[];for(r in l)!n(d,r)&&n(l,r)&&c(a,r);for(;t.length>o;)!n(l,r=t[o++])||~s(a,r)||c(a,r);return a}},1072:function(e,t,r){var l=r(1828),o=r(8727);e.exports=Object.keys||function(e){return l(e,o)}},8773:function(e,t){var r={}.propertyIsEnumerable,l=Object.getOwnPropertyDescriptor,o=l&&!r.call({1:2},1);t.f=o?function(e){e=l(this,e);return!!e&&e.enumerable}:r},4270:function(e,t,r){var o=r(9565),a=r(4901),n=r(34),i=TypeError;e.exports=function(e,t){var r,l;if("string"===t&&a(r=e.toString)&&!n(l=o(r,e)))return l;if(a(r=e.valueOf)&&!n(l=o(r,e)))return l;if("string"!==t&&a(r=e.toString)&&!n(l=o(r,e)))return l;throw new i("Can't convert object to primitive value")}},5031:function(e,t,r){var l=r(7751),o=r(9504),a=r(8480),n=r(3717),i=r(8551),s=o([].concat);e.exports=l("Reflect","ownKeys")||function(e){var t=a.f(i(e)),r=n.f;return r?s(t,r(e)):t}},7750:function(e,t,r){var l=r(4117),o=TypeError;e.exports=function(e){if(l(e))throw new o("Can't call method on "+e);return e}},6119:function(e,t,r){var l=r(5745),o=r(3392),a=l("keys");e.exports=function(e){return a[e]||(a[e]=o(e))}},7629:function(e,t,r){var l=r(6395),o=r(4576),r=r(9433),a="__core-js_shared__",e=e.exports=o[a]||r(a,{});(e.versions||(e.versions=[])).push({version:"3.39.0",mode:l?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.39.0/LICENSE",source:"https://github.com/zloirock/core-js"})},5745:function(e,t,r){var l=r(7629);e.exports=function(e,t){return l[e]||(l[e]=t||{})}},4495:function(e,t,r){var l=r(9519),o=r(9039),a=r(4576).String;e.exports=!!Object.getOwnPropertySymbols&&!o(function(){var e=Symbol("symbol detection");return!a(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&l&&l<41})},5610:function(e,t,r){var l=r(1291),o=Math.max,a=Math.min;e.exports=function(e,t){e=l(e);return e<0?o(e+t,0):a(e,t)}},5397:function(e,t,r){var l=r(7055),o=r(7750);e.exports=function(e){return l(o(e))}},1291:function(e,t,r){var l=r(741);e.exports=function(e){e=+e;return e!=e||0==e?0:l(e)}},8014:function(e,t,r){var l=r(1291),o=Math.min;e.exports=function(e){e=l(e);return 0<e?o(e,9007199254740991):0}},8981:function(e,t,r){var l=r(7750),o=Object;e.exports=function(e){return o(l(e))}},2777:function(e,t,r){var l=r(9565),o=r(34),a=r(757),n=r(5966),i=r(4270),r=r(8227),s=TypeError,d=r("toPrimitive");e.exports=function(e,t){if(!o(e)||a(e))return e;var r=n(e,d);if(r){if(r=l(r,e,t=void 0===t?"default":t),!o(r)||a(r))return r;throw new s("Can't convert object to primitive value")}return i(e,t=void 0===t?"number":t)}},6969:function(e,t,r){var l=r(2777),o=r(757);e.exports=function(e){e=l(e,"string");return o(e)?e:e+""}},2140:function(e,t,r){var l={};l[r(8227)("toStringTag")]="z",e.exports="[object z]"===String(l)},6823:function(e){var t=String;e.exports=function(e){try{return t(e)}catch(e){return"Object"}}},3392:function(e,t,r){var r=r(9504),l=0,o=Math.random(),a=r(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+a(++l+o,36)}},7040:function(e,t,r){r=r(4495);e.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},8686:function(e,t,r){var l=r(3724),r=r(9039);e.exports=l&&r(function(){return 42!==Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype})},8622:function(e,t,r){var l=r(4576),r=r(4901),l=l.WeakMap;e.exports=r(l)&&/native code/.test(String(l))},8227:function(e,t,r){var l=r(4576),o=r(5745),a=r(9297),n=r(3392),i=r(4495),r=r(7040),s=l.Symbol,d=o("wks"),c=r?s.for||s:s&&s.withoutSetter||n;e.exports=function(e){return a(d,e)||(d[e]=i&&a(s,e)?s[e]:c("Symbol."+e)),d[e]}},4114:function(e,t,r){var l=r(6518),a=r(8981),n=r(6198),i=r(4527),s=r(6837);l({target:"Array",proto:!0,arity:1,forced:r(9039)(function(){return 4294967297!==[].push.call({length:4294967296},1)})||!(()=>{try{Object.defineProperty([],"length",{writable:!1}).push()}catch(e){return e instanceof TypeError}})()},{push:function(e){var t=a(this),r=n(t),l=arguments.length;s(r+l);for(var o=0;o<l;o++)t[r]=arguments[o],r++;return i(t,r),r}})},8111:function(e,t,r){function l(){if(i(this,v),c(this)===v)throw new C("Abstract class Iterator not directly constructable")}function o(t,e){f?u(v,t,{configurable:!0,get:function(){return e},set:function(e){if(s(this),this===v)throw new C("You can't redefine this property");g(this,t)?this[t]=e:p(this,t,e)}}):v[t]=e}var a=r(6518),n=r(4576),i=r(679),s=r(8551),d=r(4901),c=r(2787),u=r(2106),p=r(4659),h=r(9039),g=r(9297),m=r(8227),v=r(7657).IteratorPrototype,f=r(3724),r=r(6395),x="constructor",b="Iterator",m=m("toStringTag"),C=TypeError,w=n[b],n=r||!d(w)||w.prototype!==v||!h(function(){w({})});g(v,m)||o(m,b),!n&&g(v,x)&&v[x]!==Object||o(x,l),l.prototype=v,a({global:!0,constructor:!0,forced:n},{Iterator:l})},1148:function(e,t,r){var l=r(6518),o=r(2652),a=r(9306),n=r(8551),i=r(1767);l({target:"Iterator",proto:!0,real:!0},{every:function(r){n(this),a(r);var e=i(this),l=0;return!o(e,function(e,t){if(!r(e,l++))return t()},{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},2489:function(e,t,r){var l=r(6518),o=r(9565),a=r(9306),n=r(8551),i=r(1767),s=r(9462),d=r(6319),r=r(6395),c=s(function(){for(var e,t=this.iterator,r=this.predicate,l=this.next;;){if(e=n(o(l,t)),this.done=!!e.done)return;if(d(t,r,[e=e.value,this.counter++],!0))return e}});l({target:"Iterator",proto:!0,real:!0,forced:r},{filter:function(e){return n(this),a(e),new c(i(this),{predicate:e})}})},116:function(e,t,r){var l=r(6518),o=r(2652),a=r(9306),n=r(8551),i=r(1767);l({target:"Iterator",proto:!0,real:!0},{find:function(r){n(this),a(r);var e=i(this),l=0;return o(e,function(e,t){if(r(e,l++))return t(e)},{IS_RECORD:!0,INTERRUPTED:!0}).result}})},7588:function(e,t,r){var l=r(6518),o=r(2652),a=r(9306),n=r(8551),i=r(1767);l({target:"Iterator",proto:!0,real:!0},{forEach:function(t){n(this),a(t);var e=i(this),r=0;o(e,function(e){t(e,r++)},{IS_RECORD:!0})}})},1701:function(e,t,r){var l=r(6518),o=r(713);l({target:"Iterator",proto:!0,real:!0,forced:r(6395)},{map:o})},8237:function(e,t,r){var l=r(6518),a=r(2652),n=r(9306),i=r(8551),s=r(1767),d=TypeError;l({target:"Iterator",proto:!0,real:!0},{reduce:function(t){i(this),n(t);var e=s(this),r=arguments.length<2,l=r?void 0:arguments[1],o=0;if(a(e,function(e){l=r?(r=!1,e):t(l,e,o),o++},{IS_RECORD:!0}),r)throw new d("Reduce of empty iterator with no initial value");return l}})},3579:function(e,t,r){var l=r(6518),o=r(2652),a=r(9306),n=r(8551),i=r(1767);l({target:"Iterator",proto:!0,real:!0},{some:function(r){n(this),a(r);var e=i(this),l=0;return o(e,function(e,t){if(r(e,l++))return t()},{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},1806:function(e,t,r){var l=r(6518),o=r(8551),a=r(2652),n=r(1767),i=[].push;l({target:"Iterator",proto:!0,real:!0},{toArray:function(){var e=[];return a(n(o(this)),i,{that:e,IS_RECORD:!0}),e}})},8992:function(e,t,r){r(8111)},3215:function(e,t,r){r(1148)},4520:function(e,t,r){r(2489)},2577:function(e,t,r){r(116)},3949:function(e,t,r){r(7588)},1454:function(e,t,r){r(1701)},8872:function(e,t,r){r(8237)},7550:function(e,t,r){r(3579)},1795:function(e,t,r){r(1806)}},zt={};function yt(e){var t=zt[e];return void 0!==t||(t=zt[e]={exports:{}},Nt[e].call(t.exports,t,t.exports,yt)),t.exports}yt.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return yt.d(t,{a:t}),t},yt.d=function(e,t){for(var r in t)yt.o(t,r)&&!yt.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},yt.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),yt.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},yt.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},yt.p="";var jt={},Ut=(yt.r(jt),yt.d(jt,{Colgroup:function(){return ie},Column:function(){return ne},Grid:function(){return He},Table:function(){return xt},Toolbar:function(){return Ct},VXETable:function(){return q},VxeColgroup:function(){return l},VxeColumn:function(){return r},VxeGrid:function(){return _e},VxeTable:function(){return ft},VxeToolbar:function(){return bt},VxeUI:function(){return Dl.VxeUI},_t:function(){return G},clipboard:function(){return A},commands:function(){return k},config:function(){return z},default:function(){return Zo},formats:function(){return F},getConfig:function(){return s},getI18n:function(){return m},getIcon:function(){return c},getTheme:function(){return n},globalEvents:function(){return v},globalResize:function(){return f},hooks:function(){return V},install:function(){return Yo},interceptor:function(){return O},log:function(){return L},menus:function(){return w},modal:function(){return Z},print:function(){return Y},readFile:function(){return K},renderer:function(){return x},saveFile:function(){return X},setConfig:function(){return i},setI18n:function(){return h},setIcon:function(){return d},setLanguage:function(){return p},setTheme:function(){return a},setup:function(){return P},t:function(){return W},use:function(){return $},validators:function(){return C},version:function(){return e}}),{}),Dl=(yt.r(Ut),yt.d(Ut,{Colgroup:function(){return ie},Column:function(){return ne},Grid:function(){return He},Table:function(){return xt},Toolbar:function(){return Ct},VXETable:function(){return q},VxeColgroup:function(){return l},VxeColumn:function(){return r},VxeGrid:function(){return _e},VxeTable:function(){return ft},VxeToolbar:function(){return bt},VxeUI:function(){return Dl.VxeUI},_t:function(){return G},clipboard:function(){return A},commands:function(){return k},config:function(){return z},formats:function(){return F},getConfig:function(){return s},getI18n:function(){return m},getIcon:function(){return c},getTheme:function(){return n},globalEvents:function(){return v},globalResize:function(){return f},hooks:function(){return V},install:function(){return Yo},interceptor:function(){return O},log:function(){return L},menus:function(){return w},modal:function(){return Z},print:function(){return Y},readFile:function(){return K},renderer:function(){return x},saveFile:function(){return X},setConfig:function(){return i},setI18n:function(){return h},setIcon:function(){return d},setLanguage:function(){return p},setTheme:function(){return a},setup:function(){return P},t:function(){return W},use:function(){return $},validators:function(){return C},version:function(){return e}}),"undefined"!=typeof window&&(Tt=(Tt=window.document.currentScript)&&Tt.src.match(/(.+\/)[^/]+\.js(\?.*)?$/))&&(yt.p=Tt[1]),yt(8992),yt(3949),yt(4345)),Tt=yt(8871),Il=yt.n(Tt),Wt=null,Gt=null,qt=null,Xt="z-index-manage",Kt=null,Yt="z-index-style",Zt={m:1e3,s:1e3};function Qt(){return Wt||"undefined"!=typeof document&&(Wt=document),Wt}function Jt(){return Gt=Wt&&!Gt?Wt.body||Wt.getElementsByTagName("body")[0]:Gt}function er(){var e;Kt||!(e=Qt())||(Kt=e.getElementById(Yt))||((Kt=e.createElement("style")).id=Yt,e.getElementsByTagName("head")[0].appendChild(Kt)),Kt&&(Kt.innerHTML=":root{--dom-main"+(e="-z-index")+":"+ar()+";--dom-sub"+e+":"+dr()+"}")}function tr(){var e,t;return qt||(e=Qt())&&!(qt=e.getElementById(Xt))&&(t=Jt())&&((qt=e.createElement("div")).id=Xt,qt.style.display="none",t.appendChild(qt),lr(Zt.m),ir(Zt.s)),qt}function rr(r){return function(e){var t;return e&&(e=Number(e),Zt[r]=e,t=tr())&&(t.dataset?t.dataset[r]=e+"":t.setAttribute("data-"+r,e+"")),er(),Zt[r]}}var lr=rr("m");function or(l,o){return function(e){var t=tr(),r=(r=t&&(t=t.dataset?t.dataset[l]:t.getAttribute("data-"+l))?Number(t):r)||Zt[l];return e?Number(e)<r?o():e:r}}var ar=or("m",nr);function nr(){return lr(ar()+1)}var ir=rr("s"),sr=or("s",cr);function dr(){return ar()+sr()}function cr(){return ir(sr()+1),dr()}var Tt={setCurrent:lr,getCurrent:ar,getNext:nr,setSubCurrent:ir,getSubCurrent:dr,getSubNext:cr,getMax:function(){var e=0;if(Qt()){var t=Jt();if(t)for(var r=t.getElementsByTagName("*"),l=0;l<r.length;l++){var o=r[l];o&&o.style&&1===o.nodeType&&(o=o.style.zIndex)&&/^\d+$/.test(o)&&(e=Math.max(e,Number(o)))}}return e}},ur=(er(),Tt);function Fl(e){return e&&!1!==e.enabled}function pr(e){return null==e||""===e}function hr(e){var e=e.name,t=Il().lastIndexOf(e,"."),r=e.substring(t+1,e.length).toLowerCase();return{filename:e.substring(0,t),type:r}}function ql(){return ur.getNext()}function Xl(){return ur.getCurrent()}function Kl(e){return e&&e.children&&0<e.children.length}function Ml(e,t){var r;return e?(r=Dl.VxeUI.getConfig().translate,Il().toValueString(r?r(""+e,t):e)):""}function kl(e,t){return""+(pr(e)?t?Dl.VxeUI.getConfig().emptyCell:"":e)}function Ol(e){return""===e||Il().eqNull(e)}let e="4.13.41",a=(Dl.VxeUI.version=e,Dl.VxeUI.tableVersion=e,Dl.VxeUI.setConfig({emptyCell:"　",table:{fit:!0,showHeader:!0,animat:!0,delayHover:250,autoResize:!0,minHeight:144,resizeConfig:{},resizableConfig:{dragMode:"auto",showDragTip:!0,isSyncAutoHeight:!0,isSyncAutoWidth:!0,minHeight:18},radioConfig:{strict:!0},rowDragConfig:{showIcon:!0,animation:!0,showGuidesStatus:!0,showDragTip:!0},columnDragConfig:{showIcon:!0,animation:!0,showGuidesStatus:!0,showDragTip:!0},checkboxConfig:{strict:!0},tooltipConfig:{enterable:!0},validConfig:{showMessage:!0,autoClear:!0,autoPos:!0,message:"inline",msgMode:"single",theme:"beautify"},columnConfig:{maxFixedSize:4},cellConfig:{padding:!0},headerCellConfig:{height:"unset"},footerCellConfig:{height:"unset"},customConfig:{allowVisible:!0,allowResizable:!0,allowFixed:!0,allowSort:!0,showFooter:!0,placement:"top-right",storeOptions:{visible:!0,resizable:!0,sort:!0,fixed:!0},modalOptions:{showMaximize:!0,mask:!0,lockView:!0,resize:!0,escClosable:!0},drawerOptions:{mask:!0,lockView:!0,escClosable:!0,resize:!0}},sortConfig:{showIcon:!0,allowClear:!0,allowBtn:!0,iconLayout:"vertical"},filterConfig:{showIcon:!0},aggregateConfig:{padding:!0,rowField:"id",parentField:"_X_ROW_PARENT_KEY",childrenField:"_X_ROW_CHILDREN",mapChildrenField:"_X_ROW_CHILD_LIST",indent:20,showIcon:!0},treeConfig:{padding:!0,rowField:"id",parentField:"parentId",childrenField:"children",hasChildField:"hasChild",mapChildrenField:"_X_ROW_CHILD",indent:20,showIcon:!0},expandConfig:{showIcon:!0,mode:"fixed"},editConfig:{showIcon:!0,showAsterisk:!0,autoFocus:!0},importConfig:{_typeMaps:{csv:1,html:1,xml:1,txt:1}},exportConfig:{_typeMaps:{csv:1,html:1,xml:1,txt:1}},printConfig:{},mouseConfig:{extension:!0},keyboardConfig:{isAll:!0,isEsc:!0},areaConfig:{autoClear:!0,selectCellByHeader:!0,selectCellByBody:!0,extendDirection:{top:!0,left:!0,bottom:!0,right:!0}},clipConfig:{isCopy:!0,isCut:!0,isPaste:!0},fnrConfig:{isFind:!0,isReplace:!0},virtualXConfig:{gt:24,preSize:1,oSize:0},virtualYConfig:{gt:100,preSize:1,oSize:0},scrollbarConfig:{}},grid:{formConfig:{enabled:!0},pagerConfig:{enabled:!0},toolbarConfig:{enabled:!0},proxyConfig:{enabled:!0,autoLoad:!0,showResponseMsg:!0,showActiveMsg:!0,props:{list:null,result:"result",total:"page.total",message:"message"}}},toolbar:{}}),Tt="vxe-table-icon-",Dl.VxeUI.setIcon({TABLE_SORT_ASC:Tt+"caret-up",TABLE_SORT_DESC:Tt+"caret-down",TABLE_FILTER_NONE:Tt+"funnel",TABLE_FILTER_MATCH:Tt+"funnel",TABLE_EDIT:Tt+"edit",TABLE_TITLE_PREFIX:Tt+"question-circle-fill",TABLE_TITLE_SUFFIX:Tt+"question-circle-fill",TABLE_TREE_LOADED:Tt+"spinner roll",TABLE_TREE_OPEN:Tt+"caret-right rotate90",TABLE_TREE_CLOSE:Tt+"caret-right",TABLE_EXPAND_LOADED:Tt+"spinner roll",TABLE_EXPAND_OPEN:Tt+"arrow-right rotate90",TABLE_EXPAND_CLOSE:Tt+"arrow-right",TABLE_CHECKBOX_CHECKED:Tt+"checkbox-checked-fill",TABLE_CHECKBOX_UNCHECKED:Tt+"checkbox-unchecked",TABLE_CHECKBOX_INDETERMINATE:Tt+"checkbox-indeterminate-fill",TABLE_RADIO_CHECKED:Tt+"radio-checked-fill",TABLE_RADIO_UNCHECKED:Tt+"radio-unchecked",TABLE_CUSTOM_SORT:Tt+"drag-handle",TABLE_MENU_OPTIONS:Tt+"arrow-right",TABLE_DRAG_ROW:Tt+"drag-handle",TABLE_DRAG_COLUMN:Tt+"drag-handle",TABLE_DRAG_STATUS_ROW:Tt+"sort",TABLE_DRAG_STATUS_SUB_ROW:Tt+"add-sub",TABLE_DRAG_STATUS_COLUMN:Tt+"swap",TABLE_DRAG_DISABLED:Tt+"no-drop",TABLE_ROW_GROUP_OPEN:Tt+"arrow-right rotate90",TABLE_ROW_GROUP_CLOSE:Tt+"arrow-right",TABLE_AGGREGATION_GROUPING:Tt+"grouping",TABLE_AGGREGATION_VALUES:Tt+"values",TABLE_AGGREGATION_DELETE:Tt+"close",TOOLBAR_TOOLS_REFRESH:Tt+"repeat",TOOLBAR_TOOLS_REFRESH_LOADING:Tt+"repeat roll",TOOLBAR_TOOLS_IMPORT:Tt+"upload",TOOLBAR_TOOLS_EXPORT:Tt+"download",TOOLBAR_TOOLS_PRINT:Tt+"print",TOOLBAR_TOOLS_FULLSCREEN:Tt+"fullscreen",TOOLBAR_TOOLS_MINIMIZE:Tt+"minimize",TOOLBAR_TOOLS_CUSTOM:Tt+"custom-column",TOOLBAR_TOOLS_FIXED_LEFT:Tt+"fixed-left",TOOLBAR_TOOLS_FIXED_LEFT_ACTIVE:Tt+"fixed-left-fill",TOOLBAR_TOOLS_FIXED_RIGHT:Tt+"fixed-right",TOOLBAR_TOOLS_FIXED_RIGHT_ACTIVE:Tt+"fixed-right-fill"}),Dl.VxeUI.setTheme),n=Dl.VxeUI.getTheme,i=Dl.VxeUI.setConfig,s=Dl.VxeUI.getConfig,d=Dl.VxeUI.setIcon,c=Dl.VxeUI.getIcon,p=Dl.VxeUI.setLanguage,h=Dl.VxeUI.setI18n,m=Dl.VxeUI.getI18n,v=Dl.VxeUI.globalEvents,f=Dl.VxeUI.globalResize,x=Dl.VxeUI.renderer,C=Dl.VxeUI.validators,w=Dl.VxeUI.menus,F=Dl.VxeUI.formats,k=Dl.VxeUI.commands,O=Dl.VxeUI.interceptor,A=Dl.VxeUI.clipboard,L=Dl.VxeUI.log,V=Dl.VxeUI.hooks,$=Dl.VxeUI.use,P=e=>Dl.VxeUI.setConfig(e),z=(Dl.VxeUI.setup=P,e=>Dl.VxeUI.setConfig(e)),W=(Dl.VxeUI.config=z,(e,t)=>Dl.VxeUI.getI18n(e,t)),G=(Dl.VxeUI.t=W,(e,t)=>Ml(e,t)),q=(Dl.VxeUI._t=G,Dl.VxeUI),X=e=>Dl.VxeUI.saveFile(e),K=e=>Dl.VxeUI.readFile(e),Y=e=>Dl.VxeUI.print(e),Z={get(e){return Dl.VxeUI.modal.get(e)},close(e){return Dl.VxeUI.modal.close(e)},open(e){return Dl.VxeUI.modal.open(e)},alert(e,t,r){return Dl.VxeUI.modal.alert(e,t,r)},confirm(e,t,r){return Dl.VxeUI.modal.confirm(e,t,r)},message(e,t){return Dl.VxeUI.modal.message(e,t)},notification(e,t,r){return Dl.VxeUI.modal.notification(e,t,r)}};var Al=yt(9274),Tt=Al.defineComponent,gr=(yt(4114),yt(1454),yt(7550),Dl.VxeUI.log),mr="table v4.13.41";let ul=gr.create("warn",mr),pl=gr.create("error",mr),{getI18n:Q,formats:J}=Dl.VxeUI;class Qo{constructor(e,t,{renderHeader:r,renderCell:l,renderFooter:o,renderData:a}={}){var n=e.props,i=e.xeGrid,s=t.formatter,d=!Il().isBoolean(t.visible)||t.visible,c=["seq","checkbox","radio","expand","html"];t.type&&-1===c.indexOf(t.type)&&ul("vxe.error.errProp",["type="+t.type,c.join(", ")]),(Il().isBoolean(t.cellRender)||t.cellRender&&!Il().isObject(t.cellRender))&&ul("vxe.error.errProp",["column.cell-render="+t.cellRender,"column.cell-render={}"]),(Il().isBoolean(t.editRender)||t.editRender&&!Il().isObject(t.editRender))&&ul("vxe.error.errProp",["column.edit-render="+t.editRender,"column.edit-render={}"]),"expand"===t.type&&(c=n.treeConfig,n=e.getComputeMaps().computeTreeOpts,e=n.value,c)&&(e.showLine||e.line)&&pl("vxe.error.errConflicts",["tree-config.showLine","column.type=expand"]),s&&(Il().isString(s)?(n=J.get(s)||Il()[s])&&Il().isFunction(n.tableCellFormatMethod||n.cellFormatMethod)||pl("vxe.error.notFormats",[s]):!Il().isArray(s)||(c=J.get(s[0])||Il()[s[0]])&&Il().isFunction(c.tableCellFormatMethod||c.cellFormatMethod)||pl("vxe.error.notFormats",[s[0]])),Object.assign(this,{type:t.type,property:t.field,field:t.field,title:t.title,width:t.width,minWidth:t.minWidth,maxWidth:t.maxWidth,resizable:t.resizable,fixed:t.fixed,align:t.align,headerAlign:t.headerAlign,footerAlign:t.footerAlign,showOverflow:t.showOverflow,showHeaderOverflow:t.showHeaderOverflow,showFooterOverflow:t.showFooterOverflow,className:t.className,headerClassName:t.headerClassName,footerClassName:t.footerClassName,formatter:s,footerFormatter:t.footerFormatter,padding:t.padding,verticalAlign:t.verticalAlign,sortable:t.sortable,sortBy:t.sortBy,sortType:t.sortType,filters:so(t.filters),filterMultiple:!Il().isBoolean(t.filterMultiple)||t.filterMultiple,filterMethod:t.filterMethod,filterResetMethod:t.filterResetMethod,filterRecoverMethod:t.filterRecoverMethod,filterRender:t.filterRender,rowGroupNode:t.rowGroupNode,treeNode:t.treeNode,dragSort:t.dragSort,rowResize:t.rowResize,cellType:t.cellType,cellRender:t.cellRender,editRender:t.editRender,contentRender:t.contentRender,headerExportMethod:t.headerExportMethod,exportMethod:t.exportMethod,footerExportMethod:t.footerExportMethod,titleHelp:t.titleHelp,titlePrefix:t.titlePrefix,titleSuffix:t.titleSuffix,aggFunc:t.aggFunc,params:t.params,id:t.colId||Il().uniqueId("col_"),parentId:null,visible:d,halfVisible:!1,defaultVisible:d,defaultFixed:t.fixed,checked:!1,halfChecked:!1,disabled:!1,level:1,rowSpan:1,colSpan:1,order:null,sortTime:0,sortNumber:0,renderSortNumber:0,renderAggFn:"",renderFixed:"",renderVisible:!1,renderWidth:0,renderHeight:0,renderResizeWidth:0,renderAutoWidth:0,resizeWidth:0,renderLeft:0,renderArgs:[],model:{},renderHeader:r||t.renderHeader,renderCell:l||t.renderCell,renderFooter:o||t.renderFooter,renderData:a,slots:t.slots}),i&&(e=i.getComputeMaps().computeProxyOpts,(n=e.value).beforeColumn)&&n.beforeColumn({$grid:i,column:this})}getTitle(){return Ml(this.title||("seq"===this.type?Q("vxe.table.seqTitle"):""))}getKey(){var e=this.type;return this.field||(e?"type="+e:null)}update(e,t){"filters"!==e&&("field"===e&&(this.property=t),this[e]=t)}}let ee={},t;function Yl(){return t||((t=new Image).src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="),t}function Zl(){return t||Yl()}function Et(e,t){return e?Il().isFunction(e)?e(t):e:""}function vr(e){return ee[e]||(ee[e]=new RegExp(`(?:^|\\s)${e}(?!\\S)`,"g")),ee[e]}function Ql(e){return e&&/^\d+(px)?$/.test(e)}function Jl(e){return e&&/^\d+%$/.test(e)}function Ll(e,t){return!!(e&&e.className&&e.className.match&&e.className.match(vr(t)))}function Vl(e,t){e&&Ll(e,t)&&(e.className=e.className.replace(vr(t),""))}function $l(e,t){e&&!Ll(e,t)&&(Vl(e,t),e.className=e.className+" "+t)}function eo(e){return e.ctrlKey||e.metaKey}function to(e,t="px"){return Il().isNumber(e)||/^\d+$/.test(""+e)?""+e+t:""+(e||"")}function Rt(e,t){return e?e.querySelector(t):null}function fr(){var e=document.documentElement,t=document.body;return{scrollTop:e.scrollTop||t.scrollTop,scrollLeft:e.scrollLeft||t.scrollLeft,visibleHeight:e.clientHeight||t.clientHeight,visibleWidth:e.clientWidth||t.clientWidth}}function xr(e){return e?e.offsetHeight:0}function ro(e){return e?(e=getComputedStyle(e),Il().toNumber(e.paddingTop)+Il().toNumber(e.paddingBottom)):0}function _l(e,t){e&&(e.scrollTop=t)}function Hl(e,t){e&&(e.scrollLeft=t)}function br(e,t){t="html"===t.type?e.innerText:e.textContent;e.getAttribute("title")!==t&&e.setAttribute("title",t)}function Bl(e,t,r,l){let o,a=e.target.shadowRoot&&e.composed&&e.composedPath()[0]||e.target;for(;a&&a.nodeType&&a!==document;){if(r&&Ll(a,r)&&(!l||l(a)))o=a;else if(a===t)return{flag:!r||!!o,container:t,targetElem:o};a=a.parentNode}return{flag:!1}}function Cr(e){var e=e.getBoundingClientRect(),t=e.top,e=e.left,{scrollTop:r,scrollLeft:l,visibleHeight:o,visibleWidth:a}=fr();return{boundingTop:t,top:r+t,boundingLeft:e,left:l+e,visibleHeight:o,visibleWidth:a}}let te="scrollIntoViewIfNeeded",re="scrollIntoView",le=(e,t)=>{let r=[];return e.forEach(e=>{e.parentId=t?t.id:null,e.visible&&(e.children&&e.children.length&&e.children.some(e=>e.visible)?(r.push(e),r.push(...le(e.children,e))):r.push(e))}),r};function lo(e,t,r){var l=e.internalData;return t||r?(l.intoRunScroll=!1,l.inVirtualScroll=!1,l.inWheelScroll=!1,l.inHeaderScroll=!1,l.inBodyScroll=!1,l.inFooterScroll=!1,l.scrollRenderType="",e.scrollTo(t,r)):e.clearScroll()}function oo(){return Il().uniqueId("row_")}function ao(e){e=e.internalData.currKeyField;return e}function Pl(e,t){var{isCurrDeepKey:e,currKeyField:r}=e.internalData;return t?wr((e?yr:Er)(t,r)):""}function no(e){let{isCurrDeepKey:t,currKeyField:r}=e.internalData,l=t?Tr:Rr;return{rowKey:r,handleUpdateRowId(e){return e?l(e,r):""}}}function Nl(e){let{isCurrDeepKey:t,currKeyField:r}=e.internalData,l=t?yr:Er;return{rowKey:r,handleGetRowId(e){return e?wr(l(e,r)):""}}}function wr(e){return Il().eqNull(e)?"":encodeURIComponent(e)}function yr(e,t){return Il().get(e,t)}function Tr(e,t){let r=wr(yr(e,t));return Ol(r)&&(r=oo(),Il().set(e,t,r)),r}function Er(e,t){return e[t]}function Rr(e,t){let r=wr(e[t]);return Ol(r)&&(r=oo(),e[t]=r),r}function zl(e,t){return t?Il().isString(t)||Il().isNumber(t)?e.getColumnByField(""+t):t:null}function io(e,t){return t?(t=Il().isString(t)||Il().isNumber(t)?t:Pl(e,t),e.getRowById(t)):null}function Sr(e,t,r,l){return e.resizeHeight||t.height||r.height||e.height||l}function Dr(e){return e?(e=getComputedStyle(e),Il().toNumber(e.paddingLeft)+Il().toNumber(e.paddingRight)):0}function St(e){var t,r;return e?(r=getComputedStyle(e),t=Il().toNumber(r.marginLeft),r=Il().toNumber(r.marginRight),e.offsetWidth+t+r):0}function so(e){return e&&Il().isArray(e)?e.map(({label:e,value:t,data:r,resetValue:l,checked:o})=>({label:e,value:t,data:r,resetValue:l,checked:!!o,_checked:!!o})):e}function co(e){return e.map((e,t)=>t%2==0?Number(e)+1:".").join("")}function jl(e,t){return Il().get(e,t.field)}function Ul(e,t,r){Il().set(e,t.field,r)}function Wl(e){if(e){e=e.value;if(e)return e.$el||e}return null}function uo(e){return"unset"!==e&&e||0}function po(e){var{$table:t,column:r,cell:l}=e,o=t.props,a=t.internalData,t=t.getComputeMaps().computeResizableOpts,t=t.value.minWidth;if(t){e=Il().isFunction(t)?t(e):t;if("auto"!==e)return Math.max(1,Il().toNumber(e))}var n,i,s,d,t=a.elemStore,e=o.showHeaderOverflow,{showHeaderOverflow:a,minWidth:o}=r,r=Il().isUndefined(a)||Il().isNull(a)?e:a,e="title"===r||(!0===r||"tooltip"===r)||"ellipsis"===r;let c=Il().floor(1.8*(Il().toNumber(getComputedStyle(l).fontSize)||14))+(Dr(l)+Dr(Rt(l,".vxe-cell")));if(e&&(a=St(Rt(l,".vxe-cell--drag-handle")),r=St(Rt(l,".vxe-cell--checkbox")),e=St(Rt(l,".vxe-cell--required-icon")),n=St(Rt(l,".vxe-cell--edit-icon")),i=St(Rt(l,".vxe-cell-title-prefix-icon")),s=St(Rt(l,".vxe-cell-title-suffix-icon")),d=St(Rt(l,".vxe-cell--sort")),l=St(Rt(l,".vxe-cell--filter")),c+=a+r+e+n+i+s+l+d),o){a=Wl(t["main-body-scroll"]);if(a){if(Jl(o))return r=(a.clientWidth-1)/100,Math.max(c,Math.floor(Il().toInteger(o)*r));if(Ql(o))return Math.max(c,Il().toInteger(o))}}return c}function Ir(e){return e&&(e.constructor===Qo||e instanceof Qo)}function Fr(r,e,l){Object.keys(e).forEach(t=>{(0,Al.watch)(()=>e[t],e=>{l.update(t,e),r&&("filters"===t?(r.setFilter(l,e),r.handleUpdateDataQueue()):["visible","fixed","width","minWidth","maxWidth"].includes(t)&&r.handleRefreshColumnQueue())})})}function Mr(e,t,r,l){var e=e.reactData,o=e.staticColumns,a=t.parentNode,l=l?l.columnConfig:null,l=l?l.children:o;a&&l&&(l.splice(Il().arrayIndexOf(a.children,t),0,r),e.staticColumns=o.slice(0))}function kr(e,t){var e=e.reactData,r=e.staticColumns,l=Il().findTree(r,e=>e.id===t.id,{children:"children"});l&&l.items.splice(l.index,1),e.staticColumns=r.slice(0)}function ho(e,t){var e=e.internalData,r=e.fullColumnIdData;if(!t)return null;let l=t.parentId;for(;r[l];){let e=r[l].column;if(!(l=e.parentId))return e}return t}let oe={mini:3,small:2,medium:1,large:0},se=(e,t)=>{let r=1;if(e){var l=t.$table,o=l.getComputeMaps().computeTreeOpts,o=o.value,{transform:a,mapChildrenField:n}=o,o=o.children||o.childrenField,i=e[a?n:o];if(i&&l.isTreeExpandByRow(e))for(let e=0;e<i.length;e++)r+=se(i[e],t)}return r},pe=e=>{e=e.getComputeMaps().computeSize,e=e.value;return e&&oe[e]||0};function go(r,l){var e=r.props,t=r.reactData,o=r.internalData,{computeLeftFixedWidth:a,computeRightFixedWidth:n,computeRowOpts:i,computeCellOpts:s,computeDefaultRowHeight:d}=r.getComputeMaps(),e=e.showOverflow,{scrollYLoad:t,scrollYTop:c}=t,{elemStore:o,afterFullData:u,fullAllDataRowIdData:p,isResizeCellHeight:h}=o,g=i.value,m=s.value,v=d.value,i=a.value,s=n.value,d=Wl(o["main-body-scroll"]),f=Pl(r,l);if(d){a=d.clientHeight,n=d.scrollTop,o=d.querySelector(`[rowid="${f}"]`);if(o){d=o.offsetTop+(t?c:0),c=o.clientHeight;if(d<n||n+a<d)return r.scrollTo(null,d);if(a+n<=d+c)return r.scrollTo(null,n+c)}else if(t){if(!(h||m.height||g.height)&&e)return r.scrollTo(null,(r.findRowIndexOf(u,l)-1)*v);let t=0;o=p[f]||{},d=o.resizeHeight||m.height||g.height||o.height||v;for(let e=0;e<u.length;e++){var x=u[e],b=Pl(r,x);if(x===l||b===f)break;x=p[b]||{};t+=x.resizeHeight||m.height||g.height||x.height||v}return t<n?r.scrollTo(null,t-i-1):r.scrollTo(null,t+d-(a-s-1))}}return Promise.resolve()}function mo(r,l,t){var o=r.reactData,a=r.internalData,{computeLeftFixedWidth:n,computeRightFixedWidth:i}=r.getComputeMaps(),{scrollXLoad:o,scrollXLeft:s}=o,{elemStore:a,visibleColumn:d}=a,n=n.value,i=i.value,a=Wl(a["main-body-scroll"]);if(!l.fixed&&a){var c=a.clientWidth,u=a.scrollLeft;let e=null;if(t&&(t=Pl(r,t),e=a.querySelector(`[rowid="${t}"] .`+l.id)),e=e||a.querySelector("."+l.id)){t=e.offsetLeft+(o?s:0),a=e.clientWidth;if(t<u+n)return r.scrollTo(t-n-1);if(c-i<t+a-u)return r.scrollTo(t+a-(c-i-1))}else if(o){let t=0;s=l.renderWidth;for(let e=0;e<d.length;e++){var p=d[e];if(p===l||p.id===l.id)break;t+=p.renderWidth}return t<u?r.scrollTo(t-n-1):r.scrollTo(t+s-(c-i-1))}}return Promise.resolve()}function Dt(e){return"on"+e.substring(0,1).toLocaleUpperCase()+e.substring(1)}function Or(e){switch(e.name){case"input":case"textarea":return"input";case"select":return"change"}return"update:modelValue"}function Ar(e){switch(e.name){case"input":case"textarea":case"VxeInput":case"VxeNumberInput":case"VxeTextarea":case"$input":case"$textarea":return"input"}return"change"}function Gl(e){return null==e?[]:Il().isArray(e)?e:[e]}let{getI18n:T,getIcon:S,renderer:E,formats:ae,renderEmptyElement:b}=Dl.VxeUI;function Lr(t){let{$table:r,column:e}=t;var l=r.context,l=l.slots,o=e.slots,a=r.props.dragConfig,n=r.getComputeMaps().computeRowDragOpts,{icon:n,trigger:i,disabledMethod:s}=n.value,s=s||(a?a.rowDisabledMethod:null);let d=s&&s(t);s=(o?o.rowDragIcon||o["row-drag-icon"]:null)||l.rowDragIcon||l["row-drag-icon"],o={};return"cell"!==i&&(o.onMousedown=e=>{d||r.handleCellDragMousedownEvent(e,t)},o.onMouseup=r.handleCellDragMouseupEvent),(0,Al.h)("span",{key:"dg",class:["vxe-cell--drag-handle",{"is--disabled":d}],...o},s?r.callSlot(s,t):[(0,Al.h)("i",{class:n||(a?a.rowIcon:"")||S().TABLE_DRAG_ROW})])}function It(e,t){var{$table:r,column:l,level:o}=e,l=l.dragSort,{treeConfig:a,dragConfig:n}=r.props,{computeRowOpts:r,computeRowDragOpts:i,computeTreeOpts:s}=r.getComputeMaps(),r=r.value,i=i.value,s=s.value,{showIcon:i,isPeerDrag:d,isCrossDrag:c,visibleMethod:u}=i,u=u||(n?n.rowVisibleMethod:null),p=[];return l&&r.drag&&(i||n&&n.showRowIcon)&&(!u||u(e))&&(!a||s.transform&&(d||c||!o))&&p.push(Lr(e)),p.concat(Il().isArray(t)?t:[t])}function Ft(e,t){return[(t=>{let{$table:r,column:e}=t,l=e.titlePrefix||e.titleHelp;return l?(0,Al.h)("span",{class:["vxe-cell-title-prefix-icon",l.iconStatus?"theme--"+l.iconStatus:""],onMouseenter(e){r.triggerHeaderTitleEvent(e,l,t)},onMouseleave(e){r.handleTargetLeaveEvent(e)}},[(0,Al.h)("i",{class:l.icon||S().TABLE_TITLE_PREFIX})]):b(r)})(e),(r=>{let{$table:l,column:e}=r;var o=(o=l.context).slots,a=e.slots,{computeColumnOpts:n,computeColumnDragOpts:i}=l.getComputeMaps(),n=n.value,{showIcon:i,icon:s,trigger:d,isPeerDrag:t,isCrossDrag:c,visibleMethod:u,disabledMethod:p}=i.value;if(!n.drag||!i||u&&!u(r)||e.fixed||!t&&!c&&e.parentId)return b(l);{let t=p&&p(r);n=(a?a.columnDragIcon||a["column-drag-icon"]:null)||o.columnDragIcon||o["column-drag-icon"],i={};return"cell"!==d&&(i.onMousedown=e=>{t||l.handleHeaderCellDragMousedownEvent(e,r)},i.onMouseup=l.handleHeaderCellDragMouseupEvent),(0,Al.h)("span",{key:"dg",class:["vxe-cell--drag-handle",{"is--disabled":t}],...i},n?l.callSlot(n,r):[(0,Al.h)("i",{class:s||S().TABLE_DRAG_COLUMN})])}})(e),...Il().isArray(t)?t:[t],(t=>{let{$table:r,column:e}=t,l=e.titleSuffix;return l?(0,Al.h)("span",{class:["vxe-cell-title-suffix-icon",l.iconStatus?"theme--"+l.iconStatus:""],onMouseenter(e){r.triggerHeaderTitleEvent(e,l,t)},onMouseleave(e){r.handleTargetLeaveEvent(e)}},[(0,Al.h)("i",{class:l.icon||S().TABLE_TITLE_SUFFIX})]):b(r)})(e)]}function Vr(t,e){let{$table:r,column:l}=t;var o=r.props;let a=r.reactData;var n=r.getComputeMaps().computeTooltipOpts,o=o.showHeaderOverflow,{type:i,showHeaderOverflow:s}=l;let d=n.value.showAll;n=Il().isUndefined(s)||Il().isNull(s)?o:s;let c="title"===n,u=!0===n||"tooltip"===n;o={};return(c||u||d)&&(o.onMouseenter=e=>{a.isDragResize||(c?br(e.currentTarget,l):(u||d)&&r.triggerHeaderTooltipEvent(e,t))}),(u||d)&&(o.onMouseleave=e=>{a.isDragResize||(u||d)&&r.handleTargetLeaveEvent(e)}),["html"===i&&Il().isString(e)?(0,Al.h)("span",{class:"vxe-cell--title",innerHTML:e,...o}):(0,Al.h)("span",{class:"vxe-cell--title",...o},Gl(e))]}function $r(e){var{$table:e,row:t,column:r}=e;return kl(e.getCellLabel(t,r),1)}function _r(e){var{column:t,row:r,$table:l}=e,o=l.props,a=l.reactData.isRowGroupStatus,o=o.editConfig,{type:n,treeNode:i,rowGroupNode:s,editRender:d}=t,{computeEditOpts:l,computeCheckboxOpts:c,computeAggregateOpts:u}=l.getComputeMaps(),u=u.value.mode,p=c.value,c=l.value,h=i||a&&("column"===u?t.field===r.groupField:s);switch(n){case"seq":return h?g.renderDeepIndexCell(e):g.renderSeqCell(e);case"radio":return h?g.renderDeepRadioCell(e):g.renderRadioCell(e);case"checkbox":return p.checkField?h?g.renderDeepSelectionCellByProp(e):g.renderCheckboxCellByProp(e):h?g.renderDeepSelectionCell(e):g.renderCheckboxCell(e);case"expand":return g.renderExpandCell(e);case"html":return h?g.renderDeepHTMLCell(e):g.renderHTMLCell(e)}return Fl(o)&&d?"cell"===c.mode?h?g.renderDeepCellEdit(e):g.renderCellEdit(e):h?g.renderDeepRowEdit(e):g.renderRowEdit(e):h?g.renderDeepCell(e):g.renderDefaultCell(e)}function Hr(e){var{column:t,$table:r}=e,r=r.props.editConfig,{type:t,filters:l,sortable:o,editRender:a}=t;switch(t){case"seq":return g.renderSeqHeader(e);case"radio":return g.renderRadioHeader(e);case"checkbox":return g.renderCheckboxHeader(e);case"html":if(l&&o)return g.renderSortAndFilterHeader(e);if(o)return g.renderSortHeader(e);if(l)return g.renderFilterHeader(e)}return r&&a?g.renderEditHeader(e):l&&o?g.renderSortAndFilterHeader(e):o?g.renderSortHeader(e):l?g.renderFilterHeader(e):g.renderDefaultHeader(e)}function Br(e){return g.renderDefaultFooter(e)}let g={createColumn(e,t){var r=t.type,l={renderHeader:Hr,renderCell:_r,renderFooter:Br};return"expand"===r&&(l.renderData=g.renderExpandData),r=e,e=l,Ir(l=t)?l:(0,Al.reactive)(new Qo(r,l,e))},renderHeaderTitle(e){var{$table:t,column:r}=e,{slots:l,editRender:o,cellRender:a}=r,o=o||a,a=l?l.header:null;if(a)return Vr(e,t.callSlot(a,e));if(o){l=E.get(o.name);if(l){t=l.renderTableHeader||l.renderHeader;if(t)return Vr(e,Gl(t(o,e)))}}return Vr(e,kl(r.getTitle(),1))},renderDefaultHeader(e){return Ft(e,g.renderHeaderTitle(e))},renderDefaultCell(e){var{$table:t,row:r,column:l}=e,o=t.reactData.isRowGroupStatus,{field:a,slots:n,editRender:i,cellRender:s,rowGroupNode:d,aggFunc:c}=l,s=i||s,n=n?n.default:null;let u="";if(o&&a&&r.isAggregate){var o=r,p=t.internalData.fullColumnFieldData,h=t.getComputeMaps().computeAggregateOpts,h=h.value,{mode:g,showTotal:m,totalMethod:v,countFields:f,contentMethod:x,mapChildrenField:b}=h,h=h.aggregateMethod||h.countMethod,C=o.groupField,w=o.groupContent,b=b&&o[b]||[],y=o.childCount,p=p[C]||{},C={$table:t,groupField:C,groupColumn:p?p.column:null,column:l,groupValue:w,children:b,childCount:y,aggValue:null,totalValue:y};("column"===g?a===o.groupField:d)?(u=w,x&&(u=""+x(C)),m&&(u=T("vxe.table.rowGroupContentTotal",[u,v?v(C):y,y]))):t.getPivotTableAggregateCellAggValue?u=t.getPivotTableAggregateCellAggValue(e):(!0===c||f&&f.includes(a))&&h&&(C.aggValue=y,u=""+h(C))}else{if(n)return It(e,t.callSlot(n,e));if(s){p=E.get(s.name);if(p){b=p.renderTableCell||p.renderCell,g=p.renderTableDefault||p.renderDefault,o=i?b:g;if(o)return It(e,Gl(o(s,Object.assign({$type:i?"edit":"cell"},e))))}}u=t.getCellLabel(r,l)}d=i?i.placeholder:"";return It(e,[(0,Al.h)("span",{class:"vxe-cell--label"},[i&&Ol(u)?(0,Al.h)("span",{class:"vxe-cell--placeholder"},kl(Ml(d),1)):(0,Al.h)("span",kl(u,1))])])},renderDeepCell(e){return g.renderDeepNodeBtn(e,g.renderDefaultCell(e))},renderDefaultFooter(t){{var{$table:r,column:l,_columnIndex:o,items:a,row:n}=t,{slots:i,editRender:s,cellRender:d,footerFormatter:c}=l,s=s||d;if(d=i?i.footer:null)return r.callSlot(d,t);let e="";if(e=Il().isArray(a)?a[o]:Il().get(n,l.field),i=Object.assign(t,{itemValue:e}),c)return Il().isFunction(c)?[(0,Al.h)("span",{class:"vxe-cell--label"},""+c(i))]:(a=(d=(r=Il().isArray(c))?ae.get(c[0]):ae.get(c))?d.tableFooterCellFormatMethod:null)?[(0,Al.h)("span",{class:"vxe-cell--label"},""+(r?a(i,...c.slice(1)):a(i)))]:[(0,Al.h)("span",{class:"vxe-cell--label"},"")];if(s){o=E.get(s.name);if(o){n=o.renderTableFooter||o.renderFooter;if(n)return Gl(n(s,i))}}return[(0,Al.h)("span",{class:"vxe-cell--label"},kl(e,1))]}},renderRowGroupBtn(t,e){let r=t.$table;var l=r.reactData,o=r.internalData,{row:a,level:n}=t,i=r.getComputeMaps().computeAggregateOpts,l=l.rowGroupExpandedFlag,o=o.rowGroupExpandedMaps,{padding:i,indent:s}=i.value,d=Pl(r,a),l=!!l&&!!o[d];return(0,Al.h)("div",{class:["vxe-row-group--tree-node",{"is--expanded":l}],style:i&&s?{paddingLeft:n*s+"px"}:void 0},[a.isAggregate?(0,Al.h)("span",{class:"vxe-row-group--node-btn",onClick(e){r.triggerRowGroupExpandEvent(e,t)}},[(0,Al.h)("i",{class:l?S().TABLE_ROW_GROUP_OPEN:S().TABLE_ROW_GROUP_CLOSE})]):b(r),(0,Al.h)("div",{class:"vxe-row-group-cell"},e)])},renderTreeNodeBtn(t,e){let{$table:r,isHidden:l}=t;var o=r.reactData,a=r.internalData,{row:n,column:i,level:s}=t,i=i.slots,i=i?i.icon:null;if(i)return r.callSlot(i,t);var i=r.getComputeMaps().computeTreeOpts,o=o.treeExpandedFlag,{fullAllDataRowIdData:a,treeExpandedMaps:d,treeExpandLazyLoadedMaps:c}=a,i=i.value,{padding:u,indent:p,lazy:h,trigger:g,iconLoaded:m,showIcon:v,iconOpen:f,iconClose:x}=i,b=i.children||i.childrenField,i=i.hasChild||i.hasChildField,b=n[b],b=b&&b.length;let C=!1,w=!1,y=!1,T=!1;var E,R={};return l||(E=Pl(r,n),w=!!o&&!!d[E],h&&(o=a[E],y=!!c[E],C=n[i],T=!!o.treeLoaded)),g&&"default"!==g||(R.onClick=e=>{r.triggerTreeExpandEvent(e,t)}),(0,Al.h)("div",{class:["vxe-cell--tree-node",{"is--active":w}],style:u&&p?{paddingLeft:s*p+"px"}:void 0},[v&&(!h||T?b:b||C)?[(0,Al.h)("div",{class:"vxe-cell--tree-btn",...R},[(0,Al.h)("i",{class:y?m||S().TABLE_TREE_LOADED:w?f||S().TABLE_TREE_OPEN:x||S().TABLE_TREE_CLOSE})])]:null,(0,Al.h)("div",{class:"vxe-tree-cell"},e)])},renderDeepNodeBtn(e,t){var{$table:r,row:l,column:o}=e,a=o.rowGroupNode,n=r.reactData.rowGroupList;if(n.length){n=r.getComputeMaps().computeAggregateOpts,r=n.value.mode;if("column"===r?o.field===l.groupField:a)return[g.renderRowGroupBtn(e,t)]}return[g.renderTreeNodeBtn(e,t)]},renderSeqHeader(e){var{$table:t,column:r}=e,l=r.slots,l=l?l.header:null;return Ft(e,Vr(e,l?t.callSlot(l,e):kl(r.getTitle(),1)))},renderSeqCell(e){var{$table:t,column:r}=e,l=t.props.treeConfig,o=t.getComputeMaps().computeSeqOpts,o=o.value,r=r.slots,r=r?r.default:null;return r?It(e,t.callSlot(r,e)):(t=e.seq,r=o.seqMethod,It(e,[(0,Al.h)("span",""+kl(r?r(e):l?t:(o.startIndex||0)+t,1))]))},renderDeepIndexCell(e){return g.renderDeepNodeBtn(e,g.renderSeqCell(e))},renderRadioHeader(e){var{$table:t,column:r}=e,l=r.slots,o=l?l.header:null,l=l?l.title:null;return Ft(e,Vr(e,o?t.callSlot(o,e):[(0,Al.h)("span",{class:"vxe-radio--label"},l?t.callSlot(l,e):kl(r.getTitle(),1))]))},renderRadioCell(t){let{$table:r,column:e,isHidden:l}=t;var o=r.reactData,a=r.getComputeMaps().computeRadioOpts,o=o.selectRadioRow,n=e.slots,{labelField:a,checkMethod:i,visibleMethod:s}=a.value,d=t.row,c=n?n.default:null,n=n?n.radio:null,o=r.eqRow(d,o);let u=!s||s({$table:r,row:d}),p=!!i,h;l||(h={onClick(e){!p&&u&&r.triggerRadioRowEvent(e,t)}},i&&(p=!i({$table:r,row:d})));s={...t,checked:o,disabled:p,visible:u};return n?It(t,r.callSlot(n,s)):(i=[],u&&i.push((0,Al.h)("span",{class:["vxe-radio--icon",o?S().TABLE_RADIO_CHECKED:S().TABLE_RADIO_UNCHECKED]})),(c||a)&&i.push((0,Al.h)("span",{class:"vxe-radio--label"},c?r.callSlot(c,s):Il().get(d,a))),It(t,[(0,Al.h)("span",{class:["vxe-cell--radio",{"is--checked":o,"is--disabled":p}],...h},i)]))},renderDeepRadioCell(e){return g.renderDeepNodeBtn(e,g.renderRadioCell(e))},renderCheckboxHeader(e){let{$table:t,column:r,isHidden:l}=e;var o=t.reactData,{computeIsAllCheckboxDisabled:a,computeCheckboxOpts:n}=t.getComputeMaps();let{isAllSelected:i,isIndeterminate:s}=o,d=a.value;var o=r.slots,a=o?o.header:null,o=o?o.title:null,{checkStrictly:n,showHeader:c,headerTitle:u}=n.value,p=r.getTitle(),h={},g=(l||(h.onClick=e=>{d||t.triggerCheckAllEvent(e,!i)}),{...e,checked:i,disabled:d,indeterminate:s});return Ft(e,Vr(g,a?t.callSlot(a,g):(n?c:!1!==c)?[(0,Al.h)("span",{class:["vxe-cell--checkbox",{"is--checked":i,"is--disabled":d,"is--indeterminate":s}],title:Il().eqNull(u)?T("vxe.table.allTitle"):""+(u||""),...h},[(0,Al.h)("span",{class:["vxe-checkbox--icon",s?S().TABLE_CHECKBOX_INDETERMINATE:i?S().TABLE_CHECKBOX_CHECKED:S().TABLE_CHECKBOX_UNCHECKED]})].concat(o||p?[(0,Al.h)("span",{class:"vxe-checkbox--label"},o?t.callSlot(o,g):p)]:[]))]:[(0,Al.h)("span",{class:"vxe-checkbox--label"},o?t.callSlot(o,g):p)]))},renderCheckboxCell(t){let{$table:r,row:e,column:l,isHidden:o}=t;var a=r.props.treeConfig,{updateCheckboxFlag:n,isRowGroupStatus:i}=r.reactData,{selectCheckboxMaps:s,treeIndeterminateRowMaps:d}=r.internalData,c=r.getComputeMaps().computeCheckboxOpts,{labelField:c,checkMethod:u,visibleMethod:p}=c.value,h=l.slots,g=h?h.default:null,h=h?h.checkbox:null;let m=!1,v=!1,f=!p||p({$table:r,row:e}),x=!!u;var b,p={},n=(!o&&(b=Pl(r,e),v=!!n&&!!s[b],p.onClick=e=>{!x&&f&&r.triggerCheckRowEvent(e,t,!v)},u&&(x=!u({$table:r,row:e})),a||i)&&(m=!!d[b]),{...t,checked:v,disabled:x,visible:f,indeterminate:m});return h?It(t,r.callSlot(h,n)):(s=[],f&&s.push((0,Al.h)("span",{class:["vxe-checkbox--icon",m?S().TABLE_CHECKBOX_INDETERMINATE:v?S().TABLE_CHECKBOX_CHECKED:S().TABLE_CHECKBOX_UNCHECKED]})),(g||c)&&s.push((0,Al.h)("span",{class:"vxe-checkbox--label"},g?r.callSlot(g,n):Il().get(e,c))),It(t,[(0,Al.h)("span",{class:["vxe-cell--checkbox",{"is--checked":v,"is--disabled":x,"is--indeterminate":m,"is--hidden":!f}],...p},s)]))},renderDeepSelectionCell(e){return g.renderDeepNodeBtn(e,g.renderCheckboxCell(e))},renderCheckboxCellByProp(t){let{$table:r,row:e,column:l,isHidden:o}=t;var a=r.props.treeConfig,{updateCheckboxFlag:n,isRowGroupStatus:i}=r.reactData,s=r.internalData.treeIndeterminateRowMaps,d=r.getComputeMaps().computeCheckboxOpts,d=d.value,{labelField:c,checkField:u,checkMethod:p,visibleMethod:h}=d,d=d.indeterminateField||d.halfField,g=l.slots,m=g?g.default:null,g=g?g.checkbox:null;let v=!1,f=!1,x=!h||h({$table:r,row:e}),b=!!p;var C,h={},n=(!o&&(C=Pl(r,e),f=!!n&&Il().get(e,u),h.onClick=e=>{!b&&x&&r.triggerCheckRowEvent(e,t,!f)},p&&(b=!p({$table:r,row:e})),a||i)&&(v=!!s[C]),{...t,checked:f,disabled:b,visible:x,indeterminate:v});return g?It(t,r.callSlot(g,n)):(u=[],x&&(u.push((0,Al.h)("span",{class:["vxe-checkbox--icon",v?S().TABLE_CHECKBOX_INDETERMINATE:f?S().TABLE_CHECKBOX_CHECKED:S().TABLE_CHECKBOX_UNCHECKED]})),m||c)&&u.push((0,Al.h)("span",{class:"vxe-checkbox--label"},m?r.callSlot(m,n):Il().get(e,c))),It(t,[(0,Al.h)("span",{class:["vxe-cell--checkbox",{"is--checked":f,"is--disabled":b,"is--indeterminate":d&&!f?e[d]:v,"is--hidden":!x}],...h},u)]))},renderDeepSelectionCellByProp(e){return g.renderDeepNodeBtn(e,g.renderCheckboxCellByProp(e))},renderExpandCell(t){let{$table:r,isHidden:e,row:l,column:o}=t;var a=r.reactData.isRowGroupStatus,{rowExpandedMaps:n,rowExpandLazyLoadedMaps:i}=r.internalData,s=r.getComputeMaps().computeExpandOpts,{lazy:s,labelField:d,iconLoaded:c,showIcon:u,iconOpen:p,iconClose:h,visibleMethod:g}=s.value,m=o.slots,v=m?m.default:null,m=m?m.icon:null;let f=!1,x=!1;return a&&l.isAggregate?It(t,[]):m?It(t,r.callSlot(m,t)):(e||(a=Pl(r,l),f=!!n[a],s&&(x=!!i[a])),It(t,[!u||g&&!g(t)?b(r):(0,Al.h)("span",{class:["vxe-table--expanded",{"is--active":f}],onMousedown(e){e.stopPropagation()},onClick(e){r.triggerRowExpandEvent(e,t)}},[(0,Al.h)("i",{class:["vxe-table--expand-btn",x?c||S().TABLE_EXPAND_LOADED:f?p||S().TABLE_EXPAND_OPEN:h||S().TABLE_EXPAND_CLOSE]})]),v||d?(0,Al.h)("span",{class:"vxe-table--expand-label"},v?r.callSlot(v,t):Il().get(l,d)):b(r)]))},renderExpandData(e){var{$table:t,column:r}=e,{slots:r,contentRender:l}=r,r=r?r.content:null;if(r)return t.callSlot(r,e);if(l){t=E.get(l.name);if(t){r=t.renderTableExpand||t.renderExpand;if(r)return Gl(r(l,e))}}return[]},renderHTMLCell(e){var{$table:t,column:r}=e,r=r.slots,r=r?r.default:null;return It(e,r?t.callSlot(r,e):[(0,Al.h)("span",{class:"vxe-cell--html",innerHTML:$r(e)})])},renderDeepHTMLCell(e){return g.renderDeepNodeBtn(e,g.renderHTMLCell(e))},renderSortAndFilterHeader(e){return Ft(e,g.renderHeaderTitle(e).concat(g.renderSortIcon(e).concat(g.renderFilterIcon(e))))},renderSortHeader(e){return Ft(e,g.renderHeaderTitle(e).concat(g.renderSortIcon(e)))},renderSortIcon(e){let{$table:t,column:r}=e;var l=t.getComputeMaps().computeSortOpts,{showIcon:l,allowBtn:o,ascTitle:a,descTitle:n,iconLayout:i,iconAsc:s,iconDesc:d,iconVisibleMethod:c}=l.value,u=r.order;return!l||c&&!c(e)?[]:[(0,Al.h)("span",{class:["vxe-cell--sort",`vxe-cell--sort-${i}-layout`]},[(0,Al.h)("i",{class:["vxe-sort--asc-btn",s||S().TABLE_SORT_ASC,{"sort--active":"asc"===u}],title:Il().eqNull(a)?T("vxe.table.sortAsc"):""+(a||""),onClick:o?e=>{e.stopPropagation(),t.triggerSortEvent(e,r,"asc")}:void 0}),(0,Al.h)("i",{class:["vxe-sort--desc-btn",d||S().TABLE_SORT_DESC,{"sort--active":"desc"===u}],title:Il().eqNull(n)?T("vxe.table.sortDesc"):""+(n||""),onClick:o?e=>{e.stopPropagation(),t.triggerSortEvent(e,r,"desc")}:void 0})])]},renderFilterHeader(e){return Ft(e,g.renderHeaderTitle(e).concat(g.renderFilterIcon(e)))},renderFilterIcon(t){let{$table:r,column:e,hasFilter:l}=t;var o=r.reactData.filterStore,a=r.getComputeMaps().computeFilterOpts,{showIcon:a,iconNone:n,iconMatch:i,iconVisibleMethod:s}=a.value;return!a||s&&!s(t)?[]:[(0,Al.h)("span",{class:["vxe-cell--filter",{"is--active":o.visible&&o.column===e}],onClick(e){r.triggerFilterEvent&&r.triggerFilterEvent(e,t.column,t)}},[(0,Al.h)("i",{class:["vxe-filter--btn",l?i||S().TABLE_FILTER_MATCH:n||S().TABLE_FILTER_NONE],title:T("vxe.table.filter")})])]},renderEditHeader(e){var{$table:t,column:r}=e,l=t.props,o=t.getComputeMaps().computeEditOpts,{editConfig:l,editRules:a}=l,o=o.value,{sortable:n,filters:i,editRender:s}=r;let d=!1,c=(a&&(a=Il().get(a,r.field))&&(d=a.some(e=>e.required)),[]);return Ft(e,(c=Fl(l)?[d&&o.showAsterisk?(0,Al.h)("span",{class:"vxe-cell--required-icon"},[(0,Al.h)("i")]):b(t),Fl(s)&&o.showIcon?(0,Al.h)("span",{class:"vxe-cell--edit-icon"},[(0,Al.h)("i",{class:o.icon||S().TABLE_EDIT})]):b(t)]:c).concat(g.renderHeaderTitle(e)).concat(n?g.renderSortIcon(e):[]).concat(i?g.renderFilterIcon(e):[]))},renderRowEdit(e){var{$table:t,column:r}=e,t=t.reactData.editStore,t=t.actived,r=r.editRender;return g.runRenderer(e,Fl(r)&&t&&t.row===e.row)},renderDeepRowEdit(e){return g.renderDeepNodeBtn(e,g.renderRowEdit(e))},renderCellEdit(e){var{$table:t,column:r}=e,t=t.reactData.editStore,t=t.actived,r=r.editRender;return g.runRenderer(e,Fl(r)&&t&&t.row===e.row&&t.column===e.column)},renderDeepCellEdit(e){return g.renderDeepNodeBtn(e,g.renderCellEdit(e))},runRenderer(e,t){var{$table:r,column:l}=e,{slots:l,editRender:o,formatter:a}=l,n=l?l.default:null,l=l?l.edit:null,i=E.get(o.name),i=i?i.renderTableEdit||i.renderEdit:null,s=Object.assign({$type:"",isEdit:t},e);return t?(s.$type="edit",l?r.callSlot(l,s):i?Gl(i(o,s)):[]):n?It(e,r.callSlot(n,s)):a?It(e,[(0,Al.h)("span",{class:"vxe-cell--label"},$r(s))]):g.renderDefaultCell(s)}};var vo=g,gr={colId:[String,Number],type:String,field:String,title:String,width:[Number,String],minWidth:[Number,String],maxWidth:[Number,String],resizable:{type:Boolean,default:null},fixed:String,align:String,headerAlign:String,footerAlign:String,showOverflow:{type:[Boolean,String],default:null},showHeaderOverflow:{type:[Boolean,String],default:null},showFooterOverflow:{type:[Boolean,String],default:null},className:[String,Function],headerClassName:[String,Function],footerClassName:[String,Function],formatter:[Function,Array,String],footerFormatter:[Function,Array,String],padding:{type:Boolean,default:null},verticalAlign:{type:String,default:null},sortable:Boolean,sortBy:[String,Function],sortType:String,filters:{type:Array,default:null},filterMultiple:{type:Boolean,default:!0},filterMethod:Function,filterResetMethod:Function,filterRecoverMethod:Function,filterRender:Object,rowGroupNode:Boolean,treeNode:Boolean,dragSort:Boolean,rowResize:Boolean,visible:{type:Boolean,default:null},headerExportMethod:Function,exportMethod:Function,footerExportMethod:Function,titleHelp:Object,titlePrefix:Object,titleSuffix:Object,cellType:String,cellRender:Object,editRender:Object,contentRender:Object,aggFunc:[String,Boolean],params:Object},Mt=Tt({name:"VxeColumn",props:gr,setup(e,{slots:t}){let r=(0,Al.ref)(),l=(0,Al.inject)("$xeTable",null),o=(0,Al.inject)("$xeColgroup",null);if(!l)return()=>(0,Al.createCommentVNode)();let a=vo.createColumn(l,e);a.slots=t;var t=()=>(0,Al.h)("div",{ref:r}),n={columnConfig:a,renderVN:t};return Fr(l,e,a),(0,Al.onMounted)(()=>{var e=r.value;e&&Mr(l,e,a,o)}),(0,Al.onUnmounted)(()=>{kr(l,a)}),(0,Al.provide)("$xeColumn",n),(0,Al.provide)("$xeGrid",null),t}});let r=Object.assign({},Mt,{install(e){e.component(Mt.name,Mt),e.component("VxeTableColumn",Mt)}}),ne=(Dl.VxeUI.dynamicApp&&(Dl.VxeUI.dynamicApp.component(Mt.name,Mt),Dl.VxeUI.dynamicApp.component("VxeTableColumn",Mt)),Dl.VxeUI.component(Mt),r);var Pr=Tt({name:"VxeColgroup",props:gr,setup(e,{slots:t}){let r=(0,Al.ref)(),l=(0,Al.inject)("$xeTable",null),o=(0,Al.inject)("$xeColgroup",null);if(!l)return()=>(0,Al.createCommentVNode)();let a=vo.createColumn(l,e);var n={},n=(t.header&&(n.header=t.header),a.slots=n,a.children=[],Fr(l,e,a),(0,Al.onMounted)(()=>{var e=r.value;e&&Mr(l,e,a,o)}),(0,Al.onUnmounted)(()=>{kr(l,a)}),{columnConfig:a});return(0,Al.provide)("$xeColgroup",n),(0,Al.provide)("$xeGrid",null),()=>(0,Al.h)("div",{ref:r},t.default?t.default():[])}});let l=Object.assign({},Pr,{install(e){e.component(Pr.name,Pr),e.component("VxeTableColgroup",Pr)}}),ie=(Dl.VxeUI.dynamicApp&&(Dl.VxeUI.dynamicApp.component(Pr.name,Pr),Dl.VxeUI.dynamicApp.component("VxeTableColgroup",Pr)),Dl.VxeUI.component(Pr),l),{getI18n:he,renderer:mt,renderEmptyElement:ge}=(yt(4520),yt(3215),yt(8872),yt(1795),Dl.VxeUI),gt="body";var fo=Tt({name:"VxeTableBody",props:{tableData:Array,tableColumn:Array,fixedColumn:Array,fixedType:{type:String,default:""}},setup(O){let qe=(0,Al.inject)("$xeTable",{}),{xID:A,props:Xe,context:L,reactData:Ke,internalData:Ye}=qe,{computeEditOpts:Ze,computeMouseOpts:Qe,computeCellOffsetWidth:Je,computeAreaOpts:et,computeDefaultRowHeight:tt,computeEmptyOpts:V,computeTooltipOpts:rt,computeRadioOpts:e,computeExpandOpts:ae,computeTreeOpts:p,computeCheckboxOpts:lt,computeCellOpts:ot,computeValidOpts:at,computeRowOpts:nt,computeColumnOpts:it,computeRowDragOpts:st,computeColumnDragOpts:t,computeResizableOpts:dt,computeVirtualXOpts:ct,computeVirtualYOpts:pt}=qe.getComputeMaps(),$=(0,Al.ref)(),_=(0,Al.ref)(),H=(0,Al.ref)(),B=(0,Al.ref)(),P=(0,Al.ref)(),N=(0,Al.ref)(),z=(0,Al.ref)(),j=(0,Al.ref)(),ut=()=>{var e=Xe.delayHover,{lastScrollTime:t,isDragResize:r}=Ke;return!!(r||t&&Date.now()<t+e)},ht=(e,t,r)=>{var{row:l,column:o}=t,a=Ye.afterFullData,n=Xe.treeConfig,i=p.value,{slots:o,treeNode:s}=o,d=Ye.fullAllDataRowIdData;if(o&&o.line)return qe.callSlot(o.line,t);o=d[e];let c=0,u=null;return o&&(c=o.level,u=o.items[o.treeIndex-1]),n&&s&&(i.showLine||i.line)?[(0,Al.h)("div",{key:"tl",class:"vxe-tree--line-wrapper"},[(0,Al.h)("div",{class:"vxe-tree--line",style:{height:`${qe.eqRow(a[0],l)?1:((e,t)=>{var{$table:r,row:l}=e,o=r.props.showOverflow,a=r.reactData.scrollYLoad,n=r.internalData.fullAllDataRowIdData,{computeRowOpts:i,computeCellOpts:s,computeDefaultRowHeight:d}=r.getComputeMaps(),i=i.value,s=s.value,d=d.value,l=(n=n[Pl(r,l)]).resizeHeight||s.height||i.height||n.height||d;let c=1,u=(t&&(c=se(t,e)),l);return(u=a&&!o?n.height||l:u)*c-(t?1:12-pe(r))})(t,u)}px`,bottom:`-${Math.floor(r/2)}px`,left:c*i.indent+(c?2-pe(qe):0)+16+"px"}})])]:[]},ie=($,e,t,_,H,r,B,P,l,o,N,z,a)=>{var n=qe.xeGrid,{columnKey:j,resizable:i,showOverflow:s,border:U,height:d,treeConfig:W,cellClassName:G,cellStyle:q,align:X,spanMethod:K,mouseConfig:Y,editConfig:Z,editRules:c,tooltipConfig:u,padding:p}=Xe,{tableData:h,dragRow:Q,overflowX:J,currentColumn:ee,scrollXLoad:te,scrollYLoad:g,mergeBodyFlag:re,calcCellHeightFlag:m,resizeHeightFlag:v,resizeWidthFlag:le,editStore:oe,isAllOverflow:ae,validErrorMaps:f}=Ke,{fullAllDataRowIdData:ne,fullColumnIdData:x,mergeBodyCellMaps:ie,visibleColumn:se,afterFullData:de,mergeBodyList:ce,scrollXStore:ue,scrollYStore:pe}=Ye,b=ot.value,C=at.value,he=lt.value,ge=Ze.value,w=rt.value,me=dt.value,ve=ct.value,fe=pt.value,{isAllColumnDrag:me,isAllRowDrag:xe}=me,y=nt.value,T=st.value,be=tt.value,m=m?b.height||y.height:0,{disabledMethod:E,isCrossDrag:Ce,isPeerDrag:we}=T,ye=it.value,Te=Qe.value,Ee=et.value,Re=Je.value,Ee=Ee.selectCellToRow,{type:Se,cellRender:De,editRender:Ie,align:Fe,showOverflow:Me,className:ke,treeNode:Oe,rowResize:Ae,padding:R,verticalAlign:S,slots:Le}=o,D=b.verticalAlign,oe=oe.actived,Ve=ne[e]||{},I=o.id,x=x[I]||{},F=Ie||De,F=F?mt.get(F.name):null,$e=F?F.tableCellClassName||F.cellClassName:null,_e=F?F.tableCellStyle||F.cellStyle:"";let He=w.showAll;var Be=x.index,w=x._index,x=Fl(Ie),v=v?Ve.resizeHeight:0;let M=t?o.fixed!==t:o.fixed&&J;J=Il().eqNull(R)?null===p?b.padding:p:R,p=Il().eqNull(Me)?s:Me,R="ellipsis"===p;let k="title"===p,O=!0===p||"tooltip"===p;s=ae||k||O||R,Me=Il().isBoolean(o.resizable)?o.resizable:ye.resizable||i,p=!!m,i=0<v;let Pe;m={},v=Fe||(F?F.tableCellAlign:"")||X,Fe=Il().eqNull(S)?D:S,F=f[e+":"+I],X=c&&C.showMessage&&("default"===C.message?d||1<h.length:"inline"===C.message),D={colid:I};let A={$table:qe,$grid:n,isEdit:!1,seq:$,rowid:e,row:r,rowIndex:B,$rowIndex:P,_rowIndex:l,column:o,columnIndex:Be,$columnIndex:N,_columnIndex:w,fixed:t,type:gt,isHidden:!!M,level:H,visibleData:de,data:h,items:a},L=!1,Ne=!1,V=((L=y.drag?"row"===T.trigger||o.dragSort&&"cell"===T.trigger:L)&&(Ne=!(!E||!E(A))),(k||O||He||u)&&(m.onMouseenter=e=>{ut()||(k?br(e.currentTarget,o):(O||He)&&qe.triggerBodyTooltipEvent(e,A),qe.dispatchEvent("cell-mouseenter",Object.assign({cell:e.currentTarget},A),e))}),(O||He||u)&&(m.onMouseleave=e=>{ut()||((O||He)&&qe.handleTargetLeaveEvent(e),qe.dispatchEvent("cell-mouseleave",Object.assign({cell:e.currentTarget},A),e))}),(L||he.range||Y)&&(m.onMousedown=e=>{qe.triggerCellMousedownEvent(e,A)}),L&&(m.onMouseup=qe.triggerCellMouseupEvent),m.onClick=e=>{qe.triggerCellClickEvent(e,A)},!(m.onDblclick=e=>{qe.triggerCellDblclickEvent(e,A)})),ze=1;if(re&&ce.length){S=ie[l+":"+w];if(S){var{rowspan:f,colspan:c}=S;if(!f||!c)return null;1<f&&(V=!0,ze=f,D.rowspan=f),1<c&&(V=!0,D.colspan=c)}}else if(K){var{rowspan:d=1,colspan:n=1}=K(A)||{};if(!d||!n)return null;1<d&&(V=!0,ze=d,D.rowspan=d),1<n&&(V=!0,D.colspan=n)}!(M=M&&V&&(1<D.colspan||1<D.rowspan)?!1:M)&&Z&&(Ie||De)&&(ge.showStatus||ge.showUpdateStatus)&&(Pe=qe.isUpdateByRow(r,o.field));$=g&&!s;let je=Sr(Ve,b,y,be);B=N===z.length-1,P=!o.resizeWidth&&("auto"===o.minWidth||"auto"===o.width);let Ue=!1;V||Q&&Pl(qe,Q)===e||(g&&!W&&!fe.immediate&&(l<pe.visibleStartIndex-pe.preloadSize||l>pe.visibleEndIndex+pe.preloadSize)||te&&!ve.immediate&&!o.fixed&&(w<ue.visibleStartIndex-ue.preloadSize||w>ue.visibleEndIndex+ue.preloadSize))&&(Ue=!0),1<ze&&(h=de[l+ze-1])&&(a=ne[Pl(qe,h)])&&(je+=a.oTop+Sr(a,b,y,be)-Ve.oTop-Sr(Ve,b,y,be));T={};if(s&&le){let t=D.colspan||0;if(1<t)for(let e=1;e<t;e++){var We=se[Be+e];We&&(t+=We.renderWidth)}T.width=o.renderWidth-Re*t+"px"}g||s||p||i?T.height=je+"px":T.minHeight=je+"px";E=[];M&&ae?E.push((0,Al.h)("div",{key:"tc",class:["vxe-cell",{"c--title":k,"c--tooltip":O,"c--ellipsis":R}],style:T})):(W&&E.push(...ht(e,A,je)),E.push((0,Al.h)("div",{key:"tc",class:["vxe-cell",{"c--title":k,"c--tooltip":O,"c--ellipsis":R}],style:T,title:k?qe.getCellLabel(r,o):null},Ue?[]:[(0,Al.h)("div",{colid:I,rowid:e,class:"vxe-cell--wrapper"},o.renderCell(A))])),X&&F&&(u=F.rule,he=Le?Le.valid:null,re={...A,...F,rule:F},E.push((0,Al.h)("div",{key:"tcv",class:["vxe-cell--valid-error-tip",Et(C.className,re)],style:u&&u.maxWidth?{width:u.maxWidth+"px"}:null},[(0,Al.h)("div",{class:"vxe-cell--valid-error-wrapper vxe-cell--valid-error-theme-"+(C.theme||"normal")},[he?qe.callSlot(he,re):[(0,Al.h)("span",{class:"vxe-cell--valid-error-msg"},F.content)]])]))));let Ge=!1;return Y&&Te.area&&Ee&&((w||!0!==Ee)&&Ee!==o.field||(Ge=!0)),!M&&Me&&me&&E.push((0,Al.h)("div",{key:"tcc",class:["vxe-cell--col-resizable",{"is--line":!U||"none"===U}],onMousedown:e=>qe.handleColResizeMousedownEvent(e,t,A),onDblclick:e=>qe.handleColResizeDblclickEvent(e,A)})),(Ae||xe)&&y.resizable&&E.push((0,Al.h)("div",{key:"tcr",class:"vxe-cell--row-resizable",onMousedown:e=>qe.handleRowResizeMousedownEvent(e,A),onDblclick:e=>qe.handleRowResizeDblclickEvent(e,A)})),(0,Al.h)("td",{class:["vxe-body--column",I,Fe?"col--vertical-"+Fe:"",v?"col--"+v:"",Se?"col--"+Se:"",{"col--last":B,"col--tree-node":Oe,"col--edit":x,"col--ellipsis":s,"col--cs-height":p,"col--rs-height":i,"col--to-row":Ge,"col--auto-height":$,"fixed--width":!P,"fixed--hidden":M,"is--padding":J,"is--progress":M&&ae||Ue,"is--drag-cell":L&&(Ce||we||!H),"is--drag-disabled":Ne,"col--dirty":Pe,"col--active":Z&&x&&oe.row===r&&(oe.column===o||"row"===ge.mode),"col--valid-error":!!F,"col--current":ee===o},Et($e,A),Et(ke,A),Et(G,A)],key:j||te||g||ye.useKey||y.useKey||ye.drag?I:N,...D,style:Object.assign({},Il().isFunction(_e)?_e(A):_e,Il().isFunction(q)?q(A):q),...m},_&&M?[]:E)},ne=(v,f,x,b)=>{let C=qe.xeGrid,{stripe:w,rowKey:y,highlightHoverRow:T,rowClassName:E,rowStyle:R,editConfig:S,treeConfig:D}=Xe,{hasFixedColumn:I,treeExpandedFlag:F,isColLoading:M,scrollXLoad:k,scrollYLoad:O,isAllOverflow:A,rowExpandedFlag:L,expandColumn:V,selectRadioRow:$,pendingRowFlag:_,isDragColMove:H,rowExpandHeightFlag:B,isRowGroupStatus:P}=Ke,{fullAllDataRowIdData:N,fullColumnIdData:z,treeExpandedMaps:j,pendingRowMaps:U,rowExpandedMaps:W}=Ye,Q=lt.value,J=e.value,G=p.value,q=Ze.value,X=nt.value,K=it.value,ee=t.value,{transform:Y,seqMode:te}=G,re=G.children||G.childrenField,Z=[],le=Nl(qe).handleGetRowId,oe=D||P;return x.forEach((l,o)=>{let a=le(l);var n=N[a]||{};let i=o,s=0,d=-1,c=-1;var u=P&&l.isAggregate,p={},h=((X.isHover||T)&&(p.onMouseenter=e=>{ut()||qe.triggerHoverEvent(e,{row:l,rowIndex:i})},p.onMouseleave=()=>{ut()||qe.clearHoverRow()}),n&&(s=n.level,d=u||D&&Y&&"increasing"===te?n._index+1:n.seq,i=n.index,c=n._index),{$table:qe,seq:d,rowid:a,fixed:v,type:gt,level:s,row:l,rowIndex:i,$rowIndex:o,_rowIndex:c}),g=V&&!!L&&!!W[a];let e=!1,t=[],r=!1;S&&(r=qe.isInsertByRow(l)),!D||O||Y||(t=l[re],e=!!F&&t&&0<t.length&&!!j[a]),!X.drag||P||D&&!Y||(p.onDragstart=qe.handleRowDragDragstartEvent,p.onDragend=qe.handleRowDragDragendEvent,p.onDragover=qe.handleRowDragDragoverEvent);u=["vxe-body--row",oe?"row--level-"+s:"",{"row--stripe":w&&(c+1)%2==0,"is--new":r,"is--expand-row":g,"is--expand-tree":e,"row--new":r&&(q.showStatus||q.showInsertStatus),"row--radio":J.highlight&&qe.eqRow($,l),"row--checked":Q.highlight&&qe.isCheckedByCheckboxRow(l),"row--pending":!!_&&!!U[a],"row--group":u},Et(E,h)];let m=b.map((e,t)=>ie(d,a,v,f,s,l,i,o,c,e,t,b,x));if(Z.push(!M&&K.drag&&ee.animation?(0,Al.h)(Al.TransitionGroup,{name:"vxe-header--col-list"+(H?"":"-disabled"),tag:"tr",class:u,rowid:a,style:R?Il().isFunction(R)?R(h):R:null,key:y||k||O||X.useKey||X.drag||K.drag||P||D?a:o,...p},{default:()=>m}):(0,Al.h)("tr",{class:u,rowid:a,style:R?Il().isFunction(R)?R(h):R:null,key:y||k||O||X.useKey||X.drag||K.drag||P||D?a:o,...p},m)),g){var{height:u,padding:h,mode:p}=ae.value;if("fixed"===p)Z.push((0,Al.h)("tr",{class:"vxe-body--row-expanded-place",key:"expand_"+a,rowid:a},[(0,Al.h)("td",{class:"vxe-body--row-expanded-place-column",colspan:b.length,style:{height:`${B?n.expandHeight||u:0}px`}})]));else{g={},p=(u&&(g.height=u+"px"),D&&(g.paddingLeft=s*G.indent+30+"px"),V||{}).showOverflow,n=V.id,n=z[n]||{},p=Il().eqNull(p)?A:p;let e=-1,t=-1,r=-1;n&&(e=n.index,t=n.$index,r=n._index);n={$grid:C,$table:qe,seq:d,column:V,columnIndex:e,$columnIndex:t,_columnIndex:r,fixed:v,type:gt,level:s,row:l,rowid:a,rowIndex:i,$rowIndex:o,_rowIndex:c,isHidden:!1,isEdit:!1,visibleData:[],data:[],items:[]};Z.push((0,Al.h)("tr",{class:["vxe-body--expanded-row",{"is--padding":h}],key:"expand_"+a},[(0,Al.h)("td",{class:["vxe-body--expanded-column",{"fixed--hidden":v&&!I,"col--ellipsis":p}],colspan:b.length},[(0,Al.h)("div",{class:["vxe-body--expanded-cell",{"is--ellipsis":u}],style:g},[V.renderData(n)])])]))}}e&&Z.push(...ne(v,f,t,b))}),Z};(0,Al.onMounted)(()=>{(0,Al.nextTick)(()=>{var e=O.fixedType,t=Ye.elemStore,e=`${e||"main"}-body-`;t[e+"wrapper"]=$,t[e+"scroll"]=_,t[e+"table"]=H,t[e+"colgroup"]=B,t[e+"list"]=P,t[e+"xSpace"]=N,t[e+"ySpace"]=z,t[e+"emptyBlock"]=j})}),(0,Al.onUnmounted)(()=>{var e=O.fixedType,t=Ye.elemStore,e=`${e||"main"}-body-`;t[e+"wrapper"]=null,t[e+"scroll"]=null,t[e+"table"]=null,t[e+"colgroup"]=null,t[e+"list"]=null,t[e+"xSpace"]=null,t[e+"ySpace"]=null,t[e+"emptyBlock"]=null});return()=>{var e=L.slots,t=qe.xeGrid;let{fixedColumn:r,fixedType:l,tableColumn:o}=O;var{spanMethod:a,footerSpanMethod:n,mouseConfig:i}=Xe,{isGroup:s,tableData:d,isRowLoading:c,isColLoading:u,overflowX:p,scrollXLoad:h,scrollYLoad:g,isAllOverflow:m,isDragRowMove:v,expandColumn:f,dragRow:x,dragCol:b}=Ke,{visibleColumn:C,fullAllDataRowIdData:w,fullColumnIdData:y}=Ye,T=nt.value,E=V.value,R=Qe.value,S=st.value,D=ae.value;let I=d,F=o,M=!1;!(h||g||m)||f&&"fixed"!==D.mode||a||n||(M=!0),u||!l&&p||(F=C),l&&M&&(F=r||[]),g&&x&&2<I.length&&(d=w[Pl(qe,x)])&&(m=d._index,f=I[0],D=I[I.length-1],a=w[Pl(qe,f)],n=w[Pl(qe,D)],a)&&n&&(p=a._index,C=n._index,m<p?I=[x].concat(I):C<m&&(I=I.concat([x]))),l||s||h&&b&&2<F.length&&(g=y[b.id])&&(d=g._index,f=F[0],w=F[F.length-1],D=y[f.id],a=y[w.id],D)&&a&&(n=D._index,p=a._index,d<n?F=[b].concat(F):p<d&&(F=F.concat([b])));let k;C=e?e.empty:null,m={$table:qe,$grid:t},k=C?qe.callSlot(C,m):(s=(x=E.name?mt.get(E.name):null)?x.renderTableEmpty||x.renderTableEmptyView||x.renderEmpty:null)?Gl(s(E,m)):Xe.emptyText||he("vxe.table.emptyText"),h={onScroll(e){qe.triggerBodyScrollEvent(e,l)}};return(0,Al.h)("div",{ref:$,class:["vxe-table--body-wrapper",l?`fixed-${l}--wrapper`:"body--wrapper"],xid:A},[(0,Al.h)("div",{ref:_,class:"vxe-table--body-inner-wrapper",...h},[l?ge(qe):(0,Al.h)("div",{ref:N,class:"vxe-body--x-space"}),(0,Al.h)("div",{ref:z,class:"vxe-body--y-space"}),(0,Al.h)("table",{ref:H,class:"vxe-table--body",xid:A,cellspacing:0,cellpadding:0,border:0,xvm:M?"1":null},[(0,Al.h)("colgroup",{ref:B},F.map((e,t)=>(0,Al.h)("col",{name:e.id,key:t,style:{width:e.renderWidth+"px"}}))),!c&&!u&&T.drag&&S.animation?(0,Al.h)(Al.TransitionGroup,{ref:P,name:"vxe-body--row-list"+(v?"":"-disabled"),tag:"tbody"},{default:()=>ne(l,M,I,F)}):(0,Al.h)("tbody",{ref:P},ne(l,M,I,F))]),(0,Al.h)("div",{class:"vxe-table--checkbox-range"}),i&&R.area?(0,Al.h)("div",{class:"vxe-table--cell-area"},[(0,Al.h)("span",{class:"vxe-table--cell-main-area"},R.extension?[(0,Al.h)("span",{class:"vxe-table--cell-main-area-btn",onMousedown(e){qe.triggerCellAreaExtendMousedownEvent&&qe.triggerCellAreaExtendMousedownEvent(e,{$table:qe,fixed:l,type:gt})}})]:[]),(0,Al.h)("span",{class:"vxe-table--cell-copy-area"}),(0,Al.h)("span",{class:"vxe-table--cell-extend-area"}),(0,Al.h)("span",{class:"vxe-table--cell-multi-area"}),(0,Al.h)("span",{class:"vxe-table--cell-active-area"}),(0,Al.h)("span",{class:"vxe-table--cell-row-status-area"})]):ge(qe),l?ge(qe):(0,Al.h)("div",{class:"vxe-table--empty-block",ref:j},[(0,Al.h)("div",{class:"vxe-table--empty-content"},k)])])])}}});let{renderer:xe,renderEmptyElement:be}=Dl.VxeUI;var xo=Tt({name:"VxeTableHeader",props:{tableData:Array,tableColumn:Array,tableGroupColumn:Array,fixedColumn:Array,fixedType:{type:String,default:null}},setup(x){let te=(0,Al.inject)("$xeTable",{}),{xID:b,props:C,reactData:w,internalData:y}=te,{computeColumnOpts:T,computeColumnDragOpts:E,computeCellOpts:t,computeMouseOpts:R,computeHeaderCellOpts:r,computeDefaultRowHeight:l,computeVirtualXOpts:o}=te.getComputeMaps(),S=(0,Al.ref)([]),D=(0,Al.ref)(),I=(0,Al.ref)(),F=(0,Al.ref)(),M=(0,Al.ref)(),k=(0,Al.ref)(),O=(0,Al.ref)(),a=(0,Al.ref)(),n=()=>{var e=w.isGroup;S.value=e?(e=>{let t=1,l=(r,e)=>{if(e&&(r.level=e.level+1,t<r.level)&&(t=r.level),r.children&&r.children.length&&r.children.some(e=>e.visible)){let t=0;r.children.forEach(e=>{e.visible&&(l(e,r),t+=e.colSpan)}),r.colSpan=t}else r.colSpan=1},r=(e.forEach(e=>{e.level=1,l(e)}),[]);for(let e=0;e<t;e++)r.push([]);return le(e).forEach(e=>{e.children&&e.children.length&&e.children.some(e=>e.visible)?e.rowSpan=1:e.rowSpan=t-e.level+1,r[e.level-1].push(e)}),r})(x.tableGroupColumn):[]},A=(R,S,D,I)=>{let F=te.xeGrid,M=x.fixedType,{resizable:k,columnKey:O,headerCellClassName:A,headerCellStyle:L,showHeaderOverflow:V,headerAlign:$,align:_,mouseConfig:H}=C,{currentColumn:B,dragCol:P,scrollXLoad:N,scrollYLoad:z,overflowX:j}=w,{fullColumnIdData:U,scrollXStore:W}=y,G=o.value,q=T.value,X=E.value,K=t.value;var e=l.value;let Y=r.value,Z=uo(Y.height)||e,{disabledMethod:Q,isCrossDrag:J,isPeerDrag:ee}=X;return D.map((e,t)=>{var{type:r,showHeaderOverflow:l,headerAlign:o,align:a,filters:n,headerClassName:i,editRender:s,cellRender:d}=e,c=e.id,u=U[c]||{},s=s||d,d=s?xe.get(s.name):null,s=e.children&&e.children.length,p=M?e.fixed!==M&&!s:!!e.fixed&&j,h=(Il().isBoolean(Y.padding)?Y:K).padding,l=Il().eqNull(l)?V:l,o=o||(d?d.tableHeaderCellAlign:"")||$||a||(d?d.tableCellAlign:"")||_,a="ellipsis"===l,d="title"===l,l=!0===l||"tooltip"===l,g=d||l||a;let m=!1,v=null;n&&(v=n[0],m=n.some(e=>e.checked));var f=u.index,u=u._index;let x={$table:te,$grid:F,$rowIndex:I,column:e,columnIndex:f,$columnIndex:t,_columnIndex:u,firstFilterOption:v,fixed:M,type:"header",isHidden:p,hasFilter:m};var f={colid:c,colspan:1<e.colSpan?e.colSpan:null,rowspan:1<e.rowSpan?e.rowSpan:null},b={onClick:e=>te.triggerHeaderCellClickEvent(e,x),onDblclick:e=>te.triggerHeaderCellDblclickEvent(e,x)},C=q.drag&&"cell"===X.trigger;let w=!1;C&&(w=!(!Q||!Q(x))),(H||C)&&(b.onMousedown=e=>te.triggerHeaderCellMousedownEvent(e,x)),q.drag&&(b.onDragstart=te.handleHeaderCellDragDragstartEvent,b.onDragend=te.handleHeaderCellDragDragendEvent,b.onDragover=te.handleHeaderCellDragDragoverEvent,C)&&(b.onMouseup=te.handleHeaderCellDragMouseupEvent);var C=t===D.length-1,y=Il().isBoolean(e.resizable)?e.resizable:q.resizable||k,T=!e.resizeWidth&&("auto"===e.minWidth||"auto"===e.width);let E=!1;!S||R||P&&P.id===c||N&&!e.fixed&&!G.immediate&&(u<W.visibleStartIndex-W.preloadSize||u>W.visibleEndIndex+W.preloadSize)&&(E=!0);u={};return g?u.height=Z+"px":u.minHeight=Z+"px",(0,Al.h)("th",{class:["vxe-header--column",c,{["col--"+o]:o,["col--"+r]:r,"col--last":C,"col--fixed":e.fixed,"col--group":s,"col--ellipsis":g,"fixed--width":!T,"fixed--hidden":p,"is--padding":h,"is--sortable":e.sortable,"col--filter":!!n,"is--filter-active":m,"is--drag-active":q.drag&&!e.fixed&&!w&&(J||ee||!e.parentId),"is--drag-disabled":q.drag&&w,"col--current":B===e},i?Il().isFunction(i)?i(x):i:"",A?Il().isFunction(A)?A(x):A:""],style:L?Il().isFunction(L)?L(x):L:null,...f,...b,key:O||N||z||q.useKey||q.drag||s?c:t},[(0,Al.h)("div",{class:["vxe-cell",{"c--title":d,"c--tooltip":l,"c--ellipsis":a}],style:u},E||S&&p?[]:[(0,Al.h)("div",{colid:c,class:"vxe-cell--wrapper"},e.renderHeader(x))]),!p&&y?(0,Al.h)("div",{class:"vxe-cell--col-resizable",onMousedown:e=>te.handleColResizeMousedownEvent(e,M,x),onDblclick:e=>te.handleColResizeDblclickEvent(e,x)}):be(te)])})};return(0,Al.watch)(()=>x.tableColumn,n),(0,Al.onMounted)(()=>{(0,Al.nextTick)(()=>{var e=x.fixedType,t=te.internalData,t=t.elemStore,e=`${e||"main"}-header-`;t[e+"wrapper"]=D,t[e+"scroll"]=I,t[e+"table"]=F,t[e+"colgroup"]=M,t[e+"list"]=k,t[e+"xSpace"]=O,t[e+"repair"]=a,n()})}),(0,Al.onUnmounted)(()=>{var e=x.fixedType,t=te.internalData,t=t.elemStore,e=`${e||"main"}-header-`;t[e+"wrapper"]=null,t[e+"scroll"]=null,t[e+"table"]=null,t[e+"colgroup"]=null,t[e+"list"]=null,t[e+"xSpace"]=null,t[e+"repair"]=null}),()=>{let{fixedType:t,fixedColumn:e,tableColumn:r}=x;var{mouseConfig:l,showHeaderOverflow:o,spanMethod:a,footerSpanMethod:n}=C,{isGroup:i,isColLoading:s,overflowX:d,scrollXLoad:c,dragCol:u}=w,{visibleColumn:p,fullColumnIdData:h}=y,g=R.value;let m=S.value,v=r,f=!1;return i?v=p:(c&&o&&(a||n||(f=!0)),f&&(s||!t&&d)||(v=p),t&&f&&(v=e||[]),m=[v]),t||i||c&&u&&2<v.length&&(o=h[u.id])&&(a=o._index,n=v[0],s=v[v.length-1],d=h[n.id],p=h[s.id],d)&&p&&(c=d._index,o=p._index,a<c?(v=[u].concat(v),m=[[u].concat(m[0])].concat(m.slice(1))):o<a&&(v=v.concat([u]),m=[m[0].concat([u])].concat(m.slice(1)))),(0,Al.h)("div",{ref:D,class:["vxe-table--header-wrapper",t?`fixed-${t}--wrapper`:"body--wrapper"],xid:b},[(0,Al.h)("div",{ref:I,class:"vxe-table--header-inner-wrapper",onScroll(e){te.triggerHeaderScrollEvent(e,t)}},[t?be(te):(0,Al.h)("div",{ref:O,class:"vxe-body--x-space"}),(0,Al.h)("table",{ref:F,class:"vxe-table--header",xid:b,cellspacing:0,cellpadding:0,border:0,xvm:f?"1":null},[(0,Al.h)("colgroup",{ref:M},v.map((e,t)=>(0,Al.h)("col",{name:e.id,key:t,style:{width:e.renderWidth+"px"}}))),(0,Al.h)("thead",{ref:k},((l,o,e)=>{let a=x.fixedType,{headerRowClassName:n,headerRowStyle:i}=C,{isColLoading:s,isDragColMove:d}=w,c=T.value,u=E.value;return e.map((e,t)=>{var r={$table:te,$rowIndex:t,fixed:a,type:"header"};return!s&&c.drag&&u.animation?(0,Al.h)(Al.TransitionGroup,{key:t,name:"vxe-header--col-list"+(d?"":"-disabled"),tag:"tr",class:["vxe-header--row",n?Il().isFunction(n)?n(r):n:""],style:i?Il().isFunction(i)?i(r):i:null},{default:()=>A(l,o,e,t)}):(0,Al.h)("tr",{key:t,class:["vxe-header--row",n?Il().isFunction(n)?n(r):n:""],style:i?Il().isFunction(i)?i(r):i:null},A(l,o,e,t))})})(i,f,m))]),l&&g.area?(0,Al.h)("div",{class:"vxe-table--cell-area"},[(0,Al.h)("span",{class:"vxe-table--cell-main-area"}),(0,Al.h)("span",{class:"vxe-table--cell-copy-area"}),(0,Al.h)("span",{class:"vxe-table--cell-extend-area"}),(0,Al.h)("span",{class:"vxe-table--cell-multi-area"}),(0,Al.h)("span",{class:"vxe-table--cell-active-area"}),(0,Al.h)("span",{class:"vxe-table--cell-col-status-area"})]):be(te)])])}}});let{renderer:Ce,renderEmptyElement:we}=Dl.VxeUI;var bo=Tt({name:"VxeTableFooter",props:{footerTableData:{type:Array,default:()=>[]},tableColumn:{type:Array,default:()=>[]},fixedColumn:{type:Array,default:()=>[]},fixedType:{type:String,default:null}},setup(m){let ee=(0,Al.inject)("$xeTable",{}),{xID:v,props:f,reactData:x,internalData:b}=ee,{computeTooltipOpts:t,computeColumnOpts:C,computeColumnDragOpts:w,computeCellOpts:r,computeFooterCellOpts:l,computeDefaultRowHeight:o,computeResizableOpts:a,computeVirtualXOpts:n}=ee.getComputeMaps(),y=(0,Al.ref)(),T=(0,Al.ref)(),E=(0,Al.ref)(),R=(0,Al.ref)(),S=(0,Al.ref)(),D=(0,Al.ref)(),I=(y,T,E,R,S,D)=>{let I=ee.xeGrid,F=m.fixedType,{resizable:M,border:k,footerCellClassName:O,footerCellStyle:A,footerAlign:L,footerSpanMethod:V,align:$,columnKey:_,showFooterOverflow:H}=f,{scrollXLoad:B,scrollYLoad:P,overflowX:N,currentColumn:z}=x,{fullColumnIdData:j,mergeFooterList:U,mergeFooterCellMaps:W,scrollXStore:G}=b,q=n.value,X=t.value;let K=a.value.isAllColumnDrag,Y=C.value;var e=o.value;let Z=r.value,Q=l.value,J=uo(Q.height)||e;return T.map((t,e)=>{var{type:r,showFooterOverflow:l,footerAlign:o,align:a,footerClassName:n,editRender:i,cellRender:s}=t,d=t.id,c=j[d]||{},i=i||s,s=i?Ce.get(i.name):null;let u=X.showAll;var i=t.children&&t.children.length,i=F?t.fixed!==F&&!i:t.fixed&&N,p=(Il().isBoolean(Q.padding)?Q:Z).padding,l=Il().eqNull(l)?H:l,o=o||(s?s.tableFooterCellAlign:"")||L||a||(s?s.tableCellAlign:"")||$,a="ellipsis"===l;let h="title"===l,g=!0===l||"tooltip"===l;var s=h||g||a,l=Il().isBoolean(t.resizable)?t.resizable:Y.resizable||M,m={colid:d},v={},f=c.index,c=c._index,x=c;let b={$table:ee,$grid:I,row:R,rowIndex:D,_rowIndex:D,$rowIndex:S,column:t,columnIndex:f,$columnIndex:e,_columnIndex:c,itemIndex:x,items:R,fixed:F,type:"footer",data:E},C=((h||g||u)&&(v.onMouseenter=e=>{h?br(e.currentTarget,t):(g||u)&&ee.triggerFooterTooltipEvent(e,b)}),(g||u)&&(v.onMouseleave=e=>{(g||u)&&ee.handleTargetLeaveEvent(e)}),v.onClick=e=>{ee.dispatchEvent("footer-cell-click",Object.assign({cell:e.currentTarget},b),e)},!(v.onDblclick=e=>{ee.dispatchEvent("footer-cell-dblclick",Object.assign({cell:e.currentTarget},b),e)}));if(U.length){f=W[D+":"+c];if(f){var{rowspan:x,colspan:f}=f;if(!x||!f)return null;1<x&&(C=!0,m.rowspan=x),1<f&&(C=!0,m.colspan=f)}}else if(V){var{rowspan:x=1,colspan:f=1}=V(b)||{};if(!x||!f)return null;1<x&&(m.rowspan=x),1<f&&(m.colspan=f)}x=e===T.length-1,f=!t.resizeWidth&&("auto"===t.minWidth||"auto"===t.width);let w=!1;y&&!C&&B&&!t.fixed&&!q.immediate&&(c<G.visibleStartIndex-G.preloadSize||c>G.visibleEndIndex+G.preloadSize)&&(w=!0);c={};return s?c.height=J+"px":c.minHeight=J+"px",(0,Al.h)("td",{class:["vxe-footer--column",t.id,{["col--"+o]:o,["col--"+r]:r,"col--last":x,"fixed--width":!f,"fixed--hidden":i,"is--padding":p,"col--ellipsis":s,"col--current":z===t},Et(n,b),Et(O,b)],...m,style:A?Il().isFunction(A)?A(b):A:null,...v,key:_||B||P||Y.useKey||Y.drag?t.id:e},[(0,Al.h)("div",{class:["vxe-cell",{"c--title":h,"c--tooltip":g,"c--ellipsis":a}],style:c},w?[]:[(0,Al.h)("div",{colid:d,class:"vxe-cell--wrapper"},t.renderFooter(b))]),!i&&l&&K?(0,Al.h)("div",{class:["vxe-cell--col-resizable",{"is--line":!k||"none"===k}],onMousedown:e=>ee.handleColResizeMousedownEvent(e,F,b),onDblclick:e=>ee.handleColResizeDblclickEvent(e,b)}):we(ee)])})};return(0,Al.onMounted)(()=>{(0,Al.nextTick)(()=>{var e=m.fixedType,t=b.elemStore,e=`${e||"main"}-footer-`;t[e+"wrapper"]=y,t[e+"scroll"]=T,t[e+"table"]=E,t[e+"colgroup"]=R,t[e+"list"]=S,t[e+"xSpace"]=D})}),(0,Al.onUnmounted)(()=>{var e=m.fixedType,t=b.elemStore,e=`${e||"main"}-footer-`;t[e+"wrapper"]=null,t[e+"scroll"]=null,t[e+"table"]=null,t[e+"colgroup"]=null,t[e+"list"]=null,t[e+"xSpace"]=null}),()=>{let{fixedType:t,fixedColumn:e,tableColumn:r}=m;var{spanMethod:l,footerSpanMethod:o,showFooterOverflow:a}=f,{visibleColumn:n,fullColumnIdData:i}=b,{isGroup:s,isColLoading:d,overflowX:c,scrollXLoad:u,dragCol:p}=x;let h=r,g=!1;return u&&a&&(l||o||(g=!0)),g&&(d||!t&&c)||(h=n),t&&g&&(h=e||[]),t||s||u&&p&&2<h.length&&(a=i[p.id])&&(l=a._index,o=h[0],d=h[h.length-1],c=i[o.id],n=i[d.id],c)&&n&&(s=c._index,u=n._index,l<s?h=[p].concat(h):u<l&&(h=h.concat([p]))),(0,Al.h)("div",{ref:y,class:["vxe-table--footer-wrapper",t?`fixed-${t}--wrapper`:"body--wrapper"],xid:v},[(0,Al.h)("div",{ref:T,class:"vxe-table--footer-inner-wrapper",onScroll(e){ee.triggerFooterScrollEvent(e,t)}},[t?we(ee):(0,Al.h)("div",{ref:D,class:"vxe-body--x-space"}),(0,Al.h)("table",{ref:E,class:"vxe-table--footer",xid:v,cellspacing:0,cellpadding:0,border:0,xvm:g?"1":null},[(0,Al.h)("colgroup",{ref:R},h.map((e,t)=>(0,Al.h)("col",{name:e.id,key:t,style:{width:e.renderWidth+"px"}}))),(0,Al.h)("tfoot",{ref:S},((o,a)=>{let{fixedType:n,footerTableData:i}=m,{footerRowClassName:s,footerRowStyle:d}=f,{isColLoading:c,isDragColMove:u}=x,p=C.value,h=w.value;return i.map((e,t)=>{let r=t;var l={$table:ee,row:e,_rowIndex:r,$rowIndex:t,fixed:n,type:"footer"};return!c&&p.drag&&h.animation?(0,Al.h)(Al.TransitionGroup,{key:t,name:"vxe-header--col-list"+(u?"":"-disabled"),tag:"tr",class:["vxe-footer--row",s?Il().isFunction(s)?s(l):s:""],style:d?Il().isFunction(d)?d(l):d:null},{default:()=>I(o,a,i,e,t,r)}):(0,Al.h)("tr",{key:t,class:["vxe-footer--row",s?Il().isFunction(s)?s(l):s:""],style:d?Il().isFunction(d)?d(l):d:null},I(o,a,i,e,t,r))})})(g,h))])])])}}});let o=Dl.VxeUI.getConfig;var Co={id:[String,Function],data:Array,height:[Number,String],minHeight:{type:[Number,String],default:()=>o().table.minHeight},maxHeight:[Number,String],resizable:{type:Boolean,default:()=>o().table.resizable},stripe:{type:Boolean,default:()=>o().table.stripe},border:{type:[Boolean,String],default:()=>o().table.border},padding:{type:Boolean,default:null},round:{type:Boolean,default:()=>o().table.round},size:{type:String,default:()=>o().table.size||o().size},fit:{type:Boolean,default:()=>o().table.fit},loading:Boolean,align:{type:String,default:()=>o().table.align},headerAlign:{type:String,default:()=>o().table.headerAlign},footerAlign:{type:String,default:()=>o().table.footerAlign},showHeader:{type:Boolean,default:()=>o().table.showHeader},highlightCurrentRow:{type:Boolean,default:()=>o().table.highlightCurrentRow},highlightHoverRow:{type:Boolean,default:()=>o().table.highlightHoverRow},highlightCurrentColumn:{type:Boolean,default:()=>o().table.highlightCurrentColumn},highlightHoverColumn:{type:Boolean,default:()=>o().table.highlightHoverColumn},highlightCell:Boolean,showFooter:Boolean,footerData:Array,footerMethod:Function,rowClassName:[String,Function],cellClassName:[String,Function],headerRowClassName:[String,Function],headerCellClassName:[String,Function],footerRowClassName:[String,Function],footerCellClassName:[String,Function],cellStyle:[Object,Function],headerCellStyle:[Object,Function],footerCellStyle:[Object,Function],rowStyle:[Object,Function],headerRowStyle:[Object,Function],footerRowStyle:[Object,Function],mergeCells:Array,mergeFooterItems:Array,spanMethod:Function,footerSpanMethod:Function,showOverflow:{type:[Boolean,String],default:()=>o().table.showOverflow},showHeaderOverflow:{type:[Boolean,String],default:()=>o().table.showHeaderOverflow},showFooterOverflow:{type:[Boolean,String],default:()=>o().table.showFooterOverflow},columnKey:Boolean,rowKey:Boolean,rowId:{type:String,default:()=>o().table.rowId},zIndex:Number,emptyText:{type:String,default:()=>o().table.emptyText},keepSource:{type:Boolean,default:()=>o().table.keepSource},autoResize:{type:Boolean,default:()=>o().table.autoResize},syncResize:[Boolean,String,Number],resizeConfig:Object,columnConfig:Object,currentColumnConfig:Object,cellConfig:Object,headerCellConfig:Object,footerCellConfig:Object,rowConfig:Object,aggregateConfig:Object,rowGroupConfig:Object,currentRowConfig:Object,dragConfig:Object,rowDragConfig:Object,columnDragConfig:Object,resizableConfig:Object,seqConfig:Object,sortConfig:Object,filterConfig:Object,radioConfig:Object,checkboxConfig:Object,tooltipConfig:Object,exportConfig:Object,importConfig:Object,printConfig:Object,expandConfig:Object,treeConfig:Object,menuConfig:Object,mouseConfig:Object,areaConfig:Object,keyboardConfig:Object,clipConfig:Object,fnrConfig:Object,editConfig:Object,validConfig:Object,editRules:Object,loadingConfig:Object,emptyRender:Object,customConfig:Object,scrollX:Object,scrollY:Object,virtualXConfig:Object,virtualYConfig:Object,scrollbarConfig:Object,animat:{type:Boolean,default:()=>o().table.animat},delayHover:{type:Number,default:()=>o().table.delayHover},params:Object},Nr=["update:data","keydown-start","keydown","keydown-end","paste","copy","cut","current-change","current-row-change","current-row-disabled","current-column-change","current-column-disabled","radio-change","checkbox-change","checkbox-all","checkbox-range-start","checkbox-range-change","checkbox-range-end","checkbox-range-select","cell-click","cell-dblclick","cell-menu","cell-mouseenter","cell-mouseleave","cell-selected","cell-delete-value","cell-backspace-value","header-cell-click","header-cell-dblclick","header-cell-menu","footer-cell-click","footer-cell-dblclick","footer-cell-menu","clear-merge","sort-change","clear-sort","clear-all-sort","filter-change","filter-visible","clear-filter","clear-all-filter","resizable-change","column-resizable-change","row-resizable-change","toggle-row-group-expand","toggle-row-expand","toggle-tree-expand","menu-click","edit-closed","row-dragstart","row-dragover","row-dragend","column-dragstart","column-dragover","column-dragend","enter-append-row","edit-actived","edit-activated","edit-disabled","valid-error","scroll","scroll-boundary","custom","change-fnr","open-fnr","show-fnr","hide-fnr","fnr-change","fnr-find","fnr-find-all","fnr-replace","fnr-replace-all","cell-area-copy","cell-area-cut","cell-area-paste","cell-area-merge","clear-cell-area-selection","clear-cell-area-merge","header-cell-area-selection","cell-area-selection-invalid","cell-area-selection-start","cell-area-selection-drag","cell-area-selection-end","cell-area-extension-start","cell-area-extension-drag","cell-area-extension-end","cell-area-selection-all-start","cell-area-selection-all-end","cell-area-arrows-start","cell-area-arrows-end","active-cell-change-start","active-cell-change-end"];let{getI18n:de,getIcon:ce,renderEmptyElement:ue}=Dl.VxeUI;var wo=Tt({name:"TableCustomPanel",props:{customStore:{type:Object,default:()=>({})}},setup(L){let V=Dl.VxeUI.getComponent("VxeModal"),$=Dl.VxeUI.getComponent("VxeDrawer"),_=Dl.VxeUI.getComponent("VxeButton"),H=Dl.VxeUI.getComponent("VxeNumberInput"),B=Dl.VxeUI.getComponent("VxeRadioGroup"),P=(0,Al.inject)("$xeTable",{}),{props:N,reactData:z,internalData:a}=P,{computeCustomOpts:j,computeColumnDragOpts:U,computeColumnOpts:ne,computeIsMaxFixedColumn:W,computeResizableOpts:ie}=P.getComputeMaps(),I=(0,Al.ref)(),G=(0,Al.ref)(),i=(0,Al.ref)(),s=(0,Al.ref)(),d=(0,Al.ref)(),b,C=!1,w,F=e=>{var t=L.customStore;t.activeWrapper=!0,P.customOpenEvent(e)},M=e=>{let t=L.customStore;t.activeWrapper=!1,setTimeout(()=>{t.activeBtn||t.activeWrapper||P.customCloseEvent(e)},300)},q=({$event:e})=>{z.isCustomStatus=!0,P.saveCustom(),P.closeCustom(),P.emitCustomEvent("confirm",e)},X=({$event:e})=>{P.closeCustom(),P.emitCustomEvent("close",e)},K=({$event:e})=>{P.cancelCustom(),P.closeCustom(),P.emitCustomEvent("cancel",e)},r=e=>{P.resetCustom(!0),P.closeCustom(),P.emitCustomEvent("reset",e)},Y=({$event:t})=>{Dl.VxeUI.modal?Dl.VxeUI.modal.confirm({content:de("vxe.custom.cstmConfirmRestore"),className:"vxe-table--ignore-clear",escClosable:!0}).then(e=>{"confirm"===e&&r(t)}):r(t)},l=t=>{var e=z.customColumnList,e=Il().findTree(e,e=>e===t);e&&e.parent&&(e=e.parent,e.children)&&e.children.length&&(e.renderVisible=e.children.every(e=>e.renderVisible),e.halfVisible=!e.renderVisible&&e.children.some(e=>e.renderVisible||e.halfVisible),l(e))},Z=e=>{let t=!e.renderVisible;j.value.immediate?(Il().eachTree([e],e=>{e.visible=t,e.renderVisible=t,e.halfVisible=!1}),z.isCustomStatus=!0,P.handleCustom(),P.saveCustomStore("update:visible")):Il().eachTree([e],e=>{e.renderVisible=t,e.halfVisible=!1}),l(e),P.checkCustomStatus()},se=e=>{j.value.immediate&&e.renderResizeWidth!==e.renderWidth&&(e.resizeWidth=e.renderResizeWidth,e.renderWidth=e.renderResizeWidth,z.isCustomStatus=!0,P.handleCustom(),P.saveCustomStore("update:width"))},Q=(e,t)=>{var r=W.value;j.value.immediate?(e.renderFixed===t?Il().eachTree([e],e=>{e.fixed="",e.renderFixed=""}):r&&!e.renderFixed||Il().eachTree([e],e=>{e.fixed=t,e.renderFixed=t}),z.isCustomStatus=!0,P.handleCustom(),P.saveCustomStore("update:fixed")):e.renderFixed===t?Il().eachTree([e],e=>{e.renderFixed=""}):r&&!e.renderFixed||Il().eachTree([e],e=>{e.renderFixed=t})},J=()=>{P.toggleCustomAllCheckbox()},c=(e,t,r,l)=>{var o,a,n=G.value;n&&(o=n.getBoundingClientRect(),t&&(a=i.value)&&(r?(t=t.getBoundingClientRect(),a.style.display="block",a.style.top=Math.max(1,t.y+n.scrollTop-o.y)+"px",a.style.height=t.height+"px",a.style.width=t.width+"px",a.setAttribute("drag-pos",l),a.setAttribute("drag-to-child",C?"y":"n")):a.style.display=""),t=s.value)&&(t.style.display="block",t.style.top=Math.min(n.clientHeight+n.scrollTop-t.clientHeight,e.clientY+n.scrollTop-o.y)+"px",t.style.left=Math.min(n.clientWidth+n.scrollLeft-t.clientWidth,e.clientX+n.scrollLeft-o.x)+"px",t.setAttribute("drag-status",r?C?"sub":"normal":"disabled"))},o=()=>{var e=s.value,t=i.value;e&&(e.style.display=""),t&&(t.style.display="")},ee=e=>{var e=e.currentTarget.parentElement.parentElement.parentElement,t=e.getAttribute("colid"),t=P.getColumnById(t);e.draggable=!0,d.value=t,$l(e,"active--drag-origin")},te=e=>{e=e.currentTarget.parentElement.parentElement.parentElement;o(),e.draggable=!1,d.value=null,Vl(e,"active--drag-origin")},re=e=>{e.dataTransfer&&e.dataTransfer.setDragImage(Zl(),0,0)},le=c=>{let u=N.mouseConfig,p=z.customColumnList,h=a.collectColumn;let g=j.value.immediate;var e=c.currentTarget,t=d.value;let{isCrossDrag:m,isSelfToChildDrag:v,isToChildDrag:f,dragEndMethod:r}=U.value,x="bottom"===w?1:0;if(b&&t&&b!==t){let s=t,d=b;Promise.resolve(!r||r({oldColumn:s,newColumn:d,dragColumn:s,dragPos:w,dragToChild:!!C,offsetIndex:x})).then(o=>{if(o){let e=-1,t=-1,r={},l=(Il().eachTree([s],e=>{r[e.id]=e}),!1);if(g){if(s.parentId&&d.parentId){if(!m)return;if(r[d.id]&&(l=!0,!m||!v))return void(Dl.VxeUI.modal&&Dl.VxeUI.modal.message({status:"error",content:de("vxe.error.treeDragChild")}))}else if(s.parentId){if(!m)return}else if(d.parentId){if(!m)return;if(r[d.id]&&(l=!0,!m||!v))return void(Dl.VxeUI.modal&&Dl.VxeUI.modal.message({status:"error",content:de("vxe.error.treeDragChild")}))}var a,n,i,o=Il().findTree(h,e=>e.id===s.id),o=(l&&m&&v?o&&({items:a,index:i}=o,(n=s.children||[]).forEach(e=>{e.parentId=s.parentId}),a.splice(i,1,...n),s.children=[]):o&&({items:a,index:i,parent:n}=o,a.splice(i,1),n||(e=i)),Il().findTree(h,e=>e.id===d.id));o&&({items:a,index:n,parent:i}=o,m&&f&&C?(s.parentId=d.id,d.children=(d.children||[]).concat([s])):(s.parentId=d.parentId,a.splice(n+x,0,s)),i||(t=n)),Il().eachTree(h,(e,t,r,l,o)=>{o||(e.renderSortNumber=t+1)})}else e=Il().findIndexOf(p,e=>e.id===s.id),p.splice(e,1),t=Il().findIndexOf(p,e=>e.id===d.id),p.splice(t+x,0,s);z.isDragColMove=!0,u&&(P.clearSelected&&P.clearSelected(),P.clearCellAreas)&&(P.clearCellAreas(),P.clearCopyCellArea()),P.dispatchEvent("column-dragend",{oldColumn:s,newColumn:d,dragColumn:s,dragPos:w,offsetIndex:x,_index:{newIndex:t,oldIndex:e}},c),g&&(z.customColumnList=h.slice(0),P.handleColDragSwapColumn())}}).catch(()=>{})}o(),d.value=null,e.draggable=!1,e.removeAttribute("drag-pos"),Vl(e,"active--drag-target"),Vl(e,"active--drag-origin")},oe=e=>{var t,r=j.value.immediate,{isCrossDrag:l,isToChildDrag:o}=U.value,a=e.currentTarget,n=eo(e),i=a.getAttribute("colid"),i=P.getColumnById(i),s=d.value;i&&(l||1===i.level)&&(e.preventDefault(),t=e.clientY-a.getBoundingClientRect().y<a.clientHeight/2?"top":"bottom",s&&s.id===i.id||!l&&1<i.level||!r&&1<i.level||i.renderFixed?c(e,a,!1,t):(C=!!(l&&o&&n&&r),b=i,w=t,c(e,a,!0,t)))},ae=()=>{var e=d.value,t=U.value;return(0,Al.h)("div",{},[(0,Al.h)("div",{ref:i,class:["vxe-table-custom-popup--drag-line",{"is--guides":t.showGuidesStatus}]}),(0,Al.h)("div",{ref:s,class:"vxe-table-custom-popup--drag-tip"},[(0,Al.h)("div",{class:"vxe-table-custom-popup--drag-tip-wrapper"},[(0,Al.h)("div",{class:"vxe-table-custom-popup--drag-tip-status"},[(0,Al.h)("span",{class:["vxe-table-custom-popup--drag-tip-normal-status",ce().TABLE_DRAG_STATUS_ROW]}),(0,Al.h)("span",{class:["vxe-table-custom-popup--drag-tip-sub-status",ce().TABLE_DRAG_STATUS_SUB_ROW]}),(0,Al.h)("span",{class:["vxe-table-custom-popup--drag-tip-disabled-status",ce().TABLE_DRAG_DISABLED]})]),(0,Al.h)("div",{class:"vxe-table-custom-popup--drag-tip-content"},de("vxe.custom.cstmDragTarget",[e&&"html"!==e.type?e.getTitle():""]))])])])};return(0,Al.nextTick)(()=>{var e=j.value.mode;V||"modal"!==e||pl("vxe.error.reqComp",["vxe-modal"]),$||"drawer"!==e||pl("vxe.error.reqComp",["vxe-drawer"]),_||pl("vxe.error.reqComp",["vxe-button"]),H||pl("vxe.error.reqComp",["vxe-number-input"]),B||pl("vxe.error.reqComp",["vxe-radio-group"])}),()=>{var e=j.value;return(["modal","drawer","popup"].includes(""+e.mode)?()=>{var e=P.xeGrid;let t=L.customStore,c=N.resizable,{isCustomStatus:r,customColumnList:l}=z,o=j.value,u=o.immediate;var a=U.value;let{mode:n,modalOptions:i,drawerOptions:s,allowVisible:p,allowSort:h,allowFixed:g,allowResizable:m,checkMethod:v,visibleMethod:f}=o,x=ne.value,d=x.maxFixedSize,{minWidth:b,maxWidth:C}=ie.value;var w=Object.assign({},i),y=Object.assign({},s);let T=W.value,E=a.isCrossDrag;a=o.slots||{};let R=a.header,S=a.top,D=a.bottom,I=a.default,F=a.footer,M=[],k=t.isAll,O=t.isIndeterminate,A={$table:P,$grid:e,columns:l,isAllChecked:k,isAllIndeterminate:O,isCustomStatus:r};Il().eachTree(l,(l,o,e,t,a)=>{if(!f||f({$table:P,column:l})){let e=0,t=0;m&&(o={$table:P,column:l,columnIndex:o,$columnIndex:o,$rowIndex:-1},b&&(e=Il().toNumber(Il().isFunction(b)?b(o):b)),C)&&(t=Il().toNumber(Il().isFunction(C)?C(o):C));var o=l.renderVisible,n=l.halfVisible,i=kl(l.getTitle(),1),s=l.children&&l.children.length;let r=!!v&&!v({$table:P,column:l});var d=!o;M.push((0,Al.h)("tr",{key:l.id,colid:l.id,class:["vxe-table-custom-popup--row level--"+l.level,{"is--group":s}],onDragstart:re,onDragend:le,onDragover:oe},[p?(0,Al.h)("td",{class:"vxe-table-custom-popup--column-item col--visible"},[(0,Al.h)("div",{class:["vxe-table-custom--checkbox-option",{"is--checked":o,"is--indeterminate":n,"is--disabled":r}],title:de("vxe.custom.setting.colVisible"),onClick:()=>{r||Z(l)}},[(0,Al.h)("span",{class:["vxe-checkbox--icon",n?ce().TABLE_CHECKBOX_INDETERMINATE:o?ce().TABLE_CHECKBOX_CHECKED:ce().TABLE_CHECKBOX_UNCHECKED]})])]):(0,Al.createCommentVNode)(),(0,Al.h)("td",{class:"vxe-table-custom-popup--column-item col--name"},[(0,Al.h)("div",{class:"vxe-table-custom-popup--name"},[h?E&&u||1===l.level?(0,Al.h)("div",{class:["vxe-table-custom-popup--column-sort-btn",{"is--disabled":r||d||l.renderFixed}],title:de("vxe.custom.setting.sortHelpTip"),...r||d||l.renderFixed?{}:{onMousedown:ee,onMouseup:te}},[(0,Al.h)("i",{class:ce().TABLE_CUSTOM_SORT})]):(0,Al.h)("div",{class:"vxe-table-custom-popup--column-sort-placeholder"}):(0,Al.createCommentVNode)(),"html"===l.type?(0,Al.h)("div",{key:"1",class:"vxe-table-custom-popup--title",innerHTML:i}):(0,Al.h)("div",{key:"0",class:"vxe-table-custom-popup--title",title:i},i)])]),m?(0,Al.h)("td",{class:"vxe-table-custom-popup--column-item col--resizable"},[l.children&&l.children.length||!(Il().isBoolean(l.resizable)?l.resizable:x.resizable||c)?(0,Al.h)("span","-"):H?(0,Al.h)(H,{type:"integer",immediate:!1,disabled:r||d,modelValue:l.renderResizeWidth,min:e||void 0,max:t||void 0,"onUpdate:modelValue"(e){e=Math.max(0,Number(e));l.renderResizeWidth=e},onChange(){se(l)}}):(0,Al.createCommentVNode)()]):(0,Al.createCommentVNode)(),g?(0,Al.h)("td",{class:"vxe-table-custom-popup--column-item col--fixed"},[a?(0,Al.h)("span","-"):B?(0,Al.h)(B,{modelValue:l.renderFixed||"",type:"button",size:"mini",disabled:r||d,options:[{label:de("vxe.custom.setting.fixedLeft"),value:"left",disabled:r||d||T},{label:de("vxe.custom.setting.fixedUnset"),value:"",disabled:r||d},{label:de("vxe.custom.setting.fixedRight"),value:"right",disabled:r||d||T}],"onUpdate:modelValue"(e){Q(l,e)}}):(0,Al.createCommentVNode)()]):(0,Al.createCommentVNode)()]))}});a={default:()=>I?P.callSlot(I,A):(0,Al.h)("div",{ref:G,class:"vxe-table-custom-popup--body"},[S?(0,Al.h)("div",{class:"vxe-table-custom-popup--table-top"},P.callSlot(S,A)):ue(P),(0,Al.h)("div",{class:"vxe-table-custom-popup--table-wrapper"},[(0,Al.h)("table",{},[(0,Al.h)("colgroup",{},[p?(0,Al.h)("col",{class:"vxe-table-custom-popup--table-col-seq"}):(0,Al.createCommentVNode)(),(0,Al.h)("col",{class:"vxe-table-custom-popup--table-col-title"}),m?(0,Al.h)("col",{class:"vxe-table-custom-popup--table-col-width"}):(0,Al.createCommentVNode)(),g?(0,Al.h)("col",{class:"vxe-table-custom-popup--table-col-fixed"}):(0,Al.createCommentVNode)()]),(0,Al.h)("thead",{},[(0,Al.h)("tr",{},[p?(0,Al.h)("th",{},[(0,Al.h)("div",{class:["vxe-table-custom--checkbox-option",{"is--checked":k,"is--indeterminate":O}],title:de("vxe.table.allTitle"),onClick:J},[(0,Al.h)("span",{class:["vxe-checkbox--icon",O?ce().TABLE_CHECKBOX_INDETERMINATE:k?ce().TABLE_CHECKBOX_CHECKED:ce().TABLE_CHECKBOX_UNCHECKED]}),(0,Al.h)("span",{class:"vxe-checkbox--label"},de("vxe.toolbar.customAll"))])]):(0,Al.createCommentVNode)(),(0,Al.h)("th",{},de("vxe.custom.setting.colTitle")),m?(0,Al.h)("th",{},de("vxe.custom.setting.colResizable")):(0,Al.createCommentVNode)(),g?(0,Al.h)("th",{},de("vxe.custom.setting."+(d?"colFixedMax":"colFixed"),[d])):(0,Al.createCommentVNode)()])]),(0,Al.h)(Al.TransitionGroup,{class:"vxe-table-custom--panel-list",tag:"tbody",name:"vxe-table-custom--list"},{default:()=>M})])]),D?(0,Al.h)("div",{class:"vxe-table-custom-popup--table-bottom"},P.callSlot(D,A)):ue(P),ae()]),footer:()=>F?P.callSlot(F,A):(0,Al.h)("div",{class:"vxe-table-custom-popup--footer"},[_?(0,Al.h)(_,{content:o.resetButtonText||de("vxe.custom.cstmRestore"),disabled:!r,onClick:Y}):(0,Al.createCommentVNode)(),u?_?(0,Al.h)(_,{content:o.closeButtonText||de("vxe.table.customClose"),onClick:X}):(0,Al.createCommentVNode)():_?(0,Al.h)(_,{content:o.cancelButtonText||de("vxe.table.customCancel"),onClick:K}):(0,Al.createCommentVNode)(),!u&&_?(0,Al.h)(_,{status:"primary",content:o.confirmButtonText||de("vxe.custom.cstmConfirm"),onClick:q}):(0,Al.createCommentVNode)()])};return R&&(a.header=()=>P.callSlot(R,A)),"drawer"===n?$?(0,Al.h)($,{key:"drawer",className:["vxe-table-custom-drawer-wrapper","vxe-table--ignore-clear",y.className||""].join(" "),modelValue:t.visible,title:y.title||de("vxe.custom.cstmTitle"),width:y.width||Math.min(880,Math.floor(.6*document.documentElement.clientWidth)),position:y.position,resize:!!y.resize,escClosable:!!y.escClosable,maskClosable:!!y.maskClosable,destroyOnClose:!0,showFooter:!0,"onUpdate:modelValue"(e){t.visible=e}},a):(0,Al.createCommentVNode)():V?(0,Al.h)(V,{key:"modal",className:["vxe-table-custom-modal-wrapper","vxe-table--ignore-clear",w.className||""].join(" "),modelValue:t.visible,title:w.title||de("vxe.custom.cstmTitle"),width:w.width||Math.min(880,document.documentElement.clientWidth),minWidth:w.minWidth||700,height:w.height||Math.min(680,document.documentElement.clientHeight),minHeight:w.minHeight||400,showZoom:w.showZoom,showMaximize:w.showMaximize,showMinimize:w.showMinimize,mask:w.mask,lockView:w.lockView,resize:w.resize,escClosable:!!w.escClosable,maskClosable:!!w.maskClosable,destroyOnClose:!0,showFooter:!0,"onUpdate:modelValue"(e){t.visible=e}},a):(0,Al.createCommentVNode)()}:()=>{var e=P.xeGrid,t=P.props,r=L.customStore,{treeConfig:t,rowGroupConfig:l,aggregateConfig:o}=t,{isCustomStatus:a,customColumnList:n}=z,i=j.value;let c=i.immediate;var s=U.value,d=r.maxHeight;let{checkMethod:u,visibleMethod:p,allowVisible:h,allowSort:g,allowFixed:m,trigger:v,placement:f}=i,x=W.value,b=s.isCrossDrag;var s=i.slots||{},C=s.header,w=s.top,y=s.bottom,T=s.default,s=s.footer;let E=[];var R={},S=r.isAll,D=r.isIndeterminate,e=("hover"===v&&(R.onMouseenter=F,R.onMouseleave=M),{$table:P,$grid:e,columns:n,isAllChecked:S,isAllIndeterminate:D,isCustomStatus:a});return Il().eachTree(n,(t,e,r,l,o)=>{if(!p||p({$table:P,column:t})){var a=t.renderVisible,n=t.halfVisible,i=t.children&&t.children.length,s=kl(t.getTitle(),1);let e=!!u&&!u({$table:P,column:t});var d=!a;E.push((0,Al.h)("li",{key:t.id,colid:t.id,class:["vxe-table-custom--option","level--"+t.level,{"is--hidden":e||d,"is--group":i}],onDragstart:re,onDragend:le,onDragover:oe},[h?(0,Al.h)("div",{class:["vxe-table-custom--checkbox-option",{"is--checked":a,"is--indeterminate":n,"is--disabled":e}],title:de("vxe.custom.setting.colVisible"),onClick:()=>{e||Z(t)}},[(0,Al.h)("span",{class:["vxe-checkbox--icon",n?ce().TABLE_CHECKBOX_INDETERMINATE:a?ce().TABLE_CHECKBOX_CHECKED:ce().TABLE_CHECKBOX_UNCHECKED]})]):(0,Al.createCommentVNode)(),(0,Al.h)("div",{class:"vxe-table-custom--name-option"},[g&&(b&&c||1===t.level)?(0,Al.h)("div",{class:"vxe-table-custom--sort-option"},[(0,Al.h)("span",{class:["vxe-table-custom--sort-btn",{"is--disabled":e||d||t.renderFixed}],title:de("vxe.custom.setting.sortHelpTip"),...e||d||t.renderFixed?{}:{onMousedown:ee,onMouseup:te}},[(0,Al.h)("i",{class:ce().TABLE_CUSTOM_SORT})])]):(0,Al.createCommentVNode)(),"html"===t.type?(0,Al.h)("div",{key:"1",class:"vxe-table-custom--checkbox-label",innerHTML:s}):(0,Al.h)("div",{key:"0",class:"vxe-table-custom--checkbox-label"},s)]),!o&&m?(0,Al.h)("div",{class:"vxe-table-custom--fixed-option"},[_?(0,Al.h)(_,{mode:"text",icon:"left"===t.renderFixed?ce().TOOLBAR_TOOLS_FIXED_LEFT_ACTIVE:ce().TOOLBAR_TOOLS_FIXED_LEFT,status:"left"===t.renderFixed?"primary":"",disabled:e||d||x&&!t.renderFixed,title:de("left"===t.renderFixed?"vxe.toolbar.cancelFixed":"vxe.toolbar.fixedLeft"),onClick:()=>{Q(t,"left")}}):(0,Al.createCommentVNode)(),_?(0,Al.h)(_,{mode:"text",icon:"right"===t.renderFixed?ce().TOOLBAR_TOOLS_FIXED_RIGHT_ACTIVE:ce().TOOLBAR_TOOLS_FIXED_RIGHT,status:"right"===t.renderFixed?"primary":"",disabled:e||d||x&&!t.renderFixed,title:de("right"===t.renderFixed?"vxe.toolbar.cancelFixed":"vxe.toolbar.fixedRight"),onClick:()=>{Q(t,"right")}}):(0,Al.createCommentVNode)()]):(0,Al.createCommentVNode)()]))}}),(0,Al.h)("div",{ref:I,key:"simple",class:["vxe-table-custom-wrapper","placement--"+f,{"is--active":r.visible}],style:d&&!["left","right"].includes(f)?{maxHeight:d+"px"}:{}},r.visible?[!t&&(o||l)&&P.getPivotTableAggregateSimplePanel?(0,Al.h)(P.getPivotTableAggregateSimplePanel(),{customStore:r}):ue(P),(0,Al.h)("div",{class:"vxe-table-custom--handle-wrapper"},[(0,Al.h)("div",{class:"vxe-table-custom--header"},C?P.callSlot(C,e):[(0,Al.h)("ul",{class:"vxe-table-custom--panel-list"},[(0,Al.h)("li",{class:"vxe-table-custom--option"},[h?(0,Al.h)("div",{class:["vxe-table-custom--checkbox-option",{"is--checked":S,"is--indeterminate":D}],title:de("vxe.table.allTitle"),onClick:J},[(0,Al.h)("span",{class:["vxe-checkbox--icon",D?ce().TABLE_CHECKBOX_INDETERMINATE:S?ce().TABLE_CHECKBOX_CHECKED:ce().TABLE_CHECKBOX_UNCHECKED]}),(0,Al.h)("span",{class:"vxe-checkbox--label"},de("vxe.toolbar.customAll"))]):(0,Al.h)("span",{class:"vxe-checkbox--label"},de("vxe.table.customTitle"))])])]),(0,Al.h)("div",{ref:G,class:"vxe-table-custom--body"},[w?(0,Al.h)("div",{class:"vxe-table-custom--panel-top"},P.callSlot(w,e)):ue(P),T?(0,Al.h)("div",{class:"vxe-table-custom--panel-body"},P.callSlot(T,e)):(0,Al.h)(Al.TransitionGroup,{class:"vxe-table-custom--panel-list",name:"vxe-table-custom--list",tag:"ul",...R},{default:()=>E}),y?(0,Al.h)("div",{class:"vxe-table-custom--panel-bottom"},P.callSlot(y,e)):ue(P),ae()]),i.showFooter?(0,Al.h)("div",{class:"vxe-table-custom--footer"},s?P.callSlot(s,e):[(0,Al.h)("div",{class:"vxe-table-custom--footer-buttons"},[_?(0,Al.h)(_,{mode:"text",content:i.resetButtonText||de("vxe.table.customRestore"),disabled:!a,onClick:Y}):(0,Al.createCommentVNode)(),c?_?(0,Al.h)(_,{mode:"text",content:i.closeButtonText||de("vxe.table.customClose"),onClick:X}):(0,Al.createCommentVNode)():_?(0,Al.h)(_,{mode:"text",content:i.cancelButtonText||de("vxe.table.customCancel"),onClick:K}):(0,Al.createCommentVNode)(),!c&&_?(0,Al.h)(_,{mode:"text",status:"primary",content:i.confirmButtonText||de("vxe.table.customConfirm"),onClick:q}):(0,Al.createCommentVNode)()])]):null])]:[])})()}}});let{getI18n:R,getIcon:D,renderer:ye}=Dl.VxeUI;var yo=Tt({name:"VxeTableFilterPanel",props:{filterStore:Object},setup(h,e){var t=Il().uniqueId();let g=(0,Al.inject)("$xeTable",{}),{reactData:m,internalData:v,getComputeMaps:r}=g,f=r().computeFilterOpts,x=(0,Al.ref)(),l={refElem:x},b={xID:t,props:h,context:e,getRefMaps:()=>l},C=(0,Al.computed)(()=>{var e=h.filterStore;return e&&e.options.some(e=>e.checked)}),o=(e,t)=>{var r=h.filterStore;r.options.forEach(e=>{e._checked=t,e.checked=t}),r.isAllSelected=t,r.isIndeterminate=!1},w=e=>{g.handleFilterConfirmFilter(e)};let y=e=>{g.handleFilterResetFilter(e)};let T=(e,t,r)=>{g.handleFilterChangeOption(e,t,r)},E=(e,t)=>{var r=h.filterStore;r.multiple?o(0,t):y(e)};t={changeRadioOption:(e,t,r)=>{g.handleFilterChangeRadioOption(e,t,r)},changeMultipleOption:(e,t,r)=>{g.handleFilterChangeMultipleOption(e,t,r)},changeAllOption:E,changeOption:T,confirmFilter:w,resetFilter:y};Object.assign(b,t);return b.renderVN=()=>{var e=h.filterStore,t=m.initStore,{visible:r,multiple:l,column:o}=e,a=o?o.filterRender:null,n=Fl(a)?ye.get(a.name):null,i=n?n.tableFilterClassName||n.filterClassName:"",s=Object.assign({},v._currFilterParams,{$panel:b,$table:g}),d=g.props,c=g.getComputeMaps().computeSize,c=c.value,{transfer:u,destroyOnClose:p}=f.value;return(0,Al.h)(Al.Teleport,{to:"body",disabled:!u},[(0,Al.h)("div",{ref:x,class:["vxe-table--filter-wrapper","filter--prevent-default",Et(i,s),{["size--"+c]:c,"is--animat":d.animat,"is--multiple":l,"is--active":r}],style:e.style},t.filter&&(!p||r)&&o?((e,t)=>{let r=h.filterStore,{column:l,multiple:o,maxHeight:a}=r;var n=l?l.slots:null,n=n?n.filter:null,i=Object.assign({},v._currFilterParams,{$panel:b,$table:g}),t=t?t.renderTableFilter||t.renderFilter:null;return n?[(0,Al.h)("div",{class:"vxe-table--filter-template",style:a?{maxHeight:a+"px"}:{}},g.callSlot(n,i))]:t?[(0,Al.h)("div",{class:"vxe-table--filter-template",style:a?{maxHeight:a+"px"}:{}},Gl(t(e,i)))]:(n=o?r.isAllSelected:!r.options.some(e=>e._checked),t=o&&r.isIndeterminate,[(0,Al.h)("ul",{class:"vxe-table--filter-header"},[(0,Al.h)("li",{class:["vxe-table--filter-option",{"is--checked":n,"is--indeterminate":t}],title:R(o?"vxe.table.allTitle":"vxe.table.allFilter"),onClick:e=>{E(e,!r.isAllSelected)}},(o?[(0,Al.h)("span",{class:["vxe-checkbox--icon",t?D().TABLE_CHECKBOX_INDETERMINATE:n?D().TABLE_CHECKBOX_CHECKED:D().TABLE_CHECKBOX_UNCHECKED]})]:[]).concat([(0,Al.h)("span",{class:"vxe-checkbox--label"},R("vxe.table.allFilter"))]))]),(0,Al.h)("ul",{class:"vxe-table--filter-body",style:a?{maxHeight:a+"px"}:{}},r.options.map(t=>{var e=t._checked;return(0,Al.h)("li",{class:["vxe-table--filter-option",{"is--checked":t._checked}],title:t.label,onClick:e=>{T(e,!t._checked,t)}},(o?[(0,Al.h)("span",{class:["vxe-checkbox--icon",e?D().TABLE_CHECKBOX_CHECKED:D().TABLE_CHECKBOX_UNCHECKED]})]:[]).concat([(0,Al.h)("span",{class:"vxe-checkbox--label"},kl(t.label,1))]))}))])})(a,n).concat((()=>{var e=h.filterStore,{column:t,multiple:r}=e,l=f.value,o=C.value,t=t.filterRender,t=Fl(t)?ye.get(t.name):null,o=!o&&!e.isAllSelected&&!e.isIndeterminate;return r&&(!t||!1!==t.showTableFilterFooter&&!1!==t.showFilterFooter&&!1!==t.isFooter)?[(0,Al.h)("div",{class:"vxe-table--filter-footer"},[(0,Al.h)("button",{class:{"is--disabled":o},disabled:o,onClick:w},l.confirmButtonText||R("vxe.table.confirmFilter")),(0,Al.h)("button",{onClick:y},l.resetButtonText||R("vxe.table.resetFilter"))])]:[]})()):[])])},b},render(){return this.renderVN()}});yt(2577);let{getI18n:I,getIcon:Te}=Dl.VxeUI;var To=Tt({name:"VxeTableImportPanel",props:{defaultOptions:Object,storeData:Object},setup(u){let p=Dl.VxeUI.getComponent("VxeModal"),h=Dl.VxeUI.getComponent("VxeButton"),g=Dl.VxeUI.getComponent("VxeSelect"),m=(0,Al.inject)("$xeTable",{}),l=m.getComputeMaps().computeImportOpts,v=(0,Al.reactive)({loading:!1}),f=(0,Al.ref)(),x=(0,Al.computed)(()=>{var e=u.storeData;return e.filename+"."+e.type}),b=(0,Al.computed)(()=>{var e=u.storeData;return e.file&&e.type}),C=(0,Al.computed)(()=>{var e=u.storeData;let{type:t,typeList:r}=e;return t?(e=Il().find(r,e=>t===e.value))?e.label:"*.*":"*."+r.map(e=>e.value).join(", *.")}),w=()=>{var e=u.storeData;Object.assign(e,{filename:"",sheetName:"",type:""})},y=()=>{let{storeData:t,defaultOptions:e}=u;m.readFile(e).then(e=>{e=e.file;Object.assign(t,hr(e),{file:e})}).catch(e=>e)},T=()=>{(0,Al.nextTick)(()=>{var e=f.value;e&&e.focus()})},E=()=>{var e=u.storeData;e.visible=!1},R=()=>{let{storeData:e,defaultOptions:t}=u;var r=l.value;v.loading=!0,m.importByFile(e.file,Object.assign({},r,t)).then(()=>{v.loading=!1,e.visible=!1}).catch(()=>{v.loading=!1})};return(0,Al.nextTick)(()=>{p||pl("vxe.error.reqComp",["vxe-modal"]),h||pl("vxe.error.reqComp",["vxe-button"]),g||pl("vxe.error.reqComp",["vxe-select"])}),()=>{let t=m.xeGrid,{defaultOptions:r,storeData:l}=u,o=x.value,a=b.value,n=C.value;var e=r.slots||{};let i=e.top,s=e.bottom,d=e.default,c=e.footer;return p?(0,Al.h)(p,{id:"VXE_IMPORT_MODAL",modelValue:l.visible,title:I("vxe.import.impTitle"),className:"vxe-table-export-popup-wrapper",width:540,minWidth:360,minHeight:240,mask:!0,lockView:!0,showFooter:!0,escClosable:!0,maskClosable:!0,showMaximize:!0,resize:!0,loading:v.loading,"onUpdate:modelValue"(e){l.visible=e},onShow:T},{default:()=>{var e={$table:m,$grid:t,options:r,params:r.params};return(0,Al.h)("div",{class:"vxe-table-export--panel"},[i?(0,Al.h)("div",{class:"vxe-table-export--panel-top"},m.callSlot(i,e)):(0,Al.createCommentVNode)(),(0,Al.h)("div",{class:"vxe-table-export--panel-body"},d?m.callSlot(d,e):[(0,Al.h)("table",{class:"vxe-table-export--panel-table",cellspacing:0,cellpadding:0,border:0},[(0,Al.h)("tbody",[(0,Al.h)("tr",[(0,Al.h)("td",I("vxe.import.impFile")),(0,Al.h)("td",[a?(0,Al.h)("div",{class:"vxe-table-export--selected--file",title:o},[(0,Al.h)("span",o),(0,Al.h)("i",{class:Te().INPUT_CLEAR,onClick:w})]):(0,Al.h)("button",{ref:f,class:"vxe-table-export--select--file",onClick:y},I("vxe.import.impSelect"))])]),(0,Al.h)("tr",[(0,Al.h)("td",I("vxe.import.impType")),(0,Al.h)("td",n)]),(0,Al.h)("tr",[(0,Al.h)("td",I("vxe.import.impMode")),(0,Al.h)("td",[g?(0,Al.h)(g,{modelValue:r.mode,options:l.modeList,"onUpdate:modelValue"(e){r.mode=e}}):(0,Al.createCommentVNode)()])])])])]),s?(0,Al.h)("div",{class:"vxe-table-export--panel-bottom"},m.callSlot(s,e)):(0,Al.createCommentVNode)()])},footer(){var e={$table:m,$grid:t,options:r,params:r.params};return(0,Al.h)("div",{class:"vxe-table-export--panel-footer"},c?m.callSlot(c,e):[(0,Al.h)("div",{class:"vxe-table-export--panel-btns"},[h?(0,Al.h)(h,{content:I("vxe.import.impCancel"),onClick:E}):(0,Al.createCommentVNode)(),h?(0,Al.h)(h,{status:"primary",disabled:!a||v.loading,content:I("vxe.import.impConfirm"),onClick:R}):(0,Al.createCommentVNode)()])])}}):(0,Al.createCommentVNode)()}}});let{getI18n:j,getIcon:U}=Dl.VxeUI;var Eo=Tt({name:"VxeTableExportPanel",props:{defaultOptions:Object,storeData:Object},setup(y){let T=Dl.VxeUI.getComponent("VxeModal"),E=Dl.VxeUI.getComponent("VxeButton"),R=Dl.VxeUI.getComponent("VxeSelect"),S=Dl.VxeUI.getComponent("VxeInput"),D=Dl.VxeUI.getComponent("VxeCheckbox"),I=(0,Al.inject)("$xeTable",{}),{computeExportOpts:r,computePrintOpts:l}=I.getComputeMaps(),F=(0,Al.reactive)({isAll:!1,isIndeterminate:!1,loading:!1}),M=(0,Al.ref)(),k=(0,Al.ref)(),O=(0,Al.ref)(),A=(0,Al.computed)(()=>{var e=y.storeData;return e.columns.every(e=>e.checked)}),L=(0,Al.computed)(()=>{var e=y.defaultOptions;return-1<["html","xml","xlsx","pdf"].indexOf(e.type)}),V=(0,Al.computed)(()=>{var{storeData:e,defaultOptions:t}=y;return!t.original&&"current"===t.mode&&(e.isPrint||-1<["html","xlsx"].indexOf(t.type))}),$=(0,Al.computed)(()=>{var e=y.defaultOptions;return!e.original&&-1<["xlsx"].indexOf(e.type)}),_=t=>{var e=y.storeData,e=Il().findTree(e.columns,e=>e===t);e&&e.parent&&(e=e.parent,e.children)&&e.children.length&&(e.checked=e.children.every(e=>e.checked),e.halfChecked=!e.checked&&e.children.some(e=>e.checked||e.halfChecked),_(e))},H=()=>{var e=y.storeData,e=e.columns;F.isAll=e.every(e=>e.disabled||e.checked),F.isIndeterminate=!F.isAll&&e.some(e=>!e.disabled&&(e.checked||e.halfChecked))},B=()=>{var e=y.storeData;let t=!F.isAll;Il().eachTree(e.columns,e=>{e.disabled||(e.checked=t,e.halfChecked=!1)}),F.isAll=t,H()},P=()=>{(0,Al.nextTick)(()=>{var e=k.value,t=O.value,r=M.value,e=e||t||r;e&&e.focus()}),H()},o=()=>{var{storeData:e,defaultOptions:t}=y,{hasMerge:e,columns:r}=e,l=A.value,o=V.value,r=Il().searchTree(r,e=>e.checked,{children:"children",mapChildren:"childNodes",original:!0});return Object.assign({},t,{columns:r,isMerge:!!(e&&o&&l)&&t.isMerge})},t=()=>{var e=y.storeData,t=l.value;e.visible=!1,I.print(Object.assign({},t,o()))},a=()=>{let e=y.storeData;var t=r.value;F.loading=!0,I.exportData(Object.assign({},t,o())).then(()=>{F.loading=!1,e.visible=!1}).catch(()=>{F.loading=!1})},N=()=>{var e=y.storeData;e.visible=!1},z=()=>{var e=y.storeData;(e.isPrint?t:a)()};return(0,Al.nextTick)(()=>{T||pl("vxe.error.reqComp",["vxe-modal"]),E||pl("vxe.error.reqComp",["vxe-button"]),R||pl("vxe.error.reqComp",["vxe-select"]),S||pl("vxe.error.reqComp",["vxe-input"]),D||pl("vxe.error.reqComp",["vxe-checkbox"])}),()=>{let r=I.xeGrid,{defaultOptions:l,storeData:o}=y,{isAll:a,isIndeterminate:n}=F,{hasTree:i,hasMerge:s,isPrint:d,hasColgroup:c,columns:u}=o,p=l.isHeader,h=[],g=A.value,m=L.value,v=V.value,f=$.value;var e=l.slots||{};let x=e.top,b=e.bottom,C=e.default,t=e.footer,w=e.parameter;return Il().eachTree(u,e=>{var t=kl(e.getTitle(),1),r=e.children&&e.children.length,l=e.checked,o=e.halfChecked,a="html"===e.type;h.push((0,Al.h)("li",{key:e.id,class:["vxe-table-export--panel-column-option","level--"+e.level,{"is--group":r,"is--checked":l,"is--indeterminate":o,"is--disabled":e.disabled}],title:a?"":t,onClick:()=>{e.disabled||(e=>{let t=!e.checked;Il().eachTree([e],e=>{e.checked=t,e.halfChecked=!1}),_(e),H()})(e)}},[(0,Al.h)("span",{class:["vxe-checkbox--icon",o?U().TABLE_CHECKBOX_INDETERMINATE:l?U().TABLE_CHECKBOX_CHECKED:U().TABLE_CHECKBOX_UNCHECKED]}),a?(0,Al.h)("span",{key:"1",class:"vxe-checkbox--label",innerHTML:t}):(0,Al.h)("span",{key:"0",class:"vxe-checkbox--label"},t)]))}),T?(0,Al.h)(T,{id:"VXE_EXPORT_MODAL",modelValue:o.visible,title:j(d?"vxe.export.printTitle":"vxe.export.expTitle"),className:"vxe-table-export-popup-wrapper",width:660,minWidth:500,minHeight:400,mask:!0,lockView:!0,showFooter:!0,escClosable:!0,maskClosable:!0,showMaximize:!0,resize:!0,loading:F.loading,"onUpdate:modelValue"(e){o.visible=e},onShow:P},{default:()=>{var e={$table:I,$grid:r,options:l,columns:u,params:l.params},t="empty"===l.mode;return(0,Al.h)("div",{class:"vxe-table-export--panel"},[x?(0,Al.h)("div",{class:"vxe-table-export--panel-top"},I.callSlot(x,e)):(0,Al.createCommentVNode)(),(0,Al.h)("div",{class:"vxe-table-export--panel-body"},C?I.callSlot(C,e):[(0,Al.h)("table",{class:"vxe-table-export--panel-table",cellspacing:0,cellpadding:0,border:0},[(0,Al.h)("tbody",[[d?(0,Al.createCommentVNode)():(0,Al.h)("tr",[(0,Al.h)("td",j("vxe.export.expName")),(0,Al.h)("td",[S?(0,Al.h)(S,{ref:k,modelValue:l.filename,type:"text",clearable:!0,placeholder:j("vxe.export.expNamePlaceholder"),"onUpdate:modelValue"(e){l.filename=e}}):(0,Al.createCommentVNode)()])]),d?(0,Al.createCommentVNode)():(0,Al.h)("tr",[(0,Al.h)("td",j("vxe.export.expType")),(0,Al.h)("td",[R?(0,Al.h)(R,{modelValue:l.type,options:o.typeList,"onUpdate:modelValue"(e){l.type=e}}):(0,Al.createCommentVNode)()])]),d||m?(0,Al.h)("tr",[(0,Al.h)("td",j("vxe.export.expSheetName")),(0,Al.h)("td",[S?(0,Al.h)(S,{ref:O,modelValue:l.sheetName,type:"text",clearable:!0,placeholder:j("vxe.export.expSheetNamePlaceholder"),"onUpdate:modelValue"(e){l.sheetName=e}}):(0,Al.createCommentVNode)()])]):(0,Al.createCommentVNode)(),(0,Al.h)("tr",[(0,Al.h)("td",j("vxe.export.expMode")),(0,Al.h)("td",[R?(0,Al.h)(R,{modelValue:l.mode,options:o.modeList.map(e=>({value:e.value,label:j(e.label)})),"onUpdate:modelValue"(e){l.mode=e}}):(0,Al.createCommentVNode)()])]),(0,Al.h)("tr",[(0,Al.h)("td",[j("vxe.export.expColumn")]),(0,Al.h)("td",[(0,Al.h)("div",{class:"vxe-table-export--panel-column"},[(0,Al.h)("ul",{class:"vxe-table-export--panel-column-header"},[(0,Al.h)("li",{class:["vxe-table-export--panel-column-option",{"is--checked":a,"is--indeterminate":n}],title:j("vxe.table.allTitle"),onClick:B},[(0,Al.h)("span",{class:["vxe-checkbox--icon",n?U().TABLE_CHECKBOX_INDETERMINATE:a?U().TABLE_CHECKBOX_CHECKED:U().TABLE_CHECKBOX_UNCHECKED]}),(0,Al.h)("span",{class:"vxe-checkbox--label"},j("vxe.export.expCurrentColumn"))])]),(0,Al.h)("ul",{class:"vxe-table-export--panel-column-body"},h)])])]),(0,Al.h)("tr",[(0,Al.h)("td",j("vxe.export.expOpts")),w?(0,Al.h)("td",[(0,Al.h)("div",{class:"vxe-table-export--panel-option-row"},I.callSlot(w,e))]):(0,Al.h)("td",[(0,Al.h)("div",{class:"vxe-table-export--panel-option-row"},[D?(0,Al.h)(D,{modelValue:t||p,disabled:t,title:j("vxe.export.expHeaderTitle"),content:j("vxe.export.expOptHeader"),"onUpdate:modelValue"(e){l.isHeader=e}}):(0,Al.createCommentVNode)(),D?(0,Al.h)(D,{modelValue:!!p&&l.isTitle,disabled:!p,title:j("vxe.export.expTitleTitle"),content:j("vxe.export.expOptTitle"),"onUpdate:modelValue"(e){l.isTitle=e}}):(0,Al.createCommentVNode)(),D?(0,Al.h)(D,{modelValue:!!(p&&c&&v)&&l.isColgroup,title:j("vxe.export.expColgroupTitle"),disabled:!p||!c||!v,content:j("vxe.export.expOptColgroup"),"onUpdate:modelValue"(e){l.isColgroup=e}}):(0,Al.createCommentVNode)()]),(0,Al.h)("div",{class:"vxe-table-export--panel-option-row"},[D?(0,Al.h)(D,{modelValue:!t&&l.original,disabled:t,title:j("vxe.export.expOriginalTitle"),content:j("vxe.export.expOptOriginal"),"onUpdate:modelValue"(e){l.original=e}}):(0,Al.createCommentVNode)(),D?(0,Al.h)(D,{modelValue:!!(s&&v&&g)&&l.isMerge,title:j("vxe.export.expMergeTitle"),disabled:t||!s||!v||!g,content:j("vxe.export.expOptMerge"),"onUpdate:modelValue"(e){l.isMerge=e}}):(0,Al.createCommentVNode)(),d||!D?(0,Al.createCommentVNode)():(0,Al.h)(D,{modelValue:!!f&&l.useStyle,disabled:!f,title:j("vxe.export.expUseStyleTitle"),content:j("vxe.export.expOptUseStyle"),"onUpdate:modelValue"(e){l.useStyle=e}}),D?(0,Al.h)(D,{modelValue:!!i&&l.isAllExpand,disabled:t||!i,title:j("vxe.export.expAllExpandTitle"),content:j("vxe.export.expOptAllExpand"),"onUpdate:modelValue"(e){l.isAllExpand=e}}):(0,Al.createCommentVNode)()]),(0,Al.h)("div",{class:"vxe-table-export--panel-option-row"},[D?(0,Al.h)(D,{modelValue:l.isFooter,disabled:!o.hasFooter,title:j("vxe.export.expFooterTitle"),content:j("vxe.export.expOptFooter"),"onUpdate:modelValue"(e){l.isFooter=e}}):(0,Al.createCommentVNode)()])])])]])])]),b?(0,Al.h)("div",{class:"vxe-table-export--panel-bottom"},I.callSlot(b,e)):(0,Al.createCommentVNode)()])},footer(){var e={$table:I,$grid:r,options:l,columns:u,params:l.params};return(0,Al.h)("div",{class:"vxe-table-export--panel-footer"},t?I.callSlot(t,e):[(0,Al.h)("div",{class:"vxe-table-export--panel-btns"},[E?(0,Al.h)(E,{content:j("vxe.export.expCancel"),onClick:N}):(0,Al.createCommentVNode)(),E?(0,Al.h)(E,{ref:M,status:"primary",content:j(d?"vxe.export.expPrint":"vxe.export.expConfirm"),onClick:z}):(0,Al.createCommentVNode)()])])}}):(0,Al.createCommentVNode)()}}});let Ee=Dl.VxeUI.getIcon;var Ro=Tt({name:"VxeTableMenuPanel",setup(e,t){var r=Il().uniqueId();let d=(0,Al.inject)("$xeTable",{}),l=d.reactData,o=(0,Al.ref)(),a={refElem:o};r={xID:r,props:e,context:t,getRefMaps:()=>a};return r.renderVN=()=>{let s=l.ctxMenuStore;var e=d.getComputeMaps().computeMenuOpts,e=e.value;return(0,Al.h)(Al.Teleport,{to:"body",disabled:!1},[(0,Al.h)("div",{ref:o,class:["vxe-table--context-menu-wrapper",e.className,{"is--visible":s.visible}],style:s.style},s.list.map((e,i)=>e.every(e=>!1===e.visible)?(0,Al.createCommentVNode)():(0,Al.h)("ul",{class:"vxe-context-menu--option-wrapper",key:i},e.map((a,n)=>{var e=a.children&&a.children.some(e=>!1!==e.visible),t=Object.assign({},a.prefixConfig),r=Object.assign({},a.suffixConfig),l=Ml(a.name);return!1===a.visible?null:(0,Al.h)("li",{class:[a.className,{"link--disabled":a.disabled,"link--active":a===s.selected}],key:i+"_"+n},[(0,Al.h)("a",{class:"vxe-context-menu--link",onClick(e){d.ctxMenuLinkEvent(e,a)},onMouseover(e){d.ctxMenuMouseoverEvent(e,a)},onMouseout(e){d.ctxMenuMouseoutEvent(e,a)}},[(0,Al.h)("div",{class:["vxe-context-menu--link-prefix",t.className||""]},[(0,Al.h)("i",{class:t.icon||a.prefixIcon}),t.content?(0,Al.h)("span",{},""+t.content):(0,Al.createCommentVNode)()]),(0,Al.h)("div",{class:"vxe-context-menu--link-content",title:l},l),(0,Al.h)("div",{class:["vxe-context-menu--link-suffix",r.className||""]},[(0,Al.h)("i",{class:r.icon||a.suffixIcon||(e?Ee().TABLE_MENU_OPTIONS:"")}),r.content?(0,Al.h)("span",""+r.content):(0,Al.createCommentVNode)()])]),e?(0,Al.h)("ul",{class:["vxe-table--context-menu-clild-wrapper",{"is--show":a===s.selected&&s.showChild}]},a.children.map((t,e)=>{var r=Object.assign({},t.prefixConfig),l=Object.assign({},t.suffixConfig),o=Ml(t.name);return!1===t.visible?null:(0,Al.h)("li",{class:[t.className,{"link--disabled":t.disabled,"link--active":t===s.selectChild}],key:i+`_${n}_`+e},[(0,Al.h)("a",{class:"vxe-context-menu--link",onClick(e){d.ctxMenuLinkEvent(e,t)},onMouseover(e){d.ctxMenuMouseoverEvent(e,a,t)},onMouseout(e){d.ctxMenuMouseoutEvent(e,a)}},[(0,Al.h)("div",{class:["vxe-context-menu--link-prefix",r.className||""]},[(0,Al.h)("i",{class:r.icon||t.prefixIcon}),r.content?(0,Al.h)("span",""+r.content):(0,Al.createCommentVNode)()]),(0,Al.h)("div",{class:"vxe-context-menu--link-content",title:o},o),(0,Al.h)("div",{class:["vxe-context-menu--link-suffix",l.className||""]},[(0,Al.h)("i",{class:l.icon}),l.content?(0,Al.h)("span",""+l.content):(0,Al.createCommentVNode)()])])])})):null])}))))])},r},render(){return this.renderVN()}});let{getConfig:hl,getIcon:xl,getI18n:gl,renderer:bl,formats:Cl,createEvent:wl,globalResize:yl,interceptor:Tl,hooks:El,globalEvents:ml,GLOBAL_EVENT_KEYS:vl,useFns:Rl,renderEmptyElement:fl}=Dl.VxeUI,Sl="VXE_CUSTOM_STORE";var zr=Tt({name:"VxeTable",props:Co,emits:Nr,setup(j,e){let{slots:de,emit:l}=e,ce=Il().uniqueId(),ue=Il().browse(),pe=Dl.VxeUI.getComponent("VxeLoading"),he=Dl.VxeUI.getComponent("VxeTooltip"),t=(0,Al.inject)("$xeTabs",null),ge=Rl.useSize(j).computeSize,U=(0,Al.reactive)({staticColumns:[],tableGroupColumn:[],tableColumn:[],tableData:[],scrollXLoad:!1,scrollYLoad:!1,overflowY:!0,overflowX:!1,scrollbarWidth:0,scrollbarHeight:0,lastScrollTime:0,rowHeight:0,parentHeight:0,isGroup:!1,isAllOverflow:!1,isAllSelected:!1,isIndeterminate:!1,currentRow:null,currentColumn:null,selectRadioRow:null,footerTableData:[],rowGroupColumn:null,expandColumn:null,treeNodeColumn:null,hasFixedColumn:!1,upDataFlag:0,reColumnFlag:0,initStore:{filter:!1,import:!1,export:!1,custom:!1},customStore:{btnEl:null,isAll:!1,isIndeterminate:!1,activeBtn:!1,activeWrapper:!1,visible:!1,maxHeight:0,oldSortMaps:{},oldFixedMaps:{},oldVisibleMaps:{}},customColumnList:[],filterStore:{isAllSelected:!1,isIndeterminate:!1,style:null,options:[],column:null,multiple:!1,visible:!1,maxHeight:null},columnStore:{leftList:[],centerList:[],rightList:[],resizeList:[],pxList:[],pxMinList:[],autoMinList:[],scaleList:[],scaleMinList:[],autoList:[],remainList:[]},ctxMenuStore:{selected:null,visible:!1,showChild:!1,selectChild:null,list:[],style:null},editStore:{indexs:{columns:[]},titles:{columns:[]},selected:{row:null,column:null},copyed:{cut:!1,rows:[],columns:[]},actived:{row:null,column:null},focused:{row:null,column:null}},tooltipStore:{row:null,column:null,content:null,visible:!1,currOpts:{}},validStore:{visible:!1},validErrorMaps:{},importStore:{inited:!1,file:null,type:"",modeList:[],typeList:[],filename:"",visible:!1},importParams:{mode:"",types:null,message:!0},exportStore:{inited:!1,name:"",modeList:[],typeList:[],columns:[],isPrint:!1,hasFooter:!1,hasMerge:!1,hasTree:!1,hasColgroup:!1,visible:!1},exportParams:{filename:"",sheetName:"",mode:"",type:"",isColgroup:!1,isMerge:!1,isAllExpand:!1,useStyle:!1,original:!1,message:!0,isHeader:!1,isTitle:!1,isFooter:!1},visiblwRowsFlag:1,isRowGroupStatus:!1,rowGroupList:[],aggHandleFields:[],rowGroupExpandedFlag:1,rowExpandedFlag:1,treeExpandedFlag:1,updateCheckboxFlag:1,pendingRowFlag:1,insertRowFlag:1,removeRowFlag:1,mergeBodyFlag:1,mergeFootFlag:1,rowHeightStore:{large:52,default:48,medium:44,small:40,mini:36},scrollVMLoading:!1,scrollYHeight:0,scrollYTop:0,isScrollYBig:!1,scrollXLeft:0,scrollXWidth:0,isScrollXBig:!1,rowExpandHeightFlag:1,calcCellHeightFlag:1,resizeHeightFlag:1,resizeWidthFlag:1,isCustomStatus:!1,isDragRowMove:!1,dragRow:null,isDragColMove:!1,dragCol:null,dragTipText:"",isDragResize:!1,isRowLoading:!1,isColLoading:!1}),N={tZindex:0,currKeyField:"",isCurrDeepKey:!1,elemStore:{},scrollXStore:{preloadSize:0,offsetSize:0,visibleSize:0,visibleStartIndex:0,visibleEndIndex:0,startIndex:0,endIndex:0},scrollYStore:{preloadSize:0,offsetSize:0,visibleSize:0,visibleStartIndex:0,visibleEndIndex:0,startIndex:0,endIndex:0},tableWidth:0,tableHeight:0,headerHeight:0,footerHeight:0,customHeight:0,customMinHeight:0,customMaxHeight:0,hoverRow:null,lastScrollLeft:0,lastScrollTop:0,radioReserveRow:null,checkboxReserveRowMap:{},rowExpandedReserveRowMap:{},treeExpandedReserveRowMap:{},treeIndeterminateRowMaps:{},tableFullData:[],afterFullData:[],afterTreeFullData:[],afterGroupFullData:[],afterFullRowMaps:{},tableFullTreeData:[],tableFullGroupData:[],tableSynchData:[],tableSourceData:[],collectColumn:[],tableFullColumn:[],visibleColumn:[],fullAllDataRowIdData:{},fullDataRowIdData:{},visibleDataRowIdData:{},sourceDataRowIdData:{},fullColumnIdData:{},fullColumnFieldData:{},mergeBodyList:[],mergeBodyMaps:{},mergeFooterList:[],mergeFooterMaps:{},mergeBodyCellMaps:{},mergeFooterCellMaps:{},rowExpandedMaps:{},rowExpandLazyLoadedMaps:{},rowGroupExpandedMaps:{},treeExpandedMaps:{},treeExpandLazyLoadedMaps:{},selectCheckboxMaps:{},pendingRowMaps:{},insertRowMaps:{},removeRowMaps:{},cvCacheMaps:{},inited:!1,tooltipTimeout:null,initStatus:!1,isActivated:!1},z={},W={},G=(0,Al.ref)(),me=(0,Al.ref)(),q=(0,Al.ref)(),ve=(0,Al.ref)(),fe=(0,Al.ref)(),xe=(0,Al.ref)(),be=(0,Al.ref)(),Ce=(0,Al.ref)(),d=(0,Al.ref)(),c=(0,Al.ref)(),f=(0,Al.ref)(),u=(0,Al.ref)(),p=(0,Al.ref)(),h=(0,Al.ref)(),g=(0,Al.ref)(),m=(0,Al.ref)(),v=(0,Al.ref)(),x=(0,Al.ref)(),P=(0,Al.ref)(),X=(0,Al.ref)(),we=(0,Al.ref)(),ye=(0,Al.ref)(),Te=(0,Al.ref)(),Ee=(0,Al.ref)(),Re=(0,Al.ref)(),Se=(0,Al.ref)(),K=(0,Al.ref)(),V=(0,Al.ref)(),De=(0,Al.ref)(),Ie=(0,Al.ref)(),S=(0,Al.ref)(),Fe=(0,Al.ref)(),Me=(0,Al.ref)(),D=(0,Al.ref)(),ke=(0,Al.ref)(),Oe=(0,Al.ref)(),Ae=(0,Al.ref)(),Le=(0,Al.ref)(),C=(0,Al.ref)(),$=(0,Al.ref)(),Y=(0,Al.inject)("$xeGrid",null),y,T=(0,Al.computed)(()=>{var e=j.id;return e?Il().isFunction(e)?""+(e({$table:ie,$grid:Y})||""):""+e:""}),r=(0,Al.computed)(()=>{var e=Q.value;return""+(j.rowId||e.keyField||"_X_ROW_KEY")}),Ve=(0,Al.computed)(()=>Object.assign({},hl().table.validConfig,j.validConfig));var o=(0,Al.computed)(()=>w.value);let _=(0,Al.computed)(()=>{var e=w.value.threshold;return e?Il().toNumber(e):0});var a=(0,Al.computed)(()=>R.value);let w=(0,Al.computed)(()=>Object.assign({},hl().table.virtualXConfig||hl().table.scrollX,j.virtualXConfig||j.scrollX)),R=(0,Al.computed)(()=>Object.assign({},hl().table.virtualYConfig||hl().table.scrollY,j.virtualYConfig||j.scrollY)),E=(0,Al.computed)(()=>Object.assign({},hl().table.scrollbarConfig,j.scrollbarConfig)),$e=(0,Al.computed)(()=>{var e=E.value;return!(!e.x||"top"!==e.x.position)}),_e=(0,Al.computed)(()=>{var e=E.value;return!(!e.y||"left"!==e.y.position)}),H=(0,Al.computed)(()=>{var e=R.value.threshold;return e?Il().toNumber(e):0}),n=(0,Al.computed)(()=>U.rowHeightStore),I=(0,Al.computed)(()=>{var e=ge.value;return n.value[e||"default"]||18}),Z=(0,Al.computed)(()=>Object.assign({},hl().table.columnConfig,j.columnConfig)),B=(0,Al.computed)(()=>Object.assign({},hl().table.currentColumnConfig,j.currentColumnConfig)),F=(0,Al.computed)(()=>{var e=Object.assign({},hl().table.cellConfig,j.cellConfig);return e.height&&(e.height=Il().toNumber(e.height)),e});var s=(0,Al.computed)(()=>{var e=Object.assign({},hl().table.headerCellConfig,j.headerCellConfig),t=F.value;return e.height=Il().toNumber(uo(e.height||t.height)),e}),He=(0,Al.computed)(()=>{var e=Object.assign({},hl().table.footerCellConfig,j.footerCellConfig),t=F.value;return e.height=Il().toNumber(uo(e.height||t.height)),e});let Q=(0,Al.computed)(()=>Object.assign({},hl().table.rowConfig,j.rowConfig)),M=(0,Al.computed)(()=>Object.assign({},hl().table.aggregateConfig||hl().table.rowGroupConfig,j.aggregateConfig||j.rowGroupConfig)),Be=(0,Al.computed)(()=>M.value),Pe=(0,Al.computed)(()=>Object.assign({},hl().table.currentRowConfig,j.currentRowConfig)),J=(0,Al.computed)(()=>Object.assign({},hl().table.rowDragConfig,j.rowDragConfig)),ee=(0,Al.computed)(()=>Object.assign({},hl().table.columnDragConfig,j.columnDragConfig)),Ne=(0,Al.computed)(()=>Object.assign({},hl().table.resizeConfig,j.resizeConfig)),ze=(0,Al.computed)(()=>Object.assign({},hl().table.resizableConfig,j.resizableConfig));var je=(0,Al.computed)(()=>Object.assign({startIndex:0},hl().table.seqConfig,j.seqConfig));let k=(0,Al.computed)(()=>Object.assign({},hl().table.radioConfig,j.radioConfig)),te=(0,Al.computed)(()=>Object.assign({},hl().table.checkboxConfig,j.checkboxConfig)),Ue=(0,Al.computed)(()=>Object.assign({},hl().tooltip,hl().table.tooltipConfig,j.tooltipConfig)),We=(0,Al.computed)(()=>{var e=U.tooltipStore,t=Ue.value;return Object.assign({},t,e.currOpts)}),Ge=(0,Al.computed)(()=>{var e=Ue.value;return Object.assign({},e)}),re=(0,Al.computed)(()=>Object.assign({},hl().table.editConfig,j.editConfig)),O=(0,Al.computed)(()=>Object.assign({orders:["asc","desc",null]},hl().table.sortConfig,j.sortConfig)),qe=(0,Al.computed)(()=>Object.assign({},hl().table.filterConfig,j.filterConfig)),le=(0,Al.computed)(()=>Object.assign({},hl().table.mouseConfig,j.mouseConfig)),Xe=(0,Al.computed)(()=>Object.assign({},hl().table.areaConfig,j.areaConfig)),oe=(0,Al.computed)(()=>Object.assign({},hl().table.keyboardConfig,j.keyboardConfig));var Ke=(0,Al.computed)(()=>Object.assign({},hl().table.clipConfig,j.clipConfig)),Ye=(0,Al.computed)(()=>Object.assign({},hl().table.fnrConfig,j.fnrConfig));let Ze=(0,Al.computed)(()=>Object.assign({},hl().table.menuConfig,j.menuConfig)),Qe=(0,Al.computed)(()=>{var e=U.columnStore,t=e.leftList;let r=0;for(let e=0;e<t.length;e++){var l=t[e];r+=l.renderWidth}return r}),Je=(0,Al.computed)(()=>{var e=U.columnStore,t=e.rightList;let r=0;for(let e=0;e<t.length;e++){var l=t[e];r+=l.renderWidth}return r}),et=(0,Al.computed)(()=>{var e=Ze.value.header;return e&&e.options?e.options:[]}),tt=(0,Al.computed)(()=>{var e=Ze.value.body;return e&&e.options?e.options:[]}),rt=(0,Al.computed)(()=>{var e=Ze.value.footer;return e&&e.options?e.options:[]}),lt=(0,Al.computed)(()=>{var e=Ze.value,t=et.value,r=tt.value,l=rt.value;return!!(j.menuConfig&&Fl(e)&&(t.length||r.length||l.length))}),ot=(0,Al.computed)(()=>{var e=U.ctxMenuStore;let t=[];return e.list.forEach(e=>{e.forEach(e=>{t.push(e)})}),t}),at=(0,Al.computed)(()=>Object.assign({},hl().table.exportConfig,j.exportConfig)),nt=(0,Al.computed)(()=>Object.assign({},hl().table.importConfig,j.importConfig));var it=(0,Al.computed)(()=>Object.assign({},hl().table.printConfig,j.printConfig));let ae=(0,Al.computed)(()=>Object.assign({},hl().table.expandConfig,j.expandConfig)),ne=(0,Al.computed)(()=>Object.assign({},hl().table.treeConfig,j.treeConfig)),st=(0,Al.computed)(()=>Object.assign({},hl().table.emptyRender,j.emptyRender)),dt=(0,Al.computed)(()=>Object.assign({},hl().table.loadingConfig,j.loadingConfig));var ct=(0,Al.computed)(()=>j.border?Math.max(2,Math.ceil(U.scrollbarWidth/U.tableColumn.length)):1);let ut=(0,Al.computed)(()=>Object.assign({},hl().table.customConfig,j.customConfig)),pt=(0,Al.computed)(()=>{var{rowExpandedFlag:e,expandColumn:t,rowGroupExpandedFlag:r,treeExpandedFlag:l}=U;let{visibleDataRowIdData:o,rowExpandedMaps:a}=N,n=[];return t&&e&&r&&l&&Il().each(a,(e,t)=>{o[t]&&n.push(e)}),n}),ht=(0,Al.computed)(()=>{var e=N.visibleColumn,t=U.tableColumn;return t.length||e.length?e.filter(e=>"auto"===e.width||"auto"===e.minWidth):[]}),gt=(0,Al.computed)(()=>{var e=U.tableColumn,t=N.collectColumn;let r=0;return e.length&&t.length&&t.forEach(e=>{e.renderFixed&&r++}),r}),mt=(0,Al.computed)(()=>{var e=gt.value,t=Z.value.maxFixedSize;return!!t&&t<=e}),vt=(0,Al.computed)(()=>{var e=j.border;return!0===e?"full":e||"default"});var ft=(0,Al.computed)(()=>{var{}=j,e=U.tableData,t=N.tableFullData;let{strict:r,checkMethod:l}=te.value;return!!r&&(!e.length&&!t.length||!!l&&t.every(e=>!l({$table:ie,row:e})))}),xt=(0,Al.computed)(()=>{var{overflowX:e,scrollXLoad:t,overflowY:r,scrollYLoad:l}=U;return{x:e&&t,y:r&&l}}),bt=(0,Al.computed)(()=>Be.value.groupFields),Ct=(0,Al.computed)(()=>{var e=U.rowGroupList;let t=N.fullColumnFieldData,r=[];return e.forEach(e=>{e=t[e.field];e&&r.push(e.column)}),r});let wt={refElem:G,refTooltip:q,refValidTooltip:fe,refTableFilter:be,refTableCustom:Ce,refTableMenu:xe,refTableHeader:c,refTableBody:f,refTableFooter:u,refTableLeftHeader:p,refTableLeftBody:h,refTableLeftFooter:g,refTableRightHeader:m,refTableRightBody:v,refTableRightFooter:x,refLeftContainer:P,refRightContainer:X,refColResizeBar:we,refRowResizeBar:ye,refScrollXVirtualElem:De,refScrollYVirtualElem:Ie,refScrollXHandleElem:S,refScrollYHandleElem:D,refScrollXSpaceElem:C,refScrollYSpaceElem:$},yt={computeSize:ge,computeTableId:T,computeValidOpts:Ve,computeRowField:r,computeVirtualXOpts:w,computeVirtualYOpts:R,computeScrollbarOpts:E,computeScrollbarXToTop:$e,computeScrollbarYToLeft:_e,computeColumnOpts:Z,computeCurrentColumnOpts:B,computeScrollXThreshold:_,computeScrollYThreshold:H,computeRowHeightMaps:n,computeDefaultRowHeight:I,computeCellOpts:F,computeHeaderCellOpts:s,computeFooterCellOpts:He,computeRowOpts:Q,computeAggregateOpts:M,computeRowGroupOpts:Be,computeCurrentRowOpts:Pe,computeRowDragOpts:J,computeColumnDragOpts:ee,computeResizeOpts:Ne,computeResizableOpts:ze,computeSeqOpts:je,computeRadioOpts:k,computeCheckboxOpts:te,computeTooltipOpts:Ue,computeEditOpts:re,computeSortOpts:O,computeFilterOpts:qe,computeMouseOpts:le,computeAreaOpts:Xe,computeKeyboardOpts:oe,computeClipOpts:Ke,computeFNROpts:Ye,computeHeaderMenu:et,computeBodyMenu:tt,computeFooterMenu:rt,computeIsMenu:lt,computeMenuList:ot,computeMenuOpts:Ze,computeExportOpts:at,computeImportOpts:nt,computePrintOpts:it,computeExpandOpts:ae,computeTreeOpts:ne,computeEmptyOpts:st,computeLoadingOpts:dt,computeCellOffsetWidth:ct,computeCustomOpts:ut,computeLeftFixedWidth:Qe,computeRightFixedWidth:Je,computeFixedColumnSize:gt,computeIsMaxFixedColumn:mt,computeIsAllCheckboxDisabled:ft,computeVirtualScrollBars:xt,computeRowGroupFields:bt,computeRowGroupColumns:Ct,computeSXOpts:o,computeSYOpts:a},ie={xID:ce,props:j,context:e,reactData:U,internalData:N,getRefMaps:()=>wt,getComputeMaps:()=>yt,xeGrid:Y,xegrid:Y},Tt=(e,t,r)=>{e=Il().get(e,r),t=Il().get(t,r);return!(!Ol(e)||!Ol(t))||(Il().isString(e)||Il().isNumber(e)?""+e==""+t:Il().isEqual(e,t))},Et=()=>{var e=r.value;N.currKeyField=e,N.isCurrDeepKey=-1<e.indexOf(".")},b=(e,t)=>Il().isBoolean(e)?e:t,Rt=e=>{var{orders:t=[]}=O.value,e=e.order||null,e=t.indexOf(e)+1;return t[e<t.length?e:0]},St=e=>{var t=hl().version,r=Il().toStringJSON(localStorage.getItem(Sl)||""),r=r&&r._v===t?r:{_v:t};return(e?r[e]:r)||{}},Dt=e=>{let r=N.fullAllDataRowIdData,l={};return Il().each(e,(e,t)=>{r[t]&&(l[t]=e)}),l},It=e=>{let r=N.fullDataRowIdData,l=[];return Il().each(e,(e,t)=>{r[t]&&-1===ie.findRowIndexOf(l,r[t].row)&&l.push(r[t].row)}),l},Ft=()=>{var{isScrollXBig:o,scrollXWidth:a}=U,{elemStore:n,visibleColumn:i,fullColumnIdData:s}=N,d=Qe.value,c=Je.value,n=Wl(n["main-body-scroll"]);if(n){var u=n.clientWidth;let e=n.scrollLeft;var p=(e=o?Math.ceil((a-u)*Math.min(1,e/(5e6-u))):e)+d,h=e+u-c;let t=0,r=i.length;for(;t<r;){var g=Math.floor((t+r)/2);(s[i[g].id]||{}).oLeft<=p?t=g+1:r=g}let l=0;n=t===i.length?t:Math.max(0,t<i.length?t-2:0);for(let e=n,t=i.length;e<t;e++){var m=s[i[e].id]||{};if(l++,m.oLeft>h||60<=l)break}return{toVisibleIndex:Math.max(0,n),visibleSize:Math.max(1,l)}}return{toVisibleIndex:0,visibleSize:6}},Mt=(e,t)=>{var r=U.rowHeightStore;t&&t.clientHeight&&(r[e]=t.clientHeight)},kt=()=>{var t=U.isAllOverflow,r=c.value,l=f.value,l=l?l.$el:null,e=I.value;let o=0;if(t){if(l){t=r?r.$el:null;let e;(e=!(e=l.querySelector("tr"))&&t?t.querySelector("tr"):e)&&(o=e.clientHeight)}o=o||e}else o=e;return Math.max(18,o)},Ot=()=>{var{isAllOverflow:t,expandColumn:r,isScrollYBig:a,scrollYHeight:n}=U,{elemStore:i,isResizeCellHeight:s,afterFullData:d,fullAllDataRowIdData:c}=N,u=Q.value,p=F.value,h=I.value,i=Wl(i["main-body-scroll"]);if(i){var g=i.clientHeight;let e=i.scrollTop;var m=e=a?Math.ceil((n-g)*Math.min(1,e/(5e6-g))):e,v=e+g;let l=-1,o=0;if(s||p.height||u.height||r||!t){var f=Nl(ie).handleGetRowId;let r=0,e=d.length;for(;r<e;){var x=Math.floor((r+e)/2);(c[f(d[x])]||{}).oTop<=m?r=x+1:e=x}for(let e=l=r===d.length?r:Math.max(0,r<d.length?r-2:0),t=d.length;e<t;e++){var b=c[f(d[e])]||{};if(o++,b.oTop>v||100<=o)break}}else l=Math.floor(m/h)-1,o=Math.ceil(g/h)+1;return{toVisibleIndex:Math.max(0,l),visibleSize:Math.max(6,o)}}return{toVisibleIndex:0,visibleSize:6}},At=(r,l,o)=>{for(let e=0,t=r.length;e<t;e++){var a=r[e],{startIndex:n,endIndex:i}=l,s=a[o],a=s+a[o+"span"];s<n&&n<a&&(l.startIndex=s),s<i&&i<a&&(l.endIndex=a),l.startIndex===n&&l.endIndex===i||(e=-1)}};function Lt(t){var r={};if(t&&t.length)for(let e=0;e<t.length;e++){var{row:l,col:o,rowspan:a,colspan:n}=t[e];for(let t=0;t<a;t++)for(let e=0;e<n;e++)r[l+t+":"+(o+e)]=t||e?{rowspan:0,colspan:0}:{rowspan:a,colspan:n}}return r}let Vt=(e,t,r)=>{let{multiple:l,remote:o,orders:n}=O.value;if((t=Il().isArray(t)?t:[t])&&t.length){l||(t=[t[0]],$t());let a=null;return t.forEach((e,t)=>{let{field:r,order:l}=e,o=r;Il().isString(r)&&(o=ie.getColumnByField(r)),a=a||o,o&&o.sortable&&(n&&-1===n.indexOf(l)&&(l=Rt(o)),o.order!==l&&(o.order=l),o.sortTime=Date.now()+t)}),r&&!o&&ie.handleTableData(!0),e&&ie.handleColumnSortEvent(e,a),(0,Al.nextTick)().then(()=>(L(),ie.updateCellAreas(),A()))}return(0,Al.nextTick)()},$t=()=>{var e=N.tableFullColumn;e.forEach(e=>{e.order=null})},_t=e=>{var t,r=U.parentHeight,e=j[e];let l=0;return l=e?"100%"===e||"auto"===e?r:(t=ie.getExcludeHeight(),l=Jl(e)?Math.floor((Il().toInteger(e)||1)/100*r):Il().toNumber(e),Math.max(40,l-t)):l},Ht=e=>{var t=N.collectColumn;let{resizableData:n,sortData:i,visibleData:s,fixedData:d}=e,c=!1;n||i||s||d?(Il().eachTree(t,(e,t,r,l,o)=>{var a=e.getKey();o||(d&&void 0!==d[a]&&(e.fixed=d[a]),i&&Il().isNumber(i[a])&&(c=!0,e.renderSortNumber=i[a])),n&&Il().isNumber(n[a])&&(e.resizeWidth=n[a]),s&&Il().isBoolean(s[a])&&(e.visible=s[a])}),c&&(t=Il().orderBy(t,"renderSortNumber"),N.collectColumn=t,N.tableFullColumn=fr(t)),U.isCustomStatus=!0):U.isCustomStatus=!1},Bt=()=>{var{tableFullColumn:e,collectColumn:t}=N;let u=N.fullColumnIdData={},p=N.fullColumnFieldData={};var r=le.value,l=ae.value;let h=Z.value;var o=ee.value,a=R.value;let{isCrossDrag:g,isSelfToChildDrag:m}=o,v=ut.value.storage;var o=Q.value,n=t.some(Kl);let f=!!j.showOverflow,x,b,C,w,y,T,E,i=(e,t,r,l,o)=>{var{id:a,field:n,fixed:i,type:s,treeNode:d,rowGroupNode:c}=e,t={$index:-1,_index:-1,column:e,colid:a,index:t,items:r,parent:o||null,width:0,oLeft:0};n?(p[n]&&pl("vxe.error.colRepet",["field",n]),p[n]=t):(v&&!s||h.drag&&(g||m))&&pl("vxe.error.reqProp",[`${e.getTitle()||s||""} -> column.field=?`]),!E&&i&&(E=i),T||"html"!==s||(T=e),d&&(C&&ul("vxe.error.colRepet",["tree-node",d]),C=C||e),c&&(C&&ul("vxe.error.colRepet",["row-group-node",c]),x=x||e),"expand"===s&&(b&&ul("vxe.error.colRepet",["type",s]),b=b||e),"checkbox"===s?(w&&ul("vxe.error.colRepet",["type",s]),w=w||e):"radio"===s&&(y&&ul("vxe.error.colRepet",["type",s]),y=y||e),f&&!1===e.showOverflow&&(f=!1),u[a]&&pl("vxe.error.colRepet",["colId",a]),u[a]=t};n?Il().eachTree(t,(e,t,r,l,o,a)=>{e.level=a.length,i(e,t,r,0,o)}):e.forEach(i),b&&"fixed"!==l.mode&&a.enabled&&ul("vxe.error.notConflictProp",['column.type="expand',"virtual-y-config.enabled=false"]),b&&"fixed"!==l.mode&&r.area&&pl("vxe.error.errConflicts",["mouse-config.area","column.type=expand"]),T&&(h.useKey||pl("vxe.error.reqProp",["column-config.useKey & column.type=html"]),o.useKey||pl("vxe.error.reqProp",["row-config.useKey & column.type=html"])),U.isGroup=n,U.rowGroupColumn=x,U.treeNodeColumn=C,U.expandColumn=b,U.isAllOverflow=f},Pt=()=>{N.customHeight=_t("height"),N.customMinHeight=_t("minHeight"),N.customMaxHeight=_t("maxHeight"),!U.scrollYLoad||N.customHeight||N.customMinHeight||(N.customHeight=300)},Nt=(e,t)=>{var r=t.querySelectorAll(`.vxe-cell--wrapper[colid="${e.id}"]`);let l=0;var t=r[0];t&&t.parentElement&&(t=getComputedStyle(t.parentElement),l=Math.ceil(Il().toNumber(t.paddingLeft)+Il().toNumber(t.paddingRight)));let o=e.renderAutoWidth-l;for(let e=0;e<r.length;e++){var a=r[e];o=Math.max(o,a?Math.ceil(a.scrollWidth)+4:0)}return o+l},zt=()=>{var e=ht.value;let l=N.fullColumnIdData,o=G.value;o&&(o.setAttribute("data-calc-col","Y"),e.forEach(e=>{var t=e.id,t=l[t],r=Nt(e,o);t&&(t.width=Math.max(r,t.width)),e.renderAutoWidth=r}),ie.analyColumnWidth(),o.removeAttribute("data-calc-col"))},jt=()=>{var e=N.elemStore,e=Wl(e["main-body-wrapper"]);if(e){var o=D.value;if(o)if(S.value){let r=0;o=e.clientWidth,e=o;let l=e/100;var t=j.fit,a=U.columnStore,{resizeList:a,pxMinList:n,autoMinList:i,pxList:s,scaleList:d,scaleMinList:c,autoList:u,remainList:p}=a;if(n.forEach(e=>{var t=Il().toInteger(e.minWidth);r+=t,e.renderWidth=t}),i.forEach(e=>{var t=Math.max(60,Il().toInteger(e.renderAutoWidth));r+=t,e.renderWidth=t}),c.forEach(e=>{var t=Math.floor(Il().toInteger(e.minWidth)*l);r+=t,e.renderWidth=t}),d.forEach(e=>{var t=Math.floor(Il().toInteger(e.width)*l);r+=t,e.renderWidth=t}),s.forEach(e=>{var t=Il().toInteger(e.width);r+=t,e.renderWidth=t}),u.forEach(e=>{var t=Math.max(60,Il().toInteger(e.renderAutoWidth));r+=t,e.renderWidth=t}),a.forEach(e=>{var t=Il().toInteger(e.resizeWidth);r+=t,e.renderWidth=t}),e-=r,l=0<e?Math.floor(e/(c.length+n.length+i.length+p.length)):0,t?0<e&&c.concat(n).concat(i).forEach(e=>{r+=l,e.renderWidth+=l}):l=40,p.forEach(e=>{var t=Math.max(l,40);e.renderWidth=t,r+=t}),t){var h=d.concat(c).concat(n).concat(i).concat(p);let t=h.length-1;if(0<t){let e=o-r;if(0<e){for(;0<e&&0<=t;)e--,h[t--].renderWidth++;r=o}}}U.scrollXWidth=r,U.resizeWidthFlag++,Fr(),Pt()}}},Ut=(e,t)=>{var r=t.querySelectorAll(`.vxe-cell--wrapper[rowid="${e.rowid}"]`);let l=e.height;for(let e=0;e<r.length;e++){var o=r[e],a=o.parentElement,a=Math.ceil(Il().toNumber(a.style.paddingTop)+Il().toNumber(a.style.paddingBottom)),o=o?o.clientHeight:0;l=Math.max(l-a,Math.ceil(o))}return l},Wt=()=>{let{tableData:e,isAllOverflow:t,scrollYLoad:r,scrollXLoad:l}=U,o=N.fullAllDataRowIdData,a=I.value,n=G.value;if(!t&&r&&n){let r=Nl(ie).handleGetRowId;n.setAttribute("data-calc-row","Y"),e.forEach(e=>{var t,e=r(e),e=o[e];e&&(t=Ut(e,n),e.height=Math.max(a,l?Math.max(e.height,t):t)),n.removeAttribute("data-calc-row")}),U.calcCellHeightFlag++}},Gt=r=>{let{sortBy:l,sortType:o}=r;return e=>{let t;return t=l?Il().isFunction(l)?l({row:e,column:r}):Il().get(e,l):z.getCellLabel(e,r),o&&"auto"!==o?"number"===o?Il().toNumber(t):"string"===o?Il().toValueString(t):t:isNaN(t)?t:Il().toNumber(t)}},qt=()=>{let l=j.treeConfig,{fullDataRowIdData:i,fullAllDataRowIdData:s,afterFullData:e,afterTreeFullData:t}=N;var r=ne.value,o=r.transform,a=r.children||r.childrenField;let d={};if(l){let n=Nl(ie).handleGetRowId;Il().eachTree(t,(e,t,r,l)=>{var o=n(e),a=s[o],l=l.map((e,t)=>t%2==0?Number(e)+1:".").join("");a?(a.seq=l,a.treeIndex=t):(a={row:e,rowid:o,seq:l,index:-1,$index:-1,_index:-1,treeIndex:-1,items:[],parent:null,level:0,height:0,resizeHeight:0,oTop:0,expandHeight:0},s[o]=a,i[o]=a),d[o]=e},{children:o?r.mapChildrenField:a}),o&&e.forEach((e,t)=>{var e=n(e),e=s[e],r=t+1;e&&(l||(e.seq=r),e._index=t)}),N.afterFullRowMaps=d}else(()=>{let a=j.treeConfig,{afterFullData:e,fullDataRowIdData:n,fullAllDataRowIdData:i}=N,s=Nl(ie).handleGetRowId,d={};e.forEach((e,t)=>{var r=s(e),l=i[r],o=t+1;l?(a||(l.seq=o),l._index=t):(l={row:e,rowid:r,seq:o,index:-1,$index:-1,_index:t,treeIndex:-1,items:[],parent:null,level:0,height:0,resizeHeight:0,oTop:0,expandHeight:0},i[r]=l,n[r]=l),d[r]=e}),N.afterFullRowMaps=d})()},i=()=>{var e=j.treeConfig,t=U.isRowGroupStatus;let{fullAllDataRowIdData:i,treeExpandedMaps:s,rowGroupExpandedMaps:d}=N;var r=M.value,l=ne.value;let c=Nl(ie).handleGetRowId,u=[],p={};return e&&l.transform?(e=l.children||l.childrenField,Il().eachTree(N.afterTreeFullData,(e,t,r,l,o)=>{var a=c(e),n=c(o);(!o||p[n]&&s[n])&&((o=i[a])&&(o._index=u.length),p[a]=1,u.push(e))},{children:e}),N.afterFullData=u,wr(u),u):t?(l=r.childrenField,Il().eachTree(N.afterGroupFullData,(e,t,r,l,o)=>{var a=c(e),n=c(o);(!o||p[n]&&d[n])&&((o=i[a])&&(o._index=u.length),p[a]=1,u.push(e))},{children:l}),N.afterFullData=u,wr(u),u):N.afterFullData},A=()=>{let{showHeaderOverflow:x,showFooterOverflow:b,mouseConfig:e,spanMethod:C,footerSpanMethod:w}=j,{isGroup:y,currentRow:t,tableColumn:T,scrollXLoad:E,scrollYLoad:R,overflowX:S,scrollbarWidth:r,overflowY:l,scrollbarHeight:o,scrollXWidth:D,columnStore:a,editStore:n,isAllOverflow:I,expandColumn:F,isColLoading:M}=U,{visibleColumn:k,tableHeight:O,headerHeight:A,footerHeight:L,elemStore:V,customHeight:$,customMinHeight:i,customMaxHeight:_}=N;var s=G.value;if(s){let p=l?r:0,h=S?o:0;var d=Te.value,c=le.value;let g=ae.value;var u=Wl(V["main-body-wrapper"]),H=Wl(V["main-body-table"]);d&&(d.style.top=A+"px",d.style.height=u?u.offsetHeight-h+"px":"");let m=0,v=0,f=i-A-L-h;_&&(v=Math.max(f,_-A-L-h)),(m=$?$-A-L-h:m)||H&&(m=H.clientHeight),m&&(v&&(m=Math.min(v,m)),m=Math.max(f,m));var d=$e.value,u=Fe.value,H=Me.value,B=De.value,B=(B&&(B.style.height=h+"px",B.style.visibility=S?"visible":"hidden"),Oe.value),B=(B&&(B.style.left=d?p+"px":"",B.style.width=s.clientWidth-p+"px"),u&&(u.style.width=d?p+"px":"",u.style.display=d&&S&&h?"block":""),H&&(H.style.width=d?"":p+"px",H.style.display=!d&&S&&h?"block":""),Ie.value),s=(B&&(B.style.width=p+"px",B.style.height=m+A+L+"px",B.style.visibility=l?"visible":"hidden"),ke.value),u=(s&&(s.style.height=A+"px",s.style.display=l&&A?"block":""),Ae.value),H=(u&&(u.style.height=m+"px",u.style.top=A+"px"),Le.value),d=(H&&(H.style.height=L+"px",H.style.top=A+m+"px",H.style.display=l&&L?"block":""),K.value);return d&&(d.style.height=m+"px",d.style.top=A+"px"),["main","left","right"].forEach((s,e)=>{let d=0<e?s:"";e="left"===d;let c=[],u;d&&(c=e?a.leftList:a.rightList,u=(e?P:X).value),["header","body","footer"].forEach(r=>{var l=Wl(V[s+`-${r}-wrapper`]),o=Wl(V[s+`-${r}-scroll`]),a=Wl(V[s+`-${r}-table`]);if("header"===r){let e=T,t=!1;y?e=k:(E&&x&&(C||w||(t=!0)),t&&(M||!d&&S)||(e=k),d&&t&&(e=c||[]));var n=e.reduce((e,t)=>e+t.renderWidth,0);d&&(!y&&t?l&&(l.style.width=n?n+"px":""):l&&(l.style.width=D?D+"px":"")),o&&(o.style.height=A+"px"),a&&(a.style.width=n?n+"px":"")}else if("body"===r){o&&(o.style.maxHeight=_?v+"px":"",o.style.height=$?m+"px":"",o.style.minHeight=f+"px"),u&&(l&&(l.style.top=A+"px"),u.style.height=`${0<$?$:O+A+L+h}px`,u.style.width=c.reduce((e,t)=>e+t.renderWidth,0)+"px");let e=T,t=!1;!(E||R||I)||F&&"fixed"!==g.mode||C||w||(t=!0);var n=(e=d&&(e=k,t)?c||[]:e).reduce((e,t)=>e+t.renderWidth,0),i=(d&&(t?l&&(l.style.width=n?n+"px":""):l&&(l.style.width=D?D+"px":"")),a&&(a.style.width=n?n+"px":"",a.style.paddingRight=p&&d&&(ue.firefox||ue.safari)?p+"px":""),Wl(V[s+`-${r}-emptyBlock`]));i&&(i.style.width=n?n+"px":"")}else if("footer"===r){let e=T,t=!1;E&&b&&(C||w||(t=!0)),t&&(M||!d&&S)||(e=k);i=(e=d&&t?c||[]:e).reduce((e,t)=>e+t.renderWidth,0);d&&(t?l&&(l.style.width=i?i+"px":""):l&&(l.style.width=D?D+"px":"")),o&&(o.style.height=L+"px",u)&&l&&(l.style.top=`${0<$?$-L-h:O+A}px`),a&&(a.style.width=i?i+"px":"")}})}),t&&ie.setCurrentRow(t),e&&c.selected&&n.selected.row&&n.selected.column&&ie.addCellSelectedClass(),(0,Al.nextTick)()}},Xt=e=>ie.triggerValidate?ie.triggerValidate(e):(0,Al.nextTick)(),Kt=(e,t)=>{Xt("blur").catch(e=>e).then(()=>{ie.handleEdit(t,e).then(()=>Xt("change")).catch(e=>e)})},Yt=e=>{k.value.reserve&&(N.radioReserveRow=e)},Zt=(e,t)=>{var r,l=N.checkboxReserveRowMap;te.value.reserve&&(r=Pl(ie,e),t?l[r]=e:l[r]&&delete l[r])},Qt=(e,t)=>{var r=k.value.checkMethod;return e&&(t||!r||r({$table:ie,row:e}))&&(U.selectRadioRow=e,Yt(e)),(0,Al.nextTick)()},Jt=(e,t,r)=>(e&&!Il().isArray(e)&&(e=[e]),ie.handleBatchSelectRows(e,!!t,r),ie.checkSelectionStatus(),(0,Al.nextTick)()),er=(t,r)=>{let l=j.treeConfig,o=U.isRowGroupStatus,{afterFullData:e,afterTreeFullData:a,afterGroupFullData:n,checkboxReserveRowMap:i,selectCheckboxMaps:s}=N;var d=ne.value,c=M.value,d=d.children||d.childrenField,u=te.value;let{checkField:p,reserve:h,checkMethod:g}=u,m=Nl(ie).handleGetRowId,v=u.indeterminateField||u.halfField,f={};return p?(u=e=>{!r&&g&&!g({$table:ie,row:e})||(t&&(f[m(e)]=e),Il().set(e,p,t)),(l||o)&&v&&Il().set(e,v,!1)},l||o?Il().eachTree(e,u,{children:d}):e.forEach(u)):o?t?Il().eachTree(n,e=>{var t;!r&&g&&!g({$table:ie,row:e})||(t=m(e),f[t]=e)},{children:c.mapChildrenField}):!r&&g&&Il().eachTree(n,e=>{var t=m(e);!g({$table:ie,row:e})&&s[t]&&(f[t]=e)},{children:c.mapChildrenField}):l?t?Il().eachTree(a,e=>{var t;!r&&g&&!g({$table:ie,row:e})||(t=m(e),f[t]=e)},{children:d}):!r&&g&&Il().eachTree(a,e=>{var t=m(e);!g({$table:ie,row:e})&&s[t]&&(f[t]=e)},{children:d}):t?!r&&g?e.forEach(e=>{var t=m(e);(s[t]||g({$table:ie,row:e}))&&(f[t]=e)}):e.forEach(e=>{var t=m(e);f[t]=e}):!r&&g&&e.forEach(e=>{var t=m(e);!g({$table:ie,row:e})&&s[t]&&(f[t]=e)}),h&&(t?Il().each(f,(e,t)=>{i[t]=e}):e.forEach(e=>Zt(e,!1))),U.updateCheckboxFlag++,N.selectCheckboxMaps=p?{}:f,U.isAllSelected=t,U.isIndeterminate=!1,N.treeIndeterminateRowMaps={},ie.checkSelectionStatus(),(0,Al.nextTick)()},tr=a=>{var e=ne.value,t=te.value;let{transform:n,loadMethod:i}=e,s=t.checkStrictly;return new Promise(o=>{if(i){let{fullAllDataRowIdData:e,treeExpandLazyLoadedMaps:t}=N,r=Pl(ie,a),l=e[r];t[r]=a,Promise.resolve(i({$table:ie,row:a})).then(e=>{if(l&&(l.treeLoaded=!0),t[r]&&delete t[r],e=Il().isArray(e)?e:[])return ie.loadTreeChildren(a,e).then(e=>{var t=N.treeExpandedMaps;return e.length&&!t[r]&&(t[r]=a),U.treeExpandedFlag++,!s&&ie.isCheckedByCheckboxRow(a)&&Jt(e,!0),(0,Al.nextTick)().then(()=>{if(n)return ie.handleTableData(),qt(),(0,Al.nextTick)()})})}).catch(()=>{var e=N.treeExpandLazyLoadedMaps;l&&(l.treeLoaded=!1),e[r]&&delete e[r]}).finally(()=>{U.treeExpandedFlag++,(0,Al.nextTick)().then(()=>ie.recalculate()).then(()=>o())})}else o()})},rr=(e,t)=>{var r,l=N.treeExpandedReserveRowMap;ne.value.reserve&&(r=Pl(ie,e),t?l[r]=e:l[r]&&delete l[r])},lr=n=>new Promise(l=>{var e=ae.value.loadMethod;if(e){var{fullAllDataRowIdData:o,rowExpandLazyLoadedMaps:a}=N;let t=Pl(ie,n),r=o[t];a[t]=n,e({$table:ie,row:n,rowIndex:ie.getRowIndex(n),$rowIndex:ie.getVMRowIndex(n)}).then(()=>{var e=N.rowExpandedMaps;r&&(r.expandLoaded=!0),e[t]=n,U.rowExpandedFlag++}).catch(()=>{r&&(r.expandLoaded=!1)}).finally(()=>{var e=N.rowExpandLazyLoadedMaps;e[t]&&delete e[t],U.rowExpandedFlag++,(0,Al.nextTick)().then(()=>ie.recalculate()).then(()=>ie.updateCellAreas()).then(()=>l())})}else l()}),or=(e,t)=>{var r,l=N.rowExpandedReserveRowMap;ae.value.reserve&&(r=Pl(ie,e),t?l[r]=e:l[r]&&delete l[r])},ar=()=>(0,Al.nextTick)().then(()=>{var e,{scrollXLoad:t,scrollYLoad:r}=U,{scrollXStore:l,scrollYStore:o}=N,a=R.value,n=w.value,n=(t?({toVisibleIndex:t,visibleSize:e}=Ft(),i=Math.max(0,n.oSize?Il().toNumber(n.oSize):0),l.preloadSize=Il().toNumber(n.preSize),l.offsetSize=i,l.visibleSize=e,l.endIndex=Math.max(l.startIndex+l.visibleSize+i,l.endIndex),l.visibleStartIndex=Math.max(l.startIndex,t),l.visibleEndIndex=Math.min(l.endIndex,t+e),ie.updateScrollXData().then(()=>{vr()})):ie.updateScrollXSpace(),kt()),{toVisibleIndex:i,visibleSize:l}=(o.rowHeight=n,U.rowHeight=n,Ot());r?(t=Math.max(0,a.oSize?Il().toNumber(a.oSize):0),o.preloadSize=Il().toNumber(a.preSize),o.offsetSize=t,o.visibleSize=l,o.endIndex=Math.max(o.startIndex+l+t,o.endIndex),o.visibleStartIndex=Math.max(o.startIndex,i),o.visibleEndIndex=Math.min(o.endIndex,i+l),ie.updateScrollYData().then(()=>{Rr()})):ie.updateScrollYSpace()}),nr=()=>{var{scrollXWidth:e,scrollYHeight:t}=U,r=N.elemStore,l=E.value,o=Wl(r["main-body-wrapper"]),a=Wl(r["main-header-table"]),r=Wl(r["main-footer-table"]),n=S.value,i=D.value;let s=!1;o&&(t=t>o.clientHeight,i&&(U.scrollbarWidth=l.width||i.offsetWidth-i.clientWidth||14),U.overflowY=t,s=e>o.clientWidth,n&&(U.scrollbarHeight=l.height||n.offsetHeight-n.clientHeight||14),i=a?a.clientHeight:0,t=r?r.clientHeight:0,N.tableHeight=o.offsetHeight,N.headerHeight=i,N.footerHeight=t,U.overflowX=s,U.parentHeight=Math.max(N.headerHeight+t+20,ie.getParentHeight())),s&&ie.checkScrolling()},ir=e=>{var t,r,l,o=G.value;return N.rceRunTime=Date.now(),o&&o.clientWidth?((o=me.value)&&([o,t,r,l]=o.children,Mt("default",o),Mt("medium",t),Mt("small",r),Mt("mini",l)),zt(),jt(),nr(),A(),Mr(),ar().then(()=>{if(zt(),e&&jt(),nr(),A(),e&&L(),Mr(),e)return ar()})):(0,Al.nextTick)()},sr=e=>{let t=[],r=[];e&&(Il().isArray(e)?e:[e]).forEach(e=>{t.push(e),r.push({field:e})}),U.rowGroupList=r,U.aggHandleFields=t},dr=e=>{let r=M.value.mapChildrenField;r&&(Il().lastEach(e,e=>{let t=0;Il().each(e[r],e=>{e.isAggregate?t+=e.childCount||0:t++}),e.childCount=t}),ie.handlePivotTableAggregateData)&&ie.handlePivotTableAggregateData(e)},cr=(e,g)=>{let m=e,v=e;if(g){let{rowField:s,parentField:d,childrenField:c,mapChildrenField:u}=M.value;var t=te.value;let p=t.checkField,h=t.indeterminateField||t.halfField;t=g[0];if(t&&s&&d&&c&&u){m=[],v=[];let l=t.field,o=ie.getColumnByField(l),a={},n=[],i=ao(ie);e.forEach(e=>{var t=o?ie.getCellLabel(e,o):Il().get(e,l),t=Il().eqNull(t)?"":t;let r=a[t];r||(r=[],a[t]=r),e.isAggregate&&(e.isAggregate=void 0),r.push(e)}),Il().objectEach(a,(e,t)=>{var{fullData:e,treeData:r}=cr(e,g.slice(1)),t={isAggregate:!0,aggData:{},groupContent:t,groupField:l,childCount:0,[s]:oo(),[d]:null,[i]:oo(),[c]:r,[u]:r};p&&(t[p]=!1),h&&(t[h]=!1),n.push(t),v.push(t),m.push(t),e.length&&m.push(...e)}),dr(n)}}return{treeData:v,fullData:m}},ur=(e,t)=>{var{keepSource:r,treeConfig:l,rowGroupConfig:o,aggregateConfig:a}=j;let{rowGroupList:n,scrollYLoad:i}=U,{scrollYStore:s,scrollXStore:d,lastScrollLeft:c,lastScrollTop:u}=N;var p=Q.value,h=ne.value;let g=ae.value;var m=h.transform,v=h.children||h.childrenField;let f=[],x=(0,Al.reactive)(e?e.slice(0):[]);if(5e6<x.length&&pl("vxe.error.errMaxRow",[5e6]),l&&n.length)return pl("vxe.error.noTree",["aggregate-config"]),(0,Al.nextTick)();if(p.drag&&n.length)return pl("vxe.error.errConflicts",["row-config.drag","aggregate-config"]),(0,Al.nextTick)();let b=!1,C=(l?m?(h.rowField||pl("vxe.error.reqProp",["tree-config.rowField"]),h.parentField||pl("vxe.error.reqProp",["tree-config.parentField"]),v||pl("vxe.error.reqProp",["tree-config.childrenField"]),h.mapChildrenField||pl("vxe.error.reqProp",["tree-config.mapChildrenField"]),v===h.mapChildrenField&&pl("vxe.error.errConflicts",["tree-config.childrenField","tree-config.mapChildrenField"]),f=Il().toArrayTree(x,{key:h.rowField,parentKey:h.parentField,children:v,mapChildren:h.mapChildrenField}),x=f.slice(0)):f=x.slice(0):(a||o)&&n.length&&(p=cr(x,n),f=p.treeData,x=p.fullData,b=!0),U.isRowGroupStatus=b,s.startIndex=0,s.endIndex=1,d.startIndex=0,d.endIndex=1,N.cvCacheMaps={},U.isRowLoading=!0,U.scrollVMLoading=!1,U.treeExpandedFlag++,U.rowExpandedFlag++,N.insertRowMaps={},U.insertRowFlag++,N.removeRowMaps={},U.removeRowFlag++,wr(x));return U.isDragColMove=!1,U.isDragRowMove=!1,N.tableFullData=x,N.tableFullTreeData=b?[]:f,N.tableFullGroupData=b?f:[],ie.cacheRowMap(t),N.tableSynchData=e,t&&(N.isResizeCellHeight=!1),r&&ie.cacheSourceMap(x),ie.clearCellAreas&&j.mouseConfig&&(ie.clearCellAreas(),ie.clearCopyCellArea()),ie.clearMergeCells(),ie.clearMergeFooterItems(),ie.handleTableData(!0),ie.updateFooter(),ie.handleUpdateBodyMerge(),(0,Al.nextTick)().then(()=>{Pt(),A()}).then(()=>{ar()}).then(()=>(C&&(s.endIndex=s.visibleSize),C&&(U.expandColumn&&"fixed"!==g.mode&&pl("vxe.error.notConflictProp",['column.type="expand','expand-config.mode="fixed"']),j.height||j.maxHeight||pl("vxe.error.reqProp",["height | max-height | virtual-y-config={enabled: false}"]),j.spanMethod)&&pl("vxe.error.scrollErrProp",["table.span-method"]),(()=>{var e=j.treeConfig,{expandColumn:t,currentRow:r,selectRadioRow:l}=U,{fullDataRowIdData:o,fullAllDataRowIdData:a,radioReserveRow:n,selectCheckboxMaps:i,treeExpandedMaps:s,rowExpandedMaps:d}=N,c=ae.value,u=ne.value,p=k.value,h=te.value;l&&!a[Pl(ie,l)]&&(U.selectRadioRow=null),p.reserve&&n&&o[l=Pl(ie,n)]&&Qt(o[l].row,!0),N.selectCheckboxMaps=Dt(i),U.updateCheckboxFlag++,h.reserve&&Jt(It(N.checkboxReserveRowMap),!0,!0),r&&!a[Pl(ie,r)]&&(U.currentRow=null),N.rowExpandedMaps=t?Dt(d):{},U.rowExpandedFlag++,t&&c.reserve&&ie.setRowExpand(It(N.rowExpandedReserveRowMap),!0),N.treeExpandedMaps=e?Dt(s):{},U.treeExpandedFlag++,e&&u.reserve&&ie.setTreeExpand(It(N.treeExpandedReserveRowMap),!0)})(),ie.checkSelectionStatus(),new Promise(o=>{(0,Al.nextTick)().then(()=>ir(!1)).then(()=>(Wt(),L(),ir(!1))).then(()=>{let e=c,t=u;var r=w.value,l=R.value;r.scrollToLeftOnChange&&(e=0),l.scrollToTopOnChange&&(t=0),U.isRowLoading=!1,ir(!1),i===C?lo(ie,e,t).then(()=>{Wt(),L(),o()}):setTimeout(()=>{lo(ie,e,t).then(()=>{Wt(),L(),o()})})})})))},pr=()=>{var e,t,r;(()=>{var e=j.checkboxConfig;if(e){let r=N.fullDataRowIdData;var{checkAll:e,checkRowKeys:l}=te.value;if(e)er(!0,!0);else if(l){let t=[];l.forEach(e=>{r[e]&&t.push(r[e].row)}),Jt(t,!0,!0)}}})(),(r=j.radioConfig)&&(r=N.fullDataRowIdData,{checkRowKey:e,reserve:t}=k.value,e)&&(r[e]&&Qt(r[e].row,!0),t)&&(r=ao(ie),N.radioReserveRow={[r]:e}),(()=>{var e=j.expandConfig;if(e){let r=N.fullDataRowIdData;var{expandAll:e,expandRowKeys:l}=ae.value;if(e)ie.setAllRowExpand(!0);else if(l){let t=[];l.forEach(e=>{r[e]&&t.push(r[e].row)}),ie.setRowExpand(t,!0)}}})(),(()=>{var e=j.treeConfig;if(e){let o=N.tableFullData;var e=ne.value,{expandAll:t,expandRowKeys:n}=e;let a=e.children||e.childrenField;if(t)ie.setAllTreeExpand(!0);else if(n){let r=[],l=ao(ie);n.forEach(t=>{var e=Il().findTree(o,e=>t===Il().get(e,l),{children:a});e&&r.push(e.item)}),ie.setTreeExpand(r,!0)}}})(),(t=j.mergeCells)&&ie.setMergeCells(t),(r=j.mergeFooterItems)&&ie.setMergeFooterItems(r),(0,Al.nextTick)(()=>setTimeout(()=>ie.recalculate()))},hr=()=>{(()=>{var t=j.sortConfig;if(t){var r=O.value;let e=r.defaultSort;e&&(e=Il().isArray(e)?e:[e]).length&&((t.multiple?e:e.slice(0,1)).forEach((e,t)=>{var{field:e,order:r}=e;e&&r&&(e=ie.getColumnByField(e))&&e.sortable&&(e.order=r,e.sortTime=Date.now()+t)}),r.remote||ie.handleTableData(!0).then(A))}})()},gr=()=>{var e=U.scrollXLoad;let{visibleColumn:t,scrollXStore:r,fullColumnIdData:l}=N;e=e?t.slice(r.startIndex,r.endIndex):t.slice(0);e.forEach((e,t)=>{e=e.id,e=l[e];e&&(e.$index=t)}),U.tableColumn=e},mr=()=>{var e=Il().orderBy(N.collectColumn,"renderSortNumber"),e=(N.collectColumn=e,fr(e));N.tableFullColumn=e,Bt()},vr=()=>{var e=U.isScrollXBig,{mergeBodyList:t,mergeFooterList:r,scrollXStore:l}=N,{preloadSize:o,startIndex:a,endIndex:n,offsetSize:i}=l,{toVisibleIndex:s,visibleSize:d}=Ft(),e={startIndex:Math.max(0,e?s-1:s-1-i-o),endIndex:e?s+d:s+d+i+o},{startIndex:i,endIndex:o}=(l.visibleStartIndex=s-1,l.visibleEndIndex=s+d+1,At(t.concat(r),e,"col"),e);!(s<=a||n-d-1<=s)||a===i&&n===o||(l.startIndex=i,l.endIndex=o,ie.updateScrollXData()),ie.closeTooltip()},fr=e=>{let t=[];return e.forEach(e=>{t.push(...e.children&&e.children.length?fr(e.children):[e])}),t},xr=e=>{let n=[],i=[],s=[];var{isGroup:t,columnStore:r}=U;let{collectColumn:o,tableFullColumn:l,scrollXStore:a,fullColumnIdData:d}=N;if(t){let t=[],r=[],l=[];Il().eachTree(o,(e,t,r,l,o)=>{var a=Kl(e);o&&o.fixed&&(e.fixed=o.fixed),o&&e.fixed!==o.fixed&&pl("vxe.error.groupFixed"),a?e.visible=!!Il().findTree(e.children,e=>!Kl(e)&&e.visible):e.visible&&("left"===e.fixed?n:"right"===e.fixed?s:i).push(e)}),o.forEach(e=>{e.visible&&("left"===e.fixed?t:"right"===e.fixed?l:r).push(e)}),U.tableGroupColumn=t.concat(r).concat(l)}else l.forEach(e=>{e.visible&&("left"===e.fixed?n:"right"===e.fixed?s:i).push(e)});let c=n.concat(i).concat(s);N.visibleColumn=c,Fr();t=Cr();return U.hasFixedColumn=0<n.length||0<s.length,Object.assign(r,{leftList:n,centerList:i,rightList:s}),t&&(j.spanMethod&&ul("vxe.error.scrollErrProp",["span-method"]),j.footerSpanMethod&&ul("vxe.error.scrollErrProp",["footer-span-method"]),e)&&(r=Ft().visibleSize,a.startIndex=0,a.endIndex=r,a.visibleSize=r,a.visibleStartIndex=0,a.visibleEndIndex=r),c.length===N.visibleColumn.length&&N.visibleColumn.every((e,t)=>e===c[t])||(ie.clearMergeCells(),ie.clearMergeFooterItems()),c.forEach((e,t)=>{e=e.id,e=d[e];e&&(e._index=t)}),gr(),e?(Fr(),ie.updateFooter().then(()=>ie.recalculate()).then(()=>(ie.updateCellAreas(),ie.recalculate()))):ie.updateFooter()},br=e=>{let l=ae.value;N.collectColumn=e;e=fr(e);return N.tableFullColumn=e,U.isColLoading=!0,U.isDragColMove=!1,N.collectColumn.forEach((e,t)=>{t+=1;e.sortNumber=t,e.renderSortNumber=t}),Promise.resolve((()=>{var e=j.customConfig,t=T.value,r=ut.value,{storage:l,restoreStore:o,storeOptions:a}=r,n=!0===l,l=n?{}:Object.assign({},l||{},a),a=b(l.resizable,n),i=b(l.visible,n),s=b(l.fixed,n),l=b(l.sort,n);if((e?Fl(r):r.enabled)&&(a||i||s||l)){if(t)return n=St(t),o?Promise.resolve(o({$table:ie,id:t,type:"restore",storeData:n})).then(e=>{if(e)return Ht(e)}).catch(e=>e):Ht(n);pl("vxe.error.reqProp",["id"])}})()).then(()=>{var{scrollXLoad:e,scrollYLoad:t,expandColumn:r}=U;return Bt(),xr(!0).then(()=>{U.scrollXLoad&&vr()}),ie.clearMergeCells(),ie.clearMergeFooterItems(),ie.handleTableData(!0),ie.handleAggregateSummaryData(),(e||t)&&r&&"fixed"!==l.mode&&ul("vxe.error.scrollErrProp",["column.type=expand"]),(0,Al.nextTick)().then(()=>(y&&y.syncUpdate({collectColumn:N.collectColumn,$table:ie}),ie.handleUpdateCustomColumn&&ie.handleUpdateCustomColumn(),U.isColLoading=!1,ie.recalculate()))})},Cr=e=>{var t=w.value,e=e||N.tableFullColumn,t=!!t.enabled&&-1<t.gt&&(0===t.gt||t.gt<e.length);return U.scrollXLoad=t},wr=e=>{var t=j.treeConfig,r=R.value,l=ne.value.transform,e=e||N.tableFullData,l=(l||!t)&&!!r.enabled&&-1<r.gt&&(0===r.gt||r.gt<e.length);return U.scrollYLoad=l},yr=(e,t)=>{let r=U.treeNodeColumn,{fullAllDataRowIdData:l,tableFullTreeData:o,treeExpandedMaps:a,treeExpandLazyLoadedMaps:n}=N;var i=ne.value;let{reserve:s,lazy:d,accordion:c,toggleMethod:u}=i,p=i.children||i.childrenField,h=i.hasChild||i.hasChildField,g=[],m=ie.getColumnIndex(r),v=ie.getVMColumnIndex(r),f=Nl(ie).handleGetRowId,x=u?e.filter(e=>u({$table:ie,expanded:t,column:r,columnIndex:m,$columnIndex:v,row:e})):e;return c&&(x=x.length?[x[x.length-1]]:[],i=Il().findTree(o,e=>e===x[0],{children:p}))&&i.items.forEach(e=>{e=f(e);a[e]&&delete a[e]}),t?x.forEach(e=>{var t,r=f(e);a[r]||(t=l[r])&&(d&&e[h]&&!t.treeLoaded&&!n[r]?g.push(tr(e)):e[p]&&e[p].length&&(a[r]=e))}):x.forEach(e=>{e=f(e);a[e]&&delete a[e]}),s&&x.forEach(e=>rr(e,t)),U.treeExpandedFlag++,Promise.all(g).then(()=>ie.recalculate())},Tr=(e,t)=>((e,t)=>{let{fullAllDataRowIdData:r,tableFullGroupData:l,rowGroupExpandedMaps:o}=N;let{mapChildrenField:a,accordion:n}=M.value,i=Nl(ie).handleGetRowId,s=e;return a&&(n&&(s=s.length?[s[s.length-1]]:[],e=Il().findTree(l,e=>Pl(ie,e)===Pl(ie,s[0]),{children:a}))&&e.items.forEach(e=>{e=i(e);o[e]&&delete o[e]}),t?s.forEach(e=>{var t=i(e);o[t]||r[t]&&e[a]&&e[a].length&&(o[t]=e)}):s.forEach(e=>{e=i(e);o[e]&&delete o[e]})),U.rowGroupExpandedFlag++,ie.recalculate()})(e,t).then(()=>(i(),ie.handleTableData(),U.rowGroupExpandedFlag++,qt(),(0,Al.nextTick)())).then(()=>ie.recalculate(!0)).then(()=>{setTimeout(()=>{ie.updateCellAreas()},30)}),Er=(e,t)=>{er(t),e&&se("checkbox-all",{records:()=>ie.getCheckboxRecords(),reserves:()=>ie.getCheckboxReserveRecords(),indeterminates:()=>ie.getCheckboxIndeterminateRecords(),checked:t},e)},Rr=()=>{var{isAllOverflow:e,isScrollYBig:t}=U,{mergeBodyList:r,scrollYStore:l}=N,{preloadSize:o,startIndex:a,endIndex:n,offsetSize:i}=l,e=e?i:i+1,{toVisibleIndex:s,visibleSize:d}=Ot(),i={startIndex:Math.max(0,t?s-1:s-1-i-o),endIndex:t?s+d:s+d+e+o},{startIndex:t,endIndex:e}=(l.visibleStartIndex=s-1,l.visibleEndIndex=s+d+1,At(r,i,"row"),i);!(s<=a||n-d-1<=s)||a===t&&n===e||(l.startIndex=t,l.endIndex=e,ie.updateScrollYData())};s=r=>function(e){var t=N.fullAllDataRowIdData;if(e){t=t[Pl(ie,e)];if(t)return t[r]}return-1},He=r=>function(e){var t=N.fullColumnIdData;if(e){t=t[e.id];if(t)return t[r]}return-1};let se=(e,t,r)=>{l(e,wl(r,{$table:ie,$grid:Y},t))},Sr=()=>{var e=G.value;e&&e.clientWidth&&e.clientHeight&&ie.recalculate()},Dr=(e,t)=>{ie.analyColumnWidth(),ie.recalculate().then(()=>{ie.saveCustomStore("update:width"),ie.updateCellAreas(),ie.dispatchEvent("column-resizable-change",t,e),ie.dispatchEvent("resizable-change",t,e),setTimeout(()=>ie.recalculate(!0),300)})},Ir=(e,t)=>{U.resizeHeightFlag++,ie.recalculate().then(()=>{ie.updateCellAreas(),ie.dispatchEvent("row-resizable-change",t,e),setTimeout(()=>ie.recalculate(!0),300)})},Fr=()=>{var{visibleColumn:r,fullColumnIdData:l}=N;let o=0;for(let e=0,t=r.length;e<t;e++){var a=r[e],n=l[a.id];n&&(n.oLeft=o),o+=a.renderWidth}},L=()=>{var r=U.expandColumn,{afterFullData:l,fullAllDataRowIdData:o,rowExpandedMaps:a}=N,n=ae.value,i=Q.value,s=F.value,d=I.value,c=Nl(ie).handleGetRowId;let u=0;for(let e=0,t=l.length;e<t;e++){var p=c(l[e]),h=o[p]||{};h.oTop=u,u+=h.resizeHeight||s.height||i.height||h.height||d,r&&a[p]&&(u+=h.expandHeight||n.height||0)}},Mr=()=>{let{expandColumn:e,scrollYLoad:s,scrollYTop:d,isScrollYBig:c}=U;var t=ae.value;let u=Q.value,p=F.value,h=I.value;t=t.mode;if(e&&"fixed"===t){let{elemStore:e,fullAllDataRowIdData:n}=N;t=K.value;let i=Wl(e["main-body-scroll"]);if(t&&i){let a=!1;Il().arrayEach(t.children,t=>{var r=t.getAttribute("rowid")||"",l=n[r];if(l){var o=t.offsetHeight+1,r=i.querySelector(`.vxe-body--row[rowid="${r}"]`);let e=0;s?e=c&&r?r.offsetTop+r.offsetHeight:l.oTop+(l.resizeHeight||p.height||u.height||l.height||h):r&&(e=r.offsetTop+r.offsetHeight),c&&(e+=d),t.style.top=to(e),a||l.expandHeight!==o&&(a=!0),l.expandHeight=o}}),a&&(U.rowExpandHeightFlag++,(0,Al.nextTick)(()=>{L()}))}}},kr=()=>{var e=N.elemStore,t=K.value,e=Wl(e["main-body-scroll"]);t&&e&&(t.scrollTop=e.scrollTop)},Or=(z={dispatchEvent:se,getEl(){return G.value},clearAll(){var e=ie,{props:t,internalData:r}=(e.clearFilter&&e.clearFilter(),e);return r.initStatus=!1,e.clearSort(),e.clearCurrentRow(),e.clearCurrentColumn(),e.clearRadioRow(),e.clearRadioReserve(),e.clearCheckboxRow(),e.clearCheckboxReserve(),e.clearRowExpand(),e.clearTreeExpand(),e.clearTreeExpandReserve(),e.clearPendingRow(),e.clearFilter&&e.clearFilter(),e.clearSelected&&(t.keyboardConfig||t.mouseConfig)&&e.clearSelected(),e.clearCellAreas&&t.mouseConfig&&(e.clearCellAreas(),e.clearCopyCellArea()),e.clearScroll()},syncData(){return pl("vxe.error.delFunc",["syncData","getData"]),(0,Al.nextTick)().then(()=>(U.tableData=[],l("update:data",N.tableFullData),(0,Al.nextTick)()))},updateData(){let{scrollXLoad:e,scrollYLoad:t}=U;return W.handleTableData(!0).then(()=>{if(z.updateFooter(),e||t)return e&&W.updateScrollXSpace(),t&&W.updateScrollYSpace(),z.refreshScroll()}).then(()=>(z.updateCellAreas(),z.recalculate(!0))).then(()=>{setTimeout(()=>ie.recalculate(),50)})},loadData(e){let t=N.initStatus;return ur(e,!1).then(()=>(N.inited=!0,N.initStatus=!0,t||pr(),z.recalculate()))},reloadData(e){return z.clearAll().then(()=>(N.inited=!0,N.initStatus=!0,ur(e,!0))).then(()=>(pr(),z.recalculate()))},setRow(t,o){if(t&&o){let e=t,l=(Il().isArray(t)||(e=[t]),ao(ie));e.forEach(e=>{var t=Pl(ie,e),r=Il().clone(Object.assign({},o),!0);Il().set(r,l,t),Object.assign(e,r)})}return(0,Al.nextTick)()},reloadRow(e,t,r){var l=j.keepSource,o=U.tableData,a=N.sourceDataRowIdData;if(l){if(ie.isAggregateRecord(e))return(0,Al.nextTick)();l=a[Pl(ie,e)];l&&e&&(r?(a=Il().clone(Il().get(t||e,r),!0),Il().set(e,r,a),Il().set(l,r,a)):(r=ao(ie),a=Pl(ie,e),t=Il().clone(Object.assign({},t),!0),Il().set(t,r,a),Il().destructuring(l,Object.assign(e,t)))),U.tableData=o.slice(0)}else pl("vxe.error.reqProp",["keep-source"]);return(0,Al.nextTick)()},getParams(){return j.params},loadTreeChildren(l,e){let t=j.keepSource,{tableSourceData:o,fullDataRowIdData:i,fullAllDataRowIdData:s,sourceDataRowIdData:a}=N;var r=ne.value;let{transform:n,mapChildrenField:d}=r,c=r.children||r.childrenField,u=s[Pl(ie,l)],p=u?u.level:0;return z.createData(e).then(e=>{if(t){let t=Pl(ie,l);var r=Il().findTree(o,e=>t===Pl(ie,e),{children:c});r&&(r.item[c]=Il().clone(e,!0)),e.forEach(e=>{var t=Pl(ie,e);a[t]=Il().clone(e,!0)})}return Il().eachTree(e,(e,t,r,l,o,a)=>{var n=Pl(ie,e),e={row:e,rowid:n,seq:-1,index:t,_index:-1,$index:-1,treeIndex:-1,items:r,parent:o||u.row,level:p+a.length,height:0,resizeHeight:0,oTop:0,expandHeight:0};i[n]=e,s[n]=e},{children:c}),l[c]=e,n&&(l[d]=Il().clone(e,!1)),qt(),e})},loadColumn(e){let{lastScrollLeft:o,lastScrollTop:a}=N;e=Il().mapTree(e,e=>(0,Al.reactive)(vo.createColumn(ie,e)));return br(e).then(()=>{let e=o,t=a;var r=w.value,l=R.value;r.scrollToLeftOnChange&&(e=0),l.scrollToTopOnChange&&(t=0),lo(ie,e,t)})},reloadColumn(e){return z.clearAll().then(()=>z.loadColumn(e))},getRowNode(e){if(e){var t=N.fullAllDataRowIdData,e=e.getAttribute("rowid");if(e){t=t[e];if(t)return{rowid:t.rowid,item:t.row,index:t.index,items:t.items,parent:t.parent}}}return null},getColumnNode(e){if(e){var t=N.fullColumnIdData,e=e.getAttribute("colid");if(e){t=t[e];if(t)return{colid:t.colid,item:t.column,index:t.index,items:t.items,parent:t.parent}}}return null},getRowSeq:s("seq"),getRowIndex:s("index"),getVTRowIndex:s("_index"),getVMRowIndex:s("$index"),getColumnIndex:He("index"),getVTColumnIndex:He("_index"),getVMColumnIndex:He("$index"),createData(e){return(0,Al.nextTick)().then(()=>(0,Al.reactive)(W.defineField(e)))},createRow(e){let t=Il().isArray(e);return t||(e=[e||{}]),z.createData(e).then(e=>t?e:e[0])},revertData(e,l){var{keepSource:t,treeConfig:r}=j;let{fullAllDataRowIdData:o,fullDataRowIdData:a,tableSourceData:n,sourceDataRowIdData:i,tableFullData:s,afterFullData:d,removeRowMaps:c}=N;var u=ne.value.transform;let p=Nl(ie).handleGetRowId;if(!t)return pl("vxe.error.reqProp",["keep-source"]),(0,Al.nextTick)();let h=e,g=(e?Il().isArray(e)||(h=[e]):h=Il().toArray(ie.getUpdateRecords()),!1);return h.length&&h.forEach(e=>{var t,e=p(e),r=o[e];r&&(r=r.row,ie.isInsertByRow(r)||(t=i[e])&&r&&(l?Il().set(r,l,Il().clone(Il().get(t,l),!0)):Il().destructuring(r,Il().clone(t,!0)),!a[e])&&ie.isRemoveByRow(r)&&(c[e]&&delete c[e],s.unshift(r),d.unshift(r),g=!0))}),e?(g&&(U.removeRowFlag++,ie.updateFooter(),ie.cacheRowMap(!1),ie.handleTableData(r&&u),r&&u||ie.updateAfterDataIndex(),ie.checkSelectionStatus(),U.scrollYLoad)&&ie.updateScrollYSpace(),(0,Al.nextTick)().then(()=>(ie.updateCellAreas(),ie.recalculate()))):ie.reloadData(n)},clearData(e,t){let{tableFullData:r,visibleColumn:l}=N;return arguments.length?e&&!Il().isArray(e)&&(e=[e]):e=r,t?e.forEach(e=>Il().set(e,t,null)):e.forEach(t=>{l.forEach(e=>{e.field&&Ul(t,e,null)})}),(0,Al.nextTick)()},getCellElement(e,t){var r=N.elemStore,t=zl(ie,t);if(!t)return null;var e=Pl(ie,e),l=Wl(r["main-body-scroll"]),o=Wl(r["left-body-scroll"]),r=Wl(r["right-body-scroll"]);let a;return t&&(t.fixed&&("left"===t.fixed?o&&(a=o):r&&(a=r)),a=a||l)?a.querySelector(`.vxe-body--row[rowid="${e}"] .`+t.id):null},getCellLabel(t,r){r=zl(ie,r);if(!r)return null;var l=r.formatter,o=jl(t,r);let a=o;if(l){let e;var n=N.fullAllDataRowIdData,i=Pl(ie,t),s=r.id,d=n[i];if(d&&(e=(e=d.formatData)||(n[i].formatData={}),d)&&e[s]&&e[s].value===o)return e[s].label;n={cellValue:o,row:t,rowIndex:z.getRowIndex(t),column:r,columnIndex:z.getColumnIndex(r)};a=Il().isString(l)?(d=(i=Cl.get(l))?i.tableCellFormatMethod||i.cellFormatMethod:null)?d(n):"":Il().isArray(l)?(r=(t=Cl.get(l[0]))?t.tableCellFormatMethod||t.cellFormatMethod:null)?r(n,...l.slice(1)):"":l(n),e&&(e[s]={value:o,label:a})}return a},isInsertByRow(e){e=Pl(ie,e);return!!U.insertRowFlag&&!!N.insertRowMaps[e]},isRemoveByRow(e){e=Pl(ie,e);return!!U.removeRowFlag&&!!N.removeRowMaps[e]},removeInsertRow(){var e=N.insertRowMaps;return ie.remove(Il().values(e))},isUpdateByRow(e,t){var r=j.keepSource,{tableFullColumn:l,fullDataRowIdData:o,sourceDataRowIdData:a}=N;if(r){r=Il().isString(e)||Il().isNumber(e)?e:Pl(ie,e),e=o[r];if(!e)return!1;var n=e.row,i=a[r];if(i){if(1<arguments.length)return!Tt(i,n,t);for(let e=0,t=l.length;e<t;e++){var s=l[e].field;if(s&&!Tt(i,n,s))return!0}}}return!1},getColumns(e){var t=N.visibleColumn;return Il().isUndefined(e)?t.slice(0):t[e]},getColid(e){e=zl(ie,e);return e?e.id:null},getColumnById(e){var t=N.fullColumnIdData;return e&&t[e]?t[e].column:null},getColumnByField(e){var t=N.fullColumnFieldData;return e&&t[e]?t[e].column:null},getParentColumn(e){var t=N.fullColumnIdData,e=zl(ie,e);return e&&e.parentId&&t[e.parentId]?t[e.parentId].column:null},getTableColumn(){return{collectColumn:N.collectColumn.slice(0),fullColumn:N.tableFullColumn.slice(0),visibleColumn:N.visibleColumn.slice(0),tableColumn:U.tableColumn.slice(0)}},moveColumnTo(e,r,t){var{fullColumnIdData:l,visibleColumn:o}=N,{dragToChild:t,dragPos:a,isCrossDrag:n}=Object.assign({},t),e=zl(ie,e);let i=null;var s=e?l[e.id]:null;let d="left";if(Il().isNumber(r)){if(s&&r){let e=s.items,t=s._index+r;n&&(e=o,t=s._index+r),0<t&&t<e.length-1&&(i=e[t]),0<r&&(d="right")}}else{n=(i=zl(ie,r))?l[i.id]:null;s&&n&&n._index>s._index&&(d="right")}return ie.handleColDragSwapEvent(null,!0,e,i,a||d,!0===t)},moveRowTo(e,r,t){var l=j.treeConfig,{fullAllDataRowIdData:o,afterFullData:a}=N,{dragToChild:t,dragPos:n,isCrossDrag:i}=Object.assign({},t),s=ne.value,e=io(ie,e);let d=null,c="top";var u=e?o[Pl(ie,e)]:null;if(Il().isNumber(r)){if(u&&r){let e=a,t=u._index+r;l&&(e=u.items,s.transform)&&(t=u.treeIndex+r,i)&&(e=a,t=u._index+r),0<=t&&t<=e.length-1&&(d=e[t]),0<r&&(c="bottom")}}else{l=(d=io(ie,r))?o[Pl(ie,d)]:null;u&&l&&l._index>u._index&&(c="bottom")}return ie.handleRowDragSwapEvent(null,!0,e,d,n||c,!0===t)},getFullColumns(){var e=N.collectColumn;return e.slice(0)},getData(e){var t=j.data||N.tableSynchData;return Il().isUndefined(e)?t.slice(0):t[e]},getCheckboxRecords(e){var t=j.treeConfig,r=U.updateCheckboxFlag;let{tableFullData:l,afterFullData:o,tableFullTreeData:a,fullDataRowIdData:n,afterFullRowMaps:i,selectCheckboxMaps:s}=N;var d=ne.value,{transform:c,mapChildrenField:u}=d;let p=te.value.checkField;d=d.children||d.childrenField;let h=[];if(r)if(p)h=t?(r=e?c?a:l:c?a:o,Il().filterTree(r,e=>Il().get(e,p),{children:c?u:d})):(e?l:o).filter(e=>Il().get(e,p));else{let r=e||t&&!c?n:i;Il().each(s,(e,t)=>{r[t]&&h.push(n[t].row)})}return h},getTreeRowChildren(t){var r=j.treeConfig,l=N.fullAllDataRowIdData,o=ne.value,{transform:a,mapChildrenField:n}=o,o=o.children||o.childrenField;if(t&&r){let e;if(e=Il().isString(t)?t:Pl(ie,t)){r=l[e],t=r?r.row:null;if(t)return t[a?n:o]||[]}}return[]},getTreeParentRow(t){var r=j.treeConfig,l=N.fullAllDataRowIdData;if(t&&r){let e;if(e=Il().isString(t)?t:Pl(ie,t))return(r=l[e])?r.parent:null}return null},getParentRow(e){return ul("vxe.error.delFunc",["getParentRow","getTreeParentRow"]),ie.getTreeParentRow(e)},getRowById(e){var t=N.fullAllDataRowIdData,e=Il().eqNull(e)?"":encodeURIComponent(e||"");return t[e]?t[e].row:null},getRowid(e){return Pl(ie,e)},getTableData(){var{tableData:e,footerTableData:t}=U,{tableFullData:r,afterFullData:l,tableFullTreeData:o}=N;return{fullData:(j.treeConfig?o:r).slice(0),visibleData:l.slice(0),tableData:e.slice(0),footerData:t.slice(0)}},getFullData(){var e,t,r,l,o=j.treeConfig,{tableFullData:a,tableFullTreeData:n}=N;return o?({transform:e,mapChildrenField:t,rowField:r,parentField:l}=o=ne.value,o=o.children||o.childrenField,e?Il().toArrayTree(Il().toTreeArray(n,{children:t}),{key:r,parentKey:l,children:o,mapChildren:t}):n.slice(0)):a.slice(0)},setColumnFixed(e,t){let r=!1;var l=Il().isArray(e)?e:[e],o=Z.value,a=mt.value;for(let e=0;e<l.length;e++){var n=l[e],n=zl(ie,n),n=ho(ie,n);if(n&&n.fixed!==t){if(!n.fixed&&a)return Dl.VxeUI.modal&&Dl.VxeUI.modal.message({status:"error",content:gl("vxe.table.maxFixedCol",[o.maxFixedSize])}),(0,Al.nextTick)();Il().eachTree([n],e=>{e.fixed=t,e.renderFixed=t}),W.saveCustomStore("update:fixed"),r=r||!0}}return r?z.refreshColumn():(0,Al.nextTick)()},clearColumnFixed(e){let t=!1;return(Il().isArray(e)?e:[e]).forEach(e=>{e=zl(ie,e),e=ho(ie,e);e&&e.fixed&&(Il().eachTree([e],e=>{e.fixed=null,e.renderFixed=null}),W.saveCustomStore("update:fixed"),t=t||!0)}),t?z.refreshColumn():(0,Al.nextTick)()},hideColumn(e){let t=!1;return(Il().isArray(e)?e:[e]).forEach(e=>{e=zl(ie,e);e&&e.visible&&(e.visible=!1,t=t||!0)}),t?W.handleCustom():(0,Al.nextTick)()},showColumn(e){let t=!1;return(Il().isArray(e)?e:[e]).forEach(e=>{e=zl(ie,e);e&&!e.visible&&(e.visible=!0,t=t||!0)}),t?W.handleCustom():(0,Al.nextTick)()},setColumnWidth(e,t){var r=N.elemStore;let l=!1;e=Il().isArray(e)?e:[e];let o=Il().toInteger(t);return Jl(t)&&(r=(t=Wl(r["main-body-scroll"]))?t.clientWidth-1:0,o=Math.floor(o*r)),(o&&(e.forEach(e=>{e=zl(ie,e);e&&(e.resizeWidth=o,l=l||!0)}),l)?ie.refreshColumn():(0,Al.nextTick)()).then(()=>({status:l}))},getColumnWidth(e){e=zl(ie,e);return e?e.renderWidth:0},resetColumn(e){return ul("vxe.error.delFunc",["resetColumn","resetCustom"]),ie.resetCustom(e)},refreshColumn(e){return e&&mr(),xr(!0).then(()=>z.refreshScroll()).then(()=>z.recalculate())},setRowHeightConf(e){let r=N.fullAllDataRowIdData,l=!1;return e&&(Il().each(e,(e,t)=>{t=r[t];t&&(e=Il().toInteger(e))&&(t.resizeHeight=e,l=l||!0)}),l)&&(N.isResizeCellHeight=!0,U.resizeHeightFlag++),(0,Al.nextTick)().then(()=>(L(),{status:l}))},getRowHeightConf(l){let{fullAllDataRowIdData:o,afterFullData:e}=N,a=Nl(ie).handleGetRowId,n=Q.value,i=F.value,s=I.value,d={};return e.forEach(e=>{var t,e=a(e),r=o[e];r&&((t=r.resizeHeight)||l)&&(t=t||i.height||n.height||r.height||s,d[e]=t)}),d},setRowHeight(e,t){let r=N.fullAllDataRowIdData,l=!1;e=Il().isArray(e)?e:[e];let o=Il().toInteger(t);if(Jl(t)&&(t=(t=(t=f.value)?t.$el:null)?t.clientHeight-1:0,o=Math.floor(o*t)),o){let t=Nl(ie).handleGetRowId;e.forEach(e=>{e=Il().isString(e)||Il().isNumber(e)?e:t(e),e=r[e];e&&(e.resizeHeight=o,l=l||!0)}),l&&(N.isResizeCellHeight=!0,U.resizeHeightFlag++)}return(0,Al.nextTick)().then(()=>({status:l}))},getRowHeight(e){var t=N.fullAllDataRowIdData,r=Q.value,l=F.value,o=I.value,t=t[Il().isString(e)||Il().isNumber(e)?e:Pl(ie,e)];return t?t.resizeHeight||l.height||r.height||t.height||o:0},refreshScroll(){let{elemStore:e,lastScrollLeft:t,lastScrollTop:r}=N,l=Wl(e["main-header-scroll"]),o=Wl(e["main-body-scroll"]),a=Wl(e["main-footer-scroll"]),n=Wl(e["left-body-scroll"]),i=Wl(e["right-body-scroll"]),s=S.value,d=D.value;return new Promise(e=>{if(t||r)return lo(ie,t,r).then(()=>{setTimeout(e,10)});N.intoRunScroll=!0,_l(d,r),_l(o,r),_l(n,r),_l(i,r),Hl(s,t),Hl(o,t),Hl(l,t),Hl(a,t),setTimeout(()=>{N.intoRunScroll=!1,e()},10)})},recalculate(a){return new Promise(e=>{var{rceTimeout:t,rceRunTime:r}=N,l=Ne.value.refreshDelay||20,o=G.value;o&&o.clientWidth&&(jt(),Mr()),!t||(clearTimeout(t),r&&r+(l-5)<Date.now())?e(ir(!!a)):(0,Al.nextTick)(()=>{e()}),N.rceTimeout=setTimeout(()=>{N.rceTimeout=void 0,ir(!!a)},l)})},openTooltip(e,t){var r=ve.value;return r&&r.open?r.open(e,t):(0,Al.nextTick)()},closeTooltip(){var e=U.tooltipStore,t=q.value,r=ve.value;return e.visible&&(Object.assign(e,{row:null,column:null,content:null,visible:!1,currOpts:{}}),t)&&t.close&&t.close(),r&&r.close&&r.close(),(0,Al.nextTick)()},isAllCheckboxChecked(){return U.isAllSelected},isAllCheckboxIndeterminate(){return!U.isAllSelected&&U.isIndeterminate},getCheckboxIndeterminateRecords(e){var t=j.treeConfig;let{fullDataRowIdData:o,treeIndeterminateRowMaps:a}=N;if(t){let r=[],l=[];return Il().each(a,(e,t)=>{e&&(r.push(e),o[t])&&l.push(e)}),e?r:l}return[]},setCheckboxRow(e,t){return e&&!Il().isArray(e)&&(e=[e]),Jt(e,t,!0)},setCheckboxRowKey(e,t){let r=N.fullAllDataRowIdData,l=(Il().isArray(e)||(e=[e]),[]);return e.forEach(e=>{e=r[e];e&&l.push(e.row)}),Jt(l,t,!0)},isCheckedByCheckboxRow(e){var t=U.updateCheckboxFlag,r=N.selectCheckboxMaps,l=te.value.checkField;return l?Il().get(e,l):!!t&&!!r[Pl(ie,e)]},isCheckedByCheckboxRowKey(e){var t=U.updateCheckboxFlag,{fullAllDataRowIdData:r,selectCheckboxMaps:l}=N,o=te.value.checkField;return o?!!(r=r[e])&&Il().get(r.row,o):!!t&&!!l[e]},isIndeterminateByCheckboxRow(e){var t=N.treeIndeterminateRowMaps;return!!t[Pl(ie,e)]&&!ie.isCheckedByCheckboxRow(e)},isIndeterminateByCheckboxRowKey(e){var t=N.treeIndeterminateRowMaps;return!!t[e]&&!ie.isCheckedByCheckboxRowKey(e)},toggleCheckboxRow(e){var t=N.selectCheckboxMaps,r=te.value.checkField,r=r?!Il().get(e,r):!t[Pl(ie,e)];return W.handleBatchSelectRows([e],r,!0),W.checkSelectionStatus(),(0,Al.nextTick)()},setAllCheckboxRow(e){return er(e,!0)},getRadioReserveRecord(e){var l=j.treeConfig,{fullDataRowIdData:t,radioReserveRow:o,afterFullData:a}=N,r=k.value,n=ne.value,n=n.children||n.childrenField;if(r.reserve&&o){let r=Pl(ie,o);if(e){if(!t[r])return o}else{let t=ao(ie);if(l){if(Il().findTree(a,e=>r===Il().get(e,t),{children:n}))return o}else if(!a.some(e=>r===Il().get(e,t)))return o}}return null},clearRadioReserve(){return(N.radioReserveRow=null,Al.nextTick)()},getCheckboxReserveRecords(l){var e=j.treeConfig;let{afterFullData:o,fullDataRowIdData:a,checkboxReserveRowMap:n}=N;var t=te.value,i=ne.value,i=i.children||i.childrenField;let s=[];if(t.reserve){let t=Nl(ie).handleGetRowId,r={};e?Il().eachTree(o,e=>{r[t(e)]=1},{children:i}):o.forEach(e=>{r[t(e)]=1}),Il().each(n,(e,t)=>{e&&(l?a[t]||s.push(e):r[t]||s.push(e))})}return s},clearCheckboxReserve(){return N.checkboxReserveRowMap={},(0,Al.nextTick)()},toggleAllCheckboxRow(){return Er(null,!U.isAllSelected),(0,Al.nextTick)()},clearCheckboxRow(){let t=j.treeConfig;var e=N.tableFullData,r=ne.value,r=r.children||r.childrenField,l=te.value;let{checkField:o,reserve:a}=l,n=l.indeterminateField||l.halfField;return o&&(l=e=>{t&&n&&Il().set(e,n,!1),Il().set(e,o,!1)},t?Il().eachTree(e,l,{children:r}):e.forEach(l)),a&&e.forEach(e=>Zt(e,!1)),U.isAllSelected=!1,U.isIndeterminate=!1,N.selectCheckboxMaps={},N.treeIndeterminateRowMaps={},U.updateCheckboxFlag++,(0,Al.nextTick)()},setCurrentRow(e){var t=Q.value,r=G.value;return z.clearCurrentRow(),U.currentRow=e,(t.isCurrent||j.highlightCurrentRow)&&r&&Il().arrayEach(r.querySelectorAll(`[rowid="${Pl(ie,e)}"]`),e=>$l(e,"row--current")),(0,Al.nextTick)()},isCheckedByRadioRow(e){var t=U.selectRadioRow;return!(!e||!t)&&ie.eqRow(t,e)},isCheckedByRadioRowKey(e){var t=U.selectRadioRow;return!!t&&e===Pl(ie,t)},setRadioRow(e){return Qt(e,!0)},setRadioRowKey(e){var t=N.fullAllDataRowIdData,t=t[e];return t?Qt(t.row,!0):(0,Al.nextTick)()},clearCurrentRow(){var e=G.value;return U.currentRow=null,N.hoverRow=null,e&&Il().arrayEach(e.querySelectorAll(".row--current"),e=>Vl(e,"row--current")),(0,Al.nextTick)()},clearRadioRow(){return(U.selectRadioRow=null,Al.nextTick)()},getCurrentRecord(){return Q.value.isCurrent||j.highlightCurrentRow?U.currentRow:null},getRadioRecord(e){var{fullDataRowIdData:t,afterFullRowMaps:r}=N,l=U.selectRadioRow;if(l){var o=Pl(ie,l);if(e){if(t[o])return l}else if(r[o])return l}return null},getCurrentColumn(){return Z.value.isCurrent||j.highlightCurrentColumn?U.currentColumn:null},setCurrentColumn(e){var t=j.mouseConfig,r=le.value;let l=t&&r.selected;t=zl(ie,e);return t&&(ie.clearCurrentColumn(),U.currentColumn=t),(0,Al.nextTick)().then(()=>{l&&ie.addCellSelectedClass()})},clearCurrentColumn(){return(U.currentColumn=null,Al.nextTick)()},setPendingRow(e,t){let r=Nl(ie).handleGetRowId,l=N.pendingRowMaps;return e&&!Il().isArray(e)&&(e=[e]),t?e.forEach(e=>{var t=r(e);t&&!l[t]&&(l[t]=e)}):e.forEach(e=>{e=r(e);e&&l[e]&&delete l[e]}),U.pendingRowFlag++,(0,Al.nextTick)()},togglePendingRow(e){let r=Nl(ie).handleGetRowId,l=N.pendingRowMaps;return(e=e&&!Il().isArray(e)?[e]:e).forEach(e=>{var t=r(e);t&&(l[t]?delete l[t]:l[t]=e)}),U.pendingRowFlag++,(0,Al.nextTick)()},hasPendingByRow(e){return z.isPendingByRow(e)},isPendingByRow(e){var t=N.pendingRowMaps;return!!t[Pl(ie,e)]},getPendingRecords(){let{fullAllDataRowIdData:r,pendingRowMaps:e}=N,l=[];return Il().each(e,(e,t)=>{r[t]&&l.push(e)}),l},clearPendingRow(){return N.pendingRowMaps={},U.pendingRowFlag++,(0,Al.nextTick)()},sort(e,t){let{multiple:r,remote:l,orders:a}=O.value;return e&&Il().isString(e)&&(e=[{field:e,order:t}]),(e=Il().isArray(e)?e:[e]).length?(r||$t(),(r?e:[e[0]]).forEach((e,t)=>{let{field:r,order:l}=e,o=r;(o=Il().isString(r)?z.getColumnByField(r):o)&&o.sortable&&(a&&-1===a.indexOf(l)&&(l=Rt(o)),o.order!==l&&(o.order=l),o.sortTime=Date.now()+t)}),l||W.handleTableData(!0),(0,Al.nextTick)().then(()=>(L(),z.updateCellAreas(),A()))):(0,Al.nextTick)()},setSort(e,t){return Vt(new Event("click"),e,t)},setSortByEvent(e,t,r){return Vt(e,t,r)},clearSort(e){var t=O.value;return e?(e=zl(ie,e))&&(e.order=null):$t(),t.remote||ie.handleTableData(!0),(0,Al.nextTick)().then(()=>(L(),A()))},clearSortByEvent(e,t){var r=N.tableFullColumn,l=O.value;let o=[],a=null;return e&&(t?(a=zl(ie,t))&&(a.order=null):r.forEach(e=>{e.order&&(e.order=null,o.push(e))}),l.remote||ie.handleTableData(!0),o.length?(t={$table:ie,$event:e,cols:o,sortList:[]},se("clear-all-sort",t,e)):a&&ie.handleColumnSortEvent(e,a)),(0,Al.nextTick)().then(()=>(L(),A()))},isSort(e){return e?!!(e=zl(ie,e))&&e.sortable&&!!e.order:0<z.getSortColumns().length},getSortColumns(){var{multiple:e,chronological:t}=O.value;let l=[];var r=N.tableFullColumn;return r.forEach(e=>{var{field:t,order:r}=e;e.sortable&&r&&l.push({column:e,field:t,property:t,order:r,sortTime:e.sortTime})}),e&&t&&1<l.length?Il().orderBy(l,"sortTime"):l},setFilterByEvent(e,t,r,l){t=zl(ie,t);return t&&t.filters&&(t.filters=so(r||[]),l)?ie.handleColumnConfirmFilter(t,e):(0,Al.nextTick)()},closeFilter(){var e=U.filterStore,{column:t,visible:r}=e;return e.isAllSelected=!1,e.isIndeterminate=!1,e.options=[],e.visible=!1,r&&se("filter-visible",{column:t,property:t.field,field:t.field,filterList:()=>ie.getCheckedFilters(),visible:!1},null),(0,Al.nextTick)()},isActiveFilterByColumn(e){e=zl(ie,e);return e?e.filters&&e.filters.some(e=>e.checked):0<ie.getCheckedFilters().length},isFilter(e){return z.isActiveFilterByColumn(e)},clearFilterByEvent(e,t){var r=U.filterStore,l=N.tableFullColumn,o=qe.value;let a=[],n=null;return t?(n=zl(ie,t))&&ie.handleClearFilter(n):l.forEach(e=>{e.filters&&(a.push(e),ie.handleClearFilter(e))}),t&&n===r.column||Object.assign(r,{isAllSelected:!1,isIndeterminate:!1,style:null,options:[],column:null,multiple:!1,visible:!1}),o.remote||ie.updateData(),a.length?(l={$table:ie,$event:e,cols:a,filterList:[]},se("clear-all-filter",l,e)):n&&ie.dispatchEvent("clear-filter",{filterList:()=>ie.getCheckedFilters()},e),(0,Al.nextTick)()},isRowExpandLoaded(e){var t=N.fullAllDataRowIdData,t=t[Pl(ie,e)];return t&&!!t.expandLoaded},clearRowExpandLoaded(e){var{fullAllDataRowIdData:t,rowExpandLazyLoadedMaps:r}=N,l=ae.value.lazy,e=Pl(ie,e),t=t[e];return l&&t&&(t.expandLoaded=!1,delete r[e]),U.rowExpandedFlag++,(0,Al.nextTick)()},reloadRowExpand(e){var t=N.rowExpandLazyLoadedMaps,r=ae.value.lazy,l=Pl(ie,e);return r&&!t[l]&&ie.clearRowExpandLoaded(e).then(()=>lr(e)),(0,Al.nextTick)()},reloadExpandContent(e){return ul("vxe.error.delFunc",["reloadExpandContent","reloadRowExpand"]),ie.reloadRowExpand(e)},toggleRowExpand(e){return ie.setRowExpand(e,!ie.isRowExpandByRow(e))},setAllRowExpand(e){var t=ne.value,{tableFullData:r,tableFullTreeData:l}=N,t=t.children||t.childrenField;let o=[];return j.treeConfig?Il().eachTree(l,e=>{o.push(e)},{children:t}):o=r,z.setRowExpand(o,e)},setRowExpand(e,t){let r=U.expandColumn,{fullAllDataRowIdData:l,rowExpandedMaps:o,rowExpandLazyLoadedMaps:a}=N,n=Nl(ie).handleGetRowId;let{reserve:i,lazy:s,accordion:d,toggleMethod:c}=ae.value,u=[],p=r?ie.getColumnIndex(r):-1,h=r?ie.getVMColumnIndex(r):-1;return e&&(Il().isArray(e)||(e=[e]),d&&(o={},N.rowExpandedMaps=o,e=e.slice(e.length-1,e.length)),e=c?e.filter(e=>c({$table:ie,expanded:t,column:r,columnIndex:p,$columnIndex:h,row:e,rowIndex:ie.getRowIndex(e),$rowIndex:ie.getVMRowIndex(e)})):e,t?e.forEach(e=>{var t,r=n(e);o[r]||(t=l[r],s&&!t.expandLoaded&&!a[r]?u.push(lr(e)):o[r]=e)}):e.forEach(e=>{e=n(e);o[e]&&delete o[e]}),i)&&e.forEach(e=>or(e,t)),U.rowExpandedFlag++,Promise.all(u).then(()=>(0,Al.nextTick)()).then(()=>ie.recalculate(!0)).then(()=>(L(),Mr(),kr(),ie.updateCellAreas()))},isRowExpandByRow(e){var t=U.rowExpandedFlag,r=N.rowExpandedMaps,e=Pl(ie,e);return!!t&&!!r[e]},isExpandByRow(e){return ul("vxe.error.delFunc",["isExpandByRow","isRowExpandByRow"]),z.isRowExpandByRow(e)},clearRowExpand(){var e=N.tableFullData,t=ae.value.reserve;let r=ie.getRowExpandRecords();return N.rowExpandedMaps={},U.rowExpandedFlag++,t&&e.forEach(e=>or(e,!1)),(0,Al.nextTick)().then(()=>{if(r.length)return ie.recalculate(!0)}).then(()=>(L(),Mr(),kr(),ie.updateCellAreas()))},clearRowExpandReserve(){return N.rowExpandedReserveRowMap={},(0,Al.nextTick)()},getRowExpandRecords(){let t=[];return Il().each(N.rowExpandedMaps,e=>{e&&t.push(e)}),t},setRowGroups(e){var{aggregateConfig:t,rowGroupConfig:r}=j;return t||r?e?(sr((Il().isArray(e)?e:[e]).map(e=>Il().isString(e)?e:e.field)),ur(N.tableSynchData,!0)):(0,Al.nextTick)():(pl("vxe.error.reqProp",["aggregate-config"]),(0,Al.nextTick)())},clearRowGroups(){var{aggregateConfig:e,rowGroupConfig:t}=j;return e||t?(sr([]),ur(N.tableSynchData,!0)):(pl("vxe.error.reqProp",["aggregate-config"]),(0,Al.nextTick)())},isRowGroupRecord(e){return ul("vxe.error.delFunc",["isRowGroupRecord","isAggregateRecord"]),ie.isAggregateRecord(e)},isRowGroupExpandByRow(e){return ul("vxe.error.delFunc",["isRowGroupExpandByRow","isAggregateExpandByRow"]),ie.isAggregateExpandByRow(e)},isAggregateRecord(e){var t=U.isRowGroupStatus;return t&&e.isAggregate},isAggregateExpandByRow(e){var t=U.rowGroupExpandedFlag,r=N.rowGroupExpandedMaps;return!!t&&!!r[Pl(ie,e)]},setRowGroupExpand(e,t){return e?(Il().isArray(e)||(e=[e]),Tr(e,t)):(0,Al.nextTick)()},setAllRowGroupExpand(e){var t=N.tableFullGroupData;let r=M.value.mapChildrenField,l={};return e&&r&&Il().eachTree(t,e=>{e[r]&&e[r].length&&(l[Pl(ie,e)]=e)},{children:r}),N.rowGroupExpandedMaps=l,i(),U.rowGroupExpandedFlag++,ie.handleTableData()},clearRowGroupExpand(){return N.rowGroupExpandedMaps={},i(),U.rowGroupExpandedFlag++,ie.handleTableData()},getTreeExpandRecords(){let t=[];return Il().each(N.treeExpandedMaps,e=>{e&&t.push(e)}),t},isTreeExpandLoaded(e){var t=N.fullAllDataRowIdData,t=t[Pl(ie,e)];return t&&!!t.treeLoaded},clearTreeExpandLoaded(e){let{fullAllDataRowIdData:r,treeExpandedMaps:l}=N;var t=ne.value.transform;return e?(e=Il().isArray(e)?e:[e]).forEach(e=>{var e=Pl(ie,e),t=r[e];t&&(t.treeLoaded=!1,l[e])&&delete l[e]}):Il().each(r,e=>{e.treeLoaded=!1}),N.treeExpandedMaps={},t&&(i(),ie.handleTableData()),U.treeExpandedFlag++,(0,Al.nextTick)()},reloadTreeExpand(e){var t=N.treeExpandLazyLoadedMaps,r=ne.value,l=r.hasChild||r.hasChildField;let{transform:o,lazy:a}=r;r=Pl(ie,e);return a&&e[l]&&!t[r]?ie.clearTreeExpandLoaded(e).then(()=>tr(e)).then(()=>{o&&(i(),ie.handleTableData()),U.treeExpandedFlag++}).then(()=>ie.recalculate()):(0,Al.nextTick)()},reloadTreeChilds(e){return ul("vxe.error.delFunc",["reloadTreeChilds","reloadTreeExpand"]),ie.reloadTreeExpand(e)},toggleTreeExpand(e){return ie.setTreeExpand(e,!ie.isTreeExpandByRow(e))},setAllTreeExpand(e){var t=N.tableFullData,r=ne.value;let{transform:l,lazy:o}=r,a=r.children||r.childrenField,n=[];return Il().eachTree(t,e=>{var t=e[a];(o||t&&t.length)&&n.push(e)},{children:a}),ie.setTreeExpand(n,e).then(()=>{if(l)return i(),U.treeExpandedFlag++,ie.recalculate()})},setTreeExpand(e,t){var r,l=ne.value.transform;return e&&(e=Il().isArray(e)?e:[e]).length?l?(l=e,r=t,yr(l,r).then(()=>(i(),ie.handleTableData(),U.treeExpandedFlag++,qt(),(0,Al.nextTick)())).then(()=>ie.recalculate(!0)).then(()=>{setTimeout(()=>{ie.updateCellAreas()},30)})):yr(e,t):(0,Al.nextTick)()},isTreeExpandByRow(e){var t=U.treeExpandedFlag,r=N.treeExpandedMaps;return!!t&&!!r[Pl(ie,e)]},clearTreeExpand(){var e=N.tableFullTreeData,t=ne.value,r=t.children||t.childrenField;let{transform:l,reserve:o}=t,a=ie.getTreeExpandRecords();return N.treeExpandedMaps={},o&&Il().eachTree(e,e=>rr(e,!1),{children:r}),ie.handleTableData().then(()=>{l&&(i(),ie.handleTableData()),U.treeExpandedFlag++}).then(()=>{if(a.length)return ie.recalculate()})},clearTreeExpandReserve(){return N.treeExpandedReserveRowMap={},(0,Al.nextTick)()},getScroll(){var{scrollXLoad:e,scrollYLoad:t}=U,r=N.elemStore,r=Wl(r["main-body-scroll"]);return{virtualX:e,virtualY:t,scrollTop:r?r.scrollTop:0,scrollLeft:r?r.scrollLeft:0}},scrollTo(e,t){var r=N.elemStore,l=Wl(r["main-header-scroll"]),o=Wl(r["main-body-scroll"]),a=Wl(r["main-footer-scroll"]),n=Wl(r["left-body-scroll"]),r=Wl(r["right-body-scroll"]),i=S.value,s=D.value;return N.intoRunScroll=!0,Il().isNumber(e)&&(Hl(i,e),Hl(o,e),Hl(l,e),Hl(a,e),vr()),Il().isNumber(t)&&(_l(s,t),_l(o,t),_l(n,t),_l(r,t),Rr()),U.scrollXLoad||U.scrollYLoad?new Promise(e=>{setTimeout(()=>{(0,Al.nextTick)(()=>{N.intoRunScroll=!1,e()})},30)}):(0,Al.nextTick)().then(()=>{N.intoRunScroll=!1})},scrollToRow(e,t){let{isAllOverflow:r,scrollYLoad:l,scrollXLoad:o}=U;var a,n,i=[];return e&&(j.treeConfig?i.push(ie.scrollToTreeRow(e)):i.push(go(ie,e))),t&&i.push((t=t,a=e,n=N.fullColumnIdData,(t=zl(ie,t))&&n[t.id]?mo(ie,t,a):(0,Al.nextTick)())),Promise.all(i).then(()=>{if(e)return r||!l&&!o||(Wt(),zt()),(0,Al.nextTick)()})},scrollToColumn(e){var t=N.fullColumnIdData,e=zl(ie,e);return e&&t[e.id]?mo(ie,e):(0,Al.nextTick)()},clearScroll(){var{elemStore:e,scrollXStore:t,scrollYStore:r}=N,l=Wl(e["main-header-scroll"]),o=Wl(e["main-body-scroll"]),a=Wl(e["main-footer-scroll"]),n=Wl(e["left-body-scroll"]),e=Wl(e["right-body-scroll"]),i=S.value,s=D.value;return N.intoRunScroll=!0,Hl(i,0),Hl(o,0),Hl(l,0),Hl(a,0),_l(s,0),_l(o,0),_l(n,0),_l(e,0),t.startIndex=0,t.visibleStartIndex=0,t.endIndex=t.visibleSize,t.visibleEndIndex=t.visibleSize,r.startIndex=0,r.visibleStartIndex=0,r.endIndex=r.visibleSize,r.visibleEndIndex=r.visibleSize,(0,Al.nextTick)().then(()=>{N.intoRunScroll=!1})},updateFooter(){var{showFooter:e,footerData:t,footerMethod:r}=j,{visibleColumn:l,afterFullData:o}=N;let a=[];return e&&t&&t.length?a=t.slice(0):e&&r&&(a=l.length?r({columns:l,data:o,$table:ie,$grid:Y}):[]),U.footerTableData=a,ie.handleUpdateFooterMerge(),(0,Al.nextTick)()},updateStatus(t,r){return(0,Al.nextTick)().then(()=>{var e=j.editRules;if(t&&e)return ie.handleCellRuleUpdateStatus("change",t,r)})},setMergeCells(e){return j.spanMethod&&pl("vxe.error.errConflicts",["merge-cells","span-method"]),(t=>{let{fullAllDataRowIdData:i,fullColumnIdData:s,visibleColumn:d,afterFullData:c,mergeBodyList:u,mergeBodyMaps:p}=N;if(t){let e=Nl(ie).handleGetRowId;(t=Il().isArray(t)?t:[t]).forEach(t=>{var{row:t,col:r,rowspan:l,colspan:o}=t;let a=-1,n=-1;if(Il().isNumber(t)?a=t:(t=(t=t?e(t):null)?i[t]:null)&&(a=t._index),Il().isNumber(r)?n=r:(r=(t=r?r.id:null)?s[t]:null)&&(n=r._index),-1<a&&-1<n&&(l||o)&&(l=Il().toNumber(l)||1,o=Il().toNumber(o)||1,1<l||1<o)){t=c[a],r=d[n];let e=p[a+":"+n];e?(e.rowspan=l,e.colspan=o,e._rowspan=l,e._colspan=o):(e={row:a,col:n,rowspan:l,colspan:o,_row:t,_col:r,_rowspan:l,_colspan:o},p[a+":"+n]=e,u.push(e))}})}})(e),ie.handleUpdateBodyMerge(),(0,Al.nextTick)().then(()=>(ie.updateCellAreas(),A()))},removeMergeCells(e){j.spanMethod&&pl("vxe.error.errConflicts",["merge-cells","span-method"]);let t=(e=>{let{mergeBodyList:a,fullColumnIdData:n,fullAllDataRowIdData:i,mergeBodyMaps:s}=N,d=[];if(e){let o=Nl(ie).handleGetRowId;(e=Il().isArray(e)?e:[e]).forEach(e=>{var{row:e,col:t}=e;let r=-1,l=-1;Il().isNumber(e)?r=e:(e=(e=e?o(e):null)?i[e]:null)&&(r=e._index),Il().isNumber(t)?l=t:(t=(e=t?t.id:null)?n[e]:null)&&(l=t._index);t=Il().findIndexOf(a,e=>e.row===r&&e.col===l);if(-1<t){t=a.splice(t,1);let e=t[0];e&&(d.push(t[0]),s[r+":"+l])&&delete s[r+":"+l]}})}return d})(e);return ie.handleUpdateBodyMerge(),(0,Al.nextTick)().then(()=>(ie.updateCellAreas(),A(),t))},getMergeCells(){return N.mergeBodyList.slice(0)},clearMergeCells(){return N.mergeBodyList=[],N.mergeBodyMaps={},N.mergeBodyCellMaps={},U.mergeBodyFlag++,(0,Al.nextTick)().then(()=>A())},setMergeFooterItems(e){return j.footerSpanMethod&&pl("vxe.error.errConflicts",["merge-footer-items","footer-span-method"]),(e=>{let s=U.footerTableData,{mergeFooterList:d,mergeFooterMaps:c,fullColumnIdData:u}=N;if(e){let i=N.visibleColumn;(e=Il().isArray(e)?e:[e]).forEach(t=>{var{row:t,col:r,rowspan:l,colspan:o}=t,t=Il().isNumber(t)?t:-1;let a=-1;if(Il().isNumber(r)?a=r:(r=(r=r?r.id:null)?u[r]:null)&&(a=r._index),-1<t&&-1<a&&(l||o)&&(l=Il().toNumber(l)||1,o=Il().toNumber(o)||1,1<l||1<o)){var r=s[t],n=i[a];let e=c[t+":"+a];e?(e.rowspan=l,e.colspan=o,e._rowspan=l,e._colspan=o):(e={row:t,col:a,rowspan:l,colspan:o,_row:r,_col:n,_rowspan:l,_colspan:o},c[t+":"+a]=e,d.push(e))}})}})(e),ie.handleUpdateFooterMerge(),(0,Al.nextTick)().then(()=>(z.updateCellAreas(),A()))},removeMergeFooterItems(e){j.footerSpanMethod&&pl("vxe.error.errConflicts",["merge-footer-items","footer-span-method"]);let t=(e=>{let{mergeFooterList:o,fullColumnIdData:a,mergeFooterMaps:n}=N,i=[];return e&&(e=Il().isArray(e)?e:[e]).forEach(e=>{var{row:e,col:t}=e;let r=Il().isNumber(e)?e:-1,l=-1;Il().isNumber(t)?l=t:(t=(e=t?t.id:null)?a[e]:null)&&(l=t._index);t=Il().findIndexOf(o,e=>e.row===r&&e.col===l);if(-1<t){let e=o.splice(t,1)[0];e&&(i.push(e),n[r+":"+l])&&delete n[r+":"+l]}}),i})(e);return ie.handleUpdateFooterMerge(),(0,Al.nextTick)().then(()=>(z.updateCellAreas(),A(),t))},getMergeFooterItems(){return N.mergeFooterList.slice(0)},clearMergeFooterItems(){return N.mergeFooterList=[],N.mergeFooterMaps={},N.mergeFooterCellMaps={},U.mergeFootFlag++,(0,Al.nextTick)().then(()=>A())},updateCellAreas(){var e=j.mouseConfig,t=le.value;return e&&t.area&&ie.handleRecalculateCellAreaEvent?ie.handleRecalculateCellAreaEvent():(0,Al.nextTick)()},getCustomStoreData(){var e=j.id,t=ut.value,r=N.collectColumn;let{storage:l,checkMethod:c,storeOptions:o}=t;var t=!0===l,a=t?{}:Object.assign({},l||{},o);let u=b(a.resizable,t),p=b(a.visible,t),h=b(a.fixed,t),g=b(a.sort,t),m={},v={},f={},x={};a={resizableData:void 0,sortData:void 0,visibleData:void 0,fixedData:void 0};if(e){let n=0,i=0,s=0,d=0;Il().eachTree(r,(e,t,r,l,o)=>{var a=e.getKey();a?(o||(g&&(i=1,v[a]=e.renderSortNumber),h&&e.fixed!==e.defaultFixed&&(s=1,x[a]=e.fixed)),u&&e.resizeWidth&&(n=1,m[a]=e.renderWidth),!p||c&&!c({$table:ie,column:e})||(!e.visible&&e.defaultVisible?(d=1,f[a]=!1):e.visible&&!e.defaultVisible&&(d=1,f[a]=!0))):pl("vxe.error.reqProp",[`${e.getTitle()||e.type||""} -> column.field=?`])}),n&&(a.resizableData=m),i&&(a.sortData=v),s&&(a.fixedData=x),d&&(a.visibleData=f)}else pl("vxe.error.reqProp",["id"]);return a},focus(){return N.isActivated=!0,(0,Al.nextTick)()},blur(){return(N.isActivated=!1,Al.nextTick)()},connect(e){return e?(y=e).syncUpdate({collectColumn:N.collectColumn,$table:ie}):pl("vxe.error.barUnableLink"),(0,Al.nextTick)()}},r=>{var{editStore:e,ctxMenuStore:t,filterStore:l,customStore:o}=U;let{mouseConfig:a,editRules:n}=j,i=G.value,s=re.value,d=Ve.value;var c=Xe.value;let u=e.actived;var e=fe.value,p=be.value,h=Ce.value,g=xe.value;if(!p||Bl(r,i,"vxe-cell--filter").flag||Bl(r,p.getRefMaps().refElem.value).flag||Bl(r,document.body,"vxe-table--ignore-clear").flag||W.preventEvent(r,"event.clearFilter",l.args,z.closeFilter),!h||o.btnEl===r.target||Bl(r,document.body,"vxe-toolbar-custom-target").flag||Bl(r,h.$el).flag||Bl(r,document.body,"vxe-table--ignore-clear").flag||W.preventEvent(r,"event.clearCustom",{},()=>{ie.closeCustom&&ie.closeCustom()}),u.row)!1===s.autoClear||(p=u.args.cell)&&Bl(r,p).flag||e&&Bl(r,e.$el).flag||N._lastCallTime&&!(N._lastCallTime+50<Date.now())||Bl(r,document.body,"vxe-table--ignore-clear").flag||W.preventEvent(r,"event.clearEdit",u.args,()=>{let e;var t;(e=(e=(e="row"===s.mode?!!(t=(t=Bl(r,i,"vxe-body--row")).flag?z.getRowNode(t.targetElem):null)&&!ie.eqRow(t.item,u.args.row):!Bl(r,i,"col--edit").flag)||Bl(r,i,"vxe-header--row").flag)||Bl(r,i,"vxe-footer--row").flag)||!j.height||U.overflowY||Ll(t=r.target,"vxe-table--body-wrapper")&&(e=r.offsetY<t.clientHeight),!e&&Bl(r,i).flag||setTimeout(()=>{ie.handleClearEdit(r).then(()=>{!N.isActivated&&n&&d.autoClear&&(U.validErrorMaps={})})})});else if(a&&!(Bl(r,i).flag||Y&&Bl(r,Y.getRefMaps().refElem.value).flag||g&&Bl(r,g.getRefMaps().refElem.value).flag||y&&Bl(r,y.getRefMaps().refElem.value).flag)&&(ie.clearSelected&&ie.clearSelected(),c.autoClear)&&ie.getCellAreas){let e=ie.getCellAreas();e&&e.length&&!Bl(r,document.body,"vxe-table--ignore-areas-clear").flag&&W.preventEvent(r,"event.clearAreas",{},()=>{ie.clearCellAreas(),ie.clearCopyCellArea(),se("clear-cell-area-selection",{cellAreas:e},r)})}ie.closeMenu&&t.visible&&g&&!Bl(r,g.getRefMaps().refElem.value).flag&&ie.closeMenu();l=Bl(r,Y?Y.getRefMaps().refElem.value:i).flag;!l&&n&&d.autoClear&&(U.validErrorMaps={}),N.isActivated=l}),Ar=()=>{z.closeFilter(),ie.closeMenu&&ie.closeMenu()},Lr=()=>{z.closeTooltip(),ie.closeMenu&&ie.closeMenu()},Vr=t=>{let{mouseConfig:e,keyboardConfig:r}=j,{filterStore:l,ctxMenuStore:o,editStore:a}=U,n=le.value,i=oe.value,s=a.actived;ml.hasKey(t,vl.ESCAPE)&&W.preventEvent(t,"event.keydown",null,()=>{if(se("keydown-start",{},t),r&&e&&n.area&&ie.handleKeyboardCellAreaEvent)ie.handleKeyboardCellAreaEvent(t);else if((s.row||l.visible||o.visible)&&(t.stopPropagation(),ie.closeMenu&&ie.closeMenu(),z.closeFilter(),r)&&i.isEsc&&s.row){let e=s.args;ie.handleClearEdit(t),n.selected&&(0,Al.nextTick)(()=>ie.handleSelected(e,t))}se("keydown",{},t),se("keydown-end",{},t)})},$r=P=>{N.isActivated&&ie.preventEvent(P,"event.keydown",null,()=>{var{mouseConfig:e,keyboardConfig:t,treeConfig:r,editConfig:l,highlightCurrentRow:o,highlightCurrentColumn:a}=j;let{ctxMenuStore:n,editStore:$,currentRow:i}=U;var s=N.afterFullData,d=lt.value,c=tt.value,u=oe.value,p=le.value,h=re.value,g=ne.value,m=ot.value,v=Q.value,_=Z.value,{selected:f,actived:x}=$,g=g.children||g.childrenField,b=P.keyCode,C=ml.hasKey(P,vl.ESCAPE),w=ml.hasKey(P,vl.BACKSPACE),H=ml.hasKey(P,vl.TAB),y=ml.hasKey(P,vl.ENTER),T=ml.hasKey(P,vl.SPACEBAR),E=ml.hasKey(P,vl.ARROW_LEFT),R=ml.hasKey(P,vl.ARROW_UP),S=ml.hasKey(P,vl.ARROW_RIGHT),D=ml.hasKey(P,vl.ARROW_DOWN),B=ml.hasKey(P,vl.DELETE),I=ml.hasKey(P,vl.F2),F=ml.hasKey(P,vl.CONTEXT_MENU),M=eo(P),k=P.shiftKey,O=P.altKey,A=E||R||S||D,d=d&&n.visible&&(y||T||A),L=Fl(l)&&x.column&&x.row;let V=h.beforeEditMethod||h.activeMethod;if(d)P.preventDefault(),n.showChild&&Kl(n.selected)?ie.moveCtxMenu(P,n,"selectChild",E,!1,n.selected.children):ie.moveCtxMenu(P,n,"selected",S,!0,m);else if(t&&e&&p.area&&ie.handleKeyboardCellAreaEvent)ie.handleKeyboardCellAreaEvent(P);else if(C){if(ie.closeMenu&&ie.closeMenu(),ie.closeFilter(),t&&u.isEsc&&x.row){let e=x.args;ie.handleClearEdit(P),p.selected&&(0,Al.nextTick)(()=>ie.handleSelected(e,P))}}else if(T&&t&&u.isChecked&&f.row&&f.column&&("checkbox"===f.column.type||"radio"===f.column.type))P.preventDefault(),"checkbox"===f.column.type?W.handleToggleCheckRowEvent(P,f.args):W.triggerRadioRowEvent(P,f.args);else if(I&&Fl(l))L||f.row&&f.column&&(P.preventDefault(),ie.handleEdit(f.args,P));else if(F)N._keyCtx=f.row&&f.column&&c.length,clearTimeout(N.keyCtxTimeout),N.keyCtxTimeout=setTimeout(()=>{N._keyCtx=!1},1e3);else if(y&&!O&&t&&u.isEnter&&(f.row||x.row||r&&(v.isCurrent||o)&&i)){var{isLastEnterAppendRow:d,beforeEnterMethod:m,enterMethod:e}=u;if(M){if(x.row){let e=x.args;ie.handleClearEdit(P),p.selected&&(0,Al.nextTick)(()=>{ie.handleSelected(e,P)})}}else if(f.row||x.row){let r=(f.row?f:x).args;if(k)u.enterToTab?ie.moveTabSelected(r,k,P):ie.moveEnterSelected(r,E,!0,S,!1,P);else if(u.enterToTab)ie.moveTabSelected(r,k,P);else{var C=f.row||x.row;let t=f.column||x.column;I=ie.getVTRowIndex(C),F={row:C,rowIndex:ie.getRowIndex(C),$rowIndex:ie.getVMRowIndex(C),_rowIndex:I,column:t,columnIndex:ie.getColumnIndex(t),$columnIndex:ie.getVMColumnIndex(t),_columnIndex:ie.getVTColumnIndex(t),$table:ie};if(!m||!1!==m(F)){if(d&&I>=s.length-1)return ie.insertAt({},-1).then(({row:e})=>{ie.scrollToRow(e,t),ie.handleSelected({...r,row:e},P)}),void ie.dispatchEvent("enter-append-row",F,P);ie.moveEnterSelected(r,E,!1,S,!0,P),e&&e(F)}}}else if(r&&(v.isCurrent||o)&&i){c=i[g];if(c&&c.length){P.preventDefault();let e=c[0],t={$table:ie,row:e,rowIndex:ie.getRowIndex(e),$rowIndex:ie.getVMRowIndex(e)};ie.setTreeExpand(i,!0).then(()=>ie.scrollToRow(e)).then(()=>ie.triggerCurrentRowEvent(P,t))}}}else if(A&&t&&u.isArrow)L||(p.selected&&f.row&&f.column?ie.moveArrowSelected(f.args,E,R,S,D,P):((R||D)&&(v.isCurrent||o)&&ie.moveCurrentRow(R,D,P),(E||S)&&(_.isCurrent||a)&&ie.moveCurrentColumn(E,S,P)));else if(H&&t&&u.isTab)f.row||f.column?ie.moveTabSelected(f.args,k,P):(x.row||x.column)&&ie.moveTabSelected(x.args,k,P);else if(t&&u.isDel&&B&&Fl(l)&&(f.row||f.column))L||(y=u.delMethod,O={row:f.row,rowIndex:z.getRowIndex(f.row),column:f.column,columnIndex:z.getColumnIndex(f.column),$table:ie,$grid:Y},V&&!V(O))||(y?y(O):Ul(f.row,f.column,null),z.updateFooter(),se("cell-delete-value",O,P));else if(w&&t&&u.isBack&&Fl(l)&&(f.row||f.column))L||(C=u.backMethod,u.isDel&&Fl(l)&&(f.row||f.column)&&(m={row:f.row,rowIndex:ie.getRowIndex(f.row),column:f.column,columnIndex:ie.getColumnIndex(f.column),$table:ie,$grid:Y},V&&!V(m)||(C?C(m):(Ul(f.row,f.column,null),ie.handleEdit(f.args,P)),se("cell-backspace-value",m,P))));else if(w&&t&&r&&u.isBack&&(v.isCurrent||o)&&i){let t=Il().findTree(N.afterTreeFullData,e=>e===i,{children:g}).parent;if(t){P.preventDefault();let e={row:t,rowIndex:ie.getRowIndex(t),$rowIndex:ie.getVMRowIndex(t),$table:ie,$grid:Y};ie.setTreeExpand(t,!1).then(()=>ie.scrollToRow(t)).then(()=>ie.triggerCurrentRowEvent(P,e))}}else if(t&&Fl(l)&&u.isEdit&&!M&&(T||48<=b&&b<=57||65<=b&&b<=90||96<=b&&b<=111||186<=b&&b<=192||219<=b&&b<=222)){var{editMode:d,editMethod:I}=u;if(f.column&&f.row&&Fl(f.column.editRender)){let e=h.beforeEditMethod||h.activeMethod;s={row:f.row,rowIndex:ie.getRowIndex(f.row),column:f.column,columnIndex:ie.getColumnIndex(f.column),$table:ie,$grid:Y};e&&!e({...f.args,$table:ie,$grid:Y})||(I?I(s):("insert"!==d&&Ul(f.row,f.column,null),ie.handleEdit(f.args,P)))}}se("keydown",{},P)})},_r=e=>{var{keyboardConfig:t,mouseConfig:r}=j,{editStore:l,filterStore:o}=U,a=N.isActivated,n=le.value,i=oe.value,l=l.actived;a&&!o.visible&&(l.row||l.column||t&&i.isClip&&r&&n.area&&ie.handlePasteCellAreaEvent&&ie.handlePasteCellAreaEvent(e),se("paste",{},e))},Hr=e=>{var{keyboardConfig:t,mouseConfig:r}=j,{editStore:l,filterStore:o}=U,a=N.isActivated,n=le.value,i=oe.value,l=l.actived;a&&!o.visible&&(l.row||l.column||t&&i.isClip&&r&&n.area&&ie.handleCopyCellAreaEvent&&ie.handleCopyCellAreaEvent(e),se("copy",{},e))},Br=e=>{var{keyboardConfig:t,mouseConfig:r}=j,{editStore:l,filterStore:o}=U,a=N.isActivated,n=le.value,i=oe.value,l=l.actived;a&&!o.visible&&(l.row||l.column||t&&i.isClip&&r&&n.area&&ie.handleCutCellAreaEvent&&ie.handleCutCellAreaEvent(e),se("cut",{},e))},Pr=()=>{ie.closeMenu&&ie.closeMenu();var e=G.value;if(!e||!e.clientWidth)return(0,Al.nextTick)();z.recalculate(!0),z.updateCellAreas()},Nr=e=>{var t=q.value;clearTimeout(N.tooltipTimeout),e?z.closeTooltip():t&&t.setActived&&t.setActived(!0)},zr=()=>{var{dragRow:e,dragCol:t}=U;(e||t)&&(Ur(),jr(),Gr(),U.dragRow=null,U.dragCol=null,U.isDragColMove=!1,U.isDragRowMove=!1)},jr=()=>{var e=G.value;if(e){let t="row--drag-origin";Il().arrayEach(e.querySelectorAll("."+t),e=>{e.draggable=!1,Vl(e,t)})}},Ur=()=>{var e=G.value;if(e){let t="col--drag-origin";Il().arrayEach(e.querySelectorAll("."+t),e=>{e.draggable=!1,Vl(e,t)})}},Wr=(e,r,l,t,o)=>{var a=G.value;if(a){var{overflowX:n,scrollbarWidth:i,overflowY:s,scrollbarHeight:d}=U,c=N.prevDragToChild,u=a.getBoundingClientRect(),s=s?i:0,i=n?d:0,n=a.clientWidth,d=a.clientHeight;if(r){var p=Re.value;if(p)if(t){var h=_e.value,g=r.getBoundingClientRect();let e=r.clientHeight;r=Math.max(1,g.y-u.y);r+e>d-i&&(e=d-r-i),p.style.display="block",p.style.left=`${h?s:0}px`,p.style.top=r+"px",p.style.height=e+"px",p.style.width=n-s+"px",p.setAttribute("drag-pos",o),p.setAttribute("drag-to-child",c?"y":"n")}else p.style.display=""}else if(l){g=Se.value;if(g)if(t){var h=$e.value,r=P.value,p=r?r.clientWidth:0,r=X.value,r=r?r.clientWidth:0,m=l.getBoundingClientRect();let e=l.clientWidth;l=Math.max(0,m.y-u.y);let t=m.x-u.x;t<p&&(e-=p-t,t=p);p=n-r-(r?0:s);t+e>p&&(e=p-t),g.style.display="block",g.style.top=l+"px",g.style.left=t+"px",g.style.width=e+"px",g.style.height=c?m.height+"px":d-l-(h?0:i)+"px",g.setAttribute("drag-pos",o),g.setAttribute("drag-to-child",c?"y":"n")}else g.style.display=""}n=Ee.value;n&&(n.style.display="block",n.style.top=Math.min(a.clientHeight-a.scrollTop-n.clientHeight,e.clientY-u.y)+"px",n.style.left=Math.min(a.clientWidth-a.scrollLeft-n.clientWidth-16,e.clientX-u.x)+"px",n.setAttribute("drag-status",t?c?"sub":"normal":"disabled"))}},Gr=()=>{var e=Ee.value,t=Re.value,r=Se.value;e&&(e.style.display=""),t&&(t.style.display=""),r&&(r.style.display="")},qr=(e,l,o,a,n)=>{let i=o||l;if(i){n.cell=l;var o=U.tooltipStore,{column:l,row:s}=n,{showAll:d,contentMethod:c}=Ue.value,n=c?c(n):null,c=c&&!Il().eqNull(n);let t=c?n:Il().toString("html"===l.type?i.innerText:i.textContent).trim(),r=i.scrollWidth>i.clientWidth;t&&(d||c||r)&&(Object.assign(o,{row:s,column:l,visible:!0,currOpts:{}}),(0,Al.nextTick)(()=>{var e=q.value;e&&e.open&&e.open(r?i:a,kl(t))}))}return(0,Al.nextTick)()},Xr=(e,t)=>{if(e){if(Y)return Y.callSlot(e,t);if(Il().isFunction(e))return Gl(e(t))}return[]},Kr=(W={getSetupOptions(){return hl()},updateAfterDataIndex:qt,callSlot:Xr,getParentElem(){var e,t=G.value;return Y?(e=Y.getRefMaps().refElem.value)?e.parentNode:null:t?t.parentNode:null},getParentHeight(){var t=j.height,r=G.value;if(r){r=r.parentNode,t="100%"===t||"auto"===t?ro(r):0;let e=0;return r&&(e=Y&&Ll(r,"vxe-grid--table-wrapper")?Y.getParentHeight():r.clientHeight),Math.floor(e-t)}return 0},getExcludeHeight(){return Y?Y.getExcludeHeight():0},defineField(e){let t=j.treeConfig,r=ae.value,l=ne.value,a=k.value,n=te.value,i=l.children||l.childrenField,s=ao(ie);return(e=Il().isArray(e)?e:[e]).map(o=>(N.tableFullColumn.forEach(t=>{var{field:r,editRender:l}=t;if(r&&!Il().has(o,r)&&!o[r]){let e=null;l&&(l=l.defaultValue,Il().isFunction(l)?e=l({column:t}):Il().isUndefined(l)||(e=l)),Il().set(o,r,e)}}),[a.labelField,n.checkField,n.labelField,r.labelField].forEach(e=>{e&&Ol(Il().get(o,e))&&Il().set(o,e,null)}),t&&l.lazy&&Il().isUndefined(o[i])&&(o[i]=null),Ol(Il().get(o,s))&&Il().set(o,s,oo()),o))},handleTableData(e){var t=U.scrollYLoad;let{scrollYStore:r,fullDataRowIdData:o}=N,l=N.afterFullData;e&&((()=>{var e=j.treeConfig,t=U.isRowGroupStatus,{tableFullColumn:r,tableFullData:l,tableFullTreeData:o,tableFullGroupData:i}=N,s=qe.value,d=O.value,c=M.value,u=ne.value,p=u.children||u.childrenField,{transform:u,rowField:h,parentField:g,mapChildrenField:m}=u;let{isEvery:v,remote:f,filterMethod:x}=s,{remote:b,sortMethod:C,multiple:w,chronological:y}=d,T=[],E=[];if(f&&b)t?(E=Il().searchTree(i,()=>!0,{original:!0,isEvery:!0,children:c.mapChildrenField,mapChildren:c.childrenField}),T=E):e&&u?(E=Il().searchTree(o,()=>!0,{original:!0,isEvery:v,children:m,mapChildren:p}),T=E):(T=(e?o:l).slice(0),E=T);else{let a=[],n=[];r.forEach(e=>{var{field:t,sortable:r,order:l,filters:o}=e;if(!f&&o&&o.length){let t=[],r=[];o.forEach(e=>{e.checked&&(r.push(e),t.push(e.value))}),r.length&&a.push({column:e,valueList:t,itemList:r})}!b&&r&&l&&n.push({column:e,field:t,property:t,order:l,sortTime:e.sortTime})}),w&&y&&1<n.length&&(n=Il().orderBy(n,"sortTime")),!f&&a.length?(s=d=>a.every(({column:t,valueList:e,itemList:r})=>{let{filterMethod:l,filterRender:o}=t;var a=Fl(o)?bl.get(o.name):null;let n=a?a.tableFilterMethod||a.filterMethod:null,i=a?a.tableFilterDefaultMethod||a.defaultTableFilterMethod||a.defaultFilterMethod:null,s=jl(d,t);return l?r.some(e=>l({value:e.value,option:e,cellValue:s,row:d,column:t,$table:ie})):n?r.some(e=>n({value:e.value,option:e,cellValue:s,row:d,column:t,$table:ie})):x?x({$table:ie,options:r,values:e,cellValue:s,row:d,column:t}):i?r.some(e=>i({value:e.value,option:e,cellValue:s,row:d,column:t,$table:ie})):-1<e.indexOf(Il().get(d,t.field))}),t?(E=Il().searchTree(i,s,{original:!0,isEvery:!0,children:c.mapChildrenField,mapChildren:c.childrenField}),T=E):e&&u?(E=Il().searchTree(o,s,{original:!0,isEvery:v,children:m,mapChildren:p}),T=E):(T=(e?o:l).filter(s),E=T)):t?(E=Il().searchTree(i,()=>!0,{original:!0,isEvery:!0,children:c.mapChildrenField,mapChildren:c.childrenField}),T=E):e&&u?(E=Il().searchTree(o,()=>!0,{original:!0,isEvery:v,children:m,mapChildren:p}),T=E):(T=(e?o:l).slice(0),E=T),!b&&n.length&&(t?(E=C?(d=C({data:E,sortList:n,$table:ie}),Il().isArray(d)?d:E):(r=Il().toTreeArray(E,{key:c.rowField,parentKey:c.parentField,children:c.mapChildrenField}),Il().toArrayTree(Il().orderBy(r,n.map(({column:e,order:t})=>[Gt(e),t])),{key:c.rowField,parentKey:c.parentField,children:c.childrenField,mapChildren:c.mapChildrenField})),T=E):e&&u?(E=C?(s=C({data:E,sortList:n,$table:ie}),Il().isArray(s)?s:E):(i=Il().toTreeArray(E,{children:m}),Il().toArrayTree(Il().orderBy(i,n.map(({column:e,order:t})=>[Gt(e),t])),{key:h,parentKey:g,children:p,mapChildren:m})),T=E):(T=C?(o=C({data:T,sortList:n,$table:ie}),Il().isArray(o)?o:T):Il().orderBy(T,n.map(({column:e,order:t})=>[Gt(e),t])),E=T))}N.afterFullData=T,N.afterTreeFullData=E,N.afterGroupFullData=E,qt()})(),l=i());e=t?l.slice(r.startIndex,r.endIndex):l.slice(0);let a={};return e.forEach((e,t)=>{var r=Pl(ie,e),l=o[r];l&&(l.$index=t),a[r]=e}),U.tableData=e,N.visibleDataRowIdData=a,(0,Al.nextTick)()},cacheRowMap(e){let c=j.treeConfig;var t=U.isRowGroupStatus;let{fullAllDataRowIdData:r,tableFullData:l,tableFullTreeData:o,tableFullGroupData:a,treeExpandedMaps:u}=N,d=e?{}:{...r},p={},h=no(ie).handleUpdateRowId,g=(e,t,r,l,o,a,n,i)=>{let s=d[a];s||(s={row:e,rowid:a,seq:i,index:-1,_index:-1,$index:-1,treeIndex:t,items:r,parent:o,level:n,height:0,resizeHeight:0,oTop:0,expandHeight:0},p[a]=s,d[a]=s),s.treeLoaded=!1,s.expandLoaded=!1,s.row=e,s.items=r,s.parent=o,s.level=n,s.index=l,s.treeIndex=t,p[a]=s,d[a]=s};if(c){var e=ne.value;let i=e.lazy,s=e.children||e.childrenField,d=e.hasChild||e.hasChildField;Il().eachTree(o,(e,t,r,l,o,a)=>{var n=h(e);c&&i&&(e[d]&&void 0===e[s]&&(e[s]=null),!u[n]||e[s]&&e[s].length||delete u[n]),g(e,t,r,o?-1:t,o,n,a.length-1,co(l))},{children:s})}else t?(e=M.value.mapChildrenField,Il().eachTree(a,(e,t,r,l,o,a)=>{var n=h(e);g(e,t,r,o?-1:t,o,n,a.length-1,co(l))},{children:e})):l.forEach((e,t,r)=>{g(e,t,r,t,null,h(e),0,t+1)});N.fullDataRowIdData=p,N.fullAllDataRowIdData=d,U.treeExpandedFlag++},cacheSourceMap(e){var t=j.treeConfig,r=ne.value,e=Il().clone(e,!0);let l=no(ie).handleUpdateRowId,o={};var a=e=>{var t=l(e);o[t]=e};t?(t=r.children||r.childrenField,Il().eachTree(e,a,{children:r.transform?r.mapChildrenField:t})):e.forEach(a),N.sourceDataRowIdData=o,N.tableSourceData=e},analyColumnWidth(){var e=N.tableFullColumn;let{width:t,minWidth:r}=Z.value,l=[],o=[],a=[],n=[],i=[],s=[],d=[],c=[];e.forEach(e=>{t&&!e.width&&(e.width=t),r&&!e.minWidth&&(e.minWidth=r),e.visible&&(e.resizeWidth?l:"auto"===e.width?d:Ql(e.width)?o:Jl(e.width)?i:Ql(e.minWidth)?a:"auto"===e.minWidth?n:Jl(e.minWidth)?s:c).push(e)}),Object.assign(U.columnStore,{resizeList:l,pxList:o,pxMinList:a,autoMinList:n,scaleList:i,scaleMinList:s,autoList:d,remainList:c})},handleColResizeMousedownEvent(e,t,l){e.stopPropagation(),e.preventDefault();var r=l.column,{columnStore:o,overflowX:a,scrollbarHeight:n}=U;let{elemStore:i,visibleColumn:T}=N;var{leftList:o,rightList:E}=o;let R=ze.value,S=a?n:0,D=G.value,I=P.value;a=X.value;let F=we.value;if(F){let v="left"===t,f="right"===t,x=F.firstElementChild,b=$e.value,C=e.clientX;n=e.target;let w=r;r.children&&r.children.length&&Il().eachTree(r.children,e=>{w=e});t=n.parentNode,r=Object.assign(l,{cell:t});let y=0;if(Wl(i["main-body-scroll"])){let s=D.getBoundingClientRect(),d=a?a.getBoundingClientRect():null;var a=t.getBoundingClientRect(),M=n.getBoundingClientRect(),n=n.clientWidth,k=Math.floor(n/2);let c=M.x-s.x+k,u=po(r)-k,p=f?0:a.x-s.x+n+u,h=a.x-s.x+t.clientWidth-u,g=0,m=0;if(v||f){let t=!1;var O=v?o:E;for(let e=0;e<O.length;e++){var A=O[e];t?g+=A.renderWidth:(t=A.id===w.id)||(m+=A.renderWidth)}}M=t=>{t.stopPropagation(),t.preventDefault();var r=D.clientHeight,l=t.clientX-C;let e=c+l;v?d&&(e=Math.min(e,d.x-s.x-g-u)):f&&(I&&(e=Math.max(e,I.clientWidth+m+u)),e=Math.min(e,h)),y=Math.max(e,p);l=Math.max(1,y);if(F.style.left=l+"px",F.style.top=`${b?S:0}px`,F.style.height=`${b?r-S:r}px`,R.showDragTip&&x){x.textContent=gl("vxe.table.resizeColTip",[Math.floor(w.renderWidth+(f?c-y:y-c))]);var o=D.clientWidth,a=F.clientWidth,n=x.clientWidth,i=x.clientHeight;let e=-n;l<n+a?e=0:o<l&&(e+=o-l),x.style.left=e+"px",x.style.top=Math.min(r-i,Math.max(0,t.clientY-s.y-i/2))+"px"}U.isDragResize=!0};U.isDragResize=!0,$l(D,"col-drag--resize"),F.style.display="block",document.onmousemove=M,document.onmouseup=function(e){document.onmousemove=null,document.onmouseup=null,F.style.display="none",N._lastResizeTime=Date.now(),setTimeout(()=>{U.isDragResize=!1},50);var t=w.renderWidth+(f?c-y:y-c),r={...l,resizeWidth:t,resizeColumn:w};"fixed"===R.dragMode&&T.forEach(e=>{e.id===w.id||e.resizeWidth||(e.resizeWidth=e.renderWidth)}),ie.handleColResizeCellAreaEvent?ie.handleColResizeCellAreaEvent(e,r):(w.resizeWidth=t,Dr(e,r)),Vl(D,"col-drag--resize")},M(e),ie.closeMenu&&ie.closeMenu()}}},handleColResizeDblclickEvent(r,l){var o=ze.value.isDblclickAutoWidth,a=G.value;if(o&&a){r.stopPropagation(),r.preventDefault();var o=N.fullColumnIdData,n=l.column;let t=n;n.children&&n.children.length&&Il().eachTree(n.children,e=>{t=e});n=o[t.id],o=r.target.parentNode,o=po(Object.assign(l,{cell:o}));a.setAttribute("data-calc-col","Y");let e=Nt(t,a);a.removeAttribute("data-calc-col"),n&&(e=Math.max(e,n.width)),e=Math.max(o,e);a={...l,resizeWidth:e,resizeColumn:t};U.isDragResize=!1,N._lastResizeTime=Date.now(),ie.handleColResizeDblclickCellAreaEvent?ie.handleColResizeDblclickCellAreaEvent(r,a):(t.resizeWidth=e,Dr(r,a))}},handleRowResizeMousedownEvent(l,o){l.stopPropagation(),l.preventDefault();let a=o.row;var{overflowX:n,scrollbarWidth:i,overflowY:g,scrollbarHeight:m}=U,{elemStore:v,fullAllDataRowIdData:f}=N;let x=g?i:0,b=n?m:0,C=_e.value,w=ze.value;g=Q.value,i=F.value;let y=G.value,T=ye.value;if(T){let p=l.clientY,h=T.firstElementChild;n=l.currentTarget.parentNode,m=n.parentNode;if(Wl(v["main-body-scroll"])){let r=f[Pl(ie,a)];if(r){v=I.value;let s=r.resizeHeight||i.height||g.height||r.height||v,d=y.getBoundingClientRect();f=m.getBoundingClientRect();let e=p-f.y-m.clientHeight,c=s;i=n.querySelector(".vxe-cell");let t=0,u=(i&&(g=getComputedStyle(i),t=Math.max(1,Math.ceil(Il().toNumber(g.paddingTop)+Il().toNumber(g.paddingBottom)))),f.y-d.y+t);v=r=>{r.stopPropagation(),r.preventDefault();var l=y.clientWidth-x,o=y.clientHeight-b;let a=r.clientY-d.y-e;if(a<u?a=u:c=Math.max(t,s+r.clientY-p),T.style.left=`${C?x:0}px`,T.style.top=a+"px",T.style.width=l+"px",w.showDragTip&&h){h.textContent=gl("vxe.table.resizeRowTip",[c]);var n=h.clientWidth,i=h.clientHeight;let e=Math.max(2,r.clientX-d.x),t=0;e+n>=l-2&&(e=l-n-2),a+i>=o&&(t=o-(a+i)),h.style.left=e+"px",h.style.top=t+"px"}U.isDragResize=!0};U.isDragResize=!0,$l(y,"row-drag--resize"),T.style.display="block",document.onmousemove=v,document.onmouseup=function(e){var t;document.onmousemove=null,document.onmouseup=null,T.style.display="none",N._lastResizeTime=Date.now(),setTimeout(()=>{U.isDragResize=!1},50),c!==s&&(t={...o,resizeHeight:c,resizeRow:a},N.isResizeCellHeight=!0,ie.handleRowResizeCellAreaEvent?ie.handleRowResizeCellAreaEvent(e,t):(r.resizeHeight=c,Ir(e,t),L())),Vl(y,"row-drag--resize")},v(l)}}}},handleRowResizeDblclickEvent(o,a){var e=ze.value.isDblclickAutoHeight;let n=G.value;if(e&&n){o.stopPropagation(),o.preventDefault();var e=U.editStore,t=N.fullAllDataRowIdData,e=e.actived;let r=a.row,l=t[Pl(ie,r)];l&&(t=()=>{n.setAttribute("data-calc-row","Y");var e=Ut(l,n),t=(n.removeAttribute("data-calc-row"),{...a,resizeHeight:e,resizeRow:r});U.isDragResize=!1,N._lastResizeTime=Date.now(),ie.handleRowResizeDblclickCellAreaEvent?ie.handleRowResizeDblclickCellAreaEvent(o,t):(l.resizeHeight=e,Ir(o,t))},e.row||e.column?ie.clearEdit().then(t):t())}},saveCustomStore(e){var t=j.customConfig,r=T.value,l=ut.value,{updateStore:o,storage:a,storeOptions:n}=l,i=!0===a,a=i?{}:Object.assign({},a||{},n),n=b(a.resizable,i),s=b(a.visible,i),d=b(a.fixed,i),a=b(a.sort,i);if("reset"!==e&&(U.isCustomStatus=!0),(t?Fl(l):l.enabled)&&(n||s||d||a)){if(!r)return pl("vxe.error.reqProp",["id"]),(0,Al.nextTick)();i="reset"===e?{resizableData:{},sortData:{},visibleData:{},fixedData:{}}:z.getCustomStoreData();if(o)return o({$table:ie,id:r,type:e,storeData:i});t=r,l="reset"===e?null:i,n=hl().version,(s=St())[t]=l||void 0,s._v=n,localStorage.setItem(Sl,Il().toJSONString(s))}return(0,Al.nextTick)()},handleCustom(){var e=j.mouseConfig;return e&&(ie.clearSelected&&ie.clearSelected(),ie.clearCellAreas)&&(ie.clearCellAreas(),ie.clearCopyCellArea()),W.analyColumnWidth(),z.refreshColumn(!0)},handleUpdateDataQueue(){U.upDataFlag++},handleRefreshColumnQueue(){U.reColumnFlag++},preventEvent(t,e,r,l,o){let a=Tl.get(e),n=(a.length||"event.clearEdit"!==e||(a=Tl.get("event.clearActived")).length&&ul("vxe.error.delEvent",["event.clearActived","event.clearEdit"]),null),i=!1;for(let e=0;e<a.length;e++){var s=(0,a[e])(Object.assign({$grid:Y,$table:ie,$event:t},r));if(!1===s){i=!0;break}if(s&&!1===s.status){n=s.result,i=!0;break}}return i||l&&(n=l()),o&&o(),n},updateCheckboxStatus(){var e=j.treeConfig,t=U.isRowGroupStatus;let{afterTreeFullData:r,afterGroupFullData:o,selectCheckboxMaps:s,treeIndeterminateRowMaps:d}=N;var c=M.value,u=ne.value;let p=u.children||u.childrenField,{checkField:h,checkStrictly:l,checkMethod:g}=te.value;if(!l){if(t||e){let i=Nl(ie).handleGetRowId,a={},n=[];if(t){let l=c.mapChildrenField;l&&Il().eachTree(o,e=>{var t=i(e),r=e[l];r&&r.length&&!a[t]&&(a[t]=1,n.unshift([e,t,r]))},{children:l})}else if(e){let{transform:l,mapChildrenField:o}=u;Il().eachTree(r,e=>{var t=i(e),r=e[l?o:p];r&&r.length&&!a[t]&&(a[t]=1,n.unshift([e,t,r]))},{children:l?o:p})}n.forEach(e=>{var t=e[0],r=e[1];let l=0,o=0,a=0;e[2].forEach(g?e=>{var t=i(e),r=h?Il().get(e,h):s[t];g({$table:ie,row:e})?(r?l++:d[t]&&o++,a++):r?l++:d[t]&&o++}:e=>{var t=i(e);(h?Il().get(e,h):s[t])?l++:d[t]&&o++,a++});var e=l>=a,n=!e&&(1<=l||1<=o);h&&Il().set(t,h,e),e?(h||(s[r]=t),d[r]&&delete d[r]):(h||s[r]&&delete s[r],n?d[r]=t:d[r]&&delete d[r])})}U.updateCheckboxFlag++}},updateAllCheckboxStatus(){var e=j.treeConfig,t=U.isRowGroupStatus;let{afterFullData:r,afterTreeFullData:l,afterGroupFullData:o,checkboxReserveRowMap:a,selectCheckboxMaps:n,treeIndeterminateRowMaps:i}=N,{checkField:s,checkMethod:d,showReserveStatus:c}=te.value,u=Nl(ie).handleGetRowId,p=0,h=0,g=0;e=e?l:t?o:r,e.forEach(d?e=>{var t=u(e),r=s?Il().get(e,s):n[t];d({$table:ie,row:e})?(r?p++:i[t]&&h++,g++):r?p++:i[t]&&h++}:e=>{var t=u(e);(s?Il().get(e,s):n[t])?p++:i[t]&&h++,g++}),t=0<e.length&&(0<g?p>=g:p>=e.length);let m=!t&&(1<=p||1<=h);t||m||!c||(m=!Il().isEmpty(a)),U.isAllSelected=t,U.isIndeterminate=m},checkSelectionStatus(){ie.updateCheckboxStatus(),ie.updateAllCheckboxStatus()},handleBatchSelectRows(e,r,l){var t=j.treeConfig,o=U.isRowGroupStatus;let a=N.selectCheckboxMaps;var n=M.value,i=ne.value,{transform:s,mapChildrenField:d}=i,i=i.children||i.childrenField,c=te.value;let{checkField:u,checkStrictly:p,checkMethod:h}=c,g=Nl(ie).handleGetRowId,m=c.indeterminateField||c.halfField;if(u)!t&&!o||p?e.forEach(e=>{!l&&h&&!h({$table:ie,row:e})||(Il().set(e,u,r),Zt(e,r))}):Il().eachTree(e,e=>{!l&&h&&!h({$table:ie,row:e})||(Il().set(e,u,r),m&&Il().set(e,m,!1),Zt(e,r))},{children:s?d:i}),U.updateCheckboxFlag++;else{if(!p){if(o)return Il().eachTree(e,e=>{var t=g(e);!l&&h&&!h({$table:ie,row:e})||(r?a[t]=e:a[t]&&delete a[t],Zt(e,r))},{children:n.mapChildrenField}),void U.updateCheckboxFlag++;if(t)return Il().eachTree(e,e=>{var t=g(e);!l&&h&&!h({$table:ie,row:e})||(r?a[t]=e:a[t]&&delete a[t],Zt(e,r))},{children:s?d:i}),void U.updateCheckboxFlag++}e.forEach(e=>{var t=g(e);!l&&h&&!h({$table:ie,row:e})||(r?a[t]||(a[t]=e):a[t]&&delete a[t],Zt(e,r),U.updateCheckboxFlag++)})}},handleSelectRow({row:e},t,r){ie.handleBatchSelectRows([e],t,r)},handleUpdateBodyMerge(){var e=N.mergeBodyList;N.mergeBodyCellMaps=Lt(e),U.mergeBodyFlag++},handleUpdateFooterMerge(){var e=N.mergeFooterList;N.mergeFooterCellMaps=Lt(e),U.mergeFootFlag++},handleAggregateSummaryData(){return(()=>{var{aggregateConfig:e,rowGroupConfig:t}=j,r=U.isRowGroupStatus,l=N.tableFullGroupData,o=M.value.mapChildrenField;if((e||t)&&r){let t=[];Il().eachTree(l,e=>{e.isAggregate&&t.push(e)},{children:o}),dr(t)}})()},triggerHeaderTitleEvent(r,e,l){var o=e.content||e.message;if(o){var a=U.tooltipStore,l=l.column;let t=Ml(o);Nr(!0),a.row=null,a.column=l,a.visible=!0,a.currOpts=e,(0,Al.nextTick)(()=>{var e=q.value;e&&e.open&&e.open(r.currentTarget,t)})}},triggerHeaderTooltipEvent(e,t){var r,l,o=U.tooltipStore,a=t.column,e=(Nr(!0),e.currentTarget);(e=e&&e.parentElement)&&(r=e.parentElement)&&(l=r.parentElement)&&(o.column!==a||!o.visible)&&(a=l.querySelector(".vxe-cell--title"),qr(0,l,(Ll(l,"col--ellipsis")?a:e)||e,a||r,t))},triggerBodyTooltipEvent(r,l){var o=j.editConfig,e=U.editStore,t=U.tooltipStore,a=re.value,e=e.actived,{row:n,column:i}=l,r=r.currentTarget;if(Nr(t.column!==i||t.row!==n),i.editRender&&Fl(o)){if("row"===a.mode&&e.row===n)return;if(e.row===n&&e.column===i)return}if(t.column!==i||t.row!==n||!t.visible){o=r.querySelector(".vxe-cell--wrapper");let e=null,t=r.querySelector("html"===i.type?".vxe-cell--html":".vxe-cell--label");i.treeNode&&(e=r.querySelector(".vxe-tree-cell")),t=t||o,qr(0,r,e||o,t,l)}},triggerFooterTooltipEvent(r,l){var o=l.column,a=U.tooltipStore,r=r.currentTarget;if(Nr(a.column!==o||!!a.row),a.column!==o||!a.visible){a=r.querySelector(".vxe-cell--wrapper");let e=null,t=r.querySelector("html"===o.type?".vxe-cell--html":".vxe-cell--label");"html"===o.type&&(e=r.querySelector(".vxe-cell--html")),t=t||a,qr(0,r,e||a,t,l)}},handleTargetLeaveEvent(){var e=Ue.value;let t=q.value;t&&t.setActived&&t.setActived(!1),e.enterable?N.tooltipTimeout=setTimeout(()=>{(t=q.value)&&t.isActived&&!t.isActived()&&ie.closeTooltip()},e.leaveDelay):ie.closeTooltip()},triggerHeaderCellClickEvent(e,t){var r=N._lastResizeTime,l=O.value,o=Z.value,a=B.value,n=t.column,i=e.currentTarget,r=r&&r>Date.now()-300,s=Bl(e,i,"vxe-cell--sort").flag,d=Bl(e,i,"vxe-cell--filter").flag;"cell"!==l.trigger||r||s||d||ie.triggerSortEvent(e,n,Rt(n)),se("header-cell-click",Object.assign({triggerResizable:r,triggerSort:s,triggerFilter:d,cell:i},t),e),!o.isCurrent&&!j.highlightCurrentColumn||a.trigger&&!["header","default"].includes(a.trigger)||ie.triggerCurrentColumnEvent(e,t)},triggerHeaderCellDblclickEvent(e,t){se("header-cell-dblclick",Object.assign({cell:e.currentTarget},t),e)},triggerCellClickEvent(e,t){var r,l,o,a,n,i,s,d,c,u,p,h,g,m,v,f,x,b,C,w,y,T,E,{highlightCurrentRow:R,highlightCurrentColumn:S,editConfig:D}=j,{editStore:I,isDragResize:F}=U;F||(F=ae.value,r=re.value,l=ne.value,o=k.value,a=te.value,n=oe.value,i=M.value,s=Q.value,d=Z.value,c=B.value,{actived:I,focused:u}=I,{row:p,column:h}=t,{type:v,treeNode:g,rowGroupNode:m}=h,f="checkbox"===v,x="expand"===v,b=e.currentTarget,C=(v="radio"===v)&&Bl(e,b,"vxe-cell--radio").flag,w=f&&Bl(e,b,"vxe-cell--checkbox").flag,y=g&&Bl(e,b,"vxe-cell--tree-btn").flag,T=x&&Bl(e,b,"vxe-table--expanded").flag,E=x&&Bl(e,b,"vxe-row-group--node-btn").flag,t=Object.assign({cell:b,triggerRadio:C,triggerCheckbox:w,triggerTreeNode:y,triggerExpandNode:T},t),!w&&!C&&(!T&&("row"===F.trigger||x&&"cell"===F.trigger)&&ie.triggerRowExpandEvent(e,t),("row"===l.trigger||g&&"cell"===l.trigger)&&ie.triggerTreeExpandEvent(e,t),"row"===i.trigger||m&&"cell"===i.trigger)&&ie.triggerRowGroupExpandEvent(e,t),y||(T||E||(!s.isCurrent&&!R||w||C||ie.triggerCurrentRowEvent(e,t),!d.isCurrent&&!S||c.trigger&&!["cell","default"].includes(c.trigger)||w||C||ie.triggerCurrentColumnEvent(e,t),!C&&("row"===o.trigger||v&&"cell"===o.trigger)&&ie.triggerRadioRowEvent(e,t),!w&&("row"===a.trigger||f&&"cell"===a.trigger)&&ie.handleToggleCheckRowEvent(e,t)),Fl(D)&&(n.arrowCursorLock&&e&&"cell"===r.mode&&e.target&&/^input|textarea$/i.test(e.target.tagName)&&(u.column=h,u.row=p),"manual"===r.trigger?I.args&&I.row===p&&h!==I.column&&Kt(e,t):I.args&&p===I.row&&h===I.column||("click"===r.trigger||"dblclick"===r.trigger&&"row"===r.mode&&I.row===p)&&Kt(e,t))),Fl(D)&&"dblclick"===r.trigger&&I.row&&I.column&&("row"===r.mode?ie.eqRow(I.row,p)||ie.handleClearEdit(e):"cell"!==r.mode||ie.eqRow(I.row,p)&&I.column.id===h.id||ie.handleClearEdit(e)),se("cell-click",t,e))},triggerCellDblclickEvent(e,t){var r,l=j.editConfig,{editStore:o,isDragResize:a}=U;a||(a=re.value,o=o.actived,r=e.currentTarget,t=Object.assign({cell:r},t),!Fl(l)||"dblclick"!==a.trigger||o.args&&e.currentTarget===o.args.cell||("row"===a.mode?Xt("blur").catch(e=>e).then(()=>{ie.handleEdit(t,e).then(()=>Xt("change")).catch(e=>e)}):"cell"===a.mode&&ie.handleEdit(t,e).then(()=>Xt("change")).catch(e=>e)),se("cell-dblclick",t,e))},handleToggleCheckRowEvent(t,r){var l=N.selectCheckboxMaps,{checkField:o,trigger:e}=te.value,a=r.row;if("manual"!==e){let e=!1;e=o?!Il().get(a,o):!l[Pl(ie,a)],t?ie.triggerCheckRowEvent(t,r,e):(ie.handleBatchSelectRows([a],e),ie.checkSelectionStatus())}},triggerCheckRowEvent(t,r,e){var l=j.treeConfig,o=r.row,a=U.isRowGroupStatus,n=N.afterFullData,i=te.value,{checkMethod:s,trigger:d}=i;if("manual"!==d){if(t.stopPropagation(),i.isShiftKey&&t.shiftKey&&!l&&!a){d=ie.getCheckboxRecords();if(d.length){i=d[0],l=ie.getVTRowIndex(o),a=ie.getVTRowIndex(i);if(l!==a){ie.setAllCheckboxRow(!1);let e=l<a?n.slice(l,a+1):n.slice(a,l+1);return(0,Al.nextTick)(()=>{Jt(e,!0,!1)}),void se("checkbox-range-select",Object.assign({rangeRecords:e},r),t)}}}s&&!s({$table:ie,row:o})||(ie.handleBatchSelectRows([o],e),ie.checkSelectionStatus(),se("checkbox-change",Object.assign({records:()=>ie.getCheckboxRecords(),reserves:()=>ie.getCheckboxReserveRecords(),indeterminates:()=>ie.getCheckboxIndeterminateRecords(),checked:e},r),t))}},triggerCheckAllEvent(e,t){var r=te.value.trigger;"manual"!==r&&(e&&e.stopPropagation(),Er(e,t))},triggerRadioRowEvent(r,l){var o=U.selectRadioRow,a=l.row,n=k.value,{trigger:e,checkMethod:t}=n;if("manual"!==e&&(r.stopPropagation(),!t||t({$table:ie,row:a}))){let e=a,t=o!==e;t?Qt(e):n.strict||(t=o===e)&&(e=null,ie.clearRadioRow()),t&&se("radio-change",{oldValue:o,newValue:e,...l},r)}},triggerCurrentColumnEvent(e,t){var r=U.currentColumn,l=Z.value,o=B.value,l=o.beforeSelectMethod||l.currentMethod,a=t.column,o=o.trigger;"manual"!==o&&(o=r!==a,!l||l({column:a,$table:ie})?(ie.setCurrentColumn(a),o&&se("current-column-change",{oldValue:r,newValue:a,...t},e)):se("current-column-disabled",t,e))},triggerCurrentRowEvent(e,t){var r=U.currentRow,l=Q.value,o=Pe.value,l=o.beforeSelectMethod||l.currentMethod,a=t.row,o=o.trigger;"manual"!==o&&(o=r!==a,!l||l({row:a,$table:ie})?(ie.setCurrentRow(a),o&&(se("current-row-change",{oldValue:r,newValue:a,...t},e),se("current-change",{oldValue:r,newValue:a,...t},e))):se("current-row-disabled",t,e))},triggerRowExpandEvent(e,t){var r=U.expandColumn,l=N.rowExpandLazyLoadedMaps,t=t.row,{lazy:o,trigger:a}=ae.value;"manual"===a||(e.stopPropagation(),a=Pl(ie,t),o&&l[a])||(o=!ie.isRowExpandByRow(t),l=r?ie.getColumnIndex(r):-1,a=r?ie.getVMColumnIndex(r):-1,ie.setRowExpand(t,o),se("toggle-row-expand",{expanded:o,column:r,columnIndex:l,$columnIndex:a,row:t,rowIndex:ie.getRowIndex(t),$rowIndex:ie.getVMRowIndex(t)},e))},triggerRowGroupExpandEvent(e,t){var r,l=N.rowGroupExpandedMaps,{row:t,column:o}=t,a=M.value.trigger;"manual"!==a&&(e.stopPropagation(),a=!l[Pl(ie,t)],l=ie.getColumnIndex(o),r=ie.getVMColumnIndex(o),ie.setRowGroupExpand(t,a),se("toggle-row-group-expand",{expanded:a,column:o,columnIndex:l,$columnIndex:r,row:t},e))},triggerTreeExpandEvent(e,t){var{treeExpandLazyLoadedMaps:r,treeEATime:l}=N,o=ne.value;let{row:a,column:n}=t,{lazy:i,trigger:s,accordion:d}=o;"manual"===s||(e.stopPropagation(),t=Pl(ie,a),i&&r[t])||(o=!ie.isTreeExpandByRow(a),r=ie.getColumnIndex(n),t=ie.getVMColumnIndex(n),l&&clearTimeout(l),ie.setTreeExpand(a,o).then(()=>{d&&(N.treeEATime=setTimeout(()=>{N.treeEATime=void 0,ie.scrollToRow(a)},30))}),se("toggle-tree-expand",{expanded:o,column:n,columnIndex:r,$columnIndex:t,row:a},e))},handleColumnSortEvent(e,t){var r=j.mouseConfig,l=le.value,{field:o,sortable:a,order:n}=t;a&&(a={$table:ie,$event:e,column:t,field:o,property:o,order:n,sortList:z.getSortColumns(),sortTime:t.sortTime},r&&l.area&&ie.handleSortEvent&&ie.handleSortEvent(e,a),n||se("clear-sort",a,e),se("sort-change",a,e))},triggerSortEvent(e,t,r){var{multiple:l,allowClear:o}=O.value,{field:a,sortable:n}=t;n&&(r&&t.order!==r?ie.sort({field:a,order:r}):o&&ie.clearSort(l?t:null),ie.handleColumnSortEvent(e,t))},handleCellRuleUpdateStatus(e,t,l){let o=U.validStore,{row:a,column:n}=t;if(ie.hasCellRules&&ie.hasCellRules(e,a,n)){let r=ie.getCellElement(a,n);if(r){let t=!Il().isUndefined(l);return ie.validCellRules(e,a,n,l).then(()=>{t&&o.visible&&Ul(a,n,l),ie.clearValidate(a,n)}).catch(({rule:e})=>{t&&Ul(a,n,l),ie.showValidTooltip({rule:e,row:a,column:n,cell:r})})}}return(0,Al.nextTick)()},triggerHeaderCellMousedownEvent(e,t){var r=j.mouseConfig,l=le.value,o=Z.value,{trigger:a,isCrossDrag:n,isPeerDrag:i,disabledMethod:s}=ee.value,d=e.currentTarget,c=d&&d.tagName&&"input"===d.tagName.toLowerCase(),u=Bl(e,d,"vxe-cell--checkbox").flag,p=Bl(e,d,"vxe-cell--sort").flag,h=Bl(e,d,"vxe-cell--filter").flag;let g=!1;o=o.drag&&"cell"===a;c||u||p||h||(a=t.column,!o)||a.fixed||!n&&!i&&a.parentId||s&&s(t)||(g=!0,ie.handleHeaderCellDragMousedownEvent(e,t)),!g&&r&&l.area&&ie.handleHeaderCellAreaEvent&&ie.handleHeaderCellAreaEvent(e,Object.assign({cell:d,triggerSort:p,triggerFilter:h},t)),ie.focus(),ie.closeMenu&&ie.closeMenu()},triggerCellMousedownEvent(e,t){var r=t.column,{type:l,treeNode:o}=r,a="radio"===l,n="checkbox"===l,l="expand"===l,i=Q.value,{trigger:s,isCrossDrag:d,isPeerDrag:c,disabledMethod:u}=J.value,p=e.currentTarget,h=(t.cell=p)&&p.tagName&&"input"===p.tagName.toLowerCase(),a=a&&Bl(e,p,"vxe-cell--radio").flag,n=n&&Bl(e,p,"vxe-cell--checkbox").flag,o=o&&Bl(e,p,"vxe-cell--tree-btn").flag,l=l&&Bl(e,p,"vxe-table--expanded").flag;let g=!1,m=(i.drag&&(g="row"===s||r.dragSort&&"cell"===s),!1);h||a||n||o||l||!g||!d&&!c&&t.level||u&&u(t)||(m=!0,ie.handleCellDragMousedownEvent(e,t)),!m&&ie.handleCellMousedownEvent&&ie.handleCellMousedownEvent(e,t),ie.focus(),ie.closeFilter(),ie.closeMenu&&ie.closeMenu()},triggerCellMouseupEvent(){zr()},handleRowDragDragstartEvent(e){e.dataTransfer&&e.dataTransfer.setDragImage(Zl(),0,0)},handleRowDragSwapEvent(s,e,d,c,u,t){let{treeConfig:p,dragConfig:r}=j;var l=J.value;let{afterFullData:h,tableFullData:g,fullAllDataRowIdData:m}=N,{isPeerDrag:v,isCrossDrag:f,isSelfToChildDrag:x,dragEndMethod:o,dragToChildMethod:a}=l;l=ne.value;let{transform:b,rowField:C,mapChildrenField:w,parentField:y}=l,T=l.children||l.childrenField;l=o||(r?r.dragEndMethod:null);let E="bottom"===u?1:0,R={status:!1};if(c&&d&&c!==d){var n={oldRow:d,newRow:c,dragRow:d,dragPos:u,dragToChild:!!t,offsetIndex:E};let i=x&&a?a(n):t;return Promise.resolve(!l||l(n)).then(r=>{if(!r)return R;let e=-1,t=-1;if(p){if(b){var r=Pl(ie,d),r=m[r],l=Pl(ie,c),o=m[l];if(r&&o){var a=r.level,n=o.level;let t={},e=(Il().eachTree([d],e=>{t[Pl(ie,e)]=e},{children:w}),!1);if(a&&n)if(v&&!f){if(r.row[y]!==o.row[y])return R}else{if(!f)return R;if(t[l]&&(e=!0,!f||!x))return Dl.VxeUI.modal&&Dl.VxeUI.modal.message({status:"error",content:gl("vxe.error.treeDragChild")}),R}else if(a){if(!f)return R}else if(n){if(!f)return R;if(t[l]&&(e=!0,!f||!x))return Dl.VxeUI.modal&&Dl.VxeUI.modal.message({status:"error",content:gl("vxe.error.treeDragChild")}),R}r=Il().toTreeArray(N.afterTreeFullData,{key:C,parentKey:y,children:w}),o=ie.findRowIndexOf(r,d),a=(r.splice(o,1),ie.findRowIndexOf(r,c)),n=a+E;r.splice(n,0,d),e&&f&&x&&Il().each(d[T],e=>{e[y]=d[y]}),d[y]=i?c[C]:c[y],N.tableFullTreeData=Il().toArrayTree(r,{key:C,parentKey:y,children:T,mapChildren:w})}}}else{e=ie.findRowIndexOf(h,d);l=ie.findRowIndexOf(g,d),o=(h.splice(e,1),g.splice(l,1),ie.findRowIndexOf(h,c)),a=ie.findRowIndexOf(g,c),n=(t=o+E,a+E);h.splice(t,0,d),g.splice(n,0,d)}return U.isDragRowMove=!0,ie.handleTableData(p&&b),ie.cacheRowMap(!1),wr(),p&&b||ie.updateAfterDataIndex(),ie.checkSelectionStatus(),U.scrollYLoad&&ie.updateScrollYSpace(),s&&se("row-dragend",{oldRow:d,newRow:c,dragRow:d,dragPos:u,dragToChild:i,offsetIndex:E,_index:{newIndex:t,oldIndex:e}},s),(0,Al.nextTick)().then(()=>{ie.updateCellAreas(),ie.recalculate()}).then(()=>({status:!0}))}).catch(()=>R)}return Promise.resolve(R)},handleRowDragDragendEvent(e){var t=j.treeConfig,{fullAllDataRowIdData:r,prevDragToChild:l}=N,o=U.dragRow,a=ne.value,n=a.lazy,a=a.hasChild||a.hasChildField,{prevDragRow:i,prevDragPos:s}=N;t&&n&&l&&(t=r[Pl(ie,i)],i[a])&&(!t||!t.treeLoaded)||ie.handleRowDragSwapEvent(e,!0,o,i,s,l),Gr(),jr(),N.prevDragToChild=!1,U.dragRow=null,U.dragCol=null,setTimeout(()=>{U.isDragRowMove=!1},500)},handleRowDragDragoverEvent(r){var l=j.treeConfig,o=N.fullAllDataRowIdData;let e=U.dragRow;var a=ne.value,{lazy:n,transform:i,parentField:s}=a,a=a.hasChild||a.hasChildField,{isPeerDrag:d,isCrossDrag:c,isToChildDrag:u}=J.value;if(e){var p=eo(r),h=r.currentTarget;let e=h.getAttribute("rowid")||"";var g=o[e];if(g){var m=g.row;let e=Pl(ie,m);o=o[e];r.preventDefault();let t=U.dragRow;var v=r.clientY-h.getBoundingClientRect().y<h.clientHeight/2?"top":"bottom";N.prevDragToChild=!!(l&&i&&c&&u&&p),N.prevDragRow=m,N.prevDragPos=v,ie.eqRow(t,m)||p&&l&&n&&m[a]&&o&&!o.treeLoaded||!c&&l&&i&&(d?t[s]!==m[s]:g.level)?Wr(r,h,null,!1,v):(Wr(r,h,null,!0,v),se("row-dragover",{oldRow:t,targetRow:m,dragPos:v},r))}}else r.preventDefault()},handleCellDragMousedownEvent(e,t){e.stopPropagation();var r=j.dragConfig,{trigger:l,dragStartMethod:o}=J.value,a=t.row,n=e.currentTarget,l="cell"===l||"row"===l?n:n.parentElement?.parentElement,n=l.parentElement,o=o||(r?r.dragStartMethod:null);jr(),o&&!o(t)?(n.draggable=!1,U.dragRow=null,U.dragCol=null,Gr()):(U.dragRow=a,U.dragCol=null,n.draggable=!0,(e=>{var t=G.value;if(t){e=Pl(ie,e);Il().arrayEach(t.querySelectorAll(`[rowid="${e}"]`),e=>{$l(e,"row--drag-origin")})}})(a),(e=>{var t=j.dragConfig,r=U.dragRow,l=J.value.tooltipMethod,l=l||(t?t.rowTooltipMethod:null);let o="";o=l?""+(l({$table:ie,row:r})||""):gl("vxe.table.dragTip",[e.textContent||""]),U.dragTipText=o})(l),se("row-dragstart",t,e))},handleCellDragMouseupEvent(){zr()},handleHeaderCellDragDragstartEvent(e){e.dataTransfer&&e.dataTransfer.setDragImage(Zl(),0,0)},handleColDragSwapColumn(){mr(),xr(!1).then(()=>{ie.updateCellAreas(),ie.saveCustomStore("update:sort")})},handleColDragSwapEvent(u,p,e,t,h,r){let g=j.mouseConfig;let{isPeerDrag:m,isCrossDrag:v,isSelfToChildDrag:f,isToChildDrag:x,dragEndMethod:l,dragToChildMethod:o}=ee.value,b=N.collectColumn,C="right"===h?1:0,w={status:!1};if(t&&e&&t!==e){let s=e,d=t;e={oldColumn:s,newColumn:d,dragColumn:s,dragPos:h,dragToChild:!!r,offsetIndex:C};let c=f&&o?o(e):r;return Promise.resolve(!l||l(e)).then(e=>{if(!e)return w;let t=-1,r=-1,l={},o=(Il().eachTree([s],e=>{l[e.id]=e}),!1);if(s.parentId&&d.parentId)if(m&&!v){if(s.parentId!==d.parentId)return w}else{if(!v)return w;if(l[d.id]&&(o=!0,!v||!f))return Dl.VxeUI.modal&&Dl.VxeUI.modal.message({status:"error",content:gl("vxe.error.treeDragChild")}),w}else if(s.parentId){if(!v)return w}else if(d.parentId){if(!v)return w;if(l[d.id]&&(o=!0,!v||!f))return Dl.VxeUI.modal&&Dl.VxeUI.modal.message({status:"error",content:gl("vxe.error.treeDragChild")}),w}var a,n,i,e=Il().findTree(b,e=>e.id===s.id),e=(o&&v&&f?e&&({items:a,index:i}=e,(n=s.children||[]).forEach(e=>{e.parentId=s.parentId}),a.splice(i,1,...n),s.children=[]):e&&({items:a,index:i,parent:n}=e,a.splice(i,1),n||(t=i)),Il().findTree(b,e=>e.id===d.id));return e&&({items:a,index:n,parent:i}=e,v&&x&&c?(s.parentId=d.id,d.children=(d.children||[]).concat([s])):(s.parentId=d.parentId,a.splice(n+C,0,s)),i||(r=n)),Il().eachTree(b,(e,t,r,l,o)=>{o||(e.renderSortNumber=t+1)}),U.isDragColMove=!0,g&&(ie.clearSelected&&ie.clearSelected(),ie.clearCellAreas)&&(ie.clearCellAreas(),ie.clearCopyCellArea()),u&&se("column-dragend",{oldColumn:s,newColumn:d,dragColumn:s,dragPos:h,dragToChild:c,offsetIndex:C,_index:{newIndex:r,oldIndex:t}},u),p&&ie.handleColDragSwapColumn(),{status:!0}}).catch(()=>w)}return Promise.resolve(w)},handleHeaderCellDragDragendEvent(e){var t=U.dragCol,{prevDragCol:r,prevDragPos:l,prevDragToChild:o}=N;ie.handleColDragSwapEvent(e,!0,t,r,l,o),Gr(),Ur(),N.prevDragToChild=!1,U.dragRow=null,U.dragCol=null,setTimeout(()=>{U.isDragColMove=!1,ie.recalculate().then(()=>{vr()})},500)},handleHeaderCellDragDragoverEvent(e){var t,r,l,o,a,n=U.dragCol,{isToChildDrag:i,isPeerDrag:s,isCrossDrag:d}=ee.value;n?(a=eo(e),l=(r=e.currentTarget).getAttribute("colid"),(l=ie.getColumnById(l))&&(e.preventDefault(),t=e.clientX,o=t-r.getBoundingClientRect().x<r.clientWidth/2?"left":"right",N.prevDragToChild=!!(d&&i&&a),N.prevDragCol=l,N.prevDragPos=o,l.fixed||n&&n.id===l.id||!d&&(s?n.parentId!==l.parentId:l.parentId)?Wr(e,null,r,!1,o):(Wr(e,null,r,!0,o),se("column-dragover",{oldColumn:n,targetColumn:l,dragPos:o},e),(i=G.value)&&(a=S.value,s=(d=f.value)?d.$el:null,r=a||s)&&(n=i.getBoundingClientRect(),l=i.clientWidth,d=(o=P.value)?o.clientWidth:0,s=(a=X.value)?a.clientWidth:0,i=t-(n.x+d),o=n.x+l-s-t,0<i&&i<=28?(a=Math.floor(l/(14<i?240:120)),r.scrollLeft-=a*(28-i)):0<o&&o<=28&&(d=Math.floor(l/(14<o?240:120)),r.scrollLeft+=d*(28-o)))))):e.preventDefault()},handleHeaderCellDragMousedownEvent(e,t){e.stopPropagation();var{trigger:r,dragStartMethod:l}=ee.value,o=t.column,a=e.currentTarget,r="cell"===r?a:a.parentElement?.parentElement;U.isDragColMove=!1,Ur(),l&&!l(t)?(r.draggable=!1,U.dragRow=null,U.dragCol=null,Gr()):(U.dragCol=o,U.dragRow=null,r.draggable=!0,(e=>{var r=G.value;if(r){let t=[];Il().eachTree([e],e=>{t.push(`[colid="${e.id}"]`)});Il().arrayEach(r.querySelectorAll(t.join(",")),e=>{$l(e,"col--drag-origin")})}})(o),(e=>{var t=U.dragCol,r=ee.value.tooltipMethod;let l="";l=r?""+(r({$table:ie,column:t})||""):gl("vxe.table.dragTip",[e.textContent||""]),U.dragTipText=l})(r),se("column-dragstart",t,e))},handleHeaderCellDragMouseupEvent(){Ur(),Gr(),U.dragRow=null,U.dragCol=null,U.isDragColMove=!1},handleScrollEvent(d,c,u,p,h,g){var m=j.highlightHoverRow,{lastScrollLeft:v,lastScrollTop:f}=N,x=S.value,b=D.value;if(x&&b){var C=Q.value,w=fe.value,y=q.value,T=b.clientHeight,E=x.clientWidth,b=b.scrollHeight,x=x.scrollWidth;let e=!1,t=!1,r=!1,l=!1,o="",a=!1,n=!1,i=!1,s=!1;u&&(R=_.value,(r=h<=0)||(l=x<=h+E),v<h?(o="right",x-R<=h+E&&(s=!0)):(o="left",h<=R&&(i=!0)),ie.checkScrolling(),N.lastScrollLeft=h),c&&(v=H.value,(e=p<=0)||(t=b<=p+T),f<p?(o="bottom",b-v<=p+T&&(n=!0)):(o="top",p<=v&&(a=!0)),N.lastScrollTop=p),U.isDragColMove=!1,U.isDragRowMove=!1,U.lastScrollTime=Date.now();var R={scrollTop:p,scrollLeft:h,bodyHeight:T,bodyWidth:E,scrollHeight:b,scrollWidth:x,isX:u,isY:c,isTop:e,isBottom:t,isLeft:r,isRight:l,direction:o,...g};Mr(),((e,t)=>{let{scrollXLoad:r,scrollYLoad:l,isAllOverflow:o}=U;var a=N.lcsTimeout;a&&clearTimeout(a),N.lcsTimeout=setTimeout(()=>{N.lcsRunTime=Date.now(),N.lcsTimeout=void 0,N.intoRunScroll=!1,N.inVirtualScroll=!1,N.inWheelScroll=!1,N.inHeaderScroll=!1,N.inBodyScroll=!1,N.inFooterScroll=!1,N.scrollRenderType="",o||(Wt(),L()),e&&r&&ie.updateScrollXData(),t&&l&&ie.updateScrollYData().then(()=>{o||(Wt(),L()),ie.updateScrollYSpace()}),Mr(),ie.updateCellAreas()},200)})(u,c),u&&ie.closeFilter(),(C.isHover||m)&&ie.clearHoverRow(),w&&w.reactData.visible&&w.close(),y&&y.reactData.visible&&y.close(),(n||a||s||i)&&se("scroll-boundary",R,d),se("scroll",R,d)}},triggerScrollXEvent(){(w.value.immediate?vr:()=>{var{lxTimeout:e,lxRunTime:t,scrollXStore:r}=N,r=r.visibleSize,r=26<r?26:16<r?14:6;e&&clearTimeout(e),(!t||t+r<Date.now())&&(N.lxRunTime=Date.now(),vr()),N.lxTimeout=setTimeout(()=>{N.lxTimeout=void 0,N.lxRunTime=void 0,vr()},r)})()},triggerScrollYEvent(){(R.value.immediate?Rr:()=>{var{lyTimeout:e,lyRunTime:t,scrollYStore:r}=N,r=r.visibleSize,r=30<r?32:20<r?18:8;e&&clearTimeout(e),(!t||t+r<Date.now())&&(N.lyRunTime=Date.now(),Rr()),N.lyTimeout=setTimeout(()=>{N.lyTimeout=void 0,N.lyRunTime=void 0,Rr()},r)})()},triggerBodyScrollEvent(r,l){var{scrollYLoad:o,scrollXLoad:a}=U,{elemStore:n,intoRunScroll:i,lastScrollTop:s,lastScrollLeft:d,inWheelScroll:c,inVirtualScroll:u,inHeaderScroll:p,inBodyScroll:h,scrollRenderType:e,inFooterScroll:g}=N;if(!(c||u||p||g)){var c=S.value,u=D.value,p=Wl(n["left-body-scroll"]),g=Wl(n["main-body-scroll"]),m=Wl(n["right-body-scroll"]),v=Wl(n["main-header-scroll"]),n=Wl(n["main-footer-scroll"]),f=K.value;if(!i&&g&&c&&u&&(!h||e===l)){let e=u.scrollTop,t=c.scrollLeft;p&&"left"===l?e=p.scrollTop:m&&"right"===l?e=m.scrollTop:(e=g.scrollTop,t=g.scrollLeft);i=t!==d,h=e!==s;N.inBodyScroll=!0,N.scrollRenderType=l,h&&("left"===l?(_l(g,e),_l(m,e)):"right"===l?(_l(g,e),_l(p,e)):(_l(p,e),_l(m,e)),_l(u,e),_l(f,e),o)&&ie.triggerScrollYEvent(r),i&&(Hl(c,t),Hl(v,t),Hl(n,t),a)&&ie.triggerScrollXEvent(r),ie.handleScrollEvent(r,h,i,e,t,{type:"body",fixed:l})}}},triggerHeaderScrollEvent(e,t){var r=U.scrollXLoad,{elemStore:l,intoRunScroll:o,inWheelScroll:a,inVirtualScroll:n,inBodyScroll:i,inFooterScroll:s}=N;a||n||i||s||(a=D.value,n=S.value,i=Wl(l["main-body-scroll"]),s=Wl(l["main-header-scroll"]),l=Wl(l["main-footer-scroll"]),o)||s&&n&&a&&(o=a.scrollTop,a=s.scrollLeft,N.inHeaderScroll=!0,Hl(n,a),Hl(l,a),Hl(i,a),r&&ie.triggerScrollXEvent(e),ie.handleScrollEvent(e,!1,!0,o,a,{type:"header",fixed:t}))},triggerFooterScrollEvent(e,t){var r=U.scrollXLoad,{elemStore:l,intoRunScroll:o,inWheelScroll:a,inVirtualScroll:n,inHeaderScroll:i,inBodyScroll:s}=N;a||n||i||s||(a=D.value,n=S.value,i=Wl(l["main-body-scroll"]),s=Wl(l["main-header-scroll"]),l=Wl(l["main-footer-scroll"]),o)||l&&n&&a&&(o=a.scrollTop,a=l.scrollLeft,N.inFooterScroll=!0,Hl(n,a),Hl(s,a),Hl(i,a),r&&ie.triggerScrollXEvent(e),ie.handleScrollEvent(e,!1,!0,o,a,{type:"footer",fixed:t}))},triggerBodyWheelEvent(h){var{target:g,deltaY:m,deltaX:v,shiftKey:f}=h;if(!g||!/^textarea$/i.test(g.tagName)){g=Co.highlightHoverRow;let{scrollXLoad:u,scrollYLoad:p,expandColumn:e}=U;var x=Qe.value,b=Je.value;if(x||b||e){var{elemStore:x,lastScrollTop:b,lastScrollLeft:C}=N,w=Q.value;let l=S.value,o=D.value,a=Wl(x["left-body-scroll"]),n=Wl(x["main-header-scroll"]),i=Wl(x["main-body-scroll"]),s=Wl(x["main-footer-scroll"]),d=Wl(x["right-body-scroll"]),c=K.value;if(l&&o&&i){var x=(e=>{let t=1;var r=Date.now();return r<e+25?t=1.18:r<e+30?t=1.15:r<e+40?t=1.12:r<e+55?t=1.09:r<e+75?t=1.06:r<e+100&&(t=1.03),t})(U.lastScrollTime),y=f?0:Math.ceil(m*x),f=f?Math.ceil((f&&m||v)*x):0,m=y<0,v=i.scrollTop;if(!(m?v<=0:v>=i.scrollHeight-i.clientHeight)){var T,E,x=v+y,m=i.scrollLeft+f;let t=m!==C,r=x!==b;(w.isHover||g)&&ie.clearHoverRow(),t&&(h.preventDefault(),N.inWheelScroll=!0,ue.firefox||ue.safari?(Hl(l,y=m),Hl(i,y),Hl(n,y),Hl(s,y),u&&ie.triggerScrollXEvent(h),ie.handleScrollEvent(h,r,t,i.scrollTop,y,{type:"table",fixed:""})):(T=m,E=e=>{N.inWheelScroll=!0;Hl(l,e),Hl(i,e),Hl(n,e),Hl(s,e),u&&ie.triggerScrollXEvent(h),ie.handleScrollEvent(h,r,t,i.scrollTop,e,{type:"table",fixed:""})},requestAnimationFrame(()=>{E(T)}))),r&&(h.preventDefault(),N.inWheelScroll=!0,ue.firefox||ue.safari?(_l(o,f=x),_l(i,f),_l(a,f),_l(d,f),_l(c,f),p&&ie.triggerScrollYEvent(h),ie.handleScrollEvent(h,r,t,f,i.scrollLeft,{type:"table",fixed:""})):((r,l)=>{let o=Math.abs(r),a=performance.now(),n=0,i=e=>{let t=(e-a)/o;1<t&&(t=1);e=Math.pow(t,2),e=Math.floor(r*e)-n;n+=e,l(e),t<1&&requestAnimationFrame(i)};requestAnimationFrame(i)})(x-v,e=>{N.inWheelScroll=!0;e=i.scrollTop+e;_l(o,e),_l(i,e),_l(a,e),_l(d,e),_l(c,e),p&&ie.triggerScrollYEvent(h),ie.handleScrollEvent(h,r,t,e,i.scrollLeft,{type:"table",fixed:""})}))}}}}},triggerVirtualScrollXEvent(t){var r=U.scrollXLoad,{elemStore:l,inWheelScroll:o,lastScrollTop:a,inHeaderScroll:n,inBodyScroll:i,inFooterScroll:s}=N;if(!(n||i||s||o)){n=Wl(l["main-header-scroll"]),i=Wl(l["main-body-scroll"]),s=Wl(l["main-footer-scroll"]),o=D.value,l=t.currentTarget.scrollLeft,o=o||i;let e=0;o=(e=o?o.scrollTop:e)!==a;N.inVirtualScroll=!0,Hl(i,l),Hl(n,l),Hl(s,l),r&&ie.triggerScrollXEvent(t),ie.handleScrollEvent(t,o,!0,e,l,{type:"table",fixed:""})}},triggerVirtualScrollYEvent(t){var r=U.scrollYLoad,{elemStore:l,inWheelScroll:o,lastScrollLeft:a,inHeaderScroll:n,inBodyScroll:i,inFooterScroll:s}=N;if(!(n||i||s||o)){var n=Wl(l["left-body-scroll"]),i=Wl(l["main-body-scroll"]),s=Wl(l["right-body-scroll"]),o=K.value,l=S.value,d=t.currentTarget.scrollTop,l=l||i;let e=0;l=(e=l?l.scrollLeft:e)!==a;N.inVirtualScroll=!0,_l(i,d),_l(n,d),_l(s,d),_l(o,d),r&&ie.triggerScrollYEvent(t),ie.handleScrollEvent(t,!0,l,d,e,{type:"table",fixed:""})}},scrollToTreeRow(t){var e=j.treeConfig,r=U.isRowGroupStatus,l=N.tableFullData;let o=[];if(e||r){var e=M.value,a=ne.value,a=a.children||a.childrenField,l=Il().findTree(l,e=>ie.eqRow(e,t),{children:r?e.mapChildrenField:a});if(l){let r=l.nodes;r.forEach((e,t)=>{t<r.length-1&&!ie.isTreeExpandByRow(e)&&o.push(ie.setTreeExpand(e,!0))})}}return Promise.all(o).then(()=>go(ie,t))},updateScrollYStatus:wr,updateScrollXSpace(){let{scrollXLoad:o,overflowX:a,scrollXWidth:n}=U,{visibleColumn:i,scrollXStore:s,elemStore:d,fullColumnIdData:c}=N;var u=le.value,p=f.value;if(p?p.$el:null){var p=Wl(d["main-body-scroll"]),h=Wl(d["main-body-table"]),g=Wl(d["main-header-table"]),m=Wl(d["main-footer-table"]);let e=0;var v=i[s.startIndex];v&&(v=c[v.id]||{},e=v.oLeft);let t=0,r=(p&&(t=p.clientWidth),!1),l=n;5e6<n&&(e=p&&h&&5e6<=p.scrollLeft+t?5e6-h.clientWidth:(5e6-t)*(e/(n-t)),l=5e6,r=!0),o&&a||(e=0),g&&(g.style.transform=g.getAttribute("xvm")?`translate(${e}px, 0px)`:""),h&&(h.style.transform=`translate(${e}px, ${U.scrollYTop||0}px)`),m&&(m.style.transform=m.getAttribute("xvm")?`translate(${e}px, 0px)`:"");["main"].forEach(t=>{["header","body","footer"].forEach(e=>{e=Wl(d[t+`-${e}-xSpace`]);e&&(e.style.width=o?l+"px":"")})}),U.scrollXLeft=e,U.scrollXWidth=l,U.isScrollXBig=r;v=C.value;return v&&(v.style.width=l+"px"),r&&u.area&&pl("vxe.error.notProp",["mouse-config.area"]),nr(),(0,Al.nextTick)().then(()=>{A()})}},updateScrollYSpace(){var{isAllOverflow:e,overflowY:t,scrollYLoad:r,expandColumn:l}=U;let{scrollYStore:o,elemStore:a,isResizeCellHeight:n,afterFullData:i,fullAllDataRowIdData:s,rowExpandedMaps:d}=N;var c,u=o.startIndex,p=le.value,h=ae.value,g=Q.value,m=F.value,v=I.value,f=Wl(a["main-body-scroll"]),x=Wl(a["main-body-table"]),b=Wl(a["left-body-table"]),C=Wl(a["right-body-table"]);let w=0,y=0,T=!1,E=(r?n||m.height||g.height||l||!e?(e=i[u],e=s[Pl(ie,e)]||{},w=e.oTop||0,c=i[i.length-1],e=s[c=Pl(ie,c)]||{},y=(e.oTop||0)+(e.resizeHeight||m.height||g.height||e.height||v),l&&d[c]&&(y+=e.expandHeight||h.height||0),5e6<y&&(T=!0)):(5e6<(y=i.length*v)&&(T=!0),w=Math.max(0,u*v)):x&&(y=x.clientHeight),0),R=(f&&(E=f.clientHeight),y),S=w;T&&(S=f&&x&&5e6<=f.scrollTop+E?5e6-x.clientHeight:(5e6-E)*(w/(y-E)),R=5e6),r&&t||(S=0),b&&(b.style.transform=`translate(0px, ${S}px)`),x&&(x.style.transform=`translate(${U.scrollXLeft||0}px, ${S}px)`),C&&(C.style.transform=`translate(0px, ${S}px)`),["main","left","right"].forEach(t=>{["header","body","footer"].forEach(e=>{e=Wl(a[t+`-${e}-ySpace`]);e&&(e.style.height=R?R+"px":"")})});m=$.value,m&&(m.style.height=R?R+"px":""),g=V.value;return g&&(g.style.height=R?R+"px":""),U.scrollYTop=S,U.scrollYHeight=y,(U.isScrollYBig=T)&&p.area&&pl("vxe.error.notProp",["mouse-config.area"]),nr(),(0,Al.nextTick)().then(()=>{A()})},updateScrollXData(){let e=U.isAllOverflow;return gr(),ie.updateScrollXSpace(),(0,Al.nextTick)().then(()=>{gr(),ie.updateScrollXSpace(),e||ie.updateScrollYSpace()})},updateScrollYData(){return ie.handleTableData(),ie.updateScrollYSpace(),(0,Al.nextTick)().then(()=>{ie.handleTableData(),ie.updateScrollYSpace()})},checkScrolling(){var e=N.elemStore,e=Wl(e["main-body-scroll"]),t=P.value,r=X.value,e=S.value||e;e&&(t&&(0<e.scrollLeft?$l:Vl)(t,"scrolling--middle"),r)&&(e.clientWidth<e.scrollWidth-Math.ceil(e.scrollLeft)?$l:Vl)(r,"scrolling--middle")},updateZindex(){j.zIndex?N.tZindex=j.zIndex:N.tZindex<Xl()&&(N.tZindex=ql())},handleCheckedCheckboxRow:Jt,triggerHoverEvent(e,{row:t}){W.setHoverRow(t)},setHoverRow(e){var t=Pl(ie,e),r=G.value;W.clearHoverRow(),r&&Il().arrayEach(r.querySelectorAll(`.vxe-body--row[rowid="${t}"]`),e=>$l(e,"row--hover")),N.hoverRow=e},clearHoverRow(){var e=G.value;e&&Il().arrayEach(e.querySelectorAll(".vxe-body--row.row--hover"),e=>Vl(e,"row--hover")),N.hoverRow=null},getCell(e,t){return z.getCellElement(e,t)},findRowIndexOf(e,t){return t?Il().findIndexOf(e,e=>ie.eqRow(e,t)):-1},eqRow(e,t){return!(!e||!t||e!==t&&Pl(ie,e)!==Pl(ie,t))}},"openExport,openPrint,exportData,openImport,importData,saveFile,readFile,importByFile,print".split(",").forEach(e=>{ie[e]=function(){pl("vxe.error.reqModule",["Export"])}}),"clearValidate,fullValidate,validate".split(",").forEach(e=>{ie[e]=function(){pl("vxe.error.reqModule",["Validator"])}}),Object.assign(ie,z,W),e=>{var{showHeader:t,showFooter:r}=j,{tableData:l,tableColumn:o,tableGroupColumn:a,columnStore:n,footerTableData:i}=U,s="left"===e,n=s?n.leftList:n.rightList;return(0,Al.h)("div",{ref:s?P:X,class:`vxe-table--fixed-${e}-wrapper`},[t?(0,Al.h)(xo,{ref:s?p:m,fixedType:e,tableData:l,tableColumn:o,tableGroupColumn:a,fixedColumn:n}):fl(ie),(0,Al.h)(fo,{ref:s?h:v,fixedType:e,tableData:l,tableColumn:o,fixedColumn:n}),r?(0,Al.h)(bo,{ref:s?g:x,footerTableData:i,tableColumn:o,fixedColumn:n,fixedType:e}):fl(ie)])}),Yr=()=>{var e=j.dragConfig,{dragRow:t,dragCol:r,dragTipText:l}=U,o=ee.value,e=(J.value.slots||{}).tip||(e&&e.slots?e.slots.rowTip:null),o=(o.slots||{}).tip;return t&&e?Xr(e,{row:t}):r&&o?Xr(o,{column:r}):[(0,Al.h)("span",l)]},Zr=()=>{let C=j.treeConfig,{expandColumn:w,isRowGroupStatus:y}=U;var e=pt.value,t=ae.value.mode;if("fixed"!==t)return fl(ie);let T=[(0,Al.h)("div",{key:"repY",ref:V})];if(w){let b=Nl(ie).handleGetRowId;e.forEach(e=>{var{height:t,padding:r,indent:l}=ae.value,{fullAllDataRowIdData:o,fullColumnIdData:a}=N,n=ne.value,{transform:i,seqMode:s}=n,d={},c=b(e),o=o[c],a=a[w.id]||{};let u=0,p=-1,h=-1,g=-1,m=-1,v=(o&&(u=o.level,p=y||C&&i&&"increasing"===s?o._index+1:o.seq,g=o.index,m=o.$index,h=o._index),t&&(d.height=t+"px"),(y||C)&&(d.paddingLeft=u*(Il().isNumber(l)?l:n.indent)+30+"px"),-1),f=-1,x=-1;a&&(v=a.index,f=a.$index,x=a._index);i={$grid:Y,$table:ie,seq:p,column:w,columnIndex:v,$columnIndex:f,_columnIndex:x,fixed:"",type:"body",level:u,rowid:c,row:e,rowIndex:g,$rowIndex:m,_rowIndex:h,isHidden:!1,isEdit:!1,visibleData:[],data:[],items:[]};T.push((0,Al.h)("div",{key:c,class:["vxe-body--row-expanded-cell",{"is--padding":r,"is--ellipsis":t}],rowid:c,style:d},w.renderData(i)))})}return(0,Al.h)("div",{ref:K,class:"vxe-table--row-expanded-wrapper"},T)},Qr=()=>(0,Al.h)("div",{key:"vsx",ref:De,class:"vxe-table--scroll-x-virtual"},[(0,Al.h)("div",{ref:Fe,class:"vxe-table--scroll-x-left-corner"}),(0,Al.h)("div",{ref:Oe,class:"vxe-table--scroll-x-wrapper"},[(0,Al.h)("div",{ref:S,class:"vxe-table--scroll-x-handle",onScroll:ie.triggerVirtualScrollXEvent},[(0,Al.h)("div",{ref:C,class:"vxe-table--scroll-x-space"})])]),(0,Al.h)("div",{ref:Me,class:"vxe-table--scroll-x-right-corner"})]),Jr=()=>(0,Al.h)("div",{ref:Ie,class:"vxe-table--scroll-y-virtual"},[(0,Al.h)("div",{ref:ke,class:"vxe-table--scroll-y-top-corner"}),(0,Al.h)("div",{ref:Ae,class:"vxe-table--scroll-y-wrapper"},[(0,Al.h)("div",{ref:D,class:"vxe-table--scroll-y-handle",onScroll:ie.triggerVirtualScrollYEvent},[(0,Al.h)("div",{ref:$,class:"vxe-table--scroll-y-space"})])]),(0,Al.h)("div",{ref:Le,class:"vxe-table--scroll-y-bottom-corner"})]),el=()=>{var{showHeader:e,showFooter:t}=j,{overflowX:r,tableData:l,tableColumn:o,tableGroupColumn:a,footerTableData:n,columnStore:i}=U,{leftList:i,rightList:s}=i;return(0,Al.h)("div",{ref:d,class:"vxe-table--viewport-wrapper"},[(0,Al.h)("div",{class:"vxe-table--main-wrapper"},[e?(0,Al.h)(xo,{ref:c,tableData:l,tableColumn:o,tableGroupColumn:a}):fl(ie),(0,Al.h)(fo,{ref:f,tableData:l,tableColumn:o}),t?(0,Al.h)(bo,{ref:u,footerTableData:n,tableColumn:o}):fl(ie)]),(0,Al.h)("div",{class:"vxe-table--fixed-wrapper"},[i&&i.length&&r?Kr("left"):fl(ie),s&&s.length&&r?Kr("right"):fl(ie)]),Zr()])},tl=()=>{var e=_e.value;return(0,Al.h)("div",{class:"vxe-table--layout-wrapper"},e?[Jr(),el()]:[el(),Jr()])};let rl=(0,Al.ref)(0),ll=((0,Al.watch)(()=>j.data?j.data.length:-1,()=>{rl.value++}),(0,Al.watch)(()=>j.data,()=>{rl.value++}),(0,Al.watch)(rl,()=>{let o=N.initStatus;var e=j.data||[];e&&5e4<=e.length&&ul("vxe.error.errLargeData",["loadData(data), reloadData(data)"]),ur(e,!1).then(()=>{var{scrollXLoad:e,scrollYLoad:t,expandColumn:r}=U,l=ae.value;return N.inited=!0,N.initStatus=!0,o||pr(),(e||t)&&r&&"fixed"!==l.mode&&ul("vxe.error.scrollErrProp",["column.type=expand"]),z.recalculate()})}),(0,Al.ref)(0)),ol=((0,Al.watch)(()=>U.staticColumns.length,()=>{ll.value++}),(0,Al.watch)(()=>U.staticColumns,()=>{ll.value++}),(0,Al.watch)(ll,()=>{br(Il().clone(U.staticColumns))}),(0,Al.ref)(0)),al=((0,Al.watch)(()=>U.tableColumn.length,()=>{ol.value++}),(0,Al.watch)(()=>U.tableColumn,()=>{ol.value++}),(0,Al.watch)(ol,()=>{W.analyColumnWidth()}),(0,Al.watch)(()=>U.upDataFlag,()=>{(0,Al.nextTick)(()=>{z.updateData()})}),(0,Al.watch)(()=>U.reColumnFlag,()=>{(0,Al.nextTick)(()=>{z.refreshColumn()})}),(0,Al.ref)(0)),nl=((0,Al.watch)(ge,()=>{al.value++}),(0,Al.watch)(()=>j.showHeader,()=>{al.value++}),(0,Al.watch)(()=>j.showFooter,()=>{al.value++}),(0,Al.watch)(()=>U.overflowX,()=>{al.value++}),(0,Al.watch)(()=>U.overflowY,()=>{al.value++}),(0,Al.watch)(al,()=>{(0,Al.nextTick)(()=>{z.recalculate(!0).then(()=>z.refreshScroll())})}),(0,Al.ref)(0)),il=((0,Al.watch)(()=>j.height,()=>{nl.value++}),(0,Al.watch)(()=>j.maxHeight,()=>{nl.value++}),(0,Al.watch)($e,()=>{nl.value++}),(0,Al.watch)(_e,()=>{nl.value++}),(0,Al.watch)(()=>Dl.VxeUI.getLanguage(),()=>{nl.value++}),(0,Al.watch)(nl,()=>{(0,Al.nextTick)(()=>z.recalculate(!0))}),(0,Al.ref)(0)),sl=((0,Al.watch)(()=>j.footerData?j.footerData.length:-1,()=>{il.value++}),(0,Al.watch)(()=>j.footerData,()=>{il.value++}),(0,Al.watch)(il,()=>{z.updateFooter()}),(0,Al.watch)(()=>j.syncResize,e=>{e&&(Sr(),(0,Al.nextTick)(()=>{Sr(),setTimeout(()=>Sr())}))}),(0,Al.ref)(0)),dl=((0,Al.watch)(()=>j.mergeCells?j.mergeCells.length:-1,()=>{sl.value++}),(0,Al.watch)(()=>j.mergeCells,()=>{sl.value++}),(0,Al.watch)(sl,()=>{z.clearMergeCells(),(0,Al.nextTick)(()=>{j.mergeCells&&z.setMergeCells(j.mergeCells)})}),(0,Al.ref)(0));(0,Al.watch)(()=>j.mergeFooterItems?j.mergeFooterItems.length:-1,()=>{dl.value++}),(0,Al.watch)(()=>j.mergeFooterItems,()=>{dl.value++}),(0,Al.watch)(dl,()=>{z.clearMergeFooterItems(),(0,Al.nextTick)(()=>{j.mergeFooterItems&&z.setMergeFooterItems(j.mergeFooterItems)})}),(0,Al.watch)(bt,e=>{sr(e)}),(0,Al.watch)(r,()=>{let{inited:e,tableFullData:t}=N;e&&(Et(),U.tableData=[],(0,Al.nextTick)(()=>{ie.reloadData(t)}))}),t&&(0,Al.watch)(()=>t?t.reactData.resizeFlag:null,()=>{Pr()}),Et(),El.forEach(e=>{var e=e.setupTable;e&&(e=e(ie))&&Il().isObject(e)&&Object.assign(ie,e)}),W.preventEvent(null,"created",{$table:ie});let cl;return(0,Al.onActivated)(()=>{z.recalculate().then(()=>z.refreshScroll()),W.preventEvent(null,"activated",{$table:ie})}),(0,Al.onDeactivated)(()=>{N.isActivated=!1,W.preventEvent(null,"deactivated",{$table:ie})}),(0,Al.onMounted)(()=>{let T=Z.value;var e=Q.value,t=ut.value,r=M.value,l=R.value,r=r.groupFields;(T.drag||e.drag||t.allowSort)&&Yl(),sr(r),(0,Al.nextTick)(()=>{let{data:e,exportConfig:t,importConfig:r,treeConfig:l,showOverflow:o,highlightCurrentRow:a,highlightCurrentColumn:n}=j;var{scrollXStore:i,scrollYStore:s}=N,d=re.value,c=ne.value,u=k.value,p=te.value,h=ae.value,g=Q.value,m=ut.value,v=le.value;let f=at.value,x=nt.value;var b=Pe.value,C=B.value,w=oe.value,y=M.value;j.rowId&&ul("vxe.error.delProp",["row-id","row-config.keyField"]),j.rowKey&&ul("vxe.error.delProp",["row-key","row-config.useKey"]),j.columnKey&&ul("vxe.error.delProp",["column-id","column-config.useKey"]),j.rowId||g.keyField||!(p.reserve||p.checkRowKeys||u.reserve||u.checkRowKey||h.expandRowKeys||c.expandRowKeys)||ul("vxe.error.reqProp",["row-config.keyField"]),j.editConfig&&(d.showStatus||d.showUpdateStatus||d.showInsertStatus)&&!j.keepSource&&ul("vxe.error.reqProp",["keep-source"]),l&&(c.showLine||c.line)&&!o&&ul("vxe.error.reqProp",["show-overflow"]),l&&!c.transform&&j.stripe&&ul("vxe.error.noTree",["stripe"]),!j.showFooter||j.footerMethod||j.footerData||ul("vxe.error.reqProp",["footer-data | footer-method"]),g.height&&ul("vxe.error.delProp",["row-config.height","cell-config.height"]),j.highlightCurrentRow&&ul("vxe.error.delProp",["highlight-current-row","row-config.isCurrent"]),j.highlightHoverRow&&ul("vxe.error.delProp",["highlight-hover-row","row-config.isHover"]),j.highlightCurrentColumn&&ul("vxe.error.delProp",["highlight-current-column","column-config.isCurrent"]),j.highlightHoverColumn&&ul("vxe.error.delProp",["highlight-hover-column","column-config.isHover"]),j.resizable&&ul("vxe.error.delProp",["resizable","column-config.resizable"]),r&&x.types&&!x.importMethod&&!Il().includeArrays(Il().keys(x._typeMaps),x.types)&&ul("vxe.error.errProp",["export-config.types="+x.types.join(","),x.types.filter(e=>Il().includes(Il().keys(x._typeMaps),e)).join(",")||Il().keys(x._typeMaps).join(",")]),t&&f.types&&!f.exportMethod&&!Il().includeArrays(Il().keys(f._typeMaps),f.types)&&ul("vxe.error.errProp",["export-config.types="+f.types.join(","),f.types.filter(e=>Il().includes(Il().keys(f._typeMaps),e)).join(",")||Il().keys(f._typeMaps).join(",")]),j.id||(j.customConfig?Fl(m):m.enabled)&&m.storage&&pl("vxe.error.reqProp",["id"]),j.treeConfig&&p.range&&pl("vxe.error.noTree",["checkbox-config.range"]),g.height&&!j.showOverflow&&ul("vxe.error.notProp",["table.show-overflow"]),!ie.triggerCellAreaMousedownEvent&&(j.areaConfig&&ul("vxe.error.notProp",["area-config"]),j.clipConfig&&ul("vxe.error.notProp",["clip-config"]),j.fnrConfig&&ul("vxe.error.notProp",["fnr-config"]),v.area)?pl("vxe.error.notProp",["mouse-config.area"]):(l&&g.drag&&!c.transform&&pl("vxe.error.notSupportProp",["column-config.drag","tree-config.transform=false","tree-config.transform=true"]),j.dragConfig&&ul("vxe.error.delProp",["drag-config","row-drag-config"]),j.rowGroupConfig&&ul("vxe.error.delProp",["row-group-config","aggregate-config"]),y.countFields&&ul("vxe.error.delProp",["row-group-config.countFields","column.agg-func"]),y.countMethod&&ul("vxe.error.delProp",["row-group-config.countMethod","aggregate-config.aggregateMethod"]),j.treeConfig&&c.children&&ul("vxe.error.delProp",["tree-config.children","tree-config.childrenField"]),j.treeConfig&&c.line&&ul("vxe.error.delProp",["tree-config.line","tree-config.showLine"]),v.area&&v.selected&&ul("vxe.error.errConflicts",["mouse-config.area","mouse-config.selected"]),v.area&&j.treeConfig&&!c.transform&&pl("vxe.error.noTree",["mouse-config.area"]),j.editConfig&&d.activeMethod&&ul("vxe.error.delProp",["edit-config.activeMethod","edit-config.beforeEditMethod"]),j.treeConfig&&p.isShiftKey&&pl("vxe.error.errConflicts",["tree-config","checkbox-config.isShiftKey"]),p.halfField&&ul("vxe.error.delProp",["checkbox-config.halfField","checkbox-config.indeterminateField"]),g.currentMethod&&ul("vxe.error.delProp",["row-config.currentMethod","current-row-config.beforeSelectMethod"]),T.currentMethod&&ul("vxe.error.delProp",["row-config.currentMethod","current-column-config.beforeSelectMethod"]),(g.isCurrent||a)&&j.keyboardConfig&&w.isArrow&&!Il().isBoolean(b.isFollowSelected)&&ul("vxe.error.notConflictProp",["row-config.isCurrent","current-row-config.isFollowSelected"]),(T.isCurrent||n)&&j.keyboardConfig&&w.isArrow&&!Il().isBoolean(C.isFollowSelected)&&ul("vxe.error.notConflictProp",["column-config.isCurrent","current-column-config.isFollowSelected"]),j.editConfig&&!ie.insert&&pl("vxe.error.reqModule",["Edit"]),j.editRules&&!ie.validate&&pl("vxe.error.reqModule",["Validator"]),(p.range||j.keyboardConfig||j.mouseConfig)&&!ie.handleCellMousedownEvent&&pl("vxe.error.reqModule",["Keyboard"]),(j.printConfig||j.importConfig||j.exportConfig)&&!ie.exportData&&pl("vxe.error.reqModule",["Export"]),Object.assign(s,{startIndex:0,endIndex:0,visibleSize:0}),Object.assign(i,{startIndex:0,endIndex:0,visibleSize:0}),ur(e||[],!0).then(()=>{e&&e.length&&(N.inited=!0,N.initStatus=!0,pr()),hr(),A()}),j.autoResize&&(u=G.value,h=W.getParentElem(),cl=yl.create(()=>{j.autoResize&&z.recalculate(!0)}),u&&cl.observe(u),h)&&cl.observe(h))}),"scroll"!==l.mode&&(e=d.value)&&e.addEventListener("wheel",ie.triggerBodyWheelEvent,{passive:!1}),ml.on(ie,"paste",_r),ml.on(ie,"copy",Hr),ml.on(ie,"cut",Br),ml.on(ie,"mousedown",Or),ml.on(ie,"blur",Ar),ml.on(ie,"mousewheel",Lr),ml.on(ie,"keydown",$r),ml.on(ie,"resize",Pr),ml.on(ie,"contextmenu",ie.handleGlobalContextmenuEvent),W.preventEvent(null,"mounted",{$table:ie})}),(0,Al.onBeforeUnmount)(()=>{var e=d.value;e&&e.removeEventListener("wheel",ie.triggerBodyWheelEvent),N.cvCacheMaps={},N.prevDragRow=null,N.prevDragCol=null,cl&&cl.disconnect(),z.closeFilter(),ie.closeMenu&&ie.closeMenu(),W.preventEvent(null,"beforeUnmount",{$table:ie})}),(0,Al.onUnmounted)(()=>{ml.off(ie,"paste"),ml.off(ie,"copy"),ml.off(ie,"cut"),ml.off(ie,"mousedown"),ml.off(ie,"blur"),ml.off(ie,"mousewheel"),ml.off(ie,"keydown"),ml.off(ie,"resize"),ml.off(ie,"contextmenu"),W.preventEvent(null,"unmounted",{$table:ie})}),(0,Al.nextTick)(()=>{!j.loading||pe||de.loading||pl("vxe.error.reqComp",["vxe-loading"]),!0!==j.showOverflow&&"tooltip"!==j.showOverflow&&!0!==j.showHeaderOverflow&&"tooltip"!==j.showHeaderOverflow&&!0!==j.showFooterOverflow&&"tooltip"!==j.showFooterOverflow&&!j.tooltipConfig&&!j.editRules||he||pl("vxe.error.reqComp",["vxe-tooltip"])}),(0,Al.provide)("$xeColgroup",null),(0,Al.provide)("$xeTable",ie),ie.renderVN=()=>{var{loading:e,stripe:t,showHeader:r,height:l,treeConfig:o,mouseConfig:a,showFooter:n,highlightCell:i,highlightHoverRow:s,highlightHoverColumn:d,editConfig:c,editRules:u}=j,{isGroup:p,overflowX:h,overflowY:g,scrollXLoad:m,scrollYLoad:v,tableData:f,initStore:x,isRowGroupStatus:b,columnStore:C,filterStore:$,customStore:_}=U,{leftList:C,rightList:H}=C;let w=de.loading;var y=We.value,T=Ge.value,E=Ve.value,B=te.value,R=ne.value,P=Q.value,S=Z.value,D=ge.value,I=vt.value,F=le.value,M=Xe.value,k=dt.value,N=lt.value;let O=U.isColLoading||U.isRowLoading||e;var e=ze.value,A=a&&F.area,z=ee.value,L=$e.value,V=_e.value;return(0,Al.h)("div",{ref:G,class:["vxe-table","vxe-table--render-default","tid_"+ce,"border--"+I,"sx-pos--"+(L?"top":"bottom"),"sy-pos--"+(V?"left":"right"),{["size--"+D]:D,["valid-msg--"+E.msgMode]:!!u,"vxe-editable":!!c,"old-cell-valid":u&&"obsolete"===hl().cellVaildMode,"cell--highlight":i,"cell--selected":a&&F.selected,"cell--area":A,"header-cell--area":A&&M.selectCellByHeader,"body-cell--area":A&&M.selectCellByBody,"row--highlight":P.isHover||s,"column--highlight":S.isHover||d,"checkbox--range":B.range,"col--drag-cell":S.drag&&"cell"===z.trigger,"is--header":r,"is--footer":n,"is--group":p,"is-row-group":b,"is--tree-line":o&&(R.showLine||R.line),"is--fixed-left":C.length,"is--fixed-right":H.length,"is--animat":!!j.animat,"is--round":j.round,"is--stripe":!o&&t,"is--loading":O,"is--empty":!O&&!f.length,"is--scroll-y":g,"is--scroll-x":h,"is--virtual-x":m,"is--virtual-y":v}],spellcheck:!1,onKeydown:Vr},[(0,Al.h)("div",{class:"vxe-table-slots"},de.default?de.default({}):[]),(0,Al.h)("div",{ref:me,class:"vxe-table-vars"},[(0,Al.h)("div",{class:"vxe-table-var-default"}),(0,Al.h)("div",{class:"vxe-table-var-medium"}),(0,Al.h)("div",{class:"vxe-table-var-small"}),(0,Al.h)("div",{class:"vxe-table-var-mini"})]),(0,Al.h)("div",{key:"tw",class:"vxe-table--render-wrapper"},L?[Qr(),tl()]:[tl(),Qr()]),(0,Al.h)("div",{key:"tn",ref:Te,class:"vxe-table--empty-placeholder"},[(0,Al.h)("div",{class:"vxe-table--empty-content"},(I=st.value,V=de.empty,D={$table:ie,$grid:Y},V?V(D):(V=(V=I.name?bl.get(I.name):null)?V.renderTableEmpty||V.renderTableEmptyView||V.renderEmpty:null)?Gl(V(I,D)):Ml(j.emptyText)||gl("vxe.table.emptyText")))]),(0,Al.h)("div",{key:"tl",class:"vxe-table--border-line"}),(0,Al.h)("div",{key:"tcl",ref:we,class:"vxe-table--resizable-col-bar"},e.showDragTip?[(0,Al.h)("div",{class:"vxe-table--resizable-number-tip"})]:[]),(0,Al.h)("div",{key:"trl",ref:ye,class:"vxe-table--resizable-row-bar"},e.showDragTip?[(0,Al.h)("div",{class:"vxe-table--resizable-number-tip"})]:[]),pe?(0,Al.h)(pe,{key:"lg",class:"vxe-table--loading",modelValue:O,icon:k.icon,text:k.text},w?{default:()=>Xr(w,{$table:ie,$grid:Y,loading:O})}:{}):w?(0,Al.h)("div",{class:["vxe-loading--custom-wrapper",{"is--visible":O}]},Xr(w,{$table:ie,$grid:Y,loading:O})):fl(ie),x.custom?(0,Al.h)(wo,{key:"cs",ref:Ce,customStore:_}):fl(ie),x.filter?(0,Al.h)(yo,{key:"tf",ref:be,filterStore:$}):fl(ie),x.import&&j.importConfig?(0,Al.h)(To,{key:"it",defaultOptions:U.importParams,storeData:U.importStore}):fl(ie),x.export&&(j.exportConfig||j.printConfig)?(0,Al.h)(Eo,{key:"et",defaultOptions:U.exportParams,storeData:U.exportStore}):fl(ie),N?(0,Al.h)(Ro,{key:"tm",ref:xe}):fl(ie),(()=>{var{dragRow:e,dragCol:t}=U,r=Q.value,l=Z.value,o=J.value,a=ee.value;return r.drag||l.drag?(0,Al.h)("div",{class:"vxe-table--drag-wrapper"},[(0,Al.h)("div",{ref:Re,class:["vxe-table--drag-row-line",{"is--guides":o.showGuidesStatus}]}),(0,Al.h)("div",{ref:Se,class:["vxe-table--drag-col-line",{"is--guides":a.showGuidesStatus}]}),e&&o.showDragTip||t&&a.showDragTip?(0,Al.h)("div",{ref:Ee,class:"vxe-table--drag-sort-tip"},[(0,Al.h)("div",{class:"vxe-table--drag-sort-tip-wrapper"},[(0,Al.h)("div",{class:"vxe-table--drag-sort-tip-status"},[(0,Al.h)("span",{class:["vxe-table--drag-sort-tip-normal-status",e?xl().TABLE_DRAG_STATUS_ROW:xl().TABLE_DRAG_STATUS_COLUMN]}),(0,Al.h)("span",{class:["vxe-table--drag-sort-tip-sub-status",xl().TABLE_DRAG_STATUS_SUB_ROW]}),(0,Al.h)("span",{class:["vxe-table--drag-sort-tip-disabled-status",xl().TABLE_DRAG_DISABLED]})]),(0,Al.h)("div",{class:"vxe-table--drag-sort-tip-content"},Yr())])]):fl(ie)]):fl(ie)})(),he?(0,Al.h)("div",{},[(0,Al.h)(he,{key:"ctp",ref:ve,isArrow:!1,enterable:!1}),(0,Al.h)(he,{key:"btp",ref:q,theme:y.theme,enterable:y.enterable,enterDelay:y.enterDelay,leaveDelay:y.leaveDelay,useHTML:y.useHTML}),j.editRules&&E.showMessage&&("default"===E.message?!l:"tooltip"===E.message)?(0,Al.h)(he,{key:"vtp",ref:fe,class:[{"old-cell-valid":u&&"obsolete"===hl().cellVaildMode},"vxe-table--valid-error"],theme:T.theme,enterable:T.enterable,enterDelay:T.enterDelay,leaveDelay:T.leaveDelay}):fl(ie)]):fl(ie)])},ie},render(){return this.renderVN()}});let{getConfig:_,getIcon:H,getI18n:B,renderer:Re,commands:Se,createEvent:De,useFns:Ie}=Dl.VxeUI;var jr=Tt({name:"VxeToolbar",props:{loading:Boolean,refresh:[Boolean,Object],refreshOptions:Object,import:[Boolean,Object],importOptions:Object,export:[Boolean,Object],exportOptions:Object,print:[Boolean,Object],printOptions:Object,zoom:[Boolean,Object],zoomOptions:Object,custom:[Boolean,Object],customOptions:Object,buttons:{type:Array,default:()=>_().toolbar.buttons},tools:{type:Array,default:()=>_().toolbar.tools},perfect:{type:Boolean,default:()=>_().toolbar.perfect},size:{type:String,default:()=>_().toolbar.size||_().size},className:[String,Function]},emits:["button-click","tool-click"],setup(c,e){let{slots:u,emit:l}=e;var t=Il().uniqueId();let p=Dl.VxeUI.getComponent("VxeButton"),h=Ie.useSize(c).computeSize,g=(0,Al.reactive)({isRefresh:!1,connectFlag:0,columns:[]}),m={connectTable:null},v=(0,Al.ref)(),r={refElem:v},f={xID:t,props:c,context:e,reactData:g,internalData:m,getRefMaps:()=>r};let x=(0,Al.inject)("$xeGrid",null),b=(0,Al.computed)(()=>Object.assign({},Il().clone(_().toolbar.refresh,!0),c.refreshOptions,c.refresh)),C=(0,Al.computed)(()=>Object.assign({},Il().clone(_().toolbar.import,!0),c.importOptions,c.import)),w=(0,Al.computed)(()=>Object.assign({},Il().clone(_().toolbar.export,!0),c.exportOptions,c.export)),y=(0,Al.computed)(()=>Object.assign({},Il().clone(_().toolbar.print,!0),c.printOptions,c.print)),T=(0,Al.computed)(()=>Object.assign({},Il().clone(_().toolbar.zoom,!0),c.zoomOptions,c.zoom)),E=(0,Al.computed)(()=>Object.assign({},Il().clone(_().toolbar.custom,!0),c.customOptions,c.custom)),o=(0,Al.computed)(()=>{var e=m.connectTable;if((g.connectFlag||e)&&e)return e=e.getComputeMaps().computeCustomOpts,e.value;return{trigger:""}}),R=(0,Al.computed)(()=>o.value.trigger),a=()=>{var e=m.connectTable;if(e)return!0;pl("vxe.error.barUnableLink")},S=({$event:e})=>{var t=m.connectTable;t&&t.triggerCustomEvent&&t.triggerCustomEvent(e)},D=({$event:e})=>{var t=m.connectTable;t&&t.customOpenEvent(e)},I=({$event:t})=>{var e=m.connectTable;let r=e;if(r){let e=r.reactData.customStore;e.activeBtn=!1,setTimeout(()=>{e.activeBtn||e.activeWrapper||r.customCloseEvent(t)},350)}},F=({$event:e})=>{var t=g.isRefresh,r=b.value;if(!t){t=r.queryMethod||r.query;if(t){g.isRefresh=!0;try{Promise.resolve(t({})).catch(e=>e).then(()=>{g.isRefresh=!1})}catch(e){g.isRefresh=!1}}else x&&(g.isRefresh=!0,x.triggerToolbarCommitEvent({code:r.code||"reload"},e).catch(e=>e).then(()=>{g.isRefresh=!1}))}},M=({$event:e})=>{x?x.triggerZoomEvent(e):ul("vxe.error.notProp",["zoom"])},n=()=>{var e;a()&&(e=m.connectTable,e)&&e.importData()},k=()=>{var e;a()&&(e=m.connectTable,e)&&e.openImport()},i=()=>{var e;a()&&(e=m.connectTable,e)&&e.exportData()},O=()=>{var e;a()&&(e=m.connectTable,e)&&e.openExport()},d=()=>{var e;a()&&(e=m.connectTable,e)&&e.print()},A=()=>{var e;a()&&(e=m.connectTable,e)&&e.openPrint()},s=(e,t,r)=>{switch(t.code){case"print":d();break;case"open_print":A();break;case"custom":S(e);break;case"export":i();break;case"open_export":O();break;case"import":n();break;case"open_import":k();break;case"zoom":M(e);break;case"refresh":F(e);break;default:r()}},L=(e,r)=>{let l=e.$event;var t=m.connectTable;let o=t,a=r.code;a&&s(e,r,()=>{var e,t;x?x.triggerToolbarBtnEvent(r,l):(t=Se.get(a),e={code:a,button:r,$table:o,$grid:x,$event:l},t&&((t=t.tableCommandMethod||t.commandMethod)?t(e):pl("vxe.error.notCommands",[a])),f.dispatchEvent("button-click",e,l))})},V=(e,r)=>{let l=e.$event;var t=m.connectTable;let o=t,a=r.code;a&&s(e,r,()=>{var e,t;x?x.triggerToolbarTolEvent(r,l):(t=Se.get(a),e={code:a,button:null,tool:r,$table:o,$grid:x,$event:l},t&&((t=t.tableCommandMethod||t.commandMethod)?t(e):pl("vxe.error.notCommands",[a])),f.dispatchEvent("tool-click",e,l))})};Object.assign(f,{dispatchEvent:(e,t,r)=>{l(e,De(r,{$toolbar:f},t))},syncUpdate(e){m.connectTable=e.$table,g.columns=e.collectColumn,g.connectFlag++}});let $=(e,r)=>{e=e.dropdowns;return e?e.map((t,e)=>!1!==t.visible&&p?(0,Al.h)(p,{key:e,disabled:t.disabled,loading:t.loading,type:t.type,mode:t.mode,icon:t.icon,circle:t.circle,round:t.round,status:t.status,content:t.name,title:t.title,routerLink:t.routerLink,permissionCode:t.permissionCode,prefixTooltip:t.prefixTooltip,suffixTooltip:t.suffixTooltip,onClick:e=>(r?L:V)(e,t)}):(0,Al.createCommentVNode)()):[]};return f.renderVN=()=>{var{perfect:e,loading:t,refresh:r,zoom:l,custom:o,className:a}=c,n=m.connectTable,i=h.value,s=u.tools,d=u.buttons;return(0,Al.h)("div",{ref:v,class:["vxe-toolbar",a?Il().isFunction(a)?a({$toolbar:f}):a:"",{["size--"+i]:i,"is--perfect":e,"is--loading":t}]},[(0,Al.h)("div",{class:"vxe-buttons--wrapper"},d?d({$grid:x,$table:n}):(()=>{var e=c.buttons,t=m.connectTable;let i=t;var t=u.buttonPrefix||u["button-prefix"],r=u.buttonSuffix||u["button-suffix"];let s=[];return t&&s.push(...Gl(t({buttons:e||[],$grid:x,$table:i}))),e&&e.forEach((t,e)=>{var r,l,o,{dropdowns:a,buttonRender:n}=t;!1!==t.visible&&(r=n?Re.get(n.name):null,n&&r&&r.renderToolbarButton?(l=r.toolbarButtonClassName,o={$grid:x,$table:i,button:t},s.push((0,Al.h)("span",{key:"br"+(t.code||e),class:["vxe-button--item",l?Il().isFunction(l)?l(o):l:""]},Gl(r.renderToolbarButton(n,o))))):p&&s.push((0,Al.h)(p,{key:"bd"+(t.code||e),disabled:t.disabled,loading:t.loading,type:t.type,mode:t.mode,icon:t.icon,circle:t.circle,round:t.round,status:t.status,content:t.name,title:t.title,routerLink:t.routerLink,permissionCode:t.permissionCode,prefixTooltip:t.prefixTooltip,suffixTooltip:t.suffixTooltip,destroyOnClose:t.destroyOnClose,placement:t.placement,transfer:t.transfer,onClick:e=>L(e,t)},a&&a.length?{dropdowns:()=>$(t,!0)}:{})))}),r&&s.push(...Gl(r({buttons:e||[],$grid:x,$table:i}))),s})()),(0,Al.h)("div",{class:"vxe-tools--wrapper"},s?s({$grid:x,$table:n}):(()=>{var e=c.tools,t=m.connectTable;let s=t;var t=u.toolPrefix||u["tool-prefix"],r=u.toolSuffix||u["tool-suffix"];let d=[];return t&&d.push(...Gl(t({tools:e||[],$grid:x,$table:s}))),e&&e.forEach((t,e)=>{var r,l,o,a,{dropdowns:n,toolRender:i}=t;!1!==t.visible&&(r=i?i.name:null,l=i?Re.get(r):null,i&&l&&l.renderToolbarTool?(o=l.toolbarToolClassName,a={$grid:x,$table:s,tool:t},d.push((0,Al.h)("span",{key:r,class:["vxe-tool--item",o?Il().isFunction(o)?o(a):o:""]},Gl(l.renderToolbarTool(i,a))))):p&&d.push((0,Al.h)(p,{key:e,disabled:t.disabled,loading:t.loading,type:t.type,mode:t.mode,icon:t.icon,circle:t.circle,round:t.round,status:t.status,content:t.name,title:t.title,routerLink:t.routerLink,permissionCode:t.permissionCode,prefixTooltip:t.prefixTooltip,suffixTooltip:t.suffixTooltip,destroyOnClose:t.destroyOnClose,placement:t.placement,transfer:t.transfer,onClick:e=>V(e,t)},n&&n.length?{dropdowns:()=>$(t,!1)}:{})))}),r&&d.push(...Gl(r({tools:e||[],$grid:x,$table:s}))),d})()),(0,Al.h)("div",{class:"vxe-tools--operate"},[c.import&&(a=C.value,p)?(0,Al.h)(p,{key:"import",circle:!0,icon:a.icon||H().TOOLBAR_TOOLS_IMPORT,title:B("vxe.toolbar.import"),onClick:k}):(0,Al.createCommentVNode)(),c.export&&(i=w.value,p)?(0,Al.h)(p,{key:"export",circle:!0,icon:i.icon||H().TOOLBAR_TOOLS_EXPORT,title:B("vxe.toolbar.export"),onClick:O}):(0,Al.createCommentVNode)(),c.print&&(e=y.value,p)?(0,Al.h)(p,{key:"print",circle:!0,icon:e.icon||H().TOOLBAR_TOOLS_PRINT,title:B("vxe.toolbar.print"),onClick:A}):(0,Al.createCommentVNode)(),r&&(t=b.value,p)?(0,Al.h)(p,{key:"refresh",circle:!0,icon:g.isRefresh?t.iconLoading||H().TOOLBAR_TOOLS_REFRESH_LOADING:t.icon||H().TOOLBAR_TOOLS_REFRESH,title:B("vxe.toolbar.refresh"),onClick:F}):(0,Al.createCommentVNode)(),l&&x&&(d=T.value,x)&&p?(0,Al.h)(p,{key:"zoom",circle:!0,icon:x.isMaximized()?d.iconOut||H().TOOLBAR_TOOLS_MINIMIZE:d.iconIn||H().TOOLBAR_TOOLS_FULLSCREEN,title:B("vxe.toolbar.zoom"+(x.isMaximized()?"Out":"In")),onClick:M}):(0,Al.createCommentVNode)(),o&&(s=E.value,n=R.value,a={},"manual"!==n&&("hover"===n?(a.onMouseenter=D,a.onMouseleave=I):a.onClick=S),p)?(0,Al.h)(p,{key:"custom",circle:!0,icon:s.icon||H().TOOLBAR_TOOLS_CUSTOM,title:B("vxe.toolbar.custom"),className:"vxe-toolbar-custom-target",...a}):(0,Al.createCommentVNode)()])])},(0,Al.nextTick)(()=>{var e=b.value,e=e.queryMethod||e.query,e=(!c.refresh||x||e||ul("vxe.error.notFunc",["queryMethod"]),Il().isPlainObject(c.custom)&&ul("vxe.error.delProp",["custom={...}","custom=boolean & custom-options={...}"]),Il().isPlainObject(c.print)&&ul("vxe.error.delProp",["print={...}","print=boolean & print-options={...}"]),Il().isPlainObject(c.export)&&ul("vxe.error.delProp",["export={...}","export=boolean & export-options={...}"]),Il().isPlainObject(c.import)&&ul("vxe.error.delProp",["import={...}","import=boolean & import-options={...}"]),Il().isPlainObject(c.refresh)&&ul("vxe.error.delProp",["refresh={...}","refresh=boolean & refresh-options={...}"]),Il().isPlainObject(c.refresh)&&ul("vxe.error.delProp",["zoom={...}","zoom=boolean & zoom-options={...}"]),E.value);e.isFooter&&ul("vxe.error.delProp",["toolbar.custom.isFooter","table.custom-config.showFooter"]),e.showFooter&&ul("vxe.error.delProp",["toolbar.custom.showFooter","table.custom-config.showFooter"]),e.immediate&&ul("vxe.error.delProp",["toolbar.custom.immediate","table.custom-config.immediate"]),e.trigger&&ul("vxe.error.delProp",["toolbar.custom.trigger","table.custom-config.trigger"]),(c.refresh||c.import||c.export||c.print||c.zoom)&&!p&&pl("vxe.error.reqComp",["vxe-button"])}),f},render(){return this.renderVN()}});let{getConfig:me,getI18n:ve,commands:Fe,hooks:Me,useFns:ke,createEvent:Oe,globalEvents:Ae,GLOBAL_EVENT_KEYS:Le,renderEmptyElement:fe}=Dl.VxeUI,Ve=Object.keys(Co),$e=["clearAll","syncData","updateData","loadData","reloadData","reloadRow","loadColumn","reloadColumn","getRowNode","getColumnNode","getRowIndex","getVTRowIndex","getVMRowIndex","getColumnIndex","getVTColumnIndex","getVMColumnIndex","setRow","createData","createRow","revertData","clearData","isRemoveByRow","isInsertByRow","isUpdateByRow","getColumns","getColumnById","getColumnByField","getTableColumn","getFullColumns","getData","getCheckboxRecords","getParentRow","getTreeRowChildren","getTreeParentRow","getRowSeq","getRowById","getRowid","getTableData","getFullData","setColumnFixed","clearColumnFixed","setColumnWidth","getColumnWidth","setRowHeightConf","getRowHeightConf","setRowHeight","getRowHeight","hideColumn","showColumn","resetColumn","refreshColumn","refreshScroll","recalculate","closeTooltip","isAllCheckboxChecked","isAllCheckboxIndeterminate","getCheckboxIndeterminateRecords","setCheckboxRow","setCheckboxRowKey","isCheckedByCheckboxRow","isCheckedByCheckboxRowKey","isIndeterminateByCheckboxRow","isIndeterminateByCheckboxRowKey","toggleCheckboxRow","setAllCheckboxRow","getRadioReserveRecord","clearRadioReserve","getCheckboxReserveRecords","clearCheckboxReserve","toggleAllCheckboxRow","clearCheckboxRow","setCurrentRow","isCheckedByRadioRow","isCheckedByRadioRowKey","setRadioRow","setRadioRowKey","clearCurrentRow","clearRadioRow","getCurrentRecord","getRadioRecord","getCurrentColumn","setCurrentColumn","clearCurrentColumn","setPendingRow","togglePendingRow","hasPendingByRow","isPendingByRow","getPendingRecords","clearPendingRow","sort","setSort","clearSort","clearSortByEvent","isSort","getSortColumns","closeFilter","isFilter","clearFilterByEvent","isActiveFilterByColumn","isRowExpandLoaded","clearRowExpandLoaded","reloadRowExpand","reloadRowExpand","toggleRowExpand","setAllRowExpand","setRowExpand","isExpandByRow","isRowExpandByRow","clearRowExpand","clearRowExpandReserve","getRowExpandRecords","getTreeExpandRecords","isTreeExpandLoaded","clearTreeExpandLoaded","reloadTreeExpand","reloadTreeChilds","toggleTreeExpand","setAllTreeExpand","setTreeExpand","isTreeExpandByRow","clearTreeExpand","clearTreeExpandReserve","getScroll","scrollTo","scrollToRow","scrollToColumn","clearScroll","updateFooter","updateStatus","setMergeCells","removeInsertRow","removeMergeCells","getMergeCells","clearMergeCells","setMergeFooterItems","removeMergeFooterItems","getMergeFooterItems","clearMergeFooterItems","getCustomStoreData","setRowGroupExpand","setAllRowGroupExpand","clearRowGroupExpand","isRowGroupExpandByRow","isRowGroupRecord","isAggregateRecord","isAggregateExpandByRow","setRowGroups","clearRowGroups","openTooltip","moveColumnTo","moveRowTo","getCellLabel","getCellElement","focus","blur","connect"];var mr=[...Nr,"page-change","form-submit","form-submit-invalid","form-reset","form-collapse","form-toggle-collapse","proxy-query","proxy-delete","proxy-save","toolbar-button-click","toolbar-tool-click","zoom"],Ur=Tt({name:"VxeGrid",props:{...Co,layouts:Array,columns:Array,pagerConfig:Object,proxyConfig:Object,toolbarConfig:Object,formConfig:Object,zoomConfig:Object,size:{type:String,default:()=>me().grid.size||me().size}},emits:mr,setup(k,e){let{slots:d,emit:l}=e;var T=Il().uniqueId();let n=Dl.VxeUI.getComponent("VxeForm"),a=Dl.VxeUI.getComponent("VxePager"),E=[["Form"],["Toolbar","Top","Table","Bottom","Pager"]],R=ke.useSize(k).computeSize,O=(0,Al.reactive)({tableLoading:!1,proxyInited:!1,isZMax:!1,tableData:[],filterData:[],formData:{},sortData:[],tZindex:0,tablePage:{total:0,pageSize:me().pager?.pageSize||10,currentPage:1}}),s=(0,Al.ref)(),A=(0,Al.ref)(),i=(0,Al.ref)(),c=(0,Al.ref)(),u=(0,Al.ref)(),p=(0,Al.ref)(),h=(0,Al.ref)(),g=(0,Al.ref)(),m=(0,Al.ref)(),v=(0,Al.ref)();var t=e=>{let t={};return e.forEach(r=>{t[r]=(...e)=>{var t=A.value;if(t&&t[r])return t[r](...e)}}),t};let L=t($e),V=($e.forEach(r=>{L[r]=(...e)=>{var t=A.value;if(t&&t[r])return t&&t[r](...e)}}),(0,Al.computed)(()=>Il().merge({},Il().clone(me().grid.proxyConfig,!0),k.proxyConfig))),U=(0,Al.computed)(()=>{var e=V.value;return Il().isBoolean(e.message)?e.message:e.showResponseMsg}),$=(0,Al.computed)(()=>V.value.showActiveMsg),_=(0,Al.computed)(()=>Object.assign({},me().grid.pagerConfig,k.pagerConfig)),f=(0,Al.computed)(()=>Object.assign({},me().grid.formConfig,k.formConfig)),H=(0,Al.computed)(()=>Object.assign({},me().grid.toolbarConfig,k.toolbarConfig)),r=(0,Al.computed)(()=>Object.assign({},me().grid.zoomConfig,k.zoomConfig)),S=(0,Al.computed)(()=>{var{height:e,maxHeight:t}=k,{isZMax:r,tZindex:l}=O,o={};return r?o.zIndex=l:(e&&(o.height="auto"===e||"100%"===e?"100%":to(e)),t&&(o.maxHeight="auto"===t||"100%"===t?"100%":to(t))),o}),D=(0,Al.computed)(()=>{let t={},r=k;return Ve.forEach(e=>{t[e]=r[e]}),t}),I=(0,Al.computed)(()=>{var{seqConfig:e,pagerConfig:t,loading:r,editConfig:l,proxyConfig:o}=k,{isZMax:a,tableLoading:n,tablePage:i}=O,s=D.value,d=V.value,c=_.value,u=Object.assign({},s);return a&&(s.maxHeight?u.maxHeight="100%":u.height="100%"),o&&Fl(d)&&(u.loading=r||n,t)&&d.seq&&Fl(c)&&(u.seqConfig=Object.assign({},e,{startIndex:(i.currentPage-1)*i.pageSize})),l&&(u.editConfig=Object.assign({},l)),u}),F=(0,Al.computed)(()=>{var e=k.layouts;let t=[],r=[],l=[],o=[];return(t=e&&e.length?e:me().grid.layouts||E).length&&(Il().isArray(t[0])?(r=t[0],l=t[1]||[],o=t[2]||[]):l=t),{headKeys:r,bodyKeys:l,footKeys:o}});var M=(0,Al.computed)(()=>_.value.currentPage),W=(0,Al.computed)(()=>_.value.pageSize),G=(0,Al.computed)(()=>_.value.total);let q={refElem:s,refTable:A,refForm:i,refToolbar:c,refPager:u},X={computeProxyOpts:V,computePagerOpts:_,computeFormOpts:f,computeToolbarOpts:H,computeZoomOpts:r},B={xID:T,props:k,context:e,reactData:O,getRefMaps:()=>q,getComputeMaps:()=>X},o=()=>{var e=H.value;k.toolbarConfig&&Fl(e)&&(0,Al.nextTick)(()=>{var e=A.value,t=c.value;e&&t&&e.connect(t)})},P=()=>{var e=k.proxyConfig,t=O.formData,r=V.value,l=f.value;return e&&Fl(r)&&r.form?t:l.data},x=e=>{var t=O.tablePage,r=k.pagerConfig,l=_.value;r&&Fl(l)&&(e?l[e]&&(t[e]=Il().toNumber(l[e])):({currentPage:r,pageSize:e,total:l}=l,r&&(t.currentPage=r),e&&(t.pageSize=e),l&&(t.pageSize=l)))},N=(e,t)=>{var r=V.value,r=(r.response||r.props||{}).message;let l;return(l=e&&r?Il().isFunction(r)?r({data:e,$grid:B}):Il().get(e,r):l)||ve(t)},z=(e,t,r)=>{var l=$.value,o=L.getCheckboxRecords();if(l)if(o.length){if(Dl.VxeUI.modal)return Dl.VxeUI.modal.confirm({id:"cfm_"+e,content:ve(t),escClosable:!0}).then(e=>{if("confirm"===e)return r()})}else Dl.VxeUI.modal&&Dl.VxeUI.modal.message({id:"msg_"+e,content:ve("vxe.grid.selectOneRecord"),status:"warning"});else o.length&&r();return Promise.resolve()},K=e=>{var t=k.proxyConfig,r=O.tablePage;let{$event:l,currentPage:o,pageSize:a}=e;var n=V.value;r.currentPage=o,r.pageSize=a,B.dispatchEvent("page-change",e,l),t&&Fl(n)&&B.commitProxy("query").then(e=>{B.dispatchEvent("proxy-query",e,l)})},Y=t=>{var e=A.value,r=k.proxyConfig,e=e.getComputeMaps().computeSortOpts,l=V.value;e.value.remote&&(O.sortData=t.sortList,r)&&Fl(l)&&(O.tablePage.currentPage=1,j.commitProxy("query").then(e=>{j.dispatchEvent("proxy-query",e,t.$event)})),j.dispatchEvent("sort-change",t,t.$event)},Z=t=>{var e=A.value,r=k.proxyConfig,e=e.getComputeMaps().computeFilterOpts,l=V.value;e.value.remote&&(O.filterData=t.filterList,r)&&Fl(l)&&(O.tablePage.currentPage=1,j.commitProxy("query").then(e=>{j.dispatchEvent("proxy-query",e,t.$event)})),j.dispatchEvent("filter-change",t,t.$event)},Q=t=>{var e=k.proxyConfig,r=V.value;O.tableLoading||(e&&Fl(r)&&j.commitProxy("reload").then(e=>{j.dispatchEvent("proxy-query",{...e,isReload:!0},t.$event)}),j.dispatchEvent("form-submit",t,t.$event))},J=e=>{var t=k.proxyConfig;let r=e.$event;var l=V.value,o=A.value;t&&Fl(l)&&(o.clearScroll(),j.commitProxy("reload").then(e=>{j.dispatchEvent("proxy-query",{...e,isReload:!0},r)})),j.dispatchEvent("form-reset",e,r)},ee=e=>{j.dispatchEvent("form-submit-invalid",e,e.$event)},te=e=>{var t=e.$event;j.dispatchEvent("form-toggle-collapse",e,t),j.dispatchEvent("form-collapse",e,t)},re=e=>{var t=O.isZMax;return(e?!t:t)&&(O.isZMax=!t,O.tZindex<Xl())&&(O.tZindex=ql()),(0,Al.nextTick)().then(()=>L.recalculate(!0)).then(()=>(setTimeout(()=>L.recalculate(!0),15),O.isZMax))},b=(e,t)=>{e=e[t];if(e){if(!Il().isString(e))return e;if(d[e])return d[e];pl("vxe.error.notSlot",[e])}return null},le=e=>{let r={};return Il().objectMap(e,(e,t)=>{e&&(Il().isString(e)?d[e]?r[t]=d[e]:pl("vxe.error.notSlot",[e]):r[t]=e)}),r},oe=()=>{var{formConfig:e,proxyConfig:r}=k,l=O.formData,o=V.value,a=f.value;if(e&&Fl(a)||d.form){let e=[];if(d.form)e=d.form({$grid:B});else if(a.items){let t={};if(!a.inited){a.inited=!0;let t=o.beforeItem;o&&t&&a.items.forEach(e=>{t({$grid:B,item:e})})}a.items.forEach(e=>{Il().each(e.slots,e=>{Il().isFunction(e)||d[e]&&(t[e]=d[e])})}),n&&e.push((0,Al.h)(n,{ref:i,...Object.assign({},a,{data:r&&Fl(o)&&o.form?l:a.data}),onSubmit:Q,onReset:J,onSubmitInvalid:ee,onCollapse:te},t))}return(0,Al.h)("div",{ref:p,key:"form",class:"vxe-grid--form-wrapper"},e)}return fe(B)},ae=()=>{var t,r,l,o,a,n,i=k.toolbarConfig,s=H.value;if(i&&Fl(s)||d.toolbar){let e=[];return d.toolbar?e=d.toolbar({$grid:B}):(i={},(n=s.slots)&&(t=b(n,"buttons"),r=b(n,"buttonPrefix"),l=b(n,"buttonSuffix"),o=b(n,"tools"),a=b(n,"toolPrefix"),n=b(n,"toolSuffix"),t&&(i.buttons=t),r&&(i.buttonPrefix=r),l&&(i.buttonSuffix=l),o&&(i.tools=o),a&&(i.toolPrefix=a),n)&&(i.toolSuffix=n),e.push((0,Al.h)(jr,{ref:c,...s,slots:void 0},i))),(0,Al.h)("div",{ref:h,key:"toolbar",class:"vxe-grid--toolbar-wrapper"},e)}return fe(B)},ne=()=>d.top?(0,Al.h)("div",{ref:g,key:"top",class:"vxe-grid--top-wrapper"},d.top({$grid:B})):fe(B),ie=()=>{var e=d.left;return e?(0,Al.h)("div",{class:"vxe-grid--left-wrapper"},e({$grid:B})):fe(B)},se=()=>{var e=d.right;return e?(0,Al.h)("div",{class:"vxe-grid--right-wrapper"},e({$grid:B})):fe(B)},de=()=>{var e=k.proxyConfig,t=I.value,r=V.value,l=Object.assign({},pe),o=d.empty,a=d.loading,n=d.rowDragIcon||d["row-drag-icon"],i=d.columnDragIcon||d["column-drag-icon"],e=(e&&Fl(r)&&(r.sort&&(l.onSortChange=Y),r.filter)&&(l.onFilterChange=Z),{});return o&&(e.empty=o),a&&(e.loading=a),n&&(e.rowDragIcon=n),i&&(e.columnDragIcon=i),(0,Al.h)("div",{class:"vxe-grid--table-wrapper"},[(0,Al.h)(zr,{ref:A,...t,...l},e)])},ce=()=>d.bottom?(0,Al.h)("div",{ref:m,key:"bottom",class:"vxe-grid--bottom-wrapper"},d.bottom({$grid:B})):fe(B),ue=()=>{var{proxyConfig:e,pagerConfig:t}=k,r=V.value,l=_.value,o=d.pager;return t&&Fl(l)||d.pager?(0,Al.h)("div",{ref:v,key:"pager",class:"vxe-grid--pager-wrapper"},o?o({$grid:B}):[a?(0,Al.h)(a,{ref:u,...l,...e&&Fl(r)?O.tablePage:{},onPageChange:K},le(l.slots)):fe(B)]):fe(B)},C=e=>{let t=[];return e.forEach(e=>{switch(e){case"Form":t.push(oe());break;case"Toolbar":t.push(ae());break;case"Top":t.push(ne());break;case"Table":t.push((0,Al.h)("div",{key:"table",class:"vxe-grid--table-container"},[ie(),de(),se()]));break;case"Bottom":t.push(ce());break;case"Pager":t.push(ue());break;default:pl("vxe.error.notProp",["layouts -> "+e])}}),t},pe={},he=(Nr.forEach(t=>{var e=Il().camelCase("on-"+t);pe[e]=(...e)=>l(t,...e)}),()=>{var{proxyConfig:e,formConfig:t}=k,r=O.proxyInited,l=V.value,a=f.value;if(e&&Fl(l)){if(t&&Fl(a)&&l.form&&a.items){let o={};a.items.forEach(t=>{var{field:r,itemRender:l}=t;if(r){let e=null;l&&(l=l.defaultValue,Il().isFunction(l)?e=l({item:t}):Il().isUndefined(l)||(e=l)),o[r]=e}}),O.formData=o}r||!(O.proxyInited=!0)!==l.autoLoad&&(0,Al.nextTick)().then(()=>j.commitProxy("_init")).then(e=>{j.dispatchEvent("proxy-query",{...e,isInited:!0},new Event("init"))})}}),ge=e=>{var t=r.value;Ae.hasKey(e,Le.ESCAPE)&&O.isZMax&&!1!==t.escRestore&&w.triggerZoomEvent(e)};let j={dispatchEvent:(e,t,r)=>{l(e,Oe(r,{$grid:B},t))},getEl(){return s.value},commitProxy(t,...c){let{toolbarConfig:e,pagerConfig:n,editRules:u,validConfig:p}=k,i=O.tablePage,h=$.value,g=U.value;var r=V.value;let s=_.value;var l=H.value;let{beforeQuery:d,afterQuery:m,beforeDelete:v,afterDelete:f,beforeSave:x,afterSave:b,ajax:C={}}=r,w=r.response||r.props||{},y=A.value;var T=P();let E=null,R=null;R=Il().isString(t)?(r=l.buttons,l=e&&Fl(l)&&r?Il().findTree(r,e=>e.code===t,{children:"dropdowns"}):null,E=l?l.item:null,t):(E=t).code;var o=E?E.params:null;switch(R){case"insert":return y.insert({});case"insert_edit":return y.insert({}).then(({row:e})=>y.setEditRow(e));case"insert_actived":return y.insert({}).then(({row:e})=>y.setEditRow(e));case"mark_cancel":D=R,F=$.value,S=A.value,(I=S.getCheckboxRecords()).length?(S.togglePendingRow(I),L.clearCheckboxRow()):F&&Dl.VxeUI.modal&&Dl.VxeUI.modal.message({id:D,content:ve("vxe.grid.selectOneRecord"),status:"warning"});break;case"remove":return z(R,"vxe.grid.removeSelectRecord",()=>y.removeCheckboxRow());case"import":y.importData(o);break;case"open_import":y.openImport(o);break;case"export":y.exportData(o);break;case"open_export":y.openExport(o);break;case"reset_custom":return y.resetCustom(!0);case"_init":case"reload":case"query":{var S=C.query;let a=C.querySuccess,l=C.queryError;if(S){var D,I="_init"===R,F="reload"===R;if(!I&&O.tableLoading)return(0,Al.nextTick)();let t=[],r=[],e={};if(n&&((I||F)&&(i.currentPage=1),Fl(s))&&(e={...i}),I){let e=null;y&&(D=y.getComputeMaps().computeSortOpts,M=D.value,e=M.defaultSort),e&&(Il().isArray(e)||(e=[e]),t=e.map(e=>({field:e.field,property:e.field,order:e.order}))),y&&(r=y.getCheckedFilters())}else y&&(F?y.clearAll():(t=y.getSortColumns(),r=y.getCheckedFilters()));let o={code:R,button:E,isInited:I,isReload:F,$grid:B,page:e,sort:t.length?t[0]:{},sorts:t,filters:r,form:T,options:S};return O.sortData=t,O.filterData=r,O.tableLoading=!0,Promise.resolve((d||S)(o,...c)).then(e=>{let t=[];var r,l;return O.tableLoading=!1,e&&(n&&Fl(s)?(l=w.total,l=(Il().isFunction(l)?l({data:e,$grid:B}):Il().get(e,l||"page.total"))||0,i.total=Il().toNumber(l),r=w.result,t=(Il().isFunction(r)?r({data:e,$grid:B}):Il().get(e,r||"result"))||[],r=Math.max(Math.ceil(l/i.pageSize),1),i.currentPage>r&&(i.currentPage=r)):(l=w.list,t=(l?Il().isFunction(l)?l({data:e,$grid:B}):Il().get(e,l):e)||[])),y?y.loadData(t):(0,Al.nextTick)(()=>{y&&y.loadData(t)}),m&&m(o,...c),a&&a({...o,response:e}),{status:!0}}).catch(e=>(O.tableLoading=!1,l&&l({...o,response:e}),{status:!1}))}pl("vxe.error.notFunc",["proxy-config.ajax.query"]);break}case"delete":{let l=C.delete,o=C.deleteSuccess,a=C.deleteError;if(l){let e=L.getCheckboxRecords(),t=e.filter(e=>!y.isInsertByRow(e));var M={removeRecords:t};let r={$grid:B,code:R,button:E,body:M,form:T,options:l};if(e.length)return z(R,"vxe.grid.deleteSelectRecord",()=>t.length?(O.tableLoading=!0,Promise.resolve((v||l)(r,...c)).then(e=>(O.tableLoading=!1,y.setPendingRow(t,!1),g&&Dl.VxeUI.modal&&Dl.VxeUI.modal.message({content:N(e,"vxe.grid.delSuccess"),status:"success"}),f?f(r,...c):j.commitProxy("query"),o&&o({...r,response:e}),{status:!0})).catch(e=>(O.tableLoading=!1,g&&Dl.VxeUI.modal&&Dl.VxeUI.modal.message({id:R,content:N(e,"vxe.grid.operError"),status:"error"}),a&&a({...r,response:e}),{status:!1}))):y.remove(e));h&&Dl.VxeUI.modal&&Dl.VxeUI.modal.message({id:R,content:ve("vxe.grid.selectOneRecord"),status:"warning"})}else pl("vxe.error.notFunc",["proxy-config.ajax.delete"]);break}case"save":{let i=C.save,s=C.saveSuccess,d=C.saveError;if(i){let t=y.getRecordset(),{insertRecords:r,removeRecords:l,updateRecords:o,pendingRecords:a}=t,n={$grid:B,code:R,button:E,body:t,form:T,options:i},e=(r.length&&(t.pendingRecords=a.filter(e=>-1===y.findRowIndexOf(r,e))),a.length&&(t.insertRecords=r.filter(e=>-1===y.findRowIndexOf(a,e))),Promise.resolve());return(e=u?y[p&&"full"===p.msgMode?"fullValidate":"validate"](t.insertRecords.concat(o)):e).then(e=>{if(!e)return t.insertRecords.length||l.length||o.length||t.pendingRecords.length?(O.tableLoading=!0,Promise.resolve((x||i)(n,...c)).then(e=>(O.tableLoading=!1,y.clearPendingRow(),g&&Dl.VxeUI.modal&&Dl.VxeUI.modal.message({content:N(e,"vxe.grid.saveSuccess"),status:"success"}),b?b(n,...c):j.commitProxy("query"),s&&s({...n,response:e}),{status:!0})).catch(e=>(O.tableLoading=!1,g&&Dl.VxeUI.modal&&Dl.VxeUI.modal.message({id:R,content:N(e,"vxe.grid.operError"),status:"error"}),d&&d({...n,response:e}),{status:!1}))):void(h&&Dl.VxeUI.modal&&Dl.VxeUI.modal.message({id:R,content:ve("vxe.grid.dataUnchanged"),status:"info"}))})}pl("vxe.error.notFunc",["proxy-config.ajax.save"]);break}default:var M=Fe.get(R);M&&((M=M.tableCommandMethod||M.commandMethod)?M({code:R,button:E,$grid:B,$table:y},...c):pl("vxe.error.notCommands",[R]))}return(0,Al.nextTick)()},zoom(){return O.isZMax?j.revert():j.maximize()},isMaximized(){return O.isZMax},maximize(){return re(!0)},revert(){return re()},getFormData:P,getFormItems(e){var t=f.value,r=k.formConfig,l=t.items;let o=[];return Il().eachTree(r&&Fl(t)&&l?l:[],e=>{o.push(e)},{children:"children"}),Il().isUndefined(e)?o:o[e]},getProxyInfo(){var e,t=A.value;return k.proxyConfig?(e=O.sortData,{data:t?t.getFullData():[],filter:O.filterData,form:P(),sort:e.length?e[0]:{},sorts:e,pager:O.tablePage,pendingRecords:t?t.getPendingRecords():[]}):null}},w={extendTableMethods:t,callSlot(e,t){return e&&(Il().isString(e)&&(e=d[e]||null),Il().isFunction(e))?Gl(e(t)):[]},getExcludeHeight(){var e,t,r,l,o,a,n=O.isZMax,i=s.value;return i?(e=p.value,t=h.value,r=g.value,l=m.value,o=v.value,a=i.parentElement,(!n&&a?ro(a):0)+ro(i)+xr(e)+xr(t)+xr(r)+xr(l)+xr(o)):0},getParentHeight(){var e=s.value;return e?(e=e.parentElement,(O.isZMax?fr().visibleHeight:e?Il().toNumber(getComputedStyle(e).height):0)-w.getExcludeHeight()):0},triggerToolbarCommitEvent(e,t){let r=e.code;return j.commitProxy(e,t).then(e=>{r&&e&&e.status&&["query","reload","delete","save"].includes(r)&&j.dispatchEvent("delete"===r||"save"===r?"proxy-"+r:"proxy-query",{...e,isReload:"reload"===r},t)})},triggerToolbarBtnEvent(e,t){w.triggerToolbarCommitEvent(e,t),j.dispatchEvent("toolbar-button-click",{code:e.code,button:e},t)},triggerToolbarTolEvent(e,t){w.triggerToolbarCommitEvent(e,t),j.dispatchEvent("toolbar-tool-click",{code:e.code,tool:e},t)},triggerZoomEvent(e){j.zoom(),j.dispatchEvent("zoom",{type:O.isZMax?"max":"revert"},e)}},y=(Object.assign(B,L,j,w,{loadColumn(e){var t=A.value;return Il().eachTree(e,e=>{e.slots&&Il().each(e.slots,e=>{Il().isFunction(e)||d[e]||pl("vxe.error.notSlot",[e])})}),t?t.loadColumn(e):(0,Al.nextTick)()},reloadColumn(e){return B.clearAll(),B.loadColumn(e)}}),(0,Al.ref)(0));(0,Al.watch)(()=>k.columns?k.columns.length:-1,()=>{y.value++}),(0,Al.watch)(()=>k.columns,()=>{y.value++}),(0,Al.watch)(y,()=>{(0,Al.nextTick)(()=>B.loadColumn(k.columns||[]))}),(0,Al.watch)(()=>k.toolbarConfig,()=>{o()}),(0,Al.watch)(M,()=>{x("currentPage")}),(0,Al.watch)(W,()=>{x("pageSize")}),(0,Al.watch)(G,()=>{x("total")}),(0,Al.watch)(()=>k.proxyConfig,()=>{he()}),Me.forEach(e=>{var e=e.setupGrid;e&&(e=e(B))&&Il().isObject(e)&&Object.assign(B,e)}),x(),(0,Al.onMounted)(()=>{(0,Al.nextTick)(()=>{var e=k.columns;k.formConfig&&!n&&pl("vxe.error.reqComp",["vxe-form"]),k.pagerConfig&&!a&&pl("vxe.error.reqComp",["vxe-pager"]),e&&e.length&&B.loadColumn(e),o(),he()}),Ae.on(B,"keydown",ge)}),(0,Al.onUnmounted)(()=>{Ae.off(B,"keydown")});return B.renderVN=()=>{var e=R.value,t=S.value;return(0,Al.h)("div",{ref:s,class:["vxe-grid",{["size--"+e]:e,"is--animat":!!k.animat,"is--round":k.round,"is--maximize":O.isZMax,"is--loading":k.loading||O.tableLoading}],style:t},(()=>{var{headKeys:e,bodyKeys:t,footKeys:r}=F.value,l=d.asideLeft||d["aside-left"],o=d.asideRight||d["aside-right"];return[(0,Al.h)("div",{class:"vxe-grid--layout-header-wrapper"},C(e)),(0,Al.h)("div",{class:"vxe-grid--layout-body-wrapper"},[l?(0,Al.h)("div",{class:"vxe-grid--layout-aside-left-wrapper"},l({})):fe(B),(0,Al.h)("div",{class:"vxe-grid--layout-body-content-wrapper"},C(t)),o?(0,Al.h)("div",{class:"vxe-grid--layout-aside-right-wrapper"},o({})):fe(B)]),(0,Al.h)("div",{class:"vxe-grid--layout-footer-wrapper"},C(r))]})())},(0,Al.provide)("$xeGrid",B),B},render(){return this.renderVN()}});let _e=Object.assign({},Ur,{install(e){e.component(Ur.name,Ur)}}),He=(Dl.VxeUI.dynamicApp&&Dl.VxeUI.dynamicApp.component(Ur.name,Ur),Dl.VxeUI.component(Ur),_e),{renderer:Be,hooks:Pe}=Dl.VxeUI,Ne=["openFilter","setFilter","clearFilter","saveFilterPanel","resetFilterPanel","getCheckedFilters","updateFilterOptionStatus"],{menus:ze,hooks:je,globalEvents:Ue,GLOBAL_EVENT_KEYS:We}=(Pe.add("tableFilterModule",{setupTable(x){let{props:u,reactData:p,internalData:b}=x,{refElem:C,refTableFilter:w}=x.getRefMaps(),{computeFilterOpts:y,computeMouseOpts:h}=x.getComputeMaps(),o=e=>{var t=p.filterStore;t.options.forEach(e=>{e.checked=e._checked}),x.confirmFilterEvent(e)},a=(e,t,r)=>{var l=p.filterStore;l.options.forEach(e=>{e._checked=!1}),r._checked=t,x.checkFilterOptions(),o(e)},n=(e,t,r)=>{r._checked=t,x.checkFilterOptions()},t=e=>{var t=p.filterStore;x.handleClearFilter(t.column),x.confirmFilterEvent(e),e&&x.dispatchEvent("clear-filter",{filterList:[]},e)};var e={checkFilterOptions(){var e=p.filterStore;e.isAllSelected=e.options.every(e=>e._checked),e.isIndeterminate=!e.isAllSelected&&e.options.some(e=>e._checked)},triggerFilterEvent(e,o,t){let{initStore:r,filterStore:v}=p,f=b.elemStore;if(v.column===o&&v.visible)v.visible=!1;else{let s=C.value,{scrollTop:d,scrollLeft:c,visibleHeight:u,visibleWidth:p}=fr();let h=y.value.transfer,g=s.getBoundingClientRect(),m=e.currentTarget;var{filters:a,filterMultiple:n,filterRender:i}=o,i=Fl(i)?Be.get(i.name):null;let l=o.filterRecoverMethod||(i?i.tableFilterRecoverMethod||i.filterRecoverMethod:null);b._currFilterParams=t,Object.assign(v,{multiple:n,options:a,column:o,style:null}),v.options.forEach(e=>{var{_checked:t,checked:r}=e;(e._checked=r)||t===r||l&&l({option:e,column:o,$table:x})}),this.checkFilterOptions(),v.visible=!0,r.filter=!0,(0,Al.nextTick)(()=>{if(Wl(f["main-header-scroll"])){var l=w.value,l=l?l.getRefMaps().refElem.value:null;if(l){var o=m.getBoundingClientRect(),a=l.querySelector(".vxe-table--filter-header"),n=l.querySelector(".vxe-table--filter-footer"),l=l.offsetWidth,i=l/2;let e=0,t=0,r=0;h?(e=o.left-i+c,t=o.top+m.clientHeight+d,r=Math.min(Math.max(g.height,Math.floor(u/2)),Math.max(80,u-t-(a?a.clientHeight:0)-(n?n.clientHeight:0)-28)),e<16?e=16:e>p-l-16&&(e=p-l-16)):(e=o.left-g.left-i,t=o.top-g.top+m.clientHeight,r=Math.max(40,s.clientHeight-t-(a?a.clientHeight:0)-(n?n.clientHeight:0)-14),e<1?e=1:e>s.clientWidth-l-1&&(e=s.clientWidth-l-1)),v.style={top:to(t),left:to(e)},v.maxHeight=r}}})}x.dispatchEvent("filter-visible",{column:o,field:o.field,property:o.field,filterList:x.getCheckedFilters(),visible:v.visible},e)},handleClearFilter(e){if(e){var{filters:r,filterRender:l}=e;if(r){l=Fl(l)?Be.get(l.name):null;let t=e.filterResetMethod||(l?l.tableFilterResetMethod||l.filterResetMethod:null);r.forEach(e=>{e._checked=!1,e.checked=!1,t||(e.data=Il().clone(e.resetValue,!0))}),t&&t({options:r,column:e,$table:x})}}},handleColumnConfirmFilter(e,t){var r=u.mouseConfig;let{scrollXLoad:l,scrollYLoad:o}=p;var a=y.value,n=h.value,i=e.field;let s=[],d=[];e.filters.forEach(e=>{e.checked&&(s.push(e.value),d.push(e.data))});var c=x.getCheckedFilters(),e={$table:x,$event:t,column:e,field:i,property:i,values:s,datas:d,filters:c,filterList:c};return a.remote||(x.handleTableData(!0),x.checkSelectionStatus()),r&&n.area&&x.handleFilterEvent&&x.handleFilterEvent(t,e),t&&x.dispatchEvent("filter-change",e,t),x.closeFilter(),x.updateFooter().then(()=>{var{scrollXLoad:e,scrollYLoad:t}=p;if(l||e||o||t)return(l||e)&&x.updateScrollXSpace(),(o||t)&&x.updateScrollYSpace(),x.refreshScroll()}).then(()=>(x.updateCellAreas(),x.recalculate(!0))).then(()=>{setTimeout(()=>x.recalculate(),50)})},confirmFilterEvent(e){var t=p.filterStore,t=t.column;x.handleColumnConfirmFilter(t,e)},handleFilterChangeRadioOption:a,handleFilterChangeMultipleOption:n,handleFilterChangeOption(e,t,r){var l=p.filterStore;l.multiple?n(0,t,r):a(e,t,r)},handleFilterConfirmFilter:o,handleFilterResetFilter:t};return{...{openFilter(e){let o=zl(x,e);if(o&&o.filters){let r=b.elemStore,l=o.fixed;return x.scrollToColumn(o).then(()=>{var e,t=Wl(r[`${l||"main"}-header-wrapper`]||r["main-header-wrapper"]);t&&(t=t.querySelector(`.vxe-header--column.${o.id} .vxe-cell--filter`),e="click",t=t)&&t.dispatchEvent(new Event(e))})}return(0,Al.nextTick)()},setFilter(e,t,r){e=zl(x,e);return e&&e.filters&&(e.filters=so(t||[]),r)?x.handleColumnConfirmFilter(e,new Event("click")):(0,Al.nextTick)()},clearFilter(e){var t=p.filterStore,r=b.tableFullColumn,l=y.value;let o;return e?(o=zl(x,e))&&x.handleClearFilter(o):r.forEach(x.handleClearFilter),e&&o===t.column||Object.assign(t,{isAllSelected:!1,isIndeterminate:!1,style:null,options:[],column:null,multiple:!1,visible:!1}),l.remote?(0,Al.nextTick)():x.updateData()},saveFilterPanel(){return o(null),(0,Al.nextTick)()},saveFilterPanelByEvent(e){return o(e),(0,Al.nextTick)()},resetFilterPanel(){return t(null),(0,Al.nextTick)()},resetFilterPanelByEvent(e){return t(e),(0,Al.nextTick)()},getCheckedFilters(){var e=b.tableFullColumn;let a=[];return e.forEach(e=>{var{field:t,filters:r}=e;let l=[],o=[];r&&r.length&&(r.forEach(e=>{e.checked&&(l.push(e.value),o.push(e.data))}),l.length)&&a.push({column:e,field:t,property:t,values:l,datas:o})}),a},updateFilterOptionStatus(e,t){return e._checked=t,e.checked=t,(0,Al.nextTick)()}},...e}},setupGrid(e){return e.extendTableMethods(Ne)}}),Dl.VxeUI),Ge=["closeMenu"],{getConfig:qe,renderer:Xe,hooks:Ke,getI18n:Ye}=(je.add("tableMenuModule",{setupTable(v){let{xID:f,props:x,reactData:b,internalData:C}=v,{refElem:w,refTableFilter:y,refTableMenu:T}=v.getRefMaps(),{computeMouseOpts:E,computeIsMenu:t,computeMenuOpts:R}=v.getComputeMaps(),S;var e;let D=(r,l,p)=>{let h=b.ctxMenuStore;var o=t.value,e=R.value,l=e[l];let a=e.visibleMethod;if(l){let{options:t,disabled:e}=l;e?r.preventDefault():o&&t&&t.length&&(p.options=t,v.preventEvent(r,"event.showMenu",p,()=>{if(!a||a(p)){r.preventDefault(),v.updateZindex();let{scrollTop:o,scrollLeft:a,visibleHeight:n,visibleWidth:i}=fr(),s=r.clientY+o,d=r.clientX+a,l=()=>{C._currMenuParams=p,Object.assign(h,{visible:!0,list:t,selected:null,selectChild:null,showChild:!1,style:{zIndex:C.tZindex,top:s+"px",left:d+"px"}}),(0,Al.nextTick)(()=>{var e=T.value.getRefMaps().refElem.value,t=e.clientHeight,r=e.clientWidth,{boundingTop:e,boundingLeft:l}=Cr(e),e=e+t-n,l=l+r-i;-10<e&&(h.style.top=Math.max(o+2,s-t-2)+"px"),-10<l&&(h.style.left=Math.max(a+2,d-r-2)+"px")})},{keyboard:e,row:c,column:u}=p;e&&c&&u?v.scrollToRow(c,u).then(()=>{var e,t,r=v.getCellElement(c,u);r&&({boundingTop:e,boundingLeft:t}=Cr(r),s=e+o+Math.floor(r.offsetHeight/2),d=t+a+Math.floor(r.offsetWidth/2)),l()}):l()}else S.closeMenu()}))}v.closeFilter()};return S={closeMenu(){return Object.assign(b.ctxMenuStore,{visible:!1,selected:null,selectChild:null,showChild:!1}),(0,Al.nextTick)()}},e={moveCtxMenu(e,t,r,l,o,a){let n;var i=Il().findIndexOf(a,e=>t[r]===e);if(l)o&&Kl(t.selected)?t.showChild=!0:(t.showChild=!1,t.selectChild=null);else if(Ue.hasKey(e,We.ARROW_UP)){for(let e=i-1;0<=e;e--)if(!1!==a[e].visible){n=a[e];break}t[r]=n||a[a.length-1]}else if(Ue.hasKey(e,We.ARROW_DOWN)){for(let e=i+1;e<a.length;e++)if(!1!==a[e].visible){n=a[e];break}t[r]=n||a[0]}else t[r]&&(Ue.hasKey(e,We.ENTER)||Ue.hasKey(e,We.SPACEBAR))&&v.ctxMenuLinkEvent(e,t[r])},handleOpenMenuEvent:D,handleGlobalContextmenuEvent(t){var{mouseConfig:e,menuConfig:r}=x,{editStore:l,ctxMenuStore:o}=b,a=C.visibleColumn,n=y.value,i=T.value,s=E.value,d=R.value,c=w.value,l=l.selected,u=["header","body","footer"];if(Fl(r)){if(o.visible&&i&&Bl(t,i.getRefMaps().refElem.value).flag)return void t.preventDefault();if(C._keyCtx){r={type:"body",$table:v,keyboard:!0,columns:a.slice(0),$event:t};if(e&&s.area){o=v.getActiveCellArea();if(o&&o.row&&o.column)return r.row=o.row,r.column=o.column,void D(t,"body",r)}else if(e&&s.selected&&l.row&&l.column)return r.row=l.row,r.column=l.column,void D(t,"body",r)}for(let e=0;e<u.length;e++){var p=u[e],h=Bl(t,c,`vxe-${p}--column`,e=>e.parentNode.parentNode.parentNode.getAttribute("xid")===f),g={type:p,$table:v,columns:a.slice(0),$event:t};if(h.flag){var h=h.targetElem,m=v.getColumnNode(h),m=m?m.item:null;let e=p+"-";m&&Object.assign(g,{column:m,columnIndex:v.getColumnIndex(m),cell:h}),"body"===p&&(h=(m=v.getRowNode(h.parentNode))?m.item:null,e="",h)&&(g.row=h,g.rowIndex=v.getRowIndex(h));m=e+"cell-menu";return D(t,p,g),void v.dispatchEvent(m,g,t)}if(Bl(t,c,`vxe-table--${p}-wrapper`,e=>e.getAttribute("xid")===f).flag)return void("cell"===d.trigger?t.preventDefault():D(t,p,g))}}n&&!Bl(t,n.getRefMaps().refElem.value).flag&&v.closeFilter(),S.closeMenu()},ctxMenuMouseoverEvent(e,t,r){let d=e.currentTarget;var l=b.ctxMenuStore;e.preventDefault(),e.stopPropagation(),l.selected=t,(l.selectChild=r)||(l.showChild=Kl(t),l.showChild&&(0,Al.nextTick)(()=>{var o=d.nextElementSibling;if(o){var{boundingTop:a,boundingLeft:n,visibleHeight:i,visibleWidth:s}=Cr(d),a=a+d.offsetHeight;let e="",t="",r=(n+d.offsetWidth+o.offsetWidth>s-10&&(e="auto",t=d.offsetWidth+"px"),""),l="";a+o.offsetHeight>i-10&&(r="auto",l="0"),o.style.left=e,o.style.right=t,o.style.top=r,o.style.bottom=l}}))},ctxMenuMouseoutEvent(e,t){var r=b.ctxMenuStore;t.children||(r.selected=null),r.selectChild=null},ctxMenuLinkEvent(e,t){var r,l=v.xeGrid;t.disabled||!t.code&&t.children&&t.children.length||(r=ze.get(t.code),t=Object.assign({},C._currMenuParams,{menu:t,$table:v,$grid:l,$event:e}),(l=r?r.tableMenuMethod||r.menuMethod:null)&&l(t,e),v.dispatchEvent("menu-click",t,e),S.closeMenu())}},{...S,...e}},setupGrid(e){return e.extendTableMethods(Ge)}}),Dl.VxeUI),Ze=["insert","insertAt","insertNextAt","insertChild","insertChildAt","insertChildNextAt","remove","removeCheckboxRow","removeRadioRow","removeCurrentRow","getRecordset","getInsertRecords","getRemoveRecords","getUpdateRecords","getEditRecord","getActiveRecord","getSelectedCell","clearEdit","clearActived","clearSelected","isEditByRow","isActiveByRow","setEditRow","setActiveRow","setEditCell","setActiveCell","setSelectCell"],Qe=(Ke.add("tableEditModule",{setupTable(x){let{props:b,reactData:C,internalData:w}=x,o=x.getRefMaps().refElem,{computeMouseOpts:u,computeEditOpts:f,computeCheckboxOpts:y,computeTreeOpts:T,computeValidOpts:i}=x.getComputeMaps(),c=Il().browse(),E={},p={},R=(e,t)=>{var{model:r,editRender:l}=t;l&&(r.value=jl(e,t),r.update=!1)},a=(e,t)=>{var{model:r,editRender:l}=t;l&&r.update&&(Ul(e,t,r.value),r.update=!1,r.value=null)},r=()=>{var e=o.value;e&&(e=e.querySelector(".col--selected"))&&Vl(e,"col--selected")},s=()=>{var{editStore:e,tableColumn:t}=C,r=f.value,e=e.actived;let{row:l,column:o}=e;(l||o)&&("row"===r.mode?t.forEach(e=>a(l,e)):a(l,o))},S=(e,t)=>{let{tableFullTreeData:n,afterFullData:i,fullDataRowIdData:s,fullAllDataRowIdData:d}=w;var r=T.value;let{rowField:c,parentField:u,mapChildrenField:p}=r,h=r.children||r.childrenField,g=t?"push":"unshift";e.forEach(r=>{let t=r[u];var l=Pl(x,r),o=t?Il().findTree(n,e=>t===e[c],{children:p}):null;if(o){var o=o.item,a=d[Pl(x,o)],a=a?a.level:0;let e=o[h],t=o[p];Il().isArray(e)||(e=o[h]=[]),Il().isArray(t)||(t=o[h]=[]),e[g](r),t[g](r);o={row:r,rowid:l,seq:-1,index:-1,_index:-1,$index:-1,treeIndex:-1,items:e,parent:o,level:a+1,height:0,resizeHeight:0,oTop:0,expandHeight:0};s[l]=o,d[l]=o}else{t&&ul("vxe.error.unableInsert"),i[g](r),n[g](r);a={row:r,rowid:l,seq:-1,index:-1,_index:-1,$index:-1,treeIndex:-1,items:n,parent:null,level:0,height:0,resizeHeight:0,oTop:0,expandHeight:0};s[l]=a,d[l]=a}})},d=(t,r,s)=>{let o=b.treeConfig,{tableFullTreeData:e,afterFullData:a,mergeBodyList:n,tableFullData:i,fullDataRowIdData:d,fullAllDataRowIdData:c,insertRowMaps:l}=w,u=T.value,{transform:p,rowField:h,mapChildrenField:g}=u,m=u.children||u.childrenField,v=(Il().isArray(t)||(t=[t]),(0,Al.reactive)(x.defineField(t.map(e=>Object.assign(o&&p?{[g]:[],[m]:[]}:{},e)))));if(Il().eqNull(r))o&&p?S(v,!1):(v.forEach(e=>{var t=Pl(x,e),r={row:e,rowid:t,seq:-1,index:-1,_index:-1,$index:-1,treeIndex:-1,items:a,parent:null,level:0,height:0,resizeHeight:0,oTop:0,expandHeight:0};d[t]=r,c[t]=r,a.unshift(e),i.unshift(e)}),n.forEach(e=>{var t=e.row;0<t&&(e.row=t+v.length)}));else if(-1===r)o&&p?S(v,!0):(v.forEach(e=>{var t=Pl(x,e),r={row:e,rowid:t,seq:-1,index:-1,_index:-1,treeIndex:-1,$index:-1,items:a,parent:null,level:0,height:0,resizeHeight:0,oTop:0,expandHeight:0};d[t]=r,c[t]=r,a.push(e),i.push(e)}),n.forEach(e=>{var{row:t,rowspan:r}=e;t+r>a.length&&(e.rowspan=r+v.length)}));else if(o&&p){let i=Il().findTree(e,e=>r[h]===e[h],{children:g});if(i){let o=i.parent,a=o?o[g]:e;t=c[Pl(x,o)];let n=t?t.level:0;if(v.forEach((e,t)=>{var r=Pl(x,e);e[u.parentField]&&o&&e[u.parentField]!==o[h]&&pl("vxe.error.errProp",[u.parentField+"="+e[u.parentField],u.parentField+"="+o[h]]),o&&(e[u.parentField]=o[h]);let l=i.index+t;s&&(l+=1),a.splice(l,0,e);t={row:e,rowid:r,seq:-1,index:-1,_index:-1,$index:-1,treeIndex:-1,items:a,parent:o,level:n+1,height:0,resizeHeight:0,oTop:0,expandHeight:0};d[r]=t,c[r]=t}),o){t=Il().findTree(e,e=>r[h]===e[h],{children:m});if(t){var f=t.items;let e=t.index;s&&(e+=1),f.splice(e,0,...v)}}}else ul("vxe.error.unableInsert"),S(v,!0)}else{if(o)throw new Error(Ye("vxe.error.noTree",["insert"]));let l=-1;if(Il().isNumber(r)?r<a.length&&(l=r):l=x.findRowIndexOf(a,r),-1===(l=s?Math.min(a.length,l+1):l))throw new Error(Ye("vxe.error.unableInsert"));a.splice(l,0,...v);t=x.findRowIndexOf(i,r);-1<t?i.splice(t+(s?1:0),0,...v):i.push(...v),n.forEach(e=>{var{row:t,rowspan:r}=e;t>l?e.row=t+v.length:t+r>l&&(e.rowspan=r+v.length)})}return v.forEach(e=>{var t=Pl(x,e);l[t]=e}),C.insertRowFlag++,x.cacheRowMap(!1),x.updateScrollYStatus(),x.handleTableData(o&&p),o&&p||x.updateAfterDataIndex(),x.updateFooter(),x.handleUpdateBodyMerge(),x.checkSelectionStatus(),C.scrollYLoad&&x.updateScrollYSpace(),(0,Al.nextTick)().then(()=>(x.updateCellAreas(),x.recalculate(!0))).then(()=>({row:v.length?v[v.length-1]:null,rows:v}))},l=(e,t,r,l)=>{var o=b.treeConfig;let{transform:a,rowField:n,parentField:i}=T.value;return o&&a?(Il().isArray(e)||(e=[e]),d(e.map(e=>Object.assign({},e,{[i]:t[n]})),r,l)):(pl("vxe.error.errProp",["tree-config.transform=false","tree-config.transform=true"]),Promise.resolve({row:null,rows:[]}))},D=(e,t)=>{var r=C.editStore,{actived:r,focused:l}=r,{row:o,column:a}=r,n=i.value;if(o||a){if(t&&Pl(x,t)!==Pl(x,o))return(0,Al.nextTick)();s(),r.args=null,r.row=null,r.column=null,x.updateFooter(),x.dispatchEvent("edit-closed",{row:o,rowIndex:x.getRowIndex(o),$rowIndex:x.getVMRowIndex(o),column:a,columnIndex:x.getColumnIndex(a),$columnIndex:x.getVMColumnIndex(a)},e||null)}return l.row=null,l.column=null,n.autoClear&&("full"!==n.msgMode||"obsolete"===qe().cellVaildMode)&&x.clearValidate?x.clearValidate():(0,Al.nextTick)().then(()=>x.updateCellAreas())},n=(r,l,o,e)=>{let a=x.xeGrid;var{editConfig:t,mouseConfig:n}=b,{editStore:i,tableColumn:s}=C,d=f.value,c=d.mode,{actived:i,focused:u}=i;let{row:p,column:h}=r;var g=h.editRender,m=r.cell||x.getCellElement(p,h),v=d.beforeEditMethod||d.activeMethod;if((r.cell=m)&&Fl(t)&&Fl(g)&&!x.isPendingByRow(p)&&!x.isAggregateRecord(p)){if(i.row!==p||"cell"===c&&i.column!==h){let t="edit-disabled";if(!v||v({...r,$table:x,$grid:a})){n&&(x.clearSelected(),x.clearCellAreas)&&(x.clearCellAreas(),x.clearCopyCellArea()),x.closeTooltip(),i.column&&D(l),t="edit-activated",h.renderHeight=m.offsetHeight,i.args=r,i.row=p,i.column=h,"row"===c?s.forEach(e=>R(p,e)):R(p,h);let e=d.afterEditMethod;(0,Al.nextTick)(()=>{o&&x.handleFocus(r,l),e&&e({...r,$table:x,$grid:a})})}x.dispatchEvent(t,{row:p,rowIndex:x.getRowIndex(p),$rowIndex:x.getVMRowIndex(p),column:h,columnIndex:x.getColumnIndex(h),$columnIndex:x.getVMColumnIndex(h)},l),"edit-activated"===t&&x.dispatchEvent("edit-actived",{row:p,rowIndex:x.getRowIndex(p),$rowIndex:x.getVMRowIndex(p),column:h,columnIndex:x.getColumnIndex(h),$columnIndex:x.getVMColumnIndex(h)},l)}else{t=i.column;n&&(x.clearSelected(),x.clearCellAreas)&&(x.clearCellAreas(),x.clearCopyCellArea()),t!==h&&(g=t.model,g.update&&Ul(p,t,g.value),x.clearValidate)&&x.clearValidate(p,h),h.renderHeight=m.offsetHeight,i.args=r,i.column=h,e&&setTimeout(()=>{x.handleFocus(r,l)})}u.column=null,u.row=null,x.focus()}return(0,Al.nextTick)()},h=(t,e,r)=>{var l=b.editConfig;let o=Il().isString(e)?x.getColumnByField(e):e;return t&&o&&Fl(l)&&Fl(o.editRender)&&!x.isAggregateRecord(t)?Promise.resolve(r?x.scrollToRow(t,o):null).then(()=>{var e=x.getCellElement(t,o);return e&&(n({row:t,rowIndex:x.getRowIndex(t),column:o,columnIndex:x.getColumnIndex(o),cell:e,$table:x},null,r,r),w._lastCallTime=Date.now()),(0,Al.nextTick)()}):(0,Al.nextTick)()};return E={insert(e){return d(e,null)},insertAt(e,t){return d(e,t)},insertNextAt(e,t){return d(e,t,!0)},insertChild(e,t){return l(e,t,null)},insertChildAt(e,t,r){return l(e,t,r)},insertChildNextAt(e,t,r){return l(e,t,r,!0)},remove(e){var t=b.treeConfig,r=C.editStore;let{tableFullTreeData:l,selectCheckboxMaps:o,afterFullData:a,mergeBodyList:n,tableFullData:i,pendingRowMaps:s,insertRowMaps:d,removeRowMaps:c}=w;var u=y.value,p=T.value;let{transform:h,mapChildrenField:g}=p,m=p.children||p.childrenField;p=r.actived,r=u.checkField;let v=[];return e?Il().isArray(e)||(e=[e]):e=i,e.forEach(e=>{var t;x.isInsertByRow(e)||(t=Pl(x,e),c[t]=e)}),r||(e.forEach(e=>{e=Pl(x,e);o[e]&&delete o[e]}),C.updateCheckboxFlag++),i===e?(e=v=i.slice(0),w.tableFullData=[],w.afterFullData=[],x.clearMergeCells()):t&&h?e.forEach(e=>{let t=Pl(x,e);var r=Il().findTree(l,e=>t===Pl(x,e),{children:g}),r=(r&&(r=r.items.splice(r.index,1),v.push(r[0])),Il().findTree(l,e=>t===Pl(x,e),{children:m})),r=(r&&r.items.splice(r.index,1),x.findRowIndexOf(a,e));-1<r&&a.splice(r,1)}):e.forEach(e=>{var t=x.findRowIndexOf(i,e);-1<t&&(t=i.splice(t,1),v.push(t[0]));let l=x.findRowIndexOf(a,e);-1<l&&(n.forEach(e=>{var{row:t,rowspan:r}=e;t>l?e.row=t-1:t+r>l&&(e.rowspan=r-1)}),a.splice(l,1))}),p.row&&-1<x.findRowIndexOf(e,p.row)&&E.clearEdit(),e.forEach(e=>{e=Pl(x,e);d[e]&&delete d[e],s[e]&&delete s[e]}),C.removeRowFlag++,C.insertRowFlag++,C.pendingRowFlag++,x.cacheRowMap(!1),x.handleTableData(t&&h),x.updateFooter(),x.handleUpdateBodyMerge(),t&&h||x.updateAfterDataIndex(),x.checkSelectionStatus(),C.scrollYLoad&&x.updateScrollYSpace(),(0,Al.nextTick)().then(()=>(x.updateCellAreas(),x.recalculate(!0))).then(()=>({row:v.length?v[v.length-1]:null,rows:v}))},removeCheckboxRow(){return E.remove(x.getCheckboxRecords()).then(e=>(x.clearCheckboxRow(),e))},removeRadioRow(){var e=x.getRadioRecord();return E.remove(e||[]).then(e=>(x.clearRadioRow(),e))},removeCurrentRow(){var e=x.getCurrentRecord();return E.remove(e||[]).then(e=>(x.clearCurrentRow(),e))},getRecordset(){var e=E.getRemoveRecords(),t=x.getPendingRecords();let r=e.concat(t);var l=E.getUpdateRecords().filter(t=>!r.some(e=>x.eqRow(e,t)));return{insertRecords:E.getInsertRecords(),removeRecords:e,updateRecords:l,pendingRecords:t}},getInsertRecords(){let{fullAllDataRowIdData:r,insertRowMaps:e}=w,l=[];return Il().each(e,(e,t)=>{r[t]&&l.push(e)}),l},getRemoveRecords(){var e=w.removeRowMaps;let t=[];return Il().each(e,e=>{t.push(e)}),t},getUpdateRecords(){var{keepSource:e,treeConfig:t}=b,r=w.tableFullData,l=T.value;return e?(s(),t?Il().filterTree(r,e=>x.isUpdateByRow(e),l):r.filter(e=>x.isUpdateByRow(e))):[]},getActiveRecord(){return ul("vxe.error.delFunc",["getActiveRecord","getEditRecord"]),x.getEditRecord()},getEditRecord(){var e=C.editStore,t=w.afterFullData,r=o.value,{args:e,row:l}=e.actived;return e&&-1<x.findRowIndexOf(t,l)&&r.querySelectorAll(".vxe-body--column.col--active").length?Object.assign({},e):null},getSelectedCell(){var e=C.editStore,{args:e,column:t}=e.selected;return e&&t?Object.assign({},e):null},clearActived(e){return ul("vxe.error.delFunc",["clearActived","clearEdit"]),x.clearEdit(e)},clearEdit(e){return D(null,e)},clearSelected(){var e=C.editStore,e=e.selected;return e.row=null,e.column=null,r(),(0,Al.nextTick)()},isActiveByRow(e){return ul("vxe.error.delFunc",["isActiveByRow","isEditByRow"]),x.isEditByRow(e)},isEditByRow(e){var t=C.editStore;return t.actived.row===e},setActiveRow(e){return ul("vxe.error.delFunc",["setActiveRow","setEditRow"]),E.setEditRow(e)},setEditRow(e,t){var r=w.visibleColumn;let l=Il().find(r,e=>Fl(e.editRender)),o=!1;return t&&(o=!0)!==t&&(l=Il().isString(t)?x.getColumnByField(t):t),h(e,l,o)},setActiveCell(e,t){return ul("vxe.error.delFunc",["setActiveCell","setEditCell"]),E.setEditCell(e,t)},setEditCell(e,t){return h(e,t,!0)},setSelectCell(e,t){var r=C.tableData,l=f.value,t=Il().isString(t)?x.getColumnByField(t):t;return e&&t&&"manual"!==l.trigger&&-1<(l=x.findRowIndexOf(r,e))&&t&&(r=x.getCellElement(e,t),e={row:e,rowIndex:l,column:t,columnIndex:x.getColumnIndex(t),cell:r},x.handleSelected(e,{})),(0,Al.nextTick)()}},p={handleEdit(e,t){return n(e,t,!0,!0)},handleActived(e,t){return p.handleEdit(e,t)},handleClearEdit:D,handleFocus(l){var{row:o,column:a,cell:n}=l,i=a.editRender,s=f.value;if(Fl(i)){var d=Xe.get(i.name);let e=i.autofocus||i.autoFocus,t=i.autoSelect||i.autoselect,r;s.autoFocus&&(!e&&d&&(e=d.tableAutoFocus||d.tableAutofocus||d.autofocus),!t&&d&&(t=d.tableAutoSelect||d.autoselect),Il().isFunction(e)?r=e(l):e&&(r=!0===e?n.querySelector("input,textarea"):n.querySelector(e))&&r.focus()),r?t?r.select():c.msie&&((i=r.createTextRange()).collapse(!1),i.select()):s.autoPos&&!a.fixed&&x.scrollToRow(o,a)}},handleSelected(e,t){var r=b.mouseConfig,l=C.editStore,o=u.value;let a=f.value,{actived:n,selected:i}=l,{row:s,column:d}=e,c=r&&o.selected;return!c||i.row===s&&i.column===d||(n.row!==s||"cell"===a.mode&&n.column!==d)&&(D(t),x.clearSelected(),x.clearCellAreas&&(x.clearCellAreas(),x.clearCopyCellArea()),i.args=e,i.row=s,i.column=d,c&&p.addCellSelectedClass(),x.focus(),t)&&x.dispatchEvent("cell-selected",e,t),(0,Al.nextTick)()},addCellSelectedClass(){var e=C.editStore,e=e.selected,{row:e,column:t}=e;r(),e&&t&&(e=x.getCellElement(e,t))&&$l(e,"col--selected")}},{...E,...p}},setupGrid(e){return e.extendTableMethods(Ze)}}),'body{margin:0;padding: 0 1px;color:#333333;font-size:14px;font-family:"Microsoft YaHei",微软雅黑,"MicrosoftJhengHei",华文细黑,STHeiti,MingLiu}body *{-webkit-box-sizing:border-box;box-sizing:border-box}.vxe-table{border-collapse:collapse;text-align:left;border-spacing:0}.vxe-table:not(.is--print){table-layout:fixed}.vxe-table,.vxe-table th,.vxe-table td,.vxe-table td{border-color:#D0D0D0;border-style:solid;border-width:0}.vxe-table.is--print{width:100%}.border--default,.border--full,.border--outer{border-top-width:1px}.border--default,.border--full,.border--outer{border-left-width:1px}.border--outer,.border--default th,.border--default td,.border--full th,.border--full td,.border--outer th,.border--inner th,.border--inner td{border-bottom-width:1px}.border--default,.border--outer,.border--full th,.border--full td{border-right-width:1px}.border--default th,.border--full th,.border--outer th{background-color:#f8f8f9}.vxe-table td>div,.vxe-table th>div{padding:.5em .4em}.col--center{text-align:center}.col--right{text-align:right}.vxe-table:not(.is--print) .col--ellipsis>div{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;word-break:break-all}.vxe-table--tree-node{text-align:left}.vxe-table--tree-node-wrapper{position:relative}.vxe-table--tree-icon-wrapper{position:absolute;top:50%;width:1em;height:1em;text-align:center;-webkit-transform:translateY(-50%);transform:translateY(-50%);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer}.vxe-table--tree-unfold-icon,.vxe-table--tree-fold-icon{position:absolute;width:0;height:0;border-style:solid;border-width:.5em;border-right-color:transparent;border-bottom-color:transparent}.vxe-table--tree-unfold-icon{left:.3em;top:0;border-left-color:#939599;border-top-color:transparent}.vxe-table--tree-fold-icon{left:0;top:.3em;border-left-color:transparent;border-top-color:#939599}.vxe-table--tree-cell{display:block;padding-left:1.5em}.vxe-table input[type="checkbox"]{margin:0}.vxe-table input[type="checkbox"],.vxe-table input[type="radio"],.vxe-table input[type="checkbox"]+span,.vxe-table input[type="radio"]+span{vertical-align:middle;padding-left:0.4em}'),{getI18n:N,hooks:Je,renderer:et}=Dl.VxeUI,y,M="\r\n";function Wr(e){return!!e.field||-1===["seq","checkbox","radio"].indexOf(e.type||"")}let tt=e=>{let t=[];return e.forEach(e=>{e.childNodes&&e.childNodes.length?(t.push(e),t.push(...tt(e.childNodes))):t.push(e)}),t};function Gr(e){return"TRUE"===e||"true"===e||!0===e}function qr(r,e,t){let l=e.footerFilterMethod;return l?t.filter((e,t)=>l({$table:r,items:e,$rowIndex:t})):t}function Xr(e){return/[",\s\n]/.test(e)?`"${e.replace(/"/g,'""')}"`:e}function kt(e,t){return e.getElementsByTagName(t)}function Kr(e){return`#${e}@`+Il().uniqueId()}function Yr(e,t){return e.replace(/#\d+@\d+/g,e=>Il().hasOwnProp(t,e)?t[e]:e)}function Zr(e,t){return Yr(e,t).replace(/^"+$/g,e=>'"'.repeat(Math.ceil(e.length/2)))}function Qr(e,t){var{fieldMaps:e,titleMaps:r}=e;return e[t]||(e=r[t])&&e.field&&(t=e.field),t}function Jr(t,e,a){e=e.split(M);let n=[],i=[];if(e.length){let l={},o=Date.now();e.forEach(e=>{if(e){let r={};e=(e=e.replace(/("")|(\n)/g,(e,t)=>{var r=Kr(o);return l[r]=t?'"':"\n",r}).replace(/"(.*?)"/g,(e,t)=>{var r=Kr(o);return l[r]=Yr(t,l),r})).split(a);i.length?(e.forEach((e,t)=>{t<i.length&&(r[i[t]]=Zr(e.trim(),l))}),n.push(r)):i=e.map(e=>Qr(t,Zr(e.trim(),l)))}})}return{fields:i,rows:n}}function el(e){Il().eachTree(e,e=>{delete e._level,delete e._colSpan,delete e._rowSpan,delete e._children,delete e.childNodes},{children:"children"})}let rt=["exportData","importByFile","importData","saveFile","readFile","print","getPrintHtml","openImport","closeImport","openExport","closeExport","openPrint","closePrint"],lt=(Je.add("tableExportModule",{setupTable(k){let{props:O,reactData:A,internalData:L}=k,{computeTreeOpts:V,computePrintOpts:n,computeExportOpts:$,computeImportOpts:c,computeCustomOpts:S,computeSeqOpts:i,computeRadioOpts:l,computeCheckboxOpts:o,computeColumnOpts:s}=k.getComputeMaps(),_=(0,Al.inject)("$xeGrid",null),v=e=>{var t=V.value,t=t.children||t.childrenField;return e[t]&&e[t].length},f=(e,t,r,l,o)=>{var a=i.value.seqMethod||l.seqMethod;return a?a({$table:k,row:t,rowIndex:k.getRowIndex(t),$rowIndex:r,column:l,columnIndex:k.getColumnIndex(l),$columnIndex:o}):e};function D(e,t){var r=s.value,r=t.headerExportMethod||r.headerExportMethod;return r?r({column:t,options:e,$table:k}):(e.isTitle?t.getTitle():t.field)||""}let x=e=>Il().isBoolean(e)?e?"TRUE":"FALSE":e,b=e=>Ol(e)?"":""+e,d=(c,u,e)=>{let{isAllExpand:n,mode:p}=c;var t=O.treeConfig;let h=l.value,g=o.value;var r=V.value;let m=s.value;if(y=y||document.createElement("div"),t){t=r.children||r.childrenField;let o=[],a=new Map;return Il().eachTree(e,(e,i,t,s,r,l)=>{let d=e._row||e;e=r&&r._row?r._row:r;if(n||!e||a.has(e)&&k.isTreeExpandByRow(e)){r=v(d);let n={_row:d,_level:l.length-1,_hasChild:r,_expand:r&&k.isTreeExpandByRow(d)};u.forEach((e,t)=>{let r="";var l=e.editRender||e.cellRender;let o=e.exportMethod||m.exportMethod;if(o=(o=!o&&l&&l.name&&(l=et.get(l.name))?l.tableExportMethod||l.exportMethod:o)||m.exportMethod)r=o({$table:k,row:d,column:e,options:c});else switch(e.type){case"seq":var a=s.map((e,t)=>t%2==0?Number(e)+1:".").join("");r="all"===p?a:f(a,d,i,e,t);break;case"checkbox":r=x(k.isCheckedByCheckboxRow(d)),n._checkboxLabel=g.labelField?Il().get(d,g.labelField):"",n._checkboxDisabled=g.checkMethod&&!g.checkMethod({$table:k,row:d});break;case"radio":r=x(k.isCheckedByRadioRow(d)),n._radioLabel=h.labelField?Il().get(d,h.labelField):"",n._radioDisabled=h.checkMethod&&!h.checkMethod({$table:k,row:d});break;default:c.original?r=jl(d,e):(r=k.getCellLabel(d,e),"html"===e.type?(y.innerHTML=r,r=y.innerText.trim()):(a=k.getCellElement(d,e))&&!Ll(a,"is--progress")&&(r=a.innerText.trim()))}n[e.id]=b(r)}),a.set(d,1),o.push(Object.assign(n,d))}},{children:t}),o}return e.map((n,i)=>{let s={_row:n};return u.forEach((e,t)=>{let r="";var l=e.editRender||e.cellRender;let o=e.exportMethod||m.exportMethod;if(o=!o&&l&&l.name&&(l=et.get(l.name))?l.tableExportMethod||l.exportMethod:o)r=o({$table:k,row:n,column:e,options:c});else switch(e.type){case"seq":var a=i+1;r="all"===p?a:f(a,n,i,e,t);break;case"checkbox":r=x(k.isCheckedByCheckboxRow(n)),s._checkboxLabel=g.labelField?Il().get(n,g.labelField):"",s._checkboxDisabled=g.checkMethod&&!g.checkMethod({$table:k,row:n});break;case"radio":r=x(k.isCheckedByRadioRow(n)),s._radioLabel=h.labelField?Il().get(n,h.labelField):"",s._radioDisabled=h.checkMethod&&!h.checkMethod({$table:k,row:n});break;default:c.original?r=jl(n,e):(r=k.getCellLabel(n,e),"html"===e.type?(y.innerHTML=r,r=y.innerText.trim()):(a=k.getCellElement(n,e))&&!Ll(a,"is--progress")&&(r=a.innerText.trim()))}s[e.id]=b(r)}),s})},I=(e,t,r)=>{var l=s.value,o=r.editRender||r.cellRender;let a=r.footerExportMethod;a=(a=!a&&o&&o.name&&(o=et.get(o.name))?o.tableFooterExportMethod||o.footerExportMethod:a)||l.footerExportMethod;o=k.getVTColumnIndex(r);return a?a({$table:k,items:t,itemIndex:o,row:t,_columnIndex:o,column:r,options:e}):Il().isArray(t)?Il().toValueString(t[o]):Il().get(t,r.field)},u=(e,r,l,t)=>{let o="\ufeff";return r.isHeader&&(o+=l.map(e=>Xr(D(r,e))).join(",")+M),t.forEach(t=>{o+=l.map(e=>Xr(((e,t)=>{if(t){if("seq"===e.type)return`	`+t;switch(e.cellType){case"string":if(isNaN(t))break;return`	`+t;case"number":break;default:if(12<=t.length&&!isNaN(t))return`	`+t}}return t})(e,t[e.id]))).join(",")+M}),r.isFooter&&(t=A.footerTableData,qr(e,r,t).forEach(t=>{o+=l.map(e=>Xr(I(r,t,e))).join(",")+M})),o},p=(e,r,l,t)=>{let o="";return r.isHeader&&(o+=l.map(e=>Xr(D(r,e))).join("\t")+M),t.forEach(t=>{o+=l.map(e=>Xr(t[e.id])).join("\t")+M}),r.isFooter&&(t=A.footerTableData,qr(e,r,t).forEach(t=>{o+=l.map(e=>Xr(I(r,t,e))).join("\t")+M})),o},F=(e,t,r)=>{e=e[t],t=Il().isUndefined(e)||Il().isNull(e)?r:e;let l="title"===t||(!0===t||"tooltip"===t)||"ellipsis"===t;var{scrollXLoad:r,scrollYLoad:e}=A;return l=r||e?l||!0:l},h=(i,e,t)=>{let{id:d,border:r,treeConfig:l,headerAlign:s,align:c,footerAlign:a,showOverflow:u,showHeaderOverflow:p}=O,{isAllSelected:h,isIndeterminate:o}=A,g=L.mergeBodyCellMaps,n=V.value,{print:m,isHeader:v,isFooter:f,isColgroup:x,isMerge:b,colgroups:C,original:w}=i,y="check-all";let T=[`<table class="${["vxe-table","border--"+(!0===(R=r)?"full":R||"default"),m?"is--print":"",v?"is--header":""].filter(e=>e).join(" ")}" border="0" cellspacing="0" cellpadding="0">`,`<colgroup>${e.map(e=>`<col style="width:${e.renderWidth}px">`).join("")}</colgroup>`];v&&(T.push("<thead>"),x&&!w?C.forEach(e=>{T.push(`<tr>${e.map(t=>{var e=t.headerAlign||t.align||s||c,r=F(t,"showHeaderOverflow",p)?["col--ellipsis"]:[],l=D(i,t);let o=0,a=0;Il().eachTree([t],e=>{e.childNodes&&t.childNodes.length||a++,o+=e.renderWidth},{children:"childNodes"});var n=o-a;return e&&r.push("col--"+e),"checkbox"===t.type?`<th class="${r.join(" ")}" colspan="${t._colSpan}" rowspan="${t._rowSpan}"><div ${m?"":`style="width: ${n}px"`}><input type="checkbox" class="${y}" ${h?"checked":""}><span>${l}</span></div></th>`:`<th class="${r.join(" ")}" colspan="${t._colSpan}" rowspan="${t._rowSpan}" title="${l}"><div ${m?"":`style="width: ${n}px"`}><span>${kl(l,!0)}</span></div></th>`}).join("")}</tr>`)}):T.push(`<tr>${e.map(e=>{var t=e.headerAlign||e.align||s||c,r=F(e,"showHeaderOverflow",p)?["col--ellipsis"]:[],l=D(i,e);return t&&r.push("col--"+t),"checkbox"===e.type?`<th class="${r.join(" ")}"><div ${m?"":`style="width: ${e.renderWidth}px"`}><input type="checkbox" class="${y}" ${h?"checked":""}><span>${l}</span></div></th>`:`<th class="${r.join(" ")}" title="${l}"><div ${m?"":`style="width: ${e.renderWidth}px"`}><span>${kl(l,!0)}</span></div></th>`}).join("")}</tr>`),T.push("</thead>")),t.length&&(T.push("<tbody>"),l?t.forEach(o=>{T.push("<tr>"+e.map(t=>{var r=t.id,e=t.align||c,l=F(t,"showOverflow",u)?["col--ellipsis"]:[],r=o[r];if(e&&l.push("col--"+e),t.treeNode){let e="";return o._hasChild&&(e=`<i class="${o._expand?"vxe-table--tree-fold-icon":"vxe-table--tree-unfold-icon"}"></i>`),l.push("vxe-table--tree-node"),"radio"===t.type?`<td class="${l.join(" ")}" title="${r}"><div ${m?"":`style="width: ${t.renderWidth}px"`}><div class="vxe-table--tree-node-wrapper" style="padding-left: ${o._level*n.indent}px"><div class="vxe-table--tree-icon-wrapper">${e}</div><div class="vxe-table--tree-cell"><input type="radio" name="radio_${d}" ${o._radioDisabled?"disabled ":""}${Gr(r)?"checked":""}><span>${o._radioLabel}</span></div></div></div></td>`:"checkbox"===t.type?`<td class="${l.join(" ")}" title="${r}"><div ${m?"":`style="width: ${t.renderWidth}px"`}><div class="vxe-table--tree-node-wrapper" style="padding-left: ${o._level*n.indent}px"><div class="vxe-table--tree-icon-wrapper">${e}</div><div class="vxe-table--tree-cell"><input type="checkbox" ${o._checkboxDisabled?"disabled ":""}${Gr(r)?"checked":""}><span>${o._checkboxLabel}</span></div></div></div></td>`:`<td class="${l.join(" ")}" title="${r}"><div ${m?"":`style="width: ${t.renderWidth}px"`}><div class="vxe-table--tree-node-wrapper" style="padding-left: ${o._level*n.indent}px"><div class="vxe-table--tree-icon-wrapper">${e}</div><div class="vxe-table--tree-cell">${r}</div></div></div></td>`}return"radio"===t.type?`<td class="${l.join(" ")}"><div ${m?"":`style="width: ${t.renderWidth}px"`}><input type="radio" name="radio_${d}" ${o._radioDisabled?"disabled ":""}${Gr(r)?"checked":""}><span>${o._radioLabel}</span></div></td>`:"checkbox"===t.type?`<td class="${l.join(" ")}"><div ${m?"":`style="width: ${t.renderWidth}px"`}><input type="checkbox" ${o._checkboxDisabled?"disabled ":""}${Gr(r)?"checked":""}><span>${o._checkboxLabel}</span></div></td>`:`<td class="${l.join(" ")}" title="${r}"><div ${m?"":`style="width: ${t.renderWidth}px"`}>${kl(r,!0)}</div></td>`}).join("")+"</tr>")}):t.forEach(s=>{T.push("<tr>"+e.map(e=>{var t=e.align||c,r=F(e,"showOverflow",u)?["col--ellipsis"]:[],l=s[e.id];let o=1,a=1;if(b){var n=k.getVTRowIndex(s._row),i=k.getVTColumnIndex(e),n=g[n+":"+i];if(n){var{rowspan:i,colspan:n}=n;if(!i||!n)return"";1<i&&(o=i),1<n&&(a=n)}}return t&&r.push("col--"+t),"radio"===e.type?`<td class="${r.join(" ")}" rowspan="${o}" colspan="${a}"><div ${m?"":`style="width: ${e.renderWidth}px"`}><input type="radio" name="radio_${d}" ${s._radioDisabled?"disabled ":""}${Gr(l)?"checked":""}><span>${s._radioLabel}</span></div></td>`:"checkbox"===e.type?`<td class="${r.join(" ")}" rowspan="${o}" colspan="${a}"><div ${m?"":`style="width: ${e.renderWidth}px"`}><input type="checkbox" ${s._checkboxDisabled?"disabled ":""}${Gr(l)?"checked":""}><span>${s._checkboxLabel}</span></div></td>`:`<td class="${r.join(" ")}" rowspan="${o}" colspan="${a}" title="${l}"><div ${m?"":`style="width: ${e.renderWidth}px"`}>${kl(l,!0)}</div></td>`}).join("")+"</tr>")}),T.push("</tbody>")),f&&(R=A.footerTableData,(t=qr(k,i,R)).length)&&(T.push("<tfoot>"),t.forEach(o=>{T.push(`<tr>${e.map(e=>{var t=e.footerAlign||e.align||a||c,r=F(e,"showOverflow",u)?["col--ellipsis"]:[],l=I(i,o,e);return t&&r.push("col--"+t),`<td class="${r.join(" ")}" title="${l}"><div ${m?"":`style="width: ${e.renderWidth}px"`}>${kl(l,!0)}</div></td>`}).join("")}</tr>`)}),T.push("</tfoot>"));var E,R=!h&&o?`<script>(function(){var a=document.querySelector(".${y}");if(a){a.indeterminate=true}})()</script>`:"";return T.push("</table>",R),(m?T:(t=i,R=T.join(""),E=t.style,["<!DOCTYPE html><html>","<head>",'<meta charset="utf-8"><meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,minimal-ui">',`<title>${t.sheetName}</title>`,'<style media="print">.vxe-page-break-before{page-break-before:always;}.vxe-page-break-after{page-break-after:always;}</style>',`<style>${Qe}</style>`,E?`<style>${E}</style>`:"","</head>",`<body>${R}</body>`,"</html>"])).join("")},g=(r,e,t)=>{let l=['<?xml version="1.0"?>','<?mso-application progid="Excel.Sheet"?>','<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40">','<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">',"<Version>16.00</Version>","</DocumentProperties>",'<ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">',"<WindowHeight>7920</WindowHeight>","<WindowWidth>21570</WindowWidth>","<WindowTopX>32767</WindowTopX>","<WindowTopY>32767</WindowTopY>","<ProtectStructure>False</ProtectStructure>","<ProtectWindows>False</ProtectWindows>","</ExcelWorkbook>",`<Worksheet ss:Name="${r.sheetName}">`,"<Table>",e.map(e=>`<Column ss:Width="${e.renderWidth}"/>`).join("")].join("");return r.isHeader&&(l+=`<Row>${e.map(e=>`<Cell><Data ss:Type="String">${D(r,e)}</Data></Cell>`).join("")}</Row>`),t.forEach(t=>{l+="<Row>"+e.map(e=>`<Cell><Data ss:Type="String">${t[e.id]}</Data></Cell>`).join("")+"</Row>"}),r.isFooter&&(t=A.footerTableData,qr(k,r,t).forEach(t=>{l+=`<Row>${e.map(e=>`<Cell><Data ss:Type="String">${I(r,t,e)}</Data></Cell>`).join("")}</Row>`})),l+"</Table></Worksheet></Workbook>"},m=(e,t)=>{var{filename:r,type:l,download:o}=e;if(!o)return o=new Blob([t],{type:`text/${e.type};charset=utf-8;`}),Promise.resolve({type:l,content:t,blob:o});Dl.VxeUI.saveFile&&Dl.VxeUI.saveFile({filename:r,type:l,content:t}).then(()=>{!1!==e.message&&Dl.VxeUI.modal&&Dl.VxeUI.modal.message({content:N("vxe.table.expSuccess"),status:"success"})})},H=r=>{let{remote:l,columns:o,colgroups:a,exportMethod:n,afterExportMethod:t}=r;return new Promise(t=>{if(l){var e={options:r,$table:k,$grid:_};t(n?n(e):e)}else{let e=(e=>{let{columns:t,dataFilterMethod:r}=e,l=e.data;return r&&(l=l.filter((e,t)=>r({$table:k,row:e,$rowIndex:t}))),d(e,t,l)})(r);t(k.preventEvent(null,"event.export",{options:r,columns:o,colgroups:a,datas:e},()=>m(r,((e,t,r,l)=>{if(r.length)switch(t.type){case"csv":return u(e,t,r,l);case"txt":return p(e,t,r,l);case"html":return h(t,r,l);case"xml":return g(t,r,l)}return""})(k,r,o,e))))}}).then(e=>(el(o),r.print||t&&t({status:!0,options:r,$table:k,$grid:_}),Object.assign({status:!0},e))).catch(()=>{el(o),r.print||t&&t({status:!1,options:r,$table:k,$grid:_});return Promise.reject({status:!1})})},C=(e,r)=>{let{tableFullColumn:t,_importResolve:l,_importReject:o}=L,a={fields:[],rows:[]},n={},i={};t.forEach(e=>{var t=e.field,r=e.getTitle();t&&(n[t]=e),r&&(i[e.getTitle()]=e)});var s={fieldMaps:n,titleMaps:i};switch(r.type){case"csv":a=Jr(s,e,",");break;case"txt":a=Jr(s,e,"\t");break;case"html":a=((t,e)=>{var r,e=kt((new DOMParser).parseFromString(e,"text/html"),"body");let l=[],o=[];return e.length&&(e=kt(e[0],"table")).length&&(r=kt(e[0],"thead")).length&&(Il().arrayEach(kt(r[0],"tr"),e=>{Il().arrayEach(kt(e,"th"),e=>{o.push(Qr(t,e.textContent||""))})}),(r=kt(e[0],"tbody")).length)&&Il().arrayEach(kt(r[0],"tr"),e=>{let r={};Il().arrayEach(kt(e,"td"),(e,t)=>{o[t]&&(r[o[t]]=e.textContent||"")}),l.push(r)}),{fields:o,rows:l}})(s,e);break;case"xml":a=((t,e)=>{var e=kt((new DOMParser).parseFromString(e,"application/xml"),"Worksheet");let l=[],o=[];return e.length&&(e=kt(e[0],"Table")).length&&(e=kt(e[0],"Row")).length&&(Il().arrayEach(kt(e[0],"Cell"),e=>{o.push(Qr(t,e.textContent||""))}),Il().arrayEach(e,(e,t)=>{if(t){let r={};t=kt(e,"Cell");Il().arrayEach(t,(e,t)=>{o[t]&&(r[o[t]]=e.textContent)}),l.push(r)}})),{fields:o,rows:l}})(s,e)}let{fields:d,rows:c}=a;d.some(e=>n[e]||i[e])?k.createData(c).then(e=>{let t;return"insert"!==r.mode&&"insertBottom"!==r.mode||(t=k.insertAt(e,-1)),t="insertTop"===r.mode?k.insert(e):k.reloadData(e),!1!==r.message&&Dl.VxeUI.modal&&Dl.VxeUI.modal.message({content:N("vxe.table.impSuccess",[c.length]),status:"success"}),t.then(()=>{l&&l({status:!0})})}):!1!==r.message&&(Dl.VxeUI.modal&&Dl.VxeUI.modal.message({content:N("vxe.error.impFields"),status:"error"}),o)&&o({status:!1})},a=(a,n)=>{let{importMethod:i,afterImportMethod:t}=n,{type:s,filename:d}=hr(a);var e=c.value;return i||Il().includes(Il().keys(e._typeMaps),s)?new Promise((t,r)=>{let e=e=>{t(e),L._importResolve=null,L._importReject=null},l=e=>{r(e),L._importResolve=null,L._importReject=null};if(L._importResolve=e,L._importReject=l,window.FileReader){let t=Object.assign({mode:"insertTop"},n,{type:s,filename:d});var o;t.remote?i?Promise.resolve(i({file:a,options:t,$table:k})).then(()=>{e({status:!0})}).catch(()=>{e({status:!0})}):e({status:!0}):(o=L.tableFullColumn,k.preventEvent(null,"event.import",{file:a,options:t,columns:o},()=>{var e=new FileReader;e.onerror=()=>{pl("vxe.error.notType",[s]),l({status:!1})},e.onload=e=>{C(e.target.result,t)},e.readAsText(a,t.encoding||"UTF-8")}))}else pl("vxe.error.notExp"),e({status:!0})}).then(()=>{t&&t({status:!0,options:n,$table:k})}).catch(e=>(t&&t({status:!1,options:n,$table:k}),Promise.reject(e))):(!1!==n.message&&Dl.VxeUI.modal&&Dl.VxeUI.modal.message({content:N("vxe.error.notType",[s]),status:"error"}),Promise.reject({status:!1}))},B=(e,l,t)=>t.some(e=>{var t,r;return Ir(e)?l.id===e.id:Il().isString(e)?l.field===e:(t=e.id||e.colId,r=e.type,e=e.field,t?l.id===t:e&&r?l.field===e&&l.type===r:e?l.field===e:!!r&&l.type===r)}),P=(e,t,r,l)=>(!l||!Il().includes(l,t.field))&&(r?!!Il().includes(r,t.field):e.original?!!t.field:Wr(t)),r=(e,t)=>{var{treeConfig:r,showHeader:l,showFooter:o}=O,{initStore:a,isGroup:n,footerTableData:i,exportStore:s,exportParams:d}=A,{collectColumn:c,mergeBodyList:u,mergeFooterList:p}=L,h=$.value,g=S.value,m=k.getCheckboxRecords(),v=_?_.getComputeMaps().computeProxyOpts.value:{},i=!!i.length,u=!(!u.length&&!p.length);let f=Object.assign({message:!0,isHeader:l,isTitle:l,isFooter:o,isColgroup:n,isMerge:u,useStyle:!0,current:"current",modes:(v.ajax&&v.ajax.queryAll?["all"]:[]).concat(["current","selected","empty"])},e);p=f.types||Il().keys(h._typeMaps),l=f.modes||[];let x=g.checkMethod;o=c.slice(0);let{columns:b,excludeFields:C,includeFields:w}=f;v=p.map(e=>({value:e,label:N("vxe.export.types."+e)})),e=l.map(e=>e&&e.value?{value:e.value,label:e.label||e.value}:{value:e,label:N("vxe.export.modes."+e)});Il().eachTree(o,(e,t,r,l,o)=>{var a=e.children&&0<e.children.length;let n=!1;n=b&&b.length?B(f,e,b):C||w?P(f,e,w,C):e.visible&&(a||Wr(e)),e.checked=n,e.halfChecked=!1,e.disabled=o&&o.disabled||!!x&&!x({$table:k,column:e})}),Object.assign(s,{columns:o,typeList:v,modeList:e,hasFooter:i,hasMerge:u,hasTree:r,isPrint:t,hasColgroup:n,visible:!0}),Object.assign(d,{mode:m.length?"selected":"current"},f);let{filename:y,sheetName:T,mode:E,type:R}=d;return y&&(Il().isFunction(y)?d.filename=y({options:f,$table:k,$grid:_}):d.filename=""+y),T&&(Il().isFunction(T)?d.sheetName=T({options:f,$table:k,$grid:_}):d.sheetName=""+T),e.some(e=>e.value===E)||(d.mode=e[0].value),v.some(e=>e.value===R)||(d.type=v[0].value),a.export=!0,(0,Al.nextTick)()};var e=()=>Dl.VxeUI.modal?Dl.VxeUI.modal.close("VXE_EXPORT_MODAL"):Promise.resolve();let w={exportData(i){var{treeConfig:e,showHeader:s,showFooter:d}=O,t=A.isGroup;let{tableFullColumn:a,afterFullData:r,collectColumn:l,mergeBodyList:o,mergeFooterList:n}=L;var c=$.value,u=V.value;let p=_?_.getComputeMaps().computeProxyOpts.value:{};var h=!(!o.length&&!n.length);let g=Object.assign({message:!0,isHeader:s,isTitle:s,isFooter:d,isColgroup:t,isMerge:h,useStyle:!0,current:"current",modes:(p.ajax&&p.ajax.queryAll?["all"]:[]).concat(["current","selected","empty"]),download:!0,type:"csv"},c,i),{filename:m,sheetName:v,type:f,mode:x,columns:b,original:C,columnFilterMethod:w,beforeExportMethod:y,includeFields:T,excludeFields:E}=g,R=[],S=k.getCheckboxRecords(),D=(x=x||(S.length?"selected":"current"),!1),I=[],F=(I=b&&b.length?(D=!0,b):Il().searchTree(l,e=>{var t=e.children&&0<e.children.length;let r=!1;return r=b&&b.length?B(g,e,b):E||T?P(g,e,T,E):e.visible&&(t||Wr(e))},{children:"children",mapChildren:"childNodes",original:!0}),Object.assign({},g,{filename:"",sheetName:""})),M=(D||w||(w=({column:e})=>(!E||!Il().includes(E,e.field))&&(T?!!Il().includes(T,e.field):C?!!e.field:Wr(e)),F.columnFilterMethod=w),R=I?(F._isCustomColumn=!0,Il().searchTree(Il().mapTree(I,e=>{let l;if(e){if(Ir(e))l=e;else if(Il().isString(e))l=k.getColumnByField(e);else{var o=e.id||e.colId;let t=e.type,r=e.field;o?l=k.getColumnById(o):r&&t?l=a.find(e=>e.field===r&&e.type===t):r?l=k.getColumnByField(r):t&&(l=a.find(e=>e.type===t))}return l||{}}},{children:"childNodes",mapChildren:"_children"}),(e,t)=>Ir(e)&&(!w||w({$table:k,column:e,$columnIndex:t})),{children:"_children",mapChildren:"childNodes",original:!0})):Il().searchTree(t?l:a,(e,t)=>e.visible&&(!w||w({$table:k,column:e,$columnIndex:t})),{children:"children",mapChildren:"childNodes",original:!0}),[]);if(Il().eachTree(R,e=>{e.children&&e.children.length||M.push(e)},{children:"childNodes"}),F.columns=M,F.colgroups=(e=>{let t=1,l=(r,e)=>{if(e&&(r._level=e._level+1,t<r._level)&&(t=r._level),r.childNodes&&r.childNodes.length){let t=0;r.childNodes.forEach(e=>{l(e,r),t+=e._colSpan}),r._colSpan=t}else r._colSpan=1},r=(e.forEach(e=>{e._level=1,l(e)}),[]);for(let e=0;e<t;e++)r.push([]);return tt(e).forEach(e=>{e.childNodes&&e.childNodes.length?e._rowSpan=1:e._rowSpan=t-e._level+1,r[e._level-1].push(e)}),r})(R),m&&(Il().isFunction(m)?F.filename=m({options:g,$table:k,$grid:_}):F.filename=""+m),F.filename||(F.filename=N(F.original?"vxe.table.expOriginFilename":"vxe.table.expFilename",[Il().toDateString(Date.now(),"yyyyMMddHHmmss")])),v&&(Il().isFunction(v)?F.sheetName=v({options:g,$table:k,$grid:_}):F.sheetName=""+v),F.sheetName||(F.sheetName=document.title||""),!F.exportMethod&&!Il().includes(Il().keys(c._typeMaps),f))return pl("vxe.error.notType",[f]),["xlsx","pdf"].includes(f)&&ul("vxe.error.reqPlugin",[4,"plugin-export-xlsx"]),Promise.reject({status:!1});if(F.print||y&&y({options:F,$table:k,$grid:_}),!F.data){if(F.data=[],"selected"===x)-1<["html","pdf"].indexOf(f)&&e?F.data=Il().searchTree(k.getTableData().fullData,e=>-1<k.findRowIndexOf(S,e),Object.assign({},u,{data:"_row"})):F.data=S;else if("all"===x&&(_||pl("vxe.error.errProp",["all","mode=current,selected"]),_)&&!F.remote){s=_.reactData,d=_.getComputeMaps().computeProxyOpts;let e=d.value;h=s.sortData;let{beforeQueryAll:t,afterQueryAll:l,ajax:r={}}=e,o=e.response||e.props||{};i=r.queryAll;let a=r.queryAllSuccess,n=r.queryAllError;if(i||pl("vxe.error.notFunc",["proxy-config.ajax.queryAll"]),i){let r={$table:k,$grid:_,sort:h.length?h[0]:{},sorts:h,filters:s.filterData,form:s.formData,options:F};return Promise.resolve((t||i)(r)).then(e=>{var t=o.list;return F.data=(t?Il().isFunction(t)?t({data:e,$grid:_}):Il().get(e,t):e)||[],l&&l(r),a&&a({...r,response:e}),H(F)}).catch(e=>{n&&n({...r,response:e})})}}"current"===x&&(F.data=r)}return H(F)},importByFile(e,t){var t=Object.assign({},t),r=t.beforeImportMethod;return r&&r({options:t,$table:k}),a(e,t)},importData(e){var t=c.value;let r=Object.assign({types:Il().keys(t._typeMaps)},t,e),{beforeImportMethod:l,afterImportMethod:o}=r;return l&&l({options:r,$table:k}),Dl.VxeUI.readFile(r).catch(e=>(o&&o({status:!1,options:r,$table:k}),Promise.reject(e))).then(e=>{e=e.file;return a(e,r)})},saveFile(e){return Dl.VxeUI.saveFile(e)},readFile(e){return Dl.VxeUI.readFile(e)},print(e){var t=n.value;let r=Object.assign({original:!1},t,e,{type:"html",download:!1,remote:!1,print:!0});t=r.sheetName;let l="",o=(l=(l=t?Il().isFunction(t)?t({options:r,$table:k,$grid:_}):""+t:l)||document.title||"",r.beforePrintMethod),a=r.html||r.content;return new Promise((e,t)=>{Dl.VxeUI.print?a?e(Dl.VxeUI.print({title:l,html:a,customStyle:r.style,beforeMethod:o?({html:e})=>o({html:e,content:e,options:r,$table:k}):void 0})):e(w.exportData(r).then(({content:e})=>Dl.VxeUI.print({title:l,html:e,customStyle:r.style,beforeMethod:o?({html:e})=>o({html:e,content:e,options:r,$table:k}):void 0}))):t({status:!1})})},getPrintHtml(e){var t=n.value,t=Object.assign({original:!1},t,e,{type:"html",download:!1,remote:!1,print:!0});return k.exportData(t).then(({content:e})=>({html:e}))},closeImport(){return Dl.VxeUI.modal?Dl.VxeUI.modal.close("VXE_IMPORT_MODAL"):Promise.resolve()},openImport(e){var{treeConfig:t,importConfig:r}=O;let{initStore:l,importStore:o,importParams:a}=A;var n=c.value,n=Object.assign({mode:"insertTop",message:!0,types:Il().keys(n._typeMaps),modes:["insertTop","covering"]},n,e),e=n.types||[],i=n.modes||[];!t?(r||pl("vxe.error.reqProp",["import-config"]),t=e.map(e=>({value:e,label:N("vxe.export.types."+e)})),r=i.map(e=>e&&e.value?{value:e.value,label:e.label||e.value}:{value:e,label:N("vxe.import.modes."+e)}),Object.assign(o,{file:null,type:"",filename:"",modeList:r,typeList:t,visible:!0}),Object.assign(a,n),r.some(e=>e.value===a.mode)||(a.mode=r[0].value),l.import=!0):n.message&&Dl.VxeUI.modal&&Dl.VxeUI.modal.message({content:N("vxe.error.treeNotImp"),status:"error"})},closeExport:e,openExport(e){var t=$.value,t=Object.assign({message:!0,types:Il().keys(t._typeMaps)},t,e);return O.exportConfig||pl("vxe.error.reqProp",["export-config"]),r(t)},closePrint:e,openPrint(e){var t=n.value,t=Object.assign({message:!0},t,e);return O.printConfig||pl("vxe.error.reqProp",["print-config"]),r(t,!0)}};return w},setupGrid(e){return e.extendTableMethods(rt)}}),gr=Dl.VxeUI.hooks,Il().browse()),{getConfig:ot,validators:at,hooks:nt}=(gr.add("tableKeyboardModule",{setupTable(I){let{props:F,reactData:M,internalData:k}=I,O=I.getRefMaps().refElem,{computeEditOpts:v,computeCheckboxOpts:s,computeMouseOpts:d,computeTreeOpts:c,computeRowOpts:A,computeColumnOpts:f,computeCellOpts:L,computeDefaultRowHeight:V,computeCurrentRowOpts:x,computeCurrentColumnOpts:b}=I.getComputeMaps();let u=(e,D)=>{var t=k.elemStore,r=Wl(t["main-body-scroll"]),l=Wl(t["left-body-scroll"]),t=Wl(t["right-body-scroll"]),{column:o,cell:a}=D;if("checkbox"===o.type){let S=r;if(l&&"left"===o.fixed?S=l:t&&"right"===o.fixed&&(S=t),S){let t=O.value,i=e.clientX,s=e.clientY,d=S.querySelector(".vxe-table--checkbox-range"),c=a.parentElement,u=I.getCheckboxRecords(),p=[],h=1;r=((e,t)=>{let r=0,l=0;var o,a,n=!lt.firefox&&Ll(e,"vxe-checkbox--label");for(n&&(o=getComputedStyle(e),r-=Il().toNumber(o.paddingTop),l-=Il().toNumber(o.paddingLeft));e&&e!==t;)r+=e.offsetTop,l+=e.offsetLeft,e=e.offsetParent,n&&(a=getComputedStyle(e),r-=Il().toNumber(a.paddingTop),l-=Il().toNumber(a.paddingLeft));return{offsetTop:r,offsetLeft:l}})(e.target,S);let g=r.offsetTop+e.offsetY,m=r.offsetLeft+e.offsetX,v=S.scrollTop,n=c.offsetHeight,f=c.getBoundingClientRect(),x=s-f.y,b=null,C=!1,w=1,y=(e,t)=>{I.dispatchEvent("checkbox-range-"+e,{records:()=>I.getCheckboxRecords(),reserves:()=>I.getCheckboxReserveRecords()},t)},T=e=>{var{clientX:t,clientY:r}=e,t=t-i,r=r-s+(S.scrollTop-v);let l=Math.abs(r),o=Math.abs(t),a=g,n=m;r<h?(a+=r)<h&&(a=h,l=g):l=Math.min(l,S.scrollHeight-g-h),t<h?(n+=t,o>m&&(n=h,o=m)):o=Math.min(o,S.clientWidth-m-h),d.style.height=l+"px",d.style.width=o+"px",d.style.left=n+"px",d.style.top=a+"px",d.style.display="block";t=((e,t,r,l,o)=>{var a=F.showOverflow,{fullAllDataRowIdData:n,isResizeCellHeight:i}=k,s=A.value,d=L.value,c=V.value,e=e.row;let u=0,p=[],h=0;var g=0<o,m=M.scrollYLoad,v=k.afterFullData;if(h=g?l+o:r.height-l+Math.abs(o),m){r=I.getVTRowIndex(e);if(!(i||d.height||s.height)&&a)p=g?v.slice(r,r+Math.ceil(h/c)):v.slice(r-Math.floor(h/c),r+1);else if(g)for(let e=r;e<v.length;e++){var f=v[e],x=n[I.getRowid(f)]||{};if(u+=x.resizeHeight||d.height||s.height||x.height||c,p.push(f),u>h)return p}else for(let e=r;0<=e;e--){var b=v[e],C=n[I.getRowid(b)]||{};if(u+=C.resizeHeight||d.height||s.height||C.height||c,p.push(b),u>h)return p}}else for(var w=g?"next":"previous";t&&u<h;){var y=I.getRowNode(t);y&&(p.push(y.item),u+=t.offsetHeight,t=t[w+"ElementSibling"])}return p})(D,c,f,x,r<h?-l:l);10<l&&t.length!==p.length&&(r=eo(e),p=t,r?t.forEach(e=>{I.handleBatchSelectRows([e],-1===u.indexOf(e))}):(I.setAllCheckboxRow(!1),I.handleCheckedCheckboxRow(t,!0,!1)),y("change",e))},E=()=>{clearTimeout(b),b=null},R=a=>{E(),b=setTimeout(()=>{var e,t,r,l,o;b&&({scrollLeft:e,scrollTop:t,clientHeight:r,scrollHeight:l}=S,o=Math.ceil(50*w/n),C?t+r<l?(I.scrollTo(e,t+o),R(a),T(a)):E():t?(I.scrollTo(e,t-o),R(a),T(a)):E())},50)};$l(t,"drag--range"),document.onmousemove=e=>{e.preventDefault(),e.stopPropagation();var t=e.clientY,r=Cr(S).boundingTop;t<r?(C=!1,w=r-t,b||R(e)):t>r+S.clientHeight?(C=!0,w=t-r-S.clientHeight,b||R(e)):b&&E(),T(e)},document.onmouseup=e=>{E(),Vl(t,"drag--range"),d.removeAttribute("style"),document.onmousemove=null,document.onmouseup=null,y("end",e)},y("start",e)}}};let p=(e,t,r,l,o,a)=>{var{afterFullData:n,visibleColumn:i}=k;let s=Object.assign({},t);var t=I.getVTRowIndex(s.row),d=I.getVTColumnIndex(s.column);return e.preventDefault(),l&&0<t?(s.rowIndex=t-1,s.row=n[s.rowIndex]):a&&t<n.length-1?(s.rowIndex=t+1,s.row=n[s.rowIndex]):r&&d?(s.columnIndex=d-1,s.column=i[s.columnIndex]):o&&d<i.length-1&&(s.columnIndex=d+1,s.column=i[s.columnIndex]),I.scrollToRow(s.row,s.column).then(()=>{s.cell=I.getCellElement(s.row,s.column),I.handleSelected(s,e)}),s};return{moveTabSelected(e,t,r){var l=F.editConfig,{afterFullData:o,visibleColumn:a}=k,n=v.value,i=A.value,s=x.value,d=f.value,c=b.value;let u,p,h,g=Object.assign({},e);var e=I.getVTRowIndex(g.row),m=I.getVTColumnIndex(g.column),t=(r.preventDefault(),t?m<=0?0<e&&(p=e-1,u=o[p],h=a.length-1):h=m-1:m>=a.length-1?e<o.length-1&&(p=e+1,u=o[p],h=0):h=m+1,a[h]);t&&(u?(g.rowIndex=p,g.row=u):g.rowIndex=e,g.columnIndex=h,g.column=t,g.cell=I.getCellElement(g.row,g.column),i.isCurrent&&s.isFollowSelected&&I.triggerCurrentRowEvent(r,g),d.isCurrent&&c.isFollowSelected&&I.triggerCurrentColumnEvent(r,g),l?"click"!==n.trigger&&"dblclick"!==n.trigger||("row"===n.mode?I.handleEdit(g,r):I.scrollToRow(g.row,g.column).then(()=>{I.handleSelected(g,r)})):I.scrollToRow(g.row,g.column).then(()=>{I.handleSelected(g,r)}))},moveCurrentRow(e,t,r){var l=F.treeConfig;let o=M.currentRow;var a=k.afterFullData,n=c.value,n=n.children||n.childrenField;let i;if(o?l?({index:l,items:n}=Il().findTree(a,e=>e===o,{children:n}),e&&0<l?i=n[l-1]:t&&l<n.length-1&&(i=n[l+1])):(n=I.getVTRowIndex(o),e&&0<n?i=a[n-1]:t&&n<a.length-1&&(i=a[n+1])):i=a[0],i){r.preventDefault();let e={$table:I,row:i,rowIndex:I.getRowIndex(i),$rowIndex:I.getVMRowIndex(i)};I.scrollToRow(i).then(()=>I.triggerCurrentRowEvent(r,e))}},moveCurrentColumn(e,t,r){var l=M.currentColumn,o=k.visibleColumn;let a=null;if(l?(l=I.getVTColumnIndex(l),e&&0<l?a=o[l-1]:t&&l<o.length-1&&(a=o[l+1])):a=o[0],a){r.preventDefault();let e={$table:I,column:a,columnIndex:I.getColumnIndex(a),$columnIndex:I.getVMColumnIndex(a)};I.scrollToColumn(a).then(()=>I.triggerCurrentColumnEvent(r,e))}},moveArrowSelected(e,t,r,l,o,a){var{highlightCurrentRow:n,highlightCurrentColumn:i}=F,s=A.value,d=x.value,c=f.value,u=b.value,e=p(a,e,t,r,l,o);(s.isCurrent||n)&&(d.isFollowSelected?I.triggerCurrentRowEvent(a,e):(r||o)&&(s.isCurrent||n)&&I.moveCurrentRow(r,o,a)),(c.isCurrent||i)&&(u.isFollowSelected?I.triggerCurrentColumnEvent(a,e):(t||l)&&(c.isCurrent||i)&&I.moveCurrentColumn(t,l,a))},moveEnterSelected(e,t,r,l,o,a){var{highlightCurrentRow:n,highlightCurrentColumn:i}=F,s=A.value,d=x.value,c=f.value,u=b.value,e=p(a,e,t,r,l,o);(s.isCurrent||n)&&d.isFollowSelected&&I.triggerCurrentRowEvent(a,e),(c.isCurrent||i)&&u.isFollowSelected&&I.triggerCurrentColumnEvent(a,e)},moveSelected(e,t,r,l,o,a){p(a,e,t,r,l,o)},handleCellMousedownEvent:(e,t)=>{var{editConfig:r,checkboxConfig:l,mouseConfig:o}=F,a=s.value,n=d.value,i=v.value;if(o&&n.area&&I.triggerCellAreaMousedownEvent)return I.triggerCellAreaMousedownEvent(e,t);l&&a.range&&u(e,t),o&&n.selected&&(r&&"cell"!==i.mode||I.handleSelected(t,e))}}}}),Dl.VxeUI);class Jo{constructor(e){Object.assign(this,{$options:e,required:e.required,min:e.min,max:e.max,type:e.type,pattern:e.pattern,validator:e.validator,trigger:e.trigger,maxWidth:e.maxWidth})}get content(){return Ml(this.$options.content||this.$options.message)}get message(){return this.content}}function tl(e,t){return!(!Il().eqNull(e)&&t>Il().toNumber(e))}function rl(e,t){return!(!Il().eqNull(e)&&t<Il().toNumber(e))}function ll(e,t,r){var l,{type:e,min:o,max:a,pattern:n}=e,i="array"===e,s="number"===e,e="string"===e,d=""+t;if(l=d,!n||(Il().isRegExp(n)?n:new RegExp(n)).test(l)){if(i){if(!Il().isArray(t))return;if(r&&!t.length)return;if(!rl(o,t.length))return;if(!tl(a,t.length))return}else if(s){n=Number(t);if(isNaN(n))return;if(!rl(o,n))return;if(!tl(a,n))return}else{if(e&&!Il().isString(t))return;if(r&&!d)return;if(!rl(o,d.length))return;if(!tl(a,d.length))return}return 1}}let it=["fullValidate","validate","fullValidateField","validateField","clearValidate"],st=(nt.add("tableValidatorModule",{setupTable(f){let{props:x,reactData:b,internalData:C}=f,p=f.getRefMaps().refValidTooltip,{computeValidOpts:w,computeTreeOpts:y,computeEditOpts:l,computeAggregateOpts:T}=f.getComputeMaps(),E={},R={},S,r=(e,r,n,a)=>{let i={},{editRules:s,treeConfig:l}=x;var o=b.isRowGroupStatus;let{afterFullData:t,pendingRowMaps:d,removeRowMaps:c}=C;var u=y.value,p=T.value;let h=w.value,g,m=(!0===e?g=t:e&&(Il().isFunction(e)?n=e:g=Il().isArray(e)?e:[e]),g=g||(f.getInsertRecords?f.getInsertRecords().concat(f.getUpdateRecords()):[]),[]),v=(C._lastCallTime=Date.now(),S=!1,E.clearValidate(),{});if(s){let t=r&&r.length?r:f.getColumns();e=o=>{var e=Pl(f,o);if(!c[e]&&!d[e]&&!f.isAggregateRecord(o)&&(a||!S)){let e=[];t.forEach(r=>{let l=Il().isString(r)?r:r.field;!a&&S||!Il().has(s,l)||e.push(R.validCellRules("all",o,r).catch(({rule:e,rules:t})=>{t={rule:e,rules:t,rowIndex:f.getRowIndex(o),row:o,columnIndex:f.getColumnIndex(r),column:r,field:l,$table:f};if(i[l]||(i[l]=[]),v[Pl(f,o)+":"+r.id]={column:r,row:o,rule:e,content:e.content},i[l].push(t),!a)return S=!0,Promise.reject(t)}))}),m.push(Promise.all(e))}};return o?Il().eachTree(g,e,{children:p.mapChildrenField}):l?(r=u.children||u.childrenField,Il().eachTree(g,e,{children:r})):g.forEach(e),Promise.all(m).then(()=>{let e=Object.keys(i);var t,r,l;return b.validErrorMaps=(t=v,"single"===w.value.msgMode?(r={},(l=Object.keys(t)).length&&(r[l=l[0]]=t[l]),r):t),(0,Al.nextTick)().then(()=>{if(e.length)return Promise.reject(i[e[0]][0]);n&&n()})}).catch(a=>new Promise((e,t)=>{let r=()=>{(0,Al.nextTick)(()=>{n?(n(i),e()):("obsolete"===ot().validToReject?t:e)(i)})};var l,o=()=>{var e,t;a.cell=f.getCellElement(a.row,a.column),(e=a.cell)&&(e[te]?e[te]():e[re]&&e[re]()),t=a,new Promise(e=>{!1===w.value.autoPos?(f.dispatchEvent("valid-error",t,null),e()):f.handleEdit(t,{type:"valid-error",trigger:"call"}).then(()=>{e(R.showValidTooltip(t))})}).then(r)};!1===h.autoPos?r():(l=a.row,f.scrollToRow(l,a.column).then(o))}))}return b.validErrorMaps={},(0,Al.nextTick)().then(()=>{n&&n()})};return E={fullValidate(e,t){return Il().isFunction(t)&&ul("vxe.error.notValidators",["fullValidate(rows, callback)","fullValidate(rows)"]),r(e,null,t,!0)},validate(e,t){return r(e,null,t)},fullValidateField(e,t){t=(Il().isArray(t)?t:t?[t]:[]).map(e=>zl(f,e));return t.length?r(e,t,null,!0):(0,Al.nextTick)()},validateField(e,t){t=(Il().isArray(t)?t:t?[t]:[]).map(e=>zl(f,e));return t.length?r(e,t,null):(0,Al.nextTick)()},clearValidate(e,t){var l=b.validErrorMaps,r=p.value,o=w.value,e=Il().isArray(e)?e:e?[e]:[];let a=(Il().isArray(t)?t:t?[t]:[]).map(e=>zl(f,e)),n={};if(r&&r.reactData.visible&&r.close(),"single"===o.msgMode)b.validErrorMaps={};else{if(e.length&&a.length)n=Object.assign({},l),e.forEach(t=>{a.forEach(e=>{e=Pl(f,t)+":"+e.id;n[e]&&delete n[e]})});else if(e.length){let r=e.map(e=>""+Pl(f,e));Il().each(l,(e,t)=>{-1<r.indexOf(t.split(":")[0])&&(n[t]=e)})}else if(a.length){let r=a.map(e=>""+e.id);Il().each(l,(e,t)=>{-1<r.indexOf(t.split(":")[1])&&(n[t]=e)})}b.validErrorMaps=n}return(0,Al.nextTick)()}},R={validCellRules(e,s,d,t){let c=f.xeGrid;var r=x.editRules,l=d.field;let u=[],p=[];if(l&&r){let i=Il().get(r,l);if(i){let n=Il().isUndefined(t)?Il().get(s,l):t;i.forEach(t=>{let{trigger:r,validator:l}=t;if("all"===e||!r||e===r)if(l){var o,a={cellValue:n,rule:t,rules:i,row:s,rowIndex:f.getRowIndex(s),column:d,columnIndex:f.getColumnIndex(d),field:d.field,$table:f,$grid:c};let e;Il().isString(l)?(o=at.get(l))&&(o=o.tableCellValidatorMethod||o.cellValidatorMethod)?e=o(a):pl("vxe.error.notValidators",[l]):e=l(a),e&&(Il().isError(e)?(S=!0,u.push(new Jo({type:"custom",trigger:r,content:e.message,rule:new Jo(t)}))):e.catch&&p.push(e.catch(e=>{S=!0,u.push(new Jo({type:"custom",trigger:r,content:e&&e.message?e.message:t.content||t.message,rule:new Jo(t)}))})))}else((e,t)=>{var r=e.required,l=Il().isArray(t)?!t.length:Ol(t);if(r){if(l)return;if(!ll(e,t,r))return}else if(!l&&!ll(e,t,r))return;return 1})(t,n)||(S=!0,u.push(new Jo(t)))})}}return Promise.all(p).then(()=>{var e;if(u.length)return e={rules:u,rule:u[0]},Promise.reject(e)})},hasCellRules(t,e,r){var l=x.editRules,r=r.field;return!(!r||!l)&&(l=Il().get(l,r))&&!!Il().find(l,e=>"all"===t||!e.trigger||t===e.trigger)},triggerValidate(o){var{editConfig:e,editRules:t}=x,a=b.editStore,a=a.actived;let n=l.value;var r=w.value;if(t&&"single"===r.msgMode&&(b.validErrorMaps={}),e&&t&&a.row){let{row:t,column:r,cell:l}=a.args;if(R.hasCellRules(o,t,r))return R.validCellRules(o,t,r).then(()=>{"row"===n.mode&&E.clearValidate(t,r)}).catch(({rule:e})=>e.trigger&&o!==e.trigger?Promise.resolve():(e={rule:e,row:t,column:r,cell:l},R.showValidTooltip(e),Promise.reject(e)))}return Promise.resolve()},showValidTooltip(e){var t=x.height,{tableData:r,validStore:l,validErrorMaps:o}=b,{rule:a,row:n,column:i,cell:s}=e,d=w.value,c=p.value,u=a.content;return l.visible=!0,"single"===d.msgMode?b.validErrorMaps={[Pl(f,n)+":"+i.id]:{column:i,row:n,rule:a,content:u}}:b.validErrorMaps=Object.assign({},o,{[Pl(f,n)+":"+i.id]:{column:i,row:n,rule:a,content:u}}),f.dispatchEvent("valid-error",e,null),c&&("tooltip"===d.message||"default"===d.message&&!t&&r.length<2)?c.open(s,u):(0,Al.nextTick)()}},{...E,...R}},setupGrid(e){return e.extendTableMethods(it)}}),["openCustom","closeCustom","saveCustom","cancelCustom","resetCustom","toggleCustomAllCheckbox","setCustomAllCheckbox"]),{getConfig:dt,renderer:ct,getI18n:ut,getComponent:pt}=(Dl.VxeUI.hooks.add("tableCustomModule",{setupTable(d){let{reactData:u,internalData:a}=d,{computeCustomOpts:p,computeRowGroupFields:n}=d.getComputeMaps(),l=d.getRefMaps().refElem,r=d.xeGrid,o=()=>{var e=u.customStore,t=l.value;let r=0;t&&(r=t.clientHeight-28),e.maxHeight=Math.max(88,r)},i=()=>{var{initStore:e,customStore:t}=u;return t.visible=!0,e.custom=!0,s(),h(),o(),(0,Al.nextTick)().then(()=>o())},s=()=>{var e=u.customStore,t=a.collectColumn;if(e.visible){let r={},l={},o={};Il().eachTree(t,e=>{var t=e.getKey();e.renderFixed=e.fixed,e.renderVisible=e.visible,e.renderResizeWidth=e.renderWidth,r[t]=e.renderSortNumber,l[t]=e.fixed,o[t]=e.visible}),e.oldSortMaps=r,e.oldFixedMaps=l,e.oldVisibleMaps=o,u.customColumnList=t.slice(0)}},c=()=>{var e=u.customStore,t=p.value;return e.visible&&(e.visible=!1,t.immediate||d.handleCustom()),(0,Al.nextTick)()};let t=e=>{var t=u.customStore,r=u.customColumnList,l=p.value;let{checkMethod:o,visibleMethod:a}=l,n=!!e;return l.immediate?(Il().eachTree(r,e=>{a&&!a({$table:d,column:e})||o&&!o({$table:d,column:e})||(e.visible=n,e.renderVisible=n,e.halfVisible=!1)}),t.isAll=n,u.isCustomStatus=!0,d.handleCustom(),d.saveCustomStore("update:visible")):(Il().eachTree(r,e=>{a&&!a({$table:d,column:e})||o&&!o({$table:d,column:e})||(e.renderVisible=n,e.halfVisible=!1)}),t.isAll=n),d.checkCustomStatus(),(0,Al.nextTick)()};var e={openCustom:i,closeCustom:c,saveCustom:()=>{let{customColumnList:e,aggHandleFields:r,rowGroupList:t}=u;let{allowVisible:a,allowSort:n,allowFixed:i,allowResizable:s}=p.value;return Il().eachTree(e,(e,t,r,l,o)=>{o?e.fixed=o.fixed:(n&&(e.renderSortNumber=t+1),i&&(e.fixed=e.renderFixed)),s&&e.renderVisible&&(!e.children||e.children.length)&&e.renderResizeWidth!==e.renderWidth&&(e.resizeWidth=e.renderResizeWidth,e.renderWidth=e.renderResizeWidth),a&&(e.visible=e.renderVisible)}),u.isCustomStatus=!0,u.isDragColMove=!0,setTimeout(()=>{u.isDragColMove=!1},1e3),d.saveCustomStore("confirm").then(()=>{!d.handlePivotTableAggregateData||t.length===r.length&&!t.some((e,t)=>e.field!==r[t])||(r.length?d.setRowGroups(r):d.clearRowGroups())})},cancelCustom:()=>{var{customColumnList:e,customStore:t}=u;let{oldSortMaps:o,oldFixedMaps:a,oldVisibleMaps:n}=t,{allowVisible:i,allowSort:s,allowFixed:d,allowResizable:c}=p.value;return Il().eachTree(e,e=>{var t=e.getKey(),r=!!n[t],l=a[t]||"";i&&(e.renderVisible=r,e.visible=r),d&&(e.renderFixed=l,e.fixed=l),s&&(e.renderSortNumber=o[t]||0),c&&(e.renderResizeWidth=e.renderWidth)},{children:"children"}),(0,Al.nextTick)()},resetCustom(e){let t=u.rowGroupList;var r=a.collectColumn;let l=p.value.checkMethod,o=Object.assign({visible:!0,resizable:!0===e,fixed:!0===e,sort:!0===e},e);return Il().eachTree(r,e=>{o.resizable&&(e.resizeWidth=0),o.fixed&&(e.fixed=e.defaultFixed),o.sort&&(e.renderSortNumber=e.sortNumber),l&&!l({$table:d,column:e})||(e.visible=e.defaultVisible),e.renderResizeWidth=e.renderWidth}),u.isCustomStatus=!1,d.saveCustomStore("reset"),d.handleCustom().then(()=>{var e;d.handlePivotTableAggregateData&&((e=n.value)||t).length&&(e&&e.length?d.setRowGroups(e):d.clearRowGroups())})},toggleCustomAllCheckbox(){var e=u.customStore,e=!e.isAll;return t(e)},setCustomAllCheckbox:t};let h=()=>{var e=u.customStore,t=a.collectColumn;let r=p.value.checkMethod;e.isAll=t.every(e=>!!r&&!r({$table:d,column:e})||e.renderVisible),e.isIndeterminate=!e.isAll&&t.some(e=>(!r||r({$table:d,column:e}))&&(e.renderVisible||e.halfVisible))},g=(e,t)=>{(r||d).dispatchEvent("custom",{type:e},t)};var m={checkCustomStatus:h,emitCustomEvent:g,triggerCustomEvent(e){var t=d.reactData.customStore;t.visible?(c(),g("close",e)):(t.btnEl=e.target,i(),g("open",e))},customOpenEvent(e){var t=d.reactData.customStore;t.visible||(t.activeBtn=!0,t.btnEl=e.target,d.openCustom(),d.emitCustomEvent("open",e))},customCloseEvent(e){var t=d.reactData.customStore;t.visible&&(t.activeBtn=!1,d.closeCustom(),d.emitCustomEvent("close",e))},handleUpdateCustomColumn:s};return{...e,...m}},setupGrid(e){return e.extendTableMethods(st)}}),Dl.VxeUI),ht="modelValue",vt={};function Ot(e,t,r){return Il().eqNull(e)?Il().eqNull(t)?r:t:e}function ol(e,t,r){var{dateConfig:l={}}=t;return Il().toDateString((t=t,(e=e)&&t.valueFormat?Il().toStringDate(e,t.valueFormat):e),l.labelFormat||r)}function al(e,t){return ol(e,t,ut("vxe.input.date.labelFormat."+(t.type||"date")))}function At({name:e}){return pt(e)}function nl({name:e}){return(0,Al.resolveComponent)("vxe-"+e.replace("$",""))}function il(e,t,r){e=e.$panel;e.changeOption({},t,r)}function sl(e){let{name:t,attrs:r}=e;return r="input"===t?Object.assign({type:"text"},r):r}function dl(e){var{name:e,immediate:t,props:r}=e;return t||("VxeInput"===e||"$input"===e?(t=(r||{}).type,!(!t||"text"===t||"number"===t||"integer"===t||"float"===t)):"input"!==e&&"textarea"!==e&&"$textarea"!==e)}function Lt(e,t,r,l){return Il().assign({immediate:dl(e)},vt,l,e.props,{[ht]:r})}function cl(e,t,r,l){return Il().assign({},vt,l,e.props,{[ht]:r})}function So(e,t){return"cell"===t.$type||dl(e)}function Vt(e,t,r,l){e=e.placeholder;return[(0,Al.h)("span",{class:["vxe-cell--label",l?l.class:""]},e&&pr(r)?[(0,Al.h)("span",{class:"vxe-cell--placeholder"},kl(Ml(e),1))]:kl(r,1))]}function Do(e,r,t){let l=e.events,o=Or(e),a=Ar(e),{model:n,change:i,blur:s}=t||{},d=a===o,c={};return l&&Il().objectEach(l,(t,e)=>{c[Dt(e)]=function(...e){t(r,...e)}}),n&&(c[Dt(o)]=function(e){n(e),d&&i&&i(e),l&&l[o]&&l[o](r,e)}),!d&&i&&(c[Dt(a)]=function(e){i(e),l&&l[a]&&l[a](r,e)}),s&&(c[Dt(u)]=function(e){s(e),l&&l[u]&&l[u](r,e)}),c}let u="blur";function Io(e,r,t,l){let o=e.events,a=Or(e),n=Ar(e),{model:i,change:s,blur:d}=t||{},c={};return Il().objectEach(o,(t,e)=>{c[Dt(e)]=function(...e){Il().isFunction(t)||pl("vxe.error.errFunc",[t]),t(r,...e)}}),i&&(c[Dt(a)]=function(e){i(e),o&&o[a]&&o[a](r,e)}),s&&(c[Dt(n)]=function(...e){s(...e),o&&o[n]&&o[n](r,...e)}),d&&(c[Dt(u)]=function(...e){d(...e),o&&o[u]&&o[u](r,...e)}),l?Object.assign(c,l):c}function $t(e,t){let{$table:r,row:l,column:o}=t,a=e.name,n=o.model,i=So(e,t);return Io(e,t,{model(e){n.update=!0,n.value=e,i&&Ul(l,o,e)},change(e){!i&&a&&["VxeInput","VxeNumberInput","VxeTextarea","$input","$textarea"].includes(a)?(e=e.value,n.update=!0,n.value=e,r.updateStatus(t,e)):r.updateStatus(t)},blur(){i?r.handleCellRuleUpdateStatus("blur",t):r.handleCellRuleUpdateStatus("blur",t,n.value)}})}function Fo(e,t,r){return Io(e,t,{model(e){r.data=e},change(){il(t,!Il().eqNull(r.data),r)},blur(){il(t,!Il().eqNull(r.data),r)}})}function Mo(t,r){let{$table:l,row:o,column:a}=r,n=a.model;return Do(t,r,{model(e){var e=e.target;e&&(e=e.value,So(t,r)?Ul(o,a,e):(n.update=!0,n.value=e))},change(e){var e=e.target;e&&(e=e.value,l.updateStatus(r,e))},blur(e){var e=e.target;e&&(e=e.value,l.updateStatus(r,e))}})}function ko(e,t,r){return Do(e,t,{model(e){e=e.target;e&&(r.data=e.value)},change(){il(t,!Il().eqNull(r.data),r)},blur(){il(t,!Il().eqNull(r.data),r)}})}function Oo(e,t){var{row:r,column:l}=t,o=e.name,r=So(e,t)?jl(r,l):l.model.value;return[(0,Al.h)(o,{class:"vxe-default-"+o,...sl(e),value:r,...Mo(e,t)})]}function _t(e,t){var{row:r,column:l}=t,r=jl(r,l);return[(0,Al.h)(At(e),{...Lt(e,0,r),...$t(e,t)})]}function Ao(e,t){var r=e.options,{row:l,column:o}=t,l=jl(l,o);return[(0,Al.h)(At(e),{options:r,...Lt(e,0,l),...$t(e,t)})]}function Lo(e,t){var{row:r,column:l}=t,r=jl(r,l);return[(0,Al.h)(nl(e),{...Lt(e,0,r),...$t(e,t)})]}function Vo(e,t){return[(0,Al.h)(pt("vxe-button"),{...Lt(e,0,null),...Io(e,t)})]}function $o(r,l,o){var{optionGroups:e,optionGroupProps:t={}}=r;let a=t.options||"options",n=t.label||"label";return e?e.map((e,t)=>(0,Al.h)("optgroup",{key:t,label:e[n]},o(e[a],r,l))):[]}function _o(e,t,r){var{optionProps:l={}}=t,{row:o,column:a}=r;let n=l.label||"label",i=l.value||"value",s=l.disabled||"disabled",d=So(t,r)?jl(o,a):a.model.value;return e?e.map((e,t)=>(0,Al.h)("option",{key:t,value:e[i],disabled:e[s],selected:e[i]==d},e[n])):[]}function Ho(l,o){var e=o.column;return e.filters.map((e,t)=>{var r=e.data;return(0,Al.h)(At(l),{key:t,...cl(l,0,r),...Fo(l,o,e)})})}function Bo({option:e,row:t,column:r}){e=e.data;return Il().get(t,r.field)==e}function Po({option:e,row:t,column:r}){e=e.data,t=Il().get(t,r.field);return-1<Il().toValueString(t).indexOf(e)}function No(e,t){return[(0,Al.h)("select",{class:"vxe-default-select",...sl(e),...Mo(e,t)},e.optionGroups?$o(e,t,_o):_o(e.options,e,t))]}function zo(e,t){var{row:r,column:l}=t,{options:o,optionProps:a,optionGroups:n,optionGroupProps:i}=e,r=jl(r,l);return[(0,Al.h)(At(e),{...Lt(e,0,r,{options:o,optionProps:a,optionGroups:n,optionGroupProps:i}),...$t(e,t)})]}function jo(e,t){var{row:r,column:l}=t,{options:o,optionProps:a}=e,r=jl(r,l);return[(0,Al.h)(At(e),{...Lt(e,0,r,{options:o,optionProps:a}),...$t(e,t)})]}function Uo(e,t){var{row:r,column:l}=t,{options:o,optionProps:a,optionGroups:n,optionGroupProps:i}=e,r=jl(r,l);return[(0,Al.h)(nl(e),{...Lt(e,0,r,{options:o,optionProps:a,optionGroups:n,optionGroupProps:i}),...$t(e,t)})]}function Wo(e,{row:t,column:r}){let{options:l,optionGroups:o,optionProps:a={},optionGroupProps:n={}}=e;e=Il().get(t,r.field);let i,s=a.label||"label",d=a.value||"value";return null!=e?Il().map(Il().isArray(e)?e:[e],o?t=>{var r=n.options||"options";for(let e=0;e<o.length&&!(i=Il().find(o[e][r],e=>e[d]==t));e++);return i?i[s]:t}:t=>(i=Il().find(l,e=>e[d]==t))?i[s]:t).join(", "):""}function Go(e){var{row:t,column:r,options:l}=e;return l.original?jl(t,r):Wo(r.editRender||r.cellRender,e)}function qo(e,{row:r,column:l}){var{options:e,optionProps:t={}}=e,r=Il().get(r,l.field);let o=t.label||"label",a=t.value||"value";l=t.children||"children";if(null==r)return"";{let t={};return Il().eachTree(e,e=>{t[Il().get(e,a)]=e},{children:l}),Il().map(Il().isArray(r)?r:[r],e=>{e=t[e];return e&&Il().get(e,o)}).join(", ")}}function Xo(e){var{row:t,column:r,options:l}=e;return l.original?jl(t,r):qo(r.editRender||r.cellRender,e)}function Ko(e,t){var r,{props:l={},showNegativeStatus:o}=e,{row:t,column:a}=t,n=l.type;let i=Il().get(t,a.field),s=!1;return pr(i)||(t=dt().numberInput||{},"float"===n?(a=Ot(l.autoFill,t.autoFill,!0),r=Ot(l.digits,t.digits,1),i=Il().toFixed(Il().floor(i,r),r),a||(i=Il().toNumber(i)),o&&i<0&&(s=!0)):"amount"===n?(r=Ot(l.autoFill,t.autoFill,!0),a=Ot(l.digits,t.digits,2),n=Ot(l.showCurrency,t.showCurrency,!1),i=Il().toNumber(i),o&&i<0&&(s=!0),i=Il().commafy(i,{digits:a}),r||([a,r]=i.split("."),r&&(r=r.replace(/0+$/,""),i=r?[a,".",r].join(""):a)),n&&(i=""+(l.currencySymbol||t.currencySymbol||ut("vxe.numberInput.currencySymbol")||"")+i)):o&&Il().toNumber(i)<0&&(s=!0)),Vt(e,0,i,s?{class:"is--negative"}:{})}ct.mixin({input:{tableAutoFocus:"input",renderTableEdit:Oo,renderTableDefault:Oo,renderTableFilter:function(r,l){var e=l.column;let o=r.name,a=sl(r);return e.filters.map((e,t)=>(0,Al.h)(o,{key:t,class:"vxe-default-"+o,...a,value:e.data,...ko(r,l,e)}))},tableFilterDefaultMethod:Po},textarea:{tableAutoFocus:"textarea",renderTableEdit:Oo},select:{renderTableEdit:No,renderTableDefault:No,renderTableCell(e,t){return Vt(e,0,Wo(e,t))},renderTableFilter(r,l){var e=l.column;return e.filters.map((e,t)=>(0,Al.h)("select",{key:t,class:"vxe-default-select",...sl(r),...ko(r,l,e)},r.optionGroups?$o(r,l,_o):_o(r.options,r,l)))},tableFilterDefaultMethod:Bo,tableExportMethod:Go},VxeInput:{tableAutoFocus:"input",renderTableEdit:_t,renderTableCell(e,t){var{props:r={}}=e,{row:t,column:l}=t,o=dt().input||{},a=r.digits||o.digits||2;let n=Il().get(t,l.field);if(n)switch(r.type){case"date":case"week":case"month":case"quarter":case"year":n=al(n,r);break;case"float":n=Il().toFixed(Il().floor(n,a),a)}return Vt(e,0,n)},renderTableDefault:_t,renderTableFilter:Ho,tableFilterDefaultMethod:Po},FormatNumberInput:{renderTableDefault:Ko,tableFilterDefaultMethod:Po,tableExportMethod(e){var{row:e,column:t}=e;return Il().get(e,t.field)}},VxeNumberInput:{tableAutoFocus:"input",renderTableEdit:_t,renderTableCell:Ko,renderTableFooter(t,r){var{props:t={}}=t,{row:r,column:l,_columnIndex:o}=r,a=t.type,o=Il().isArray(r)?r[o]:Il().get(r,l.field);if(Il().isNumber(o)){r=dt().numberInput||{};if("float"===a){var l=Ot(t.autoFill,r.autoFill,!0),n=Ot(t.digits,r.digits,1);let e=Il().toFixed(Il().floor(o,n),n);return e=l?e:Il().toNumber(e)}if("amount"===a){var n=Ot(t.autoFill,r.autoFill,!0),l=Ot(t.digits,r.digits,2),a=Ot(t.showCurrency,r.showCurrency,!1);let e=Il().commafy(Il().toNumber(o),{digits:l});return n||([l,n]=e.split("."),n&&(n=n.replace(/0+$/,""),e=n?[l,".",n].join(""):l)),e=a?""+(t.currencySymbol||r.currencySymbol||ut("vxe.numberInput.currencySymbol")||"")+e:e}}return Ml(o,1)},renderTableDefault:_t,renderTableFilter:Ho,tableFilterDefaultMethod:Po,tableExportMethod(e){var{row:e,column:t}=e;return Il().get(e,t.field)}},VxeDatePicker:{tableAutoFocus:"input",renderTableEdit:_t,renderTableCell(e,t){var{props:r={}}=e,{row:t,column:l}=t;let o=Il().get(t,l.field);return Vt(e,0,o=o&&"time"!==r.type?al(o,r):o)},renderTableDefault:_t,renderTableFilter:Ho,tableFilterDefaultMethod:Bo},VxeDateRangePicker:{tableAutoFocus:"input",renderTableEdit(e,t){let{startField:r,endField:l}=e,{$table:o,row:a,column:n}=t,i=n.model;var s=jl(a,n),d={},c={};return r&&l&&(d.startValue=Il().get(a,r),d.endValue=Il().get(a,l),c["onUpdate:startValue"]=e=>{r&&Il().set(a,r,e)},c["onUpdate:endValue"]=e=>{l&&Il().set(a,l,e)}),[(0,Al.h)(At(e),{...Lt(e,0,s,d),...Io(e,t,{model(e){i.update=!0,i.value=e,Ul(a,n,e)},change(){o.updateStatus(t)},blur(){o.handleCellRuleUpdateStatus("blur",t)}},c)})]},renderTableCell(e,t){var{startField:r,endField:l}=e,{row:t,column:o}=t;let a="",n="",i=(r&&l?(a=Il().get(t,r),n=Il().get(t,l)):(r=Il().get(t,o.field))&&(n=(Il().isArray(r)?(a=r[0],r):(l=(""+r).split(","),a=l[0],l))[1]),"");return Vt(e,0,i=a&&n?a+" ~ "+n:i)}},VxeTextarea:{tableAutoFocus:"textarea",renderTableEdit:_t,renderTableCell(e,t){var{row:t,column:r}=t;return Vt(e,0,Il().get(t,r.field))}},VxeButton:{renderTableDefault:function(e,t){return[(0,Al.h)(At(e),{...Lt(e,0,null),...Io(e,t)})]}},VxeButtonGroup:{renderTableDefault(e,t){var r=e.options;return[(0,Al.h)(At(e),{options:r,...Lt(e,0,null),...Io(e,t)})]}},VxeSelect:{tableAutoFocus:"input",renderTableEdit:zo,renderTableDefault:zo,renderTableCell(e,t){return Vt(e,0,Wo(e,t))},renderTableFilter(l,o){var e=o.column;let{options:a,optionProps:n,optionGroups:i,optionGroupProps:s}=l;return e.filters.map((e,t)=>{var r=e.data;return(0,Al.h)(At(l),{key:t,...cl(l,0,r,{options:a,optionProps:n,optionGroups:i,optionGroupProps:s}),...Fo(l,o,e)})})},tableFilterDefaultMethod:Bo,tableExportMethod:Go},formatOption:{renderTableDefault(e,t){return Vt(e,0,Wo(e,t))}},FormatSelect:{renderTableDefault(e,t){return Vt(e,0,Wo(e,t))},tableFilterDefaultMethod:Bo,tableExportMethod:Go},VxeTreeSelect:{tableAutoFocus:"input",renderTableEdit:jo,renderTableCell(e,t){return Vt(e,0,qo(e,t))},tableExportMethod:Xo},formatTree:{renderTableDefault(e,t){return Vt(e,0,qo(e,t))}},FormatTreeSelect:{renderTableDefault(e,t){return Vt(e,0,qo(e,t))},tableExportMethod:Xo},VxeTableSelect:{tableAutoFocus:"input",renderTableEdit:jo,renderTableCell(e,t){return Vt(e,0,qo(e,t))},tableExportMethod:Xo},VxeColorPicker:{tableAutoFocus:"input",renderTableEdit(e,t){var{row:r,column:l}=t,o=e.options,r=jl(r,l);return[(0,Al.h)(At(e),{...Lt(e,0,r,{colors:o}),...$t(e,t)})]},renderTableCell(e,t){var{row:t,column:r}=t,t=Il().get(t,r.field);return(0,Al.h)("span",{class:"vxe-color-picker--readonly"},[(0,Al.h)("div",{class:"vxe-color-picker--readonly-color",style:{backgroundColor:t}})])}},VxeIconPicker:{tableAutoFocus:"input",renderTableEdit(e,t){var{row:r,column:l}=t,o=e.options,r=jl(r,l);return[(0,Al.h)(At(e),{...Lt(e,0,r,{icons:o}),...$t(e,t)})]},renderTableCell(e,t){var{row:t,column:r}=t,t=Il().get(t,r.field);return(0,Al.h)("i",{class:t})}},VxeRadioGroup:{renderTableDefault:Ao},VxeCheckbox:{renderTableDefault:function(e,t){var{row:r,column:l}=t,r=jl(r,l);return[(0,Al.h)(At(e),{...Lt(e,0,r),...$t(e,t)})]}},VxeCheckboxGroup:{renderTableDefault:Ao},VxeSwitch:{tableAutoFocus:"button",renderTableEdit:_t,renderTableDefault:_t},VxeUpload:{renderTableEdit:_t,renderTableCell:_t,renderTableDefault:_t},VxeImage:{renderTableDefault(e,t){var{row:r,column:l}=t,o=e.props,r=jl(r,l);return[(0,Al.h)(At(e),{...o,src:r,...$t(e,t)})]}},VxeImageGroup:{renderTableDefault(e,t){var{row:r,column:l}=t,o=e.props,r=jl(r,l);return[(0,Al.h)(At(e),{...o,urlList:r,...$t(e,t)})]}},VxeTextEllipsis:{renderTableDefault(e,t){var{row:r,column:l}=t,o=e.props,r=jl(r,l);return[(0,Al.h)(At(e),{...o,content:r,...$t(e,t)})]}},VxeRate:{renderTableDefault:_t},VxeSlider:{renderTableDefault:_t},$input:{tableAutoFocus:".vxe-input--inner",renderTableEdit:Lo,renderTableCell(e,t){var{props:r={}}=e,{row:t,column:l}=t,o=r.digits||dt().input?.digits||2;let a=Il().get(t,l.field);if(a)switch(r.type){case"date":case"week":case"month":case"year":a=al(a,r);break;case"float":a=Il().toFixed(Il().floor(a,o),o)}return Vt(e,0,a)},renderTableDefault:Lo,renderTableFilter:function(l,o){return o.column.filters.map((e,t)=>{var r=e.data;return(0,Al.h)(nl(l),{key:t,...cl(l,0,r),...Fo(l,o,e)})})},tableFilterDefaultMethod:Po},$textarea:{tableAutoFocus:".vxe-textarea--inner"},$button:{renderTableDefault:Vo},$buttons:{renderTableDefault:function(e,t){return e.children.map(e=>Vo(e,t)[0])}},$select:{tableAutoFocus:".vxe-input--inner",renderTableEdit:Uo,renderTableDefault:Uo,renderTableCell(e,t){return Vt(e,0,Wo(e,t))},renderTableFilter(l,o){var e=o.column;let{options:a,optionProps:n,optionGroups:i,optionGroupProps:s}=l;return e.filters.map((e,t)=>{var r=e.data;return(0,Al.h)(nl(l),{key:t,...cl(l,0,r,{options:a,optionProps:n,optionGroups:i,optionGroupProps:s}),...Fo(l,o,e)})})},tableFilterDefaultMethod:Bo,tableExportMethod:Go},$radio:{tableAutoFocus:".vxe-radio--input"},$checkbox:{tableAutoFocus:".vxe-checkbox--input"},$switch:{tableAutoFocus:".vxe-switch--button",renderTableEdit:Lo,renderTableDefault:Lo}});let ft=Object.assign({},zr,{install(e){e.component(zr.name,zr)}}),xt=(Tt={useCellView:function(l){var e=(0,Al.computed)(()=>{var e=l.renderParams;return e.column}),t=(0,Al.computed)(()=>{var e=l.renderParams;return e.row}),r=(0,Al.computed)(()=>{var e=l.renderOpts;return e.props||{}});return{currColumn:e,currRow:t,cellModel:(0,Al.computed)({get(){var e=l.renderParams,{row:e,column:t}=e;return Il().get(e,t.field)},set(e){var t=l.renderParams,{row:t,column:r}=t;return Il().set(t,r.field,e)}}),cellOptions:r}}},Dl.VxeUI.dynamicApp&&Dl.VxeUI.dynamicApp.component(zr.name,zr),Dl.VxeUI.component(zr),Dl.VxeUI.tableHandle=Tt,ft),bt=Object.assign({},jr,{install(e){e.component(jr.name,jr)}}),Ct=(Dl.VxeUI.dynamicApp&&Dl.VxeUI.dynamicApp.component(jr.name,jr),Dl.VxeUI.component(jr),bt),wt=[r,l,_e,ft,bt];function Yo(t,e){Dl.VxeUI.setConfig(e),wt.forEach(e=>e.install(t))}Dl.VxeUI.hasLanguage("zh-CN")||(Dl.VxeUI.setI18n("zh-CN",{vxe:{base:{pleaseInput:"请输入",pleaseSelect:"请选择",comma:"，",fullStop:"。"},loading:{text:"加载中..."},error:{downErr:"下载失败",errLargeData:"当绑定的数据量过大时，应该请使用 {0}，否则可能会出现卡顿",groupFixed:"如果使用分组表头，冻结列必须按组设置",groupMouseRange:'分组表头与 "{0}" 不能同时使用，这可能会出现错误',groupTag:'分组列头应该使用 "{0}" 而不是 "{1}"，这可能会出现错误',scrollErrProp:'启用虚拟滚动后不支持该参数 "{0}"',errConflicts:'参数 "{0}" 与 "{1}" 有冲突',notSupportProp:'当启用参数 "{0}" 时不支持 "{1}"，应该为 "{2}"，否则将会出现错误',notConflictProp:'当使用 "{0}" 时，应该设置 "{1}"，否则可能会存在功能冲突',unableInsert:"无法插入到指定位置，请检查参数是否正确",useErr:'安装 "{0}" 模块时发生错误，可能顺序不正确，依赖的模块需要在 Table 之前安装',barUnableLink:"工具栏无法关联表格",expandContent:'展开行的插槽应该是 "content"，请检查是否正确',reqComp:'缺少 "{0}" 组件，请检查是否正确安装。 https://vxeui.com/#/start/useGlobal',reqModule:'缺少 "{0}" 模块',reqProp:'缺少必要的 "{0}" 参数，这可能会导致出现错误',emptyProp:'参数 "{0}" 不允许为空',errProp:'不支持的参数 "{0}"，可能为 "{1}"',colRepet:'column.{0}="{1}" 重复了，这可能会导致某些功能无法使用',notFunc:'方法 "{0}" 不存在',errFunc:'参数 "{0}" 不是一个方法',notValidators:'全局校验 "{0}" 不存在',notFormats:'全局格式化 "{0}" 不存在',notCommands:'全局指令 "{0}" 不存在',notSlot:'插槽 "{0}" 不存在',noTree:'树结构不支持 "{0}"',notProp:'不支持的参数 "{0}"',checkProp:'当数据量过大时可能会导致复选框卡顿，建议设置参数 "{0}" 提升渲染速度',coverProp:'"{0}" 的参数 "{1}" 重复定义，这可能会出现错误',uniField:'字段名 "{0}" 重复定义，这可能会出现错误',repeatKey:'主键重复 {0}="{1}"，这可能会出现错误',delFunc:'方法 "{0}" 已废弃，请使用 "{1}"',delProp:'参数 "{0}" 已废弃，请使用 "{1}"',delEvent:'事件 "{0}" 已废弃，请使用 "{1}"',removeProp:'参数 "{0}" 已废弃，不建议使用，这可能会导致出现错误',errFormat:'全局的格式化内容应该使用 "VXETable.formats" 定义，挂载 "formatter={0}" 的方式已不建议使用',notType:'不支持的文件类型 "{0}"',notExp:"该浏览器不支持导入/导出功能",impFields:"导入失败，请检查字段名和数据格式是否正确",treeNotImp:"树表格不支持导入",treeCrossDrag:"只能拖拽第一层级",treeDragChild:"父级不能拖拽到自己的子级中",reqPlugin:'扩展插件未安装 "{1}" https://vxeui.com/other{0}/#/{1}/install',errMaxRow:"超过支持的最大数据量 {0} 行，这可能会导致出现错误"},table:{emptyText:"暂无数据",allTitle:"全选/取消",seqTitle:"序号",actionTitle:"操作",confirmFilter:"筛选",resetFilter:"重置",allFilter:"全部",sortAsc:"升序：最低到最高",sortDesc:"降序：最高到最低",filter:"对所选的列启用筛选",impSuccess:"成功导入 {0} 条记录",expLoading:"正在导出中",expSuccess:"导出成功",expError:"导出失败",expFilename:"导出_{0}",expOriginFilename:"导出_源_{0}",customTitle:"列设置",customAll:"全部",customConfirm:"确认",customClose:"关闭",customCancel:"取消",customRestore:"恢复默认",maxFixedCol:"最大冻结列的数量不能超过 {0} 个",dragTip:"移动：{0}",resizeColTip:"宽：{0} 像素",resizeRowTip:"高：{0} 像素",rowGroupContentTotal:"{0}（{1}）"},grid:{selectOneRecord:"请至少选择一条记录！",deleteSelectRecord:"您确定要删除所选记录吗？",removeSelectRecord:"您确定要移除所选记录吗？",dataUnchanged:"数据未改动！",delSuccess:"成功删除所选记录！",saveSuccess:"保存成功！",operError:"发生错误，操作失败！"},select:{search:"搜索",loadingText:"加载中",emptyText:"暂无数据"},pager:{goto:"前往",gotoTitle:"页数",pagesize:"{0}条/页",total:"共 {0} 条记录",pageClassifier:"页",homePage:"首页",homePageTitle:"首页",prevPage:"上一页",prevPageTitle:"上一页",nextPage:"下一页",nextPageTitle:"下一页",prevJump:"向上跳页",prevJumpTitle:"向上跳页",nextJump:"向下跳页",nextJumpTitle:"向下跳页",endPage:"末页",endPageTitle:"末页"},alert:{title:"系统提示"},button:{confirm:"确认",cancel:"取消",clear:"清除"},filter:{search:"搜索"},custom:{cstmTitle:"列设置",cstmRestore:"恢复默认",cstmCancel:"取消",cstmConfirm:"确定",cstmConfirmRestore:"请确认是否恢复成默认列配置？",cstmDragTarget:"移动：{0}",setting:{colSort:"排序",sortHelpTip:"点击并拖动图标可以调整列的排序",colTitle:"列标题",colResizable:"列宽（像素）",colVisible:"是否显示",colFixed:"冻结列",colFixedMax:"冻结列（最多 {0} 列）",fixedLeft:"左侧",fixedUnset:"不设置",fixedRight:"右侧"}},import:{modes:{covering:"覆盖方式（直接覆盖表格数据）",insert:"底部追加（在表格的底部追加新数据）",insertTop:"顶部追加（在表格的顶部追加新数据）",insertBottom:"底部追加（在表格的底部追加新数据）"},impTitle:"导入数据",impFile:"文件名",impSelect:"选择文件",impType:"文件类型",impOpts:"参数设置",impMode:"导入模式",impConfirm:"导入",impCancel:"取消"},export:{types:{csv:"CSV (逗号分隔)(*.csv)",html:"网页(*.html)",xml:"XML 数据(*.xml)",txt:"文本文件(制表符分隔)(*.txt)",xls:"Excel 97-2003 工作簿(*.xls)",xlsx:"Excel 工作簿(*.xlsx)",pdf:"PDF (*.pdf)"},modes:{empty:"空数据",current:"当前数据（当前页的数据）",selected:"选中数据（当前页选中的数据）",all:"全量数据（包括所有分页的数据）"},printTitle:"打印数据",expTitle:"导出数据",expName:"文件名",expNamePlaceholder:"请输入文件名",expSheetName:"标题",expSheetNamePlaceholder:"请输入标题",expType:"保存类型",expMode:"选择数据",expCurrentColumn:"全部字段",expColumn:"选择字段",expOpts:"参数设置",expOptHeader:"表头",expHeaderTitle:"是否需要表头",expOptFooter:"表尾",expFooterTitle:"是否需要表尾",expOptColgroup:"分组表头",expOptTitle:"列标题",expTitleTitle:"是否为列标题，否则显示为列的字段名",expColgroupTitle:"如果存在，则支持带有分组结构的表头",expOptMerge:"合并",expMergeTitle:"如果存在，则支持带有合并结构的单元格",expOptAllExpand:"展开树",expAllExpandTitle:"如果存在，则支持将带有层级结构的数据全部展开",expOptUseStyle:"样式",expUseStyleTitle:"如果存在，则支持带样式的单元格",expOptOriginal:"源数据",expOriginalTitle:"如果为源数据，则支持导入到表格中",expPrint:"打印",expConfirm:"导出",expCancel:"取消"},modal:{errTitle:"错误提示",zoomMin:"最小化",zoomIn:"最大化",zoomOut:"还原",close:"关闭",miniMaxSize:"最小化窗口的数量不能超过 {0} 个",footPropErr:"show-footer 仅用于启用表尾，需配合 show-confirm-button | show-cancel-button | 插槽使用"},drawer:{close:"关闭"},form:{folding:"收起",unfolding:"展开"},toolbar:{import:"导入",export:"导出",print:"打印",refresh:"刷新",zoomIn:"全屏",zoomOut:"还原",custom:"列设置",customAll:"全部",customConfirm:"确认",customRestore:"重置",fixedLeft:"冻结在左侧",fixedRight:"冻结在右侧",cancelFixed:"取消冻结列"},datePicker:{yearTitle:"{0} 年"},dateRangePicker:{pleaseRange:"请选择开始日期与结束日期"},input:{date:{m1:"01 月",m2:"02 月",m3:"03 月",m4:"04 月",m5:"05 月",m6:"06 月",m7:"07 月",m8:"08 月",m9:"09 月",m10:"10 月",m11:"11 月",m12:"12 月",quarterLabel:"{0} 年",monthLabel:"{0} 年",dayLabel:"{0} 年 {1}",labelFormat:{date:"yyyy-MM-dd",time:"HH:mm:ss",datetime:"yyyy-MM-dd HH:mm:ss",week:"yyyy 年第 WW 周",month:"yyyy-MM",quarter:"yyyy 年第 q 季度",year:"yyyy"},weeks:{w:"",w0:"周日",w1:"周一",w2:"周二",w3:"周三",w4:"周四",w5:"周五",w6:"周六"},months:{m0:"一月",m1:"二月",m2:"三月",m3:"四月",m4:"五月",m5:"六月",m6:"七月",m7:"八月",m8:"九月",m9:"十月",m10:"十一月",m11:"十二月"},quarters:{q1:"第一季度",q2:"第二季度",q3:"第三季度",q4:"第四季度"}}},numberInput:{currencySymbol:"¥"},imagePreview:{popupTitle:"预览",operBtn:{zoomOut:"缩小",zoomIn:"放大",pctFull:"等比例缩放",pct11:"显示原始尺寸",rotateLeft:"向左旋转",rotateRight:"向右旋转",print:"点击打印图片",download:"点击下载图片"}},upload:{fileBtnText:"点击或拖拽上传",imgBtnText:"点击或拖拽上传",dragPlaceholder:"请把文件拖放到这个区域即可上传",imgSizeHint:"单张{0}",imgCountHint:"最多{0}张",fileTypeHint:"支持 {0} 文件类型",fileSizeHint:"单个文件大小不超过{0}",fileCountHint:"最多可上传{0}个文件",uploadTypeErr:"文件类型不匹配！",overCountErr:"最多只能选择{0}个文件！",overCountExtraErr:"已超出最大数量{0}个，超出的{1}个文件将被忽略！",overSizeErr:"文件大小最大不能超过{0}！",reUpload:"重新上传",uploadProgress:"上传中 {0}%",uploadErr:"上传失败",uploadSuccess:"上传成功",moreBtnText:"更多（{0}）",viewItemTitle:"点击查看",morePopup:{readTitle:"查看列表",imageTitle:"上传图片",fileTitle:"上传文件"}},empty:{defText:"暂无数据"},colorPicker:{clear:"清除",confirm:"确认",copySuccess:"已复制到剪贴板：{0}"},formDesign:{formName:"表单名称",defFormTitle:"未命名的表单",widgetPropTab:"控件属性",widgetFormTab:"表单属性",error:{wdFormUni:"该类型的控件在表单中只允许添加一个",wdSubUni:"该类型的控件在子表中只允许添加一个"},styleSetting:{btn:"样式设置",title:"表单的样式设置",layoutTitle:"控件布局",verticalLayout:"上下布局",horizontalLayout:"横向布局",styleTitle:"标题样式",boldTitle:"标题加粗",fontBold:"加粗",fontNormal:"常规",colonTitle:"显示冒号",colonVisible:"显示",colonHidden:"隐藏",alignTitle:"对齐方式",widthTitle:"标题宽度",alignLeft:"居左",alignRight:"居右",unitPx:"像素",unitPct:"百分比"},widget:{group:{base:"基础控件",layout:"布局控件",system:"系统控件",module:"模块控件",chart:"图表控件",advanced:"高级控件"},copyTitle:"副本_{0}",component:{input:"输入框",textarea:"文本域",select:"下拉选择",row:"一行多列",title:"标题",text:"文本",subtable:"子表",VxeSwitch:"是/否",VxeInput:"输入框",VxeNumberInput:"数字",VxeDatePicker:"日期",VxeTextarea:"文本域",VxeSelect:"下拉选择",VxeTreeSelect:"树形选择",VxeRadioGroup:"单选框",VxeCheckboxGroup:"复选框",VxeUploadFile:"文件",VxeUploadImage:"图片",VxeRate:"评分",VxeSlider:"滑块"}},widgetProp:{name:"控件名称",placeholder:"提示语",required:"必填校验",multiple:"允许多选",displaySetting:{name:"显示设置",pc:"电脑端",mobile:"手机端",visible:"显示",hidden:"隐藏"},dataSource:{name:"数据源",defValue:"选项{0}",addOption:"添加选项",batchEditOption:"批量编辑",batchEditTip:"每行对应一个选项，支持从表格、Excel、WPS 中直接复制粘贴。",batchEditSubTip:"每行对应一个选项，如果是分组，子项可以是空格或制表键开头，支持从表格、Excel、WPS 中直接复制粘贴。",buildOption:"生成选项"},rowProp:{colSize:"列数",col2:"两列",col3:"三列",col4:"四列",col6:"六列",layout:"布局"},textProp:{name:"内容",alignTitle:"对齐方式",alignLeft:"居左",alignCenter:"居中",alignRight:"居右",colorTitle:"字体颜色",sizeTitle:"字体大小",boldTitle:"字体加粗",fontNormal:"常规",fontBold:"加粗"},subtableProp:{seqTitle:"序号",showSeq:"显示序号",showCheckbox:"允许多选",errSubDrag:"子表不支持该控件，请使用其他控件",colPlace:"将控件拖拽进来"},uploadProp:{limitFileCount:"文件数量限制",limitFileSize:"文件大小限制",multiFile:"允许上传多个文件",limitImgCount:"图片数量限制",limitImgSize:"图片大小限制",multiImg:"允许上传多张图片"}}},listDesign:{fieldSettingTab:"字段设置",listSettingTab:"参数设置",searchTitle:"查询条件",listTitle:"列表字段",searchField:"查询字段",listField:"列表字段",activeBtn:{ActionButtonUpdate:"编辑",ActionButtonDelete:"删除"},search:{addBtn:"编辑",emptyText:"未配置查询条件",editPopupTitle:"编辑查询字段"},searchPopup:{colTitle:"标题",saveBtn:"保存"}},text:{copySuccess:"已复制到剪贴板",copyError:"当前环境不支持该操作"},countdown:{formats:{yyyy:"年",MM:"月",dd:"天",HH:"时",mm:"分",ss:"秒"}},plugins:{extendCellArea:{area:{mergeErr:"无法对合并单元格进行该操作",multiErr:"无法对多重选择区域进行该操作",selectErr:"无法操作指定区域的单元格",extendErr:"如果延伸的区域包含被合并的单元格，所有合并的单元格需大小相同",pasteMultiErr:"无法粘贴，需要相同大小的复制的区域和粘贴的区域才能执行此操作",cpInvalidErr:"该操作无法进行，您选择的区域中存在被禁止的列（{0}）"},fnr:{title:"查找和替换",findLabel:"查找",replaceLabel:"替换",findTitle:"查找内容：",replaceTitle:"替换为：",tabs:{find:"查找",replace:"替换"},filter:{re:"正则表达式",whole:"全词匹配",sensitive:"区分大小写"},btns:{findNext:"查找下一个",findAll:"查找全部",replace:"替换",replaceAll:"替换全部",cancel:"取消"},header:{seq:"#",cell:"单元格",value:"值"},body:{row:"行：{0}",col:"列：{0}"},empty:"(空值)",reError:"无效的正则表达式",recordCount:"已找到 {0} 个单元格",notCell:"找不到匹配的单元格",replaceSuccess:"成功替换 {0} 个单元格"}},extendPivotTable:{aggregation:{grouping:"分组",values:"值"}},filterComplexInput:{menus:{fixedColumn:"冻结列",fixedGroup:"冻结分组",cancelFixed:"取消冻结",fixedLeft:"冻结左侧",fixedRight:"冻结右侧"},cases:{equal:"等于",gt:"大于",lt:"小于",begin:"开头是",endin:"结尾是",include:"包含",isSensitive:"区分大小写"}},filterCombination:{menus:{sort:"排序",clearSort:"清除排序",sortAsc:"升序",sortDesc:"降序",fixedColumn:"冻结列",fixedGroup:"冻结分组",cancelFixed:"取消冻结",fixedLeft:"冻结左侧",fixedRight:"冻结右侧",clearFilter:"清除筛选",textOption:"文本筛选",numberOption:"数值筛选"},popup:{title:"自定义筛选的方式",currColumnTitle:"当前列：",and:"与",or:"或",describeHtml:"可用 ? 代表单个字符<br/>用 * 代表任意多个字符"},cases:{equal:"等于",unequal:"不等于",gt:"大于",ge:"大于或等于",lt:"小于",le:"小于或等于",begin:"开头是",notbegin:"开头不是",endin:"结尾是",notendin:"结尾不是",include:"包含",exclude:"不包含",between:"介于",custom:"自定义筛选",insensitive:"不区分大小写",isSensitive:"区分大小写"},empty:"(空白)",notData:"无匹配项"}},pro:{area:{mergeErr:"无法对合并单元格进行该操作",multiErr:"无法对多重选择区域进行该操作",extendErr:"如果延伸的区域包含被合并的单元格，所有合并的单元格需大小相同",pasteMultiErr:"无法粘贴，需要相同大小的复制的区域和粘贴的区域才能执行此操作"},fnr:{title:"查找和替换",findLabel:"查找",replaceLabel:"替换",findTitle:"查找内容：",replaceTitle:"替换为：",tabs:{find:"查找",replace:"替换"},filter:{re:"正则表达式",whole:"全词匹配",sensitive:"区分大小写"},btns:{findNext:"查找下一个",findAll:"查找全部",replace:"替换",replaceAll:"替换全部",cancel:"取消"},header:{seq:"#",cell:"单元格",value:"值"},empty:"(空值)",reError:"无效的正则表达式",recordCount:"已找到 {0} 个单元格",notCell:"找不到匹配的单元格",replaceSuccess:"成功替换 {0} 个单元格"}},renderer:{search:"搜索",cases:{equal:"等于",unequal:"不等于",gt:"大于",ge:"大于或等于",lt:"小于",le:"小于或等于",begin:"开头是",notbegin:"开头不是",endin:"结尾是",notendin:"结尾不是",include:"包含",exclude:"不包含",between:"介于",custom:"自定义筛选",insensitive:"不区分大小写",isSensitive:"区分大小写"},combination:{menus:{sort:"排序",clearSort:"清除排序",sortAsc:"升序",sortDesc:"降序",fixedColumn:"冻结列",fixedGroup:"冻结分组",cancelFixed:"取消冻结",fixedLeft:"冻结到左侧",fixedRight:"冻结到右侧",clearFilter:"清除筛选",textOption:"文本筛选",numberOption:"数值筛选"},popup:{title:"自定义筛选的方式",currColumnTitle:"当前列：",and:"与",or:"或",describeHtml:"可用 ? 代表单个字符<br/>用 * 代表任意多个字符"},empty:"(空白)",notData:"无匹配项"}}}}),Dl.VxeUI.setLanguage("zh-CN")),Dl.VxeUI.setTheme("light"),"undefined"!=typeof window&&window.Vue&&!window.VXETable&&(window.VXETable=Ut);var Zo=Ut;return jt}});