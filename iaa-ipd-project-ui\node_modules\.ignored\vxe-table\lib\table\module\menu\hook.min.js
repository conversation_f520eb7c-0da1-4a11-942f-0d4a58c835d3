var _vue=require("vue"),_xeUtils=_interopRequireDefault(require("xe-utils")),_ui=require("../../../ui"),_dom=require("../../../ui/src/dom"),_utils=require("../../../ui/src/utils");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let{menus,hooks,globalEvents,GLOBAL_EVENT_KEYS}=_ui.VxeUI,tableMenuMethodKeys=["closeMenu"];hooks.add("tableMenuModule",{setupTable(b){let{xID:m,props:p,reactData:M,internalData:E}=b,{refElem:x,refTableFilter:_,refTableMenu:C}=b.getRefMaps(),{computeMouseOpts:T,computeIsMenu:t,computeMenuOpts:y}=b.getComputeMaps(),w;var e;let L=(l,n,v)=>{let f=M.ctxMenuStore;var o=t.value,e=y.value,n=e[n];let i=e.visibleMethod;if(n){let{options:t,disabled:e}=n;e?l.preventDefault():o&&t&&t.length&&(v.options=t,b.preventEvent(l,"event.showMenu",v,()=>{if(!i||i(v)){l.preventDefault(),b.updateZindex();let{scrollTop:o,scrollLeft:i,visibleHeight:u,visibleWidth:s}=(0,_dom.getDomNode)(),r=l.clientY+o,a=l.clientX+i,n=()=>{E._currMenuParams=v,Object.assign(f,{visible:!0,list:t,selected:null,selectChild:null,showChild:!1,style:{zIndex:E.tZindex,top:r+"px",left:a+"px"}}),(0,_vue.nextTick)(()=>{var e=C.value.getRefMaps().refElem.value,t=e.clientHeight,l=e.clientWidth,{boundingTop:e,boundingLeft:n}=(0,_dom.getAbsolutePos)(e),e=e+t-u,n=n+l-s;-10<e&&(f.style.top=Math.max(o+2,r-t-2)+"px"),-10<n&&(f.style.left=Math.max(i+2,a-l-2)+"px")})},{keyboard:e,row:d,column:c}=v;e&&d&&c?b.scrollToRow(d,c).then(()=>{var e,t,l=b.getCellElement(d,c);l&&({boundingTop:e,boundingLeft:t}=(0,_dom.getAbsolutePos)(l),r=e+o+Math.floor(l.offsetHeight/2),a=t+i+Math.floor(l.offsetWidth/2)),n()}):n()}else w.closeMenu()}))}b.closeFilter()};return w={closeMenu(){return Object.assign(M.ctxMenuStore,{visible:!1,selected:null,selectChild:null,showChild:!1}),(0,_vue.nextTick)()}},e={moveCtxMenu(e,t,l,n,o,i){let u;var s=_xeUtils.default.findIndexOf(i,e=>t[l]===e);if(n)o&&(0,_utils.hasChildrenList)(t.selected)?t.showChild=!0:(t.showChild=!1,t.selectChild=null);else if(globalEvents.hasKey(e,GLOBAL_EVENT_KEYS.ARROW_UP)){for(let e=s-1;0<=e;e--)if(!1!==i[e].visible){u=i[e];break}t[l]=u||i[i.length-1]}else if(globalEvents.hasKey(e,GLOBAL_EVENT_KEYS.ARROW_DOWN)){for(let e=s+1;e<i.length;e++)if(!1!==i[e].visible){u=i[e];break}t[l]=u||i[0]}else t[l]&&(globalEvents.hasKey(e,GLOBAL_EVENT_KEYS.ENTER)||globalEvents.hasKey(e,GLOBAL_EVENT_KEYS.SPACEBAR))&&b.ctxMenuLinkEvent(e,t[l])},handleOpenMenuEvent:L,handleGlobalContextmenuEvent(t){var{mouseConfig:e,menuConfig:l}=p,{editStore:n,ctxMenuStore:o}=M,i=E.visibleColumn,u=_.value,s=C.value,r=T.value,a=y.value,d=x.value,n=n.selected,c=["header","body","footer"];if((0,_utils.isEnableConf)(l)){if(o.visible&&s&&(0,_dom.getEventTargetNode)(t,s.getRefMaps().refElem.value).flag)return void t.preventDefault();if(E._keyCtx){l="body",o={type:l,$table:b,keyboard:!0,columns:i.slice(0),$event:t};if(e&&r.area){s=b.getActiveCellArea();if(s&&s.row&&s.column)return o.row=s.row,o.column=s.column,void L(t,l,o)}else if(e&&r.selected&&n.row&&n.column)return o.row=n.row,o.column=n.column,void L(t,l,o)}for(let e=0;e<c.length;e++){var v=c[e],f=(0,_dom.getEventTargetNode)(t,d,`vxe-${v}--column`,e=>e.parentNode.parentNode.parentNode.getAttribute("xid")===m),g={type:v,$table:b,columns:i.slice(0),$event:t};if(f.flag){var f=f.targetElem,h=b.getColumnNode(f),h=h?h.item:null;let e=v+"-";h&&Object.assign(g,{column:h,columnIndex:b.getColumnIndex(h),cell:f}),"body"===v&&(f=(h=b.getRowNode(f.parentNode))?h.item:null,e="",f)&&(g.row=f,g.rowIndex=b.getRowIndex(f));h=e+"cell-menu";return L(t,v,g),void b.dispatchEvent(h,g,t)}if((0,_dom.getEventTargetNode)(t,d,`vxe-table--${v}-wrapper`,e=>e.getAttribute("xid")===m).flag)return void("cell"===a.trigger?t.preventDefault():L(t,v,g))}}u&&!(0,_dom.getEventTargetNode)(t,u.getRefMaps().refElem.value).flag&&b.closeFilter(),w.closeMenu()},ctxMenuMouseoverEvent(e,t,l){let a=e.currentTarget;var n=M.ctxMenuStore;e.preventDefault(),e.stopPropagation(),n.selected=t,(n.selectChild=l)||(n.showChild=(0,_utils.hasChildrenList)(t),n.showChild&&(0,_vue.nextTick)(()=>{var o=a.nextElementSibling;if(o){var{boundingTop:i,boundingLeft:u,visibleHeight:s,visibleWidth:r}=(0,_dom.getAbsolutePos)(a),i=i+a.offsetHeight;let e="",t="",l=(u+a.offsetWidth+o.offsetWidth>r-10&&(e="auto",t=a.offsetWidth+"px"),""),n="";i+o.offsetHeight>s-10&&(l="auto",n="0"),o.style.left=e,o.style.right=t,o.style.top=l,o.style.bottom=n}}))},ctxMenuMouseoutEvent(e,t){var l=M.ctxMenuStore;t.children||(l.selected=null),l.selectChild=null},ctxMenuLinkEvent(e,t){var l,n=b.xeGrid;t.disabled||!t.code&&t.children&&t.children.length||(l=menus.get(t.code),t=Object.assign({},E._currMenuParams,{menu:t,$table:b,$grid:n,$event:e}),(n=l?l.tableMenuMethod||l.menuMethod:null)&&n(t,e),b.dispatchEvent("menu-click",t,e),w.closeMenu())}},Object.assign(Object.assign({},w),e)},setupGrid(e){return e.extendTableMethods(tableMenuMethodKeys)}});