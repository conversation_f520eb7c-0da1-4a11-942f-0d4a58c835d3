{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/swift/swift.js"], "sourcesContent": ["/*!---------------------------------------------------------------------------------------------\n *  Copyright (C) <PERSON>, owensd.io. All rights reserved.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    comments: {\n        lineComment: '//',\n        blockComment: ['/*', '*/']\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" },\n        { open: '`', close: '`' }\n    ],\n    surroundingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" },\n        { open: '`', close: '`' }\n    ]\n};\nexport var language = {\n    defaultToken: '',\n    tokenPostfix: '.swift',\n    // TODO(owensd): Support the full range of unicode valid identifiers.\n    identifier: /[a-zA-Z_][\\w$]*/,\n    // TODO(owensd): Support the @availability macro properly.\n    attributes: [\n        '@autoclosure',\n        '@noescape',\n        '@noreturn',\n        '@NSApplicationMain',\n        '@NSCopying',\n        '@NSManaged',\n        '@objc',\n        '@UIApplicationMain',\n        '@noreturn',\n        '@availability',\n        '@IBAction',\n        '@IBDesignable',\n        '@IBInspectable',\n        '@IBOutlet'\n    ],\n    accessmodifiers: ['public', 'private', 'fileprivate', 'internal'],\n    keywords: [\n        '__COLUMN__',\n        '__FILE__',\n        '__FUNCTION__',\n        '__LINE__',\n        'as',\n        'as!',\n        'as?',\n        'associativity',\n        'break',\n        'case',\n        'catch',\n        'class',\n        'continue',\n        'convenience',\n        'default',\n        'deinit',\n        'didSet',\n        'do',\n        'dynamic',\n        'dynamicType',\n        'else',\n        'enum',\n        'extension',\n        'fallthrough',\n        'fileprivate',\n        'final',\n        'for',\n        'func',\n        'get',\n        'guard',\n        'if',\n        'import',\n        'in',\n        'infix',\n        'init',\n        'inout',\n        'internal',\n        'is',\n        'lazy',\n        'left',\n        'let',\n        'mutating',\n        'nil',\n        'none',\n        'nonmutating',\n        'operator',\n        'optional',\n        'override',\n        'postfix',\n        'precedence',\n        'prefix',\n        'private',\n        'protocol',\n        'Protocol',\n        'public',\n        'repeat',\n        'required',\n        'return',\n        'right',\n        'self',\n        'Self',\n        'set',\n        'static',\n        'struct',\n        'subscript',\n        'super',\n        'switch',\n        'throw',\n        'throws',\n        'try',\n        'try!',\n        'Type',\n        'typealias',\n        'unowned',\n        'var',\n        'weak',\n        'where',\n        'while',\n        'willSet',\n        'FALSE',\n        'TRUE'\n    ],\n    symbols: /[=(){}\\[\\].,:;@#\\_&\\-<>`?!+*\\\\\\/]/,\n    // Moved . to operatorstart so it can be a delimiter\n    operatorstart: /[\\/=\\-+!*%<>&|^~?\\u00A1-\\u00A7\\u00A9\\u00AB\\u00AC\\u00AE\\u00B0-\\u00B1\\u00B6\\u00BB\\u00BF\\u00D7\\u00F7\\u2016-\\u2017\\u2020-\\u2027\\u2030-\\u203E\\u2041-\\u2053\\u2055-\\u205E\\u2190-\\u23FF\\u2500-\\u2775\\u2794-\\u2BFF\\u2E00-\\u2E7F\\u3001-\\u3003\\u3008-\\u3030]/,\n    operatorend: /[\\u0300-\\u036F\\u1DC0-\\u1DFF\\u20D0-\\u20FF\\uFE00-\\uFE0F\\uFE20-\\uFE2F\\uE0100-\\uE01EF]/,\n    operators: /(@operatorstart)((@operatorstart)|(@operatorend))*/,\n    // TODO(owensd): These are borrowed from C#; need to validate correctness for Swift.\n    escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n    tokenizer: {\n        root: [\n            { include: '@whitespace' },\n            { include: '@comment' },\n            { include: '@attribute' },\n            { include: '@literal' },\n            { include: '@keyword' },\n            { include: '@invokedmethod' },\n            { include: '@symbol' }\n        ],\n        whitespace: [\n            [/\\s+/, 'white'],\n            [/\"\"\"/, 'string.quote', '@endDblDocString']\n        ],\n        endDblDocString: [\n            [/[^\"]+/, 'string'],\n            [/\\\\\"/, 'string'],\n            [/\"\"\"/, 'string.quote', '@popall'],\n            [/\"/, 'string']\n        ],\n        symbol: [\n            [/[{}()\\[\\]]/, '@brackets'],\n            [/[<>](?!@symbols)/, '@brackets'],\n            [/[.]/, 'delimiter'],\n            [/@operators/, 'operator'],\n            [/@symbols/, 'operator']\n        ],\n        comment: [\n            [/\\/\\/\\/.*$/, 'comment.doc'],\n            [/\\/\\*\\*/, 'comment.doc', '@commentdocbody'],\n            [/\\/\\/.*$/, 'comment'],\n            [/\\/\\*/, 'comment', '@commentbody']\n        ],\n        commentdocbody: [\n            [/\\/\\*/, 'comment', '@commentbody'],\n            [/\\*\\//, 'comment.doc', '@pop'],\n            [/\\:[a-zA-Z]+\\:/, 'comment.doc.param'],\n            [/./, 'comment.doc']\n        ],\n        commentbody: [\n            [/\\/\\*/, 'comment', '@commentbody'],\n            [/\\*\\//, 'comment', '@pop'],\n            [/./, 'comment']\n        ],\n        attribute: [\n            [\n                /@@@identifier/,\n                {\n                    cases: {\n                        '@attributes': 'keyword.control',\n                        '@default': ''\n                    }\n                }\n            ]\n        ],\n        literal: [\n            [/\"/, { token: 'string.quote', next: '@stringlit' }],\n            [/0[b]([01]_?)+/, 'number.binary'],\n            [/0[o]([0-7]_?)+/, 'number.octal'],\n            [/0[x]([0-9a-fA-F]_?)+([pP][\\-+](\\d_?)+)?/, 'number.hex'],\n            [/(\\d_?)*\\.(\\d_?)+([eE][\\-+]?(\\d_?)+)?/, 'number.float'],\n            [/(\\d_?)+/, 'number']\n        ],\n        stringlit: [\n            [/\\\\\\(/, { token: 'operator', next: '@interpolatedexpression' }],\n            [/@escapes/, 'string'],\n            [/\\\\./, 'string.escape.invalid'],\n            [/\"/, { token: 'string.quote', next: '@pop' }],\n            [/./, 'string']\n        ],\n        interpolatedexpression: [\n            [/\\(/, { token: 'operator', next: '@interpolatedexpression' }],\n            [/\\)/, { token: 'operator', next: '@pop' }],\n            { include: '@literal' },\n            { include: '@keyword' },\n            { include: '@symbol' }\n        ],\n        keyword: [\n            [/`/, { token: 'operator', next: '@escapedkeyword' }],\n            [\n                /@identifier/,\n                {\n                    cases: {\n                        '@keywords': 'keyword',\n                        '[A-Z][a-zA-Z0-9$]*': 'type.identifier',\n                        '@default': 'identifier'\n                    }\n                }\n            ]\n        ],\n        escapedkeyword: [\n            [/`/, { token: 'operator', next: '@pop' }],\n            [/./, 'identifier']\n        ],\n        //\t\tsymbol: [\n        //\t\t\t[ /@symbols/, 'operator' ],\n        //\t\t\t[ /@operators/, 'operator' ]\n        //\t\t],\n        invokedmethod: [\n            [\n                /([.])(@identifier)/,\n                {\n                    cases: {\n                        $2: ['delimeter', 'type.identifier'],\n                        '@default': ''\n                    }\n                }\n            ]\n        ]\n    }\n};\n"], "mappings": ";;;AAGO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC7B;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA;AAAA,EAEd,YAAY;AAAA;AAAA,EAEZ,YAAY;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,iBAAiB,CAAC,UAAU,WAAW,eAAe,UAAU;AAAA,EAChE,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA;AAAA,EAET,eAAe;AAAA,EACf,aAAa;AAAA,EACb,WAAW;AAAA;AAAA,EAEX,SAAS;AAAA,EACT,WAAW;AAAA,IACP,MAAM;AAAA,MACF,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,aAAa;AAAA,MACxB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,iBAAiB;AAAA,MAC5B,EAAE,SAAS,UAAU;AAAA,IACzB;AAAA,IACA,YAAY;AAAA,MACR,CAAC,OAAO,OAAO;AAAA,MACf,CAAC,OAAO,gBAAgB,kBAAkB;AAAA,IAC9C;AAAA,IACA,iBAAiB;AAAA,MACb,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,OAAO,QAAQ;AAAA,MAChB,CAAC,OAAO,gBAAgB,SAAS;AAAA,MACjC,CAAC,KAAK,QAAQ;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACJ,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,oBAAoB,WAAW;AAAA,MAChC,CAAC,OAAO,WAAW;AAAA,MACnB,CAAC,cAAc,UAAU;AAAA,MACzB,CAAC,YAAY,UAAU;AAAA,IAC3B;AAAA,IACA,SAAS;AAAA,MACL,CAAC,aAAa,aAAa;AAAA,MAC3B,CAAC,UAAU,eAAe,iBAAiB;AAAA,MAC3C,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,QAAQ,WAAW,cAAc;AAAA,IACtC;AAAA,IACA,gBAAgB;AAAA,MACZ,CAAC,QAAQ,WAAW,cAAc;AAAA,MAClC,CAAC,QAAQ,eAAe,MAAM;AAAA,MAC9B,CAAC,iBAAiB,mBAAmB;AAAA,MACrC,CAAC,KAAK,aAAa;AAAA,IACvB;AAAA,IACA,aAAa;AAAA,MACT,CAAC,QAAQ,WAAW,cAAc;AAAA,MAClC,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,KAAK,SAAS;AAAA,IACnB;AAAA,IACA,WAAW;AAAA,MACP;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,eAAe;AAAA,YACf,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,aAAa,CAAC;AAAA,MACnD,CAAC,iBAAiB,eAAe;AAAA,MACjC,CAAC,kBAAkB,cAAc;AAAA,MACjC,CAAC,2CAA2C,YAAY;AAAA,MACxD,CAAC,wCAAwC,cAAc;AAAA,MACvD,CAAC,WAAW,QAAQ;AAAA,IACxB;AAAA,IACA,WAAW;AAAA,MACP,CAAC,QAAQ,EAAE,OAAO,YAAY,MAAM,0BAA0B,CAAC;AAAA,MAC/D,CAAC,YAAY,QAAQ;AAAA,MACrB,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,OAAO,CAAC;AAAA,MAC7C,CAAC,KAAK,QAAQ;AAAA,IAClB;AAAA,IACA,wBAAwB;AAAA,MACpB,CAAC,MAAM,EAAE,OAAO,YAAY,MAAM,0BAA0B,CAAC;AAAA,MAC7D,CAAC,MAAM,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,MAC1C,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,UAAU;AAAA,IACzB;AAAA,IACA,SAAS;AAAA,MACL,CAAC,KAAK,EAAE,OAAO,YAAY,MAAM,kBAAkB,CAAC;AAAA,MACpD;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,aAAa;AAAA,YACb,sBAAsB;AAAA,YACtB,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,gBAAgB;AAAA,MACZ,CAAC,KAAK,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,MACzC,CAAC,KAAK,YAAY;AAAA,IACtB;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,eAAe;AAAA,MACX;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,IAAI,CAAC,aAAa,iBAAiB;AAAA,YACnC,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;", "names": []}