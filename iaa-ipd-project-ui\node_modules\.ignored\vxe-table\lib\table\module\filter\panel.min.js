Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var _vue=require("vue"),_comp=require("../../../ui/src/comp"),_ui=require("../../../ui"),_utils=require("../../../ui/src/utils"),_dom=require("../../../ui/src/dom"),_vn=require("../../../ui/src/vn"),_xeUtils=_interopRequireDefault(require("xe-utils"));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let{getI18n,getIcon,renderer}=_ui.VxeUI;var _default=exports.default=(0,_comp.defineVxeComponent)({name:"VxeTableFilterPanel",props:{filterStore:Object},setup(p,e){var t=_xeUtils.default.uniqueId();let _=(0,_vue.inject)("$xeTable",{}),{reactData:h,internalData:m,getComputeMaps:l}=_,f=l().computeFilterOpts,b=(0,_vue.ref)(),r={refElem:b},x={xID:t,props:p,context:e,getRefMaps:()=>r},C=(0,_vue.computed)(()=>{var e=p.filterStore;return e&&e.options.some(e=>e.checked)}),i=(e,t)=>{var l=p.filterStore;l.options.forEach(e=>{e._checked=t,e.checked=t}),l.isAllSelected=t,l.isIndeterminate=!1},g=e=>{_.handleFilterConfirmFilter(e)};let E=e=>{_.handleFilterResetFilter(e)};let F=(e,t,l)=>{_.handleFilterChangeOption(e,t,l)},I=(e,t)=>{var l=p.filterStore;l.multiple?i(0,t):E(e)};t={changeRadioOption:(e,t,l)=>{_.handleFilterChangeRadioOption(e,t,l)},changeMultipleOption:(e,t,l)=>{_.handleFilterChangeMultipleOption(e,t,l)},changeAllOption:I,changeOption:F,confirmFilter:g,resetFilter:E};Object.assign(x,t);return x.renderVN=()=>{var e=p.filterStore,t=h.initStore,{visible:l,multiple:r,column:i}=e,a=i?i.filterRender:null,n=(0,_utils.isEnableConf)(a)?renderer.get(a.name):null,s=n?n.tableFilterClassName||n.filterClassName:"",o=Object.assign({},m._currFilterParams,{$panel:x,$table:_}),u=_.props,c=_.getComputeMaps().computeSize,c=c.value,{transfer:d,destroyOnClose:v}=f.value;return(0,_vue.h)(_vue.Teleport,{to:"body",disabled:!d},[(0,_vue.h)("div",{ref:b,class:["vxe-table--filter-wrapper","filter--prevent-default",(0,_dom.getPropClass)(s,o),{["size--"+c]:c,"is--animat":u.animat,"is--multiple":r,"is--active":l}],style:e.style},t.filter&&(!v||l)&&i?((e,t)=>{let l=p.filterStore,{column:r,multiple:i,maxHeight:a}=l;var n=r?r.slots:null,n=n?n.filter:null,s=Object.assign({},m._currFilterParams,{$panel:x,$table:_}),t=t?t.renderTableFilter||t.renderFilter:null;return n?[(0,_vue.h)("div",{class:"vxe-table--filter-template",style:a?{maxHeight:a+"px"}:{}},_.callSlot(n,s))]:t?[(0,_vue.h)("div",{class:"vxe-table--filter-template",style:a?{maxHeight:a+"px"}:{}},(0,_vn.getSlotVNs)(t(e,s)))]:(n=i?l.isAllSelected:!l.options.some(e=>e._checked),t=i&&l.isIndeterminate,[(0,_vue.h)("ul",{class:"vxe-table--filter-header"},[(0,_vue.h)("li",{class:["vxe-table--filter-option",{"is--checked":n,"is--indeterminate":t}],title:getI18n(i?"vxe.table.allTitle":"vxe.table.allFilter"),onClick:e=>{I(e,!l.isAllSelected)}},(i?[(0,_vue.h)("span",{class:["vxe-checkbox--icon",t?getIcon().TABLE_CHECKBOX_INDETERMINATE:n?getIcon().TABLE_CHECKBOX_CHECKED:getIcon().TABLE_CHECKBOX_UNCHECKED]})]:[]).concat([(0,_vue.h)("span",{class:"vxe-checkbox--label"},getI18n("vxe.table.allFilter"))]))]),(0,_vue.h)("ul",{class:"vxe-table--filter-body",style:a?{maxHeight:a+"px"}:{}},l.options.map(t=>{var e=t._checked;return(0,_vue.h)("li",{class:["vxe-table--filter-option",{"is--checked":t._checked}],title:t.label,onClick:e=>{F(e,!t._checked,t)}},(i?[(0,_vue.h)("span",{class:["vxe-checkbox--icon",e?getIcon().TABLE_CHECKBOX_CHECKED:getIcon().TABLE_CHECKBOX_UNCHECKED]})]:[]).concat([(0,_vue.h)("span",{class:"vxe-checkbox--label"},(0,_utils.formatText)(t.label,1))]))}))])})(a,n).concat((()=>{var e=p.filterStore,{column:t,multiple:l}=e,r=f.value,i=C.value,t=t.filterRender,t=(0,_utils.isEnableConf)(t)?renderer.get(t.name):null,i=!i&&!e.isAllSelected&&!e.isIndeterminate;return l&&(!t||!1!==t.showTableFilterFooter&&!1!==t.showFilterFooter&&!1!==t.isFooter)?[(0,_vue.h)("div",{class:"vxe-table--filter-footer"},[(0,_vue.h)("button",{class:{"is--disabled":i},disabled:i,onClick:g},r.confirmButtonText||getI18n("vxe.table.confirmFilter")),(0,_vue.h)("button",{onClick:E},r.resetButtonText||getI18n("vxe.table.resetFilter"))])]:[]})()):[])])},x},render(){return this.renderVN()}});