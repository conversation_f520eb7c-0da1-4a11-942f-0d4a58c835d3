import {
  MobXProviderContext,
  ObserverComponent,
  PropTypes,
  Provider,
  disposeOnUnmount,
  enableStaticRendering,
  inject,
  isUsingStaticRendering,
  observer,
  observerBatching,
  useAsObservableSource,
  useLocalObservable,
  useLocalStore,
  useObserver,
  useStaticRendering
} from "./chunk-VWXSHSCS.js";
import "./chunk-FPQZHXQV.js";
import "./chunk-7XBFYOTW.js";
import "./chunk-ZUWHLQVA.js";
import "./chunk-GFT2G5UO.js";
export {
  MobXProviderContext,
  ObserverComponent as Observer,
  PropTypes,
  Provider,
  disposeOnUnmount,
  enableStaticRendering,
  inject,
  isUsingStaticRendering,
  observer,
  observerBatching,
  useAsObservableSource,
  useLocalObservable,
  useLocalStore,
  useObserver,
  useStaticRendering
};
//# sourceMappingURL=mobx-react.js.map
