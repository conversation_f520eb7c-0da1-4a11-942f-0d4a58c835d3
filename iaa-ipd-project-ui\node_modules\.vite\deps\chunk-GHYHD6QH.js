import {
  $mobx,
  ObservableMap,
  action,
  allowStateChangesInsideComputed,
  computed,
  createAtom,
  entries,
  getAdministration,
  getAtom,
  intercept,
  interceptReads,
  isComputedProp,
  isObservableArray,
  observable,
  observe,
  reaction,
  set,
  values
} from "./chunk-ECE3U5IW.js";

// node_modules/amis/node_modules/mobx-state-tree/dist/mobx-state-tree.module.js
var livelinessChecking = "warn";
function getLivelinessChecking() {
  return livelinessChecking;
}
var Hook;
(function(Hook2) {
  Hook2["afterCreate"] = "afterCreate";
  Hook2["afterAttach"] = "afterAttach";
  Hook2["afterCreationFinalization"] = "afterCreationFinalization";
  Hook2["beforeDetach"] = "beforeDetach";
  Hook2["beforeDestroy"] = "beforeDestroy";
})(Hook || (Hook = {}));
var extendStatics = function(d, b) {
  extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
    d2.__proto__ = b2;
  } || function(d2, b2) {
    for (var p in b2)
      if (b2.hasOwnProperty(p))
        d2[p] = b2[p];
  };
  return extendStatics(d, b);
};
function __extends(d, b) {
  extendStatics(d, b);
  function __() {
    this.constructor = d;
  }
  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
}
var __assign = function() {
  __assign = Object.assign || function __assign2(t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];
      for (var p in s)
        if (Object.prototype.hasOwnProperty.call(s, p))
          t[p] = s[p];
    }
    return t;
  };
  return __assign.apply(this, arguments);
};
function __rest(s, e) {
  var t = {};
  for (var p in s)
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
      t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function")
    for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
      if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
        t[p[i]] = s[p[i]];
    }
  return t;
}
function __decorate(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function __values(o) {
  var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
  if (m)
    return m.call(o);
  if (o && typeof o.length === "number")
    return {
      next: function() {
        if (o && i >= o.length)
          o = void 0;
        return { value: o && o[i++], done: !o };
      }
    };
  throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
}
function __read(o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m)
    return o;
  var i = m.call(o), r, ar = [], e;
  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done)
      ar.push(r.value);
  } catch (error) {
    e = { error };
  } finally {
    try {
      if (r && !r.done && (m = i["return"]))
        m.call(i);
    } finally {
      if (e)
        throw e.error;
    }
  }
  return ar;
}
function __spread() {
  for (var ar = [], i = 0; i < arguments.length; i++)
    ar = ar.concat(__read(arguments[i]));
  return ar;
}
function getType(object) {
  assertIsStateTreeNode(object, 1);
  return getStateTreeNode(object).type;
}
function applyPatch(target, patch) {
  assertIsStateTreeNode(target, 1);
  assertArg(patch, function(p) {
    return typeof p === "object";
  }, "object or array", 2);
  getStateTreeNode(target).applyPatches(asArray(patch));
}
function getSnapshot(target, applyPostProcess) {
  if (applyPostProcess === void 0) {
    applyPostProcess = true;
  }
  assertIsStateTreeNode(target, 1);
  var node = getStateTreeNode(target);
  if (applyPostProcess)
    return node.snapshot;
  return freeze(node.type.getSnapshot(node, false));
}
function getRoot(target) {
  assertIsStateTreeNode(target, 1);
  return getStateTreeNode(target).root.storedValue;
}
function getPath(target) {
  assertIsStateTreeNode(target, 1);
  return getStateTreeNode(target).path;
}
function getIdentifier(target) {
  assertIsStateTreeNode(target, 1);
  return getStateTreeNode(target).identifier;
}
function clone(source, keepEnvironment) {
  if (keepEnvironment === void 0) {
    keepEnvironment = true;
  }
  assertIsStateTreeNode(source, 1);
  var node = getStateTreeNode(source);
  return node.type.create(node.snapshot, keepEnvironment === true ? node.root.environment : keepEnvironment === false ? void 0 : keepEnvironment);
}
function destroy(target) {
  assertIsStateTreeNode(target, 1);
  var node = getStateTreeNode(target);
  if (node.isRoot)
    node.die();
  else
    node.parent.removeChild(node.subpath);
}
function isAlive(target) {
  assertIsStateTreeNode(target, 1);
  return getStateTreeNode(target).observableIsAlive;
}
var BaseNode = (
  /** @class */
  function() {
    function BaseNode2(type, parent, subpath, environment) {
      this.type = type;
      this.environment = environment;
      this._state = NodeLifeCycle.INITIALIZING;
      this.environment = environment;
      this.baseSetParent(parent, subpath);
    }
    Object.defineProperty(BaseNode2.prototype, "subpath", {
      get: function() {
        return this._subpath;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(BaseNode2.prototype, "subpathUponDeath", {
      get: function() {
        return this._subpathUponDeath;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(BaseNode2.prototype, "pathUponDeath", {
      get: function() {
        return this._pathUponDeath;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(BaseNode2.prototype, "value", {
      get: function() {
        return this.type.getValue(this);
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(BaseNode2.prototype, "state", {
      get: function() {
        return this._state;
      },
      set: function(val) {
        var wasAlive = this.isAlive;
        this._state = val;
        var isAlive2 = this.isAlive;
        if (this.aliveAtom && wasAlive !== isAlive2) {
          this.aliveAtom.reportChanged();
        }
      },
      enumerable: false,
      configurable: true
    });
    BaseNode2.prototype.fireInternalHook = function(name) {
      if (this._hookSubscribers) {
        this._hookSubscribers.emit(name, this, name);
      }
    };
    BaseNode2.prototype.registerHook = function(hook, hookHandler) {
      if (!this._hookSubscribers) {
        this._hookSubscribers = new EventHandlers();
      }
      return this._hookSubscribers.register(hook, hookHandler);
    };
    Object.defineProperty(BaseNode2.prototype, "parent", {
      get: function() {
        return this._parent;
      },
      enumerable: false,
      configurable: true
    });
    BaseNode2.prototype.baseSetParent = function(parent, subpath) {
      this._parent = parent;
      this._subpath = subpath;
      this._escapedSubpath = void 0;
      if (this.pathAtom) {
        this.pathAtom.reportChanged();
      }
    };
    Object.defineProperty(BaseNode2.prototype, "path", {
      /*
       * Returns (escaped) path representation as string
       */
      get: function() {
        return this.getEscapedPath(true);
      },
      enumerable: false,
      configurable: true
    });
    BaseNode2.prototype.getEscapedPath = function(reportObserved) {
      if (reportObserved) {
        if (!this.pathAtom) {
          this.pathAtom = createAtom("path");
        }
        this.pathAtom.reportObserved();
      }
      if (!this.parent)
        return "";
      if (this._escapedSubpath === void 0) {
        this._escapedSubpath = !this._subpath ? "" : escapeJsonPath(this._subpath);
      }
      return this.parent.getEscapedPath(reportObserved) + "/" + this._escapedSubpath;
    };
    Object.defineProperty(BaseNode2.prototype, "isRoot", {
      get: function() {
        return this.parent === null;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(BaseNode2.prototype, "isAlive", {
      get: function() {
        return this.state !== NodeLifeCycle.DEAD;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(BaseNode2.prototype, "isDetaching", {
      get: function() {
        return this.state === NodeLifeCycle.DETACHING;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(BaseNode2.prototype, "observableIsAlive", {
      get: function() {
        if (!this.aliveAtom) {
          this.aliveAtom = createAtom("alive");
        }
        this.aliveAtom.reportObserved();
        return this.isAlive;
      },
      enumerable: false,
      configurable: true
    });
    BaseNode2.prototype.baseFinalizeCreation = function(whenFinalized) {
      if (devMode()) {
        if (!this.isAlive) {
          throw fail("assertion failed: cannot finalize the creation of a node that is already dead");
        }
      }
      if (this.state === NodeLifeCycle.CREATED) {
        if (this.parent) {
          if (this.parent.state !== NodeLifeCycle.FINALIZED) {
            return;
          }
          this.fireHook(Hook.afterAttach);
        }
        this.state = NodeLifeCycle.FINALIZED;
        if (whenFinalized) {
          whenFinalized();
        }
      }
    };
    BaseNode2.prototype.baseFinalizeDeath = function() {
      if (this._hookSubscribers) {
        this._hookSubscribers.clearAll();
      }
      this._subpathUponDeath = this._subpath;
      this._pathUponDeath = this.getEscapedPath(false);
      this.baseSetParent(null, "");
      this.state = NodeLifeCycle.DEAD;
    };
    BaseNode2.prototype.baseAboutToDie = function() {
      this.fireHook(Hook.beforeDestroy);
    };
    return BaseNode2;
  }()
);
var ScalarNode = (
  /** @class */
  function(_super) {
    __extends(ScalarNode2, _super);
    function ScalarNode2(simpleType, parent, subpath, environment, initialSnapshot) {
      var _this = _super.call(this, simpleType, parent, subpath, environment) || this;
      try {
        _this.storedValue = simpleType.createNewInstance(initialSnapshot);
      } catch (e) {
        _this.state = NodeLifeCycle.DEAD;
        throw e;
      }
      _this.state = NodeLifeCycle.CREATED;
      _this.finalizeCreation();
      return _this;
    }
    Object.defineProperty(ScalarNode2.prototype, "root", {
      get: function() {
        if (!this.parent)
          throw fail$1("This scalar node is not part of a tree");
        return this.parent.root;
      },
      enumerable: false,
      configurable: true
    });
    ScalarNode2.prototype.setParent = function(newParent, subpath) {
      var parentChanged = this.parent !== newParent;
      var subpathChanged = this.subpath !== subpath;
      if (!parentChanged && !subpathChanged) {
        return;
      }
      if (devMode()) {
        if (!subpath) {
          throw fail$1("assertion failed: subpath expected");
        }
        if (!newParent) {
          throw fail$1("assertion failed: parent expected");
        }
        if (parentChanged) {
          throw fail$1("assertion failed: scalar nodes cannot change their parent");
        }
      }
      this.environment = void 0;
      this.baseSetParent(this.parent, subpath);
    };
    Object.defineProperty(ScalarNode2.prototype, "snapshot", {
      get: function() {
        return freeze(this.getSnapshot());
      },
      enumerable: false,
      configurable: true
    });
    ScalarNode2.prototype.getSnapshot = function() {
      return this.type.getSnapshot(this);
    };
    ScalarNode2.prototype.toString = function() {
      var path = (this.isAlive ? this.path : this.pathUponDeath) || "<root>";
      return this.type.name + "@" + path + (this.isAlive ? "" : " [dead]");
    };
    ScalarNode2.prototype.die = function() {
      if (!this.isAlive || this.state === NodeLifeCycle.DETACHING)
        return;
      this.aboutToDie();
      this.finalizeDeath();
    };
    ScalarNode2.prototype.finalizeCreation = function() {
      this.baseFinalizeCreation();
    };
    ScalarNode2.prototype.aboutToDie = function() {
      this.baseAboutToDie();
    };
    ScalarNode2.prototype.finalizeDeath = function() {
      this.baseFinalizeDeath();
    };
    ScalarNode2.prototype.fireHook = function(name) {
      this.fireInternalHook(name);
    };
    __decorate([
      action
    ], ScalarNode2.prototype, "die", null);
    return ScalarNode2;
  }(BaseNode)
);
var nextNodeId = 1;
var snapshotReactionOptions = {
  onError: function(e) {
    throw e;
  }
};
var ObjectNode = (
  /** @class */
  function(_super) {
    __extends(ObjectNode2, _super);
    function ObjectNode2(complexType, parent, subpath, environment, initialValue) {
      var _this = _super.call(this, complexType, parent, subpath, environment) || this;
      _this.nodeId = ++nextNodeId;
      _this.isProtectionEnabled = true;
      _this._autoUnbox = true;
      _this._isRunningAction = false;
      _this._hasSnapshotReaction = false;
      _this._observableInstanceState = 0;
      _this._cachedInitialSnapshotCreated = false;
      _this.unbox = _this.unbox.bind(_this);
      _this._initialSnapshot = freeze(initialValue);
      _this.identifierAttribute = complexType.identifierAttribute;
      if (!parent) {
        _this.identifierCache = new IdentifierCache();
      }
      _this._childNodes = complexType.initializeChildNodes(_this, _this._initialSnapshot);
      _this.identifier = null;
      _this.unnormalizedIdentifier = null;
      if (_this.identifierAttribute && _this._initialSnapshot) {
        var id = _this._initialSnapshot[_this.identifierAttribute];
        if (id === void 0) {
          var childNode = _this._childNodes[_this.identifierAttribute];
          if (childNode) {
            id = childNode.value;
          }
        }
        if (typeof id !== "string" && typeof id !== "number") {
          throw fail$1("Instance identifier '" + _this.identifierAttribute + "' for type '" + _this.type.name + "' must be a string or a number");
        }
        _this.identifier = normalizeIdentifier(id);
        _this.unnormalizedIdentifier = id;
      }
      if (!parent) {
        _this.identifierCache.addNodeToCache(_this);
      } else {
        parent.root.identifierCache.addNodeToCache(_this);
      }
      return _this;
    }
    ObjectNode2.prototype.applyPatches = function(patches) {
      this.createObservableInstanceIfNeeded();
      this._applyPatches(patches);
    };
    ObjectNode2.prototype.applySnapshot = function(snapshot) {
      this.createObservableInstanceIfNeeded();
      this._applySnapshot(snapshot);
    };
    ObjectNode2.prototype.createObservableInstanceIfNeeded = function() {
      if (this._observableInstanceState === 0) {
        this.createObservableInstance();
      }
    };
    ObjectNode2.prototype.createObservableInstance = function() {
      var e_1, _a;
      if (devMode()) {
        if (this.state !== NodeLifeCycle.INITIALIZING) {
          throw fail$1("assertion failed: the creation of the observable instance must be done on the initializing phase");
        }
      }
      this._observableInstanceState = 1;
      var parentChain = [];
      var parent = this.parent;
      while (parent && parent._observableInstanceState === 0) {
        parentChain.unshift(parent);
        parent = parent.parent;
      }
      try {
        for (var parentChain_1 = __values(parentChain), parentChain_1_1 = parentChain_1.next(); !parentChain_1_1.done; parentChain_1_1 = parentChain_1.next()) {
          var p = parentChain_1_1.value;
          p.createObservableInstanceIfNeeded();
        }
      } catch (e_1_1) {
        e_1 = { error: e_1_1 };
      } finally {
        try {
          if (parentChain_1_1 && !parentChain_1_1.done && (_a = parentChain_1.return))
            _a.call(parentChain_1);
        } finally {
          if (e_1)
            throw e_1.error;
        }
      }
      var type = this.type;
      try {
        this.storedValue = type.createNewInstance(this._childNodes);
        this.preboot();
        this._isRunningAction = true;
        type.finalizeNewInstance(this, this.storedValue);
      } catch (e) {
        this.state = NodeLifeCycle.DEAD;
        throw e;
      } finally {
        this._isRunningAction = false;
      }
      this._observableInstanceState = 2;
      invalidateComputed(this, "snapshot");
      if (this.isRoot)
        this._addSnapshotReaction();
      this._childNodes = EMPTY_OBJECT;
      this.state = NodeLifeCycle.CREATED;
      this.fireHook(Hook.afterCreate);
      this.finalizeCreation();
    };
    Object.defineProperty(ObjectNode2.prototype, "root", {
      get: function() {
        var parent = this.parent;
        return parent ? parent.root : this;
      },
      enumerable: false,
      configurable: true
    });
    ObjectNode2.prototype.clearParent = function() {
      if (!this.parent)
        return;
      this.fireHook(Hook.beforeDetach);
      var previousState = this.state;
      this.state = NodeLifeCycle.DETACHING;
      var root = this.root;
      var newEnv = root.environment;
      var newIdCache = root.identifierCache.splitCache(this);
      try {
        this.parent.removeChild(this.subpath);
        this.baseSetParent(null, "");
        this.environment = newEnv;
        this.identifierCache = newIdCache;
      } finally {
        this.state = previousState;
      }
    };
    ObjectNode2.prototype.setParent = function(newParent, subpath) {
      var parentChanged = newParent !== this.parent;
      var subpathChanged = subpath !== this.subpath;
      if (!parentChanged && !subpathChanged) {
        return;
      }
      if (devMode()) {
        if (!subpath) {
          throw fail$1("assertion failed: subpath expected");
        }
        if (!newParent) {
          throw fail$1("assertion failed: new parent expected");
        }
        if (this.parent && parentChanged) {
          throw fail$1("A node cannot exists twice in the state tree. Failed to add " + this + " to path '" + newParent.path + "/" + subpath + "'.");
        }
        if (!this.parent && newParent.root === this) {
          throw fail$1("A state tree is not allowed to contain itself. Cannot assign " + this + " to path '" + newParent.path + "/" + subpath + "'");
        }
        if (!this.parent && !!this.environment && this.environment !== newParent.root.environment) {
          throw fail$1("A state tree cannot be made part of another state tree as long as their environments are different.");
        }
      }
      if (parentChanged) {
        this.environment = void 0;
        newParent.root.identifierCache.mergeCache(this);
        this.baseSetParent(newParent, subpath);
        this.fireHook(Hook.afterAttach);
      } else if (subpathChanged) {
        this.baseSetParent(this.parent, subpath);
      }
    };
    ObjectNode2.prototype.fireHook = function(name) {
      var _this = this;
      this.fireInternalHook(name);
      var fn = this.storedValue && typeof this.storedValue === "object" && this.storedValue[name];
      if (typeof fn === "function") {
        if (allowStateChangesInsideComputed) {
          allowStateChangesInsideComputed(function() {
            fn.apply(_this.storedValue);
          });
        } else {
          fn.apply(this.storedValue);
        }
      }
    };
    Object.defineProperty(ObjectNode2.prototype, "snapshot", {
      // advantage of using computed for a snapshot is that nicely respects transactions etc.
      get: function() {
        return freeze(this.getSnapshot());
      },
      enumerable: false,
      configurable: true
    });
    ObjectNode2.prototype.getSnapshot = function() {
      if (!this.isAlive)
        return this._snapshotUponDeath;
      return this._observableInstanceState === 2 ? this._getActualSnapshot() : this._getCachedInitialSnapshot();
    };
    ObjectNode2.prototype._getActualSnapshot = function() {
      return this.type.getSnapshot(this);
    };
    ObjectNode2.prototype._getCachedInitialSnapshot = function() {
      if (!this._cachedInitialSnapshotCreated) {
        var type = this.type;
        var childNodes = this._childNodes;
        var snapshot = this._initialSnapshot;
        this._cachedInitialSnapshot = type.processInitialSnapshot(childNodes, snapshot);
        this._cachedInitialSnapshotCreated = true;
      }
      return this._cachedInitialSnapshot;
    };
    ObjectNode2.prototype.isRunningAction = function() {
      if (this._isRunningAction)
        return true;
      if (this.isRoot)
        return false;
      return this.parent.isRunningAction();
    };
    ObjectNode2.prototype.assertAlive = function(context) {
      var livelinessChecking2 = getLivelinessChecking();
      if (!this.isAlive && livelinessChecking2 !== "ignore") {
        var error = this._getAssertAliveError(context);
        switch (livelinessChecking2) {
          case "error":
            throw fail$1(error);
          case "warn":
            warnError(error);
        }
      }
    };
    ObjectNode2.prototype._getAssertAliveError = function(context) {
      var escapedPath = this.getEscapedPath(false) || this.pathUponDeath || "";
      var subpath = context.subpath && escapeJsonPath(context.subpath) || "";
      var actionContext = context.actionContext || getCurrentActionContext();
      if (actionContext && actionContext.type !== "action" && actionContext.parentActionEvent) {
        actionContext = actionContext.parentActionEvent;
      }
      var actionFullPath = "";
      if (actionContext && actionContext.name != null) {
        var actionPath = actionContext && actionContext.context && getPath(actionContext.context) || escapedPath;
        actionFullPath = actionPath + "." + actionContext.name + "()";
      }
      return "You are trying to read or write to an object that is no longer part of a state tree. (Object type: '" + this.type.name + "', Path upon death: '" + escapedPath + "', Subpath: '" + subpath + "', Action: '" + actionFullPath + "'). Either detach nodes first, or don't use objects after removing / replacing them in the tree.";
    };
    ObjectNode2.prototype.getChildNode = function(subpath) {
      this.assertAlive({
        subpath
      });
      this._autoUnbox = false;
      try {
        return this._observableInstanceState === 2 ? this.type.getChildNode(this, subpath) : this._childNodes[subpath];
      } finally {
        this._autoUnbox = true;
      }
    };
    ObjectNode2.prototype.getChildren = function() {
      this.assertAlive(EMPTY_OBJECT);
      this._autoUnbox = false;
      try {
        return this._observableInstanceState === 2 ? this.type.getChildren(this) : convertChildNodesToArray(this._childNodes);
      } finally {
        this._autoUnbox = true;
      }
    };
    ObjectNode2.prototype.getChildType = function(propertyName) {
      return this.type.getChildType(propertyName);
    };
    Object.defineProperty(ObjectNode2.prototype, "isProtected", {
      get: function() {
        return this.root.isProtectionEnabled;
      },
      enumerable: false,
      configurable: true
    });
    ObjectNode2.prototype.assertWritable = function(context) {
      this.assertAlive(context);
      if (!this.isRunningAction() && this.isProtected) {
        throw fail$1("Cannot modify '" + this + "', the object is protected and can only be modified by using an action.");
      }
    };
    ObjectNode2.prototype.removeChild = function(subpath) {
      this.type.removeChild(this, subpath);
    };
    ObjectNode2.prototype.unbox = function(childNode) {
      if (!childNode)
        return childNode;
      this.assertAlive({
        subpath: childNode.subpath || childNode.subpathUponDeath
      });
      return this._autoUnbox ? childNode.value : childNode;
    };
    ObjectNode2.prototype.toString = function() {
      var path = (this.isAlive ? this.path : this.pathUponDeath) || "<root>";
      var identifier2 = this.identifier ? "(id: " + this.identifier + ")" : "";
      return this.type.name + "@" + path + identifier2 + (this.isAlive ? "" : " [dead]");
    };
    ObjectNode2.prototype.finalizeCreation = function() {
      var _this = this;
      this.baseFinalizeCreation(function() {
        var e_2, _a;
        try {
          for (var _b = __values(_this.getChildren()), _c = _b.next(); !_c.done; _c = _b.next()) {
            var child = _c.value;
            child.finalizeCreation();
          }
        } catch (e_2_1) {
          e_2 = { error: e_2_1 };
        } finally {
          try {
            if (_c && !_c.done && (_a = _b.return))
              _a.call(_b);
          } finally {
            if (e_2)
              throw e_2.error;
          }
        }
        _this.fireInternalHook(Hook.afterCreationFinalization);
      });
    };
    ObjectNode2.prototype.detach = function() {
      if (!this.isAlive)
        throw fail$1("Error while detaching, node is not alive.");
      this.clearParent();
    };
    ObjectNode2.prototype.preboot = function() {
      var self = this;
      this._applyPatches = createActionInvoker(this.storedValue, "@APPLY_PATCHES", function(patches) {
        patches.forEach(function(patch) {
          var parts = splitJsonPath(patch.path);
          var node = resolveNodeByPathParts(self, parts.slice(0, -1));
          node.applyPatchLocally(parts[parts.length - 1], patch);
        });
      });
      this._applySnapshot = createActionInvoker(this.storedValue, "@APPLY_SNAPSHOT", function(snapshot) {
        if (snapshot === self.snapshot)
          return;
        return self.type.applySnapshot(self, snapshot);
      });
      addHiddenFinalProp(this.storedValue, "$treenode", this);
      addHiddenFinalProp(this.storedValue, "toJSON", toJSON);
    };
    ObjectNode2.prototype.die = function() {
      if (!this.isAlive || this.state === NodeLifeCycle.DETACHING)
        return;
      this.aboutToDie();
      this.finalizeDeath();
    };
    ObjectNode2.prototype.aboutToDie = function() {
      if (this._observableInstanceState === 0) {
        return;
      }
      this.getChildren().forEach(function(node) {
        node.aboutToDie();
      });
      this.baseAboutToDie();
      this._internalEventsEmit(
        "dispose"
        /* Dispose */
      );
      this._internalEventsClear(
        "dispose"
        /* Dispose */
      );
    };
    ObjectNode2.prototype.finalizeDeath = function() {
      this.getChildren().forEach(function(node) {
        node.finalizeDeath();
      });
      this.root.identifierCache.notifyDied(this);
      var snapshot = this.snapshot;
      this._snapshotUponDeath = snapshot;
      this._internalEventsClearAll();
      this.baseFinalizeDeath();
    };
    ObjectNode2.prototype.onSnapshot = function(onChange) {
      this._addSnapshotReaction();
      return this._internalEventsRegister("snapshot", onChange);
    };
    ObjectNode2.prototype.emitSnapshot = function(snapshot) {
      this._internalEventsEmit("snapshot", snapshot);
    };
    ObjectNode2.prototype.onPatch = function(handler) {
      return this._internalEventsRegister("patch", handler);
    };
    ObjectNode2.prototype.emitPatch = function(basePatch, source) {
      if (this._internalEventsHasSubscribers(
        "patch"
        /* Patch */
      )) {
        var localizedPatch = extend({}, basePatch, {
          path: source.path.substr(this.path.length) + "/" + basePatch.path
          // calculate the relative path of the patch
        });
        var _a = __read(splitPatch(localizedPatch), 2), patch = _a[0], reversePatch = _a[1];
        this._internalEventsEmit("patch", patch, reversePatch);
      }
      if (this.parent)
        this.parent.emitPatch(basePatch, source);
    };
    ObjectNode2.prototype.hasDisposer = function(disposer) {
      return this._internalEventsHas("dispose", disposer);
    };
    ObjectNode2.prototype.addDisposer = function(disposer) {
      if (!this.hasDisposer(disposer)) {
        this._internalEventsRegister("dispose", disposer, true);
        return;
      }
      throw fail$1("cannot add a disposer when it is already registered for execution");
    };
    ObjectNode2.prototype.removeDisposer = function(disposer) {
      if (!this._internalEventsHas("dispose", disposer)) {
        throw fail$1("cannot remove a disposer which was never registered for execution");
      }
      this._internalEventsUnregister("dispose", disposer);
    };
    ObjectNode2.prototype.removeMiddleware = function(middleware) {
      if (this.middlewares) {
        var index = this.middlewares.indexOf(middleware);
        if (index >= 0) {
          this.middlewares.splice(index, 1);
        }
      }
    };
    ObjectNode2.prototype.addMiddleWare = function(handler, includeHooks) {
      var _this = this;
      if (includeHooks === void 0) {
        includeHooks = true;
      }
      var middleware = { handler, includeHooks };
      if (!this.middlewares)
        this.middlewares = [middleware];
      else
        this.middlewares.push(middleware);
      return function() {
        _this.removeMiddleware(middleware);
      };
    };
    ObjectNode2.prototype.applyPatchLocally = function(subpath, patch) {
      this.assertWritable({
        subpath
      });
      this.createObservableInstanceIfNeeded();
      this.type.applyPatchLocally(this, subpath, patch);
    };
    ObjectNode2.prototype._addSnapshotReaction = function() {
      var _this = this;
      if (!this._hasSnapshotReaction) {
        var snapshotDisposer = reaction(function() {
          return _this.snapshot;
        }, function(snapshot) {
          return _this.emitSnapshot(snapshot);
        }, snapshotReactionOptions);
        this.addDisposer(snapshotDisposer);
        this._hasSnapshotReaction = true;
      }
    };
    ObjectNode2.prototype._internalEventsHasSubscribers = function(event) {
      return !!this._internalEvents && this._internalEvents.hasSubscribers(event);
    };
    ObjectNode2.prototype._internalEventsRegister = function(event, eventHandler, atTheBeginning) {
      if (atTheBeginning === void 0) {
        atTheBeginning = false;
      }
      if (!this._internalEvents) {
        this._internalEvents = new EventHandlers();
      }
      return this._internalEvents.register(event, eventHandler, atTheBeginning);
    };
    ObjectNode2.prototype._internalEventsHas = function(event, eventHandler) {
      return !!this._internalEvents && this._internalEvents.has(event, eventHandler);
    };
    ObjectNode2.prototype._internalEventsUnregister = function(event, eventHandler) {
      if (this._internalEvents) {
        this._internalEvents.unregister(event, eventHandler);
      }
    };
    ObjectNode2.prototype._internalEventsEmit = function(event) {
      var _a;
      var args = [];
      for (var _i = 1; _i < arguments.length; _i++) {
        args[_i - 1] = arguments[_i];
      }
      if (this._internalEvents) {
        (_a = this._internalEvents).emit.apply(_a, __spread([event], args));
      }
    };
    ObjectNode2.prototype._internalEventsClear = function(event) {
      if (this._internalEvents) {
        this._internalEvents.clear(event);
      }
    };
    ObjectNode2.prototype._internalEventsClearAll = function() {
      if (this._internalEvents) {
        this._internalEvents.clearAll();
      }
    };
    __decorate([
      action
    ], ObjectNode2.prototype, "createObservableInstance", null);
    __decorate([
      computed
    ], ObjectNode2.prototype, "snapshot", null);
    __decorate([
      action
    ], ObjectNode2.prototype, "detach", null);
    __decorate([
      action
    ], ObjectNode2.prototype, "die", null);
    return ObjectNode2;
  }(BaseNode)
);
var TypeFlags;
(function(TypeFlags2) {
  TypeFlags2[TypeFlags2["String"] = 1] = "String";
  TypeFlags2[TypeFlags2["Number"] = 2] = "Number";
  TypeFlags2[TypeFlags2["Boolean"] = 4] = "Boolean";
  TypeFlags2[TypeFlags2["Date"] = 8] = "Date";
  TypeFlags2[TypeFlags2["Literal"] = 16] = "Literal";
  TypeFlags2[TypeFlags2["Array"] = 32] = "Array";
  TypeFlags2[TypeFlags2["Map"] = 64] = "Map";
  TypeFlags2[TypeFlags2["Object"] = 128] = "Object";
  TypeFlags2[TypeFlags2["Frozen"] = 256] = "Frozen";
  TypeFlags2[TypeFlags2["Optional"] = 512] = "Optional";
  TypeFlags2[TypeFlags2["Reference"] = 1024] = "Reference";
  TypeFlags2[TypeFlags2["Identifier"] = 2048] = "Identifier";
  TypeFlags2[TypeFlags2["Late"] = 4096] = "Late";
  TypeFlags2[TypeFlags2["Refinement"] = 8192] = "Refinement";
  TypeFlags2[TypeFlags2["Union"] = 16384] = "Union";
  TypeFlags2[TypeFlags2["Null"] = 32768] = "Null";
  TypeFlags2[TypeFlags2["Undefined"] = 65536] = "Undefined";
  TypeFlags2[TypeFlags2["Integer"] = 131072] = "Integer";
  TypeFlags2[TypeFlags2["Custom"] = 262144] = "Custom";
  TypeFlags2[TypeFlags2["SnapshotProcessor"] = 524288] = "SnapshotProcessor";
})(TypeFlags || (TypeFlags = {}));
var cannotDetermineSubtype = "cannotDetermine";
var BaseType = (
  /** @class */
  function() {
    function BaseType2(name) {
      this.isType = true;
      this.name = name;
    }
    BaseType2.prototype.create = function(snapshot, environment) {
      typecheckInternal(this, snapshot);
      return this.instantiate(null, "", environment, snapshot).value;
    };
    BaseType2.prototype.getSnapshot = function(node, applyPostProcess) {
      throw fail$1("unimplemented method");
    };
    BaseType2.prototype.isAssignableFrom = function(type) {
      return type === this;
    };
    BaseType2.prototype.validate = function(value, context) {
      var node = getStateTreeNodeSafe(value);
      if (node) {
        var valueType = getType(value);
        return this.isAssignableFrom(valueType) ? typeCheckSuccess() : typeCheckFailure(context, value);
      }
      return this.isValidSnapshot(value, context);
    };
    BaseType2.prototype.is = function(thing) {
      return this.validate(thing, [{ path: "", type: this }]).length === 0;
    };
    Object.defineProperty(BaseType2.prototype, "Type", {
      get: function() {
        throw fail$1("Factory.Type should not be actually called. It is just a Type signature that can be used at compile time with Typescript, by using `typeof type.Type`");
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(BaseType2.prototype, "TypeWithoutSTN", {
      get: function() {
        throw fail$1("Factory.TypeWithoutSTN should not be actually called. It is just a Type signature that can be used at compile time with Typescript, by using `typeof type.TypeWithoutSTN`");
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(BaseType2.prototype, "SnapshotType", {
      get: function() {
        throw fail$1("Factory.SnapshotType should not be actually called. It is just a Type signature that can be used at compile time with Typescript, by using `typeof type.SnapshotType`");
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(BaseType2.prototype, "CreationType", {
      get: function() {
        throw fail$1("Factory.CreationType should not be actually called. It is just a Type signature that can be used at compile time with Typescript, by using `typeof type.CreationType`");
      },
      enumerable: false,
      configurable: true
    });
    __decorate([
      action
    ], BaseType2.prototype, "create", null);
    return BaseType2;
  }()
);
var ComplexType = (
  /** @class */
  function(_super) {
    __extends(ComplexType2, _super);
    function ComplexType2(name) {
      return _super.call(this, name) || this;
    }
    ComplexType2.prototype.create = function(snapshot, environment) {
      if (snapshot === void 0) {
        snapshot = this.getDefaultSnapshot();
      }
      return _super.prototype.create.call(this, snapshot, environment);
    };
    ComplexType2.prototype.getValue = function(node) {
      node.createObservableInstanceIfNeeded();
      return node.storedValue;
    };
    ComplexType2.prototype.tryToReconcileNode = function(current, newValue) {
      if (current.isDetaching)
        return false;
      if (current.snapshot === newValue) {
        return true;
      }
      if (isStateTreeNode(newValue) && getStateTreeNode(newValue) === current) {
        return true;
      }
      if (current.type === this && isMutable(newValue) && !isStateTreeNode(newValue) && (!current.identifierAttribute || current.identifier === normalizeIdentifier(newValue[current.identifierAttribute]))) {
        current.applySnapshot(newValue);
        return true;
      }
      return false;
    };
    ComplexType2.prototype.reconcile = function(current, newValue, parent, subpath) {
      var nodeReconciled = this.tryToReconcileNode(current, newValue);
      if (nodeReconciled) {
        current.setParent(parent, subpath);
        return current;
      }
      current.die();
      if (isStateTreeNode(newValue) && this.isAssignableFrom(getType(newValue))) {
        var newNode = getStateTreeNode(newValue);
        newNode.setParent(parent, subpath);
        return newNode;
      }
      return this.instantiate(parent, subpath, void 0, newValue);
    };
    ComplexType2.prototype.getSubTypes = function() {
      return null;
    };
    __decorate([
      action
    ], ComplexType2.prototype, "create", null);
    return ComplexType2;
  }(BaseType)
);
var SimpleType = (
  /** @class */
  function(_super) {
    __extends(SimpleType2, _super);
    function SimpleType2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    SimpleType2.prototype.createNewInstance = function(snapshot) {
      return snapshot;
    };
    SimpleType2.prototype.getValue = function(node) {
      return node.storedValue;
    };
    SimpleType2.prototype.getSnapshot = function(node) {
      return node.storedValue;
    };
    SimpleType2.prototype.reconcile = function(current, newValue, parent, subpath) {
      if (!current.isDetaching && current.type === this && current.storedValue === newValue) {
        return current;
      }
      var res = this.instantiate(parent, subpath, void 0, newValue);
      current.die();
      return res;
    };
    SimpleType2.prototype.getSubTypes = function() {
      return null;
    };
    return SimpleType2;
  }(BaseType)
);
function isType(value) {
  return typeof value === "object" && value && value.isType === true;
}
function assertIsType(type, argNumber) {
  assertArg(type, isType, "mobx-state-tree type", argNumber);
}
var RunningAction = (
  /** @class */
  function() {
    function RunningAction2(hooks, call) {
      this.hooks = hooks;
      this.call = call;
      this.flowsPending = 0;
      this.running = true;
      if (hooks) {
        hooks.onStart(call);
      }
    }
    RunningAction2.prototype.finish = function(error) {
      if (this.running) {
        this.running = false;
        if (this.hooks) {
          this.hooks.onFinish(this.call, error);
        }
      }
    };
    RunningAction2.prototype.incFlowsPending = function() {
      this.flowsPending++;
    };
    RunningAction2.prototype.decFlowsPending = function() {
      this.flowsPending--;
    };
    Object.defineProperty(RunningAction2.prototype, "hasFlowsPending", {
      get: function() {
        return this.flowsPending > 0;
      },
      enumerable: false,
      configurable: true
    });
    return RunningAction2;
  }()
);
var nextActionId = 1;
var currentActionContext;
function getCurrentActionContext() {
  return currentActionContext;
}
function getNextActionId() {
  return nextActionId++;
}
function runWithActionContext(context, fn) {
  var node = getStateTreeNode(context.context);
  if (context.type === "action") {
    node.assertAlive({
      actionContext: context
    });
  }
  var baseIsRunningAction = node._isRunningAction;
  node._isRunningAction = true;
  var previousContext = currentActionContext;
  currentActionContext = context;
  try {
    return runMiddleWares(node, context, fn);
  } finally {
    currentActionContext = previousContext;
    node._isRunningAction = baseIsRunningAction;
  }
}
function getParentActionContext(parentContext) {
  if (!parentContext)
    return void 0;
  if (parentContext.type === "action")
    return parentContext;
  return parentContext.parentActionEvent;
}
function createActionInvoker(target, name, fn) {
  var res = function() {
    var id = getNextActionId();
    var parentContext = currentActionContext;
    var parentActionContext = getParentActionContext(parentContext);
    return runWithActionContext({
      type: "action",
      name,
      id,
      args: argsToArray(arguments),
      context: target,
      tree: getRoot(target),
      rootId: parentContext ? parentContext.rootId : id,
      parentId: parentContext ? parentContext.id : 0,
      allParentIds: parentContext ? __spread(parentContext.allParentIds, [parentContext.id]) : [],
      parentEvent: parentContext,
      parentActionEvent: parentActionContext
    }, fn);
  };
  res._isMSTAction = true;
  return res;
}
var CollectedMiddlewares = (
  /** @class */
  function() {
    function CollectedMiddlewares2(node, fn) {
      this.arrayIndex = 0;
      this.inArrayIndex = 0;
      this.middlewares = [];
      if (fn.$mst_middleware) {
        this.middlewares.push(fn.$mst_middleware);
      }
      var n = node;
      while (n) {
        if (n.middlewares)
          this.middlewares.push(n.middlewares);
        n = n.parent;
      }
    }
    Object.defineProperty(CollectedMiddlewares2.prototype, "isEmpty", {
      get: function() {
        return this.middlewares.length <= 0;
      },
      enumerable: false,
      configurable: true
    });
    CollectedMiddlewares2.prototype.getNextMiddleware = function() {
      var array2 = this.middlewares[this.arrayIndex];
      if (!array2)
        return void 0;
      var item = array2[this.inArrayIndex++];
      if (!item) {
        this.arrayIndex++;
        this.inArrayIndex = 0;
        return this.getNextMiddleware();
      }
      return item;
    };
    return CollectedMiddlewares2;
  }()
);
function runMiddleWares(node, baseCall, originalFn) {
  var middlewares = new CollectedMiddlewares(node, originalFn);
  if (middlewares.isEmpty)
    return action(originalFn).apply(null, baseCall.args);
  var result = null;
  function runNextMiddleware(call) {
    var middleware = middlewares.getNextMiddleware();
    var handler = middleware && middleware.handler;
    if (!handler) {
      return action(originalFn).apply(null, call.args);
    }
    if (!middleware.includeHooks && Hook[call.name]) {
      return runNextMiddleware(call);
    }
    var nextInvoked = false;
    function next(call2, callback) {
      nextInvoked = true;
      result = runNextMiddleware(call2);
      if (callback) {
        result = callback(result);
      }
    }
    var abortInvoked = false;
    function abort(value) {
      abortInvoked = true;
      result = value;
    }
    handler(call, next, abort);
    if (devMode()) {
      if (!nextInvoked && !abortInvoked) {
        var node2 = getStateTreeNode(call.tree);
        throw fail$1("Neither the next() nor the abort() callback within the middleware " + handler.name + ' for the action: "' + call.name + '" on the node: ' + node2.type.name + " was invoked.");
      } else if (nextInvoked && abortInvoked) {
        var node2 = getStateTreeNode(call.tree);
        throw fail$1("The next() and abort() callback within the middleware " + handler.name + ' for the action: "' + call.name + '" on the node: ' + node2.type.name + " were invoked.");
      }
    }
    return result;
  }
  return runNextMiddleware(baseCall);
}
function safeStringify(value) {
  try {
    return JSON.stringify(value);
  } catch (e) {
    return "<Unserializable: " + e + ">";
  }
}
function prettyPrintValue(value) {
  return typeof value === "function" ? "<function" + (value.name ? " " + value.name : "") + ">" : isStateTreeNode(value) ? "<" + value + ">" : "`" + safeStringify(value) + "`";
}
function shortenPrintValue(valueInString) {
  return valueInString.length < 280 ? valueInString : valueInString.substring(0, 272) + "......" + valueInString.substring(valueInString.length - 8);
}
function toErrorString(error) {
  var value = error.value;
  var type = error.context[error.context.length - 1].type;
  var fullPath = error.context.map(function(_a) {
    var path = _a.path;
    return path;
  }).filter(function(path) {
    return path.length > 0;
  }).join("/");
  var pathPrefix = fullPath.length > 0 ? 'at path "/' + fullPath + '" ' : "";
  var currentTypename = isStateTreeNode(value) ? "value of type " + getStateTreeNode(value).type.name + ":" : isPrimitive(value) ? "value" : "snapshot";
  var isSnapshotCompatible = type && isStateTreeNode(value) && type.is(getStateTreeNode(value).snapshot);
  return "" + pathPrefix + currentTypename + " " + prettyPrintValue(value) + " is not assignable " + (type ? "to type: `" + type.name + "`" : "") + (error.message ? " (" + error.message + ")" : "") + (type ? isPrimitiveType(type) || isPrimitive(value) ? "." : ", expected an instance of `" + type.name + "` or a snapshot like `" + type.describe() + "` instead." + (isSnapshotCompatible ? " (Note that a snapshot of the provided value is compatible with the targeted type)" : "") : ".");
}
function getContextForPath(context, path, type) {
  return context.concat([{ path, type }]);
}
function typeCheckSuccess() {
  return EMPTY_ARRAY;
}
function typeCheckFailure(context, value, message) {
  return [{ context, value, message }];
}
function flattenTypeErrors(errors) {
  return errors.reduce(function(a, i) {
    return a.concat(i);
  }, []);
}
function typecheckInternal(type, value) {
  if (isTypeCheckingEnabled()) {
    typecheck(type, value);
  }
}
function typecheck(type, value) {
  var errors = type.validate(value, [{ path: "", type }]);
  if (errors.length > 0) {
    throw fail$1(validationErrorsToString(type, value, errors));
  }
}
function validationErrorsToString(type, value, errors) {
  if (errors.length === 0) {
    return void 0;
  }
  return "Error while converting " + shortenPrintValue(prettyPrintValue(value)) + " to `" + type.name + "`:\n\n    " + errors.map(toErrorString).join("\n    ");
}
var identifierCacheId = 0;
var IdentifierCache = (
  /** @class */
  function() {
    function IdentifierCache2() {
      this.cacheId = identifierCacheId++;
      this.cache = observable.map();
      this.lastCacheModificationPerId = observable.map();
    }
    IdentifierCache2.prototype.updateLastCacheModificationPerId = function(identifier2) {
      var lcm = this.lastCacheModificationPerId.get(identifier2);
      this.lastCacheModificationPerId.set(identifier2, lcm === void 0 ? 1 : lcm + 1);
    };
    IdentifierCache2.prototype.getLastCacheModificationPerId = function(identifier2) {
      var modificationId = this.lastCacheModificationPerId.get(identifier2) || 0;
      return this.cacheId + "-" + modificationId;
    };
    IdentifierCache2.prototype.addNodeToCache = function(node, lastCacheUpdate) {
      if (lastCacheUpdate === void 0) {
        lastCacheUpdate = true;
      }
      if (node.identifierAttribute) {
        var identifier2 = node.identifier;
        if (!this.cache.has(identifier2)) {
          this.cache.set(identifier2, observable.array([], mobxShallow));
        }
        var set2 = this.cache.get(identifier2);
        if (set2.indexOf(node) !== -1)
          throw fail$1("Already registered");
        set2.push(node);
        if (lastCacheUpdate) {
          this.updateLastCacheModificationPerId(identifier2);
        }
      }
    };
    IdentifierCache2.prototype.mergeCache = function(node) {
      var _this = this;
      values(node.identifierCache.cache).forEach(function(nodes) {
        return nodes.forEach(function(child) {
          _this.addNodeToCache(child);
        });
      });
    };
    IdentifierCache2.prototype.notifyDied = function(node) {
      if (node.identifierAttribute) {
        var id = node.identifier;
        var set2 = this.cache.get(id);
        if (set2) {
          set2.remove(node);
          if (!set2.length) {
            this.cache.delete(id);
          }
          this.updateLastCacheModificationPerId(node.identifier);
        }
      }
    };
    IdentifierCache2.prototype.splitCache = function(node) {
      var _this = this;
      var res = new IdentifierCache2();
      var basePath = node.path;
      entries(this.cache).forEach(function(_a) {
        var _b = __read(_a, 2), id = _b[0], nodes = _b[1];
        var modified = false;
        for (var i = nodes.length - 1; i >= 0; i--) {
          if (nodes[i].path.indexOf(basePath) === 0) {
            res.addNodeToCache(nodes[i], false);
            nodes.splice(i, 1);
            modified = true;
          }
        }
        if (modified) {
          _this.updateLastCacheModificationPerId(id);
        }
      });
      return res;
    };
    IdentifierCache2.prototype.has = function(type, identifier2) {
      var set2 = this.cache.get(identifier2);
      if (!set2)
        return false;
      return set2.some(function(candidate) {
        return type.isAssignableFrom(candidate.type);
      });
    };
    IdentifierCache2.prototype.resolve = function(type, identifier2) {
      var set2 = this.cache.get(identifier2);
      if (!set2)
        return null;
      var matches = set2.filter(function(candidate) {
        return type.isAssignableFrom(candidate.type);
      });
      switch (matches.length) {
        case 0:
          return null;
        case 1:
          return matches[0];
        default:
          throw fail$1("Cannot resolve a reference to type '" + type.name + "' with id: '" + identifier2 + "' unambigously, there are multiple candidates: " + matches.map(function(n) {
            return n.path;
          }).join(", "));
      }
    };
    return IdentifierCache2;
  }()
);
function createObjectNode(type, parent, subpath, environment, initialValue) {
  var existingNode = getStateTreeNodeSafe(initialValue);
  if (existingNode) {
    if (existingNode.parent) {
      throw fail$1("Cannot add an object to a state tree if it is already part of the same or another state tree. Tried to assign an object to '" + (parent ? parent.path : "") + "/" + subpath + "', but it lives already at '" + existingNode.path + "'");
    }
    if (parent) {
      existingNode.setParent(parent, subpath);
    }
    return existingNode;
  }
  return new ObjectNode(type, parent, subpath, environment, initialValue);
}
function createScalarNode(type, parent, subpath, environment, initialValue) {
  return new ScalarNode(type, parent, subpath, environment, initialValue);
}
function isNode(value) {
  return value instanceof ScalarNode || value instanceof ObjectNode;
}
var NodeLifeCycle;
(function(NodeLifeCycle2) {
  NodeLifeCycle2[NodeLifeCycle2["INITIALIZING"] = 0] = "INITIALIZING";
  NodeLifeCycle2[NodeLifeCycle2["CREATED"] = 1] = "CREATED";
  NodeLifeCycle2[NodeLifeCycle2["FINALIZED"] = 2] = "FINALIZED";
  NodeLifeCycle2[NodeLifeCycle2["DETACHING"] = 3] = "DETACHING";
  NodeLifeCycle2[NodeLifeCycle2["DEAD"] = 4] = "DEAD";
})(NodeLifeCycle || (NodeLifeCycle = {}));
function isStateTreeNode(value) {
  return !!(value && value.$treenode);
}
function assertIsStateTreeNode(value, argNumber) {
  assertArg(value, isStateTreeNode, "mobx-state-tree node", argNumber);
}
function getStateTreeNode(value) {
  if (!isStateTreeNode(value)) {
    throw fail$1("Value " + value + " is no MST Node");
  }
  return value.$treenode;
}
function getStateTreeNodeSafe(value) {
  return value && value.$treenode || null;
}
function toJSON() {
  return getStateTreeNode(this).snapshot;
}
function resolveNodeByPathParts(base, pathParts, failIfResolveFails) {
  if (failIfResolveFails === void 0) {
    failIfResolveFails = true;
  }
  var current = base;
  for (var i = 0; i < pathParts.length; i++) {
    var part = pathParts[i];
    if (part === "..") {
      current = current.parent;
      if (current)
        continue;
    } else if (part === ".") {
      continue;
    } else if (current) {
      if (current instanceof ScalarNode) {
        try {
          var value = current.value;
          if (isStateTreeNode(value)) {
            current = getStateTreeNode(value);
          }
        } catch (e) {
          if (!failIfResolveFails) {
            return void 0;
          }
          throw e;
        }
      }
      if (current instanceof ObjectNode) {
        var subType = current.getChildType(part);
        if (subType) {
          current = current.getChildNode(part);
          if (current)
            continue;
        }
      }
    }
    if (failIfResolveFails)
      throw fail$1("Could not resolve '" + part + "' in path '" + (joinJsonPath(pathParts.slice(0, i)) || "/") + "' while resolving '" + joinJsonPath(pathParts) + "'");
    else
      return void 0;
  }
  return current;
}
function convertChildNodesToArray(childNodes) {
  if (!childNodes)
    return EMPTY_ARRAY;
  var keys = Object.keys(childNodes);
  if (!keys.length)
    return EMPTY_ARRAY;
  var result = new Array(keys.length);
  keys.forEach(function(key, index) {
    result[index] = childNodes[key];
  });
  return result;
}
var EMPTY_ARRAY = Object.freeze([]);
var EMPTY_OBJECT = Object.freeze({});
var mobxShallow = typeof $mobx === "string" ? { deep: false } : { deep: false, proxy: false };
Object.freeze(mobxShallow);
function fail$1(message) {
  if (message === void 0) {
    message = "Illegal state";
  }
  return new Error("[mobx-state-tree] " + message);
}
function identity(_) {
  return _;
}
var isInteger = Number.isInteger || function(value) {
  return typeof value === "number" && isFinite(value) && Math.floor(value) === value;
};
function isArray(val) {
  return Array.isArray(val) || isObservableArray(val);
}
function asArray(val) {
  if (!val)
    return EMPTY_ARRAY;
  if (isArray(val))
    return val;
  return [val];
}
function extend(a) {
  var b = [];
  for (var _i = 1; _i < arguments.length; _i++) {
    b[_i - 1] = arguments[_i];
  }
  for (var i = 0; i < b.length; i++) {
    var current = b[i];
    for (var key in current)
      a[key] = current[key];
  }
  return a;
}
function isPlainObject(value) {
  if (value === null || typeof value !== "object")
    return false;
  var proto = Object.getPrototypeOf(value);
  return proto === Object.prototype || proto === null;
}
function isMutable(value) {
  return value !== null && typeof value === "object" && !(value instanceof Date) && !(value instanceof RegExp);
}
function isPrimitive(value, includeDate) {
  if (includeDate === void 0) {
    includeDate = true;
  }
  if (value === null || value === void 0)
    return true;
  if (typeof value === "string" || typeof value === "number" || typeof value === "boolean" || includeDate && value instanceof Date)
    return true;
  return false;
}
function freeze(value) {
  if (!devMode())
    return value;
  return isPrimitive(value) || isObservableArray(value) ? value : Object.freeze(value);
}
function deepFreeze(value) {
  if (!devMode())
    return value;
  freeze(value);
  if (isPlainObject(value)) {
    Object.keys(value).forEach(function(propKey) {
      if (!isPrimitive(value[propKey]) && !Object.isFrozen(value[propKey])) {
        deepFreeze(value[propKey]);
      }
    });
  }
  return value;
}
function isSerializable(value) {
  return typeof value !== "function";
}
function addHiddenFinalProp(object, propName, value) {
  Object.defineProperty(object, propName, {
    enumerable: false,
    writable: false,
    configurable: true,
    value
  });
}
function addHiddenWritableProp(object, propName, value) {
  Object.defineProperty(object, propName, {
    enumerable: false,
    writable: true,
    configurable: true,
    value
  });
}
var EventHandler = (
  /** @class */
  function() {
    function EventHandler2() {
      this.handlers = [];
    }
    Object.defineProperty(EventHandler2.prototype, "hasSubscribers", {
      get: function() {
        return this.handlers.length > 0;
      },
      enumerable: false,
      configurable: true
    });
    EventHandler2.prototype.register = function(fn, atTheBeginning) {
      var _this = this;
      if (atTheBeginning === void 0) {
        atTheBeginning = false;
      }
      if (atTheBeginning) {
        this.handlers.unshift(fn);
      } else {
        this.handlers.push(fn);
      }
      return function() {
        _this.unregister(fn);
      };
    };
    EventHandler2.prototype.has = function(fn) {
      return this.handlers.indexOf(fn) >= 0;
    };
    EventHandler2.prototype.unregister = function(fn) {
      var index = this.handlers.indexOf(fn);
      if (index >= 0) {
        this.handlers.splice(index, 1);
      }
    };
    EventHandler2.prototype.clear = function() {
      this.handlers.length = 0;
    };
    EventHandler2.prototype.emit = function() {
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      var handlers = this.handlers.slice();
      handlers.forEach(function(f) {
        return f.apply(void 0, __spread(args));
      });
    };
    return EventHandler2;
  }()
);
var EventHandlers = (
  /** @class */
  function() {
    function EventHandlers2() {
    }
    EventHandlers2.prototype.hasSubscribers = function(event) {
      var handler = this.eventHandlers && this.eventHandlers[event];
      return !!handler && handler.hasSubscribers;
    };
    EventHandlers2.prototype.register = function(event, fn, atTheBeginning) {
      if (atTheBeginning === void 0) {
        atTheBeginning = false;
      }
      if (!this.eventHandlers) {
        this.eventHandlers = {};
      }
      var handler = this.eventHandlers[event];
      if (!handler) {
        handler = this.eventHandlers[event] = new EventHandler();
      }
      return handler.register(fn, atTheBeginning);
    };
    EventHandlers2.prototype.has = function(event, fn) {
      var handler = this.eventHandlers && this.eventHandlers[event];
      return !!handler && handler.has(fn);
    };
    EventHandlers2.prototype.unregister = function(event, fn) {
      var handler = this.eventHandlers && this.eventHandlers[event];
      if (handler) {
        handler.unregister(fn);
      }
    };
    EventHandlers2.prototype.clear = function(event) {
      if (this.eventHandlers) {
        delete this.eventHandlers[event];
      }
    };
    EventHandlers2.prototype.clearAll = function() {
      this.eventHandlers = void 0;
    };
    EventHandlers2.prototype.emit = function(event) {
      var _a;
      var args = [];
      for (var _i = 1; _i < arguments.length; _i++) {
        args[_i - 1] = arguments[_i];
      }
      var handler = this.eventHandlers && this.eventHandlers[event];
      if (handler) {
        (_a = handler).emit.apply(_a, __spread(args));
      }
    };
    return EventHandlers2;
  }()
);
function argsToArray(args) {
  var res = new Array(args.length);
  for (var i = 0; i < args.length; i++)
    res[i] = args[i];
  return res;
}
function invalidateComputed(target, propName) {
  var atom = getAtom(target, propName);
  atom.trackAndCompute();
}
function stringStartsWith(str, beginning) {
  return str.indexOf(beginning) === 0;
}
var deprecated = function(id, message) {
  if (!devMode())
    return;
  if (deprecated.ids && !deprecated.ids.hasOwnProperty(id)) {
    warnError("Deprecation warning: " + message);
  }
  if (deprecated.ids)
    deprecated.ids[id] = true;
};
deprecated.ids = {};
function warnError(msg) {
  console.warn(new Error("[mobx-state-tree] " + msg));
}
function isTypeCheckingEnabled() {
  return devMode() || typeof process !== "undefined" && process.env && process.env.ENABLE_TYPE_CHECK === "true";
}
function devMode() {
  return true;
}
function assertArg(value, fn, typeName, argNumber) {
  if (devMode()) {
    if (!fn(value)) {
      throw fail$1("expected " + typeName + " as argument " + asArray(argNumber).join(" or ") + ", got " + value + " instead");
    }
  }
}
function assertIsFunction(value, argNumber) {
  assertArg(value, function(fn) {
    return typeof fn === "function";
  }, "function", argNumber);
}
function assertIsString(value, argNumber, canBeEmpty) {
  if (canBeEmpty === void 0) {
    canBeEmpty = true;
  }
  assertArg(value, function(s) {
    return typeof s === "string";
  }, "string", argNumber);
  if (!canBeEmpty) {
    assertArg(value, function(s) {
      return s !== "";
    }, "not empty string", argNumber);
  }
}
function setImmediateWithFallback(fn) {
  if (typeof queueMicrotask === "function") {
    queueMicrotask(fn);
  } else if (typeof setImmediate === "function") {
    setImmediate(fn);
  } else {
    setTimeout(fn, 1);
  }
}
function flow(generator) {
  return createFlowSpawner(generator.name, generator);
}
function createFlowSpawner(name, generator) {
  var spawner = function flowSpawner() {
    var runId = getNextActionId();
    var parentContext = getCurrentActionContext();
    if (!parentContext) {
      throw fail$1("a mst flow must always have a parent context");
    }
    var parentActionContext = getParentActionContext(parentContext);
    if (!parentActionContext) {
      throw fail$1("a mst flow must always have a parent action context");
    }
    var contextBase = {
      name,
      id: runId,
      tree: parentContext.tree,
      context: parentContext.context,
      parentId: parentContext.id,
      allParentIds: __spread(parentContext.allParentIds, [parentContext.id]),
      rootId: parentContext.rootId,
      parentEvent: parentContext,
      parentActionEvent: parentActionContext
    };
    var args = arguments;
    function wrap(fn, type, arg) {
      fn.$mst_middleware = spawner.$mst_middleware;
      runWithActionContext(__assign(__assign({}, contextBase), { type, args: [arg] }), fn);
    }
    return new Promise(function(resolve, reject) {
      var gen;
      var init = function asyncActionInit() {
        gen = generator.apply(null, arguments);
        onFulfilled(void 0);
      };
      init.$mst_middleware = spawner.$mst_middleware;
      runWithActionContext(__assign(__assign({}, contextBase), { type: "flow_spawn", args: argsToArray(args) }), init);
      function onFulfilled(res) {
        var ret;
        try {
          wrap(function(r) {
            ret = gen.next(r);
          }, "flow_resume", res);
        } catch (e) {
          setImmediateWithFallback(function() {
            wrap(function(r) {
              reject(e);
            }, "flow_throw", e);
          });
          return;
        }
        next(ret);
        return;
      }
      function onRejected(err) {
        var ret;
        try {
          wrap(function(r) {
            ret = gen.throw(r);
          }, "flow_resume_error", err);
        } catch (e) {
          setImmediateWithFallback(function() {
            wrap(function(r) {
              reject(e);
            }, "flow_throw", e);
          });
          return;
        }
        next(ret);
      }
      function next(ret) {
        if (ret.done) {
          setImmediateWithFallback(function() {
            wrap(function(r) {
              resolve(r);
            }, "flow_return", ret.value);
          });
          return;
        }
        if (!ret.value || typeof ret.value.then !== "function") {
          throw fail$1("Only promises can be yielded to `async`, got: " + ret);
        }
        return ret.value.then(onFulfilled, onRejected);
      }
    });
  };
  return spawner;
}
function splitPatch(patch) {
  if (!("oldValue" in patch))
    throw fail$1("Patches without `oldValue` field cannot be inversed");
  return [stripPatch(patch), invertPatch(patch)];
}
function stripPatch(patch) {
  switch (patch.op) {
    case "add":
      return { op: "add", path: patch.path, value: patch.value };
    case "remove":
      return { op: "remove", path: patch.path };
    case "replace":
      return { op: "replace", path: patch.path, value: patch.value };
  }
}
function invertPatch(patch) {
  switch (patch.op) {
    case "add":
      return {
        op: "remove",
        path: patch.path
      };
    case "remove":
      return {
        op: "add",
        path: patch.path,
        value: patch.oldValue
      };
    case "replace":
      return {
        op: "replace",
        path: patch.path,
        value: patch.oldValue
      };
  }
}
function isNumber(x) {
  return typeof x === "number";
}
function escapeJsonPath(path) {
  if (isNumber(path) === true) {
    return "" + path;
  }
  if (path.indexOf("/") === -1 && path.indexOf("~") === -1)
    return path;
  return path.replace(/~/g, "~0").replace(/\//g, "~1");
}
function unescapeJsonPath(path) {
  return path.replace(/~1/g, "/").replace(/~0/g, "~");
}
function joinJsonPath(path) {
  if (path.length === 0)
    return "";
  var getPathStr = function(p) {
    return p.map(escapeJsonPath).join("/");
  };
  if (path[0] === "." || path[0] === "..") {
    return getPathStr(path);
  } else {
    return "/" + getPathStr(path);
  }
}
function splitJsonPath(path) {
  var parts = path.split("/").map(unescapeJsonPath);
  var valid = path === "" || path === "." || path === ".." || stringStartsWith(path, "/") || stringStartsWith(path, "./") || stringStartsWith(path, "../");
  if (!valid) {
    throw fail$1("a json path must be either rooted, empty or relative, but got '" + path + "'");
  }
  if (parts[0] === "") {
    parts.shift();
  }
  return parts;
}
var SnapshotProcessor = (
  /** @class */
  function(_super) {
    __extends(SnapshotProcessor2, _super);
    function SnapshotProcessor2(_subtype, _processors, name) {
      var _this = _super.call(this, name || _subtype.name) || this;
      _this._subtype = _subtype;
      _this._processors = _processors;
      return _this;
    }
    Object.defineProperty(SnapshotProcessor2.prototype, "flags", {
      get: function() {
        return this._subtype.flags | TypeFlags.SnapshotProcessor;
      },
      enumerable: false,
      configurable: true
    });
    SnapshotProcessor2.prototype.describe = function() {
      return "snapshotProcessor(" + this._subtype.describe() + ")";
    };
    SnapshotProcessor2.prototype.preProcessSnapshot = function(sn) {
      if (this._processors.preProcessor) {
        return this._processors.preProcessor.call(null, sn);
      }
      return sn;
    };
    SnapshotProcessor2.prototype.postProcessSnapshot = function(sn) {
      if (this._processors.postProcessor) {
        return this._processors.postProcessor.call(null, sn);
      }
      return sn;
    };
    SnapshotProcessor2.prototype._fixNode = function(node) {
      var _this = this;
      proxyNodeTypeMethods(node.type, this, "isAssignableFrom", "create");
      var oldGetSnapshot = node.getSnapshot;
      node.getSnapshot = function() {
        return _this.postProcessSnapshot(oldGetSnapshot.call(node));
      };
    };
    SnapshotProcessor2.prototype.instantiate = function(parent, subpath, environment, initialValue) {
      var processedInitialValue = isStateTreeNode(initialValue) ? initialValue : this.preProcessSnapshot(initialValue);
      var node = this._subtype.instantiate(parent, subpath, environment, processedInitialValue);
      this._fixNode(node);
      return node;
    };
    SnapshotProcessor2.prototype.reconcile = function(current, newValue, parent, subpath) {
      var node = this._subtype.reconcile(current, isStateTreeNode(newValue) ? newValue : this.preProcessSnapshot(newValue), parent, subpath);
      if (node !== current) {
        this._fixNode(node);
      }
      return node;
    };
    SnapshotProcessor2.prototype.getSnapshot = function(node, applyPostProcess) {
      if (applyPostProcess === void 0) {
        applyPostProcess = true;
      }
      var sn = this._subtype.getSnapshot(node);
      return applyPostProcess ? this.postProcessSnapshot(sn) : sn;
    };
    SnapshotProcessor2.prototype.isValidSnapshot = function(value, context) {
      var processedSn = this.preProcessSnapshot(value);
      return this._subtype.validate(processedSn, context);
    };
    SnapshotProcessor2.prototype.getSubTypes = function() {
      return this._subtype;
    };
    SnapshotProcessor2.prototype.is = function(thing) {
      var value = isType(thing) ? this._subtype : isStateTreeNode(thing) ? getSnapshot(thing, false) : this.preProcessSnapshot(thing);
      return this._subtype.validate(value, [{ path: "", type: this._subtype }]).length === 0;
    };
    return SnapshotProcessor2;
  }(BaseType)
);
function proxyNodeTypeMethods(nodeType, snapshotProcessorType) {
  var e_1, _a;
  var methods = [];
  for (var _i = 2; _i < arguments.length; _i++) {
    methods[_i - 2] = arguments[_i];
  }
  try {
    for (var methods_1 = __values(methods), methods_1_1 = methods_1.next(); !methods_1_1.done; methods_1_1 = methods_1.next()) {
      var method = methods_1_1.value;
      nodeType[method] = snapshotProcessorType[method].bind(snapshotProcessorType);
    }
  } catch (e_1_1) {
    e_1 = { error: e_1_1 };
  } finally {
    try {
      if (methods_1_1 && !methods_1_1.done && (_a = methods_1.return))
        _a.call(methods_1);
    } finally {
      if (e_1)
        throw e_1.error;
    }
  }
}
function snapshotProcessor(type, processors, name) {
  assertIsType(type, 1);
  if (devMode()) {
    if (processors.postProcessor && typeof processors.postProcessor !== "function") {
      throw fail("postSnapshotProcessor must be a function");
    }
    if (processors.preProcessor && typeof processors.preProcessor !== "function") {
      throw fail("preSnapshotProcessor must be a function");
    }
  }
  return new SnapshotProcessor(type, processors, name);
}
var needsIdentifierError = "Map.put can only be used to store complex values that have an identifier type attribute";
function tryCollectModelTypes(type, modelTypes) {
  var e_1, _a;
  var subtypes = type.getSubTypes();
  if (subtypes === cannotDetermineSubtype) {
    return false;
  }
  if (subtypes) {
    var subtypesArray = asArray(subtypes);
    try {
      for (var subtypesArray_1 = __values(subtypesArray), subtypesArray_1_1 = subtypesArray_1.next(); !subtypesArray_1_1.done; subtypesArray_1_1 = subtypesArray_1.next()) {
        var subtype = subtypesArray_1_1.value;
        if (!tryCollectModelTypes(subtype, modelTypes))
          return false;
      }
    } catch (e_1_1) {
      e_1 = { error: e_1_1 };
    } finally {
      try {
        if (subtypesArray_1_1 && !subtypesArray_1_1.done && (_a = subtypesArray_1.return))
          _a.call(subtypesArray_1);
      } finally {
        if (e_1)
          throw e_1.error;
      }
    }
  }
  if (type instanceof ModelType) {
    modelTypes.push(type);
  }
  return true;
}
var MapIdentifierMode;
(function(MapIdentifierMode2) {
  MapIdentifierMode2[MapIdentifierMode2["UNKNOWN"] = 0] = "UNKNOWN";
  MapIdentifierMode2[MapIdentifierMode2["YES"] = 1] = "YES";
  MapIdentifierMode2[MapIdentifierMode2["NO"] = 2] = "NO";
})(MapIdentifierMode || (MapIdentifierMode = {}));
var MSTMap = (
  /** @class */
  function(_super) {
    __extends(MSTMap2, _super);
    function MSTMap2(initialData) {
      return _super.call(this, initialData, observable.ref.enhancer) || this;
    }
    MSTMap2.prototype.get = function(key) {
      return _super.prototype.get.call(this, "" + key);
    };
    MSTMap2.prototype.has = function(key) {
      return _super.prototype.has.call(this, "" + key);
    };
    MSTMap2.prototype.delete = function(key) {
      return _super.prototype.delete.call(this, "" + key);
    };
    MSTMap2.prototype.set = function(key, value) {
      return _super.prototype.set.call(this, "" + key, value);
    };
    MSTMap2.prototype.put = function(value) {
      if (!value)
        throw fail$1("Map.put cannot be used to set empty values");
      if (isStateTreeNode(value)) {
        var node = getStateTreeNode(value);
        if (devMode()) {
          if (!node.identifierAttribute) {
            throw fail$1(needsIdentifierError);
          }
        }
        if (node.identifier === null) {
          throw fail$1(needsIdentifierError);
        }
        this.set(node.identifier, value);
        return value;
      } else if (!isMutable(value)) {
        throw fail$1("Map.put can only be used to store complex values");
      } else {
        var mapNode = getStateTreeNode(this);
        var mapType = mapNode.type;
        if (mapType.identifierMode !== MapIdentifierMode.YES) {
          throw fail$1(needsIdentifierError);
        }
        var idAttr = mapType.mapIdentifierAttribute;
        var id = value[idAttr];
        if (!isValidIdentifier(id)) {
          var newNode = this.put(mapType.getChildType().create(value, mapNode.environment));
          return this.put(getSnapshot(newNode));
        }
        var key = normalizeIdentifier(id);
        this.set(key, value);
        return this.get(key);
      }
    };
    return MSTMap2;
  }(ObservableMap)
);
var MapType = (
  /** @class */
  function(_super) {
    __extends(MapType2, _super);
    function MapType2(name, _subType, hookInitializers) {
      if (hookInitializers === void 0) {
        hookInitializers = [];
      }
      var _this = _super.call(this, name) || this;
      _this._subType = _subType;
      _this.identifierMode = MapIdentifierMode.UNKNOWN;
      _this.mapIdentifierAttribute = void 0;
      _this.flags = TypeFlags.Map;
      _this.hookInitializers = [];
      _this._determineIdentifierMode();
      _this.hookInitializers = hookInitializers;
      return _this;
    }
    MapType2.prototype.hooks = function(hooks) {
      var hookInitializers = this.hookInitializers.length > 0 ? this.hookInitializers.concat(hooks) : [hooks];
      return new MapType2(this.name, this._subType, hookInitializers);
    };
    MapType2.prototype.instantiate = function(parent, subpath, environment, initialValue) {
      this._determineIdentifierMode();
      return createObjectNode(this, parent, subpath, environment, initialValue);
    };
    MapType2.prototype._determineIdentifierMode = function() {
      if (this.identifierMode !== MapIdentifierMode.UNKNOWN) {
        return;
      }
      var modelTypes = [];
      if (tryCollectModelTypes(this._subType, modelTypes)) {
        var identifierAttribute_1 = void 0;
        modelTypes.forEach(function(type) {
          if (type.identifierAttribute) {
            if (identifierAttribute_1 && identifierAttribute_1 !== type.identifierAttribute) {
              throw fail$1("The objects in a map should all have the same identifier attribute, expected '" + identifierAttribute_1 + "', but child of type '" + type.name + "' declared attribute '" + type.identifierAttribute + "' as identifier");
            }
            identifierAttribute_1 = type.identifierAttribute;
          }
        });
        if (identifierAttribute_1) {
          this.identifierMode = MapIdentifierMode.YES;
          this.mapIdentifierAttribute = identifierAttribute_1;
        } else {
          this.identifierMode = MapIdentifierMode.NO;
        }
      }
    };
    MapType2.prototype.initializeChildNodes = function(objNode, initialSnapshot) {
      if (initialSnapshot === void 0) {
        initialSnapshot = {};
      }
      var subType = objNode.type._subType;
      var result = {};
      Object.keys(initialSnapshot).forEach(function(name) {
        result[name] = subType.instantiate(objNode, name, void 0, initialSnapshot[name]);
      });
      return result;
    };
    MapType2.prototype.createNewInstance = function(childNodes) {
      return new MSTMap(childNodes);
    };
    MapType2.prototype.finalizeNewInstance = function(node, instance) {
      interceptReads(instance, node.unbox);
      var type = node.type;
      type.hookInitializers.forEach(function(initializer) {
        var hooks = initializer(instance);
        Object.keys(hooks).forEach(function(name) {
          var hook = hooks[name];
          var actionInvoker = createActionInvoker(instance, name, hook);
          (!devMode() ? addHiddenFinalProp : addHiddenWritableProp)(instance, name, actionInvoker);
        });
      });
      intercept(instance, this.willChange);
      observe(instance, this.didChange);
    };
    MapType2.prototype.describe = function() {
      return "Map<string, " + this._subType.describe() + ">";
    };
    MapType2.prototype.getChildren = function(node) {
      return values(node.storedValue);
    };
    MapType2.prototype.getChildNode = function(node, key) {
      var childNode = node.storedValue.get("" + key);
      if (!childNode)
        throw fail$1("Not a child " + key);
      return childNode;
    };
    MapType2.prototype.willChange = function(change) {
      var node = getStateTreeNode(change.object);
      var key = change.name;
      node.assertWritable({ subpath: key });
      var mapType = node.type;
      var subType = mapType._subType;
      switch (change.type) {
        case "update":
          {
            var newValue = change.newValue;
            var oldValue = change.object.get(key);
            if (newValue === oldValue)
              return null;
            typecheckInternal(subType, newValue);
            change.newValue = subType.reconcile(node.getChildNode(key), change.newValue, node, key);
            mapType.processIdentifier(key, change.newValue);
          }
          break;
        case "add":
          {
            typecheckInternal(subType, change.newValue);
            change.newValue = subType.instantiate(node, key, void 0, change.newValue);
            mapType.processIdentifier(key, change.newValue);
          }
          break;
      }
      return change;
    };
    MapType2.prototype.processIdentifier = function(expected, node) {
      if (this.identifierMode === MapIdentifierMode.YES && node instanceof ObjectNode) {
        var identifier2 = node.identifier;
        if (identifier2 !== expected)
          throw fail$1("A map of objects containing an identifier should always store the object under their own identifier. Trying to store key '" + identifier2 + "', but expected: '" + expected + "'");
      }
    };
    MapType2.prototype.getSnapshot = function(node) {
      var res = {};
      node.getChildren().forEach(function(childNode) {
        res[childNode.subpath] = childNode.snapshot;
      });
      return res;
    };
    MapType2.prototype.processInitialSnapshot = function(childNodes) {
      var processed = {};
      Object.keys(childNodes).forEach(function(key) {
        processed[key] = childNodes[key].getSnapshot();
      });
      return processed;
    };
    MapType2.prototype.didChange = function(change) {
      var node = getStateTreeNode(change.object);
      switch (change.type) {
        case "update":
          return void node.emitPatch({
            op: "replace",
            path: escapeJsonPath(change.name),
            value: change.newValue.snapshot,
            oldValue: change.oldValue ? change.oldValue.snapshot : void 0
          }, node);
        case "add":
          return void node.emitPatch({
            op: "add",
            path: escapeJsonPath(change.name),
            value: change.newValue.snapshot,
            oldValue: void 0
          }, node);
        case "delete":
          var oldSnapshot = change.oldValue.snapshot;
          change.oldValue.die();
          return void node.emitPatch({
            op: "remove",
            path: escapeJsonPath(change.name),
            oldValue: oldSnapshot
          }, node);
      }
    };
    MapType2.prototype.applyPatchLocally = function(node, subpath, patch) {
      var target = node.storedValue;
      switch (patch.op) {
        case "add":
        case "replace":
          target.set(subpath, patch.value);
          break;
        case "remove":
          target.delete(subpath);
          break;
      }
    };
    MapType2.prototype.applySnapshot = function(node, snapshot) {
      typecheckInternal(this, snapshot);
      var target = node.storedValue;
      var currentKeys = {};
      Array.from(target.keys()).forEach(function(key2) {
        currentKeys[key2] = false;
      });
      if (snapshot) {
        for (var key in snapshot) {
          target.set(key, snapshot[key]);
          currentKeys["" + key] = true;
        }
      }
      Object.keys(currentKeys).forEach(function(key2) {
        if (currentKeys[key2] === false)
          target.delete(key2);
      });
    };
    MapType2.prototype.getChildType = function() {
      return this._subType;
    };
    MapType2.prototype.isValidSnapshot = function(value, context) {
      var _this = this;
      if (!isPlainObject(value)) {
        return typeCheckFailure(context, value, "Value is not a plain object");
      }
      return flattenTypeErrors(Object.keys(value).map(function(path) {
        return _this._subType.validate(value[path], getContextForPath(context, path, _this._subType));
      }));
    };
    MapType2.prototype.getDefaultSnapshot = function() {
      return EMPTY_OBJECT;
    };
    MapType2.prototype.removeChild = function(node, subpath) {
      node.storedValue.delete(subpath);
    };
    __decorate([
      action
    ], MapType2.prototype, "applySnapshot", null);
    return MapType2;
  }(ComplexType)
);
function map(subtype) {
  return new MapType("map<string, " + subtype.name + ">", subtype);
}
var ArrayType = (
  /** @class */
  function(_super) {
    __extends(ArrayType2, _super);
    function ArrayType2(name, _subType, hookInitializers) {
      if (hookInitializers === void 0) {
        hookInitializers = [];
      }
      var _this = _super.call(this, name) || this;
      _this._subType = _subType;
      _this.flags = TypeFlags.Array;
      _this.hookInitializers = [];
      _this.hookInitializers = hookInitializers;
      return _this;
    }
    ArrayType2.prototype.hooks = function(hooks) {
      var hookInitializers = this.hookInitializers.length > 0 ? this.hookInitializers.concat(hooks) : [hooks];
      return new ArrayType2(this.name, this._subType, hookInitializers);
    };
    ArrayType2.prototype.instantiate = function(parent, subpath, environment, initialValue) {
      return createObjectNode(this, parent, subpath, environment, initialValue);
    };
    ArrayType2.prototype.initializeChildNodes = function(objNode, snapshot) {
      if (snapshot === void 0) {
        snapshot = [];
      }
      var subType = objNode.type._subType;
      var result = {};
      snapshot.forEach(function(item, index) {
        var subpath = "" + index;
        result[subpath] = subType.instantiate(objNode, subpath, void 0, item);
      });
      return result;
    };
    ArrayType2.prototype.createNewInstance = function(childNodes) {
      return observable.array(convertChildNodesToArray(childNodes), mobxShallow);
    };
    ArrayType2.prototype.finalizeNewInstance = function(node, instance) {
      getAdministration(instance).dehancer = node.unbox;
      var type = node.type;
      type.hookInitializers.forEach(function(initializer) {
        var hooks = initializer(instance);
        Object.keys(hooks).forEach(function(name) {
          var hook = hooks[name];
          var actionInvoker = createActionInvoker(instance, name, hook);
          (!devMode() ? addHiddenFinalProp : addHiddenWritableProp)(instance, name, actionInvoker);
        });
      });
      intercept(instance, this.willChange);
      observe(instance, this.didChange);
    };
    ArrayType2.prototype.describe = function() {
      return this._subType.describe() + "[]";
    };
    ArrayType2.prototype.getChildren = function(node) {
      return node.storedValue.slice();
    };
    ArrayType2.prototype.getChildNode = function(node, key) {
      var index = Number(key);
      if (index < node.storedValue.length)
        return node.storedValue[index];
      throw fail$1("Not a child: " + key);
    };
    ArrayType2.prototype.willChange = function(change) {
      var node = getStateTreeNode(change.object);
      node.assertWritable({ subpath: "" + change.index });
      var subType = node.type._subType;
      var childNodes = node.getChildren();
      switch (change.type) {
        case "update":
          {
            if (change.newValue === change.object[change.index])
              return null;
            var updatedNodes = reconcileArrayChildren(node, subType, [childNodes[change.index]], [change.newValue], [change.index]);
            if (!updatedNodes) {
              return null;
            }
            change.newValue = updatedNodes[0];
          }
          break;
        case "splice":
          {
            var index_1 = change.index, removedCount = change.removedCount, added = change.added;
            var addedNodes = reconcileArrayChildren(node, subType, childNodes.slice(index_1, index_1 + removedCount), added, added.map(function(_, i2) {
              return index_1 + i2;
            }));
            if (!addedNodes) {
              return null;
            }
            change.added = addedNodes;
            for (var i = index_1 + removedCount; i < childNodes.length; i++) {
              childNodes[i].setParent(node, "" + (i + added.length - removedCount));
            }
          }
          break;
      }
      return change;
    };
    ArrayType2.prototype.getSnapshot = function(node) {
      return node.getChildren().map(function(childNode) {
        return childNode.snapshot;
      });
    };
    ArrayType2.prototype.processInitialSnapshot = function(childNodes) {
      var processed = [];
      Object.keys(childNodes).forEach(function(key) {
        processed.push(childNodes[key].getSnapshot());
      });
      return processed;
    };
    ArrayType2.prototype.didChange = function(change) {
      var node = getStateTreeNode(change.object);
      switch (change.type) {
        case "update":
          return void node.emitPatch({
            op: "replace",
            path: "" + change.index,
            value: change.newValue.snapshot,
            oldValue: change.oldValue ? change.oldValue.snapshot : void 0
          }, node);
        case "splice":
          for (var i = change.removedCount - 1; i >= 0; i--)
            node.emitPatch({
              op: "remove",
              path: "" + (change.index + i),
              oldValue: change.removed[i].snapshot
            }, node);
          for (var i = 0; i < change.addedCount; i++)
            node.emitPatch({
              op: "add",
              path: "" + (change.index + i),
              value: node.getChildNode("" + (change.index + i)).snapshot,
              oldValue: void 0
            }, node);
          return;
      }
    };
    ArrayType2.prototype.applyPatchLocally = function(node, subpath, patch) {
      var target = node.storedValue;
      var index = subpath === "-" ? target.length : Number(subpath);
      switch (patch.op) {
        case "replace":
          target[index] = patch.value;
          break;
        case "add":
          target.splice(index, 0, patch.value);
          break;
        case "remove":
          target.splice(index, 1);
          break;
      }
    };
    ArrayType2.prototype.applySnapshot = function(node, snapshot) {
      typecheckInternal(this, snapshot);
      var target = node.storedValue;
      target.replace(snapshot);
    };
    ArrayType2.prototype.getChildType = function() {
      return this._subType;
    };
    ArrayType2.prototype.isValidSnapshot = function(value, context) {
      var _this = this;
      if (!isArray(value)) {
        return typeCheckFailure(context, value, "Value is not an array");
      }
      return flattenTypeErrors(value.map(function(item, index) {
        return _this._subType.validate(item, getContextForPath(context, "" + index, _this._subType));
      }));
    };
    ArrayType2.prototype.getDefaultSnapshot = function() {
      return EMPTY_ARRAY;
    };
    ArrayType2.prototype.removeChild = function(node, subpath) {
      node.storedValue.splice(Number(subpath), 1);
    };
    __decorate([
      action
    ], ArrayType2.prototype, "applySnapshot", null);
    return ArrayType2;
  }(ComplexType)
);
function array(subtype) {
  assertIsType(subtype, 1);
  return new ArrayType(subtype.name + "[]", subtype);
}
function reconcileArrayChildren(parent, childType, oldNodes, newValues, newPaths) {
  var nothingChanged = true;
  for (var i = 0; ; i++) {
    var hasNewNode = i <= newValues.length - 1;
    var oldNode = oldNodes[i];
    var newValue = hasNewNode ? newValues[i] : void 0;
    var newPath = "" + newPaths[i];
    if (isNode(newValue))
      newValue = newValue.storedValue;
    if (!oldNode && !hasNewNode) {
      break;
    } else if (!hasNewNode) {
      nothingChanged = false;
      oldNodes.splice(i, 1);
      if (oldNode instanceof ObjectNode) {
        oldNode.createObservableInstanceIfNeeded();
      }
      oldNode.die();
      i--;
    } else if (!oldNode) {
      if (isStateTreeNode(newValue) && getStateTreeNode(newValue).parent === parent) {
        throw fail$1("Cannot add an object to a state tree if it is already part of the same or another state tree. Tried to assign an object to '" + parent.path + "/" + newPath + "', but it lives already at '" + getStateTreeNode(newValue).path + "'");
      }
      nothingChanged = false;
      var newNode = valueAsNode(childType, parent, newPath, newValue);
      oldNodes.splice(i, 0, newNode);
    } else if (areSame(oldNode, newValue)) {
      oldNodes[i] = valueAsNode(childType, parent, newPath, newValue, oldNode);
    } else {
      var oldMatch = void 0;
      for (var j = i; j < oldNodes.length; j++) {
        if (areSame(oldNodes[j], newValue)) {
          oldMatch = oldNodes.splice(j, 1)[0];
          break;
        }
      }
      nothingChanged = false;
      var newNode = valueAsNode(childType, parent, newPath, newValue, oldMatch);
      oldNodes.splice(i, 0, newNode);
    }
  }
  return nothingChanged ? null : oldNodes;
}
function valueAsNode(childType, parent, subpath, newValue, oldNode) {
  typecheckInternal(childType, newValue);
  function getNewNode() {
    if (isStateTreeNode(newValue)) {
      var childNode = getStateTreeNode(newValue);
      childNode.assertAlive(EMPTY_OBJECT);
      if (childNode.parent !== null && childNode.parent === parent) {
        childNode.setParent(parent, subpath);
        return childNode;
      }
    }
    if (oldNode) {
      return childType.reconcile(oldNode, newValue, parent, subpath);
    }
    return childType.instantiate(parent, subpath, void 0, newValue);
  }
  var newNode = getNewNode();
  if (oldNode && oldNode !== newNode) {
    if (oldNode instanceof ObjectNode) {
      oldNode.createObservableInstanceIfNeeded();
    }
    oldNode.die();
  }
  return newNode;
}
function areSame(oldNode, newValue) {
  if (!oldNode.isAlive) {
    return false;
  }
  if (isStateTreeNode(newValue)) {
    var newNode = getStateTreeNode(newValue);
    return newNode.isAlive && newNode === oldNode;
  }
  if (oldNode.snapshot === newValue) {
    return true;
  }
  return oldNode instanceof ObjectNode && oldNode.identifier !== null && oldNode.identifierAttribute && isPlainObject(newValue) && oldNode.identifier === normalizeIdentifier(newValue[oldNode.identifierAttribute]) && oldNode.type.is(newValue);
}
var PRE_PROCESS_SNAPSHOT = "preProcessSnapshot";
var POST_PROCESS_SNAPSHOT = "postProcessSnapshot";
function objectTypeToString() {
  return getStateTreeNode(this).toString();
}
var defaultObjectOptions = {
  name: "AnonymousModel",
  properties: {},
  initializers: EMPTY_ARRAY
};
function toPropertiesObject(declaredProps) {
  return Object.keys(declaredProps).reduce(function(props, key) {
    var _a, _b, _c;
    if (key in Hook)
      throw fail$1("Hook '" + key + "' was defined as property. Hooks should be defined as part of the actions");
    var descriptor = Object.getOwnPropertyDescriptor(props, key);
    if ("get" in descriptor) {
      throw fail$1("Getters are not supported as properties. Please use views instead");
    }
    var value = descriptor.value;
    if (value === null || value === void 0) {
      throw fail$1("The default value of an attribute cannot be null or undefined as the type cannot be inferred. Did you mean `types.maybe(someType)`?");
    } else if (isPrimitive(value)) {
      return Object.assign({}, props, (_a = {}, _a[key] = optional(getPrimitiveFactoryFromValue(value), value), _a));
    } else if (value instanceof MapType) {
      return Object.assign({}, props, (_b = {}, _b[key] = optional(value, {}), _b));
    } else if (value instanceof ArrayType) {
      return Object.assign({}, props, (_c = {}, _c[key] = optional(value, []), _c));
    } else if (isType(value)) {
      return props;
    } else if (devMode() && typeof value === "function") {
      throw fail$1("Invalid type definition for property '" + key + "', it looks like you passed a function. Did you forget to invoke it, or did you intend to declare a view / action?");
    } else if (devMode() && typeof value === "object") {
      throw fail$1("Invalid type definition for property '" + key + "', it looks like you passed an object. Try passing another model type or a types.frozen.");
    } else {
      throw fail$1("Invalid type definition for property '" + key + "', cannot infer a type from a value like '" + value + "' (" + typeof value + ")");
    }
  }, declaredProps);
}
var ModelType = (
  /** @class */
  function(_super) {
    __extends(ModelType2, _super);
    function ModelType2(opts) {
      var _this = _super.call(this, opts.name || defaultObjectOptions.name) || this;
      _this.flags = TypeFlags.Object;
      _this.named = function(name) {
        return _this.cloneAndEnhance({ name });
      };
      _this.props = function(properties) {
        return _this.cloneAndEnhance({ properties });
      };
      _this.preProcessSnapshot = function(preProcessor) {
        var currentPreprocessor = _this.preProcessor;
        if (!currentPreprocessor)
          return _this.cloneAndEnhance({ preProcessor });
        else
          return _this.cloneAndEnhance({
            preProcessor: function(snapshot) {
              return currentPreprocessor(preProcessor(snapshot));
            }
          });
      };
      _this.postProcessSnapshot = function(postProcessor) {
        var currentPostprocessor = _this.postProcessor;
        if (!currentPostprocessor)
          return _this.cloneAndEnhance({ postProcessor });
        else
          return _this.cloneAndEnhance({
            postProcessor: function(snapshot) {
              return postProcessor(currentPostprocessor(snapshot));
            }
          });
      };
      Object.assign(_this, defaultObjectOptions, opts);
      _this.properties = toPropertiesObject(_this.properties);
      freeze(_this.properties);
      _this.propertyNames = Object.keys(_this.properties);
      _this.identifierAttribute = _this._getIdentifierAttribute();
      return _this;
    }
    ModelType2.prototype._getIdentifierAttribute = function() {
      var identifierAttribute = void 0;
      this.forAllProps(function(propName, propType) {
        if (propType.flags & TypeFlags.Identifier) {
          if (identifierAttribute)
            throw fail$1("Cannot define property '" + propName + "' as object identifier, property '" + identifierAttribute + "' is already defined as identifier property");
          identifierAttribute = propName;
        }
      });
      return identifierAttribute;
    };
    ModelType2.prototype.cloneAndEnhance = function(opts) {
      return new ModelType2({
        name: opts.name || this.name,
        properties: Object.assign({}, this.properties, opts.properties),
        initializers: this.initializers.concat(opts.initializers || []),
        preProcessor: opts.preProcessor || this.preProcessor,
        postProcessor: opts.postProcessor || this.postProcessor
      });
    };
    ModelType2.prototype.actions = function(fn) {
      var _this = this;
      var actionInitializer = function(self) {
        _this.instantiateActions(self, fn(self));
        return self;
      };
      return this.cloneAndEnhance({ initializers: [actionInitializer] });
    };
    ModelType2.prototype.instantiateActions = function(self, actions) {
      if (!isPlainObject(actions))
        throw fail$1("actions initializer should return a plain object containing actions");
      Object.keys(actions).forEach(function(name) {
        if (name === PRE_PROCESS_SNAPSHOT)
          throw fail$1("Cannot define action '" + PRE_PROCESS_SNAPSHOT + "', it should be defined using 'type.preProcessSnapshot(fn)' instead");
        if (name === POST_PROCESS_SNAPSHOT)
          throw fail$1("Cannot define action '" + POST_PROCESS_SNAPSHOT + "', it should be defined using 'type.postProcessSnapshot(fn)' instead");
        var action2 = actions[name];
        var baseAction = self[name];
        if (name in Hook && baseAction) {
          var specializedAction_1 = action2;
          action2 = function() {
            baseAction.apply(null, arguments);
            specializedAction_1.apply(null, arguments);
          };
        }
        var middlewares = action2.$mst_middleware;
        var boundAction = action2.bind(actions);
        boundAction.$mst_middleware = middlewares;
        var actionInvoker = createActionInvoker(self, name, boundAction);
        actions[name] = actionInvoker;
        (!devMode() ? addHiddenFinalProp : addHiddenWritableProp)(self, name, actionInvoker);
      });
    };
    ModelType2.prototype.volatile = function(fn) {
      var _this = this;
      if (typeof fn !== "function") {
        throw fail$1("You passed an " + typeof fn + " to volatile state as an argument, when function is expected");
      }
      var stateInitializer = function(self) {
        _this.instantiateVolatileState(self, fn(self));
        return self;
      };
      return this.cloneAndEnhance({ initializers: [stateInitializer] });
    };
    ModelType2.prototype.instantiateVolatileState = function(self, state) {
      if (!isPlainObject(state))
        throw fail$1("volatile state initializer should return a plain object containing state");
      set(self, state);
    };
    ModelType2.prototype.extend = function(fn) {
      var _this = this;
      var initializer = function(self) {
        var _a = fn(self), actions = _a.actions, views = _a.views, state = _a.state, rest = __rest(_a, ["actions", "views", "state"]);
        for (var key in rest)
          throw fail$1("The `extend` function should return an object with a subset of the fields 'actions', 'views' and 'state'. Found invalid key '" + key + "'");
        if (state)
          _this.instantiateVolatileState(self, state);
        if (views)
          _this.instantiateViews(self, views);
        if (actions)
          _this.instantiateActions(self, actions);
        return self;
      };
      return this.cloneAndEnhance({ initializers: [initializer] });
    };
    ModelType2.prototype.views = function(fn) {
      var _this = this;
      var viewInitializer = function(self) {
        _this.instantiateViews(self, fn(self));
        return self;
      };
      return this.cloneAndEnhance({ initializers: [viewInitializer] });
    };
    ModelType2.prototype.instantiateViews = function(self, views) {
      if (!isPlainObject(views))
        throw fail$1("views initializer should return a plain object containing views");
      Object.keys(views).forEach(function(key) {
        var descriptor = Object.getOwnPropertyDescriptor(views, key);
        if ("get" in descriptor) {
          if (isComputedProp(self, key)) {
            var computedValue = getAdministration(self, key);
            computedValue.derivation = descriptor.get;
            computedValue.scope = self;
            if (descriptor.set)
              computedValue.setter = action(computedValue.name + "-setter", descriptor.set);
          } else {
            computed(self, key, descriptor, true);
          }
        } else if (typeof descriptor.value === "function") {
          (!devMode() ? addHiddenFinalProp : addHiddenWritableProp)(self, key, descriptor.value);
        } else {
          throw fail$1("A view member should either be a function or getter based property");
        }
      });
    };
    ModelType2.prototype.instantiate = function(parent, subpath, environment, initialValue) {
      var value = isStateTreeNode(initialValue) ? initialValue : this.applySnapshotPreProcessor(initialValue);
      return createObjectNode(this, parent, subpath, environment, value);
    };
    ModelType2.prototype.initializeChildNodes = function(objNode, initialSnapshot) {
      if (initialSnapshot === void 0) {
        initialSnapshot = {};
      }
      var type = objNode.type;
      var result = {};
      type.forAllProps(function(name, childType) {
        result[name] = childType.instantiate(objNode, name, void 0, initialSnapshot[name]);
      });
      return result;
    };
    ModelType2.prototype.createNewInstance = function(childNodes) {
      return observable.object(childNodes, EMPTY_OBJECT, mobxShallow);
    };
    ModelType2.prototype.finalizeNewInstance = function(node, instance) {
      addHiddenFinalProp(instance, "toString", objectTypeToString);
      this.forAllProps(function(name) {
        interceptReads(instance, name, node.unbox);
      });
      this.initializers.reduce(function(self, fn) {
        return fn(self);
      }, instance);
      intercept(instance, this.willChange);
      observe(instance, this.didChange);
    };
    ModelType2.prototype.willChange = function(chg) {
      var change = chg;
      var node = getStateTreeNode(change.object);
      var subpath = change.name;
      node.assertWritable({ subpath });
      var childType = node.type.properties[subpath];
      if (childType) {
        typecheckInternal(childType, change.newValue);
        change.newValue = childType.reconcile(node.getChildNode(subpath), change.newValue, node, subpath);
      }
      return change;
    };
    ModelType2.prototype.didChange = function(chg) {
      var change = chg;
      var childNode = getStateTreeNode(change.object);
      var childType = childNode.type.properties[change.name];
      if (!childType) {
        return;
      }
      var oldChildValue = change.oldValue ? change.oldValue.snapshot : void 0;
      childNode.emitPatch({
        op: "replace",
        path: escapeJsonPath(change.name),
        value: change.newValue.snapshot,
        oldValue: oldChildValue
      }, childNode);
    };
    ModelType2.prototype.getChildren = function(node) {
      var _this = this;
      var res = [];
      this.forAllProps(function(name) {
        res.push(_this.getChildNode(node, name));
      });
      return res;
    };
    ModelType2.prototype.getChildNode = function(node, key) {
      if (!(key in this.properties))
        throw fail$1("Not a value property: " + key);
      var childNode = getAdministration(node.storedValue, key).value;
      if (!childNode)
        throw fail$1("Node not available for property " + key);
      return childNode;
    };
    ModelType2.prototype.getSnapshot = function(node, applyPostProcess) {
      var _this = this;
      if (applyPostProcess === void 0) {
        applyPostProcess = true;
      }
      var res = {};
      this.forAllProps(function(name, type) {
        getAtom(node.storedValue, name).reportObserved();
        res[name] = _this.getChildNode(node, name).snapshot;
      });
      if (applyPostProcess) {
        return this.applySnapshotPostProcessor(res);
      }
      return res;
    };
    ModelType2.prototype.processInitialSnapshot = function(childNodes) {
      var processed = {};
      Object.keys(childNodes).forEach(function(key) {
        processed[key] = childNodes[key].getSnapshot();
      });
      return this.applySnapshotPostProcessor(processed);
    };
    ModelType2.prototype.applyPatchLocally = function(node, subpath, patch) {
      if (!(patch.op === "replace" || patch.op === "add")) {
        throw fail$1("object does not support operation " + patch.op);
      }
      node.storedValue[subpath] = patch.value;
    };
    ModelType2.prototype.applySnapshot = function(node, snapshot) {
      var preProcessedSnapshot = this.applySnapshotPreProcessor(snapshot);
      typecheckInternal(this, preProcessedSnapshot);
      this.forAllProps(function(name) {
        node.storedValue[name] = preProcessedSnapshot[name];
      });
    };
    ModelType2.prototype.applySnapshotPreProcessor = function(snapshot) {
      var processor = this.preProcessor;
      return processor ? processor.call(null, snapshot) : snapshot;
    };
    ModelType2.prototype.applySnapshotPostProcessor = function(snapshot) {
      var postProcessor = this.postProcessor;
      if (postProcessor)
        return postProcessor.call(null, snapshot);
      return snapshot;
    };
    ModelType2.prototype.getChildType = function(propertyName) {
      assertIsString(propertyName, 1);
      return this.properties[propertyName];
    };
    ModelType2.prototype.isValidSnapshot = function(value, context) {
      var _this = this;
      var snapshot = this.applySnapshotPreProcessor(value);
      if (!isPlainObject(snapshot)) {
        return typeCheckFailure(context, snapshot, "Value is not a plain object");
      }
      return flattenTypeErrors(this.propertyNames.map(function(key) {
        return _this.properties[key].validate(snapshot[key], getContextForPath(context, key, _this.properties[key]));
      }));
    };
    ModelType2.prototype.forAllProps = function(fn) {
      var _this = this;
      this.propertyNames.forEach(function(key) {
        return fn(key, _this.properties[key]);
      });
    };
    ModelType2.prototype.describe = function() {
      var _this = this;
      return "{ " + this.propertyNames.map(function(key) {
        return key + ": " + _this.properties[key].describe();
      }).join("; ") + " }";
    };
    ModelType2.prototype.getDefaultSnapshot = function() {
      return EMPTY_OBJECT;
    };
    ModelType2.prototype.removeChild = function(node, subpath) {
      node.storedValue[subpath] = void 0;
    };
    __decorate([
      action
    ], ModelType2.prototype, "applySnapshot", null);
    return ModelType2;
  }(ComplexType)
);
function model() {
  var args = [];
  for (var _i = 0; _i < arguments.length; _i++) {
    args[_i] = arguments[_i];
  }
  var name = typeof args[0] === "string" ? args.shift() : "AnonymousModel";
  var properties = args.shift() || {};
  return new ModelType({ name, properties });
}
function compose() {
  var args = [];
  for (var _i = 0; _i < arguments.length; _i++) {
    args[_i] = arguments[_i];
  }
  var hasTypename = typeof args[0] === "string";
  var typeName = hasTypename ? args[0] : "AnonymousModel";
  if (hasTypename) {
    args.shift();
  }
  if (devMode()) {
    args.forEach(function(type, i) {
      assertArg(type, isModelType, "mobx-state-tree model type", hasTypename ? i + 2 : i + 1);
    });
  }
  return args.reduce(function(prev, cur) {
    return prev.cloneAndEnhance({
      name: prev.name + "_" + cur.name,
      properties: cur.properties,
      initializers: cur.initializers,
      preProcessor: function(snapshot) {
        return cur.applySnapshotPreProcessor(prev.applySnapshotPreProcessor(snapshot));
      },
      postProcessor: function(snapshot) {
        return cur.applySnapshotPostProcessor(prev.applySnapshotPostProcessor(snapshot));
      }
    });
  }).named(typeName);
}
function isModelType(type) {
  return isType(type) && (type.flags & TypeFlags.Object) > 0;
}
var CoreType = (
  /** @class */
  function(_super) {
    __extends(CoreType2, _super);
    function CoreType2(name, flags, checker, initializer) {
      if (initializer === void 0) {
        initializer = identity;
      }
      var _this = _super.call(this, name) || this;
      _this.flags = flags;
      _this.checker = checker;
      _this.initializer = initializer;
      _this.flags = flags;
      return _this;
    }
    CoreType2.prototype.describe = function() {
      return this.name;
    };
    CoreType2.prototype.instantiate = function(parent, subpath, environment, initialValue) {
      return createScalarNode(this, parent, subpath, environment, initialValue);
    };
    CoreType2.prototype.createNewInstance = function(snapshot) {
      return this.initializer(snapshot);
    };
    CoreType2.prototype.isValidSnapshot = function(value, context) {
      if (isPrimitive(value) && this.checker(value)) {
        return typeCheckSuccess();
      }
      var typeName = this.name === "Date" ? "Date or a unix milliseconds timestamp" : this.name;
      return typeCheckFailure(context, value, "Value is not a " + typeName);
    };
    return CoreType2;
  }(SimpleType)
);
var string = new CoreType("string", TypeFlags.String, function(v) {
  return typeof v === "string";
});
var number = new CoreType("number", TypeFlags.Number, function(v) {
  return typeof v === "number";
});
var integer = new CoreType("integer", TypeFlags.Integer, function(v) {
  return isInteger(v);
});
var boolean = new CoreType("boolean", TypeFlags.Boolean, function(v) {
  return typeof v === "boolean";
});
var nullType = new CoreType("null", TypeFlags.Null, function(v) {
  return v === null;
});
var undefinedType = new CoreType("undefined", TypeFlags.Undefined, function(v) {
  return v === void 0;
});
var _DatePrimitive = new CoreType("Date", TypeFlags.Date, function(v) {
  return typeof v === "number" || v instanceof Date;
}, function(v) {
  return v instanceof Date ? v : new Date(v);
});
_DatePrimitive.getSnapshot = function(node) {
  return node.storedValue.getTime();
};
var DatePrimitive = _DatePrimitive;
function getPrimitiveFactoryFromValue(value) {
  switch (typeof value) {
    case "string":
      return string;
    case "number":
      return number;
    case "boolean":
      return boolean;
    case "object":
      if (value instanceof Date)
        return DatePrimitive;
  }
  throw fail$1("Cannot determine primitive type from value " + value);
}
function isPrimitiveType(type) {
  return isType(type) && (type.flags & (TypeFlags.String | TypeFlags.Number | TypeFlags.Integer | TypeFlags.Boolean | TypeFlags.Date)) > 0;
}
var Literal = (
  /** @class */
  function(_super) {
    __extends(Literal2, _super);
    function Literal2(value) {
      var _this = _super.call(this, JSON.stringify(value)) || this;
      _this.flags = TypeFlags.Literal;
      _this.value = value;
      return _this;
    }
    Literal2.prototype.instantiate = function(parent, subpath, environment, initialValue) {
      return createScalarNode(this, parent, subpath, environment, initialValue);
    };
    Literal2.prototype.describe = function() {
      return JSON.stringify(this.value);
    };
    Literal2.prototype.isValidSnapshot = function(value, context) {
      if (isPrimitive(value) && value === this.value) {
        return typeCheckSuccess();
      }
      return typeCheckFailure(context, value, "Value is not a literal " + JSON.stringify(this.value));
    };
    return Literal2;
  }(SimpleType)
);
function literal(value) {
  assertArg(value, isPrimitive, "primitive", 1);
  return new Literal(value);
}
var Refinement = (
  /** @class */
  function(_super) {
    __extends(Refinement2, _super);
    function Refinement2(name, _subtype, _predicate, _message) {
      var _this = _super.call(this, name) || this;
      _this._subtype = _subtype;
      _this._predicate = _predicate;
      _this._message = _message;
      return _this;
    }
    Object.defineProperty(Refinement2.prototype, "flags", {
      get: function() {
        return this._subtype.flags | TypeFlags.Refinement;
      },
      enumerable: false,
      configurable: true
    });
    Refinement2.prototype.describe = function() {
      return this.name;
    };
    Refinement2.prototype.instantiate = function(parent, subpath, environment, initialValue) {
      return this._subtype.instantiate(parent, subpath, environment, initialValue);
    };
    Refinement2.prototype.isAssignableFrom = function(type) {
      return this._subtype.isAssignableFrom(type);
    };
    Refinement2.prototype.isValidSnapshot = function(value, context) {
      var subtypeErrors = this._subtype.validate(value, context);
      if (subtypeErrors.length > 0)
        return subtypeErrors;
      var snapshot = isStateTreeNode(value) ? getStateTreeNode(value).snapshot : value;
      if (!this._predicate(snapshot)) {
        return typeCheckFailure(context, value, this._message(value));
      }
      return typeCheckSuccess();
    };
    Refinement2.prototype.reconcile = function(current, newValue, parent, subpath) {
      return this._subtype.reconcile(current, newValue, parent, subpath);
    };
    Refinement2.prototype.getSubTypes = function() {
      return this._subtype;
    };
    return Refinement2;
  }(BaseType)
);
function refinement() {
  var args = [];
  for (var _i = 0; _i < arguments.length; _i++) {
    args[_i] = arguments[_i];
  }
  var name = typeof args[0] === "string" ? args.shift() : isType(args[0]) ? args[0].name : null;
  var type = args[0];
  var predicate = args[1];
  var message = args[2] ? args[2] : function(v) {
    return "Value does not respect the refinement predicate";
  };
  assertIsType(type, [1, 2]);
  assertIsString(name, 1);
  assertIsFunction(predicate, [2, 3]);
  assertIsFunction(message, [3, 4]);
  return new Refinement(name, type, predicate, message);
}
function enumeration(name, options) {
  var realOptions = typeof name === "string" ? options : name;
  if (devMode()) {
    realOptions.forEach(function(option, i) {
      assertIsString(option, i + 1);
    });
  }
  var type = union.apply(void 0, __spread(realOptions.map(function(option) {
    return literal("" + option);
  })));
  if (typeof name === "string")
    type.name = name;
  return type;
}
var Union = (
  /** @class */
  function(_super) {
    __extends(Union2, _super);
    function Union2(name, _types, options) {
      var _this = _super.call(this, name) || this;
      _this._types = _types;
      _this._eager = true;
      options = __assign({ eager: true, dispatcher: void 0 }, options);
      _this._dispatcher = options.dispatcher;
      if (!options.eager)
        _this._eager = false;
      return _this;
    }
    Object.defineProperty(Union2.prototype, "flags", {
      get: function() {
        var result = TypeFlags.Union;
        this._types.forEach(function(type) {
          result |= type.flags;
        });
        return result;
      },
      enumerable: false,
      configurable: true
    });
    Union2.prototype.isAssignableFrom = function(type) {
      return this._types.some(function(subType) {
        return subType.isAssignableFrom(type);
      });
    };
    Union2.prototype.describe = function() {
      return "(" + this._types.map(function(factory) {
        return factory.describe();
      }).join(" | ") + ")";
    };
    Union2.prototype.instantiate = function(parent, subpath, environment, initialValue) {
      var type = this.determineType(initialValue, void 0);
      if (!type)
        throw fail$1("No matching type for union " + this.describe());
      return type.instantiate(parent, subpath, environment, initialValue);
    };
    Union2.prototype.reconcile = function(current, newValue, parent, subpath) {
      var type = this.determineType(newValue, current.type);
      if (!type)
        throw fail$1("No matching type for union " + this.describe());
      return type.reconcile(current, newValue, parent, subpath);
    };
    Union2.prototype.determineType = function(value, reconcileCurrentType) {
      if (this._dispatcher) {
        return this._dispatcher(value);
      }
      if (reconcileCurrentType) {
        if (reconcileCurrentType.is(value)) {
          return reconcileCurrentType;
        }
        return this._types.filter(function(t) {
          return t !== reconcileCurrentType;
        }).find(function(type) {
          return type.is(value);
        });
      } else {
        return this._types.find(function(type) {
          return type.is(value);
        });
      }
    };
    Union2.prototype.isValidSnapshot = function(value, context) {
      if (this._dispatcher) {
        return this._dispatcher(value).validate(value, context);
      }
      var allErrors = [];
      var applicableTypes = 0;
      for (var i = 0; i < this._types.length; i++) {
        var type = this._types[i];
        var errors = type.validate(value, context);
        if (errors.length === 0) {
          if (this._eager)
            return typeCheckSuccess();
          else
            applicableTypes++;
        } else {
          allErrors.push(errors);
        }
      }
      if (applicableTypes === 1)
        return typeCheckSuccess();
      return typeCheckFailure(context, value, "No type is applicable for the union").concat(flattenTypeErrors(allErrors));
    };
    Union2.prototype.getSubTypes = function() {
      return this._types;
    };
    return Union2;
  }(BaseType)
);
function union(optionsOrType) {
  var otherTypes = [];
  for (var _i = 1; _i < arguments.length; _i++) {
    otherTypes[_i - 1] = arguments[_i];
  }
  var options = isType(optionsOrType) ? void 0 : optionsOrType;
  var types2 = isType(optionsOrType) ? __spread([optionsOrType], otherTypes) : otherTypes;
  var name = "(" + types2.map(function(type) {
    return type.name;
  }).join(" | ") + ")";
  if (devMode()) {
    if (options) {
      assertArg(options, function(o) {
        return isPlainObject(o);
      }, "object { eager?: boolean, dispatcher?: Function }", 1);
    }
    types2.forEach(function(type, i) {
      assertIsType(type, options ? i + 2 : i + 1);
    });
  }
  return new Union(name, types2, options);
}
var OptionalValue = (
  /** @class */
  function(_super) {
    __extends(OptionalValue2, _super);
    function OptionalValue2(_subtype, _defaultValue, optionalValues) {
      var _this = _super.call(this, _subtype.name) || this;
      _this._subtype = _subtype;
      _this._defaultValue = _defaultValue;
      _this.optionalValues = optionalValues;
      return _this;
    }
    Object.defineProperty(OptionalValue2.prototype, "flags", {
      get: function() {
        return this._subtype.flags | TypeFlags.Optional;
      },
      enumerable: false,
      configurable: true
    });
    OptionalValue2.prototype.describe = function() {
      return this._subtype.describe() + "?";
    };
    OptionalValue2.prototype.instantiate = function(parent, subpath, environment, initialValue) {
      if (this.optionalValues.indexOf(initialValue) >= 0) {
        var defaultInstanceOrSnapshot = this.getDefaultInstanceOrSnapshot();
        return this._subtype.instantiate(parent, subpath, environment, defaultInstanceOrSnapshot);
      }
      return this._subtype.instantiate(parent, subpath, environment, initialValue);
    };
    OptionalValue2.prototype.reconcile = function(current, newValue, parent, subpath) {
      return this._subtype.reconcile(current, this.optionalValues.indexOf(newValue) < 0 && this._subtype.is(newValue) ? newValue : this.getDefaultInstanceOrSnapshot(), parent, subpath);
    };
    OptionalValue2.prototype.getDefaultInstanceOrSnapshot = function() {
      var defaultInstanceOrSnapshot = typeof this._defaultValue === "function" ? this._defaultValue() : this._defaultValue;
      if (typeof this._defaultValue === "function") {
        typecheckInternal(this, defaultInstanceOrSnapshot);
      }
      return defaultInstanceOrSnapshot;
    };
    OptionalValue2.prototype.isValidSnapshot = function(value, context) {
      if (this.optionalValues.indexOf(value) >= 0) {
        return typeCheckSuccess();
      }
      return this._subtype.validate(value, context);
    };
    OptionalValue2.prototype.isAssignableFrom = function(type) {
      return this._subtype.isAssignableFrom(type);
    };
    OptionalValue2.prototype.getSubTypes = function() {
      return this._subtype;
    };
    return OptionalValue2;
  }(BaseType)
);
function checkOptionalPreconditions(type, defaultValueOrFunction) {
  if (typeof defaultValueOrFunction !== "function" && isStateTreeNode(defaultValueOrFunction)) {
    throw fail$1("default value cannot be an instance, pass a snapshot or a function that creates an instance/snapshot instead");
  }
  assertIsType(type, 1);
  if (devMode()) {
    if (typeof defaultValueOrFunction !== "function") {
      typecheckInternal(type, defaultValueOrFunction);
    }
  }
}
function optional(type, defaultValueOrFunction, optionalValues) {
  checkOptionalPreconditions(type, defaultValueOrFunction);
  return new OptionalValue(type, defaultValueOrFunction, optionalValues ? optionalValues : undefinedAsOptionalValues);
}
var undefinedAsOptionalValues = [void 0];
var optionalUndefinedType = optional(undefinedType, void 0);
var optionalNullType = optional(nullType, null);
function maybe(type) {
  assertIsType(type, 1);
  return union(type, optionalUndefinedType);
}
function maybeNull(type) {
  assertIsType(type, 1);
  return union(type, optionalNullType);
}
var Late = (
  /** @class */
  function(_super) {
    __extends(Late2, _super);
    function Late2(name, _definition) {
      var _this = _super.call(this, name) || this;
      _this._definition = _definition;
      return _this;
    }
    Object.defineProperty(Late2.prototype, "flags", {
      get: function() {
        return (this._subType ? this._subType.flags : 0) | TypeFlags.Late;
      },
      enumerable: false,
      configurable: true
    });
    Late2.prototype.getSubType = function(mustSucceed) {
      if (!this._subType) {
        var t = void 0;
        try {
          t = this._definition();
        } catch (e) {
          if (e instanceof ReferenceError)
            t = void 0;
          else
            throw e;
        }
        if (mustSucceed && t === void 0)
          throw fail$1("Late type seems to be used too early, the definition (still) returns undefined");
        if (t) {
          if (devMode() && !isType(t))
            throw fail$1("Failed to determine subtype, make sure types.late returns a type definition.");
          this._subType = t;
        }
      }
      return this._subType;
    };
    Late2.prototype.instantiate = function(parent, subpath, environment, initialValue) {
      return this.getSubType(true).instantiate(parent, subpath, environment, initialValue);
    };
    Late2.prototype.reconcile = function(current, newValue, parent, subpath) {
      return this.getSubType(true).reconcile(current, newValue, parent, subpath);
    };
    Late2.prototype.describe = function() {
      var t = this.getSubType(false);
      return t ? t.name : "<uknown late type>";
    };
    Late2.prototype.isValidSnapshot = function(value, context) {
      var t = this.getSubType(false);
      if (!t) {
        return typeCheckSuccess();
      }
      return t.validate(value, context);
    };
    Late2.prototype.isAssignableFrom = function(type) {
      var t = this.getSubType(false);
      return t ? t.isAssignableFrom(type) : false;
    };
    Late2.prototype.getSubTypes = function() {
      var subtype = this.getSubType(false);
      return subtype ? subtype : cannotDetermineSubtype;
    };
    return Late2;
  }(BaseType)
);
function late(nameOrType, maybeType) {
  var name = typeof nameOrType === "string" ? nameOrType : "late(" + nameOrType.toString() + ")";
  var type = typeof nameOrType === "string" ? maybeType : nameOrType;
  if (devMode()) {
    if (!(typeof type === "function" && type.length === 0))
      throw fail$1("Invalid late type, expected a function with zero arguments that returns a type, got: " + type);
  }
  return new Late(name, type);
}
var Frozen = (
  /** @class */
  function(_super) {
    __extends(Frozen2, _super);
    function Frozen2(subType) {
      var _this = _super.call(this, subType ? "frozen(" + subType.name + ")" : "frozen") || this;
      _this.subType = subType;
      _this.flags = TypeFlags.Frozen;
      return _this;
    }
    Frozen2.prototype.describe = function() {
      return "<any immutable value>";
    };
    Frozen2.prototype.instantiate = function(parent, subpath, environment, value) {
      return createScalarNode(this, parent, subpath, environment, deepFreeze(value));
    };
    Frozen2.prototype.isValidSnapshot = function(value, context) {
      if (!isSerializable(value)) {
        return typeCheckFailure(context, value, "Value is not serializable and cannot be frozen");
      }
      if (this.subType)
        return this.subType.validate(value, context);
      return typeCheckSuccess();
    };
    return Frozen2;
  }(SimpleType)
);
var untypedFrozenInstance = new Frozen();
function frozen(arg) {
  if (arguments.length === 0)
    return untypedFrozenInstance;
  else if (isType(arg))
    return new Frozen(arg);
  else
    return optional(untypedFrozenInstance, arg);
}
function getInvalidationCause(hook) {
  switch (hook) {
    case Hook.beforeDestroy:
      return "destroy";
    case Hook.beforeDetach:
      return "detach";
    default:
      return void 0;
  }
}
var StoredReference = (
  /** @class */
  function() {
    function StoredReference2(value, targetType) {
      this.targetType = targetType;
      if (isValidIdentifier(value)) {
        this.identifier = value;
      } else if (isStateTreeNode(value)) {
        var targetNode = getStateTreeNode(value);
        if (!targetNode.identifierAttribute)
          throw fail$1("Can only store references with a defined identifier attribute.");
        var id = targetNode.unnormalizedIdentifier;
        if (id === null || id === void 0) {
          throw fail$1("Can only store references to tree nodes with a defined identifier.");
        }
        this.identifier = id;
      } else {
        throw fail$1("Can only store references to tree nodes or identifiers, got: '" + value + "'");
      }
    }
    StoredReference2.prototype.updateResolvedReference = function(node) {
      var normalizedId = normalizeIdentifier(this.identifier);
      var root = node.root;
      var lastCacheModification = root.identifierCache.getLastCacheModificationPerId(normalizedId);
      if (!this.resolvedReference || this.resolvedReference.lastCacheModification !== lastCacheModification) {
        var targetType = this.targetType;
        var target = root.identifierCache.resolve(targetType, normalizedId);
        if (!target) {
          throw new InvalidReferenceError("[mobx-state-tree] Failed to resolve reference '" + this.identifier + "' to type '" + this.targetType.name + "' (from node: " + node.path + ")");
        }
        this.resolvedReference = {
          node: target,
          lastCacheModification
        };
      }
    };
    Object.defineProperty(StoredReference2.prototype, "resolvedValue", {
      get: function() {
        this.updateResolvedReference(this.node);
        return this.resolvedReference.node.value;
      },
      enumerable: false,
      configurable: true
    });
    return StoredReference2;
  }()
);
var InvalidReferenceError = (
  /** @class */
  function(_super) {
    __extends(InvalidReferenceError2, _super);
    function InvalidReferenceError2(m) {
      var _this = _super.call(this, m) || this;
      Object.setPrototypeOf(_this, InvalidReferenceError2.prototype);
      return _this;
    }
    return InvalidReferenceError2;
  }(Error)
);
var BaseReferenceType = (
  /** @class */
  function(_super) {
    __extends(BaseReferenceType2, _super);
    function BaseReferenceType2(targetType, onInvalidated) {
      var _this = _super.call(this, "reference(" + targetType.name + ")") || this;
      _this.targetType = targetType;
      _this.onInvalidated = onInvalidated;
      _this.flags = TypeFlags.Reference;
      return _this;
    }
    BaseReferenceType2.prototype.describe = function() {
      return this.name;
    };
    BaseReferenceType2.prototype.isAssignableFrom = function(type) {
      return this.targetType.isAssignableFrom(type);
    };
    BaseReferenceType2.prototype.isValidSnapshot = function(value, context) {
      return isValidIdentifier(value) ? typeCheckSuccess() : typeCheckFailure(context, value, "Value is not a valid identifier, which is a string or a number");
    };
    BaseReferenceType2.prototype.fireInvalidated = function(cause, storedRefNode, referenceId, refTargetNode) {
      var storedRefParentNode = storedRefNode.parent;
      if (!storedRefParentNode || !storedRefParentNode.isAlive) {
        return;
      }
      var storedRefParentValue = storedRefParentNode.storedValue;
      if (!storedRefParentValue) {
        return;
      }
      this.onInvalidated({
        cause,
        parent: storedRefParentValue,
        invalidTarget: refTargetNode ? refTargetNode.storedValue : void 0,
        invalidId: referenceId,
        replaceRef: function(newRef) {
          applyPatch(storedRefNode.root.storedValue, {
            op: "replace",
            value: newRef,
            path: storedRefNode.path
          });
        },
        removeRef: function() {
          if (isModelType(storedRefParentNode.type)) {
            this.replaceRef(void 0);
          } else {
            applyPatch(storedRefNode.root.storedValue, {
              op: "remove",
              path: storedRefNode.path
            });
          }
        }
      });
    };
    BaseReferenceType2.prototype.addTargetNodeWatcher = function(storedRefNode, referenceId) {
      var _this = this;
      var refTargetValue = this.getValue(storedRefNode);
      if (!refTargetValue) {
        return void 0;
      }
      var refTargetNode = getStateTreeNode(refTargetValue);
      var hookHandler = function(_, refTargetNodeHook) {
        var cause = getInvalidationCause(refTargetNodeHook);
        if (!cause) {
          return;
        }
        _this.fireInvalidated(cause, storedRefNode, referenceId, refTargetNode);
      };
      var refTargetDetachHookDisposer = refTargetNode.registerHook(Hook.beforeDetach, hookHandler);
      var refTargetDestroyHookDisposer = refTargetNode.registerHook(Hook.beforeDestroy, hookHandler);
      return function() {
        refTargetDetachHookDisposer();
        refTargetDestroyHookDisposer();
      };
    };
    BaseReferenceType2.prototype.watchTargetNodeForInvalidations = function(storedRefNode, identifier2, customGetSet) {
      var _this = this;
      if (!this.onInvalidated) {
        return;
      }
      var onRefTargetDestroyedHookDisposer;
      storedRefNode.registerHook(Hook.beforeDestroy, function() {
        if (onRefTargetDestroyedHookDisposer) {
          onRefTargetDestroyedHookDisposer();
        }
      });
      var startWatching = function(sync) {
        if (onRefTargetDestroyedHookDisposer) {
          onRefTargetDestroyedHookDisposer();
        }
        var storedRefParentNode = storedRefNode.parent;
        var storedRefParentValue = storedRefParentNode && storedRefParentNode.storedValue;
        if (storedRefParentNode && storedRefParentNode.isAlive && storedRefParentValue) {
          var refTargetNodeExists = void 0;
          if (customGetSet) {
            refTargetNodeExists = !!customGetSet.get(identifier2, storedRefParentValue);
          } else {
            refTargetNodeExists = storedRefNode.root.identifierCache.has(_this.targetType, normalizeIdentifier(identifier2));
          }
          if (!refTargetNodeExists) {
            if (!sync) {
              _this.fireInvalidated("invalidSnapshotReference", storedRefNode, identifier2, null);
            }
          } else {
            onRefTargetDestroyedHookDisposer = _this.addTargetNodeWatcher(storedRefNode, identifier2);
          }
        }
      };
      if (storedRefNode.state === NodeLifeCycle.FINALIZED) {
        startWatching(true);
      } else {
        if (!storedRefNode.isRoot) {
          storedRefNode.root.registerHook(Hook.afterCreationFinalization, function() {
            if (storedRefNode.parent) {
              storedRefNode.parent.createObservableInstanceIfNeeded();
            }
          });
        }
        storedRefNode.registerHook(Hook.afterAttach, function() {
          startWatching(false);
        });
      }
    };
    return BaseReferenceType2;
  }(SimpleType)
);
var IdentifierReferenceType = (
  /** @class */
  function(_super) {
    __extends(IdentifierReferenceType2, _super);
    function IdentifierReferenceType2(targetType, onInvalidated) {
      return _super.call(this, targetType, onInvalidated) || this;
    }
    IdentifierReferenceType2.prototype.getValue = function(storedRefNode) {
      if (!storedRefNode.isAlive)
        return void 0;
      var storedRef = storedRefNode.storedValue;
      return storedRef.resolvedValue;
    };
    IdentifierReferenceType2.prototype.getSnapshot = function(storedRefNode) {
      var ref = storedRefNode.storedValue;
      return ref.identifier;
    };
    IdentifierReferenceType2.prototype.instantiate = function(parent, subpath, environment, initialValue) {
      var identifier2 = isStateTreeNode(initialValue) ? getIdentifier(initialValue) : initialValue;
      var storedRef = new StoredReference(initialValue, this.targetType);
      var storedRefNode = createScalarNode(this, parent, subpath, environment, storedRef);
      storedRef.node = storedRefNode;
      this.watchTargetNodeForInvalidations(storedRefNode, identifier2, void 0);
      return storedRefNode;
    };
    IdentifierReferenceType2.prototype.reconcile = function(current, newValue, parent, subpath) {
      if (!current.isDetaching && current.type === this) {
        var compareByValue = isStateTreeNode(newValue);
        var ref = current.storedValue;
        if (!compareByValue && ref.identifier === newValue || compareByValue && ref.resolvedValue === newValue) {
          current.setParent(parent, subpath);
          return current;
        }
      }
      var newNode = this.instantiate(parent, subpath, void 0, newValue);
      current.die();
      return newNode;
    };
    return IdentifierReferenceType2;
  }(BaseReferenceType)
);
var CustomReferenceType = (
  /** @class */
  function(_super) {
    __extends(CustomReferenceType2, _super);
    function CustomReferenceType2(targetType, options, onInvalidated) {
      var _this = _super.call(this, targetType, onInvalidated) || this;
      _this.options = options;
      return _this;
    }
    CustomReferenceType2.prototype.getValue = function(storedRefNode) {
      if (!storedRefNode.isAlive)
        return void 0;
      var referencedNode = this.options.get(storedRefNode.storedValue, storedRefNode.parent ? storedRefNode.parent.storedValue : null);
      return referencedNode;
    };
    CustomReferenceType2.prototype.getSnapshot = function(storedRefNode) {
      return storedRefNode.storedValue;
    };
    CustomReferenceType2.prototype.instantiate = function(parent, subpath, environment, newValue) {
      var identifier2 = isStateTreeNode(newValue) ? this.options.set(newValue, parent ? parent.storedValue : null) : newValue;
      var storedRefNode = createScalarNode(this, parent, subpath, environment, identifier2);
      this.watchTargetNodeForInvalidations(storedRefNode, identifier2, this.options);
      return storedRefNode;
    };
    CustomReferenceType2.prototype.reconcile = function(current, newValue, parent, subpath) {
      var newIdentifier = isStateTreeNode(newValue) ? this.options.set(newValue, current ? current.storedValue : null) : newValue;
      if (!current.isDetaching && current.type === this && current.storedValue === newIdentifier) {
        current.setParent(parent, subpath);
        return current;
      }
      var newNode = this.instantiate(parent, subpath, void 0, newIdentifier);
      current.die();
      return newNode;
    };
    return CustomReferenceType2;
  }(BaseReferenceType)
);
function reference(subType, options) {
  assertIsType(subType, 1);
  if (devMode()) {
    if (arguments.length === 2 && typeof arguments[1] === "string") {
      throw fail$1("References with base path are no longer supported. Please remove the base path.");
    }
  }
  var getSetOptions = options ? options : void 0;
  var onInvalidated = options ? options.onInvalidated : void 0;
  if (getSetOptions && (getSetOptions.get || getSetOptions.set)) {
    if (devMode()) {
      if (!getSetOptions.get || !getSetOptions.set) {
        throw fail$1("reference options must either contain both a 'get' and a 'set' method or none of them");
      }
    }
    return new CustomReferenceType(subType, {
      get: getSetOptions.get,
      set: getSetOptions.set
    }, onInvalidated);
  } else {
    return new IdentifierReferenceType(subType, onInvalidated);
  }
}
function safeReference(subType, options) {
  var refType = reference(subType, __assign(__assign({}, options), { onInvalidated: function(ev) {
    if (options && options.onInvalidated) {
      options.onInvalidated(ev);
    }
    ev.removeRef();
  } }));
  if (options && options.acceptsUndefined === false) {
    return refType;
  } else {
    return maybe(refType);
  }
}
var BaseIdentifierType = (
  /** @class */
  function(_super) {
    __extends(BaseIdentifierType2, _super);
    function BaseIdentifierType2(name, validType) {
      var _this = _super.call(this, name) || this;
      _this.validType = validType;
      _this.flags = TypeFlags.Identifier;
      return _this;
    }
    BaseIdentifierType2.prototype.instantiate = function(parent, subpath, environment, initialValue) {
      if (!parent || !(parent.type instanceof ModelType))
        throw fail$1("Identifier types can only be instantiated as direct child of a model type");
      return createScalarNode(this, parent, subpath, environment, initialValue);
    };
    BaseIdentifierType2.prototype.reconcile = function(current, newValue, parent, subpath) {
      if (current.storedValue !== newValue)
        throw fail$1("Tried to change identifier from '" + current.storedValue + "' to '" + newValue + "'. Changing identifiers is not allowed.");
      current.setParent(parent, subpath);
      return current;
    };
    BaseIdentifierType2.prototype.isValidSnapshot = function(value, context) {
      if (typeof value !== this.validType) {
        return typeCheckFailure(context, value, "Value is not a valid " + this.describe() + ", expected a " + this.validType);
      }
      return typeCheckSuccess();
    };
    return BaseIdentifierType2;
  }(SimpleType)
);
var IdentifierType = (
  /** @class */
  function(_super) {
    __extends(IdentifierType2, _super);
    function IdentifierType2() {
      var _this = _super.call(this, "identifier", "string") || this;
      _this.flags = TypeFlags.Identifier;
      return _this;
    }
    IdentifierType2.prototype.describe = function() {
      return "identifier";
    };
    return IdentifierType2;
  }(BaseIdentifierType)
);
var IdentifierNumberType = (
  /** @class */
  function(_super) {
    __extends(IdentifierNumberType2, _super);
    function IdentifierNumberType2() {
      return _super.call(this, "identifierNumber", "number") || this;
    }
    IdentifierNumberType2.prototype.getSnapshot = function(node) {
      return node.storedValue;
    };
    IdentifierNumberType2.prototype.describe = function() {
      return "identifierNumber";
    };
    return IdentifierNumberType2;
  }(BaseIdentifierType)
);
var identifier = new IdentifierType();
var identifierNumber = new IdentifierNumberType();
function normalizeIdentifier(id) {
  return "" + id;
}
function isValidIdentifier(id) {
  return typeof id === "string" || typeof id === "number";
}
function custom(options) {
  return new CustomType(options);
}
var CustomType = (
  /** @class */
  function(_super) {
    __extends(CustomType2, _super);
    function CustomType2(options) {
      var _this = _super.call(this, options.name) || this;
      _this.options = options;
      _this.flags = TypeFlags.Custom;
      return _this;
    }
    CustomType2.prototype.describe = function() {
      return this.name;
    };
    CustomType2.prototype.isValidSnapshot = function(value, context) {
      if (this.options.isTargetType(value))
        return typeCheckSuccess();
      var typeError = this.options.getValidationMessage(value);
      if (typeError) {
        return typeCheckFailure(context, value, "Invalid value for type '" + this.name + "': " + typeError);
      }
      return typeCheckSuccess();
    };
    CustomType2.prototype.getSnapshot = function(node) {
      return this.options.toSnapshot(node.storedValue);
    };
    CustomType2.prototype.instantiate = function(parent, subpath, environment, initialValue) {
      var valueToStore = this.options.isTargetType(initialValue) ? initialValue : this.options.fromSnapshot(initialValue, parent && parent.root.environment);
      return createScalarNode(this, parent, subpath, environment, valueToStore);
    };
    CustomType2.prototype.reconcile = function(current, value, parent, subpath) {
      var isSnapshot = !this.options.isTargetType(value);
      if (!current.isDetaching) {
        var unchanged = current.type === this && (isSnapshot ? value === current.snapshot : value === current.storedValue);
        if (unchanged) {
          current.setParent(parent, subpath);
          return current;
        }
      }
      var valueToStore = isSnapshot ? this.options.fromSnapshot(value, parent.root.environment) : value;
      var newNode = this.instantiate(parent, subpath, void 0, valueToStore);
      current.die();
      return newNode;
    };
    return CustomType2;
  }(SimpleType)
);
var types = {
  enumeration,
  model,
  compose,
  custom,
  reference,
  safeReference,
  union,
  optional,
  literal,
  maybe,
  maybeNull,
  refinement,
  string,
  boolean,
  number,
  integer,
  Date: DatePrimitive,
  map,
  array,
  frozen,
  identifier,
  identifierNumber,
  late,
  undefined: undefinedType,
  null: nullType,
  snapshotProcessor
};

export {
  getSnapshot,
  clone,
  destroy,
  isAlive,
  flow,
  types
};
/*! Bundled license information:

mobx-state-tree/dist/mobx-state-tree.module.js:
  (*! *****************************************************************************
  Copyright (c) Microsoft Corporation.
  
  Permission to use, copy, modify, and/or distribute this software for any
  purpose with or without fee is hereby granted.
  
  THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
  REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
  AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
  INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
  LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
  OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
  PERFORMANCE OF THIS SOFTWARE.
  ***************************************************************************** *)
*/
//# sourceMappingURL=chunk-GHYHD6QH.js.map
