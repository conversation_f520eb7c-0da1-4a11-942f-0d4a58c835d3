{"version": 3, "sources": ["../../amis/esm/renderers/AnchorNav.js"], "sourcesContent": ["/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __assign, __spreadArray, __read, __decorate, __metadata } from 'tslib';\nimport React from 'react';\nimport { isVisible, filter, autobind, Renderer } from 'amis-core';\nimport { AnchorNavSection, AnchorNav as AnchorNav$1 } from 'amis-ui';\n\nvar AnchorNav = /** @class */ (function (_super) {\n    __extends(AnchorNav, _super);\n    function AnchorNav(props) {\n        var _this = _super.call(this, props) || this;\n        // 设置默认激活项\n        var links = props.links;\n        var active = 0;\n        if (typeof props.active !== 'undefined') {\n            active = props.active;\n        }\n        else {\n            var section = _this.getActiveSection(links, props.active, null);\n            active =\n                section && section.href\n                    ? section.href\n                    : (links[0] && links[0].href) || 0;\n        }\n        _this.state = {\n            active: active\n        };\n        return _this;\n    }\n    // 获取激活的内容区\n    AnchorNav.prototype.getActiveSection = function (links, active, section) {\n        var _this = this;\n        if (section) {\n            return section;\n        }\n        links.forEach(function (link) {\n            if (link.href === active) {\n                section = link;\n            }\n            else {\n                if (link.children) {\n                    _this.getActiveSection(link.children, active, section);\n                }\n            }\n        });\n        return section;\n    };\n    AnchorNav.prototype.handleSelect = function (key) {\n        this.setState({\n            active: key\n        });\n    };\n    AnchorNav.prototype.locateTo = function (index) {\n        var links = this.props.links;\n        Array.isArray(links) &&\n            links[index] &&\n            this.setState({\n                active: links[index].href || index\n            });\n    };\n    AnchorNav.prototype.renderSections = function (links, parentIdx) {\n        var _this = this;\n        var _a = this.props, cx = _a.classnames, ns = _a.classPrefix, sectionRender = _a.sectionRender, render = _a.render, data = _a.data;\n        links = Array.isArray(links) ? links : [links];\n        var children = [];\n        links.forEach(function (section, index) {\n            if (isVisible(section, data)) {\n                // 若有子节点，key为parentIdx-index\n                var curIdx = (parentIdx ? parentIdx + '-' : '') + index;\n                children.push(\n                /** 内容区 */\n                React.createElement(AnchorNavSection, __assign({}, section, { title: filter(section.title, data), key: curIdx, name: section.href || curIdx }), _this.renderSection\n                    ? _this.renderSection(section, _this.props, curIdx)\n                    : sectionRender\n                        ? sectionRender(section, _this.props, curIdx)\n                        : render(\"section/\".concat(curIdx), section.body || '')));\n                if (section.children) {\n                    children.push.apply(children, __spreadArray([], __read(_this.renderSections(section.children, curIdx)), false));\n                }\n            }\n        });\n        return children.filter(function (item) { return !!item; });\n    };\n    AnchorNav.prototype.render = function () {\n        var _a = this.props, cx = _a.classnames, ns = _a.classPrefix, className = _a.className, style = _a.style, linkClassName = _a.linkClassName, sectionClassName = _a.sectionClassName, direction = _a.direction, sectionRender = _a.sectionRender, render = _a.render, data = _a.data;\n        var links = this.props.links;\n        if (!links) {\n            return null;\n        }\n        var children = this.renderSections(links);\n        return (React.createElement(AnchorNav$1, { classPrefix: ns, classnames: cx, className: className, style: style, linkClassName: linkClassName, sectionClassName: sectionClassName, onSelect: this.handleSelect, active: this.state.active, direction: direction }, children));\n    };\n    AnchorNav.defaultProps = {\n        className: '',\n        linkClassName: '',\n        sectionClassName: ''\n    };\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], AnchorNav.prototype, \"handleSelect\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Number]),\n        __metadata(\"design:returntype\", void 0)\n    ], AnchorNav.prototype, \"locateTo\", null);\n    return AnchorNav;\n}(React.Component));\nvar AnchorNavRenderer = /** @class */ (function (_super) {\n    __extends(AnchorNavRenderer, _super);\n    function AnchorNavRenderer() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    AnchorNavRenderer = __decorate([\n        Renderer({\n            type: 'anchor-nav'\n        })\n    ], AnchorNavRenderer);\n    return AnchorNavRenderer;\n}(AnchorNav));\n\nexport { AnchorNavRenderer, AnchorNav as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,mBAAkB;AAIlB,IAAI;AAAA;AAAA,EAA2B,SAAU,QAAQ;AAC7C,cAAUA,YAAW,MAAM;AAC3B,aAASA,WAAU,OAAO;AACtB,UAAI,QAAQ,OAAO,KAAK,MAAM,KAAK,KAAK;AAExC,UAAI,QAAQ,MAAM;AAClB,UAAI,SAAS;AACb,UAAI,OAAO,MAAM,WAAW,aAAa;AACrC,iBAAS,MAAM;AAAA,MACnB,OACK;AACD,YAAI,UAAU,MAAM,iBAAiB,OAAO,MAAM,QAAQ,IAAI;AAC9D,iBACI,WAAW,QAAQ,OACb,QAAQ,OACP,MAAM,CAAC,KAAK,MAAM,CAAC,EAAE,QAAS;AAAA,MAC7C;AACA,YAAM,QAAQ;AAAA,QACV;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAEA,IAAAA,WAAU,UAAU,mBAAmB,SAAU,OAAO,QAAQ,SAAS;AACrE,UAAI,QAAQ;AACZ,UAAI,SAAS;AACT,eAAO;AAAA,MACX;AACA,YAAM,QAAQ,SAAU,MAAM;AAC1B,YAAI,KAAK,SAAS,QAAQ;AACtB,oBAAU;AAAA,QACd,OACK;AACD,cAAI,KAAK,UAAU;AACf,kBAAM,iBAAiB,KAAK,UAAU,QAAQ,OAAO;AAAA,UACzD;AAAA,QACJ;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AACA,IAAAA,WAAU,UAAU,eAAe,SAAU,KAAK;AAC9C,WAAK,SAAS;AAAA,QACV,QAAQ;AAAA,MACZ,CAAC;AAAA,IACL;AACA,IAAAA,WAAU,UAAU,WAAW,SAAU,OAAO;AAC5C,UAAI,QAAQ,KAAK,MAAM;AACvB,YAAM,QAAQ,KAAK,KACf,MAAM,KAAK,KACX,KAAK,SAAS;AAAA,QACV,QAAQ,MAAM,KAAK,EAAE,QAAQ;AAAA,MACjC,CAAC;AAAA,IACT;AACA,IAAAA,WAAU,UAAU,iBAAiB,SAAU,OAAO,WAAW;AAC7D,UAAI,QAAQ;AACZ,UAAI,KAAK,KAAK,OAAO,KAAK,GAAG,YAAY,KAAK,GAAG,aAAa,gBAAgB,GAAG,eAAe,SAAS,GAAG,QAAQ,OAAO,GAAG;AAC9H,cAAQ,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAC7C,UAAI,WAAW,CAAC;AAChB,YAAM,QAAQ,SAAU,SAAS,OAAO;AACpC,YAAI,UAAU,SAAS,IAAI,GAAG;AAE1B,cAAI,UAAU,YAAY,YAAY,MAAM,MAAM;AAClD,mBAAS;AAAA;AAAA,YAET,aAAAC,QAAM,cAAc,kBAAkB,SAAS,CAAC,GAAG,SAAS,EAAE,OAAO,OAAO,QAAQ,OAAO,IAAI,GAAG,KAAK,QAAQ,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,MAAM,gBAChJ,MAAM,cAAc,SAAS,MAAM,OAAO,MAAM,IAChD,gBACI,cAAc,SAAS,MAAM,OAAO,MAAM,IAC1C,OAAO,WAAW,OAAO,MAAM,GAAG,QAAQ,QAAQ,EAAE,CAAC;AAAA,UAAC;AAChE,cAAI,QAAQ,UAAU;AAClB,qBAAS,KAAK,MAAM,UAAU,cAAc,CAAC,GAAG,OAAO,MAAM,eAAe,QAAQ,UAAU,MAAM,CAAC,GAAG,KAAK,CAAC;AAAA,UAClH;AAAA,QACJ;AAAA,MACJ,CAAC;AACD,aAAO,SAAS,OAAO,SAAU,MAAM;AAAE,eAAO,CAAC,CAAC;AAAA,MAAM,CAAC;AAAA,IAC7D;AACA,IAAAD,WAAU,UAAU,SAAS,WAAY;AACrC,UAAI,KAAK,KAAK,OAAO,KAAK,GAAG,YAAY,KAAK,GAAG,aAAa,YAAY,GAAG,WAAW,QAAQ,GAAG,OAAO,gBAAgB,GAAG,eAAe,mBAAmB,GAAG,kBAAkB,YAAY,GAAG,WAAW,gBAAgB,GAAG,eAAe,SAAS,GAAG,QAAQ,OAAO,GAAG;AAC9Q,UAAI,QAAQ,KAAK,MAAM;AACvB,UAAI,CAAC,OAAO;AACR,eAAO;AAAA,MACX;AACA,UAAI,WAAW,KAAK,eAAe,KAAK;AACxC,aAAQ,aAAAC,QAAM,cAAc,iBAAa,EAAE,aAAa,IAAI,YAAY,IAAI,WAAsB,OAAc,eAA8B,kBAAoC,UAAU,KAAK,cAAc,QAAQ,KAAK,MAAM,QAAQ,UAAqB,GAAG,QAAQ;AAAA,IAC9Q;AACA,IAAAD,WAAU,eAAe;AAAA,MACrB,WAAW;AAAA,MACX,eAAe;AAAA,MACf,kBAAkB;AAAA,IACtB;AACA,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,WAAU,WAAW,gBAAgB,IAAI;AAC5C,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,WAAU,WAAW,YAAY,IAAI;AACxC,WAAOA;AAAA,EACX,EAAE,aAAAC,QAAM,SAAS;AAAA;AACjB,IAAI;AAAA;AAAA,EAAmC,SAAU,QAAQ;AACrD,cAAUC,oBAAmB,MAAM;AACnC,aAASA,qBAAoB;AACzB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,qBAAoB,WAAW;AAAA,MAC3B,SAAS;AAAA,QACL,MAAM;AAAA,MACV,CAAC;AAAA,IACL,GAAGA,kBAAiB;AACpB,WAAOA;AAAA,EACX,EAAE,SAAS;AAAA;", "names": ["<PERSON><PERSON><PERSON><PERSON>", "React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}