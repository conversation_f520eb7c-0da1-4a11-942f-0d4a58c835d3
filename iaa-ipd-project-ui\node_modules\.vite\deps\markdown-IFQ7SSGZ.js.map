{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/markdown/markdown.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    comments: {\n        blockComment: ['<!--', '-->']\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '<', close: '>', notIn: ['string'] }\n    ],\n    surroundingPairs: [\n        { open: '(', close: ')' },\n        { open: '[', close: ']' },\n        { open: '`', close: '`' }\n    ],\n    folding: {\n        markers: {\n            start: new RegExp('^\\\\s*<!--\\\\s*#?region\\\\b.*-->'),\n            end: new RegExp('^\\\\s*<!--\\\\s*#?endregion\\\\b.*-->')\n        }\n    }\n};\nexport var language = {\n    defaultToken: '',\n    tokenPostfix: '.md',\n    // escape codes\n    control: /[\\\\`*_\\[\\]{}()#+\\-\\.!]/,\n    noncontrol: /[^\\\\`*_\\[\\]{}()#+\\-\\.!]/,\n    escapes: /\\\\(?:@control)/,\n    // escape codes for javascript/CSS strings\n    jsescapes: /\\\\(?:[btnfr\\\\\"']|[0-7][0-7]?|[0-3][0-7]{2})/,\n    // non matched elements\n    empty: [\n        'area',\n        'base',\n        'basefont',\n        'br',\n        'col',\n        'frame',\n        'hr',\n        'img',\n        'input',\n        'isindex',\n        'link',\n        'meta',\n        'param'\n    ],\n    tokenizer: {\n        root: [\n            // markdown tables\n            [/^\\s*\\|/, '@rematch', '@table_header'],\n            // headers (with #)\n            [/^(\\s{0,3})(#+)((?:[^\\\\#]|@escapes)+)((?:#+)?)/, ['white', 'keyword', 'keyword', 'keyword']],\n            // headers (with =)\n            [/^\\s*(=+|\\-+)\\s*$/, 'keyword'],\n            // headers (with ***)\n            [/^\\s*((\\*[ ]?)+)\\s*$/, 'meta.separator'],\n            // quote\n            [/^\\s*>+/, 'comment'],\n            // list (starting with * or number)\n            [/^\\s*([\\*\\-+:]|\\d+\\.)\\s/, 'keyword'],\n            // code block (4 spaces indent)\n            [/^(\\t|[ ]{4})[^ ].*$/, 'string'],\n            // code block (3 tilde)\n            [/^\\s*~~~\\s*((?:\\w|[\\/\\-#])+)?\\s*$/, { token: 'string', next: '@codeblock' }],\n            // github style code blocks (with backticks and language)\n            [\n                /^\\s*```\\s*((?:\\w|[\\/\\-#])+).*$/,\n                { token: 'string', next: '@codeblockgh', nextEmbedded: '$1' }\n            ],\n            // github style code blocks (with backticks but no language)\n            [/^\\s*```\\s*$/, { token: 'string', next: '@codeblock' }],\n            // markup within lines\n            { include: '@linecontent' }\n        ],\n        table_header: [\n            { include: '@table_common' },\n            [/[^\\|]+/, 'keyword.table.header'] // table header\n        ],\n        table_body: [{ include: '@table_common' }, { include: '@linecontent' }],\n        table_common: [\n            [/\\s*[\\-:]+\\s*/, { token: 'keyword', switchTo: 'table_body' }],\n            [/^\\s*\\|/, 'keyword.table.left'],\n            [/^\\s*[^\\|]/, '@rematch', '@pop'],\n            [/^\\s*$/, '@rematch', '@pop'],\n            [\n                /\\|/,\n                {\n                    cases: {\n                        '@eos': 'keyword.table.right',\n                        '@default': 'keyword.table.middle' // inner |\n                    }\n                }\n            ]\n        ],\n        codeblock: [\n            [/^\\s*~~~\\s*$/, { token: 'string', next: '@pop' }],\n            [/^\\s*```\\s*$/, { token: 'string', next: '@pop' }],\n            [/.*$/, 'variable.source']\n        ],\n        // github style code blocks\n        codeblockgh: [\n            [/```\\s*$/, { token: 'string', next: '@pop', nextEmbedded: '@pop' }],\n            [/[^`]+/, 'variable.source']\n        ],\n        linecontent: [\n            // escapes\n            [/&\\w+;/, 'string.escape'],\n            [/@escapes/, 'escape'],\n            // various markup\n            [/\\b__([^\\\\_]|@escapes|_(?!_))+__\\b/, 'strong'],\n            [/\\*\\*([^\\\\*]|@escapes|\\*(?!\\*))+\\*\\*/, 'strong'],\n            [/\\b_[^_]+_\\b/, 'emphasis'],\n            [/\\*([^\\\\*]|@escapes)+\\*/, 'emphasis'],\n            [/`([^\\\\`]|@escapes)+`/, 'variable'],\n            // links\n            [/\\{+[^}]+\\}+/, 'string.target'],\n            [/(!?\\[)((?:[^\\]\\\\]|@escapes)*)(\\]\\([^\\)]+\\))/, ['string.link', '', 'string.link']],\n            [/(!?\\[)((?:[^\\]\\\\]|@escapes)*)(\\])/, 'string.link'],\n            // or html\n            { include: 'html' }\n        ],\n        // Note: it is tempting to rather switch to the real HTML mode instead of building our own here\n        // but currently there is a limitation in Monarch that prevents us from doing it: The opening\n        // '<' would start the HTML mode, however there is no way to jump 1 character back to let the\n        // HTML mode also tokenize the opening angle bracket. Thus, even though we could jump to HTML,\n        // we cannot correctly tokenize it in that mode yet.\n        html: [\n            // html tags\n            [/<(\\w+)\\/>/, 'tag'],\n            [\n                /<(\\w+)/,\n                {\n                    cases: {\n                        '@empty': { token: 'tag', next: '@tag.$1' },\n                        '@default': { token: 'tag', next: '@tag.$1' }\n                    }\n                }\n            ],\n            [/<\\/(\\w+)\\s*>/, { token: 'tag' }],\n            [/<!--/, 'comment', '@comment']\n        ],\n        comment: [\n            [/[^<\\-]+/, 'comment.content'],\n            [/-->/, 'comment', '@pop'],\n            [/<!--/, 'comment.content.invalid'],\n            [/[<\\-]/, 'comment.content']\n        ],\n        // Almost full HTML tag matching, complete with embedded scripts & styles\n        tag: [\n            [/[ \\t\\r\\n]+/, 'white'],\n            [\n                /(type)(\\s*=\\s*)(\")([^\"]+)(\")/,\n                [\n                    'attribute.name.html',\n                    'delimiter.html',\n                    'string.html',\n                    { token: 'string.html', switchTo: '@tag.$S2.$4' },\n                    'string.html'\n                ]\n            ],\n            [\n                /(type)(\\s*=\\s*)(')([^']+)(')/,\n                [\n                    'attribute.name.html',\n                    'delimiter.html',\n                    'string.html',\n                    { token: 'string.html', switchTo: '@tag.$S2.$4' },\n                    'string.html'\n                ]\n            ],\n            [/(\\w+)(\\s*=\\s*)(\"[^\"]*\"|'[^']*')/, ['attribute.name.html', 'delimiter.html', 'string.html']],\n            [/\\w+/, 'attribute.name.html'],\n            [/\\/>/, 'tag', '@pop'],\n            [\n                />/,\n                {\n                    cases: {\n                        '$S2==style': {\n                            token: 'tag',\n                            switchTo: 'embeddedStyle',\n                            nextEmbedded: 'text/css'\n                        },\n                        '$S2==script': {\n                            cases: {\n                                $S3: {\n                                    token: 'tag',\n                                    switchTo: 'embeddedScript',\n                                    nextEmbedded: '$S3'\n                                },\n                                '@default': {\n                                    token: 'tag',\n                                    switchTo: 'embeddedScript',\n                                    nextEmbedded: 'text/javascript'\n                                }\n                            }\n                        },\n                        '@default': { token: 'tag', next: '@pop' }\n                    }\n                }\n            ]\n        ],\n        embeddedStyle: [\n            [/[^<]+/, ''],\n            [/<\\/style\\s*>/, { token: '@rematch', next: '@pop', nextEmbedded: '@pop' }],\n            [/</, '']\n        ],\n        embeddedScript: [\n            [/[^<]+/, ''],\n            [/<\\/script\\s*>/, { token: '@rematch', next: '@pop', nextEmbedded: '@pop' }],\n            [/</, '']\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,cAAc,CAAC,QAAQ,KAAK;AAAA,EAChC;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,EAC/C;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,SAAS;AAAA,IACL,SAAS;AAAA,MACL,OAAO,IAAI,OAAO,+BAA+B;AAAA,MACjD,KAAK,IAAI,OAAO,kCAAkC;AAAA,IACtD;AAAA,EACJ;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA;AAAA,EAEd,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,SAAS;AAAA;AAAA,EAET,WAAW;AAAA;AAAA,EAEX,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,WAAW;AAAA,IACP,MAAM;AAAA;AAAA,MAEF,CAAC,UAAU,YAAY,eAAe;AAAA;AAAA,MAEtC,CAAC,iDAAiD,CAAC,SAAS,WAAW,WAAW,SAAS,CAAC;AAAA;AAAA,MAE5F,CAAC,oBAAoB,SAAS;AAAA;AAAA,MAE9B,CAAC,uBAAuB,gBAAgB;AAAA;AAAA,MAExC,CAAC,UAAU,SAAS;AAAA;AAAA,MAEpB,CAAC,0BAA0B,SAAS;AAAA;AAAA,MAEpC,CAAC,uBAAuB,QAAQ;AAAA;AAAA,MAEhC,CAAC,oCAAoC,EAAE,OAAO,UAAU,MAAM,aAAa,CAAC;AAAA;AAAA,MAE5E;AAAA,QACI;AAAA,QACA,EAAE,OAAO,UAAU,MAAM,gBAAgB,cAAc,KAAK;AAAA,MAChE;AAAA;AAAA,MAEA,CAAC,eAAe,EAAE,OAAO,UAAU,MAAM,aAAa,CAAC;AAAA;AAAA,MAEvD,EAAE,SAAS,eAAe;AAAA,IAC9B;AAAA,IACA,cAAc;AAAA,MACV,EAAE,SAAS,gBAAgB;AAAA,MAC3B,CAAC,UAAU,sBAAsB;AAAA;AAAA,IACrC;AAAA,IACA,YAAY,CAAC,EAAE,SAAS,gBAAgB,GAAG,EAAE,SAAS,eAAe,CAAC;AAAA,IACtE,cAAc;AAAA,MACV,CAAC,gBAAgB,EAAE,OAAO,WAAW,UAAU,aAAa,CAAC;AAAA,MAC7D,CAAC,UAAU,oBAAoB;AAAA,MAC/B,CAAC,aAAa,YAAY,MAAM;AAAA,MAChC,CAAC,SAAS,YAAY,MAAM;AAAA,MAC5B;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ;AAAA,YACR,YAAY;AAAA;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,MACP,CAAC,eAAe,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,MACjD,CAAC,eAAe,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,MACjD,CAAC,OAAO,iBAAiB;AAAA,IAC7B;AAAA;AAAA,IAEA,aAAa;AAAA,MACT,CAAC,WAAW,EAAE,OAAO,UAAU,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,MACnE,CAAC,SAAS,iBAAiB;AAAA,IAC/B;AAAA,IACA,aAAa;AAAA;AAAA,MAET,CAAC,SAAS,eAAe;AAAA,MACzB,CAAC,YAAY,QAAQ;AAAA;AAAA,MAErB,CAAC,qCAAqC,QAAQ;AAAA,MAC9C,CAAC,uCAAuC,QAAQ;AAAA,MAChD,CAAC,eAAe,UAAU;AAAA,MAC1B,CAAC,0BAA0B,UAAU;AAAA,MACrC,CAAC,wBAAwB,UAAU;AAAA;AAAA,MAEnC,CAAC,eAAe,eAAe;AAAA,MAC/B,CAAC,+CAA+C,CAAC,eAAe,IAAI,aAAa,CAAC;AAAA,MAClF,CAAC,qCAAqC,aAAa;AAAA;AAAA,MAEnD,EAAE,SAAS,OAAO;AAAA,IACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,MAAM;AAAA;AAAA,MAEF,CAAC,aAAa,KAAK;AAAA,MACnB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,UAAU,EAAE,OAAO,OAAO,MAAM,UAAU;AAAA,YAC1C,YAAY,EAAE,OAAO,OAAO,MAAM,UAAU;AAAA,UAChD;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,CAAC,gBAAgB,EAAE,OAAO,MAAM,CAAC;AAAA,MACjC,CAAC,QAAQ,WAAW,UAAU;AAAA,IAClC;AAAA,IACA,SAAS;AAAA,MACL,CAAC,WAAW,iBAAiB;AAAA,MAC7B,CAAC,OAAO,WAAW,MAAM;AAAA,MACzB,CAAC,QAAQ,yBAAyB;AAAA,MAClC,CAAC,SAAS,iBAAiB;AAAA,IAC/B;AAAA;AAAA,IAEA,KAAK;AAAA,MACD,CAAC,cAAc,OAAO;AAAA,MACtB;AAAA,QACI;AAAA,QACA;AAAA,UACI;AAAA,UACA;AAAA,UACA;AAAA,UACA,EAAE,OAAO,eAAe,UAAU,cAAc;AAAA,UAChD;AAAA,QACJ;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI;AAAA,UACA;AAAA,UACA;AAAA,UACA,EAAE,OAAO,eAAe,UAAU,cAAc;AAAA,UAChD;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,CAAC,mCAAmC,CAAC,uBAAuB,kBAAkB,aAAa,CAAC;AAAA,MAC5F,CAAC,OAAO,qBAAqB;AAAA,MAC7B,CAAC,OAAO,OAAO,MAAM;AAAA,MACrB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,cAAc;AAAA,cACV,OAAO;AAAA,cACP,UAAU;AAAA,cACV,cAAc;AAAA,YAClB;AAAA,YACA,eAAe;AAAA,cACX,OAAO;AAAA,gBACH,KAAK;AAAA,kBACD,OAAO;AAAA,kBACP,UAAU;AAAA,kBACV,cAAc;AAAA,gBAClB;AAAA,gBACA,YAAY;AAAA,kBACR,OAAO;AAAA,kBACP,UAAU;AAAA,kBACV,cAAc;AAAA,gBAClB;AAAA,cACJ;AAAA,YACJ;AAAA,YACA,YAAY,EAAE,OAAO,OAAO,MAAM,OAAO;AAAA,UAC7C;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,eAAe;AAAA,MACX,CAAC,SAAS,EAAE;AAAA,MACZ,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,MAC1E,CAAC,KAAK,EAAE;AAAA,IACZ;AAAA,IACA,gBAAgB;AAAA,MACZ,CAAC,SAAS,EAAE;AAAA,MACZ,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,MAC3E,CAAC,KAAK,EAAE;AAAA,IACZ;AAAA,EACJ;AACJ;", "names": []}