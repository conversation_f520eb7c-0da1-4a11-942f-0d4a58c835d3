{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/dart/dart.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    comments: {\n        lineComment: '//',\n        blockComment: ['/*', '*/']\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: \"'\", close: \"'\", notIn: ['string', 'comment'] },\n        { open: '\"', close: '\"', notIn: ['string'] },\n        { open: '`', close: '`', notIn: ['string', 'comment'] },\n        { open: '/**', close: ' */', notIn: ['string'] }\n    ],\n    surroundingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '<', close: '>' },\n        { open: \"'\", close: \"'\" },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: '`', close: '`' }\n    ],\n    folding: {\n        markers: {\n            start: /^\\s*\\s*#?region\\b/,\n            end: /^\\s*\\s*#?endregion\\b/\n        }\n    }\n};\nexport var language = {\n    defaultToken: 'invalid',\n    tokenPostfix: '.dart',\n    keywords: [\n        'abstract',\n        'dynamic',\n        'implements',\n        'show',\n        'as',\n        'else',\n        'import',\n        'static',\n        'assert',\n        'enum',\n        'in',\n        'super',\n        'async',\n        'export',\n        'interface',\n        'switch',\n        'await',\n        'extends',\n        'is',\n        'sync',\n        'break',\n        'external',\n        'library',\n        'this',\n        'case',\n        'factory',\n        'mixin',\n        'throw',\n        'catch',\n        'false',\n        'new',\n        'true',\n        'class',\n        'final',\n        'null',\n        'try',\n        'const',\n        'finally',\n        'on',\n        'typedef',\n        'continue',\n        'for',\n        'operator',\n        'var',\n        'covariant',\n        'Function',\n        'part',\n        'void',\n        'default',\n        'get',\n        'rethrow',\n        'while',\n        'deferred',\n        'hide',\n        'return',\n        'with',\n        'do',\n        'if',\n        'set',\n        'yield'\n    ],\n    typeKeywords: ['int', 'double', 'String', 'bool'],\n    operators: [\n        '+',\n        '-',\n        '*',\n        '/',\n        '~/',\n        '%',\n        '++',\n        '--',\n        '==',\n        '!=',\n        '>',\n        '<',\n        '>=',\n        '<=',\n        '=',\n        '-=',\n        '/=',\n        '%=',\n        '>>=',\n        '^=',\n        '+=',\n        '*=',\n        '~/=',\n        '<<=',\n        '&=',\n        '!=',\n        '||',\n        '&&',\n        '&',\n        '|',\n        '^',\n        '~',\n        '<<',\n        '>>',\n        '!',\n        '>>>',\n        '??',\n        '?',\n        ':',\n        '|='\n    ],\n    // we include these common regular expressions\n    symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n    escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n    digits: /\\d+(_+\\d+)*/,\n    octaldigits: /[0-7]+(_+[0-7]+)*/,\n    binarydigits: /[0-1]+(_+[0-1]+)*/,\n    hexdigits: /[[0-9a-fA-F]+(_+[0-9a-fA-F]+)*/,\n    regexpctl: /[(){}\\[\\]\\$\\^|\\-*+?\\.]/,\n    regexpesc: /\\\\(?:[bBdDfnrstvwWn0\\\\\\/]|@regexpctl|c[A-Z]|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4})/,\n    // The main tokenizer for our languages\n    tokenizer: {\n        root: [[/[{}]/, 'delimiter.bracket'], { include: 'common' }],\n        common: [\n            // identifiers and keywords\n            [\n                /[a-z_$][\\w$]*/,\n                {\n                    cases: {\n                        '@typeKeywords': 'type.identifier',\n                        '@keywords': 'keyword',\n                        '@default': 'identifier'\n                    }\n                }\n            ],\n            [/[A-Z_$][\\w\\$]*/, 'type.identifier'],\n            // [/[A-Z][\\w\\$]*/, 'identifier'],\n            // whitespace\n            { include: '@whitespace' },\n            // regular expression: ensure it is terminated before beginning (otherwise it is an opeator)\n            [\n                /\\/(?=([^\\\\\\/]|\\\\.)+\\/([gimsuy]*)(\\s*)(\\.|;|,|\\)|\\]|\\}|$))/,\n                { token: 'regexp', bracket: '@open', next: '@regexp' }\n            ],\n            // @ annotations.\n            [/@[a-zA-Z]+/, 'annotation'],\n            // variable\n            // delimiters and operators\n            [/[()\\[\\]]/, '@brackets'],\n            [/[<>](?!@symbols)/, '@brackets'],\n            [/!(?=([^=]|$))/, 'delimiter'],\n            [\n                /@symbols/,\n                {\n                    cases: {\n                        '@operators': 'delimiter',\n                        '@default': ''\n                    }\n                }\n            ],\n            // numbers\n            [/(@digits)[eE]([\\-+]?(@digits))?/, 'number.float'],\n            [/(@digits)\\.(@digits)([eE][\\-+]?(@digits))?/, 'number.float'],\n            [/0[xX](@hexdigits)n?/, 'number.hex'],\n            [/0[oO]?(@octaldigits)n?/, 'number.octal'],\n            [/0[bB](@binarydigits)n?/, 'number.binary'],\n            [/(@digits)n?/, 'number'],\n            // delimiter: after number because of .\\d floats\n            [/[;,.]/, 'delimiter'],\n            // strings\n            [/\"([^\"\\\\]|\\\\.)*$/, 'string.invalid'],\n            [/'([^'\\\\]|\\\\.)*$/, 'string.invalid'],\n            [/\"/, 'string', '@string_double'],\n            [/'/, 'string', '@string_single']\n            //   [/[a-zA-Z]+/, \"variable\"]\n        ],\n        whitespace: [\n            [/[ \\t\\r\\n]+/, ''],\n            [/\\/\\*\\*(?!\\/)/, 'comment.doc', '@jsdoc'],\n            [/\\/\\*/, 'comment', '@comment'],\n            [/\\/\\/\\/.*$/, 'comment.doc'],\n            [/\\/\\/.*$/, 'comment']\n        ],\n        comment: [\n            [/[^\\/*]+/, 'comment'],\n            [/\\*\\//, 'comment', '@pop'],\n            [/[\\/*]/, 'comment']\n        ],\n        jsdoc: [\n            [/[^\\/*]+/, 'comment.doc'],\n            [/\\*\\//, 'comment.doc', '@pop'],\n            [/[\\/*]/, 'comment.doc']\n        ],\n        // We match regular expression quite precisely\n        regexp: [\n            [\n                /(\\{)(\\d+(?:,\\d*)?)(\\})/,\n                ['regexp.escape.control', 'regexp.escape.control', 'regexp.escape.control']\n            ],\n            [\n                /(\\[)(\\^?)(?=(?:[^\\]\\\\\\/]|\\\\.)+)/,\n                ['regexp.escape.control', { token: 'regexp.escape.control', next: '@regexrange' }]\n            ],\n            [/(\\()(\\?:|\\?=|\\?!)/, ['regexp.escape.control', 'regexp.escape.control']],\n            [/[()]/, 'regexp.escape.control'],\n            [/@regexpctl/, 'regexp.escape.control'],\n            [/[^\\\\\\/]/, 'regexp'],\n            [/@regexpesc/, 'regexp.escape'],\n            [/\\\\\\./, 'regexp.invalid'],\n            [/(\\/)([gimsuy]*)/, [{ token: 'regexp', bracket: '@close', next: '@pop' }, 'keyword.other']]\n        ],\n        regexrange: [\n            [/-/, 'regexp.escape.control'],\n            [/\\^/, 'regexp.invalid'],\n            [/@regexpesc/, 'regexp.escape'],\n            [/[^\\]]/, 'regexp'],\n            [\n                /\\]/,\n                {\n                    token: 'regexp.escape.control',\n                    next: '@pop',\n                    bracket: '@close'\n                }\n            ]\n        ],\n        string_double: [\n            [/[^\\\\\"\\$]+/, 'string'],\n            [/[^\\\\\"]+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/\\\\./, 'string.escape.invalid'],\n            [/\"/, 'string', '@pop'],\n            [/\\$\\w+/, 'identifier']\n        ],\n        string_single: [\n            [/[^\\\\'\\$]+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/\\\\./, 'string.escape.invalid'],\n            [/'/, 'string', '@pop'],\n            [/\\$\\w+/, 'identifier']\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC7B;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,OAAO,OAAO,OAAO,OAAO,CAAC,QAAQ,EAAE;AAAA,EACnD;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,SAAS;AAAA,IACL,SAAS;AAAA,MACL,OAAO;AAAA,MACP,KAAK;AAAA,IACT;AAAA,EACJ;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,cAAc,CAAC,OAAO,UAAU,UAAU,MAAM;AAAA,EAChD,WAAW;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA;AAAA,EAEA,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,cAAc;AAAA,EACd,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA;AAAA,EAEX,WAAW;AAAA,IACP,MAAM,CAAC,CAAC,QAAQ,mBAAmB,GAAG,EAAE,SAAS,SAAS,CAAC;AAAA,IAC3D,QAAQ;AAAA;AAAA,MAEJ;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,iBAAiB;AAAA,YACjB,aAAa;AAAA,YACb,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,CAAC,kBAAkB,iBAAiB;AAAA;AAAA;AAAA,MAGpC,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB;AAAA,QACI;AAAA,QACA,EAAE,OAAO,UAAU,SAAS,SAAS,MAAM,UAAU;AAAA,MACzD;AAAA;AAAA,MAEA,CAAC,cAAc,YAAY;AAAA;AAAA;AAAA,MAG3B,CAAC,YAAY,WAAW;AAAA,MACxB,CAAC,oBAAoB,WAAW;AAAA,MAChC,CAAC,iBAAiB,WAAW;AAAA,MAC7B;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,cAAc;AAAA,YACd,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,CAAC,mCAAmC,cAAc;AAAA,MAClD,CAAC,8CAA8C,cAAc;AAAA,MAC7D,CAAC,uBAAuB,YAAY;AAAA,MACpC,CAAC,0BAA0B,cAAc;AAAA,MACzC,CAAC,0BAA0B,eAAe;AAAA,MAC1C,CAAC,eAAe,QAAQ;AAAA;AAAA,MAExB,CAAC,SAAS,WAAW;AAAA;AAAA,MAErB,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,KAAK,UAAU,gBAAgB;AAAA,MAChC,CAAC,KAAK,UAAU,gBAAgB;AAAA;AAAA,IAEpC;AAAA,IACA,YAAY;AAAA,MACR,CAAC,cAAc,EAAE;AAAA,MACjB,CAAC,gBAAgB,eAAe,QAAQ;AAAA,MACxC,CAAC,QAAQ,WAAW,UAAU;AAAA,MAC9B,CAAC,aAAa,aAAa;AAAA,MAC3B,CAAC,WAAW,SAAS;AAAA,IACzB;AAAA,IACA,SAAS;AAAA,MACL,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,SAAS,SAAS;AAAA,IACvB;AAAA,IACA,OAAO;AAAA,MACH,CAAC,WAAW,aAAa;AAAA,MACzB,CAAC,QAAQ,eAAe,MAAM;AAAA,MAC9B,CAAC,SAAS,aAAa;AAAA,IAC3B;AAAA;AAAA,IAEA,QAAQ;AAAA,MACJ;AAAA,QACI;AAAA,QACA,CAAC,yBAAyB,yBAAyB,uBAAuB;AAAA,MAC9E;AAAA,MACA;AAAA,QACI;AAAA,QACA,CAAC,yBAAyB,EAAE,OAAO,yBAAyB,MAAM,cAAc,CAAC;AAAA,MACrF;AAAA,MACA,CAAC,qBAAqB,CAAC,yBAAyB,uBAAuB,CAAC;AAAA,MACxE,CAAC,QAAQ,uBAAuB;AAAA,MAChC,CAAC,cAAc,uBAAuB;AAAA,MACtC,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,cAAc,eAAe;AAAA,MAC9B,CAAC,QAAQ,gBAAgB;AAAA,MACzB,CAAC,mBAAmB,CAAC,EAAE,OAAO,UAAU,SAAS,UAAU,MAAM,OAAO,GAAG,eAAe,CAAC;AAAA,IAC/F;AAAA,IACA,YAAY;AAAA,MACR,CAAC,KAAK,uBAAuB;AAAA,MAC7B,CAAC,MAAM,gBAAgB;AAAA,MACvB,CAAC,cAAc,eAAe;AAAA,MAC9B,CAAC,SAAS,QAAQ;AAAA,MAClB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,SAAS;AAAA,QACb;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,eAAe;AAAA,MACX,CAAC,aAAa,QAAQ;AAAA,MACtB,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,UAAU,MAAM;AAAA,MACtB,CAAC,SAAS,YAAY;AAAA,IAC1B;AAAA,IACA,eAAe;AAAA,MACX,CAAC,aAAa,QAAQ;AAAA,MACtB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,UAAU,MAAM;AAAA,MACtB,CAAC,SAAS,YAAY;AAAA,IAC1B;AAAA,EACJ;AACJ;", "names": []}