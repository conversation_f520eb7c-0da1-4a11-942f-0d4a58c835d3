Object.defineProperty(exports,"__esModule",{value:!0}),exports.useCellView=useCellView;var _vue=require("vue"),_xeUtils=_interopRequireDefault(require("xe-utils"));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function useCellView(t){var e=(0,_vue.computed)(()=>{var e=t.renderParams;return e.column}),r=(0,_vue.computed)(()=>{var e=t.renderParams;return e.row}),u=(0,_vue.computed)(()=>{var e=t.renderOpts;return e.props||{}});return{currColumn:e,currRow:r,cellModel:(0,_vue.computed)({get(){var e=t.renderParams,{row:e,column:r}=e;return _xeUtils.default.get(e,r.field)},set(e){var r=t.renderParams,{row:r,column:u}=r;return _xeUtils.default.set(r,u.field,e)}}),cellOptions:u}}