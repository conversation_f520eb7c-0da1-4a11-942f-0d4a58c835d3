{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/scss/scss.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    wordPattern: /(#?-?\\d*\\.\\d\\w*%?)|([@$#!.:]?[\\w-?]+%?)|[@#!.]/g,\n    comments: {\n        blockComment: ['/*', '*/'],\n        lineComment: '//'\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}', notIn: ['string', 'comment'] },\n        { open: '[', close: ']', notIn: ['string', 'comment'] },\n        { open: '(', close: ')', notIn: ['string', 'comment'] },\n        { open: '\"', close: '\"', notIn: ['string', 'comment'] },\n        { open: \"'\", close: \"'\", notIn: ['string', 'comment'] }\n    ],\n    surroundingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" }\n    ],\n    folding: {\n        markers: {\n            start: new RegExp('^\\\\s*\\\\/\\\\*\\\\s*#region\\\\b\\\\s*(.*?)\\\\s*\\\\*\\\\/'),\n            end: new RegExp('^\\\\s*\\\\/\\\\*\\\\s*#endregion\\\\b.*\\\\*\\\\/')\n        }\n    }\n};\nexport var language = {\n    defaultToken: '',\n    tokenPostfix: '.scss',\n    ws: '[ \\t\\n\\r\\f]*',\n    identifier: '-?-?([a-zA-Z]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))([\\\\w\\\\-]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))*',\n    brackets: [\n        { open: '{', close: '}', token: 'delimiter.curly' },\n        { open: '[', close: ']', token: 'delimiter.bracket' },\n        { open: '(', close: ')', token: 'delimiter.parenthesis' },\n        { open: '<', close: '>', token: 'delimiter.angle' }\n    ],\n    tokenizer: {\n        root: [{ include: '@selector' }],\n        selector: [\n            { include: '@comments' },\n            { include: '@import' },\n            { include: '@variabledeclaration' },\n            { include: '@warndebug' },\n            ['[@](include)', { token: 'keyword', next: '@includedeclaration' }],\n            [\n                '[@](keyframes|-webkit-keyframes|-moz-keyframes|-o-keyframes)',\n                { token: 'keyword', next: '@keyframedeclaration' }\n            ],\n            ['[@](page|content|font-face|-moz-document)', { token: 'keyword' }],\n            ['[@](charset|namespace)', { token: 'keyword', next: '@declarationbody' }],\n            ['[@](function)', { token: 'keyword', next: '@functiondeclaration' }],\n            ['[@](mixin)', { token: 'keyword', next: '@mixindeclaration' }],\n            ['url(\\\\-prefix)?\\\\(', { token: 'meta', next: '@urldeclaration' }],\n            { include: '@controlstatement' },\n            { include: '@selectorname' },\n            ['[&\\\\*]', 'tag'],\n            ['[>\\\\+,]', 'delimiter'],\n            ['\\\\[', { token: 'delimiter.bracket', next: '@selectorattribute' }],\n            ['{', { token: 'delimiter.curly', next: '@selectorbody' }]\n        ],\n        selectorbody: [\n            ['[*_]?@identifier@ws:(?=(\\\\s|\\\\d|[^{;}]*[;}]))', 'attribute.name', '@rulevalue'],\n            { include: '@selector' },\n            ['[@](extend)', { token: 'keyword', next: '@extendbody' }],\n            ['[@](return)', { token: 'keyword', next: '@declarationbody' }],\n            ['}', { token: 'delimiter.curly', next: '@pop' }]\n        ],\n        selectorname: [\n            ['#{', { token: 'meta', next: '@variableinterpolation' }],\n            ['(\\\\.|#(?=[^{])|%|(@identifier)|:)+', 'tag'] // selector (.foo, div, ...)\n        ],\n        selectorattribute: [{ include: '@term' }, [']', { token: 'delimiter.bracket', next: '@pop' }]],\n        term: [\n            { include: '@comments' },\n            ['url(\\\\-prefix)?\\\\(', { token: 'meta', next: '@urldeclaration' }],\n            { include: '@functioninvocation' },\n            { include: '@numbers' },\n            { include: '@strings' },\n            { include: '@variablereference' },\n            ['(and\\\\b|or\\\\b|not\\\\b)', 'operator'],\n            { include: '@name' },\n            ['([<>=\\\\+\\\\-\\\\*\\\\/\\\\^\\\\|\\\\~,])', 'operator'],\n            [',', 'delimiter'],\n            ['!default', 'literal'],\n            ['\\\\(', { token: 'delimiter.parenthesis', next: '@parenthizedterm' }]\n        ],\n        rulevalue: [\n            { include: '@term' },\n            ['!important', 'literal'],\n            [';', 'delimiter', '@pop'],\n            ['{', { token: 'delimiter.curly', switchTo: '@nestedproperty' }],\n            ['(?=})', { token: '', next: '@pop' }] // missing semicolon\n        ],\n        nestedproperty: [\n            ['[*_]?@identifier@ws:', 'attribute.name', '@rulevalue'],\n            { include: '@comments' },\n            ['}', { token: 'delimiter.curly', next: '@pop' }]\n        ],\n        warndebug: [['[@](warn|debug)', { token: 'keyword', next: '@declarationbody' }]],\n        import: [['[@](import)', { token: 'keyword', next: '@declarationbody' }]],\n        variabledeclaration: [\n            // sass variables\n            ['\\\\$@identifier@ws:', 'variable.decl', '@declarationbody']\n        ],\n        urldeclaration: [\n            { include: '@strings' },\n            ['[^)\\r\\n]+', 'string'],\n            ['\\\\)', { token: 'meta', next: '@pop' }]\n        ],\n        parenthizedterm: [\n            { include: '@term' },\n            ['\\\\)', { token: 'delimiter.parenthesis', next: '@pop' }]\n        ],\n        declarationbody: [\n            { include: '@term' },\n            [';', 'delimiter', '@pop'],\n            ['(?=})', { token: '', next: '@pop' }] // missing semicolon\n        ],\n        extendbody: [\n            { include: '@selectorname' },\n            ['!optional', 'literal'],\n            [';', 'delimiter', '@pop'],\n            ['(?=})', { token: '', next: '@pop' }] // missing semicolon\n        ],\n        variablereference: [\n            // sass variable reference\n            ['\\\\$@identifier', 'variable.ref'],\n            ['\\\\.\\\\.\\\\.', 'operator'],\n            ['#{', { token: 'meta', next: '@variableinterpolation' }] // sass var resolve\n        ],\n        variableinterpolation: [\n            { include: '@variablereference' },\n            ['}', { token: 'meta', next: '@pop' }]\n        ],\n        comments: [\n            ['\\\\/\\\\*', 'comment', '@comment'],\n            ['\\\\/\\\\/+.*', 'comment']\n        ],\n        comment: [\n            ['\\\\*\\\\/', 'comment', '@pop'],\n            ['.', 'comment']\n        ],\n        name: [['@identifier', 'attribute.value']],\n        numbers: [\n            ['(\\\\d*\\\\.)?\\\\d+([eE][\\\\-+]?\\\\d+)?', { token: 'number', next: '@units' }],\n            ['#[0-9a-fA-F_]+(?!\\\\w)', 'number.hex']\n        ],\n        units: [\n            [\n                '(em|ex|ch|rem|vmin|vmax|vw|vh|vm|cm|mm|in|px|pt|pc|deg|grad|rad|turn|s|ms|Hz|kHz|%)?',\n                'number',\n                '@pop'\n            ]\n        ],\n        functiondeclaration: [\n            ['@identifier@ws\\\\(', { token: 'meta', next: '@parameterdeclaration' }],\n            ['{', { token: 'delimiter.curly', switchTo: '@functionbody' }]\n        ],\n        mixindeclaration: [\n            // mixin with parameters\n            ['@identifier@ws\\\\(', { token: 'meta', next: '@parameterdeclaration' }],\n            // mixin without parameters\n            ['@identifier', 'meta'],\n            ['{', { token: 'delimiter.curly', switchTo: '@selectorbody' }]\n        ],\n        parameterdeclaration: [\n            ['\\\\$@identifier@ws:', 'variable.decl'],\n            ['\\\\.\\\\.\\\\.', 'operator'],\n            [',', 'delimiter'],\n            { include: '@term' },\n            ['\\\\)', { token: 'meta', next: '@pop' }]\n        ],\n        includedeclaration: [\n            { include: '@functioninvocation' },\n            ['@identifier', 'meta'],\n            [';', 'delimiter', '@pop'],\n            ['(?=})', { token: '', next: '@pop' }],\n            ['{', { token: 'delimiter.curly', switchTo: '@selectorbody' }]\n        ],\n        keyframedeclaration: [\n            ['@identifier', 'meta'],\n            ['{', { token: 'delimiter.curly', switchTo: '@keyframebody' }]\n        ],\n        keyframebody: [\n            { include: '@term' },\n            ['{', { token: 'delimiter.curly', next: '@selectorbody' }],\n            ['}', { token: 'delimiter.curly', next: '@pop' }]\n        ],\n        controlstatement: [\n            [\n                '[@](if|else|for|while|each|media)',\n                { token: 'keyword.flow', next: '@controlstatementdeclaration' }\n            ]\n        ],\n        controlstatementdeclaration: [\n            ['(in|from|through|if|to)\\\\b', { token: 'keyword.flow' }],\n            { include: '@term' },\n            ['{', { token: 'delimiter.curly', switchTo: '@selectorbody' }]\n        ],\n        functionbody: [\n            ['[@](return)', { token: 'keyword' }],\n            { include: '@variabledeclaration' },\n            { include: '@term' },\n            { include: '@controlstatement' },\n            [';', 'delimiter'],\n            ['}', { token: 'delimiter.curly', next: '@pop' }]\n        ],\n        functioninvocation: [['@identifier\\\\(', { token: 'meta', next: '@functionarguments' }]],\n        functionarguments: [\n            ['\\\\$@identifier@ws:', 'attribute.name'],\n            ['[,]', 'delimiter'],\n            { include: '@term' },\n            ['\\\\)', { token: 'meta', next: '@pop' }]\n        ],\n        strings: [\n            ['~?\"', { token: 'string.delimiter', next: '@stringenddoublequote' }],\n            [\"~?'\", { token: 'string.delimiter', next: '@stringendquote' }]\n        ],\n        stringenddoublequote: [\n            ['\\\\\\\\.', 'string'],\n            ['\"', { token: 'string.delimiter', next: '@pop' }],\n            ['.', 'string']\n        ],\n        stringendquote: [\n            ['\\\\\\\\.', 'string'],\n            [\"'\", { token: 'string.delimiter', next: '@pop' }],\n            ['.', 'string']\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,aAAa;AAAA,EACb,UAAU;AAAA,IACN,cAAc,CAAC,MAAM,IAAI;AAAA,IACzB,aAAa;AAAA,EACjB;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EAC1D;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,SAAS;AAAA,IACL,SAAS;AAAA,MACL,OAAO,IAAI,OAAO,8CAA8C;AAAA,MAChE,KAAK,IAAI,OAAO,sCAAsC;AAAA,IAC1D;AAAA,EACJ;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,IAAI;AAAA,EACJ,YAAY;AAAA,EACZ,UAAU;AAAA,IACN,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,oBAAoB;AAAA,IACpD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,EACtD;AAAA,EACA,WAAW;AAAA,IACP,MAAM,CAAC,EAAE,SAAS,YAAY,CAAC;AAAA,IAC/B,UAAU;AAAA,MACN,EAAE,SAAS,YAAY;AAAA,MACvB,EAAE,SAAS,UAAU;AAAA,MACrB,EAAE,SAAS,uBAAuB;AAAA,MAClC,EAAE,SAAS,aAAa;AAAA,MACxB,CAAC,gBAAgB,EAAE,OAAO,WAAW,MAAM,sBAAsB,CAAC;AAAA,MAClE;AAAA,QACI;AAAA,QACA,EAAE,OAAO,WAAW,MAAM,uBAAuB;AAAA,MACrD;AAAA,MACA,CAAC,6CAA6C,EAAE,OAAO,UAAU,CAAC;AAAA,MAClE,CAAC,0BAA0B,EAAE,OAAO,WAAW,MAAM,mBAAmB,CAAC;AAAA,MACzE,CAAC,iBAAiB,EAAE,OAAO,WAAW,MAAM,uBAAuB,CAAC;AAAA,MACpE,CAAC,cAAc,EAAE,OAAO,WAAW,MAAM,oBAAoB,CAAC;AAAA,MAC9D,CAAC,sBAAsB,EAAE,OAAO,QAAQ,MAAM,kBAAkB,CAAC;AAAA,MACjE,EAAE,SAAS,oBAAoB;AAAA,MAC/B,EAAE,SAAS,gBAAgB;AAAA,MAC3B,CAAC,UAAU,KAAK;AAAA,MAChB,CAAC,WAAW,WAAW;AAAA,MACvB,CAAC,OAAO,EAAE,OAAO,qBAAqB,MAAM,qBAAqB,CAAC;AAAA,MAClE,CAAC,KAAK,EAAE,OAAO,mBAAmB,MAAM,gBAAgB,CAAC;AAAA,IAC7D;AAAA,IACA,cAAc;AAAA,MACV,CAAC,iDAAiD,kBAAkB,YAAY;AAAA,MAChF,EAAE,SAAS,YAAY;AAAA,MACvB,CAAC,eAAe,EAAE,OAAO,WAAW,MAAM,cAAc,CAAC;AAAA,MACzD,CAAC,eAAe,EAAE,OAAO,WAAW,MAAM,mBAAmB,CAAC;AAAA,MAC9D,CAAC,KAAK,EAAE,OAAO,mBAAmB,MAAM,OAAO,CAAC;AAAA,IACpD;AAAA,IACA,cAAc;AAAA,MACV,CAAC,MAAM,EAAE,OAAO,QAAQ,MAAM,yBAAyB,CAAC;AAAA,MACxD,CAAC,sCAAsC,KAAK;AAAA;AAAA,IAChD;AAAA,IACA,mBAAmB,CAAC,EAAE,SAAS,QAAQ,GAAG,CAAC,KAAK,EAAE,OAAO,qBAAqB,MAAM,OAAO,CAAC,CAAC;AAAA,IAC7F,MAAM;AAAA,MACF,EAAE,SAAS,YAAY;AAAA,MACvB,CAAC,sBAAsB,EAAE,OAAO,QAAQ,MAAM,kBAAkB,CAAC;AAAA,MACjE,EAAE,SAAS,sBAAsB;AAAA,MACjC,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,qBAAqB;AAAA,MAChC,CAAC,yBAAyB,UAAU;AAAA,MACpC,EAAE,SAAS,QAAQ;AAAA,MACnB,CAAC,iCAAiC,UAAU;AAAA,MAC5C,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY,SAAS;AAAA,MACtB,CAAC,OAAO,EAAE,OAAO,yBAAyB,MAAM,mBAAmB,CAAC;AAAA,IACxE;AAAA,IACA,WAAW;AAAA,MACP,EAAE,SAAS,QAAQ;AAAA,MACnB,CAAC,cAAc,SAAS;AAAA,MACxB,CAAC,KAAK,aAAa,MAAM;AAAA,MACzB,CAAC,KAAK,EAAE,OAAO,mBAAmB,UAAU,kBAAkB,CAAC;AAAA,MAC/D,CAAC,SAAS,EAAE,OAAO,IAAI,MAAM,OAAO,CAAC;AAAA;AAAA,IACzC;AAAA,IACA,gBAAgB;AAAA,MACZ,CAAC,wBAAwB,kBAAkB,YAAY;AAAA,MACvD,EAAE,SAAS,YAAY;AAAA,MACvB,CAAC,KAAK,EAAE,OAAO,mBAAmB,MAAM,OAAO,CAAC;AAAA,IACpD;AAAA,IACA,WAAW,CAAC,CAAC,mBAAmB,EAAE,OAAO,WAAW,MAAM,mBAAmB,CAAC,CAAC;AAAA,IAC/E,QAAQ,CAAC,CAAC,eAAe,EAAE,OAAO,WAAW,MAAM,mBAAmB,CAAC,CAAC;AAAA,IACxE,qBAAqB;AAAA;AAAA,MAEjB,CAAC,sBAAsB,iBAAiB,kBAAkB;AAAA,IAC9D;AAAA,IACA,gBAAgB;AAAA,MACZ,EAAE,SAAS,WAAW;AAAA,MACtB,CAAC,aAAa,QAAQ;AAAA,MACtB,CAAC,OAAO,EAAE,OAAO,QAAQ,MAAM,OAAO,CAAC;AAAA,IAC3C;AAAA,IACA,iBAAiB;AAAA,MACb,EAAE,SAAS,QAAQ;AAAA,MACnB,CAAC,OAAO,EAAE,OAAO,yBAAyB,MAAM,OAAO,CAAC;AAAA,IAC5D;AAAA,IACA,iBAAiB;AAAA,MACb,EAAE,SAAS,QAAQ;AAAA,MACnB,CAAC,KAAK,aAAa,MAAM;AAAA,MACzB,CAAC,SAAS,EAAE,OAAO,IAAI,MAAM,OAAO,CAAC;AAAA;AAAA,IACzC;AAAA,IACA,YAAY;AAAA,MACR,EAAE,SAAS,gBAAgB;AAAA,MAC3B,CAAC,aAAa,SAAS;AAAA,MACvB,CAAC,KAAK,aAAa,MAAM;AAAA,MACzB,CAAC,SAAS,EAAE,OAAO,IAAI,MAAM,OAAO,CAAC;AAAA;AAAA,IACzC;AAAA,IACA,mBAAmB;AAAA;AAAA,MAEf,CAAC,kBAAkB,cAAc;AAAA,MACjC,CAAC,aAAa,UAAU;AAAA,MACxB,CAAC,MAAM,EAAE,OAAO,QAAQ,MAAM,yBAAyB,CAAC;AAAA;AAAA,IAC5D;AAAA,IACA,uBAAuB;AAAA,MACnB,EAAE,SAAS,qBAAqB;AAAA,MAChC,CAAC,KAAK,EAAE,OAAO,QAAQ,MAAM,OAAO,CAAC;AAAA,IACzC;AAAA,IACA,UAAU;AAAA,MACN,CAAC,UAAU,WAAW,UAAU;AAAA,MAChC,CAAC,aAAa,SAAS;AAAA,IAC3B;AAAA,IACA,SAAS;AAAA,MACL,CAAC,UAAU,WAAW,MAAM;AAAA,MAC5B,CAAC,KAAK,SAAS;AAAA,IACnB;AAAA,IACA,MAAM,CAAC,CAAC,eAAe,iBAAiB,CAAC;AAAA,IACzC,SAAS;AAAA,MACL,CAAC,oCAAoC,EAAE,OAAO,UAAU,MAAM,SAAS,CAAC;AAAA,MACxE,CAAC,yBAAyB,YAAY;AAAA,IAC1C;AAAA,IACA,OAAO;AAAA,MACH;AAAA,QACI;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,qBAAqB;AAAA,MACjB,CAAC,qBAAqB,EAAE,OAAO,QAAQ,MAAM,wBAAwB,CAAC;AAAA,MACtE,CAAC,KAAK,EAAE,OAAO,mBAAmB,UAAU,gBAAgB,CAAC;AAAA,IACjE;AAAA,IACA,kBAAkB;AAAA;AAAA,MAEd,CAAC,qBAAqB,EAAE,OAAO,QAAQ,MAAM,wBAAwB,CAAC;AAAA;AAAA,MAEtE,CAAC,eAAe,MAAM;AAAA,MACtB,CAAC,KAAK,EAAE,OAAO,mBAAmB,UAAU,gBAAgB,CAAC;AAAA,IACjE;AAAA,IACA,sBAAsB;AAAA,MAClB,CAAC,sBAAsB,eAAe;AAAA,MACtC,CAAC,aAAa,UAAU;AAAA,MACxB,CAAC,KAAK,WAAW;AAAA,MACjB,EAAE,SAAS,QAAQ;AAAA,MACnB,CAAC,OAAO,EAAE,OAAO,QAAQ,MAAM,OAAO,CAAC;AAAA,IAC3C;AAAA,IACA,oBAAoB;AAAA,MAChB,EAAE,SAAS,sBAAsB;AAAA,MACjC,CAAC,eAAe,MAAM;AAAA,MACtB,CAAC,KAAK,aAAa,MAAM;AAAA,MACzB,CAAC,SAAS,EAAE,OAAO,IAAI,MAAM,OAAO,CAAC;AAAA,MACrC,CAAC,KAAK,EAAE,OAAO,mBAAmB,UAAU,gBAAgB,CAAC;AAAA,IACjE;AAAA,IACA,qBAAqB;AAAA,MACjB,CAAC,eAAe,MAAM;AAAA,MACtB,CAAC,KAAK,EAAE,OAAO,mBAAmB,UAAU,gBAAgB,CAAC;AAAA,IACjE;AAAA,IACA,cAAc;AAAA,MACV,EAAE,SAAS,QAAQ;AAAA,MACnB,CAAC,KAAK,EAAE,OAAO,mBAAmB,MAAM,gBAAgB,CAAC;AAAA,MACzD,CAAC,KAAK,EAAE,OAAO,mBAAmB,MAAM,OAAO,CAAC;AAAA,IACpD;AAAA,IACA,kBAAkB;AAAA,MACd;AAAA,QACI;AAAA,QACA,EAAE,OAAO,gBAAgB,MAAM,+BAA+B;AAAA,MAClE;AAAA,IACJ;AAAA,IACA,6BAA6B;AAAA,MACzB,CAAC,8BAA8B,EAAE,OAAO,eAAe,CAAC;AAAA,MACxD,EAAE,SAAS,QAAQ;AAAA,MACnB,CAAC,KAAK,EAAE,OAAO,mBAAmB,UAAU,gBAAgB,CAAC;AAAA,IACjE;AAAA,IACA,cAAc;AAAA,MACV,CAAC,eAAe,EAAE,OAAO,UAAU,CAAC;AAAA,MACpC,EAAE,SAAS,uBAAuB;AAAA,MAClC,EAAE,SAAS,QAAQ;AAAA,MACnB,EAAE,SAAS,oBAAoB;AAAA,MAC/B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,KAAK,EAAE,OAAO,mBAAmB,MAAM,OAAO,CAAC;AAAA,IACpD;AAAA,IACA,oBAAoB,CAAC,CAAC,kBAAkB,EAAE,OAAO,QAAQ,MAAM,qBAAqB,CAAC,CAAC;AAAA,IACtF,mBAAmB;AAAA,MACf,CAAC,sBAAsB,gBAAgB;AAAA,MACvC,CAAC,OAAO,WAAW;AAAA,MACnB,EAAE,SAAS,QAAQ;AAAA,MACnB,CAAC,OAAO,EAAE,OAAO,QAAQ,MAAM,OAAO,CAAC;AAAA,IAC3C;AAAA,IACA,SAAS;AAAA,MACL,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,wBAAwB,CAAC;AAAA,MACpE,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,kBAAkB,CAAC;AAAA,IAClE;AAAA,IACA,sBAAsB;AAAA,MAClB,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,MACjD,CAAC,KAAK,QAAQ;AAAA,IAClB;AAAA,IACA,gBAAgB;AAAA,MACZ,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,MACjD,CAAC,KAAK,QAAQ;AAAA,IAClB;AAAA,EACJ;AACJ;", "names": []}