import {
  __decorate,
  __extends
} from "./chunk-F25BIIHK.js";
import {
  FormItem
} from "./chunk-LZQZ2OHM.js";
import "./chunk-5QW7M2DY.js";
import "./chunk-ZJNJ4ZV3.js";
import "./chunk-CNJUQDSN.js";
import "./chunk-7XBFYOTW.js";
import {
  require_react
} from "./chunk-ZUWHLQVA.js";
import {
  __toESM
} from "./chunk-GFT2G5UO.js";

// node_modules/amis/esm/renderers/Form/ButtonToolbar.js
var import_react = __toESM(require_react());
var ButtonToolbar = (
  /** @class */
  function(_super) {
    __extends(ButtonToolbar2, _super);
    function ButtonToolbar2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    ButtonToolbar2.prototype.renderButtons = function() {
      var _a = this.props, render = _a.render, ns = _a.classPrefix, buttons = _a.buttons;
      return Array.isArray(buttons) ? buttons.map(function(button, key) {
        return render("button/".concat(key), button, {
          key
        });
      }) : null;
    };
    ButtonToolbar2.prototype.render = function() {
      var _a = this.props, buttons = _a.buttons, className = _a.className, cx = _a.classnames, render = _a.render, style = _a.style;
      return import_react.default.createElement("div", { className: cx("ButtonToolbar", className) }, this.renderButtons());
    };
    ButtonToolbar2.propsList = ["buttons", "className"];
    return ButtonToolbar2;
  }(import_react.default.Component)
);
var ButtonToolbarRenderer = (
  /** @class */
  function(_super) {
    __extends(ButtonToolbarRenderer2, _super);
    function ButtonToolbarRenderer2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    ButtonToolbarRenderer2 = __decorate([
      FormItem({
        type: "button-toolbar",
        strictMode: false
      })
    ], ButtonToolbarRenderer2);
    return ButtonToolbarRenderer2;
  }(ButtonToolbar)
);
export {
  ButtonToolbarRenderer,
  ButtonToolbar as default
};
//# sourceMappingURL=ButtonToolbar-ZT4HBFBC.js.map
