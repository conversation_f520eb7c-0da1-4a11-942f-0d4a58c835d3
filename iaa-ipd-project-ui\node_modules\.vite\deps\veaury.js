import {
  require_react_dom
} from "./chunk-7XBFYOTW.js";
import {
  require_react
} from "./chunk-ZUWHLQVA.js";
import "./chunk-EROHHDF3.js";
import {
  Comment,
  Fragment,
  Teleport,
  Text,
  createApp,
  defineAsyncComponent,
  getCurrentInstance,
  h,
  inject,
  provide,
  reactive
} from "./chunk-CIMZBJPB.js";
import {
  __toESM
} from "./chunk-GFT2G5UO.js";

// node_modules/veaury/dist/veaury.esm.js
var import_react = __toESM(require_react());
var import_react_dom = __toESM(require_react_dom());
function ownKeys(t, e) {
  var r, n = Object.keys(t);
  return Object.getOwnPropertySymbols && (r = Object.getOwnPropertySymbols(t), e && (r = r.filter(function(e2) {
    return Object.getOwnPropertyDescriptor(t, e2).enumerable;
  })), n.push.apply(n, r)), n;
}
function _objectSpread2(t) {
  for (var e = 1; e < arguments.length; e++) {
    var r = null != arguments[e] ? arguments[e] : {};
    e % 2 ? ownKeys(Object(r), true).forEach(function(e2) {
      _defineProperty(t, e2, r[e2]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(r)) : ownKeys(Object(r)).forEach(function(e2) {
      Object.defineProperty(t, e2, Object.getOwnPropertyDescriptor(r, e2));
    });
  }
  return t;
}
function _typeof(e) {
  return (_typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e2) {
    return typeof e2;
  } : function(e2) {
    return e2 && "function" == typeof Symbol && e2.constructor === Symbol && e2 !== Symbol.prototype ? "symbol" : typeof e2;
  })(e);
}
function _classCallCheck(e, t) {
  if (!(e instanceof t))
    throw new TypeError("Cannot call a class as a function");
}
function _defineProperties(e, t) {
  for (var r = 0; r < t.length; r++) {
    var n = t[r];
    n.enumerable = n.enumerable || false, n.configurable = true, "value" in n && (n.writable = true), Object.defineProperty(e, n.key, n);
  }
}
function _createClass(e, t, r) {
  return t && _defineProperties(e.prototype, t), r && _defineProperties(e, r), Object.defineProperty(e, "prototype", { writable: false }), e;
}
function _defineProperty(e, t, r) {
  return t in e ? Object.defineProperty(e, t, { value: r, enumerable: true, configurable: true, writable: true }) : e[t] = r, e;
}
function _extends() {
  return (_extends = Object.assign ? Object.assign.bind() : function(e) {
    for (var t = 1; t < arguments.length; t++) {
      var r, n = arguments[t];
      for (r in n)
        Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]);
    }
    return e;
  }).apply(this, arguments);
}
function _inherits(e, t) {
  if ("function" != typeof t && null !== t)
    throw new TypeError("Super expression must either be null or a function");
  e.prototype = Object.create(t && t.prototype, { constructor: { value: e, writable: true, configurable: true } }), Object.defineProperty(e, "prototype", { writable: false }), t && _setPrototypeOf(e, t);
}
function _getPrototypeOf(e) {
  return (_getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(e2) {
    return e2.__proto__ || Object.getPrototypeOf(e2);
  })(e);
}
function _setPrototypeOf(e, t) {
  return (_setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(e2, t2) {
    return e2.__proto__ = t2, e2;
  })(e, t);
}
function _isNativeReflectConstruct() {
  if ("undefined" == typeof Reflect || !Reflect.construct)
    return false;
  if (Reflect.construct.sham)
    return false;
  if ("function" == typeof Proxy)
    return true;
  try {
    return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    })), true;
  } catch (e) {
    return false;
  }
}
function _objectWithoutPropertiesLoose(e, t) {
  if (null == e)
    return {};
  for (var r, n = {}, o = Object.keys(e), a = 0; a < o.length; a++)
    r = o[a], 0 <= t.indexOf(r) || (n[r] = e[r]);
  return n;
}
function _objectWithoutProperties(e, t) {
  if (null == e)
    return {};
  var r, n = _objectWithoutPropertiesLoose(e, t);
  if (Object.getOwnPropertySymbols)
    for (var o = Object.getOwnPropertySymbols(e), a = 0; a < o.length; a++)
      r = o[a], 0 <= t.indexOf(r) || Object.prototype.propertyIsEnumerable.call(e, r) && (n[r] = e[r]);
  return n;
}
function _assertThisInitialized(e) {
  if (void 0 === e)
    throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
  return e;
}
function _possibleConstructorReturn(e, t) {
  if (t && ("object" == typeof t || "function" == typeof t))
    return t;
  if (void 0 !== t)
    throw new TypeError("Derived constructors may only return object or undefined");
  return _assertThisInitialized(e);
}
function _createSuper(r) {
  var n = _isNativeReflectConstruct();
  return function() {
    var e, t = _getPrototypeOf(r);
    return _possibleConstructorReturn(this, n ? (e = _getPrototypeOf(this).constructor, Reflect.construct(t, arguments, e)) : t.apply(this, arguments));
  };
}
function _slicedToArray(e, t) {
  return _arrayWithHoles(e) || _iterableToArrayLimit(e, t) || _unsupportedIterableToArray(e, t) || _nonIterableRest();
}
function _toConsumableArray(e) {
  return _arrayWithoutHoles(e) || _iterableToArray(e) || _unsupportedIterableToArray(e) || _nonIterableSpread();
}
function _arrayWithoutHoles(e) {
  if (Array.isArray(e))
    return _arrayLikeToArray(e);
}
function _arrayWithHoles(e) {
  if (Array.isArray(e))
    return e;
}
function _iterableToArray(e) {
  if ("undefined" != typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"])
    return Array.from(e);
}
function _iterableToArrayLimit(e, t) {
  var r = null == e ? null : "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"];
  if (null != r) {
    var n, o, a = [], i = true, u = false;
    try {
      for (r = r.call(e); !(i = (n = r.next()).done) && (a.push(n.value), !t || a.length !== t); i = true)
        ;
    } catch (e2) {
      u = true, o = e2;
    } finally {
      try {
        i || null == r.return || r.return();
      } finally {
        if (u)
          throw o;
      }
    }
    return a;
  }
}
function _unsupportedIterableToArray(e, t) {
  var r;
  if (e)
    return "string" == typeof e ? _arrayLikeToArray(e, t) : "Map" === (r = "Object" === (r = Object.prototype.toString.call(e).slice(8, -1)) && e.constructor ? e.constructor.name : r) || "Set" === r ? Array.from(e) : "Arguments" === r || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r) ? _arrayLikeToArray(e, t) : void 0;
}
function _arrayLikeToArray(e, t) {
  (null == t || t > e.length) && (t = e.length);
  for (var r = 0, n = new Array(t); r < t; r++)
    n[r] = e[r];
  return n;
}
function _nonIterableSpread() {
  throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _nonIterableRest() {
  throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _toPrimitive(e, t) {
  if ("object" != typeof e || null === e)
    return e;
  var r = e[Symbol.toPrimitive];
  if (void 0 === r)
    return ("string" === t ? String : Number)(e);
  r = r.call(e, t || "default");
  if ("object" != typeof r)
    return r;
  throw new TypeError("@@toPrimitive must return a primitive value.");
}
function _toPropertyKey(e) {
  e = _toPrimitive(e, "string");
  return "symbol" == typeof e ? e : String(e);
}
var originOptions = { react: { componentWrap: "div", slotWrap: "div", componentWrapAttrs: { __use_react_component_wrap: "", style: { all: "unset" } }, slotWrapAttrs: { __use_react_slot_wrap: "", style: { all: "unset" } }, vueNamedSlotsKey: ["node:"] }, vue: { componentWrapHOC: function(t) {
  return function() {
    var e = (0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : {}).portals;
    return (0, import_react.createElement)(import_react.Fragment, null, t, (void 0 === e ? [] : e).map(function(e2) {
      var t2 = e2.Portal, e2 = e2.key;
      return (0, import_react.createElement)(t2, { key: e2 });
    }));
  };
}, componentWrapAttrs: { "data-use-vue-component-wrap": "", style: { all: "unset" } }, slotWrapAttrs: { "data-use-vue-slot-wrap": "", style: { all: "unset" } } } };
function setOptions() {
  var e = 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : { react: {}, vue: {} }, t = 1 < arguments.length && void 0 !== arguments[1] ? arguments[1] : originOptions, r = 2 < arguments.length ? arguments[2] : void 0, t = (e.vue || (e.vue = {}), e.react || (e.react = {}), [t, _objectSpread2(_objectSpread2({}, e), {}, { react: _objectSpread2(_objectSpread2(_objectSpread2({}, t.react), e.react), {}, { componentWrapAttrs: _objectSpread2(_objectSpread2({}, t.react.componentWrapAttrs), e.react.componentWrapAttrs), slotWrapAttrs: _objectSpread2(_objectSpread2({}, t.react.slotWrapAttrs), e.react.slotWrapAttrs) }), vue: _objectSpread2(_objectSpread2(_objectSpread2({}, t.vue), e.vue), {}, { componentWrapAttrs: _objectSpread2(_objectSpread2({}, t.vue.componentWrapAttrs), e.vue.componentWrapAttrs), slotWrapAttrs: _objectSpread2(_objectSpread2({}, t.vue.slotWrapAttrs), e.vue.slotWrapAttrs) }) })]);
  return r && t.unshift({}), Object.assign.apply(this, t);
}
var domMethods = ["getElementById", "getElementsByClassName", "getElementsByTagName", "getElementsByTagNameNS", "querySelector", "querySelectorAll"];
var domTopObject = { Document: {}, Element: {} };
function overwriteDomMethods(i) {
  Object.keys(domTopObject).forEach(function(e) {
    domMethods.forEach(function(o) {
      var a = window[e].prototype[o];
      domTopObject[e][o] = a, window[e].prototype[o] = function() {
        for (var e2 = arguments.length, t = new Array(e2), r = 0; r < e2; r++)
          t[r] = arguments[r];
        var n = a.apply(this, t);
        return n && (n.constructor !== NodeList || n.constructor === NodeList && 0 < n.length) ? n : Element.prototype[o].apply(i, t);
      };
    });
  });
}
function recoverDomMethods() {
  Object.keys(domTopObject).forEach(function(t) {
    domMethods.forEach(function(e) {
      window[t].prototype[e] = domTopObject[t][e];
    });
  });
}
var _excluded = ["ref"];
var _excluded2 = ["key"];
var _excluded3 = ["hashList"];
var ReactMajorVersion = parseInt(import_react.version);
function toRaws(e) {
  return e;
}
var FunctionComponentWrap = function() {
  _inherits(r, import_react.Component);
  var t = _createSuper(r);
  function r(e) {
    return _classCallCheck(this, r), t.call(this, e);
  }
  return _createClass(r, [{ key: "render", value: function() {
    var e = this.props.component, t2 = this.props.passedProps, t2 = (t2.ref, _objectWithoutProperties(t2, _excluded));
    return (0, import_react.createElement)(e, t2, this.props.children);
  } }]), r;
}();
var createReactContainer = function(p, _, f) {
  var e = function() {
    _inherits(l, import_react.Component);
    var r = _createSuper(l);
    function l(e2) {
      var t;
      return _classCallCheck(this, l), (t = r.call(this, e2)).state = _objectSpread2(_objectSpread2({}, e2), _.isSlots ? { children: p } : {}), t.setRef = t.setRef.bind(_assertThisInitialized(t)), t.vueInReactCall = t.vueInReactCall.bind(_assertThisInitialized(t)), (t.__veauryVueWrapperRef__ = f).__veauryVueInReactCall__ = t.vueInReactCall, t;
    }
    return _createClass(l, [{ key: "reactPropsLinkToVueInstance", value: function(t) {
      Object.keys(t).forEach(function(e2) {
        f[e2] || (f[e2] = t[e2]);
      }), Object.getOwnPropertyNames(t.__proto__).filter(function(e2) {
        return ["constructor", "render"].indexOf(e2) < 0;
      }).forEach(function(e2) {
        f[e2] || (f[e2] = t[e2]);
      });
    } }, { key: "setRef", value: function(e2) {
      var t = this;
      e2 && (f.__veauryReactRef__ = e2, this.reactPropsLinkToVueInstance(e2), Promise.resolve().then(function() {
        return t.reactPropsLinkToVueInstance(e2);
      }), (this.setRef.current = e2).__veauryVueWrapperRef__ = f);
    } }, { key: "createSlot", value: function(r2) {
      return { originVNode: r2, inheritAttrs: false, __fromReactSlot: true, render: function() {
        var e2, t;
        return 1 === (null == (e2 = r2 = (r2 = (null == (t = this.$slots) || null == (e2 = t.default) ? void 0 : e2.call(t)) || r2) instanceof Function ? r2(this) : r2) ? void 0 : e2.length) && null != (t = r2[0]) && t.data && ((e2 = this.$attrs).key, t = _objectWithoutProperties(e2, _excluded2), r2[0].props = _objectSpread2(_objectSpread2({}, t), r2[0].props)), r2;
      } };
    } }, { key: "componentWillUnmount", value: function() {
      f.__veauryReactRef__ && (f.__veauryReactRef__.__veauryVueWrapperRef__ = null, f.__veauryReactRef__ = null);
    } }, { key: "vueInReactCall", value: function(e2) {
      var r2 = this, n = 1 < arguments.length && void 0 !== arguments[1] ? arguments[1] : {};
      return (2 < arguments.length ? arguments[2] : void 0) && e2 && e2[0] ? e2.map(function(e3, t) {
        return applyVueInReact(r2.createSlot(e3 instanceof Function ? e3 : [e3]), _objectSpread2(_objectSpread2(_objectSpread2({}, _), n), {}, { isSlots: true, wrapInstance: f })).render({ key: (null == e3 || null == (e3 = e3.data) ? void 0 : e3.key) || t });
      }) : applyVueInReact(this.createSlot(e2), _objectSpread2(_objectSpread2(_objectSpread2({}, _), n), {}, { isSlots: true, wrapInstance: f })).render();
    } }, { key: "render", value: function() {
      var e2, t, r2, n = this, o = this.state, a = o.hashList, i = _objectWithoutProperties(o, _excluded3), u = {}, c = {};
      for (e2 in i)
        t = e2, r2 = void 0, i.hasOwnProperty(t) && null != i[t] && (i[t].__slot ? (i[t].reactSlot ? i[t] = i[t].reactSlot : (r2 = i[t], _.defaultSlotsFormatter && i[t].__trueChildren ? (i[t].__top__ = n.__veauryVueWrapperRef__, i[t] = _.defaultSlotsFormatter(i[t].__trueChildren, n.vueInReactCall, a), i[t] instanceof Array ? i[t] = _toConsumableArray(i[t]) : -1 < ["string", "number"].indexOf(_typeof(i[t])) ? i[t] = [i[t]] : "object" === _typeof(i[t]) && (i[t] = _objectSpread2({}, i[t]))) : i[t] = _objectSpread2({}, applyVueInReact(n.createSlot(i[t]), _objectSpread2(_objectSpread2({}, _), {}, { isSlots: true, wrapInstance: f })).render()), i[t].vueFunction = r2), u[t] = i[t]) : i[t].__scopedSlot && (i[t] = i[t](n.createSlot), c[t] = i[t]));
      var s, o = {};
      return o.ref = this.setRef, _.isSlots ? this.state.children || this.props.children : (s = i, s = _objectSpread2(_objectSpread2(_objectSpread2({}, s = _.defaultPropsFormatter ? _.defaultPropsFormatter(i, this.vueInReactCall, a) : s), u), c), Object.getPrototypeOf(p) !== Function.prototype && ("object" !== _typeof(p) || p.render) || l.catchVueRefs() ? (Object.getPrototypeOf(p) === Function.prototype && delete o.ref, (0, import_react.createElement)(p, _extends({}, s, o))) : (0, import_react.createElement)(FunctionComponentWrap, _extends({ passedProps: s, component: p }, o), s.children));
    } }], [{ key: "catchVueRefs", value: function() {
      if (f.$parent) {
        for (var e2 in f.$parent.$refs)
          if (f.$parent.$refs[e2] === f)
            return true;
      }
      return false;
    } }]), l;
  }();
  return _defineProperty(e, "displayName", "applyReact_".concat(p.displayName || p.name || "Component")), e;
};
function applyReactInVue(m) {
  var b = 1 < arguments.length && void 0 !== arguments[1] ? arguments[1] : {};
  return m.__esModule && m.default && (m = m.default), b.isSlots && (m = m()), b = setOptions(b, void 0, true), { originReactComponent: m, setup: function(e, t) {
    var r, n, o, a;
    if (!b.isSlots)
      return r = {}, n = reactive({}), o = getCurrentInstance(), "function" == typeof (a = b.useInjectPropsFromWrapper || m.__veauryInjectPropsFromWrapper__) && ("function" != typeof (a = a.call(o.proxy, e)) ? (Object.assign(n, a), r.__veauryInjectedProps__ = n) : o.proxy.__veauryInjectedComputed__ = a), r;
  }, data: function() {
    return { VEAURY_Portals: [] };
  }, created: function() {
    this.__veauryPortalKeyPool__ = [], this.__veauryMaxPortalCount__ = 0;
  }, computed: { __veauryInjectedProps__: function() {
    var e;
    return null == (e = this.__veauryInjectedComputed__) ? void 0 : e.call(this);
  } }, render: function() {
    var e = h(b.react.componentWrap, _objectSpread2({ ref: "react" }, b.react.componentWrapAttrs || {}), this.VEAURY_Portals.map(function(e2) {
      var t = e2.Portal, e2 = e2.key;
      return t(h, e2);
    }));
    return this.__veauryCheckReactSlot__(this.$slots), e;
  }, methods: { __veauryCheckReactSlot__: function(i) {
    var u = this;
    function c(e, t, r) {
      return t[r] && (e[r] = t[r], 1);
    }
    Object.keys(i).forEach(function(e) {
      try {
        var t, r, n, o = i[e], a = o.apply(u, o.__reactArgs || [{}]);
        (o.__trueChildren = a).forEach(function(e2) {
          e2.children && u.__veauryCheckReactSlot__(e2.children);
        }), 1 !== a.length || c(o, r = a[0], "reactSlot") || c(o, r, "reactFunction") || r.type !== Fragment || 1 !== (null == (t = r.children) ? void 0 : t.length) || c(o, n = r.children[0], "reactSlot") || c(o, n, "reactFunction");
      } catch (e2) {
      }
    });
  }, __veauryPushVuePortal__: function(e) {
    var t = this.__veauryPortalKeyPool__.shift() || this.__veauryMaxPortalCount__++;
    this.VEAURY_Portals.push({ Portal: e, key: t });
  }, __veauryRemoveVuePortal__: function(r) {
    var n, e = this.VEAURY_Portals.find(function(e2, t) {
      if (e2.Portal === r)
        return n = t, true;
    });
    this.__veauryPortalKeyPool__.push(e.key), this.VEAURY_Portals.splice(n, 1);
  }, __veauryGetScopeSlot__: function(i, u, t) {
    var c = this;
    function e(a) {
      function e2() {
        for (var e3, t2 = this, r = arguments.length, n = new Array(r), o = 0; o < r; o++)
          n[o] = arguments[o];
        return i.reactFunction ? i.reactFunction.apply(this, n) : b.defaultSlotsFormatter ? ((e3 = i.apply(this, n)).__top__ = c, (e3 = b.defaultSlotsFormatter(e3, c.__veauryVueInReactCall__, u)) instanceof Array || -1 < _typeof(e3).indexOf("string", "number") ? e3 = _toConsumableArray(e3) : "object" === _typeof(e3) && (e3 = _objectSpread2({}, e3)), e3) : applyVueInReact(a(function() {
          return i.apply(t2, n);
        }), _objectSpread2(_objectSpread2({}, b), {}, { isSlots: true, wrapInstance: c })).render();
      }
      return b.pureTransformer && t ? e2.vueFunction = t : e2.vueFunction = i, e2;
    }
    return e.__scopedSlot = true, e;
  }, __veaurySyncUpdateProps__: function(e) {
    this.__veauryReactInstance__ && this.__veauryReactInstance__.setState(e);
  }, __veauryMountReactComponent__: function(e, t) {
    var r, n, o = this, a = 2 < arguments.length && void 0 !== arguments[2] ? arguments[2] : {}, i = {}, u = [], c = this.$.vnode.scopeId, s = (c && (i[c] = "", u.push(c)), {}), l = {};
    if (!e || null != t && t.slot)
      for (var p in this.$slots || {})
        (function(t2) {
          var e2;
          o.$slots.hasOwnProperty(t2) && null != o.$slots[t2] && ((e2 = b.react.vueNamedSlotsKey.find(function(e3) {
            return 0 === t2.indexOf(e3);
          })) || "default" === t2 ? (e2 = t2.replace(new RegExp("^".concat(e2)), ""), s[e2] = o.$slots[t2], s[e2].__slot = true) : l[t2] = o.__veauryGetScopeSlot__(o.$slots[t2], u, null == (e2 = o.$.vnode) || null == (e2 = e2.children) ? void 0 : e2[t2]));
        })(p);
    (!e || null != t && t.slot) && (n = _objectSpread2({}, s), r = n.default, delete n.default), this.__veauryLast__ = this.__veauryLast__ || {}, this.__veauryLast__.slot = this.__veauryLast__.slot || {}, this.__veauryLast__.attrs = this.__veauryLast__.attrs || {};
    var _ = { slot: function() {
      o.__veauryLast__.slot = _objectSpread2(_objectSpread2(_objectSpread2({}, r ? { children: r } : { children: null }), n), l);
    }, attrs: function() {
      o.__veauryLast__.attrs = o.$attrs;
    } };
    if (t && Object.keys(t).forEach(function(e2) {
      return _[e2]();
    }), e) {
      let f = function() {
        o.__veauryReactInstance__ && o.__veauryReactInstance__.setState(function(t2) {
          return Object.keys(t2).forEach(function(e2) {
            b.isSlots && "children" === e2 || delete t2[e2];
          }), _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, o.__veauryCache__), toRaws(o.__veauryInjectedProps__)), !b.isSlots && o.__veauryLast__.slot), toRaws(o.__veauryLast__.attrs));
        }), o.__veauryCache__ = null;
      };
      !this.microTaskUpdate || this.__veauryCache__ || this.$nextTick(function() {
        f(), o.microTaskUpdate = false;
      }), this.macroTaskUpdate && (clearTimeout(this.updateTimer), this.updateTimer = setTimeout(function() {
        clearTimeout(o.updateTimer), f(), o.macroTaskUpdate = false;
      })), this.__veauryCache__ = _objectSpread2(_objectSpread2({}, this.__veauryCache__ || {}), _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, a), this.$attrs.class ? { className: this.$attrs.class } : {}), _objectSpread2({}, i)), {}, { hashList: u }, this.$attrs.style ? { style: this.$attrs.style } : {})), this.macroTaskUpdate || this.microTaskUpdate || f();
    } else {
      _.slot(), _.attrs();
      var c = createReactContainer(m, b, this), d = (0, import_react.createElement)(c, _extends({}, toRaws(this.$attrs), toRaws(this.__veauryInjectedProps__), { children: r }, n, l, this.$attrs.class ? { className: this.$attrs.class } : {}, i, { hashList: u }, this.$attrs.style ? { style: this.$attrs.style } : {}, { ref: function(e2) {
        return o.__veauryReactInstance__ = e2;
      } })), y = this.$refs.react, v = b.wrapInstance;
      if (v)
        (v = b.wrapInstance).__veauryVueWrapperRef__ = this;
      else
        for (var h2 = this.$parent; h2; ) {
          if (h2.parentReactWrapperRef) {
            v = h2.parentReactWrapperRef;
            break;
          }
          if (h2.reactWrapperRef) {
            v = h2.reactWrapperRef;
            break;
          }
          h2 = h2.$parent;
        }
      v ? (this.parentReactWrapperRef = v, this.reactPortal = function() {
        return (0, import_react_dom.createPortal)(d, y);
      }, v.pushReactPortal(this.reactPortal)) : 17 < ReactMajorVersion ? (void 0 !== import_react_dom.default.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED && (import_react_dom.default.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.usingClientEntryPoint = true), this.__veauryReactApp__ = import_react_dom.default.createRoot(y), this.__veauryReactApp__.render(d)) : import_react_dom.default.render(d, y);
    }
  } }, mounted: function() {
    var e = this;
    this.__VEAURY_IGNORE_STRANGE_UPDATE__ = true, Promise.resolve().then(function() {
      e.__VEAURY_IGNORE_STRANGE_UPDATE__ = false;
    }), clearTimeout(this.updateTimer), this.__veauryMountReactComponent__();
  }, beforeUnmount: function() {
    var e;
    clearTimeout(this.updateTimer), this.reactPortal ? (overwriteDomMethods(this.$refs.react), null != (e = this.parentReactWrapperRef) && e.removeReactPortal(this.reactPortal)) : (overwriteDomMethods(this.$refs.react), 17 < ReactMajorVersion ? this.__veauryReactApp__.unmount() : import_react_dom.default.unmountComponentAtNode(this.$refs.react)), recoverDomMethods();
  }, updated: function() {
    this.__VEAURY_IGNORE_STRANGE_UPDATE__ || this.__veauryMountReactComponent__(true, { slot: true });
  }, inheritAttrs: false, watch: { $attrs: { handler: function() {
    this.__veauryMountReactComponent__(true, { attrs: true });
  }, deep: true }, __veauryInjectedProps__: { handler: function() {
    this.__veauryMountReactComponent__(true, { attrs: true });
  }, deep: true } } };
}
var REACT_ALL_HANDLERS = /* @__PURE__ */ new Set(["onClick", "onContextMenu", "onDoubleClick", "onDrag", "onDragEnd", "onDragEnter", "onDragExit", "onDragLeave", "onDragOver", "onDragStart", "onDrop", "onMouseDown", "onMouseEnter", "onMouseLeave", "onMouseMove", "onMouseOut", "onMouseOver", "onMouseUp", "onChange", "onInput", "onInvalid", "onReset", "onSubmit", "onError", "onLoad", "onPointerDown", "onPointerMove", "onPointerUp", "onPointerCancel", "onGotPointerCapture", "onLostPointerCapture", "onPointerEnter", "onPointerLeave", "onPointerOver", "onPointerOut", "onSelect", "onTouchCancel", "onTouchEnd", "onTouchMove", "onTouchStart", "onScroll", "onWheel", "onAbort", "onCanPlay", "onCanPlayThrough", "onDurationChange", "onEmptied", "onEncrypted", "onEnded", "onError", "onLoadedData", "onLoadedMetadata", "onLoadStart", "onPause", "onPlay", "onPlaying", "onProgress", "onRateChange", "onSeeked", "onSeeking", "onStalled", "onSuspend", "onTimeUpdate", "onVolumeChange", "onWaiting", "onLoad", "onError", "onAnimationStart", "onAnimationEnd", "onAnimationIteration", "onTransitionEnd", "onToggle"]);
function lookupVueWrapperRef(e, t) {
  for (var r = null == (e = t = (null == e ? void 0 : e._reactInternals) || (null == e ? void 0 : e._reactInternalFiber) || t) ? void 0 : e.return; r; ) {
    var n = r.stateNode;
    if (n = (null == n ? void 0 : n.parentVueWrapperRef) || (null == n ? void 0 : n.__veauryVueWrapperRef__))
      return n;
    r = r.return;
  }
}
function createModifiers(e, t, r) {
  var n = {};
  return r.forEach(function(e2) {
    n[e2] = true;
  }), e[("modelValue" === t ? "model" : t) + "Modifiers"] = n;
}
function setVModel(e, t, r) {
  var n = this, o = 3 < arguments.length && void 0 !== arguments[3] ? arguments[3] : "v-model", a = t;
  if (!(a instanceof Array))
    throw Error("[error:veaury] Parameter type error from '".concat(o, "', a single v-model is an array, such as [val, setter, argumentKey, modifiers] or [val, setter, modifiers]"));
  if ("function" != typeof a[1])
    throw Error("[error:veaury] Parameter type error from '".concat(o, "', a single v-model is an array, the second element of the array must be a setter function"));
  var i = a[1], u = ("string" == typeof a[2] ? (r = a[2], a[3] instanceof Array && createModifiers(e, r, a[3])) : a[2] instanceof Array && createModifiers(e, r, a[2]), e["onUpdate:" + r]);
  e["onUpdate:" + r] = "function" == typeof u ? function() {
    for (var e2 = arguments.length, t2 = new Array(e2), r2 = 0; r2 < e2; r2++)
      t2[r2] = arguments[r2];
    u.apply(n, t2), i.apply(n, t2);
  } : i, e[r] = a[0];
}
function parseVModel(a) {
  var i = this, r = {}, u = _objectSpread2({}, a);
  return Object.keys(a).forEach(function(n) {
    var o, e = n.match(/^onUpdate-([^-]+)/);
    if (e)
      delete u[n], o = r["onUpdate:".concat(e[1])], r["onUpdate:".concat(e[1])] = "function" == typeof o ? function() {
        for (var e2 = arguments.length, t2 = new Array(e2), r2 = 0; r2 < e2; r2++)
          t2[r2] = arguments[r2];
        o.apply(i, t2), a[n].apply(i, t2);
      } : a[n];
    else if (e = n.match(/^v-model($|:([^:]+)|-([^:]+))/))
      e = e[2] || e[3] || "modelValue", setVModel(r, a[n], e), delete u[n];
    else if ("v-models" === n) {
      if ("object" !== _typeof(a[n]) || a[n] instanceof Array)
        throw Error("[error:veaury] The parameter 'v-models' must be an object type, such as {[argumentKey]: singleVModel}");
      var t = a[n];
      Object.keys(t).forEach(function(e2) {
        setVModel(r, t[e2], e2, "v-models");
      }), delete u[n];
    }
  }), _objectSpread2(_objectSpread2({}, u), r);
}
var _default = function() {
  function e() {
    _classCallCheck(this, e), _defineProperty(this, "pool", /* @__PURE__ */ new Set());
  }
  return _createClass(e, [{ key: "getRandomId", value: function(e2) {
    var t = e2 + (Math.random() + "").substr(2);
    return this.pool.has(t) ? this.getRandomId(e2) : (this.pool.add(t), t);
  } }]), e;
}();
function RenderReactNode(e, t) {
  var r, e = e.node;
  if ("function" == typeof e && (e = e()), null != (r = t) && r.current || "function" == typeof t || null != (r = t) && r.toString().match(/^function/) || (t = null), -1 < ["string", "number"].indexOf(_typeof(e)))
    return e;
  if (e instanceof Array) {
    if (1 !== e.length)
      return e;
    e = e[0];
  }
  return _objectSpread2(_objectSpread2({}, e), {}, { ref: t });
}
var Bridge = applyReactInVue(RenderReactNode);
function WrapVue(e) {
  return h(Bridge, { node: function() {
    return e.node;
  } });
}
WrapVue.originReactComponent = (0, import_react.forwardRef)(RenderReactNode);
var _excluded$1 = ["component", "node"];
var _excluded2$1 = ["component", "$slots", "children", "class", "style"];
var _excluded3$1 = ["className", "classname"];
var optionsName = "veaury-options";
var random = new _default();
function filterVueComponent(e, t) {
  var r;
  return e = "string" == typeof e && t ? null == (t = t.$) || null == (t = t.appContext) || null == (t = t.app) || null == (r = t.component) ? void 0 : r.call(t, e) : e;
}
function transferSlots(r) {
  if (r)
    return Object.keys(r).forEach(function(e) {
      var t = r[e];
      null != t && ("function" == typeof t ? (r[e] = t, r[e].reactFunction = t) : (r[e] = function() {
        return t;
      }, r[e].reactSlot = t), t.vueFunction && (r[e].vueFunction = t.vueFunction));
    }), r;
}
function VNodeBridge(e) {
  var t;
  return null == (t = e.node) ? void 0 : t.call(e);
}
var VueContainer = (0, import_react.forwardRef)(function(e, t) {
  var r, n = e.component, o = e.node, e = _objectWithoutProperties(e, _excluded$1);
  if (null == n && null == o)
    return null;
  if (null != o) {
    if (o.$$typeof || "string" == typeof o || "number" == typeof o)
      return o;
    "function" != typeof o && (r = o, o = function() {
      return r;
    });
  }
  var a, n = n || VNodeBridge, i = setOptions(e[optionsName] || {}, void 0, true), u = i.useInjectPropsFromWrapper || n.__veauryInjectPropsFromWrapper__;
  return i.isSlots || "function" == typeof u && (a = u(e)), (0, import_react.createElement)(VueComponentLoader, _extends({}, _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({ component: n }, o ? { node: o } : {}), e), a), {}, _defineProperty({}, optionsName, i)), { ref: t }));
});
var VueComponentLoader = function() {
  _inherits(n, import_react.Component);
  var r = _createSuper(n);
  function n(e) {
    var t;
    return _classCallCheck(this, n), (t = r.call(this, e)).state = { portals: [] }, t.__veauryPortalKeyPool__ = [], t.__veauryMaxPortalCount__ = 0, t.__veauryCurrentVueComponent__ = e.component, t.__veauryCreateVueInstance__ = t.__veauryCreateVueInstance__.bind(_assertThisInitialized(t)), t.__veauryVueComponentContainer__ = t.createVueComponentContainer(), t;
  }
  return _createClass(n, [{ key: "pushReactPortal", value: function(e) {
    var t = this.state.portals, r2 = this.__veauryPortalKeyPool__.shift() || this.__veauryMaxPortalCount__++;
    t.push({ Portal: e, key: r2 }), this.setState({ portals: t });
  } }, { key: "removeReactPortal", value: function(r2) {
    var n2, e = this.state.portals, t = e.find(function(e2, t2) {
      if (e2.Portal === r2)
        return n2 = t2, true;
    });
    this.__veauryPortalKeyPool__.push(t.key), e.splice(n2, 1), this.__veauryVueRef__ && this.setState({ portals: e });
  } }, { key: "createVueComponentContainer", value: function() {
    var t = this, r2 = {}, e = this.props[optionsName];
    return e.isSlots ? (Object.keys(this.props).forEach(function(e2) {
      REACT_ALL_HANDLERS.has(e2) && "function" == typeof t.props[e2] && (r2[e2] = t.props[e2]);
    }), e.vue.slotWrapAttrs && (r2 = _objectSpread2(_objectSpread2({}, r2), e.vue.slotWrapAttrs))) : e.vue.componentWrapAttrs && (r2 = _objectSpread2(_objectSpread2({}, r2), e.vue.componentWrapAttrs)), e.vue.componentWrapHOC((0, import_react.createElement)("div", _extends({}, e.vue.componentWrapAttrs, { ref: this.__veauryCreateVueInstance__, key: null })), r2);
  } }, { key: "shouldComponentUpdate", value: function(e, t, r2) {
    var n2, o, a, i, u = this;
    return e === this.props || (n2 = e.component, e[optionsName], o = void 0 === (o = e["v-slots"]) ? null : o, a = e.children, e = _objectWithoutProperties(e, ["component", optionsName, "v-slots", "children"].map(_toPropertyKey)), this.__veauryCurrentVueComponent__ !== n2 && this.updateVueComponent(n2), !!n2.__fromReactSlot || (this.__veauryVueInstance__ ? (a && (o = o || {}, "object" !== _typeof(a) || a instanceof Array || a.$$typeof ? o.default = a : o = a), (i = this.__veauryVueInstance__.$data.$slots) && Object.keys(i).forEach(function(e2) {
      delete i[e2];
    }), o && (i || (this.__veauryVueInstance__.$data.$slots = {}), Object.assign(this.__veauryVueInstance__.$data.$slots, transferSlots(o))), Object.keys(this.__veauryVueInstance__.$data).forEach(function(e2) {
      "$slots" !== e2 && delete u.__veauryVueInstance__.$data[e2];
    }), this.__veauryVueInstance__ && Object.assign(this.__veauryVueInstance__.$data, parseVModel(e)), true) : void 0));
  } }, { key: "componentWillUnmount", value: function() {
    this.vuePortal ? this.parentVueWrapperRef.__veauryRemoveVuePortal__(this.vuePortal) : (this.__veauryVueInstance__ && this.__veauryVueInstance__.$.appContext.app.unmount(), random.pool.delete(this.__veauryVueTargetId__));
  } }, { key: "__veauryCreateVueInstance__", value: function(e) {
    var r2 = this, p = this, t = this.props, _ = (t.component, t[optionsName]), n2 = t.children, o = t["v-slots"], o = void 0 === o ? {} : o, t = _objectWithoutProperties(t, ["component", optionsName, "children", "v-slots"].map(_toPropertyKey));
    function a(e2) {
      this.__veauryVueInstance__ || (this.__veauryVueInstance__ = e2);
    }
    n2 && ("object" !== _typeof(n2) || n2 instanceof Array || n2.$$typeof ? o.default = n2 : o = n2), (o = transferSlots(o)) && (t.$slots = o), a = a.bind(this);
    var i, u = _objectSpread2({}, parseVModel(t)), c = { data: function() {
      return _.isSlots ? { children: p.__veauryCurrentVueComponent__.originVNode } : u;
    }, created: function() {
      this.reactWrapperRef = p, a(this);
    }, methods: { reactInVueCall: function(e2) {
      return (2 < arguments.length ? arguments[2] : void 0) && e2 && e2[0] ? e2.map(function(e3, t2) {
        return h(WrapVue, { node: e3, key: (null == e3 || null == (e3 = e3.data) ? void 0 : e3.key) || t2 });
      }) : h(WrapVue, { node: e2 });
    }, getScopedSlots: function(s, e2) {
      var t2, l = this, r3 = (this.getScopedSlots.__scopeSlots || (this.getScopedSlots.__scopeSlots = {}), _objectSpread2({}, e2));
      for (t2 in r3)
        (function(u2) {
          var e3, c2;
          !r3.hasOwnProperty(u2) || null == (e3 = r3[u2]) || (r3[u2] = (c2 = e3, function() {
            for (var e4, t3, r4, n3, o2 = arguments.length, a2 = new Array(o2), i2 = 0; i2 < o2; i2++)
              a2[i2] = arguments[i2];
            return c2.vueFunction ? c2.vueFunction.apply(l, a2) : (r4 = c2.reactSlot, n3 = c2.reactFunction, r4 = r4 || (null == n3 ? void 0 : n3.apply(l, a2)), n3 = _.defaultSlotsFormatter, null != (e4 = l.getScopedSlots.__scopeSlots[u2]) && null != (e4 = e4.component) && null != (e4 = e4.ctx) && e4.__veauryReactInstance__ ? (t3 = l.getScopedSlots.__scopeSlots[u2], Promise.resolve().then(function() {
              var e5;
              null != (e5 = t3) && null != (e5 = e5.component) && null != (e5 = e5.ctx) && null != (e5 = e5.__veauryReactInstance__) && e5.setState({ children: c2.apply(l, a2) });
            })) : (t3 = n3 && r4 ? [n3(r4, l.reactInVueCall)] : s(applyReactInVue(function() {
              return c2.apply(l, a2);
            }, _objectSpread2(_objectSpread2({}, _), {}, { isSlots: true, wrapInstance: p }))), l.getScopedSlots.__scopeSlots[u2] = t3), c2.reactFunction ? t3.reactFunction = c2.reactFunction : c2.reactSlot && (t3.reactSlot = c2.reactSlot), t3);
          }), r3[u2].reactFunction = e3);
        })(t2);
      return r3;
    } }, mounted: function() {
      e.removeAttribute("id"), p.__veauryVueRef__ = this.$refs.use_vue_wrapper, this.$refs.use_vue_wrapper.reactWrapperRef = p;
    }, beforeUnmount: function() {
      p.__veauryVueRef__ = null, this.$refs.use_vue_wrapper.reactWrapperRef = null;
    }, render: function() {
      var e2 = this, t2 = this.$data, r3 = (t2.component, t2.$slots), n3 = (t2.children, t2.class), o2 = t2.style, t2 = _objectWithoutProperties(t2, _excluded2$1), a2 = this.getScopedSlots(h, _objectSpread2({}, r3)), r3 = t2.className, i2 = t2.classname, t2 = _objectWithoutProperties(t2, _excluded3$1), u2 = {};
      return Object.keys(a2).forEach(function(e3) {
        var t3 = a2[e3];
        u2[e3] = "function" == typeof t3 ? t3 : function() {
          return t3;
        };
      }), h(filterVueComponent(p.__veauryCurrentVueComponent__, this), _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, t2), n3 || r3 || i2 ? { class: n3 || r3 || i2 } : {}), o2 ? { style: o2 } : {}), {}, { ref: "use_vue_wrapper" }), _objectSpread2({}, _.isSlots && this.children ? { default: "function" == typeof this.children ? this.children : function() {
        return e2.children;
      } } : _objectSpread2({}, u2)));
    } };
    e && (i = random.getRandomId("__vue_wrapper_container_"), e.id = i, this.__veauryVueTargetId__ = i, (n2 = _.wrapInstance) ? (n2 = _.wrapInstance).reactWrapperRef = p : n2 = lookupVueWrapperRef(this), n2 && document.getElementById(i) ? (this.parentVueWrapperRef = n2, this.vuePortal = function(e2, t2) {
      return e2(Teleport, { to: "#" + i, key: i }, [e2(Object.assign(c, { router: r2._router }))]);
    }, n2.__veauryPushVuePortal__(this.vuePortal)) : (o = createApp(c), "function" == typeof _.beforeVueAppMount && _.beforeVueAppMount(o), this.__veauryVueInstance__ = o.mount(e)));
  } }, { key: "updateVueComponent", value: function(e) {
    this.__veauryVueInstance__ && (e.__fromReactSlot ? this.__veauryVueInstance__.children = "function" == typeof e.originVNode ? e.originVNode : function() {
      return e.originVNode;
    } : (this.__veauryCurrentVueComponent__ = e, this.__veauryVueInstance__.$forceUpdate()));
  } }, { key: "render", value: function() {
    return (0, import_react.createElement)(this.__veauryVueComponentContainer__, { portals: this.state.portals });
  } }]), n;
}();
function applyVueInReact(r) {
  var n = 1 < arguments.length && void 0 !== arguments[1] ? arguments[1] : {}, e = (r || console.warn("Component must be passed in applyVueInReact!"), r.__esModule && r.default && (r = r.default), (0, import_react.forwardRef)(function(e2, t) {
    return (0, import_react.createElement)(VueContainer, _extends({}, e2, { component: r, ref: t }, _defineProperty({}, optionsName, n)));
  }));
  return e.originVueComponent = r, e;
}
function lazyVueInReact(e, t) {
  return (0, import_react.lazy)(function() {
    return e().then(function(e2) {
      return { default: applyVueInReact(e2.default, t) };
    });
  });
}
function lazyReactInVue(e, t) {
  function r() {
    return n().then(function(e2) {
      return applyReactInVue(e2.default, t);
    });
  }
  var n = e;
  "object" === _typeof(e) && (n = e.loader);
  return defineAsyncComponent("object" === _typeof(e) ? _objectSpread2(_objectSpread2({}, e), {}, { loader: r }) : r);
}
function injectPropsFromWrapper(e, t) {
  return console.warn("[veaury warn]: HOC injectPropsFromWrapper is deprecated! Try using 'useInjectPropsFromWrapper' in the options of 'applyReactInVue' or 'applyVueInReact'!"), "function" != typeof e ? console.warn("[veaury warn]: parameter 'injectionHook' is not a function") : t.__veauryInjectPropsFromWrapper__ = e, t;
}
var _excluded$2 = ["children"];
function createCrossingProviderForReactInVue(e) {
  var r = (0, import_react.createContext)({});
  return [function() {
    return (0, import_react.useContext)(r);
  }, applyReactInVue(function(e2) {
    var t = e2.children, e2 = _objectWithoutProperties(e2, _excluded$2);
    return (0, import_react.createElement)(r.Provider, { value: _objectSpread2({}, e2) }, t);
  }, { useInjectPropsFromWrapper: e }), r];
}
var random$1 = new _default();
function createCrossingProviderForVueInReact(e, r) {
  return r = r || random$1.getRandomId("veauryCrossingProvide_"), [function() {
    return inject(r);
  }, applyVueInReact({ setup: function(e2, t) {
    return provide(r, t.attrs), function() {
      return h(t.slots.default);
    };
  } }, { useInjectPropsFromWrapper: e })];
}
function createReactMissVue(e) {
  var t = e.useVueInjection, e = e.beforeVueAppMount, t = _slicedToArray(createCrossingProviderForReactInVue(t), 3), r = t[0], n = t[1], t = t[2];
  return [r, applyVueInReact(n, { beforeVueAppMount: e }), t];
}
function transformer(e) {
  var t = 1 < arguments.length && void 0 !== arguments[1] ? arguments[1] : {}, r = t.globalName, n = t.combinedOption, o = (t.transparentApi, applyReactInVue(e, n || {}));
  return o.install = function(e2) {
    var t2 = (1 < arguments.length && void 0 !== arguments[1] ? arguments[1] : {}).globalName;
    return r && e2.component(t2 || r, o), o;
  }, o;
}
function toCamelCase(e) {
  return e.replace(/-(\w)/g, function(e2, t) {
    return t.toUpperCase();
  });
}
function formatStyle(t) {
  var r;
  return t ? "string" == typeof t ? (t = t.trim()).split(/\s*;\s*/).reduce(function(e, t2) {
    return t2 && 2 === (t2 = t2.split(/\s*:\s*/)).length && Object.assign(e, _defineProperty({}, toCamelCase(t2[0]), t2[1])), e;
  }, {}) : "object" === _typeof(t) ? (r = {}, Object.keys(t).forEach(function(e) {
    r[toCamelCase(e)] = t[e];
  }), r) : {} : {};
}
function formatClass(t) {
  return t ? t instanceof Array ? t : "string" == typeof t ? (t = t.trim()).split(/\s+/) : "object" === _typeof(t) ? Object.keys(t).filter(function(e) {
    return !!t[e];
  }) : [] : [];
}
var _excluded$3 = ["ref"];
function getChildInfo(r, e, o, a, i) {
  var t = r.props || {}, t = (t.ref, _objectWithoutProperties(t, _excluded$3)), u = {}, n = (Object.keys(r.children || {}).forEach(function(t2) {
    var n2 = r.children[t2], e2 = originOptions.react.vueNamedSlotsKey.find(function(e3) {
      return 0 === t2.indexOf(e3);
    });
    e2 || "default" === t2 ? (e2 = t2.replace(new RegExp("^".concat(e2)), "").replace(/^default$/, "children"), u[e2] = a(n2(), o, i)) : u[t2] = function() {
      for (var e3 = arguments.length, t3 = new Array(e3), r2 = 0; r2 < e3; r2++)
        t3[r2] = arguments[r2];
      return n2.__reactArgs = t3, a(n2.apply(this, t3), o, i);
    };
  }), {}), c = formatStyle(t.style), s = Array.from(new Set(formatClass(t.class))).join(" ");
  return 0 < Object.keys(c).length && (n.style = c), "" !== s && (n.className = s), Object.assign(t, _objectSpread2(_objectSpread2({}, n), u)), delete t.class, t;
}
function isTextOwner(e) {
  return e.type === Text;
}
var random$2 = new _default();
function DirectiveHOC(e, t) {
  var r;
  return 0 < (null == (r = e.dirs) ? void 0 : r.length) ? (0, import_react.createElement)(FakeDirective, { vnode: e }, t) : t;
}
var FakeDirective = function() {
  _inherits(n, import_react.Component);
  var r = _createSuper(n);
  function n(e) {
    var t;
    return _classCallCheck(this, n), (t = r.call(this, e)).state = { prevVnode: null, savedDirectives: [], ref: null, prevProps: e }, t;
  }
  return _createClass(n, [{ key: "findDirectiveName", value: function(e) {
    var r2 = e.dir, n2 = -1;
    return [this.state.savedDirectives.find(function(e2, t) {
      if (e2.dir === r2)
        return n2 = t, true;
    }), n2];
  } }, { key: "doDirective", value: function() {
    var c = this, e = this.state, s = e.savedDirectives;
    if (!(l = e.ref)) {
      for (var l = (this._reactInternals || this._reactInternalFiber).child; l && 5 !== l.tag; )
        l = l.child;
      if (!l)
        return;
      l = l.stateNode;
    }
    var p = this.props.vnode, e = p.dirs;
    e && (e.forEach(function(e2) {
      var t, r2, n2, o, a, i, u;
      e2 && (u = (t = _slicedToArray(c.findDirectiveName(e2), 2))[0], t = t[1], r2 = (a = e2.dir).created, n2 = a.beforeMount, o = a.mounted, i = a.beforeUpdate, a = a.updated, u ? (s[t] = _objectSpread2(_objectSpread2(_objectSpread2({}, u), e2), {}, { oldValue: u.oldValue }), u = [l, s[t], p, c.state.prevVnode], null != i && i.apply(null, u), null != a && a.apply(null, u), s[t].oldValue = e2.value) : (s.push(e2), i = [l, e2, p, null], null != r2 && r2.apply(null, i), null != n2 && n2.apply(null, i), null != o && o.apply(null, i), e2.oldValue = e2.value));
    }), this.setState({ prevVnode: _objectSpread2({}, p), savedDirectives: s, ref: l }));
  } }, { key: "componentDidMount", value: function() {
    this.doDirective();
  } }, { key: "componentDidUpdate", value: function(e) {
    e.vnode !== this.props.vnode && this.doDirective();
  } }, { key: "componentWillUnmount", value: function() {
    var a = this, i = this.props.vnode, e = this.state, u = e.savedDirectives, c = e.ref, s = e.prevVnode, e = i.dirs;
    e && (e.forEach(function(e2) {
      var t, r2, n2, o;
      e2 && (t = (o = _slicedToArray(a.findDirectiveName(e2), 2))[0], o = o[1], t && (r2 = (n2 = e2.dir).beforeUnmount, n2 = n2.unmounted, u[o] = _objectSpread2(_objectSpread2({}, t), e2), o = [c, t, i, s], null != r2 && r2.apply(null, o), null != n2 && n2.apply(null, o)));
    }), this.setState({ prevVnode: _objectSpread2({}, i), savedDirectives: u }));
  } }, { key: "render", value: function() {
    var e = this.props;
    e.vnode;
    return e.children;
  } }]), n;
}();
function couldBeClass(e, t) {
  var r;
  return "function" == typeof e && (r = e.toString(), void 0 !== e.prototype && (e.prototype.constructor === e && ("class" == r.slice(0, 5) || (2 <= Object.getOwnPropertyNames(e.prototype).length || !/^function\s+\(|^function\s+anonymous\(/.test(r) && (!(!t || !/^function\s+[A-Z]/.test(r)) || !!/\b\(this\b|\bthis[\.\[]\b/.test(r) && (!(t && !/classCallCheck\(this/.test(r)) || /^function\sdefault_\d+\s*\(/.test(r)))))));
}
function resolveRef(o) {
  var a, e;
  return "function" != typeof (null == (e = o.type) ? void 0 : e.originReactComponent) || couldBeClass(null == (e = o.type) ? void 0 : e.originReactComponent) ? ((e = null == (e = o.ref) ? void 0 : e.r) && "string" == typeof e && (a = e, e = function(e2) {
    var t;
    e2 && (o.ref.i.refs && ((t = _objectSpread2({}, o.ref.i.refs))[a] = e2, o.ref.i.refs = t), void 0 !== (null == (t = o.ref.i.setupState) ? void 0 : t[a]) && (o.ref.i.setupState[a] = e2));
  }, e = new Proxy(e, { get: function(e2, t) {
    return e2[t];
  }, set: function(e2, t, r) {
    var n;
    return null != (n = o.ref.i.refs) && n[a] && ((n = _objectSpread2({}, o.ref.i.refs))[t] = r, o.ref.i.refs = n), r;
  } })), e) : null;
}
function addScopeId(t, e) {
  return !e || e instanceof Array && 0 === e.length || ("string" == typeof e && (e = [e]), (t = _objectSpread2({}, t)).props = _objectSpread2({}, t.props), e.forEach(function(e2) {
    t.props[e2] = "";
  })), t;
}
var _excluded$4 = ["style", "class"];
function takeVueDomInReact(e, t, r, n, o, a, i) {
  var u, c, s;
  return "all" === t || t instanceof Array || (t = t ? [t] : []), e.type === Fragment ? o(e.children, r, a) : "string" == typeof e.type && ("all" === t || -1 < t.indexOf(e.type)) ? (t = resolveRef(e), s = (c = e.props || {}).style, u = c.class, c = _objectSpread2(_objectSpread2({}, _objectWithoutProperties(c, _excluded$4)), {}, { style: formatStyle(s), className: Array.from(new Set(formatClass(u))).join(" ") }, t ? { ref: t } : {}), (s = e.children || c.children) && ((s = -1 < ["string", "number"].indexOf(_typeof(s)) ? [s] : _toConsumableArray(s)).__top__ = i), DirectiveHOC(e, addScopeId(import_react.default.createElement(e.type, c, o(s, r, a)), e.scopeId))) : r([e], null, n);
}
function pureInterceptProps() {
  return 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : {};
}
function setChildKey(e, t, r) {
  return !((e = e instanceof Array && 1 === e.length ? e[0] : e) instanceof Array) && null == e.key && 1 < t.length && ((e = _objectSpread2({}, e)).key = "_key_".concat(r)), e;
}
function getDistinguishReactOrVue(e) {
  var l = e.reactComponents, p = e.domTags, e = e.division, _ = void 0 === e || e;
  return function a(i, u, c) {
    var s;
    return i && i.forEach ? (s = [], i.forEach(function(e2, t) {
      if (e2 && e2.type !== Comment) {
        if (null == (o = e2.type) || !o.originReactComponent)
          return e2.$$typeof || "string" == typeof e2 || "number" == typeof e2 ? void s.push(e2) : isTextOwner(e2) ? void ("" !== e2.children.trim() && s.push(e2.children.trim())) : void (e2.type && (addScopeId(o = setChildKey(takeVueDomInReact(e2, p, u, _, a, c, i.__top__), i, t), e2.scopeId), s.push(o)));
        var r, n, o = e2.type.originReactComponent;
        addScopeId(r = setChildKey(r = "all" === (l = "all" === l || l instanceof Array ? l : [l]) || -1 < l.indexOf(o) ? (e2.__top__ = i.__top__, r = getChildInfo(e2, "_key_".concat(t), u, a, c), n = resolveRef(e2), e2.children && (e2.children.__top__ = i.__top__), DirectiveHOC(e2, import_react.default.createElement(o, _objectSpread2(_objectSpread2(_objectSpread2({}, pureInterceptProps(r, e2, o)), e2.__extraData || {}), n ? { ref: n } : {})))) : isTextOwner(e2) ? e2.text : takeVueDomInReact(e2, p, u, _, a, c), i, t), e2.scopeId), s.push(r);
      }
    }), 1 === s.length ? s[0] : s) : i;
  };
}
var NoWrapFunction = getDistinguishReactOrVue({ reactComponents: "all", domTags: "all" });
function applyPureReactInVue(e, t) {
  return transformer(e, { combinedOption: _objectSpread2({ pureTransformer: true, defaultSlotsFormatter: NoWrapFunction, defaultPropsFormatter: function(t2, o, a) {
    var r = {};
    return Object.keys(t2).forEach(function(e2) {
      var n = t2[e2];
      n && (n.vueFunction ? (r[e2] = function() {
        for (var e3 = arguments.length, t3 = new Array(e3), r2 = 0; r2 < e3; r2++)
          t3[r2] = arguments[r2];
        return NoWrapFunction(n.vueFunction.apply(this, t3), o, a);
      }, Object.defineProperty(r[e2], "length", { get: function() {
        return n.vueFunction.length;
      } })) : n.vueSlot && (r[e2] = NoWrapFunction(n.vueSlot, o, a)));
    }), Object.assign(t2, r);
  } }, t) });
}
var NoWrapFunction$1 = getDistinguishReactOrVue({ reactComponents: "all", domTags: "all" });
function getReactNode(e) {
  return e = (e = [e = "function" == typeof e ? e() : e]).flat(1 / 0), NoWrapFunction$1(e, function(e2) {
    return import_react.default.createElement(VueContainer, { node: e2 });
  });
}
function transformer$1(e) {
  var t = 1 < arguments.length && void 0 !== arguments[1] ? arguments[1] : {}, r = (t.globalName, t.combinedOption);
  t.transparentApi;
  return applyVueInReact(e, r || {});
}
var _excluded$5 = ["ref", "children", "v-slots"];
function getChildInfo$1(e, t, o, a, i) {
  var e = e.props || {}, r = (e.ref, e.children), n = e["v-slots"], u = void 0 === n ? {} : n, n = _objectWithoutProperties(e, _excluded$5), c = (r && ("object" !== _typeof(r) || r instanceof Array || r.$$typeof ? u.default = r : u = r), null), e = (Object.keys(u || {}).forEach(function(e2) {
    var n2 = u[e2];
    (c = c || {})[e2] = function() {
      if ("function" == typeof n2) {
        for (var e3 = arguments.length, t2 = new Array(e3), r2 = 0; r2 < e3; r2++)
          t2[r2] = arguments[r2];
        n2 = n2.apply(this, t2);
      }
      return a(n2, o, i);
    };
  }), {}), r = formatStyle(n.style), s = Array.from(new Set(formatClass(n.className))).join(" ");
  return 0 < Object.keys(r).length && (e.style = r), "" !== s && (e.class = s), Object.assign(n, _objectSpread2({}, e)), delete n.className, { props: n = parseVModel(n), slots: c };
}
function resolveRef$1(t) {
  var e = t.ref;
  if (e)
    return "object" === _typeof(e) ? function(e2) {
      t.ref.current = e2;
    } : "function" == typeof e ? e : void 0;
}
var _excluded$6 = ["style", "class", "children"];
function takeReactDomInVue(e, t, r, n, o, a) {
  var i, u, c, s;
  return "all" === t || t instanceof Array || (t = t ? [t] : []), e.type === import_react.Fragment ? o(null == (i = e.props) ? void 0 : i.children, r) : "string" == typeof e.type && ("all" === t || -1 < t.indexOf(e.type)) ? (i = resolveRef$1(e), s = (t = e.props || {}).style, c = t.class, u = t.children, t = _objectWithoutProperties(t, _excluded$6), c = Array.from(new Set(formatClass(c))).join(" "), s = formatStyle(s), t = _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, t), 0 === Object.keys(s).length ? {} : { style: s }), c ? { className: c } : {}), i ? { ref: i } : {}), 0 === Object.keys(t).length && (t = null), (s = u) && ((s = -1 < ["string", "number"].indexOf(_typeof(s)) ? [s] : s instanceof Array ? _toConsumableArray(s) : _objectSpread2({}, s)).__top__ = a), h(e.type, t, o(s, r))) : r([e], null, n);
}
function getDistinguishReactOrVue$1(e) {
  var c = e.vueComponents, s = e.domTags, e = e.division, l = void 0 === e || e;
  return function o(a, i) {
    if (null == a)
      return a;
    a instanceof Array || (a = [a]);
    var u = [];
    return a.forEach(function(e2, t) {
      if ((null == (r = e2.type) || !r.originVueComponent) && e2.type !== VueContainer)
        return e2.__v_isVNode || "string" == typeof e2 || "number" == typeof e2 ? void u.push(e2) : void (e2.type && (r = takeReactDomInVue(e2, s, i, l, o, a.__top__), u.push(r)));
      var r = e2.type.originVueComponent;
      if (e2.type === VueContainer) {
        if (!e2.props.component)
          return void u.push(e2.props.node);
        r = e2.props.component, e2 = _objectSpread2({}, e2);
        var n = _objectSpread2({}, e2.props);
        delete n.component, e2.props = n;
      }
      r = "all" === (c = "all" === c || c instanceof Array ? c : [c]) || -1 < c.indexOf(r) ? ((e2 = _objectSpread2({}, e2)).__top__ = a.__top__, t = (n = getChildInfo$1(e2, "_key_".concat(t), i, o)).props, n = n.slots, resolveRef$1(e2), e2.children && (e2.children.__top__ = a.__top__), h(r, _objectSpread2({}, t), n)) : takeReactDomInVue(e2, s, i, l, o), u.push(r);
    }), 1 === (u = u.flat(1 / 0)).length ? u[0] : u;
  };
}
var NoWrapFunction$2 = getDistinguishReactOrVue$1({ vueComponents: "all", domTags: "all" });
function applyPureVueInReact(e, t) {
  return transformer$1(e, { combinedOption: _objectSpread2({ pureTransformer: true, defaultSlotsFormatter: NoWrapFunction$2 }, t) });
}
var NoWrapFunction$3 = getDistinguishReactOrVue$1({ reactComponents: "all", domTags: "all" });
function getVNode(e) {
  return 1 === (e = (e = [e = "function" == typeof e ? e() : e]).flat(1 / 0)).length && (e = e[0]), NoWrapFunction$3(e, function(e2) {
    return h(WrapVue, { node: e2 });
  });
}
function lazyReactInVue$1(e, t) {
  function r() {
    return n().then(function(e2) {
      return applyPureReactInVue(e2.default, t);
    });
  }
  var n = e;
  "object" === _typeof(e) && (n = e.loader);
  return defineAsyncComponent("object" === _typeof(e) ? _objectSpread2(_objectSpread2({}, e), {}, { loader: r }) : r);
}
function lazyVueInReact$1(e, t) {
  return (0, import_react.lazy)(function() {
    return e().then(function(e2) {
      return { default: applyPureVueInReact(e2.default, t) };
    });
  });
}
var _excluded$7 = ["children"];
function createCrossingProviderForReactInVue$1(e) {
  var r = (0, import_react.createContext)({});
  return [function() {
    return (0, import_react.useContext)(r);
  }, applyPureReactInVue(function(e2) {
    var t = e2.children, e2 = _objectWithoutProperties(e2, _excluded$7);
    return (0, import_react.createElement)(r.Provider, { value: _objectSpread2({}, e2) }, t);
  }, { useInjectPropsFromWrapper: e }), r];
}
var random$3 = new _default();
function createCrossingProviderForVueInReact$1(e, r) {
  return r = r || random$3.getRandomId("veauryCrossingProvide_"), [function() {
    return inject(r);
  }, applyPureVueInReact({ setup: function(e2, t) {
    return provide(r, t.attrs), function() {
      return h(t.slots.default);
    };
  } }, { useInjectPropsFromWrapper: e })];
}
export {
  REACT_ALL_HANDLERS,
  WrapVue as RenderReactNode,
  VueContainer,
  applyPureReactInVue,
  applyPureVueInReact,
  applyReactInVue,
  applyVueInReact,
  createCrossingProviderForReactInVue$1 as createCrossingProviderForPureReactInVue,
  createCrossingProviderForVueInReact$1 as createCrossingProviderForPureVueInReact,
  createCrossingProviderForReactInVue,
  createCrossingProviderForVueInReact,
  createReactMissVue,
  getReactNode,
  getVNode,
  injectPropsFromWrapper,
  lazyReactInVue$1 as lazyPureReactInVue,
  lazyVueInReact$1 as lazyPureVueInReact,
  lazyReactInVue,
  lazyVueInReact,
  setOptions as setVeauryOptions,
  originOptions as veauryOptions
};
//# sourceMappingURL=veaury.js.map
