Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var _vue=require("vue"),_comp=require("../../ui/src/comp"),_xeUtils=_interopRequireDefault(require("xe-utils")),_ui=require("../../ui"),_util=require("./util");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let{renderer,renderEmptyElement}=_ui.VxeUI,renderType="header";var _default=exports.default=(0,_comp.defineVxeComponent)({name:"VxeTableHeader",props:{tableData:Array,tableColumn:Array,tableGroupColumn:Array,fixedColumn:Array,fixedType:{type:String,default:null}},setup(m){let le=(0,_vue.inject)("$xeTable",{}),{xID:_,props:b,reactData:C,internalData:y}=le,{computeColumnOpts:w,computeColumnDragOpts:D,computeCellOpts:l,computeMouseOpts:H,computeHeaderCellOpts:a,computeDefaultRowHeight:r,computeVirtualXOpts:t}=le.getComputeMaps(),E=(0,_vue.ref)([]),S=(0,_vue.ref)(),M=(0,_vue.ref)(),O=(0,_vue.ref)(),T=(0,_vue.ref)(),U=(0,_vue.ref)(),k=(0,_vue.ref)(),i=(0,_vue.ref)(),n=()=>{var e=C.isGroup;E.value=e?(0,_util.convertHeaderColumnToRows)(m.tableGroupColumn):[]},I=(H,E,S,M)=>{let O=le.xeGrid,T=m.fixedType,{resizable:U,columnKey:k,headerCellClassName:I,headerCellStyle:z,showHeaderOverflow:R,headerAlign:$,align:F,mouseConfig:q}=b,{currentColumn:A,dragCol:G,scrollXLoad:X,scrollYLoad:j,overflowX:L}=C,{fullColumnIdData:N,scrollXStore:V}=y,W=t.value,B=w.value,K=D.value,P=l.value;var e=r.value;let Y=a.value,J=(0,_util.getCellHeight)(Y.height)||e,{disabledMethod:Q,isCrossDrag:Z,isPeerDrag:ee}=K;return S.map((e,l)=>{var{type:a,showHeaderOverflow:r,headerAlign:t,align:i,filters:n,headerClassName:o,editRender:d,cellRender:u}=e,s=e.id,c=N[s]||{},d=d||u,u=d?renderer.get(d.name):null,d=e.children&&e.children.length,p=T?e.fixed!==T&&!d:!!e.fixed&&L,v=(_xeUtils.default.isBoolean(Y.padding)?Y:P).padding,r=_xeUtils.default.eqNull(r)?R:r,t=t||(u?u.tableHeaderCellAlign:"")||$||i||(u?u.tableCellAlign:"")||F,i="ellipsis"===r,u="title"===r,r=!0===r||"tooltip"===r,x=u||r||i;let h=!1,g=null;n&&(g=n[0],h=n.some(e=>e.checked));var f=c.index,c=c._index;let m={$table:le,$grid:O,$rowIndex:M,column:e,columnIndex:f,$columnIndex:l,_columnIndex:c,firstFilterOption:g,fixed:T,type:renderType,isHidden:p,hasFilter:h};var f={colid:s,colspan:1<e.colSpan?e.colSpan:null,rowspan:1<e.rowSpan?e.rowSpan:null},_={onClick:e=>le.triggerHeaderCellClickEvent(e,m),onDblclick:e=>le.triggerHeaderCellDblclickEvent(e,m)},b=B.drag&&"cell"===K.trigger;let C=!1;b&&(C=!(!Q||!Q(m))),(q||b)&&(_.onMousedown=e=>le.triggerHeaderCellMousedownEvent(e,m)),B.drag&&(_.onDragstart=le.handleHeaderCellDragDragstartEvent,_.onDragend=le.handleHeaderCellDragDragendEvent,_.onDragover=le.handleHeaderCellDragDragoverEvent,b)&&(_.onMouseup=le.handleHeaderCellDragMouseupEvent);var b=l===S.length-1,y=_xeUtils.default.isBoolean(e.resizable)?e.resizable:B.resizable||U,w=!e.resizeWidth&&("auto"===e.minWidth||"auto"===e.width);let D=!1;!E||H||G&&G.id===s||X&&!e.fixed&&!W.immediate&&(c<V.visibleStartIndex-V.preloadSize||c>V.visibleEndIndex+V.preloadSize)&&(D=!0);c={};return x?c.height=J+"px":c.minHeight=J+"px",(0,_vue.h)("th",Object.assign(Object.assign(Object.assign({class:["vxe-header--column",s,{["col--"+t]:t,["col--"+a]:a,"col--last":b,"col--fixed":e.fixed,"col--group":d,"col--ellipsis":x,"fixed--width":!w,"fixed--hidden":p,"is--padding":v,"is--sortable":e.sortable,"col--filter":!!n,"is--filter-active":h,"is--drag-active":B.drag&&!e.fixed&&!C&&(Z||ee||!e.parentId),"is--drag-disabled":B.drag&&C,"col--current":A===e},o?_xeUtils.default.isFunction(o)?o(m):o:"",I?_xeUtils.default.isFunction(I)?I(m):I:""],style:z?_xeUtils.default.isFunction(z)?z(m):z:null},f),_),{key:k||X||j||B.useKey||B.drag||d?s:l}),[(0,_vue.h)("div",{class:["vxe-cell",{"c--title":u,"c--tooltip":r,"c--ellipsis":i}],style:c},D||E&&p?[]:[(0,_vue.h)("div",{colid:s,class:"vxe-cell--wrapper"},e.renderHeader(m))]),!p&&y?(0,_vue.h)("div",{class:"vxe-cell--col-resizable",onMousedown:e=>le.handleColResizeMousedownEvent(e,T,m),onDblclick:e=>le.handleColResizeDblclickEvent(e,m)}):renderEmptyElement(le)])})};return(0,_vue.watch)(()=>m.tableColumn,n),(0,_vue.onMounted)(()=>{(0,_vue.nextTick)(()=>{var e=m.fixedType,l=le.internalData,l=l.elemStore,e=`${e||"main"}-header-`;l[e+"wrapper"]=S,l[e+"scroll"]=M,l[e+"table"]=O,l[e+"colgroup"]=T,l[e+"list"]=U,l[e+"xSpace"]=k,l[e+"repair"]=i,n()})}),(0,_vue.onUnmounted)(()=>{var e=m.fixedType,l=le.internalData,l=l.elemStore,e=`${e||"main"}-header-`;l[e+"wrapper"]=null,l[e+"scroll"]=null,l[e+"table"]=null,l[e+"colgroup"]=null,l[e+"list"]=null,l[e+"xSpace"]=null,l[e+"repair"]=null}),()=>{let{fixedType:l,fixedColumn:e,tableColumn:a}=m;var{mouseConfig:r,showHeaderOverflow:t,spanMethod:i,footerSpanMethod:n}=b,{isGroup:o,isColLoading:d,overflowX:u,scrollXLoad:s,dragCol:c}=C,{visibleColumn:p,fullColumnIdData:v}=y,x=H.value;let h=E.value,g=a,f=!1;return o?g=p:(s&&t&&(i||n||(f=!0)),f&&(d||!l&&u)||(g=p),l&&f&&(g=e||[]),h=[g]),l||o||s&&c&&2<g.length&&(t=v[c.id])&&(i=t._index,n=g[0],d=g[g.length-1],u=v[n.id],p=v[d.id],u)&&p&&(s=u._index,t=p._index,i<s?(g=[c].concat(g),h=[[c].concat(h[0])].concat(h.slice(1))):t<i&&(g=g.concat([c]),h=[h[0].concat([c])].concat(h.slice(1)))),(0,_vue.h)("div",{ref:S,class:["vxe-table--header-wrapper",l?`fixed-${l}--wrapper`:"body--wrapper"],xid:_},[(0,_vue.h)("div",{ref:M,class:"vxe-table--header-inner-wrapper",onScroll(e){le.triggerHeaderScrollEvent(e,l)}},[l?renderEmptyElement(le):(0,_vue.h)("div",{ref:k,class:"vxe-body--x-space"}),(0,_vue.h)("table",{ref:O,class:"vxe-table--header",xid:_,cellspacing:0,cellpadding:0,border:0,xvm:f?"1":null},[(0,_vue.h)("colgroup",{ref:T},g.map((e,l)=>(0,_vue.h)("col",{name:e.id,key:l,style:{width:e.renderWidth+"px"}}))),(0,_vue.h)("thead",{ref:U},((r,t,e)=>{let i=m.fixedType,{headerRowClassName:n,headerRowStyle:o}=b,{isColLoading:d,isDragColMove:u}=C,s=w.value,c=D.value;return e.map((e,l)=>{var a={$table:le,$rowIndex:l,fixed:i,type:renderType};return!d&&s.drag&&c.animation?(0,_vue.h)(_vue.TransitionGroup,{key:l,name:"vxe-header--col-list"+(u?"":"-disabled"),tag:"tr",class:["vxe-header--row",n?_xeUtils.default.isFunction(n)?n(a):n:""],style:o?_xeUtils.default.isFunction(o)?o(a):o:null},{default:()=>I(r,t,e,l)}):(0,_vue.h)("tr",{key:l,class:["vxe-header--row",n?_xeUtils.default.isFunction(n)?n(a):n:""],style:o?_xeUtils.default.isFunction(o)?o(a):o:null},I(r,t,e,l))})})(o,f,h))]),r&&x.area?(0,_vue.h)("div",{class:"vxe-table--cell-area"},[(0,_vue.h)("span",{class:"vxe-table--cell-main-area"}),(0,_vue.h)("span",{class:"vxe-table--cell-copy-area"}),(0,_vue.h)("span",{class:"vxe-table--cell-extend-area"}),(0,_vue.h)("span",{class:"vxe-table--cell-multi-area"}),(0,_vue.h)("span",{class:"vxe-table--cell-active-area"}),(0,_vue.h)("span",{class:"vxe-table--cell-col-status-area"})]):renderEmptyElement(le)])])}}});