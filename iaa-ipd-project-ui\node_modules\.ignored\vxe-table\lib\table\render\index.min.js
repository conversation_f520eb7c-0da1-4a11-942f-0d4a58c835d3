var _vue=require("vue"),_xeUtils=_interopRequireDefault(require("xe-utils")),_ui=require("../../ui"),_util=require("../../table/src/util"),_utils=require("../../ui/src/utils"),_vn=require("../../ui/src/vn"),_log=require("../../ui/src/log");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let{getConfig,renderer,getI18n,getComponent}=_ui.VxeUI,componentDefaultModelProp="modelValue",defaultCompProps={};function handleDefaultValue(e,t,l){return _xeUtils.default.eqNull(e)?_xeUtils.default.eqNull(t)?l:t:e}function parseDate(e,t){return e&&t.valueFormat?_xeUtils.default.toStringDate(e,t.valueFormat):e}function getFormatDate(e,t,l){var{dateConfig:n={}}=t;return _xeUtils.default.toDateString(parseDate(e,t),n.labelFormat||l)}function getLabelFormatDate(e,t){return getFormatDate(e,t,getI18n("vxe.input.date.labelFormat."+(t.type||"date")))}function getOldComponentName(e){return"vxe-"+e.replace("$","")}function getDefaultComponent({name:e}){return getComponent(e)}function getOldComponent({name:e}){return(0,_vue.resolveComponent)(getOldComponentName(e))}function handleConfirmFilter(e,t,l){e=e.$panel;e.changeOption({},t,l)}function getNativeAttrs(e){let{name:t,attrs:l}=e;return l="input"===t?Object.assign({type:"text"},l):l}function getInputImmediateModel(e){var{name:e,immediate:t,props:l}=e;return t||("VxeInput"===e||"$input"===e?(t=(l||{}).type,!(!t||"text"===t||"number"===t||"integer"===t||"float"===t)):"input"!==e&&"textarea"!==e&&"$textarea"!==e)}function getCellEditProps(e,t,l,n){return _xeUtils.default.assign({immediate:getInputImmediateModel(e)},defaultCompProps,n,e.props,{[componentDefaultModelProp]:l})}function getCellEditFilterProps(e,t,l,n){return _xeUtils.default.assign({},defaultCompProps,n,e.props,{[componentDefaultModelProp]:l})}function isImmediateCell(e,t){return"cell"===t.$type||getInputImmediateModel(e)}function getCellLabelVNs(e,t,l,n){e=e.placeholder;return[(0,_vue.h)("span",{class:["vxe-cell--label",n?n.class:""]},e&&(0,_utils.isEmptyValue)(l)?[(0,_vue.h)("span",{class:"vxe-cell--placeholder"},(0,_utils.formatText)((0,_utils.getFuncText)(e),1))]:(0,_utils.formatText)(l,1))]}function getNativeElementOns(e,l,t){let n=e.events,r=(0,_vn.getModelEvent)(e),a=(0,_vn.getChangeEvent)(e),{model:u,change:o,blur:i}=t||{},d=a===r,s={};return n&&_xeUtils.default.objectEach(n,(t,e)=>{s[(0,_vn.getOnName)(e)]=function(...e){t(l,...e)}}),u&&(s[(0,_vn.getOnName)(r)]=function(e){u(e),d&&o&&o(e),n&&n[r]&&n[r](l,e)}),!d&&o&&(s[(0,_vn.getOnName)(a)]=function(e){o(e),n&&n[a]&&n[a](l,e)}),i&&(s[(0,_vn.getOnName)(blurEvent)]=function(e){i(e),n&&n[blurEvent]&&n[blurEvent](l,e)}),s}let blurEvent="blur";function getComponentOns(e,l,t,n){let r=e.events,a=(0,_vn.getModelEvent)(e),u=(0,_vn.getChangeEvent)(e),{model:o,change:i,blur:d}=t||{},s={};return _xeUtils.default.objectEach(r,(t,e)=>{s[(0,_vn.getOnName)(e)]=function(...e){_xeUtils.default.isFunction(t)||(0,_log.errLog)("vxe.error.errFunc",[t]),t(l,...e)}}),o&&(s[(0,_vn.getOnName)(a)]=function(e){o(e),r&&r[a]&&r[a](l,e)}),i&&(s[(0,_vn.getOnName)(u)]=function(...e){i(...e),r&&r[u]&&r[u](l,...e)}),d&&(s[(0,_vn.getOnName)(blurEvent)]=function(...e){d(...e),r&&r[blurEvent]&&r[blurEvent](l,...e)}),n?Object.assign(s,n):s}function getEditOns(e,t){let{$table:l,row:n,column:r}=t,a=e.name,u=r.model,o=isImmediateCell(e,t);return getComponentOns(e,t,{model(e){u.update=!0,u.value=e,o&&(0,_util.setCellValue)(n,r,e)},change(e){!o&&a&&["VxeInput","VxeNumberInput","VxeTextarea","$input","$textarea"].includes(a)?(e=e.value,u.update=!0,u.value=e,l.updateStatus(t,e)):l.updateStatus(t)},blur(){o?l.handleCellRuleUpdateStatus("blur",t):l.handleCellRuleUpdateStatus("blur",t,u.value)}})}function getFilterOns(e,t,l){return getComponentOns(e,t,{model(e){l.data=e},change(){handleConfirmFilter(t,!_xeUtils.default.eqNull(l.data),l)},blur(){handleConfirmFilter(t,!_xeUtils.default.eqNull(l.data),l)}})}function getNativeEditOns(t,l){let{$table:n,row:r,column:a}=l,u=a.model;return getNativeElementOns(t,l,{model(e){var e=e.target;e&&(e=e.value,isImmediateCell(t,l)?(0,_util.setCellValue)(r,a,e):(u.update=!0,u.value=e))},change(e){var e=e.target;e&&(e=e.value,n.updateStatus(l,e))},blur(e){var e=e.target;e&&(e=e.value,n.updateStatus(l,e))}})}function getNativeFilterOns(e,t,l){return getNativeElementOns(e,t,{model(e){e=e.target;e&&(l.data=e.value)},change(){handleConfirmFilter(t,!_xeUtils.default.eqNull(l.data),l)},blur(){handleConfirmFilter(t,!_xeUtils.default.eqNull(l.data),l)}})}function nativeEditRender(e,t){var{row:l,column:n}=t,r=e.name,l=isImmediateCell(e,t)?(0,_util.getCellValue)(l,n):n.model.value;return[(0,_vue.h)(r,Object.assign(Object.assign(Object.assign({class:"vxe-default-"+r},getNativeAttrs(e)),{value:l}),getNativeEditOns(e,t)))]}function buttonCellRender(e,t){return[(0,_vue.h)(getDefaultComponent(e),Object.assign(Object.assign({},getCellEditProps(e,t,null)),getComponentOns(e,t)))]}function defaultEditRender(e,t){var{row:l,column:n}=t,l=(0,_util.getCellValue)(l,n);return[(0,_vue.h)(getDefaultComponent(e),Object.assign(Object.assign({},getCellEditProps(e,t,l)),getEditOns(e,t)))]}function checkboxEditRender(e,t){var{row:l,column:n}=t,l=(0,_util.getCellValue)(l,n);return[(0,_vue.h)(getDefaultComponent(e),Object.assign(Object.assign({},getCellEditProps(e,t,l)),getEditOns(e,t)))]}function radioAndCheckboxGroupEditRender(e,t){var l=e.options,{row:n,column:r}=t,n=(0,_util.getCellValue)(n,r);return[(0,_vue.h)(getDefaultComponent(e),Object.assign(Object.assign({options:l},getCellEditProps(e,t,n)),getEditOns(e,t)))]}function oldEditRender(e,t){var{row:l,column:n}=t,l=(0,_util.getCellValue)(l,n);return[(0,_vue.h)(getOldComponent(e),Object.assign(Object.assign({},getCellEditProps(e,t,l)),getEditOns(e,t)))]}function oldButtonEditRender(e,t){return[(0,_vue.h)(getComponent("vxe-button"),Object.assign(Object.assign({},getCellEditProps(e,t,null)),getComponentOns(e,t)))]}function oldButtonsEditRender(e,t){return e.children.map(e=>oldButtonEditRender(e,t)[0])}function renderNativeOptgroups(l,n,r){var{optionGroups:e,optionGroupProps:t={}}=l;let a=t.options||"options",u=t.label||"label";return e?e.map((e,t)=>(0,_vue.h)("optgroup",{key:t,label:e[u]},r(e[a],l,n))):[]}function renderNativeOptions(e,t,l){var{optionProps:n={}}=t,{row:r,column:a}=l;let u=n.label||"label",o=n.value||"value",i=n.disabled||"disabled",d=isImmediateCell(t,l)?(0,_util.getCellValue)(r,a):a.model.value;return e?e.map((e,t)=>(0,_vue.h)("option",{key:t,value:e[o],disabled:e[i],selected:e[o]==d},e[u])):[]}function nativeFilterRender(l,n){var e=n.column;let r=l.name,a=getNativeAttrs(l);return e.filters.map((e,t)=>(0,_vue.h)(r,Object.assign(Object.assign(Object.assign({key:t,class:"vxe-default-"+r},a),{value:e.data}),getNativeFilterOns(l,n,e))))}function defaultFilterRender(n,r){var e=r.column;return e.filters.map((e,t)=>{var l=e.data;return(0,_vue.h)(getDefaultComponent(n),Object.assign(Object.assign({key:t},getCellEditFilterProps(n,n,l)),getFilterOns(n,r,e)))})}function oldFilterRender(n,r){var e=r.column;return e.filters.map((e,t)=>{var l=e.data;return(0,_vue.h)(getOldComponent(n),Object.assign(Object.assign({key:t},getCellEditFilterProps(n,n,l)),getFilterOns(n,r,e)))})}function handleFilterMethod({option:e,row:t,column:l}){e=e.data;return _xeUtils.default.get(t,l.field)==e}function handleInputFilterMethod({option:e,row:t,column:l}){e=e.data,t=_xeUtils.default.get(t,l.field);return-1<_xeUtils.default.toValueString(t).indexOf(e)}function nativeSelectEditRender(e,t){return[(0,_vue.h)("select",Object.assign(Object.assign({class:"vxe-default-select"},getNativeAttrs(e)),getNativeEditOns(e,t)),e.optionGroups?renderNativeOptgroups(e,t,renderNativeOptions):renderNativeOptions(e.options,e,t))]}function defaultSelectEditRender(e,t){var{row:l,column:n}=t,{options:r,optionProps:a,optionGroups:u,optionGroupProps:o}=e,l=(0,_util.getCellValue)(l,n);return[(0,_vue.h)(getDefaultComponent(e),Object.assign(Object.assign({},getCellEditProps(e,t,l,{options:r,optionProps:a,optionGroups:u,optionGroupProps:o})),getEditOns(e,t)))]}function defaultTableOrTreeSelectEditRender(e,t){var{row:l,column:n}=t,{options:r,optionProps:a}=e,l=(0,_util.getCellValue)(l,n);return[(0,_vue.h)(getDefaultComponent(e),Object.assign(Object.assign({},getCellEditProps(e,t,l,{options:r,optionProps:a})),getEditOns(e,t)))]}function oldSelectEditRender(e,t){var{row:l,column:n}=t,{options:r,optionProps:a,optionGroups:u,optionGroupProps:o}=e,l=(0,_util.getCellValue)(l,n);return[(0,_vue.h)(getOldComponent(e),Object.assign(Object.assign({},getCellEditProps(e,t,l,{options:r,optionProps:a,optionGroups:u,optionGroupProps:o})),getEditOns(e,t)))]}function getSelectCellValue(e,{row:t,column:l}){let{options:n,optionGroups:r,optionProps:a={},optionGroupProps:u={}}=e;e=_xeUtils.default.get(t,l.field);let o,i=a.label||"label",d=a.value||"value";return null!=e?_xeUtils.default.map(_xeUtils.default.isArray(e)?e:[e],r?t=>{var l=u.options||"options";for(let e=0;e<r.length&&!(o=_xeUtils.default.find(r[e][l],e=>e[d]==t));e++);return o?o[i]:t}:t=>(o=_xeUtils.default.find(n,e=>e[d]==t))?o[i]:t).join(", "):""}function handleExportSelectMethod(e){var{row:t,column:l,options:n}=e;return n.original?(0,_util.getCellValue)(t,l):getSelectCellValue(l.editRender||l.cellRender,e)}function getTreeSelectCellValue(e,{row:l,column:n}){var{options:e,optionProps:t={}}=e,l=_xeUtils.default.get(l,n.field);let r=t.label||"label",a=t.value||"value";n=t.children||"children";if(null==l)return"";{let t={};return _xeUtils.default.eachTree(e,e=>{t[_xeUtils.default.get(e,a)]=e},{children:n}),_xeUtils.default.map(_xeUtils.default.isArray(l)?l:[l],e=>{e=t[e];return e&&_xeUtils.default.get(e,r)}).join(", ")}}function handleExportTreeSelectMethod(e){var{row:t,column:l,options:n}=e;return n.original?(0,_util.getCellValue)(t,l):getTreeSelectCellValue(l.editRender||l.cellRender,e)}function handleNumberCell(e,t){var l,{props:n={},showNegativeStatus:r}=e,{row:a,column:u}=t,o=n.type;let i=_xeUtils.default.get(a,u.field),d=!1;return(0,_utils.isEmptyValue)(i)||(a=getConfig().numberInput||{},"float"===o?(u=handleDefaultValue(n.autoFill,a.autoFill,!0),l=handleDefaultValue(n.digits,a.digits,1),i=_xeUtils.default.toFixed(_xeUtils.default.floor(i,l),l),u||(i=_xeUtils.default.toNumber(i)),r&&i<0&&(d=!0)):"amount"===o?(l=handleDefaultValue(n.autoFill,a.autoFill,!0),u=handleDefaultValue(n.digits,a.digits,2),o=handleDefaultValue(n.showCurrency,a.showCurrency,!1),i=_xeUtils.default.toNumber(i),r&&i<0&&(d=!0),i=_xeUtils.default.commafy(i,{digits:u}),l||([u,l]=i.split("."),l&&(l=l.replace(/0+$/,""),i=l?[u,".",l].join(""):u)),o&&(i=""+(n.currencySymbol||a.currencySymbol||getI18n("vxe.numberInput.currencySymbol")||"")+i)):r&&_xeUtils.default.toNumber(i)<0&&(d=!0)),getCellLabelVNs(e,t,i,d?{class:"is--negative"}:{})}renderer.mixin({input:{tableAutoFocus:"input",renderTableEdit:nativeEditRender,renderTableDefault:nativeEditRender,renderTableFilter:nativeFilterRender,tableFilterDefaultMethod:handleInputFilterMethod},textarea:{tableAutoFocus:"textarea",renderTableEdit:nativeEditRender},select:{renderTableEdit:nativeSelectEditRender,renderTableDefault:nativeSelectEditRender,renderTableCell(e,t){return getCellLabelVNs(e,t,getSelectCellValue(e,t))},renderTableFilter(l,n){var e=n.column;return e.filters.map((e,t)=>(0,_vue.h)("select",Object.assign(Object.assign({key:t,class:"vxe-default-select"},getNativeAttrs(l)),getNativeFilterOns(l,n,e)),l.optionGroups?renderNativeOptgroups(l,n,renderNativeOptions):renderNativeOptions(l.options,l,n)))},tableFilterDefaultMethod:handleFilterMethod,tableExportMethod:handleExportSelectMethod},VxeInput:{tableAutoFocus:"input",renderTableEdit:defaultEditRender,renderTableCell(e,t){var{props:l={}}=e,{row:n,column:r}=t,a=getConfig().input||{},u=l.digits||a.digits||2;let o=_xeUtils.default.get(n,r.field);if(o)switch(l.type){case"date":case"week":case"month":case"quarter":case"year":o=getLabelFormatDate(o,l);break;case"float":o=_xeUtils.default.toFixed(_xeUtils.default.floor(o,u),u)}return getCellLabelVNs(e,t,o)},renderTableDefault:defaultEditRender,renderTableFilter:defaultFilterRender,tableFilterDefaultMethod:handleInputFilterMethod},FormatNumberInput:{renderTableDefault:handleNumberCell,tableFilterDefaultMethod:handleInputFilterMethod,tableExportMethod(e){var{row:e,column:t}=e;return _xeUtils.default.get(e,t.field)}},VxeNumberInput:{tableAutoFocus:"input",renderTableEdit:defaultEditRender,renderTableCell:handleNumberCell,renderTableFooter(t,l){var{props:t={}}=t,{row:l,column:n,_columnIndex:r}=l,a=t.type,r=_xeUtils.default.isArray(l)?l[r]:_xeUtils.default.get(l,n.field);if(_xeUtils.default.isNumber(r)){l=getConfig().numberInput||{};if("float"===a){var n=handleDefaultValue(t.autoFill,l.autoFill,!0),u=handleDefaultValue(t.digits,l.digits,1);let e=_xeUtils.default.toFixed(_xeUtils.default.floor(r,u),u);return e=n?e:_xeUtils.default.toNumber(e)}if("amount"===a){var u=handleDefaultValue(t.autoFill,l.autoFill,!0),n=handleDefaultValue(t.digits,l.digits,2),a=handleDefaultValue(t.showCurrency,l.showCurrency,!1);let e=_xeUtils.default.commafy(_xeUtils.default.toNumber(r),{digits:n});return u||([n,u]=e.split("."),u&&(u=u.replace(/0+$/,""),e=u?[n,".",u].join(""):n)),e=a?""+(t.currencySymbol||l.currencySymbol||getI18n("vxe.numberInput.currencySymbol")||"")+e:e}}return(0,_utils.getFuncText)(r,1)},renderTableDefault:defaultEditRender,renderTableFilter:defaultFilterRender,tableFilterDefaultMethod:handleInputFilterMethod,tableExportMethod(e){var{row:e,column:t}=e;return _xeUtils.default.get(e,t.field)}},VxeDatePicker:{tableAutoFocus:"input",renderTableEdit:defaultEditRender,renderTableCell(e,t){var{props:l={}}=e,{row:n,column:r}=t;let a=_xeUtils.default.get(n,r.field);return getCellLabelVNs(e,t,a=a&&"time"!==l.type?getLabelFormatDate(a,l):a)},renderTableDefault:defaultEditRender,renderTableFilter:defaultFilterRender,tableFilterDefaultMethod:handleFilterMethod},VxeDateRangePicker:{tableAutoFocus:"input",renderTableEdit(e,t){let{startField:l,endField:n}=e,{$table:r,row:a,column:u}=t,o=u.model;var i=(0,_util.getCellValue)(a,u),d={},s={};return l&&n&&(d.startValue=_xeUtils.default.get(a,l),d.endValue=_xeUtils.default.get(a,n),s["onUpdate:startValue"]=e=>{l&&_xeUtils.default.set(a,l,e)},s["onUpdate:endValue"]=e=>{n&&_xeUtils.default.set(a,n,e)}),[(0,_vue.h)(getDefaultComponent(e),Object.assign(Object.assign({},getCellEditProps(e,t,i,d)),getComponentOns(e,t,{model(e){o.update=!0,o.value=e,(0,_util.setCellValue)(a,u,e)},change(){r.updateStatus(t)},blur(){r.handleCellRuleUpdateStatus("blur",t)}},s)))]},renderTableCell(e,t){var{startField:l,endField:n}=e,{row:r,column:a}=t;let u="",o="",i=(l&&n?(u=_xeUtils.default.get(r,l),o=_xeUtils.default.get(r,n)):(l=_xeUtils.default.get(r,a.field))&&(o=(_xeUtils.default.isArray(l)?(u=l[0],l):(n=(""+l).split(","),u=n[0],n))[1]),"");return getCellLabelVNs(e,t,i=u&&o?u+" ~ "+o:i)}},VxeTextarea:{tableAutoFocus:"textarea",renderTableEdit:defaultEditRender,renderTableCell(e,t){var{row:l,column:n}=t;return getCellLabelVNs(e,t,_xeUtils.default.get(l,n.field))}},VxeButton:{renderTableDefault:buttonCellRender},VxeButtonGroup:{renderTableDefault(e,t){var l=e.options;return[(0,_vue.h)(getDefaultComponent(e),Object.assign(Object.assign({options:l},getCellEditProps(e,t,null)),getComponentOns(e,t)))]}},VxeSelect:{tableAutoFocus:"input",renderTableEdit:defaultSelectEditRender,renderTableDefault:defaultSelectEditRender,renderTableCell(e,t){return getCellLabelVNs(e,t,getSelectCellValue(e,t))},renderTableFilter(n,r){var e=r.column;let{options:a,optionProps:u,optionGroups:o,optionGroupProps:i}=n;return e.filters.map((e,t)=>{var l=e.data;return(0,_vue.h)(getDefaultComponent(n),Object.assign(Object.assign({key:t},getCellEditFilterProps(n,r,l,{options:a,optionProps:u,optionGroups:o,optionGroupProps:i})),getFilterOns(n,r,e)))})},tableFilterDefaultMethod:handleFilterMethod,tableExportMethod:handleExportSelectMethod},formatOption:{renderTableDefault(e,t){return getCellLabelVNs(e,t,getSelectCellValue(e,t))}},FormatSelect:{renderTableDefault(e,t){return getCellLabelVNs(e,t,getSelectCellValue(e,t))},tableFilterDefaultMethod:handleFilterMethod,tableExportMethod:handleExportSelectMethod},VxeTreeSelect:{tableAutoFocus:"input",renderTableEdit:defaultTableOrTreeSelectEditRender,renderTableCell(e,t){return getCellLabelVNs(e,t,getTreeSelectCellValue(e,t))},tableExportMethod:handleExportTreeSelectMethod},formatTree:{renderTableDefault(e,t){return getCellLabelVNs(e,t,getTreeSelectCellValue(e,t))}},FormatTreeSelect:{renderTableDefault(e,t){return getCellLabelVNs(e,t,getTreeSelectCellValue(e,t))},tableExportMethod:handleExportTreeSelectMethod},VxeTableSelect:{tableAutoFocus:"input",renderTableEdit:defaultTableOrTreeSelectEditRender,renderTableCell(e,t){return getCellLabelVNs(e,t,getTreeSelectCellValue(e,t))},tableExportMethod:handleExportTreeSelectMethod},VxeColorPicker:{tableAutoFocus:"input",renderTableEdit(e,t){var{row:l,column:n}=t,r=e.options,l=(0,_util.getCellValue)(l,n);return[(0,_vue.h)(getDefaultComponent(e),Object.assign(Object.assign({},getCellEditProps(e,t,l,{colors:r})),getEditOns(e,t)))]},renderTableCell(e,t){var{row:t,column:l}=t,t=_xeUtils.default.get(t,l.field);return(0,_vue.h)("span",{class:"vxe-color-picker--readonly"},[(0,_vue.h)("div",{class:"vxe-color-picker--readonly-color",style:{backgroundColor:t}})])}},VxeIconPicker:{tableAutoFocus:"input",renderTableEdit(e,t){var{row:l,column:n}=t,r=e.options,l=(0,_util.getCellValue)(l,n);return[(0,_vue.h)(getDefaultComponent(e),Object.assign(Object.assign({},getCellEditProps(e,t,l,{icons:r})),getEditOns(e,t)))]},renderTableCell(e,t){var{row:t,column:l}=t,t=_xeUtils.default.get(t,l.field);return(0,_vue.h)("i",{class:t})}},VxeRadioGroup:{renderTableDefault:radioAndCheckboxGroupEditRender},VxeCheckbox:{renderTableDefault:checkboxEditRender},VxeCheckboxGroup:{renderTableDefault:radioAndCheckboxGroupEditRender},VxeSwitch:{tableAutoFocus:"button",renderTableEdit:defaultEditRender,renderTableDefault:defaultEditRender},VxeUpload:{renderTableEdit:defaultEditRender,renderTableCell:defaultEditRender,renderTableDefault:defaultEditRender},VxeImage:{renderTableDefault(e,t){var{row:l,column:n}=t,r=e.props,l=(0,_util.getCellValue)(l,n);return[(0,_vue.h)(getDefaultComponent(e),Object.assign(Object.assign(Object.assign({},r),{src:l}),getEditOns(e,t)))]}},VxeImageGroup:{renderTableDefault(e,t){var{row:l,column:n}=t,r=e.props,l=(0,_util.getCellValue)(l,n);return[(0,_vue.h)(getDefaultComponent(e),Object.assign(Object.assign(Object.assign({},r),{urlList:l}),getEditOns(e,t)))]}},VxeTextEllipsis:{renderTableDefault(e,t){var{row:l,column:n}=t,r=e.props,l=(0,_util.getCellValue)(l,n);return[(0,_vue.h)(getDefaultComponent(e),Object.assign(Object.assign(Object.assign({},r),{content:l}),getEditOns(e,t)))]}},VxeRate:{renderTableDefault:defaultEditRender},VxeSlider:{renderTableDefault:defaultEditRender},$input:{tableAutoFocus:".vxe-input--inner",renderTableEdit:oldEditRender,renderTableCell(e,t){var l,{props:n={}}=e,{row:r,column:a}=t,u=n.digits||(null==(l=getConfig().input)?void 0:l.digits)||2;let o=_xeUtils.default.get(r,a.field);if(o)switch(n.type){case"date":case"week":case"month":case"year":o=getLabelFormatDate(o,n);break;case"float":o=_xeUtils.default.toFixed(_xeUtils.default.floor(o,u),u)}return getCellLabelVNs(e,t,o)},renderTableDefault:oldEditRender,renderTableFilter:oldFilterRender,tableFilterDefaultMethod:handleInputFilterMethod},$textarea:{tableAutoFocus:".vxe-textarea--inner"},$button:{renderTableDefault:oldButtonEditRender},$buttons:{renderTableDefault:oldButtonsEditRender},$select:{tableAutoFocus:".vxe-input--inner",renderTableEdit:oldSelectEditRender,renderTableDefault:oldSelectEditRender,renderTableCell(e,t){return getCellLabelVNs(e,t,getSelectCellValue(e,t))},renderTableFilter(n,r){var e=r.column;let{options:a,optionProps:u,optionGroups:o,optionGroupProps:i}=n;return e.filters.map((e,t)=>{var l=e.data;return(0,_vue.h)(getOldComponent(n),Object.assign(Object.assign({key:t},getCellEditFilterProps(n,r,l,{options:a,optionProps:u,optionGroups:o,optionGroupProps:i})),getFilterOns(n,r,e)))})},tableFilterDefaultMethod:handleFilterMethod,tableExportMethod:handleExportSelectMethod},$radio:{tableAutoFocus:".vxe-radio--input"},$checkbox:{tableAutoFocus:".vxe-checkbox--input"},$switch:{tableAutoFocus:".vxe-switch--button",renderTableEdit:oldEditRender,renderTableDefault:oldEditRender}});