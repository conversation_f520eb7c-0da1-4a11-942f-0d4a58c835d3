{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/csharp/csharp.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\#\\$\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n    comments: {\n        lineComment: '//',\n        blockComment: ['/*', '*/']\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: \"'\", close: \"'\", notIn: ['string', 'comment'] },\n        { open: '\"', close: '\"', notIn: ['string', 'comment'] }\n    ],\n    surroundingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '<', close: '>' },\n        { open: \"'\", close: \"'\" },\n        { open: '\"', close: '\"' }\n    ],\n    folding: {\n        markers: {\n            start: new RegExp('^\\\\s*#region\\\\b'),\n            end: new RegExp('^\\\\s*#endregion\\\\b')\n        }\n    }\n};\nexport var language = {\n    defaultToken: '',\n    tokenPostfix: '.cs',\n    brackets: [\n        { open: '{', close: '}', token: 'delimiter.curly' },\n        { open: '[', close: ']', token: 'delimiter.square' },\n        { open: '(', close: ')', token: 'delimiter.parenthesis' },\n        { open: '<', close: '>', token: 'delimiter.angle' }\n    ],\n    keywords: [\n        'extern',\n        'alias',\n        'using',\n        'bool',\n        'decimal',\n        'sbyte',\n        'byte',\n        'short',\n        'ushort',\n        'int',\n        'uint',\n        'long',\n        'ulong',\n        'char',\n        'float',\n        'double',\n        'object',\n        'dynamic',\n        'string',\n        'assembly',\n        'is',\n        'as',\n        'ref',\n        'out',\n        'this',\n        'base',\n        'new',\n        'typeof',\n        'void',\n        'checked',\n        'unchecked',\n        'default',\n        'delegate',\n        'var',\n        'const',\n        'if',\n        'else',\n        'switch',\n        'case',\n        'while',\n        'do',\n        'for',\n        'foreach',\n        'in',\n        'break',\n        'continue',\n        'goto',\n        'return',\n        'throw',\n        'try',\n        'catch',\n        'finally',\n        'lock',\n        'yield',\n        'from',\n        'let',\n        'where',\n        'join',\n        'on',\n        'equals',\n        'into',\n        'orderby',\n        'ascending',\n        'descending',\n        'select',\n        'group',\n        'by',\n        'namespace',\n        'partial',\n        'class',\n        'field',\n        'event',\n        'method',\n        'param',\n        'public',\n        'protected',\n        'internal',\n        'private',\n        'abstract',\n        'sealed',\n        'static',\n        'struct',\n        'readonly',\n        'volatile',\n        'virtual',\n        'override',\n        'params',\n        'get',\n        'set',\n        'add',\n        'remove',\n        'operator',\n        'true',\n        'false',\n        'implicit',\n        'explicit',\n        'interface',\n        'enum',\n        'null',\n        'async',\n        'await',\n        'fixed',\n        'sizeof',\n        'stackalloc',\n        'unsafe',\n        'nameof',\n        'when'\n    ],\n    namespaceFollows: ['namespace', 'using'],\n    parenFollows: ['if', 'for', 'while', 'switch', 'foreach', 'using', 'catch', 'when'],\n    operators: [\n        '=',\n        '??',\n        '||',\n        '&&',\n        '|',\n        '^',\n        '&',\n        '==',\n        '!=',\n        '<=',\n        '>=',\n        '<<',\n        '+',\n        '-',\n        '*',\n        '/',\n        '%',\n        '!',\n        '~',\n        '++',\n        '--',\n        '+=',\n        '-=',\n        '*=',\n        '/=',\n        '%=',\n        '&=',\n        '|=',\n        '^=',\n        '<<=',\n        '>>=',\n        '>>',\n        '=>'\n    ],\n    symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n    // escape sequences\n    escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n    // The main tokenizer for our languages\n    tokenizer: {\n        root: [\n            // identifiers and keywords\n            [\n                /\\@?[a-zA-Z_]\\w*/,\n                {\n                    cases: {\n                        '@namespaceFollows': {\n                            token: 'keyword.$0',\n                            next: '@namespace'\n                        },\n                        '@keywords': {\n                            token: 'keyword.$0',\n                            next: '@qualified'\n                        },\n                        '@default': { token: 'identifier', next: '@qualified' }\n                    }\n                }\n            ],\n            // whitespace\n            { include: '@whitespace' },\n            // delimiters and operators\n            [\n                /}/,\n                {\n                    cases: {\n                        '$S2==interpolatedstring': {\n                            token: 'string.quote',\n                            next: '@pop'\n                        },\n                        '$S2==litinterpstring': {\n                            token: 'string.quote',\n                            next: '@pop'\n                        },\n                        '@default': '@brackets'\n                    }\n                }\n            ],\n            [/[{}()\\[\\]]/, '@brackets'],\n            [/[<>](?!@symbols)/, '@brackets'],\n            [\n                /@symbols/,\n                {\n                    cases: {\n                        '@operators': 'delimiter',\n                        '@default': ''\n                    }\n                }\n            ],\n            // numbers\n            [/[0-9_]*\\.[0-9_]+([eE][\\-+]?\\d+)?[fFdD]?/, 'number.float'],\n            [/0[xX][0-9a-fA-F_]+/, 'number.hex'],\n            [/0[bB][01_]+/, 'number.hex'],\n            [/[0-9_]+/, 'number'],\n            // delimiter: after number because of .\\d floats\n            [/[;,.]/, 'delimiter'],\n            // strings\n            [/\"([^\"\\\\]|\\\\.)*$/, 'string.invalid'],\n            [/\"/, { token: 'string.quote', next: '@string' }],\n            [/\\$\\@\"/, { token: 'string.quote', next: '@litinterpstring' }],\n            [/\\@\"/, { token: 'string.quote', next: '@litstring' }],\n            [/\\$\"/, { token: 'string.quote', next: '@interpolatedstring' }],\n            // characters\n            [/'[^\\\\']'/, 'string'],\n            [/(')(@escapes)(')/, ['string', 'string.escape', 'string']],\n            [/'/, 'string.invalid']\n        ],\n        qualified: [\n            [\n                /[a-zA-Z_][\\w]*/,\n                {\n                    cases: {\n                        '@keywords': { token: 'keyword.$0' },\n                        '@default': 'identifier'\n                    }\n                }\n            ],\n            [/\\./, 'delimiter'],\n            ['', '', '@pop']\n        ],\n        namespace: [\n            { include: '@whitespace' },\n            [/[A-Z]\\w*/, 'namespace'],\n            [/[\\.=]/, 'delimiter'],\n            ['', '', '@pop']\n        ],\n        comment: [\n            [/[^\\/*]+/, 'comment'],\n            // [/\\/\\*/,    'comment', '@push' ],    // no nested comments :-(\n            ['\\\\*/', 'comment', '@pop'],\n            [/[\\/*]/, 'comment']\n        ],\n        string: [\n            [/[^\\\\\"]+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/\\\\./, 'string.escape.invalid'],\n            [/\"/, { token: 'string.quote', next: '@pop' }]\n        ],\n        litstring: [\n            [/[^\"]+/, 'string'],\n            [/\"\"/, 'string.escape'],\n            [/\"/, { token: 'string.quote', next: '@pop' }]\n        ],\n        litinterpstring: [\n            [/[^\"{]+/, 'string'],\n            [/\"\"/, 'string.escape'],\n            [/{{/, 'string.escape'],\n            [/}}/, 'string.escape'],\n            [/{/, { token: 'string.quote', next: 'root.litinterpstring' }],\n            [/\"/, { token: 'string.quote', next: '@pop' }]\n        ],\n        interpolatedstring: [\n            [/[^\\\\\"{]+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/\\\\./, 'string.escape.invalid'],\n            [/{{/, 'string.escape'],\n            [/}}/, 'string.escape'],\n            [/{/, { token: 'string.quote', next: 'root.interpolatedstring' }],\n            [/\"/, { token: 'string.quote', next: '@pop' }]\n        ],\n        whitespace: [\n            [/^[ \\t\\v\\f]*#((r)|(load))(?=\\s)/, 'directive.csx'],\n            [/^[ \\t\\v\\f]*#\\w.*$/, 'namespace.cpp'],\n            [/[ \\t\\v\\f\\r\\n]+/, ''],\n            [/\\/\\*/, 'comment', '@comment'],\n            [/\\/\\/.*$/, 'comment']\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,aAAa;AAAA,EACb,UAAU;AAAA,IACN,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC7B;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EAC1D;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,SAAS;AAAA,IACL,SAAS;AAAA,MACL,OAAO,IAAI,OAAO,iBAAiB;AAAA,MACnC,KAAK,IAAI,OAAO,oBAAoB;AAAA,IACxC;AAAA,EACJ;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACN,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,EACtD;AAAA,EACA,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,kBAAkB,CAAC,aAAa,OAAO;AAAA,EACvC,cAAc,CAAC,MAAM,OAAO,SAAS,UAAU,WAAW,SAAS,SAAS,MAAM;AAAA,EAClF,WAAW;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA;AAAA,EAET,SAAS;AAAA;AAAA,EAET,WAAW;AAAA,IACP,MAAM;AAAA;AAAA,MAEF;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,qBAAqB;AAAA,cACjB,OAAO;AAAA,cACP,MAAM;AAAA,YACV;AAAA,YACA,aAAa;AAAA,cACT,OAAO;AAAA,cACP,MAAM;AAAA,YACV;AAAA,YACA,YAAY,EAAE,OAAO,cAAc,MAAM,aAAa;AAAA,UAC1D;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,2BAA2B;AAAA,cACvB,OAAO;AAAA,cACP,MAAM;AAAA,YACV;AAAA,YACA,wBAAwB;AAAA,cACpB,OAAO;AAAA,cACP,MAAM;AAAA,YACV;AAAA,YACA,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,oBAAoB,WAAW;AAAA,MAChC;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,cAAc;AAAA,YACd,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,CAAC,2CAA2C,cAAc;AAAA,MAC1D,CAAC,sBAAsB,YAAY;AAAA,MACnC,CAAC,eAAe,YAAY;AAAA,MAC5B,CAAC,WAAW,QAAQ;AAAA;AAAA,MAEpB,CAAC,SAAS,WAAW;AAAA;AAAA,MAErB,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,UAAU,CAAC;AAAA,MAChD,CAAC,SAAS,EAAE,OAAO,gBAAgB,MAAM,mBAAmB,CAAC;AAAA,MAC7D,CAAC,OAAO,EAAE,OAAO,gBAAgB,MAAM,aAAa,CAAC;AAAA,MACrD,CAAC,OAAO,EAAE,OAAO,gBAAgB,MAAM,sBAAsB,CAAC;AAAA;AAAA,MAE9D,CAAC,YAAY,QAAQ;AAAA,MACrB,CAAC,oBAAoB,CAAC,UAAU,iBAAiB,QAAQ,CAAC;AAAA,MAC1D,CAAC,KAAK,gBAAgB;AAAA,IAC1B;AAAA,IACA,WAAW;AAAA,MACP;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,aAAa,EAAE,OAAO,aAAa;AAAA,YACnC,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,CAAC,MAAM,WAAW;AAAA,MAClB,CAAC,IAAI,IAAI,MAAM;AAAA,IACnB;AAAA,IACA,WAAW;AAAA,MACP,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,YAAY,WAAW;AAAA,MACxB,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,IAAI,IAAI,MAAM;AAAA,IACnB;AAAA,IACA,SAAS;AAAA,MACL,CAAC,WAAW,SAAS;AAAA;AAAA,MAErB,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,SAAS,SAAS;AAAA,IACvB;AAAA,IACA,QAAQ;AAAA,MACJ,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,OAAO,CAAC;AAAA,IACjD;AAAA,IACA,WAAW;AAAA,MACP,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,MAAM,eAAe;AAAA,MACtB,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,OAAO,CAAC;AAAA,IACjD;AAAA,IACA,iBAAiB;AAAA,MACb,CAAC,UAAU,QAAQ;AAAA,MACnB,CAAC,MAAM,eAAe;AAAA,MACtB,CAAC,MAAM,eAAe;AAAA,MACtB,CAAC,MAAM,eAAe;AAAA,MACtB,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,uBAAuB,CAAC;AAAA,MAC7D,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,OAAO,CAAC;AAAA,IACjD;AAAA,IACA,oBAAoB;AAAA,MAChB,CAAC,YAAY,QAAQ;AAAA,MACrB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,MAAM,eAAe;AAAA,MACtB,CAAC,MAAM,eAAe;AAAA,MACtB,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,0BAA0B,CAAC;AAAA,MAChE,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,OAAO,CAAC;AAAA,IACjD;AAAA,IACA,YAAY;AAAA,MACR,CAAC,kCAAkC,eAAe;AAAA,MAClD,CAAC,qBAAqB,eAAe;AAAA,MACrC,CAAC,kBAAkB,EAAE;AAAA,MACrB,CAAC,QAAQ,WAAW,UAAU;AAAA,MAC9B,CAAC,WAAW,SAAS;AAAA,IACzB;AAAA,EACJ;AACJ;", "names": []}