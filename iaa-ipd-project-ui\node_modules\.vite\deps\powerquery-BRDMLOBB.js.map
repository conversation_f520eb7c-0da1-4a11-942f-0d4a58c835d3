{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/powerquery/powerquery.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    comments: {\n        lineComment: '//',\n        blockComment: ['/*', '*/']\n    },\n    brackets: [\n        ['[', ']'],\n        ['(', ')'],\n        ['{', '}']\n    ],\n    autoClosingPairs: [\n        { open: '\"', close: '\"', notIn: ['string', 'comment', 'identifier'] },\n        { open: '[', close: ']', notIn: ['string', 'comment', 'identifier'] },\n        { open: '(', close: ')', notIn: ['string', 'comment', 'identifier'] },\n        { open: '{', close: '}', notIn: ['string', 'comment', 'identifier'] }\n    ]\n};\nexport var language = {\n    defaultToken: '',\n    tokenPostfix: '.pq',\n    ignoreCase: false,\n    brackets: [\n        { open: '[', close: ']', token: 'delimiter.square' },\n        { open: '{', close: '}', token: 'delimiter.brackets' },\n        { open: '(', close: ')', token: 'delimiter.parenthesis' }\n    ],\n    operatorKeywords: ['and', 'not', 'or'],\n    keywords: [\n        'as',\n        'each',\n        'else',\n        'error',\n        'false',\n        'if',\n        'in',\n        'is',\n        'let',\n        'meta',\n        'otherwise',\n        'section',\n        'shared',\n        'then',\n        'true',\n        'try',\n        'type'\n    ],\n    constructors: ['#binary', '#date', '#datetime', '#datetimezone', '#duration', '#table', '#time'],\n    constants: ['#infinity', '#nan', '#sections', '#shared'],\n    typeKeywords: [\n        'action',\n        'any',\n        'anynonnull',\n        'none',\n        'null',\n        'logical',\n        'number',\n        'time',\n        'date',\n        'datetime',\n        'datetimezone',\n        'duration',\n        'text',\n        'binary',\n        'list',\n        'record',\n        'table',\n        'function'\n    ],\n    builtinFunctions: [\n        'Access.Database',\n        'Action.Return',\n        'Action.Sequence',\n        'Action.Try',\n        'ActiveDirectory.Domains',\n        'AdoDotNet.DataSource',\n        'AdoDotNet.Query',\n        'AdobeAnalytics.Cubes',\n        'AnalysisServices.Database',\n        'AnalysisServices.Databases',\n        'AzureStorage.BlobContents',\n        'AzureStorage.Blobs',\n        'AzureStorage.Tables',\n        'Binary.Buffer',\n        'Binary.Combine',\n        'Binary.Compress',\n        'Binary.Decompress',\n        'Binary.End',\n        'Binary.From',\n        'Binary.FromList',\n        'Binary.FromText',\n        'Binary.InferContentType',\n        'Binary.Length',\n        'Binary.ToList',\n        'Binary.ToText',\n        'BinaryFormat.7BitEncodedSignedInteger',\n        'BinaryFormat.7BitEncodedUnsignedInteger',\n        'BinaryFormat.Binary',\n        'BinaryFormat.Byte',\n        'BinaryFormat.ByteOrder',\n        'BinaryFormat.Choice',\n        'BinaryFormat.Decimal',\n        'BinaryFormat.Double',\n        'BinaryFormat.Group',\n        'BinaryFormat.Length',\n        'BinaryFormat.List',\n        'BinaryFormat.Null',\n        'BinaryFormat.Record',\n        'BinaryFormat.SignedInteger16',\n        'BinaryFormat.SignedInteger32',\n        'BinaryFormat.SignedInteger64',\n        'BinaryFormat.Single',\n        'BinaryFormat.Text',\n        'BinaryFormat.Transform',\n        'BinaryFormat.UnsignedInteger16',\n        'BinaryFormat.UnsignedInteger32',\n        'BinaryFormat.UnsignedInteger64',\n        'Byte.From',\n        'Character.FromNumber',\n        'Character.ToNumber',\n        'Combiner.CombineTextByDelimiter',\n        'Combiner.CombineTextByEachDelimiter',\n        'Combiner.CombineTextByLengths',\n        'Combiner.CombineTextByPositions',\n        'Combiner.CombineTextByRanges',\n        'Comparer.Equals',\n        'Comparer.FromCulture',\n        'Comparer.Ordinal',\n        'Comparer.OrdinalIgnoreCase',\n        'Csv.Document',\n        'Cube.AddAndExpandDimensionColumn',\n        'Cube.AddMeasureColumn',\n        'Cube.ApplyParameter',\n        'Cube.AttributeMemberId',\n        'Cube.AttributeMemberProperty',\n        'Cube.CollapseAndRemoveColumns',\n        'Cube.Dimensions',\n        'Cube.DisplayFolders',\n        'Cube.Measures',\n        'Cube.Parameters',\n        'Cube.Properties',\n        'Cube.PropertyKey',\n        'Cube.ReplaceDimensions',\n        'Cube.Transform',\n        'Currency.From',\n        'DB2.Database',\n        'Date.AddDays',\n        'Date.AddMonths',\n        'Date.AddQuarters',\n        'Date.AddWeeks',\n        'Date.AddYears',\n        'Date.Day',\n        'Date.DayOfWeek',\n        'Date.DayOfWeekName',\n        'Date.DayOfYear',\n        'Date.DaysInMonth',\n        'Date.EndOfDay',\n        'Date.EndOfMonth',\n        'Date.EndOfQuarter',\n        'Date.EndOfWeek',\n        'Date.EndOfYear',\n        'Date.From',\n        'Date.FromText',\n        'Date.IsInCurrentDay',\n        'Date.IsInCurrentMonth',\n        'Date.IsInCurrentQuarter',\n        'Date.IsInCurrentWeek',\n        'Date.IsInCurrentYear',\n        'Date.IsInNextDay',\n        'Date.IsInNextMonth',\n        'Date.IsInNextNDays',\n        'Date.IsInNextNMonths',\n        'Date.IsInNextNQuarters',\n        'Date.IsInNextNWeeks',\n        'Date.IsInNextNYears',\n        'Date.IsInNextQuarter',\n        'Date.IsInNextWeek',\n        'Date.IsInNextYear',\n        'Date.IsInPreviousDay',\n        'Date.IsInPreviousMonth',\n        'Date.IsInPreviousNDays',\n        'Date.IsInPreviousNMonths',\n        'Date.IsInPreviousNQuarters',\n        'Date.IsInPreviousNWeeks',\n        'Date.IsInPreviousNYears',\n        'Date.IsInPreviousQuarter',\n        'Date.IsInPreviousWeek',\n        'Date.IsInPreviousYear',\n        'Date.IsInYearToDate',\n        'Date.IsLeapYear',\n        'Date.Month',\n        'Date.MonthName',\n        'Date.QuarterOfYear',\n        'Date.StartOfDay',\n        'Date.StartOfMonth',\n        'Date.StartOfQuarter',\n        'Date.StartOfWeek',\n        'Date.StartOfYear',\n        'Date.ToRecord',\n        'Date.ToText',\n        'Date.WeekOfMonth',\n        'Date.WeekOfYear',\n        'Date.Year',\n        'DateTime.AddZone',\n        'DateTime.Date',\n        'DateTime.FixedLocalNow',\n        'DateTime.From',\n        'DateTime.FromFileTime',\n        'DateTime.FromText',\n        'DateTime.IsInCurrentHour',\n        'DateTime.IsInCurrentMinute',\n        'DateTime.IsInCurrentSecond',\n        'DateTime.IsInNextHour',\n        'DateTime.IsInNextMinute',\n        'DateTime.IsInNextNHours',\n        'DateTime.IsInNextNMinutes',\n        'DateTime.IsInNextNSeconds',\n        'DateTime.IsInNextSecond',\n        'DateTime.IsInPreviousHour',\n        'DateTime.IsInPreviousMinute',\n        'DateTime.IsInPreviousNHours',\n        'DateTime.IsInPreviousNMinutes',\n        'DateTime.IsInPreviousNSeconds',\n        'DateTime.IsInPreviousSecond',\n        'DateTime.LocalNow',\n        'DateTime.Time',\n        'DateTime.ToRecord',\n        'DateTime.ToText',\n        'DateTimeZone.FixedLocalNow',\n        'DateTimeZone.FixedUtcNow',\n        'DateTimeZone.From',\n        'DateTimeZone.FromFileTime',\n        'DateTimeZone.FromText',\n        'DateTimeZone.LocalNow',\n        'DateTimeZone.RemoveZone',\n        'DateTimeZone.SwitchZone',\n        'DateTimeZone.ToLocal',\n        'DateTimeZone.ToRecord',\n        'DateTimeZone.ToText',\n        'DateTimeZone.ToUtc',\n        'DateTimeZone.UtcNow',\n        'DateTimeZone.ZoneHours',\n        'DateTimeZone.ZoneMinutes',\n        'Decimal.From',\n        'Diagnostics.ActivityId',\n        'Diagnostics.Trace',\n        'DirectQueryCapabilities.From',\n        'Double.From',\n        'Duration.Days',\n        'Duration.From',\n        'Duration.FromText',\n        'Duration.Hours',\n        'Duration.Minutes',\n        'Duration.Seconds',\n        'Duration.ToRecord',\n        'Duration.ToText',\n        'Duration.TotalDays',\n        'Duration.TotalHours',\n        'Duration.TotalMinutes',\n        'Duration.TotalSeconds',\n        'Embedded.Value',\n        'Error.Record',\n        'Excel.CurrentWorkbook',\n        'Excel.Workbook',\n        'Exchange.Contents',\n        'Expression.Constant',\n        'Expression.Evaluate',\n        'Expression.Identifier',\n        'Facebook.Graph',\n        'File.Contents',\n        'Folder.Contents',\n        'Folder.Files',\n        'Function.From',\n        'Function.Invoke',\n        'Function.InvokeAfter',\n        'Function.IsDataSource',\n        'GoogleAnalytics.Accounts',\n        'Guid.From',\n        'HdInsight.Containers',\n        'HdInsight.Contents',\n        'HdInsight.Files',\n        'Hdfs.Contents',\n        'Hdfs.Files',\n        'Informix.Database',\n        'Int16.From',\n        'Int32.From',\n        'Int64.From',\n        'Int8.From',\n        'ItemExpression.From',\n        'Json.Document',\n        'Json.FromValue',\n        'Lines.FromBinary',\n        'Lines.FromText',\n        'Lines.ToBinary',\n        'Lines.ToText',\n        'List.Accumulate',\n        'List.AllTrue',\n        'List.Alternate',\n        'List.AnyTrue',\n        'List.Average',\n        'List.Buffer',\n        'List.Combine',\n        'List.Contains',\n        'List.ContainsAll',\n        'List.ContainsAny',\n        'List.Count',\n        'List.Covariance',\n        'List.DateTimeZones',\n        'List.DateTimes',\n        'List.Dates',\n        'List.Difference',\n        'List.Distinct',\n        'List.Durations',\n        'List.FindText',\n        'List.First',\n        'List.FirstN',\n        'List.Generate',\n        'List.InsertRange',\n        'List.Intersect',\n        'List.IsDistinct',\n        'List.IsEmpty',\n        'List.Last',\n        'List.LastN',\n        'List.MatchesAll',\n        'List.MatchesAny',\n        'List.Max',\n        'List.MaxN',\n        'List.Median',\n        'List.Min',\n        'List.MinN',\n        'List.Mode',\n        'List.Modes',\n        'List.NonNullCount',\n        'List.Numbers',\n        'List.PositionOf',\n        'List.PositionOfAny',\n        'List.Positions',\n        'List.Product',\n        'List.Random',\n        'List.Range',\n        'List.RemoveFirstN',\n        'List.RemoveItems',\n        'List.RemoveLastN',\n        'List.RemoveMatchingItems',\n        'List.RemoveNulls',\n        'List.RemoveRange',\n        'List.Repeat',\n        'List.ReplaceMatchingItems',\n        'List.ReplaceRange',\n        'List.ReplaceValue',\n        'List.Reverse',\n        'List.Select',\n        'List.Single',\n        'List.SingleOrDefault',\n        'List.Skip',\n        'List.Sort',\n        'List.StandardDeviation',\n        'List.Sum',\n        'List.Times',\n        'List.Transform',\n        'List.TransformMany',\n        'List.Union',\n        'List.Zip',\n        'Logical.From',\n        'Logical.FromText',\n        'Logical.ToText',\n        'MQ.Queue',\n        'MySQL.Database',\n        'Number.Abs',\n        'Number.Acos',\n        'Number.Asin',\n        'Number.Atan',\n        'Number.Atan2',\n        'Number.BitwiseAnd',\n        'Number.BitwiseNot',\n        'Number.BitwiseOr',\n        'Number.BitwiseShiftLeft',\n        'Number.BitwiseShiftRight',\n        'Number.BitwiseXor',\n        'Number.Combinations',\n        'Number.Cos',\n        'Number.Cosh',\n        'Number.Exp',\n        'Number.Factorial',\n        'Number.From',\n        'Number.FromText',\n        'Number.IntegerDivide',\n        'Number.IsEven',\n        'Number.IsNaN',\n        'Number.IsOdd',\n        'Number.Ln',\n        'Number.Log',\n        'Number.Log10',\n        'Number.Mod',\n        'Number.Permutations',\n        'Number.Power',\n        'Number.Random',\n        'Number.RandomBetween',\n        'Number.Round',\n        'Number.RoundAwayFromZero',\n        'Number.RoundDown',\n        'Number.RoundTowardZero',\n        'Number.RoundUp',\n        'Number.Sign',\n        'Number.Sin',\n        'Number.Sinh',\n        'Number.Sqrt',\n        'Number.Tan',\n        'Number.Tanh',\n        'Number.ToText',\n        'OData.Feed',\n        'Odbc.DataSource',\n        'Odbc.Query',\n        'OleDb.DataSource',\n        'OleDb.Query',\n        'Oracle.Database',\n        'Percentage.From',\n        'PostgreSQL.Database',\n        'RData.FromBinary',\n        'Record.AddField',\n        'Record.Combine',\n        'Record.Field',\n        'Record.FieldCount',\n        'Record.FieldNames',\n        'Record.FieldOrDefault',\n        'Record.FieldValues',\n        'Record.FromList',\n        'Record.FromTable',\n        'Record.HasFields',\n        'Record.RemoveFields',\n        'Record.RenameFields',\n        'Record.ReorderFields',\n        'Record.SelectFields',\n        'Record.ToList',\n        'Record.ToTable',\n        'Record.TransformFields',\n        'Replacer.ReplaceText',\n        'Replacer.ReplaceValue',\n        'RowExpression.Column',\n        'RowExpression.From',\n        'Salesforce.Data',\n        'Salesforce.Reports',\n        'SapBusinessWarehouse.Cubes',\n        'SapHana.Database',\n        'SharePoint.Contents',\n        'SharePoint.Files',\n        'SharePoint.Tables',\n        'Single.From',\n        'Soda.Feed',\n        'Splitter.SplitByNothing',\n        'Splitter.SplitTextByAnyDelimiter',\n        'Splitter.SplitTextByDelimiter',\n        'Splitter.SplitTextByEachDelimiter',\n        'Splitter.SplitTextByLengths',\n        'Splitter.SplitTextByPositions',\n        'Splitter.SplitTextByRanges',\n        'Splitter.SplitTextByRepeatedLengths',\n        'Splitter.SplitTextByWhitespace',\n        'Sql.Database',\n        'Sql.Databases',\n        'SqlExpression.SchemaFrom',\n        'SqlExpression.ToExpression',\n        'Sybase.Database',\n        'Table.AddColumn',\n        'Table.AddIndexColumn',\n        'Table.AddJoinColumn',\n        'Table.AddKey',\n        'Table.AggregateTableColumn',\n        'Table.AlternateRows',\n        'Table.Buffer',\n        'Table.Column',\n        'Table.ColumnCount',\n        'Table.ColumnNames',\n        'Table.ColumnsOfType',\n        'Table.Combine',\n        'Table.CombineColumns',\n        'Table.Contains',\n        'Table.ContainsAll',\n        'Table.ContainsAny',\n        'Table.DemoteHeaders',\n        'Table.Distinct',\n        'Table.DuplicateColumn',\n        'Table.ExpandListColumn',\n        'Table.ExpandRecordColumn',\n        'Table.ExpandTableColumn',\n        'Table.FillDown',\n        'Table.FillUp',\n        'Table.FilterWithDataTable',\n        'Table.FindText',\n        'Table.First',\n        'Table.FirstN',\n        'Table.FirstValue',\n        'Table.FromColumns',\n        'Table.FromList',\n        'Table.FromPartitions',\n        'Table.FromRecords',\n        'Table.FromRows',\n        'Table.FromValue',\n        'Table.Group',\n        'Table.HasColumns',\n        'Table.InsertRows',\n        'Table.IsDistinct',\n        'Table.IsEmpty',\n        'Table.Join',\n        'Table.Keys',\n        'Table.Last',\n        'Table.LastN',\n        'Table.MatchesAllRows',\n        'Table.MatchesAnyRows',\n        'Table.Max',\n        'Table.MaxN',\n        'Table.Min',\n        'Table.MinN',\n        'Table.NestedJoin',\n        'Table.Partition',\n        'Table.PartitionValues',\n        'Table.Pivot',\n        'Table.PositionOf',\n        'Table.PositionOfAny',\n        'Table.PrefixColumns',\n        'Table.Profile',\n        'Table.PromoteHeaders',\n        'Table.Range',\n        'Table.RemoveColumns',\n        'Table.RemoveFirstN',\n        'Table.RemoveLastN',\n        'Table.RemoveMatchingRows',\n        'Table.RemoveRows',\n        'Table.RemoveRowsWithErrors',\n        'Table.RenameColumns',\n        'Table.ReorderColumns',\n        'Table.Repeat',\n        'Table.ReplaceErrorValues',\n        'Table.ReplaceKeys',\n        'Table.ReplaceMatchingRows',\n        'Table.ReplaceRelationshipIdentity',\n        'Table.ReplaceRows',\n        'Table.ReplaceValue',\n        'Table.ReverseRows',\n        'Table.RowCount',\n        'Table.Schema',\n        'Table.SelectColumns',\n        'Table.SelectRows',\n        'Table.SelectRowsWithErrors',\n        'Table.SingleRow',\n        'Table.Skip',\n        'Table.Sort',\n        'Table.SplitColumn',\n        'Table.ToColumns',\n        'Table.ToList',\n        'Table.ToRecords',\n        'Table.ToRows',\n        'Table.TransformColumnNames',\n        'Table.TransformColumnTypes',\n        'Table.TransformColumns',\n        'Table.TransformRows',\n        'Table.Transpose',\n        'Table.Unpivot',\n        'Table.UnpivotOtherColumns',\n        'Table.View',\n        'Table.ViewFunction',\n        'TableAction.DeleteRows',\n        'TableAction.InsertRows',\n        'TableAction.UpdateRows',\n        'Tables.GetRelationships',\n        'Teradata.Database',\n        'Text.AfterDelimiter',\n        'Text.At',\n        'Text.BeforeDelimiter',\n        'Text.BetweenDelimiters',\n        'Text.Clean',\n        'Text.Combine',\n        'Text.Contains',\n        'Text.End',\n        'Text.EndsWith',\n        'Text.Format',\n        'Text.From',\n        'Text.FromBinary',\n        'Text.Insert',\n        'Text.Length',\n        'Text.Lower',\n        'Text.Middle',\n        'Text.NewGuid',\n        'Text.PadEnd',\n        'Text.PadStart',\n        'Text.PositionOf',\n        'Text.PositionOfAny',\n        'Text.Proper',\n        'Text.Range',\n        'Text.Remove',\n        'Text.RemoveRange',\n        'Text.Repeat',\n        'Text.Replace',\n        'Text.ReplaceRange',\n        'Text.Select',\n        'Text.Split',\n        'Text.SplitAny',\n        'Text.Start',\n        'Text.StartsWith',\n        'Text.ToBinary',\n        'Text.ToList',\n        'Text.Trim',\n        'Text.TrimEnd',\n        'Text.TrimStart',\n        'Text.Upper',\n        'Time.EndOfHour',\n        'Time.From',\n        'Time.FromText',\n        'Time.Hour',\n        'Time.Minute',\n        'Time.Second',\n        'Time.StartOfHour',\n        'Time.ToRecord',\n        'Time.ToText',\n        'Type.AddTableKey',\n        'Type.ClosedRecord',\n        'Type.Facets',\n        'Type.ForFunction',\n        'Type.ForRecord',\n        'Type.FunctionParameters',\n        'Type.FunctionRequiredParameters',\n        'Type.FunctionReturn',\n        'Type.Is',\n        'Type.IsNullable',\n        'Type.IsOpenRecord',\n        'Type.ListItem',\n        'Type.NonNullable',\n        'Type.OpenRecord',\n        'Type.RecordFields',\n        'Type.ReplaceFacets',\n        'Type.ReplaceTableKeys',\n        'Type.TableColumn',\n        'Type.TableKeys',\n        'Type.TableRow',\n        'Type.TableSchema',\n        'Type.Union',\n        'Uri.BuildQueryString',\n        'Uri.Combine',\n        'Uri.EscapeDataString',\n        'Uri.Parts',\n        'Value.Add',\n        'Value.As',\n        'Value.Compare',\n        'Value.Divide',\n        'Value.Equals',\n        'Value.Firewall',\n        'Value.FromText',\n        'Value.Is',\n        'Value.Metadata',\n        'Value.Multiply',\n        'Value.NativeQuery',\n        'Value.NullableEquals',\n        'Value.RemoveMetadata',\n        'Value.ReplaceMetadata',\n        'Value.ReplaceType',\n        'Value.Subtract',\n        'Value.Type',\n        'ValueAction.NativeStatement',\n        'ValueAction.Replace',\n        'Variable.Value',\n        'Web.Contents',\n        'Web.Page',\n        'WebAction.Request',\n        'Xml.Document',\n        'Xml.Tables'\n    ],\n    builtinConstants: [\n        'BinaryEncoding.Base64',\n        'BinaryEncoding.Hex',\n        'BinaryOccurrence.Optional',\n        'BinaryOccurrence.Repeating',\n        'BinaryOccurrence.Required',\n        'ByteOrder.BigEndian',\n        'ByteOrder.LittleEndian',\n        'Compression.Deflate',\n        'Compression.GZip',\n        'CsvStyle.QuoteAfterDelimiter',\n        'CsvStyle.QuoteAlways',\n        'Culture.Current',\n        'Day.Friday',\n        'Day.Monday',\n        'Day.Saturday',\n        'Day.Sunday',\n        'Day.Thursday',\n        'Day.Tuesday',\n        'Day.Wednesday',\n        'ExtraValues.Error',\n        'ExtraValues.Ignore',\n        'ExtraValues.List',\n        'GroupKind.Global',\n        'GroupKind.Local',\n        'JoinAlgorithm.Dynamic',\n        'JoinAlgorithm.LeftHash',\n        'JoinAlgorithm.LeftIndex',\n        'JoinAlgorithm.PairwiseHash',\n        'JoinAlgorithm.RightHash',\n        'JoinAlgorithm.RightIndex',\n        'JoinAlgorithm.SortMerge',\n        'JoinKind.FullOuter',\n        'JoinKind.Inner',\n        'JoinKind.LeftAnti',\n        'JoinKind.LeftOuter',\n        'JoinKind.RightAnti',\n        'JoinKind.RightOuter',\n        'JoinSide.Left',\n        'JoinSide.Right',\n        'MissingField.Error',\n        'MissingField.Ignore',\n        'MissingField.UseNull',\n        'Number.E',\n        'Number.Epsilon',\n        'Number.NaN',\n        'Number.NegativeInfinity',\n        'Number.PI',\n        'Number.PositiveInfinity',\n        'Occurrence.All',\n        'Occurrence.First',\n        'Occurrence.Last',\n        'Occurrence.Optional',\n        'Occurrence.Repeating',\n        'Occurrence.Required',\n        'Order.Ascending',\n        'Order.Descending',\n        'Precision.Decimal',\n        'Precision.Double',\n        'QuoteStyle.Csv',\n        'QuoteStyle.None',\n        'RelativePosition.FromEnd',\n        'RelativePosition.FromStart',\n        'RoundingMode.AwayFromZero',\n        'RoundingMode.Down',\n        'RoundingMode.ToEven',\n        'RoundingMode.TowardZero',\n        'RoundingMode.Up',\n        'SapHanaDistribution.All',\n        'SapHanaDistribution.Connection',\n        'SapHanaDistribution.Off',\n        'SapHanaDistribution.Statement',\n        'SapHanaRangeOperator.Equals',\n        'SapHanaRangeOperator.GreaterThan',\n        'SapHanaRangeOperator.GreaterThanOrEquals',\n        'SapHanaRangeOperator.LessThan',\n        'SapHanaRangeOperator.LessThanOrEquals',\n        'SapHanaRangeOperator.NotEquals',\n        'TextEncoding.Ascii',\n        'TextEncoding.BigEndianUnicode',\n        'TextEncoding.Unicode',\n        'TextEncoding.Utf16',\n        'TextEncoding.Utf8',\n        'TextEncoding.Windows',\n        'TraceLevel.Critical',\n        'TraceLevel.Error',\n        'TraceLevel.Information',\n        'TraceLevel.Verbose',\n        'TraceLevel.Warning',\n        'WebMethod.Delete',\n        'WebMethod.Get',\n        'WebMethod.Head',\n        'WebMethod.Patch',\n        'WebMethod.Post',\n        'WebMethod.Put'\n    ],\n    builtinTypes: [\n        'Action.Type',\n        'Any.Type',\n        'Binary.Type',\n        'BinaryEncoding.Type',\n        'BinaryOccurrence.Type',\n        'Byte.Type',\n        'ByteOrder.Type',\n        'Character.Type',\n        'Compression.Type',\n        'CsvStyle.Type',\n        'Currency.Type',\n        'Date.Type',\n        'DateTime.Type',\n        'DateTimeZone.Type',\n        'Day.Type',\n        'Decimal.Type',\n        'Double.Type',\n        'Duration.Type',\n        'ExtraValues.Type',\n        'Function.Type',\n        'GroupKind.Type',\n        'Guid.Type',\n        'Int16.Type',\n        'Int32.Type',\n        'Int64.Type',\n        'Int8.Type',\n        'JoinAlgorithm.Type',\n        'JoinKind.Type',\n        'JoinSide.Type',\n        'List.Type',\n        'Logical.Type',\n        'MissingField.Type',\n        'None.Type',\n        'Null.Type',\n        'Number.Type',\n        'Occurrence.Type',\n        'Order.Type',\n        'Password.Type',\n        'Percentage.Type',\n        'Precision.Type',\n        'QuoteStyle.Type',\n        'Record.Type',\n        'RelativePosition.Type',\n        'RoundingMode.Type',\n        'SapHanaDistribution.Type',\n        'SapHanaRangeOperator.Type',\n        'Single.Type',\n        'Table.Type',\n        'Text.Type',\n        'TextEncoding.Type',\n        'Time.Type',\n        'TraceLevel.Type',\n        'Type.Type',\n        'Uri.Type',\n        'WebMethod.Type'\n    ],\n    tokenizer: {\n        root: [\n            // quoted identifier\n            [/#\"[\\w \\.]+\"/, 'identifier.quote'],\n            // numbers\n            [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, 'number.float'],\n            [/0[xX][0-9a-fA-F]+/, 'number.hex'],\n            [/\\d+([eE][\\-+]?\\d+)?/, 'number'],\n            // keywords\n            [\n                /(#?[a-z]+)\\b/,\n                {\n                    cases: {\n                        '@typeKeywords': 'type',\n                        '@keywords': 'keyword',\n                        '@constants': 'constant',\n                        '@constructors': 'constructor',\n                        '@operatorKeywords': 'operators',\n                        '@default': 'identifier'\n                    }\n                }\n            ],\n            // built-in types\n            [\n                /\\b([A-Z][a-zA-Z0-9]+\\.Type)\\b/,\n                {\n                    cases: {\n                        '@builtinTypes': 'type',\n                        '@default': 'identifier'\n                    }\n                }\n            ],\n            // other built-ins\n            [\n                /\\b([A-Z][a-zA-Z0-9]+\\.[A-Z][a-zA-Z0-9]+)\\b/,\n                {\n                    cases: {\n                        '@builtinFunctions': 'keyword.function',\n                        '@builtinConstants': 'constant',\n                        '@default': 'identifier'\n                    }\n                }\n            ],\n            // other identifiers\n            [/\\b([a-zA-Z_][\\w\\.]*)\\b/, 'identifier'],\n            { include: '@whitespace' },\n            { include: '@comments' },\n            { include: '@strings' },\n            [/[{}()\\[\\]]/, '@brackets'],\n            [/([=\\+<>\\-\\*&@\\?\\/!])|([<>]=)|(<>)|(=>)|(\\.\\.\\.)|(\\.\\.)/, 'operators'],\n            [/[,;]/, 'delimiter']\n        ],\n        whitespace: [[/\\s+/, 'white']],\n        comments: [\n            ['\\\\/\\\\*', 'comment', '@comment'],\n            ['\\\\/\\\\/+.*', 'comment']\n        ],\n        comment: [\n            ['\\\\*\\\\/', 'comment', '@pop'],\n            ['.', 'comment']\n        ],\n        strings: [['\"', 'string', '@string']],\n        string: [\n            ['\"\"', 'string.escape'],\n            ['\"', 'string', '@pop'],\n            ['.', 'string']\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC7B;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,WAAW,YAAY,EAAE;AAAA,IACpE,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,WAAW,YAAY,EAAE;AAAA,IACpE,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,WAAW,YAAY,EAAE;AAAA,IACpE,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,WAAW,YAAY,EAAE;AAAA,EACxE;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,UAAU;AAAA,IACN,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,qBAAqB;AAAA,IACrD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,EAC5D;AAAA,EACA,kBAAkB,CAAC,OAAO,OAAO,IAAI;AAAA,EACrC,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,cAAc,CAAC,WAAW,SAAS,aAAa,iBAAiB,aAAa,UAAU,OAAO;AAAA,EAC/F,WAAW,CAAC,aAAa,QAAQ,aAAa,SAAS;AAAA,EACvD,cAAc;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,kBAAkB;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,kBAAkB;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,cAAc;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,WAAW;AAAA,IACP,MAAM;AAAA;AAAA,MAEF,CAAC,eAAe,kBAAkB;AAAA;AAAA,MAElC,CAAC,4BAA4B,cAAc;AAAA,MAC3C,CAAC,qBAAqB,YAAY;AAAA,MAClC,CAAC,uBAAuB,QAAQ;AAAA;AAAA,MAEhC;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,iBAAiB;AAAA,YACjB,aAAa;AAAA,YACb,cAAc;AAAA,YACd,iBAAiB;AAAA,YACjB,qBAAqB;AAAA,YACrB,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,iBAAiB;AAAA,YACjB,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,qBAAqB;AAAA,YACrB,qBAAqB;AAAA,YACrB,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,CAAC,0BAA0B,YAAY;AAAA,MACvC,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,YAAY;AAAA,MACvB,EAAE,SAAS,WAAW;AAAA,MACtB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,0DAA0D,WAAW;AAAA,MACtE,CAAC,QAAQ,WAAW;AAAA,IACxB;AAAA,IACA,YAAY,CAAC,CAAC,OAAO,OAAO,CAAC;AAAA,IAC7B,UAAU;AAAA,MACN,CAAC,UAAU,WAAW,UAAU;AAAA,MAChC,CAAC,aAAa,SAAS;AAAA,IAC3B;AAAA,IACA,SAAS;AAAA,MACL,CAAC,UAAU,WAAW,MAAM;AAAA,MAC5B,CAAC,KAAK,SAAS;AAAA,IACnB;AAAA,IACA,SAAS,CAAC,CAAC,KAAK,UAAU,SAAS,CAAC;AAAA,IACpC,QAAQ;AAAA,MACJ,CAAC,MAAM,eAAe;AAAA,MACtB,CAAC,KAAK,UAAU,MAAM;AAAA,MACtB,CAAC,KAAK,QAAQ;AAAA,IAClB;AAAA,EACJ;AACJ;", "names": []}