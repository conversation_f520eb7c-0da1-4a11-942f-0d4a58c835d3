import {
  supportStatic
} from "./chunk-JHAOQP73.js";
import {
  Checkbox$1
} from "./chunk-GFI6XVUE.js";
import "./chunk-ZRTRFIWU.js";
import "./chunk-6C4OBBQZ.js";
import "./chunk-M5OFQAQB.js";
import {
  Icon,
  Spinner$1
} from "./chunk-YPPVVTGH.js";
import {
  __assign,
  __decorate,
  __extends,
  __metadata
} from "./chunk-F25BIIHK.js";
import {
  CustomStyle,
  OptionsControl,
  autobind,
  columnsSplit,
  createObject,
  flattenTreeWithLeafNodes,
  formateCheckThemeCss,
  getVariable,
  hasAbility,
  require_debounce,
  setThemeClassName
} from "./chunk-LZQZ2OHM.js";
import "./chunk-QMS2VRR4.js";
import {
  require_toFinite,
  require_toNumber
} from "./chunk-5QW7M2DY.js";
import "./chunk-ZJNJ4ZV3.js";
import "./chunk-KEABM62F.js";
import "./chunk-CNJUQDSN.js";
import "./chunk-YZFIV7S4.js";
import "./chunk-7XBFYOTW.js";
import {
  require_react
} from "./chunk-ZUWHLQVA.js";
import "./chunk-KNGT5NUO.js";
import "./chunk-X3JSFYRQ.js";
import {
  __commonJS,
  __toESM
} from "./chunk-GFT2G5UO.js";

// node_modules/lodash/_baseInRange.js
var require_baseInRange = __commonJS({
  "node_modules/lodash/_baseInRange.js"(exports, module) {
    var nativeMax = Math.max;
    var nativeMin = Math.min;
    function baseInRange(number, start, end) {
      return number >= nativeMin(start, end) && number < nativeMax(start, end);
    }
    module.exports = baseInRange;
  }
});

// node_modules/lodash/inRange.js
var require_inRange = __commonJS({
  "node_modules/lodash/inRange.js"(exports, module) {
    var baseInRange = require_baseInRange();
    var toFinite = require_toFinite();
    var toNumber = require_toNumber();
    function inRange2(number, start, end) {
      start = toFinite(start);
      if (end === void 0) {
        end = start;
        start = 0;
      } else {
        end = toFinite(end);
      }
      number = toNumber(number);
      return baseInRange(number, start, end);
    }
    module.exports = inRange2;
  }
});

// node_modules/amis/esm/renderers/Form/Checkboxes.js
var import_react = __toESM(require_react());
var import_inRange = __toESM(require_inRange());
var import_debounce = __toESM(require_debounce());
var CheckboxesControl = (
  /** @class */
  function(_super) {
    __extends(CheckboxesControl2, _super);
    function CheckboxesControl2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.checkboxRef = import_react.default.createRef();
      _this.childRefs = [];
      return _this;
    }
    CheckboxesControl2.prototype.doAction = function(action, data, throwErrors) {
      var _a, _b;
      var _c = this.props, resetValue = _c.resetValue, onChange = _c.onChange, formStore = _c.formStore, store = _c.store, name = _c.name;
      var actionType = action === null || action === void 0 ? void 0 : action.actionType;
      if (actionType === "clear") {
        onChange("");
      } else if (actionType === "reset") {
        var pristineVal = (_b = getVariable((_a = formStore === null || formStore === void 0 ? void 0 : formStore.pristine) !== null && _a !== void 0 ? _a : store === null || store === void 0 ? void 0 : store.pristine, name)) !== null && _b !== void 0 ? _b : resetValue;
        onChange(pristineVal !== null && pristineVal !== void 0 ? pristineVal : "");
      }
    };
    CheckboxesControl2.prototype.reload = function(subpath, query) {
      var reload = this.props.reloadOptions;
      reload && reload(subpath, query);
    };
    CheckboxesControl2.prototype.handleAddClick = function() {
      var onAdd = this.props.onAdd;
      onAdd && onAdd();
    };
    CheckboxesControl2.prototype.handleEditClick = function(e, item) {
      var onEdit = this.props.onEdit;
      e.preventDefault();
      e.stopPropagation();
      onEdit && onEdit(item);
    };
    CheckboxesControl2.prototype.handleDeleteClick = function(e, item) {
      var onDelete = this.props.onDelete;
      e.preventDefault();
      e.stopPropagation();
      onDelete && onDelete(item);
    };
    CheckboxesControl2.prototype.componentDidMount = function() {
      this.updateBorderStyle();
      var updateBorderStyleFn = (0, import_debounce.default)(this.updateBorderStyle, 100);
      if (this.checkboxRef.current) {
        this.checkboxRefObserver = new ResizeObserver(updateBorderStyleFn);
        this.checkboxRefObserver.observe(this.checkboxRef.current);
      }
    };
    CheckboxesControl2.prototype.componentWillUnmount = function() {
      var _a;
      (_a = this.checkboxRefObserver) === null || _a === void 0 ? void 0 : _a.disconnect();
    };
    CheckboxesControl2.prototype.updateBorderStyle = function() {
      if (this.props.optionType !== "button") {
        return;
      }
      if (!this.childRefs.length) {
        return;
      }
      var children = this.childRefs.map(function(item2) {
        return item2 === null || item2 === void 0 ? void 0 : item2.ref;
      });
      var lastOffsetTop = children[0].labelRef.current.offsetTop;
      var options = [];
      var len = children.length;
      options[0] = "first";
      var lastLineStart = 0;
      for (var i = 1; i < len; i++) {
        var item = children[i];
        var currentOffsetTop = item.labelRef.current.offsetTop;
        options[i] = "";
        if (currentOffsetTop !== lastOffsetTop) {
          options[i] = "first";
          options[i - 1] += " last";
          lastLineStart = i;
          lastOffsetTop = currentOffsetTop;
        }
      }
      options[len - 1] += " last";
      options.forEach(function(option, index) {
        if (index >= lastLineStart) {
          option += " last-line";
        }
        children[index].setClassName(option);
      });
    };
    CheckboxesControl2.prototype.renderGroup = function(option, index) {
      var _this = this;
      var _a;
      var _b = this.props, cx = _b.classnames, labelField = _b.labelField;
      if (!((_a = option.children) === null || _a === void 0 ? void 0 : _a.length)) {
        return null;
      }
      var children = option.children.map(function(option2, index2) {
        return _this.renderItem(option2, index2);
      });
      var body = this.columnsSplit(children);
      return import_react.default.createElement(
        "div",
        { key: "group-" + index, className: cx("CheckboxesControl-group", option.className) },
        import_react.default.createElement("label", { className: cx("CheckboxesControl-groupLabel", option.labelClassName) }, option[labelField || "label"]),
        body
      );
    };
    CheckboxesControl2.prototype.addChildRefs = function(el) {
      el && this.childRefs.push(el);
    };
    CheckboxesControl2.prototype.renderItem = function(option, index) {
      var _this = this;
      var _a;
      if ((_a = option.children) === null || _a === void 0 ? void 0 : _a.length) {
        return this.renderGroup(option, index);
      }
      var _b = this.props, render = _b.render, itemClassName = _b.itemClassName, onToggle = _b.onToggle, selectedOptions = _b.selectedOptions, disabled = _b.disabled, inline = _b.inline, labelClassName = _b.labelClassName, labelField = _b.labelField, removable = _b.removable, editable = _b.editable, __ = _b.translate, optionType = _b.optionType, menuTpl = _b.menuTpl, data = _b.data, testIdBuilder = _b.testIdBuilder;
      var labelText = String(option[labelField || "label"]);
      var optionLabelClassName = option["labelClassName"];
      var itemTestIdBuilder = testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild("item-" + labelText || index);
      return import_react.default.createElement(
        Checkbox$1,
        { className: itemClassName, key: index, onChange: function() {
          return onToggle(option);
        }, checked: !!~selectedOptions.indexOf(option), disabled: disabled || option.disabled, inline, labelClassName: optionLabelClassName || labelClassName, description: option.description, optionType, testIdBuilder: itemTestIdBuilder, ref: this.addChildRefs },
        menuTpl ? render("checkboxes/".concat(index), menuTpl, {
          data: createObject(data, option)
        }) : labelText,
        removable && hasAbility(option, "removable") ? import_react.default.createElement(
          "a",
          { "data-tooltip": __("Select.clear"), "data-position": "left" },
          import_react.default.createElement(Icon, { icon: "minus", className: "icon", onClick: function(e) {
            return _this.handleDeleteClick(e, option);
          } })
        ) : null,
        editable && hasAbility(option, "editable") ? import_react.default.createElement(
          "a",
          { "data-tooltip": "编辑", "data-position": "left" },
          import_react.default.createElement(Icon, { icon: "pencil", className: "icon", onClick: function(e) {
            return _this.handleEditClick(e, option);
          } })
        ) : null
      );
    };
    CheckboxesControl2.prototype.columnsSplit = function(body) {
      var _a = this.props, columnsCount = _a.columnsCount, cx = _a.classnames;
      var result = [];
      var tmp = [];
      body.forEach(function(node) {
        if (node && node.key && String(node.key).startsWith("group")) {
          if (tmp.length) {
            result.push(columnsSplit(tmp, cx, columnsCount));
            tmp = [];
          }
          result.push(node);
        } else {
          tmp.push(node);
        }
      });
      tmp.length && result.push(columnsSplit(tmp, cx, columnsCount));
      return result;
    };
    CheckboxesControl2.prototype.render = function() {
      var _this = this;
      var _a = this.props, className = _a.className, style = _a.style, disabled = _a.disabled, placeholder = _a.placeholder, options = _a.options, inline = _a.inline, columnsCount = _a.columnsCount, selectedOptions = _a.selectedOptions, onToggle = _a.onToggle, onToggleAll = _a.onToggleAll, checkAll = _a.checkAll, checkAllText = _a.checkAllText, cx = _a.classnames, itemClassName = _a.itemClassName, labelClassName = _a.labelClassName, creatable = _a.creatable, addApi = _a.addApi, createBtnLabel = _a.createBtnLabel, __ = _a.translate, optionType = _a.optionType, loading = _a.loading, loadingConfig = _a.loadingConfig, themeCss = _a.themeCss, id = _a.id, env = _a.env, ns = _a.classPrefix;
      var body = [];
      if (options && options.length) {
        body = options.map(function(option, key) {
          return _this.renderItem(option, key);
        });
      }
      if (checkAll && body.length && optionType === "default") {
        body.unshift(import_react.default.createElement(Checkbox$1, { key: "checkall", className: itemClassName, onChange: onToggleAll, checked: !!selectedOptions.length, partial: (0, import_inRange.default)(selectedOptions.length, 0, flattenTreeWithLeafNodes(options).length), disabled, inline, labelClassName }, checkAllText !== null && checkAllText !== void 0 ? checkAllText : __("Checkboxes.selectAll")));
      }
      body = this.columnsSplit(body);
      var css = formateCheckThemeCss(themeCss, "checkboxes");
      return import_react.default.createElement(
        "div",
        { className: cx("CheckboxesControl", className, setThemeClassName(__assign(__assign({}, this.props), { name: [
          "checkboxesControlClassName",
          "checkboxesControlCheckedClassName",
          "checkboxesClassName",
          "checkboxesCheckedClassName",
          "checkboxesInnerClassName",
          "checkboxesShowClassName"
        ], id, themeCss: css }))), ref: this.checkboxRef },
        body && body.length ? body : loading ? null : import_react.default.createElement("span", { className: "Form-placeholder" }, __(placeholder)),
        loading ? import_react.default.createElement(Spinner$1, { show: true, icon: "reload", size: "sm", spinnerClassName: cx("Checkboxes-spinner"), loadingConfig }) : null,
        (creatable || addApi) && !disabled ? import_react.default.createElement(
          "a",
          { className: cx("Checkboxes-addBtn"), onClick: this.handleAddClick },
          import_react.default.createElement(Icon, { icon: "plus", className: "icon" }),
          __(createBtnLabel)
        ) : null,
        import_react.default.createElement(CustomStyle, __assign({}, this.props, { config: {
          themeCss: css,
          classNames: [
            {
              key: "checkboxesControlClassName",
              weights: {
                default: {
                  inner: ".".concat(ns, "Checkbox:not(.checked):not(.disabled)")
                },
                hover: {
                  suf: " .".concat(ns, "Checkbox:not(.disabled):not(.checked)")
                },
                disabled: {
                  inner: ".".concat(ns, "Checkbox.disabled:not(.checked)")
                }
              }
            },
            {
              key: "checkboxesControlCheckedClassName",
              weights: {
                default: {
                  inner: ".".concat(ns, "Checkbox.checked:not(.disabled)")
                },
                hover: {
                  suf: " .".concat(ns, "Checkbox.checked:not(.disabled)")
                },
                disabled: {
                  inner: ".".concat(ns, "Checkbox.checked.disabled")
                }
              }
            },
            {
              key: "checkboxesClassName",
              weights: {
                default: {
                  inner: ".".concat(ns, "Checkbox:not(.checked):not(.disabled) > i")
                },
                hover: {
                  suf: " .".concat(ns, "Checkbox:not(.disabled):not(.checked)"),
                  inner: "> i"
                },
                disabled: {
                  inner: ".".concat(ns, "Checkbox.disabled:not(.checked) > i")
                }
              }
            },
            {
              key: "checkboxesCheckedClassName",
              weights: {
                default: {
                  inner: ".".concat(ns, "Checkbox:not(.disabled) > i")
                },
                hover: {
                  suf: " .".concat(ns, "Checkbox:not(.disabled)"),
                  inner: "> i"
                },
                disabled: {
                  inner: ".".concat(ns, "Checkbox.disabled > i")
                }
              }
            },
            {
              key: "checkboxesInnerClassName",
              weights: {
                default: {
                  inner: ".".concat(ns, "Checkbox:not(.disabled) > i .icon")
                },
                hover: {
                  suf: " .".concat(ns, "Checkbox:not(.disabled)"),
                  inner: "> i .icon"
                },
                disabled: {
                  inner: ".".concat(ns, "Checkbox.disabled > i .icon")
                }
              }
            },
            {
              key: "checkboxesShowClassName",
              weights: {
                default: {
                  inner: ".".concat(ns, "Checkbox > i")
                }
              }
            }
          ],
          id
        }, env }))
      );
    };
    CheckboxesControl2.defaultProps = {
      columnsCount: 1,
      multiple: true,
      placeholder: "placeholder.noOption",
      creatable: false,
      inline: true,
      createBtnLabel: "Select.createLabel",
      optionType: "default"
    };
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", void 0)
    ], CheckboxesControl2.prototype, "handleAddClick", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Event, Object]),
      __metadata("design:returntype", void 0)
    ], CheckboxesControl2.prototype, "handleEditClick", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Event, Object]),
      __metadata("design:returntype", void 0)
    ], CheckboxesControl2.prototype, "handleDeleteClick", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", void 0)
    ], CheckboxesControl2.prototype, "updateBorderStyle", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], CheckboxesControl2.prototype, "addChildRefs", null);
    __decorate([
      supportStatic(),
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", void 0)
    ], CheckboxesControl2.prototype, "render", null);
    return CheckboxesControl2;
  }(import_react.default.Component)
);
var CheckboxesControlRenderer = (
  /** @class */
  function(_super) {
    __extends(CheckboxesControlRenderer2, _super);
    function CheckboxesControlRenderer2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    CheckboxesControlRenderer2 = __decorate([
      OptionsControl({
        type: "checkboxes",
        sizeMutable: false,
        thin: true
      })
    ], CheckboxesControlRenderer2);
    return CheckboxesControlRenderer2;
  }(CheckboxesControl)
);
export {
  CheckboxesControlRenderer,
  CheckboxesControl as default
};
//# sourceMappingURL=Checkboxes-WSCZXL46.js.map
