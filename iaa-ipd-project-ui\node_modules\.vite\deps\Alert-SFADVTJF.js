import {
  Alert2
} from "./chunk-GFI6XVUE.js";
import "./chunk-ZRTRFIWU.js";
import "./chunk-6C4OBBQZ.js";
import "./chunk-M5OFQAQB.js";
import "./chunk-YPPVVTGH.js";
import {
  __assign,
  __decorate,
  __extends,
  __rest
} from "./chunk-F25BIIHK.js";
import {
  Renderer,
  isPureVariable,
  resolveVariableAndFilter
} from "./chunk-LZQZ2OHM.js";
import "./chunk-QMS2VRR4.js";
import "./chunk-5QW7M2DY.js";
import "./chunk-ZJNJ4ZV3.js";
import "./chunk-KEABM62F.js";
import "./chunk-CNJUQDSN.js";
import "./chunk-YZFIV7S4.js";
import "./chunk-7XBFYOTW.js";
import {
  require_react
} from "./chunk-ZUWHLQVA.js";
import "./chunk-KNGT5NUO.js";
import "./chunk-X3JSFYRQ.js";
import {
  __toESM
} from "./chunk-GFT2G5UO.js";

// node_modules/amis/esm/renderers/Alert.js
var import_react = __toESM(require_react());
var AlertRenderer = (
  /** @class */
  function(_super) {
    __extends(AlertRenderer2, _super);
    function AlertRenderer2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    AlertRenderer2.prototype.render = function() {
      var _a = this.props, render = _a.render, body = _a.body, level = _a.level, icon = _a.icon, showIcon = _a.showIcon, actions = _a.actions, rest = __rest(_a, ["render", "body", "level", "icon", "showIcon", "actions"]);
      if (isPureVariable(level)) {
        level = resolveVariableAndFilter(level, this.props.data);
      }
      if (isPureVariable(icon)) {
        icon = resolveVariableAndFilter(icon, this.props.data);
      }
      if (isPureVariable(showIcon)) {
        showIcon = resolveVariableAndFilter(showIcon, this.props.data);
      }
      var actionsDom = actions ? import_react.default.isValidElement(actions) ? actions : render("alert-actions", actions) : null;
      return import_react.default.createElement(Alert2, __assign({}, rest, { level, icon, showIcon, actions: actionsDom }), render("body", body));
    };
    AlertRenderer2 = __decorate([
      Renderer({
        type: "alert"
      })
    ], AlertRenderer2);
    return AlertRenderer2;
  }(import_react.default.Component)
);
export {
  AlertRenderer
};
//# sourceMappingURL=Alert-SFADVTJF.js.map
