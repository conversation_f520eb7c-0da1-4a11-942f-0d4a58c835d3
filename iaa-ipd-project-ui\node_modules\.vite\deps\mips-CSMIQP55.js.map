{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/mips/mips.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#%\\^\\&\\*\\(\\)\\=\\$\\-\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n    comments: {\n        blockComment: ['###', '###'],\n        lineComment: '#'\n    },\n    folding: {\n        markers: {\n            start: new RegExp('^\\\\s*#region\\\\b'),\n            end: new RegExp('^\\\\s*#endregion\\\\b')\n        }\n    }\n};\nexport var language = {\n    defaultToken: '',\n    ignoreCase: false,\n    tokenPostfix: '.mips',\n    regEx: /\\/(?!\\/\\/)(?:[^\\/\\\\]|\\\\.)*\\/[igm]*/,\n    keywords: [\n        '.data',\n        '.text',\n        'syscall',\n        'trap',\n        'add',\n        'addu',\n        'addi',\n        'addiu',\n        'and',\n        'andi',\n        'div',\n        'divu',\n        'mult',\n        'multu',\n        'nor',\n        'or',\n        'ori',\n        'sll',\n        'slv',\n        'sra',\n        'srav',\n        'srl',\n        'srlv',\n        'sub',\n        'subu',\n        'xor',\n        'xori',\n        'lhi',\n        'lho',\n        'lhi',\n        'llo',\n        'slt',\n        'slti',\n        'sltu',\n        'sltiu',\n        'beq',\n        'bgtz',\n        'blez',\n        'bne',\n        'j',\n        'jal',\n        'jalr',\n        'jr',\n        'lb',\n        'lbu',\n        'lh',\n        'lhu',\n        'lw',\n        'li',\n        'la',\n        'sb',\n        'sh',\n        'sw',\n        'mfhi',\n        'mflo',\n        'mthi',\n        'mtlo',\n        'move'\n    ],\n    // we include these common regular expressions\n    symbols: /[\\.,\\:]+/,\n    escapes: /\\\\(?:[abfnrtv\\\\\"'$]|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n    // The main tokenizer for our languages\n    tokenizer: {\n        root: [\n            // identifiers and keywords\n            [/\\$[a-zA-Z_]\\w*/, 'variable.predefined'],\n            [\n                /[.a-zA-Z_]\\w*/,\n                {\n                    cases: {\n                        this: 'variable.predefined',\n                        '@keywords': { token: 'keyword.$0' },\n                        '@default': ''\n                    }\n                }\n            ],\n            // whitespace\n            [/[ \\t\\r\\n]+/, ''],\n            // Comments\n            [/#.*$/, 'comment'],\n            // regular expressions\n            ['///', { token: 'regexp', next: '@hereregexp' }],\n            [/^(\\s*)(@regEx)/, ['', 'regexp']],\n            [/(\\,)(\\s*)(@regEx)/, ['delimiter', '', 'regexp']],\n            [/(\\:)(\\s*)(@regEx)/, ['delimiter', '', 'regexp']],\n            // delimiters\n            [/@symbols/, 'delimiter'],\n            // numbers\n            [/\\d+[eE]([\\-+]?\\d+)?/, 'number.float'],\n            [/\\d+\\.\\d+([eE][\\-+]?\\d+)?/, 'number.float'],\n            [/0[xX][0-9a-fA-F]+/, 'number.hex'],\n            [/0[0-7]+(?!\\d)/, 'number.octal'],\n            [/\\d+/, 'number'],\n            // delimiter: after number because of .\\d floats\n            [/[,.]/, 'delimiter'],\n            // strings:\n            [/\"\"\"/, 'string', '@herestring.\"\"\"'],\n            [/'''/, 'string', \"@herestring.'''\"],\n            [\n                /\"/,\n                {\n                    cases: {\n                        '@eos': 'string',\n                        '@default': { token: 'string', next: '@string.\"' }\n                    }\n                }\n            ],\n            [\n                /'/,\n                {\n                    cases: {\n                        '@eos': 'string',\n                        '@default': { token: 'string', next: \"@string.'\" }\n                    }\n                }\n            ]\n        ],\n        string: [\n            [/[^\"'\\#\\\\]+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/\\./, 'string.escape.invalid'],\n            [/\\./, 'string.escape.invalid'],\n            [\n                /#{/,\n                {\n                    cases: {\n                        '$S2==\"': {\n                            token: 'string',\n                            next: 'root.interpolatedstring'\n                        },\n                        '@default': 'string'\n                    }\n                }\n            ],\n            [\n                /[\"']/,\n                {\n                    cases: {\n                        '$#==$S2': { token: 'string', next: '@pop' },\n                        '@default': 'string'\n                    }\n                }\n            ],\n            [/#/, 'string']\n        ],\n        herestring: [\n            [\n                /(\"\"\"|''')/,\n                {\n                    cases: {\n                        '$1==$S2': { token: 'string', next: '@pop' },\n                        '@default': 'string'\n                    }\n                }\n            ],\n            [/[^#\\\\'\"]+/, 'string'],\n            [/['\"]+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/\\./, 'string.escape.invalid'],\n            [/#{/, { token: 'string.quote', next: 'root.interpolatedstring' }],\n            [/#/, 'string']\n        ],\n        comment: [\n            [/[^#]+/, 'comment'],\n            [/#/, 'comment']\n        ],\n        hereregexp: [\n            [/[^\\\\\\/#]+/, 'regexp'],\n            [/\\\\./, 'regexp'],\n            [/#.*$/, 'comment'],\n            ['///[igm]*', { token: 'regexp', next: '@pop' }],\n            [/\\//, 'regexp']\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,aAAa;AAAA,EACb,UAAU;AAAA,IACN,cAAc,CAAC,OAAO,KAAK;AAAA,IAC3B,aAAa;AAAA,EACjB;AAAA,EACA,SAAS;AAAA,IACL,SAAS;AAAA,MACL,OAAO,IAAI,OAAO,iBAAiB;AAAA,MACnC,KAAK,IAAI,OAAO,oBAAoB;AAAA,IACxC;AAAA,EACJ;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,OAAO;AAAA,EACP,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA;AAAA,EAEA,SAAS;AAAA,EACT,SAAS;AAAA;AAAA,EAET,WAAW;AAAA,IACP,MAAM;AAAA;AAAA,MAEF,CAAC,kBAAkB,qBAAqB;AAAA,MACxC;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,MAAM;AAAA,YACN,aAAa,EAAE,OAAO,aAAa;AAAA,YACnC,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,CAAC,cAAc,EAAE;AAAA;AAAA,MAEjB,CAAC,QAAQ,SAAS;AAAA;AAAA,MAElB,CAAC,OAAO,EAAE,OAAO,UAAU,MAAM,cAAc,CAAC;AAAA,MAChD,CAAC,kBAAkB,CAAC,IAAI,QAAQ,CAAC;AAAA,MACjC,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA,MACjD,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA;AAAA,MAEjD,CAAC,YAAY,WAAW;AAAA;AAAA,MAExB,CAAC,uBAAuB,cAAc;AAAA,MACtC,CAAC,4BAA4B,cAAc;AAAA,MAC3C,CAAC,qBAAqB,YAAY;AAAA,MAClC,CAAC,iBAAiB,cAAc;AAAA,MAChC,CAAC,OAAO,QAAQ;AAAA;AAAA,MAEhB,CAAC,QAAQ,WAAW;AAAA;AAAA,MAEpB,CAAC,OAAO,UAAU,iBAAiB;AAAA,MACnC,CAAC,OAAO,UAAU,iBAAiB;AAAA,MACnC;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ;AAAA,YACR,YAAY,EAAE,OAAO,UAAU,MAAM,YAAY;AAAA,UACrD;AAAA,QACJ;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ;AAAA,YACR,YAAY,EAAE,OAAO,UAAU,MAAM,YAAY;AAAA,UACrD;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,MACJ,CAAC,cAAc,QAAQ;AAAA,MACvB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,MAAM,uBAAuB;AAAA,MAC9B,CAAC,MAAM,uBAAuB;AAAA,MAC9B;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,UAAU;AAAA,cACN,OAAO;AAAA,cACP,MAAM;AAAA,YACV;AAAA,YACA,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,WAAW,EAAE,OAAO,UAAU,MAAM,OAAO;AAAA,YAC3C,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,CAAC,KAAK,QAAQ;AAAA,IAClB;AAAA,IACA,YAAY;AAAA,MACR;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,WAAW,EAAE,OAAO,UAAU,MAAM,OAAO;AAAA,YAC3C,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,CAAC,aAAa,QAAQ;AAAA,MACtB,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,MAAM,uBAAuB;AAAA,MAC9B,CAAC,MAAM,EAAE,OAAO,gBAAgB,MAAM,0BAA0B,CAAC;AAAA,MACjE,CAAC,KAAK,QAAQ;AAAA,IAClB;AAAA,IACA,SAAS;AAAA,MACL,CAAC,SAAS,SAAS;AAAA,MACnB,CAAC,KAAK,SAAS;AAAA,IACnB;AAAA,IACA,YAAY;AAAA,MACR,CAAC,aAAa,QAAQ;AAAA,MACtB,CAAC,OAAO,QAAQ;AAAA,MAChB,CAAC,QAAQ,SAAS;AAAA,MAClB,CAAC,aAAa,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,MAC/C,CAAC,MAAM,QAAQ;AAAA,IACnB;AAAA,EACJ;AACJ;", "names": []}