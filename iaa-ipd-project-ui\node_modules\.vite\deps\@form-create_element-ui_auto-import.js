import "./chunk-5USEMFK4.js";
import "./chunk-2QVQYDQQ.js";
import "./chunk-KXGTSU4J.js";
import "./chunk-C46K6ZZX.js";
import "./chunk-KHPQTWST.js";
import "./chunk-4CY3VCXQ.js";
import "./chunk-CDTXYLUI.js";
import "./chunk-V66BOG3M.js";
import "./chunk-NRORYYUK.js";
import "./chunk-UFUFIGYI.js";
import "./chunk-E3DLDBED.js";
import "./chunk-UOUPA56J.js";
import "./chunk-WPEEW5RJ.js";
import "./chunk-O7CZYL2V.js";
import "./chunk-EH4EBJTH.js";
import "./chunk-LYMETERA.js";
import "./chunk-OUC6N52S.js";
import "./chunk-VLXJSUZQ.js";
import "./chunk-IMXV4MEY.js";
import "./chunk-YEZBMTJC.js";
import "./chunk-DGSUCHCR.js";
import "./chunk-I2NJJVUP.js";
import "./chunk-QGAZT4QQ.js";
import "./chunk-DZARXTWG.js";
import "./chunk-YVIH6KH3.js";
import "./chunk-HBOPRAUP.js";
import "./chunk-FQ4DDLB2.js";
import "./chunk-BWMIJGN7.js";
import "./chunk-WS6GU23O.js";
import "./chunk-5TJXHT6U.js";
import "./chunk-6WK4XNM2.js";
import "./chunk-N2ZUR6FP.js";
import "./chunk-V5ETOCBL.js";
import {
  ElAutocomplete,
  ElButton,
  ElCascader,
  ElCheckbox,
  ElCheckboxButton,
  ElCheckboxGroup,
  ElCol,
  ElColorPicker,
  ElDatePicker,
  ElDialog,
  ElForm,
  ElIcon,
  ElInput,
  ElInputNumber,
  ElPopover,
  ElProgress,
  ElRadio,
  ElRadioButton,
  ElRadioGroup,
  ElRate,
  ElRow,
  ElSelect,
  ElSlider,
  ElSwitch,
  ElTimePicker,
  ElTooltip,
  ElTree,
  ElUpload
} from "./chunk-DQNB3WI4.js";
import "./chunk-BLSZLSRI.js";
import "./chunk-KNGT5NUO.js";
import "./chunk-VCUJYIKY.js";
import "./chunk-XYY2WIPO.js";
import "./chunk-37KPRRXT.js";
import "./chunk-M4QTSFFX.js";
import "./chunk-F2WSVMX6.js";
import "./chunk-EROHHDF3.js";
import "./chunk-CIMZBJPB.js";
import "./chunk-P35NP2B7.js";
import "./chunk-GFT2G5UO.js";

// node_modules/@form-create/element-ui/auto-import.js
function install(formCreate) {
  formCreate.useApp((_, app) => {
    app.component(ElForm.name) || app.use(ElForm);
    app.component(ElButton.name) || app.use(ElButton);
    app.component(ElRow.name) || app.use(ElRow);
    app.component(ElCol.name) || app.use(ElCol);
    app.component(ElInput.name) || app.use(ElInput);
    app.component(ElInputNumber.name) || app.use(ElInputNumber);
    app.component(ElCascader.name) || app.use(ElCascader);
    app.component(ElPopover.name) || app.use(ElPopover);
    app.component(ElTooltip.name) || app.use(ElTooltip);
    app.component(ElAutocomplete.name) || app.use(ElAutocomplete);
    app.component(ElCheckboxGroup.name) || app.use(ElCheckboxGroup);
    app.component(ElCheckboxButton.name) || app.use(ElCheckboxButton);
    app.component(ElRadioGroup.name) || app.use(ElRadioGroup);
    app.component(ElRadioButton.name) || app.use(ElRadioButton);
    app.component(ElRadio.name) || app.use(ElRadio);
    app.component(ElDialog.name) || app.use(ElDialog);
    app.component(ElCheckbox.name) || app.use(ElCheckbox);
    app.component(ElSelect.name) || app.use(ElSelect);
    app.component(ElTree.name) || app.use(ElTree);
    app.component(ElUpload.name) || app.use(ElUpload);
    app.component(ElSlider.name) || app.use(ElSlider);
    app.component(ElRate.name) || app.use(ElRate);
    app.component(ElColorPicker.name) || app.use(ElColorPicker);
    app.component(ElSwitch.name) || app.use(ElSwitch);
    app.component(ElDatePicker.name) || app.use(ElDatePicker);
    app.component(ElIcon.name) || app.use(ElIcon);
    app.component(ElTimePicker.name) || app.use(ElTimePicker);
    app.component(ElProgress.name) || app.use(ElProgress);
  });
}
export {
  install as default
};
//# sourceMappingURL=@form-create_element-ui_auto-import.js.map
