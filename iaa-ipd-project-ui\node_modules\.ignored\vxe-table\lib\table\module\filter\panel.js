"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _vue = require("vue");
var _comp = require("../../../ui/src/comp");
var _ui = require("../../../ui");
var _utils = require("../../../ui/src/utils");
var _dom = require("../../../ui/src/dom");
var _vn = require("../../../ui/src/vn");
var _xeUtils = _interopRequireDefault(require("xe-utils"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
const {
  getI18n,
  getIcon,
  renderer
} = _ui.VxeUI;
var _default = exports.default = (0, _comp.defineVxeComponent)({
  name: 'VxeTableFilterPanel',
  props: {
    filterStore: Object
  },
  setup(props, context) {
    const xID = _xeUtils.default.uniqueId();
    const $xeTable = (0, _vue.inject)('$xeTable', {});
    const {
      reactData: tableReactData,
      internalData: tableInternalData,
      getComputeMaps
    } = $xeTable;
    const {
      computeFilterOpts
    } = getComputeMaps();
    const refElem = (0, _vue.ref)();
    const refMaps = {
      refElem
    };
    const $xeFilterPanel = {
      xID,
      props,
      context,
      getRefMaps: () => refMaps
    };
    const computeHasCheckOption = (0, _vue.computed)(() => {
      const {
        filterStore
      } = props;
      return filterStore && filterStore.options.some(option => option.checked);
    });
    // 全部筛选事件
    const filterCheckAllEvent = (evnt, value) => {
      const {
        filterStore
      } = props;
      filterStore.options.forEach(option => {
        option._checked = value;
        option.checked = value;
      });
      filterStore.isAllSelected = value;
      filterStore.isIndeterminate = false;
    };
    /*************************
     * Publish methods
     *************************/
    // 确认筛选
    const confirmFilter = evnt => {
      $xeTable.handleFilterConfirmFilter(evnt);
    };
    // （单选）筛选发生改变
    const changeRadioOption = (evnt, checked, item) => {
      $xeTable.handleFilterChangeRadioOption(evnt, checked, item);
    };
    /**
     * 重置筛选
     * 当筛选面板中的重置按钮被按下时触发
     * @param {Event} evnt 事件
     */
    const resetFilter = evnt => {
      $xeTable.handleFilterResetFilter(evnt);
    };
    // （多选）筛选发生改变
    const changeMultipleOption = (evnt, checked, item) => {
      $xeTable.handleFilterChangeMultipleOption(evnt, checked, item);
    };
    // 筛选发生改变
    const changeOption = (evnt, checked, item) => {
      $xeTable.handleFilterChangeOption(evnt, checked, item);
    };
    const changeAllOption = (evnt, checked) => {
      const {
        filterStore
      } = props;
      if (filterStore.multiple) {
        filterCheckAllEvent(evnt, checked);
      } else {
        resetFilter(evnt);
      }
    };
    /*************************
     * Publish methods
     *************************/
    const filterPanelMethods = {
      changeRadioOption,
      changeMultipleOption,
      changeAllOption,
      changeOption,
      confirmFilter,
      resetFilter
    };
    Object.assign($xeFilterPanel, filterPanelMethods);
    const renderOptions = (filterRender, compConf) => {
      const {
        filterStore
      } = props;
      const {
        column,
        multiple,
        maxHeight
      } = filterStore;
      const slots = column ? column.slots : null;
      const filterSlot = slots ? slots.filter : null;
      const params = Object.assign({}, tableInternalData._currFilterParams, {
        $panel: $xeFilterPanel,
        $table: $xeTable
      });
      const rtFilter = compConf ? compConf.renderTableFilter || compConf.renderFilter : null;
      if (filterSlot) {
        return [(0, _vue.h)('div', {
          class: 'vxe-table--filter-template',
          style: maxHeight ? {
            maxHeight: `${maxHeight}px`
          } : {}
        }, $xeTable.callSlot(filterSlot, params))];
      } else if (rtFilter) {
        return [(0, _vue.h)('div', {
          class: 'vxe-table--filter-template',
          style: maxHeight ? {
            maxHeight: `${maxHeight}px`
          } : {}
        }, (0, _vn.getSlotVNs)(rtFilter(filterRender, params)))];
      }
      const isAllChecked = multiple ? filterStore.isAllSelected : !filterStore.options.some(item => item._checked);
      const isAllIndeterminate = multiple && filterStore.isIndeterminate;
      return [(0, _vue.h)('ul', {
        class: 'vxe-table--filter-header'
      }, [(0, _vue.h)('li', {
        class: ['vxe-table--filter-option', {
          'is--checked': isAllChecked,
          'is--indeterminate': isAllIndeterminate
        }],
        title: getI18n(multiple ? 'vxe.table.allTitle' : 'vxe.table.allFilter'),
        onClick: evnt => {
          changeAllOption(evnt, !filterStore.isAllSelected);
        }
      }, (multiple ? [(0, _vue.h)('span', {
        class: ['vxe-checkbox--icon', isAllIndeterminate ? getIcon().TABLE_CHECKBOX_INDETERMINATE : isAllChecked ? getIcon().TABLE_CHECKBOX_CHECKED : getIcon().TABLE_CHECKBOX_UNCHECKED]
      })] : []).concat([(0, _vue.h)('span', {
        class: 'vxe-checkbox--label'
      }, getI18n('vxe.table.allFilter'))]))]), (0, _vue.h)('ul', {
        class: 'vxe-table--filter-body',
        style: maxHeight ? {
          maxHeight: `${maxHeight}px`
        } : {}
      }, filterStore.options.map(item => {
        const isChecked = item._checked;
        const isIndeterminate = false;
        return (0, _vue.h)('li', {
          class: ['vxe-table--filter-option', {
            'is--checked': item._checked
          }],
          title: item.label,
          onClick: evnt => {
            changeOption(evnt, !item._checked, item);
          }
        }, (multiple ? [(0, _vue.h)('span', {
          class: ['vxe-checkbox--icon', isIndeterminate ? getIcon().TABLE_CHECKBOX_INDETERMINATE : isChecked ? getIcon().TABLE_CHECKBOX_CHECKED : getIcon().TABLE_CHECKBOX_UNCHECKED]
        })] : []).concat([(0, _vue.h)('span', {
          class: 'vxe-checkbox--label'
        }, (0, _utils.formatText)(item.label, 1))]));
      }))];
    };
    const renderFooters = () => {
      const {
        filterStore
      } = props;
      const {
        column,
        multiple
      } = filterStore;
      const filterOpts = computeFilterOpts.value;
      const hasCheckOption = computeHasCheckOption.value;
      const {
        filterRender
      } = column;
      const compConf = (0, _utils.isEnableConf)(filterRender) ? renderer.get(filterRender.name) : null;
      const isDisabled = !hasCheckOption && !filterStore.isAllSelected && !filterStore.isIndeterminate;
      return multiple && (compConf ? !(compConf.showTableFilterFooter === false || compConf.showFilterFooter === false || compConf.isFooter === false) : true) ? [(0, _vue.h)('div', {
        class: 'vxe-table--filter-footer'
      }, [(0, _vue.h)('button', {
        class: {
          'is--disabled': isDisabled
        },
        disabled: isDisabled,
        onClick: confirmFilter
      }, filterOpts.confirmButtonText || getI18n('vxe.table.confirmFilter')), (0, _vue.h)('button', {
        onClick: resetFilter
      }, filterOpts.resetButtonText || getI18n('vxe.table.resetFilter'))])] : [];
    };
    const renderVN = () => {
      const {
        filterStore
      } = props;
      const {
        initStore
      } = tableReactData;
      const {
        visible,
        multiple,
        column
      } = filterStore;
      const filterRender = column ? column.filterRender : null;
      const compConf = (0, _utils.isEnableConf)(filterRender) ? renderer.get(filterRender.name) : null;
      const filterClassName = compConf ? compConf.tableFilterClassName || compConf.filterClassName : '';
      const params = Object.assign({}, tableInternalData._currFilterParams, {
        $panel: $xeFilterPanel,
        $table: $xeTable
      });
      const tableProps = $xeTable.props;
      const {
        computeSize
      } = $xeTable.getComputeMaps();
      const vSize = computeSize.value;
      const filterOpts = computeFilterOpts.value;
      const {
        transfer,
        destroyOnClose
      } = filterOpts;
      return (0, _vue.h)(_vue.Teleport, {
        to: 'body',
        disabled: !transfer
      }, [(0, _vue.h)('div', {
        ref: refElem,
        class: ['vxe-table--filter-wrapper', 'filter--prevent-default', (0, _dom.getPropClass)(filterClassName, params), {
          [`size--${vSize}`]: vSize,
          'is--animat': tableProps.animat,
          'is--multiple': multiple,
          'is--active': visible
        }],
        style: filterStore.style
      }, initStore.filter && (destroyOnClose ? visible : true) && column ? renderOptions(filterRender, compConf).concat(renderFooters()) : [])]);
    };
    $xeFilterPanel.renderVN = renderVN;
    return $xeFilterPanel;
  },
  render() {
    return this.renderVN();
  }
});