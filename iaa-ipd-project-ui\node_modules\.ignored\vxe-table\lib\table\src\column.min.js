Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=exports.columnProps=void 0;var _vue=require("vue"),_comp=require("../../ui/src/comp"),_util=require("./util"),_cell=_interopRequireDefault(require("./cell"));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let columnProps=exports.columnProps={colId:[String,Number],type:String,field:String,title:String,width:[Number,String],minWidth:[Number,String],maxWidth:[Number,String],resizable:{type:Boolean,default:null},fixed:String,align:String,headerAlign:String,footerAlign:String,showOverflow:{type:[Boolean,String],default:null},showHeaderOverflow:{type:[Boolean,String],default:null},showFooterOverflow:{type:[Boolean,String],default:null},className:[String,Function],headerClassName:[String,Function],footerClassName:[String,Function],formatter:[Function,Array,String],footerFormatter:[Function,Array,String],padding:{type:Boolean,default:null},verticalAlign:{type:String,default:null},sortable:Boolean,sortBy:[String,Function],sortType:String,filters:{type:Array,default:null},filterMultiple:{type:Boolean,default:!0},filterMethod:Function,filterResetMethod:Function,filterRecoverMethod:Function,filterRender:Object,rowGroupNode:Boolean,treeNode:Boolean,dragSort:Boolean,rowResize:Boolean,visible:{type:Boolean,default:null},headerExportMethod:Function,exportMethod:Function,footerExportMethod:Function,titleHelp:Object,titlePrefix:Object,titleSuffix:Object,cellType:String,cellRender:Object,editRender:Object,contentRender:Object,aggFunc:[String,Boolean],params:Object};var _default=exports.default=(0,_comp.defineVxeComponent)({name:"VxeColumn",props:columnProps,setup(e,{slots:t}){let o=(0,_vue.ref)(),r=(0,_vue.inject)("$xeTable",null),l=(0,_vue.inject)("$xeColgroup",null);if(!r)return()=>(0,_vue.createCommentVNode)();let n=_cell.default.createColumn(r,e);n.slots=t;var t=()=>(0,_vue.h)("div",{ref:o}),u={columnConfig:n,renderVN:t};return(0,_util.watchColumn)(r,e,n),(0,_vue.onMounted)(()=>{var e=o.value;e&&(0,_util.assembleColumn)(r,e,n,l)}),(0,_vue.onUnmounted)(()=>{(0,_util.destroyColumn)(r,n)}),(0,_vue.provide)("$xeColumn",u),(0,_vue.provide)("$xeGrid",null),t}});