.vxe-checkbox-group{display:inline-block;vertical-align:middle;line-height:1}.vxe-checkbox-slots{display:none}.vxe-checkbox--readonly{color:var(--vxe-ui-font-color);display:inline-flex}.vxe-checkbox{display:inline-block;vertical-align:middle;white-space:nowrap;line-height:1}.vxe-checkbox .vxe-checkbox--icon{font-size:1.22em}.vxe-checkbox .vxe-checkbox--icon{color:var(--vxe-ui-input-border-color);vertical-align:middle;font-weight:700;-webkit-user-select:none;-moz-user-select:none;user-select:none}.vxe-checkbox.is--checked,.vxe-checkbox.is--indeterminate{color:var(--vxe-ui-font-primary-color)}.vxe-checkbox.is--checked .vxe-checkbox--icon,.vxe-checkbox.is--indeterminate .vxe-checkbox--icon{color:var(--vxe-ui-font-primary-color)}.vxe-checkbox:not(.is--disabled){cursor:pointer}.vxe-checkbox:not(.is--disabled):hover .vxe-checkbox--icon{color:var(--vxe-ui-font-primary-color)}.vxe-checkbox.is--hidden{cursor:default}.vxe-checkbox.is--disabled{color:var(--vxe-ui-font-disabled-color);cursor:not-allowed}.vxe-checkbox.is--disabled .vxe-checkbox--icon{color:var(--vxe-ui-input-disabled-color)}.vxe-checkbox .vxe-checkbox--label{padding-left:.5em;vertical-align:middle}.vxe-checkbox+.vxe-checkbox{margin-left:10px}.vxe-checkbox>input[type=checkbox]{position:absolute;width:0;height:0;border:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.vxe-checkbox.is--indeterminate>input:not(:checked)+.vxe-checkbox--icon{color:var(--vxe-ui-font-primary-color)}.vxe-checkbox:not(.is--disabled)>input:focus+.vxe-checkbox--icon{color:var(--vxe-ui-font-primary-color);box-shadow:0 0 .2em 0 var(--vxe-ui-font-primary-color)}.vxe-checkbox:not(.is--disabled):hover>input+.vxe-checkbox--icon{border-color:var(--vxe-ui-font-primary-color)}.vxe-checkbox.is--disabled{cursor:not-allowed}.vxe-checkbox.is--disabled>input+.vxe-checkbox--icon{color:var(--vxe-ui-input-disabled-color)}.vxe-checkbox.is--disabled>input+.vxe-checkbox--icon+.vxe-checkbox--label{color:var(--vxe-ui-font-disabled-color)}.vxe-checkbox.is--disabled>input:checked+.vxe-checkbox--icon{color:var(--vxe-ui-input-disabled-color)}.vxe-checkbox .vxe-checkbox--label{padding-left:.5em;vertical-align:middle;display:inline-block;max-width:50em;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.vxe-checkbox{font-size:var(--vxe-ui-font-size-default)}.vxe-checkbox.size--medium{font-size:var(--vxe-ui-font-size-medium)}.vxe-checkbox.size--small{font-size:var(--vxe-ui-font-size-small)}.vxe-checkbox.size--mini{font-size:var(--vxe-ui-font-size-mini)}