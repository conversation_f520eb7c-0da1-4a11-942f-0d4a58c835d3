import XEUtils from 'xe-utils';
export function toStringTimeDate(str) {
    if (str) {
        const rest = new Date();
        let h = 0;
        let m = 0;
        let s = 0;
        if (XEUtils.isDate(str)) {
            h = str.getHours();
            m = str.getMinutes();
            s = str.getSeconds();
        }
        else {
            str = XEUtils.toValueString(str);
            const parses = str.match(/^(\d{1,2})(:(\d{1,2}))?(:(\d{1,2}))?/);
            if (parses) {
                h = XEUtils.toNumber(parses[1]);
                m = XEUtils.toNumber(parses[3]);
                s = XEUtils.toNumber(parses[5]);
            }
        }
        rest.setHours(h);
        rest.setMinutes(m);
        rest.setSeconds(s);
        return rest;
    }
    return new Date('');
}
export function getDateQuarter(date) {
    const month = date.getMonth();
    if (month < 3) {
        return 1;
    }
    else if (month < 6) {
        return 2;
    }
    else if (month < 9) {
        return 3;
    }
    return 4;
}
export const parseDateValue = (val, type, options) => {
    const { valueFormat } = options;
    if (val) {
        if (type === 'time') {
            return toStringTimeDate(val);
        }
        if (XEUtils.isNumber(val) || /^[0-9]{11,15}$/.test(`${val}`)) {
            return new Date(Number(val));
        }
        if (XEUtils.isString(val)) {
            return XEUtils.toStringDate(XEUtils.last(val.split(',')), valueFormat);
        }
        return XEUtils.toStringDate(val, valueFormat);
    }
    return null;
};
export const parseDateString = (val, type, options) => {
    const dValue = parseDateValue(val, type, options);
    return dValue ? XEUtils.toDateString(dValue, options.valueFormat) : '';
};
export function parseDateObj(val, type, options) {
    const { labelFormat, firstDay } = options;
    let dValue = null;
    let dLabel = '';
    if (val) {
        dValue = parseDateValue(val, type, options);
    }
    if (XEUtils.isValidDate(dValue)) {
        dLabel = XEUtils.toDateString(dValue, labelFormat, { firstDay });
        // 周选择器，由于年份和第几周是冲突的行为，所以需要特殊处理，判断是否跨年，例如
        // '2024-12-31' 'yyyy-MM-dd W' >> '2024-12-31 1'
        // '2025-01-01' 'yyyy-MM-dd W' >> '2025-01-01 1'
        if (labelFormat && type === 'week') {
            const weekNum = XEUtils.getYearWeek(dValue, firstDay);
            const weekDate = XEUtils.getWhatWeek(dValue, 0, weekNum === 1 ? ((6 + firstDay) % 7) : firstDay, firstDay);
            const weekFullYear = weekDate.getFullYear();
            if (weekFullYear !== dValue.getFullYear()) {
                const yyIndex = labelFormat.indexOf('yyyy');
                if (yyIndex > -1) {
                    const yyNum = Number(dLabel.substring(yyIndex, yyIndex + 4));
                    if (yyNum && !isNaN(yyNum)) {
                        dLabel = dLabel.replace(`${yyNum}`, `${weekFullYear}`);
                    }
                }
            }
        }
    }
    else {
        dValue = null;
    }
    return { label: dLabel, value: dValue };
}
export function getDateByCode(code, val, type, options) {
    const { valueFormat, firstDay } = options;
    let dValue = null;
    const value = (code === 'prev' || code === 'next' ? new Date() : (val ? parseDateValue(val, type, options) : null)) || new Date();
    switch (code) {
        case 'prev':
        case 'next':
        case 'minus':
        case 'plus': {
            const offsetNum = code === 'plus' || code === 'next' ? 1 : -1;
            switch (type) {
                case 'date':
                case 'datetime':
                    dValue = XEUtils.getWhatDay(value, offsetNum);
                    break;
                case 'week':
                    dValue = XEUtils.getWhatWeek(value, offsetNum, firstDay, firstDay);
                    break;
                case 'month':
                    dValue = XEUtils.getWhatMonth(value, offsetNum);
                    break;
                case 'quarter':
                    dValue = XEUtils.getWhatQuarter(value, offsetNum);
                    break;
                case 'year':
                    dValue = XEUtils.getWhatYear(value, offsetNum);
                    break;
            }
            break;
        }
        default:
            dValue = new Date();
            break;
    }
    return {
        value: dValue ? XEUtils.toDateString(dValue, valueFormat) : ''
    };
}
const rangeDateOffsetNumMaps = {
    last180: -180,
    last90: -90,
    last60: -60,
    last30: -30,
    last7: -7,
    last3: -3,
    last1: -1
};
function getRangeDateOffsetNum(code) {
    return rangeDateOffsetNumMaps[code] || 0;
}
export function getRangeDateByCode(code, val, type, options) {
    const { valueFormat, firstDay } = options;
    if (XEUtils.isArray(val)) {
        val = val.join('');
    }
    const value = (val ? parseDateValue(val, type, options) : null) || new Date();
    let sValue = null;
    const eValue = value;
    switch (code) {
        case 'last1':
        case 'last3':
        case 'last7':
        case 'last30':
        case 'last60':
        case 'last90':
        case 'last180': {
            const offsetNum = getRangeDateOffsetNum(code);
            switch (type) {
                case 'date':
                case 'datetime':
                    sValue = XEUtils.getWhatDay(value, offsetNum);
                    break;
                case 'week':
                    sValue = XEUtils.getWhatWeek(value, offsetNum, firstDay, firstDay);
                    break;
                case 'month':
                    sValue = XEUtils.getWhatMonth(value, offsetNum);
                    break;
                case 'quarter':
                    sValue = XEUtils.getWhatQuarter(value, offsetNum);
                    break;
                case 'year':
                    sValue = XEUtils.getWhatYear(value, offsetNum);
                    break;
            }
            break;
        }
        default:
            sValue = new Date();
            break;
    }
    const startValue = sValue ? XEUtils.toDateString(sValue, valueFormat) : '';
    const endValue = eValue ? XEUtils.toDateString(eValue, valueFormat) : '';
    return {
        startValue,
        endValue
    };
}
