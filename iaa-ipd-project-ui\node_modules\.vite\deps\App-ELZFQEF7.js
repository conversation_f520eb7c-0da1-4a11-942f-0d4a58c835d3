import {
  AsideNav$1,
  Layout$1,
  _404
} from "./chunk-GFI6XVUE.js";
import "./chunk-ZRTRFIWU.js";
import "./chunk-6C4OBBQZ.js";
import {
  Html$1
} from "./chunk-M5OFQAQB.js";
import {
  Icon,
  Spinner$1
} from "./chunk-YPPVVTGH.js";
import {
  __awaiter,
  __decorate,
  __extends,
  __generator,
  __metadata
} from "./chunk-F25BIIHK.js";
import {
  AppS<PERSON>,
  Renderer,
  ScopedContext,
  autobind,
  envOverwrite,
  filter,
  isApiOutdated,
  isEffectiveApi,
  replaceText
} from "./chunk-LZQZ2OHM.js";
import "./chunk-QMS2VRR4.js";
import "./chunk-5QW7M2DY.js";
import "./chunk-ZJNJ4ZV3.js";
import "./chunk-KEABM62F.js";
import "./chunk-CNJUQDSN.js";
import "./chunk-YZFIV7S4.js";
import "./chunk-7XBFYOTW.js";
import {
  require_react
} from "./chunk-ZUWHLQVA.js";
import "./chunk-KNGT5NUO.js";
import "./chunk-X3JSFYRQ.js";
import {
  __toESM
} from "./chunk-GFT2G5UO.js";

// node_modules/amis/esm/renderers/App.js
var import_react = __toESM(require_react());
var App = (
  /** @class */
  function(_super) {
    __extends(App2, _super);
    function App2(props) {
      var _this = this;
      var _a, _b, _c;
      _this = _super.call(this, props) || this;
      var store = props.store;
      store.syncProps(props, void 0, ["pages"]);
      store.updateActivePage(Object.assign({}, (_a = props.env) !== null && _a !== void 0 ? _a : {}, {
        showFullBreadcrumbPath: (_b = props.showFullBreadcrumbPath) !== null && _b !== void 0 ? _b : false,
        showBreadcrumbHomePath: (_c = props.showBreadcrumbHomePath) !== null && _c !== void 0 ? _c : true
      }));
      if (props.env.watchRouteChange) {
        _this.unWatchRouteChange = props.env.watchRouteChange(function() {
          var _a2, _b2, _c2;
          return store.updateActivePage(Object.assign({}, (_a2 = props.env) !== null && _a2 !== void 0 ? _a2 : {}, {
            showFullBreadcrumbPath: (_b2 = props.showFullBreadcrumbPath) !== null && _b2 !== void 0 ? _b2 : false,
            showBreadcrumbHomePath: (_c2 = props.showBreadcrumbHomePath) !== null && _c2 !== void 0 ? _c2 : true
          }));
        });
      }
      return _this;
    }
    App2.prototype.componentDidMount = function() {
      return __awaiter(this, void 0, void 0, function() {
        var _a, data, dispatchEvent, rendererEvent;
        return __generator(this, function(_b) {
          switch (_b.label) {
            case 0:
              _a = this.props, data = _a.data, dispatchEvent = _a.dispatchEvent;
              return [4, dispatchEvent("init", data, this)];
            case 1:
              rendererEvent = _b.sent();
              if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                return [
                  2
                  /*return*/
                ];
              }
              this.reload();
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    App2.prototype.componentDidUpdate = function(prevProps) {
      var _a, _b, _c;
      return __awaiter(this, void 0, void 0, function() {
        var props, store;
        return __generator(this, function(_d) {
          props = this.props;
          store = props.store;
          store.syncProps(props, prevProps, ["pages"]);
          if (isApiOutdated(prevProps.api, props.api, prevProps.data, props.data)) {
            this.reload();
          } else if (props.location && props.location !== prevProps.location) {
            store.updateActivePage(Object.assign({}, (_a = props.env) !== null && _a !== void 0 ? _a : {}, {
              showFullBreadcrumbPath: (_b = props.showFullBreadcrumbPath) !== null && _b !== void 0 ? _b : false,
              showBreadcrumbHomePath: (_c = props.showBreadcrumbHomePath) !== null && _c !== void 0 ? _c : true
            }));
          }
          return [
            2
            /*return*/
          ];
        });
      });
    };
    App2.prototype.componentWillUnmount = function() {
      var _a;
      (_a = this.unWatchRouteChange) === null || _a === void 0 ? void 0 : _a.call(this);
    };
    App2.prototype.reload = function(subpath, query, ctx, silent, replace) {
      return __awaiter(this, void 0, void 0, function() {
        var _a, api, store, env, _b, showFullBreadcrumbPath, _c, showBreadcrumbHomePath, locale, json;
        return __generator(this, function(_d) {
          switch (_d.label) {
            case 0:
              if (query) {
                return [2, this.receive(query, void 0, replace)];
              }
              _a = this.props, api = _a.api, store = _a.store, env = _a.env, _b = _a.showFullBreadcrumbPath, showFullBreadcrumbPath = _b === void 0 ? false : _b, _c = _a.showBreadcrumbHomePath, showBreadcrumbHomePath = _c === void 0 ? true : _c, locale = _a.locale;
              if (!isEffectiveApi(api, store.data))
                return [3, 2];
              return [4, store.fetchInitData(api, store.data, {})];
            case 1:
              json = _d.sent();
              if (env.replaceText) {
                json.data = replaceText(json.data, env.replaceText, env.replaceTextIgnoreKeys);
              }
              if (json === null || json === void 0 ? void 0 : json.data.pages) {
                json.data = envOverwrite(json.data, locale);
                store.setPages(json.data.pages);
                store.updateActivePage(Object.assign({}, env !== null && env !== void 0 ? env : {}, {
                  showFullBreadcrumbPath,
                  showBreadcrumbHomePath
                }));
              }
              _d.label = 2;
            case 2:
              return [2, store.data];
          }
        });
      });
    };
    App2.prototype.receive = function(values, subPath, replace) {
      return __awaiter(this, void 0, void 0, function() {
        var store;
        return __generator(this, function(_a) {
          store = this.props.store;
          store.updateData(values, void 0, replace);
          return [2, this.reload()];
        });
      });
    };
    App2.prototype.resolveDefinitions = function(name) {
      var _a;
      var _b = this.props, resolveDefinitions = _b.resolveDefinitions, store = _b.store;
      var definitions = (_a = store.schema) === null || _a === void 0 ? void 0 : _a.definitions;
      return (definitions === null || definitions === void 0 ? void 0 : definitions[name]) || resolveDefinitions(name);
    };
    App2.prototype.handleNavClick = function(e) {
      e.preventDefault();
      var env = this.props.env;
      var link = e.currentTarget.getAttribute("href");
      env.jumpTo(link, void 0, this.props.data);
    };
    App2.prototype.renderHeader = function() {
      var _a = this.props, cx = _a.classnames, brandName = _a.brandName, header = _a.header, render = _a.render, store = _a.store, logo = _a.logo, env = _a.env;
      if (!header && !logo && !brandName) {
        return null;
      }
      return import_react.default.createElement(
        import_react.default.Fragment,
        null,
        import_react.default.createElement(
          "div",
          { className: cx("Layout-brandBar") },
          import_react.default.createElement(
            "div",
            { onClick: store.toggleOffScreen, className: cx("Layout-offScreenBtn") },
            import_react.default.createElement("i", { className: "bui-icon iconfont icon-collapse" })
          ),
          import_react.default.createElement(
            "div",
            { className: cx("Layout-brand") },
            logo && ~logo.indexOf("<svg") ? import_react.default.createElement(Html$1, { className: cx("AppLogo-html"), html: logo, filterHtml: env.filterHtml }) : logo ? import_react.default.createElement("img", { className: cx("AppLogo"), src: logo }) : import_react.default.createElement("span", { className: "visible-folded " }, brandName === null || brandName === void 0 ? void 0 : brandName.substring(0, 1)),
            import_react.default.createElement("span", { className: "hidden-folded m-l-sm" }, brandName)
          )
        ),
        import_react.default.createElement(
          "div",
          { className: cx("Layout-headerBar") },
          import_react.default.createElement(
            "a",
            { onClick: store.toggleFolded, type: "button", className: cx("AppFoldBtn") },
            import_react.default.createElement("i", { className: "fa fa-".concat(store.folded ? "indent" : "dedent", " fa-fw") })
          ),
          header ? render("header", header) : null
        )
      );
    };
    App2.prototype.renderAside = function() {
      var _this = this;
      var _a = this.props, store = _a.store, env = _a.env, asideBefore = _a.asideBefore, asideAfter = _a.asideAfter, render = _a.render, data = _a.data;
      return import_react.default.createElement(
        import_react.default.Fragment,
        null,
        asideBefore ? render("aside-before", asideBefore) : null,
        import_react.default.createElement(AsideNav$1, { navigations: store.navigations, renderLink: function(_a2, key) {
          var link = _a2.link, active = _a2.active, toggleExpand = _a2.toggleExpand, cx = _a2.classnames, depth = _a2.depth, subHeader = _a2.subHeader;
          var children = [];
          if (link.visible === false) {
            return null;
          }
          if (!subHeader && link.children && link.children.some(function(item) {
            return item === null || item === void 0 ? void 0 : item.visible;
          })) {
            children.push(import_react.default.createElement("span", { key: "expand-toggle", className: cx("AsideNav-itemArrow"), onClick: function(e) {
              return toggleExpand(link, e);
            } }));
          }
          var badge = typeof link.badge === "string" ? filter(link.badge, data) : link.badge;
          badge != null && children.push(import_react.default.createElement("b", { key: "badge", className: cx("AsideNav-itemBadge", link.badgeClassName || "bg-info") }, badge));
          if (!subHeader && link.icon) {
            children.push(import_react.default.createElement(Icon, { key: "icon", cx, icon: link.icon, className: "AsideNav-itemIcon" }));
          } else if (store.folded && depth === 1 && !subHeader) {
            children.push(import_react.default.createElement("i", { key: "icon", className: cx("AsideNav-itemIcon", link.children ? "fa fa-folder" : "fa fa-info") }));
          }
          children.push(import_react.default.createElement("span", { className: cx("AsideNav-itemLabel"), key: "label" }, typeof link.label === "string" ? filter(link.label, data) : link.label));
          return link.path ? /^https?\:/.test(link.path) ? import_react.default.createElement("a", { target: "_blank", key: "link", href: link.path, rel: "noopener" }, children) : import_react.default.createElement("a", { key: "link", onClick: _this.handleNavClick, href: link.path || link.children && link.children[0].path }, children) : import_react.default.createElement("a", { key: "link", onClick: link.children ? function() {
            return toggleExpand(link);
          } : void 0 }, children);
        }, isActive: function(link) {
          return !!env.isCurrentUrl(link === null || link === void 0 ? void 0 : link.path, link);
        } }),
        asideAfter ? render("aside-before", asideAfter) : null
      );
    };
    App2.prototype.renderFooter = function() {
      var _a = this.props, render = _a.render, footer = _a.footer;
      return footer ? render("footer", footer) : null;
    };
    App2.prototype.render = function() {
      var _this = this;
      var _a;
      var _b = this.props, cx = _b.classnames, store = _b.store, render = _b.render, _c = _b.showBreadcrumb, showBreadcrumb = _c === void 0 ? true : _c, loadingConfig = _b.loadingConfig;
      return import_react.default.createElement(
        Layout$1,
        { header: this.renderHeader(), aside: this.renderAside(), footer: this.renderFooter(), folded: store.folded, offScreen: store.offScreen, contentClassName: cx("AppContent") },
        store.activePage && store.schema ? import_react.default.createElement(
          import_react.default.Fragment,
          null,
          showBreadcrumb && store.bcn.length ? import_react.default.createElement("ul", { className: cx("AppBcn") }, store.bcn.map(function(item, index) {
            return import_react.default.createElement("li", { key: index, className: cx("AppBcn-item") }, item.path ? import_react.default.createElement("a", { href: item.path, onClick: _this.handleNavClick }, item.label) : index !== store.bcn.length - 1 ? import_react.default.createElement("a", null, item.label) : item.label);
          })) : null,
          import_react.default.createElement("div", { className: cx("AppBody") }, render("page", store.schema, {
            key: "".concat((_a = store.activePage) === null || _a === void 0 ? void 0 : _a.id, "-").concat(store.schemaKey),
            data: store.pageData,
            resolveDefinitions: this.resolveDefinitions
          }))
        ) : store.pages && !store.activePage ? import_react.default.createElement(
          _404,
          null,
          import_react.default.createElement("div", { className: "text-center" }, "页面不存在")
        ) : null,
        import_react.default.createElement(Spinner$1, { loadingConfig, overlay: true, show: store.loading || !store.pages, size: "lg" })
      );
    };
    App2.propsList = [
      "brandName",
      "logo",
      "header",
      "asideBefore",
      "asideAfter",
      "pages",
      "footer"
    ];
    App2.defaultProps = {};
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [String]),
      __metadata("design:returntype", void 0)
    ], App2.prototype, "resolveDefinitions", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], App2.prototype, "handleNavClick", null);
    return App2;
  }(import_react.default.Component)
);
var AppRenderer = (
  /** @class */
  function(_super) {
    __extends(AppRenderer2, _super);
    function AppRenderer2(props, context) {
      var _this = _super.call(this, props) || this;
      var scoped = context;
      scoped.registerComponent(_this);
      return _this;
    }
    AppRenderer2.prototype.componentWillUnmount = function() {
      var scoped = this.context;
      scoped.unRegisterComponent(this);
      _super.prototype.componentWillUnmount.call(this);
    };
    AppRenderer2.prototype.setData = function(values, replace) {
      return this.props.store.updateData(values, void 0, replace);
    };
    AppRenderer2.prototype.getData = function() {
      var store = this.props.store;
      return store.data;
    };
    AppRenderer2.contextType = ScopedContext;
    AppRenderer2 = __decorate([
      Renderer({
        type: "app",
        storeType: AppStore.name
      }),
      __metadata("design:paramtypes", [Object, Object])
    ], AppRenderer2);
    return AppRenderer2;
  }(App)
);
export {
  App,
  AppRenderer as default
};
//# sourceMappingURL=App-ELZFQEF7.js.map
