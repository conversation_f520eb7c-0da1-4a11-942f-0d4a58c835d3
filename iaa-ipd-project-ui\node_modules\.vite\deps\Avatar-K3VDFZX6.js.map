{"version": 3, "sources": ["../../amis/esm/renderers/Avatar.js"], "sourcesContent": ["/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __decorate, __metadata } from 'tslib';\nimport React from 'react';\nimport { isPureVariable, resolveVariableAndFilter, autobind, Renderer } from 'amis-core';\nimport { Avatar, withBadge } from 'amis-ui';\n\nvar AvatarField = /** @class */ (function (_super) {\n    __extends(AvatarField, _super);\n    function AvatarField() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    AvatarField.prototype.handleClick = function (e) {\n        var _a = this.props, dispatchEvent = _a.dispatchEvent, data = _a.data;\n        dispatchEvent(e, data);\n    };\n    AvatarField.prototype.handleMouseEnter = function (e) {\n        var _a = this.props, dispatchEvent = _a.dispatchEvent, data = _a.data;\n        dispatchEvent(e, data);\n    };\n    AvatarField.prototype.handleMouseLeave = function (e) {\n        var _a = this.props, dispatchEvent = _a.dispatchEvent, data = _a.data;\n        dispatchEvent(e, data);\n    };\n    AvatarField.prototype.render = function () {\n        var _a = this.props, _b = _a.style, style = _b === void 0 ? {} : _b, className = _a.className, cx = _a.classnames, src = _a.src, defaultAvatar = _a.defaultAvatar, _c = _a.icon, icon = _c === void 0 ? 'fa fa-user' : _c, fit = _a.fit, shape = _a.shape, size = _a.size, text = _a.text, gap = _a.gap, alt = _a.alt, draggable = _a.draggable, crossOrigin = _a.crossOrigin, onError = _a.onError, data = _a.data;\n        var errHandler = function () { return false; };\n        if (typeof onError === 'string') {\n            try {\n                errHandler = new Function('event', onError);\n            }\n            catch (e) {\n                console.warn(onError, e);\n            }\n        }\n        if (isPureVariable(src)) {\n            src = resolveVariableAndFilter(src, data, '| raw');\n        }\n        if (isPureVariable(text)) {\n            text = resolveVariableAndFilter(text, data);\n        }\n        if (isPureVariable(icon)) {\n            icon = resolveVariableAndFilter(icon, data);\n        }\n        return (React.createElement(Avatar, { style: style, className: className, classnames: cx, src: src || defaultAvatar, icon: icon, fit: fit, shape: shape, size: size, text: text, gap: gap, alt: alt, draggable: draggable, crossOrigin: crossOrigin, onError: errHandler, onClick: this.handleClick, onMouseEnter: this.handleMouseEnter, onMouseLeave: this.handleMouseLeave }));\n    };\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], AvatarField.prototype, \"handleClick\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], AvatarField.prototype, \"handleMouseEnter\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], AvatarField.prototype, \"handleMouseLeave\", null);\n    return AvatarField;\n}(React.Component));\nvar AvatarFieldRenderer = /** @class */ (function (_super) {\n    __extends(AvatarFieldRenderer, _super);\n    function AvatarFieldRenderer() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    AvatarFieldRenderer = __decorate([\n        Renderer({\n            type: 'avatar'\n        })\n        // @ts-ignore\n        ,\n        withBadge\n    ], AvatarFieldRenderer);\n    return AvatarFieldRenderer;\n}(AvatarField));\n\nexport { AvatarField, AvatarFieldRenderer };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,mBAAkB;AAIlB,IAAI;AAAA;AAAA,EAA6B,SAAU,QAAQ;AAC/C,cAAUA,cAAa,MAAM;AAC7B,aAASA,eAAc;AACnB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,aAAY,UAAU,cAAc,SAAU,GAAG;AAC7C,UAAI,KAAK,KAAK,OAAO,gBAAgB,GAAG,eAAe,OAAO,GAAG;AACjE,oBAAc,GAAG,IAAI;AAAA,IACzB;AACA,IAAAA,aAAY,UAAU,mBAAmB,SAAU,GAAG;AAClD,UAAI,KAAK,KAAK,OAAO,gBAAgB,GAAG,eAAe,OAAO,GAAG;AACjE,oBAAc,GAAG,IAAI;AAAA,IACzB;AACA,IAAAA,aAAY,UAAU,mBAAmB,SAAU,GAAG;AAClD,UAAI,KAAK,KAAK,OAAO,gBAAgB,GAAG,eAAe,OAAO,GAAG;AACjE,oBAAc,GAAG,IAAI;AAAA,IACzB;AACA,IAAAA,aAAY,UAAU,SAAS,WAAY;AACvC,UAAI,KAAK,KAAK,OAAO,KAAK,GAAG,OAAO,QAAQ,OAAO,SAAS,CAAC,IAAI,IAAI,YAAY,GAAG,WAAW,KAAK,GAAG,YAAY,MAAM,GAAG,KAAK,gBAAgB,GAAG,eAAe,KAAK,GAAG,MAAM,OAAO,OAAO,SAAS,eAAe,IAAI,MAAM,GAAG,KAAK,QAAQ,GAAG,OAAO,OAAO,GAAG,MAAM,OAAO,GAAG,MAAM,MAAM,GAAG,KAAK,MAAM,GAAG,KAAK,YAAY,GAAG,WAAW,cAAc,GAAG,aAAa,UAAU,GAAG,SAAS,OAAO,GAAG;AAC/Y,UAAI,aAAa,WAAY;AAAE,eAAO;AAAA,MAAO;AAC7C,UAAI,OAAO,YAAY,UAAU;AAC7B,YAAI;AACA,uBAAa,IAAI,SAAS,SAAS,OAAO;AAAA,QAC9C,SACO,GAAG;AACN,kBAAQ,KAAK,SAAS,CAAC;AAAA,QAC3B;AAAA,MACJ;AACA,UAAI,eAAe,GAAG,GAAG;AACrB,cAAM,yBAAyB,KAAK,MAAM,OAAO;AAAA,MACrD;AACA,UAAI,eAAe,IAAI,GAAG;AACtB,eAAO,yBAAyB,MAAM,IAAI;AAAA,MAC9C;AACA,UAAI,eAAe,IAAI,GAAG;AACtB,eAAO,yBAAyB,MAAM,IAAI;AAAA,MAC9C;AACA,aAAQ,aAAAC,QAAM,cAAc,UAAQ,EAAE,OAAc,WAAsB,YAAY,IAAI,KAAK,OAAO,eAAe,MAAY,KAAU,OAAc,MAAY,MAAY,KAAU,KAAU,WAAsB,aAA0B,SAAS,YAAY,SAAS,KAAK,aAAa,cAAc,KAAK,kBAAkB,cAAc,KAAK,iBAAiB,CAAC;AAAA,IACnX;AACA,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGD,aAAY,WAAW,eAAe,IAAI;AAC7C,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,aAAY,WAAW,oBAAoB,IAAI;AAClD,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,aAAY,WAAW,oBAAoB,IAAI;AAClD,WAAOA;AAAA,EACX,EAAE,aAAAC,QAAM,SAAS;AAAA;AACjB,IAAI;AAAA;AAAA,EAAqC,SAAU,QAAQ;AACvD,cAAUC,sBAAqB,MAAM;AACrC,aAASA,uBAAsB;AAC3B,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,uBAAsB,WAAW;AAAA,MAC7B,SAAS;AAAA,QACL,MAAM;AAAA,MACV,CAAC;AAAA,MAGD;AAAA,IACJ,GAAGA,oBAAmB;AACtB,WAAOA;AAAA,EACX,EAAE,WAAW;AAAA;", "names": ["AvatarField", "React", "AvatarField<PERSON><PERSON><PERSON>"]}