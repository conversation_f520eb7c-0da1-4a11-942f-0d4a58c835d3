{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/restructuredtext/restructuredtext.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '<', close: '>', notIn: ['string'] }\n    ],\n    surroundingPairs: [\n        { open: '(', close: ')' },\n        { open: '[', close: ']' },\n        { open: '`', close: '`' }\n    ],\n    folding: {\n        markers: {\n            start: new RegExp('^\\\\s*<!--\\\\s*#?region\\\\b.*-->'),\n            end: new RegExp('^\\\\s*<!--\\\\s*#?endregion\\\\b.*-->')\n        }\n    }\n};\nexport var language = {\n    defaultToken: '',\n    tokenPostfix: '.rst',\n    control: /[\\\\`*_\\[\\]{}()#+\\-\\.!]/,\n    escapes: /\\\\(?:@control)/,\n    empty: [\n        'area',\n        'base',\n        'basefont',\n        'br',\n        'col',\n        'frame',\n        'hr',\n        'img',\n        'input',\n        'isindex',\n        'link',\n        'meta',\n        'param'\n    ],\n    alphanumerics: /[A-Za-z0-9]/,\n    simpleRefNameWithoutBq: /(?:@alphanumerics[-_+:.]*@alphanumerics)+|(?:@alphanumerics+)/,\n    simpleRefName: /(?:`@phrase`|@simpleRefNameWithoutBq)/,\n    phrase: /@simpleRefNameWithoutBq(?:\\s@simpleRefNameWithoutBq)*/,\n    citationName: /[A-Za-z][A-Za-z0-9-_.]*/,\n    blockLiteralStart: /(?:[!\"#$%&'()*+,-./:;<=>?@\\[\\]^_`{|}~]|[\\s])/,\n    precedingChars: /(?:[ -:/'\"<([{])/,\n    followingChars: /(?:[ -.,:;!?/'\")\\]}>]|$)/,\n    punctuation: /(=|-|~|`|#|\"|\\^|\\+|\\*|:|\\.|'|_|\\+)/,\n    tokenizer: {\n        root: [\n            //sections\n            [/^(@punctuation{3,}$){1,1}?/, 'keyword'],\n            //line-blocks\n            //No rules on it\n            //bullet-lists\n            [/^\\s*([\\*\\-+‣•]|[a-zA-Z0-9]+\\.|\\([a-zA-Z0-9]+\\)|[a-zA-Z0-9]+\\))\\s/, 'keyword'],\n            //literal-blocks\n            [/([ ]::)\\s*$/, 'keyword', '@blankLineOfLiteralBlocks'],\n            [/(::)\\s*$/, 'keyword', '@blankLineOfLiteralBlocks'],\n            { include: '@tables' },\n            { include: '@explicitMarkupBlocks' },\n            { include: '@inlineMarkup' }\n        ],\n        explicitMarkupBlocks: [\n            //citations\n            { include: '@citations' },\n            //footnotes\n            { include: '@footnotes' },\n            //directives\n            [\n                /^(\\.\\.\\s)(@simpleRefName)(::\\s)(.*)$/,\n                [{ token: '', next: 'subsequentLines' }, 'keyword', '', '']\n            ],\n            //hyperlink-targets\n            [\n                /^(\\.\\.)(\\s+)(_)(@simpleRefName)(:)(\\s+)(.*)/,\n                [{ token: '', next: 'hyperlinks' }, '', '', 'string.link', '', '', 'string.link']\n            ],\n            //anonymous-hyperlinks\n            [\n                /^((?:(?:\\.\\.)(?:\\s+))?)(__)(:)(\\s+)(.*)/,\n                [{ token: '', next: 'subsequentLines' }, '', '', '', 'string.link']\n            ],\n            [/^(__\\s+)(.+)/, ['', 'string.link']],\n            //substitution-definitions\n            [\n                /^(\\.\\.)( \\|)([^| ]+[^|]*[^| ]*)(\\| )(@simpleRefName)(:: .*)/,\n                [{ token: '', next: 'subsequentLines' }, '', 'string.link', '', 'keyword', ''],\n                '@rawBlocks'\n            ],\n            [/(\\|)([^| ]+[^|]*[^| ]*)(\\|_{0,2})/, ['', 'string.link', '']],\n            //comments\n            [/^(\\.\\.)([ ].*)$/, [{ token: '', next: '@comments' }, 'comment']]\n        ],\n        inlineMarkup: [\n            { include: '@citationsReference' },\n            { include: '@footnotesReference' },\n            //hyperlink-references\n            [/(@simpleRefName)(_{1,2})/, ['string.link', '']],\n            //embedded-uris-and-aliases\n            [/(`)([^<`]+\\s+)(<)(.*)(>)(`)(_)/, ['', 'string.link', '', 'string.link', '', '', '']],\n            //emphasis\n            [/\\*\\*([^\\\\*]|\\*(?!\\*))+\\*\\*/, 'strong'],\n            [/\\*[^*]+\\*/, 'emphasis'],\n            //inline-literals\n            [/(``)((?:[^`]|\\`(?!`))+)(``)/, ['', 'keyword', '']],\n            [/(__\\s+)(.+)/, ['', 'keyword']],\n            //interpreted-text\n            [/(:)((?:@simpleRefNameWithoutBq)?)(:`)([^`]+)(`)/, ['', 'keyword', '', '', '']],\n            [/(`)([^`]+)(`:)((?:@simpleRefNameWithoutBq)?)(:)/, ['', '', '', 'keyword', '']],\n            [/(`)([^`]+)(`)/, ''],\n            //inline-internal-targets\n            [/(_`)(@phrase)(`)/, ['', 'string.link', '']]\n        ],\n        citations: [\n            [\n                /^(\\.\\.\\s+\\[)((?:@citationName))(\\]\\s+)(.*)/,\n                [{ token: '', next: '@subsequentLines' }, 'string.link', '', '']\n            ]\n        ],\n        citationsReference: [[/(\\[)(@citationName)(\\]_)/, ['', 'string.link', '']]],\n        footnotes: [\n            [\n                /^(\\.\\.\\s+\\[)((?:[0-9]+))(\\]\\s+.*)/,\n                [{ token: '', next: '@subsequentLines' }, 'string.link', '']\n            ],\n            [\n                /^(\\.\\.\\s+\\[)((?:#@simpleRefName?))(\\]\\s+)(.*)/,\n                [{ token: '', next: '@subsequentLines' }, 'string.link', '', '']\n            ],\n            [\n                /^(\\.\\.\\s+\\[)((?:\\*))(\\]\\s+)(.*)/,\n                [{ token: '', next: '@subsequentLines' }, 'string.link', '', '']\n            ]\n        ],\n        footnotesReference: [\n            [/(\\[)([0-9]+)(\\])(_)/, ['', 'string.link', '', '']],\n            [/(\\[)(#@simpleRefName?)(\\])(_)/, ['', 'string.link', '', '']],\n            [/(\\[)(\\*)(\\])(_)/, ['', 'string.link', '', '']]\n        ],\n        blankLineOfLiteralBlocks: [\n            [/^$/, '', '@subsequentLinesOfLiteralBlocks'],\n            [/^.*$/, '', '@pop']\n        ],\n        subsequentLinesOfLiteralBlocks: [\n            [/(@blockLiteralStart+)(.*)/, ['keyword', '']],\n            [/^(?!blockLiteralStart)/, '', '@popall']\n        ],\n        subsequentLines: [\n            [/^[\\s]+.*/, ''],\n            [/^(?!\\s)/, '', '@pop']\n        ],\n        hyperlinks: [\n            [/^[\\s]+.*/, 'string.link'],\n            [/^(?!\\s)/, '', '@pop']\n        ],\n        comments: [\n            [/^[\\s]+.*/, 'comment'],\n            [/^(?!\\s)/, '', '@pop']\n        ],\n        tables: [\n            [/\\+-[+-]+/, 'keyword'],\n            [/\\+=[+=]+/, 'keyword']\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,EAC/C;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,SAAS;AAAA,IACL,SAAS;AAAA,MACL,OAAO,IAAI,OAAO,+BAA+B;AAAA,MACjD,KAAK,IAAI,OAAO,kCAAkC;AAAA,IACtD;AAAA,EACJ;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,SAAS;AAAA,EACT,SAAS;AAAA,EACT,OAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,eAAe;AAAA,EACf,wBAAwB;AAAA,EACxB,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,WAAW;AAAA,IACP,MAAM;AAAA;AAAA,MAEF,CAAC,8BAA8B,SAAS;AAAA;AAAA;AAAA;AAAA,MAIxC,CAAC,oEAAoE,SAAS;AAAA;AAAA,MAE9E,CAAC,eAAe,WAAW,2BAA2B;AAAA,MACtD,CAAC,YAAY,WAAW,2BAA2B;AAAA,MACnD,EAAE,SAAS,UAAU;AAAA,MACrB,EAAE,SAAS,wBAAwB;AAAA,MACnC,EAAE,SAAS,gBAAgB;AAAA,IAC/B;AAAA,IACA,sBAAsB;AAAA;AAAA,MAElB,EAAE,SAAS,aAAa;AAAA;AAAA,MAExB,EAAE,SAAS,aAAa;AAAA;AAAA,MAExB;AAAA,QACI;AAAA,QACA,CAAC,EAAE,OAAO,IAAI,MAAM,kBAAkB,GAAG,WAAW,IAAI,EAAE;AAAA,MAC9D;AAAA;AAAA,MAEA;AAAA,QACI;AAAA,QACA,CAAC,EAAE,OAAO,IAAI,MAAM,aAAa,GAAG,IAAI,IAAI,eAAe,IAAI,IAAI,aAAa;AAAA,MACpF;AAAA;AAAA,MAEA;AAAA,QACI;AAAA,QACA,CAAC,EAAE,OAAO,IAAI,MAAM,kBAAkB,GAAG,IAAI,IAAI,IAAI,aAAa;AAAA,MACtE;AAAA,MACA,CAAC,gBAAgB,CAAC,IAAI,aAAa,CAAC;AAAA;AAAA,MAEpC;AAAA,QACI;AAAA,QACA,CAAC,EAAE,OAAO,IAAI,MAAM,kBAAkB,GAAG,IAAI,eAAe,IAAI,WAAW,EAAE;AAAA,QAC7E;AAAA,MACJ;AAAA,MACA,CAAC,qCAAqC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA;AAAA,MAE7D,CAAC,mBAAmB,CAAC,EAAE,OAAO,IAAI,MAAM,YAAY,GAAG,SAAS,CAAC;AAAA,IACrE;AAAA,IACA,cAAc;AAAA,MACV,EAAE,SAAS,sBAAsB;AAAA,MACjC,EAAE,SAAS,sBAAsB;AAAA;AAAA,MAEjC,CAAC,4BAA4B,CAAC,eAAe,EAAE,CAAC;AAAA;AAAA,MAEhD,CAAC,kCAAkC,CAAC,IAAI,eAAe,IAAI,eAAe,IAAI,IAAI,EAAE,CAAC;AAAA;AAAA,MAErF,CAAC,8BAA8B,QAAQ;AAAA,MACvC,CAAC,aAAa,UAAU;AAAA;AAAA,MAExB,CAAC,+BAA+B,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,MACnD,CAAC,eAAe,CAAC,IAAI,SAAS,CAAC;AAAA;AAAA,MAE/B,CAAC,mDAAmD,CAAC,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;AAAA,MAC/E,CAAC,mDAAmD,CAAC,IAAI,IAAI,IAAI,WAAW,EAAE,CAAC;AAAA,MAC/E,CAAC,iBAAiB,EAAE;AAAA;AAAA,MAEpB,CAAC,oBAAoB,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,IAChD;AAAA,IACA,WAAW;AAAA,MACP;AAAA,QACI;AAAA,QACA,CAAC,EAAE,OAAO,IAAI,MAAM,mBAAmB,GAAG,eAAe,IAAI,EAAE;AAAA,MACnE;AAAA,IACJ;AAAA,IACA,oBAAoB,CAAC,CAAC,4BAA4B,CAAC,IAAI,eAAe,EAAE,CAAC,CAAC;AAAA,IAC1E,WAAW;AAAA,MACP;AAAA,QACI;AAAA,QACA,CAAC,EAAE,OAAO,IAAI,MAAM,mBAAmB,GAAG,eAAe,EAAE;AAAA,MAC/D;AAAA,MACA;AAAA,QACI;AAAA,QACA,CAAC,EAAE,OAAO,IAAI,MAAM,mBAAmB,GAAG,eAAe,IAAI,EAAE;AAAA,MACnE;AAAA,MACA;AAAA,QACI;AAAA,QACA,CAAC,EAAE,OAAO,IAAI,MAAM,mBAAmB,GAAG,eAAe,IAAI,EAAE;AAAA,MACnE;AAAA,IACJ;AAAA,IACA,oBAAoB;AAAA,MAChB,CAAC,uBAAuB,CAAC,IAAI,eAAe,IAAI,EAAE,CAAC;AAAA,MACnD,CAAC,iCAAiC,CAAC,IAAI,eAAe,IAAI,EAAE,CAAC;AAAA,MAC7D,CAAC,mBAAmB,CAAC,IAAI,eAAe,IAAI,EAAE,CAAC;AAAA,IACnD;AAAA,IACA,0BAA0B;AAAA,MACtB,CAAC,MAAM,IAAI,iCAAiC;AAAA,MAC5C,CAAC,QAAQ,IAAI,MAAM;AAAA,IACvB;AAAA,IACA,gCAAgC;AAAA,MAC5B,CAAC,6BAA6B,CAAC,WAAW,EAAE,CAAC;AAAA,MAC7C,CAAC,0BAA0B,IAAI,SAAS;AAAA,IAC5C;AAAA,IACA,iBAAiB;AAAA,MACb,CAAC,YAAY,EAAE;AAAA,MACf,CAAC,WAAW,IAAI,MAAM;AAAA,IAC1B;AAAA,IACA,YAAY;AAAA,MACR,CAAC,YAAY,aAAa;AAAA,MAC1B,CAAC,WAAW,IAAI,MAAM;AAAA,IAC1B;AAAA,IACA,UAAU;AAAA,MACN,CAAC,YAAY,SAAS;AAAA,MACtB,CAAC,WAAW,IAAI,MAAM;AAAA,IAC1B;AAAA,IACA,QAAQ;AAAA,MACJ,CAAC,YAAY,SAAS;AAAA,MACtB,CAAC,YAAY,SAAS;AAAA,IAC1B;AAAA,EACJ;AACJ;", "names": []}