var _vue=require("vue"),_xeUtils=_interopRequireDefault(require("xe-utils")),_ui=require("../../../ui"),_utils=require("../../../ui/src/utils"),_dom=require("../../../ui/src/dom"),_util=require("../../src/util"),_log=require("../../../ui/src/log");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let{getConfig,validators,hooks}=_ui.VxeUI;class Rule{constructor(e){Object.assign(this,{$options:e,required:e.required,min:e.min,max:e.max,type:e.type,pattern:e.pattern,validator:e.validator,trigger:e.trigger,maxWidth:e.maxWidth})}get content(){return(0,_utils.getFuncText)(this.$options.content||this.$options.message)}get message(){return this.content}}function validREValue(e,l){return!(e&&!(_xeUtils.default.isRegExp(e)?e:new RegExp(e)).test(l))}function validMaxValue(e,l){return!(!_xeUtils.default.eqNull(e)&&l>_xeUtils.default.toNumber(e))}function validMinValue(e,l){return!(!_xeUtils.default.eqNull(e)&&l<_xeUtils.default.toNumber(e))}function validRuleValue(e,l,t){var{type:e,min:r,max:i,pattern:a}=e,u="array"===e,n="number"===e,e="string"===e,s=""+l;if(!validREValue(a,s))return!1;if(u){if(!_xeUtils.default.isArray(l))return!1;if(t&&!l.length)return!1;if(!validMinValue(r,l.length))return!1;if(!validMaxValue(i,l.length))return!1}else if(n){a=Number(l);if(isNaN(a))return!1;if(!validMinValue(r,a))return!1;if(!validMaxValue(i,a))return!1}else{if(e&&!_xeUtils.default.isString(l))return!1;if(t&&!s)return!1;if(!validMinValue(r,s.length))return!1;if(!validMaxValue(i,s.length))return!1}return!0}function checkRuleStatus(e,l){var t=e.required,r=_xeUtils.default.isArray(l)?!l.length:(0,_utils.eqEmptyValue)(l);if(t){if(r)return!1;if(!validRuleValue(e,l,t))return!1}else if(!r&&!validRuleValue(e,l,t))return!1;return!0}let tableValidatorMethodKeys=["fullValidate","validate","fullValidateField","validateField","clearValidate"];hooks.add("tableValidatorModule",{setupTable(p){let{props:x,reactData:_,internalData:R}=p,g=p.getRefMaps().refValidTooltip,{computeValidOpts:w,computeTreeOpts:V,computeEditOpts:r,computeAggregateOpts:M}=p.getComputeMaps(),U={},E={},b,t=(e,t,u,a)=>{let n={},{editRules:s,treeConfig:r}=x;var i=_.isRowGroupStatus;let{afterFullData:l,pendingRowMaps:o,removeRowMaps:d}=R;var c=V.value,g=M.value;let f=w.value,v,h=(!0===e?v=l:e&&(_xeUtils.default.isFunction(e)?u=e:v=_xeUtils.default.isArray(e)?e:[e]),v=v||(p.getInsertRecords?p.getInsertRecords().concat(p.getUpdateRecords()):[]),[]),m=(R._lastCallTime=Date.now(),b=!1,U.clearValidate(),{});if(s){let l=t&&t.length?t:p.getColumns();e=i=>{var e=(0,_util.getRowid)(p,i);if(!d[e]&&!o[e]&&!p.isAggregateRecord(i)&&(a||!b)){let e=[];l.forEach(t=>{let r=_xeUtils.default.isString(t)?t:t.field;!a&&b||!_xeUtils.default.has(s,r)||e.push(E.validCellRules("all",i,t).catch(({rule:e,rules:l})=>{l={rule:e,rules:l,rowIndex:p.getRowIndex(i),row:i,columnIndex:p.getColumnIndex(t),column:t,field:r,$table:p};if(n[r]||(n[r]=[]),m[(0,_util.getRowid)(p,i)+":"+t.id]={column:t,row:i,rule:e,content:e.content},n[r].push(l),!a)return b=!0,Promise.reject(l)}))}),h.push(Promise.all(e))}};return i?_xeUtils.default.eachTree(v,e,{children:g.mapChildrenField}):r?(t=c.children||c.childrenField,_xeUtils.default.eachTree(v,e,{children:t})):v.forEach(e),Promise.all(h).then(()=>{let e=Object.keys(n);var l,t,r;return _.validErrorMaps=(l=m,"single"===w.value.msgMode?(t={},(r=Object.keys(l)).length&&(t[r=r[0]]=l[r]),t):l),(0,_vue.nextTick)().then(()=>{if(e.length)return Promise.reject(n[e[0]][0]);u&&u()})}).catch(a=>new Promise((e,l)=>{let t=()=>{(0,_vue.nextTick)(()=>{u?(u(n),e()):("obsolete"===getConfig().validToReject?l:e)(n)})};var r,i=()=>{var l;a.cell=p.getCellElement(a.row,a.column),(0,_dom.scrollToView)(a.cell),l=a,new Promise(e=>{!1===w.value.autoPos?(p.dispatchEvent("valid-error",l,null),e()):p.handleEdit(l,{type:"valid-error",trigger:"call"}).then(()=>{e(E.showValidTooltip(l))})}).then(t)};!1===f.autoPos?t():(r=a.row,p.scrollToRow(r,a.column).then(i))}))}return _.validErrorMaps={},(0,_vue.nextTick)().then(()=>{u&&u()})};return U={fullValidate(e,l){return _xeUtils.default.isFunction(l)&&(0,_log.warnLog)("vxe.error.notValidators",["fullValidate(rows, callback)","fullValidate(rows)"]),t(e,null,l,!0)},validate(e,l){return t(e,null,l)},fullValidateField(e,l){l=(_xeUtils.default.isArray(l)?l:l?[l]:[]).map(e=>(0,_util.handleFieldOrColumn)(p,e));return l.length?t(e,l,null,!0):(0,_vue.nextTick)()},validateField(e,l){l=(_xeUtils.default.isArray(l)?l:l?[l]:[]).map(e=>(0,_util.handleFieldOrColumn)(p,e));return l.length?t(e,l,null):(0,_vue.nextTick)()},clearValidate(e,l){var r=_.validErrorMaps,t=g.value,i=w.value,e=_xeUtils.default.isArray(e)?e:e?[e]:[];let a=(_xeUtils.default.isArray(l)?l:l?[l]:[]).map(e=>(0,_util.handleFieldOrColumn)(p,e)),u={};if(t&&t.reactData.visible&&t.close(),"single"===i.msgMode)_.validErrorMaps={};else{if(e.length&&a.length)u=Object.assign({},r),e.forEach(l=>{a.forEach(e=>{e=(0,_util.getRowid)(p,l)+":"+e.id;u[e]&&delete u[e]})});else if(e.length){let t=e.map(e=>""+(0,_util.getRowid)(p,e));_xeUtils.default.each(r,(e,l)=>{-1<t.indexOf(l.split(":")[0])&&(u[l]=e)})}else if(a.length){let t=a.map(e=>""+e.id);_xeUtils.default.each(r,(e,l)=>{-1<t.indexOf(l.split(":")[1])&&(u[l]=e)})}_.validErrorMaps=u}return(0,_vue.nextTick)()}},E={validCellRules(e,s,o,l){let d=p.xeGrid;var t=x.editRules,r=o.field;let c=[],g=[];if(r&&t){let n=_xeUtils.default.get(t,r);if(n){let u=_xeUtils.default.isUndefined(l)?_xeUtils.default.get(s,r):l;n.forEach(l=>{let{trigger:t,validator:r}=l;if("all"===e||!t||e===t)if(r){var i,a={cellValue:u,rule:l,rules:n,row:s,rowIndex:p.getRowIndex(s),column:o,columnIndex:p.getColumnIndex(o),field:o.field,$table:p,$grid:d};let e;_xeUtils.default.isString(r)?(i=validators.get(r))&&(i=i.tableCellValidatorMethod||i.cellValidatorMethod)?e=i(a):(0,_log.errLog)("vxe.error.notValidators",[r]):e=r(a),e&&(_xeUtils.default.isError(e)?(b=!0,c.push(new Rule({type:"custom",trigger:t,content:e.message,rule:new Rule(l)}))):e.catch&&g.push(e.catch(e=>{b=!0,c.push(new Rule({type:"custom",trigger:t,content:e&&e.message?e.message:l.content||l.message,rule:new Rule(l)}))})))}else checkRuleStatus(l,u)||(b=!0,c.push(new Rule(l)))})}}return Promise.all(g).then(()=>{var e;if(c.length)return e={rules:c,rule:c[0]},Promise.reject(e)})},hasCellRules(l,e,t){var r=x.editRules,t=t.field;return!(!t||!r)&&(r=_xeUtils.default.get(r,t))&&!!_xeUtils.default.find(r,e=>"all"===l||!e.trigger||l===e.trigger)},triggerValidate(i){var{editConfig:e,editRules:l}=x,a=_.editStore,a=a.actived;let u=r.value;var t=w.value;if(l&&"single"===t.msgMode&&(_.validErrorMaps={}),e&&l&&a.row){let{row:l,column:t,cell:r}=a.args;if(E.hasCellRules(i,l,t))return E.validCellRules(i,l,t).then(()=>{"row"===u.mode&&U.clearValidate(l,t)}).catch(({rule:e})=>e.trigger&&i!==e.trigger?Promise.resolve():(e={rule:e,row:l,column:t,cell:r},E.showValidTooltip(e),Promise.reject(e)))}return Promise.resolve()},showValidTooltip(e){var l=x.height,{tableData:t,validStore:r,validErrorMaps:i}=_,{rule:a,row:u,column:n,cell:s}=e,o=w.value,d=g.value,c=a.content;return r.visible=!0,"single"===o.msgMode?_.validErrorMaps={[(0,_util.getRowid)(p,u)+":"+n.id]:{column:n,row:u,rule:a,content:c}}:_.validErrorMaps=Object.assign({},i,{[(0,_util.getRowid)(p,u)+":"+n.id]:{column:n,row:u,rule:a,content:c}}),p.dispatchEvent("valid-error",e,null),d&&("tooltip"===o.message||"default"===o.message&&!l&&t.length<2)?d.open(s,c):(0,_vue.nextTick)()}},Object.assign(Object.assign({},U),E)},setupGrid(e){return e.extendTableMethods(tableValidatorMethodKeys)}});