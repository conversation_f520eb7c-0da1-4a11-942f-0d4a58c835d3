{"version": 3, "sources": ["../../min-dash/dist/index.esm.js"], "sourcesContent": ["/**\n * Flatten array, one level deep.\n *\n * @template T\n *\n * @param {T[][] | T[] | null} [arr]\n *\n * @return {T[]}\n */\nfunction flatten(arr) {\n  return Array.prototype.concat.apply([], arr);\n}\n\nconst nativeToString = Object.prototype.toString;\nconst nativeHasOwnProperty = Object.prototype.hasOwnProperty;\n\nfunction isUndefined(obj) {\n  return obj === undefined;\n}\n\nfunction isDefined(obj) {\n  return obj !== undefined;\n}\n\nfunction isNil(obj) {\n  return obj == null;\n}\n\nfunction isArray(obj) {\n  return nativeToString.call(obj) === '[object Array]';\n}\n\nfunction isObject(obj) {\n  return nativeToString.call(obj) === '[object Object]';\n}\n\nfunction isNumber(obj) {\n  return nativeToString.call(obj) === '[object Number]';\n}\n\n/**\n * @param {any} obj\n *\n * @return {boolean}\n */\nfunction isFunction(obj) {\n  const tag = nativeToString.call(obj);\n\n  return (\n    tag === '[object Function]' ||\n    tag === '[object AsyncFunction]' ||\n    tag === '[object GeneratorFunction]' ||\n    tag === '[object AsyncGeneratorFunction]' ||\n    tag === '[object Proxy]'\n  );\n}\n\nfunction isString(obj) {\n  return nativeToString.call(obj) === '[object String]';\n}\n\n\n/**\n * Ensure collection is an array.\n *\n * @param {Object} obj\n */\nfunction ensureArray(obj) {\n\n  if (isArray(obj)) {\n    return;\n  }\n\n  throw new Error('must supply array');\n}\n\n/**\n * Return true, if target owns a property with the given key.\n *\n * @param {Object} target\n * @param {String} key\n *\n * @return {Boolean}\n */\nfunction has(target, key) {\n  return nativeHasOwnProperty.call(target, key);\n}\n\n/**\n * @template T\n * @typedef { (\n *   ((e: T) => boolean) |\n *   ((e: T, idx: number) => boolean) |\n *   ((e: T, key: string) => boolean) |\n *   string |\n *   number\n * ) } Matcher\n */\n\n/**\n * @template T\n * @template U\n *\n * @typedef { (\n *   ((e: T) => U) | string | number\n * ) } Extractor\n */\n\n\n/**\n * @template T\n * @typedef { (val: T, key: any) => boolean } MatchFn\n */\n\n/**\n * @template T\n * @typedef { T[] } ArrayCollection\n */\n\n/**\n * @template T\n * @typedef { { [key: string]: T } } StringKeyValueCollection\n */\n\n/**\n * @template T\n * @typedef { { [key: number]: T } } NumberKeyValueCollection\n */\n\n/**\n * @template T\n * @typedef { StringKeyValueCollection<T> | NumberKeyValueCollection<T> } KeyValueCollection\n */\n\n/**\n * @template T\n * @typedef { KeyValueCollection<T> | ArrayCollection<T> } Collection\n */\n\n/**\n * Find element in collection.\n *\n * @template T\n * @param {Collection<T>} collection\n * @param {Matcher<T>} matcher\n *\n * @return {Object}\n */\nfunction find(collection, matcher) {\n\n  const matchFn = toMatcher(matcher);\n\n  let match;\n\n  forEach(collection, function(val, key) {\n    if (matchFn(val, key)) {\n      match = val;\n\n      return false;\n    }\n  });\n\n  return match;\n\n}\n\n\n/**\n * Find element index in collection.\n *\n * @template T\n * @param {Collection<T>} collection\n * @param {Matcher<T>} matcher\n *\n * @return {number}\n */\nfunction findIndex(collection, matcher) {\n\n  const matchFn = toMatcher(matcher);\n\n  let idx = isArray(collection) ? -1 : undefined;\n\n  forEach(collection, function(val, key) {\n    if (matchFn(val, key)) {\n      idx = key;\n\n      return false;\n    }\n  });\n\n  return idx;\n}\n\n\n/**\n * Filter elements in collection.\n *\n * @template T\n * @param {Collection<T>} collection\n * @param {Matcher<T>} matcher\n *\n * @return {T[]} result\n */\nfunction filter(collection, matcher) {\n\n  const matchFn = toMatcher(matcher);\n\n  let result = [];\n\n  forEach(collection, function(val, key) {\n    if (matchFn(val, key)) {\n      result.push(val);\n    }\n  });\n\n  return result;\n}\n\n\n/**\n * Iterate over collection; returning something\n * (non-undefined) will stop iteration.\n *\n * @template T\n * @param {Collection<T>} collection\n * @param { ((item: T, idx: number) => (boolean|void)) | ((item: T, key: string) => (boolean|void)) } iterator\n *\n * @return {T} return result that stopped the iteration\n */\nfunction forEach(collection, iterator) {\n\n  let val,\n      result;\n\n  if (isUndefined(collection)) {\n    return;\n  }\n\n  const convertKey = isArray(collection) ? toNum : identity;\n\n  for (let key in collection) {\n\n    if (has(collection, key)) {\n      val = collection[key];\n\n      result = iterator(val, convertKey(key));\n\n      if (result === false) {\n        return val;\n      }\n    }\n  }\n}\n\n/**\n * Return collection without element.\n *\n * @template T\n * @param {ArrayCollection<T>} arr\n * @param {Matcher<T>} matcher\n *\n * @return {T[]}\n */\nfunction without(arr, matcher) {\n\n  if (isUndefined(arr)) {\n    return [];\n  }\n\n  ensureArray(arr);\n\n  const matchFn = toMatcher(matcher);\n\n  return arr.filter(function(el, idx) {\n    return !matchFn(el, idx);\n  });\n\n}\n\n\n/**\n * Reduce collection, returning a single result.\n *\n * @template T\n * @template V\n *\n * @param {Collection<T>} collection\n * @param {(result: V, entry: T, index: any) => V} iterator\n * @param {V} result\n *\n * @return {V} result returned from last iterator\n */\nfunction reduce(collection, iterator, result) {\n\n  forEach(collection, function(value, idx) {\n    result = iterator(result, value, idx);\n  });\n\n  return result;\n}\n\n\n/**\n * Return true if every element in the collection\n * matches the criteria.\n *\n * @param  {Object|Array} collection\n * @param  {Function} matcher\n *\n * @return {Boolean}\n */\nfunction every(collection, matcher) {\n\n  return !!reduce(collection, function(matches, val, key) {\n    return matches && matcher(val, key);\n  }, true);\n}\n\n\n/**\n * Return true if some elements in the collection\n * match the criteria.\n *\n * @param  {Object|Array} collection\n * @param  {Function} matcher\n *\n * @return {Boolean}\n */\nfunction some(collection, matcher) {\n\n  return !!find(collection, matcher);\n}\n\n\n/**\n * Transform a collection into another collection\n * by piping each member through the given fn.\n *\n * @param  {Object|Array}   collection\n * @param  {Function} fn\n *\n * @return {Array} transformed collection\n */\nfunction map(collection, fn) {\n\n  let result = [];\n\n  forEach(collection, function(val, key) {\n    result.push(fn(val, key));\n  });\n\n  return result;\n}\n\n\n/**\n * Get the collections keys.\n *\n * @param  {Object|Array} collection\n *\n * @return {Array}\n */\nfunction keys(collection) {\n  return collection && Object.keys(collection) || [];\n}\n\n\n/**\n * Shorthand for `keys(o).length`.\n *\n * @param  {Object|Array} collection\n *\n * @return {Number}\n */\nfunction size(collection) {\n  return keys(collection).length;\n}\n\n\n/**\n * Get the values in the collection.\n *\n * @param  {Object|Array} collection\n *\n * @return {Array}\n */\nfunction values(collection) {\n  return map(collection, (val) => val);\n}\n\n\n/**\n * Group collection members by attribute.\n *\n * @param {Object|Array} collection\n * @param {Extractor} extractor\n *\n * @return {Object} map with { attrValue => [ a, b, c ] }\n */\nfunction groupBy(collection, extractor, grouped = {}) {\n\n  extractor = toExtractor(extractor);\n\n  forEach(collection, function(val) {\n    let discriminator = extractor(val) || '_';\n\n    let group = grouped[discriminator];\n\n    if (!group) {\n      group = grouped[discriminator] = [];\n    }\n\n    group.push(val);\n  });\n\n  return grouped;\n}\n\n\nfunction uniqueBy(extractor, ...collections) {\n\n  extractor = toExtractor(extractor);\n\n  let grouped = {};\n\n  forEach(collections, (c) => groupBy(c, extractor, grouped));\n\n  let result = map(grouped, function(val, key) {\n    return val[0];\n  });\n\n  return result;\n}\n\n\nconst unionBy = uniqueBy;\n\n\n\n/**\n * Sort collection by criteria.\n *\n * @template T\n *\n * @param {Collection<T>} collection\n * @param {Extractor<T, number | string>} extractor\n *\n * @return {Array}\n */\nfunction sortBy(collection, extractor) {\n\n  extractor = toExtractor(extractor);\n\n  let sorted = [];\n\n  forEach(collection, function(value, key) {\n    let disc = extractor(value, key);\n\n    let entry = {\n      d: disc,\n      v: value\n    };\n\n    for (var idx = 0; idx < sorted.length; idx++) {\n      let { d } = sorted[idx];\n\n      if (disc < d) {\n        sorted.splice(idx, 0, entry);\n        return;\n      }\n    }\n\n    // not inserted, append (!)\n    sorted.push(entry);\n  });\n\n  return map(sorted, (e) => e.v);\n}\n\n\n/**\n * Create an object pattern matcher.\n *\n * @example\n *\n * ```javascript\n * const matcher = matchPattern({ id: 1 });\n *\n * let element = find(elements, matcher);\n * ```\n *\n * @template T\n *\n * @param {T} pattern\n *\n * @return { (el: any) =>  boolean } matcherFn\n */\nfunction matchPattern(pattern) {\n\n  return function(el) {\n\n    return every(pattern, function(val, key) {\n      return el[key] === val;\n    });\n\n  };\n}\n\n\n/**\n * @param {string | ((e: any) => any) } extractor\n *\n * @return { (e: any) => any }\n */\nfunction toExtractor(extractor) {\n\n  /**\n   * @satisfies { (e: any) => any }\n   */\n  return isFunction(extractor) ? extractor : (e) => {\n\n    // @ts-ignore: just works\n    return e[extractor];\n  };\n}\n\n\n/**\n * @template T\n * @param {Matcher<T>} matcher\n *\n * @return {MatchFn<T>}\n */\nfunction toMatcher(matcher) {\n  return isFunction(matcher) ? matcher : (e) => {\n    return e === matcher;\n  };\n}\n\n\nfunction identity(arg) {\n  return arg;\n}\n\nfunction toNum(arg) {\n  return Number(arg);\n}\n\n/* global setTimeout clearTimeout */\n\n/**\n * @typedef { {\n *   (...args: any[]): any;\n *   flush: () => void;\n *   cancel: () => void;\n * } } DebouncedFunction\n */\n\n/**\n * Debounce fn, calling it only once if the given time\n * elapsed between calls.\n *\n * Lodash-style the function exposes methods to `#clear`\n * and `#flush` to control internal behavior.\n *\n * @param  {Function} fn\n * @param  {Number} timeout\n *\n * @return {DebouncedFunction} debounced function\n */\nfunction debounce(fn, timeout) {\n\n  let timer;\n\n  let lastArgs;\n  let lastThis;\n\n  let lastNow;\n\n  function fire(force) {\n\n    let now = Date.now();\n\n    let scheduledDiff = force ? 0 : (lastNow + timeout) - now;\n\n    if (scheduledDiff > 0) {\n      return schedule(scheduledDiff);\n    }\n\n    fn.apply(lastThis, lastArgs);\n\n    clear();\n  }\n\n  function schedule(timeout) {\n    timer = setTimeout(fire, timeout);\n  }\n\n  function clear() {\n    if (timer) {\n      clearTimeout(timer);\n    }\n\n    timer = lastNow = lastArgs = lastThis = undefined;\n  }\n\n  function flush() {\n    if (timer) {\n      fire(true);\n    }\n\n    clear();\n  }\n\n  /**\n   * @type { DebouncedFunction }\n   */\n  function callback(...args) {\n    lastNow = Date.now();\n\n    lastArgs = args;\n    lastThis = this;\n\n    // ensure an execution is scheduled\n    if (!timer) {\n      schedule(timeout);\n    }\n  }\n\n  callback.flush = flush;\n  callback.cancel = clear;\n\n  return callback;\n}\n\n/**\n * Throttle fn, calling at most once\n * in the given interval.\n *\n * @param  {Function} fn\n * @param  {Number} interval\n *\n * @return {Function} throttled function\n */\nfunction throttle(fn, interval) {\n  let throttling = false;\n\n  return function(...args) {\n\n    if (throttling) {\n      return;\n    }\n\n    fn(...args);\n    throttling = true;\n\n    setTimeout(() => {\n      throttling = false;\n    }, interval);\n  };\n}\n\n/**\n * Bind function against target <this>.\n *\n * @param  {Function} fn\n * @param  {Object}   target\n *\n * @return {Function} bound function\n */\nfunction bind(fn, target) {\n  return fn.bind(target);\n}\n\n/**\n * Convenience wrapper for `Object.assign`.\n *\n * @param {Object} target\n * @param {...Object} others\n *\n * @return {Object} the target\n */\nfunction assign(target, ...others) {\n  return Object.assign(target, ...others);\n}\n\n/**\n * Sets a nested property of a given object to the specified value.\n *\n * This mutates the object and returns it.\n *\n * @template T\n *\n * @param {T} target The target of the set operation.\n * @param {(string|number)[]} path The path to the nested value.\n * @param {any} value The value to set.\n *\n * @return {T}\n */\nfunction set(target, path, value) {\n\n  let currentTarget = target;\n\n  forEach(path, function(key, idx) {\n\n    if (typeof key !== 'number' && typeof key !== 'string') {\n      throw new Error('illegal key type: ' + typeof key + '. Key should be of type number or string.');\n    }\n\n    if (key === 'constructor') {\n      throw new Error('illegal key: constructor');\n    }\n\n    if (key === '__proto__') {\n      throw new Error('illegal key: __proto__');\n    }\n\n    let nextKey = path[idx + 1];\n    let nextTarget = currentTarget[key];\n\n    if (isDefined(nextKey) && isNil(nextTarget)) {\n      nextTarget = currentTarget[key] = isNaN(+nextKey) ? {} : [];\n    }\n\n    if (isUndefined(nextKey)) {\n      if (isUndefined(value)) {\n        delete currentTarget[key];\n      } else {\n        currentTarget[key] = value;\n      }\n    } else {\n      currentTarget = nextTarget;\n    }\n  });\n\n  return target;\n}\n\n\n/**\n * Gets a nested property of a given object.\n *\n * @param {Object} target The target of the get operation.\n * @param {(string|number)[]} path The path to the nested value.\n * @param {any} [defaultValue] The value to return if no value exists.\n *\n * @return {any}\n */\nfunction get(target, path, defaultValue) {\n\n  let currentTarget = target;\n\n  forEach(path, function(key) {\n\n    // accessing nil property yields <undefined>\n    if (isNil(currentTarget)) {\n      currentTarget = undefined;\n\n      return false;\n    }\n\n    currentTarget = currentTarget[key];\n  });\n\n  return isUndefined(currentTarget) ? defaultValue : currentTarget;\n}\n\n/**\n * Pick properties from the given target.\n *\n * @template T\n * @template {any[]} V\n *\n * @param {T} target\n * @param {V} properties\n *\n * @return Pick<T, V>\n */\nfunction pick(target, properties) {\n\n  let result = {};\n\n  let obj = Object(target);\n\n  forEach(properties, function(prop) {\n\n    if (prop in obj) {\n      result[prop] = target[prop];\n    }\n  });\n\n  return result;\n}\n\n/**\n * Pick all target properties, excluding the given ones.\n *\n * @template T\n * @template {any[]} V\n *\n * @param {T} target\n * @param {V} properties\n *\n * @return {Omit<T, V>} target\n */\nfunction omit(target, properties) {\n\n  let result = {};\n\n  let obj = Object(target);\n\n  forEach(obj, function(prop, key) {\n\n    if (properties.indexOf(key) === -1) {\n      result[key] = prop;\n    }\n  });\n\n  return result;\n}\n\n/**\n * Recursively merge `...sources` into given target.\n *\n * Does support merging objects; does not support merging arrays.\n *\n * @param {Object} target\n * @param {...Object} sources\n *\n * @return {Object} the target\n */\nfunction merge(target, ...sources) {\n\n  if (!sources.length) {\n    return target;\n  }\n\n  forEach(sources, function(source) {\n\n    // skip non-obj sources, i.e. null\n    if (!source || !isObject(source)) {\n      return;\n    }\n\n    forEach(source, function(sourceVal, key) {\n\n      if (key === '__proto__') {\n        return;\n      }\n\n      let targetVal = target[key];\n\n      if (isObject(sourceVal)) {\n\n        if (!isObject(targetVal)) {\n\n          // override target[key] with object\n          targetVal = {};\n        }\n\n        target[key] = merge(targetVal, sourceVal);\n      } else {\n        target[key] = sourceVal;\n      }\n\n    });\n  });\n\n  return target;\n}\n\nexport { assign, bind, debounce, ensureArray, every, filter, find, findIndex, flatten, forEach, get, groupBy, has, isArray, isDefined, isFunction, isNil, isNumber, isObject, isString, isUndefined, keys, map, matchPattern, merge, omit, pick, reduce, set, size, some, sortBy, throttle, unionBy, uniqueBy, values, without };\n"], "mappings": ";AASA,SAAS,QAAQ,KAAK;AACpB,SAAO,MAAM,UAAU,OAAO,MAAM,CAAC,GAAG,GAAG;AAC7C;AAEA,IAAM,iBAAiB,OAAO,UAAU;AACxC,IAAM,uBAAuB,OAAO,UAAU;AAE9C,SAAS,YAAY,KAAK;AACxB,SAAO,QAAQ;AACjB;AAEA,SAAS,UAAU,KAAK;AACtB,SAAO,QAAQ;AACjB;AAEA,SAAS,MAAM,KAAK;AAClB,SAAO,OAAO;AAChB;AAEA,SAAS,QAAQ,KAAK;AACpB,SAAO,eAAe,KAAK,GAAG,MAAM;AACtC;AAEA,SAAS,SAAS,KAAK;AACrB,SAAO,eAAe,KAAK,GAAG,MAAM;AACtC;AAEA,SAAS,SAAS,KAAK;AACrB,SAAO,eAAe,KAAK,GAAG,MAAM;AACtC;AAOA,SAAS,WAAW,KAAK;AACvB,QAAM,MAAM,eAAe,KAAK,GAAG;AAEnC,SACE,QAAQ,uBACR,QAAQ,4BACR,QAAQ,gCACR,QAAQ,qCACR,QAAQ;AAEZ;AAEA,SAAS,SAAS,KAAK;AACrB,SAAO,eAAe,KAAK,GAAG,MAAM;AACtC;AAQA,SAAS,YAAY,KAAK;AAExB,MAAI,QAAQ,GAAG,GAAG;AAChB;AAAA,EACF;AAEA,QAAM,IAAI,MAAM,mBAAmB;AACrC;AAUA,SAAS,IAAI,QAAQ,KAAK;AACxB,SAAO,qBAAqB,KAAK,QAAQ,GAAG;AAC9C;AA8DA,SAAS,KAAK,YAAY,SAAS;AAEjC,QAAM,UAAU,UAAU,OAAO;AAEjC,MAAI;AAEJ,UAAQ,YAAY,SAAS,KAAK,KAAK;AACrC,QAAI,QAAQ,KAAK,GAAG,GAAG;AACrB,cAAQ;AAER,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AAED,SAAO;AAET;AAYA,SAAS,UAAU,YAAY,SAAS;AAEtC,QAAM,UAAU,UAAU,OAAO;AAEjC,MAAI,MAAM,QAAQ,UAAU,IAAI,KAAK;AAErC,UAAQ,YAAY,SAAS,KAAK,KAAK;AACrC,QAAI,QAAQ,KAAK,GAAG,GAAG;AACrB,YAAM;AAEN,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAYA,SAAS,OAAO,YAAY,SAAS;AAEnC,QAAM,UAAU,UAAU,OAAO;AAEjC,MAAI,SAAS,CAAC;AAEd,UAAQ,YAAY,SAAS,KAAK,KAAK;AACrC,QAAI,QAAQ,KAAK,GAAG,GAAG;AACrB,aAAO,KAAK,GAAG;AAAA,IACjB;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAaA,SAAS,QAAQ,YAAY,UAAU;AAErC,MAAI,KACA;AAEJ,MAAI,YAAY,UAAU,GAAG;AAC3B;AAAA,EACF;AAEA,QAAM,aAAa,QAAQ,UAAU,IAAI,QAAQ;AAEjD,WAAS,OAAO,YAAY;AAE1B,QAAI,IAAI,YAAY,GAAG,GAAG;AACxB,YAAM,WAAW,GAAG;AAEpB,eAAS,SAAS,KAAK,WAAW,GAAG,CAAC;AAEtC,UAAI,WAAW,OAAO;AACpB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AAWA,SAAS,QAAQ,KAAK,SAAS;AAE7B,MAAI,YAAY,GAAG,GAAG;AACpB,WAAO,CAAC;AAAA,EACV;AAEA,cAAY,GAAG;AAEf,QAAM,UAAU,UAAU,OAAO;AAEjC,SAAO,IAAI,OAAO,SAAS,IAAI,KAAK;AAClC,WAAO,CAAC,QAAQ,IAAI,GAAG;AAAA,EACzB,CAAC;AAEH;AAeA,SAAS,OAAO,YAAY,UAAU,QAAQ;AAE5C,UAAQ,YAAY,SAAS,OAAO,KAAK;AACvC,aAAS,SAAS,QAAQ,OAAO,GAAG;AAAA,EACtC,CAAC;AAED,SAAO;AACT;AAYA,SAAS,MAAM,YAAY,SAAS;AAElC,SAAO,CAAC,CAAC,OAAO,YAAY,SAAS,SAAS,KAAK,KAAK;AACtD,WAAO,WAAW,QAAQ,KAAK,GAAG;AAAA,EACpC,GAAG,IAAI;AACT;AAYA,SAAS,KAAK,YAAY,SAAS;AAEjC,SAAO,CAAC,CAAC,KAAK,YAAY,OAAO;AACnC;AAYA,SAAS,IAAI,YAAY,IAAI;AAE3B,MAAI,SAAS,CAAC;AAEd,UAAQ,YAAY,SAAS,KAAK,KAAK;AACrC,WAAO,KAAK,GAAG,KAAK,GAAG,CAAC;AAAA,EAC1B,CAAC;AAED,SAAO;AACT;AAUA,SAAS,KAAK,YAAY;AACxB,SAAO,cAAc,OAAO,KAAK,UAAU,KAAK,CAAC;AACnD;AAUA,SAAS,KAAK,YAAY;AACxB,SAAO,KAAK,UAAU,EAAE;AAC1B;AAUA,SAAS,OAAO,YAAY;AAC1B,SAAO,IAAI,YAAY,CAAC,QAAQ,GAAG;AACrC;AAWA,SAAS,QAAQ,YAAY,WAAW,UAAU,CAAC,GAAG;AAEpD,cAAY,YAAY,SAAS;AAEjC,UAAQ,YAAY,SAAS,KAAK;AAChC,QAAI,gBAAgB,UAAU,GAAG,KAAK;AAEtC,QAAI,QAAQ,QAAQ,aAAa;AAEjC,QAAI,CAAC,OAAO;AACV,cAAQ,QAAQ,aAAa,IAAI,CAAC;AAAA,IACpC;AAEA,UAAM,KAAK,GAAG;AAAA,EAChB,CAAC;AAED,SAAO;AACT;AAGA,SAAS,SAAS,cAAc,aAAa;AAE3C,cAAY,YAAY,SAAS;AAEjC,MAAI,UAAU,CAAC;AAEf,UAAQ,aAAa,CAAC,MAAM,QAAQ,GAAG,WAAW,OAAO,CAAC;AAE1D,MAAI,SAAS,IAAI,SAAS,SAAS,KAAK,KAAK;AAC3C,WAAO,IAAI,CAAC;AAAA,EACd,CAAC;AAED,SAAO;AACT;AAGA,IAAM,UAAU;AAchB,SAAS,OAAO,YAAY,WAAW;AAErC,cAAY,YAAY,SAAS;AAEjC,MAAI,SAAS,CAAC;AAEd,UAAQ,YAAY,SAAS,OAAO,KAAK;AACvC,QAAI,OAAO,UAAU,OAAO,GAAG;AAE/B,QAAI,QAAQ;AAAA,MACV,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAEA,aAAS,MAAM,GAAG,MAAM,OAAO,QAAQ,OAAO;AAC5C,UAAI,EAAE,EAAE,IAAI,OAAO,GAAG;AAEtB,UAAI,OAAO,GAAG;AACZ,eAAO,OAAO,KAAK,GAAG,KAAK;AAC3B;AAAA,MACF;AAAA,IACF;AAGA,WAAO,KAAK,KAAK;AAAA,EACnB,CAAC;AAED,SAAO,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;AAC/B;AAoBA,SAAS,aAAa,SAAS;AAE7B,SAAO,SAAS,IAAI;AAElB,WAAO,MAAM,SAAS,SAAS,KAAK,KAAK;AACvC,aAAO,GAAG,GAAG,MAAM;AAAA,IACrB,CAAC;AAAA,EAEH;AACF;AAQA,SAAS,YAAY,WAAW;AAK9B,SAAO,WAAW,SAAS,IAAI,YAAY,CAAC,MAAM;AAGhD,WAAO,EAAE,SAAS;AAAA,EACpB;AACF;AASA,SAAS,UAAU,SAAS;AAC1B,SAAO,WAAW,OAAO,IAAI,UAAU,CAAC,MAAM;AAC5C,WAAO,MAAM;AAAA,EACf;AACF;AAGA,SAAS,SAAS,KAAK;AACrB,SAAO;AACT;AAEA,SAAS,MAAM,KAAK;AAClB,SAAO,OAAO,GAAG;AACnB;AAwBA,SAAS,SAAS,IAAI,SAAS;AAE7B,MAAI;AAEJ,MAAI;AACJ,MAAI;AAEJ,MAAI;AAEJ,WAAS,KAAK,OAAO;AAEnB,QAAI,MAAM,KAAK,IAAI;AAEnB,QAAI,gBAAgB,QAAQ,IAAK,UAAU,UAAW;AAEtD,QAAI,gBAAgB,GAAG;AACrB,aAAO,SAAS,aAAa;AAAA,IAC/B;AAEA,OAAG,MAAM,UAAU,QAAQ;AAE3B,UAAM;AAAA,EACR;AAEA,WAAS,SAASA,UAAS;AACzB,YAAQ,WAAW,MAAMA,QAAO;AAAA,EAClC;AAEA,WAAS,QAAQ;AACf,QAAI,OAAO;AACT,mBAAa,KAAK;AAAA,IACpB;AAEA,YAAQ,UAAU,WAAW,WAAW;AAAA,EAC1C;AAEA,WAAS,QAAQ;AACf,QAAI,OAAO;AACT,WAAK,IAAI;AAAA,IACX;AAEA,UAAM;AAAA,EACR;AAKA,WAAS,YAAY,MAAM;AACzB,cAAU,KAAK,IAAI;AAEnB,eAAW;AACX,eAAW;AAGX,QAAI,CAAC,OAAO;AACV,eAAS,OAAO;AAAA,IAClB;AAAA,EACF;AAEA,WAAS,QAAQ;AACjB,WAAS,SAAS;AAElB,SAAO;AACT;AAWA,SAAS,SAAS,IAAI,UAAU;AAC9B,MAAI,aAAa;AAEjB,SAAO,YAAY,MAAM;AAEvB,QAAI,YAAY;AACd;AAAA,IACF;AAEA,OAAG,GAAG,IAAI;AACV,iBAAa;AAEb,eAAW,MAAM;AACf,mBAAa;AAAA,IACf,GAAG,QAAQ;AAAA,EACb;AACF;AAUA,SAAS,KAAK,IAAI,QAAQ;AACxB,SAAO,GAAG,KAAK,MAAM;AACvB;AAUA,SAAS,OAAO,WAAW,QAAQ;AACjC,SAAO,OAAO,OAAO,QAAQ,GAAG,MAAM;AACxC;AAeA,SAAS,IAAI,QAAQ,MAAM,OAAO;AAEhC,MAAI,gBAAgB;AAEpB,UAAQ,MAAM,SAAS,KAAK,KAAK;AAE/B,QAAI,OAAO,QAAQ,YAAY,OAAO,QAAQ,UAAU;AACtD,YAAM,IAAI,MAAM,uBAAuB,OAAO,MAAM,2CAA2C;AAAA,IACjG;AAEA,QAAI,QAAQ,eAAe;AACzB,YAAM,IAAI,MAAM,0BAA0B;AAAA,IAC5C;AAEA,QAAI,QAAQ,aAAa;AACvB,YAAM,IAAI,MAAM,wBAAwB;AAAA,IAC1C;AAEA,QAAI,UAAU,KAAK,MAAM,CAAC;AAC1B,QAAI,aAAa,cAAc,GAAG;AAElC,QAAI,UAAU,OAAO,KAAK,MAAM,UAAU,GAAG;AAC3C,mBAAa,cAAc,GAAG,IAAI,MAAM,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC;AAAA,IAC5D;AAEA,QAAI,YAAY,OAAO,GAAG;AACxB,UAAI,YAAY,KAAK,GAAG;AACtB,eAAO,cAAc,GAAG;AAAA,MAC1B,OAAO;AACL,sBAAc,GAAG,IAAI;AAAA,MACvB;AAAA,IACF,OAAO;AACL,sBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAYA,SAAS,IAAI,QAAQ,MAAM,cAAc;AAEvC,MAAI,gBAAgB;AAEpB,UAAQ,MAAM,SAAS,KAAK;AAG1B,QAAI,MAAM,aAAa,GAAG;AACxB,sBAAgB;AAEhB,aAAO;AAAA,IACT;AAEA,oBAAgB,cAAc,GAAG;AAAA,EACnC,CAAC;AAED,SAAO,YAAY,aAAa,IAAI,eAAe;AACrD;AAaA,SAAS,KAAK,QAAQ,YAAY;AAEhC,MAAI,SAAS,CAAC;AAEd,MAAI,MAAM,OAAO,MAAM;AAEvB,UAAQ,YAAY,SAAS,MAAM;AAEjC,QAAI,QAAQ,KAAK;AACf,aAAO,IAAI,IAAI,OAAO,IAAI;AAAA,IAC5B;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAaA,SAAS,KAAK,QAAQ,YAAY;AAEhC,MAAI,SAAS,CAAC;AAEd,MAAI,MAAM,OAAO,MAAM;AAEvB,UAAQ,KAAK,SAAS,MAAM,KAAK;AAE/B,QAAI,WAAW,QAAQ,GAAG,MAAM,IAAI;AAClC,aAAO,GAAG,IAAI;AAAA,IAChB;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAYA,SAAS,MAAM,WAAW,SAAS;AAEjC,MAAI,CAAC,QAAQ,QAAQ;AACnB,WAAO;AAAA,EACT;AAEA,UAAQ,SAAS,SAAS,QAAQ;AAGhC,QAAI,CAAC,UAAU,CAAC,SAAS,MAAM,GAAG;AAChC;AAAA,IACF;AAEA,YAAQ,QAAQ,SAAS,WAAW,KAAK;AAEvC,UAAI,QAAQ,aAAa;AACvB;AAAA,MACF;AAEA,UAAI,YAAY,OAAO,GAAG;AAE1B,UAAI,SAAS,SAAS,GAAG;AAEvB,YAAI,CAAC,SAAS,SAAS,GAAG;AAGxB,sBAAY,CAAC;AAAA,QACf;AAEA,eAAO,GAAG,IAAI,MAAM,WAAW,SAAS;AAAA,MAC1C,OAAO;AACL,eAAO,GAAG,IAAI;AAAA,MAChB;AAAA,IAEF,CAAC;AAAA,EACH,CAAC;AAED,SAAO;AACT;", "names": ["timeout"]}