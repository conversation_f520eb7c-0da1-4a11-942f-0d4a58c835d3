{"version": 3, "sources": ["../../codemirror/addon/mode/simple.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  CodeMirror.defineSimpleMode = function(name, states) {\n    CodeMirror.defineMode(name, function(config) {\n      return CodeMirror.simpleMode(config, states);\n    });\n  };\n\n  CodeMirror.simpleMode = function(config, states) {\n    ensureState(states, \"start\");\n    var states_ = {}, meta = states.meta || {}, hasIndentation = false;\n    for (var state in states) if (state != meta && states.hasOwnProperty(state)) {\n      var list = states_[state] = [], orig = states[state];\n      for (var i = 0; i < orig.length; i++) {\n        var data = orig[i];\n        list.push(new Rule(data, states));\n        if (data.indent || data.dedent) hasIndentation = true;\n      }\n    }\n    var mode = {\n      startState: function() {\n        return {state: \"start\", pending: null,\n                local: null, localState: null,\n                indent: hasIndentation ? [] : null};\n      },\n      copyState: function(state) {\n        var s = {state: state.state, pending: state.pending,\n                 local: state.local, localState: null,\n                 indent: state.indent && state.indent.slice(0)};\n        if (state.localState)\n          s.localState = CodeMirror.copyState(state.local.mode, state.localState);\n        if (state.stack)\n          s.stack = state.stack.slice(0);\n        for (var pers = state.persistentStates; pers; pers = pers.next)\n          s.persistentStates = {mode: pers.mode,\n                                spec: pers.spec,\n                                state: pers.state == state.localState ? s.localState : CodeMirror.copyState(pers.mode, pers.state),\n                                next: s.persistentStates};\n        return s;\n      },\n      token: tokenFunction(states_, config),\n      innerMode: function(state) { return state.local && {mode: state.local.mode, state: state.localState}; },\n      indent: indentFunction(states_, meta)\n    };\n    if (meta) for (var prop in meta) if (meta.hasOwnProperty(prop))\n      mode[prop] = meta[prop];\n    return mode;\n  };\n\n  function ensureState(states, name) {\n    if (!states.hasOwnProperty(name))\n      throw new Error(\"Undefined state \" + name + \" in simple mode\");\n  }\n\n  function toRegex(val, caret) {\n    if (!val) return /(?:)/;\n    var flags = \"\";\n    if (val instanceof RegExp) {\n      if (val.ignoreCase) flags = \"i\";\n      if (val.unicode) flags += \"u\"\n      val = val.source;\n    } else {\n      val = String(val);\n    }\n    return new RegExp((caret === false ? \"\" : \"^\") + \"(?:\" + val + \")\", flags);\n  }\n\n  function asToken(val) {\n    if (!val) return null;\n    if (val.apply) return val\n    if (typeof val == \"string\") return val.replace(/\\./g, \" \");\n    var result = [];\n    for (var i = 0; i < val.length; i++)\n      result.push(val[i] && val[i].replace(/\\./g, \" \"));\n    return result;\n  }\n\n  function Rule(data, states) {\n    if (data.next || data.push) ensureState(states, data.next || data.push);\n    this.regex = toRegex(data.regex);\n    this.token = asToken(data.token);\n    this.data = data;\n  }\n\n  function tokenFunction(states, config) {\n    return function(stream, state) {\n      if (state.pending) {\n        var pend = state.pending.shift();\n        if (state.pending.length == 0) state.pending = null;\n        stream.pos += pend.text.length;\n        return pend.token;\n      }\n\n      if (state.local) {\n        if (state.local.end && stream.match(state.local.end)) {\n          var tok = state.local.endToken || null;\n          state.local = state.localState = null;\n          return tok;\n        } else {\n          var tok = state.local.mode.token(stream, state.localState), m;\n          if (state.local.endScan && (m = state.local.endScan.exec(stream.current())))\n            stream.pos = stream.start + m.index;\n          return tok;\n        }\n      }\n\n      var curState = states[state.state];\n      for (var i = 0; i < curState.length; i++) {\n        var rule = curState[i];\n        var matches = (!rule.data.sol || stream.sol()) && stream.match(rule.regex);\n        if (matches) {\n          if (rule.data.next) {\n            state.state = rule.data.next;\n          } else if (rule.data.push) {\n            (state.stack || (state.stack = [])).push(state.state);\n            state.state = rule.data.push;\n          } else if (rule.data.pop && state.stack && state.stack.length) {\n            state.state = state.stack.pop();\n          }\n\n          if (rule.data.mode)\n            enterLocalMode(config, state, rule.data.mode, rule.token);\n          if (rule.data.indent)\n            state.indent.push(stream.indentation() + config.indentUnit);\n          if (rule.data.dedent)\n            state.indent.pop();\n          var token = rule.token\n          if (token && token.apply) token = token(matches)\n          if (matches.length > 2 && rule.token && typeof rule.token != \"string\") {\n            for (var j = 2; j < matches.length; j++)\n              if (matches[j])\n                (state.pending || (state.pending = [])).push({text: matches[j], token: rule.token[j - 1]});\n            stream.backUp(matches[0].length - (matches[1] ? matches[1].length : 0));\n            return token[0];\n          } else if (token && token.join) {\n            return token[0];\n          } else {\n            return token;\n          }\n        }\n      }\n      stream.next();\n      return null;\n    };\n  }\n\n  function cmp(a, b) {\n    if (a === b) return true;\n    if (!a || typeof a != \"object\" || !b || typeof b != \"object\") return false;\n    var props = 0;\n    for (var prop in a) if (a.hasOwnProperty(prop)) {\n      if (!b.hasOwnProperty(prop) || !cmp(a[prop], b[prop])) return false;\n      props++;\n    }\n    for (var prop in b) if (b.hasOwnProperty(prop)) props--;\n    return props == 0;\n  }\n\n  function enterLocalMode(config, state, spec, token) {\n    var pers;\n    if (spec.persistent) for (var p = state.persistentStates; p && !pers; p = p.next)\n      if (spec.spec ? cmp(spec.spec, p.spec) : spec.mode == p.mode) pers = p;\n    var mode = pers ? pers.mode : spec.mode || CodeMirror.getMode(config, spec.spec);\n    var lState = pers ? pers.state : CodeMirror.startState(mode);\n    if (spec.persistent && !pers)\n      state.persistentStates = {mode: mode, spec: spec.spec, state: lState, next: state.persistentStates};\n\n    state.localState = lState;\n    state.local = {mode: mode,\n                   end: spec.end && toRegex(spec.end),\n                   endScan: spec.end && spec.forceEnd !== false && toRegex(spec.end, false),\n                   endToken: token && token.join ? token[token.length - 1] : token};\n  }\n\n  function indexOf(val, arr) {\n    for (var i = 0; i < arr.length; i++) if (arr[i] === val) return true;\n  }\n\n  function indentFunction(states, meta) {\n    return function(state, textAfter, line) {\n      if (state.local && state.local.mode.indent)\n        return state.local.mode.indent(state.localState, textAfter, line);\n      if (state.indent == null || state.local || meta.dontIndentStates && indexOf(state.state, meta.dontIndentStates) > -1)\n        return CodeMirror.Pass;\n\n      var pos = state.indent.length - 1, rules = states[state.state];\n      scan: for (;;) {\n        for (var i = 0; i < rules.length; i++) {\n          var rule = rules[i];\n          if (rule.data.dedent && rule.data.dedentIfLineStart !== false) {\n            var m = rule.regex.exec(textAfter);\n            if (m && m[0]) {\n              pos--;\n              if (rule.next || rule.push) rules = states[rule.next || rule.push];\n              textAfter = textAfter.slice(m[0].length);\n              continue scan;\n            }\n          }\n        }\n        break;\n      }\n      return pos < 0 ? 0 : state.indent[pos];\n    };\n  }\n});\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACtB;AAEA,MAAAA,YAAW,mBAAmB,SAAS,MAAM,QAAQ;AACnD,QAAAA,YAAW,WAAW,MAAM,SAAS,QAAQ;AAC3C,iBAAOA,YAAW,WAAW,QAAQ,MAAM;AAAA,QAC7C,CAAC;AAAA,MACH;AAEA,MAAAA,YAAW,aAAa,SAAS,QAAQ,QAAQ;AAC/C,oBAAY,QAAQ,OAAO;AAC3B,YAAI,UAAU,CAAC,GAAG,OAAO,OAAO,QAAQ,CAAC,GAAG,iBAAiB;AAC7D,iBAAS,SAAS;AAAQ,cAAI,SAAS,QAAQ,OAAO,eAAe,KAAK,GAAG;AAC3E,gBAAI,OAAO,QAAQ,KAAK,IAAI,CAAC,GAAG,OAAO,OAAO,KAAK;AACnD,qBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,kBAAI,OAAO,KAAK,CAAC;AACjB,mBAAK,KAAK,IAAI,KAAK,MAAM,MAAM,CAAC;AAChC,kBAAI,KAAK,UAAU,KAAK;AAAQ,iCAAiB;AAAA,YACnD;AAAA,UACF;AACA,YAAI,OAAO;AAAA,UACT,YAAY,WAAW;AACrB,mBAAO;AAAA,cAAC,OAAO;AAAA,cAAS,SAAS;AAAA,cACzB,OAAO;AAAA,cAAM,YAAY;AAAA,cACzB,QAAQ,iBAAiB,CAAC,IAAI;AAAA,YAAI;AAAA,UAC5C;AAAA,UACA,WAAW,SAASC,QAAO;AACzB,gBAAI,IAAI;AAAA,cAAC,OAAOA,OAAM;AAAA,cAAO,SAASA,OAAM;AAAA,cACnC,OAAOA,OAAM;AAAA,cAAO,YAAY;AAAA,cAChC,QAAQA,OAAM,UAAUA,OAAM,OAAO,MAAM,CAAC;AAAA,YAAC;AACtD,gBAAIA,OAAM;AACR,gBAAE,aAAaD,YAAW,UAAUC,OAAM,MAAM,MAAMA,OAAM,UAAU;AACxE,gBAAIA,OAAM;AACR,gBAAE,QAAQA,OAAM,MAAM,MAAM,CAAC;AAC/B,qBAAS,OAAOA,OAAM,kBAAkB,MAAM,OAAO,KAAK;AACxD,gBAAE,mBAAmB;AAAA,gBAAC,MAAM,KAAK;AAAA,gBACX,MAAM,KAAK;AAAA,gBACX,OAAO,KAAK,SAASA,OAAM,aAAa,EAAE,aAAaD,YAAW,UAAU,KAAK,MAAM,KAAK,KAAK;AAAA,gBACjG,MAAM,EAAE;AAAA,cAAgB;AAChD,mBAAO;AAAA,UACT;AAAA,UACA,OAAO,cAAc,SAAS,MAAM;AAAA,UACpC,WAAW,SAASC,QAAO;AAAE,mBAAOA,OAAM,SAAS,EAAC,MAAMA,OAAM,MAAM,MAAM,OAAOA,OAAM,WAAU;AAAA,UAAG;AAAA,UACtG,QAAQ,eAAe,SAAS,IAAI;AAAA,QACtC;AACA,YAAI;AAAM,mBAAS,QAAQ;AAAM,gBAAI,KAAK,eAAe,IAAI;AAC3D,mBAAK,IAAI,IAAI,KAAK,IAAI;AAAA;AACxB,eAAO;AAAA,MACT;AAEA,eAAS,YAAY,QAAQ,MAAM;AACjC,YAAI,CAAC,OAAO,eAAe,IAAI;AAC7B,gBAAM,IAAI,MAAM,qBAAqB,OAAO,iBAAiB;AAAA,MACjE;AAEA,eAAS,QAAQ,KAAK,OAAO;AAC3B,YAAI,CAAC;AAAK,iBAAO;AACjB,YAAI,QAAQ;AACZ,YAAI,eAAe,QAAQ;AACzB,cAAI,IAAI;AAAY,oBAAQ;AAC5B,cAAI,IAAI;AAAS,qBAAS;AAC1B,gBAAM,IAAI;AAAA,QACZ,OAAO;AACL,gBAAM,OAAO,GAAG;AAAA,QAClB;AACA,eAAO,IAAI,QAAQ,UAAU,QAAQ,KAAK,OAAO,QAAQ,MAAM,KAAK,KAAK;AAAA,MAC3E;AAEA,eAAS,QAAQ,KAAK;AACpB,YAAI,CAAC;AAAK,iBAAO;AACjB,YAAI,IAAI;AAAO,iBAAO;AACtB,YAAI,OAAO,OAAO;AAAU,iBAAO,IAAI,QAAQ,OAAO,GAAG;AACzD,YAAI,SAAS,CAAC;AACd,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ;AAC9B,iBAAO,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,EAAE,QAAQ,OAAO,GAAG,CAAC;AAClD,eAAO;AAAA,MACT;AAEA,eAAS,KAAK,MAAM,QAAQ;AAC1B,YAAI,KAAK,QAAQ,KAAK;AAAM,sBAAY,QAAQ,KAAK,QAAQ,KAAK,IAAI;AACtE,aAAK,QAAQ,QAAQ,KAAK,KAAK;AAC/B,aAAK,QAAQ,QAAQ,KAAK,KAAK;AAC/B,aAAK,OAAO;AAAA,MACd;AAEA,eAAS,cAAc,QAAQ,QAAQ;AACrC,eAAO,SAAS,QAAQ,OAAO;AAC7B,cAAI,MAAM,SAAS;AACjB,gBAAI,OAAO,MAAM,QAAQ,MAAM;AAC/B,gBAAI,MAAM,QAAQ,UAAU;AAAG,oBAAM,UAAU;AAC/C,mBAAO,OAAO,KAAK,KAAK;AACxB,mBAAO,KAAK;AAAA,UACd;AAEA,cAAI,MAAM,OAAO;AACf,gBAAI,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,GAAG,GAAG;AACpD,kBAAI,MAAM,MAAM,MAAM,YAAY;AAClC,oBAAM,QAAQ,MAAM,aAAa;AACjC,qBAAO;AAAA,YACT,OAAO;AACL,kBAAI,MAAM,MAAM,MAAM,KAAK,MAAM,QAAQ,MAAM,UAAU,GAAG;AAC5D,kBAAI,MAAM,MAAM,YAAY,IAAI,MAAM,MAAM,QAAQ,KAAK,OAAO,QAAQ,CAAC;AACvE,uBAAO,MAAM,OAAO,QAAQ,EAAE;AAChC,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,cAAI,WAAW,OAAO,MAAM,KAAK;AACjC,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,gBAAI,OAAO,SAAS,CAAC;AACrB,gBAAI,WAAW,CAAC,KAAK,KAAK,OAAO,OAAO,IAAI,MAAM,OAAO,MAAM,KAAK,KAAK;AACzE,gBAAI,SAAS;AACX,kBAAI,KAAK,KAAK,MAAM;AAClB,sBAAM,QAAQ,KAAK,KAAK;AAAA,cAC1B,WAAW,KAAK,KAAK,MAAM;AACzB,iBAAC,MAAM,UAAU,MAAM,QAAQ,CAAC,IAAI,KAAK,MAAM,KAAK;AACpD,sBAAM,QAAQ,KAAK,KAAK;AAAA,cAC1B,WAAW,KAAK,KAAK,OAAO,MAAM,SAAS,MAAM,MAAM,QAAQ;AAC7D,sBAAM,QAAQ,MAAM,MAAM,IAAI;AAAA,cAChC;AAEA,kBAAI,KAAK,KAAK;AACZ,+BAAe,QAAQ,OAAO,KAAK,KAAK,MAAM,KAAK,KAAK;AAC1D,kBAAI,KAAK,KAAK;AACZ,sBAAM,OAAO,KAAK,OAAO,YAAY,IAAI,OAAO,UAAU;AAC5D,kBAAI,KAAK,KAAK;AACZ,sBAAM,OAAO,IAAI;AACnB,kBAAI,QAAQ,KAAK;AACjB,kBAAI,SAAS,MAAM;AAAO,wBAAQ,MAAM,OAAO;AAC/C,kBAAI,QAAQ,SAAS,KAAK,KAAK,SAAS,OAAO,KAAK,SAAS,UAAU;AACrE,yBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ;AAClC,sBAAI,QAAQ,CAAC;AACX,qBAAC,MAAM,YAAY,MAAM,UAAU,CAAC,IAAI,KAAK,EAAC,MAAM,QAAQ,CAAC,GAAG,OAAO,KAAK,MAAM,IAAI,CAAC,EAAC,CAAC;AAC7F,uBAAO,OAAO,QAAQ,CAAC,EAAE,UAAU,QAAQ,CAAC,IAAI,QAAQ,CAAC,EAAE,SAAS,EAAE;AACtE,uBAAO,MAAM,CAAC;AAAA,cAChB,WAAW,SAAS,MAAM,MAAM;AAC9B,uBAAO,MAAM,CAAC;AAAA,cAChB,OAAO;AACL,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AACA,iBAAO,KAAK;AACZ,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,eAAS,IAAI,GAAG,GAAG;AACjB,YAAI,MAAM;AAAG,iBAAO;AACpB,YAAI,CAAC,KAAK,OAAO,KAAK,YAAY,CAAC,KAAK,OAAO,KAAK;AAAU,iBAAO;AACrE,YAAI,QAAQ;AACZ,iBAAS,QAAQ;AAAG,cAAI,EAAE,eAAe,IAAI,GAAG;AAC9C,gBAAI,CAAC,EAAE,eAAe,IAAI,KAAK,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,IAAI,CAAC;AAAG,qBAAO;AAC9D;AAAA,UACF;AACA,iBAAS,QAAQ;AAAG,cAAI,EAAE,eAAe,IAAI;AAAG;AAChD,eAAO,SAAS;AAAA,MAClB;AAEA,eAAS,eAAe,QAAQ,OAAO,MAAM,OAAO;AAClD,YAAI;AACJ,YAAI,KAAK;AAAY,mBAAS,IAAI,MAAM,kBAAkB,KAAK,CAAC,MAAM,IAAI,EAAE;AAC1E,gBAAI,KAAK,OAAO,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,KAAK,QAAQ,EAAE;AAAM,qBAAO;AAAA;AACvE,YAAI,OAAO,OAAO,KAAK,OAAO,KAAK,QAAQD,YAAW,QAAQ,QAAQ,KAAK,IAAI;AAC/E,YAAI,SAAS,OAAO,KAAK,QAAQA,YAAW,WAAW,IAAI;AAC3D,YAAI,KAAK,cAAc,CAAC;AACtB,gBAAM,mBAAmB,EAAC,MAAY,MAAM,KAAK,MAAM,OAAO,QAAQ,MAAM,MAAM,iBAAgB;AAEpG,cAAM,aAAa;AACnB,cAAM,QAAQ;AAAA,UAAC;AAAA,UACA,KAAK,KAAK,OAAO,QAAQ,KAAK,GAAG;AAAA,UACjC,SAAS,KAAK,OAAO,KAAK,aAAa,SAAS,QAAQ,KAAK,KAAK,KAAK;AAAA,UACvE,UAAU,SAAS,MAAM,OAAO,MAAM,MAAM,SAAS,CAAC,IAAI;AAAA,QAAK;AAAA,MAChF;AAEA,eAAS,QAAQ,KAAK,KAAK;AACzB,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ;AAAK,cAAI,IAAI,CAAC,MAAM;AAAK,mBAAO;AAAA,MAClE;AAEA,eAAS,eAAe,QAAQ,MAAM;AACpC,eAAO,SAAS,OAAO,WAAW,MAAM;AACtC,cAAI,MAAM,SAAS,MAAM,MAAM,KAAK;AAClC,mBAAO,MAAM,MAAM,KAAK,OAAO,MAAM,YAAY,WAAW,IAAI;AAClE,cAAI,MAAM,UAAU,QAAQ,MAAM,SAAS,KAAK,oBAAoB,QAAQ,MAAM,OAAO,KAAK,gBAAgB,IAAI;AAChH,mBAAOA,YAAW;AAEpB,cAAI,MAAM,MAAM,OAAO,SAAS,GAAG,QAAQ,OAAO,MAAM,KAAK;AAC7D;AAAM,uBAAS;AACb,uBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,oBAAI,OAAO,MAAM,CAAC;AAClB,oBAAI,KAAK,KAAK,UAAU,KAAK,KAAK,sBAAsB,OAAO;AAC7D,sBAAI,IAAI,KAAK,MAAM,KAAK,SAAS;AACjC,sBAAI,KAAK,EAAE,CAAC,GAAG;AACb;AACA,wBAAI,KAAK,QAAQ,KAAK;AAAM,8BAAQ,OAAO,KAAK,QAAQ,KAAK,IAAI;AACjE,gCAAY,UAAU,MAAM,EAAE,CAAC,EAAE,MAAM;AACvC,6BAAS;AAAA,kBACX;AAAA,gBACF;AAAA,cACF;AACA;AAAA,YACF;AACA,iBAAO,MAAM,IAAI,IAAI,MAAM,OAAO,GAAG;AAAA,QACvC;AAAA,MACF;AAAA,IACF,CAAC;AAAA;AAAA;", "names": ["CodeMirror", "state"]}