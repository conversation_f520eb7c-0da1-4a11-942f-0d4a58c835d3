/*pager*/
.vxe-pager {
  position: relative;
  display: flex;
  align-items: center;
  color: var(--vxe-ui-font-color);
  font-family: var(--vxe-ui-font-family);
  text-align: right;
  background-color: var(--vxe-ui-layout-background-color);
}
.vxe-pager.is--hidden {
  display: none;
}
.vxe-pager.align--left {
  text-align: left;
}
.vxe-pager.align--center {
  text-align: center;
}
.vxe-pager.is--loading:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  background-color: var(--vxe-ui-loading-background-color);
}
.vxe-pager .vxe-pager--home-btn,
.vxe-pager .vxe-pager--end-btn,
.vxe-pager .vxe-pager--prev-btn,
.vxe-pager .vxe-pager--next-btn,
.vxe-pager .vxe-pager--num-btn,
.vxe-pager .vxe-pager--jump-prev,
.vxe-pager .vxe-pager--jump-next {
  color: inherit;
  outline: 0;
  padding: 0;
  border: 1px solid transparent;
  font-size: inherit;
}
.vxe-pager .vxe-pager--home-btn:not(.is--disabled):focus,
.vxe-pager .vxe-pager--end-btn:not(.is--disabled):focus,
.vxe-pager .vxe-pager--prev-btn:not(.is--disabled):focus,
.vxe-pager .vxe-pager--next-btn:not(.is--disabled):focus,
.vxe-pager .vxe-pager--num-btn:not(.is--disabled):focus,
.vxe-pager .vxe-pager--jump-prev:not(.is--disabled):focus,
.vxe-pager .vxe-pager--jump-next:not(.is--disabled):focus {
  box-shadow: 0 0 0.25em 0 var(--vxe-ui-font-primary-color);
}
.vxe-pager .vxe-pager--home-btn:not(.is--disabled):hover,
.vxe-pager .vxe-pager--end-btn:not(.is--disabled):hover,
.vxe-pager .vxe-pager--prev-btn:not(.is--disabled):hover,
.vxe-pager .vxe-pager--next-btn:not(.is--disabled):hover,
.vxe-pager .vxe-pager--num-btn:not(.is--disabled):hover,
.vxe-pager .vxe-pager--jump-prev:not(.is--disabled):hover,
.vxe-pager .vxe-pager--jump-next:not(.is--disabled):hover {
  color: var(--vxe-ui-font-primary-lighten-color);
}
.vxe-pager .vxe-pager--home-btn:not(.is--disabled):active,
.vxe-pager .vxe-pager--end-btn:not(.is--disabled):active,
.vxe-pager .vxe-pager--prev-btn:not(.is--disabled):active,
.vxe-pager .vxe-pager--next-btn:not(.is--disabled):active,
.vxe-pager .vxe-pager--num-btn:not(.is--disabled):active,
.vxe-pager .vxe-pager--jump-prev:not(.is--disabled):active,
.vxe-pager .vxe-pager--jump-next:not(.is--disabled):active {
  background-color: #fff;
}
.vxe-pager.is--border:not(.is--background) .vxe-pager--home-btn,
.vxe-pager.is--border:not(.is--background) .vxe-pager--end-btn,
.vxe-pager.is--border:not(.is--background) .vxe-pager--prev-btn,
.vxe-pager.is--border:not(.is--background) .vxe-pager--next-btn,
.vxe-pager.is--border:not(.is--background) .vxe-pager--num-btn,
.vxe-pager.is--border:not(.is--background) .vxe-pager--jump-prev,
.vxe-pager.is--border:not(.is--background) .vxe-pager--jump-next, .vxe-pager.is--perfect:not(.is--background) .vxe-pager--home-btn,
.vxe-pager.is--perfect:not(.is--background) .vxe-pager--end-btn,
.vxe-pager.is--perfect:not(.is--background) .vxe-pager--prev-btn,
.vxe-pager.is--perfect:not(.is--background) .vxe-pager--next-btn,
.vxe-pager.is--perfect:not(.is--background) .vxe-pager--num-btn,
.vxe-pager.is--perfect:not(.is--background) .vxe-pager--jump-prev,
.vxe-pager.is--perfect:not(.is--background) .vxe-pager--jump-next {
  border-color: var(--vxe-ui-input-border-color);
}
.vxe-pager.is--background .vxe-pager--home-btn,
.vxe-pager.is--background .vxe-pager--end-btn,
.vxe-pager.is--background .vxe-pager--prev-btn,
.vxe-pager.is--background .vxe-pager--next-btn,
.vxe-pager.is--background .vxe-pager--jump-prev,
.vxe-pager.is--background .vxe-pager--num-btn,
.vxe-pager.is--background .vxe-pager--jump-next, .vxe-pager.is--perfect .vxe-pager--home-btn,
.vxe-pager.is--perfect .vxe-pager--end-btn,
.vxe-pager.is--perfect .vxe-pager--prev-btn,
.vxe-pager.is--perfect .vxe-pager--next-btn,
.vxe-pager.is--perfect .vxe-pager--jump-prev,
.vxe-pager.is--perfect .vxe-pager--num-btn,
.vxe-pager.is--perfect .vxe-pager--jump-next {
  background-color: var(--vxe-ui-layout-background-color);
}
.vxe-pager.is--background .vxe-pager--jump-prev:not(.is--disabled).is--active,
.vxe-pager.is--background .vxe-pager--num-btn:not(.is--disabled).is--active,
.vxe-pager.is--background .vxe-pager--jump-next:not(.is--disabled).is--active, .vxe-pager.is--perfect .vxe-pager--jump-prev:not(.is--disabled).is--active,
.vxe-pager.is--perfect .vxe-pager--num-btn:not(.is--disabled).is--active,
.vxe-pager.is--perfect .vxe-pager--jump-next:not(.is--disabled).is--active {
  color: #fff;
  background-color: var(--vxe-ui-font-primary-color);
}
.vxe-pager.is--background .vxe-pager--jump-prev:not(.is--disabled).is--active:hover,
.vxe-pager.is--background .vxe-pager--num-btn:not(.is--disabled).is--active:hover,
.vxe-pager.is--background .vxe-pager--jump-next:not(.is--disabled).is--active:hover, .vxe-pager.is--perfect .vxe-pager--jump-prev:not(.is--disabled).is--active:hover,
.vxe-pager.is--perfect .vxe-pager--num-btn:not(.is--disabled).is--active:hover,
.vxe-pager.is--perfect .vxe-pager--jump-next:not(.is--disabled).is--active:hover {
  background-color: var(--vxe-ui-font-primary-lighten-color);
}
.vxe-pager.is--background .vxe-pager--jump-prev:not(.is--disabled).is--active:focus,
.vxe-pager.is--background .vxe-pager--num-btn:not(.is--disabled).is--active:focus,
.vxe-pager.is--background .vxe-pager--jump-next:not(.is--disabled).is--active:focus, .vxe-pager.is--perfect .vxe-pager--jump-prev:not(.is--disabled).is--active:focus,
.vxe-pager.is--perfect .vxe-pager--num-btn:not(.is--disabled).is--active:focus,
.vxe-pager.is--perfect .vxe-pager--jump-next:not(.is--disabled).is--active:focus {
  border-color: var(--vxe-ui-font-primary-color);
}
.vxe-pager.is--background .vxe-pager--jump-prev:not(.is--disabled).is--active:active,
.vxe-pager.is--background .vxe-pager--num-btn:not(.is--disabled).is--active:active,
.vxe-pager.is--background .vxe-pager--jump-next:not(.is--disabled).is--active:active, .vxe-pager.is--perfect .vxe-pager--jump-prev:not(.is--disabled).is--active:active,
.vxe-pager.is--perfect .vxe-pager--num-btn:not(.is--disabled).is--active:active,
.vxe-pager.is--perfect .vxe-pager--jump-next:not(.is--disabled).is--active:active {
  border-color: var(--vxe-ui-font-primary-darken-color);
  background-color: var(--vxe-ui-font-primary-darken-color);
}
.vxe-pager.is--perfect {
  border: 1px solid var(--vxe-ui-input-border-color);
  border-top-width: 0;
  background-color: var(--vxe-ui-layout-background-color);
}
.vxe-pager.is--border .vxe-pager--num-btn.is--active {
  border-color: var(--vxe-ui-font-primary-color);
}
.vxe-pager .vxe-pager--wrapper {
  flex-grow: 1;
}
.vxe-pager .vxe-pager--jump-icon,
.vxe-pager .vxe-pager--btn-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.vxe-pager .vxe-pager--left-wrapper,
.vxe-pager .vxe-pager--right-wrapper,
.vxe-pager .vxe-pager--total,
.vxe-pager .vxe-pager--home-btn,
.vxe-pager .vxe-pager--end-btn,
.vxe-pager .vxe-pager--prev-btn,
.vxe-pager .vxe-pager--next-btn,
.vxe-pager .vxe-pager--jump,
.vxe-pager .vxe-pager--jump-prev,
.vxe-pager .vxe-pager--jump-next,
.vxe-pager .vxe-pager--count,
.vxe-pager .vxe-pager--sizes {
  margin: 0 0.4em;
  vertical-align: middle;
  display: inline-block;
}
.vxe-pager .vxe-pager--home-btn,
.vxe-pager .vxe-pager--end-btn,
.vxe-pager .vxe-pager--prev-btn,
.vxe-pager .vxe-pager--jump-prev,
.vxe-pager .vxe-pager--num-btn,
.vxe-pager .vxe-pager--jump-next,
.vxe-pager .vxe-pager--next-btn {
  position: relative;
  cursor: pointer;
}
.vxe-pager .vxe-pager--left-wrapper,
.vxe-pager .vxe-pager--right-wrapper,
.vxe-pager .vxe-pager--count,
.vxe-pager .vxe-pager--home-btn,
.vxe-pager .vxe-pager--end-btn,
.vxe-pager .vxe-pager--prev-btn,
.vxe-pager .vxe-pager--next-btn,
.vxe-pager .vxe-pager--jump-prev,
.vxe-pager .vxe-pager--num-btn,
.vxe-pager .vxe-pager--jump-next {
  height: 2.15em;
  line-height: 2em;
  display: inline-block;
}
.vxe-pager .vxe-pager--sizes > .vxe-select--panel .vxe-select-option {
  text-align: center;
}
.vxe-pager .vxe-pager--jump-prev,
.vxe-pager .vxe-pager--home-btn,
.vxe-pager .vxe-pager--end-btn,
.vxe-pager .vxe-pager--prev-btn,
.vxe-pager .vxe-pager--next-btn,
.vxe-pager .vxe-pager--jump-next,
.vxe-pager .vxe-pager--num-btn,
.vxe-pager .vxe-pager--count {
  min-width: 2.15em;
}
.vxe-pager .vxe-pager--btn-wrapper {
  padding: 0;
  margin: 0;
  display: inline-block;
  text-align: center;
}
.vxe-pager .vxe-pager--btn-wrapper .vxe-pager--jump-prev:hover .vxe-pager--jump-more-icon,
.vxe-pager .vxe-pager--btn-wrapper .vxe-pager--jump-next:hover .vxe-pager--jump-more-icon {
  display: none;
}
.vxe-pager .vxe-pager--btn-wrapper .vxe-pager--jump-prev:hover .vxe-pager--jump-icon,
.vxe-pager .vxe-pager--btn-wrapper .vxe-pager--jump-next:hover .vxe-pager--jump-icon {
  display: inline-block;
}
.vxe-pager .vxe-pager--btn-wrapper .vxe-pager--jump-icon {
  display: none;
}
.vxe-pager .vxe-pager--jump-prev,
.vxe-pager .vxe-pager--home-btn,
.vxe-pager .vxe-pager--end-btn,
.vxe-pager .vxe-pager--prev-btn,
.vxe-pager .vxe-pager--next-btn,
.vxe-pager .vxe-pager--jump-next,
.vxe-pager .vxe-pager--num-btn {
  text-align: center;
  border-radius: var(--vxe-ui-base-border-radius);
  margin: 0 0.25em;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  background-color: var(--vxe-ui-layout-background-color);
}
.vxe-pager .vxe-pager--jump-prev:not(.is--disabled):hover,
.vxe-pager .vxe-pager--home-btn:not(.is--disabled):hover,
.vxe-pager .vxe-pager--end-btn:not(.is--disabled):hover,
.vxe-pager .vxe-pager--prev-btn:not(.is--disabled):hover,
.vxe-pager .vxe-pager--next-btn:not(.is--disabled):hover,
.vxe-pager .vxe-pager--jump-next:not(.is--disabled):hover,
.vxe-pager .vxe-pager--num-btn:not(.is--disabled):hover {
  color: var(--vxe-ui-font-primary-lighten-color);
}
.vxe-pager .vxe-pager--jump-prev:not(.is--disabled).is--active, .vxe-pager .vxe-pager--jump-prev:not(.is--disabled):focus,
.vxe-pager .vxe-pager--home-btn:not(.is--disabled).is--active,
.vxe-pager .vxe-pager--home-btn:not(.is--disabled):focus,
.vxe-pager .vxe-pager--end-btn:not(.is--disabled).is--active,
.vxe-pager .vxe-pager--end-btn:not(.is--disabled):focus,
.vxe-pager .vxe-pager--prev-btn:not(.is--disabled).is--active,
.vxe-pager .vxe-pager--prev-btn:not(.is--disabled):focus,
.vxe-pager .vxe-pager--next-btn:not(.is--disabled).is--active,
.vxe-pager .vxe-pager--next-btn:not(.is--disabled):focus,
.vxe-pager .vxe-pager--jump-next:not(.is--disabled).is--active,
.vxe-pager .vxe-pager--jump-next:not(.is--disabled):focus,
.vxe-pager .vxe-pager--num-btn:not(.is--disabled).is--active,
.vxe-pager .vxe-pager--num-btn:not(.is--disabled):focus {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-pager .vxe-pager--jump-prev:not(.is--disabled):active,
.vxe-pager .vxe-pager--home-btn:not(.is--disabled):active,
.vxe-pager .vxe-pager--end-btn:not(.is--disabled):active,
.vxe-pager .vxe-pager--prev-btn:not(.is--disabled):active,
.vxe-pager .vxe-pager--next-btn:not(.is--disabled):active,
.vxe-pager .vxe-pager--jump-next:not(.is--disabled):active,
.vxe-pager .vxe-pager--num-btn:not(.is--disabled):active {
  color: var(--vxe-ui-font-primary-darken-color);
}
.vxe-pager .vxe-pager--jump-prev.is--disabled,
.vxe-pager .vxe-pager--home-btn.is--disabled,
.vxe-pager .vxe-pager--end-btn.is--disabled,
.vxe-pager .vxe-pager--prev-btn.is--disabled,
.vxe-pager .vxe-pager--next-btn.is--disabled,
.vxe-pager .vxe-pager--jump-next.is--disabled,
.vxe-pager .vxe-pager--num-btn.is--disabled {
  cursor: no-drop;
  color: var(--vxe-ui-font-disabled-color);
}
.vxe-pager .vxe-pager--jump-prev.is--disabled:hover,
.vxe-pager .vxe-pager--home-btn.is--disabled:hover,
.vxe-pager .vxe-pager--end-btn.is--disabled:hover,
.vxe-pager .vxe-pager--prev-btn.is--disabled:hover,
.vxe-pager .vxe-pager--next-btn.is--disabled:hover,
.vxe-pager .vxe-pager--jump-next.is--disabled:hover,
.vxe-pager .vxe-pager--num-btn.is--disabled:hover {
  color: var(--vxe-ui-font-disabled-color);
}
.vxe-pager .vxe-pager--num-btn {
  vertical-align: middle;
}
.vxe-pager .vxe-pager--num-btn.is--active {
  font-weight: 700;
}
.vxe-pager .vxe-pager--sizes {
  width: 9em;
  text-align: center;
  cursor: pointer;
}
.vxe-pager .vxe-pager--sizes .vxe-input--inner {
  text-align: center;
}
.vxe-pager .vxe-pager--count {
  text-align: center;
}
.vxe-pager .vxe-pager--count > span {
  vertical-align: middle;
}
.vxe-pager .vxe-pager--count .vxe-pager--separator {
  margin-right: 0.2em;
}
.vxe-pager .vxe-pager--count .vxe-pager--separator:before {
  content: "/";
}
.vxe-pager .vxe-pager--jump .vxe-pager--goto-text {
  margin-right: 0.25em;
}
.vxe-pager .vxe-pager--jump .vxe-pager--classifier-text {
  margin-left: 0.25em;
}
.vxe-pager .vxe-pager--jump .vxe-pager--goto {
  width: 4em;
}

.vxe-pager {
  font-size: var(--vxe-ui-font-size-default);
  height: 48px;
}
.vxe-pager.size--medium {
  font-size: var(--vxe-ui-font-size-medium);
  height: 44px;
}
.vxe-pager.size--small {
  font-size: var(--vxe-ui-font-size-small);
  height: 40px;
}
.vxe-pager.size--mini {
  font-size: var(--vxe-ui-font-size-mini);
  height: 36px;
}