{"version": 3, "sources": ["../../amis/esm/renderers/App.js"], "sourcesContent": ["/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __awaiter, __generator, __decorate, __metadata } from 'tslib';\nimport React from 'react';\nimport { Html, AsideNav, Icon, Layout, NotFound, Spinner } from 'amis-ui';\nimport { isApiOutdated, replaceText, envOverwrite, isEffectiveApi, filter, autobind, ScopedContext, Renderer, AppStore } from 'amis-core';\n\nvar App = /** @class */ (function (_super) {\n    __extends(App, _super);\n    function App(props) {\n        var _this = this;\n        var _a, _b, _c;\n        _this = _super.call(this, props) || this;\n        var store = props.store;\n        store.syncProps(props, undefined, ['pages']);\n        store.updateActivePage(Object.assign({}, (_a = props.env) !== null && _a !== void 0 ? _a : {}, {\n            showFullBreadcrumbPath: (_b = props.showFullBreadcrumbPath) !== null && _b !== void 0 ? _b : false,\n            showBreadcrumbHomePath: (_c = props.showBreadcrumbHomePath) !== null && _c !== void 0 ? _c : true\n        }));\n        if (props.env.watchRouteChange) {\n            _this.unWatchRouteChange = props.env.watchRouteChange(function () {\n                var _a, _b, _c;\n                return store.updateActivePage(Object.assign({}, (_a = props.env) !== null && _a !== void 0 ? _a : {}, {\n                    showFullBreadcrumbPath: (_b = props.showFullBreadcrumbPath) !== null && _b !== void 0 ? _b : false,\n                    showBreadcrumbHomePath: (_c = props.showBreadcrumbHomePath) !== null && _c !== void 0 ? _c : true\n                }));\n            });\n        }\n        return _this;\n    }\n    App.prototype.componentDidMount = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var _a, data, dispatchEvent, rendererEvent;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        _a = this.props, data = _a.data, dispatchEvent = _a.dispatchEvent;\n                        return [4 /*yield*/, dispatchEvent('init', data, this)];\n                    case 1:\n                        rendererEvent = _b.sent();\n                        if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {\n                            return [2 /*return*/];\n                        }\n                        this.reload();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    App.prototype.componentDidUpdate = function (prevProps) {\n        var _a, _b, _c;\n        return __awaiter(this, void 0, void 0, function () {\n            var props, store;\n            return __generator(this, function (_d) {\n                props = this.props;\n                store = props.store;\n                store.syncProps(props, prevProps, ['pages']);\n                if (isApiOutdated(prevProps.api, props.api, prevProps.data, props.data)) {\n                    this.reload();\n                }\n                else if (props.location && props.location !== prevProps.location) {\n                    store.updateActivePage(Object.assign({}, (_a = props.env) !== null && _a !== void 0 ? _a : {}, {\n                        showFullBreadcrumbPath: (_b = props.showFullBreadcrumbPath) !== null && _b !== void 0 ? _b : false,\n                        showBreadcrumbHomePath: (_c = props.showBreadcrumbHomePath) !== null && _c !== void 0 ? _c : true\n                    }));\n                }\n                return [2 /*return*/];\n            });\n        });\n    };\n    App.prototype.componentWillUnmount = function () {\n        var _a;\n        (_a = this.unWatchRouteChange) === null || _a === void 0 ? void 0 : _a.call(this);\n    };\n    App.prototype.reload = function (subpath, query, ctx, silent, replace) {\n        return __awaiter(this, void 0, void 0, function () {\n            var _a, api, store, env, _b, showFullBreadcrumbPath, _c, showBreadcrumbHomePath, locale, json;\n            return __generator(this, function (_d) {\n                switch (_d.label) {\n                    case 0:\n                        if (query) {\n                            return [2 /*return*/, this.receive(query, undefined, replace)];\n                        }\n                        _a = this.props, api = _a.api, store = _a.store, env = _a.env, _b = _a.showFullBreadcrumbPath, showFullBreadcrumbPath = _b === void 0 ? false : _b, _c = _a.showBreadcrumbHomePath, showBreadcrumbHomePath = _c === void 0 ? true : _c, locale = _a.locale;\n                        if (!isEffectiveApi(api, store.data)) return [3 /*break*/, 2];\n                        return [4 /*yield*/, store.fetchInitData(api, store.data, {})];\n                    case 1:\n                        json = _d.sent();\n                        if (env.replaceText) {\n                            json.data = replaceText(json.data, env.replaceText, env.replaceTextIgnoreKeys);\n                        }\n                        if (json === null || json === void 0 ? void 0 : json.data.pages) {\n                            json.data = envOverwrite(json.data, locale);\n                            store.setPages(json.data.pages);\n                            store.updateActivePage(Object.assign({}, env !== null && env !== void 0 ? env : {}, {\n                                showFullBreadcrumbPath: showFullBreadcrumbPath,\n                                showBreadcrumbHomePath: showBreadcrumbHomePath\n                            }));\n                        }\n                        _d.label = 2;\n                    case 2: return [2 /*return*/, store.data];\n                }\n            });\n        });\n    };\n    App.prototype.receive = function (values, subPath, replace) {\n        return __awaiter(this, void 0, void 0, function () {\n            var store;\n            return __generator(this, function (_a) {\n                store = this.props.store;\n                store.updateData(values, undefined, replace);\n                return [2 /*return*/, this.reload()];\n            });\n        });\n    };\n    /**\n     * 支持页面层定义 definitions，并且优先取页面层的 definitions\n     * @param name\n     * @returns\n     */\n    App.prototype.resolveDefinitions = function (name) {\n        var _a;\n        var _b = this.props, resolveDefinitions = _b.resolveDefinitions, store = _b.store;\n        var definitions = (_a = store.schema) === null || _a === void 0 ? void 0 : _a.definitions;\n        return (definitions === null || definitions === void 0 ? void 0 : definitions[name]) || resolveDefinitions(name);\n    };\n    App.prototype.handleNavClick = function (e) {\n        e.preventDefault();\n        var env = this.props.env;\n        var link = e.currentTarget.getAttribute('href');\n        env.jumpTo(link, undefined, this.props.data);\n    };\n    App.prototype.renderHeader = function () {\n        var _a = this.props, cx = _a.classnames, brandName = _a.brandName, header = _a.header, render = _a.render, store = _a.store, logo = _a.logo, env = _a.env;\n        if (!header && !logo && !brandName) {\n            return null;\n        }\n        return (React.createElement(React.Fragment, null,\n            React.createElement(\"div\", { className: cx('Layout-brandBar') },\n                React.createElement(\"div\", { onClick: store.toggleOffScreen, className: cx('Layout-offScreenBtn') },\n                    React.createElement(\"i\", { className: \"bui-icon iconfont icon-collapse\" })),\n                React.createElement(\"div\", { className: cx('Layout-brand') },\n                    logo && ~logo.indexOf('<svg') ? (React.createElement(Html, { className: cx('AppLogo-html'), html: logo, filterHtml: env.filterHtml })) : logo ? (React.createElement(\"img\", { className: cx('AppLogo'), src: logo })) : (React.createElement(\"span\", { className: \"visible-folded \" }, brandName === null || brandName === void 0 ? void 0 : brandName.substring(0, 1))),\n                    React.createElement(\"span\", { className: \"hidden-folded m-l-sm\" }, brandName))),\n            React.createElement(\"div\", { className: cx('Layout-headerBar') },\n                React.createElement(\"a\", { onClick: store.toggleFolded, type: \"button\", className: cx('AppFoldBtn') },\n                    React.createElement(\"i\", { className: \"fa fa-\".concat(store.folded ? 'indent' : 'dedent', \" fa-fw\") })),\n                header ? render('header', header) : null)));\n    };\n    App.prototype.renderAside = function () {\n        var _this = this;\n        var _a = this.props, store = _a.store, env = _a.env, asideBefore = _a.asideBefore, asideAfter = _a.asideAfter, render = _a.render, data = _a.data;\n        return (React.createElement(React.Fragment, null,\n            asideBefore ? render('aside-before', asideBefore) : null,\n            React.createElement(AsideNav, { navigations: store.navigations, renderLink: function (_a, key) {\n                    var link = _a.link, active = _a.active, toggleExpand = _a.toggleExpand, cx = _a.classnames, depth = _a.depth, subHeader = _a.subHeader;\n                    var children = [];\n                    if (link.visible === false) {\n                        return null;\n                    }\n                    if (!subHeader &&\n                        link.children &&\n                        link.children.some(function (item) { return item === null || item === void 0 ? void 0 : item.visible; })) {\n                        children.push(React.createElement(\"span\", { key: \"expand-toggle\", className: cx('AsideNav-itemArrow'), onClick: function (e) { return toggleExpand(link, e); } }));\n                    }\n                    var badge = typeof link.badge === 'string'\n                        ? filter(link.badge, data)\n                        : link.badge;\n                    badge != null &&\n                        children.push(React.createElement(\"b\", { key: \"badge\", className: cx(\"AsideNav-itemBadge\", link.badgeClassName || 'bg-info') }, badge));\n                    if (!subHeader && link.icon) {\n                        children.push(React.createElement(Icon, { key: \"icon\", cx: cx, icon: link.icon, className: \"AsideNav-itemIcon\" }));\n                    }\n                    else if (store.folded && depth === 1 && !subHeader) {\n                        children.push(React.createElement(\"i\", { key: \"icon\", className: cx(\"AsideNav-itemIcon\", link.children ? 'fa fa-folder' : 'fa fa-info') }));\n                    }\n                    children.push(React.createElement(\"span\", { className: cx('AsideNav-itemLabel'), key: \"label\" }, typeof link.label === 'string'\n                        ? filter(link.label, data)\n                        : link.label));\n                    return link.path ? (/^https?\\:/.test(link.path) ? (React.createElement(\"a\", { target: \"_blank\", key: \"link\", href: link.path, rel: \"noopener\" }, children)) : (React.createElement(\"a\", { key: \"link\", onClick: _this.handleNavClick, href: link.path || (link.children && link.children[0].path) }, children))) : (React.createElement(\"a\", { key: \"link\", onClick: link.children ? function () { return toggleExpand(link); } : undefined }, children));\n                }, isActive: function (link) { return !!env.isCurrentUrl(link === null || link === void 0 ? void 0 : link.path, link); } }),\n            asideAfter ? render('aside-before', asideAfter) : null));\n    };\n    App.prototype.renderFooter = function () {\n        var _a = this.props, render = _a.render, footer = _a.footer;\n        return footer ? render('footer', footer) : null;\n    };\n    App.prototype.render = function () {\n        var _this = this;\n        var _a;\n        var _b = this.props, cx = _b.classnames, store = _b.store, render = _b.render, _c = _b.showBreadcrumb, showBreadcrumb = _c === void 0 ? true : _c, loadingConfig = _b.loadingConfig;\n        return (React.createElement(Layout, { header: this.renderHeader(), aside: this.renderAside(), footer: this.renderFooter(), folded: store.folded, offScreen: store.offScreen, contentClassName: cx('AppContent') },\n            store.activePage && store.schema ? (React.createElement(React.Fragment, null,\n                showBreadcrumb && store.bcn.length ? (React.createElement(\"ul\", { className: cx('AppBcn') }, store.bcn.map(function (item, index) {\n                    return (React.createElement(\"li\", { key: index, className: cx('AppBcn-item') }, item.path ? (React.createElement(\"a\", { href: item.path, onClick: _this.handleNavClick }, item.label)) : index !== store.bcn.length - 1 ? (React.createElement(\"a\", null, item.label)) : (item.label)));\n                }))) : null,\n                React.createElement(\"div\", { className: cx('AppBody') }, render('page', store.schema, {\n                    key: \"\".concat((_a = store.activePage) === null || _a === void 0 ? void 0 : _a.id, \"-\").concat(store.schemaKey),\n                    data: store.pageData,\n                    resolveDefinitions: this.resolveDefinitions\n                })))) : store.pages && !store.activePage ? (React.createElement(NotFound, null,\n                React.createElement(\"div\", { className: \"text-center\" }, \"\\u9875\\u9762\\u4E0D\\u5B58\\u5728\"))) : null,\n            React.createElement(Spinner, { loadingConfig: loadingConfig, overlay: true, show: store.loading || !store.pages, size: \"lg\" })));\n    };\n    App.propsList = [\n        'brandName',\n        'logo',\n        'header',\n        'asideBefore',\n        'asideAfter',\n        'pages',\n        'footer'\n    ];\n    App.defaultProps = {};\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [String]),\n        __metadata(\"design:returntype\", void 0)\n    ], App.prototype, \"resolveDefinitions\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], App.prototype, \"handleNavClick\", null);\n    return App;\n}(React.Component));\nvar AppRenderer = /** @class */ (function (_super) {\n    __extends(AppRenderer, _super);\n    function AppRenderer(props, context) {\n        var _this = _super.call(this, props) || this;\n        var scoped = context;\n        scoped.registerComponent(_this);\n        return _this;\n    }\n    AppRenderer.prototype.componentWillUnmount = function () {\n        var scoped = this.context;\n        scoped.unRegisterComponent(this);\n        _super.prototype.componentWillUnmount.call(this);\n    };\n    AppRenderer.prototype.setData = function (values, replace) {\n        return this.props.store.updateData(values, undefined, replace);\n    };\n    AppRenderer.prototype.getData = function () {\n        var store = this.props.store;\n        return store.data;\n    };\n    AppRenderer.contextType = ScopedContext;\n    AppRenderer = __decorate([\n        Renderer({\n            type: 'app',\n            storeType: AppStore.name\n        }),\n        __metadata(\"design:paramtypes\", [Object, Object])\n    ], AppRenderer);\n    return AppRenderer;\n}(App));\n\nexport { App, AppRenderer as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,mBAAkB;AAIlB,IAAI;AAAA;AAAA,EAAqB,SAAU,QAAQ;AACvC,cAAUA,MAAK,MAAM;AACrB,aAASA,KAAI,OAAO;AAChB,UAAI,QAAQ;AACZ,UAAI,IAAI,IAAI;AACZ,cAAQ,OAAO,KAAK,MAAM,KAAK,KAAK;AACpC,UAAI,QAAQ,MAAM;AAClB,YAAM,UAAU,OAAO,QAAW,CAAC,OAAO,CAAC;AAC3C,YAAM,iBAAiB,OAAO,OAAO,CAAC,IAAI,KAAK,MAAM,SAAS,QAAQ,OAAO,SAAS,KAAK,CAAC,GAAG;AAAA,QAC3F,yBAAyB,KAAK,MAAM,4BAA4B,QAAQ,OAAO,SAAS,KAAK;AAAA,QAC7F,yBAAyB,KAAK,MAAM,4BAA4B,QAAQ,OAAO,SAAS,KAAK;AAAA,MACjG,CAAC,CAAC;AACF,UAAI,MAAM,IAAI,kBAAkB;AAC5B,cAAM,qBAAqB,MAAM,IAAI,iBAAiB,WAAY;AAC9D,cAAIC,KAAIC,KAAIC;AACZ,iBAAO,MAAM,iBAAiB,OAAO,OAAO,CAAC,IAAIF,MAAK,MAAM,SAAS,QAAQA,QAAO,SAASA,MAAK,CAAC,GAAG;AAAA,YAClG,yBAAyBC,MAAK,MAAM,4BAA4B,QAAQA,QAAO,SAASA,MAAK;AAAA,YAC7F,yBAAyBC,MAAK,MAAM,4BAA4B,QAAQA,QAAO,SAASA,MAAK;AAAA,UACjG,CAAC,CAAC;AAAA,QACN,CAAC;AAAA,MACL;AACA,aAAO;AAAA,IACX;AACA,IAAAH,KAAI,UAAU,oBAAoB,WAAY;AAC1C,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,IAAI,MAAM,eAAe;AAC7B,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,mBAAK,KAAK,OAAO,OAAO,GAAG,MAAM,gBAAgB,GAAG;AACpD,qBAAO,CAAC,GAAa,cAAc,QAAQ,MAAM,IAAI,CAAC;AAAA,YAC1D,KAAK;AACD,8BAAgB,GAAG,KAAK;AACxB,kBAAI,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,WAAW;AACvF,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,mBAAK,OAAO;AACZ,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,KAAI,UAAU,qBAAqB,SAAU,WAAW;AACpD,UAAI,IAAI,IAAI;AACZ,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,OAAO;AACX,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,KAAK;AACb,kBAAQ,MAAM;AACd,gBAAM,UAAU,OAAO,WAAW,CAAC,OAAO,CAAC;AAC3C,cAAI,cAAc,UAAU,KAAK,MAAM,KAAK,UAAU,MAAM,MAAM,IAAI,GAAG;AACrE,iBAAK,OAAO;AAAA,UAChB,WACS,MAAM,YAAY,MAAM,aAAa,UAAU,UAAU;AAC9D,kBAAM,iBAAiB,OAAO,OAAO,CAAC,IAAI,KAAK,MAAM,SAAS,QAAQ,OAAO,SAAS,KAAK,CAAC,GAAG;AAAA,cAC3F,yBAAyB,KAAK,MAAM,4BAA4B,QAAQ,OAAO,SAAS,KAAK;AAAA,cAC7F,yBAAyB,KAAK,MAAM,4BAA4B,QAAQ,OAAO,SAAS,KAAK;AAAA,YACjG,CAAC,CAAC;AAAA,UACN;AACA,iBAAO;AAAA,YAAC;AAAA;AAAA,UAAY;AAAA,QACxB,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,KAAI,UAAU,uBAAuB,WAAY;AAC7C,UAAI;AACJ,OAAC,KAAK,KAAK,wBAAwB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI;AAAA,IACpF;AACA,IAAAA,KAAI,UAAU,SAAS,SAAU,SAAS,OAAO,KAAK,QAAQ,SAAS;AACnE,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,IAAI,KAAK,OAAO,KAAK,IAAI,wBAAwB,IAAI,wBAAwB,QAAQ;AACzF,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,OAAO;AACP,uBAAO,CAAC,GAAc,KAAK,QAAQ,OAAO,QAAW,OAAO,CAAC;AAAA,cACjE;AACA,mBAAK,KAAK,OAAO,MAAM,GAAG,KAAK,QAAQ,GAAG,OAAO,MAAM,GAAG,KAAK,KAAK,GAAG,wBAAwB,yBAAyB,OAAO,SAAS,QAAQ,IAAI,KAAK,GAAG,wBAAwB,yBAAyB,OAAO,SAAS,OAAO,IAAI,SAAS,GAAG;AACpP,kBAAI,CAAC,eAAe,KAAK,MAAM,IAAI;AAAG,uBAAO,CAAC,GAAa,CAAC;AAC5D,qBAAO,CAAC,GAAa,MAAM,cAAc,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAAA,YACjE,KAAK;AACD,qBAAO,GAAG,KAAK;AACf,kBAAI,IAAI,aAAa;AACjB,qBAAK,OAAO,YAAY,KAAK,MAAM,IAAI,aAAa,IAAI,qBAAqB;AAAA,cACjF;AACA,kBAAI,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,KAAK,OAAO;AAC7D,qBAAK,OAAO,aAAa,KAAK,MAAM,MAAM;AAC1C,sBAAM,SAAS,KAAK,KAAK,KAAK;AAC9B,sBAAM,iBAAiB,OAAO,OAAO,CAAC,GAAG,QAAQ,QAAQ,QAAQ,SAAS,MAAM,CAAC,GAAG;AAAA,kBAChF;AAAA,kBACA;AAAA,gBACJ,CAAC,CAAC;AAAA,cACN;AACA,iBAAG,QAAQ;AAAA,YACf,KAAK;AAAG,qBAAO,CAAC,GAAc,MAAM,IAAI;AAAA,UAC5C;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,KAAI,UAAU,UAAU,SAAU,QAAQ,SAAS,SAAS;AACxD,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI;AACJ,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,KAAK,MAAM;AACnB,gBAAM,WAAW,QAAQ,QAAW,OAAO;AAC3C,iBAAO,CAAC,GAAc,KAAK,OAAO,CAAC;AAAA,QACvC,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AAMA,IAAAA,KAAI,UAAU,qBAAqB,SAAU,MAAM;AAC/C,UAAI;AACJ,UAAI,KAAK,KAAK,OAAO,qBAAqB,GAAG,oBAAoB,QAAQ,GAAG;AAC5E,UAAI,eAAe,KAAK,MAAM,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC9E,cAAQ,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,IAAI,MAAM,mBAAmB,IAAI;AAAA,IACnH;AACA,IAAAA,KAAI,UAAU,iBAAiB,SAAU,GAAG;AACxC,QAAE,eAAe;AACjB,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,OAAO,EAAE,cAAc,aAAa,MAAM;AAC9C,UAAI,OAAO,MAAM,QAAW,KAAK,MAAM,IAAI;AAAA,IAC/C;AACA,IAAAA,KAAI,UAAU,eAAe,WAAY;AACrC,UAAI,KAAK,KAAK,OAAO,KAAK,GAAG,YAAY,YAAY,GAAG,WAAW,SAAS,GAAG,QAAQ,SAAS,GAAG,QAAQ,QAAQ,GAAG,OAAO,OAAO,GAAG,MAAM,MAAM,GAAG;AACtJ,UAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,WAAW;AAChC,eAAO;AAAA,MACX;AACA,aAAQ,aAAAI,QAAM;AAAA,QAAc,aAAAA,QAAM;AAAA,QAAU;AAAA,QACxC,aAAAA,QAAM;AAAA,UAAc;AAAA,UAAO,EAAE,WAAW,GAAG,iBAAiB,EAAE;AAAA,UAC1D,aAAAA,QAAM;AAAA,YAAc;AAAA,YAAO,EAAE,SAAS,MAAM,iBAAiB,WAAW,GAAG,qBAAqB,EAAE;AAAA,YAC9F,aAAAA,QAAM,cAAc,KAAK,EAAE,WAAW,kCAAkC,CAAC;AAAA,UAAC;AAAA,UAC9E,aAAAA,QAAM;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,GAAG,cAAc,EAAE;AAAA,YACvD,QAAQ,CAAC,KAAK,QAAQ,MAAM,IAAK,aAAAA,QAAM,cAAc,QAAM,EAAE,WAAW,GAAG,cAAc,GAAG,MAAM,MAAM,YAAY,IAAI,WAAW,CAAC,IAAK,OAAQ,aAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,SAAS,GAAG,KAAK,KAAK,CAAC,IAAM,aAAAA,QAAM,cAAc,QAAQ,EAAE,WAAW,kBAAkB,GAAG,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,UAAU,GAAG,CAAC,CAAC;AAAA,YACtW,aAAAA,QAAM,cAAc,QAAQ,EAAE,WAAW,uBAAuB,GAAG,SAAS;AAAA,UAAC;AAAA,QAAC;AAAA,QACtF,aAAAA,QAAM;AAAA,UAAc;AAAA,UAAO,EAAE,WAAW,GAAG,kBAAkB,EAAE;AAAA,UAC3D,aAAAA,QAAM;AAAA,YAAc;AAAA,YAAK,EAAE,SAAS,MAAM,cAAc,MAAM,UAAU,WAAW,GAAG,YAAY,EAAE;AAAA,YAChG,aAAAA,QAAM,cAAc,KAAK,EAAE,WAAW,SAAS,OAAO,MAAM,SAAS,WAAW,UAAU,QAAQ,EAAE,CAAC;AAAA,UAAC;AAAA,UAC1G,SAAS,OAAO,UAAU,MAAM,IAAI;AAAA,QAAI;AAAA,MAAC;AAAA,IACrD;AACA,IAAAJ,KAAI,UAAU,cAAc,WAAY;AACpC,UAAI,QAAQ;AACZ,UAAI,KAAK,KAAK,OAAO,QAAQ,GAAG,OAAO,MAAM,GAAG,KAAK,cAAc,GAAG,aAAa,aAAa,GAAG,YAAY,SAAS,GAAG,QAAQ,OAAO,GAAG;AAC7I,aAAQ,aAAAI,QAAM;AAAA,QAAc,aAAAA,QAAM;AAAA,QAAU;AAAA,QACxC,cAAc,OAAO,gBAAgB,WAAW,IAAI;AAAA,QACpD,aAAAA,QAAM,cAAc,YAAU,EAAE,aAAa,MAAM,aAAa,YAAY,SAAUH,KAAI,KAAK;AACvF,cAAI,OAAOA,IAAG,MAAM,SAASA,IAAG,QAAQ,eAAeA,IAAG,cAAc,KAAKA,IAAG,YAAY,QAAQA,IAAG,OAAO,YAAYA,IAAG;AAC7H,cAAI,WAAW,CAAC;AAChB,cAAI,KAAK,YAAY,OAAO;AACxB,mBAAO;AAAA,UACX;AACA,cAAI,CAAC,aACD,KAAK,YACL,KAAK,SAAS,KAAK,SAAU,MAAM;AAAE,mBAAO,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK;AAAA,UAAS,CAAC,GAAG;AAC1G,qBAAS,KAAK,aAAAG,QAAM,cAAc,QAAQ,EAAE,KAAK,iBAAiB,WAAW,GAAG,oBAAoB,GAAG,SAAS,SAAU,GAAG;AAAE,qBAAO,aAAa,MAAM,CAAC;AAAA,YAAG,EAAE,CAAC,CAAC;AAAA,UACrK;AACA,cAAI,QAAQ,OAAO,KAAK,UAAU,WAC5B,OAAO,KAAK,OAAO,IAAI,IACvB,KAAK;AACX,mBAAS,QACL,SAAS,KAAK,aAAAA,QAAM,cAAc,KAAK,EAAE,KAAK,SAAS,WAAW,GAAG,sBAAsB,KAAK,kBAAkB,SAAS,EAAE,GAAG,KAAK,CAAC;AAC1I,cAAI,CAAC,aAAa,KAAK,MAAM;AACzB,qBAAS,KAAK,aAAAA,QAAM,cAAc,MAAM,EAAE,KAAK,QAAQ,IAAQ,MAAM,KAAK,MAAM,WAAW,oBAAoB,CAAC,CAAC;AAAA,UACrH,WACS,MAAM,UAAU,UAAU,KAAK,CAAC,WAAW;AAChD,qBAAS,KAAK,aAAAA,QAAM,cAAc,KAAK,EAAE,KAAK,QAAQ,WAAW,GAAG,qBAAqB,KAAK,WAAW,iBAAiB,YAAY,EAAE,CAAC,CAAC;AAAA,UAC9I;AACA,mBAAS,KAAK,aAAAA,QAAM,cAAc,QAAQ,EAAE,WAAW,GAAG,oBAAoB,GAAG,KAAK,QAAQ,GAAG,OAAO,KAAK,UAAU,WACjH,OAAO,KAAK,OAAO,IAAI,IACvB,KAAK,KAAK,CAAC;AACjB,iBAAO,KAAK,OAAQ,YAAY,KAAK,KAAK,IAAI,IAAK,aAAAA,QAAM,cAAc,KAAK,EAAE,QAAQ,UAAU,KAAK,QAAQ,MAAM,KAAK,MAAM,KAAK,WAAW,GAAG,QAAQ,IAAM,aAAAA,QAAM,cAAc,KAAK,EAAE,KAAK,QAAQ,SAAS,MAAM,gBAAgB,MAAM,KAAK,QAAS,KAAK,YAAY,KAAK,SAAS,CAAC,EAAE,KAAM,GAAG,QAAQ,IAAO,aAAAA,QAAM,cAAc,KAAK,EAAE,KAAK,QAAQ,SAAS,KAAK,WAAW,WAAY;AAAE,mBAAO,aAAa,IAAI;AAAA,UAAG,IAAI,OAAU,GAAG,QAAQ;AAAA,QAC3b,GAAG,UAAU,SAAU,MAAM;AAAE,iBAAO,CAAC,CAAC,IAAI,aAAa,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,MAAM,IAAI;AAAA,QAAG,EAAE,CAAC;AAAA,QAC9H,aAAa,OAAO,gBAAgB,UAAU,IAAI;AAAA,MAAI;AAAA,IAC9D;AACA,IAAAJ,KAAI,UAAU,eAAe,WAAY;AACrC,UAAI,KAAK,KAAK,OAAO,SAAS,GAAG,QAAQ,SAAS,GAAG;AACrD,aAAO,SAAS,OAAO,UAAU,MAAM,IAAI;AAAA,IAC/C;AACA,IAAAA,KAAI,UAAU,SAAS,WAAY;AAC/B,UAAI,QAAQ;AACZ,UAAI;AACJ,UAAI,KAAK,KAAK,OAAO,KAAK,GAAG,YAAY,QAAQ,GAAG,OAAO,SAAS,GAAG,QAAQ,KAAK,GAAG,gBAAgB,iBAAiB,OAAO,SAAS,OAAO,IAAI,gBAAgB,GAAG;AACtK,aAAQ,aAAAI,QAAM;AAAA,QAAc;AAAA,QAAQ,EAAE,QAAQ,KAAK,aAAa,GAAG,OAAO,KAAK,YAAY,GAAG,QAAQ,KAAK,aAAa,GAAG,QAAQ,MAAM,QAAQ,WAAW,MAAM,WAAW,kBAAkB,GAAG,YAAY,EAAE;AAAA,QAC5M,MAAM,cAAc,MAAM,SAAU,aAAAA,QAAM;AAAA,UAAc,aAAAA,QAAM;AAAA,UAAU;AAAA,UACpE,kBAAkB,MAAM,IAAI,SAAU,aAAAA,QAAM,cAAc,MAAM,EAAE,WAAW,GAAG,QAAQ,EAAE,GAAG,MAAM,IAAI,IAAI,SAAU,MAAM,OAAO;AAC9H,mBAAQ,aAAAA,QAAM,cAAc,MAAM,EAAE,KAAK,OAAO,WAAW,GAAG,aAAa,EAAE,GAAG,KAAK,OAAQ,aAAAA,QAAM,cAAc,KAAK,EAAE,MAAM,KAAK,MAAM,SAAS,MAAM,eAAe,GAAG,KAAK,KAAK,IAAK,UAAU,MAAM,IAAI,SAAS,IAAK,aAAAA,QAAM,cAAc,KAAK,MAAM,KAAK,KAAK,IAAM,KAAK,KAAM;AAAA,UACzR,CAAC,CAAC,IAAK;AAAA,UACP,aAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,SAAS,EAAE,GAAG,OAAO,QAAQ,MAAM,QAAQ;AAAA,YAClF,KAAK,GAAG,QAAQ,KAAK,MAAM,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI,GAAG,EAAE,OAAO,MAAM,SAAS;AAAA,YAC9G,MAAM,MAAM;AAAA,YACZ,oBAAoB,KAAK;AAAA,UAC7B,CAAC,CAAC;AAAA,QAAC,IAAK,MAAM,SAAS,CAAC,MAAM,aAAc,aAAAA,QAAM;AAAA,UAAc;AAAA,UAAU;AAAA,UAC1E,aAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,cAAc,GAAG,OAAgC;AAAA,QAAC,IAAK;AAAA,QACnG,aAAAA,QAAM,cAAc,WAAS,EAAE,eAA8B,SAAS,MAAM,MAAM,MAAM,WAAW,CAAC,MAAM,OAAO,MAAM,KAAK,CAAC;AAAA,MAAC;AAAA,IACtI;AACA,IAAAJ,KAAI,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,IAAAA,KAAI,eAAe,CAAC;AACpB,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,KAAI,WAAW,sBAAsB,IAAI;AAC5C,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,KAAI,WAAW,kBAAkB,IAAI;AACxC,WAAOA;AAAA,EACX,EAAE,aAAAI,QAAM,SAAS;AAAA;AACjB,IAAI;AAAA;AAAA,EAA6B,SAAU,QAAQ;AAC/C,cAAUC,cAAa,MAAM;AAC7B,aAASA,aAAY,OAAO,SAAS;AACjC,UAAI,QAAQ,OAAO,KAAK,MAAM,KAAK,KAAK;AACxC,UAAI,SAAS;AACb,aAAO,kBAAkB,KAAK;AAC9B,aAAO;AAAA,IACX;AACA,IAAAA,aAAY,UAAU,uBAAuB,WAAY;AACrD,UAAI,SAAS,KAAK;AAClB,aAAO,oBAAoB,IAAI;AAC/B,aAAO,UAAU,qBAAqB,KAAK,IAAI;AAAA,IACnD;AACA,IAAAA,aAAY,UAAU,UAAU,SAAU,QAAQ,SAAS;AACvD,aAAO,KAAK,MAAM,MAAM,WAAW,QAAQ,QAAW,OAAO;AAAA,IACjE;AACA,IAAAA,aAAY,UAAU,UAAU,WAAY;AACxC,UAAI,QAAQ,KAAK,MAAM;AACvB,aAAO,MAAM;AAAA,IACjB;AACA,IAAAA,aAAY,cAAc;AAC1B,IAAAA,eAAc,WAAW;AAAA,MACrB,SAAS;AAAA,QACL,MAAM;AAAA,QACN,WAAW,SAAS;AAAA,MACxB,CAAC;AAAA,MACD,WAAW,qBAAqB,CAAC,QAAQ,MAAM,CAAC;AAAA,IACpD,GAAGA,YAAW;AACd,WAAOA;AAAA,EACX,EAAE,GAAG;AAAA;", "names": ["App", "_a", "_b", "_c", "React", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}