import {
  LANE_INDENTATION,
  collectLanes,
  computeLanesResize,
  getChildLanes,
  getLanesRoot
} from "./chunk-YUY6GSLW.js";
import "./chunk-RAC5CVLD.js";
import "./chunk-ZYICBXDL.js";
import "./chunk-VRJCCLJE.js";
import "./chunk-7XBT5GDQ.js";
import "./chunk-GFT2G5UO.js";
export {
  LANE_INDENTATION,
  collectLanes,
  computeLanesResize,
  getChildLanes,
  getLanesRoot
};
//# sourceMappingURL=bpmn-js_lib_features_modeling_util_LaneUtil.js.map
