Object.defineProperty(exports,"__esModule",{value:!0}),exports.ColumnInfo=void 0;var _xeUtils=_interopRequireDefault(require("xe-utils")),_ui=require("../../ui"),_util=require("./util"),_utils=require("../../ui/src/utils"),_log=require("../../ui/src/log");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let{getI18n,formats}=_ui.VxeUI;class ColumnInfo{constructor(e,r,{renderHeader:t,renderCell:l,renderFooter:o,renderData:i}={}){var d=e.props,n=e.xeGrid,s=r.formatter,a=!_xeUtils.default.isBoolean(r.visible)||r.visible,u=["seq","checkbox","radio","expand","html"];r.type&&-1===u.indexOf(r.type)&&(0,_log.warnLog)("vxe.error.errProp",["type="+r.type,u.join(", ")]),(_xeUtils.default.isBoolean(r.cellRender)||r.cellRender&&!_xeUtils.default.isObject(r.cellRender))&&(0,_log.warnLog)("vxe.error.errProp",["column.cell-render="+r.cellRender,"column.cell-render={}"]),(_xeUtils.default.isBoolean(r.editRender)||r.editRender&&!_xeUtils.default.isObject(r.editRender))&&(0,_log.warnLog)("vxe.error.errProp",["column.edit-render="+r.editRender,"column.edit-render={}"]),"expand"===r.type&&(u=d.treeConfig,d=e.getComputeMaps().computeTreeOpts,e=d.value,u)&&(e.showLine||e.line)&&(0,_log.errLog)("vxe.error.errConflicts",["tree-config.showLine","column.type=expand"]),s&&(_xeUtils.default.isString(s)?(d=formats.get(s)||_xeUtils.default[s])&&_xeUtils.default.isFunction(d.tableCellFormatMethod||d.cellFormatMethod)||(0,_log.errLog)("vxe.error.notFormats",[s]):!_xeUtils.default.isArray(s)||(u=formats.get(s[0])||_xeUtils.default[s[0]])&&_xeUtils.default.isFunction(u.tableCellFormatMethod||u.cellFormatMethod)||(0,_log.errLog)("vxe.error.notFormats",[s[0]])),Object.assign(this,{type:r.type,property:r.field,field:r.field,title:r.title,width:r.width,minWidth:r.minWidth,maxWidth:r.maxWidth,resizable:r.resizable,fixed:r.fixed,align:r.align,headerAlign:r.headerAlign,footerAlign:r.footerAlign,showOverflow:r.showOverflow,showHeaderOverflow:r.showHeaderOverflow,showFooterOverflow:r.showFooterOverflow,className:r.className,headerClassName:r.headerClassName,footerClassName:r.footerClassName,formatter:s,footerFormatter:r.footerFormatter,padding:r.padding,verticalAlign:r.verticalAlign,sortable:r.sortable,sortBy:r.sortBy,sortType:r.sortType,filters:(0,_util.toFilters)(r.filters),filterMultiple:!_xeUtils.default.isBoolean(r.filterMultiple)||r.filterMultiple,filterMethod:r.filterMethod,filterResetMethod:r.filterResetMethod,filterRecoverMethod:r.filterRecoverMethod,filterRender:r.filterRender,rowGroupNode:r.rowGroupNode,treeNode:r.treeNode,dragSort:r.dragSort,rowResize:r.rowResize,cellType:r.cellType,cellRender:r.cellRender,editRender:r.editRender,contentRender:r.contentRender,headerExportMethod:r.headerExportMethod,exportMethod:r.exportMethod,footerExportMethod:r.footerExportMethod,titleHelp:r.titleHelp,titlePrefix:r.titlePrefix,titleSuffix:r.titleSuffix,aggFunc:r.aggFunc,params:r.params,id:r.colId||_xeUtils.default.uniqueId("col_"),parentId:null,visible:a,halfVisible:!1,defaultVisible:a,defaultFixed:r.fixed,checked:!1,halfChecked:!1,disabled:!1,level:1,rowSpan:1,colSpan:1,order:null,sortTime:0,sortNumber:0,renderSortNumber:0,renderAggFn:"",renderFixed:"",renderVisible:!1,renderWidth:0,renderHeight:0,renderResizeWidth:0,renderAutoWidth:0,resizeWidth:0,renderLeft:0,renderArgs:[],model:{},renderHeader:t||r.renderHeader,renderCell:l||r.renderCell,renderFooter:o||r.renderFooter,renderData:i,slots:r.slots}),n&&(e=n.getComputeMaps().computeProxyOpts,(d=e.value).beforeColumn)&&d.beforeColumn({$grid:n,column:this})}getTitle(){return(0,_utils.getFuncText)(this.title||("seq"===this.type?getI18n("vxe.table.seqTitle"):""))}getKey(){var e=this.type;return this.field||(e?"type="+e:null)}update(e,r){"filters"!==e&&("field"===e&&(this.property=r),this[e]=r)}}exports.ColumnInfo=ColumnInfo;