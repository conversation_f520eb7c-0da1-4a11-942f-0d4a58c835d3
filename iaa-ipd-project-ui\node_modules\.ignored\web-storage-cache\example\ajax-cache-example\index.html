<!DOCTYPE html>
<html>
    <head>
        <title>AJAX Cache Example</title>
    </head>
    <body>

        <div>
            <p>Name:<span id="name"></span></p>
            <p>Orgnization:<span id="org"></span></p>
        </div>

        <script type="text/javascript" src="js/jquery-2.1.3.min.js"></script>
        <script type="text/javascript" src="js/web-storage-cache.min.js"></script>
        <script type="text/javascript" src="js/ajax-cache.js"></script>
        <script type="text/javascript">
            $(function() {
                $.ajax({
                    url: 'data.json',
                    wsCache: true,
                    success: function(data){
                        $('#name').html(data.LastName + data.FirstName );
                        $('#org').html(data.Organization);
                    }
                });
            });
        </script>
    </body>
</html>
