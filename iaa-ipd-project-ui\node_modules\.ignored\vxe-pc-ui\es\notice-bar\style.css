.vxe-notice-bar {
  display: flex;
  flex-direction: row;
  font-size: var(--vxe-ui-font-size-default);
  color: var(--vxe-ui-font-color);
  font-family: var(--vxe-ui-font-family);
}

.vxe-notice-bar--prefix,
.vxe-notice-bar--suffix {
  flex-shrink: 0;
}

.vxe-notice-bar--content {
  flex-grow: 1;
  overflow: hidden;
  padding: 0 0.2em;
}

.vxe-notice-bar--inner {
  overflow: hidden;
}

.vxe-notice-bar--wrapper {
  display: inline-block;
  white-space: nowrap;
}

.vxe-notice-bar.is--horizontal.dir--left .vxe-notice-bar--wrapper {
  padding-left: 100%;
  animation: 15s linear 0s infinite normal none running scrollLeftText;
}
.vxe-notice-bar.is--horizontal.dir--right .vxe-notice-bar--wrapper {
  padding-left: 100%;
  animation: 15s linear 0s infinite normal none running scrollRightText;
}
.vxe-notice-bar.is--horizontal .vxe-notice-bar--wrapper:hover {
  animation-play-state: paused;
}

@keyframes scrollRightText {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(0);
  }
}
@keyframes scrollLeftText {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}
.vxe-notice-bar.size--medium {
  font-size: var(--vxe-ui-font-size-medium);
}
.vxe-notice-bar.size--small {
  font-size: var(--vxe-ui-font-size-small);
}
.vxe-notice-bar.size--mini {
  font-size: var(--vxe-ui-font-size-mini);
}