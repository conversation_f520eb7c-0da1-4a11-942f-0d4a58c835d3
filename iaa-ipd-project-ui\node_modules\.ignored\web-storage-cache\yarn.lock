# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


abbrev@1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/abbrev/download/abbrev-1.1.1.tgz#f8f2c887ad10bf67f634f005b6987fed3179aac8"

ajv@^6.5.5:
  version "6.10.0"
  resolved "https://registry.npm.taobao.org/ajv/download/ajv-6.10.0.tgz#90d0d54439da587cd7e843bfb7045f50bd22bdf1"
  dependencies:
    fast-deep-equal "^2.0.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

amdefine@>=0.0.4:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/amdefine/download/amdefine-1.0.1.tgz#4a5282ac164729e93619bcfd3ad151f817ce91f5"

ansi-colors@3.2.3:
  version "3.2.3"
  resolved "https://registry.npm.taobao.org/ansi-colors/download/ansi-colors-3.2.3.tgz#57d35b8686e851e2cc04c403f1c00203976a1813"

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"

ansi-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-3.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fansi-regex%2Fdownload%2Fansi-regex-3.0.0.tgz#ed0317c322064f79466c02966bddb605ab37d998"

ansi-regex@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npm.taobao.org/ansi-regex/download/ansi-regex-4.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fansi-regex%2Fdownload%2Fansi-regex-4.1.0.tgz#8b9f8f08cf1acb843756a839ca8c7e3168c51997"

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npm.taobao.org/ansi-styles/download/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://registry.npm.taobao.org/ansi-styles/download/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  dependencies:
    color-convert "^1.9.0"

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry.npm.taobao.org/argparse/download/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  dependencies:
    sprintf-js "~1.0.2"

array-find-index@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/array-find-index/download/array-find-index-1.0.2.tgz#df010aa1287e164bbda6f9723b0a96a1ec4187a1"

asn1@~0.2.3:
  version "0.2.4"
  resolved "https://registry.npm.taobao.org/asn1/download/asn1-0.2.4.tgz#8d2475dfab553bb33e77b54e59e880bb8ce23136"
  dependencies:
    safer-buffer "~2.1.0"

assert-plus@1.0.0, assert-plus@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/assert-plus/download/assert-plus-1.0.0.tgz#f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525"

assertion-error@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/assertion-error/download/assertion-error-1.1.0.tgz#e60b6b0e8f301bd97e5375215bda406c85118c0b"

async@~0.2.6:
  version "0.2.10"
  resolved "https://registry.npm.taobao.org/async/download/async-0.2.10.tgz#b6bbe0b0674b9d719708ca38de8c237cb526c3d1"

async@~1.5.2:
  version "1.5.2"
  resolved "https://registry.npm.taobao.org/async/download/async-1.5.2.tgz#ec6a61ae56480c0c3cb241c95618e20892f9672a"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npm.taobao.org/asynckit/download/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "https://registry.npm.taobao.org/aws-sign2/download/aws-sign2-0.7.0.tgz#b46e890934a9591f2d2f6f86d7e6a9f1b3fe76a8"

aws4@^1.8.0:
  version "1.8.0"
  resolved "https://registry.npm.taobao.org/aws4/download/aws4-1.8.0.tgz#f0e003d9ca9e7f59c7a508945d7b2ef9a04a542f"

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/balanced-match/download/balanced-match-1.0.0.tgz#89b4d199ab2bee49de164ea02b89ce462d71b767"

bcrypt-pbkdf@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz#a4301d389b6a43f9b67ff3ca11a3f6637e360e9e"
  dependencies:
    tweetnacl "^0.14.3"

brace-expansion@^1.0.0, brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.npm.taobao.org/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

browser-stdout@1.3.1:
  version "1.3.1"
  resolved "https://registry.npm.taobao.org/browser-stdout/download/browser-stdout-1.3.1.tgz#baa559ee14ced73452229bad7326467c61fabd60"

browserify-zlib@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npm.taobao.org/browserify-zlib/download/browserify-zlib-0.1.4.tgz#bb35f8a519f600e0fa6b8485241c979d0141fb2d"
  dependencies:
    pako "~0.2.0"

buffer-from@^1.0.0:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/buffer-from/download/buffer-from-1.1.1.tgz#32713bc028f75c02fdb710d7c7bcec1f2c6070ef"

camelcase-keys@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/camelcase-keys/download/camelcase-keys-2.1.0.tgz#308beeaffdf28119051efa1d932213c91b8f92e7"
  dependencies:
    camelcase "^2.0.0"
    map-obj "^1.0.0"

camelcase@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/camelcase/download/camelcase-2.1.1.tgz#7c1d16d679a1bbe59ca02cacecfb011e201f5a1f"

camelcase@^5.0.0:
  version "5.3.1"
  resolved "https://registry.npm.taobao.org/camelcase/download/camelcase-5.3.1.tgz#e3c9b31569e106811df242f715725a1f4c494320"

caseless@~0.12.0:
  version "0.12.0"
  resolved "https://registry.npm.taobao.org/caseless/download/caseless-0.12.0.tgz#1b681c21ff84033c826543090689420d187151dc"

chai@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npm.taobao.org/chai/download/chai-4.2.0.tgz#760aa72cf20e3795e84b12877ce0e83737aa29e5"
  dependencies:
    assertion-error "^1.1.0"
    check-error "^1.0.2"
    deep-eql "^3.0.1"
    get-func-name "^2.0.0"
    pathval "^1.1.0"
    type-detect "^4.0.5"

chalk@^1.0.0:
  version "1.1.3"
  resolved "https://registry.npm.taobao.org/chalk/download/chalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^2.0.1, chalk@~2.4.1:
  version "2.4.2"
  resolved "https://registry.npm.taobao.org/chalk/download/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

check-error@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/check-error/download/check-error-1.0.2.tgz#574d312edd88bb5dd8912e9286dd6c0aed4aac82"

cli@0.6.x:
  version "0.6.6"
  resolved "https://registry.npm.taobao.org/cli/download/cli-0.6.6.tgz#02ad44a380abf27adac5e6f0cdd7b043d74c53e3"
  dependencies:
    exit "0.1.2"
    glob "~ 3.2.1"

cliui@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npm.taobao.org/cliui/download/cliui-4.1.0.tgz#348422dbe82d800b3022eef4f6ac10bf2e4d1b49"
  dependencies:
    string-width "^2.1.1"
    strip-ansi "^4.0.0"
    wrap-ansi "^2.0.0"

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/code-point-at/download/code-point-at-1.1.0.tgz#0d070b4d043a5bea33a2f1a40e2edb3d9a4ccf77"

coffeescript@~1.10.0:
  version "1.10.0"
  resolved "https://registry.npm.taobao.org/coffeescript/download/coffeescript-1.10.0.tgz#e7aa8301917ef621b35d8a39f348dcdd1db7e33e"

color-convert@^1.9.0:
  version "1.9.3"
  resolved "https://registry.npm.taobao.org/color-convert/download/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  dependencies:
    color-name "1.1.3"

color-name@1.1.3:
  version "1.1.3"
  resolved "https://registry.npm.taobao.org/color-name/download/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"

colors@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/colors/download/colors-1.1.2.tgz#168a4701756b6a7f51a12ce0c97bfa28c084ed63"

combined-stream@^1.0.6, combined-stream@~1.0.6:
  version "1.0.8"
  resolved "https://registry.npm.taobao.org/combined-stream/download/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  dependencies:
    delayed-stream "~1.0.0"

commander@2.15.1:
  version "2.15.1"
  resolved "https://registry.npm.taobao.org/commander/download/commander-2.15.1.tgz#df46e867d0fc2aec66a34662b406a9ccafff5b0f"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.npm.taobao.org/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"

concat-stream@1.6.2, concat-stream@^1.4.1:
  version "1.6.2"
  resolved "https://registry.npm.taobao.org/concat-stream/download/concat-stream-1.6.2.tgz#904bdf194cd3122fc675c77fc4ac3d4ff0fd1a34"
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

console-browserify@1.1.x:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/console-browserify/download/console-browserify-1.1.0.tgz#f0241c45730a9fc6323b206dbf38edc741d0bb10"
  dependencies:
    date-now "^0.1.4"

core-util-is@1.0.2, core-util-is@~1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/core-util-is/download/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"

cross-spawn@^6.0.0:
  version "6.0.5"
  resolved "https://registry.npm.taobao.org/cross-spawn/download/cross-spawn-6.0.5.tgz#4a5ec7c64dfae22c3a14124dbacdee846d80cbc4"
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

currently-unhandled@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npm.taobao.org/currently-unhandled/download/currently-unhandled-0.4.1.tgz#988df33feab191ef799a61369dd76c17adf957ea"
  dependencies:
    array-find-index "^1.0.1"

dashdash@^1.12.0:
  version "1.14.1"
  resolved "https://registry.npm.taobao.org/dashdash/download/dashdash-1.14.1.tgz#853cfa0f7cbe2fed5de20326b8dd581035f6e2f0"
  dependencies:
    assert-plus "^1.0.0"

date-now@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npm.taobao.org/date-now/download/date-now-0.1.4.tgz#eaf439fd4d4848ad74e5cc7dbef200672b9e345b"

dateformat@~1.0.12:
  version "1.0.12"
  resolved "https://registry.npm.taobao.org/dateformat/download/dateformat-1.0.12.tgz#9f124b67594c937ff706932e4a642cca8dbbfee9"
  dependencies:
    get-stdin "^4.0.1"
    meow "^3.3.0"

debug@2.6.9:
  version "2.6.9"
  resolved "https://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  dependencies:
    ms "2.0.0"

debug@3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/debug/download/debug-3.1.0.tgz#5bb5a0672628b64149566ba16819e61518c67261"
  dependencies:
    ms "2.0.0"

debug@3.2.6:
  version "3.2.6"
  resolved "https://registry.npm.taobao.org/debug/download/debug-3.2.6.tgz#e83d17de16d8a7efb7717edbe5fb10135eee629b"
  dependencies:
    ms "^2.1.1"

debug@~0.7.0:
  version "0.7.4"
  resolved "https://registry.npm.taobao.org/debug/download/debug-0.7.4.tgz#06e1ea8082c2cb14e39806e22e2f6f757f92af39"

decamelize@^1.1.2, decamelize@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/decamelize/download/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"

deep-eql@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npm.taobao.org/deep-eql/download/deep-eql-3.0.1.tgz#dfc9404400ad1c8fe023e7da1df1c147c4b444df"
  dependencies:
    type-detect "^4.0.0"

define-properties@^1.1.2:
  version "1.1.3"
  resolved "https://registry.npm.taobao.org/define-properties/download/define-properties-1.1.3.tgz#cf88da6cbee26fe6db7094f61d870cbd84cee9f1"
  dependencies:
    object-keys "^1.0.12"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/delayed-stream/download/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"

diff@3.5.0:
  version "3.5.0"
  resolved "https://registry.npm.taobao.org/diff/download/diff-3.5.0.tgz#800c0dd1e0a8bfbc95835c202ad220fe317e5a12"

dom-serializer@0:
  version "0.1.1"
  resolved "https://registry.npm.taobao.org/dom-serializer/download/dom-serializer-0.1.1.tgz#1ec4059e284babed36eec2941d4a970a189ce7c0"
  dependencies:
    domelementtype "^1.3.0"
    entities "^1.1.1"

domelementtype@1, domelementtype@^1.3.0:
  version "1.3.1"
  resolved "https://registry.npm.taobao.org/domelementtype/download/domelementtype-1.3.1.tgz#d048c44b37b0d10a7f2a3d5fee3f4333d790481f"

domhandler@2.3:
  version "2.3.0"
  resolved "https://registry.npm.taobao.org/domhandler/download/domhandler-2.3.0.tgz#2de59a0822d5027fabff6f032c2b25a2a8abe738"
  dependencies:
    domelementtype "1"

domutils@1.5:
  version "1.5.1"
  resolved "https://registry.npm.taobao.org/domutils/download/domutils-1.5.1.tgz#dcd8488a26f563d61079e48c9f7b7e32373682cf"
  dependencies:
    dom-serializer "0"
    domelementtype "1"

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "https://registry.npm.taobao.org/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz#3a83a904e54353287874c564b7549386849a98c9"
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

emoji-regex@^7.0.1:
  version "7.0.3"
  resolved "https://registry.npm.taobao.org/emoji-regex/download/emoji-regex-7.0.3.tgz#933a04052860c85e83c122479c4748a8e4c72156"

end-of-stream@^1.1.0:
  version "1.4.1"
  resolved "https://registry.npm.taobao.org/end-of-stream/download/end-of-stream-1.4.1.tgz#ed29634d19baba463b6ce6b80a37213eab71ec43"
  dependencies:
    once "^1.4.0"

entities@1.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/entities/download/entities-1.0.0.tgz#b2987aa3821347fcde642b24fdfc9e4fb712bf26"

entities@^1.1.1:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/entities/download/entities-1.1.2.tgz#bdfa735299664dfafd34529ed4f8522a275fea56"

error-ex@^1.2.0:
  version "1.3.2"
  resolved "https://registry.npm.taobao.org/error-ex/download/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  dependencies:
    is-arrayish "^0.2.1"

es-abstract@^1.5.1:
  version "1.13.0"
  resolved "https://registry.npm.taobao.org/es-abstract/download/es-abstract-1.13.0.tgz#ac86145fdd5099d8dd49558ccba2eaf9b88e24e9"
  dependencies:
    es-to-primitive "^1.2.0"
    function-bind "^1.1.1"
    has "^1.0.3"
    is-callable "^1.1.4"
    is-regex "^1.0.4"
    object-keys "^1.0.12"

es-to-primitive@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/es-to-primitive/download/es-to-primitive-1.2.0.tgz#edf72478033456e8dda8ef09e00ad9650707f377"
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

es6-promise@^4.0.3:
  version "4.2.6"
  resolved "https://registry.npm.taobao.org/es6-promise/download/es6-promise-4.2.6.tgz#b685edd8258886365ea62b57d30de28fadcd974f"

escape-string-regexp@1.0.5, escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npm.taobao.org/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"

esprima@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/esprima/download/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"

eventemitter2@^0.4.9, eventemitter2@~0.4.13:
  version "0.4.14"
  resolved "https://registry.npm.taobao.org/eventemitter2/download/eventemitter2-0.4.14.tgz#8f61b75cde012b2e9eb284d4545583b5643b61ab"

execa@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/execa/download/execa-1.0.0.tgz#c6236a5bb4df6d6f15e88e7f017798216749ddd8"
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

exit@0.1.2, exit@0.1.x, exit@~0.1.1:
  version "0.1.2"
  resolved "https://registry.npm.taobao.org/exit/download/exit-0.1.2.tgz#0632638f8d877cc82107d30a0fff1a17cba1cd0c"

extend@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npm.taobao.org/extend/download/extend-3.0.2.tgz#f8b1136b4071fbd8eb140aff858b1019ec2915fa"

extract-zip@^1.6.5:
  version "1.6.7"
  resolved "https://registry.npm.taobao.org/extract-zip/download/extract-zip-1.6.7.tgz#a840b4b8af6403264c8db57f4f1a74333ef81fe9"
  dependencies:
    concat-stream "1.6.2"
    debug "2.6.9"
    mkdirp "0.5.1"
    yauzl "2.4.1"

extsprintf@1.3.0:
  version "1.3.0"
  resolved "https://registry.npm.taobao.org/extsprintf/download/extsprintf-1.3.0.tgz#96918440e3041a7a414f8c52e3c574eb3c3e1e05"

extsprintf@^1.2.0:
  version "1.4.0"
  resolved "https://registry.npm.taobao.org/extsprintf/download/extsprintf-1.4.0.tgz#e2689f8f356fad62cca65a3a91c5df5f9551692f"

fast-deep-equal@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/fast-deep-equal/download/fast-deep-equal-2.0.1.tgz#7b05218ddf9667bf7f370bf7fdb2cb15fdd0aa49"

fast-json-stable-stringify@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/fast-json-stable-stringify/download/fast-json-stable-stringify-2.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffast-json-stable-stringify%2Fdownload%2Ffast-json-stable-stringify-2.0.0.tgz#d5142c0caee6b1189f87d3a76111064f86c8bbf2"

faye-websocket@~0.4.3:
  version "0.4.4"
  resolved "https://registry.npm.taobao.org/faye-websocket/download/faye-websocket-0.4.4.tgz#c14c5b3bf14d7417ffbfd990c0a7495cd9f337bc"

fd-slicer@~1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/fd-slicer/download/fd-slicer-1.0.1.tgz#8b5bcbd9ec327c5041bf9ab023fd6750f1177e65"
  dependencies:
    pend "~1.2.0"

figures@^1.0.1:
  version "1.7.0"
  resolved "https://registry.npm.taobao.org/figures/download/figures-1.7.0.tgz#cbe1e3affcf1cd44b80cadfed28dc793a9701d2e"
  dependencies:
    escape-string-regexp "^1.0.5"
    object-assign "^4.1.0"

find-up@3.0.0, find-up@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/find-up/download/find-up-3.0.0.tgz#49169f1d7993430646da61ecc5ae355c21c97b73"
  dependencies:
    locate-path "^3.0.0"

find-up@^1.0.0:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/find-up/download/find-up-1.1.2.tgz#6b2e9822b1a2ce0a60ab64d610eccad53cb24d0f"
  dependencies:
    path-exists "^2.0.0"
    pinkie-promise "^2.0.0"

findup-sync@~0.3.0:
  version "0.3.0"
  resolved "https://registry.npm.taobao.org/findup-sync/download/findup-sync-0.3.0.tgz#37930aa5d816b777c03445e1966cc6790a4c0b16"
  dependencies:
    glob "~5.0.0"

flat@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npm.taobao.org/flat/download/flat-4.1.0.tgz#090bec8b05e39cba309747f1d588f04dbaf98db2"
  dependencies:
    is-buffer "~2.0.3"

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "https://registry.npm.taobao.org/forever-agent/download/forever-agent-0.6.1.tgz#fbc71f0c41adeb37f96c577ad1ed42d8fdacca91"

form-data@~2.3.2:
  version "2.3.3"
  resolved "https://registry.npm.taobao.org/form-data/download/form-data-2.3.3.tgz#dcce52c05f644f298c6a7ab936bd724ceffbf3a6"
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

fs-extra@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/fs-extra/download/fs-extra-1.0.0.tgz#cd3ce5f7e7cb6145883fcae3191e9877f8587950"
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^2.1.0"
    klaw "^1.0.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"

function-bind@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/function-bind/download/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"

gaze@~0.4.0:
  version "0.4.3"
  resolved "https://registry.npm.taobao.org/gaze/download/gaze-0.4.3.tgz#e538f4ff5e4fe648f473a97e1ebb253d2de127b5"
  dependencies:
    globule "~0.1.0"

get-caller-file@^1.0.1:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/get-caller-file/download/get-caller-file-1.0.3.tgz#f978fa4c90d1dfe7ff2d6beda2a515e713bdcf4a"

get-caller-file@^2.0.1:
  version "2.0.5"
  resolved "https://registry.npm.taobao.org/get-caller-file/download/get-caller-file-2.0.5.tgz#4f94412a82db32f36e3b0b9741f8a97feb031f7e"

get-func-name@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/get-func-name/download/get-func-name-2.0.0.tgz#ead774abee72e20409433a066366023dd6887a41"

get-stdin@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/get-stdin/download/get-stdin-4.0.1.tgz#b968c6b0a04384324902e8bf1a5df32579a450fe"

get-stream@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npm.taobao.org/get-stream/download/get-stream-4.1.0.tgz#c1b255575f3dc21d59bfc79cd3d2b46b1c3a54b5"
  dependencies:
    pump "^3.0.0"

getobject@~0.1.0:
  version "0.1.0"
  resolved "https://registry.npm.taobao.org/getobject/download/getobject-0.1.0.tgz#047a449789fa160d018f5486ed91320b6ec7885c"

getpass@^0.1.1:
  version "0.1.7"
  resolved "https://registry.npm.taobao.org/getpass/download/getpass-0.1.7.tgz#5eff8e3e684d569ae4cb2b1282604e8ba62149fa"
  dependencies:
    assert-plus "^1.0.0"

glob@7.1.2:
  version "7.1.2"
  resolved "https://registry.npm.taobao.org/glob/download/glob-7.1.2.tgz#c19c9df9a028702d678612384a6552404c636d15"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@7.1.3:
  version "7.1.3"
  resolved "https://registry.npm.taobao.org/glob/download/glob-7.1.3.tgz#3960832d3f1574108342dafd3a67b332c0969df1"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@^7.1.3:
  version "7.1.4"
  resolved "https://registry.npm.taobao.org/glob/download/glob-7.1.4.tgz#aa608a2f6c577ad357e1ae5a5c26d9a8d1969255"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

"glob@~ 3.2.1":
  version "3.2.11"
  resolved "https://registry.npm.taobao.org/glob/download/glob-3.2.11.tgz#4a973f635b9190f715d10987d5c00fd2815ebe3d"
  dependencies:
    inherits "2"
    minimatch "0.3"

glob@~3.1.21:
  version "3.1.21"
  resolved "https://registry.npm.taobao.org/glob/download/glob-3.1.21.tgz#d29e0a055dea5138f4d07ed40e8982e83c2066cd"
  dependencies:
    graceful-fs "~1.2.0"
    inherits "1"
    minimatch "~0.2.11"

glob@~5.0.0:
  version "5.0.15"
  resolved "https://registry.npm.taobao.org/glob/download/glob-5.0.15.tgz#1bc936b9e02f4a603fcc222ecf7633d30b8b93b1"
  dependencies:
    inflight "^1.0.4"
    inherits "2"
    minimatch "2 || 3"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@~7.0.0:
  version "7.0.6"
  resolved "https://registry.npm.taobao.org/glob/download/glob-7.0.6.tgz#211bafaf49e525b8cd93260d14ab136152b3f57a"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.2"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globule@~0.1.0:
  version "0.1.0"
  resolved "https://registry.npm.taobao.org/globule/download/globule-0.1.0.tgz#d9c8edde1da79d125a151b79533b978676346ae5"
  dependencies:
    glob "~3.1.21"
    lodash "~1.0.1"
    minimatch "~0.2.11"

graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.1.9:
  version "4.1.15"
  resolved "https://registry.npm.taobao.org/graceful-fs/download/graceful-fs-4.1.15.tgz#ffb703e1066e8a0eeaa4c8b80ba9253eeefbfb00"

graceful-fs@~1.2.0:
  version "1.2.3"
  resolved "https://registry.npm.taobao.org/graceful-fs/download/graceful-fs-1.2.3.tgz#15a4806a57547cb2d2dbf27f42e89a8c3451b364"

growl@1.10.5, growl@^1.10.5:
  version "1.10.5"
  resolved "https://registry.npm.taobao.org/growl/download/growl-1.10.5.tgz#f2735dc2283674fa67478b10181059355c369e5e"

grunt-cli@~1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/grunt-cli/download/grunt-cli-1.2.0.tgz#562b119ebb069ddb464ace2845501be97b35b6a8"
  dependencies:
    findup-sync "~0.3.0"
    grunt-known-options "~1.1.0"
    nopt "~3.0.6"
    resolve "~1.1.0"

grunt-contrib-jshint@~0.11.0:
  version "0.11.3"
  resolved "https://registry.npm.taobao.org/grunt-contrib-jshint/download/grunt-contrib-jshint-0.11.3.tgz#80368181dccd551186e5b8385c011cee24d640a0"
  dependencies:
    hooker "^0.2.3"
    jshint "~2.8.0"

grunt-contrib-uglify@~0.8.0:
  version "0.8.1"
  resolved "https://registry.npm.taobao.org/grunt-contrib-uglify/download/grunt-contrib-uglify-0.8.1.tgz#e770afd47b1cd1de8d93fc19b6f00d9b2ad7eab7"
  dependencies:
    chalk "^1.0.0"
    lodash "^3.2.0"
    maxmin "^1.0.0"
    uglify-js "2.4.17"
    uri-path "0.0.2"

grunt-contrib-watch@~0.5.0:
  version "0.5.3"
  resolved "https://registry.npm.taobao.org/grunt-contrib-watch/download/grunt-contrib-watch-0.5.3.tgz#7d9eb5465d506fa14faaca47e6e8790a82c1c9ee"
  dependencies:
    gaze "~0.4.0"
    tiny-lr "0.0.4"

grunt-known-options@~1.1.0:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/grunt-known-options/download/grunt-known-options-1.1.1.tgz#6cc088107bd0219dc5d3e57d91923f469059804d"

grunt-legacy-log-utils@~2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/grunt-legacy-log-utils/download/grunt-legacy-log-utils-2.0.1.tgz#d2f442c7c0150065d9004b08fd7410d37519194e"
  dependencies:
    chalk "~2.4.1"
    lodash "~4.17.10"

grunt-legacy-log@~2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/grunt-legacy-log/download/grunt-legacy-log-2.0.0.tgz#c8cd2c6c81a4465b9bbf2d874d963fef7a59ffb9"
  dependencies:
    colors "~1.1.2"
    grunt-legacy-log-utils "~2.0.0"
    hooker "~0.2.3"
    lodash "~4.17.5"

grunt-legacy-util@~1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/grunt-legacy-util/download/grunt-legacy-util-1.1.1.tgz#e10624e7c86034e5b870c8a8616743f0a0845e42"
  dependencies:
    async "~1.5.2"
    exit "~0.1.1"
    getobject "~0.1.0"
    hooker "~0.2.3"
    lodash "~4.17.10"
    underscore.string "~3.3.4"
    which "~1.3.0"

grunt-lib-phantomjs@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/grunt-lib-phantomjs/download/grunt-lib-phantomjs-1.1.0.tgz#9e9edcdd9fd2dd40e0c181c94371d572aa5eead2"
  dependencies:
    eventemitter2 "^0.4.9"
    phantomjs-prebuilt "^2.1.3"
    rimraf "^2.5.2"
    semver "^5.1.0"
    temporary "^0.0.8"

grunt-mocha@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/grunt-mocha/download/grunt-mocha-1.2.0.tgz#33435963f9a7e7f01b66ea3af8cec052f6bfbdd4"
  dependencies:
    grunt-lib-phantomjs "^1.1.0"
    lodash "^4.17.11"
    mocha "^5.2.0"

grunt@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/grunt/download/grunt-1.0.4.tgz#c799883945a53a3d07622e0737c8f70bfe19eb38"
  dependencies:
    coffeescript "~1.10.0"
    dateformat "~1.0.12"
    eventemitter2 "~0.4.13"
    exit "~0.1.1"
    findup-sync "~0.3.0"
    glob "~7.0.0"
    grunt-cli "~1.2.0"
    grunt-known-options "~1.1.0"
    grunt-legacy-log "~2.0.0"
    grunt-legacy-util "~1.1.1"
    iconv-lite "~0.4.13"
    js-yaml "~3.13.0"
    minimatch "~3.0.2"
    mkdirp "~0.5.1"
    nopt "~3.0.6"
    path-is-absolute "~1.0.0"
    rimraf "~2.6.2"

gzip-size@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/gzip-size/download/gzip-size-1.0.0.tgz#66cf8b101047227b95bace6ea1da0c177ed5c22f"
  dependencies:
    browserify-zlib "^0.1.4"
    concat-stream "^1.4.1"

har-schema@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/har-schema/download/har-schema-2.0.0.tgz#a94c2224ebcac04782a0d9035521f24735b7ec92"

har-validator@~5.1.0:
  version "5.1.3"
  resolved "https://registry.npm.taobao.org/har-validator/download/har-validator-5.1.3.tgz#1ef89ebd3e4996557675eed9893110dc350fa080"
  dependencies:
    ajv "^6.5.5"
    har-schema "^2.0.0"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/has-ansi/download/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
  dependencies:
    ansi-regex "^2.0.0"

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/has-flag/download/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"

has-symbols@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/has-symbols/download/has-symbols-1.0.0.tgz#ba1a8f1af2a0fc39650f5c850367704122063b44"

has@^1.0.1, has@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/has/download/has-1.0.3.tgz#722d7cbfc1f6aa8241f16dd814e011e1f41e8796"
  dependencies:
    function-bind "^1.1.1"

hasha@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/hasha/download/hasha-2.2.0.tgz#78d7cbfc1e6d66303fe79837365984517b2f6ee1"
  dependencies:
    is-stream "^1.0.1"
    pinkie-promise "^2.0.0"

he@1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/he/download/he-1.1.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhe%2Fdownload%2Fhe-1.1.1.tgz#93410fd21b009735151f8868c2f271f3427e23fd"

he@1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/he/download/he-1.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhe%2Fdownload%2Fhe-1.2.0.tgz#84ae65fa7eafb165fddb61566ae14baf05664f0f"

hooker@^0.2.3, hooker@~0.2.3:
  version "0.2.3"
  resolved "https://registry.npm.taobao.org/hooker/download/hooker-0.2.3.tgz#b834f723cc4a242aa65963459df6d984c5d3d959"

hosted-git-info@^2.1.4:
  version "2.7.1"
  resolved "https://registry.npm.taobao.org/hosted-git-info/download/hosted-git-info-2.7.1.tgz#97f236977bd6e125408930ff6de3eec6281ec047"

htmlparser2@3.8.x:
  version "3.8.3"
  resolved "https://registry.npm.taobao.org/htmlparser2/download/htmlparser2-3.8.3.tgz#996c28b191516a8be86501a7d79757e5c70c1068"
  dependencies:
    domelementtype "1"
    domhandler "2.3"
    domutils "1.5"
    entities "1.0"
    readable-stream "1.1"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/http-signature/download/http-signature-1.2.0.tgz#9aecd925114772f3d95b65a60abb8f7c18fbace1"
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

iconv-lite@~0.4.13:
  version "0.4.24"
  resolved "https://registry.npm.taobao.org/iconv-lite/download/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

indent-string@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/indent-string/download/indent-string-2.1.0.tgz#8e2d48348742121b4a8218b7a137e9a52049dc80"
  dependencies:
    repeating "^2.0.0"

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npm.taobao.org/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@1:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/inherits/download/inherits-1.0.2.tgz#ca4309dadee6b54cc0b8d247e8d7c7a0975bdc9b"

inherits@2, inherits@^2.0.3, inherits@~2.0.1, inherits@~2.0.3:
  version "2.0.3"
  resolved "https://registry.npm.taobao.org/inherits/download/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"

invert-kv@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/invert-kv/download/invert-kv-2.0.0.tgz#7393f5afa59ec9ff5f67a27620d11c226e3eec02"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npm.taobao.org/is-arrayish/download/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"

is-buffer@~2.0.3:
  version "2.0.3"
  resolved "https://registry.npm.taobao.org/is-buffer/download/is-buffer-2.0.3.tgz#4ecf3fcf749cbd1e472689e109ac66261a25e725"

is-callable@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npm.taobao.org/is-callable/download/is-callable-1.1.4.tgz#1e1adf219e1eeb684d691f9d6a05ff0d30a24d75"

is-date-object@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/is-date-object/download/is-date-object-1.0.1.tgz#9aa20eb6aeebbff77fbd33e74ca01b33581d3a16"

is-finite@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/is-finite/download/is-finite-1.0.2.tgz#cc6677695602be550ef11e8b4aa6305342b6d0aa"
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz#ef9e31386f031a7f0d643af82fde50c457ef00cb"
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"

is-regex@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/is-regex/download/is-regex-1.0.4.tgz#5517489b547091b0930e095654ced25ee97e9491"
  dependencies:
    has "^1.0.1"

is-stream@^1.0.1, is-stream@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/is-stream/download/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"

is-symbol@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/is-symbol/download/is-symbol-1.0.2.tgz#a055f6ae57192caee329e7a860118b497a950f38"
  dependencies:
    has-symbols "^1.0.0"

is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/is-typedarray/download/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"

is-utf8@^0.2.0:
  version "0.2.1"
  resolved "https://registry.npm.taobao.org/is-utf8/download/is-utf8-0.2.1.tgz#4b0da1442104d1b336340e80797e865cf39f7d72"

isarray@0.0.1:
  version "0.0.1"
  resolved "https://registry.npm.taobao.org/isarray/download/isarray-0.0.1.tgz#8a18acfca9a8f4177e09abfc6038939b05d1eedf"

isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/isarray/download/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"

isstream@~0.1.2:
  version "0.1.2"
  resolved "https://registry.npm.taobao.org/isstream/download/isstream-0.1.2.tgz#47e63f7af55afa6f92e1500e690eb8b8529c099a"

js-yaml@3.13.1, js-yaml@~3.13.0:
  version "3.13.1"
  resolved "https://registry.npm.taobao.org/js-yaml/download/js-yaml-3.13.1.tgz#aff151b30bfdfa8e49e05da22e7415e9dfa37847"
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "https://registry.npm.taobao.org/jsbn/download/jsbn-0.1.1.tgz#a5e654c2e5a2deb5f201d96cefbca80c0ef2f513"

jshint@~2.8.0:
  version "2.8.0"
  resolved "https://registry.npm.taobao.org/jshint/download/jshint-2.8.0.tgz#1d09a3bd913c4cadfa81bf18d582bd85bffe0d44"
  dependencies:
    cli "0.6.x"
    console-browserify "1.1.x"
    exit "0.1.x"
    htmlparser2 "3.8.x"
    lodash "3.7.x"
    minimatch "2.0.x"
    shelljs "0.3.x"
    strip-json-comments "1.0.x"

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npm.taobao.org/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"

json-schema@0.2.3:
  version "0.2.3"
  resolved "https://registry.npm.taobao.org/json-schema/download/json-schema-0.2.3.tgz#b480c892e59a2f05954ce727bd3f2a4e882f9e13"

json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "https://registry.npm.taobao.org/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz#1296a2d58fd45f19a0f6ce01d65701e2c735b6eb"

jsonfile@^2.1.0:
  version "2.4.0"
  resolved "https://registry.npm.taobao.org/jsonfile/download/jsonfile-2.4.0.tgz#3736a2b428b87bbda0cc83b53fa3d633a35c2ae8"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsprim@^1.2.2:
  version "1.4.1"
  resolved "https://registry.npm.taobao.org/jsprim/download/jsprim-1.4.1.tgz#313e66bc1e5cc06e438bc1b7499c2e5c56acb6a2"
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.2.3"
    verror "1.10.0"

kew@^0.7.0:
  version "0.7.0"
  resolved "https://registry.npm.taobao.org/kew/download/kew-0.7.0.tgz#79d93d2d33363d6fdd2970b335d9141ad591d79b"

klaw@^1.0.0:
  version "1.3.1"
  resolved "https://registry.npm.taobao.org/klaw/download/klaw-1.3.1.tgz#4088433b46b3b1ba259d78785d8e96f73ba02439"
  optionalDependencies:
    graceful-fs "^4.1.9"

lcid@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/lcid/download/lcid-2.0.0.tgz#6ef5d2df60e52f82eb228a4c373e8d1f397253cf"
  dependencies:
    invert-kv "^2.0.0"

load-json-file@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/load-json-file/download/load-json-file-1.1.0.tgz#956905708d58b4bab4c2261b04f59f31c99374c0"
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"
    strip-bom "^2.0.0"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/locate-path/download/locate-path-3.0.0.tgz#dbec3b3ab759758071b58fe59fc41871af21400e"
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

lodash@3.7.x:
  version "3.7.0"
  resolved "https://registry.npm.taobao.org/lodash/download/lodash-3.7.0.tgz#3678bd8ab995057c07ade836ed2ef087da811d45"

lodash@^3.2.0:
  version "3.10.1"
  resolved "https://registry.npm.taobao.org/lodash/download/lodash-3.10.1.tgz#5bf45e8e49ba4189e17d482789dfd15bd140b7b6"

lodash@^4.17.11, lodash@~4.17.10, lodash@~4.17.5:
  version "4.17.11"
  resolved "https://registry.npm.taobao.org/lodash/download/lodash-4.17.11.tgz#b39ea6229ef607ecd89e2c8df12536891cac9b8d"

lodash@~1.0.1:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/lodash/download/lodash-1.0.2.tgz#8f57560c83b59fc270bd3d561b690043430e2551"

log-symbols@2.2.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/log-symbols/download/log-symbols-2.2.0.tgz#5740e1c5d6f0dfda4ad9323b5332107ef6b4c40a"
  dependencies:
    chalk "^2.0.1"

loud-rejection@^1.0.0:
  version "1.6.0"
  resolved "https://registry.npm.taobao.org/loud-rejection/download/loud-rejection-1.6.0.tgz#5b46f80147edee578870f086d04821cf998e551f"
  dependencies:
    currently-unhandled "^0.4.1"
    signal-exit "^3.0.0"

lru-cache@2:
  version "2.7.3"
  resolved "https://registry.npm.taobao.org/lru-cache/download/lru-cache-2.7.3.tgz#6d4524e8b955f95d4f5b58851ce21dd72fb4e952"

map-age-cleaner@^0.1.1:
  version "0.1.3"
  resolved "https://registry.npm.taobao.org/map-age-cleaner/download/map-age-cleaner-0.1.3.tgz#7d583a7306434c055fe474b0f45078e6e1b4b92a"
  dependencies:
    p-defer "^1.0.0"

map-obj@^1.0.0, map-obj@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/map-obj/download/map-obj-1.0.1.tgz#d933ceb9205d82bdcf4886f6742bdc2b4dea146d"

maxmin@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/maxmin/download/maxmin-1.1.0.tgz#71365e84a99dd8f8b3f7d5fde2f00d1e7f73be61"
  dependencies:
    chalk "^1.0.0"
    figures "^1.0.1"
    gzip-size "^1.0.0"
    pretty-bytes "^1.0.0"

mem@^4.0.0:
  version "4.3.0"
  resolved "https://registry.npm.taobao.org/mem/download/mem-4.3.0.tgz#461af497bc4ae09608cdb2e60eefb69bff744178"
  dependencies:
    map-age-cleaner "^0.1.1"
    mimic-fn "^2.0.0"
    p-is-promise "^2.0.0"

meow@^3.1.0, meow@^3.3.0:
  version "3.7.0"
  resolved "https://registry.npm.taobao.org/meow/download/meow-3.7.0.tgz#72cb668b425228290abbfa856892587308a801fb"
  dependencies:
    camelcase-keys "^2.0.0"
    decamelize "^1.1.2"
    loud-rejection "^1.0.0"
    map-obj "^1.0.1"
    minimist "^1.1.3"
    normalize-package-data "^2.3.4"
    object-assign "^4.0.1"
    read-pkg-up "^1.0.1"
    redent "^1.0.0"
    trim-newlines "^1.0.0"

mime-db@1.40.0:
  version "1.40.0"
  resolved "https://registry.npm.taobao.org/mime-db/download/mime-db-1.40.0.tgz#a65057e998db090f732a68f6c276d387d4126c32"

mime-types@^2.1.12, mime-types@~2.1.19:
  version "2.1.24"
  resolved "https://registry.npm.taobao.org/mime-types/download/mime-types-2.1.24.tgz#b6f8d0b3e951efb77dedeca194cff6d16f676f81"
  dependencies:
    mime-db "1.40.0"

mimic-fn@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/mimic-fn/download/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"

minimatch@0.3:
  version "0.3.0"
  resolved "https://registry.npm.taobao.org/minimatch/download/minimatch-0.3.0.tgz#275d8edaac4f1bb3326472089e7949c8394699dd"
  dependencies:
    lru-cache "2"
    sigmund "~1.0.0"

"minimatch@2 || 3", minimatch@3.0.4, minimatch@^3.0.2, minimatch@^3.0.4, minimatch@~3.0.2:
  version "3.0.4"
  resolved "https://registry.npm.taobao.org/minimatch/download/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
  dependencies:
    brace-expansion "^1.1.7"

minimatch@2.0.x:
  version "2.0.10"
  resolved "https://registry.npm.taobao.org/minimatch/download/minimatch-2.0.10.tgz#8d087c39c6b38c001b97fca7ce6d0e1e80afbac7"
  dependencies:
    brace-expansion "^1.0.0"

minimatch@~0.2.11:
  version "0.2.14"
  resolved "https://registry.npm.taobao.org/minimatch/download/minimatch-0.2.14.tgz#c74e780574f63c6f9a090e90efbe6ef53a6a756a"
  dependencies:
    lru-cache "2"
    sigmund "~1.0.0"

minimist@0.0.8:
  version "0.0.8"
  resolved "https://registry.npm.taobao.org/minimist/download/minimist-0.0.8.tgz#857fcabfc3397d2625b8228262e86aa7a011b05d"

minimist@^1.1.3:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/minimist/download/minimist-1.2.0.tgz#a35008b20f41383eec1fb914f4cd5df79a264284"

mkdirp@0.5.1, mkdirp@~0.5.1:
  version "0.5.1"
  resolved "https://registry.npm.taobao.org/mkdirp/download/mkdirp-0.5.1.tgz#30057438eac6cf7f8c4767f38648d6697d75c903"
  dependencies:
    minimist "0.0.8"

mocha@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npm.taobao.org/mocha/download/mocha-5.2.0.tgz#6d8ae508f59167f940f2b5b3c4a612ae50c90ae6"
  dependencies:
    browser-stdout "1.3.1"
    commander "2.15.1"
    debug "3.1.0"
    diff "3.5.0"
    escape-string-regexp "1.0.5"
    glob "7.1.2"
    growl "1.10.5"
    he "1.1.1"
    minimatch "3.0.4"
    mkdirp "0.5.1"
    supports-color "5.4.0"

mocha@^6.1.4:
  version "6.1.4"
  resolved "https://registry.npm.taobao.org/mocha/download/mocha-6.1.4.tgz#e35fada242d5434a7e163d555c705f6875951640"
  dependencies:
    ansi-colors "3.2.3"
    browser-stdout "1.3.1"
    debug "3.2.6"
    diff "3.5.0"
    escape-string-regexp "1.0.5"
    find-up "3.0.0"
    glob "7.1.3"
    growl "1.10.5"
    he "1.2.0"
    js-yaml "3.13.1"
    log-symbols "2.2.0"
    minimatch "3.0.4"
    mkdirp "0.5.1"
    ms "2.1.1"
    node-environment-flags "1.0.5"
    object.assign "4.1.0"
    strip-json-comments "2.0.1"
    supports-color "6.0.0"
    which "1.3.1"
    wide-align "1.1.3"
    yargs "13.2.2"
    yargs-parser "13.0.0"
    yargs-unparser "1.5.0"

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/ms/download/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"

ms@2.1.1, ms@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/ms/download/ms-2.1.1.tgz#30a5864eb3ebb0a66f2ebe6d727af06a09d86e0a"

nice-try@^1.0.4:
  version "1.0.5"
  resolved "https://registry.npm.taobao.org/nice-try/download/nice-try-1.0.5.tgz#a3378a7696ce7d223e88fc9b764bd7ef1089e366"

node-environment-flags@1.0.5:
  version "1.0.5"
  resolved "https://registry.npm.taobao.org/node-environment-flags/download/node-environment-flags-1.0.5.tgz#fa930275f5bf5dae188d6192b24b4c8bbac3d76a"
  dependencies:
    object.getownpropertydescriptors "^2.0.3"
    semver "^5.7.0"

nopt@~2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/nopt/download/nopt-2.0.0.tgz#ca7416f20a5e3f9c3b86180f96295fa3d0b52e0d"
  dependencies:
    abbrev "1"

nopt@~3.0.6:
  version "3.0.6"
  resolved "https://registry.npm.taobao.org/nopt/download/nopt-3.0.6.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnopt%2Fdownload%2Fnopt-3.0.6.tgz#c6465dbf08abcd4db359317f79ac68a646b28ff9"
  dependencies:
    abbrev "1"

noptify@latest:
  version "0.0.3"
  resolved "https://registry.npm.taobao.org/noptify/download/noptify-0.0.3.tgz#58f654a73d9753df0c51d9686dc92104a67f4bbb"
  dependencies:
    nopt "~2.0.0"

normalize-package-data@^2.3.2, normalize-package-data@^2.3.4:
  version "2.5.0"
  resolved "https://registry.npm.taobao.org/normalize-package-data/download/normalize-package-data-2.5.0.tgz#e66db1838b200c1dfc233225d12cb36520e234a8"
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npm.taobao.org/npm-run-path/download/npm-run-path-2.0.2.tgz#35a9232dfa35d7067b4cb2ddf2357b1871536c5f"
  dependencies:
    path-key "^2.0.0"

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/number-is-nan/download/number-is-nan-1.0.1.tgz#097b602b53422a522c1afb8790318336941a011d"

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "https://registry.npm.taobao.org/oauth-sign/download/oauth-sign-0.9.0.tgz#47a7b016baa68b5fa0ecf3dee08a85c679ac6455"

object-assign@^4.0.1, object-assign@^4.1.0:
  version "4.1.1"
  resolved "https://registry.npm.taobao.org/object-assign/download/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"

object-keys@^1.0.11, object-keys@^1.0.12:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/object-keys/download/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"

object.assign@4.1.0:
  version "4.1.0"
  resolved "https://registry.npm.taobao.org/object.assign/download/object.assign-4.1.0.tgz#968bf1100d7956bb3ca086f006f846b3bc4008da"
  dependencies:
    define-properties "^1.1.2"
    function-bind "^1.1.1"
    has-symbols "^1.0.0"
    object-keys "^1.0.11"

object.getownpropertydescriptors@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npm.taobao.org/object.getownpropertydescriptors/download/object.getownpropertydescriptors-2.0.3.tgz#8758c846f5b407adab0f236e0986f14b051caa16"
  dependencies:
    define-properties "^1.1.2"
    es-abstract "^1.5.1"

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npm.taobao.org/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  dependencies:
    wrappy "1"

os-locale@^3.0.0, os-locale@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/os-locale/download/os-locale-3.1.0.tgz#a802a6ee17f24c10483ab9935719cef4ed16bf1a"
  dependencies:
    execa "^1.0.0"
    lcid "^2.0.0"
    mem "^4.0.0"

p-defer@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/p-defer/download/p-defer-1.0.0.tgz#9f6eb182f6c9aa8cd743004a7d4f96b196b0fb0c"

p-finally@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/p-finally/download/p-finally-1.0.0.tgz#3fbcfb15b899a44123b34b6dcc18b724336a2cae"

p-is-promise@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/p-is-promise/download/p-is-promise-2.1.0.tgz#918cebaea248a62cf7ffab8e3bca8c5f882fc42e"

p-limit@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/p-limit/download/p-limit-2.2.0.tgz#417c9941e6027a9abcba5092dd2904e255b5fbc2"
  dependencies:
    p-try "^2.0.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/p-locate/download/p-locate-3.0.0.tgz#322d69a05c0264b25997d9f40cd8a891ab0064a4"
  dependencies:
    p-limit "^2.0.0"

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/p-try/download/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"

"package@>= 1.0.0 < 1.2.0":
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/package/download/package-1.0.1.tgz#d25a1f99e2506dcb27d6704b83dca8a312e4edcc"

pako@~0.2.0:
  version "0.2.9"
  resolved "https://registry.npm.taobao.org/pako/download/pako-0.2.9.tgz#f3f7522f4ef782348da8161bad9ecfd51bf83a75"

parse-json@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/parse-json/download/parse-json-2.2.0.tgz#f480f40434ef80741f8469099f8dea18f55a4dc9"
  dependencies:
    error-ex "^1.2.0"

path-exists@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/path-exists/download/path-exists-2.1.0.tgz#0feb6c64f0fc518d9a754dd5efb62c7022761f4b"
  dependencies:
    pinkie-promise "^2.0.0"

path-exists@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/path-exists/download/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"

path-is-absolute@^1.0.0, path-is-absolute@~1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/path-key/download/path-key-2.0.1.tgz#411cadb574c5a140d3a4b1910d40d80cc9f40b40"

path-parse@^1.0.6:
  version "1.0.6"
  resolved "https://registry.npm.taobao.org/path-parse/download/path-parse-1.0.6.tgz#d62dbb5679405d72c4737ec58600e9ddcf06d24c"

path-type@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/path-type/download/path-type-1.1.0.tgz#59c44f7ee491da704da415da5a4070ba4f8fe441"
  dependencies:
    graceful-fs "^4.1.2"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

pathval@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/pathval/download/pathval-1.1.0.tgz#b942e6d4bde653005ef6b71361def8727d0645e0"

pend@~1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/pend/download/pend-1.2.0.tgz#7a57eb550a6783f9115331fcf4663d5c8e007a50"

performance-now@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/performance-now/download/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"

phantomjs-prebuilt@^2.1.3:
  version "2.1.16"
  resolved "https://registry.npm.taobao.org/phantomjs-prebuilt/download/phantomjs-prebuilt-2.1.16.tgz#efd212a4a3966d3647684ea8ba788549be2aefef"
  dependencies:
    es6-promise "^4.0.3"
    extract-zip "^1.6.5"
    fs-extra "^1.0.0"
    hasha "^2.2.0"
    kew "^0.7.0"
    progress "^1.1.8"
    request "^2.81.0"
    request-progress "^2.0.1"
    which "^1.2.10"

pify@^2.0.0:
  version "2.3.0"
  resolved "https://registry.npm.taobao.org/pify/download/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/pinkie-promise/download/pinkie-promise-2.0.1.tgz#2135d6dfa7a358c069ac9b178776288228450ffa"
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "https://registry.npm.taobao.org/pinkie/download/pinkie-2.0.4.tgz#72556b80cfa0d48a974e80e77248e80ed4f7f870"

pretty-bytes@^1.0.0:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/pretty-bytes/download/pretty-bytes-1.0.4.tgz#0a22e8210609ad35542f8c8d5d2159aff0751c84"
  dependencies:
    get-stdin "^4.0.1"
    meow "^3.1.0"

process-nextick-args@~2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/process-nextick-args/download/process-nextick-args-2.0.0.tgz#a37d732f4271b4ab1ad070d35508e8290788ffaa"

progress@^1.1.8:
  version "1.1.8"
  resolved "https://registry.npm.taobao.org/progress/download/progress-1.1.8.tgz#e260c78f6161cdd9b0e56cc3e0a85de17c7a57be"

psl@^1.1.24:
  version "1.1.31"
  resolved "https://registry.npm.taobao.org/psl/download/psl-1.1.31.tgz#e9aa86d0101b5b105cbe93ac6b784cd547276184"

pump@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/pump/download/pump-3.0.0.tgz#b4a2116815bde2f4e1ea602354e8c75565107a64"
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

punycode@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npm.taobao.org/punycode/download/punycode-1.4.1.tgz#c0d5a63b2718800ad8e1eb0fa5269c84dd41845e"

punycode@^2.1.0:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/punycode/download/punycode-2.1.1.tgz#b58b010ac40c22c5657616c8d2c2c02c7bf479ec"

qs@~0.5.2:
  version "0.5.6"
  resolved "https://registry.npm.taobao.org/qs/download/qs-0.5.6.tgz#31b1ad058567651c526921506b9a8793911a0384"

qs@~6.5.2:
  version "6.5.2"
  resolved "https://registry.npm.taobao.org/qs/download/qs-6.5.2.tgz#cb3ae806e8740444584ef154ce8ee98d403f3e36"

read-pkg-up@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/read-pkg-up/download/read-pkg-up-1.0.1.tgz#9d63c13276c065918d57f002a57f40a1b643fb02"
  dependencies:
    find-up "^1.0.0"
    read-pkg "^1.0.0"

read-pkg@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/read-pkg/download/read-pkg-1.1.0.tgz#f5ffaa5ecd29cb31c0474bca7d756b6bb29e3f28"
  dependencies:
    load-json-file "^1.0.0"
    normalize-package-data "^2.3.2"
    path-type "^1.0.0"

readable-stream@1.1:
  version "1.1.13"
  resolved "https://registry.npm.taobao.org/readable-stream/download/readable-stream-1.1.13.tgz#f6eef764f514c89e2b9e23146a75ba106756d23e"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

readable-stream@^2.2.2:
  version "2.3.6"
  resolved "https://registry.npm.taobao.org/readable-stream/download/readable-stream-2.3.6.tgz#b11c27d88b8ff1fbe070643cf94b0c79ae1b0aaf"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

redent@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/redent/download/redent-1.0.0.tgz#cf916ab1fd5f1f16dfb20822dd6ec7f730c2afde"
  dependencies:
    indent-string "^2.1.0"
    strip-indent "^1.0.1"

repeating@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/repeating/download/repeating-2.0.1.tgz#5214c53a926d3552707527fbab415dbc08d06dda"
  dependencies:
    is-finite "^1.0.0"

request-progress@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/request-progress/download/request-progress-2.0.1.tgz#5d36bb57961c673aa5b788dbc8141fdf23b44e08"
  dependencies:
    throttleit "^1.0.0"

request@^2.81.0:
  version "2.88.0"
  resolved "https://registry.npm.taobao.org/request/download/request-2.88.0.tgz#9c2fca4f7d35b592efe57c7f0a55e81052124fef"
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.0"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.4.3"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/require-directory/download/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"

require-main-filename@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/require-main-filename/download/require-main-filename-1.0.1.tgz#97f717b69d48784f5f526a6c5aa8ffdda055a4d1"

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/require-main-filename/download/require-main-filename-2.0.0.tgz#d0b329ecc7cc0f61649f62215be69af54aa8989b"

resolve@^1.10.0:
  version "1.11.0"
  resolved "https://registry.npm.taobao.org/resolve/download/resolve-1.11.0.tgz#4014870ba296176b86343d50b60f3b50609ce232"
  dependencies:
    path-parse "^1.0.6"

resolve@~1.1.0:
  version "1.1.7"
  resolved "https://registry.npm.taobao.org/resolve/download/resolve-1.1.7.tgz#203114d82ad2c5ed9e8e0411b3932875e889e97b"

rimraf@^2.5.2, rimraf@~2.6.2:
  version "2.6.3"
  resolved "https://registry.npm.taobao.org/rimraf/download/rimraf-2.6.3.tgz#b2d104fe0d8fb27cf9e0a1cda8262dd3833c6cab"
  dependencies:
    glob "^7.1.3"

safe-buffer@^5.0.1, safe-buffer@^5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://registry.npm.taobao.org/safe-buffer/download/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"

"safer-buffer@>= 2.1.2 < 3", safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "https://registry.npm.taobao.org/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"

"semver@2 || 3 || 4 || 5", semver@^5.1.0, semver@^5.5.0, semver@^5.7.0:
  version "5.7.0"
  resolved "https://registry.npm.taobao.org/semver/download/semver-5.7.0.tgz#790a7cf6fea5459bac96110b29b60412dc8ff96b"

set-blocking@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/set-blocking/download/set-blocking-2.0.0.tgz#045f9782d011ae9a6803ddd382b24392b3d890f7"

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/shebang-command/download/shebang-command-1.2.0.tgz#44aac65b695b03398968c39f363fee5deafdf1ea"
  dependencies:
    shebang-regex "^1.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/shebang-regex/download/shebang-regex-1.0.0.tgz#da42f49740c0b42db2ca9728571cb190c98efea3"

shelljs@0.3.x:
  version "0.3.0"
  resolved "https://registry.npm.taobao.org/shelljs/download/shelljs-0.3.0.tgz#3596e6307a781544f591f37da618360f31db57b1"

sigmund@~1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/sigmund/download/sigmund-1.0.1.tgz#3ff21f198cad2175f9f3b781853fd94d0d19b590"

signal-exit@^3.0.0:
  version "3.0.2"
  resolved "https://registry.npm.taobao.org/signal-exit/download/signal-exit-3.0.2.tgz#b5fdc08f1287ea1178628e415e25132b73646c6d"

source-map@0.1.34:
  version "0.1.34"
  resolved "https://registry.npm.taobao.org/source-map/download/source-map-0.1.34.tgz#a7cfe89aec7b1682c3b198d0acfb47d7d090566b"
  dependencies:
    amdefine ">=0.0.4"

spdx-correct@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/spdx-correct/download/spdx-correct-3.1.0.tgz#fb83e504445268f154b074e218c87c003cd31df4"
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/spdx-exceptions/download/spdx-exceptions-2.2.0.tgz#2ea450aee74f2a89bfb94519c07fcd6f41322977"

spdx-expression-parse@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/spdx-expression-parse/download/spdx-expression-parse-3.0.0.tgz#99e119b7a5da00e05491c9fa338b7904823b41d0"
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.4"
  resolved "https://registry.npm.taobao.org/spdx-license-ids/download/spdx-license-ids-3.0.4.tgz#75ecd1a88de8c184ef015eafb51b5b48bfd11bb1"

sprintf-js@^1.0.3:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/sprintf-js/download/sprintf-js-1.1.2.tgz#da1765262bf8c0f571749f2ad6c26300207ae673"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/sprintf-js/download/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"

sshpk@^1.7.0:
  version "1.16.1"
  resolved "https://registry.npm.taobao.org/sshpk/download/sshpk-1.16.1.tgz#fb661c0bef29b39db40769ee39fa70093d6f6877"
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    bcrypt-pbkdf "^1.0.0"
    dashdash "^1.12.0"
    ecc-jsbn "~0.1.1"
    getpass "^0.1.1"
    jsbn "~0.1.0"
    safer-buffer "^2.0.2"
    tweetnacl "~0.14.0"

string-width@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/string-width/download/string-width-1.0.2.tgz#118bdf5b8cdc51a2a7e70d211e07e2b0b9b107d3"
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

"string-width@^1.0.2 || 2", string-width@^2.0.0, string-width@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/string-width/download/string-width-2.1.1.tgz#ab93f27a8dc13d28cac815c462143a6d9012ae9e"
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/string-width/download/string-width-3.1.0.tgz#22767be21b62af1081574306f69ac51b62203961"
  dependencies:
    emoji-regex "^7.0.1"
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^5.1.0"

string_decoder@~0.10.x:
  version "0.10.31"
  resolved "https://registry.npm.taobao.org/string_decoder/download/string_decoder-0.10.31.tgz#62e203bc41766c6c28c9fc84301dab1c5310fa94"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/string_decoder/download/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npm.taobao.org/strip-ansi/download/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/strip-ansi/download/strip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^5.1.0:
  version "5.2.0"
  resolved "https://registry.npm.taobao.org/strip-ansi/download/strip-ansi-5.2.0.tgz#8c9a536feb6afc962bdfa5b104a5091c1ad9c0ae"
  dependencies:
    ansi-regex "^4.1.0"

strip-bom@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/strip-bom/download/strip-bom-2.0.0.tgz#6219a85616520491f35788bdbf1447a99c7e6b0e"
  dependencies:
    is-utf8 "^0.2.0"

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/strip-eof/download/strip-eof-1.0.0.tgz#bb43ff5598a6eb05d89b59fcd129c983313606bf"

strip-indent@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/strip-indent/download/strip-indent-1.0.1.tgz#0c7962a6adefa7bbd4ac366460a638552ae1a0a2"
  dependencies:
    get-stdin "^4.0.1"

strip-json-comments@1.0.x:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/strip-json-comments/download/strip-json-comments-1.0.4.tgz#1e15fbcac97d3ee99bf2d73b4c656b082bbafb91"

strip-json-comments@2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/strip-json-comments/download/strip-json-comments-2.0.1.tgz#3c531942e908c2697c0ec344858c286c7ca0a60a"

supports-color@5.4.0:
  version "5.4.0"
  resolved "https://registry.npm.taobao.org/supports-color/download/supports-color-5.4.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsupports-color%2Fdownload%2Fsupports-color-5.4.0.tgz#1c6b337402c2137605efe19f10fec390f6faab54"
  dependencies:
    has-flag "^3.0.0"

supports-color@6.0.0:
  version "6.0.0"
  resolved "https://registry.npm.taobao.org/supports-color/download/supports-color-6.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsupports-color%2Fdownload%2Fsupports-color-6.0.0.tgz#76cfe742cf1f41bb9b1c29ad03068c05b4c0e40a"
  dependencies:
    has-flag "^3.0.0"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/supports-color/download/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://registry.npm.taobao.org/supports-color/download/supports-color-5.5.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsupports-color%2Fdownload%2Fsupports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  dependencies:
    has-flag "^3.0.0"

temporary@^0.0.8:
  version "0.0.8"
  resolved "https://registry.npm.taobao.org/temporary/download/temporary-0.0.8.tgz#a18a981d28ba8ca36027fb3c30538c3ecb740ac0"
  dependencies:
    package ">= 1.0.0 < 1.2.0"

throttleit@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/throttleit/download/throttleit-1.0.0.tgz#9e785836daf46743145a5984b6268d828528ac6c"

tiny-lr@0.0.4:
  version "0.0.4"
  resolved "https://registry.npm.taobao.org/tiny-lr/download/tiny-lr-0.0.4.tgz#80618547f63f697d05cb40c4c2c4b083521aefb6"
  dependencies:
    debug "~0.7.0"
    faye-websocket "~0.4.3"
    noptify latest
    qs "~0.5.2"

tough-cookie@~2.4.3:
  version "2.4.3"
  resolved "https://registry.npm.taobao.org/tough-cookie/download/tough-cookie-2.4.3.tgz#53f36da3f47783b0925afa06ff9f3b165280f781"
  dependencies:
    psl "^1.1.24"
    punycode "^1.4.1"

trim-newlines@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/trim-newlines/download/trim-newlines-1.0.0.tgz#5887966bb582a4503a41eb524f7d35011815a613"

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "https://registry.npm.taobao.org/tunnel-agent/download/tunnel-agent-0.6.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftunnel-agent%2Fdownload%2Ftunnel-agent-0.6.0.tgz#27a5dea06b36b04a0a9966774b290868f0fc40fd"
  dependencies:
    safe-buffer "^5.0.1"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "https://registry.npm.taobao.org/tweetnacl/download/tweetnacl-0.14.5.tgz#5ae68177f192d4456269d108afa93ff8743f4f64"

type-detect@^4.0.0, type-detect@^4.0.5:
  version "4.0.8"
  resolved "https://registry.npm.taobao.org/type-detect/download/type-detect-4.0.8.tgz#7646fb5f18871cfbb7749e69bd39a6388eb7450c"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "https://registry.npm.taobao.org/typedarray/download/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"

uglify-js@2.4.17:
  version "2.4.17"
  resolved "https://registry.npm.taobao.org/uglify-js/download/uglify-js-2.4.17.tgz#01b9b38c828cb62ccfc25bedd1dfabd907c431a1"
  dependencies:
    async "~0.2.6"
    source-map "0.1.34"
    uglify-to-browserify "~1.0.0"
    yargs "~1.3.3"

uglify-to-browserify@~1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/uglify-to-browserify/download/uglify-to-browserify-1.0.2.tgz#6e0924d6bda6b5afe349e39a6d632850a0f882b7"

underscore.string@~3.3.4:
  version "3.3.5"
  resolved "https://registry.npm.taobao.org/underscore.string/download/underscore.string-3.3.5.tgz#fc2ad255b8bd309e239cbc5816fd23a9b7ea4023"
  dependencies:
    sprintf-js "^1.0.3"
    util-deprecate "^1.0.2"

uri-js@^4.2.2:
  version "4.2.2"
  resolved "https://registry.npm.taobao.org/uri-js/download/uri-js-4.2.2.tgz#94c540e1ff772956e2299507c010aea6c8838eb0"
  dependencies:
    punycode "^2.1.0"

uri-path@0.0.2:
  version "0.0.2"
  resolved "https://registry.npm.taobao.org/uri-path/download/uri-path-0.0.2.tgz#803eb01f2feb17927dcce0f6187e72b75f53f554"

util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"

uuid@^3.3.2:
  version "3.3.2"
  resolved "https://registry.npm.taobao.org/uuid/download/uuid-3.3.2.tgz#1b4af4955eb3077c501c23872fc6513811587131"

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "https://registry.npm.taobao.org/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz#fc91f6b9c7ba15c857f4cb2c5defeec39d4f410a"
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

verror@1.10.0:
  version "1.10.0"
  resolved "https://registry.npm.taobao.org/verror/download/verror-1.10.0.tgz#3a105ca17053af55d6e270c1f8288682e18da400"
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

which-module@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/which-module/download/which-module-2.0.0.tgz#d9ef07dce77b9902b8a3a8fa4b31c3e3f7e6e87a"

which@1.3.1, which@^1.2.10, which@^1.2.9, which@~1.3.0:
  version "1.3.1"
  resolved "https://registry.npm.taobao.org/which/download/which-1.3.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwhich%2Fdownload%2Fwhich-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
  dependencies:
    isexe "^2.0.0"

wide-align@1.1.3:
  version "1.1.3"
  resolved "https://registry.npm.taobao.org/wide-align/download/wide-align-1.1.3.tgz#ae074e6bdc0c14a431e804e624549c633b000457"
  dependencies:
    string-width "^1.0.2 || 2"

wrap-ansi@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/wrap-ansi/download/wrap-ansi-2.1.0.tgz#d8fc3d284dd05794fe84973caecdd1cf824fdd85"
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"

"y18n@^3.2.1 || ^4.0.0", y18n@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/y18n/download/y18n-4.0.0.tgz#95ef94f85ecc81d007c264e190a120f0a3c8566b"

yargs-parser@13.0.0:
  version "13.0.0"
  resolved "https://registry.npm.taobao.org/yargs-parser/download/yargs-parser-13.0.0.tgz#3fc44f3e76a8bdb1cc3602e860108602e5ccde8b"
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^11.1.1:
  version "11.1.1"
  resolved "https://registry.npm.taobao.org/yargs-parser/download/yargs-parser-11.1.1.tgz#879a0865973bca9f6bab5cbdf3b1c67ec7d3bcf4"
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^13.0.0:
  version "13.1.0"
  resolved "https://registry.npm.taobao.org/yargs-parser/download/yargs-parser-13.1.0.tgz#7016b6dd03e28e1418a510e258be4bff5a31138f"
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-unparser@1.5.0:
  version "1.5.0"
  resolved "https://registry.npm.taobao.org/yargs-unparser/download/yargs-unparser-1.5.0.tgz#f2bb2a7e83cbc87bb95c8e572828a06c9add6e0d"
  dependencies:
    flat "^4.1.0"
    lodash "^4.17.11"
    yargs "^12.0.5"

yargs@13.2.2:
  version "13.2.2"
  resolved "https://registry.npm.taobao.org/yargs/download/yargs-13.2.2.tgz#0c101f580ae95cea7f39d927e7770e3fdc97f993"
  dependencies:
    cliui "^4.0.0"
    find-up "^3.0.0"
    get-caller-file "^2.0.1"
    os-locale "^3.1.0"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^3.0.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^13.0.0"

yargs@^12.0.5:
  version "12.0.5"
  resolved "https://registry.npm.taobao.org/yargs/download/yargs-12.0.5.tgz#05f5997b609647b64f66b81e3b4b10a368e7ad13"
  dependencies:
    cliui "^4.0.0"
    decamelize "^1.2.0"
    find-up "^3.0.0"
    get-caller-file "^1.0.1"
    os-locale "^3.0.0"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^2.0.0"
    which-module "^2.0.0"
    y18n "^3.2.1 || ^4.0.0"
    yargs-parser "^11.1.1"

yargs@~1.3.3:
  version "1.3.3"
  resolved "https://registry.npm.taobao.org/yargs/download/yargs-1.3.3.tgz#054de8b61f22eefdb7207059eaef9d6b83fb931a"

yauzl@2.4.1:
  version "2.4.1"
  resolved "https://registry.npm.taobao.org/yauzl/download/yauzl-2.4.1.tgz#9528f442dab1b2284e58b4379bb194e22e0c4005"
  dependencies:
    fd-slicer "~1.0.1"
