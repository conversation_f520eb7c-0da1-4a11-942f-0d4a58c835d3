{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/pascal/pascal.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    // the default separators except `@$`\n    wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\#\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n    comments: {\n        lineComment: '//',\n        blockComment: ['{', '}']\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')'],\n        ['<', '>']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '<', close: '>' },\n        { open: \"'\", close: \"'\" }\n    ],\n    surroundingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '<', close: '>' },\n        { open: \"'\", close: \"'\" }\n    ],\n    folding: {\n        markers: {\n            start: new RegExp(\"^\\\\s*\\\\{\\\\$REGION(\\\\s\\\\'.*\\\\')?\\\\}\"),\n            end: new RegExp('^\\\\s*\\\\{\\\\$ENDREGION\\\\}')\n        }\n    }\n};\nexport var language = {\n    defaultToken: '',\n    tokenPostfix: '.pascal',\n    ignoreCase: true,\n    brackets: [\n        { open: '{', close: '}', token: 'delimiter.curly' },\n        { open: '[', close: ']', token: 'delimiter.square' },\n        { open: '(', close: ')', token: 'delimiter.parenthesis' },\n        { open: '<', close: '>', token: 'delimiter.angle' }\n    ],\n    keywords: [\n        'absolute',\n        'abstract',\n        'all',\n        'and_then',\n        'array',\n        'as',\n        'asm',\n        'attribute',\n        'begin',\n        'bindable',\n        'case',\n        'class',\n        'const',\n        'contains',\n        'default',\n        'div',\n        'else',\n        'end',\n        'except',\n        'exports',\n        'external',\n        'far',\n        'file',\n        'finalization',\n        'finally',\n        'forward',\n        'generic',\n        'goto',\n        'if',\n        'implements',\n        'import',\n        'in',\n        'index',\n        'inherited',\n        'initialization',\n        'interrupt',\n        'is',\n        'label',\n        'library',\n        'mod',\n        'module',\n        'name',\n        'near',\n        'not',\n        'object',\n        'of',\n        'on',\n        'only',\n        'operator',\n        'or_else',\n        'otherwise',\n        'override',\n        'package',\n        'packed',\n        'pow',\n        'private',\n        'program',\n        'protected',\n        'public',\n        'published',\n        'interface',\n        'implementation',\n        'qualified',\n        'read',\n        'record',\n        'resident',\n        'requires',\n        'resourcestring',\n        'restricted',\n        'segment',\n        'set',\n        'shl',\n        'shr',\n        'specialize',\n        'stored',\n        'strict',\n        'then',\n        'threadvar',\n        'to',\n        'try',\n        'type',\n        'unit',\n        'uses',\n        'var',\n        'view',\n        'virtual',\n        'dynamic',\n        'overload',\n        'reintroduce',\n        'with',\n        'write',\n        'xor',\n        'true',\n        'false',\n        'procedure',\n        'function',\n        'constructor',\n        'destructor',\n        'property',\n        'break',\n        'continue',\n        'exit',\n        'abort',\n        'while',\n        'do',\n        'for',\n        'raise',\n        'repeat',\n        'until'\n    ],\n    typeKeywords: [\n        'boolean',\n        'double',\n        'byte',\n        'integer',\n        'shortint',\n        'char',\n        'longint',\n        'float',\n        'string'\n    ],\n    operators: [\n        '=',\n        '>',\n        '<',\n        '<=',\n        '>=',\n        '<>',\n        ':',\n        ':=',\n        'and',\n        'or',\n        '+',\n        '-',\n        '*',\n        '/',\n        '@',\n        '&',\n        '^',\n        '%'\n    ],\n    // we include these common regular expressions\n    symbols: /[=><:@\\^&|+\\-*\\/\\^%]+/,\n    // The main tokenizer for our languages\n    tokenizer: {\n        root: [\n            // identifiers and keywords\n            [\n                /[a-zA-Z_][\\w]*/,\n                {\n                    cases: {\n                        '@keywords': { token: 'keyword.$0' },\n                        '@default': 'identifier'\n                    }\n                }\n            ],\n            // whitespace\n            { include: '@whitespace' },\n            // delimiters and operators\n            [/[{}()\\[\\]]/, '@brackets'],\n            [/[<>](?!@symbols)/, '@brackets'],\n            [\n                /@symbols/,\n                {\n                    cases: {\n                        '@operators': 'delimiter',\n                        '@default': ''\n                    }\n                }\n            ],\n            // numbers\n            [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, 'number.float'],\n            [/\\$[0-9a-fA-F]{1,16}/, 'number.hex'],\n            [/\\d+/, 'number'],\n            // delimiter: after number because of .\\d floats\n            [/[;,.]/, 'delimiter'],\n            // strings\n            [/'([^'\\\\]|\\\\.)*$/, 'string.invalid'],\n            [/'/, 'string', '@string'],\n            // characters\n            [/'[^\\\\']'/, 'string'],\n            [/'/, 'string.invalid'],\n            [/\\#\\d+/, 'string']\n        ],\n        comment: [\n            [/[^\\*\\}]+/, 'comment'],\n            //[/\\(\\*/,    'comment', '@push' ],    // nested comment  not allowed :-(\n            [/\\}/, 'comment', '@pop'],\n            [/[\\{]/, 'comment']\n        ],\n        string: [\n            [/[^\\\\']+/, 'string'],\n            [/\\\\./, 'string.escape.invalid'],\n            [/'/, { token: 'string.quote', bracket: '@close', next: '@pop' }]\n        ],\n        whitespace: [\n            [/[ \\t\\r\\n]+/, 'white'],\n            [/\\{/, 'comment', '@comment'],\n            [/\\/\\/.*$/, 'comment']\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA;AAAA,EAEd,aAAa;AAAA,EACb,UAAU;AAAA,IACN,aAAa;AAAA,IACb,cAAc,CAAC,KAAK,GAAG;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,SAAS;AAAA,IACL,SAAS;AAAA,MACL,OAAO,IAAI,OAAO,oCAAoC;AAAA,MACtD,KAAK,IAAI,OAAO,yBAAyB;AAAA,IAC7C;AAAA,EACJ;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,UAAU;AAAA,IACN,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,EACtD;AAAA,EACA,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,cAAc;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,WAAW;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA;AAAA,EAEA,SAAS;AAAA;AAAA,EAET,WAAW;AAAA,IACP,MAAM;AAAA;AAAA,MAEF;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,aAAa,EAAE,OAAO,aAAa;AAAA,YACnC,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,oBAAoB,WAAW;AAAA,MAChC;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,cAAc;AAAA,YACd,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,CAAC,4BAA4B,cAAc;AAAA,MAC3C,CAAC,uBAAuB,YAAY;AAAA,MACpC,CAAC,OAAO,QAAQ;AAAA;AAAA,MAEhB,CAAC,SAAS,WAAW;AAAA;AAAA,MAErB,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,KAAK,UAAU,SAAS;AAAA;AAAA,MAEzB,CAAC,YAAY,QAAQ;AAAA,MACrB,CAAC,KAAK,gBAAgB;AAAA,MACtB,CAAC,SAAS,QAAQ;AAAA,IACtB;AAAA,IACA,SAAS;AAAA,MACL,CAAC,YAAY,SAAS;AAAA;AAAA,MAEtB,CAAC,MAAM,WAAW,MAAM;AAAA,MACxB,CAAC,QAAQ,SAAS;AAAA,IACtB;AAAA,IACA,QAAQ;AAAA,MACJ,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IACpE;AAAA,IACA,YAAY;AAAA,MACR,CAAC,cAAc,OAAO;AAAA,MACtB,CAAC,MAAM,WAAW,UAAU;AAAA,MAC5B,CAAC,WAAW,SAAS;AAAA,IACzB;AAAA,EACJ;AACJ;", "names": []}