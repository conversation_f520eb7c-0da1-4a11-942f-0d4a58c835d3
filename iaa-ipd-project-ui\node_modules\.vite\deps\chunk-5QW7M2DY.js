import {
  require_MapCache,
  require_Set,
  require_SetCache,
  require_Stack,
  require_Symbol,
  require_baseGetTag,
  require_baseIsEqual,
  require_baseUnary,
  require_cacheHas,
  require_isArguments,
  require_isArray,
  require_isEqual,
  require_isIndex,
  require_isLength,
  require_isObject,
  require_isObjectLike,
  require_keys,
  require_nodeUtil,
  require_overArg,
  require_setToArray
} from "./chunk-ZJNJ4ZV3.js";
import {
  init_moment,
  moment_default
} from "./chunk-CNJUQDSN.js";
import {
  __commonJS,
  __toESM
} from "./chunk-GFT2G5UO.js";

// node_modules/lodash/_getPrototype.js
var require_getPrototype = __commonJS({
  "node_modules/lodash/_getPrototype.js"(exports, module) {
    var overArg = require_overArg();
    var getPrototype = overArg(Object.getPrototypeOf, Object);
    module.exports = getPrototype;
  }
});

// node_modules/lodash/isPlainObject.js
var require_isPlainObject = __commonJS({
  "node_modules/lodash/isPlainObject.js"(exports, module) {
    var baseGetTag = require_baseGetTag();
    var getPrototype = require_getPrototype();
    var isObjectLike = require_isObjectLike();
    var objectTag = "[object Object]";
    var funcProto = Function.prototype;
    var objectProto = Object.prototype;
    var funcToString = funcProto.toString;
    var hasOwnProperty = objectProto.hasOwnProperty;
    var objectCtorString = funcToString.call(Object);
    function isPlainObject2(value) {
      if (!isObjectLike(value) || baseGetTag(value) != objectTag) {
        return false;
      }
      var proto = getPrototype(value);
      if (proto === null) {
        return true;
      }
      var Ctor = hasOwnProperty.call(proto, "constructor") && proto.constructor;
      return typeof Ctor == "function" && Ctor instanceof Ctor && funcToString.call(Ctor) == objectCtorString;
    }
    module.exports = isPlainObject2;
  }
});

// node_modules/lodash/_baseSlice.js
var require_baseSlice = __commonJS({
  "node_modules/lodash/_baseSlice.js"(exports, module) {
    function baseSlice(array, start, end) {
      var index = -1, length = array.length;
      if (start < 0) {
        start = -start > length ? 0 : length + start;
      }
      end = end > length ? length : end;
      if (end < 0) {
        end += length;
      }
      length = start > end ? 0 : end - start >>> 0;
      start >>>= 0;
      var result = Array(length);
      while (++index < length) {
        result[index] = array[index + start];
      }
      return result;
    }
    module.exports = baseSlice;
  }
});

// node_modules/lodash/_castSlice.js
var require_castSlice = __commonJS({
  "node_modules/lodash/_castSlice.js"(exports, module) {
    var baseSlice = require_baseSlice();
    function castSlice(array, start, end) {
      var length = array.length;
      end = end === void 0 ? length : end;
      return !start && end >= length ? array : baseSlice(array, start, end);
    }
    module.exports = castSlice;
  }
});

// node_modules/lodash/_hasUnicode.js
var require_hasUnicode = __commonJS({
  "node_modules/lodash/_hasUnicode.js"(exports, module) {
    var rsAstralRange = "\\ud800-\\udfff";
    var rsComboMarksRange = "\\u0300-\\u036f";
    var reComboHalfMarksRange = "\\ufe20-\\ufe2f";
    var rsComboSymbolsRange = "\\u20d0-\\u20ff";
    var rsComboRange = rsComboMarksRange + reComboHalfMarksRange + rsComboSymbolsRange;
    var rsVarRange = "\\ufe0e\\ufe0f";
    var rsZWJ = "\\u200d";
    var reHasUnicode = RegExp("[" + rsZWJ + rsAstralRange + rsComboRange + rsVarRange + "]");
    function hasUnicode(string) {
      return reHasUnicode.test(string);
    }
    module.exports = hasUnicode;
  }
});

// node_modules/lodash/_asciiToArray.js
var require_asciiToArray = __commonJS({
  "node_modules/lodash/_asciiToArray.js"(exports, module) {
    function asciiToArray(string) {
      return string.split("");
    }
    module.exports = asciiToArray;
  }
});

// node_modules/lodash/_unicodeToArray.js
var require_unicodeToArray = __commonJS({
  "node_modules/lodash/_unicodeToArray.js"(exports, module) {
    var rsAstralRange = "\\ud800-\\udfff";
    var rsComboMarksRange = "\\u0300-\\u036f";
    var reComboHalfMarksRange = "\\ufe20-\\ufe2f";
    var rsComboSymbolsRange = "\\u20d0-\\u20ff";
    var rsComboRange = rsComboMarksRange + reComboHalfMarksRange + rsComboSymbolsRange;
    var rsVarRange = "\\ufe0e\\ufe0f";
    var rsAstral = "[" + rsAstralRange + "]";
    var rsCombo = "[" + rsComboRange + "]";
    var rsFitz = "\\ud83c[\\udffb-\\udfff]";
    var rsModifier = "(?:" + rsCombo + "|" + rsFitz + ")";
    var rsNonAstral = "[^" + rsAstralRange + "]";
    var rsRegional = "(?:\\ud83c[\\udde6-\\uddff]){2}";
    var rsSurrPair = "[\\ud800-\\udbff][\\udc00-\\udfff]";
    var rsZWJ = "\\u200d";
    var reOptMod = rsModifier + "?";
    var rsOptVar = "[" + rsVarRange + "]?";
    var rsOptJoin = "(?:" + rsZWJ + "(?:" + [rsNonAstral, rsRegional, rsSurrPair].join("|") + ")" + rsOptVar + reOptMod + ")*";
    var rsSeq = rsOptVar + reOptMod + rsOptJoin;
    var rsSymbol = "(?:" + [rsNonAstral + rsCombo + "?", rsCombo, rsRegional, rsSurrPair, rsAstral].join("|") + ")";
    var reUnicode = RegExp(rsFitz + "(?=" + rsFitz + ")|" + rsSymbol + rsSeq, "g");
    function unicodeToArray(string) {
      return string.match(reUnicode) || [];
    }
    module.exports = unicodeToArray;
  }
});

// node_modules/lodash/_stringToArray.js
var require_stringToArray = __commonJS({
  "node_modules/lodash/_stringToArray.js"(exports, module) {
    var asciiToArray = require_asciiToArray();
    var hasUnicode = require_hasUnicode();
    var unicodeToArray = require_unicodeToArray();
    function stringToArray(string) {
      return hasUnicode(string) ? unicodeToArray(string) : asciiToArray(string);
    }
    module.exports = stringToArray;
  }
});

// node_modules/lodash/_arrayMap.js
var require_arrayMap = __commonJS({
  "node_modules/lodash/_arrayMap.js"(exports, module) {
    function arrayMap(array, iteratee) {
      var index = -1, length = array == null ? 0 : array.length, result = Array(length);
      while (++index < length) {
        result[index] = iteratee(array[index], index, array);
      }
      return result;
    }
    module.exports = arrayMap;
  }
});

// node_modules/lodash/isSymbol.js
var require_isSymbol = __commonJS({
  "node_modules/lodash/isSymbol.js"(exports, module) {
    var baseGetTag = require_baseGetTag();
    var isObjectLike = require_isObjectLike();
    var symbolTag = "[object Symbol]";
    function isSymbol(value) {
      return typeof value == "symbol" || isObjectLike(value) && baseGetTag(value) == symbolTag;
    }
    module.exports = isSymbol;
  }
});

// node_modules/lodash/_baseToString.js
var require_baseToString = __commonJS({
  "node_modules/lodash/_baseToString.js"(exports, module) {
    var Symbol2 = require_Symbol();
    var arrayMap = require_arrayMap();
    var isArray = require_isArray();
    var isSymbol = require_isSymbol();
    var INFINITY = 1 / 0;
    var symbolProto = Symbol2 ? Symbol2.prototype : void 0;
    var symbolToString = symbolProto ? symbolProto.toString : void 0;
    function baseToString(value) {
      if (typeof value == "string") {
        return value;
      }
      if (isArray(value)) {
        return arrayMap(value, baseToString) + "";
      }
      if (isSymbol(value)) {
        return symbolToString ? symbolToString.call(value) : "";
      }
      var result = value + "";
      return result == "0" && 1 / value == -INFINITY ? "-0" : result;
    }
    module.exports = baseToString;
  }
});

// node_modules/lodash/toString.js
var require_toString = __commonJS({
  "node_modules/lodash/toString.js"(exports, module) {
    var baseToString = require_baseToString();
    function toString(value) {
      return value == null ? "" : baseToString(value);
    }
    module.exports = toString;
  }
});

// node_modules/lodash/_createCaseFirst.js
var require_createCaseFirst = __commonJS({
  "node_modules/lodash/_createCaseFirst.js"(exports, module) {
    var castSlice = require_castSlice();
    var hasUnicode = require_hasUnicode();
    var stringToArray = require_stringToArray();
    var toString = require_toString();
    function createCaseFirst(methodName) {
      return function(string) {
        string = toString(string);
        var strSymbols = hasUnicode(string) ? stringToArray(string) : void 0;
        var chr = strSymbols ? strSymbols[0] : string.charAt(0);
        var trailing = strSymbols ? castSlice(strSymbols, 1).join("") : string.slice(1);
        return chr[methodName]() + trailing;
      };
    }
    module.exports = createCaseFirst;
  }
});

// node_modules/lodash/upperFirst.js
var require_upperFirst = __commonJS({
  "node_modules/lodash/upperFirst.js"(exports, module) {
    var createCaseFirst = require_createCaseFirst();
    var upperFirst2 = createCaseFirst("toUpperCase");
    module.exports = upperFirst2;
  }
});

// node_modules/lodash/memoize.js
var require_memoize = __commonJS({
  "node_modules/lodash/memoize.js"(exports, module) {
    var MapCache = require_MapCache();
    var FUNC_ERROR_TEXT = "Expected a function";
    function memoize(func, resolver) {
      if (typeof func != "function" || resolver != null && typeof resolver != "function") {
        throw new TypeError(FUNC_ERROR_TEXT);
      }
      var memoized = function() {
        var args = arguments, key = resolver ? resolver.apply(this, args) : args[0], cache = memoized.cache;
        if (cache.has(key)) {
          return cache.get(key);
        }
        var result = func.apply(this, args);
        memoized.cache = cache.set(key, result) || cache;
        return result;
      };
      memoized.cache = new (memoize.Cache || MapCache)();
      return memoized;
    }
    memoize.Cache = MapCache;
    module.exports = memoize;
  }
});

// node_modules/lodash/_baseRepeat.js
var require_baseRepeat = __commonJS({
  "node_modules/lodash/_baseRepeat.js"(exports, module) {
    var MAX_SAFE_INTEGER = 9007199254740991;
    var nativeFloor = Math.floor;
    function baseRepeat(string, n) {
      var result = "";
      if (!string || n < 1 || n > MAX_SAFE_INTEGER) {
        return result;
      }
      do {
        if (n % 2) {
          result += string;
        }
        n = nativeFloor(n / 2);
        if (n) {
          string += string;
        }
      } while (n);
      return result;
    }
    module.exports = baseRepeat;
  }
});

// node_modules/lodash/_baseProperty.js
var require_baseProperty = __commonJS({
  "node_modules/lodash/_baseProperty.js"(exports, module) {
    function baseProperty(key) {
      return function(object) {
        return object == null ? void 0 : object[key];
      };
    }
    module.exports = baseProperty;
  }
});

// node_modules/lodash/_asciiSize.js
var require_asciiSize = __commonJS({
  "node_modules/lodash/_asciiSize.js"(exports, module) {
    var baseProperty = require_baseProperty();
    var asciiSize = baseProperty("length");
    module.exports = asciiSize;
  }
});

// node_modules/lodash/_unicodeSize.js
var require_unicodeSize = __commonJS({
  "node_modules/lodash/_unicodeSize.js"(exports, module) {
    var rsAstralRange = "\\ud800-\\udfff";
    var rsComboMarksRange = "\\u0300-\\u036f";
    var reComboHalfMarksRange = "\\ufe20-\\ufe2f";
    var rsComboSymbolsRange = "\\u20d0-\\u20ff";
    var rsComboRange = rsComboMarksRange + reComboHalfMarksRange + rsComboSymbolsRange;
    var rsVarRange = "\\ufe0e\\ufe0f";
    var rsAstral = "[" + rsAstralRange + "]";
    var rsCombo = "[" + rsComboRange + "]";
    var rsFitz = "\\ud83c[\\udffb-\\udfff]";
    var rsModifier = "(?:" + rsCombo + "|" + rsFitz + ")";
    var rsNonAstral = "[^" + rsAstralRange + "]";
    var rsRegional = "(?:\\ud83c[\\udde6-\\uddff]){2}";
    var rsSurrPair = "[\\ud800-\\udbff][\\udc00-\\udfff]";
    var rsZWJ = "\\u200d";
    var reOptMod = rsModifier + "?";
    var rsOptVar = "[" + rsVarRange + "]?";
    var rsOptJoin = "(?:" + rsZWJ + "(?:" + [rsNonAstral, rsRegional, rsSurrPair].join("|") + ")" + rsOptVar + reOptMod + ")*";
    var rsSeq = rsOptVar + reOptMod + rsOptJoin;
    var rsSymbol = "(?:" + [rsNonAstral + rsCombo + "?", rsCombo, rsRegional, rsSurrPair, rsAstral].join("|") + ")";
    var reUnicode = RegExp(rsFitz + "(?=" + rsFitz + ")|" + rsSymbol + rsSeq, "g");
    function unicodeSize(string) {
      var result = reUnicode.lastIndex = 0;
      while (reUnicode.test(string)) {
        ++result;
      }
      return result;
    }
    module.exports = unicodeSize;
  }
});

// node_modules/lodash/_stringSize.js
var require_stringSize = __commonJS({
  "node_modules/lodash/_stringSize.js"(exports, module) {
    var asciiSize = require_asciiSize();
    var hasUnicode = require_hasUnicode();
    var unicodeSize = require_unicodeSize();
    function stringSize(string) {
      return hasUnicode(string) ? unicodeSize(string) : asciiSize(string);
    }
    module.exports = stringSize;
  }
});

// node_modules/lodash/_createPadding.js
var require_createPadding = __commonJS({
  "node_modules/lodash/_createPadding.js"(exports, module) {
    var baseRepeat = require_baseRepeat();
    var baseToString = require_baseToString();
    var castSlice = require_castSlice();
    var hasUnicode = require_hasUnicode();
    var stringSize = require_stringSize();
    var stringToArray = require_stringToArray();
    var nativeCeil = Math.ceil;
    function createPadding(length, chars) {
      chars = chars === void 0 ? " " : baseToString(chars);
      var charsLength = chars.length;
      if (charsLength < 2) {
        return charsLength ? baseRepeat(chars, length) : chars;
      }
      var result = baseRepeat(chars, nativeCeil(length / stringSize(chars)));
      return hasUnicode(chars) ? castSlice(stringToArray(result), 0, length).join("") : result.slice(0, length);
    }
    module.exports = createPadding;
  }
});

// node_modules/lodash/_trimmedEndIndex.js
var require_trimmedEndIndex = __commonJS({
  "node_modules/lodash/_trimmedEndIndex.js"(exports, module) {
    var reWhitespace = /\s/;
    function trimmedEndIndex(string) {
      var index = string.length;
      while (index-- && reWhitespace.test(string.charAt(index))) {
      }
      return index;
    }
    module.exports = trimmedEndIndex;
  }
});

// node_modules/lodash/_baseTrim.js
var require_baseTrim = __commonJS({
  "node_modules/lodash/_baseTrim.js"(exports, module) {
    var trimmedEndIndex = require_trimmedEndIndex();
    var reTrimStart = /^\s+/;
    function baseTrim(string) {
      return string ? string.slice(0, trimmedEndIndex(string) + 1).replace(reTrimStart, "") : string;
    }
    module.exports = baseTrim;
  }
});

// node_modules/lodash/toNumber.js
var require_toNumber = __commonJS({
  "node_modules/lodash/toNumber.js"(exports, module) {
    var baseTrim = require_baseTrim();
    var isObject = require_isObject();
    var isSymbol = require_isSymbol();
    var NAN = 0 / 0;
    var reIsBadHex = /^[-+]0x[0-9a-f]+$/i;
    var reIsBinary = /^0b[01]+$/i;
    var reIsOctal = /^0o[0-7]+$/i;
    var freeParseInt = parseInt;
    function toNumber(value) {
      if (typeof value == "number") {
        return value;
      }
      if (isSymbol(value)) {
        return NAN;
      }
      if (isObject(value)) {
        var other = typeof value.valueOf == "function" ? value.valueOf() : value;
        value = isObject(other) ? other + "" : other;
      }
      if (typeof value != "string") {
        return value === 0 ? value : +value;
      }
      value = baseTrim(value);
      var isBinary = reIsBinary.test(value);
      return isBinary || reIsOctal.test(value) ? freeParseInt(value.slice(2), isBinary ? 2 : 8) : reIsBadHex.test(value) ? NAN : +value;
    }
    module.exports = toNumber;
  }
});

// node_modules/lodash/toFinite.js
var require_toFinite = __commonJS({
  "node_modules/lodash/toFinite.js"(exports, module) {
    var toNumber = require_toNumber();
    var INFINITY = 1 / 0;
    var MAX_INTEGER = 17976931348623157e292;
    function toFinite(value) {
      if (!value) {
        return value === 0 ? value : 0;
      }
      value = toNumber(value);
      if (value === INFINITY || value === -INFINITY) {
        var sign = value < 0 ? -1 : 1;
        return sign * MAX_INTEGER;
      }
      return value === value ? value : 0;
    }
    module.exports = toFinite;
  }
});

// node_modules/lodash/toInteger.js
var require_toInteger = __commonJS({
  "node_modules/lodash/toInteger.js"(exports, module) {
    var toFinite = require_toFinite();
    function toInteger(value) {
      var result = toFinite(value), remainder = result % 1;
      return result === result ? remainder ? result - remainder : result : 0;
    }
    module.exports = toInteger;
  }
});

// node_modules/lodash/padStart.js
var require_padStart = __commonJS({
  "node_modules/lodash/padStart.js"(exports, module) {
    var createPadding = require_createPadding();
    var stringSize = require_stringSize();
    var toInteger = require_toInteger();
    var toString = require_toString();
    function padStart2(string, length, chars) {
      string = toString(string);
      length = toInteger(length);
      var strLength = length ? stringSize(string) : 0;
      return length && strLength < length ? createPadding(length - strLength, chars) + string : string;
    }
    module.exports = padStart2;
  }
});

// node_modules/lodash/capitalize.js
var require_capitalize = __commonJS({
  "node_modules/lodash/capitalize.js"(exports, module) {
    var toString = require_toString();
    var upperFirst2 = require_upperFirst();
    function capitalize2(string) {
      return upperFirst2(toString(string).toLowerCase());
    }
    module.exports = capitalize2;
  }
});

// node_modules/lodash/_basePropertyOf.js
var require_basePropertyOf = __commonJS({
  "node_modules/lodash/_basePropertyOf.js"(exports, module) {
    function basePropertyOf(object) {
      return function(key) {
        return object == null ? void 0 : object[key];
      };
    }
    module.exports = basePropertyOf;
  }
});

// node_modules/lodash/_escapeHtmlChar.js
var require_escapeHtmlChar = __commonJS({
  "node_modules/lodash/_escapeHtmlChar.js"(exports, module) {
    var basePropertyOf = require_basePropertyOf();
    var htmlEscapes = {
      "&": "&amp;",
      "<": "&lt;",
      ">": "&gt;",
      '"': "&quot;",
      "'": "&#39;"
    };
    var escapeHtmlChar = basePropertyOf(htmlEscapes);
    module.exports = escapeHtmlChar;
  }
});

// node_modules/lodash/escape.js
var require_escape = __commonJS({
  "node_modules/lodash/escape.js"(exports, module) {
    var escapeHtmlChar = require_escapeHtmlChar();
    var toString = require_toString();
    var reUnescapedHtml = /[&<>"']/g;
    var reHasUnescapedHtml = RegExp(reUnescapedHtml.source);
    function escape2(string) {
      string = toString(string);
      return string && reHasUnescapedHtml.test(string) ? string.replace(reUnescapedHtml, escapeHtmlChar) : string;
    }
    module.exports = escape2;
  }
});

// node_modules/lodash/_baseIsRegExp.js
var require_baseIsRegExp = __commonJS({
  "node_modules/lodash/_baseIsRegExp.js"(exports, module) {
    var baseGetTag = require_baseGetTag();
    var isObjectLike = require_isObjectLike();
    var regexpTag = "[object RegExp]";
    function baseIsRegExp(value) {
      return isObjectLike(value) && baseGetTag(value) == regexpTag;
    }
    module.exports = baseIsRegExp;
  }
});

// node_modules/lodash/isRegExp.js
var require_isRegExp = __commonJS({
  "node_modules/lodash/isRegExp.js"(exports, module) {
    var baseIsRegExp = require_baseIsRegExp();
    var baseUnary = require_baseUnary();
    var nodeUtil = require_nodeUtil();
    var nodeIsRegExp = nodeUtil && nodeUtil.isRegExp;
    var isRegExp = nodeIsRegExp ? baseUnary(nodeIsRegExp) : baseIsRegExp;
    module.exports = isRegExp;
  }
});

// node_modules/lodash/truncate.js
var require_truncate = __commonJS({
  "node_modules/lodash/truncate.js"(exports, module) {
    var baseToString = require_baseToString();
    var castSlice = require_castSlice();
    var hasUnicode = require_hasUnicode();
    var isObject = require_isObject();
    var isRegExp = require_isRegExp();
    var stringSize = require_stringSize();
    var stringToArray = require_stringToArray();
    var toInteger = require_toInteger();
    var toString = require_toString();
    var DEFAULT_TRUNC_LENGTH = 30;
    var DEFAULT_TRUNC_OMISSION = "...";
    var reFlags = /\w*$/;
    function truncate2(string, options) {
      var length = DEFAULT_TRUNC_LENGTH, omission = DEFAULT_TRUNC_OMISSION;
      if (isObject(options)) {
        var separator = "separator" in options ? options.separator : separator;
        length = "length" in options ? toInteger(options.length) : length;
        omission = "omission" in options ? baseToString(options.omission) : omission;
      }
      string = toString(string);
      var strLength = string.length;
      if (hasUnicode(string)) {
        var strSymbols = stringToArray(string);
        strLength = strSymbols.length;
      }
      if (length >= strLength) {
        return string;
      }
      var end = length - stringSize(omission);
      if (end < 1) {
        return omission;
      }
      var result = strSymbols ? castSlice(strSymbols, 0, end).join("") : string.slice(0, end);
      if (separator === void 0) {
        return result + omission;
      }
      if (strSymbols) {
        end += result.length - end;
      }
      if (isRegExp(separator)) {
        if (string.slice(end).search(separator)) {
          var match, substring = result;
          if (!separator.global) {
            separator = RegExp(separator.source, toString(reFlags.exec(separator)) + "g");
          }
          separator.lastIndex = 0;
          while (match = separator.exec(substring)) {
            var newEnd = match.index;
          }
          result = result.slice(0, newEnd === void 0 ? end : newEnd);
        }
      } else if (string.indexOf(baseToString(separator), end) != end) {
        var index = result.lastIndexOf(separator);
        if (index > -1) {
          result = result.slice(0, index);
        }
      }
      return result + omission;
    }
    module.exports = truncate2;
  }
});

// node_modules/lodash/_baseFindIndex.js
var require_baseFindIndex = __commonJS({
  "node_modules/lodash/_baseFindIndex.js"(exports, module) {
    function baseFindIndex(array, predicate, fromIndex, fromRight) {
      var length = array.length, index = fromIndex + (fromRight ? 1 : -1);
      while (fromRight ? index-- : ++index < length) {
        if (predicate(array[index], index, array)) {
          return index;
        }
      }
      return -1;
    }
    module.exports = baseFindIndex;
  }
});

// node_modules/lodash/_baseIsNaN.js
var require_baseIsNaN = __commonJS({
  "node_modules/lodash/_baseIsNaN.js"(exports, module) {
    function baseIsNaN(value) {
      return value !== value;
    }
    module.exports = baseIsNaN;
  }
});

// node_modules/lodash/_strictIndexOf.js
var require_strictIndexOf = __commonJS({
  "node_modules/lodash/_strictIndexOf.js"(exports, module) {
    function strictIndexOf(array, value, fromIndex) {
      var index = fromIndex - 1, length = array.length;
      while (++index < length) {
        if (array[index] === value) {
          return index;
        }
      }
      return -1;
    }
    module.exports = strictIndexOf;
  }
});

// node_modules/lodash/_baseIndexOf.js
var require_baseIndexOf = __commonJS({
  "node_modules/lodash/_baseIndexOf.js"(exports, module) {
    var baseFindIndex = require_baseFindIndex();
    var baseIsNaN = require_baseIsNaN();
    var strictIndexOf = require_strictIndexOf();
    function baseIndexOf(array, value, fromIndex) {
      return value === value ? strictIndexOf(array, value, fromIndex) : baseFindIndex(array, baseIsNaN, fromIndex);
    }
    module.exports = baseIndexOf;
  }
});

// node_modules/lodash/_arrayIncludes.js
var require_arrayIncludes = __commonJS({
  "node_modules/lodash/_arrayIncludes.js"(exports, module) {
    var baseIndexOf = require_baseIndexOf();
    function arrayIncludes(array, value) {
      var length = array == null ? 0 : array.length;
      return !!length && baseIndexOf(array, value, 0) > -1;
    }
    module.exports = arrayIncludes;
  }
});

// node_modules/lodash/_arrayIncludesWith.js
var require_arrayIncludesWith = __commonJS({
  "node_modules/lodash/_arrayIncludesWith.js"(exports, module) {
    function arrayIncludesWith(array, value, comparator) {
      var index = -1, length = array == null ? 0 : array.length;
      while (++index < length) {
        if (comparator(value, array[index])) {
          return true;
        }
      }
      return false;
    }
    module.exports = arrayIncludesWith;
  }
});

// node_modules/lodash/noop.js
var require_noop = __commonJS({
  "node_modules/lodash/noop.js"(exports, module) {
    function noop() {
    }
    module.exports = noop;
  }
});

// node_modules/lodash/_createSet.js
var require_createSet = __commonJS({
  "node_modules/lodash/_createSet.js"(exports, module) {
    var Set = require_Set();
    var noop = require_noop();
    var setToArray = require_setToArray();
    var INFINITY = 1 / 0;
    var createSet = !(Set && 1 / setToArray(new Set([, -0]))[1] == INFINITY) ? noop : function(values) {
      return new Set(values);
    };
    module.exports = createSet;
  }
});

// node_modules/lodash/_baseUniq.js
var require_baseUniq = __commonJS({
  "node_modules/lodash/_baseUniq.js"(exports, module) {
    var SetCache = require_SetCache();
    var arrayIncludes = require_arrayIncludes();
    var arrayIncludesWith = require_arrayIncludesWith();
    var cacheHas = require_cacheHas();
    var createSet = require_createSet();
    var setToArray = require_setToArray();
    var LARGE_ARRAY_SIZE = 200;
    function baseUniq(array, iteratee, comparator) {
      var index = -1, includes = arrayIncludes, length = array.length, isCommon = true, result = [], seen = result;
      if (comparator) {
        isCommon = false;
        includes = arrayIncludesWith;
      } else if (length >= LARGE_ARRAY_SIZE) {
        var set = iteratee ? null : createSet(array);
        if (set) {
          return setToArray(set);
        }
        isCommon = false;
        includes = cacheHas;
        seen = new SetCache();
      } else {
        seen = iteratee ? [] : result;
      }
      outer:
        while (++index < length) {
          var value = array[index], computed = iteratee ? iteratee(value) : value;
          value = comparator || value !== 0 ? value : 0;
          if (isCommon && computed === computed) {
            var seenIndex = seen.length;
            while (seenIndex--) {
              if (seen[seenIndex] === computed) {
                continue outer;
              }
            }
            if (iteratee) {
              seen.push(computed);
            }
            result.push(value);
          } else if (!includes(seen, computed, comparator)) {
            if (seen !== result) {
              seen.push(computed);
            }
            result.push(value);
          }
        }
      return result;
    }
    module.exports = baseUniq;
  }
});

// node_modules/lodash/uniqWith.js
var require_uniqWith = __commonJS({
  "node_modules/lodash/uniqWith.js"(exports, module) {
    var baseUniq = require_baseUniq();
    function uniqWith2(array, comparator) {
      comparator = typeof comparator == "function" ? comparator : void 0;
      return array && array.length ? baseUniq(array, void 0, comparator) : [];
    }
    module.exports = uniqWith2;
  }
});

// node_modules/lodash/_baseIsMatch.js
var require_baseIsMatch = __commonJS({
  "node_modules/lodash/_baseIsMatch.js"(exports, module) {
    var Stack = require_Stack();
    var baseIsEqual = require_baseIsEqual();
    var COMPARE_PARTIAL_FLAG = 1;
    var COMPARE_UNORDERED_FLAG = 2;
    function baseIsMatch(object, source, matchData, customizer) {
      var index = matchData.length, length = index, noCustomizer = !customizer;
      if (object == null) {
        return !length;
      }
      object = Object(object);
      while (index--) {
        var data = matchData[index];
        if (noCustomizer && data[2] ? data[1] !== object[data[0]] : !(data[0] in object)) {
          return false;
        }
      }
      while (++index < length) {
        data = matchData[index];
        var key = data[0], objValue = object[key], srcValue = data[1];
        if (noCustomizer && data[2]) {
          if (objValue === void 0 && !(key in object)) {
            return false;
          }
        } else {
          var stack = new Stack();
          if (customizer) {
            var result = customizer(objValue, srcValue, key, object, source, stack);
          }
          if (!(result === void 0 ? baseIsEqual(srcValue, objValue, COMPARE_PARTIAL_FLAG | COMPARE_UNORDERED_FLAG, customizer, stack) : result)) {
            return false;
          }
        }
      }
      return true;
    }
    module.exports = baseIsMatch;
  }
});

// node_modules/lodash/_isStrictComparable.js
var require_isStrictComparable = __commonJS({
  "node_modules/lodash/_isStrictComparable.js"(exports, module) {
    var isObject = require_isObject();
    function isStrictComparable(value) {
      return value === value && !isObject(value);
    }
    module.exports = isStrictComparable;
  }
});

// node_modules/lodash/_getMatchData.js
var require_getMatchData = __commonJS({
  "node_modules/lodash/_getMatchData.js"(exports, module) {
    var isStrictComparable = require_isStrictComparable();
    var keys = require_keys();
    function getMatchData(object) {
      var result = keys(object), length = result.length;
      while (length--) {
        var key = result[length], value = object[key];
        result[length] = [key, value, isStrictComparable(value)];
      }
      return result;
    }
    module.exports = getMatchData;
  }
});

// node_modules/lodash/_matchesStrictComparable.js
var require_matchesStrictComparable = __commonJS({
  "node_modules/lodash/_matchesStrictComparable.js"(exports, module) {
    function matchesStrictComparable(key, srcValue) {
      return function(object) {
        if (object == null) {
          return false;
        }
        return object[key] === srcValue && (srcValue !== void 0 || key in Object(object));
      };
    }
    module.exports = matchesStrictComparable;
  }
});

// node_modules/lodash/_baseMatches.js
var require_baseMatches = __commonJS({
  "node_modules/lodash/_baseMatches.js"(exports, module) {
    var baseIsMatch = require_baseIsMatch();
    var getMatchData = require_getMatchData();
    var matchesStrictComparable = require_matchesStrictComparable();
    function baseMatches(source) {
      var matchData = getMatchData(source);
      if (matchData.length == 1 && matchData[0][2]) {
        return matchesStrictComparable(matchData[0][0], matchData[0][1]);
      }
      return function(object) {
        return object === source || baseIsMatch(object, source, matchData);
      };
    }
    module.exports = baseMatches;
  }
});

// node_modules/lodash/_isKey.js
var require_isKey = __commonJS({
  "node_modules/lodash/_isKey.js"(exports, module) {
    var isArray = require_isArray();
    var isSymbol = require_isSymbol();
    var reIsDeepProp = /\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/;
    var reIsPlainProp = /^\w*$/;
    function isKey(value, object) {
      if (isArray(value)) {
        return false;
      }
      var type = typeof value;
      if (type == "number" || type == "symbol" || type == "boolean" || value == null || isSymbol(value)) {
        return true;
      }
      return reIsPlainProp.test(value) || !reIsDeepProp.test(value) || object != null && value in Object(object);
    }
    module.exports = isKey;
  }
});

// node_modules/lodash/_memoizeCapped.js
var require_memoizeCapped = __commonJS({
  "node_modules/lodash/_memoizeCapped.js"(exports, module) {
    var memoize = require_memoize();
    var MAX_MEMOIZE_SIZE = 500;
    function memoizeCapped(func) {
      var result = memoize(func, function(key) {
        if (cache.size === MAX_MEMOIZE_SIZE) {
          cache.clear();
        }
        return key;
      });
      var cache = result.cache;
      return result;
    }
    module.exports = memoizeCapped;
  }
});

// node_modules/lodash/_stringToPath.js
var require_stringToPath = __commonJS({
  "node_modules/lodash/_stringToPath.js"(exports, module) {
    var memoizeCapped = require_memoizeCapped();
    var rePropName = /[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g;
    var reEscapeChar = /\\(\\)?/g;
    var stringToPath = memoizeCapped(function(string) {
      var result = [];
      if (string.charCodeAt(0) === 46) {
        result.push("");
      }
      string.replace(rePropName, function(match, number, quote, subString) {
        result.push(quote ? subString.replace(reEscapeChar, "$1") : number || match);
      });
      return result;
    });
    module.exports = stringToPath;
  }
});

// node_modules/lodash/_castPath.js
var require_castPath = __commonJS({
  "node_modules/lodash/_castPath.js"(exports, module) {
    var isArray = require_isArray();
    var isKey = require_isKey();
    var stringToPath = require_stringToPath();
    var toString = require_toString();
    function castPath(value, object) {
      if (isArray(value)) {
        return value;
      }
      return isKey(value, object) ? [value] : stringToPath(toString(value));
    }
    module.exports = castPath;
  }
});

// node_modules/lodash/_toKey.js
var require_toKey = __commonJS({
  "node_modules/lodash/_toKey.js"(exports, module) {
    var isSymbol = require_isSymbol();
    var INFINITY = 1 / 0;
    function toKey(value) {
      if (typeof value == "string" || isSymbol(value)) {
        return value;
      }
      var result = value + "";
      return result == "0" && 1 / value == -INFINITY ? "-0" : result;
    }
    module.exports = toKey;
  }
});

// node_modules/lodash/_baseGet.js
var require_baseGet = __commonJS({
  "node_modules/lodash/_baseGet.js"(exports, module) {
    var castPath = require_castPath();
    var toKey = require_toKey();
    function baseGet(object, path) {
      path = castPath(path, object);
      var index = 0, length = path.length;
      while (object != null && index < length) {
        object = object[toKey(path[index++])];
      }
      return index && index == length ? object : void 0;
    }
    module.exports = baseGet;
  }
});

// node_modules/lodash/get.js
var require_get = __commonJS({
  "node_modules/lodash/get.js"(exports, module) {
    var baseGet = require_baseGet();
    function get2(object, path, defaultValue) {
      var result = object == null ? void 0 : baseGet(object, path);
      return result === void 0 ? defaultValue : result;
    }
    module.exports = get2;
  }
});

// node_modules/lodash/_baseHasIn.js
var require_baseHasIn = __commonJS({
  "node_modules/lodash/_baseHasIn.js"(exports, module) {
    function baseHasIn(object, key) {
      return object != null && key in Object(object);
    }
    module.exports = baseHasIn;
  }
});

// node_modules/lodash/_hasPath.js
var require_hasPath = __commonJS({
  "node_modules/lodash/_hasPath.js"(exports, module) {
    var castPath = require_castPath();
    var isArguments = require_isArguments();
    var isArray = require_isArray();
    var isIndex = require_isIndex();
    var isLength = require_isLength();
    var toKey = require_toKey();
    function hasPath(object, path, hasFunc) {
      path = castPath(path, object);
      var index = -1, length = path.length, result = false;
      while (++index < length) {
        var key = toKey(path[index]);
        if (!(result = object != null && hasFunc(object, key))) {
          break;
        }
        object = object[key];
      }
      if (result || ++index != length) {
        return result;
      }
      length = object == null ? 0 : object.length;
      return !!length && isLength(length) && isIndex(key, length) && (isArray(object) || isArguments(object));
    }
    module.exports = hasPath;
  }
});

// node_modules/lodash/hasIn.js
var require_hasIn = __commonJS({
  "node_modules/lodash/hasIn.js"(exports, module) {
    var baseHasIn = require_baseHasIn();
    var hasPath = require_hasPath();
    function hasIn(object, path) {
      return object != null && hasPath(object, path, baseHasIn);
    }
    module.exports = hasIn;
  }
});

// node_modules/lodash/_baseMatchesProperty.js
var require_baseMatchesProperty = __commonJS({
  "node_modules/lodash/_baseMatchesProperty.js"(exports, module) {
    var baseIsEqual = require_baseIsEqual();
    var get2 = require_get();
    var hasIn = require_hasIn();
    var isKey = require_isKey();
    var isStrictComparable = require_isStrictComparable();
    var matchesStrictComparable = require_matchesStrictComparable();
    var toKey = require_toKey();
    var COMPARE_PARTIAL_FLAG = 1;
    var COMPARE_UNORDERED_FLAG = 2;
    function baseMatchesProperty(path, srcValue) {
      if (isKey(path) && isStrictComparable(srcValue)) {
        return matchesStrictComparable(toKey(path), srcValue);
      }
      return function(object) {
        var objValue = get2(object, path);
        return objValue === void 0 && objValue === srcValue ? hasIn(object, path) : baseIsEqual(srcValue, objValue, COMPARE_PARTIAL_FLAG | COMPARE_UNORDERED_FLAG);
      };
    }
    module.exports = baseMatchesProperty;
  }
});

// node_modules/lodash/identity.js
var require_identity = __commonJS({
  "node_modules/lodash/identity.js"(exports, module) {
    function identity(value) {
      return value;
    }
    module.exports = identity;
  }
});

// node_modules/lodash/_basePropertyDeep.js
var require_basePropertyDeep = __commonJS({
  "node_modules/lodash/_basePropertyDeep.js"(exports, module) {
    var baseGet = require_baseGet();
    function basePropertyDeep(path) {
      return function(object) {
        return baseGet(object, path);
      };
    }
    module.exports = basePropertyDeep;
  }
});

// node_modules/lodash/property.js
var require_property = __commonJS({
  "node_modules/lodash/property.js"(exports, module) {
    var baseProperty = require_baseProperty();
    var basePropertyDeep = require_basePropertyDeep();
    var isKey = require_isKey();
    var toKey = require_toKey();
    function property(path) {
      return isKey(path) ? baseProperty(toKey(path)) : basePropertyDeep(path);
    }
    module.exports = property;
  }
});

// node_modules/lodash/_baseIteratee.js
var require_baseIteratee = __commonJS({
  "node_modules/lodash/_baseIteratee.js"(exports, module) {
    var baseMatches = require_baseMatches();
    var baseMatchesProperty = require_baseMatchesProperty();
    var identity = require_identity();
    var isArray = require_isArray();
    var property = require_property();
    function baseIteratee(value) {
      if (typeof value == "function") {
        return value;
      }
      if (value == null) {
        return identity;
      }
      if (typeof value == "object") {
        return isArray(value) ? baseMatchesProperty(value[0], value[1]) : baseMatches(value);
      }
      return property(value);
    }
    module.exports = baseIteratee;
  }
});

// node_modules/lodash/uniqBy.js
var require_uniqBy = __commonJS({
  "node_modules/lodash/uniqBy.js"(exports, module) {
    var baseIteratee = require_baseIteratee();
    var baseUniq = require_baseUniq();
    function uniqBy2(array, iteratee) {
      return array && array.length ? baseUniq(array, baseIteratee(iteratee, 2)) : [];
    }
    module.exports = uniqBy2;
  }
});

// node_modules/amis-formula/node_modules/tslib/tslib.es6.mjs
var extendStatics = function(d, b) {
  extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
    d2.__proto__ = b2;
  } || function(d2, b2) {
    for (var p in b2)
      if (Object.prototype.hasOwnProperty.call(b2, p))
        d2[p] = b2[p];
  };
  return extendStatics(d, b);
};
function __extends(d, b) {
  if (typeof b !== "function" && b !== null)
    throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
  extendStatics(d, b);
  function __() {
    this.constructor = d;
  }
  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
}
var __assign = function() {
  __assign = Object.assign || function __assign2(t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];
      for (var p in s)
        if (Object.prototype.hasOwnProperty.call(s, p))
          t[p] = s[p];
    }
    return t;
  };
  return __assign.apply(this, arguments);
};
function __awaiter(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
}
function __generator(thisArg, body) {
  var _ = { label: 0, sent: function() {
    if (t[0] & 1)
      throw t[1];
    return t[1];
  }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() {
    return this;
  }), g;
  function verb(n) {
    return function(v) {
      return step([n, v]);
    };
  }
  function step(op) {
    if (f)
      throw new TypeError("Generator is already executing.");
    while (g && (g = 0, op[0] && (_ = 0)), _)
      try {
        if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done)
          return t;
        if (y = 0, t)
          op = [op[0] & 2, t.value];
        switch (op[0]) {
          case 0:
          case 1:
            t = op;
            break;
          case 4:
            _.label++;
            return { value: op[1], done: false };
          case 5:
            _.label++;
            y = op[1];
            op = [0];
            continue;
          case 7:
            op = _.ops.pop();
            _.trys.pop();
            continue;
          default:
            if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
              _ = 0;
              continue;
            }
            if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
              _.label = op[1];
              break;
            }
            if (op[0] === 6 && _.label < t[1]) {
              _.label = t[1];
              t = op;
              break;
            }
            if (t && _.label < t[2]) {
              _.label = t[2];
              _.ops.push(op);
              break;
            }
            if (t[2])
              _.ops.pop();
            _.trys.pop();
            continue;
        }
        op = body.call(thisArg, _);
      } catch (e) {
        op = [6, e];
        y = 0;
      } finally {
        f = t = 0;
      }
    if (op[0] & 5)
      throw op[1];
    return { value: op[0] ? op[1] : void 0, done: true };
  }
}
function __values(o) {
  var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
  if (m)
    return m.call(o);
  if (o && typeof o.length === "number")
    return {
      next: function() {
        if (o && i >= o.length)
          o = void 0;
        return { value: o && o[i++], done: !o };
      }
    };
  throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
}
function __read(o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m)
    return o;
  var i = m.call(o), r, ar = [], e;
  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done)
      ar.push(r.value);
  } catch (error) {
    e = { error };
  } finally {
    try {
      if (r && !r.done && (m = i["return"]))
        m.call(i);
    } finally {
      if (e)
        throw e.error;
    }
  }
  return ar;
}
function __spreadArray(to, from, pack) {
  if (pack || arguments.length === 2)
    for (var i = 0, l = from.length, ar; i < l; i++) {
      if (ar || !(i in from)) {
        if (!ar)
          ar = Array.prototype.slice.call(from, 0, i);
        ar[i] = from[i];
      }
    }
  return to.concat(ar || Array.prototype.slice.call(from));
}

// node_modules/amis-formula/esm/evalutor.js
init_moment();
var import_upperFirst = __toESM(require_upperFirst());
var import_padStart = __toESM(require_padStart());
var import_capitalize = __toESM(require_capitalize());
var import_escape = __toESM(require_escape());
var import_truncate = __toESM(require_truncate());
var import_uniqWith = __toESM(require_uniqWith());
var import_uniqBy = __toESM(require_uniqBy());
var import_isEqual = __toESM(require_isEqual());
var import_isPlainObject = __toESM(require_isPlainObject());
var import_get = __toESM(require_get());

// node_modules/amis-formula/esm/error.js
var FormulaEvalError = (
  /** @class */
  function(_super) {
    __extends(FormulaEvalError2, _super);
    function FormulaEvalError2(message) {
      var _this = _super.call(this, message) || this;
      _this.name = "FormulaEvalError";
      return _this;
    }
    return FormulaEvalError2;
  }(Error)
);

// node_modules/amis-formula/esm/evalutor.js
var Evaluator = (
  /** @class */
  function() {
    function Evaluator2(context, options) {
      if (options === void 0) {
        options = {
          defaultFilter: "html"
        };
      }
      this.options = options;
      this.functions = {};
      this.contextStack = [];
      this.context = context;
      this.contextStack.push(function(varname) {
        return varname === "&" ? context : context === null || context === void 0 ? void 0 : context[varname];
      });
      this.filters = __assign(__assign(__assign({}, Evaluator2.defaultFilters), this.filters), options === null || options === void 0 ? void 0 : options.filters);
      this.functions = __assign(__assign(__assign({}, Evaluator2.defaultFunctions), this.functions), options === null || options === void 0 ? void 0 : options.functions);
    }
    Evaluator2.extendDefaultFilters = function(filters) {
      Evaluator2.defaultFilters = __assign(__assign({}, Evaluator2.defaultFilters), filters);
    };
    Evaluator2.extendDefaultFunctions = function(funtions) {
      Evaluator2.defaultFunctions = __assign(__assign({}, Evaluator2.defaultFunctions), funtions);
    };
    Evaluator2.prototype.evalute = function(ast) {
      if (ast && ast.type) {
        var name_1 = ast.type.replace(/(?:_|\-)(\w)/g, function(_, l) {
          return l.toUpperCase();
        });
        var fn = this.functions[name_1] || this[name_1];
        if (!fn) {
          throw new Error("".concat(ast.type, " unkown."));
        }
        return fn.call(this, ast);
      } else {
        return ast;
      }
    };
    Evaluator2.prototype.document = function(ast) {
      var _this = this;
      if (!ast.body.length) {
        return void 0;
      }
      var isString = ast.body.length > 1;
      var content = ast.body.map(function(item) {
        var result = _this.evalute(item);
        if (isString && result == null) {
          return "";
        }
        return result;
      });
      return content.length === 1 ? content[0] : content.join("");
    };
    Evaluator2.prototype.filter = function(ast) {
      var _this = this;
      var input = this.evalute(ast.input);
      var filters = ast.filters.concat();
      var context = {
        filter: void 0,
        data: this.context,
        restFilters: filters
      };
      while (filters.length) {
        var filter = filters.shift();
        var fn = this.filters[filter.name];
        if (!fn) {
          throw new Error("filter `".concat(filter.name, "` not exists."));
        }
        context.filter = filter;
        input = fn.apply(context, [input].concat(filter.args.map(function(item) {
          if ((item === null || item === void 0 ? void 0 : item.type) === "mixed") {
            return item.body.map(function(item2) {
              return typeof item2 === "string" ? item2 : _this.evalute(item2);
            }).join("");
          } else if (item.type) {
            return _this.evalute(item);
          }
          return item;
        })));
      }
      return input;
    };
    Evaluator2.prototype.raw = function(ast) {
      return ast.value;
    };
    Evaluator2.prototype.script = function(ast) {
      var _a;
      var defaultFilter = this.options.defaultFilter;
      if (defaultFilter && ~["getter", "variable"].indexOf((_a = ast.body) === null || _a === void 0 ? void 0 : _a.type)) {
        ast = __assign(__assign({}, ast), { body: {
          type: "filter",
          input: ast.body,
          filters: [
            {
              name: defaultFilter.replace(/^\s*\|\s*/, ""),
              args: []
            }
          ]
        } });
      }
      return this.evalute(ast.body);
    };
    Evaluator2.prototype.expressionList = function(ast) {
      var _this = this;
      return ast.body.reduce(function(prev, current) {
        return _this.evalute(current);
      });
    };
    Evaluator2.prototype.template = function(ast) {
      var _this = this;
      return ast.body.map(function(arg) {
        return _this.evalute(arg);
      }).join("");
    };
    Evaluator2.prototype.templateRaw = function(ast) {
      return ast.value;
    };
    Evaluator2.prototype.getter = function(ast) {
      var _a;
      var host = this.evalute(ast.host);
      var key = this.evalute(ast.key);
      if (typeof key === "undefined" && ((_a = ast.key) === null || _a === void 0 ? void 0 : _a.type) === "variable") {
        key = ast.key.name;
      }
      return host === null || host === void 0 ? void 0 : host[key];
    };
    Evaluator2.prototype.unary = function(ast) {
      var value = this.evalute(ast.value);
      switch (ast.op) {
        case "+":
          return +value;
        case "-":
          return -value;
        case "~":
          return ~value;
        case "!":
          return !value;
      }
    };
    Evaluator2.prototype.formatNumber = function(value, int) {
      if (int === void 0) {
        int = false;
      }
      var typeName = typeof value;
      if (typeName === "string") {
        return (int ? parseInt(value, 10) : parseFloat(value)) || 0;
      } else if (typeName === "number" && int) {
        return Math.round(value);
      }
      return value !== null && value !== void 0 ? value : 0;
    };
    Evaluator2.prototype.isValidValue = function(value) {
      return typeof value === "number" || typeof value === "string" && /^\d+(\.\d+)?$/.test(value);
    };
    Evaluator2.prototype.power = function(ast) {
      var left = this.evalute(ast.left);
      var right = this.evalute(ast.right);
      if (!this.isValidValue(left) || !this.isValidValue(right)) {
        return left;
      }
      return Math.pow(left, right);
    };
    Evaluator2.prototype.multiply = function(ast) {
      var left = this.evalute(ast.left);
      var right = this.evalute(ast.right);
      return stripNumber(this.formatNumber(left) * this.formatNumber(right));
    };
    Evaluator2.prototype.divide = function(ast) {
      var left = this.evalute(ast.left);
      var right = this.evalute(ast.right);
      return stripNumber(this.formatNumber(left) / this.formatNumber(right));
    };
    Evaluator2.prototype.remainder = function(ast) {
      var left = this.evalute(ast.left);
      var right = this.evalute(ast.right);
      return this.formatNumber(left) % this.formatNumber(right);
    };
    Evaluator2.prototype.add = function(ast) {
      var left = this.evalute(ast.left);
      var right = this.evalute(ast.right);
      if (isNaN(left) || isNaN(right)) {
        return left + right;
      }
      return stripNumber(this.formatNumber(left) + this.formatNumber(right));
    };
    Evaluator2.prototype.minus = function(ast) {
      var left = this.evalute(ast.left);
      var right = this.evalute(ast.right);
      return stripNumber(this.formatNumber(left) - this.formatNumber(right));
    };
    Evaluator2.prototype.shift = function(ast) {
      var left = this.evalute(ast.left);
      var right = this.formatNumber(this.evalute(ast.right), true);
      if (ast.op === "<<") {
        return left << right;
      } else if (ast.op == ">>") {
        return left >> right;
      } else {
        return left >>> right;
      }
    };
    Evaluator2.prototype.lt = function(ast) {
      var left = this.evalute(ast.left);
      var right = this.evalute(ast.right);
      return left < right;
    };
    Evaluator2.prototype.gt = function(ast) {
      var left = this.evalute(ast.left);
      var right = this.evalute(ast.right);
      return left > right;
    };
    Evaluator2.prototype.le = function(ast) {
      var left = this.evalute(ast.left);
      var right = this.evalute(ast.right);
      return left <= right;
    };
    Evaluator2.prototype.ge = function(ast) {
      var left = this.evalute(ast.left);
      var right = this.evalute(ast.right);
      return left >= right;
    };
    Evaluator2.prototype.eq = function(ast) {
      var left = this.evalute(ast.left);
      var right = this.evalute(ast.right);
      return left == right;
    };
    Evaluator2.prototype.ne = function(ast) {
      var left = this.evalute(ast.left);
      var right = this.evalute(ast.right);
      return left != right;
    };
    Evaluator2.prototype.streq = function(ast) {
      var left = this.evalute(ast.left);
      var right = this.evalute(ast.right);
      return left === right;
    };
    Evaluator2.prototype.strneq = function(ast) {
      var left = this.evalute(ast.left);
      var right = this.evalute(ast.right);
      return left !== right;
    };
    Evaluator2.prototype.binary = function(ast) {
      var left = this.evalute(ast.left);
      var right = this.evalute(ast.right);
      if (ast.op === "&") {
        return left & right;
      } else if (ast.op === "^") {
        return left ^ right;
      } else {
        return left | right;
      }
    };
    Evaluator2.prototype.and = function(ast) {
      var left = this.evalute(ast.left);
      return left && this.evalute(ast.right);
    };
    Evaluator2.prototype.or = function(ast) {
      var left = this.evalute(ast.left);
      return left || this.evalute(ast.right);
    };
    Evaluator2.prototype.number = function(ast) {
      return ast.value;
    };
    Evaluator2.prototype.convertHostGetterToVariable = function(ast) {
      var _a, _b;
      if (ast.type !== "getter") {
        return ast;
      }
      var gettter = ast;
      var keys = [];
      while (((_a = gettter.host) === null || _a === void 0 ? void 0 : _a.type) === "getter") {
        keys.push("host");
        gettter = gettter.host;
      }
      if (((_b = gettter.host) === null || _b === void 0 ? void 0 : _b.type) === "variable" && gettter.host.name === "&") {
        var ret = {
          host: ast
        };
        var host = keys.reduce(function(host2, key) {
          host2[key] = __assign({}, host2[key]);
          return host2[key];
        }, ret);
        host.host = {
          start: host.host.start,
          end: host.host.end,
          type: "variable",
          name: this.evalute(host.host.key)
        };
        return ret.host;
      }
      return ast;
    };
    Evaluator2.prototype.nsVariable = function(ast) {
      var _this = this;
      var body = ast.body;
      if (ast.namespace === "window") {
        this.contextStack.push(function(name) {
          return name === "&" ? window : window[name];
        });
      } else if (ast.namespace === "cookie") {
        body = this.convertHostGetterToVariable(body);
        this.contextStack.push(function(name) {
          return getCookie(name);
        });
      } else if (ast.namespace === "ls" || ast.namespace === "ss") {
        var ns_1 = ast.namespace;
        body = this.convertHostGetterToVariable(body);
        this.contextStack.push(function(name) {
          var raw = ns_1 === "ss" ? sessionStorage.getItem(name) : localStorage.getItem(name);
          if (typeof raw === "string") {
            if (/^\d+$/.test(raw)) {
              var parsed = JSON.parse(raw);
              return "".concat(parsed) === raw ? parsed : raw;
            }
            return parseJson(raw, raw);
          }
          return void 0;
        });
      } else {
        throw new Error("Unsupported namespace: " + ast.namespace);
      }
      var result = this.evalute(body);
      (result === null || result === void 0 ? void 0 : result.then) ? result.then(function() {
        return _this.contextStack.pop();
      }) : this.contextStack.pop();
      return result;
    };
    Evaluator2.prototype.variable = function(ast) {
      var contextGetter = this.contextStack[this.contextStack.length - 1];
      return contextGetter(ast.name);
    };
    Evaluator2.prototype.identifier = function(ast) {
      return ast.name;
    };
    Evaluator2.prototype.array = function(ast) {
      var _this = this;
      return ast.members.map(function(member) {
        return _this.evalute(member);
      });
    };
    Evaluator2.prototype.literal = function(ast) {
      return ast.value;
    };
    Evaluator2.prototype.string = function(ast) {
      return ast.value;
    };
    Evaluator2.prototype.object = function(ast) {
      var _this = this;
      var object = {};
      ast.members.forEach(function(_a) {
        var key = _a.key, value = _a.value;
        object[_this.evalute(key)] = _this.evalute(value);
      });
      return object;
    };
    Evaluator2.prototype.conditional = function(ast) {
      return this.evalute(ast.test) ? this.evalute(ast.consequent) : this.evalute(ast.alternate);
    };
    Evaluator2.prototype.funcCall = function(ast) {
      var _this = this;
      var fnName = "fn".concat(ast.identifier);
      var fn = this.functions[fnName] || this[fnName] || this.filters.hasOwnProperty(ast.identifier) && this.filters[ast.identifier];
      if (!fn) {
        throw new FormulaEvalError("".concat(ast.identifier, "函数没有定义"));
      }
      var args = ast.args;
      if (~["IF", "AND", "OR", "XOR", "IFS"].indexOf(ast.identifier)) {
        args = args.map(function(a) {
          return function() {
            return _this.evalute(a);
          };
        });
      } else {
        args = args.map(function(a) {
          return _this.evalute(a);
        });
      }
      return fn.apply(this, args);
    };
    Evaluator2.prototype.anonymousFunction = function(ast) {
      return ast;
    };
    Evaluator2.prototype.callAnonymousFunction = function(ast, args) {
      var ctx = createObject(this.contextStack[this.contextStack.length - 1]("&") || {}, {});
      ast.args.forEach(function(arg) {
        if (arg.type !== "variable") {
          throw new Error("expected a variable as argument");
        }
        ctx[arg.name] = args.shift();
      });
      this.contextStack.push(function(varName) {
        return varName === "&" ? ctx : ctx[varName];
      });
      var result = this.evalute(ast.return);
      this.contextStack.pop();
      return result;
    };
    Evaluator2.prototype.fnIF = function(condition, trueValue, falseValue) {
      return condition() ? trueValue() : falseValue();
    };
    Evaluator2.prototype.fnAND = function() {
      var condtions = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        condtions[_i] = arguments[_i];
      }
      return condtions.every(function(c) {
        return c();
      });
    };
    Evaluator2.prototype.fnOR = function() {
      var condtions = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        condtions[_i] = arguments[_i];
      }
      return condtions.some(function(c) {
        return c();
      });
    };
    Evaluator2.prototype.fnXOR = function() {
      var condtions = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        condtions[_i] = arguments[_i];
      }
      return !!(condtions.filter(function(c) {
        return c();
      }).length % 2);
    };
    Evaluator2.prototype.fnIFS = function() {
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      if (args.length % 2) {
        args.splice(args.length - 1, 0, function() {
          return true;
        });
      }
      while (args.length) {
        var c = args.shift();
        var v = args.shift();
        if (c()) {
          return v();
        }
      }
      return;
    };
    Evaluator2.prototype.fnABS = function(a) {
      a = this.formatNumber(a);
      return Math.abs(a);
    };
    Evaluator2.prototype.fnMAX = function() {
      var _this = this;
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      var arr = normalizeArgs(args);
      return Math.max.apply(Math, arr.map(function(item) {
        return _this.formatNumber(item);
      }));
    };
    Evaluator2.prototype.fnMIN = function() {
      var _this = this;
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      var arr = normalizeArgs(args);
      return Math.min.apply(Math, arr.map(function(item) {
        return _this.formatNumber(item);
      }));
    };
    Evaluator2.prototype.fnSUM = function() {
      var _this = this;
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      var arr = normalizeArgs(args);
      return arr.reduce(function(sum, a) {
        return sum + _this.formatNumber(a) || 0;
      }, 0);
    };
    Evaluator2.prototype.fnINT = function(n) {
      return Math.floor(this.formatNumber(n));
    };
    Evaluator2.prototype.fnMOD = function(a, b) {
      return this.formatNumber(a) % this.formatNumber(b);
    };
    Evaluator2.prototype.fnPI = function() {
      return Math.PI;
    };
    Evaluator2.prototype.fnROUND = function(a, b) {
      if (b === void 0) {
        b = 2;
      }
      a = this.formatNumber(a);
      b = this.formatNumber(b);
      var bResult = Math.round(b);
      if (bResult) {
        var c = Math.pow(10, bResult);
        return Math.round(a * c) / c;
      }
      return Math.round(a);
    };
    Evaluator2.prototype.fnFLOOR = function(a, b) {
      if (b === void 0) {
        b = 2;
      }
      a = this.formatNumber(a);
      b = this.formatNumber(b);
      var bResult = Math.round(b);
      if (bResult) {
        var c = Math.pow(10, bResult);
        return Math.floor(a * c) / c;
      }
      return Math.floor(a);
    };
    Evaluator2.prototype.fnCEIL = function(a, b) {
      if (b === void 0) {
        b = 2;
      }
      a = this.formatNumber(a);
      b = this.formatNumber(b);
      var bResult = Math.round(b);
      if (bResult) {
        var c = Math.pow(10, bResult);
        return Math.ceil(a * c) / c;
      }
      return Math.ceil(a);
    };
    Evaluator2.prototype.fnSQRT = function(n) {
      return Math.sqrt(this.formatNumber(n));
    };
    Evaluator2.prototype.fnAVG = function() {
      var _this = this;
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      var arr = normalizeArgs(args);
      return this.fnSUM.apply(this, arr.map(function(item) {
        return _this.formatNumber(item);
      })) / arr.length;
    };
    Evaluator2.prototype.fnDEVSQ = function() {
      var e_1, _a;
      var _this = this;
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      if (args.length === 0) {
        return null;
      }
      var arr = normalizeArgs(args);
      var nums = arr.map(function(item) {
        return _this.formatNumber(item);
      });
      var sum = nums.reduce(function(sum2, a) {
        return sum2 + a || 0;
      }, 0);
      var mean = sum / nums.length;
      var result = 0;
      try {
        for (var nums_1 = __values(nums), nums_1_1 = nums_1.next(); !nums_1_1.done; nums_1_1 = nums_1.next()) {
          var num = nums_1_1.value;
          result += Math.pow(num - mean, 2);
        }
      } catch (e_1_1) {
        e_1 = { error: e_1_1 };
      } finally {
        try {
          if (nums_1_1 && !nums_1_1.done && (_a = nums_1.return))
            _a.call(nums_1);
        } finally {
          if (e_1)
            throw e_1.error;
        }
      }
      return result;
    };
    Evaluator2.prototype.fnAVEDEV = function() {
      var e_2, _a;
      var _this = this;
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      if (args.length === 0) {
        return null;
      }
      var arr = args;
      if (args.length === 1 && Array.isArray(args[0])) {
        arr = args[0];
      }
      var nums = arr.map(function(item) {
        return _this.formatNumber(item);
      });
      var sum = nums.reduce(function(sum2, a) {
        return sum2 + a || 0;
      }, 0);
      var mean = sum / nums.length;
      var result = 0;
      try {
        for (var nums_2 = __values(nums), nums_2_1 = nums_2.next(); !nums_2_1.done; nums_2_1 = nums_2.next()) {
          var num = nums_2_1.value;
          result += Math.abs(num - mean);
        }
      } catch (e_2_1) {
        e_2 = { error: e_2_1 };
      } finally {
        try {
          if (nums_2_1 && !nums_2_1.done && (_a = nums_2.return))
            _a.call(nums_2);
        } finally {
          if (e_2)
            throw e_2.error;
        }
      }
      return result / nums.length;
    };
    Evaluator2.prototype.fnHARMEAN = function() {
      var e_3, _a;
      var _this = this;
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      if (args.length === 0) {
        return null;
      }
      var arr = args;
      if (args.length === 1 && Array.isArray(args[0])) {
        arr = args[0];
      }
      var nums = arr.map(function(item) {
        return _this.formatNumber(item);
      });
      var den = 0;
      try {
        for (var nums_3 = __values(nums), nums_3_1 = nums_3.next(); !nums_3_1.done; nums_3_1 = nums_3.next()) {
          var num = nums_3_1.value;
          den += 1 / num;
        }
      } catch (e_3_1) {
        e_3 = { error: e_3_1 };
      } finally {
        try {
          if (nums_3_1 && !nums_3_1.done && (_a = nums_3.return))
            _a.call(nums_3);
        } finally {
          if (e_3)
            throw e_3.error;
        }
      }
      return nums.length / den;
    };
    Evaluator2.prototype.fnLARGE = function(nums, k) {
      var _this = this;
      if (nums.length === 0) {
        return null;
      }
      var numsFormat = nums.map(function(item) {
        return _this.formatNumber(item);
      });
      if (k < 0 || numsFormat.length < k) {
        return null;
      }
      return numsFormat.sort(function(a, b) {
        return b - a;
      })[k - 1];
    };
    Evaluator2.prototype.fnUPPERMONEY = function(n) {
      var _a;
      n = this.formatNumber(n);
      var maxLen = 14;
      if (((_a = n.toString().split(".")[0]) === null || _a === void 0 ? void 0 : _a.length) > maxLen) {
        return "最大数额只支持到兆(既小数点前".concat(maxLen, "位)");
      }
      var fraction = ["角", "分"];
      var digit = ["零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"];
      var unit = [
        ["元", "万", "亿", "兆"],
        ["", "拾", "佰", "仟"]
      ];
      var head = n < 0 ? "欠" : "";
      n = Math.abs(n);
      var s = "";
      for (var i = 0; i < fraction.length; i++) {
        s += (digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, "");
      }
      s = s || "整";
      n = Math.floor(n);
      for (var i = 0; i < unit[0].length && n > 0; i++) {
        var p = "";
        for (var j = 0; j < unit[1].length && n > 0; j++) {
          p = digit[n % 10] + unit[1][j] + p;
          n = Math.floor(n / 10);
        }
        s = p.replace(/(零.)*零$/, "").replace(/^$/, "零") + unit[0][i] + s;
      }
      return head + s.replace(/(零.)*零元/, "元").replace(/(零.)+/g, "零").replace(/^整$/, "零元整");
    };
    Evaluator2.prototype.fnRAND = function() {
      return Math.random();
    };
    Evaluator2.prototype.fnLAST = function(arr) {
      return arr.length ? arr[arr.length - 1] : null;
    };
    Evaluator2.prototype.fnPOW = function(base, exponent) {
      if (!this.isValidValue(base) || !this.isValidValue(exponent)) {
        return base;
      }
      return Math.pow(this.formatNumber(base), this.formatNumber(exponent));
    };
    Evaluator2.prototype.normalizeText = function(raw) {
      if (raw instanceof Date) {
        return moment_default(raw).format();
      }
      return "".concat(raw);
    };
    Evaluator2.prototype.fnLEFT = function(text, len) {
      text = this.normalizeText(text);
      return text.substring(0, len);
    };
    Evaluator2.prototype.fnRIGHT = function(text, len) {
      text = this.normalizeText(text);
      return text.substring(text.length - len, text.length);
    };
    Evaluator2.prototype.fnLEN = function(text) {
      if (text === void 0 || text === null) {
        return 0;
      }
      text = this.normalizeText(text);
      return text === null || text === void 0 ? void 0 : text.length;
    };
    Evaluator2.prototype.fnLENGTH = function() {
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      return this.fnLEN.apply(this, args);
    };
    Evaluator2.prototype.fnISEMPTY = function(text) {
      return !text || !String(text).trim();
    };
    Evaluator2.prototype.fnCONCATENATE = function() {
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      return args.map(this.normalizeText).join("");
    };
    Evaluator2.prototype.fnCHAR = function(code) {
      return String.fromCharCode(code);
    };
    Evaluator2.prototype.fnLOWER = function(text) {
      text = this.normalizeText(text);
      return text.toLowerCase();
    };
    Evaluator2.prototype.fnUPPER = function(text) {
      text = this.normalizeText(text);
      return text.toUpperCase();
    };
    Evaluator2.prototype.fnUPPERFIRST = function(text) {
      text = this.normalizeText(text);
      return (0, import_upperFirst.default)(text);
    };
    Evaluator2.prototype.fnPADSTART = function(text, num, pad) {
      text = this.normalizeText(text);
      return (0, import_padStart.default)(text, num, pad);
    };
    Evaluator2.prototype.fnCAPITALIZE = function(text) {
      text = this.normalizeText(text);
      return (0, import_capitalize.default)(text);
    };
    Evaluator2.prototype.fnESCAPE = function(text) {
      text = this.normalizeText(text);
      return (0, import_escape.default)(text);
    };
    Evaluator2.prototype.fnTRUNCATE = function(text, length) {
      text = this.normalizeText(text);
      return (0, import_truncate.default)(text, { length });
    };
    Evaluator2.prototype.fnBEFORELAST = function(text, delimiter) {
      if (delimiter === void 0) {
        delimiter = ".";
      }
      text = this.normalizeText(text);
      delimiter = this.normalizeText(delimiter);
      return text.split(delimiter).slice(0, -1).join(delimiter) || text + "";
    };
    Evaluator2.prototype.fnSPLIT = function(text, sep) {
      if (sep === void 0) {
        sep = ",";
      }
      text = this.normalizeText(text);
      return text.split(sep);
    };
    Evaluator2.prototype.fnTRIM = function(text) {
      text = this.normalizeText(text);
      return text.trim();
    };
    Evaluator2.prototype.fnSTRIPTAG = function(text) {
      text = this.normalizeText(text);
      return text.replace(/<\/?[^>]+(>|$)/g, "");
    };
    Evaluator2.prototype.fnLINEBREAK = function(text) {
      text = this.normalizeText(text);
      return text.replace(/(?:\r\n|\r|\n)/g, "<br/>");
    };
    Evaluator2.prototype.fnSTARTSWITH = function(text, search) {
      search = this.normalizeText(search);
      if (!search) {
        return false;
      }
      text = this.normalizeText(text);
      return text.indexOf(search) === 0;
    };
    Evaluator2.prototype.fnENDSWITH = function(text, search) {
      search = this.normalizeText(search);
      if (!search) {
        return false;
      }
      text = this.normalizeText(text);
      return text.indexOf(search, text.length - search.length) !== -1;
    };
    Evaluator2.prototype.fnCONTAINS = function(text, search) {
      search = this.normalizeText(search);
      if (!search) {
        return false;
      }
      text = this.normalizeText(text);
      return !!~text.indexOf(search);
    };
    Evaluator2.prototype.fnREPLACE = function(text, search, replace) {
      text = this.normalizeText(text);
      search = this.normalizeText(search);
      replace = this.normalizeText(replace);
      var result = text;
      if (typeof replace === "undefined" || !search) {
        return result;
      }
      var shouldLoop = !(typeof replace === "string" && replace.includes(search));
      while (true) {
        var idx = result.indexOf(search);
        if (!~idx) {
          break;
        }
        result = result.substring(0, idx) + replace + result.substring(idx + search.length);
        if (!shouldLoop) {
          break;
        }
      }
      return result;
    };
    Evaluator2.prototype.fnSEARCH = function(text, search, start) {
      if (start === void 0) {
        start = 0;
      }
      search = this.normalizeText(search);
      text = this.normalizeText(text);
      start = this.formatNumber(start);
      var idx = text.indexOf(search, start);
      if (~idx && search) {
        return idx;
      }
      return -1;
    };
    Evaluator2.prototype.fnMID = function(text, from, len) {
      text = this.normalizeText(text);
      from = this.formatNumber(from);
      len = this.formatNumber(len);
      return text.substring(from, from + len);
    };
    Evaluator2.prototype.fnBASENAME = function(text) {
      text = this.normalizeText(text);
      return text.split(/[\\/]/).pop();
    };
    Evaluator2.prototype.fnUUID = function(length) {
      if (length === void 0) {
        length = 36;
      }
      var len = Math.min(Math.max(length, 0), 36);
      return uuidv4().slice(0, len);
    };
    Evaluator2.prototype.fnDATE = function(year, month, day, hour, minute, second) {
      if (month === void 0) {
        return new Date(year);
      }
      return new Date(year, month, day, hour, minute, second);
    };
    Evaluator2.prototype.fnTIMESTAMP = function(date, format) {
      return parseInt(moment_default(this.normalizeDate(date)).format(format === "x" ? "x" : "X"), 10);
    };
    Evaluator2.prototype.fnTODAY = function() {
      return /* @__PURE__ */ new Date();
    };
    Evaluator2.prototype.fnNOW = function() {
      return /* @__PURE__ */ new Date();
    };
    Evaluator2.prototype.fnWEEKDAY = function(date, type) {
      var md = moment_default(this.normalizeDate(date));
      return type === 2 ? md.isoWeekday() : md.weekday();
    };
    Evaluator2.prototype.fnWEEK = function(date, isISO) {
      if (isISO === void 0) {
        isISO = false;
      }
      var md = moment_default(this.normalizeDate(date));
      return isISO ? md.isoWeek() : md.week();
    };
    Evaluator2.prototype.fnDATETOSTR = function(date, format) {
      if (format === void 0) {
        format = "YYYY-MM-DD HH:mm:ss";
      }
      date = this.normalizeDate(date);
      return moment_default(date).format(format);
    };
    Evaluator2.prototype.fnDATERANGESPLIT = function(daterange, key, format, delimiter) {
      var _this = this;
      if (delimiter === void 0) {
        delimiter = ",";
      }
      if (!daterange || typeof daterange !== "string") {
        return daterange;
      }
      var dateArr = daterange.split(delimiter).map(function(item) {
        return item && format ? moment_default(_this.normalizeDate(item.trim())).format(format) : item.trim();
      });
      if ([0, "0", "start"].includes(key)) {
        return dateArr[0];
      }
      if ([1, "1", "end"].includes(key)) {
        return dateArr[1];
      }
      return dateArr;
    };
    Evaluator2.prototype.fnSTARTOF = function(date, unit, format) {
      var md = moment_default(this.normalizeDate(date)).startOf(unit || "day");
      return format ? md.format(format) : md.toDate();
    };
    Evaluator2.prototype.fnENDOF = function(date, unit, format) {
      var md = moment_default(this.normalizeDate(date)).endOf(unit || "day");
      return format ? md.format(format) : md.toDate();
    };
    Evaluator2.prototype.normalizeDate = function(raw) {
      if (typeof raw === "string" || typeof raw === "number") {
        var formats = ["", "YYYY-MM-DD HH:mm:ss", "X"];
        if (/^\d{10}((\.\d+)*)$/.test(raw.toString())) {
          formats = ["X", "x", "YYYY-MM-DD HH:mm:ss", ""];
        } else if (/^\d{13}((\.\d+)*)$/.test(raw.toString())) {
          formats = ["x", "X", "YYYY-MM-DD HH:mm:ss", ""];
        }
        while (formats.length) {
          var format = formats.shift();
          var date = moment_default(raw, format);
          if (date.isValid()) {
            return date.toDate();
          }
        }
      }
      return raw;
    };
    Evaluator2.prototype.normalizeDateRange = function(raw) {
      var _this = this;
      return (Array.isArray(raw) ? raw : raw.split(",")).map(function(item) {
        return _this.normalizeDate(String(item).trim());
      });
    };
    Evaluator2.prototype.fnYEAR = function(date) {
      date = this.normalizeDate(date);
      return date.getFullYear();
    };
    Evaluator2.prototype.fnMONTH = function(date) {
      date = this.normalizeDate(date);
      return date.getMonth() + 1;
    };
    Evaluator2.prototype.fnDAY = function(date) {
      date = this.normalizeDate(date);
      return date.getDate();
    };
    Evaluator2.prototype.fnHOUR = function(date) {
      date = this.normalizeDate(date);
      return date.getHours();
    };
    Evaluator2.prototype.fnMINUTE = function(date) {
      date = this.normalizeDate(date);
      return date.getMinutes();
    };
    Evaluator2.prototype.fnSECOND = function(date) {
      date = this.normalizeDate(date);
      return date.getSeconds();
    };
    Evaluator2.prototype.fnYEARS = function(endDate, startDate) {
      endDate = this.normalizeDate(endDate);
      startDate = this.normalizeDate(startDate);
      return moment_default(endDate).diff(moment_default(startDate), "year");
    };
    Evaluator2.prototype.fnMINUTES = function(endDate, startDate) {
      endDate = this.normalizeDate(endDate);
      startDate = this.normalizeDate(startDate);
      return moment_default(endDate).diff(moment_default(startDate), "minutes");
    };
    Evaluator2.prototype.fnDAYS = function(endDate, startDate) {
      endDate = this.normalizeDate(endDate);
      startDate = this.normalizeDate(startDate);
      return moment_default(endDate).diff(moment_default(startDate), "days");
    };
    Evaluator2.prototype.fnHOURS = function(endDate, startDate) {
      endDate = this.normalizeDate(endDate);
      startDate = this.normalizeDate(startDate);
      return moment_default(endDate).diff(moment_default(startDate), "hour");
    };
    Evaluator2.prototype.fnDATEMODIFY = function(date, num, format) {
      date = this.normalizeDate(date);
      return moment_default(date).add(num, format).toDate();
    };
    Evaluator2.prototype.fnSTRTODATE = function(value, format) {
      if (format === void 0) {
        format = "";
      }
      return moment_default(value, format).toDate();
    };
    Evaluator2.prototype.fnISBEFORE = function(a, b, unit) {
      if (unit === void 0) {
        unit = "day";
      }
      a = this.normalizeDate(a);
      b = this.normalizeDate(b);
      return moment_default(a).isBefore(moment_default(b), unit);
    };
    Evaluator2.prototype.fnISAFTER = function(a, b, unit) {
      if (unit === void 0) {
        unit = "day";
      }
      a = this.normalizeDate(a);
      b = this.normalizeDate(b);
      return moment_default(a).isAfter(moment_default(b), unit);
    };
    Evaluator2.prototype.fnBETWEENRANGE = function(date, daterange, unit, inclusivity) {
      if (unit === void 0) {
        unit = "day";
      }
      if (inclusivity === void 0) {
        inclusivity = "[]";
      }
      var range = this.normalizeDateRange(daterange);
      return moment_default(this.normalizeDate(date)).isBetween(range[0], range[1], unit, inclusivity);
    };
    Evaluator2.prototype.fnISSAMEORBEFORE = function(a, b, unit) {
      if (unit === void 0) {
        unit = "day";
      }
      a = this.normalizeDate(a);
      b = this.normalizeDate(b);
      return moment_default(a).isSameOrBefore(moment_default(b), unit);
    };
    Evaluator2.prototype.fnISSAMEORAFTER = function(a, b, unit) {
      if (unit === void 0) {
        unit = "day";
      }
      a = this.normalizeDate(a);
      b = this.normalizeDate(b);
      return moment_default(a).isSameOrAfter(moment_default(b), unit);
    };
    Evaluator2.prototype.fnCOUNT = function(value) {
      return Array.isArray(value) ? value.length : value ? 1 : 0;
    };
    Evaluator2.prototype.fnARRAYMAP = function(value, iterator) {
      var _this = this;
      if (!iterator || iterator.type !== "anonymous_function") {
        throw new Error("expected an anonymous function get " + iterator);
      }
      return (Array.isArray(value) ? value : []).map(function(item, index, arr) {
        return _this.callAnonymousFunction(iterator, [item, index, arr]);
      });
    };
    Evaluator2.prototype.fnARRAYFILTER = function(value, iterator) {
      var _this = this;
      if (!iterator || iterator.type !== "anonymous_function") {
        throw new Error("expected an anonymous function get " + iterator);
      }
      return (Array.isArray(value) ? value : []).filter(function(item, index, arr) {
        return _this.callAnonymousFunction(iterator, [item, index, arr]);
      });
    };
    Evaluator2.prototype.fnARRAYFINDINDEX = function(arr, iterator) {
      var _this = this;
      if (!iterator || iterator.type !== "anonymous_function") {
        throw new Error("expected an anonymous function get " + iterator);
      }
      return (Array.isArray(arr) ? arr : []).findIndex(function(item, index, arr2) {
        return _this.callAnonymousFunction(iterator, [item, index, arr2]);
      });
    };
    Evaluator2.prototype.fnARRAYFIND = function(arr, iterator) {
      var _this = this;
      if (!iterator || iterator.type !== "anonymous_function") {
        throw new Error("expected an anonymous function get " + iterator);
      }
      return (Array.isArray(arr) ? arr : []).find(function(item, index, arr2) {
        return _this.callAnonymousFunction(iterator, [item, index, arr2]);
      });
    };
    Evaluator2.prototype.fnARRAYSOME = function(arr, iterator) {
      var _this = this;
      if (!iterator || iterator.type !== "anonymous_function") {
        throw new Error("expected an anonymous function get " + iterator);
      }
      return (Array.isArray(arr) ? arr : []).some(function(item, index, arr2) {
        return _this.callAnonymousFunction(iterator, [item, index, arr2]);
      });
    };
    Evaluator2.prototype.fnARRAYEVERY = function(arr, iterator) {
      var _this = this;
      if (!iterator || iterator.type !== "anonymous_function") {
        throw new Error("expected an anonymous function get " + iterator);
      }
      return (Array.isArray(arr) ? arr : []).every(function(item, index, arr2) {
        return _this.callAnonymousFunction(iterator, [item, index, arr2]);
      });
    };
    Evaluator2.prototype.fnARRAYINCLUDES = function(arr, item) {
      return (Array.isArray(arr) ? arr : []).includes(item);
    };
    Evaluator2.prototype.fnCOMPACT = function(arr) {
      var e_4, _a;
      if (Array.isArray(arr)) {
        var resIndex = 0;
        var result = [];
        try {
          for (var arr_1 = __values(arr), arr_1_1 = arr_1.next(); !arr_1_1.done; arr_1_1 = arr_1.next()) {
            var item = arr_1_1.value;
            if (item) {
              result[resIndex++] = item;
            }
          }
        } catch (e_4_1) {
          e_4 = { error: e_4_1 };
        } finally {
          try {
            if (arr_1_1 && !arr_1_1.done && (_a = arr_1.return))
              _a.call(arr_1);
          } finally {
            if (e_4)
              throw e_4.error;
          }
        }
        return result;
      } else {
        return [];
      }
    };
    Evaluator2.prototype.fnJOIN = function(arr, separator) {
      if (separator === void 0) {
        separator = "";
      }
      if (Array.isArray(arr)) {
        return arr.join(separator);
      } else {
        return "";
      }
    };
    Evaluator2.prototype.fnCONCAT = function() {
      var arr = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        arr[_i] = arguments[_i];
      }
      if ((arr === null || arr === void 0 ? void 0 : arr[0]) && !Array.isArray(arr[0])) {
        arr[0] = [arr[0]];
      }
      return arr.reduce(function(a, b) {
        return a.concat(b);
      }, []).filter(function(item) {
        return item;
      });
    };
    Evaluator2.prototype.fnUNIQ = function(arr, field) {
      return field ? (0, import_uniqBy.default)(arr, field) : (0, import_uniqWith.default)(arr, import_isEqual.default);
    };
    Evaluator2.prototype.fnENCODEJSON = function(obj) {
      return JSON.stringify(obj);
    };
    Evaluator2.prototype.fnDECODEJSON = function(str) {
      return JSON.parse(str);
    };
    Evaluator2.prototype.fnGET = function(obj, path, defaultValue) {
      return (0, import_get.default)(obj, path, defaultValue);
    };
    Evaluator2.prototype.fnISTYPE = function(target, type) {
      switch (type) {
        case "string":
          return typeof target === "string";
        case "number":
          return typeof target === "number";
        case "array":
          return Array.isArray(target);
        case "date":
          return !!(target && target instanceof Date);
        case "plain-object":
          return (0, import_isPlainObject.default)(target);
        case "nil":
          return !target;
      }
      return false;
    };
    Evaluator2.defaultFilters = {};
    Evaluator2.defaultFunctions = {};
    return Evaluator2;
  }()
);
Evaluator.setDefaultFilters = Evaluator.extendDefaultFilters;
Evaluator.setDefaultFunctions = Evaluator.extendDefaultFunctions;
function getCookie(name) {
  var value = "; ".concat(document.cookie);
  var parts = value.split("; ".concat(name, "="));
  if (parts.length === 2) {
    return parts.pop().split(";").shift();
  }
  return void 0;
}
function parseJson(str, defaultValue) {
  try {
    return JSON.parse(str);
  } catch (e) {
    return defaultValue;
  }
}
function stripNumber(number) {
  if (typeof number === "number" && !Number.isInteger(number)) {
    return parseFloat(number.toPrecision(16));
  } else {
    return number;
  }
}
function normalizeArgs(args) {
  if (args.length === 1 && Array.isArray(args[0])) {
    args = args[0];
  }
  return args;
}
function createObject(superProps, props, properties) {
  var obj = superProps ? Object.create(superProps, __assign(__assign({}, properties), { __super: {
    value: superProps,
    writable: false,
    enumerable: false
  } })) : Object.create(Object.prototype, properties);
  props && Object.keys(props).forEach(function(key) {
    return obj[key] = props[key];
  });
  return obj;
}
function createStr() {
  return ("00000000000000000" + (Math.random() * 18446744073709552e3).toString(16)).slice(-16);
}
function uuidv4() {
  var a = createStr();
  var b = createStr();
  return a.slice(0, 8) + "-" + a.slice(8, 12) + "-4" + a.slice(13) + "-a" + b.slice(1, 4) + "-" + b.slice(4);
}

// node_modules/amis-formula/esm/function.js
function registerFunction(name, fn) {
  var _a;
  Evaluator.extendDefaultFunctions((_a = {}, _a["fn".concat(name)] = fn, _a));
}
var functionDocs = {};
function registerFunctionDoc(groupName, item) {
  if (functionDocs[groupName]) {
    functionDocs[groupName].push(item);
  } else {
    functionDocs[groupName] = [item];
  }
}
function bulkRegisterFunctionDoc(fnDocs) {
  fnDocs.forEach(function(item) {
    return registerFunctionDoc(item.namespace || "Others", item);
  });
}

export {
  require_baseFindIndex,
  require_baseIndexOf,
  require_arrayIncludes,
  require_arrayIncludesWith,
  require_baseUniq,
  require_getPrototype,
  require_isPlainObject,
  require_identity,
  __extends,
  __assign,
  __awaiter,
  __generator,
  __values,
  __read,
  __spreadArray,
  require_baseSlice,
  require_arrayMap,
  require_isSymbol,
  require_baseToString,
  require_toString,
  require_upperFirst,
  require_toNumber,
  require_toFinite,
  require_toInteger,
  require_capitalize,
  require_basePropertyOf,
  require_escape,
  require_uniqWith,
  require_memoize,
  require_castPath,
  require_toKey,
  require_baseGet,
  require_get,
  require_hasIn,
  require_baseIteratee,
  require_uniqBy,
  FormulaEvalError,
  Evaluator,
  stripNumber,
  createObject,
  registerFunction,
  functionDocs,
  bulkRegisterFunctionDoc
};
//# sourceMappingURL=chunk-5QW7M2DY.js.map
