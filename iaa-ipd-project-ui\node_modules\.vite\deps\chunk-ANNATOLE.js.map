{"version": 3, "sources": ["../../monaco-editor/esm/vs/language/typescript/lib/typescriptServicesMetadata.js", "../../monaco-editor/esm/vs/language/typescript/monaco.contribution.js"], "sourcesContent": ["//\n// **NOTE**: Do not edit directly! This file is generated using `npm run import-typescript`\n//\nexport var typescriptVersion = \"4.4.4\";\n", "import '../../editor/editor.api.js';\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n'use strict';\nimport { typescriptVersion as tsversion } from './lib/typescriptServicesMetadata.js'; // do not import the whole typescriptServices here\nimport { languages, Emitter } from './fillers/monaco-editor-core.js';\n//#region enums copied from typescript to prevent loading the entire typescriptServices ---\nexport var ModuleKind;\n(function (ModuleKind) {\n    ModuleKind[ModuleKind[\"None\"] = 0] = \"None\";\n    ModuleKind[ModuleKind[\"CommonJS\"] = 1] = \"CommonJS\";\n    ModuleKind[ModuleKind[\"AMD\"] = 2] = \"AMD\";\n    ModuleKind[ModuleKind[\"UMD\"] = 3] = \"UMD\";\n    ModuleKind[ModuleKind[\"System\"] = 4] = \"System\";\n    ModuleKind[ModuleKind[\"ES2015\"] = 5] = \"ES2015\";\n    ModuleKind[ModuleKind[\"ESNext\"] = 99] = \"ESNext\";\n})(ModuleKind || (ModuleKind = {}));\nexport var JsxEmit;\n(function (JsxEmit) {\n    JsxEmit[JsxEmit[\"None\"] = 0] = \"None\";\n    JsxEmit[JsxEmit[\"Preserve\"] = 1] = \"Preserve\";\n    JsxEmit[JsxEmit[\"React\"] = 2] = \"React\";\n    JsxEmit[JsxEmit[\"ReactNative\"] = 3] = \"ReactNative\";\n    JsxEmit[JsxEmit[\"ReactJSX\"] = 4] = \"ReactJSX\";\n    JsxEmit[JsxEmit[\"ReactJSXDev\"] = 5] = \"ReactJSXDev\";\n})(JsxEmit || (JsxEmit = {}));\nexport var NewLineKind;\n(function (NewLineKind) {\n    NewLineKind[NewLineKind[\"CarriageReturnLineFeed\"] = 0] = \"CarriageReturnLineFeed\";\n    NewLineKind[NewLineKind[\"LineFeed\"] = 1] = \"LineFeed\";\n})(NewLineKind || (NewLineKind = {}));\nexport var ScriptTarget;\n(function (ScriptTarget) {\n    ScriptTarget[ScriptTarget[\"ES3\"] = 0] = \"ES3\";\n    ScriptTarget[ScriptTarget[\"ES5\"] = 1] = \"ES5\";\n    ScriptTarget[ScriptTarget[\"ES2015\"] = 2] = \"ES2015\";\n    ScriptTarget[ScriptTarget[\"ES2016\"] = 3] = \"ES2016\";\n    ScriptTarget[ScriptTarget[\"ES2017\"] = 4] = \"ES2017\";\n    ScriptTarget[ScriptTarget[\"ES2018\"] = 5] = \"ES2018\";\n    ScriptTarget[ScriptTarget[\"ES2019\"] = 6] = \"ES2019\";\n    ScriptTarget[ScriptTarget[\"ES2020\"] = 7] = \"ES2020\";\n    ScriptTarget[ScriptTarget[\"ESNext\"] = 99] = \"ESNext\";\n    ScriptTarget[ScriptTarget[\"JSON\"] = 100] = \"JSON\";\n    ScriptTarget[ScriptTarget[\"Latest\"] = 99] = \"Latest\";\n})(ScriptTarget || (ScriptTarget = {}));\nexport var ModuleResolutionKind;\n(function (ModuleResolutionKind) {\n    ModuleResolutionKind[ModuleResolutionKind[\"Classic\"] = 1] = \"Classic\";\n    ModuleResolutionKind[ModuleResolutionKind[\"NodeJs\"] = 2] = \"NodeJs\";\n})(ModuleResolutionKind || (ModuleResolutionKind = {}));\n// --- TypeScript configuration and defaults ---------\nvar LanguageServiceDefaultsImpl = /** @class */ (function () {\n    function LanguageServiceDefaultsImpl(compilerOptions, diagnosticsOptions, workerOptions, inlayHintsOptions) {\n        this._onDidChange = new Emitter();\n        this._onDidExtraLibsChange = new Emitter();\n        this._extraLibs = Object.create(null);\n        this._removedExtraLibs = Object.create(null);\n        this._eagerModelSync = false;\n        this.setCompilerOptions(compilerOptions);\n        this.setDiagnosticsOptions(diagnosticsOptions);\n        this.setWorkerOptions(workerOptions);\n        this.setInlayHintsOptions(inlayHintsOptions);\n        this._onDidExtraLibsChangeTimeout = -1;\n    }\n    Object.defineProperty(LanguageServiceDefaultsImpl.prototype, \"onDidChange\", {\n        get: function () {\n            return this._onDidChange.event;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(LanguageServiceDefaultsImpl.prototype, \"onDidExtraLibsChange\", {\n        get: function () {\n            return this._onDidExtraLibsChange.event;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(LanguageServiceDefaultsImpl.prototype, \"workerOptions\", {\n        get: function () {\n            return this._workerOptions;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(LanguageServiceDefaultsImpl.prototype, \"inlayHintsOptions\", {\n        get: function () {\n            return this._inlayHintsOptions;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    LanguageServiceDefaultsImpl.prototype.getExtraLibs = function () {\n        return this._extraLibs;\n    };\n    LanguageServiceDefaultsImpl.prototype.addExtraLib = function (content, _filePath) {\n        var _this = this;\n        var filePath;\n        if (typeof _filePath === 'undefined') {\n            filePath = \"ts:extralib-\" + Math.random().toString(36).substring(2, 15);\n        }\n        else {\n            filePath = _filePath;\n        }\n        if (this._extraLibs[filePath] && this._extraLibs[filePath].content === content) {\n            // no-op, there already exists an extra lib with this content\n            return {\n                dispose: function () { }\n            };\n        }\n        var myVersion = 1;\n        if (this._removedExtraLibs[filePath]) {\n            myVersion = this._removedExtraLibs[filePath] + 1;\n        }\n        if (this._extraLibs[filePath]) {\n            myVersion = this._extraLibs[filePath].version + 1;\n        }\n        this._extraLibs[filePath] = {\n            content: content,\n            version: myVersion\n        };\n        this._fireOnDidExtraLibsChangeSoon();\n        return {\n            dispose: function () {\n                var extraLib = _this._extraLibs[filePath];\n                if (!extraLib) {\n                    return;\n                }\n                if (extraLib.version !== myVersion) {\n                    return;\n                }\n                delete _this._extraLibs[filePath];\n                _this._removedExtraLibs[filePath] = myVersion;\n                _this._fireOnDidExtraLibsChangeSoon();\n            }\n        };\n    };\n    LanguageServiceDefaultsImpl.prototype.setExtraLibs = function (libs) {\n        for (var filePath in this._extraLibs) {\n            this._removedExtraLibs[filePath] = this._extraLibs[filePath].version;\n        }\n        // clear out everything\n        this._extraLibs = Object.create(null);\n        if (libs && libs.length > 0) {\n            for (var _i = 0, libs_1 = libs; _i < libs_1.length; _i++) {\n                var lib = libs_1[_i];\n                var filePath = lib.filePath || \"ts:extralib-\" + Math.random().toString(36).substring(2, 15);\n                var content = lib.content;\n                var myVersion = 1;\n                if (this._removedExtraLibs[filePath]) {\n                    myVersion = this._removedExtraLibs[filePath] + 1;\n                }\n                this._extraLibs[filePath] = {\n                    content: content,\n                    version: myVersion\n                };\n            }\n        }\n        this._fireOnDidExtraLibsChangeSoon();\n    };\n    LanguageServiceDefaultsImpl.prototype._fireOnDidExtraLibsChangeSoon = function () {\n        var _this = this;\n        if (this._onDidExtraLibsChangeTimeout !== -1) {\n            // already scheduled\n            return;\n        }\n        this._onDidExtraLibsChangeTimeout = window.setTimeout(function () {\n            _this._onDidExtraLibsChangeTimeout = -1;\n            _this._onDidExtraLibsChange.fire(undefined);\n        }, 0);\n    };\n    LanguageServiceDefaultsImpl.prototype.getCompilerOptions = function () {\n        return this._compilerOptions;\n    };\n    LanguageServiceDefaultsImpl.prototype.setCompilerOptions = function (options) {\n        this._compilerOptions = options || Object.create(null);\n        this._onDidChange.fire(undefined);\n    };\n    LanguageServiceDefaultsImpl.prototype.getDiagnosticsOptions = function () {\n        return this._diagnosticsOptions;\n    };\n    LanguageServiceDefaultsImpl.prototype.setDiagnosticsOptions = function (options) {\n        this._diagnosticsOptions = options || Object.create(null);\n        this._onDidChange.fire(undefined);\n    };\n    LanguageServiceDefaultsImpl.prototype.setWorkerOptions = function (options) {\n        this._workerOptions = options || Object.create(null);\n        this._onDidChange.fire(undefined);\n    };\n    LanguageServiceDefaultsImpl.prototype.setInlayHintsOptions = function (options) {\n        this._inlayHintsOptions = options || Object.create(null);\n        this._onDidChange.fire(undefined);\n    };\n    LanguageServiceDefaultsImpl.prototype.setMaximumWorkerIdleTime = function (value) { };\n    LanguageServiceDefaultsImpl.prototype.setEagerModelSync = function (value) {\n        // doesn't fire an event since no\n        // worker restart is required here\n        this._eagerModelSync = value;\n    };\n    LanguageServiceDefaultsImpl.prototype.getEagerModelSync = function () {\n        return this._eagerModelSync;\n    };\n    return LanguageServiceDefaultsImpl;\n}());\nexport var typescriptVersion = tsversion;\nexport var typescriptDefaults = new LanguageServiceDefaultsImpl({ allowNonTsExtensions: true, target: ScriptTarget.Latest }, { noSemanticValidation: false, noSyntaxValidation: false, onlyVisible: false }, {}, {});\nexport var javascriptDefaults = new LanguageServiceDefaultsImpl({ allowNonTsExtensions: true, allowJs: true, target: ScriptTarget.Latest }, { noSemanticValidation: true, noSyntaxValidation: false, onlyVisible: false }, {}, {});\nexport var getTypeScriptWorker = function () {\n    return getMode().then(function (mode) { return mode.getTypeScriptWorker(); });\n};\nexport var getJavaScriptWorker = function () {\n    return getMode().then(function (mode) { return mode.getJavaScriptWorker(); });\n};\n// export to the global based API\nlanguages.typescript = {\n    ModuleKind: ModuleKind,\n    JsxEmit: JsxEmit,\n    NewLineKind: NewLineKind,\n    ScriptTarget: ScriptTarget,\n    ModuleResolutionKind: ModuleResolutionKind,\n    typescriptVersion: typescriptVersion,\n    typescriptDefaults: typescriptDefaults,\n    javascriptDefaults: javascriptDefaults,\n    getTypeScriptWorker: getTypeScriptWorker,\n    getJavaScriptWorker: getJavaScriptWorker\n};\n// --- Registration to monaco editor ---\nfunction getMode() {\n    return import('./tsMode.js');\n}\nlanguages.onLanguage('typescript', function () {\n    return getMode().then(function (mode) { return mode.setupTypeScript(typescriptDefaults); });\n});\nlanguages.onLanguage('javascript', function () {\n    return getMode().then(function (mode) { return mode.setupJavaScript(javascriptDefaults); });\n});\n"], "mappings": ";;;;;;AAGO,IAAI,oBAAoB;;;ACMxB,IAAI;AAAA,CACV,SAAUA,aAAY;AACnB,EAAAA,YAAWA,YAAW,MAAM,IAAI,CAAC,IAAI;AACrC,EAAAA,YAAWA,YAAW,UAAU,IAAI,CAAC,IAAI;AACzC,EAAAA,YAAWA,YAAW,KAAK,IAAI,CAAC,IAAI;AACpC,EAAAA,YAAWA,YAAW,KAAK,IAAI,CAAC,IAAI;AACpC,EAAAA,YAAWA,YAAW,QAAQ,IAAI,CAAC,IAAI;AACvC,EAAAA,YAAWA,YAAW,QAAQ,IAAI,CAAC,IAAI;AACvC,EAAAA,YAAWA,YAAW,QAAQ,IAAI,EAAE,IAAI;AAC5C,GAAG,eAAe,aAAa,CAAC,EAAE;AAC3B,IAAI;AAAA,CACV,SAAUC,UAAS;AAChB,EAAAA,SAAQA,SAAQ,MAAM,IAAI,CAAC,IAAI;AAC/B,EAAAA,SAAQA,SAAQ,UAAU,IAAI,CAAC,IAAI;AACnC,EAAAA,SAAQA,SAAQ,OAAO,IAAI,CAAC,IAAI;AAChC,EAAAA,SAAQA,SAAQ,aAAa,IAAI,CAAC,IAAI;AACtC,EAAAA,SAAQA,SAAQ,UAAU,IAAI,CAAC,IAAI;AACnC,EAAAA,SAAQA,SAAQ,aAAa,IAAI,CAAC,IAAI;AAC1C,GAAG,YAAY,UAAU,CAAC,EAAE;AACrB,IAAI;AAAA,CACV,SAAUC,cAAa;AACpB,EAAAA,aAAYA,aAAY,wBAAwB,IAAI,CAAC,IAAI;AACzD,EAAAA,aAAYA,aAAY,UAAU,IAAI,CAAC,IAAI;AAC/C,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAC7B,IAAI;AAAA,CACV,SAAUC,eAAc;AACrB,EAAAA,cAAaA,cAAa,KAAK,IAAI,CAAC,IAAI;AACxC,EAAAA,cAAaA,cAAa,KAAK,IAAI,CAAC,IAAI;AACxC,EAAAA,cAAaA,cAAa,QAAQ,IAAI,CAAC,IAAI;AAC3C,EAAAA,cAAaA,cAAa,QAAQ,IAAI,CAAC,IAAI;AAC3C,EAAAA,cAAaA,cAAa,QAAQ,IAAI,CAAC,IAAI;AAC3C,EAAAA,cAAaA,cAAa,QAAQ,IAAI,CAAC,IAAI;AAC3C,EAAAA,cAAaA,cAAa,QAAQ,IAAI,CAAC,IAAI;AAC3C,EAAAA,cAAaA,cAAa,QAAQ,IAAI,CAAC,IAAI;AAC3C,EAAAA,cAAaA,cAAa,QAAQ,IAAI,EAAE,IAAI;AAC5C,EAAAA,cAAaA,cAAa,MAAM,IAAI,GAAG,IAAI;AAC3C,EAAAA,cAAaA,cAAa,QAAQ,IAAI,EAAE,IAAI;AAChD,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAC/B,IAAI;AAAA,CACV,SAAUC,uBAAsB;AAC7B,EAAAA,sBAAqBA,sBAAqB,SAAS,IAAI,CAAC,IAAI;AAC5D,EAAAA,sBAAqBA,sBAAqB,QAAQ,IAAI,CAAC,IAAI;AAC/D,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAEtD,IAAI;AAAA;AAAA,EAA6C,WAAY;AACzD,aAASC,6BAA4B,iBAAiB,oBAAoB,eAAe,mBAAmB;AACxG,WAAK,eAAe,IAAI,QAAQ;AAChC,WAAK,wBAAwB,IAAI,QAAQ;AACzC,WAAK,aAAa,uBAAO,OAAO,IAAI;AACpC,WAAK,oBAAoB,uBAAO,OAAO,IAAI;AAC3C,WAAK,kBAAkB;AACvB,WAAK,mBAAmB,eAAe;AACvC,WAAK,sBAAsB,kBAAkB;AAC7C,WAAK,iBAAiB,aAAa;AACnC,WAAK,qBAAqB,iBAAiB;AAC3C,WAAK,+BAA+B;AAAA,IACxC;AACA,WAAO,eAAeA,6BAA4B,WAAW,eAAe;AAAA,MACxE,KAAK,WAAY;AACb,eAAO,KAAK,aAAa;AAAA,MAC7B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,6BAA4B,WAAW,wBAAwB;AAAA,MACjF,KAAK,WAAY;AACb,eAAO,KAAK,sBAAsB;AAAA,MACtC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,6BAA4B,WAAW,iBAAiB;AAAA,MAC1E,KAAK,WAAY;AACb,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,6BAA4B,WAAW,qBAAqB;AAAA,MAC9E,KAAK,WAAY;AACb,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,6BAA4B,UAAU,eAAe,WAAY;AAC7D,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,6BAA4B,UAAU,cAAc,SAAU,SAAS,WAAW;AAC9E,UAAI,QAAQ;AACZ,UAAI;AACJ,UAAI,OAAO,cAAc,aAAa;AAClC,mBAAW,iBAAiB,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;AAAA,MAC1E,OACK;AACD,mBAAW;AAAA,MACf;AACA,UAAI,KAAK,WAAW,QAAQ,KAAK,KAAK,WAAW,QAAQ,EAAE,YAAY,SAAS;AAE5E,eAAO;AAAA,UACH,SAAS,WAAY;AAAA,UAAE;AAAA,QAC3B;AAAA,MACJ;AACA,UAAI,YAAY;AAChB,UAAI,KAAK,kBAAkB,QAAQ,GAAG;AAClC,oBAAY,KAAK,kBAAkB,QAAQ,IAAI;AAAA,MACnD;AACA,UAAI,KAAK,WAAW,QAAQ,GAAG;AAC3B,oBAAY,KAAK,WAAW,QAAQ,EAAE,UAAU;AAAA,MACpD;AACA,WAAK,WAAW,QAAQ,IAAI;AAAA,QACxB;AAAA,QACA,SAAS;AAAA,MACb;AACA,WAAK,8BAA8B;AACnC,aAAO;AAAA,QACH,SAAS,WAAY;AACjB,cAAI,WAAW,MAAM,WAAW,QAAQ;AACxC,cAAI,CAAC,UAAU;AACX;AAAA,UACJ;AACA,cAAI,SAAS,YAAY,WAAW;AAChC;AAAA,UACJ;AACA,iBAAO,MAAM,WAAW,QAAQ;AAChC,gBAAM,kBAAkB,QAAQ,IAAI;AACpC,gBAAM,8BAA8B;AAAA,QACxC;AAAA,MACJ;AAAA,IACJ;AACA,IAAAA,6BAA4B,UAAU,eAAe,SAAU,MAAM;AACjE,eAAS,YAAY,KAAK,YAAY;AAClC,aAAK,kBAAkB,QAAQ,IAAI,KAAK,WAAW,QAAQ,EAAE;AAAA,MACjE;AAEA,WAAK,aAAa,uBAAO,OAAO,IAAI;AACpC,UAAI,QAAQ,KAAK,SAAS,GAAG;AACzB,iBAAS,KAAK,GAAG,SAAS,MAAM,KAAK,OAAO,QAAQ,MAAM;AACtD,cAAI,MAAM,OAAO,EAAE;AACnB,cAAI,WAAW,IAAI,YAAY,iBAAiB,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;AAC1F,cAAI,UAAU,IAAI;AAClB,cAAI,YAAY;AAChB,cAAI,KAAK,kBAAkB,QAAQ,GAAG;AAClC,wBAAY,KAAK,kBAAkB,QAAQ,IAAI;AAAA,UACnD;AACA,eAAK,WAAW,QAAQ,IAAI;AAAA,YACxB;AAAA,YACA,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,MACJ;AACA,WAAK,8BAA8B;AAAA,IACvC;AACA,IAAAA,6BAA4B,UAAU,gCAAgC,WAAY;AAC9E,UAAI,QAAQ;AACZ,UAAI,KAAK,iCAAiC,IAAI;AAE1C;AAAA,MACJ;AACA,WAAK,+BAA+B,OAAO,WAAW,WAAY;AAC9D,cAAM,+BAA+B;AACrC,cAAM,sBAAsB,KAAK,MAAS;AAAA,MAC9C,GAAG,CAAC;AAAA,IACR;AACA,IAAAA,6BAA4B,UAAU,qBAAqB,WAAY;AACnE,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,6BAA4B,UAAU,qBAAqB,SAAU,SAAS;AAC1E,WAAK,mBAAmB,WAAW,uBAAO,OAAO,IAAI;AACrD,WAAK,aAAa,KAAK,MAAS;AAAA,IACpC;AACA,IAAAA,6BAA4B,UAAU,wBAAwB,WAAY;AACtE,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,6BAA4B,UAAU,wBAAwB,SAAU,SAAS;AAC7E,WAAK,sBAAsB,WAAW,uBAAO,OAAO,IAAI;AACxD,WAAK,aAAa,KAAK,MAAS;AAAA,IACpC;AACA,IAAAA,6BAA4B,UAAU,mBAAmB,SAAU,SAAS;AACxE,WAAK,iBAAiB,WAAW,uBAAO,OAAO,IAAI;AACnD,WAAK,aAAa,KAAK,MAAS;AAAA,IACpC;AACA,IAAAA,6BAA4B,UAAU,uBAAuB,SAAU,SAAS;AAC5E,WAAK,qBAAqB,WAAW,uBAAO,OAAO,IAAI;AACvD,WAAK,aAAa,KAAK,MAAS;AAAA,IACpC;AACA,IAAAA,6BAA4B,UAAU,2BAA2B,SAAU,OAAO;AAAA,IAAE;AACpF,IAAAA,6BAA4B,UAAU,oBAAoB,SAAU,OAAO;AAGvE,WAAK,kBAAkB;AAAA,IAC3B;AACA,IAAAA,6BAA4B,UAAU,oBAAoB,WAAY;AAClE,aAAO,KAAK;AAAA,IAChB;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACK,IAAIC,qBAAoB;AACxB,IAAI,qBAAqB,IAAI,4BAA4B,EAAE,sBAAsB,MAAM,QAAQ,aAAa,OAAO,GAAG,EAAE,sBAAsB,OAAO,oBAAoB,OAAO,aAAa,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;AAC5M,IAAI,qBAAqB,IAAI,4BAA4B,EAAE,sBAAsB,MAAM,SAAS,MAAM,QAAQ,aAAa,OAAO,GAAG,EAAE,sBAAsB,MAAM,oBAAoB,OAAO,aAAa,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1N,IAAI,sBAAsB,WAAY;AACzC,SAAO,QAAQ,EAAE,KAAK,SAAU,MAAM;AAAE,WAAO,KAAK,oBAAoB;AAAA,EAAG,CAAC;AAChF;AACO,IAAI,sBAAsB,WAAY;AACzC,SAAO,QAAQ,EAAE,KAAK,SAAU,MAAM;AAAE,WAAO,KAAK,oBAAoB;AAAA,EAAG,CAAC;AAChF;AAEA,UAAU,aAAa;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,mBAAmBA;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAEA,SAAS,UAAU;AACf,SAAO,OAAO,sBAAa;AAC/B;AACA,UAAU,WAAW,cAAc,WAAY;AAC3C,SAAO,QAAQ,EAAE,KAAK,SAAU,MAAM;AAAE,WAAO,KAAK,gBAAgB,kBAAkB;AAAA,EAAG,CAAC;AAC9F,CAAC;AACD,UAAU,WAAW,cAAc,WAAY;AAC3C,SAAO,QAAQ,EAAE,KAAK,SAAU,MAAM;AAAE,WAAO,KAAK,gBAAgB,kBAAkB;AAAA,EAAG,CAAC;AAC9F,CAAC;", "names": ["Module<PERSON>ind", "JsxEmit", "NewLineKind", "<PERSON><PERSON><PERSON><PERSON>ar<PERSON>", "ModuleResolutionKind", "LanguageServiceDefaultsImpl", "typescriptVersion"]}