{"version": 3, "sources": ["../../bpmn-js/lib/util/DiUtil.js"], "sourcesContent": ["import {\n  is,\n  getBusinessObject\n} from './ModelUtil';\n\nimport {\n  forEach\n} from 'min-dash';\n\n\nexport function isExpanded(element) {\n\n  if (is(element, 'bpmn:CallActivity')) {\n    return false;\n  }\n\n  if (is(element, 'bpmn:SubProcess')) {\n    return getBusinessObject(element).di && !!getBusinessObject(element).di.isExpanded;\n  }\n\n  if (is(element, 'bpmn:Participant')) {\n    return !!getBusinessObject(element).processRef;\n  }\n\n  return true;\n}\n\nexport function isInterrupting(element) {\n  return element && getBusinessObject(element).isInterrupting !== false;\n}\n\nexport function isEventSubProcess(element) {\n  return element && !!getBusinessObject(element).triggeredByEvent;\n}\n\nexport function hasEventDefinition(element, eventType) {\n  var bo = getBusinessObject(element),\n      hasEventDefinition = false;\n\n  if (bo.eventDefinitions) {\n    forEach(bo.eventDefinitions, function(event) {\n      if (is(event, eventType)) {\n        hasEventDefinition = true;\n      }\n    });\n  }\n\n  return hasEventDefinition;\n}\n\nexport function hasErrorEventDefinition(element) {\n  return hasEventDefinition(element, 'bpmn:ErrorEventDefinition');\n}\n\nexport function hasEscalationEventDefinition(element) {\n  return hasEventDefinition(element, 'bpmn:EscalationEventDefinition');\n}\n\nexport function hasCompensateEventDefinition(element) {\n  return hasEventDefinition(element, 'bpmn:CompensateEventDefinition');\n}\n"], "mappings": ";;;;;;;;;AAUO,SAAS,WAAW,SAAS;AAElC,MAAI,GAAG,SAAS,mBAAmB,GAAG;AACpC,WAAO;AAAA,EACT;AAEA,MAAI,GAAG,SAAS,iBAAiB,GAAG;AAClC,WAAO,kBAAkB,OAAO,EAAE,MAAM,CAAC,CAAC,kBAAkB,OAAO,EAAE,GAAG;AAAA,EAC1E;AAEA,MAAI,GAAG,SAAS,kBAAkB,GAAG;AACnC,WAAO,CAAC,CAAC,kBAAkB,OAAO,EAAE;AAAA,EACtC;AAEA,SAAO;AACT;AAEO,SAAS,eAAe,SAAS;AACtC,SAAO,WAAW,kBAAkB,OAAO,EAAE,mBAAmB;AAClE;AAEO,SAAS,kBAAkB,SAAS;AACzC,SAAO,WAAW,CAAC,CAAC,kBAAkB,OAAO,EAAE;AACjD;AAEO,SAAS,mBAAmB,SAAS,WAAW;AACrD,MAAI,KAAK,kBAAkB,OAAO,GAC9BA,sBAAqB;AAEzB,MAAI,GAAG,kBAAkB;AACvB,YAAQ,GAAG,kBAAkB,SAAS,OAAO;AAC3C,UAAI,GAAG,OAAO,SAAS,GAAG;AACxB,QAAAA,sBAAqB;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAOA;AACT;AAEO,SAAS,wBAAwB,SAAS;AAC/C,SAAO,mBAAmB,SAAS,2BAA2B;AAChE;AAEO,SAAS,6BAA6B,SAAS;AACpD,SAAO,mBAAmB,SAAS,gCAAgC;AACrE;AAEO,SAAS,6BAA6B,SAAS;AACpD,SAAO,mBAAmB,SAAS,gCAAgC;AACrE;", "names": ["hasEventDefinition"]}