.vxe-layout-aside {
  position: relative;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  transition: width 0.3s;
  overflow: hidden;
}
.vxe-layout-aside.is--default-width {
  width: var(--vxe-ui-layout-aside-default-width);
}
.vxe-layout-aside.is--collapse {
  width: var(--vxe-ui-layout-aside-collapse-width);
}
.vxe-layout-aside.is--padding {
  padding: var(--vxe-ui-layout-padding-default);
}

.vxe-layout-aside--inner {
  flex-grow: 1;
  overflow-y: auto;
  overflow-x: hidden;
  outline: 0;
}