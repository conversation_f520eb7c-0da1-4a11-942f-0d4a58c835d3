{"name": "vxe-table", "version": "4.13.41", "description": "一个基于 vue 的 PC 端表格组件，支持增删改查、虚拟树、拖拽排序，懒加载、快捷菜单、数据校验、树形结构、打印、导入导出、自定义模板、渲染器、JSON 配置式...", "scripts": {"update": "npm install --legacy-peer-deps", "serve": "vue-cli-service serve", "lint": "vue-cli-service lint", "build": "vue-cli-service build", "lib:modules": "gulp build", "lib:pack": "vue-cli-service build --target lib --name index --dest lib_temp index.ts", "lib": "npm run lib:pack && npm run lib:modules", "format": "eslint --fix examples/**/*.{js,ts,vue}"}, "files": ["lib", "es", "src", "helper", "types", "styles", "packages"], "main": "lib/index.common.js", "module": "es/index.esm.js", "unpkg": "lib/index.umd.js", "jsdelivr": "lib/index.umd.js", "style": "lib/style.css", "typings": "types/index.d.ts", "dependencies": {"vxe-pc-ui": "^4.6.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.11", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-standard": "^6.1.0", "@vue/eslint-config-typescript": "^9.1.0", "core-js": "^3.8.3", "del": "^6.1.1", "eslint": "^7.32.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^8.0.3", "gulp": "^4.0.2", "gulp-autoprefixer": "^8.0.0", "gulp-babel": "^8.0.0", "gulp-clean-css": "^4.3.0", "gulp-concat": "^2.6.1", "gulp-rename": "^2.0.0", "gulp-replace": "^1.1.4", "gulp-sass": "^5.1.0", "gulp-sourcemaps": "^3.0.0", "gulp-typescript": "^5.0.1", "gulp-uglify": "^3.0.2", "postcss": "^8.4.38", "sass": "^1.80.4", "sass-loader": "^14.2.0", "typescript": "~4.7.4", "vue": "3.4.27", "vue-router": "~4.5.1"}, "vetur": {"tags": "helper/vetur/tags.json", "attributes": "helper/vetur/attributes.json"}, "repository": {"type": "git", "url": "git+https://github.com/x-extends/vxe-table.git"}, "keywords": ["vxe", "vxe-ui", "vxe-table"], "author": {"name": "<PERSON>", "email": "xu_liang<PERSON><PERSON>@163.com"}, "license": "MIT", "bugs": {"url": "https://github.com/x-extends/vxe-table/issues"}}