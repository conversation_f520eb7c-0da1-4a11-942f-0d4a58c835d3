import "./chunk-GFI6XVUE.js";
import "./chunk-ZRTRFIWU.js";
import "./chunk-6C4OBBQZ.js";
import "./chunk-M5OFQAQB.js";
import {
  Icon
} from "./chunk-YPPVVTGH.js";
import {
  __decorate,
  __extends,
  __metadata
} from "./chunk-F25BIIHK.js";
import {
  Renderer,
  autobind,
  detectPropValueChanged,
  filter,
  getPropValue
} from "./chunk-LZQZ2OHM.js";
import "./chunk-QMS2VRR4.js";
import {
  require_upperFirst
} from "./chunk-5QW7M2DY.js";
import "./chunk-ZJNJ4ZV3.js";
import "./chunk-KEABM62F.js";
import "./chunk-CNJUQDSN.js";
import "./chunk-YZFIV7S4.js";
import "./chunk-7XBFYOTW.js";
import {
  require_react
} from "./chunk-ZUWHLQVA.js";
import "./chunk-KNGT5NUO.js";
import "./chunk-X3JSFYRQ.js";
import {
  __toESM
} from "./chunk-GFT2G5UO.js";

// node_modules/amis/esm/renderers/Audio.js
var import_react = __toESM(require_react());
var import_upperFirst = __toESM(require_upperFirst());
var Audio = (
  /** @class */
  function(_super) {
    __extends(Audio2, _super);
    function Audio2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.state = {
        src: getPropValue(_this.props, function(props) {
          return props.src ? filter(props.src, props.data, "| raw") : void 0;
        }) || "",
        isReady: false,
        muted: false,
        playing: false,
        played: 0,
        seeking: false,
        volume: 0.8,
        prevVolume: 0.8,
        loaded: 0,
        playbackRate: 1,
        showHandlePlaybackRate: false,
        showHandleVolume: false
      };
      return _this;
    }
    Audio2.prototype.componentWillUnmount = function() {
      clearTimeout(this.progressTimeout);
      clearTimeout(this.durationTimeout);
    };
    Audio2.prototype.componentDidMount = function() {
      var autoPlay = this.props.autoPlay;
      var playing = autoPlay ? true : false;
      this.setState({
        playing
      }, this.progress);
    };
    Audio2.prototype.componentDidUpdate = function(prevProps) {
      var _this = this;
      var props = this.props;
      detectPropValueChanged(props, prevProps, function(value) {
        return _this.setState({
          src: value,
          playing: false
        }, function() {
          _this.audio.load();
          _this.progress();
        });
      }, function(props2) {
        return props2.src ? filter(props2.src, props2.data, "| raw") : void 0;
      });
    };
    Audio2.prototype.progress = function() {
      clearTimeout(this.progressTimeout);
      if (this.state.src && this.audio) {
        var currentTime = this.audio.currentTime || 0;
        var duration = this.audio.duration;
        var played = currentTime / duration;
        var playing = this.state.playing;
        playing = played != 1 && playing ? true : false;
        this.setState({
          played,
          playing
        });
        this.progressTimeout = setTimeout(this.progress, this.props.progressInterval / this.state.playbackRate);
      }
    };
    Audio2.prototype.audioRef = function(audio) {
      this.audio = audio;
    };
    Audio2.prototype.load = function() {
      this.setState({
        isReady: true
      });
    };
    Audio2.prototype.handlePlaybackRate = function(rate) {
      this.audio.playbackRate = rate;
      this.setState({
        playbackRate: rate,
        showHandlePlaybackRate: false
      });
    };
    Audio2.prototype.handleMute = function() {
      if (!this.state.src) {
        return;
      }
      var _a = this.state, muted = _a.muted, prevVolume = _a.prevVolume;
      var curVolume = !muted ? 0 : prevVolume;
      this.audio.muted = !muted;
      this.setState({
        muted: !muted,
        volume: curVolume
      });
    };
    Audio2.prototype.handlePlaying = function() {
      if (!this.state.src) {
        return;
      }
      var playing = this.state.playing;
      playing ? this.audio.pause() : this.audio.play();
      this.setState({
        playing: !playing
      });
    };
    Audio2.prototype.getCurrentTime = function() {
      if (!this.audio || !this.state.src || !this.state.isReady) {
        return "0:00";
      }
      var duration = this.audio.duration;
      var played = this.state.played;
      return this.formatTime(duration * (played || 0));
    };
    Audio2.prototype.getDuration = function() {
      if (!this.audio || !this.state.src) {
        return "0:00";
      }
      if (!this.state.isReady) {
        this.onDurationCheck();
        return "0:00";
      }
      var _a = this.audio, duration = _a.duration, seekable = _a.seekable;
      if (duration === Infinity && seekable.length > 0) {
        return seekable.end(seekable.length - 1);
      }
      return this.formatTime(duration);
    };
    Audio2.prototype.onDurationCheck = function() {
      clearTimeout(this.durationTimeout);
      var duration = this.audio && this.audio.duration;
      if (!duration) {
        this.durationTimeout = setTimeout(this.onDurationCheck, 500);
      }
    };
    Audio2.prototype.onSeekChange = function(e) {
      if (!this.state.src) {
        return;
      }
      var played = e.target.value;
      this.setState({ played });
    };
    Audio2.prototype.onSeekMouseDown = function() {
      this.setState({ seeking: true });
    };
    Audio2.prototype.onSeekMouseUp = function(e) {
      if (!this.state.src) {
        return;
      }
      if (!this.state.seeking) {
        return;
      }
      var played = e.target.value;
      var duration = this.audio.duration;
      this.audio.currentTime = duration * played;
      var loop = this.props.loop;
      var playing = this.state.playing;
      playing = played < 1 || loop ? playing : false;
      this.setState({
        playing,
        seeking: false
      });
    };
    Audio2.prototype.setVolume = function(e) {
      if (!this.state.src) {
        return;
      }
      var volume = e.target.value;
      this.audio.volume = volume;
      this.setState({
        volume,
        prevVolume: volume
      });
    };
    Audio2.prototype.formatTime = function(seconds) {
      var date = new Date(seconds * 1e3);
      var hh = date.getUTCHours();
      var mm = isNaN(date.getUTCMinutes()) ? 0 : date.getUTCMinutes();
      var ss = isNaN(date.getUTCSeconds()) ? "00" : this.pad(date.getUTCSeconds());
      if (hh) {
        return "".concat(hh, ":").concat(this.pad(mm), ":").concat(ss);
      }
      return "".concat(mm, ":").concat(ss);
    };
    Audio2.prototype.pad = function(string) {
      return ("0" + string).slice(-2);
    };
    Audio2.prototype.toggleHandlePlaybackRate = function() {
      if (!this.state.src) {
        return;
      }
      this.setState({
        showHandlePlaybackRate: !this.state.showHandlePlaybackRate
      });
    };
    Audio2.prototype.toggleHandleVolume = function(type) {
      if (!this.state.src) {
        return;
      }
      this.setState({
        showHandleVolume: type
      });
    };
    Audio2.prototype.renderRates = function() {
      var _this = this;
      var _a = this.props, rates = _a.rates, cx = _a.classnames;
      var _b = this.state, showHandlePlaybackRate = _b.showHandlePlaybackRate, playbackRate = _b.playbackRate;
      return rates && rates.length ? showHandlePlaybackRate ? import_react.default.createElement("div", { className: cx("Audio-rateControl") }, rates.map(function(rate, index) {
        return import_react.default.createElement(
          "div",
          { key: index, className: cx("Audio-rateControlItem"), onClick: function() {
            return _this.handlePlaybackRate(rate);
          } },
          "x",
          rate.toFixed(1)
        );
      })) : import_react.default.createElement(
        "div",
        { className: cx("Audio-rates"), onClick: this.toggleHandlePlaybackRate },
        "x",
        playbackRate.toFixed(1)
      ) : null;
    };
    Audio2.prototype.renderPlay = function() {
      var cx = this.props.classnames;
      var playing = this.state.playing;
      return import_react.default.createElement("div", { className: cx("Audio-play"), onClick: this.handlePlaying }, playing ? import_react.default.createElement(Icon, { icon: "pause", className: "icon" }) : import_react.default.createElement(Icon, { icon: "play", className: "icon" }));
    };
    Audio2.prototype.renderTime = function() {
      var cx = this.props.classnames;
      return import_react.default.createElement(
        "div",
        { className: cx("Audio-times") },
        this.getCurrentTime(),
        " / ",
        this.getDuration()
      );
    };
    Audio2.prototype.renderProcess = function() {
      var cx = this.props.classnames;
      var played = this.state.played;
      return import_react.default.createElement(
        "div",
        { className: cx("Audio-process") },
        import_react.default.createElement("input", { type: "range", min: 0, max: 1, step: "any", value: played || 0, onMouseDown: this.onSeekMouseDown, onChange: this.onSeekChange, onMouseUp: this.onSeekMouseUp })
      );
    };
    Audio2.prototype.renderVolume = function() {
      var _this = this;
      var cx = this.props.classnames;
      var _a = this.state, volume = _a.volume, showHandleVolume = _a.showHandleVolume;
      return showHandleVolume ? import_react.default.createElement(
        "div",
        { className: cx("Audio-volumeControl"), onMouseLeave: function() {
          return _this.toggleHandleVolume(false);
        } },
        import_react.default.createElement("div", { className: cx("Audio-volumeControlIcon"), onClick: this.handleMute }, volume > 0 ? import_react.default.createElement(Icon, { icon: "volume", className: "icon" }) : import_react.default.createElement(Icon, { icon: "mute", className: "icon" })),
        import_react.default.createElement("input", { type: "range", min: 0, max: 1, step: "any", value: volume, onChange: this.setVolume })
      ) : import_react.default.createElement("div", { className: cx("Audio-volume"), onMouseEnter: function() {
        return _this.toggleHandleVolume(true);
      } }, volume > 0 ? import_react.default.createElement(Icon, { icon: "volume", className: "icon" }) : import_react.default.createElement(Icon, { icon: "mute", className: "icon" }));
    };
    Audio2.prototype.render = function() {
      var _this = this;
      var _a = this.props, className = _a.className, style = _a.style, inline = _a.inline, autoPlay = _a.autoPlay, loop = _a.loop, controls = _a.controls, cx = _a.classnames;
      var _b = this.state, muted = _b.muted, src = _b.src;
      return import_react.default.createElement(
        "div",
        { className: cx("Audio", className, inline ? "Audio--inline" : ""), style },
        import_react.default.createElement(
          "audio",
          { className: cx("Audio-original"), ref: this.audioRef, onCanPlay: this.load, autoPlay, controls: true, muted, loop },
          import_react.default.createElement("source", { src })
        ),
        import_react.default.createElement("div", { className: cx("Audio-controls") }, controls && controls.map(function(control, index) {
          control = "render" + (0, import_upperFirst.default)(control);
          var method = control;
          return import_react.default.createElement(import_react.default.Fragment, { key: index }, _this[method]());
        }))
      );
    };
    Audio2.defaultProps = {
      inline: true,
      autoPlay: false,
      playbackRate: 1,
      loop: false,
      rates: [],
      progressInterval: 1e3,
      controls: ["rates", "play", "time", "process", "volume"]
    };
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", void 0)
    ], Audio2.prototype, "progress", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [HTMLMediaElement]),
      __metadata("design:returntype", void 0)
    ], Audio2.prototype, "audioRef", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", void 0)
    ], Audio2.prototype, "load", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Number]),
      __metadata("design:returntype", void 0)
    ], Audio2.prototype, "handlePlaybackRate", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", void 0)
    ], Audio2.prototype, "handleMute", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", void 0)
    ], Audio2.prototype, "handlePlaying", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", void 0)
    ], Audio2.prototype, "getCurrentTime", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", void 0)
    ], Audio2.prototype, "getDuration", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", void 0)
    ], Audio2.prototype, "onDurationCheck", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], Audio2.prototype, "onSeekChange", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", void 0)
    ], Audio2.prototype, "onSeekMouseDown", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], Audio2.prototype, "onSeekMouseUp", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], Audio2.prototype, "setVolume", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Number]),
      __metadata("design:returntype", void 0)
    ], Audio2.prototype, "formatTime", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Number]),
      __metadata("design:returntype", void 0)
    ], Audio2.prototype, "pad", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", void 0)
    ], Audio2.prototype, "toggleHandlePlaybackRate", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Boolean]),
      __metadata("design:returntype", void 0)
    ], Audio2.prototype, "toggleHandleVolume", null);
    return Audio2;
  }(import_react.default.Component)
);
var AudioRenderer = (
  /** @class */
  function(_super) {
    __extends(AudioRenderer2, _super);
    function AudioRenderer2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    AudioRenderer2 = __decorate([
      Renderer({
        type: "audio"
      })
    ], AudioRenderer2);
    return AudioRenderer2;
  }(Audio)
);
export {
  Audio,
  AudioRenderer
};
//# sourceMappingURL=Audio-3XQ4XLY7.js.map
