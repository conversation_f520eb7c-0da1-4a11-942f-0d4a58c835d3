Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=exports.VxeGrid=exports.Grid=void 0;var _ui=require("../ui"),_grid=_interopRequireDefault(require("./src/grid"));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let VxeGrid=exports.VxeGrid=Object.assign({},_grid.default,{install(e){e.component(_grid.default.name,_grid.default)}}),Grid=(_ui.VxeUI.dynamicApp&&_ui.VxeUI.dynamicApp.component(_grid.default.name,_grid.default),_ui.VxeUI.component(_grid.default),exports.Grid=VxeGrid);var _default=exports.default=VxeGrid;