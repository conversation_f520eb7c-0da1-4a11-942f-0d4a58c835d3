import {
  supportStatic
} from "./chunk-JHAOQP73.js";
import {
  __assign,
  __decorate,
  __extends,
  __metadata
} from "./chunk-F25BIIHK.js";
import {
  OptionsControl,
  autobind,
  getLevelFromClassName,
  getVariable,
  isObject
} from "./chunk-LZQZ2OHM.js";
import {
  require_react
} from "./chunk-ZUWHLQVA.js";
import {
  __toESM
} from "./chunk-GFT2G5UO.js";

// node_modules/amis/esm/renderers/Form/ButtonGroupSelect.js
var import_react = __toESM(require_react());
var ButtonGroupControl = (
  /** @class */
  function(_super) {
    __extends(ButtonGroupControl2, _super);
    function ButtonGroupControl2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    ButtonGroupControl2.prototype.doAction = function(action, data, throwErrors) {
      var _a, _b;
      var _c = this.props, resetValue = _c.resetValue, onChange = _c.onChange, formStore = _c.formStore, store = _c.store, name = _c.name;
      var actionType = action === null || action === void 0 ? void 0 : action.actionType;
      if (actionType === "clear") {
        onChange("");
      } else if (actionType === "reset") {
        var pristineVal = (_b = getVariable((_a = formStore === null || formStore === void 0 ? void 0 : formStore.pristine) !== null && _a !== void 0 ? _a : store === null || store === void 0 ? void 0 : store.pristine, name)) !== null && _b !== void 0 ? _b : resetValue;
        onChange(pristineVal !== null && pristineVal !== void 0 ? pristineVal : "");
      }
    };
    ButtonGroupControl2.prototype.handleToggle = function(option) {
      var _a = this.props, onToggle = _a.onToggle, multiple = _a.multiple, autoFill = _a.autoFill, onBulkChange = _a.onBulkChange;
      onToggle(option);
    };
    ButtonGroupControl2.prototype.reload = function(subpath, query) {
      var reload = this.props.reloadOptions;
      reload && reload(subpath, query);
    };
    ButtonGroupControl2.prototype.getBadgeConfig = function(config, item) {
      return config ? (item === null || item === void 0 ? void 0 : item.badge) && (typeof item.badge === "string" || typeof item.badge === "number") ? __assign(__assign({}, config), { text: item.badge }) : (item === null || item === void 0 ? void 0 : item.badge) && isObject(item.badge) ? __assign(__assign({}, config), item.badge) : null : item.badge;
    };
    ButtonGroupControl2.prototype.render = function(props) {
      var _a;
      var _this = this;
      if (props === void 0) {
        props = this.props;
      }
      var render = props.render, ns = props.classPrefix, cx = props.classnames, className = props.className, style = props.style, disabled = props.disabled, options = props.options, value = props.value, labelField = props.labelField, placeholder = props.placeholder, btnClassName = props.btnClassName, btnActiveClassName = props.btnActiveClassName, selectedOptions = props.selectedOptions, buttons = props.buttons, size = props.size, block = props.block, vertical = props.vertical, tiled = props.tiled, badge = props.badge, testIdBuilder = props.testIdBuilder, __ = props.translate;
      var body = [];
      var btnLevel = props.btnLevel;
      var btnActiveLevel = props.btnActiveLevel;
      btnClassName && (btnLevel = getLevelFromClassName(btnClassName));
      btnActiveClassName && (btnActiveLevel = getLevelFromClassName(btnActiveClassName));
      if (options && options.length) {
        body = options.map(function(option, key) {
          var active = !!~selectedOptions.indexOf(option);
          var optionBadge = _this.getBadgeConfig(badge, option);
          return render("option/".concat(key), {
            label: option[labelField || "label"],
            icon: option.icon,
            size: option.size || size,
            badge: optionBadge,
            type: "button",
            block
          }, {
            key,
            level: (active ? btnActiveLevel : "") || option.level || btnLevel,
            className: cx(option.className, btnClassName, active && "ButtonGroup-button--active"),
            disabled: option.disabled || disabled,
            testIdBuilder: testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild("item-".concat(option[labelField || "label"] || key)),
            onClick: function(e) {
              if (disabled) {
                return;
              }
              _this.handleToggle(option);
              e.preventDefault();
            }
          });
        });
      } else if (Array.isArray(buttons)) {
        body = buttons.map(function(button, key) {
          var buttonBadge = _this.getBadgeConfig(badge, button);
          return render("button/".concat(key), __assign(__assign({ size, block, activeLevel: btnActiveLevel, level: btnLevel, disabled }, button), { badge: buttonBadge }), {
            key,
            className: cx(button.className, btnClassName)
          });
        });
      }
      return import_react.default.createElement("div", { className: cx("ButtonGroup", (_a = {
        "ButtonGroup--block": block,
        "ButtonGroup--vertical": vertical,
        "ButtonGroup--tiled": tiled
      }, _a["ButtonGroup--".concat(size)] = size, _a), className) }, body.length ? body : import_react.default.createElement("span", { className: "".concat(ns, "ButtonGroup-placeholder") }, __(placeholder)));
    };
    ButtonGroupControl2.defaultProps = {
      btnLevel: "default",
      btnActiveLevel: "primary",
      clearable: false,
      vertical: false
    };
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], ButtonGroupControl2.prototype, "handleToggle", null);
    __decorate([
      supportStatic(),
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], ButtonGroupControl2.prototype, "render", null);
    return ButtonGroupControl2;
  }(import_react.default.Component)
);
var ButtonGroupControlRenderer = (
  /** @class */
  function(_super) {
    __extends(ButtonGroupControlRenderer2, _super);
    function ButtonGroupControlRenderer2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    ButtonGroupControlRenderer2 = __decorate([
      OptionsControl({
        type: "button-group-select",
        sizeMutable: false,
        strictMode: false
      })
    ], ButtonGroupControlRenderer2);
    return ButtonGroupControlRenderer2;
  }(ButtonGroupControl)
);

export {
  ButtonGroupControl,
  ButtonGroupControlRenderer
};
//# sourceMappingURL=chunk-2ZPAPTZ4.js.map
