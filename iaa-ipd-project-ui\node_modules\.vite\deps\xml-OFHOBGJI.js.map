{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/xml/xml.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { languages } from '../fillers/monaco-editor-core.js';\nexport var conf = {\n    comments: {\n        blockComment: ['<!--', '-->']\n    },\n    brackets: [['<', '>']],\n    autoClosingPairs: [\n        { open: '<', close: '>' },\n        { open: \"'\", close: \"'\" },\n        { open: '\"', close: '\"' }\n    ],\n    surroundingPairs: [\n        { open: '<', close: '>' },\n        { open: \"'\", close: \"'\" },\n        { open: '\"', close: '\"' }\n    ],\n    onEnterRules: [\n        {\n            beforeText: new RegExp(\"<([_:\\\\w][_:\\\\w-.\\\\d]*)([^/>]*(?!/)>)[^<]*$\", 'i'),\n            afterText: /^<\\/([_:\\w][_:\\w-.\\d]*)\\s*>$/i,\n            action: {\n                indentAction: languages.IndentAction.IndentOutdent\n            }\n        },\n        {\n            beforeText: new RegExp(\"<(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$\", 'i'),\n            action: { indentAction: languages.IndentAction.Indent }\n        }\n    ]\n};\nexport var language = {\n    defaultToken: '',\n    tokenPostfix: '.xml',\n    ignoreCase: true,\n    // Useful regular expressions\n    qualifiedName: /(?:[\\w\\.\\-]+:)?[\\w\\.\\-]+/,\n    tokenizer: {\n        root: [\n            [/[^<&]+/, ''],\n            { include: '@whitespace' },\n            // Standard opening tag\n            [/(<)(@qualifiedName)/, [{ token: 'delimiter' }, { token: 'tag', next: '@tag' }]],\n            // Standard closing tag\n            [\n                /(<\\/)(@qualifiedName)(\\s*)(>)/,\n                [{ token: 'delimiter' }, { token: 'tag' }, '', { token: 'delimiter' }]\n            ],\n            // Meta tags - instruction\n            [/(<\\?)(@qualifiedName)/, [{ token: 'delimiter' }, { token: 'metatag', next: '@tag' }]],\n            // Meta tags - declaration\n            [/(<\\!)(@qualifiedName)/, [{ token: 'delimiter' }, { token: 'metatag', next: '@tag' }]],\n            // CDATA\n            [/<\\!\\[CDATA\\[/, { token: 'delimiter.cdata', next: '@cdata' }],\n            [/&\\w+;/, 'string.escape']\n        ],\n        cdata: [\n            [/[^\\]]+/, ''],\n            [/\\]\\]>/, { token: 'delimiter.cdata', next: '@pop' }],\n            [/\\]/, '']\n        ],\n        tag: [\n            [/[ \\t\\r\\n]+/, ''],\n            [/(@qualifiedName)(\\s*=\\s*)(\"[^\"]*\"|'[^']*')/, ['attribute.name', '', 'attribute.value']],\n            [\n                /(@qualifiedName)(\\s*=\\s*)(\"[^\">?\\/]*|'[^'>?\\/]*)(?=[\\?\\/]\\>)/,\n                ['attribute.name', '', 'attribute.value']\n            ],\n            [/(@qualifiedName)(\\s*=\\s*)(\"[^\">]*|'[^'>]*)/, ['attribute.name', '', 'attribute.value']],\n            [/@qualifiedName/, 'attribute.name'],\n            [/\\?>/, { token: 'delimiter', next: '@pop' }],\n            [/(\\/)(>)/, [{ token: 'tag' }, { token: 'delimiter', next: '@pop' }]],\n            [/>/, { token: 'delimiter', next: '@pop' }]\n        ],\n        whitespace: [\n            [/[ \\t\\r\\n]+/, ''],\n            [/<!--/, { token: 'comment', next: '@comment' }]\n        ],\n        comment: [\n            [/[^<\\-]+/, 'comment.content'],\n            [/-->/, { token: 'comment', next: '@pop' }],\n            [/<!--/, 'comment.content.invalid'],\n            [/[<\\-]/, 'comment.content']\n        ]\n    }\n};\n"], "mappings": ";;;;;;;AAKO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,cAAc,CAAC,QAAQ,KAAK;AAAA,EAChC;AAAA,EACA,UAAU,CAAC,CAAC,KAAK,GAAG,CAAC;AAAA,EACrB,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,cAAc;AAAA,IACV;AAAA,MACI,YAAY,IAAI,OAAO,+CAA+C,GAAG;AAAA,MACzE,WAAW;AAAA,MACX,QAAQ;AAAA,QACJ,cAAc,UAAU,aAAa;AAAA,MACzC;AAAA,IACJ;AAAA,IACA;AAAA,MACI,YAAY,IAAI,OAAO,uCAAuC,GAAG;AAAA,MACjE,QAAQ,EAAE,cAAc,UAAU,aAAa,OAAO;AAAA,IAC1D;AAAA,EACJ;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA;AAAA,EAEZ,eAAe;AAAA,EACf,WAAW;AAAA,IACP,MAAM;AAAA,MACF,CAAC,UAAU,EAAE;AAAA,MACb,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB,CAAC,uBAAuB,CAAC,EAAE,OAAO,YAAY,GAAG,EAAE,OAAO,OAAO,MAAM,OAAO,CAAC,CAAC;AAAA;AAAA,MAEhF;AAAA,QACI;AAAA,QACA,CAAC,EAAE,OAAO,YAAY,GAAG,EAAE,OAAO,MAAM,GAAG,IAAI,EAAE,OAAO,YAAY,CAAC;AAAA,MACzE;AAAA;AAAA,MAEA,CAAC,yBAAyB,CAAC,EAAE,OAAO,YAAY,GAAG,EAAE,OAAO,WAAW,MAAM,OAAO,CAAC,CAAC;AAAA;AAAA,MAEtF,CAAC,yBAAyB,CAAC,EAAE,OAAO,YAAY,GAAG,EAAE,OAAO,WAAW,MAAM,OAAO,CAAC,CAAC;AAAA;AAAA,MAEtF,CAAC,gBAAgB,EAAE,OAAO,mBAAmB,MAAM,SAAS,CAAC;AAAA,MAC7D,CAAC,SAAS,eAAe;AAAA,IAC7B;AAAA,IACA,OAAO;AAAA,MACH,CAAC,UAAU,EAAE;AAAA,MACb,CAAC,SAAS,EAAE,OAAO,mBAAmB,MAAM,OAAO,CAAC;AAAA,MACpD,CAAC,MAAM,EAAE;AAAA,IACb;AAAA,IACA,KAAK;AAAA,MACD,CAAC,cAAc,EAAE;AAAA,MACjB,CAAC,8CAA8C,CAAC,kBAAkB,IAAI,iBAAiB,CAAC;AAAA,MACxF;AAAA,QACI;AAAA,QACA,CAAC,kBAAkB,IAAI,iBAAiB;AAAA,MAC5C;AAAA,MACA,CAAC,8CAA8C,CAAC,kBAAkB,IAAI,iBAAiB,CAAC;AAAA,MACxF,CAAC,kBAAkB,gBAAgB;AAAA,MACnC,CAAC,OAAO,EAAE,OAAO,aAAa,MAAM,OAAO,CAAC;AAAA,MAC5C,CAAC,WAAW,CAAC,EAAE,OAAO,MAAM,GAAG,EAAE,OAAO,aAAa,MAAM,OAAO,CAAC,CAAC;AAAA,MACpE,CAAC,KAAK,EAAE,OAAO,aAAa,MAAM,OAAO,CAAC;AAAA,IAC9C;AAAA,IACA,YAAY;AAAA,MACR,CAAC,cAAc,EAAE;AAAA,MACjB,CAAC,QAAQ,EAAE,OAAO,WAAW,MAAM,WAAW,CAAC;AAAA,IACnD;AAAA,IACA,SAAS;AAAA,MACL,CAAC,WAAW,iBAAiB;AAAA,MAC7B,CAAC,OAAO,EAAE,OAAO,WAAW,MAAM,OAAO,CAAC;AAAA,MAC1C,CAAC,QAAQ,yBAAyB;AAAA,MAClC,CAAC,SAAS,iBAAiB;AAAA,IAC/B;AAAA,EACJ;AACJ;", "names": []}