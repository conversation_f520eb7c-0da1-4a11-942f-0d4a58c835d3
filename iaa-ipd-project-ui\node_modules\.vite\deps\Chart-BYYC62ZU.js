import {
  isAlive
} from "./chunk-GHYHD6QH.js";
import "./chunk-ECE3U5IW.js";
import {
  __assign,
  __awaiter,
  __decorate,
  __extends,
  __generator,
  __metadata,
  __read,
  __values
} from "./chunk-F25BIIHK.js";
import {
  CustomStyle,
  Renderer,
  ScopedContext,
  ServiceStore,
  buildStyle,
  createObject,
  dataMapping,
  filter,
  findObjectsWithKey,
  isApiOutdated,
  isEffectiveApi,
  isPureVariable,
  loadScript,
  normalizeApiResponseData,
  require_classnames,
  require_debounce,
  require_isString,
  require_pick,
  resizeSensor,
  resolveVariableAndFilter,
  setThemeClassName,
  str2function,
  themedLazyComponent
} from "./chunk-LZQZ2OHM.js";
import "./chunk-5QW7M2DY.js";
import "./chunk-ZJNJ4ZV3.js";
import "./chunk-CNJUQDSN.js";
import "./chunk-7XBFYOTW.js";
import {
  require_react
} from "./chunk-ZUWHLQVA.js";
import {
  __toESM
} from "./chunk-GFT2G5UO.js";

// node_modules/amis/esm/renderers/Chart.js
var import_react = __toESM(require_react());
var import_classnames = __toESM(require_classnames());
var import_debounce = __toESM(require_debounce());
var import_pick = __toESM(require_pick());
var import_isString = __toESM(require_isString());
var DEFAULT_EVENT_PARAMS = [
  "componentType",
  "seriesType",
  "seriesIndex",
  "seriesName",
  "name",
  "dataIndex",
  "data",
  "dataType",
  "value",
  "color"
];
var EVAL_CACHE = {};
function recoverFunctionType(config) {
  [
    "interval",
    "formatter",
    "color",
    "min",
    "max",
    "labelFormatter",
    "valueFormatter",
    "pageFormatter",
    "optionToContent",
    "contentToOption",
    "animationDelay",
    "animationDurationUpdate",
    "animationDelayUpdate",
    "animationDuration",
    "position",
    "sort",
    "renderItem"
  ].forEach(function(key) {
    var e_1, _a;
    var objects = findObjectsWithKey(config, key);
    try {
      for (var objects_1 = __values(objects), objects_1_1 = objects_1.next(); !objects_1_1.done; objects_1_1 = objects_1.next()) {
        var object = objects_1_1.value;
        var code = object[key];
        if (typeof code === "string" && code.trim().startsWith("function")) {
          try {
            if (!(code in EVAL_CACHE)) {
              EVAL_CACHE[code] = eval("(" + code + ")");
            }
            object[key] = EVAL_CACHE[code];
          } catch (e) {
            console.warn(code, e);
          }
        }
      }
    } catch (e_1_1) {
      e_1 = { error: e_1_1 };
    } finally {
      try {
        if (objects_1_1 && !objects_1_1.done && (_a = objects_1.return))
          _a.call(objects_1);
      } finally {
        if (e_1)
          throw e_1.error;
      }
    }
  });
}
var Chart = (
  /** @class */
  function(_super) {
    __extends(Chart2, _super);
    function Chart2(props) {
      var _this = _super.call(this, props) || this;
      _this.refFn = _this.refFn.bind(_this);
      _this.reload = _this.reload.bind(_this);
      _this.reloadEcharts = (0, import_debounce.default)(_this.reloadEcharts.bind(_this), 300);
      _this.handleClick = _this.handleClick.bind(_this);
      _this.dispatchEvent = _this.dispatchEvent.bind(_this);
      _this.loadChartMapData = _this.loadChartMapData.bind(_this);
      _this.mounted = true;
      props.config && _this.renderChart(props.config);
      return _this;
    }
    Chart2.prototype.componentDidMount = function() {
      return __awaiter(this, void 0, void 0, function() {
        var _a2, api, data, initFetch, source, dispatchEvent, rendererEvent, ret;
        return __generator(this, function(_b) {
          switch (_b.label) {
            case 0:
              _a2 = this.props, api = _a2.api, data = _a2.data, initFetch = _a2.initFetch, source = _a2.source, dispatchEvent = _a2.dispatchEvent;
              return [4, dispatchEvent("init", data, this)];
            case 1:
              rendererEvent = _b.sent();
              if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                return [
                  2
                  /*return*/
                ];
              }
              if (source && isPureVariable(source)) {
                ret = resolveVariableAndFilter(source, data, "| raw");
                ret && this.renderChart(ret);
              } else if (api && initFetch !== false) {
                this.reload();
              }
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    Chart2.prototype.componentDidUpdate = function(prevProps) {
      var _this = this;
      var props = this.props;
      if (isApiOutdated(prevProps.api, props.api, prevProps.data, props.data)) {
        this.reload();
      } else if (props.source && isPureVariable(props.source)) {
        var prevRet = prevProps.source ? resolveVariableAndFilter(prevProps.source, prevProps.data, "| raw") : null;
        var ret = resolveVariableAndFilter(props.source, props.data, "| raw");
        if (prevRet !== ret) {
          this.renderChart(ret || {});
        }
      } else if (props.config !== prevProps.config) {
        this.renderChart(props.config || {});
      } else if (props.config && props.trackExpression && filter(props.trackExpression, props.data) !== filter(prevProps.trackExpression, prevProps.data)) {
        this.renderChart(props.config || {});
      } else if (isApiOutdated(prevProps.mapURL, props.mapURL, prevProps.data, props.data)) {
        var source_1 = props.source, data_1 = props.data, api_1 = props.api, config_1 = props.config;
        this.loadChartMapData(function() {
          if (source_1 && isPureVariable(source_1)) {
            var ret2 = resolveVariableAndFilter(source_1, data_1, "| raw");
            ret2 && _this.renderChart(ret2);
          } else if (api_1) {
            _this.reload();
          } else if (config_1) {
            _this.renderChart(config_1 || {});
          }
        });
      }
    };
    Chart2.prototype.componentWillUnmount = function() {
      this.mounted = false;
      this.reloadEcharts.cancel();
      clearTimeout(this.timer);
    };
    Chart2.prototype.loadChartMapData = function(callBackFn) {
      return __awaiter(this, void 0, void 0, function() {
        var _a2, env, data, _b, mapName, mapURL, mapGeoResult;
        return __generator(this, function(_c) {
          switch (_c.label) {
            case 0:
              _a2 = this.props, env = _a2.env, data = _a2.data;
              _b = this.props, mapName = _b.mapName, mapURL = _b.mapURL;
              if (!(mapURL && mapName && window.echarts))
                return [3, 2];
              if (isPureVariable(mapName)) {
                mapName = resolveVariableAndFilter(mapName, data);
              }
              return [4, env.fetcher(mapURL, data)];
            case 1:
              mapGeoResult = _c.sent();
              if (!mapGeoResult.ok) {
                console.warn("fetch map geo error " + mapURL);
              }
              window.echarts.registerMap(mapName, mapGeoResult.data);
              _c.label = 2;
            case 2:
              if (callBackFn) {
                callBackFn();
              }
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    Chart2.prototype.handleClick = function(ctx) {
      return __awaiter(this, void 0, void 0, function() {
        var _a2, onAction, clickAction, data, dispatchEvent, rendererEvent;
        return __generator(this, function(_b) {
          switch (_b.label) {
            case 0:
              _a2 = this.props, onAction = _a2.onAction, clickAction = _a2.clickAction, data = _a2.data, dispatchEvent = _a2.dispatchEvent;
              return [4, dispatchEvent(ctx.event, createObject(data, __assign({}, (0, import_pick.default)(ctx, DEFAULT_EVENT_PARAMS))))];
            case 1:
              rendererEvent = _b.sent();
              if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                return [
                  2
                  /*return*/
                ];
              }
              clickAction && onAction && onAction(null, clickAction, createObject(data, ctx));
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    Chart2.prototype.dispatchEvent = function(ctx) {
      var _a2 = this.props, data = _a2.data, dispatchEvent = _a2.dispatchEvent;
      dispatchEvent(ctx.event || ctx.type, createObject(data, __assign({}, (0, import_pick.default)(ctx, ctx.type === "legendselectchanged" ? ["name", "selected"] : DEFAULT_EVENT_PARAMS))));
    };
    Chart2.prototype.refFn = function(ref) {
      var _this = this;
      var chartRef = this.props.chartRef;
      var _a2 = this.props, chartTheme = _a2.chartTheme, onChartWillMount = _a2.onChartWillMount, onChartUnMount = _a2.onChartUnMount, env = _a2.env, loadBaiduMap = _a2.loadBaiduMap, data = _a2.data;
      var _b = this.props, mapURL = _b.mapURL, mapName = _b.mapName;
      var onChartMount = this.props.onChartMount || this.onChartMount;
      if (ref) {
        Promise.all([
          import("./echarts.js"),
          import("./echarts-stat-Z35GD2RG.js"),
          // @ts-ignore 官方没提供 type
          import("./dataTool-CR3MZP4F.js"),
          // @ts-ignore 官方没提供 type
          import("./bmap-AYXO53F5.js"),
          // @ts-ignore 官方没提供 type
          import("./echarts-wordcloud-AOJB2SPF.js")
        ]).then(function(_a3) {
          var _b2 = __read(_a3, 2), echarts = _b2[0], ecStat = _b2[1];
          return __awaiter(_this, void 0, void 0, function() {
            var theme;
            var _this2 = this;
            return __generator(this, function(_c) {
              switch (_c.label) {
                case 0:
                  window.echarts = echarts;
                  window.ecStat = (ecStat === null || ecStat === void 0 ? void 0 : ecStat.default) || ecStat;
                  if (!(mapURL && mapName))
                    return [3, 2];
                  return [4, this.loadChartMapData()];
                case 1:
                  _c.sent();
                  _c.label = 2;
                case 2:
                  if (!loadBaiduMap)
                    return [3, 4];
                  return [4, loadScript("//api.map.baidu.com/api?v=3.0&ak=".concat(this.props.ak, "&callback={{callback}}"))];
                case 3:
                  _c.sent();
                  _c.label = 4;
                case 4:
                  theme = "default";
                  if (chartTheme) {
                    echarts.registerTheme("custom", chartTheme);
                    theme = "custom";
                  }
                  if (!onChartWillMount)
                    return [3, 6];
                  return [4, onChartWillMount(echarts)];
                case 5:
                  _c.sent();
                  _c.label = 6;
                case 6:
                  if (ecStat.transform) {
                    echarts.registerTransform(ecStat.transform.regression);
                    echarts.registerTransform(ecStat.transform.histogram);
                    echarts.registerTransform(ecStat.transform.clustering);
                  }
                  if (!env.loadChartExtends)
                    return [3, 8];
                  return [4, env.loadChartExtends()];
                case 7:
                  _c.sent();
                  _c.label = 8;
                case 8:
                  this.echarts = echarts.init(ref, theme);
                  if (typeof onChartMount === "string") {
                    onChartMount = str2function(onChartMount, "chart", "echarts");
                  }
                  onChartMount === null || onChartMount === void 0 ? void 0 : onChartMount(this.echarts, echarts);
                  this.echarts.on("click", this.handleClick);
                  this.echarts.on("mouseover", this.dispatchEvent);
                  this.echarts.on("legendselectchanged", this.dispatchEvent);
                  this.unSensor = resizeSensor(ref, function() {
                    var _a4;
                    var width = ref.offsetWidth;
                    var height = ref.offsetHeight;
                    (_a4 = _this2.echarts) === null || _a4 === void 0 ? void 0 : _a4.resize({
                      width,
                      height
                    });
                  });
                  chartRef && chartRef(this.echarts);
                  this.renderChart();
                  return [
                    2
                    /*return*/
                  ];
              }
            });
          });
        });
      } else {
        chartRef && chartRef(null);
        this.unSensor && this.unSensor();
        if (this.echarts) {
          onChartUnMount === null || onChartUnMount === void 0 ? void 0 : onChartUnMount(this.echarts, window.echarts);
          this.echarts.dispose();
          delete this.echarts;
        }
      }
      this.ref = ref;
    };
    Chart2.prototype.doAction = function(action, data, throwErrors) {
      var _a2, _b;
      if (throwErrors === void 0) {
        throwErrors = false;
      }
      return (_b = (_a2 = this.echarts) === null || _a2 === void 0 ? void 0 : _a2.dispatchAction) === null || _b === void 0 ? void 0 : _b.call(_a2, __assign({ type: action.actionType }, data));
    };
    Chart2.prototype.reload = function(subpath, query, ctx, silent, replace) {
      var _a2, _b, _c, _d, _e, _f;
      return __awaiter(this, void 0, void 0, function() {
        var _g, api, env, store, __, result, data, ctx_1, curInterval, reason_1;
        var _this = this;
        return __generator(this, function(_h) {
          switch (_h.label) {
            case 0:
              _g = this.props, api = _g.api, env = _g.env, store = _g.store, __ = _g.translate;
              if (query) {
                return [2, this.receive(query, void 0, replace)];
              } else if (!env || !env.fetcher || !isEffectiveApi(api, store.data)) {
                return [
                  2
                  /*return*/
                ];
              }
              clearTimeout(this.timer);
              if (this.reloadCancel) {
                this.reloadCancel();
                delete this.reloadCancel;
                (_a2 = this.echarts) === null || _a2 === void 0 ? void 0 : _a2.hideLoading();
              }
              (_b = this.echarts) === null || _b === void 0 ? void 0 : _b.showLoading();
              store.markFetching(true);
              _h.label = 1;
            case 1:
              _h.trys.push([1, 3, , 4]);
              return [4, env.fetcher(api, store.data, {
                cancelExecutor: function(executor) {
                  return _this.reloadCancel = executor;
                }
              })];
            case 2:
              result = _h.sent();
              isAlive(store) && store.markFetching(false);
              if (!result.ok) {
                !(api === null || api === void 0 ? void 0 : api.silent) && env.notify("error", (_d = (_c = api === null || api === void 0 ? void 0 : api.messages) === null || _c === void 0 ? void 0 : _c.failed) !== null && _d !== void 0 ? _d : result.msg || __("fetchFailed"), result.msgTimeout !== void 0 ? {
                  closeButton: true,
                  timeout: result.msgTimeout
                } : void 0);
                return [
                  2
                  /*return*/
                ];
              }
              delete this.reloadCancel;
              data = normalizeApiResponseData(result.data);
              if (!data.series && this.props.config) {
                ctx_1 = createObject(this.props.data, data);
                this.renderChart(this.props.config, ctx_1);
              } else {
                this.renderChart(result.data || {});
              }
              (_e = this.echarts) === null || _e === void 0 ? void 0 : _e.hideLoading();
              curInterval = this.props.interval;
              if (curInterval && (0, import_isString.default)(curInterval)) {
                curInterval = Number.parseInt(curInterval);
              }
              curInterval && this.mounted && (this.timer = setTimeout(this.reload, Math.max(curInterval, 1e3)));
              return [2, store.data];
            case 3:
              reason_1 = _h.sent();
              if (env.isCancel(reason_1)) {
                return [
                  2
                  /*return*/
                ];
              }
              isAlive(store) && store.markFetching(false);
              !(api === null || api === void 0 ? void 0 : api.silent) && env.notify("error", reason_1);
              (_f = this.echarts) === null || _f === void 0 ? void 0 : _f.hideLoading();
              return [3, 4];
            case 4:
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    Chart2.prototype.receive = function(data, subPath, replace) {
      var store = this.props.store;
      store.updateData(data, void 0, replace);
      this.reload();
    };
    Chart2.prototype.renderChart = function(config2, data) {
      var _a2, _b;
      config2 && (this.pending = config2);
      data && (this.pendingCtx = data);
      if (!this.echarts) {
        return;
      }
      var store = this.props.store;
      var onDataFilter = this.props.onDataFilter;
      var dataFilter = this.props.dataFilter;
      if (!onDataFilter && typeof dataFilter === "string") {
        onDataFilter = new Function("config", "echarts", "data", dataFilter);
      }
      config2 = config2 || this.pending;
      data = data || this.pendingCtx || this.props.data;
      if (typeof config2 === "string") {
        config2 = new Function("return " + config2)();
      }
      try {
        onDataFilter && (config2 = onDataFilter(config2, window.echarts, data) || config2);
      } catch (e) {
        console.warn(e);
      }
      if (config2) {
        try {
          if (!this.props.disableDataMapping) {
            config2 = dataMapping(config2, data, function(key2, value) {
              return typeof value === "function" || typeof value === "string" && value.startsWith("function");
            });
          }
          recoverFunctionType(config2);
          if (isAlive(store) && store.loading) {
            (_a2 = this.echarts) === null || _a2 === void 0 ? void 0 : _a2.showLoading();
          } else {
            (_b = this.echarts) === null || _b === void 0 ? void 0 : _b.hideLoading();
          }
          this.reloadEcharts(config2);
        } catch (e) {
          console.warn(e);
        }
      }
    };
    Chart2.prototype.reloadEcharts = function(config2) {
      var _this = this;
      var _a2;
      (_a2 = this.echarts) === null || _a2 === void 0 ? void 0 : _a2.setOption(config2, this.props.replaceChartOption);
      this.echarts.on("finished", function() {
        return __awaiter(_this, void 0, void 0, function() {
          var _a3, data, dispatchEvent, rendererEvent;
          return __generator(this, function(_b) {
            switch (_b.label) {
              case 0:
                _a3 = this.props, data = _a3.data, dispatchEvent = _a3.dispatchEvent;
                return [4, dispatchEvent("finished", createObject(data, {}, {
                  echarts: {
                    value: this.echarts,
                    enumerable: false,
                    configurable: true,
                    writable: true
                  }
                }))];
              case 1:
                rendererEvent = _b.sent();
                if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                  return [
                    2
                    /*return*/
                  ];
                }
                return [
                  2
                  /*return*/
                ];
            }
          });
        });
      });
    };
    Chart2.prototype.render = function() {
      var _this = this;
      var _a2 = this.props, className = _a2.className, width = _a2.width, height = _a2.height, ns = _a2.classPrefix, unMountOnHidden = _a2.unMountOnHidden, data = _a2.data, id = _a2.id, wrapperCustomStyle = _a2.wrapperCustomStyle, env = _a2.env, themeCss = _a2.themeCss, baseControlClassName = _a2.baseControlClassName;
      var style = this.props.style || {};
      style.width = style.width || width || "100%";
      style.height = style.height || height || "300px";
      var styleVar = buildStyle(style, data);
      return import_react.default.createElement(
        "div",
        { className: (0, import_classnames.default)("".concat(ns, "Chart"), className, setThemeClassName(__assign(__assign({}, this.props), { name: "baseControlClassName", id, themeCss })), setThemeClassName(__assign(__assign({}, this.props), { name: "wrapperCustomStyle", id, themeCss: wrapperCustomStyle }))), style: styleVar },
        import_react.default.createElement(themedLazyComponent, {
          unMountOnHidden,
          placeholder: "...",
          component: function() {
            return import_react.default.createElement("div", { className: "".concat(ns, "Chart-content"), ref: _this.refFn });
          }
        }),
        import_react.default.createElement(CustomStyle, __assign({}, this.props, { config: {
          wrapperCustomStyle,
          id,
          themeCss,
          classNames: [
            {
              key: "baseControlClassName"
            }
          ]
        }, env }))
      );
    };
    Chart2.defaultProps = {
      replaceChartOption: false,
      unMountOnHidden: false
    };
    Chart2.propsList = [];
    return Chart2;
  }(import_react.default.Component)
);
var ChartRenderer = (
  /** @class */
  function(_super) {
    __extends(ChartRenderer2, _super);
    function ChartRenderer2(props, context) {
      var _this = _super.call(this, props) || this;
      var scoped = context;
      scoped.registerComponent(_this);
      return _this;
    }
    ChartRenderer2.prototype.componentWillUnmount = function() {
      _super.prototype.componentWillUnmount.call(this);
      var scoped = this.context;
      scoped.unRegisterComponent(this);
    };
    ChartRenderer2.prototype.setData = function(values, replace) {
      var store = this.props.store;
      store.updateData(values, void 0, replace);
      this.renderChart(this.props.config, store.data);
    };
    ChartRenderer2.prototype.getData = function() {
      var store = this.props.store;
      return store.data;
    };
    ChartRenderer2.contextType = ScopedContext;
    ChartRenderer2 = __decorate([
      Renderer({
        type: "chart",
        storeType: ServiceStore.name
      }),
      __metadata("design:paramtypes", [Object, Object])
    ], ChartRenderer2);
    return ChartRenderer2;
  }(Chart)
);
export {
  Chart,
  ChartRenderer
};
//# sourceMappingURL=Chart-BYYC62ZU.js.map
