(function (global, factory) {
  if (typeof define === "function" && define.amd) {
    define("vxe-table-lang.uk-UA", ["exports"], factory);
  } else if (typeof exports !== "undefined") {
    factory(exports);
  } else {
    var mod = {
      exports: {}
    };
    factory(mod.exports);
    global.vxeTableLangUkUA = mod.exports;
  }
})(typeof globalThis !== "undefined" ? globalThis : typeof self !== "undefined" ? self : this, function (_exports) {
  "use strict";

  Object.defineProperty(_exports, "__esModule", {
    value: true
  });
  _exports.default = void 0;
  var _default = _exports.default = {
    vxe: {
      base: {
        pleaseInput: 'Будь ласка, введіть',
        pleaseSelect: 'Будь ласка, виберіть',
        comma: '，',
        fullStop: '。'
      },
      loading: {
        text: 'Завантаження ...'
      },
      error: {
        downErr: 'Завантажити не вдалося',
        errLargeData: "Коли кількість пов'язаних даних занадто велика, будь ласка, використовуйте {0}, інакше це може спричинити відставання",
        groupFixed: 'Якщо ви використовуєте згруповані заголовки, заморожений стовпчик повинен бути встановлений групою',
        groupMouseRange: 'Заголовок групування не може бути використаний одночасно з "{0}", і це може спричинити помилку',
        groupTag: 'Заголовок стовпців групування повинно використовувати "{0}" замість "{1}", що може спричинити помилки',
        scrollErrProp: 'Цей параметр "{0}" не підтримується після включення віртуальної прокрутки',
        errConflicts: 'Параметр "{0}" Конфлікти з "{1}"',
        notSupportProp: '"{1}" не підтримується, коли параметр "{0}" ввімкнено, він повинен бути "{2}", інакше помилка станеться',
        notConflictProp: 'При використанні "{0}", "{1}" слід встановити, інакше можуть бути функціональні конфлікти',
        unableInsert: 'Неможливо вставити у вказане місце, будь ласка, перевірте, чи правильні параметри',
        useErr: 'Під час встановлення модуля "{0}" сталася помилка. Порядок може бути неправильним. Залежний модуль потрібно встановити перед таблицею',
        barUnableLink: 'Панель інструментів не може асоціювати таблиці',
        expandContent: 'Слот для розширеної лінії повинен бути "вмістом", будь ласка, перевірте, чи це правильно',
        reqComp: 'Компонент "{0}" відсутній, будь ласка, перевірте, чи встановлено його правильно. https://vxeui.com/#/start/useglobal',
        reqModule: 'Відсутній модуль "{0}"',
        reqProp: 'Необхідний параметр "{0}" відсутній, що може спричинити помилку',
        emptyProp: 'Параметр "{0}" не дозволяється бути порожнім',
        errProp: 'Непідтримуваний параметр "{0}", можливо "{1}"',
        colRepet: 'Стовпчик. {0} = "{1}" повторюється, що може призвести до того, що деякі функції стануть непридатними',
        notFunc: 'Метод "{0}" не існує',
        errFunc: 'Параметр "{0}" - це не метод',
        notValidators: 'Глобальна перевірка "{0}" не існує',
        notFormats: 'Глобальне форматування "{0}" не існує',
        notCommands: 'Глобальної директиви "{0}" не існує',
        notSlot: 'Слот "{0}" не існує',
        noTree: '"{0}" не підтримується в структурі дерева',
        notProp: 'Непідтримуваний параметр "{0}"',
        checkProp: "Коли об'єм даних занадто великий, прапорець може бути заїканий. Рекомендується встановити параметр \"{0}\" для покращення швидкості візуалізації",
        coverProp: 'Параметр "{1}" "{0}" неодноразово визначається, що може спричинити помилку',
        uniField: 'Назва поля "{0}" неодноразово визначається, що може спричинити помилку',
        repeatKey: 'Повторіть первинний ключ {0} = "{1}", що може спричинити помилку',
        delFunc: 'Метод "{0}" застарілий, будь ласка, використовуйте "{1}"',
        delProp: 'Параметр "{0}" застарілий, будь ласка, використовуйте "{1}"',
        delEvent: 'Подія "{0}" застаріла, будь ласка, використовуйте "{1}"',
        removeProp: 'Параметр "{0}" застарілий і не рекомендується, що може спричинити помилку',
        errFormat: 'Глобальний відформатований вміст слід визначити за допомогою "vxetable.formats", і метод монтажу "formatter = {0}" більше не рекомендується.',
        notType: 'Непідтримуваний тип файлу "{0}"',
        notExp: 'Цей браузер не підтримує функцію імпорту/експорту',
        impFields: 'Імпорт не вдався. Будь ласка, перевірте, чи правильно назва поля та формат даних.',
        treeNotImp: 'Таблиці дерев не підтримують імпорт',
        treeCrossDrag: 'Тільки перетягніть перший рівень',
        treeDragChild: 'Батьки не можуть тягнути до своїх дітей',
        reqPlugin: '"{1}" не встановлюється на https://vxeui.com/other інд.',
        errMaxRow: 'Перевищення максимально підтримуваного обсягу даних {0} рядків це може спричинити помилку'
      },
      table: {
        emptyText: 'Ще немає даних',
        allTitle: 'Виберіть усі/Скасувати',
        seqTitle: 'Серійний номер',
        actionTitle: 'діяти',
        confirmFilter: 'фільтрувати',
        resetFilter: 'Скинути',
        allFilter: 'все',
        sortAsc: 'Порядок зростання: найнижчий до найвищого',
        sortDesc: 'Порядок зниження: найвищий до найнижчого',
        filter: 'Увімкніть фільтрування для вибраних стовпців',
        impSuccess: 'Успішно імпортовано {0} записи',
        expLoading: 'Експорт',
        expSuccess: 'Експорт успішно',
        expError: 'Експорт не вдався',
        expFilename: 'Export_ {0}',
        expOriginFilename: 'Export_source_ {0}',
        customTitle: 'Налаштування стовпця',
        customAll: 'все',
        customConfirm: 'підтверджувати',
        customClose: 'закриття',
        customCancel: 'Скасувати',
        customRestore: 'Відновити за замовчуванням',
        maxFixedCol: 'Максимальна кількість заморожених стовпців не може перевищувати {0}',
        dragTip: 'Перемістити: {0}',
        resizeColTip: 'Ширина: {0} пікселів',
        resizeRowTip: 'Висота: {0} пікселів',
        rowGroupContentTotal: '{0} ({1})'
      },
      grid: {
        selectOneRecord: 'Виберіть принаймні один запис!',
        deleteSelectRecord: 'Ви впевнені, що хочете видалити вибраний запис?',
        removeSelectRecord: 'Ви впевнені, що хочете видалити вибраний запис?',
        dataUnchanged: 'Дані не змінилися!',
        delSuccess: 'Вибраний запис був успішно видалений!',
        saveSuccess: 'Зберегти успішно!',
        operError: 'Сталася помилка, і операція не вдалася!'
      },
      select: {
        search: 'обшук',
        loadingText: 'навантаження',
        emptyText: 'Ще немає даних'
      },
      pager: {
        goto: 'Йти',
        gotoTitle: 'Кількість сторінок',
        pagesize: '{0} елементи/сторінка',
        total: 'Всього {0} записи',
        pageClassifier: 'Сторінка',
        homePage: 'головна сторінка',
        homePageTitle: 'головна сторінка',
        prevPage: 'Попередня сторінка',
        prevPageTitle: 'Попередня сторінка',
        nextPage: 'Наступна сторінка',
        nextPageTitle: 'Наступна сторінка',
        prevJump: 'Сторінка',
        prevJumpTitle: 'Сторінка',
        nextJump: 'Стрибайте на сторінку',
        nextJumpTitle: 'Стрибайте на сторінку',
        endPage: 'Остання сторінка',
        endPageTitle: 'Остання сторінка'
      },
      alert: {
        title: 'Підказки системи'
      },
      button: {
        confirm: 'підтверджувати',
        cancel: 'Скасувати',
        clear: 'Чіткий'
      },
      filter: {
        search: 'обшук'
      },
      custom: {
        cstmTitle: 'Налаштування стовпця',
        cstmRestore: 'Відновити за замовчуванням',
        cstmCancel: 'Скасувати',
        cstmConfirm: 'Упевнений',
        cstmConfirmRestore: 'Будь ласка, підтвердьте, чи відновиться він до конфігурації стовпця за замовчуванням?',
        cstmDragTarget: 'Перемістити: {0}',
        setting: {
          colSort: 'Сортувати',
          sortHelpTip: 'Клацніть та перетягніть значок, щоб відрегулювати стовпці',
          colTitle: 'Заголовок стовпців',
          colResizable: 'Ширина стовпця (пікселі)',
          colVisible: 'Чи відображати',
          colFixed: 'Заморожування стовпчика',
          colFixedMax: 'Заморожування стовпців (до {0} стовпців)',
          fixedLeft: 'Лівий бік',
          fixedUnset: 'Не встановити',
          fixedRight: 'Правий бік'
        }
      },
      import: {
        modes: {
          covering: 'Метод перезапис (безпосередньо переписати дані таблиці)',
          insert: 'Додайте внизу (додайте нові дані внизу таблиці)',
          insertTop: 'Додайте вгорі (додайте нові дані вгорі таблиці)',
          insertBottom: 'Додайте внизу (додайте нові дані внизу таблиці)'
        },
        impTitle: 'Імпортувати дані',
        impFile: 'Назва файлу',
        impSelect: 'Виберіть Файл',
        impType: 'Тип файлу',
        impOpts: 'Налаштування параметрів',
        impMode: 'Режим імпорту',
        impConfirm: 'Імпорт',
        impCancel: 'Скасувати'
      },
      export: {
        types: {
          csv: 'CSV (Comma розділений) (*. CSV)',
          html: 'Веб -сторінка (*.html)',
          xml: 'Дані XML (*.xml)',
          txt: 'Текстовий файл (розділений вкладкою) (*. Txt)',
          xls: 'Excel 97-2003 Робоча книга (*.xls)',
          xlsx: 'Робоча книга Excel (*.xlsx)',
          pdf: 'PDF (*.pdf)'
        },
        modes: {
          empty: 'Порожні дані',
          current: 'Поточні дані (дані на поточній сторінці)',
          selected: 'Вибрані дані (дані, вибрані на поточній сторінці)',
          all: 'Повні дані (включаючи всі петлі дані)'
        },
        printTitle: 'Друкувати дані',
        expTitle: 'Дані експорту',
        expName: 'Назва файлу',
        expNamePlaceholder: "Введіть ім'я файлу",
        expSheetName: 'титул',
        expSheetNamePlaceholder: 'Будь ласка, введіть назву',
        expType: 'Зберегти тип',
        expMode: 'Виберіть дані',
        expCurrentColumn: 'Всі поля',
        expColumn: 'Виберіть поле',
        expOpts: 'Налаштування параметрів',
        expOptHeader: 'Заголовок',
        expHeaderTitle: 'Необхідний заголовок столу',
        expOptFooter: 'Кінець столу',
        expFooterTitle: 'Чи потрібен кінець таблиці?',
        expOptColgroup: 'Заголовок групування',
        expOptTitle: 'Заголовок стовпців',
        expTitleTitle: 'Будь то назва стовпця, інакше він відображатиметься як назва поля стовпця',
        expColgroupTitle: 'Якщо вони присутні, підтримується заголовок із структурою групування',
        expOptMerge: "об'єднання",
        expMergeTitle: "Якщо вони присутні, підтримуються клітини з об'єднаними структурами",
        expOptAllExpand: 'Розширити дерево',
        expAllExpandTitle: 'Якщо він існує, підтримується розширення всіх даних за допомогою ієрархічних структур',
        expOptUseStyle: 'стиль',
        expUseStyleTitle: 'Якщо вони присутні, підтримуються клітини зі стилем',
        expOptOriginal: 'Джерело даних',
        expOriginalTitle: 'Якщо це джерело даних, підтримується імпорт у таблиці',
        expPrint: 'Друкувати',
        expConfirm: 'Експорт',
        expCancel: 'Скасувати'
      },
      modal: {
        errTitle: 'Повідомлення про помилку',
        zoomMin: 'Мінімізувати',
        zoomIn: 'максимізувати',
        zoomOut: 'зменшення',
        close: 'закриття',
        miniMaxSize: 'Кількість мінімізованих вікон не може перевищувати {0}',
        footPropErr: 'Шоу-футер використовується лише для ввімкнення хвоста столу, і його потрібно використовувати з кнопкою шоу-конфігурації | Шоу-кнопка | проріз'
      },
      drawer: {
        close: 'закриття'
      },
      form: {
        folding: 'Закривати',
        unfolding: 'Розширити'
      },
      toolbar: {
        import: 'Імпорт',
        export: 'Експорт',
        print: 'Друкувати',
        refresh: 'оновлювати',
        zoomIn: 'повний екран',
        zoomOut: 'зменшення',
        custom: 'Налаштування стовпця',
        customAll: 'все',
        customConfirm: 'підтверджувати',
        customRestore: 'Скинути',
        fixedLeft: 'Заморожуйте ліворуч',
        fixedRight: 'Заморожуйте право',
        cancelFixed: 'Розморожувати'
      },
      datePicker: {
        yearTitle: '{0} років'
      },
      dateRangePicker: {
        pleaseRange: '请选择开始日期与结束日期'
      },
      input: {
        date: {
          m1: 'Січень',
          m2: 'Лютий',
          m3: 'Марш',
          m4: 'Квітень',
          m5: 'Травень',
          m6: 'Червень',
          m7: 'Липень',
          m8: 'Серпень',
          m9: 'Вересень',
          m10: 'Жовтень',
          m11: 'Листопад',
          m12: 'Грудень',
          quarterLabel: '{0} років',
          monthLabel: '{0} років',
          dayLabel: '{0} year {1}',
          labelFormat: {
            date: 'yyyy-MM-dd',
            time: 'HH:mm:ss',
            datetime: 'yyyy-MM-dd HH:mm:ss',
            week: 'Week WW of year yyyy',
            month: 'yyyy-MM',
            quarter: 'quarter q of year yyyy',
            year: 'yyyy'
          },
          weeks: {
            w: '',
            w0: 'Сонце',
            w1: 'Мн',
            w2: 'Зміст',
            w3: 'Одружуватися',
            w4: 'Чт',
            w5: 'Пт',
            w6: 'Сидіти'
          },
          months: {
            m0: 'Січень',
            m1: 'Лютий',
            m2: 'Марш',
            m3: 'Квітень',
            m4: 'Травень',
            m5: 'Червень',
            m6: 'Липень',
            m7: 'Серпень',
            m8: 'Вересень',
            m9: 'Жовтень',
            m10: 'Листопад',
            m11: 'Грудень'
          },
          quarters: {
            q1: 'Перша чверть',
            q2: 'Другий чверть',
            q3: 'Третя чверть',
            q4: 'Четверта чверть'
          }
        }
      },
      numberInput: {
        currencySymbol: '$'
      },
      imagePreview: {
        popupTitle: 'Попередній перегляд',
        operBtn: {
          zoomOut: 'Стискатися',
          zoomIn: 'збільшувати',
          pctFull: 'Масштабування однаково',
          pct11: 'Показати оригінальний розмір',
          rotateLeft: 'Обертати ліворуч',
          rotateRight: 'Обертайте праворуч',
          print: 'Клацніть, щоб надрукувати зображення',
          download: 'Клацніть, щоб завантажити зображення'
        }
      },
      upload: {
        fileBtnText: 'Клацніть або перетягніть на завантаження',
        imgBtnText: 'Клацніть або перетягніть на завантаження',
        dragPlaceholder: 'Будь ласка, перетягніть файл у цю область, щоб завантажити',
        imgSizeHint: 'Листівка {0}',
        imgCountHint: 'Максимум {0} картинки',
        fileTypeHint: 'Підтримка {0} типів файлів',
        fileSizeHint: 'Один розмір файлу не перевищує {0}',
        fileCountHint: 'До {0} файлів можна завантажити',
        uploadTypeErr: 'Тип файлу невідповідність!',
        overCountErr: 'Лише {0} файли можна вибрати щонайбільше!',
        overCountExtraErr: 'Максимальна кількість {0} була перевищена, а надлишки {1} файлів будуть ігноровані!',
        overSizeErr: 'Максимальний розмір файлу не може перевищувати {0}!',
        reUpload: 'Переробляти',
        uploadProgress: 'Завантаження {0}%',
        uploadErr: 'Завантаження не вдалося',
        uploadSuccess: 'Завантажте успішно',
        moreBtnText: 'Більше ({0})',
        viewItemTitle: 'Клацніть, щоб переглянути',
        morePopup: {
          readTitle: 'Переглянути список',
          imageTitle: 'Завантажте фотографії',
          fileTitle: 'Завантажити файл'
        }
      },
      empty: {
        defText: 'Ще немає даних'
      },
      colorPicker: {
        clear: 'Чіткий',
        confirm: 'підтверджувати',
        copySuccess: 'Копіюється в буфер обміну: {0}'
      },
      formDesign: {
        formName: 'Назва',
        defFormTitle: 'Неназвана форма',
        widgetPropTab: 'Контрольні властивості',
        widgetFormTab: 'Утворюють властивості',
        error: {
          wdFormUni: 'Цей тип управління дозволяється додавати лише один у формі',
          wdSubUni: 'Цей тип управління дозволяється додати лише один у підкладці'
        },
        styleSetting: {
          btn: 'Налаштування стилю',
          title: 'Налаштування стилю форми',
          layoutTitle: 'Контрольний макет',
          verticalLayout: 'Верхній і нижній макет',
          horizontalLayout: 'Горизонтальний макет',
          styleTitle: 'Стиль титулу',
          boldTitle: 'Назва сміливий',
          fontBold: 'Сміливий',
          fontNormal: 'звичайний',
          colonTitle: 'Показувати Колон',
          colonVisible: 'показувати',
          colonHidden: 'сховати',
          alignTitle: 'Вирівнювання',
          widthTitle: 'Ширина титулу',
          alignLeft: 'Зліва',
          alignRight: 'Праворуч',
          unitPx: 'Пікселі',
          unitPct: 'відсоток'
        },
        widget: {
          group: {
            base: 'Основні елементи управління',
            layout: 'Управління макета',
            system: 'Системне управління',
            module: 'Управління модулем',
            chart: 'Управління діаграмами',
            advanced: 'Вдосконалені елементи управління'
          },
          copyTitle: 'Copy_ {0}',
          component: {
            input: 'Поле введення',
            textarea: 'Текстове поле',
            select: 'Витягніть, щоб вибрати',
            row: 'Один ряд та кілька стовпців',
            title: 'титул',
            text: 'текст',
            subtable: 'Підрозділ',
            VxeSwitch: 'чи',
            VxeInput: 'Поле введення',
            VxeNumberInput: 'число',
            VxeDatePicker: 'дата',
            VxeTextarea: 'Текстове поле',
            VxeSelect: 'Витягніть, щоб вибрати',
            VxeTreeSelect: 'Вибір дерев',
            VxeRadioGroup: 'Перемикач',
            VxeCheckboxGroup: 'Прапорець',
            VxeUploadFile: 'документ',
            VxeUploadImage: 'малюнок',
            VxeRate: 'оцінка',
            VxeSlider: 'повзунок'
          }
        },
        widgetProp: {
          name: 'Назва управління',
          placeholder: 'Підказувати',
          required: 'Необхідна перевірка',
          multiple: 'Дозволено кілька варіантів',
          displaySetting: {
            name: 'Налаштування відображення',
            pc: 'ПК',
            mobile: 'Мобільний',
            visible: 'показувати',
            hidden: 'сховати'
          },
          dataSource: {
            name: 'Джерело даних',
            defValue: 'Варіант {0}',
            addOption: 'Додати параметри',
            batchEditOption: 'Редагування партії',
            batchEditTip: 'Кожен рядок відповідає опції, яка підтримує пряму копію та вставку з таблиць, Excel та WPS.',
            batchEditSubTip: 'Кожен рядок відповідає опції. Якщо це група, дочірні елементи можуть починатися з простору або клавіші вкладки, і вона підтримує пряму копію та вставку з таблиць, Excel та WPS.',
            buildOption: 'Варіанти побудови'
          },
          rowProp: {
            colSize: 'Кількість стовпців',
            col2: 'Два стовпці',
            col3: 'Три стовпці',
            col4: 'Чотири стовпці',
            col6: 'Шість стовпців',
            layout: 'макет'
          },
          textProp: {
            name: 'зміст',
            alignTitle: 'Вирівнювання',
            alignLeft: 'Зліва',
            alignCenter: 'Центр',
            alignRight: 'Праворуч',
            colorTitle: 'Шрифт',
            sizeTitle: 'Розмір шрифту',
            boldTitle: 'Сміливий шрифт',
            fontNormal: 'звичайний',
            fontBold: 'Сміливий'
          },
          subtableProp: {
            seqTitle: 'Серійний номер',
            showSeq: 'Покажіть серійний номер',
            showCheckbox: 'Дозволено кілька варіантів',
            errSubDrag: 'Підкладка не підтримує цей контроль, будь ласка, використовуйте інші елементи управління',
            colPlace: 'Перетягніть керування в'
          },
          uploadProp: {
            limitFileCount: 'Обмеження кількості файлу',
            limitFileSize: 'Обмеження розміру файлу',
            multiFile: 'Дозволити завантажувати кілька файлів',
            limitImgCount: 'Обмежте кількість зображень',
            limitImgSize: 'Обмеження розміру зображення',
            multiImg: 'Дозвольте завантажувати кілька фотографій'
          }
        }
      },
      listDesign: {
        fieldSettingTab: 'Налаштування поля',
        listSettingTab: 'Налаштування параметрів',
        searchTitle: 'Критерії запитів',
        listTitle: 'Поле списку',
        searchField: 'Поля запиту',
        listField: 'Поле списку',
        activeBtn: {
          ActionButtonUpdate: 'редагувати',
          ActionButtonDelete: 'видаляти'
        },
        search: {
          addBtn: 'редагувати',
          emptyText: 'Умови запиту не налаштовані',
          editPopupTitle: 'Редагувати поля запиту'
        },
        searchPopup: {
          colTitle: 'титул',
          saveBtn: 'заощадити'
        }
      },
      text: {
        copySuccess: 'Копіюється в буфер обміну',
        copyError: 'Поточне середовище не підтримує цю операцію'
      },
      countdown: {
        formats: {
          yyyy: 'Рік',
          MM: 'місяць',
          dd: 'небо',
          HH: 'година',
          mm: 'точка',
          ss: 'Другий'
        }
      },
      plugins: {
        extendCellArea: {
          area: {
            mergeErr: "Ця операція не може бути виконана на об'єднаних комірок",
            multiErr: 'Ця операція не може бути виконана на декількох областях вибору',
            selectErr: 'Не в змозі працювати на клітинах у визначеному діапазоні',
            extendErr: "Якщо розширений діапазон містить об'єднані клітини, всі об'єднані клітини повинні бути однаковими розмірами",
            pasteMultiErr: 'Не в змозі вставити, скопійовані та вставлені ділянки повинні мати однакового розміру для виконання цієї операції',
            cpInvalidErr: 'Операцію неможливо виконати. У вибраному вами стовпці є заборонені стовпці ({0}).'
          },
          fnr: {
            title: 'Знайти та замінити',
            findLabel: 'Знаходити',
            replaceLabel: 'замінити',
            findTitle: 'Знайти що:',
            replaceTitle: 'Замініть на:',
            tabs: {
              find: 'Знаходити',
              replace: 'замінити'
            },
            filter: {
              re: 'Регулярні вирази',
              whole: 'Повна відповідність слів',
              sensitive: 'Чутливий'
            },
            btns: {
              findNext: 'Знайти далі',
              findAll: 'Знайти все',
              replace: 'замінити',
              replaceAll: 'Замініть усі',
              cancel: 'Скасувати'
            },
            header: {
              seq: '#',
              cell: 'Клітина',
              value: 'цінність'
            },
            body: {
              row: 'Рядок: {0}',
              col: 'Стовпчик: {0}'
            },
            empty: '(Нульове значення)',
            reError: 'Недійсний регулярний вираз',
            recordCount: '{0} клітини знайдені',
            notCell: 'Неможливо знайти відповідну клітину',
            replaceSuccess: 'Успішно замінив {0} комірок'
          }
        },
        extendPivotTable: {
          aggregation: {
            grouping: '分组',
            values: '值'
          }
        },
        filterComplexInput: {
          menus: {
            fixedColumn: 'Заморожування стовпчика',
            fixedGroup: 'Група заморожування',
            cancelFixed: 'Розморожувати',
            fixedLeft: 'Заморожуйте ліворуч',
            fixedRight: 'Заморожуйте право'
          },
          cases: {
            equal: 'рівний',
            gt: 'Більше',
            lt: 'Менше',
            begin: 'Початок є',
            endin: 'Кінець є',
            include: 'Включати',
            isSensitive: 'Чутливий'
          }
        },
        filterCombination: {
          menus: {
            sort: 'Сортувати',
            clearSort: 'Чіткий сорт',
            sortAsc: 'Здійснення',
            sortDesc: 'Здійснення порядок',
            fixedColumn: 'Заморожування стовпчика',
            fixedGroup: 'Група заморожування',
            cancelFixed: 'Розморожувати',
            fixedLeft: 'Заморожуйте ліворуч',
            fixedRight: 'Заморожуйте право',
            clearFilter: 'Прозорий фільтр',
            textOption: 'Текстовий фільтр',
            numberOption: 'Числовий фільтр'
          },
          popup: {
            title: 'Спеціальні методи фільтрації',
            currColumnTitle: 'Поточний стовпець:',
            and: 'і',
            or: 'або',
            describeHtml: 'Доступно? Являє собою один символ <br/> використання * являє собою будь -які кілька символів'
          },
          cases: {
            equal: 'рівний',
            unequal: 'Не дорівнює',
            gt: 'Більше',
            ge: 'Більше або дорівнює',
            lt: 'Менше',
            le: 'Менше або дорівнює',
            begin: 'Початок є',
            notbegin: 'Це не на початку',
            endin: 'Кінець є',
            notendin: 'Кінець не є',
            include: 'Включати',
            exclude: 'Не входить',
            between: 'Між',
            custom: 'Спеціальний фільтр',
            insensitive: 'Нечутливий',
            isSensitive: 'Чутливий'
          },
          empty: '(порожній)',
          notData: 'Немає відповідності'
        }
      },
      pro: {
        area: {
          mergeErr: "Ця операція не може бути виконана на об'єднаних комірок",
          multiErr: 'Ця операція не може бути виконана на декількох областях вибору',
          extendErr: "Якщо розширений діапазон містить об'єднані клітини, всі об'єднані клітини повинні бути однаковими розмірами",
          pasteMultiErr: 'Не в змозі вставити, скопійовані та вставлені ділянки повинні мати однакового розміру для виконання цієї операції'
        },
        fnr: {
          title: 'Знайти та замінити',
          findLabel: 'Знаходити',
          replaceLabel: 'замінити',
          findTitle: 'Знайдіть вміст:',
          replaceTitle: 'Замініть на:',
          tabs: {
            find: 'Знаходити',
            replace: 'замінити'
          },
          filter: {
            re: 'Регулярні вирази',
            whole: 'Повна відповідність слів',
            sensitive: 'Чутливий'
          },
          btns: {
            findNext: 'Знайти далі',
            findAll: 'Знайти все',
            replace: 'замінити',
            replaceAll: 'Замініть усі',
            cancel: 'Скасувати'
          },
          header: {
            seq: '#',
            cell: 'Клітина',
            value: 'цінність'
          },
          empty: '(Нульове значення)',
          reError: 'Недійсний регулярний вираз',
          recordCount: '{0} клітини знайдені',
          notCell: 'Не знайдено відповідної клітини',
          replaceSuccess: 'Успішно замінив {0} комірок'
        }
      },
      renderer: {
        search: 'обшук',
        cases: {
          equal: 'рівний',
          unequal: 'Не дорівнює',
          gt: 'Більше',
          ge: 'Більше або дорівнює',
          lt: 'Менше',
          le: 'Менше або дорівнює',
          begin: 'Початок є',
          notbegin: 'Це не на початку',
          endin: 'Кінець є',
          notendin: 'Кінець не є',
          include: 'Включати',
          exclude: 'Не входить',
          between: 'Між',
          custom: 'Спеціальний фільтр',
          insensitive: 'Нечутливий',
          isSensitive: 'Чутливий'
        },
        combination: {
          menus: {
            sort: 'Сортувати',
            clearSort: 'Чіткий сорт',
            sortAsc: 'Здійснення',
            sortDesc: 'Здійснення порядок',
            fixedColumn: 'Заморожування стовпчика',
            fixedGroup: 'Група заморожування',
            cancelFixed: 'Розморожувати',
            fixedLeft: 'Заморожуйте ліворуч',
            fixedRight: 'Заморожуйте право',
            clearFilter: 'Прозорий фільтр',
            textOption: 'Текстове фільтрація',
            numberOption: 'Чисельна фільтрація'
          },
          popup: {
            title: 'Спеціальні методи фільтрації',
            currColumnTitle: 'Поточний стовпець:',
            and: 'і',
            or: 'або',
            describeHtml: 'Доступно? Являє собою один символ <br/> використання * являє собою будь -які кілька символів'
          },
          empty: '(порожній)',
          notData: 'Немає відповідності'
        }
      }
    }
  };
});