{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/dockerfile/dockerfile.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" }\n    ],\n    surroundingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" }\n    ]\n};\nexport var language = {\n    defaultToken: '',\n    tokenPostfix: '.dockerfile',\n    variable: /\\${?[\\w]+}?/,\n    tokenizer: {\n        root: [\n            { include: '@whitespace' },\n            { include: '@comment' },\n            [/(ONBUILD)(\\s+)/, ['keyword', '']],\n            [/(ENV)(\\s+)([\\w]+)/, ['keyword', '', { token: 'variable', next: '@arguments' }]],\n            [\n                /(FROM|MAINTAINER|RUN|EXPOSE|ENV|ADD|ARG|VOLUME|LABEL|USER|WORKDIR|COPY|CMD|STOPSIGNAL|SHELL|HEALTHCHECK|ENTRYPOINT)/,\n                { token: 'keyword', next: '@arguments' }\n            ]\n        ],\n        arguments: [\n            { include: '@whitespace' },\n            { include: '@strings' },\n            [\n                /(@variable)/,\n                {\n                    cases: {\n                        '@eos': { token: 'variable', next: '@popall' },\n                        '@default': 'variable'\n                    }\n                }\n            ],\n            [\n                /\\\\/,\n                {\n                    cases: {\n                        '@eos': '',\n                        '@default': ''\n                    }\n                }\n            ],\n            [\n                /./,\n                {\n                    cases: {\n                        '@eos': { token: '', next: '@popall' },\n                        '@default': ''\n                    }\n                }\n            ]\n        ],\n        // Deal with white space, including comments\n        whitespace: [\n            [\n                /\\s+/,\n                {\n                    cases: {\n                        '@eos': { token: '', next: '@popall' },\n                        '@default': ''\n                    }\n                }\n            ]\n        ],\n        comment: [[/(^#.*$)/, 'comment', '@popall']],\n        // Recognize strings, including those broken across lines with \\ (but not without)\n        strings: [\n            [/\\\\'$/, '', '@popall'],\n            [/\\\\'/, ''],\n            [/'$/, 'string', '@popall'],\n            [/'/, 'string', '@stringBody'],\n            [/\"$/, 'string', '@popall'],\n            [/\"/, 'string', '@dblStringBody']\n        ],\n        stringBody: [\n            [\n                /[^\\\\\\$']/,\n                {\n                    cases: {\n                        '@eos': { token: 'string', next: '@popall' },\n                        '@default': 'string'\n                    }\n                }\n            ],\n            [/\\\\./, 'string.escape'],\n            [/'$/, 'string', '@popall'],\n            [/'/, 'string', '@pop'],\n            [/(@variable)/, 'variable'],\n            [/\\\\$/, 'string'],\n            [/$/, 'string', '@popall']\n        ],\n        dblStringBody: [\n            [\n                /[^\\\\\\$\"]/,\n                {\n                    cases: {\n                        '@eos': { token: 'string', next: '@popall' },\n                        '@default': 'string'\n                    }\n                }\n            ],\n            [/\\\\./, 'string.escape'],\n            [/\"$/, 'string', '@popall'],\n            [/\"/, 'string', '@pop'],\n            [/(@variable)/, 'variable'],\n            [/\\\\$/, 'string'],\n            [/$/, 'string', '@popall']\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,EACV,WAAW;AAAA,IACP,MAAM;AAAA,MACF,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA,MACtB,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC;AAAA,MAClC,CAAC,qBAAqB,CAAC,WAAW,IAAI,EAAE,OAAO,YAAY,MAAM,aAAa,CAAC,CAAC;AAAA,MAChF;AAAA,QACI;AAAA,QACA,EAAE,OAAO,WAAW,MAAM,aAAa;AAAA,MAC3C;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,MACP,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA,MACtB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ,EAAE,OAAO,YAAY,MAAM,UAAU;AAAA,YAC7C,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ;AAAA,YACR,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ,EAAE,OAAO,IAAI,MAAM,UAAU;AAAA,YACrC,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA,IAEA,YAAY;AAAA,MACR;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ,EAAE,OAAO,IAAI,MAAM,UAAU;AAAA,YACrC,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,SAAS,CAAC,CAAC,WAAW,WAAW,SAAS,CAAC;AAAA;AAAA,IAE3C,SAAS;AAAA,MACL,CAAC,QAAQ,IAAI,SAAS;AAAA,MACtB,CAAC,OAAO,EAAE;AAAA,MACV,CAAC,MAAM,UAAU,SAAS;AAAA,MAC1B,CAAC,KAAK,UAAU,aAAa;AAAA,MAC7B,CAAC,MAAM,UAAU,SAAS;AAAA,MAC1B,CAAC,KAAK,UAAU,gBAAgB;AAAA,IACpC;AAAA,IACA,YAAY;AAAA,MACR;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ,EAAE,OAAO,UAAU,MAAM,UAAU;AAAA,YAC3C,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,CAAC,OAAO,eAAe;AAAA,MACvB,CAAC,MAAM,UAAU,SAAS;AAAA,MAC1B,CAAC,KAAK,UAAU,MAAM;AAAA,MACtB,CAAC,eAAe,UAAU;AAAA,MAC1B,CAAC,OAAO,QAAQ;AAAA,MAChB,CAAC,KAAK,UAAU,SAAS;AAAA,IAC7B;AAAA,IACA,eAAe;AAAA,MACX;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ,EAAE,OAAO,UAAU,MAAM,UAAU;AAAA,YAC3C,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,CAAC,OAAO,eAAe;AAAA,MACvB,CAAC,MAAM,UAAU,SAAS;AAAA,MAC1B,CAAC,KAAK,UAAU,MAAM;AAAA,MACtB,CAAC,eAAe,UAAU;AAAA,MAC1B,CAAC,OAAO,QAAQ;AAAA,MAChB,CAAC,KAAK,UAAU,SAAS;AAAA,IAC7B;AAAA,EACJ;AACJ;", "names": []}