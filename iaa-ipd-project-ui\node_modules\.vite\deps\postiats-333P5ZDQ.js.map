{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/postiats/postiats.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) <PERSON><PERSON><PERSON>. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *\n *  Based on the ATS/Postiats lexer by Hongwei Xi.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    comments: {\n        lineComment: '//',\n        blockComment: ['(*', '*)']\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')'],\n        ['<', '>']\n    ],\n    autoClosingPairs: [\n        { open: '\"', close: '\"', notIn: ['string', 'comment'] },\n        { open: '{', close: '}', notIn: ['string', 'comment'] },\n        { open: '[', close: ']', notIn: ['string', 'comment'] },\n        { open: '(', close: ')', notIn: ['string', 'comment'] }\n    ]\n};\nexport var language = {\n    tokenPostfix: '.pats',\n    // TODO: staload and dynload are followed by a special kind of string literals\n    // with {$IDENTIFER} variables, and it also may make sense to highlight\n    // the punctuation (. and / and \\) differently.\n    // Set defaultToken to invalid to see what you do not tokenize yet\n    defaultToken: 'invalid',\n    // keyword reference: https://github.com/githwxi/ATS-Postiats/blob/master/src/pats_lexing_token.dats\n    keywords: [\n        //\n        'abstype',\n        'abst0ype',\n        'absprop',\n        'absview',\n        'absvtype',\n        'absviewtype',\n        'absvt0ype',\n        'absviewt0ype',\n        //\n        'as',\n        //\n        'and',\n        //\n        'assume',\n        //\n        'begin',\n        //\n        /*\n                \"case\", // CASE\n        */\n        //\n        'classdec',\n        //\n        'datasort',\n        //\n        'datatype',\n        'dataprop',\n        'dataview',\n        'datavtype',\n        'dataviewtype',\n        //\n        'do',\n        //\n        'end',\n        //\n        'extern',\n        'extype',\n        'extvar',\n        //\n        'exception',\n        //\n        'fn',\n        'fnx',\n        'fun',\n        //\n        'prfn',\n        'prfun',\n        //\n        'praxi',\n        'castfn',\n        //\n        'if',\n        'then',\n        'else',\n        //\n        'ifcase',\n        //\n        'in',\n        //\n        'infix',\n        'infixl',\n        'infixr',\n        'prefix',\n        'postfix',\n        //\n        'implmnt',\n        'implement',\n        //\n        'primplmnt',\n        'primplement',\n        //\n        'import',\n        //\n        /*\n                \"lam\", // LAM\n                \"llam\", // LLAM\n                \"fix\", // FIX\n        */\n        //\n        'let',\n        //\n        'local',\n        //\n        'macdef',\n        'macrodef',\n        //\n        'nonfix',\n        //\n        'symelim',\n        'symintr',\n        'overload',\n        //\n        'of',\n        'op',\n        //\n        'rec',\n        //\n        'sif',\n        'scase',\n        //\n        'sortdef',\n        /*\n        // HX: [sta] is now deprecated\n        */\n        'sta',\n        'stacst',\n        'stadef',\n        'static',\n        /*\n                \"stavar\", // T_STAVAR\n        */\n        //\n        'staload',\n        'dynload',\n        //\n        'try',\n        //\n        'tkindef',\n        //\n        /*\n                \"type\", // TYPE\n        */\n        'typedef',\n        'propdef',\n        'viewdef',\n        'vtypedef',\n        'viewtypedef',\n        //\n        /*\n                \"val\", // VAL\n        */\n        'prval',\n        //\n        'var',\n        'prvar',\n        //\n        'when',\n        'where',\n        //\n        /*\n                \"for\", // T_FOR\n                \"while\", // T_WHILE\n        */\n        //\n        'with',\n        //\n        'withtype',\n        'withprop',\n        'withview',\n        'withvtype',\n        'withviewtype' // WITHVIEWTYPE\n        //\n    ],\n    keywords_dlr: [\n        '$delay',\n        '$ldelay',\n        //\n        '$arrpsz',\n        '$arrptrsize',\n        //\n        '$d2ctype',\n        //\n        '$effmask',\n        '$effmask_ntm',\n        '$effmask_exn',\n        '$effmask_ref',\n        '$effmask_wrt',\n        '$effmask_all',\n        //\n        '$extern',\n        '$extkind',\n        '$extype',\n        '$extype_struct',\n        //\n        '$extval',\n        '$extfcall',\n        '$extmcall',\n        //\n        '$literal',\n        //\n        '$myfilename',\n        '$mylocation',\n        '$myfunction',\n        //\n        '$lst',\n        '$lst_t',\n        '$lst_vt',\n        '$list',\n        '$list_t',\n        '$list_vt',\n        //\n        '$rec',\n        '$rec_t',\n        '$rec_vt',\n        '$record',\n        '$record_t',\n        '$record_vt',\n        //\n        '$tup',\n        '$tup_t',\n        '$tup_vt',\n        '$tuple',\n        '$tuple_t',\n        '$tuple_vt',\n        //\n        '$break',\n        '$continue',\n        //\n        '$raise',\n        //\n        '$showtype',\n        //\n        '$vcopyenv_v',\n        '$vcopyenv_vt',\n        //\n        '$tempenver',\n        //\n        '$solver_assert',\n        '$solver_verify' // T_DLRSOLVERIFY\n    ],\n    keywords_srp: [\n        //\n        '#if',\n        '#ifdef',\n        '#ifndef',\n        //\n        '#then',\n        //\n        '#elif',\n        '#elifdef',\n        '#elifndef',\n        //\n        '#else',\n        '#endif',\n        //\n        '#error',\n        //\n        '#prerr',\n        '#print',\n        //\n        '#assert',\n        //\n        '#undef',\n        '#define',\n        //\n        '#include',\n        '#require',\n        //\n        '#pragma',\n        '#codegen2',\n        '#codegen3' // T_SRPCODEGEN3 // for level-3 codegen\n        //\n        // HX: end of special tokens\n        //\n    ],\n    irregular_keyword_list: [\n        'val+',\n        'val-',\n        'val',\n        'case+',\n        'case-',\n        'case',\n        'addr@',\n        'addr',\n        'fold@',\n        'free@',\n        'fix@',\n        'fix',\n        'lam@',\n        'lam',\n        'llam@',\n        'llam',\n        'viewt@ype+',\n        'viewt@ype-',\n        'viewt@ype',\n        'viewtype+',\n        'viewtype-',\n        'viewtype',\n        'view+',\n        'view-',\n        'view@',\n        'view',\n        'type+',\n        'type-',\n        'type',\n        'vtype+',\n        'vtype-',\n        'vtype',\n        'vt@ype+',\n        'vt@ype-',\n        'vt@ype',\n        'viewt@ype+',\n        'viewt@ype-',\n        'viewt@ype',\n        'viewtype+',\n        'viewtype-',\n        'viewtype',\n        'prop+',\n        'prop-',\n        'prop',\n        'type+',\n        'type-',\n        'type',\n        't@ype',\n        't@ype+',\n        't@ype-',\n        'abst@ype',\n        'abstype',\n        'absviewt@ype',\n        'absvt@ype',\n        'for*',\n        'for',\n        'while*',\n        'while'\n    ],\n    keywords_types: [\n        'bool',\n        'double',\n        'byte',\n        'int',\n        'short',\n        'char',\n        'void',\n        'unit',\n        'long',\n        'float',\n        'string',\n        'strptr'\n    ],\n    // TODO: reference for this?\n    keywords_effects: [\n        '0',\n        'fun',\n        'clo',\n        'prf',\n        'funclo',\n        'cloptr',\n        'cloref',\n        'ref',\n        'ntm',\n        '1' // all effects\n    ],\n    operators: [\n        '@',\n        '!',\n        '|',\n        '`',\n        ':',\n        '$',\n        '.',\n        '=',\n        '#',\n        '~',\n        //\n        '..',\n        '...',\n        //\n        '=>',\n        // \"=<\", // T_EQLT\n        '=<>',\n        '=/=>',\n        '=>>',\n        '=/=>>',\n        //\n        '<',\n        '>',\n        //\n        '><',\n        //\n        '.<',\n        '>.',\n        //\n        '.<>.',\n        //\n        '->',\n        //\"-<\", // T_MINUSLT\n        '-<>' // T_MINUSLTGT\n        //\n        /*\n                \":<\", // T_COLONLT\n        */\n    ],\n    brackets: [\n        { open: ',(', close: ')', token: 'delimiter.parenthesis' },\n        { open: '`(', close: ')', token: 'delimiter.parenthesis' },\n        { open: '%(', close: ')', token: 'delimiter.parenthesis' },\n        { open: \"'(\", close: ')', token: 'delimiter.parenthesis' },\n        { open: \"'{\", close: '}', token: 'delimiter.parenthesis' },\n        { open: '@(', close: ')', token: 'delimiter.parenthesis' },\n        { open: '@{', close: '}', token: 'delimiter.brace' },\n        { open: '@[', close: ']', token: 'delimiter.square' },\n        { open: '#[', close: ']', token: 'delimiter.square' },\n        { open: '{', close: '}', token: 'delimiter.curly' },\n        { open: '[', close: ']', token: 'delimiter.square' },\n        { open: '(', close: ')', token: 'delimiter.parenthesis' },\n        { open: '<', close: '>', token: 'delimiter.angle' }\n    ],\n    // we include these common regular expressions\n    symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n    IDENTFST: /[a-zA-Z_]/,\n    IDENTRST: /[a-zA-Z0-9_'$]/,\n    symbolic: /[%&+-./:=@~`^|*!$#?<>]/,\n    digit: /[0-9]/,\n    digitseq0: /@digit*/,\n    xdigit: /[0-9A-Za-z]/,\n    xdigitseq0: /@xdigit*/,\n    INTSP: /[lLuU]/,\n    FLOATSP: /[fFlL]/,\n    fexponent: /[eE][+-]?[0-9]+/,\n    fexponent_bin: /[pP][+-]?[0-9]+/,\n    deciexp: /\\.[0-9]*@fexponent?/,\n    hexiexp: /\\.[0-9a-zA-Z]*@fexponent_bin?/,\n    irregular_keywords: /val[+-]?|case[+-]?|addr\\@?|fold\\@|free\\@|fix\\@?|lam\\@?|llam\\@?|prop[+-]?|type[+-]?|view[+-@]?|viewt@?ype[+-]?|t@?ype[+-]?|v(iew)?t@?ype[+-]?|abst@?ype|absv(iew)?t@?ype|for\\*?|while\\*?/,\n    ESCHAR: /[ntvbrfa\\\\\\?'\"\\(\\[\\{]/,\n    start: 'root',\n    // The main tokenizer for ATS/Postiats\n    // reference: https://github.com/githwxi/ATS-Postiats/blob/master/src/pats_lexing.dats\n    tokenizer: {\n        root: [\n            // lexing_blankseq0\n            { regex: /[ \\t\\r\\n]+/, action: { token: '' } },\n            // NOTE: (*) is an invalid ML-like comment!\n            { regex: /\\(\\*\\)/, action: { token: 'invalid' } },\n            {\n                regex: /\\(\\*/,\n                action: { token: 'comment', next: 'lexing_COMMENT_block_ml' }\n            },\n            {\n                regex: /\\(/,\n                action: '@brackets' /*{ token: 'delimiter.parenthesis' }*/\n            },\n            {\n                regex: /\\)/,\n                action: '@brackets' /*{ token: 'delimiter.parenthesis' }*/\n            },\n            {\n                regex: /\\[/,\n                action: '@brackets' /*{ token: 'delimiter.bracket' }*/\n            },\n            {\n                regex: /\\]/,\n                action: '@brackets' /*{ token: 'delimiter.bracket' }*/\n            },\n            {\n                regex: /\\{/,\n                action: '@brackets' /*{ token: 'delimiter.brace' }*/\n            },\n            {\n                regex: /\\}/,\n                action: '@brackets' /*{ token: 'delimiter.brace' }*/\n            },\n            // lexing_COMMA\n            {\n                regex: /,\\(/,\n                action: '@brackets' /*{ token: 'delimiter.parenthesis' }*/\n            },\n            { regex: /,/, action: { token: 'delimiter.comma' } },\n            { regex: /;/, action: { token: 'delimiter.semicolon' } },\n            // lexing_AT\n            {\n                regex: /@\\(/,\n                action: '@brackets' /* { token: 'delimiter.parenthesis' }*/\n            },\n            {\n                regex: /@\\[/,\n                action: '@brackets' /* { token: 'delimiter.bracket' }*/\n            },\n            {\n                regex: /@\\{/,\n                action: '@brackets' /*{ token: 'delimiter.brace' }*/\n            },\n            // lexing_COLON\n            {\n                regex: /:</,\n                action: { token: 'keyword', next: '@lexing_EFFECT_commaseq0' }\n            },\n            /*\n            lexing_DOT:\n\n            . // SYMBOLIC => lexing_IDENT_sym\n            . FLOATDOT => lexing_FLOAT_deciexp\n            . DIGIT => T_DOTINT\n            */\n            { regex: /\\.@symbolic+/, action: { token: 'identifier.sym' } },\n            // FLOATDOT case\n            {\n                regex: /\\.@digit*@fexponent@FLOATSP*/,\n                action: { token: 'number.float' }\n            },\n            { regex: /\\.@digit+/, action: { token: 'number.float' } },\n            // lexing_DOLLAR:\n            // '$' IDENTFST IDENTRST* => lexing_IDENT_dlr, _ => lexing_IDENT_sym\n            {\n                regex: /\\$@IDENTFST@IDENTRST*/,\n                action: {\n                    cases: {\n                        '@keywords_dlr': { token: 'keyword.dlr' },\n                        '@default': { token: 'namespace' } // most likely a module qualifier\n                    }\n                }\n            },\n            // lexing_SHARP:\n            // '#' IDENTFST IDENTRST* => lexing_ident_srp, _ => lexing_IDENT_sym\n            {\n                regex: /\\#@IDENTFST@IDENTRST*/,\n                action: {\n                    cases: {\n                        '@keywords_srp': { token: 'keyword.srp' },\n                        '@default': { token: 'identifier' }\n                    }\n                }\n            },\n            // lexing_PERCENT:\n            { regex: /%\\(/, action: { token: 'delimiter.parenthesis' } },\n            {\n                regex: /^%{(#|\\^|\\$)?/,\n                action: {\n                    token: 'keyword',\n                    next: '@lexing_EXTCODE',\n                    nextEmbedded: 'text/javascript'\n                }\n            },\n            { regex: /^%}/, action: { token: 'keyword' } },\n            // lexing_QUOTE\n            { regex: /'\\(/, action: { token: 'delimiter.parenthesis' } },\n            { regex: /'\\[/, action: { token: 'delimiter.bracket' } },\n            { regex: /'\\{/, action: { token: 'delimiter.brace' } },\n            [/(')(\\\\@ESCHAR|\\\\[xX]@xdigit+|\\\\@digit+)(')/, ['string', 'string.escape', 'string']],\n            [/'[^\\\\']'/, 'string'],\n            // lexing_DQUOTE\n            [/\"/, 'string.quote', '@lexing_DQUOTE'],\n            // lexing_BQUOTE\n            {\n                regex: /`\\(/,\n                action: '@brackets' /* { token: 'delimiter.parenthesis' }*/\n            },\n            // TODO: otherwise, try lexing_IDENT_sym\n            { regex: /\\\\/, action: { token: 'punctuation' } },\n            // lexing_IDENT_alp:\n            // NOTE: (?!regex) is syntax for \"not-followed-by\" regex\n            // to resolve ambiguity such as foreach$fwork being incorrectly lexed as [for] [each$fwork]!\n            {\n                regex: /@irregular_keywords(?!@IDENTRST)/,\n                action: { token: 'keyword' }\n            },\n            {\n                regex: /@IDENTFST@IDENTRST*[<!\\[]?/,\n                action: {\n                    cases: {\n                        // TODO: dynload and staload should be specially parsed\n                        // dynload whitespace+ \"special_string\"\n                        // this special string is really:\n                        //  '/' '\\\\' '.' => punctuation\n                        // ({\\$)([a-zA-Z_][a-zA-Z_0-9]*)(}) => punctuation,keyword,punctuation\n                        // [^\"] => identifier/literal\n                        '@keywords': { token: 'keyword' },\n                        '@keywords_types': { token: 'type' },\n                        '@default': { token: 'identifier' }\n                    }\n                }\n            },\n            // lexing_IDENT_sym:\n            {\n                regex: /\\/\\/\\/\\//,\n                action: { token: 'comment', next: '@lexing_COMMENT_rest' }\n            },\n            { regex: /\\/\\/.*$/, action: { token: 'comment' } },\n            {\n                regex: /\\/\\*/,\n                action: { token: 'comment', next: '@lexing_COMMENT_block_c' }\n            },\n            // AS-20160627: specifically for effect annotations\n            {\n                regex: /-<|=</,\n                action: { token: 'keyword', next: '@lexing_EFFECT_commaseq0' }\n            },\n            {\n                regex: /@symbolic+/,\n                action: {\n                    cases: {\n                        '@operators': 'keyword',\n                        '@default': 'operator'\n                    }\n                }\n            },\n            // lexing_ZERO:\n            // FIXME: this one is quite messy/unfinished yet\n            // TODO: lexing_INT_hex\n            // - testing_hexiexp => lexing_FLOAT_hexiexp\n            // - testing_fexponent_bin => lexing_FLOAT_hexiexp\n            // - testing_intspseq0 => T_INT_hex\n            // lexing_INT_hex:\n            {\n                regex: /0[xX]@xdigit+(@hexiexp|@fexponent_bin)@FLOATSP*/,\n                action: { token: 'number.float' }\n            },\n            { regex: /0[xX]@xdigit+@INTSP*/, action: { token: 'number.hex' } },\n            {\n                regex: /0[0-7]+(?![0-9])@INTSP*/,\n                action: { token: 'number.octal' }\n            },\n            //{regex: /0/, action: { token: 'number' } }, // INTZERO\n            // lexing_INT_dec:\n            // - testing_deciexp => lexing_FLOAT_deciexp\n            // - testing_fexponent => lexing_FLOAT_deciexp\n            // - otherwise => intspseq0 ([0-9]*[lLuU]?)\n            {\n                regex: /@digit+(@fexponent|@deciexp)@FLOATSP*/,\n                action: { token: 'number.float' }\n            },\n            {\n                regex: /@digit@digitseq0@INTSP*/,\n                action: { token: 'number.decimal' }\n            },\n            // DIGIT, if followed by digitseq0, is lexing_INT_dec\n            { regex: /@digit+@INTSP*/, action: { token: 'number' } }\n        ],\n        lexing_COMMENT_block_ml: [\n            [/[^\\(\\*]+/, 'comment'],\n            [/\\(\\*/, 'comment', '@push'],\n            [/\\(\\*/, 'comment.invalid'],\n            [/\\*\\)/, 'comment', '@pop'],\n            [/\\*/, 'comment']\n        ],\n        lexing_COMMENT_block_c: [\n            [/[^\\/*]+/, 'comment'],\n            // [/\\/\\*/, 'comment', '@push' ],    // nested C-style block comments not allowed\n            // [/\\/\\*/,    'comment.invalid' ],\t// NOTE: this breaks block comments in the shape of /* //*/\n            [/\\*\\//, 'comment', '@pop'],\n            [/[\\/*]/, 'comment']\n        ],\n        lexing_COMMENT_rest: [\n            [/$/, 'comment', '@pop'],\n            [/.*/, 'comment']\n        ],\n        // NOTE: added by AS, specifically for highlighting\n        lexing_EFFECT_commaseq0: [\n            {\n                regex: /@IDENTFST@IDENTRST+|@digit+/,\n                action: {\n                    cases: {\n                        '@keywords_effects': { token: 'type.effect' },\n                        '@default': { token: 'identifier' }\n                    }\n                }\n            },\n            { regex: /,/, action: { token: 'punctuation' } },\n            { regex: />/, action: { token: '@rematch', next: '@pop' } }\n        ],\n        lexing_EXTCODE: [\n            {\n                regex: /^%}/,\n                action: {\n                    token: '@rematch',\n                    next: '@pop',\n                    nextEmbedded: '@pop'\n                }\n            },\n            { regex: /[^%]+/, action: '' }\n        ],\n        lexing_DQUOTE: [\n            { regex: /\"/, action: { token: 'string.quote', next: '@pop' } },\n            // AS-20160628: additional hi-lighting for variables in staload/dynload strings\n            {\n                regex: /(\\{\\$)(@IDENTFST@IDENTRST*)(\\})/,\n                action: [{ token: 'string.escape' }, { token: 'identifier' }, { token: 'string.escape' }]\n            },\n            { regex: /\\\\$/, action: { token: 'string.escape' } },\n            {\n                regex: /\\\\(@ESCHAR|[xX]@xdigit+|@digit+)/,\n                action: { token: 'string.escape' }\n            },\n            { regex: /[^\\\\\"]+/, action: { token: 'string' } }\n        ]\n    }\n};\n"], "mappings": ";;;AAMO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC7B;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EAC1D;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,cAAc;AAAA;AAAA,EAEd,UAAU;AAAA;AAAA,IAEN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA;AAAA,IAIA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA;AAAA,EAEJ;AAAA,EACA,cAAc;AAAA,IACV;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,EACJ;AAAA,EACA,cAAc;AAAA;AAAA,IAEV;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAIJ;AAAA,EACA,wBAAwB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,gBAAgB;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA;AAAA,EAEA,kBAAkB;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,EACJ;AAAA,EACA,WAAW;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKJ;AAAA,EACA,UAAU;AAAA,IACN,EAAE,MAAM,MAAM,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACzD,EAAE,MAAM,MAAM,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACzD,EAAE,MAAM,MAAM,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACzD,EAAE,MAAM,MAAM,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACzD,EAAE,MAAM,MAAM,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACzD,EAAE,MAAM,MAAM,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACzD,EAAE,MAAM,MAAM,OAAO,KAAK,OAAO,kBAAkB;AAAA,IACnD,EAAE,MAAM,MAAM,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACpD,EAAE,MAAM,MAAM,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACpD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,EACtD;AAAA;AAAA,EAEA,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,OAAO;AAAA,EACP,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,SAAS;AAAA,EACT,WAAW;AAAA,EACX,eAAe;AAAA,EACf,SAAS;AAAA,EACT,SAAS;AAAA,EACT,oBAAoB;AAAA,EACpB,QAAQ;AAAA,EACR,OAAO;AAAA;AAAA;AAAA,EAGP,WAAW;AAAA,IACP,MAAM;AAAA;AAAA,MAEF,EAAE,OAAO,cAAc,QAAQ,EAAE,OAAO,GAAG,EAAE;AAAA;AAAA,MAE7C,EAAE,OAAO,UAAU,QAAQ,EAAE,OAAO,UAAU,EAAE;AAAA,MAChD;AAAA,QACI,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,WAAW,MAAM,0BAA0B;AAAA,MAChE;AAAA,MACA;AAAA,QACI,OAAO;AAAA,QACP,QAAQ;AAAA;AAAA,MACZ;AAAA,MACA;AAAA,QACI,OAAO;AAAA,QACP,QAAQ;AAAA;AAAA,MACZ;AAAA,MACA;AAAA,QACI,OAAO;AAAA,QACP,QAAQ;AAAA;AAAA,MACZ;AAAA,MACA;AAAA,QACI,OAAO;AAAA,QACP,QAAQ;AAAA;AAAA,MACZ;AAAA,MACA;AAAA,QACI,OAAO;AAAA,QACP,QAAQ;AAAA;AAAA,MACZ;AAAA,MACA;AAAA,QACI,OAAO;AAAA,QACP,QAAQ;AAAA;AAAA,MACZ;AAAA;AAAA,MAEA;AAAA,QACI,OAAO;AAAA,QACP,QAAQ;AAAA;AAAA,MACZ;AAAA,MACA,EAAE,OAAO,KAAK,QAAQ,EAAE,OAAO,kBAAkB,EAAE;AAAA,MACnD,EAAE,OAAO,KAAK,QAAQ,EAAE,OAAO,sBAAsB,EAAE;AAAA;AAAA,MAEvD;AAAA,QACI,OAAO;AAAA,QACP,QAAQ;AAAA;AAAA,MACZ;AAAA,MACA;AAAA,QACI,OAAO;AAAA,QACP,QAAQ;AAAA;AAAA,MACZ;AAAA,MACA;AAAA,QACI,OAAO;AAAA,QACP,QAAQ;AAAA;AAAA,MACZ;AAAA;AAAA,MAEA;AAAA,QACI,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,WAAW,MAAM,2BAA2B;AAAA,MACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,EAAE,OAAO,gBAAgB,QAAQ,EAAE,OAAO,iBAAiB,EAAE;AAAA;AAAA,MAE7D;AAAA,QACI,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,eAAe;AAAA,MACpC;AAAA,MACA,EAAE,OAAO,aAAa,QAAQ,EAAE,OAAO,eAAe,EAAE;AAAA;AAAA;AAAA,MAGxD;AAAA,QACI,OAAO;AAAA,QACP,QAAQ;AAAA,UACJ,OAAO;AAAA,YACH,iBAAiB,EAAE,OAAO,cAAc;AAAA,YACxC,YAAY,EAAE,OAAO,YAAY;AAAA;AAAA,UACrC;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA;AAAA,MAGA;AAAA,QACI,OAAO;AAAA,QACP,QAAQ;AAAA,UACJ,OAAO;AAAA,YACH,iBAAiB,EAAE,OAAO,cAAc;AAAA,YACxC,YAAY,EAAE,OAAO,aAAa;AAAA,UACtC;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,EAAE,OAAO,OAAO,QAAQ,EAAE,OAAO,wBAAwB,EAAE;AAAA,MAC3D;AAAA,QACI,OAAO;AAAA,QACP,QAAQ;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,EAAE,OAAO,OAAO,QAAQ,EAAE,OAAO,UAAU,EAAE;AAAA;AAAA,MAE7C,EAAE,OAAO,OAAO,QAAQ,EAAE,OAAO,wBAAwB,EAAE;AAAA,MAC3D,EAAE,OAAO,OAAO,QAAQ,EAAE,OAAO,oBAAoB,EAAE;AAAA,MACvD,EAAE,OAAO,OAAO,QAAQ,EAAE,OAAO,kBAAkB,EAAE;AAAA,MACrD,CAAC,8CAA8C,CAAC,UAAU,iBAAiB,QAAQ,CAAC;AAAA,MACpF,CAAC,YAAY,QAAQ;AAAA;AAAA,MAErB,CAAC,KAAK,gBAAgB,gBAAgB;AAAA;AAAA,MAEtC;AAAA,QACI,OAAO;AAAA,QACP,QAAQ;AAAA;AAAA,MACZ;AAAA;AAAA,MAEA,EAAE,OAAO,MAAM,QAAQ,EAAE,OAAO,cAAc,EAAE;AAAA;AAAA;AAAA;AAAA,MAIhD;AAAA,QACI,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,UAAU;AAAA,MAC/B;AAAA,MACA;AAAA,QACI,OAAO;AAAA,QACP,QAAQ;AAAA,UACJ,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAOH,aAAa,EAAE,OAAO,UAAU;AAAA,YAChC,mBAAmB,EAAE,OAAO,OAAO;AAAA,YACnC,YAAY,EAAE,OAAO,aAAa;AAAA,UACtC;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA;AAAA,QACI,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,WAAW,MAAM,uBAAuB;AAAA,MAC7D;AAAA,MACA,EAAE,OAAO,WAAW,QAAQ,EAAE,OAAO,UAAU,EAAE;AAAA,MACjD;AAAA,QACI,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,WAAW,MAAM,0BAA0B;AAAA,MAChE;AAAA;AAAA,MAEA;AAAA,QACI,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,WAAW,MAAM,2BAA2B;AAAA,MACjE;AAAA,MACA;AAAA,QACI,OAAO;AAAA,QACP,QAAQ;AAAA,UACJ,OAAO;AAAA,YACH,cAAc;AAAA,YACd,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA;AAAA,QACI,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,eAAe;AAAA,MACpC;AAAA,MACA,EAAE,OAAO,wBAAwB,QAAQ,EAAE,OAAO,aAAa,EAAE;AAAA,MACjE;AAAA,QACI,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,eAAe;AAAA,MACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA;AAAA,QACI,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,eAAe;AAAA,MACpC;AAAA,MACA;AAAA,QACI,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,iBAAiB;AAAA,MACtC;AAAA;AAAA,MAEA,EAAE,OAAO,kBAAkB,QAAQ,EAAE,OAAO,SAAS,EAAE;AAAA,IAC3D;AAAA,IACA,yBAAyB;AAAA,MACrB,CAAC,YAAY,SAAS;AAAA,MACtB,CAAC,QAAQ,WAAW,OAAO;AAAA,MAC3B,CAAC,QAAQ,iBAAiB;AAAA,MAC1B,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,MAAM,SAAS;AAAA,IACpB;AAAA,IACA,wBAAwB;AAAA,MACpB,CAAC,WAAW,SAAS;AAAA;AAAA;AAAA,MAGrB,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,SAAS,SAAS;AAAA,IACvB;AAAA,IACA,qBAAqB;AAAA,MACjB,CAAC,KAAK,WAAW,MAAM;AAAA,MACvB,CAAC,MAAM,SAAS;AAAA,IACpB;AAAA;AAAA,IAEA,yBAAyB;AAAA,MACrB;AAAA,QACI,OAAO;AAAA,QACP,QAAQ;AAAA,UACJ,OAAO;AAAA,YACH,qBAAqB,EAAE,OAAO,cAAc;AAAA,YAC5C,YAAY,EAAE,OAAO,aAAa;AAAA,UACtC;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,EAAE,OAAO,KAAK,QAAQ,EAAE,OAAO,cAAc,EAAE;AAAA,MAC/C,EAAE,OAAO,KAAK,QAAQ,EAAE,OAAO,YAAY,MAAM,OAAO,EAAE;AAAA,IAC9D;AAAA,IACA,gBAAgB;AAAA,MACZ;AAAA,QACI,OAAO;AAAA,QACP,QAAQ;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,EAAE,OAAO,SAAS,QAAQ,GAAG;AAAA,IACjC;AAAA,IACA,eAAe;AAAA,MACX,EAAE,OAAO,KAAK,QAAQ,EAAE,OAAO,gBAAgB,MAAM,OAAO,EAAE;AAAA;AAAA,MAE9D;AAAA,QACI,OAAO;AAAA,QACP,QAAQ,CAAC,EAAE,OAAO,gBAAgB,GAAG,EAAE,OAAO,aAAa,GAAG,EAAE,OAAO,gBAAgB,CAAC;AAAA,MAC5F;AAAA,MACA,EAAE,OAAO,OAAO,QAAQ,EAAE,OAAO,gBAAgB,EAAE;AAAA,MACnD;AAAA,QACI,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,gBAAgB;AAAA,MACrC;AAAA,MACA,EAAE,OAAO,WAAW,QAAQ,EAAE,OAAO,SAAS,EAAE;AAAA,IACpD;AAAA,EACJ;AACJ;", "names": []}