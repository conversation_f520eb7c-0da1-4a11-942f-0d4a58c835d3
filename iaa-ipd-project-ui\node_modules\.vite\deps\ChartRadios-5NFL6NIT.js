import {
  supportStatic
} from "./chunk-JHAOQP73.js";
import {
  __assign,
  __decorate,
  __extends,
  __metadata
} from "./chunk-F25BIIHK.js";
import {
  OptionsControl,
  autobind
} from "./chunk-LZQZ2OHM.js";
import "./chunk-5QW7M2DY.js";
import "./chunk-ZJNJ4ZV3.js";
import "./chunk-CNJUQDSN.js";
import "./chunk-7XBFYOTW.js";
import {
  require_react
} from "./chunk-ZUWHLQVA.js";
import {
  __toESM
} from "./chunk-GFT2G5UO.js";

// node_modules/amis/esm/renderers/Form/ChartRadios.js
var import_react = __toESM(require_react());
var ChartRadiosControl = (
  /** @class */
  function(_super) {
    __extends(ChartRadiosControl2, _super);
    function ChartRadiosControl2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.highlightIndex = -1;
      _this.prevIndex = -1;
      return _this;
    }
    ChartRadiosControl2.prototype.chartRef = function(chart) {
      var _this = this;
      var _a;
      this.chart = chart;
      (_a = this.chart) === null || _a === void 0 ? void 0 : _a.on("click", "series", function(params) {
        _this.props.onToggle(_this.props.options[params.dataIndex]);
      });
      setTimeout(function() {
        return _this.highlight();
      });
    };
    ChartRadiosControl2.prototype.highlight = function(index) {
      if (index === void 0) {
        index = this.highlightIndex;
      }
      if (this.props.static) {
        return;
      }
      this.highlightIndex = index;
      if (!this.chart || this.prevIndex === index) {
        return;
      }
      if (~this.prevIndex) {
        this.chart.dispatchAction({
          type: "downplay",
          seriesIndex: 0,
          dataIndex: this.prevIndex
        });
      }
      if (~index) {
        this.chart.dispatchAction({
          type: "highlight",
          seriesIndex: 0,
          dataIndex: index
        });
        if (this.props.showTooltipOnHighlight) {
          this.chart.dispatchAction({
            type: "showTip",
            seriesIndex: 0,
            dataIndex: index
          });
        }
      }
      this.prevIndex = index;
    };
    ChartRadiosControl2.prototype.componentDidMount = function() {
      if (this.props.selectedOptions.length) {
        this.highlight(this.props.options.indexOf(this.props.selectedOptions[0]));
      }
    };
    ChartRadiosControl2.prototype.componentDidUpdate = function() {
      if (this.props.selectedOptions.length) {
        this.highlight(this.props.options.indexOf(this.props.selectedOptions[0]));
      }
    };
    ChartRadiosControl2.prototype.renderStatic = function(displayValue) {
      if (displayValue === void 0) {
        displayValue = "-";
      }
      this.prevIndex = -1;
      this.highlightIndex = -1;
      var _a = this.props, _b = _a.options, options = _b === void 0 ? [] : _b, selectedOptions = _a.selectedOptions, _c = _a.labelField, labelField = _c === void 0 ? "label" : _c, _d = _a.valueField, valueField = _d === void 0 ? "value" : _d, chartValueField = _a.chartValueField;
      if (options.length && selectedOptions.length) {
        var count = options.reduce(function(all, cur) {
          return all + cur[chartValueField || valueField];
        }, 0);
        if (count > 0) {
          var percent = (+selectedOptions[0][chartValueField || valueField] / count * 100).toFixed(2);
          displayValue = "".concat(selectedOptions[0][labelField], "：").concat(percent, "%");
        }
      }
      return import_react.default.createElement(import_react.default.Fragment, null, displayValue);
    };
    ChartRadiosControl2.prototype.render = function() {
      var _a = this.props, options = _a.options, labelField = _a.labelField, chartValueField = _a.chartValueField, valueField = _a.valueField, render = _a.render;
      var config = __assign(__assign({ legend: {
        top: 10
      }, tooltip: {
        formatter: function(params) {
          return "".concat(params.name, "：").concat(params.value[chartValueField || valueField || "value"], "（").concat(params.percent, "%）");
        }
      }, series: [
        {
          type: "pie",
          top: 30,
          bottom: 0
        }
      ] }, this.props.config), { dataset: {
        dimensions: [
          labelField || "label",
          chartValueField || valueField || "value"
        ],
        source: options
      } });
      return render("chart", {
        type: "chart"
      }, {
        config,
        chartRef: this.chartRef
      });
    };
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], ChartRadiosControl2.prototype, "chartRef", null);
    __decorate([
      supportStatic(),
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", void 0)
    ], ChartRadiosControl2.prototype, "render", null);
    return ChartRadiosControl2;
  }(import_react.default.Component)
);
var RadiosControlRenderer = (
  /** @class */
  function(_super) {
    __extends(RadiosControlRenderer2, _super);
    function RadiosControlRenderer2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    RadiosControlRenderer2.defaultProps = {
      multiple: false
    };
    RadiosControlRenderer2 = __decorate([
      OptionsControl({
        type: "chart-radios",
        sizeMutable: false
      })
    ], RadiosControlRenderer2);
    return RadiosControlRenderer2;
  }(ChartRadiosControl)
);
export {
  RadiosControlRenderer,
  ChartRadiosControl as default
};
//# sourceMappingURL=ChartRadios-5NFL6NIT.js.map
