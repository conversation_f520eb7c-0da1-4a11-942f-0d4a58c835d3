import { defineComponent, ref, h, reactive, computed, onMounted, onBeforeUnmount } from 'vue';
import XEUtils from 'xe-utils';
import { getConfig, useSize, createEvent, globalEvents, renderEmptyElement } from '../../ui';
import { toCssUnit } from '../../ui/src/dom';
export default defineComponent({
    name: 'VxeNoticeBar',
    props: {
        duration: [String, Number],
        direction: {
            type: String,
            default: () => getConfig().noticeBar.direction
        },
        speed: {
            type: String,
            default: () => getConfig().noticeBar.speed
        },
        content: String,
        vertical: Boolean,
        loop: {
            type: Boolean
        },
        size: {
            type: String,
            default: () => getConfig().noticeBar.size || getConfig().size
        }
    },
    emits: [],
    setup(props, context) {
        const { slots, emit } = context;
        const xID = XEUtils.uniqueId();
        const { computeSize } = useSize(props);
        const refElem = ref();
        const refContentElem = ref();
        const reactData = reactive({
            animationDuration: 0
        });
        const refMaps = {
            refElem
        };
        const computeNoticeText = computed(() => {
            const { content } = props;
            return `${content || ''}`;
        });
        const computeMaps = {};
        const $xeNoticeBar = {
            xID,
            props,
            context,
            reactData,
            getRefMaps: () => refMaps,
            getComputeMaps: () => computeMaps
        };
        const dispatchEvent = (type, params, evnt) => {
            emit(type, createEvent(evnt, { $noticeBar: $xeNoticeBar }, params));
        };
        const noticeBarMethods = {
            dispatchEvent
        };
        const noticeBarPrivateMethods = {};
        const updateAnimationStyle = () => {
            const { speed } = props;
            const contEl = refContentElem.value;
            if (contEl) {
                let sRate = 46;
                if (speed === 'fast') {
                    sRate = 118;
                }
                else if (speed === 'slow') {
                    sRate = 18;
                }
                reactData.animationDuration = Math.ceil(contEl.scrollWidth / sRate);
            }
        };
        Object.assign($xeNoticeBar, noticeBarMethods, noticeBarPrivateMethods);
        const renderVN = () => {
            const { vertical, duration, direction } = props;
            const { animationDuration } = reactData;
            const vSize = computeSize.value;
            const noticeText = computeNoticeText.value;
            const defaultSlot = slots.default;
            const prefixSlot = slots.prefix;
            const suffixSlot = slots.suffix;
            return h('div', {
                ref: refElem,
                class: ['vxe-notice-bar', `is--${vertical ? 'vertical' : 'horizontal'}`, `dir--${direction || 'left'}`, {
                        [`size--${vSize}`]: vSize
                    }]
            }, [
                prefixSlot
                    ? h('div', {
                        class: 'vxe-notice-bar--prefix'
                    }, prefixSlot({}))
                    : renderEmptyElement($xeNoticeBar),
                h('div', {
                    class: 'vxe-notice-bar--content'
                }, [
                    h('div', {
                        ref: refContentElem,
                        class: 'vxe-notice-bar--inner'
                    }, [
                        h('div', {
                            class: 'vxe-notice-bar--wrapper',
                            style: {
                                animationDuration: `${duration ? toCssUnit(duration, 's') : animationDuration}s`
                            }
                        }, defaultSlot ? defaultSlot({}) : noticeText)
                    ])
                ]),
                suffixSlot
                    ? h('div', {
                        class: 'vxe-notice-bar--suffix'
                    }, suffixSlot({}))
                    : renderEmptyElement($xeNoticeBar)
            ]);
        };
        $xeNoticeBar.renderVN = renderVN;
        onMounted(() => {
            globalEvents.on($xeNoticeBar, 'resize', updateAnimationStyle);
            updateAnimationStyle();
        });
        onBeforeUnmount(() => {
            globalEvents.off($xeNoticeBar, 'resize');
        });
        return $xeNoticeBar;
    },
    render() {
        return this.renderVN();
    }
});
