Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var _vue=require("vue"),_comp=require("../../../ui/src/comp"),_ui=require("../../../ui"),_utils=require("../../../ui/src/utils"),_xeUtils=_interopRequireDefault(require("xe-utils"));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let getIcon=_ui.VxeUI.getIcon;var _default=exports.default=(0,_comp.defineVxeComponent)({name:"VxeTableMenuPanel",setup(e,t){var n=_xeUtils.default.uniqueId();let a=(0,_vue.inject)("$xeTable",{}),u=a.reactData,s=(0,_vue.ref)(),i={refElem:s};n={xID:n,props:e,context:t,getRefMaps:()=>i};return n.renderVN=()=>{let o=u.ctxMenuStore;var e=a.getComputeMaps().computeMenuOpts,e=e.value;return(0,_vue.h)(_vue.Teleport,{to:"body",disabled:!1},[(0,_vue.h)("div",{ref:s,class:["vxe-table--context-menu-wrapper",e.className,{"is--visible":o.visible}],style:o.style},o.list.map((e,c)=>e.every(e=>!1===e.visible)?(0,_vue.createCommentVNode)():(0,_vue.h)("ul",{class:"vxe-context-menu--option-wrapper",key:c},e.map((i,l)=>{var e=i.children&&i.children.some(e=>!1!==e.visible),t=Object.assign({},i.prefixConfig),n=Object.assign({},i.suffixConfig),u=(0,_utils.getFuncText)(i.name);return!1===i.visible?null:(0,_vue.h)("li",{class:[i.className,{"link--disabled":i.disabled,"link--active":i===o.selected}],key:c+"_"+l},[(0,_vue.h)("a",{class:"vxe-context-menu--link",onClick(e){a.ctxMenuLinkEvent(e,i)},onMouseover(e){a.ctxMenuMouseoverEvent(e,i)},onMouseout(e){a.ctxMenuMouseoutEvent(e,i)}},[(0,_vue.h)("div",{class:["vxe-context-menu--link-prefix",t.className||""]},[(0,_vue.h)("i",{class:t.icon||i.prefixIcon}),t.content?(0,_vue.h)("span",{},""+t.content):(0,_vue.createCommentVNode)()]),(0,_vue.h)("div",{class:"vxe-context-menu--link-content",title:u},u),(0,_vue.h)("div",{class:["vxe-context-menu--link-suffix",n.className||""]},[(0,_vue.h)("i",{class:n.icon||i.suffixIcon||(e?getIcon().TABLE_MENU_OPTIONS:"")}),n.content?(0,_vue.h)("span",""+n.content):(0,_vue.createCommentVNode)()])]),e?(0,_vue.h)("ul",{class:["vxe-table--context-menu-clild-wrapper",{"is--show":i===o.selected&&o.showChild}]},i.children.map((t,e)=>{var n=Object.assign({},t.prefixConfig),u=Object.assign({},t.suffixConfig),s=(0,_utils.getFuncText)(t.name);return!1===t.visible?null:(0,_vue.h)("li",{class:[t.className,{"link--disabled":t.disabled,"link--active":t===o.selectChild}],key:c+`_${l}_`+e},[(0,_vue.h)("a",{class:"vxe-context-menu--link",onClick(e){a.ctxMenuLinkEvent(e,t)},onMouseover(e){a.ctxMenuMouseoverEvent(e,i,t)},onMouseout(e){a.ctxMenuMouseoutEvent(e,i)}},[(0,_vue.h)("div",{class:["vxe-context-menu--link-prefix",n.className||""]},[(0,_vue.h)("i",{class:n.icon||t.prefixIcon}),n.content?(0,_vue.h)("span",""+n.content):(0,_vue.createCommentVNode)()]),(0,_vue.h)("div",{class:"vxe-context-menu--link-content",title:s},s),(0,_vue.h)("div",{class:["vxe-context-menu--link-suffix",u.className||""]},[(0,_vue.h)("i",{class:u.icon}),u.content?(0,_vue.h)("span",""+u.content):(0,_vue.createCommentVNode)()])])])})):null])}))))])},n},render(){return this.renderVN()}});