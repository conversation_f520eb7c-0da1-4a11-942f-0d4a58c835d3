{"version": 3, "sources": ["../../monaco-editor/esm/vs/language/typescript/workerManager.js", "../../monaco-editor/esm/vs/language/typescript/lib/lib.index.js", "../../monaco-editor/esm/vs/language/typescript/languageFeatures.js", "../../monaco-editor/esm/vs/language/typescript/tsMode.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n'use strict';\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nimport { editor } from './fillers/monaco-editor-core.js';\nvar WorkerManager = /** @class */ (function () {\n    function WorkerManager(modeId, defaults) {\n        var _this = this;\n        this._modeId = modeId;\n        this._defaults = defaults;\n        this._worker = null;\n        this._client = null;\n        this._configChangeListener = this._defaults.onDidChange(function () { return _this._stopWorker(); });\n        this._updateExtraLibsToken = 0;\n        this._extraLibsChangeListener = this._defaults.onDidExtraLibsChange(function () {\n            return _this._updateExtraLibs();\n        });\n    }\n    WorkerManager.prototype._stopWorker = function () {\n        if (this._worker) {\n            this._worker.dispose();\n            this._worker = null;\n        }\n        this._client = null;\n    };\n    WorkerManager.prototype.dispose = function () {\n        this._configChangeListener.dispose();\n        this._extraLibsChangeListener.dispose();\n        this._stopWorker();\n    };\n    WorkerManager.prototype._updateExtraLibs = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var myToken, proxy;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this._worker) {\n                            return [2 /*return*/];\n                        }\n                        myToken = ++this._updateExtraLibsToken;\n                        return [4 /*yield*/, this._worker.getProxy()];\n                    case 1:\n                        proxy = _a.sent();\n                        if (this._updateExtraLibsToken !== myToken) {\n                            // avoid multiple calls\n                            return [2 /*return*/];\n                        }\n                        proxy.updateExtraLibs(this._defaults.getExtraLibs());\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    WorkerManager.prototype._getClient = function () {\n        var _this = this;\n        if (!this._client) {\n            this._worker = editor.createWebWorker({\n                // module that exports the create() method and returns a `TypeScriptWorker` instance\n                moduleId: 'vs/language/typescript/tsWorker',\n                label: this._modeId,\n                keepIdleModels: true,\n                // passed in to the create() method\n                createData: {\n                    compilerOptions: this._defaults.getCompilerOptions(),\n                    extraLibs: this._defaults.getExtraLibs(),\n                    customWorkerPath: this._defaults.workerOptions.customWorkerPath,\n                    inlayHintsOptions: this._defaults.inlayHintsOptions\n                }\n            });\n            var p = this._worker.getProxy();\n            if (this._defaults.getEagerModelSync()) {\n                p = p.then(function (worker) {\n                    if (_this._worker) {\n                        return _this._worker.withSyncedResources(editor\n                            .getModels()\n                            .filter(function (model) { return model.getLanguageId() === _this._modeId; })\n                            .map(function (model) { return model.uri; }));\n                    }\n                    return worker;\n                });\n            }\n            this._client = p;\n        }\n        return this._client;\n    };\n    WorkerManager.prototype.getLanguageServiceWorker = function () {\n        var _this = this;\n        var resources = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            resources[_i] = arguments[_i];\n        }\n        var _client;\n        return this._getClient()\n            .then(function (client) {\n            _client = client;\n        })\n            .then(function (_) {\n            if (_this._worker) {\n                return _this._worker.withSyncedResources(resources);\n            }\n        })\n            .then(function (_) { return _client; });\n    };\n    return WorkerManager;\n}());\nexport { WorkerManager };\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n//\n// **NOTE**: Do not edit directly! This file is generated using `npm run import-typescript`\n//\n/** Contains all the lib files */\nexport var libFileSet = {};\nlibFileSet['lib.d.ts'] = true;\nlibFileSet['lib.dom.d.ts'] = true;\nlibFileSet['lib.dom.iterable.d.ts'] = true;\nlibFileSet['lib.es2015.collection.d.ts'] = true;\nlibFileSet['lib.es2015.core.d.ts'] = true;\nlibFileSet['lib.es2015.d.ts'] = true;\nlibFileSet['lib.es2015.generator.d.ts'] = true;\nlibFileSet['lib.es2015.iterable.d.ts'] = true;\nlibFileSet['lib.es2015.promise.d.ts'] = true;\nlibFileSet['lib.es2015.proxy.d.ts'] = true;\nlibFileSet['lib.es2015.reflect.d.ts'] = true;\nlibFileSet['lib.es2015.symbol.d.ts'] = true;\nlibFileSet['lib.es2015.symbol.wellknown.d.ts'] = true;\nlibFileSet['lib.es2016.array.include.d.ts'] = true;\nlibFileSet['lib.es2016.d.ts'] = true;\nlibFileSet['lib.es2016.full.d.ts'] = true;\nlibFileSet['lib.es2017.d.ts'] = true;\nlibFileSet['lib.es2017.full.d.ts'] = true;\nlibFileSet['lib.es2017.intl.d.ts'] = true;\nlibFileSet['lib.es2017.object.d.ts'] = true;\nlibFileSet['lib.es2017.sharedmemory.d.ts'] = true;\nlibFileSet['lib.es2017.string.d.ts'] = true;\nlibFileSet['lib.es2017.typedarrays.d.ts'] = true;\nlibFileSet['lib.es2018.asyncgenerator.d.ts'] = true;\nlibFileSet['lib.es2018.asynciterable.d.ts'] = true;\nlibFileSet['lib.es2018.d.ts'] = true;\nlibFileSet['lib.es2018.full.d.ts'] = true;\nlibFileSet['lib.es2018.intl.d.ts'] = true;\nlibFileSet['lib.es2018.promise.d.ts'] = true;\nlibFileSet['lib.es2018.regexp.d.ts'] = true;\nlibFileSet['lib.es2019.array.d.ts'] = true;\nlibFileSet['lib.es2019.d.ts'] = true;\nlibFileSet['lib.es2019.full.d.ts'] = true;\nlibFileSet['lib.es2019.object.d.ts'] = true;\nlibFileSet['lib.es2019.string.d.ts'] = true;\nlibFileSet['lib.es2019.symbol.d.ts'] = true;\nlibFileSet['lib.es2020.bigint.d.ts'] = true;\nlibFileSet['lib.es2020.d.ts'] = true;\nlibFileSet['lib.es2020.full.d.ts'] = true;\nlibFileSet['lib.es2020.intl.d.ts'] = true;\nlibFileSet['lib.es2020.promise.d.ts'] = true;\nlibFileSet['lib.es2020.sharedmemory.d.ts'] = true;\nlibFileSet['lib.es2020.string.d.ts'] = true;\nlibFileSet['lib.es2020.symbol.wellknown.d.ts'] = true;\nlibFileSet['lib.es2021.d.ts'] = true;\nlibFileSet['lib.es2021.full.d.ts'] = true;\nlibFileSet['lib.es2021.promise.d.ts'] = true;\nlibFileSet['lib.es2021.string.d.ts'] = true;\nlibFileSet['lib.es2021.weakref.d.ts'] = true;\nlibFileSet['lib.es5.d.ts'] = true;\nlibFileSet['lib.es6.d.ts'] = true;\nlibFileSet['lib.esnext.d.ts'] = true;\nlibFileSet['lib.esnext.full.d.ts'] = true;\nlibFileSet['lib.esnext.intl.d.ts'] = true;\nlibFileSet['lib.esnext.promise.d.ts'] = true;\nlibFileSet['lib.esnext.string.d.ts'] = true;\nlibFileSet['lib.esnext.weakref.d.ts'] = true;\nlibFileSet['lib.scripthost.d.ts'] = true;\nlibFileSet['lib.webworker.d.ts'] = true;\nlibFileSet['lib.webworker.importscripts.d.ts'] = true;\nlibFileSet['lib.webworker.iterable.d.ts'] = true;\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n'use strict';\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nimport { typescriptDefaults } from './monaco.contribution.js';\nimport { libFileSet } from './lib/lib.index.js';\nimport { editor, languages, Uri, Range, MarkerTag, MarkerSeverity } from './fillers/monaco-editor-core.js';\n//#region utils copied from typescript to prevent loading the entire typescriptServices ---\nvar IndentStyle;\n(function (IndentStyle) {\n    IndentStyle[IndentStyle[\"None\"] = 0] = \"None\";\n    IndentStyle[IndentStyle[\"Block\"] = 1] = \"Block\";\n    IndentStyle[IndentStyle[\"Smart\"] = 2] = \"Smart\";\n})(IndentStyle || (IndentStyle = {}));\nexport function flattenDiagnosticMessageText(diag, newLine, indent) {\n    if (indent === void 0) { indent = 0; }\n    if (typeof diag === 'string') {\n        return diag;\n    }\n    else if (diag === undefined) {\n        return '';\n    }\n    var result = '';\n    if (indent) {\n        result += newLine;\n        for (var i = 0; i < indent; i++) {\n            result += '  ';\n        }\n    }\n    result += diag.messageText;\n    indent++;\n    if (diag.next) {\n        for (var _i = 0, _a = diag.next; _i < _a.length; _i++) {\n            var kid = _a[_i];\n            result += flattenDiagnosticMessageText(kid, newLine, indent);\n        }\n    }\n    return result;\n}\nfunction displayPartsToString(displayParts) {\n    if (displayParts) {\n        return displayParts.map(function (displayPart) { return displayPart.text; }).join('');\n    }\n    return '';\n}\n//#endregion\nvar Adapter = /** @class */ (function () {\n    function Adapter(_worker) {\n        this._worker = _worker;\n    }\n    // protected _positionToOffset(model: editor.ITextModel, position: monaco.IPosition): number {\n    // \treturn model.getOffsetAt(position);\n    // }\n    // protected _offsetToPosition(model: editor.ITextModel, offset: number): monaco.IPosition {\n    // \treturn model.getPositionAt(offset);\n    // }\n    Adapter.prototype._textSpanToRange = function (model, span) {\n        var p1 = model.getPositionAt(span.start);\n        var p2 = model.getPositionAt(span.start + span.length);\n        var startLineNumber = p1.lineNumber, startColumn = p1.column;\n        var endLineNumber = p2.lineNumber, endColumn = p2.column;\n        return { startLineNumber: startLineNumber, startColumn: startColumn, endLineNumber: endLineNumber, endColumn: endColumn };\n    };\n    return Adapter;\n}());\nexport { Adapter };\n// --- lib files\nvar LibFiles = /** @class */ (function () {\n    function LibFiles(_worker) {\n        this._worker = _worker;\n        this._libFiles = {};\n        this._hasFetchedLibFiles = false;\n        this._fetchLibFilesPromise = null;\n    }\n    LibFiles.prototype.isLibFile = function (uri) {\n        if (!uri) {\n            return false;\n        }\n        if (uri.path.indexOf('/lib.') === 0) {\n            return !!libFileSet[uri.path.slice(1)];\n        }\n        return false;\n    };\n    LibFiles.prototype.getOrCreateModel = function (fileName) {\n        var uri = Uri.parse(fileName);\n        var model = editor.getModel(uri);\n        if (model) {\n            return model;\n        }\n        if (this.isLibFile(uri) && this._hasFetchedLibFiles) {\n            return editor.createModel(this._libFiles[uri.path.slice(1)], 'typescript', uri);\n        }\n        var matchedLibFile = typescriptDefaults.getExtraLibs()[fileName];\n        if (matchedLibFile) {\n            return editor.createModel(matchedLibFile.content, 'typescript', uri);\n        }\n        return null;\n    };\n    LibFiles.prototype._containsLibFile = function (uris) {\n        for (var _i = 0, uris_1 = uris; _i < uris_1.length; _i++) {\n            var uri = uris_1[_i];\n            if (this.isLibFile(uri)) {\n                return true;\n            }\n        }\n        return false;\n    };\n    LibFiles.prototype.fetchLibFilesIfNecessary = function (uris) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this._containsLibFile(uris)) {\n                            // no lib files necessary\n                            return [2 /*return*/];\n                        }\n                        return [4 /*yield*/, this._fetchLibFiles()];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    LibFiles.prototype._fetchLibFiles = function () {\n        var _this = this;\n        if (!this._fetchLibFilesPromise) {\n            this._fetchLibFilesPromise = this._worker()\n                .then(function (w) { return w.getLibFiles(); })\n                .then(function (libFiles) {\n                _this._hasFetchedLibFiles = true;\n                _this._libFiles = libFiles;\n            });\n        }\n        return this._fetchLibFilesPromise;\n    };\n    return LibFiles;\n}());\nexport { LibFiles };\n// --- diagnostics --- ---\nvar DiagnosticCategory;\n(function (DiagnosticCategory) {\n    DiagnosticCategory[DiagnosticCategory[\"Warning\"] = 0] = \"Warning\";\n    DiagnosticCategory[DiagnosticCategory[\"Error\"] = 1] = \"Error\";\n    DiagnosticCategory[DiagnosticCategory[\"Suggestion\"] = 2] = \"Suggestion\";\n    DiagnosticCategory[DiagnosticCategory[\"Message\"] = 3] = \"Message\";\n})(DiagnosticCategory || (DiagnosticCategory = {}));\nvar DiagnosticsAdapter = /** @class */ (function (_super) {\n    __extends(DiagnosticsAdapter, _super);\n    function DiagnosticsAdapter(_libFiles, _defaults, _selector, worker) {\n        var _this = _super.call(this, worker) || this;\n        _this._libFiles = _libFiles;\n        _this._defaults = _defaults;\n        _this._selector = _selector;\n        _this._disposables = [];\n        _this._listener = Object.create(null);\n        var onModelAdd = function (model) {\n            if (model.getLanguageId() !== _selector) {\n                return;\n            }\n            var maybeValidate = function () {\n                var onlyVisible = _this._defaults.getDiagnosticsOptions().onlyVisible;\n                if (onlyVisible) {\n                    if (model.isAttachedToEditor()) {\n                        _this._doValidate(model);\n                    }\n                }\n                else {\n                    _this._doValidate(model);\n                }\n            };\n            var handle;\n            var changeSubscription = model.onDidChangeContent(function () {\n                clearTimeout(handle);\n                handle = window.setTimeout(maybeValidate, 500);\n            });\n            var visibleSubscription = model.onDidChangeAttached(function () {\n                var onlyVisible = _this._defaults.getDiagnosticsOptions().onlyVisible;\n                if (onlyVisible) {\n                    if (model.isAttachedToEditor()) {\n                        // this model is now attached to an editor\n                        // => compute diagnostics\n                        maybeValidate();\n                    }\n                    else {\n                        // this model is no longer attached to an editor\n                        // => clear existing diagnostics\n                        editor.setModelMarkers(model, _this._selector, []);\n                    }\n                }\n            });\n            _this._listener[model.uri.toString()] = {\n                dispose: function () {\n                    changeSubscription.dispose();\n                    visibleSubscription.dispose();\n                    clearTimeout(handle);\n                }\n            };\n            maybeValidate();\n        };\n        var onModelRemoved = function (model) {\n            editor.setModelMarkers(model, _this._selector, []);\n            var key = model.uri.toString();\n            if (_this._listener[key]) {\n                _this._listener[key].dispose();\n                delete _this._listener[key];\n            }\n        };\n        _this._disposables.push(editor.onDidCreateModel(function (model) { return onModelAdd(model); }));\n        _this._disposables.push(editor.onWillDisposeModel(onModelRemoved));\n        _this._disposables.push(editor.onDidChangeModelLanguage(function (event) {\n            onModelRemoved(event.model);\n            onModelAdd(event.model);\n        }));\n        _this._disposables.push({\n            dispose: function () {\n                for (var _i = 0, _a = editor.getModels(); _i < _a.length; _i++) {\n                    var model = _a[_i];\n                    onModelRemoved(model);\n                }\n            }\n        });\n        var recomputeDiagostics = function () {\n            // redo diagnostics when options change\n            for (var _i = 0, _a = editor.getModels(); _i < _a.length; _i++) {\n                var model = _a[_i];\n                onModelRemoved(model);\n                onModelAdd(model);\n            }\n        };\n        _this._disposables.push(_this._defaults.onDidChange(recomputeDiagostics));\n        _this._disposables.push(_this._defaults.onDidExtraLibsChange(recomputeDiagostics));\n        editor.getModels().forEach(function (model) { return onModelAdd(model); });\n        return _this;\n    }\n    DiagnosticsAdapter.prototype.dispose = function () {\n        this._disposables.forEach(function (d) { return d && d.dispose(); });\n        this._disposables = [];\n    };\n    DiagnosticsAdapter.prototype._doValidate = function (model) {\n        return __awaiter(this, void 0, void 0, function () {\n            var worker, promises, _a, noSyntaxValidation, noSemanticValidation, noSuggestionDiagnostics, allDiagnostics, diagnostics, relatedUris;\n            var _this = this;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0: return [4 /*yield*/, this._worker(model.uri)];\n                    case 1:\n                        worker = _b.sent();\n                        if (model.isDisposed()) {\n                            // model was disposed in the meantime\n                            return [2 /*return*/];\n                        }\n                        promises = [];\n                        _a = this._defaults.getDiagnosticsOptions(), noSyntaxValidation = _a.noSyntaxValidation, noSemanticValidation = _a.noSemanticValidation, noSuggestionDiagnostics = _a.noSuggestionDiagnostics;\n                        if (!noSyntaxValidation) {\n                            promises.push(worker.getSyntacticDiagnostics(model.uri.toString()));\n                        }\n                        if (!noSemanticValidation) {\n                            promises.push(worker.getSemanticDiagnostics(model.uri.toString()));\n                        }\n                        if (!noSuggestionDiagnostics) {\n                            promises.push(worker.getSuggestionDiagnostics(model.uri.toString()));\n                        }\n                        return [4 /*yield*/, Promise.all(promises)];\n                    case 2:\n                        allDiagnostics = _b.sent();\n                        if (!allDiagnostics || model.isDisposed()) {\n                            // model was disposed in the meantime\n                            return [2 /*return*/];\n                        }\n                        diagnostics = allDiagnostics\n                            .reduce(function (p, c) { return c.concat(p); }, [])\n                            .filter(function (d) {\n                            return (_this._defaults.getDiagnosticsOptions().diagnosticCodesToIgnore || []).indexOf(d.code) ===\n                                -1;\n                        });\n                        relatedUris = diagnostics\n                            .map(function (d) { return d.relatedInformation || []; })\n                            .reduce(function (p, c) { return c.concat(p); }, [])\n                            .map(function (relatedInformation) {\n                            return relatedInformation.file ? Uri.parse(relatedInformation.file.fileName) : null;\n                        });\n                        return [4 /*yield*/, this._libFiles.fetchLibFilesIfNecessary(relatedUris)];\n                    case 3:\n                        _b.sent();\n                        if (model.isDisposed()) {\n                            // model was disposed in the meantime\n                            return [2 /*return*/];\n                        }\n                        editor.setModelMarkers(model, this._selector, diagnostics.map(function (d) { return _this._convertDiagnostics(model, d); }));\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    DiagnosticsAdapter.prototype._convertDiagnostics = function (model, diag) {\n        var diagStart = diag.start || 0;\n        var diagLength = diag.length || 1;\n        var _a = model.getPositionAt(diagStart), startLineNumber = _a.lineNumber, startColumn = _a.column;\n        var _b = model.getPositionAt(diagStart + diagLength), endLineNumber = _b.lineNumber, endColumn = _b.column;\n        var tags = [];\n        if (diag.reportsUnnecessary) {\n            tags.push(MarkerTag.Unnecessary);\n        }\n        if (diag.reportsDeprecated) {\n            tags.push(MarkerTag.Deprecated);\n        }\n        return {\n            severity: this._tsDiagnosticCategoryToMarkerSeverity(diag.category),\n            startLineNumber: startLineNumber,\n            startColumn: startColumn,\n            endLineNumber: endLineNumber,\n            endColumn: endColumn,\n            message: flattenDiagnosticMessageText(diag.messageText, '\\n'),\n            code: diag.code.toString(),\n            tags: tags,\n            relatedInformation: this._convertRelatedInformation(model, diag.relatedInformation)\n        };\n    };\n    DiagnosticsAdapter.prototype._convertRelatedInformation = function (model, relatedInformation) {\n        var _this = this;\n        if (!relatedInformation) {\n            return [];\n        }\n        var result = [];\n        relatedInformation.forEach(function (info) {\n            var relatedResource = model;\n            if (info.file) {\n                relatedResource = _this._libFiles.getOrCreateModel(info.file.fileName);\n            }\n            if (!relatedResource) {\n                return;\n            }\n            var infoStart = info.start || 0;\n            var infoLength = info.length || 1;\n            var _a = relatedResource.getPositionAt(infoStart), startLineNumber = _a.lineNumber, startColumn = _a.column;\n            var _b = relatedResource.getPositionAt(infoStart + infoLength), endLineNumber = _b.lineNumber, endColumn = _b.column;\n            result.push({\n                resource: relatedResource.uri,\n                startLineNumber: startLineNumber,\n                startColumn: startColumn,\n                endLineNumber: endLineNumber,\n                endColumn: endColumn,\n                message: flattenDiagnosticMessageText(info.messageText, '\\n')\n            });\n        });\n        return result;\n    };\n    DiagnosticsAdapter.prototype._tsDiagnosticCategoryToMarkerSeverity = function (category) {\n        switch (category) {\n            case DiagnosticCategory.Error:\n                return MarkerSeverity.Error;\n            case DiagnosticCategory.Message:\n                return MarkerSeverity.Info;\n            case DiagnosticCategory.Warning:\n                return MarkerSeverity.Warning;\n            case DiagnosticCategory.Suggestion:\n                return MarkerSeverity.Hint;\n        }\n        return MarkerSeverity.Info;\n    };\n    return DiagnosticsAdapter;\n}(Adapter));\nexport { DiagnosticsAdapter };\nvar SuggestAdapter = /** @class */ (function (_super) {\n    __extends(SuggestAdapter, _super);\n    function SuggestAdapter() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(SuggestAdapter.prototype, \"triggerCharacters\", {\n        get: function () {\n            return ['.'];\n        },\n        enumerable: false,\n        configurable: true\n    });\n    SuggestAdapter.prototype.provideCompletionItems = function (model, position, _context, token) {\n        return __awaiter(this, void 0, void 0, function () {\n            var wordInfo, wordRange, resource, offset, worker, info, suggestions;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        wordInfo = model.getWordUntilPosition(position);\n                        wordRange = new Range(position.lineNumber, wordInfo.startColumn, position.lineNumber, wordInfo.endColumn);\n                        resource = model.uri;\n                        offset = model.getOffsetAt(position);\n                        return [4 /*yield*/, this._worker(resource)];\n                    case 1:\n                        worker = _a.sent();\n                        if (model.isDisposed()) {\n                            return [2 /*return*/];\n                        }\n                        return [4 /*yield*/, worker.getCompletionsAtPosition(resource.toString(), offset)];\n                    case 2:\n                        info = _a.sent();\n                        if (!info || model.isDisposed()) {\n                            return [2 /*return*/];\n                        }\n                        suggestions = info.entries.map(function (entry) {\n                            var _a;\n                            var range = wordRange;\n                            if (entry.replacementSpan) {\n                                var p1 = model.getPositionAt(entry.replacementSpan.start);\n                                var p2 = model.getPositionAt(entry.replacementSpan.start + entry.replacementSpan.length);\n                                range = new Range(p1.lineNumber, p1.column, p2.lineNumber, p2.column);\n                            }\n                            var tags = [];\n                            if (((_a = entry.kindModifiers) === null || _a === void 0 ? void 0 : _a.indexOf('deprecated')) !== -1) {\n                                tags.push(languages.CompletionItemTag.Deprecated);\n                            }\n                            return {\n                                uri: resource,\n                                position: position,\n                                offset: offset,\n                                range: range,\n                                label: entry.name,\n                                insertText: entry.name,\n                                sortText: entry.sortText,\n                                kind: SuggestAdapter.convertKind(entry.kind),\n                                tags: tags\n                            };\n                        });\n                        return [2 /*return*/, {\n                                suggestions: suggestions\n                            }];\n                }\n            });\n        });\n    };\n    SuggestAdapter.prototype.resolveCompletionItem = function (item, token) {\n        return __awaiter(this, void 0, void 0, function () {\n            var myItem, resource, position, offset, worker, details;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        myItem = item;\n                        resource = myItem.uri;\n                        position = myItem.position;\n                        offset = myItem.offset;\n                        return [4 /*yield*/, this._worker(resource)];\n                    case 1:\n                        worker = _a.sent();\n                        return [4 /*yield*/, worker.getCompletionEntryDetails(resource.toString(), offset, myItem.label)];\n                    case 2:\n                        details = _a.sent();\n                        if (!details) {\n                            return [2 /*return*/, myItem];\n                        }\n                        return [2 /*return*/, {\n                                uri: resource,\n                                position: position,\n                                label: details.name,\n                                kind: SuggestAdapter.convertKind(details.kind),\n                                detail: displayPartsToString(details.displayParts),\n                                documentation: {\n                                    value: SuggestAdapter.createDocumentationString(details)\n                                }\n                            }];\n                }\n            });\n        });\n    };\n    SuggestAdapter.convertKind = function (kind) {\n        switch (kind) {\n            case Kind.primitiveType:\n            case Kind.keyword:\n                return languages.CompletionItemKind.Keyword;\n            case Kind.variable:\n            case Kind.localVariable:\n                return languages.CompletionItemKind.Variable;\n            case Kind.memberVariable:\n            case Kind.memberGetAccessor:\n            case Kind.memberSetAccessor:\n                return languages.CompletionItemKind.Field;\n            case Kind.function:\n            case Kind.memberFunction:\n            case Kind.constructSignature:\n            case Kind.callSignature:\n            case Kind.indexSignature:\n                return languages.CompletionItemKind.Function;\n            case Kind.enum:\n                return languages.CompletionItemKind.Enum;\n            case Kind.module:\n                return languages.CompletionItemKind.Module;\n            case Kind.class:\n                return languages.CompletionItemKind.Class;\n            case Kind.interface:\n                return languages.CompletionItemKind.Interface;\n            case Kind.warning:\n                return languages.CompletionItemKind.File;\n        }\n        return languages.CompletionItemKind.Property;\n    };\n    SuggestAdapter.createDocumentationString = function (details) {\n        var documentationString = displayPartsToString(details.documentation);\n        if (details.tags) {\n            for (var _i = 0, _a = details.tags; _i < _a.length; _i++) {\n                var tag = _a[_i];\n                documentationString += \"\\n\\n\" + tagToString(tag);\n            }\n        }\n        return documentationString;\n    };\n    return SuggestAdapter;\n}(Adapter));\nexport { SuggestAdapter };\nfunction tagToString(tag) {\n    var tagLabel = \"*@\" + tag.name + \"*\";\n    if (tag.name === 'param' && tag.text) {\n        var _a = tag.text, paramName = _a[0], rest = _a.slice(1);\n        tagLabel += \"`\" + paramName.text + \"`\";\n        if (rest.length > 0)\n            tagLabel += \" \\u2014 \" + rest.map(function (r) { return r.text; }).join(' ');\n    }\n    else if (Array.isArray(tag.text)) {\n        tagLabel += \" \\u2014 \" + tag.text.map(function (r) { return r.text; }).join(' ');\n    }\n    else if (tag.text) {\n        tagLabel += \" \\u2014 \" + tag.text;\n    }\n    return tagLabel;\n}\nvar SignatureHelpAdapter = /** @class */ (function (_super) {\n    __extends(SignatureHelpAdapter, _super);\n    function SignatureHelpAdapter() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.signatureHelpTriggerCharacters = ['(', ','];\n        return _this;\n    }\n    SignatureHelpAdapter._toSignatureHelpTriggerReason = function (context) {\n        switch (context.triggerKind) {\n            case languages.SignatureHelpTriggerKind.TriggerCharacter:\n                if (context.triggerCharacter) {\n                    if (context.isRetrigger) {\n                        return { kind: 'retrigger', triggerCharacter: context.triggerCharacter };\n                    }\n                    else {\n                        return { kind: 'characterTyped', triggerCharacter: context.triggerCharacter };\n                    }\n                }\n                else {\n                    return { kind: 'invoked' };\n                }\n            case languages.SignatureHelpTriggerKind.ContentChange:\n                return context.isRetrigger ? { kind: 'retrigger' } : { kind: 'invoked' };\n            case languages.SignatureHelpTriggerKind.Invoke:\n            default:\n                return { kind: 'invoked' };\n        }\n    };\n    SignatureHelpAdapter.prototype.provideSignatureHelp = function (model, position, token, context) {\n        return __awaiter(this, void 0, void 0, function () {\n            var resource, offset, worker, info, ret;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        resource = model.uri;\n                        offset = model.getOffsetAt(position);\n                        return [4 /*yield*/, this._worker(resource)];\n                    case 1:\n                        worker = _a.sent();\n                        if (model.isDisposed()) {\n                            return [2 /*return*/];\n                        }\n                        return [4 /*yield*/, worker.getSignatureHelpItems(resource.toString(), offset, {\n                                triggerReason: SignatureHelpAdapter._toSignatureHelpTriggerReason(context)\n                            })];\n                    case 2:\n                        info = _a.sent();\n                        if (!info || model.isDisposed()) {\n                            return [2 /*return*/];\n                        }\n                        ret = {\n                            activeSignature: info.selectedItemIndex,\n                            activeParameter: info.argumentIndex,\n                            signatures: []\n                        };\n                        info.items.forEach(function (item) {\n                            var signature = {\n                                label: '',\n                                parameters: []\n                            };\n                            signature.documentation = {\n                                value: displayPartsToString(item.documentation)\n                            };\n                            signature.label += displayPartsToString(item.prefixDisplayParts);\n                            item.parameters.forEach(function (p, i, a) {\n                                var label = displayPartsToString(p.displayParts);\n                                var parameter = {\n                                    label: label,\n                                    documentation: {\n                                        value: displayPartsToString(p.documentation)\n                                    }\n                                };\n                                signature.label += label;\n                                signature.parameters.push(parameter);\n                                if (i < a.length - 1) {\n                                    signature.label += displayPartsToString(item.separatorDisplayParts);\n                                }\n                            });\n                            signature.label += displayPartsToString(item.suffixDisplayParts);\n                            ret.signatures.push(signature);\n                        });\n                        return [2 /*return*/, {\n                                value: ret,\n                                dispose: function () { }\n                            }];\n                }\n            });\n        });\n    };\n    return SignatureHelpAdapter;\n}(Adapter));\nexport { SignatureHelpAdapter };\n// --- hover ------\nvar QuickInfoAdapter = /** @class */ (function (_super) {\n    __extends(QuickInfoAdapter, _super);\n    function QuickInfoAdapter() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    QuickInfoAdapter.prototype.provideHover = function (model, position, token) {\n        return __awaiter(this, void 0, void 0, function () {\n            var resource, offset, worker, info, documentation, tags, contents;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        resource = model.uri;\n                        offset = model.getOffsetAt(position);\n                        return [4 /*yield*/, this._worker(resource)];\n                    case 1:\n                        worker = _a.sent();\n                        if (model.isDisposed()) {\n                            return [2 /*return*/];\n                        }\n                        return [4 /*yield*/, worker.getQuickInfoAtPosition(resource.toString(), offset)];\n                    case 2:\n                        info = _a.sent();\n                        if (!info || model.isDisposed()) {\n                            return [2 /*return*/];\n                        }\n                        documentation = displayPartsToString(info.documentation);\n                        tags = info.tags ? info.tags.map(function (tag) { return tagToString(tag); }).join('  \\n\\n') : '';\n                        contents = displayPartsToString(info.displayParts);\n                        return [2 /*return*/, {\n                                range: this._textSpanToRange(model, info.textSpan),\n                                contents: [\n                                    {\n                                        value: '```typescript\\n' + contents + '\\n```\\n'\n                                    },\n                                    {\n                                        value: documentation + (tags ? '\\n\\n' + tags : '')\n                                    }\n                                ]\n                            }];\n                }\n            });\n        });\n    };\n    return QuickInfoAdapter;\n}(Adapter));\nexport { QuickInfoAdapter };\n// --- occurrences ------\nvar OccurrencesAdapter = /** @class */ (function (_super) {\n    __extends(OccurrencesAdapter, _super);\n    function OccurrencesAdapter() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    OccurrencesAdapter.prototype.provideDocumentHighlights = function (model, position, token) {\n        return __awaiter(this, void 0, void 0, function () {\n            var resource, offset, worker, entries;\n            var _this = this;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        resource = model.uri;\n                        offset = model.getOffsetAt(position);\n                        return [4 /*yield*/, this._worker(resource)];\n                    case 1:\n                        worker = _a.sent();\n                        if (model.isDisposed()) {\n                            return [2 /*return*/];\n                        }\n                        return [4 /*yield*/, worker.getOccurrencesAtPosition(resource.toString(), offset)];\n                    case 2:\n                        entries = _a.sent();\n                        if (!entries || model.isDisposed()) {\n                            return [2 /*return*/];\n                        }\n                        return [2 /*return*/, entries.map(function (entry) {\n                                return {\n                                    range: _this._textSpanToRange(model, entry.textSpan),\n                                    kind: entry.isWriteAccess\n                                        ? languages.DocumentHighlightKind.Write\n                                        : languages.DocumentHighlightKind.Text\n                                };\n                            })];\n                }\n            });\n        });\n    };\n    return OccurrencesAdapter;\n}(Adapter));\nexport { OccurrencesAdapter };\n// --- definition ------\nvar DefinitionAdapter = /** @class */ (function (_super) {\n    __extends(DefinitionAdapter, _super);\n    function DefinitionAdapter(_libFiles, worker) {\n        var _this = _super.call(this, worker) || this;\n        _this._libFiles = _libFiles;\n        return _this;\n    }\n    DefinitionAdapter.prototype.provideDefinition = function (model, position, token) {\n        return __awaiter(this, void 0, void 0, function () {\n            var resource, offset, worker, entries, result, _i, entries_1, entry, refModel;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        resource = model.uri;\n                        offset = model.getOffsetAt(position);\n                        return [4 /*yield*/, this._worker(resource)];\n                    case 1:\n                        worker = _a.sent();\n                        if (model.isDisposed()) {\n                            return [2 /*return*/];\n                        }\n                        return [4 /*yield*/, worker.getDefinitionAtPosition(resource.toString(), offset)];\n                    case 2:\n                        entries = _a.sent();\n                        if (!entries || model.isDisposed()) {\n                            return [2 /*return*/];\n                        }\n                        // Fetch lib files if necessary\n                        return [4 /*yield*/, this._libFiles.fetchLibFilesIfNecessary(entries.map(function (entry) { return Uri.parse(entry.fileName); }))];\n                    case 3:\n                        // Fetch lib files if necessary\n                        _a.sent();\n                        if (model.isDisposed()) {\n                            return [2 /*return*/];\n                        }\n                        result = [];\n                        for (_i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n                            entry = entries_1[_i];\n                            refModel = this._libFiles.getOrCreateModel(entry.fileName);\n                            if (refModel) {\n                                result.push({\n                                    uri: refModel.uri,\n                                    range: this._textSpanToRange(refModel, entry.textSpan)\n                                });\n                            }\n                        }\n                        return [2 /*return*/, result];\n                }\n            });\n        });\n    };\n    return DefinitionAdapter;\n}(Adapter));\nexport { DefinitionAdapter };\n// --- references ------\nvar ReferenceAdapter = /** @class */ (function (_super) {\n    __extends(ReferenceAdapter, _super);\n    function ReferenceAdapter(_libFiles, worker) {\n        var _this = _super.call(this, worker) || this;\n        _this._libFiles = _libFiles;\n        return _this;\n    }\n    ReferenceAdapter.prototype.provideReferences = function (model, position, context, token) {\n        return __awaiter(this, void 0, void 0, function () {\n            var resource, offset, worker, entries, result, _i, entries_2, entry, refModel;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        resource = model.uri;\n                        offset = model.getOffsetAt(position);\n                        return [4 /*yield*/, this._worker(resource)];\n                    case 1:\n                        worker = _a.sent();\n                        if (model.isDisposed()) {\n                            return [2 /*return*/];\n                        }\n                        return [4 /*yield*/, worker.getReferencesAtPosition(resource.toString(), offset)];\n                    case 2:\n                        entries = _a.sent();\n                        if (!entries || model.isDisposed()) {\n                            return [2 /*return*/];\n                        }\n                        // Fetch lib files if necessary\n                        return [4 /*yield*/, this._libFiles.fetchLibFilesIfNecessary(entries.map(function (entry) { return Uri.parse(entry.fileName); }))];\n                    case 3:\n                        // Fetch lib files if necessary\n                        _a.sent();\n                        if (model.isDisposed()) {\n                            return [2 /*return*/];\n                        }\n                        result = [];\n                        for (_i = 0, entries_2 = entries; _i < entries_2.length; _i++) {\n                            entry = entries_2[_i];\n                            refModel = this._libFiles.getOrCreateModel(entry.fileName);\n                            if (refModel) {\n                                result.push({\n                                    uri: refModel.uri,\n                                    range: this._textSpanToRange(refModel, entry.textSpan)\n                                });\n                            }\n                        }\n                        return [2 /*return*/, result];\n                }\n            });\n        });\n    };\n    return ReferenceAdapter;\n}(Adapter));\nexport { ReferenceAdapter };\n// --- outline ------\nvar OutlineAdapter = /** @class */ (function (_super) {\n    __extends(OutlineAdapter, _super);\n    function OutlineAdapter() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    OutlineAdapter.prototype.provideDocumentSymbols = function (model, token) {\n        return __awaiter(this, void 0, void 0, function () {\n            var resource, worker, items, convert, result;\n            var _this = this;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        resource = model.uri;\n                        return [4 /*yield*/, this._worker(resource)];\n                    case 1:\n                        worker = _a.sent();\n                        if (model.isDisposed()) {\n                            return [2 /*return*/];\n                        }\n                        return [4 /*yield*/, worker.getNavigationBarItems(resource.toString())];\n                    case 2:\n                        items = _a.sent();\n                        if (!items || model.isDisposed()) {\n                            return [2 /*return*/];\n                        }\n                        convert = function (bucket, item, containerLabel) {\n                            var result = {\n                                name: item.text,\n                                detail: '',\n                                kind: (outlineTypeTable[item.kind] || languages.SymbolKind.Variable),\n                                range: _this._textSpanToRange(model, item.spans[0]),\n                                selectionRange: _this._textSpanToRange(model, item.spans[0]),\n                                tags: []\n                            };\n                            if (containerLabel)\n                                result.containerName = containerLabel;\n                            if (item.childItems && item.childItems.length > 0) {\n                                for (var _i = 0, _a = item.childItems; _i < _a.length; _i++) {\n                                    var child = _a[_i];\n                                    convert(bucket, child, result.name);\n                                }\n                            }\n                            bucket.push(result);\n                        };\n                        result = [];\n                        items.forEach(function (item) { return convert(result, item); });\n                        return [2 /*return*/, result];\n                }\n            });\n        });\n    };\n    return OutlineAdapter;\n}(Adapter));\nexport { OutlineAdapter };\nvar Kind = /** @class */ (function () {\n    function Kind() {\n    }\n    Kind.unknown = '';\n    Kind.keyword = 'keyword';\n    Kind.script = 'script';\n    Kind.module = 'module';\n    Kind.class = 'class';\n    Kind.interface = 'interface';\n    Kind.type = 'type';\n    Kind.enum = 'enum';\n    Kind.variable = 'var';\n    Kind.localVariable = 'local var';\n    Kind.function = 'function';\n    Kind.localFunction = 'local function';\n    Kind.memberFunction = 'method';\n    Kind.memberGetAccessor = 'getter';\n    Kind.memberSetAccessor = 'setter';\n    Kind.memberVariable = 'property';\n    Kind.constructorImplementation = 'constructor';\n    Kind.callSignature = 'call';\n    Kind.indexSignature = 'index';\n    Kind.constructSignature = 'construct';\n    Kind.parameter = 'parameter';\n    Kind.typeParameter = 'type parameter';\n    Kind.primitiveType = 'primitive type';\n    Kind.label = 'label';\n    Kind.alias = 'alias';\n    Kind.const = 'const';\n    Kind.let = 'let';\n    Kind.warning = 'warning';\n    return Kind;\n}());\nexport { Kind };\nvar outlineTypeTable = Object.create(null);\noutlineTypeTable[Kind.module] = languages.SymbolKind.Module;\noutlineTypeTable[Kind.class] = languages.SymbolKind.Class;\noutlineTypeTable[Kind.enum] = languages.SymbolKind.Enum;\noutlineTypeTable[Kind.interface] = languages.SymbolKind.Interface;\noutlineTypeTable[Kind.memberFunction] = languages.SymbolKind.Method;\noutlineTypeTable[Kind.memberVariable] = languages.SymbolKind.Property;\noutlineTypeTable[Kind.memberGetAccessor] = languages.SymbolKind.Property;\noutlineTypeTable[Kind.memberSetAccessor] = languages.SymbolKind.Property;\noutlineTypeTable[Kind.variable] = languages.SymbolKind.Variable;\noutlineTypeTable[Kind.const] = languages.SymbolKind.Variable;\noutlineTypeTable[Kind.localVariable] = languages.SymbolKind.Variable;\noutlineTypeTable[Kind.variable] = languages.SymbolKind.Variable;\noutlineTypeTable[Kind.function] = languages.SymbolKind.Function;\noutlineTypeTable[Kind.localFunction] = languages.SymbolKind.Function;\n// --- formatting ----\nvar FormatHelper = /** @class */ (function (_super) {\n    __extends(FormatHelper, _super);\n    function FormatHelper() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    FormatHelper._convertOptions = function (options) {\n        return {\n            ConvertTabsToSpaces: options.insertSpaces,\n            TabSize: options.tabSize,\n            IndentSize: options.tabSize,\n            IndentStyle: IndentStyle.Smart,\n            NewLineCharacter: '\\n',\n            InsertSpaceAfterCommaDelimiter: true,\n            InsertSpaceAfterSemicolonInForStatements: true,\n            InsertSpaceBeforeAndAfterBinaryOperators: true,\n            InsertSpaceAfterKeywordsInControlFlowStatements: true,\n            InsertSpaceAfterFunctionKeywordForAnonymousFunctions: true,\n            InsertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis: false,\n            InsertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets: false,\n            InsertSpaceAfterOpeningAndBeforeClosingTemplateStringBraces: false,\n            PlaceOpenBraceOnNewLineForControlBlocks: false,\n            PlaceOpenBraceOnNewLineForFunctions: false\n        };\n    };\n    FormatHelper.prototype._convertTextChanges = function (model, change) {\n        return {\n            text: change.newText,\n            range: this._textSpanToRange(model, change.span)\n        };\n    };\n    return FormatHelper;\n}(Adapter));\nexport { FormatHelper };\nvar FormatAdapter = /** @class */ (function (_super) {\n    __extends(FormatAdapter, _super);\n    function FormatAdapter() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    FormatAdapter.prototype.provideDocumentRangeFormattingEdits = function (model, range, options, token) {\n        return __awaiter(this, void 0, void 0, function () {\n            var resource, startOffset, endOffset, worker, edits;\n            var _this = this;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        resource = model.uri;\n                        startOffset = model.getOffsetAt({\n                            lineNumber: range.startLineNumber,\n                            column: range.startColumn\n                        });\n                        endOffset = model.getOffsetAt({\n                            lineNumber: range.endLineNumber,\n                            column: range.endColumn\n                        });\n                        return [4 /*yield*/, this._worker(resource)];\n                    case 1:\n                        worker = _a.sent();\n                        if (model.isDisposed()) {\n                            return [2 /*return*/];\n                        }\n                        return [4 /*yield*/, worker.getFormattingEditsForRange(resource.toString(), startOffset, endOffset, FormatHelper._convertOptions(options))];\n                    case 2:\n                        edits = _a.sent();\n                        if (!edits || model.isDisposed()) {\n                            return [2 /*return*/];\n                        }\n                        return [2 /*return*/, edits.map(function (edit) { return _this._convertTextChanges(model, edit); })];\n                }\n            });\n        });\n    };\n    return FormatAdapter;\n}(FormatHelper));\nexport { FormatAdapter };\nvar FormatOnTypeAdapter = /** @class */ (function (_super) {\n    __extends(FormatOnTypeAdapter, _super);\n    function FormatOnTypeAdapter() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Object.defineProperty(FormatOnTypeAdapter.prototype, \"autoFormatTriggerCharacters\", {\n        get: function () {\n            return [';', '}', '\\n'];\n        },\n        enumerable: false,\n        configurable: true\n    });\n    FormatOnTypeAdapter.prototype.provideOnTypeFormattingEdits = function (model, position, ch, options, token) {\n        return __awaiter(this, void 0, void 0, function () {\n            var resource, offset, worker, edits;\n            var _this = this;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        resource = model.uri;\n                        offset = model.getOffsetAt(position);\n                        return [4 /*yield*/, this._worker(resource)];\n                    case 1:\n                        worker = _a.sent();\n                        if (model.isDisposed()) {\n                            return [2 /*return*/];\n                        }\n                        return [4 /*yield*/, worker.getFormattingEditsAfterKeystroke(resource.toString(), offset, ch, FormatHelper._convertOptions(options))];\n                    case 2:\n                        edits = _a.sent();\n                        if (!edits || model.isDisposed()) {\n                            return [2 /*return*/];\n                        }\n                        return [2 /*return*/, edits.map(function (edit) { return _this._convertTextChanges(model, edit); })];\n                }\n            });\n        });\n    };\n    return FormatOnTypeAdapter;\n}(FormatHelper));\nexport { FormatOnTypeAdapter };\n// --- code actions ------\nvar CodeActionAdaptor = /** @class */ (function (_super) {\n    __extends(CodeActionAdaptor, _super);\n    function CodeActionAdaptor() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    CodeActionAdaptor.prototype.provideCodeActions = function (model, range, context, token) {\n        return __awaiter(this, void 0, void 0, function () {\n            var resource, start, end, formatOptions, errorCodes, worker, codeFixes, actions;\n            var _this = this;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        resource = model.uri;\n                        start = model.getOffsetAt({\n                            lineNumber: range.startLineNumber,\n                            column: range.startColumn\n                        });\n                        end = model.getOffsetAt({\n                            lineNumber: range.endLineNumber,\n                            column: range.endColumn\n                        });\n                        formatOptions = FormatHelper._convertOptions(model.getOptions());\n                        errorCodes = context.markers\n                            .filter(function (m) { return m.code; })\n                            .map(function (m) { return m.code; })\n                            .map(Number);\n                        return [4 /*yield*/, this._worker(resource)];\n                    case 1:\n                        worker = _a.sent();\n                        if (model.isDisposed()) {\n                            return [2 /*return*/];\n                        }\n                        return [4 /*yield*/, worker.getCodeFixesAtPosition(resource.toString(), start, end, errorCodes, formatOptions)];\n                    case 2:\n                        codeFixes = _a.sent();\n                        if (!codeFixes || model.isDisposed()) {\n                            return [2 /*return*/, { actions: [], dispose: function () { } }];\n                        }\n                        actions = codeFixes\n                            .filter(function (fix) {\n                            // Removes any 'make a new file'-type code fix\n                            return fix.changes.filter(function (change) { return change.isNewFile; }).length === 0;\n                        })\n                            .map(function (fix) {\n                            return _this._tsCodeFixActionToMonacoCodeAction(model, context, fix);\n                        });\n                        return [2 /*return*/, {\n                                actions: actions,\n                                dispose: function () { }\n                            }];\n                }\n            });\n        });\n    };\n    CodeActionAdaptor.prototype._tsCodeFixActionToMonacoCodeAction = function (model, context, codeFix) {\n        var edits = [];\n        for (var _i = 0, _a = codeFix.changes; _i < _a.length; _i++) {\n            var change = _a[_i];\n            for (var _b = 0, _c = change.textChanges; _b < _c.length; _b++) {\n                var textChange = _c[_b];\n                edits.push({\n                    resource: model.uri,\n                    edit: {\n                        range: this._textSpanToRange(model, textChange.span),\n                        text: textChange.newText\n                    }\n                });\n            }\n        }\n        var action = {\n            title: codeFix.description,\n            edit: { edits: edits },\n            diagnostics: context.markers,\n            kind: 'quickfix'\n        };\n        return action;\n    };\n    return CodeActionAdaptor;\n}(FormatHelper));\nexport { CodeActionAdaptor };\n// --- rename ----\nvar RenameAdapter = /** @class */ (function (_super) {\n    __extends(RenameAdapter, _super);\n    function RenameAdapter(_libFiles, worker) {\n        var _this = _super.call(this, worker) || this;\n        _this._libFiles = _libFiles;\n        return _this;\n    }\n    RenameAdapter.prototype.provideRenameEdits = function (model, position, newName, token) {\n        return __awaiter(this, void 0, void 0, function () {\n            var resource, fileName, offset, worker, renameInfo, renameLocations, edits, _i, renameLocations_1, renameLocation, model_1;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        resource = model.uri;\n                        fileName = resource.toString();\n                        offset = model.getOffsetAt(position);\n                        return [4 /*yield*/, this._worker(resource)];\n                    case 1:\n                        worker = _a.sent();\n                        if (model.isDisposed()) {\n                            return [2 /*return*/];\n                        }\n                        return [4 /*yield*/, worker.getRenameInfo(fileName, offset, {\n                                allowRenameOfImportPath: false\n                            })];\n                    case 2:\n                        renameInfo = _a.sent();\n                        if (renameInfo.canRename === false) {\n                            // use explicit comparison so that the discriminated union gets resolved properly\n                            return [2 /*return*/, {\n                                    edits: [],\n                                    rejectReason: renameInfo.localizedErrorMessage\n                                }];\n                        }\n                        if (renameInfo.fileToRename !== undefined) {\n                            throw new Error('Renaming files is not supported.');\n                        }\n                        return [4 /*yield*/, worker.findRenameLocations(fileName, offset, \n                            /*strings*/ false, \n                            /*comments*/ false, \n                            /*prefixAndSuffix*/ false)];\n                    case 3:\n                        renameLocations = _a.sent();\n                        if (!renameLocations || model.isDisposed()) {\n                            return [2 /*return*/];\n                        }\n                        edits = [];\n                        for (_i = 0, renameLocations_1 = renameLocations; _i < renameLocations_1.length; _i++) {\n                            renameLocation = renameLocations_1[_i];\n                            model_1 = this._libFiles.getOrCreateModel(renameLocation.fileName);\n                            if (model_1) {\n                                edits.push({\n                                    resource: model_1.uri,\n                                    edit: {\n                                        range: this._textSpanToRange(model_1, renameLocation.textSpan),\n                                        text: newName\n                                    }\n                                });\n                            }\n                            else {\n                                throw new Error(\"Unknown file \" + renameLocation.fileName + \".\");\n                            }\n                        }\n                        return [2 /*return*/, { edits: edits }];\n                }\n            });\n        });\n    };\n    return RenameAdapter;\n}(Adapter));\nexport { RenameAdapter };\n// --- inlay hints ----\nvar InlayHintsAdapter = /** @class */ (function (_super) {\n    __extends(InlayHintsAdapter, _super);\n    function InlayHintsAdapter() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    InlayHintsAdapter.prototype.provideInlayHints = function (model, range, token) {\n        return __awaiter(this, void 0, void 0, function () {\n            var resource, fileName, start, end, worker, hints;\n            var _this = this;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        resource = model.uri;\n                        fileName = resource.toString();\n                        start = model.getOffsetAt({\n                            lineNumber: range.startLineNumber,\n                            column: range.startColumn\n                        });\n                        end = model.getOffsetAt({\n                            lineNumber: range.endLineNumber,\n                            column: range.endColumn\n                        });\n                        return [4 /*yield*/, this._worker(resource)];\n                    case 1:\n                        worker = _a.sent();\n                        if (model.isDisposed()) {\n                            return [2 /*return*/, []];\n                        }\n                        return [4 /*yield*/, worker.provideInlayHints(fileName, start, end)];\n                    case 2:\n                        hints = _a.sent();\n                        return [2 /*return*/, hints.map(function (hint) {\n                                return __assign(__assign({}, hint), { position: model.getPositionAt(hint.position), kind: _this._convertHintKind(hint.kind) });\n                            })];\n                }\n            });\n        });\n    };\n    InlayHintsAdapter.prototype._convertHintKind = function (kind) {\n        switch (kind) {\n            case 'Parameter':\n                return languages.InlayHintKind.Parameter;\n            case 'Type':\n                return languages.InlayHintKind.Type;\n            default:\n                return languages.InlayHintKind.Other;\n        }\n    };\n    return InlayHintsAdapter;\n}(Adapter));\nexport { InlayHintsAdapter };\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n'use strict';\nimport { WorkerManager } from './workerManager.js';\nimport * as languageFeatures from './languageFeatures.js';\nimport { languages } from './fillers/monaco-editor-core.js';\nvar javaScriptWorker;\nvar typeScriptWorker;\nexport function setupTypeScript(defaults) {\n    typeScriptWorker = setupMode(defaults, 'typescript');\n}\nexport function setupJavaScript(defaults) {\n    javaScriptWorker = setupMode(defaults, 'javascript');\n}\nexport function getJavaScriptWorker() {\n    return new Promise(function (resolve, reject) {\n        if (!javaScriptWorker) {\n            return reject('JavaScript not registered!');\n        }\n        resolve(javaScriptWorker);\n    });\n}\nexport function getTypeScriptWorker() {\n    return new Promise(function (resolve, reject) {\n        if (!typeScriptWorker) {\n            return reject('TypeScript not registered!');\n        }\n        resolve(typeScriptWorker);\n    });\n}\nfunction setupMode(defaults, modeId) {\n    var client = new WorkerManager(modeId, defaults);\n    var worker = function () {\n        var uris = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            uris[_i] = arguments[_i];\n        }\n        return client.getLanguageServiceWorker.apply(client, uris);\n    };\n    var libFiles = new languageFeatures.LibFiles(worker);\n    languages.registerCompletionItemProvider(modeId, new languageFeatures.SuggestAdapter(worker));\n    languages.registerSignatureHelpProvider(modeId, new languageFeatures.SignatureHelpAdapter(worker));\n    languages.registerHoverProvider(modeId, new languageFeatures.QuickInfoAdapter(worker));\n    languages.registerDocumentHighlightProvider(modeId, new languageFeatures.OccurrencesAdapter(worker));\n    languages.registerDefinitionProvider(modeId, new languageFeatures.DefinitionAdapter(libFiles, worker));\n    languages.registerReferenceProvider(modeId, new languageFeatures.ReferenceAdapter(libFiles, worker));\n    languages.registerDocumentSymbolProvider(modeId, new languageFeatures.OutlineAdapter(worker));\n    languages.registerDocumentRangeFormattingEditProvider(modeId, new languageFeatures.FormatAdapter(worker));\n    languages.registerOnTypeFormattingEditProvider(modeId, new languageFeatures.FormatOnTypeAdapter(worker));\n    languages.registerCodeActionProvider(modeId, new languageFeatures.CodeActionAdaptor(worker));\n    languages.registerRenameProvider(modeId, new languageFeatures.RenameAdapter(libFiles, worker));\n    languages.registerInlayHintsProvider(modeId, new languageFeatures.InlayHintsAdapter(worker));\n    new languageFeatures.DiagnosticsAdapter(libFiles, defaults, modeId, worker);\n    return worker;\n}\n"], "mappings": ";;;;;;;;;;;;;;AAKA,IAAI,YAAwC,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AACA,IAAI,cAA4C,SAAU,SAAS,MAAM;AACrE,MAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,QAAI,EAAE,CAAC,IAAI;AAAG,YAAM,EAAE,CAAC;AAAG,WAAO,EAAE,CAAC;AAAA,EAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG;AAC/G,SAAO,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,WAAO;AAAA,EAAM,IAAI;AACvJ,WAAS,KAAK,GAAG;AAAE,WAAO,SAAU,GAAG;AAAE,aAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,IAAG;AAAA,EAAG;AACjE,WAAS,KAAK,IAAI;AACd,QAAI;AAAG,YAAM,IAAI,UAAU,iCAAiC;AAC5D,WAAO;AAAG,UAAI;AACV,YAAI,IAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG;AAAM,iBAAO;AAC3J,YAAI,IAAI,GAAG;AAAG,eAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,gBAAQ,GAAG,CAAC,GAAG;AAAA,UACX,KAAK;AAAA,UAAG,KAAK;AAAG,gBAAI;AAAI;AAAA,UACxB,KAAK;AAAG,cAAE;AAAS,mBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,UACtD,KAAK;AAAG,cAAE;AAAS,gBAAI,GAAG,CAAC;AAAG,iBAAK,CAAC,CAAC;AAAG;AAAA,UACxC,KAAK;AAAG,iBAAK,EAAE,IAAI,IAAI;AAAG,cAAE,KAAK,IAAI;AAAG;AAAA,UACxC;AACI,gBAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,kBAAI;AAAG;AAAA,YAAU;AAC3G,gBAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,gBAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,YAAO;AACrF,gBAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,gBAAE,QAAQ,EAAE,CAAC;AAAG,kBAAI;AAAI;AAAA,YAAO;AACpE,gBAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,gBAAE,QAAQ,EAAE,CAAC;AAAG,gBAAE,IAAI,KAAK,EAAE;AAAG;AAAA,YAAO;AAClE,gBAAI,EAAE,CAAC;AAAG,gBAAE,IAAI,IAAI;AACpB,cAAE,KAAK,IAAI;AAAG;AAAA,QACtB;AACA,aAAK,KAAK,KAAK,SAAS,CAAC;AAAA,MAC7B,SAAS,GAAG;AAAE,aAAK,CAAC,GAAG,CAAC;AAAG,YAAI;AAAA,MAAG,UAAE;AAAU,YAAI,IAAI;AAAA,MAAG;AACzD,QAAI,GAAG,CAAC,IAAI;AAAG,YAAM,GAAG,CAAC;AAAG,WAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,EACnF;AACJ;AAEA,IAAI;AAAA;AAAA,EAA+B,WAAY;AAC3C,aAASA,eAAc,QAAQ,UAAU;AACrC,UAAI,QAAQ;AACZ,WAAK,UAAU;AACf,WAAK,YAAY;AACjB,WAAK,UAAU;AACf,WAAK,UAAU;AACf,WAAK,wBAAwB,KAAK,UAAU,YAAY,WAAY;AAAE,eAAO,MAAM,YAAY;AAAA,MAAG,CAAC;AACnG,WAAK,wBAAwB;AAC7B,WAAK,2BAA2B,KAAK,UAAU,qBAAqB,WAAY;AAC5E,eAAO,MAAM,iBAAiB;AAAA,MAClC,CAAC;AAAA,IACL;AACA,IAAAA,eAAc,UAAU,cAAc,WAAY;AAC9C,UAAI,KAAK,SAAS;AACd,aAAK,QAAQ,QAAQ;AACrB,aAAK,UAAU;AAAA,MACnB;AACA,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,eAAc,UAAU,UAAU,WAAY;AAC1C,WAAK,sBAAsB,QAAQ;AACnC,WAAK,yBAAyB,QAAQ;AACtC,WAAK,YAAY;AAAA,IACrB;AACA,IAAAA,eAAc,UAAU,mBAAmB,WAAY;AACnD,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,SAAS;AACb,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,SAAS;AACf,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,wBAAU,EAAE,KAAK;AACjB,qBAAO,CAAC,GAAa,KAAK,QAAQ,SAAS,CAAC;AAAA,YAChD,KAAK;AACD,sBAAQ,GAAG,KAAK;AAChB,kBAAI,KAAK,0BAA0B,SAAS;AAExC,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,oBAAM,gBAAgB,KAAK,UAAU,aAAa,CAAC;AACnD,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,eAAc,UAAU,aAAa,WAAY;AAC7C,UAAI,QAAQ;AACZ,UAAI,CAAC,KAAK,SAAS;AACf,aAAK,UAAU,OAAO,gBAAgB;AAAA;AAAA,UAElC,UAAU;AAAA,UACV,OAAO,KAAK;AAAA,UACZ,gBAAgB;AAAA;AAAA,UAEhB,YAAY;AAAA,YACR,iBAAiB,KAAK,UAAU,mBAAmB;AAAA,YACnD,WAAW,KAAK,UAAU,aAAa;AAAA,YACvC,kBAAkB,KAAK,UAAU,cAAc;AAAA,YAC/C,mBAAmB,KAAK,UAAU;AAAA,UACtC;AAAA,QACJ,CAAC;AACD,YAAI,IAAI,KAAK,QAAQ,SAAS;AAC9B,YAAI,KAAK,UAAU,kBAAkB,GAAG;AACpC,cAAI,EAAE,KAAK,SAAU,QAAQ;AACzB,gBAAI,MAAM,SAAS;AACf,qBAAO,MAAM,QAAQ,oBAAoB,OACpC,UAAU,EACV,OAAO,SAAU,OAAO;AAAE,uBAAO,MAAM,cAAc,MAAM,MAAM;AAAA,cAAS,CAAC,EAC3E,IAAI,SAAU,OAAO;AAAE,uBAAO,MAAM;AAAA,cAAK,CAAC,CAAC;AAAA,YACpD;AACA,mBAAO;AAAA,UACX,CAAC;AAAA,QACL;AACA,aAAK,UAAU;AAAA,MACnB;AACA,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,eAAc,UAAU,2BAA2B,WAAY;AAC3D,UAAI,QAAQ;AACZ,UAAI,YAAY,CAAC;AACjB,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,kBAAU,EAAE,IAAI,UAAU,EAAE;AAAA,MAChC;AACA,UAAI;AACJ,aAAO,KAAK,WAAW,EAClB,KAAK,SAAU,QAAQ;AACxB,kBAAU;AAAA,MACd,CAAC,EACI,KAAK,SAAU,GAAG;AACnB,YAAI,MAAM,SAAS;AACf,iBAAO,MAAM,QAAQ,oBAAoB,SAAS;AAAA,QACtD;AAAA,MACJ,CAAC,EACI,KAAK,SAAU,GAAG;AAAE,eAAO;AAAA,MAAS,CAAC;AAAA,IAC9C;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;;;ACrIK,IAAI,aAAa,CAAC;AACzB,WAAW,UAAU,IAAI;AACzB,WAAW,cAAc,IAAI;AAC7B,WAAW,uBAAuB,IAAI;AACtC,WAAW,4BAA4B,IAAI;AAC3C,WAAW,sBAAsB,IAAI;AACrC,WAAW,iBAAiB,IAAI;AAChC,WAAW,2BAA2B,IAAI;AAC1C,WAAW,0BAA0B,IAAI;AACzC,WAAW,yBAAyB,IAAI;AACxC,WAAW,uBAAuB,IAAI;AACtC,WAAW,yBAAyB,IAAI;AACxC,WAAW,wBAAwB,IAAI;AACvC,WAAW,kCAAkC,IAAI;AACjD,WAAW,+BAA+B,IAAI;AAC9C,WAAW,iBAAiB,IAAI;AAChC,WAAW,sBAAsB,IAAI;AACrC,WAAW,iBAAiB,IAAI;AAChC,WAAW,sBAAsB,IAAI;AACrC,WAAW,sBAAsB,IAAI;AACrC,WAAW,wBAAwB,IAAI;AACvC,WAAW,8BAA8B,IAAI;AAC7C,WAAW,wBAAwB,IAAI;AACvC,WAAW,6BAA6B,IAAI;AAC5C,WAAW,gCAAgC,IAAI;AAC/C,WAAW,+BAA+B,IAAI;AAC9C,WAAW,iBAAiB,IAAI;AAChC,WAAW,sBAAsB,IAAI;AACrC,WAAW,sBAAsB,IAAI;AACrC,WAAW,yBAAyB,IAAI;AACxC,WAAW,wBAAwB,IAAI;AACvC,WAAW,uBAAuB,IAAI;AACtC,WAAW,iBAAiB,IAAI;AAChC,WAAW,sBAAsB,IAAI;AACrC,WAAW,wBAAwB,IAAI;AACvC,WAAW,wBAAwB,IAAI;AACvC,WAAW,wBAAwB,IAAI;AACvC,WAAW,wBAAwB,IAAI;AACvC,WAAW,iBAAiB,IAAI;AAChC,WAAW,sBAAsB,IAAI;AACrC,WAAW,sBAAsB,IAAI;AACrC,WAAW,yBAAyB,IAAI;AACxC,WAAW,8BAA8B,IAAI;AAC7C,WAAW,wBAAwB,IAAI;AACvC,WAAW,kCAAkC,IAAI;AACjD,WAAW,iBAAiB,IAAI;AAChC,WAAW,sBAAsB,IAAI;AACrC,WAAW,yBAAyB,IAAI;AACxC,WAAW,wBAAwB,IAAI;AACvC,WAAW,yBAAyB,IAAI;AACxC,WAAW,cAAc,IAAI;AAC7B,WAAW,cAAc,IAAI;AAC7B,WAAW,iBAAiB,IAAI;AAChC,WAAW,sBAAsB,IAAI;AACrC,WAAW,sBAAsB,IAAI;AACrC,WAAW,yBAAyB,IAAI;AACxC,WAAW,wBAAwB,IAAI;AACvC,WAAW,yBAAyB,IAAI;AACxC,WAAW,qBAAqB,IAAI;AACpC,WAAW,oBAAoB,IAAI;AACnC,WAAW,kCAAkC,IAAI;AACjD,WAAW,6BAA6B,IAAI;;;AChE5C,IAAI,YAAyC,2BAAY;AACrD,MAAI,gBAAgB,SAAU,GAAG,GAAG;AAChC,oBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,MAAAD,GAAE,YAAYC;AAAA,IAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,eAAS,KAAKA;AAAG,YAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC;AAAG,UAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,IAAG;AACpG,WAAO,cAAc,GAAG,CAAC;AAAA,EAC7B;AACA,SAAO,SAAU,GAAG,GAAG;AACnB,QAAI,OAAO,MAAM,cAAc,MAAM;AACjC,YAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,kBAAc,GAAG,CAAC;AAClB,aAAS,KAAK;AAAE,WAAK,cAAc;AAAA,IAAG;AACtC,MAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AAAA,EACtF;AACJ,EAAG;AACH,IAAI,WAAsC,WAAY;AAClD,aAAW,OAAO,UAAU,SAAS,GAAG;AACpC,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK;AAAG,YAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,YAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACzC;AACA,IAAIC,aAAwC,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AACA,IAAIC,eAA4C,SAAU,SAAS,MAAM;AACrE,MAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,QAAI,EAAE,CAAC,IAAI;AAAG,YAAM,EAAE,CAAC;AAAG,WAAO,EAAE,CAAC;AAAA,EAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG;AAC/G,SAAO,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,WAAO;AAAA,EAAM,IAAI;AACvJ,WAAS,KAAK,GAAG;AAAE,WAAO,SAAU,GAAG;AAAE,aAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,IAAG;AAAA,EAAG;AACjE,WAAS,KAAK,IAAI;AACd,QAAI;AAAG,YAAM,IAAI,UAAU,iCAAiC;AAC5D,WAAO;AAAG,UAAI;AACV,YAAI,IAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG;AAAM,iBAAO;AAC3J,YAAI,IAAI,GAAG;AAAG,eAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,gBAAQ,GAAG,CAAC,GAAG;AAAA,UACX,KAAK;AAAA,UAAG,KAAK;AAAG,gBAAI;AAAI;AAAA,UACxB,KAAK;AAAG,cAAE;AAAS,mBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,UACtD,KAAK;AAAG,cAAE;AAAS,gBAAI,GAAG,CAAC;AAAG,iBAAK,CAAC,CAAC;AAAG;AAAA,UACxC,KAAK;AAAG,iBAAK,EAAE,IAAI,IAAI;AAAG,cAAE,KAAK,IAAI;AAAG;AAAA,UACxC;AACI,gBAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,kBAAI;AAAG;AAAA,YAAU;AAC3G,gBAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,gBAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,YAAO;AACrF,gBAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,gBAAE,QAAQ,EAAE,CAAC;AAAG,kBAAI;AAAI;AAAA,YAAO;AACpE,gBAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,gBAAE,QAAQ,EAAE,CAAC;AAAG,gBAAE,IAAI,KAAK,EAAE;AAAG;AAAA,YAAO;AAClE,gBAAI,EAAE,CAAC;AAAG,gBAAE,IAAI,IAAI;AACpB,cAAE,KAAK,IAAI;AAAG;AAAA,QACtB;AACA,aAAK,KAAK,KAAK,SAAS,CAAC;AAAA,MAC7B,SAAS,GAAG;AAAE,aAAK,CAAC,GAAG,CAAC;AAAG,YAAI;AAAA,MAAG,UAAE;AAAU,YAAI,IAAI;AAAA,MAAG;AACzD,QAAI,GAAG,CAAC,IAAI;AAAG,YAAM,GAAG,CAAC;AAAG,WAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,EACnF;AACJ;AAKA,IAAI;AAAA,CACH,SAAUC,cAAa;AACpB,EAAAA,aAAYA,aAAY,MAAM,IAAI,CAAC,IAAI;AACvC,EAAAA,aAAYA,aAAY,OAAO,IAAI,CAAC,IAAI;AACxC,EAAAA,aAAYA,aAAY,OAAO,IAAI,CAAC,IAAI;AAC5C,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAC7B,SAAS,6BAA6B,MAAM,SAAS,QAAQ;AAChE,MAAI,WAAW,QAAQ;AAAE,aAAS;AAAA,EAAG;AACrC,MAAI,OAAO,SAAS,UAAU;AAC1B,WAAO;AAAA,EACX,WACS,SAAS,QAAW;AACzB,WAAO;AAAA,EACX;AACA,MAAI,SAAS;AACb,MAAI,QAAQ;AACR,cAAU;AACV,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,gBAAU;AAAA,IACd;AAAA,EACJ;AACA,YAAU,KAAK;AACf;AACA,MAAI,KAAK,MAAM;AACX,aAAS,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK,GAAG,QAAQ,MAAM;AACnD,UAAI,MAAM,GAAG,EAAE;AACf,gBAAU,6BAA6B,KAAK,SAAS,MAAM;AAAA,IAC/D;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,qBAAqB,cAAc;AACxC,MAAI,cAAc;AACd,WAAO,aAAa,IAAI,SAAU,aAAa;AAAE,aAAO,YAAY;AAAA,IAAM,CAAC,EAAE,KAAK,EAAE;AAAA,EACxF;AACA,SAAO;AACX;AAEA,IAAI;AAAA;AAAA,EAAyB,WAAY;AACrC,aAASC,SAAQ,SAAS;AACtB,WAAK,UAAU;AAAA,IACnB;AAOA,IAAAA,SAAQ,UAAU,mBAAmB,SAAU,OAAO,MAAM;AACxD,UAAI,KAAK,MAAM,cAAc,KAAK,KAAK;AACvC,UAAI,KAAK,MAAM,cAAc,KAAK,QAAQ,KAAK,MAAM;AACrD,UAAI,kBAAkB,GAAG,YAAY,cAAc,GAAG;AACtD,UAAI,gBAAgB,GAAG,YAAY,YAAY,GAAG;AAClD,aAAO,EAAE,iBAAkC,aAA0B,eAA8B,UAAqB;AAAA,IAC5H;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAGF,IAAI;AAAA;AAAA,EAA0B,WAAY;AACtC,aAASC,UAAS,SAAS;AACvB,WAAK,UAAU;AACf,WAAK,YAAY,CAAC;AAClB,WAAK,sBAAsB;AAC3B,WAAK,wBAAwB;AAAA,IACjC;AACA,IAAAA,UAAS,UAAU,YAAY,SAAU,KAAK;AAC1C,UAAI,CAAC,KAAK;AACN,eAAO;AAAA,MACX;AACA,UAAI,IAAI,KAAK,QAAQ,OAAO,MAAM,GAAG;AACjC,eAAO,CAAC,CAAC,WAAW,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,MACzC;AACA,aAAO;AAAA,IACX;AACA,IAAAA,UAAS,UAAU,mBAAmB,SAAU,UAAU;AACtD,UAAI,MAAM,IAAI,MAAM,QAAQ;AAC5B,UAAI,QAAQ,OAAO,SAAS,GAAG;AAC/B,UAAI,OAAO;AACP,eAAO;AAAA,MACX;AACA,UAAI,KAAK,UAAU,GAAG,KAAK,KAAK,qBAAqB;AACjD,eAAO,OAAO,YAAY,KAAK,UAAU,IAAI,KAAK,MAAM,CAAC,CAAC,GAAG,cAAc,GAAG;AAAA,MAClF;AACA,UAAI,iBAAiB,mBAAmB,aAAa,EAAE,QAAQ;AAC/D,UAAI,gBAAgB;AAChB,eAAO,OAAO,YAAY,eAAe,SAAS,cAAc,GAAG;AAAA,MACvE;AACA,aAAO;AAAA,IACX;AACA,IAAAA,UAAS,UAAU,mBAAmB,SAAU,MAAM;AAClD,eAAS,KAAK,GAAG,SAAS,MAAM,KAAK,OAAO,QAAQ,MAAM;AACtD,YAAI,MAAM,OAAO,EAAE;AACnB,YAAI,KAAK,UAAU,GAAG,GAAG;AACrB,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,IAAAA,UAAS,UAAU,2BAA2B,SAAU,MAAM;AAC1D,aAAOC,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,kBAAI,CAAC,KAAK,iBAAiB,IAAI,GAAG;AAE9B,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,qBAAO,CAAC,GAAa,KAAK,eAAe,CAAC;AAAA,YAC9C,KAAK;AACD,iBAAG,KAAK;AACR,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAF,UAAS,UAAU,iBAAiB,WAAY;AAC5C,UAAI,QAAQ;AACZ,UAAI,CAAC,KAAK,uBAAuB;AAC7B,aAAK,wBAAwB,KAAK,QAAQ,EACrC,KAAK,SAAU,GAAG;AAAE,iBAAO,EAAE,YAAY;AAAA,QAAG,CAAC,EAC7C,KAAK,SAAU,UAAU;AAC1B,gBAAM,sBAAsB;AAC5B,gBAAM,YAAY;AAAA,QACtB,CAAC;AAAA,MACL;AACA,aAAO,KAAK;AAAA,IAChB;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAGF,IAAI;AAAA,CACH,SAAUG,qBAAoB;AAC3B,EAAAA,oBAAmBA,oBAAmB,SAAS,IAAI,CAAC,IAAI;AACxD,EAAAA,oBAAmBA,oBAAmB,OAAO,IAAI,CAAC,IAAI;AACtD,EAAAA,oBAAmBA,oBAAmB,YAAY,IAAI,CAAC,IAAI;AAC3D,EAAAA,oBAAmBA,oBAAmB,SAAS,IAAI,CAAC,IAAI;AAC5D,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAClD,IAAI;AAAA;AAAA,EAAoC,SAAU,QAAQ;AACtD,cAAUC,qBAAoB,MAAM;AACpC,aAASA,oBAAmB,WAAW,WAAW,WAAW,QAAQ;AACjE,UAAI,QAAQ,OAAO,KAAK,MAAM,MAAM,KAAK;AACzC,YAAM,YAAY;AAClB,YAAM,YAAY;AAClB,YAAM,YAAY;AAClB,YAAM,eAAe,CAAC;AACtB,YAAM,YAAY,uBAAO,OAAO,IAAI;AACpC,UAAI,aAAa,SAAU,OAAO;AAC9B,YAAI,MAAM,cAAc,MAAM,WAAW;AACrC;AAAA,QACJ;AACA,YAAI,gBAAgB,WAAY;AAC5B,cAAI,cAAc,MAAM,UAAU,sBAAsB,EAAE;AAC1D,cAAI,aAAa;AACb,gBAAI,MAAM,mBAAmB,GAAG;AAC5B,oBAAM,YAAY,KAAK;AAAA,YAC3B;AAAA,UACJ,OACK;AACD,kBAAM,YAAY,KAAK;AAAA,UAC3B;AAAA,QACJ;AACA,YAAI;AACJ,YAAI,qBAAqB,MAAM,mBAAmB,WAAY;AAC1D,uBAAa,MAAM;AACnB,mBAAS,OAAO,WAAW,eAAe,GAAG;AAAA,QACjD,CAAC;AACD,YAAI,sBAAsB,MAAM,oBAAoB,WAAY;AAC5D,cAAI,cAAc,MAAM,UAAU,sBAAsB,EAAE;AAC1D,cAAI,aAAa;AACb,gBAAI,MAAM,mBAAmB,GAAG;AAG5B,4BAAc;AAAA,YAClB,OACK;AAGD,qBAAO,gBAAgB,OAAO,MAAM,WAAW,CAAC,CAAC;AAAA,YACrD;AAAA,UACJ;AAAA,QACJ,CAAC;AACD,cAAM,UAAU,MAAM,IAAI,SAAS,CAAC,IAAI;AAAA,UACpC,SAAS,WAAY;AACjB,+BAAmB,QAAQ;AAC3B,gCAAoB,QAAQ;AAC5B,yBAAa,MAAM;AAAA,UACvB;AAAA,QACJ;AACA,sBAAc;AAAA,MAClB;AACA,UAAI,iBAAiB,SAAU,OAAO;AAClC,eAAO,gBAAgB,OAAO,MAAM,WAAW,CAAC,CAAC;AACjD,YAAI,MAAM,MAAM,IAAI,SAAS;AAC7B,YAAI,MAAM,UAAU,GAAG,GAAG;AACtB,gBAAM,UAAU,GAAG,EAAE,QAAQ;AAC7B,iBAAO,MAAM,UAAU,GAAG;AAAA,QAC9B;AAAA,MACJ;AACA,YAAM,aAAa,KAAK,OAAO,iBAAiB,SAAU,OAAO;AAAE,eAAO,WAAW,KAAK;AAAA,MAAG,CAAC,CAAC;AAC/F,YAAM,aAAa,KAAK,OAAO,mBAAmB,cAAc,CAAC;AACjE,YAAM,aAAa,KAAK,OAAO,yBAAyB,SAAU,OAAO;AACrE,uBAAe,MAAM,KAAK;AAC1B,mBAAW,MAAM,KAAK;AAAA,MAC1B,CAAC,CAAC;AACF,YAAM,aAAa,KAAK;AAAA,QACpB,SAAS,WAAY;AACjB,mBAAS,KAAK,GAAG,KAAK,OAAO,UAAU,GAAG,KAAK,GAAG,QAAQ,MAAM;AAC5D,gBAAI,QAAQ,GAAG,EAAE;AACjB,2BAAe,KAAK;AAAA,UACxB;AAAA,QACJ;AAAA,MACJ,CAAC;AACD,UAAI,sBAAsB,WAAY;AAElC,iBAAS,KAAK,GAAG,KAAK,OAAO,UAAU,GAAG,KAAK,GAAG,QAAQ,MAAM;AAC5D,cAAI,QAAQ,GAAG,EAAE;AACjB,yBAAe,KAAK;AACpB,qBAAW,KAAK;AAAA,QACpB;AAAA,MACJ;AACA,YAAM,aAAa,KAAK,MAAM,UAAU,YAAY,mBAAmB,CAAC;AACxE,YAAM,aAAa,KAAK,MAAM,UAAU,qBAAqB,mBAAmB,CAAC;AACjF,aAAO,UAAU,EAAE,QAAQ,SAAU,OAAO;AAAE,eAAO,WAAW,KAAK;AAAA,MAAG,CAAC;AACzE,aAAO;AAAA,IACX;AACA,IAAAA,oBAAmB,UAAU,UAAU,WAAY;AAC/C,WAAK,aAAa,QAAQ,SAAU,GAAG;AAAE,eAAO,KAAK,EAAE,QAAQ;AAAA,MAAG,CAAC;AACnE,WAAK,eAAe,CAAC;AAAA,IACzB;AACA,IAAAA,oBAAmB,UAAU,cAAc,SAAU,OAAO;AACxD,aAAOC,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,QAAQ,UAAU,IAAI,oBAAoB,sBAAsB,yBAAyB,gBAAgB,aAAa;AAC1H,YAAI,QAAQ;AACZ,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AAAG,qBAAO,CAAC,GAAa,KAAK,QAAQ,MAAM,GAAG,CAAC;AAAA,YACpD,KAAK;AACD,uBAAS,GAAG,KAAK;AACjB,kBAAI,MAAM,WAAW,GAAG;AAEpB,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,yBAAW,CAAC;AACZ,mBAAK,KAAK,UAAU,sBAAsB,GAAG,qBAAqB,GAAG,oBAAoB,uBAAuB,GAAG,sBAAsB,0BAA0B,GAAG;AACtK,kBAAI,CAAC,oBAAoB;AACrB,yBAAS,KAAK,OAAO,wBAAwB,MAAM,IAAI,SAAS,CAAC,CAAC;AAAA,cACtE;AACA,kBAAI,CAAC,sBAAsB;AACvB,yBAAS,KAAK,OAAO,uBAAuB,MAAM,IAAI,SAAS,CAAC,CAAC;AAAA,cACrE;AACA,kBAAI,CAAC,yBAAyB;AAC1B,yBAAS,KAAK,OAAO,yBAAyB,MAAM,IAAI,SAAS,CAAC,CAAC;AAAA,cACvE;AACA,qBAAO,CAAC,GAAa,QAAQ,IAAI,QAAQ,CAAC;AAAA,YAC9C,KAAK;AACD,+BAAiB,GAAG,KAAK;AACzB,kBAAI,CAAC,kBAAkB,MAAM,WAAW,GAAG;AAEvC,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,4BAAc,eACT,OAAO,SAAU,GAAG,GAAG;AAAE,uBAAO,EAAE,OAAO,CAAC;AAAA,cAAG,GAAG,CAAC,CAAC,EAClD,OAAO,SAAU,GAAG;AACrB,wBAAQ,MAAM,UAAU,sBAAsB,EAAE,2BAA2B,CAAC,GAAG,QAAQ,EAAE,IAAI,MACzF;AAAA,cACR,CAAC;AACD,4BAAc,YACT,IAAI,SAAU,GAAG;AAAE,uBAAO,EAAE,sBAAsB,CAAC;AAAA,cAAG,CAAC,EACvD,OAAO,SAAU,GAAG,GAAG;AAAE,uBAAO,EAAE,OAAO,CAAC;AAAA,cAAG,GAAG,CAAC,CAAC,EAClD,IAAI,SAAU,oBAAoB;AACnC,uBAAO,mBAAmB,OAAO,IAAI,MAAM,mBAAmB,KAAK,QAAQ,IAAI;AAAA,cACnF,CAAC;AACD,qBAAO,CAAC,GAAa,KAAK,UAAU,yBAAyB,WAAW,CAAC;AAAA,YAC7E,KAAK;AACD,iBAAG,KAAK;AACR,kBAAI,MAAM,WAAW,GAAG;AAEpB,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,qBAAO,gBAAgB,OAAO,KAAK,WAAW,YAAY,IAAI,SAAU,GAAG;AAAE,uBAAO,MAAM,oBAAoB,OAAO,CAAC;AAAA,cAAG,CAAC,CAAC;AAC3H,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAF,oBAAmB,UAAU,sBAAsB,SAAU,OAAO,MAAM;AACtE,UAAI,YAAY,KAAK,SAAS;AAC9B,UAAI,aAAa,KAAK,UAAU;AAChC,UAAI,KAAK,MAAM,cAAc,SAAS,GAAG,kBAAkB,GAAG,YAAY,cAAc,GAAG;AAC3F,UAAI,KAAK,MAAM,cAAc,YAAY,UAAU,GAAG,gBAAgB,GAAG,YAAY,YAAY,GAAG;AACpG,UAAI,OAAO,CAAC;AACZ,UAAI,KAAK,oBAAoB;AACzB,aAAK,KAAK,UAAU,WAAW;AAAA,MACnC;AACA,UAAI,KAAK,mBAAmB;AACxB,aAAK,KAAK,UAAU,UAAU;AAAA,MAClC;AACA,aAAO;AAAA,QACH,UAAU,KAAK,sCAAsC,KAAK,QAAQ;AAAA,QAClE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS,6BAA6B,KAAK,aAAa,IAAI;AAAA,QAC5D,MAAM,KAAK,KAAK,SAAS;AAAA,QACzB;AAAA,QACA,oBAAoB,KAAK,2BAA2B,OAAO,KAAK,kBAAkB;AAAA,MACtF;AAAA,IACJ;AACA,IAAAA,oBAAmB,UAAU,6BAA6B,SAAU,OAAO,oBAAoB;AAC3F,UAAI,QAAQ;AACZ,UAAI,CAAC,oBAAoB;AACrB,eAAO,CAAC;AAAA,MACZ;AACA,UAAI,SAAS,CAAC;AACd,yBAAmB,QAAQ,SAAU,MAAM;AACvC,YAAI,kBAAkB;AACtB,YAAI,KAAK,MAAM;AACX,4BAAkB,MAAM,UAAU,iBAAiB,KAAK,KAAK,QAAQ;AAAA,QACzE;AACA,YAAI,CAAC,iBAAiB;AAClB;AAAA,QACJ;AACA,YAAI,YAAY,KAAK,SAAS;AAC9B,YAAI,aAAa,KAAK,UAAU;AAChC,YAAI,KAAK,gBAAgB,cAAc,SAAS,GAAG,kBAAkB,GAAG,YAAY,cAAc,GAAG;AACrG,YAAI,KAAK,gBAAgB,cAAc,YAAY,UAAU,GAAG,gBAAgB,GAAG,YAAY,YAAY,GAAG;AAC9G,eAAO,KAAK;AAAA,UACR,UAAU,gBAAgB;AAAA,UAC1B;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAS,6BAA6B,KAAK,aAAa,IAAI;AAAA,QAChE,CAAC;AAAA,MACL,CAAC;AACD,aAAO;AAAA,IACX;AACA,IAAAA,oBAAmB,UAAU,wCAAwC,SAAU,UAAU;AACrF,cAAQ,UAAU;AAAA,QACd,KAAK,mBAAmB;AACpB,iBAAO,eAAe;AAAA,QAC1B,KAAK,mBAAmB;AACpB,iBAAO,eAAe;AAAA,QAC1B,KAAK,mBAAmB;AACpB,iBAAO,eAAe;AAAA,QAC1B,KAAK,mBAAmB;AACpB,iBAAO,eAAe;AAAA,MAC9B;AACA,aAAO,eAAe;AAAA,IAC1B;AACA,WAAOA;AAAA,EACX,EAAE,OAAO;AAAA;AAET,IAAI;AAAA;AAAA,EAAgC,SAAU,QAAQ;AAClD,cAAUG,iBAAgB,MAAM;AAChC,aAASA,kBAAiB;AACtB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,WAAO,eAAeA,gBAAe,WAAW,qBAAqB;AAAA,MACjE,KAAK,WAAY;AACb,eAAO,CAAC,GAAG;AAAA,MACf;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,gBAAe,UAAU,yBAAyB,SAAU,OAAO,UAAU,UAAU,OAAO;AAC1F,aAAOC,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,UAAU,WAAW,UAAU,QAAQ,QAAQ,MAAM;AACzD,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,yBAAW,MAAM,qBAAqB,QAAQ;AAC9C,0BAAY,IAAI,MAAM,SAAS,YAAY,SAAS,aAAa,SAAS,YAAY,SAAS,SAAS;AACxG,yBAAW,MAAM;AACjB,uBAAS,MAAM,YAAY,QAAQ;AACnC,qBAAO,CAAC,GAAa,KAAK,QAAQ,QAAQ,CAAC;AAAA,YAC/C,KAAK;AACD,uBAAS,GAAG,KAAK;AACjB,kBAAI,MAAM,WAAW,GAAG;AACpB,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,qBAAO,CAAC,GAAa,OAAO,yBAAyB,SAAS,SAAS,GAAG,MAAM,CAAC;AAAA,YACrF,KAAK;AACD,qBAAO,GAAG,KAAK;AACf,kBAAI,CAAC,QAAQ,MAAM,WAAW,GAAG;AAC7B,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,4BAAc,KAAK,QAAQ,IAAI,SAAU,OAAO;AAC5C,oBAAIC;AACJ,oBAAI,QAAQ;AACZ,oBAAI,MAAM,iBAAiB;AACvB,sBAAI,KAAK,MAAM,cAAc,MAAM,gBAAgB,KAAK;AACxD,sBAAI,KAAK,MAAM,cAAc,MAAM,gBAAgB,QAAQ,MAAM,gBAAgB,MAAM;AACvF,0BAAQ,IAAI,MAAM,GAAG,YAAY,GAAG,QAAQ,GAAG,YAAY,GAAG,MAAM;AAAA,gBACxE;AACA,oBAAI,OAAO,CAAC;AACZ,sBAAMA,MAAK,MAAM,mBAAmB,QAAQA,QAAO,SAAS,SAASA,IAAG,QAAQ,YAAY,OAAO,IAAI;AACnG,uBAAK,KAAK,UAAU,kBAAkB,UAAU;AAAA,gBACpD;AACA,uBAAO;AAAA,kBACH,KAAK;AAAA,kBACL;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA,OAAO,MAAM;AAAA,kBACb,YAAY,MAAM;AAAA,kBAClB,UAAU,MAAM;AAAA,kBAChB,MAAMH,gBAAe,YAAY,MAAM,IAAI;AAAA,kBAC3C;AAAA,gBACJ;AAAA,cACJ,CAAC;AACD,qBAAO,CAAC,GAAc;AAAA,gBACd;AAAA,cACJ,CAAC;AAAA,UACb;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,gBAAe,UAAU,wBAAwB,SAAU,MAAM,OAAO;AACpE,aAAOC,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,QAAQ,UAAU,UAAU,QAAQ,QAAQ;AAChD,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,uBAAS;AACT,yBAAW,OAAO;AAClB,yBAAW,OAAO;AAClB,uBAAS,OAAO;AAChB,qBAAO,CAAC,GAAa,KAAK,QAAQ,QAAQ,CAAC;AAAA,YAC/C,KAAK;AACD,uBAAS,GAAG,KAAK;AACjB,qBAAO,CAAC,GAAa,OAAO,0BAA0B,SAAS,SAAS,GAAG,QAAQ,OAAO,KAAK,CAAC;AAAA,YACpG,KAAK;AACD,wBAAU,GAAG,KAAK;AAClB,kBAAI,CAAC,SAAS;AACV,uBAAO,CAAC,GAAc,MAAM;AAAA,cAChC;AACA,qBAAO,CAAC,GAAc;AAAA,gBACd,KAAK;AAAA,gBACL;AAAA,gBACA,OAAO,QAAQ;AAAA,gBACf,MAAMF,gBAAe,YAAY,QAAQ,IAAI;AAAA,gBAC7C,QAAQ,qBAAqB,QAAQ,YAAY;AAAA,gBACjD,eAAe;AAAA,kBACX,OAAOA,gBAAe,0BAA0B,OAAO;AAAA,gBAC3D;AAAA,cACJ,CAAC;AAAA,UACb;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,gBAAe,cAAc,SAAU,MAAM;AACzC,cAAQ,MAAM;AAAA,QACV,KAAK,KAAK;AAAA,QACV,KAAK,KAAK;AACN,iBAAO,UAAU,mBAAmB;AAAA,QACxC,KAAK,KAAK;AAAA,QACV,KAAK,KAAK;AACN,iBAAO,UAAU,mBAAmB;AAAA,QACxC,KAAK,KAAK;AAAA,QACV,KAAK,KAAK;AAAA,QACV,KAAK,KAAK;AACN,iBAAO,UAAU,mBAAmB;AAAA,QACxC,KAAK,KAAK;AAAA,QACV,KAAK,KAAK;AAAA,QACV,KAAK,KAAK;AAAA,QACV,KAAK,KAAK;AAAA,QACV,KAAK,KAAK;AACN,iBAAO,UAAU,mBAAmB;AAAA,QACxC,KAAK,KAAK;AACN,iBAAO,UAAU,mBAAmB;AAAA,QACxC,KAAK,KAAK;AACN,iBAAO,UAAU,mBAAmB;AAAA,QACxC,KAAK,KAAK;AACN,iBAAO,UAAU,mBAAmB;AAAA,QACxC,KAAK,KAAK;AACN,iBAAO,UAAU,mBAAmB;AAAA,QACxC,KAAK,KAAK;AACN,iBAAO,UAAU,mBAAmB;AAAA,MAC5C;AACA,aAAO,UAAU,mBAAmB;AAAA,IACxC;AACA,IAAAA,gBAAe,4BAA4B,SAAU,SAAS;AAC1D,UAAI,sBAAsB,qBAAqB,QAAQ,aAAa;AACpE,UAAI,QAAQ,MAAM;AACd,iBAAS,KAAK,GAAG,KAAK,QAAQ,MAAM,KAAK,GAAG,QAAQ,MAAM;AACtD,cAAI,MAAM,GAAG,EAAE;AACf,iCAAuB,SAAS,YAAY,GAAG;AAAA,QACnD;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE,OAAO;AAAA;AAET,SAAS,YAAY,KAAK;AACtB,MAAI,WAAW,OAAO,IAAI,OAAO;AACjC,MAAI,IAAI,SAAS,WAAW,IAAI,MAAM;AAClC,QAAI,KAAK,IAAI,MAAM,YAAY,GAAG,CAAC,GAAG,OAAO,GAAG,MAAM,CAAC;AACvD,gBAAY,MAAM,UAAU,OAAO;AACnC,QAAI,KAAK,SAAS;AACd,kBAAY,QAAa,KAAK,IAAI,SAAU,GAAG;AAAE,eAAO,EAAE;AAAA,MAAM,CAAC,EAAE,KAAK,GAAG;AAAA,EACnF,WACS,MAAM,QAAQ,IAAI,IAAI,GAAG;AAC9B,gBAAY,QAAa,IAAI,KAAK,IAAI,SAAU,GAAG;AAAE,aAAO,EAAE;AAAA,IAAM,CAAC,EAAE,KAAK,GAAG;AAAA,EACnF,WACS,IAAI,MAAM;AACf,gBAAY,QAAa,IAAI;AAAA,EACjC;AACA,SAAO;AACX;AACA,IAAI;AAAA;AAAA,EAAsC,SAAU,QAAQ;AACxD,cAAUI,uBAAsB,MAAM;AACtC,aAASA,wBAAuB;AAC5B,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,iCAAiC,CAAC,KAAK,GAAG;AAChD,aAAO;AAAA,IACX;AACA,IAAAA,sBAAqB,gCAAgC,SAAU,SAAS;AACpE,cAAQ,QAAQ,aAAa;AAAA,QACzB,KAAK,UAAU,yBAAyB;AACpC,cAAI,QAAQ,kBAAkB;AAC1B,gBAAI,QAAQ,aAAa;AACrB,qBAAO,EAAE,MAAM,aAAa,kBAAkB,QAAQ,iBAAiB;AAAA,YAC3E,OACK;AACD,qBAAO,EAAE,MAAM,kBAAkB,kBAAkB,QAAQ,iBAAiB;AAAA,YAChF;AAAA,UACJ,OACK;AACD,mBAAO,EAAE,MAAM,UAAU;AAAA,UAC7B;AAAA,QACJ,KAAK,UAAU,yBAAyB;AACpC,iBAAO,QAAQ,cAAc,EAAE,MAAM,YAAY,IAAI,EAAE,MAAM,UAAU;AAAA,QAC3E,KAAK,UAAU,yBAAyB;AAAA,QACxC;AACI,iBAAO,EAAE,MAAM,UAAU;AAAA,MACjC;AAAA,IACJ;AACA,IAAAA,sBAAqB,UAAU,uBAAuB,SAAU,OAAO,UAAU,OAAO,SAAS;AAC7F,aAAOC,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,UAAU,QAAQ,QAAQ,MAAM;AACpC,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,yBAAW,MAAM;AACjB,uBAAS,MAAM,YAAY,QAAQ;AACnC,qBAAO,CAAC,GAAa,KAAK,QAAQ,QAAQ,CAAC;AAAA,YAC/C,KAAK;AACD,uBAAS,GAAG,KAAK;AACjB,kBAAI,MAAM,WAAW,GAAG;AACpB,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,qBAAO,CAAC,GAAa,OAAO,sBAAsB,SAAS,SAAS,GAAG,QAAQ;AAAA,gBACvE,eAAeF,sBAAqB,8BAA8B,OAAO;AAAA,cAC7E,CAAC,CAAC;AAAA,YACV,KAAK;AACD,qBAAO,GAAG,KAAK;AACf,kBAAI,CAAC,QAAQ,MAAM,WAAW,GAAG;AAC7B,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,oBAAM;AAAA,gBACF,iBAAiB,KAAK;AAAA,gBACtB,iBAAiB,KAAK;AAAA,gBACtB,YAAY,CAAC;AAAA,cACjB;AACA,mBAAK,MAAM,QAAQ,SAAU,MAAM;AAC/B,oBAAI,YAAY;AAAA,kBACZ,OAAO;AAAA,kBACP,YAAY,CAAC;AAAA,gBACjB;AACA,0BAAU,gBAAgB;AAAA,kBACtB,OAAO,qBAAqB,KAAK,aAAa;AAAA,gBAClD;AACA,0BAAU,SAAS,qBAAqB,KAAK,kBAAkB;AAC/D,qBAAK,WAAW,QAAQ,SAAU,GAAG,GAAG,GAAG;AACvC,sBAAI,QAAQ,qBAAqB,EAAE,YAAY;AAC/C,sBAAI,YAAY;AAAA,oBACZ;AAAA,oBACA,eAAe;AAAA,sBACX,OAAO,qBAAqB,EAAE,aAAa;AAAA,oBAC/C;AAAA,kBACJ;AACA,4BAAU,SAAS;AACnB,4BAAU,WAAW,KAAK,SAAS;AACnC,sBAAI,IAAI,EAAE,SAAS,GAAG;AAClB,8BAAU,SAAS,qBAAqB,KAAK,qBAAqB;AAAA,kBACtE;AAAA,gBACJ,CAAC;AACD,0BAAU,SAAS,qBAAqB,KAAK,kBAAkB;AAC/D,oBAAI,WAAW,KAAK,SAAS;AAAA,cACjC,CAAC;AACD,qBAAO,CAAC,GAAc;AAAA,gBACd,OAAO;AAAA,gBACP,SAAS,WAAY;AAAA,gBAAE;AAAA,cAC3B,CAAC;AAAA,UACb;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOA;AAAA,EACX,EAAE,OAAO;AAAA;AAGT,IAAI;AAAA;AAAA,EAAkC,SAAU,QAAQ;AACpD,cAAUG,mBAAkB,MAAM;AAClC,aAASA,oBAAmB;AACxB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,kBAAiB,UAAU,eAAe,SAAU,OAAO,UAAU,OAAO;AACxE,aAAOC,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,UAAU,QAAQ,QAAQ,MAAM,eAAe,MAAM;AACzD,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,yBAAW,MAAM;AACjB,uBAAS,MAAM,YAAY,QAAQ;AACnC,qBAAO,CAAC,GAAa,KAAK,QAAQ,QAAQ,CAAC;AAAA,YAC/C,KAAK;AACD,uBAAS,GAAG,KAAK;AACjB,kBAAI,MAAM,WAAW,GAAG;AACpB,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,qBAAO,CAAC,GAAa,OAAO,uBAAuB,SAAS,SAAS,GAAG,MAAM,CAAC;AAAA,YACnF,KAAK;AACD,qBAAO,GAAG,KAAK;AACf,kBAAI,CAAC,QAAQ,MAAM,WAAW,GAAG;AAC7B,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,8BAAgB,qBAAqB,KAAK,aAAa;AACvD,qBAAO,KAAK,OAAO,KAAK,KAAK,IAAI,SAAU,KAAK;AAAE,uBAAO,YAAY,GAAG;AAAA,cAAG,CAAC,EAAE,KAAK,QAAQ,IAAI;AAC/F,yBAAW,qBAAqB,KAAK,YAAY;AACjD,qBAAO,CAAC,GAAc;AAAA,gBACd,OAAO,KAAK,iBAAiB,OAAO,KAAK,QAAQ;AAAA,gBACjD,UAAU;AAAA,kBACN;AAAA,oBACI,OAAO,oBAAoB,WAAW;AAAA,kBAC1C;AAAA,kBACA;AAAA,oBACI,OAAO,iBAAiB,OAAO,SAAS,OAAO;AAAA,kBACnD;AAAA,gBACJ;AAAA,cACJ,CAAC;AAAA,UACb;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOF;AAAA,EACX,EAAE,OAAO;AAAA;AAGT,IAAI;AAAA;AAAA,EAAoC,SAAU,QAAQ;AACtD,cAAUG,qBAAoB,MAAM;AACpC,aAASA,sBAAqB;AAC1B,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,oBAAmB,UAAU,4BAA4B,SAAU,OAAO,UAAU,OAAO;AACvF,aAAOC,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,UAAU,QAAQ,QAAQ;AAC9B,YAAI,QAAQ;AACZ,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,yBAAW,MAAM;AACjB,uBAAS,MAAM,YAAY,QAAQ;AACnC,qBAAO,CAAC,GAAa,KAAK,QAAQ,QAAQ,CAAC;AAAA,YAC/C,KAAK;AACD,uBAAS,GAAG,KAAK;AACjB,kBAAI,MAAM,WAAW,GAAG;AACpB,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,qBAAO,CAAC,GAAa,OAAO,yBAAyB,SAAS,SAAS,GAAG,MAAM,CAAC;AAAA,YACrF,KAAK;AACD,wBAAU,GAAG,KAAK;AAClB,kBAAI,CAAC,WAAW,MAAM,WAAW,GAAG;AAChC,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,qBAAO,CAAC,GAAc,QAAQ,IAAI,SAAU,OAAO;AAC3C,uBAAO;AAAA,kBACH,OAAO,MAAM,iBAAiB,OAAO,MAAM,QAAQ;AAAA,kBACnD,MAAM,MAAM,gBACN,UAAU,sBAAsB,QAChC,UAAU,sBAAsB;AAAA,gBAC1C;AAAA,cACJ,CAAC,CAAC;AAAA,UACd;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOF;AAAA,EACX,EAAE,OAAO;AAAA;AAGT,IAAI;AAAA;AAAA,EAAmC,SAAU,QAAQ;AACrD,cAAUG,oBAAmB,MAAM;AACnC,aAASA,mBAAkB,WAAW,QAAQ;AAC1C,UAAI,QAAQ,OAAO,KAAK,MAAM,MAAM,KAAK;AACzC,YAAM,YAAY;AAClB,aAAO;AAAA,IACX;AACA,IAAAA,mBAAkB,UAAU,oBAAoB,SAAU,OAAO,UAAU,OAAO;AAC9E,aAAOC,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,UAAU,QAAQ,QAAQ,SAAS,QAAQ,IAAI,WAAW,OAAO;AACrE,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,yBAAW,MAAM;AACjB,uBAAS,MAAM,YAAY,QAAQ;AACnC,qBAAO,CAAC,GAAa,KAAK,QAAQ,QAAQ,CAAC;AAAA,YAC/C,KAAK;AACD,uBAAS,GAAG,KAAK;AACjB,kBAAI,MAAM,WAAW,GAAG;AACpB,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,qBAAO,CAAC,GAAa,OAAO,wBAAwB,SAAS,SAAS,GAAG,MAAM,CAAC;AAAA,YACpF,KAAK;AACD,wBAAU,GAAG,KAAK;AAClB,kBAAI,CAAC,WAAW,MAAM,WAAW,GAAG;AAChC,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AAEA,qBAAO,CAAC,GAAa,KAAK,UAAU,yBAAyB,QAAQ,IAAI,SAAUC,QAAO;AAAE,uBAAO,IAAI,MAAMA,OAAM,QAAQ;AAAA,cAAG,CAAC,CAAC,CAAC;AAAA,YACrI,KAAK;AAED,iBAAG,KAAK;AACR,kBAAI,MAAM,WAAW,GAAG;AACpB,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,uBAAS,CAAC;AACV,mBAAK,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,QAAQ,MAAM;AAC3D,wBAAQ,UAAU,EAAE;AACpB,2BAAW,KAAK,UAAU,iBAAiB,MAAM,QAAQ;AACzD,oBAAI,UAAU;AACV,yBAAO,KAAK;AAAA,oBACR,KAAK,SAAS;AAAA,oBACd,OAAO,KAAK,iBAAiB,UAAU,MAAM,QAAQ;AAAA,kBACzD,CAAC;AAAA,gBACL;AAAA,cACJ;AACA,qBAAO,CAAC,GAAc,MAAM;AAAA,UACpC;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOH;AAAA,EACX,EAAE,OAAO;AAAA;AAGT,IAAI;AAAA;AAAA,EAAkC,SAAU,QAAQ;AACpD,cAAUI,mBAAkB,MAAM;AAClC,aAASA,kBAAiB,WAAW,QAAQ;AACzC,UAAI,QAAQ,OAAO,KAAK,MAAM,MAAM,KAAK;AACzC,YAAM,YAAY;AAClB,aAAO;AAAA,IACX;AACA,IAAAA,kBAAiB,UAAU,oBAAoB,SAAU,OAAO,UAAU,SAAS,OAAO;AACtF,aAAOC,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,UAAU,QAAQ,QAAQ,SAAS,QAAQ,IAAI,WAAW,OAAO;AACrE,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,yBAAW,MAAM;AACjB,uBAAS,MAAM,YAAY,QAAQ;AACnC,qBAAO,CAAC,GAAa,KAAK,QAAQ,QAAQ,CAAC;AAAA,YAC/C,KAAK;AACD,uBAAS,GAAG,KAAK;AACjB,kBAAI,MAAM,WAAW,GAAG;AACpB,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,qBAAO,CAAC,GAAa,OAAO,wBAAwB,SAAS,SAAS,GAAG,MAAM,CAAC;AAAA,YACpF,KAAK;AACD,wBAAU,GAAG,KAAK;AAClB,kBAAI,CAAC,WAAW,MAAM,WAAW,GAAG;AAChC,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AAEA,qBAAO,CAAC,GAAa,KAAK,UAAU,yBAAyB,QAAQ,IAAI,SAAUC,QAAO;AAAE,uBAAO,IAAI,MAAMA,OAAM,QAAQ;AAAA,cAAG,CAAC,CAAC,CAAC;AAAA,YACrI,KAAK;AAED,iBAAG,KAAK;AACR,kBAAI,MAAM,WAAW,GAAG;AACpB,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,uBAAS,CAAC;AACV,mBAAK,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,QAAQ,MAAM;AAC3D,wBAAQ,UAAU,EAAE;AACpB,2BAAW,KAAK,UAAU,iBAAiB,MAAM,QAAQ;AACzD,oBAAI,UAAU;AACV,yBAAO,KAAK;AAAA,oBACR,KAAK,SAAS;AAAA,oBACd,OAAO,KAAK,iBAAiB,UAAU,MAAM,QAAQ;AAAA,kBACzD,CAAC;AAAA,gBACL;AAAA,cACJ;AACA,qBAAO,CAAC,GAAc,MAAM;AAAA,UACpC;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOH;AAAA,EACX,EAAE,OAAO;AAAA;AAGT,IAAI;AAAA;AAAA,EAAgC,SAAU,QAAQ;AAClD,cAAUI,iBAAgB,MAAM;AAChC,aAASA,kBAAiB;AACtB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,gBAAe,UAAU,yBAAyB,SAAU,OAAO,OAAO;AACtE,aAAOC,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,UAAU,QAAQ,OAAO,SAAS;AACtC,YAAI,QAAQ;AACZ,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,yBAAW,MAAM;AACjB,qBAAO,CAAC,GAAa,KAAK,QAAQ,QAAQ,CAAC;AAAA,YAC/C,KAAK;AACD,uBAAS,GAAG,KAAK;AACjB,kBAAI,MAAM,WAAW,GAAG;AACpB,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,qBAAO,CAAC,GAAa,OAAO,sBAAsB,SAAS,SAAS,CAAC,CAAC;AAAA,YAC1E,KAAK;AACD,sBAAQ,GAAG,KAAK;AAChB,kBAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAC9B,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,wBAAU,SAAU,QAAQ,MAAM,gBAAgB;AAC9C,oBAAIC,UAAS;AAAA,kBACT,MAAM,KAAK;AAAA,kBACX,QAAQ;AAAA,kBACR,MAAO,iBAAiB,KAAK,IAAI,KAAK,UAAU,WAAW;AAAA,kBAC3D,OAAO,MAAM,iBAAiB,OAAO,KAAK,MAAM,CAAC,CAAC;AAAA,kBAClD,gBAAgB,MAAM,iBAAiB,OAAO,KAAK,MAAM,CAAC,CAAC;AAAA,kBAC3D,MAAM,CAAC;AAAA,gBACX;AACA,oBAAI;AACA,kBAAAA,QAAO,gBAAgB;AAC3B,oBAAI,KAAK,cAAc,KAAK,WAAW,SAAS,GAAG;AAC/C,2BAAS,KAAK,GAAGC,MAAK,KAAK,YAAY,KAAKA,IAAG,QAAQ,MAAM;AACzD,wBAAI,QAAQA,IAAG,EAAE;AACjB,4BAAQ,QAAQ,OAAOD,QAAO,IAAI;AAAA,kBACtC;AAAA,gBACJ;AACA,uBAAO,KAAKA,OAAM;AAAA,cACtB;AACA,uBAAS,CAAC;AACV,oBAAM,QAAQ,SAAU,MAAM;AAAE,uBAAO,QAAQ,QAAQ,IAAI;AAAA,cAAG,CAAC;AAC/D,qBAAO,CAAC,GAAc,MAAM;AAAA,UACpC;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOH;AAAA,EACX,EAAE,OAAO;AAAA;AAET,IAAI;AAAA;AAAA,EAAsB,WAAY;AAClC,aAASK,QAAO;AAAA,IAChB;AACA,IAAAA,MAAK,UAAU;AACf,IAAAA,MAAK,UAAU;AACf,IAAAA,MAAK,SAAS;AACd,IAAAA,MAAK,SAAS;AACd,IAAAA,MAAK,QAAQ;AACb,IAAAA,MAAK,YAAY;AACjB,IAAAA,MAAK,OAAO;AACZ,IAAAA,MAAK,OAAO;AACZ,IAAAA,MAAK,WAAW;AAChB,IAAAA,MAAK,gBAAgB;AACrB,IAAAA,MAAK,WAAW;AAChB,IAAAA,MAAK,gBAAgB;AACrB,IAAAA,MAAK,iBAAiB;AACtB,IAAAA,MAAK,oBAAoB;AACzB,IAAAA,MAAK,oBAAoB;AACzB,IAAAA,MAAK,iBAAiB;AACtB,IAAAA,MAAK,4BAA4B;AACjC,IAAAA,MAAK,gBAAgB;AACrB,IAAAA,MAAK,iBAAiB;AACtB,IAAAA,MAAK,qBAAqB;AAC1B,IAAAA,MAAK,YAAY;AACjB,IAAAA,MAAK,gBAAgB;AACrB,IAAAA,MAAK,gBAAgB;AACrB,IAAAA,MAAK,QAAQ;AACb,IAAAA,MAAK,QAAQ;AACb,IAAAA,MAAK,QAAQ;AACb,IAAAA,MAAK,MAAM;AACX,IAAAA,MAAK,UAAU;AACf,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,IAAI,mBAAmB,uBAAO,OAAO,IAAI;AACzC,iBAAiB,KAAK,MAAM,IAAI,UAAU,WAAW;AACrD,iBAAiB,KAAK,KAAK,IAAI,UAAU,WAAW;AACpD,iBAAiB,KAAK,IAAI,IAAI,UAAU,WAAW;AACnD,iBAAiB,KAAK,SAAS,IAAI,UAAU,WAAW;AACxD,iBAAiB,KAAK,cAAc,IAAI,UAAU,WAAW;AAC7D,iBAAiB,KAAK,cAAc,IAAI,UAAU,WAAW;AAC7D,iBAAiB,KAAK,iBAAiB,IAAI,UAAU,WAAW;AAChE,iBAAiB,KAAK,iBAAiB,IAAI,UAAU,WAAW;AAChE,iBAAiB,KAAK,QAAQ,IAAI,UAAU,WAAW;AACvD,iBAAiB,KAAK,KAAK,IAAI,UAAU,WAAW;AACpD,iBAAiB,KAAK,aAAa,IAAI,UAAU,WAAW;AAC5D,iBAAiB,KAAK,QAAQ,IAAI,UAAU,WAAW;AACvD,iBAAiB,KAAK,QAAQ,IAAI,UAAU,WAAW;AACvD,iBAAiB,KAAK,aAAa,IAAI,UAAU,WAAW;AAE5D,IAAI;AAAA;AAAA,EAA8B,SAAU,QAAQ;AAChD,cAAUC,eAAc,MAAM;AAC9B,aAASA,gBAAe;AACpB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,cAAa,kBAAkB,SAAU,SAAS;AAC9C,aAAO;AAAA,QACH,qBAAqB,QAAQ;AAAA,QAC7B,SAAS,QAAQ;AAAA,QACjB,YAAY,QAAQ;AAAA,QACpB,aAAa,YAAY;AAAA,QACzB,kBAAkB;AAAA,QAClB,gCAAgC;AAAA,QAChC,0CAA0C;AAAA,QAC1C,0CAA0C;AAAA,QAC1C,iDAAiD;AAAA,QACjD,sDAAsD;AAAA,QACtD,4DAA4D;AAAA,QAC5D,yDAAyD;AAAA,QACzD,6DAA6D;AAAA,QAC7D,yCAAyC;AAAA,QACzC,qCAAqC;AAAA,MACzC;AAAA,IACJ;AACA,IAAAA,cAAa,UAAU,sBAAsB,SAAU,OAAO,QAAQ;AAClE,aAAO;AAAA,QACH,MAAM,OAAO;AAAA,QACb,OAAO,KAAK,iBAAiB,OAAO,OAAO,IAAI;AAAA,MACnD;AAAA,IACJ;AACA,WAAOA;AAAA,EACX,EAAE,OAAO;AAAA;AAET,IAAI;AAAA;AAAA,EAA+B,SAAU,QAAQ;AACjD,cAAUC,gBAAe,MAAM;AAC/B,aAASA,iBAAgB;AACrB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,eAAc,UAAU,sCAAsC,SAAU,OAAO,OAAO,SAAS,OAAO;AAClG,aAAOC,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,UAAU,aAAa,WAAW,QAAQ;AAC9C,YAAI,QAAQ;AACZ,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,yBAAW,MAAM;AACjB,4BAAc,MAAM,YAAY;AAAA,gBAC5B,YAAY,MAAM;AAAA,gBAClB,QAAQ,MAAM;AAAA,cAClB,CAAC;AACD,0BAAY,MAAM,YAAY;AAAA,gBAC1B,YAAY,MAAM;AAAA,gBAClB,QAAQ,MAAM;AAAA,cAClB,CAAC;AACD,qBAAO,CAAC,GAAa,KAAK,QAAQ,QAAQ,CAAC;AAAA,YAC/C,KAAK;AACD,uBAAS,GAAG,KAAK;AACjB,kBAAI,MAAM,WAAW,GAAG;AACpB,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,qBAAO,CAAC,GAAa,OAAO,2BAA2B,SAAS,SAAS,GAAG,aAAa,WAAW,aAAa,gBAAgB,OAAO,CAAC,CAAC;AAAA,YAC9I,KAAK;AACD,sBAAQ,GAAG,KAAK;AAChB,kBAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAC9B,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,qBAAO,CAAC,GAAc,MAAM,IAAI,SAAU,MAAM;AAAE,uBAAO,MAAM,oBAAoB,OAAO,IAAI;AAAA,cAAG,CAAC,CAAC;AAAA,UAC3G;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOF;AAAA,EACX,EAAE,YAAY;AAAA;AAEd,IAAI;AAAA;AAAA,EAAqC,SAAU,QAAQ;AACvD,cAAUG,sBAAqB,MAAM;AACrC,aAASA,uBAAsB;AAC3B,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,WAAO,eAAeA,qBAAoB,WAAW,+BAA+B;AAAA,MAChF,KAAK,WAAY;AACb,eAAO,CAAC,KAAK,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,qBAAoB,UAAU,+BAA+B,SAAU,OAAO,UAAU,IAAI,SAAS,OAAO;AACxG,aAAOC,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,UAAU,QAAQ,QAAQ;AAC9B,YAAI,QAAQ;AACZ,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,yBAAW,MAAM;AACjB,uBAAS,MAAM,YAAY,QAAQ;AACnC,qBAAO,CAAC,GAAa,KAAK,QAAQ,QAAQ,CAAC;AAAA,YAC/C,KAAK;AACD,uBAAS,GAAG,KAAK;AACjB,kBAAI,MAAM,WAAW,GAAG;AACpB,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,qBAAO,CAAC,GAAa,OAAO,iCAAiC,SAAS,SAAS,GAAG,QAAQ,IAAI,aAAa,gBAAgB,OAAO,CAAC,CAAC;AAAA,YACxI,KAAK;AACD,sBAAQ,GAAG,KAAK;AAChB,kBAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAC9B,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,qBAAO,CAAC,GAAc,MAAM,IAAI,SAAU,MAAM;AAAE,uBAAO,MAAM,oBAAoB,OAAO,IAAI;AAAA,cAAG,CAAC,CAAC;AAAA,UAC3G;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOF;AAAA,EACX,EAAE,YAAY;AAAA;AAGd,IAAI;AAAA;AAAA,EAAmC,SAAU,QAAQ;AACrD,cAAUG,oBAAmB,MAAM;AACnC,aAASA,qBAAoB;AACzB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,mBAAkB,UAAU,qBAAqB,SAAU,OAAO,OAAO,SAAS,OAAO;AACrF,aAAOC,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,UAAU,OAAO,KAAK,eAAe,YAAY,QAAQ,WAAW;AACxE,YAAI,QAAQ;AACZ,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,yBAAW,MAAM;AACjB,sBAAQ,MAAM,YAAY;AAAA,gBACtB,YAAY,MAAM;AAAA,gBAClB,QAAQ,MAAM;AAAA,cAClB,CAAC;AACD,oBAAM,MAAM,YAAY;AAAA,gBACpB,YAAY,MAAM;AAAA,gBAClB,QAAQ,MAAM;AAAA,cAClB,CAAC;AACD,8BAAgB,aAAa,gBAAgB,MAAM,WAAW,CAAC;AAC/D,2BAAa,QAAQ,QAChB,OAAO,SAAU,GAAG;AAAE,uBAAO,EAAE;AAAA,cAAM,CAAC,EACtC,IAAI,SAAU,GAAG;AAAE,uBAAO,EAAE;AAAA,cAAM,CAAC,EACnC,IAAI,MAAM;AACf,qBAAO,CAAC,GAAa,KAAK,QAAQ,QAAQ,CAAC;AAAA,YAC/C,KAAK;AACD,uBAAS,GAAG,KAAK;AACjB,kBAAI,MAAM,WAAW,GAAG;AACpB,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,qBAAO,CAAC,GAAa,OAAO,uBAAuB,SAAS,SAAS,GAAG,OAAO,KAAK,YAAY,aAAa,CAAC;AAAA,YAClH,KAAK;AACD,0BAAY,GAAG,KAAK;AACpB,kBAAI,CAAC,aAAa,MAAM,WAAW,GAAG;AAClC,uBAAO,CAAC,GAAc,EAAE,SAAS,CAAC,GAAG,SAAS,WAAY;AAAA,gBAAE,EAAE,CAAC;AAAA,cACnE;AACA,wBAAU,UACL,OAAO,SAAU,KAAK;AAEvB,uBAAO,IAAI,QAAQ,OAAO,SAAU,QAAQ;AAAE,yBAAO,OAAO;AAAA,gBAAW,CAAC,EAAE,WAAW;AAAA,cACzF,CAAC,EACI,IAAI,SAAU,KAAK;AACpB,uBAAO,MAAM,mCAAmC,OAAO,SAAS,GAAG;AAAA,cACvE,CAAC;AACD,qBAAO,CAAC,GAAc;AAAA,gBACd;AAAA,gBACA,SAAS,WAAY;AAAA,gBAAE;AAAA,cAC3B,CAAC;AAAA,UACb;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAF,mBAAkB,UAAU,qCAAqC,SAAU,OAAO,SAAS,SAAS;AAChG,UAAI,QAAQ,CAAC;AACb,eAAS,KAAK,GAAG,KAAK,QAAQ,SAAS,KAAK,GAAG,QAAQ,MAAM;AACzD,YAAI,SAAS,GAAG,EAAE;AAClB,iBAAS,KAAK,GAAG,KAAK,OAAO,aAAa,KAAK,GAAG,QAAQ,MAAM;AAC5D,cAAI,aAAa,GAAG,EAAE;AACtB,gBAAM,KAAK;AAAA,YACP,UAAU,MAAM;AAAA,YAChB,MAAM;AAAA,cACF,OAAO,KAAK,iBAAiB,OAAO,WAAW,IAAI;AAAA,cACnD,MAAM,WAAW;AAAA,YACrB;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,MACJ;AACA,UAAI,SAAS;AAAA,QACT,OAAO,QAAQ;AAAA,QACf,MAAM,EAAE,MAAa;AAAA,QACrB,aAAa,QAAQ;AAAA,QACrB,MAAM;AAAA,MACV;AACA,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE,YAAY;AAAA;AAGd,IAAI;AAAA;AAAA,EAA+B,SAAU,QAAQ;AACjD,cAAUG,gBAAe,MAAM;AAC/B,aAASA,eAAc,WAAW,QAAQ;AACtC,UAAI,QAAQ,OAAO,KAAK,MAAM,MAAM,KAAK;AACzC,YAAM,YAAY;AAClB,aAAO;AAAA,IACX;AACA,IAAAA,eAAc,UAAU,qBAAqB,SAAU,OAAO,UAAU,SAAS,OAAO;AACpF,aAAOC,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,UAAU,UAAU,QAAQ,QAAQ,YAAY,iBAAiB,OAAO,IAAI,mBAAmB,gBAAgB;AACnH,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,yBAAW,MAAM;AACjB,yBAAW,SAAS,SAAS;AAC7B,uBAAS,MAAM,YAAY,QAAQ;AACnC,qBAAO,CAAC,GAAa,KAAK,QAAQ,QAAQ,CAAC;AAAA,YAC/C,KAAK;AACD,uBAAS,GAAG,KAAK;AACjB,kBAAI,MAAM,WAAW,GAAG;AACpB,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,qBAAO,CAAC,GAAa,OAAO,cAAc,UAAU,QAAQ;AAAA,gBACpD,yBAAyB;AAAA,cAC7B,CAAC,CAAC;AAAA,YACV,KAAK;AACD,2BAAa,GAAG,KAAK;AACrB,kBAAI,WAAW,cAAc,OAAO;AAEhC,uBAAO,CAAC,GAAc;AAAA,kBACd,OAAO,CAAC;AAAA,kBACR,cAAc,WAAW;AAAA,gBAC7B,CAAC;AAAA,cACT;AACA,kBAAI,WAAW,iBAAiB,QAAW;AACvC,sBAAM,IAAI,MAAM,kCAAkC;AAAA,cACtD;AACA,qBAAO,CAAC,GAAa,OAAO;AAAA,gBAAoB;AAAA,gBAAU;AAAA;AAAA,gBAC1C;AAAA;AAAA,gBACC;AAAA;AAAA,gBACO;AAAA,cAAK,CAAC;AAAA,YAClC,KAAK;AACD,gCAAkB,GAAG,KAAK;AAC1B,kBAAI,CAAC,mBAAmB,MAAM,WAAW,GAAG;AACxC,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,sBAAQ,CAAC;AACT,mBAAK,KAAK,GAAG,oBAAoB,iBAAiB,KAAK,kBAAkB,QAAQ,MAAM;AACnF,iCAAiB,kBAAkB,EAAE;AACrC,0BAAU,KAAK,UAAU,iBAAiB,eAAe,QAAQ;AACjE,oBAAI,SAAS;AACT,wBAAM,KAAK;AAAA,oBACP,UAAU,QAAQ;AAAA,oBAClB,MAAM;AAAA,sBACF,OAAO,KAAK,iBAAiB,SAAS,eAAe,QAAQ;AAAA,sBAC7D,MAAM;AAAA,oBACV;AAAA,kBACJ,CAAC;AAAA,gBACL,OACK;AACD,wBAAM,IAAI,MAAM,kBAAkB,eAAe,WAAW,GAAG;AAAA,gBACnE;AAAA,cACJ;AACA,qBAAO,CAAC,GAAc,EAAE,MAAa,CAAC;AAAA,UAC9C;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOF;AAAA,EACX,EAAE,OAAO;AAAA;AAGT,IAAI;AAAA;AAAA,EAAmC,SAAU,QAAQ;AACrD,cAAUG,oBAAmB,MAAM;AACnC,aAASA,qBAAoB;AACzB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,mBAAkB,UAAU,oBAAoB,SAAU,OAAO,OAAO,OAAO;AAC3E,aAAOC,WAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,UAAU,UAAU,OAAO,KAAK,QAAQ;AAC5C,YAAI,QAAQ;AACZ,eAAOC,aAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,yBAAW,MAAM;AACjB,yBAAW,SAAS,SAAS;AAC7B,sBAAQ,MAAM,YAAY;AAAA,gBACtB,YAAY,MAAM;AAAA,gBAClB,QAAQ,MAAM;AAAA,cAClB,CAAC;AACD,oBAAM,MAAM,YAAY;AAAA,gBACpB,YAAY,MAAM;AAAA,gBAClB,QAAQ,MAAM;AAAA,cAClB,CAAC;AACD,qBAAO,CAAC,GAAa,KAAK,QAAQ,QAAQ,CAAC;AAAA,YAC/C,KAAK;AACD,uBAAS,GAAG,KAAK;AACjB,kBAAI,MAAM,WAAW,GAAG;AACpB,uBAAO,CAAC,GAAc,CAAC,CAAC;AAAA,cAC5B;AACA,qBAAO,CAAC,GAAa,OAAO,kBAAkB,UAAU,OAAO,GAAG,CAAC;AAAA,YACvE,KAAK;AACD,sBAAQ,GAAG,KAAK;AAChB,qBAAO,CAAC,GAAc,MAAM,IAAI,SAAU,MAAM;AACxC,uBAAO,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG,EAAE,UAAU,MAAM,cAAc,KAAK,QAAQ,GAAG,MAAM,MAAM,iBAAiB,KAAK,IAAI,EAAE,CAAC;AAAA,cACjI,CAAC,CAAC;AAAA,UACd;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAF,mBAAkB,UAAU,mBAAmB,SAAU,MAAM;AAC3D,cAAQ,MAAM;AAAA,QACV,KAAK;AACD,iBAAO,UAAU,cAAc;AAAA,QACnC,KAAK;AACD,iBAAO,UAAU,cAAc;AAAA,QACnC;AACI,iBAAO,UAAU,cAAc;AAAA,MACvC;AAAA,IACJ;AACA,WAAOA;AAAA,EACX,EAAE,OAAO;AAAA;;;AC3wCT,IAAI;AACJ,IAAI;AACG,SAAS,gBAAgB,UAAU;AACtC,qBAAmB,UAAU,UAAU,YAAY;AACvD;AACO,SAAS,gBAAgB,UAAU;AACtC,qBAAmB,UAAU,UAAU,YAAY;AACvD;AACO,SAAS,sBAAsB;AAClC,SAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC1C,QAAI,CAAC,kBAAkB;AACnB,aAAO,OAAO,4BAA4B;AAAA,IAC9C;AACA,YAAQ,gBAAgB;AAAA,EAC5B,CAAC;AACL;AACO,SAAS,sBAAsB;AAClC,SAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC1C,QAAI,CAAC,kBAAkB;AACnB,aAAO,OAAO,4BAA4B;AAAA,IAC9C;AACA,YAAQ,gBAAgB;AAAA,EAC5B,CAAC;AACL;AACA,SAAS,UAAU,UAAU,QAAQ;AACjC,MAAI,SAAS,IAAI,cAAc,QAAQ,QAAQ;AAC/C,MAAI,SAAS,WAAY;AACrB,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IAC3B;AACA,WAAO,OAAO,yBAAyB,MAAM,QAAQ,IAAI;AAAA,EAC7D;AACA,MAAI,WAAW,IAAqB,SAAS,MAAM;AACnD,YAAU,+BAA+B,QAAQ,IAAqB,eAAe,MAAM,CAAC;AAC5F,YAAU,8BAA8B,QAAQ,IAAqB,qBAAqB,MAAM,CAAC;AACjG,YAAU,sBAAsB,QAAQ,IAAqB,iBAAiB,MAAM,CAAC;AACrF,YAAU,kCAAkC,QAAQ,IAAqB,mBAAmB,MAAM,CAAC;AACnG,YAAU,2BAA2B,QAAQ,IAAqB,kBAAkB,UAAU,MAAM,CAAC;AACrG,YAAU,0BAA0B,QAAQ,IAAqB,iBAAiB,UAAU,MAAM,CAAC;AACnG,YAAU,+BAA+B,QAAQ,IAAqB,eAAe,MAAM,CAAC;AAC5F,YAAU,4CAA4C,QAAQ,IAAqB,cAAc,MAAM,CAAC;AACxG,YAAU,qCAAqC,QAAQ,IAAqB,oBAAoB,MAAM,CAAC;AACvG,YAAU,2BAA2B,QAAQ,IAAqB,kBAAkB,MAAM,CAAC;AAC3F,YAAU,uBAAuB,QAAQ,IAAqB,cAAc,UAAU,MAAM,CAAC;AAC7F,YAAU,2BAA2B,QAAQ,IAAqB,kBAAkB,MAAM,CAAC;AAC3F,MAAqB,mBAAmB,UAAU,UAAU,QAAQ,MAAM;AAC1E,SAAO;AACX;", "names": ["WorkerManager", "d", "b", "__awaiter", "__generator", "IndentStyle", "Adapter", "LibFiles", "__awaiter", "__generator", "DiagnosticCategory", "DiagnosticsAdapter", "__awaiter", "__generator", "SuggestAdapter", "__awaiter", "__generator", "_a", "SignatureHelpAdapter", "__awaiter", "__generator", "QuickInfoAdapter", "__awaiter", "__generator", "OccurrencesAdapter", "__awaiter", "__generator", "DefinitionAdapter", "__awaiter", "__generator", "entry", "ReferenceAdapter", "__awaiter", "__generator", "entry", "OutlineAdapter", "__awaiter", "__generator", "result", "_a", "Kind", "FormatHelper", "FormatAdapter", "__awaiter", "__generator", "FormatOnTypeAdapter", "__awaiter", "__generator", "CodeActionAdaptor", "__awaiter", "__generator", "RenameAdapter", "__awaiter", "__generator", "InlayHintsAdapter", "__awaiter", "__generator"]}