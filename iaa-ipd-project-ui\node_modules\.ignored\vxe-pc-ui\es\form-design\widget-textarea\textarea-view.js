import { defineComponent, h, inject } from 'vue';
import { getI18n } from '@vxe-ui/core';
import { useWidgetName } from '../../form-design/src/use';
import VxeFormItemComponent from '../../form/src/form-item';
export const WidgetTextareaViewComponent = defineComponent({
    props: {
        renderOpts: {
            type: Object,
            default: () => ({})
        },
        renderParams: {
            type: Object,
            default: () => ({})
        }
    },
    emits: [],
    setup(props) {
        const $xeFormView = inject('$xeFormView', null);
        const { computeKebabCaseName } = useWidgetName(props);
        const changeEvent = () => {
            const { renderParams } = props;
            const { widget } = renderParams;
            if ($xeFormView) {
                const itemValue = $xeFormView ? $xeFormView.getItemValue(widget) : null;
                $xeFormView.updateWidgetStatus(widget, itemValue);
            }
        };
        return () => {
            const { renderParams } = props;
            const { widget } = renderParams;
            const { options } = widget;
            const kebabCaseName = computeKebabCaseName.value;
            return h(VxeFormItemComponent, {
                class: ['vxe-form-design--widget-render-form-item', `widget-${kebabCaseName}`],
                title: widget.title,
                field: widget.field,
                itemRender: {}
            }, {
                default() {
                    return h('textarea', {
                        class: 'vxe-default-textarea',
                        placeholder: options.placeholder || getI18n('vxe.base.pleaseInput'),
                        value: $xeFormView ? $xeFormView.getItemValue(widget) : null,
                        onChange: changeEvent,
                        onInput(evnt) {
                            if ($xeFormView) {
                                $xeFormView.setItemValue(widget, evnt.target.value);
                            }
                        }
                    });
                }
            });
        };
    }
});
