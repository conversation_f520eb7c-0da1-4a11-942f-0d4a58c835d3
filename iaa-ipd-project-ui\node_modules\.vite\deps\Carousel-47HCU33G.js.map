{"version": 3, "sources": ["../../amis/esm/renderers/Carousel.js"], "sourcesContent": ["/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __awaiter, __generator, __assign, __read, __rest, __decorate, __metadata } from 'tslib';\nimport React from 'react';\nimport Transition, { ENTERING, ENTERED, EXITING, EXITED } from 'react-transition-group/Transition';\nimport { getPropValue, isArrayChildrenModified, resolveVariableAndFilter, createObject, isObject, setThemeClassName, CustomStyle, autobind, ScopedContext, Renderer } from 'amis-core';\nimport { Html, Icon } from 'amis-ui';\nimport ThemedImageThumb from './Image.js';\n\nvar _a;\nvar animationStyles = (_a = {},\n    _a[ENTERING] = 'in',\n    _a[ENTERED] = 'in',\n    _a[EXITING] = 'out',\n    _a);\nvar defaultSchema = {\n    component: function (props) {\n        var _a, _b;\n        var data = props.data || {};\n        var thumbMode = props.thumbMode;\n        var cx = props.classnames;\n        return (React.createElement(React.Fragment, null, data.hasOwnProperty('image') ? (React.createElement(ThemedImageThumb, { src: data.image, title: data.title, href: data.href, blank: data.blank, htmlTarget: data.htmlTarget, caption: data.description, thumbMode: (_b = (_a = data.thumbMode) !== null && _a !== void 0 ? _a : thumbMode) !== null && _b !== void 0 ? _b : 'contain', imageMode: \"original\", className: cx('Carousel-image') })) : data.hasOwnProperty('html') ? (React.createElement(Html, { html: data.html, filterHtml: props.env.filterHtml })) : data.hasOwnProperty('item') ? (React.createElement(\"span\", null, data.item)) : (React.createElement(\"p\", null))));\n    }\n};\nvar SCROLL_THRESHOLD = 20;\nvar Carousel = /** @class */ (function (_super) {\n    __extends(Carousel, _super);\n    function Carousel() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.wrapperRef = React.createRef();\n        _this.state = {\n            current: 0,\n            options: _this.props.options || getPropValue(_this.props) || [],\n            nextAnimation: '',\n            mouseStartLocation: null,\n            isPaused: false\n        };\n        _this.loading = false;\n        _this.marqueeRef = React.createRef();\n        _this.contentRef = React.createRef();\n        return _this;\n    }\n    Carousel.prototype.componentDidMount = function () {\n        this.prepareAutoSlide();\n        // 跑马灯效果\n        if (this.props.animation === 'marquee') {\n            this.marquee();\n        }\n    };\n    Carousel.prototype.componentDidUpdate = function (prevProps) {\n        var props = this.props;\n        var nextOptions = props.options || getPropValue(props) || [];\n        var prevOptions = prevProps.options || getPropValue(prevProps) || [];\n        if (isArrayChildrenModified(prevOptions, nextOptions)) {\n            this.setState({\n                options: nextOptions\n            });\n        }\n        if (this.props.animation === 'marquee' &&\n            prevProps.animation !== 'marquee') {\n            this.marquee();\n        }\n    };\n    Carousel.prototype.componentWillUnmount = function () {\n        this.clearAutoTimeout();\n        cancelAnimationFrame(this.marqueeRequestId);\n    };\n    Carousel.prototype.marquee = function () {\n        var _this = this;\n        if (!this.marqueeRef.current || !this.contentRef.current) {\n            return;\n        }\n        var positionNum = 0;\n        var lastTime = performance.now();\n        var contentDom = this.contentRef.current;\n        var animate = function (time) {\n            var _a, _b;\n            var diffTime = time - lastTime;\n            lastTime = time;\n            var wrapWidth = (_b = (_a = _this.marqueeRef.current) === null || _a === void 0 ? void 0 : _a.offsetWidth) !== null && _b !== void 0 ? _b : 0;\n            if (!_this.state.isPaused) {\n                // 计算每帧移动距离\n                var moveDistance = wrapWidth * (diffTime / _this.props.duration);\n                positionNum += -moveDistance;\n                // 检查是否需要重置位置\n                var contentWidth = contentDom.scrollWidth / 2;\n                if (Math.abs(positionNum) >= contentWidth) {\n                    positionNum = 0;\n                }\n                contentDom.style.transform = \"translateX(\".concat(positionNum, \"px)\");\n            }\n            _this.marqueeRequestId = requestAnimationFrame(animate);\n        };\n        this.marqueeRequestId = requestAnimationFrame(animate);\n    };\n    Carousel.prototype.doAction = function (action, ctx, throwErrors, args) {\n        var actionType = action === null || action === void 0 ? void 0 : action.actionType;\n        if (!!~['next', 'prev'].indexOf(actionType)) {\n            this.autoSlide(actionType);\n        }\n        else if (actionType === 'goto-image') {\n            this.changeSlide((args === null || args === void 0 ? void 0 : args.activeIndex) - 1);\n        }\n    };\n    Carousel.prototype.prepareAutoSlide = function () {\n        if (this.state.options.length < 2) {\n            return;\n        }\n        this.clearAutoTimeout();\n        if (this.props.auto) {\n            var interval = this.props.interval;\n            this.intervalTimeout = setTimeout(this.autoSlide, typeof interval === 'string'\n                ? resolveVariableAndFilter(interval, this.props.data) || 5000\n                : interval);\n        }\n    };\n    Carousel.prototype.autoSlide = function (rel) {\n        this.clearAutoTimeout();\n        var animation = this.props.animation;\n        var nextAnimation = this.state.nextAnimation;\n        switch (rel) {\n            case 'prev':\n                animation === 'slide'\n                    ? (nextAnimation = 'slideRight')\n                    : (nextAnimation = '');\n                this.transitFramesTowards('right', nextAnimation);\n                break;\n            case 'next':\n            default:\n                nextAnimation = '';\n                this.transitFramesTowards('left', nextAnimation);\n                break;\n        }\n        this.durationTimeout = setTimeout(this.prepareAutoSlide, this.props.duration);\n    };\n    Carousel.prototype.transitFramesTowards = function (direction, nextAnimation) {\n        return __awaiter(this, void 0, void 0, function () {\n            var current, prevIndex, _a, dispatchEvent, data, rendererEvent;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        current = this.state.current;\n                        prevIndex = current;\n                        // 如果这里是不循环状态，需要阻止切换到第一张或者最后一张图片\n                        if (this.props.loop === false &&\n                            ((current === 0 && direction === 'right') ||\n                                (current === this.state.options.length - 1 && direction === 'left'))) {\n                            return [2 /*return*/];\n                        }\n                        switch (direction) {\n                            case 'left':\n                                current = this.getFrameId('next');\n                                break;\n                            case 'right':\n                                current = this.getFrameId('prev');\n                                break;\n                        }\n                        _a = this.props, dispatchEvent = _a.dispatchEvent, data = _a.data;\n                        return [4 /*yield*/, dispatchEvent('change', createObject(data, {\n                                activeIndex: current + 1,\n                                prevIndex: prevIndex\n                            }))];\n                    case 1:\n                        rendererEvent = _b.sent();\n                        if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {\n                            return [2 /*return*/];\n                        }\n                        this.setState({\n                            current: current,\n                            nextAnimation: nextAnimation\n                        });\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    Carousel.prototype.getFrameId = function (pos) {\n        var _a = this.state, options = _a.options, current = _a.current;\n        var total = options.length;\n        switch (pos) {\n            case 'prev':\n                return (current - 1 + total) % total;\n            case 'next':\n                return (current + 1) % total;\n            default:\n                return current;\n        }\n    };\n    Carousel.prototype.next = function () {\n        var multiple = this.props.multiple;\n        if (this.loading && multiple && multiple.count > 1) {\n            return;\n        }\n        this.autoSlide('next');\n    };\n    Carousel.prototype.prev = function () {\n        var multiple = this.props.multiple;\n        if (this.loading && multiple && multiple.count > 1) {\n            return;\n        }\n        this.autoSlide('prev');\n    };\n    Carousel.prototype.clearAutoTimeout = function () {\n        clearTimeout(this.intervalTimeout);\n        clearTimeout(this.durationTimeout);\n    };\n    Carousel.prototype.changeSlide = function (index) {\n        return __awaiter(this, void 0, void 0, function () {\n            var current, _a, dispatchEvent, data, multiple, rendererEvent;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        current = this.state.current;\n                        _a = this.props, dispatchEvent = _a.dispatchEvent, data = _a.data, multiple = _a.multiple;\n                        if (this.loading && multiple && multiple.count > 1) {\n                            return [2 /*return*/];\n                        }\n                        return [4 /*yield*/, dispatchEvent('change', createObject(data, {\n                                activeIndex: index,\n                                prevIndex: current\n                            }))];\n                    case 1:\n                        rendererEvent = _b.sent();\n                        if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {\n                            return [2 /*return*/];\n                        }\n                        this.setState({\n                            current: index\n                        });\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    Carousel.prototype.renderDots = function () {\n        var _this = this;\n        var cx = this.props.classnames;\n        var _a = this.state, current = _a.current, options = _a.options;\n        return (React.createElement(\"div\", { className: cx('Carousel-dotsControl'), onMouseEnter: this.handleMouseEnter, onMouseLeave: this.handleMouseLeave }, Array.from({ length: options.length }).map(function (_, i) { return (React.createElement(\"span\", { key: i, onClick: function () { return _this.changeSlide(i); }, className: cx('Carousel-dot', current === i ? 'is-active' : '') })); })));\n    };\n    Carousel.prototype.renderArrows = function () {\n        var cx = this.props.classnames;\n        return (React.createElement(\"div\", { className: cx('Carousel-arrowsControl'), onMouseEnter: this.handleMouseEnter, onMouseLeave: this.handleMouseLeave },\n            React.createElement(\"div\", { className: cx('Carousel-leftArrow'), onClick: this.prev },\n                React.createElement(Icon, { icon: \"left-arrow\", className: \"icon\" })),\n            React.createElement(\"div\", { className: cx('Carousel-rightArrow'), onClick: this.next },\n                React.createElement(Icon, { icon: \"right-arrow\", className: \"icon\" }))));\n    };\n    Carousel.prototype.handleMouseEnter = function () {\n        var multiple = this.props.multiple;\n        if (multiple && multiple.count > 1) {\n            return;\n        }\n        this.clearAutoTimeout();\n    };\n    Carousel.prototype.handleMouseLeave = function () {\n        var multiple = this.props.multiple;\n        if (multiple && multiple.count > 1) {\n            return;\n        }\n        this.prepareAutoSlide();\n    };\n    // 处理options\n    Carousel.prototype.getNewOptions = function (options, count) {\n        if (count === void 0) { count = 1; }\n        var newOptions = options;\n        if (Array.isArray(options) && options.length) {\n            newOptions = new Array(options.length);\n            for (var i = 0; i < options.length; i++) {\n                newOptions[i] = new Array(count);\n                for (var j = 0; j < count; j++) {\n                    newOptions[i][j] = options[(i + j) % options.length];\n                }\n            }\n        }\n        return newOptions;\n    };\n    /**\n     * 获取事件发生的屏幕坐标，兼容鼠标事件和触摸事件。\n     *\n     * @param event 事件对象，可以是鼠标事件或触摸事件\n     * @returns 返回包含屏幕横纵坐标的对象\n     */\n    Carousel.prototype.getEventScreenXY = function (event) {\n        var _a, _b, _c, _d, _e, _f;\n        var screenX, screenY;\n        if (event.screenX !== undefined) {\n            screenX = event.screenX;\n            screenY = event.screenY;\n        }\n        else if ((_a = event.touches) === null || _a === void 0 ? void 0 : _a.length) {\n            // touchStart 事件\n            screenX = (_b = event.touches[0]) === null || _b === void 0 ? void 0 : _b.screenX;\n            screenY = (_c = event.touches[0]) === null || _c === void 0 ? void 0 : _c.screenY;\n        }\n        else if ((_d = event.changedTouches) === null || _d === void 0 ? void 0 : _d.length) {\n            // touchEnd 事件\n            screenX = (_e = event.changedTouches[0]) === null || _e === void 0 ? void 0 : _e.screenX;\n            screenY = (_f = event.changedTouches[0]) === null || _f === void 0 ? void 0 : _f.screenY;\n        }\n        return {\n            screenX: screenX,\n            screenY: screenY\n        };\n    };\n    /**\n     * 添加鼠标按下事件监听器, 用于判断滑动方向\n     * @param event 鼠标事件对象\n     */\n    Carousel.prototype.addMouseDownListener = function (event) {\n        var direction = this.props.direction;\n        var _a = this.getEventScreenXY(event), screenX = _a.screenX, screenY = _a.screenY;\n        // 根据当前滑动方向确定是应该使用x坐标还是y坐标做mark\n        var location = direction === 'vertical' ? screenY : screenX;\n        location !== undefined &&\n            this.setState({\n                mouseStartLocation: location\n            });\n    };\n    /**\n     * 添加鼠标抬起事件监听器, 用于判断滑动方向\n     * @param event 鼠标事件对象\n     */\n    Carousel.prototype.addMouseUpListener = function (event) {\n        var _a = this.getEventScreenXY(event), screenX = _a.screenX, screenY = _a.screenY;\n        // 根据当前滑动方向确定是应该使用x坐标还是y坐标做mark\n        var direction = this.props.direction;\n        var location = direction === 'vertical' ? screenY : screenX;\n        if (this.state.mouseStartLocation !== null && location !== undefined) {\n            if (location - this.state.mouseStartLocation > SCROLL_THRESHOLD) {\n                this.autoSlide('prev');\n            }\n            else if (this.state.mouseStartLocation - location > SCROLL_THRESHOLD) {\n                this.autoSlide();\n            }\n            this.setState({\n                mouseStartLocation: null\n            });\n        }\n    };\n    Carousel.prototype.render = function () {\n        var _a, _b;\n        var _this = this;\n        var _c = this.props, render = _c.render, className = _c.className, style = _c.style, cx = _c.classnames, itemSchema = _c.itemSchema, animation = _c.animation, width = _c.width, height = _c.height, controls = _c.controls, controlsTheme = _c.controlsTheme, placeholder = _c.placeholder, data = _c.data, name = _c.name, duration = _c.duration, multiple = _c.multiple, alwaysShowArrow = _c.alwaysShowArrow, icons = _c.icons, id = _c.id, wrapperCustomStyle = _c.wrapperCustomStyle, env = _c.env, themeCss = _c.themeCss;\n        var _d = this.state, options = _d.options, current = _d.current, nextAnimation = _d.nextAnimation;\n        var body = null;\n        var carouselStyles = style ? __assign({}, style) : {};\n        // 不允许传0，需要有最小高度\n        if (width) {\n            // 数字类型认为是px单位，否则传入字符串直接赋给style对象\n            !isNaN(Number(width))\n                ? (carouselStyles.width = width + 'px')\n                : (carouselStyles.width = width);\n        }\n        if (height) {\n            !isNaN(Number(height))\n                ? (carouselStyles.height = height + 'px')\n                : (carouselStyles.height = height);\n        }\n        var _e = __read([\n            controls.indexOf('dots') > -1 && animation !== 'marquee',\n            controls.indexOf('arrows') > -1 && animation !== 'marquee'\n        ], 2), dots = _e[0], arrows = _e[1];\n        var animationName = nextAnimation || animation;\n        if (Array.isArray(options) && options.length) {\n            var multipleCount_1 = 1;\n            if (multiple &&\n                typeof multiple.count === 'number' &&\n                multiple.count >= 2) {\n                multipleCount_1 =\n                    Math.floor(multiple.count) < options.length\n                        ? Math.floor(multiple.count)\n                        : options.length;\n            }\n            var newOptions_1 = this.getNewOptions(options, multipleCount_1);\n            var transitionDuration = multipleCount_1 > 1 && typeof duration === 'number'\n                ? \"\".concat(duration, \"ms\")\n                : duration || '500ms';\n            var timeout_1 = multipleCount_1 > 1 && typeof duration === 'number' ? duration : 500;\n            var transformStyles = (_a = {},\n                _a[ENTERING] = 0,\n                _a[ENTERED] = 0,\n                _a[EXITING] = animationName === 'slideRight'\n                    ? 100 / multipleCount_1\n                    : -100 / multipleCount_1,\n                _a[EXITED] = animationName === 'slideRight'\n                    ? -100 / multipleCount_1\n                    : 100 / multipleCount_1,\n                _a);\n            var itemStyle_1 = multipleCount_1 > 1\n                ? __assign({ transitionTimingFunction: 'linear', transitionDuration: transitionDuration }, (animation === 'slide'\n                    ? {\n                        transform: \"translateX(\".concat(transformStyles[status], \"%)\")\n                    }\n                    : {})) : {};\n            var itemRender_1 = function (option) {\n                var _a;\n                var optionItemSchema = option.itemSchema, restOption = __rest(option, [\"itemSchema\"]);\n                return render(\"\".concat(current, \"/body\"), optionItemSchema || itemSchema\n                    ? optionItemSchema || itemSchema\n                    : defaultSchema, {\n                    thumbMode: _this.props.thumbMode,\n                    data: createObject(data, isObject(option) ? restOption : (_a = { item: option }, _a[name] = option, _a))\n                });\n            };\n            body =\n                animation === 'marquee' ? (React.createElement(\"div\", { ref: this.marqueeRef, className: cx('Marquee-container'), onMouseEnter: function () {\n                        return _this.setState({\n                            isPaused: true\n                        });\n                    }, onMouseLeave: function () {\n                        return _this.setState({\n                            isPaused: false\n                        });\n                    }, style: {\n                        width: '100%',\n                        height: height\n                    } },\n                    React.createElement(\"div\", { className: cx('Marquee-content'), ref: this.contentRef }, options.concat(options).map(function (option, key) { return (React.createElement(\"div\", { key: key, className: cx('Marquee-item') },\n                        multipleCount_1 === 1 ? itemRender_1(option) : null,\n                        multipleCount_1 > 1\n                            ? newOptions_1\n                                .concat(newOptions_1)[key].map(function (option, index) { return (React.createElement(\"div\", { key: index, style: {\n                                    width: 100 / multipleCount_1 + '%',\n                                    height: '100%',\n                                    float: 'left'\n                                } }, itemRender_1(option))); })\n                            : null)); })))) : (React.createElement(\"div\", { ref: this.wrapperRef, className: cx('Carousel-container'), onMouseEnter: this.handleMouseEnter, onMouseLeave: this.handleMouseLeave }, options.map(function (option, key) { return (React.createElement(Transition, { mountOnEnter: true, unmountOnExit: true, in: key === current, timeout: timeout_1, key: key }, function (status) {\n                    if (status === ENTERING) {\n                        _this.wrapperRef.current &&\n                            _this.wrapperRef.current.childNodes.forEach(function (item) { return item.offsetHeight; });\n                    }\n                    if (multipleCount_1 > 1) {\n                        if ((status === ENTERING || status === EXITING) &&\n                            !_this.loading) {\n                            _this.loading = true;\n                        }\n                        else if ((status === ENTERED || status === EXITED) &&\n                            _this.loading) {\n                            _this.loading = false;\n                        }\n                    }\n                    return (React.createElement(\"div\", { className: cx('Carousel-item', animationName, animationStyles[status]), style: itemStyle_1 },\n                        multipleCount_1 === 1 ? itemRender_1(option) : null,\n                        multipleCount_1 > 1\n                            ? newOptions_1[key].map(function (option, index) { return (React.createElement(\"div\", { key: index, style: {\n                                    width: 100 / multipleCount_1 + '%',\n                                    height: '100%',\n                                    float: 'left'\n                                } }, itemRender_1(option))); })\n                            : null));\n                })); })));\n        }\n        return (React.createElement(\"div\", { className: cx(\"Carousel Carousel--\".concat(controlsTheme), (_b = {}, _b['Carousel-arrow--always'] = !!alwaysShowArrow, _b), className, setThemeClassName(__assign(__assign({}, this.props), { name: 'baseControlClassName', id: id, themeCss: themeCss })), setThemeClassName(__assign(__assign({}, this.props), { name: 'wrapperCustomStyle', id: id, themeCss: wrapperCustomStyle })), { 'Carousel-vertical': this.props.direction === 'vertical' }), onMouseDown: this.props.mouseEvent ? this.addMouseDownListener : undefined, onMouseUp: this.props.mouseEvent ? this.addMouseUpListener : undefined, onMouseLeave: this.props.mouseEvent ? this.addMouseUpListener : undefined, onTouchStart: this.props.mouseEvent ? this.addMouseDownListener : undefined, onTouchEnd: this.props.mouseEvent ? this.addMouseUpListener : undefined, style: carouselStyles },\n            body ? body : placeholder,\n            dots ? this.renderDots() : null,\n            arrows ? (React.createElement(\"div\", { className: cx('Carousel-leftArrow', setThemeClassName(__assign(__assign({}, this.props), { name: 'galleryControlClassName', id: id, themeCss: themeCss }))), onClick: this.prev }, icons && icons.prev ? (React.isValidElement(icons.prev) ? (icons.prev) : (render('arrow-prev', icons.prev))) : (React.createElement(Icon, { icon: \"left-arrow\", className: \"icon\", iconContent: \"ImageGallery-prevBtn\" })))) : null,\n            arrows ? (React.createElement(\"div\", { className: cx('Carousel-rightArrow', setThemeClassName(__assign(__assign({}, this.props), { name: 'galleryControlClassName', id: id, themeCss: themeCss }))), onClick: this.next }, icons && icons.next ? (React.isValidElement(icons.next) ? (icons.next) : (render('arrow-next', icons.next))) : (React.createElement(Icon, { icon: \"right-arrow\", className: \"icon\", iconContent: \"ImageGallery-nextBtn\" })))) : null,\n            React.createElement(CustomStyle, __assign({}, this.props, { config: {\n                    wrapperCustomStyle: wrapperCustomStyle,\n                    id: id,\n                    themeCss: themeCss,\n                    classNames: [\n                        {\n                            key: 'baseControlClassName'\n                        },\n                        {\n                            key: 'galleryControlClassName',\n                            weights: {\n                                default: {\n                                    suf: ' svg',\n                                    important: true\n                                }\n                            }\n                        }\n                    ]\n                }, env: env }))));\n    };\n    Carousel.defaultProps = {\n        auto: true,\n        interval: 5000,\n        duration: 500,\n        controlsTheme: 'light',\n        animation: 'fade',\n        controls: ['dots', 'arrows'],\n        placeholder: '-',\n        multiple: { count: 1 },\n        alwaysShowArrow: false\n    };\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", []),\n        __metadata(\"design:returntype\", void 0)\n    ], Carousel.prototype, \"prepareAutoSlide\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [String]),\n        __metadata(\"design:returntype\", void 0)\n    ], Carousel.prototype, \"autoSlide\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [String, String]),\n        __metadata(\"design:returntype\", Promise)\n    ], Carousel.prototype, \"transitFramesTowards\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [String]),\n        __metadata(\"design:returntype\", void 0)\n    ], Carousel.prototype, \"getFrameId\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", []),\n        __metadata(\"design:returntype\", void 0)\n    ], Carousel.prototype, \"next\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", []),\n        __metadata(\"design:returntype\", void 0)\n    ], Carousel.prototype, \"prev\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", []),\n        __metadata(\"design:returntype\", void 0)\n    ], Carousel.prototype, \"clearAutoTimeout\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Number]),\n        __metadata(\"design:returntype\", Promise)\n    ], Carousel.prototype, \"changeSlide\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", []),\n        __metadata(\"design:returntype\", void 0)\n    ], Carousel.prototype, \"handleMouseEnter\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", []),\n        __metadata(\"design:returntype\", void 0)\n    ], Carousel.prototype, \"handleMouseLeave\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], Carousel.prototype, \"addMouseDownListener\", null);\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], Carousel.prototype, \"addMouseUpListener\", null);\n    return Carousel;\n}(React.Component));\nvar CarouselRenderer = /** @class */ (function (_super) {\n    __extends(CarouselRenderer, _super);\n    function CarouselRenderer(props, context) {\n        var _this = _super.call(this, props) || this;\n        var scoped = context;\n        scoped.registerComponent(_this);\n        return _this;\n    }\n    CarouselRenderer.prototype.componentWillUnmount = function () {\n        var _a;\n        (_a = _super.prototype.componentWillUnmount) === null || _a === void 0 ? void 0 : _a.call(this);\n        var scoped = this.context;\n        scoped.unRegisterComponent(this);\n    };\n    CarouselRenderer.contextType = ScopedContext;\n    CarouselRenderer = __decorate([\n        Renderer({\n            type: 'carousel'\n        }),\n        __metadata(\"design:paramtypes\", [Object, Object])\n    ], CarouselRenderer);\n    return CarouselRenderer;\n}(Carousel));\n\nexport { Carousel, CarouselRenderer };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,mBAAkB;AAMlB,IAAI;AACJ,IAAI,mBAAmB,KAAK,CAAC,GACzB,GAAG,QAAQ,IAAI,MACf,GAAG,OAAO,IAAI,MACd,GAAG,OAAO,IAAI,OACd;AACJ,IAAI,gBAAgB;AAAA,EAChB,WAAW,SAAU,OAAO;AACxB,QAAIA,KAAI;AACR,QAAI,OAAO,MAAM,QAAQ,CAAC;AAC1B,QAAI,YAAY,MAAM;AACtB,QAAI,KAAK,MAAM;AACf,WAAQ,aAAAC,QAAM,cAAc,aAAAA,QAAM,UAAU,MAAM,KAAK,eAAe,OAAO,IAAK,aAAAA,QAAM,cAAc,kBAAkB,EAAE,KAAK,KAAK,OAAO,OAAO,KAAK,OAAO,MAAM,KAAK,MAAM,OAAO,KAAK,OAAO,YAAY,KAAK,YAAY,SAAS,KAAK,aAAa,YAAY,MAAMD,MAAK,KAAK,eAAe,QAAQA,QAAO,SAASA,MAAK,eAAe,QAAQ,OAAO,SAAS,KAAK,WAAW,WAAW,YAAY,WAAW,GAAG,gBAAgB,EAAE,CAAC,IAAK,KAAK,eAAe,MAAM,IAAK,aAAAC,QAAM,cAAc,QAAM,EAAE,MAAM,KAAK,MAAM,YAAY,MAAM,IAAI,WAAW,CAAC,IAAK,KAAK,eAAe,MAAM,IAAK,aAAAA,QAAM,cAAc,QAAQ,MAAM,KAAK,IAAI,IAAM,aAAAA,QAAM,cAAc,KAAK,IAAI,CAAE;AAAA,EAC5pB;AACJ;AACA,IAAI,mBAAmB;AACvB,IAAI;AAAA;AAAA,EAA0B,SAAU,QAAQ;AAC5C,cAAUC,WAAU,MAAM;AAC1B,aAASA,YAAW;AAChB,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,aAAa,aAAAD,QAAM,UAAU;AACnC,YAAM,QAAQ;AAAA,QACV,SAAS;AAAA,QACT,SAAS,MAAM,MAAM,WAAW,aAAa,MAAM,KAAK,KAAK,CAAC;AAAA,QAC9D,eAAe;AAAA,QACf,oBAAoB;AAAA,QACpB,UAAU;AAAA,MACd;AACA,YAAM,UAAU;AAChB,YAAM,aAAa,aAAAA,QAAM,UAAU;AACnC,YAAM,aAAa,aAAAA,QAAM,UAAU;AACnC,aAAO;AAAA,IACX;AACA,IAAAC,UAAS,UAAU,oBAAoB,WAAY;AAC/C,WAAK,iBAAiB;AAEtB,UAAI,KAAK,MAAM,cAAc,WAAW;AACpC,aAAK,QAAQ;AAAA,MACjB;AAAA,IACJ;AACA,IAAAA,UAAS,UAAU,qBAAqB,SAAU,WAAW;AACzD,UAAI,QAAQ,KAAK;AACjB,UAAI,cAAc,MAAM,WAAW,aAAa,KAAK,KAAK,CAAC;AAC3D,UAAI,cAAc,UAAU,WAAW,aAAa,SAAS,KAAK,CAAC;AACnE,UAAI,wBAAwB,aAAa,WAAW,GAAG;AACnD,aAAK,SAAS;AAAA,UACV,SAAS;AAAA,QACb,CAAC;AAAA,MACL;AACA,UAAI,KAAK,MAAM,cAAc,aACzB,UAAU,cAAc,WAAW;AACnC,aAAK,QAAQ;AAAA,MACjB;AAAA,IACJ;AACA,IAAAA,UAAS,UAAU,uBAAuB,WAAY;AAClD,WAAK,iBAAiB;AACtB,2BAAqB,KAAK,gBAAgB;AAAA,IAC9C;AACA,IAAAA,UAAS,UAAU,UAAU,WAAY;AACrC,UAAI,QAAQ;AACZ,UAAI,CAAC,KAAK,WAAW,WAAW,CAAC,KAAK,WAAW,SAAS;AACtD;AAAA,MACJ;AACA,UAAI,cAAc;AAClB,UAAI,WAAW,YAAY,IAAI;AAC/B,UAAI,aAAa,KAAK,WAAW;AACjC,UAAI,UAAU,SAAU,MAAM;AAC1B,YAAIF,KAAI;AACR,YAAI,WAAW,OAAO;AACtB,mBAAW;AACX,YAAI,aAAa,MAAMA,MAAK,MAAM,WAAW,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,iBAAiB,QAAQ,OAAO,SAAS,KAAK;AAC5I,YAAI,CAAC,MAAM,MAAM,UAAU;AAEvB,cAAI,eAAe,aAAa,WAAW,MAAM,MAAM;AACvD,yBAAe,CAAC;AAEhB,cAAI,eAAe,WAAW,cAAc;AAC5C,cAAI,KAAK,IAAI,WAAW,KAAK,cAAc;AACvC,0BAAc;AAAA,UAClB;AACA,qBAAW,MAAM,YAAY,cAAc,OAAO,aAAa,KAAK;AAAA,QACxE;AACA,cAAM,mBAAmB,sBAAsB,OAAO;AAAA,MAC1D;AACA,WAAK,mBAAmB,sBAAsB,OAAO;AAAA,IACzD;AACA,IAAAE,UAAS,UAAU,WAAW,SAAU,QAAQ,KAAK,aAAa,MAAM;AACpE,UAAI,aAAa,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO;AACxE,UAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,MAAM,EAAE,QAAQ,UAAU,GAAG;AACzC,aAAK,UAAU,UAAU;AAAA,MAC7B,WACS,eAAe,cAAc;AAClC,aAAK,aAAa,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,eAAe,CAAC;AAAA,MACvF;AAAA,IACJ;AACA,IAAAA,UAAS,UAAU,mBAAmB,WAAY;AAC9C,UAAI,KAAK,MAAM,QAAQ,SAAS,GAAG;AAC/B;AAAA,MACJ;AACA,WAAK,iBAAiB;AACtB,UAAI,KAAK,MAAM,MAAM;AACjB,YAAI,WAAW,KAAK,MAAM;AAC1B,aAAK,kBAAkB,WAAW,KAAK,WAAW,OAAO,aAAa,WAChE,yBAAyB,UAAU,KAAK,MAAM,IAAI,KAAK,MACvD,QAAQ;AAAA,MAClB;AAAA,IACJ;AACA,IAAAA,UAAS,UAAU,YAAY,SAAU,KAAK;AAC1C,WAAK,iBAAiB;AACtB,UAAI,YAAY,KAAK,MAAM;AAC3B,UAAI,gBAAgB,KAAK,MAAM;AAC/B,cAAQ,KAAK;AAAA,QACT,KAAK;AACD,wBAAc,UACP,gBAAgB,eAChB,gBAAgB;AACvB,eAAK,qBAAqB,SAAS,aAAa;AAChD;AAAA,QACJ,KAAK;AAAA,QACL;AACI,0BAAgB;AAChB,eAAK,qBAAqB,QAAQ,aAAa;AAC/C;AAAA,MACR;AACA,WAAK,kBAAkB,WAAW,KAAK,kBAAkB,KAAK,MAAM,QAAQ;AAAA,IAChF;AACA,IAAAA,UAAS,UAAU,uBAAuB,SAAU,WAAW,eAAe;AAC1E,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,SAAS,WAAWF,KAAI,eAAe,MAAM;AACjD,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,wBAAU,KAAK,MAAM;AACrB,0BAAY;AAEZ,kBAAI,KAAK,MAAM,SAAS,UAClB,YAAY,KAAK,cAAc,WAC5B,YAAY,KAAK,MAAM,QAAQ,SAAS,KAAK,cAAc,SAAU;AAC1E,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,sBAAQ,WAAW;AAAA,gBACf,KAAK;AACD,4BAAU,KAAK,WAAW,MAAM;AAChC;AAAA,gBACJ,KAAK;AACD,4BAAU,KAAK,WAAW,MAAM;AAChC;AAAA,cACR;AACA,cAAAA,MAAK,KAAK,OAAO,gBAAgBA,IAAG,eAAe,OAAOA,IAAG;AAC7D,qBAAO,CAAC,GAAa,cAAc,UAAU,aAAa,MAAM;AAAA,gBACxD,aAAa,UAAU;AAAA,gBACvB;AAAA,cACJ,CAAC,CAAC,CAAC;AAAA,YACX,KAAK;AACD,8BAAgB,GAAG,KAAK;AACxB,kBAAI,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,WAAW;AACvF,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,mBAAK,SAAS;AAAA,gBACV;AAAA,gBACA;AAAA,cACJ,CAAC;AACD,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAE,UAAS,UAAU,aAAa,SAAU,KAAK;AAC3C,UAAIF,MAAK,KAAK,OAAO,UAAUA,IAAG,SAAS,UAAUA,IAAG;AACxD,UAAI,QAAQ,QAAQ;AACpB,cAAQ,KAAK;AAAA,QACT,KAAK;AACD,kBAAQ,UAAU,IAAI,SAAS;AAAA,QACnC,KAAK;AACD,kBAAQ,UAAU,KAAK;AAAA,QAC3B;AACI,iBAAO;AAAA,MACf;AAAA,IACJ;AACA,IAAAE,UAAS,UAAU,OAAO,WAAY;AAClC,UAAI,WAAW,KAAK,MAAM;AAC1B,UAAI,KAAK,WAAW,YAAY,SAAS,QAAQ,GAAG;AAChD;AAAA,MACJ;AACA,WAAK,UAAU,MAAM;AAAA,IACzB;AACA,IAAAA,UAAS,UAAU,OAAO,WAAY;AAClC,UAAI,WAAW,KAAK,MAAM;AAC1B,UAAI,KAAK,WAAW,YAAY,SAAS,QAAQ,GAAG;AAChD;AAAA,MACJ;AACA,WAAK,UAAU,MAAM;AAAA,IACzB;AACA,IAAAA,UAAS,UAAU,mBAAmB,WAAY;AAC9C,mBAAa,KAAK,eAAe;AACjC,mBAAa,KAAK,eAAe;AAAA,IACrC;AACA,IAAAA,UAAS,UAAU,cAAc,SAAU,OAAO;AAC9C,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,SAASF,KAAI,eAAe,MAAM,UAAU;AAChD,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,wBAAU,KAAK,MAAM;AACrB,cAAAA,MAAK,KAAK,OAAO,gBAAgBA,IAAG,eAAe,OAAOA,IAAG,MAAM,WAAWA,IAAG;AACjF,kBAAI,KAAK,WAAW,YAAY,SAAS,QAAQ,GAAG;AAChD,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,qBAAO,CAAC,GAAa,cAAc,UAAU,aAAa,MAAM;AAAA,gBACxD,aAAa;AAAA,gBACb,WAAW;AAAA,cACf,CAAC,CAAC,CAAC;AAAA,YACX,KAAK;AACD,8BAAgB,GAAG,KAAK;AACxB,kBAAI,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,WAAW;AACvF,uBAAO;AAAA,kBAAC;AAAA;AAAA,gBAAY;AAAA,cACxB;AACA,mBAAK,SAAS;AAAA,gBACV,SAAS;AAAA,cACb,CAAC;AACD,qBAAO;AAAA,gBAAC;AAAA;AAAA,cAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAE,UAAS,UAAU,aAAa,WAAY;AACxC,UAAI,QAAQ;AACZ,UAAI,KAAK,KAAK,MAAM;AACpB,UAAIF,MAAK,KAAK,OAAO,UAAUA,IAAG,SAAS,UAAUA,IAAG;AACxD,aAAQ,aAAAC,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,sBAAsB,GAAG,cAAc,KAAK,kBAAkB,cAAc,KAAK,iBAAiB,GAAG,MAAM,KAAK,EAAE,QAAQ,QAAQ,OAAO,CAAC,EAAE,IAAI,SAAU,GAAG,GAAG;AAAE,eAAQ,aAAAA,QAAM,cAAc,QAAQ,EAAE,KAAK,GAAG,SAAS,WAAY;AAAE,iBAAO,MAAM,YAAY,CAAC;AAAA,QAAG,GAAG,WAAW,GAAG,gBAAgB,YAAY,IAAI,cAAc,EAAE,EAAE,CAAC;AAAA,MAAI,CAAC,CAAC;AAAA,IACrY;AACA,IAAAC,UAAS,UAAU,eAAe,WAAY;AAC1C,UAAI,KAAK,KAAK,MAAM;AACpB,aAAQ,aAAAD,QAAM;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,GAAG,wBAAwB,GAAG,cAAc,KAAK,kBAAkB,cAAc,KAAK,iBAAiB;AAAA,QACnJ,aAAAA,QAAM;AAAA,UAAc;AAAA,UAAO,EAAE,WAAW,GAAG,oBAAoB,GAAG,SAAS,KAAK,KAAK;AAAA,UACjF,aAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,cAAc,WAAW,OAAO,CAAC;AAAA,QAAC;AAAA,QACxE,aAAAA,QAAM;AAAA,UAAc;AAAA,UAAO,EAAE,WAAW,GAAG,qBAAqB,GAAG,SAAS,KAAK,KAAK;AAAA,UAClF,aAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,eAAe,WAAW,OAAO,CAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAClF;AACA,IAAAC,UAAS,UAAU,mBAAmB,WAAY;AAC9C,UAAI,WAAW,KAAK,MAAM;AAC1B,UAAI,YAAY,SAAS,QAAQ,GAAG;AAChC;AAAA,MACJ;AACA,WAAK,iBAAiB;AAAA,IAC1B;AACA,IAAAA,UAAS,UAAU,mBAAmB,WAAY;AAC9C,UAAI,WAAW,KAAK,MAAM;AAC1B,UAAI,YAAY,SAAS,QAAQ,GAAG;AAChC;AAAA,MACJ;AACA,WAAK,iBAAiB;AAAA,IAC1B;AAEA,IAAAA,UAAS,UAAU,gBAAgB,SAAU,SAAS,OAAO;AACzD,UAAI,UAAU,QAAQ;AAAE,gBAAQ;AAAA,MAAG;AACnC,UAAI,aAAa;AACjB,UAAI,MAAM,QAAQ,OAAO,KAAK,QAAQ,QAAQ;AAC1C,qBAAa,IAAI,MAAM,QAAQ,MAAM;AACrC,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,qBAAW,CAAC,IAAI,IAAI,MAAM,KAAK;AAC/B,mBAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC5B,uBAAW,CAAC,EAAE,CAAC,IAAI,SAAS,IAAI,KAAK,QAAQ,MAAM;AAAA,UACvD;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAOA,IAAAA,UAAS,UAAU,mBAAmB,SAAU,OAAO;AACnD,UAAIF,KAAI,IAAI,IAAI,IAAI,IAAI;AACxB,UAAI,SAAS;AACb,UAAI,MAAM,YAAY,QAAW;AAC7B,kBAAU,MAAM;AAChB,kBAAU,MAAM;AAAA,MACpB,YACUA,MAAK,MAAM,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,QAAQ;AAE1E,mBAAW,KAAK,MAAM,QAAQ,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC1E,mBAAW,KAAK,MAAM,QAAQ,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAC9E,YACU,KAAK,MAAM,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ;AAEjF,mBAAW,KAAK,MAAM,eAAe,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG;AACjF,mBAAW,KAAK,MAAM,eAAe,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MACrF;AACA,aAAO;AAAA,QACH;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAKA,IAAAE,UAAS,UAAU,uBAAuB,SAAU,OAAO;AACvD,UAAI,YAAY,KAAK,MAAM;AAC3B,UAAIF,MAAK,KAAK,iBAAiB,KAAK,GAAG,UAAUA,IAAG,SAAS,UAAUA,IAAG;AAE1E,UAAI,WAAW,cAAc,aAAa,UAAU;AACpD,mBAAa,UACT,KAAK,SAAS;AAAA,QACV,oBAAoB;AAAA,MACxB,CAAC;AAAA,IACT;AAKA,IAAAE,UAAS,UAAU,qBAAqB,SAAU,OAAO;AACrD,UAAIF,MAAK,KAAK,iBAAiB,KAAK,GAAG,UAAUA,IAAG,SAAS,UAAUA,IAAG;AAE1E,UAAI,YAAY,KAAK,MAAM;AAC3B,UAAI,WAAW,cAAc,aAAa,UAAU;AACpD,UAAI,KAAK,MAAM,uBAAuB,QAAQ,aAAa,QAAW;AAClE,YAAI,WAAW,KAAK,MAAM,qBAAqB,kBAAkB;AAC7D,eAAK,UAAU,MAAM;AAAA,QACzB,WACS,KAAK,MAAM,qBAAqB,WAAW,kBAAkB;AAClE,eAAK,UAAU;AAAA,QACnB;AACA,aAAK,SAAS;AAAA,UACV,oBAAoB;AAAA,QACxB,CAAC;AAAA,MACL;AAAA,IACJ;AACA,IAAAE,UAAS,UAAU,SAAS,WAAY;AACpC,UAAIF,KAAI;AACR,UAAI,QAAQ;AACZ,UAAI,KAAK,KAAK,OAAO,SAAS,GAAG,QAAQ,YAAY,GAAG,WAAW,QAAQ,GAAG,OAAO,KAAK,GAAG,YAAY,aAAa,GAAG,YAAY,YAAY,GAAG,WAAW,QAAQ,GAAG,OAAO,SAAS,GAAG,QAAQ,WAAW,GAAG,UAAU,gBAAgB,GAAG,eAAe,cAAc,GAAG,aAAa,OAAO,GAAG,MAAM,OAAO,GAAG,MAAM,WAAW,GAAG,UAAU,WAAW,GAAG,UAAU,kBAAkB,GAAG,iBAAiB,QAAQ,GAAG,OAAO,KAAK,GAAG,IAAI,qBAAqB,GAAG,oBAAoB,MAAM,GAAG,KAAK,WAAW,GAAG;AACzf,UAAI,KAAK,KAAK,OAAO,UAAU,GAAG,SAAS,UAAU,GAAG,SAAS,gBAAgB,GAAG;AACpF,UAAI,OAAO;AACX,UAAI,iBAAiB,QAAQ,SAAS,CAAC,GAAG,KAAK,IAAI,CAAC;AAEpD,UAAI,OAAO;AAEP,SAAC,MAAM,OAAO,KAAK,CAAC,IACb,eAAe,QAAQ,QAAQ,OAC/B,eAAe,QAAQ;AAAA,MAClC;AACA,UAAI,QAAQ;AACR,SAAC,MAAM,OAAO,MAAM,CAAC,IACd,eAAe,SAAS,SAAS,OACjC,eAAe,SAAS;AAAA,MACnC;AACA,UAAI,KAAK,OAAO;AAAA,QACZ,SAAS,QAAQ,MAAM,IAAI,MAAM,cAAc;AAAA,QAC/C,SAAS,QAAQ,QAAQ,IAAI,MAAM,cAAc;AAAA,MACrD,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC;AAClC,UAAI,gBAAgB,iBAAiB;AACrC,UAAI,MAAM,QAAQ,OAAO,KAAK,QAAQ,QAAQ;AAC1C,YAAI,kBAAkB;AACtB,YAAI,YACA,OAAO,SAAS,UAAU,YAC1B,SAAS,SAAS,GAAG;AACrB,4BACI,KAAK,MAAM,SAAS,KAAK,IAAI,QAAQ,SAC/B,KAAK,MAAM,SAAS,KAAK,IACzB,QAAQ;AAAA,QACtB;AACA,YAAI,eAAe,KAAK,cAAc,SAAS,eAAe;AAC9D,YAAI,qBAAqB,kBAAkB,KAAK,OAAO,aAAa,WAC9D,GAAG,OAAO,UAAU,IAAI,IACxB,YAAY;AAClB,YAAI,YAAY,kBAAkB,KAAK,OAAO,aAAa,WAAW,WAAW;AACjF,YAAI,mBAAmBA,MAAK,CAAC,GACzBA,IAAG,QAAQ,IAAI,GACfA,IAAG,OAAO,IAAI,GACdA,IAAG,OAAO,IAAI,kBAAkB,eAC1B,MAAM,kBACN,OAAO,iBACbA,IAAG,MAAM,IAAI,kBAAkB,eACzB,OAAO,kBACP,MAAM,iBACZA;AACJ,YAAI,cAAc,kBAAkB,IAC9B,SAAS,EAAE,0BAA0B,UAAU,mBAAuC,GAAI,cAAc,UACpG;AAAA,UACE,WAAW,cAAc,OAAO,gBAAgB,MAAM,GAAG,IAAI;AAAA,QACjE,IACE,CAAC,CAAE,IAAI,CAAC;AAClB,YAAI,eAAe,SAAU,QAAQ;AACjC,cAAIA;AACJ,cAAI,mBAAmB,OAAO,YAAY,aAAa,OAAO,QAAQ,CAAC,YAAY,CAAC;AACpF,iBAAO,OAAO,GAAG,OAAO,SAAS,OAAO,GAAG,oBAAoB,aACzD,oBAAoB,aACpB,eAAe;AAAA,YACjB,WAAW,MAAM,MAAM;AAAA,YACvB,MAAM,aAAa,MAAM,SAAS,MAAM,IAAI,cAAcA,MAAK,EAAE,MAAM,OAAO,GAAGA,IAAG,IAAI,IAAI,QAAQA,IAAG;AAAA,UAC3G,CAAC;AAAA,QACL;AACA,eACI,cAAc,YAAa,aAAAC,QAAM;AAAA,UAAc;AAAA,UAAO,EAAE,KAAK,KAAK,YAAY,WAAW,GAAG,mBAAmB,GAAG,cAAc,WAAY;AACpI,mBAAO,MAAM,SAAS;AAAA,cAClB,UAAU;AAAA,YACd,CAAC;AAAA,UACL,GAAG,cAAc,WAAY;AACzB,mBAAO,MAAM,SAAS;AAAA,cAClB,UAAU;AAAA,YACd,CAAC;AAAA,UACL,GAAG,OAAO;AAAA,YACN,OAAO;AAAA,YACP;AAAA,UACJ,EAAE;AAAA,UACF,aAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,iBAAiB,GAAG,KAAK,KAAK,WAAW,GAAG,QAAQ,OAAO,OAAO,EAAE,IAAI,SAAU,QAAQ,KAAK;AAAE,mBAAQ,aAAAA,QAAM;AAAA,cAAc;AAAA,cAAO,EAAE,KAAU,WAAW,GAAG,cAAc,EAAE;AAAA,cACrN,oBAAoB,IAAI,aAAa,MAAM,IAAI;AAAA,cAC/C,kBAAkB,IACZ,aACG,OAAO,YAAY,EAAE,GAAG,EAAE,IAAI,SAAUE,SAAQ,OAAO;AAAE,uBAAQ,aAAAF,QAAM,cAAc,OAAO,EAAE,KAAK,OAAO,OAAO;AAAA,kBAC9G,OAAO,MAAM,kBAAkB;AAAA,kBAC/B,QAAQ;AAAA,kBACR,OAAO;AAAA,gBACX,EAAE,GAAG,aAAaE,OAAM,CAAC;AAAA,cAAI,CAAC,IAChC;AAAA,YAAI;AAAA,UAAI,CAAC,CAAC;AAAA,QAAC,IAAM,aAAAF,QAAM,cAAc,OAAO,EAAE,KAAK,KAAK,YAAY,WAAW,GAAG,oBAAoB,GAAG,cAAc,KAAK,kBAAkB,cAAc,KAAK,iBAAiB,GAAG,QAAQ,IAAI,SAAU,QAAQ,KAAK;AAAE,iBAAQ,aAAAA,QAAM,cAAc,oBAAY,EAAE,cAAc,MAAM,eAAe,MAAM,IAAI,QAAQ,SAAS,SAAS,WAAW,IAAS,GAAG,SAAUG,SAAQ;AAC9X,gBAAIA,YAAW,UAAU;AACrB,oBAAM,WAAW,WACb,MAAM,WAAW,QAAQ,WAAW,QAAQ,SAAU,MAAM;AAAE,uBAAO,KAAK;AAAA,cAAc,CAAC;AAAA,YACjG;AACA,gBAAI,kBAAkB,GAAG;AACrB,mBAAKA,YAAW,YAAYA,YAAW,YACnC,CAAC,MAAM,SAAS;AAChB,sBAAM,UAAU;AAAA,cACpB,YACUA,YAAW,WAAWA,YAAW,WACvC,MAAM,SAAS;AACf,sBAAM,UAAU;AAAA,cACpB;AAAA,YACJ;AACA,mBAAQ,aAAAH,QAAM;AAAA,cAAc;AAAA,cAAO,EAAE,WAAW,GAAG,iBAAiB,eAAe,gBAAgBG,OAAM,CAAC,GAAG,OAAO,YAAY;AAAA,cAC5H,oBAAoB,IAAI,aAAa,MAAM,IAAI;AAAA,cAC/C,kBAAkB,IACZ,aAAa,GAAG,EAAE,IAAI,SAAUD,SAAQ,OAAO;AAAE,uBAAQ,aAAAF,QAAM,cAAc,OAAO,EAAE,KAAK,OAAO,OAAO;AAAA,kBACnG,OAAO,MAAM,kBAAkB;AAAA,kBAC/B,QAAQ;AAAA,kBACR,OAAO;AAAA,gBACX,EAAE,GAAG,aAAaE,OAAM,CAAC;AAAA,cAAI,CAAC,IAChC;AAAA,YAAI;AAAA,UAClB,CAAC;AAAA,QAAI,CAAC,CAAC;AAAA,MACf;AACA,aAAQ,aAAAF,QAAM;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,GAAG,sBAAsB,OAAO,aAAa,IAAI,KAAK,CAAC,GAAG,GAAG,wBAAwB,IAAI,CAAC,CAAC,iBAAiB,KAAK,WAAW,kBAAkB,SAAS,SAAS,CAAC,GAAG,KAAK,KAAK,GAAG,EAAE,MAAM,wBAAwB,IAAQ,SAAmB,CAAC,CAAC,GAAG,kBAAkB,SAAS,SAAS,CAAC,GAAG,KAAK,KAAK,GAAG,EAAE,MAAM,sBAAsB,IAAQ,UAAU,mBAAmB,CAAC,CAAC,GAAG,EAAE,qBAAqB,KAAK,MAAM,cAAc,WAAW,CAAC,GAAG,aAAa,KAAK,MAAM,aAAa,KAAK,uBAAuB,QAAW,WAAW,KAAK,MAAM,aAAa,KAAK,qBAAqB,QAAW,cAAc,KAAK,MAAM,aAAa,KAAK,qBAAqB,QAAW,cAAc,KAAK,MAAM,aAAa,KAAK,uBAAuB,QAAW,YAAY,KAAK,MAAM,aAAa,KAAK,qBAAqB,QAAW,OAAO,eAAe;AAAA,QACp2B,OAAO,OAAO;AAAA,QACd,OAAO,KAAK,WAAW,IAAI;AAAA,QAC3B,SAAU,aAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,sBAAsB,kBAAkB,SAAS,SAAS,CAAC,GAAG,KAAK,KAAK,GAAG,EAAE,MAAM,2BAA2B,IAAQ,SAAmB,CAAC,CAAC,CAAC,GAAG,SAAS,KAAK,KAAK,GAAG,SAAS,MAAM,OAAQ,aAAAA,QAAM,eAAe,MAAM,IAAI,IAAK,MAAM,OAAS,OAAO,cAAc,MAAM,IAAI,IAAO,aAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,cAAc,WAAW,QAAQ,aAAa,uBAAuB,CAAC,CAAE,IAAK;AAAA,QACzb,SAAU,aAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,uBAAuB,kBAAkB,SAAS,SAAS,CAAC,GAAG,KAAK,KAAK,GAAG,EAAE,MAAM,2BAA2B,IAAQ,SAAmB,CAAC,CAAC,CAAC,GAAG,SAAS,KAAK,KAAK,GAAG,SAAS,MAAM,OAAQ,aAAAA,QAAM,eAAe,MAAM,IAAI,IAAK,MAAM,OAAS,OAAO,cAAc,MAAM,IAAI,IAAO,aAAAA,QAAM,cAAc,MAAM,EAAE,MAAM,eAAe,WAAW,QAAQ,aAAa,uBAAuB,CAAC,CAAE,IAAK;AAAA,QAC3b,aAAAA,QAAM,cAAc,aAAa,SAAS,CAAC,GAAG,KAAK,OAAO,EAAE,QAAQ;AAAA,UAC5D;AAAA,UACA;AAAA,UACA;AAAA,UACA,YAAY;AAAA,YACR;AAAA,cACI,KAAK;AAAA,YACT;AAAA,YACA;AAAA,cACI,KAAK;AAAA,cACL,SAAS;AAAA,gBACL,SAAS;AAAA,kBACL,KAAK;AAAA,kBACL,WAAW;AAAA,gBACf;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ,GAAG,IAAS,CAAC,CAAC;AAAA,MAAC;AAAA,IAC3B;AACA,IAAAC,UAAS,eAAe;AAAA,MACpB,MAAM;AAAA,MACN,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe;AAAA,MACf,WAAW;AAAA,MACX,UAAU,CAAC,QAAQ,QAAQ;AAAA,MAC3B,aAAa;AAAA,MACb,UAAU,EAAE,OAAO,EAAE;AAAA,MACrB,iBAAiB;AAAA,IACrB;AACA,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,CAAC;AAAA,MAClC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,UAAS,WAAW,oBAAoB,IAAI;AAC/C,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,UAAS,WAAW,aAAa,IAAI;AACxC,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,QAAQ,MAAM,CAAC;AAAA,MAChD,WAAW,qBAAqB,OAAO;AAAA,IAC3C,GAAGA,UAAS,WAAW,wBAAwB,IAAI;AACnD,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,UAAS,WAAW,cAAc,IAAI;AACzC,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,CAAC;AAAA,MAClC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,UAAS,WAAW,QAAQ,IAAI;AACnC,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,CAAC;AAAA,MAClC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,UAAS,WAAW,QAAQ,IAAI;AACnC,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,CAAC;AAAA,MAClC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,UAAS,WAAW,oBAAoB,IAAI;AAC/C,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,OAAO;AAAA,IAC3C,GAAGA,UAAS,WAAW,eAAe,IAAI;AAC1C,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,CAAC;AAAA,MAClC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,UAAS,WAAW,oBAAoB,IAAI;AAC/C,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,CAAC;AAAA,MAClC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,UAAS,WAAW,oBAAoB,IAAI;AAC/C,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,UAAS,WAAW,wBAAwB,IAAI;AACnD,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,UAAS,WAAW,sBAAsB,IAAI;AACjD,WAAOA;AAAA,EACX,EAAE,aAAAD,QAAM,SAAS;AAAA;AACjB,IAAI;AAAA;AAAA,EAAkC,SAAU,QAAQ;AACpD,cAAUI,mBAAkB,MAAM;AAClC,aAASA,kBAAiB,OAAO,SAAS;AACtC,UAAI,QAAQ,OAAO,KAAK,MAAM,KAAK,KAAK;AACxC,UAAI,SAAS;AACb,aAAO,kBAAkB,KAAK;AAC9B,aAAO;AAAA,IACX;AACA,IAAAA,kBAAiB,UAAU,uBAAuB,WAAY;AAC1D,UAAIL;AACJ,OAACA,MAAK,OAAO,UAAU,0BAA0B,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,IAAI;AAC9F,UAAI,SAAS,KAAK;AAClB,aAAO,oBAAoB,IAAI;AAAA,IACnC;AACA,IAAAK,kBAAiB,cAAc;AAC/B,IAAAA,oBAAmB,WAAW;AAAA,MAC1B,SAAS;AAAA,QACL,MAAM;AAAA,MACV,CAAC;AAAA,MACD,WAAW,qBAAqB,CAAC,QAAQ,MAAM,CAAC;AAAA,IACpD,GAAGA,iBAAgB;AACnB,WAAOA;AAAA,EACX,EAAE,QAAQ;AAAA;", "names": ["_a", "React", "Carousel", "option", "status", "Carousel<PERSON><PERSON><PERSON>"]}