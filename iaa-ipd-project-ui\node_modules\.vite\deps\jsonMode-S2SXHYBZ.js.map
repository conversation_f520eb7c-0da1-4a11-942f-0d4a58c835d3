{"version": 3, "sources": ["../../monaco-editor/esm/vs/language/json/workerManager.js", "../../monaco-editor/esm/vs/language/json/_deps/vscode-languageserver-types/main.js", "../../monaco-editor/esm/vs/language/json/languageFeatures.js", "../../monaco-editor/esm/vs/language/json/_deps/jsonc-parser/impl/scanner.js", "../../monaco-editor/esm/vs/language/json/_deps/jsonc-parser/impl/parser.js", "../../monaco-editor/esm/vs/language/json/_deps/jsonc-parser/main.js", "../../monaco-editor/esm/vs/language/json/tokenization.js", "../../monaco-editor/esm/vs/language/json/jsonMode.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { editor } from './fillers/monaco-editor-core.js';\nvar STOP_WHEN_IDLE_FOR = 2 * 60 * 1000; // 2min\nvar WorkerManager = /** @class */ (function () {\n    function WorkerManager(defaults) {\n        var _this = this;\n        this._defaults = defaults;\n        this._worker = null;\n        this._idleCheckInterval = window.setInterval(function () { return _this._checkIfIdle(); }, 30 * 1000);\n        this._lastUsedTime = 0;\n        this._configChangeListener = this._defaults.onDidChange(function () { return _this._stopWorker(); });\n    }\n    WorkerManager.prototype._stopWorker = function () {\n        if (this._worker) {\n            this._worker.dispose();\n            this._worker = null;\n        }\n        this._client = null;\n    };\n    WorkerManager.prototype.dispose = function () {\n        clearInterval(this._idleCheckInterval);\n        this._configChangeListener.dispose();\n        this._stopWorker();\n    };\n    WorkerManager.prototype._checkIfIdle = function () {\n        if (!this._worker) {\n            return;\n        }\n        var timePassedSinceLastUsed = Date.now() - this._lastUsedTime;\n        if (timePassedSinceLastUsed > STOP_WHEN_IDLE_FOR) {\n            this._stopWorker();\n        }\n    };\n    WorkerManager.prototype._getClient = function () {\n        this._lastUsedTime = Date.now();\n        if (!this._client) {\n            this._worker = editor.createWebWorker({\n                // module that exports the create() method and returns a `JSONWorker` instance\n                moduleId: 'vs/language/json/jsonWorker',\n                label: this._defaults.languageId,\n                // passed in to the create() method\n                createData: {\n                    languageSettings: this._defaults.diagnosticsOptions,\n                    languageId: this._defaults.languageId,\n                    enableSchemaRequest: this._defaults.diagnosticsOptions.enableSchemaRequest\n                }\n            });\n            this._client = this._worker.getProxy();\n        }\n        return this._client;\n    };\n    WorkerManager.prototype.getLanguageServiceWorker = function () {\n        var _this = this;\n        var resources = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            resources[_i] = arguments[_i];\n        }\n        var _client;\n        return this._getClient()\n            .then(function (client) {\n            _client = client;\n        })\n            .then(function (_) {\n            return _this._worker.withSyncedResources(resources);\n        })\n            .then(function (_) { return _client; });\n    };\n    return WorkerManager;\n}());\nexport { WorkerManager };\n", "/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\n'use strict';\nexport var integer;\n(function (integer) {\n    integer.MIN_VALUE = -**********;\n    integer.MAX_VALUE = **********;\n})(integer || (integer = {}));\nexport var uinteger;\n(function (uinteger) {\n    uinteger.MIN_VALUE = 0;\n    uinteger.MAX_VALUE = **********;\n})(uinteger || (uinteger = {}));\n/**\n * The Position namespace provides helper functions to work with\n * [Position](#Position) literals.\n */\nexport var Position;\n(function (Position) {\n    /**\n     * Creates a new Position literal from the given line and character.\n     * @param line The position's line.\n     * @param character The position's character.\n     */\n    function create(line, character) {\n        if (line === Number.MAX_VALUE) {\n            line = uinteger.MAX_VALUE;\n        }\n        if (character === Number.MAX_VALUE) {\n            character = uinteger.MAX_VALUE;\n        }\n        return { line: line, character: character };\n    }\n    Position.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Position](#Position) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Is.uinteger(candidate.line) && Is.uinteger(candidate.character);\n    }\n    Position.is = is;\n})(Position || (Position = {}));\n/**\n * The Range namespace provides helper functions to work with\n * [Range](#Range) literals.\n */\nexport var Range;\n(function (Range) {\n    function create(one, two, three, four) {\n        if (Is.uinteger(one) && Is.uinteger(two) && Is.uinteger(three) && Is.uinteger(four)) {\n            return { start: Position.create(one, two), end: Position.create(three, four) };\n        }\n        else if (Position.is(one) && Position.is(two)) {\n            return { start: one, end: two };\n        }\n        else {\n            throw new Error(\"Range#create called with invalid arguments[\" + one + \", \" + two + \", \" + three + \", \" + four + \"]\");\n        }\n    }\n    Range.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Range](#Range) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Position.is(candidate.start) && Position.is(candidate.end);\n    }\n    Range.is = is;\n})(Range || (Range = {}));\n/**\n * The Location namespace provides helper functions to work with\n * [Location](#Location) literals.\n */\nexport var Location;\n(function (Location) {\n    /**\n     * Creates a Location literal.\n     * @param uri The location's uri.\n     * @param range The location's range.\n     */\n    function create(uri, range) {\n        return { uri: uri, range: range };\n    }\n    Location.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Location](#Location) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Range.is(candidate.range) && (Is.string(candidate.uri) || Is.undefined(candidate.uri));\n    }\n    Location.is = is;\n})(Location || (Location = {}));\n/**\n * The LocationLink namespace provides helper functions to work with\n * [LocationLink](#LocationLink) literals.\n */\nexport var LocationLink;\n(function (LocationLink) {\n    /**\n     * Creates a LocationLink literal.\n     * @param targetUri The definition's uri.\n     * @param targetRange The full range of the definition.\n     * @param targetSelectionRange The span of the symbol definition at the target.\n     * @param originSelectionRange The span of the symbol being defined in the originating source file.\n     */\n    function create(targetUri, targetRange, targetSelectionRange, originSelectionRange) {\n        return { targetUri: targetUri, targetRange: targetRange, targetSelectionRange: targetSelectionRange, originSelectionRange: originSelectionRange };\n    }\n    LocationLink.create = create;\n    /**\n     * Checks whether the given literal conforms to the [LocationLink](#LocationLink) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Range.is(candidate.targetRange) && Is.string(candidate.targetUri)\n            && (Range.is(candidate.targetSelectionRange) || Is.undefined(candidate.targetSelectionRange))\n            && (Range.is(candidate.originSelectionRange) || Is.undefined(candidate.originSelectionRange));\n    }\n    LocationLink.is = is;\n})(LocationLink || (LocationLink = {}));\n/**\n * The Color namespace provides helper functions to work with\n * [Color](#Color) literals.\n */\nexport var Color;\n(function (Color) {\n    /**\n     * Creates a new Color literal.\n     */\n    function create(red, green, blue, alpha) {\n        return {\n            red: red,\n            green: green,\n            blue: blue,\n            alpha: alpha,\n        };\n    }\n    Color.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Color](#Color) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.numberRange(candidate.red, 0, 1)\n            && Is.numberRange(candidate.green, 0, 1)\n            && Is.numberRange(candidate.blue, 0, 1)\n            && Is.numberRange(candidate.alpha, 0, 1);\n    }\n    Color.is = is;\n})(Color || (Color = {}));\n/**\n * The ColorInformation namespace provides helper functions to work with\n * [ColorInformation](#ColorInformation) literals.\n */\nexport var ColorInformation;\n(function (ColorInformation) {\n    /**\n     * Creates a new ColorInformation literal.\n     */\n    function create(range, color) {\n        return {\n            range: range,\n            color: color,\n        };\n    }\n    ColorInformation.create = create;\n    /**\n     * Checks whether the given literal conforms to the [ColorInformation](#ColorInformation) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Range.is(candidate.range) && Color.is(candidate.color);\n    }\n    ColorInformation.is = is;\n})(ColorInformation || (ColorInformation = {}));\n/**\n * The Color namespace provides helper functions to work with\n * [ColorPresentation](#ColorPresentation) literals.\n */\nexport var ColorPresentation;\n(function (ColorPresentation) {\n    /**\n     * Creates a new ColorInformation literal.\n     */\n    function create(label, textEdit, additionalTextEdits) {\n        return {\n            label: label,\n            textEdit: textEdit,\n            additionalTextEdits: additionalTextEdits,\n        };\n    }\n    ColorPresentation.create = create;\n    /**\n     * Checks whether the given literal conforms to the [ColorInformation](#ColorInformation) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.string(candidate.label)\n            && (Is.undefined(candidate.textEdit) || TextEdit.is(candidate))\n            && (Is.undefined(candidate.additionalTextEdits) || Is.typedArray(candidate.additionalTextEdits, TextEdit.is));\n    }\n    ColorPresentation.is = is;\n})(ColorPresentation || (ColorPresentation = {}));\n/**\n * Enum of known range kinds\n */\nexport var FoldingRangeKind;\n(function (FoldingRangeKind) {\n    /**\n     * Folding range for a comment\n     */\n    FoldingRangeKind[\"Comment\"] = \"comment\";\n    /**\n     * Folding range for a imports or includes\n     */\n    FoldingRangeKind[\"Imports\"] = \"imports\";\n    /**\n     * Folding range for a region (e.g. `#region`)\n     */\n    FoldingRangeKind[\"Region\"] = \"region\";\n})(FoldingRangeKind || (FoldingRangeKind = {}));\n/**\n * The folding range namespace provides helper functions to work with\n * [FoldingRange](#FoldingRange) literals.\n */\nexport var FoldingRange;\n(function (FoldingRange) {\n    /**\n     * Creates a new FoldingRange literal.\n     */\n    function create(startLine, endLine, startCharacter, endCharacter, kind) {\n        var result = {\n            startLine: startLine,\n            endLine: endLine\n        };\n        if (Is.defined(startCharacter)) {\n            result.startCharacter = startCharacter;\n        }\n        if (Is.defined(endCharacter)) {\n            result.endCharacter = endCharacter;\n        }\n        if (Is.defined(kind)) {\n            result.kind = kind;\n        }\n        return result;\n    }\n    FoldingRange.create = create;\n    /**\n     * Checks whether the given literal conforms to the [FoldingRange](#FoldingRange) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.uinteger(candidate.startLine) && Is.uinteger(candidate.startLine)\n            && (Is.undefined(candidate.startCharacter) || Is.uinteger(candidate.startCharacter))\n            && (Is.undefined(candidate.endCharacter) || Is.uinteger(candidate.endCharacter))\n            && (Is.undefined(candidate.kind) || Is.string(candidate.kind));\n    }\n    FoldingRange.is = is;\n})(FoldingRange || (FoldingRange = {}));\n/**\n * The DiagnosticRelatedInformation namespace provides helper functions to work with\n * [DiagnosticRelatedInformation](#DiagnosticRelatedInformation) literals.\n */\nexport var DiagnosticRelatedInformation;\n(function (DiagnosticRelatedInformation) {\n    /**\n     * Creates a new DiagnosticRelatedInformation literal.\n     */\n    function create(location, message) {\n        return {\n            location: location,\n            message: message\n        };\n    }\n    DiagnosticRelatedInformation.create = create;\n    /**\n     * Checks whether the given literal conforms to the [DiagnosticRelatedInformation](#DiagnosticRelatedInformation) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Location.is(candidate.location) && Is.string(candidate.message);\n    }\n    DiagnosticRelatedInformation.is = is;\n})(DiagnosticRelatedInformation || (DiagnosticRelatedInformation = {}));\n/**\n * The diagnostic's severity.\n */\nexport var DiagnosticSeverity;\n(function (DiagnosticSeverity) {\n    /**\n     * Reports an error.\n     */\n    DiagnosticSeverity.Error = 1;\n    /**\n     * Reports a warning.\n     */\n    DiagnosticSeverity.Warning = 2;\n    /**\n     * Reports an information.\n     */\n    DiagnosticSeverity.Information = 3;\n    /**\n     * Reports a hint.\n     */\n    DiagnosticSeverity.Hint = 4;\n})(DiagnosticSeverity || (DiagnosticSeverity = {}));\n/**\n * The diagnostic tags.\n *\n * @since 3.15.0\n */\nexport var DiagnosticTag;\n(function (DiagnosticTag) {\n    /**\n     * Unused or unnecessary code.\n     *\n     * Clients are allowed to render diagnostics with this tag faded out instead of having\n     * an error squiggle.\n     */\n    DiagnosticTag.Unnecessary = 1;\n    /**\n     * Deprecated or obsolete code.\n     *\n     * Clients are allowed to rendered diagnostics with this tag strike through.\n     */\n    DiagnosticTag.Deprecated = 2;\n})(DiagnosticTag || (DiagnosticTag = {}));\n/**\n * The CodeDescription namespace provides functions to deal with descriptions for diagnostic codes.\n *\n * @since 3.16.0\n */\nexport var CodeDescription;\n(function (CodeDescription) {\n    function is(value) {\n        var candidate = value;\n        return candidate !== undefined && candidate !== null && Is.string(candidate.href);\n    }\n    CodeDescription.is = is;\n})(CodeDescription || (CodeDescription = {}));\n/**\n * The Diagnostic namespace provides helper functions to work with\n * [Diagnostic](#Diagnostic) literals.\n */\nexport var Diagnostic;\n(function (Diagnostic) {\n    /**\n     * Creates a new Diagnostic literal.\n     */\n    function create(range, message, severity, code, source, relatedInformation) {\n        var result = { range: range, message: message };\n        if (Is.defined(severity)) {\n            result.severity = severity;\n        }\n        if (Is.defined(code)) {\n            result.code = code;\n        }\n        if (Is.defined(source)) {\n            result.source = source;\n        }\n        if (Is.defined(relatedInformation)) {\n            result.relatedInformation = relatedInformation;\n        }\n        return result;\n    }\n    Diagnostic.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Diagnostic](#Diagnostic) interface.\n     */\n    function is(value) {\n        var _a;\n        var candidate = value;\n        return Is.defined(candidate)\n            && Range.is(candidate.range)\n            && Is.string(candidate.message)\n            && (Is.number(candidate.severity) || Is.undefined(candidate.severity))\n            && (Is.integer(candidate.code) || Is.string(candidate.code) || Is.undefined(candidate.code))\n            && (Is.undefined(candidate.codeDescription) || (Is.string((_a = candidate.codeDescription) === null || _a === void 0 ? void 0 : _a.href)))\n            && (Is.string(candidate.source) || Is.undefined(candidate.source))\n            && (Is.undefined(candidate.relatedInformation) || Is.typedArray(candidate.relatedInformation, DiagnosticRelatedInformation.is));\n    }\n    Diagnostic.is = is;\n})(Diagnostic || (Diagnostic = {}));\n/**\n * The Command namespace provides helper functions to work with\n * [Command](#Command) literals.\n */\nexport var Command;\n(function (Command) {\n    /**\n     * Creates a new Command literal.\n     */\n    function create(title, command) {\n        var args = [];\n        for (var _i = 2; _i < arguments.length; _i++) {\n            args[_i - 2] = arguments[_i];\n        }\n        var result = { title: title, command: command };\n        if (Is.defined(args) && args.length > 0) {\n            result.arguments = args;\n        }\n        return result;\n    }\n    Command.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Command](#Command) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.title) && Is.string(candidate.command);\n    }\n    Command.is = is;\n})(Command || (Command = {}));\n/**\n * The TextEdit namespace provides helper function to create replace,\n * insert and delete edits more easily.\n */\nexport var TextEdit;\n(function (TextEdit) {\n    /**\n     * Creates a replace text edit.\n     * @param range The range of text to be replaced.\n     * @param newText The new text.\n     */\n    function replace(range, newText) {\n        return { range: range, newText: newText };\n    }\n    TextEdit.replace = replace;\n    /**\n     * Creates a insert text edit.\n     * @param position The position to insert the text at.\n     * @param newText The text to be inserted.\n     */\n    function insert(position, newText) {\n        return { range: { start: position, end: position }, newText: newText };\n    }\n    TextEdit.insert = insert;\n    /**\n     * Creates a delete text edit.\n     * @param range The range of text to be deleted.\n     */\n    function del(range) {\n        return { range: range, newText: '' };\n    }\n    TextEdit.del = del;\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate)\n            && Is.string(candidate.newText)\n            && Range.is(candidate.range);\n    }\n    TextEdit.is = is;\n})(TextEdit || (TextEdit = {}));\nexport var ChangeAnnotation;\n(function (ChangeAnnotation) {\n    function create(label, needsConfirmation, description) {\n        var result = { label: label };\n        if (needsConfirmation !== undefined) {\n            result.needsConfirmation = needsConfirmation;\n        }\n        if (description !== undefined) {\n            result.description = description;\n        }\n        return result;\n    }\n    ChangeAnnotation.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate !== undefined && Is.objectLiteral(candidate) && Is.string(candidate.label) &&\n            (Is.boolean(candidate.needsConfirmation) || candidate.needsConfirmation === undefined) &&\n            (Is.string(candidate.description) || candidate.description === undefined);\n    }\n    ChangeAnnotation.is = is;\n})(ChangeAnnotation || (ChangeAnnotation = {}));\nexport var ChangeAnnotationIdentifier;\n(function (ChangeAnnotationIdentifier) {\n    function is(value) {\n        var candidate = value;\n        return typeof candidate === 'string';\n    }\n    ChangeAnnotationIdentifier.is = is;\n})(ChangeAnnotationIdentifier || (ChangeAnnotationIdentifier = {}));\nexport var AnnotatedTextEdit;\n(function (AnnotatedTextEdit) {\n    /**\n     * Creates an annotated replace text edit.\n     *\n     * @param range The range of text to be replaced.\n     * @param newText The new text.\n     * @param annotation The annotation.\n     */\n    function replace(range, newText, annotation) {\n        return { range: range, newText: newText, annotationId: annotation };\n    }\n    AnnotatedTextEdit.replace = replace;\n    /**\n     * Creates an annotated insert text edit.\n     *\n     * @param position The position to insert the text at.\n     * @param newText The text to be inserted.\n     * @param annotation The annotation.\n     */\n    function insert(position, newText, annotation) {\n        return { range: { start: position, end: position }, newText: newText, annotationId: annotation };\n    }\n    AnnotatedTextEdit.insert = insert;\n    /**\n     * Creates an annotated delete text edit.\n     *\n     * @param range The range of text to be deleted.\n     * @param annotation The annotation.\n     */\n    function del(range, annotation) {\n        return { range: range, newText: '', annotationId: annotation };\n    }\n    AnnotatedTextEdit.del = del;\n    function is(value) {\n        var candidate = value;\n        return TextEdit.is(candidate) && (ChangeAnnotation.is(candidate.annotationId) || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    AnnotatedTextEdit.is = is;\n})(AnnotatedTextEdit || (AnnotatedTextEdit = {}));\n/**\n * The TextDocumentEdit namespace provides helper function to create\n * an edit that manipulates a text document.\n */\nexport var TextDocumentEdit;\n(function (TextDocumentEdit) {\n    /**\n     * Creates a new `TextDocumentEdit`\n     */\n    function create(textDocument, edits) {\n        return { textDocument: textDocument, edits: edits };\n    }\n    TextDocumentEdit.create = create;\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate)\n            && OptionalVersionedTextDocumentIdentifier.is(candidate.textDocument)\n            && Array.isArray(candidate.edits);\n    }\n    TextDocumentEdit.is = is;\n})(TextDocumentEdit || (TextDocumentEdit = {}));\nexport var CreateFile;\n(function (CreateFile) {\n    function create(uri, options, annotation) {\n        var result = {\n            kind: 'create',\n            uri: uri\n        };\n        if (options !== undefined && (options.overwrite !== undefined || options.ignoreIfExists !== undefined)) {\n            result.options = options;\n        }\n        if (annotation !== undefined) {\n            result.annotationId = annotation;\n        }\n        return result;\n    }\n    CreateFile.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate && candidate.kind === 'create' && Is.string(candidate.uri) && (candidate.options === undefined ||\n            ((candidate.options.overwrite === undefined || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === undefined || Is.boolean(candidate.options.ignoreIfExists)))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    CreateFile.is = is;\n})(CreateFile || (CreateFile = {}));\nexport var RenameFile;\n(function (RenameFile) {\n    function create(oldUri, newUri, options, annotation) {\n        var result = {\n            kind: 'rename',\n            oldUri: oldUri,\n            newUri: newUri\n        };\n        if (options !== undefined && (options.overwrite !== undefined || options.ignoreIfExists !== undefined)) {\n            result.options = options;\n        }\n        if (annotation !== undefined) {\n            result.annotationId = annotation;\n        }\n        return result;\n    }\n    RenameFile.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate && candidate.kind === 'rename' && Is.string(candidate.oldUri) && Is.string(candidate.newUri) && (candidate.options === undefined ||\n            ((candidate.options.overwrite === undefined || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === undefined || Is.boolean(candidate.options.ignoreIfExists)))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    RenameFile.is = is;\n})(RenameFile || (RenameFile = {}));\nexport var DeleteFile;\n(function (DeleteFile) {\n    function create(uri, options, annotation) {\n        var result = {\n            kind: 'delete',\n            uri: uri\n        };\n        if (options !== undefined && (options.recursive !== undefined || options.ignoreIfNotExists !== undefined)) {\n            result.options = options;\n        }\n        if (annotation !== undefined) {\n            result.annotationId = annotation;\n        }\n        return result;\n    }\n    DeleteFile.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate && candidate.kind === 'delete' && Is.string(candidate.uri) && (candidate.options === undefined ||\n            ((candidate.options.recursive === undefined || Is.boolean(candidate.options.recursive)) && (candidate.options.ignoreIfNotExists === undefined || Is.boolean(candidate.options.ignoreIfNotExists)))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    DeleteFile.is = is;\n})(DeleteFile || (DeleteFile = {}));\nexport var WorkspaceEdit;\n(function (WorkspaceEdit) {\n    function is(value) {\n        var candidate = value;\n        return candidate &&\n            (candidate.changes !== undefined || candidate.documentChanges !== undefined) &&\n            (candidate.documentChanges === undefined || candidate.documentChanges.every(function (change) {\n                if (Is.string(change.kind)) {\n                    return CreateFile.is(change) || RenameFile.is(change) || DeleteFile.is(change);\n                }\n                else {\n                    return TextDocumentEdit.is(change);\n                }\n            }));\n    }\n    WorkspaceEdit.is = is;\n})(WorkspaceEdit || (WorkspaceEdit = {}));\nvar TextEditChangeImpl = /** @class */ (function () {\n    function TextEditChangeImpl(edits, changeAnnotations) {\n        this.edits = edits;\n        this.changeAnnotations = changeAnnotations;\n    }\n    TextEditChangeImpl.prototype.insert = function (position, newText, annotation) {\n        var edit;\n        var id;\n        if (annotation === undefined) {\n            edit = TextEdit.insert(position, newText);\n        }\n        else if (ChangeAnnotationIdentifier.is(annotation)) {\n            id = annotation;\n            edit = AnnotatedTextEdit.insert(position, newText, annotation);\n        }\n        else {\n            this.assertChangeAnnotations(this.changeAnnotations);\n            id = this.changeAnnotations.manage(annotation);\n            edit = AnnotatedTextEdit.insert(position, newText, id);\n        }\n        this.edits.push(edit);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    TextEditChangeImpl.prototype.replace = function (range, newText, annotation) {\n        var edit;\n        var id;\n        if (annotation === undefined) {\n            edit = TextEdit.replace(range, newText);\n        }\n        else if (ChangeAnnotationIdentifier.is(annotation)) {\n            id = annotation;\n            edit = AnnotatedTextEdit.replace(range, newText, annotation);\n        }\n        else {\n            this.assertChangeAnnotations(this.changeAnnotations);\n            id = this.changeAnnotations.manage(annotation);\n            edit = AnnotatedTextEdit.replace(range, newText, id);\n        }\n        this.edits.push(edit);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    TextEditChangeImpl.prototype.delete = function (range, annotation) {\n        var edit;\n        var id;\n        if (annotation === undefined) {\n            edit = TextEdit.del(range);\n        }\n        else if (ChangeAnnotationIdentifier.is(annotation)) {\n            id = annotation;\n            edit = AnnotatedTextEdit.del(range, annotation);\n        }\n        else {\n            this.assertChangeAnnotations(this.changeAnnotations);\n            id = this.changeAnnotations.manage(annotation);\n            edit = AnnotatedTextEdit.del(range, id);\n        }\n        this.edits.push(edit);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    TextEditChangeImpl.prototype.add = function (edit) {\n        this.edits.push(edit);\n    };\n    TextEditChangeImpl.prototype.all = function () {\n        return this.edits;\n    };\n    TextEditChangeImpl.prototype.clear = function () {\n        this.edits.splice(0, this.edits.length);\n    };\n    TextEditChangeImpl.prototype.assertChangeAnnotations = function (value) {\n        if (value === undefined) {\n            throw new Error(\"Text edit change is not configured to manage change annotations.\");\n        }\n    };\n    return TextEditChangeImpl;\n}());\n/**\n * A helper class\n */\nvar ChangeAnnotations = /** @class */ (function () {\n    function ChangeAnnotations(annotations) {\n        this._annotations = annotations === undefined ? Object.create(null) : annotations;\n        this._counter = 0;\n        this._size = 0;\n    }\n    ChangeAnnotations.prototype.all = function () {\n        return this._annotations;\n    };\n    Object.defineProperty(ChangeAnnotations.prototype, \"size\", {\n        get: function () {\n            return this._size;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    ChangeAnnotations.prototype.manage = function (idOrAnnotation, annotation) {\n        var id;\n        if (ChangeAnnotationIdentifier.is(idOrAnnotation)) {\n            id = idOrAnnotation;\n        }\n        else {\n            id = this.nextId();\n            annotation = idOrAnnotation;\n        }\n        if (this._annotations[id] !== undefined) {\n            throw new Error(\"Id \" + id + \" is already in use.\");\n        }\n        if (annotation === undefined) {\n            throw new Error(\"No annotation provided for id \" + id);\n        }\n        this._annotations[id] = annotation;\n        this._size++;\n        return id;\n    };\n    ChangeAnnotations.prototype.nextId = function () {\n        this._counter++;\n        return this._counter.toString();\n    };\n    return ChangeAnnotations;\n}());\n/**\n * A workspace change helps constructing changes to a workspace.\n */\nvar WorkspaceChange = /** @class */ (function () {\n    function WorkspaceChange(workspaceEdit) {\n        var _this = this;\n        this._textEditChanges = Object.create(null);\n        if (workspaceEdit !== undefined) {\n            this._workspaceEdit = workspaceEdit;\n            if (workspaceEdit.documentChanges) {\n                this._changeAnnotations = new ChangeAnnotations(workspaceEdit.changeAnnotations);\n                workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n                workspaceEdit.documentChanges.forEach(function (change) {\n                    if (TextDocumentEdit.is(change)) {\n                        var textEditChange = new TextEditChangeImpl(change.edits, _this._changeAnnotations);\n                        _this._textEditChanges[change.textDocument.uri] = textEditChange;\n                    }\n                });\n            }\n            else if (workspaceEdit.changes) {\n                Object.keys(workspaceEdit.changes).forEach(function (key) {\n                    var textEditChange = new TextEditChangeImpl(workspaceEdit.changes[key]);\n                    _this._textEditChanges[key] = textEditChange;\n                });\n            }\n        }\n        else {\n            this._workspaceEdit = {};\n        }\n    }\n    Object.defineProperty(WorkspaceChange.prototype, \"edit\", {\n        /**\n         * Returns the underlying [WorkspaceEdit](#WorkspaceEdit) literal\n         * use to be returned from a workspace edit operation like rename.\n         */\n        get: function () {\n            this.initDocumentChanges();\n            if (this._changeAnnotations !== undefined) {\n                if (this._changeAnnotations.size === 0) {\n                    this._workspaceEdit.changeAnnotations = undefined;\n                }\n                else {\n                    this._workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n                }\n            }\n            return this._workspaceEdit;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    WorkspaceChange.prototype.getTextEditChange = function (key) {\n        if (OptionalVersionedTextDocumentIdentifier.is(key)) {\n            this.initDocumentChanges();\n            if (this._workspaceEdit.documentChanges === undefined) {\n                throw new Error('Workspace edit is not configured for document changes.');\n            }\n            var textDocument = { uri: key.uri, version: key.version };\n            var result = this._textEditChanges[textDocument.uri];\n            if (!result) {\n                var edits = [];\n                var textDocumentEdit = {\n                    textDocument: textDocument,\n                    edits: edits\n                };\n                this._workspaceEdit.documentChanges.push(textDocumentEdit);\n                result = new TextEditChangeImpl(edits, this._changeAnnotations);\n                this._textEditChanges[textDocument.uri] = result;\n            }\n            return result;\n        }\n        else {\n            this.initChanges();\n            if (this._workspaceEdit.changes === undefined) {\n                throw new Error('Workspace edit is not configured for normal text edit changes.');\n            }\n            var result = this._textEditChanges[key];\n            if (!result) {\n                var edits = [];\n                this._workspaceEdit.changes[key] = edits;\n                result = new TextEditChangeImpl(edits);\n                this._textEditChanges[key] = result;\n            }\n            return result;\n        }\n    };\n    WorkspaceChange.prototype.initDocumentChanges = function () {\n        if (this._workspaceEdit.documentChanges === undefined && this._workspaceEdit.changes === undefined) {\n            this._changeAnnotations = new ChangeAnnotations();\n            this._workspaceEdit.documentChanges = [];\n            this._workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n        }\n    };\n    WorkspaceChange.prototype.initChanges = function () {\n        if (this._workspaceEdit.documentChanges === undefined && this._workspaceEdit.changes === undefined) {\n            this._workspaceEdit.changes = Object.create(null);\n        }\n    };\n    WorkspaceChange.prototype.createFile = function (uri, optionsOrAnnotation, options) {\n        this.initDocumentChanges();\n        if (this._workspaceEdit.documentChanges === undefined) {\n            throw new Error('Workspace edit is not configured for document changes.');\n        }\n        var annotation;\n        if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n            annotation = optionsOrAnnotation;\n        }\n        else {\n            options = optionsOrAnnotation;\n        }\n        var operation;\n        var id;\n        if (annotation === undefined) {\n            operation = CreateFile.create(uri, options);\n        }\n        else {\n            id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n            operation = CreateFile.create(uri, options, id);\n        }\n        this._workspaceEdit.documentChanges.push(operation);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    WorkspaceChange.prototype.renameFile = function (oldUri, newUri, optionsOrAnnotation, options) {\n        this.initDocumentChanges();\n        if (this._workspaceEdit.documentChanges === undefined) {\n            throw new Error('Workspace edit is not configured for document changes.');\n        }\n        var annotation;\n        if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n            annotation = optionsOrAnnotation;\n        }\n        else {\n            options = optionsOrAnnotation;\n        }\n        var operation;\n        var id;\n        if (annotation === undefined) {\n            operation = RenameFile.create(oldUri, newUri, options);\n        }\n        else {\n            id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n            operation = RenameFile.create(oldUri, newUri, options, id);\n        }\n        this._workspaceEdit.documentChanges.push(operation);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    WorkspaceChange.prototype.deleteFile = function (uri, optionsOrAnnotation, options) {\n        this.initDocumentChanges();\n        if (this._workspaceEdit.documentChanges === undefined) {\n            throw new Error('Workspace edit is not configured for document changes.');\n        }\n        var annotation;\n        if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n            annotation = optionsOrAnnotation;\n        }\n        else {\n            options = optionsOrAnnotation;\n        }\n        var operation;\n        var id;\n        if (annotation === undefined) {\n            operation = DeleteFile.create(uri, options);\n        }\n        else {\n            id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n            operation = DeleteFile.create(uri, options, id);\n        }\n        this._workspaceEdit.documentChanges.push(operation);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    return WorkspaceChange;\n}());\nexport { WorkspaceChange };\n/**\n * The TextDocumentIdentifier namespace provides helper functions to work with\n * [TextDocumentIdentifier](#TextDocumentIdentifier) literals.\n */\nexport var TextDocumentIdentifier;\n(function (TextDocumentIdentifier) {\n    /**\n     * Creates a new TextDocumentIdentifier literal.\n     * @param uri The document's uri.\n     */\n    function create(uri) {\n        return { uri: uri };\n    }\n    TextDocumentIdentifier.create = create;\n    /**\n     * Checks whether the given literal conforms to the [TextDocumentIdentifier](#TextDocumentIdentifier) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri);\n    }\n    TextDocumentIdentifier.is = is;\n})(TextDocumentIdentifier || (TextDocumentIdentifier = {}));\n/**\n * The VersionedTextDocumentIdentifier namespace provides helper functions to work with\n * [VersionedTextDocumentIdentifier](#VersionedTextDocumentIdentifier) literals.\n */\nexport var VersionedTextDocumentIdentifier;\n(function (VersionedTextDocumentIdentifier) {\n    /**\n     * Creates a new VersionedTextDocumentIdentifier literal.\n     * @param uri The document's uri.\n     * @param uri The document's text.\n     */\n    function create(uri, version) {\n        return { uri: uri, version: version };\n    }\n    VersionedTextDocumentIdentifier.create = create;\n    /**\n     * Checks whether the given literal conforms to the [VersionedTextDocumentIdentifier](#VersionedTextDocumentIdentifier) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && Is.integer(candidate.version);\n    }\n    VersionedTextDocumentIdentifier.is = is;\n})(VersionedTextDocumentIdentifier || (VersionedTextDocumentIdentifier = {}));\n/**\n * The OptionalVersionedTextDocumentIdentifier namespace provides helper functions to work with\n * [OptionalVersionedTextDocumentIdentifier](#OptionalVersionedTextDocumentIdentifier) literals.\n */\nexport var OptionalVersionedTextDocumentIdentifier;\n(function (OptionalVersionedTextDocumentIdentifier) {\n    /**\n     * Creates a new OptionalVersionedTextDocumentIdentifier literal.\n     * @param uri The document's uri.\n     * @param uri The document's text.\n     */\n    function create(uri, version) {\n        return { uri: uri, version: version };\n    }\n    OptionalVersionedTextDocumentIdentifier.create = create;\n    /**\n     * Checks whether the given literal conforms to the [OptionalVersionedTextDocumentIdentifier](#OptionalVersionedTextDocumentIdentifier) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && (candidate.version === null || Is.integer(candidate.version));\n    }\n    OptionalVersionedTextDocumentIdentifier.is = is;\n})(OptionalVersionedTextDocumentIdentifier || (OptionalVersionedTextDocumentIdentifier = {}));\n/**\n * The TextDocumentItem namespace provides helper functions to work with\n * [TextDocumentItem](#TextDocumentItem) literals.\n */\nexport var TextDocumentItem;\n(function (TextDocumentItem) {\n    /**\n     * Creates a new TextDocumentItem literal.\n     * @param uri The document's uri.\n     * @param languageId The document's language identifier.\n     * @param version The document's version number.\n     * @param text The document's text.\n     */\n    function create(uri, languageId, version, text) {\n        return { uri: uri, languageId: languageId, version: version, text: text };\n    }\n    TextDocumentItem.create = create;\n    /**\n     * Checks whether the given literal conforms to the [TextDocumentItem](#TextDocumentItem) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && Is.string(candidate.languageId) && Is.integer(candidate.version) && Is.string(candidate.text);\n    }\n    TextDocumentItem.is = is;\n})(TextDocumentItem || (TextDocumentItem = {}));\n/**\n * Describes the content type that a client supports in various\n * result literals like `Hover`, `ParameterInfo` or `CompletionItem`.\n *\n * Please note that `MarkupKinds` must not start with a `$`. This kinds\n * are reserved for internal usage.\n */\nexport var MarkupKind;\n(function (MarkupKind) {\n    /**\n     * Plain text is supported as a content format\n     */\n    MarkupKind.PlainText = 'plaintext';\n    /**\n     * Markdown is supported as a content format\n     */\n    MarkupKind.Markdown = 'markdown';\n})(MarkupKind || (MarkupKind = {}));\n(function (MarkupKind) {\n    /**\n     * Checks whether the given value is a value of the [MarkupKind](#MarkupKind) type.\n     */\n    function is(value) {\n        var candidate = value;\n        return candidate === MarkupKind.PlainText || candidate === MarkupKind.Markdown;\n    }\n    MarkupKind.is = is;\n})(MarkupKind || (MarkupKind = {}));\nexport var MarkupContent;\n(function (MarkupContent) {\n    /**\n     * Checks whether the given value conforms to the [MarkupContent](#MarkupContent) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(value) && MarkupKind.is(candidate.kind) && Is.string(candidate.value);\n    }\n    MarkupContent.is = is;\n})(MarkupContent || (MarkupContent = {}));\n/**\n * The kind of a completion entry.\n */\nexport var CompletionItemKind;\n(function (CompletionItemKind) {\n    CompletionItemKind.Text = 1;\n    CompletionItemKind.Method = 2;\n    CompletionItemKind.Function = 3;\n    CompletionItemKind.Constructor = 4;\n    CompletionItemKind.Field = 5;\n    CompletionItemKind.Variable = 6;\n    CompletionItemKind.Class = 7;\n    CompletionItemKind.Interface = 8;\n    CompletionItemKind.Module = 9;\n    CompletionItemKind.Property = 10;\n    CompletionItemKind.Unit = 11;\n    CompletionItemKind.Value = 12;\n    CompletionItemKind.Enum = 13;\n    CompletionItemKind.Keyword = 14;\n    CompletionItemKind.Snippet = 15;\n    CompletionItemKind.Color = 16;\n    CompletionItemKind.File = 17;\n    CompletionItemKind.Reference = 18;\n    CompletionItemKind.Folder = 19;\n    CompletionItemKind.EnumMember = 20;\n    CompletionItemKind.Constant = 21;\n    CompletionItemKind.Struct = 22;\n    CompletionItemKind.Event = 23;\n    CompletionItemKind.Operator = 24;\n    CompletionItemKind.TypeParameter = 25;\n})(CompletionItemKind || (CompletionItemKind = {}));\n/**\n * Defines whether the insert text in a completion item should be interpreted as\n * plain text or a snippet.\n */\nexport var InsertTextFormat;\n(function (InsertTextFormat) {\n    /**\n     * The primary text to be inserted is treated as a plain string.\n     */\n    InsertTextFormat.PlainText = 1;\n    /**\n     * The primary text to be inserted is treated as a snippet.\n     *\n     * A snippet can define tab stops and placeholders with `$1`, `$2`\n     * and `${3:foo}`. `$0` defines the final tab stop, it defaults to\n     * the end of the snippet. Placeholders with equal identifiers are linked,\n     * that is typing in one will update others too.\n     *\n     * See also: https://microsoft.github.io/language-server-protocol/specifications/specification-current/#snippet_syntax\n     */\n    InsertTextFormat.Snippet = 2;\n})(InsertTextFormat || (InsertTextFormat = {}));\n/**\n * Completion item tags are extra annotations that tweak the rendering of a completion\n * item.\n *\n * @since 3.15.0\n */\nexport var CompletionItemTag;\n(function (CompletionItemTag) {\n    /**\n     * Render a completion as obsolete, usually using a strike-out.\n     */\n    CompletionItemTag.Deprecated = 1;\n})(CompletionItemTag || (CompletionItemTag = {}));\n/**\n * The InsertReplaceEdit namespace provides functions to deal with insert / replace edits.\n *\n * @since 3.16.0\n */\nexport var InsertReplaceEdit;\n(function (InsertReplaceEdit) {\n    /**\n     * Creates a new insert / replace edit\n     */\n    function create(newText, insert, replace) {\n        return { newText: newText, insert: insert, replace: replace };\n    }\n    InsertReplaceEdit.create = create;\n    /**\n     * Checks whether the given literal conforms to the [InsertReplaceEdit](#InsertReplaceEdit) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return candidate && Is.string(candidate.newText) && Range.is(candidate.insert) && Range.is(candidate.replace);\n    }\n    InsertReplaceEdit.is = is;\n})(InsertReplaceEdit || (InsertReplaceEdit = {}));\n/**\n * How whitespace and indentation is handled during completion\n * item insertion.\n *\n * @since 3.16.0\n */\nexport var InsertTextMode;\n(function (InsertTextMode) {\n    /**\n     * The insertion or replace strings is taken as it is. If the\n     * value is multi line the lines below the cursor will be\n     * inserted using the indentation defined in the string value.\n     * The client will not apply any kind of adjustments to the\n     * string.\n     */\n    InsertTextMode.asIs = 1;\n    /**\n     * The editor adjusts leading whitespace of new lines so that\n     * they match the indentation up to the cursor of the line for\n     * which the item is accepted.\n     *\n     * Consider a line like this: <2tabs><cursor><3tabs>foo. Accepting a\n     * multi line completion item is indented using 2 tabs and all\n     * following lines inserted will be indented using 2 tabs as well.\n     */\n    InsertTextMode.adjustIndentation = 2;\n})(InsertTextMode || (InsertTextMode = {}));\n/**\n * The CompletionItem namespace provides functions to deal with\n * completion items.\n */\nexport var CompletionItem;\n(function (CompletionItem) {\n    /**\n     * Create a completion item and seed it with a label.\n     * @param label The completion item's label\n     */\n    function create(label) {\n        return { label: label };\n    }\n    CompletionItem.create = create;\n})(CompletionItem || (CompletionItem = {}));\n/**\n * The CompletionList namespace provides functions to deal with\n * completion lists.\n */\nexport var CompletionList;\n(function (CompletionList) {\n    /**\n     * Creates a new completion list.\n     *\n     * @param items The completion items.\n     * @param isIncomplete The list is not complete.\n     */\n    function create(items, isIncomplete) {\n        return { items: items ? items : [], isIncomplete: !!isIncomplete };\n    }\n    CompletionList.create = create;\n})(CompletionList || (CompletionList = {}));\nexport var MarkedString;\n(function (MarkedString) {\n    /**\n     * Creates a marked string from plain text.\n     *\n     * @param plainText The plain text.\n     */\n    function fromPlainText(plainText) {\n        return plainText.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, '\\\\$&'); // escape markdown syntax tokens: http://daringfireball.net/projects/markdown/syntax#backslash\n    }\n    MarkedString.fromPlainText = fromPlainText;\n    /**\n     * Checks whether the given value conforms to the [MarkedString](#MarkedString) type.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.string(candidate) || (Is.objectLiteral(candidate) && Is.string(candidate.language) && Is.string(candidate.value));\n    }\n    MarkedString.is = is;\n})(MarkedString || (MarkedString = {}));\nexport var Hover;\n(function (Hover) {\n    /**\n     * Checks whether the given value conforms to the [Hover](#Hover) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return !!candidate && Is.objectLiteral(candidate) && (MarkupContent.is(candidate.contents) ||\n            MarkedString.is(candidate.contents) ||\n            Is.typedArray(candidate.contents, MarkedString.is)) && (value.range === undefined || Range.is(value.range));\n    }\n    Hover.is = is;\n})(Hover || (Hover = {}));\n/**\n * The ParameterInformation namespace provides helper functions to work with\n * [ParameterInformation](#ParameterInformation) literals.\n */\nexport var ParameterInformation;\n(function (ParameterInformation) {\n    /**\n     * Creates a new parameter information literal.\n     *\n     * @param label A label string.\n     * @param documentation A doc string.\n     */\n    function create(label, documentation) {\n        return documentation ? { label: label, documentation: documentation } : { label: label };\n    }\n    ParameterInformation.create = create;\n})(ParameterInformation || (ParameterInformation = {}));\n/**\n * The SignatureInformation namespace provides helper functions to work with\n * [SignatureInformation](#SignatureInformation) literals.\n */\nexport var SignatureInformation;\n(function (SignatureInformation) {\n    function create(label, documentation) {\n        var parameters = [];\n        for (var _i = 2; _i < arguments.length; _i++) {\n            parameters[_i - 2] = arguments[_i];\n        }\n        var result = { label: label };\n        if (Is.defined(documentation)) {\n            result.documentation = documentation;\n        }\n        if (Is.defined(parameters)) {\n            result.parameters = parameters;\n        }\n        else {\n            result.parameters = [];\n        }\n        return result;\n    }\n    SignatureInformation.create = create;\n})(SignatureInformation || (SignatureInformation = {}));\n/**\n * A document highlight kind.\n */\nexport var DocumentHighlightKind;\n(function (DocumentHighlightKind) {\n    /**\n     * A textual occurrence.\n     */\n    DocumentHighlightKind.Text = 1;\n    /**\n     * Read-access of a symbol, like reading a variable.\n     */\n    DocumentHighlightKind.Read = 2;\n    /**\n     * Write-access of a symbol, like writing to a variable.\n     */\n    DocumentHighlightKind.Write = 3;\n})(DocumentHighlightKind || (DocumentHighlightKind = {}));\n/**\n * DocumentHighlight namespace to provide helper functions to work with\n * [DocumentHighlight](#DocumentHighlight) literals.\n */\nexport var DocumentHighlight;\n(function (DocumentHighlight) {\n    /**\n     * Create a DocumentHighlight object.\n     * @param range The range the highlight applies to.\n     */\n    function create(range, kind) {\n        var result = { range: range };\n        if (Is.number(kind)) {\n            result.kind = kind;\n        }\n        return result;\n    }\n    DocumentHighlight.create = create;\n})(DocumentHighlight || (DocumentHighlight = {}));\n/**\n * A symbol kind.\n */\nexport var SymbolKind;\n(function (SymbolKind) {\n    SymbolKind.File = 1;\n    SymbolKind.Module = 2;\n    SymbolKind.Namespace = 3;\n    SymbolKind.Package = 4;\n    SymbolKind.Class = 5;\n    SymbolKind.Method = 6;\n    SymbolKind.Property = 7;\n    SymbolKind.Field = 8;\n    SymbolKind.Constructor = 9;\n    SymbolKind.Enum = 10;\n    SymbolKind.Interface = 11;\n    SymbolKind.Function = 12;\n    SymbolKind.Variable = 13;\n    SymbolKind.Constant = 14;\n    SymbolKind.String = 15;\n    SymbolKind.Number = 16;\n    SymbolKind.Boolean = 17;\n    SymbolKind.Array = 18;\n    SymbolKind.Object = 19;\n    SymbolKind.Key = 20;\n    SymbolKind.Null = 21;\n    SymbolKind.EnumMember = 22;\n    SymbolKind.Struct = 23;\n    SymbolKind.Event = 24;\n    SymbolKind.Operator = 25;\n    SymbolKind.TypeParameter = 26;\n})(SymbolKind || (SymbolKind = {}));\n/**\n * Symbol tags are extra annotations that tweak the rendering of a symbol.\n * @since 3.16\n */\nexport var SymbolTag;\n(function (SymbolTag) {\n    /**\n     * Render a symbol as obsolete, usually using a strike-out.\n     */\n    SymbolTag.Deprecated = 1;\n})(SymbolTag || (SymbolTag = {}));\nexport var SymbolInformation;\n(function (SymbolInformation) {\n    /**\n     * Creates a new symbol information literal.\n     *\n     * @param name The name of the symbol.\n     * @param kind The kind of the symbol.\n     * @param range The range of the location of the symbol.\n     * @param uri The resource of the location of symbol, defaults to the current document.\n     * @param containerName The name of the symbol containing the symbol.\n     */\n    function create(name, kind, range, uri, containerName) {\n        var result = {\n            name: name,\n            kind: kind,\n            location: { uri: uri, range: range }\n        };\n        if (containerName) {\n            result.containerName = containerName;\n        }\n        return result;\n    }\n    SymbolInformation.create = create;\n})(SymbolInformation || (SymbolInformation = {}));\nexport var DocumentSymbol;\n(function (DocumentSymbol) {\n    /**\n     * Creates a new symbol information literal.\n     *\n     * @param name The name of the symbol.\n     * @param detail The detail of the symbol.\n     * @param kind The kind of the symbol.\n     * @param range The range of the symbol.\n     * @param selectionRange The selectionRange of the symbol.\n     * @param children Children of the symbol.\n     */\n    function create(name, detail, kind, range, selectionRange, children) {\n        var result = {\n            name: name,\n            detail: detail,\n            kind: kind,\n            range: range,\n            selectionRange: selectionRange\n        };\n        if (children !== undefined) {\n            result.children = children;\n        }\n        return result;\n    }\n    DocumentSymbol.create = create;\n    /**\n     * Checks whether the given literal conforms to the [DocumentSymbol](#DocumentSymbol) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return candidate &&\n            Is.string(candidate.name) && Is.number(candidate.kind) &&\n            Range.is(candidate.range) && Range.is(candidate.selectionRange) &&\n            (candidate.detail === undefined || Is.string(candidate.detail)) &&\n            (candidate.deprecated === undefined || Is.boolean(candidate.deprecated)) &&\n            (candidate.children === undefined || Array.isArray(candidate.children)) &&\n            (candidate.tags === undefined || Array.isArray(candidate.tags));\n    }\n    DocumentSymbol.is = is;\n})(DocumentSymbol || (DocumentSymbol = {}));\n/**\n * A set of predefined code action kinds\n */\nexport var CodeActionKind;\n(function (CodeActionKind) {\n    /**\n     * Empty kind.\n     */\n    CodeActionKind.Empty = '';\n    /**\n     * Base kind for quickfix actions: 'quickfix'\n     */\n    CodeActionKind.QuickFix = 'quickfix';\n    /**\n     * Base kind for refactoring actions: 'refactor'\n     */\n    CodeActionKind.Refactor = 'refactor';\n    /**\n     * Base kind for refactoring extraction actions: 'refactor.extract'\n     *\n     * Example extract actions:\n     *\n     * - Extract method\n     * - Extract function\n     * - Extract variable\n     * - Extract interface from class\n     * - ...\n     */\n    CodeActionKind.RefactorExtract = 'refactor.extract';\n    /**\n     * Base kind for refactoring inline actions: 'refactor.inline'\n     *\n     * Example inline actions:\n     *\n     * - Inline function\n     * - Inline variable\n     * - Inline constant\n     * - ...\n     */\n    CodeActionKind.RefactorInline = 'refactor.inline';\n    /**\n     * Base kind for refactoring rewrite actions: 'refactor.rewrite'\n     *\n     * Example rewrite actions:\n     *\n     * - Convert JavaScript function to class\n     * - Add or remove parameter\n     * - Encapsulate field\n     * - Make method static\n     * - Move method to base class\n     * - ...\n     */\n    CodeActionKind.RefactorRewrite = 'refactor.rewrite';\n    /**\n     * Base kind for source actions: `source`\n     *\n     * Source code actions apply to the entire file.\n     */\n    CodeActionKind.Source = 'source';\n    /**\n     * Base kind for an organize imports source action: `source.organizeImports`\n     */\n    CodeActionKind.SourceOrganizeImports = 'source.organizeImports';\n    /**\n     * Base kind for auto-fix source actions: `source.fixAll`.\n     *\n     * Fix all actions automatically fix errors that have a clear fix that do not require user input.\n     * They should not suppress errors or perform unsafe fixes such as generating new types or classes.\n     *\n     * @since 3.15.0\n     */\n    CodeActionKind.SourceFixAll = 'source.fixAll';\n})(CodeActionKind || (CodeActionKind = {}));\n/**\n * The CodeActionContext namespace provides helper functions to work with\n * [CodeActionContext](#CodeActionContext) literals.\n */\nexport var CodeActionContext;\n(function (CodeActionContext) {\n    /**\n     * Creates a new CodeActionContext literal.\n     */\n    function create(diagnostics, only) {\n        var result = { diagnostics: diagnostics };\n        if (only !== undefined && only !== null) {\n            result.only = only;\n        }\n        return result;\n    }\n    CodeActionContext.create = create;\n    /**\n     * Checks whether the given literal conforms to the [CodeActionContext](#CodeActionContext) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.typedArray(candidate.diagnostics, Diagnostic.is) && (candidate.only === undefined || Is.typedArray(candidate.only, Is.string));\n    }\n    CodeActionContext.is = is;\n})(CodeActionContext || (CodeActionContext = {}));\nexport var CodeAction;\n(function (CodeAction) {\n    function create(title, kindOrCommandOrEdit, kind) {\n        var result = { title: title };\n        var checkKind = true;\n        if (typeof kindOrCommandOrEdit === 'string') {\n            checkKind = false;\n            result.kind = kindOrCommandOrEdit;\n        }\n        else if (Command.is(kindOrCommandOrEdit)) {\n            result.command = kindOrCommandOrEdit;\n        }\n        else {\n            result.edit = kindOrCommandOrEdit;\n        }\n        if (checkKind && kind !== undefined) {\n            result.kind = kind;\n        }\n        return result;\n    }\n    CodeAction.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate && Is.string(candidate.title) &&\n            (candidate.diagnostics === undefined || Is.typedArray(candidate.diagnostics, Diagnostic.is)) &&\n            (candidate.kind === undefined || Is.string(candidate.kind)) &&\n            (candidate.edit !== undefined || candidate.command !== undefined) &&\n            (candidate.command === undefined || Command.is(candidate.command)) &&\n            (candidate.isPreferred === undefined || Is.boolean(candidate.isPreferred)) &&\n            (candidate.edit === undefined || WorkspaceEdit.is(candidate.edit));\n    }\n    CodeAction.is = is;\n})(CodeAction || (CodeAction = {}));\n/**\n * The CodeLens namespace provides helper functions to work with\n * [CodeLens](#CodeLens) literals.\n */\nexport var CodeLens;\n(function (CodeLens) {\n    /**\n     * Creates a new CodeLens literal.\n     */\n    function create(range, data) {\n        var result = { range: range };\n        if (Is.defined(data)) {\n            result.data = data;\n        }\n        return result;\n    }\n    CodeLens.create = create;\n    /**\n     * Checks whether the given literal conforms to the [CodeLens](#CodeLens) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.command) || Command.is(candidate.command));\n    }\n    CodeLens.is = is;\n})(CodeLens || (CodeLens = {}));\n/**\n * The FormattingOptions namespace provides helper functions to work with\n * [FormattingOptions](#FormattingOptions) literals.\n */\nexport var FormattingOptions;\n(function (FormattingOptions) {\n    /**\n     * Creates a new FormattingOptions literal.\n     */\n    function create(tabSize, insertSpaces) {\n        return { tabSize: tabSize, insertSpaces: insertSpaces };\n    }\n    FormattingOptions.create = create;\n    /**\n     * Checks whether the given literal conforms to the [FormattingOptions](#FormattingOptions) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.uinteger(candidate.tabSize) && Is.boolean(candidate.insertSpaces);\n    }\n    FormattingOptions.is = is;\n})(FormattingOptions || (FormattingOptions = {}));\n/**\n * The DocumentLink namespace provides helper functions to work with\n * [DocumentLink](#DocumentLink) literals.\n */\nexport var DocumentLink;\n(function (DocumentLink) {\n    /**\n     * Creates a new DocumentLink literal.\n     */\n    function create(range, target, data) {\n        return { range: range, target: target, data: data };\n    }\n    DocumentLink.create = create;\n    /**\n     * Checks whether the given literal conforms to the [DocumentLink](#DocumentLink) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.target) || Is.string(candidate.target));\n    }\n    DocumentLink.is = is;\n})(DocumentLink || (DocumentLink = {}));\n/**\n * The SelectionRange namespace provides helper function to work with\n * SelectionRange literals.\n */\nexport var SelectionRange;\n(function (SelectionRange) {\n    /**\n     * Creates a new SelectionRange\n     * @param range the range.\n     * @param parent an optional parent.\n     */\n    function create(range, parent) {\n        return { range: range, parent: parent };\n    }\n    SelectionRange.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate !== undefined && Range.is(candidate.range) && (candidate.parent === undefined || SelectionRange.is(candidate.parent));\n    }\n    SelectionRange.is = is;\n})(SelectionRange || (SelectionRange = {}));\nexport var EOL = ['\\n', '\\r\\n', '\\r'];\n/**\n * @deprecated Use the text document from the new vscode-languageserver-textdocument package.\n */\nexport var TextDocument;\n(function (TextDocument) {\n    /**\n     * Creates a new ITextDocument literal from the given uri and content.\n     * @param uri The document's uri.\n     * @param languageId  The document's language Id.\n     * @param content The document's content.\n     */\n    function create(uri, languageId, version, content) {\n        return new FullTextDocument(uri, languageId, version, content);\n    }\n    TextDocument.create = create;\n    /**\n     * Checks whether the given literal conforms to the [ITextDocument](#ITextDocument) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && (Is.undefined(candidate.languageId) || Is.string(candidate.languageId)) && Is.uinteger(candidate.lineCount)\n            && Is.func(candidate.getText) && Is.func(candidate.positionAt) && Is.func(candidate.offsetAt) ? true : false;\n    }\n    TextDocument.is = is;\n    function applyEdits(document, edits) {\n        var text = document.getText();\n        var sortedEdits = mergeSort(edits, function (a, b) {\n            var diff = a.range.start.line - b.range.start.line;\n            if (diff === 0) {\n                return a.range.start.character - b.range.start.character;\n            }\n            return diff;\n        });\n        var lastModifiedOffset = text.length;\n        for (var i = sortedEdits.length - 1; i >= 0; i--) {\n            var e = sortedEdits[i];\n            var startOffset = document.offsetAt(e.range.start);\n            var endOffset = document.offsetAt(e.range.end);\n            if (endOffset <= lastModifiedOffset) {\n                text = text.substring(0, startOffset) + e.newText + text.substring(endOffset, text.length);\n            }\n            else {\n                throw new Error('Overlapping edit');\n            }\n            lastModifiedOffset = startOffset;\n        }\n        return text;\n    }\n    TextDocument.applyEdits = applyEdits;\n    function mergeSort(data, compare) {\n        if (data.length <= 1) {\n            // sorted\n            return data;\n        }\n        var p = (data.length / 2) | 0;\n        var left = data.slice(0, p);\n        var right = data.slice(p);\n        mergeSort(left, compare);\n        mergeSort(right, compare);\n        var leftIdx = 0;\n        var rightIdx = 0;\n        var i = 0;\n        while (leftIdx < left.length && rightIdx < right.length) {\n            var ret = compare(left[leftIdx], right[rightIdx]);\n            if (ret <= 0) {\n                // smaller_equal -> take left to preserve order\n                data[i++] = left[leftIdx++];\n            }\n            else {\n                // greater -> take right\n                data[i++] = right[rightIdx++];\n            }\n        }\n        while (leftIdx < left.length) {\n            data[i++] = left[leftIdx++];\n        }\n        while (rightIdx < right.length) {\n            data[i++] = right[rightIdx++];\n        }\n        return data;\n    }\n})(TextDocument || (TextDocument = {}));\n/**\n * @deprecated Use the text document from the new vscode-languageserver-textdocument package.\n */\nvar FullTextDocument = /** @class */ (function () {\n    function FullTextDocument(uri, languageId, version, content) {\n        this._uri = uri;\n        this._languageId = languageId;\n        this._version = version;\n        this._content = content;\n        this._lineOffsets = undefined;\n    }\n    Object.defineProperty(FullTextDocument.prototype, \"uri\", {\n        get: function () {\n            return this._uri;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(FullTextDocument.prototype, \"languageId\", {\n        get: function () {\n            return this._languageId;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(FullTextDocument.prototype, \"version\", {\n        get: function () {\n            return this._version;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    FullTextDocument.prototype.getText = function (range) {\n        if (range) {\n            var start = this.offsetAt(range.start);\n            var end = this.offsetAt(range.end);\n            return this._content.substring(start, end);\n        }\n        return this._content;\n    };\n    FullTextDocument.prototype.update = function (event, version) {\n        this._content = event.text;\n        this._version = version;\n        this._lineOffsets = undefined;\n    };\n    FullTextDocument.prototype.getLineOffsets = function () {\n        if (this._lineOffsets === undefined) {\n            var lineOffsets = [];\n            var text = this._content;\n            var isLineStart = true;\n            for (var i = 0; i < text.length; i++) {\n                if (isLineStart) {\n                    lineOffsets.push(i);\n                    isLineStart = false;\n                }\n                var ch = text.charAt(i);\n                isLineStart = (ch === '\\r' || ch === '\\n');\n                if (ch === '\\r' && i + 1 < text.length && text.charAt(i + 1) === '\\n') {\n                    i++;\n                }\n            }\n            if (isLineStart && text.length > 0) {\n                lineOffsets.push(text.length);\n            }\n            this._lineOffsets = lineOffsets;\n        }\n        return this._lineOffsets;\n    };\n    FullTextDocument.prototype.positionAt = function (offset) {\n        offset = Math.max(Math.min(offset, this._content.length), 0);\n        var lineOffsets = this.getLineOffsets();\n        var low = 0, high = lineOffsets.length;\n        if (high === 0) {\n            return Position.create(0, offset);\n        }\n        while (low < high) {\n            var mid = Math.floor((low + high) / 2);\n            if (lineOffsets[mid] > offset) {\n                high = mid;\n            }\n            else {\n                low = mid + 1;\n            }\n        }\n        // low is the least x for which the line offset is larger than the current offset\n        // or array.length if no line offset is larger than the current offset\n        var line = low - 1;\n        return Position.create(line, offset - lineOffsets[line]);\n    };\n    FullTextDocument.prototype.offsetAt = function (position) {\n        var lineOffsets = this.getLineOffsets();\n        if (position.line >= lineOffsets.length) {\n            return this._content.length;\n        }\n        else if (position.line < 0) {\n            return 0;\n        }\n        var lineOffset = lineOffsets[position.line];\n        var nextLineOffset = (position.line + 1 < lineOffsets.length) ? lineOffsets[position.line + 1] : this._content.length;\n        return Math.max(Math.min(lineOffset + position.character, nextLineOffset), lineOffset);\n    };\n    Object.defineProperty(FullTextDocument.prototype, \"lineCount\", {\n        get: function () {\n            return this.getLineOffsets().length;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return FullTextDocument;\n}());\nvar Is;\n(function (Is) {\n    var toString = Object.prototype.toString;\n    function defined(value) {\n        return typeof value !== 'undefined';\n    }\n    Is.defined = defined;\n    function undefined(value) {\n        return typeof value === 'undefined';\n    }\n    Is.undefined = undefined;\n    function boolean(value) {\n        return value === true || value === false;\n    }\n    Is.boolean = boolean;\n    function string(value) {\n        return toString.call(value) === '[object String]';\n    }\n    Is.string = string;\n    function number(value) {\n        return toString.call(value) === '[object Number]';\n    }\n    Is.number = number;\n    function numberRange(value, min, max) {\n        return toString.call(value) === '[object Number]' && min <= value && value <= max;\n    }\n    Is.numberRange = numberRange;\n    function integer(value) {\n        return toString.call(value) === '[object Number]' && -********** <= value && value <= **********;\n    }\n    Is.integer = integer;\n    function uinteger(value) {\n        return toString.call(value) === '[object Number]' && 0 <= value && value <= **********;\n    }\n    Is.uinteger = uinteger;\n    function func(value) {\n        return toString.call(value) === '[object Function]';\n    }\n    Is.func = func;\n    function objectLiteral(value) {\n        // Strictly speaking class instances pass this check as well. Since the LSP\n        // doesn't use classes we ignore this for now. If we do we need to add something\n        // like this: `Object.getPrototypeOf(Object.getPrototypeOf(x)) === null`\n        return value !== null && typeof value === 'object';\n    }\n    Is.objectLiteral = objectLiteral;\n    function typedArray(value, check) {\n        return Array.isArray(value) && value.every(check);\n    }\n    Is.typedArray = typedArray;\n})(Is || (Is = {}));\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { <PERSON><PERSON>, <PERSON>, editor, languages, MarkerSeverity } from './fillers/monaco-editor-core.js';\nimport * as lsTypes from './_deps/vscode-languageserver-types/main.js';\n// --- diagnostics --- ---\nvar DiagnosticsAdapter = /** @class */ (function () {\n    function DiagnosticsAdapter(_languageId, _worker, defaults) {\n        var _this = this;\n        this._languageId = _languageId;\n        this._worker = _worker;\n        this._disposables = [];\n        this._listener = Object.create(null);\n        var onModelAdd = function (model) {\n            var modeId = model.getLanguageId();\n            if (modeId !== _this._languageId) {\n                return;\n            }\n            var handle;\n            _this._listener[model.uri.toString()] = model.onDidChangeContent(function () {\n                clearTimeout(handle);\n                handle = window.setTimeout(function () { return _this._doValidate(model.uri, modeId); }, 500);\n            });\n            _this._doValidate(model.uri, modeId);\n        };\n        var onModelRemoved = function (model) {\n            editor.setModelMarkers(model, _this._languageId, []);\n            var uriStr = model.uri.toString();\n            var listener = _this._listener[uriStr];\n            if (listener) {\n                listener.dispose();\n                delete _this._listener[uriStr];\n            }\n        };\n        this._disposables.push(editor.onDidCreateModel(onModelAdd));\n        this._disposables.push(editor.onWillDisposeModel(function (model) {\n            onModelRemoved(model);\n            _this._resetSchema(model.uri);\n        }));\n        this._disposables.push(editor.onDidChangeModelLanguage(function (event) {\n            onModelRemoved(event.model);\n            onModelAdd(event.model);\n            _this._resetSchema(event.model.uri);\n        }));\n        this._disposables.push(defaults.onDidChange(function (_) {\n            editor.getModels().forEach(function (model) {\n                if (model.getLanguageId() === _this._languageId) {\n                    onModelRemoved(model);\n                    onModelAdd(model);\n                }\n            });\n        }));\n        this._disposables.push({\n            dispose: function () {\n                editor.getModels().forEach(onModelRemoved);\n                for (var key in _this._listener) {\n                    _this._listener[key].dispose();\n                }\n            }\n        });\n        editor.getModels().forEach(onModelAdd);\n    }\n    DiagnosticsAdapter.prototype.dispose = function () {\n        this._disposables.forEach(function (d) { return d && d.dispose(); });\n        this._disposables = [];\n    };\n    DiagnosticsAdapter.prototype._resetSchema = function (resource) {\n        this._worker().then(function (worker) {\n            worker.resetSchema(resource.toString());\n        });\n    };\n    DiagnosticsAdapter.prototype._doValidate = function (resource, languageId) {\n        this._worker(resource)\n            .then(function (worker) {\n            return worker.doValidation(resource.toString()).then(function (diagnostics) {\n                var markers = diagnostics.map(function (d) { return toDiagnostics(resource, d); });\n                var model = editor.getModel(resource);\n                if (model && model.getLanguageId() === languageId) {\n                    editor.setModelMarkers(model, languageId, markers);\n                }\n            });\n        })\n            .then(undefined, function (err) {\n            console.error(err);\n        });\n    };\n    return DiagnosticsAdapter;\n}());\nexport { DiagnosticsAdapter };\nfunction toSeverity(lsSeverity) {\n    switch (lsSeverity) {\n        case lsTypes.DiagnosticSeverity.Error:\n            return MarkerSeverity.Error;\n        case lsTypes.DiagnosticSeverity.Warning:\n            return MarkerSeverity.Warning;\n        case lsTypes.DiagnosticSeverity.Information:\n            return MarkerSeverity.Info;\n        case lsTypes.DiagnosticSeverity.Hint:\n            return MarkerSeverity.Hint;\n        default:\n            return MarkerSeverity.Info;\n    }\n}\nfunction toDiagnostics(resource, diag) {\n    var code = typeof diag.code === 'number' ? String(diag.code) : diag.code;\n    return {\n        severity: toSeverity(diag.severity),\n        startLineNumber: diag.range.start.line + 1,\n        startColumn: diag.range.start.character + 1,\n        endLineNumber: diag.range.end.line + 1,\n        endColumn: diag.range.end.character + 1,\n        message: diag.message,\n        code: code,\n        source: diag.source\n    };\n}\n// --- completion ------\nfunction fromPosition(position) {\n    if (!position) {\n        return void 0;\n    }\n    return { character: position.column - 1, line: position.lineNumber - 1 };\n}\nfunction fromRange(range) {\n    if (!range) {\n        return void 0;\n    }\n    return {\n        start: {\n            line: range.startLineNumber - 1,\n            character: range.startColumn - 1\n        },\n        end: { line: range.endLineNumber - 1, character: range.endColumn - 1 }\n    };\n}\nfunction toRange(range) {\n    if (!range) {\n        return void 0;\n    }\n    return new Range(range.start.line + 1, range.start.character + 1, range.end.line + 1, range.end.character + 1);\n}\nfunction isInsertReplaceEdit(edit) {\n    return (typeof edit.insert !== 'undefined' &&\n        typeof edit.replace !== 'undefined');\n}\nfunction toCompletionItemKind(kind) {\n    var mItemKind = languages.CompletionItemKind;\n    switch (kind) {\n        case lsTypes.CompletionItemKind.Text:\n            return mItemKind.Text;\n        case lsTypes.CompletionItemKind.Method:\n            return mItemKind.Method;\n        case lsTypes.CompletionItemKind.Function:\n            return mItemKind.Function;\n        case lsTypes.CompletionItemKind.Constructor:\n            return mItemKind.Constructor;\n        case lsTypes.CompletionItemKind.Field:\n            return mItemKind.Field;\n        case lsTypes.CompletionItemKind.Variable:\n            return mItemKind.Variable;\n        case lsTypes.CompletionItemKind.Class:\n            return mItemKind.Class;\n        case lsTypes.CompletionItemKind.Interface:\n            return mItemKind.Interface;\n        case lsTypes.CompletionItemKind.Module:\n            return mItemKind.Module;\n        case lsTypes.CompletionItemKind.Property:\n            return mItemKind.Property;\n        case lsTypes.CompletionItemKind.Unit:\n            return mItemKind.Unit;\n        case lsTypes.CompletionItemKind.Value:\n            return mItemKind.Value;\n        case lsTypes.CompletionItemKind.Enum:\n            return mItemKind.Enum;\n        case lsTypes.CompletionItemKind.Keyword:\n            return mItemKind.Keyword;\n        case lsTypes.CompletionItemKind.Snippet:\n            return mItemKind.Snippet;\n        case lsTypes.CompletionItemKind.Color:\n            return mItemKind.Color;\n        case lsTypes.CompletionItemKind.File:\n            return mItemKind.File;\n        case lsTypes.CompletionItemKind.Reference:\n            return mItemKind.Reference;\n    }\n    return mItemKind.Property;\n}\nfunction fromCompletionItemKind(kind) {\n    var mItemKind = languages.CompletionItemKind;\n    switch (kind) {\n        case mItemKind.Text:\n            return lsTypes.CompletionItemKind.Text;\n        case mItemKind.Method:\n            return lsTypes.CompletionItemKind.Method;\n        case mItemKind.Function:\n            return lsTypes.CompletionItemKind.Function;\n        case mItemKind.Constructor:\n            return lsTypes.CompletionItemKind.Constructor;\n        case mItemKind.Field:\n            return lsTypes.CompletionItemKind.Field;\n        case mItemKind.Variable:\n            return lsTypes.CompletionItemKind.Variable;\n        case mItemKind.Class:\n            return lsTypes.CompletionItemKind.Class;\n        case mItemKind.Interface:\n            return lsTypes.CompletionItemKind.Interface;\n        case mItemKind.Module:\n            return lsTypes.CompletionItemKind.Module;\n        case mItemKind.Property:\n            return lsTypes.CompletionItemKind.Property;\n        case mItemKind.Unit:\n            return lsTypes.CompletionItemKind.Unit;\n        case mItemKind.Value:\n            return lsTypes.CompletionItemKind.Value;\n        case mItemKind.Enum:\n            return lsTypes.CompletionItemKind.Enum;\n        case mItemKind.Keyword:\n            return lsTypes.CompletionItemKind.Keyword;\n        case mItemKind.Snippet:\n            return lsTypes.CompletionItemKind.Snippet;\n        case mItemKind.Color:\n            return lsTypes.CompletionItemKind.Color;\n        case mItemKind.File:\n            return lsTypes.CompletionItemKind.File;\n        case mItemKind.Reference:\n            return lsTypes.CompletionItemKind.Reference;\n    }\n    return lsTypes.CompletionItemKind.Property;\n}\nfunction toTextEdit(textEdit) {\n    if (!textEdit) {\n        return void 0;\n    }\n    return {\n        range: toRange(textEdit.range),\n        text: textEdit.newText\n    };\n}\nfunction toCommand(c) {\n    return c && c.command === 'editor.action.triggerSuggest'\n        ? { id: c.command, title: c.title, arguments: c.arguments }\n        : undefined;\n}\nvar CompletionAdapter = /** @class */ (function () {\n    function CompletionAdapter(_worker) {\n        this._worker = _worker;\n    }\n    Object.defineProperty(CompletionAdapter.prototype, \"triggerCharacters\", {\n        get: function () {\n            return [' ', ':', '\"'];\n        },\n        enumerable: false,\n        configurable: true\n    });\n    CompletionAdapter.prototype.provideCompletionItems = function (model, position, context, token) {\n        var resource = model.uri;\n        return this._worker(resource)\n            .then(function (worker) {\n            return worker.doComplete(resource.toString(), fromPosition(position));\n        })\n            .then(function (info) {\n            if (!info) {\n                return;\n            }\n            var wordInfo = model.getWordUntilPosition(position);\n            var wordRange = new Range(position.lineNumber, wordInfo.startColumn, position.lineNumber, wordInfo.endColumn);\n            var items = info.items.map(function (entry) {\n                var item = {\n                    label: entry.label,\n                    insertText: entry.insertText || entry.label,\n                    sortText: entry.sortText,\n                    filterText: entry.filterText,\n                    documentation: entry.documentation,\n                    detail: entry.detail,\n                    command: toCommand(entry.command),\n                    range: wordRange,\n                    kind: toCompletionItemKind(entry.kind)\n                };\n                if (entry.textEdit) {\n                    if (isInsertReplaceEdit(entry.textEdit)) {\n                        item.range = {\n                            insert: toRange(entry.textEdit.insert),\n                            replace: toRange(entry.textEdit.replace)\n                        };\n                    }\n                    else {\n                        item.range = toRange(entry.textEdit.range);\n                    }\n                    item.insertText = entry.textEdit.newText;\n                }\n                if (entry.additionalTextEdits) {\n                    item.additionalTextEdits = entry.additionalTextEdits.map(toTextEdit);\n                }\n                if (entry.insertTextFormat === lsTypes.InsertTextFormat.Snippet) {\n                    item.insertTextRules = languages.CompletionItemInsertTextRule.InsertAsSnippet;\n                }\n                return item;\n            });\n            return {\n                isIncomplete: info.isIncomplete,\n                suggestions: items\n            };\n        });\n    };\n    return CompletionAdapter;\n}());\nexport { CompletionAdapter };\nfunction isMarkupContent(thing) {\n    return (thing && typeof thing === 'object' && typeof thing.kind === 'string');\n}\nfunction toMarkdownString(entry) {\n    if (typeof entry === 'string') {\n        return {\n            value: entry\n        };\n    }\n    if (isMarkupContent(entry)) {\n        if (entry.kind === 'plaintext') {\n            return {\n                value: entry.value.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, '\\\\$&')\n            };\n        }\n        return {\n            value: entry.value\n        };\n    }\n    return { value: '```' + entry.language + '\\n' + entry.value + '\\n```\\n' };\n}\nfunction toMarkedStringArray(contents) {\n    if (!contents) {\n        return void 0;\n    }\n    if (Array.isArray(contents)) {\n        return contents.map(toMarkdownString);\n    }\n    return [toMarkdownString(contents)];\n}\n// --- hover ------\nvar HoverAdapter = /** @class */ (function () {\n    function HoverAdapter(_worker) {\n        this._worker = _worker;\n    }\n    HoverAdapter.prototype.provideHover = function (model, position, token) {\n        var resource = model.uri;\n        return this._worker(resource)\n            .then(function (worker) {\n            return worker.doHover(resource.toString(), fromPosition(position));\n        })\n            .then(function (info) {\n            if (!info) {\n                return;\n            }\n            return {\n                range: toRange(info.range),\n                contents: toMarkedStringArray(info.contents)\n            };\n        });\n    };\n    return HoverAdapter;\n}());\nexport { HoverAdapter };\n// --- definition ------\nfunction toLocation(location) {\n    return {\n        uri: Uri.parse(location.uri),\n        range: toRange(location.range)\n    };\n}\n// --- document symbols ------\nfunction toSymbolKind(kind) {\n    var mKind = languages.SymbolKind;\n    switch (kind) {\n        case lsTypes.SymbolKind.File:\n            return mKind.Array;\n        case lsTypes.SymbolKind.Module:\n            return mKind.Module;\n        case lsTypes.SymbolKind.Namespace:\n            return mKind.Namespace;\n        case lsTypes.SymbolKind.Package:\n            return mKind.Package;\n        case lsTypes.SymbolKind.Class:\n            return mKind.Class;\n        case lsTypes.SymbolKind.Method:\n            return mKind.Method;\n        case lsTypes.SymbolKind.Property:\n            return mKind.Property;\n        case lsTypes.SymbolKind.Field:\n            return mKind.Field;\n        case lsTypes.SymbolKind.Constructor:\n            return mKind.Constructor;\n        case lsTypes.SymbolKind.Enum:\n            return mKind.Enum;\n        case lsTypes.SymbolKind.Interface:\n            return mKind.Interface;\n        case lsTypes.SymbolKind.Function:\n            return mKind.Function;\n        case lsTypes.SymbolKind.Variable:\n            return mKind.Variable;\n        case lsTypes.SymbolKind.Constant:\n            return mKind.Constant;\n        case lsTypes.SymbolKind.String:\n            return mKind.String;\n        case lsTypes.SymbolKind.Number:\n            return mKind.Number;\n        case lsTypes.SymbolKind.Boolean:\n            return mKind.Boolean;\n        case lsTypes.SymbolKind.Array:\n            return mKind.Array;\n    }\n    return mKind.Function;\n}\nvar DocumentSymbolAdapter = /** @class */ (function () {\n    function DocumentSymbolAdapter(_worker) {\n        this._worker = _worker;\n    }\n    DocumentSymbolAdapter.prototype.provideDocumentSymbols = function (model, token) {\n        var resource = model.uri;\n        return this._worker(resource)\n            .then(function (worker) { return worker.findDocumentSymbols(resource.toString()); })\n            .then(function (items) {\n            if (!items) {\n                return;\n            }\n            return items.map(function (item) { return ({\n                name: item.name,\n                detail: '',\n                containerName: item.containerName,\n                kind: toSymbolKind(item.kind),\n                range: toRange(item.location.range),\n                selectionRange: toRange(item.location.range),\n                tags: []\n            }); });\n        });\n    };\n    return DocumentSymbolAdapter;\n}());\nexport { DocumentSymbolAdapter };\nfunction fromFormattingOptions(options) {\n    return {\n        tabSize: options.tabSize,\n        insertSpaces: options.insertSpaces\n    };\n}\nvar DocumentFormattingEditProvider = /** @class */ (function () {\n    function DocumentFormattingEditProvider(_worker) {\n        this._worker = _worker;\n    }\n    DocumentFormattingEditProvider.prototype.provideDocumentFormattingEdits = function (model, options, token) {\n        var resource = model.uri;\n        return this._worker(resource).then(function (worker) {\n            return worker\n                .format(resource.toString(), null, fromFormattingOptions(options))\n                .then(function (edits) {\n                if (!edits || edits.length === 0) {\n                    return;\n                }\n                return edits.map(toTextEdit);\n            });\n        });\n    };\n    return DocumentFormattingEditProvider;\n}());\nexport { DocumentFormattingEditProvider };\nvar DocumentRangeFormattingEditProvider = /** @class */ (function () {\n    function DocumentRangeFormattingEditProvider(_worker) {\n        this._worker = _worker;\n    }\n    DocumentRangeFormattingEditProvider.prototype.provideDocumentRangeFormattingEdits = function (model, range, options, token) {\n        var resource = model.uri;\n        return this._worker(resource).then(function (worker) {\n            return worker\n                .format(resource.toString(), fromRange(range), fromFormattingOptions(options))\n                .then(function (edits) {\n                if (!edits || edits.length === 0) {\n                    return;\n                }\n                return edits.map(toTextEdit);\n            });\n        });\n    };\n    return DocumentRangeFormattingEditProvider;\n}());\nexport { DocumentRangeFormattingEditProvider };\nvar DocumentColorAdapter = /** @class */ (function () {\n    function DocumentColorAdapter(_worker) {\n        this._worker = _worker;\n    }\n    DocumentColorAdapter.prototype.provideDocumentColors = function (model, token) {\n        var resource = model.uri;\n        return this._worker(resource)\n            .then(function (worker) { return worker.findDocumentColors(resource.toString()); })\n            .then(function (infos) {\n            if (!infos) {\n                return;\n            }\n            return infos.map(function (item) { return ({\n                color: item.color,\n                range: toRange(item.range)\n            }); });\n        });\n    };\n    DocumentColorAdapter.prototype.provideColorPresentations = function (model, info, token) {\n        var resource = model.uri;\n        return this._worker(resource)\n            .then(function (worker) {\n            return worker.getColorPresentations(resource.toString(), info.color, fromRange(info.range));\n        })\n            .then(function (presentations) {\n            if (!presentations) {\n                return;\n            }\n            return presentations.map(function (presentation) {\n                var item = {\n                    label: presentation.label\n                };\n                if (presentation.textEdit) {\n                    item.textEdit = toTextEdit(presentation.textEdit);\n                }\n                if (presentation.additionalTextEdits) {\n                    item.additionalTextEdits = presentation.additionalTextEdits.map(toTextEdit);\n                }\n                return item;\n            });\n        });\n    };\n    return DocumentColorAdapter;\n}());\nexport { DocumentColorAdapter };\nvar FoldingRangeAdapter = /** @class */ (function () {\n    function FoldingRangeAdapter(_worker) {\n        this._worker = _worker;\n    }\n    FoldingRangeAdapter.prototype.provideFoldingRanges = function (model, context, token) {\n        var resource = model.uri;\n        return this._worker(resource)\n            .then(function (worker) { return worker.getFoldingRanges(resource.toString(), context); })\n            .then(function (ranges) {\n            if (!ranges) {\n                return;\n            }\n            return ranges.map(function (range) {\n                var result = {\n                    start: range.startLine + 1,\n                    end: range.endLine + 1\n                };\n                if (typeof range.kind !== 'undefined') {\n                    result.kind = toFoldingRangeKind(range.kind);\n                }\n                return result;\n            });\n        });\n    };\n    return FoldingRangeAdapter;\n}());\nexport { FoldingRangeAdapter };\nfunction toFoldingRangeKind(kind) {\n    switch (kind) {\n        case lsTypes.FoldingRangeKind.Comment:\n            return languages.FoldingRangeKind.Comment;\n        case lsTypes.FoldingRangeKind.Imports:\n            return languages.FoldingRangeKind.Imports;\n        case lsTypes.FoldingRangeKind.Region:\n            return languages.FoldingRangeKind.Region;\n    }\n    return void 0;\n}\nvar SelectionRangeAdapter = /** @class */ (function () {\n    function SelectionRangeAdapter(_worker) {\n        this._worker = _worker;\n    }\n    SelectionRangeAdapter.prototype.provideSelectionRanges = function (model, positions, token) {\n        var resource = model.uri;\n        return this._worker(resource)\n            .then(function (worker) { return worker.getSelectionRanges(resource.toString(), positions.map(fromPosition)); })\n            .then(function (selectionRanges) {\n            if (!selectionRanges) {\n                return;\n            }\n            return selectionRanges.map(function (selectionRange) {\n                var result = [];\n                while (selectionRange) {\n                    result.push({ range: toRange(selectionRange.range) });\n                    selectionRange = selectionRange.parent;\n                }\n                return result;\n            });\n        });\n    };\n    return SelectionRangeAdapter;\n}());\nexport { SelectionRangeAdapter };\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n'use strict';\n/**\n * Creates a JSON scanner on the given text.\n * If ignoreTrivia is set, whitespaces or comments are ignored.\n */\nexport function createScanner(text, ignoreTrivia) {\n    if (ignoreTrivia === void 0) { ignoreTrivia = false; }\n    var len = text.length;\n    var pos = 0, value = '', tokenOffset = 0, token = 16 /* Unknown */, lineNumber = 0, lineStartOffset = 0, tokenLineStartOffset = 0, prevTokenLineStartOffset = 0, scanError = 0 /* None */;\n    function scanHexDigits(count, exact) {\n        var digits = 0;\n        var value = 0;\n        while (digits < count || !exact) {\n            var ch = text.charCodeAt(pos);\n            if (ch >= 48 /* _0 */ && ch <= 57 /* _9 */) {\n                value = value * 16 + ch - 48 /* _0 */;\n            }\n            else if (ch >= 65 /* A */ && ch <= 70 /* F */) {\n                value = value * 16 + ch - 65 /* A */ + 10;\n            }\n            else if (ch >= 97 /* a */ && ch <= 102 /* f */) {\n                value = value * 16 + ch - 97 /* a */ + 10;\n            }\n            else {\n                break;\n            }\n            pos++;\n            digits++;\n        }\n        if (digits < count) {\n            value = -1;\n        }\n        return value;\n    }\n    function setPosition(newPosition) {\n        pos = newPosition;\n        value = '';\n        tokenOffset = 0;\n        token = 16 /* Unknown */;\n        scanError = 0 /* None */;\n    }\n    function scanNumber() {\n        var start = pos;\n        if (text.charCodeAt(pos) === 48 /* _0 */) {\n            pos++;\n        }\n        else {\n            pos++;\n            while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n                pos++;\n            }\n        }\n        if (pos < text.length && text.charCodeAt(pos) === 46 /* dot */) {\n            pos++;\n            if (pos < text.length && isDigit(text.charCodeAt(pos))) {\n                pos++;\n                while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n                    pos++;\n                }\n            }\n            else {\n                scanError = 3 /* UnexpectedEndOfNumber */;\n                return text.substring(start, pos);\n            }\n        }\n        var end = pos;\n        if (pos < text.length && (text.charCodeAt(pos) === 69 /* E */ || text.charCodeAt(pos) === 101 /* e */)) {\n            pos++;\n            if (pos < text.length && text.charCodeAt(pos) === 43 /* plus */ || text.charCodeAt(pos) === 45 /* minus */) {\n                pos++;\n            }\n            if (pos < text.length && isDigit(text.charCodeAt(pos))) {\n                pos++;\n                while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n                    pos++;\n                }\n                end = pos;\n            }\n            else {\n                scanError = 3 /* UnexpectedEndOfNumber */;\n            }\n        }\n        return text.substring(start, end);\n    }\n    function scanString() {\n        var result = '', start = pos;\n        while (true) {\n            if (pos >= len) {\n                result += text.substring(start, pos);\n                scanError = 2 /* UnexpectedEndOfString */;\n                break;\n            }\n            var ch = text.charCodeAt(pos);\n            if (ch === 34 /* doubleQuote */) {\n                result += text.substring(start, pos);\n                pos++;\n                break;\n            }\n            if (ch === 92 /* backslash */) {\n                result += text.substring(start, pos);\n                pos++;\n                if (pos >= len) {\n                    scanError = 2 /* UnexpectedEndOfString */;\n                    break;\n                }\n                var ch2 = text.charCodeAt(pos++);\n                switch (ch2) {\n                    case 34 /* doubleQuote */:\n                        result += '\\\"';\n                        break;\n                    case 92 /* backslash */:\n                        result += '\\\\';\n                        break;\n                    case 47 /* slash */:\n                        result += '/';\n                        break;\n                    case 98 /* b */:\n                        result += '\\b';\n                        break;\n                    case 102 /* f */:\n                        result += '\\f';\n                        break;\n                    case 110 /* n */:\n                        result += '\\n';\n                        break;\n                    case 114 /* r */:\n                        result += '\\r';\n                        break;\n                    case 116 /* t */:\n                        result += '\\t';\n                        break;\n                    case 117 /* u */:\n                        var ch3 = scanHexDigits(4, true);\n                        if (ch3 >= 0) {\n                            result += String.fromCharCode(ch3);\n                        }\n                        else {\n                            scanError = 4 /* InvalidUnicode */;\n                        }\n                        break;\n                    default:\n                        scanError = 5 /* InvalidEscapeCharacter */;\n                }\n                start = pos;\n                continue;\n            }\n            if (ch >= 0 && ch <= 0x1f) {\n                if (isLineBreak(ch)) {\n                    result += text.substring(start, pos);\n                    scanError = 2 /* UnexpectedEndOfString */;\n                    break;\n                }\n                else {\n                    scanError = 6 /* InvalidCharacter */;\n                    // mark as error but continue with string\n                }\n            }\n            pos++;\n        }\n        return result;\n    }\n    function scanNext() {\n        value = '';\n        scanError = 0 /* None */;\n        tokenOffset = pos;\n        lineStartOffset = lineNumber;\n        prevTokenLineStartOffset = tokenLineStartOffset;\n        if (pos >= len) {\n            // at the end\n            tokenOffset = len;\n            return token = 17 /* EOF */;\n        }\n        var code = text.charCodeAt(pos);\n        // trivia: whitespace\n        if (isWhiteSpace(code)) {\n            do {\n                pos++;\n                value += String.fromCharCode(code);\n                code = text.charCodeAt(pos);\n            } while (isWhiteSpace(code));\n            return token = 15 /* Trivia */;\n        }\n        // trivia: newlines\n        if (isLineBreak(code)) {\n            pos++;\n            value += String.fromCharCode(code);\n            if (code === 13 /* carriageReturn */ && text.charCodeAt(pos) === 10 /* lineFeed */) {\n                pos++;\n                value += '\\n';\n            }\n            lineNumber++;\n            tokenLineStartOffset = pos;\n            return token = 14 /* LineBreakTrivia */;\n        }\n        switch (code) {\n            // tokens: []{}:,\n            case 123 /* openBrace */:\n                pos++;\n                return token = 1 /* OpenBraceToken */;\n            case 125 /* closeBrace */:\n                pos++;\n                return token = 2 /* CloseBraceToken */;\n            case 91 /* openBracket */:\n                pos++;\n                return token = 3 /* OpenBracketToken */;\n            case 93 /* closeBracket */:\n                pos++;\n                return token = 4 /* CloseBracketToken */;\n            case 58 /* colon */:\n                pos++;\n                return token = 6 /* ColonToken */;\n            case 44 /* comma */:\n                pos++;\n                return token = 5 /* CommaToken */;\n            // strings\n            case 34 /* doubleQuote */:\n                pos++;\n                value = scanString();\n                return token = 10 /* StringLiteral */;\n            // comments\n            case 47 /* slash */:\n                var start = pos - 1;\n                // Single-line comment\n                if (text.charCodeAt(pos + 1) === 47 /* slash */) {\n                    pos += 2;\n                    while (pos < len) {\n                        if (isLineBreak(text.charCodeAt(pos))) {\n                            break;\n                        }\n                        pos++;\n                    }\n                    value = text.substring(start, pos);\n                    return token = 12 /* LineCommentTrivia */;\n                }\n                // Multi-line comment\n                if (text.charCodeAt(pos + 1) === 42 /* asterisk */) {\n                    pos += 2;\n                    var safeLength = len - 1; // For lookahead.\n                    var commentClosed = false;\n                    while (pos < safeLength) {\n                        var ch = text.charCodeAt(pos);\n                        if (ch === 42 /* asterisk */ && text.charCodeAt(pos + 1) === 47 /* slash */) {\n                            pos += 2;\n                            commentClosed = true;\n                            break;\n                        }\n                        pos++;\n                        if (isLineBreak(ch)) {\n                            if (ch === 13 /* carriageReturn */ && text.charCodeAt(pos) === 10 /* lineFeed */) {\n                                pos++;\n                            }\n                            lineNumber++;\n                            tokenLineStartOffset = pos;\n                        }\n                    }\n                    if (!commentClosed) {\n                        pos++;\n                        scanError = 1 /* UnexpectedEndOfComment */;\n                    }\n                    value = text.substring(start, pos);\n                    return token = 13 /* BlockCommentTrivia */;\n                }\n                // just a single slash\n                value += String.fromCharCode(code);\n                pos++;\n                return token = 16 /* Unknown */;\n            // numbers\n            case 45 /* minus */:\n                value += String.fromCharCode(code);\n                pos++;\n                if (pos === len || !isDigit(text.charCodeAt(pos))) {\n                    return token = 16 /* Unknown */;\n                }\n            // found a minus, followed by a number so\n            // we fall through to proceed with scanning\n            // numbers\n            case 48 /* _0 */:\n            case 49 /* _1 */:\n            case 50 /* _2 */:\n            case 51 /* _3 */:\n            case 52 /* _4 */:\n            case 53 /* _5 */:\n            case 54 /* _6 */:\n            case 55 /* _7 */:\n            case 56 /* _8 */:\n            case 57 /* _9 */:\n                value += scanNumber();\n                return token = 11 /* NumericLiteral */;\n            // literals and unknown symbols\n            default:\n                // is a literal? Read the full word.\n                while (pos < len && isUnknownContentCharacter(code)) {\n                    pos++;\n                    code = text.charCodeAt(pos);\n                }\n                if (tokenOffset !== pos) {\n                    value = text.substring(tokenOffset, pos);\n                    // keywords: true, false, null\n                    switch (value) {\n                        case 'true': return token = 8 /* TrueKeyword */;\n                        case 'false': return token = 9 /* FalseKeyword */;\n                        case 'null': return token = 7 /* NullKeyword */;\n                    }\n                    return token = 16 /* Unknown */;\n                }\n                // some\n                value += String.fromCharCode(code);\n                pos++;\n                return token = 16 /* Unknown */;\n        }\n    }\n    function isUnknownContentCharacter(code) {\n        if (isWhiteSpace(code) || isLineBreak(code)) {\n            return false;\n        }\n        switch (code) {\n            case 125 /* closeBrace */:\n            case 93 /* closeBracket */:\n            case 123 /* openBrace */:\n            case 91 /* openBracket */:\n            case 34 /* doubleQuote */:\n            case 58 /* colon */:\n            case 44 /* comma */:\n            case 47 /* slash */:\n                return false;\n        }\n        return true;\n    }\n    function scanNextNonTrivia() {\n        var result;\n        do {\n            result = scanNext();\n        } while (result >= 12 /* LineCommentTrivia */ && result <= 15 /* Trivia */);\n        return result;\n    }\n    return {\n        setPosition: setPosition,\n        getPosition: function () { return pos; },\n        scan: ignoreTrivia ? scanNextNonTrivia : scanNext,\n        getToken: function () { return token; },\n        getTokenValue: function () { return value; },\n        getTokenOffset: function () { return tokenOffset; },\n        getTokenLength: function () { return pos - tokenOffset; },\n        getTokenStartLine: function () { return lineStartOffset; },\n        getTokenStartCharacter: function () { return tokenOffset - prevTokenLineStartOffset; },\n        getTokenError: function () { return scanError; },\n    };\n}\nfunction isWhiteSpace(ch) {\n    return ch === 32 /* space */ || ch === 9 /* tab */ || ch === 11 /* verticalTab */ || ch === 12 /* formFeed */ ||\n        ch === 160 /* nonBreakingSpace */ || ch === 5760 /* ogham */ || ch >= 8192 /* enQuad */ && ch <= 8203 /* zeroWidthSpace */ ||\n        ch === 8239 /* narrowNoBreakSpace */ || ch === 8287 /* mathematicalSpace */ || ch === 12288 /* ideographicSpace */ || ch === 65279 /* byteOrderMark */;\n}\nfunction isLineBreak(ch) {\n    return ch === 10 /* lineFeed */ || ch === 13 /* carriageReturn */ || ch === 8232 /* lineSeparator */ || ch === 8233 /* paragraphSeparator */;\n}\nfunction isDigit(ch) {\n    return ch >= 48 /* _0 */ && ch <= 57 /* _9 */;\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n'use strict';\nimport { createScanner } from './scanner.js';\nvar ParseOptions;\n(function (ParseOptions) {\n    ParseOptions.DEFAULT = {\n        allowTrailingComma: false\n    };\n})(ParseOptions || (ParseOptions = {}));\n/**\n * For a given offset, evaluate the location in the JSON document. Each segment in the location path is either a property name or an array index.\n */\nexport function getLocation(text, position) {\n    var segments = []; // strings or numbers\n    var earlyReturnException = new Object();\n    var previousNode = undefined;\n    var previousNodeInst = {\n        value: {},\n        offset: 0,\n        length: 0,\n        type: 'object',\n        parent: undefined\n    };\n    var isAtPropertyKey = false;\n    function setPreviousNode(value, offset, length, type) {\n        previousNodeInst.value = value;\n        previousNodeInst.offset = offset;\n        previousNodeInst.length = length;\n        previousNodeInst.type = type;\n        previousNodeInst.colonOffset = undefined;\n        previousNode = previousNodeInst;\n    }\n    try {\n        visit(text, {\n            onObjectBegin: function (offset, length) {\n                if (position <= offset) {\n                    throw earlyReturnException;\n                }\n                previousNode = undefined;\n                isAtPropertyKey = position > offset;\n                segments.push(''); // push a placeholder (will be replaced)\n            },\n            onObjectProperty: function (name, offset, length) {\n                if (position < offset) {\n                    throw earlyReturnException;\n                }\n                setPreviousNode(name, offset, length, 'property');\n                segments[segments.length - 1] = name;\n                if (position <= offset + length) {\n                    throw earlyReturnException;\n                }\n            },\n            onObjectEnd: function (offset, length) {\n                if (position <= offset) {\n                    throw earlyReturnException;\n                }\n                previousNode = undefined;\n                segments.pop();\n            },\n            onArrayBegin: function (offset, length) {\n                if (position <= offset) {\n                    throw earlyReturnException;\n                }\n                previousNode = undefined;\n                segments.push(0);\n            },\n            onArrayEnd: function (offset, length) {\n                if (position <= offset) {\n                    throw earlyReturnException;\n                }\n                previousNode = undefined;\n                segments.pop();\n            },\n            onLiteralValue: function (value, offset, length) {\n                if (position < offset) {\n                    throw earlyReturnException;\n                }\n                setPreviousNode(value, offset, length, getNodeType(value));\n                if (position <= offset + length) {\n                    throw earlyReturnException;\n                }\n            },\n            onSeparator: function (sep, offset, length) {\n                if (position <= offset) {\n                    throw earlyReturnException;\n                }\n                if (sep === ':' && previousNode && previousNode.type === 'property') {\n                    previousNode.colonOffset = offset;\n                    isAtPropertyKey = false;\n                    previousNode = undefined;\n                }\n                else if (sep === ',') {\n                    var last = segments[segments.length - 1];\n                    if (typeof last === 'number') {\n                        segments[segments.length - 1] = last + 1;\n                    }\n                    else {\n                        isAtPropertyKey = true;\n                        segments[segments.length - 1] = '';\n                    }\n                    previousNode = undefined;\n                }\n            }\n        });\n    }\n    catch (e) {\n        if (e !== earlyReturnException) {\n            throw e;\n        }\n    }\n    return {\n        path: segments,\n        previousNode: previousNode,\n        isAtPropertyKey: isAtPropertyKey,\n        matches: function (pattern) {\n            var k = 0;\n            for (var i = 0; k < pattern.length && i < segments.length; i++) {\n                if (pattern[k] === segments[i] || pattern[k] === '*') {\n                    k++;\n                }\n                else if (pattern[k] !== '**') {\n                    return false;\n                }\n            }\n            return k === pattern.length;\n        }\n    };\n}\n/**\n * Parses the given text and returns the object the JSON content represents. On invalid input, the parser tries to be as fault tolerant as possible, but still return a result.\n * Therefore always check the errors list to find out if the input was valid.\n */\nexport function parse(text, errors, options) {\n    if (errors === void 0) { errors = []; }\n    if (options === void 0) { options = ParseOptions.DEFAULT; }\n    var currentProperty = null;\n    var currentParent = [];\n    var previousParents = [];\n    function onValue(value) {\n        if (Array.isArray(currentParent)) {\n            currentParent.push(value);\n        }\n        else if (currentProperty !== null) {\n            currentParent[currentProperty] = value;\n        }\n    }\n    var visitor = {\n        onObjectBegin: function () {\n            var object = {};\n            onValue(object);\n            previousParents.push(currentParent);\n            currentParent = object;\n            currentProperty = null;\n        },\n        onObjectProperty: function (name) {\n            currentProperty = name;\n        },\n        onObjectEnd: function () {\n            currentParent = previousParents.pop();\n        },\n        onArrayBegin: function () {\n            var array = [];\n            onValue(array);\n            previousParents.push(currentParent);\n            currentParent = array;\n            currentProperty = null;\n        },\n        onArrayEnd: function () {\n            currentParent = previousParents.pop();\n        },\n        onLiteralValue: onValue,\n        onError: function (error, offset, length) {\n            errors.push({ error: error, offset: offset, length: length });\n        }\n    };\n    visit(text, visitor, options);\n    return currentParent[0];\n}\n/**\n * Parses the given text and returns a tree representation the JSON content. On invalid input, the parser tries to be as fault tolerant as possible, but still return a result.\n */\nexport function parseTree(text, errors, options) {\n    if (errors === void 0) { errors = []; }\n    if (options === void 0) { options = ParseOptions.DEFAULT; }\n    var currentParent = { type: 'array', offset: -1, length: -1, children: [], parent: undefined }; // artificial root\n    function ensurePropertyComplete(endOffset) {\n        if (currentParent.type === 'property') {\n            currentParent.length = endOffset - currentParent.offset;\n            currentParent = currentParent.parent;\n        }\n    }\n    function onValue(valueNode) {\n        currentParent.children.push(valueNode);\n        return valueNode;\n    }\n    var visitor = {\n        onObjectBegin: function (offset) {\n            currentParent = onValue({ type: 'object', offset: offset, length: -1, parent: currentParent, children: [] });\n        },\n        onObjectProperty: function (name, offset, length) {\n            currentParent = onValue({ type: 'property', offset: offset, length: -1, parent: currentParent, children: [] });\n            currentParent.children.push({ type: 'string', value: name, offset: offset, length: length, parent: currentParent });\n        },\n        onObjectEnd: function (offset, length) {\n            ensurePropertyComplete(offset + length); // in case of a missing value for a property: make sure property is complete\n            currentParent.length = offset + length - currentParent.offset;\n            currentParent = currentParent.parent;\n            ensurePropertyComplete(offset + length);\n        },\n        onArrayBegin: function (offset, length) {\n            currentParent = onValue({ type: 'array', offset: offset, length: -1, parent: currentParent, children: [] });\n        },\n        onArrayEnd: function (offset, length) {\n            currentParent.length = offset + length - currentParent.offset;\n            currentParent = currentParent.parent;\n            ensurePropertyComplete(offset + length);\n        },\n        onLiteralValue: function (value, offset, length) {\n            onValue({ type: getNodeType(value), offset: offset, length: length, parent: currentParent, value: value });\n            ensurePropertyComplete(offset + length);\n        },\n        onSeparator: function (sep, offset, length) {\n            if (currentParent.type === 'property') {\n                if (sep === ':') {\n                    currentParent.colonOffset = offset;\n                }\n                else if (sep === ',') {\n                    ensurePropertyComplete(offset);\n                }\n            }\n        },\n        onError: function (error, offset, length) {\n            errors.push({ error: error, offset: offset, length: length });\n        }\n    };\n    visit(text, visitor, options);\n    var result = currentParent.children[0];\n    if (result) {\n        delete result.parent;\n    }\n    return result;\n}\n/**\n * Finds the node at the given path in a JSON DOM.\n */\nexport function findNodeAtLocation(root, path) {\n    if (!root) {\n        return undefined;\n    }\n    var node = root;\n    for (var _i = 0, path_1 = path; _i < path_1.length; _i++) {\n        var segment = path_1[_i];\n        if (typeof segment === 'string') {\n            if (node.type !== 'object' || !Array.isArray(node.children)) {\n                return undefined;\n            }\n            var found = false;\n            for (var _a = 0, _b = node.children; _a < _b.length; _a++) {\n                var propertyNode = _b[_a];\n                if (Array.isArray(propertyNode.children) && propertyNode.children[0].value === segment) {\n                    node = propertyNode.children[1];\n                    found = true;\n                    break;\n                }\n            }\n            if (!found) {\n                return undefined;\n            }\n        }\n        else {\n            var index = segment;\n            if (node.type !== 'array' || index < 0 || !Array.isArray(node.children) || index >= node.children.length) {\n                return undefined;\n            }\n            node = node.children[index];\n        }\n    }\n    return node;\n}\n/**\n * Gets the JSON path of the given JSON DOM node\n */\nexport function getNodePath(node) {\n    if (!node.parent || !node.parent.children) {\n        return [];\n    }\n    var path = getNodePath(node.parent);\n    if (node.parent.type === 'property') {\n        var key = node.parent.children[0].value;\n        path.push(key);\n    }\n    else if (node.parent.type === 'array') {\n        var index = node.parent.children.indexOf(node);\n        if (index !== -1) {\n            path.push(index);\n        }\n    }\n    return path;\n}\n/**\n * Evaluates the JavaScript object of the given JSON DOM node\n */\nexport function getNodeValue(node) {\n    switch (node.type) {\n        case 'array':\n            return node.children.map(getNodeValue);\n        case 'object':\n            var obj = Object.create(null);\n            for (var _i = 0, _a = node.children; _i < _a.length; _i++) {\n                var prop = _a[_i];\n                var valueNode = prop.children[1];\n                if (valueNode) {\n                    obj[prop.children[0].value] = getNodeValue(valueNode);\n                }\n            }\n            return obj;\n        case 'null':\n        case 'string':\n        case 'number':\n        case 'boolean':\n            return node.value;\n        default:\n            return undefined;\n    }\n}\nexport function contains(node, offset, includeRightBound) {\n    if (includeRightBound === void 0) { includeRightBound = false; }\n    return (offset >= node.offset && offset < (node.offset + node.length)) || includeRightBound && (offset === (node.offset + node.length));\n}\n/**\n * Finds the most inner node at the given offset. If includeRightBound is set, also finds nodes that end at the given offset.\n */\nexport function findNodeAtOffset(node, offset, includeRightBound) {\n    if (includeRightBound === void 0) { includeRightBound = false; }\n    if (contains(node, offset, includeRightBound)) {\n        var children = node.children;\n        if (Array.isArray(children)) {\n            for (var i = 0; i < children.length && children[i].offset <= offset; i++) {\n                var item = findNodeAtOffset(children[i], offset, includeRightBound);\n                if (item) {\n                    return item;\n                }\n            }\n        }\n        return node;\n    }\n    return undefined;\n}\n/**\n * Parses the given text and invokes the visitor functions for each object, array and literal reached.\n */\nexport function visit(text, visitor, options) {\n    if (options === void 0) { options = ParseOptions.DEFAULT; }\n    var _scanner = createScanner(text, false);\n    function toNoArgVisit(visitFunction) {\n        return visitFunction ? function () { return visitFunction(_scanner.getTokenOffset(), _scanner.getTokenLength(), _scanner.getTokenStartLine(), _scanner.getTokenStartCharacter()); } : function () { return true; };\n    }\n    function toOneArgVisit(visitFunction) {\n        return visitFunction ? function (arg) { return visitFunction(arg, _scanner.getTokenOffset(), _scanner.getTokenLength(), _scanner.getTokenStartLine(), _scanner.getTokenStartCharacter()); } : function () { return true; };\n    }\n    var onObjectBegin = toNoArgVisit(visitor.onObjectBegin), onObjectProperty = toOneArgVisit(visitor.onObjectProperty), onObjectEnd = toNoArgVisit(visitor.onObjectEnd), onArrayBegin = toNoArgVisit(visitor.onArrayBegin), onArrayEnd = toNoArgVisit(visitor.onArrayEnd), onLiteralValue = toOneArgVisit(visitor.onLiteralValue), onSeparator = toOneArgVisit(visitor.onSeparator), onComment = toNoArgVisit(visitor.onComment), onError = toOneArgVisit(visitor.onError);\n    var disallowComments = options && options.disallowComments;\n    var allowTrailingComma = options && options.allowTrailingComma;\n    function scanNext() {\n        while (true) {\n            var token = _scanner.scan();\n            switch (_scanner.getTokenError()) {\n                case 4 /* InvalidUnicode */:\n                    handleError(14 /* InvalidUnicode */);\n                    break;\n                case 5 /* InvalidEscapeCharacter */:\n                    handleError(15 /* InvalidEscapeCharacter */);\n                    break;\n                case 3 /* UnexpectedEndOfNumber */:\n                    handleError(13 /* UnexpectedEndOfNumber */);\n                    break;\n                case 1 /* UnexpectedEndOfComment */:\n                    if (!disallowComments) {\n                        handleError(11 /* UnexpectedEndOfComment */);\n                    }\n                    break;\n                case 2 /* UnexpectedEndOfString */:\n                    handleError(12 /* UnexpectedEndOfString */);\n                    break;\n                case 6 /* InvalidCharacter */:\n                    handleError(16 /* InvalidCharacter */);\n                    break;\n            }\n            switch (token) {\n                case 12 /* LineCommentTrivia */:\n                case 13 /* BlockCommentTrivia */:\n                    if (disallowComments) {\n                        handleError(10 /* InvalidCommentToken */);\n                    }\n                    else {\n                        onComment();\n                    }\n                    break;\n                case 16 /* Unknown */:\n                    handleError(1 /* InvalidSymbol */);\n                    break;\n                case 15 /* Trivia */:\n                case 14 /* LineBreakTrivia */:\n                    break;\n                default:\n                    return token;\n            }\n        }\n    }\n    function handleError(error, skipUntilAfter, skipUntil) {\n        if (skipUntilAfter === void 0) { skipUntilAfter = []; }\n        if (skipUntil === void 0) { skipUntil = []; }\n        onError(error);\n        if (skipUntilAfter.length + skipUntil.length > 0) {\n            var token = _scanner.getToken();\n            while (token !== 17 /* EOF */) {\n                if (skipUntilAfter.indexOf(token) !== -1) {\n                    scanNext();\n                    break;\n                }\n                else if (skipUntil.indexOf(token) !== -1) {\n                    break;\n                }\n                token = scanNext();\n            }\n        }\n    }\n    function parseString(isValue) {\n        var value = _scanner.getTokenValue();\n        if (isValue) {\n            onLiteralValue(value);\n        }\n        else {\n            onObjectProperty(value);\n        }\n        scanNext();\n        return true;\n    }\n    function parseLiteral() {\n        switch (_scanner.getToken()) {\n            case 11 /* NumericLiteral */:\n                var tokenValue = _scanner.getTokenValue();\n                var value = Number(tokenValue);\n                if (isNaN(value)) {\n                    handleError(2 /* InvalidNumberFormat */);\n                    value = 0;\n                }\n                onLiteralValue(value);\n                break;\n            case 7 /* NullKeyword */:\n                onLiteralValue(null);\n                break;\n            case 8 /* TrueKeyword */:\n                onLiteralValue(true);\n                break;\n            case 9 /* FalseKeyword */:\n                onLiteralValue(false);\n                break;\n            default:\n                return false;\n        }\n        scanNext();\n        return true;\n    }\n    function parseProperty() {\n        if (_scanner.getToken() !== 10 /* StringLiteral */) {\n            handleError(3 /* PropertyNameExpected */, [], [2 /* CloseBraceToken */, 5 /* CommaToken */]);\n            return false;\n        }\n        parseString(false);\n        if (_scanner.getToken() === 6 /* ColonToken */) {\n            onSeparator(':');\n            scanNext(); // consume colon\n            if (!parseValue()) {\n                handleError(4 /* ValueExpected */, [], [2 /* CloseBraceToken */, 5 /* CommaToken */]);\n            }\n        }\n        else {\n            handleError(5 /* ColonExpected */, [], [2 /* CloseBraceToken */, 5 /* CommaToken */]);\n        }\n        return true;\n    }\n    function parseObject() {\n        onObjectBegin();\n        scanNext(); // consume open brace\n        var needsComma = false;\n        while (_scanner.getToken() !== 2 /* CloseBraceToken */ && _scanner.getToken() !== 17 /* EOF */) {\n            if (_scanner.getToken() === 5 /* CommaToken */) {\n                if (!needsComma) {\n                    handleError(4 /* ValueExpected */, [], []);\n                }\n                onSeparator(',');\n                scanNext(); // consume comma\n                if (_scanner.getToken() === 2 /* CloseBraceToken */ && allowTrailingComma) {\n                    break;\n                }\n            }\n            else if (needsComma) {\n                handleError(6 /* CommaExpected */, [], []);\n            }\n            if (!parseProperty()) {\n                handleError(4 /* ValueExpected */, [], [2 /* CloseBraceToken */, 5 /* CommaToken */]);\n            }\n            needsComma = true;\n        }\n        onObjectEnd();\n        if (_scanner.getToken() !== 2 /* CloseBraceToken */) {\n            handleError(7 /* CloseBraceExpected */, [2 /* CloseBraceToken */], []);\n        }\n        else {\n            scanNext(); // consume close brace\n        }\n        return true;\n    }\n    function parseArray() {\n        onArrayBegin();\n        scanNext(); // consume open bracket\n        var needsComma = false;\n        while (_scanner.getToken() !== 4 /* CloseBracketToken */ && _scanner.getToken() !== 17 /* EOF */) {\n            if (_scanner.getToken() === 5 /* CommaToken */) {\n                if (!needsComma) {\n                    handleError(4 /* ValueExpected */, [], []);\n                }\n                onSeparator(',');\n                scanNext(); // consume comma\n                if (_scanner.getToken() === 4 /* CloseBracketToken */ && allowTrailingComma) {\n                    break;\n                }\n            }\n            else if (needsComma) {\n                handleError(6 /* CommaExpected */, [], []);\n            }\n            if (!parseValue()) {\n                handleError(4 /* ValueExpected */, [], [4 /* CloseBracketToken */, 5 /* CommaToken */]);\n            }\n            needsComma = true;\n        }\n        onArrayEnd();\n        if (_scanner.getToken() !== 4 /* CloseBracketToken */) {\n            handleError(8 /* CloseBracketExpected */, [4 /* CloseBracketToken */], []);\n        }\n        else {\n            scanNext(); // consume close bracket\n        }\n        return true;\n    }\n    function parseValue() {\n        switch (_scanner.getToken()) {\n            case 3 /* OpenBracketToken */:\n                return parseArray();\n            case 1 /* OpenBraceToken */:\n                return parseObject();\n            case 10 /* StringLiteral */:\n                return parseString(true);\n            default:\n                return parseLiteral();\n        }\n    }\n    scanNext();\n    if (_scanner.getToken() === 17 /* EOF */) {\n        if (options.allowEmptyContent) {\n            return true;\n        }\n        handleError(4 /* ValueExpected */, [], []);\n        return false;\n    }\n    if (!parseValue()) {\n        handleError(4 /* ValueExpected */, [], []);\n        return false;\n    }\n    if (_scanner.getToken() !== 17 /* EOF */) {\n        handleError(9 /* EndOfFileExpected */, [], []);\n    }\n    return true;\n}\n/**\n * Takes JSON with JavaScript-style comments and remove\n * them. Optionally replaces every none-newline character\n * of comments with a replaceCharacter\n */\nexport function stripComments(text, replaceCh) {\n    var _scanner = createScanner(text), parts = [], kind, offset = 0, pos;\n    do {\n        pos = _scanner.getPosition();\n        kind = _scanner.scan();\n        switch (kind) {\n            case 12 /* LineCommentTrivia */:\n            case 13 /* BlockCommentTrivia */:\n            case 17 /* EOF */:\n                if (offset !== pos) {\n                    parts.push(text.substring(offset, pos));\n                }\n                if (replaceCh !== undefined) {\n                    parts.push(_scanner.getTokenValue().replace(/[^\\r\\n]/g, replaceCh));\n                }\n                offset = _scanner.getPosition();\n                break;\n        }\n    } while (kind !== 17 /* EOF */);\n    return parts.join('');\n}\nexport function getNodeType(value) {\n    switch (typeof value) {\n        case 'boolean': return 'boolean';\n        case 'number': return 'number';\n        case 'string': return 'string';\n        case 'object': {\n            if (!value) {\n                return 'null';\n            }\n            else if (Array.isArray(value)) {\n                return 'array';\n            }\n            return 'object';\n        }\n        default: return 'null';\n    }\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n'use strict';\nimport * as formatter from './impl/format.js';\nimport * as edit from './impl/edit.js';\nimport * as scanner from './impl/scanner.js';\nimport * as parser from './impl/parser.js';\n/**\n * Creates a JSON scanner on the given text.\n * If ignoreTrivia is set, whitespaces or comments are ignored.\n */\nexport var createScanner = scanner.createScanner;\n/**\n * For a given offset, evaluate the location in the JSON document. Each segment in the location path is either a property name or an array index.\n */\nexport var getLocation = parser.getLocation;\n/**\n * Parses the given text and returns the object the JSON content represents. On invalid input, the parser tries to be as fault tolerant as possible, but still return a result.\n * Therefore, always check the errors list to find out if the input was valid.\n */\nexport var parse = parser.parse;\n/**\n * Parses the given text and returns a tree representation the JSON content. On invalid input, the parser tries to be as fault tolerant as possible, but still return a result.\n */\nexport var parseTree = parser.parseTree;\n/**\n * Finds the node at the given path in a JSON DOM.\n */\nexport var findNodeAtLocation = parser.findNodeAtLocation;\n/**\n * Finds the innermost node at the given offset. If includeRightBound is set, also finds nodes that end at the given offset.\n */\nexport var findNodeAtOffset = parser.findNodeAtOffset;\n/**\n * Gets the JSON path of the given JSON DOM node\n */\nexport var getNodePath = parser.getNodePath;\n/**\n * Evaluates the JavaScript object of the given JSON DOM node\n */\nexport var getNodeValue = parser.getNodeValue;\n/**\n * Parses the given text and invokes the visitor functions for each object, array and literal reached.\n */\nexport var visit = parser.visit;\n/**\n * Takes JSON with JavaScript-style comments and remove\n * them. Optionally replaces every none-newline character\n * of comments with a replaceCharacter\n */\nexport var stripComments = parser.stripComments;\nexport function printParseErrorCode(code) {\n    switch (code) {\n        case 1 /* InvalidSymbol */: return 'InvalidSymbol';\n        case 2 /* InvalidNumberFormat */: return 'InvalidNumberFormat';\n        case 3 /* PropertyNameExpected */: return 'PropertyNameExpected';\n        case 4 /* ValueExpected */: return 'ValueExpected';\n        case 5 /* ColonExpected */: return 'ColonExpected';\n        case 6 /* CommaExpected */: return 'CommaExpected';\n        case 7 /* CloseBraceExpected */: return 'CloseBraceExpected';\n        case 8 /* CloseBracketExpected */: return 'CloseBracketExpected';\n        case 9 /* EndOfFileExpected */: return 'EndOfFileExpected';\n        case 10 /* InvalidCommentToken */: return 'InvalidCommentToken';\n        case 11 /* UnexpectedEndOfComment */: return 'UnexpectedEndOfComment';\n        case 12 /* UnexpectedEndOfString */: return 'UnexpectedEndOfString';\n        case 13 /* UnexpectedEndOfNumber */: return 'UnexpectedEndOfNumber';\n        case 14 /* InvalidUnicode */: return 'InvalidUnicode';\n        case 15 /* InvalidEscapeCharacter */: return 'InvalidEscapeCharacter';\n        case 16 /* InvalidCharacter */: return 'InvalidCharacter';\n    }\n    return '<unknown ParseErrorCode>';\n}\n/**\n * Computes the edits needed to format a JSON document.\n *\n * @param documentText The input text\n * @param range The range to format or `undefined` to format the full content\n * @param options The formatting options\n * @returns A list of edit operations describing the formatting changes to the original document. Edits can be either inserts, replacements or\n * removals of text segments. All offsets refer to the original state of the document. No two edits must change or remove the same range of\n * text in the original document. However, multiple edits can have\n * the same offset, for example multiple inserts, or an insert followed by a remove or replace. The order in the array defines which edit is applied first.\n * To apply edits to an input, you can use `applyEdits`.\n */\nexport function format(documentText, range, options) {\n    return formatter.format(documentText, range, options);\n}\n/**\n * Computes the edits needed to modify a value in the JSON document.\n *\n * @param documentText The input text\n * @param path The path of the value to change. The path represents either to the document root, a property or an array item.\n * If the path points to an non-existing property or item, it will be created.\n * @param value The new value for the specified property or item. If the value is undefined,\n * the property or item will be removed.\n * @param options Options\n * @returns A list of edit operations describing the formatting changes to the original document. Edits can be either inserts, replacements or\n * removals of text segments. All offsets refer to the original state of the document. No two edits must change or remove the same range of\n * text in the original document. However, multiple edits can have\n * the same offset, for example multiple inserts, or an insert followed by a remove or replace. The order in the array defines which edit is applied first.\n * To apply edits to an input, you can use `applyEdits`.\n */\nexport function modify(text, path, value, options) {\n    return edit.setProperty(text, path, value, options);\n}\n/**\n * Applies edits to a input string.\n */\nexport function applyEdits(text, edits) {\n    for (var i = edits.length - 1; i >= 0; i--) {\n        text = edit.applyEdit(text, edits[i]);\n    }\n    return text;\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport * as json from './_deps/jsonc-parser/main.js';\nexport function createTokenizationSupport(supportComments) {\n    return {\n        getInitialState: function () { return new JSONState(null, null, false, null); },\n        tokenize: function (line, state, offsetDelta, stopAtOffset) {\n            return tokenize(supportComments, line, state, offsetDelta, stopAtOffset);\n        }\n    };\n}\nexport var TOKEN_DELIM_OBJECT = 'delimiter.bracket.json';\nexport var TOKEN_DELIM_ARRAY = 'delimiter.array.json';\nexport var TOKEN_DELIM_COLON = 'delimiter.colon.json';\nexport var TOKEN_DELIM_COMMA = 'delimiter.comma.json';\nexport var TOKEN_VALUE_BOOLEAN = 'keyword.json';\nexport var TOKEN_VALUE_NULL = 'keyword.json';\nexport var TOKEN_VALUE_STRING = 'string.value.json';\nexport var TOKEN_VALUE_NUMBER = 'number.json';\nexport var TOKEN_PROPERTY_NAME = 'string.key.json';\nexport var TOKEN_COMMENT_BLOCK = 'comment.block.json';\nexport var TOKEN_COMMENT_LINE = 'comment.line.json';\nvar ParentsStack = /** @class */ (function () {\n    function ParentsStack(parent, type) {\n        this.parent = parent;\n        this.type = type;\n    }\n    ParentsStack.pop = function (parents) {\n        if (parents) {\n            return parents.parent;\n        }\n        return null;\n    };\n    ParentsStack.push = function (parents, type) {\n        return new ParentsStack(parents, type);\n    };\n    ParentsStack.equals = function (a, b) {\n        if (!a && !b) {\n            return true;\n        }\n        if (!a || !b) {\n            return false;\n        }\n        while (a && b) {\n            if (a === b) {\n                return true;\n            }\n            if (a.type !== b.type) {\n                return false;\n            }\n            a = a.parent;\n            b = b.parent;\n        }\n        return true;\n    };\n    return ParentsStack;\n}());\nvar JSONState = /** @class */ (function () {\n    function JSONState(state, scanError, lastWasColon, parents) {\n        this._state = state;\n        this.scanError = scanError;\n        this.lastWasColon = lastWasColon;\n        this.parents = parents;\n    }\n    JSONState.prototype.clone = function () {\n        return new JSONState(this._state, this.scanError, this.lastWasColon, this.parents);\n    };\n    JSONState.prototype.equals = function (other) {\n        if (other === this) {\n            return true;\n        }\n        if (!other || !(other instanceof JSONState)) {\n            return false;\n        }\n        return (this.scanError === other.scanError &&\n            this.lastWasColon === other.lastWasColon &&\n            ParentsStack.equals(this.parents, other.parents));\n    };\n    JSONState.prototype.getStateData = function () {\n        return this._state;\n    };\n    JSONState.prototype.setStateData = function (state) {\n        this._state = state;\n    };\n    return JSONState;\n}());\nfunction tokenize(comments, line, state, offsetDelta, stopAtOffset) {\n    if (offsetDelta === void 0) { offsetDelta = 0; }\n    // handle multiline strings and block comments\n    var numberOfInsertedCharacters = 0;\n    var adjustOffset = false;\n    switch (state.scanError) {\n        case 2 /* UnexpectedEndOfString */:\n            line = '\"' + line;\n            numberOfInsertedCharacters = 1;\n            break;\n        case 1 /* UnexpectedEndOfComment */:\n            line = '/*' + line;\n            numberOfInsertedCharacters = 2;\n            break;\n    }\n    var scanner = json.createScanner(line);\n    var lastWasColon = state.lastWasColon;\n    var parents = state.parents;\n    var ret = {\n        tokens: [],\n        endState: state.clone()\n    };\n    while (true) {\n        var offset = offsetDelta + scanner.getPosition();\n        var type = '';\n        var kind = scanner.scan();\n        if (kind === 17 /* EOF */) {\n            break;\n        }\n        // Check that the scanner has advanced\n        if (offset === offsetDelta + scanner.getPosition()) {\n            throw new Error('Scanner did not advance, next 3 characters are: ' + line.substr(scanner.getPosition(), 3));\n        }\n        // In case we inserted /* or \" character, we need to\n        // adjust the offset of all tokens (except the first)\n        if (adjustOffset) {\n            offset -= numberOfInsertedCharacters;\n        }\n        adjustOffset = numberOfInsertedCharacters > 0;\n        // brackets and type\n        switch (kind) {\n            case 1 /* OpenBraceToken */:\n                parents = ParentsStack.push(parents, 0 /* Object */);\n                type = TOKEN_DELIM_OBJECT;\n                lastWasColon = false;\n                break;\n            case 2 /* CloseBraceToken */:\n                parents = ParentsStack.pop(parents);\n                type = TOKEN_DELIM_OBJECT;\n                lastWasColon = false;\n                break;\n            case 3 /* OpenBracketToken */:\n                parents = ParentsStack.push(parents, 1 /* Array */);\n                type = TOKEN_DELIM_ARRAY;\n                lastWasColon = false;\n                break;\n            case 4 /* CloseBracketToken */:\n                parents = ParentsStack.pop(parents);\n                type = TOKEN_DELIM_ARRAY;\n                lastWasColon = false;\n                break;\n            case 6 /* ColonToken */:\n                type = TOKEN_DELIM_COLON;\n                lastWasColon = true;\n                break;\n            case 5 /* CommaToken */:\n                type = TOKEN_DELIM_COMMA;\n                lastWasColon = false;\n                break;\n            case 8 /* TrueKeyword */:\n            case 9 /* FalseKeyword */:\n                type = TOKEN_VALUE_BOOLEAN;\n                lastWasColon = false;\n                break;\n            case 7 /* NullKeyword */:\n                type = TOKEN_VALUE_NULL;\n                lastWasColon = false;\n                break;\n            case 10 /* StringLiteral */:\n                var currentParent = parents ? parents.type : 0 /* Object */;\n                var inArray = currentParent === 1 /* Array */;\n                type = lastWasColon || inArray ? TOKEN_VALUE_STRING : TOKEN_PROPERTY_NAME;\n                lastWasColon = false;\n                break;\n            case 11 /* NumericLiteral */:\n                type = TOKEN_VALUE_NUMBER;\n                lastWasColon = false;\n                break;\n        }\n        // comments, iff enabled\n        if (comments) {\n            switch (kind) {\n                case 12 /* LineCommentTrivia */:\n                    type = TOKEN_COMMENT_LINE;\n                    break;\n                case 13 /* BlockCommentTrivia */:\n                    type = TOKEN_COMMENT_BLOCK;\n                    break;\n            }\n        }\n        ret.endState = new JSONState(state.getStateData(), scanner.getTokenError(), lastWasColon, parents);\n        ret.tokens.push({\n            startIndex: offset,\n            scopes: type\n        });\n    }\n    return ret;\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nimport { WorkerManager } from './workerManager.js';\nimport * as languageFeatures from './languageFeatures.js';\nimport { createTokenizationSupport } from './tokenization.js';\nimport { languages } from './fillers/monaco-editor-core.js';\nexport function setupMode(defaults) {\n    var disposables = [];\n    var providers = [];\n    var client = new WorkerManager(defaults);\n    disposables.push(client);\n    var worker = function () {\n        var uris = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            uris[_i] = arguments[_i];\n        }\n        return client.getLanguageServiceWorker.apply(client, uris);\n    };\n    function registerProviders() {\n        var languageId = defaults.languageId, modeConfiguration = defaults.modeConfiguration;\n        disposeAll(providers);\n        if (modeConfiguration.documentFormattingEdits) {\n            providers.push(languages.registerDocumentFormattingEditProvider(languageId, new languageFeatures.DocumentFormattingEditProvider(worker)));\n        }\n        if (modeConfiguration.documentRangeFormattingEdits) {\n            providers.push(languages.registerDocumentRangeFormattingEditProvider(languageId, new languageFeatures.DocumentRangeFormattingEditProvider(worker)));\n        }\n        if (modeConfiguration.completionItems) {\n            providers.push(languages.registerCompletionItemProvider(languageId, new languageFeatures.CompletionAdapter(worker)));\n        }\n        if (modeConfiguration.hovers) {\n            providers.push(languages.registerHoverProvider(languageId, new languageFeatures.HoverAdapter(worker)));\n        }\n        if (modeConfiguration.documentSymbols) {\n            providers.push(languages.registerDocumentSymbolProvider(languageId, new languageFeatures.DocumentSymbolAdapter(worker)));\n        }\n        if (modeConfiguration.tokens) {\n            providers.push(languages.setTokensProvider(languageId, createTokenizationSupport(true)));\n        }\n        if (modeConfiguration.colors) {\n            providers.push(languages.registerColorProvider(languageId, new languageFeatures.DocumentColorAdapter(worker)));\n        }\n        if (modeConfiguration.foldingRanges) {\n            providers.push(languages.registerFoldingRangeProvider(languageId, new languageFeatures.FoldingRangeAdapter(worker)));\n        }\n        if (modeConfiguration.diagnostics) {\n            providers.push(new languageFeatures.DiagnosticsAdapter(languageId, worker, defaults));\n        }\n        if (modeConfiguration.selectionRanges) {\n            providers.push(languages.registerSelectionRangeProvider(languageId, new languageFeatures.SelectionRangeAdapter(worker)));\n        }\n    }\n    registerProviders();\n    disposables.push(languages.setLanguageConfiguration(defaults.languageId, richEditConfiguration));\n    var modeConfiguration = defaults.modeConfiguration;\n    defaults.onDidChange(function (newDefaults) {\n        if (newDefaults.modeConfiguration !== modeConfiguration) {\n            modeConfiguration = newDefaults.modeConfiguration;\n            registerProviders();\n        }\n    });\n    disposables.push(asDisposable(providers));\n    return asDisposable(disposables);\n}\nfunction asDisposable(disposables) {\n    return { dispose: function () { return disposeAll(disposables); } };\n}\nfunction disposeAll(disposables) {\n    while (disposables.length) {\n        disposables.pop().dispose();\n    }\n}\nvar richEditConfiguration = {\n    wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\[\\{\\]\\}\\:\\\"\\,\\s]+)/g,\n    comments: {\n        lineComment: '//',\n        blockComment: ['/*', '*/']\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}', notIn: ['string'] },\n        { open: '[', close: ']', notIn: ['string'] },\n        { open: '\"', close: '\"', notIn: ['string'] }\n    ]\n};\n"], "mappings": ";;;;;;;;;;AAKA,IAAI,qBAAqB,IAAI,KAAK;AAClC,IAAI;AAAA;AAAA,EAA+B,WAAY;AAC3C,aAASA,eAAc,UAAU;AAC7B,UAAI,QAAQ;AACZ,WAAK,YAAY;AACjB,WAAK,UAAU;AACf,WAAK,qBAAqB,OAAO,YAAY,WAAY;AAAE,eAAO,MAAM,aAAa;AAAA,MAAG,GAAG,KAAK,GAAI;AACpG,WAAK,gBAAgB;AACrB,WAAK,wBAAwB,KAAK,UAAU,YAAY,WAAY;AAAE,eAAO,MAAM,YAAY;AAAA,MAAG,CAAC;AAAA,IACvG;AACA,IAAAA,eAAc,UAAU,cAAc,WAAY;AAC9C,UAAI,KAAK,SAAS;AACd,aAAK,QAAQ,QAAQ;AACrB,aAAK,UAAU;AAAA,MACnB;AACA,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,eAAc,UAAU,UAAU,WAAY;AAC1C,oBAAc,KAAK,kBAAkB;AACrC,WAAK,sBAAsB,QAAQ;AACnC,WAAK,YAAY;AAAA,IACrB;AACA,IAAAA,eAAc,UAAU,eAAe,WAAY;AAC/C,UAAI,CAAC,KAAK,SAAS;AACf;AAAA,MACJ;AACA,UAAI,0BAA0B,KAAK,IAAI,IAAI,KAAK;AAChD,UAAI,0BAA0B,oBAAoB;AAC9C,aAAK,YAAY;AAAA,MACrB;AAAA,IACJ;AACA,IAAAA,eAAc,UAAU,aAAa,WAAY;AAC7C,WAAK,gBAAgB,KAAK,IAAI;AAC9B,UAAI,CAAC,KAAK,SAAS;AACf,aAAK,UAAU,OAAO,gBAAgB;AAAA;AAAA,UAElC,UAAU;AAAA,UACV,OAAO,KAAK,UAAU;AAAA;AAAA,UAEtB,YAAY;AAAA,YACR,kBAAkB,KAAK,UAAU;AAAA,YACjC,YAAY,KAAK,UAAU;AAAA,YAC3B,qBAAqB,KAAK,UAAU,mBAAmB;AAAA,UAC3D;AAAA,QACJ,CAAC;AACD,aAAK,UAAU,KAAK,QAAQ,SAAS;AAAA,MACzC;AACA,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,eAAc,UAAU,2BAA2B,WAAY;AAC3D,UAAI,QAAQ;AACZ,UAAI,YAAY,CAAC;AACjB,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,kBAAU,EAAE,IAAI,UAAU,EAAE;AAAA,MAChC;AACA,UAAI;AACJ,aAAO,KAAK,WAAW,EAClB,KAAK,SAAU,QAAQ;AACxB,kBAAU;AAAA,MACd,CAAC,EACI,KAAK,SAAU,GAAG;AACnB,eAAO,MAAM,QAAQ,oBAAoB,SAAS;AAAA,MACtD,CAAC,EACI,KAAK,SAAU,GAAG;AAAE,eAAO;AAAA,MAAS,CAAC;AAAA,IAC9C;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;;;AClEK,IAAI;AAAA,CACV,SAAUC,UAAS;AAChB,EAAAA,SAAQ,YAAY;AACpB,EAAAA,SAAQ,YAAY;AACxB,GAAG,YAAY,UAAU,CAAC,EAAE;AACrB,IAAI;AAAA,CACV,SAAUC,WAAU;AACjB,EAAAA,UAAS,YAAY;AACrB,EAAAA,UAAS,YAAY;AACzB,GAAG,aAAa,WAAW,CAAC,EAAE;AAKvB,IAAI;AAAA,CACV,SAAUC,WAAU;AAMjB,WAAS,OAAO,MAAM,WAAW;AAC7B,QAAI,SAAS,OAAO,WAAW;AAC3B,aAAO,SAAS;AAAA,IACpB;AACA,QAAI,cAAc,OAAO,WAAW;AAChC,kBAAY,SAAS;AAAA,IACzB;AACA,WAAO,EAAE,MAAY,UAAqB;AAAA,EAC9C;AACA,EAAAA,UAAS,SAAS;AAIlB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,cAAc,SAAS,KAAK,GAAG,SAAS,UAAU,IAAI,KAAK,GAAG,SAAS,UAAU,SAAS;AAAA,EACxG;AACA,EAAAA,UAAS,KAAK;AAClB,GAAG,aAAa,WAAW,CAAC,EAAE;AAKvB,IAAIC;AAAA,CACV,SAAUA,QAAO;AACd,WAAS,OAAO,KAAK,KAAK,OAAO,MAAM;AACnC,QAAI,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,GAAG,KAAK,GAAG,SAAS,KAAK,KAAK,GAAG,SAAS,IAAI,GAAG;AACjF,aAAO,EAAE,OAAO,SAAS,OAAO,KAAK,GAAG,GAAG,KAAK,SAAS,OAAO,OAAO,IAAI,EAAE;AAAA,IACjF,WACS,SAAS,GAAG,GAAG,KAAK,SAAS,GAAG,GAAG,GAAG;AAC3C,aAAO,EAAE,OAAO,KAAK,KAAK,IAAI;AAAA,IAClC,OACK;AACD,YAAM,IAAI,MAAM,gDAAgD,MAAM,OAAO,MAAM,OAAO,QAAQ,OAAO,OAAO,GAAG;AAAA,IACvH;AAAA,EACJ;AACA,EAAAA,OAAM,SAAS;AAIf,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,cAAc,SAAS,KAAK,SAAS,GAAG,UAAU,KAAK,KAAK,SAAS,GAAG,UAAU,GAAG;AAAA,EACnG;AACA,EAAAA,OAAM,KAAK;AACf,GAAGA,WAAUA,SAAQ,CAAC,EAAE;AAKjB,IAAI;AAAA,CACV,SAAUC,WAAU;AAMjB,WAAS,OAAO,KAAK,OAAO;AACxB,WAAO,EAAE,KAAU,MAAa;AAAA,EACpC;AACA,EAAAA,UAAS,SAAS;AAIlB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAKD,OAAM,GAAG,UAAU,KAAK,MAAM,GAAG,OAAO,UAAU,GAAG,KAAK,GAAG,UAAU,UAAU,GAAG;AAAA,EACxH;AACA,EAAAC,UAAS,KAAK;AAClB,GAAG,aAAa,WAAW,CAAC,EAAE;AAKvB,IAAI;AAAA,CACV,SAAUC,eAAc;AAQrB,WAAS,OAAO,WAAW,aAAa,sBAAsB,sBAAsB;AAChF,WAAO,EAAE,WAAsB,aAA0B,sBAA4C,qBAA2C;AAAA,EACpJ;AACA,EAAAA,cAAa,SAAS;AAItB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAKF,OAAM,GAAG,UAAU,WAAW,KAAK,GAAG,OAAO,UAAU,SAAS,MACxFA,OAAM,GAAG,UAAU,oBAAoB,KAAK,GAAG,UAAU,UAAU,oBAAoB,OACvFA,OAAM,GAAG,UAAU,oBAAoB,KAAK,GAAG,UAAU,UAAU,oBAAoB;AAAA,EACnG;AACA,EAAAE,cAAa,KAAK;AACtB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAK/B,IAAI;AAAA,CACV,SAAUC,QAAO;AAId,WAAS,OAAO,KAAK,OAAO,MAAM,OAAO;AACrC,WAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,OAAM,SAAS;AAIf,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,YAAY,UAAU,KAAK,GAAG,CAAC,KAClC,GAAG,YAAY,UAAU,OAAO,GAAG,CAAC,KACpC,GAAG,YAAY,UAAU,MAAM,GAAG,CAAC,KACnC,GAAG,YAAY,UAAU,OAAO,GAAG,CAAC;AAAA,EAC/C;AACA,EAAAA,OAAM,KAAK;AACf,GAAG,UAAU,QAAQ,CAAC,EAAE;AAKjB,IAAI;AAAA,CACV,SAAUC,mBAAkB;AAIzB,WAAS,OAAO,OAAO,OAAO;AAC1B,WAAO;AAAA,MACH;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,kBAAiB,SAAS;AAI1B,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAOJ,OAAM,GAAG,UAAU,KAAK,KAAK,MAAM,GAAG,UAAU,KAAK;AAAA,EAChE;AACA,EAAAI,kBAAiB,KAAK;AAC1B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAKvC,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAI1B,WAAS,OAAO,OAAO,UAAU,qBAAqB;AAClD,WAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,mBAAkB,SAAS;AAI3B,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,OAAO,UAAU,KAAK,MACxB,GAAG,UAAU,UAAU,QAAQ,KAAK,SAAS,GAAG,SAAS,OACzD,GAAG,UAAU,UAAU,mBAAmB,KAAK,GAAG,WAAW,UAAU,qBAAqB,SAAS,EAAE;AAAA,EACnH;AACA,EAAAA,mBAAkB,KAAK;AAC3B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAIzC,IAAI;AAAA,CACV,SAAUC,mBAAkB;AAIzB,EAAAA,kBAAiB,SAAS,IAAI;AAI9B,EAAAA,kBAAiB,SAAS,IAAI;AAI9B,EAAAA,kBAAiB,QAAQ,IAAI;AACjC,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAKvC,IAAI;AAAA,CACV,SAAUC,eAAc;AAIrB,WAAS,OAAO,WAAW,SAAS,gBAAgB,cAAc,MAAM;AACpE,QAAI,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACJ;AACA,QAAI,GAAG,QAAQ,cAAc,GAAG;AAC5B,aAAO,iBAAiB;AAAA,IAC5B;AACA,QAAI,GAAG,QAAQ,YAAY,GAAG;AAC1B,aAAO,eAAe;AAAA,IAC1B;AACA,QAAI,GAAG,QAAQ,IAAI,GAAG;AAClB,aAAO,OAAO;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,cAAa,SAAS;AAItB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,SAAS,UAAU,SAAS,KAAK,GAAG,SAAS,UAAU,SAAS,MAClE,GAAG,UAAU,UAAU,cAAc,KAAK,GAAG,SAAS,UAAU,cAAc,OAC9E,GAAG,UAAU,UAAU,YAAY,KAAK,GAAG,SAAS,UAAU,YAAY,OAC1E,GAAG,UAAU,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,IAAI;AAAA,EACpE;AACA,EAAAA,cAAa,KAAK;AACtB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAK/B,IAAI;AAAA,CACV,SAAUC,+BAA8B;AAIrC,WAAS,OAAO,UAAU,SAAS;AAC/B,WAAO;AAAA,MACH;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACA,EAAAA,8BAA6B,SAAS;AAItC,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,SAAS,GAAG,UAAU,QAAQ,KAAK,GAAG,OAAO,UAAU,OAAO;AAAA,EAClG;AACA,EAAAA,8BAA6B,KAAK;AACtC,GAAG,iCAAiC,+BAA+B,CAAC,EAAE;AAI/D,IAAI;AAAA,CACV,SAAUC,qBAAoB;AAI3B,EAAAA,oBAAmB,QAAQ;AAI3B,EAAAA,oBAAmB,UAAU;AAI7B,EAAAA,oBAAmB,cAAc;AAIjC,EAAAA,oBAAmB,OAAO;AAC9B,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAM3C,IAAI;AAAA,CACV,SAAUC,gBAAe;AAOtB,EAAAA,eAAc,cAAc;AAM5B,EAAAA,eAAc,aAAa;AAC/B,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAMjC,IAAI;AAAA,CACV,SAAUC,kBAAiB;AACxB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,cAAc,UAAa,cAAc,QAAQ,GAAG,OAAO,UAAU,IAAI;AAAA,EACpF;AACA,EAAAA,iBAAgB,KAAK;AACzB,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAKrC,IAAI;AAAA,CACV,SAAUC,aAAY;AAInB,WAAS,OAAO,OAAO,SAAS,UAAU,MAAM,QAAQ,oBAAoB;AACxE,QAAI,SAAS,EAAE,OAAc,QAAiB;AAC9C,QAAI,GAAG,QAAQ,QAAQ,GAAG;AACtB,aAAO,WAAW;AAAA,IACtB;AACA,QAAI,GAAG,QAAQ,IAAI,GAAG;AAClB,aAAO,OAAO;AAAA,IAClB;AACA,QAAI,GAAG,QAAQ,MAAM,GAAG;AACpB,aAAO,SAAS;AAAA,IACpB;AACA,QAAI,GAAG,QAAQ,kBAAkB,GAAG;AAChC,aAAO,qBAAqB;AAAA,IAChC;AACA,WAAO;AAAA,EACX;AACA,EAAAA,YAAW,SAAS;AAIpB,WAAS,GAAG,OAAO;AACf,QAAI;AACJ,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KACpBZ,OAAM,GAAG,UAAU,KAAK,KACxB,GAAG,OAAO,UAAU,OAAO,MAC1B,GAAG,OAAO,UAAU,QAAQ,KAAK,GAAG,UAAU,UAAU,QAAQ,OAChE,GAAG,QAAQ,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,IAAI,KAAK,GAAG,UAAU,UAAU,IAAI,OACtF,GAAG,UAAU,UAAU,eAAe,KAAM,GAAG,QAAQ,KAAK,UAAU,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI,OACnI,GAAG,OAAO,UAAU,MAAM,KAAK,GAAG,UAAU,UAAU,MAAM,OAC5D,GAAG,UAAU,UAAU,kBAAkB,KAAK,GAAG,WAAW,UAAU,oBAAoB,6BAA6B,EAAE;AAAA,EACrI;AACA,EAAAY,YAAW,KAAK;AACpB,GAAG,eAAe,aAAa,CAAC,EAAE;AAK3B,IAAI;AAAA,CACV,SAAUC,UAAS;AAIhB,WAAS,OAAO,OAAO,SAAS;AAC5B,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,IAC/B;AACA,QAAI,SAAS,EAAE,OAAc,QAAiB;AAC9C,QAAI,GAAG,QAAQ,IAAI,KAAK,KAAK,SAAS,GAAG;AACrC,aAAO,YAAY;AAAA,IACvB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,SAAQ,SAAS;AAIjB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,KAAK,KAAK,GAAG,OAAO,UAAU,OAAO;AAAA,EAC7F;AACA,EAAAA,SAAQ,KAAK;AACjB,GAAG,YAAY,UAAU,CAAC,EAAE;AAKrB,IAAI;AAAA,CACV,SAAUC,WAAU;AAMjB,WAAS,QAAQ,OAAO,SAAS;AAC7B,WAAO,EAAE,OAAc,QAAiB;AAAA,EAC5C;AACA,EAAAA,UAAS,UAAU;AAMnB,WAAS,OAAO,UAAU,SAAS;AAC/B,WAAO,EAAE,OAAO,EAAE,OAAO,UAAU,KAAK,SAAS,GAAG,QAAiB;AAAA,EACzE;AACA,EAAAA,UAAS,SAAS;AAKlB,WAAS,IAAI,OAAO;AAChB,WAAO,EAAE,OAAc,SAAS,GAAG;AAAA,EACvC;AACA,EAAAA,UAAS,MAAM;AACf,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,cAAc,SAAS,KAC1B,GAAG,OAAO,UAAU,OAAO,KAC3Bd,OAAM,GAAG,UAAU,KAAK;AAAA,EACnC;AACA,EAAAc,UAAS,KAAK;AAClB,GAAG,aAAa,WAAW,CAAC,EAAE;AACvB,IAAI;AAAA,CACV,SAAUC,mBAAkB;AACzB,WAAS,OAAO,OAAO,mBAAmB,aAAa;AACnD,QAAI,SAAS,EAAE,MAAa;AAC5B,QAAI,sBAAsB,QAAW;AACjC,aAAO,oBAAoB;AAAA,IAC/B;AACA,QAAI,gBAAgB,QAAW;AAC3B,aAAO,cAAc;AAAA,IACzB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,kBAAiB,SAAS;AAC1B,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,cAAc,UAAa,GAAG,cAAc,SAAS,KAAK,GAAG,OAAO,UAAU,KAAK,MACrF,GAAG,QAAQ,UAAU,iBAAiB,KAAK,UAAU,sBAAsB,YAC3E,GAAG,OAAO,UAAU,WAAW,KAAK,UAAU,gBAAgB;AAAA,EACvE;AACA,EAAAA,kBAAiB,KAAK;AAC1B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AACvC,IAAI;AAAA,CACV,SAAUC,6BAA4B;AACnC,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,OAAO,cAAc;AAAA,EAChC;AACA,EAAAA,4BAA2B,KAAK;AACpC,GAAG,+BAA+B,6BAA6B,CAAC,EAAE;AAC3D,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAQ1B,WAAS,QAAQ,OAAO,SAAS,YAAY;AACzC,WAAO,EAAE,OAAc,SAAkB,cAAc,WAAW;AAAA,EACtE;AACA,EAAAA,mBAAkB,UAAU;AAQ5B,WAAS,OAAO,UAAU,SAAS,YAAY;AAC3C,WAAO,EAAE,OAAO,EAAE,OAAO,UAAU,KAAK,SAAS,GAAG,SAAkB,cAAc,WAAW;AAAA,EACnG;AACA,EAAAA,mBAAkB,SAAS;AAO3B,WAAS,IAAI,OAAO,YAAY;AAC5B,WAAO,EAAE,OAAc,SAAS,IAAI,cAAc,WAAW;AAAA,EACjE;AACA,EAAAA,mBAAkB,MAAM;AACxB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,SAAS,GAAG,SAAS,MAAM,iBAAiB,GAAG,UAAU,YAAY,KAAK,2BAA2B,GAAG,UAAU,YAAY;AAAA,EACzI;AACA,EAAAA,mBAAkB,KAAK;AAC3B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAKzC,IAAI;AAAA,CACV,SAAUC,mBAAkB;AAIzB,WAAS,OAAO,cAAc,OAAO;AACjC,WAAO,EAAE,cAA4B,MAAa;AAAA,EACtD;AACA,EAAAA,kBAAiB,SAAS;AAC1B,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KACpB,wCAAwC,GAAG,UAAU,YAAY,KACjE,MAAM,QAAQ,UAAU,KAAK;AAAA,EACxC;AACA,EAAAA,kBAAiB,KAAK;AAC1B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AACvC,IAAI;AAAA,CACV,SAAUC,aAAY;AACnB,WAAS,OAAO,KAAK,SAAS,YAAY;AACtC,QAAI,SAAS;AAAA,MACT,MAAM;AAAA,MACN;AAAA,IACJ;AACA,QAAI,YAAY,WAAc,QAAQ,cAAc,UAAa,QAAQ,mBAAmB,SAAY;AACpG,aAAO,UAAU;AAAA,IACrB;AACA,QAAI,eAAe,QAAW;AAC1B,aAAO,eAAe;AAAA,IAC1B;AACA,WAAO;AAAA,EACX;AACA,EAAAA,YAAW,SAAS;AACpB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,aAAa,UAAU,SAAS,YAAY,GAAG,OAAO,UAAU,GAAG,MAAM,UAAU,YAAY,WAChG,UAAU,QAAQ,cAAc,UAAa,GAAG,QAAQ,UAAU,QAAQ,SAAS,OAAO,UAAU,QAAQ,mBAAmB,UAAa,GAAG,QAAQ,UAAU,QAAQ,cAAc,QAAS,UAAU,iBAAiB,UAAa,2BAA2B,GAAG,UAAU,YAAY;AAAA,EACtS;AACA,EAAAA,YAAW,KAAK;AACpB,GAAG,eAAe,aAAa,CAAC,EAAE;AAC3B,IAAI;AAAA,CACV,SAAUC,aAAY;AACnB,WAAS,OAAO,QAAQ,QAAQ,SAAS,YAAY;AACjD,QAAI,SAAS;AAAA,MACT,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACJ;AACA,QAAI,YAAY,WAAc,QAAQ,cAAc,UAAa,QAAQ,mBAAmB,SAAY;AACpG,aAAO,UAAU;AAAA,IACrB;AACA,QAAI,eAAe,QAAW;AAC1B,aAAO,eAAe;AAAA,IAC1B;AACA,WAAO;AAAA,EACX;AACA,EAAAA,YAAW,SAAS;AACpB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,aAAa,UAAU,SAAS,YAAY,GAAG,OAAO,UAAU,MAAM,KAAK,GAAG,OAAO,UAAU,MAAM,MAAM,UAAU,YAAY,WAClI,UAAU,QAAQ,cAAc,UAAa,GAAG,QAAQ,UAAU,QAAQ,SAAS,OAAO,UAAU,QAAQ,mBAAmB,UAAa,GAAG,QAAQ,UAAU,QAAQ,cAAc,QAAS,UAAU,iBAAiB,UAAa,2BAA2B,GAAG,UAAU,YAAY;AAAA,EACtS;AACA,EAAAA,YAAW,KAAK;AACpB,GAAG,eAAe,aAAa,CAAC,EAAE;AAC3B,IAAI;AAAA,CACV,SAAUC,aAAY;AACnB,WAAS,OAAO,KAAK,SAAS,YAAY;AACtC,QAAI,SAAS;AAAA,MACT,MAAM;AAAA,MACN;AAAA,IACJ;AACA,QAAI,YAAY,WAAc,QAAQ,cAAc,UAAa,QAAQ,sBAAsB,SAAY;AACvG,aAAO,UAAU;AAAA,IACrB;AACA,QAAI,eAAe,QAAW;AAC1B,aAAO,eAAe;AAAA,IAC1B;AACA,WAAO;AAAA,EACX;AACA,EAAAA,YAAW,SAAS;AACpB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,aAAa,UAAU,SAAS,YAAY,GAAG,OAAO,UAAU,GAAG,MAAM,UAAU,YAAY,WAChG,UAAU,QAAQ,cAAc,UAAa,GAAG,QAAQ,UAAU,QAAQ,SAAS,OAAO,UAAU,QAAQ,sBAAsB,UAAa,GAAG,QAAQ,UAAU,QAAQ,iBAAiB,QAAS,UAAU,iBAAiB,UAAa,2BAA2B,GAAG,UAAU,YAAY;AAAA,EAC5S;AACA,EAAAA,YAAW,KAAK;AACpB,GAAG,eAAe,aAAa,CAAC,EAAE;AAC3B,IAAI;AAAA,CACV,SAAUC,gBAAe;AACtB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,cACF,UAAU,YAAY,UAAa,UAAU,oBAAoB,YACjE,UAAU,oBAAoB,UAAa,UAAU,gBAAgB,MAAM,SAAU,QAAQ;AAC1F,UAAI,GAAG,OAAO,OAAO,IAAI,GAAG;AACxB,eAAO,WAAW,GAAG,MAAM,KAAK,WAAW,GAAG,MAAM,KAAK,WAAW,GAAG,MAAM;AAAA,MACjF,OACK;AACD,eAAO,iBAAiB,GAAG,MAAM;AAAA,MACrC;AAAA,IACJ,CAAC;AAAA,EACT;AACA,EAAAA,eAAc,KAAK;AACvB,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AACxC,IAAI;AAAA;AAAA,EAAoC,WAAY;AAChD,aAASC,oBAAmB,OAAO,mBAAmB;AAClD,WAAK,QAAQ;AACb,WAAK,oBAAoB;AAAA,IAC7B;AACA,IAAAA,oBAAmB,UAAU,SAAS,SAAU,UAAU,SAAS,YAAY;AAC3E,UAAI;AACJ,UAAI;AACJ,UAAI,eAAe,QAAW;AAC1B,eAAO,SAAS,OAAO,UAAU,OAAO;AAAA,MAC5C,WACS,2BAA2B,GAAG,UAAU,GAAG;AAChD,aAAK;AACL,eAAO,kBAAkB,OAAO,UAAU,SAAS,UAAU;AAAA,MACjE,OACK;AACD,aAAK,wBAAwB,KAAK,iBAAiB;AACnD,aAAK,KAAK,kBAAkB,OAAO,UAAU;AAC7C,eAAO,kBAAkB,OAAO,UAAU,SAAS,EAAE;AAAA,MACzD;AACA,WAAK,MAAM,KAAK,IAAI;AACpB,UAAI,OAAO,QAAW;AAClB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,IAAAA,oBAAmB,UAAU,UAAU,SAAU,OAAO,SAAS,YAAY;AACzE,UAAI;AACJ,UAAI;AACJ,UAAI,eAAe,QAAW;AAC1B,eAAO,SAAS,QAAQ,OAAO,OAAO;AAAA,MAC1C,WACS,2BAA2B,GAAG,UAAU,GAAG;AAChD,aAAK;AACL,eAAO,kBAAkB,QAAQ,OAAO,SAAS,UAAU;AAAA,MAC/D,OACK;AACD,aAAK,wBAAwB,KAAK,iBAAiB;AACnD,aAAK,KAAK,kBAAkB,OAAO,UAAU;AAC7C,eAAO,kBAAkB,QAAQ,OAAO,SAAS,EAAE;AAAA,MACvD;AACA,WAAK,MAAM,KAAK,IAAI;AACpB,UAAI,OAAO,QAAW;AAClB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,IAAAA,oBAAmB,UAAU,SAAS,SAAU,OAAO,YAAY;AAC/D,UAAI;AACJ,UAAI;AACJ,UAAI,eAAe,QAAW;AAC1B,eAAO,SAAS,IAAI,KAAK;AAAA,MAC7B,WACS,2BAA2B,GAAG,UAAU,GAAG;AAChD,aAAK;AACL,eAAO,kBAAkB,IAAI,OAAO,UAAU;AAAA,MAClD,OACK;AACD,aAAK,wBAAwB,KAAK,iBAAiB;AACnD,aAAK,KAAK,kBAAkB,OAAO,UAAU;AAC7C,eAAO,kBAAkB,IAAI,OAAO,EAAE;AAAA,MAC1C;AACA,WAAK,MAAM,KAAK,IAAI;AACpB,UAAI,OAAO,QAAW;AAClB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,IAAAA,oBAAmB,UAAU,MAAM,SAAU,MAAM;AAC/C,WAAK,MAAM,KAAK,IAAI;AAAA,IACxB;AACA,IAAAA,oBAAmB,UAAU,MAAM,WAAY;AAC3C,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,oBAAmB,UAAU,QAAQ,WAAY;AAC7C,WAAK,MAAM,OAAO,GAAG,KAAK,MAAM,MAAM;AAAA,IAC1C;AACA,IAAAA,oBAAmB,UAAU,0BAA0B,SAAU,OAAO;AACpE,UAAI,UAAU,QAAW;AACrB,cAAM,IAAI,MAAM,kEAAkE;AAAA,MACtF;AAAA,IACJ;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAIF,IAAI;AAAA;AAAA,EAAmC,WAAY;AAC/C,aAASC,mBAAkB,aAAa;AACpC,WAAK,eAAe,gBAAgB,SAAY,uBAAO,OAAO,IAAI,IAAI;AACtE,WAAK,WAAW;AAChB,WAAK,QAAQ;AAAA,IACjB;AACA,IAAAA,mBAAkB,UAAU,MAAM,WAAY;AAC1C,aAAO,KAAK;AAAA,IAChB;AACA,WAAO,eAAeA,mBAAkB,WAAW,QAAQ;AAAA,MACvD,KAAK,WAAY;AACb,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,mBAAkB,UAAU,SAAS,SAAU,gBAAgB,YAAY;AACvE,UAAI;AACJ,UAAI,2BAA2B,GAAG,cAAc,GAAG;AAC/C,aAAK;AAAA,MACT,OACK;AACD,aAAK,KAAK,OAAO;AACjB,qBAAa;AAAA,MACjB;AACA,UAAI,KAAK,aAAa,EAAE,MAAM,QAAW;AACrC,cAAM,IAAI,MAAM,QAAQ,KAAK,qBAAqB;AAAA,MACtD;AACA,UAAI,eAAe,QAAW;AAC1B,cAAM,IAAI,MAAM,mCAAmC,EAAE;AAAA,MACzD;AACA,WAAK,aAAa,EAAE,IAAI;AACxB,WAAK;AACL,aAAO;AAAA,IACX;AACA,IAAAA,mBAAkB,UAAU,SAAS,WAAY;AAC7C,WAAK;AACL,aAAO,KAAK,SAAS,SAAS;AAAA,IAClC;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAIF,IAAI;AAAA;AAAA,EAAiC,WAAY;AAC7C,aAASC,iBAAgB,eAAe;AACpC,UAAI,QAAQ;AACZ,WAAK,mBAAmB,uBAAO,OAAO,IAAI;AAC1C,UAAI,kBAAkB,QAAW;AAC7B,aAAK,iBAAiB;AACtB,YAAI,cAAc,iBAAiB;AAC/B,eAAK,qBAAqB,IAAI,kBAAkB,cAAc,iBAAiB;AAC/E,wBAAc,oBAAoB,KAAK,mBAAmB,IAAI;AAC9D,wBAAc,gBAAgB,QAAQ,SAAU,QAAQ;AACpD,gBAAI,iBAAiB,GAAG,MAAM,GAAG;AAC7B,kBAAI,iBAAiB,IAAI,mBAAmB,OAAO,OAAO,MAAM,kBAAkB;AAClF,oBAAM,iBAAiB,OAAO,aAAa,GAAG,IAAI;AAAA,YACtD;AAAA,UACJ,CAAC;AAAA,QACL,WACS,cAAc,SAAS;AAC5B,iBAAO,KAAK,cAAc,OAAO,EAAE,QAAQ,SAAU,KAAK;AACtD,gBAAI,iBAAiB,IAAI,mBAAmB,cAAc,QAAQ,GAAG,CAAC;AACtE,kBAAM,iBAAiB,GAAG,IAAI;AAAA,UAClC,CAAC;AAAA,QACL;AAAA,MACJ,OACK;AACD,aAAK,iBAAiB,CAAC;AAAA,MAC3B;AAAA,IACJ;AACA,WAAO,eAAeA,iBAAgB,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAKrD,KAAK,WAAY;AACb,aAAK,oBAAoB;AACzB,YAAI,KAAK,uBAAuB,QAAW;AACvC,cAAI,KAAK,mBAAmB,SAAS,GAAG;AACpC,iBAAK,eAAe,oBAAoB;AAAA,UAC5C,OACK;AACD,iBAAK,eAAe,oBAAoB,KAAK,mBAAmB,IAAI;AAAA,UACxE;AAAA,QACJ;AACA,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,iBAAgB,UAAU,oBAAoB,SAAU,KAAK;AACzD,UAAI,wCAAwC,GAAG,GAAG,GAAG;AACjD,aAAK,oBAAoB;AACzB,YAAI,KAAK,eAAe,oBAAoB,QAAW;AACnD,gBAAM,IAAI,MAAM,wDAAwD;AAAA,QAC5E;AACA,YAAI,eAAe,EAAE,KAAK,IAAI,KAAK,SAAS,IAAI,QAAQ;AACxD,YAAI,SAAS,KAAK,iBAAiB,aAAa,GAAG;AACnD,YAAI,CAAC,QAAQ;AACT,cAAI,QAAQ,CAAC;AACb,cAAI,mBAAmB;AAAA,YACnB;AAAA,YACA;AAAA,UACJ;AACA,eAAK,eAAe,gBAAgB,KAAK,gBAAgB;AACzD,mBAAS,IAAI,mBAAmB,OAAO,KAAK,kBAAkB;AAC9D,eAAK,iBAAiB,aAAa,GAAG,IAAI;AAAA,QAC9C;AACA,eAAO;AAAA,MACX,OACK;AACD,aAAK,YAAY;AACjB,YAAI,KAAK,eAAe,YAAY,QAAW;AAC3C,gBAAM,IAAI,MAAM,gEAAgE;AAAA,QACpF;AACA,YAAI,SAAS,KAAK,iBAAiB,GAAG;AACtC,YAAI,CAAC,QAAQ;AACT,cAAI,QAAQ,CAAC;AACb,eAAK,eAAe,QAAQ,GAAG,IAAI;AACnC,mBAAS,IAAI,mBAAmB,KAAK;AACrC,eAAK,iBAAiB,GAAG,IAAI;AAAA,QACjC;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AACA,IAAAA,iBAAgB,UAAU,sBAAsB,WAAY;AACxD,UAAI,KAAK,eAAe,oBAAoB,UAAa,KAAK,eAAe,YAAY,QAAW;AAChG,aAAK,qBAAqB,IAAI,kBAAkB;AAChD,aAAK,eAAe,kBAAkB,CAAC;AACvC,aAAK,eAAe,oBAAoB,KAAK,mBAAmB,IAAI;AAAA,MACxE;AAAA,IACJ;AACA,IAAAA,iBAAgB,UAAU,cAAc,WAAY;AAChD,UAAI,KAAK,eAAe,oBAAoB,UAAa,KAAK,eAAe,YAAY,QAAW;AAChG,aAAK,eAAe,UAAU,uBAAO,OAAO,IAAI;AAAA,MACpD;AAAA,IACJ;AACA,IAAAA,iBAAgB,UAAU,aAAa,SAAU,KAAK,qBAAqB,SAAS;AAChF,WAAK,oBAAoB;AACzB,UAAI,KAAK,eAAe,oBAAoB,QAAW;AACnD,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC5E;AACA,UAAI;AACJ,UAAI,iBAAiB,GAAG,mBAAmB,KAAK,2BAA2B,GAAG,mBAAmB,GAAG;AAChG,qBAAa;AAAA,MACjB,OACK;AACD,kBAAU;AAAA,MACd;AACA,UAAI;AACJ,UAAI;AACJ,UAAI,eAAe,QAAW;AAC1B,oBAAY,WAAW,OAAO,KAAK,OAAO;AAAA,MAC9C,OACK;AACD,aAAK,2BAA2B,GAAG,UAAU,IAAI,aAAa,KAAK,mBAAmB,OAAO,UAAU;AACvG,oBAAY,WAAW,OAAO,KAAK,SAAS,EAAE;AAAA,MAClD;AACA,WAAK,eAAe,gBAAgB,KAAK,SAAS;AAClD,UAAI,OAAO,QAAW;AAClB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,IAAAA,iBAAgB,UAAU,aAAa,SAAU,QAAQ,QAAQ,qBAAqB,SAAS;AAC3F,WAAK,oBAAoB;AACzB,UAAI,KAAK,eAAe,oBAAoB,QAAW;AACnD,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC5E;AACA,UAAI;AACJ,UAAI,iBAAiB,GAAG,mBAAmB,KAAK,2BAA2B,GAAG,mBAAmB,GAAG;AAChG,qBAAa;AAAA,MACjB,OACK;AACD,kBAAU;AAAA,MACd;AACA,UAAI;AACJ,UAAI;AACJ,UAAI,eAAe,QAAW;AAC1B,oBAAY,WAAW,OAAO,QAAQ,QAAQ,OAAO;AAAA,MACzD,OACK;AACD,aAAK,2BAA2B,GAAG,UAAU,IAAI,aAAa,KAAK,mBAAmB,OAAO,UAAU;AACvG,oBAAY,WAAW,OAAO,QAAQ,QAAQ,SAAS,EAAE;AAAA,MAC7D;AACA,WAAK,eAAe,gBAAgB,KAAK,SAAS;AAClD,UAAI,OAAO,QAAW;AAClB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,IAAAA,iBAAgB,UAAU,aAAa,SAAU,KAAK,qBAAqB,SAAS;AAChF,WAAK,oBAAoB;AACzB,UAAI,KAAK,eAAe,oBAAoB,QAAW;AACnD,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC5E;AACA,UAAI;AACJ,UAAI,iBAAiB,GAAG,mBAAmB,KAAK,2BAA2B,GAAG,mBAAmB,GAAG;AAChG,qBAAa;AAAA,MACjB,OACK;AACD,kBAAU;AAAA,MACd;AACA,UAAI;AACJ,UAAI;AACJ,UAAI,eAAe,QAAW;AAC1B,oBAAY,WAAW,OAAO,KAAK,OAAO;AAAA,MAC9C,OACK;AACD,aAAK,2BAA2B,GAAG,UAAU,IAAI,aAAa,KAAK,mBAAmB,OAAO,UAAU;AACvG,oBAAY,WAAW,OAAO,KAAK,SAAS,EAAE;AAAA,MAClD;AACA,WAAK,eAAe,gBAAgB,KAAK,SAAS;AAClD,UAAI,OAAO,QAAW;AAClB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAMK,IAAI;AAAA,CACV,SAAUC,yBAAwB;AAK/B,WAAS,OAAO,KAAK;AACjB,WAAO,EAAE,IAAS;AAAA,EACtB;AACA,EAAAA,wBAAuB,SAAS;AAIhC,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG;AAAA,EAC3D;AACA,EAAAA,wBAAuB,KAAK;AAChC,GAAG,2BAA2B,yBAAyB,CAAC,EAAE;AAKnD,IAAI;AAAA,CACV,SAAUC,kCAAiC;AAMxC,WAAS,OAAO,KAAK,SAAS;AAC1B,WAAO,EAAE,KAAU,QAAiB;AAAA,EACxC;AACA,EAAAA,iCAAgC,SAAS;AAIzC,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,KAAK,GAAG,QAAQ,UAAU,OAAO;AAAA,EAC5F;AACA,EAAAA,iCAAgC,KAAK;AACzC,GAAG,oCAAoC,kCAAkC,CAAC,EAAE;AAKrE,IAAI;AAAA,CACV,SAAUC,0CAAyC;AAMhD,WAAS,OAAO,KAAK,SAAS;AAC1B,WAAO,EAAE,KAAU,QAAiB;AAAA,EACxC;AACA,EAAAA,yCAAwC,SAAS;AAIjD,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,MAAM,UAAU,YAAY,QAAQ,GAAG,QAAQ,UAAU,OAAO;AAAA,EAC3H;AACA,EAAAA,yCAAwC,KAAK;AACjD,GAAG,4CAA4C,0CAA0C,CAAC,EAAE;AAKrF,IAAI;AAAA,CACV,SAAUC,mBAAkB;AAQzB,WAAS,OAAO,KAAK,YAAY,SAAS,MAAM;AAC5C,WAAO,EAAE,KAAU,YAAwB,SAAkB,KAAW;AAAA,EAC5E;AACA,EAAAA,kBAAiB,SAAS;AAI1B,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,KAAK,GAAG,OAAO,UAAU,UAAU,KAAK,GAAG,QAAQ,UAAU,OAAO,KAAK,GAAG,OAAO,UAAU,IAAI;AAAA,EAC5J;AACA,EAAAA,kBAAiB,KAAK;AAC1B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAQvC,IAAI;AAAA,CACV,SAAUC,aAAY;AAInB,EAAAA,YAAW,YAAY;AAIvB,EAAAA,YAAW,WAAW;AAC1B,GAAG,eAAe,aAAa,CAAC,EAAE;AAAA,CACjC,SAAUA,aAAY;AAInB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,cAAcA,YAAW,aAAa,cAAcA,YAAW;AAAA,EAC1E;AACA,EAAAA,YAAW,KAAK;AACpB,GAAG,eAAe,aAAa,CAAC,EAAE;AAC3B,IAAI;AAAA,CACV,SAAUC,gBAAe;AAItB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,cAAc,KAAK,KAAK,WAAW,GAAG,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,KAAK;AAAA,EAChG;AACA,EAAAA,eAAc,KAAK;AACvB,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAIjC,IAAI;AAAA,CACV,SAAUC,qBAAoB;AAC3B,EAAAA,oBAAmB,OAAO;AAC1B,EAAAA,oBAAmB,SAAS;AAC5B,EAAAA,oBAAmB,WAAW;AAC9B,EAAAA,oBAAmB,cAAc;AACjC,EAAAA,oBAAmB,QAAQ;AAC3B,EAAAA,oBAAmB,WAAW;AAC9B,EAAAA,oBAAmB,QAAQ;AAC3B,EAAAA,oBAAmB,YAAY;AAC/B,EAAAA,oBAAmB,SAAS;AAC5B,EAAAA,oBAAmB,WAAW;AAC9B,EAAAA,oBAAmB,OAAO;AAC1B,EAAAA,oBAAmB,QAAQ;AAC3B,EAAAA,oBAAmB,OAAO;AAC1B,EAAAA,oBAAmB,UAAU;AAC7B,EAAAA,oBAAmB,UAAU;AAC7B,EAAAA,oBAAmB,QAAQ;AAC3B,EAAAA,oBAAmB,OAAO;AAC1B,EAAAA,oBAAmB,YAAY;AAC/B,EAAAA,oBAAmB,SAAS;AAC5B,EAAAA,oBAAmB,aAAa;AAChC,EAAAA,oBAAmB,WAAW;AAC9B,EAAAA,oBAAmB,SAAS;AAC5B,EAAAA,oBAAmB,QAAQ;AAC3B,EAAAA,oBAAmB,WAAW;AAC9B,EAAAA,oBAAmB,gBAAgB;AACvC,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAK3C,IAAI;AAAA,CACV,SAAUC,mBAAkB;AAIzB,EAAAA,kBAAiB,YAAY;AAW7B,EAAAA,kBAAiB,UAAU;AAC/B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAOvC,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAI1B,EAAAA,mBAAkB,aAAa;AACnC,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAMzC,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAI1B,WAAS,OAAO,SAAS,QAAQ,SAAS;AACtC,WAAO,EAAE,SAAkB,QAAgB,QAAiB;AAAA,EAChE;AACA,EAAAA,mBAAkB,SAAS;AAI3B,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,aAAa,GAAG,OAAO,UAAU,OAAO,KAAKC,OAAM,GAAG,UAAU,MAAM,KAAKA,OAAM,GAAG,UAAU,OAAO;AAAA,EAChH;AACA,EAAAD,mBAAkB,KAAK;AAC3B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAOzC,IAAI;AAAA,CACV,SAAUE,iBAAgB;AAQvB,EAAAA,gBAAe,OAAO;AAUtB,EAAAA,gBAAe,oBAAoB;AACvC,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAKnC,IAAI;AAAA,CACV,SAAUC,iBAAgB;AAKvB,WAAS,OAAO,OAAO;AACnB,WAAO,EAAE,MAAa;AAAA,EAC1B;AACA,EAAAA,gBAAe,SAAS;AAC5B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAKnC,IAAI;AAAA,CACV,SAAUC,iBAAgB;AAOvB,WAAS,OAAO,OAAO,cAAc;AACjC,WAAO,EAAE,OAAO,QAAQ,QAAQ,CAAC,GAAG,cAAc,CAAC,CAAC,aAAa;AAAA,EACrE;AACA,EAAAA,gBAAe,SAAS;AAC5B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AACnC,IAAI;AAAA,CACV,SAAUC,eAAc;AAMrB,WAAS,cAAc,WAAW;AAC9B,WAAO,UAAU,QAAQ,yBAAyB,MAAM;AAAA,EAC5D;AACA,EAAAA,cAAa,gBAAgB;AAI7B,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,OAAO,SAAS,KAAM,GAAG,cAAc,SAAS,KAAK,GAAG,OAAO,UAAU,QAAQ,KAAK,GAAG,OAAO,UAAU,KAAK;AAAA,EAC7H;AACA,EAAAA,cAAa,KAAK;AACtB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAC/B,IAAI;AAAA,CACV,SAAUC,QAAO;AAId,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,CAAC,CAAC,aAAa,GAAG,cAAc,SAAS,MAAM,cAAc,GAAG,UAAU,QAAQ,KACrF,aAAa,GAAG,UAAU,QAAQ,KAClC,GAAG,WAAW,UAAU,UAAU,aAAa,EAAE,OAAO,MAAM,UAAU,UAAaL,OAAM,GAAG,MAAM,KAAK;AAAA,EACjH;AACA,EAAAK,OAAM,KAAK;AACf,GAAG,UAAU,QAAQ,CAAC,EAAE;AAKjB,IAAI;AAAA,CACV,SAAUC,uBAAsB;AAO7B,WAAS,OAAO,OAAO,eAAe;AAClC,WAAO,gBAAgB,EAAE,OAAc,cAA6B,IAAI,EAAE,MAAa;AAAA,EAC3F;AACA,EAAAA,sBAAqB,SAAS;AAClC,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAK/C,IAAI;AAAA,CACV,SAAUC,uBAAsB;AAC7B,WAAS,OAAO,OAAO,eAAe;AAClC,QAAI,aAAa,CAAC;AAClB,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,iBAAW,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,IACrC;AACA,QAAI,SAAS,EAAE,MAAa;AAC5B,QAAI,GAAG,QAAQ,aAAa,GAAG;AAC3B,aAAO,gBAAgB;AAAA,IAC3B;AACA,QAAI,GAAG,QAAQ,UAAU,GAAG;AACxB,aAAO,aAAa;AAAA,IACxB,OACK;AACD,aAAO,aAAa,CAAC;AAAA,IACzB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,sBAAqB,SAAS;AAClC,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAI/C,IAAI;AAAA,CACV,SAAUC,wBAAuB;AAI9B,EAAAA,uBAAsB,OAAO;AAI7B,EAAAA,uBAAsB,OAAO;AAI7B,EAAAA,uBAAsB,QAAQ;AAClC,GAAG,0BAA0B,wBAAwB,CAAC,EAAE;AAKjD,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAK1B,WAAS,OAAO,OAAO,MAAM;AACzB,QAAI,SAAS,EAAE,MAAa;AAC5B,QAAI,GAAG,OAAO,IAAI,GAAG;AACjB,aAAO,OAAO;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,mBAAkB,SAAS;AAC/B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAIzC,IAAI;AAAA,CACV,SAAUC,aAAY;AACnB,EAAAA,YAAW,OAAO;AAClB,EAAAA,YAAW,SAAS;AACpB,EAAAA,YAAW,YAAY;AACvB,EAAAA,YAAW,UAAU;AACrB,EAAAA,YAAW,QAAQ;AACnB,EAAAA,YAAW,SAAS;AACpB,EAAAA,YAAW,WAAW;AACtB,EAAAA,YAAW,QAAQ;AACnB,EAAAA,YAAW,cAAc;AACzB,EAAAA,YAAW,OAAO;AAClB,EAAAA,YAAW,YAAY;AACvB,EAAAA,YAAW,WAAW;AACtB,EAAAA,YAAW,WAAW;AACtB,EAAAA,YAAW,WAAW;AACtB,EAAAA,YAAW,SAAS;AACpB,EAAAA,YAAW,SAAS;AACpB,EAAAA,YAAW,UAAU;AACrB,EAAAA,YAAW,QAAQ;AACnB,EAAAA,YAAW,SAAS;AACpB,EAAAA,YAAW,MAAM;AACjB,EAAAA,YAAW,OAAO;AAClB,EAAAA,YAAW,aAAa;AACxB,EAAAA,YAAW,SAAS;AACpB,EAAAA,YAAW,QAAQ;AACnB,EAAAA,YAAW,WAAW;AACtB,EAAAA,YAAW,gBAAgB;AAC/B,GAAG,eAAe,aAAa,CAAC,EAAE;AAK3B,IAAI;AAAA,CACV,SAAUC,YAAW;AAIlB,EAAAA,WAAU,aAAa;AAC3B,GAAG,cAAc,YAAY,CAAC,EAAE;AACzB,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAU1B,WAAS,OAAO,MAAM,MAAM,OAAO,KAAK,eAAe;AACnD,QAAI,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA,UAAU,EAAE,KAAU,MAAa;AAAA,IACvC;AACA,QAAI,eAAe;AACf,aAAO,gBAAgB;AAAA,IAC3B;AACA,WAAO;AAAA,EACX;AACA,EAAAA,mBAAkB,SAAS;AAC/B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AACzC,IAAI;AAAA,CACV,SAAUC,iBAAgB;AAWvB,WAAS,OAAO,MAAM,QAAQ,MAAM,OAAO,gBAAgB,UAAU;AACjE,QAAI,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,QAAI,aAAa,QAAW;AACxB,aAAO,WAAW;AAAA,IACtB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,gBAAe,SAAS;AAIxB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,aACH,GAAG,OAAO,UAAU,IAAI,KAAK,GAAG,OAAO,UAAU,IAAI,KACrDb,OAAM,GAAG,UAAU,KAAK,KAAKA,OAAM,GAAG,UAAU,cAAc,MAC7D,UAAU,WAAW,UAAa,GAAG,OAAO,UAAU,MAAM,OAC5D,UAAU,eAAe,UAAa,GAAG,QAAQ,UAAU,UAAU,OACrE,UAAU,aAAa,UAAa,MAAM,QAAQ,UAAU,QAAQ,OACpE,UAAU,SAAS,UAAa,MAAM,QAAQ,UAAU,IAAI;AAAA,EACrE;AACA,EAAAa,gBAAe,KAAK;AACxB,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAInC,IAAI;AAAA,CACV,SAAUC,iBAAgB;AAIvB,EAAAA,gBAAe,QAAQ;AAIvB,EAAAA,gBAAe,WAAW;AAI1B,EAAAA,gBAAe,WAAW;AAY1B,EAAAA,gBAAe,kBAAkB;AAWjC,EAAAA,gBAAe,iBAAiB;AAahC,EAAAA,gBAAe,kBAAkB;AAMjC,EAAAA,gBAAe,SAAS;AAIxB,EAAAA,gBAAe,wBAAwB;AASvC,EAAAA,gBAAe,eAAe;AAClC,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAKnC,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAI1B,WAAS,OAAO,aAAa,MAAM;AAC/B,QAAI,SAAS,EAAE,YAAyB;AACxC,QAAI,SAAS,UAAa,SAAS,MAAM;AACrC,aAAO,OAAO;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,mBAAkB,SAAS;AAI3B,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,WAAW,UAAU,aAAa,WAAW,EAAE,MAAM,UAAU,SAAS,UAAa,GAAG,WAAW,UAAU,MAAM,GAAG,MAAM;AAAA,EACnK;AACA,EAAAA,mBAAkB,KAAK;AAC3B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AACzC,IAAI;AAAA,CACV,SAAUC,aAAY;AACnB,WAAS,OAAO,OAAO,qBAAqB,MAAM;AAC9C,QAAI,SAAS,EAAE,MAAa;AAC5B,QAAI,YAAY;AAChB,QAAI,OAAO,wBAAwB,UAAU;AACzC,kBAAY;AACZ,aAAO,OAAO;AAAA,IAClB,WACS,QAAQ,GAAG,mBAAmB,GAAG;AACtC,aAAO,UAAU;AAAA,IACrB,OACK;AACD,aAAO,OAAO;AAAA,IAClB;AACA,QAAI,aAAa,SAAS,QAAW;AACjC,aAAO,OAAO;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,YAAW,SAAS;AACpB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,aAAa,GAAG,OAAO,UAAU,KAAK,MACxC,UAAU,gBAAgB,UAAa,GAAG,WAAW,UAAU,aAAa,WAAW,EAAE,OACzF,UAAU,SAAS,UAAa,GAAG,OAAO,UAAU,IAAI,OACxD,UAAU,SAAS,UAAa,UAAU,YAAY,YACtD,UAAU,YAAY,UAAa,QAAQ,GAAG,UAAU,OAAO,OAC/D,UAAU,gBAAgB,UAAa,GAAG,QAAQ,UAAU,WAAW,OACvE,UAAU,SAAS,UAAa,cAAc,GAAG,UAAU,IAAI;AAAA,EACxE;AACA,EAAAA,YAAW,KAAK;AACpB,GAAG,eAAe,aAAa,CAAC,EAAE;AAK3B,IAAI;AAAA,CACV,SAAUC,WAAU;AAIjB,WAAS,OAAO,OAAO,MAAM;AACzB,QAAI,SAAS,EAAE,MAAa;AAC5B,QAAI,GAAG,QAAQ,IAAI,GAAG;AAClB,aAAO,OAAO;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,UAAS,SAAS;AAIlB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAKjB,OAAM,GAAG,UAAU,KAAK,MAAM,GAAG,UAAU,UAAU,OAAO,KAAK,QAAQ,GAAG,UAAU,OAAO;AAAA,EACjI;AACA,EAAAiB,UAAS,KAAK;AAClB,GAAG,aAAa,WAAW,CAAC,EAAE;AAKvB,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAI1B,WAAS,OAAO,SAAS,cAAc;AACnC,WAAO,EAAE,SAAkB,aAA2B;AAAA,EAC1D;AACA,EAAAA,mBAAkB,SAAS;AAI3B,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,SAAS,UAAU,OAAO,KAAK,GAAG,QAAQ,UAAU,YAAY;AAAA,EACvG;AACA,EAAAA,mBAAkB,KAAK;AAC3B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAKzC,IAAI;AAAA,CACV,SAAUC,eAAc;AAIrB,WAAS,OAAO,OAAO,QAAQ,MAAM;AACjC,WAAO,EAAE,OAAc,QAAgB,KAAW;AAAA,EACtD;AACA,EAAAA,cAAa,SAAS;AAItB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAKnB,OAAM,GAAG,UAAU,KAAK,MAAM,GAAG,UAAU,UAAU,MAAM,KAAK,GAAG,OAAO,UAAU,MAAM;AAAA,EAC9H;AACA,EAAAmB,cAAa,KAAK;AACtB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAK/B,IAAI;AAAA,CACV,SAAUC,iBAAgB;AAMvB,WAAS,OAAO,OAAO,QAAQ;AAC3B,WAAO,EAAE,OAAc,OAAe;AAAA,EAC1C;AACA,EAAAA,gBAAe,SAAS;AACxB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,cAAc,UAAapB,OAAM,GAAG,UAAU,KAAK,MAAM,UAAU,WAAW,UAAaoB,gBAAe,GAAG,UAAU,MAAM;AAAA,EACxI;AACA,EAAAA,gBAAe,KAAK;AACxB,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAKnC,IAAI;AAAA,CACV,SAAUC,eAAc;AAOrB,WAAS,OAAO,KAAK,YAAY,SAAS,SAAS;AAC/C,WAAO,IAAI,iBAAiB,KAAK,YAAY,SAAS,OAAO;AAAA,EACjE;AACA,EAAAA,cAAa,SAAS;AAItB,WAAS,GAAG,OAAO;AACf,QAAI,YAAY;AAChB,WAAO,GAAG,QAAQ,SAAS,KAAK,GAAG,OAAO,UAAU,GAAG,MAAM,GAAG,UAAU,UAAU,UAAU,KAAK,GAAG,OAAO,UAAU,UAAU,MAAM,GAAG,SAAS,UAAU,SAAS,KAC/J,GAAG,KAAK,UAAU,OAAO,KAAK,GAAG,KAAK,UAAU,UAAU,KAAK,GAAG,KAAK,UAAU,QAAQ,IAAI,OAAO;AAAA,EAC/G;AACA,EAAAA,cAAa,KAAK;AAClB,WAAS,WAAW,UAAU,OAAO;AACjC,QAAI,OAAO,SAAS,QAAQ;AAC5B,QAAI,cAAc,UAAU,OAAO,SAAU,GAAG,GAAG;AAC/C,UAAI,OAAO,EAAE,MAAM,MAAM,OAAO,EAAE,MAAM,MAAM;AAC9C,UAAI,SAAS,GAAG;AACZ,eAAO,EAAE,MAAM,MAAM,YAAY,EAAE,MAAM,MAAM;AAAA,MACnD;AACA,aAAO;AAAA,IACX,CAAC;AACD,QAAI,qBAAqB,KAAK;AAC9B,aAAS,IAAI,YAAY,SAAS,GAAG,KAAK,GAAG,KAAK;AAC9C,UAAI,IAAI,YAAY,CAAC;AACrB,UAAI,cAAc,SAAS,SAAS,EAAE,MAAM,KAAK;AACjD,UAAI,YAAY,SAAS,SAAS,EAAE,MAAM,GAAG;AAC7C,UAAI,aAAa,oBAAoB;AACjC,eAAO,KAAK,UAAU,GAAG,WAAW,IAAI,EAAE,UAAU,KAAK,UAAU,WAAW,KAAK,MAAM;AAAA,MAC7F,OACK;AACD,cAAM,IAAI,MAAM,kBAAkB;AAAA,MACtC;AACA,2BAAqB;AAAA,IACzB;AACA,WAAO;AAAA,EACX;AACA,EAAAA,cAAa,aAAa;AAC1B,WAAS,UAAU,MAAM,SAAS;AAC9B,QAAI,KAAK,UAAU,GAAG;AAElB,aAAO;AAAA,IACX;AACA,QAAI,IAAK,KAAK,SAAS,IAAK;AAC5B,QAAI,OAAO,KAAK,MAAM,GAAG,CAAC;AAC1B,QAAI,QAAQ,KAAK,MAAM,CAAC;AACxB,cAAU,MAAM,OAAO;AACvB,cAAU,OAAO,OAAO;AACxB,QAAI,UAAU;AACd,QAAI,WAAW;AACf,QAAI,IAAI;AACR,WAAO,UAAU,KAAK,UAAU,WAAW,MAAM,QAAQ;AACrD,UAAI,MAAM,QAAQ,KAAK,OAAO,GAAG,MAAM,QAAQ,CAAC;AAChD,UAAI,OAAO,GAAG;AAEV,aAAK,GAAG,IAAI,KAAK,SAAS;AAAA,MAC9B,OACK;AAED,aAAK,GAAG,IAAI,MAAM,UAAU;AAAA,MAChC;AAAA,IACJ;AACA,WAAO,UAAU,KAAK,QAAQ;AAC1B,WAAK,GAAG,IAAI,KAAK,SAAS;AAAA,IAC9B;AACA,WAAO,WAAW,MAAM,QAAQ;AAC5B,WAAK,GAAG,IAAI,MAAM,UAAU;AAAA,IAChC;AACA,WAAO;AAAA,EACX;AACJ,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAItC,IAAI;AAAA;AAAA,EAAkC,WAAY;AAC9C,aAASC,kBAAiB,KAAK,YAAY,SAAS,SAAS;AACzD,WAAK,OAAO;AACZ,WAAK,cAAc;AACnB,WAAK,WAAW;AAChB,WAAK,WAAW;AAChB,WAAK,eAAe;AAAA,IACxB;AACA,WAAO,eAAeA,kBAAiB,WAAW,OAAO;AAAA,MACrD,KAAK,WAAY;AACb,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,kBAAiB,WAAW,cAAc;AAAA,MAC5D,KAAK,WAAY;AACb,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,kBAAiB,WAAW,WAAW;AAAA,MACzD,KAAK,WAAY;AACb,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,kBAAiB,UAAU,UAAU,SAAU,OAAO;AAClD,UAAI,OAAO;AACP,YAAI,QAAQ,KAAK,SAAS,MAAM,KAAK;AACrC,YAAI,MAAM,KAAK,SAAS,MAAM,GAAG;AACjC,eAAO,KAAK,SAAS,UAAU,OAAO,GAAG;AAAA,MAC7C;AACA,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,kBAAiB,UAAU,SAAS,SAAU,OAAO,SAAS;AAC1D,WAAK,WAAW,MAAM;AACtB,WAAK,WAAW;AAChB,WAAK,eAAe;AAAA,IACxB;AACA,IAAAA,kBAAiB,UAAU,iBAAiB,WAAY;AACpD,UAAI,KAAK,iBAAiB,QAAW;AACjC,YAAI,cAAc,CAAC;AACnB,YAAI,OAAO,KAAK;AAChB,YAAI,cAAc;AAClB,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,cAAI,aAAa;AACb,wBAAY,KAAK,CAAC;AAClB,0BAAc;AAAA,UAClB;AACA,cAAI,KAAK,KAAK,OAAO,CAAC;AACtB,wBAAe,OAAO,QAAQ,OAAO;AACrC,cAAI,OAAO,QAAQ,IAAI,IAAI,KAAK,UAAU,KAAK,OAAO,IAAI,CAAC,MAAM,MAAM;AACnE;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,eAAe,KAAK,SAAS,GAAG;AAChC,sBAAY,KAAK,KAAK,MAAM;AAAA,QAChC;AACA,aAAK,eAAe;AAAA,MACxB;AACA,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,kBAAiB,UAAU,aAAa,SAAU,QAAQ;AACtD,eAAS,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK,SAAS,MAAM,GAAG,CAAC;AAC3D,UAAI,cAAc,KAAK,eAAe;AACtC,UAAI,MAAM,GAAG,OAAO,YAAY;AAChC,UAAI,SAAS,GAAG;AACZ,eAAO,SAAS,OAAO,GAAG,MAAM;AAAA,MACpC;AACA,aAAO,MAAM,MAAM;AACf,YAAI,MAAM,KAAK,OAAO,MAAM,QAAQ,CAAC;AACrC,YAAI,YAAY,GAAG,IAAI,QAAQ;AAC3B,iBAAO;AAAA,QACX,OACK;AACD,gBAAM,MAAM;AAAA,QAChB;AAAA,MACJ;AAGA,UAAI,OAAO,MAAM;AACjB,aAAO,SAAS,OAAO,MAAM,SAAS,YAAY,IAAI,CAAC;AAAA,IAC3D;AACA,IAAAA,kBAAiB,UAAU,WAAW,SAAU,UAAU;AACtD,UAAI,cAAc,KAAK,eAAe;AACtC,UAAI,SAAS,QAAQ,YAAY,QAAQ;AACrC,eAAO,KAAK,SAAS;AAAA,MACzB,WACS,SAAS,OAAO,GAAG;AACxB,eAAO;AAAA,MACX;AACA,UAAI,aAAa,YAAY,SAAS,IAAI;AAC1C,UAAI,iBAAkB,SAAS,OAAO,IAAI,YAAY,SAAU,YAAY,SAAS,OAAO,CAAC,IAAI,KAAK,SAAS;AAC/G,aAAO,KAAK,IAAI,KAAK,IAAI,aAAa,SAAS,WAAW,cAAc,GAAG,UAAU;AAAA,IACzF;AACA,WAAO,eAAeA,kBAAiB,WAAW,aAAa;AAAA,MAC3D,KAAK,WAAY;AACb,eAAO,KAAK,eAAe,EAAE;AAAA,MACjC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,IAAI;AAAA,CACH,SAAUC,KAAI;AACX,MAAI,WAAW,OAAO,UAAU;AAChC,WAAS,QAAQ,OAAO;AACpB,WAAO,OAAO,UAAU;AAAA,EAC5B;AACA,EAAAA,IAAG,UAAU;AACb,WAASC,WAAU,OAAO;AACtB,WAAO,OAAO,UAAU;AAAA,EAC5B;AACA,EAAAD,IAAG,YAAYC;AACf,WAAS,QAAQ,OAAO;AACpB,WAAO,UAAU,QAAQ,UAAU;AAAA,EACvC;AACA,EAAAD,IAAG,UAAU;AACb,WAAS,OAAO,OAAO;AACnB,WAAO,SAAS,KAAK,KAAK,MAAM;AAAA,EACpC;AACA,EAAAA,IAAG,SAAS;AACZ,WAAS,OAAO,OAAO;AACnB,WAAO,SAAS,KAAK,KAAK,MAAM;AAAA,EACpC;AACA,EAAAA,IAAG,SAAS;AACZ,WAAS,YAAY,OAAO,KAAK,KAAK;AAClC,WAAO,SAAS,KAAK,KAAK,MAAM,qBAAqB,OAAO,SAAS,SAAS;AAAA,EAClF;AACA,EAAAA,IAAG,cAAc;AACjB,WAASE,SAAQ,OAAO;AACpB,WAAO,SAAS,KAAK,KAAK,MAAM,qBAAqB,eAAe,SAAS,SAAS;AAAA,EAC1F;AACA,EAAAF,IAAG,UAAUE;AACb,WAASC,UAAS,OAAO;AACrB,WAAO,SAAS,KAAK,KAAK,MAAM,qBAAqB,KAAK,SAAS,SAAS;AAAA,EAChF;AACA,EAAAH,IAAG,WAAWG;AACd,WAAS,KAAK,OAAO;AACjB,WAAO,SAAS,KAAK,KAAK,MAAM;AAAA,EACpC;AACA,EAAAH,IAAG,OAAO;AACV,WAAS,cAAc,OAAO;AAI1B,WAAO,UAAU,QAAQ,OAAO,UAAU;AAAA,EAC9C;AACA,EAAAA,IAAG,gBAAgB;AACnB,WAAS,WAAW,OAAO,OAAO;AAC9B,WAAO,MAAM,QAAQ,KAAK,KAAK,MAAM,MAAM,KAAK;AAAA,EACpD;AACA,EAAAA,IAAG,aAAa;AACpB,GAAG,OAAO,KAAK,CAAC,EAAE;;;AC52DlB,IAAI;AAAA;AAAA,EAAoC,WAAY;AAChD,aAASI,oBAAmB,aAAa,SAAS,UAAU;AACxD,UAAI,QAAQ;AACZ,WAAK,cAAc;AACnB,WAAK,UAAU;AACf,WAAK,eAAe,CAAC;AACrB,WAAK,YAAY,uBAAO,OAAO,IAAI;AACnC,UAAI,aAAa,SAAU,OAAO;AAC9B,YAAI,SAAS,MAAM,cAAc;AACjC,YAAI,WAAW,MAAM,aAAa;AAC9B;AAAA,QACJ;AACA,YAAI;AACJ,cAAM,UAAU,MAAM,IAAI,SAAS,CAAC,IAAI,MAAM,mBAAmB,WAAY;AACzE,uBAAa,MAAM;AACnB,mBAAS,OAAO,WAAW,WAAY;AAAE,mBAAO,MAAM,YAAY,MAAM,KAAK,MAAM;AAAA,UAAG,GAAG,GAAG;AAAA,QAChG,CAAC;AACD,cAAM,YAAY,MAAM,KAAK,MAAM;AAAA,MACvC;AACA,UAAI,iBAAiB,SAAU,OAAO;AAClC,eAAO,gBAAgB,OAAO,MAAM,aAAa,CAAC,CAAC;AACnD,YAAI,SAAS,MAAM,IAAI,SAAS;AAChC,YAAI,WAAW,MAAM,UAAU,MAAM;AACrC,YAAI,UAAU;AACV,mBAAS,QAAQ;AACjB,iBAAO,MAAM,UAAU,MAAM;AAAA,QACjC;AAAA,MACJ;AACA,WAAK,aAAa,KAAK,OAAO,iBAAiB,UAAU,CAAC;AAC1D,WAAK,aAAa,KAAK,OAAO,mBAAmB,SAAU,OAAO;AAC9D,uBAAe,KAAK;AACpB,cAAM,aAAa,MAAM,GAAG;AAAA,MAChC,CAAC,CAAC;AACF,WAAK,aAAa,KAAK,OAAO,yBAAyB,SAAU,OAAO;AACpE,uBAAe,MAAM,KAAK;AAC1B,mBAAW,MAAM,KAAK;AACtB,cAAM,aAAa,MAAM,MAAM,GAAG;AAAA,MACtC,CAAC,CAAC;AACF,WAAK,aAAa,KAAK,SAAS,YAAY,SAAU,GAAG;AACrD,eAAO,UAAU,EAAE,QAAQ,SAAU,OAAO;AACxC,cAAI,MAAM,cAAc,MAAM,MAAM,aAAa;AAC7C,2BAAe,KAAK;AACpB,uBAAW,KAAK;AAAA,UACpB;AAAA,QACJ,CAAC;AAAA,MACL,CAAC,CAAC;AACF,WAAK,aAAa,KAAK;AAAA,QACnB,SAAS,WAAY;AACjB,iBAAO,UAAU,EAAE,QAAQ,cAAc;AACzC,mBAAS,OAAO,MAAM,WAAW;AAC7B,kBAAM,UAAU,GAAG,EAAE,QAAQ;AAAA,UACjC;AAAA,QACJ;AAAA,MACJ,CAAC;AACD,aAAO,UAAU,EAAE,QAAQ,UAAU;AAAA,IACzC;AACA,IAAAA,oBAAmB,UAAU,UAAU,WAAY;AAC/C,WAAK,aAAa,QAAQ,SAAU,GAAG;AAAE,eAAO,KAAK,EAAE,QAAQ;AAAA,MAAG,CAAC;AACnE,WAAK,eAAe,CAAC;AAAA,IACzB;AACA,IAAAA,oBAAmB,UAAU,eAAe,SAAU,UAAU;AAC5D,WAAK,QAAQ,EAAE,KAAK,SAAU,QAAQ;AAClC,eAAO,YAAY,SAAS,SAAS,CAAC;AAAA,MAC1C,CAAC;AAAA,IACL;AACA,IAAAA,oBAAmB,UAAU,cAAc,SAAU,UAAU,YAAY;AACvE,WAAK,QAAQ,QAAQ,EAChB,KAAK,SAAU,QAAQ;AACxB,eAAO,OAAO,aAAa,SAAS,SAAS,CAAC,EAAE,KAAK,SAAU,aAAa;AACxE,cAAI,UAAU,YAAY,IAAI,SAAU,GAAG;AAAE,mBAAO,cAAc,UAAU,CAAC;AAAA,UAAG,CAAC;AACjF,cAAI,QAAQ,OAAO,SAAS,QAAQ;AACpC,cAAI,SAAS,MAAM,cAAc,MAAM,YAAY;AAC/C,mBAAO,gBAAgB,OAAO,YAAY,OAAO;AAAA,UACrD;AAAA,QACJ,CAAC;AAAA,MACL,CAAC,EACI,KAAK,QAAW,SAAU,KAAK;AAChC,gBAAQ,MAAM,GAAG;AAAA,MACrB,CAAC;AAAA,IACL;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,SAAS,WAAW,YAAY;AAC5B,UAAQ,YAAY;AAAA,IAChB,KAAa,mBAAmB;AAC5B,aAAO,eAAe;AAAA,IAC1B,KAAa,mBAAmB;AAC5B,aAAO,eAAe;AAAA,IAC1B,KAAa,mBAAmB;AAC5B,aAAO,eAAe;AAAA,IAC1B,KAAa,mBAAmB;AAC5B,aAAO,eAAe;AAAA,IAC1B;AACI,aAAO,eAAe;AAAA,EAC9B;AACJ;AACA,SAAS,cAAc,UAAU,MAAM;AACnC,MAAI,OAAO,OAAO,KAAK,SAAS,WAAW,OAAO,KAAK,IAAI,IAAI,KAAK;AACpE,SAAO;AAAA,IACH,UAAU,WAAW,KAAK,QAAQ;AAAA,IAClC,iBAAiB,KAAK,MAAM,MAAM,OAAO;AAAA,IACzC,aAAa,KAAK,MAAM,MAAM,YAAY;AAAA,IAC1C,eAAe,KAAK,MAAM,IAAI,OAAO;AAAA,IACrC,WAAW,KAAK,MAAM,IAAI,YAAY;AAAA,IACtC,SAAS,KAAK;AAAA,IACd;AAAA,IACA,QAAQ,KAAK;AAAA,EACjB;AACJ;AAEA,SAAS,aAAa,UAAU;AAC5B,MAAI,CAAC,UAAU;AACX,WAAO;AAAA,EACX;AACA,SAAO,EAAE,WAAW,SAAS,SAAS,GAAG,MAAM,SAAS,aAAa,EAAE;AAC3E;AACA,SAAS,UAAU,OAAO;AACtB,MAAI,CAAC,OAAO;AACR,WAAO;AAAA,EACX;AACA,SAAO;AAAA,IACH,OAAO;AAAA,MACH,MAAM,MAAM,kBAAkB;AAAA,MAC9B,WAAW,MAAM,cAAc;AAAA,IACnC;AAAA,IACA,KAAK,EAAE,MAAM,MAAM,gBAAgB,GAAG,WAAW,MAAM,YAAY,EAAE;AAAA,EACzE;AACJ;AACA,SAAS,QAAQ,OAAO;AACpB,MAAI,CAAC,OAAO;AACR,WAAO;AAAA,EACX;AACA,SAAO,IAAI,MAAM,MAAM,MAAM,OAAO,GAAG,MAAM,MAAM,YAAY,GAAG,MAAM,IAAI,OAAO,GAAG,MAAM,IAAI,YAAY,CAAC;AACjH;AACA,SAAS,oBAAoB,MAAM;AAC/B,SAAQ,OAAO,KAAK,WAAW,eAC3B,OAAO,KAAK,YAAY;AAChC;AACA,SAAS,qBAAqB,MAAM;AAChC,MAAI,YAAY,UAAU;AAC1B,UAAQ,MAAM;AAAA,IACV,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,IACrB,KAAa,mBAAmB;AAC5B,aAAO,UAAU;AAAA,EACzB;AACA,SAAO,UAAU;AACrB;AA2CA,SAAS,WAAW,UAAU;AAC1B,MAAI,CAAC,UAAU;AACX,WAAO;AAAA,EACX;AACA,SAAO;AAAA,IACH,OAAO,QAAQ,SAAS,KAAK;AAAA,IAC7B,MAAM,SAAS;AAAA,EACnB;AACJ;AACA,SAAS,UAAU,GAAG;AAClB,SAAO,KAAK,EAAE,YAAY,iCACpB,EAAE,IAAI,EAAE,SAAS,OAAO,EAAE,OAAO,WAAW,EAAE,UAAU,IACxD;AACV;AACA,IAAI;AAAA;AAAA,EAAmC,WAAY;AAC/C,aAASC,mBAAkB,SAAS;AAChC,WAAK,UAAU;AAAA,IACnB;AACA,WAAO,eAAeA,mBAAkB,WAAW,qBAAqB;AAAA,MACpE,KAAK,WAAY;AACb,eAAO,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,mBAAkB,UAAU,yBAAyB,SAAU,OAAO,UAAU,SAAS,OAAO;AAC5F,UAAI,WAAW,MAAM;AACrB,aAAO,KAAK,QAAQ,QAAQ,EACvB,KAAK,SAAU,QAAQ;AACxB,eAAO,OAAO,WAAW,SAAS,SAAS,GAAG,aAAa,QAAQ,CAAC;AAAA,MACxE,CAAC,EACI,KAAK,SAAU,MAAM;AACtB,YAAI,CAAC,MAAM;AACP;AAAA,QACJ;AACA,YAAI,WAAW,MAAM,qBAAqB,QAAQ;AAClD,YAAI,YAAY,IAAI,MAAM,SAAS,YAAY,SAAS,aAAa,SAAS,YAAY,SAAS,SAAS;AAC5G,YAAI,QAAQ,KAAK,MAAM,IAAI,SAAU,OAAO;AACxC,cAAI,OAAO;AAAA,YACP,OAAO,MAAM;AAAA,YACb,YAAY,MAAM,cAAc,MAAM;AAAA,YACtC,UAAU,MAAM;AAAA,YAChB,YAAY,MAAM;AAAA,YAClB,eAAe,MAAM;AAAA,YACrB,QAAQ,MAAM;AAAA,YACd,SAAS,UAAU,MAAM,OAAO;AAAA,YAChC,OAAO;AAAA,YACP,MAAM,qBAAqB,MAAM,IAAI;AAAA,UACzC;AACA,cAAI,MAAM,UAAU;AAChB,gBAAI,oBAAoB,MAAM,QAAQ,GAAG;AACrC,mBAAK,QAAQ;AAAA,gBACT,QAAQ,QAAQ,MAAM,SAAS,MAAM;AAAA,gBACrC,SAAS,QAAQ,MAAM,SAAS,OAAO;AAAA,cAC3C;AAAA,YACJ,OACK;AACD,mBAAK,QAAQ,QAAQ,MAAM,SAAS,KAAK;AAAA,YAC7C;AACA,iBAAK,aAAa,MAAM,SAAS;AAAA,UACrC;AACA,cAAI,MAAM,qBAAqB;AAC3B,iBAAK,sBAAsB,MAAM,oBAAoB,IAAI,UAAU;AAAA,UACvE;AACA,cAAI,MAAM,qBAA6B,iBAAiB,SAAS;AAC7D,iBAAK,kBAAkB,UAAU,6BAA6B;AAAA,UAClE;AACA,iBAAO;AAAA,QACX,CAAC;AACD,eAAO;AAAA,UACH,cAAc,KAAK;AAAA,UACnB,aAAa;AAAA,QACjB;AAAA,MACJ,CAAC;AAAA,IACL;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,SAAS,gBAAgB,OAAO;AAC5B,SAAQ,SAAS,OAAO,UAAU,YAAY,OAAO,MAAM,SAAS;AACxE;AACA,SAAS,iBAAiB,OAAO;AAC7B,MAAI,OAAO,UAAU,UAAU;AAC3B,WAAO;AAAA,MACH,OAAO;AAAA,IACX;AAAA,EACJ;AACA,MAAI,gBAAgB,KAAK,GAAG;AACxB,QAAI,MAAM,SAAS,aAAa;AAC5B,aAAO;AAAA,QACH,OAAO,MAAM,MAAM,QAAQ,yBAAyB,MAAM;AAAA,MAC9D;AAAA,IACJ;AACA,WAAO;AAAA,MACH,OAAO,MAAM;AAAA,IACjB;AAAA,EACJ;AACA,SAAO,EAAE,OAAO,QAAQ,MAAM,WAAW,OAAO,MAAM,QAAQ,UAAU;AAC5E;AACA,SAAS,oBAAoB,UAAU;AACnC,MAAI,CAAC,UAAU;AACX,WAAO;AAAA,EACX;AACA,MAAI,MAAM,QAAQ,QAAQ,GAAG;AACzB,WAAO,SAAS,IAAI,gBAAgB;AAAA,EACxC;AACA,SAAO,CAAC,iBAAiB,QAAQ,CAAC;AACtC;AAEA,IAAI;AAAA;AAAA,EAA8B,WAAY;AAC1C,aAASC,cAAa,SAAS;AAC3B,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,cAAa,UAAU,eAAe,SAAU,OAAO,UAAU,OAAO;AACpE,UAAI,WAAW,MAAM;AACrB,aAAO,KAAK,QAAQ,QAAQ,EACvB,KAAK,SAAU,QAAQ;AACxB,eAAO,OAAO,QAAQ,SAAS,SAAS,GAAG,aAAa,QAAQ,CAAC;AAAA,MACrE,CAAC,EACI,KAAK,SAAU,MAAM;AACtB,YAAI,CAAC,MAAM;AACP;AAAA,QACJ;AACA,eAAO;AAAA,UACH,OAAO,QAAQ,KAAK,KAAK;AAAA,UACzB,UAAU,oBAAoB,KAAK,QAAQ;AAAA,QAC/C;AAAA,MACJ,CAAC;AAAA,IACL;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAUF,SAAS,aAAa,MAAM;AACxB,MAAI,QAAQ,UAAU;AACtB,UAAQ,MAAM;AAAA,IACV,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,IACjB,KAAa,WAAW;AACpB,aAAO,MAAM;AAAA,EACrB;AACA,SAAO,MAAM;AACjB;AACA,IAAI;AAAA;AAAA,EAAuC,WAAY;AACnD,aAASC,uBAAsB,SAAS;AACpC,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,uBAAsB,UAAU,yBAAyB,SAAU,OAAO,OAAO;AAC7E,UAAI,WAAW,MAAM;AACrB,aAAO,KAAK,QAAQ,QAAQ,EACvB,KAAK,SAAU,QAAQ;AAAE,eAAO,OAAO,oBAAoB,SAAS,SAAS,CAAC;AAAA,MAAG,CAAC,EAClF,KAAK,SAAU,OAAO;AACvB,YAAI,CAAC,OAAO;AACR;AAAA,QACJ;AACA,eAAO,MAAM,IAAI,SAAU,MAAM;AAAE,iBAAQ;AAAA,YACvC,MAAM,KAAK;AAAA,YACX,QAAQ;AAAA,YACR,eAAe,KAAK;AAAA,YACpB,MAAM,aAAa,KAAK,IAAI;AAAA,YAC5B,OAAO,QAAQ,KAAK,SAAS,KAAK;AAAA,YAClC,gBAAgB,QAAQ,KAAK,SAAS,KAAK;AAAA,YAC3C,MAAM,CAAC;AAAA,UACX;AAAA,QAAI,CAAC;AAAA,MACT,CAAC;AAAA,IACL;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,SAAS,sBAAsB,SAAS;AACpC,SAAO;AAAA,IACH,SAAS,QAAQ;AAAA,IACjB,cAAc,QAAQ;AAAA,EAC1B;AACJ;AACA,IAAI;AAAA;AAAA,EAAgD,WAAY;AAC5D,aAASC,gCAA+B,SAAS;AAC7C,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,gCAA+B,UAAU,iCAAiC,SAAU,OAAO,SAAS,OAAO;AACvG,UAAI,WAAW,MAAM;AACrB,aAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,SAAU,QAAQ;AACjD,eAAO,OACF,OAAO,SAAS,SAAS,GAAG,MAAM,sBAAsB,OAAO,CAAC,EAChE,KAAK,SAAU,OAAO;AACvB,cAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAC9B;AAAA,UACJ;AACA,iBAAO,MAAM,IAAI,UAAU;AAAA,QAC/B,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,IAAI;AAAA;AAAA,EAAqD,WAAY;AACjE,aAASC,qCAAoC,SAAS;AAClD,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,qCAAoC,UAAU,sCAAsC,SAAU,OAAO,OAAO,SAAS,OAAO;AACxH,UAAI,WAAW,MAAM;AACrB,aAAO,KAAK,QAAQ,QAAQ,EAAE,KAAK,SAAU,QAAQ;AACjD,eAAO,OACF,OAAO,SAAS,SAAS,GAAG,UAAU,KAAK,GAAG,sBAAsB,OAAO,CAAC,EAC5E,KAAK,SAAU,OAAO;AACvB,cAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAC9B;AAAA,UACJ;AACA,iBAAO,MAAM,IAAI,UAAU;AAAA,QAC/B,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,IAAI;AAAA;AAAA,EAAsC,WAAY;AAClD,aAASC,sBAAqB,SAAS;AACnC,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,sBAAqB,UAAU,wBAAwB,SAAU,OAAO,OAAO;AAC3E,UAAI,WAAW,MAAM;AACrB,aAAO,KAAK,QAAQ,QAAQ,EACvB,KAAK,SAAU,QAAQ;AAAE,eAAO,OAAO,mBAAmB,SAAS,SAAS,CAAC;AAAA,MAAG,CAAC,EACjF,KAAK,SAAU,OAAO;AACvB,YAAI,CAAC,OAAO;AACR;AAAA,QACJ;AACA,eAAO,MAAM,IAAI,SAAU,MAAM;AAAE,iBAAQ;AAAA,YACvC,OAAO,KAAK;AAAA,YACZ,OAAO,QAAQ,KAAK,KAAK;AAAA,UAC7B;AAAA,QAAI,CAAC;AAAA,MACT,CAAC;AAAA,IACL;AACA,IAAAA,sBAAqB,UAAU,4BAA4B,SAAU,OAAO,MAAM,OAAO;AACrF,UAAI,WAAW,MAAM;AACrB,aAAO,KAAK,QAAQ,QAAQ,EACvB,KAAK,SAAU,QAAQ;AACxB,eAAO,OAAO,sBAAsB,SAAS,SAAS,GAAG,KAAK,OAAO,UAAU,KAAK,KAAK,CAAC;AAAA,MAC9F,CAAC,EACI,KAAK,SAAU,eAAe;AAC/B,YAAI,CAAC,eAAe;AAChB;AAAA,QACJ;AACA,eAAO,cAAc,IAAI,SAAU,cAAc;AAC7C,cAAI,OAAO;AAAA,YACP,OAAO,aAAa;AAAA,UACxB;AACA,cAAI,aAAa,UAAU;AACvB,iBAAK,WAAW,WAAW,aAAa,QAAQ;AAAA,UACpD;AACA,cAAI,aAAa,qBAAqB;AAClC,iBAAK,sBAAsB,aAAa,oBAAoB,IAAI,UAAU;AAAA,UAC9E;AACA,iBAAO;AAAA,QACX,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,IAAI;AAAA;AAAA,EAAqC,WAAY;AACjD,aAASC,qBAAoB,SAAS;AAClC,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,qBAAoB,UAAU,uBAAuB,SAAU,OAAO,SAAS,OAAO;AAClF,UAAI,WAAW,MAAM;AACrB,aAAO,KAAK,QAAQ,QAAQ,EACvB,KAAK,SAAU,QAAQ;AAAE,eAAO,OAAO,iBAAiB,SAAS,SAAS,GAAG,OAAO;AAAA,MAAG,CAAC,EACxF,KAAK,SAAU,QAAQ;AACxB,YAAI,CAAC,QAAQ;AACT;AAAA,QACJ;AACA,eAAO,OAAO,IAAI,SAAU,OAAO;AAC/B,cAAI,SAAS;AAAA,YACT,OAAO,MAAM,YAAY;AAAA,YACzB,KAAK,MAAM,UAAU;AAAA,UACzB;AACA,cAAI,OAAO,MAAM,SAAS,aAAa;AACnC,mBAAO,OAAO,mBAAmB,MAAM,IAAI;AAAA,UAC/C;AACA,iBAAO;AAAA,QACX,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,SAAS,mBAAmB,MAAM;AAC9B,UAAQ,MAAM;AAAA,IACV,KAAa,iBAAiB;AAC1B,aAAO,UAAU,iBAAiB;AAAA,IACtC,KAAa,iBAAiB;AAC1B,aAAO,UAAU,iBAAiB;AAAA,IACtC,KAAa,iBAAiB;AAC1B,aAAO,UAAU,iBAAiB;AAAA,EAC1C;AACA,SAAO;AACX;AACA,IAAI;AAAA;AAAA,EAAuC,WAAY;AACnD,aAASC,uBAAsB,SAAS;AACpC,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,uBAAsB,UAAU,yBAAyB,SAAU,OAAO,WAAW,OAAO;AACxF,UAAI,WAAW,MAAM;AACrB,aAAO,KAAK,QAAQ,QAAQ,EACvB,KAAK,SAAU,QAAQ;AAAE,eAAO,OAAO,mBAAmB,SAAS,SAAS,GAAG,UAAU,IAAI,YAAY,CAAC;AAAA,MAAG,CAAC,EAC9G,KAAK,SAAU,iBAAiB;AACjC,YAAI,CAAC,iBAAiB;AAClB;AAAA,QACJ;AACA,eAAO,gBAAgB,IAAI,SAAU,gBAAgB;AACjD,cAAI,SAAS,CAAC;AACd,iBAAO,gBAAgB;AACnB,mBAAO,KAAK,EAAE,OAAO,QAAQ,eAAe,KAAK,EAAE,CAAC;AACpD,6BAAiB,eAAe;AAAA,UACpC;AACA,iBAAO;AAAA,QACX,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;;;ACrkBK,SAAS,cAAc,MAAM,cAAc;AAC9C,MAAI,iBAAiB,QAAQ;AAAE,mBAAe;AAAA,EAAO;AACrD,MAAI,MAAM,KAAK;AACf,MAAI,MAAM,GAAG,QAAQ,IAAI,cAAc,GAAG,QAAQ,IAAkB,aAAa,GAAG,kBAAkB,GAAG,uBAAuB,GAAG,2BAA2B,GAAG,YAAY;AAC7K,WAAS,cAAc,OAAO,OAAO;AACjC,QAAI,SAAS;AACb,QAAIC,SAAQ;AACZ,WAAO,SAAS,SAAS,CAAC,OAAO;AAC7B,UAAI,KAAK,KAAK,WAAW,GAAG;AAC5B,UAAI,MAAM,MAAe,MAAM,IAAa;AACxC,QAAAA,SAAQA,SAAQ,KAAK,KAAK;AAAA,MAC9B,WACS,MAAM,MAAc,MAAM,IAAY;AAC3C,QAAAA,SAAQA,SAAQ,KAAK,KAAK,KAAa;AAAA,MAC3C,WACS,MAAM,MAAc,MAAM,KAAa;AAC5C,QAAAA,SAAQA,SAAQ,KAAK,KAAK,KAAa;AAAA,MAC3C,OACK;AACD;AAAA,MACJ;AACA;AACA;AAAA,IACJ;AACA,QAAI,SAAS,OAAO;AAChB,MAAAA,SAAQ;AAAA,IACZ;AACA,WAAOA;AAAA,EACX;AACA,WAAS,YAAY,aAAa;AAC9B,UAAM;AACN,YAAQ;AACR,kBAAc;AACd,YAAQ;AACR,gBAAY;AAAA,EAChB;AACA,WAAS,aAAa;AAClB,QAAI,QAAQ;AACZ,QAAI,KAAK,WAAW,GAAG,MAAM,IAAa;AACtC;AAAA,IACJ,OACK;AACD;AACA,aAAO,MAAM,KAAK,UAAU,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AACvD;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,MAAM,KAAK,UAAU,KAAK,WAAW,GAAG,MAAM,IAAc;AAC5D;AACA,UAAI,MAAM,KAAK,UAAU,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AACpD;AACA,eAAO,MAAM,KAAK,UAAU,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AACvD;AAAA,QACJ;AAAA,MACJ,OACK;AACD,oBAAY;AACZ,eAAO,KAAK,UAAU,OAAO,GAAG;AAAA,MACpC;AAAA,IACJ;AACA,QAAI,MAAM;AACV,QAAI,MAAM,KAAK,WAAW,KAAK,WAAW,GAAG,MAAM,MAAc,KAAK,WAAW,GAAG,MAAM,MAAc;AACpG;AACA,UAAI,MAAM,KAAK,UAAU,KAAK,WAAW,GAAG,MAAM,MAAiB,KAAK,WAAW,GAAG,MAAM,IAAgB;AACxG;AAAA,MACJ;AACA,UAAI,MAAM,KAAK,UAAU,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AACpD;AACA,eAAO,MAAM,KAAK,UAAU,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AACvD;AAAA,QACJ;AACA,cAAM;AAAA,MACV,OACK;AACD,oBAAY;AAAA,MAChB;AAAA,IACJ;AACA,WAAO,KAAK,UAAU,OAAO,GAAG;AAAA,EACpC;AACA,WAAS,aAAa;AAClB,QAAI,SAAS,IAAI,QAAQ;AACzB,WAAO,MAAM;AACT,UAAI,OAAO,KAAK;AACZ,kBAAU,KAAK,UAAU,OAAO,GAAG;AACnC,oBAAY;AACZ;AAAA,MACJ;AACA,UAAI,KAAK,KAAK,WAAW,GAAG;AAC5B,UAAI,OAAO,IAAsB;AAC7B,kBAAU,KAAK,UAAU,OAAO,GAAG;AACnC;AACA;AAAA,MACJ;AACA,UAAI,OAAO,IAAoB;AAC3B,kBAAU,KAAK,UAAU,OAAO,GAAG;AACnC;AACA,YAAI,OAAO,KAAK;AACZ,sBAAY;AACZ;AAAA,QACJ;AACA,YAAI,MAAM,KAAK,WAAW,KAAK;AAC/B,gBAAQ,KAAK;AAAA,UACT,KAAK;AACD,sBAAU;AACV;AAAA,UACJ,KAAK;AACD,sBAAU;AACV;AAAA,UACJ,KAAK;AACD,sBAAU;AACV;AAAA,UACJ,KAAK;AACD,sBAAU;AACV;AAAA,UACJ,KAAK;AACD,sBAAU;AACV;AAAA,UACJ,KAAK;AACD,sBAAU;AACV;AAAA,UACJ,KAAK;AACD,sBAAU;AACV;AAAA,UACJ,KAAK;AACD,sBAAU;AACV;AAAA,UACJ,KAAK;AACD,gBAAI,MAAM,cAAc,GAAG,IAAI;AAC/B,gBAAI,OAAO,GAAG;AACV,wBAAU,OAAO,aAAa,GAAG;AAAA,YACrC,OACK;AACD,0BAAY;AAAA,YAChB;AACA;AAAA,UACJ;AACI,wBAAY;AAAA,QACpB;AACA,gBAAQ;AACR;AAAA,MACJ;AACA,UAAI,MAAM,KAAK,MAAM,IAAM;AACvB,YAAI,YAAY,EAAE,GAAG;AACjB,oBAAU,KAAK,UAAU,OAAO,GAAG;AACnC,sBAAY;AACZ;AAAA,QACJ,OACK;AACD,sBAAY;AAAA,QAEhB;AAAA,MACJ;AACA;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,WAAS,WAAW;AAChB,YAAQ;AACR,gBAAY;AACZ,kBAAc;AACd,sBAAkB;AAClB,+BAA2B;AAC3B,QAAI,OAAO,KAAK;AAEZ,oBAAc;AACd,aAAO,QAAQ;AAAA,IACnB;AACA,QAAI,OAAO,KAAK,WAAW,GAAG;AAE9B,QAAI,aAAa,IAAI,GAAG;AACpB,SAAG;AACC;AACA,iBAAS,OAAO,aAAa,IAAI;AACjC,eAAO,KAAK,WAAW,GAAG;AAAA,MAC9B,SAAS,aAAa,IAAI;AAC1B,aAAO,QAAQ;AAAA,IACnB;AAEA,QAAI,YAAY,IAAI,GAAG;AACnB;AACA,eAAS,OAAO,aAAa,IAAI;AACjC,UAAI,SAAS,MAA2B,KAAK,WAAW,GAAG,MAAM,IAAmB;AAChF;AACA,iBAAS;AAAA,MACb;AACA;AACA,6BAAuB;AACvB,aAAO,QAAQ;AAAA,IACnB;AACA,YAAQ,MAAM;AAAA,MAEV,KAAK;AACD;AACA,eAAO,QAAQ;AAAA,MACnB,KAAK;AACD;AACA,eAAO,QAAQ;AAAA,MACnB,KAAK;AACD;AACA,eAAO,QAAQ;AAAA,MACnB,KAAK;AACD;AACA,eAAO,QAAQ;AAAA,MACnB,KAAK;AACD;AACA,eAAO,QAAQ;AAAA,MACnB,KAAK;AACD;AACA,eAAO,QAAQ;AAAA,MAEnB,KAAK;AACD;AACA,gBAAQ,WAAW;AACnB,eAAO,QAAQ;AAAA,MAEnB,KAAK;AACD,YAAI,QAAQ,MAAM;AAElB,YAAI,KAAK,WAAW,MAAM,CAAC,MAAM,IAAgB;AAC7C,iBAAO;AACP,iBAAO,MAAM,KAAK;AACd,gBAAI,YAAY,KAAK,WAAW,GAAG,CAAC,GAAG;AACnC;AAAA,YACJ;AACA;AAAA,UACJ;AACA,kBAAQ,KAAK,UAAU,OAAO,GAAG;AACjC,iBAAO,QAAQ;AAAA,QACnB;AAEA,YAAI,KAAK,WAAW,MAAM,CAAC,MAAM,IAAmB;AAChD,iBAAO;AACP,cAAI,aAAa,MAAM;AACvB,cAAI,gBAAgB;AACpB,iBAAO,MAAM,YAAY;AACrB,gBAAI,KAAK,KAAK,WAAW,GAAG;AAC5B,gBAAI,OAAO,MAAqB,KAAK,WAAW,MAAM,CAAC,MAAM,IAAgB;AACzE,qBAAO;AACP,8BAAgB;AAChB;AAAA,YACJ;AACA;AACA,gBAAI,YAAY,EAAE,GAAG;AACjB,kBAAI,OAAO,MAA2B,KAAK,WAAW,GAAG,MAAM,IAAmB;AAC9E;AAAA,cACJ;AACA;AACA,qCAAuB;AAAA,YAC3B;AAAA,UACJ;AACA,cAAI,CAAC,eAAe;AAChB;AACA,wBAAY;AAAA,UAChB;AACA,kBAAQ,KAAK,UAAU,OAAO,GAAG;AACjC,iBAAO,QAAQ;AAAA,QACnB;AAEA,iBAAS,OAAO,aAAa,IAAI;AACjC;AACA,eAAO,QAAQ;AAAA,MAEnB,KAAK;AACD,iBAAS,OAAO,aAAa,IAAI;AACjC;AACA,YAAI,QAAQ,OAAO,CAAC,QAAQ,KAAK,WAAW,GAAG,CAAC,GAAG;AAC/C,iBAAO,QAAQ;AAAA,QACnB;AAAA,MAIJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,iBAAS,WAAW;AACpB,eAAO,QAAQ;AAAA,MAEnB;AAEI,eAAO,MAAM,OAAO,0BAA0B,IAAI,GAAG;AACjD;AACA,iBAAO,KAAK,WAAW,GAAG;AAAA,QAC9B;AACA,YAAI,gBAAgB,KAAK;AACrB,kBAAQ,KAAK,UAAU,aAAa,GAAG;AAEvC,kBAAQ,OAAO;AAAA,YACX,KAAK;AAAQ,qBAAO,QAAQ;AAAA,YAC5B,KAAK;AAAS,qBAAO,QAAQ;AAAA,YAC7B,KAAK;AAAQ,qBAAO,QAAQ;AAAA,UAChC;AACA,iBAAO,QAAQ;AAAA,QACnB;AAEA,iBAAS,OAAO,aAAa,IAAI;AACjC;AACA,eAAO,QAAQ;AAAA,IACvB;AAAA,EACJ;AACA,WAAS,0BAA0B,MAAM;AACrC,QAAI,aAAa,IAAI,KAAK,YAAY,IAAI,GAAG;AACzC,aAAO;AAAA,IACX;AACA,YAAQ,MAAM;AAAA,MACV,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,eAAO;AAAA,IACf;AACA,WAAO;AAAA,EACX;AACA,WAAS,oBAAoB;AACzB,QAAI;AACJ,OAAG;AACC,eAAS,SAAS;AAAA,IACtB,SAAS,UAAU,MAA8B,UAAU;AAC3D,WAAO;AAAA,EACX;AACA,SAAO;AAAA,IACH;AAAA,IACA,aAAa,WAAY;AAAE,aAAO;AAAA,IAAK;AAAA,IACvC,MAAM,eAAe,oBAAoB;AAAA,IACzC,UAAU,WAAY;AAAE,aAAO;AAAA,IAAO;AAAA,IACtC,eAAe,WAAY;AAAE,aAAO;AAAA,IAAO;AAAA,IAC3C,gBAAgB,WAAY;AAAE,aAAO;AAAA,IAAa;AAAA,IAClD,gBAAgB,WAAY;AAAE,aAAO,MAAM;AAAA,IAAa;AAAA,IACxD,mBAAmB,WAAY;AAAE,aAAO;AAAA,IAAiB;AAAA,IACzD,wBAAwB,WAAY;AAAE,aAAO,cAAc;AAAA,IAA0B;AAAA,IACrF,eAAe,WAAY;AAAE,aAAO;AAAA,IAAW;AAAA,EACnD;AACJ;AACA,SAAS,aAAa,IAAI;AACtB,SAAO,OAAO,MAAkB,OAAO,KAAe,OAAO,MAAwB,OAAO,MACxF,OAAO,OAA8B,OAAO,QAAoB,MAAM,QAAqB,MAAM,QACjG,OAAO,QAAiC,OAAO,QAAgC,OAAO,SAAgC,OAAO;AACrI;AACA,SAAS,YAAY,IAAI;AACrB,SAAO,OAAO,MAAqB,OAAO,MAA2B,OAAO,QAA4B,OAAO;AACnH;AACA,SAAS,QAAQ,IAAI;AACjB,SAAO,MAAM,MAAe,MAAM;AACtC;;;ACpWA,IAAI;AAAA,CACH,SAAUC,eAAc;AACrB,EAAAA,cAAa,UAAU;AAAA,IACnB,oBAAoB;AAAA,EACxB;AACJ,GAAG,iBAAiB,eAAe,CAAC,EAAE;;;ACE/B,IAAIC,iBAAwB;;;ACR5B,SAAS,0BAA0B,iBAAiB;AACvD,SAAO;AAAA,IACH,iBAAiB,WAAY;AAAE,aAAO,IAAI,UAAU,MAAM,MAAM,OAAO,IAAI;AAAA,IAAG;AAAA,IAC9E,UAAU,SAAU,MAAM,OAAO,aAAa,cAAc;AACxD,aAAO,SAAS,iBAAiB,MAAM,OAAO,aAAa,YAAY;AAAA,IAC3E;AAAA,EACJ;AACJ;AACO,IAAI,qBAAqB;AACzB,IAAI,oBAAoB;AACxB,IAAI,oBAAoB;AACxB,IAAI,oBAAoB;AACxB,IAAI,sBAAsB;AAC1B,IAAI,mBAAmB;AACvB,IAAI,qBAAqB;AACzB,IAAI,qBAAqB;AACzB,IAAI,sBAAsB;AAC1B,IAAI,sBAAsB;AAC1B,IAAI,qBAAqB;AAChC,IAAI;AAAA;AAAA,EAA8B,WAAY;AAC1C,aAASC,cAAa,QAAQ,MAAM;AAChC,WAAK,SAAS;AACd,WAAK,OAAO;AAAA,IAChB;AACA,IAAAA,cAAa,MAAM,SAAU,SAAS;AAClC,UAAI,SAAS;AACT,eAAO,QAAQ;AAAA,MACnB;AACA,aAAO;AAAA,IACX;AACA,IAAAA,cAAa,OAAO,SAAU,SAAS,MAAM;AACzC,aAAO,IAAIA,cAAa,SAAS,IAAI;AAAA,IACzC;AACA,IAAAA,cAAa,SAAS,SAAU,GAAG,GAAG;AAClC,UAAI,CAAC,KAAK,CAAC,GAAG;AACV,eAAO;AAAA,MACX;AACA,UAAI,CAAC,KAAK,CAAC,GAAG;AACV,eAAO;AAAA,MACX;AACA,aAAO,KAAK,GAAG;AACX,YAAI,MAAM,GAAG;AACT,iBAAO;AAAA,QACX;AACA,YAAI,EAAE,SAAS,EAAE,MAAM;AACnB,iBAAO;AAAA,QACX;AACA,YAAI,EAAE;AACN,YAAI,EAAE;AAAA,MACV;AACA,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,IAAI;AAAA;AAAA,EAA2B,WAAY;AACvC,aAASC,WAAU,OAAO,WAAW,cAAc,SAAS;AACxD,WAAK,SAAS;AACd,WAAK,YAAY;AACjB,WAAK,eAAe;AACpB,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,WAAU,UAAU,QAAQ,WAAY;AACpC,aAAO,IAAIA,WAAU,KAAK,QAAQ,KAAK,WAAW,KAAK,cAAc,KAAK,OAAO;AAAA,IACrF;AACA,IAAAA,WAAU,UAAU,SAAS,SAAU,OAAO;AAC1C,UAAI,UAAU,MAAM;AAChB,eAAO;AAAA,MACX;AACA,UAAI,CAAC,SAAS,EAAE,iBAAiBA,aAAY;AACzC,eAAO;AAAA,MACX;AACA,aAAQ,KAAK,cAAc,MAAM,aAC7B,KAAK,iBAAiB,MAAM,gBAC5B,aAAa,OAAO,KAAK,SAAS,MAAM,OAAO;AAAA,IACvD;AACA,IAAAA,WAAU,UAAU,eAAe,WAAY;AAC3C,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,WAAU,UAAU,eAAe,SAAU,OAAO;AAChD,WAAK,SAAS;AAAA,IAClB;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,SAAS,SAAS,UAAU,MAAM,OAAO,aAAa,cAAc;AAChE,MAAI,gBAAgB,QAAQ;AAAE,kBAAc;AAAA,EAAG;AAE/C,MAAI,6BAA6B;AACjC,MAAI,eAAe;AACnB,UAAQ,MAAM,WAAW;AAAA,IACrB,KAAK;AACD,aAAO,MAAM;AACb,mCAA6B;AAC7B;AAAA,IACJ,KAAK;AACD,aAAO,OAAO;AACd,mCAA6B;AAC7B;AAAA,EACR;AACA,MAAI,UAAeC,eAAc,IAAI;AACrC,MAAI,eAAe,MAAM;AACzB,MAAI,UAAU,MAAM;AACpB,MAAI,MAAM;AAAA,IACN,QAAQ,CAAC;AAAA,IACT,UAAU,MAAM,MAAM;AAAA,EAC1B;AACA,SAAO,MAAM;AACT,QAAI,SAAS,cAAc,QAAQ,YAAY;AAC/C,QAAI,OAAO;AACX,QAAI,OAAO,QAAQ,KAAK;AACxB,QAAI,SAAS,IAAc;AACvB;AAAA,IACJ;AAEA,QAAI,WAAW,cAAc,QAAQ,YAAY,GAAG;AAChD,YAAM,IAAI,MAAM,qDAAqD,KAAK,OAAO,QAAQ,YAAY,GAAG,CAAC,CAAC;AAAA,IAC9G;AAGA,QAAI,cAAc;AACd,gBAAU;AAAA,IACd;AACA,mBAAe,6BAA6B;AAE5C,YAAQ,MAAM;AAAA,MACV,KAAK;AACD,kBAAU,aAAa;AAAA,UAAK;AAAA,UAAS;AAAA;AAAA,QAAc;AACnD,eAAO;AACP,uBAAe;AACf;AAAA,MACJ,KAAK;AACD,kBAAU,aAAa,IAAI,OAAO;AAClC,eAAO;AACP,uBAAe;AACf;AAAA,MACJ,KAAK;AACD,kBAAU,aAAa;AAAA,UAAK;AAAA,UAAS;AAAA;AAAA,QAAa;AAClD,eAAO;AACP,uBAAe;AACf;AAAA,MACJ,KAAK;AACD,kBAAU,aAAa,IAAI,OAAO;AAClC,eAAO;AACP,uBAAe;AACf;AAAA,MACJ,KAAK;AACD,eAAO;AACP,uBAAe;AACf;AAAA,MACJ,KAAK;AACD,eAAO;AACP,uBAAe;AACf;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,eAAO;AACP,uBAAe;AACf;AAAA,MACJ,KAAK;AACD,eAAO;AACP,uBAAe;AACf;AAAA,MACJ,KAAK;AACD,YAAI,gBAAgB,UAAU,QAAQ,OAAO;AAC7C,YAAI,UAAU,kBAAkB;AAChC,eAAO,gBAAgB,UAAU,qBAAqB;AACtD,uBAAe;AACf;AAAA,MACJ,KAAK;AACD,eAAO;AACP,uBAAe;AACf;AAAA,IACR;AAEA,QAAI,UAAU;AACV,cAAQ,MAAM;AAAA,QACV,KAAK;AACD,iBAAO;AACP;AAAA,QACJ,KAAK;AACD,iBAAO;AACP;AAAA,MACR;AAAA,IACJ;AACA,QAAI,WAAW,IAAI,UAAU,MAAM,aAAa,GAAG,QAAQ,cAAc,GAAG,cAAc,OAAO;AACjG,QAAI,OAAO,KAAK;AAAA,MACZ,YAAY;AAAA,MACZ,QAAQ;AAAA,IACZ,CAAC;AAAA,EACL;AACA,SAAO;AACX;;;AC3LO,SAAS,UAAU,UAAU;AAChC,MAAI,cAAc,CAAC;AACnB,MAAI,YAAY,CAAC;AACjB,MAAI,SAAS,IAAI,cAAc,QAAQ;AACvC,cAAY,KAAK,MAAM;AACvB,MAAI,SAAS,WAAY;AACrB,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IAC3B;AACA,WAAO,OAAO,yBAAyB,MAAM,QAAQ,IAAI;AAAA,EAC7D;AACA,WAAS,oBAAoB;AACzB,QAAI,aAAa,SAAS,YAAYC,qBAAoB,SAAS;AACnE,eAAW,SAAS;AACpB,QAAIA,mBAAkB,yBAAyB;AAC3C,gBAAU,KAAK,UAAU,uCAAuC,YAAY,IAAqB,+BAA+B,MAAM,CAAC,CAAC;AAAA,IAC5I;AACA,QAAIA,mBAAkB,8BAA8B;AAChD,gBAAU,KAAK,UAAU,4CAA4C,YAAY,IAAqB,oCAAoC,MAAM,CAAC,CAAC;AAAA,IACtJ;AACA,QAAIA,mBAAkB,iBAAiB;AACnC,gBAAU,KAAK,UAAU,+BAA+B,YAAY,IAAqB,kBAAkB,MAAM,CAAC,CAAC;AAAA,IACvH;AACA,QAAIA,mBAAkB,QAAQ;AAC1B,gBAAU,KAAK,UAAU,sBAAsB,YAAY,IAAqB,aAAa,MAAM,CAAC,CAAC;AAAA,IACzG;AACA,QAAIA,mBAAkB,iBAAiB;AACnC,gBAAU,KAAK,UAAU,+BAA+B,YAAY,IAAqB,sBAAsB,MAAM,CAAC,CAAC;AAAA,IAC3H;AACA,QAAIA,mBAAkB,QAAQ;AAC1B,gBAAU,KAAK,UAAU,kBAAkB,YAAY,0BAA0B,IAAI,CAAC,CAAC;AAAA,IAC3F;AACA,QAAIA,mBAAkB,QAAQ;AAC1B,gBAAU,KAAK,UAAU,sBAAsB,YAAY,IAAqB,qBAAqB,MAAM,CAAC,CAAC;AAAA,IACjH;AACA,QAAIA,mBAAkB,eAAe;AACjC,gBAAU,KAAK,UAAU,6BAA6B,YAAY,IAAqB,oBAAoB,MAAM,CAAC,CAAC;AAAA,IACvH;AACA,QAAIA,mBAAkB,aAAa;AAC/B,gBAAU,KAAK,IAAqB,mBAAmB,YAAY,QAAQ,QAAQ,CAAC;AAAA,IACxF;AACA,QAAIA,mBAAkB,iBAAiB;AACnC,gBAAU,KAAK,UAAU,+BAA+B,YAAY,IAAqB,sBAAsB,MAAM,CAAC,CAAC;AAAA,IAC3H;AAAA,EACJ;AACA,oBAAkB;AAClB,cAAY,KAAK,UAAU,yBAAyB,SAAS,YAAY,qBAAqB,CAAC;AAC/F,MAAI,oBAAoB,SAAS;AACjC,WAAS,YAAY,SAAU,aAAa;AACxC,QAAI,YAAY,sBAAsB,mBAAmB;AACrD,0BAAoB,YAAY;AAChC,wBAAkB;AAAA,IACtB;AAAA,EACJ,CAAC;AACD,cAAY,KAAK,aAAa,SAAS,CAAC;AACxC,SAAO,aAAa,WAAW;AACnC;AACA,SAAS,aAAa,aAAa;AAC/B,SAAO,EAAE,SAAS,WAAY;AAAE,WAAO,WAAW,WAAW;AAAA,EAAG,EAAE;AACtE;AACA,SAAS,WAAW,aAAa;AAC7B,SAAO,YAAY,QAAQ;AACvB,gBAAY,IAAI,EAAE,QAAQ;AAAA,EAC9B;AACJ;AACA,IAAI,wBAAwB;AAAA,EACxB,aAAa;AAAA,EACb,UAAU;AAAA,IACN,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC7B;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,EAC/C;AACJ;", "names": ["WorkerManager", "integer", "<PERSON><PERSON><PERSON><PERSON>", "Position", "Range", "Location", "LocationLink", "Color", "ColorInformation", "ColorPresentation", "FoldingRangeKind", "FoldingRange", "DiagnosticRelatedInformation", "DiagnosticSeverity", "DiagnosticTag", "CodeDescription", "Diagnostic", "Command", "TextEdit", "ChangeAnnotation", "ChangeAnnotationIdentifier", "AnnotatedTextEdit", "TextDocumentEdit", "CreateFile", "RenameFile", "DeleteFile", "WorkspaceEdit", "TextEditChangeImpl", "ChangeAnnotations", "WorkspaceChange", "TextDocumentIdentifier", "VersionedTextDocumentIdentifier", "OptionalVersionedTextDocumentIdentifier", "TextDocumentItem", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CompletionItemKind", "InsertTextFormat", "CompletionItemTag", "InsertReplaceEdit", "Range", "InsertTextMode", "CompletionItem", "CompletionList", "MarkedString", "Hover", "ParameterInformation", "SignatureInformation", "DocumentHighlightKind", "DocumentHighlight", "SymbolKind", "SymbolTag", "SymbolInformation", "DocumentSymbol", "CodeActionKind", "CodeActionContext", "CodeAction", "CodeLens", "FormattingOptions", "DocumentLink", "SelectionRange", "TextDocument", "FullTextDocument", "Is", "undefined", "integer", "<PERSON><PERSON><PERSON><PERSON>", "DiagnosticsAdapter", "CompletionAdapter", "HoverAdapter", "DocumentSymbolAdapter", "DocumentFormattingEditProvider", "DocumentRangeFormattingEditProvider", "DocumentColorAdapter", "FoldingRangeAdapter", "SelectionRangeAdapter", "value", "ParseOptions", "createScanner", "ParentsStack", "JSONState", "createScanner", "modeConfiguration"]}