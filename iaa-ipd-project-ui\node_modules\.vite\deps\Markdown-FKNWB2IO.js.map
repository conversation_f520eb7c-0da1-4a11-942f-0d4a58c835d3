{"version": 3, "sources": ["../../amis-ui/node_modules/entities/lib/maps/entities.json", "../../amis-ui/node_modules/markdown-it/lib/common/entities.js", "../../amis-ui/node_modules/uc.micro/categories/P/regex.js", "../../amis-ui/node_modules/mdurl/encode.js", "../../amis-ui/node_modules/mdurl/decode.js", "../../amis-ui/node_modules/mdurl/format.js", "../../amis-ui/node_modules/mdurl/parse.js", "../../amis-ui/node_modules/mdurl/index.js", "../../amis-ui/node_modules/uc.micro/properties/Any/regex.js", "../../amis-ui/node_modules/uc.micro/categories/Cc/regex.js", "../../amis-ui/node_modules/uc.micro/categories/Cf/regex.js", "../../amis-ui/node_modules/uc.micro/categories/Z/regex.js", "../../amis-ui/node_modules/uc.micro/index.js", "../../amis-ui/node_modules/markdown-it/lib/common/utils.js", "../../amis-ui/node_modules/markdown-it/lib/helpers/parse_link_label.js", "../../amis-ui/node_modules/markdown-it/lib/helpers/parse_link_destination.js", "../../amis-ui/node_modules/markdown-it/lib/helpers/parse_link_title.js", "../../amis-ui/node_modules/markdown-it/lib/helpers/index.js", "../../amis-ui/node_modules/markdown-it/lib/renderer.js", "../../amis-ui/node_modules/markdown-it/lib/ruler.js", "../../amis-ui/node_modules/markdown-it/lib/rules_core/normalize.js", "../../amis-ui/node_modules/markdown-it/lib/rules_core/block.js", "../../amis-ui/node_modules/markdown-it/lib/rules_core/inline.js", "../../amis-ui/node_modules/markdown-it/lib/rules_core/linkify.js", "../../amis-ui/node_modules/markdown-it/lib/rules_core/replacements.js", "../../amis-ui/node_modules/markdown-it/lib/rules_core/smartquotes.js", "../../amis-ui/node_modules/markdown-it/lib/token.js", "../../amis-ui/node_modules/markdown-it/lib/rules_core/state_core.js", "../../amis-ui/node_modules/markdown-it/lib/parser_core.js", "../../amis-ui/node_modules/markdown-it/lib/rules_block/table.js", "../../amis-ui/node_modules/markdown-it/lib/rules_block/code.js", "../../amis-ui/node_modules/markdown-it/lib/rules_block/fence.js", "../../amis-ui/node_modules/markdown-it/lib/rules_block/blockquote.js", "../../amis-ui/node_modules/markdown-it/lib/rules_block/hr.js", "../../amis-ui/node_modules/markdown-it/lib/rules_block/list.js", "../../amis-ui/node_modules/markdown-it/lib/rules_block/reference.js", "../../amis-ui/node_modules/markdown-it/lib/common/html_blocks.js", "../../amis-ui/node_modules/markdown-it/lib/common/html_re.js", "../../amis-ui/node_modules/markdown-it/lib/rules_block/html_block.js", "../../amis-ui/node_modules/markdown-it/lib/rules_block/heading.js", "../../amis-ui/node_modules/markdown-it/lib/rules_block/lheading.js", "../../amis-ui/node_modules/markdown-it/lib/rules_block/paragraph.js", "../../amis-ui/node_modules/markdown-it/lib/rules_block/state_block.js", "../../amis-ui/node_modules/markdown-it/lib/parser_block.js", "../../amis-ui/node_modules/markdown-it/lib/rules_inline/text.js", "../../amis-ui/node_modules/markdown-it/lib/rules_inline/newline.js", "../../amis-ui/node_modules/markdown-it/lib/rules_inline/escape.js", "../../amis-ui/node_modules/markdown-it/lib/rules_inline/backticks.js", "../../amis-ui/node_modules/markdown-it/lib/rules_inline/strikethrough.js", "../../amis-ui/node_modules/markdown-it/lib/rules_inline/emphasis.js", "../../amis-ui/node_modules/markdown-it/lib/rules_inline/link.js", "../../amis-ui/node_modules/markdown-it/lib/rules_inline/image.js", "../../amis-ui/node_modules/markdown-it/lib/rules_inline/autolink.js", "../../amis-ui/node_modules/markdown-it/lib/rules_inline/html_inline.js", "../../amis-ui/node_modules/markdown-it/lib/rules_inline/entity.js", "../../amis-ui/node_modules/markdown-it/lib/rules_inline/balance_pairs.js", "../../amis-ui/node_modules/markdown-it/lib/rules_inline/text_collapse.js", "../../amis-ui/node_modules/markdown-it/lib/rules_inline/state_inline.js", "../../amis-ui/node_modules/markdown-it/lib/parser_inline.js", "../../amis-ui/node_modules/linkify-it/lib/re.js", "../../amis-ui/node_modules/linkify-it/index.js", "../../amis-ui/node_modules/markdown-it/lib/presets/default.js", "../../amis-ui/node_modules/markdown-it/lib/presets/zero.js", "../../amis-ui/node_modules/markdown-it/lib/presets/commonmark.js", "../../amis-ui/node_modules/markdown-it/lib/index.js", "../../amis-ui/node_modules/markdown-it/index.js", "../../markdown-it-html5-media/lib/index.js", "../../amis-ui/esm/components/Markdown.js"], "sourcesContent": ["{\"Aacute\":\"Á\",\"aacute\":\"á\",\"Abreve\":\"Ă\",\"abreve\":\"ă\",\"ac\":\"∾\",\"acd\":\"∿\",\"acE\":\"∾̳\",\"Acirc\":\"Â\",\"acirc\":\"â\",\"acute\":\"´\",\"Acy\":\"А\",\"acy\":\"а\",\"AElig\":\"Æ\",\"aelig\":\"æ\",\"af\":\"⁡\",\"Afr\":\"𝔄\",\"afr\":\"𝔞\",\"Agrave\":\"À\",\"agrave\":\"à\",\"alefsym\":\"ℵ\",\"aleph\":\"ℵ\",\"Alpha\":\"Α\",\"alpha\":\"α\",\"Amacr\":\"Ā\",\"amacr\":\"ā\",\"amalg\":\"⨿\",\"amp\":\"&\",\"AMP\":\"&\",\"andand\":\"⩕\",\"And\":\"⩓\",\"and\":\"∧\",\"andd\":\"⩜\",\"andslope\":\"⩘\",\"andv\":\"⩚\",\"ang\":\"∠\",\"ange\":\"⦤\",\"angle\":\"∠\",\"angmsdaa\":\"⦨\",\"angmsdab\":\"⦩\",\"angmsdac\":\"⦪\",\"angmsdad\":\"⦫\",\"angmsdae\":\"⦬\",\"angmsdaf\":\"⦭\",\"angmsdag\":\"⦮\",\"angmsdah\":\"⦯\",\"angmsd\":\"∡\",\"angrt\":\"∟\",\"angrtvb\":\"⊾\",\"angrtvbd\":\"⦝\",\"angsph\":\"∢\",\"angst\":\"Å\",\"angzarr\":\"⍼\",\"Aogon\":\"Ą\",\"aogon\":\"ą\",\"Aopf\":\"𝔸\",\"aopf\":\"𝕒\",\"apacir\":\"⩯\",\"ap\":\"≈\",\"apE\":\"⩰\",\"ape\":\"≊\",\"apid\":\"≋\",\"apos\":\"'\",\"ApplyFunction\":\"⁡\",\"approx\":\"≈\",\"approxeq\":\"≊\",\"Aring\":\"Å\",\"aring\":\"å\",\"Ascr\":\"𝒜\",\"ascr\":\"𝒶\",\"Assign\":\"≔\",\"ast\":\"*\",\"asymp\":\"≈\",\"asympeq\":\"≍\",\"Atilde\":\"Ã\",\"atilde\":\"ã\",\"Auml\":\"Ä\",\"auml\":\"ä\",\"awconint\":\"∳\",\"awint\":\"⨑\",\"backcong\":\"≌\",\"backepsilon\":\"϶\",\"backprime\":\"‵\",\"backsim\":\"∽\",\"backsimeq\":\"⋍\",\"Backslash\":\"∖\",\"Barv\":\"⫧\",\"barvee\":\"⊽\",\"barwed\":\"⌅\",\"Barwed\":\"⌆\",\"barwedge\":\"⌅\",\"bbrk\":\"⎵\",\"bbrktbrk\":\"⎶\",\"bcong\":\"≌\",\"Bcy\":\"Б\",\"bcy\":\"б\",\"bdquo\":\"„\",\"becaus\":\"∵\",\"because\":\"∵\",\"Because\":\"∵\",\"bemptyv\":\"⦰\",\"bepsi\":\"϶\",\"bernou\":\"ℬ\",\"Bernoullis\":\"ℬ\",\"Beta\":\"Β\",\"beta\":\"β\",\"beth\":\"ℶ\",\"between\":\"≬\",\"Bfr\":\"𝔅\",\"bfr\":\"𝔟\",\"bigcap\":\"⋂\",\"bigcirc\":\"◯\",\"bigcup\":\"⋃\",\"bigodot\":\"⨀\",\"bigoplus\":\"⨁\",\"bigotimes\":\"⨂\",\"bigsqcup\":\"⨆\",\"bigstar\":\"★\",\"bigtriangledown\":\"▽\",\"bigtriangleup\":\"△\",\"biguplus\":\"⨄\",\"bigvee\":\"⋁\",\"bigwedge\":\"⋀\",\"bkarow\":\"⤍\",\"blacklozenge\":\"⧫\",\"blacksquare\":\"▪\",\"blacktriangle\":\"▴\",\"blacktriangledown\":\"▾\",\"blacktriangleleft\":\"◂\",\"blacktriangleright\":\"▸\",\"blank\":\"␣\",\"blk12\":\"▒\",\"blk14\":\"░\",\"blk34\":\"▓\",\"block\":\"█\",\"bne\":\"=⃥\",\"bnequiv\":\"≡⃥\",\"bNot\":\"⫭\",\"bnot\":\"⌐\",\"Bopf\":\"𝔹\",\"bopf\":\"𝕓\",\"bot\":\"⊥\",\"bottom\":\"⊥\",\"bowtie\":\"⋈\",\"boxbox\":\"⧉\",\"boxdl\":\"┐\",\"boxdL\":\"╕\",\"boxDl\":\"╖\",\"boxDL\":\"╗\",\"boxdr\":\"┌\",\"boxdR\":\"╒\",\"boxDr\":\"╓\",\"boxDR\":\"╔\",\"boxh\":\"─\",\"boxH\":\"═\",\"boxhd\":\"┬\",\"boxHd\":\"╤\",\"boxhD\":\"╥\",\"boxHD\":\"╦\",\"boxhu\":\"┴\",\"boxHu\":\"╧\",\"boxhU\":\"╨\",\"boxHU\":\"╩\",\"boxminus\":\"⊟\",\"boxplus\":\"⊞\",\"boxtimes\":\"⊠\",\"boxul\":\"┘\",\"boxuL\":\"╛\",\"boxUl\":\"╜\",\"boxUL\":\"╝\",\"boxur\":\"└\",\"boxuR\":\"╘\",\"boxUr\":\"╙\",\"boxUR\":\"╚\",\"boxv\":\"│\",\"boxV\":\"║\",\"boxvh\":\"┼\",\"boxvH\":\"╪\",\"boxVh\":\"╫\",\"boxVH\":\"╬\",\"boxvl\":\"┤\",\"boxvL\":\"╡\",\"boxVl\":\"╢\",\"boxVL\":\"╣\",\"boxvr\":\"├\",\"boxvR\":\"╞\",\"boxVr\":\"╟\",\"boxVR\":\"╠\",\"bprime\":\"‵\",\"breve\":\"˘\",\"Breve\":\"˘\",\"brvbar\":\"¦\",\"bscr\":\"𝒷\",\"Bscr\":\"ℬ\",\"bsemi\":\"⁏\",\"bsim\":\"∽\",\"bsime\":\"⋍\",\"bsolb\":\"⧅\",\"bsol\":\"\\\\\",\"bsolhsub\":\"⟈\",\"bull\":\"•\",\"bullet\":\"•\",\"bump\":\"≎\",\"bumpE\":\"⪮\",\"bumpe\":\"≏\",\"Bumpeq\":\"≎\",\"bumpeq\":\"≏\",\"Cacute\":\"Ć\",\"cacute\":\"ć\",\"capand\":\"⩄\",\"capbrcup\":\"⩉\",\"capcap\":\"⩋\",\"cap\":\"∩\",\"Cap\":\"⋒\",\"capcup\":\"⩇\",\"capdot\":\"⩀\",\"CapitalDifferentialD\":\"ⅅ\",\"caps\":\"∩︀\",\"caret\":\"⁁\",\"caron\":\"ˇ\",\"Cayleys\":\"ℭ\",\"ccaps\":\"⩍\",\"Ccaron\":\"Č\",\"ccaron\":\"č\",\"Ccedil\":\"Ç\",\"ccedil\":\"ç\",\"Ccirc\":\"Ĉ\",\"ccirc\":\"ĉ\",\"Cconint\":\"∰\",\"ccups\":\"⩌\",\"ccupssm\":\"⩐\",\"Cdot\":\"Ċ\",\"cdot\":\"ċ\",\"cedil\":\"¸\",\"Cedilla\":\"¸\",\"cemptyv\":\"⦲\",\"cent\":\"¢\",\"centerdot\":\"·\",\"CenterDot\":\"·\",\"cfr\":\"𝔠\",\"Cfr\":\"ℭ\",\"CHcy\":\"Ч\",\"chcy\":\"ч\",\"check\":\"✓\",\"checkmark\":\"✓\",\"Chi\":\"Χ\",\"chi\":\"χ\",\"circ\":\"ˆ\",\"circeq\":\"≗\",\"circlearrowleft\":\"↺\",\"circlearrowright\":\"↻\",\"circledast\":\"⊛\",\"circledcirc\":\"⊚\",\"circleddash\":\"⊝\",\"CircleDot\":\"⊙\",\"circledR\":\"®\",\"circledS\":\"Ⓢ\",\"CircleMinus\":\"⊖\",\"CirclePlus\":\"⊕\",\"CircleTimes\":\"⊗\",\"cir\":\"○\",\"cirE\":\"⧃\",\"cire\":\"≗\",\"cirfnint\":\"⨐\",\"cirmid\":\"⫯\",\"cirscir\":\"⧂\",\"ClockwiseContourIntegral\":\"∲\",\"CloseCurlyDoubleQuote\":\"”\",\"CloseCurlyQuote\":\"’\",\"clubs\":\"♣\",\"clubsuit\":\"♣\",\"colon\":\":\",\"Colon\":\"∷\",\"Colone\":\"⩴\",\"colone\":\"≔\",\"coloneq\":\"≔\",\"comma\":\",\",\"commat\":\"@\",\"comp\":\"∁\",\"compfn\":\"∘\",\"complement\":\"∁\",\"complexes\":\"ℂ\",\"cong\":\"≅\",\"congdot\":\"⩭\",\"Congruent\":\"≡\",\"conint\":\"∮\",\"Conint\":\"∯\",\"ContourIntegral\":\"∮\",\"copf\":\"𝕔\",\"Copf\":\"ℂ\",\"coprod\":\"∐\",\"Coproduct\":\"∐\",\"copy\":\"©\",\"COPY\":\"©\",\"copysr\":\"℗\",\"CounterClockwiseContourIntegral\":\"∳\",\"crarr\":\"↵\",\"cross\":\"✗\",\"Cross\":\"⨯\",\"Cscr\":\"𝒞\",\"cscr\":\"𝒸\",\"csub\":\"⫏\",\"csube\":\"⫑\",\"csup\":\"⫐\",\"csupe\":\"⫒\",\"ctdot\":\"⋯\",\"cudarrl\":\"⤸\",\"cudarrr\":\"⤵\",\"cuepr\":\"⋞\",\"cuesc\":\"⋟\",\"cularr\":\"↶\",\"cularrp\":\"⤽\",\"cupbrcap\":\"⩈\",\"cupcap\":\"⩆\",\"CupCap\":\"≍\",\"cup\":\"∪\",\"Cup\":\"⋓\",\"cupcup\":\"⩊\",\"cupdot\":\"⊍\",\"cupor\":\"⩅\",\"cups\":\"∪︀\",\"curarr\":\"↷\",\"curarrm\":\"⤼\",\"curlyeqprec\":\"⋞\",\"curlyeqsucc\":\"⋟\",\"curlyvee\":\"⋎\",\"curlywedge\":\"⋏\",\"curren\":\"¤\",\"curvearrowleft\":\"↶\",\"curvearrowright\":\"↷\",\"cuvee\":\"⋎\",\"cuwed\":\"⋏\",\"cwconint\":\"∲\",\"cwint\":\"∱\",\"cylcty\":\"⌭\",\"dagger\":\"†\",\"Dagger\":\"‡\",\"daleth\":\"ℸ\",\"darr\":\"↓\",\"Darr\":\"↡\",\"dArr\":\"⇓\",\"dash\":\"‐\",\"Dashv\":\"⫤\",\"dashv\":\"⊣\",\"dbkarow\":\"⤏\",\"dblac\":\"˝\",\"Dcaron\":\"Ď\",\"dcaron\":\"ď\",\"Dcy\":\"Д\",\"dcy\":\"д\",\"ddagger\":\"‡\",\"ddarr\":\"⇊\",\"DD\":\"ⅅ\",\"dd\":\"ⅆ\",\"DDotrahd\":\"⤑\",\"ddotseq\":\"⩷\",\"deg\":\"°\",\"Del\":\"∇\",\"Delta\":\"Δ\",\"delta\":\"δ\",\"demptyv\":\"⦱\",\"dfisht\":\"⥿\",\"Dfr\":\"𝔇\",\"dfr\":\"𝔡\",\"dHar\":\"⥥\",\"dharl\":\"⇃\",\"dharr\":\"⇂\",\"DiacriticalAcute\":\"´\",\"DiacriticalDot\":\"˙\",\"DiacriticalDoubleAcute\":\"˝\",\"DiacriticalGrave\":\"`\",\"DiacriticalTilde\":\"˜\",\"diam\":\"⋄\",\"diamond\":\"⋄\",\"Diamond\":\"⋄\",\"diamondsuit\":\"♦\",\"diams\":\"♦\",\"die\":\"¨\",\"DifferentialD\":\"ⅆ\",\"digamma\":\"ϝ\",\"disin\":\"⋲\",\"div\":\"÷\",\"divide\":\"÷\",\"divideontimes\":\"⋇\",\"divonx\":\"⋇\",\"DJcy\":\"Ђ\",\"djcy\":\"ђ\",\"dlcorn\":\"⌞\",\"dlcrop\":\"⌍\",\"dollar\":\"$\",\"Dopf\":\"𝔻\",\"dopf\":\"𝕕\",\"Dot\":\"¨\",\"dot\":\"˙\",\"DotDot\":\"⃜\",\"doteq\":\"≐\",\"doteqdot\":\"≑\",\"DotEqual\":\"≐\",\"dotminus\":\"∸\",\"dotplus\":\"∔\",\"dotsquare\":\"⊡\",\"doublebarwedge\":\"⌆\",\"DoubleContourIntegral\":\"∯\",\"DoubleDot\":\"¨\",\"DoubleDownArrow\":\"⇓\",\"DoubleLeftArrow\":\"⇐\",\"DoubleLeftRightArrow\":\"⇔\",\"DoubleLeftTee\":\"⫤\",\"DoubleLongLeftArrow\":\"⟸\",\"DoubleLongLeftRightArrow\":\"⟺\",\"DoubleLongRightArrow\":\"⟹\",\"DoubleRightArrow\":\"⇒\",\"DoubleRightTee\":\"⊨\",\"DoubleUpArrow\":\"⇑\",\"DoubleUpDownArrow\":\"⇕\",\"DoubleVerticalBar\":\"∥\",\"DownArrowBar\":\"⤓\",\"downarrow\":\"↓\",\"DownArrow\":\"↓\",\"Downarrow\":\"⇓\",\"DownArrowUpArrow\":\"⇵\",\"DownBreve\":\"̑\",\"downdownarrows\":\"⇊\",\"downharpoonleft\":\"⇃\",\"downharpoonright\":\"⇂\",\"DownLeftRightVector\":\"⥐\",\"DownLeftTeeVector\":\"⥞\",\"DownLeftVectorBar\":\"⥖\",\"DownLeftVector\":\"↽\",\"DownRightTeeVector\":\"⥟\",\"DownRightVectorBar\":\"⥗\",\"DownRightVector\":\"⇁\",\"DownTeeArrow\":\"↧\",\"DownTee\":\"⊤\",\"drbkarow\":\"⤐\",\"drcorn\":\"⌟\",\"drcrop\":\"⌌\",\"Dscr\":\"𝒟\",\"dscr\":\"𝒹\",\"DScy\":\"Ѕ\",\"dscy\":\"ѕ\",\"dsol\":\"⧶\",\"Dstrok\":\"Đ\",\"dstrok\":\"đ\",\"dtdot\":\"⋱\",\"dtri\":\"▿\",\"dtrif\":\"▾\",\"duarr\":\"⇵\",\"duhar\":\"⥯\",\"dwangle\":\"⦦\",\"DZcy\":\"Џ\",\"dzcy\":\"џ\",\"dzigrarr\":\"⟿\",\"Eacute\":\"É\",\"eacute\":\"é\",\"easter\":\"⩮\",\"Ecaron\":\"Ě\",\"ecaron\":\"ě\",\"Ecirc\":\"Ê\",\"ecirc\":\"ê\",\"ecir\":\"≖\",\"ecolon\":\"≕\",\"Ecy\":\"Э\",\"ecy\":\"э\",\"eDDot\":\"⩷\",\"Edot\":\"Ė\",\"edot\":\"ė\",\"eDot\":\"≑\",\"ee\":\"ⅇ\",\"efDot\":\"≒\",\"Efr\":\"𝔈\",\"efr\":\"𝔢\",\"eg\":\"⪚\",\"Egrave\":\"È\",\"egrave\":\"è\",\"egs\":\"⪖\",\"egsdot\":\"⪘\",\"el\":\"⪙\",\"Element\":\"∈\",\"elinters\":\"⏧\",\"ell\":\"ℓ\",\"els\":\"⪕\",\"elsdot\":\"⪗\",\"Emacr\":\"Ē\",\"emacr\":\"ē\",\"empty\":\"∅\",\"emptyset\":\"∅\",\"EmptySmallSquare\":\"◻\",\"emptyv\":\"∅\",\"EmptyVerySmallSquare\":\"▫\",\"emsp13\":\" \",\"emsp14\":\" \",\"emsp\":\" \",\"ENG\":\"Ŋ\",\"eng\":\"ŋ\",\"ensp\":\" \",\"Eogon\":\"Ę\",\"eogon\":\"ę\",\"Eopf\":\"𝔼\",\"eopf\":\"𝕖\",\"epar\":\"⋕\",\"eparsl\":\"⧣\",\"eplus\":\"⩱\",\"epsi\":\"ε\",\"Epsilon\":\"Ε\",\"epsilon\":\"ε\",\"epsiv\":\"ϵ\",\"eqcirc\":\"≖\",\"eqcolon\":\"≕\",\"eqsim\":\"≂\",\"eqslantgtr\":\"⪖\",\"eqslantless\":\"⪕\",\"Equal\":\"⩵\",\"equals\":\"=\",\"EqualTilde\":\"≂\",\"equest\":\"≟\",\"Equilibrium\":\"⇌\",\"equiv\":\"≡\",\"equivDD\":\"⩸\",\"eqvparsl\":\"⧥\",\"erarr\":\"⥱\",\"erDot\":\"≓\",\"escr\":\"ℯ\",\"Escr\":\"ℰ\",\"esdot\":\"≐\",\"Esim\":\"⩳\",\"esim\":\"≂\",\"Eta\":\"Η\",\"eta\":\"η\",\"ETH\":\"Ð\",\"eth\":\"ð\",\"Euml\":\"Ë\",\"euml\":\"ë\",\"euro\":\"€\",\"excl\":\"!\",\"exist\":\"∃\",\"Exists\":\"∃\",\"expectation\":\"ℰ\",\"exponentiale\":\"ⅇ\",\"ExponentialE\":\"ⅇ\",\"fallingdotseq\":\"≒\",\"Fcy\":\"Ф\",\"fcy\":\"ф\",\"female\":\"♀\",\"ffilig\":\"ﬃ\",\"fflig\":\"ﬀ\",\"ffllig\":\"ﬄ\",\"Ffr\":\"𝔉\",\"ffr\":\"𝔣\",\"filig\":\"ﬁ\",\"FilledSmallSquare\":\"◼\",\"FilledVerySmallSquare\":\"▪\",\"fjlig\":\"fj\",\"flat\":\"♭\",\"fllig\":\"ﬂ\",\"fltns\":\"▱\",\"fnof\":\"ƒ\",\"Fopf\":\"𝔽\",\"fopf\":\"𝕗\",\"forall\":\"∀\",\"ForAll\":\"∀\",\"fork\":\"⋔\",\"forkv\":\"⫙\",\"Fouriertrf\":\"ℱ\",\"fpartint\":\"⨍\",\"frac12\":\"½\",\"frac13\":\"⅓\",\"frac14\":\"¼\",\"frac15\":\"⅕\",\"frac16\":\"⅙\",\"frac18\":\"⅛\",\"frac23\":\"⅔\",\"frac25\":\"⅖\",\"frac34\":\"¾\",\"frac35\":\"⅗\",\"frac38\":\"⅜\",\"frac45\":\"⅘\",\"frac56\":\"⅚\",\"frac58\":\"⅝\",\"frac78\":\"⅞\",\"frasl\":\"⁄\",\"frown\":\"⌢\",\"fscr\":\"𝒻\",\"Fscr\":\"ℱ\",\"gacute\":\"ǵ\",\"Gamma\":\"Γ\",\"gamma\":\"γ\",\"Gammad\":\"Ϝ\",\"gammad\":\"ϝ\",\"gap\":\"⪆\",\"Gbreve\":\"Ğ\",\"gbreve\":\"ğ\",\"Gcedil\":\"Ģ\",\"Gcirc\":\"Ĝ\",\"gcirc\":\"ĝ\",\"Gcy\":\"Г\",\"gcy\":\"г\",\"Gdot\":\"Ġ\",\"gdot\":\"ġ\",\"ge\":\"≥\",\"gE\":\"≧\",\"gEl\":\"⪌\",\"gel\":\"⋛\",\"geq\":\"≥\",\"geqq\":\"≧\",\"geqslant\":\"⩾\",\"gescc\":\"⪩\",\"ges\":\"⩾\",\"gesdot\":\"⪀\",\"gesdoto\":\"⪂\",\"gesdotol\":\"⪄\",\"gesl\":\"⋛︀\",\"gesles\":\"⪔\",\"Gfr\":\"𝔊\",\"gfr\":\"𝔤\",\"gg\":\"≫\",\"Gg\":\"⋙\",\"ggg\":\"⋙\",\"gimel\":\"ℷ\",\"GJcy\":\"Ѓ\",\"gjcy\":\"ѓ\",\"gla\":\"⪥\",\"gl\":\"≷\",\"glE\":\"⪒\",\"glj\":\"⪤\",\"gnap\":\"⪊\",\"gnapprox\":\"⪊\",\"gne\":\"⪈\",\"gnE\":\"≩\",\"gneq\":\"⪈\",\"gneqq\":\"≩\",\"gnsim\":\"⋧\",\"Gopf\":\"𝔾\",\"gopf\":\"𝕘\",\"grave\":\"`\",\"GreaterEqual\":\"≥\",\"GreaterEqualLess\":\"⋛\",\"GreaterFullEqual\":\"≧\",\"GreaterGreater\":\"⪢\",\"GreaterLess\":\"≷\",\"GreaterSlantEqual\":\"⩾\",\"GreaterTilde\":\"≳\",\"Gscr\":\"𝒢\",\"gscr\":\"ℊ\",\"gsim\":\"≳\",\"gsime\":\"⪎\",\"gsiml\":\"⪐\",\"gtcc\":\"⪧\",\"gtcir\":\"⩺\",\"gt\":\">\",\"GT\":\">\",\"Gt\":\"≫\",\"gtdot\":\"⋗\",\"gtlPar\":\"⦕\",\"gtquest\":\"⩼\",\"gtrapprox\":\"⪆\",\"gtrarr\":\"⥸\",\"gtrdot\":\"⋗\",\"gtreqless\":\"⋛\",\"gtreqqless\":\"⪌\",\"gtrless\":\"≷\",\"gtrsim\":\"≳\",\"gvertneqq\":\"≩︀\",\"gvnE\":\"≩︀\",\"Hacek\":\"ˇ\",\"hairsp\":\" \",\"half\":\"½\",\"hamilt\":\"ℋ\",\"HARDcy\":\"Ъ\",\"hardcy\":\"ъ\",\"harrcir\":\"⥈\",\"harr\":\"↔\",\"hArr\":\"⇔\",\"harrw\":\"↭\",\"Hat\":\"^\",\"hbar\":\"ℏ\",\"Hcirc\":\"Ĥ\",\"hcirc\":\"ĥ\",\"hearts\":\"♥\",\"heartsuit\":\"♥\",\"hellip\":\"…\",\"hercon\":\"⊹\",\"hfr\":\"𝔥\",\"Hfr\":\"ℌ\",\"HilbertSpace\":\"ℋ\",\"hksearow\":\"⤥\",\"hkswarow\":\"⤦\",\"hoarr\":\"⇿\",\"homtht\":\"∻\",\"hookleftarrow\":\"↩\",\"hookrightarrow\":\"↪\",\"hopf\":\"𝕙\",\"Hopf\":\"ℍ\",\"horbar\":\"―\",\"HorizontalLine\":\"─\",\"hscr\":\"𝒽\",\"Hscr\":\"ℋ\",\"hslash\":\"ℏ\",\"Hstrok\":\"Ħ\",\"hstrok\":\"ħ\",\"HumpDownHump\":\"≎\",\"HumpEqual\":\"≏\",\"hybull\":\"⁃\",\"hyphen\":\"‐\",\"Iacute\":\"Í\",\"iacute\":\"í\",\"ic\":\"⁣\",\"Icirc\":\"Î\",\"icirc\":\"î\",\"Icy\":\"И\",\"icy\":\"и\",\"Idot\":\"İ\",\"IEcy\":\"Е\",\"iecy\":\"е\",\"iexcl\":\"¡\",\"iff\":\"⇔\",\"ifr\":\"𝔦\",\"Ifr\":\"ℑ\",\"Igrave\":\"Ì\",\"igrave\":\"ì\",\"ii\":\"ⅈ\",\"iiiint\":\"⨌\",\"iiint\":\"∭\",\"iinfin\":\"⧜\",\"iiota\":\"℩\",\"IJlig\":\"Ĳ\",\"ijlig\":\"ĳ\",\"Imacr\":\"Ī\",\"imacr\":\"ī\",\"image\":\"ℑ\",\"ImaginaryI\":\"ⅈ\",\"imagline\":\"ℐ\",\"imagpart\":\"ℑ\",\"imath\":\"ı\",\"Im\":\"ℑ\",\"imof\":\"⊷\",\"imped\":\"Ƶ\",\"Implies\":\"⇒\",\"incare\":\"℅\",\"in\":\"∈\",\"infin\":\"∞\",\"infintie\":\"⧝\",\"inodot\":\"ı\",\"intcal\":\"⊺\",\"int\":\"∫\",\"Int\":\"∬\",\"integers\":\"ℤ\",\"Integral\":\"∫\",\"intercal\":\"⊺\",\"Intersection\":\"⋂\",\"intlarhk\":\"⨗\",\"intprod\":\"⨼\",\"InvisibleComma\":\"⁣\",\"InvisibleTimes\":\"⁢\",\"IOcy\":\"Ё\",\"iocy\":\"ё\",\"Iogon\":\"Į\",\"iogon\":\"į\",\"Iopf\":\"𝕀\",\"iopf\":\"𝕚\",\"Iota\":\"Ι\",\"iota\":\"ι\",\"iprod\":\"⨼\",\"iquest\":\"¿\",\"iscr\":\"𝒾\",\"Iscr\":\"ℐ\",\"isin\":\"∈\",\"isindot\":\"⋵\",\"isinE\":\"⋹\",\"isins\":\"⋴\",\"isinsv\":\"⋳\",\"isinv\":\"∈\",\"it\":\"⁢\",\"Itilde\":\"Ĩ\",\"itilde\":\"ĩ\",\"Iukcy\":\"І\",\"iukcy\":\"і\",\"Iuml\":\"Ï\",\"iuml\":\"ï\",\"Jcirc\":\"Ĵ\",\"jcirc\":\"ĵ\",\"Jcy\":\"Й\",\"jcy\":\"й\",\"Jfr\":\"𝔍\",\"jfr\":\"𝔧\",\"jmath\":\"ȷ\",\"Jopf\":\"𝕁\",\"jopf\":\"𝕛\",\"Jscr\":\"𝒥\",\"jscr\":\"𝒿\",\"Jsercy\":\"Ј\",\"jsercy\":\"ј\",\"Jukcy\":\"Є\",\"jukcy\":\"є\",\"Kappa\":\"Κ\",\"kappa\":\"κ\",\"kappav\":\"ϰ\",\"Kcedil\":\"Ķ\",\"kcedil\":\"ķ\",\"Kcy\":\"К\",\"kcy\":\"к\",\"Kfr\":\"𝔎\",\"kfr\":\"𝔨\",\"kgreen\":\"ĸ\",\"KHcy\":\"Х\",\"khcy\":\"х\",\"KJcy\":\"Ќ\",\"kjcy\":\"ќ\",\"Kopf\":\"𝕂\",\"kopf\":\"𝕜\",\"Kscr\":\"𝒦\",\"kscr\":\"𝓀\",\"lAarr\":\"⇚\",\"Lacute\":\"Ĺ\",\"lacute\":\"ĺ\",\"laemptyv\":\"⦴\",\"lagran\":\"ℒ\",\"Lambda\":\"Λ\",\"lambda\":\"λ\",\"lang\":\"⟨\",\"Lang\":\"⟪\",\"langd\":\"⦑\",\"langle\":\"⟨\",\"lap\":\"⪅\",\"Laplacetrf\":\"ℒ\",\"laquo\":\"«\",\"larrb\":\"⇤\",\"larrbfs\":\"⤟\",\"larr\":\"←\",\"Larr\":\"↞\",\"lArr\":\"⇐\",\"larrfs\":\"⤝\",\"larrhk\":\"↩\",\"larrlp\":\"↫\",\"larrpl\":\"⤹\",\"larrsim\":\"⥳\",\"larrtl\":\"↢\",\"latail\":\"⤙\",\"lAtail\":\"⤛\",\"lat\":\"⪫\",\"late\":\"⪭\",\"lates\":\"⪭︀\",\"lbarr\":\"⤌\",\"lBarr\":\"⤎\",\"lbbrk\":\"❲\",\"lbrace\":\"{\",\"lbrack\":\"[\",\"lbrke\":\"⦋\",\"lbrksld\":\"⦏\",\"lbrkslu\":\"⦍\",\"Lcaron\":\"Ľ\",\"lcaron\":\"ľ\",\"Lcedil\":\"Ļ\",\"lcedil\":\"ļ\",\"lceil\":\"⌈\",\"lcub\":\"{\",\"Lcy\":\"Л\",\"lcy\":\"л\",\"ldca\":\"⤶\",\"ldquo\":\"“\",\"ldquor\":\"„\",\"ldrdhar\":\"⥧\",\"ldrushar\":\"⥋\",\"ldsh\":\"↲\",\"le\":\"≤\",\"lE\":\"≦\",\"LeftAngleBracket\":\"⟨\",\"LeftArrowBar\":\"⇤\",\"leftarrow\":\"←\",\"LeftArrow\":\"←\",\"Leftarrow\":\"⇐\",\"LeftArrowRightArrow\":\"⇆\",\"leftarrowtail\":\"↢\",\"LeftCeiling\":\"⌈\",\"LeftDoubleBracket\":\"⟦\",\"LeftDownTeeVector\":\"⥡\",\"LeftDownVectorBar\":\"⥙\",\"LeftDownVector\":\"⇃\",\"LeftFloor\":\"⌊\",\"leftharpoondown\":\"↽\",\"leftharpoonup\":\"↼\",\"leftleftarrows\":\"⇇\",\"leftrightarrow\":\"↔\",\"LeftRightArrow\":\"↔\",\"Leftrightarrow\":\"⇔\",\"leftrightarrows\":\"⇆\",\"leftrightharpoons\":\"⇋\",\"leftrightsquigarrow\":\"↭\",\"LeftRightVector\":\"⥎\",\"LeftTeeArrow\":\"↤\",\"LeftTee\":\"⊣\",\"LeftTeeVector\":\"⥚\",\"leftthreetimes\":\"⋋\",\"LeftTriangleBar\":\"⧏\",\"LeftTriangle\":\"⊲\",\"LeftTriangleEqual\":\"⊴\",\"LeftUpDownVector\":\"⥑\",\"LeftUpTeeVector\":\"⥠\",\"LeftUpVectorBar\":\"⥘\",\"LeftUpVector\":\"↿\",\"LeftVectorBar\":\"⥒\",\"LeftVector\":\"↼\",\"lEg\":\"⪋\",\"leg\":\"⋚\",\"leq\":\"≤\",\"leqq\":\"≦\",\"leqslant\":\"⩽\",\"lescc\":\"⪨\",\"les\":\"⩽\",\"lesdot\":\"⩿\",\"lesdoto\":\"⪁\",\"lesdotor\":\"⪃\",\"lesg\":\"⋚︀\",\"lesges\":\"⪓\",\"lessapprox\":\"⪅\",\"lessdot\":\"⋖\",\"lesseqgtr\":\"⋚\",\"lesseqqgtr\":\"⪋\",\"LessEqualGreater\":\"⋚\",\"LessFullEqual\":\"≦\",\"LessGreater\":\"≶\",\"lessgtr\":\"≶\",\"LessLess\":\"⪡\",\"lesssim\":\"≲\",\"LessSlantEqual\":\"⩽\",\"LessTilde\":\"≲\",\"lfisht\":\"⥼\",\"lfloor\":\"⌊\",\"Lfr\":\"𝔏\",\"lfr\":\"𝔩\",\"lg\":\"≶\",\"lgE\":\"⪑\",\"lHar\":\"⥢\",\"lhard\":\"↽\",\"lharu\":\"↼\",\"lharul\":\"⥪\",\"lhblk\":\"▄\",\"LJcy\":\"Љ\",\"ljcy\":\"љ\",\"llarr\":\"⇇\",\"ll\":\"≪\",\"Ll\":\"⋘\",\"llcorner\":\"⌞\",\"Lleftarrow\":\"⇚\",\"llhard\":\"⥫\",\"lltri\":\"◺\",\"Lmidot\":\"Ŀ\",\"lmidot\":\"ŀ\",\"lmoustache\":\"⎰\",\"lmoust\":\"⎰\",\"lnap\":\"⪉\",\"lnapprox\":\"⪉\",\"lne\":\"⪇\",\"lnE\":\"≨\",\"lneq\":\"⪇\",\"lneqq\":\"≨\",\"lnsim\":\"⋦\",\"loang\":\"⟬\",\"loarr\":\"⇽\",\"lobrk\":\"⟦\",\"longleftarrow\":\"⟵\",\"LongLeftArrow\":\"⟵\",\"Longleftarrow\":\"⟸\",\"longleftrightarrow\":\"⟷\",\"LongLeftRightArrow\":\"⟷\",\"Longleftrightarrow\":\"⟺\",\"longmapsto\":\"⟼\",\"longrightarrow\":\"⟶\",\"LongRightArrow\":\"⟶\",\"Longrightarrow\":\"⟹\",\"looparrowleft\":\"↫\",\"looparrowright\":\"↬\",\"lopar\":\"⦅\",\"Lopf\":\"𝕃\",\"lopf\":\"𝕝\",\"loplus\":\"⨭\",\"lotimes\":\"⨴\",\"lowast\":\"∗\",\"lowbar\":\"_\",\"LowerLeftArrow\":\"↙\",\"LowerRightArrow\":\"↘\",\"loz\":\"◊\",\"lozenge\":\"◊\",\"lozf\":\"⧫\",\"lpar\":\"(\",\"lparlt\":\"⦓\",\"lrarr\":\"⇆\",\"lrcorner\":\"⌟\",\"lrhar\":\"⇋\",\"lrhard\":\"⥭\",\"lrm\":\"‎\",\"lrtri\":\"⊿\",\"lsaquo\":\"‹\",\"lscr\":\"𝓁\",\"Lscr\":\"ℒ\",\"lsh\":\"↰\",\"Lsh\":\"↰\",\"lsim\":\"≲\",\"lsime\":\"⪍\",\"lsimg\":\"⪏\",\"lsqb\":\"[\",\"lsquo\":\"‘\",\"lsquor\":\"‚\",\"Lstrok\":\"Ł\",\"lstrok\":\"ł\",\"ltcc\":\"⪦\",\"ltcir\":\"⩹\",\"lt\":\"<\",\"LT\":\"<\",\"Lt\":\"≪\",\"ltdot\":\"⋖\",\"lthree\":\"⋋\",\"ltimes\":\"⋉\",\"ltlarr\":\"⥶\",\"ltquest\":\"⩻\",\"ltri\":\"◃\",\"ltrie\":\"⊴\",\"ltrif\":\"◂\",\"ltrPar\":\"⦖\",\"lurdshar\":\"⥊\",\"luruhar\":\"⥦\",\"lvertneqq\":\"≨︀\",\"lvnE\":\"≨︀\",\"macr\":\"¯\",\"male\":\"♂\",\"malt\":\"✠\",\"maltese\":\"✠\",\"Map\":\"⤅\",\"map\":\"↦\",\"mapsto\":\"↦\",\"mapstodown\":\"↧\",\"mapstoleft\":\"↤\",\"mapstoup\":\"↥\",\"marker\":\"▮\",\"mcomma\":\"⨩\",\"Mcy\":\"М\",\"mcy\":\"м\",\"mdash\":\"—\",\"mDDot\":\"∺\",\"measuredangle\":\"∡\",\"MediumSpace\":\" \",\"Mellintrf\":\"ℳ\",\"Mfr\":\"𝔐\",\"mfr\":\"𝔪\",\"mho\":\"℧\",\"micro\":\"µ\",\"midast\":\"*\",\"midcir\":\"⫰\",\"mid\":\"∣\",\"middot\":\"·\",\"minusb\":\"⊟\",\"minus\":\"−\",\"minusd\":\"∸\",\"minusdu\":\"⨪\",\"MinusPlus\":\"∓\",\"mlcp\":\"⫛\",\"mldr\":\"…\",\"mnplus\":\"∓\",\"models\":\"⊧\",\"Mopf\":\"𝕄\",\"mopf\":\"𝕞\",\"mp\":\"∓\",\"mscr\":\"𝓂\",\"Mscr\":\"ℳ\",\"mstpos\":\"∾\",\"Mu\":\"Μ\",\"mu\":\"μ\",\"multimap\":\"⊸\",\"mumap\":\"⊸\",\"nabla\":\"∇\",\"Nacute\":\"Ń\",\"nacute\":\"ń\",\"nang\":\"∠⃒\",\"nap\":\"≉\",\"napE\":\"⩰̸\",\"napid\":\"≋̸\",\"napos\":\"ŉ\",\"napprox\":\"≉\",\"natural\":\"♮\",\"naturals\":\"ℕ\",\"natur\":\"♮\",\"nbsp\":\" \",\"nbump\":\"≎̸\",\"nbumpe\":\"≏̸\",\"ncap\":\"⩃\",\"Ncaron\":\"Ň\",\"ncaron\":\"ň\",\"Ncedil\":\"Ņ\",\"ncedil\":\"ņ\",\"ncong\":\"≇\",\"ncongdot\":\"⩭̸\",\"ncup\":\"⩂\",\"Ncy\":\"Н\",\"ncy\":\"н\",\"ndash\":\"–\",\"nearhk\":\"⤤\",\"nearr\":\"↗\",\"neArr\":\"⇗\",\"nearrow\":\"↗\",\"ne\":\"≠\",\"nedot\":\"≐̸\",\"NegativeMediumSpace\":\"​\",\"NegativeThickSpace\":\"​\",\"NegativeThinSpace\":\"​\",\"NegativeVeryThinSpace\":\"​\",\"nequiv\":\"≢\",\"nesear\":\"⤨\",\"nesim\":\"≂̸\",\"NestedGreaterGreater\":\"≫\",\"NestedLessLess\":\"≪\",\"NewLine\":\"\\n\",\"nexist\":\"∄\",\"nexists\":\"∄\",\"Nfr\":\"𝔑\",\"nfr\":\"𝔫\",\"ngE\":\"≧̸\",\"nge\":\"≱\",\"ngeq\":\"≱\",\"ngeqq\":\"≧̸\",\"ngeqslant\":\"⩾̸\",\"nges\":\"⩾̸\",\"nGg\":\"⋙̸\",\"ngsim\":\"≵\",\"nGt\":\"≫⃒\",\"ngt\":\"≯\",\"ngtr\":\"≯\",\"nGtv\":\"≫̸\",\"nharr\":\"↮\",\"nhArr\":\"⇎\",\"nhpar\":\"⫲\",\"ni\":\"∋\",\"nis\":\"⋼\",\"nisd\":\"⋺\",\"niv\":\"∋\",\"NJcy\":\"Њ\",\"njcy\":\"њ\",\"nlarr\":\"↚\",\"nlArr\":\"⇍\",\"nldr\":\"‥\",\"nlE\":\"≦̸\",\"nle\":\"≰\",\"nleftarrow\":\"↚\",\"nLeftarrow\":\"⇍\",\"nleftrightarrow\":\"↮\",\"nLeftrightarrow\":\"⇎\",\"nleq\":\"≰\",\"nleqq\":\"≦̸\",\"nleqslant\":\"⩽̸\",\"nles\":\"⩽̸\",\"nless\":\"≮\",\"nLl\":\"⋘̸\",\"nlsim\":\"≴\",\"nLt\":\"≪⃒\",\"nlt\":\"≮\",\"nltri\":\"⋪\",\"nltrie\":\"⋬\",\"nLtv\":\"≪̸\",\"nmid\":\"∤\",\"NoBreak\":\"⁠\",\"NonBreakingSpace\":\" \",\"nopf\":\"𝕟\",\"Nopf\":\"ℕ\",\"Not\":\"⫬\",\"not\":\"¬\",\"NotCongruent\":\"≢\",\"NotCupCap\":\"≭\",\"NotDoubleVerticalBar\":\"∦\",\"NotElement\":\"∉\",\"NotEqual\":\"≠\",\"NotEqualTilde\":\"≂̸\",\"NotExists\":\"∄\",\"NotGreater\":\"≯\",\"NotGreaterEqual\":\"≱\",\"NotGreaterFullEqual\":\"≧̸\",\"NotGreaterGreater\":\"≫̸\",\"NotGreaterLess\":\"≹\",\"NotGreaterSlantEqual\":\"⩾̸\",\"NotGreaterTilde\":\"≵\",\"NotHumpDownHump\":\"≎̸\",\"NotHumpEqual\":\"≏̸\",\"notin\":\"∉\",\"notindot\":\"⋵̸\",\"notinE\":\"⋹̸\",\"notinva\":\"∉\",\"notinvb\":\"⋷\",\"notinvc\":\"⋶\",\"NotLeftTriangleBar\":\"⧏̸\",\"NotLeftTriangle\":\"⋪\",\"NotLeftTriangleEqual\":\"⋬\",\"NotLess\":\"≮\",\"NotLessEqual\":\"≰\",\"NotLessGreater\":\"≸\",\"NotLessLess\":\"≪̸\",\"NotLessSlantEqual\":\"⩽̸\",\"NotLessTilde\":\"≴\",\"NotNestedGreaterGreater\":\"⪢̸\",\"NotNestedLessLess\":\"⪡̸\",\"notni\":\"∌\",\"notniva\":\"∌\",\"notnivb\":\"⋾\",\"notnivc\":\"⋽\",\"NotPrecedes\":\"⊀\",\"NotPrecedesEqual\":\"⪯̸\",\"NotPrecedesSlantEqual\":\"⋠\",\"NotReverseElement\":\"∌\",\"NotRightTriangleBar\":\"⧐̸\",\"NotRightTriangle\":\"⋫\",\"NotRightTriangleEqual\":\"⋭\",\"NotSquareSubset\":\"⊏̸\",\"NotSquareSubsetEqual\":\"⋢\",\"NotSquareSuperset\":\"⊐̸\",\"NotSquareSupersetEqual\":\"⋣\",\"NotSubset\":\"⊂⃒\",\"NotSubsetEqual\":\"⊈\",\"NotSucceeds\":\"⊁\",\"NotSucceedsEqual\":\"⪰̸\",\"NotSucceedsSlantEqual\":\"⋡\",\"NotSucceedsTilde\":\"≿̸\",\"NotSuperset\":\"⊃⃒\",\"NotSupersetEqual\":\"⊉\",\"NotTilde\":\"≁\",\"NotTildeEqual\":\"≄\",\"NotTildeFullEqual\":\"≇\",\"NotTildeTilde\":\"≉\",\"NotVerticalBar\":\"∤\",\"nparallel\":\"∦\",\"npar\":\"∦\",\"nparsl\":\"⫽⃥\",\"npart\":\"∂̸\",\"npolint\":\"⨔\",\"npr\":\"⊀\",\"nprcue\":\"⋠\",\"nprec\":\"⊀\",\"npreceq\":\"⪯̸\",\"npre\":\"⪯̸\",\"nrarrc\":\"⤳̸\",\"nrarr\":\"↛\",\"nrArr\":\"⇏\",\"nrarrw\":\"↝̸\",\"nrightarrow\":\"↛\",\"nRightarrow\":\"⇏\",\"nrtri\":\"⋫\",\"nrtrie\":\"⋭\",\"nsc\":\"⊁\",\"nsccue\":\"⋡\",\"nsce\":\"⪰̸\",\"Nscr\":\"𝒩\",\"nscr\":\"𝓃\",\"nshortmid\":\"∤\",\"nshortparallel\":\"∦\",\"nsim\":\"≁\",\"nsime\":\"≄\",\"nsimeq\":\"≄\",\"nsmid\":\"∤\",\"nspar\":\"∦\",\"nsqsube\":\"⋢\",\"nsqsupe\":\"⋣\",\"nsub\":\"⊄\",\"nsubE\":\"⫅̸\",\"nsube\":\"⊈\",\"nsubset\":\"⊂⃒\",\"nsubseteq\":\"⊈\",\"nsubseteqq\":\"⫅̸\",\"nsucc\":\"⊁\",\"nsucceq\":\"⪰̸\",\"nsup\":\"⊅\",\"nsupE\":\"⫆̸\",\"nsupe\":\"⊉\",\"nsupset\":\"⊃⃒\",\"nsupseteq\":\"⊉\",\"nsupseteqq\":\"⫆̸\",\"ntgl\":\"≹\",\"Ntilde\":\"Ñ\",\"ntilde\":\"ñ\",\"ntlg\":\"≸\",\"ntriangleleft\":\"⋪\",\"ntrianglelefteq\":\"⋬\",\"ntriangleright\":\"⋫\",\"ntrianglerighteq\":\"⋭\",\"Nu\":\"Ν\",\"nu\":\"ν\",\"num\":\"#\",\"numero\":\"№\",\"numsp\":\" \",\"nvap\":\"≍⃒\",\"nvdash\":\"⊬\",\"nvDash\":\"⊭\",\"nVdash\":\"⊮\",\"nVDash\":\"⊯\",\"nvge\":\"≥⃒\",\"nvgt\":\">⃒\",\"nvHarr\":\"⤄\",\"nvinfin\":\"⧞\",\"nvlArr\":\"⤂\",\"nvle\":\"≤⃒\",\"nvlt\":\"<⃒\",\"nvltrie\":\"⊴⃒\",\"nvrArr\":\"⤃\",\"nvrtrie\":\"⊵⃒\",\"nvsim\":\"∼⃒\",\"nwarhk\":\"⤣\",\"nwarr\":\"↖\",\"nwArr\":\"⇖\",\"nwarrow\":\"↖\",\"nwnear\":\"⤧\",\"Oacute\":\"Ó\",\"oacute\":\"ó\",\"oast\":\"⊛\",\"Ocirc\":\"Ô\",\"ocirc\":\"ô\",\"ocir\":\"⊚\",\"Ocy\":\"О\",\"ocy\":\"о\",\"odash\":\"⊝\",\"Odblac\":\"Ő\",\"odblac\":\"ő\",\"odiv\":\"⨸\",\"odot\":\"⊙\",\"odsold\":\"⦼\",\"OElig\":\"Œ\",\"oelig\":\"œ\",\"ofcir\":\"⦿\",\"Ofr\":\"𝔒\",\"ofr\":\"𝔬\",\"ogon\":\"˛\",\"Ograve\":\"Ò\",\"ograve\":\"ò\",\"ogt\":\"⧁\",\"ohbar\":\"⦵\",\"ohm\":\"Ω\",\"oint\":\"∮\",\"olarr\":\"↺\",\"olcir\":\"⦾\",\"olcross\":\"⦻\",\"oline\":\"‾\",\"olt\":\"⧀\",\"Omacr\":\"Ō\",\"omacr\":\"ō\",\"Omega\":\"Ω\",\"omega\":\"ω\",\"Omicron\":\"Ο\",\"omicron\":\"ο\",\"omid\":\"⦶\",\"ominus\":\"⊖\",\"Oopf\":\"𝕆\",\"oopf\":\"𝕠\",\"opar\":\"⦷\",\"OpenCurlyDoubleQuote\":\"“\",\"OpenCurlyQuote\":\"‘\",\"operp\":\"⦹\",\"oplus\":\"⊕\",\"orarr\":\"↻\",\"Or\":\"⩔\",\"or\":\"∨\",\"ord\":\"⩝\",\"order\":\"ℴ\",\"orderof\":\"ℴ\",\"ordf\":\"ª\",\"ordm\":\"º\",\"origof\":\"⊶\",\"oror\":\"⩖\",\"orslope\":\"⩗\",\"orv\":\"⩛\",\"oS\":\"Ⓢ\",\"Oscr\":\"𝒪\",\"oscr\":\"ℴ\",\"Oslash\":\"Ø\",\"oslash\":\"ø\",\"osol\":\"⊘\",\"Otilde\":\"Õ\",\"otilde\":\"õ\",\"otimesas\":\"⨶\",\"Otimes\":\"⨷\",\"otimes\":\"⊗\",\"Ouml\":\"Ö\",\"ouml\":\"ö\",\"ovbar\":\"⌽\",\"OverBar\":\"‾\",\"OverBrace\":\"⏞\",\"OverBracket\":\"⎴\",\"OverParenthesis\":\"⏜\",\"para\":\"¶\",\"parallel\":\"∥\",\"par\":\"∥\",\"parsim\":\"⫳\",\"parsl\":\"⫽\",\"part\":\"∂\",\"PartialD\":\"∂\",\"Pcy\":\"П\",\"pcy\":\"п\",\"percnt\":\"%\",\"period\":\".\",\"permil\":\"‰\",\"perp\":\"⊥\",\"pertenk\":\"‱\",\"Pfr\":\"𝔓\",\"pfr\":\"𝔭\",\"Phi\":\"Φ\",\"phi\":\"φ\",\"phiv\":\"ϕ\",\"phmmat\":\"ℳ\",\"phone\":\"☎\",\"Pi\":\"Π\",\"pi\":\"π\",\"pitchfork\":\"⋔\",\"piv\":\"ϖ\",\"planck\":\"ℏ\",\"planckh\":\"ℎ\",\"plankv\":\"ℏ\",\"plusacir\":\"⨣\",\"plusb\":\"⊞\",\"pluscir\":\"⨢\",\"plus\":\"+\",\"plusdo\":\"∔\",\"plusdu\":\"⨥\",\"pluse\":\"⩲\",\"PlusMinus\":\"±\",\"plusmn\":\"±\",\"plussim\":\"⨦\",\"plustwo\":\"⨧\",\"pm\":\"±\",\"Poincareplane\":\"ℌ\",\"pointint\":\"⨕\",\"popf\":\"𝕡\",\"Popf\":\"ℙ\",\"pound\":\"£\",\"prap\":\"⪷\",\"Pr\":\"⪻\",\"pr\":\"≺\",\"prcue\":\"≼\",\"precapprox\":\"⪷\",\"prec\":\"≺\",\"preccurlyeq\":\"≼\",\"Precedes\":\"≺\",\"PrecedesEqual\":\"⪯\",\"PrecedesSlantEqual\":\"≼\",\"PrecedesTilde\":\"≾\",\"preceq\":\"⪯\",\"precnapprox\":\"⪹\",\"precneqq\":\"⪵\",\"precnsim\":\"⋨\",\"pre\":\"⪯\",\"prE\":\"⪳\",\"precsim\":\"≾\",\"prime\":\"′\",\"Prime\":\"″\",\"primes\":\"ℙ\",\"prnap\":\"⪹\",\"prnE\":\"⪵\",\"prnsim\":\"⋨\",\"prod\":\"∏\",\"Product\":\"∏\",\"profalar\":\"⌮\",\"profline\":\"⌒\",\"profsurf\":\"⌓\",\"prop\":\"∝\",\"Proportional\":\"∝\",\"Proportion\":\"∷\",\"propto\":\"∝\",\"prsim\":\"≾\",\"prurel\":\"⊰\",\"Pscr\":\"𝒫\",\"pscr\":\"𝓅\",\"Psi\":\"Ψ\",\"psi\":\"ψ\",\"puncsp\":\" \",\"Qfr\":\"𝔔\",\"qfr\":\"𝔮\",\"qint\":\"⨌\",\"qopf\":\"𝕢\",\"Qopf\":\"ℚ\",\"qprime\":\"⁗\",\"Qscr\":\"𝒬\",\"qscr\":\"𝓆\",\"quaternions\":\"ℍ\",\"quatint\":\"⨖\",\"quest\":\"?\",\"questeq\":\"≟\",\"quot\":\"\\\"\",\"QUOT\":\"\\\"\",\"rAarr\":\"⇛\",\"race\":\"∽̱\",\"Racute\":\"Ŕ\",\"racute\":\"ŕ\",\"radic\":\"√\",\"raemptyv\":\"⦳\",\"rang\":\"⟩\",\"Rang\":\"⟫\",\"rangd\":\"⦒\",\"range\":\"⦥\",\"rangle\":\"⟩\",\"raquo\":\"»\",\"rarrap\":\"⥵\",\"rarrb\":\"⇥\",\"rarrbfs\":\"⤠\",\"rarrc\":\"⤳\",\"rarr\":\"→\",\"Rarr\":\"↠\",\"rArr\":\"⇒\",\"rarrfs\":\"⤞\",\"rarrhk\":\"↪\",\"rarrlp\":\"↬\",\"rarrpl\":\"⥅\",\"rarrsim\":\"⥴\",\"Rarrtl\":\"⤖\",\"rarrtl\":\"↣\",\"rarrw\":\"↝\",\"ratail\":\"⤚\",\"rAtail\":\"⤜\",\"ratio\":\"∶\",\"rationals\":\"ℚ\",\"rbarr\":\"⤍\",\"rBarr\":\"⤏\",\"RBarr\":\"⤐\",\"rbbrk\":\"❳\",\"rbrace\":\"}\",\"rbrack\":\"]\",\"rbrke\":\"⦌\",\"rbrksld\":\"⦎\",\"rbrkslu\":\"⦐\",\"Rcaron\":\"Ř\",\"rcaron\":\"ř\",\"Rcedil\":\"Ŗ\",\"rcedil\":\"ŗ\",\"rceil\":\"⌉\",\"rcub\":\"}\",\"Rcy\":\"Р\",\"rcy\":\"р\",\"rdca\":\"⤷\",\"rdldhar\":\"⥩\",\"rdquo\":\"”\",\"rdquor\":\"”\",\"rdsh\":\"↳\",\"real\":\"ℜ\",\"realine\":\"ℛ\",\"realpart\":\"ℜ\",\"reals\":\"ℝ\",\"Re\":\"ℜ\",\"rect\":\"▭\",\"reg\":\"®\",\"REG\":\"®\",\"ReverseElement\":\"∋\",\"ReverseEquilibrium\":\"⇋\",\"ReverseUpEquilibrium\":\"⥯\",\"rfisht\":\"⥽\",\"rfloor\":\"⌋\",\"rfr\":\"𝔯\",\"Rfr\":\"ℜ\",\"rHar\":\"⥤\",\"rhard\":\"⇁\",\"rharu\":\"⇀\",\"rharul\":\"⥬\",\"Rho\":\"Ρ\",\"rho\":\"ρ\",\"rhov\":\"ϱ\",\"RightAngleBracket\":\"⟩\",\"RightArrowBar\":\"⇥\",\"rightarrow\":\"→\",\"RightArrow\":\"→\",\"Rightarrow\":\"⇒\",\"RightArrowLeftArrow\":\"⇄\",\"rightarrowtail\":\"↣\",\"RightCeiling\":\"⌉\",\"RightDoubleBracket\":\"⟧\",\"RightDownTeeVector\":\"⥝\",\"RightDownVectorBar\":\"⥕\",\"RightDownVector\":\"⇂\",\"RightFloor\":\"⌋\",\"rightharpoondown\":\"⇁\",\"rightharpoonup\":\"⇀\",\"rightleftarrows\":\"⇄\",\"rightleftharpoons\":\"⇌\",\"rightrightarrows\":\"⇉\",\"rightsquigarrow\":\"↝\",\"RightTeeArrow\":\"↦\",\"RightTee\":\"⊢\",\"RightTeeVector\":\"⥛\",\"rightthreetimes\":\"⋌\",\"RightTriangleBar\":\"⧐\",\"RightTriangle\":\"⊳\",\"RightTriangleEqual\":\"⊵\",\"RightUpDownVector\":\"⥏\",\"RightUpTeeVector\":\"⥜\",\"RightUpVectorBar\":\"⥔\",\"RightUpVector\":\"↾\",\"RightVectorBar\":\"⥓\",\"RightVector\":\"⇀\",\"ring\":\"˚\",\"risingdotseq\":\"≓\",\"rlarr\":\"⇄\",\"rlhar\":\"⇌\",\"rlm\":\"‏\",\"rmoustache\":\"⎱\",\"rmoust\":\"⎱\",\"rnmid\":\"⫮\",\"roang\":\"⟭\",\"roarr\":\"⇾\",\"robrk\":\"⟧\",\"ropar\":\"⦆\",\"ropf\":\"𝕣\",\"Ropf\":\"ℝ\",\"roplus\":\"⨮\",\"rotimes\":\"⨵\",\"RoundImplies\":\"⥰\",\"rpar\":\")\",\"rpargt\":\"⦔\",\"rppolint\":\"⨒\",\"rrarr\":\"⇉\",\"Rrightarrow\":\"⇛\",\"rsaquo\":\"›\",\"rscr\":\"𝓇\",\"Rscr\":\"ℛ\",\"rsh\":\"↱\",\"Rsh\":\"↱\",\"rsqb\":\"]\",\"rsquo\":\"’\",\"rsquor\":\"’\",\"rthree\":\"⋌\",\"rtimes\":\"⋊\",\"rtri\":\"▹\",\"rtrie\":\"⊵\",\"rtrif\":\"▸\",\"rtriltri\":\"⧎\",\"RuleDelayed\":\"⧴\",\"ruluhar\":\"⥨\",\"rx\":\"℞\",\"Sacute\":\"Ś\",\"sacute\":\"ś\",\"sbquo\":\"‚\",\"scap\":\"⪸\",\"Scaron\":\"Š\",\"scaron\":\"š\",\"Sc\":\"⪼\",\"sc\":\"≻\",\"sccue\":\"≽\",\"sce\":\"⪰\",\"scE\":\"⪴\",\"Scedil\":\"Ş\",\"scedil\":\"ş\",\"Scirc\":\"Ŝ\",\"scirc\":\"ŝ\",\"scnap\":\"⪺\",\"scnE\":\"⪶\",\"scnsim\":\"⋩\",\"scpolint\":\"⨓\",\"scsim\":\"≿\",\"Scy\":\"С\",\"scy\":\"с\",\"sdotb\":\"⊡\",\"sdot\":\"⋅\",\"sdote\":\"⩦\",\"searhk\":\"⤥\",\"searr\":\"↘\",\"seArr\":\"⇘\",\"searrow\":\"↘\",\"sect\":\"§\",\"semi\":\";\",\"seswar\":\"⤩\",\"setminus\":\"∖\",\"setmn\":\"∖\",\"sext\":\"✶\",\"Sfr\":\"𝔖\",\"sfr\":\"𝔰\",\"sfrown\":\"⌢\",\"sharp\":\"♯\",\"SHCHcy\":\"Щ\",\"shchcy\":\"щ\",\"SHcy\":\"Ш\",\"shcy\":\"ш\",\"ShortDownArrow\":\"↓\",\"ShortLeftArrow\":\"←\",\"shortmid\":\"∣\",\"shortparallel\":\"∥\",\"ShortRightArrow\":\"→\",\"ShortUpArrow\":\"↑\",\"shy\":\"­\",\"Sigma\":\"Σ\",\"sigma\":\"σ\",\"sigmaf\":\"ς\",\"sigmav\":\"ς\",\"sim\":\"∼\",\"simdot\":\"⩪\",\"sime\":\"≃\",\"simeq\":\"≃\",\"simg\":\"⪞\",\"simgE\":\"⪠\",\"siml\":\"⪝\",\"simlE\":\"⪟\",\"simne\":\"≆\",\"simplus\":\"⨤\",\"simrarr\":\"⥲\",\"slarr\":\"←\",\"SmallCircle\":\"∘\",\"smallsetminus\":\"∖\",\"smashp\":\"⨳\",\"smeparsl\":\"⧤\",\"smid\":\"∣\",\"smile\":\"⌣\",\"smt\":\"⪪\",\"smte\":\"⪬\",\"smtes\":\"⪬︀\",\"SOFTcy\":\"Ь\",\"softcy\":\"ь\",\"solbar\":\"⌿\",\"solb\":\"⧄\",\"sol\":\"/\",\"Sopf\":\"𝕊\",\"sopf\":\"𝕤\",\"spades\":\"♠\",\"spadesuit\":\"♠\",\"spar\":\"∥\",\"sqcap\":\"⊓\",\"sqcaps\":\"⊓︀\",\"sqcup\":\"⊔\",\"sqcups\":\"⊔︀\",\"Sqrt\":\"√\",\"sqsub\":\"⊏\",\"sqsube\":\"⊑\",\"sqsubset\":\"⊏\",\"sqsubseteq\":\"⊑\",\"sqsup\":\"⊐\",\"sqsupe\":\"⊒\",\"sqsupset\":\"⊐\",\"sqsupseteq\":\"⊒\",\"square\":\"□\",\"Square\":\"□\",\"SquareIntersection\":\"⊓\",\"SquareSubset\":\"⊏\",\"SquareSubsetEqual\":\"⊑\",\"SquareSuperset\":\"⊐\",\"SquareSupersetEqual\":\"⊒\",\"SquareUnion\":\"⊔\",\"squarf\":\"▪\",\"squ\":\"□\",\"squf\":\"▪\",\"srarr\":\"→\",\"Sscr\":\"𝒮\",\"sscr\":\"𝓈\",\"ssetmn\":\"∖\",\"ssmile\":\"⌣\",\"sstarf\":\"⋆\",\"Star\":\"⋆\",\"star\":\"☆\",\"starf\":\"★\",\"straightepsilon\":\"ϵ\",\"straightphi\":\"ϕ\",\"strns\":\"¯\",\"sub\":\"⊂\",\"Sub\":\"⋐\",\"subdot\":\"⪽\",\"subE\":\"⫅\",\"sube\":\"⊆\",\"subedot\":\"⫃\",\"submult\":\"⫁\",\"subnE\":\"⫋\",\"subne\":\"⊊\",\"subplus\":\"⪿\",\"subrarr\":\"⥹\",\"subset\":\"⊂\",\"Subset\":\"⋐\",\"subseteq\":\"⊆\",\"subseteqq\":\"⫅\",\"SubsetEqual\":\"⊆\",\"subsetneq\":\"⊊\",\"subsetneqq\":\"⫋\",\"subsim\":\"⫇\",\"subsub\":\"⫕\",\"subsup\":\"⫓\",\"succapprox\":\"⪸\",\"succ\":\"≻\",\"succcurlyeq\":\"≽\",\"Succeeds\":\"≻\",\"SucceedsEqual\":\"⪰\",\"SucceedsSlantEqual\":\"≽\",\"SucceedsTilde\":\"≿\",\"succeq\":\"⪰\",\"succnapprox\":\"⪺\",\"succneqq\":\"⪶\",\"succnsim\":\"⋩\",\"succsim\":\"≿\",\"SuchThat\":\"∋\",\"sum\":\"∑\",\"Sum\":\"∑\",\"sung\":\"♪\",\"sup1\":\"¹\",\"sup2\":\"²\",\"sup3\":\"³\",\"sup\":\"⊃\",\"Sup\":\"⋑\",\"supdot\":\"⪾\",\"supdsub\":\"⫘\",\"supE\":\"⫆\",\"supe\":\"⊇\",\"supedot\":\"⫄\",\"Superset\":\"⊃\",\"SupersetEqual\":\"⊇\",\"suphsol\":\"⟉\",\"suphsub\":\"⫗\",\"suplarr\":\"⥻\",\"supmult\":\"⫂\",\"supnE\":\"⫌\",\"supne\":\"⊋\",\"supplus\":\"⫀\",\"supset\":\"⊃\",\"Supset\":\"⋑\",\"supseteq\":\"⊇\",\"supseteqq\":\"⫆\",\"supsetneq\":\"⊋\",\"supsetneqq\":\"⫌\",\"supsim\":\"⫈\",\"supsub\":\"⫔\",\"supsup\":\"⫖\",\"swarhk\":\"⤦\",\"swarr\":\"↙\",\"swArr\":\"⇙\",\"swarrow\":\"↙\",\"swnwar\":\"⤪\",\"szlig\":\"ß\",\"Tab\":\"\\t\",\"target\":\"⌖\",\"Tau\":\"Τ\",\"tau\":\"τ\",\"tbrk\":\"⎴\",\"Tcaron\":\"Ť\",\"tcaron\":\"ť\",\"Tcedil\":\"Ţ\",\"tcedil\":\"ţ\",\"Tcy\":\"Т\",\"tcy\":\"т\",\"tdot\":\"⃛\",\"telrec\":\"⌕\",\"Tfr\":\"𝔗\",\"tfr\":\"𝔱\",\"there4\":\"∴\",\"therefore\":\"∴\",\"Therefore\":\"∴\",\"Theta\":\"Θ\",\"theta\":\"θ\",\"thetasym\":\"ϑ\",\"thetav\":\"ϑ\",\"thickapprox\":\"≈\",\"thicksim\":\"∼\",\"ThickSpace\":\"  \",\"ThinSpace\":\" \",\"thinsp\":\" \",\"thkap\":\"≈\",\"thksim\":\"∼\",\"THORN\":\"Þ\",\"thorn\":\"þ\",\"tilde\":\"˜\",\"Tilde\":\"∼\",\"TildeEqual\":\"≃\",\"TildeFullEqual\":\"≅\",\"TildeTilde\":\"≈\",\"timesbar\":\"⨱\",\"timesb\":\"⊠\",\"times\":\"×\",\"timesd\":\"⨰\",\"tint\":\"∭\",\"toea\":\"⤨\",\"topbot\":\"⌶\",\"topcir\":\"⫱\",\"top\":\"⊤\",\"Topf\":\"𝕋\",\"topf\":\"𝕥\",\"topfork\":\"⫚\",\"tosa\":\"⤩\",\"tprime\":\"‴\",\"trade\":\"™\",\"TRADE\":\"™\",\"triangle\":\"▵\",\"triangledown\":\"▿\",\"triangleleft\":\"◃\",\"trianglelefteq\":\"⊴\",\"triangleq\":\"≜\",\"triangleright\":\"▹\",\"trianglerighteq\":\"⊵\",\"tridot\":\"◬\",\"trie\":\"≜\",\"triminus\":\"⨺\",\"TripleDot\":\"⃛\",\"triplus\":\"⨹\",\"trisb\":\"⧍\",\"tritime\":\"⨻\",\"trpezium\":\"⏢\",\"Tscr\":\"𝒯\",\"tscr\":\"𝓉\",\"TScy\":\"Ц\",\"tscy\":\"ц\",\"TSHcy\":\"Ћ\",\"tshcy\":\"ћ\",\"Tstrok\":\"Ŧ\",\"tstrok\":\"ŧ\",\"twixt\":\"≬\",\"twoheadleftarrow\":\"↞\",\"twoheadrightarrow\":\"↠\",\"Uacute\":\"Ú\",\"uacute\":\"ú\",\"uarr\":\"↑\",\"Uarr\":\"↟\",\"uArr\":\"⇑\",\"Uarrocir\":\"⥉\",\"Ubrcy\":\"Ў\",\"ubrcy\":\"ў\",\"Ubreve\":\"Ŭ\",\"ubreve\":\"ŭ\",\"Ucirc\":\"Û\",\"ucirc\":\"û\",\"Ucy\":\"У\",\"ucy\":\"у\",\"udarr\":\"⇅\",\"Udblac\":\"Ű\",\"udblac\":\"ű\",\"udhar\":\"⥮\",\"ufisht\":\"⥾\",\"Ufr\":\"𝔘\",\"ufr\":\"𝔲\",\"Ugrave\":\"Ù\",\"ugrave\":\"ù\",\"uHar\":\"⥣\",\"uharl\":\"↿\",\"uharr\":\"↾\",\"uhblk\":\"▀\",\"ulcorn\":\"⌜\",\"ulcorner\":\"⌜\",\"ulcrop\":\"⌏\",\"ultri\":\"◸\",\"Umacr\":\"Ū\",\"umacr\":\"ū\",\"uml\":\"¨\",\"UnderBar\":\"_\",\"UnderBrace\":\"⏟\",\"UnderBracket\":\"⎵\",\"UnderParenthesis\":\"⏝\",\"Union\":\"⋃\",\"UnionPlus\":\"⊎\",\"Uogon\":\"Ų\",\"uogon\":\"ų\",\"Uopf\":\"𝕌\",\"uopf\":\"𝕦\",\"UpArrowBar\":\"⤒\",\"uparrow\":\"↑\",\"UpArrow\":\"↑\",\"Uparrow\":\"⇑\",\"UpArrowDownArrow\":\"⇅\",\"updownarrow\":\"↕\",\"UpDownArrow\":\"↕\",\"Updownarrow\":\"⇕\",\"UpEquilibrium\":\"⥮\",\"upharpoonleft\":\"↿\",\"upharpoonright\":\"↾\",\"uplus\":\"⊎\",\"UpperLeftArrow\":\"↖\",\"UpperRightArrow\":\"↗\",\"upsi\":\"υ\",\"Upsi\":\"ϒ\",\"upsih\":\"ϒ\",\"Upsilon\":\"Υ\",\"upsilon\":\"υ\",\"UpTeeArrow\":\"↥\",\"UpTee\":\"⊥\",\"upuparrows\":\"⇈\",\"urcorn\":\"⌝\",\"urcorner\":\"⌝\",\"urcrop\":\"⌎\",\"Uring\":\"Ů\",\"uring\":\"ů\",\"urtri\":\"◹\",\"Uscr\":\"𝒰\",\"uscr\":\"𝓊\",\"utdot\":\"⋰\",\"Utilde\":\"Ũ\",\"utilde\":\"ũ\",\"utri\":\"▵\",\"utrif\":\"▴\",\"uuarr\":\"⇈\",\"Uuml\":\"Ü\",\"uuml\":\"ü\",\"uwangle\":\"⦧\",\"vangrt\":\"⦜\",\"varepsilon\":\"ϵ\",\"varkappa\":\"ϰ\",\"varnothing\":\"∅\",\"varphi\":\"ϕ\",\"varpi\":\"ϖ\",\"varpropto\":\"∝\",\"varr\":\"↕\",\"vArr\":\"⇕\",\"varrho\":\"ϱ\",\"varsigma\":\"ς\",\"varsubsetneq\":\"⊊︀\",\"varsubsetneqq\":\"⫋︀\",\"varsupsetneq\":\"⊋︀\",\"varsupsetneqq\":\"⫌︀\",\"vartheta\":\"ϑ\",\"vartriangleleft\":\"⊲\",\"vartriangleright\":\"⊳\",\"vBar\":\"⫨\",\"Vbar\":\"⫫\",\"vBarv\":\"⫩\",\"Vcy\":\"В\",\"vcy\":\"в\",\"vdash\":\"⊢\",\"vDash\":\"⊨\",\"Vdash\":\"⊩\",\"VDash\":\"⊫\",\"Vdashl\":\"⫦\",\"veebar\":\"⊻\",\"vee\":\"∨\",\"Vee\":\"⋁\",\"veeeq\":\"≚\",\"vellip\":\"⋮\",\"verbar\":\"|\",\"Verbar\":\"‖\",\"vert\":\"|\",\"Vert\":\"‖\",\"VerticalBar\":\"∣\",\"VerticalLine\":\"|\",\"VerticalSeparator\":\"❘\",\"VerticalTilde\":\"≀\",\"VeryThinSpace\":\" \",\"Vfr\":\"𝔙\",\"vfr\":\"𝔳\",\"vltri\":\"⊲\",\"vnsub\":\"⊂⃒\",\"vnsup\":\"⊃⃒\",\"Vopf\":\"𝕍\",\"vopf\":\"𝕧\",\"vprop\":\"∝\",\"vrtri\":\"⊳\",\"Vscr\":\"𝒱\",\"vscr\":\"𝓋\",\"vsubnE\":\"⫋︀\",\"vsubne\":\"⊊︀\",\"vsupnE\":\"⫌︀\",\"vsupne\":\"⊋︀\",\"Vvdash\":\"⊪\",\"vzigzag\":\"⦚\",\"Wcirc\":\"Ŵ\",\"wcirc\":\"ŵ\",\"wedbar\":\"⩟\",\"wedge\":\"∧\",\"Wedge\":\"⋀\",\"wedgeq\":\"≙\",\"weierp\":\"℘\",\"Wfr\":\"𝔚\",\"wfr\":\"𝔴\",\"Wopf\":\"𝕎\",\"wopf\":\"𝕨\",\"wp\":\"℘\",\"wr\":\"≀\",\"wreath\":\"≀\",\"Wscr\":\"𝒲\",\"wscr\":\"𝓌\",\"xcap\":\"⋂\",\"xcirc\":\"◯\",\"xcup\":\"⋃\",\"xdtri\":\"▽\",\"Xfr\":\"𝔛\",\"xfr\":\"𝔵\",\"xharr\":\"⟷\",\"xhArr\":\"⟺\",\"Xi\":\"Ξ\",\"xi\":\"ξ\",\"xlarr\":\"⟵\",\"xlArr\":\"⟸\",\"xmap\":\"⟼\",\"xnis\":\"⋻\",\"xodot\":\"⨀\",\"Xopf\":\"𝕏\",\"xopf\":\"𝕩\",\"xoplus\":\"⨁\",\"xotime\":\"⨂\",\"xrarr\":\"⟶\",\"xrArr\":\"⟹\",\"Xscr\":\"𝒳\",\"xscr\":\"𝓍\",\"xsqcup\":\"⨆\",\"xuplus\":\"⨄\",\"xutri\":\"△\",\"xvee\":\"⋁\",\"xwedge\":\"⋀\",\"Yacute\":\"Ý\",\"yacute\":\"ý\",\"YAcy\":\"Я\",\"yacy\":\"я\",\"Ycirc\":\"Ŷ\",\"ycirc\":\"ŷ\",\"Ycy\":\"Ы\",\"ycy\":\"ы\",\"yen\":\"¥\",\"Yfr\":\"𝔜\",\"yfr\":\"𝔶\",\"YIcy\":\"Ї\",\"yicy\":\"ї\",\"Yopf\":\"𝕐\",\"yopf\":\"𝕪\",\"Yscr\":\"𝒴\",\"yscr\":\"𝓎\",\"YUcy\":\"Ю\",\"yucy\":\"ю\",\"yuml\":\"ÿ\",\"Yuml\":\"Ÿ\",\"Zacute\":\"Ź\",\"zacute\":\"ź\",\"Zcaron\":\"Ž\",\"zcaron\":\"ž\",\"Zcy\":\"З\",\"zcy\":\"з\",\"Zdot\":\"Ż\",\"zdot\":\"ż\",\"zeetrf\":\"ℨ\",\"ZeroWidthSpace\":\"​\",\"Zeta\":\"Ζ\",\"zeta\":\"ζ\",\"zfr\":\"𝔷\",\"Zfr\":\"ℨ\",\"ZHcy\":\"Ж\",\"zhcy\":\"ж\",\"zigrarr\":\"⇝\",\"zopf\":\"𝕫\",\"Zopf\":\"ℤ\",\"Zscr\":\"𝒵\",\"zscr\":\"𝓏\",\"zwj\":\"‍\",\"zwnj\":\"‌\"}\n", "// HTML5 entities map: { name -> utf16string }\n//\n'use strict';\n\n/*eslint quotes:0*/\nmodule.exports = require('entities/lib/maps/entities.json');\n", "module.exports=/[!-#%-\\*,-\\/:;\\?@\\[-\\]_\\{\\}\\xA1\\xA7\\xAB\\xB6\\xB7\\xBB\\xBF\\u037E\\u0387\\u055A-\\u055F\\u0589\\u058A\\u05BE\\u05C0\\u05C3\\u05C6\\u05F3\\u05F4\\u0609\\u060A\\u060C\\u060D\\u061B\\u061E\\u061F\\u066A-\\u066D\\u06D4\\u0700-\\u070D\\u07F7-\\u07F9\\u0830-\\u083E\\u085E\\u0964\\u0965\\u0970\\u09FD\\u0A76\\u0AF0\\u0C84\\u0DF4\\u0E4F\\u0E5A\\u0E5B\\u0F04-\\u0F12\\u0F14\\u0F3A-\\u0F3D\\u0F85\\u0FD0-\\u0FD4\\u0FD9\\u0FDA\\u104A-\\u104F\\u10FB\\u1360-\\u1368\\u1400\\u166D\\u166E\\u169B\\u169C\\u16EB-\\u16ED\\u1735\\u1736\\u17D4-\\u17D6\\u17D8-\\u17DA\\u1800-\\u180A\\u1944\\u1945\\u1A1E\\u1A1F\\u1AA0-\\u1AA6\\u1AA8-\\u1AAD\\u1B5A-\\u1B60\\u1BFC-\\u1BFF\\u1C3B-\\u1C3F\\u1C7E\\u1C7F\\u1CC0-\\u1CC7\\u1CD3\\u2010-\\u2027\\u2030-\\u2043\\u2045-\\u2051\\u2053-\\u205E\\u207D\\u207E\\u208D\\u208E\\u2308-\\u230B\\u2329\\u232A\\u2768-\\u2775\\u27C5\\u27C6\\u27E6-\\u27EF\\u2983-\\u2998\\u29D8-\\u29DB\\u29FC\\u29FD\\u2CF9-\\u2CFC\\u2CFE\\u2CFF\\u2D70\\u2E00-\\u2E2E\\u2E30-\\u2E4E\\u3001-\\u3003\\u3008-\\u3011\\u3014-\\u301F\\u3030\\u303D\\u30A0\\u30FB\\uA4FE\\uA4FF\\uA60D-\\uA60F\\uA673\\uA67E\\uA6F2-\\uA6F7\\uA874-\\uA877\\uA8CE\\uA8CF\\uA8F8-\\uA8FA\\uA8FC\\uA92E\\uA92F\\uA95F\\uA9C1-\\uA9CD\\uA9DE\\uA9DF\\uAA5C-\\uAA5F\\uAADE\\uAADF\\uAAF0\\uAAF1\\uABEB\\uFD3E\\uFD3F\\uFE10-\\uFE19\\uFE30-\\uFE52\\uFE54-\\uFE61\\uFE63\\uFE68\\uFE6A\\uFE6B\\uFF01-\\uFF03\\uFF05-\\uFF0A\\uFF0C-\\uFF0F\\uFF1A\\uFF1B\\uFF1F\\uFF20\\uFF3B-\\uFF3D\\uFF3F\\uFF5B\\uFF5D\\uFF5F-\\uFF65]|\\uD800[\\uDD00-\\uDD02\\uDF9F\\uDFD0]|\\uD801\\uDD6F|\\uD802[\\uDC57\\uDD1F\\uDD3F\\uDE50-\\uDE58\\uDE7F\\uDEF0-\\uDEF6\\uDF39-\\uDF3F\\uDF99-\\uDF9C]|\\uD803[\\uDF55-\\uDF59]|\\uD804[\\uDC47-\\uDC4D\\uDCBB\\uDCBC\\uDCBE-\\uDCC1\\uDD40-\\uDD43\\uDD74\\uDD75\\uDDC5-\\uDDC8\\uDDCD\\uDDDB\\uDDDD-\\uDDDF\\uDE38-\\uDE3D\\uDEA9]|\\uD805[\\uDC4B-\\uDC4F\\uDC5B\\uDC5D\\uDCC6\\uDDC1-\\uDDD7\\uDE41-\\uDE43\\uDE60-\\uDE6C\\uDF3C-\\uDF3E]|\\uD806[\\uDC3B\\uDE3F-\\uDE46\\uDE9A-\\uDE9C\\uDE9E-\\uDEA2]|\\uD807[\\uDC41-\\uDC45\\uDC70\\uDC71\\uDEF7\\uDEF8]|\\uD809[\\uDC70-\\uDC74]|\\uD81A[\\uDE6E\\uDE6F\\uDEF5\\uDF37-\\uDF3B\\uDF44]|\\uD81B[\\uDE97-\\uDE9A]|\\uD82F\\uDC9F|\\uD836[\\uDE87-\\uDE8B]|\\uD83A[\\uDD5E\\uDD5F]/", "\n'use strict';\n\n\nvar encodeCache = {};\n\n\n// Create a lookup array where anything but characters in `chars` string\n// and alphanumeric chars is percent-encoded.\n//\nfunction getEncodeCache(exclude) {\n  var i, ch, cache = encodeCache[exclude];\n  if (cache) { return cache; }\n\n  cache = encodeCache[exclude] = [];\n\n  for (i = 0; i < 128; i++) {\n    ch = String.fromCharCode(i);\n\n    if (/^[0-9a-z]$/i.test(ch)) {\n      // always allow unencoded alphanumeric characters\n      cache.push(ch);\n    } else {\n      cache.push('%' + ('0' + i.toString(16).toUpperCase()).slice(-2));\n    }\n  }\n\n  for (i = 0; i < exclude.length; i++) {\n    cache[exclude.charCodeAt(i)] = exclude[i];\n  }\n\n  return cache;\n}\n\n\n// Encode unsafe characters with percent-encoding, skipping already\n// encoded sequences.\n//\n//  - string       - string to encode\n//  - exclude      - list of characters to ignore (in addition to a-zA-Z0-9)\n//  - keepEscaped  - don't encode '%' in a correct escape sequence (default: true)\n//\nfunction encode(string, exclude, keepEscaped) {\n  var i, l, code, nextCode, cache,\n      result = '';\n\n  if (typeof exclude !== 'string') {\n    // encode(string, keepEscaped)\n    keepEscaped  = exclude;\n    exclude = encode.defaultChars;\n  }\n\n  if (typeof keepEscaped === 'undefined') {\n    keepEscaped = true;\n  }\n\n  cache = getEncodeCache(exclude);\n\n  for (i = 0, l = string.length; i < l; i++) {\n    code = string.charCodeAt(i);\n\n    if (keepEscaped && code === 0x25 /* % */ && i + 2 < l) {\n      if (/^[0-9a-f]{2}$/i.test(string.slice(i + 1, i + 3))) {\n        result += string.slice(i, i + 3);\n        i += 2;\n        continue;\n      }\n    }\n\n    if (code < 128) {\n      result += cache[code];\n      continue;\n    }\n\n    if (code >= 0xD800 && code <= 0xDFFF) {\n      if (code >= 0xD800 && code <= 0xDBFF && i + 1 < l) {\n        nextCode = string.charCodeAt(i + 1);\n        if (nextCode >= 0xDC00 && nextCode <= 0xDFFF) {\n          result += encodeURIComponent(string[i] + string[i + 1]);\n          i++;\n          continue;\n        }\n      }\n      result += '%EF%BF%BD';\n      continue;\n    }\n\n    result += encodeURIComponent(string[i]);\n  }\n\n  return result;\n}\n\nencode.defaultChars   = \";/?:@&=+$,-_.!~*'()#\";\nencode.componentChars = \"-_.!~*'()\";\n\n\nmodule.exports = encode;\n", "\n'use strict';\n\n\n/* eslint-disable no-bitwise */\n\nvar decodeCache = {};\n\nfunction getDecodeCache(exclude) {\n  var i, ch, cache = decodeCache[exclude];\n  if (cache) { return cache; }\n\n  cache = decodeCache[exclude] = [];\n\n  for (i = 0; i < 128; i++) {\n    ch = String.fromCharCode(i);\n    cache.push(ch);\n  }\n\n  for (i = 0; i < exclude.length; i++) {\n    ch = exclude.charCodeAt(i);\n    cache[ch] = '%' + ('0' + ch.toString(16).toUpperCase()).slice(-2);\n  }\n\n  return cache;\n}\n\n\n// Decode percent-encoded string.\n//\nfunction decode(string, exclude) {\n  var cache;\n\n  if (typeof exclude !== 'string') {\n    exclude = decode.defaultChars;\n  }\n\n  cache = getDecodeCache(exclude);\n\n  return string.replace(/(%[a-f0-9]{2})+/gi, function(seq) {\n    var i, l, b1, b2, b3, b4, chr,\n        result = '';\n\n    for (i = 0, l = seq.length; i < l; i += 3) {\n      b1 = parseInt(seq.slice(i + 1, i + 3), 16);\n\n      if (b1 < 0x80) {\n        result += cache[b1];\n        continue;\n      }\n\n      if ((b1 & 0xE0) === 0xC0 && (i + 3 < l)) {\n        // 110xxxxx 10xxxxxx\n        b2 = parseInt(seq.slice(i + 4, i + 6), 16);\n\n        if ((b2 & 0xC0) === 0x80) {\n          chr = ((b1 << 6) & 0x7C0) | (b2 & 0x3F);\n\n          if (chr < 0x80) {\n            result += '\\ufffd\\ufffd';\n          } else {\n            result += String.fromCharCode(chr);\n          }\n\n          i += 3;\n          continue;\n        }\n      }\n\n      if ((b1 & 0xF0) === 0xE0 && (i + 6 < l)) {\n        // 1110xxxx 10xxxxxx 10xxxxxx\n        b2 = parseInt(seq.slice(i + 4, i + 6), 16);\n        b3 = parseInt(seq.slice(i + 7, i + 9), 16);\n\n        if ((b2 & 0xC0) === 0x80 && (b3 & 0xC0) === 0x80) {\n          chr = ((b1 << 12) & 0xF000) | ((b2 << 6) & 0xFC0) | (b3 & 0x3F);\n\n          if (chr < 0x800 || (chr >= 0xD800 && chr <= 0xDFFF)) {\n            result += '\\ufffd\\ufffd\\ufffd';\n          } else {\n            result += String.fromCharCode(chr);\n          }\n\n          i += 6;\n          continue;\n        }\n      }\n\n      if ((b1 & 0xF8) === 0xF0 && (i + 9 < l)) {\n        // 111110xx 10xxxxxx 10xxxxxx 10xxxxxx\n        b2 = parseInt(seq.slice(i + 4, i + 6), 16);\n        b3 = parseInt(seq.slice(i + 7, i + 9), 16);\n        b4 = parseInt(seq.slice(i + 10, i + 12), 16);\n\n        if ((b2 & 0xC0) === 0x80 && (b3 & 0xC0) === 0x80 && (b4 & 0xC0) === 0x80) {\n          chr = ((b1 << 18) & 0x1C0000) | ((b2 << 12) & 0x3F000) | ((b3 << 6) & 0xFC0) | (b4 & 0x3F);\n\n          if (chr < 0x10000 || chr > 0x10FFFF) {\n            result += '\\ufffd\\ufffd\\ufffd\\ufffd';\n          } else {\n            chr -= 0x10000;\n            result += String.fromCharCode(0xD800 + (chr >> 10), 0xDC00 + (chr & 0x3FF));\n          }\n\n          i += 9;\n          continue;\n        }\n      }\n\n      result += '\\ufffd';\n    }\n\n    return result;\n  });\n}\n\n\ndecode.defaultChars   = ';/?:@&=+$,#';\ndecode.componentChars = '';\n\n\nmodule.exports = decode;\n", "\n'use strict';\n\n\nmodule.exports = function format(url) {\n  var result = '';\n\n  result += url.protocol || '';\n  result += url.slashes ? '//' : '';\n  result += url.auth ? url.auth + '@' : '';\n\n  if (url.hostname && url.hostname.indexOf(':') !== -1) {\n    // ipv6 address\n    result += '[' + url.hostname + ']';\n  } else {\n    result += url.hostname || '';\n  }\n\n  result += url.port ? ':' + url.port : '';\n  result += url.pathname || '';\n  result += url.search || '';\n  result += url.hash || '';\n\n  return result;\n};\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\n//\n// Changes from joyent/node:\n//\n// 1. No leading slash in paths,\n//    e.g. in `url.parse('http://foo?bar')` pathname is ``, not `/`\n//\n// 2. Backslashes are not replaced with slashes,\n//    so `http:\\\\example.org\\` is treated like a relative path\n//\n// 3. Trailing colon is treated like a part of the path,\n//    i.e. in `http://example.org:foo` pathname is `:foo`\n//\n// 4. Nothing is URL-encoded in the resulting object,\n//    (in joyent/node some chars in auth and paths are encoded)\n//\n// 5. `url.parse()` does not have `parseQueryString` argument\n//\n// 6. Removed extraneous result properties: `host`, `path`, `query`, etc.,\n//    which can be constructed using other parts of the url.\n//\n\n\nfunction Url() {\n  this.protocol = null;\n  this.slashes = null;\n  this.auth = null;\n  this.port = null;\n  this.hostname = null;\n  this.hash = null;\n  this.search = null;\n  this.pathname = null;\n}\n\n// Reference: RFC 3986, RFC 1808, RFC 2396\n\n// define these here so at least they only have to be\n// compiled once on the first module load.\nvar protocolPattern = /^([a-z0-9.+-]+:)/i,\n    portPattern = /:[0-9]*$/,\n\n    // Special case for a simple path URL\n    simplePathPattern = /^(\\/\\/?(?!\\/)[^\\?\\s]*)(\\?[^\\s]*)?$/,\n\n    // RFC 2396: characters reserved for delimiting URLs.\n    // We actually just auto-escape these.\n    delims = [ '<', '>', '\"', '`', ' ', '\\r', '\\n', '\\t' ],\n\n    // RFC 2396: characters not allowed for various reasons.\n    unwise = [ '{', '}', '|', '\\\\', '^', '`' ].concat(delims),\n\n    // Allowed by RFCs, but cause of XSS attacks.  Always escape these.\n    autoEscape = [ '\\'' ].concat(unwise),\n    // Characters that are never ever allowed in a hostname.\n    // Note that any invalid chars are also handled, but these\n    // are the ones that are *expected* to be seen, so we fast-path\n    // them.\n    nonHostChars = [ '%', '/', '?', ';', '#' ].concat(autoEscape),\n    hostEndingChars = [ '/', '?', '#' ],\n    hostnameMaxLen = 255,\n    hostnamePartPattern = /^[+a-z0-9A-Z_-]{0,63}$/,\n    hostnamePartStart = /^([+a-z0-9A-Z_-]{0,63})(.*)$/,\n    // protocols that can allow \"unsafe\" and \"unwise\" chars.\n    /* eslint-disable no-script-url */\n    // protocols that never have a hostname.\n    hostlessProtocol = {\n      'javascript': true,\n      'javascript:': true\n    },\n    // protocols that always contain a // bit.\n    slashedProtocol = {\n      'http': true,\n      'https': true,\n      'ftp': true,\n      'gopher': true,\n      'file': true,\n      'http:': true,\n      'https:': true,\n      'ftp:': true,\n      'gopher:': true,\n      'file:': true\n    };\n    /* eslint-enable no-script-url */\n\nfunction urlParse(url, slashesDenoteHost) {\n  if (url && url instanceof Url) { return url; }\n\n  var u = new Url();\n  u.parse(url, slashesDenoteHost);\n  return u;\n}\n\nUrl.prototype.parse = function(url, slashesDenoteHost) {\n  var i, l, lowerProto, hec, slashes,\n      rest = url;\n\n  // trim before proceeding.\n  // This is to support parse stuff like \"  http://foo.com  \\n\"\n  rest = rest.trim();\n\n  if (!slashesDenoteHost && url.split('#').length === 1) {\n    // Try fast path regexp\n    var simplePath = simplePathPattern.exec(rest);\n    if (simplePath) {\n      this.pathname = simplePath[1];\n      if (simplePath[2]) {\n        this.search = simplePath[2];\n      }\n      return this;\n    }\n  }\n\n  var proto = protocolPattern.exec(rest);\n  if (proto) {\n    proto = proto[0];\n    lowerProto = proto.toLowerCase();\n    this.protocol = proto;\n    rest = rest.substr(proto.length);\n  }\n\n  // figure out if it's got a host\n  // user@server is *always* interpreted as a hostname, and url\n  // resolution will treat //foo/bar as host=foo,path=bar because that's\n  // how the browser resolves relative URLs.\n  if (slashesDenoteHost || proto || rest.match(/^\\/\\/[^@\\/]+@[^@\\/]+/)) {\n    slashes = rest.substr(0, 2) === '//';\n    if (slashes && !(proto && hostlessProtocol[proto])) {\n      rest = rest.substr(2);\n      this.slashes = true;\n    }\n  }\n\n  if (!hostlessProtocol[proto] &&\n      (slashes || (proto && !slashedProtocol[proto]))) {\n\n    // there's a hostname.\n    // the first instance of /, ?, ;, or # ends the host.\n    //\n    // If there is an @ in the hostname, then non-host chars *are* allowed\n    // to the left of the last @ sign, unless some host-ending character\n    // comes *before* the @-sign.\n    // URLs are obnoxious.\n    //\n    // ex:\n    // http://a@b@c/ => user:a@b host:c\n    // http://a@b?@c => user:a host:c path:/?@c\n\n    // v0.12 TODO(isaacs): This is not quite how Chrome does things.\n    // Review our test case against browsers more comprehensively.\n\n    // find the first instance of any hostEndingChars\n    var hostEnd = -1;\n    for (i = 0; i < hostEndingChars.length; i++) {\n      hec = rest.indexOf(hostEndingChars[i]);\n      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd)) {\n        hostEnd = hec;\n      }\n    }\n\n    // at this point, either we have an explicit point where the\n    // auth portion cannot go past, or the last @ char is the decider.\n    var auth, atSign;\n    if (hostEnd === -1) {\n      // atSign can be anywhere.\n      atSign = rest.lastIndexOf('@');\n    } else {\n      // atSign must be in auth portion.\n      // http://a@b/c@d => host:b auth:a path:/c@d\n      atSign = rest.lastIndexOf('@', hostEnd);\n    }\n\n    // Now we have a portion which is definitely the auth.\n    // Pull that off.\n    if (atSign !== -1) {\n      auth = rest.slice(0, atSign);\n      rest = rest.slice(atSign + 1);\n      this.auth = auth;\n    }\n\n    // the host is the remaining to the left of the first non-host char\n    hostEnd = -1;\n    for (i = 0; i < nonHostChars.length; i++) {\n      hec = rest.indexOf(nonHostChars[i]);\n      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd)) {\n        hostEnd = hec;\n      }\n    }\n    // if we still have not hit it, then the entire thing is a host.\n    if (hostEnd === -1) {\n      hostEnd = rest.length;\n    }\n\n    if (rest[hostEnd - 1] === ':') { hostEnd--; }\n    var host = rest.slice(0, hostEnd);\n    rest = rest.slice(hostEnd);\n\n    // pull out port.\n    this.parseHost(host);\n\n    // we've indicated that there is a hostname,\n    // so even if it's empty, it has to be present.\n    this.hostname = this.hostname || '';\n\n    // if hostname begins with [ and ends with ]\n    // assume that it's an IPv6 address.\n    var ipv6Hostname = this.hostname[0] === '[' &&\n        this.hostname[this.hostname.length - 1] === ']';\n\n    // validate a little.\n    if (!ipv6Hostname) {\n      var hostparts = this.hostname.split(/\\./);\n      for (i = 0, l = hostparts.length; i < l; i++) {\n        var part = hostparts[i];\n        if (!part) { continue; }\n        if (!part.match(hostnamePartPattern)) {\n          var newpart = '';\n          for (var j = 0, k = part.length; j < k; j++) {\n            if (part.charCodeAt(j) > 127) {\n              // we replace non-ASCII char with a temporary placeholder\n              // we need this to make sure size of hostname is not\n              // broken by replacing non-ASCII by nothing\n              newpart += 'x';\n            } else {\n              newpart += part[j];\n            }\n          }\n          // we test again with ASCII char only\n          if (!newpart.match(hostnamePartPattern)) {\n            var validParts = hostparts.slice(0, i);\n            var notHost = hostparts.slice(i + 1);\n            var bit = part.match(hostnamePartStart);\n            if (bit) {\n              validParts.push(bit[1]);\n              notHost.unshift(bit[2]);\n            }\n            if (notHost.length) {\n              rest = notHost.join('.') + rest;\n            }\n            this.hostname = validParts.join('.');\n            break;\n          }\n        }\n      }\n    }\n\n    if (this.hostname.length > hostnameMaxLen) {\n      this.hostname = '';\n    }\n\n    // strip [ and ] from the hostname\n    // the host field still retains them, though\n    if (ipv6Hostname) {\n      this.hostname = this.hostname.substr(1, this.hostname.length - 2);\n    }\n  }\n\n  // chop off from the tail first.\n  var hash = rest.indexOf('#');\n  if (hash !== -1) {\n    // got a fragment string.\n    this.hash = rest.substr(hash);\n    rest = rest.slice(0, hash);\n  }\n  var qm = rest.indexOf('?');\n  if (qm !== -1) {\n    this.search = rest.substr(qm);\n    rest = rest.slice(0, qm);\n  }\n  if (rest) { this.pathname = rest; }\n  if (slashedProtocol[lowerProto] &&\n      this.hostname && !this.pathname) {\n    this.pathname = '';\n  }\n\n  return this;\n};\n\nUrl.prototype.parseHost = function(host) {\n  var port = portPattern.exec(host);\n  if (port) {\n    port = port[0];\n    if (port !== ':') {\n      this.port = port.substr(1);\n    }\n    host = host.substr(0, host.length - port.length);\n  }\n  if (host) { this.hostname = host; }\n};\n\nmodule.exports = urlParse;\n", "'use strict';\n\n\nmodule.exports.encode = require('./encode');\nmodule.exports.decode = require('./decode');\nmodule.exports.format = require('./format');\nmodule.exports.parse  = require('./parse');\n", "module.exports=/[\\0-\\uD7FF\\uE000-\\uFFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]/", "module.exports=/[\\0-\\x1F\\x7F-\\x9F]/", "module.exports=/[\\xAD\\u0600-\\u0605\\u061C\\u06DD\\u070F\\u08E2\\u180E\\u200B-\\u200F\\u202A-\\u202E\\u2060-\\u2064\\u2066-\\u206F\\uFEFF\\uFFF9-\\uFFFB]|\\uD804[\\uDCBD\\uDCCD]|\\uD82F[\\uDCA0-\\uDCA3]|\\uD834[\\uDD73-\\uDD7A]|\\uDB40[\\uDC01\\uDC20-\\uDC7F]/", "module.exports=/[ \\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000]/", "'use strict';\n\nexports.Any = require('./properties/Any/regex');\nexports.Cc  = require('./categories/Cc/regex');\nexports.Cf  = require('./categories/Cf/regex');\nexports.P   = require('./categories/P/regex');\nexports.Z   = require('./categories/Z/regex');\n", "// Utilities\n//\n'use strict';\n\n\nfunction _class(obj) { return Object.prototype.toString.call(obj); }\n\nfunction isString(obj) { return _class(obj) === '[object String]'; }\n\nvar _hasOwnProperty = Object.prototype.hasOwnProperty;\n\nfunction has(object, key) {\n  return _hasOwnProperty.call(object, key);\n}\n\n// Merge objects\n//\nfunction assign(obj /*from1, from2, from3, ...*/) {\n  var sources = Array.prototype.slice.call(arguments, 1);\n\n  sources.forEach(function (source) {\n    if (!source) { return; }\n\n    if (typeof source !== 'object') {\n      throw new TypeError(source + 'must be object');\n    }\n\n    Object.keys(source).forEach(function (key) {\n      obj[key] = source[key];\n    });\n  });\n\n  return obj;\n}\n\n// Remove element from array and put another array at those position.\n// Useful for some operations with tokens\nfunction arrayReplaceAt(src, pos, newElements) {\n  return [].concat(src.slice(0, pos), newElements, src.slice(pos + 1));\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\nfunction isValidEntityCode(c) {\n  /*eslint no-bitwise:0*/\n  // broken sequence\n  if (c >= 0xD800 && c <= 0xDFFF) { return false; }\n  // never used\n  if (c >= 0xFDD0 && c <= 0xFDEF) { return false; }\n  if ((c & 0xFFFF) === 0xFFFF || (c & 0xFFFF) === 0xFFFE) { return false; }\n  // control codes\n  if (c >= 0x00 && c <= 0x08) { return false; }\n  if (c === 0x0B) { return false; }\n  if (c >= 0x0E && c <= 0x1F) { return false; }\n  if (c >= 0x7F && c <= 0x9F) { return false; }\n  // out of range\n  if (c > 0x10FFFF) { return false; }\n  return true;\n}\n\nfunction fromCodePoint(c) {\n  /*eslint no-bitwise:0*/\n  if (c > 0xffff) {\n    c -= 0x10000;\n    var surrogate1 = 0xd800 + (c >> 10),\n        surrogate2 = 0xdc00 + (c & 0x3ff);\n\n    return String.fromCharCode(surrogate1, surrogate2);\n  }\n  return String.fromCharCode(c);\n}\n\n\nvar UNESCAPE_MD_RE  = /\\\\([!\"#$%&'()*+,\\-.\\/:;<=>?@[\\\\\\]^_`{|}~])/g;\nvar ENTITY_RE       = /&([a-z#][a-z0-9]{1,31});/gi;\nvar UNESCAPE_ALL_RE = new RegExp(UNESCAPE_MD_RE.source + '|' + ENTITY_RE.source, 'gi');\n\nvar DIGITAL_ENTITY_TEST_RE = /^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))/i;\n\nvar entities = require('./entities');\n\nfunction replaceEntityPattern(match, name) {\n  var code = 0;\n\n  if (has(entities, name)) {\n    return entities[name];\n  }\n\n  if (name.charCodeAt(0) === 0x23/* # */ && DIGITAL_ENTITY_TEST_RE.test(name)) {\n    code = name[1].toLowerCase() === 'x' ?\n      parseInt(name.slice(2), 16) : parseInt(name.slice(1), 10);\n\n    if (isValidEntityCode(code)) {\n      return fromCodePoint(code);\n    }\n  }\n\n  return match;\n}\n\n/*function replaceEntities(str) {\n  if (str.indexOf('&') < 0) { return str; }\n\n  return str.replace(ENTITY_RE, replaceEntityPattern);\n}*/\n\nfunction unescapeMd(str) {\n  if (str.indexOf('\\\\') < 0) { return str; }\n  return str.replace(UNESCAPE_MD_RE, '$1');\n}\n\nfunction unescapeAll(str) {\n  if (str.indexOf('\\\\') < 0 && str.indexOf('&') < 0) { return str; }\n\n  return str.replace(UNESCAPE_ALL_RE, function (match, escaped, entity) {\n    if (escaped) { return escaped; }\n    return replaceEntityPattern(match, entity);\n  });\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\nvar HTML_ESCAPE_TEST_RE = /[&<>\"]/;\nvar HTML_ESCAPE_REPLACE_RE = /[&<>\"]/g;\nvar HTML_REPLACEMENTS = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;'\n};\n\nfunction replaceUnsafeChar(ch) {\n  return HTML_REPLACEMENTS[ch];\n}\n\nfunction escapeHtml(str) {\n  if (HTML_ESCAPE_TEST_RE.test(str)) {\n    return str.replace(HTML_ESCAPE_REPLACE_RE, replaceUnsafeChar);\n  }\n  return str;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\nvar REGEXP_ESCAPE_RE = /[.?*+^$[\\]\\\\(){}|-]/g;\n\nfunction escapeRE(str) {\n  return str.replace(REGEXP_ESCAPE_RE, '\\\\$&');\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\nfunction isSpace(code) {\n  switch (code) {\n    case 0x09:\n    case 0x20:\n      return true;\n  }\n  return false;\n}\n\n// Zs (unicode class) || [\\t\\f\\v\\r\\n]\nfunction isWhiteSpace(code) {\n  if (code >= 0x2000 && code <= 0x200A) { return true; }\n  switch (code) {\n    case 0x09: // \\t\n    case 0x0A: // \\n\n    case 0x0B: // \\v\n    case 0x0C: // \\f\n    case 0x0D: // \\r\n    case 0x20:\n    case 0xA0:\n    case 0x1680:\n    case 0x202F:\n    case 0x205F:\n    case 0x3000:\n      return true;\n  }\n  return false;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\n/*eslint-disable max-len*/\nvar UNICODE_PUNCT_RE = require('uc.micro/categories/P/regex');\n\n// Currently without astral characters support.\nfunction isPunctChar(ch) {\n  return UNICODE_PUNCT_RE.test(ch);\n}\n\n\n// Markdown ASCII punctuation characters.\n//\n// !, \", #, $, %, &, ', (, ), *, +, ,, -, ., /, :, ;, <, =, >, ?, @, [, \\, ], ^, _, `, {, |, }, or ~\n// http://spec.commonmark.org/0.15/#ascii-punctuation-character\n//\n// Don't confuse with unicode punctuation !!! It lacks some chars in ascii range.\n//\nfunction isMdAsciiPunct(ch) {\n  switch (ch) {\n    case 0x21/* ! */:\n    case 0x22/* \" */:\n    case 0x23/* # */:\n    case 0x24/* $ */:\n    case 0x25/* % */:\n    case 0x26/* & */:\n    case 0x27/* ' */:\n    case 0x28/* ( */:\n    case 0x29/* ) */:\n    case 0x2A/* * */:\n    case 0x2B/* + */:\n    case 0x2C/* , */:\n    case 0x2D/* - */:\n    case 0x2E/* . */:\n    case 0x2F/* / */:\n    case 0x3A/* : */:\n    case 0x3B/* ; */:\n    case 0x3C/* < */:\n    case 0x3D/* = */:\n    case 0x3E/* > */:\n    case 0x3F/* ? */:\n    case 0x40/* @ */:\n    case 0x5B/* [ */:\n    case 0x5C/* \\ */:\n    case 0x5D/* ] */:\n    case 0x5E/* ^ */:\n    case 0x5F/* _ */:\n    case 0x60/* ` */:\n    case 0x7B/* { */:\n    case 0x7C/* | */:\n    case 0x7D/* } */:\n    case 0x7E/* ~ */:\n      return true;\n    default:\n      return false;\n  }\n}\n\n// Hepler to unify [reference labels].\n//\nfunction normalizeReference(str) {\n  // Trim and collapse whitespace\n  //\n  str = str.trim().replace(/\\s+/g, ' ');\n\n  // In node v10 'ẞ'.toLowerCase() === 'Ṿ', which is presumed to be a bug\n  // fixed in v12 (couldn't find any details).\n  //\n  // So treat this one as a special case\n  // (remove this when node v10 is no longer supported).\n  //\n  if ('ẞ'.toLowerCase() === 'Ṿ') {\n    str = str.replace(/ẞ/g, 'ß');\n  }\n\n  // .toLowerCase().toUpperCase() should get rid of all differences\n  // between letter variants.\n  //\n  // Simple .toLowerCase() doesn't normalize 125 code points correctly,\n  // and .toUpperCase doesn't normalize 6 of them (list of exceptions:\n  // İ, ϴ, ẞ, Ω, K, Å - those are already uppercased, but have differently\n  // uppercased versions).\n  //\n  // Here's an example showing how it happens. Lets take greek letter omega:\n  // uppercase U+0398 (Θ), U+03f4 (ϴ) and lowercase U+03b8 (θ), U+03d1 (ϑ)\n  //\n  // Unicode entries:\n  // 0398;GREEK CAPITAL LETTER THETA;Lu;0;L;;;;;N;;;;03B8;\n  // 03B8;GREEK SMALL LETTER THETA;Ll;0;L;;;;;N;;;0398;;0398\n  // 03D1;GREEK THETA SYMBOL;Ll;0;L;<compat> 03B8;;;;N;GREEK SMALL LETTER SCRIPT THETA;;0398;;0398\n  // 03F4;GREEK CAPITAL THETA SYMBOL;Lu;0;L;<compat> 0398;;;;N;;;;03B8;\n  //\n  // Case-insensitive comparison should treat all of them as equivalent.\n  //\n  // But .toLowerCase() doesn't change ϑ (it's already lowercase),\n  // and .toUpperCase() doesn't change ϴ (already uppercase).\n  //\n  // Applying first lower then upper case normalizes any character:\n  // '\\u0398\\u03f4\\u03b8\\u03d1'.toLowerCase().toUpperCase() === '\\u0398\\u0398\\u0398\\u0398'\n  //\n  // Note: this is equivalent to unicode case folding; unicode normalization\n  // is a different step that is not required here.\n  //\n  // Final result should be uppercased, because it's later stored in an object\n  // (this avoid a conflict with Object.prototype members,\n  // most notably, `__proto__`)\n  //\n  return str.toLowerCase().toUpperCase();\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\n// Re-export libraries commonly used in both markdown-it and its plugins,\n// so plugins won't have to depend on them explicitly, which reduces their\n// bundled size (e.g. a browser build).\n//\nexports.lib                 = {};\nexports.lib.mdurl           = require('mdurl');\nexports.lib.ucmicro         = require('uc.micro');\n\nexports.assign              = assign;\nexports.isString            = isString;\nexports.has                 = has;\nexports.unescapeMd          = unescapeMd;\nexports.unescapeAll         = unescapeAll;\nexports.isValidEntityCode   = isValidEntityCode;\nexports.fromCodePoint       = fromCodePoint;\n// exports.replaceEntities     = replaceEntities;\nexports.escapeHtml          = escapeHtml;\nexports.arrayReplaceAt      = arrayReplaceAt;\nexports.isSpace             = isSpace;\nexports.isWhiteSpace        = isWhiteSpace;\nexports.isMdAsciiPunct      = isMdAsciiPunct;\nexports.isPunctChar         = isPunctChar;\nexports.escapeRE            = escapeRE;\nexports.normalizeReference  = normalizeReference;\n", "// Parse link label\n//\n// this function assumes that first character (\"[\") already matches;\n// returns the end of the label\n//\n'use strict';\n\nmodule.exports = function parseLinkLabel(state, start, disableNested) {\n  var level, found, marker, prevPos,\n      labelEnd = -1,\n      max = state.posMax,\n      oldPos = state.pos;\n\n  state.pos = start + 1;\n  level = 1;\n\n  while (state.pos < max) {\n    marker = state.src.charCodeAt(state.pos);\n    if (marker === 0x5D /* ] */) {\n      level--;\n      if (level === 0) {\n        found = true;\n        break;\n      }\n    }\n\n    prevPos = state.pos;\n    state.md.inline.skipToken(state);\n    if (marker === 0x5B /* [ */) {\n      if (prevPos === state.pos - 1) {\n        // increase level if we find text `[`, which is not a part of any token\n        level++;\n      } else if (disableNested) {\n        state.pos = oldPos;\n        return -1;\n      }\n    }\n  }\n\n  if (found) {\n    labelEnd = state.pos;\n  }\n\n  // restore old state\n  state.pos = oldPos;\n\n  return labelEnd;\n};\n", "// Parse link destination\n//\n'use strict';\n\n\nvar unescapeAll = require('../common/utils').unescapeAll;\n\n\nmodule.exports = function parseLinkDestination(str, pos, max) {\n  var code, level,\n      lines = 0,\n      start = pos,\n      result = {\n        ok: false,\n        pos: 0,\n        lines: 0,\n        str: ''\n      };\n\n  if (str.charCodeAt(pos) === 0x3C /* < */) {\n    pos++;\n    while (pos < max) {\n      code = str.charCodeAt(pos);\n      if (code === 0x0A /* \\n */) { return result; }\n      if (code === 0x3C /* < */) { return result; }\n      if (code === 0x3E /* > */) {\n        result.pos = pos + 1;\n        result.str = unescapeAll(str.slice(start + 1, pos));\n        result.ok = true;\n        return result;\n      }\n      if (code === 0x5C /* \\ */ && pos + 1 < max) {\n        pos += 2;\n        continue;\n      }\n\n      pos++;\n    }\n\n    // no closing '>'\n    return result;\n  }\n\n  // this should be ... } else { ... branch\n\n  level = 0;\n  while (pos < max) {\n    code = str.charCodeAt(pos);\n\n    if (code === 0x20) { break; }\n\n    // ascii control characters\n    if (code < 0x20 || code === 0x7F) { break; }\n\n    if (code === 0x5C /* \\ */ && pos + 1 < max) {\n      if (str.charCodeAt(pos + 1) === 0x20) { break; }\n      pos += 2;\n      continue;\n    }\n\n    if (code === 0x28 /* ( */) {\n      level++;\n      if (level > 32) { return result; }\n    }\n\n    if (code === 0x29 /* ) */) {\n      if (level === 0) { break; }\n      level--;\n    }\n\n    pos++;\n  }\n\n  if (start === pos) { return result; }\n  if (level !== 0) { return result; }\n\n  result.str = unescapeAll(str.slice(start, pos));\n  result.lines = lines;\n  result.pos = pos;\n  result.ok = true;\n  return result;\n};\n", "// Parse link title\n//\n'use strict';\n\n\nvar unescapeAll = require('../common/utils').unescapeAll;\n\n\nmodule.exports = function parseLinkTitle(str, pos, max) {\n  var code,\n      marker,\n      lines = 0,\n      start = pos,\n      result = {\n        ok: false,\n        pos: 0,\n        lines: 0,\n        str: ''\n      };\n\n  if (pos >= max) { return result; }\n\n  marker = str.charCodeAt(pos);\n\n  if (marker !== 0x22 /* \" */ && marker !== 0x27 /* ' */ && marker !== 0x28 /* ( */) { return result; }\n\n  pos++;\n\n  // if opening marker is \"(\", switch it to closing marker \")\"\n  if (marker === 0x28) { marker = 0x29; }\n\n  while (pos < max) {\n    code = str.charCodeAt(pos);\n    if (code === marker) {\n      result.pos = pos + 1;\n      result.lines = lines;\n      result.str = unescapeAll(str.slice(start + 1, pos));\n      result.ok = true;\n      return result;\n    } else if (code === 0x28 /* ( */ && marker === 0x29 /* ) */) {\n      return result;\n    } else if (code === 0x0A) {\n      lines++;\n    } else if (code === 0x5C /* \\ */ && pos + 1 < max) {\n      pos++;\n      if (str.charCodeAt(pos) === 0x0A) {\n        lines++;\n      }\n    }\n\n    pos++;\n  }\n\n  return result;\n};\n", "// Just a shortcut for bulk export\n'use strict';\n\n\nexports.parseLinkLabel       = require('./parse_link_label');\nexports.parseLinkDestination = require('./parse_link_destination');\nexports.parseLinkTitle       = require('./parse_link_title');\n", "/**\n * class Renderer\n *\n * Generates HTML from parsed token stream. Each instance has independent\n * copy of rules. Those can be rewritten with ease. Also, you can add new\n * rules if you create plugin and adds new token types.\n **/\n'use strict';\n\n\nvar assign          = require('./common/utils').assign;\nvar unescapeAll     = require('./common/utils').unescapeAll;\nvar escapeHtml      = require('./common/utils').escapeHtml;\n\n\n////////////////////////////////////////////////////////////////////////////////\n\nvar default_rules = {};\n\n\ndefault_rules.code_inline = function (tokens, idx, options, env, slf) {\n  var token = tokens[idx];\n\n  return  '<code' + slf.renderAttrs(token) + '>' +\n          escapeHtml(tokens[idx].content) +\n          '</code>';\n};\n\n\ndefault_rules.code_block = function (tokens, idx, options, env, slf) {\n  var token = tokens[idx];\n\n  return  '<pre' + slf.renderAttrs(token) + '><code>' +\n          escapeHtml(tokens[idx].content) +\n          '</code></pre>\\n';\n};\n\n\ndefault_rules.fence = function (tokens, idx, options, env, slf) {\n  var token = tokens[idx],\n      info = token.info ? unescapeAll(token.info).trim() : '',\n      langName = '',\n      langAttrs = '',\n      highlighted, i, arr, tmpAttrs, tmpToken;\n\n  if (info) {\n    arr = info.split(/(\\s+)/g);\n    langName = arr[0];\n    langAttrs = arr.slice(2).join('');\n  }\n\n  if (options.highlight) {\n    highlighted = options.highlight(token.content, langName, langAttrs) || escapeHtml(token.content);\n  } else {\n    highlighted = escapeHtml(token.content);\n  }\n\n  if (highlighted.indexOf('<pre') === 0) {\n    return highlighted + '\\n';\n  }\n\n  // If language exists, inject class gently, without modifying original token.\n  // May be, one day we will add .deepClone() for token and simplify this part, but\n  // now we prefer to keep things local.\n  if (info) {\n    i        = token.attrIndex('class');\n    tmpAttrs = token.attrs ? token.attrs.slice() : [];\n\n    if (i < 0) {\n      tmpAttrs.push([ 'class', options.langPrefix + langName ]);\n    } else {\n      tmpAttrs[i] = tmpAttrs[i].slice();\n      tmpAttrs[i][1] += ' ' + options.langPrefix + langName;\n    }\n\n    // Fake token just to render attributes\n    tmpToken = {\n      attrs: tmpAttrs\n    };\n\n    return  '<pre><code' + slf.renderAttrs(tmpToken) + '>'\n          + highlighted\n          + '</code></pre>\\n';\n  }\n\n\n  return  '<pre><code' + slf.renderAttrs(token) + '>'\n        + highlighted\n        + '</code></pre>\\n';\n};\n\n\ndefault_rules.image = function (tokens, idx, options, env, slf) {\n  var token = tokens[idx];\n\n  // \"alt\" attr MUST be set, even if empty. Because it's mandatory and\n  // should be placed on proper position for tests.\n  //\n  // Replace content with actual value\n\n  token.attrs[token.attrIndex('alt')][1] =\n    slf.renderInlineAsText(token.children, options, env);\n\n  return slf.renderToken(tokens, idx, options);\n};\n\n\ndefault_rules.hardbreak = function (tokens, idx, options /*, env */) {\n  return options.xhtmlOut ? '<br />\\n' : '<br>\\n';\n};\ndefault_rules.softbreak = function (tokens, idx, options /*, env */) {\n  return options.breaks ? (options.xhtmlOut ? '<br />\\n' : '<br>\\n') : '\\n';\n};\n\n\ndefault_rules.text = function (tokens, idx /*, options, env */) {\n  return escapeHtml(tokens[idx].content);\n};\n\n\ndefault_rules.html_block = function (tokens, idx /*, options, env */) {\n  return tokens[idx].content;\n};\ndefault_rules.html_inline = function (tokens, idx /*, options, env */) {\n  return tokens[idx].content;\n};\n\n\n/**\n * new Renderer()\n *\n * Creates new [[Renderer]] instance and fill [[Renderer#rules]] with defaults.\n **/\nfunction Renderer() {\n\n  /**\n   * Renderer#rules -> Object\n   *\n   * Contains render rules for tokens. Can be updated and extended.\n   *\n   * ##### Example\n   *\n   * ```javascript\n   * var md = require('markdown-it')();\n   *\n   * md.renderer.rules.strong_open  = function () { return '<b>'; };\n   * md.renderer.rules.strong_close = function () { return '</b>'; };\n   *\n   * var result = md.renderInline(...);\n   * ```\n   *\n   * Each rule is called as independent static function with fixed signature:\n   *\n   * ```javascript\n   * function my_token_render(tokens, idx, options, env, renderer) {\n   *   // ...\n   *   return renderedHTML;\n   * }\n   * ```\n   *\n   * See [source code](https://github.com/markdown-it/markdown-it/blob/master/lib/renderer.js)\n   * for more details and examples.\n   **/\n  this.rules = assign({}, default_rules);\n}\n\n\n/**\n * Renderer.renderAttrs(token) -> String\n *\n * Render token attributes to string.\n **/\nRenderer.prototype.renderAttrs = function renderAttrs(token) {\n  var i, l, result;\n\n  if (!token.attrs) { return ''; }\n\n  result = '';\n\n  for (i = 0, l = token.attrs.length; i < l; i++) {\n    result += ' ' + escapeHtml(token.attrs[i][0]) + '=\"' + escapeHtml(token.attrs[i][1]) + '\"';\n  }\n\n  return result;\n};\n\n\n/**\n * Renderer.renderToken(tokens, idx, options) -> String\n * - tokens (Array): list of tokens\n * - idx (Numbed): token index to render\n * - options (Object): params of parser instance\n *\n * Default token renderer. Can be overriden by custom function\n * in [[Renderer#rules]].\n **/\nRenderer.prototype.renderToken = function renderToken(tokens, idx, options) {\n  var nextToken,\n      result = '',\n      needLf = false,\n      token = tokens[idx];\n\n  // Tight list paragraphs\n  if (token.hidden) {\n    return '';\n  }\n\n  // Insert a newline between hidden paragraph and subsequent opening\n  // block-level tag.\n  //\n  // For example, here we should insert a newline before blockquote:\n  //  - a\n  //    >\n  //\n  if (token.block && token.nesting !== -1 && idx && tokens[idx - 1].hidden) {\n    result += '\\n';\n  }\n\n  // Add token name, e.g. `<img`\n  result += (token.nesting === -1 ? '</' : '<') + token.tag;\n\n  // Encode attributes, e.g. `<img src=\"foo\"`\n  result += this.renderAttrs(token);\n\n  // Add a slash for self-closing tags, e.g. `<img src=\"foo\" /`\n  if (token.nesting === 0 && options.xhtmlOut) {\n    result += ' /';\n  }\n\n  // Check if we need to add a newline after this tag\n  if (token.block) {\n    needLf = true;\n\n    if (token.nesting === 1) {\n      if (idx + 1 < tokens.length) {\n        nextToken = tokens[idx + 1];\n\n        if (nextToken.type === 'inline' || nextToken.hidden) {\n          // Block-level tag containing an inline tag.\n          //\n          needLf = false;\n\n        } else if (nextToken.nesting === -1 && nextToken.tag === token.tag) {\n          // Opening tag + closing tag of the same type. E.g. `<li></li>`.\n          //\n          needLf = false;\n        }\n      }\n    }\n  }\n\n  result += needLf ? '>\\n' : '>';\n\n  return result;\n};\n\n\n/**\n * Renderer.renderInline(tokens, options, env) -> String\n * - tokens (Array): list on block tokens to render\n * - options (Object): params of parser instance\n * - env (Object): additional data from parsed input (references, for example)\n *\n * The same as [[Renderer.render]], but for single token of `inline` type.\n **/\nRenderer.prototype.renderInline = function (tokens, options, env) {\n  var type,\n      result = '',\n      rules = this.rules;\n\n  for (var i = 0, len = tokens.length; i < len; i++) {\n    type = tokens[i].type;\n\n    if (typeof rules[type] !== 'undefined') {\n      result += rules[type](tokens, i, options, env, this);\n    } else {\n      result += this.renderToken(tokens, i, options);\n    }\n  }\n\n  return result;\n};\n\n\n/** internal\n * Renderer.renderInlineAsText(tokens, options, env) -> String\n * - tokens (Array): list on block tokens to render\n * - options (Object): params of parser instance\n * - env (Object): additional data from parsed input (references, for example)\n *\n * Special kludge for image `alt` attributes to conform CommonMark spec.\n * Don't try to use it! Spec requires to show `alt` content with stripped markup,\n * instead of simple escaping.\n **/\nRenderer.prototype.renderInlineAsText = function (tokens, options, env) {\n  var result = '';\n\n  for (var i = 0, len = tokens.length; i < len; i++) {\n    if (tokens[i].type === 'text') {\n      result += tokens[i].content;\n    } else if (tokens[i].type === 'image') {\n      result += this.renderInlineAsText(tokens[i].children, options, env);\n    } else if (tokens[i].type === 'softbreak') {\n      result += '\\n';\n    }\n  }\n\n  return result;\n};\n\n\n/**\n * Renderer.render(tokens, options, env) -> String\n * - tokens (Array): list on block tokens to render\n * - options (Object): params of parser instance\n * - env (Object): additional data from parsed input (references, for example)\n *\n * Takes token stream and generates HTML. Probably, you will never need to call\n * this method directly.\n **/\nRenderer.prototype.render = function (tokens, options, env) {\n  var i, len, type,\n      result = '',\n      rules = this.rules;\n\n  for (i = 0, len = tokens.length; i < len; i++) {\n    type = tokens[i].type;\n\n    if (type === 'inline') {\n      result += this.renderInline(tokens[i].children, options, env);\n    } else if (typeof rules[type] !== 'undefined') {\n      result += rules[tokens[i].type](tokens, i, options, env, this);\n    } else {\n      result += this.renderToken(tokens, i, options, env);\n    }\n  }\n\n  return result;\n};\n\nmodule.exports = Renderer;\n", "/**\n * class Ruler\n *\n * Helper class, used by [[MarkdownIt#core]], [[MarkdownIt#block]] and\n * [[MarkdownIt#inline]] to manage sequences of functions (rules):\n *\n * - keep rules in defined order\n * - assign the name to each rule\n * - enable/disable rules\n * - add/replace rules\n * - allow assign rules to additional named chains (in the same)\n * - cacheing lists of active rules\n *\n * You will not need use this class directly until write plugins. For simple\n * rules control use [[MarkdownIt.disable]], [[MarkdownIt.enable]] and\n * [[MarkdownIt.use]].\n **/\n'use strict';\n\n\n/**\n * new Ruler()\n **/\nfunction Ruler() {\n  // List of added rules. Each element is:\n  //\n  // {\n  //   name: XXX,\n  //   enabled: Boolean,\n  //   fn: Function(),\n  //   alt: [ name2, name3 ]\n  // }\n  //\n  this.__rules__ = [];\n\n  // Cached rule chains.\n  //\n  // First level - chain name, '' for default.\n  // Second level - diginal anchor for fast filtering by charcodes.\n  //\n  this.__cache__ = null;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n// Helper methods, should not be used directly\n\n\n// Find rule index by name\n//\nRuler.prototype.__find__ = function (name) {\n  for (var i = 0; i < this.__rules__.length; i++) {\n    if (this.__rules__[i].name === name) {\n      return i;\n    }\n  }\n  return -1;\n};\n\n\n// Build rules lookup cache\n//\nRuler.prototype.__compile__ = function () {\n  var self = this;\n  var chains = [ '' ];\n\n  // collect unique names\n  self.__rules__.forEach(function (rule) {\n    if (!rule.enabled) { return; }\n\n    rule.alt.forEach(function (altName) {\n      if (chains.indexOf(altName) < 0) {\n        chains.push(altName);\n      }\n    });\n  });\n\n  self.__cache__ = {};\n\n  chains.forEach(function (chain) {\n    self.__cache__[chain] = [];\n    self.__rules__.forEach(function (rule) {\n      if (!rule.enabled) { return; }\n\n      if (chain && rule.alt.indexOf(chain) < 0) { return; }\n\n      self.__cache__[chain].push(rule.fn);\n    });\n  });\n};\n\n\n/**\n * Ruler.at(name, fn [, options])\n * - name (String): rule name to replace.\n * - fn (Function): new rule function.\n * - options (Object): new rule options (not mandatory).\n *\n * Replace rule by name with new function & options. Throws error if name not\n * found.\n *\n * ##### Options:\n *\n * - __alt__ - array with names of \"alternate\" chains.\n *\n * ##### Example\n *\n * Replace existing typographer replacement rule with new one:\n *\n * ```javascript\n * var md = require('markdown-it')();\n *\n * md.core.ruler.at('replacements', function replace(state) {\n *   //...\n * });\n * ```\n **/\nRuler.prototype.at = function (name, fn, options) {\n  var index = this.__find__(name);\n  var opt = options || {};\n\n  if (index === -1) { throw new Error('Parser rule not found: ' + name); }\n\n  this.__rules__[index].fn = fn;\n  this.__rules__[index].alt = opt.alt || [];\n  this.__cache__ = null;\n};\n\n\n/**\n * Ruler.before(beforeName, ruleName, fn [, options])\n * - beforeName (String): new rule will be added before this one.\n * - ruleName (String): name of added rule.\n * - fn (Function): rule function.\n * - options (Object): rule options (not mandatory).\n *\n * Add new rule to chain before one with given name. See also\n * [[Ruler.after]], [[Ruler.push]].\n *\n * ##### Options:\n *\n * - __alt__ - array with names of \"alternate\" chains.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')();\n *\n * md.block.ruler.before('paragraph', 'my_rule', function replace(state) {\n *   //...\n * });\n * ```\n **/\nRuler.prototype.before = function (beforeName, ruleName, fn, options) {\n  var index = this.__find__(beforeName);\n  var opt = options || {};\n\n  if (index === -1) { throw new Error('Parser rule not found: ' + beforeName); }\n\n  this.__rules__.splice(index, 0, {\n    name: ruleName,\n    enabled: true,\n    fn: fn,\n    alt: opt.alt || []\n  });\n\n  this.__cache__ = null;\n};\n\n\n/**\n * Ruler.after(afterName, ruleName, fn [, options])\n * - afterName (String): new rule will be added after this one.\n * - ruleName (String): name of added rule.\n * - fn (Function): rule function.\n * - options (Object): rule options (not mandatory).\n *\n * Add new rule to chain after one with given name. See also\n * [[Ruler.before]], [[Ruler.push]].\n *\n * ##### Options:\n *\n * - __alt__ - array with names of \"alternate\" chains.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')();\n *\n * md.inline.ruler.after('text', 'my_rule', function replace(state) {\n *   //...\n * });\n * ```\n **/\nRuler.prototype.after = function (afterName, ruleName, fn, options) {\n  var index = this.__find__(afterName);\n  var opt = options || {};\n\n  if (index === -1) { throw new Error('Parser rule not found: ' + afterName); }\n\n  this.__rules__.splice(index + 1, 0, {\n    name: ruleName,\n    enabled: true,\n    fn: fn,\n    alt: opt.alt || []\n  });\n\n  this.__cache__ = null;\n};\n\n/**\n * Ruler.push(ruleName, fn [, options])\n * - ruleName (String): name of added rule.\n * - fn (Function): rule function.\n * - options (Object): rule options (not mandatory).\n *\n * Push new rule to the end of chain. See also\n * [[Ruler.before]], [[Ruler.after]].\n *\n * ##### Options:\n *\n * - __alt__ - array with names of \"alternate\" chains.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')();\n *\n * md.core.ruler.push('my_rule', function replace(state) {\n *   //...\n * });\n * ```\n **/\nRuler.prototype.push = function (ruleName, fn, options) {\n  var opt = options || {};\n\n  this.__rules__.push({\n    name: ruleName,\n    enabled: true,\n    fn: fn,\n    alt: opt.alt || []\n  });\n\n  this.__cache__ = null;\n};\n\n\n/**\n * Ruler.enable(list [, ignoreInvalid]) -> Array\n * - list (String|Array): list of rule names to enable.\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * Enable rules with given names. If any rule name not found - throw Error.\n * Errors can be disabled by second param.\n *\n * Returns list of found rule names (if no exception happened).\n *\n * See also [[Ruler.disable]], [[Ruler.enableOnly]].\n **/\nRuler.prototype.enable = function (list, ignoreInvalid) {\n  if (!Array.isArray(list)) { list = [ list ]; }\n\n  var result = [];\n\n  // Search by name and enable\n  list.forEach(function (name) {\n    var idx = this.__find__(name);\n\n    if (idx < 0) {\n      if (ignoreInvalid) { return; }\n      throw new Error('Rules manager: invalid rule name ' + name);\n    }\n    this.__rules__[idx].enabled = true;\n    result.push(name);\n  }, this);\n\n  this.__cache__ = null;\n  return result;\n};\n\n\n/**\n * Ruler.enableOnly(list [, ignoreInvalid])\n * - list (String|Array): list of rule names to enable (whitelist).\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * Enable rules with given names, and disable everything else. If any rule name\n * not found - throw Error. Errors can be disabled by second param.\n *\n * See also [[Ruler.disable]], [[Ruler.enable]].\n **/\nRuler.prototype.enableOnly = function (list, ignoreInvalid) {\n  if (!Array.isArray(list)) { list = [ list ]; }\n\n  this.__rules__.forEach(function (rule) { rule.enabled = false; });\n\n  this.enable(list, ignoreInvalid);\n};\n\n\n/**\n * Ruler.disable(list [, ignoreInvalid]) -> Array\n * - list (String|Array): list of rule names to disable.\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * Disable rules with given names. If any rule name not found - throw Error.\n * Errors can be disabled by second param.\n *\n * Returns list of found rule names (if no exception happened).\n *\n * See also [[Ruler.enable]], [[Ruler.enableOnly]].\n **/\nRuler.prototype.disable = function (list, ignoreInvalid) {\n  if (!Array.isArray(list)) { list = [ list ]; }\n\n  var result = [];\n\n  // Search by name and disable\n  list.forEach(function (name) {\n    var idx = this.__find__(name);\n\n    if (idx < 0) {\n      if (ignoreInvalid) { return; }\n      throw new Error('Rules manager: invalid rule name ' + name);\n    }\n    this.__rules__[idx].enabled = false;\n    result.push(name);\n  }, this);\n\n  this.__cache__ = null;\n  return result;\n};\n\n\n/**\n * Ruler.getRules(chainName) -> Array\n *\n * Return array of active functions (rules) for given chain name. It analyzes\n * rules configuration, compiles caches if not exists and returns result.\n *\n * Default chain name is `''` (empty string). It can't be skipped. That's\n * done intentionally, to keep signature monomorphic for high speed.\n **/\nRuler.prototype.getRules = function (chainName) {\n  if (this.__cache__ === null) {\n    this.__compile__();\n  }\n\n  // Chain can be empty, if rules disabled. But we still have to return Array.\n  return this.__cache__[chainName] || [];\n};\n\nmodule.exports = Ruler;\n", "// Normalize input string\n\n'use strict';\n\n\n// https://spec.commonmark.org/0.29/#line-ending\nvar NEWLINES_RE  = /\\r\\n?|\\n/g;\nvar NULL_RE      = /\\0/g;\n\n\nmodule.exports = function normalize(state) {\n  var str;\n\n  // Normalize newlines\n  str = state.src.replace(NEWLINES_RE, '\\n');\n\n  // Replace NULL characters\n  str = str.replace(NULL_RE, '\\uFFFD');\n\n  state.src = str;\n};\n", "'use strict';\n\n\nmodule.exports = function block(state) {\n  var token;\n\n  if (state.inlineMode) {\n    token          = new state.Token('inline', '', 0);\n    token.content  = state.src;\n    token.map      = [ 0, 1 ];\n    token.children = [];\n    state.tokens.push(token);\n  } else {\n    state.md.block.parse(state.src, state.md, state.env, state.tokens);\n  }\n};\n", "'use strict';\n\nmodule.exports = function inline(state) {\n  var tokens = state.tokens, tok, i, l;\n\n  // Parse inlines\n  for (i = 0, l = tokens.length; i < l; i++) {\n    tok = tokens[i];\n    if (tok.type === 'inline') {\n      state.md.inline.parse(tok.content, state.md, state.env, tok.children);\n    }\n  }\n};\n", "// Replace link-like texts with link nodes.\n//\n// Currently restricted by `md.validateLink()` to http/https/ftp\n//\n'use strict';\n\n\nvar arrayReplaceAt = require('../common/utils').arrayReplaceAt;\n\n\nfunction isLinkOpen(str) {\n  return /^<a[>\\s]/i.test(str);\n}\nfunction isLinkClose(str) {\n  return /^<\\/a\\s*>/i.test(str);\n}\n\n\nmodule.exports = function linkify(state) {\n  var i, j, l, tokens, token, currentToken, nodes, ln, text, pos, lastPos,\n      level, htmlLinkLevel, url, fullUrl, urlText,\n      blockTokens = state.tokens,\n      links;\n\n  if (!state.md.options.linkify) { return; }\n\n  for (j = 0, l = blockTokens.length; j < l; j++) {\n    if (blockTokens[j].type !== 'inline' ||\n        !state.md.linkify.pretest(blockTokens[j].content)) {\n      continue;\n    }\n\n    tokens = blockTokens[j].children;\n\n    htmlLinkLevel = 0;\n\n    // We scan from the end, to keep position when new tags added.\n    // Use reversed logic in links start/end match\n    for (i = tokens.length - 1; i >= 0; i--) {\n      currentToken = tokens[i];\n\n      // Skip content of markdown links\n      if (currentToken.type === 'link_close') {\n        i--;\n        while (tokens[i].level !== currentToken.level && tokens[i].type !== 'link_open') {\n          i--;\n        }\n        continue;\n      }\n\n      // Skip content of html tag links\n      if (currentToken.type === 'html_inline') {\n        if (isLinkOpen(currentToken.content) && htmlLinkLevel > 0) {\n          htmlLinkLevel--;\n        }\n        if (isLinkClose(currentToken.content)) {\n          htmlLinkLevel++;\n        }\n      }\n      if (htmlLinkLevel > 0) { continue; }\n\n      if (currentToken.type === 'text' && state.md.linkify.test(currentToken.content)) {\n\n        text = currentToken.content;\n        links = state.md.linkify.match(text);\n\n        // Now split string to nodes\n        nodes = [];\n        level = currentToken.level;\n        lastPos = 0;\n\n        for (ln = 0; ln < links.length; ln++) {\n\n          url = links[ln].url;\n          fullUrl = state.md.normalizeLink(url);\n          if (!state.md.validateLink(fullUrl)) { continue; }\n\n          urlText = links[ln].text;\n\n          // Linkifier might send raw hostnames like \"example.com\", where url\n          // starts with domain name. So we prepend http:// in those cases,\n          // and remove it afterwards.\n          //\n          if (!links[ln].schema) {\n            urlText = state.md.normalizeLinkText('http://' + urlText).replace(/^http:\\/\\//, '');\n          } else if (links[ln].schema === 'mailto:' && !/^mailto:/i.test(urlText)) {\n            urlText = state.md.normalizeLinkText('mailto:' + urlText).replace(/^mailto:/, '');\n          } else {\n            urlText = state.md.normalizeLinkText(urlText);\n          }\n\n          pos = links[ln].index;\n\n          if (pos > lastPos) {\n            token         = new state.Token('text', '', 0);\n            token.content = text.slice(lastPos, pos);\n            token.level   = level;\n            nodes.push(token);\n          }\n\n          token         = new state.Token('link_open', 'a', 1);\n          token.attrs   = [ [ 'href', fullUrl ] ];\n          token.level   = level++;\n          token.markup  = 'linkify';\n          token.info    = 'auto';\n          nodes.push(token);\n\n          token         = new state.Token('text', '', 0);\n          token.content = urlText;\n          token.level   = level;\n          nodes.push(token);\n\n          token         = new state.Token('link_close', 'a', -1);\n          token.level   = --level;\n          token.markup  = 'linkify';\n          token.info    = 'auto';\n          nodes.push(token);\n\n          lastPos = links[ln].lastIndex;\n        }\n        if (lastPos < text.length) {\n          token         = new state.Token('text', '', 0);\n          token.content = text.slice(lastPos);\n          token.level   = level;\n          nodes.push(token);\n        }\n\n        // replace current node\n        blockTokens[j].children = tokens = arrayReplaceAt(tokens, i, nodes);\n      }\n    }\n  }\n};\n", "// Simple typographic replacements\n//\n// (c) (C) → ©\n// (tm) (TM) → ™\n// (r) (R) → ®\n// +- → ±\n// (p) (P) -> §\n// ... → … (also ?.... → ?.., !.... → !..)\n// ???????? → ???, !!!!! → !!!, `,,` → `,`\n// -- → &ndash;, --- → &mdash;\n//\n'use strict';\n\n// TODO:\n// - fractionals 1/2, 1/4, 3/4 -> ½, ¼, ¾\n// - miltiplication 2 x 4 -> 2 × 4\n\nvar RARE_RE = /\\+-|\\.\\.|\\?\\?\\?\\?|!!!!|,,|--/;\n\n// Workaround for phantomjs - need regex without /g flag,\n// or root check will fail every second time\nvar SCOPED_ABBR_TEST_RE = /\\((c|tm|r|p)\\)/i;\n\nvar SCOPED_ABBR_RE = /\\((c|tm|r|p)\\)/ig;\nvar SCOPED_ABBR = {\n  c: '©',\n  r: '®',\n  p: '§',\n  tm: '™'\n};\n\nfunction replaceFn(match, name) {\n  return SCOPED_ABBR[name.toLowerCase()];\n}\n\nfunction replace_scoped(inlineTokens) {\n  var i, token, inside_autolink = 0;\n\n  for (i = inlineTokens.length - 1; i >= 0; i--) {\n    token = inlineTokens[i];\n\n    if (token.type === 'text' && !inside_autolink) {\n      token.content = token.content.replace(SCOPED_ABBR_RE, replaceFn);\n    }\n\n    if (token.type === 'link_open' && token.info === 'auto') {\n      inside_autolink--;\n    }\n\n    if (token.type === 'link_close' && token.info === 'auto') {\n      inside_autolink++;\n    }\n  }\n}\n\nfunction replace_rare(inlineTokens) {\n  var i, token, inside_autolink = 0;\n\n  for (i = inlineTokens.length - 1; i >= 0; i--) {\n    token = inlineTokens[i];\n\n    if (token.type === 'text' && !inside_autolink) {\n      if (RARE_RE.test(token.content)) {\n        token.content = token.content\n          .replace(/\\+-/g, '±')\n          // .., ..., ....... -> …\n          // but ?..... & !..... -> ?.. & !..\n          .replace(/\\.{2,}/g, '…').replace(/([?!])…/g, '$1..')\n          .replace(/([?!]){4,}/g, '$1$1$1').replace(/,{2,}/g, ',')\n          // em-dash\n          .replace(/(^|[^-])---(?=[^-]|$)/mg, '$1\\u2014')\n          // en-dash\n          .replace(/(^|\\s)--(?=\\s|$)/mg, '$1\\u2013')\n          .replace(/(^|[^-\\s])--(?=[^-\\s]|$)/mg, '$1\\u2013');\n      }\n    }\n\n    if (token.type === 'link_open' && token.info === 'auto') {\n      inside_autolink--;\n    }\n\n    if (token.type === 'link_close' && token.info === 'auto') {\n      inside_autolink++;\n    }\n  }\n}\n\n\nmodule.exports = function replace(state) {\n  var blkIdx;\n\n  if (!state.md.options.typographer) { return; }\n\n  for (blkIdx = state.tokens.length - 1; blkIdx >= 0; blkIdx--) {\n\n    if (state.tokens[blkIdx].type !== 'inline') { continue; }\n\n    if (SCOPED_ABBR_TEST_RE.test(state.tokens[blkIdx].content)) {\n      replace_scoped(state.tokens[blkIdx].children);\n    }\n\n    if (RARE_RE.test(state.tokens[blkIdx].content)) {\n      replace_rare(state.tokens[blkIdx].children);\n    }\n\n  }\n};\n", "// Convert straight quotation marks to typographic ones\n//\n'use strict';\n\n\nvar isWhiteSpace   = require('../common/utils').isWhiteSpace;\nvar isPunctChar    = require('../common/utils').isPunctChar;\nvar isMdAsciiPunct = require('../common/utils').isMdAsciiPunct;\n\nvar QUOTE_TEST_RE = /['\"]/;\nvar QUOTE_RE = /['\"]/g;\nvar APOSTROPHE = '\\u2019'; /* ’ */\n\n\nfunction replaceAt(str, index, ch) {\n  return str.substr(0, index) + ch + str.substr(index + 1);\n}\n\nfunction process_inlines(tokens, state) {\n  var i, token, text, t, pos, max, thisLevel, item, lastChar, nextChar,\n      isLastPunctChar, isNextPunctChar, isLastWhiteSpace, isNextWhiteSpace,\n      canOpen, canClose, j, isSingle, stack, openQuote, closeQuote;\n\n  stack = [];\n\n  for (i = 0; i < tokens.length; i++) {\n    token = tokens[i];\n\n    thisLevel = tokens[i].level;\n\n    for (j = stack.length - 1; j >= 0; j--) {\n      if (stack[j].level <= thisLevel) { break; }\n    }\n    stack.length = j + 1;\n\n    if (token.type !== 'text') { continue; }\n\n    text = token.content;\n    pos = 0;\n    max = text.length;\n\n    /*eslint no-labels:0,block-scoped-var:0*/\n    OUTER:\n    while (pos < max) {\n      QUOTE_RE.lastIndex = pos;\n      t = QUOTE_RE.exec(text);\n      if (!t) { break; }\n\n      canOpen = canClose = true;\n      pos = t.index + 1;\n      isSingle = (t[0] === \"'\");\n\n      // Find previous character,\n      // default to space if it's the beginning of the line\n      //\n      lastChar = 0x20;\n\n      if (t.index - 1 >= 0) {\n        lastChar = text.charCodeAt(t.index - 1);\n      } else {\n        for (j = i - 1; j >= 0; j--) {\n          if (tokens[j].type === 'softbreak' || tokens[j].type === 'hardbreak') break; // lastChar defaults to 0x20\n          if (!tokens[j].content) continue; // should skip all tokens except 'text', 'html_inline' or 'code_inline'\n\n          lastChar = tokens[j].content.charCodeAt(tokens[j].content.length - 1);\n          break;\n        }\n      }\n\n      // Find next character,\n      // default to space if it's the end of the line\n      //\n      nextChar = 0x20;\n\n      if (pos < max) {\n        nextChar = text.charCodeAt(pos);\n      } else {\n        for (j = i + 1; j < tokens.length; j++) {\n          if (tokens[j].type === 'softbreak' || tokens[j].type === 'hardbreak') break; // nextChar defaults to 0x20\n          if (!tokens[j].content) continue; // should skip all tokens except 'text', 'html_inline' or 'code_inline'\n\n          nextChar = tokens[j].content.charCodeAt(0);\n          break;\n        }\n      }\n\n      isLastPunctChar = isMdAsciiPunct(lastChar) || isPunctChar(String.fromCharCode(lastChar));\n      isNextPunctChar = isMdAsciiPunct(nextChar) || isPunctChar(String.fromCharCode(nextChar));\n\n      isLastWhiteSpace = isWhiteSpace(lastChar);\n      isNextWhiteSpace = isWhiteSpace(nextChar);\n\n      if (isNextWhiteSpace) {\n        canOpen = false;\n      } else if (isNextPunctChar) {\n        if (!(isLastWhiteSpace || isLastPunctChar)) {\n          canOpen = false;\n        }\n      }\n\n      if (isLastWhiteSpace) {\n        canClose = false;\n      } else if (isLastPunctChar) {\n        if (!(isNextWhiteSpace || isNextPunctChar)) {\n          canClose = false;\n        }\n      }\n\n      if (nextChar === 0x22 /* \" */ && t[0] === '\"') {\n        if (lastChar >= 0x30 /* 0 */ && lastChar <= 0x39 /* 9 */) {\n          // special case: 1\"\" - count first quote as an inch\n          canClose = canOpen = false;\n        }\n      }\n\n      if (canOpen && canClose) {\n        // Replace quotes in the middle of punctuation sequence, but not\n        // in the middle of the words, i.e.:\n        //\n        // 1. foo \" bar \" baz - not replaced\n        // 2. foo-\"-bar-\"-baz - replaced\n        // 3. foo\"bar\"baz     - not replaced\n        //\n        canOpen = isLastPunctChar;\n        canClose = isNextPunctChar;\n      }\n\n      if (!canOpen && !canClose) {\n        // middle of word\n        if (isSingle) {\n          token.content = replaceAt(token.content, t.index, APOSTROPHE);\n        }\n        continue;\n      }\n\n      if (canClose) {\n        // this could be a closing quote, rewind the stack to get a match\n        for (j = stack.length - 1; j >= 0; j--) {\n          item = stack[j];\n          if (stack[j].level < thisLevel) { break; }\n          if (item.single === isSingle && stack[j].level === thisLevel) {\n            item = stack[j];\n\n            if (isSingle) {\n              openQuote = state.md.options.quotes[2];\n              closeQuote = state.md.options.quotes[3];\n            } else {\n              openQuote = state.md.options.quotes[0];\n              closeQuote = state.md.options.quotes[1];\n            }\n\n            // replace token.content *before* tokens[item.token].content,\n            // because, if they are pointing at the same token, replaceAt\n            // could mess up indices when quote length != 1\n            token.content = replaceAt(token.content, t.index, closeQuote);\n            tokens[item.token].content = replaceAt(\n              tokens[item.token].content, item.pos, openQuote);\n\n            pos += closeQuote.length - 1;\n            if (item.token === i) { pos += openQuote.length - 1; }\n\n            text = token.content;\n            max = text.length;\n\n            stack.length = j;\n            continue OUTER;\n          }\n        }\n      }\n\n      if (canOpen) {\n        stack.push({\n          token: i,\n          pos: t.index,\n          single: isSingle,\n          level: thisLevel\n        });\n      } else if (canClose && isSingle) {\n        token.content = replaceAt(token.content, t.index, APOSTROPHE);\n      }\n    }\n  }\n}\n\n\nmodule.exports = function smartquotes(state) {\n  /*eslint max-depth:0*/\n  var blkIdx;\n\n  if (!state.md.options.typographer) { return; }\n\n  for (blkIdx = state.tokens.length - 1; blkIdx >= 0; blkIdx--) {\n\n    if (state.tokens[blkIdx].type !== 'inline' ||\n        !QUOTE_TEST_RE.test(state.tokens[blkIdx].content)) {\n      continue;\n    }\n\n    process_inlines(state.tokens[blkIdx].children, state);\n  }\n};\n", "// Token class\n\n'use strict';\n\n\n/**\n * class Token\n **/\n\n/**\n * new Token(type, tag, nesting)\n *\n * Create new token and fill passed properties.\n **/\nfunction Token(type, tag, nesting) {\n  /**\n   * Token#type -> String\n   *\n   * Type of the token (string, e.g. \"paragraph_open\")\n   **/\n  this.type     = type;\n\n  /**\n   * Token#tag -> String\n   *\n   * html tag name, e.g. \"p\"\n   **/\n  this.tag      = tag;\n\n  /**\n   * Token#attrs -> Array\n   *\n   * Html attributes. Format: `[ [ name1, value1 ], [ name2, value2 ] ]`\n   **/\n  this.attrs    = null;\n\n  /**\n   * Token#map -> Array\n   *\n   * Source map info. Format: `[ line_begin, line_end ]`\n   **/\n  this.map      = null;\n\n  /**\n   * Token#nesting -> Number\n   *\n   * Level change (number in {-1, 0, 1} set), where:\n   *\n   * -  `1` means the tag is opening\n   * -  `0` means the tag is self-closing\n   * - `-1` means the tag is closing\n   **/\n  this.nesting  = nesting;\n\n  /**\n   * Token#level -> Number\n   *\n   * nesting level, the same as `state.level`\n   **/\n  this.level    = 0;\n\n  /**\n   * Token#children -> Array\n   *\n   * An array of child nodes (inline and img tokens)\n   **/\n  this.children = null;\n\n  /**\n   * Token#content -> String\n   *\n   * In a case of self-closing tag (code, html, fence, etc.),\n   * it has contents of this tag.\n   **/\n  this.content  = '';\n\n  /**\n   * Token#markup -> String\n   *\n   * '*' or '_' for emphasis, fence string for fence, etc.\n   **/\n  this.markup   = '';\n\n  /**\n   * Token#info -> String\n   *\n   * Additional information:\n   *\n   * - Info string for \"fence\" tokens\n   * - The value \"auto\" for autolink \"link_open\" and \"link_close\" tokens\n   * - The string value of the item marker for ordered-list \"list_item_open\" tokens\n   **/\n  this.info     = '';\n\n  /**\n   * Token#meta -> Object\n   *\n   * A place for plugins to store an arbitrary data\n   **/\n  this.meta     = null;\n\n  /**\n   * Token#block -> Boolean\n   *\n   * True for block-level tokens, false for inline tokens.\n   * Used in renderer to calculate line breaks\n   **/\n  this.block    = false;\n\n  /**\n   * Token#hidden -> Boolean\n   *\n   * If it's true, ignore this element when rendering. Used for tight lists\n   * to hide paragraphs.\n   **/\n  this.hidden   = false;\n}\n\n\n/**\n * Token.attrIndex(name) -> Number\n *\n * Search attribute index by name.\n **/\nToken.prototype.attrIndex = function attrIndex(name) {\n  var attrs, i, len;\n\n  if (!this.attrs) { return -1; }\n\n  attrs = this.attrs;\n\n  for (i = 0, len = attrs.length; i < len; i++) {\n    if (attrs[i][0] === name) { return i; }\n  }\n  return -1;\n};\n\n\n/**\n * Token.attrPush(attrData)\n *\n * Add `[ name, value ]` attribute to list. Init attrs if necessary\n **/\nToken.prototype.attrPush = function attrPush(attrData) {\n  if (this.attrs) {\n    this.attrs.push(attrData);\n  } else {\n    this.attrs = [ attrData ];\n  }\n};\n\n\n/**\n * Token.attrSet(name, value)\n *\n * Set `name` attribute to `value`. Override old value if exists.\n **/\nToken.prototype.attrSet = function attrSet(name, value) {\n  var idx = this.attrIndex(name),\n      attrData = [ name, value ];\n\n  if (idx < 0) {\n    this.attrPush(attrData);\n  } else {\n    this.attrs[idx] = attrData;\n  }\n};\n\n\n/**\n * Token.attrGet(name)\n *\n * Get the value of attribute `name`, or null if it does not exist.\n **/\nToken.prototype.attrGet = function attrGet(name) {\n  var idx = this.attrIndex(name), value = null;\n  if (idx >= 0) {\n    value = this.attrs[idx][1];\n  }\n  return value;\n};\n\n\n/**\n * Token.attrJoin(name, value)\n *\n * Join value to existing attribute via space. Or create new attribute if not\n * exists. Useful to operate with token classes.\n **/\nToken.prototype.attrJoin = function attrJoin(name, value) {\n  var idx = this.attrIndex(name);\n\n  if (idx < 0) {\n    this.attrPush([ name, value ]);\n  } else {\n    this.attrs[idx][1] = this.attrs[idx][1] + ' ' + value;\n  }\n};\n\n\nmodule.exports = Token;\n", "// Core state object\n//\n'use strict';\n\nvar Token = require('../token');\n\n\nfunction StateCore(src, md, env) {\n  this.src = src;\n  this.env = env;\n  this.tokens = [];\n  this.inlineMode = false;\n  this.md = md; // link to parser instance\n}\n\n// re-export Token class to use in core rules\nStateCore.prototype.Token = Token;\n\n\nmodule.exports = StateCore;\n", "/** internal\n * class Core\n *\n * Top-level rules executor. Glues block/inline parsers and does intermediate\n * transformations.\n **/\n'use strict';\n\n\nvar Ruler  = require('./ruler');\n\n\nvar _rules = [\n  [ 'normalize',      require('./rules_core/normalize')      ],\n  [ 'block',          require('./rules_core/block')          ],\n  [ 'inline',         require('./rules_core/inline')         ],\n  [ 'linkify',        require('./rules_core/linkify')        ],\n  [ 'replacements',   require('./rules_core/replacements')   ],\n  [ 'smartquotes',    require('./rules_core/smartquotes')    ]\n];\n\n\n/**\n * new Core()\n **/\nfunction Core() {\n  /**\n   * Core#ruler -> Ruler\n   *\n   * [[Ruler]] instance. Keep configuration of core rules.\n   **/\n  this.ruler = new Ruler();\n\n  for (var i = 0; i < _rules.length; i++) {\n    this.ruler.push(_rules[i][0], _rules[i][1]);\n  }\n}\n\n\n/**\n * Core.process(state)\n *\n * Executes core chain rules.\n **/\nCore.prototype.process = function (state) {\n  var i, l, rules;\n\n  rules = this.ruler.getRules('');\n\n  for (i = 0, l = rules.length; i < l; i++) {\n    rules[i](state);\n  }\n};\n\nCore.prototype.State = require('./rules_core/state_core');\n\n\nmodule.exports = Core;\n", "// GFM table, https://github.github.com/gfm/#tables-extension-\n\n'use strict';\n\nvar isSpace = require('../common/utils').isSpace;\n\n\nfunction getLine(state, line) {\n  var pos = state.bMarks[line] + state.tShift[line],\n      max = state.eMarks[line];\n\n  return state.src.substr(pos, max - pos);\n}\n\nfunction escapedSplit(str) {\n  var result = [],\n      pos = 0,\n      max = str.length,\n      ch,\n      isEscaped = false,\n      lastPos = 0,\n      current = '';\n\n  ch  = str.charCodeAt(pos);\n\n  while (pos < max) {\n    if (ch === 0x7c/* | */) {\n      if (!isEscaped) {\n        // pipe separating cells, '|'\n        result.push(current + str.substring(lastPos, pos));\n        current = '';\n        lastPos = pos + 1;\n      } else {\n        // escaped pipe, '\\|'\n        current += str.substring(lastPos, pos - 1);\n        lastPos = pos;\n      }\n    }\n\n    isEscaped = (ch === 0x5c/* \\ */);\n    pos++;\n\n    ch = str.charCodeAt(pos);\n  }\n\n  result.push(current + str.substring(lastPos));\n\n  return result;\n}\n\n\nmodule.exports = function table(state, startLine, endLine, silent) {\n  var ch, lineText, pos, i, l, nextLine, columns, columnCount, token,\n      aligns, t, tableLines, tbodyLines, oldParentType, terminate,\n      terminatorRules, firstCh, secondCh;\n\n  // should have at least two lines\n  if (startLine + 2 > endLine) { return false; }\n\n  nextLine = startLine + 1;\n\n  if (state.sCount[nextLine] < state.blkIndent) { return false; }\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[nextLine] - state.blkIndent >= 4) { return false; }\n\n  // first character of the second line should be '|', '-', ':',\n  // and no other characters are allowed but spaces;\n  // basically, this is the equivalent of /^[-:|][-:|\\s]*$/ regexp\n\n  pos = state.bMarks[nextLine] + state.tShift[nextLine];\n  if (pos >= state.eMarks[nextLine]) { return false; }\n\n  firstCh = state.src.charCodeAt(pos++);\n  if (firstCh !== 0x7C/* | */ && firstCh !== 0x2D/* - */ && firstCh !== 0x3A/* : */) { return false; }\n\n  if (pos >= state.eMarks[nextLine]) { return false; }\n\n  secondCh = state.src.charCodeAt(pos++);\n  if (secondCh !== 0x7C/* | */ && secondCh !== 0x2D/* - */ && secondCh !== 0x3A/* : */ && !isSpace(secondCh)) {\n    return false;\n  }\n\n  // if first character is '-', then second character must not be a space\n  // (due to parsing ambiguity with list)\n  if (firstCh === 0x2D/* - */ && isSpace(secondCh)) { return false; }\n\n  while (pos < state.eMarks[nextLine]) {\n    ch = state.src.charCodeAt(pos);\n\n    if (ch !== 0x7C/* | */ && ch !== 0x2D/* - */ && ch !== 0x3A/* : */ && !isSpace(ch)) { return false; }\n\n    pos++;\n  }\n\n  lineText = getLine(state, startLine + 1);\n\n  columns = lineText.split('|');\n  aligns = [];\n  for (i = 0; i < columns.length; i++) {\n    t = columns[i].trim();\n    if (!t) {\n      // allow empty columns before and after table, but not in between columns;\n      // e.g. allow ` |---| `, disallow ` ---||--- `\n      if (i === 0 || i === columns.length - 1) {\n        continue;\n      } else {\n        return false;\n      }\n    }\n\n    if (!/^:?-+:?$/.test(t)) { return false; }\n    if (t.charCodeAt(t.length - 1) === 0x3A/* : */) {\n      aligns.push(t.charCodeAt(0) === 0x3A/* : */ ? 'center' : 'right');\n    } else if (t.charCodeAt(0) === 0x3A/* : */) {\n      aligns.push('left');\n    } else {\n      aligns.push('');\n    }\n  }\n\n  lineText = getLine(state, startLine).trim();\n  if (lineText.indexOf('|') === -1) { return false; }\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false; }\n  columns = escapedSplit(lineText);\n  if (columns.length && columns[0] === '') columns.shift();\n  if (columns.length && columns[columns.length - 1] === '') columns.pop();\n\n  // header row will define an amount of columns in the entire table,\n  // and align row should be exactly the same (the rest of the rows can differ)\n  columnCount = columns.length;\n  if (columnCount === 0 || columnCount !== aligns.length) { return false; }\n\n  if (silent) { return true; }\n\n  oldParentType = state.parentType;\n  state.parentType = 'table';\n\n  // use 'blockquote' lists for termination because it's\n  // the most similar to tables\n  terminatorRules = state.md.block.ruler.getRules('blockquote');\n\n  token     = state.push('table_open', 'table', 1);\n  token.map = tableLines = [ startLine, 0 ];\n\n  token     = state.push('thead_open', 'thead', 1);\n  token.map = [ startLine, startLine + 1 ];\n\n  token     = state.push('tr_open', 'tr', 1);\n  token.map = [ startLine, startLine + 1 ];\n\n  for (i = 0; i < columns.length; i++) {\n    token          = state.push('th_open', 'th', 1);\n    if (aligns[i]) {\n      token.attrs  = [ [ 'style', 'text-align:' + aligns[i] ] ];\n    }\n\n    token          = state.push('inline', '', 0);\n    token.content  = columns[i].trim();\n    token.children = [];\n\n    token          = state.push('th_close', 'th', -1);\n  }\n\n  token     = state.push('tr_close', 'tr', -1);\n  token     = state.push('thead_close', 'thead', -1);\n\n  for (nextLine = startLine + 2; nextLine < endLine; nextLine++) {\n    if (state.sCount[nextLine] < state.blkIndent) { break; }\n\n    terminate = false;\n    for (i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true;\n        break;\n      }\n    }\n\n    if (terminate) { break; }\n    lineText = getLine(state, nextLine).trim();\n    if (!lineText) { break; }\n    if (state.sCount[nextLine] - state.blkIndent >= 4) { break; }\n    columns = escapedSplit(lineText);\n    if (columns.length && columns[0] === '') columns.shift();\n    if (columns.length && columns[columns.length - 1] === '') columns.pop();\n\n    if (nextLine === startLine + 2) {\n      token     = state.push('tbody_open', 'tbody', 1);\n      token.map = tbodyLines = [ startLine + 2, 0 ];\n    }\n\n    token     = state.push('tr_open', 'tr', 1);\n    token.map = [ nextLine, nextLine + 1 ];\n\n    for (i = 0; i < columnCount; i++) {\n      token          = state.push('td_open', 'td', 1);\n      if (aligns[i]) {\n        token.attrs  = [ [ 'style', 'text-align:' + aligns[i] ] ];\n      }\n\n      token          = state.push('inline', '', 0);\n      token.content  = columns[i] ? columns[i].trim() : '';\n      token.children = [];\n\n      token          = state.push('td_close', 'td', -1);\n    }\n    token = state.push('tr_close', 'tr', -1);\n  }\n\n  if (tbodyLines) {\n    token = state.push('tbody_close', 'tbody', -1);\n    tbodyLines[1] = nextLine;\n  }\n\n  token = state.push('table_close', 'table', -1);\n  tableLines[1] = nextLine;\n\n  state.parentType = oldParentType;\n  state.line = nextLine;\n  return true;\n};\n", "// Code block (4 spaces padded)\n\n'use strict';\n\n\nmodule.exports = function code(state, startLine, endLine/*, silent*/) {\n  var nextLine, last, token;\n\n  if (state.sCount[startLine] - state.blkIndent < 4) { return false; }\n\n  last = nextLine = startLine + 1;\n\n  while (nextLine < endLine) {\n    if (state.isEmpty(nextLine)) {\n      nextLine++;\n      continue;\n    }\n\n    if (state.sCount[nextLine] - state.blkIndent >= 4) {\n      nextLine++;\n      last = nextLine;\n      continue;\n    }\n    break;\n  }\n\n  state.line = last;\n\n  token         = state.push('code_block', 'code', 0);\n  token.content = state.getLines(startLine, last, 4 + state.blkIndent, false) + '\\n';\n  token.map     = [ startLine, state.line ];\n\n  return true;\n};\n", "// fences (``` lang, ~~~ lang)\n\n'use strict';\n\n\nmodule.exports = function fence(state, startLine, endLine, silent) {\n  var marker, len, params, nextLine, mem, token, markup,\n      haveEndMarker = false,\n      pos = state.bMarks[startLine] + state.tShift[startLine],\n      max = state.eMarks[startLine];\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false; }\n\n  if (pos + 3 > max) { return false; }\n\n  marker = state.src.charCodeAt(pos);\n\n  if (marker !== 0x7E/* ~ */ && marker !== 0x60 /* ` */) {\n    return false;\n  }\n\n  // scan marker length\n  mem = pos;\n  pos = state.skipChars(pos, marker);\n\n  len = pos - mem;\n\n  if (len < 3) { return false; }\n\n  markup = state.src.slice(mem, pos);\n  params = state.src.slice(pos, max);\n\n  if (marker === 0x60 /* ` */) {\n    if (params.indexOf(String.fromCharCode(marker)) >= 0) {\n      return false;\n    }\n  }\n\n  // Since start is found, we can report success here in validation mode\n  if (silent) { return true; }\n\n  // search end of block\n  nextLine = startLine;\n\n  for (;;) {\n    nextLine++;\n    if (nextLine >= endLine) {\n      // unclosed block should be autoclosed by end of document.\n      // also block seems to be autoclosed by end of parent\n      break;\n    }\n\n    pos = mem = state.bMarks[nextLine] + state.tShift[nextLine];\n    max = state.eMarks[nextLine];\n\n    if (pos < max && state.sCount[nextLine] < state.blkIndent) {\n      // non-empty line with negative indent should stop the list:\n      // - ```\n      //  test\n      break;\n    }\n\n    if (state.src.charCodeAt(pos) !== marker) { continue; }\n\n    if (state.sCount[nextLine] - state.blkIndent >= 4) {\n      // closing fence should be indented less than 4 spaces\n      continue;\n    }\n\n    pos = state.skipChars(pos, marker);\n\n    // closing code fence must be at least as long as the opening one\n    if (pos - mem < len) { continue; }\n\n    // make sure tail has spaces only\n    pos = state.skipSpaces(pos);\n\n    if (pos < max) { continue; }\n\n    haveEndMarker = true;\n    // found!\n    break;\n  }\n\n  // If a fence has heading spaces, they should be removed from its inner block\n  len = state.sCount[startLine];\n\n  state.line = nextLine + (haveEndMarker ? 1 : 0);\n\n  token         = state.push('fence', 'code', 0);\n  token.info    = params;\n  token.content = state.getLines(startLine + 1, nextLine, len, true);\n  token.markup  = markup;\n  token.map     = [ startLine, state.line ];\n\n  return true;\n};\n", "// Block quotes\n\n'use strict';\n\nvar isSpace = require('../common/utils').isSpace;\n\n\nmodule.exports = function blockquote(state, startLine, endLine, silent) {\n  var adjustTab,\n      ch,\n      i,\n      initial,\n      l,\n      lastLineEmpty,\n      lines,\n      nextLine,\n      offset,\n      oldBMarks,\n      oldBSCount,\n      oldIndent,\n      oldParentType,\n      oldSCount,\n      oldTShift,\n      spaceAfterMarker,\n      terminate,\n      terminatorRules,\n      token,\n      isOutdented,\n      oldLineMax = state.lineMax,\n      pos = state.bMarks[startLine] + state.tShift[startLine],\n      max = state.eMarks[startLine];\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false; }\n\n  // check the block quote marker\n  if (state.src.charCodeAt(pos++) !== 0x3E/* > */) { return false; }\n\n  // we know that it's going to be a valid blockquote,\n  // so no point trying to find the end of it in silent mode\n  if (silent) { return true; }\n\n  // set offset past spaces and \">\"\n  initial = offset = state.sCount[startLine] + 1;\n\n  // skip one optional space after '>'\n  if (state.src.charCodeAt(pos) === 0x20 /* space */) {\n    // ' >   test '\n    //     ^ -- position start of line here:\n    pos++;\n    initial++;\n    offset++;\n    adjustTab = false;\n    spaceAfterMarker = true;\n  } else if (state.src.charCodeAt(pos) === 0x09 /* tab */) {\n    spaceAfterMarker = true;\n\n    if ((state.bsCount[startLine] + offset) % 4 === 3) {\n      // '  >\\t  test '\n      //       ^ -- position start of line here (tab has width===1)\n      pos++;\n      initial++;\n      offset++;\n      adjustTab = false;\n    } else {\n      // ' >\\t  test '\n      //    ^ -- position start of line here + shift bsCount slightly\n      //         to make extra space appear\n      adjustTab = true;\n    }\n  } else {\n    spaceAfterMarker = false;\n  }\n\n  oldBMarks = [ state.bMarks[startLine] ];\n  state.bMarks[startLine] = pos;\n\n  while (pos < max) {\n    ch = state.src.charCodeAt(pos);\n\n    if (isSpace(ch)) {\n      if (ch === 0x09) {\n        offset += 4 - (offset + state.bsCount[startLine] + (adjustTab ? 1 : 0)) % 4;\n      } else {\n        offset++;\n      }\n    } else {\n      break;\n    }\n\n    pos++;\n  }\n\n  oldBSCount = [ state.bsCount[startLine] ];\n  state.bsCount[startLine] = state.sCount[startLine] + 1 + (spaceAfterMarker ? 1 : 0);\n\n  lastLineEmpty = pos >= max;\n\n  oldSCount = [ state.sCount[startLine] ];\n  state.sCount[startLine] = offset - initial;\n\n  oldTShift = [ state.tShift[startLine] ];\n  state.tShift[startLine] = pos - state.bMarks[startLine];\n\n  terminatorRules = state.md.block.ruler.getRules('blockquote');\n\n  oldParentType = state.parentType;\n  state.parentType = 'blockquote';\n\n  // Search the end of the block\n  //\n  // Block ends with either:\n  //  1. an empty line outside:\n  //     ```\n  //     > test\n  //\n  //     ```\n  //  2. an empty line inside:\n  //     ```\n  //     >\n  //     test\n  //     ```\n  //  3. another tag:\n  //     ```\n  //     > test\n  //      - - -\n  //     ```\n  for (nextLine = startLine + 1; nextLine < endLine; nextLine++) {\n    // check if it's outdented, i.e. it's inside list item and indented\n    // less than said list item:\n    //\n    // ```\n    // 1. anything\n    //    > current blockquote\n    // 2. checking this line\n    // ```\n    isOutdented = state.sCount[nextLine] < state.blkIndent;\n\n    pos = state.bMarks[nextLine] + state.tShift[nextLine];\n    max = state.eMarks[nextLine];\n\n    if (pos >= max) {\n      // Case 1: line is not inside the blockquote, and this line is empty.\n      break;\n    }\n\n    if (state.src.charCodeAt(pos++) === 0x3E/* > */ && !isOutdented) {\n      // This line is inside the blockquote.\n\n      // set offset past spaces and \">\"\n      initial = offset = state.sCount[nextLine] + 1;\n\n      // skip one optional space after '>'\n      if (state.src.charCodeAt(pos) === 0x20 /* space */) {\n        // ' >   test '\n        //     ^ -- position start of line here:\n        pos++;\n        initial++;\n        offset++;\n        adjustTab = false;\n        spaceAfterMarker = true;\n      } else if (state.src.charCodeAt(pos) === 0x09 /* tab */) {\n        spaceAfterMarker = true;\n\n        if ((state.bsCount[nextLine] + offset) % 4 === 3) {\n          // '  >\\t  test '\n          //       ^ -- position start of line here (tab has width===1)\n          pos++;\n          initial++;\n          offset++;\n          adjustTab = false;\n        } else {\n          // ' >\\t  test '\n          //    ^ -- position start of line here + shift bsCount slightly\n          //         to make extra space appear\n          adjustTab = true;\n        }\n      } else {\n        spaceAfterMarker = false;\n      }\n\n      oldBMarks.push(state.bMarks[nextLine]);\n      state.bMarks[nextLine] = pos;\n\n      while (pos < max) {\n        ch = state.src.charCodeAt(pos);\n\n        if (isSpace(ch)) {\n          if (ch === 0x09) {\n            offset += 4 - (offset + state.bsCount[nextLine] + (adjustTab ? 1 : 0)) % 4;\n          } else {\n            offset++;\n          }\n        } else {\n          break;\n        }\n\n        pos++;\n      }\n\n      lastLineEmpty = pos >= max;\n\n      oldBSCount.push(state.bsCount[nextLine]);\n      state.bsCount[nextLine] = state.sCount[nextLine] + 1 + (spaceAfterMarker ? 1 : 0);\n\n      oldSCount.push(state.sCount[nextLine]);\n      state.sCount[nextLine] = offset - initial;\n\n      oldTShift.push(state.tShift[nextLine]);\n      state.tShift[nextLine] = pos - state.bMarks[nextLine];\n      continue;\n    }\n\n    // Case 2: line is not inside the blockquote, and the last line was empty.\n    if (lastLineEmpty) { break; }\n\n    // Case 3: another tag found.\n    terminate = false;\n    for (i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true;\n        break;\n      }\n    }\n\n    if (terminate) {\n      // Quirk to enforce \"hard termination mode\" for paragraphs;\n      // normally if you call `tokenize(state, startLine, nextLine)`,\n      // paragraphs will look below nextLine for paragraph continuation,\n      // but if blockquote is terminated by another tag, they shouldn't\n      state.lineMax = nextLine;\n\n      if (state.blkIndent !== 0) {\n        // state.blkIndent was non-zero, we now set it to zero,\n        // so we need to re-calculate all offsets to appear as\n        // if indent wasn't changed\n        oldBMarks.push(state.bMarks[nextLine]);\n        oldBSCount.push(state.bsCount[nextLine]);\n        oldTShift.push(state.tShift[nextLine]);\n        oldSCount.push(state.sCount[nextLine]);\n        state.sCount[nextLine] -= state.blkIndent;\n      }\n\n      break;\n    }\n\n    oldBMarks.push(state.bMarks[nextLine]);\n    oldBSCount.push(state.bsCount[nextLine]);\n    oldTShift.push(state.tShift[nextLine]);\n    oldSCount.push(state.sCount[nextLine]);\n\n    // A negative indentation means that this is a paragraph continuation\n    //\n    state.sCount[nextLine] = -1;\n  }\n\n  oldIndent = state.blkIndent;\n  state.blkIndent = 0;\n\n  token        = state.push('blockquote_open', 'blockquote', 1);\n  token.markup = '>';\n  token.map    = lines = [ startLine, 0 ];\n\n  state.md.block.tokenize(state, startLine, nextLine);\n\n  token        = state.push('blockquote_close', 'blockquote', -1);\n  token.markup = '>';\n\n  state.lineMax = oldLineMax;\n  state.parentType = oldParentType;\n  lines[1] = state.line;\n\n  // Restore original tShift; this might not be necessary since the parser\n  // has already been here, but just to make sure we can do that.\n  for (i = 0; i < oldTShift.length; i++) {\n    state.bMarks[i + startLine] = oldBMarks[i];\n    state.tShift[i + startLine] = oldTShift[i];\n    state.sCount[i + startLine] = oldSCount[i];\n    state.bsCount[i + startLine] = oldBSCount[i];\n  }\n  state.blkIndent = oldIndent;\n\n  return true;\n};\n", "// Horizontal rule\n\n'use strict';\n\nvar isSpace = require('../common/utils').isSpace;\n\n\nmodule.exports = function hr(state, startLine, endLine, silent) {\n  var marker, cnt, ch, token,\n      pos = state.bMarks[startLine] + state.tShift[startLine],\n      max = state.eMarks[startLine];\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false; }\n\n  marker = state.src.charCodeAt(pos++);\n\n  // Check hr marker\n  if (marker !== 0x2A/* * */ &&\n      marker !== 0x2D/* - */ &&\n      marker !== 0x5F/* _ */) {\n    return false;\n  }\n\n  // markers can be mixed with spaces, but there should be at least 3 of them\n\n  cnt = 1;\n  while (pos < max) {\n    ch = state.src.charCodeAt(pos++);\n    if (ch !== marker && !isSpace(ch)) { return false; }\n    if (ch === marker) { cnt++; }\n  }\n\n  if (cnt < 3) { return false; }\n\n  if (silent) { return true; }\n\n  state.line = startLine + 1;\n\n  token        = state.push('hr', 'hr', 0);\n  token.map    = [ startLine, state.line ];\n  token.markup = Array(cnt + 1).join(String.fromCharCode(marker));\n\n  return true;\n};\n", "// Lists\n\n'use strict';\n\nvar isSpace = require('../common/utils').isSpace;\n\n\n// Search `[-+*][\\n ]`, returns next pos after marker on success\n// or -1 on fail.\nfunction skipBulletListMarker(state, startLine) {\n  var marker, pos, max, ch;\n\n  pos = state.bMarks[startLine] + state.tShift[startLine];\n  max = state.eMarks[startLine];\n\n  marker = state.src.charCodeAt(pos++);\n  // Check bullet\n  if (marker !== 0x2A/* * */ &&\n      marker !== 0x2D/* - */ &&\n      marker !== 0x2B/* + */) {\n    return -1;\n  }\n\n  if (pos < max) {\n    ch = state.src.charCodeAt(pos);\n\n    if (!isSpace(ch)) {\n      // \" -test \" - is not a list item\n      return -1;\n    }\n  }\n\n  return pos;\n}\n\n// Search `\\d+[.)][\\n ]`, returns next pos after marker on success\n// or -1 on fail.\nfunction skipOrderedListMarker(state, startLine) {\n  var ch,\n      start = state.bMarks[startLine] + state.tShift[startLine],\n      pos = start,\n      max = state.eMarks[startLine];\n\n  // List marker should have at least 2 chars (digit + dot)\n  if (pos + 1 >= max) { return -1; }\n\n  ch = state.src.charCodeAt(pos++);\n\n  if (ch < 0x30/* 0 */ || ch > 0x39/* 9 */) { return -1; }\n\n  for (;;) {\n    // EOL -> fail\n    if (pos >= max) { return -1; }\n\n    ch = state.src.charCodeAt(pos++);\n\n    if (ch >= 0x30/* 0 */ && ch <= 0x39/* 9 */) {\n\n      // List marker should have no more than 9 digits\n      // (prevents integer overflow in browsers)\n      if (pos - start >= 10) { return -1; }\n\n      continue;\n    }\n\n    // found valid marker\n    if (ch === 0x29/* ) */ || ch === 0x2e/* . */) {\n      break;\n    }\n\n    return -1;\n  }\n\n\n  if (pos < max) {\n    ch = state.src.charCodeAt(pos);\n\n    if (!isSpace(ch)) {\n      // \" 1.test \" - is not a list item\n      return -1;\n    }\n  }\n  return pos;\n}\n\nfunction markTightParagraphs(state, idx) {\n  var i, l,\n      level = state.level + 2;\n\n  for (i = idx + 2, l = state.tokens.length - 2; i < l; i++) {\n    if (state.tokens[i].level === level && state.tokens[i].type === 'paragraph_open') {\n      state.tokens[i + 2].hidden = true;\n      state.tokens[i].hidden = true;\n      i += 2;\n    }\n  }\n}\n\n\nmodule.exports = function list(state, startLine, endLine, silent) {\n  var ch,\n      contentStart,\n      i,\n      indent,\n      indentAfterMarker,\n      initial,\n      isOrdered,\n      itemLines,\n      l,\n      listLines,\n      listTokIdx,\n      markerCharCode,\n      markerValue,\n      max,\n      nextLine,\n      offset,\n      oldListIndent,\n      oldParentType,\n      oldSCount,\n      oldTShift,\n      oldTight,\n      pos,\n      posAfterMarker,\n      prevEmptyEnd,\n      start,\n      terminate,\n      terminatorRules,\n      token,\n      isTerminatingParagraph = false,\n      tight = true;\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false; }\n\n  // Special case:\n  //  - item 1\n  //   - item 2\n  //    - item 3\n  //     - item 4\n  //      - this one is a paragraph continuation\n  if (state.listIndent >= 0 &&\n      state.sCount[startLine] - state.listIndent >= 4 &&\n      state.sCount[startLine] < state.blkIndent) {\n    return false;\n  }\n\n  // limit conditions when list can interrupt\n  // a paragraph (validation mode only)\n  if (silent && state.parentType === 'paragraph') {\n    // Next list item should still terminate previous list item;\n    //\n    // This code can fail if plugins use blkIndent as well as lists,\n    // but I hope the spec gets fixed long before that happens.\n    //\n    if (state.sCount[startLine] >= state.blkIndent) {\n      isTerminatingParagraph = true;\n    }\n  }\n\n  // Detect list type and position after marker\n  if ((posAfterMarker = skipOrderedListMarker(state, startLine)) >= 0) {\n    isOrdered = true;\n    start = state.bMarks[startLine] + state.tShift[startLine];\n    markerValue = Number(state.src.slice(start, posAfterMarker - 1));\n\n    // If we're starting a new ordered list right after\n    // a paragraph, it should start with 1.\n    if (isTerminatingParagraph && markerValue !== 1) return false;\n\n  } else if ((posAfterMarker = skipBulletListMarker(state, startLine)) >= 0) {\n    isOrdered = false;\n\n  } else {\n    return false;\n  }\n\n  // If we're starting a new unordered list right after\n  // a paragraph, first line should not be empty.\n  if (isTerminatingParagraph) {\n    if (state.skipSpaces(posAfterMarker) >= state.eMarks[startLine]) return false;\n  }\n\n  // We should terminate list on style change. Remember first one to compare.\n  markerCharCode = state.src.charCodeAt(posAfterMarker - 1);\n\n  // For validation mode we can terminate immediately\n  if (silent) { return true; }\n\n  // Start list\n  listTokIdx = state.tokens.length;\n\n  if (isOrdered) {\n    token       = state.push('ordered_list_open', 'ol', 1);\n    if (markerValue !== 1) {\n      token.attrs = [ [ 'start', markerValue ] ];\n    }\n\n  } else {\n    token       = state.push('bullet_list_open', 'ul', 1);\n  }\n\n  token.map    = listLines = [ startLine, 0 ];\n  token.markup = String.fromCharCode(markerCharCode);\n\n  //\n  // Iterate list items\n  //\n\n  nextLine = startLine;\n  prevEmptyEnd = false;\n  terminatorRules = state.md.block.ruler.getRules('list');\n\n  oldParentType = state.parentType;\n  state.parentType = 'list';\n\n  while (nextLine < endLine) {\n    pos = posAfterMarker;\n    max = state.eMarks[nextLine];\n\n    initial = offset = state.sCount[nextLine] + posAfterMarker - (state.bMarks[startLine] + state.tShift[startLine]);\n\n    while (pos < max) {\n      ch = state.src.charCodeAt(pos);\n\n      if (ch === 0x09) {\n        offset += 4 - (offset + state.bsCount[nextLine]) % 4;\n      } else if (ch === 0x20) {\n        offset++;\n      } else {\n        break;\n      }\n\n      pos++;\n    }\n\n    contentStart = pos;\n\n    if (contentStart >= max) {\n      // trimming space in \"-    \\n  3\" case, indent is 1 here\n      indentAfterMarker = 1;\n    } else {\n      indentAfterMarker = offset - initial;\n    }\n\n    // If we have more than 4 spaces, the indent is 1\n    // (the rest is just indented code block)\n    if (indentAfterMarker > 4) { indentAfterMarker = 1; }\n\n    // \"  -  test\"\n    //  ^^^^^ - calculating total length of this thing\n    indent = initial + indentAfterMarker;\n\n    // Run subparser & write tokens\n    token        = state.push('list_item_open', 'li', 1);\n    token.markup = String.fromCharCode(markerCharCode);\n    token.map    = itemLines = [ startLine, 0 ];\n    if (isOrdered) {\n      token.info = state.src.slice(start, posAfterMarker - 1);\n    }\n\n    // change current state, then restore it after parser subcall\n    oldTight = state.tight;\n    oldTShift = state.tShift[startLine];\n    oldSCount = state.sCount[startLine];\n\n    //  - example list\n    // ^ listIndent position will be here\n    //   ^ blkIndent position will be here\n    //\n    oldListIndent = state.listIndent;\n    state.listIndent = state.blkIndent;\n    state.blkIndent = indent;\n\n    state.tight = true;\n    state.tShift[startLine] = contentStart - state.bMarks[startLine];\n    state.sCount[startLine] = offset;\n\n    if (contentStart >= max && state.isEmpty(startLine + 1)) {\n      // workaround for this case\n      // (list item is empty, list terminates before \"foo\"):\n      // ~~~~~~~~\n      //   -\n      //\n      //     foo\n      // ~~~~~~~~\n      state.line = Math.min(state.line + 2, endLine);\n    } else {\n      state.md.block.tokenize(state, startLine, endLine, true);\n    }\n\n    // If any of list item is tight, mark list as tight\n    if (!state.tight || prevEmptyEnd) {\n      tight = false;\n    }\n    // Item become loose if finish with empty line,\n    // but we should filter last element, because it means list finish\n    prevEmptyEnd = (state.line - startLine) > 1 && state.isEmpty(state.line - 1);\n\n    state.blkIndent = state.listIndent;\n    state.listIndent = oldListIndent;\n    state.tShift[startLine] = oldTShift;\n    state.sCount[startLine] = oldSCount;\n    state.tight = oldTight;\n\n    token        = state.push('list_item_close', 'li', -1);\n    token.markup = String.fromCharCode(markerCharCode);\n\n    nextLine = startLine = state.line;\n    itemLines[1] = nextLine;\n    contentStart = state.bMarks[startLine];\n\n    if (nextLine >= endLine) { break; }\n\n    //\n    // Try to check if list is terminated or continued.\n    //\n    if (state.sCount[nextLine] < state.blkIndent) { break; }\n\n    // if it's indented more than 3 spaces, it should be a code block\n    if (state.sCount[startLine] - state.blkIndent >= 4) { break; }\n\n    // fail if terminating block found\n    terminate = false;\n    for (i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true;\n        break;\n      }\n    }\n    if (terminate) { break; }\n\n    // fail if list has another type\n    if (isOrdered) {\n      posAfterMarker = skipOrderedListMarker(state, nextLine);\n      if (posAfterMarker < 0) { break; }\n      start = state.bMarks[nextLine] + state.tShift[nextLine];\n    } else {\n      posAfterMarker = skipBulletListMarker(state, nextLine);\n      if (posAfterMarker < 0) { break; }\n    }\n\n    if (markerCharCode !== state.src.charCodeAt(posAfterMarker - 1)) { break; }\n  }\n\n  // Finalize list\n  if (isOrdered) {\n    token = state.push('ordered_list_close', 'ol', -1);\n  } else {\n    token = state.push('bullet_list_close', 'ul', -1);\n  }\n  token.markup = String.fromCharCode(markerCharCode);\n\n  listLines[1] = nextLine;\n  state.line = nextLine;\n\n  state.parentType = oldParentType;\n\n  // mark paragraphs tight if needed\n  if (tight) {\n    markTightParagraphs(state, listTokIdx);\n  }\n\n  return true;\n};\n", "'use strict';\n\n\nvar normalizeReference   = require('../common/utils').normalizeReference;\nvar isSpace              = require('../common/utils').isSpace;\n\n\nmodule.exports = function reference(state, startLine, _endLine, silent) {\n  var ch,\n      destEndPos,\n      destEndLineNo,\n      endLine,\n      href,\n      i,\n      l,\n      label,\n      labelEnd,\n      oldParentType,\n      res,\n      start,\n      str,\n      terminate,\n      terminatorRules,\n      title,\n      lines = 0,\n      pos = state.bMarks[startLine] + state.tShift[startLine],\n      max = state.eMarks[startLine],\n      nextLine = startLine + 1;\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false; }\n\n  if (state.src.charCodeAt(pos) !== 0x5B/* [ */) { return false; }\n\n  // Simple check to quickly interrupt scan on [link](url) at the start of line.\n  // Can be useful on practice: https://github.com/markdown-it/markdown-it/issues/54\n  while (++pos < max) {\n    if (state.src.charCodeAt(pos) === 0x5D /* ] */ &&\n        state.src.charCodeAt(pos - 1) !== 0x5C/* \\ */) {\n      if (pos + 1 === max) { return false; }\n      if (state.src.charCodeAt(pos + 1) !== 0x3A/* : */) { return false; }\n      break;\n    }\n  }\n\n  endLine = state.lineMax;\n\n  // jump line-by-line until empty one or EOF\n  terminatorRules = state.md.block.ruler.getRules('reference');\n\n  oldParentType = state.parentType;\n  state.parentType = 'reference';\n\n  for (; nextLine < endLine && !state.isEmpty(nextLine); nextLine++) {\n    // this would be a code block normally, but after paragraph\n    // it's considered a lazy continuation regardless of what's there\n    if (state.sCount[nextLine] - state.blkIndent > 3) { continue; }\n\n    // quirk for blockquotes, this line should already be checked by that rule\n    if (state.sCount[nextLine] < 0) { continue; }\n\n    // Some tags can terminate paragraph without empty line.\n    terminate = false;\n    for (i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true;\n        break;\n      }\n    }\n    if (terminate) { break; }\n  }\n\n  str = state.getLines(startLine, nextLine, state.blkIndent, false).trim();\n  max = str.length;\n\n  for (pos = 1; pos < max; pos++) {\n    ch = str.charCodeAt(pos);\n    if (ch === 0x5B /* [ */) {\n      return false;\n    } else if (ch === 0x5D /* ] */) {\n      labelEnd = pos;\n      break;\n    } else if (ch === 0x0A /* \\n */) {\n      lines++;\n    } else if (ch === 0x5C /* \\ */) {\n      pos++;\n      if (pos < max && str.charCodeAt(pos) === 0x0A) {\n        lines++;\n      }\n    }\n  }\n\n  if (labelEnd < 0 || str.charCodeAt(labelEnd + 1) !== 0x3A/* : */) { return false; }\n\n  // [label]:   destination   'title'\n  //         ^^^ skip optional whitespace here\n  for (pos = labelEnd + 2; pos < max; pos++) {\n    ch = str.charCodeAt(pos);\n    if (ch === 0x0A) {\n      lines++;\n    } else if (isSpace(ch)) {\n      /*eslint no-empty:0*/\n    } else {\n      break;\n    }\n  }\n\n  // [label]:   destination   'title'\n  //            ^^^^^^^^^^^ parse this\n  res = state.md.helpers.parseLinkDestination(str, pos, max);\n  if (!res.ok) { return false; }\n\n  href = state.md.normalizeLink(res.str);\n  if (!state.md.validateLink(href)) { return false; }\n\n  pos = res.pos;\n  lines += res.lines;\n\n  // save cursor state, we could require to rollback later\n  destEndPos = pos;\n  destEndLineNo = lines;\n\n  // [label]:   destination   'title'\n  //                       ^^^ skipping those spaces\n  start = pos;\n  for (; pos < max; pos++) {\n    ch = str.charCodeAt(pos);\n    if (ch === 0x0A) {\n      lines++;\n    } else if (isSpace(ch)) {\n      /*eslint no-empty:0*/\n    } else {\n      break;\n    }\n  }\n\n  // [label]:   destination   'title'\n  //                          ^^^^^^^ parse this\n  res = state.md.helpers.parseLinkTitle(str, pos, max);\n  if (pos < max && start !== pos && res.ok) {\n    title = res.str;\n    pos = res.pos;\n    lines += res.lines;\n  } else {\n    title = '';\n    pos = destEndPos;\n    lines = destEndLineNo;\n  }\n\n  // skip trailing spaces until the rest of the line\n  while (pos < max) {\n    ch = str.charCodeAt(pos);\n    if (!isSpace(ch)) { break; }\n    pos++;\n  }\n\n  if (pos < max && str.charCodeAt(pos) !== 0x0A) {\n    if (title) {\n      // garbage at the end of the line after title,\n      // but it could still be a valid reference if we roll back\n      title = '';\n      pos = destEndPos;\n      lines = destEndLineNo;\n      while (pos < max) {\n        ch = str.charCodeAt(pos);\n        if (!isSpace(ch)) { break; }\n        pos++;\n      }\n    }\n  }\n\n  if (pos < max && str.charCodeAt(pos) !== 0x0A) {\n    // garbage at the end of the line\n    return false;\n  }\n\n  label = normalizeReference(str.slice(1, labelEnd));\n  if (!label) {\n    // CommonMark 0.20 disallows empty labels\n    return false;\n  }\n\n  // Reference can not terminate anything. This check is for safety only.\n  /*istanbul ignore if*/\n  if (silent) { return true; }\n\n  if (typeof state.env.references === 'undefined') {\n    state.env.references = {};\n  }\n  if (typeof state.env.references[label] === 'undefined') {\n    state.env.references[label] = { title: title, href: href };\n  }\n\n  state.parentType = oldParentType;\n\n  state.line = startLine + lines + 1;\n  return true;\n};\n", "// List of valid html blocks names, accorting to commonmark spec\n// http://jgm.github.io/CommonMark/spec.html#html-blocks\n\n'use strict';\n\n\nmodule.exports = [\n  'address',\n  'article',\n  'aside',\n  'base',\n  'basefont',\n  'blockquote',\n  'body',\n  'caption',\n  'center',\n  'col',\n  'colgroup',\n  'dd',\n  'details',\n  'dialog',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'frame',\n  'frameset',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hr',\n  'html',\n  'iframe',\n  'legend',\n  'li',\n  'link',\n  'main',\n  'menu',\n  'menuitem',\n  'nav',\n  'noframes',\n  'ol',\n  'optgroup',\n  'option',\n  'p',\n  'param',\n  'section',\n  'source',\n  'summary',\n  'table',\n  'tbody',\n  'td',\n  'tfoot',\n  'th',\n  'thead',\n  'title',\n  'tr',\n  'track',\n  'ul'\n];\n", "// Regexps to match html elements\n\n'use strict';\n\nvar attr_name     = '[a-zA-Z_:][a-zA-Z0-9:._-]*';\n\nvar unquoted      = '[^\"\\'=<>`\\\\x00-\\\\x20]+';\nvar single_quoted = \"'[^']*'\";\nvar double_quoted = '\"[^\"]*\"';\n\nvar attr_value  = '(?:' + unquoted + '|' + single_quoted + '|' + double_quoted + ')';\n\nvar attribute   = '(?:\\\\s+' + attr_name + '(?:\\\\s*=\\\\s*' + attr_value + ')?)';\n\nvar open_tag    = '<[A-Za-z][A-Za-z0-9\\\\-]*' + attribute + '*\\\\s*\\\\/?>';\n\nvar close_tag   = '<\\\\/[A-Za-z][A-Za-z0-9\\\\-]*\\\\s*>';\nvar comment     = '<!---->|<!--(?:-?[^>-])(?:-?[^-])*-->';\nvar processing  = '<[?][\\\\s\\\\S]*?[?]>';\nvar declaration = '<![A-Z]+\\\\s+[^>]*>';\nvar cdata       = '<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>';\n\nvar HTML_TAG_RE = new RegExp('^(?:' + open_tag + '|' + close_tag + '|' + comment +\n                        '|' + processing + '|' + declaration + '|' + cdata + ')');\nvar HTML_OPEN_CLOSE_TAG_RE = new RegExp('^(?:' + open_tag + '|' + close_tag + ')');\n\nmodule.exports.HTML_TAG_RE = HTML_TAG_RE;\nmodule.exports.HTML_OPEN_CLOSE_TAG_RE = HTML_OPEN_CLOSE_TAG_RE;\n", "// HTML block\n\n'use strict';\n\n\nvar block_names = require('../common/html_blocks');\nvar HTML_OPEN_CLOSE_TAG_RE = require('../common/html_re').HTML_OPEN_CLOSE_TAG_RE;\n\n// An array of opening and corresponding closing sequences for html tags,\n// last argument defines whether it can terminate a paragraph or not\n//\nvar HTML_SEQUENCES = [\n  [ /^<(script|pre|style|textarea)(?=(\\s|>|$))/i, /<\\/(script|pre|style|textarea)>/i, true ],\n  [ /^<!--/,        /-->/,   true ],\n  [ /^<\\?/,         /\\?>/,   true ],\n  [ /^<![A-Z]/,     />/,     true ],\n  [ /^<!\\[CDATA\\[/, /\\]\\]>/, true ],\n  [ new RegExp('^</?(' + block_names.join('|') + ')(?=(\\\\s|/?>|$))', 'i'), /^$/, true ],\n  [ new RegExp(HTML_OPEN_CLOSE_TAG_RE.source + '\\\\s*$'),  /^$/, false ]\n];\n\n\nmodule.exports = function html_block(state, startLine, endLine, silent) {\n  var i, nextLine, token, lineText,\n      pos = state.bMarks[startLine] + state.tShift[startLine],\n      max = state.eMarks[startLine];\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false; }\n\n  if (!state.md.options.html) { return false; }\n\n  if (state.src.charCodeAt(pos) !== 0x3C/* < */) { return false; }\n\n  lineText = state.src.slice(pos, max);\n\n  for (i = 0; i < HTML_SEQUENCES.length; i++) {\n    if (HTML_SEQUENCES[i][0].test(lineText)) { break; }\n  }\n\n  if (i === HTML_SEQUENCES.length) { return false; }\n\n  if (silent) {\n    // true if this sequence can be a terminator, false otherwise\n    return HTML_SEQUENCES[i][2];\n  }\n\n  nextLine = startLine + 1;\n\n  // If we are here - we detected HTML block.\n  // Let's roll down till block end.\n  if (!HTML_SEQUENCES[i][1].test(lineText)) {\n    for (; nextLine < endLine; nextLine++) {\n      if (state.sCount[nextLine] < state.blkIndent) { break; }\n\n      pos = state.bMarks[nextLine] + state.tShift[nextLine];\n      max = state.eMarks[nextLine];\n      lineText = state.src.slice(pos, max);\n\n      if (HTML_SEQUENCES[i][1].test(lineText)) {\n        if (lineText.length !== 0) { nextLine++; }\n        break;\n      }\n    }\n  }\n\n  state.line = nextLine;\n\n  token         = state.push('html_block', '', 0);\n  token.map     = [ startLine, nextLine ];\n  token.content = state.getLines(startLine, nextLine, state.blkIndent, true);\n\n  return true;\n};\n", "// heading (#, ##, ...)\n\n'use strict';\n\nvar isSpace = require('../common/utils').isSpace;\n\n\nmodule.exports = function heading(state, startLine, endLine, silent) {\n  var ch, level, tmp, token,\n      pos = state.bMarks[startLine] + state.tShift[startLine],\n      max = state.eMarks[startLine];\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false; }\n\n  ch  = state.src.charCodeAt(pos);\n\n  if (ch !== 0x23/* # */ || pos >= max) { return false; }\n\n  // count heading level\n  level = 1;\n  ch = state.src.charCodeAt(++pos);\n  while (ch === 0x23/* # */ && pos < max && level <= 6) {\n    level++;\n    ch = state.src.charCodeAt(++pos);\n  }\n\n  if (level > 6 || (pos < max && !isSpace(ch))) { return false; }\n\n  if (silent) { return true; }\n\n  // Let's cut tails like '    ###  ' from the end of string\n\n  max = state.skipSpacesBack(max, pos);\n  tmp = state.skipCharsBack(max, 0x23, pos); // #\n  if (tmp > pos && isSpace(state.src.charCodeAt(tmp - 1))) {\n    max = tmp;\n  }\n\n  state.line = startLine + 1;\n\n  token        = state.push('heading_open', 'h' + String(level), 1);\n  token.markup = '########'.slice(0, level);\n  token.map    = [ startLine, state.line ];\n\n  token          = state.push('inline', '', 0);\n  token.content  = state.src.slice(pos, max).trim();\n  token.map      = [ startLine, state.line ];\n  token.children = [];\n\n  token        = state.push('heading_close', 'h' + String(level), -1);\n  token.markup = '########'.slice(0, level);\n\n  return true;\n};\n", "// lheading (---, ===)\n\n'use strict';\n\n\nmodule.exports = function lheading(state, startLine, endLine/*, silent*/) {\n  var content, terminate, i, l, token, pos, max, level, marker,\n      nextLine = startLine + 1, oldParentType,\n      terminatorRules = state.md.block.ruler.getRules('paragraph');\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false; }\n\n  oldParentType = state.parentType;\n  state.parentType = 'paragraph'; // use paragraph to match terminatorRules\n\n  // jump line-by-line until empty one or EOF\n  for (; nextLine < endLine && !state.isEmpty(nextLine); nextLine++) {\n    // this would be a code block normally, but after paragraph\n    // it's considered a lazy continuation regardless of what's there\n    if (state.sCount[nextLine] - state.blkIndent > 3) { continue; }\n\n    //\n    // Check for underline in setext header\n    //\n    if (state.sCount[nextLine] >= state.blkIndent) {\n      pos = state.bMarks[nextLine] + state.tShift[nextLine];\n      max = state.eMarks[nextLine];\n\n      if (pos < max) {\n        marker = state.src.charCodeAt(pos);\n\n        if (marker === 0x2D/* - */ || marker === 0x3D/* = */) {\n          pos = state.skipChars(pos, marker);\n          pos = state.skipSpaces(pos);\n\n          if (pos >= max) {\n            level = (marker === 0x3D/* = */ ? 1 : 2);\n            break;\n          }\n        }\n      }\n    }\n\n    // quirk for blockquotes, this line should already be checked by that rule\n    if (state.sCount[nextLine] < 0) { continue; }\n\n    // Some tags can terminate paragraph without empty line.\n    terminate = false;\n    for (i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true;\n        break;\n      }\n    }\n    if (terminate) { break; }\n  }\n\n  if (!level) {\n    // Didn't find valid underline\n    return false;\n  }\n\n  content = state.getLines(startLine, nextLine, state.blkIndent, false).trim();\n\n  state.line = nextLine + 1;\n\n  token          = state.push('heading_open', 'h' + String(level), 1);\n  token.markup   = String.fromCharCode(marker);\n  token.map      = [ startLine, state.line ];\n\n  token          = state.push('inline', '', 0);\n  token.content  = content;\n  token.map      = [ startLine, state.line - 1 ];\n  token.children = [];\n\n  token          = state.push('heading_close', 'h' + String(level), -1);\n  token.markup   = String.fromCharCode(marker);\n\n  state.parentType = oldParentType;\n\n  return true;\n};\n", "// Paragraph\n\n'use strict';\n\n\nmodule.exports = function paragraph(state, startLine/*, endLine*/) {\n  var content, terminate, i, l, token, oldParentType,\n      nextLine = startLine + 1,\n      terminatorRules = state.md.block.ruler.getRules('paragraph'),\n      endLine = state.lineMax;\n\n  oldParentType = state.parentType;\n  state.parentType = 'paragraph';\n\n  // jump line-by-line until empty one or EOF\n  for (; nextLine < endLine && !state.isEmpty(nextLine); nextLine++) {\n    // this would be a code block normally, but after paragraph\n    // it's considered a lazy continuation regardless of what's there\n    if (state.sCount[nextLine] - state.blkIndent > 3) { continue; }\n\n    // quirk for blockquotes, this line should already be checked by that rule\n    if (state.sCount[nextLine] < 0) { continue; }\n\n    // Some tags can terminate paragraph without empty line.\n    terminate = false;\n    for (i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true;\n        break;\n      }\n    }\n    if (terminate) { break; }\n  }\n\n  content = state.getLines(startLine, nextLine, state.blkIndent, false).trim();\n\n  state.line = nextLine;\n\n  token          = state.push('paragraph_open', 'p', 1);\n  token.map      = [ startLine, state.line ];\n\n  token          = state.push('inline', '', 0);\n  token.content  = content;\n  token.map      = [ startLine, state.line ];\n  token.children = [];\n\n  token          = state.push('paragraph_close', 'p', -1);\n\n  state.parentType = oldParentType;\n\n  return true;\n};\n", "// Parser state class\n\n'use strict';\n\nvar Token = require('../token');\nvar isSpace = require('../common/utils').isSpace;\n\n\nfunction StateBlock(src, md, env, tokens) {\n  var ch, s, start, pos, len, indent, offset, indent_found;\n\n  this.src = src;\n\n  // link to parser instance\n  this.md     = md;\n\n  this.env = env;\n\n  //\n  // Internal state vartiables\n  //\n\n  this.tokens = tokens;\n\n  this.bMarks = [];  // line begin offsets for fast jumps\n  this.eMarks = [];  // line end offsets for fast jumps\n  this.tShift = [];  // offsets of the first non-space characters (tabs not expanded)\n  this.sCount = [];  // indents for each line (tabs expanded)\n\n  // An amount of virtual spaces (tabs expanded) between beginning\n  // of each line (bMarks) and real beginning of that line.\n  //\n  // It exists only as a hack because blockquotes override bMarks\n  // losing information in the process.\n  //\n  // It's used only when expanding tabs, you can think about it as\n  // an initial tab length, e.g. bsCount=21 applied to string `\\t123`\n  // means first tab should be expanded to 4-21%4 === 3 spaces.\n  //\n  this.bsCount = [];\n\n  // block parser variables\n  this.blkIndent  = 0; // required block content indent (for example, if we are\n                       // inside a list, it would be positioned after list marker)\n  this.line       = 0; // line index in src\n  this.lineMax    = 0; // lines count\n  this.tight      = false;  // loose/tight mode for lists\n  this.ddIndent   = -1; // indent of the current dd block (-1 if there isn't any)\n  this.listIndent = -1; // indent of the current list block (-1 if there isn't any)\n\n  // can be 'blockquote', 'list', 'root', 'paragraph' or 'reference'\n  // used in lists to determine if they interrupt a paragraph\n  this.parentType = 'root';\n\n  this.level = 0;\n\n  // renderer\n  this.result = '';\n\n  // Create caches\n  // Generate markers.\n  s = this.src;\n  indent_found = false;\n\n  for (start = pos = indent = offset = 0, len = s.length; pos < len; pos++) {\n    ch = s.charCodeAt(pos);\n\n    if (!indent_found) {\n      if (isSpace(ch)) {\n        indent++;\n\n        if (ch === 0x09) {\n          offset += 4 - offset % 4;\n        } else {\n          offset++;\n        }\n        continue;\n      } else {\n        indent_found = true;\n      }\n    }\n\n    if (ch === 0x0A || pos === len - 1) {\n      if (ch !== 0x0A) { pos++; }\n      this.bMarks.push(start);\n      this.eMarks.push(pos);\n      this.tShift.push(indent);\n      this.sCount.push(offset);\n      this.bsCount.push(0);\n\n      indent_found = false;\n      indent = 0;\n      offset = 0;\n      start = pos + 1;\n    }\n  }\n\n  // Push fake entry to simplify cache bounds checks\n  this.bMarks.push(s.length);\n  this.eMarks.push(s.length);\n  this.tShift.push(0);\n  this.sCount.push(0);\n  this.bsCount.push(0);\n\n  this.lineMax = this.bMarks.length - 1; // don't count last fake line\n}\n\n// Push new token to \"stream\".\n//\nStateBlock.prototype.push = function (type, tag, nesting) {\n  var token = new Token(type, tag, nesting);\n  token.block = true;\n\n  if (nesting < 0) this.level--; // closing tag\n  token.level = this.level;\n  if (nesting > 0) this.level++; // opening tag\n\n  this.tokens.push(token);\n  return token;\n};\n\nStateBlock.prototype.isEmpty = function isEmpty(line) {\n  return this.bMarks[line] + this.tShift[line] >= this.eMarks[line];\n};\n\nStateBlock.prototype.skipEmptyLines = function skipEmptyLines(from) {\n  for (var max = this.lineMax; from < max; from++) {\n    if (this.bMarks[from] + this.tShift[from] < this.eMarks[from]) {\n      break;\n    }\n  }\n  return from;\n};\n\n// Skip spaces from given position.\nStateBlock.prototype.skipSpaces = function skipSpaces(pos) {\n  var ch;\n\n  for (var max = this.src.length; pos < max; pos++) {\n    ch = this.src.charCodeAt(pos);\n    if (!isSpace(ch)) { break; }\n  }\n  return pos;\n};\n\n// Skip spaces from given position in reverse.\nStateBlock.prototype.skipSpacesBack = function skipSpacesBack(pos, min) {\n  if (pos <= min) { return pos; }\n\n  while (pos > min) {\n    if (!isSpace(this.src.charCodeAt(--pos))) { return pos + 1; }\n  }\n  return pos;\n};\n\n// Skip char codes from given position\nStateBlock.prototype.skipChars = function skipChars(pos, code) {\n  for (var max = this.src.length; pos < max; pos++) {\n    if (this.src.charCodeAt(pos) !== code) { break; }\n  }\n  return pos;\n};\n\n// Skip char codes reverse from given position - 1\nStateBlock.prototype.skipCharsBack = function skipCharsBack(pos, code, min) {\n  if (pos <= min) { return pos; }\n\n  while (pos > min) {\n    if (code !== this.src.charCodeAt(--pos)) { return pos + 1; }\n  }\n  return pos;\n};\n\n// cut lines range from source.\nStateBlock.prototype.getLines = function getLines(begin, end, indent, keepLastLF) {\n  var i, lineIndent, ch, first, last, queue, lineStart,\n      line = begin;\n\n  if (begin >= end) {\n    return '';\n  }\n\n  queue = new Array(end - begin);\n\n  for (i = 0; line < end; line++, i++) {\n    lineIndent = 0;\n    lineStart = first = this.bMarks[line];\n\n    if (line + 1 < end || keepLastLF) {\n      // No need for bounds check because we have fake entry on tail.\n      last = this.eMarks[line] + 1;\n    } else {\n      last = this.eMarks[line];\n    }\n\n    while (first < last && lineIndent < indent) {\n      ch = this.src.charCodeAt(first);\n\n      if (isSpace(ch)) {\n        if (ch === 0x09) {\n          lineIndent += 4 - (lineIndent + this.bsCount[line]) % 4;\n        } else {\n          lineIndent++;\n        }\n      } else if (first - lineStart < this.tShift[line]) {\n        // patched tShift masked characters to look like spaces (blockquotes, list markers)\n        lineIndent++;\n      } else {\n        break;\n      }\n\n      first++;\n    }\n\n    if (lineIndent > indent) {\n      // partially expanding tabs in code blocks, e.g '\\t\\tfoobar'\n      // with indent=2 becomes '  \\tfoobar'\n      queue[i] = new Array(lineIndent - indent + 1).join(' ') + this.src.slice(first, last);\n    } else {\n      queue[i] = this.src.slice(first, last);\n    }\n  }\n\n  return queue.join('');\n};\n\n// re-export Token class to use in block rules\nStateBlock.prototype.Token = Token;\n\n\nmodule.exports = StateBlock;\n", "/** internal\n * class ParserBlock\n *\n * Block-level tokenizer.\n **/\n'use strict';\n\n\nvar Ruler           = require('./ruler');\n\n\nvar _rules = [\n  // First 2 params - rule name & source. Secondary array - list of rules,\n  // which can be terminated by this one.\n  [ 'table',      require('./rules_block/table'),      [ 'paragraph', 'reference' ] ],\n  [ 'code',       require('./rules_block/code') ],\n  [ 'fence',      require('./rules_block/fence'),      [ 'paragraph', 'reference', 'blockquote', 'list' ] ],\n  [ 'blockquote', require('./rules_block/blockquote'), [ 'paragraph', 'reference', 'blockquote', 'list' ] ],\n  [ 'hr',         require('./rules_block/hr'),         [ 'paragraph', 'reference', 'blockquote', 'list' ] ],\n  [ 'list',       require('./rules_block/list'),       [ 'paragraph', 'reference', 'blockquote' ] ],\n  [ 'reference',  require('./rules_block/reference') ],\n  [ 'html_block', require('./rules_block/html_block'), [ 'paragraph', 'reference', 'blockquote' ] ],\n  [ 'heading',    require('./rules_block/heading'),    [ 'paragraph', 'reference', 'blockquote' ] ],\n  [ 'lheading',   require('./rules_block/lheading') ],\n  [ 'paragraph',  require('./rules_block/paragraph') ]\n];\n\n\n/**\n * new ParserBlock()\n **/\nfunction ParserBlock() {\n  /**\n   * ParserBlock#ruler -> Ruler\n   *\n   * [[Ruler]] instance. Keep configuration of block rules.\n   **/\n  this.ruler = new Ruler();\n\n  for (var i = 0; i < _rules.length; i++) {\n    this.ruler.push(_rules[i][0], _rules[i][1], { alt: (_rules[i][2] || []).slice() });\n  }\n}\n\n\n// Generate tokens for input range\n//\nParserBlock.prototype.tokenize = function (state, startLine, endLine) {\n  var ok, i,\n      rules = this.ruler.getRules(''),\n      len = rules.length,\n      line = startLine,\n      hasEmptyLines = false,\n      maxNesting = state.md.options.maxNesting;\n\n  while (line < endLine) {\n    state.line = line = state.skipEmptyLines(line);\n    if (line >= endLine) { break; }\n\n    // Termination condition for nested calls.\n    // Nested calls currently used for blockquotes & lists\n    if (state.sCount[line] < state.blkIndent) { break; }\n\n    // If nesting level exceeded - skip tail to the end. That's not ordinary\n    // situation and we should not care about content.\n    if (state.level >= maxNesting) {\n      state.line = endLine;\n      break;\n    }\n\n    // Try all possible rules.\n    // On success, rule should:\n    //\n    // - update `state.line`\n    // - update `state.tokens`\n    // - return true\n\n    for (i = 0; i < len; i++) {\n      ok = rules[i](state, line, endLine, false);\n      if (ok) { break; }\n    }\n\n    // set state.tight if we had an empty line before current tag\n    // i.e. latest empty line should not count\n    state.tight = !hasEmptyLines;\n\n    // paragraph might \"eat\" one newline after it in nested lists\n    if (state.isEmpty(state.line - 1)) {\n      hasEmptyLines = true;\n    }\n\n    line = state.line;\n\n    if (line < endLine && state.isEmpty(line)) {\n      hasEmptyLines = true;\n      line++;\n      state.line = line;\n    }\n  }\n};\n\n\n/**\n * ParserBlock.parse(str, md, env, outTokens)\n *\n * Process input string and push block tokens into `outTokens`\n **/\nParserBlock.prototype.parse = function (src, md, env, outTokens) {\n  var state;\n\n  if (!src) { return; }\n\n  state = new this.State(src, md, env, outTokens);\n\n  this.tokenize(state, state.line, state.lineMax);\n};\n\n\nParserBlock.prototype.State = require('./rules_block/state_block');\n\n\nmodule.exports = ParserBlock;\n", "// Skip text characters for text token, place those to pending buffer\n// and increment current pos\n\n'use strict';\n\n\n// Rule to skip pure text\n// '{}$%@~+=:' reserved for extentions\n\n// !, \", #, $, %, &, ', (, ), *, +, ,, -, ., /, :, ;, <, =, >, ?, @, [, \\, ], ^, _, `, {, |, }, or ~\n\n// !!!! Don't confuse with \"Markdown ASCII Punctuation\" chars\n// http://spec.commonmark.org/0.15/#ascii-punctuation-character\nfunction isTerminatorChar(ch) {\n  switch (ch) {\n    case 0x0A/* \\n */:\n    case 0x21/* ! */:\n    case 0x23/* # */:\n    case 0x24/* $ */:\n    case 0x25/* % */:\n    case 0x26/* & */:\n    case 0x2A/* * */:\n    case 0x2B/* + */:\n    case 0x2D/* - */:\n    case 0x3A/* : */:\n    case 0x3C/* < */:\n    case 0x3D/* = */:\n    case 0x3E/* > */:\n    case 0x40/* @ */:\n    case 0x5B/* [ */:\n    case 0x5C/* \\ */:\n    case 0x5D/* ] */:\n    case 0x5E/* ^ */:\n    case 0x5F/* _ */:\n    case 0x60/* ` */:\n    case 0x7B/* { */:\n    case 0x7D/* } */:\n    case 0x7E/* ~ */:\n      return true;\n    default:\n      return false;\n  }\n}\n\nmodule.exports = function text(state, silent) {\n  var pos = state.pos;\n\n  while (pos < state.posMax && !isTerminatorChar(state.src.charCodeAt(pos))) {\n    pos++;\n  }\n\n  if (pos === state.pos) { return false; }\n\n  if (!silent) { state.pending += state.src.slice(state.pos, pos); }\n\n  state.pos = pos;\n\n  return true;\n};\n\n// Alternative implementation, for memory.\n//\n// It costs 10% of performance, but allows extend terminators list, if place it\n// to `ParcerInline` property. Probably, will switch to it sometime, such\n// flexibility required.\n\n/*\nvar TERMINATOR_RE = /[\\n!#$%&*+\\-:<=>@[\\\\\\]^_`{}~]/;\n\nmodule.exports = function text(state, silent) {\n  var pos = state.pos,\n      idx = state.src.slice(pos).search(TERMINATOR_RE);\n\n  // first char is terminator -> empty text\n  if (idx === 0) { return false; }\n\n  // no terminator -> text till end of string\n  if (idx < 0) {\n    if (!silent) { state.pending += state.src.slice(pos); }\n    state.pos = state.src.length;\n    return true;\n  }\n\n  if (!silent) { state.pending += state.src.slice(pos, pos + idx); }\n\n  state.pos += idx;\n\n  return true;\n};*/\n", "// Proceess '\\n'\n\n'use strict';\n\nvar isSpace = require('../common/utils').isSpace;\n\n\nmodule.exports = function newline(state, silent) {\n  var pmax, max, ws, pos = state.pos;\n\n  if (state.src.charCodeAt(pos) !== 0x0A/* \\n */) { return false; }\n\n  pmax = state.pending.length - 1;\n  max = state.posMax;\n\n  // '  \\n' -> hardbreak\n  // Lookup in pending chars is bad practice! Don't copy to other rules!\n  // Pending string is stored in concat mode, indexed lookups will cause\n  // convertion to flat mode.\n  if (!silent) {\n    if (pmax >= 0 && state.pending.charCodeAt(pmax) === 0x20) {\n      if (pmax >= 1 && state.pending.charCodeAt(pmax - 1) === 0x20) {\n        // Find whitespaces tail of pending chars.\n        ws = pmax - 1;\n        while (ws >= 1 && state.pending.charCodeAt(ws - 1) === 0x20) ws--;\n\n        state.pending = state.pending.slice(0, ws);\n        state.push('hardbreak', 'br', 0);\n      } else {\n        state.pending = state.pending.slice(0, -1);\n        state.push('softbreak', 'br', 0);\n      }\n\n    } else {\n      state.push('softbreak', 'br', 0);\n    }\n  }\n\n  pos++;\n\n  // skip heading spaces for next line\n  while (pos < max && isSpace(state.src.charCodeAt(pos))) { pos++; }\n\n  state.pos = pos;\n  return true;\n};\n", "// Process escaped chars and hardbreaks\n\n'use strict';\n\nvar isSpace = require('../common/utils').isSpace;\n\nvar ESCAPED = [];\n\nfor (var i = 0; i < 256; i++) { ESCAPED.push(0); }\n\n'\\\\!\"#$%&\\'()*+,./:;<=>?@[]^_`{|}~-'\n  .split('').forEach(function (ch) { ESCAPED[ch.charCodeAt(0)] = 1; });\n\n\nmodule.exports = function escape(state, silent) {\n  var ch, pos = state.pos, max = state.posMax;\n\n  if (state.src.charCodeAt(pos) !== 0x5C/* \\ */) { return false; }\n\n  pos++;\n\n  if (pos < max) {\n    ch = state.src.charCodeAt(pos);\n\n    if (ch < 256 && ESCAPED[ch] !== 0) {\n      if (!silent) { state.pending += state.src[pos]; }\n      state.pos += 2;\n      return true;\n    }\n\n    if (ch === 0x0A) {\n      if (!silent) {\n        state.push('hardbreak', 'br', 0);\n      }\n\n      pos++;\n      // skip leading whitespaces from next line\n      while (pos < max) {\n        ch = state.src.charCodeAt(pos);\n        if (!isSpace(ch)) { break; }\n        pos++;\n      }\n\n      state.pos = pos;\n      return true;\n    }\n  }\n\n  if (!silent) { state.pending += '\\\\'; }\n  state.pos++;\n  return true;\n};\n", "// Parse backticks\n\n'use strict';\n\n\nmodule.exports = function backtick(state, silent) {\n  var start, max, marker, token, matchStart, matchEnd, openerLength, closerLength,\n      pos = state.pos,\n      ch = state.src.charCodeAt(pos);\n\n  if (ch !== 0x60/* ` */) { return false; }\n\n  start = pos;\n  pos++;\n  max = state.posMax;\n\n  // scan marker length\n  while (pos < max && state.src.charCodeAt(pos) === 0x60/* ` */) { pos++; }\n\n  marker = state.src.slice(start, pos);\n  openerLength = marker.length;\n\n  if (state.backticksScanned && (state.backticks[openerLength] || 0) <= start) {\n    if (!silent) state.pending += marker;\n    state.pos += openerLength;\n    return true;\n  }\n\n  matchStart = matchEnd = pos;\n\n  // Nothing found in the cache, scan until the end of the line (or until marker is found)\n  while ((matchStart = state.src.indexOf('`', matchEnd)) !== -1) {\n    matchEnd = matchStart + 1;\n\n    // scan marker length\n    while (matchEnd < max && state.src.charCodeAt(matchEnd) === 0x60/* ` */) { matchEnd++; }\n\n    closerLength = matchEnd - matchStart;\n\n    if (closerLength === openerLength) {\n      // Found matching closer length.\n      if (!silent) {\n        token     = state.push('code_inline', 'code', 0);\n        token.markup  = marker;\n        token.content = state.src.slice(pos, matchStart)\n          .replace(/\\n/g, ' ')\n          .replace(/^ (.+) $/, '$1');\n      }\n      state.pos = matchEnd;\n      return true;\n    }\n\n    // Some different length found, put it in cache as upper limit of where closer can be found\n    state.backticks[closerLength] = matchStart;\n  }\n\n  // Scanned through the end, didn't find anything\n  state.backticksScanned = true;\n\n  if (!silent) state.pending += marker;\n  state.pos += openerLength;\n  return true;\n};\n", "// ~~strike through~~\n//\n'use strict';\n\n\n// Insert each marker as a separate text token, and add it to delimiter list\n//\nmodule.exports.tokenize = function strikethrough(state, silent) {\n  var i, scanned, token, len, ch,\n      start = state.pos,\n      marker = state.src.charCodeAt(start);\n\n  if (silent) { return false; }\n\n  if (marker !== 0x7E/* ~ */) { return false; }\n\n  scanned = state.scanDelims(state.pos, true);\n  len = scanned.length;\n  ch = String.fromCharCode(marker);\n\n  if (len < 2) { return false; }\n\n  if (len % 2) {\n    token         = state.push('text', '', 0);\n    token.content = ch;\n    len--;\n  }\n\n  for (i = 0; i < len; i += 2) {\n    token         = state.push('text', '', 0);\n    token.content = ch + ch;\n\n    state.delimiters.push({\n      marker: marker,\n      length: 0,     // disable \"rule of 3\" length checks meant for emphasis\n      token:  state.tokens.length - 1,\n      end:    -1,\n      open:   scanned.can_open,\n      close:  scanned.can_close\n    });\n  }\n\n  state.pos += scanned.length;\n\n  return true;\n};\n\n\nfunction postProcess(state, delimiters) {\n  var i, j,\n      startDelim,\n      endDelim,\n      token,\n      loneMarkers = [],\n      max = delimiters.length;\n\n  for (i = 0; i < max; i++) {\n    startDelim = delimiters[i];\n\n    if (startDelim.marker !== 0x7E/* ~ */) {\n      continue;\n    }\n\n    if (startDelim.end === -1) {\n      continue;\n    }\n\n    endDelim = delimiters[startDelim.end];\n\n    token         = state.tokens[startDelim.token];\n    token.type    = 's_open';\n    token.tag     = 's';\n    token.nesting = 1;\n    token.markup  = '~~';\n    token.content = '';\n\n    token         = state.tokens[endDelim.token];\n    token.type    = 's_close';\n    token.tag     = 's';\n    token.nesting = -1;\n    token.markup  = '~~';\n    token.content = '';\n\n    if (state.tokens[endDelim.token - 1].type === 'text' &&\n        state.tokens[endDelim.token - 1].content === '~') {\n\n      loneMarkers.push(endDelim.token - 1);\n    }\n  }\n\n  // If a marker sequence has an odd number of characters, it's splitted\n  // like this: `~~~~~` -> `~` + `~~` + `~~`, leaving one marker at the\n  // start of the sequence.\n  //\n  // So, we have to move all those markers after subsequent s_close tags.\n  //\n  while (loneMarkers.length) {\n    i = loneMarkers.pop();\n    j = i + 1;\n\n    while (j < state.tokens.length && state.tokens[j].type === 's_close') {\n      j++;\n    }\n\n    j--;\n\n    if (i !== j) {\n      token = state.tokens[j];\n      state.tokens[j] = state.tokens[i];\n      state.tokens[i] = token;\n    }\n  }\n}\n\n\n// Walk through delimiter list and replace text tokens with tags\n//\nmodule.exports.postProcess = function strikethrough(state) {\n  var curr,\n      tokens_meta = state.tokens_meta,\n      max = state.tokens_meta.length;\n\n  postProcess(state, state.delimiters);\n\n  for (curr = 0; curr < max; curr++) {\n    if (tokens_meta[curr] && tokens_meta[curr].delimiters) {\n      postProcess(state, tokens_meta[curr].delimiters);\n    }\n  }\n};\n", "// Process *this* and _that_\n//\n'use strict';\n\n\n// Insert each marker as a separate text token, and add it to delimiter list\n//\nmodule.exports.tokenize = function emphasis(state, silent) {\n  var i, scanned, token,\n      start = state.pos,\n      marker = state.src.charCodeAt(start);\n\n  if (silent) { return false; }\n\n  if (marker !== 0x5F /* _ */ && marker !== 0x2A /* * */) { return false; }\n\n  scanned = state.scanDelims(state.pos, marker === 0x2A);\n\n  for (i = 0; i < scanned.length; i++) {\n    token         = state.push('text', '', 0);\n    token.content = String.fromCharCode(marker);\n\n    state.delimiters.push({\n      // Char code of the starting marker (number).\n      //\n      marker: marker,\n\n      // Total length of these series of delimiters.\n      //\n      length: scanned.length,\n\n      // A position of the token this delimiter corresponds to.\n      //\n      token:  state.tokens.length - 1,\n\n      // If this delimiter is matched as a valid opener, `end` will be\n      // equal to its position, otherwise it's `-1`.\n      //\n      end:    -1,\n\n      // Boolean flags that determine if this delimiter could open or close\n      // an emphasis.\n      //\n      open:   scanned.can_open,\n      close:  scanned.can_close\n    });\n  }\n\n  state.pos += scanned.length;\n\n  return true;\n};\n\n\nfunction postProcess(state, delimiters) {\n  var i,\n      startDelim,\n      endDelim,\n      token,\n      ch,\n      isStrong,\n      max = delimiters.length;\n\n  for (i = max - 1; i >= 0; i--) {\n    startDelim = delimiters[i];\n\n    if (startDelim.marker !== 0x5F/* _ */ && startDelim.marker !== 0x2A/* * */) {\n      continue;\n    }\n\n    // Process only opening markers\n    if (startDelim.end === -1) {\n      continue;\n    }\n\n    endDelim = delimiters[startDelim.end];\n\n    // If the previous delimiter has the same marker and is adjacent to this one,\n    // merge those into one strong delimiter.\n    //\n    // `<em><em>whatever</em></em>` -> `<strong>whatever</strong>`\n    //\n    isStrong = i > 0 &&\n               delimiters[i - 1].end === startDelim.end + 1 &&\n               // check that first two markers match and adjacent\n               delimiters[i - 1].marker === startDelim.marker &&\n               delimiters[i - 1].token === startDelim.token - 1 &&\n               // check that last two markers are adjacent (we can safely assume they match)\n               delimiters[startDelim.end + 1].token === endDelim.token + 1;\n\n    ch = String.fromCharCode(startDelim.marker);\n\n    token         = state.tokens[startDelim.token];\n    token.type    = isStrong ? 'strong_open' : 'em_open';\n    token.tag     = isStrong ? 'strong' : 'em';\n    token.nesting = 1;\n    token.markup  = isStrong ? ch + ch : ch;\n    token.content = '';\n\n    token         = state.tokens[endDelim.token];\n    token.type    = isStrong ? 'strong_close' : 'em_close';\n    token.tag     = isStrong ? 'strong' : 'em';\n    token.nesting = -1;\n    token.markup  = isStrong ? ch + ch : ch;\n    token.content = '';\n\n    if (isStrong) {\n      state.tokens[delimiters[i - 1].token].content = '';\n      state.tokens[delimiters[startDelim.end + 1].token].content = '';\n      i--;\n    }\n  }\n}\n\n\n// Walk through delimiter list and replace text tokens with tags\n//\nmodule.exports.postProcess = function emphasis(state) {\n  var curr,\n      tokens_meta = state.tokens_meta,\n      max = state.tokens_meta.length;\n\n  postProcess(state, state.delimiters);\n\n  for (curr = 0; curr < max; curr++) {\n    if (tokens_meta[curr] && tokens_meta[curr].delimiters) {\n      postProcess(state, tokens_meta[curr].delimiters);\n    }\n  }\n};\n", "// Process [link](<to> \"stuff\")\n\n'use strict';\n\nvar normalizeReference   = require('../common/utils').normalizeReference;\nvar isSpace              = require('../common/utils').isSpace;\n\n\nmodule.exports = function link(state, silent) {\n  var attrs,\n      code,\n      label,\n      labelEnd,\n      labelStart,\n      pos,\n      res,\n      ref,\n      token,\n      href = '',\n      title = '',\n      oldPos = state.pos,\n      max = state.posMax,\n      start = state.pos,\n      parseReference = true;\n\n  if (state.src.charCodeAt(state.pos) !== 0x5B/* [ */) { return false; }\n\n  labelStart = state.pos + 1;\n  labelEnd = state.md.helpers.parseLinkLabel(state, state.pos, true);\n\n  // parser failed to find ']', so it's not a valid link\n  if (labelEnd < 0) { return false; }\n\n  pos = labelEnd + 1;\n  if (pos < max && state.src.charCodeAt(pos) === 0x28/* ( */) {\n    //\n    // Inline link\n    //\n\n    // might have found a valid shortcut link, disable reference parsing\n    parseReference = false;\n\n    // [link](  <href>  \"title\"  )\n    //        ^^ skipping these spaces\n    pos++;\n    for (; pos < max; pos++) {\n      code = state.src.charCodeAt(pos);\n      if (!isSpace(code) && code !== 0x0A) { break; }\n    }\n    if (pos >= max) { return false; }\n\n    // [link](  <href>  \"title\"  )\n    //          ^^^^^^ parsing link destination\n    start = pos;\n    res = state.md.helpers.parseLinkDestination(state.src, pos, state.posMax);\n    if (res.ok) {\n      href = state.md.normalizeLink(res.str);\n      if (state.md.validateLink(href)) {\n        pos = res.pos;\n      } else {\n        href = '';\n      }\n\n      // [link](  <href>  \"title\"  )\n      //                ^^ skipping these spaces\n      start = pos;\n      for (; pos < max; pos++) {\n        code = state.src.charCodeAt(pos);\n        if (!isSpace(code) && code !== 0x0A) { break; }\n      }\n\n      // [link](  <href>  \"title\"  )\n      //                  ^^^^^^^ parsing link title\n      res = state.md.helpers.parseLinkTitle(state.src, pos, state.posMax);\n      if (pos < max && start !== pos && res.ok) {\n        title = res.str;\n        pos = res.pos;\n\n        // [link](  <href>  \"title\"  )\n        //                         ^^ skipping these spaces\n        for (; pos < max; pos++) {\n          code = state.src.charCodeAt(pos);\n          if (!isSpace(code) && code !== 0x0A) { break; }\n        }\n      }\n    }\n\n    if (pos >= max || state.src.charCodeAt(pos) !== 0x29/* ) */) {\n      // parsing a valid shortcut link failed, fallback to reference\n      parseReference = true;\n    }\n    pos++;\n  }\n\n  if (parseReference) {\n    //\n    // Link reference\n    //\n    if (typeof state.env.references === 'undefined') { return false; }\n\n    if (pos < max && state.src.charCodeAt(pos) === 0x5B/* [ */) {\n      start = pos + 1;\n      pos = state.md.helpers.parseLinkLabel(state, pos);\n      if (pos >= 0) {\n        label = state.src.slice(start, pos++);\n      } else {\n        pos = labelEnd + 1;\n      }\n    } else {\n      pos = labelEnd + 1;\n    }\n\n    // covers label === '' and label === undefined\n    // (collapsed reference link and shortcut reference link respectively)\n    if (!label) { label = state.src.slice(labelStart, labelEnd); }\n\n    ref = state.env.references[normalizeReference(label)];\n    if (!ref) {\n      state.pos = oldPos;\n      return false;\n    }\n    href = ref.href;\n    title = ref.title;\n  }\n\n  //\n  // We found the end of the link, and know for a fact it's a valid link;\n  // so all that's left to do is to call tokenizer.\n  //\n  if (!silent) {\n    state.pos = labelStart;\n    state.posMax = labelEnd;\n\n    token        = state.push('link_open', 'a', 1);\n    token.attrs  = attrs = [ [ 'href', href ] ];\n    if (title) {\n      attrs.push([ 'title', title ]);\n    }\n\n    state.md.inline.tokenize(state);\n\n    token        = state.push('link_close', 'a', -1);\n  }\n\n  state.pos = pos;\n  state.posMax = max;\n  return true;\n};\n", "// Process ![image](<src> \"title\")\n\n'use strict';\n\nvar normalizeReference   = require('../common/utils').normalizeReference;\nvar isSpace              = require('../common/utils').isSpace;\n\n\nmodule.exports = function image(state, silent) {\n  var attrs,\n      code,\n      content,\n      label,\n      labelEnd,\n      labelStart,\n      pos,\n      ref,\n      res,\n      title,\n      token,\n      tokens,\n      start,\n      href = '',\n      oldPos = state.pos,\n      max = state.posMax;\n\n  if (state.src.charCodeAt(state.pos) !== 0x21/* ! */) { return false; }\n  if (state.src.charCodeAt(state.pos + 1) !== 0x5B/* [ */) { return false; }\n\n  labelStart = state.pos + 2;\n  labelEnd = state.md.helpers.parseLinkLabel(state, state.pos + 1, false);\n\n  // parser failed to find ']', so it's not a valid link\n  if (labelEnd < 0) { return false; }\n\n  pos = labelEnd + 1;\n  if (pos < max && state.src.charCodeAt(pos) === 0x28/* ( */) {\n    //\n    // Inline link\n    //\n\n    // [link](  <href>  \"title\"  )\n    //        ^^ skipping these spaces\n    pos++;\n    for (; pos < max; pos++) {\n      code = state.src.charCodeAt(pos);\n      if (!isSpace(code) && code !== 0x0A) { break; }\n    }\n    if (pos >= max) { return false; }\n\n    // [link](  <href>  \"title\"  )\n    //          ^^^^^^ parsing link destination\n    start = pos;\n    res = state.md.helpers.parseLinkDestination(state.src, pos, state.posMax);\n    if (res.ok) {\n      href = state.md.normalizeLink(res.str);\n      if (state.md.validateLink(href)) {\n        pos = res.pos;\n      } else {\n        href = '';\n      }\n    }\n\n    // [link](  <href>  \"title\"  )\n    //                ^^ skipping these spaces\n    start = pos;\n    for (; pos < max; pos++) {\n      code = state.src.charCodeAt(pos);\n      if (!isSpace(code) && code !== 0x0A) { break; }\n    }\n\n    // [link](  <href>  \"title\"  )\n    //                  ^^^^^^^ parsing link title\n    res = state.md.helpers.parseLinkTitle(state.src, pos, state.posMax);\n    if (pos < max && start !== pos && res.ok) {\n      title = res.str;\n      pos = res.pos;\n\n      // [link](  <href>  \"title\"  )\n      //                         ^^ skipping these spaces\n      for (; pos < max; pos++) {\n        code = state.src.charCodeAt(pos);\n        if (!isSpace(code) && code !== 0x0A) { break; }\n      }\n    } else {\n      title = '';\n    }\n\n    if (pos >= max || state.src.charCodeAt(pos) !== 0x29/* ) */) {\n      state.pos = oldPos;\n      return false;\n    }\n    pos++;\n  } else {\n    //\n    // Link reference\n    //\n    if (typeof state.env.references === 'undefined') { return false; }\n\n    if (pos < max && state.src.charCodeAt(pos) === 0x5B/* [ */) {\n      start = pos + 1;\n      pos = state.md.helpers.parseLinkLabel(state, pos);\n      if (pos >= 0) {\n        label = state.src.slice(start, pos++);\n      } else {\n        pos = labelEnd + 1;\n      }\n    } else {\n      pos = labelEnd + 1;\n    }\n\n    // covers label === '' and label === undefined\n    // (collapsed reference link and shortcut reference link respectively)\n    if (!label) { label = state.src.slice(labelStart, labelEnd); }\n\n    ref = state.env.references[normalizeReference(label)];\n    if (!ref) {\n      state.pos = oldPos;\n      return false;\n    }\n    href = ref.href;\n    title = ref.title;\n  }\n\n  //\n  // We found the end of the link, and know for a fact it's a valid link;\n  // so all that's left to do is to call tokenizer.\n  //\n  if (!silent) {\n    content = state.src.slice(labelStart, labelEnd);\n\n    state.md.inline.parse(\n      content,\n      state.md,\n      state.env,\n      tokens = []\n    );\n\n    token          = state.push('image', 'img', 0);\n    token.attrs    = attrs = [ [ 'src', href ], [ 'alt', '' ] ];\n    token.children = tokens;\n    token.content  = content;\n\n    if (title) {\n      attrs.push([ 'title', title ]);\n    }\n  }\n\n  state.pos = pos;\n  state.posMax = max;\n  return true;\n};\n", "// Process autolinks '<protocol:...>'\n\n'use strict';\n\n\n/*eslint max-len:0*/\nvar EMAIL_RE    = /^([a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/;\nvar AUTOLINK_RE = /^([a-zA-Z][a-zA-Z0-9+.\\-]{1,31}):([^<>\\x00-\\x20]*)$/;\n\n\nmodule.exports = function autolink(state, silent) {\n  var url, fullUrl, token, ch, start, max,\n      pos = state.pos;\n\n  if (state.src.charCodeAt(pos) !== 0x3C/* < */) { return false; }\n\n  start = state.pos;\n  max = state.posMax;\n\n  for (;;) {\n    if (++pos >= max) return false;\n\n    ch = state.src.charCodeAt(pos);\n\n    if (ch === 0x3C /* < */) return false;\n    if (ch === 0x3E /* > */) break;\n  }\n\n  url = state.src.slice(start + 1, pos);\n\n  if (AUTOLINK_RE.test(url)) {\n    fullUrl = state.md.normalizeLink(url);\n    if (!state.md.validateLink(fullUrl)) { return false; }\n\n    if (!silent) {\n      token         = state.push('link_open', 'a', 1);\n      token.attrs   = [ [ 'href', fullUrl ] ];\n      token.markup  = 'autolink';\n      token.info    = 'auto';\n\n      token         = state.push('text', '', 0);\n      token.content = state.md.normalizeLinkText(url);\n\n      token         = state.push('link_close', 'a', -1);\n      token.markup  = 'autolink';\n      token.info    = 'auto';\n    }\n\n    state.pos += url.length + 2;\n    return true;\n  }\n\n  if (EMAIL_RE.test(url)) {\n    fullUrl = state.md.normalizeLink('mailto:' + url);\n    if (!state.md.validateLink(fullUrl)) { return false; }\n\n    if (!silent) {\n      token         = state.push('link_open', 'a', 1);\n      token.attrs   = [ [ 'href', fullUrl ] ];\n      token.markup  = 'autolink';\n      token.info    = 'auto';\n\n      token         = state.push('text', '', 0);\n      token.content = state.md.normalizeLinkText(url);\n\n      token         = state.push('link_close', 'a', -1);\n      token.markup  = 'autolink';\n      token.info    = 'auto';\n    }\n\n    state.pos += url.length + 2;\n    return true;\n  }\n\n  return false;\n};\n", "// Process html tags\n\n'use strict';\n\n\nvar HTML_TAG_RE = require('../common/html_re').HTML_TAG_RE;\n\n\nfunction isLetter(ch) {\n  /*eslint no-bitwise:0*/\n  var lc = ch | 0x20; // to lower case\n  return (lc >= 0x61/* a */) && (lc <= 0x7a/* z */);\n}\n\n\nmodule.exports = function html_inline(state, silent) {\n  var ch, match, max, token,\n      pos = state.pos;\n\n  if (!state.md.options.html) { return false; }\n\n  // Check start\n  max = state.posMax;\n  if (state.src.charCodeAt(pos) !== 0x3C/* < */ ||\n      pos + 2 >= max) {\n    return false;\n  }\n\n  // Quick fail on second char\n  ch = state.src.charCodeAt(pos + 1);\n  if (ch !== 0x21/* ! */ &&\n      ch !== 0x3F/* ? */ &&\n      ch !== 0x2F/* / */ &&\n      !isLetter(ch)) {\n    return false;\n  }\n\n  match = state.src.slice(pos).match(HTML_TAG_RE);\n  if (!match) { return false; }\n\n  if (!silent) {\n    token         = state.push('html_inline', '', 0);\n    token.content = state.src.slice(pos, pos + match[0].length);\n  }\n  state.pos += match[0].length;\n  return true;\n};\n", "// Process html entity - &#123;, &#xAF;, &quot;, ...\n\n'use strict';\n\nvar entities          = require('../common/entities');\nvar has               = require('../common/utils').has;\nvar isValidEntityCode = require('../common/utils').isValidEntityCode;\nvar fromCodePoint     = require('../common/utils').fromCodePoint;\n\n\nvar DIGITAL_RE = /^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i;\nvar NAMED_RE   = /^&([a-z][a-z0-9]{1,31});/i;\n\n\nmodule.exports = function entity(state, silent) {\n  var ch, code, match, pos = state.pos, max = state.posMax;\n\n  if (state.src.charCodeAt(pos) !== 0x26/* & */) { return false; }\n\n  if (pos + 1 < max) {\n    ch = state.src.charCodeAt(pos + 1);\n\n    if (ch === 0x23 /* # */) {\n      match = state.src.slice(pos).match(DIGITAL_RE);\n      if (match) {\n        if (!silent) {\n          code = match[1][0].toLowerCase() === 'x' ? parseInt(match[1].slice(1), 16) : parseInt(match[1], 10);\n          state.pending += isValidEntityCode(code) ? fromCodePoint(code) : fromCodePoint(0xFFFD);\n        }\n        state.pos += match[0].length;\n        return true;\n      }\n    } else {\n      match = state.src.slice(pos).match(NAMED_RE);\n      if (match) {\n        if (has(entities, match[1])) {\n          if (!silent) { state.pending += entities[match[1]]; }\n          state.pos += match[0].length;\n          return true;\n        }\n      }\n    }\n  }\n\n  if (!silent) { state.pending += '&'; }\n  state.pos++;\n  return true;\n};\n", "// For each opening emphasis-like marker find a matching closing one\n//\n'use strict';\n\n\nfunction processDelimiters(state, delimiters) {\n  var closerIdx, openerIdx, closer, opener, minOpenerIdx, newMinOpenerIdx,\n      isOddMatch, lastJump,\n      openersBottom = {},\n      max = delimiters.length;\n\n  if (!max) return;\n\n  // headerIdx is the first delimiter of the current (where closer is) delimiter run\n  var headerIdx = 0;\n  var lastTokenIdx = -2; // needs any value lower than -1\n  var jumps = [];\n\n  for (closerIdx = 0; closerIdx < max; closerIdx++) {\n    closer = delimiters[closerIdx];\n\n    jumps.push(0);\n\n    // markers belong to same delimiter run if:\n    //  - they have adjacent tokens\n    //  - AND markers are the same\n    //\n    if (delimiters[headerIdx].marker !== closer.marker || lastTokenIdx !== closer.token - 1) {\n      headerIdx = closerIdx;\n    }\n\n    lastTokenIdx = closer.token;\n\n    // Length is only used for emphasis-specific \"rule of 3\",\n    // if it's not defined (in strikethrough or 3rd party plugins),\n    // we can default it to 0 to disable those checks.\n    //\n    closer.length = closer.length || 0;\n\n    if (!closer.close) continue;\n\n    // Previously calculated lower bounds (previous fails)\n    // for each marker, each delimiter length modulo 3,\n    // and for whether this closer can be an opener;\n    // https://github.com/commonmark/cmark/commit/34250e12ccebdc6372b8b49c44fab57c72443460\n    if (!openersBottom.hasOwnProperty(closer.marker)) {\n      openersBottom[closer.marker] = [ -1, -1, -1, -1, -1, -1 ];\n    }\n\n    minOpenerIdx = openersBottom[closer.marker][(closer.open ? 3 : 0) + (closer.length % 3)];\n\n    openerIdx = headerIdx - jumps[headerIdx] - 1;\n\n    newMinOpenerIdx = openerIdx;\n\n    for (; openerIdx > minOpenerIdx; openerIdx -= jumps[openerIdx] + 1) {\n      opener = delimiters[openerIdx];\n\n      if (opener.marker !== closer.marker) continue;\n\n      if (opener.open && opener.end < 0) {\n\n        isOddMatch = false;\n\n        // from spec:\n        //\n        // If one of the delimiters can both open and close emphasis, then the\n        // sum of the lengths of the delimiter runs containing the opening and\n        // closing delimiters must not be a multiple of 3 unless both lengths\n        // are multiples of 3.\n        //\n        if (opener.close || closer.open) {\n          if ((opener.length + closer.length) % 3 === 0) {\n            if (opener.length % 3 !== 0 || closer.length % 3 !== 0) {\n              isOddMatch = true;\n            }\n          }\n        }\n\n        if (!isOddMatch) {\n          // If previous delimiter cannot be an opener, we can safely skip\n          // the entire sequence in future checks. This is required to make\n          // sure algorithm has linear complexity (see *_*_*_*_*_... case).\n          //\n          lastJump = openerIdx > 0 && !delimiters[openerIdx - 1].open ?\n            jumps[openerIdx - 1] + 1 :\n            0;\n\n          jumps[closerIdx] = closerIdx - openerIdx + lastJump;\n          jumps[openerIdx] = lastJump;\n\n          closer.open  = false;\n          opener.end   = closerIdx;\n          opener.close = false;\n          newMinOpenerIdx = -1;\n          // treat next token as start of run,\n          // it optimizes skips in **<...>**a**<...>** pathological case\n          lastTokenIdx = -2;\n          break;\n        }\n      }\n    }\n\n    if (newMinOpenerIdx !== -1) {\n      // If match for this delimiter run failed, we want to set lower bound for\n      // future lookups. This is required to make sure algorithm has linear\n      // complexity.\n      //\n      // See details here:\n      // https://github.com/commonmark/cmark/issues/178#issuecomment-270417442\n      //\n      openersBottom[closer.marker][(closer.open ? 3 : 0) + ((closer.length || 0) % 3)] = newMinOpenerIdx;\n    }\n  }\n}\n\n\nmodule.exports = function link_pairs(state) {\n  var curr,\n      tokens_meta = state.tokens_meta,\n      max = state.tokens_meta.length;\n\n  processDelimiters(state, state.delimiters);\n\n  for (curr = 0; curr < max; curr++) {\n    if (tokens_meta[curr] && tokens_meta[curr].delimiters) {\n      processDelimiters(state, tokens_meta[curr].delimiters);\n    }\n  }\n};\n", "// Clean up tokens after emphasis and strikethrough postprocessing:\n// merge adjacent text nodes into one and re-calculate all token levels\n//\n// This is necessary because initially emphasis delimiter markers (*, _, ~)\n// are treated as their own separate text tokens. Then emphasis rule either\n// leaves them as text (needed to merge with adjacent text) or turns them\n// into opening/closing tags (which messes up levels inside).\n//\n'use strict';\n\n\nmodule.exports = function text_collapse(state) {\n  var curr, last,\n      level = 0,\n      tokens = state.tokens,\n      max = state.tokens.length;\n\n  for (curr = last = 0; curr < max; curr++) {\n    // re-calculate levels after emphasis/strikethrough turns some text nodes\n    // into opening/closing tags\n    if (tokens[curr].nesting < 0) level--; // closing tag\n    tokens[curr].level = level;\n    if (tokens[curr].nesting > 0) level++; // opening tag\n\n    if (tokens[curr].type === 'text' &&\n        curr + 1 < max &&\n        tokens[curr + 1].type === 'text') {\n\n      // collapse two adjacent text nodes\n      tokens[curr + 1].content = tokens[curr].content + tokens[curr + 1].content;\n    } else {\n      if (curr !== last) { tokens[last] = tokens[curr]; }\n\n      last++;\n    }\n  }\n\n  if (curr !== last) {\n    tokens.length = last;\n  }\n};\n", "// Inline parser state\n\n'use strict';\n\n\nvar Token          = require('../token');\nvar isWhiteSpace   = require('../common/utils').isWhiteSpace;\nvar isPunctChar    = require('../common/utils').isPunctChar;\nvar isMdAsciiPunct = require('../common/utils').isMdAsciiPunct;\n\n\nfunction StateInline(src, md, env, outTokens) {\n  this.src = src;\n  this.env = env;\n  this.md = md;\n  this.tokens = outTokens;\n  this.tokens_meta = Array(outTokens.length);\n\n  this.pos = 0;\n  this.posMax = this.src.length;\n  this.level = 0;\n  this.pending = '';\n  this.pendingLevel = 0;\n\n  // Stores { start: end } pairs. Useful for backtrack\n  // optimization of pairs parse (emphasis, strikes).\n  this.cache = {};\n\n  // List of emphasis-like delimiters for current tag\n  this.delimiters = [];\n\n  // Stack of delimiter lists for upper level tags\n  this._prev_delimiters = [];\n\n  // backtick length => last seen position\n  this.backticks = {};\n  this.backticksScanned = false;\n}\n\n\n// Flush pending text\n//\nStateInline.prototype.pushPending = function () {\n  var token = new Token('text', '', 0);\n  token.content = this.pending;\n  token.level = this.pendingLevel;\n  this.tokens.push(token);\n  this.pending = '';\n  return token;\n};\n\n\n// Push new token to \"stream\".\n// If pending text exists - flush it as text token\n//\nStateInline.prototype.push = function (type, tag, nesting) {\n  if (this.pending) {\n    this.pushPending();\n  }\n\n  var token = new Token(type, tag, nesting);\n  var token_meta = null;\n\n  if (nesting < 0) {\n    // closing tag\n    this.level--;\n    this.delimiters = this._prev_delimiters.pop();\n  }\n\n  token.level = this.level;\n\n  if (nesting > 0) {\n    // opening tag\n    this.level++;\n    this._prev_delimiters.push(this.delimiters);\n    this.delimiters = [];\n    token_meta = { delimiters: this.delimiters };\n  }\n\n  this.pendingLevel = this.level;\n  this.tokens.push(token);\n  this.tokens_meta.push(token_meta);\n  return token;\n};\n\n\n// Scan a sequence of emphasis-like markers, and determine whether\n// it can start an emphasis sequence or end an emphasis sequence.\n//\n//  - start - position to scan from (it should point at a valid marker);\n//  - canSplitWord - determine if these markers can be found inside a word\n//\nStateInline.prototype.scanDelims = function (start, canSplitWord) {\n  var pos = start, lastChar, nextChar, count, can_open, can_close,\n      isLastWhiteSpace, isLastPunctChar,\n      isNextWhiteSpace, isNextPunctChar,\n      left_flanking = true,\n      right_flanking = true,\n      max = this.posMax,\n      marker = this.src.charCodeAt(start);\n\n  // treat beginning of the line as a whitespace\n  lastChar = start > 0 ? this.src.charCodeAt(start - 1) : 0x20;\n\n  while (pos < max && this.src.charCodeAt(pos) === marker) { pos++; }\n\n  count = pos - start;\n\n  // treat end of the line as a whitespace\n  nextChar = pos < max ? this.src.charCodeAt(pos) : 0x20;\n\n  isLastPunctChar = isMdAsciiPunct(lastChar) || isPunctChar(String.fromCharCode(lastChar));\n  isNextPunctChar = isMdAsciiPunct(nextChar) || isPunctChar(String.fromCharCode(nextChar));\n\n  isLastWhiteSpace = isWhiteSpace(lastChar);\n  isNextWhiteSpace = isWhiteSpace(nextChar);\n\n  if (isNextWhiteSpace) {\n    left_flanking = false;\n  } else if (isNextPunctChar) {\n    if (!(isLastWhiteSpace || isLastPunctChar)) {\n      left_flanking = false;\n    }\n  }\n\n  if (isLastWhiteSpace) {\n    right_flanking = false;\n  } else if (isLastPunctChar) {\n    if (!(isNextWhiteSpace || isNextPunctChar)) {\n      right_flanking = false;\n    }\n  }\n\n  if (!canSplitWord) {\n    can_open  = left_flanking  && (!right_flanking || isLastPunctChar);\n    can_close = right_flanking && (!left_flanking  || isNextPunctChar);\n  } else {\n    can_open  = left_flanking;\n    can_close = right_flanking;\n  }\n\n  return {\n    can_open:  can_open,\n    can_close: can_close,\n    length:    count\n  };\n};\n\n\n// re-export Token class to use in block rules\nStateInline.prototype.Token = Token;\n\n\nmodule.exports = StateInline;\n", "/** internal\n * class ParserInline\n *\n * Tokenizes paragraph content.\n **/\n'use strict';\n\n\nvar Ruler           = require('./ruler');\n\n\n////////////////////////////////////////////////////////////////////////////////\n// Parser rules\n\nvar _rules = [\n  [ 'text',            require('./rules_inline/text') ],\n  [ 'newline',         require('./rules_inline/newline') ],\n  [ 'escape',          require('./rules_inline/escape') ],\n  [ 'backticks',       require('./rules_inline/backticks') ],\n  [ 'strikethrough',   require('./rules_inline/strikethrough').tokenize ],\n  [ 'emphasis',        require('./rules_inline/emphasis').tokenize ],\n  [ 'link',            require('./rules_inline/link') ],\n  [ 'image',           require('./rules_inline/image') ],\n  [ 'autolink',        require('./rules_inline/autolink') ],\n  [ 'html_inline',     require('./rules_inline/html_inline') ],\n  [ 'entity',          require('./rules_inline/entity') ]\n];\n\nvar _rules2 = [\n  [ 'balance_pairs',   require('./rules_inline/balance_pairs') ],\n  [ 'strikethrough',   require('./rules_inline/strikethrough').postProcess ],\n  [ 'emphasis',        require('./rules_inline/emphasis').postProcess ],\n  [ 'text_collapse',   require('./rules_inline/text_collapse') ]\n];\n\n\n/**\n * new ParserInline()\n **/\nfunction ParserInline() {\n  var i;\n\n  /**\n   * ParserInline#ruler -> Ruler\n   *\n   * [[Ruler]] instance. Keep configuration of inline rules.\n   **/\n  this.ruler = new Ruler();\n\n  for (i = 0; i < _rules.length; i++) {\n    this.ruler.push(_rules[i][0], _rules[i][1]);\n  }\n\n  /**\n   * ParserInline#ruler2 -> Ruler\n   *\n   * [[Ruler]] instance. Second ruler used for post-processing\n   * (e.g. in emphasis-like rules).\n   **/\n  this.ruler2 = new Ruler();\n\n  for (i = 0; i < _rules2.length; i++) {\n    this.ruler2.push(_rules2[i][0], _rules2[i][1]);\n  }\n}\n\n\n// Skip single token by running all rules in validation mode;\n// returns `true` if any rule reported success\n//\nParserInline.prototype.skipToken = function (state) {\n  var ok, i, pos = state.pos,\n      rules = this.ruler.getRules(''),\n      len = rules.length,\n      maxNesting = state.md.options.maxNesting,\n      cache = state.cache;\n\n\n  if (typeof cache[pos] !== 'undefined') {\n    state.pos = cache[pos];\n    return;\n  }\n\n  if (state.level < maxNesting) {\n    for (i = 0; i < len; i++) {\n      // Increment state.level and decrement it later to limit recursion.\n      // It's harmless to do here, because no tokens are created. But ideally,\n      // we'd need a separate private state variable for this purpose.\n      //\n      state.level++;\n      ok = rules[i](state, true);\n      state.level--;\n\n      if (ok) { break; }\n    }\n  } else {\n    // Too much nesting, just skip until the end of the paragraph.\n    //\n    // NOTE: this will cause links to behave incorrectly in the following case,\n    //       when an amount of `[` is exactly equal to `maxNesting + 1`:\n    //\n    //       [[[[[[[[[[[[[[[[[[[[[foo]()\n    //\n    // TODO: remove this workaround when CM standard will allow nested links\n    //       (we can replace it by preventing links from being parsed in\n    //       validation mode)\n    //\n    state.pos = state.posMax;\n  }\n\n  if (!ok) { state.pos++; }\n  cache[pos] = state.pos;\n};\n\n\n// Generate tokens for input range\n//\nParserInline.prototype.tokenize = function (state) {\n  var ok, i,\n      rules = this.ruler.getRules(''),\n      len = rules.length,\n      end = state.posMax,\n      maxNesting = state.md.options.maxNesting;\n\n  while (state.pos < end) {\n    // Try all possible rules.\n    // On success, rule should:\n    //\n    // - update `state.pos`\n    // - update `state.tokens`\n    // - return true\n\n    if (state.level < maxNesting) {\n      for (i = 0; i < len; i++) {\n        ok = rules[i](state, false);\n        if (ok) { break; }\n      }\n    }\n\n    if (ok) {\n      if (state.pos >= end) { break; }\n      continue;\n    }\n\n    state.pending += state.src[state.pos++];\n  }\n\n  if (state.pending) {\n    state.pushPending();\n  }\n};\n\n\n/**\n * ParserInline.parse(str, md, env, outTokens)\n *\n * Process input string and push inline tokens into `outTokens`\n **/\nParserInline.prototype.parse = function (str, md, env, outTokens) {\n  var i, rules, len;\n  var state = new this.State(str, md, env, outTokens);\n\n  this.tokenize(state);\n\n  rules = this.ruler2.getRules('');\n  len = rules.length;\n\n  for (i = 0; i < len; i++) {\n    rules[i](state);\n  }\n};\n\n\nParserInline.prototype.State = require('./rules_inline/state_inline');\n\n\nmodule.exports = ParserInline;\n", "'use strict';\n\n\nmodule.exports = function (opts) {\n  var re = {};\n\n  // Use direct extract instead of `regenerate` to reduse browserified size\n  re.src_Any = require('uc.micro/properties/Any/regex').source;\n  re.src_Cc  = require('uc.micro/categories/Cc/regex').source;\n  re.src_Z   = require('uc.micro/categories/Z/regex').source;\n  re.src_P   = require('uc.micro/categories/P/regex').source;\n\n  // \\p{\\Z\\P\\Cc\\CF} (white spaces + control + format + punctuation)\n  re.src_ZPCc = [ re.src_Z, re.src_P, re.src_Cc ].join('|');\n\n  // \\p{\\Z\\Cc} (white spaces + control)\n  re.src_ZCc = [ re.src_Z, re.src_Cc ].join('|');\n\n  // Experimental. List of chars, completely prohibited in links\n  // because can separate it from other part of text\n  var text_separators = '[><\\uff5c]';\n\n  // All possible word characters (everything without punctuation, spaces & controls)\n  // Defined via punctuation & spaces to save space\n  // Should be something like \\p{\\L\\N\\S\\M} (\\w but without `_`)\n  re.src_pseudo_letter       = '(?:(?!' + text_separators + '|' + re.src_ZPCc + ')' + re.src_Any + ')';\n  // The same as abothe but without [0-9]\n  // var src_pseudo_letter_non_d = '(?:(?![0-9]|' + src_ZPCc + ')' + src_Any + ')';\n\n  ////////////////////////////////////////////////////////////////////////////////\n\n  re.src_ip4 =\n\n    '(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)';\n\n  // Prohibit any of \"@/[]()\" in user/pass to avoid wrong domain fetch.\n  re.src_auth    = '(?:(?:(?!' + re.src_ZCc + '|[@/\\\\[\\\\]()]).)+@)?';\n\n  re.src_port =\n\n    '(?::(?:6(?:[0-4]\\\\d{3}|5(?:[0-4]\\\\d{2}|5(?:[0-2]\\\\d|3[0-5])))|[1-5]?\\\\d{1,4}))?';\n\n  re.src_host_terminator =\n\n    '(?=$|' + text_separators + '|' + re.src_ZPCc + ')(?!-|_|:\\\\d|\\\\.-|\\\\.(?!$|' + re.src_ZPCc + '))';\n\n  re.src_path =\n\n    '(?:' +\n      '[/?#]' +\n        '(?:' +\n          '(?!' + re.src_ZCc + '|' + text_separators + '|[()[\\\\]{}.,\"\\'?!\\\\-;]).|' +\n          '\\\\[(?:(?!' + re.src_ZCc + '|\\\\]).)*\\\\]|' +\n          '\\\\((?:(?!' + re.src_ZCc + '|[)]).)*\\\\)|' +\n          '\\\\{(?:(?!' + re.src_ZCc + '|[}]).)*\\\\}|' +\n          '\\\\\"(?:(?!' + re.src_ZCc + '|[\"]).)+\\\\\"|' +\n          \"\\\\'(?:(?!\" + re.src_ZCc + \"|[']).)+\\\\'|\" +\n          \"\\\\'(?=\" + re.src_pseudo_letter + '|[-]).|' +  // allow `I'm_king` if no pair found\n          '\\\\.{2,}[a-zA-Z0-9%/&]|' + // google has many dots in \"google search\" links (#66, #81).\n                                     // github has ... in commit range links,\n                                     // Restrict to\n                                     // - english\n                                     // - percent-encoded\n                                     // - parts of file path\n                                     // - params separator\n                                     // until more examples found.\n          '\\\\.(?!' + re.src_ZCc + '|[.]).|' +\n          (opts && opts['---'] ?\n            '\\\\-(?!--(?:[^-]|$))(?:-*)|' // `---` => long dash, terminate\n            :\n            '\\\\-+|'\n          ) +\n          ',(?!' + re.src_ZCc + ').|' +       // allow `,,,` in paths\n          ';(?!' + re.src_ZCc + ').|' +       // allow `;` if not followed by space-like char\n          '\\\\!+(?!' + re.src_ZCc + '|[!]).|' +  // allow `!!!` in paths, but not at the end\n          '\\\\?(?!' + re.src_ZCc + '|[?]).' +\n        ')+' +\n      '|\\\\/' +\n    ')?';\n\n  // Allow anything in markdown spec, forbid quote (\") at the first position\n  // because emails enclosed in quotes are far more common\n  re.src_email_name =\n\n    '[\\\\-;:&=\\\\+\\\\$,\\\\.a-zA-Z0-9_][\\\\-;:&=\\\\+\\\\$,\\\\\"\\\\.a-zA-Z0-9_]*';\n\n  re.src_xn =\n\n    'xn--[a-z0-9\\\\-]{1,59}';\n\n  // More to read about domain names\n  // http://serverfault.com/questions/638260/\n\n  re.src_domain_root =\n\n    // Allow letters & digits (http://test1)\n    '(?:' +\n      re.src_xn +\n      '|' +\n      re.src_pseudo_letter + '{1,63}' +\n    ')';\n\n  re.src_domain =\n\n    '(?:' +\n      re.src_xn +\n      '|' +\n      '(?:' + re.src_pseudo_letter + ')' +\n      '|' +\n      '(?:' + re.src_pseudo_letter + '(?:-|' + re.src_pseudo_letter + '){0,61}' + re.src_pseudo_letter + ')' +\n    ')';\n\n  re.src_host =\n\n    '(?:' +\n    // Don't need IP check, because digits are already allowed in normal domain names\n    //   src_ip4 +\n    // '|' +\n      '(?:(?:(?:' + re.src_domain + ')\\\\.)*' + re.src_domain/*_root*/ + ')' +\n    ')';\n\n  re.tpl_host_fuzzy =\n\n    '(?:' +\n      re.src_ip4 +\n    '|' +\n      '(?:(?:(?:' + re.src_domain + ')\\\\.)+(?:%TLDS%))' +\n    ')';\n\n  re.tpl_host_no_ip_fuzzy =\n\n    '(?:(?:(?:' + re.src_domain + ')\\\\.)+(?:%TLDS%))';\n\n  re.src_host_strict =\n\n    re.src_host + re.src_host_terminator;\n\n  re.tpl_host_fuzzy_strict =\n\n    re.tpl_host_fuzzy + re.src_host_terminator;\n\n  re.src_host_port_strict =\n\n    re.src_host + re.src_port + re.src_host_terminator;\n\n  re.tpl_host_port_fuzzy_strict =\n\n    re.tpl_host_fuzzy + re.src_port + re.src_host_terminator;\n\n  re.tpl_host_port_no_ip_fuzzy_strict =\n\n    re.tpl_host_no_ip_fuzzy + re.src_port + re.src_host_terminator;\n\n\n  ////////////////////////////////////////////////////////////////////////////////\n  // Main rules\n\n  // Rude test fuzzy links by host, for quick deny\n  re.tpl_host_fuzzy_test =\n\n    'localhost|www\\\\.|\\\\.\\\\d{1,3}\\\\.|(?:\\\\.(?:%TLDS%)(?:' + re.src_ZPCc + '|>|$))';\n\n  re.tpl_email_fuzzy =\n\n      '(^|' + text_separators + '|\"|\\\\(|' + re.src_ZCc + ')' +\n      '(' + re.src_email_name + '@' + re.tpl_host_fuzzy_strict + ')';\n\n  re.tpl_link_fuzzy =\n      // Fuzzy link can't be prepended with .:/\\- and non punctuation.\n      // but can start with > (markdown blockquote)\n      '(^|(?![.:/\\\\-_@])(?:[$+<=>^`|\\uff5c]|' + re.src_ZPCc + '))' +\n      '((?![$+<=>^`|\\uff5c])' + re.tpl_host_port_fuzzy_strict + re.src_path + ')';\n\n  re.tpl_link_no_ip_fuzzy =\n      // Fuzzy link can't be prepended with .:/\\- and non punctuation.\n      // but can start with > (markdown blockquote)\n      '(^|(?![.:/\\\\-_@])(?:[$+<=>^`|\\uff5c]|' + re.src_ZPCc + '))' +\n      '((?![$+<=>^`|\\uff5c])' + re.tpl_host_port_no_ip_fuzzy_strict + re.src_path + ')';\n\n  return re;\n};\n", "'use strict';\n\n\n////////////////////////////////////////////////////////////////////////////////\n// Helpers\n\n// Merge objects\n//\nfunction assign(obj /*from1, from2, from3, ...*/) {\n  var sources = Array.prototype.slice.call(arguments, 1);\n\n  sources.forEach(function (source) {\n    if (!source) { return; }\n\n    Object.keys(source).forEach(function (key) {\n      obj[key] = source[key];\n    });\n  });\n\n  return obj;\n}\n\nfunction _class(obj) { return Object.prototype.toString.call(obj); }\nfunction isString(obj) { return _class(obj) === '[object String]'; }\nfunction isObject(obj) { return _class(obj) === '[object Object]'; }\nfunction isRegExp(obj) { return _class(obj) === '[object RegExp]'; }\nfunction isFunction(obj) { return _class(obj) === '[object Function]'; }\n\n\nfunction escapeRE(str) { return str.replace(/[.?*+^$[\\]\\\\(){}|-]/g, '\\\\$&'); }\n\n////////////////////////////////////////////////////////////////////////////////\n\n\nvar defaultOptions = {\n  fuzzyLink: true,\n  fuzzyEmail: true,\n  fuzzyIP: false\n};\n\n\nfunction isOptionsObj(obj) {\n  return Object.keys(obj || {}).reduce(function (acc, k) {\n    return acc || defaultOptions.hasOwnProperty(k);\n  }, false);\n}\n\n\nvar defaultSchemas = {\n  'http:': {\n    validate: function (text, pos, self) {\n      var tail = text.slice(pos);\n\n      if (!self.re.http) {\n        // compile lazily, because \"host\"-containing variables can change on tlds update.\n        self.re.http =  new RegExp(\n          '^\\\\/\\\\/' + self.re.src_auth + self.re.src_host_port_strict + self.re.src_path, 'i'\n        );\n      }\n      if (self.re.http.test(tail)) {\n        return tail.match(self.re.http)[0].length;\n      }\n      return 0;\n    }\n  },\n  'https:':  'http:',\n  'ftp:':    'http:',\n  '//':      {\n    validate: function (text, pos, self) {\n      var tail = text.slice(pos);\n\n      if (!self.re.no_http) {\n      // compile lazily, because \"host\"-containing variables can change on tlds update.\n        self.re.no_http =  new RegExp(\n          '^' +\n          self.re.src_auth +\n          // Don't allow single-level domains, because of false positives like '//test'\n          // with code comments\n          '(?:localhost|(?:(?:' + self.re.src_domain + ')\\\\.)+' + self.re.src_domain_root + ')' +\n          self.re.src_port +\n          self.re.src_host_terminator +\n          self.re.src_path,\n\n          'i'\n        );\n      }\n\n      if (self.re.no_http.test(tail)) {\n        // should not be `://` & `///`, that protects from errors in protocol name\n        if (pos >= 3 && text[pos - 3] === ':') { return 0; }\n        if (pos >= 3 && text[pos - 3] === '/') { return 0; }\n        return tail.match(self.re.no_http)[0].length;\n      }\n      return 0;\n    }\n  },\n  'mailto:': {\n    validate: function (text, pos, self) {\n      var tail = text.slice(pos);\n\n      if (!self.re.mailto) {\n        self.re.mailto =  new RegExp(\n          '^' + self.re.src_email_name + '@' + self.re.src_host_strict, 'i'\n        );\n      }\n      if (self.re.mailto.test(tail)) {\n        return tail.match(self.re.mailto)[0].length;\n      }\n      return 0;\n    }\n  }\n};\n\n/*eslint-disable max-len*/\n\n// RE pattern for 2-character tlds (autogenerated by ./support/tlds_2char_gen.js)\nvar tlds_2ch_src_re = 'a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]';\n\n// DON'T try to make PRs with changes. Extend TLDs with LinkifyIt.tlds() instead\nvar tlds_default = 'biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф'.split('|');\n\n/*eslint-enable max-len*/\n\n////////////////////////////////////////////////////////////////////////////////\n\nfunction resetScanCache(self) {\n  self.__index__ = -1;\n  self.__text_cache__   = '';\n}\n\nfunction createValidator(re) {\n  return function (text, pos) {\n    var tail = text.slice(pos);\n\n    if (re.test(tail)) {\n      return tail.match(re)[0].length;\n    }\n    return 0;\n  };\n}\n\nfunction createNormalizer() {\n  return function (match, self) {\n    self.normalize(match);\n  };\n}\n\n// Schemas compiler. Build regexps.\n//\nfunction compile(self) {\n\n  // Load & clone RE patterns.\n  var re = self.re = require('./lib/re')(self.__opts__);\n\n  // Define dynamic patterns\n  var tlds = self.__tlds__.slice();\n\n  self.onCompile();\n\n  if (!self.__tlds_replaced__) {\n    tlds.push(tlds_2ch_src_re);\n  }\n  tlds.push(re.src_xn);\n\n  re.src_tlds = tlds.join('|');\n\n  function untpl(tpl) { return tpl.replace('%TLDS%', re.src_tlds); }\n\n  re.email_fuzzy      = RegExp(untpl(re.tpl_email_fuzzy), 'i');\n  re.link_fuzzy       = RegExp(untpl(re.tpl_link_fuzzy), 'i');\n  re.link_no_ip_fuzzy = RegExp(untpl(re.tpl_link_no_ip_fuzzy), 'i');\n  re.host_fuzzy_test  = RegExp(untpl(re.tpl_host_fuzzy_test), 'i');\n\n  //\n  // Compile each schema\n  //\n\n  var aliases = [];\n\n  self.__compiled__ = {}; // Reset compiled data\n\n  function schemaError(name, val) {\n    throw new Error('(LinkifyIt) Invalid schema \"' + name + '\": ' + val);\n  }\n\n  Object.keys(self.__schemas__).forEach(function (name) {\n    var val = self.__schemas__[name];\n\n    // skip disabled methods\n    if (val === null) { return; }\n\n    var compiled = { validate: null, link: null };\n\n    self.__compiled__[name] = compiled;\n\n    if (isObject(val)) {\n      if (isRegExp(val.validate)) {\n        compiled.validate = createValidator(val.validate);\n      } else if (isFunction(val.validate)) {\n        compiled.validate = val.validate;\n      } else {\n        schemaError(name, val);\n      }\n\n      if (isFunction(val.normalize)) {\n        compiled.normalize = val.normalize;\n      } else if (!val.normalize) {\n        compiled.normalize = createNormalizer();\n      } else {\n        schemaError(name, val);\n      }\n\n      return;\n    }\n\n    if (isString(val)) {\n      aliases.push(name);\n      return;\n    }\n\n    schemaError(name, val);\n  });\n\n  //\n  // Compile postponed aliases\n  //\n\n  aliases.forEach(function (alias) {\n    if (!self.__compiled__[self.__schemas__[alias]]) {\n      // Silently fail on missed schemas to avoid errons on disable.\n      // schemaError(alias, self.__schemas__[alias]);\n      return;\n    }\n\n    self.__compiled__[alias].validate =\n      self.__compiled__[self.__schemas__[alias]].validate;\n    self.__compiled__[alias].normalize =\n      self.__compiled__[self.__schemas__[alias]].normalize;\n  });\n\n  //\n  // Fake record for guessed links\n  //\n  self.__compiled__[''] = { validate: null, normalize: createNormalizer() };\n\n  //\n  // Build schema condition\n  //\n  var slist = Object.keys(self.__compiled__)\n                      .filter(function (name) {\n                        // Filter disabled & fake schemas\n                        return name.length > 0 && self.__compiled__[name];\n                      })\n                      .map(escapeRE)\n                      .join('|');\n  // (?!_) cause 1.5x slowdown\n  self.re.schema_test   = RegExp('(^|(?!_)(?:[><\\uff5c]|' + re.src_ZPCc + '))(' + slist + ')', 'i');\n  self.re.schema_search = RegExp('(^|(?!_)(?:[><\\uff5c]|' + re.src_ZPCc + '))(' + slist + ')', 'ig');\n\n  self.re.pretest = RegExp(\n    '(' + self.re.schema_test.source + ')|(' + self.re.host_fuzzy_test.source + ')|@',\n    'i'\n  );\n\n  //\n  // Cleanup\n  //\n\n  resetScanCache(self);\n}\n\n/**\n * class Match\n *\n * Match result. Single element of array, returned by [[LinkifyIt#match]]\n **/\nfunction Match(self, shift) {\n  var start = self.__index__,\n      end   = self.__last_index__,\n      text  = self.__text_cache__.slice(start, end);\n\n  /**\n   * Match#schema -> String\n   *\n   * Prefix (protocol) for matched string.\n   **/\n  this.schema    = self.__schema__.toLowerCase();\n  /**\n   * Match#index -> Number\n   *\n   * First position of matched string.\n   **/\n  this.index     = start + shift;\n  /**\n   * Match#lastIndex -> Number\n   *\n   * Next position after matched string.\n   **/\n  this.lastIndex = end + shift;\n  /**\n   * Match#raw -> String\n   *\n   * Matched string.\n   **/\n  this.raw       = text;\n  /**\n   * Match#text -> String\n   *\n   * Notmalized text of matched string.\n   **/\n  this.text      = text;\n  /**\n   * Match#url -> String\n   *\n   * Normalized url of matched string.\n   **/\n  this.url       = text;\n}\n\nfunction createMatch(self, shift) {\n  var match = new Match(self, shift);\n\n  self.__compiled__[match.schema].normalize(match, self);\n\n  return match;\n}\n\n\n/**\n * class LinkifyIt\n **/\n\n/**\n * new LinkifyIt(schemas, options)\n * - schemas (Object): Optional. Additional schemas to validate (prefix/validator)\n * - options (Object): { fuzzyLink|fuzzyEmail|fuzzyIP: true|false }\n *\n * Creates new linkifier instance with optional additional schemas.\n * Can be called without `new` keyword for convenience.\n *\n * By default understands:\n *\n * - `http(s)://...` , `ftp://...`, `mailto:...` & `//...` links\n * - \"fuzzy\" links and emails (example.com, <EMAIL>).\n *\n * `schemas` is an object, where each key/value describes protocol/rule:\n *\n * - __key__ - link prefix (usually, protocol name with `:` at the end, `skype:`\n *   for example). `linkify-it` makes shure that prefix is not preceeded with\n *   alphanumeric char and symbols. Only whitespaces and punctuation allowed.\n * - __value__ - rule to check tail after link prefix\n *   - _String_ - just alias to existing rule\n *   - _Object_\n *     - _validate_ - validator function (should return matched length on success),\n *       or `RegExp`.\n *     - _normalize_ - optional function to normalize text & url of matched result\n *       (for example, for @twitter mentions).\n *\n * `options`:\n *\n * - __fuzzyLink__ - recognige URL-s without `http(s):` prefix. Default `true`.\n * - __fuzzyIP__ - allow IPs in fuzzy links above. Can conflict with some texts\n *   like version numbers. Default `false`.\n * - __fuzzyEmail__ - recognize emails without `mailto:` prefix.\n *\n **/\nfunction LinkifyIt(schemas, options) {\n  if (!(this instanceof LinkifyIt)) {\n    return new LinkifyIt(schemas, options);\n  }\n\n  if (!options) {\n    if (isOptionsObj(schemas)) {\n      options = schemas;\n      schemas = {};\n    }\n  }\n\n  this.__opts__           = assign({}, defaultOptions, options);\n\n  // Cache last tested result. Used to skip repeating steps on next `match` call.\n  this.__index__          = -1;\n  this.__last_index__     = -1; // Next scan position\n  this.__schema__         = '';\n  this.__text_cache__     = '';\n\n  this.__schemas__        = assign({}, defaultSchemas, schemas);\n  this.__compiled__       = {};\n\n  this.__tlds__           = tlds_default;\n  this.__tlds_replaced__  = false;\n\n  this.re = {};\n\n  compile(this);\n}\n\n\n/** chainable\n * LinkifyIt#add(schema, definition)\n * - schema (String): rule name (fixed pattern prefix)\n * - definition (String|RegExp|Object): schema definition\n *\n * Add new rule definition. See constructor description for details.\n **/\nLinkifyIt.prototype.add = function add(schema, definition) {\n  this.__schemas__[schema] = definition;\n  compile(this);\n  return this;\n};\n\n\n/** chainable\n * LinkifyIt#set(options)\n * - options (Object): { fuzzyLink|fuzzyEmail|fuzzyIP: true|false }\n *\n * Set recognition options for links without schema.\n **/\nLinkifyIt.prototype.set = function set(options) {\n  this.__opts__ = assign(this.__opts__, options);\n  return this;\n};\n\n\n/**\n * LinkifyIt#test(text) -> Boolean\n *\n * Searches linkifiable pattern and returns `true` on success or `false` on fail.\n **/\nLinkifyIt.prototype.test = function test(text) {\n  // Reset scan cache\n  this.__text_cache__ = text;\n  this.__index__      = -1;\n\n  if (!text.length) { return false; }\n\n  var m, ml, me, len, shift, next, re, tld_pos, at_pos;\n\n  // try to scan for link with schema - that's the most simple rule\n  if (this.re.schema_test.test(text)) {\n    re = this.re.schema_search;\n    re.lastIndex = 0;\n    while ((m = re.exec(text)) !== null) {\n      len = this.testSchemaAt(text, m[2], re.lastIndex);\n      if (len) {\n        this.__schema__     = m[2];\n        this.__index__      = m.index + m[1].length;\n        this.__last_index__ = m.index + m[0].length + len;\n        break;\n      }\n    }\n  }\n\n  if (this.__opts__.fuzzyLink && this.__compiled__['http:']) {\n    // guess schemaless links\n    tld_pos = text.search(this.re.host_fuzzy_test);\n    if (tld_pos >= 0) {\n      // if tld is located after found link - no need to check fuzzy pattern\n      if (this.__index__ < 0 || tld_pos < this.__index__) {\n        if ((ml = text.match(this.__opts__.fuzzyIP ? this.re.link_fuzzy : this.re.link_no_ip_fuzzy)) !== null) {\n\n          shift = ml.index + ml[1].length;\n\n          if (this.__index__ < 0 || shift < this.__index__) {\n            this.__schema__     = '';\n            this.__index__      = shift;\n            this.__last_index__ = ml.index + ml[0].length;\n          }\n        }\n      }\n    }\n  }\n\n  if (this.__opts__.fuzzyEmail && this.__compiled__['mailto:']) {\n    // guess schemaless emails\n    at_pos = text.indexOf('@');\n    if (at_pos >= 0) {\n      // We can't skip this check, because this cases are possible:\n      // <EMAIL>, <EMAIL>\n      if ((me = text.match(this.re.email_fuzzy)) !== null) {\n\n        shift = me.index + me[1].length;\n        next  = me.index + me[0].length;\n\n        if (this.__index__ < 0 || shift < this.__index__ ||\n            (shift === this.__index__ && next > this.__last_index__)) {\n          this.__schema__     = 'mailto:';\n          this.__index__      = shift;\n          this.__last_index__ = next;\n        }\n      }\n    }\n  }\n\n  return this.__index__ >= 0;\n};\n\n\n/**\n * LinkifyIt#pretest(text) -> Boolean\n *\n * Very quick check, that can give false positives. Returns true if link MAY BE\n * can exists. Can be used for speed optimization, when you need to check that\n * link NOT exists.\n **/\nLinkifyIt.prototype.pretest = function pretest(text) {\n  return this.re.pretest.test(text);\n};\n\n\n/**\n * LinkifyIt#testSchemaAt(text, name, position) -> Number\n * - text (String): text to scan\n * - name (String): rule (schema) name\n * - position (Number): text offset to check from\n *\n * Similar to [[LinkifyIt#test]] but checks only specific protocol tail exactly\n * at given position. Returns length of found pattern (0 on fail).\n **/\nLinkifyIt.prototype.testSchemaAt = function testSchemaAt(text, schema, pos) {\n  // If not supported schema check requested - terminate\n  if (!this.__compiled__[schema.toLowerCase()]) {\n    return 0;\n  }\n  return this.__compiled__[schema.toLowerCase()].validate(text, pos, this);\n};\n\n\n/**\n * LinkifyIt#match(text) -> Array|null\n *\n * Returns array of found link descriptions or `null` on fail. We strongly\n * recommend to use [[LinkifyIt#test]] first, for best speed.\n *\n * ##### Result match description\n *\n * - __schema__ - link schema, can be empty for fuzzy links, or `//` for\n *   protocol-neutral  links.\n * - __index__ - offset of matched text\n * - __lastIndex__ - index of next char after mathch end\n * - __raw__ - matched text\n * - __text__ - normalized text\n * - __url__ - link, generated from matched text\n **/\nLinkifyIt.prototype.match = function match(text) {\n  var shift = 0, result = [];\n\n  // Try to take previous element from cache, if .test() called before\n  if (this.__index__ >= 0 && this.__text_cache__ === text) {\n    result.push(createMatch(this, shift));\n    shift = this.__last_index__;\n  }\n\n  // Cut head if cache was used\n  var tail = shift ? text.slice(shift) : text;\n\n  // Scan string until end reached\n  while (this.test(tail)) {\n    result.push(createMatch(this, shift));\n\n    tail = tail.slice(this.__last_index__);\n    shift += this.__last_index__;\n  }\n\n  if (result.length) {\n    return result;\n  }\n\n  return null;\n};\n\n\n/** chainable\n * LinkifyIt#tlds(list [, keepOld]) -> this\n * - list (Array): list of tlds\n * - keepOld (Boolean): merge with current list if `true` (`false` by default)\n *\n * Load (or merge) new tlds list. Those are user for fuzzy links (without prefix)\n * to avoid false positives. By default this algorythm used:\n *\n * - hostname with any 2-letter root zones are ok.\n * - biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф\n *   are ok.\n * - encoded (`xn--...`) root zones are ok.\n *\n * If list is replaced, then exact match for 2-chars root zones will be checked.\n **/\nLinkifyIt.prototype.tlds = function tlds(list, keepOld) {\n  list = Array.isArray(list) ? list : [ list ];\n\n  if (!keepOld) {\n    this.__tlds__ = list.slice();\n    this.__tlds_replaced__ = true;\n    compile(this);\n    return this;\n  }\n\n  this.__tlds__ = this.__tlds__.concat(list)\n                                  .sort()\n                                  .filter(function (el, idx, arr) {\n                                    return el !== arr[idx - 1];\n                                  })\n                                  .reverse();\n\n  compile(this);\n  return this;\n};\n\n/**\n * LinkifyIt#normalize(match)\n *\n * Default normalizer (if schema does not define it's own).\n **/\nLinkifyIt.prototype.normalize = function normalize(match) {\n\n  // Do minimal possible changes by default. Need to collect feedback prior\n  // to move forward https://github.com/markdown-it/linkify-it/issues/1\n\n  if (!match.schema) { match.url = 'http://' + match.url; }\n\n  if (match.schema === 'mailto:' && !/^mailto:/i.test(match.url)) {\n    match.url = 'mailto:' + match.url;\n  }\n};\n\n\n/**\n * LinkifyIt#onCompile()\n *\n * Override to modify basic RegExp-s.\n **/\nLinkifyIt.prototype.onCompile = function onCompile() {\n};\n\n\nmodule.exports = LinkifyIt;\n", "// markdown-it default options\n\n'use strict';\n\n\nmodule.exports = {\n  options: {\n    html:         false,        // Enable HTML tags in source\n    xhtmlOut:     false,        // Use '/' to close single tags (<br />)\n    breaks:       false,        // Convert '\\n' in paragraphs into <br>\n    langPrefix:   'language-',  // CSS language prefix for fenced blocks\n    linkify:      false,        // autoconvert URL-like texts to links\n\n    // Enable some language-neutral replacements + quotes beautification\n    typographer:  false,\n\n    // Double + single quotes replacement pairs, when typographer enabled,\n    // and smartquotes on. Could be either a String or an Array.\n    //\n    // For example, you can use '«»„“' for Russian, '„“‚‘' for German,\n    // and ['«\\xA0', '\\xA0»', '‹\\xA0', '\\xA0›'] for French (including nbsp).\n    quotes: '\\u201c\\u201d\\u2018\\u2019', /* “”‘’ */\n\n    // Highlighter function. Should return escaped HTML,\n    // or '' if the source string is not changed and should be escaped externaly.\n    // If result starts with <pre... internal wrapper is skipped.\n    //\n    // function (/*str, lang*/) { return ''; }\n    //\n    highlight: null,\n\n    maxNesting:   100            // Internal protection, recursion limit\n  },\n\n  components: {\n\n    core: {},\n    block: {},\n    inline: {}\n  }\n};\n", "// \"Zero\" preset, with nothing enabled. Useful for manual configuring of simple\n// modes. For example, to parse bold/italic only.\n\n'use strict';\n\n\nmodule.exports = {\n  options: {\n    html:         false,        // Enable HTML tags in source\n    xhtmlOut:     false,        // Use '/' to close single tags (<br />)\n    breaks:       false,        // Convert '\\n' in paragraphs into <br>\n    langPrefix:   'language-',  // CSS language prefix for fenced blocks\n    linkify:      false,        // autoconvert URL-like texts to links\n\n    // Enable some language-neutral replacements + quotes beautification\n    typographer:  false,\n\n    // Double + single quotes replacement pairs, when typographer enabled,\n    // and smartquotes on. Could be either a String or an Array.\n    //\n    // For example, you can use '«»„“' for Russian, '„“‚‘' for German,\n    // and ['«\\xA0', '\\xA0»', '‹\\xA0', '\\xA0›'] for French (including nbsp).\n    quotes: '\\u201c\\u201d\\u2018\\u2019', /* “”‘’ */\n\n    // Highlighter function. Should return escaped HTML,\n    // or '' if the source string is not changed and should be escaped externaly.\n    // If result starts with <pre... internal wrapper is skipped.\n    //\n    // function (/*str, lang*/) { return ''; }\n    //\n    highlight: null,\n\n    maxNesting:   20            // Internal protection, recursion limit\n  },\n\n  components: {\n\n    core: {\n      rules: [\n        'normalize',\n        'block',\n        'inline'\n      ]\n    },\n\n    block: {\n      rules: [\n        'paragraph'\n      ]\n    },\n\n    inline: {\n      rules: [\n        'text'\n      ],\n      rules2: [\n        'balance_pairs',\n        'text_collapse'\n      ]\n    }\n  }\n};\n", "// Commonmark default options\n\n'use strict';\n\n\nmodule.exports = {\n  options: {\n    html:         true,         // Enable HTML tags in source\n    xhtmlOut:     true,         // Use '/' to close single tags (<br />)\n    breaks:       false,        // Convert '\\n' in paragraphs into <br>\n    langPrefix:   'language-',  // CSS language prefix for fenced blocks\n    linkify:      false,        // autoconvert URL-like texts to links\n\n    // Enable some language-neutral replacements + quotes beautification\n    typographer:  false,\n\n    // Double + single quotes replacement pairs, when typographer enabled,\n    // and smartquotes on. Could be either a String or an Array.\n    //\n    // For example, you can use '«»„“' for Russian, '„“‚‘' for German,\n    // and ['«\\xA0', '\\xA0»', '‹\\xA0', '\\xA0›'] for French (including nbsp).\n    quotes: '\\u201c\\u201d\\u2018\\u2019', /* “”‘’ */\n\n    // Highlighter function. Should return escaped HTML,\n    // or '' if the source string is not changed and should be escaped externaly.\n    // If result starts with <pre... internal wrapper is skipped.\n    //\n    // function (/*str, lang*/) { return ''; }\n    //\n    highlight: null,\n\n    maxNesting:   20            // Internal protection, recursion limit\n  },\n\n  components: {\n\n    core: {\n      rules: [\n        'normalize',\n        'block',\n        'inline'\n      ]\n    },\n\n    block: {\n      rules: [\n        'blockquote',\n        'code',\n        'fence',\n        'heading',\n        'hr',\n        'html_block',\n        'lheading',\n        'list',\n        'reference',\n        'paragraph'\n      ]\n    },\n\n    inline: {\n      rules: [\n        'autolink',\n        'backticks',\n        'emphasis',\n        'entity',\n        'escape',\n        'html_inline',\n        'image',\n        'link',\n        'newline',\n        'text'\n      ],\n      rules2: [\n        'balance_pairs',\n        'emphasis',\n        'text_collapse'\n      ]\n    }\n  }\n};\n", "// Main parser class\n\n'use strict';\n\n\nvar utils        = require('./common/utils');\nvar helpers      = require('./helpers');\nvar Renderer     = require('./renderer');\nvar ParserCore   = require('./parser_core');\nvar ParserBlock  = require('./parser_block');\nvar ParserInline = require('./parser_inline');\nvar LinkifyIt    = require('linkify-it');\nvar mdurl        = require('mdurl');\nvar punycode     = require('punycode');\n\n\nvar config = {\n  default: require('./presets/default'),\n  zero: require('./presets/zero'),\n  commonmark: require('./presets/commonmark')\n};\n\n////////////////////////////////////////////////////////////////////////////////\n//\n// This validator can prohibit more than really needed to prevent XSS. It's a\n// tradeoff to keep code simple and to be secure by default.\n//\n// If you need different setup - override validator method as you wish. Or\n// replace it with dummy function and use external sanitizer.\n//\n\nvar BAD_PROTO_RE = /^(vbscript|javascript|file|data):/;\nvar GOOD_DATA_RE = /^data:image\\/(gif|png|jpeg|webp);/;\n\nfunction validateLink(url) {\n  // url should be normalized at this point, and existing entities are decoded\n  var str = url.trim().toLowerCase();\n\n  return BAD_PROTO_RE.test(str) ? (GOOD_DATA_RE.test(str) ? true : false) : true;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\n\nvar RECODE_HOSTNAME_FOR = [ 'http:', 'https:', 'mailto:' ];\n\nfunction normalizeLink(url) {\n  var parsed = mdurl.parse(url, true);\n\n  if (parsed.hostname) {\n    // Encode hostnames in urls like:\n    // `http://host/`, `https://host/`, `mailto:user@host`, `//host/`\n    //\n    // We don't encode unknown schemas, because it's likely that we encode\n    // something we shouldn't (e.g. `skype:name` treated as `skype:host`)\n    //\n    if (!parsed.protocol || RECODE_HOSTNAME_FOR.indexOf(parsed.protocol) >= 0) {\n      try {\n        parsed.hostname = punycode.toASCII(parsed.hostname);\n      } catch (er) { /**/ }\n    }\n  }\n\n  return mdurl.encode(mdurl.format(parsed));\n}\n\nfunction normalizeLinkText(url) {\n  var parsed = mdurl.parse(url, true);\n\n  if (parsed.hostname) {\n    // Encode hostnames in urls like:\n    // `http://host/`, `https://host/`, `mailto:user@host`, `//host/`\n    //\n    // We don't encode unknown schemas, because it's likely that we encode\n    // something we shouldn't (e.g. `skype:name` treated as `skype:host`)\n    //\n    if (!parsed.protocol || RECODE_HOSTNAME_FOR.indexOf(parsed.protocol) >= 0) {\n      try {\n        parsed.hostname = punycode.toUnicode(parsed.hostname);\n      } catch (er) { /**/ }\n    }\n  }\n\n  // add '%' to exclude list because of https://github.com/markdown-it/markdown-it/issues/720\n  return mdurl.decode(mdurl.format(parsed), mdurl.decode.defaultChars + '%');\n}\n\n\n/**\n * class MarkdownIt\n *\n * Main parser/renderer class.\n *\n * ##### Usage\n *\n * ```javascript\n * // node.js, \"classic\" way:\n * var MarkdownIt = require('markdown-it'),\n *     md = new MarkdownIt();\n * var result = md.render('# markdown-it rulezz!');\n *\n * // node.js, the same, but with sugar:\n * var md = require('markdown-it')();\n * var result = md.render('# markdown-it rulezz!');\n *\n * // browser without AMD, added to \"window\" on script load\n * // Note, there are no dash.\n * var md = window.markdownit();\n * var result = md.render('# markdown-it rulezz!');\n * ```\n *\n * Single line rendering, without paragraph wrap:\n *\n * ```javascript\n * var md = require('markdown-it')();\n * var result = md.renderInline('__markdown-it__ rulezz!');\n * ```\n **/\n\n/**\n * new MarkdownIt([presetName, options])\n * - presetName (String): optional, `commonmark` / `zero`\n * - options (Object)\n *\n * Creates parser instanse with given config. Can be called without `new`.\n *\n * ##### presetName\n *\n * MarkdownIt provides named presets as a convenience to quickly\n * enable/disable active syntax rules and options for common use cases.\n *\n * - [\"commonmark\"](https://github.com/markdown-it/markdown-it/blob/master/lib/presets/commonmark.js) -\n *   configures parser to strict [CommonMark](http://commonmark.org/) mode.\n * - [default](https://github.com/markdown-it/markdown-it/blob/master/lib/presets/default.js) -\n *   similar to GFM, used when no preset name given. Enables all available rules,\n *   but still without html, typographer & autolinker.\n * - [\"zero\"](https://github.com/markdown-it/markdown-it/blob/master/lib/presets/zero.js) -\n *   all rules disabled. Useful to quickly setup your config via `.enable()`.\n *   For example, when you need only `bold` and `italic` markup and nothing else.\n *\n * ##### options:\n *\n * - __html__ - `false`. Set `true` to enable HTML tags in source. Be careful!\n *   That's not safe! You may need external sanitizer to protect output from XSS.\n *   It's better to extend features via plugins, instead of enabling HTML.\n * - __xhtmlOut__ - `false`. Set `true` to add '/' when closing single tags\n *   (`<br />`). This is needed only for full CommonMark compatibility. In real\n *   world you will need HTML output.\n * - __breaks__ - `false`. Set `true` to convert `\\n` in paragraphs into `<br>`.\n * - __langPrefix__ - `language-`. CSS language class prefix for fenced blocks.\n *   Can be useful for external highlighters.\n * - __linkify__ - `false`. Set `true` to autoconvert URL-like text to links.\n * - __typographer__  - `false`. Set `true` to enable [some language-neutral\n *   replacement](https://github.com/markdown-it/markdown-it/blob/master/lib/rules_core/replacements.js) +\n *   quotes beautification (smartquotes).\n * - __quotes__ - `“”‘’`, String or Array. Double + single quotes replacement\n *   pairs, when typographer enabled and smartquotes on. For example, you can\n *   use `'«»„“'` for Russian, `'„“‚‘'` for German, and\n *   `['«\\xA0', '\\xA0»', '‹\\xA0', '\\xA0›']` for French (including nbsp).\n * - __highlight__ - `null`. Highlighter function for fenced code blocks.\n *   Highlighter `function (str, lang)` should return escaped HTML. It can also\n *   return empty string if the source was not changed and should be escaped\n *   externaly. If result starts with <pre... internal wrapper is skipped.\n *\n * ##### Example\n *\n * ```javascript\n * // commonmark mode\n * var md = require('markdown-it')('commonmark');\n *\n * // default mode\n * var md = require('markdown-it')();\n *\n * // enable everything\n * var md = require('markdown-it')({\n *   html: true,\n *   linkify: true,\n *   typographer: true\n * });\n * ```\n *\n * ##### Syntax highlighting\n *\n * ```js\n * var hljs = require('highlight.js') // https://highlightjs.org/\n *\n * var md = require('markdown-it')({\n *   highlight: function (str, lang) {\n *     if (lang && hljs.getLanguage(lang)) {\n *       try {\n *         return hljs.highlight(str, { language: lang, ignoreIllegals: true }).value;\n *       } catch (__) {}\n *     }\n *\n *     return ''; // use external default escaping\n *   }\n * });\n * ```\n *\n * Or with full wrapper override (if you need assign class to `<pre>`):\n *\n * ```javascript\n * var hljs = require('highlight.js') // https://highlightjs.org/\n *\n * // Actual default values\n * var md = require('markdown-it')({\n *   highlight: function (str, lang) {\n *     if (lang && hljs.getLanguage(lang)) {\n *       try {\n *         return '<pre class=\"hljs\"><code>' +\n *                hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +\n *                '</code></pre>';\n *       } catch (__) {}\n *     }\n *\n *     return '<pre class=\"hljs\"><code>' + md.utils.escapeHtml(str) + '</code></pre>';\n *   }\n * });\n * ```\n *\n **/\nfunction MarkdownIt(presetName, options) {\n  if (!(this instanceof MarkdownIt)) {\n    return new MarkdownIt(presetName, options);\n  }\n\n  if (!options) {\n    if (!utils.isString(presetName)) {\n      options = presetName || {};\n      presetName = 'default';\n    }\n  }\n\n  /**\n   * MarkdownIt#inline -> ParserInline\n   *\n   * Instance of [[ParserInline]]. You may need it to add new rules when\n   * writing plugins. For simple rules control use [[MarkdownIt.disable]] and\n   * [[MarkdownIt.enable]].\n   **/\n  this.inline = new ParserInline();\n\n  /**\n   * MarkdownIt#block -> ParserBlock\n   *\n   * Instance of [[ParserBlock]]. You may need it to add new rules when\n   * writing plugins. For simple rules control use [[MarkdownIt.disable]] and\n   * [[MarkdownIt.enable]].\n   **/\n  this.block = new ParserBlock();\n\n  /**\n   * MarkdownIt#core -> Core\n   *\n   * Instance of [[Core]] chain executor. You may need it to add new rules when\n   * writing plugins. For simple rules control use [[MarkdownIt.disable]] and\n   * [[MarkdownIt.enable]].\n   **/\n  this.core = new ParserCore();\n\n  /**\n   * MarkdownIt#renderer -> Renderer\n   *\n   * Instance of [[Renderer]]. Use it to modify output look. Or to add rendering\n   * rules for new token types, generated by plugins.\n   *\n   * ##### Example\n   *\n   * ```javascript\n   * var md = require('markdown-it')();\n   *\n   * function myToken(tokens, idx, options, env, self) {\n   *   //...\n   *   return result;\n   * };\n   *\n   * md.renderer.rules['my_token'] = myToken\n   * ```\n   *\n   * See [[Renderer]] docs and [source code](https://github.com/markdown-it/markdown-it/blob/master/lib/renderer.js).\n   **/\n  this.renderer = new Renderer();\n\n  /**\n   * MarkdownIt#linkify -> LinkifyIt\n   *\n   * [linkify-it](https://github.com/markdown-it/linkify-it) instance.\n   * Used by [linkify](https://github.com/markdown-it/markdown-it/blob/master/lib/rules_core/linkify.js)\n   * rule.\n   **/\n  this.linkify = new LinkifyIt();\n\n  /**\n   * MarkdownIt#validateLink(url) -> Boolean\n   *\n   * Link validation function. CommonMark allows too much in links. By default\n   * we disable `javascript:`, `vbscript:`, `file:` schemas, and almost all `data:...` schemas\n   * except some embedded image types.\n   *\n   * You can change this behaviour:\n   *\n   * ```javascript\n   * var md = require('markdown-it')();\n   * // enable everything\n   * md.validateLink = function () { return true; }\n   * ```\n   **/\n  this.validateLink = validateLink;\n\n  /**\n   * MarkdownIt#normalizeLink(url) -> String\n   *\n   * Function used to encode link url to a machine-readable format,\n   * which includes url-encoding, punycode, etc.\n   **/\n  this.normalizeLink = normalizeLink;\n\n  /**\n   * MarkdownIt#normalizeLinkText(url) -> String\n   *\n   * Function used to decode link url to a human-readable format`\n   **/\n  this.normalizeLinkText = normalizeLinkText;\n\n\n  // Expose utils & helpers for easy acces from plugins\n\n  /**\n   * MarkdownIt#utils -> utils\n   *\n   * Assorted utility functions, useful to write plugins. See details\n   * [here](https://github.com/markdown-it/markdown-it/blob/master/lib/common/utils.js).\n   **/\n  this.utils = utils;\n\n  /**\n   * MarkdownIt#helpers -> helpers\n   *\n   * Link components parser functions, useful to write plugins. See details\n   * [here](https://github.com/markdown-it/markdown-it/blob/master/lib/helpers).\n   **/\n  this.helpers = utils.assign({}, helpers);\n\n\n  this.options = {};\n  this.configure(presetName);\n\n  if (options) { this.set(options); }\n}\n\n\n/** chainable\n * MarkdownIt.set(options)\n *\n * Set parser options (in the same format as in constructor). Probably, you\n * will never need it, but you can change options after constructor call.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')()\n *             .set({ html: true, breaks: true })\n *             .set({ typographer, true });\n * ```\n *\n * __Note:__ To achieve the best possible performance, don't modify a\n * `markdown-it` instance options on the fly. If you need multiple configurations\n * it's best to create multiple instances and initialize each with separate\n * config.\n **/\nMarkdownIt.prototype.set = function (options) {\n  utils.assign(this.options, options);\n  return this;\n};\n\n\n/** chainable, internal\n * MarkdownIt.configure(presets)\n *\n * Batch load of all options and compenent settings. This is internal method,\n * and you probably will not need it. But if you will - see available presets\n * and data structure [here](https://github.com/markdown-it/markdown-it/tree/master/lib/presets)\n *\n * We strongly recommend to use presets instead of direct config loads. That\n * will give better compatibility with next versions.\n **/\nMarkdownIt.prototype.configure = function (presets) {\n  var self = this, presetName;\n\n  if (utils.isString(presets)) {\n    presetName = presets;\n    presets = config[presetName];\n    if (!presets) { throw new Error('Wrong `markdown-it` preset \"' + presetName + '\", check name'); }\n  }\n\n  if (!presets) { throw new Error('Wrong `markdown-it` preset, can\\'t be empty'); }\n\n  if (presets.options) { self.set(presets.options); }\n\n  if (presets.components) {\n    Object.keys(presets.components).forEach(function (name) {\n      if (presets.components[name].rules) {\n        self[name].ruler.enableOnly(presets.components[name].rules);\n      }\n      if (presets.components[name].rules2) {\n        self[name].ruler2.enableOnly(presets.components[name].rules2);\n      }\n    });\n  }\n  return this;\n};\n\n\n/** chainable\n * MarkdownIt.enable(list, ignoreInvalid)\n * - list (String|Array): rule name or list of rule names to enable\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * Enable list or rules. It will automatically find appropriate components,\n * containing rules with given names. If rule not found, and `ignoreInvalid`\n * not set - throws exception.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')()\n *             .enable(['sub', 'sup'])\n *             .disable('smartquotes');\n * ```\n **/\nMarkdownIt.prototype.enable = function (list, ignoreInvalid) {\n  var result = [];\n\n  if (!Array.isArray(list)) { list = [ list ]; }\n\n  [ 'core', 'block', 'inline' ].forEach(function (chain) {\n    result = result.concat(this[chain].ruler.enable(list, true));\n  }, this);\n\n  result = result.concat(this.inline.ruler2.enable(list, true));\n\n  var missed = list.filter(function (name) { return result.indexOf(name) < 0; });\n\n  if (missed.length && !ignoreInvalid) {\n    throw new Error('MarkdownIt. Failed to enable unknown rule(s): ' + missed);\n  }\n\n  return this;\n};\n\n\n/** chainable\n * MarkdownIt.disable(list, ignoreInvalid)\n * - list (String|Array): rule name or list of rule names to disable.\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * The same as [[MarkdownIt.enable]], but turn specified rules off.\n **/\nMarkdownIt.prototype.disable = function (list, ignoreInvalid) {\n  var result = [];\n\n  if (!Array.isArray(list)) { list = [ list ]; }\n\n  [ 'core', 'block', 'inline' ].forEach(function (chain) {\n    result = result.concat(this[chain].ruler.disable(list, true));\n  }, this);\n\n  result = result.concat(this.inline.ruler2.disable(list, true));\n\n  var missed = list.filter(function (name) { return result.indexOf(name) < 0; });\n\n  if (missed.length && !ignoreInvalid) {\n    throw new Error('MarkdownIt. Failed to disable unknown rule(s): ' + missed);\n  }\n  return this;\n};\n\n\n/** chainable\n * MarkdownIt.use(plugin, params)\n *\n * Load specified plugin with given params into current parser instance.\n * It's just a sugar to call `plugin(md, params)` with curring.\n *\n * ##### Example\n *\n * ```javascript\n * var iterator = require('markdown-it-for-inline');\n * var md = require('markdown-it')()\n *             .use(iterator, 'foo_replace', 'text', function (tokens, idx) {\n *               tokens[idx].content = tokens[idx].content.replace(/foo/g, 'bar');\n *             });\n * ```\n **/\nMarkdownIt.prototype.use = function (plugin /*, params, ... */) {\n  var args = [ this ].concat(Array.prototype.slice.call(arguments, 1));\n  plugin.apply(plugin, args);\n  return this;\n};\n\n\n/** internal\n * MarkdownIt.parse(src, env) -> Array\n * - src (String): source string\n * - env (Object): environment sandbox\n *\n * Parse input string and return list of block tokens (special token type\n * \"inline\" will contain list of inline tokens). You should not call this\n * method directly, until you write custom renderer (for example, to produce\n * AST).\n *\n * `env` is used to pass data between \"distributed\" rules and return additional\n * metadata like reference info, needed for the renderer. It also can be used to\n * inject data in specific cases. Usually, you will be ok to pass `{}`,\n * and then pass updated object to renderer.\n **/\nMarkdownIt.prototype.parse = function (src, env) {\n  if (typeof src !== 'string') {\n    throw new Error('Input data should be a String');\n  }\n\n  var state = new this.core.State(src, this, env);\n\n  this.core.process(state);\n\n  return state.tokens;\n};\n\n\n/**\n * MarkdownIt.render(src [, env]) -> String\n * - src (String): source string\n * - env (Object): environment sandbox\n *\n * Render markdown string into html. It does all magic for you :).\n *\n * `env` can be used to inject additional metadata (`{}` by default).\n * But you will not need it with high probability. See also comment\n * in [[MarkdownIt.parse]].\n **/\nMarkdownIt.prototype.render = function (src, env) {\n  env = env || {};\n\n  return this.renderer.render(this.parse(src, env), this.options, env);\n};\n\n\n/** internal\n * MarkdownIt.parseInline(src, env) -> Array\n * - src (String): source string\n * - env (Object): environment sandbox\n *\n * The same as [[MarkdownIt.parse]] but skip all block rules. It returns the\n * block tokens list with the single `inline` element, containing parsed inline\n * tokens in `children` property. Also updates `env` object.\n **/\nMarkdownIt.prototype.parseInline = function (src, env) {\n  var state = new this.core.State(src, this, env);\n\n  state.inlineMode = true;\n  this.core.process(state);\n\n  return state.tokens;\n};\n\n\n/**\n * MarkdownIt.renderInline(src [, env]) -> String\n * - src (String): source string\n * - env (Object): environment sandbox\n *\n * Similar to [[MarkdownIt.render]] but for single paragraph content. Result\n * will NOT be wrapped into `<p>` tags.\n **/\nMarkdownIt.prototype.renderInline = function (src, env) {\n  env = env || {};\n\n  return this.renderer.render(this.parseInline(src, env), this.options, env);\n};\n\n\nmodule.exports = MarkdownIt;\n", "'use strict';\n\n\nmodule.exports = require('./lib/');\n", "'use strict';\n/* eslint complexity: \"off\" */\n\n\n/**\n * A minimalist `markdown-it` plugin for parsing video/audio references inside\n * markdown image syntax as `<video>` / `<audio>` tags.\n *\n * @namespace HTML5Media\n */\n\n// We can only detect video/audio files from the extension in the URL.\n// We ignore MP1 and MP2 (not in active use) and default to video for ambiguous\n// extensions (MPG, MP4)\nconst validAudioExtensions = ['aac', 'm4a', 'mp3', 'oga', 'ogg', 'wav'];\nconst validVideoExtensions = ['mp4', 'm4v', 'ogv', 'webm', 'mpg', 'mpeg'];\n\n/**\n * @property {Object} messages\n * @property {Object} messages.languageCode\n *  a set of messages identified with a language code, typically an ISO639 code\n * @property {String} messages.languageCode.messageKey\n *  an individual translation of a message to that language, identified with a\n *  message key\n * @typedef {Object} MessagesObj\n */\nlet messages = {\n  en: {\n    'html5 video not supported': 'Your browser does not support playing HTML5 video.',\n    'html5 audio not supported': 'Your browser does not support playing HTML5 audio.',\n    'html5 media fallback link': 'You can <a href=\"%s\" download>download the file</a> instead.',\n    'html5 media description': 'Here is a description of the content: %s'\n  }\n};\n\n/**\n * You can override this function using options.translateFn.\n *\n * @param {String} language\n *  a language code, typically an ISO 639-[1-3] code.\n * @param {String} messageKey\n *  an identifier for the message, typically a short descriptive text\n * @param {String[]} messageParams\n *  Strings to be substituted into the message using some pattern, e.g., %s or\n *  %1$s, %2$s. By default we only use a simple %s pattern.\n * @returns {String}\n *  the translation to use\n * @memberof HTML5Media\n */\nlet translate = function(language, messageKey, messageParams) {\n\n  // Revert back to English default if no message object, or no translation\n  // for this language\n  if (!messages[language] || !messages[language][messageKey])\n    language = 'en';\n\n  if (!messages[language])\n    return '';\n\n  let message = messages[language][messageKey] || '';\n\n  if (messageParams)\n    for (let param of messageParams)\n      message = message.replace('%s', param);\n\n  return message;\n};\n\n\n/**\n * A fork of the built-in image tokenizer which guesses video/audio files based\n * on their extension, and tokenizes them accordingly.\n *\n * @param {Object} state\n *  Markdown-It state\n * @param {Boolean} silent\n *  if true, only validate, don't tokenize\n * @param {MarkdownIt} md\n *  instance of Markdown-It used for utility functions\n * @returns {Boolean}\n * @memberof HTML5Media\n */\nfunction tokenizeImagesAndMedia(state, silent, md) {\n  let attrs, code, content, label, labelEnd, labelStart, pos, ref, res, title,\n    token, tokens, start;\n  let href = '',\n    oldPos = state.pos,\n    max = state.posMax;\n\n  // Exclamation mark followed by open square bracket - ![ - otherwise abort\n  if (state.src.charCodeAt(state.pos) !== 0x21 ||\n    state.src.charCodeAt(state.pos + 1) !== 0x5B)\n    return false;\n\n  labelStart = state.pos + 2;\n  labelEnd = state.md.helpers.parseLinkLabel(state, state.pos + 1, false);\n\n  // Parser failed to find ']', so it's not a valid link\n  if (labelEnd < 0)\n    return false;\n\n  pos = labelEnd + 1;\n  if (pos < max && state.src.charCodeAt(pos) === 0x28) { // Parenthesis: (\n    //\n    // Inline link\n    //\n\n    // [link](  <href>  \"title\"  )\n    //        ^^ skipping these spaces\n    pos++;\n    for (; pos < max; pos++) {\n      code = state.src.charCodeAt(pos);\n      if (!md.utils.isSpace(code) && code !== 0x0A) // LF \\n\n        break;\n    }\n    if (pos >= max)\n      return false;\n\n    // [link](  <href>  \"title\"  )\n    //          ^^^^^^ parsing link destination\n    start = pos;\n    res = state.md.helpers.parseLinkDestination(state.src, pos, state.posMax);\n    if (res.ok) {\n      href = state.md.normalizeLink(res.str);\n      if (state.md.validateLink(href)) {\n        pos = res.pos;\n      } else {\n        href = '';\n      }\n    }\n\n    // [link](  <href>  \"title\"  )\n    //                ^^ skipping these spaces\n    start = pos;\n    for (; pos < max; pos++) {\n      code = state.src.charCodeAt(pos);\n      if (!md.utils.isSpace(code) && code !== 0x0A)\n        break;\n    }\n\n    // [link](  <href>  \"title\"  )\n    //                  ^^^^^^^ parsing link title\n    res = state.md.helpers.parseLinkTitle(state.src, pos, state.posMax);\n    if (pos < max && start !== pos && res.ok) {\n      title = res.str;\n      pos = res.pos;\n\n      // [link](  <href>  \"title\"  )\n      //                         ^^ skipping these spaces\n      for (; pos < max; pos++) {\n        code = state.src.charCodeAt(pos);\n        if (!md.utils.isSpace(code) && code !== 0x0A)\n          break;\n      }\n    } else {\n      title = '';\n    }\n\n    if (pos >= max || state.src.charCodeAt(pos) !== 0x29) { // Parenthesis: )\n      state.pos = oldPos;\n      return false;\n    }\n    pos++;\n  } else {\n    //\n    // Link reference\n    //\n    if (typeof state.env.references === 'undefined')\n      return false;\n\n    if (pos < max && state.src.charCodeAt(pos) === 0x5B) { // Bracket: [\n      start = pos + 1;\n      pos = state.md.helpers.parseLinkLabel(state, pos);\n      if (pos >= 0) {\n        label = state.src.slice(start, pos++);\n      } else {\n        pos = labelEnd + 1;\n      }\n    } else {\n      pos = labelEnd + 1;\n    }\n\n    // covers label === '' and label === undefined\n    // (collapsed reference link and shortcut reference link respectively)\n    if (!label)\n      label = state.src.slice(labelStart, labelEnd);\n\n    ref = state.env.references[md.utils.normalizeReference(label)];\n    if (!ref) {\n      state.pos = oldPos;\n      return false;\n    }\n    href = ref.href;\n    title = ref.title;\n  }\n\n  state.pos = pos;\n  state.posMax = max;\n\n  if (silent)\n    return true;\n\n  // We found the end of the link, and know for a fact it's a valid link;\n  // so all that's left to do is to call tokenizer.\n  content = state.src.slice(labelStart, labelEnd);\n\n  state.md.inline.parse(\n    content,\n    state.md,\n    state.env,\n    tokens = []\n  );\n\n  const mediaType = guessMediaType(href);\n  const tag = mediaType == 'image' ? 'img' : mediaType;\n\n  token = state.push(mediaType, tag, 0);\n  token.attrs = attrs = [\n    ['src', href]\n  ];\n  if (mediaType == 'image')\n    attrs.push(['alt', '']);\n  token.children = tokens;\n  token.content = content;\n\n  if (title)\n    attrs.push(['title', title]);\n\n  state.pos = pos;\n  state.posMax = max;\n  return true;\n\n}\n\n\n/**\n * Guess the media type represented by a URL based on the file extension,\n * if any\n *\n * @param {String} url\n *  any valid URL\n * @returns {String}\n *  a type identifier: 'image' (default for all unrecognized URLs), 'audio'\n *  or 'video'\n * @memberof HTML5Media\n */\nfunction guessMediaType(url) {\n  const extensionMatch = url.match(/\\.([^/.]+)$/);\n  if (extensionMatch === null)\n    return 'image';\n  const extension = extensionMatch[1];\n  if (validAudioExtensions.indexOf(extension.toLowerCase()) != -1)\n    return 'audio';\n  else if (validVideoExtensions.indexOf(extension.toLowerCase()) != -1)\n    return 'video';\n  else\n    return 'image';\n}\n\n\n/**\n * Render tokens of the video/audio type to HTML5 tags\n *\n * @param {Object} tokens\n *  token stream\n * @param {Number} idx\n *  which token are we rendering\n * @param {Object} options\n *  Markdown-It options, including this plugin's settings\n * @param {Object} env\n *  Markdown-It environment, potentially including language setting\n * @param {MarkdownIt} md\n *  instance used for utilities access\n * @returns {String}\n *  rendered token\n * @memberof HTML5Media\n */\nfunction renderMedia(tokens, idx, options, env, md) {\n  const token = tokens[idx];\n  const type = token.type;\n  if (type !== 'video' && type !== 'audio')\n    return '';\n  let attrs = options.html5Media[`${type}Attrs`].trim();\n  if (attrs)\n    attrs = ' ' + attrs;\n\n  // We'll always have a URL for non-image media: they are detected by URL\n  const url = token.attrs[token.attrIndex('src')][1];\n\n  // Title is set like this: ![descriptive text](video.mp4 \"title\")\n  const title = token.attrIndex('title') != -1 ?\n    ` title=\"${md.utils.escapeHtml(token.attrs[token.attrIndex('title')][1])}\"` :\n    '';\n\n  const fallbackText =\n    translate(env.language, `html5 ${type} not supported`) + '\\n' +\n    translate(env.language, 'html5 media fallback link', [url]);\n\n  const description = token.content ?\n    '\\n' + translate(env.language, 'html5 media description', [md.utils.escapeHtml(token.content)]) :\n    '';\n\n  return `<${type} src=\"${url}\"${title}${attrs}>\\n` +\n    `${fallbackText}${description}\\n` +\n    `</${type}>`;\n}\n\n\n/**\n * The main plugin function, exported as module.exports\n *\n * @param {MarkdownIt} md\n *  instance, automatically passed by md.use\n * @param {Object} [options]\n *  configuration\n * @param {String} [options.videoAttrs='controls class=\"html5-video-player\"']\n *  attributes to include inside `<video>` tags\n * @param {String} [options.audioAttrs='controls class=\"html5-audio-player\"']\n *  attributes to include inside `<audio>` tags\n * @param {MessagesObj} [options.messages=built-in messages]\n *  human-readable text that is part of the output\n * @memberof HTML5Media\n */\nfunction html5Media(md, options = {}) {\n  if (options.messages)\n    messages = options.messages;\n  if (options.translateFn)\n    translate = options.translateFn;\n\n  const videoAttrs = options.videoAttrs !== undefined ?\n    options.videoAttrs :\n    'controls class=\"html5-video-player\"';\n  const audioAttrs = options.audioAttrs !== undefined ?\n    options.audioAttrs :\n    'controls class=\"html5-audio-player\"';\n\n  md.inline.ruler.at('image', (tokens, silent) => tokenizeImagesAndMedia(tokens, silent, md));\n\n  md.renderer.rules.video = md.renderer.rules.audio =\n    (tokens, idx, opt, env) => {\n      opt.html5Media = {\n        videoAttrs,\n        audioAttrs\n      };\n      return renderMedia(tokens, idx, opt, env, md);\n    };\n}\n\nmodule.exports = {\n  html5Media,\n  messages, // For partial customization of messages\n  guessMediaType\n};\n", "/**\n * amis-ui v6.12.0\n * Copyright 2018-2025 fex\n */\n\nimport { __extends, __awaiter, __generator } from 'tslib';\nimport React__default from 'react';\nimport markdownIt from 'markdown-it';\nimport { html5Media } from 'markdown-it-html5-media';\n\nvar doMarkdown = markdownIt();\ndoMarkdown.use(html5Media);\nfunction markdown(content, options) {\n    if (options) {\n        doMarkdown.set(options);\n    }\n    return doMarkdown.render(content);\n}\nvar Markdown = /** @class */ (function (_super) {\n    __extends(Markdown, _super);\n    function Markdown(props) {\n        var _this = _super.call(this, props) || this;\n        _this.htmlRef = _this.htmlRef.bind(_this);\n        return _this;\n    }\n    Markdown.prototype.htmlRef = function (dom) {\n        this.dom = dom;\n        if (!dom) {\n            return;\n        }\n        this._render();\n    };\n    Markdown.prototype.componentDidUpdate = function (nextProps) {\n        if (this.props.content !== nextProps.content) {\n            this._render();\n        }\n    };\n    Markdown.prototype._render = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var _a, content, options;\n            return __generator(this, function (_b) {\n                _a = this.props, content = _a.content, options = _a.options;\n                this.dom.innerHTML = markdown(content, options);\n                // @ts-ignore 需要用户手动加载 katex\n                if (typeof renderMathInElement === 'function') {\n                    // @ts-ignore\n                    renderMathInElement(this.dom, {\n                        delimiters: [\n                            { left: '$$', right: '$$', display: true },\n                            { left: '$', right: '$', display: false }\n                        ]\n                    });\n                }\n                return [2 /*return*/];\n            });\n        });\n    };\n    Markdown.prototype.render = function () {\n        return (React__default.createElement(\"div\", { \"data-testid\": \"markdown-body\", className: \"markdown-body\", ref: this.htmlRef }));\n    };\n    Markdown.defaultProps = {\n        content: '',\n        options: {\n            linkify: true\n        }\n    };\n    return Markdown;\n}(React__default.Component));\n\nexport { Markdown as default, markdown };\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,uBAAC,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,IAAK,KAAI,KAAM,KAAI,KAAM,MAAK,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,KAAM,KAAI,KAAM,KAAI,OAAQ,KAAI,OAAQ,KAAI,IAAK,KAAI,KAAM,MAAK,KAAM,MAAK,QAAS,KAAI,QAAS,KAAI,SAAU,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,KAAM,KAAI,KAAM,KAAI,QAAS,KAAI,KAAM,KAAI,KAAM,KAAI,MAAO,KAAI,UAAW,KAAI,MAAO,KAAI,KAAM,KAAI,MAAO,KAAI,OAAQ,KAAI,UAAW,KAAI,UAAW,KAAI,UAAW,KAAI,UAAW,KAAI,UAAW,KAAI,UAAW,KAAI,UAAW,KAAI,UAAW,KAAI,QAAS,KAAI,OAAQ,KAAI,SAAU,KAAI,UAAW,KAAI,QAAS,KAAI,OAAQ,KAAI,SAAU,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,MAAK,MAAO,MAAK,QAAS,KAAI,IAAK,KAAI,KAAM,KAAI,KAAM,KAAI,MAAO,KAAI,MAAO,KAAI,eAAgB,KAAI,QAAS,KAAI,UAAW,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,MAAK,MAAO,MAAK,QAAS,KAAI,KAAM,KAAI,OAAQ,KAAI,SAAU,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,KAAI,MAAO,KAAI,UAAW,KAAI,OAAQ,KAAI,UAAW,KAAI,aAAc,KAAI,WAAY,KAAI,SAAU,KAAI,WAAY,KAAI,WAAY,KAAI,MAAO,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,UAAW,KAAI,MAAO,KAAI,UAAW,KAAI,OAAQ,KAAI,KAAM,KAAI,KAAM,KAAI,OAAQ,KAAI,QAAS,KAAI,SAAU,KAAI,SAAU,KAAI,SAAU,KAAI,OAAQ,KAAI,QAAS,KAAI,YAAa,KAAI,MAAO,KAAI,MAAO,KAAI,MAAO,KAAI,SAAU,KAAI,KAAM,MAAK,KAAM,MAAK,QAAS,KAAI,SAAU,KAAI,QAAS,KAAI,SAAU,KAAI,UAAW,KAAI,WAAY,KAAI,UAAW,KAAI,SAAU,KAAI,iBAAkB,KAAI,eAAgB,KAAI,UAAW,KAAI,QAAS,KAAI,UAAW,KAAI,QAAS,KAAI,cAAe,KAAI,aAAc,KAAI,eAAgB,KAAI,mBAAoB,KAAI,mBAAoB,KAAI,oBAAqB,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,KAAM,MAAK,SAAU,MAAK,MAAO,KAAI,MAAO,KAAI,MAAO,MAAK,MAAO,MAAK,KAAM,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,UAAW,KAAI,SAAU,KAAI,UAAW,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,MAAO,MAAK,MAAO,KAAI,OAAQ,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,MAAK,UAAW,KAAI,MAAO,KAAI,QAAS,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,UAAW,KAAI,QAAS,KAAI,KAAM,KAAI,KAAM,KAAI,QAAS,KAAI,QAAS,KAAI,sBAAuB,KAAI,MAAO,MAAK,OAAQ,KAAI,OAAQ,KAAI,SAAU,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,SAAU,KAAI,OAAQ,KAAI,SAAU,KAAI,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,SAAU,KAAI,SAAU,KAAI,MAAO,KAAI,WAAY,KAAI,WAAY,KAAI,KAAM,MAAK,KAAM,KAAI,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,WAAY,KAAI,KAAM,KAAI,KAAM,KAAI,MAAO,KAAI,QAAS,KAAI,iBAAkB,KAAI,kBAAmB,KAAI,YAAa,KAAI,aAAc,KAAI,aAAc,KAAI,WAAY,KAAI,UAAW,KAAI,UAAW,KAAI,aAAc,KAAI,YAAa,KAAI,aAAc,KAAI,KAAM,KAAI,MAAO,KAAI,MAAO,KAAI,UAAW,KAAI,QAAS,KAAI,SAAU,KAAI,0BAA2B,KAAI,uBAAwB,KAAI,iBAAkB,KAAI,OAAQ,KAAI,UAAW,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,SAAU,KAAI,OAAQ,KAAI,QAAS,KAAI,MAAO,KAAI,QAAS,KAAI,YAAa,KAAI,WAAY,KAAI,MAAO,KAAI,SAAU,KAAI,WAAY,KAAI,QAAS,KAAI,QAAS,KAAI,iBAAkB,KAAI,MAAO,MAAK,MAAO,KAAI,QAAS,KAAI,WAAY,KAAI,MAAO,KAAI,MAAO,KAAI,QAAS,KAAI,iCAAkC,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,MAAK,MAAO,MAAK,MAAO,KAAI,OAAQ,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,SAAU,KAAI,SAAU,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,SAAU,KAAI,UAAW,KAAI,QAAS,KAAI,QAAS,KAAI,KAAM,KAAI,KAAM,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,MAAO,MAAK,QAAS,KAAI,SAAU,KAAI,aAAc,KAAI,aAAc,KAAI,UAAW,KAAI,YAAa,KAAI,QAAS,KAAI,gBAAiB,KAAI,iBAAkB,KAAI,OAAQ,KAAI,OAAQ,KAAI,UAAW,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,KAAI,MAAO,KAAI,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,SAAU,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,KAAM,KAAI,KAAM,KAAI,SAAU,KAAI,OAAQ,KAAI,IAAK,KAAI,IAAK,KAAI,UAAW,KAAI,SAAU,KAAI,KAAM,KAAI,KAAM,KAAI,OAAQ,KAAI,OAAQ,KAAI,SAAU,KAAI,QAAS,KAAI,KAAM,MAAK,KAAM,MAAK,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,kBAAmB,KAAI,gBAAiB,KAAI,wBAAyB,KAAI,kBAAmB,KAAI,kBAAmB,KAAI,MAAO,KAAI,SAAU,KAAI,SAAU,KAAI,aAAc,KAAI,OAAQ,KAAI,KAAM,KAAI,eAAgB,KAAI,SAAU,KAAI,OAAQ,KAAI,KAAM,KAAI,QAAS,KAAI,eAAgB,KAAI,QAAS,KAAI,MAAO,KAAI,MAAO,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,MAAK,MAAO,MAAK,KAAM,KAAI,KAAM,KAAI,QAAS,KAAI,OAAQ,KAAI,UAAW,KAAI,UAAW,KAAI,UAAW,KAAI,SAAU,KAAI,WAAY,KAAI,gBAAiB,KAAI,uBAAwB,KAAI,WAAY,KAAI,iBAAkB,KAAI,iBAAkB,KAAI,sBAAuB,KAAI,eAAgB,KAAI,qBAAsB,KAAI,0BAA2B,KAAI,sBAAuB,KAAI,kBAAmB,KAAI,gBAAiB,KAAI,eAAgB,KAAI,mBAAoB,KAAI,mBAAoB,KAAI,cAAe,KAAI,WAAY,KAAI,WAAY,KAAI,WAAY,KAAI,kBAAmB,KAAI,WAAY,KAAI,gBAAiB,KAAI,iBAAkB,KAAI,kBAAmB,KAAI,qBAAsB,KAAI,mBAAoB,KAAI,mBAAoB,KAAI,gBAAiB,KAAI,oBAAqB,KAAI,oBAAqB,KAAI,iBAAkB,KAAI,cAAe,KAAI,SAAU,KAAI,UAAW,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,MAAK,MAAO,MAAK,MAAO,KAAI,MAAO,KAAI,MAAO,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,SAAU,KAAI,MAAO,KAAI,MAAO,KAAI,UAAW,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,KAAI,QAAS,KAAI,KAAM,KAAI,KAAM,KAAI,OAAQ,KAAI,MAAO,KAAI,MAAO,KAAI,MAAO,KAAI,IAAK,KAAI,OAAQ,KAAI,KAAM,MAAK,KAAM,MAAK,IAAK,KAAI,QAAS,KAAI,QAAS,KAAI,KAAM,KAAI,QAAS,KAAI,IAAK,KAAI,SAAU,KAAI,UAAW,KAAI,KAAM,KAAI,KAAM,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,UAAW,KAAI,kBAAmB,KAAI,QAAS,KAAI,sBAAuB,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,KAAI,KAAM,KAAI,KAAM,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,MAAK,MAAO,MAAK,MAAO,KAAI,QAAS,KAAI,OAAQ,KAAI,MAAO,KAAI,SAAU,KAAI,SAAU,KAAI,OAAQ,KAAI,QAAS,KAAI,SAAU,KAAI,OAAQ,KAAI,YAAa,KAAI,aAAc,KAAI,OAAQ,KAAI,QAAS,KAAI,YAAa,KAAI,QAAS,KAAI,aAAc,KAAI,OAAQ,KAAI,SAAU,KAAI,UAAW,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,MAAO,KAAI,MAAO,KAAI,KAAM,KAAI,KAAM,KAAI,KAAM,KAAI,KAAM,KAAI,MAAO,KAAI,MAAO,KAAI,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,QAAS,KAAI,aAAc,KAAI,cAAe,KAAI,cAAe,KAAI,eAAgB,KAAI,KAAM,KAAI,KAAM,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,QAAS,KAAI,KAAM,MAAK,KAAM,MAAK,OAAQ,KAAI,mBAAoB,KAAI,uBAAwB,KAAI,OAAQ,MAAK,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,KAAI,MAAO,MAAK,MAAO,MAAK,QAAS,KAAI,QAAS,KAAI,MAAO,KAAI,OAAQ,KAAI,YAAa,KAAI,UAAW,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,MAAK,MAAO,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,KAAM,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,KAAM,KAAI,KAAM,KAAI,MAAO,KAAI,MAAO,KAAI,IAAK,KAAI,IAAK,KAAI,KAAM,KAAI,KAAM,KAAI,KAAM,KAAI,MAAO,KAAI,UAAW,KAAI,OAAQ,KAAI,KAAM,KAAI,QAAS,KAAI,SAAU,KAAI,UAAW,KAAI,MAAO,MAAK,QAAS,KAAI,KAAM,MAAK,KAAM,MAAK,IAAK,KAAI,IAAK,KAAI,KAAM,KAAI,OAAQ,KAAI,MAAO,KAAI,MAAO,KAAI,KAAM,KAAI,IAAK,KAAI,KAAM,KAAI,KAAM,KAAI,MAAO,KAAI,UAAW,KAAI,KAAM,KAAI,KAAM,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,MAAK,MAAO,MAAK,OAAQ,KAAI,cAAe,KAAI,kBAAmB,KAAI,kBAAmB,KAAI,gBAAiB,KAAI,aAAc,KAAI,mBAAoB,KAAI,cAAe,KAAI,MAAO,MAAK,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,KAAI,OAAQ,KAAI,IAAK,KAAI,IAAK,KAAI,IAAK,KAAI,OAAQ,KAAI,QAAS,KAAI,SAAU,KAAI,WAAY,KAAI,QAAS,KAAI,QAAS,KAAI,WAAY,KAAI,YAAa,KAAI,SAAU,KAAI,QAAS,KAAI,WAAY,MAAK,MAAO,MAAK,OAAQ,KAAI,QAAS,KAAI,MAAO,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,SAAU,KAAI,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,KAAM,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,WAAY,KAAI,QAAS,KAAI,QAAS,KAAI,KAAM,MAAK,KAAM,KAAI,cAAe,KAAI,UAAW,KAAI,UAAW,KAAI,OAAQ,KAAI,QAAS,KAAI,eAAgB,KAAI,gBAAiB,KAAI,MAAO,MAAK,MAAO,KAAI,QAAS,KAAI,gBAAiB,KAAI,MAAO,MAAK,MAAO,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,cAAe,KAAI,WAAY,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,IAAK,KAAI,OAAQ,KAAI,OAAQ,KAAI,KAAM,KAAI,KAAM,KAAI,MAAO,KAAI,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,KAAM,KAAI,KAAM,MAAK,KAAM,KAAI,QAAS,KAAI,QAAS,KAAI,IAAK,KAAI,QAAS,KAAI,OAAQ,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,YAAa,KAAI,UAAW,KAAI,UAAW,KAAI,OAAQ,KAAI,IAAK,KAAI,MAAO,KAAI,OAAQ,KAAI,SAAU,KAAI,QAAS,KAAI,IAAK,KAAI,OAAQ,KAAI,UAAW,KAAI,QAAS,KAAI,QAAS,KAAI,KAAM,KAAI,KAAM,KAAI,UAAW,KAAI,UAAW,KAAI,UAAW,KAAI,cAAe,KAAI,UAAW,KAAI,SAAU,KAAI,gBAAiB,KAAI,gBAAiB,KAAI,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,MAAK,MAAO,MAAK,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,QAAS,KAAI,MAAO,MAAK,MAAO,KAAI,MAAO,KAAI,SAAU,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,OAAQ,KAAI,IAAK,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,KAAM,KAAI,KAAM,KAAI,KAAM,MAAK,KAAM,MAAK,OAAQ,KAAI,MAAO,MAAK,MAAO,MAAK,MAAO,MAAK,MAAO,MAAK,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,KAAM,KAAI,KAAM,KAAI,KAAM,MAAK,KAAM,MAAK,QAAS,KAAI,MAAO,KAAI,MAAO,KAAI,MAAO,KAAI,MAAO,KAAI,MAAO,MAAK,MAAO,MAAK,MAAO,MAAK,MAAO,MAAK,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,UAAW,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,QAAS,KAAI,KAAM,KAAI,YAAa,KAAI,OAAQ,KAAI,OAAQ,KAAI,SAAU,KAAI,MAAO,KAAI,MAAO,KAAI,MAAO,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,SAAU,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,KAAM,KAAI,MAAO,KAAI,OAAQ,MAAK,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,SAAU,KAAI,SAAU,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,MAAO,KAAI,KAAM,KAAI,KAAM,KAAI,MAAO,KAAI,OAAQ,KAAI,QAAS,KAAI,SAAU,KAAI,UAAW,KAAI,MAAO,KAAI,IAAK,KAAI,IAAK,KAAI,kBAAmB,KAAI,cAAe,KAAI,WAAY,KAAI,WAAY,KAAI,WAAY,KAAI,qBAAsB,KAAI,eAAgB,KAAI,aAAc,KAAI,mBAAoB,KAAI,mBAAoB,KAAI,mBAAoB,KAAI,gBAAiB,KAAI,WAAY,KAAI,iBAAkB,KAAI,eAAgB,KAAI,gBAAiB,KAAI,gBAAiB,KAAI,gBAAiB,KAAI,gBAAiB,KAAI,iBAAkB,KAAI,mBAAoB,KAAI,qBAAsB,KAAI,iBAAkB,KAAI,cAAe,KAAI,SAAU,KAAI,eAAgB,KAAI,gBAAiB,KAAI,iBAAkB,KAAI,cAAe,KAAI,mBAAoB,KAAI,kBAAmB,KAAI,iBAAkB,KAAI,iBAAkB,KAAI,cAAe,KAAI,eAAgB,KAAI,YAAa,KAAI,KAAM,KAAI,KAAM,KAAI,KAAM,KAAI,MAAO,KAAI,UAAW,KAAI,OAAQ,KAAI,KAAM,KAAI,QAAS,KAAI,SAAU,KAAI,UAAW,KAAI,MAAO,MAAK,QAAS,KAAI,YAAa,KAAI,SAAU,KAAI,WAAY,KAAI,YAAa,KAAI,kBAAmB,KAAI,eAAgB,KAAI,aAAc,KAAI,SAAU,KAAI,UAAW,KAAI,SAAU,KAAI,gBAAiB,KAAI,WAAY,KAAI,QAAS,KAAI,QAAS,KAAI,KAAM,MAAK,KAAM,MAAK,IAAK,KAAI,KAAM,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,OAAQ,KAAI,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,IAAK,KAAI,IAAK,KAAI,UAAW,KAAI,YAAa,KAAI,QAAS,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,YAAa,KAAI,QAAS,KAAI,MAAO,KAAI,UAAW,KAAI,KAAM,KAAI,KAAM,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,eAAgB,KAAI,eAAgB,KAAI,eAAgB,KAAI,oBAAqB,KAAI,oBAAqB,KAAI,oBAAqB,KAAI,YAAa,KAAI,gBAAiB,KAAI,gBAAiB,KAAI,gBAAiB,KAAI,eAAgB,KAAI,gBAAiB,KAAI,OAAQ,KAAI,MAAO,MAAK,MAAO,MAAK,QAAS,KAAI,SAAU,KAAI,QAAS,KAAI,QAAS,KAAI,gBAAiB,KAAI,iBAAkB,KAAI,KAAM,KAAI,SAAU,KAAI,MAAO,KAAI,MAAO,KAAI,QAAS,KAAI,OAAQ,KAAI,UAAW,KAAI,OAAQ,KAAI,QAAS,KAAI,KAAM,KAAI,OAAQ,KAAI,QAAS,KAAI,MAAO,MAAK,MAAO,KAAI,KAAM,KAAI,KAAM,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,KAAI,OAAQ,KAAI,IAAK,KAAI,IAAK,KAAI,IAAK,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,SAAU,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,UAAW,KAAI,SAAU,KAAI,WAAY,MAAK,MAAO,MAAK,MAAO,KAAI,MAAO,KAAI,MAAO,KAAI,SAAU,KAAI,KAAM,KAAI,KAAM,KAAI,QAAS,KAAI,YAAa,KAAI,YAAa,KAAI,UAAW,KAAI,QAAS,KAAI,QAAS,KAAI,KAAM,KAAI,KAAM,KAAI,OAAQ,KAAI,OAAQ,KAAI,eAAgB,KAAI,aAAc,KAAI,WAAY,KAAI,KAAM,MAAK,KAAM,MAAK,KAAM,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,KAAM,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,QAAS,KAAI,SAAU,KAAI,WAAY,KAAI,MAAO,KAAI,MAAO,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,MAAK,MAAO,MAAK,IAAK,KAAI,MAAO,MAAK,MAAO,KAAI,QAAS,KAAI,IAAK,KAAI,IAAK,KAAI,UAAW,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,MAAK,KAAM,KAAI,MAAO,MAAK,OAAQ,MAAK,OAAQ,KAAI,SAAU,KAAI,SAAU,KAAI,UAAW,KAAI,OAAQ,KAAI,MAAO,KAAI,OAAQ,MAAK,QAAS,MAAK,MAAO,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,UAAW,MAAK,MAAO,KAAI,KAAM,KAAI,KAAM,KAAI,OAAQ,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,SAAU,KAAI,IAAK,KAAI,OAAQ,MAAK,qBAAsB,KAAI,oBAAqB,KAAI,mBAAoB,KAAI,uBAAwB,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,MAAK,sBAAuB,KAAI,gBAAiB,KAAI,SAAU,MAAK,QAAS,KAAI,SAAU,KAAI,KAAM,MAAK,KAAM,MAAK,KAAM,MAAK,KAAM,KAAI,MAAO,KAAI,OAAQ,MAAK,WAAY,MAAK,MAAO,MAAK,KAAM,MAAK,OAAQ,KAAI,KAAM,MAAK,KAAM,KAAI,MAAO,KAAI,MAAO,MAAK,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,IAAK,KAAI,KAAM,KAAI,MAAO,KAAI,KAAM,KAAI,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,KAAI,KAAM,MAAK,KAAM,KAAI,YAAa,KAAI,YAAa,KAAI,iBAAkB,KAAI,iBAAkB,KAAI,MAAO,KAAI,OAAQ,MAAK,WAAY,MAAK,MAAO,MAAK,OAAQ,KAAI,KAAM,MAAK,OAAQ,KAAI,KAAM,MAAK,KAAM,KAAI,OAAQ,KAAI,QAAS,KAAI,MAAO,MAAK,MAAO,KAAI,SAAU,KAAI,kBAAmB,KAAI,MAAO,MAAK,MAAO,KAAI,KAAM,KAAI,KAAM,KAAI,cAAe,KAAI,WAAY,KAAI,sBAAuB,KAAI,YAAa,KAAI,UAAW,KAAI,eAAgB,MAAK,WAAY,KAAI,YAAa,KAAI,iBAAkB,KAAI,qBAAsB,MAAK,mBAAoB,MAAK,gBAAiB,KAAI,sBAAuB,MAAK,iBAAkB,KAAI,iBAAkB,MAAK,cAAe,MAAK,OAAQ,KAAI,UAAW,MAAK,QAAS,MAAK,SAAU,KAAI,SAAU,KAAI,SAAU,KAAI,oBAAqB,MAAK,iBAAkB,KAAI,sBAAuB,KAAI,SAAU,KAAI,cAAe,KAAI,gBAAiB,KAAI,aAAc,MAAK,mBAAoB,MAAK,cAAe,KAAI,yBAA0B,MAAK,mBAAoB,MAAK,OAAQ,KAAI,SAAU,KAAI,SAAU,KAAI,SAAU,KAAI,aAAc,KAAI,kBAAmB,MAAK,uBAAwB,KAAI,mBAAoB,KAAI,qBAAsB,MAAK,kBAAmB,KAAI,uBAAwB,KAAI,iBAAkB,MAAK,sBAAuB,KAAI,mBAAoB,MAAK,wBAAyB,KAAI,WAAY,MAAK,gBAAiB,KAAI,aAAc,KAAI,kBAAmB,MAAK,uBAAwB,KAAI,kBAAmB,MAAK,aAAc,MAAK,kBAAmB,KAAI,UAAW,KAAI,eAAgB,KAAI,mBAAoB,KAAI,eAAgB,KAAI,gBAAiB,KAAI,WAAY,KAAI,MAAO,KAAI,QAAS,MAAK,OAAQ,MAAK,SAAU,KAAI,KAAM,KAAI,QAAS,KAAI,OAAQ,KAAI,SAAU,MAAK,MAAO,MAAK,QAAS,MAAK,OAAQ,KAAI,OAAQ,KAAI,QAAS,MAAK,aAAc,KAAI,aAAc,KAAI,OAAQ,KAAI,QAAS,KAAI,KAAM,KAAI,QAAS,KAAI,MAAO,MAAK,MAAO,MAAK,MAAO,MAAK,WAAY,KAAI,gBAAiB,KAAI,MAAO,KAAI,OAAQ,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,SAAU,KAAI,SAAU,KAAI,MAAO,KAAI,OAAQ,MAAK,OAAQ,KAAI,SAAU,MAAK,WAAY,KAAI,YAAa,MAAK,OAAQ,KAAI,SAAU,MAAK,MAAO,KAAI,OAAQ,MAAK,OAAQ,KAAI,SAAU,MAAK,WAAY,KAAI,YAAa,MAAK,MAAO,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,KAAI,eAAgB,KAAI,iBAAkB,KAAI,gBAAiB,KAAI,kBAAmB,KAAI,IAAK,KAAI,IAAK,KAAI,KAAM,KAAI,QAAS,KAAI,OAAQ,KAAI,MAAO,MAAK,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,MAAK,MAAO,MAAK,QAAS,KAAI,SAAU,KAAI,QAAS,KAAI,MAAO,MAAK,MAAO,MAAK,SAAU,MAAK,QAAS,KAAI,SAAU,MAAK,OAAQ,MAAK,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,SAAU,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,KAAI,KAAM,KAAI,KAAM,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,KAAI,MAAO,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,KAAM,MAAK,KAAM,MAAK,MAAO,KAAI,QAAS,KAAI,QAAS,KAAI,KAAM,KAAI,OAAQ,KAAI,KAAM,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,SAAU,KAAI,OAAQ,KAAI,KAAM,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,SAAU,KAAI,SAAU,KAAI,MAAO,KAAI,QAAS,KAAI,MAAO,MAAK,MAAO,MAAK,MAAO,KAAI,sBAAuB,KAAI,gBAAiB,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,IAAK,KAAI,IAAK,KAAI,KAAM,KAAI,OAAQ,KAAI,SAAU,KAAI,MAAO,KAAI,MAAO,KAAI,QAAS,KAAI,MAAO,KAAI,SAAU,KAAI,KAAM,KAAI,IAAK,KAAI,MAAO,MAAK,MAAO,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,KAAI,QAAS,KAAI,QAAS,KAAI,UAAW,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,SAAU,KAAI,WAAY,KAAI,aAAc,KAAI,iBAAkB,KAAI,MAAO,KAAI,UAAW,KAAI,KAAM,KAAI,QAAS,KAAI,OAAQ,KAAI,MAAO,KAAI,UAAW,KAAI,KAAM,KAAI,KAAM,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,KAAI,SAAU,KAAI,KAAM,MAAK,KAAM,MAAK,KAAM,KAAI,KAAM,KAAI,MAAO,KAAI,QAAS,KAAI,OAAQ,KAAI,IAAK,KAAI,IAAK,KAAI,WAAY,KAAI,KAAM,KAAI,QAAS,KAAI,SAAU,KAAI,QAAS,KAAI,UAAW,KAAI,OAAQ,KAAI,SAAU,KAAI,MAAO,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,WAAY,KAAI,QAAS,KAAI,SAAU,KAAI,SAAU,KAAI,IAAK,KAAI,eAAgB,KAAI,UAAW,KAAI,MAAO,MAAK,MAAO,KAAI,OAAQ,KAAI,MAAO,KAAI,IAAK,KAAI,IAAK,KAAI,OAAQ,KAAI,YAAa,KAAI,MAAO,KAAI,aAAc,KAAI,UAAW,KAAI,eAAgB,KAAI,oBAAqB,KAAI,eAAgB,KAAI,QAAS,KAAI,aAAc,KAAI,UAAW,KAAI,UAAW,KAAI,KAAM,KAAI,KAAM,KAAI,SAAU,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,OAAQ,KAAI,MAAO,KAAI,QAAS,KAAI,MAAO,KAAI,SAAU,KAAI,UAAW,KAAI,UAAW,KAAI,UAAW,KAAI,MAAO,KAAI,cAAe,KAAI,YAAa,KAAI,QAAS,KAAI,OAAQ,KAAI,QAAS,KAAI,MAAO,MAAK,MAAO,MAAK,KAAM,KAAI,KAAM,KAAI,QAAS,KAAI,KAAM,MAAK,KAAM,MAAK,MAAO,KAAI,MAAO,MAAK,MAAO,KAAI,QAAS,KAAI,MAAO,MAAK,MAAO,MAAK,aAAc,KAAI,SAAU,KAAI,OAAQ,KAAI,SAAU,KAAI,MAAO,KAAK,MAAO,KAAK,OAAQ,KAAI,MAAO,MAAK,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,UAAW,KAAI,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,OAAQ,KAAI,QAAS,KAAI,OAAQ,KAAI,SAAU,KAAI,OAAQ,KAAI,MAAO,KAAI,MAAO,KAAI,MAAO,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,SAAU,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,WAAY,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,SAAU,KAAI,SAAU,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,MAAO,KAAI,KAAM,KAAI,KAAM,KAAI,MAAO,KAAI,SAAU,KAAI,OAAQ,KAAI,QAAS,KAAI,MAAO,KAAI,MAAO,KAAI,SAAU,KAAI,UAAW,KAAI,OAAQ,KAAI,IAAK,KAAI,MAAO,KAAI,KAAM,KAAI,KAAM,KAAI,gBAAiB,KAAI,oBAAqB,KAAI,sBAAuB,KAAI,QAAS,KAAI,QAAS,KAAI,KAAM,MAAK,KAAM,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,KAAM,KAAI,KAAM,KAAI,MAAO,KAAI,mBAAoB,KAAI,eAAgB,KAAI,YAAa,KAAI,YAAa,KAAI,YAAa,KAAI,qBAAsB,KAAI,gBAAiB,KAAI,cAAe,KAAI,oBAAqB,KAAI,oBAAqB,KAAI,oBAAqB,KAAI,iBAAkB,KAAI,YAAa,KAAI,kBAAmB,KAAI,gBAAiB,KAAI,iBAAkB,KAAI,mBAAoB,KAAI,kBAAmB,KAAI,iBAAkB,KAAI,eAAgB,KAAI,UAAW,KAAI,gBAAiB,KAAI,iBAAkB,KAAI,kBAAmB,KAAI,eAAgB,KAAI,oBAAqB,KAAI,mBAAoB,KAAI,kBAAmB,KAAI,kBAAmB,KAAI,eAAgB,KAAI,gBAAiB,KAAI,aAAc,KAAI,MAAO,KAAI,cAAe,KAAI,OAAQ,KAAI,OAAQ,KAAI,KAAM,KAAI,YAAa,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,MAAK,MAAO,KAAI,QAAS,KAAI,SAAU,KAAI,cAAe,KAAI,MAAO,KAAI,QAAS,KAAI,UAAW,KAAI,OAAQ,KAAI,aAAc,KAAI,QAAS,KAAI,MAAO,MAAK,MAAO,KAAI,KAAM,KAAI,KAAM,KAAI,MAAO,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,UAAW,KAAI,aAAc,KAAI,SAAU,KAAI,IAAK,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,MAAO,KAAI,QAAS,KAAI,QAAS,KAAI,IAAK,KAAI,IAAK,KAAI,OAAQ,KAAI,KAAM,KAAI,KAAM,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,KAAI,QAAS,KAAI,UAAW,KAAI,OAAQ,KAAI,KAAM,KAAI,KAAM,KAAI,OAAQ,KAAI,MAAO,KAAI,OAAQ,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,SAAU,KAAI,MAAO,KAAI,MAAO,KAAI,QAAS,KAAI,UAAW,KAAI,OAAQ,KAAI,MAAO,KAAI,KAAM,MAAK,KAAM,MAAK,QAAS,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,KAAI,MAAO,KAAI,gBAAiB,KAAI,gBAAiB,KAAI,UAAW,KAAI,eAAgB,KAAI,iBAAkB,KAAI,cAAe,KAAI,KAAM,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,KAAM,KAAI,QAAS,KAAI,MAAO,KAAI,OAAQ,KAAI,MAAO,KAAI,OAAQ,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,SAAU,KAAI,SAAU,KAAI,OAAQ,KAAI,aAAc,KAAI,eAAgB,KAAI,QAAS,KAAI,UAAW,KAAI,MAAO,KAAI,OAAQ,KAAI,KAAM,KAAI,MAAO,KAAI,OAAQ,MAAK,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,KAAI,KAAM,KAAI,MAAO,MAAK,MAAO,MAAK,QAAS,KAAI,WAAY,KAAI,MAAO,KAAI,OAAQ,KAAI,QAAS,MAAK,OAAQ,KAAI,QAAS,MAAK,MAAO,KAAI,OAAQ,KAAI,QAAS,KAAI,UAAW,KAAI,YAAa,KAAI,OAAQ,KAAI,QAAS,KAAI,UAAW,KAAI,YAAa,KAAI,QAAS,KAAI,QAAS,KAAI,oBAAqB,KAAI,cAAe,KAAI,mBAAoB,KAAI,gBAAiB,KAAI,qBAAsB,KAAI,aAAc,KAAI,QAAS,KAAI,KAAM,KAAI,MAAO,KAAI,OAAQ,KAAI,MAAO,MAAK,MAAO,MAAK,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,iBAAkB,KAAI,aAAc,KAAI,OAAQ,KAAI,KAAM,KAAI,KAAM,KAAI,QAAS,KAAI,MAAO,KAAI,MAAO,KAAI,SAAU,KAAI,SAAU,KAAI,OAAQ,KAAI,OAAQ,KAAI,SAAU,KAAI,SAAU,KAAI,QAAS,KAAI,QAAS,KAAI,UAAW,KAAI,WAAY,KAAI,aAAc,KAAI,WAAY,KAAI,YAAa,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,YAAa,KAAI,MAAO,KAAI,aAAc,KAAI,UAAW,KAAI,eAAgB,KAAI,oBAAqB,KAAI,eAAgB,KAAI,QAAS,KAAI,aAAc,KAAI,UAAW,KAAI,UAAW,KAAI,SAAU,KAAI,UAAW,KAAI,KAAM,KAAI,KAAM,KAAI,MAAO,KAAI,MAAO,KAAI,MAAO,KAAI,MAAO,KAAI,KAAM,KAAI,KAAM,KAAI,QAAS,KAAI,SAAU,KAAI,MAAO,KAAI,MAAO,KAAI,SAAU,KAAI,UAAW,KAAI,eAAgB,KAAI,SAAU,KAAI,SAAU,KAAI,SAAU,KAAI,SAAU,KAAI,OAAQ,KAAI,OAAQ,KAAI,SAAU,KAAI,QAAS,KAAI,QAAS,KAAI,UAAW,KAAI,WAAY,KAAI,WAAY,KAAI,YAAa,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,SAAU,KAAI,QAAS,KAAI,OAAQ,KAAI,KAAM,KAAK,QAAS,KAAI,KAAM,KAAI,KAAM,KAAI,MAAO,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,KAAM,KAAI,KAAM,KAAI,MAAO,KAAI,QAAS,KAAI,KAAM,MAAK,KAAM,MAAK,QAAS,KAAI,WAAY,KAAI,WAAY,KAAI,OAAQ,KAAI,OAAQ,KAAI,UAAW,KAAI,QAAS,KAAI,aAAc,KAAI,UAAW,KAAI,YAAa,MAAK,WAAY,KAAI,QAAS,KAAI,OAAQ,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,YAAa,KAAI,gBAAiB,KAAI,YAAa,KAAI,UAAW,KAAI,QAAS,KAAI,OAAQ,KAAI,QAAS,KAAI,MAAO,KAAI,MAAO,KAAI,QAAS,KAAI,QAAS,KAAI,KAAM,KAAI,MAAO,MAAK,MAAO,MAAK,SAAU,KAAI,MAAO,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,UAAW,KAAI,cAAe,KAAI,cAAe,KAAI,gBAAiB,KAAI,WAAY,KAAI,eAAgB,KAAI,iBAAkB,KAAI,QAAS,KAAI,MAAO,KAAI,UAAW,KAAI,WAAY,KAAI,SAAU,KAAI,OAAQ,KAAI,SAAU,KAAI,UAAW,KAAI,MAAO,MAAK,MAAO,MAAK,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,kBAAmB,KAAI,mBAAoB,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,KAAI,MAAO,KAAI,MAAO,KAAI,UAAW,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,KAAM,KAAI,KAAM,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,QAAS,KAAI,KAAM,MAAK,KAAM,MAAK,QAAS,KAAI,QAAS,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,UAAW,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,KAAM,KAAI,UAAW,KAAI,YAAa,KAAI,cAAe,KAAI,kBAAmB,KAAI,OAAQ,KAAI,WAAY,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,MAAK,MAAO,MAAK,YAAa,KAAI,SAAU,KAAI,SAAU,KAAI,SAAU,KAAI,kBAAmB,KAAI,aAAc,KAAI,aAAc,KAAI,aAAc,KAAI,eAAgB,KAAI,eAAgB,KAAI,gBAAiB,KAAI,OAAQ,KAAI,gBAAiB,KAAI,iBAAkB,KAAI,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,SAAU,KAAI,SAAU,KAAI,YAAa,KAAI,OAAQ,KAAI,YAAa,KAAI,QAAS,KAAI,UAAW,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,MAAK,MAAO,MAAK,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,KAAI,MAAO,KAAI,SAAU,KAAI,QAAS,KAAI,YAAa,KAAI,UAAW,KAAI,YAAa,KAAI,QAAS,KAAI,OAAQ,KAAI,WAAY,KAAI,MAAO,KAAI,MAAO,KAAI,QAAS,KAAI,UAAW,KAAI,cAAe,MAAK,eAAgB,MAAK,cAAe,MAAK,eAAgB,MAAK,UAAW,KAAI,iBAAkB,KAAI,kBAAmB,KAAI,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,KAAM,KAAI,KAAM,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,KAAM,KAAI,KAAM,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,KAAI,MAAO,KAAI,aAAc,KAAI,cAAe,KAAI,mBAAoB,KAAI,eAAgB,KAAI,eAAgB,KAAI,KAAM,MAAK,KAAM,MAAK,OAAQ,KAAI,OAAQ,MAAK,OAAQ,MAAK,MAAO,MAAK,MAAO,MAAK,OAAQ,KAAI,OAAQ,KAAI,MAAO,MAAK,MAAO,MAAK,QAAS,MAAK,QAAS,MAAK,QAAS,MAAK,QAAS,MAAK,QAAS,KAAI,SAAU,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,QAAS,KAAI,QAAS,KAAI,KAAM,MAAK,KAAM,MAAK,MAAO,MAAK,MAAO,MAAK,IAAK,KAAI,IAAK,KAAI,QAAS,KAAI,MAAO,MAAK,MAAO,MAAK,MAAO,KAAI,OAAQ,KAAI,MAAO,KAAI,OAAQ,KAAI,KAAM,MAAK,KAAM,MAAK,OAAQ,KAAI,OAAQ,KAAI,IAAK,KAAI,IAAK,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,MAAO,MAAK,MAAO,MAAK,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,OAAQ,KAAI,MAAO,MAAK,MAAO,MAAK,QAAS,KAAI,QAAS,KAAI,OAAQ,KAAI,MAAO,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,MAAO,KAAI,MAAO,KAAI,OAAQ,KAAI,OAAQ,KAAI,KAAM,KAAI,KAAM,KAAI,KAAM,KAAI,KAAM,MAAK,KAAM,MAAK,MAAO,KAAI,MAAO,KAAI,MAAO,MAAK,MAAO,MAAK,MAAO,MAAK,MAAO,MAAK,MAAO,KAAI,MAAO,KAAI,MAAO,KAAI,MAAO,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,QAAS,KAAI,KAAM,KAAI,KAAM,KAAI,MAAO,KAAI,MAAO,KAAI,QAAS,KAAI,gBAAiB,KAAI,MAAO,KAAI,MAAO,KAAI,KAAM,MAAK,KAAM,KAAI,MAAO,KAAI,MAAO,KAAI,SAAU,KAAI,MAAO,MAAK,MAAO,KAAI,MAAO,MAAK,MAAO,MAAK,KAAM,KAAI,MAAO,IAAG;AAAA;AAAA;;;ACAt74B,IAAAA,oBAAA;AAAA;AAAA;AAKA,WAAO,UAAU;AAAA;AAAA;;;ACLjB;AAAA;AAAA,WAAO,UAAQ;AAAA;AAAA;;;ACAf;AAAA;AAAA;AAIA,QAAI,cAAc,CAAC;AAMnB,aAAS,eAAe,SAAS;AAC/B,UAAI,GAAG,IAAI,QAAQ,YAAY,OAAO;AACtC,UAAI,OAAO;AAAE,eAAO;AAAA,MAAO;AAE3B,cAAQ,YAAY,OAAO,IAAI,CAAC;AAEhC,WAAK,IAAI,GAAG,IAAI,KAAK,KAAK;AACxB,aAAK,OAAO,aAAa,CAAC;AAE1B,YAAI,cAAc,KAAK,EAAE,GAAG;AAE1B,gBAAM,KAAK,EAAE;AAAA,QACf,OAAO;AACL,gBAAM,KAAK,OAAO,MAAM,EAAE,SAAS,EAAE,EAAE,YAAY,GAAG,MAAM,EAAE,CAAC;AAAA,QACjE;AAAA,MACF;AAEA,WAAK,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACnC,cAAM,QAAQ,WAAW,CAAC,CAAC,IAAI,QAAQ,CAAC;AAAA,MAC1C;AAEA,aAAO;AAAA,IACT;AAUA,aAAS,OAAO,QAAQ,SAAS,aAAa;AAC5C,UAAI,GAAG,GAAG,MAAM,UAAU,OACtB,SAAS;AAEb,UAAI,OAAO,YAAY,UAAU;AAE/B,sBAAe;AACf,kBAAU,OAAO;AAAA,MACnB;AAEA,UAAI,OAAO,gBAAgB,aAAa;AACtC,sBAAc;AAAA,MAChB;AAEA,cAAQ,eAAe,OAAO;AAE9B,WAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AACzC,eAAO,OAAO,WAAW,CAAC;AAE1B,YAAI,eAAe,SAAS,MAAgB,IAAI,IAAI,GAAG;AACrD,cAAI,iBAAiB,KAAK,OAAO,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG;AACrD,sBAAU,OAAO,MAAM,GAAG,IAAI,CAAC;AAC/B,iBAAK;AACL;AAAA,UACF;AAAA,QACF;AAEA,YAAI,OAAO,KAAK;AACd,oBAAU,MAAM,IAAI;AACpB;AAAA,QACF;AAEA,YAAI,QAAQ,SAAU,QAAQ,OAAQ;AACpC,cAAI,QAAQ,SAAU,QAAQ,SAAU,IAAI,IAAI,GAAG;AACjD,uBAAW,OAAO,WAAW,IAAI,CAAC;AAClC,gBAAI,YAAY,SAAU,YAAY,OAAQ;AAC5C,wBAAU,mBAAmB,OAAO,CAAC,IAAI,OAAO,IAAI,CAAC,CAAC;AACtD;AACA;AAAA,YACF;AAAA,UACF;AACA,oBAAU;AACV;AAAA,QACF;AAEA,kBAAU,mBAAmB,OAAO,CAAC,CAAC;AAAA,MACxC;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,eAAiB;AACxB,WAAO,iBAAiB;AAGxB,WAAO,UAAU;AAAA;AAAA;;;ACjGjB;AAAA;AAAA;AAMA,QAAI,cAAc,CAAC;AAEnB,aAAS,eAAe,SAAS;AAC/B,UAAI,GAAG,IAAI,QAAQ,YAAY,OAAO;AACtC,UAAI,OAAO;AAAE,eAAO;AAAA,MAAO;AAE3B,cAAQ,YAAY,OAAO,IAAI,CAAC;AAEhC,WAAK,IAAI,GAAG,IAAI,KAAK,KAAK;AACxB,aAAK,OAAO,aAAa,CAAC;AAC1B,cAAM,KAAK,EAAE;AAAA,MACf;AAEA,WAAK,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACnC,aAAK,QAAQ,WAAW,CAAC;AACzB,cAAM,EAAE,IAAI,OAAO,MAAM,GAAG,SAAS,EAAE,EAAE,YAAY,GAAG,MAAM,EAAE;AAAA,MAClE;AAEA,aAAO;AAAA,IACT;AAKA,aAAS,OAAO,QAAQ,SAAS;AAC/B,UAAI;AAEJ,UAAI,OAAO,YAAY,UAAU;AAC/B,kBAAU,OAAO;AAAA,MACnB;AAEA,cAAQ,eAAe,OAAO;AAE9B,aAAO,OAAO,QAAQ,qBAAqB,SAAS,KAAK;AACvD,YAAI,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,KACtB,SAAS;AAEb,aAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,KAAK,GAAG;AACzC,eAAK,SAAS,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE;AAEzC,cAAI,KAAK,KAAM;AACb,sBAAU,MAAM,EAAE;AAClB;AAAA,UACF;AAEA,eAAK,KAAK,SAAU,OAAS,IAAI,IAAI,GAAI;AAEvC,iBAAK,SAAS,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE;AAEzC,iBAAK,KAAK,SAAU,KAAM;AACxB,oBAAQ,MAAM,IAAK,OAAU,KAAK;AAElC,kBAAI,MAAM,KAAM;AACd,0BAAU;AAAA,cACZ,OAAO;AACL,0BAAU,OAAO,aAAa,GAAG;AAAA,cACnC;AAEA,mBAAK;AACL;AAAA,YACF;AAAA,UACF;AAEA,eAAK,KAAK,SAAU,OAAS,IAAI,IAAI,GAAI;AAEvC,iBAAK,SAAS,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE;AACzC,iBAAK,SAAS,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE;AAEzC,iBAAK,KAAK,SAAU,QAAS,KAAK,SAAU,KAAM;AAChD,oBAAQ,MAAM,KAAM,QAAY,MAAM,IAAK,OAAU,KAAK;AAE1D,kBAAI,MAAM,QAAU,OAAO,SAAU,OAAO,OAAS;AACnD,0BAAU;AAAA,cACZ,OAAO;AACL,0BAAU,OAAO,aAAa,GAAG;AAAA,cACnC;AAEA,mBAAK;AACL;AAAA,YACF;AAAA,UACF;AAEA,eAAK,KAAK,SAAU,OAAS,IAAI,IAAI,GAAI;AAEvC,iBAAK,SAAS,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE;AACzC,iBAAK,SAAS,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE;AACzC,iBAAK,SAAS,IAAI,MAAM,IAAI,IAAI,IAAI,EAAE,GAAG,EAAE;AAE3C,iBAAK,KAAK,SAAU,QAAS,KAAK,SAAU,QAAS,KAAK,SAAU,KAAM;AACxE,oBAAQ,MAAM,KAAM,UAAc,MAAM,KAAM,SAAa,MAAM,IAAK,OAAU,KAAK;AAErF,kBAAI,MAAM,SAAW,MAAM,SAAU;AACnC,0BAAU;AAAA,cACZ,OAAO;AACL,uBAAO;AACP,0BAAU,OAAO,aAAa,SAAU,OAAO,KAAK,SAAU,MAAM,KAAM;AAAA,cAC5E;AAEA,mBAAK;AACL;AAAA,YACF;AAAA,UACF;AAEA,oBAAU;AAAA,QACZ;AAEA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAGA,WAAO,eAAiB;AACxB,WAAO,iBAAiB;AAGxB,WAAO,UAAU;AAAA;AAAA;;;ACzHjB;AAAA;AAAA;AAIA,WAAO,UAAU,SAAS,OAAO,KAAK;AACpC,UAAI,SAAS;AAEb,gBAAU,IAAI,YAAY;AAC1B,gBAAU,IAAI,UAAU,OAAO;AAC/B,gBAAU,IAAI,OAAO,IAAI,OAAO,MAAM;AAEtC,UAAI,IAAI,YAAY,IAAI,SAAS,QAAQ,GAAG,MAAM,IAAI;AAEpD,kBAAU,MAAM,IAAI,WAAW;AAAA,MACjC,OAAO;AACL,kBAAU,IAAI,YAAY;AAAA,MAC5B;AAEA,gBAAU,IAAI,OAAO,MAAM,IAAI,OAAO;AACtC,gBAAU,IAAI,YAAY;AAC1B,gBAAU,IAAI,UAAU;AACxB,gBAAU,IAAI,QAAQ;AAEtB,aAAO;AAAA,IACT;AAAA;AAAA;;;ACxBA;AAAA;AAAA;AA6CA,aAAS,MAAM;AACb,WAAK,WAAW;AAChB,WAAK,UAAU;AACf,WAAK,OAAO;AACZ,WAAK,OAAO;AACZ,WAAK,WAAW;AAChB,WAAK,OAAO;AACZ,WAAK,SAAS;AACd,WAAK,WAAW;AAAA,IAClB;AAMA,QAAI,kBAAkB;AAAtB,QACI,cAAc;AADlB,QAII,oBAAoB;AAJxB,QAQI,SAAS,CAAE,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,GAAK;AARzD,QAWI,SAAS,CAAE,KAAK,KAAK,KAAK,MAAM,KAAK,GAAI,EAAE,OAAO,MAAM;AAX5D,QAcI,aAAa,CAAE,GAAK,EAAE,OAAO,MAAM;AAdvC,QAmBI,eAAe,CAAE,KAAK,KAAK,KAAK,KAAK,GAAI,EAAE,OAAO,UAAU;AAnBhE,QAoBI,kBAAkB,CAAE,KAAK,KAAK,GAAI;AApBtC,QAqBI,iBAAiB;AArBrB,QAsBI,sBAAsB;AAtB1B,QAuBI,oBAAoB;AAvBxB,QA2BI,mBAAmB;AAAA,MACjB,cAAc;AAAA,MACd,eAAe;AAAA,IACjB;AA9BJ,QAgCI,kBAAkB;AAAA,MAChB,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,OAAO;AAAA,MACP,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,SAAS;AAAA,IACX;AAGJ,aAAS,SAAS,KAAK,mBAAmB;AACxC,UAAI,OAAO,eAAe,KAAK;AAAE,eAAO;AAAA,MAAK;AAE7C,UAAI,IAAI,IAAI,IAAI;AAChB,QAAE,MAAM,KAAK,iBAAiB;AAC9B,aAAO;AAAA,IACT;AAEA,QAAI,UAAU,QAAQ,SAAS,KAAK,mBAAmB;AACrD,UAAI,GAAG,GAAG,YAAY,KAAK,SACvB,OAAO;AAIX,aAAO,KAAK,KAAK;AAEjB,UAAI,CAAC,qBAAqB,IAAI,MAAM,GAAG,EAAE,WAAW,GAAG;AAErD,YAAI,aAAa,kBAAkB,KAAK,IAAI;AAC5C,YAAI,YAAY;AACd,eAAK,WAAW,WAAW,CAAC;AAC5B,cAAI,WAAW,CAAC,GAAG;AACjB,iBAAK,SAAS,WAAW,CAAC;AAAA,UAC5B;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,UAAI,QAAQ,gBAAgB,KAAK,IAAI;AACrC,UAAI,OAAO;AACT,gBAAQ,MAAM,CAAC;AACf,qBAAa,MAAM,YAAY;AAC/B,aAAK,WAAW;AAChB,eAAO,KAAK,OAAO,MAAM,MAAM;AAAA,MACjC;AAMA,UAAI,qBAAqB,SAAS,KAAK,MAAM,sBAAsB,GAAG;AACpE,kBAAU,KAAK,OAAO,GAAG,CAAC,MAAM;AAChC,YAAI,WAAW,EAAE,SAAS,iBAAiB,KAAK,IAAI;AAClD,iBAAO,KAAK,OAAO,CAAC;AACpB,eAAK,UAAU;AAAA,QACjB;AAAA,MACF;AAEA,UAAI,CAAC,iBAAiB,KAAK,MACtB,WAAY,SAAS,CAAC,gBAAgB,KAAK,IAAK;AAkBnD,YAAI,UAAU;AACd,aAAK,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC3C,gBAAM,KAAK,QAAQ,gBAAgB,CAAC,CAAC;AACrC,cAAI,QAAQ,OAAO,YAAY,MAAM,MAAM,UAAU;AACnD,sBAAU;AAAA,UACZ;AAAA,QACF;AAIA,YAAI,MAAM;AACV,YAAI,YAAY,IAAI;AAElB,mBAAS,KAAK,YAAY,GAAG;AAAA,QAC/B,OAAO;AAGL,mBAAS,KAAK,YAAY,KAAK,OAAO;AAAA,QACxC;AAIA,YAAI,WAAW,IAAI;AACjB,iBAAO,KAAK,MAAM,GAAG,MAAM;AAC3B,iBAAO,KAAK,MAAM,SAAS,CAAC;AAC5B,eAAK,OAAO;AAAA,QACd;AAGA,kBAAU;AACV,aAAK,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AACxC,gBAAM,KAAK,QAAQ,aAAa,CAAC,CAAC;AAClC,cAAI,QAAQ,OAAO,YAAY,MAAM,MAAM,UAAU;AACnD,sBAAU;AAAA,UACZ;AAAA,QACF;AAEA,YAAI,YAAY,IAAI;AAClB,oBAAU,KAAK;AAAA,QACjB;AAEA,YAAI,KAAK,UAAU,CAAC,MAAM,KAAK;AAAE;AAAA,QAAW;AAC5C,YAAI,OAAO,KAAK,MAAM,GAAG,OAAO;AAChC,eAAO,KAAK,MAAM,OAAO;AAGzB,aAAK,UAAU,IAAI;AAInB,aAAK,WAAW,KAAK,YAAY;AAIjC,YAAI,eAAe,KAAK,SAAS,CAAC,MAAM,OACpC,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC,MAAM;AAGhD,YAAI,CAAC,cAAc;AACjB,cAAI,YAAY,KAAK,SAAS,MAAM,IAAI;AACxC,eAAK,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AAC5C,gBAAI,OAAO,UAAU,CAAC;AACtB,gBAAI,CAAC,MAAM;AAAE;AAAA,YAAU;AACvB,gBAAI,CAAC,KAAK,MAAM,mBAAmB,GAAG;AACpC,kBAAI,UAAU;AACd,uBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK;AAC3C,oBAAI,KAAK,WAAW,CAAC,IAAI,KAAK;AAI5B,6BAAW;AAAA,gBACb,OAAO;AACL,6BAAW,KAAK,CAAC;AAAA,gBACnB;AAAA,cACF;AAEA,kBAAI,CAAC,QAAQ,MAAM,mBAAmB,GAAG;AACvC,oBAAI,aAAa,UAAU,MAAM,GAAG,CAAC;AACrC,oBAAI,UAAU,UAAU,MAAM,IAAI,CAAC;AACnC,oBAAI,MAAM,KAAK,MAAM,iBAAiB;AACtC,oBAAI,KAAK;AACP,6BAAW,KAAK,IAAI,CAAC,CAAC;AACtB,0BAAQ,QAAQ,IAAI,CAAC,CAAC;AAAA,gBACxB;AACA,oBAAI,QAAQ,QAAQ;AAClB,yBAAO,QAAQ,KAAK,GAAG,IAAI;AAAA,gBAC7B;AACA,qBAAK,WAAW,WAAW,KAAK,GAAG;AACnC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,YAAI,KAAK,SAAS,SAAS,gBAAgB;AACzC,eAAK,WAAW;AAAA,QAClB;AAIA,YAAI,cAAc;AAChB,eAAK,WAAW,KAAK,SAAS,OAAO,GAAG,KAAK,SAAS,SAAS,CAAC;AAAA,QAClE;AAAA,MACF;AAGA,UAAI,OAAO,KAAK,QAAQ,GAAG;AAC3B,UAAI,SAAS,IAAI;AAEf,aAAK,OAAO,KAAK,OAAO,IAAI;AAC5B,eAAO,KAAK,MAAM,GAAG,IAAI;AAAA,MAC3B;AACA,UAAI,KAAK,KAAK,QAAQ,GAAG;AACzB,UAAI,OAAO,IAAI;AACb,aAAK,SAAS,KAAK,OAAO,EAAE;AAC5B,eAAO,KAAK,MAAM,GAAG,EAAE;AAAA,MACzB;AACA,UAAI,MAAM;AAAE,aAAK,WAAW;AAAA,MAAM;AAClC,UAAI,gBAAgB,UAAU,KAC1B,KAAK,YAAY,CAAC,KAAK,UAAU;AACnC,aAAK,WAAW;AAAA,MAClB;AAEA,aAAO;AAAA,IACT;AAEA,QAAI,UAAU,YAAY,SAAS,MAAM;AACvC,UAAI,OAAO,YAAY,KAAK,IAAI;AAChC,UAAI,MAAM;AACR,eAAO,KAAK,CAAC;AACb,YAAI,SAAS,KAAK;AAChB,eAAK,OAAO,KAAK,OAAO,CAAC;AAAA,QAC3B;AACA,eAAO,KAAK,OAAO,GAAG,KAAK,SAAS,KAAK,MAAM;AAAA,MACjD;AACA,UAAI,MAAM;AAAE,aAAK,WAAW;AAAA,MAAM;AAAA,IACpC;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACvTjB;AAAA;AAAA;AAGA,WAAO,QAAQ,SAAS;AACxB,WAAO,QAAQ,SAAS;AACxB,WAAO,QAAQ,SAAS;AACxB,WAAO,QAAQ,QAAS;AAAA;AAAA;;;ACNxB,IAAAC,iBAAA;AAAA;AAAA,WAAO,UAAQ;AAAA;AAAA;;;ACAf,IAAAC,iBAAA;AAAA;AAAA,WAAO,UAAQ;AAAA;AAAA;;;ACAf,IAAAC,iBAAA;AAAA;AAAA,WAAO,UAAQ;AAAA;AAAA;;;ACAf,IAAAC,iBAAA;AAAA;AAAA,WAAO,UAAQ;AAAA;AAAA;;;ACAf;AAAA;AAAA;AAEA,YAAQ,MAAM;AACd,YAAQ,KAAM;AACd,YAAQ,KAAM;AACd,YAAQ,IAAM;AACd,YAAQ,IAAM;AAAA;AAAA;;;ACNd;AAAA;AAAA;AAKA,aAAS,OAAO,KAAK;AAAE,aAAO,OAAO,UAAU,SAAS,KAAK,GAAG;AAAA,IAAG;AAEnE,aAAS,SAAS,KAAK;AAAE,aAAO,OAAO,GAAG,MAAM;AAAA,IAAmB;AAEnE,QAAI,kBAAkB,OAAO,UAAU;AAEvC,aAAS,IAAI,QAAQ,KAAK;AACxB,aAAO,gBAAgB,KAAK,QAAQ,GAAG;AAAA,IACzC;AAIA,aAAS,OAAO,KAAkC;AAChD,UAAI,UAAU,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AAErD,cAAQ,QAAQ,SAAU,QAAQ;AAChC,YAAI,CAAC,QAAQ;AAAE;AAAA,QAAQ;AAEvB,YAAI,OAAO,WAAW,UAAU;AAC9B,gBAAM,IAAI,UAAU,SAAS,gBAAgB;AAAA,QAC/C;AAEA,eAAO,KAAK,MAAM,EAAE,QAAQ,SAAU,KAAK;AACzC,cAAI,GAAG,IAAI,OAAO,GAAG;AAAA,QACvB,CAAC;AAAA,MACH,CAAC;AAED,aAAO;AAAA,IACT;AAIA,aAAS,eAAe,KAAK,KAAK,aAAa;AAC7C,aAAO,CAAC,EAAE,OAAO,IAAI,MAAM,GAAG,GAAG,GAAG,aAAa,IAAI,MAAM,MAAM,CAAC,CAAC;AAAA,IACrE;AAIA,aAAS,kBAAkB,GAAG;AAG5B,UAAI,KAAK,SAAU,KAAK,OAAQ;AAAE,eAAO;AAAA,MAAO;AAEhD,UAAI,KAAK,SAAU,KAAK,OAAQ;AAAE,eAAO;AAAA,MAAO;AAChD,WAAK,IAAI,WAAY,UAAW,IAAI,WAAY,OAAQ;AAAE,eAAO;AAAA,MAAO;AAExE,UAAI,KAAK,KAAQ,KAAK,GAAM;AAAE,eAAO;AAAA,MAAO;AAC5C,UAAI,MAAM,IAAM;AAAE,eAAO;AAAA,MAAO;AAChC,UAAI,KAAK,MAAQ,KAAK,IAAM;AAAE,eAAO;AAAA,MAAO;AAC5C,UAAI,KAAK,OAAQ,KAAK,KAAM;AAAE,eAAO;AAAA,MAAO;AAE5C,UAAI,IAAI,SAAU;AAAE,eAAO;AAAA,MAAO;AAClC,aAAO;AAAA,IACT;AAEA,aAAS,cAAc,GAAG;AAExB,UAAI,IAAI,OAAQ;AACd,aAAK;AACL,YAAI,aAAa,SAAU,KAAK,KAC5B,aAAa,SAAU,IAAI;AAE/B,eAAO,OAAO,aAAa,YAAY,UAAU;AAAA,MACnD;AACA,aAAO,OAAO,aAAa,CAAC;AAAA,IAC9B;AAGA,QAAI,iBAAkB;AACtB,QAAI,YAAkB;AACtB,QAAI,kBAAkB,IAAI,OAAO,eAAe,SAAS,MAAM,UAAU,QAAQ,IAAI;AAErF,QAAI,yBAAyB;AAE7B,QAAI,WAAW;AAEf,aAAS,qBAAqB,OAAO,MAAM;AACzC,UAAI,OAAO;AAEX,UAAI,IAAI,UAAU,IAAI,GAAG;AACvB,eAAO,SAAS,IAAI;AAAA,MACtB;AAEA,UAAI,KAAK,WAAW,CAAC,MAAM,MAAe,uBAAuB,KAAK,IAAI,GAAG;AAC3E,eAAO,KAAK,CAAC,EAAE,YAAY,MAAM,MAC/B,SAAS,KAAK,MAAM,CAAC,GAAG,EAAE,IAAI,SAAS,KAAK,MAAM,CAAC,GAAG,EAAE;AAE1D,YAAI,kBAAkB,IAAI,GAAG;AAC3B,iBAAO,cAAc,IAAI;AAAA,QAC3B;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAQA,aAAS,WAAW,KAAK;AACvB,UAAI,IAAI,QAAQ,IAAI,IAAI,GAAG;AAAE,eAAO;AAAA,MAAK;AACzC,aAAO,IAAI,QAAQ,gBAAgB,IAAI;AAAA,IACzC;AAEA,aAAS,YAAY,KAAK;AACxB,UAAI,IAAI,QAAQ,IAAI,IAAI,KAAK,IAAI,QAAQ,GAAG,IAAI,GAAG;AAAE,eAAO;AAAA,MAAK;AAEjE,aAAO,IAAI,QAAQ,iBAAiB,SAAU,OAAO,SAAS,QAAQ;AACpE,YAAI,SAAS;AAAE,iBAAO;AAAA,QAAS;AAC/B,eAAO,qBAAqB,OAAO,MAAM;AAAA,MAC3C,CAAC;AAAA,IACH;AAIA,QAAI,sBAAsB;AAC1B,QAAI,yBAAyB;AAC7B,QAAI,oBAAoB;AAAA,MACtB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAEA,aAAS,kBAAkB,IAAI;AAC7B,aAAO,kBAAkB,EAAE;AAAA,IAC7B;AAEA,aAAS,WAAW,KAAK;AACvB,UAAI,oBAAoB,KAAK,GAAG,GAAG;AACjC,eAAO,IAAI,QAAQ,wBAAwB,iBAAiB;AAAA,MAC9D;AACA,aAAO;AAAA,IACT;AAIA,QAAI,mBAAmB;AAEvB,aAAS,SAAS,KAAK;AACrB,aAAO,IAAI,QAAQ,kBAAkB,MAAM;AAAA,IAC7C;AAIA,aAAS,QAAQ,MAAM;AACrB,cAAQ,MAAM;AAAA,QACZ,KAAK;AAAA,QACL,KAAK;AACH,iBAAO;AAAA,MACX;AACA,aAAO;AAAA,IACT;AAGA,aAAS,aAAa,MAAM;AAC1B,UAAI,QAAQ,QAAU,QAAQ,MAAQ;AAAE,eAAO;AAAA,MAAM;AACrD,cAAQ,MAAM;AAAA,QACZ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO;AAAA,MACX;AACA,aAAO;AAAA,IACT;AAKA,QAAI,mBAAmB;AAGvB,aAAS,YAAY,IAAI;AACvB,aAAO,iBAAiB,KAAK,EAAE;AAAA,IACjC;AAUA,aAAS,eAAe,IAAI;AAC1B,cAAQ,IAAI;AAAA,QACV,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AAIA,aAAS,mBAAmB,KAAK;AAG/B,YAAM,IAAI,KAAK,EAAE,QAAQ,QAAQ,GAAG;AAQpC,UAAI,IAAI,YAAY,MAAM,KAAK;AAC7B,cAAM,IAAI,QAAQ,MAAM,GAAG;AAAA,MAC7B;AAkCA,aAAO,IAAI,YAAY,EAAE,YAAY;AAAA,IACvC;AAQA,YAAQ,MAAsB,CAAC;AAC/B,YAAQ,IAAI,QAAkB;AAC9B,YAAQ,IAAI,UAAkB;AAE9B,YAAQ,SAAsB;AAC9B,YAAQ,WAAsB;AAC9B,YAAQ,MAAsB;AAC9B,YAAQ,aAAsB;AAC9B,YAAQ,cAAsB;AAC9B,YAAQ,oBAAsB;AAC9B,YAAQ,gBAAsB;AAE9B,YAAQ,aAAsB;AAC9B,YAAQ,iBAAsB;AAC9B,YAAQ,UAAsB;AAC9B,YAAQ,eAAsB;AAC9B,YAAQ,iBAAsB;AAC9B,YAAQ,cAAsB;AAC9B,YAAQ,WAAsB;AAC9B,YAAQ,qBAAsB;AAAA;AAAA;;;AC5T9B;AAAA;AAAA;AAOA,WAAO,UAAU,SAAS,eAAe,OAAO,OAAO,eAAe;AACpE,UAAI,OAAO,OAAO,QAAQ,SACtB,WAAW,IACX,MAAM,MAAM,QACZ,SAAS,MAAM;AAEnB,YAAM,MAAM,QAAQ;AACpB,cAAQ;AAER,aAAO,MAAM,MAAM,KAAK;AACtB,iBAAS,MAAM,IAAI,WAAW,MAAM,GAAG;AACvC,YAAI,WAAW,IAAc;AAC3B;AACA,cAAI,UAAU,GAAG;AACf,oBAAQ;AACR;AAAA,UACF;AAAA,QACF;AAEA,kBAAU,MAAM;AAChB,cAAM,GAAG,OAAO,UAAU,KAAK;AAC/B,YAAI,WAAW,IAAc;AAC3B,cAAI,YAAY,MAAM,MAAM,GAAG;AAE7B;AAAA,UACF,WAAW,eAAe;AACxB,kBAAM,MAAM;AACZ,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAEA,UAAI,OAAO;AACT,mBAAW,MAAM;AAAA,MACnB;AAGA,YAAM,MAAM;AAEZ,aAAO;AAAA,IACT;AAAA;AAAA;;;AC/CA;AAAA;AAAA;AAKA,QAAI,cAAc,gBAA2B;AAG7C,WAAO,UAAU,SAAS,qBAAqB,KAAK,KAAK,KAAK;AAC5D,UAAI,MAAM,OACN,QAAQ,GACR,QAAQ,KACR,SAAS;AAAA,QACP,IAAI;AAAA,QACJ,KAAK;AAAA,QACL,OAAO;AAAA,QACP,KAAK;AAAA,MACP;AAEJ,UAAI,IAAI,WAAW,GAAG,MAAM,IAAc;AACxC;AACA,eAAO,MAAM,KAAK;AAChB,iBAAO,IAAI,WAAW,GAAG;AACzB,cAAI,SAAS,IAAe;AAAE,mBAAO;AAAA,UAAQ;AAC7C,cAAI,SAAS,IAAc;AAAE,mBAAO;AAAA,UAAQ;AAC5C,cAAI,SAAS,IAAc;AACzB,mBAAO,MAAM,MAAM;AACnB,mBAAO,MAAM,YAAY,IAAI,MAAM,QAAQ,GAAG,GAAG,CAAC;AAClD,mBAAO,KAAK;AACZ,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,MAAgB,MAAM,IAAI,KAAK;AAC1C,mBAAO;AACP;AAAA,UACF;AAEA;AAAA,QACF;AAGA,eAAO;AAAA,MACT;AAIA,cAAQ;AACR,aAAO,MAAM,KAAK;AAChB,eAAO,IAAI,WAAW,GAAG;AAEzB,YAAI,SAAS,IAAM;AAAE;AAAA,QAAO;AAG5B,YAAI,OAAO,MAAQ,SAAS,KAAM;AAAE;AAAA,QAAO;AAE3C,YAAI,SAAS,MAAgB,MAAM,IAAI,KAAK;AAC1C,cAAI,IAAI,WAAW,MAAM,CAAC,MAAM,IAAM;AAAE;AAAA,UAAO;AAC/C,iBAAO;AACP;AAAA,QACF;AAEA,YAAI,SAAS,IAAc;AACzB;AACA,cAAI,QAAQ,IAAI;AAAE,mBAAO;AAAA,UAAQ;AAAA,QACnC;AAEA,YAAI,SAAS,IAAc;AACzB,cAAI,UAAU,GAAG;AAAE;AAAA,UAAO;AAC1B;AAAA,QACF;AAEA;AAAA,MACF;AAEA,UAAI,UAAU,KAAK;AAAE,eAAO;AAAA,MAAQ;AACpC,UAAI,UAAU,GAAG;AAAE,eAAO;AAAA,MAAQ;AAElC,aAAO,MAAM,YAAY,IAAI,MAAM,OAAO,GAAG,CAAC;AAC9C,aAAO,QAAQ;AACf,aAAO,MAAM;AACb,aAAO,KAAK;AACZ,aAAO;AAAA,IACT;AAAA;AAAA;;;ACjFA;AAAA;AAAA;AAKA,QAAI,cAAc,gBAA2B;AAG7C,WAAO,UAAU,SAAS,eAAe,KAAK,KAAK,KAAK;AACtD,UAAI,MACA,QACA,QAAQ,GACR,QAAQ,KACR,SAAS;AAAA,QACP,IAAI;AAAA,QACJ,KAAK;AAAA,QACL,OAAO;AAAA,QACP,KAAK;AAAA,MACP;AAEJ,UAAI,OAAO,KAAK;AAAE,eAAO;AAAA,MAAQ;AAEjC,eAAS,IAAI,WAAW,GAAG;AAE3B,UAAI,WAAW,MAAgB,WAAW,MAAgB,WAAW,IAAc;AAAE,eAAO;AAAA,MAAQ;AAEpG;AAGA,UAAI,WAAW,IAAM;AAAE,iBAAS;AAAA,MAAM;AAEtC,aAAO,MAAM,KAAK;AAChB,eAAO,IAAI,WAAW,GAAG;AACzB,YAAI,SAAS,QAAQ;AACnB,iBAAO,MAAM,MAAM;AACnB,iBAAO,QAAQ;AACf,iBAAO,MAAM,YAAY,IAAI,MAAM,QAAQ,GAAG,GAAG,CAAC;AAClD,iBAAO,KAAK;AACZ,iBAAO;AAAA,QACT,WAAW,SAAS,MAAgB,WAAW,IAAc;AAC3D,iBAAO;AAAA,QACT,WAAW,SAAS,IAAM;AACxB;AAAA,QACF,WAAW,SAAS,MAAgB,MAAM,IAAI,KAAK;AACjD;AACA,cAAI,IAAI,WAAW,GAAG,MAAM,IAAM;AAChC;AAAA,UACF;AAAA,QACF;AAEA;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACtDA;AAAA;AAAA;AAIA,YAAQ,iBAAuB;AAC/B,YAAQ,uBAAuB;AAC/B,YAAQ,iBAAuB;AAAA;AAAA;;;ACN/B;AAAA;AAAA;AAUA,QAAI,SAAkB,gBAA0B;AAChD,QAAI,cAAkB,gBAA0B;AAChD,QAAI,aAAkB,gBAA0B;AAKhD,QAAI,gBAAgB,CAAC;AAGrB,kBAAc,cAAc,SAAU,QAAQ,KAAK,SAAS,KAAK,KAAK;AACpE,UAAI,QAAQ,OAAO,GAAG;AAEtB,aAAQ,UAAU,IAAI,YAAY,KAAK,IAAI,MACnC,WAAW,OAAO,GAAG,EAAE,OAAO,IAC9B;AAAA,IACV;AAGA,kBAAc,aAAa,SAAU,QAAQ,KAAK,SAAS,KAAK,KAAK;AACnE,UAAI,QAAQ,OAAO,GAAG;AAEtB,aAAQ,SAAS,IAAI,YAAY,KAAK,IAAI,YAClC,WAAW,OAAO,GAAG,EAAE,OAAO,IAC9B;AAAA,IACV;AAGA,kBAAc,QAAQ,SAAU,QAAQ,KAAK,SAAS,KAAK,KAAK;AAC9D,UAAI,QAAQ,OAAO,GAAG,GAClB,OAAO,MAAM,OAAO,YAAY,MAAM,IAAI,EAAE,KAAK,IAAI,IACrD,WAAW,IACX,YAAY,IACZ,aAAa,GAAG,KAAK,UAAU;AAEnC,UAAI,MAAM;AACR,cAAM,KAAK,MAAM,QAAQ;AACzB,mBAAW,IAAI,CAAC;AAChB,oBAAY,IAAI,MAAM,CAAC,EAAE,KAAK,EAAE;AAAA,MAClC;AAEA,UAAI,QAAQ,WAAW;AACrB,sBAAc,QAAQ,UAAU,MAAM,SAAS,UAAU,SAAS,KAAK,WAAW,MAAM,OAAO;AAAA,MACjG,OAAO;AACL,sBAAc,WAAW,MAAM,OAAO;AAAA,MACxC;AAEA,UAAI,YAAY,QAAQ,MAAM,MAAM,GAAG;AACrC,eAAO,cAAc;AAAA,MACvB;AAKA,UAAI,MAAM;AACR,YAAW,MAAM,UAAU,OAAO;AAClC,mBAAW,MAAM,QAAQ,MAAM,MAAM,MAAM,IAAI,CAAC;AAEhD,YAAI,IAAI,GAAG;AACT,mBAAS,KAAK,CAAE,SAAS,QAAQ,aAAa,QAAS,CAAC;AAAA,QAC1D,OAAO;AACL,mBAAS,CAAC,IAAI,SAAS,CAAC,EAAE,MAAM;AAChC,mBAAS,CAAC,EAAE,CAAC,KAAK,MAAM,QAAQ,aAAa;AAAA,QAC/C;AAGA,mBAAW;AAAA,UACT,OAAO;AAAA,QACT;AAEA,eAAQ,eAAe,IAAI,YAAY,QAAQ,IAAI,MAC3C,cACA;AAAA,MACV;AAGA,aAAQ,eAAe,IAAI,YAAY,KAAK,IAAI,MACxC,cACA;AAAA,IACV;AAGA,kBAAc,QAAQ,SAAU,QAAQ,KAAK,SAAS,KAAK,KAAK;AAC9D,UAAI,QAAQ,OAAO,GAAG;AAOtB,YAAM,MAAM,MAAM,UAAU,KAAK,CAAC,EAAE,CAAC,IACnC,IAAI,mBAAmB,MAAM,UAAU,SAAS,GAAG;AAErD,aAAO,IAAI,YAAY,QAAQ,KAAK,OAAO;AAAA,IAC7C;AAGA,kBAAc,YAAY,SAAU,QAAQ,KAAK,SAAoB;AACnE,aAAO,QAAQ,WAAW,aAAa;AAAA,IACzC;AACA,kBAAc,YAAY,SAAU,QAAQ,KAAK,SAAoB;AACnE,aAAO,QAAQ,SAAU,QAAQ,WAAW,aAAa,WAAY;AAAA,IACvE;AAGA,kBAAc,OAAO,SAAU,QAAQ,KAAyB;AAC9D,aAAO,WAAW,OAAO,GAAG,EAAE,OAAO;AAAA,IACvC;AAGA,kBAAc,aAAa,SAAU,QAAQ,KAAyB;AACpE,aAAO,OAAO,GAAG,EAAE;AAAA,IACrB;AACA,kBAAc,cAAc,SAAU,QAAQ,KAAyB;AACrE,aAAO,OAAO,GAAG,EAAE;AAAA,IACrB;AAQA,aAAS,WAAW;AA8BlB,WAAK,QAAQ,OAAO,CAAC,GAAG,aAAa;AAAA,IACvC;AAQA,aAAS,UAAU,cAAc,SAAS,YAAY,OAAO;AAC3D,UAAI,GAAG,GAAG;AAEV,UAAI,CAAC,MAAM,OAAO;AAAE,eAAO;AAAA,MAAI;AAE/B,eAAS;AAET,WAAK,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC9C,kBAAU,MAAM,WAAW,MAAM,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,OAAO,WAAW,MAAM,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI;AAAA,MACzF;AAEA,aAAO;AAAA,IACT;AAYA,aAAS,UAAU,cAAc,SAAS,YAAY,QAAQ,KAAK,SAAS;AAC1E,UAAI,WACA,SAAS,IACT,SAAS,OACT,QAAQ,OAAO,GAAG;AAGtB,UAAI,MAAM,QAAQ;AAChB,eAAO;AAAA,MACT;AASA,UAAI,MAAM,SAAS,MAAM,YAAY,MAAM,OAAO,OAAO,MAAM,CAAC,EAAE,QAAQ;AACxE,kBAAU;AAAA,MACZ;AAGA,iBAAW,MAAM,YAAY,KAAK,OAAO,OAAO,MAAM;AAGtD,gBAAU,KAAK,YAAY,KAAK;AAGhC,UAAI,MAAM,YAAY,KAAK,QAAQ,UAAU;AAC3C,kBAAU;AAAA,MACZ;AAGA,UAAI,MAAM,OAAO;AACf,iBAAS;AAET,YAAI,MAAM,YAAY,GAAG;AACvB,cAAI,MAAM,IAAI,OAAO,QAAQ;AAC3B,wBAAY,OAAO,MAAM,CAAC;AAE1B,gBAAI,UAAU,SAAS,YAAY,UAAU,QAAQ;AAGnD,uBAAS;AAAA,YAEX,WAAW,UAAU,YAAY,MAAM,UAAU,QAAQ,MAAM,KAAK;AAGlE,uBAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,gBAAU,SAAS,QAAQ;AAE3B,aAAO;AAAA,IACT;AAWA,aAAS,UAAU,eAAe,SAAU,QAAQ,SAAS,KAAK;AAChE,UAAI,MACA,SAAS,IACT,QAAQ,KAAK;AAEjB,eAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AACjD,eAAO,OAAO,CAAC,EAAE;AAEjB,YAAI,OAAO,MAAM,IAAI,MAAM,aAAa;AACtC,oBAAU,MAAM,IAAI,EAAE,QAAQ,GAAG,SAAS,KAAK,IAAI;AAAA,QACrD,OAAO;AACL,oBAAU,KAAK,YAAY,QAAQ,GAAG,OAAO;AAAA,QAC/C;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAaA,aAAS,UAAU,qBAAqB,SAAU,QAAQ,SAAS,KAAK;AACtE,UAAI,SAAS;AAEb,eAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AACjD,YAAI,OAAO,CAAC,EAAE,SAAS,QAAQ;AAC7B,oBAAU,OAAO,CAAC,EAAE;AAAA,QACtB,WAAW,OAAO,CAAC,EAAE,SAAS,SAAS;AACrC,oBAAU,KAAK,mBAAmB,OAAO,CAAC,EAAE,UAAU,SAAS,GAAG;AAAA,QACpE,WAAW,OAAO,CAAC,EAAE,SAAS,aAAa;AACzC,oBAAU;AAAA,QACZ;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAYA,aAAS,UAAU,SAAS,SAAU,QAAQ,SAAS,KAAK;AAC1D,UAAI,GAAG,KAAK,MACR,SAAS,IACT,QAAQ,KAAK;AAEjB,WAAK,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AAC7C,eAAO,OAAO,CAAC,EAAE;AAEjB,YAAI,SAAS,UAAU;AACrB,oBAAU,KAAK,aAAa,OAAO,CAAC,EAAE,UAAU,SAAS,GAAG;AAAA,QAC9D,WAAW,OAAO,MAAM,IAAI,MAAM,aAAa;AAC7C,oBAAU,MAAM,OAAO,CAAC,EAAE,IAAI,EAAE,QAAQ,GAAG,SAAS,KAAK,IAAI;AAAA,QAC/D,OAAO;AACL,oBAAU,KAAK,YAAY,QAAQ,GAAG,SAAS,GAAG;AAAA,QACpD;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpVjB;AAAA;AAAA;AAuBA,aAAS,QAAQ;AAUf,WAAK,YAAY,CAAC;AAOlB,WAAK,YAAY;AAAA,IACnB;AAQA,UAAM,UAAU,WAAW,SAAU,MAAM;AACzC,eAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,YAAI,KAAK,UAAU,CAAC,EAAE,SAAS,MAAM;AACnC,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAKA,UAAM,UAAU,cAAc,WAAY;AACxC,UAAI,OAAO;AACX,UAAI,SAAS,CAAE,EAAG;AAGlB,WAAK,UAAU,QAAQ,SAAU,MAAM;AACrC,YAAI,CAAC,KAAK,SAAS;AAAE;AAAA,QAAQ;AAE7B,aAAK,IAAI,QAAQ,SAAU,SAAS;AAClC,cAAI,OAAO,QAAQ,OAAO,IAAI,GAAG;AAC/B,mBAAO,KAAK,OAAO;AAAA,UACrB;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAED,WAAK,YAAY,CAAC;AAElB,aAAO,QAAQ,SAAU,OAAO;AAC9B,aAAK,UAAU,KAAK,IAAI,CAAC;AACzB,aAAK,UAAU,QAAQ,SAAU,MAAM;AACrC,cAAI,CAAC,KAAK,SAAS;AAAE;AAAA,UAAQ;AAE7B,cAAI,SAAS,KAAK,IAAI,QAAQ,KAAK,IAAI,GAAG;AAAE;AAAA,UAAQ;AAEpD,eAAK,UAAU,KAAK,EAAE,KAAK,KAAK,EAAE;AAAA,QACpC,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AA4BA,UAAM,UAAU,KAAK,SAAU,MAAM,IAAI,SAAS;AAChD,UAAI,QAAQ,KAAK,SAAS,IAAI;AAC9B,UAAI,MAAM,WAAW,CAAC;AAEtB,UAAI,UAAU,IAAI;AAAE,cAAM,IAAI,MAAM,4BAA4B,IAAI;AAAA,MAAG;AAEvE,WAAK,UAAU,KAAK,EAAE,KAAK;AAC3B,WAAK,UAAU,KAAK,EAAE,MAAM,IAAI,OAAO,CAAC;AACxC,WAAK,YAAY;AAAA,IACnB;AA2BA,UAAM,UAAU,SAAS,SAAU,YAAY,UAAU,IAAI,SAAS;AACpE,UAAI,QAAQ,KAAK,SAAS,UAAU;AACpC,UAAI,MAAM,WAAW,CAAC;AAEtB,UAAI,UAAU,IAAI;AAAE,cAAM,IAAI,MAAM,4BAA4B,UAAU;AAAA,MAAG;AAE7E,WAAK,UAAU,OAAO,OAAO,GAAG;AAAA,QAC9B,MAAM;AAAA,QACN,SAAS;AAAA,QACT;AAAA,QACA,KAAK,IAAI,OAAO,CAAC;AAAA,MACnB,CAAC;AAED,WAAK,YAAY;AAAA,IACnB;AA2BA,UAAM,UAAU,QAAQ,SAAU,WAAW,UAAU,IAAI,SAAS;AAClE,UAAI,QAAQ,KAAK,SAAS,SAAS;AACnC,UAAI,MAAM,WAAW,CAAC;AAEtB,UAAI,UAAU,IAAI;AAAE,cAAM,IAAI,MAAM,4BAA4B,SAAS;AAAA,MAAG;AAE5E,WAAK,UAAU,OAAO,QAAQ,GAAG,GAAG;AAAA,QAClC,MAAM;AAAA,QACN,SAAS;AAAA,QACT;AAAA,QACA,KAAK,IAAI,OAAO,CAAC;AAAA,MACnB,CAAC;AAED,WAAK,YAAY;AAAA,IACnB;AAyBA,UAAM,UAAU,OAAO,SAAU,UAAU,IAAI,SAAS;AACtD,UAAI,MAAM,WAAW,CAAC;AAEtB,WAAK,UAAU,KAAK;AAAA,QAClB,MAAM;AAAA,QACN,SAAS;AAAA,QACT;AAAA,QACA,KAAK,IAAI,OAAO,CAAC;AAAA,MACnB,CAAC;AAED,WAAK,YAAY;AAAA,IACnB;AAeA,UAAM,UAAU,SAAS,SAAU,MAAM,eAAe;AACtD,UAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AAAE,eAAO,CAAE,IAAK;AAAA,MAAG;AAE7C,UAAI,SAAS,CAAC;AAGd,WAAK,QAAQ,SAAU,MAAM;AAC3B,YAAI,MAAM,KAAK,SAAS,IAAI;AAE5B,YAAI,MAAM,GAAG;AACX,cAAI,eAAe;AAAE;AAAA,UAAQ;AAC7B,gBAAM,IAAI,MAAM,sCAAsC,IAAI;AAAA,QAC5D;AACA,aAAK,UAAU,GAAG,EAAE,UAAU;AAC9B,eAAO,KAAK,IAAI;AAAA,MAClB,GAAG,IAAI;AAEP,WAAK,YAAY;AACjB,aAAO;AAAA,IACT;AAaA,UAAM,UAAU,aAAa,SAAU,MAAM,eAAe;AAC1D,UAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AAAE,eAAO,CAAE,IAAK;AAAA,MAAG;AAE7C,WAAK,UAAU,QAAQ,SAAU,MAAM;AAAE,aAAK,UAAU;AAAA,MAAO,CAAC;AAEhE,WAAK,OAAO,MAAM,aAAa;AAAA,IACjC;AAeA,UAAM,UAAU,UAAU,SAAU,MAAM,eAAe;AACvD,UAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AAAE,eAAO,CAAE,IAAK;AAAA,MAAG;AAE7C,UAAI,SAAS,CAAC;AAGd,WAAK,QAAQ,SAAU,MAAM;AAC3B,YAAI,MAAM,KAAK,SAAS,IAAI;AAE5B,YAAI,MAAM,GAAG;AACX,cAAI,eAAe;AAAE;AAAA,UAAQ;AAC7B,gBAAM,IAAI,MAAM,sCAAsC,IAAI;AAAA,QAC5D;AACA,aAAK,UAAU,GAAG,EAAE,UAAU;AAC9B,eAAO,KAAK,IAAI;AAAA,MAClB,GAAG,IAAI;AAEP,WAAK,YAAY;AACjB,aAAO;AAAA,IACT;AAYA,UAAM,UAAU,WAAW,SAAU,WAAW;AAC9C,UAAI,KAAK,cAAc,MAAM;AAC3B,aAAK,YAAY;AAAA,MACnB;AAGA,aAAO,KAAK,UAAU,SAAS,KAAK,CAAC;AAAA,IACvC;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC/VjB;AAAA;AAAA;AAMA,QAAI,cAAe;AACnB,QAAI,UAAe;AAGnB,WAAO,UAAU,SAAS,UAAU,OAAO;AACzC,UAAI;AAGJ,YAAM,MAAM,IAAI,QAAQ,aAAa,IAAI;AAGzC,YAAM,IAAI,QAAQ,SAAS,GAAQ;AAEnC,YAAM,MAAM;AAAA,IACd;AAAA;AAAA;;;ACpBA;AAAA;AAAA;AAGA,WAAO,UAAU,SAAS,MAAM,OAAO;AACrC,UAAI;AAEJ,UAAI,MAAM,YAAY;AACpB,gBAAiB,IAAI,MAAM,MAAM,UAAU,IAAI,CAAC;AAChD,cAAM,UAAW,MAAM;AACvB,cAAM,MAAW,CAAE,GAAG,CAAE;AACxB,cAAM,WAAW,CAAC;AAClB,cAAM,OAAO,KAAK,KAAK;AAAA,MACzB,OAAO;AACL,cAAM,GAAG,MAAM,MAAM,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK,MAAM,MAAM;AAAA,MACnE;AAAA,IACF;AAAA;AAAA;;;ACfA;AAAA;AAAA;AAEA,WAAO,UAAU,SAAS,OAAO,OAAO;AACtC,UAAI,SAAS,MAAM,QAAQ,KAAK,GAAG;AAGnC,WAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AACzC,cAAM,OAAO,CAAC;AACd,YAAI,IAAI,SAAS,UAAU;AACzB,gBAAM,GAAG,OAAO,MAAM,IAAI,SAAS,MAAM,IAAI,MAAM,KAAK,IAAI,QAAQ;AAAA,QACtE;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;ACZA;AAAA;AAAA;AAOA,QAAI,iBAAiB,gBAA2B;AAGhD,aAAS,WAAW,KAAK;AACvB,aAAO,YAAY,KAAK,GAAG;AAAA,IAC7B;AACA,aAAS,YAAY,KAAK;AACxB,aAAO,aAAa,KAAK,GAAG;AAAA,IAC9B;AAGA,WAAO,UAAU,SAAS,QAAQ,OAAO;AACvC,UAAI,GAAG,GAAG,GAAG,QAAQ,OAAO,cAAc,OAAO,IAAI,MAAM,KAAK,SAC5D,OAAO,eAAe,KAAK,SAAS,SACpC,cAAc,MAAM,QACpB;AAEJ,UAAI,CAAC,MAAM,GAAG,QAAQ,SAAS;AAAE;AAAA,MAAQ;AAEzC,WAAK,IAAI,GAAG,IAAI,YAAY,QAAQ,IAAI,GAAG,KAAK;AAC9C,YAAI,YAAY,CAAC,EAAE,SAAS,YACxB,CAAC,MAAM,GAAG,QAAQ,QAAQ,YAAY,CAAC,EAAE,OAAO,GAAG;AACrD;AAAA,QACF;AAEA,iBAAS,YAAY,CAAC,EAAE;AAExB,wBAAgB;AAIhB,aAAK,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AACvC,yBAAe,OAAO,CAAC;AAGvB,cAAI,aAAa,SAAS,cAAc;AACtC;AACA,mBAAO,OAAO,CAAC,EAAE,UAAU,aAAa,SAAS,OAAO,CAAC,EAAE,SAAS,aAAa;AAC/E;AAAA,YACF;AACA;AAAA,UACF;AAGA,cAAI,aAAa,SAAS,eAAe;AACvC,gBAAI,WAAW,aAAa,OAAO,KAAK,gBAAgB,GAAG;AACzD;AAAA,YACF;AACA,gBAAI,YAAY,aAAa,OAAO,GAAG;AACrC;AAAA,YACF;AAAA,UACF;AACA,cAAI,gBAAgB,GAAG;AAAE;AAAA,UAAU;AAEnC,cAAI,aAAa,SAAS,UAAU,MAAM,GAAG,QAAQ,KAAK,aAAa,OAAO,GAAG;AAE/E,mBAAO,aAAa;AACpB,oBAAQ,MAAM,GAAG,QAAQ,MAAM,IAAI;AAGnC,oBAAQ,CAAC;AACT,oBAAQ,aAAa;AACrB,sBAAU;AAEV,iBAAK,KAAK,GAAG,KAAK,MAAM,QAAQ,MAAM;AAEpC,oBAAM,MAAM,EAAE,EAAE;AAChB,wBAAU,MAAM,GAAG,cAAc,GAAG;AACpC,kBAAI,CAAC,MAAM,GAAG,aAAa,OAAO,GAAG;AAAE;AAAA,cAAU;AAEjD,wBAAU,MAAM,EAAE,EAAE;AAMpB,kBAAI,CAAC,MAAM,EAAE,EAAE,QAAQ;AACrB,0BAAU,MAAM,GAAG,kBAAkB,YAAY,OAAO,EAAE,QAAQ,cAAc,EAAE;AAAA,cACpF,WAAW,MAAM,EAAE,EAAE,WAAW,aAAa,CAAC,YAAY,KAAK,OAAO,GAAG;AACvE,0BAAU,MAAM,GAAG,kBAAkB,YAAY,OAAO,EAAE,QAAQ,YAAY,EAAE;AAAA,cAClF,OAAO;AACL,0BAAU,MAAM,GAAG,kBAAkB,OAAO;AAAA,cAC9C;AAEA,oBAAM,MAAM,EAAE,EAAE;AAEhB,kBAAI,MAAM,SAAS;AACjB,wBAAgB,IAAI,MAAM,MAAM,QAAQ,IAAI,CAAC;AAC7C,sBAAM,UAAU,KAAK,MAAM,SAAS,GAAG;AACvC,sBAAM,QAAU;AAChB,sBAAM,KAAK,KAAK;AAAA,cAClB;AAEA,sBAAgB,IAAI,MAAM,MAAM,aAAa,KAAK,CAAC;AACnD,oBAAM,QAAU,CAAE,CAAE,QAAQ,OAAQ,CAAE;AACtC,oBAAM,QAAU;AAChB,oBAAM,SAAU;AAChB,oBAAM,OAAU;AAChB,oBAAM,KAAK,KAAK;AAEhB,sBAAgB,IAAI,MAAM,MAAM,QAAQ,IAAI,CAAC;AAC7C,oBAAM,UAAU;AAChB,oBAAM,QAAU;AAChB,oBAAM,KAAK,KAAK;AAEhB,sBAAgB,IAAI,MAAM,MAAM,cAAc,KAAK,EAAE;AACrD,oBAAM,QAAU,EAAE;AAClB,oBAAM,SAAU;AAChB,oBAAM,OAAU;AAChB,oBAAM,KAAK,KAAK;AAEhB,wBAAU,MAAM,EAAE,EAAE;AAAA,YACtB;AACA,gBAAI,UAAU,KAAK,QAAQ;AACzB,sBAAgB,IAAI,MAAM,MAAM,QAAQ,IAAI,CAAC;AAC7C,oBAAM,UAAU,KAAK,MAAM,OAAO;AAClC,oBAAM,QAAU;AAChB,oBAAM,KAAK,KAAK;AAAA,YAClB;AAGA,wBAAY,CAAC,EAAE,WAAW,SAAS,eAAe,QAAQ,GAAG,KAAK;AAAA,UACpE;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;ACpIA;AAAA;AAAA;AAiBA,QAAI,UAAU;AAId,QAAI,sBAAsB;AAE1B,QAAI,iBAAiB;AACrB,QAAI,cAAc;AAAA,MAChB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,IAAI;AAAA,IACN;AAEA,aAAS,UAAU,OAAO,MAAM;AAC9B,aAAO,YAAY,KAAK,YAAY,CAAC;AAAA,IACvC;AAEA,aAAS,eAAe,cAAc;AACpC,UAAI,GAAG,OAAO,kBAAkB;AAEhC,WAAK,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,gBAAQ,aAAa,CAAC;AAEtB,YAAI,MAAM,SAAS,UAAU,CAAC,iBAAiB;AAC7C,gBAAM,UAAU,MAAM,QAAQ,QAAQ,gBAAgB,SAAS;AAAA,QACjE;AAEA,YAAI,MAAM,SAAS,eAAe,MAAM,SAAS,QAAQ;AACvD;AAAA,QACF;AAEA,YAAI,MAAM,SAAS,gBAAgB,MAAM,SAAS,QAAQ;AACxD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,aAAS,aAAa,cAAc;AAClC,UAAI,GAAG,OAAO,kBAAkB;AAEhC,WAAK,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,gBAAQ,aAAa,CAAC;AAEtB,YAAI,MAAM,SAAS,UAAU,CAAC,iBAAiB;AAC7C,cAAI,QAAQ,KAAK,MAAM,OAAO,GAAG;AAC/B,kBAAM,UAAU,MAAM,QACnB,QAAQ,QAAQ,GAAG,EAGnB,QAAQ,WAAW,GAAG,EAAE,QAAQ,YAAY,MAAM,EAClD,QAAQ,eAAe,QAAQ,EAAE,QAAQ,UAAU,GAAG,EAEtD,QAAQ,2BAA2B,KAAU,EAE7C,QAAQ,sBAAsB,KAAU,EACxC,QAAQ,8BAA8B,KAAU;AAAA,UACrD;AAAA,QACF;AAEA,YAAI,MAAM,SAAS,eAAe,MAAM,SAAS,QAAQ;AACvD;AAAA,QACF;AAEA,YAAI,MAAM,SAAS,gBAAgB,MAAM,SAAS,QAAQ;AACxD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,WAAO,UAAU,SAAS,QAAQ,OAAO;AACvC,UAAI;AAEJ,UAAI,CAAC,MAAM,GAAG,QAAQ,aAAa;AAAE;AAAA,MAAQ;AAE7C,WAAK,SAAS,MAAM,OAAO,SAAS,GAAG,UAAU,GAAG,UAAU;AAE5D,YAAI,MAAM,OAAO,MAAM,EAAE,SAAS,UAAU;AAAE;AAAA,QAAU;AAExD,YAAI,oBAAoB,KAAK,MAAM,OAAO,MAAM,EAAE,OAAO,GAAG;AAC1D,yBAAe,MAAM,OAAO,MAAM,EAAE,QAAQ;AAAA,QAC9C;AAEA,YAAI,QAAQ,KAAK,MAAM,OAAO,MAAM,EAAE,OAAO,GAAG;AAC9C,uBAAa,MAAM,OAAO,MAAM,EAAE,QAAQ;AAAA,QAC5C;AAAA,MAEF;AAAA,IACF;AAAA;AAAA;;;AC1GA;AAAA;AAAA;AAKA,QAAI,eAAiB,gBAA2B;AAChD,QAAI,cAAiB,gBAA2B;AAChD,QAAI,iBAAiB,gBAA2B;AAEhD,QAAI,gBAAgB;AACpB,QAAI,WAAW;AACf,QAAI,aAAa;AAGjB,aAAS,UAAU,KAAK,OAAO,IAAI;AACjC,aAAO,IAAI,OAAO,GAAG,KAAK,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC;AAAA,IACzD;AAEA,aAAS,gBAAgB,QAAQ,OAAO;AACtC,UAAI,GAAG,OAAO,MAAM,GAAG,KAAK,KAAK,WAAW,MAAM,UAAU,UACxD,iBAAiB,iBAAiB,kBAAkB,kBACpD,SAAS,UAAU,GAAG,UAAU,OAAO,WAAW;AAEtD,cAAQ,CAAC;AAET,WAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AAClC,gBAAQ,OAAO,CAAC;AAEhB,oBAAY,OAAO,CAAC,EAAE;AAEtB,aAAK,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AACtC,cAAI,MAAM,CAAC,EAAE,SAAS,WAAW;AAAE;AAAA,UAAO;AAAA,QAC5C;AACA,cAAM,SAAS,IAAI;AAEnB,YAAI,MAAM,SAAS,QAAQ;AAAE;AAAA,QAAU;AAEvC,eAAO,MAAM;AACb,cAAM;AACN,cAAM,KAAK;AAGX;AACA,iBAAO,MAAM,KAAK;AAChB,qBAAS,YAAY;AACrB,gBAAI,SAAS,KAAK,IAAI;AACtB,gBAAI,CAAC,GAAG;AAAE;AAAA,YAAO;AAEjB,sBAAU,WAAW;AACrB,kBAAM,EAAE,QAAQ;AAChB,uBAAY,EAAE,CAAC,MAAM;AAKrB,uBAAW;AAEX,gBAAI,EAAE,QAAQ,KAAK,GAAG;AACpB,yBAAW,KAAK,WAAW,EAAE,QAAQ,CAAC;AAAA,YACxC,OAAO;AACL,mBAAK,IAAI,IAAI,GAAG,KAAK,GAAG,KAAK;AAC3B,oBAAI,OAAO,CAAC,EAAE,SAAS,eAAe,OAAO,CAAC,EAAE,SAAS;AAAa;AACtE,oBAAI,CAAC,OAAO,CAAC,EAAE;AAAS;AAExB,2BAAW,OAAO,CAAC,EAAE,QAAQ,WAAW,OAAO,CAAC,EAAE,QAAQ,SAAS,CAAC;AACpE;AAAA,cACF;AAAA,YACF;AAKA,uBAAW;AAEX,gBAAI,MAAM,KAAK;AACb,yBAAW,KAAK,WAAW,GAAG;AAAA,YAChC,OAAO;AACL,mBAAK,IAAI,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,oBAAI,OAAO,CAAC,EAAE,SAAS,eAAe,OAAO,CAAC,EAAE,SAAS;AAAa;AACtE,oBAAI,CAAC,OAAO,CAAC,EAAE;AAAS;AAExB,2BAAW,OAAO,CAAC,EAAE,QAAQ,WAAW,CAAC;AACzC;AAAA,cACF;AAAA,YACF;AAEA,8BAAkB,eAAe,QAAQ,KAAK,YAAY,OAAO,aAAa,QAAQ,CAAC;AACvF,8BAAkB,eAAe,QAAQ,KAAK,YAAY,OAAO,aAAa,QAAQ,CAAC;AAEvF,+BAAmB,aAAa,QAAQ;AACxC,+BAAmB,aAAa,QAAQ;AAExC,gBAAI,kBAAkB;AACpB,wBAAU;AAAA,YACZ,WAAW,iBAAiB;AAC1B,kBAAI,EAAE,oBAAoB,kBAAkB;AAC1C,0BAAU;AAAA,cACZ;AAAA,YACF;AAEA,gBAAI,kBAAkB;AACpB,yBAAW;AAAA,YACb,WAAW,iBAAiB;AAC1B,kBAAI,EAAE,oBAAoB,kBAAkB;AAC1C,2BAAW;AAAA,cACb;AAAA,YACF;AAEA,gBAAI,aAAa,MAAgB,EAAE,CAAC,MAAM,KAAK;AAC7C,kBAAI,YAAY,MAAgB,YAAY,IAAc;AAExD,2BAAW,UAAU;AAAA,cACvB;AAAA,YACF;AAEA,gBAAI,WAAW,UAAU;AAQvB,wBAAU;AACV,yBAAW;AAAA,YACb;AAEA,gBAAI,CAAC,WAAW,CAAC,UAAU;AAEzB,kBAAI,UAAU;AACZ,sBAAM,UAAU,UAAU,MAAM,SAAS,EAAE,OAAO,UAAU;AAAA,cAC9D;AACA;AAAA,YACF;AAEA,gBAAI,UAAU;AAEZ,mBAAK,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AACtC,uBAAO,MAAM,CAAC;AACd,oBAAI,MAAM,CAAC,EAAE,QAAQ,WAAW;AAAE;AAAA,gBAAO;AACzC,oBAAI,KAAK,WAAW,YAAY,MAAM,CAAC,EAAE,UAAU,WAAW;AAC5D,yBAAO,MAAM,CAAC;AAEd,sBAAI,UAAU;AACZ,gCAAY,MAAM,GAAG,QAAQ,OAAO,CAAC;AACrC,iCAAa,MAAM,GAAG,QAAQ,OAAO,CAAC;AAAA,kBACxC,OAAO;AACL,gCAAY,MAAM,GAAG,QAAQ,OAAO,CAAC;AACrC,iCAAa,MAAM,GAAG,QAAQ,OAAO,CAAC;AAAA,kBACxC;AAKA,wBAAM,UAAU,UAAU,MAAM,SAAS,EAAE,OAAO,UAAU;AAC5D,yBAAO,KAAK,KAAK,EAAE,UAAU;AAAA,oBAC3B,OAAO,KAAK,KAAK,EAAE;AAAA,oBAAS,KAAK;AAAA,oBAAK;AAAA,kBAAS;AAEjD,yBAAO,WAAW,SAAS;AAC3B,sBAAI,KAAK,UAAU,GAAG;AAAE,2BAAO,UAAU,SAAS;AAAA,kBAAG;AAErD,yBAAO,MAAM;AACb,wBAAM,KAAK;AAEX,wBAAM,SAAS;AACf,2BAAS;AAAA,gBACX;AAAA,cACF;AAAA,YACF;AAEA,gBAAI,SAAS;AACX,oBAAM,KAAK;AAAA,gBACT,OAAO;AAAA,gBACP,KAAK,EAAE;AAAA,gBACP,QAAQ;AAAA,gBACR,OAAO;AAAA,cACT,CAAC;AAAA,YACH,WAAW,YAAY,UAAU;AAC/B,oBAAM,UAAU,UAAU,MAAM,SAAS,EAAE,OAAO,UAAU;AAAA,YAC9D;AAAA,UACF;AAAA,MACF;AAAA,IACF;AAGA,WAAO,UAAU,SAAS,YAAY,OAAO;AAE3C,UAAI;AAEJ,UAAI,CAAC,MAAM,GAAG,QAAQ,aAAa;AAAE;AAAA,MAAQ;AAE7C,WAAK,SAAS,MAAM,OAAO,SAAS,GAAG,UAAU,GAAG,UAAU;AAE5D,YAAI,MAAM,OAAO,MAAM,EAAE,SAAS,YAC9B,CAAC,cAAc,KAAK,MAAM,OAAO,MAAM,EAAE,OAAO,GAAG;AACrD;AAAA,QACF;AAEA,wBAAgB,MAAM,OAAO,MAAM,EAAE,UAAU,KAAK;AAAA,MACtD;AAAA,IACF;AAAA;AAAA;;;ACxMA;AAAA;AAAA;AAcA,aAAS,MAAM,MAAM,KAAK,SAAS;AAMjC,WAAK,OAAW;AAOhB,WAAK,MAAW;AAOhB,WAAK,QAAW;AAOhB,WAAK,MAAW;AAWhB,WAAK,UAAW;AAOhB,WAAK,QAAW;AAOhB,WAAK,WAAW;AAQhB,WAAK,UAAW;AAOhB,WAAK,SAAW;AAWhB,WAAK,OAAW;AAOhB,WAAK,OAAW;AAQhB,WAAK,QAAW;AAQhB,WAAK,SAAW;AAAA,IAClB;AAQA,UAAM,UAAU,YAAY,SAAS,UAAU,MAAM;AACnD,UAAI,OAAO,GAAG;AAEd,UAAI,CAAC,KAAK,OAAO;AAAE,eAAO;AAAA,MAAI;AAE9B,cAAQ,KAAK;AAEb,WAAK,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAC5C,YAAI,MAAM,CAAC,EAAE,CAAC,MAAM,MAAM;AAAE,iBAAO;AAAA,QAAG;AAAA,MACxC;AACA,aAAO;AAAA,IACT;AAQA,UAAM,UAAU,WAAW,SAAS,SAAS,UAAU;AACrD,UAAI,KAAK,OAAO;AACd,aAAK,MAAM,KAAK,QAAQ;AAAA,MAC1B,OAAO;AACL,aAAK,QAAQ,CAAE,QAAS;AAAA,MAC1B;AAAA,IACF;AAQA,UAAM,UAAU,UAAU,SAAS,QAAQ,MAAM,OAAO;AACtD,UAAI,MAAM,KAAK,UAAU,IAAI,GACzB,WAAW,CAAE,MAAM,KAAM;AAE7B,UAAI,MAAM,GAAG;AACX,aAAK,SAAS,QAAQ;AAAA,MACxB,OAAO;AACL,aAAK,MAAM,GAAG,IAAI;AAAA,MACpB;AAAA,IACF;AAQA,UAAM,UAAU,UAAU,SAAS,QAAQ,MAAM;AAC/C,UAAI,MAAM,KAAK,UAAU,IAAI,GAAG,QAAQ;AACxC,UAAI,OAAO,GAAG;AACZ,gBAAQ,KAAK,MAAM,GAAG,EAAE,CAAC;AAAA,MAC3B;AACA,aAAO;AAAA,IACT;AASA,UAAM,UAAU,WAAW,SAAS,SAAS,MAAM,OAAO;AACxD,UAAI,MAAM,KAAK,UAAU,IAAI;AAE7B,UAAI,MAAM,GAAG;AACX,aAAK,SAAS,CAAE,MAAM,KAAM,CAAC;AAAA,MAC/B,OAAO;AACL,aAAK,MAAM,GAAG,EAAE,CAAC,IAAI,KAAK,MAAM,GAAG,EAAE,CAAC,IAAI,MAAM;AAAA,MAClD;AAAA,IACF;AAGA,WAAO,UAAU;AAAA;AAAA;;;ACxMjB;AAAA;AAAA;AAIA,QAAI,QAAQ;AAGZ,aAAS,UAAU,KAAK,IAAI,KAAK;AAC/B,WAAK,MAAM;AACX,WAAK,MAAM;AACX,WAAK,SAAS,CAAC;AACf,WAAK,aAAa;AAClB,WAAK,KAAK;AAAA,IACZ;AAGA,cAAU,UAAU,QAAQ;AAG5B,WAAO,UAAU;AAAA;AAAA;;;ACnBjB;AAAA;AAAA;AASA,QAAI,QAAS;AAGb,QAAI,SAAS;AAAA,MACX,CAAE,aAAkB,mBAAuC;AAAA,MAC3D,CAAE,SAAkB,eAAuC;AAAA,MAC3D,CAAE,UAAkB,gBAAuC;AAAA,MAC3D,CAAE,WAAkB,iBAAuC;AAAA,MAC3D,CAAE,gBAAkB,sBAAuC;AAAA,MAC3D,CAAE,eAAkB,qBAAuC;AAAA,IAC7D;AAMA,aAAS,OAAO;AAMd,WAAK,QAAQ,IAAI,MAAM;AAEvB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,aAAK,MAAM,KAAK,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC;AAAA,MAC5C;AAAA,IACF;AAQA,SAAK,UAAU,UAAU,SAAU,OAAO;AACxC,UAAI,GAAG,GAAG;AAEV,cAAQ,KAAK,MAAM,SAAS,EAAE;AAE9B,WAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AACxC,cAAM,CAAC,EAAE,KAAK;AAAA,MAChB;AAAA,IACF;AAEA,SAAK,UAAU,QAAQ;AAGvB,WAAO,UAAU;AAAA;AAAA;;;ACzDjB;AAAA;AAAA;AAIA,QAAI,UAAU,gBAA2B;AAGzC,aAAS,QAAQ,OAAO,MAAM;AAC5B,UAAI,MAAM,MAAM,OAAO,IAAI,IAAI,MAAM,OAAO,IAAI,GAC5C,MAAM,MAAM,OAAO,IAAI;AAE3B,aAAO,MAAM,IAAI,OAAO,KAAK,MAAM,GAAG;AAAA,IACxC;AAEA,aAAS,aAAa,KAAK;AACzB,UAAI,SAAS,CAAC,GACV,MAAM,GACN,MAAM,IAAI,QACV,IACA,YAAY,OACZ,UAAU,GACV,UAAU;AAEd,WAAM,IAAI,WAAW,GAAG;AAExB,aAAO,MAAM,KAAK;AAChB,YAAI,OAAO,KAAa;AACtB,cAAI,CAAC,WAAW;AAEd,mBAAO,KAAK,UAAU,IAAI,UAAU,SAAS,GAAG,CAAC;AACjD,sBAAU;AACV,sBAAU,MAAM;AAAA,UAClB,OAAO;AAEL,uBAAW,IAAI,UAAU,SAAS,MAAM,CAAC;AACzC,sBAAU;AAAA,UACZ;AAAA,QACF;AAEA,oBAAa,OAAO;AACpB;AAEA,aAAK,IAAI,WAAW,GAAG;AAAA,MACzB;AAEA,aAAO,KAAK,UAAU,IAAI,UAAU,OAAO,CAAC;AAE5C,aAAO;AAAA,IACT;AAGA,WAAO,UAAU,SAAS,MAAM,OAAO,WAAW,SAAS,QAAQ;AACjE,UAAI,IAAI,UAAU,KAAK,GAAG,GAAG,UAAU,SAAS,aAAa,OACzD,QAAQ,GAAG,YAAY,YAAY,eAAe,WAClD,iBAAiB,SAAS;AAG9B,UAAI,YAAY,IAAI,SAAS;AAAE,eAAO;AAAA,MAAO;AAE7C,iBAAW,YAAY;AAEvB,UAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,WAAW;AAAE,eAAO;AAAA,MAAO;AAG9D,UAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,aAAa,GAAG;AAAE,eAAO;AAAA,MAAO;AAMnE,YAAM,MAAM,OAAO,QAAQ,IAAI,MAAM,OAAO,QAAQ;AACpD,UAAI,OAAO,MAAM,OAAO,QAAQ,GAAG;AAAE,eAAO;AAAA,MAAO;AAEnD,gBAAU,MAAM,IAAI,WAAW,KAAK;AACpC,UAAI,YAAY,OAAe,YAAY,MAAe,YAAY,IAAa;AAAE,eAAO;AAAA,MAAO;AAEnG,UAAI,OAAO,MAAM,OAAO,QAAQ,GAAG;AAAE,eAAO;AAAA,MAAO;AAEnD,iBAAW,MAAM,IAAI,WAAW,KAAK;AACrC,UAAI,aAAa,OAAe,aAAa,MAAe,aAAa,MAAe,CAAC,QAAQ,QAAQ,GAAG;AAC1G,eAAO;AAAA,MACT;AAIA,UAAI,YAAY,MAAe,QAAQ,QAAQ,GAAG;AAAE,eAAO;AAAA,MAAO;AAElE,aAAO,MAAM,MAAM,OAAO,QAAQ,GAAG;AACnC,aAAK,MAAM,IAAI,WAAW,GAAG;AAE7B,YAAI,OAAO,OAAe,OAAO,MAAe,OAAO,MAAe,CAAC,QAAQ,EAAE,GAAG;AAAE,iBAAO;AAAA,QAAO;AAEpG;AAAA,MACF;AAEA,iBAAW,QAAQ,OAAO,YAAY,CAAC;AAEvC,gBAAU,SAAS,MAAM,GAAG;AAC5B,eAAS,CAAC;AACV,WAAK,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACnC,YAAI,QAAQ,CAAC,EAAE,KAAK;AACpB,YAAI,CAAC,GAAG;AAGN,cAAI,MAAM,KAAK,MAAM,QAAQ,SAAS,GAAG;AACvC;AAAA,UACF,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,YAAI,CAAC,WAAW,KAAK,CAAC,GAAG;AAAE,iBAAO;AAAA,QAAO;AACzC,YAAI,EAAE,WAAW,EAAE,SAAS,CAAC,MAAM,IAAa;AAC9C,iBAAO,KAAK,EAAE,WAAW,CAAC,MAAM,KAAc,WAAW,OAAO;AAAA,QAClE,WAAW,EAAE,WAAW,CAAC,MAAM,IAAa;AAC1C,iBAAO,KAAK,MAAM;AAAA,QACpB,OAAO;AACL,iBAAO,KAAK,EAAE;AAAA,QAChB;AAAA,MACF;AAEA,iBAAW,QAAQ,OAAO,SAAS,EAAE,KAAK;AAC1C,UAAI,SAAS,QAAQ,GAAG,MAAM,IAAI;AAAE,eAAO;AAAA,MAAO;AAClD,UAAI,MAAM,OAAO,SAAS,IAAI,MAAM,aAAa,GAAG;AAAE,eAAO;AAAA,MAAO;AACpE,gBAAU,aAAa,QAAQ;AAC/B,UAAI,QAAQ,UAAU,QAAQ,CAAC,MAAM;AAAI,gBAAQ,MAAM;AACvD,UAAI,QAAQ,UAAU,QAAQ,QAAQ,SAAS,CAAC,MAAM;AAAI,gBAAQ,IAAI;AAItE,oBAAc,QAAQ;AACtB,UAAI,gBAAgB,KAAK,gBAAgB,OAAO,QAAQ;AAAE,eAAO;AAAA,MAAO;AAExE,UAAI,QAAQ;AAAE,eAAO;AAAA,MAAM;AAE3B,sBAAgB,MAAM;AACtB,YAAM,aAAa;AAInB,wBAAkB,MAAM,GAAG,MAAM,MAAM,SAAS,YAAY;AAE5D,cAAY,MAAM,KAAK,cAAc,SAAS,CAAC;AAC/C,YAAM,MAAM,aAAa,CAAE,WAAW,CAAE;AAExC,cAAY,MAAM,KAAK,cAAc,SAAS,CAAC;AAC/C,YAAM,MAAM,CAAE,WAAW,YAAY,CAAE;AAEvC,cAAY,MAAM,KAAK,WAAW,MAAM,CAAC;AACzC,YAAM,MAAM,CAAE,WAAW,YAAY,CAAE;AAEvC,WAAK,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACnC,gBAAiB,MAAM,KAAK,WAAW,MAAM,CAAC;AAC9C,YAAI,OAAO,CAAC,GAAG;AACb,gBAAM,QAAS,CAAE,CAAE,SAAS,gBAAgB,OAAO,CAAC,CAAE,CAAE;AAAA,QAC1D;AAEA,gBAAiB,MAAM,KAAK,UAAU,IAAI,CAAC;AAC3C,cAAM,UAAW,QAAQ,CAAC,EAAE,KAAK;AACjC,cAAM,WAAW,CAAC;AAElB,gBAAiB,MAAM,KAAK,YAAY,MAAM,EAAE;AAAA,MAClD;AAEA,cAAY,MAAM,KAAK,YAAY,MAAM,EAAE;AAC3C,cAAY,MAAM,KAAK,eAAe,SAAS,EAAE;AAEjD,WAAK,WAAW,YAAY,GAAG,WAAW,SAAS,YAAY;AAC7D,YAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,WAAW;AAAE;AAAA,QAAO;AAEvD,oBAAY;AACZ,aAAK,IAAI,GAAG,IAAI,gBAAgB,QAAQ,IAAI,GAAG,KAAK;AAClD,cAAI,gBAAgB,CAAC,EAAE,OAAO,UAAU,SAAS,IAAI,GAAG;AACtD,wBAAY;AACZ;AAAA,UACF;AAAA,QACF;AAEA,YAAI,WAAW;AAAE;AAAA,QAAO;AACxB,mBAAW,QAAQ,OAAO,QAAQ,EAAE,KAAK;AACzC,YAAI,CAAC,UAAU;AAAE;AAAA,QAAO;AACxB,YAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,aAAa,GAAG;AAAE;AAAA,QAAO;AAC5D,kBAAU,aAAa,QAAQ;AAC/B,YAAI,QAAQ,UAAU,QAAQ,CAAC,MAAM;AAAI,kBAAQ,MAAM;AACvD,YAAI,QAAQ,UAAU,QAAQ,QAAQ,SAAS,CAAC,MAAM;AAAI,kBAAQ,IAAI;AAEtE,YAAI,aAAa,YAAY,GAAG;AAC9B,kBAAY,MAAM,KAAK,cAAc,SAAS,CAAC;AAC/C,gBAAM,MAAM,aAAa,CAAE,YAAY,GAAG,CAAE;AAAA,QAC9C;AAEA,gBAAY,MAAM,KAAK,WAAW,MAAM,CAAC;AACzC,cAAM,MAAM,CAAE,UAAU,WAAW,CAAE;AAErC,aAAK,IAAI,GAAG,IAAI,aAAa,KAAK;AAChC,kBAAiB,MAAM,KAAK,WAAW,MAAM,CAAC;AAC9C,cAAI,OAAO,CAAC,GAAG;AACb,kBAAM,QAAS,CAAE,CAAE,SAAS,gBAAgB,OAAO,CAAC,CAAE,CAAE;AAAA,UAC1D;AAEA,kBAAiB,MAAM,KAAK,UAAU,IAAI,CAAC;AAC3C,gBAAM,UAAW,QAAQ,CAAC,IAAI,QAAQ,CAAC,EAAE,KAAK,IAAI;AAClD,gBAAM,WAAW,CAAC;AAElB,kBAAiB,MAAM,KAAK,YAAY,MAAM,EAAE;AAAA,QAClD;AACA,gBAAQ,MAAM,KAAK,YAAY,MAAM,EAAE;AAAA,MACzC;AAEA,UAAI,YAAY;AACd,gBAAQ,MAAM,KAAK,eAAe,SAAS,EAAE;AAC7C,mBAAW,CAAC,IAAI;AAAA,MAClB;AAEA,cAAQ,MAAM,KAAK,eAAe,SAAS,EAAE;AAC7C,iBAAW,CAAC,IAAI;AAEhB,YAAM,aAAa;AACnB,YAAM,OAAO;AACb,aAAO;AAAA,IACT;AAAA;AAAA;;;AC5NA;AAAA;AAAA;AAKA,WAAO,UAAU,SAAS,KAAK,OAAO,WAAW,SAAqB;AACpE,UAAI,UAAU,MAAM;AAEpB,UAAI,MAAM,OAAO,SAAS,IAAI,MAAM,YAAY,GAAG;AAAE,eAAO;AAAA,MAAO;AAEnE,aAAO,WAAW,YAAY;AAE9B,aAAO,WAAW,SAAS;AACzB,YAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B;AACA;AAAA,QACF;AAEA,YAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,aAAa,GAAG;AACjD;AACA,iBAAO;AACP;AAAA,QACF;AACA;AAAA,MACF;AAEA,YAAM,OAAO;AAEb,cAAgB,MAAM,KAAK,cAAc,QAAQ,CAAC;AAClD,YAAM,UAAU,MAAM,SAAS,WAAW,MAAM,IAAI,MAAM,WAAW,KAAK,IAAI;AAC9E,YAAM,MAAU,CAAE,WAAW,MAAM,IAAK;AAExC,aAAO;AAAA,IACT;AAAA;AAAA;;;ACjCA;AAAA;AAAA;AAKA,WAAO,UAAU,SAAS,MAAM,OAAO,WAAW,SAAS,QAAQ;AACjE,UAAI,QAAQ,KAAK,QAAQ,UAAU,KAAK,OAAO,QAC3C,gBAAgB,OAChB,MAAM,MAAM,OAAO,SAAS,IAAI,MAAM,OAAO,SAAS,GACtD,MAAM,MAAM,OAAO,SAAS;AAGhC,UAAI,MAAM,OAAO,SAAS,IAAI,MAAM,aAAa,GAAG;AAAE,eAAO;AAAA,MAAO;AAEpE,UAAI,MAAM,IAAI,KAAK;AAAE,eAAO;AAAA,MAAO;AAEnC,eAAS,MAAM,IAAI,WAAW,GAAG;AAEjC,UAAI,WAAW,OAAe,WAAW,IAAc;AACrD,eAAO;AAAA,MACT;AAGA,YAAM;AACN,YAAM,MAAM,UAAU,KAAK,MAAM;AAEjC,YAAM,MAAM;AAEZ,UAAI,MAAM,GAAG;AAAE,eAAO;AAAA,MAAO;AAE7B,eAAS,MAAM,IAAI,MAAM,KAAK,GAAG;AACjC,eAAS,MAAM,IAAI,MAAM,KAAK,GAAG;AAEjC,UAAI,WAAW,IAAc;AAC3B,YAAI,OAAO,QAAQ,OAAO,aAAa,MAAM,CAAC,KAAK,GAAG;AACpD,iBAAO;AAAA,QACT;AAAA,MACF;AAGA,UAAI,QAAQ;AAAE,eAAO;AAAA,MAAM;AAG3B,iBAAW;AAEX,iBAAS;AACP;AACA,YAAI,YAAY,SAAS;AAGvB;AAAA,QACF;AAEA,cAAM,MAAM,MAAM,OAAO,QAAQ,IAAI,MAAM,OAAO,QAAQ;AAC1D,cAAM,MAAM,OAAO,QAAQ;AAE3B,YAAI,MAAM,OAAO,MAAM,OAAO,QAAQ,IAAI,MAAM,WAAW;AAIzD;AAAA,QACF;AAEA,YAAI,MAAM,IAAI,WAAW,GAAG,MAAM,QAAQ;AAAE;AAAA,QAAU;AAEtD,YAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,aAAa,GAAG;AAEjD;AAAA,QACF;AAEA,cAAM,MAAM,UAAU,KAAK,MAAM;AAGjC,YAAI,MAAM,MAAM,KAAK;AAAE;AAAA,QAAU;AAGjC,cAAM,MAAM,WAAW,GAAG;AAE1B,YAAI,MAAM,KAAK;AAAE;AAAA,QAAU;AAE3B,wBAAgB;AAEhB;AAAA,MACF;AAGA,YAAM,MAAM,OAAO,SAAS;AAE5B,YAAM,OAAO,YAAY,gBAAgB,IAAI;AAE7C,cAAgB,MAAM,KAAK,SAAS,QAAQ,CAAC;AAC7C,YAAM,OAAU;AAChB,YAAM,UAAU,MAAM,SAAS,YAAY,GAAG,UAAU,KAAK,IAAI;AACjE,YAAM,SAAU;AAChB,YAAM,MAAU,CAAE,WAAW,MAAM,IAAK;AAExC,aAAO;AAAA,IACT;AAAA;AAAA;;;ACjGA;AAAA;AAAA;AAIA,QAAI,UAAU,gBAA2B;AAGzC,WAAO,UAAU,SAAS,WAAW,OAAO,WAAW,SAAS,QAAQ;AACtE,UAAI,WACA,IACA,GACA,SACA,GACA,eACA,OACA,UACA,QACA,WACA,YACA,WACA,eACA,WACA,WACA,kBACA,WACA,iBACA,OACA,aACA,aAAa,MAAM,SACnB,MAAM,MAAM,OAAO,SAAS,IAAI,MAAM,OAAO,SAAS,GACtD,MAAM,MAAM,OAAO,SAAS;AAGhC,UAAI,MAAM,OAAO,SAAS,IAAI,MAAM,aAAa,GAAG;AAAE,eAAO;AAAA,MAAO;AAGpE,UAAI,MAAM,IAAI,WAAW,KAAK,MAAM,IAAa;AAAE,eAAO;AAAA,MAAO;AAIjE,UAAI,QAAQ;AAAE,eAAO;AAAA,MAAM;AAG3B,gBAAU,SAAS,MAAM,OAAO,SAAS,IAAI;AAG7C,UAAI,MAAM,IAAI,WAAW,GAAG,MAAM,IAAkB;AAGlD;AACA;AACA;AACA,oBAAY;AACZ,2BAAmB;AAAA,MACrB,WAAW,MAAM,IAAI,WAAW,GAAG,MAAM,GAAgB;AACvD,2BAAmB;AAEnB,aAAK,MAAM,QAAQ,SAAS,IAAI,UAAU,MAAM,GAAG;AAGjD;AACA;AACA;AACA,sBAAY;AAAA,QACd,OAAO;AAIL,sBAAY;AAAA,QACd;AAAA,MACF,OAAO;AACL,2BAAmB;AAAA,MACrB;AAEA,kBAAY,CAAE,MAAM,OAAO,SAAS,CAAE;AACtC,YAAM,OAAO,SAAS,IAAI;AAE1B,aAAO,MAAM,KAAK;AAChB,aAAK,MAAM,IAAI,WAAW,GAAG;AAE7B,YAAI,QAAQ,EAAE,GAAG;AACf,cAAI,OAAO,GAAM;AACf,sBAAU,KAAK,SAAS,MAAM,QAAQ,SAAS,KAAK,YAAY,IAAI,MAAM;AAAA,UAC5E,OAAO;AACL;AAAA,UACF;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAEA;AAAA,MACF;AAEA,mBAAa,CAAE,MAAM,QAAQ,SAAS,CAAE;AACxC,YAAM,QAAQ,SAAS,IAAI,MAAM,OAAO,SAAS,IAAI,KAAK,mBAAmB,IAAI;AAEjF,sBAAgB,OAAO;AAEvB,kBAAY,CAAE,MAAM,OAAO,SAAS,CAAE;AACtC,YAAM,OAAO,SAAS,IAAI,SAAS;AAEnC,kBAAY,CAAE,MAAM,OAAO,SAAS,CAAE;AACtC,YAAM,OAAO,SAAS,IAAI,MAAM,MAAM,OAAO,SAAS;AAEtD,wBAAkB,MAAM,GAAG,MAAM,MAAM,SAAS,YAAY;AAE5D,sBAAgB,MAAM;AACtB,YAAM,aAAa;AAoBnB,WAAK,WAAW,YAAY,GAAG,WAAW,SAAS,YAAY;AAS7D,sBAAc,MAAM,OAAO,QAAQ,IAAI,MAAM;AAE7C,cAAM,MAAM,OAAO,QAAQ,IAAI,MAAM,OAAO,QAAQ;AACpD,cAAM,MAAM,OAAO,QAAQ;AAE3B,YAAI,OAAO,KAAK;AAEd;AAAA,QACF;AAEA,YAAI,MAAM,IAAI,WAAW,KAAK,MAAM,MAAe,CAAC,aAAa;AAI/D,oBAAU,SAAS,MAAM,OAAO,QAAQ,IAAI;AAG5C,cAAI,MAAM,IAAI,WAAW,GAAG,MAAM,IAAkB;AAGlD;AACA;AACA;AACA,wBAAY;AACZ,+BAAmB;AAAA,UACrB,WAAW,MAAM,IAAI,WAAW,GAAG,MAAM,GAAgB;AACvD,+BAAmB;AAEnB,iBAAK,MAAM,QAAQ,QAAQ,IAAI,UAAU,MAAM,GAAG;AAGhD;AACA;AACA;AACA,0BAAY;AAAA,YACd,OAAO;AAIL,0BAAY;AAAA,YACd;AAAA,UACF,OAAO;AACL,+BAAmB;AAAA,UACrB;AAEA,oBAAU,KAAK,MAAM,OAAO,QAAQ,CAAC;AACrC,gBAAM,OAAO,QAAQ,IAAI;AAEzB,iBAAO,MAAM,KAAK;AAChB,iBAAK,MAAM,IAAI,WAAW,GAAG;AAE7B,gBAAI,QAAQ,EAAE,GAAG;AACf,kBAAI,OAAO,GAAM;AACf,0BAAU,KAAK,SAAS,MAAM,QAAQ,QAAQ,KAAK,YAAY,IAAI,MAAM;AAAA,cAC3E,OAAO;AACL;AAAA,cACF;AAAA,YACF,OAAO;AACL;AAAA,YACF;AAEA;AAAA,UACF;AAEA,0BAAgB,OAAO;AAEvB,qBAAW,KAAK,MAAM,QAAQ,QAAQ,CAAC;AACvC,gBAAM,QAAQ,QAAQ,IAAI,MAAM,OAAO,QAAQ,IAAI,KAAK,mBAAmB,IAAI;AAE/E,oBAAU,KAAK,MAAM,OAAO,QAAQ,CAAC;AACrC,gBAAM,OAAO,QAAQ,IAAI,SAAS;AAElC,oBAAU,KAAK,MAAM,OAAO,QAAQ,CAAC;AACrC,gBAAM,OAAO,QAAQ,IAAI,MAAM,MAAM,OAAO,QAAQ;AACpD;AAAA,QACF;AAGA,YAAI,eAAe;AAAE;AAAA,QAAO;AAG5B,oBAAY;AACZ,aAAK,IAAI,GAAG,IAAI,gBAAgB,QAAQ,IAAI,GAAG,KAAK;AAClD,cAAI,gBAAgB,CAAC,EAAE,OAAO,UAAU,SAAS,IAAI,GAAG;AACtD,wBAAY;AACZ;AAAA,UACF;AAAA,QACF;AAEA,YAAI,WAAW;AAKb,gBAAM,UAAU;AAEhB,cAAI,MAAM,cAAc,GAAG;AAIzB,sBAAU,KAAK,MAAM,OAAO,QAAQ,CAAC;AACrC,uBAAW,KAAK,MAAM,QAAQ,QAAQ,CAAC;AACvC,sBAAU,KAAK,MAAM,OAAO,QAAQ,CAAC;AACrC,sBAAU,KAAK,MAAM,OAAO,QAAQ,CAAC;AACrC,kBAAM,OAAO,QAAQ,KAAK,MAAM;AAAA,UAClC;AAEA;AAAA,QACF;AAEA,kBAAU,KAAK,MAAM,OAAO,QAAQ,CAAC;AACrC,mBAAW,KAAK,MAAM,QAAQ,QAAQ,CAAC;AACvC,kBAAU,KAAK,MAAM,OAAO,QAAQ,CAAC;AACrC,kBAAU,KAAK,MAAM,OAAO,QAAQ,CAAC;AAIrC,cAAM,OAAO,QAAQ,IAAI;AAAA,MAC3B;AAEA,kBAAY,MAAM;AAClB,YAAM,YAAY;AAElB,cAAe,MAAM,KAAK,mBAAmB,cAAc,CAAC;AAC5D,YAAM,SAAS;AACf,YAAM,MAAS,QAAQ,CAAE,WAAW,CAAE;AAEtC,YAAM,GAAG,MAAM,SAAS,OAAO,WAAW,QAAQ;AAElD,cAAe,MAAM,KAAK,oBAAoB,cAAc,EAAE;AAC9D,YAAM,SAAS;AAEf,YAAM,UAAU;AAChB,YAAM,aAAa;AACnB,YAAM,CAAC,IAAI,MAAM;AAIjB,WAAK,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACrC,cAAM,OAAO,IAAI,SAAS,IAAI,UAAU,CAAC;AACzC,cAAM,OAAO,IAAI,SAAS,IAAI,UAAU,CAAC;AACzC,cAAM,OAAO,IAAI,SAAS,IAAI,UAAU,CAAC;AACzC,cAAM,QAAQ,IAAI,SAAS,IAAI,WAAW,CAAC;AAAA,MAC7C;AACA,YAAM,YAAY;AAElB,aAAO;AAAA,IACT;AAAA;AAAA;;;AC3RA;AAAA;AAAA;AAIA,QAAI,UAAU,gBAA2B;AAGzC,WAAO,UAAU,SAAS,GAAG,OAAO,WAAW,SAAS,QAAQ;AAC9D,UAAI,QAAQ,KAAK,IAAI,OACjB,MAAM,MAAM,OAAO,SAAS,IAAI,MAAM,OAAO,SAAS,GACtD,MAAM,MAAM,OAAO,SAAS;AAGhC,UAAI,MAAM,OAAO,SAAS,IAAI,MAAM,aAAa,GAAG;AAAE,eAAO;AAAA,MAAO;AAEpE,eAAS,MAAM,IAAI,WAAW,KAAK;AAGnC,UAAI,WAAW,MACX,WAAW,MACX,WAAW,IAAa;AAC1B,eAAO;AAAA,MACT;AAIA,YAAM;AACN,aAAO,MAAM,KAAK;AAChB,aAAK,MAAM,IAAI,WAAW,KAAK;AAC/B,YAAI,OAAO,UAAU,CAAC,QAAQ,EAAE,GAAG;AAAE,iBAAO;AAAA,QAAO;AACnD,YAAI,OAAO,QAAQ;AAAE;AAAA,QAAO;AAAA,MAC9B;AAEA,UAAI,MAAM,GAAG;AAAE,eAAO;AAAA,MAAO;AAE7B,UAAI,QAAQ;AAAE,eAAO;AAAA,MAAM;AAE3B,YAAM,OAAO,YAAY;AAEzB,cAAe,MAAM,KAAK,MAAM,MAAM,CAAC;AACvC,YAAM,MAAS,CAAE,WAAW,MAAM,IAAK;AACvC,YAAM,SAAS,MAAM,MAAM,CAAC,EAAE,KAAK,OAAO,aAAa,MAAM,CAAC;AAE9D,aAAO;AAAA,IACT;AAAA;AAAA;;;AC5CA;AAAA;AAAA;AAIA,QAAI,UAAU,gBAA2B;AAKzC,aAAS,qBAAqB,OAAO,WAAW;AAC9C,UAAI,QAAQ,KAAK,KAAK;AAEtB,YAAM,MAAM,OAAO,SAAS,IAAI,MAAM,OAAO,SAAS;AACtD,YAAM,MAAM,OAAO,SAAS;AAE5B,eAAS,MAAM,IAAI,WAAW,KAAK;AAEnC,UAAI,WAAW,MACX,WAAW,MACX,WAAW,IAAa;AAC1B,eAAO;AAAA,MACT;AAEA,UAAI,MAAM,KAAK;AACb,aAAK,MAAM,IAAI,WAAW,GAAG;AAE7B,YAAI,CAAC,QAAQ,EAAE,GAAG;AAEhB,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAIA,aAAS,sBAAsB,OAAO,WAAW;AAC/C,UAAI,IACA,QAAQ,MAAM,OAAO,SAAS,IAAI,MAAM,OAAO,SAAS,GACxD,MAAM,OACN,MAAM,MAAM,OAAO,SAAS;AAGhC,UAAI,MAAM,KAAK,KAAK;AAAE,eAAO;AAAA,MAAI;AAEjC,WAAK,MAAM,IAAI,WAAW,KAAK;AAE/B,UAAI,KAAK,MAAe,KAAK,IAAa;AAAE,eAAO;AAAA,MAAI;AAEvD,iBAAS;AAEP,YAAI,OAAO,KAAK;AAAE,iBAAO;AAAA,QAAI;AAE7B,aAAK,MAAM,IAAI,WAAW,KAAK;AAE/B,YAAI,MAAM,MAAe,MAAM,IAAa;AAI1C,cAAI,MAAM,SAAS,IAAI;AAAE,mBAAO;AAAA,UAAI;AAEpC;AAAA,QACF;AAGA,YAAI,OAAO,MAAe,OAAO,IAAa;AAC5C;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAGA,UAAI,MAAM,KAAK;AACb,aAAK,MAAM,IAAI,WAAW,GAAG;AAE7B,YAAI,CAAC,QAAQ,EAAE,GAAG;AAEhB,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,aAAS,oBAAoB,OAAO,KAAK;AACvC,UAAI,GAAG,GACH,QAAQ,MAAM,QAAQ;AAE1B,WAAK,IAAI,MAAM,GAAG,IAAI,MAAM,OAAO,SAAS,GAAG,IAAI,GAAG,KAAK;AACzD,YAAI,MAAM,OAAO,CAAC,EAAE,UAAU,SAAS,MAAM,OAAO,CAAC,EAAE,SAAS,kBAAkB;AAChF,gBAAM,OAAO,IAAI,CAAC,EAAE,SAAS;AAC7B,gBAAM,OAAO,CAAC,EAAE,SAAS;AACzB,eAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAGA,WAAO,UAAU,SAAS,KAAK,OAAO,WAAW,SAAS,QAAQ;AAChE,UAAI,IACA,cACA,GACA,QACA,mBACA,SACA,WACA,WACA,GACA,WACA,YACA,gBACA,aACA,KACA,UACA,QACA,eACA,eACA,WACA,WACA,UACA,KACA,gBACA,cACA,OACA,WACA,iBACA,OACA,yBAAyB,OACzB,QAAQ;AAGZ,UAAI,MAAM,OAAO,SAAS,IAAI,MAAM,aAAa,GAAG;AAAE,eAAO;AAAA,MAAO;AAQpE,UAAI,MAAM,cAAc,KACpB,MAAM,OAAO,SAAS,IAAI,MAAM,cAAc,KAC9C,MAAM,OAAO,SAAS,IAAI,MAAM,WAAW;AAC7C,eAAO;AAAA,MACT;AAIA,UAAI,UAAU,MAAM,eAAe,aAAa;AAM9C,YAAI,MAAM,OAAO,SAAS,KAAK,MAAM,WAAW;AAC9C,mCAAyB;AAAA,QAC3B;AAAA,MACF;AAGA,WAAK,iBAAiB,sBAAsB,OAAO,SAAS,MAAM,GAAG;AACnE,oBAAY;AACZ,gBAAQ,MAAM,OAAO,SAAS,IAAI,MAAM,OAAO,SAAS;AACxD,sBAAc,OAAO,MAAM,IAAI,MAAM,OAAO,iBAAiB,CAAC,CAAC;AAI/D,YAAI,0BAA0B,gBAAgB;AAAG,iBAAO;AAAA,MAE1D,YAAY,iBAAiB,qBAAqB,OAAO,SAAS,MAAM,GAAG;AACzE,oBAAY;AAAA,MAEd,OAAO;AACL,eAAO;AAAA,MACT;AAIA,UAAI,wBAAwB;AAC1B,YAAI,MAAM,WAAW,cAAc,KAAK,MAAM,OAAO,SAAS;AAAG,iBAAO;AAAA,MAC1E;AAGA,uBAAiB,MAAM,IAAI,WAAW,iBAAiB,CAAC;AAGxD,UAAI,QAAQ;AAAE,eAAO;AAAA,MAAM;AAG3B,mBAAa,MAAM,OAAO;AAE1B,UAAI,WAAW;AACb,gBAAc,MAAM,KAAK,qBAAqB,MAAM,CAAC;AACrD,YAAI,gBAAgB,GAAG;AACrB,gBAAM,QAAQ,CAAE,CAAE,SAAS,WAAY,CAAE;AAAA,QAC3C;AAAA,MAEF,OAAO;AACL,gBAAc,MAAM,KAAK,oBAAoB,MAAM,CAAC;AAAA,MACtD;AAEA,YAAM,MAAS,YAAY,CAAE,WAAW,CAAE;AAC1C,YAAM,SAAS,OAAO,aAAa,cAAc;AAMjD,iBAAW;AACX,qBAAe;AACf,wBAAkB,MAAM,GAAG,MAAM,MAAM,SAAS,MAAM;AAEtD,sBAAgB,MAAM;AACtB,YAAM,aAAa;AAEnB,aAAO,WAAW,SAAS;AACzB,cAAM;AACN,cAAM,MAAM,OAAO,QAAQ;AAE3B,kBAAU,SAAS,MAAM,OAAO,QAAQ,IAAI,kBAAkB,MAAM,OAAO,SAAS,IAAI,MAAM,OAAO,SAAS;AAE9G,eAAO,MAAM,KAAK;AAChB,eAAK,MAAM,IAAI,WAAW,GAAG;AAE7B,cAAI,OAAO,GAAM;AACf,sBAAU,KAAK,SAAS,MAAM,QAAQ,QAAQ,KAAK;AAAA,UACrD,WAAW,OAAO,IAAM;AACtB;AAAA,UACF,OAAO;AACL;AAAA,UACF;AAEA;AAAA,QACF;AAEA,uBAAe;AAEf,YAAI,gBAAgB,KAAK;AAEvB,8BAAoB;AAAA,QACtB,OAAO;AACL,8BAAoB,SAAS;AAAA,QAC/B;AAIA,YAAI,oBAAoB,GAAG;AAAE,8BAAoB;AAAA,QAAG;AAIpD,iBAAS,UAAU;AAGnB,gBAAe,MAAM,KAAK,kBAAkB,MAAM,CAAC;AACnD,cAAM,SAAS,OAAO,aAAa,cAAc;AACjD,cAAM,MAAS,YAAY,CAAE,WAAW,CAAE;AAC1C,YAAI,WAAW;AACb,gBAAM,OAAO,MAAM,IAAI,MAAM,OAAO,iBAAiB,CAAC;AAAA,QACxD;AAGA,mBAAW,MAAM;AACjB,oBAAY,MAAM,OAAO,SAAS;AAClC,oBAAY,MAAM,OAAO,SAAS;AAMlC,wBAAgB,MAAM;AACtB,cAAM,aAAa,MAAM;AACzB,cAAM,YAAY;AAElB,cAAM,QAAQ;AACd,cAAM,OAAO,SAAS,IAAI,eAAe,MAAM,OAAO,SAAS;AAC/D,cAAM,OAAO,SAAS,IAAI;AAE1B,YAAI,gBAAgB,OAAO,MAAM,QAAQ,YAAY,CAAC,GAAG;AAQvD,gBAAM,OAAO,KAAK,IAAI,MAAM,OAAO,GAAG,OAAO;AAAA,QAC/C,OAAO;AACL,gBAAM,GAAG,MAAM,SAAS,OAAO,WAAW,SAAS,IAAI;AAAA,QACzD;AAGA,YAAI,CAAC,MAAM,SAAS,cAAc;AAChC,kBAAQ;AAAA,QACV;AAGA,uBAAgB,MAAM,OAAO,YAAa,KAAK,MAAM,QAAQ,MAAM,OAAO,CAAC;AAE3E,cAAM,YAAY,MAAM;AACxB,cAAM,aAAa;AACnB,cAAM,OAAO,SAAS,IAAI;AAC1B,cAAM,OAAO,SAAS,IAAI;AAC1B,cAAM,QAAQ;AAEd,gBAAe,MAAM,KAAK,mBAAmB,MAAM,EAAE;AACrD,cAAM,SAAS,OAAO,aAAa,cAAc;AAEjD,mBAAW,YAAY,MAAM;AAC7B,kBAAU,CAAC,IAAI;AACf,uBAAe,MAAM,OAAO,SAAS;AAErC,YAAI,YAAY,SAAS;AAAE;AAAA,QAAO;AAKlC,YAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,WAAW;AAAE;AAAA,QAAO;AAGvD,YAAI,MAAM,OAAO,SAAS,IAAI,MAAM,aAAa,GAAG;AAAE;AAAA,QAAO;AAG7D,oBAAY;AACZ,aAAK,IAAI,GAAG,IAAI,gBAAgB,QAAQ,IAAI,GAAG,KAAK;AAClD,cAAI,gBAAgB,CAAC,EAAE,OAAO,UAAU,SAAS,IAAI,GAAG;AACtD,wBAAY;AACZ;AAAA,UACF;AAAA,QACF;AACA,YAAI,WAAW;AAAE;AAAA,QAAO;AAGxB,YAAI,WAAW;AACb,2BAAiB,sBAAsB,OAAO,QAAQ;AACtD,cAAI,iBAAiB,GAAG;AAAE;AAAA,UAAO;AACjC,kBAAQ,MAAM,OAAO,QAAQ,IAAI,MAAM,OAAO,QAAQ;AAAA,QACxD,OAAO;AACL,2BAAiB,qBAAqB,OAAO,QAAQ;AACrD,cAAI,iBAAiB,GAAG;AAAE;AAAA,UAAO;AAAA,QACnC;AAEA,YAAI,mBAAmB,MAAM,IAAI,WAAW,iBAAiB,CAAC,GAAG;AAAE;AAAA,QAAO;AAAA,MAC5E;AAGA,UAAI,WAAW;AACb,gBAAQ,MAAM,KAAK,sBAAsB,MAAM,EAAE;AAAA,MACnD,OAAO;AACL,gBAAQ,MAAM,KAAK,qBAAqB,MAAM,EAAE;AAAA,MAClD;AACA,YAAM,SAAS,OAAO,aAAa,cAAc;AAEjD,gBAAU,CAAC,IAAI;AACf,YAAM,OAAO;AAEb,YAAM,aAAa;AAGnB,UAAI,OAAO;AACT,4BAAoB,OAAO,UAAU;AAAA,MACvC;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;AC3WA;AAAA;AAAA;AAGA,QAAI,qBAAuB,gBAA2B;AACtD,QAAI,UAAuB,gBAA2B;AAGtD,WAAO,UAAU,SAAS,UAAU,OAAO,WAAW,UAAU,QAAQ;AACtE,UAAI,IACA,YACA,eACA,SACA,MACA,GACA,GACA,OACA,UACA,eACA,KACA,OACA,KACA,WACA,iBACA,OACA,QAAQ,GACR,MAAM,MAAM,OAAO,SAAS,IAAI,MAAM,OAAO,SAAS,GACtD,MAAM,MAAM,OAAO,SAAS,GAC5B,WAAW,YAAY;AAG3B,UAAI,MAAM,OAAO,SAAS,IAAI,MAAM,aAAa,GAAG;AAAE,eAAO;AAAA,MAAO;AAEpE,UAAI,MAAM,IAAI,WAAW,GAAG,MAAM,IAAa;AAAE,eAAO;AAAA,MAAO;AAI/D,aAAO,EAAE,MAAM,KAAK;AAClB,YAAI,MAAM,IAAI,WAAW,GAAG,MAAM,MAC9B,MAAM,IAAI,WAAW,MAAM,CAAC,MAAM,IAAa;AACjD,cAAI,MAAM,MAAM,KAAK;AAAE,mBAAO;AAAA,UAAO;AACrC,cAAI,MAAM,IAAI,WAAW,MAAM,CAAC,MAAM,IAAa;AAAE,mBAAO;AAAA,UAAO;AACnE;AAAA,QACF;AAAA,MACF;AAEA,gBAAU,MAAM;AAGhB,wBAAkB,MAAM,GAAG,MAAM,MAAM,SAAS,WAAW;AAE3D,sBAAgB,MAAM;AACtB,YAAM,aAAa;AAEnB,aAAO,WAAW,WAAW,CAAC,MAAM,QAAQ,QAAQ,GAAG,YAAY;AAGjE,YAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,YAAY,GAAG;AAAE;AAAA,QAAU;AAG9D,YAAI,MAAM,OAAO,QAAQ,IAAI,GAAG;AAAE;AAAA,QAAU;AAG5C,oBAAY;AACZ,aAAK,IAAI,GAAG,IAAI,gBAAgB,QAAQ,IAAI,GAAG,KAAK;AAClD,cAAI,gBAAgB,CAAC,EAAE,OAAO,UAAU,SAAS,IAAI,GAAG;AACtD,wBAAY;AACZ;AAAA,UACF;AAAA,QACF;AACA,YAAI,WAAW;AAAE;AAAA,QAAO;AAAA,MAC1B;AAEA,YAAM,MAAM,SAAS,WAAW,UAAU,MAAM,WAAW,KAAK,EAAE,KAAK;AACvE,YAAM,IAAI;AAEV,WAAK,MAAM,GAAG,MAAM,KAAK,OAAO;AAC9B,aAAK,IAAI,WAAW,GAAG;AACvB,YAAI,OAAO,IAAc;AACvB,iBAAO;AAAA,QACT,WAAW,OAAO,IAAc;AAC9B,qBAAW;AACX;AAAA,QACF,WAAW,OAAO,IAAe;AAC/B;AAAA,QACF,WAAW,OAAO,IAAc;AAC9B;AACA,cAAI,MAAM,OAAO,IAAI,WAAW,GAAG,MAAM,IAAM;AAC7C;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,UAAI,WAAW,KAAK,IAAI,WAAW,WAAW,CAAC,MAAM,IAAa;AAAE,eAAO;AAAA,MAAO;AAIlF,WAAK,MAAM,WAAW,GAAG,MAAM,KAAK,OAAO;AACzC,aAAK,IAAI,WAAW,GAAG;AACvB,YAAI,OAAO,IAAM;AACf;AAAA,QACF,WAAW,QAAQ,EAAE,GAAG;AAAA,QAExB,OAAO;AACL;AAAA,QACF;AAAA,MACF;AAIA,YAAM,MAAM,GAAG,QAAQ,qBAAqB,KAAK,KAAK,GAAG;AACzD,UAAI,CAAC,IAAI,IAAI;AAAE,eAAO;AAAA,MAAO;AAE7B,aAAO,MAAM,GAAG,cAAc,IAAI,GAAG;AACrC,UAAI,CAAC,MAAM,GAAG,aAAa,IAAI,GAAG;AAAE,eAAO;AAAA,MAAO;AAElD,YAAM,IAAI;AACV,eAAS,IAAI;AAGb,mBAAa;AACb,sBAAgB;AAIhB,cAAQ;AACR,aAAO,MAAM,KAAK,OAAO;AACvB,aAAK,IAAI,WAAW,GAAG;AACvB,YAAI,OAAO,IAAM;AACf;AAAA,QACF,WAAW,QAAQ,EAAE,GAAG;AAAA,QAExB,OAAO;AACL;AAAA,QACF;AAAA,MACF;AAIA,YAAM,MAAM,GAAG,QAAQ,eAAe,KAAK,KAAK,GAAG;AACnD,UAAI,MAAM,OAAO,UAAU,OAAO,IAAI,IAAI;AACxC,gBAAQ,IAAI;AACZ,cAAM,IAAI;AACV,iBAAS,IAAI;AAAA,MACf,OAAO;AACL,gBAAQ;AACR,cAAM;AACN,gBAAQ;AAAA,MACV;AAGA,aAAO,MAAM,KAAK;AAChB,aAAK,IAAI,WAAW,GAAG;AACvB,YAAI,CAAC,QAAQ,EAAE,GAAG;AAAE;AAAA,QAAO;AAC3B;AAAA,MACF;AAEA,UAAI,MAAM,OAAO,IAAI,WAAW,GAAG,MAAM,IAAM;AAC7C,YAAI,OAAO;AAGT,kBAAQ;AACR,gBAAM;AACN,kBAAQ;AACR,iBAAO,MAAM,KAAK;AAChB,iBAAK,IAAI,WAAW,GAAG;AACvB,gBAAI,CAAC,QAAQ,EAAE,GAAG;AAAE;AAAA,YAAO;AAC3B;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,UAAI,MAAM,OAAO,IAAI,WAAW,GAAG,MAAM,IAAM;AAE7C,eAAO;AAAA,MACT;AAEA,cAAQ,mBAAmB,IAAI,MAAM,GAAG,QAAQ,CAAC;AACjD,UAAI,CAAC,OAAO;AAEV,eAAO;AAAA,MACT;AAIA,UAAI,QAAQ;AAAE,eAAO;AAAA,MAAM;AAE3B,UAAI,OAAO,MAAM,IAAI,eAAe,aAAa;AAC/C,cAAM,IAAI,aAAa,CAAC;AAAA,MAC1B;AACA,UAAI,OAAO,MAAM,IAAI,WAAW,KAAK,MAAM,aAAa;AACtD,cAAM,IAAI,WAAW,KAAK,IAAI,EAAE,OAAc,KAAW;AAAA,MAC3D;AAEA,YAAM,aAAa;AAEnB,YAAM,OAAO,YAAY,QAAQ;AACjC,aAAO;AAAA,IACT;AAAA;AAAA;;;ACrMA;AAAA;AAAA;AAMA,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;ACrEA;AAAA;AAAA;AAIA,QAAI,YAAgB;AAEpB,QAAI,WAAgB;AACpB,QAAI,gBAAgB;AACpB,QAAI,gBAAgB;AAEpB,QAAI,aAAc,QAAQ,WAAW,MAAM,gBAAgB,MAAM,gBAAgB;AAEjF,QAAI,YAAc,YAAY,YAAY,iBAAiB,aAAa;AAExE,QAAI,WAAc,6BAA6B,YAAY;AAE3D,QAAI,YAAc;AAClB,QAAI,UAAc;AAClB,QAAI,aAAc;AAClB,QAAI,cAAc;AAClB,QAAI,QAAc;AAElB,QAAI,cAAc,IAAI,OAAO,SAAS,WAAW,MAAM,YAAY,MAAM,UACjD,MAAM,aAAa,MAAM,cAAc,MAAM,QAAQ,GAAG;AAChF,QAAI,yBAAyB,IAAI,OAAO,SAAS,WAAW,MAAM,YAAY,GAAG;AAEjF,WAAO,QAAQ,cAAc;AAC7B,WAAO,QAAQ,yBAAyB;AAAA;AAAA;;;AC3BxC;AAAA;AAAA;AAKA,QAAI,cAAc;AAClB,QAAI,yBAAyB,kBAA6B;AAK1D,QAAI,iBAAiB;AAAA,MACnB,CAAE,8CAA8C,oCAAoC,IAAK;AAAA,MACzF,CAAE,SAAgB,OAAS,IAAK;AAAA,MAChC,CAAE,QAAgB,OAAS,IAAK;AAAA,MAChC,CAAE,YAAgB,KAAS,IAAK;AAAA,MAChC,CAAE,gBAAgB,SAAS,IAAK;AAAA,MAChC,CAAE,IAAI,OAAO,UAAU,YAAY,KAAK,GAAG,IAAI,oBAAoB,GAAG,GAAG,MAAM,IAAK;AAAA,MACpF,CAAE,IAAI,OAAO,uBAAuB,SAAS,OAAO,GAAI,MAAM,KAAM;AAAA,IACtE;AAGA,WAAO,UAAU,SAAS,WAAW,OAAO,WAAW,SAAS,QAAQ;AACtE,UAAI,GAAG,UAAU,OAAO,UACpB,MAAM,MAAM,OAAO,SAAS,IAAI,MAAM,OAAO,SAAS,GACtD,MAAM,MAAM,OAAO,SAAS;AAGhC,UAAI,MAAM,OAAO,SAAS,IAAI,MAAM,aAAa,GAAG;AAAE,eAAO;AAAA,MAAO;AAEpE,UAAI,CAAC,MAAM,GAAG,QAAQ,MAAM;AAAE,eAAO;AAAA,MAAO;AAE5C,UAAI,MAAM,IAAI,WAAW,GAAG,MAAM,IAAa;AAAE,eAAO;AAAA,MAAO;AAE/D,iBAAW,MAAM,IAAI,MAAM,KAAK,GAAG;AAEnC,WAAK,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC1C,YAAI,eAAe,CAAC,EAAE,CAAC,EAAE,KAAK,QAAQ,GAAG;AAAE;AAAA,QAAO;AAAA,MACpD;AAEA,UAAI,MAAM,eAAe,QAAQ;AAAE,eAAO;AAAA,MAAO;AAEjD,UAAI,QAAQ;AAEV,eAAO,eAAe,CAAC,EAAE,CAAC;AAAA,MAC5B;AAEA,iBAAW,YAAY;AAIvB,UAAI,CAAC,eAAe,CAAC,EAAE,CAAC,EAAE,KAAK,QAAQ,GAAG;AACxC,eAAO,WAAW,SAAS,YAAY;AACrC,cAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,WAAW;AAAE;AAAA,UAAO;AAEvD,gBAAM,MAAM,OAAO,QAAQ,IAAI,MAAM,OAAO,QAAQ;AACpD,gBAAM,MAAM,OAAO,QAAQ;AAC3B,qBAAW,MAAM,IAAI,MAAM,KAAK,GAAG;AAEnC,cAAI,eAAe,CAAC,EAAE,CAAC,EAAE,KAAK,QAAQ,GAAG;AACvC,gBAAI,SAAS,WAAW,GAAG;AAAE;AAAA,YAAY;AACzC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,YAAM,OAAO;AAEb,cAAgB,MAAM,KAAK,cAAc,IAAI,CAAC;AAC9C,YAAM,MAAU,CAAE,WAAW,QAAS;AACtC,YAAM,UAAU,MAAM,SAAS,WAAW,UAAU,MAAM,WAAW,IAAI;AAEzE,aAAO;AAAA,IACT;AAAA;AAAA;;;ACzEA;AAAA;AAAA;AAIA,QAAI,UAAU,gBAA2B;AAGzC,WAAO,UAAU,SAAS,QAAQ,OAAO,WAAW,SAAS,QAAQ;AACnE,UAAI,IAAI,OAAO,KAAK,OAChB,MAAM,MAAM,OAAO,SAAS,IAAI,MAAM,OAAO,SAAS,GACtD,MAAM,MAAM,OAAO,SAAS;AAGhC,UAAI,MAAM,OAAO,SAAS,IAAI,MAAM,aAAa,GAAG;AAAE,eAAO;AAAA,MAAO;AAEpE,WAAM,MAAM,IAAI,WAAW,GAAG;AAE9B,UAAI,OAAO,MAAe,OAAO,KAAK;AAAE,eAAO;AAAA,MAAO;AAGtD,cAAQ;AACR,WAAK,MAAM,IAAI,WAAW,EAAE,GAAG;AAC/B,aAAO,OAAO,MAAe,MAAM,OAAO,SAAS,GAAG;AACpD;AACA,aAAK,MAAM,IAAI,WAAW,EAAE,GAAG;AAAA,MACjC;AAEA,UAAI,QAAQ,KAAM,MAAM,OAAO,CAAC,QAAQ,EAAE,GAAI;AAAE,eAAO;AAAA,MAAO;AAE9D,UAAI,QAAQ;AAAE,eAAO;AAAA,MAAM;AAI3B,YAAM,MAAM,eAAe,KAAK,GAAG;AACnC,YAAM,MAAM,cAAc,KAAK,IAAM,GAAG;AACxC,UAAI,MAAM,OAAO,QAAQ,MAAM,IAAI,WAAW,MAAM,CAAC,CAAC,GAAG;AACvD,cAAM;AAAA,MACR;AAEA,YAAM,OAAO,YAAY;AAEzB,cAAe,MAAM,KAAK,gBAAgB,MAAM,OAAO,KAAK,GAAG,CAAC;AAChE,YAAM,SAAS,WAAW,MAAM,GAAG,KAAK;AACxC,YAAM,MAAS,CAAE,WAAW,MAAM,IAAK;AAEvC,cAAiB,MAAM,KAAK,UAAU,IAAI,CAAC;AAC3C,YAAM,UAAW,MAAM,IAAI,MAAM,KAAK,GAAG,EAAE,KAAK;AAChD,YAAM,MAAW,CAAE,WAAW,MAAM,IAAK;AACzC,YAAM,WAAW,CAAC;AAElB,cAAe,MAAM,KAAK,iBAAiB,MAAM,OAAO,KAAK,GAAG,EAAE;AAClE,YAAM,SAAS,WAAW,MAAM,GAAG,KAAK;AAExC,aAAO;AAAA,IACT;AAAA;AAAA;;;ACtDA;AAAA;AAAA;AAKA,WAAO,UAAU,SAAS,SAAS,OAAO,WAAW,SAAqB;AACxE,UAAI,SAAS,WAAW,GAAG,GAAG,OAAO,KAAK,KAAK,OAAO,QAClD,WAAW,YAAY,GAAG,eAC1B,kBAAkB,MAAM,GAAG,MAAM,MAAM,SAAS,WAAW;AAG/D,UAAI,MAAM,OAAO,SAAS,IAAI,MAAM,aAAa,GAAG;AAAE,eAAO;AAAA,MAAO;AAEpE,sBAAgB,MAAM;AACtB,YAAM,aAAa;AAGnB,aAAO,WAAW,WAAW,CAAC,MAAM,QAAQ,QAAQ,GAAG,YAAY;AAGjE,YAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,YAAY,GAAG;AAAE;AAAA,QAAU;AAK9D,YAAI,MAAM,OAAO,QAAQ,KAAK,MAAM,WAAW;AAC7C,gBAAM,MAAM,OAAO,QAAQ,IAAI,MAAM,OAAO,QAAQ;AACpD,gBAAM,MAAM,OAAO,QAAQ;AAE3B,cAAI,MAAM,KAAK;AACb,qBAAS,MAAM,IAAI,WAAW,GAAG;AAEjC,gBAAI,WAAW,MAAe,WAAW,IAAa;AACpD,oBAAM,MAAM,UAAU,KAAK,MAAM;AACjC,oBAAM,MAAM,WAAW,GAAG;AAE1B,kBAAI,OAAO,KAAK;AACd,wBAAS,WAAW,KAAc,IAAI;AACtC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAGA,YAAI,MAAM,OAAO,QAAQ,IAAI,GAAG;AAAE;AAAA,QAAU;AAG5C,oBAAY;AACZ,aAAK,IAAI,GAAG,IAAI,gBAAgB,QAAQ,IAAI,GAAG,KAAK;AAClD,cAAI,gBAAgB,CAAC,EAAE,OAAO,UAAU,SAAS,IAAI,GAAG;AACtD,wBAAY;AACZ;AAAA,UACF;AAAA,QACF;AACA,YAAI,WAAW;AAAE;AAAA,QAAO;AAAA,MAC1B;AAEA,UAAI,CAAC,OAAO;AAEV,eAAO;AAAA,MACT;AAEA,gBAAU,MAAM,SAAS,WAAW,UAAU,MAAM,WAAW,KAAK,EAAE,KAAK;AAE3E,YAAM,OAAO,WAAW;AAExB,cAAiB,MAAM,KAAK,gBAAgB,MAAM,OAAO,KAAK,GAAG,CAAC;AAClE,YAAM,SAAW,OAAO,aAAa,MAAM;AAC3C,YAAM,MAAW,CAAE,WAAW,MAAM,IAAK;AAEzC,cAAiB,MAAM,KAAK,UAAU,IAAI,CAAC;AAC3C,YAAM,UAAW;AACjB,YAAM,MAAW,CAAE,WAAW,MAAM,OAAO,CAAE;AAC7C,YAAM,WAAW,CAAC;AAElB,cAAiB,MAAM,KAAK,iBAAiB,MAAM,OAAO,KAAK,GAAG,EAAE;AACpE,YAAM,SAAW,OAAO,aAAa,MAAM;AAE3C,YAAM,aAAa;AAEnB,aAAO;AAAA,IACT;AAAA;AAAA;;;AClFA;AAAA;AAAA;AAKA,WAAO,UAAU,SAAS,UAAU,OAAO,WAAwB;AACjE,UAAI,SAAS,WAAW,GAAG,GAAG,OAAO,eACjC,WAAW,YAAY,GACvB,kBAAkB,MAAM,GAAG,MAAM,MAAM,SAAS,WAAW,GAC3D,UAAU,MAAM;AAEpB,sBAAgB,MAAM;AACtB,YAAM,aAAa;AAGnB,aAAO,WAAW,WAAW,CAAC,MAAM,QAAQ,QAAQ,GAAG,YAAY;AAGjE,YAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,YAAY,GAAG;AAAE;AAAA,QAAU;AAG9D,YAAI,MAAM,OAAO,QAAQ,IAAI,GAAG;AAAE;AAAA,QAAU;AAG5C,oBAAY;AACZ,aAAK,IAAI,GAAG,IAAI,gBAAgB,QAAQ,IAAI,GAAG,KAAK;AAClD,cAAI,gBAAgB,CAAC,EAAE,OAAO,UAAU,SAAS,IAAI,GAAG;AACtD,wBAAY;AACZ;AAAA,UACF;AAAA,QACF;AACA,YAAI,WAAW;AAAE;AAAA,QAAO;AAAA,MAC1B;AAEA,gBAAU,MAAM,SAAS,WAAW,UAAU,MAAM,WAAW,KAAK,EAAE,KAAK;AAE3E,YAAM,OAAO;AAEb,cAAiB,MAAM,KAAK,kBAAkB,KAAK,CAAC;AACpD,YAAM,MAAW,CAAE,WAAW,MAAM,IAAK;AAEzC,cAAiB,MAAM,KAAK,UAAU,IAAI,CAAC;AAC3C,YAAM,UAAW;AACjB,YAAM,MAAW,CAAE,WAAW,MAAM,IAAK;AACzC,YAAM,WAAW,CAAC;AAElB,cAAiB,MAAM,KAAK,mBAAmB,KAAK,EAAE;AAEtD,YAAM,aAAa;AAEnB,aAAO;AAAA,IACT;AAAA;AAAA;;;ACnDA;AAAA;AAAA;AAIA,QAAI,QAAQ;AACZ,QAAI,UAAU,gBAA2B;AAGzC,aAAS,WAAW,KAAK,IAAI,KAAK,QAAQ;AACxC,UAAI,IAAI,GAAG,OAAO,KAAK,KAAK,QAAQ,QAAQ;AAE5C,WAAK,MAAM;AAGX,WAAK,KAAS;AAEd,WAAK,MAAM;AAMX,WAAK,SAAS;AAEd,WAAK,SAAS,CAAC;AACf,WAAK,SAAS,CAAC;AACf,WAAK,SAAS,CAAC;AACf,WAAK,SAAS,CAAC;AAYf,WAAK,UAAU,CAAC;AAGhB,WAAK,YAAa;AAElB,WAAK,OAAa;AAClB,WAAK,UAAa;AAClB,WAAK,QAAa;AAClB,WAAK,WAAa;AAClB,WAAK,aAAa;AAIlB,WAAK,aAAa;AAElB,WAAK,QAAQ;AAGb,WAAK,SAAS;AAId,UAAI,KAAK;AACT,qBAAe;AAEf,WAAK,QAAQ,MAAM,SAAS,SAAS,GAAG,MAAM,EAAE,QAAQ,MAAM,KAAK,OAAO;AACxE,aAAK,EAAE,WAAW,GAAG;AAErB,YAAI,CAAC,cAAc;AACjB,cAAI,QAAQ,EAAE,GAAG;AACf;AAEA,gBAAI,OAAO,GAAM;AACf,wBAAU,IAAI,SAAS;AAAA,YACzB,OAAO;AACL;AAAA,YACF;AACA;AAAA,UACF,OAAO;AACL,2BAAe;AAAA,UACjB;AAAA,QACF;AAEA,YAAI,OAAO,MAAQ,QAAQ,MAAM,GAAG;AAClC,cAAI,OAAO,IAAM;AAAE;AAAA,UAAO;AAC1B,eAAK,OAAO,KAAK,KAAK;AACtB,eAAK,OAAO,KAAK,GAAG;AACpB,eAAK,OAAO,KAAK,MAAM;AACvB,eAAK,OAAO,KAAK,MAAM;AACvB,eAAK,QAAQ,KAAK,CAAC;AAEnB,yBAAe;AACf,mBAAS;AACT,mBAAS;AACT,kBAAQ,MAAM;AAAA,QAChB;AAAA,MACF;AAGA,WAAK,OAAO,KAAK,EAAE,MAAM;AACzB,WAAK,OAAO,KAAK,EAAE,MAAM;AACzB,WAAK,OAAO,KAAK,CAAC;AAClB,WAAK,OAAO,KAAK,CAAC;AAClB,WAAK,QAAQ,KAAK,CAAC;AAEnB,WAAK,UAAU,KAAK,OAAO,SAAS;AAAA,IACtC;AAIA,eAAW,UAAU,OAAO,SAAU,MAAM,KAAK,SAAS;AACxD,UAAI,QAAQ,IAAI,MAAM,MAAM,KAAK,OAAO;AACxC,YAAM,QAAQ;AAEd,UAAI,UAAU;AAAG,aAAK;AACtB,YAAM,QAAQ,KAAK;AACnB,UAAI,UAAU;AAAG,aAAK;AAEtB,WAAK,OAAO,KAAK,KAAK;AACtB,aAAO;AAAA,IACT;AAEA,eAAW,UAAU,UAAU,SAAS,QAAQ,MAAM;AACpD,aAAO,KAAK,OAAO,IAAI,IAAI,KAAK,OAAO,IAAI,KAAK,KAAK,OAAO,IAAI;AAAA,IAClE;AAEA,eAAW,UAAU,iBAAiB,SAAS,eAAe,MAAM;AAClE,eAAS,MAAM,KAAK,SAAS,OAAO,KAAK,QAAQ;AAC/C,YAAI,KAAK,OAAO,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,OAAO,IAAI,GAAG;AAC7D;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAGA,eAAW,UAAU,aAAa,SAAS,WAAW,KAAK;AACzD,UAAI;AAEJ,eAAS,MAAM,KAAK,IAAI,QAAQ,MAAM,KAAK,OAAO;AAChD,aAAK,KAAK,IAAI,WAAW,GAAG;AAC5B,YAAI,CAAC,QAAQ,EAAE,GAAG;AAAE;AAAA,QAAO;AAAA,MAC7B;AACA,aAAO;AAAA,IACT;AAGA,eAAW,UAAU,iBAAiB,SAAS,eAAe,KAAK,KAAK;AACtE,UAAI,OAAO,KAAK;AAAE,eAAO;AAAA,MAAK;AAE9B,aAAO,MAAM,KAAK;AAChB,YAAI,CAAC,QAAQ,KAAK,IAAI,WAAW,EAAE,GAAG,CAAC,GAAG;AAAE,iBAAO,MAAM;AAAA,QAAG;AAAA,MAC9D;AACA,aAAO;AAAA,IACT;AAGA,eAAW,UAAU,YAAY,SAAS,UAAU,KAAK,MAAM;AAC7D,eAAS,MAAM,KAAK,IAAI,QAAQ,MAAM,KAAK,OAAO;AAChD,YAAI,KAAK,IAAI,WAAW,GAAG,MAAM,MAAM;AAAE;AAAA,QAAO;AAAA,MAClD;AACA,aAAO;AAAA,IACT;AAGA,eAAW,UAAU,gBAAgB,SAAS,cAAc,KAAK,MAAM,KAAK;AAC1E,UAAI,OAAO,KAAK;AAAE,eAAO;AAAA,MAAK;AAE9B,aAAO,MAAM,KAAK;AAChB,YAAI,SAAS,KAAK,IAAI,WAAW,EAAE,GAAG,GAAG;AAAE,iBAAO,MAAM;AAAA,QAAG;AAAA,MAC7D;AACA,aAAO;AAAA,IACT;AAGA,eAAW,UAAU,WAAW,SAAS,SAAS,OAAO,KAAK,QAAQ,YAAY;AAChF,UAAI,GAAG,YAAY,IAAI,OAAO,MAAM,OAAO,WACvC,OAAO;AAEX,UAAI,SAAS,KAAK;AAChB,eAAO;AAAA,MACT;AAEA,cAAQ,IAAI,MAAM,MAAM,KAAK;AAE7B,WAAK,IAAI,GAAG,OAAO,KAAK,QAAQ,KAAK;AACnC,qBAAa;AACb,oBAAY,QAAQ,KAAK,OAAO,IAAI;AAEpC,YAAI,OAAO,IAAI,OAAO,YAAY;AAEhC,iBAAO,KAAK,OAAO,IAAI,IAAI;AAAA,QAC7B,OAAO;AACL,iBAAO,KAAK,OAAO,IAAI;AAAA,QACzB;AAEA,eAAO,QAAQ,QAAQ,aAAa,QAAQ;AAC1C,eAAK,KAAK,IAAI,WAAW,KAAK;AAE9B,cAAI,QAAQ,EAAE,GAAG;AACf,gBAAI,OAAO,GAAM;AACf,4BAAc,KAAK,aAAa,KAAK,QAAQ,IAAI,KAAK;AAAA,YACxD,OAAO;AACL;AAAA,YACF;AAAA,UACF,WAAW,QAAQ,YAAY,KAAK,OAAO,IAAI,GAAG;AAEhD;AAAA,UACF,OAAO;AACL;AAAA,UACF;AAEA;AAAA,QACF;AAEA,YAAI,aAAa,QAAQ;AAGvB,gBAAM,CAAC,IAAI,IAAI,MAAM,aAAa,SAAS,CAAC,EAAE,KAAK,GAAG,IAAI,KAAK,IAAI,MAAM,OAAO,IAAI;AAAA,QACtF,OAAO;AACL,gBAAM,CAAC,IAAI,KAAK,IAAI,MAAM,OAAO,IAAI;AAAA,QACvC;AAAA,MACF;AAEA,aAAO,MAAM,KAAK,EAAE;AAAA,IACtB;AAGA,eAAW,UAAU,QAAQ;AAG7B,WAAO,UAAU;AAAA;AAAA;;;ACtOjB;AAAA;AAAA;AAQA,QAAI,QAAkB;AAGtB,QAAI,SAAS;AAAA;AAAA;AAAA,MAGX,CAAE,SAAc,iBAAqC,CAAE,aAAa,WAAY,CAAE;AAAA,MAClF,CAAE,QAAc,cAA8B;AAAA,MAC9C,CAAE,SAAc,iBAAqC,CAAE,aAAa,aAAa,cAAc,MAAO,CAAE;AAAA,MACxG,CAAE,cAAc,sBAAqC,CAAE,aAAa,aAAa,cAAc,MAAO,CAAE;AAAA,MACxG,CAAE,MAAc,cAAqC,CAAE,aAAa,aAAa,cAAc,MAAO,CAAE;AAAA,MACxG,CAAE,QAAc,gBAAqC,CAAE,aAAa,aAAa,YAAa,CAAE;AAAA,MAChG,CAAE,aAAc,mBAAmC;AAAA,MACnD,CAAE,cAAc,sBAAqC,CAAE,aAAa,aAAa,YAAa,CAAE;AAAA,MAChG,CAAE,WAAc,mBAAqC,CAAE,aAAa,aAAa,YAAa,CAAE;AAAA,MAChG,CAAE,YAAc,kBAAkC;AAAA,MAClD,CAAE,aAAc,mBAAmC;AAAA,IACrD;AAMA,aAAS,cAAc;AAMrB,WAAK,QAAQ,IAAI,MAAM;AAEvB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,aAAK,MAAM,KAAK,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC;AAAA,MACnF;AAAA,IACF;AAKA,gBAAY,UAAU,WAAW,SAAU,OAAO,WAAW,SAAS;AACpE,UAAI,IAAI,GACJ,QAAQ,KAAK,MAAM,SAAS,EAAE,GAC9B,MAAM,MAAM,QACZ,OAAO,WACP,gBAAgB,OAChB,aAAa,MAAM,GAAG,QAAQ;AAElC,aAAO,OAAO,SAAS;AACrB,cAAM,OAAO,OAAO,MAAM,eAAe,IAAI;AAC7C,YAAI,QAAQ,SAAS;AAAE;AAAA,QAAO;AAI9B,YAAI,MAAM,OAAO,IAAI,IAAI,MAAM,WAAW;AAAE;AAAA,QAAO;AAInD,YAAI,MAAM,SAAS,YAAY;AAC7B,gBAAM,OAAO;AACb;AAAA,QACF;AASA,aAAK,IAAI,GAAG,IAAI,KAAK,KAAK;AACxB,eAAK,MAAM,CAAC,EAAE,OAAO,MAAM,SAAS,KAAK;AACzC,cAAI,IAAI;AAAE;AAAA,UAAO;AAAA,QACnB;AAIA,cAAM,QAAQ,CAAC;AAGf,YAAI,MAAM,QAAQ,MAAM,OAAO,CAAC,GAAG;AACjC,0BAAgB;AAAA,QAClB;AAEA,eAAO,MAAM;AAEb,YAAI,OAAO,WAAW,MAAM,QAAQ,IAAI,GAAG;AACzC,0BAAgB;AAChB;AACA,gBAAM,OAAO;AAAA,QACf;AAAA,MACF;AAAA,IACF;AAQA,gBAAY,UAAU,QAAQ,SAAU,KAAK,IAAI,KAAK,WAAW;AAC/D,UAAI;AAEJ,UAAI,CAAC,KAAK;AAAE;AAAA,MAAQ;AAEpB,cAAQ,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,SAAS;AAE9C,WAAK,SAAS,OAAO,MAAM,MAAM,MAAM,OAAO;AAAA,IAChD;AAGA,gBAAY,UAAU,QAAQ;AAG9B,WAAO,UAAU;AAAA;AAAA;;;ACzHjB;AAAA;AAAA;AAaA,aAAS,iBAAiB,IAAI;AAC5B,cAAQ,IAAI;AAAA,QACV,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AAEA,WAAO,UAAU,SAAS,KAAK,OAAO,QAAQ;AAC5C,UAAI,MAAM,MAAM;AAEhB,aAAO,MAAM,MAAM,UAAU,CAAC,iBAAiB,MAAM,IAAI,WAAW,GAAG,CAAC,GAAG;AACzE;AAAA,MACF;AAEA,UAAI,QAAQ,MAAM,KAAK;AAAE,eAAO;AAAA,MAAO;AAEvC,UAAI,CAAC,QAAQ;AAAE,cAAM,WAAW,MAAM,IAAI,MAAM,MAAM,KAAK,GAAG;AAAA,MAAG;AAEjE,YAAM,MAAM;AAEZ,aAAO;AAAA,IACT;AAAA;AAAA;;;AC1DA;AAAA;AAAA;AAIA,QAAI,UAAU,gBAA2B;AAGzC,WAAO,UAAU,SAAS,QAAQ,OAAO,QAAQ;AAC/C,UAAI,MAAM,KAAK,IAAI,MAAM,MAAM;AAE/B,UAAI,MAAM,IAAI,WAAW,GAAG,MAAM,IAAc;AAAE,eAAO;AAAA,MAAO;AAEhE,aAAO,MAAM,QAAQ,SAAS;AAC9B,YAAM,MAAM;AAMZ,UAAI,CAAC,QAAQ;AACX,YAAI,QAAQ,KAAK,MAAM,QAAQ,WAAW,IAAI,MAAM,IAAM;AACxD,cAAI,QAAQ,KAAK,MAAM,QAAQ,WAAW,OAAO,CAAC,MAAM,IAAM;AAE5D,iBAAK,OAAO;AACZ,mBAAO,MAAM,KAAK,MAAM,QAAQ,WAAW,KAAK,CAAC,MAAM;AAAM;AAE7D,kBAAM,UAAU,MAAM,QAAQ,MAAM,GAAG,EAAE;AACzC,kBAAM,KAAK,aAAa,MAAM,CAAC;AAAA,UACjC,OAAO;AACL,kBAAM,UAAU,MAAM,QAAQ,MAAM,GAAG,EAAE;AACzC,kBAAM,KAAK,aAAa,MAAM,CAAC;AAAA,UACjC;AAAA,QAEF,OAAO;AACL,gBAAM,KAAK,aAAa,MAAM,CAAC;AAAA,QACjC;AAAA,MACF;AAEA;AAGA,aAAO,MAAM,OAAO,QAAQ,MAAM,IAAI,WAAW,GAAG,CAAC,GAAG;AAAE;AAAA,MAAO;AAEjE,YAAM,MAAM;AACZ,aAAO;AAAA,IACT;AAAA;AAAA;;;AC7CA;AAAA;AAAA;AAIA,QAAI,UAAU,gBAA2B;AAEzC,QAAI,UAAU,CAAC;AAEf,SAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAAE,cAAQ,KAAK,CAAC;AAAA,IAAG;AAAxC;AAET,yCACG,MAAM,EAAE,EAAE,QAAQ,SAAU,IAAI;AAAE,cAAQ,GAAG,WAAW,CAAC,CAAC,IAAI;AAAA,IAAG,CAAC;AAGrE,WAAO,UAAU,SAAS,OAAO,OAAO,QAAQ;AAC9C,UAAI,IAAI,MAAM,MAAM,KAAK,MAAM,MAAM;AAErC,UAAI,MAAM,IAAI,WAAW,GAAG,MAAM,IAAa;AAAE,eAAO;AAAA,MAAO;AAE/D;AAEA,UAAI,MAAM,KAAK;AACb,aAAK,MAAM,IAAI,WAAW,GAAG;AAE7B,YAAI,KAAK,OAAO,QAAQ,EAAE,MAAM,GAAG;AACjC,cAAI,CAAC,QAAQ;AAAE,kBAAM,WAAW,MAAM,IAAI,GAAG;AAAA,UAAG;AAChD,gBAAM,OAAO;AACb,iBAAO;AAAA,QACT;AAEA,YAAI,OAAO,IAAM;AACf,cAAI,CAAC,QAAQ;AACX,kBAAM,KAAK,aAAa,MAAM,CAAC;AAAA,UACjC;AAEA;AAEA,iBAAO,MAAM,KAAK;AAChB,iBAAK,MAAM,IAAI,WAAW,GAAG;AAC7B,gBAAI,CAAC,QAAQ,EAAE,GAAG;AAAE;AAAA,YAAO;AAC3B;AAAA,UACF;AAEA,gBAAM,MAAM;AACZ,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,UAAI,CAAC,QAAQ;AAAE,cAAM,WAAW;AAAA,MAAM;AACtC,YAAM;AACN,aAAO;AAAA,IACT;AAAA;AAAA;;;ACnDA;AAAA;AAAA;AAKA,WAAO,UAAU,SAAS,SAAS,OAAO,QAAQ;AAChD,UAAI,OAAO,KAAK,QAAQ,OAAO,YAAY,UAAU,cAAc,cAC/D,MAAM,MAAM,KACZ,KAAK,MAAM,IAAI,WAAW,GAAG;AAEjC,UAAI,OAAO,IAAa;AAAE,eAAO;AAAA,MAAO;AAExC,cAAQ;AACR;AACA,YAAM,MAAM;AAGZ,aAAO,MAAM,OAAO,MAAM,IAAI,WAAW,GAAG,MAAM,IAAa;AAAE;AAAA,MAAO;AAExE,eAAS,MAAM,IAAI,MAAM,OAAO,GAAG;AACnC,qBAAe,OAAO;AAEtB,UAAI,MAAM,qBAAqB,MAAM,UAAU,YAAY,KAAK,MAAM,OAAO;AAC3E,YAAI,CAAC;AAAQ,gBAAM,WAAW;AAC9B,cAAM,OAAO;AACb,eAAO;AAAA,MACT;AAEA,mBAAa,WAAW;AAGxB,cAAQ,aAAa,MAAM,IAAI,QAAQ,KAAK,QAAQ,OAAO,IAAI;AAC7D,mBAAW,aAAa;AAGxB,eAAO,WAAW,OAAO,MAAM,IAAI,WAAW,QAAQ,MAAM,IAAa;AAAE;AAAA,QAAY;AAEvF,uBAAe,WAAW;AAE1B,YAAI,iBAAiB,cAAc;AAEjC,cAAI,CAAC,QAAQ;AACX,oBAAY,MAAM,KAAK,eAAe,QAAQ,CAAC;AAC/C,kBAAM,SAAU;AAChB,kBAAM,UAAU,MAAM,IAAI,MAAM,KAAK,UAAU,EAC5C,QAAQ,OAAO,GAAG,EAClB,QAAQ,YAAY,IAAI;AAAA,UAC7B;AACA,gBAAM,MAAM;AACZ,iBAAO;AAAA,QACT;AAGA,cAAM,UAAU,YAAY,IAAI;AAAA,MAClC;AAGA,YAAM,mBAAmB;AAEzB,UAAI,CAAC;AAAQ,cAAM,WAAW;AAC9B,YAAM,OAAO;AACb,aAAO;AAAA,IACT;AAAA;AAAA;;;AC9DA;AAAA;AAAA;AAOA,WAAO,QAAQ,WAAW,SAAS,cAAc,OAAO,QAAQ;AAC9D,UAAI,GAAG,SAAS,OAAO,KAAK,IACxB,QAAQ,MAAM,KACd,SAAS,MAAM,IAAI,WAAW,KAAK;AAEvC,UAAI,QAAQ;AAAE,eAAO;AAAA,MAAO;AAE5B,UAAI,WAAW,KAAa;AAAE,eAAO;AAAA,MAAO;AAE5C,gBAAU,MAAM,WAAW,MAAM,KAAK,IAAI;AAC1C,YAAM,QAAQ;AACd,WAAK,OAAO,aAAa,MAAM;AAE/B,UAAI,MAAM,GAAG;AAAE,eAAO;AAAA,MAAO;AAE7B,UAAI,MAAM,GAAG;AACX,gBAAgB,MAAM,KAAK,QAAQ,IAAI,CAAC;AACxC,cAAM,UAAU;AAChB;AAAA,MACF;AAEA,WAAK,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC3B,gBAAgB,MAAM,KAAK,QAAQ,IAAI,CAAC;AACxC,cAAM,UAAU,KAAK;AAErB,cAAM,WAAW,KAAK;AAAA,UACpB;AAAA,UACA,QAAQ;AAAA;AAAA,UACR,OAAQ,MAAM,OAAO,SAAS;AAAA,UAC9B,KAAQ;AAAA,UACR,MAAQ,QAAQ;AAAA,UAChB,OAAQ,QAAQ;AAAA,QAClB,CAAC;AAAA,MACH;AAEA,YAAM,OAAO,QAAQ;AAErB,aAAO;AAAA,IACT;AAGA,aAAS,YAAY,OAAO,YAAY;AACtC,UAAI,GAAG,GACH,YACA,UACA,OACA,cAAc,CAAC,GACf,MAAM,WAAW;AAErB,WAAK,IAAI,GAAG,IAAI,KAAK,KAAK;AACxB,qBAAa,WAAW,CAAC;AAEzB,YAAI,WAAW,WAAW,KAAa;AACrC;AAAA,QACF;AAEA,YAAI,WAAW,QAAQ,IAAI;AACzB;AAAA,QACF;AAEA,mBAAW,WAAW,WAAW,GAAG;AAEpC,gBAAgB,MAAM,OAAO,WAAW,KAAK;AAC7C,cAAM,OAAU;AAChB,cAAM,MAAU;AAChB,cAAM,UAAU;AAChB,cAAM,SAAU;AAChB,cAAM,UAAU;AAEhB,gBAAgB,MAAM,OAAO,SAAS,KAAK;AAC3C,cAAM,OAAU;AAChB,cAAM,MAAU;AAChB,cAAM,UAAU;AAChB,cAAM,SAAU;AAChB,cAAM,UAAU;AAEhB,YAAI,MAAM,OAAO,SAAS,QAAQ,CAAC,EAAE,SAAS,UAC1C,MAAM,OAAO,SAAS,QAAQ,CAAC,EAAE,YAAY,KAAK;AAEpD,sBAAY,KAAK,SAAS,QAAQ,CAAC;AAAA,QACrC;AAAA,MACF;AAQA,aAAO,YAAY,QAAQ;AACzB,YAAI,YAAY,IAAI;AACpB,YAAI,IAAI;AAER,eAAO,IAAI,MAAM,OAAO,UAAU,MAAM,OAAO,CAAC,EAAE,SAAS,WAAW;AACpE;AAAA,QACF;AAEA;AAEA,YAAI,MAAM,GAAG;AACX,kBAAQ,MAAM,OAAO,CAAC;AACtB,gBAAM,OAAO,CAAC,IAAI,MAAM,OAAO,CAAC;AAChC,gBAAM,OAAO,CAAC,IAAI;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAKA,WAAO,QAAQ,cAAc,SAAS,cAAc,OAAO;AACzD,UAAI,MACA,cAAc,MAAM,aACpB,MAAM,MAAM,YAAY;AAE5B,kBAAY,OAAO,MAAM,UAAU;AAEnC,WAAK,OAAO,GAAG,OAAO,KAAK,QAAQ;AACjC,YAAI,YAAY,IAAI,KAAK,YAAY,IAAI,EAAE,YAAY;AACrD,sBAAY,OAAO,YAAY,IAAI,EAAE,UAAU;AAAA,QACjD;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;ACjIA;AAAA;AAAA;AAOA,WAAO,QAAQ,WAAW,SAAS,SAAS,OAAO,QAAQ;AACzD,UAAI,GAAG,SAAS,OACZ,QAAQ,MAAM,KACd,SAAS,MAAM,IAAI,WAAW,KAAK;AAEvC,UAAI,QAAQ;AAAE,eAAO;AAAA,MAAO;AAE5B,UAAI,WAAW,MAAgB,WAAW,IAAc;AAAE,eAAO;AAAA,MAAO;AAExE,gBAAU,MAAM,WAAW,MAAM,KAAK,WAAW,EAAI;AAErD,WAAK,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACnC,gBAAgB,MAAM,KAAK,QAAQ,IAAI,CAAC;AACxC,cAAM,UAAU,OAAO,aAAa,MAAM;AAE1C,cAAM,WAAW,KAAK;AAAA;AAAA;AAAA,UAGpB;AAAA;AAAA;AAAA,UAIA,QAAQ,QAAQ;AAAA;AAAA;AAAA,UAIhB,OAAQ,MAAM,OAAO,SAAS;AAAA;AAAA;AAAA;AAAA,UAK9B,KAAQ;AAAA;AAAA;AAAA;AAAA,UAKR,MAAQ,QAAQ;AAAA,UAChB,OAAQ,QAAQ;AAAA,QAClB,CAAC;AAAA,MACH;AAEA,YAAM,OAAO,QAAQ;AAErB,aAAO;AAAA,IACT;AAGA,aAAS,YAAY,OAAO,YAAY;AACtC,UAAI,GACA,YACA,UACA,OACA,IACA,UACA,MAAM,WAAW;AAErB,WAAK,IAAI,MAAM,GAAG,KAAK,GAAG,KAAK;AAC7B,qBAAa,WAAW,CAAC;AAEzB,YAAI,WAAW,WAAW,MAAe,WAAW,WAAW,IAAa;AAC1E;AAAA,QACF;AAGA,YAAI,WAAW,QAAQ,IAAI;AACzB;AAAA,QACF;AAEA,mBAAW,WAAW,WAAW,GAAG;AAOpC,mBAAW,IAAI,KACJ,WAAW,IAAI,CAAC,EAAE,QAAQ,WAAW,MAAM;AAAA,QAE3C,WAAW,IAAI,CAAC,EAAE,WAAW,WAAW,UACxC,WAAW,IAAI,CAAC,EAAE,UAAU,WAAW,QAAQ;AAAA,QAE/C,WAAW,WAAW,MAAM,CAAC,EAAE,UAAU,SAAS,QAAQ;AAErE,aAAK,OAAO,aAAa,WAAW,MAAM;AAE1C,gBAAgB,MAAM,OAAO,WAAW,KAAK;AAC7C,cAAM,OAAU,WAAW,gBAAgB;AAC3C,cAAM,MAAU,WAAW,WAAW;AACtC,cAAM,UAAU;AAChB,cAAM,SAAU,WAAW,KAAK,KAAK;AACrC,cAAM,UAAU;AAEhB,gBAAgB,MAAM,OAAO,SAAS,KAAK;AAC3C,cAAM,OAAU,WAAW,iBAAiB;AAC5C,cAAM,MAAU,WAAW,WAAW;AACtC,cAAM,UAAU;AAChB,cAAM,SAAU,WAAW,KAAK,KAAK;AACrC,cAAM,UAAU;AAEhB,YAAI,UAAU;AACZ,gBAAM,OAAO,WAAW,IAAI,CAAC,EAAE,KAAK,EAAE,UAAU;AAChD,gBAAM,OAAO,WAAW,WAAW,MAAM,CAAC,EAAE,KAAK,EAAE,UAAU;AAC7D;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAKA,WAAO,QAAQ,cAAc,SAAS,SAAS,OAAO;AACpD,UAAI,MACA,cAAc,MAAM,aACpB,MAAM,MAAM,YAAY;AAE5B,kBAAY,OAAO,MAAM,UAAU;AAEnC,WAAK,OAAO,GAAG,OAAO,KAAK,QAAQ;AACjC,YAAI,YAAY,IAAI,KAAK,YAAY,IAAI,EAAE,YAAY;AACrD,sBAAY,OAAO,YAAY,IAAI,EAAE,UAAU;AAAA,QACjD;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;ACjIA;AAAA;AAAA;AAIA,QAAI,qBAAuB,gBAA2B;AACtD,QAAI,UAAuB,gBAA2B;AAGtD,WAAO,UAAU,SAAS,KAAK,OAAO,QAAQ;AAC5C,UAAI,OACA,MACA,OACA,UACA,YACA,KACA,KACA,KACA,OACA,OAAO,IACP,QAAQ,IACR,SAAS,MAAM,KACf,MAAM,MAAM,QACZ,QAAQ,MAAM,KACd,iBAAiB;AAErB,UAAI,MAAM,IAAI,WAAW,MAAM,GAAG,MAAM,IAAa;AAAE,eAAO;AAAA,MAAO;AAErE,mBAAa,MAAM,MAAM;AACzB,iBAAW,MAAM,GAAG,QAAQ,eAAe,OAAO,MAAM,KAAK,IAAI;AAGjE,UAAI,WAAW,GAAG;AAAE,eAAO;AAAA,MAAO;AAElC,YAAM,WAAW;AACjB,UAAI,MAAM,OAAO,MAAM,IAAI,WAAW,GAAG,MAAM,IAAa;AAM1D,yBAAiB;AAIjB;AACA,eAAO,MAAM,KAAK,OAAO;AACvB,iBAAO,MAAM,IAAI,WAAW,GAAG;AAC/B,cAAI,CAAC,QAAQ,IAAI,KAAK,SAAS,IAAM;AAAE;AAAA,UAAO;AAAA,QAChD;AACA,YAAI,OAAO,KAAK;AAAE,iBAAO;AAAA,QAAO;AAIhC,gBAAQ;AACR,cAAM,MAAM,GAAG,QAAQ,qBAAqB,MAAM,KAAK,KAAK,MAAM,MAAM;AACxE,YAAI,IAAI,IAAI;AACV,iBAAO,MAAM,GAAG,cAAc,IAAI,GAAG;AACrC,cAAI,MAAM,GAAG,aAAa,IAAI,GAAG;AAC/B,kBAAM,IAAI;AAAA,UACZ,OAAO;AACL,mBAAO;AAAA,UACT;AAIA,kBAAQ;AACR,iBAAO,MAAM,KAAK,OAAO;AACvB,mBAAO,MAAM,IAAI,WAAW,GAAG;AAC/B,gBAAI,CAAC,QAAQ,IAAI,KAAK,SAAS,IAAM;AAAE;AAAA,YAAO;AAAA,UAChD;AAIA,gBAAM,MAAM,GAAG,QAAQ,eAAe,MAAM,KAAK,KAAK,MAAM,MAAM;AAClE,cAAI,MAAM,OAAO,UAAU,OAAO,IAAI,IAAI;AACxC,oBAAQ,IAAI;AACZ,kBAAM,IAAI;AAIV,mBAAO,MAAM,KAAK,OAAO;AACvB,qBAAO,MAAM,IAAI,WAAW,GAAG;AAC/B,kBAAI,CAAC,QAAQ,IAAI,KAAK,SAAS,IAAM;AAAE;AAAA,cAAO;AAAA,YAChD;AAAA,UACF;AAAA,QACF;AAEA,YAAI,OAAO,OAAO,MAAM,IAAI,WAAW,GAAG,MAAM,IAAa;AAE3D,2BAAiB;AAAA,QACnB;AACA;AAAA,MACF;AAEA,UAAI,gBAAgB;AAIlB,YAAI,OAAO,MAAM,IAAI,eAAe,aAAa;AAAE,iBAAO;AAAA,QAAO;AAEjE,YAAI,MAAM,OAAO,MAAM,IAAI,WAAW,GAAG,MAAM,IAAa;AAC1D,kBAAQ,MAAM;AACd,gBAAM,MAAM,GAAG,QAAQ,eAAe,OAAO,GAAG;AAChD,cAAI,OAAO,GAAG;AACZ,oBAAQ,MAAM,IAAI,MAAM,OAAO,KAAK;AAAA,UACtC,OAAO;AACL,kBAAM,WAAW;AAAA,UACnB;AAAA,QACF,OAAO;AACL,gBAAM,WAAW;AAAA,QACnB;AAIA,YAAI,CAAC,OAAO;AAAE,kBAAQ,MAAM,IAAI,MAAM,YAAY,QAAQ;AAAA,QAAG;AAE7D,cAAM,MAAM,IAAI,WAAW,mBAAmB,KAAK,CAAC;AACpD,YAAI,CAAC,KAAK;AACR,gBAAM,MAAM;AACZ,iBAAO;AAAA,QACT;AACA,eAAO,IAAI;AACX,gBAAQ,IAAI;AAAA,MACd;AAMA,UAAI,CAAC,QAAQ;AACX,cAAM,MAAM;AACZ,cAAM,SAAS;AAEf,gBAAe,MAAM,KAAK,aAAa,KAAK,CAAC;AAC7C,cAAM,QAAS,QAAQ,CAAE,CAAE,QAAQ,IAAK,CAAE;AAC1C,YAAI,OAAO;AACT,gBAAM,KAAK,CAAE,SAAS,KAAM,CAAC;AAAA,QAC/B;AAEA,cAAM,GAAG,OAAO,SAAS,KAAK;AAE9B,gBAAe,MAAM,KAAK,cAAc,KAAK,EAAE;AAAA,MACjD;AAEA,YAAM,MAAM;AACZ,YAAM,SAAS;AACf,aAAO;AAAA,IACT;AAAA;AAAA;;;ACnJA;AAAA;AAAA;AAIA,QAAI,qBAAuB,gBAA2B;AACtD,QAAI,UAAuB,gBAA2B;AAGtD,WAAO,UAAU,SAAS,MAAM,OAAO,QAAQ;AAC7C,UAAI,OACA,MACA,SACA,OACA,UACA,YACA,KACA,KACA,KACA,OACA,OACA,QACA,OACA,OAAO,IACP,SAAS,MAAM,KACf,MAAM,MAAM;AAEhB,UAAI,MAAM,IAAI,WAAW,MAAM,GAAG,MAAM,IAAa;AAAE,eAAO;AAAA,MAAO;AACrE,UAAI,MAAM,IAAI,WAAW,MAAM,MAAM,CAAC,MAAM,IAAa;AAAE,eAAO;AAAA,MAAO;AAEzE,mBAAa,MAAM,MAAM;AACzB,iBAAW,MAAM,GAAG,QAAQ,eAAe,OAAO,MAAM,MAAM,GAAG,KAAK;AAGtE,UAAI,WAAW,GAAG;AAAE,eAAO;AAAA,MAAO;AAElC,YAAM,WAAW;AACjB,UAAI,MAAM,OAAO,MAAM,IAAI,WAAW,GAAG,MAAM,IAAa;AAO1D;AACA,eAAO,MAAM,KAAK,OAAO;AACvB,iBAAO,MAAM,IAAI,WAAW,GAAG;AAC/B,cAAI,CAAC,QAAQ,IAAI,KAAK,SAAS,IAAM;AAAE;AAAA,UAAO;AAAA,QAChD;AACA,YAAI,OAAO,KAAK;AAAE,iBAAO;AAAA,QAAO;AAIhC,gBAAQ;AACR,cAAM,MAAM,GAAG,QAAQ,qBAAqB,MAAM,KAAK,KAAK,MAAM,MAAM;AACxE,YAAI,IAAI,IAAI;AACV,iBAAO,MAAM,GAAG,cAAc,IAAI,GAAG;AACrC,cAAI,MAAM,GAAG,aAAa,IAAI,GAAG;AAC/B,kBAAM,IAAI;AAAA,UACZ,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF;AAIA,gBAAQ;AACR,eAAO,MAAM,KAAK,OAAO;AACvB,iBAAO,MAAM,IAAI,WAAW,GAAG;AAC/B,cAAI,CAAC,QAAQ,IAAI,KAAK,SAAS,IAAM;AAAE;AAAA,UAAO;AAAA,QAChD;AAIA,cAAM,MAAM,GAAG,QAAQ,eAAe,MAAM,KAAK,KAAK,MAAM,MAAM;AAClE,YAAI,MAAM,OAAO,UAAU,OAAO,IAAI,IAAI;AACxC,kBAAQ,IAAI;AACZ,gBAAM,IAAI;AAIV,iBAAO,MAAM,KAAK,OAAO;AACvB,mBAAO,MAAM,IAAI,WAAW,GAAG;AAC/B,gBAAI,CAAC,QAAQ,IAAI,KAAK,SAAS,IAAM;AAAE;AAAA,YAAO;AAAA,UAChD;AAAA,QACF,OAAO;AACL,kBAAQ;AAAA,QACV;AAEA,YAAI,OAAO,OAAO,MAAM,IAAI,WAAW,GAAG,MAAM,IAAa;AAC3D,gBAAM,MAAM;AACZ,iBAAO;AAAA,QACT;AACA;AAAA,MACF,OAAO;AAIL,YAAI,OAAO,MAAM,IAAI,eAAe,aAAa;AAAE,iBAAO;AAAA,QAAO;AAEjE,YAAI,MAAM,OAAO,MAAM,IAAI,WAAW,GAAG,MAAM,IAAa;AAC1D,kBAAQ,MAAM;AACd,gBAAM,MAAM,GAAG,QAAQ,eAAe,OAAO,GAAG;AAChD,cAAI,OAAO,GAAG;AACZ,oBAAQ,MAAM,IAAI,MAAM,OAAO,KAAK;AAAA,UACtC,OAAO;AACL,kBAAM,WAAW;AAAA,UACnB;AAAA,QACF,OAAO;AACL,gBAAM,WAAW;AAAA,QACnB;AAIA,YAAI,CAAC,OAAO;AAAE,kBAAQ,MAAM,IAAI,MAAM,YAAY,QAAQ;AAAA,QAAG;AAE7D,cAAM,MAAM,IAAI,WAAW,mBAAmB,KAAK,CAAC;AACpD,YAAI,CAAC,KAAK;AACR,gBAAM,MAAM;AACZ,iBAAO;AAAA,QACT;AACA,eAAO,IAAI;AACX,gBAAQ,IAAI;AAAA,MACd;AAMA,UAAI,CAAC,QAAQ;AACX,kBAAU,MAAM,IAAI,MAAM,YAAY,QAAQ;AAE9C,cAAM,GAAG,OAAO;AAAA,UACd;AAAA,UACA,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS,CAAC;AAAA,QACZ;AAEA,gBAAiB,MAAM,KAAK,SAAS,OAAO,CAAC;AAC7C,cAAM,QAAW,QAAQ,CAAE,CAAE,OAAO,IAAK,GAAG,CAAE,OAAO,EAAG,CAAE;AAC1D,cAAM,WAAW;AACjB,cAAM,UAAW;AAEjB,YAAI,OAAO;AACT,gBAAM,KAAK,CAAE,SAAS,KAAM,CAAC;AAAA,QAC/B;AAAA,MACF;AAEA,YAAM,MAAM;AACZ,YAAM,SAAS;AACf,aAAO;AAAA,IACT;AAAA;AAAA;;;ACvJA;AAAA;AAAA;AAMA,QAAI,WAAc;AAClB,QAAI,cAAc;AAGlB,WAAO,UAAU,SAAS,SAAS,OAAO,QAAQ;AAChD,UAAI,KAAK,SAAS,OAAO,IAAI,OAAO,KAChC,MAAM,MAAM;AAEhB,UAAI,MAAM,IAAI,WAAW,GAAG,MAAM,IAAa;AAAE,eAAO;AAAA,MAAO;AAE/D,cAAQ,MAAM;AACd,YAAM,MAAM;AAEZ,iBAAS;AACP,YAAI,EAAE,OAAO;AAAK,iBAAO;AAEzB,aAAK,MAAM,IAAI,WAAW,GAAG;AAE7B,YAAI,OAAO;AAAc,iBAAO;AAChC,YAAI,OAAO;AAAc;AAAA,MAC3B;AAEA,YAAM,MAAM,IAAI,MAAM,QAAQ,GAAG,GAAG;AAEpC,UAAI,YAAY,KAAK,GAAG,GAAG;AACzB,kBAAU,MAAM,GAAG,cAAc,GAAG;AACpC,YAAI,CAAC,MAAM,GAAG,aAAa,OAAO,GAAG;AAAE,iBAAO;AAAA,QAAO;AAErD,YAAI,CAAC,QAAQ;AACX,kBAAgB,MAAM,KAAK,aAAa,KAAK,CAAC;AAC9C,gBAAM,QAAU,CAAE,CAAE,QAAQ,OAAQ,CAAE;AACtC,gBAAM,SAAU;AAChB,gBAAM,OAAU;AAEhB,kBAAgB,MAAM,KAAK,QAAQ,IAAI,CAAC;AACxC,gBAAM,UAAU,MAAM,GAAG,kBAAkB,GAAG;AAE9C,kBAAgB,MAAM,KAAK,cAAc,KAAK,EAAE;AAChD,gBAAM,SAAU;AAChB,gBAAM,OAAU;AAAA,QAClB;AAEA,cAAM,OAAO,IAAI,SAAS;AAC1B,eAAO;AAAA,MACT;AAEA,UAAI,SAAS,KAAK,GAAG,GAAG;AACtB,kBAAU,MAAM,GAAG,cAAc,YAAY,GAAG;AAChD,YAAI,CAAC,MAAM,GAAG,aAAa,OAAO,GAAG;AAAE,iBAAO;AAAA,QAAO;AAErD,YAAI,CAAC,QAAQ;AACX,kBAAgB,MAAM,KAAK,aAAa,KAAK,CAAC;AAC9C,gBAAM,QAAU,CAAE,CAAE,QAAQ,OAAQ,CAAE;AACtC,gBAAM,SAAU;AAChB,gBAAM,OAAU;AAEhB,kBAAgB,MAAM,KAAK,QAAQ,IAAI,CAAC;AACxC,gBAAM,UAAU,MAAM,GAAG,kBAAkB,GAAG;AAE9C,kBAAgB,MAAM,KAAK,cAAc,KAAK,EAAE;AAChD,gBAAM,SAAU;AAChB,gBAAM,OAAU;AAAA,QAClB;AAEA,cAAM,OAAO,IAAI,SAAS;AAC1B,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;AC3EA;AAAA;AAAA;AAKA,QAAI,cAAc,kBAA6B;AAG/C,aAAS,SAAS,IAAI;AAEpB,UAAI,KAAK,KAAK;AACd,aAAQ,MAAM,MAAiB,MAAM;AAAA,IACvC;AAGA,WAAO,UAAU,SAAS,YAAY,OAAO,QAAQ;AACnD,UAAI,IAAI,OAAO,KAAK,OAChB,MAAM,MAAM;AAEhB,UAAI,CAAC,MAAM,GAAG,QAAQ,MAAM;AAAE,eAAO;AAAA,MAAO;AAG5C,YAAM,MAAM;AACZ,UAAI,MAAM,IAAI,WAAW,GAAG,MAAM,MAC9B,MAAM,KAAK,KAAK;AAClB,eAAO;AAAA,MACT;AAGA,WAAK,MAAM,IAAI,WAAW,MAAM,CAAC;AACjC,UAAI,OAAO,MACP,OAAO,MACP,OAAO,MACP,CAAC,SAAS,EAAE,GAAG;AACjB,eAAO;AAAA,MACT;AAEA,cAAQ,MAAM,IAAI,MAAM,GAAG,EAAE,MAAM,WAAW;AAC9C,UAAI,CAAC,OAAO;AAAE,eAAO;AAAA,MAAO;AAE5B,UAAI,CAAC,QAAQ;AACX,gBAAgB,MAAM,KAAK,eAAe,IAAI,CAAC;AAC/C,cAAM,UAAU,MAAM,IAAI,MAAM,KAAK,MAAM,MAAM,CAAC,EAAE,MAAM;AAAA,MAC5D;AACA,YAAM,OAAO,MAAM,CAAC,EAAE;AACtB,aAAO;AAAA,IACT;AAAA;AAAA;;;AC9CA;AAAA;AAAA;AAIA,QAAI,WAAoB;AACxB,QAAI,MAAoB,gBAA2B;AACnD,QAAI,oBAAoB,gBAA2B;AACnD,QAAI,gBAAoB,gBAA2B;AAGnD,QAAI,aAAa;AACjB,QAAI,WAAa;AAGjB,WAAO,UAAU,SAAS,OAAO,OAAO,QAAQ;AAC9C,UAAI,IAAI,MAAM,OAAO,MAAM,MAAM,KAAK,MAAM,MAAM;AAElD,UAAI,MAAM,IAAI,WAAW,GAAG,MAAM,IAAa;AAAE,eAAO;AAAA,MAAO;AAE/D,UAAI,MAAM,IAAI,KAAK;AACjB,aAAK,MAAM,IAAI,WAAW,MAAM,CAAC;AAEjC,YAAI,OAAO,IAAc;AACvB,kBAAQ,MAAM,IAAI,MAAM,GAAG,EAAE,MAAM,UAAU;AAC7C,cAAI,OAAO;AACT,gBAAI,CAAC,QAAQ;AACX,qBAAO,MAAM,CAAC,EAAE,CAAC,EAAE,YAAY,MAAM,MAAM,SAAS,MAAM,CAAC,EAAE,MAAM,CAAC,GAAG,EAAE,IAAI,SAAS,MAAM,CAAC,GAAG,EAAE;AAClG,oBAAM,WAAW,kBAAkB,IAAI,IAAI,cAAc,IAAI,IAAI,cAAc,KAAM;AAAA,YACvF;AACA,kBAAM,OAAO,MAAM,CAAC,EAAE;AACtB,mBAAO;AAAA,UACT;AAAA,QACF,OAAO;AACL,kBAAQ,MAAM,IAAI,MAAM,GAAG,EAAE,MAAM,QAAQ;AAC3C,cAAI,OAAO;AACT,gBAAI,IAAI,UAAU,MAAM,CAAC,CAAC,GAAG;AAC3B,kBAAI,CAAC,QAAQ;AAAE,sBAAM,WAAW,SAAS,MAAM,CAAC,CAAC;AAAA,cAAG;AACpD,oBAAM,OAAO,MAAM,CAAC,EAAE;AACtB,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,UAAI,CAAC,QAAQ;AAAE,cAAM,WAAW;AAAA,MAAK;AACrC,YAAM;AACN,aAAO;AAAA,IACT;AAAA;AAAA;;;AC/CA;AAAA;AAAA;AAKA,aAAS,kBAAkB,OAAO,YAAY;AAC5C,UAAI,WAAW,WAAW,QAAQ,QAAQ,cAAc,iBACpD,YAAY,UACZ,gBAAgB,CAAC,GACjB,MAAM,WAAW;AAErB,UAAI,CAAC;AAAK;AAGV,UAAI,YAAY;AAChB,UAAI,eAAe;AACnB,UAAI,QAAQ,CAAC;AAEb,WAAK,YAAY,GAAG,YAAY,KAAK,aAAa;AAChD,iBAAS,WAAW,SAAS;AAE7B,cAAM,KAAK,CAAC;AAMZ,YAAI,WAAW,SAAS,EAAE,WAAW,OAAO,UAAU,iBAAiB,OAAO,QAAQ,GAAG;AACvF,sBAAY;AAAA,QACd;AAEA,uBAAe,OAAO;AAMtB,eAAO,SAAS,OAAO,UAAU;AAEjC,YAAI,CAAC,OAAO;AAAO;AAMnB,YAAI,CAAC,cAAc,eAAe,OAAO,MAAM,GAAG;AAChD,wBAAc,OAAO,MAAM,IAAI,CAAE,IAAI,IAAI,IAAI,IAAI,IAAI,EAAG;AAAA,QAC1D;AAEA,uBAAe,cAAc,OAAO,MAAM,GAAG,OAAO,OAAO,IAAI,KAAM,OAAO,SAAS,CAAE;AAEvF,oBAAY,YAAY,MAAM,SAAS,IAAI;AAE3C,0BAAkB;AAElB,eAAO,YAAY,cAAc,aAAa,MAAM,SAAS,IAAI,GAAG;AAClE,mBAAS,WAAW,SAAS;AAE7B,cAAI,OAAO,WAAW,OAAO;AAAQ;AAErC,cAAI,OAAO,QAAQ,OAAO,MAAM,GAAG;AAEjC,yBAAa;AASb,gBAAI,OAAO,SAAS,OAAO,MAAM;AAC/B,mBAAK,OAAO,SAAS,OAAO,UAAU,MAAM,GAAG;AAC7C,oBAAI,OAAO,SAAS,MAAM,KAAK,OAAO,SAAS,MAAM,GAAG;AACtD,+BAAa;AAAA,gBACf;AAAA,cACF;AAAA,YACF;AAEA,gBAAI,CAAC,YAAY;AAKf,yBAAW,YAAY,KAAK,CAAC,WAAW,YAAY,CAAC,EAAE,OACrD,MAAM,YAAY,CAAC,IAAI,IACvB;AAEF,oBAAM,SAAS,IAAI,YAAY,YAAY;AAC3C,oBAAM,SAAS,IAAI;AAEnB,qBAAO,OAAQ;AACf,qBAAO,MAAQ;AACf,qBAAO,QAAQ;AACf,gCAAkB;AAGlB,6BAAe;AACf;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,YAAI,oBAAoB,IAAI;AAQ1B,wBAAc,OAAO,MAAM,GAAG,OAAO,OAAO,IAAI,MAAO,OAAO,UAAU,KAAK,CAAE,IAAI;AAAA,QACrF;AAAA,MACF;AAAA,IACF;AAGA,WAAO,UAAU,SAAS,WAAW,OAAO;AAC1C,UAAI,MACA,cAAc,MAAM,aACpB,MAAM,MAAM,YAAY;AAE5B,wBAAkB,OAAO,MAAM,UAAU;AAEzC,WAAK,OAAO,GAAG,OAAO,KAAK,QAAQ;AACjC,YAAI,YAAY,IAAI,KAAK,YAAY,IAAI,EAAE,YAAY;AACrD,4BAAkB,OAAO,YAAY,IAAI,EAAE,UAAU;AAAA,QACvD;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;ACjIA;AAAA;AAAA;AAWA,WAAO,UAAU,SAAS,cAAc,OAAO;AAC7C,UAAI,MAAM,MACN,QAAQ,GACR,SAAS,MAAM,QACf,MAAM,MAAM,OAAO;AAEvB,WAAK,OAAO,OAAO,GAAG,OAAO,KAAK,QAAQ;AAGxC,YAAI,OAAO,IAAI,EAAE,UAAU;AAAG;AAC9B,eAAO,IAAI,EAAE,QAAQ;AACrB,YAAI,OAAO,IAAI,EAAE,UAAU;AAAG;AAE9B,YAAI,OAAO,IAAI,EAAE,SAAS,UACtB,OAAO,IAAI,OACX,OAAO,OAAO,CAAC,EAAE,SAAS,QAAQ;AAGpC,iBAAO,OAAO,CAAC,EAAE,UAAU,OAAO,IAAI,EAAE,UAAU,OAAO,OAAO,CAAC,EAAE;AAAA,QACrE,OAAO;AACL,cAAI,SAAS,MAAM;AAAE,mBAAO,IAAI,IAAI,OAAO,IAAI;AAAA,UAAG;AAElD;AAAA,QACF;AAAA,MACF;AAEA,UAAI,SAAS,MAAM;AACjB,eAAO,SAAS;AAAA,MAClB;AAAA,IACF;AAAA;AAAA;;;ACxCA;AAAA;AAAA;AAKA,QAAI,QAAiB;AACrB,QAAI,eAAiB,gBAA2B;AAChD,QAAI,cAAiB,gBAA2B;AAChD,QAAI,iBAAiB,gBAA2B;AAGhD,aAAS,YAAY,KAAK,IAAI,KAAK,WAAW;AAC5C,WAAK,MAAM;AACX,WAAK,MAAM;AACX,WAAK,KAAK;AACV,WAAK,SAAS;AACd,WAAK,cAAc,MAAM,UAAU,MAAM;AAEzC,WAAK,MAAM;AACX,WAAK,SAAS,KAAK,IAAI;AACvB,WAAK,QAAQ;AACb,WAAK,UAAU;AACf,WAAK,eAAe;AAIpB,WAAK,QAAQ,CAAC;AAGd,WAAK,aAAa,CAAC;AAGnB,WAAK,mBAAmB,CAAC;AAGzB,WAAK,YAAY,CAAC;AAClB,WAAK,mBAAmB;AAAA,IAC1B;AAKA,gBAAY,UAAU,cAAc,WAAY;AAC9C,UAAI,QAAQ,IAAI,MAAM,QAAQ,IAAI,CAAC;AACnC,YAAM,UAAU,KAAK;AACrB,YAAM,QAAQ,KAAK;AACnB,WAAK,OAAO,KAAK,KAAK;AACtB,WAAK,UAAU;AACf,aAAO;AAAA,IACT;AAMA,gBAAY,UAAU,OAAO,SAAU,MAAM,KAAK,SAAS;AACzD,UAAI,KAAK,SAAS;AAChB,aAAK,YAAY;AAAA,MACnB;AAEA,UAAI,QAAQ,IAAI,MAAM,MAAM,KAAK,OAAO;AACxC,UAAI,aAAa;AAEjB,UAAI,UAAU,GAAG;AAEf,aAAK;AACL,aAAK,aAAa,KAAK,iBAAiB,IAAI;AAAA,MAC9C;AAEA,YAAM,QAAQ,KAAK;AAEnB,UAAI,UAAU,GAAG;AAEf,aAAK;AACL,aAAK,iBAAiB,KAAK,KAAK,UAAU;AAC1C,aAAK,aAAa,CAAC;AACnB,qBAAa,EAAE,YAAY,KAAK,WAAW;AAAA,MAC7C;AAEA,WAAK,eAAe,KAAK;AACzB,WAAK,OAAO,KAAK,KAAK;AACtB,WAAK,YAAY,KAAK,UAAU;AAChC,aAAO;AAAA,IACT;AASA,gBAAY,UAAU,aAAa,SAAU,OAAO,cAAc;AAChE,UAAI,MAAM,OAAO,UAAU,UAAU,OAAO,UAAU,WAClD,kBAAkB,iBAClB,kBAAkB,iBAClB,gBAAgB,MAChB,iBAAiB,MACjB,MAAM,KAAK,QACX,SAAS,KAAK,IAAI,WAAW,KAAK;AAGtC,iBAAW,QAAQ,IAAI,KAAK,IAAI,WAAW,QAAQ,CAAC,IAAI;AAExD,aAAO,MAAM,OAAO,KAAK,IAAI,WAAW,GAAG,MAAM,QAAQ;AAAE;AAAA,MAAO;AAElE,cAAQ,MAAM;AAGd,iBAAW,MAAM,MAAM,KAAK,IAAI,WAAW,GAAG,IAAI;AAElD,wBAAkB,eAAe,QAAQ,KAAK,YAAY,OAAO,aAAa,QAAQ,CAAC;AACvF,wBAAkB,eAAe,QAAQ,KAAK,YAAY,OAAO,aAAa,QAAQ,CAAC;AAEvF,yBAAmB,aAAa,QAAQ;AACxC,yBAAmB,aAAa,QAAQ;AAExC,UAAI,kBAAkB;AACpB,wBAAgB;AAAA,MAClB,WAAW,iBAAiB;AAC1B,YAAI,EAAE,oBAAoB,kBAAkB;AAC1C,0BAAgB;AAAA,QAClB;AAAA,MACF;AAEA,UAAI,kBAAkB;AACpB,yBAAiB;AAAA,MACnB,WAAW,iBAAiB;AAC1B,YAAI,EAAE,oBAAoB,kBAAkB;AAC1C,2BAAiB;AAAA,QACnB;AAAA,MACF;AAEA,UAAI,CAAC,cAAc;AACjB,mBAAY,kBAAmB,CAAC,kBAAkB;AAClD,oBAAY,mBAAmB,CAAC,iBAAkB;AAAA,MACpD,OAAO;AACL,mBAAY;AACZ,oBAAY;AAAA,MACd;AAEA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,QAAW;AAAA,MACb;AAAA,IACF;AAIA,gBAAY,UAAU,QAAQ;AAG9B,WAAO,UAAU;AAAA;AAAA;;;ACzJjB;AAAA;AAAA;AAQA,QAAI,QAAkB;AAMtB,QAAI,SAAS;AAAA,MACX,CAAE,QAAmB,cAA+B;AAAA,MACpD,CAAE,WAAmB,iBAAkC;AAAA,MACvD,CAAE,UAAmB,gBAAiC;AAAA,MACtD,CAAE,aAAmB,mBAAoC;AAAA,MACzD,CAAE,iBAAmB,wBAAwC,QAAS;AAAA,MACtE,CAAE,YAAmB,mBAAmC,QAAS;AAAA,MACjE,CAAE,QAAmB,cAA+B;AAAA,MACpD,CAAE,SAAmB,eAAgC;AAAA,MACrD,CAAE,YAAmB,kBAAmC;AAAA,MACxD,CAAE,eAAmB,qBAAsC;AAAA,MAC3D,CAAE,UAAmB,gBAAiC;AAAA,IACxD;AAEA,QAAI,UAAU;AAAA,MACZ,CAAE,iBAAmB,uBAAwC;AAAA,MAC7D,CAAE,iBAAmB,wBAAwC,WAAY;AAAA,MACzE,CAAE,YAAmB,mBAAmC,WAAY;AAAA,MACpE,CAAE,iBAAmB,uBAAwC;AAAA,IAC/D;AAMA,aAAS,eAAe;AACtB,UAAI;AAOJ,WAAK,QAAQ,IAAI,MAAM;AAEvB,WAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AAClC,aAAK,MAAM,KAAK,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC;AAAA,MAC5C;AAQA,WAAK,SAAS,IAAI,MAAM;AAExB,WAAK,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACnC,aAAK,OAAO,KAAK,QAAQ,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC;AAAA,MAC/C;AAAA,IACF;AAMA,iBAAa,UAAU,YAAY,SAAU,OAAO;AAClD,UAAI,IAAI,GAAG,MAAM,MAAM,KACnB,QAAQ,KAAK,MAAM,SAAS,EAAE,GAC9B,MAAM,MAAM,QACZ,aAAa,MAAM,GAAG,QAAQ,YAC9B,QAAQ,MAAM;AAGlB,UAAI,OAAO,MAAM,GAAG,MAAM,aAAa;AACrC,cAAM,MAAM,MAAM,GAAG;AACrB;AAAA,MACF;AAEA,UAAI,MAAM,QAAQ,YAAY;AAC5B,aAAK,IAAI,GAAG,IAAI,KAAK,KAAK;AAKxB,gBAAM;AACN,eAAK,MAAM,CAAC,EAAE,OAAO,IAAI;AACzB,gBAAM;AAEN,cAAI,IAAI;AAAE;AAAA,UAAO;AAAA,QACnB;AAAA,MACF,OAAO;AAYL,cAAM,MAAM,MAAM;AAAA,MACpB;AAEA,UAAI,CAAC,IAAI;AAAE,cAAM;AAAA,MAAO;AACxB,YAAM,GAAG,IAAI,MAAM;AAAA,IACrB;AAKA,iBAAa,UAAU,WAAW,SAAU,OAAO;AACjD,UAAI,IAAI,GACJ,QAAQ,KAAK,MAAM,SAAS,EAAE,GAC9B,MAAM,MAAM,QACZ,MAAM,MAAM,QACZ,aAAa,MAAM,GAAG,QAAQ;AAElC,aAAO,MAAM,MAAM,KAAK;AAQtB,YAAI,MAAM,QAAQ,YAAY;AAC5B,eAAK,IAAI,GAAG,IAAI,KAAK,KAAK;AACxB,iBAAK,MAAM,CAAC,EAAE,OAAO,KAAK;AAC1B,gBAAI,IAAI;AAAE;AAAA,YAAO;AAAA,UACnB;AAAA,QACF;AAEA,YAAI,IAAI;AACN,cAAI,MAAM,OAAO,KAAK;AAAE;AAAA,UAAO;AAC/B;AAAA,QACF;AAEA,cAAM,WAAW,MAAM,IAAI,MAAM,KAAK;AAAA,MACxC;AAEA,UAAI,MAAM,SAAS;AACjB,cAAM,YAAY;AAAA,MACpB;AAAA,IACF;AAQA,iBAAa,UAAU,QAAQ,SAAU,KAAK,IAAI,KAAK,WAAW;AAChE,UAAI,GAAG,OAAO;AACd,UAAI,QAAQ,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,SAAS;AAElD,WAAK,SAAS,KAAK;AAEnB,cAAQ,KAAK,OAAO,SAAS,EAAE;AAC/B,YAAM,MAAM;AAEZ,WAAK,IAAI,GAAG,IAAI,KAAK,KAAK;AACxB,cAAM,CAAC,EAAE,KAAK;AAAA,MAChB;AAAA,IACF;AAGA,iBAAa,UAAU,QAAQ;AAG/B,WAAO,UAAU;AAAA;AAAA;;;AChLjB;AAAA;AAAA;AAGA,WAAO,UAAU,SAAU,MAAM;AAC/B,UAAI,KAAK,CAAC;AAGV,SAAG,UAAU,iBAAyC;AACtD,SAAG,SAAU,iBAAwC;AACrD,SAAG,QAAU,iBAAuC;AACpD,SAAG,QAAU,gBAAuC;AAGpD,SAAG,WAAW,CAAE,GAAG,OAAO,GAAG,OAAO,GAAG,MAAO,EAAE,KAAK,GAAG;AAGxD,SAAG,UAAU,CAAE,GAAG,OAAO,GAAG,MAAO,EAAE,KAAK,GAAG;AAI7C,UAAI,kBAAkB;AAKtB,SAAG,oBAA0B,WAAW,kBAAkB,MAAM,GAAG,WAAW,MAAM,GAAG,UAAU;AAMjG,SAAG,UAED;AAGF,SAAG,WAAc,cAAc,GAAG,UAAU;AAE5C,SAAG,WAED;AAEF,SAAG,sBAED,UAAU,kBAAkB,MAAM,GAAG,WAAW,+BAA+B,GAAG,WAAW;AAE/F,SAAG,WAED,mBAGc,GAAG,UAAU,MAAM,kBAAkB,sCAC/B,GAAG,UAAU,0BACb,GAAG,UAAU,0BACb,GAAG,UAAU,0BACb,GAAG,UAAU,0BACb,GAAG,UAAU,uBAChB,GAAG,oBAAoB,wCASvB,GAAG,UAAU,aACvB,QAAQ,KAAK,KAAK,IACjB,+BAEA,WAEF,SAAS,GAAG,UAAU,YACb,GAAG,UAAU,eACV,GAAG,UAAU,kBACd,GAAG,UAAU;AAOhC,SAAG,iBAED;AAEF,SAAG,SAED;AAKF,SAAG;AAAA,MAGD,QACE,GAAG,SACH,MACA,GAAG,oBAAoB;AAG3B,SAAG,aAED,QACE,GAAG,SACH,SACQ,GAAG,oBAAoB,UAEvB,GAAG,oBAAoB,UAAU,GAAG,oBAAoB,YAAY,GAAG,oBAAoB;AAGvG,SAAG,WAED,iBAIgB,GAAG,aAAa,WAAW,GAAG,aAAsB;AAGtE,SAAG,iBAED,QACE,GAAG,UACL,eACgB,GAAG,aAAa;AAGlC,SAAG,uBAED,cAAc,GAAG,aAAa;AAEhC,SAAG,kBAED,GAAG,WAAW,GAAG;AAEnB,SAAG,wBAED,GAAG,iBAAiB,GAAG;AAEzB,SAAG,uBAED,GAAG,WAAW,GAAG,WAAW,GAAG;AAEjC,SAAG,6BAED,GAAG,iBAAiB,GAAG,WAAW,GAAG;AAEvC,SAAG,mCAED,GAAG,uBAAuB,GAAG,WAAW,GAAG;AAO7C,SAAG,sBAED,wDAAwD,GAAG,WAAW;AAExE,SAAG,kBAEC,QAAQ,kBAAkB,YAAY,GAAG,UAAU,OAC7C,GAAG,iBAAiB,MAAM,GAAG,wBAAwB;AAE/D,SAAG;AAAA;AAAA,MAGC,qCAA0C,GAAG,WAAW,uBAC9B,GAAG,6BAA6B,GAAG,WAAW;AAE5E,SAAG;AAAA;AAAA,MAGC,qCAA0C,GAAG,WAAW,uBAC9B,GAAG,mCAAmC,GAAG,WAAW;AAElF,aAAO;AAAA,IACT;AAAA;AAAA;;;ACpLA;AAAA;AAAA;AAQA,aAAS,OAAO,KAAkC;AAChD,UAAI,UAAU,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AAErD,cAAQ,QAAQ,SAAU,QAAQ;AAChC,YAAI,CAAC,QAAQ;AAAE;AAAA,QAAQ;AAEvB,eAAO,KAAK,MAAM,EAAE,QAAQ,SAAU,KAAK;AACzC,cAAI,GAAG,IAAI,OAAO,GAAG;AAAA,QACvB,CAAC;AAAA,MACH,CAAC;AAED,aAAO;AAAA,IACT;AAEA,aAAS,OAAO,KAAK;AAAE,aAAO,OAAO,UAAU,SAAS,KAAK,GAAG;AAAA,IAAG;AACnE,aAAS,SAAS,KAAK;AAAE,aAAO,OAAO,GAAG,MAAM;AAAA,IAAmB;AACnE,aAAS,SAAS,KAAK;AAAE,aAAO,OAAO,GAAG,MAAM;AAAA,IAAmB;AACnE,aAAS,SAAS,KAAK;AAAE,aAAO,OAAO,GAAG,MAAM;AAAA,IAAmB;AACnE,aAAS,WAAW,KAAK;AAAE,aAAO,OAAO,GAAG,MAAM;AAAA,IAAqB;AAGvE,aAAS,SAAS,KAAK;AAAE,aAAO,IAAI,QAAQ,wBAAwB,MAAM;AAAA,IAAG;AAK7E,QAAI,iBAAiB;AAAA,MACnB,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,SAAS;AAAA,IACX;AAGA,aAAS,aAAa,KAAK;AACzB,aAAO,OAAO,KAAK,OAAO,CAAC,CAAC,EAAE,OAAO,SAAU,KAAK,GAAG;AACrD,eAAO,OAAO,eAAe,eAAe,CAAC;AAAA,MAC/C,GAAG,KAAK;AAAA,IACV;AAGA,QAAI,iBAAiB;AAAA,MACnB,SAAS;AAAA,QACP,UAAU,SAAU,MAAM,KAAK,MAAM;AACnC,cAAI,OAAO,KAAK,MAAM,GAAG;AAEzB,cAAI,CAAC,KAAK,GAAG,MAAM;AAEjB,iBAAK,GAAG,OAAQ,IAAI;AAAA,cAClB,YAAY,KAAK,GAAG,WAAW,KAAK,GAAG,uBAAuB,KAAK,GAAG;AAAA,cAAU;AAAA,YAClF;AAAA,UACF;AACA,cAAI,KAAK,GAAG,KAAK,KAAK,IAAI,GAAG;AAC3B,mBAAO,KAAK,MAAM,KAAK,GAAG,IAAI,EAAE,CAAC,EAAE;AAAA,UACrC;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA,UAAW;AAAA,MACX,QAAW;AAAA,MACX,MAAW;AAAA,QACT,UAAU,SAAU,MAAM,KAAK,MAAM;AACnC,cAAI,OAAO,KAAK,MAAM,GAAG;AAEzB,cAAI,CAAC,KAAK,GAAG,SAAS;AAEpB,iBAAK,GAAG,UAAW,IAAI;AAAA,cACrB,MACA,KAAK,GAAG;AAAA;AAAA,cAGR,wBAAwB,KAAK,GAAG,aAAa,WAAW,KAAK,GAAG,kBAAkB,MAClF,KAAK,GAAG,WACR,KAAK,GAAG,sBACR,KAAK,GAAG;AAAA,cAER;AAAA,YACF;AAAA,UACF;AAEA,cAAI,KAAK,GAAG,QAAQ,KAAK,IAAI,GAAG;AAE9B,gBAAI,OAAO,KAAK,KAAK,MAAM,CAAC,MAAM,KAAK;AAAE,qBAAO;AAAA,YAAG;AACnD,gBAAI,OAAO,KAAK,KAAK,MAAM,CAAC,MAAM,KAAK;AAAE,qBAAO;AAAA,YAAG;AACnD,mBAAO,KAAK,MAAM,KAAK,GAAG,OAAO,EAAE,CAAC,EAAE;AAAA,UACxC;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA,WAAW;AAAA,QACT,UAAU,SAAU,MAAM,KAAK,MAAM;AACnC,cAAI,OAAO,KAAK,MAAM,GAAG;AAEzB,cAAI,CAAC,KAAK,GAAG,QAAQ;AACnB,iBAAK,GAAG,SAAU,IAAI;AAAA,cACpB,MAAM,KAAK,GAAG,iBAAiB,MAAM,KAAK,GAAG;AAAA,cAAiB;AAAA,YAChE;AAAA,UACF;AACA,cAAI,KAAK,GAAG,OAAO,KAAK,IAAI,GAAG;AAC7B,mBAAO,KAAK,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,EAAE;AAAA,UACvC;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAKA,QAAI,kBAAkB;AAGtB,QAAI,eAAe,8EAA8E,MAAM,GAAG;AAM1G,aAAS,eAAe,MAAM;AAC5B,WAAK,YAAY;AACjB,WAAK,iBAAmB;AAAA,IAC1B;AAEA,aAAS,gBAAgB,IAAI;AAC3B,aAAO,SAAU,MAAM,KAAK;AAC1B,YAAI,OAAO,KAAK,MAAM,GAAG;AAEzB,YAAI,GAAG,KAAK,IAAI,GAAG;AACjB,iBAAO,KAAK,MAAM,EAAE,EAAE,CAAC,EAAE;AAAA,QAC3B;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,mBAAmB;AAC1B,aAAO,SAAU,OAAO,MAAM;AAC5B,aAAK,UAAU,KAAK;AAAA,MACtB;AAAA,IACF;AAIA,aAAS,QAAQ,MAAM;AAGrB,UAAI,KAAK,KAAK,KAAK,aAAoB,KAAK,QAAQ;AAGpD,UAAI,OAAO,KAAK,SAAS,MAAM;AAE/B,WAAK,UAAU;AAEf,UAAI,CAAC,KAAK,mBAAmB;AAC3B,aAAK,KAAK,eAAe;AAAA,MAC3B;AACA,WAAK,KAAK,GAAG,MAAM;AAEnB,SAAG,WAAW,KAAK,KAAK,GAAG;AAE3B,eAAS,MAAM,KAAK;AAAE,eAAO,IAAI,QAAQ,UAAU,GAAG,QAAQ;AAAA,MAAG;AAEjE,SAAG,cAAmB,OAAO,MAAM,GAAG,eAAe,GAAG,GAAG;AAC3D,SAAG,aAAmB,OAAO,MAAM,GAAG,cAAc,GAAG,GAAG;AAC1D,SAAG,mBAAmB,OAAO,MAAM,GAAG,oBAAoB,GAAG,GAAG;AAChE,SAAG,kBAAmB,OAAO,MAAM,GAAG,mBAAmB,GAAG,GAAG;AAM/D,UAAI,UAAU,CAAC;AAEf,WAAK,eAAe,CAAC;AAErB,eAAS,YAAY,MAAM,KAAK;AAC9B,cAAM,IAAI,MAAM,iCAAiC,OAAO,QAAQ,GAAG;AAAA,MACrE;AAEA,aAAO,KAAK,KAAK,WAAW,EAAE,QAAQ,SAAU,MAAM;AACpD,YAAI,MAAM,KAAK,YAAY,IAAI;AAG/B,YAAI,QAAQ,MAAM;AAAE;AAAA,QAAQ;AAE5B,YAAI,WAAW,EAAE,UAAU,MAAM,MAAM,KAAK;AAE5C,aAAK,aAAa,IAAI,IAAI;AAE1B,YAAI,SAAS,GAAG,GAAG;AACjB,cAAI,SAAS,IAAI,QAAQ,GAAG;AAC1B,qBAAS,WAAW,gBAAgB,IAAI,QAAQ;AAAA,UAClD,WAAW,WAAW,IAAI,QAAQ,GAAG;AACnC,qBAAS,WAAW,IAAI;AAAA,UAC1B,OAAO;AACL,wBAAY,MAAM,GAAG;AAAA,UACvB;AAEA,cAAI,WAAW,IAAI,SAAS,GAAG;AAC7B,qBAAS,YAAY,IAAI;AAAA,UAC3B,WAAW,CAAC,IAAI,WAAW;AACzB,qBAAS,YAAY,iBAAiB;AAAA,UACxC,OAAO;AACL,wBAAY,MAAM,GAAG;AAAA,UACvB;AAEA;AAAA,QACF;AAEA,YAAI,SAAS,GAAG,GAAG;AACjB,kBAAQ,KAAK,IAAI;AACjB;AAAA,QACF;AAEA,oBAAY,MAAM,GAAG;AAAA,MACvB,CAAC;AAMD,cAAQ,QAAQ,SAAU,OAAO;AAC/B,YAAI,CAAC,KAAK,aAAa,KAAK,YAAY,KAAK,CAAC,GAAG;AAG/C;AAAA,QACF;AAEA,aAAK,aAAa,KAAK,EAAE,WACvB,KAAK,aAAa,KAAK,YAAY,KAAK,CAAC,EAAE;AAC7C,aAAK,aAAa,KAAK,EAAE,YACvB,KAAK,aAAa,KAAK,YAAY,KAAK,CAAC,EAAE;AAAA,MAC/C,CAAC;AAKD,WAAK,aAAa,EAAE,IAAI,EAAE,UAAU,MAAM,WAAW,iBAAiB,EAAE;AAKxE,UAAI,QAAQ,OAAO,KAAK,KAAK,YAAY,EACpB,OAAO,SAAU,MAAM;AAEtB,eAAO,KAAK,SAAS,KAAK,KAAK,aAAa,IAAI;AAAA,MAClD,CAAC,EACA,IAAI,QAAQ,EACZ,KAAK,GAAG;AAE7B,WAAK,GAAG,cAAgB,OAAO,sBAA2B,GAAG,WAAW,QAAQ,QAAQ,KAAK,GAAG;AAChG,WAAK,GAAG,gBAAgB,OAAO,sBAA2B,GAAG,WAAW,QAAQ,QAAQ,KAAK,IAAI;AAEjG,WAAK,GAAG,UAAU;AAAA,QAChB,MAAM,KAAK,GAAG,YAAY,SAAS,QAAQ,KAAK,GAAG,gBAAgB,SAAS;AAAA,QAC5E;AAAA,MACF;AAMA,qBAAe,IAAI;AAAA,IACrB;AAOA,aAAS,MAAM,MAAM,OAAO;AAC1B,UAAI,QAAQ,KAAK,WACb,MAAQ,KAAK,gBACb,OAAQ,KAAK,eAAe,MAAM,OAAO,GAAG;AAOhD,WAAK,SAAY,KAAK,WAAW,YAAY;AAM7C,WAAK,QAAY,QAAQ;AAMzB,WAAK,YAAY,MAAM;AAMvB,WAAK,MAAY;AAMjB,WAAK,OAAY;AAMjB,WAAK,MAAY;AAAA,IACnB;AAEA,aAAS,YAAY,MAAM,OAAO;AAChC,UAAI,QAAQ,IAAI,MAAM,MAAM,KAAK;AAEjC,WAAK,aAAa,MAAM,MAAM,EAAE,UAAU,OAAO,IAAI;AAErD,aAAO;AAAA,IACT;AAyCA,aAAS,UAAU,SAAS,SAAS;AACnC,UAAI,EAAE,gBAAgB,YAAY;AAChC,eAAO,IAAI,UAAU,SAAS,OAAO;AAAA,MACvC;AAEA,UAAI,CAAC,SAAS;AACZ,YAAI,aAAa,OAAO,GAAG;AACzB,oBAAU;AACV,oBAAU,CAAC;AAAA,QACb;AAAA,MACF;AAEA,WAAK,WAAqB,OAAO,CAAC,GAAG,gBAAgB,OAAO;AAG5D,WAAK,YAAqB;AAC1B,WAAK,iBAAqB;AAC1B,WAAK,aAAqB;AAC1B,WAAK,iBAAqB;AAE1B,WAAK,cAAqB,OAAO,CAAC,GAAG,gBAAgB,OAAO;AAC5D,WAAK,eAAqB,CAAC;AAE3B,WAAK,WAAqB;AAC1B,WAAK,oBAAqB;AAE1B,WAAK,KAAK,CAAC;AAEX,cAAQ,IAAI;AAAA,IACd;AAUA,cAAU,UAAU,MAAM,SAAS,IAAI,QAAQ,YAAY;AACzD,WAAK,YAAY,MAAM,IAAI;AAC3B,cAAQ,IAAI;AACZ,aAAO;AAAA,IACT;AASA,cAAU,UAAU,MAAM,SAAS,IAAI,SAAS;AAC9C,WAAK,WAAW,OAAO,KAAK,UAAU,OAAO;AAC7C,aAAO;AAAA,IACT;AAQA,cAAU,UAAU,OAAO,SAAS,KAAK,MAAM;AAE7C,WAAK,iBAAiB;AACtB,WAAK,YAAiB;AAEtB,UAAI,CAAC,KAAK,QAAQ;AAAE,eAAO;AAAA,MAAO;AAElC,UAAI,GAAG,IAAI,IAAI,KAAK,OAAO,MAAM,IAAI,SAAS;AAG9C,UAAI,KAAK,GAAG,YAAY,KAAK,IAAI,GAAG;AAClC,aAAK,KAAK,GAAG;AACb,WAAG,YAAY;AACf,gBAAQ,IAAI,GAAG,KAAK,IAAI,OAAO,MAAM;AACnC,gBAAM,KAAK,aAAa,MAAM,EAAE,CAAC,GAAG,GAAG,SAAS;AAChD,cAAI,KAAK;AACP,iBAAK,aAAiB,EAAE,CAAC;AACzB,iBAAK,YAAiB,EAAE,QAAQ,EAAE,CAAC,EAAE;AACrC,iBAAK,iBAAiB,EAAE,QAAQ,EAAE,CAAC,EAAE,SAAS;AAC9C;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,UAAI,KAAK,SAAS,aAAa,KAAK,aAAa,OAAO,GAAG;AAEzD,kBAAU,KAAK,OAAO,KAAK,GAAG,eAAe;AAC7C,YAAI,WAAW,GAAG;AAEhB,cAAI,KAAK,YAAY,KAAK,UAAU,KAAK,WAAW;AAClD,iBAAK,KAAK,KAAK,MAAM,KAAK,SAAS,UAAU,KAAK,GAAG,aAAa,KAAK,GAAG,gBAAgB,OAAO,MAAM;AAErG,sBAAQ,GAAG,QAAQ,GAAG,CAAC,EAAE;AAEzB,kBAAI,KAAK,YAAY,KAAK,QAAQ,KAAK,WAAW;AAChD,qBAAK,aAAiB;AACtB,qBAAK,YAAiB;AACtB,qBAAK,iBAAiB,GAAG,QAAQ,GAAG,CAAC,EAAE;AAAA,cACzC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,UAAI,KAAK,SAAS,cAAc,KAAK,aAAa,SAAS,GAAG;AAE5D,iBAAS,KAAK,QAAQ,GAAG;AACzB,YAAI,UAAU,GAAG;AAGf,eAAK,KAAK,KAAK,MAAM,KAAK,GAAG,WAAW,OAAO,MAAM;AAEnD,oBAAQ,GAAG,QAAQ,GAAG,CAAC,EAAE;AACzB,mBAAQ,GAAG,QAAQ,GAAG,CAAC,EAAE;AAEzB,gBAAI,KAAK,YAAY,KAAK,QAAQ,KAAK,aAClC,UAAU,KAAK,aAAa,OAAO,KAAK,gBAAiB;AAC5D,mBAAK,aAAiB;AACtB,mBAAK,YAAiB;AACtB,mBAAK,iBAAiB;AAAA,YACxB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,aAAO,KAAK,aAAa;AAAA,IAC3B;AAUA,cAAU,UAAU,UAAU,SAAS,QAAQ,MAAM;AACnD,aAAO,KAAK,GAAG,QAAQ,KAAK,IAAI;AAAA,IAClC;AAYA,cAAU,UAAU,eAAe,SAAS,aAAa,MAAM,QAAQ,KAAK;AAE1E,UAAI,CAAC,KAAK,aAAa,OAAO,YAAY,CAAC,GAAG;AAC5C,eAAO;AAAA,MACT;AACA,aAAO,KAAK,aAAa,OAAO,YAAY,CAAC,EAAE,SAAS,MAAM,KAAK,IAAI;AAAA,IACzE;AAmBA,cAAU,UAAU,QAAQ,SAAS,MAAM,MAAM;AAC/C,UAAI,QAAQ,GAAG,SAAS,CAAC;AAGzB,UAAI,KAAK,aAAa,KAAK,KAAK,mBAAmB,MAAM;AACvD,eAAO,KAAK,YAAY,MAAM,KAAK,CAAC;AACpC,gBAAQ,KAAK;AAAA,MACf;AAGA,UAAI,OAAO,QAAQ,KAAK,MAAM,KAAK,IAAI;AAGvC,aAAO,KAAK,KAAK,IAAI,GAAG;AACtB,eAAO,KAAK,YAAY,MAAM,KAAK,CAAC;AAEpC,eAAO,KAAK,MAAM,KAAK,cAAc;AACrC,iBAAS,KAAK;AAAA,MAChB;AAEA,UAAI,OAAO,QAAQ;AACjB,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAkBA,cAAU,UAAU,OAAO,SAAS,KAAK,MAAM,SAAS;AACtD,aAAO,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAE,IAAK;AAE3C,UAAI,CAAC,SAAS;AACZ,aAAK,WAAW,KAAK,MAAM;AAC3B,aAAK,oBAAoB;AACzB,gBAAQ,IAAI;AACZ,eAAO;AAAA,MACT;AAEA,WAAK,WAAW,KAAK,SAAS,OAAO,IAAI,EACR,KAAK,EACL,OAAO,SAAU,IAAI,KAAK,KAAK;AAC9B,eAAO,OAAO,IAAI,MAAM,CAAC;AAAA,MAC3B,CAAC,EACA,QAAQ;AAEzC,cAAQ,IAAI;AACZ,aAAO;AAAA,IACT;AAOA,cAAU,UAAU,YAAY,SAAS,UAAU,OAAO;AAKxD,UAAI,CAAC,MAAM,QAAQ;AAAE,cAAM,MAAM,YAAY,MAAM;AAAA,MAAK;AAExD,UAAI,MAAM,WAAW,aAAa,CAAC,YAAY,KAAK,MAAM,GAAG,GAAG;AAC9D,cAAM,MAAM,YAAY,MAAM;AAAA,MAChC;AAAA,IACF;AAQA,cAAU,UAAU,YAAY,SAAS,YAAY;AAAA,IACrD;AAGA,WAAO,UAAU;AAAA;AAAA;;;AC3nBjB;AAAA;AAAA;AAKA,WAAO,UAAU;AAAA,MACf,SAAS;AAAA,QACP,MAAc;AAAA;AAAA,QACd,UAAc;AAAA;AAAA,QACd,QAAc;AAAA;AAAA,QACd,YAAc;AAAA;AAAA,QACd,SAAc;AAAA;AAAA;AAAA,QAGd,aAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOd,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQR,WAAW;AAAA,QAEX,YAAc;AAAA;AAAA,MAChB;AAAA,MAEA,YAAY;AAAA,QAEV,MAAM,CAAC;AAAA,QACP,OAAO,CAAC;AAAA,QACR,QAAQ,CAAC;AAAA,MACX;AAAA,IACF;AAAA;AAAA;;;ACxCA;AAAA;AAAA;AAMA,WAAO,UAAU;AAAA,MACf,SAAS;AAAA,QACP,MAAc;AAAA;AAAA,QACd,UAAc;AAAA;AAAA,QACd,QAAc;AAAA;AAAA,QACd,YAAc;AAAA;AAAA,QACd,SAAc;AAAA;AAAA;AAAA,QAGd,aAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOd,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQR,WAAW;AAAA,QAEX,YAAc;AAAA;AAAA,MAChB;AAAA,MAEA,YAAY;AAAA,QAEV,MAAM;AAAA,UACJ,OAAO;AAAA,YACL;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QAEA,OAAO;AAAA,UACL,OAAO;AAAA,YACL;AAAA,UACF;AAAA,QACF;AAAA,QAEA,QAAQ;AAAA,UACN,OAAO;AAAA,YACL;AAAA,UACF;AAAA,UACA,QAAQ;AAAA,YACN;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;AC7DA;AAAA;AAAA;AAKA,WAAO,UAAU;AAAA,MACf,SAAS;AAAA,QACP,MAAc;AAAA;AAAA,QACd,UAAc;AAAA;AAAA,QACd,QAAc;AAAA;AAAA,QACd,YAAc;AAAA;AAAA,QACd,SAAc;AAAA;AAAA;AAAA,QAGd,aAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOd,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQR,WAAW;AAAA,QAEX,YAAc;AAAA;AAAA,MAChB;AAAA,MAEA,YAAY;AAAA,QAEV,MAAM;AAAA,UACJ,OAAO;AAAA,YACL;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QAEA,OAAO;AAAA,UACL,OAAO;AAAA,YACL;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QAEA,QAAQ;AAAA,UACN,OAAO;AAAA,YACL;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,UACA,QAAQ;AAAA,YACN;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;AC/EA;AAAA;AAAA;AAKA,QAAI,QAAe;AACnB,QAAI,UAAe;AACnB,QAAI,WAAe;AACnB,QAAI,aAAe;AACnB,QAAI,cAAe;AACnB,QAAI,eAAe;AACnB,QAAI,YAAe;AACnB,QAAI,QAAe;AACnB,QAAI,WAAe;AAGnB,QAAI,SAAS;AAAA,MACX,SAAS;AAAA,MACT,MAAM;AAAA,MACN,YAAY;AAAA,IACd;AAWA,QAAI,eAAe;AACnB,QAAI,eAAe;AAEnB,aAAS,aAAa,KAAK;AAEzB,UAAI,MAAM,IAAI,KAAK,EAAE,YAAY;AAEjC,aAAO,aAAa,KAAK,GAAG,IAAK,aAAa,KAAK,GAAG,IAAI,OAAO,QAAS;AAAA,IAC5E;AAKA,QAAI,sBAAsB,CAAE,SAAS,UAAU,SAAU;AAEzD,aAAS,cAAc,KAAK;AAC1B,UAAI,SAAS,MAAM,MAAM,KAAK,IAAI;AAElC,UAAI,OAAO,UAAU;AAOnB,YAAI,CAAC,OAAO,YAAY,oBAAoB,QAAQ,OAAO,QAAQ,KAAK,GAAG;AACzE,cAAI;AACF,mBAAO,WAAW,SAAS,QAAQ,OAAO,QAAQ;AAAA,UACpD,SAAS,IAAI;AAAA,UAAO;AAAA,QACtB;AAAA,MACF;AAEA,aAAO,MAAM,OAAO,MAAM,OAAO,MAAM,CAAC;AAAA,IAC1C;AAEA,aAAS,kBAAkB,KAAK;AAC9B,UAAI,SAAS,MAAM,MAAM,KAAK,IAAI;AAElC,UAAI,OAAO,UAAU;AAOnB,YAAI,CAAC,OAAO,YAAY,oBAAoB,QAAQ,OAAO,QAAQ,KAAK,GAAG;AACzE,cAAI;AACF,mBAAO,WAAW,SAAS,UAAU,OAAO,QAAQ;AAAA,UACtD,SAAS,IAAI;AAAA,UAAO;AAAA,QACtB;AAAA,MACF;AAGA,aAAO,MAAM,OAAO,MAAM,OAAO,MAAM,GAAG,MAAM,OAAO,eAAe,GAAG;AAAA,IAC3E;AAwIA,aAAS,WAAW,YAAY,SAAS;AACvC,UAAI,EAAE,gBAAgB,aAAa;AACjC,eAAO,IAAI,WAAW,YAAY,OAAO;AAAA,MAC3C;AAEA,UAAI,CAAC,SAAS;AACZ,YAAI,CAAC,MAAM,SAAS,UAAU,GAAG;AAC/B,oBAAU,cAAc,CAAC;AACzB,uBAAa;AAAA,QACf;AAAA,MACF;AASA,WAAK,SAAS,IAAI,aAAa;AAS/B,WAAK,QAAQ,IAAI,YAAY;AAS7B,WAAK,OAAO,IAAI,WAAW;AAuB3B,WAAK,WAAW,IAAI,SAAS;AAS7B,WAAK,UAAU,IAAI,UAAU;AAiB7B,WAAK,eAAe;AAQpB,WAAK,gBAAgB;AAOrB,WAAK,oBAAoB;AAWzB,WAAK,QAAQ;AAQb,WAAK,UAAU,MAAM,OAAO,CAAC,GAAG,OAAO;AAGvC,WAAK,UAAU,CAAC;AAChB,WAAK,UAAU,UAAU;AAEzB,UAAI,SAAS;AAAE,aAAK,IAAI,OAAO;AAAA,MAAG;AAAA,IACpC;AAsBA,eAAW,UAAU,MAAM,SAAU,SAAS;AAC5C,YAAM,OAAO,KAAK,SAAS,OAAO;AAClC,aAAO;AAAA,IACT;AAaA,eAAW,UAAU,YAAY,SAAU,SAAS;AAClD,UAAI,OAAO,MAAM;AAEjB,UAAI,MAAM,SAAS,OAAO,GAAG;AAC3B,qBAAa;AACb,kBAAU,OAAO,UAAU;AAC3B,YAAI,CAAC,SAAS;AAAE,gBAAM,IAAI,MAAM,iCAAiC,aAAa,eAAe;AAAA,QAAG;AAAA,MAClG;AAEA,UAAI,CAAC,SAAS;AAAE,cAAM,IAAI,MAAM,4CAA6C;AAAA,MAAG;AAEhF,UAAI,QAAQ,SAAS;AAAE,aAAK,IAAI,QAAQ,OAAO;AAAA,MAAG;AAElD,UAAI,QAAQ,YAAY;AACtB,eAAO,KAAK,QAAQ,UAAU,EAAE,QAAQ,SAAU,MAAM;AACtD,cAAI,QAAQ,WAAW,IAAI,EAAE,OAAO;AAClC,iBAAK,IAAI,EAAE,MAAM,WAAW,QAAQ,WAAW,IAAI,EAAE,KAAK;AAAA,UAC5D;AACA,cAAI,QAAQ,WAAW,IAAI,EAAE,QAAQ;AACnC,iBAAK,IAAI,EAAE,OAAO,WAAW,QAAQ,WAAW,IAAI,EAAE,MAAM;AAAA,UAC9D;AAAA,QACF,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AAoBA,eAAW,UAAU,SAAS,SAAU,MAAM,eAAe;AAC3D,UAAI,SAAS,CAAC;AAEd,UAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AAAE,eAAO,CAAE,IAAK;AAAA,MAAG;AAE7C,OAAE,QAAQ,SAAS,QAAS,EAAE,QAAQ,SAAU,OAAO;AACrD,iBAAS,OAAO,OAAO,KAAK,KAAK,EAAE,MAAM,OAAO,MAAM,IAAI,CAAC;AAAA,MAC7D,GAAG,IAAI;AAEP,eAAS,OAAO,OAAO,KAAK,OAAO,OAAO,OAAO,MAAM,IAAI,CAAC;AAE5D,UAAI,SAAS,KAAK,OAAO,SAAU,MAAM;AAAE,eAAO,OAAO,QAAQ,IAAI,IAAI;AAAA,MAAG,CAAC;AAE7E,UAAI,OAAO,UAAU,CAAC,eAAe;AACnC,cAAM,IAAI,MAAM,mDAAmD,MAAM;AAAA,MAC3E;AAEA,aAAO;AAAA,IACT;AAUA,eAAW,UAAU,UAAU,SAAU,MAAM,eAAe;AAC5D,UAAI,SAAS,CAAC;AAEd,UAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AAAE,eAAO,CAAE,IAAK;AAAA,MAAG;AAE7C,OAAE,QAAQ,SAAS,QAAS,EAAE,QAAQ,SAAU,OAAO;AACrD,iBAAS,OAAO,OAAO,KAAK,KAAK,EAAE,MAAM,QAAQ,MAAM,IAAI,CAAC;AAAA,MAC9D,GAAG,IAAI;AAEP,eAAS,OAAO,OAAO,KAAK,OAAO,OAAO,QAAQ,MAAM,IAAI,CAAC;AAE7D,UAAI,SAAS,KAAK,OAAO,SAAU,MAAM;AAAE,eAAO,OAAO,QAAQ,IAAI,IAAI;AAAA,MAAG,CAAC;AAE7E,UAAI,OAAO,UAAU,CAAC,eAAe;AACnC,cAAM,IAAI,MAAM,oDAAoD,MAAM;AAAA,MAC5E;AACA,aAAO;AAAA,IACT;AAmBA,eAAW,UAAU,MAAM,SAAU,QAA2B;AAC9D,UAAI,OAAO,CAAE,IAAK,EAAE,OAAO,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC,CAAC;AACnE,aAAO,MAAM,QAAQ,IAAI;AACzB,aAAO;AAAA,IACT;AAkBA,eAAW,UAAU,QAAQ,SAAU,KAAK,KAAK;AAC/C,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM,IAAI,MAAM,+BAA+B;AAAA,MACjD;AAEA,UAAI,QAAQ,IAAI,KAAK,KAAK,MAAM,KAAK,MAAM,GAAG;AAE9C,WAAK,KAAK,QAAQ,KAAK;AAEvB,aAAO,MAAM;AAAA,IACf;AAcA,eAAW,UAAU,SAAS,SAAU,KAAK,KAAK;AAChD,YAAM,OAAO,CAAC;AAEd,aAAO,KAAK,SAAS,OAAO,KAAK,MAAM,KAAK,GAAG,GAAG,KAAK,SAAS,GAAG;AAAA,IACrE;AAYA,eAAW,UAAU,cAAc,SAAU,KAAK,KAAK;AACrD,UAAI,QAAQ,IAAI,KAAK,KAAK,MAAM,KAAK,MAAM,GAAG;AAE9C,YAAM,aAAa;AACnB,WAAK,KAAK,QAAQ,KAAK;AAEvB,aAAO,MAAM;AAAA,IACf;AAWA,eAAW,UAAU,eAAe,SAAU,KAAK,KAAK;AACtD,YAAM,OAAO,CAAC;AAEd,aAAO,KAAK,SAAS,OAAO,KAAK,YAAY,KAAK,GAAG,GAAG,KAAK,SAAS,GAAG;AAAA,IAC3E;AAGA,WAAO,UAAU;AAAA;AAAA;;;ACrkBjB;AAAA;AAAA;AAGA,WAAO,UAAU;AAAA;AAAA;;;ACHjB,IAAAC,eAAA;AAAA;AAAA;AAcA,QAAM,uBAAuB,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AACtE,QAAM,uBAAuB,CAAC,OAAO,OAAO,OAAO,QAAQ,OAAO,MAAM;AAWxE,QAAI,WAAW;AAAA,MACb,IAAI;AAAA,QACF,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,2BAA2B;AAAA,MAC7B;AAAA,IACF;AAgBA,QAAI,YAAY,SAAS,UAAU,YAAY,eAAe;AAI5D,UAAI,CAAC,SAAS,QAAQ,KAAK,CAAC,SAAS,QAAQ,EAAE,UAAU;AACvD,mBAAW;AAEb,UAAI,CAAC,SAAS,QAAQ;AACpB,eAAO;AAET,UAAI,UAAU,SAAS,QAAQ,EAAE,UAAU,KAAK;AAEhD,UAAI;AACF,iBAAS,SAAS;AAChB,oBAAU,QAAQ,QAAQ,MAAM,KAAK;AAEzC,aAAO;AAAA,IACT;AAgBA,aAAS,uBAAuB,OAAO,QAAQ,IAAI;AACjD,UAAI,OAAO,MAAM,SAAS,OAAO,UAAU,YAAY,KAAK,KAAK,KAAK,OACpE,OAAO,QAAQ;AACjB,UAAI,OAAO,IACT,SAAS,MAAM,KACf,MAAM,MAAM;AAGd,UAAI,MAAM,IAAI,WAAW,MAAM,GAAG,MAAM,MACtC,MAAM,IAAI,WAAW,MAAM,MAAM,CAAC,MAAM;AACxC,eAAO;AAET,mBAAa,MAAM,MAAM;AACzB,iBAAW,MAAM,GAAG,QAAQ,eAAe,OAAO,MAAM,MAAM,GAAG,KAAK;AAGtE,UAAI,WAAW;AACb,eAAO;AAET,YAAM,WAAW;AACjB,UAAI,MAAM,OAAO,MAAM,IAAI,WAAW,GAAG,MAAM,IAAM;AAOnD;AACA,eAAO,MAAM,KAAK,OAAO;AACvB,iBAAO,MAAM,IAAI,WAAW,GAAG;AAC/B,cAAI,CAAC,GAAG,MAAM,QAAQ,IAAI,KAAK,SAAS;AACtC;AAAA,QACJ;AACA,YAAI,OAAO;AACT,iBAAO;AAIT,gBAAQ;AACR,cAAM,MAAM,GAAG,QAAQ,qBAAqB,MAAM,KAAK,KAAK,MAAM,MAAM;AACxE,YAAI,IAAI,IAAI;AACV,iBAAO,MAAM,GAAG,cAAc,IAAI,GAAG;AACrC,cAAI,MAAM,GAAG,aAAa,IAAI,GAAG;AAC/B,kBAAM,IAAI;AAAA,UACZ,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF;AAIA,gBAAQ;AACR,eAAO,MAAM,KAAK,OAAO;AACvB,iBAAO,MAAM,IAAI,WAAW,GAAG;AAC/B,cAAI,CAAC,GAAG,MAAM,QAAQ,IAAI,KAAK,SAAS;AACtC;AAAA,QACJ;AAIA,cAAM,MAAM,GAAG,QAAQ,eAAe,MAAM,KAAK,KAAK,MAAM,MAAM;AAClE,YAAI,MAAM,OAAO,UAAU,OAAO,IAAI,IAAI;AACxC,kBAAQ,IAAI;AACZ,gBAAM,IAAI;AAIV,iBAAO,MAAM,KAAK,OAAO;AACvB,mBAAO,MAAM,IAAI,WAAW,GAAG;AAC/B,gBAAI,CAAC,GAAG,MAAM,QAAQ,IAAI,KAAK,SAAS;AACtC;AAAA,UACJ;AAAA,QACF,OAAO;AACL,kBAAQ;AAAA,QACV;AAEA,YAAI,OAAO,OAAO,MAAM,IAAI,WAAW,GAAG,MAAM,IAAM;AACpD,gBAAM,MAAM;AACZ,iBAAO;AAAA,QACT;AACA;AAAA,MACF,OAAO;AAIL,YAAI,OAAO,MAAM,IAAI,eAAe;AAClC,iBAAO;AAET,YAAI,MAAM,OAAO,MAAM,IAAI,WAAW,GAAG,MAAM,IAAM;AACnD,kBAAQ,MAAM;AACd,gBAAM,MAAM,GAAG,QAAQ,eAAe,OAAO,GAAG;AAChD,cAAI,OAAO,GAAG;AACZ,oBAAQ,MAAM,IAAI,MAAM,OAAO,KAAK;AAAA,UACtC,OAAO;AACL,kBAAM,WAAW;AAAA,UACnB;AAAA,QACF,OAAO;AACL,gBAAM,WAAW;AAAA,QACnB;AAIA,YAAI,CAAC;AACH,kBAAQ,MAAM,IAAI,MAAM,YAAY,QAAQ;AAE9C,cAAM,MAAM,IAAI,WAAW,GAAG,MAAM,mBAAmB,KAAK,CAAC;AAC7D,YAAI,CAAC,KAAK;AACR,gBAAM,MAAM;AACZ,iBAAO;AAAA,QACT;AACA,eAAO,IAAI;AACX,gBAAQ,IAAI;AAAA,MACd;AAEA,YAAM,MAAM;AACZ,YAAM,SAAS;AAEf,UAAI;AACF,eAAO;AAIT,gBAAU,MAAM,IAAI,MAAM,YAAY,QAAQ;AAE9C,YAAM,GAAG,OAAO;AAAA,QACd;AAAA,QACA,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS,CAAC;AAAA,MACZ;AAEA,YAAM,YAAY,eAAe,IAAI;AACrC,YAAM,MAAM,aAAa,UAAU,QAAQ;AAE3C,cAAQ,MAAM,KAAK,WAAW,KAAK,CAAC;AACpC,YAAM,QAAQ,QAAQ;AAAA,QACpB,CAAC,OAAO,IAAI;AAAA,MACd;AACA,UAAI,aAAa;AACf,cAAM,KAAK,CAAC,OAAO,EAAE,CAAC;AACxB,YAAM,WAAW;AACjB,YAAM,UAAU;AAEhB,UAAI;AACF,cAAM,KAAK,CAAC,SAAS,KAAK,CAAC;AAE7B,YAAM,MAAM;AACZ,YAAM,SAAS;AACf,aAAO;AAAA,IAET;AAcA,aAAS,eAAe,KAAK;AAC3B,YAAM,iBAAiB,IAAI,MAAM,aAAa;AAC9C,UAAI,mBAAmB;AACrB,eAAO;AACT,YAAM,YAAY,eAAe,CAAC;AAClC,UAAI,qBAAqB,QAAQ,UAAU,YAAY,CAAC,KAAK;AAC3D,eAAO;AAAA,eACA,qBAAqB,QAAQ,UAAU,YAAY,CAAC,KAAK;AAChE,eAAO;AAAA;AAEP,eAAO;AAAA,IACX;AAoBA,aAAS,YAAY,QAAQ,KAAK,SAAS,KAAK,IAAI;AAClD,YAAM,QAAQ,OAAO,GAAG;AACxB,YAAM,OAAO,MAAM;AACnB,UAAI,SAAS,WAAW,SAAS;AAC/B,eAAO;AACT,UAAI,QAAQ,QAAQ,WAAW,GAAG,IAAI,OAAO,EAAE,KAAK;AACpD,UAAI;AACF,gBAAQ,MAAM;AAGhB,YAAM,MAAM,MAAM,MAAM,MAAM,UAAU,KAAK,CAAC,EAAE,CAAC;AAGjD,YAAM,QAAQ,MAAM,UAAU,OAAO,KAAK,KACxC,WAAW,GAAG,MAAM,WAAW,MAAM,MAAM,MAAM,UAAU,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,MACxE;AAEF,YAAM,eACJ,UAAU,IAAI,UAAU,SAAS,IAAI,gBAAgB,IAAI,OACzD,UAAU,IAAI,UAAU,6BAA6B,CAAC,GAAG,CAAC;AAE5D,YAAM,cAAc,MAAM,UACxB,OAAO,UAAU,IAAI,UAAU,2BAA2B,CAAC,GAAG,MAAM,WAAW,MAAM,OAAO,CAAC,CAAC,IAC9F;AAEF,aAAO,IAAI,IAAI,SAAS,GAAG,IAAI,KAAK,GAAG,KAAK;AAAA,EACvC,YAAY,GAAG,WAAW;AAAA,IACxB,IAAI;AAAA,IACb;AAkBA,aAASC,YAAW,IAAI,UAAU,CAAC,GAAG;AACpC,UAAI,QAAQ;AACV,mBAAW,QAAQ;AACrB,UAAI,QAAQ;AACV,oBAAY,QAAQ;AAEtB,YAAM,aAAa,QAAQ,eAAe,SACxC,QAAQ,aACR;AACF,YAAM,aAAa,QAAQ,eAAe,SACxC,QAAQ,aACR;AAEF,SAAG,OAAO,MAAM,GAAG,SAAS,CAAC,QAAQ,WAAW,uBAAuB,QAAQ,QAAQ,EAAE,CAAC;AAE1F,SAAG,SAAS,MAAM,QAAQ,GAAG,SAAS,MAAM,QAC1C,CAAC,QAAQ,KAAK,KAAK,QAAQ;AACzB,YAAI,aAAa;AAAA,UACf;AAAA,UACA;AAAA,QACF;AACA,eAAO,YAAY,QAAQ,KAAK,KAAK,KAAK,EAAE;AAAA,MAC9C;AAAA,IACJ;AAEA,WAAO,UAAU;AAAA,MACf,YAAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;AC1VA,mBAA2B;AAC3B,yBAAuB;AACvB,qCAA2B;AAE3B,IAAI,iBAAa,mBAAAC,SAAW;AAC5B,WAAW,IAAI,yCAAU;AACzB,SAAS,SAAS,SAAS,SAAS;AAChC,MAAI,SAAS;AACT,eAAW,IAAI,OAAO;AAAA,EAC1B;AACA,SAAO,WAAW,OAAO,OAAO;AACpC;AACA,IAAI;AAAA;AAAA,EAA0B,SAAU,QAAQ;AAC5C,cAAUC,WAAU,MAAM;AAC1B,aAASA,UAAS,OAAO;AACrB,UAAI,QAAQ,OAAO,KAAK,MAAM,KAAK,KAAK;AACxC,YAAM,UAAU,MAAM,QAAQ,KAAK,KAAK;AACxC,aAAO;AAAA,IACX;AACA,IAAAA,UAAS,UAAU,UAAU,SAAU,KAAK;AACxC,WAAK,MAAM;AACX,UAAI,CAAC,KAAK;AACN;AAAA,MACJ;AACA,WAAK,QAAQ;AAAA,IACjB;AACA,IAAAA,UAAS,UAAU,qBAAqB,SAAU,WAAW;AACzD,UAAI,KAAK,MAAM,YAAY,UAAU,SAAS;AAC1C,aAAK,QAAQ;AAAA,MACjB;AAAA,IACJ;AACA,IAAAA,UAAS,UAAU,UAAU,WAAY;AACrC,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,IAAI,SAAS;AACjB,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,eAAK,KAAK,OAAO,UAAU,GAAG,SAAS,UAAU,GAAG;AACpD,eAAK,IAAI,YAAY,SAAS,SAAS,OAAO;AAE9C,cAAI,OAAO,wBAAwB,YAAY;AAE3C,gCAAoB,KAAK,KAAK;AAAA,cAC1B,YAAY;AAAA,gBACR,EAAE,MAAM,MAAM,OAAO,MAAM,SAAS,KAAK;AAAA,gBACzC,EAAE,MAAM,KAAK,OAAO,KAAK,SAAS,MAAM;AAAA,cAC5C;AAAA,YACJ,CAAC;AAAA,UACL;AACA,iBAAO;AAAA,YAAC;AAAA;AAAA,UAAY;AAAA,QACxB,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,IAAAA,UAAS,UAAU,SAAS,WAAY;AACpC,aAAQ,aAAAC,QAAe,cAAc,OAAO,EAAE,eAAe,iBAAiB,WAAW,iBAAiB,KAAK,KAAK,QAAQ,CAAC;AAAA,IACjI;AACA,IAAAD,UAAS,eAAe;AAAA,MACpB,SAAS;AAAA,MACT,SAAS;AAAA,QACL,SAAS;AAAA,MACb;AAAA,IACJ;AACA,WAAOA;AAAA,EACX,EAAE,aAAAC,QAAe,SAAS;AAAA;", "names": ["require_entities", "require_regex", "require_regex", "require_regex", "require_regex", "require_lib", "html5Media", "markdownIt", "<PERSON><PERSON>", "React__default"]}