{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/coffee/coffee.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#%\\^\\&\\*\\(\\)\\=\\$\\-\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n    comments: {\n        blockComment: ['###', '###'],\n        lineComment: '#'\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" }\n    ],\n    surroundingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" }\n    ],\n    folding: {\n        markers: {\n            start: new RegExp('^\\\\s*#region\\\\b'),\n            end: new RegExp('^\\\\s*#endregion\\\\b')\n        }\n    }\n};\nexport var language = {\n    defaultToken: '',\n    ignoreCase: true,\n    tokenPostfix: '.coffee',\n    brackets: [\n        { open: '{', close: '}', token: 'delimiter.curly' },\n        { open: '[', close: ']', token: 'delimiter.square' },\n        { open: '(', close: ')', token: 'delimiter.parenthesis' }\n    ],\n    regEx: /\\/(?!\\/\\/)(?:[^\\/\\\\]|\\\\.)*\\/[igm]*/,\n    keywords: [\n        'and',\n        'or',\n        'is',\n        'isnt',\n        'not',\n        'on',\n        'yes',\n        '@',\n        'no',\n        'off',\n        'true',\n        'false',\n        'null',\n        'this',\n        'new',\n        'delete',\n        'typeof',\n        'in',\n        'instanceof',\n        'return',\n        'throw',\n        'break',\n        'continue',\n        'debugger',\n        'if',\n        'else',\n        'switch',\n        'for',\n        'while',\n        'do',\n        'try',\n        'catch',\n        'finally',\n        'class',\n        'extends',\n        'super',\n        'undefined',\n        'then',\n        'unless',\n        'until',\n        'loop',\n        'of',\n        'by',\n        'when'\n    ],\n    // we include these common regular expressions\n    symbols: /[=><!~?&%|+\\-*\\/\\^\\.,\\:]+/,\n    escapes: /\\\\(?:[abfnrtv\\\\\"'$]|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n    // The main tokenizer for our languages\n    tokenizer: {\n        root: [\n            // identifiers and keywords\n            [/\\@[a-zA-Z_]\\w*/, 'variable.predefined'],\n            [\n                /[a-zA-Z_]\\w*/,\n                {\n                    cases: {\n                        this: 'variable.predefined',\n                        '@keywords': { token: 'keyword.$0' },\n                        '@default': ''\n                    }\n                }\n            ],\n            // whitespace\n            [/[ \\t\\r\\n]+/, ''],\n            // Comments\n            [/###/, 'comment', '@comment'],\n            [/#.*$/, 'comment'],\n            // regular expressions\n            ['///', { token: 'regexp', next: '@hereregexp' }],\n            [/^(\\s*)(@regEx)/, ['', 'regexp']],\n            [/(\\()(\\s*)(@regEx)/, ['@brackets', '', 'regexp']],\n            [/(\\,)(\\s*)(@regEx)/, ['delimiter', '', 'regexp']],\n            [/(\\=)(\\s*)(@regEx)/, ['delimiter', '', 'regexp']],\n            [/(\\:)(\\s*)(@regEx)/, ['delimiter', '', 'regexp']],\n            [/(\\[)(\\s*)(@regEx)/, ['@brackets', '', 'regexp']],\n            [/(\\!)(\\s*)(@regEx)/, ['delimiter', '', 'regexp']],\n            [/(\\&)(\\s*)(@regEx)/, ['delimiter', '', 'regexp']],\n            [/(\\|)(\\s*)(@regEx)/, ['delimiter', '', 'regexp']],\n            [/(\\?)(\\s*)(@regEx)/, ['delimiter', '', 'regexp']],\n            [/(\\{)(\\s*)(@regEx)/, ['@brackets', '', 'regexp']],\n            [/(\\;)(\\s*)(@regEx)/, ['', '', 'regexp']],\n            // delimiters\n            [\n                /}/,\n                {\n                    cases: {\n                        '$S2==interpolatedstring': {\n                            token: 'string',\n                            next: '@pop'\n                        },\n                        '@default': '@brackets'\n                    }\n                }\n            ],\n            [/[{}()\\[\\]]/, '@brackets'],\n            [/@symbols/, 'delimiter'],\n            // numbers\n            [/\\d+[eE]([\\-+]?\\d+)?/, 'number.float'],\n            [/\\d+\\.\\d+([eE][\\-+]?\\d+)?/, 'number.float'],\n            [/0[xX][0-9a-fA-F]+/, 'number.hex'],\n            [/0[0-7]+(?!\\d)/, 'number.octal'],\n            [/\\d+/, 'number'],\n            // delimiter: after number because of .\\d floats\n            [/[,.]/, 'delimiter'],\n            // strings:\n            [/\"\"\"/, 'string', '@herestring.\"\"\"'],\n            [/'''/, 'string', \"@herestring.'''\"],\n            [\n                /\"/,\n                {\n                    cases: {\n                        '@eos': 'string',\n                        '@default': { token: 'string', next: '@string.\"' }\n                    }\n                }\n            ],\n            [\n                /'/,\n                {\n                    cases: {\n                        '@eos': 'string',\n                        '@default': { token: 'string', next: \"@string.'\" }\n                    }\n                }\n            ]\n        ],\n        string: [\n            [/[^\"'\\#\\\\]+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/\\./, 'string.escape.invalid'],\n            [/\\./, 'string.escape.invalid'],\n            [\n                /#{/,\n                {\n                    cases: {\n                        '$S2==\"': {\n                            token: 'string',\n                            next: 'root.interpolatedstring'\n                        },\n                        '@default': 'string'\n                    }\n                }\n            ],\n            [\n                /[\"']/,\n                {\n                    cases: {\n                        '$#==$S2': { token: 'string', next: '@pop' },\n                        '@default': 'string'\n                    }\n                }\n            ],\n            [/#/, 'string']\n        ],\n        herestring: [\n            [\n                /(\"\"\"|''')/,\n                {\n                    cases: {\n                        '$1==$S2': { token: 'string', next: '@pop' },\n                        '@default': 'string'\n                    }\n                }\n            ],\n            [/[^#\\\\'\"]+/, 'string'],\n            [/['\"]+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/\\./, 'string.escape.invalid'],\n            [/#{/, { token: 'string.quote', next: 'root.interpolatedstring' }],\n            [/#/, 'string']\n        ],\n        comment: [\n            [/[^#]+/, 'comment'],\n            [/###/, 'comment', '@pop'],\n            [/#/, 'comment']\n        ],\n        hereregexp: [\n            [/[^\\\\\\/#]+/, 'regexp'],\n            [/\\\\./, 'regexp'],\n            [/#.*$/, 'comment'],\n            ['///[igm]*', { token: 'regexp', next: '@pop' }],\n            [/\\//, 'regexp']\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,aAAa;AAAA,EACb,UAAU;AAAA,IACN,cAAc,CAAC,OAAO,KAAK;AAAA,IAC3B,aAAa;AAAA,EACjB;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,SAAS;AAAA,IACL,SAAS;AAAA,MACL,OAAO,IAAI,OAAO,iBAAiB;AAAA,MACnC,KAAK,IAAI,OAAO,oBAAoB;AAAA,IACxC;AAAA,EACJ;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,UAAU;AAAA,IACN,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,EAC5D;AAAA,EACA,OAAO;AAAA,EACP,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA;AAAA,EAEA,SAAS;AAAA,EACT,SAAS;AAAA;AAAA,EAET,WAAW;AAAA,IACP,MAAM;AAAA;AAAA,MAEF,CAAC,kBAAkB,qBAAqB;AAAA,MACxC;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,MAAM;AAAA,YACN,aAAa,EAAE,OAAO,aAAa;AAAA,YACnC,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,CAAC,cAAc,EAAE;AAAA;AAAA,MAEjB,CAAC,OAAO,WAAW,UAAU;AAAA,MAC7B,CAAC,QAAQ,SAAS;AAAA;AAAA,MAElB,CAAC,OAAO,EAAE,OAAO,UAAU,MAAM,cAAc,CAAC;AAAA,MAChD,CAAC,kBAAkB,CAAC,IAAI,QAAQ,CAAC;AAAA,MACjC,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA,MACjD,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA,MACjD,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA,MACjD,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA,MACjD,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA,MACjD,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA,MACjD,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA,MACjD,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA,MACjD,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA,MACjD,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA,MACjD,CAAC,qBAAqB,CAAC,IAAI,IAAI,QAAQ,CAAC;AAAA;AAAA,MAExC;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,2BAA2B;AAAA,cACvB,OAAO;AAAA,cACP,MAAM;AAAA,YACV;AAAA,YACA,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,YAAY,WAAW;AAAA;AAAA,MAExB,CAAC,uBAAuB,cAAc;AAAA,MACtC,CAAC,4BAA4B,cAAc;AAAA,MAC3C,CAAC,qBAAqB,YAAY;AAAA,MAClC,CAAC,iBAAiB,cAAc;AAAA,MAChC,CAAC,OAAO,QAAQ;AAAA;AAAA,MAEhB,CAAC,QAAQ,WAAW;AAAA;AAAA,MAEpB,CAAC,OAAO,UAAU,iBAAiB;AAAA,MACnC,CAAC,OAAO,UAAU,iBAAiB;AAAA,MACnC;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ;AAAA,YACR,YAAY,EAAE,OAAO,UAAU,MAAM,YAAY;AAAA,UACrD;AAAA,QACJ;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ;AAAA,YACR,YAAY,EAAE,OAAO,UAAU,MAAM,YAAY;AAAA,UACrD;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,MACJ,CAAC,cAAc,QAAQ;AAAA,MACvB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,MAAM,uBAAuB;AAAA,MAC9B,CAAC,MAAM,uBAAuB;AAAA,MAC9B;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,UAAU;AAAA,cACN,OAAO;AAAA,cACP,MAAM;AAAA,YACV;AAAA,YACA,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,WAAW,EAAE,OAAO,UAAU,MAAM,OAAO;AAAA,YAC3C,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,CAAC,KAAK,QAAQ;AAAA,IAClB;AAAA,IACA,YAAY;AAAA,MACR;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,WAAW,EAAE,OAAO,UAAU,MAAM,OAAO;AAAA,YAC3C,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,CAAC,aAAa,QAAQ;AAAA,MACtB,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,MAAM,uBAAuB;AAAA,MAC9B,CAAC,MAAM,EAAE,OAAO,gBAAgB,MAAM,0BAA0B,CAAC;AAAA,MACjE,CAAC,KAAK,QAAQ;AAAA,IAClB;AAAA,IACA,SAAS;AAAA,MACL,CAAC,SAAS,SAAS;AAAA,MACnB,CAAC,OAAO,WAAW,MAAM;AAAA,MACzB,CAAC,KAAK,SAAS;AAAA,IACnB;AAAA,IACA,YAAY;AAAA,MACR,CAAC,aAAa,QAAQ;AAAA,MACtB,CAAC,OAAO,QAAQ;AAAA,MAChB,CAAC,QAAQ,SAAS;AAAA,MAClB,CAAC,aAAa,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,MAC/C,CAAC,MAAM,QAAQ;AAAA,IACnB;AAAA,EACJ;AACJ;", "names": []}