Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=exports.Cell=void 0;var _vue=require("vue"),_xeUtils=_interopRequireDefault(require("xe-utils")),_ui=require("../../ui"),_utils=require("../../ui/src/utils"),_dom=require("../../ui/src/dom"),_util=require("./util"),_vn=require("../../ui/src/vn");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let{getI18n,getIcon,renderer,formats,renderEmptyElement}=_ui.VxeUI;function renderTitlePrefixIcon(l){let{$table:t,column:e}=l,r=e.titlePrefix||e.titleHelp;return r?(0,_vue.h)("span",{class:["vxe-cell-title-prefix-icon",r.iconStatus?"theme--"+r.iconStatus:""],onMouseenter(e){t.triggerHeaderTitleEvent(e,r,l)},onMouseleave(e){t.handleTargetLeaveEvent(e)}},[(0,_vue.h)("i",{class:r.icon||getIcon().TABLE_TITLE_PREFIX})]):renderEmptyElement(t)}function renderTitleSuffixIcon(l){let{$table:t,column:e}=l,r=e.titleSuffix;return r?(0,_vue.h)("span",{class:["vxe-cell-title-suffix-icon",r.iconStatus?"theme--"+r.iconStatus:""],onMouseenter(e){t.triggerHeaderTitleEvent(e,r,l)},onMouseleave(e){t.handleTargetLeaveEvent(e)}},[(0,_vue.h)("i",{class:r.icon||getIcon().TABLE_TITLE_SUFFIX})]):renderEmptyElement(t)}function renderCellDragIcon(l){let{$table:t,column:e}=l;var r=t.context,r=r.slots,n=e.slots,a=t.props.dragConfig,o=t.getComputeMaps().computeRowDragOpts,{icon:o,trigger:d,disabledMethod:s}=o.value,s=s||(a?a.rowDisabledMethod:null);let i=s&&s(l);s=(n?n.rowDragIcon||n["row-drag-icon"]:null)||r.rowDragIcon||r["row-drag-icon"],n={};return"cell"!==d&&(n.onMousedown=e=>{i||t.handleCellDragMousedownEvent(e,l)},n.onMouseup=t.handleCellDragMouseupEvent),(0,_vue.h)("span",Object.assign({key:"dg",class:["vxe-cell--drag-handle",{"is--disabled":i}]},n),s?t.callSlot(s,l):[(0,_vue.h)("i",{class:o||(a?a.rowIcon:"")||getIcon().TABLE_DRAG_ROW})])}function renderCellBaseVNs(e,l){var{$table:t,column:r,level:n}=e,r=r.dragSort,{treeConfig:a,dragConfig:o}=t.props,{computeRowOpts:t,computeRowDragOpts:d,computeTreeOpts:s}=t.getComputeMaps(),t=t.value,d=d.value,s=s.value,{showIcon:d,isPeerDrag:i,isCrossDrag:c,visibleMethod:u}=d,u=u||(o?o.rowVisibleMethod:null),p=[];return r&&t.drag&&(d||o&&o.showRowIcon)&&(!u||u(e))&&(!a||s.transform&&(i||c||!n))&&p.push(renderCellDragIcon(e)),p.concat(_xeUtils.default.isArray(l)?l:[l])}function renderHeaderCellDragIcon(t){let{$table:r,column:e}=t;var n=r.context,n=n.slots,a=e.slots,{computeColumnOpts:o,computeColumnDragOpts:d}=r.getComputeMaps(),o=o.value,{showIcon:d,icon:s,trigger:i,isPeerDrag:l,isCrossDrag:c,visibleMethod:u,disabledMethod:p}=d.value;if(!o.drag||!d||u&&!u(t)||e.fixed||!l&&!c&&e.parentId)return renderEmptyElement(r);{let l=p&&p(t);o=(a?a.columnDragIcon||a["column-drag-icon"]:null)||n.columnDragIcon||n["column-drag-icon"],d={};return"cell"!==i&&(d.onMousedown=e=>{l||r.handleHeaderCellDragMousedownEvent(e,t)},d.onMouseup=r.handleHeaderCellDragMouseupEvent),(0,_vue.h)("span",Object.assign({key:"dg",class:["vxe-cell--drag-handle",{"is--disabled":l}]},d),o?r.callSlot(o,t):[(0,_vue.h)("i",{class:s||getIcon().TABLE_DRAG_COLUMN})])}}function renderHeaderCellBaseVNs(e,l){return[renderTitlePrefixIcon(e),renderHeaderCellDragIcon(e),..._xeUtils.default.isArray(l)?l:[l],renderTitleSuffixIcon(e)]}function renderTitleContent(l,e){let{$table:t,column:r}=l;var n=t.props;let a=t.reactData;var o=t.getComputeMaps().computeTooltipOpts,n=n.showHeaderOverflow,{type:d,showHeaderOverflow:s}=r;let i=o.value.showAll;o=_xeUtils.default.isUndefined(s)||_xeUtils.default.isNull(s)?n:s;let c="title"===o,u=!0===o||"tooltip"===o;n={};return(c||u||i)&&(n.onMouseenter=e=>{a.isDragResize||(c?(0,_dom.updateCellTitle)(e.currentTarget,r):(u||i)&&t.triggerHeaderTooltipEvent(e,l))}),(u||i)&&(n.onMouseleave=e=>{a.isDragResize||(u||i)&&t.handleTargetLeaveEvent(e)}),["html"===d&&_xeUtils.default.isString(e)?(0,_vue.h)("span",Object.assign({class:"vxe-cell--title",innerHTML:e},n)):(0,_vue.h)("span",Object.assign({class:"vxe-cell--title"},n),(0,_vn.getSlotVNs)(e))]}function getFooterContent(e){var{$table:l,column:t,_columnIndex:r,items:n,row:a}=e,{slots:o,editRender:d,cellRender:s,footerFormatter:i}=t,d=d||s,s=o?o.footer:null;if(s)return l.callSlot(s,e);let c="";c=_xeUtils.default.isArray(n)?n[r]:_xeUtils.default.get(a,t.field);o=Object.assign(e,{itemValue:c});if(i)return _xeUtils.default.isFunction(i)?[(0,_vue.h)("span",{class:"vxe-cell--label"},""+i(o))]:(n=(s=(l=_xeUtils.default.isArray(i))?formats.get(i[0]):formats.get(i))?s.tableFooterCellFormatMethod:null)?[(0,_vue.h)("span",{class:"vxe-cell--label"},""+(l?n(o,...i.slice(1)):n(o)))]:[(0,_vue.h)("span",{class:"vxe-cell--label"},"")];if(d){r=renderer.get(d.name);if(r){a=r.renderTableFooter||r.renderFooter;if(a)return(0,_vn.getSlotVNs)(a(d,o))}}return[(0,_vue.h)("span",{class:"vxe-cell--label"},(0,_utils.formatText)(c,1))]}function getDefaultCellLabel(e){var{$table:e,row:l,column:t}=e;return(0,_utils.formatText)(e.getCellLabel(l,t),1)}function renderCellHandle(e){var{column:l,row:t,$table:r}=e,n=r.props,a=r.reactData.isRowGroupStatus,n=n.editConfig,{type:o,treeNode:d,rowGroupNode:s,editRender:i}=l,{computeEditOpts:r,computeCheckboxOpts:c,computeAggregateOpts:u}=r.getComputeMaps(),u=u.value.mode,p=c.value,c=r.value,C=d||a&&("column"===u?l.field===t.groupField:s);switch(o){case"seq":return C?Cell.renderDeepIndexCell(e):Cell.renderSeqCell(e);case"radio":return C?Cell.renderDeepRadioCell(e):Cell.renderRadioCell(e);case"checkbox":return p.checkField?C?Cell.renderDeepSelectionCellByProp(e):Cell.renderCheckboxCellByProp(e):C?Cell.renderDeepSelectionCell(e):Cell.renderCheckboxCell(e);case"expand":return Cell.renderExpandCell(e);case"html":return C?Cell.renderDeepHTMLCell(e):Cell.renderHTMLCell(e)}return(0,_utils.isEnableConf)(n)&&i?"cell"===c.mode?C?Cell.renderDeepCellEdit(e):Cell.renderCellEdit(e):C?Cell.renderDeepRowEdit(e):Cell.renderRowEdit(e):C?Cell.renderDeepCell(e):Cell.renderDefaultCell(e)}function renderHeaderHandle(e){var{column:l,$table:t}=e,t=t.props.editConfig,{type:l,filters:r,sortable:n,editRender:a}=l;switch(l){case"seq":return Cell.renderSeqHeader(e);case"radio":return Cell.renderRadioHeader(e);case"checkbox":return Cell.renderCheckboxHeader(e);case"html":if(r&&n)return Cell.renderSortAndFilterHeader(e);if(n)return Cell.renderSortHeader(e);if(r)return Cell.renderFilterHeader(e)}return t&&a?Cell.renderEditHeader(e):r&&n?Cell.renderSortAndFilterHeader(e):n?Cell.renderSortHeader(e):r?Cell.renderFilterHeader(e):Cell.renderDefaultHeader(e)}function renderFooterHandle(e){return Cell.renderDefaultFooter(e)}let Cell=exports.Cell={createColumn(e,l){var t=l.type,r={renderHeader:renderHeaderHandle,renderCell:renderCellHandle,renderFooter:renderFooterHandle};return"expand"===t&&(r.renderData=Cell.renderExpandData),(0,_util.createColumn)(e,l,r)},renderHeaderTitle(e){var{$table:l,column:t}=e,{slots:r,editRender:n,cellRender:a}=t,n=n||a,a=r?r.header:null;if(a)return renderTitleContent(e,l.callSlot(a,e));if(n){r=renderer.get(n.name);if(r){l=r.renderTableHeader||r.renderHeader;if(l)return renderTitleContent(e,(0,_vn.getSlotVNs)(l(n,e)))}}return renderTitleContent(e,(0,_utils.formatText)(t.getTitle(),1))},renderDefaultHeader(e){return renderHeaderCellBaseVNs(e,Cell.renderHeaderTitle(e))},renderDefaultCell(e){var{$table:l,row:t,column:r}=e,n=l.reactData.isRowGroupStatus,{field:a,slots:o,editRender:d,cellRender:s,rowGroupNode:i,aggFunc:c}=r,s=d||s,o=o?o.default:null;let u="";if(n&&a&&t.isAggregate){var n=t,p=l.internalData.fullColumnFieldData,C=l.getComputeMaps().computeAggregateOpts,C=C.value,{mode:g,showTotal:v,totalMethod:h,countFields:_,contentMethod:b,mapChildrenField:E}=C,C=C.aggregateMethod||C.countMethod,m=n.groupField,x=n.groupContent,E=E&&n[E]||[],f=n.childCount,p=p[m]||{},m={$table:l,groupField:m,groupColumn:p?p.column:null,column:r,groupValue:x,children:E,childCount:f,aggValue:null,totalValue:f};("column"===g?a===n.groupField:i)?(u=x,b&&(u=""+b(m)),v&&(u=getI18n("vxe.table.rowGroupContentTotal",[u,h?h(m):f,f]))):l.getPivotTableAggregateCellAggValue?u=l.getPivotTableAggregateCellAggValue(e):(!0===c||_&&_.includes(a))&&C&&(m.aggValue=f,u=""+C(m))}else{if(o)return renderCellBaseVNs(e,l.callSlot(o,e));if(s){p=renderer.get(s.name);if(p){E=p.renderTableCell||p.renderCell,g=p.renderTableDefault||p.renderDefault,n=d?E:g;if(n)return renderCellBaseVNs(e,(0,_vn.getSlotVNs)(n(s,Object.assign({$type:d?"edit":"cell"},e))))}}u=l.getCellLabel(t,r)}i=d?d.placeholder:"";return renderCellBaseVNs(e,[(0,_vue.h)("span",{class:"vxe-cell--label"},[d&&(0,_utils.eqEmptyValue)(u)?(0,_vue.h)("span",{class:"vxe-cell--placeholder"},(0,_utils.formatText)((0,_utils.getFuncText)(i),1)):(0,_vue.h)("span",(0,_utils.formatText)(u,1))])])},renderDeepCell(e){return Cell.renderDeepNodeBtn(e,Cell.renderDefaultCell(e))},renderDefaultFooter(e){return getFooterContent(e)},renderRowGroupBtn(l,e){let t=l.$table;var r=t.reactData,n=t.internalData,{row:a,level:o}=l,d=t.getComputeMaps().computeAggregateOpts,r=r.rowGroupExpandedFlag,n=n.rowGroupExpandedMaps,{padding:d,indent:s}=d.value,i=(0,_util.getRowid)(t,a),r=!!r&&!!n[i];return(0,_vue.h)("div",{class:["vxe-row-group--tree-node",{"is--expanded":r}],style:d&&s?{paddingLeft:o*s+"px"}:void 0},[a.isAggregate?(0,_vue.h)("span",{class:"vxe-row-group--node-btn",onClick(e){t.triggerRowGroupExpandEvent(e,l)}},[(0,_vue.h)("i",{class:r?getIcon().TABLE_ROW_GROUP_OPEN:getIcon().TABLE_ROW_GROUP_CLOSE})]):renderEmptyElement(t),(0,_vue.h)("div",{class:"vxe-row-group-cell"},e)])},renderTreeNodeBtn(l,e){let{$table:t,isHidden:r}=l;var n=t.reactData,a=t.internalData,{row:o,column:d,level:s}=l,d=d.slots,d=d?d.icon:null;if(d)return t.callSlot(d,l);var d=t.getComputeMaps().computeTreeOpts,n=n.treeExpandedFlag,{fullAllDataRowIdData:a,treeExpandedMaps:i,treeExpandLazyLoadedMaps:c}=a,d=d.value,{padding:u,indent:p,lazy:C,trigger:g,iconLoaded:v,showIcon:h,iconOpen:_,iconClose:b}=d,E=d.children||d.childrenField,d=d.hasChild||d.hasChildField,E=o[E],E=E&&E.length;let m=!1,x=!1,f=!1,D=!1;var T,w={};return r||(T=(0,_util.getRowid)(t,o),x=!!n&&!!i[T],C&&(n=a[T],f=!!c[T],m=o[d],D=!!n.treeLoaded)),g&&"default"!==g||(w.onClick=e=>{t.triggerTreeExpandEvent(e,l)}),(0,_vue.h)("div",{class:["vxe-cell--tree-node",{"is--active":x}],style:u&&p?{paddingLeft:s*p+"px"}:void 0},[h&&(!C||D?E:E||m)?[(0,_vue.h)("div",Object.assign({class:"vxe-cell--tree-btn"},w),[(0,_vue.h)("i",{class:f?v||getIcon().TABLE_TREE_LOADED:x?_||getIcon().TABLE_TREE_OPEN:b||getIcon().TABLE_TREE_CLOSE})])]:null,(0,_vue.h)("div",{class:"vxe-tree-cell"},e)])},renderDeepNodeBtn(e,l){var{$table:t,row:r,column:n}=e,a=n.rowGroupNode,o=t.reactData.rowGroupList;if(o.length){o=t.getComputeMaps().computeAggregateOpts,t=o.value.mode;if("column"===t?n.field===r.groupField:a)return[Cell.renderRowGroupBtn(e,l)]}return[Cell.renderTreeNodeBtn(e,l)]},renderSeqHeader(e){var{$table:l,column:t}=e,r=t.slots,r=r?r.header:null;return renderHeaderCellBaseVNs(e,renderTitleContent(e,r?l.callSlot(r,e):(0,_utils.formatText)(t.getTitle(),1)))},renderSeqCell(e){var{$table:l,column:t}=e,r=l.props.treeConfig,n=l.getComputeMaps().computeSeqOpts,n=n.value,t=t.slots,t=t?t.default:null;return t?renderCellBaseVNs(e,l.callSlot(t,e)):(l=e.seq,t=n.seqMethod,renderCellBaseVNs(e,[(0,_vue.h)("span",""+(0,_utils.formatText)(t?t(e):r?l:(n.startIndex||0)+l,1))]))},renderDeepIndexCell(e){return Cell.renderDeepNodeBtn(e,Cell.renderSeqCell(e))},renderRadioHeader(e){var{$table:l,column:t}=e,r=t.slots,n=r?r.header:null,r=r?r.title:null;return renderHeaderCellBaseVNs(e,renderTitleContent(e,n?l.callSlot(n,e):[(0,_vue.h)("span",{class:"vxe-radio--label"},r?l.callSlot(r,e):(0,_utils.formatText)(t.getTitle(),1))]))},renderRadioCell(l){let{$table:t,column:e,isHidden:r}=l;var n=t.reactData,a=t.getComputeMaps().computeRadioOpts,n=n.selectRadioRow,o=e.slots,{labelField:a,checkMethod:d,visibleMethod:s}=a.value,i=l.row,c=o?o.default:null,o=o?o.radio:null,n=t.eqRow(i,n);let u=!s||s({$table:t,row:i}),p=!!d,C;r||(C={onClick(e){!p&&u&&t.triggerRadioRowEvent(e,l)}},d&&(p=!d({$table:t,row:i})));s=Object.assign(Object.assign({},l),{checked:n,disabled:p,visible:u});return o?renderCellBaseVNs(l,t.callSlot(o,s)):(d=[],u&&d.push((0,_vue.h)("span",{class:["vxe-radio--icon",n?getIcon().TABLE_RADIO_CHECKED:getIcon().TABLE_RADIO_UNCHECKED]})),(c||a)&&d.push((0,_vue.h)("span",{class:"vxe-radio--label"},c?t.callSlot(c,s):_xeUtils.default.get(i,a))),renderCellBaseVNs(l,[(0,_vue.h)("span",Object.assign({class:["vxe-cell--radio",{"is--checked":n,"is--disabled":p}]},C),d)]))},renderDeepRadioCell(e){return Cell.renderDeepNodeBtn(e,Cell.renderRadioCell(e))},renderCheckboxHeader(e){let{$table:l,column:t,isHidden:r}=e;var n=l.reactData,{computeIsAllCheckboxDisabled:a,computeCheckboxOpts:o}=l.getComputeMaps();let{isAllSelected:d,isIndeterminate:s}=n,i=a.value;var n=t.slots,a=n?n.header:null,n=n?n.title:null,{checkStrictly:o,showHeader:c,headerTitle:u}=o.value,p=t.getTitle(),C={},g=(r||(C.onClick=e=>{i||l.triggerCheckAllEvent(e,!d)}),Object.assign(Object.assign({},e),{checked:d,disabled:i,indeterminate:s}));return renderHeaderCellBaseVNs(e,renderTitleContent(g,a?l.callSlot(a,g):(o?c:!1!==c)?[(0,_vue.h)("span",Object.assign({class:["vxe-cell--checkbox",{"is--checked":d,"is--disabled":i,"is--indeterminate":s}],title:_xeUtils.default.eqNull(u)?getI18n("vxe.table.allTitle"):""+(u||"")},C),[(0,_vue.h)("span",{class:["vxe-checkbox--icon",s?getIcon().TABLE_CHECKBOX_INDETERMINATE:d?getIcon().TABLE_CHECKBOX_CHECKED:getIcon().TABLE_CHECKBOX_UNCHECKED]})].concat(n||p?[(0,_vue.h)("span",{class:"vxe-checkbox--label"},n?l.callSlot(n,g):p)]:[]))]:[(0,_vue.h)("span",{class:"vxe-checkbox--label"},n?l.callSlot(n,g):p)]))},renderCheckboxCell(l){let{$table:t,row:e,column:r,isHidden:n}=l;var a=t.props.treeConfig,{updateCheckboxFlag:o,isRowGroupStatus:d}=t.reactData,{selectCheckboxMaps:s,treeIndeterminateRowMaps:i}=t.internalData,c=t.getComputeMaps().computeCheckboxOpts,{labelField:c,checkMethod:u,visibleMethod:p}=c.value,C=r.slots,g=C?C.default:null,C=C?C.checkbox:null;let v=!1,h=!1,_=!p||p({$table:t,row:e}),b=!!u;var E,p={},o=(!n&&(E=(0,_util.getRowid)(t,e),h=!!o&&!!s[E],p.onClick=e=>{!b&&_&&t.triggerCheckRowEvent(e,l,!h)},u&&(b=!u({$table:t,row:e})),a||d)&&(v=!!i[E]),Object.assign(Object.assign({},l),{checked:h,disabled:b,visible:_,indeterminate:v}));return C?renderCellBaseVNs(l,t.callSlot(C,o)):(s=[],_&&s.push((0,_vue.h)("span",{class:["vxe-checkbox--icon",v?getIcon().TABLE_CHECKBOX_INDETERMINATE:h?getIcon().TABLE_CHECKBOX_CHECKED:getIcon().TABLE_CHECKBOX_UNCHECKED]})),(g||c)&&s.push((0,_vue.h)("span",{class:"vxe-checkbox--label"},g?t.callSlot(g,o):_xeUtils.default.get(e,c))),renderCellBaseVNs(l,[(0,_vue.h)("span",Object.assign({class:["vxe-cell--checkbox",{"is--checked":h,"is--disabled":b,"is--indeterminate":v,"is--hidden":!_}]},p),s)]))},renderDeepSelectionCell(e){return Cell.renderDeepNodeBtn(e,Cell.renderCheckboxCell(e))},renderCheckboxCellByProp(l){let{$table:t,row:e,column:r,isHidden:n}=l;var a=t.props.treeConfig,{updateCheckboxFlag:o,isRowGroupStatus:d}=t.reactData,s=t.internalData.treeIndeterminateRowMaps,i=t.getComputeMaps().computeCheckboxOpts,i=i.value,{labelField:c,checkField:u,checkMethod:p,visibleMethod:C}=i,i=i.indeterminateField||i.halfField,g=r.slots,v=g?g.default:null,g=g?g.checkbox:null;let h=!1,_=!1,b=!C||C({$table:t,row:e}),E=!!p;var m,C={},o=(!n&&(m=(0,_util.getRowid)(t,e),_=!!o&&_xeUtils.default.get(e,u),C.onClick=e=>{!E&&b&&t.triggerCheckRowEvent(e,l,!_)},p&&(E=!p({$table:t,row:e})),a||d)&&(h=!!s[m]),Object.assign(Object.assign({},l),{checked:_,disabled:E,visible:b,indeterminate:h}));return g?renderCellBaseVNs(l,t.callSlot(g,o)):(u=[],b&&(u.push((0,_vue.h)("span",{class:["vxe-checkbox--icon",h?getIcon().TABLE_CHECKBOX_INDETERMINATE:_?getIcon().TABLE_CHECKBOX_CHECKED:getIcon().TABLE_CHECKBOX_UNCHECKED]})),v||c)&&u.push((0,_vue.h)("span",{class:"vxe-checkbox--label"},v?t.callSlot(v,o):_xeUtils.default.get(e,c))),renderCellBaseVNs(l,[(0,_vue.h)("span",Object.assign({class:["vxe-cell--checkbox",{"is--checked":_,"is--disabled":E,"is--indeterminate":i&&!_?e[i]:h,"is--hidden":!b}]},C),u)]))},renderDeepSelectionCellByProp(e){return Cell.renderDeepNodeBtn(e,Cell.renderCheckboxCellByProp(e))},renderExpandCell(l){let{$table:t,isHidden:e,row:r,column:n}=l;var a=t.reactData.isRowGroupStatus,{rowExpandedMaps:o,rowExpandLazyLoadedMaps:d}=t.internalData,s=t.getComputeMaps().computeExpandOpts,{lazy:s,labelField:i,iconLoaded:c,showIcon:u,iconOpen:p,iconClose:C,visibleMethod:g}=s.value,v=n.slots,h=v?v.default:null,v=v?v.icon:null;let _=!1,b=!1;return a&&r.isAggregate?renderCellBaseVNs(l,[]):v?renderCellBaseVNs(l,t.callSlot(v,l)):(e||(a=(0,_util.getRowid)(t,r),_=!!o[a],s&&(b=!!d[a])),renderCellBaseVNs(l,[!u||g&&!g(l)?renderEmptyElement(t):(0,_vue.h)("span",{class:["vxe-table--expanded",{"is--active":_}],onMousedown(e){e.stopPropagation()},onClick(e){t.triggerRowExpandEvent(e,l)}},[(0,_vue.h)("i",{class:["vxe-table--expand-btn",b?c||getIcon().TABLE_EXPAND_LOADED:_?p||getIcon().TABLE_EXPAND_OPEN:C||getIcon().TABLE_EXPAND_CLOSE]})]),h||i?(0,_vue.h)("span",{class:"vxe-table--expand-label"},h?t.callSlot(h,l):_xeUtils.default.get(r,i)):renderEmptyElement(t)]))},renderExpandData(e){var{$table:l,column:t}=e,{slots:t,contentRender:r}=t,t=t?t.content:null;if(t)return l.callSlot(t,e);if(r){l=renderer.get(r.name);if(l){t=l.renderTableExpand||l.renderExpand;if(t)return(0,_vn.getSlotVNs)(t(r,e))}}return[]},renderHTMLCell(e){var{$table:l,column:t}=e,t=t.slots,t=t?t.default:null;return renderCellBaseVNs(e,t?l.callSlot(t,e):[(0,_vue.h)("span",{class:"vxe-cell--html",innerHTML:getDefaultCellLabel(e)})])},renderDeepHTMLCell(e){return Cell.renderDeepNodeBtn(e,Cell.renderHTMLCell(e))},renderSortAndFilterHeader(e){return renderHeaderCellBaseVNs(e,Cell.renderHeaderTitle(e).concat(Cell.renderSortIcon(e).concat(Cell.renderFilterIcon(e))))},renderSortHeader(e){return renderHeaderCellBaseVNs(e,Cell.renderHeaderTitle(e).concat(Cell.renderSortIcon(e)))},renderSortIcon(e){let{$table:l,column:t}=e;var r=l.getComputeMaps().computeSortOpts,{showIcon:r,allowBtn:n,ascTitle:a,descTitle:o,iconLayout:d,iconAsc:s,iconDesc:i,iconVisibleMethod:c}=r.value,u=t.order;return!r||c&&!c(e)?[]:[(0,_vue.h)("span",{class:["vxe-cell--sort",`vxe-cell--sort-${d}-layout`]},[(0,_vue.h)("i",{class:["vxe-sort--asc-btn",s||getIcon().TABLE_SORT_ASC,{"sort--active":"asc"===u}],title:_xeUtils.default.eqNull(a)?getI18n("vxe.table.sortAsc"):""+(a||""),onClick:n?e=>{e.stopPropagation(),l.triggerSortEvent(e,t,"asc")}:void 0}),(0,_vue.h)("i",{class:["vxe-sort--desc-btn",i||getIcon().TABLE_SORT_DESC,{"sort--active":"desc"===u}],title:_xeUtils.default.eqNull(o)?getI18n("vxe.table.sortDesc"):""+(o||""),onClick:n?e=>{e.stopPropagation(),l.triggerSortEvent(e,t,"desc")}:void 0})])]},renderFilterHeader(e){return renderHeaderCellBaseVNs(e,Cell.renderHeaderTitle(e).concat(Cell.renderFilterIcon(e)))},renderFilterIcon(l){let{$table:t,column:e,hasFilter:r}=l;var n=t.reactData.filterStore,a=t.getComputeMaps().computeFilterOpts,{showIcon:a,iconNone:o,iconMatch:d,iconVisibleMethod:s}=a.value;return!a||s&&!s(l)?[]:[(0,_vue.h)("span",{class:["vxe-cell--filter",{"is--active":n.visible&&n.column===e}],onClick(e){t.triggerFilterEvent&&t.triggerFilterEvent(e,l.column,l)}},[(0,_vue.h)("i",{class:["vxe-filter--btn",r?d||getIcon().TABLE_FILTER_MATCH:o||getIcon().TABLE_FILTER_NONE],title:getI18n("vxe.table.filter")})])]},renderEditHeader(e){var{$table:l,column:t}=e,r=l.props,n=l.getComputeMaps().computeEditOpts,{editConfig:r,editRules:a}=r,n=n.value,{sortable:o,filters:d,editRender:s}=t;let i=!1,c=(a&&(a=_xeUtils.default.get(a,t.field))&&(i=a.some(e=>e.required)),[]);return renderHeaderCellBaseVNs(e,(c=(0,_utils.isEnableConf)(r)?[i&&n.showAsterisk?(0,_vue.h)("span",{class:"vxe-cell--required-icon"},[(0,_vue.h)("i")]):renderEmptyElement(l),(0,_utils.isEnableConf)(s)&&n.showIcon?(0,_vue.h)("span",{class:"vxe-cell--edit-icon"},[(0,_vue.h)("i",{class:n.icon||getIcon().TABLE_EDIT})]):renderEmptyElement(l)]:c).concat(Cell.renderHeaderTitle(e)).concat(o?Cell.renderSortIcon(e):[]).concat(d?Cell.renderFilterIcon(e):[]))},renderRowEdit(e){var{$table:l,column:t}=e,l=l.reactData.editStore,l=l.actived,t=t.editRender;return Cell.runRenderer(e,(0,_utils.isEnableConf)(t)&&l&&l.row===e.row)},renderDeepRowEdit(e){return Cell.renderDeepNodeBtn(e,Cell.renderRowEdit(e))},renderCellEdit(e){var{$table:l,column:t}=e,l=l.reactData.editStore,l=l.actived,t=t.editRender;return Cell.runRenderer(e,(0,_utils.isEnableConf)(t)&&l&&l.row===e.row&&l.column===e.column)},renderDeepCellEdit(e){return Cell.renderDeepNodeBtn(e,Cell.renderCellEdit(e))},runRenderer(e,l){var{$table:t,column:r}=e,{slots:r,editRender:n,formatter:a}=r,o=r?r.default:null,r=r?r.edit:null,d=renderer.get(n.name),d=d?d.renderTableEdit||d.renderEdit:null,s=Object.assign({$type:"",isEdit:l},e);return l?(s.$type="edit",r?t.callSlot(r,s):d?(0,_vn.getSlotVNs)(d(n,s)):[]):o?renderCellBaseVNs(e,t.callSlot(o,s)):a?renderCellBaseVNs(e,[(0,_vue.h)("span",{class:"vxe-cell--label"},getDefaultCellLabel(s))]):Cell.renderDefaultCell(s)}};var _default=exports.default=Cell;