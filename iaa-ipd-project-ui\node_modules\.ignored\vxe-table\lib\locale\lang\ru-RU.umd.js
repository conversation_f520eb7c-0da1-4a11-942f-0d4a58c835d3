(function (global, factory) {
  if (typeof define === "function" && define.amd) {
    define("vxe-table-lang.ru-RU", ["exports"], factory);
  } else if (typeof exports !== "undefined") {
    factory(exports);
  } else {
    var mod = {
      exports: {}
    };
    factory(mod.exports);
    global.vxeTableLangRuRU = mod.exports;
  }
})(typeof globalThis !== "undefined" ? globalThis : typeof self !== "undefined" ? self : this, function (_exports) {
  "use strict";

  Object.defineProperty(_exports, "__esModule", {
    value: true
  });
  _exports.default = void 0;
  var _default = _exports.default = {
    vxe: {
      base: {
        pleaseInput: 'Пожалуйста, введите',
        pleaseSelect: 'Пожалуйста, выберите',
        comma: '，',
        fullStop: '。'
      },
      loading: {
        text: 'загрузка ...'
      },
      error: {
        downErr: 'Скачать не удалась',
        errLargeData: 'Когда количество связанных данных слишком велик, пожалуйста, используйте {0}, в противном случае это может вызвать задержку',
        groupFixed: 'При использовании сгруппированных заголовков, замороженный столбец должен быть установлен группой',
        groupMouseRange: 'Заголовок группировки не может быть использован в то же время, что и «{0}», и это может вызвать ошибку',
        groupTag: 'Заголовки столбцов группировки должны использовать «{0}» вместо «{1}», что может вызвать ошибки',
        scrollErrProp: 'Этот параметр "{0}" не поддерживается после включения виртуальной прокрутки',
        errConflicts: 'Параметр "{0}" Конфликт с "{1}"',
        notSupportProp: '"{1}" не поддерживается, когда параметр "{0}"',
        notConflictProp: 'При использовании "{0}", "{1}" должен быть установлен, в противном случае могут быть функциональные конфликты',
        unableInsert: 'Нельзя вставить в указанное место, пожалуйста, проверьте, являются ли параметры правильными',
        useErr: 'Произошла ошибка при установке модуля "{0}". Заказ может быть неверным. Зависимый модуль должен быть установлен перед таблицей',
        barUnableLink: 'Панель инструментов не может связать таблицы',
        expandContent: 'Слот для расширенной строки должен быть «контентом», пожалуйста, проверьте, правильно ли это',
        reqComp: 'Компонент "{0}" отсутствует, пожалуйста, проверьте, правильно ли он установлен. https://vxeui.com/#/start/useglobal',
        reqModule: 'Отсутствует модуль "{0}"',
        reqProp: 'Необходимый параметр «{0}» отсутствует, что может вызвать ошибку',
        emptyProp: 'Параметр "{0}" не разрешается быть пустым',
        errProp: 'Неподдерживаемый параметр "{0}", возможно "{1}"',
        colRepet: 'столбец. {0} = "{1}" повторяется, что может привести к тому, что некоторые функции станут непригодными для использования',
        notFunc: 'Метод "{0}" не существует',
        errFunc: 'Параметр "{0}" не является методом',
        notValidators: 'Глобальная проверка "{0}" не существует',
        notFormats: 'Глобальное форматирование "{0}" не существует',
        notCommands: 'Глобальная директива "{0}" не существует',
        notSlot: 'Слот "{0}" не существует',
        noTree: '"{0}" не поддерживается в структуре дерева',
        notProp: 'Неподдерживаемый параметр "{0}"',
        checkProp: 'Когда объем данных слишком большой, флажок может быть заикается. Рекомендуется установить параметр «{0}» для улучшения скорости рендеринга',
        coverProp: 'Параметр «{1}» из «{0}» неоднократно определяется, что может вызвать ошибку',
        uniField: 'Имя поля «{0}» неоднократно определяется, что может вызвать ошибку',
        repeatKey: 'Повторите первичную клавишу {0} = "{1}", которая может вызвать ошибку',
        delFunc: 'Метод "{0}" устарел, пожалуйста, используйте "{1}"',
        delProp: 'Параметр "{0}" устарел, пожалуйста, используйте "{1}"',
        delEvent: 'Событие "{0}" устарело, пожалуйста, используйте "{1}"',
        removeProp: 'Параметр "{0}" устарел и не рекомендуется, что может вызвать ошибку',
        errFormat: 'Глобальное форматированное содержание следует определить с использованием «vxetable.formats», и метод монтажа «formatter = {0}» больше не рекомендуется.',
        notType: 'Неподдерживаемый тип файла "{0}"',
        notExp: 'Этот браузер не поддерживает функцию импорта/экспорта',
        impFields: 'Импорт не удался. Пожалуйста, проверьте, правильны ли имя поля и формат данных.',
        treeNotImp: 'Таблицы деревьев не поддерживают импорт',
        treeCrossDrag: 'Только перетащите первый уровень',
        treeDragChild: 'Родители не могут тащить своих детей',
        reqPlugin: '"{1}" не установлен по адресу https://vxeui.com/otherbe',
        errMaxRow: 'Превышение максимального поддерживаемого объема данных {0} строк, это может вызвать ошибку'
      },
      table: {
        emptyText: 'Нет данных пока',
        allTitle: 'Выберите все/отменить',
        seqTitle: 'Серийный номер',
        actionTitle: 'работать',
        confirmFilter: 'фильтр',
        resetFilter: 'Перезагрузить',
        allFilter: 'все',
        sortAsc: 'Восходящий заказ: самый низкий до самого высокого',
        sortDesc: 'Нисходящий порядок: самый высокий до самого низкого',
        filter: 'Включить фильтрацию для выбранных столбцов',
        impSuccess: 'Успешно импортированные {0} записи',
        expLoading: 'Экспорт',
        expSuccess: 'Экспорт успешно',
        expError: 'Экспорт не удался',
        expFilename: 'Export_ {0}',
        expOriginFilename: 'Export_source_ {0}',
        customTitle: 'Настройки столбца',
        customAll: 'все',
        customConfirm: 'подтверждать',
        customClose: 'закрытие',
        customCancel: 'Отмена',
        customRestore: 'Восстановить дефолт',
        maxFixedCol: 'Максимальное количество замороженных столбцов не может превышать {0}',
        dragTip: 'MOVE: {0}',
        resizeColTip: 'Ширина: {0} пиксели',
        resizeRowTip: 'Высота: {0} пиксели',
        rowGroupContentTotal: '{0} ({1})'
      },
      grid: {
        selectOneRecord: 'Пожалуйста, выберите хотя бы одну запись!',
        deleteSelectRecord: 'Вы уверены, что хотите удалить выбранную запись?',
        removeSelectRecord: 'Вы уверены, что хотите удалить выбранную запись?',
        dataUnchanged: 'Данные не изменены!',
        delSuccess: 'Выбранная запись была успешно удалена!',
        saveSuccess: 'Сохраните успешно!',
        operError: 'Произошла ошибка, и операция не удалась!'
      },
      select: {
        search: 'поиск',
        loadingText: 'загрузка',
        emptyText: 'Нет данных пока'
      },
      pager: {
        goto: 'Идти',
        gotoTitle: 'Количество страниц',
        pagesize: '{0} элементы/страница',
        total: 'Total {0} записи',
        pageClassifier: 'Страница',
        homePage: 'Первая страница',
        homePageTitle: 'Первая страница',
        prevPage: 'Предыдущая страница',
        prevPageTitle: 'Предыдущая страница',
        nextPage: 'Следующая страница',
        nextPageTitle: 'Следующая страница',
        prevJump: 'Прыгнуть вверх',
        prevJumpTitle: 'Прыгнуть вверх',
        nextJump: 'Спрыгнуть вниз по странице',
        nextJumpTitle: 'Спрыгнуть вниз по странице',
        endPage: 'Последняя страница',
        endPageTitle: 'Последняя страница'
      },
      alert: {
        title: 'Системные подсказки'
      },
      button: {
        confirm: 'подтверждать',
        cancel: 'Отмена',
        clear: 'Прозрачный'
      },
      filter: {
        search: 'поиск'
      },
      custom: {
        cstmTitle: 'Настройки столбца',
        cstmRestore: 'Восстановить дефолт',
        cstmCancel: 'Отмена',
        cstmConfirm: 'Конечно',
        cstmConfirmRestore: 'Пожалуйста, подтвердите, восстанавливается ли он в конфигурации столбца по умолчанию?',
        cstmDragTarget: 'MOVE: {0}',
        setting: {
          colSort: 'Сортировка',
          sortHelpTip: 'Нажмите и перетащите значок, чтобы настроить тип столбцов',
          colTitle: 'Заголовок столбца',
          colResizable: 'Ширина столбца (пиксели)',
          colVisible: 'Отображать ли',
          colFixed: 'Замораживание колонки',
          colFixedMax: 'Столбцы замораживания (до {0} столбцы)',
          fixedLeft: 'Левая сторона',
          fixedUnset: 'Не установлен',
          fixedRight: 'Правая сторона'
        }
      },
      import: {
        modes: {
          covering: 'Метод перезапись (непосредственно перезаписать данные таблицы)',
          insert: 'Добавить внизу (добавить новые данные внизу таблицы)',
          insertTop: 'Добавить вверху (добавить новые данные в верхней части таблицы)',
          insertBottom: 'Добавить внизу (добавить новые данные внизу таблицы)'
        },
        impTitle: 'Импорт данных',
        impFile: 'имя файла',
        impSelect: 'Выберите файл',
        impType: 'Тип файла',
        impOpts: 'Настройки параметров',
        impMode: 'Режим импорта',
        impConfirm: 'Импорт',
        impCancel: 'Отмена'
      },
      export: {
        types: {
          csv: 'CSV (запятая отделена) (*. CSV)',
          html: 'Веб -страница (*.html)',
          xml: 'Данные XML (*.xml)',
          txt: 'Текстовый файл (вкладка разделена) (*. TXT)',
          xls: 'Excel 97-2003 Workbook (*.xls)',
          xlsx: 'Excel Workbook (*.xlsx)',
          pdf: 'Pdf (*.pdf)'
        },
        modes: {
          empty: 'Пустые данные',
          current: 'Текущие данные (данные на текущей странице)',
          selected: 'Выбранные данные (данные, выбранные на текущей странице)',
          all: 'Полные данные (включая все данные на странице)'
        },
        printTitle: 'Распечатать данные',
        expTitle: 'Экспортные данные',
        expName: 'имя файла',
        expNamePlaceholder: 'Пожалуйста, введите имя файла',
        expSheetName: 'заголовок',
        expSheetNamePlaceholder: 'Пожалуйста, введите заголовок',
        expType: 'Сохранить тип',
        expMode: 'Выберите данные',
        expCurrentColumn: 'Все поля',
        expColumn: 'Выберите поле',
        expOpts: 'Настройки параметров',
        expOptHeader: 'Заголовок',
        expHeaderTitle: 'Требуется заголовок таблицы',
        expOptFooter: 'Конец таблицы',
        expFooterTitle: 'Требуется ли конец таблицы?',
        expOptColgroup: 'Группировка заголовка',
        expOptTitle: 'Заголовок столбца',
        expTitleTitle: 'Будь то заголовок столбца, в противном случае он будет отображаться как имя поля столбца',
        expColgroupTitle: 'Если присутствует, поддерживается заголовок со структурой группировки',
        expOptMerge: 'слияние',
        expMergeTitle: 'Если присутствует, клетки с объединенными структурами поддерживаются',
        expOptAllExpand: 'Расширить дерево',
        expAllExpandTitle: 'Если он существует, он поддерживается для расширения всех данных с помощью иерархических структур',
        expOptUseStyle: 'стиль',
        expUseStyleTitle: 'Если присутствует, клетки со стилем поддерживаются',
        expOptOriginal: 'Исходные данные',
        expOriginalTitle: 'Если это исходные данные, поддерживается импорт в таблицы',
        expPrint: 'Печать',
        expConfirm: 'Экспорт',
        expCancel: 'Отмена'
      },
      modal: {
        errTitle: 'Сообщение об ошибке',
        zoomMin: 'Минимизировать',
        zoomIn: 'максимизировать',
        zoomOut: 'снижение',
        close: 'закрытие',
        miniMaxSize: 'Количество минимизированных окон не может превышать {0}',
        footPropErr: 'Show-Footer используется только для включения хвоста стола и должен использоваться с Show-Confirm-Button | Show-Cancel-Button | слоты'
      },
      drawer: {
        close: 'закрытие'
      },
      form: {
        folding: 'Закрывать',
        unfolding: 'Расширять'
      },
      toolbar: {
        import: 'Импорт',
        export: 'Экспорт',
        print: 'Печать',
        refresh: 'обновлять',
        zoomIn: 'полноэкранный',
        zoomOut: 'снижение',
        custom: 'Настройки столбца',
        customAll: 'все',
        customConfirm: 'подтверждать',
        customRestore: 'Перезагрузить',
        fixedLeft: 'Заморозить',
        fixedRight: 'Заморозить правильно',
        cancelFixed: 'Размороженный'
      },
      datePicker: {
        yearTitle: '{0} Годы'
      },
      dateRangePicker: {
        pleaseRange: '请选择开始日期与结束日期'
      },
      input: {
        date: {
          m1: 'Январь',
          m2: 'Февраль',
          m3: 'Маршировать',
          m4: 'Апрель',
          m5: 'Может',
          m6: 'Июнь',
          m7: 'Июль',
          m8: 'Август',
          m9: 'Сентябрь',
          m10: 'Октябрь',
          m11: 'Ноябрь',
          m12: 'Декабрь',
          quarterLabel: '{0} Годы',
          monthLabel: '{0} Годы',
          dayLabel: '{0} Год {1}',
          labelFormat: {
            date: 'yyyy-MM-dd',
            time: 'HH:mm:ss',
            datetime: 'yyyy-MM-dd HH:mm:ss',
            week: 'Week WW of year yyyy',
            month: 'yyyy-MM',
            quarter: 'quarter q of year yyyy',
            year: 'yyyy'
          },
          weeks: {
            w: '',
            w0: 'Воскресенье',
            w1: 'в понедельник',
            w2: 'Вторник',
            w3: 'Среда',
            w4: 'Четверг',
            w5: 'Пятница',
            w6: 'Суббота'
          },
          months: {
            m0: 'Январь',
            m1: 'Февраль',
            m2: 'Маршировать',
            m3: 'Апрель',
            m4: 'Может',
            m5: 'Июнь',
            m6: 'Июль',
            m7: 'Август',
            m8: 'Сентябрь',
            m9: 'Октябрь',
            m10: 'Ноябрь',
            m11: 'Декабрь'
          },
          quarters: {
            q1: 'Первая четверть',
            q2: 'Вторая четверть',
            q3: 'Третья четверть',
            q4: 'Четвертая четверть'
          }
        }
      },
      numberInput: {
        currencySymbol: '$'
      },
      imagePreview: {
        popupTitle: 'Предварительный просмотр',
        operBtn: {
          zoomOut: 'Сокращать',
          zoomIn: 'увеличить',
          pctFull: 'Масштабирование одинаково',
          pct11: 'Показать оригинальный размер',
          rotateLeft: 'Вращаться влево',
          rotateRight: 'Вращаться вправо',
          print: 'Нажмите, чтобы распечатать изображение',
          download: 'Нажмите, чтобы загрузить картинку'
        }
      },
      upload: {
        fileBtnText: 'Нажмите или перетащите, чтобы загрузить',
        imgBtnText: 'Нажмите или перетащите, чтобы загрузить',
        dragPlaceholder: 'Пожалуйста, перетащите файл в эту область, чтобы загрузить',
        imgSizeHint: 'Листовка {0}',
        imgCountHint: 'Максимум {0} изображения',
        fileTypeHint: 'Поддержка {0} Типы файлов',
        fileSizeHint: 'Размер единого файла не превышает {0}',
        fileCountHint: 'До {0} файлы могут быть загружены',
        uploadTypeErr: 'Несоответствие типа файла!',
        overCountErr: 'Только файлы {0} могут быть выбраны наибольшим количеством!',
        overCountExtraErr: 'Максимальное количество {0} было превышено, и избыточные файлы {1} будут игнорированы!',
        overSizeErr: 'Максимальный размер файла не может превышать {0}!',
        reUpload: 'Повторная загрузка',
        uploadProgress: 'Загрузка {0}%',
        uploadErr: 'Загрузка не удалась',
        uploadSuccess: 'Загрузить успешно',
        moreBtnText: 'Больше ({0})',
        viewItemTitle: 'Нажмите, чтобы просмотреть',
        morePopup: {
          readTitle: 'Просмотр списка',
          imageTitle: 'Загрузить картинки',
          fileTitle: 'Загрузить файл'
        }
      },
      empty: {
        defText: 'Нет данных пока'
      },
      colorPicker: {
        clear: 'Прозрачный',
        confirm: 'подтверждать',
        copySuccess: 'Копировано в буфер обмена: {0}'
      },
      formDesign: {
        formName: 'Название формы',
        defFormTitle: 'Неназванная форма',
        widgetPropTab: 'Управляющие свойства',
        widgetFormTab: 'Свойства формы',
        error: {
          wdFormUni: 'Этот тип управления разрешено добавлять только один в форме',
          wdSubUni: 'Этот тип элемента управления разрешено добавлять только один в подтабл.'
        },
        styleSetting: {
          btn: 'Настройки стиля',
          title: 'Настройки стиля формы',
          layoutTitle: 'Управление',
          verticalLayout: 'Макет верхней и нижней части',
          horizontalLayout: 'Горизонтальная планировка',
          styleTitle: 'Титульный стиль',
          boldTitle: 'Название жирного шрифта',
          fontBold: 'Смелый',
          fontNormal: 'общепринятый',
          colonTitle: 'Показать толстой кишки',
          colonVisible: 'показывать',
          colonHidden: 'скрывать',
          alignTitle: 'Выравнивание',
          widthTitle: 'Ширина заголовка',
          alignLeft: 'Слева',
          alignRight: 'Справа',
          unitPx: 'Пиксели',
          unitPct: 'процент'
        },
        widget: {
          group: {
            base: 'Основные элементы управления',
            layout: 'Управление макета',
            system: 'Системные элементы управления',
            module: 'Модуль управления',
            chart: 'Управление диаграммой',
            advanced: 'Усовершенствованные элементы управления'
          },
          copyTitle: 'Copy_ {0}',
          component: {
            input: 'Входная коробка',
            textarea: 'Текстовое поле',
            select: 'Потянуть, чтобы выбрать',
            row: 'Одна строка и несколько столбцов',
            title: 'заголовок',
            text: 'текст',
            subtable: 'Подтел',
            VxeSwitch: 'ли',
            VxeInput: 'Входная коробка',
            VxeNumberInput: 'число',
            VxeDatePicker: 'дата',
            VxeTextarea: 'Текстовое поле',
            VxeSelect: 'Потянуть, чтобы выбрать',
            VxeTreeSelect: 'Выбор дерева',
            VxeRadioGroup: 'Радиобатовая',
            VxeCheckboxGroup: 'Флажок',
            VxeUploadFile: 'документ',
            VxeUploadImage: 'картина',
            VxeRate: 'счет',
            VxeSlider: 'слайдер'
          }
        },
        widgetProp: {
          name: 'Управляющее имя',
          placeholder: 'Быстрый',
          required: 'Требуемая проверка',
          multiple: 'Разрешено несколько вариантов',
          displaySetting: {
            name: 'Настройки отображения',
            pc: 'ПК',
            mobile: 'Мобильный',
            visible: 'показывать',
            hidden: 'скрывать'
          },
          dataSource: {
            name: 'Источник данных',
            defValue: 'Опция {0}',
            addOption: 'Добавить параметры',
            batchEditOption: 'Редактирование партии',
            batchEditTip: 'Каждая строка соответствует опции, которая поддерживает прямую копию и вставку из таблиц, Excel и WPS.',
            batchEditSubTip: 'Каждая строка соответствует опции. Если это группа, дочерние предметы могут начинаться с места или клавиши вкладки, и он поддерживает прямую копию и вставку из таблиц, Excel и WPS.',
            buildOption: 'Варианты сборки'
          },
          rowProp: {
            colSize: 'Количество столбцов',
            col2: 'Два столбца',
            col3: 'Три столбца',
            col4: 'Четыре столбца',
            col6: 'Шесть столбцов',
            layout: 'макет'
          },
          textProp: {
            name: 'содержание',
            alignTitle: 'Выравнивание',
            alignLeft: 'Слева',
            alignCenter: 'Центр',
            alignRight: 'Справа',
            colorTitle: 'Цвет шрифта',
            sizeTitle: 'Размер шрифта',
            boldTitle: 'Смелый шрифт',
            fontNormal: 'общепринятый',
            fontBold: 'Смелый'
          },
          subtableProp: {
            seqTitle: 'Серийный номер',
            showSeq: 'Показать серийный номер',
            showCheckbox: 'Разрешено несколько вариантов',
            errSubDrag: 'Подтабл не поддерживает это управление, используйте другие элементы управления',
            colPlace: 'Перетаскивать контроль в'
          },
          uploadProp: {
            limitFileCount: 'Ограничение количества файлов',
            limitFileSize: 'Предел размера файла',
            multiFile: 'Разрешить загружать несколько файлов',
            limitImgCount: 'Ограничение количества изображений',
            limitImgSize: 'Предел размера изображения',
            multiImg: 'Разрешить загрузку нескольких изображений'
          }
        }
      },
      listDesign: {
        fieldSettingTab: 'Настройки поля',
        listSettingTab: 'Настройки параметров',
        searchTitle: 'Критерии запроса',
        listTitle: 'Список поле',
        searchField: 'Поля запроса',
        listField: 'Список поле',
        activeBtn: {
          ActionButtonUpdate: 'редактировать',
          ActionButtonDelete: 'удалить'
        },
        search: {
          addBtn: 'редактировать',
          emptyText: 'Условия запроса не настроены',
          editPopupTitle: 'Редактировать поля запроса'
        },
        searchPopup: {
          colTitle: 'заголовок',
          saveBtn: 'сохранять'
        }
      },
      text: {
        copySuccess: 'Скопировано в буфер обмена',
        copyError: 'Текущая среда не поддерживает эту операцию'
      },
      countdown: {
        formats: {
          yyyy: 'Год',
          MM: 'луна',
          dd: 'небо',
          HH: 'час',
          mm: 'точка',
          ss: 'Второй'
        }
      },
      plugins: {
        extendCellArea: {
          area: {
            mergeErr: 'Эта операция не может быть выполнена на объединенных ячейках',
            multiErr: 'Эта операция не может быть выполнена в нескольких областях выбора',
            selectErr: 'Невозможно работать на ячейках в указанном диапазоне',
            extendErr: 'Если расширенный диапазон содержит объединенные ячейки, все объединенные ячейки должны быть одинакового размера',
            pasteMultiErr: 'Невозможно вставить, скопированные и вставленные области должны быть такого же размера, чтобы выполнить эту операцию',
            cpInvalidErr: 'Операция не может быть выполнена. В выбранном вами диапазоне есть запрещенные столбцы ({0}).'
          },
          fnr: {
            title: 'Найти и заменить',
            findLabel: 'Находить',
            replaceLabel: 'заменять',
            findTitle: 'Найдите что:',
            replaceTitle: 'Заменить на:',
            tabs: {
              find: 'Находить',
              replace: 'заменять'
            },
            filter: {
              re: 'Регулярные выражения',
              whole: 'Полное сопоставление слов',
              sensitive: 'с учетом регистра'
            },
            btns: {
              findNext: 'Найдите следующее',
              findAll: 'Найти все',
              replace: 'заменять',
              replaceAll: 'Заменить все',
              cancel: 'Отмена'
            },
            header: {
              seq: '#',
              cell: 'Клетка',
              value: 'ценить'
            },
            body: {
              row: 'Ряд: {0}',
              col: 'Столбец: {0}'
            },
            empty: '(Нулевое значение)',
            reError: 'Неверное регулярное выражение',
            recordCount: '{0} Ячейки найдены',
            notCell: 'Соответствующая ячейка не может быть найдена',
            replaceSuccess: 'Успешно заменен {0} ячейки'
          }
        },
        extendPivotTable: {
          aggregation: {
            grouping: '分组',
            values: '值'
          }
        },
        filterComplexInput: {
          menus: {
            fixedColumn: 'Замораживание колонки',
            fixedGroup: 'Freeze Group',
            cancelFixed: 'Размороженный',
            fixedLeft: 'Заморозить',
            fixedRight: 'Заморозить правильно'
          },
          cases: {
            equal: 'равный',
            gt: 'Больше',
            lt: 'Меньше, чем',
            begin: 'Начало',
            endin: 'Конец',
            include: 'Включать',
            isSensitive: 'с учетом регистра'
          }
        },
        filterCombination: {
          menus: {
            sort: 'Сортировка',
            clearSort: 'Четкий вид',
            sortAsc: 'Восходящий порядок',
            sortDesc: 'нисходящий порядок',
            fixedColumn: 'Замораживание колонки',
            fixedGroup: 'Freeze Group',
            cancelFixed: 'Размороженный',
            fixedLeft: 'Заморозить',
            fixedRight: 'Заморозить правильно',
            clearFilter: 'Чистый фильтр',
            textOption: 'Текстовый фильтр',
            numberOption: 'Числовой фильтр'
          },
          popup: {
            title: 'Пользовательские методы фильтрации',
            currColumnTitle: 'Текущий столбец:',
            and: 'и',
            or: 'или',
            describeHtml: 'Доступный? Представляет один символ <br/> Использовать * представляет любые несколько символов'
          },
          cases: {
            equal: 'равный',
            unequal: 'Не равен',
            gt: 'Больше',
            ge: 'Больше или равен',
            lt: 'Меньше, чем',
            le: 'Меньше или равен',
            begin: 'Начало',
            notbegin: 'Это не в начале',
            endin: 'Конец',
            notendin: 'Окончание не',
            include: 'Включать',
            exclude: 'Не включен',
            between: 'Между',
            custom: 'Пользовательский фильтр',
            insensitive: 'Случай нечувствителен',
            isSensitive: 'с учетом регистра'
          },
          empty: '(пустой)',
          notData: 'Нет матча'
        }
      },
      pro: {
        area: {
          mergeErr: 'Эта операция не может быть выполнена на объединенных ячейках',
          multiErr: 'Эта операция не может быть выполнена в нескольких областях выбора',
          extendErr: 'Если расширенный диапазон содержит объединенные ячейки, все объединенные ячейки должны быть одинакового размера',
          pasteMultiErr: 'Невозможно вставить, скопированные и вставленные области должны быть такого же размера, чтобы выполнить эту операцию'
        },
        fnr: {
          title: 'Найти и заменить',
          findLabel: 'Находить',
          replaceLabel: 'заменять',
          findTitle: 'Найти контент:',
          replaceTitle: 'Заменить на:',
          tabs: {
            find: 'Находить',
            replace: 'заменять'
          },
          filter: {
            re: 'Регулярные выражения',
            whole: 'Полное сопоставление слов',
            sensitive: 'с учетом регистра'
          },
          btns: {
            findNext: 'Найдите следующее',
            findAll: 'Найти все',
            replace: 'заменять',
            replaceAll: 'Заменить все',
            cancel: 'Отмена'
          },
          header: {
            seq: '#',
            cell: 'Клетка',
            value: 'ценить'
          },
          empty: '(Нулевое значение)',
          reError: 'Неверное регулярное выражение',
          recordCount: '{0} Ячейки найдены',
          notCell: 'Никакой соответствующей ячейки не найдена',
          replaceSuccess: 'Успешно заменен {0} ячейки'
        }
      },
      renderer: {
        search: 'поиск',
        cases: {
          equal: 'равный',
          unequal: 'Не равен',
          gt: 'Больше',
          ge: 'Больше или равен',
          lt: 'Меньше, чем',
          le: 'Меньше или равен',
          begin: 'Начало',
          notbegin: 'Это не в начале',
          endin: 'Конец',
          notendin: 'Окончание не',
          include: 'Включать',
          exclude: 'Не включен',
          between: 'Между',
          custom: 'Пользовательский фильтр',
          insensitive: 'Случай нечувствителен',
          isSensitive: 'с учетом регистра'
        },
        combination: {
          menus: {
            sort: 'Сортировка',
            clearSort: 'Четкий вид',
            sortAsc: 'Восходящий порядок',
            sortDesc: 'нисходящий порядок',
            fixedColumn: 'Замораживание колонки',
            fixedGroup: 'Freeze Group',
            cancelFixed: 'Размороженный',
            fixedLeft: 'Заморозить',
            fixedRight: 'Заморозить правильно',
            clearFilter: 'Чистый фильтр',
            textOption: 'Текстовая фильтрация',
            numberOption: 'Численная фильтрация'
          },
          popup: {
            title: 'Пользовательские методы фильтрации',
            currColumnTitle: 'Текущий столбец:',
            and: 'и',
            or: 'или',
            describeHtml: 'Доступный? Представляет один символ <br/> Использовать * представляет любые несколько символов'
          },
          empty: '(пустой)',
          notData: 'Нет матча'
        }
      }
    }
  };
});