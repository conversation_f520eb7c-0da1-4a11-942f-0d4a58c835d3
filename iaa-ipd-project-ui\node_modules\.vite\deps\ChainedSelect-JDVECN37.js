import {
  supportStatic
} from "./chunk-JHAOQP73.js";
import {
  EnhancedSelect
} from "./chunk-GFI6XVUE.js";
import "./chunk-ZRTRFIWU.js";
import "./chunk-6C4OBBQZ.js";
import "./chunk-M5OFQAQB.js";
import {
  Spinner$1
} from "./chunk-YPPVVTGH.js";
import {
  __assign,
  __awaiter,
  __decorate,
  __extends,
  __generator,
  __metadata,
  __read,
  __rest,
  __spreadArray
} from "./chunk-F25BIIHK.js";
import {
  CustomStyle,
  OptionsControl,
  getVariable,
  isEffectiveApi,
  require_classnames,
  require_find,
  require_isEmpty,
  resolveEventData,
  setThemeClassName
} from "./chunk-LZQZ2OHM.js";
import "./chunk-QMS2VRR4.js";
import "./chunk-5QW7M2DY.js";
import "./chunk-ZJNJ4ZV3.js";
import "./chunk-KEABM62F.js";
import "./chunk-CNJUQDSN.js";
import "./chunk-YZFIV7S4.js";
import "./chunk-7XBFYOTW.js";
import {
  require_react
} from "./chunk-ZUWHLQVA.js";
import "./chunk-KNGT5NUO.js";
import "./chunk-X3JSFYRQ.js";
import {
  __toESM
} from "./chunk-GFT2G5UO.js";

// node_modules/amis/esm/renderers/Form/ChainedSelect.js
var import_react = __toESM(require_react());
var import_classnames = __toESM(require_classnames());
var import_find = __toESM(require_find());
var import_isEmpty = __toESM(require_isEmpty());
var ChainedSelectControl = (
  /** @class */
  function(_super) {
    __extends(ChainedSelectControl2, _super);
    function ChainedSelectControl2(props) {
      var _this = _super.call(this, props) || this;
      _this.state = {
        stack: []
      };
      _this.handleChange = _this.handleChange.bind(_this);
      _this.loadMore = _this.loadMore.bind(_this);
      return _this;
    }
    ChainedSelectControl2.prototype.componentDidMount = function() {
      var _a, _b;
      var formInited = this.props.formInited;
      formInited || !this.props.addHook ? this.loadMore() : (_b = (_a = this.props).addHook) === null || _b === void 0 ? void 0 : _b.call(_a, this.loadMore, "init");
    };
    ChainedSelectControl2.prototype.componentDidUpdate = function(prevProps) {
      var props = this.props;
      if (prevProps.options !== props.options) {
        this.setState({
          stack: []
        });
      } else if (props.formInited && props.value !== prevProps.value) {
        this.loadMore();
      }
    };
    ChainedSelectControl2.prototype.doAction = function(action, data, throwErrors) {
      var _a, _b;
      var _c = this.props, resetValue = _c.resetValue, onChange = _c.onChange, formStore = _c.formStore, store = _c.store, name = _c.name;
      var actionType = action === null || action === void 0 ? void 0 : action.actionType;
      if (actionType === "clear") {
        onChange("");
      } else if (actionType === "reset") {
        var pristineVal = (_b = getVariable((_a = formStore === null || formStore === void 0 ? void 0 : formStore.pristine) !== null && _a !== void 0 ? _a : store === null || store === void 0 ? void 0 : store.pristine, name)) !== null && _b !== void 0 ? _b : resetValue;
        onChange(pristineVal !== null && pristineVal !== void 0 ? pristineVal : "");
      }
    };
    ChainedSelectControl2.prototype.array2value = function(arr, isExtracted) {
      if (isExtracted === void 0) {
        isExtracted = false;
      }
      var _a = this.props, delimiter = _a.delimiter, joinValues = _a.joinValues, extractValue = _a.extractValue, valueField = _a.valueField;
      return isExtracted ? joinValues ? arr.join(delimiter || ",") : arr : joinValues ? arr.join(delimiter || ",") : extractValue ? arr.map(function(item) {
        return item[valueField || "value"] || item;
      }) : arr;
    };
    ChainedSelectControl2.prototype.loadMore = function() {
      var _this = this;
      var _a = this.props, value = _a.value, delimiter = _a.delimiter, onChange = _a.onChange, joinValues = _a.joinValues, extractValue = _a.extractValue, valueField = _a.valueField, source = _a.source, data = _a.data, env = _a.env, dispatchEvent = _a.dispatchEvent;
      var arr = Array.isArray(value) ? value.concat() : value && typeof value === "string" ? value.split(delimiter || ",") : [];
      var idx = 0;
      var len = this.state.stack.length;
      while (idx < len && arr[idx] && this.state.stack[idx].parentId == (joinValues || extractValue ? arr[idx] : arr[idx][valueField || "value"])) {
        idx++;
      }
      if (!arr[idx] || !env || !isEffectiveApi(source, data)) {
        return;
      }
      var parentId = joinValues || extractValue ? arr[idx] : arr[idx][valueField || "value"];
      var stack = this.state.stack.concat();
      stack.splice(idx, stack.length - idx);
      stack.push({
        parentId,
        loading: true,
        options: []
      });
      this.setState({
        stack
      }, function() {
        env.fetcher(source, __assign(__assign({}, data), { value: arr, level: idx + 1, parentId, parent: arr[idx] })).then(function(ret) {
          return __awaiter(_this, void 0, void 0, function() {
            var stack2, remoteValue, options, valueRes, rendererEvent;
            var _a2, _b, _c;
            return __generator(this, function(_d) {
              switch (_d.label) {
                case 0:
                  stack2 = this.state.stack.concat();
                  remoteValue = ret.data ? ret.data[valueField || "value"] : void 0;
                  options = ((_a2 = ret === null || ret === void 0 ? void 0 : ret.data) === null || _a2 === void 0 ? void 0 : _a2.options) || ((_b = ret === null || ret === void 0 ? void 0 : ret.data) === null || _b === void 0 ? void 0 : _b.items) || ((_c = ret === null || ret === void 0 ? void 0 : ret.data) === null || _c === void 0 ? void 0 : _c.rows) || ret.data || [];
                  stack2.splice(idx, stack2.length - idx);
                  if (!(typeof remoteValue !== "undefined"))
                    return [3, 2];
                  arr.splice(idx + 1, value.length - idx - 1);
                  arr.push(remoteValue);
                  valueRes = this.array2value(arr, true);
                  return [4, dispatchEvent("change", resolveEventData(this.props, { value: valueRes }))];
                case 1:
                  rendererEvent = _d.sent();
                  if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                    return [
                      2
                      /*return*/
                    ];
                  }
                  onChange(valueRes);
                  _d.label = 2;
                case 2:
                  stack2.push({
                    options,
                    parentId,
                    loading: false,
                    visible: Array.isArray(options) && !(0, import_isEmpty.default)(options)
                  });
                  this.setState({
                    stack: stack2
                  }, this.loadMore);
                  return [
                    2
                    /*return*/
                  ];
              }
            });
          });
        }).catch(function(e) {
          !(source === null || source === void 0 ? void 0 : source.silent) && env.notify("error", e.message);
        });
      });
    };
    ChainedSelectControl2.prototype.handleChange = function(index, currentValue) {
      return __awaiter(this, void 0, void 0, function() {
        var _a, value, delimiter, onChange, joinValues, extractValue, dispatchEvent, valueField, data, arr, pushValue, valueRes, rendererEvent;
        return __generator(this, function(_b) {
          switch (_b.label) {
            case 0:
              _a = this.props, value = _a.value, delimiter = _a.delimiter, onChange = _a.onChange, joinValues = _a.joinValues, extractValue = _a.extractValue, dispatchEvent = _a.dispatchEvent, valueField = _a.valueField, data = _a.data;
              arr = Array.isArray(value) ? value.concat() : value && typeof value === "string" ? value.split(delimiter || ",") : [];
              arr.splice(index, arr.length - index);
              pushValue = joinValues ? currentValue[valueField || "value"] : currentValue;
              if (pushValue !== void 0) {
                arr.push(pushValue);
              }
              valueRes = this.array2value(arr);
              return [4, dispatchEvent("change", resolveEventData(this.props, { value: valueRes }))];
            case 1:
              rendererEvent = _b.sent();
              if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                return [
                  2
                  /*return*/
                ];
              }
              onChange(valueRes);
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    ChainedSelectControl2.prototype.reload = function(subpath, query) {
      var reload = this.props.reloadOptions;
      reload && reload(subpath, query);
    };
    ChainedSelectControl2.prototype.renderStatic = function(displayValue) {
      if (displayValue === void 0) {
        displayValue = "-";
      }
      var _a = this.props, _b = _a.options, options = _b === void 0 ? [] : _b, labelField = _a.labelField, valueField = _a.valueField, classPrefix = _a.classPrefix, cx2 = _a.classnames, className = _a.className, value = _a.value, delimiter = _a.delimiter;
      var allOptions = __spreadArray([{ options, visible: true }], __read(this.state.stack || []), false);
      var valueArr = Array.isArray(value) ? value.concat() : value && typeof value === "string" ? value.split(delimiter || ",") : [];
      if ((valueArr === null || valueArr === void 0 ? void 0 : valueArr.length) > 0) {
        displayValue = valueArr.map(function(value2, index) {
          var _a2;
          var _b2 = allOptions[index] || {}, options2 = _b2.options, visible = _b2.visible;
          if (visible === false) {
            return null;
          }
          if (!options2 || !options2.length) {
            return value2;
          }
          var selectedOption = (0, import_find.default)(options2, function(o) {
            return value2 === o[valueField || "value"];
          }) || {};
          return (_a2 = selectedOption[labelField || "label"]) !== null && _a2 !== void 0 ? _a2 : value2;
        }).filter(function(v) {
          return v != null;
        }).join(" > ");
      }
      return import_react.default.createElement("div", { className: cx2("".concat(classPrefix, "SelectStaticControl"), className) }, displayValue);
    };
    ChainedSelectControl2.prototype.render = function() {
      var _this = this;
      var _a = this.props, options = _a.options, ns = _a.classPrefix, className = _a.className, style = _a.style, inline = _a.inline, loading = _a.loading, value = _a.value, delimiter = _a.delimiter, joinValues = _a.joinValues, extractValue = _a.extractValue, multiple = _a.multiple, mobileUI = _a.mobileUI, env = _a.env, testIdBuilder = _a.testIdBuilder, popoverClassName = _a.popoverClassName, rest = __rest(_a, ["options", "classPrefix", "className", "style", "inline", "loading", "value", "delimiter", "joinValues", "extractValue", "multiple", "mobileUI", "env", "testIdBuilder", "popoverClassName"]);
      var arr = Array.isArray(value) ? value.concat() : value && typeof value === "string" ? value.split(delimiter || ",") : [];
      var _b = this.props, themeCss = _b.themeCss, id = _b.id;
      var hasStackLoading = this.state.stack.find(function(a) {
        return a.loading;
      });
      return import_react.default.createElement(
        "div",
        { className: (0, import_classnames.default)("".concat(ns, "ChainedSelectControl"), className) },
        import_react.default.createElement(EnhancedSelect, __assign({}, rest, { mobileUI, popOverContainer: mobileUI ? env === null || env === void 0 ? void 0 : env.getModalContainer : rest.popOverContainer || (env === null || env === void 0 ? void 0 : env.getModalContainer), className: (0, import_classnames.default)(setThemeClassName(__assign(__assign({}, this.props), { name: "chainedSelectControlClassName", id, themeCss }))), popoverClassName: (0, import_classnames.default)(popoverClassName, setThemeClassName(__assign(__assign({}, this.props), { name: "chainedSelectPopoverClassName", id, themeCss }))), controlStyle: style, classPrefix: ns, key: "base", testIdBuilder: testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild("base"), options: Array.isArray(options) ? options : [], value: arr[0], onChange: this.handleChange.bind(this, 0), loading, inline: true })),
        this.state.stack.map(function(_a2, index) {
          var options2 = _a2.options, loading2 = _a2.loading, visible = _a2.visible;
          return visible === false || loading2 ? null : import_react.default.createElement(EnhancedSelect, __assign({}, rest, { mobileUI, popOverContainer: mobileUI ? env.getModalContainer : rest.popOverContainer || (env === null || env === void 0 ? void 0 : env.getModalContainer), classPrefix: ns, key: "x-".concat(index + 1), testIdBuilder: testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild("x-".concat(index + 1)), options: Array.isArray(options2) ? options2 : [], value: arr[index + 1], onChange: _this.handleChange.bind(_this, index + 1), inline: true, controlStyle: style, className: (0, import_classnames.default)(setThemeClassName(__assign(__assign({}, _this.props), { name: "chainedSelectControlClassName", id, themeCss }))), popoverClassName: (0, import_classnames.default)(popoverClassName, setThemeClassName(__assign(__assign({}, _this.props), { name: "chainedSelectPopoverClassName", id, themeCss }))) }));
        }),
        hasStackLoading && import_react.default.createElement(Spinner$1, { size: "sm", className: (0, import_classnames.default)("".concat(ns, "ChainedSelectControl-spinner")) }),
        import_react.default.createElement(CustomStyle, __assign({}, this.props, { config: {
          themeCss,
          classNames: [
            {
              key: "chainedSelectControlClassName",
              weights: {
                focused: {
                  suf: ".is-opened:not(.is-mobile)"
                },
                disabled: {
                  suf: ".is-disabled"
                }
              }
            },
            {
              key: "chainedSelectPopoverClassName",
              weights: {
                default: {
                  suf: " .".concat(ns, "Select-option")
                },
                hover: {
                  suf: " .".concat(ns, "Select-option.is-highlight")
                },
                focused: {
                  inner: ".".concat(ns, "Select-option.is-active")
                }
              }
            }
          ],
          id
        }, env }))
      );
    };
    ChainedSelectControl2.defaultProps = {
      clearable: false,
      searchable: false,
      multiple: true
    };
    __decorate([
      supportStatic(),
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", void 0)
    ], ChainedSelectControl2.prototype, "render", null);
    return ChainedSelectControl2;
  }(import_react.default.Component)
);
var ChainedSelectControlRenderer = (
  /** @class */
  function(_super) {
    __extends(ChainedSelectControlRenderer2, _super);
    function ChainedSelectControlRenderer2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    ChainedSelectControlRenderer2 = __decorate([
      OptionsControl({
        type: "chained-select",
        sizeMutable: false
      })
    ], ChainedSelectControlRenderer2);
    return ChainedSelectControlRenderer2;
  }(ChainedSelectControl)
);
export {
  ChainedSelectControlRenderer,
  ChainedSelectControl as default
};
//# sourceMappingURL=ChainedSelect-JDVECN37.js.map
