export const getDefaultSettingFormData = (defOpts) => {
    return {
        title: '',
        pcVisible: defOpts ? !!defOpts.pcVisible : true,
        pcVertical: true,
        pcTitleBold: true,
        pcTitleColon: false,
        pcTitleAlign: '',
        pcTitleWidth: '',
        pcTitleWidthUnit: '',
        mobileVisible: defOpts ? !!defOpts.mobileVisible : true,
        mobileVertical: true,
        mobileTitleBold: true,
        mobileTitleColon: false,
        mobileTitleAlign: '',
        mobileTitleWidth: '',
        mobileTitleWidthUnit: ''
    };
};
export const createDefaultFormViewPCFormConfig = (params) => {
    const { formConfig } = params;
    return {
        vertical: formConfig.pcVertical,
        titleBold: formConfig.pcTitleBold,
        titleColon: formConfig.pcTitleColon,
        titleAlign: formConfig.pcTitleAlign,
        titleWidth: formConfig.pcTitleWidth
    };
};
export const createDefaultFormViewMobileFormConfig = (params) => {
    const { formConfig } = params;
    return {
        vertical: formConfig.mobileVertical,
        titleBold: formConfig.mobileTitleBold,
        titleColon: formConfig.mobileTitleColon,
        titleAlign: formConfig.mobileTitleAlign,
        titleWidth: formConfig.mobileTitleWidth
    };
};
