{"version": 3, "sources": ["../../amis-formula/esm/doc.js"], "sourcesContent": ["/**\n * amis-formula v6.12.0\n * Copyright 2021-2025 fex\n */\n\nimport { bulkRegisterFunctionDoc } from './function.js';\n\n/**\n * 公式文档 请运行 `npm run genDoc` 自动生成\n */\nbulkRegisterFunctionDoc([\n    {\n        name: \"IF\",\n        description: \"如果满足条件condition，则返回consequent，否则返回alternate，支持多层嵌套IF函数。\\n\\n等价于直接用JS表达式如：condition ? consequent : alternate。\",\n        example: \"IF(condition, consequent, alternate)\",\n        params: [\n            {\n                type: \"expression\",\n                name: \"condition\",\n                description: \"条件表达式。例如：语文成绩>80\"\n            },\n            {\n                type: \"any\",\n                name: \"consequent\",\n                description: \"条件判断通过的返回结果\"\n            },\n            {\n                type: \"any\",\n                name: \"alternate\",\n                description: \"条件判断不通过的返回结果\"\n            }\n        ],\n        returns: {\n            type: \"any\",\n            description: \"根据条件返回不同的结果\"\n        },\n        namespace: \"逻辑函数\"\n    },\n    {\n        name: \"AND\",\n        description: \"条件全部符合，返回 true，否则返回 false。\\n\\n示例：AND(语文成绩>80, 数学成绩>80)，\\n\\n语文成绩和数学成绩都大于 80，则返回 true，否则返回 false，\\n\\n等价于直接用JS表达式如：语文成绩>80 && 数学成绩>80。\",\n        example: \"AND(expression1, expression2, ...expressionN)\",\n        params: [\n            {\n                type: \"...expression\",\n                name: \"conditions\",\n                description: \"条件表达式，多个用逗号隔开。例如：语文成绩>80, 数学成绩>80\"\n            }\n        ],\n        returns: {\n            type: \"boolean\",\n            description: null\n        },\n        namespace: \"逻辑函数\"\n    },\n    {\n        name: \"OR\",\n        description: \"条件任意一个满足条件，返回 true，否则返回 false。\\n\\n示例：OR(语文成绩>80, 数学成绩>80)，\\n\\n语文成绩和数学成绩任意一个大于 80，则返回 true，否则返回 false，\\n\\n等价于直接用JS表达式如：语文成绩>80 || 数学成绩>80。\",\n        example: \"OR(expression1, expression2, ...expressionN)\",\n        params: [\n            {\n                type: \"...expression\",\n                name: \"conditions\",\n                description: \"条件表达式，多个用逗号隔开。例如：语文成绩>80, 数学成绩>80\"\n            }\n        ],\n        returns: {\n            type: \"boolean\",\n            description: null\n        },\n        namespace: \"逻辑函数\"\n    },\n    {\n        name: \"XOR\",\n        description: \"异或处理，多个表达式组中存在奇数个真时认为真。\\n\\n示例：XOR(语文成绩 > 80, 数学成绩 > 80, 英语成绩 > 80)\\n\\n三门成绩中有一门或者三门大于 80，则返回 true，否则返回 false。\",\n        example: \"XOR(condition1, condition2, ...expressionN)\",\n        params: [\n            {\n                type: \"...expression\",\n                name: \"condition\",\n                description: \"条件表达式，多个用逗号隔开。例如：语文成绩>80, 数学成绩>80\"\n            }\n        ],\n        returns: {\n            type: \"boolean\",\n            description: null\n        },\n        namespace: \"逻辑函数\"\n    },\n    {\n        name: \"IFS\",\n        description: \"判断函数集合，相当于多个 else if 合并成一个。\\n\\n示例：IFS(语文成绩 > 80, \\\"优秀\\\", 语文成绩 > 60, \\\"良\\\", \\\"继续努力\\\")，\\n\\n如果语文成绩大于 80，则返回优秀，否则判断大于 60 分，则返回良，否则返回继续努力。\",\n        example: \"IFS(condition1, result1, condition2, result2,...conditionN, resultN)\",\n        params: [\n            {\n                type: \"...expression\",\n                name: \"condition\",\n                description: \"条件表达式\"\n            },\n            {\n                type: \"...any\",\n                name: \"result\",\n                description: \"返回值\"\n            }\n        ],\n        returns: {\n            type: \"any\",\n            description: \"第一个满足条件的结果，没有命中的返回 false。\"\n        },\n        namespace: \"逻辑函数\"\n    },\n    {\n        name: \"ABS\",\n        description: \"返回传入数字的绝对值。\",\n        example: \"ABS(num)\",\n        params: [\n            {\n                type: \"number\",\n                name: \"num\",\n                description: \"数值\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"传入数值的绝对值\"\n        },\n        namespace: \"数学函数\"\n    },\n    {\n        name: \"MAX\",\n        description: \"获取最大值，如果只有一个参数且是数组，则计算这个数组内的值。\",\n        example: \"MAX(num1, num2, ...numN) or MAX([num1, num2, ...numN])\",\n        params: [\n            {\n                type: \"...number\",\n                name: \"num\",\n                description: \"数值\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"所有传入值中最大的那个\"\n        },\n        namespace: \"数学函数\"\n    },\n    {\n        name: \"MIN\",\n        description: \"获取最小值，如果只有一个参数且是数组，则计算这个数组内的值。\",\n        example: \"MIN(num1, num2, ...numN) or MIN([num1, num2, ...numN])\",\n        params: [\n            {\n                type: \"...number\",\n                name: \"num\",\n                description: \"数值\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"所有传入值中最小的那个\"\n        },\n        namespace: \"数学函数\"\n    },\n    {\n        name: \"SUM\",\n        description: \"求和，如果只有一个参数且是数组，则计算这个数组内的值。\",\n        example: \"SUM(num1, num2, ...numN) or SUM([num1, num2, ...numN])\",\n        params: [\n            {\n                type: \"...number\",\n                name: \"num\",\n                description: \"数值\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"所有传入数值的总和\"\n        },\n        namespace: \"数学函数\"\n    },\n    {\n        name: \"INT\",\n        description: \"将数值向下取整为最接近的整数。\",\n        example: \"INT(num)\",\n        params: [\n            {\n                type: \"number\",\n                name: \"num\",\n                description: \"数值\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"数值对应的整形\"\n        },\n        namespace: \"数学函数\"\n    },\n    {\n        name: \"MOD\",\n        description: \"返回两数相除的余数，参数 number 是被除数，divisor 是除数。\",\n        example: \"MOD(num, divisor)\",\n        params: [\n            {\n                type: \"number\",\n                name: \"num\",\n                description: \"被除数\"\n            },\n            {\n                type: \"number\",\n                name: \"divisor\",\n                description: \"除数\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"两数相除的余数\"\n        },\n        namespace: \"数学函数\"\n    },\n    {\n        name: \"PI\",\n        description: \"圆周率 3.1415...。\",\n        example: \"PI()\",\n        params: [],\n        returns: {\n            type: \"number\",\n            description: \"圆周率数值\"\n        },\n        namespace: \"数学函数\"\n    },\n    {\n        name: \"ROUND\",\n        description: \"将数字四舍五入到指定的位数，可以设置小数位。\",\n        example: \"ROUND(num[, numDigits = 2])\",\n        params: [\n            {\n                type: \"number\",\n                name: \"num\",\n                description: \"要处理的数字\"\n            },\n            {\n                type: \"number\",\n                name: \"numDigits\",\n                description: \"小数位数，默认为2\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"传入数值四舍五入后的结果\"\n        },\n        namespace: \"数学函数\"\n    },\n    {\n        name: \"FLOOR\",\n        description: \"将数字向下取整到指定的位数，可以设置小数位。\",\n        example: \"FLOOR(num[, numDigits=2])\",\n        params: [\n            {\n                type: \"number\",\n                name: \"num\",\n                description: \"要处理的数字\"\n            },\n            {\n                type: \"number\",\n                name: \"numDigits\",\n                description: \"小数位数，默认为2\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"传入数值向下取整后的结果\"\n        },\n        namespace: \"数学函数\"\n    },\n    {\n        name: \"CEIL\",\n        description: \"将数字向上取整到指定的位数，可以设置小数位。\",\n        example: \"CEIL(num[, numDigits=2])\",\n        params: [\n            {\n                type: \"number\",\n                name: \"num\",\n                description: \"要处理的数字\"\n            },\n            {\n                type: \"number\",\n                name: \"numDigits\",\n                description: \"小数位数，默认为2\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"传入数值向上取整后的结果\"\n        },\n        namespace: \"数学函数\"\n    },\n    {\n        name: \"SQRT\",\n        description: \"开平方，参数 number 为非负数\",\n        example: \"SQRT(num)\",\n        params: [\n            {\n                type: \"number\",\n                name: \"num\",\n                description: \"要处理的数字\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"开平方的结果\"\n        },\n        namespace: \"数学函数\"\n    },\n    {\n        name: \"AVG\",\n        description: \"返回所有参数的平均值，如果只有一个参数且是数组，则计算这个数组内的值。\",\n        example: \"AVG(num1, num2, ...numN) or AVG([num1, num2, ...numN])\",\n        params: [\n            {\n                type: \"...number\",\n                name: \"num\",\n                description: \"要处理的数字\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"所有数值的平均值\"\n        },\n        namespace: \"数学函数\"\n    },\n    {\n        name: \"DEVSQ\",\n        description: \"返回数据点与数据均值点之差（数据偏差）的平方和，如果只有一个参数且是数组，则计算这个数组内的值。\",\n        example: \"DEVSQ(num1, num2, ...numN)\",\n        params: [\n            {\n                type: \"...number\",\n                name: \"num\",\n                description: \"要处理的数字\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"所有数值的平均值\"\n        },\n        namespace: \"数学函数\"\n    },\n    {\n        name: \"AVEDEV\",\n        description: \"数据点到其算术平均值的绝对偏差的平均值。\",\n        example: \"AVEDEV(num1, num2, ...numN)\",\n        params: [\n            {\n                type: \"...number\",\n                name: \"num\",\n                description: \"要处理的数字\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"所有数值的平均值\"\n        },\n        namespace: \"数学函数\"\n    },\n    {\n        name: \"HARMEAN\",\n        description: \"数据点的调和平均值，如果只有一个参数且是数组，则计算这个数组内的值。\",\n        example: \"HARMEAN(num1, num2, ...numN)\",\n        params: [\n            {\n                type: \"...number\",\n                name: \"num\",\n                description: \"要处理的数字\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"所有数值的平均值\"\n        },\n        namespace: \"数学函数\"\n    },\n    {\n        name: \"LARGE\",\n        description: \"数据集中第 k 个最大值。\",\n        example: \"LARGE(array, k)\",\n        params: [\n            {\n                type: \"array\",\n                name: \"nums\",\n                description: \"要处理的数字\"\n            },\n            {\n                type: \"number\",\n                name: \"k\",\n                description: \"第几大\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"所有数值的平均值\"\n        },\n        namespace: \"数学函数\"\n    },\n    {\n        name: \"UPPERMONEY\",\n        description: \"将数值转为中文大写金额。\",\n        example: \"UPPERMONEY(num)\",\n        params: [\n            {\n                type: \"number\",\n                name: \"num\",\n                description: \"要处理的数字\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"数值中文大写字符\"\n        },\n        namespace: \"数学函数\"\n    },\n    {\n        name: \"RAND\",\n        description: \"返回大于等于 0 且小于 1 的均匀分布随机实数。每一次触发计算都会变化。\\n\\n示例：`RAND()*100`，\\n\\n返回 0-100 之间的随机数。\",\n        example: \"RAND()\",\n        params: [],\n        returns: {\n            type: \"number\",\n            description: \"随机数\"\n        },\n        namespace: \"数学函数\"\n    },\n    {\n        name: \"LAST\",\n        description: \"取数据最后一个。\",\n        example: \"LAST(array)\",\n        params: [\n            {\n                type: \"...number\",\n                name: \"arr\",\n                description: \"要处理的数组\"\n            }\n        ],\n        returns: {\n            type: \"any\",\n            description: \"最后一个值\"\n        },\n        namespace: \"数学函数\"\n    },\n    {\n        name: \"POW\",\n        description: \"返回基数的指数次幂，参数base为基数，exponent为指数，如果参数值不合法则返回基数本身，计算结果不合法，则返回NaN。\",\n        example: \"POW(base, exponent)\",\n        params: [\n            {\n                type: \"number\",\n                name: \"base\",\n                description: \"基数\"\n            },\n            {\n                type: \"number\",\n                name: \"exponent\",\n                description: \"指数\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"基数的指数次幂\"\n        },\n        namespace: \"数学函数\"\n    },\n    {\n        name: \"LEFT\",\n        description: \"返回传入文本左侧的指定长度字符串。\",\n        example: \"LEFT(text, len)\",\n        params: [\n            {\n                type: \"string\",\n                name: \"text\",\n                description: \"要处理的文本\"\n            },\n            {\n                type: \"number\",\n                name: \"len\",\n                description: \"要处理的长度\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"对应字符串\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"RIGHT\",\n        description: \"返回传入文本右侧的指定长度字符串。\",\n        example: \"RIGHT(text, len)\",\n        params: [\n            {\n                type: \"string\",\n                name: \"text\",\n                description: \"要处理的文本\"\n            },\n            {\n                type: \"number\",\n                name: \"len\",\n                description: \"要处理的长度\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"对应字符串\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"LEN\",\n        description: \"计算文本的长度。\",\n        example: \"LEN(text)\",\n        params: [\n            {\n                type: \"string\",\n                name: \"text\",\n                description: \"要处理的文本\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"长度\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"LENGTH\",\n        description: \"计算文本集合中所有文本的长度。\",\n        example: \"LENGTH(textArr)\",\n        params: [\n            {\n                type: \"Array<string>\",\n                name: \"textArr\",\n                description: \"要处理的文本集合\"\n            }\n        ],\n        returns: {\n            type: \"Array<number>\",\n            description: \"长度集合\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"ISEMPTY\",\n        description: \"判断文本是否为空。\",\n        example: \"ISEMPTY(text)\",\n        params: [\n            {\n                type: \"string\",\n                name: \"text\",\n                description: \"要处理的文本\"\n            }\n        ],\n        returns: {\n            type: \"boolean\",\n            description: \"判断结果\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"CONCATENATE\",\n        description: \"将多个传入值连接成文本。\",\n        example: \"CONCATENATE(text1, text2, ...textN)\",\n        params: [\n            {\n                type: \"...string\",\n                name: \"text\",\n                description: \"文本集合\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"连接后的文本\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"CHAR\",\n        description: \"返回计算机字符集的数字代码所对应的字符。\\n\\n示例：`CHAR(97)` 等价于 \\\"a\\\"。\",\n        example: \"CHAR(code)\",\n        params: [\n            {\n                type: \"number\",\n                name: \"code\",\n                description: \"编码值\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"指定位置的字符\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"LOWER\",\n        description: \"将传入文本转成小写。\",\n        example: \"LOWER(text)\",\n        params: [\n            {\n                type: \"string\",\n                name: \"text\",\n                description: \"文本\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"结果文本\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"UPPER\",\n        description: \"将传入文本转成大写。\",\n        example: \"UPPER(text)\",\n        params: [\n            {\n                type: \"string\",\n                name: \"text\",\n                description: \"文本\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"结果文本\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"UPPERFIRST\",\n        description: \"将传入文本首字母转成大写。\",\n        example: \"UPPERFIRST(text)\",\n        params: [\n            {\n                type: \"string\",\n                name: \"text\",\n                description: \"文本\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"结果文本\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"PADSTART\",\n        description: \"向前补齐文本长度。\\n\\n示例 `PADSTART(\\\"6\\\", 2, \\\"0\\\")`，\\n\\n返回 `06`。\",\n        example: \"PADSTART(text)\",\n        params: [\n            {\n                type: \"string\",\n                name: \"text\",\n                description: \"文本\"\n            },\n            {\n                type: \"number\",\n                name: \"num\",\n                description: \"目标长度\"\n            },\n            {\n                type: \"string\",\n                name: \"pad\",\n                description: \"用于补齐的文本\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"结果文本\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"CAPITALIZE\",\n        description: \"将文本转成标题。\\n\\n示例 `CAPITALIZE(\\\"star\\\")`，\\n\\n返回 `Star`。\",\n        example: \"CAPITALIZE(text)\",\n        params: [\n            {\n                type: \"string\",\n                name: \"text\",\n                description: \"文本\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"结果文本\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"ESCAPE\",\n        description: \"对文本进行 HTML 转义。\\n\\n示例 `ESCAPE(\\\"<star>&\\\")`，\\n\\n返回 `&lt;start&gt;&amp;`。\",\n        example: \"ESCAPE(text)\",\n        params: [\n            {\n                type: \"string\",\n                name: \"text\",\n                description: \"文本\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"结果文本\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"TRUNCATE\",\n        description: \"对文本长度进行截断。\\n\\n示例 `TRUNCATE(\\\"amis.baidu.com\\\", 6)`，\\n\\n返回 `amis...`。\",\n        example: \"TRUNCATE(text, 6)\",\n        params: [\n            {\n                type: \"string\",\n                name: \"text\",\n                description: \"文本\"\n            },\n            {\n                type: \"number\",\n                name: \"text\",\n                description: \"最长长度\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"结果文本\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"BEFORELAST\",\n        description: \"取在某个分隔符之前的所有字符串。\",\n        example: \"BEFORELAST(text, '.')\",\n        params: [\n            {\n                type: \"string\",\n                name: \"text\",\n                description: \"文本\"\n            },\n            {\n                type: \"string\",\n                name: \"delimiter\",\n                description: \"结束文本\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"判断结果\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"SPLIT\",\n        description: \"将文本根据指定片段分割成数组。\\n\\n示例：`SPLIT(\\\"a,b,c\\\", \\\",\\\")`，\\n\\n返回 `[\\\"a\\\", \\\"b\\\", \\\"c\\\"]`。\",\n        example: \"SPLIT(text, ',')\",\n        params: [\n            {\n                type: \"string\",\n                name: \"text\",\n                description: \"文本\"\n            },\n            {\n                type: \"string\",\n                name: \"delimiter\",\n                description: \"文本片段\"\n            }\n        ],\n        returns: {\n            type: \"Array<string>\",\n            description: \"文本集\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"TRIM\",\n        description: \"将文本去除前后空格。\",\n        example: \"TRIM(text)\",\n        params: [\n            {\n                type: \"string\",\n                name: \"text\",\n                description: \"文本\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"处理后的文本\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"STRIPTAG\",\n        description: \"去除文本中的 HTML 标签。\\n\\n示例：`STRIPTAG(\\\"<b>amis</b>\\\")`，\\n\\n返回：`amis`。\",\n        example: \"STRIPTAG(text)\",\n        params: [\n            {\n                type: \"string\",\n                name: \"text\",\n                description: \"文本\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"处理后的文本\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"LINEBREAK\",\n        description: \"将字符串中的换行转成 HTML `<br>`，用于简单换行的场景。\\n\\n示例：`LINEBREAK(\\\"\\\\n\\\")`，\\n\\n返回：`<br/>`。\",\n        example: \"LINEBREAK(text)\",\n        params: [\n            {\n                type: \"string\",\n                name: \"text\",\n                description: \"文本\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"处理后的文本\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"STARTSWITH\",\n        description: \"判断字符串(text)是否以特定字符串(startString)开始，是则返回 true，否则返回 false。\",\n        example: \"STARTSWITH(text, '片段')\",\n        params: [\n            {\n                type: \"string\",\n                name: \"text\",\n                description: \"文本\"\n            },\n            {\n                type: \"string\",\n                name: \"startString\",\n                description: \"起始文本\"\n            }\n        ],\n        returns: {\n            type: \"boolean\",\n            description: \"判断结果\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"ENDSWITH\",\n        description: \"判断字符串(text)是否以特定字符串(endString)结束，是则返回 true，否则返回 false。\",\n        example: \"ENDSWITH(text, '片段')\",\n        params: [\n            {\n                type: \"string\",\n                name: \"text\",\n                description: \"文本\"\n            },\n            {\n                type: \"string\",\n                name: \"endString\",\n                description: \"结束文本\"\n            }\n        ],\n        returns: {\n            type: \"boolean\",\n            description: \"判断结果\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"CONTAINS\",\n        description: \"判断参数 1 中的文本是否包含参数 2 中的文本，是则返回 true，否则返回 false。\",\n        example: \"CONTAINS(text, searchText)\",\n        params: [\n            {\n                type: \"string\",\n                name: \"text\",\n                description: \"文本\"\n            },\n            {\n                type: \"string\",\n                name: \"searchText\",\n                description: \"搜索文本\"\n            }\n        ],\n        returns: {\n            type: \"boolean\",\n            description: \"判断结果\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"REPLACE\",\n        description: \"对文本进行全量替换。\",\n        example: \"REPLACE(text, search, replace)\",\n        params: [\n            {\n                type: \"string\",\n                name: \"text\",\n                description: \"要处理的文本\"\n            },\n            {\n                type: \"string\",\n                name: \"search\",\n                description: \"要被替换的文本\"\n            },\n            {\n                type: \"string\",\n                name: \"replace\",\n                description: \"要替换的文本\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"处理结果\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"SEARCH\",\n        description: \"对文本进行搜索，返回命中的位置。\",\n        example: \"SEARCH(text, search, 0)\",\n        params: [\n            {\n                type: \"string\",\n                name: \"text\",\n                description: \"要处理的文本\"\n            },\n            {\n                type: \"string\",\n                name: \"search\",\n                description: \"用来搜索的文本\"\n            },\n            {\n                type: \"number\",\n                name: \"start\",\n                description: \"起始位置\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"命中的位置\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"MID\",\n        description: \"返回文本字符串中从指定位置开始的特定数目的字符。\\n\\n示例：`MID(\\\"amis.baidu.com\\\", 6, 3)`，\\n\\n返回 `aid`。\",\n        example: \"MID(text, from, len)\",\n        params: [\n            {\n                type: \"string\",\n                name: \"text\",\n                description: \"要处理的文本\"\n            },\n            {\n                type: \"number\",\n                name: \"from\",\n                description: \"起始位置\"\n            },\n            {\n                type: \"number\",\n                name: \"len\",\n                description: \"处理长度\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"命中的位置\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"BASENAME\",\n        description: \"返回路径中的文件名。\\n\\n示例：`/home/<USER>/a.json`，\\n\\n返回：`a.json`。\",\n        example: \"BASENAME(text)\",\n        params: [\n            {\n                type: \"string\",\n                name: \"text\",\n                description: \"要处理的文本\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"文件名\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"UUID\",\n        description: \"生成UUID字符串\",\n        example: \"UUID(8)\",\n        params: [\n            {\n                type: \"number\",\n                name: \"length\",\n                description: \"生成的UUID字符串长度，默认为32位\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"生成的UUID字符串\"\n        },\n        namespace: \"文本函数\"\n    },\n    {\n        name: \"DATE\",\n        description: \"创建日期对象，可以通过特定格式的字符串，或者数值。\\n\\n需要注意的是，其中月份的数值是从0开始的，\\n即如果是12月份，你应该传入数值11。\",\n        example: \"DATE('2021-12-06 08:20:00')\",\n        params: [],\n        returns: {\n            type: \"Date\",\n            description: \"日期对象\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"TIMESTAMP\",\n        description: \"返回时间的时间戳。\",\n        example: \"TIMESTAMP(date[, format = \\\"X\\\"])\",\n        params: [\n            {\n                type: \"date\",\n                name: \"date\",\n                description: \"日期对象\"\n            },\n            {\n                type: \"string\",\n                name: \"format\",\n                description: \"时间戳格式，带毫秒传入 'x'。默认为 'X' 不带毫秒的。\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"时间戳\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"TODAY\",\n        description: \"返回今天的日期。\",\n        example: \"TODAY()\",\n        params: [],\n        returns: {\n            type: \"number\",\n            description: \"日期\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"NOW\",\n        description: \"返回现在的日期\",\n        example: \"NOW()\",\n        params: [],\n        returns: {\n            type: \"number\",\n            description: \"日期\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"WEEKDAY\",\n        description: \"获取日期的星期几。\\n\\n示例\\n\\nWEEKDAY('2023-02-27') 得到 0。\\nWEEKDAY('2023-02-27', 2) 得到 1。\",\n        example: \"WEEKDAY(date)\",\n        params: [\n            {\n                type: \"any\",\n                name: \"date\",\n                description: \"日期\"\n            },\n            {\n                type: \"number\",\n                name: \"type\",\n                description: \"星期定义类型，默认为1，1表示0至6代表星期一到星期日，2表示1至7代表星期一到星期日\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"星期几的数字标识\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"WEEK\",\n        description: \"获取年份的星期，即第几周。\\n\\n示例\\n\\nWEEK('2023-03-05') 得到 9。\",\n        example: \"WEEK(date)\",\n        params: [\n            {\n                type: \"any\",\n                name: \"date\",\n                description: \"日期\"\n            },\n            {\n                type: \"boolean\",\n                name: \"isISO\",\n                description: \"是否ISO星期\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"星期几的数字标识\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"DATETOSTR\",\n        description: \"对日期、日期字符串、时间戳进行格式化。\\n\\n示例\\n\\nDATETOSTR('12/25/2022', 'YYYY-MM-DD') 得到 '2022.12.25'，\\nDATETOSTR(1676563200, 'YYYY.MM.DD') 得到 '2023.02.17'，\\nDATETOSTR(1676563200000, 'YYYY.MM.DD hh:mm:ss') 得到 '2023.02.17 12:00:00'，\\nDATETOSTR(DATE('2021-12-21'), 'YYYY.MM.DD hh:mm:ss') 得到 '2021.12.21 08:00:00'。\",\n        example: \"DATETOSTR(date, 'YYYY-MM-DD')\",\n        params: [\n            {\n                type: \"any\",\n                name: \"date\",\n                description: \"日期对象、日期字符串、时间戳\"\n            },\n            {\n                type: \"string\",\n                name: \"format\",\n                description: \"日期格式，默认为 \\\"YYYY-MM-DD HH:mm:ss\\\"\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"日期字符串\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"DATERANGESPLIT\",\n        description: \"获取日期范围字符串中的开始时间、结束时间。\\n\\n示例：\\n\\nDATERANGESPLIT('1676563200, 1676735999') 得到 [1676563200, 1676735999]，\\nDATERANGESPLIT('1676563200, 1676735999', undefined , 'YYYY.MM.DD hh:mm:ss') 得到 [2023.02.17 12:00:00, 2023.02.18 11:59:59]，\\nDATERANGESPLIT('1676563200, 1676735999', 0 , 'YYYY.MM.DD hh:mm:ss') 得到 '2023.02.17 12:00:00'，\\nDATERANGESPLIT('1676563200, 1676735999', 'start' , 'YYYY.MM.DD hh:mm:ss') 得到 '2023.02.17 12:00:00'，\\nDATERANGESPLIT('1676563200, 1676735999', 1 , 'YYYY.MM.DD hh:mm:ss') 得到 '2023.02.18 11:59:59'，\\nDATERANGESPLIT('1676563200, 1676735999', 'end' , 'YYYY.MM.DD hh:mm:ss') 得到 '2023.02.18 11:59:59'。\",\n        example: \"DATERANGESPLIT(date, 'YYYY-MM-DD')\",\n        params: [\n            {\n                type: \"string\",\n                name: \"date\",\n                description: \"日期范围字符串\"\n            },\n            {\n                type: \"string\",\n                name: \"key\",\n                description: \"取值标识，0或'start'表示获取开始时间，1或'end'表示获取结束时间\"\n            },\n            {\n                type: \"string\",\n                name: \"format\",\n                description: \"日期格式，可选\"\n            },\n            {\n                type: \"string\",\n                name: \"delimiter\",\n                description: \"分隔符，可选，默认为','\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"日期字符串\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"STARTOF\",\n        description: \"返回日期的指定范围的开端。\",\n        example: \"STARTOF(date[unit = \\\"day\\\"])\",\n        params: [\n            {\n                type: \"date\",\n                name: \"date\",\n                description: \"日期对象\"\n            },\n            {\n                type: \"string\",\n                name: \"unit\",\n                description: \"比如可以传入 'day'、'month'、'year' 或者 `week` 等等\"\n            },\n            {\n                type: \"string\",\n                name: \"format\",\n                description: \"日期格式，可选\"\n            }\n        ],\n        returns: {\n            type: \"any\",\n            description: \"新的日期对象, 如果传入 format 则返回格式化后的日期字符串\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"ENDOF\",\n        description: \"返回日期的指定范围的末尾。\",\n        example: \"ENDOF(date[unit = \\\"day\\\"])\",\n        params: [\n            {\n                type: \"date\",\n                name: \"date\",\n                description: \"日期对象\"\n            },\n            {\n                type: \"string\",\n                name: \"unit\",\n                description: \"比如可以传入 'day'、'month'、'year' 或者 `week` 等等\"\n            },\n            {\n                type: \"string\",\n                name: \"format\",\n                description: \"日期格式，可选\"\n            }\n        ],\n        returns: {\n            type: \"any\",\n            description: \"新的日期对象, 如果传入 format 则返回格式化后的日期字符串\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"YEAR\",\n        description: \"返回日期的年份。\",\n        example: \"YEAR(date)\",\n        params: [\n            {\n                type: \"date\",\n                name: \"date\",\n                description: \"日期对象\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"数值\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"MONTH\",\n        description: \"返回日期的月份，这里就是自然月份。\",\n        example: \"MONTH(date)\",\n        params: [\n            {\n                type: \"date\",\n                name: \"date\",\n                description: \"日期对象\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"数值\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"DAY\",\n        description: \"返回日期的天。\",\n        example: \"DAY(date)\",\n        params: [\n            {\n                type: \"date\",\n                name: \"date\",\n                description: \"日期对象\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"数值\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"HOUR\",\n        description: \"返回日期的小时。\",\n        example: \"HOUR(date)\",\n        params: [\n            {\n                type: \"date\",\n                name: \"date\",\n                description: \"日期对象\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"数值\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"MINUTE\",\n        description: \"返回日期的分。\",\n        example: \"MINUTE(date)\",\n        params: [\n            {\n                type: \"date\",\n                name: \"date\",\n                description: \"日期对象\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"数值\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"SECOND\",\n        description: \"返回日期的秒。\",\n        example: \"SECOND(date)\",\n        params: [\n            {\n                type: \"date\",\n                name: \"date\",\n                description: \"日期对象\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"数值\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"YEARS\",\n        description: \"返回两个日期相差多少年。\",\n        example: \"YEARS(endDate, startDate)\",\n        params: [\n            {\n                type: \"date\",\n                name: \"endDate\",\n                description: \"日期对象\"\n            },\n            {\n                type: \"date\",\n                name: \"startDate\",\n                description: \"日期对象\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"数值\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"MINUTES\",\n        description: \"返回两个日期相差多少分钟。\",\n        example: \"MINUTES(endDate, startDate)\",\n        params: [\n            {\n                type: \"date\",\n                name: \"endDate\",\n                description: \"日期对象\"\n            },\n            {\n                type: \"date\",\n                name: \"startDate\",\n                description: \"日期对象\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"数值\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"DAYS\",\n        description: \"返回两个日期相差多少天。\",\n        example: \"DAYS(endDate, startDate)\",\n        params: [\n            {\n                type: \"date\",\n                name: \"endDate\",\n                description: \"日期对象\"\n            },\n            {\n                type: \"date\",\n                name: \"startDate\",\n                description: \"日期对象\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"数值\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"HOURS\",\n        description: \"返回两个日期相差多少小时。\",\n        example: \"HOURS(endDate, startDate)\",\n        params: [\n            {\n                type: \"date\",\n                name: \"endDate\",\n                description: \"日期对象\"\n            },\n            {\n                type: \"date\",\n                name: \"startDate\",\n                description: \"日期对象\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"数值\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"DATEMODIFY\",\n        description: \"修改日期，对日期进行加减天、月份、年等操作。\\n\\n示例：\\n\\nDATEMODIFY(A, -2, 'month')，\\n\\n对日期 A 进行往前减2月的操作。\",\n        example: \"DATEMODIFY(date, 2, 'days')\",\n        params: [\n            {\n                type: \"date\",\n                name: \"date\",\n                description: \"日期对象\"\n            },\n            {\n                type: \"number\",\n                name: \"num\",\n                description: \"数值\"\n            },\n            {\n                type: \"string\",\n                name: \"unit\",\n                description: \"单位：支持年、月、天等等\"\n            }\n        ],\n        returns: {\n            type: \"date\",\n            description: \"日期对象\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"STRTODATE\",\n        description: \"将字符日期转成日期对象，可以指定日期格式。\\n\\n示例：STRTODATE('2021/12/6', 'YYYY/MM/DD')\",\n        example: \"STRTODATE(value[, format=\\\"\\\"])\",\n        params: [\n            {\n                type: \"string\",\n                name: \"value\",\n                description: \"日期字符\"\n            },\n            {\n                type: \"string\",\n                name: \"format\",\n                description: \"日期格式\"\n            }\n        ],\n        returns: {\n            type: \"date\",\n            description: \"日期对象\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"ISBEFORE\",\n        description: \"判断两个日期，是否第一个日期在第二个日期的前面，是则返回 true，否则返回 false。\",\n        example: \"ISBEFORE(a, b)\",\n        params: [\n            {\n                type: \"date\",\n                name: \"a\",\n                description: \"第一个日期\"\n            },\n            {\n                type: \"date\",\n                name: \"b\",\n                description: \"第二个日期\"\n            },\n            {\n                type: \"string\",\n                name: \"unit\",\n                description: \"单位，默认是 'day'， 即之比较到天\"\n            }\n        ],\n        returns: {\n            type: \"boolean\",\n            description: \"判断结果\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"ISAFTER\",\n        description: \"判断两个日期，是否第一个日期在第二个日期的后面，是则返回 true，否则返回 false。\",\n        example: \"ISAFTER(a, b)\",\n        params: [\n            {\n                type: \"date\",\n                name: \"a\",\n                description: \"第一个日期\"\n            },\n            {\n                type: \"date\",\n                name: \"b\",\n                description: \"第二个日期\"\n            },\n            {\n                type: \"string\",\n                name: \"unit\",\n                description: \"单位，默认是 'day'， 即之比较到天\"\n            }\n        ],\n        returns: {\n            type: \"boolean\",\n            description: \"判断结果\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"BETWEENRANGE\",\n        description: \"判断日期是否在指定范围内，是则返回 true，否则返回 false。\\n\\n示例：BETWEENRANGE('2021/12/6', ['2021/12/5','2021/12/7'])。\",\n        example: \"BETWEENRANGE(date, [start, end])\",\n        params: [\n            {\n                type: \"any\",\n                name: \"date\",\n                description: \"第一个日期\"\n            },\n            {\n                type: \"Array<any>\",\n                name: \"daterange\",\n                description: \"日期范围\"\n            },\n            {\n                type: \"string\",\n                name: \"unit\",\n                description: \"单位，默认是 'day'， 即之比较到天\"\n            },\n            {\n                type: \"string\",\n                name: \"inclusivity\",\n                description: \"包容性规则，默认为'[]'。[ 表示包含、( 表示排除，如果使用包容性参数，则必须传入两个指示符，如'()'表示左右范围都排除\"\n            }\n        ],\n        returns: {\n            type: \"boolean\",\n            description: \"判断结果\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"ISSAMEORBEFORE\",\n        description: \"判断两个日期，是否第一个日期在第二个日期的前面或者相等，是则返回 true，否则返回 false。\",\n        example: \"ISSAMEORBEFORE(a, b)\",\n        params: [\n            {\n                type: \"date\",\n                name: \"a\",\n                description: \"第一个日期\"\n            },\n            {\n                type: \"date\",\n                name: \"b\",\n                description: \"第二个日期\"\n            },\n            {\n                type: \"string\",\n                name: \"unit\",\n                description: \"单位，默认是 'day'， 即之比较到天\"\n            }\n        ],\n        returns: {\n            type: \"boolean\",\n            description: \"判断结果\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"ISSAMEORAFTER\",\n        description: \"判断两个日期，是否第一个日期在第二个日期的后面或者相等，是则返回 true，否则返回 false。\",\n        example: \"ISSAMEORAFTER(a, b)\",\n        params: [\n            {\n                type: \"date\",\n                name: \"a\",\n                description: \"第一个日期\"\n            },\n            {\n                type: \"date\",\n                name: \"b\",\n                description: \"第二个日期\"\n            },\n            {\n                type: \"string\",\n                name: \"unit\",\n                description: \"单位，默认是 'day'， 即之比较到天\"\n            }\n        ],\n        returns: {\n            type: \"boolean\",\n            description: \"判断结果\"\n        },\n        namespace: \"日期函数\"\n    },\n    {\n        name: \"COUNT\",\n        description: \"返回数组的长度。\",\n        example: \"COUNT(arr)\",\n        params: [\n            {\n                type: \"Array<any>\",\n                name: \"arr\",\n                description: \"数组\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"结果\"\n        },\n        namespace: \"数组\"\n    },\n    {\n        name: \"ARRAYMAP\",\n        description: \"数组做数据转换，需要搭配箭头函数一起使用，注意箭头函数只支持单表达式用法。\\n\\n将数组中的每个元素转换成箭头函数返回的值。\\n\\n示例：\\n\\nARRAYMAP([1, 2, 3], item => item + 1) 得到 [2, 3, 4]。\",\n        example: \"ARRAYMAP(arr, item => item)\",\n        params: [\n            {\n                type: \"Array<any>\",\n                name: \"arr\",\n                description: \"数组\"\n            },\n            {\n                type: \"Array<any>\",\n                name: \"iterator\",\n                description: \"箭头函数\"\n            }\n        ],\n        returns: {\n            type: \"Array<any>\",\n            description: \"返回转换后的数组\"\n        },\n        namespace: \"数组\"\n    },\n    {\n        name: \"ARRAYFILTER\",\n        description: \"数据做数据过滤，需要搭配箭头函数一起使用，注意箭头函数只支持单表达式用法。\\n将第二个箭头函数返回为 false 的成员过滤掉。\\n\\n示例：\\n\\nARRAYFILTER([1, 2, 3], item => item > 1) 得到 [2, 3]。\",\n        example: \"ARRAYFILTER(arr, item => item)\",\n        params: [\n            {\n                type: \"Array<any>\",\n                name: \"arr\",\n                description: \"数组\"\n            },\n            {\n                type: \"Array<any>\",\n                name: \"iterator\",\n                description: \"箭头函数\"\n            }\n        ],\n        returns: {\n            type: \"Array<any>\",\n            description: \"返回过滤后的数组\"\n        },\n        namespace: \"数组\"\n    },\n    {\n        name: \"ARRAYFINDINDEX\",\n        description: \"数据做数据查找，需要搭配箭头函数一起使用，注意箭头函数只支持单表达式用法。\\n找出第二个箭头函数返回为 true 的成员的索引。\\n\\n示例：\\n\\nARRAYFINDINDEX([0, 2, false], item => item === 2) 得到 1。\",\n        example: \"ARRAYFINDINDEX(arr, item => item === 2)\",\n        params: [\n            {\n                type: \"Array<any>\",\n                name: \"arr\",\n                description: \"数组\"\n            },\n            {\n                type: \"Array<any>\",\n                name: \"iterator\",\n                description: \"箭头函数\"\n            }\n        ],\n        returns: {\n            type: \"number\",\n            description: \"结果\"\n        },\n        namespace: \"数组\"\n    },\n    {\n        name: \"ARRAYFIND\",\n        description: \"数据做数据查找，需要搭配箭头函数一起使用，注意箭头函数只支持单表达式用法。\\n找出第二个箭头函数返回为 true 的成员。\\n\\n示例：\\n\\nARRAYFIND([0, 2, false], item => item === 2) 得到 2。\",\n        example: \"ARRAYFIND(arr, item => item === 2)\",\n        params: [\n            {\n                type: \"Array<any>\",\n                name: \"arr\",\n                description: \"数组\"\n            },\n            {\n                type: \"Array<any>\",\n                name: \"iterator\",\n                description: \"箭头函数\"\n            }\n        ],\n        returns: {\n            type: \"any\",\n            description: \"结果\"\n        },\n        namespace: \"数组\"\n    },\n    {\n        name: \"ARRAYSOME\",\n        description: \"数据做数据遍历判断，需要搭配箭头函数一起使用，注意箭头函数只支持单表达式用法。\\n判断第二个箭头函数是否存在返回为 true 的成员，是则返回 true，否则返回 false。\\n\\n示例：\\n\\nARRAYSOME([0, 2, false], item => item === 2) 得到 true。\",\n        example: \"ARRAYSOME(arr, item => item === 2)\",\n        params: [\n            {\n                type: \"Array<any>\",\n                name: \"arr\",\n                description: \"数组\"\n            },\n            {\n                type: \"Array<any>\",\n                name: \"iterator\",\n                description: \"箭头函数\"\n            }\n        ],\n        returns: {\n            type: \"boolean\",\n            description: \"结果\"\n        },\n        namespace: \"数组\"\n    },\n    {\n        name: \"ARRAYEVERY\",\n        description: \"数据做数据遍历判断，需要搭配箭头函数一起使用，注意箭头函数只支持单表达式用法。\\n判断第二个箭头函数返回是否都为 true，是则返回 true，否则返回 false。\\n\\n示例：\\n\\nARRAYEVERY([0, 2, false], item => item === 2) 得到 false\",\n        example: \"ARRAYEVERY(arr, item => item === 2)\",\n        params: [\n            {\n                type: \"Array<any>\",\n                name: \"arr\",\n                description: \"数组\"\n            },\n            {\n                type: \"Array<any>\",\n                name: \"iterator\",\n                description: \"箭头函数\"\n            }\n        ],\n        returns: {\n            type: \"boolean\",\n            description: \"结果\"\n        },\n        namespace: \"数组\"\n    },\n    {\n        name: \"ARRAYINCLUDES\",\n        description: \"判断数据中是否存在指定元素。\\n\\n示例：\\n\\nARRAYINCLUDES([0, 2, false], 2) 得到 true。\",\n        example: \"ARRAYINCLUDES(arr, 2)\",\n        params: [\n            {\n                type: \"Array<any>\",\n                name: \"arr\",\n                description: \"数组\"\n            },\n            {\n                type: \"any\",\n                name: \"item\",\n                description: \"元素\"\n            }\n        ],\n        returns: {\n            type: \"any\",\n            description: \"结果\"\n        },\n        namespace: \"数组\"\n    },\n    {\n        name: \"COMPACT\",\n        description: \"数组过滤掉 false、null、0 和 \\\"\\\"。\\n\\n示例：\\n\\nCOMPACT([0, 1, false, 2, '', 3]) 得到 [1, 2, 3]。\",\n        example: \"COMPACT(arr)\",\n        params: [\n            {\n                type: \"Array<any>\",\n                name: \"arr\",\n                description: \"数组\"\n            }\n        ],\n        returns: {\n            type: \"Array<any>\",\n            description: \"结果\"\n        },\n        namespace: \"数组\"\n    },\n    {\n        name: \"JOIN\",\n        description: \"数组转成字符串。\\n\\n示例：\\n\\nJOIN(['a', 'b', 'c'], '=') 得到 'a=b=c'。\",\n        example: \"JOIN(arr, string)\",\n        params: [\n            {\n                type: \"Array<any>\",\n                name: \"arr\",\n                description: \"数组\"\n            },\n            {\n                type: \"String\",\n                name: \"separator\",\n                description: \"分隔符\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"结果\"\n        },\n        namespace: \"数组\"\n    },\n    {\n        name: \"CONCAT\",\n        description: \"数组合并。\\n\\n示例：\\n\\nCONCAT(['a', 'b', 'c'], ['1'], ['3']) 得到 ['a', 'b', 'c', '1', '3']。\",\n        example: \"CONCAT(['a', 'b', 'c'], ['1'], ['3'])\",\n        params: [\n            {\n                type: \"Array<any>\",\n                name: \"arr\",\n                description: \"数组\"\n            }\n        ],\n        returns: {\n            type: \"Array<any>\",\n            description: \"结果\"\n        },\n        namespace: \"数组\"\n    },\n    {\n        name: \"UNIQ\",\n        description: \"数组去重，第二个参数「field」，可指定根据该字段去重。\\n\\n示例：\\n\\nUNIQ([{a: '1'}, {b: '2'}, {a: '1'}]) 得到 [{a: '1'}, {b: '2'}]。\",\n        example: \"UNIQ([{a: '1'}, {b: '2'}, {a: '1'}], 'x')\",\n        params: [\n            {\n                type: \"Array<any>\",\n                name: \"arr\",\n                description: \"数组\"\n            },\n            {\n                type: \"string\",\n                name: \"field\",\n                description: \"字段\"\n            }\n        ],\n        returns: {\n            type: \"Array<any>\",\n            description: \"结果\"\n        },\n        namespace: \"数组\"\n    },\n    {\n        name: \"ENCODEJSON\",\n        description: \"将JS对象转换成JSON字符串。\\n\\n示例：\\n\\nENCODEJSON({name: 'amis'}) 得到 '{\\\"name\\\":\\\"amis\\\"}'。\",\n        example: \"ENCODEJSON({name: 'amis'})\",\n        params: [\n            {\n                type: \"object\",\n                name: \"obj\",\n                description: \"JS对象\"\n            }\n        ],\n        returns: {\n            type: \"string\",\n            description: \"结果\"\n        },\n        namespace: \"编码\"\n    },\n    {\n        name: \"DECODEJSON\",\n        description: \"解析JSON编码数据，返回JS对象。\\n\\n示例：\\n\\nDECODEJSON('{\\\\\\\"name\\\\\\\": \\\"amis\\\"}') 得到 {name: 'amis'}。\",\n        example: \"DECODEJSON('{\\\\\\\"name\\\\\\\": \\\"amis\\\"}')\",\n        params: [\n            {\n                type: \"string\",\n                name: \"str\",\n                description: \"字符串\"\n            }\n        ],\n        returns: {\n            type: \"object\",\n            description: \"结果\"\n        },\n        namespace: \"编码\"\n    },\n    {\n        name: \"GET\",\n        description: \"根据对象或者数组的path路径获取值。 如果解析 value 是 undefined 会以 defaultValue 取代。\\n\\n示例：\\n\\nGET([0, 2, {name: 'amis', age: 18}], 1) 得到 2，\\nGET([0, 2, {name: 'amis', age: 18}], '2.name') 得到 'amis'，\\nGET({arr: [{name: 'amis', age: 18}]}, 'arr[0].name') 得到 'amis'，\\nGET({arr: [{name: 'amis', age: 18}]}, 'arr.0.name') 得到 'amis'，\\nGET({arr: [{name: 'amis', age: 18}]}, 'arr.1.name', 'not-found') 得到 'not-found'。\",\n        example: \"GET(arr, 2)\",\n        params: [\n            {\n                type: \"any\",\n                name: \"obj\",\n                description: \"对象或数组\"\n            },\n            {\n                type: \"string\",\n                name: \"path\",\n                description: \"路径\"\n            },\n            {\n                type: \"any\",\n                name: \"defaultValue\",\n                description: \"如果解析不到则返回该值\"\n            }\n        ],\n        returns: {\n            type: \"any\",\n            description: \"结果\"\n        },\n        namespace: \"其他\"\n    },\n    {\n        name: \"ISTYPE\",\n        description: \"判断是否为类型支持：string, number, array, date, plain-object。\",\n        example: \"ISTYPE([{a: '1'}, {b: '2'}, {a: '1'}], 'array')\",\n        params: [\n            {\n                type: \"string\",\n                name: \"判断对象\",\n                description: null\n            }\n        ],\n        returns: {\n            type: \"boolean\",\n            description: \"结果\"\n        },\n        namespace: \"其他\"\n    }\n]);\n"], "mappings": ";;;;;;;;AAUA,wBAAwB;AAAA,EACpB;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ,CAAC;AAAA,IACT,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ,CAAC;AAAA,IACT,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ,CAAC;AAAA,IACT,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ,CAAC;AAAA,IACT,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ,CAAC;AAAA,IACT,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,MACJ;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,EACf;AACJ,CAAC;", "names": []}