{"version": 3, "sources": ["../../highlight.js/es/languages/json.js"], "sourcesContent": ["/*\nLanguage: JSON\nDescription: <PERSON><PERSON><PERSON> (JavaScript Object Notation) is a lightweight data-interchange format.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: http://www.json.org\nCategory: common, protocols, web\n*/\n\nfunction json(hljs) {\n  const ATTRIBUTE = {\n    className: 'attr',\n    begin: /\"(\\\\.|[^\\\\\"\\r\\n])*\"(?=\\s*:)/,\n    relevance: 1.01\n  };\n  const PUNCTUATION = {\n    match: /[{}[\\],:]/,\n    className: \"punctuation\",\n    relevance: 0\n  };\n  const LITERALS = [\n    \"true\",\n    \"false\",\n    \"null\"\n  ];\n  // NOTE: normally we would rely on `keywords` for this but using a mode here allows us\n  // - to use the very tight `illegal: \\S` rule later to flag any other character\n  // - as illegal indicating that despite looking like JSON we do not truly have\n  // - JSON and thus improve false-positively greatly since JSO<PERSON> will try and claim\n  // - all sorts of JSON looking stuff\n  const LITERALS_MODE = {\n    scope: \"literal\",\n    beginKeywords: LITERALS.join(\" \"),\n  };\n\n  return {\n    name: '<PERSON><PERSON><PERSON>',\n    aliases: ['jsonc'],\n    keywords:{\n      literal: LITERALS,\n    },\n    contains: [\n      ATTRIBUTE,\n      PUNCTUATION,\n      hljs.QUOTE_STRING_MODE,\n      LITERALS_MODE,\n      hljs.C_NUMBER_MODE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE\n    ],\n    illegal: '\\\\S'\n  };\n}\n\nexport { json as default };\n"], "mappings": ";;;AAQA,SAAS,KAAK,MAAM;AAClB,QAAM,YAAY;AAAA,IAChB,WAAW;AAAA,IACX,OAAO;AAAA,IACP,WAAW;AAAA,EACb;AACA,QAAM,cAAc;AAAA,IAClB,OAAO;AAAA,IACP,WAAW;AAAA,IACX,WAAW;AAAA,EACb;AACA,QAAM,WAAW;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAMA,QAAM,gBAAgB;AAAA,IACpB,OAAO;AAAA,IACP,eAAe,SAAS,KAAK,GAAG;AAAA,EAClC;AAEA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS,CAAC,OAAO;AAAA,IACjB,UAAS;AAAA,MACP,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,IACA,SAAS;AAAA,EACX;AACF;", "names": []}