import {
  supportStatic
} from "./chunk-JHAOQP73.js";
import {
  DatePicker$1
} from "./chunk-GFI6XVUE.js";
import {
  __assign,
  __awaiter,
  __decorate,
  __extends,
  __generator,
  __metadata,
  __rest
} from "./chunk-F25BIIHK.js";
import {
  FormItem,
  anyChanged,
  autobind,
  createObject,
  filterDate,
  getVariable,
  isPureVariable,
  normalizeDate,
  resolveEventData,
  resolveVariableAndFilter,
  str2function
} from "./chunk-LZQZ2OHM.js";
import {
  init_moment,
  moment_default
} from "./chunk-CNJUQDSN.js";
import {
  require_react
} from "./chunk-ZUWHLQVA.js";
import {
  __toESM
} from "./chunk-GFT2G5UO.js";

// node_modules/amis/esm/renderers/Form/InputDate.js
var import_react = __toESM(require_react());
init_moment();
var DateControl = (
  /** @class */
  function(_super) {
    __extends(DateControl2, _super);
    function DateControl2(props) {
      var _this = _super.call(this, props) || this;
      _this.placeholder = "";
      var minDate = props.minDate, maxDate = props.maxDate, value = props.value, defaultValue = props.defaultValue, setPrinstineValue = props.setPrinstineValue, data = props.data, format = props.format, valueFormat = props.valueFormat, utc = props.utc, changeMotivation = props.changeMotivation;
      if (defaultValue && value === defaultValue) {
        var date = filterDate(defaultValue, data, valueFormat || format);
        setPrinstineValue((utc ? moment_default.utc(date) : date).format(valueFormat || format));
      } else if (changeMotivation === "formulaChanged" && defaultValue && value) {
        var date = normalizeDate(value, valueFormat || format);
        if (date && date.format(valueFormat || format) !== value) {
          setPrinstineValue(date.format(valueFormat || format));
        }
      }
      var schedulesData = props.schedules;
      if (typeof schedulesData === "string") {
        var resolved = resolveVariableAndFilter(schedulesData, data, "| raw");
        if (Array.isArray(resolved)) {
          schedulesData = resolved;
        }
      }
      _this.state = {
        minDate: minDate ? filterDate(minDate, data, valueFormat || format) : void 0,
        maxDate: maxDate ? filterDate(maxDate, data, valueFormat || format) : void 0,
        schedules: schedulesData
      };
      return _this;
    }
    DateControl2.prototype.componentDidUpdate = function(prevProps) {
      var props = this.props;
      if (prevProps.defaultValue !== props.defaultValue) {
        var date = filterDate(props.defaultValue, props.data, props.valueFormat || props.format);
        props.setPrinstineValue((props.utc ? moment_default.utc(date) : date).format(props.valueFormat || props.format));
      }
      if (prevProps.minDate !== props.minDate || prevProps.maxDate !== props.maxDate || prevProps.data !== props.data) {
        this.setState({
          minDate: props.minDate ? filterDate(props.minDate, props.data, this.props.valueFormat || this.props.format) : void 0,
          maxDate: props.maxDate ? filterDate(props.maxDate, props.data, this.props.valueFormat || this.props.format) : void 0
        });
      }
      if (anyChanged(["schedules", "data"], prevProps, props) && typeof props.schedules === "string" && isPureVariable(props.schedules)) {
        var schedulesData = resolveVariableAndFilter(props.schedules, props.data, "| raw");
        var preSchedulesData = resolveVariableAndFilter(prevProps.schedules, prevProps.data, "| raw");
        if (Array.isArray(schedulesData) && preSchedulesData !== schedulesData) {
          this.setState({
            schedules: schedulesData
          });
        }
      }
    };
    DateControl2.prototype.onScheduleClick = function(scheduleData) {
      var _a = this.props, scheduleAction = _a.scheduleAction, onAction = _a.onAction, data = _a.data, __ = _a.translate;
      var defaultscheduleAction = {
        actionType: "dialog",
        dialog: {
          title: __("Schedule"),
          actions: [],
          closeOnEsc: true,
          body: {
            type: "table",
            columns: [
              {
                name: "time",
                label: __("Time")
              },
              {
                name: "content",
                label: __("Content")
              }
            ],
            data: "${scheduleData}"
          }
        }
      };
      onAction && onAction(null, scheduleAction || defaultscheduleAction, createObject(data, scheduleData));
    };
    DateControl2.prototype.getRef = function(ref) {
      while (ref && ref.getWrappedInstance) {
        ref = ref.getWrappedInstance();
      }
      this.dateRef = ref;
    };
    DateControl2.prototype.dispatchEvent = function(e) {
      var _a = this.props, dispatchEvent = _a.dispatchEvent, value = _a.value;
      dispatchEvent(e, resolveEventData(this.props, { value }));
    };
    DateControl2.prototype.doAction = function(action, data, throwErrors) {
      var _a, _b, _c, _d;
      var _e = this.props, resetValue = _e.resetValue, formStore = _e.formStore, store = _e.store, name = _e.name;
      if (action.actionType === "clear") {
        (_a = this.dateRef) === null || _a === void 0 ? void 0 : _a.clear();
        return;
      }
      if (action.actionType === "reset") {
        var pristineVal = (_c = getVariable((_b = formStore === null || formStore === void 0 ? void 0 : formStore.pristine) !== null && _b !== void 0 ? _b : store === null || store === void 0 ? void 0 : store.pristine, name)) !== null && _c !== void 0 ? _c : resetValue;
        (_d = this.dateRef) === null || _d === void 0 ? void 0 : _d.reset(pristineVal);
      }
    };
    DateControl2.prototype.setData = function(value) {
      var _a = this.props, data = _a.data, valueFormat = _a.valueFormat, format = _a.format, utc = _a.utc, onChange = _a.onChange;
      if (typeof value === "string" || typeof value === "number" || value instanceof Date) {
        var date = filterDate(value, data, valueFormat || format);
        value = (utc ? moment_default.utc(date) : date).format(valueFormat || format);
      }
      onChange(value);
    };
    DateControl2.prototype.handleChange = function(nextValue) {
      return __awaiter(this, void 0, void 0, function() {
        var dispatchEvent, dispatcher;
        return __generator(this, function(_a) {
          dispatchEvent = this.props.dispatchEvent;
          dispatcher = dispatchEvent("change", resolveEventData(this.props, { value: nextValue }));
          this.props.onChange(nextValue);
          return [
            2
            /*return*/
          ];
        });
      });
    };
    DateControl2.prototype.handleClick = function(date) {
      return __awaiter(this, void 0, void 0, function() {
        var _a, dispatchEvent, utc, valueFormat, format;
        return __generator(this, function(_b) {
          _a = this.props, dispatchEvent = _a.dispatchEvent, utc = _a.utc, valueFormat = _a.valueFormat, format = _a.format;
          dispatchEvent("click", resolveEventData(this.props, {
            value: (utc ? moment_default.utc(date) : date).format(valueFormat || format)
          }));
          return [
            2
            /*return*/
          ];
        });
      });
    };
    DateControl2.prototype.handleMouseEnter = function(date) {
      return __awaiter(this, void 0, void 0, function() {
        var _a, dispatchEvent, utc, valueFormat, format;
        return __generator(this, function(_b) {
          _a = this.props, dispatchEvent = _a.dispatchEvent, utc = _a.utc, valueFormat = _a.valueFormat, format = _a.format;
          dispatchEvent("mouseenter", resolveEventData(this.props, {
            value: (utc ? moment_default.utc(date) : date).format(valueFormat || format)
          }));
          return [
            2
            /*return*/
          ];
        });
      });
    };
    DateControl2.prototype.handleMouseLeave = function(date) {
      return __awaiter(this, void 0, void 0, function() {
        var _a, dispatchEvent, utc, valueFormat, format;
        return __generator(this, function(_b) {
          _a = this.props, dispatchEvent = _a.dispatchEvent, utc = _a.utc, valueFormat = _a.valueFormat, format = _a.format;
          dispatchEvent("mouseleave", resolveEventData(this.props, {
            value: (utc ? moment_default.utc(date) : date).format(valueFormat || format)
          }));
          return [
            2
            /*return*/
          ];
        });
      });
    };
    DateControl2.prototype.isDisabledDate = function(currentDate) {
      var disabledDate = this.props.disabledDate;
      var fn = typeof disabledDate === "string" ? str2function(disabledDate, "currentDate", "props") : disabledDate;
      if (typeof fn === "function") {
        return fn(currentDate, this.props);
      }
      return false;
    };
    DateControl2.prototype.render = function() {
      var _a = this.props, className = _a.className, style = _a.style, defaultValue = _a.defaultValue, defaultData = _a.defaultData, cx = _a.classnames, minDate = _a.minDate, maxDate = _a.maxDate, type = _a.type, format = _a.format, timeFormat = _a.timeFormat, valueFormat = _a.valueFormat, env = _a.env, largeMode = _a.largeMode, render = _a.render, mobileUI = _a.mobileUI, placeholder = _a.placeholder, rest = __rest(_a, ["className", "style", "defaultValue", "defaultData", "classnames", "minDate", "maxDate", "type", "format", "timeFormat", "valueFormat", "env", "largeMode", "render", "mobileUI", "placeholder"]);
      if (type === "time" && timeFormat) {
        valueFormat = format = timeFormat;
      }
      return import_react.default.createElement(
        "div",
        { className: cx("DateControl", {
          "is-date": /date$/.test(type),
          "is-datetime": /datetime$/.test(type)
        }, className) },
        import_react.default.createElement(DatePicker$1, __assign({}, rest, { env, placeholder: placeholder !== null && placeholder !== void 0 ? placeholder : this.placeholder, mobileUI, popOverContainer: mobileUI ? env === null || env === void 0 ? void 0 : env.getModalContainer : rest.popOverContainer || env.getModalContainer, popOverContainerSelector: rest.popOverContainerSelector }, this.state, { valueFormat: valueFormat || format, minDateRaw: this.props.minDate, maxDateRaw: this.props.maxDate, classnames: cx, onRef: this.getRef, schedules: this.state.schedules, largeMode, onScheduleClick: this.onScheduleClick.bind(this), onChange: this.handleChange, onFocus: this.dispatchEvent, onBlur: this.dispatchEvent, disabledDate: this.isDisabledDate, onClick: this.handleClick, onMouseEnter: this.handleMouseEnter, onMouseLeave: this.handleMouseLeave }))
      );
    };
    DateControl2.defaultProps = {
      format: "X",
      viewMode: "days",
      inputFormat: "YYYY-MM-DD",
      timeConstraints: {
        minutes: {
          step: 1
        }
      },
      clearable: true
    };
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], DateControl2.prototype, "getRef", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], DateControl2.prototype, "dispatchEvent", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", Promise)
    ], DateControl2.prototype, "handleChange", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", Promise)
    ], DateControl2.prototype, "handleClick", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", Promise)
    ], DateControl2.prototype, "handleMouseEnter", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", Promise)
    ], DateControl2.prototype, "handleMouseLeave", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], DateControl2.prototype, "isDisabledDate", null);
    __decorate([
      supportStatic(),
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", void 0)
    ], DateControl2.prototype, "render", null);
    return DateControl2;
  }(import_react.default.PureComponent)
);
var DateControlRenderer = (
  /** @class */
  function(_super) {
    __extends(DateControlRenderer2, _super);
    function DateControlRenderer2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.placeholder = _this.props.translate("Date.placeholder");
      return _this;
    }
    DateControlRenderer2.defaultProps = __assign(__assign({}, DateControl.defaultProps), { strictMode: false });
    DateControlRenderer2 = __decorate([
      FormItem({
        type: "input-date",
        weight: -150
      })
    ], DateControlRenderer2);
    return DateControlRenderer2;
  }(DateControl)
);
var DatetimeControlRenderer = (
  /** @class */
  function(_super) {
    __extends(DatetimeControlRenderer2, _super);
    function DatetimeControlRenderer2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.placeholder = _this.props.translate("DateTime.placeholder");
      return _this;
    }
    DatetimeControlRenderer2.defaultProps = __assign(__assign({}, DateControl.defaultProps), { inputFormat: "YYYY-MM-DD HH:mm:ss", closeOnSelect: true, strictMode: false });
    DatetimeControlRenderer2 = __decorate([
      FormItem({
        type: "input-datetime"
      })
    ], DatetimeControlRenderer2);
    return DatetimeControlRenderer2;
  }(DateControl)
);
var TimeControlRenderer = (
  /** @class */
  function(_super) {
    __extends(TimeControlRenderer2, _super);
    function TimeControlRenderer2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.placeholder = _this.props.translate("Time.placeholder");
      return _this;
    }
    TimeControlRenderer2.defaultProps = __assign(__assign({}, DateControl.defaultProps), { inputFormat: "HH:mm", viewMode: "time", closeOnSelect: true });
    TimeControlRenderer2 = __decorate([
      FormItem({
        type: "input-time"
      })
    ], TimeControlRenderer2);
    return TimeControlRenderer2;
  }(DateControl)
);
var MonthControlRenderer = (
  /** @class */
  function(_super) {
    __extends(MonthControlRenderer2, _super);
    function MonthControlRenderer2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.placeholder = _this.props.translate("Month.placeholder");
      return _this;
    }
    MonthControlRenderer2.defaultProps = __assign(__assign({}, DateControl.defaultProps), { inputFormat: "YYYY-MM", viewMode: "months", closeOnSelect: true, strictMode: false });
    MonthControlRenderer2 = __decorate([
      FormItem({
        type: "input-month"
      })
    ], MonthControlRenderer2);
    return MonthControlRenderer2;
  }(DateControl)
);
var QuarterControlRenderer = (
  /** @class */
  function(_super) {
    __extends(QuarterControlRenderer2, _super);
    function QuarterControlRenderer2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.placeholder = _this.props.translate("Quarter.placeholder");
      return _this;
    }
    QuarterControlRenderer2.defaultProps = __assign(__assign({}, DateControl.defaultProps), { inputFormat: "YYYY [Q]Q", viewMode: "quarters", closeOnSelect: true, strictMode: false });
    QuarterControlRenderer2 = __decorate([
      FormItem({
        type: "input-quarter"
      })
    ], QuarterControlRenderer2);
    return QuarterControlRenderer2;
  }(DateControl)
);
var YearControlRenderer = (
  /** @class */
  function(_super) {
    __extends(YearControlRenderer2, _super);
    function YearControlRenderer2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.placeholder = _this.props.translate("Year.placeholder");
      return _this;
    }
    YearControlRenderer2.defaultProps = __assign(__assign({}, DateControl.defaultProps), { inputFormat: "YYYY", viewMode: "years", closeOnSelect: true, strictMode: false });
    YearControlRenderer2 = __decorate([
      FormItem({
        type: "input-year"
      })
    ], YearControlRenderer2);
    return YearControlRenderer2;
  }(DateControl)
);

export {
  DateControl,
  DateControlRenderer,
  DatetimeControlRenderer,
  TimeControlRenderer,
  MonthControlRenderer,
  QuarterControlRenderer,
  YearControlRenderer
};
//# sourceMappingURL=chunk-BQ7HYGA6.js.map
