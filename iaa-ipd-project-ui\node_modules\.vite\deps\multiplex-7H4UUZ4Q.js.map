{"version": 3, "sources": ["../../codemirror/addon/mode/multiplex.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n\"use strict\";\n\nCodeMirror.multiplexingMode = function(outer /*, others */) {\n  // Others should be {open, close, mode [, delimStyle] [, innerStyle] [, parseDelimiters]} objects\n  var others = Array.prototype.slice.call(arguments, 1);\n\n  function indexOf(string, pattern, from, returnEnd) {\n    if (typeof pattern == \"string\") {\n      var found = string.indexOf(pattern, from);\n      return returnEnd && found > -1 ? found + pattern.length : found;\n    }\n    var m = pattern.exec(from ? string.slice(from) : string);\n    return m ? m.index + from + (returnEnd ? m[0].length : 0) : -1;\n  }\n\n  return {\n    startState: function() {\n      return {\n        outer: CodeMirror.startState(outer),\n        innerActive: null,\n        inner: null,\n        startingInner: false\n      };\n    },\n\n    copyState: function(state) {\n      return {\n        outer: CodeMirror.copyState(outer, state.outer),\n        innerActive: state.innerActive,\n        inner: state.innerActive && CodeMirror.copyState(state.innerActive.mode, state.inner),\n        startingInner: state.startingInner\n      };\n    },\n\n    token: function(stream, state) {\n      if (!state.innerActive) {\n        var cutOff = Infinity, oldContent = stream.string;\n        for (var i = 0; i < others.length; ++i) {\n          var other = others[i];\n          var found = indexOf(oldContent, other.open, stream.pos);\n          if (found == stream.pos) {\n            if (!other.parseDelimiters) stream.match(other.open);\n            state.startingInner = !!other.parseDelimiters\n            state.innerActive = other;\n\n            // Get the outer indent, making sure to handle CodeMirror.Pass\n            var outerIndent = 0;\n            if (outer.indent) {\n              var possibleOuterIndent = outer.indent(state.outer, \"\", \"\");\n              if (possibleOuterIndent !== CodeMirror.Pass) outerIndent = possibleOuterIndent;\n            }\n\n            state.inner = CodeMirror.startState(other.mode, outerIndent);\n            return other.delimStyle && (other.delimStyle + \" \" + other.delimStyle + \"-open\");\n          } else if (found != -1 && found < cutOff) {\n            cutOff = found;\n          }\n        }\n        if (cutOff != Infinity) stream.string = oldContent.slice(0, cutOff);\n        var outerToken = outer.token(stream, state.outer);\n        if (cutOff != Infinity) stream.string = oldContent;\n        return outerToken;\n      } else {\n        var curInner = state.innerActive, oldContent = stream.string;\n        if (!curInner.close && stream.sol()) {\n          state.innerActive = state.inner = null;\n          return this.token(stream, state);\n        }\n        var found = curInner.close && !state.startingInner ?\n            indexOf(oldContent, curInner.close, stream.pos, curInner.parseDelimiters) : -1;\n        if (found == stream.pos && !curInner.parseDelimiters) {\n          stream.match(curInner.close);\n          state.innerActive = state.inner = null;\n          return curInner.delimStyle && (curInner.delimStyle + \" \" + curInner.delimStyle + \"-close\");\n        }\n        if (found > -1) stream.string = oldContent.slice(0, found);\n        var innerToken = curInner.mode.token(stream, state.inner);\n        if (found > -1) stream.string = oldContent;\n        else if (stream.pos > stream.start) state.startingInner = false\n\n        if (found == stream.pos && curInner.parseDelimiters)\n          state.innerActive = state.inner = null;\n\n        if (curInner.innerStyle) {\n          if (innerToken) innerToken = innerToken + \" \" + curInner.innerStyle;\n          else innerToken = curInner.innerStyle;\n        }\n\n        return innerToken;\n      }\n    },\n\n    indent: function(state, textAfter, line) {\n      var mode = state.innerActive ? state.innerActive.mode : outer;\n      if (!mode.indent) return CodeMirror.Pass;\n      return mode.indent(state.innerActive ? state.inner : state.outer, textAfter, line);\n    },\n\n    blankLine: function(state) {\n      var mode = state.innerActive ? state.innerActive.mode : outer;\n      if (mode.blankLine) {\n        mode.blankLine(state.innerActive ? state.inner : state.outer);\n      }\n      if (!state.innerActive) {\n        for (var i = 0; i < others.length; ++i) {\n          var other = others[i];\n          if (other.open === \"\\n\") {\n            state.innerActive = other;\n            state.inner = CodeMirror.startState(other.mode, mode.indent ? mode.indent(state.outer, \"\", \"\") : 0);\n          }\n        }\n      } else if (state.innerActive.close === \"\\n\") {\n        state.innerActive = state.inner = null;\n      }\n    },\n\n    electricChars: outer.electricChars,\n\n    innerMode: function(state) {\n      return state.inner ? {state: state.inner, mode: state.innerActive.mode} : {state: state.outer, mode: outer};\n    }\n  };\n};\n\n});\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACxB;AAEA,MAAAA,YAAW,mBAAmB,SAAS,OAAqB;AAE1D,YAAI,SAAS,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AAEpD,iBAAS,QAAQ,QAAQ,SAAS,MAAM,WAAW;AACjD,cAAI,OAAO,WAAW,UAAU;AAC9B,gBAAI,QAAQ,OAAO,QAAQ,SAAS,IAAI;AACxC,mBAAO,aAAa,QAAQ,KAAK,QAAQ,QAAQ,SAAS;AAAA,UAC5D;AACA,cAAI,IAAI,QAAQ,KAAK,OAAO,OAAO,MAAM,IAAI,IAAI,MAAM;AACvD,iBAAO,IAAI,EAAE,QAAQ,QAAQ,YAAY,EAAE,CAAC,EAAE,SAAS,KAAK;AAAA,QAC9D;AAEA,eAAO;AAAA,UACL,YAAY,WAAW;AACrB,mBAAO;AAAA,cACL,OAAOA,YAAW,WAAW,KAAK;AAAA,cAClC,aAAa;AAAA,cACb,OAAO;AAAA,cACP,eAAe;AAAA,YACjB;AAAA,UACF;AAAA,UAEA,WAAW,SAAS,OAAO;AACzB,mBAAO;AAAA,cACL,OAAOA,YAAW,UAAU,OAAO,MAAM,KAAK;AAAA,cAC9C,aAAa,MAAM;AAAA,cACnB,OAAO,MAAM,eAAeA,YAAW,UAAU,MAAM,YAAY,MAAM,MAAM,KAAK;AAAA,cACpF,eAAe,MAAM;AAAA,YACvB;AAAA,UACF;AAAA,UAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,gBAAI,CAAC,MAAM,aAAa;AACtB,kBAAI,SAAS,UAAU,aAAa,OAAO;AAC3C,uBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACtC,oBAAI,QAAQ,OAAO,CAAC;AACpB,oBAAI,QAAQ,QAAQ,YAAY,MAAM,MAAM,OAAO,GAAG;AACtD,oBAAI,SAAS,OAAO,KAAK;AACvB,sBAAI,CAAC,MAAM;AAAiB,2BAAO,MAAM,MAAM,IAAI;AACnD,wBAAM,gBAAgB,CAAC,CAAC,MAAM;AAC9B,wBAAM,cAAc;AAGpB,sBAAI,cAAc;AAClB,sBAAI,MAAM,QAAQ;AAChB,wBAAI,sBAAsB,MAAM,OAAO,MAAM,OAAO,IAAI,EAAE;AAC1D,wBAAI,wBAAwBA,YAAW;AAAM,oCAAc;AAAA,kBAC7D;AAEA,wBAAM,QAAQA,YAAW,WAAW,MAAM,MAAM,WAAW;AAC3D,yBAAO,MAAM,cAAe,MAAM,aAAa,MAAM,MAAM,aAAa;AAAA,gBAC1E,WAAW,SAAS,MAAM,QAAQ,QAAQ;AACxC,2BAAS;AAAA,gBACX;AAAA,cACF;AACA,kBAAI,UAAU;AAAU,uBAAO,SAAS,WAAW,MAAM,GAAG,MAAM;AAClE,kBAAI,aAAa,MAAM,MAAM,QAAQ,MAAM,KAAK;AAChD,kBAAI,UAAU;AAAU,uBAAO,SAAS;AACxC,qBAAO;AAAA,YACT,OAAO;AACL,kBAAI,WAAW,MAAM,aAAa,aAAa,OAAO;AACtD,kBAAI,CAAC,SAAS,SAAS,OAAO,IAAI,GAAG;AACnC,sBAAM,cAAc,MAAM,QAAQ;AAClC,uBAAO,KAAK,MAAM,QAAQ,KAAK;AAAA,cACjC;AACA,kBAAI,QAAQ,SAAS,SAAS,CAAC,MAAM,gBACjC,QAAQ,YAAY,SAAS,OAAO,OAAO,KAAK,SAAS,eAAe,IAAI;AAChF,kBAAI,SAAS,OAAO,OAAO,CAAC,SAAS,iBAAiB;AACpD,uBAAO,MAAM,SAAS,KAAK;AAC3B,sBAAM,cAAc,MAAM,QAAQ;AAClC,uBAAO,SAAS,cAAe,SAAS,aAAa,MAAM,SAAS,aAAa;AAAA,cACnF;AACA,kBAAI,QAAQ;AAAI,uBAAO,SAAS,WAAW,MAAM,GAAG,KAAK;AACzD,kBAAI,aAAa,SAAS,KAAK,MAAM,QAAQ,MAAM,KAAK;AACxD,kBAAI,QAAQ;AAAI,uBAAO,SAAS;AAAA,uBACvB,OAAO,MAAM,OAAO;AAAO,sBAAM,gBAAgB;AAE1D,kBAAI,SAAS,OAAO,OAAO,SAAS;AAClC,sBAAM,cAAc,MAAM,QAAQ;AAEpC,kBAAI,SAAS,YAAY;AACvB,oBAAI;AAAY,+BAAa,aAAa,MAAM,SAAS;AAAA;AACpD,+BAAa,SAAS;AAAA,cAC7B;AAEA,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,UAEA,QAAQ,SAAS,OAAO,WAAW,MAAM;AACvC,gBAAI,OAAO,MAAM,cAAc,MAAM,YAAY,OAAO;AACxD,gBAAI,CAAC,KAAK;AAAQ,qBAAOA,YAAW;AACpC,mBAAO,KAAK,OAAO,MAAM,cAAc,MAAM,QAAQ,MAAM,OAAO,WAAW,IAAI;AAAA,UACnF;AAAA,UAEA,WAAW,SAAS,OAAO;AACzB,gBAAI,OAAO,MAAM,cAAc,MAAM,YAAY,OAAO;AACxD,gBAAI,KAAK,WAAW;AAClB,mBAAK,UAAU,MAAM,cAAc,MAAM,QAAQ,MAAM,KAAK;AAAA,YAC9D;AACA,gBAAI,CAAC,MAAM,aAAa;AACtB,uBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACtC,oBAAI,QAAQ,OAAO,CAAC;AACpB,oBAAI,MAAM,SAAS,MAAM;AACvB,wBAAM,cAAc;AACpB,wBAAM,QAAQA,YAAW,WAAW,MAAM,MAAM,KAAK,SAAS,KAAK,OAAO,MAAM,OAAO,IAAI,EAAE,IAAI,CAAC;AAAA,gBACpG;AAAA,cACF;AAAA,YACF,WAAW,MAAM,YAAY,UAAU,MAAM;AAC3C,oBAAM,cAAc,MAAM,QAAQ;AAAA,YACpC;AAAA,UACF;AAAA,UAEA,eAAe,MAAM;AAAA,UAErB,WAAW,SAAS,OAAO;AACzB,mBAAO,MAAM,QAAQ,EAAC,OAAO,MAAM,OAAO,MAAM,MAAM,YAAY,KAAI,IAAI,EAAC,OAAO,MAAM,OAAO,MAAM,MAAK;AAAA,UAC5G;AAAA,QACF;AAAA,MACF;AAAA,IAEA,CAAC;AAAA;AAAA;", "names": ["CodeMirror"]}