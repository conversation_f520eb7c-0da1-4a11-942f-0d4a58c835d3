import {
  __assign,
  __decorate,
  __extends
} from "./chunk-F25BIIHK.js";
import {
  Renderer,
  isVisible,
  ucFirst
} from "./chunk-LZQZ2OHM.js";
import {
  require_react
} from "./chunk-ZUWHLQVA.js";
import {
  __toESM
} from "./chunk-GFT2G5UO.js";

// node_modules/amis/esm/renderers/HBox.js
var import_react = __toESM(require_react());
var HBox = (
  /** @class */
  function(_super) {
    __extends(HBox2, _super);
    function HBox2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    HBox2.prototype.renderChild = function(region, node, props) {
      if (props === void 0) {
        props = {};
      }
      var render = this.props.render;
      return render(region, node, props);
    };
    HBox2.prototype.renderColumn = function(column, key, length) {
      var _a;
      var _b = this.props, itemRender = _b.itemRender, data = _b.data, cx = _b.classnames, subFormMode = _b.subFormMode, subFormHorizontal = _b.subFormHorizontal, formMode = _b.formMode, formHorizontal = _b.formHorizontal;
      if (!isVisible(column, data) || !column) {
        return null;
      }
      var style = __assign({ width: column.width, height: column.height }, column.style);
      return import_react.default.createElement("div", { key, className: cx("Hbox-col", style.width === "auto" ? "Hbox-col--auto" : style.width ? "Hbox-col--customWidth" : "", (_a = {}, _a["Hbox-col--v".concat(ucFirst(column.valign))] = column.valign, _a), column.columnClassName), style }, itemRender ? itemRender(column, key, length, this.props) : this.renderChild("column/".concat(key), column.body, {
        formMode: column.mode || subFormMode || formMode,
        formHorizontal: column.horizontal || subFormHorizontal || formHorizontal
      }));
    };
    HBox2.prototype.renderColumns = function() {
      var _this = this;
      var columns = this.props.columns;
      return columns.map(function(column, key) {
        return _this.renderColumn(column, key, columns.length);
      });
    };
    HBox2.prototype.render = function() {
      var _a;
      var _b = this.props, className = _b.className, style = _b.style, cx = _b.classnames, gap = _b.gap, vAlign = _b.valign, hAlign = _b.align;
      return import_react.default.createElement("div", { className: cx("Hbox", className, (_a = {}, _a["Hbox--".concat(gap)] = gap, _a["Hbox--v".concat(ucFirst(vAlign))] = vAlign, _a["Hbox--h".concat(ucFirst(hAlign))] = hAlign, _a)), style }, this.renderColumns());
    };
    HBox2.propsList = ["columns"];
    HBox2.defaultProps = {
      gap: "xs"
    };
    return HBox2;
  }(import_react.default.Component)
);
var HBoxRenderer = (
  /** @class */
  function(_super) {
    __extends(HBoxRenderer2, _super);
    function HBoxRenderer2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    HBoxRenderer2 = __decorate([
      Renderer({
        type: "hbox"
      })
    ], HBoxRenderer2);
    return HBoxRenderer2;
  }(HBox)
);

export {
  HBox,
  HBoxRenderer
};
//# sourceMappingURL=chunk-AI6O5AVY.js.map
