import {
  Checkbox$1
} from "./chunk-GFI6XVUE.js";
import "./chunk-ZRTRFIWU.js";
import "./chunk-6C4OBBQZ.js";
import "./chunk-M5OFQAQB.js";
import "./chunk-YPPVVTGH.js";
import {
  __decorate,
  __extends,
  __metadata,
  __rest
} from "./chunk-F25BIIHK.js";
import {
  Renderer,
  autobind,
  buildStyle
} from "./chunk-LZQZ2OHM.js";
import "./chunk-QMS2VRR4.js";
import "./chunk-5QW7M2DY.js";
import "./chunk-ZJNJ4ZV3.js";
import "./chunk-KEABM62F.js";
import "./chunk-CNJUQDSN.js";
import "./chunk-YZFIV7S4.js";
import "./chunk-7XBFYOTW.js";
import {
  require_react
} from "./chunk-ZUWHLQVA.js";
import "./chunk-KNGT5NUO.js";
import "./chunk-X3JSFYRQ.js";
import {
  __toESM
} from "./chunk-GFT2G5UO.js";

// node_modules/amis/esm/renderers/Card2.js
var import_react = __toESM(require_react());
var Card2 = (
  /** @class */
  function(_super) {
    __extends(Card22, _super);
    function Card22() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    Card22.prototype.handleClick = function(e) {
      var _a = this.props, checkOnItemClick = _a.checkOnItemClick, selectable = _a.selectable;
      if (checkOnItemClick && selectable) {
        this.handleCheck();
      }
    };
    Card22.prototype.handleCheck = function() {
      var _a, _b;
      (_b = (_a = this.props).onCheck) === null || _b === void 0 ? void 0 : _b.call(_a);
    };
    Card22.prototype.renderCheckbox = function() {
      var _a = this.props, selectable = _a.selectable, cx = _a.classnames, multiple = _a.multiple, disabled = _a.disabled, selected = _a.selected, hideCheckToggler = _a.hideCheckToggler, checkOnItemClick = _a.checkOnItemClick, checkboxClassname = _a.checkboxClassname;
      if (!selectable || checkOnItemClick && hideCheckToggler) {
        return null;
      }
      return import_react.default.createElement(Checkbox$1, { className: cx("Card2-checkbox", checkboxClassname), type: multiple ? "checkbox" : "radio", disabled, checked: selected, onChange: this.handleCheck });
    };
    Card22.prototype.renderBody = function() {
      var _a = this.props, body = _a.body, render = _a.render, cx = _a.classnames, bodyClassName = _a.bodyClassName, rest = __rest(_a, ["body", "render", "classnames", "bodyClassName"]);
      return import_react.default.createElement("div", { className: cx("Card2-body", bodyClassName), onClick: this.handleClick }, body ? render("body", body, rest) : null);
    };
    Card22.prototype.render = function() {
      var _a = this.props, className = _a.className, wrapperComponent = _a.wrapperComponent, cx = _a.classnames, style = _a.style, item = _a.item, selected = _a.selected, checkOnItemClick = _a.checkOnItemClick;
      var Component = wrapperComponent || "div";
      return import_react.default.createElement(
        Component,
        { className: cx("Card2", className, {
          "checkOnItem": checkOnItemClick,
          "is-checked": selected
        }), style: buildStyle(style, item) },
        this.renderBody(),
        this.renderCheckbox()
      );
    };
    Card22.propsList = ["body", "className"];
    Card22.defaultProps = {
      className: ""
    };
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], Card22.prototype, "handleClick", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", void 0)
    ], Card22.prototype, "handleCheck", null);
    return Card22;
  }(import_react.default.Component)
);
var Card2Renderer = (
  /** @class */
  function(_super) {
    __extends(Card2Renderer2, _super);
    function Card2Renderer2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    Card2Renderer2 = __decorate([
      Renderer({
        type: "card2"
      })
    ], Card2Renderer2);
    return Card2Renderer2;
  }(Card2)
);
export {
  Card2Renderer,
  Card2 as default
};
//# sourceMappingURL=Card2-Q3B6TAK7.js.map
