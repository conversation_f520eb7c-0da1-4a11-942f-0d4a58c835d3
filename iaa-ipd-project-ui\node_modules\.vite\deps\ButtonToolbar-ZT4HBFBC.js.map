{"version": 3, "sources": ["../../amis/esm/renderers/Form/ButtonToolbar.js"], "sourcesContent": ["/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __decorate } from 'tslib';\nimport React from 'react';\nimport { FormItem } from 'amis-core';\n\nvar ButtonToolbar = /** @class */ (function (_super) {\n    __extends(ButtonToolbar, _super);\n    function ButtonToolbar() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /**\n     * 这个方法editor里要用作hack，所以不能删掉这个方法\n     * @returns\n     */\n    ButtonToolbar.prototype.renderButtons = function () {\n        var _a = this.props, render = _a.render, ns = _a.classPrefix, buttons = _a.buttons;\n        return Array.isArray(buttons)\n            ? buttons.map(function (button, key) {\n                return render(\"button/\".concat(key), button, {\n                    key: key\n                });\n            })\n            : null;\n    };\n    ButtonToolbar.prototype.render = function () {\n        var _a = this.props, buttons = _a.buttons, className = _a.className, cx = _a.classnames, render = _a.render, style = _a.style;\n        return (React.createElement(\"div\", { className: cx('ButtonToolbar', className) }, this.renderButtons()));\n    };\n    ButtonToolbar.propsList = ['buttons', 'className'];\n    return ButtonToolbar;\n}(React.Component));\nvar ButtonToolbarRenderer = /** @class */ (function (_super) {\n    __extends(ButtonToolbarRenderer, _super);\n    function ButtonToolbarRenderer() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    ButtonToolbarRenderer = __decorate([\n        FormItem({\n            type: 'button-toolbar',\n            strictMode: false\n        })\n    ], ButtonToolbarRenderer);\n    return ButtonToolbarRenderer;\n}(ButtonToolbar));\n\nexport { ButtonToolbarRenderer, ButtonToolbar as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAOA,mBAAkB;AAGlB,IAAI;AAAA;AAAA,EAA+B,SAAU,QAAQ;AACjD,cAAUA,gBAAe,MAAM;AAC/B,aAASA,iBAAgB;AACrB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AAKA,IAAAA,eAAc,UAAU,gBAAgB,WAAY;AAChD,UAAI,KAAK,KAAK,OAAO,SAAS,GAAG,QAAQ,KAAK,GAAG,aAAa,UAAU,GAAG;AAC3E,aAAO,MAAM,QAAQ,OAAO,IACtB,QAAQ,IAAI,SAAU,QAAQ,KAAK;AACjC,eAAO,OAAO,UAAU,OAAO,GAAG,GAAG,QAAQ;AAAA,UACzC;AAAA,QACJ,CAAC;AAAA,MACL,CAAC,IACC;AAAA,IACV;AACA,IAAAA,eAAc,UAAU,SAAS,WAAY;AACzC,UAAI,KAAK,KAAK,OAAO,UAAU,GAAG,SAAS,YAAY,GAAG,WAAW,KAAK,GAAG,YAAY,SAAS,GAAG,QAAQ,QAAQ,GAAG;AACxH,aAAQ,aAAAC,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,iBAAiB,SAAS,EAAE,GAAG,KAAK,cAAc,CAAC;AAAA,IAC1G;AACA,IAAAD,eAAc,YAAY,CAAC,WAAW,WAAW;AACjD,WAAOA;AAAA,EACX,EAAE,aAAAC,QAAM,SAAS;AAAA;AACjB,IAAI;AAAA;AAAA,EAAuC,SAAU,QAAQ;AACzD,cAAUC,wBAAuB,MAAM;AACvC,aAASA,yBAAwB;AAC7B,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,yBAAwB,WAAW;AAAA,MAC/B,SAAS;AAAA,QACL,MAAM;AAAA,QACN,YAAY;AAAA,MAChB,CAAC;AAAA,IACL,GAAGA,sBAAqB;AACxB,WAAOA;AAAA,EACX,EAAE,aAAa;AAAA;", "names": ["ButtonToolbar", "React", "ButtonT<PERSON><PERSON><PERSON><PERSON><PERSON>"]}