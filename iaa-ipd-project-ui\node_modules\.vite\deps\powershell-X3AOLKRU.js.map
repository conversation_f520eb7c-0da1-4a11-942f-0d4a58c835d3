{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/powershell/powershell.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    // the default separators except `$-`\n    wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#%\\^\\&\\*\\(\\)\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n    comments: {\n        lineComment: '#',\n        blockComment: ['<#', '#>']\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"', notIn: ['string'] },\n        { open: \"'\", close: \"'\", notIn: ['string', 'comment'] }\n    ],\n    surroundingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" }\n    ],\n    folding: {\n        markers: {\n            start: new RegExp('^\\\\s*#region\\\\b'),\n            end: new RegExp('^\\\\s*#endregion\\\\b')\n        }\n    }\n};\nexport var language = {\n    defaultToken: '',\n    ignoreCase: true,\n    tokenPostfix: '.ps1',\n    brackets: [\n        { token: 'delimiter.curly', open: '{', close: '}' },\n        { token: 'delimiter.square', open: '[', close: ']' },\n        { token: 'delimiter.parenthesis', open: '(', close: ')' }\n    ],\n    keywords: [\n        'begin',\n        'break',\n        'catch',\n        'class',\n        'continue',\n        'data',\n        'define',\n        'do',\n        'dynamicparam',\n        'else',\n        'elseif',\n        'end',\n        'exit',\n        'filter',\n        'finally',\n        'for',\n        'foreach',\n        'from',\n        'function',\n        'if',\n        'in',\n        'param',\n        'process',\n        'return',\n        'switch',\n        'throw',\n        'trap',\n        'try',\n        'until',\n        'using',\n        'var',\n        'while',\n        'workflow',\n        'parallel',\n        'sequence',\n        'inlinescript',\n        'configuration'\n    ],\n    helpKeywords: /SYNOPSIS|DESCRIPTION|PARAMETER|EXAMPLE|INPUTS|OUTPUTS|NOTES|LINK|COMPONENT|ROLE|FUNCTIONALITY|FORWARDHELPTARGETNAME|FORWARDHELPCATEGORY|REMOTEHELPRUNSPACE|EXTERNALHELP/,\n    // we include these common regular expressions\n    symbols: /[=><!~?&%|+\\-*\\/\\^;\\.,]+/,\n    escapes: /`(?:[abfnrtv\\\\\"'$]|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n    // The main tokenizer for our languages\n    tokenizer: {\n        root: [\n            // commands and keywords\n            [\n                /[a-zA-Z_][\\w-]*/,\n                {\n                    cases: {\n                        '@keywords': { token: 'keyword.$0' },\n                        '@default': ''\n                    }\n                }\n            ],\n            // whitespace\n            [/[ \\t\\r\\n]+/, ''],\n            // labels\n            [/^:\\w*/, 'metatag'],\n            // variables\n            [\n                /\\$(\\{((global|local|private|script|using):)?[\\w]+\\}|((global|local|private|script|using):)?[\\w]+)/,\n                'variable'\n            ],\n            // Comments\n            [/<#/, 'comment', '@comment'],\n            [/#.*$/, 'comment'],\n            // delimiters\n            [/[{}()\\[\\]]/, '@brackets'],\n            [/@symbols/, 'delimiter'],\n            // numbers\n            [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, 'number.float'],\n            [/0[xX][0-9a-fA-F_]*[0-9a-fA-F]/, 'number.hex'],\n            [/\\d+?/, 'number'],\n            // delimiter: after number because of .\\d floats\n            [/[;,.]/, 'delimiter'],\n            // strings:\n            [/\\@\"/, 'string', '@herestring.\"'],\n            [/\\@'/, 'string', \"@herestring.'\"],\n            [\n                /\"/,\n                {\n                    cases: {\n                        '@eos': 'string',\n                        '@default': { token: 'string', next: '@string.\"' }\n                    }\n                }\n            ],\n            [\n                /'/,\n                {\n                    cases: {\n                        '@eos': 'string',\n                        '@default': { token: 'string', next: \"@string.'\" }\n                    }\n                }\n            ]\n        ],\n        string: [\n            [\n                /[^\"'\\$`]+/,\n                {\n                    cases: {\n                        '@eos': { token: 'string', next: '@popall' },\n                        '@default': 'string'\n                    }\n                }\n            ],\n            [\n                /@escapes/,\n                {\n                    cases: {\n                        '@eos': { token: 'string.escape', next: '@popall' },\n                        '@default': 'string.escape'\n                    }\n                }\n            ],\n            [\n                /`./,\n                {\n                    cases: {\n                        '@eos': {\n                            token: 'string.escape.invalid',\n                            next: '@popall'\n                        },\n                        '@default': 'string.escape.invalid'\n                    }\n                }\n            ],\n            [\n                /\\$[\\w]+$/,\n                {\n                    cases: {\n                        '$S2==\"': { token: 'variable', next: '@popall' },\n                        '@default': { token: 'string', next: '@popall' }\n                    }\n                }\n            ],\n            [\n                /\\$[\\w]+/,\n                {\n                    cases: {\n                        '$S2==\"': 'variable',\n                        '@default': 'string'\n                    }\n                }\n            ],\n            [\n                /[\"']/,\n                {\n                    cases: {\n                        '$#==$S2': { token: 'string', next: '@pop' },\n                        '@default': {\n                            cases: {\n                                '@eos': { token: 'string', next: '@popall' },\n                                '@default': 'string'\n                            }\n                        }\n                    }\n                }\n            ]\n        ],\n        herestring: [\n            [\n                /^\\s*([\"'])@/,\n                {\n                    cases: {\n                        '$1==$S2': { token: 'string', next: '@pop' },\n                        '@default': 'string'\n                    }\n                }\n            ],\n            [/[^\\$`]+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/`./, 'string.escape.invalid'],\n            [\n                /\\$[\\w]+/,\n                {\n                    cases: {\n                        '$S2==\"': 'variable',\n                        '@default': 'string'\n                    }\n                }\n            ]\n        ],\n        comment: [\n            [/[^#\\.]+/, 'comment'],\n            [/#>/, 'comment', '@pop'],\n            [/(\\.)(@helpKeywords)(?!\\w)/, { token: 'comment.keyword.$2' }],\n            [/[\\.#]/, 'comment']\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA;AAAA,EAEd,aAAa;AAAA,EACb,UAAU;AAAA,IACN,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC7B;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EAC1D;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,SAAS;AAAA,IACL,SAAS;AAAA,MACL,OAAO,IAAI,OAAO,iBAAiB;AAAA,MACnC,KAAK,IAAI,OAAO,oBAAoB;AAAA,IACxC;AAAA,EACJ;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,UAAU;AAAA,IACN,EAAE,OAAO,mBAAmB,MAAM,KAAK,OAAO,IAAI;AAAA,IAClD,EAAE,OAAO,oBAAoB,MAAM,KAAK,OAAO,IAAI;AAAA,IACnD,EAAE,OAAO,yBAAyB,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5D;AAAA,EACA,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,cAAc;AAAA;AAAA,EAEd,SAAS;AAAA,EACT,SAAS;AAAA;AAAA,EAET,WAAW;AAAA,IACP,MAAM;AAAA;AAAA,MAEF;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,aAAa,EAAE,OAAO,aAAa;AAAA,YACnC,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,CAAC,cAAc,EAAE;AAAA;AAAA,MAEjB,CAAC,SAAS,SAAS;AAAA;AAAA,MAEnB;AAAA,QACI;AAAA,QACA;AAAA,MACJ;AAAA;AAAA,MAEA,CAAC,MAAM,WAAW,UAAU;AAAA,MAC5B,CAAC,QAAQ,SAAS;AAAA;AAAA,MAElB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,YAAY,WAAW;AAAA;AAAA,MAExB,CAAC,4BAA4B,cAAc;AAAA,MAC3C,CAAC,iCAAiC,YAAY;AAAA,MAC9C,CAAC,QAAQ,QAAQ;AAAA;AAAA,MAEjB,CAAC,SAAS,WAAW;AAAA;AAAA,MAErB,CAAC,OAAO,UAAU,eAAe;AAAA,MACjC,CAAC,OAAO,UAAU,eAAe;AAAA,MACjC;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ;AAAA,YACR,YAAY,EAAE,OAAO,UAAU,MAAM,YAAY;AAAA,UACrD;AAAA,QACJ;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ;AAAA,YACR,YAAY,EAAE,OAAO,UAAU,MAAM,YAAY;AAAA,UACrD;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,MACJ;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ,EAAE,OAAO,UAAU,MAAM,UAAU;AAAA,YAC3C,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ,EAAE,OAAO,iBAAiB,MAAM,UAAU;AAAA,YAClD,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ;AAAA,cACJ,OAAO;AAAA,cACP,MAAM;AAAA,YACV;AAAA,YACA,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,UAAU,EAAE,OAAO,YAAY,MAAM,UAAU;AAAA,YAC/C,YAAY,EAAE,OAAO,UAAU,MAAM,UAAU;AAAA,UACnD;AAAA,QACJ;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,UAAU;AAAA,YACV,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,WAAW,EAAE,OAAO,UAAU,MAAM,OAAO;AAAA,YAC3C,YAAY;AAAA,cACR,OAAO;AAAA,gBACH,QAAQ,EAAE,OAAO,UAAU,MAAM,UAAU;AAAA,gBAC3C,YAAY;AAAA,cAChB;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,YAAY;AAAA,MACR;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,WAAW,EAAE,OAAO,UAAU,MAAM,OAAO;AAAA,YAC3C,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,MAAM,uBAAuB;AAAA,MAC9B;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,UAAU;AAAA,YACV,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,MAAM,WAAW,MAAM;AAAA,MACxB,CAAC,6BAA6B,EAAE,OAAO,qBAAqB,CAAC;AAAA,MAC7D,CAAC,SAAS,SAAS;AAAA,IACvB;AAAA,EACJ;AACJ;", "names": []}