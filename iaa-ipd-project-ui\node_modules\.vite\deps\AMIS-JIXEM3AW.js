import {
  __decorate,
  __extends
} from "./chunk-F25BIIHK.js";
import {
  Renderer,
  getPropValue
} from "./chunk-LZQZ2OHM.js";
import "./chunk-5QW7M2DY.js";
import "./chunk-ZJNJ4ZV3.js";
import "./chunk-CNJUQDSN.js";
import "./chunk-7XBFYOTW.js";
import {
  require_react
} from "./chunk-ZUWHLQVA.js";
import {
  __toESM
} from "./chunk-GFT2G5UO.js";

// node_modules/amis/esm/renderers/AMIS.js
var import_react = __toESM(require_react());
var AMISRenderer = (
  /** @class */
  function(_super) {
    __extends(AMISRenderer2, _super);
    function AMISRenderer2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    AMISRenderer2.prototype.render = function() {
      var _a = this.props, render = _a.render, props = _a.props, schema = _a.schema;
      var value = getPropValue(this.props) || schema;
      if (typeof value === "string") {
        try {
          value = JSON.parse(value);
        } catch (e) {
          console.warn("amis value must be json string", e);
          value = null;
        }
      }
      return render("amis", value, props);
    };
    AMISRenderer2 = __decorate([
      Renderer({
        type: "amis"
      })
    ], AMISRenderer2);
    return AMISRenderer2;
  }(import_react.default.Component)
);
export {
  AMISRenderer
};
//# sourceMappingURL=AMIS-JIXEM3AW.js.map
