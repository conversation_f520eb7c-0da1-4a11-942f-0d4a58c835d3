"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.VXETable = void 0;
Object.defineProperty(exports, "VxeUI", {
  enumerable: true,
  get: function () {
    return _core.VxeUI;
  }
});
exports.version = exports.validators = exports.use = exports.t = exports.setup = exports.setTheme = exports.setLanguage = exports.setIcon = exports.setI18n = exports.setConfig = exports.saveFile = exports.renderer = exports.readFile = exports.print = exports.modal = exports.menus = exports.log = exports.interceptor = exports.hooks = exports.globalResize = exports.globalEvents = exports.getTheme = exports.getIcon = exports.getI18n = exports.getConfig = exports.formats = exports.default = exports.config = exports.commands = exports.clipboard = exports._t = void 0;
var _core = require("@vxe-ui/core");
var _utils = require("./src/utils");
const version = exports.version = "4.13.41";
_core.VxeUI.version = version;
_core.VxeUI.tableVersion = version;
_core.VxeUI.setConfig({
  emptyCell: '　',
  table: {
    fit: true,
    showHeader: true,
    animat: true,
    delayHover: 250,
    autoResize: true,
    minHeight: 144,
    // keepSource: false,
    // showOverflow: null,
    // showHeaderOverflow: null,
    // showFooterOverflow: null,
    // resizeInterval: 500,
    // size: null,
    // zIndex: null,
    // stripe: false,
    // border: false,
    // round: false,
    // emptyText: '暂无数据',
    // emptyRender: {
    //   name: ''
    // },
    // rowConfig: {
    //   keyField: '_X_ROW_KEY' // 行数据的唯一主键字段名
    // },
    resizeConfig: {
      // refreshDelay: 20
    },
    resizableConfig: {
      dragMode: 'auto',
      showDragTip: true,
      isSyncAutoHeight: true,
      isSyncAutoWidth: true,
      minHeight: 18
    },
    radioConfig: {
      // trigger: 'default'
      strict: true
    },
    rowDragConfig: {
      showIcon: true,
      animation: true,
      showGuidesStatus: true,
      showDragTip: true
    },
    columnDragConfig: {
      showIcon: true,
      animation: true,
      showGuidesStatus: true,
      showDragTip: true
    },
    checkboxConfig: {
      // trigger: 'default',
      strict: true
    },
    tooltipConfig: {
      enterable: true
    },
    validConfig: {
      showMessage: true,
      autoClear: true,
      autoPos: true,
      message: 'inline',
      msgMode: 'single',
      theme: 'beautify'
    },
    columnConfig: {
      maxFixedSize: 4
    },
    cellConfig: {
      padding: true
    },
    headerCellConfig: {
      height: 'unset'
    },
    footerCellConfig: {
      height: 'unset'
    },
    // menuConfig: {
    //   visibleMethod () {}
    // },
    customConfig: {
      // enabled: false,
      allowVisible: true,
      allowResizable: true,
      allowFixed: true,
      allowSort: true,
      showFooter: true,
      placement: 'top-right',
      //  storage: false,
      storeOptions: {
        visible: true,
        resizable: true,
        sort: true,
        fixed: true
        // rowGroup: false,
        // aggFunc: false
      },
      //  checkMethod () {},
      modalOptions: {
        showMaximize: true,
        mask: true,
        lockView: true,
        resize: true,
        escClosable: true
      },
      drawerOptions: {
        mask: true,
        lockView: true,
        escClosable: true,
        resize: true
      }
    },
    sortConfig: {
      // remote: false,
      // trigger: 'default',
      // orders: ['asc', 'desc', null],
      // sortMethod: null,
      showIcon: true,
      allowClear: true,
      allowBtn: true,
      iconLayout: 'vertical'
    },
    filterConfig: {
      // remote: false,
      // filterMethod: null,
      // destroyOnClose: false,
      // isEvery: false,
      showIcon: true
    },
    aggregateConfig: {
      padding: true,
      rowField: 'id',
      parentField: '_X_ROW_PARENT_KEY',
      childrenField: '_X_ROW_CHILDREN',
      mapChildrenField: '_X_ROW_CHILD_LIST',
      indent: 20,
      showIcon: true
    },
    treeConfig: {
      padding: true,
      rowField: 'id',
      parentField: 'parentId',
      childrenField: 'children',
      hasChildField: 'hasChild',
      mapChildrenField: '_X_ROW_CHILD',
      indent: 20,
      showIcon: true
    },
    expandConfig: {
      // trigger: 'default',
      showIcon: true,
      mode: 'fixed'
    },
    editConfig: {
      // mode: 'cell',
      showIcon: true,
      showAsterisk: true,
      autoFocus: true
    },
    importConfig: {
      _typeMaps: {
        csv: 1,
        html: 1,
        xml: 1,
        txt: 1
      }
    },
    exportConfig: {
      _typeMaps: {
        csv: 1,
        html: 1,
        xml: 1,
        txt: 1
      }
    },
    printConfig: {},
    mouseConfig: {
      extension: true
    },
    keyboardConfig: {
      isAll: true,
      isEsc: true
    },
    areaConfig: {
      autoClear: true,
      selectCellByHeader: true,
      selectCellByBody: true,
      extendDirection: {
        top: true,
        left: true,
        bottom: true,
        right: true
      }
    },
    clipConfig: {
      isCopy: true,
      isCut: true,
      isPaste: true
    },
    fnrConfig: {
      isFind: true,
      isReplace: true
    },
    virtualXConfig: {
      // enabled: false,
      gt: 24,
      preSize: 1,
      oSize: 0
    },
    virtualYConfig: {
      // enabled: false,
      gt: 100,
      preSize: 1,
      oSize: 0
    },
    scrollbarConfig: {
      // width: 14,
      // height: 14
    }
  },
  // export: {
  //   types: {}
  // },
  grid: {
    // size: null,
    // zoomConfig: {
    //   escRestore: true
    // },
    formConfig: {
      enabled: true
    },
    pagerConfig: {
      enabled: true
      // perfect: false
    },
    toolbarConfig: {
      enabled: true
      // perfect: false
    },
    proxyConfig: {
      enabled: true,
      autoLoad: true,
      showResponseMsg: true,
      showActiveMsg: true,
      props: {
        list: null,
        result: 'result',
        total: 'page.total',
        message: 'message'
      }
      // beforeItem: null,
      // beforeColumn: null,
      // beforeQuery: null,
      // afterQuery: null,
      // beforeDelete: null,
      // afterDelete: null,
      // beforeSave: null,
      // afterSave: null
    }
  },
  toolbar: {
    // size: null,
    // import: {
    //   mode: 'covering'
    // },
    // export: {
    //   types: ['csv', 'html', 'xml', 'txt']
    // },
    // buttons: []
  }
});
const iconPrefix = 'vxe-table-icon-';
_core.VxeUI.setIcon({
  // table
  TABLE_SORT_ASC: iconPrefix + 'caret-up',
  TABLE_SORT_DESC: iconPrefix + 'caret-down',
  TABLE_FILTER_NONE: iconPrefix + 'funnel',
  TABLE_FILTER_MATCH: iconPrefix + 'funnel',
  TABLE_EDIT: iconPrefix + 'edit',
  TABLE_TITLE_PREFIX: iconPrefix + 'question-circle-fill',
  TABLE_TITLE_SUFFIX: iconPrefix + 'question-circle-fill',
  TABLE_TREE_LOADED: iconPrefix + 'spinner roll',
  TABLE_TREE_OPEN: iconPrefix + 'caret-right rotate90',
  TABLE_TREE_CLOSE: iconPrefix + 'caret-right',
  TABLE_EXPAND_LOADED: iconPrefix + 'spinner roll',
  TABLE_EXPAND_OPEN: iconPrefix + 'arrow-right rotate90',
  TABLE_EXPAND_CLOSE: iconPrefix + 'arrow-right',
  TABLE_CHECKBOX_CHECKED: iconPrefix + 'checkbox-checked-fill',
  TABLE_CHECKBOX_UNCHECKED: iconPrefix + 'checkbox-unchecked',
  TABLE_CHECKBOX_INDETERMINATE: iconPrefix + 'checkbox-indeterminate-fill',
  TABLE_RADIO_CHECKED: iconPrefix + 'radio-checked-fill',
  TABLE_RADIO_UNCHECKED: iconPrefix + 'radio-unchecked',
  TABLE_CUSTOM_SORT: iconPrefix + 'drag-handle',
  TABLE_MENU_OPTIONS: iconPrefix + 'arrow-right',
  TABLE_DRAG_ROW: iconPrefix + 'drag-handle',
  TABLE_DRAG_COLUMN: iconPrefix + 'drag-handle',
  TABLE_DRAG_STATUS_ROW: iconPrefix + 'sort',
  TABLE_DRAG_STATUS_SUB_ROW: iconPrefix + 'add-sub',
  TABLE_DRAG_STATUS_COLUMN: iconPrefix + 'swap',
  TABLE_DRAG_DISABLED: iconPrefix + 'no-drop',
  TABLE_ROW_GROUP_OPEN: iconPrefix + 'arrow-right rotate90',
  TABLE_ROW_GROUP_CLOSE: iconPrefix + 'arrow-right',
  TABLE_AGGREGATION_GROUPING: iconPrefix + 'grouping',
  TABLE_AGGREGATION_VALUES: iconPrefix + 'values',
  TABLE_AGGREGATION_DELETE: iconPrefix + 'close',
  // toolbar
  TOOLBAR_TOOLS_REFRESH: iconPrefix + 'repeat',
  TOOLBAR_TOOLS_REFRESH_LOADING: iconPrefix + 'repeat roll',
  TOOLBAR_TOOLS_IMPORT: iconPrefix + 'upload',
  TOOLBAR_TOOLS_EXPORT: iconPrefix + 'download',
  TOOLBAR_TOOLS_PRINT: iconPrefix + 'print',
  TOOLBAR_TOOLS_FULLSCREEN: iconPrefix + 'fullscreen',
  TOOLBAR_TOOLS_MINIMIZE: iconPrefix + 'minimize',
  TOOLBAR_TOOLS_CUSTOM: iconPrefix + 'custom-column',
  TOOLBAR_TOOLS_FIXED_LEFT: iconPrefix + 'fixed-left',
  TOOLBAR_TOOLS_FIXED_LEFT_ACTIVE: iconPrefix + 'fixed-left-fill',
  TOOLBAR_TOOLS_FIXED_RIGHT: iconPrefix + 'fixed-right',
  TOOLBAR_TOOLS_FIXED_RIGHT_ACTIVE: iconPrefix + 'fixed-right-fill'
});
const setTheme = exports.setTheme = _core.VxeUI.setTheme;
const getTheme = exports.getTheme = _core.VxeUI.getTheme;
const setConfig = exports.setConfig = _core.VxeUI.setConfig;
const getConfig = exports.getConfig = _core.VxeUI.getConfig;
const setIcon = exports.setIcon = _core.VxeUI.setIcon;
const getIcon = exports.getIcon = _core.VxeUI.getIcon;
const setLanguage = exports.setLanguage = _core.VxeUI.setLanguage;
const setI18n = exports.setI18n = _core.VxeUI.setI18n;
const getI18n = exports.getI18n = _core.VxeUI.getI18n;
const globalEvents = exports.globalEvents = _core.VxeUI.globalEvents;
const globalResize = exports.globalResize = _core.VxeUI.globalResize;
const renderer = exports.renderer = _core.VxeUI.renderer;
const validators = exports.validators = _core.VxeUI.validators;
const menus = exports.menus = _core.VxeUI.menus;
const formats = exports.formats = _core.VxeUI.formats;
const commands = exports.commands = _core.VxeUI.commands;
const interceptor = exports.interceptor = _core.VxeUI.interceptor;
const clipboard = exports.clipboard = _core.VxeUI.clipboard;
const log = exports.log = _core.VxeUI.log;
const hooks = exports.hooks = _core.VxeUI.hooks;
const use = exports.use = _core.VxeUI.use;
/**
 * 已废弃
 * @deprecated
 */
const setup = options => {
  return _core.VxeUI.setConfig(options);
};
exports.setup = setup;
_core.VxeUI.setup = setup;
/**
 * 已废弃
 * @deprecated
 */
const config = options => {
  return _core.VxeUI.setConfig(options);
};
exports.config = config;
_core.VxeUI.config = config;
/**
 * 已废弃
 * @deprecated
 */
const t = (key, args) => {
  return _core.VxeUI.getI18n(key, args);
};
exports.t = t;
_core.VxeUI.t = t;
/**
 * 已废弃
 * @deprecated
 */
const _t = (content, args) => {
  return (0, _utils.getFuncText)(content, args);
};
exports._t = _t;
_core.VxeUI._t = _t;
/**
 * 已废弃，兼容老版本
 * @deprecated
 */
const VXETable = exports.VXETable = _core.VxeUI;
/**
 * 已废弃，兼容老版本
 * @deprecated
 */
const saveFile = options => {
  return _core.VxeUI.saveFile(options);
};
/**
 * 已废弃，兼容老版本
 * @deprecated
 */
exports.saveFile = saveFile;
const readFile = options => {
  return _core.VxeUI.readFile(options);
};
/**
 * 已废弃，兼容老版本
 * @deprecated
 */
exports.readFile = readFile;
const print = options => {
  return _core.VxeUI.print(options);
};
/**
 * 已废弃，兼容老版本
 * @deprecated
 */
exports.print = print;
const modal = exports.modal = {
  /**
   * 已废弃，兼容老版本
   * @deprecated
   */
  get(id) {
    return _core.VxeUI.modal.get(id);
  },
  /**
   * 已废弃，兼容老版本
   * @deprecated
   */
  close(id) {
    return _core.VxeUI.modal.close(id);
  },
  /**
   * 已废弃，兼容老版本
   * @deprecated
   */
  open(options) {
    return _core.VxeUI.modal.open(options);
  },
  /**
   * 已废弃，兼容老版本
   * @deprecated
   */
  alert(content, title, options) {
    return _core.VxeUI.modal.alert(content, title, options);
  },
  /**
   * 已废弃，兼容老版本
   * @deprecated
   */
  confirm(content, title, options) {
    return _core.VxeUI.modal.confirm(content, title, options);
  },
  /**
   * 已废弃，兼容老版本
   * @deprecated
   */
  message(content, options) {
    return _core.VxeUI.modal.message(content, options);
  },
  /**
   * 已废弃，兼容老版本
   * @deprecated
   */
  notification(content, title, options) {
    return _core.VxeUI.modal.notification(content, title, options);
  }
};
var _default = exports.default = _core.VxeUI;