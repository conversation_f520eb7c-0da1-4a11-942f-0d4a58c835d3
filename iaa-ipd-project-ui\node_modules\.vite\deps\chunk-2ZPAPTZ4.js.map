{"version": 3, "sources": ["../../amis/esm/renderers/Form/ButtonGroupSelect.js"], "sourcesContent": ["/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __assign, __decorate, __metadata } from 'tslib';\nimport React from 'react';\nimport { getVariable, isObject, getLevelFromClassName, autobind, OptionsControl } from 'amis-core';\nimport { supportStatic } from './StaticHoc.js';\n\nvar ButtonGroupControl = /** @class */ (function (_super) {\n    __extends(ButtonGroupControl, _super);\n    function ButtonGroupControl() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    ButtonGroupControl.prototype.doAction = function (action, data, throwErrors) {\n        var _a, _b;\n        var _c = this.props, resetValue = _c.resetValue, onChange = _c.onChange, formStore = _c.formStore, store = _c.store, name = _c.name;\n        var actionType = action === null || action === void 0 ? void 0 : action.actionType;\n        if (actionType === 'clear') {\n            onChange('');\n        }\n        else if (actionType === 'reset') {\n            var pristineVal = (_b = getVariable((_a = formStore === null || formStore === void 0 ? void 0 : formStore.pristine) !== null && _a !== void 0 ? _a : store === null || store === void 0 ? void 0 : store.pristine, name)) !== null && _b !== void 0 ? _b : resetValue;\n            onChange(pristineVal !== null && pristineVal !== void 0 ? pristineVal : '');\n        }\n    };\n    ButtonGroupControl.prototype.handleToggle = function (option) {\n        var _a = this.props, onToggle = _a.onToggle, multiple = _a.multiple, autoFill = _a.autoFill, onBulkChange = _a.onBulkChange;\n        onToggle(option);\n    };\n    ButtonGroupControl.prototype.reload = function (subpath, query) {\n        var reload = this.props.reloadOptions;\n        reload && reload(subpath, query);\n    };\n    ButtonGroupControl.prototype.getBadgeConfig = function (config, item) {\n        return config\n            ? (item === null || item === void 0 ? void 0 : item.badge) &&\n                (typeof item.badge === 'string' || typeof item.badge === 'number')\n                ? __assign(__assign({}, config), { text: item.badge }) : (item === null || item === void 0 ? void 0 : item.badge) && isObject(item.badge)\n                ? __assign(__assign({}, config), item.badge) : null\n            : item.badge;\n    };\n    ButtonGroupControl.prototype.render = function (props) {\n        var _a;\n        var _this = this;\n        if (props === void 0) { props = this.props; }\n        var render = props.render, ns = props.classPrefix, cx = props.classnames, className = props.className, style = props.style, disabled = props.disabled, options = props.options, value = props.value, labelField = props.labelField, placeholder = props.placeholder, btnClassName = props.btnClassName, btnActiveClassName = props.btnActiveClassName, selectedOptions = props.selectedOptions, buttons = props.buttons, size = props.size, block = props.block, vertical = props.vertical, tiled = props.tiled, badge = props.badge, testIdBuilder = props.testIdBuilder, __ = props.translate;\n        var body = [];\n        var btnLevel = props.btnLevel;\n        var btnActiveLevel = props.btnActiveLevel;\n        btnClassName && (btnLevel = getLevelFromClassName(btnClassName));\n        btnActiveClassName &&\n            (btnActiveLevel = getLevelFromClassName(btnActiveClassName));\n        if (options && options.length) {\n            body = options.map(function (option, key) {\n                var active = !!~selectedOptions.indexOf(option);\n                var optionBadge = _this.getBadgeConfig(badge, option);\n                return render(\"option/\".concat(key), {\n                    label: option[labelField || 'label'],\n                    icon: option.icon,\n                    size: option.size || size,\n                    badge: optionBadge,\n                    type: 'button',\n                    block: block\n                }, {\n                    key: key,\n                    level: (active ? btnActiveLevel : '') || option.level || btnLevel,\n                    className: cx(option.className, btnClassName, active && 'ButtonGroup-button--active'),\n                    disabled: option.disabled || disabled,\n                    testIdBuilder: testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild(\"item-\".concat(option[labelField || 'label'] || key)),\n                    onClick: function (e) {\n                        if (disabled) {\n                            return;\n                        }\n                        _this.handleToggle(option);\n                        e.preventDefault(); // 禁止 onAction 触发\n                    }\n                });\n            });\n        }\n        else if (Array.isArray(buttons)) {\n            body = buttons.map(function (button, key) {\n                var buttonBadge = _this.getBadgeConfig(badge, button);\n                return render(\"button/\".concat(key), __assign(__assign({ size: size, block: block, activeLevel: btnActiveLevel, level: btnLevel, disabled: disabled }, button), { badge: buttonBadge }), {\n                    key: key,\n                    className: cx(button.className, btnClassName)\n                });\n            });\n        }\n        return (React.createElement(\"div\", { className: cx(\"ButtonGroup\", (_a = {\n                    'ButtonGroup--block': block,\n                    'ButtonGroup--vertical': vertical,\n                    'ButtonGroup--tiled': tiled\n                },\n                _a[\"ButtonGroup--\".concat(size)] = size,\n                _a), className) }, body.length ? (body) : (React.createElement(\"span\", { className: \"\".concat(ns, \"ButtonGroup-placeholder\") }, __(placeholder)))));\n    };\n    ButtonGroupControl.defaultProps = {\n        btnLevel: 'default',\n        btnActiveLevel: 'primary',\n        clearable: false,\n        vertical: false\n    };\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], ButtonGroupControl.prototype, \"handleToggle\", null);\n    __decorate([\n        supportStatic(),\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], ButtonGroupControl.prototype, \"render\", null);\n    return ButtonGroupControl;\n}(React.Component));\nvar ButtonGroupControlRenderer = /** @class */ (function (_super) {\n    __extends(ButtonGroupControlRenderer, _super);\n    function ButtonGroupControlRenderer() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    ButtonGroupControlRenderer = __decorate([\n        OptionsControl({\n            type: 'button-group-select',\n            sizeMutable: false,\n            strictMode: false\n        })\n    ], ButtonGroupControlRenderer);\n    return ButtonGroupControlRenderer;\n}(ButtonGroupControl));\n\nexport { ButtonGroupControlRenderer, ButtonGroupControl as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAOA,mBAAkB;AAIlB,IAAI;AAAA;AAAA,EAAoC,SAAU,QAAQ;AACtD,cAAUA,qBAAoB,MAAM;AACpC,aAASA,sBAAqB;AAC1B,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,oBAAmB,UAAU,WAAW,SAAU,QAAQ,MAAM,aAAa;AACzE,UAAI,IAAI;AACR,UAAI,KAAK,KAAK,OAAO,aAAa,GAAG,YAAY,WAAW,GAAG,UAAU,YAAY,GAAG,WAAW,QAAQ,GAAG,OAAO,OAAO,GAAG;AAC/H,UAAI,aAAa,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO;AACxE,UAAI,eAAe,SAAS;AACxB,iBAAS,EAAE;AAAA,MACf,WACS,eAAe,SAAS;AAC7B,YAAI,eAAe,KAAK,aAAa,KAAK,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,cAAc,QAAQ,OAAO,SAAS,KAAK,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,UAAU,IAAI,OAAO,QAAQ,OAAO,SAAS,KAAK;AAC3P,iBAAS,gBAAgB,QAAQ,gBAAgB,SAAS,cAAc,EAAE;AAAA,MAC9E;AAAA,IACJ;AACA,IAAAA,oBAAmB,UAAU,eAAe,SAAU,QAAQ;AAC1D,UAAI,KAAK,KAAK,OAAO,WAAW,GAAG,UAAU,WAAW,GAAG,UAAU,WAAW,GAAG,UAAU,eAAe,GAAG;AAC/G,eAAS,MAAM;AAAA,IACnB;AACA,IAAAA,oBAAmB,UAAU,SAAS,SAAU,SAAS,OAAO;AAC5D,UAAI,SAAS,KAAK,MAAM;AACxB,gBAAU,OAAO,SAAS,KAAK;AAAA,IACnC;AACA,IAAAA,oBAAmB,UAAU,iBAAiB,SAAU,QAAQ,MAAM;AAClE,aAAO,UACA,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,WAC/C,OAAO,KAAK,UAAU,YAAY,OAAO,KAAK,UAAU,YACvD,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,MAAM,KAAK,MAAM,CAAC,KAAK,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,UAAU,SAAS,KAAK,KAAK,IACtI,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,KAAK,KAAK,IAAI,OACjD,KAAK;AAAA,IACf;AACA,IAAAA,oBAAmB,UAAU,SAAS,SAAU,OAAO;AACnD,UAAI;AACJ,UAAI,QAAQ;AACZ,UAAI,UAAU,QAAQ;AAAE,gBAAQ,KAAK;AAAA,MAAO;AAC5C,UAAI,SAAS,MAAM,QAAQ,KAAK,MAAM,aAAa,KAAK,MAAM,YAAY,YAAY,MAAM,WAAW,QAAQ,MAAM,OAAO,WAAW,MAAM,UAAU,UAAU,MAAM,SAAS,QAAQ,MAAM,OAAO,aAAa,MAAM,YAAY,cAAc,MAAM,aAAa,eAAe,MAAM,cAAc,qBAAqB,MAAM,oBAAoB,kBAAkB,MAAM,iBAAiB,UAAU,MAAM,SAAS,OAAO,MAAM,MAAM,QAAQ,MAAM,OAAO,WAAW,MAAM,UAAU,QAAQ,MAAM,OAAO,QAAQ,MAAM,OAAO,gBAAgB,MAAM,eAAe,KAAK,MAAM;AACtjB,UAAI,OAAO,CAAC;AACZ,UAAI,WAAW,MAAM;AACrB,UAAI,iBAAiB,MAAM;AAC3B,uBAAiB,WAAW,sBAAsB,YAAY;AAC9D,6BACK,iBAAiB,sBAAsB,kBAAkB;AAC9D,UAAI,WAAW,QAAQ,QAAQ;AAC3B,eAAO,QAAQ,IAAI,SAAU,QAAQ,KAAK;AACtC,cAAI,SAAS,CAAC,CAAC,CAAC,gBAAgB,QAAQ,MAAM;AAC9C,cAAI,cAAc,MAAM,eAAe,OAAO,MAAM;AACpD,iBAAO,OAAO,UAAU,OAAO,GAAG,GAAG;AAAA,YACjC,OAAO,OAAO,cAAc,OAAO;AAAA,YACnC,MAAM,OAAO;AAAA,YACb,MAAM,OAAO,QAAQ;AAAA,YACrB,OAAO;AAAA,YACP,MAAM;AAAA,YACN;AAAA,UACJ,GAAG;AAAA,YACC;AAAA,YACA,QAAQ,SAAS,iBAAiB,OAAO,OAAO,SAAS;AAAA,YACzD,WAAW,GAAG,OAAO,WAAW,cAAc,UAAU,4BAA4B;AAAA,YACpF,UAAU,OAAO,YAAY;AAAA,YAC7B,eAAe,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,SAAS,QAAQ,OAAO,OAAO,cAAc,OAAO,KAAK,GAAG,CAAC;AAAA,YACxJ,SAAS,SAAU,GAAG;AAClB,kBAAI,UAAU;AACV;AAAA,cACJ;AACA,oBAAM,aAAa,MAAM;AACzB,gBAAE,eAAe;AAAA,YACrB;AAAA,UACJ,CAAC;AAAA,QACL,CAAC;AAAA,MACL,WACS,MAAM,QAAQ,OAAO,GAAG;AAC7B,eAAO,QAAQ,IAAI,SAAU,QAAQ,KAAK;AACtC,cAAI,cAAc,MAAM,eAAe,OAAO,MAAM;AACpD,iBAAO,OAAO,UAAU,OAAO,GAAG,GAAG,SAAS,SAAS,EAAE,MAAY,OAAc,aAAa,gBAAgB,OAAO,UAAU,SAAmB,GAAG,MAAM,GAAG,EAAE,OAAO,YAAY,CAAC,GAAG;AAAA,YACrL;AAAA,YACA,WAAW,GAAG,OAAO,WAAW,YAAY;AAAA,UAChD,CAAC;AAAA,QACL,CAAC;AAAA,MACL;AACA,aAAQ,aAAAC,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,gBAAgB,KAAK;AAAA,QAC5D,sBAAsB;AAAA,QACtB,yBAAyB;AAAA,QACzB,sBAAsB;AAAA,MAC1B,GACA,GAAG,gBAAgB,OAAO,IAAI,CAAC,IAAI,MACnC,KAAK,SAAS,EAAE,GAAG,KAAK,SAAU,OAAS,aAAAA,QAAM,cAAc,QAAQ,EAAE,WAAW,GAAG,OAAO,IAAI,yBAAyB,EAAE,GAAG,GAAG,WAAW,CAAC,CAAE;AAAA,IAC7J;AACA,IAAAD,oBAAmB,eAAe;AAAA,MAC9B,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,UAAU;AAAA,IACd;AACA,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,oBAAmB,WAAW,gBAAgB,IAAI;AACrD,eAAW;AAAA,MACP,cAAc;AAAA,MACd,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,oBAAmB,WAAW,UAAU,IAAI;AAC/C,WAAOA;AAAA,EACX,EAAE,aAAAC,QAAM,SAAS;AAAA;AACjB,IAAI;AAAA;AAAA,EAA4C,SAAU,QAAQ;AAC9D,cAAUC,6BAA4B,MAAM;AAC5C,aAASA,8BAA6B;AAClC,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,8BAA6B,WAAW;AAAA,MACpC,eAAe;AAAA,QACX,MAAM;AAAA,QACN,aAAa;AAAA,QACb,YAAY;AAAA,MAChB,CAAC;AAAA,IACL,GAAGA,2BAA0B;AAC7B,WAAOA;AAAA,EACX,EAAE,kBAAkB;AAAA;", "names": ["ButtonGroupControl", "React", "ButtonGroupControlRenderer"]}