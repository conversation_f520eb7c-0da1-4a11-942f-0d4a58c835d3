{"version": 3, "sources": ["../../amis/esm/renderers/BarCode.js"], "sourcesContent": ["/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __decorate } from 'tslib';\nimport React, { Suspense } from 'react';\nimport { getPropValue, Renderer } from 'amis-core';\n\nvar BarCode = React.lazy(function () { return import('amis-ui/lib/components/BarCode'); });\nvar BarCodeField = /** @class */ (function (_super) {\n    __extends(BarCodeField, _super);\n    function BarCodeField() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    BarCodeField.prototype.render = function () {\n        var _a = this.props, className = _a.className, style = _a.style, width = _a.width, height = _a.height, cx = _a.classnames, options = _a.options;\n        var value = getPropValue(this.props);\n        return (React.createElement(Suspense, { fallback: React.createElement(\"div\", null, \"...\") },\n            React.createElement(\"div\", { \"data-testid\": \"barcode\", className: cx('BarCode', className), style: style },\n                React.createElement(BarCode, { value: value, options: options }))));\n    };\n    return BarCodeField;\n}(React.Component));\nvar BarCodeFieldRenderer = /** @class */ (function (_super) {\n    __extends(BarCodeFieldRenderer, _super);\n    function BarCodeFieldRenderer() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    BarCodeFieldRenderer = __decorate([\n        Renderer({\n            type: 'barcode'\n        })\n    ], BarCodeFieldRenderer);\n    return BarCodeFieldRenderer;\n}(BarCodeField));\n\nexport { BarCodeField, BarCodeFieldRenderer };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAOA,mBAAgC;AAGhC,IAAI,UAAU,aAAAA,QAAM,KAAK,WAAY;AAAE,SAAO,OAAO,uBAAgC;AAAG,CAAC;AACzF,IAAI;AAAA;AAAA,EAA8B,SAAU,QAAQ;AAChD,cAAUC,eAAc,MAAM;AAC9B,aAASA,gBAAe;AACpB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,cAAa,UAAU,SAAS,WAAY;AACxC,UAAI,KAAK,KAAK,OAAO,YAAY,GAAG,WAAW,QAAQ,GAAG,OAAO,QAAQ,GAAG,OAAO,SAAS,GAAG,QAAQ,KAAK,GAAG,YAAY,UAAU,GAAG;AACxI,UAAI,QAAQ,aAAa,KAAK,KAAK;AACnC,aAAQ,aAAAD,QAAM;AAAA,QAAc;AAAA,QAAU,EAAE,UAAU,aAAAA,QAAM,cAAc,OAAO,MAAM,KAAK,EAAE;AAAA,QACtF,aAAAA,QAAM;AAAA,UAAc;AAAA,UAAO,EAAE,eAAe,WAAW,WAAW,GAAG,WAAW,SAAS,GAAG,MAAa;AAAA,UACrG,aAAAA,QAAM,cAAc,SAAS,EAAE,OAAc,QAAiB,CAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAC7E;AACA,WAAOC;AAAA,EACX,EAAE,aAAAD,QAAM,SAAS;AAAA;AACjB,IAAI;AAAA;AAAA,EAAsC,SAAU,QAAQ;AACxD,cAAUE,uBAAsB,MAAM;AACtC,aAASA,wBAAuB;AAC5B,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,wBAAuB,WAAW;AAAA,MAC9B,SAAS;AAAA,QACL,MAAM;AAAA,MACV,CAAC;AAAA,IACL,GAAGA,qBAAoB;AACvB,WAAOA;AAAA,EACX,EAAE,YAAY;AAAA;", "names": ["React", "BarCodeField", "BarCodeField<PERSON><PERSON><PERSON>"]}