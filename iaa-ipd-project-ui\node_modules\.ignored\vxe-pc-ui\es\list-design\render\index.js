import { renderer } from '@vxe-ui/core';
// import { handleGetListDesignActionButtonName } from './util'
/**
 * 列表设计器 - 渲染器
 */
renderer.mixin({
// ActionButtonUpdate: {
//   createListDesignSettingActionButtonConfig () {
//     return {
//       name: handleGetListDesignActionButtonName,
//       icon: 'vxe-icon-edit',
//       status: 'primary'
//     }
//   }
// },
// ActionButtonDelete: {
//   createListDesignSettingActionButtonConfig () {
//     return {
//       name: handleGetListDesignActionButtonName,
//       icon: 'vxe-icon-delete',
//       status: 'error'
//     }
//   }
// }
});
