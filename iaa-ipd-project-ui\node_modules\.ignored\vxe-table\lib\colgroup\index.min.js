Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=exports.VxeColgroup=exports.Colgroup=void 0;var _ui=require("../ui"),_group=_interopRequireDefault(require("../table/src/group"));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let VxeColgroup=exports.VxeColgroup=Object.assign({},_group.default,{install(e){e.component(_group.default.name,_group.default),e.component("VxeTableColgroup",_group.default)}}),Colgroup=(_ui.VxeUI.dynamicApp&&(_ui.VxeUI.dynamicApp.component(_group.default.name,_group.default),_ui.VxeUI.dynamicApp.component("VxeTableColgroup",_group.default)),_ui.VxeUI.component(_group.default),exports.Colgroup=VxeColgroup);var _default=exports.default=VxeColgroup;