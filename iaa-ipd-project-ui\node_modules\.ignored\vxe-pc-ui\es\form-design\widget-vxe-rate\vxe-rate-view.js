import { defineComponent, h, inject } from 'vue';
import { useWidgetName } from '../src/use';
import VxeFormItemComponent from '../../form/src/form-item';
import VxeRateComponent from '../../rate/src/rate';
export const WidgetVxeRateViewComponent = defineComponent({
    props: {
        renderOpts: {
            type: Object,
            default: () => ({})
        },
        renderParams: {
            type: Object,
            default: () => ({})
        }
    },
    emits: [],
    setup(props) {
        const $xeFormView = inject('$xeFormView', null);
        const { computeKebabCaseName } = useWidgetName(props);
        const changeEvent = () => {
            const { renderParams } = props;
            const { widget } = renderParams;
            if ($xeFormView) {
                const itemValue = $xeFormView ? $xeFormView.getItemValue(widget) : null;
                $xeFormView.updateWidgetStatus(widget, itemValue);
            }
        };
        return () => {
            const { renderParams } = props;
            const { widget } = renderParams;
            const kebabCaseName = computeKebabCaseName.value;
            return h(VxeFormItemComponent, {
                class: ['vxe-form-design--widget-render-form-item', `widget-${kebabCaseName}`],
                field: widget.field,
                title: widget.title,
                itemRender: {}
            }, {
                default() {
                    return h(VxeRateComponent, {
                        modelValue: $xeFormView ? $xeFormView.getItemValue(widget) : null,
                        onChange: changeEvent,
                        'onUpdate:modelValue'(val) {
                            if ($xeFormView) {
                                $xeFormView.setItemValue(widget, val);
                            }
                        }
                    });
                }
            });
        };
    }
});
