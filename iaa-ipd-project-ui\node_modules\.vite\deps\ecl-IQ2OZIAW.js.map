{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/ecl/ecl.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    comments: {\n        lineComment: '//',\n        blockComment: ['/*', '*/']\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: \"'\", close: \"'\", notIn: ['string', 'comment'] },\n        { open: '\"', close: '\"', notIn: ['string', 'comment'] }\n    ],\n    surroundingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '<', close: '>' },\n        { open: \"'\", close: \"'\" },\n        { open: '\"', close: '\"' }\n    ]\n};\nexport var language = {\n    defaultToken: '',\n    tokenPostfix: '.ecl',\n    ignoreCase: true,\n    brackets: [\n        { open: '{', close: '}', token: 'delimiter.curly' },\n        { open: '[', close: ']', token: 'delimiter.square' },\n        { open: '(', close: ')', token: 'delimiter.parenthesis' },\n        { open: '<', close: '>', token: 'delimiter.angle' }\n    ],\n    pounds: [\n        'append',\n        'break',\n        'declare',\n        'demangle',\n        'end',\n        'for',\n        'getdatatype',\n        'if',\n        'inmodule',\n        'loop',\n        'mangle',\n        'onwarning',\n        'option',\n        'set',\n        'stored',\n        'uniquename'\n    ].join('|'),\n    keywords: [\n        '__compressed__',\n        'after',\n        'all',\n        'and',\n        'any',\n        'as',\n        'atmost',\n        'before',\n        'beginc',\n        'best',\n        'between',\n        'case',\n        'cluster',\n        'compressed',\n        'compression',\n        'const',\n        'counter',\n        'csv',\n        'default',\n        'descend',\n        'embed',\n        'encoding',\n        'encrypt',\n        'end',\n        'endc',\n        'endembed',\n        'endmacro',\n        'enum',\n        'escape',\n        'except',\n        'exclusive',\n        'expire',\n        'export',\n        'extend',\n        'fail',\n        'few',\n        'fileposition',\n        'first',\n        'flat',\n        'forward',\n        'from',\n        'full',\n        'function',\n        'functionmacro',\n        'group',\n        'grouped',\n        'heading',\n        'hole',\n        'ifblock',\n        'import',\n        'in',\n        'inner',\n        'interface',\n        'internal',\n        'joined',\n        'keep',\n        'keyed',\n        'last',\n        'left',\n        'limit',\n        'linkcounted',\n        'literal',\n        'little_endian',\n        'load',\n        'local',\n        'locale',\n        'lookup',\n        'lzw',\n        'macro',\n        'many',\n        'maxcount',\n        'maxlength',\n        'min skew',\n        'module',\n        'mofn',\n        'multiple',\n        'named',\n        'namespace',\n        'nocase',\n        'noroot',\n        'noscan',\n        'nosort',\n        'not',\n        'noxpath',\n        'of',\n        'onfail',\n        'only',\n        'opt',\n        'or',\n        'outer',\n        'overwrite',\n        'packed',\n        'partition',\n        'penalty',\n        'physicallength',\n        'pipe',\n        'prefetch',\n        'quote',\n        'record',\n        'repeat',\n        'retry',\n        'return',\n        'right',\n        'right1',\n        'right2',\n        'rows',\n        'rowset',\n        'scan',\n        'scope',\n        'self',\n        'separator',\n        'service',\n        'shared',\n        'skew',\n        'skip',\n        'smart',\n        'soapaction',\n        'sql',\n        'stable',\n        'store',\n        'terminator',\n        'thor',\n        'threshold',\n        'timelimit',\n        'timeout',\n        'token',\n        'transform',\n        'trim',\n        'type',\n        'unicodeorder',\n        'unordered',\n        'unsorted',\n        'unstable',\n        'update',\n        'use',\n        'validate',\n        'virtual',\n        'whole',\n        'width',\n        'wild',\n        'within',\n        'wnotrim',\n        'xml',\n        'xpath'\n    ],\n    functions: [\n        'abs',\n        'acos',\n        'aggregate',\n        'allnodes',\n        'apply',\n        'ascii',\n        'asin',\n        'assert',\n        'asstring',\n        'atan',\n        'atan2',\n        'ave',\n        'build',\n        'buildindex',\n        'case',\n        'catch',\n        'choose',\n        'choosen',\n        'choosesets',\n        'clustersize',\n        'combine',\n        'correlation',\n        'cos',\n        'cosh',\n        'count',\n        'covariance',\n        'cron',\n        'dataset',\n        'dedup',\n        'define',\n        'denormalize',\n        'dictionary',\n        'distribute',\n        'distributed',\n        'distribution',\n        'ebcdic',\n        'enth',\n        'error',\n        'evaluate',\n        'event',\n        'eventextra',\n        'eventname',\n        'exists',\n        'exp',\n        'fail',\n        'failcode',\n        'failmessage',\n        'fetch',\n        'fromunicode',\n        'fromxml',\n        'getenv',\n        'getisvalid',\n        'global',\n        'graph',\n        'group',\n        'hash',\n        'hash32',\n        'hash64',\n        'hashcrc',\n        'hashmd5',\n        'having',\n        'httpcall',\n        'httpheader',\n        'if',\n        'iff',\n        'index',\n        'intformat',\n        'isvalid',\n        'iterate',\n        'join',\n        'keydiff',\n        'keypatch',\n        'keyunicode',\n        'length',\n        'library',\n        'limit',\n        'ln',\n        'loadxml',\n        'local',\n        'log',\n        'loop',\n        'map',\n        'matched',\n        'matchlength',\n        'matchposition',\n        'matchtext',\n        'matchunicode',\n        'max',\n        'merge',\n        'mergejoin',\n        'min',\n        'nofold',\n        'nolocal',\n        'nonempty',\n        'normalize',\n        'nothor',\n        'notify',\n        'output',\n        'parallel',\n        'parse',\n        'pipe',\n        'power',\n        'preload',\n        'process',\n        'project',\n        'pull',\n        'random',\n        'range',\n        'rank',\n        'ranked',\n        'realformat',\n        'recordof',\n        'regexfind',\n        'regexreplace',\n        'regroup',\n        'rejected',\n        'rollup',\n        'round',\n        'roundup',\n        'row',\n        'rowdiff',\n        'sample',\n        'sequential',\n        'set',\n        'sin',\n        'sinh',\n        'sizeof',\n        'soapcall',\n        'sort',\n        'sorted',\n        'sqrt',\n        'stepped',\n        'stored',\n        'sum',\n        'table',\n        'tan',\n        'tanh',\n        'thisnode',\n        'topn',\n        'tounicode',\n        'toxml',\n        'transfer',\n        'transform',\n        'trim',\n        'truncate',\n        'typeof',\n        'ungroup',\n        'unicodeorder',\n        'variance',\n        'wait',\n        'which',\n        'workunit',\n        'xmldecode',\n        'xmlencode',\n        'xmltext',\n        'xmlunicode'\n    ],\n    typesint: ['integer', 'unsigned'].join('|'),\n    typesnum: ['data', 'qstring', 'string', 'unicode', 'utf8', 'varstring', 'varunicode'],\n    typesone: [\n        'ascii',\n        'big_endian',\n        'boolean',\n        'data',\n        'decimal',\n        'ebcdic',\n        'grouped',\n        'integer',\n        'linkcounted',\n        'pattern',\n        'qstring',\n        'real',\n        'record',\n        'rule',\n        'set of',\n        'streamed',\n        'string',\n        'token',\n        'udecimal',\n        'unicode',\n        'unsigned',\n        'utf8',\n        'varstring',\n        'varunicode'\n    ].join('|'),\n    operators: ['+', '-', '/', ':=', '<', '<>', '=', '>', '\\\\', 'and', 'in', 'not', 'or'],\n    symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n    // escape sequences\n    escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n    // The main tokenizer for our languages\n    tokenizer: {\n        root: [\n            [/@typesint[4|8]/, 'type'],\n            [/#(@pounds)/, 'type'],\n            [/@typesone/, 'type'],\n            [\n                /[a-zA-Z_$][\\w-$]*/,\n                {\n                    cases: {\n                        '@functions': 'keyword.function',\n                        '@keywords': 'keyword',\n                        '@operators': 'operator'\n                    }\n                }\n            ],\n            // whitespace\n            { include: '@whitespace' },\n            [/[{}()\\[\\]]/, '@brackets'],\n            [/[<>](?!@symbols)/, '@brackets'],\n            [\n                /@symbols/,\n                {\n                    cases: {\n                        '@operators': 'delimiter',\n                        '@default': ''\n                    }\n                }\n            ],\n            // numbers\n            [/[0-9_]*\\.[0-9_]+([eE][\\-+]?\\d+)?/, 'number.float'],\n            [/0[xX][0-9a-fA-F_]+/, 'number.hex'],\n            [/0[bB][01]+/, 'number.hex'],\n            [/[0-9_]+/, 'number'],\n            // delimiter: after number because of .\\d floats\n            [/[;,.]/, 'delimiter'],\n            // strings\n            [/\"([^\"\\\\]|\\\\.)*$/, 'string.invalid'],\n            [/\"/, 'string', '@string'],\n            // characters\n            [/'[^\\\\']'/, 'string'],\n            [/(')(@escapes)(')/, ['string', 'string.escape', 'string']],\n            [/'/, 'string.invalid']\n        ],\n        whitespace: [\n            [/[ \\t\\v\\f\\r\\n]+/, ''],\n            [/\\/\\*/, 'comment', '@comment'],\n            [/\\/\\/.*$/, 'comment']\n        ],\n        comment: [\n            [/[^\\/*]+/, 'comment'],\n            [/\\*\\//, 'comment', '@pop'],\n            [/[\\/*]/, 'comment']\n        ],\n        string: [\n            [/[^\\\\']+/, 'string'],\n            [/@escapes/, 'string.escape'],\n            [/\\\\./, 'string.escape.invalid'],\n            [/'/, 'string', '@pop']\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC7B;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EAC1D;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,UAAU;AAAA,IACN,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,EACtD;AAAA,EACA,QAAQ;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,EAAE,KAAK,GAAG;AAAA,EACV,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,WAAW;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,UAAU,CAAC,WAAW,UAAU,EAAE,KAAK,GAAG;AAAA,EAC1C,UAAU,CAAC,QAAQ,WAAW,UAAU,WAAW,QAAQ,aAAa,YAAY;AAAA,EACpF,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,EAAE,KAAK,GAAG;AAAA,EACV,WAAW,CAAC,KAAK,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK,MAAM,OAAO,MAAM,OAAO,IAAI;AAAA,EACpF,SAAS;AAAA;AAAA,EAET,SAAS;AAAA;AAAA,EAET,WAAW;AAAA,IACP,MAAM;AAAA,MACF,CAAC,kBAAkB,MAAM;AAAA,MACzB,CAAC,cAAc,MAAM;AAAA,MACrB,CAAC,aAAa,MAAM;AAAA,MACpB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,cAAc;AAAA,YACd,aAAa;AAAA,YACb,cAAc;AAAA,UAClB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,oBAAoB,WAAW;AAAA,MAChC;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,cAAc;AAAA,YACd,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,CAAC,oCAAoC,cAAc;AAAA,MACnD,CAAC,sBAAsB,YAAY;AAAA,MACnC,CAAC,cAAc,YAAY;AAAA,MAC3B,CAAC,WAAW,QAAQ;AAAA;AAAA,MAEpB,CAAC,SAAS,WAAW;AAAA;AAAA,MAErB,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,KAAK,UAAU,SAAS;AAAA;AAAA,MAEzB,CAAC,YAAY,QAAQ;AAAA,MACrB,CAAC,oBAAoB,CAAC,UAAU,iBAAiB,QAAQ,CAAC;AAAA,MAC1D,CAAC,KAAK,gBAAgB;AAAA,IAC1B;AAAA,IACA,YAAY;AAAA,MACR,CAAC,kBAAkB,EAAE;AAAA,MACrB,CAAC,QAAQ,WAAW,UAAU;AAAA,MAC9B,CAAC,WAAW,SAAS;AAAA,IACzB;AAAA,IACA,SAAS;AAAA,MACL,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,SAAS,SAAS;AAAA,IACvB;AAAA,IACA,QAAQ;AAAA,MACJ,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,UAAU,MAAM;AAAA,IAC1B;AAAA,EACJ;AACJ;", "names": []}