Object.defineProperty(exports,"__esModule",{value:!0});var _exportNames={},_colgroup=(exports.default=void 0,_interopRequireWildcard(require("../colgroup")));function _getRequireWildcardCache(e){var r,t;return"function"!=typeof WeakMap?null:(r=new WeakMap,t=new WeakMap,(_getRequireWildcardCache=function(e){return e?t:r})(e))}function _interopRequireWildcard(e,r){if(!r&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};r=_getRequireWildcardCache(r);if(r&&r.has(e))return r.get(e);var t,o,u={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(t in e)"default"!==t&&{}.hasOwnProperty.call(e,t)&&((o=n?Object.getOwnPropertyDescriptor(e,t):null)&&(o.get||o.set)?Object.defineProperty(u,t,o):u[t]=e[t]);return u.default=e,r&&r.set(e,u),u}Object.keys(_colgroup).forEach(function(e){"default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(_exportNames,e)||e in exports&&exports[e]===_colgroup[e]||Object.defineProperty(exports,e,{enumerable:!0,get:function(){return _colgroup[e]}})});var _default=exports.default=_colgroup.default;