@charset "UTF-8";
[class*=vxe-icon--] {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  direction: ltr;
  font-family: Verdana, Arial, Tahoma;
  font-weight: normal;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
[class*=vxe-icon--].rotate45 {
  transform: rotate(45deg);
}
[class*=vxe-icon--].rotate90 {
  transform: rotate(90deg);
}
[class*=vxe-icon--].rotate180 {
  transform: rotate(180deg);
}

.vxe-icon--search, .vxe-icon--print, .vxe-icon--dot, .vxe-icon--calendar, .vxe-icon--eye,
.vxe-icon--eye-slash, .vxe-icon--upload,
.vxe-icon--download, .vxe-icon--error, .vxe-icon--remove, .vxe-icon--circle-plus, .vxe-icon--success, .vxe-icon--warning, .vxe-icon--info, .vxe-icon--question, .vxe-icon--refresh, .vxe-icon--minus, .vxe-icon--close, .vxe-icon--check, .vxe-icon--plus, .vxe-icon--more, .vxe-icon--edit-outline, .vxe-icon--funnel, .vxe-icon--d-arrow-left, .vxe-icon--d-arrow-right, .vxe-icon--arrow-top, .vxe-icon--arrow-right, .vxe-icon--arrow-left, .vxe-icon--arrow-bottom, .vxe-icon--caret-right, .vxe-icon--caret-left, .vxe-icon--caret-bottom, .vxe-icon--caret-top, .vxe-icon--menu, .vxe-icon--zoomout, .vxe-icon--zoomin, .vxe-icon--square {
  width: 1em;
  height: 1em;
  line-height: 1em;
}

.vxe-icon--search:after, .vxe-icon--search:before, .vxe-icon--print:after, .vxe-icon--print:before, .vxe-icon--dot:before, .vxe-icon--calendar:after, .vxe-icon--calendar:before, .vxe-icon--eye-slash:after, .vxe-icon--eye:before,
.vxe-icon--eye-slash:before, .vxe-icon--upload:after,
.vxe-icon--download:after, .vxe-icon--upload:before,
.vxe-icon--download:before, .vxe-icon--error:after, .vxe-icon--remove:after, .vxe-icon--circle-plus:after, .vxe-icon--success:after, .vxe-icon--warning:after, .vxe-icon--info:after, .vxe-icon--question:after, .vxe-icon--refresh:before, .vxe-icon--refresh:after, .vxe-icon--minus:before, .vxe-icon--close:before, .vxe-icon--check:before, .vxe-icon--plus:before, .vxe-icon--more:before, .vxe-icon--edit-outline:after, .vxe-icon--edit-outline:before, .vxe-icon--funnel:after, .vxe-icon--funnel:before, .vxe-icon--d-arrow-left:before, .vxe-icon--d-arrow-right:before, .vxe-icon--d-arrow-left:after, .vxe-icon--d-arrow-right:after, .vxe-icon--arrow-top:before, .vxe-icon--arrow-right:before, .vxe-icon--arrow-left:before, .vxe-icon--arrow-bottom:before, .vxe-icon--caret-right:before, .vxe-icon--caret-left:before, .vxe-icon--caret-bottom:before, .vxe-icon--caret-top:before, .vxe-icon--zoomout:after, .vxe-icon--zoomout:before, .vxe-icon--zoomin:before, .vxe-icon--zoomin:after, .vxe-icon--square:before {
  content: "";
  position: absolute;
}

.vxe-icon--square:before {
  left: 0.05em;
  top: 0.05em;
  width: 0.9em;
  height: 0.9em;
  border-width: 0.1em;
  border-style: solid;
  border-color: inherit;
}

.vxe-icon--zoomin {
  border-width: 0.1em;
  border-style: solid;
  border-color: inherit;
  background-color: var(--vxe-ui-layout-background-color);
}
.vxe-icon--zoomin:before, .vxe-icon--zoomin:after {
  background-color: inherit;
}
.vxe-icon--zoomin:before {
  left: -0.1em;
  top: 0.2em;
  width: 1.1em;
  height: 0.4em;
}
.vxe-icon--zoomin:after {
  top: -0.1em;
  left: 0.2em;
  width: 0.4em;
  height: 1.1em;
}

.vxe-icon--zoomout {
  position: relative;
}
.vxe-icon--zoomout:before {
  right: 0;
  top: 0;
  width: 0.7em;
  height: 0.7em;
  border-width: 0.1em;
  border-style: solid;
  border-color: inherit;
}
.vxe-icon--zoomout:after {
  left: 0.1em;
  bottom: 0.1em;
  width: 0.7em;
  height: 0.7em;
  border-width: 0.1em;
  border-style: solid;
  border-color: inherit;
  background-color: var(--vxe-ui-layout-background-color);
}

.vxe-icon--menu:before {
  content: "";
  display: inline-block;
  width: 0.22em;
  height: 0.22em;
  box-shadow: 0 -0.36em 0, -0.36em -0.36em 0, 0.36em -0.36em 0, 0 0 0 1em inset, -0.36em 0 0, 0.36em 0 0, 0 0.36em 0, -0.36em 0.36em 0, 0.36em 0.36em 0;
  margin: 0.26em;
}

.vxe-icon--caret-right:before, .vxe-icon--caret-left:before, .vxe-icon--caret-bottom:before, .vxe-icon--caret-top:before {
  border-width: 0.4em;
  border-style: solid;
  border-color: transparent;
}

.vxe-icon--caret-top:before {
  left: 0.1em;
  bottom: 0.3em;
  border-bottom-color: inherit;
}

.vxe-icon--caret-bottom:before {
  left: 0.1em;
  top: 0.3em;
  border-top-color: inherit;
}

.vxe-icon--caret-left:before {
  right: 0.3em;
  bottom: 0.1em;
  border-right-color: inherit;
}

.vxe-icon--caret-right:before {
  left: 0.3em;
  bottom: 0.1em;
  border-left-color: inherit;
}

.vxe-icon--arrow-top:before, .vxe-icon--arrow-right:before, .vxe-icon--arrow-left:before, .vxe-icon--arrow-bottom:before {
  top: 0.4em;
  left: 0.14em;
  width: 0.7em;
  height: 0.7em;
  border-width: 0.15em;
  border-style: solid;
  border-top-color: inherit;
  border-right-color: inherit;
  border-bottom-color: transparent;
  border-left-color: transparent;
  border-radius: 0.15em;
  transform: rotate(-45deg);
}

.vxe-icon--arrow-bottom:before {
  top: 0;
  left: 0.14em;
  transform: rotate(135deg);
}

.vxe-icon--arrow-left:before {
  top: 0.18em;
  left: 0.35em;
  transform: rotate(-135deg);
}

.vxe-icon--arrow-right:before {
  top: 0.18em;
  left: 0;
  transform: rotate(45deg);
}

.vxe-icon--d-arrow-left:before, .vxe-icon--d-arrow-right:before {
  left: 0.15em;
}
.vxe-icon--d-arrow-left:after, .vxe-icon--d-arrow-right:after {
  left: 0.58em;
}
.vxe-icon--d-arrow-left:before, .vxe-icon--d-arrow-right:before, .vxe-icon--d-arrow-left:after, .vxe-icon--d-arrow-right:after {
  top: 0.18em;
  width: 0.7em;
  height: 0.7em;
  border-width: 0.15em;
  border-style: solid;
  border-top-color: inherit;
  border-right-color: transparent;
  border-bottom-color: transparent;
  border-left-color: inherit;
  border-radius: 0.15em;
  transform: rotate(-45deg);
}

.vxe-icon--d-arrow-right:before, .vxe-icon--d-arrow-right:after {
  transform: rotate(135deg);
}
.vxe-icon--d-arrow-right:before {
  left: -0.25em;
}
.vxe-icon--d-arrow-right:after {
  left: 0.18em;
}

.vxe-icon--funnel:before {
  top: 0.05em;
  left: 0;
  border-width: 0.5em;
  border-style: solid;
  border-top-color: inherit;
  border-right-color: transparent;
  border-bottom-color: transparent;
  border-left-color: transparent;
}
.vxe-icon--funnel:after {
  left: 0.41em;
  top: 0.4em;
  width: 0;
  height: 0.5em;
  border-width: 0 0.2em 0 0;
  border-style: solid;
  border-right-color: inherit;
}

.vxe-icon--edit-outline:before {
  height: 0.84em;
  width: 0.86em;
  top: 0.1em;
  left: 0.02em;
  border-radius: 0.2em;
  border-width: 0.1em;
  border-style: solid;
  border-color: inherit;
}
.vxe-icon--edit-outline:after {
  left: 0.6em;
  bottom: 0.2em;
  width: 0;
  height: 0.8em;
  border-radius: 0 0 80% 80%;
  border-width: 0 0 0 0.22em;
  border-style: solid;
  border-color: inherit;
  transform: rotate(45deg);
}

.vxe-icon--more:before {
  content: "...";
  top: 0;
  left: 0.1em;
  line-height: 0.5em;
  font-weight: 700;
}

.vxe-icon--plus:before {
  content: "+";
  left: -0.12em;
  bottom: -0.1em;
  line-height: 1em;
  font-size: 1.6em;
}

.vxe-icon--check:before {
  left: 0.25em;
  bottom: 0.2em;
  width: 0.5em;
  height: 0.9em;
  border-width: 0.15em;
  border-style: solid;
  border-top-color: transparent;
  border-right-color: inherit;
  border-bottom-color: inherit;
  border-radius: 0.15em;
  border-left-color: transparent;
  transform: rotate(45deg);
}

.vxe-icon--close:before {
  content: "+";
  left: -0.1em;
  bottom: -0.16em;
  line-height: 1em;
  font-size: 1.8em;
  transform: rotate(45deg);
}

.vxe-icon--minus:before {
  content: "─";
  left: 0;
  bottom: 0;
  width: 100%;
  text-align: center;
  line-height: 0.9em;
  font-size: 1.2em;
}

.vxe-icon--refresh {
  border-width: 0.1em;
  border-style: solid;
  border-radius: 50%;
  border-right-color: transparent !important;
  border-left-color: transparent !important;
}
.vxe-icon--refresh:before {
  left: 50%;
  top: 0;
  transform: translateX(50%) rotate(-45deg);
}
.vxe-icon--refresh:after {
  right: 50%;
  bottom: 0;
  transform: translateX(-50%) rotate(135deg);
}
.vxe-icon--refresh:before, .vxe-icon--refresh:after {
  width: 0;
  height: 0;
  border-width: 0.25em;
  border-style: solid;
  border-right-color: transparent;
  border-bottom-color: transparent;
  border-left-color: transparent;
}
.vxe-icon--refresh.roll {
  animation: rollCircle 1s infinite linear;
}

@keyframes rollCircle {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.vxe-icon--error:before, .vxe-icon--remove:before, .vxe-icon--circle-plus:before, .vxe-icon--success:before, .vxe-icon--warning:before, .vxe-icon--info:before, .vxe-icon--question:before {
  content: "";
  border-radius: 50%;
  border-width: 0.5em;
  border-style: solid;
  border-color: inherit;
  position: absolute;
  top: 0;
  left: 0;
  transform: scale(0.95);
}

.vxe-icon--warning:after, .vxe-icon--info:after, .vxe-icon--question:after {
  left: 0;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: var(--vxe-ui-layout-background-color);
  transform: rotate(-10deg) scale(0.75);
}

.vxe-icon--question:after {
  content: "?";
}

.vxe-icon--info:after {
  content: "¡";
}

.vxe-icon--warning:after {
  content: "!";
}

.vxe-icon--success:after {
  content: "✓";
  left: 0.25em;
  bottom: 0;
  color: var(--vxe-ui-layout-background-color);
  font-size: 0.65em;
}

.vxe-icon--circle-plus:after {
  content: "+";
  left: 0;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: var(--vxe-ui-layout-background-color);
  line-height: 1.4em;
  font-size: 0.8em;
}

.vxe-icon--remove:after {
  content: "─";
  left: 0;
  bottom: 0;
  width: 100%;
  text-align: center;
  line-height: 1.5em;
  color: var(--vxe-ui-layout-background-color);
  font-size: 0.7em;
}

.vxe-icon--error:after {
  content: "×";
  left: 0;
  bottom: 0;
  width: 100%;
  line-height: 1.4em;
  text-align: center;
  color: var(--vxe-ui-layout-background-color);
  font-size: 0.8em;
}

.vxe-icon--upload,
.vxe-icon--download {
  overflow: hidden;
}
.vxe-icon--upload:before,
.vxe-icon--download:before {
  left: 0;
  width: 1em;
  border-width: 0;
  border-style: solid;
  border-color: inherit;
}
.vxe-icon--upload:after,
.vxe-icon--download:after {
  width: 100%;
  text-align: center;
  font-size: 2em;
}

.vxe-icon--upload:before {
  top: 0.1em;
  border-top-width: 0.1em;
}
.vxe-icon--upload:after {
  content: "↑";
  left: 0;
  top: 0.15em;
}

.vxe-icon--download:before {
  bottom: 0.05em;
  border-bottom-width: 0.1em;
}
.vxe-icon--download:after {
  content: "↑";
  left: 0;
  bottom: 0.15em;
  transform: rotate(180deg);
}

.vxe-icon--eye:before,
.vxe-icon--eye-slash:before {
  content: "●";
  top: 0.16em;
  left: 0;
  width: 1em;
  height: 0.68em;
  line-height: 0.25em;
  border-radius: 50%;
  border-width: 0.1em;
  border-style: solid;
  border-color: inherit;
  text-align: center;
}

.vxe-icon--eye-slash:after {
  top: -0.1em;
  left: 0.45em;
  width: 0;
  height: 1.2em;
  border-width: 0;
  border-style: solid;
  border-color: inherit;
  border-left-width: 0.1em;
  transform: rotate(45deg);
}

.vxe-icon--calendar:before {
  top: 0.15em;
  left: 0;
  width: 1em;
  height: 0.8em;
  border-width: 0.2em 0.1em 0.1em 0.1em;
  border-radius: 0.1em 0.1em 0 0;
  border-style: solid;
  border-color: inherit;
}
.vxe-icon--calendar:after {
  left: 0.2em;
  top: 0;
  width: 0.6em;
  height: 0.3em;
  border-width: 0 0.1em;
  border-style: solid;
  border-color: inherit;
}

.vxe-icon--dot:before {
  top: 0.25em;
  left: 0.25em;
  border-radius: 50%;
  border-width: 0.25em;
  border-style: solid;
  border-color: inherit;
}

.vxe-icon--print {
  box-shadow: inset 0 0 0 0.1em;
  border-width: 0.2em 0;
  border-style: solid;
  border-color: transparent !important;
  border-radius: 0.3em 0.3em 0 0;
}
.vxe-icon--print:before {
  width: 0.6em;
  height: 0.3em;
  top: -0.2em;
  left: 0.2em;
  box-shadow: inset 0 0 0 0.1em;
}
.vxe-icon--print:after {
  width: 0.6em;
  height: 0.6em;
  left: 0.2em;
  bottom: -0.2em;
  box-shadow: inset 0 0 0 0.1em;
  background-color: var(--vxe-ui-layout-background-color);
}

.vxe-icon--search:before {
  top: 0;
  left: 0;
  width: 0.8em;
  height: 0.8em;
  border-width: 0.15em;
  border-style: solid;
  border-color: inherit;
  border-radius: 50%;
}
.vxe-icon--search:after {
  top: 0.75em;
  left: 0.6em;
  width: 0.35em;
  height: 0;
  border-width: 0.15em 0 0 0;
  border-style: solid;
  border-color: inherit;
  transform: rotate(45deg);
}

.vxe-icon-warnion-circle-fill:before {
  content: "\e848";
}

@font-face {
  font-family: "vxetableiconfont";
  src: url("data:application/x-font-woff2;charset=utf-8;base64,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") format("woff2");
}
@keyframes rollCircle {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
[class*=vxe-table-icon-] {
  font-family: "vxetableiconfont" !important;
  font-style: normal;
  font-weight: 400;
  font-size: 1.1em;
  line-height: 1em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
[class*=vxe-table-icon-].animat, [class*=vxe-table-icon-].roll {
  display: inline-block;
}
[class*=vxe-table-icon-].animat {
  transition: transform 0.25s ease-in-out;
}
[class*=vxe-table-icon-].rotate45 {
  transform: rotate(45deg);
}
[class*=vxe-table-icon-].rotate90 {
  transform: rotate(90deg);
}
[class*=vxe-table-icon-].rotate180 {
  transform: rotate(180deg);
}
[class*=vxe-table-icon-].roll {
  animation: rollCircle 1s infinite linear;
}
[class*=vxe-table-icon-].theme--primary {
  color: var(--vxe-ui-font-primary-color);
}
[class*=vxe-table-icon-].theme--success {
  color: var(--vxe-ui-status-success-color);
}
[class*=vxe-table-icon-].theme--info {
  color: var(--vxe-ui-status-info-color);
}
[class*=vxe-table-icon-].theme--warning {
  color: var(--vxe-ui-status-warning-color);
}
[class*=vxe-table-icon-].theme--danger {
  color: var(--vxe-ui-status-danger-color);
}
[class*=vxe-table-icon-].theme--error {
  color: var(--vxe-ui-status-error-color);
}

.vxe-table-icon-close:before {
  content: "\e6e9";
}

.vxe-table-icon-grouping:before {
  content: "\e66c";
}

.vxe-table-icon-values:before {
  content: "\e66f";
}

.vxe-table-icon-add-sub:before {
  content: "\e6bc";
}

.vxe-table-icon-swap:before {
  content: "\e7f3";
}

.vxe-table-icon-sort:before {
  content: "\e93e";
}

.vxe-table-icon-no-drop:before {
  content: "\e658";
}

.vxe-table-icon-edit:before {
  content: "\e66e";
}

.vxe-table-icon-question-circle-fill:before {
  content: "\e690";
}

.vxe-table-icon-radio-checked:before {
  content: "\e75b";
}

.vxe-table-icon-radio-checked-fill:before {
  content: "\e763";
}

.vxe-table-icon-print:before {
  content: "\eba0";
}

.vxe-table-icon-checkbox-checked-fill:before {
  content: "\e67d";
}

.vxe-table-icon-custom-column:before {
  content: "\e62d";
}

.vxe-table-icon-radio-unchecked:before {
  content: "\e7c9";
}

.vxe-table-icon-caret-down:before {
  content: "\e8ed";
}

.vxe-table-icon-caret-up:before {
  content: "\e8ee";
}

.vxe-table-icon-caret-right:before {
  content: "\e8ef";
}

.vxe-table-icon-caret-left:before {
  content: "\e8f0";
}

.vxe-table-icon-fullscreen:before {
  content: "\e70e";
}

.vxe-table-icon-minimize:before {
  content: "\e749";
}

.vxe-table-icon-checkbox-unchecked:before {
  content: "\e727";
}

.vxe-table-icon-funnel:before {
  content: "\e8ec";
}

.vxe-table-icon-download:before {
  content: "\e61a";
}

.vxe-table-icon-spinner:before {
  content: "\e601";
}

.vxe-table-icon-arrow-right:before {
  content: "\e743";
}

.vxe-table-icon-repeat:before {
  content: "\ea4a";
}

.vxe-table-icon-drag-handle:before {
  content: "\e64e";
}

.vxe-table-icon-checkbox-indeterminate-fill:before {
  content: "\e8c4";
}

.vxe-table-icon-upload:before {
  content: "\e683";
}

.vxe-table-icon-fixed-left-fill:before {
  content: "\e9b9";
}

.vxe-table-icon-fixed-left:before {
  content: "\e9ba";
}

.vxe-table-icon-fixed-right-fill:before {
  content: "\f290";
}

.vxe-table-icon-fixed-right:before {
  content: "\f291";
}

.vxe-table-custom--option {
  position: relative;
  display: flex;
  flex-direction: row;
}
.vxe-table-custom--option.active--drag-origin {
  opacity: 0.5;
}
.vxe-table-custom--option.active--drag-target[drag-pos=top]::after {
  display: block;
  top: -2px;
}
.vxe-table-custom--option.active--drag-target[drag-pos=bottom]::after {
  display: block;
  bottom: -2px;
}
.vxe-table-custom--option:first-child[drag-pos=top]::after {
  top: 0;
}
.vxe-table-custom--option:last-child[drag-pos=bottom]::after {
  bottom: 0;
}
.vxe-table-custom--option::after {
  display: none;
  content: "";
  position: absolute;
  left: -1px;
  width: calc(100% + 1px);
  height: 2px;
  background-color: var(--vxe-ui-font-primary-color);
  z-index: 12;
}
.vxe-table-custom--option:last-child::after {
  width: 100%;
}

.vxe-table-custom--handle-wrapper {
  display: flex;
  flex-direction: column;
}

.vxe-table-custom-wrapper {
  display: none;
  flex-direction: row;
  position: absolute;
  text-align: left;
  background-color: var(--vxe-ui-layout-background-color);
  z-index: 19;
  border: 1px solid var(--vxe-ui-table-border-color);
  border-radius: var(--vxe-ui-border-radius);
  box-shadow: var(--vxe-ui-base-popup-box-shadow);
}
.vxe-table-custom-wrapper.placement--top-left {
  top: 2px;
  left: 2px;
}
.vxe-table-custom-wrapper.placement--top-right {
  top: 2px;
  right: 2px;
}
.vxe-table-custom-wrapper.placement--bottom-left {
  bottom: 2px;
  left: 2px;
}
.vxe-table-custom-wrapper.placement--bottom-right {
  bottom: 2px;
  right: 2px;
}
.vxe-table-custom-wrapper.placement--left {
  left: 2px;
}
.vxe-table-custom-wrapper.placement--right {
  right: 2px;
}
.vxe-table-custom-wrapper.placement--left, .vxe-table-custom-wrapper.placement--right {
  top: 2px;
  height: calc(100% - 4px);
}
.vxe-table-custom-wrapper.is--active {
  display: flex;
}

.vxe-table-custom--body {
  position: relative;
  display: block;
  flex-grow: 1;
  overflow-x: hidden;
  overflow-y: auto;
}

.vxe-table-custom--panel-list {
  list-style-type: none;
  margin: 0;
  padding: 0;
}
.vxe-table-custom--panel-list > li {
  max-width: 26em;
  min-width: 18em;
  padding: 0.2em 1em 0.2em 1em;
}
.vxe-table-custom--panel-list > li.level--2 {
  padding-left: 2.7em;
}
.vxe-table-custom--panel-list > li.level--3 {
  padding-left: 3.7em;
}
.vxe-table-custom--panel-list > li.level--4 {
  padding-left: 4.7em;
}
.vxe-table-custom--panel-list > li.level--5 {
  padding-left: 5.7em;
}
.vxe-table-custom--panel-list > li.level--6 {
  padding-left: 6.7em;
}
.vxe-table-custom--panel-list > li.level--7 {
  padding-left: 7.7em;
}
.vxe-table-custom--panel-list > li.level--8 {
  padding-left: 8.7em;
}

.vxe-table-custom--header {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  height: 2.4em;
  font-weight: 700;
  border-bottom: 1px solid var(--vxe-ui-base-popup-border-color);
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.vxe-table-custom--panel-list .vxe-table-custom--checkbox-option:hover {
  background-color: var(--vxe-ui-table-row-hover-background-color);
}

.vxe-table-custom--footer-buttons {
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-shrink: 0;
  border-top: 1px solid var(--vxe-ui-base-popup-border-color);
}
.vxe-table-custom--footer-buttons button {
  flex-grow: 1;
  height: 2.8em;
}

.vxe-table-custom--checkbox-option .vxe-checkbox--icon {
  font-size: 1.34em;
}
.vxe-table-custom--checkbox-option .vxe-checkbox--icon {
  color: var(--vxe-ui-input-border-color);
  vertical-align: middle;
  font-weight: 700;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-table-custom--checkbox-option.is--checked, .vxe-table-custom--checkbox-option.is--indeterminate {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-table-custom--checkbox-option.is--checked .vxe-checkbox--icon, .vxe-table-custom--checkbox-option.is--indeterminate .vxe-checkbox--icon {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-table-custom--checkbox-option:not(.is--disabled) {
  cursor: pointer;
}
.vxe-table-custom--checkbox-option:not(.is--disabled):hover .vxe-checkbox--icon {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-table-custom--checkbox-option.is--hidden {
  cursor: default;
}
.vxe-table-custom--checkbox-option.is--disabled {
  color: var(--vxe-ui-font-disabled-color);
  cursor: not-allowed;
}
.vxe-table-custom--checkbox-option.is--disabled .vxe-checkbox--icon {
  color: var(--vxe-ui-input-disabled-color);
}
.vxe-table-custom--checkbox-option .vxe-checkbox--label {
  padding-left: 0.5em;
  vertical-align: middle;
}

.vxe-table-custom--checkbox-option,
.vxe-table-custom--sort-option {
  padding-right: 0.4em;
  flex-shrink: 0;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.vxe-table-custom--sort-option {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.vxe-table-custom--sort-btn {
  padding-left: 0.2em;
  padding-right: 0.4em;
}
.vxe-table-custom--sort-btn:not(.is--disabled) {
  cursor: grab;
}
.vxe-table-custom--sort-btn:not(.is--disabled):active {
  cursor: grabbing;
}
.vxe-table-custom--sort-btn:not(.is--disabled):hover {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-table-custom--sort-btn.is--disabled {
  color: var(--vxe-ui-input-disabled-color);
  cursor: not-allowed;
}

.vxe-table-custom--name-option {
  flex-grow: 1;
  display: flex;
  flex-direction: row;
  overflow: hidden;
}

.vxe-table-custom--checkbox-label {
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.vxe-table-custom--fixed-option {
  flex-shrink: 0;
  padding-left: 0.5em;
  display: flex;
  flex-direction: row;
  align-items: center;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.vxe-table-custom-popup--body {
  position: relative;
  overflow: auto;
  height: 100%;
  outline: 0;
}

.vxe-table-custom-popup--table-wrapper {
  border-bottom: 1px solid var(--vxe-ui-table-border-color);
}
.vxe-table-custom-popup--table-wrapper table {
  width: 100%;
  border: 0;
  table-layout: fixed;
  word-break: break-all;
  border-spacing: 0;
  border-collapse: separate;
}
.vxe-table-custom-popup--table-wrapper table th {
  position: sticky;
  top: 0;
  text-align: left;
  border-top: 1px solid var(--vxe-ui-table-border-color);
  border-bottom: 1px solid var(--vxe-ui-table-border-color);
  border-left: 1px solid var(--vxe-ui-table-border-color);
  background-color: var(--vxe-ui-table-header-background-color);
  z-index: 7;
}
.vxe-table-custom-popup--table-wrapper table td {
  border-top: 1px solid var(--vxe-ui-table-border-color);
  border-left: 1px solid var(--vxe-ui-table-border-color);
}
.vxe-table-custom-popup--table-wrapper table tr:first-child td {
  border-top: 0;
}
.vxe-table-custom-popup--table-wrapper table tr:hover {
  background-color: var(--vxe-ui-table-row-hover-background-color);
}
.vxe-table-custom-popup--table-wrapper table th,
.vxe-table-custom-popup--table-wrapper table td {
  height: 44px;
  padding: 0 0.6em;
}
.vxe-table-custom-popup--table-wrapper table th:last-child,
.vxe-table-custom-popup--table-wrapper table td:last-child {
  border-right: 1px solid var(--vxe-ui-table-border-color);
}

/*拖拽列*/
.vxe-table-custom-popup--drag-line {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 11;
  pointer-events: none;
}

.vxe-table-custom-popup--drag-line {
  width: 100%;
  height: 1px;
  border: 2px solid transparent;
}
.vxe-table-custom-popup--drag-line[drag-pos=top] {
  border-top-color: var(--vxe-ui-font-primary-color);
}
.vxe-table-custom-popup--drag-line[drag-pos=bottom] {
  border-bottom-color: var(--vxe-ui-font-primary-color);
}
.vxe-table-custom-popup--drag-line[drag-to-child=y] {
  border-top-color: transparent;
  border-bottom-color: transparent;
  border-left-color: var(--vxe-ui-status-success-color);
}
.vxe-table-custom-popup--drag-line.is--guides {
  background-color: var(--vxe-ui-table-drag-over-background-color);
}

.vxe-table-custom-popup--drag-tip {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  padding: 0.6em 1.4em;
  max-width: 50%;
  min-width: 100px;
  border-radius: var(--vxe-ui-border-radius);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  pointer-events: none;
  background-color: var(--vxe-ui-layout-background-color);
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
  z-index: 33;
}
.vxe-table-custom-popup--drag-tip[drag-status=normal] .vxe-table-custom-popup--drag-tip-normal-status {
  display: block;
}
.vxe-table-custom-popup--drag-tip[drag-status=sub] .vxe-table-custom-popup--drag-tip-sub-status {
  display: block;
}
.vxe-table-custom-popup--drag-tip[drag-status=disabled] .vxe-table-custom-popup--drag-tip-disabled-status {
  display: block;
}

.vxe-table-custom-popup--drag-tip-wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.vxe-table-custom-popup--drag-tip-status {
  padding-right: 0.4em;
}

.vxe-table-custom-popup--drag-tip-disabled-status {
  display: none;
  flex-shrink: 0;
  color: var(--vxe-ui-status-error-color);
}

.vxe-table-custom-popup--drag-tip-normal-status,
.vxe-table-custom-popup--drag-tip-sub-status {
  display: none;
}

.vxe-table-custom-popup--drag-tip-content {
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.vxe-table-custom-popup--name {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.vxe-table-custom-popup--title {
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.vxe-table-custom-popup--row.level--2 .vxe-table-custom-popup--name {
  padding-left: 24px;
}

.vxe-table-custom-popup--row.level--3 .vxe-table-custom-popup--name {
  padding-left: 48px;
}

.vxe-table-custom-popup--row.level--4 .vxe-table-custom-popup--name {
  padding-left: 72px;
}

.vxe-table-custom-popup--row.level--5 .vxe-table-custom-popup--name {
  padding-left: 96px;
}

.vxe-table-custom-popup--row.level--6 .vxe-table-custom-popup--name {
  padding-left: 120px;
}

.vxe-table-custom-popup--row.level--7 .vxe-table-custom-popup--name {
  padding-left: 144px;
}

.vxe-table-custom-popup--row.level--8 .vxe-table-custom-popup--name {
  padding-left: 168px;
}

.vxe-table-custom-popup--row.level--9 .vxe-table-custom-popup--name {
  padding-left: 192px;
}

.vxe-table-custom-popup--column-item {
  position: relative;
}
.vxe-table-custom-popup--column-item.col--visible, .vxe-table-custom-popup--column-item.col--resizable, .vxe-table-custom-popup--column-item.col--fixed {
  text-align: center;
}
.vxe-table-custom-popup--column-item.col--resizable > .vxe-input, .vxe-table-custom-popup--column-item.col--resizable > .vxe-number-input {
  width: 100%;
}

.vxe-table-custom-popup--row.active--drag-origin .vxe-table-custom-popup--column-item {
  opacity: 0.5;
}
.vxe-table-custom-popup--row.active--drag-target[drag-pos=top] .vxe-table-custom-popup--column-item::after {
  display: block;
  top: -2px;
}
.vxe-table-custom-popup--row.active--drag-target[drag-pos=bottom] .vxe-table-custom-popup--column-item::after {
  display: block;
  bottom: -2px;
}
.vxe-table-custom-popup--row:first-child[drag-pos=top] .vxe-table-custom-popup--column-item::after {
  top: 0;
}
.vxe-table-custom-popup--row:last-child[drag-pos=bottom] .vxe-table-custom-popup--column-item::after {
  bottom: 0;
}

.vxe-table-custom-popup--column-item::after {
  display: none;
  content: "";
  position: absolute;
  left: -1px;
  width: calc(100% + 1px);
  height: 2px;
  background-color: var(--vxe-ui-font-primary-color);
  z-index: 12;
}
.vxe-table-custom-popup--column-item:last-child::after {
  width: 100%;
}

.vxe-table-custom--list-move {
  transition-property: transform;
  transition-duration: 0.35s;
  transition-delay: 0.05s;
}

.vxe-table-custom-popup--column-sort-placeholder {
  padding: 0.2em 0.5em;
}

.vxe-table-custom-popup--column-sort-btn {
  font-size: 1.2em;
  padding: 0.2em 0.5em;
}
.vxe-table-custom-popup--column-sort-btn:not(.is--disabled) {
  cursor: grab;
}
.vxe-table-custom-popup--column-sort-btn:not(.is--disabled):hover {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-table-custom-popup--column-sort-btn:not(.is--disabled):active {
  cursor: grabbing;
}
.vxe-table-custom-popup--column-sort-btn.is--disabled {
  color: var(--vxe-ui-input-disabled-color);
  cursor: not-allowed;
}

.vxe-table-custom-popup--table-sort-help-title,
.vxe-table-custom-popup--table-sort-help-icon {
  vertical-align: middle;
}

.vxe-table-custom-popup--table-sort-help-icon {
  margin-left: 5px;
  cursor: help;
}

.vxe-table-custom-popup--table-col-seq {
  width: 80px;
}

.vxe-table-custom-popup--table-col-sort {
  width: 80px;
}

.vxe-table-custom-popup--table-col-title {
  min-width: 120px;
}

.vxe-table-custom-popup--table-col-width {
  width: 140px;
}

.vxe-table-custom-popup--table-col-fixed {
  width: 200px;
}

.vxe-table-export--panel-column > ul {
  list-style-type: none;
  overflow: auto;
  margin: 0;
  padding: 0;
  outline: 0;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-table-export--panel-column > ul > li {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}

.vxe-table-export--panel .vxe-table-export--panel-table {
  width: 100%;
  border: 0;
  table-layout: fixed;
}
.vxe-table-export--panel .vxe-table-export--panel-table tr td {
  padding: 0 10px;
}
.vxe-table-export--panel .vxe-table-export--panel-table tr td:nth-child(1) {
  text-align: right;
  width: 30%;
  font-weight: 700;
  padding: 8px 10px;
}
.vxe-table-export--panel .vxe-table-export--panel-table tr td:nth-child(2) {
  width: 70%;
}
.vxe-table-export--panel .vxe-table-export--panel-table tr td > .vxe-input, .vxe-table-export--panel .vxe-table-export--panel-table tr td > .vxe-select {
  width: 80%;
}
.vxe-table-export--panel .vxe-table-export--panel-table tr td > .vxe-table-export--panel-option-row {
  padding: 0.25em 0;
}
.vxe-table-export--panel .vxe-table-export--panel-column {
  width: 80%;
  border: 1px solid var(--vxe-ui-input-border-color);
  margin: 3px 0;
  border-radius: var(--vxe-ui-border-radius);
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li {
  padding: 0.2em 1em 0.2em 1em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--2 {
  padding-left: 3.5em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--2 .vxe-checkbox--icon {
  left: 1.8em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--3 {
  padding-left: 4.5em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--3 .vxe-checkbox--icon {
  left: 2.8em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--4 {
  padding-left: 5.5em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--4 .vxe-checkbox--icon {
  left: 3.8em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--5 {
  padding-left: 6.5em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--5 .vxe-checkbox--icon {
  left: 4.8em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--6 {
  padding-left: 7.5em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--6 .vxe-checkbox--icon {
  left: 5.8em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--7 {
  padding-left: 8.5em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--7 .vxe-checkbox--icon {
  left: 6.8em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--8 {
  padding-left: 9.5em;
}
.vxe-table-export--panel .vxe-table-export--panel-column > ul > li.level--8 .vxe-checkbox--icon {
  left: 7.8em;
}
.vxe-table-export--panel .vxe-table-export--panel-column .vxe-table-export--panel-column-header {
  padding: 0.1em 0;
  background-color: var(--vxe-ui-table-header-background-color);
  font-weight: 700;
  border-bottom: 1px solid var(--vxe-ui-table-border-color);
}
.vxe-table-export--panel .vxe-table-export--panel-column .vxe-table-export--panel-column-body {
  padding: 0.2em 0;
  min-height: 10em;
  max-height: 17.6em;
}
.vxe-table-export--panel .vxe-table-export--panel-btns {
  text-align: right;
  padding: 0.8em 0.25em 0.25em 0.25em;
}

.vxe-table-export--panel .vxe-table-export--selected--file {
  padding-right: 40px;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-table-export--panel .vxe-table-export--selected--file > i {
  display: none;
  position: absolute;
  top: 50%;
  right: 15px;
  transform: translateY(-50%);
  font-size: 16px;
  cursor: pointer;
}
.vxe-table-export--panel .vxe-table-export--selected--file:hover {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-table-export--panel .vxe-table-export--selected--file:hover > i {
  display: block;
}
.vxe-table-export--panel .vxe-table-export--select--file {
  width: 80%;
  border: 1px dashed var(--vxe-ui-input-border-color);
  padding: 6px 34px;
  outline: 0;
  border-radius: var(--vxe-ui-border-radius);
  background-color: var(--vxe-ui-layout-background-color);
  color: inherit;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  cursor: pointer;
}
.vxe-table-export--panel .vxe-table-export--select--file:focus {
  border-color: var(--vxe-ui-font-primary-color);
  box-shadow: 0 0 0.25em 0 var(--vxe-ui-font-primary-color);
}
.vxe-table-export--panel .vxe-table-export--select--file:hover {
  color: var(--vxe-ui-font-primary-color);
  border-color: var(--vxe-ui-font-primary-color);
}

.vxe-table-export--panel-column-option .vxe-checkbox--icon {
  font-size: 1.34em;
}
.vxe-table-export--panel-column-option .vxe-checkbox--icon {
  color: var(--vxe-ui-input-border-color);
  vertical-align: middle;
  font-weight: 700;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-table-export--panel-column-option.is--checked, .vxe-table-export--panel-column-option.is--indeterminate {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-table-export--panel-column-option.is--checked .vxe-checkbox--icon, .vxe-table-export--panel-column-option.is--indeterminate .vxe-checkbox--icon {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-table-export--panel-column-option:not(.is--disabled) {
  cursor: pointer;
}
.vxe-table-export--panel-column-option:not(.is--disabled):hover .vxe-checkbox--icon {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-table-export--panel-column-option.is--hidden {
  cursor: default;
}
.vxe-table-export--panel-column-option.is--disabled {
  color: var(--vxe-ui-font-disabled-color);
  cursor: not-allowed;
}
.vxe-table-export--panel-column-option.is--disabled .vxe-checkbox--icon {
  color: var(--vxe-ui-input-disabled-color);
}
.vxe-table-export--panel-column-option .vxe-checkbox--label {
  padding-left: 0.5em;
  vertical-align: middle;
}

.vxe-cell--filter {
  padding: 0 0.1em 0 0.2em;
  text-align: center;
  vertical-align: middle;
  display: inline-block;
  line-height: 0;
}
.vxe-cell--filter.col--filter .vxe-filter--btn {
  color: var(--vxe-ui-font-color);
}
.vxe-cell--filter .vxe-filter--btn {
  color: var(--vxe-ui-table-column-icon-border-color);
  cursor: pointer;
}
.vxe-cell--filter .vxe-filter--btn:hover {
  color: var(--vxe-ui-font-color);
}

.is--filter-active .vxe-cell--filter .vxe-filter--btn {
  color: var(--vxe-ui-font-primary-color);
}

/*筛选容器*/
.vxe-table--filter-wrapper {
  display: none;
  position: absolute;
  top: 0;
  min-width: 100px;
  font-size: var(--vxe-ui-font-size-default);
  border-radius: var(--vxe-ui-border-radius);
  background-color: var(--vxe-ui-layout-background-color);
  border: 1px solid var(--vxe-ui-base-popup-border-color);
  color: var(--vxe-ui-font-color);
  box-shadow: var(--vxe-ui-base-popup-box-shadow);
  z-index: 10;
}
.vxe-table--filter-wrapper:not(.is--multiple) {
  text-align: center;
}
.vxe-table--filter-wrapper.is--active {
  display: block;
}
.vxe-table--filter-wrapper .vxe-table--filter-header > li,
.vxe-table--filter-wrapper .vxe-table--filter-body > li {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 360px;
  padding: 0.25em 0.8em;
  cursor: pointer;
}
.vxe-table--filter-wrapper .vxe-table--filter-header > li.is--checked,
.vxe-table--filter-wrapper .vxe-table--filter-body > li.is--checked {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-table--filter-wrapper .vxe-table--filter-header > li:hover,
.vxe-table--filter-wrapper .vxe-table--filter-body > li:hover {
  background-color: var(--vxe-ui-table-row-hover-background-color);
}
.vxe-table--filter-wrapper .vxe-table--filter-header {
  padding-top: 0.2em;
}
.vxe-table--filter-wrapper .vxe-table--filter-body {
  max-height: 200px;
  padding-bottom: 0.2em;
}
.vxe-table--filter-wrapper > ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
  outline: 0;
  overflow: auto;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-table--filter-wrapper.is--multiple > ul > li {
  padding: 0.25em 0.8em 0.25em 1em;
}
.vxe-table--filter-wrapper .vxe-table--filter-footer {
  border-top: 1px solid var(--vxe-ui-base-popup-border-color);
  padding: 0.6em;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-table--filter-wrapper .vxe-table--filter-footer button {
  background-color: transparent;
  padding: 0 0.4em;
  border: 0;
  color: var(--vxe-ui-font-color);
  cursor: pointer;
}
.vxe-table--filter-wrapper .vxe-table--filter-footer button:focus {
  outline: none;
}
.vxe-table--filter-wrapper .vxe-table--filter-footer button:hover {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-table--filter-wrapper .vxe-table--filter-footer button.is--disabled {
  color: var(--vxe-ui-font-disabled-color);
  cursor: not-allowed;
}

.vxe-table--filter-option .vxe-checkbox--icon {
  font-size: 1.34em;
}
.vxe-table--filter-option .vxe-checkbox--icon {
  color: var(--vxe-ui-input-border-color);
  vertical-align: middle;
  font-weight: 700;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-table--filter-option.is--checked, .vxe-table--filter-option.is--indeterminate {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-table--filter-option.is--checked .vxe-checkbox--icon, .vxe-table--filter-option.is--indeterminate .vxe-checkbox--icon {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-table--filter-option:not(.is--disabled) {
  cursor: pointer;
}
.vxe-table--filter-option:not(.is--disabled):hover .vxe-checkbox--icon {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-table--filter-option.is--hidden {
  cursor: default;
}
.vxe-table--filter-option.is--disabled {
  color: var(--vxe-ui-font-disabled-color);
  cursor: not-allowed;
}
.vxe-table--filter-option.is--disabled .vxe-checkbox--icon {
  color: var(--vxe-ui-input-disabled-color);
}
.vxe-table--filter-option .vxe-checkbox--label {
  padding-left: 0.5em;
  vertical-align: middle;
}

.vxe-table--filter-wrapper.size--medium {
  font-size: var(--vxe-ui-font-size-medium);
}
.vxe-table--filter-wrapper.size--small {
  font-size: var(--vxe-ui-font-size-small);
}
.vxe-table--filter-wrapper.size--mini {
  font-size: var(--vxe-ui-font-size-mini);
}

.vxe-table--context-menu-wrapper {
  display: none;
}
.vxe-table--context-menu-wrapper.is--visible {
  display: block;
}

.vxe-table--context-menu-wrapper,
.vxe-table--context-menu-clild-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  font-size: 12px;
  border: 1px solid var(--vxe-ui-base-popup-border-color);
  box-shadow: 3px 3px 4px -2px rgba(0, 0, 0, 0.6);
  padding: 0 1px;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  color: var(--vxe-ui-font-color);
  font-family: var(--vxe-ui-font-family);
  background-color: var(--vxe-ui-table-menu-background-color);
}

.vxe-context-menu--link {
  display: flex;
  flex-direction: row;
  width: var(--vxe-ui-table-menu-item-width);
  line-height: 26px;
  padding: 0 0.8em;
  color: var(--vxe-ui-font-color);
  cursor: pointer;
}
.vxe-context-menu--link .vxe-context-menu--link-prefix,
.vxe-context-menu--link .vxe-context-menu--link-suffix {
  min-width: 2em;
  flex-shrink: 0;
  text-align: center;
  padding: 0 0.2em;
}
.vxe-context-menu--link .vxe-context-menu--link-content {
  flex-grow: 1;
  padding: 0 0.2em;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.vxe-context-menu--option-wrapper,
.vxe-table--context-menu-clild-wrapper {
  margin: 0;
  padding: 0;
  list-style-type: none;
  border-bottom: 1px solid #E8EAED;
}
.vxe-context-menu--option-wrapper li,
.vxe-table--context-menu-clild-wrapper li {
  position: relative;
  margin: 1px 0;
  border: 1px solid transparent;
}
.vxe-context-menu--option-wrapper li:last-child,
.vxe-table--context-menu-clild-wrapper li:last-child {
  border: 0;
}
.vxe-context-menu--option-wrapper li.link--active,
.vxe-table--context-menu-clild-wrapper li.link--active {
  background-color: #C5C5C5;
  border-color: #C5C5C5;
}
.vxe-context-menu--option-wrapper li.link--active > .vxe-context-menu--link,
.vxe-table--context-menu-clild-wrapper li.link--active > .vxe-context-menu--link {
  color: #2B2B2B;
}
.vxe-context-menu--option-wrapper li.link--disabled > .vxe-context-menu--link,
.vxe-table--context-menu-clild-wrapper li.link--disabled > .vxe-context-menu--link {
  color: var(--vxe-ui-font-disabled-color);
  cursor: no-drop;
}
.vxe-context-menu--option-wrapper li.link--disabled.link--active,
.vxe-table--context-menu-clild-wrapper li.link--disabled.link--active {
  border-color: #C0C1C2;
  background-color: #EEEEEE;
}
.vxe-context-menu--option-wrapper li.link--disabled.link--active:hover,
.vxe-table--context-menu-clild-wrapper li.link--disabled.link--active:hover {
  background-color: inherit;
}

.vxe-table--context-menu-clild-wrapper {
  display: none;
  top: 0;
  left: 100%;
}
.vxe-table--context-menu-clild-wrapper.is--show {
  display: block;
}

.vxe-table-slots,
.vxe-table--file-form {
  display: none;
}

.vxe-table-vars {
  height: 0;
  width: 0;
  visibility: hidden;
  overflow: hidden;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  pointer-events: none;
}
.vxe-table-vars .vxe-table-var-default {
  height: var(--vxe-ui-table-row-height-default);
}
.vxe-table-vars .vxe-table-var-medium {
  height: var(--vxe-ui-table-row-height-medium);
}
.vxe-table-vars .vxe-table-var-small {
  height: var(--vxe-ui-table-row-height-small);
}
.vxe-table-vars .vxe-table-var-mini {
  height: var(--vxe-ui-table-row-height-mini);
}

.vxe-table--print-frame {
  position: fixed;
  bottom: -100%;
  left: -100%;
  height: 0;
  width: 0;
  border: 0;
}

.vxe-table--layout-wrapper {
  display: flex;
  flex-direction: row;
  background-color: var(--vxe-ui-layout-background-color);
}

.vxe-table--viewport-wrapper {
  position: relative;
  overflow: hidden;
  flex-grow: 1;
}

.vxe-table--header-wrapper,
.vxe-table--body-wrapper,
.vxe-table--footer-wrapper,
.vxe-table--fixed-left-body-wrapper,
.vxe-table--fixed-right-body-wrapper {
  overflow: hidden;
  outline: 0;
  scrollbar-width: none;
  -webkit-overflow-scrolling: touch;
}

.vxe-table--header-inner-wrapper,
.vxe-table--body-inner-wrapper,
.vxe-table--footer-inner-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  scrollbar-width: none;
  -ms-overflow-style: none;
  -webkit-overflow-scrolling: touch;
}
.vxe-table--header-inner-wrapper::-webkit-scrollbar,
.vxe-table--body-inner-wrapper::-webkit-scrollbar,
.vxe-table--footer-inner-wrapper::-webkit-scrollbar {
  display: none;
}

.vxe-table--header-inner-wrapper,
.vxe-table--footer-inner-wrapper {
  overflow-y: hidden;
  overflow-x: scroll;
}

.vxe-table--body-inner-wrapper {
  overflow-y: scroll;
  overflow-x: scroll;
}

.vxe-loading--custom-wrapper {
  display: none;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 99;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-loading--custom-wrapper.is--visible {
  display: block;
}

/*默认的渲染*/
.vxe-header--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-default-input,
.vxe-header--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-default-textarea,
.vxe-header--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-default-select,
.vxe-footer--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-default-input,
.vxe-footer--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-default-textarea,
.vxe-footer--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-default-select {
  width: 100%;
}
.vxe-header--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-input,
.vxe-header--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-textarea,
.vxe-header--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-select,
.vxe-header--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-tree-select,
.vxe-header--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-date-picker,
.vxe-header--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-date-range-picker,
.vxe-header--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-number-input,
.vxe-header--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-ico-picker,
.vxe-footer--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-input,
.vxe-footer--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-textarea,
.vxe-footer--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-select,
.vxe-footer--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-tree-select,
.vxe-footer--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-date-picker,
.vxe-footer--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-date-range-picker,
.vxe-footer--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-number-input,
.vxe-footer--column.fixed--width > .vxe-cell > .vxe-cell--wrapper .vxe-ico-picker {
  width: 100%;
}

.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-default-input, .vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-default-textarea, .vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-default-select {
  width: 100%;
}
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-input, .vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-textarea, .vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-select, .vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-tree-select, .vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-date-picker, .vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-date-range-picker, .vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-number-input, .vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-ico-picker {
  width: 100%;
}
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-row-group--tree-node .vxe-default-input,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-row-group--tree-node .vxe-default-textarea,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-row-group--tree-node .vxe-default-select,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-row-group--tree-node .vxe-input,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-row-group--tree-node .vxe-textarea,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-row-group--tree-node .vxe-select,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-row-group--tree-node .vxe-tree-select,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-row-group--tree-node .vxe-date-picker,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-row-group--tree-node .vxe-date-range-picker,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-row-group--tree-node .vxe-number-input,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-row-group--tree-node .vxe-ico-picker, .vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-cell--tree-node .vxe-default-input,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-cell--tree-node .vxe-default-textarea,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-cell--tree-node .vxe-default-select,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-cell--tree-node .vxe-input,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-cell--tree-node .vxe-textarea,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-cell--tree-node .vxe-select,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-cell--tree-node .vxe-tree-select,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-cell--tree-node .vxe-date-picker,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-cell--tree-node .vxe-date-range-picker,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-cell--tree-node .vxe-number-input,
.vxe-body--column.fixed--width > .vxe-cell > .vxe-cell--wrapper > .vxe-cell--tree-node .vxe-ico-picker {
  width: 100%;
}

.vxe-table--filter-template > .vxe-default-input, .vxe-table--filter-template > .vxe-default-textarea, .vxe-table--filter-template > .vxe-default-select {
  width: 100%;
}
.vxe-table--filter-template > .vxe-input, .vxe-table--filter-template > .vxe-textarea, .vxe-table--filter-template > .vxe-select, .vxe-table--filter-template > .vxe-tree-select, .vxe-table--filter-template > .vxe-date-picker, .vxe-table--filter-template > .vxe-date-range-picker, .vxe-table--filter-template > .vxe-number-input, .vxe-table--filter-template > .vxe-ico-picker {
  width: 100%;
}

.vxe-cell .vxe-default-input,
.vxe-cell .vxe-default-textarea,
.vxe-table--filter-template .vxe-default-input,
.vxe-table--filter-template .vxe-default-textarea {
  background-color: var(--vxe-ui-layout-background-color);
}
.vxe-cell .vxe-default-input,
.vxe-cell .vxe-default-textarea,
.vxe-cell .vxe-default-select,
.vxe-table--filter-template .vxe-default-input,
.vxe-table--filter-template .vxe-default-textarea,
.vxe-table--filter-template .vxe-default-select {
  font-family: var(--vxe-ui-font-family);
  outline: 0;
  color: var(--vxe-ui-font-color);
  border-radius: var(--vxe-ui-border-radius);
  border: 1px solid var(--vxe-ui-input-border-color);
}
.vxe-cell .vxe-default-input:focus,
.vxe-cell .vxe-default-textarea:focus,
.vxe-cell .vxe-default-select:focus,
.vxe-table--filter-template .vxe-default-input:focus,
.vxe-table--filter-template .vxe-default-textarea:focus,
.vxe-table--filter-template .vxe-default-select:focus {
  border: 1px solid var(--vxe-ui-font-primary-color);
}
.vxe-cell .vxe-default-input[disabled],
.vxe-cell .vxe-default-textarea[disabled],
.vxe-cell .vxe-default-select[disabled],
.vxe-table--filter-template .vxe-default-input[disabled],
.vxe-table--filter-template .vxe-default-textarea[disabled],
.vxe-table--filter-template .vxe-default-select[disabled] {
  cursor: not-allowed;
  background-color: var(--vxe-ui-input-disabled-background-color);
}
.vxe-cell .vxe-default-input,
.vxe-cell .vxe-default-textarea,
.vxe-cell .vxe-default-select,
.vxe-table--filter-template .vxe-default-input,
.vxe-table--filter-template .vxe-default-textarea,
.vxe-table--filter-template .vxe-default-select {
  height: var(--vxe-ui-input-height-default);
}
.vxe-cell .vxe-default-input[type=date]::-webkit-inner-spin-button,
.vxe-table--filter-template .vxe-default-input[type=date]::-webkit-inner-spin-button {
  margin-top: 4px;
}
.vxe-cell .vxe-default-input[type=date]::-webkit-inner-spin-button, .vxe-cell .vxe-default-input[type=number]::-webkit-inner-spin-button,
.vxe-table--filter-template .vxe-default-input[type=date]::-webkit-inner-spin-button,
.vxe-table--filter-template .vxe-default-input[type=number]::-webkit-inner-spin-button {
  height: 24px;
}
.vxe-cell .vxe-default-input::-moz-placeholder, .vxe-table--filter-template .vxe-default-input::-moz-placeholder {
  color: var(--vxe-ui-input-placeholder-color);
}
.vxe-cell .vxe-default-input::placeholder,
.vxe-table--filter-template .vxe-default-input::placeholder {
  color: var(--vxe-ui-input-placeholder-color);
}
.vxe-cell .vxe-default-textarea,
.vxe-table--filter-template .vxe-default-textarea {
  font-size: 1em;
  resize: none;
  vertical-align: middle;
}
.vxe-cell > .vxe-input > .vxe-input--inner, .vxe-cell > .vxe-textarea > .vxe-textarea--inner,
.vxe-table--filter-template > .vxe-input > .vxe-input--inner,
.vxe-table--filter-template > .vxe-textarea > .vxe-textarea--inner {
  padding: 0 2px;
}
.vxe-cell > .vxe-textarea--inner, .vxe-cell > .vxe-default-textarea,
.vxe-table--filter-template > .vxe-textarea--inner,
.vxe-table--filter-template > .vxe-default-textarea {
  resize: none;
}
.vxe-cell > .vxe-row-group--tree-node .vxe-input > .vxe-input--inner,
.vxe-cell > .vxe-row-group--tree-node .vxe-textarea > .vxe-textarea--inner, .vxe-cell > .vxe-cell--tree-node .vxe-input > .vxe-input--inner,
.vxe-cell > .vxe-cell--tree-node .vxe-textarea > .vxe-textarea--inner,
.vxe-table--filter-template > .vxe-row-group--tree-node .vxe-input > .vxe-input--inner,
.vxe-table--filter-template > .vxe-row-group--tree-node .vxe-textarea > .vxe-textarea--inner,
.vxe-table--filter-template > .vxe-cell--tree-node .vxe-input > .vxe-input--inner,
.vxe-table--filter-template > .vxe-cell--tree-node .vxe-textarea > .vxe-textarea--inner {
  padding: 0 2px;
}
.vxe-cell > .vxe-row-group--tree-node .vxe-textarea--inner,
.vxe-cell > .vxe-row-group--tree-node .vxe-default-textarea, .vxe-cell > .vxe-cell--tree-node .vxe-textarea--inner,
.vxe-cell > .vxe-cell--tree-node .vxe-default-textarea,
.vxe-table--filter-template > .vxe-row-group--tree-node .vxe-textarea--inner,
.vxe-table--filter-template > .vxe-row-group--tree-node .vxe-default-textarea,
.vxe-table--filter-template > .vxe-cell--tree-node .vxe-textarea--inner,
.vxe-table--filter-template > .vxe-cell--tree-node .vxe-default-textarea {
  resize: none;
}

.vxe-body--column.col--vertical-top.col--active > .vxe-cell > .vxe-cell--wrapper {
  height: 100%;
}
.vxe-body--column.col--vertical-top.col--active > .vxe-cell > .vxe-cell--wrapper > .vxe-default-textarea {
  height: 100%;
}

/*行高*/
.vxe-table:not([data-calc-row]) .vxe-body--column.col--vertical-top:not(.col--active) > .vxe-cell > .vxe-cell--wrapper {
  min-height: 100%;
}

/*负数显示红色*/
.vxe-cell--label.is--negative {
  color: var(--vxe-ui-table-cell-negative-color);
}

.vxe-table--checkbox-range,
.vxe-table--cell-main-area,
.vxe-table--cell-extend-area,
.vxe-table--cell-active-area,
.vxe-table--cell-copy-area,
.vxe-table--cell-col-status-area,
.vxe-table--cell-row-status-area {
  display: none;
  position: absolute;
  pointer-events: none;
  z-index: 1;
}

.vxe-table--header-wrapper .vxe-table--cell-col-status-area {
  top: 0;
  height: 100%;
}

.vxe-table--fixed-left-wrapper .vxe-table--checkbox-range,
.vxe-table--fixed-left-wrapper .vxe-table--cell-main-area,
.vxe-table--fixed-left-wrapper .vxe-table--cell-extend-area,
.vxe-table--fixed-left-wrapper .vxe-table--cell-active-area,
.vxe-table--fixed-left-wrapper .vxe-table--cell-copy-area,
.vxe-table--fixed-left-wrapper .vxe-table--cell-col-status-area,
.vxe-table--fixed-left-wrapper .vxe-table--cell-row-status-area,
.vxe-table--fixed-right-wrapper .vxe-table--checkbox-range,
.vxe-table--fixed-right-wrapper .vxe-table--cell-main-area,
.vxe-table--fixed-right-wrapper .vxe-table--cell-extend-area,
.vxe-table--fixed-right-wrapper .vxe-table--cell-active-area,
.vxe-table--fixed-right-wrapper .vxe-table--cell-copy-area,
.vxe-table--fixed-right-wrapper .vxe-table--cell-col-status-area,
.vxe-table--fixed-right-wrapper .vxe-table--cell-row-status-area {
  z-index: 2;
}

.vxe-table--fixed-left-wrapper .vxe-table--cell-main-area[half="1"],
.vxe-table--fixed-left-wrapper .vxe-table--cell-extend-area[half="1"],
.vxe-table--fixed-left-wrapper .vxe-table--cell-active-area[half="1"] {
  border-right: 0;
}
.vxe-table--fixed-left-wrapper .vxe-table--cell-copy-area[half="1"] {
  background-size: var(--vxe-ui-table-cell-copy-area-border-width) 12px, 0 12px, 12px var(--vxe-ui-table-cell-copy-area-border-width), 12px var(--vxe-ui-table-cell-copy-area-border-width);
}

.vxe-table--fixed-right-wrapper .vxe-table--cell-main-area[half="1"],
.vxe-table--fixed-right-wrapper .vxe-table--cell-extend-area[half="1"],
.vxe-table--fixed-right-wrapper .vxe-table--cell-active-area[half="1"] {
  border-left: 0;
}
.vxe-table--fixed-right-wrapper .vxe-table--cell-copy-area[half="1"] {
  background-size: 0 12px, var(--vxe-ui-table-cell-copy-area-border-width) 12px, 12px var(--vxe-ui-table-cell-copy-area-border-width), 12px var(--vxe-ui-table-cell-copy-area-border-width);
}

/*复选框-范围选择*/
.vxe-table--checkbox-range {
  background-color: var(--vxe-ui-table-checkbox-range-background-color);
  border: var(--vxe-ui-table-checkbox-range-border-width) solid var(--vxe-ui-table-checkbox-range-border-color);
}

.vxe-table--cell-area {
  height: 0;
  font-size: 0;
  display: none;
}
.vxe-table--cell-area > .vxe-table--cell-main-area {
  background-color: var(--vxe-ui-table-cell-area-background-color);
  border: var(--vxe-ui-table-cell-area-border-width) solid var(--vxe-ui-table-cell-area-border-color);
}
.vxe-table--cell-area .vxe-table--cell-main-area-btn {
  display: none;
  position: absolute;
  right: -1px;
  bottom: -1px;
  width: 7px;
  height: 7px;
  border-style: solid;
  border-color: var(--vxe-ui-table-cell-main-area-extension-border-color);
  border-width: 1px 0 0 1px;
  background-color: var(--vxe-ui-table-cell-main-area-extension-background-color);
  pointer-events: auto;
  cursor: crosshair;
}
.vxe-table--cell-area .vxe-table--cell-extend-area {
  border: var(--vxe-ui-table-cell-extend-area-border-width) solid var(--vxe-ui-table-cell-extend-area-border-color);
}
.vxe-table--cell-area .vxe-table--cell-col-status-area,
.vxe-table--cell-area .vxe-table--cell-row-status-area {
  background-color: var(--vxe-ui-table-checkbox-range-background-color);
}

@keyframes moveCopyCellBorder {
  to {
    background-position: 0 -12px, 100% 12px, 12px 0, -12px 100%;
  }
}
.vxe-table--cell-copy-area {
  background: linear-gradient(0deg, transparent 6px, var(--vxe-ui-table-cell-copy-area-border-color) 6px) repeat-y, linear-gradient(0deg, transparent 50%, var(--vxe-ui-table-cell-copy-area-border-color) 0) repeat-y, linear-gradient(90deg, transparent 50%, var(--vxe-ui-table-cell-copy-area-border-color) 0) repeat-x, linear-gradient(90deg, transparent 50%, var(--vxe-ui-table-cell-copy-area-border-color) 0) repeat-x;
  background-size: var(--vxe-ui-table-cell-copy-area-border-width) 12px, var(--vxe-ui-table-cell-copy-area-border-width) 12px, 12px var(--vxe-ui-table-cell-copy-area-border-width), 12px var(--vxe-ui-table-cell-copy-area-border-width);
  background-position: 0 0, 100% 0, 0 0, 0 100%;
  animation: moveCopyCellBorder 0.5s infinite linear;
}

.vxe-table--cell-active-area {
  border-color: var(--vxe-ui-table-cell-active-area-border-color);
  border-style: solid;
  border-width: var(--vxe-ui-table-cell-active-area-border-width);
  background-color: var(--vxe-ui-table-cell-active-area-background-color);
}

.vxe-table--cell-multi-area > .vxe-table--cell-main-area {
  background-color: var(--vxe-ui-table-cell-area-background-color);
}

/*圆角*/
.vxe-table--render-default.is--round:not(.is--header):not(.is--footer) .vxe-table--body-wrapper.body--wrapper, .vxe-table--render-default.is--round .vxe-table--border-line, .vxe-table--render-default.is--round .vxe-table--render-default.is--round {
  border-radius: var(--vxe-ui-table-border-radius);
}
.vxe-table--render-default.is--round.is--footer:not(.is--header) .vxe-table--body-wrapper.body--wrapper, .vxe-table--render-default.is--round.is--header .vxe-table--header-wrapper.body--wrapper {
  border-radius: var(--vxe-ui-table-border-radius) var(--vxe-ui-table-border-radius) 0 0;
}
.vxe-table--render-default.is--round.sx-pos--bottom.sy-pos--left .vxe-table--scroll-y-top-corner::before, .vxe-table--render-default.is--round.sx-pos--bottom.sy-pos--left .vxe-table--scroll-y-top-corner, .vxe-table--render-default.is--round.sx-pos--top .vxe-table--scroll-x-left-corner::before, .vxe-table--render-default.is--round.is--header .vxe-table--header-wrapper.fixed-left--wrapper {
  border-radius: var(--vxe-ui-table-border-radius) 0 0 0;
}
.vxe-table--render-default.is--round.sx-pos--bottom.sy-pos--right .vxe-table--scroll-y-top-corner::before, .vxe-table--render-default.is--round.sx-pos--bottom.sy-pos--right .vxe-table--scroll-y-top-corner, .vxe-table--render-default.is--round.sx-pos--top .vxe-table--scroll-x-right-corner::before, .vxe-table--render-default.is--round.is--header .vxe-table--header-wrapper.fixed-right--wrapper {
  border-radius: 0 var(--vxe-ui-table-border-radius) 0 0;
}
.vxe-table--render-default.is--round.is--footer .vxe-table--footer-wrapper.body--wrapper, .vxe-table--render-default.is--round.is--header:not(.is--footer) .vxe-table--body-wrapper.body--wrapper {
  border-radius: 0 0 var(--vxe-ui-table-border-radius) var(--vxe-ui-table-border-radius);
}
.vxe-table--render-default.is--round.sx-pos--bottom .vxe-table--scroll-x-left-corner::before, .vxe-table--render-default.is--round.is--footer .vxe-table--footer-wrapper.fixed-left--wrapper, .vxe-table--render-default.is--round.is--footer:not(.is--header) .vxe-table--body-wrapper.fixed-left--wrapper, .vxe-table--render-default.is--round.is--header:not(.is--footer) .vxe-table--body-wrapper.fixed-left--wrapper {
  border-radius: 0 0 0 var(--vxe-ui-table-border-radius);
}
.vxe-table--render-default.is--round.sx-pos--bottom .vxe-table--scroll-x-right-corner::before, .vxe-table--render-default.is--round.is--footer .vxe-table--footer-wrapper.fixed-right--wrapper, .vxe-table--render-default.is--round.is--footer:not(.is--header) .vxe-table--body-wrapper.fixed-right--wrapper, .vxe-table--render-default.is--round.is--header:not(.is--footer) .vxe-table--body-wrapper.fixed-right--wrapper {
  border-radius: 0 0 var(--vxe-ui-table-border-radius) 0;
}
/*列宽*/
.vxe-table[data-calc-col] .vxe-header--column .vxe-cell > .vxe-cell--wrapper,
.vxe-table[data-calc-col] .vxe-body--column .vxe-cell > .vxe-cell--wrapper,
.vxe-table[data-calc-col] .vxe-footer--column .vxe-cell > .vxe-cell--wrapper {
  word-break: break-all;
  white-space: nowrap;
}
.vxe-table:not([data-calc-col]) .vxe-cell--wrapper {
  min-width: 100%;
}

/*scroll*/
.vxe-table.is--loading > .vxe-table--scroll-x-virtual {
  visibility: hidden;
}
.vxe-table.is--loading > .vxe-table--layout-wrapper > .vxe-table--scroll-y-virtual {
  visibility: hidden;
}
.vxe-table .vxe-table--scroll-x-virtual {
  height: 0;
}
.vxe-table .vxe-table--scroll-y-virtual {
  width: 0;
}
.vxe-table .vxe-table--scroll-x-virtual,
.vxe-table .vxe-table--scroll-y-virtual {
  visibility: hidden;
  position: relative;
  flex-shrink: 0;
  z-index: 7;
}
.vxe-table .vxe-table--scroll-x-handle,
.vxe-table .vxe-table--scroll-y-handle,
.vxe-table .vxe-table--scroll-x-wrapper,
.vxe-table .vxe-table--scroll-y-wrapper,
.vxe-table .vxe-table--scroll-y-top-corner,
.vxe-table .vxe-table--scroll-y-bottom-corner,
.vxe-table .vxe-table--scroll-x-left-corner,
.vxe-table .vxe-table--scroll-x-right-corner {
  position: absolute;
}
.vxe-table .vxe-table--scroll-x-handle,
.vxe-table .vxe-table--scroll-x-wrapper {
  width: 100%;
  left: 0;
  bottom: 0;
}
.vxe-table .vxe-table--scroll-x-handle {
  overflow-y: hidden;
  overflow-x: scroll;
  height: 18px;
}
.vxe-table .vxe-table--scroll-x-wrapper {
  height: 100%;
}
.vxe-table .vxe-table--scroll-y-handle,
.vxe-table .vxe-table--scroll-y-wrapper {
  width: 100%;
  height: 100%;
  right: 0;
  top: 0;
}
.vxe-table .vxe-table--scroll-y-handle {
  overflow-y: scroll;
  overflow-x: hidden;
  width: 18px;
  height: 100%;
}
.vxe-table .vxe-table--scroll-x-space {
  height: 1px;
}
.vxe-table .vxe-table--scroll-y-space {
  width: 1px;
}
.vxe-table .vxe-table--scroll-x-left-corner,
.vxe-table .vxe-table--scroll-x-right-corner,
.vxe-table .vxe-table--scroll-y-top-corner,
.vxe-table .vxe-table--scroll-y-bottom-corner {
  display: none;
  position: absolute;
}
.vxe-table .vxe-table--scroll-x-left-corner,
.vxe-table .vxe-table--scroll-x-right-corner {
  bottom: 0;
  width: 0;
  height: 100%;
}
.vxe-table .vxe-table--scroll-x-left-corner::before,
.vxe-table .vxe-table--scroll-x-right-corner::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  border-width: var(--vxe-ui-table-border-width);
  border-style: solid;
  border-color: var(--vxe-ui-table-border-color);
}
.vxe-table .vxe-table--scroll-x-left-corner {
  left: 0;
}
.vxe-table .vxe-table--scroll-x-right-corner {
  right: 0;
}
.vxe-table.sy-pos--right .vxe-table--scroll-x-right-corner {
  right: 1px;
}
.vxe-table.sy-pos--right .vxe-table--scroll-x-right-corner::before {
  border-right: 0;
}
.vxe-table.sx-pos--bottom .vxe-table--scroll-x-right-corner {
  bottom: 1px;
}
.vxe-table.sx-pos--bottom .vxe-table--scroll-x-right-corner::before {
  border-bottom: 0;
}
.vxe-table .vxe-table--scroll-y-top-corner {
  background-color: var(--vxe-ui-table-header-background-color);
}
.vxe-table .vxe-table--scroll-y-top-corner,
.vxe-table .vxe-table--scroll-y-bottom-corner {
  top: 0;
  right: 0;
  width: 100%;
  height: 0;
}
.vxe-table .vxe-table--scroll-y-bottom-corner {
  margin-top: -1px;
}

/*header*/
.vxe-table {
  /*排序*/
}
.vxe-table .vxe-table--header-wrapper {
  color: var(--vxe-ui-table-header-font-color);
}
.vxe-table .vxe-cell--sort {
  text-align: center;
  position: relative;
  padding: 0 0.1em 0 0.2em;
}
.vxe-table .vxe-cell--sort-vertical-layout {
  display: inline-flex;
  flex-direction: column;
  height: 1.8em;
  vertical-align: middle;
}
.vxe-table .vxe-cell--sort-vertical-layout .vxe-sort--asc-btn,
.vxe-table .vxe-cell--sort-vertical-layout .vxe-sort--desc-btn {
  height: 0.6em;
}
.vxe-table .vxe-cell--sort-horizontal-layout {
  display: inline-flex;
  flex-direction: row;
}
.vxe-table .vxe-cell--sort-horizontal-layout .vxe-sort--asc-btn,
.vxe-table .vxe-cell--sort-horizontal-layout .vxe-sort--desc-btn {
  width: 0.5em;
}
.vxe-table .vxe-sort--asc-btn,
.vxe-table .vxe-sort--desc-btn {
  color: var(--vxe-ui-table-column-icon-border-color);
  cursor: pointer;
}
.vxe-table .vxe-sort--asc-btn:hover,
.vxe-table .vxe-sort--desc-btn:hover {
  color: var(--vxe-ui-font-color);
}
.vxe-table .vxe-sort--asc-btn.sort--active,
.vxe-table .vxe-sort--desc-btn.sort--active {
  color: var(--vxe-ui-font-primary-color);
}

.vxe-sort--asc-btn:before, .vxe-sort--asc-btn:after,
.vxe-sort--desc-btn:before,
.vxe-sort--desc-btn:after,
.vxe-filter--btn:before,
.vxe-filter--btn:after {
  transition: border 0.1s ease-in-out;
}

.vxe-header--column {
  position: relative;
  font-weight: var(--vxe-ui-table-header-font-weight);
}
.vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--wrapper {
  display: flex;
  align-items: center;
}
.vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--wrapper .vxe-cell--drag-handle,
.vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--wrapper .vxe-cell--sort,
.vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--wrapper .vxe-cell--filter,
.vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--wrapper .vxe-cell-title-prefix-icon,
.vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--wrapper .vxe-cell-title-suffix-icon,
.vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--wrapper .vxe-cell--checkbox,
.vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--wrapper .vxe-cell-title-suffix-icon,
.vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--wrapper .vxe-cell--required-icon {
  flex-shrink: 0;
}
.vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.vxe-header--column .vxe-cell--required-icon {
  display: inline-block;
  color: var(--vxe-ui-table-validate-error-color);
  width: 0.8em;
  height: 1em;
  line-height: 1em;
  position: relative;
}
.vxe-header--column .vxe-cell--required-icon > i {
  font-family: Verdana, Arial, Tahoma;
  font-weight: normal;
}
.vxe-header--column .vxe-cell--required-icon > i:before {
  content: "*";
  position: absolute;
  left: 0;
  top: 0.2em;
}
.vxe-header--column .vxe-cell--required-icon {
  padding-right: 0.1em;
}
.vxe-header--column .vxe-cell--edit-icon,
.vxe-header--column .vxe-cell-title-prefix-icon,
.vxe-header--column .vxe-cell-title-suffix-icon {
  padding: 0.2em;
}
.vxe-header--column .vxe-cell-title-prefix-icon,
.vxe-header--column .vxe-cell-title-suffix-icon {
  cursor: help;
}
.vxe-header--column .vxe-cell-title-prefix-icon.theme--primary,
.vxe-header--column .vxe-cell-title-suffix-icon.theme--primary {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-header--column .vxe-cell-title-prefix-icon.theme--success,
.vxe-header--column .vxe-cell-title-suffix-icon.theme--success {
  color: var(--vxe-ui-status-success-color);
}
.vxe-header--column .vxe-cell-title-prefix-icon.theme--info,
.vxe-header--column .vxe-cell-title-suffix-icon.theme--info {
  color: var(--vxe-ui-status-info-color);
}
.vxe-header--column .vxe-cell-title-prefix-icon.theme--warning,
.vxe-header--column .vxe-cell-title-suffix-icon.theme--warning {
  color: var(--vxe-ui-status-warning-color);
}
.vxe-header--column .vxe-cell-title-prefix-icon.theme--danger,
.vxe-header--column .vxe-cell-title-suffix-icon.theme--danger {
  color: var(--vxe-ui-status-danger-color);
}
.vxe-header--column .vxe-cell-title-prefix-icon.theme--error,
.vxe-header--column .vxe-cell-title-suffix-icon.theme--error {
  color: var(--vxe-ui-status-error-color);
}

.vxe-cell--col-resizable {
  position: absolute;
  right: -0.3em;
  bottom: 0;
  width: 0.6em;
  height: 100%;
  text-align: center;
  z-index: 1;
  cursor: col-resize;
}
.vxe-cell--col-resizable.is--line:before, .vxe-cell--col-resizable.is--line:after {
  content: "";
  display: inline-block;
  vertical-align: middle;
}
.vxe-cell--col-resizable.is--line:before {
  width: 1px;
  height: 50%;
  background-color: var(--vxe-ui-table-resizable-line-color);
}
.vxe-cell--col-resizable.is--line:after {
  width: 0;
  height: 100%;
}

.vxe-header--column:last-child > .vxe-cell--col-resizable {
  right: 0;
}

.vxe-table--fixed-right-wrapper .vxe-cell--col-resizable {
  right: auto;
  left: -0.3em;
}
.vxe-table--fixed-right-wrapper .vxe-header--column:last-child > .vxe-cell--col-resizable {
  left: 0;
}

.vxe-body--column .vxe-cell--row-resizable {
  position: absolute;
  left: 0;
  bottom: -0.4em;
  height: 0.8em;
  width: 100%;
  text-align: center;
  z-index: 1;
  cursor: row-resize;
}

.vxe-body--row:last-child .vxe-body--column .vxe-cell--row-resizable {
  height: 0.4em;
  bottom: 0px;
}

/*table*/
.vxe-table--render-default {
  position: relative;
  font-size: var(--vxe-ui-font-size-default);
  color: var(--vxe-ui-font-color);
  font-family: var(--vxe-ui-font-family);
  direction: ltr;
  /*边框*/
  /*列宽线*/
  /*边框线*/
  /*树形节点*/
  /*行分组*/
  /*单元格高度*/
  /*溢出列*/
  /*展开行*/
  /*拖拽列*/
  /*拖拽行*/
  /*暂无数据*/
  /*校验不通过*/
  /*已废弃，旧的校验样式**/
  /*单元格标记删除状态*/
  /*单元格编辑状态*/
  /*可编辑*/
}
.vxe-table--render-default .vxe-table--body-wrapper {
  background-color: var(--vxe-ui-layout-background-color);
}
.vxe-table--render-default .vxe-table--footer-wrapper {
  margin-top: calc(var(--vxe-ui-table-border-width) * -1);
  background-color: var(--vxe-ui-table-footer-background-color);
}
.vxe-table--render-default .vxe-table--header,
.vxe-table--render-default .vxe-table--body,
.vxe-table--render-default .vxe-table--footer {
  border: 0;
  border-spacing: 0;
  border-collapse: separate;
  table-layout: fixed;
}
.vxe-table--render-default:not(.is--empty).is--footer.is--scroll-x .vxe-table--body-wrapper {
  outline: 0;
}
.vxe-table--render-default.col-drag--resize .vxe-table--main-wrapper *,
.vxe-table--render-default.col-drag--resize .vxe-table--fixed-left-wrapper *,
.vxe-table--render-default.col-drag--resize .vxe-table--fixed-right-wrapper * {
  cursor: col-resize;
}
.vxe-table--render-default.row-drag--resize .vxe-table--main-wrapper *,
.vxe-table--render-default.row-drag--resize .vxe-table--fixed-left-wrapper *,
.vxe-table--render-default.row-drag--resize .vxe-table--fixed-right-wrapper * {
  cursor: row-resize;
}
.vxe-table--render-default.drag--range .vxe-table--main-wrapper *,
.vxe-table--render-default.drag--range .vxe-table--fixed-left-wrapper *,
.vxe-table--render-default.drag--range .vxe-table--fixed-right-wrapper *, .vxe-table--render-default.drag--area .vxe-table--main-wrapper *,
.vxe-table--render-default.drag--area .vxe-table--fixed-left-wrapper *,
.vxe-table--render-default.drag--area .vxe-table--fixed-right-wrapper * {
  cursor: default;
}
.vxe-table--render-default.drag--extend-range .vxe-table--main-wrapper *,
.vxe-table--render-default.drag--extend-range .vxe-table--fixed-left-wrapper *,
.vxe-table--render-default.drag--extend-range .vxe-table--fixed-right-wrapper * {
  cursor: crosshair;
}
.vxe-table--render-default.col--drag-cell .vxe-header--column {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-table--render-default.col--drag-cell .vxe-header--column.is--drag-active {
  cursor: grab;
}
.vxe-table--render-default.col--drag-cell .vxe-header--column.is--drag-active:active {
  cursor: grabbing;
}
.vxe-table--render-default.col--drag-cell .vxe-header--column.is--drag-active:hover {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default.col--drag-cell .vxe-header--column.is--drag-disabled {
  color: var(--vxe-ui-input-disabled-color);
  cursor: not-allowed;
}
.vxe-table--render-default.header-cell--area .vxe-table--header-wrapper {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-table--render-default.body-cell--area .vxe-table--body-wrapper {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-table--render-default.drag--range .vxe-body--column {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-table--render-default.checkbox--range .vxe-body--column.col--checkbox {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-table--render-default .vxe-header--column,
.vxe-table--render-default .vxe-body--column,
.vxe-table--render-default .vxe-footer--column {
  position: relative;
  line-height: var(--vxe-ui-table-row-line-height);
  text-align: left;
}
.vxe-table--render-default .vxe-header--column.col--center,
.vxe-table--render-default .vxe-body--column.col--center,
.vxe-table--render-default .vxe-footer--column.col--center {
  text-align: center;
}
.vxe-table--render-default .vxe-header--column.col--center > .vxe-cell,
.vxe-table--render-default .vxe-body--column.col--center > .vxe-cell,
.vxe-table--render-default .vxe-footer--column.col--center > .vxe-cell {
  justify-content: center;
}
.vxe-table--render-default .vxe-header--column.col--right,
.vxe-table--render-default .vxe-body--column.col--right,
.vxe-table--render-default .vxe-footer--column.col--right {
  text-align: right;
}
.vxe-table--render-default .vxe-header--column.col--right > .vxe-cell,
.vxe-table--render-default .vxe-body--column.col--right > .vxe-cell,
.vxe-table--render-default .vxe-footer--column.col--right > .vxe-cell {
  justify-content: right;
}
.vxe-table--render-default .vxe-header--column.col--ellipsis.col--center .vxe-cell--wrapper {
  justify-content: center;
}
.vxe-table--render-default .vxe-header--column.col--ellipsis.col--right .vxe-cell--wrapper {
  justify-content: right;
}
.vxe-table--render-default .vxe-header--column.col--ellipsis.col--center .vxe-cell,
.vxe-table--render-default .vxe-footer--column.col--ellipsis.col--center .vxe-cell {
  justify-content: center;
}
.vxe-table--render-default .vxe-header--column.col--ellipsis.col--right .vxe-cell,
.vxe-table--render-default .vxe-footer--column.col--ellipsis.col--right .vxe-cell {
  justify-content: flex-end;
}
.vxe-table--render-default .vxe-body--row.row--stripe > .vxe-body--column {
  background-color: var(--vxe-ui-table-row-striped-background-color);
}
.vxe-table--render-default.column--highlight .vxe-header--column:hover {
  background-color: var(--vxe-ui-table-column-hover-background-color);
}
.vxe-table--render-default.column--highlight .vxe-header--column:hover.col--current {
  background-color: var(--vxe-ui-table-column-hover-current-background-color);
}
.vxe-table--render-default .vxe-header--column,
.vxe-table--render-default .vxe-body--column,
.vxe-table--render-default .vxe-footer--column {
  position: relative;
  line-height: var(--vxe-ui-table-row-line-height);
  text-align: left;
}
.vxe-table--render-default .vxe-header--column.col--current,
.vxe-table--render-default .vxe-body--column.col--current,
.vxe-table--render-default .vxe-footer--column.col--current {
  background-color: var(--vxe-ui-table-column-current-background-color);
}
.vxe-table--render-default .vxe-body--row.row--radio > .vxe-body--column {
  background-color: var(--vxe-ui-table-row-radio-checked-background-color);
}
.vxe-table--render-default .vxe-body--row.row--checked > .vxe-body--column {
  background-color: var(--vxe-ui-table-row-checkbox-checked-background-color);
}
.vxe-table--render-default .vxe-body--row.row--current > .vxe-body--column {
  background-color: var(--vxe-ui-table-row-current-background-color);
}
.vxe-table--render-default .vxe-body--row.row--hover > .vxe-body--column {
  background-color: var(--vxe-ui-table-row-hover-background-color);
}
.vxe-table--render-default .vxe-body--row.row--hover > .vxe-body--column.col--current {
  background-color: var(--vxe-ui-table-column-current-background-color);
}
.vxe-table--render-default .vxe-body--row.row--hover.row--stripe > .vxe-body--column {
  background-color: var(--vxe-ui-table-row-hover-striped-background-color);
}
.vxe-table--render-default .vxe-body--row.row--hover.row--radio > .vxe-body--column {
  background-color: var(--vxe-ui-table-row-hover-radio-checked-background-color);
}
.vxe-table--render-default .vxe-body--row.row--hover.row--checked > .vxe-body--column {
  background-color: var(--vxe-ui-table-row-hover-checkbox-checked-background-color);
}
.vxe-table--render-default .vxe-body--row.row--hover.row--current > .vxe-body--column {
  background-color: var(--vxe-ui-table-row-hover-current-background-color);
}
.vxe-table--render-default .vxe-table--footer-wrapper {
  border-top: var(--vxe-ui-table-border-width) solid var(--vxe-ui-table-border-color);
}
.vxe-table--render-default.border--default .vxe-table--scroll-y-top-corner::before,
.vxe-table--render-default.border--default .vxe-table--scroll-y-bottom-corner::before, .vxe-table--render-default.border--full .vxe-table--scroll-y-top-corner::before,
.vxe-table--render-default.border--full .vxe-table--scroll-y-bottom-corner::before, .vxe-table--render-default.border--outer .vxe-table--scroll-y-top-corner::before,
.vxe-table--render-default.border--outer .vxe-table--scroll-y-bottom-corner::before, .vxe-table--render-default.border--inner .vxe-table--scroll-y-top-corner::before,
.vxe-table--render-default.border--inner .vxe-table--scroll-y-bottom-corner::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  border-width: 0;
  border-style: solid;
  border-color: var(--vxe-ui-table-border-color);
}
.vxe-table--render-default.border--default .vxe-table--scroll-y-top-corner::before, .vxe-table--render-default.border--full .vxe-table--scroll-y-top-corner::before, .vxe-table--render-default.border--outer .vxe-table--scroll-y-top-corner::before, .vxe-table--render-default.border--inner .vxe-table--scroll-y-top-corner::before {
  border-bottom-width: var(--vxe-ui-table-border-width);
}
.vxe-table--render-default.border--default .vxe-table--scroll-y-bottom-corner, .vxe-table--render-default.border--full .vxe-table--scroll-y-bottom-corner, .vxe-table--render-default.border--outer .vxe-table--scroll-y-bottom-corner, .vxe-table--render-default.border--inner .vxe-table--scroll-y-bottom-corner {
  border-top: var(--vxe-ui-table-border-width) solid var(--vxe-ui-table-border-color);
}
.vxe-table--render-default.border--default .vxe-table--scroll-x-wrapper::after, .vxe-table--render-default.border--full .vxe-table--scroll-x-wrapper::after, .vxe-table--render-default.border--outer .vxe-table--scroll-x-wrapper::after, .vxe-table--render-default.border--inner .vxe-table--scroll-x-wrapper::after {
  content: "";
  position: absolute;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}
.vxe-table--render-default.border--default.sx-pos--top .vxe-table--scroll-x-wrapper::after, .vxe-table--render-default.border--full.sx-pos--top .vxe-table--scroll-x-wrapper::after, .vxe-table--render-default.border--outer.sx-pos--top .vxe-table--scroll-x-wrapper::after, .vxe-table--render-default.border--inner.sx-pos--top .vxe-table--scroll-x-wrapper::after {
  top: 0;
  border-bottom: var(--vxe-ui-table-border-width) solid var(--vxe-ui-table-border-color);
}
.vxe-table--render-default.border--default.sx-pos--bottom .vxe-table--scroll-x-wrapper::after, .vxe-table--render-default.border--full.sx-pos--bottom .vxe-table--scroll-x-wrapper::after, .vxe-table--render-default.border--outer.sx-pos--bottom .vxe-table--scroll-x-wrapper::after, .vxe-table--render-default.border--inner.sx-pos--bottom .vxe-table--scroll-x-wrapper::after {
  bottom: 0;
  height: calc(100% + var(--vxe-ui-table-border-width));
  border-top: var(--vxe-ui-table-border-width) solid var(--vxe-ui-table-border-color);
}
.vxe-table--render-default.border--default .vxe-cell--col-resizable:before, .vxe-table--render-default.border--default .vxe-cell--col-resizable:after, .vxe-table--render-default.border--none .vxe-cell--col-resizable:before, .vxe-table--render-default.border--none .vxe-cell--col-resizable:after, .vxe-table--render-default.border--outer .vxe-cell--col-resizable:before, .vxe-table--render-default.border--outer .vxe-cell--col-resizable:after, .vxe-table--render-default.border--inner .vxe-cell--col-resizable:before, .vxe-table--render-default.border--inner .vxe-cell--col-resizable:after {
  content: "";
  display: inline-block;
  vertical-align: middle;
}
.vxe-table--render-default.border--default .vxe-cell--col-resizable:before, .vxe-table--render-default.border--none .vxe-cell--col-resizable:before, .vxe-table--render-default.border--outer .vxe-cell--col-resizable:before, .vxe-table--render-default.border--inner .vxe-cell--col-resizable:before {
  width: 1px;
  height: 50%;
  background-color: var(--vxe-ui-table-resizable-line-color);
}
.vxe-table--render-default.border--default .vxe-cell--col-resizable:after, .vxe-table--render-default.border--none .vxe-cell--col-resizable:after, .vxe-table--render-default.border--outer .vxe-cell--col-resizable:after, .vxe-table--render-default.border--inner .vxe-cell--col-resizable:after {
  width: 0;
  height: 100%;
}
.vxe-table--render-default.border--default .vxe-table--header-wrapper, .vxe-table--render-default.border--full .vxe-table--header-wrapper, .vxe-table--render-default.border--outer .vxe-table--header-wrapper {
  background-color: var(--vxe-ui-table-header-background-color);
}
.vxe-table--render-default.border--default .vxe-header--column,
.vxe-table--render-default.border--default .vxe-body--column,
.vxe-table--render-default.border--default .vxe-footer--column, .vxe-table--render-default.border--inner .vxe-header--column,
.vxe-table--render-default.border--inner .vxe-body--column,
.vxe-table--render-default.border--inner .vxe-footer--column {
  background-image: linear-gradient(var(--vxe-ui-table-border-color), var(--vxe-ui-table-border-color));
  background-repeat: no-repeat;
  background-size: 100% var(--vxe-ui-table-border-width);
  background-position: right bottom;
}
.vxe-table--render-default.border--default .vxe-table--scroll-y-top-corner::before, .vxe-table--render-default.border--full .vxe-table--scroll-y-top-corner::before {
  border-left-width: var(--vxe-ui-table-border-width);
  border-right-width: var(--vxe-ui-table-border-width);
}
.vxe-table--render-default.border--default .vxe-table--scroll-y-bottom-corner::before, .vxe-table--render-default.border--full .vxe-table--scroll-y-bottom-corner::before {
  border-left-width: var(--vxe-ui-table-border-width);
  border-right-width: var(--vxe-ui-table-border-width);
}
.vxe-table--render-default.border--default.sy-pos--right .vxe-table--scroll-y-top-corner::before,
.vxe-table--render-default.border--default.sy-pos--right .vxe-table--scroll-y-bottom-corner::before, .vxe-table--render-default.border--full.sy-pos--right .vxe-table--scroll-y-top-corner::before,
.vxe-table--render-default.border--full.sy-pos--right .vxe-table--scroll-y-bottom-corner::before {
  width: calc(100% + 1px);
  left: -1px;
}
.vxe-table--render-default.border--default .vxe-table--scroll-y-wrapper::after, .vxe-table--render-default.border--full .vxe-table--scroll-y-wrapper::after {
  content: "";
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}
.vxe-table--render-default.border--default.sy-pos--left .vxe-table--scroll-y-wrapper::after, .vxe-table--render-default.border--full.sy-pos--left .vxe-table--scroll-y-wrapper::after {
  left: 0;
  border-right: var(--vxe-ui-table-border-width) solid var(--vxe-ui-table-border-color);
}
.vxe-table--render-default.border--default.sy-pos--right .vxe-table--scroll-y-wrapper::after, .vxe-table--render-default.border--full.sy-pos--right .vxe-table--scroll-y-wrapper::after {
  right: 0;
  width: calc(100% + var(--vxe-ui-table-border-width));
  border-left: var(--vxe-ui-table-border-width) solid var(--vxe-ui-table-border-color);
}
.vxe-table--render-default.border--full .vxe-header--column,
.vxe-table--render-default.border--full .vxe-body--column,
.vxe-table--render-default.border--full .vxe-footer--column {
  background-image: linear-gradient(var(--vxe-ui-table-border-color), var(--vxe-ui-table-border-color)), linear-gradient(var(--vxe-ui-table-border-color), var(--vxe-ui-table-border-color));
  background-repeat: no-repeat;
  background-size: var(--vxe-ui-table-border-width) 100%, 100% var(--vxe-ui-table-border-width);
  background-position: right top, right bottom;
}
.vxe-table--render-default.border--full .vxe-table--fixed-left-wrapper .vxe-body--column {
  border-right-color: var(--vxe-ui-table-border-color);
}
.vxe-table--render-default.border--inner .vxe-table--header-wrapper, .vxe-table--render-default.border--none .vxe-table--header-wrapper {
  background-color: var(--vxe-ui-table-header-background-color);
}
.vxe-table--render-default.border--inner .vxe-table--fixed-left-wrapper, .vxe-table--render-default.border--none .vxe-table--fixed-left-wrapper {
  border-right: 0;
}
.vxe-table--render-default.border--inner .vxe-table--border-line {
  border-width: 0 0 1px 0;
}
.vxe-table--render-default.border--none .vxe-table--border-line {
  display: none;
}
.vxe-table--render-default.size--medium {
  font-size: var(--vxe-ui-font-size-medium);
}
.vxe-table--render-default.size--medium .vxe-table--empty-placeholder,
.vxe-table--render-default.size--medium .vxe-table--empty-block {
  min-height: var(--vxe-ui-table-row-height-medium);
}
.vxe-table--render-default.size--medium .vxe-header--column.is--padding .vxe-cell,
.vxe-table--render-default.size--medium .vxe-body--column.is--padding .vxe-cell,
.vxe-table--render-default.size--medium .vxe-footer--column.is--padding .vxe-cell {
  padding: var(--vxe-ui-table-cell-padding-medium);
}
.vxe-table--render-default.size--medium .vxe-header--column.is--padding .vxe-cell .vxe-default-textarea,
.vxe-table--render-default.size--medium .vxe-body--column.is--padding .vxe-cell .vxe-default-textarea,
.vxe-table--render-default.size--medium .vxe-footer--column.is--padding .vxe-cell .vxe-default-textarea {
  padding: var(--vxe-ui-table-cell-padding-medium);
}
.vxe-table--render-default.size--medium .vxe-cell .vxe-default-input,
.vxe-table--render-default.size--medium .vxe-cell .vxe-default-textarea,
.vxe-table--render-default.size--medium .vxe-cell .vxe-default-select {
  height: var(--vxe-ui-input-height-medium);
}
.vxe-table--render-default.size--medium .vxe-cell .vxe-default-input[type=date]::-webkit-inner-spin-button {
  margin-top: 3px;
}
.vxe-table--render-default.size--medium .vxe-cell--valid-error-tip {
  padding: 0 var(--vxe-ui-table-cell-padding-medium);
}
.vxe-table--render-default.size--small {
  font-size: var(--vxe-ui-font-size-small);
}
.vxe-table--render-default.size--small .vxe-table--empty-placeholder,
.vxe-table--render-default.size--small .vxe-table--empty-block {
  min-height: var(--vxe-ui-table-row-height-small);
}
.vxe-table--render-default.size--small .vxe-header--column.is--padding .vxe-cell,
.vxe-table--render-default.size--small .vxe-body--column.is--padding .vxe-cell,
.vxe-table--render-default.size--small .vxe-footer--column.is--padding .vxe-cell {
  padding: var(--vxe-ui-table-cell-padding-small);
}
.vxe-table--render-default.size--small .vxe-header--column.is--padding .vxe-cell .vxe-default-textarea,
.vxe-table--render-default.size--small .vxe-body--column.is--padding .vxe-cell .vxe-default-textarea,
.vxe-table--render-default.size--small .vxe-footer--column.is--padding .vxe-cell .vxe-default-textarea {
  padding: var(--vxe-ui-table-cell-padding-small);
}
.vxe-table--render-default.size--small .vxe-cell .vxe-default-input,
.vxe-table--render-default.size--small .vxe-cell .vxe-default-textarea,
.vxe-table--render-default.size--small .vxe-cell .vxe-default-select {
  height: var(--vxe-ui-input-height-small);
}
.vxe-table--render-default.size--small .vxe-cell .vxe-default-input[type=date]::-webkit-inner-spin-button {
  margin-top: 2px;
}
.vxe-table--render-default.size--small .vxe-cell--valid-error-tip {
  padding: 0 var(--vxe-ui-table-cell-padding-small);
}
.vxe-table--render-default.size--mini {
  font-size: var(--vxe-ui-font-size-mini);
}
.vxe-table--render-default.size--mini .vxe-table--empty-placeholder,
.vxe-table--render-default.size--mini .vxe-table--empty-block {
  min-height: var(--vxe-ui-table-row-height-mini);
}
.vxe-table--render-default.size--mini .vxe-header--column.is--padding .vxe-cell,
.vxe-table--render-default.size--mini .vxe-body--column.is--padding .vxe-cell,
.vxe-table--render-default.size--mini .vxe-footer--column.is--padding .vxe-cell {
  padding: var(--vxe-ui-table-cell-padding-mini);
}
.vxe-table--render-default.size--mini .vxe-header--column.is--padding .vxe-cell .vxe-default-textarea,
.vxe-table--render-default.size--mini .vxe-body--column.is--padding .vxe-cell .vxe-default-textarea,
.vxe-table--render-default.size--mini .vxe-footer--column.is--padding .vxe-cell .vxe-default-textarea {
  padding: var(--vxe-ui-table-cell-padding-mini);
}
.vxe-table--render-default.size--mini .vxe-cell .vxe-default-input,
.vxe-table--render-default.size--mini .vxe-cell .vxe-default-textarea,
.vxe-table--render-default.size--mini .vxe-cell .vxe-default-select {
  height: var(--vxe-ui-input-height-mini);
}
.vxe-table--render-default.size--mini .vxe-cell .vxe-default-input[type=date]::-webkit-inner-spin-button {
  margin-top: 1px;
}
.vxe-table--render-default.size--mini .vxe-cell--valid-error-tip {
  padding: 0 var(--vxe-ui-table-cell-padding-mini);
}
.vxe-table--render-default .vxe-header--column.is--padding .vxe-cell,
.vxe-table--render-default .vxe-body--column.is--padding .vxe-cell,
.vxe-table--render-default .vxe-footer--column.is--padding .vxe-cell {
  padding: var(--vxe-ui-table-cell-padding-default);
}
.vxe-table--render-default .vxe-header--column.is--padding .vxe-cell .vxe-default-textarea,
.vxe-table--render-default .vxe-body--column.is--padding .vxe-cell .vxe-default-textarea,
.vxe-table--render-default .vxe-footer--column.is--padding .vxe-cell .vxe-default-textarea {
  padding: var(--vxe-ui-table-cell-padding-default);
}
.vxe-table--render-default .vxe-cell {
  white-space: pre-line;
  word-break: break-all;
}
.vxe-table--render-default .vxe-cell--placeholder {
  color: var(--vxe-ui-table-cell-placeholder-color);
}
.vxe-table--render-default .vxe-cell--radio {
  cursor: pointer;
}
.vxe-table--render-default .vxe-cell--radio .vxe-radio--icon {
  font-size: 1.4em;
}
.vxe-table--render-default .vxe-cell--radio .vxe-radio--icon {
  color: var(--vxe-ui-input-border-color);
  vertical-align: middle;
  font-weight: 700;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-table--render-default .vxe-cell--radio.is--checked {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-cell--radio.is--checked .vxe-radio--icon {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-cell--radio:not(.is--disabled) {
  cursor: pointer;
}
.vxe-table--render-default .vxe-cell--radio:not(.is--disabled):hover .vxe-radio--icon {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-cell--radio.is--disabled {
  color: var(--vxe-ui-font-disabled-color);
  cursor: not-allowed;
}
.vxe-table--render-default .vxe-cell--radio.is--disabled .vxe-radio--icon {
  color: var(--vxe-ui-input-disabled-color);
}
.vxe-table--render-default .vxe-cell--radio .vxe-radio--label {
  padding-left: 0.5em;
  vertical-align: middle;
}
.vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon {
  font-size: 1.34em;
}
.vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon {
  color: var(--vxe-ui-input-border-color);
  vertical-align: middle;
  font-weight: 700;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-table--render-default .vxe-cell--checkbox.is--checked, .vxe-table--render-default .vxe-cell--checkbox.is--indeterminate {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-cell--checkbox.is--checked .vxe-checkbox--icon, .vxe-table--render-default .vxe-cell--checkbox.is--indeterminate .vxe-checkbox--icon {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-cell--checkbox:not(.is--disabled) {
  cursor: pointer;
}
.vxe-table--render-default .vxe-cell--checkbox:not(.is--disabled):hover .vxe-checkbox--icon {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-cell--checkbox.is--hidden {
  cursor: default;
}
.vxe-table--render-default .vxe-cell--checkbox.is--disabled {
  color: var(--vxe-ui-font-disabled-color);
  cursor: not-allowed;
}
.vxe-table--render-default .vxe-cell--checkbox.is--disabled .vxe-checkbox--icon {
  color: var(--vxe-ui-input-disabled-color);
}
.vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--label {
  padding-left: 0.5em;
  vertical-align: middle;
}
.vxe-table--render-default .fixed--hidden {
  visibility: hidden;
}
.vxe-table--render-default .vxe-table--fixed-left-wrapper,
.vxe-table--render-default .vxe-table--fixed-right-wrapper {
  width: 100%;
  position: absolute;
  top: 0;
  z-index: 5;
  overflow: hidden;
  background-color: inherit;
  transition: 0.3s box-shadow;
  outline: 0;
  background-color: var(--vxe-ui-layout-background-color);
}
.vxe-table--render-default .vxe-table--fixed-left-wrapper .vxe-table--body-wrapper,
.vxe-table--render-default .vxe-table--fixed-right-wrapper .vxe-table--body-wrapper {
  outline: 0;
}
.vxe-table--render-default .vxe-table--fixed-left-wrapper .vxe-table--body-wrapper {
  width: calc(100% + 40px);
}
.vxe-table--render-default.is--header .vxe-table--fixed-left-wrapper .vxe-table--body-wrapper:before,
.vxe-table--render-default.is--header .vxe-table--fixed-right-wrapper .vxe-table--body-wrapper:before {
  display: none;
}
.vxe-table--render-default .vxe-table--fixed-left-wrapper {
  left: 0;
  width: 200px;
}
.vxe-table--render-default .vxe-table--fixed-left-wrapper.scrolling--middle {
  box-shadow: var(--vxe-ui-table-fixed-left-scrolling-box-shadow);
}
.vxe-table--render-default .vxe-table--fixed-right-wrapper {
  right: 0;
}
.vxe-table--render-default .vxe-table--fixed-right-wrapper.scrolling--middle {
  box-shadow: var(--vxe-ui-table-fixed-right-scrolling-box-shadow);
}
.vxe-table--render-default .vxe-table--header-wrapper,
.vxe-table--render-default .vxe-table--body-wrapper,
.vxe-table--render-default .vxe-table--footer-wrapper {
  position: relative;
  width: 100%;
}
.vxe-table--render-default .vxe-table--header-wrapper.fixed-left--wrapper, .vxe-table--render-default .vxe-table--header-wrapper.fixed-right--wrapper,
.vxe-table--render-default .vxe-table--body-wrapper.fixed-left--wrapper,
.vxe-table--render-default .vxe-table--body-wrapper.fixed-right--wrapper,
.vxe-table--render-default .vxe-table--footer-wrapper.fixed-left--wrapper,
.vxe-table--render-default .vxe-table--footer-wrapper.fixed-right--wrapper {
  position: absolute;
  top: 0;
  outline: 0;
}
.vxe-table--render-default .vxe-table--header-wrapper.fixed-left--wrapper,
.vxe-table--render-default .vxe-table--body-wrapper.fixed-left--wrapper,
.vxe-table--render-default .vxe-table--footer-wrapper.fixed-left--wrapper {
  left: 0;
}
.vxe-table--render-default .vxe-table--header-wrapper.fixed-right--wrapper,
.vxe-table--render-default .vxe-table--body-wrapper.fixed-right--wrapper,
.vxe-table--render-default .vxe-table--footer-wrapper.fixed-right--wrapper {
  right: 0;
}
.vxe-table--render-default .vxe-body--x-space {
  width: 100%;
  height: 1px;
  margin-bottom: -1px;
}
.vxe-table--render-default .vxe-body--y-space {
  width: 0;
  float: left;
}
.vxe-table--render-default .vxe-table--resizable-row-bar,
.vxe-table--render-default .vxe-table--resizable-col-bar {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9;
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-table--render-default .vxe-table--resizable-row-bar:before,
.vxe-table--render-default .vxe-table--resizable-col-bar:before {
  content: "";
  display: block;
  background-color: var(--vxe-ui-table-resizable-drag-line-color);
}
.vxe-table--render-default .vxe-table--resizable-row-bar .vxe-table--resizable-number-tip,
.vxe-table--render-default .vxe-table--resizable-col-bar .vxe-table--resizable-number-tip {
  position: absolute;
  padding: 0.25em 0.25em;
  font-size: 12px;
  border-radius: var(--vxe-ui-border-radius);
  white-space: nowrap;
  color: #ffffff;
  background-color: var(--vxe-ui-table-resizable-drag-line-color);
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  pointer-events: none;
}
.vxe-table--render-default .vxe-table--resizable-col-bar {
  width: 1px;
  height: 100%;
  cursor: col-resize;
}
.vxe-table--render-default .vxe-table--resizable-col-bar:before {
  width: 1px;
  height: 100%;
}
.vxe-table--render-default .vxe-table--resizable-col-bar .vxe-table--resizable-number-tip {
  left: 0;
  top: 1em;
}
.vxe-table--render-default .vxe-table--resizable-row-bar {
  height: 1px;
  width: 100%;
  cursor: row-resize;
}
.vxe-table--render-default .vxe-table--resizable-row-bar:before {
  height: 1px;
  width: 100%;
}
.vxe-table--render-default .vxe-table--resizable-row-bar .vxe-table--resizable-number-tip {
  top: 0;
  left: 0;
}
.vxe-table--render-default .vxe-table--border-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  pointer-events: none;
  border: var(--vxe-ui-table-border-width) solid var(--vxe-ui-table-border-color);
}
.vxe-table--render-default .vxe-tree--line-wrapper {
  position: relative;
  display: block;
  height: 0;
}
.vxe-table--render-default .vxe-tree--line {
  content: "";
  position: absolute;
  bottom: -1.5em;
  width: 0.8em;
  border-width: 0 0 1px 1px;
  border-style: var(--vxe-ui-table-tree-node-line-style);
  border-color: var(--vxe-ui-table-tree-node-line-color);
  pointer-events: none;
}
.vxe-table--render-default .vxe-row-group--tree-node,
.vxe-table--render-default .vxe-cell--tree-node {
  position: relative;
}
.vxe-table--render-default .vxe-cell--tree-btn:hover {
  color: var(--vxe-ui-font-color);
}
.vxe-table--render-default .vxe-cell--tree-btn > i {
  display: block;
  color: var(--vxe-ui-font-lighten-color);
  transition: transform 0.1s ease-in-out;
}
.vxe-table--render-default .vxe-row-group-cell,
.vxe-table--render-default .vxe-tree-cell {
  display: block;
  padding-left: 1.5em;
}
.vxe-table--render-default .vxe-cell--tree-btn,
.vxe-table--render-default .vxe-row-group--node-btn {
  position: absolute;
  top: 50%;
  width: 1em;
  height: 1em;
  text-align: center;
  transform: translateY(-50%);
  z-index: 1;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  cursor: pointer;
}
.vxe-table--render-default .vxe-row-group--node-btn:hover {
  color: var(--vxe-ui-font-color);
}
.vxe-table--render-default .vxe-row-group--node-btn > i {
  display: block;
  color: var(--vxe-ui-font-lighten-color);
  transition: transform 0.1s ease-in-out;
}
.vxe-table--render-default .vxe-body--column.col--ellipsis > .vxe-cell .vxe-row-group-cell,
.vxe-table--render-default .vxe-body--column.col--ellipsis > .vxe-cell .vxe-tree-cell {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.vxe-table--render-default .vxe-body--column.col--cs-height, .vxe-table--render-default .vxe-body--column.col--rs-height, .vxe-table--render-default .vxe-body--column.col--auto-height {
  overflow: hidden;
}
.vxe-table--render-default .vxe-body--column.col--cs-height.col--tree-node, .vxe-table--render-default .vxe-body--column.col--cs-height.col--valid-error, .vxe-table--render-default .vxe-body--column.col--rs-height.col--tree-node, .vxe-table--render-default .vxe-body--column.col--rs-height.col--valid-error, .vxe-table--render-default .vxe-body--column.col--auto-height.col--tree-node, .vxe-table--render-default .vxe-body--column.col--auto-height.col--valid-error {
  overflow: unset;
}
.vxe-table--render-default .vxe-body--column.col--cs-height > .vxe-cell, .vxe-table--render-default .vxe-body--column.col--rs-height > .vxe-cell, .vxe-table--render-default .vxe-body--column.col--auto-height > .vxe-cell {
  overflow: hidden;
}
.vxe-table--render-default .vxe-body--column > .vxe-cell {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.vxe-table--render-default .vxe-header--column:not(.col--active).col--ellipsis > .vxe-cell,
.vxe-table--render-default .vxe-body--column:not(.col--active).col--ellipsis > .vxe-cell,
.vxe-table--render-default .vxe-footer--column:not(.col--active).col--ellipsis > .vxe-cell {
  overflow: hidden;
}
.vxe-table--render-default .vxe-header--column:not(.col--active).col--ellipsis > .vxe-cell > .vxe-cell--wrapper,
.vxe-table--render-default .vxe-body--column:not(.col--active).col--ellipsis > .vxe-cell > .vxe-cell--wrapper,
.vxe-table--render-default .vxe-footer--column:not(.col--active).col--ellipsis > .vxe-cell > .vxe-cell--wrapper {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.vxe-table--render-default .vxe-header--column:not(.col--active).vxe-row-group-cell > .vxe-cell > .vxe-cell--wrapper, .vxe-table--render-default .vxe-header--column:not(.col--active).vxe-tree-cell > .vxe-cell > .vxe-cell--wrapper, .vxe-table--render-default .vxe-header--column:not(.col--active).col--vertical-top > .vxe-cell > .vxe-cell--wrapper,
.vxe-table--render-default .vxe-body--column:not(.col--active).vxe-row-group-cell > .vxe-cell > .vxe-cell--wrapper,
.vxe-table--render-default .vxe-body--column:not(.col--active).vxe-tree-cell > .vxe-cell > .vxe-cell--wrapper,
.vxe-table--render-default .vxe-body--column:not(.col--active).col--vertical-top > .vxe-cell > .vxe-cell--wrapper,
.vxe-table--render-default .vxe-footer--column:not(.col--active).vxe-row-group-cell > .vxe-cell > .vxe-cell--wrapper,
.vxe-table--render-default .vxe-footer--column:not(.col--active).vxe-tree-cell > .vxe-cell > .vxe-cell--wrapper,
.vxe-table--render-default .vxe-footer--column:not(.col--active).col--vertical-top > .vxe-cell > .vxe-cell--wrapper {
  white-space: pre-line;
}
.vxe-table--render-default .vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--wrapper,
.vxe-table--render-default .vxe-footer--column.col--ellipsis > .vxe-cell .vxe-cell--wrapper {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.vxe-table--render-default .vxe-header--column > .vxe-cell,
.vxe-table--render-default .vxe-footer--column > .vxe-cell {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.vxe-table--render-default .vxe-table--row-expanded-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}
.vxe-table--render-default .vxe-body--row-expanded-cell {
  position: absolute;
  z-index: 5;
  top: 0;
  left: 0;
  width: 100%;
  overflow: auto;
  background-color: var(--vxe-ui-layout-background-color);
  pointer-events: all;
}
.vxe-table--render-default .vxe-body--row-expanded-cell.is--padding {
  padding: var(--vxe-ui-table-expand-padding-default);
}
.vxe-table--render-default .vxe-body--row-expanded-place-column {
  border-bottom: var(--vxe-ui-table-border-width) solid var(--vxe-ui-table-border-color);
  border-right: var(--vxe-ui-table-border-width) solid var(--vxe-ui-table-border-color);
}
.vxe-table--render-default .vxe-table--expanded {
  cursor: pointer;
}
.vxe-table--render-default .vxe-table--expanded .vxe-table--expand-btn {
  display: inline-block;
  width: 1em;
  height: 1em;
  text-align: center;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  color: var(--vxe-ui-font-lighten-color);
  transition: transform 0.1s ease-in-out;
}
.vxe-table--render-default .vxe-table--expanded .vxe-table--expand-btn:hover {
  color: var(--vxe-ui-font-color);
}
.vxe-table--render-default .vxe-table--expanded + .vxe-table--expand-label {
  padding-left: 0.5em;
}
.vxe-table--render-default .vxe-body--expanded-row.is--padding > .vxe-body--expanded-column > .vxe-body--expanded-cell {
  padding: var(--vxe-ui-table-expand-padding-default);
}
.vxe-table--render-default .vxe-body--expanded-column {
  border-bottom: var(--vxe-ui-table-border-width) solid var(--vxe-ui-table-border-color);
  border-right: var(--vxe-ui-table-border-width) solid var(--vxe-ui-table-border-color);
}
.vxe-table--render-default .vxe-body--expanded-column.col--ellipsis > .vxe-body--expanded-cell {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.vxe-table--render-default .vxe-body--expanded-cell {
  position: relative;
  z-index: 1;
}
.vxe-table--render-default .vxe-body--expanded-cell.is--ellipsis {
  overflow: auto;
  outline: 0;
}
.vxe-table--render-default .vxe-table--drag-col-line {
  height: 100%;
  width: 1px;
  border: 2px solid transparent;
}
.vxe-table--render-default .vxe-table--drag-col-line[drag-pos=left] {
  border-left-color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-table--drag-col-line[drag-pos=right] {
  border-right-color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-table--drag-col-line[drag-to-child=y] {
  border-left-color: transparent;
  border-right-color: transparent;
  border-bottom-color: var(--vxe-ui-status-success-color);
}
.vxe-table--render-default .vxe-table--drag-col-line.is--guides {
  background-color: var(--vxe-ui-table-drag-over-background-color);
}
.vxe-table--render-default .vxe-header--column.col--drag-origin > .vxe-cell,
.vxe-table--render-default .vxe-body--column.col--drag-origin > .vxe-cell,
.vxe-table--render-default .vxe-footer--column.col--drag-origin > .vxe-cell {
  opacity: 0.5;
}
.vxe-table--render-default .vxe-header--col-list-move {
  transition-property: transform;
  transition-duration: 0.35s;
}
.vxe-table--render-default .vxe-table--drag-col-line,
.vxe-table--render-default .vxe-table--drag-row-line {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 11;
  pointer-events: none;
}
.vxe-table--render-default .vxe-cell--drag-handle {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-table--render-default .vxe-cell--drag-handle + span {
  padding-left: 0.5em;
}
.vxe-table--render-default .vxe-cell--drag-handle:not(.is--disabled) {
  cursor: grab;
}
.vxe-table--render-default .vxe-cell--drag-handle:not(.is--disabled):active {
  cursor: grabbing;
}
.vxe-table--render-default .vxe-cell--drag-handle:not(.is--disabled):hover {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-cell--drag-handle.is--disabled {
  color: var(--vxe-ui-input-disabled-color);
  cursor: not-allowed;
}
.vxe-table--render-default .vxe-table--drag-row-line {
  width: 100%;
  height: 1px;
  border: 2px solid transparent;
}
.vxe-table--render-default .vxe-table--drag-row-line[drag-pos=top] {
  border-top-color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-table--drag-row-line[drag-pos=bottom] {
  border-bottom-color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-table--drag-row-line[drag-to-child=y] {
  border-top-color: transparent;
  border-bottom-color: transparent;
  border-left-color: var(--vxe-ui-status-success-color);
}
.vxe-table--render-default .vxe-table--drag-row-line.is--guides {
  background-color: var(--vxe-ui-table-drag-over-background-color);
}
.vxe-table--render-default .vxe-body--row.row--drag-origin > .vxe-body--column > .vxe-cell {
  opacity: 0.5;
}
.vxe-table--render-default .vxe-body--column.is--drag-cell {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.vxe-table--render-default .vxe-body--column.is--drag-cell:not(.is--drag-disabled) {
  cursor: grab;
}
.vxe-table--render-default .vxe-body--column.is--drag-cell:not(.is--drag-disabled):active {
  cursor: grabbing;
}
.vxe-table--render-default .vxe-body--column.is--drag-cell:not(.is--drag-disabled):hover {
  color: var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-body--column.is--drag-cell.is--drag-disabled {
  color: var(--vxe-ui-input-disabled-color);
  cursor: not-allowed;
}
.vxe-table--render-default .vxe-body--row-list-move {
  transition-property: transform;
  transition-duration: 0.35s;
}
.vxe-table--render-default .vxe-table--drag-sort-tip {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  padding: 0.6em 1.4em;
  max-width: 50%;
  min-width: 100px;
  border-radius: var(--vxe-ui-border-radius);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  pointer-events: none;
  background-color: var(--vxe-ui-layout-background-color);
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
  z-index: 33;
}
.vxe-table--render-default .vxe-table--drag-sort-tip[drag-status=normal] .vxe-table--drag-sort-tip-normal-status {
  display: block;
}
.vxe-table--render-default .vxe-table--drag-sort-tip[drag-status=sub] .vxe-table--drag-sort-tip-sub-status {
  display: block;
}
.vxe-table--render-default .vxe-table--drag-sort-tip[drag-status=disabled] .vxe-table--drag-sort-tip-disabled-status {
  display: block;
}
.vxe-table--render-default .vxe-table--drag-sort-tip-wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.vxe-table--render-default .vxe-table--drag-sort-tip-status {
  padding-right: 0.4em;
}
.vxe-table--render-default .vxe-table--drag-sort-tip-disabled-status {
  display: none;
  flex-shrink: 0;
  color: var(--vxe-ui-status-error-color);
}
.vxe-table--render-default .vxe-table--drag-sort-tip-normal-status,
.vxe-table--render-default .vxe-table--drag-sort-tip-sub-status {
  display: none;
}
.vxe-table--render-default .vxe-table--drag-sort-tip-content {
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.vxe-table--render-default.size--medium .vxe-cell--checkbox {
  font-size: var(--vxe-checkbox-font-size-medium);
}
.vxe-table--render-default.size--medium .vxe-cell--radio {
  font-size: var(--vxe-radio-font-size-medium);
}
.vxe-table--render-default.size--small .vxe-cell--checkbox {
  font-size: var(--vxe-checkbox-font-size-small);
}
.vxe-table--render-default.size--small .vxe-cell--radio {
  font-size: var(--vxe-radio-font-size-small);
}
.vxe-table--render-default.size--mini .vxe-cell--checkbox {
  font-size: var(--vxe-checkbox-font-size-mini);
}
.vxe-table--render-default.size--mini .vxe-cell--radio {
  font-size: var(--vxe-radio-font-size-mini);
}
.vxe-table--render-default .vxe-table--empty-placeholder,
.vxe-table--render-default .vxe-table--empty-block {
  color: var(--vxe-ui-input-placeholder-color);
  min-height: var(--vxe-ui-table-row-height-default);
  justify-content: center;
  align-items: center;
  text-align: center;
  overflow: hidden;
  width: 100%;
  pointer-events: none;
  outline: 0;
}
.vxe-table--render-default .vxe-table--empty-block {
  display: none;
  visibility: hidden;
}
.vxe-table--render-default .vxe-table--empty-placeholder {
  display: none;
  position: absolute;
  top: 0;
  z-index: 5;
}
.vxe-table--render-default .vxe-table--empty-content {
  display: block;
  width: 50%;
  pointer-events: auto;
}
.vxe-table--render-default.is--empty .vxe-table--empty-block,
.vxe-table--render-default.is--empty .vxe-table--empty-placeholder {
  display: flex;
}
.vxe-table--render-default .vxe-body--column.col--selected {
  box-shadow: inset 0px 0px 0px 2px var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default .vxe-body--column .vxe-cell--valid-error-tip {
  width: 100%;
  position: absolute;
  left: 50%;
  font-size: 12px;
  line-height: 1.2em;
  transform: translateX(-50%);
  text-align: left;
  z-index: 4;
  padding: 0 var(--vxe-ui-table-cell-padding-default);
}
.vxe-table--render-default .vxe-body--column .vxe-cell--valid-error-wrapper {
  display: inline-block;
  border-radius: var(--vxe-ui-border-radius);
  pointer-events: auto;
}
.vxe-table--render-default .vxe-body--column .vxe-cell--valid-error-theme-beautify {
  padding: 0.2em 0.6em 0.3em 0.6em;
  color: #fff;
  background-color: var(--vxe-ui-table-validate-error-color);
}
.vxe-table--render-default .vxe-body--column .vxe-cell--valid-error-theme-beautify .vxe-cell--valid-error-msg {
  background: transparent;
}
.vxe-table--render-default .vxe-body--column .vxe-cell--valid-error-theme-normal {
  color: var(--vxe-ui-table-validate-error-color);
  background-color: var(--vxe-ui-table-validate-error-background-color);
}
.vxe-table--render-default .vxe-body--column.col--active, .vxe-table--render-default .vxe-body--column.col--selected {
  position: relative;
}
.vxe-table--render-default .vxe-body--column.col--valid-error .vxe-default-input,
.vxe-table--render-default .vxe-body--column.col--valid-error .vxe-default-textarea,
.vxe-table--render-default .vxe-body--column.col--valid-error .vxe-default-select {
  border-color: var(--vxe-ui-table-validate-error-color);
}
.vxe-table--render-default .vxe-body--column.col--valid-error .vxe-input,
.vxe-table--render-default .vxe-body--column.col--valid-error .vxe-ico-picker {
  border-color: var(--vxe-ui-table-validate-error-color);
}
.vxe-table--render-default.valid-msg--single .vxe-body--row:last-child .vxe-cell--valid-error-tip {
  bottom: 100%;
}
.vxe-table--render-default.valid-msg--single .vxe-body--row:last-child:first-child .vxe-cell--valid-error-tip {
  bottom: auto;
}
.vxe-table--render-default.valid-msg--full .vxe-body--row:last-child .vxe-cell--valid-error-tip {
  top: calc(100% - 1.3em);
}
.vxe-table--render-default.old-cell-valid .vxe-body--column.col--valid-error .vxe-cell--valid-error-tip {
  width: 320px;
  position: absolute;
  bottom: calc(100% + 4px);
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  pointer-events: none;
  z-index: 4;
}
.vxe-table--render-default.old-cell-valid .vxe-body--column.col--valid-error .vxe-cell--valid-error-tip .vxe-cell--valid-error-msg {
  display: inline-block;
  border-radius: -var(-vxe-border-radius);
  padding: 8px 12px;
  color: #fff;
  background-color: #f56c6c;
  pointer-events: auto;
}
.vxe-table--render-default.old-cell-valid .vxe-body--row:first-child .vxe-cell--valid-error-tip {
  bottom: auto;
  top: calc(100% + 4px);
}
.vxe-table--render-default.old-cell-valid .vxe-body--column:first-child .vxe-cell--valid-error-tip {
  left: 10px;
  transform: translateX(0);
  text-align: left;
}
.vxe-table--render-default .vxe-body--row.row--pending {
  color: var(--vxe-ui-table-validate-error-color);
  text-decoration: line-through;
  cursor: no-drop;
}
.vxe-table--render-default .vxe-body--row.row--pending .vxe-body--column {
  position: relative;
}
.vxe-table--render-default .vxe-body--row.row--pending .vxe-body--column:after {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 0;
  border-bottom: 1px solid var(--vxe-ui-table-validate-error-color);
  z-index: 1;
}
.vxe-table--render-default .vxe-body--row.row--new > .vxe-body--column {
  position: relative;
}
.vxe-table--render-default .vxe-body--row.row--new > .vxe-body--column:before {
  content: "";
  top: calc(var(--vxe-ui-table-cell-dirty-width) * -1);
  left: calc(var(--vxe-ui-table-cell-dirty-width) * -1);
  position: absolute;
  border-width: var(--vxe-ui-table-cell-dirty-width);
  border-style: solid;
  border-color: transparent var(--vxe-ui-table-cell-dirty-insert-color) transparent transparent;
  transform: rotate(45deg);
}
.vxe-table--render-default .vxe-body--column.col--dirty {
  position: relative;
}
.vxe-table--render-default .vxe-body--column.col--dirty:before {
  content: "";
  top: calc(var(--vxe-ui-table-cell-dirty-width) * -1);
  left: calc(var(--vxe-ui-table-cell-dirty-width) * -1);
  position: absolute;
  border-width: var(--vxe-ui-table-cell-dirty-width);
  border-style: solid;
  border-color: transparent var(--vxe-ui-table-cell-dirty-update-color) transparent transparent;
  transform: rotate(45deg);
}
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--active {
  box-shadow: inset 0px 0px 0px 2px var(--vxe-ui-font-primary-color);
}
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--active.col--valid-error {
  box-shadow: inset 0px 0px 0px 2px var(--vxe-ui-table-validate-error-color);
}
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--active .vxe-cell .vxe-default-input,
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--active .vxe-cell .vxe-default-textarea {
  border: 0;
  padding: 0;
}
.vxe-table--render-default.vxe-editable.cell--highlight .vxe-body--column.col--active .vxe-cell .vxe-input .vxe-input--inner {
  border: 0;
  padding-left: 0;
}
.vxe-table--render-default.vxe-editable .vxe-body--column {
  padding: 0;
}

/*valid error*/
div.vxe-table--tooltip-wrapper.vxe-table--valid-error {
  padding: 0;
  color: var(--vxe-ui-table-validate-error-color);
  background-color: var(--vxe-ui-table-validate-error-background-color);
}
div.vxe-table--tooltip-wrapper.vxe-table--valid-error.old-cell-valid {
  padding: 8px 12px;
  background-color: #f56c6c;
  color: #fff;
}

/*footer*/
.vxe-table--footer-wrapper {
  color: var(--vxe-ui-table-footer-font-color);
}
.vxe-table--footer-wrapper.body--wrapper {
  outline: 0;
}