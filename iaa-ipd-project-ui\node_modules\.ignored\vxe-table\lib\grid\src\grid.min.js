Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var _vue=require("vue"),_comp=require("../../ui/src/comp"),_xeUtils=_interopRequireDefault(require("xe-utils")),_utils=require("../../ui/src/utils"),_dom=require("../../ui/src/dom"),_ui=require("../../ui"),_table=_interopRequireDefault(require("../../table/src/table")),_toolbar=_interopRequireDefault(require("../../toolbar/src/toolbar")),_props=_interopRequireDefault(require("../../table/src/props")),_emits=_interopRequireDefault(require("../../table/src/emits")),_vn=require("../../ui/src/vn"),_log=require("../../ui/src/log");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let{getConfig,getI18n,commands,hooks,useFns,createEvent,globalEvents,GLOBAL_EVENT_KEYS,renderEmptyElement}=_ui.VxeUI,tableComponentPropKeys=Object.keys(_props.default),tableComponentMethodKeys=["clearAll","syncData","updateData","loadData","reloadData","reloadRow","loadColumn","reloadColumn","getRowNode","getColumnNode","getRowIndex","getVTRowIndex","getVMRowIndex","getColumnIndex","getVTColumnIndex","getVMColumnIndex","setRow","createData","createRow","revertData","clearData","isRemoveByRow","isInsertByRow","isUpdateByRow","getColumns","getColumnById","getColumnByField","getTableColumn","getFullColumns","getData","getCheckboxRecords","getParentRow","getTreeRowChildren","getTreeParentRow","getRowSeq","getRowById","getRowid","getTableData","getFullData","setColumnFixed","clearColumnFixed","setColumnWidth","getColumnWidth","setRowHeightConf","getRowHeightConf","setRowHeight","getRowHeight","hideColumn","showColumn","resetColumn","refreshColumn","refreshScroll","recalculate","closeTooltip","isAllCheckboxChecked","isAllCheckboxIndeterminate","getCheckboxIndeterminateRecords","setCheckboxRow","setCheckboxRowKey","isCheckedByCheckboxRow","isCheckedByCheckboxRowKey","isIndeterminateByCheckboxRow","isIndeterminateByCheckboxRowKey","toggleCheckboxRow","setAllCheckboxRow","getRadioReserveRecord","clearRadioReserve","getCheckboxReserveRecords","clearCheckboxReserve","toggleAllCheckboxRow","clearCheckboxRow","setCurrentRow","isCheckedByRadioRow","isCheckedByRadioRowKey","setRadioRow","setRadioRowKey","clearCurrentRow","clearRadioRow","getCurrentRecord","getRadioRecord","getCurrentColumn","setCurrentColumn","clearCurrentColumn","setPendingRow","togglePendingRow","hasPendingByRow","isPendingByRow","getPendingRecords","clearPendingRow","sort","setSort","clearSort","clearSortByEvent","isSort","getSortColumns","closeFilter","isFilter","clearFilterByEvent","isActiveFilterByColumn","isRowExpandLoaded","clearRowExpandLoaded","reloadRowExpand","reloadRowExpand","toggleRowExpand","setAllRowExpand","setRowExpand","isExpandByRow","isRowExpandByRow","clearRowExpand","clearRowExpandReserve","getRowExpandRecords","getTreeExpandRecords","isTreeExpandLoaded","clearTreeExpandLoaded","reloadTreeExpand","reloadTreeChilds","toggleTreeExpand","setAllTreeExpand","setTreeExpand","isTreeExpandByRow","clearTreeExpand","clearTreeExpandReserve","getScroll","scrollTo","scrollToRow","scrollToColumn","clearScroll","updateFooter","updateStatus","setMergeCells","removeInsertRow","removeMergeCells","getMergeCells","clearMergeCells","setMergeFooterItems","removeMergeFooterItems","getMergeFooterItems","clearMergeFooterItems","getCustomStoreData","setRowGroupExpand","setAllRowGroupExpand","clearRowGroupExpand","isRowGroupExpandByRow","isRowGroupRecord","isAggregateRecord","isAggregateExpandByRow","setRowGroups","clearRowGroups","openTooltip","moveColumnTo","moveRowTo","getCellLabel","getCellElement","focus","blur","connect"],gridComponentEmits=[..._emits.default,"page-change","form-submit","form-submit-invalid","form-reset","form-collapse","form-toggle-collapse","proxy-query","proxy-delete","proxy-save","toolbar-button-click","toolbar-tool-click","zoom"];var _default=exports.default=(0,_comp.defineVxeComponent)({name:"VxeGrid",props:Object.assign(Object.assign({},_props.default),{layouts:Array,columns:Array,pagerConfig:Object,proxyConfig:Object,toolbarConfig:Object,formConfig:Object,zoomConfig:Object,size:{type:String,default:()=>getConfig().grid.size||getConfig().size}}),emits:gridComponentEmits,setup(S,e){let t,{slots:u,emit:r}=e;var E=_xeUtils.default.uniqueId();let l=_ui.VxeUI.getComponent("VxeForm"),i=_ui.VxeUI.getComponent("VxePager"),w=[["Form"],["Toolbar","Top","Table","Bottom","Pager"]],O=useFns.useSize(S).computeSize,T=(0,_vue.reactive)({tableLoading:!1,proxyInited:!1,isZMax:!1,tableData:[],filterData:[],formData:{},sortData:[],tZindex:0,tablePage:{total:0,pageSize:(null==(t=getConfig().pager)?void 0:t.pageSize)||10,currentPage:1}}),s=(0,_vue.ref)(),j=(0,_vue.ref)(),n=(0,_vue.ref)(),d=(0,_vue.ref)(),g=(0,_vue.ref)(),c=(0,_vue.ref)(),m=(0,_vue.ref)(),v=(0,_vue.ref)(),p=(0,_vue.ref)(),f=(0,_vue.ref)();var o=e=>{let t={};return e.forEach(o=>{t[o]=(...e)=>{var t=j.value;if(t&&t[o])return t[o](...e)}}),t};let D=o(tableComponentMethodKeys),q=(tableComponentMethodKeys.forEach(o=>{D[o]=(...e)=>{var t=j.value;if(t&&t[o])return t&&t[o](...e)}}),(0,_vue.computed)(()=>_xeUtils.default.merge({},_xeUtils.default.clone(getConfig().grid.proxyConfig,!0),S.proxyConfig))),Z=(0,_vue.computed)(()=>{var e=q.value;return _xeUtils.default.isBoolean(e.message)?e.message:e.showResponseMsg}),M=(0,_vue.computed)(()=>q.value.showActiveMsg),F=(0,_vue.computed)(()=>Object.assign({},getConfig().grid.pagerConfig,S.pagerConfig)),x=(0,_vue.computed)(()=>Object.assign({},getConfig().grid.formConfig,S.formConfig)),L=(0,_vue.computed)(()=>Object.assign({},getConfig().grid.toolbarConfig,S.toolbarConfig)),a=(0,_vue.computed)(()=>Object.assign({},getConfig().grid.zoomConfig,S.zoomConfig)),I=(0,_vue.computed)(()=>{var{height:e,maxHeight:t}=S,{isZMax:o,tZindex:r}=T,a={};return o?a.zIndex=r:(e&&(a.height="auto"===e||"100%"===e?"100%":(0,_dom.toCssUnit)(e)),t&&(a.maxHeight="auto"===t||"100%"===t?"100%":(0,_dom.toCssUnit)(t))),a}),P=(0,_vue.computed)(()=>{let t={},o=S;return tableComponentPropKeys.forEach(e=>{t[e]=o[e]}),t}),k=(0,_vue.computed)(()=>{var{seqConfig:e,pagerConfig:t,loading:o,editConfig:r,proxyConfig:a}=S,{isZMax:i,tableLoading:l,tablePage:n}=T,s=P.value,u=q.value,d=F.value,g=Object.assign({},s);return i&&(s.maxHeight?g.maxHeight="100%":g.height="100%"),a&&(0,_utils.isEnableConf)(u)&&(g.loading=o||l,t)&&u.seq&&(0,_utils.isEnableConf)(d)&&(g.seqConfig=Object.assign({},e,{startIndex:(n.currentPage-1)*n.pageSize})),r&&(g.editConfig=Object.assign({},r)),g}),U=(0,_vue.computed)(()=>{var e=S.layouts;let t=[],o=[],r=[],a=[];return(t=e&&e.length?e:getConfig().grid.layouts||w).length&&(_xeUtils.default.isArray(t[0])?(o=t[0],r=t[1]||[],a=t[2]||[]):r=t),{headKeys:o,bodyKeys:r,footKeys:a}});var A=(0,_vue.computed)(()=>F.value.currentPage),K=(0,_vue.computed)(()=>F.value.pageSize),H=(0,_vue.computed)(()=>F.value.total);let G={refElem:s,refTable:j,refForm:n,refToolbar:d,refPager:g},N={computeProxyOpts:q,computePagerOpts:F,computeFormOpts:x,computeToolbarOpts:L,computeZoomOpts:a},V={xID:E,props:S,context:e,reactData:T,getRefMaps:()=>G,getComputeMaps:()=>N},b=()=>{var e=L.value;S.toolbarConfig&&(0,_utils.isEnableConf)(e)&&(0,_vue.nextTick)(()=>{var e=j.value,t=d.value;e&&t&&e.connect(t)})},z=()=>{var e=S.proxyConfig,t=T.formData,o=q.value,r=x.value;return e&&(0,_utils.isEnableConf)(o)&&o.form?t:r.data},_=e=>{var t=T.tablePage,o=S.pagerConfig,r=F.value;o&&(0,_utils.isEnableConf)(r)&&(e?r[e]&&(t[e]=_xeUtils.default.toNumber(r[e])):({currentPage:o,pageSize:e,total:r}=r,o&&(t.currentPage=o),e&&(t.pageSize=e),r&&(t.pageSize=r)))},B=(e,t)=>{var o=q.value,o=(o.response||o.props||{}).message;let r;return(r=e&&o?_xeUtils.default.isFunction(o)?o({data:e,$grid:V}):_xeUtils.default.get(e,o):r)||getI18n(t)},Q=(e,t,o)=>{var r=M.value,a=D.getCheckboxRecords();if(r)if(a.length){if(_ui.VxeUI.modal)return _ui.VxeUI.modal.confirm({id:"cfm_"+e,content:getI18n(t),escClosable:!0}).then(e=>{if("confirm"===e)return o()})}else _ui.VxeUI.modal&&_ui.VxeUI.modal.message({id:"msg_"+e,content:getI18n("vxe.grid.selectOneRecord"),status:"warning"});else a.length&&o();return Promise.resolve()},W=e=>{var t=S.proxyConfig,o=T.tablePage;let{$event:r,currentPage:a,pageSize:i}=e;var l=q.value;o.currentPage=a,o.pageSize=i,V.dispatchEvent("page-change",e,r),t&&(0,_utils.isEnableConf)(l)&&V.commitProxy("query").then(e=>{V.dispatchEvent("proxy-query",e,r)})},Y=t=>{var e=j.value,o=S.proxyConfig,e=e.getComputeMaps().computeSortOpts,r=q.value;e.value.remote&&(T.sortData=t.sortList,o)&&(0,_utils.isEnableConf)(r)&&(T.tablePage.currentPage=1,$.commitProxy("query").then(e=>{$.dispatchEvent("proxy-query",e,t.$event)})),$.dispatchEvent("sort-change",t,t.$event)},J=t=>{var e=j.value,o=S.proxyConfig,e=e.getComputeMaps().computeFilterOpts,r=q.value;e.value.remote&&(T.filterData=t.filterList,o)&&(0,_utils.isEnableConf)(r)&&(T.tablePage.currentPage=1,$.commitProxy("query").then(e=>{$.dispatchEvent("proxy-query",e,t.$event)})),$.dispatchEvent("filter-change",t,t.$event)},X=t=>{var e=S.proxyConfig,o=q.value;T.tableLoading||(e&&(0,_utils.isEnableConf)(o)&&$.commitProxy("reload").then(e=>{$.dispatchEvent("proxy-query",Object.assign(Object.assign({},e),{isReload:!0}),t.$event)}),$.dispatchEvent("form-submit",t,t.$event))},ee=e=>{var t=S.proxyConfig;let o=e.$event;var r=q.value,a=j.value;t&&(0,_utils.isEnableConf)(r)&&(a.clearScroll(),$.commitProxy("reload").then(e=>{$.dispatchEvent("proxy-query",Object.assign(Object.assign({},e),{isReload:!0}),o)})),$.dispatchEvent("form-reset",e,o)},te=e=>{$.dispatchEvent("form-submit-invalid",e,e.$event)},oe=e=>{var t=e.$event;$.dispatchEvent("form-toggle-collapse",e,t),$.dispatchEvent("form-collapse",e,t)},re=e=>{var t=T.isZMax;return(e?!t:t)&&(T.isZMax=!t,T.tZindex<(0,_utils.getLastZIndex)())&&(T.tZindex=(0,_utils.nextZIndex)()),(0,_vue.nextTick)().then(()=>D.recalculate(!0)).then(()=>(setTimeout(()=>D.recalculate(!0),15),T.isZMax))},h=(e,t)=>{e=e[t];if(e){if(!_xeUtils.default.isString(e))return e;if(u[e])return u[e];(0,_log.errLog)("vxe.error.notSlot",[e])}return null},ae=e=>{let o={};return _xeUtils.default.objectMap(e,(e,t)=>{e&&(_xeUtils.default.isString(e)?u[e]?o[t]=u[e]:(0,_log.errLog)("vxe.error.notSlot",[e]):o[t]=e)}),o},ie=()=>{var{formConfig:e,proxyConfig:o}=S,r=T.formData,a=q.value,i=x.value;if(e&&(0,_utils.isEnableConf)(i)||u.form){let e=[];if(u.form)e=u.form({$grid:V});else if(i.items){let t={};if(!i.inited){i.inited=!0;let t=a.beforeItem;a&&t&&i.items.forEach(e=>{t({$grid:V,item:e})})}i.items.forEach(e=>{_xeUtils.default.each(e.slots,e=>{_xeUtils.default.isFunction(e)||u[e]&&(t[e]=u[e])})}),l&&e.push((0,_vue.h)(l,Object.assign(Object.assign({ref:n},Object.assign({},i,{data:o&&(0,_utils.isEnableConf)(a)&&a.form?r:i.data})),{onSubmit:X,onReset:ee,onSubmitInvalid:te,onCollapse:oe}),t))}return(0,_vue.h)("div",{ref:c,key:"form",class:"vxe-grid--form-wrapper"},e)}return renderEmptyElement(V)},le=()=>{var t,o,r,a,i,l,n=S.toolbarConfig,s=L.value;if(n&&(0,_utils.isEnableConf)(s)||u.toolbar){let e=[];return u.toolbar?e=u.toolbar({$grid:V}):(n={},(l=s.slots)&&(t=h(l,"buttons"),o=h(l,"buttonPrefix"),r=h(l,"buttonSuffix"),a=h(l,"tools"),i=h(l,"toolPrefix"),l=h(l,"toolSuffix"),t&&(n.buttons=t),o&&(n.buttonPrefix=o),r&&(n.buttonSuffix=r),a&&(n.tools=a),i&&(n.toolPrefix=i),l)&&(n.toolSuffix=l),e.push((0,_vue.h)(_toolbar.default,Object.assign(Object.assign({ref:d},s),{slots:void 0}),n))),(0,_vue.h)("div",{ref:m,key:"toolbar",class:"vxe-grid--toolbar-wrapper"},e)}return renderEmptyElement(V)},ne=()=>u.top?(0,_vue.h)("div",{ref:v,key:"top",class:"vxe-grid--top-wrapper"},u.top({$grid:V})):renderEmptyElement(V),se=()=>{var e=u.left;return e?(0,_vue.h)("div",{class:"vxe-grid--left-wrapper"},e({$grid:V})):renderEmptyElement(V)},ue=()=>{var e=u.right;return e?(0,_vue.h)("div",{class:"vxe-grid--right-wrapper"},e({$grid:V})):renderEmptyElement(V)},de=()=>{var e=S.proxyConfig,t=k.value,o=q.value,r=Object.assign({},me),a=u.empty,i=u.loading,l=u.rowDragIcon||u["row-drag-icon"],n=u.columnDragIcon||u["column-drag-icon"],e=(e&&(0,_utils.isEnableConf)(o)&&(o.sort&&(r.onSortChange=Y),o.filter)&&(r.onFilterChange=J),{});return a&&(e.empty=a),i&&(e.loading=i),l&&(e.rowDragIcon=l),n&&(e.columnDragIcon=n),(0,_vue.h)("div",{class:"vxe-grid--table-wrapper"},[(0,_vue.h)(_table.default,Object.assign(Object.assign({ref:j},t),r),e)])},ge=()=>u.bottom?(0,_vue.h)("div",{ref:p,key:"bottom",class:"vxe-grid--bottom-wrapper"},u.bottom({$grid:V})):renderEmptyElement(V),ce=()=>{var{proxyConfig:e,pagerConfig:t}=S,o=q.value,r=F.value,a=u.pager;return t&&(0,_utils.isEnableConf)(r)||u.pager?(0,_vue.h)("div",{ref:f,key:"pager",class:"vxe-grid--pager-wrapper"},a?a({$grid:V}):[i?(0,_vue.h)(i,Object.assign(Object.assign(Object.assign({ref:g},r),e&&(0,_utils.isEnableConf)(o)?T.tablePage:{}),{onPageChange:W}),ae(r.slots)):renderEmptyElement(V)]):renderEmptyElement(V)},C=e=>{let t=[];return e.forEach(e=>{switch(e){case"Form":t.push(ie());break;case"Toolbar":t.push(le());break;case"Top":t.push(ne());break;case"Table":t.push((0,_vue.h)("div",{key:"table",class:"vxe-grid--table-container"},[se(),de(),ue()]));break;case"Bottom":t.push(ge());break;case"Pager":t.push(ce());break;default:(0,_log.errLog)("vxe.error.notProp",["layouts -> "+e])}}),t},me={},ve=(_emits.default.forEach(t=>{var e=_xeUtils.default.camelCase("on-"+t);me[e]=(...e)=>r(t,...e)}),()=>{var{proxyConfig:e,formConfig:t}=S,o=T.proxyInited,r=q.value,i=x.value;if(e&&(0,_utils.isEnableConf)(r)){if(t&&(0,_utils.isEnableConf)(i)&&r.form&&i.items){let a={};i.items.forEach(t=>{var{field:o,itemRender:r}=t;if(o){let e=null;r&&(r=r.defaultValue,_xeUtils.default.isFunction(r)?e=r({item:t}):_xeUtils.default.isUndefined(r)||(e=r)),a[o]=e}}),T.formData=a}o||!(T.proxyInited=!0)!==r.autoLoad&&(0,_vue.nextTick)().then(()=>$.commitProxy("_init")).then(e=>{$.dispatchEvent("proxy-query",Object.assign(Object.assign({},e),{isInited:!0}),new Event("init"))})}}),pe=e=>{var t=a.value;globalEvents.hasKey(e,GLOBAL_EVENT_KEYS.ESCAPE)&&T.isZMax&&!1!==t.escRestore&&y.triggerZoomEvent(e)};let $={dispatchEvent:(e,t,o)=>{r(e,createEvent(o,{$grid:V},t))},getEl(){return s.value},commitProxy(t,...d){let{toolbarConfig:e,pagerConfig:l,editRules:g,validConfig:c}=S,n=T.tablePage,m=M.value,v=Z.value;var o=q.value;let s=F.value;var r=L.value;let{beforeQuery:u,afterQuery:p,beforeDelete:f,afterDelete:x,beforeSave:b,afterSave:_,ajax:h={}}=o,C=o.response||o.props||{},y=j.value;var R=z();let E=null,w=null;w=_xeUtils.default.isString(t)?(o=r.buttons,r=e&&(0,_utils.isEnableConf)(r)&&o?_xeUtils.default.findTree(o,e=>e.code===t,{children:"dropdowns"}):null,E=r?r.item:null,t):(E=t).code;var a=E?E.params:null;switch(w){case"insert":return y.insert({});case"insert_edit":return y.insert({}).then(({row:e})=>y.setEditRow(e));case"insert_actived":return y.insert({}).then(({row:e})=>y.setEditRow(e));case"mark_cancel":I=w,k=M.value,O=j.value,(P=O.getCheckboxRecords()).length?(O.togglePendingRow(P),D.clearCheckboxRow()):k&&_ui.VxeUI.modal&&_ui.VxeUI.modal.message({id:I,content:getI18n("vxe.grid.selectOneRecord"),status:"warning"});break;case"remove":return Q(w,"vxe.grid.removeSelectRecord",()=>y.removeCheckboxRow());case"import":y.importData(a);break;case"open_import":y.openImport(a);break;case"export":y.exportData(a);break;case"open_export":y.openExport(a);break;case"reset_custom":return y.resetCustom(!0);case"_init":case"reload":case"query":{var O=h.query;let i=h.querySuccess,r=h.queryError;if(O){var I,P="_init"===w,k="reload"===w;if(!P&&T.tableLoading)return(0,_vue.nextTick)();let t=[],o=[],e={};if(l&&((P||k)&&(n.currentPage=1),(0,_utils.isEnableConf)(s))&&(e=Object.assign({},n)),P){let e=null;y&&(I=y.getComputeMaps().computeSortOpts,U=I.value,e=U.defaultSort),e&&(_xeUtils.default.isArray(e)||(e=[e]),t=e.map(e=>({field:e.field,property:e.field,order:e.order}))),y&&(o=y.getCheckedFilters())}else y&&(k?y.clearAll():(t=y.getSortColumns(),o=y.getCheckedFilters()));let a={code:w,button:E,isInited:P,isReload:k,$grid:V,page:e,sort:t.length?t[0]:{},sorts:t,filters:o,form:R,options:O};return T.sortData=t,T.filterData=o,T.tableLoading=!0,Promise.resolve((u||O)(a,...d)).then(e=>{let t=[];var o,r;return T.tableLoading=!1,e&&(l&&(0,_utils.isEnableConf)(s)?(r=C.total,r=(_xeUtils.default.isFunction(r)?r({data:e,$grid:V}):_xeUtils.default.get(e,r||"page.total"))||0,n.total=_xeUtils.default.toNumber(r),o=C.result,t=(_xeUtils.default.isFunction(o)?o({data:e,$grid:V}):_xeUtils.default.get(e,o||"result"))||[],o=Math.max(Math.ceil(r/n.pageSize),1),n.currentPage>o&&(n.currentPage=o)):(r=C.list,t=(r?_xeUtils.default.isFunction(r)?r({data:e,$grid:V}):_xeUtils.default.get(e,r):e)||[])),y?y.loadData(t):(0,_vue.nextTick)(()=>{y&&y.loadData(t)}),p&&p(a,...d),i&&i(Object.assign(Object.assign({},a),{response:e})),{status:!0}}).catch(e=>(T.tableLoading=!1,r&&r(Object.assign(Object.assign({},a),{response:e})),{status:!1}))}(0,_log.errLog)("vxe.error.notFunc",["proxy-config.ajax.query"]);break}case"delete":{let r=h.delete,a=h.deleteSuccess,i=h.deleteError;if(r){let e=D.getCheckboxRecords(),t=e.filter(e=>!y.isInsertByRow(e));var U={removeRecords:t};let o={$grid:V,code:w,button:E,body:U,form:R,options:r};if(e.length)return Q(w,"vxe.grid.deleteSelectRecord",()=>t.length?(T.tableLoading=!0,Promise.resolve((f||r)(o,...d)).then(e=>(T.tableLoading=!1,y.setPendingRow(t,!1),v&&_ui.VxeUI.modal&&_ui.VxeUI.modal.message({content:B(e,"vxe.grid.delSuccess"),status:"success"}),x?x(o,...d):$.commitProxy("query"),a&&a(Object.assign(Object.assign({},o),{response:e})),{status:!0})).catch(e=>(T.tableLoading=!1,v&&_ui.VxeUI.modal&&_ui.VxeUI.modal.message({id:w,content:B(e,"vxe.grid.operError"),status:"error"}),i&&i(Object.assign(Object.assign({},o),{response:e})),{status:!1}))):y.remove(e));m&&_ui.VxeUI.modal&&_ui.VxeUI.modal.message({id:w,content:getI18n("vxe.grid.selectOneRecord"),status:"warning"})}else(0,_log.errLog)("vxe.error.notFunc",["proxy-config.ajax.delete"]);break}case"save":{let n=h.save,s=h.saveSuccess,u=h.saveError;if(n){let t=y.getRecordset(),{insertRecords:o,removeRecords:r,updateRecords:a,pendingRecords:i}=t,l={$grid:V,code:w,button:E,body:t,form:R,options:n},e=(o.length&&(t.pendingRecords=i.filter(e=>-1===y.findRowIndexOf(o,e))),i.length&&(t.insertRecords=o.filter(e=>-1===y.findRowIndexOf(i,e))),Promise.resolve());return(e=g?y[c&&"full"===c.msgMode?"fullValidate":"validate"](t.insertRecords.concat(a)):e).then(e=>{if(!e)return t.insertRecords.length||r.length||a.length||t.pendingRecords.length?(T.tableLoading=!0,Promise.resolve((b||n)(l,...d)).then(e=>(T.tableLoading=!1,y.clearPendingRow(),v&&_ui.VxeUI.modal&&_ui.VxeUI.modal.message({content:B(e,"vxe.grid.saveSuccess"),status:"success"}),_?_(l,...d):$.commitProxy("query"),s&&s(Object.assign(Object.assign({},l),{response:e})),{status:!0})).catch(e=>(T.tableLoading=!1,v&&_ui.VxeUI.modal&&_ui.VxeUI.modal.message({id:w,content:B(e,"vxe.grid.operError"),status:"error"}),u&&u(Object.assign(Object.assign({},l),{response:e})),{status:!1}))):void(m&&_ui.VxeUI.modal&&_ui.VxeUI.modal.message({id:w,content:getI18n("vxe.grid.dataUnchanged"),status:"info"}))})}(0,_log.errLog)("vxe.error.notFunc",["proxy-config.ajax.save"]);break}default:var U=commands.get(w);U&&((U=U.tableCommandMethod||U.commandMethod)?U({code:w,button:E,$grid:V,$table:y},...d):(0,_log.errLog)("vxe.error.notCommands",[w]))}return(0,_vue.nextTick)()},zoom(){return T.isZMax?$.revert():$.maximize()},isMaximized(){return T.isZMax},maximize(){return re(!0)},revert(){return re()},getFormData:z,getFormItems(e){var t=x.value,o=S.formConfig,r=t.items;let a=[];return _xeUtils.default.eachTree(o&&(0,_utils.isEnableConf)(t)&&r?r:[],e=>{a.push(e)},{children:"children"}),_xeUtils.default.isUndefined(e)?a:a[e]},getProxyInfo(){var e,t=j.value;return S.proxyConfig?(e=T.sortData,{data:t?t.getFullData():[],filter:T.filterData,form:z(),sort:e.length?e[0]:{},sorts:e,pager:T.tablePage,pendingRecords:t?t.getPendingRecords():[]}):null}},y={extendTableMethods:o,callSlot(e,t){return e&&(_xeUtils.default.isString(e)&&(e=u[e]||null),_xeUtils.default.isFunction(e))?(0,_vn.getSlotVNs)(e(t)):[]},getExcludeHeight(){var e,t,o,r,a,i,l=T.isZMax,n=s.value;return n?(e=c.value,t=m.value,o=v.value,r=p.value,a=f.value,i=n.parentElement,(!l&&i?(0,_dom.getPaddingTopBottomSize)(i):0)+(0,_dom.getPaddingTopBottomSize)(n)+(0,_dom.getOffsetHeight)(e)+(0,_dom.getOffsetHeight)(t)+(0,_dom.getOffsetHeight)(o)+(0,_dom.getOffsetHeight)(r)+(0,_dom.getOffsetHeight)(a)):0},getParentHeight(){var e=s.value;return e?(e=e.parentElement,(T.isZMax?(0,_dom.getDomNode)().visibleHeight:e?_xeUtils.default.toNumber(getComputedStyle(e).height):0)-y.getExcludeHeight()):0},triggerToolbarCommitEvent(e,t){let o=e.code;return $.commitProxy(e,t).then(e=>{o&&e&&e.status&&["query","reload","delete","save"].includes(o)&&$.dispatchEvent("delete"===o||"save"===o?"proxy-"+o:"proxy-query",Object.assign(Object.assign({},e),{isReload:"reload"===o}),t)})},triggerToolbarBtnEvent(e,t){y.triggerToolbarCommitEvent(e,t),$.dispatchEvent("toolbar-button-click",{code:e.code,button:e},t)},triggerToolbarTolEvent(e,t){y.triggerToolbarCommitEvent(e,t),$.dispatchEvent("toolbar-tool-click",{code:e.code,tool:e},t)},triggerZoomEvent(e){$.zoom(),$.dispatchEvent("zoom",{type:T.isZMax?"max":"revert"},e)}},R=(Object.assign(V,D,$,y,{loadColumn(e){var t=j.value;return _xeUtils.default.eachTree(e,e=>{e.slots&&_xeUtils.default.each(e.slots,e=>{_xeUtils.default.isFunction(e)||u[e]||(0,_log.errLog)("vxe.error.notSlot",[e])})}),t?t.loadColumn(e):(0,_vue.nextTick)()},reloadColumn(e){return V.clearAll(),V.loadColumn(e)}}),(0,_vue.ref)(0));(0,_vue.watch)(()=>S.columns?S.columns.length:-1,()=>{R.value++}),(0,_vue.watch)(()=>S.columns,()=>{R.value++}),(0,_vue.watch)(R,()=>{(0,_vue.nextTick)(()=>V.loadColumn(S.columns||[]))}),(0,_vue.watch)(()=>S.toolbarConfig,()=>{b()}),(0,_vue.watch)(A,()=>{_("currentPage")}),(0,_vue.watch)(K,()=>{_("pageSize")}),(0,_vue.watch)(H,()=>{_("total")}),(0,_vue.watch)(()=>S.proxyConfig,()=>{ve()}),hooks.forEach(e=>{var e=e.setupGrid;e&&(e=e(V))&&_xeUtils.default.isObject(e)&&Object.assign(V,e)}),_(),(0,_vue.onMounted)(()=>{(0,_vue.nextTick)(()=>{var e=S.columns;S.formConfig&&!l&&(0,_log.errLog)("vxe.error.reqComp",["vxe-form"]),S.pagerConfig&&!i&&(0,_log.errLog)("vxe.error.reqComp",["vxe-pager"]),e&&e.length&&V.loadColumn(e),b(),ve()}),globalEvents.on(V,"keydown",pe)}),(0,_vue.onUnmounted)(()=>{globalEvents.off(V,"keydown")});return V.renderVN=()=>{var e=O.value,t=I.value;return(0,_vue.h)("div",{ref:s,class:["vxe-grid",{["size--"+e]:e,"is--animat":!!S.animat,"is--round":S.round,"is--maximize":T.isZMax,"is--loading":S.loading||T.tableLoading}],style:t},(()=>{var{headKeys:e,bodyKeys:t,footKeys:o}=U.value,r=u.asideLeft||u["aside-left"],a=u.asideRight||u["aside-right"];return[(0,_vue.h)("div",{class:"vxe-grid--layout-header-wrapper"},C(e)),(0,_vue.h)("div",{class:"vxe-grid--layout-body-wrapper"},[r?(0,_vue.h)("div",{class:"vxe-grid--layout-aside-left-wrapper"},r({})):renderEmptyElement(V),(0,_vue.h)("div",{class:"vxe-grid--layout-body-content-wrapper"},C(t)),a?(0,_vue.h)("div",{class:"vxe-grid--layout-aside-right-wrapper"},a({})):renderEmptyElement(V)]),(0,_vue.h)("div",{class:"vxe-grid--layout-footer-wrapper"},C(o))]})())},(0,_vue.provide)("$xeGrid",V),V},render(){return this.renderVN()}});