import { defineComponent, h, inject } from 'vue';
import { useWidgetName } from '../../form-design/src/use';
import VxeFormItemComponent from '../../form/src/form-item';
import VxeInputComponent from '../../input/src/input';
export const WidgetVxeInputViewComponent = defineComponent({
    props: {
        renderOpts: {
            type: Object,
            default: () => ({})
        },
        renderParams: {
            type: Object,
            default: () => ({})
        }
    },
    emits: [],
    setup(props) {
        const $xeFormView = inject('$xeFormView', null);
        const { computeKebabCaseName } = useWidgetName(props);
        const changeEvent = () => {
            const { renderParams } = props;
            const { widget } = renderParams;
            if ($xeFormView) {
                const itemValue = $xeFormView ? $xeFormView.getItemValue(widget) : null;
                $xeFormView.updateWidgetStatus(widget, itemValue);
            }
        };
        return () => {
            const { renderParams } = props;
            const { widget } = renderParams;
            const { options } = widget;
            const kebabCaseName = computeKebabCaseName.value;
            return h(VxeFormItemComponent, {
                class: ['vxe-form-design--widget-render-form-item', `widget-${kebabCaseName}`],
                field: widget.field,
                title: widget.title,
                itemRender: {}
            }, {
                default() {
                    return h(VxeInputComponent, {
                        modelValue: $xeFormView ? $xeFormView.getItemValue(widget) : null,
                        placeholder: options.placeholder,
                        onChange: changeEvent,
                        'onUpdate:modelValue'(val) {
                            if ($xeFormView) {
                                $xeFormView.setItemValue(widget, val);
                            }
                        }
                    });
                }
            });
        };
    }
});
