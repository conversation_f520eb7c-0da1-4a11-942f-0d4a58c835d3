import { defineComponent, h } from 'vue';
import { useWidgetName } from '../../form-design/src/use';
import VxeFormItemComponent from '../../form/src/form-item';
export const WidgetTextViewComponent = defineComponent({
    props: {
        renderOpts: {
            type: Object,
            default: () => ({})
        },
        renderParams: {
            type: Object,
            default: () => ({})
        }
    },
    emits: [],
    setup(props) {
        const { computeKebabCaseName } = useWidgetName(props);
        return () => {
            const { renderParams } = props;
            const { widget } = renderParams;
            const { options } = widget;
            const kebabCaseName = computeKebabCaseName.value;
            return h(VxeFormItemComponent, {
                class: ['vxe-form-design--widget-render-form-item', `widget-${kebabCaseName}`],
                align: options.align
            }, {
                default() {
                    return h('div', {
                        style: {
                            fontSize: options.fontSize,
                            fontWeight: options.bold ? 'bold' : ''
                        }
                    }, widget.title);
                }
            });
        };
    }
});
