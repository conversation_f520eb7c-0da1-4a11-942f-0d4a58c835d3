{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/shell/shell.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    comments: {\n        lineComment: '#'\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" },\n        { open: '`', close: '`' }\n    ],\n    surroundingPairs: [\n        { open: '{', close: '}' },\n        { open: '[', close: ']' },\n        { open: '(', close: ')' },\n        { open: '\"', close: '\"' },\n        { open: \"'\", close: \"'\" },\n        { open: '`', close: '`' }\n    ]\n};\nexport var language = {\n    defaultToken: '',\n    ignoreCase: true,\n    tokenPostfix: '.shell',\n    brackets: [\n        { token: 'delimiter.bracket', open: '{', close: '}' },\n        { token: 'delimiter.parenthesis', open: '(', close: ')' },\n        { token: 'delimiter.square', open: '[', close: ']' }\n    ],\n    keywords: [\n        'if',\n        'then',\n        'do',\n        'else',\n        'elif',\n        'while',\n        'until',\n        'for',\n        'in',\n        'esac',\n        'fi',\n        'fin',\n        'fil',\n        'done',\n        'exit',\n        'set',\n        'unset',\n        'export',\n        'function'\n    ],\n    builtins: [\n        'ab',\n        'awk',\n        'bash',\n        'beep',\n        'cat',\n        'cc',\n        'cd',\n        'chown',\n        'chmod',\n        'chroot',\n        'clear',\n        'cp',\n        'curl',\n        'cut',\n        'diff',\n        'echo',\n        'find',\n        'gawk',\n        'gcc',\n        'get',\n        'git',\n        'grep',\n        'hg',\n        'kill',\n        'killall',\n        'ln',\n        'ls',\n        'make',\n        'mkdir',\n        'openssl',\n        'mv',\n        'nc',\n        'node',\n        'npm',\n        'ping',\n        'ps',\n        'restart',\n        'rm',\n        'rmdir',\n        'sed',\n        'service',\n        'sh',\n        'shopt',\n        'shred',\n        'source',\n        'sort',\n        'sleep',\n        'ssh',\n        'start',\n        'stop',\n        'su',\n        'sudo',\n        'svn',\n        'tee',\n        'telnet',\n        'top',\n        'touch',\n        'vi',\n        'vim',\n        'wall',\n        'wc',\n        'wget',\n        'who',\n        'write',\n        'yes',\n        'zsh'\n    ],\n    // we include these common regular expressions\n    symbols: /[=><!~?&|+\\-*\\/\\^;\\.,]+/,\n    // The main tokenizer for our languages\n    tokenizer: {\n        root: [\n            { include: '@whitespace' },\n            [\n                /[a-zA-Z]\\w*/,\n                {\n                    cases: {\n                        '@keywords': 'keyword',\n                        '@builtins': 'type.identifier',\n                        '@default': ''\n                    }\n                }\n            ],\n            { include: '@strings' },\n            { include: '@parameters' },\n            { include: '@heredoc' },\n            [/[{}\\[\\]()]/, '@brackets'],\n            [/-+\\w+/, 'attribute.name'],\n            [/@symbols/, 'delimiter'],\n            { include: '@numbers' },\n            [/[,;]/, 'delimiter']\n        ],\n        whitespace: [\n            [/\\s+/, 'white'],\n            [/(^#!.*$)/, 'metatag'],\n            [/(^#.*$)/, 'comment']\n        ],\n        numbers: [\n            [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, 'number.float'],\n            [/0[xX][0-9a-fA-F_]*[0-9a-fA-F]/, 'number.hex'],\n            [/\\d+/, 'number']\n        ],\n        // Recognize strings, including those broken across lines\n        strings: [\n            [/'/, 'string', '@stringBody'],\n            [/\"/, 'string', '@dblStringBody']\n        ],\n        stringBody: [\n            [/'/, 'string', '@popall'],\n            [/./, 'string']\n        ],\n        dblStringBody: [\n            [/\"/, 'string', '@popall'],\n            [/./, 'string']\n        ],\n        heredoc: [\n            [\n                /(<<[-<]?)(\\s*)(['\"`]?)([\\w\\-]+)(['\"`]?)/,\n                [\n                    'constants',\n                    'white',\n                    'string.heredoc.delimiter',\n                    'string.heredoc',\n                    'string.heredoc.delimiter'\n                ]\n            ]\n        ],\n        parameters: [\n            [/\\$\\d+/, 'variable.predefined'],\n            [/\\$\\w+/, 'variable'],\n            [/\\$[*@#?\\-$!0_]/, 'variable'],\n            [/\\$'/, 'variable', '@parameterBodyQuote'],\n            [/\\$\"/, 'variable', '@parameterBodyDoubleQuote'],\n            [/\\$\\(/, 'variable', '@parameterBodyParen'],\n            [/\\$\\{/, 'variable', '@parameterBodyCurlyBrace']\n        ],\n        parameterBodyQuote: [\n            [/[^#:%*@\\-!_']+/, 'variable'],\n            [/[#:%*@\\-!_]/, 'delimiter'],\n            [/[']/, 'variable', '@pop']\n        ],\n        parameterBodyDoubleQuote: [\n            [/[^#:%*@\\-!_\"]+/, 'variable'],\n            [/[#:%*@\\-!_]/, 'delimiter'],\n            [/[\"]/, 'variable', '@pop']\n        ],\n        parameterBodyParen: [\n            [/[^#:%*@\\-!_)]+/, 'variable'],\n            [/[#:%*@\\-!_]/, 'delimiter'],\n            [/[)]/, 'variable', '@pop']\n        ],\n        parameterBodyCurlyBrace: [\n            [/[^#:%*@\\-!_}]+/, 'variable'],\n            [/[#:%*@\\-!_]/, 'delimiter'],\n            [/[}]/, 'variable', '@pop']\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,aAAa;AAAA,EACjB;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC5B;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,UAAU;AAAA,IACN,EAAE,OAAO,qBAAqB,MAAM,KAAK,OAAO,IAAI;AAAA,IACpD,EAAE,OAAO,yBAAyB,MAAM,KAAK,OAAO,IAAI;AAAA,IACxD,EAAE,OAAO,oBAAoB,MAAM,KAAK,OAAO,IAAI;AAAA,EACvD;AAAA,EACA,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA;AAAA,EAEA,SAAS;AAAA;AAAA,EAET,WAAW;AAAA,IACP,MAAM;AAAA,MACF,EAAE,SAAS,cAAc;AAAA,MACzB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,aAAa;AAAA,YACb,aAAa;AAAA,YACb,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA,MACtB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,SAAS,gBAAgB;AAAA,MAC1B,CAAC,YAAY,WAAW;AAAA,MACxB,EAAE,SAAS,WAAW;AAAA,MACtB,CAAC,QAAQ,WAAW;AAAA,IACxB;AAAA,IACA,YAAY;AAAA,MACR,CAAC,OAAO,OAAO;AAAA,MACf,CAAC,YAAY,SAAS;AAAA,MACtB,CAAC,WAAW,SAAS;AAAA,IACzB;AAAA,IACA,SAAS;AAAA,MACL,CAAC,4BAA4B,cAAc;AAAA,MAC3C,CAAC,iCAAiC,YAAY;AAAA,MAC9C,CAAC,OAAO,QAAQ;AAAA,IACpB;AAAA;AAAA,IAEA,SAAS;AAAA,MACL,CAAC,KAAK,UAAU,aAAa;AAAA,MAC7B,CAAC,KAAK,UAAU,gBAAgB;AAAA,IACpC;AAAA,IACA,YAAY;AAAA,MACR,CAAC,KAAK,UAAU,SAAS;AAAA,MACzB,CAAC,KAAK,QAAQ;AAAA,IAClB;AAAA,IACA,eAAe;AAAA,MACX,CAAC,KAAK,UAAU,SAAS;AAAA,MACzB,CAAC,KAAK,QAAQ;AAAA,IAClB;AAAA,IACA,SAAS;AAAA,MACL;AAAA,QACI;AAAA,QACA;AAAA,UACI;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,YAAY;AAAA,MACR,CAAC,SAAS,qBAAqB;AAAA,MAC/B,CAAC,SAAS,UAAU;AAAA,MACpB,CAAC,kBAAkB,UAAU;AAAA,MAC7B,CAAC,OAAO,YAAY,qBAAqB;AAAA,MACzC,CAAC,OAAO,YAAY,2BAA2B;AAAA,MAC/C,CAAC,QAAQ,YAAY,qBAAqB;AAAA,MAC1C,CAAC,QAAQ,YAAY,0BAA0B;AAAA,IACnD;AAAA,IACA,oBAAoB;AAAA,MAChB,CAAC,kBAAkB,UAAU;AAAA,MAC7B,CAAC,eAAe,WAAW;AAAA,MAC3B,CAAC,OAAO,YAAY,MAAM;AAAA,IAC9B;AAAA,IACA,0BAA0B;AAAA,MACtB,CAAC,kBAAkB,UAAU;AAAA,MAC7B,CAAC,eAAe,WAAW;AAAA,MAC3B,CAAC,OAAO,YAAY,MAAM;AAAA,IAC9B;AAAA,IACA,oBAAoB;AAAA,MAChB,CAAC,kBAAkB,UAAU;AAAA,MAC7B,CAAC,eAAe,WAAW;AAAA,MAC3B,CAAC,OAAO,YAAY,MAAM;AAAA,IAC9B;AAAA,IACA,yBAAyB;AAAA,MACrB,CAAC,kBAAkB,UAAU;AAAA,MAC7B,CAAC,eAAe,WAAW;AAAA,MAC3B,CAAC,OAAO,YAAY,MAAM;AAAA,IAC9B;AAAA,EACJ;AACJ;", "names": []}