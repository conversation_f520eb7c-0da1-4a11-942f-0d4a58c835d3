Object.defineProperty(exports,"__esModule",{value:!0}),exports.getChangeEvent=getChangeEvent,exports.getModelEvent=getModelEvent,exports.getOnName=getOnName,exports.getSlotVNs=getSlotVNs;var _xeUtils=_interopRequireDefault(require("xe-utils"));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function getOnName(e){return"on"+e.substring(0,1).toLocaleUpperCase()+e.substring(1)}function getModelEvent(e){switch(e.name){case"input":case"textarea":return"input";case"select":return"change"}return"update:modelValue"}function getChangeEvent(e){switch(e.name){case"input":case"textarea":case"VxeInput":case"VxeNumberInput":case"VxeTextarea":case"$input":case"$textarea":return"input"}return"change"}function getSlotVNs(e){return null==e?[]:_xeUtils.default.isArray(e)?e:[e]}