import request from '@/config/axios'

export const ActivitiesFlowApi = {
  /** 更新活动 */
  updateActivities: async (data: any) => {
    return await request.post({ url: '/bpm/activities/update', data })
  },
  /** 删除活动 */
  deleteActivities: async (data: any) => {
    return await request.post({ url: '/bpm/activities/delete', data })
  },
  completeActivities: async (data: any) => {
    return await request.post({ url: '/bpm/activities/complete', data })
  },
  getActivities: async (id: string) => {
    return await request.get({ url: `/bpm/activities/get/${id}` })
  },
  batchUpdateActivities: async (data: any) => {
    return await request.post({ url: '/bpm/activities/batch-update', data })
  },
  getBatchUpdateFlow: async (id: string) => {
    return await request.get({ url: `/bpm/activities/get-batch-update-flow/${id}` })
  }
}
