{"version": 3, "sources": ["../../@form-create/element-ui/auto-import.js"], "sourcesContent": ["import {\n    ElAutocomplete,\n    ElButton,\n    ElCascader,\n    ElCheckbox,\n    ElCheckboxButton,\n    ElCheckboxGroup,\n    ElCol,\n    ElColorPicker,\n    ElDatePicker,\n    ElDialog,\n    ElForm,\n    ElInput,\n    ElInputNumber,\n    ElPopover,\n    ElRadio,\n    ElRadioButton,\n    ElRadioGroup,\n    ElRate,\n    ElRow,\n    ElSelect,\n    ElSlider,\n    ElSwitch,\n    ElTimePicker,\n    ElTooltip,\n    ElTree,\n    ElUpload,\n    ElIcon,\n    ElProgress,\n} from 'element-plus';\nimport 'element-plus/es/components/button/style/css'\nimport 'element-plus/es/components/form/style/css'\nimport 'element-plus/es/components/form-item/style/css'\nimport 'element-plus/es/components/row/style/css'\nimport 'element-plus/es/components/col/style/css'\nimport 'element-plus/es/components/input/style/css'\nimport 'element-plus/es/components/input-number/style/css'\nimport 'element-plus/es/components/cascader/style/css'\nimport 'element-plus/es/components/popover/style/css'\nimport 'element-plus/es/components/tooltip/style/css'\nimport 'element-plus/es/components/autocomplete/style/css'\nimport 'element-plus/es/components/checkbox-group/style/css'\nimport 'element-plus/es/components/checkbox-button/style/css'\nimport 'element-plus/es/components/checkbox/style/css'\nimport 'element-plus/es/components/radio-group/style/css'\nimport 'element-plus/es/components/radio-button/style/css'\nimport 'element-plus/es/components/radio/style/css'\nimport 'element-plus/es/components/select/style/css'\nimport 'element-plus/es/components/tree/style/css'\nimport 'element-plus/es/components/upload/style/css'\nimport 'element-plus/es/components/rate/style/css'\nimport 'element-plus/es/components/switch/style/css'\nimport 'element-plus/es/components/slider/style/css'\nimport 'element-plus/es/components/dialog/style/css'\nimport 'element-plus/es/components/color-picker/style/css'\nimport 'element-plus/es/components/date-picker/style/css'\nimport 'element-plus/es/components/time-picker/style/css'\nimport 'element-plus/es/components/progress/style/css'\n\nexport default function install(formCreate) {\n    formCreate.useApp((_, app) => {\n        app.component(ElForm.name) || app.use(ElForm);\n        app.component(ElButton.name) || app.use(ElButton);\n        app.component(ElRow.name) || app.use(ElRow);\n        app.component(ElCol.name) || app.use(ElCol);\n        app.component(ElInput.name) || app.use(ElInput);\n        app.component(ElInputNumber.name) || app.use(ElInputNumber);\n        app.component(ElCascader.name) || app.use(ElCascader);\n        app.component(ElPopover.name) || app.use(ElPopover);\n        app.component(ElTooltip.name) || app.use(ElTooltip);\n        app.component(ElAutocomplete.name) || app.use(ElAutocomplete);\n        app.component(ElCheckboxGroup.name) || app.use(ElCheckboxGroup);\n        app.component(ElCheckboxButton.name) || app.use(ElCheckboxButton);\n        app.component(ElRadioGroup.name) || app.use(ElRadioGroup);\n        app.component(ElRadioButton.name) || app.use(ElRadioButton);\n        app.component(ElRadio.name) || app.use(ElRadio);\n        app.component(ElDialog.name) || app.use(ElDialog);\n        app.component(ElCheckbox.name) || app.use(ElCheckbox);\n        app.component(ElSelect.name) || app.use(ElSelect);\n        app.component(ElTree.name) || app.use(ElTree);\n        app.component(ElUpload.name) || app.use(ElUpload);\n        app.component(ElSlider.name) || app.use(ElSlider);\n        app.component(ElRate.name) || app.use(ElRate);\n        app.component(ElColorPicker.name) || app.use(ElColorPicker);\n        app.component(ElSwitch.name) || app.use(ElSwitch);\n        app.component(ElDatePicker.name) || app.use(ElDatePicker);\n        app.component(ElIcon.name) || app.use(ElIcon);\n        app.component(ElTimePicker.name) || app.use(ElTimePicker);\n        app.component(ElProgress.name) || app.use(ElProgress);\n    });\n\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2De,SAAR,QAAyB,YAAY;AACxC,aAAW,OAAO,CAAC,GAAG,QAAQ;AAC1B,QAAI,UAAU,OAAO,IAAI,KAAK,IAAI,IAAI,MAAM;AAC5C,QAAI,UAAU,SAAS,IAAI,KAAK,IAAI,IAAI,QAAQ;AAChD,QAAI,UAAU,MAAM,IAAI,KAAK,IAAI,IAAI,KAAK;AAC1C,QAAI,UAAU,MAAM,IAAI,KAAK,IAAI,IAAI,KAAK;AAC1C,QAAI,UAAU,QAAQ,IAAI,KAAK,IAAI,IAAI,OAAO;AAC9C,QAAI,UAAU,cAAc,IAAI,KAAK,IAAI,IAAI,aAAa;AAC1D,QAAI,UAAU,WAAW,IAAI,KAAK,IAAI,IAAI,UAAU;AACpD,QAAI,UAAU,UAAU,IAAI,KAAK,IAAI,IAAI,SAAS;AAClD,QAAI,UAAU,UAAU,IAAI,KAAK,IAAI,IAAI,SAAS;AAClD,QAAI,UAAU,eAAe,IAAI,KAAK,IAAI,IAAI,cAAc;AAC5D,QAAI,UAAU,gBAAgB,IAAI,KAAK,IAAI,IAAI,eAAe;AAC9D,QAAI,UAAU,iBAAiB,IAAI,KAAK,IAAI,IAAI,gBAAgB;AAChE,QAAI,UAAU,aAAa,IAAI,KAAK,IAAI,IAAI,YAAY;AACxD,QAAI,UAAU,cAAc,IAAI,KAAK,IAAI,IAAI,aAAa;AAC1D,QAAI,UAAU,QAAQ,IAAI,KAAK,IAAI,IAAI,OAAO;AAC9C,QAAI,UAAU,SAAS,IAAI,KAAK,IAAI,IAAI,QAAQ;AAChD,QAAI,UAAU,WAAW,IAAI,KAAK,IAAI,IAAI,UAAU;AACpD,QAAI,UAAU,SAAS,IAAI,KAAK,IAAI,IAAI,QAAQ;AAChD,QAAI,UAAU,OAAO,IAAI,KAAK,IAAI,IAAI,MAAM;AAC5C,QAAI,UAAU,SAAS,IAAI,KAAK,IAAI,IAAI,QAAQ;AAChD,QAAI,UAAU,SAAS,IAAI,KAAK,IAAI,IAAI,QAAQ;AAChD,QAAI,UAAU,OAAO,IAAI,KAAK,IAAI,IAAI,MAAM;AAC5C,QAAI,UAAU,cAAc,IAAI,KAAK,IAAI,IAAI,aAAa;AAC1D,QAAI,UAAU,SAAS,IAAI,KAAK,IAAI,IAAI,QAAQ;AAChD,QAAI,UAAU,aAAa,IAAI,KAAK,IAAI,IAAI,YAAY;AACxD,QAAI,UAAU,OAAO,IAAI,KAAK,IAAI,IAAI,MAAM;AAC5C,QAAI,UAAU,aAAa,IAAI,KAAK,IAAI,IAAI,YAAY;AACxD,QAAI,UAAU,WAAW,IAAI,KAAK,IAAI,IAAI,UAAU;AAAA,EACxD,CAAC;AAEL;", "names": []}