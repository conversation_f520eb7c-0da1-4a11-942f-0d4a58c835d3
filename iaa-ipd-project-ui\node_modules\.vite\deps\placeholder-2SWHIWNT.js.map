{"version": 3, "sources": ["../../codemirror/addon/display/placeholder.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  CodeMirror.defineOption(\"placeholder\", \"\", function(cm, val, old) {\n    var prev = old && old != CodeMirror.Init;\n    if (val && !prev) {\n      cm.on(\"blur\", onBlur);\n      cm.on(\"change\", onChange);\n      cm.on(\"swapDoc\", onChange);\n      CodeMirror.on(cm.getInputField(), \"compositionupdate\", cm.state.placeholderCompose = function() { onComposition(cm) })\n      on<PERSON><PERSON><PERSON>(cm);\n    } else if (!val && prev) {\n      cm.off(\"blur\", onBlur);\n      cm.off(\"change\", onChange);\n      cm.off(\"swapDoc\", onChange);\n      CodeMirror.off(cm.getInputField(), \"compositionupdate\", cm.state.placeholderCompose)\n      clearPlaceholder(cm);\n      var wrapper = cm.getWrapperElement();\n      wrapper.className = wrapper.className.replace(\" CodeMirror-empty\", \"\");\n    }\n\n    if (val && !cm.hasFocus()) onBlur(cm);\n  });\n\n  function clearPlaceholder(cm) {\n    if (cm.state.placeholder) {\n      cm.state.placeholder.parentNode.removeChild(cm.state.placeholder);\n      cm.state.placeholder = null;\n    }\n  }\n  function setPlaceholder(cm) {\n    clearPlaceholder(cm);\n    var elt = cm.state.placeholder = document.createElement(\"pre\");\n    elt.style.cssText = \"height: 0; overflow: visible\";\n    elt.style.direction = cm.getOption(\"direction\");\n    elt.className = \"CodeMirror-placeholder CodeMirror-line-like\";\n    var placeHolder = cm.getOption(\"placeholder\")\n    if (typeof placeHolder == \"string\") placeHolder = document.createTextNode(placeHolder)\n    elt.appendChild(placeHolder)\n    cm.display.lineSpace.insertBefore(elt, cm.display.lineSpace.firstChild);\n  }\n\n  function onComposition(cm) {\n    setTimeout(function() {\n      var empty = false\n      if (cm.lineCount() == 1) {\n        var input = cm.getInputField()\n        empty = input.nodeName == \"TEXTAREA\" ? !cm.getLine(0).length\n          : !/[^\\u200b]/.test(input.querySelector(\".CodeMirror-line\").textContent)\n      }\n      if (empty) setPlaceholder(cm)\n      else clearPlaceholder(cm)\n    }, 20)\n  }\n\n  function onBlur(cm) {\n    if (isEmpty(cm)) setPlaceholder(cm);\n  }\n  function onChange(cm) {\n    var wrapper = cm.getWrapperElement(), empty = isEmpty(cm);\n    wrapper.className = wrapper.className.replace(\" CodeMirror-empty\", \"\") + (empty ? \" CodeMirror-empty\" : \"\");\n\n    if (empty) setPlaceholder(cm);\n    else clearPlaceholder(cm);\n  }\n\n  function isEmpty(cm) {\n    return (cm.lineCount() === 1) && (cm.getLine(0) === \"\");\n  }\n});\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACtB,MAAAA,YAAW,aAAa,eAAe,IAAI,SAAS,IAAI,KAAK,KAAK;AAChE,YAAI,OAAO,OAAO,OAAOA,YAAW;AACpC,YAAI,OAAO,CAAC,MAAM;AAChB,aAAG,GAAG,QAAQ,MAAM;AACpB,aAAG,GAAG,UAAU,QAAQ;AACxB,aAAG,GAAG,WAAW,QAAQ;AACzB,UAAAA,YAAW,GAAG,GAAG,cAAc,GAAG,qBAAqB,GAAG,MAAM,qBAAqB,WAAW;AAAE,0BAAc,EAAE;AAAA,UAAE,CAAC;AACrH,mBAAS,EAAE;AAAA,QACb,WAAW,CAAC,OAAO,MAAM;AACvB,aAAG,IAAI,QAAQ,MAAM;AACrB,aAAG,IAAI,UAAU,QAAQ;AACzB,aAAG,IAAI,WAAW,QAAQ;AAC1B,UAAAA,YAAW,IAAI,GAAG,cAAc,GAAG,qBAAqB,GAAG,MAAM,kBAAkB;AACnF,2BAAiB,EAAE;AACnB,cAAI,UAAU,GAAG,kBAAkB;AACnC,kBAAQ,YAAY,QAAQ,UAAU,QAAQ,qBAAqB,EAAE;AAAA,QACvE;AAEA,YAAI,OAAO,CAAC,GAAG,SAAS;AAAG,iBAAO,EAAE;AAAA,MACtC,CAAC;AAED,eAAS,iBAAiB,IAAI;AAC5B,YAAI,GAAG,MAAM,aAAa;AACxB,aAAG,MAAM,YAAY,WAAW,YAAY,GAAG,MAAM,WAAW;AAChE,aAAG,MAAM,cAAc;AAAA,QACzB;AAAA,MACF;AACA,eAAS,eAAe,IAAI;AAC1B,yBAAiB,EAAE;AACnB,YAAI,MAAM,GAAG,MAAM,cAAc,SAAS,cAAc,KAAK;AAC7D,YAAI,MAAM,UAAU;AACpB,YAAI,MAAM,YAAY,GAAG,UAAU,WAAW;AAC9C,YAAI,YAAY;AAChB,YAAI,cAAc,GAAG,UAAU,aAAa;AAC5C,YAAI,OAAO,eAAe;AAAU,wBAAc,SAAS,eAAe,WAAW;AACrF,YAAI,YAAY,WAAW;AAC3B,WAAG,QAAQ,UAAU,aAAa,KAAK,GAAG,QAAQ,UAAU,UAAU;AAAA,MACxE;AAEA,eAAS,cAAc,IAAI;AACzB,mBAAW,WAAW;AACpB,cAAI,QAAQ;AACZ,cAAI,GAAG,UAAU,KAAK,GAAG;AACvB,gBAAI,QAAQ,GAAG,cAAc;AAC7B,oBAAQ,MAAM,YAAY,aAAa,CAAC,GAAG,QAAQ,CAAC,EAAE,SAClD,CAAC,YAAY,KAAK,MAAM,cAAc,kBAAkB,EAAE,WAAW;AAAA,UAC3E;AACA,cAAI;AAAO,2BAAe,EAAE;AAAA;AACvB,6BAAiB,EAAE;AAAA,QAC1B,GAAG,EAAE;AAAA,MACP;AAEA,eAAS,OAAO,IAAI;AAClB,YAAI,QAAQ,EAAE;AAAG,yBAAe,EAAE;AAAA,MACpC;AACA,eAAS,SAAS,IAAI;AACpB,YAAI,UAAU,GAAG,kBAAkB,GAAG,QAAQ,QAAQ,EAAE;AACxD,gBAAQ,YAAY,QAAQ,UAAU,QAAQ,qBAAqB,EAAE,KAAK,QAAQ,sBAAsB;AAExG,YAAI;AAAO,yBAAe,EAAE;AAAA;AACvB,2BAAiB,EAAE;AAAA,MAC1B;AAEA,eAAS,QAAQ,IAAI;AACnB,eAAQ,GAAG,UAAU,MAAM,KAAO,GAAG,QAAQ,CAAC,MAAM;AAAA,MACtD;AAAA,IACF,CAAC;AAAA;AAAA;", "names": ["CodeMirror"]}