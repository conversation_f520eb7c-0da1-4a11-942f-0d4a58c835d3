import { defineComponent, h, provide, computed, inject, reactive } from 'vue';
import { getConfig, createEvent, useSize } from '../../ui';
import XEUtils from 'xe-utils';
import VxeCheckboxComponent from './checkbox';
export default defineComponent({
    name: 'VxeCheckboxGroup',
    props: {
        modelValue: Array,
        options: Array,
        optionProps: Object,
        disabled: {
            type: Boolean,
            default: null
        },
        max: {
            type: [String, Number],
            default: null
        },
        size: {
            type: String,
            default: () => getConfig().checkboxGroup.size || getConfig().size
        }
    },
    emits: [
        'update:modelValue',
        'change'
    ],
    setup(props, context) {
        const { slots, emit } = context;
        const $xeForm = inject('$xeForm', null);
        const formItemInfo = inject('xeFormItemInfo', null);
        const xID = XEUtils.uniqueId();
        const reactData = reactive({});
        const computeIsDisabled = computed(() => {
            const { disabled } = props;
            if (disabled === null) {
                if ($xeForm) {
                    return $xeForm.props.readonly || $xeForm.props.disabled;
                }
                return false;
            }
            return disabled;
        });
        const computeIsMaximize = computed(() => {
            const { modelValue, max } = props;
            if (max) {
                return (modelValue ? modelValue.length : 0) >= XEUtils.toNumber(max);
            }
            return false;
        });
        const computePropsOpts = computed(() => {
            return Object.assign({}, props.optionProps);
        });
        const computeLabelField = computed(() => {
            const propsOpts = computePropsOpts.value;
            return propsOpts.label || 'label';
        });
        const computeValueField = computed(() => {
            const propsOpts = computePropsOpts.value;
            return propsOpts.value || 'value';
        });
        const computeDisabledField = computed(() => {
            const propsOpts = computePropsOpts.value;
            return propsOpts.disabled || 'disabled';
        });
        const computeMaps = {
            computeIsMaximize,
            computeIsDisabled
        };
        const $xeCheckboxGroup = {
            xID,
            props,
            context,
            reactData,
            getComputeMaps: () => computeMaps
        };
        useSize(props);
        const dispatchEvent = (type, params, evnt) => {
            emit(type, createEvent(evnt, { $checkboxGroup: $xeCheckboxGroup }, params));
        };
        const checkboxGroupMethods = {
            dispatchEvent
        };
        const checkboxGroupPrivateMethods = {
            handleChecked(params, evnt) {
                const { checked, label } = params;
                const checklist = props.modelValue || [];
                const checkIndex = checklist.indexOf(label);
                if (checked) {
                    if (checkIndex === -1) {
                        checklist.push(label);
                    }
                }
                else {
                    checklist.splice(checkIndex, 1);
                }
                emit('update:modelValue', checklist);
                $xeCheckboxGroup.dispatchEvent('change', Object.assign({ checklist }, params), evnt);
                // 自动更新校验状态
                if ($xeForm && formItemInfo) {
                    $xeForm.triggerItemEvent(evnt, formItemInfo.itemConfig.field, checklist);
                }
            }
        };
        Object.assign($xeCheckboxGroup, checkboxGroupMethods, checkboxGroupPrivateMethods);
        const renderVN = () => {
            const { options } = props;
            const defaultSlot = slots.default;
            const valueField = computeValueField.value;
            const labelField = computeLabelField.value;
            const disabledField = computeDisabledField.value;
            return h('div', {
                class: 'vxe-checkbox-group'
            }, defaultSlot
                ? defaultSlot({})
                : (options
                    ? options.map(item => {
                        return h(VxeCheckboxComponent, {
                            key: item[valueField],
                            label: item[valueField],
                            content: item[labelField],
                            disabled: item[disabledField]
                        });
                    })
                    : []));
        };
        provide('$xeCheckboxGroup', $xeCheckboxGroup);
        $xeCheckboxGroup.renderVN = renderVN;
        return renderVN;
    }
});
