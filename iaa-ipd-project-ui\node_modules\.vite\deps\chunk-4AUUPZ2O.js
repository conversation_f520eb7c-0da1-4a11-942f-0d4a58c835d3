import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
} from "./chunk-KE3PEXQO.js";
import {
  require_xor
} from "./chunk-GLAIV4XP.js";
import {
  getSnapshot,
  isAlive
} from "./chunk-GHYHD6QH.js";
import {
  $mobx,
  Reaction,
  allowStateChanges,
  allowStateReadsEnd,
  allowStateReadsStart,
  configure,
  createAtom,
  getDependencyTree,
  isObservableArray,
  isObservableMap,
  isObservableObject,
  observable,
  reaction,
  spy,
  untracked
} from "./chunk-ECE3U5IW.js";
import {
  sortable_esm_default
} from "./chunk-EH2XFRG5.js";
import {
  Action
} from "./chunk-HBYAKOWN.js";
import {
  Badge,
  Checkbox$1,
  SearchBox$1,
  require_intersection,
  require_keycode
} from "./chunk-GFI6XVUE.js";
import {
  Button$1,
  TooltipWrapper$1
} from "./chunk-M5OFQAQB.js";
import {
  <PERSON><PERSON>,
  Spinner$1
} from "./chunk-YPPVVTGH.js";
import {
  __assign,
  __awaiter,
  __decorate,
  __extends,
  __generator,
  __metadata,
  __read,
  __rest,
  __spreadArray,
  __values
} from "./chunk-F25BIIHK.js";
import {
  Animation,
  ColorScale,
  Overlay,
  PopOver$1,
  Renderer,
  RootClose,
  ScopedContext,
  TableStore,
  anyChanged,
  arraySlice,
  autobind,
  buildTrackExpression,
  changedEffect,
  chromeVersion,
  createObject,
  decodeEntity,
  difference,
  eachTree,
  evalTrackExpression,
  filter,
  flattenTree,
  getImageDimensions,
  getMatchedEventTargets,
  getPropValue,
  getRendererByName,
  getScrollParent,
  getStyleNumber,
  getVariable,
  isApiOutdated,
  isArrayChildrenModified,
  isClickOnInput,
  isEffectiveApi,
  isNumeric,
  isObject,
  isPureVariable,
  isSafari,
  keyToPath,
  loopTooMuch,
  matchSorter,
  noop,
  normalizeApi,
  normalizeOptions,
  offset,
  padArr,
  position,
  removeHTMLTag,
  require_FileSaver_min,
  require_debounce,
  require_find,
  require_hoist_non_react_statics_cjs,
  require_omit,
  resizeSensor,
  resolveVariable,
  resolveVariableAndFilter,
  setVariable,
  toDataURL,
  useInView
} from "./chunk-LZQZ2OHM.js";
import {
  require_isPlainObject,
  require_memoize
} from "./chunk-5QW7M2DY.js";
import {
  require_isEqual
} from "./chunk-ZJNJ4ZV3.js";
import {
  init_moment,
  moment_default
} from "./chunk-CNJUQDSN.js";
import {
  require_react_dom
} from "./chunk-7XBFYOTW.js";
import {
  require_react
} from "./chunk-ZUWHLQVA.js";
import {
  __toESM
} from "./chunk-GFT2G5UO.js";

// node_modules/amis/esm/renderers/Table/index.js
var import_react22 = __toESM(require_react());
var import_isEqual = __toESM(require_isEqual());
var import_find = __toESM(require_find());
var import_debounce = __toESM(require_debounce());
var import_intersection = __toESM(require_intersection());
var import_isPlainObject = __toESM(require_isPlainObject());

// node_modules/amis/esm/renderers/Table/TableCell.js
var import_react12 = __toESM(require_react());

// node_modules/amis/esm/renderers/QuickEdit.js
var import_react = __toESM(require_react());
var import_react_dom = __toESM(require_react_dom());
var import_hoist_non_react_statics = __toESM(require_hoist_non_react_statics_cjs());
var import_keycode = __toESM(require_keycode());
var import_omit = __toESM(require_omit());
var inited = false;
var currentOpened;
var HocQuickEdit = function(config) {
  if (config === void 0) {
    config = {};
  }
  return function(Component2) {
    var QuickEditComponent = (
      /** @class */
      function(_super) {
        __extends(QuickEditComponent2, _super);
        function QuickEditComponent2(props) {
          var _this = _super.call(this, props) || this;
          _this.openQuickEdit = _this.openQuickEdit.bind(_this);
          _this.closeQuickEdit = _this.closeQuickEdit.bind(_this);
          _this.handleAction = _this.handleAction.bind(_this);
          _this.handleSubmit = _this.handleSubmit.bind(_this);
          _this.handleKeyUp = _this.handleKeyUp.bind(_this);
          _this.overlayRef = _this.overlayRef.bind(_this);
          _this.handleWindowKeyPress = _this.handleWindowKeyPress.bind(_this);
          _this.handleWindowKeyDown = _this.handleWindowKeyDown.bind(_this);
          _this.formRef = _this.formRef.bind(_this);
          _this.formItemRef = _this.formItemRef.bind(_this);
          _this.handleInit = _this.handleInit.bind(_this);
          _this.handleChange = _this.handleChange.bind(_this);
          _this.handleFormItemChange = _this.handleFormItemChange.bind(_this);
          _this.handleBulkChange = _this.handleBulkChange.bind(_this);
          _this.state = {
            isOpened: false
          };
          return _this;
        }
        QuickEditComponent2.prototype.componentDidMount = function() {
          this.target = (0, import_react_dom.findDOMNode)(this);
          if (inited) {
            return;
          }
          inited = true;
          document.body.addEventListener("keypress", this.handleWindowKeyPress);
          document.body.addEventListener("keydown", this.handleWindowKeyDown);
        };
        QuickEditComponent2.prototype.formRef = function(ref) {
          var _a = this.props, quickEditFormRef = _a.quickEditFormRef, rowIndex = _a.rowIndex, colIndex = _a.colIndex;
          while (ref && ref.getWrappedInstance) {
            ref = ref.getWrappedInstance();
          }
          this.form = ref;
          quickEditFormRef === null || quickEditFormRef === void 0 ? void 0 : quickEditFormRef(ref, colIndex, rowIndex);
        };
        QuickEditComponent2.prototype.formItemRef = function(ref) {
          var _a = this.props, quickEditFormItemRef = _a.quickEditFormItemRef, rowIndex = _a.rowIndex, colIndex = _a.colIndex;
          while (ref && ref.getWrappedInstance) {
            ref = ref.getWrappedInstance();
          }
          this.formItem = ref;
          quickEditFormItemRef === null || quickEditFormItemRef === void 0 ? void 0 : quickEditFormItemRef(ref, colIndex, rowIndex);
        };
        QuickEditComponent2.prototype.handleWindowKeyPress = function(e) {
          var ns = this.props.classPrefix;
          var el = e.target.closest(".".concat(ns, "Field--quickEditable"));
          if (!el) {
            return;
          }
          var table = el.closest("table");
          if (!table) {
            return;
          }
          if ((0, import_keycode.default)(e) === "space" && !~["INPUT", "TEXTAREA"].indexOf(el.tagName)) {
            e.preventDefault();
            e.stopPropagation();
          }
        };
        QuickEditComponent2.prototype.handleWindowKeyDown = function(e) {
          var code = (0, import_keycode.default)(e);
          if (code === "esc" && currentOpened) {
            currentOpened.closeQuickEdit();
          } else if (~["INPUT", "TEXTAREA"].indexOf(e.target.tagName) || e.target.contentEditable === "true" || !~["up", "down", "left", "right"].indexOf(code)) {
            return;
          }
          e.preventDefault();
          var ns = this.props.classPrefix;
          var el = e.target.closest(".".concat(ns, "Field--quickEditable")) || document.querySelector(".".concat(ns, "Field--quickEditable"));
          if (!el) {
            return;
          }
          var table = el.closest("table");
          if (!table) {
            return;
          }
          var current = table.querySelector(".".concat(ns, "Field--quickEditable:focus"));
          if (!current) {
            var dom = table.querySelector(".".concat(ns, "Field--quickEditable[tabindex]"));
            dom && dom.focus();
          } else {
            var prevTr = void 0, nextTr = void 0, prevTd = void 0, nextTd = void 0;
            switch (code) {
              case "up":
                prevTr = current.parentNode.previousSibling;
                if (prevTr) {
                  var index = current.cellIndex;
                  prevTr.children[index].focus();
                }
                break;
              case "down":
                nextTr = current.parentNode.nextSibling;
                if (nextTr) {
                  var index = current.cellIndex;
                  nextTr.children[index].focus();
                }
                break;
              case "left":
                prevTd = current.previousElementSibling;
                while (prevTd) {
                  if (prevTd.matches(".".concat(ns, "Field--quickEditable[tabindex]"))) {
                    break;
                  }
                  prevTd = prevTd.previousElementSibling;
                }
                if (prevTd) {
                  prevTd.focus();
                } else if (current.parentNode.previousSibling) {
                  var tds = current.parentNode.previousSibling.querySelectorAll(".".concat(ns, "Field--quickEditable[tabindex]"));
                  if (tds.length) {
                    tds[tds.length - 1].focus();
                  }
                }
                break;
              case "right":
                nextTd = current.nextSibling;
                while (nextTd) {
                  if (nextTd.matches(".".concat(ns, "Field--quickEditable[tabindex]"))) {
                    break;
                  }
                  nextTd = nextTd.nextSibling;
                }
                if (nextTd) {
                  nextTd.focus();
                } else if (current.parentNode.nextSibling) {
                  nextTd = current.parentNode.nextSibling.querySelector(".".concat(ns, "Field--quickEditable[tabindex]"));
                  if (nextTd) {
                    nextTd.focus();
                  }
                }
                break;
            }
          }
        };
        QuickEditComponent2.prototype.overlayRef = function(ref) {
          this.overlay = ref;
        };
        QuickEditComponent2.prototype.handleAction = function(e, action, ctx) {
          var onAction = this.props.onAction;
          if (action.actionType === "cancel" || action.actionType === "close") {
            this.closeQuickEdit();
            return;
          }
          onAction && onAction(e, action, ctx);
        };
        QuickEditComponent2.prototype.handleSubmit = function(values) {
          var _a = this.props, onQuickChange = _a.onQuickChange, quickEdit = _a.quickEdit;
          this.closeQuickEdit();
          onQuickChange(values, quickEdit.saveImmediately, false, quickEdit);
          return false;
        };
        QuickEditComponent2.prototype.handleInit = function(values) {
          var _a = this.props, onQuickChange = _a.onQuickChange, data = _a.data;
          var diff = difference(values, data);
          Object.keys(diff).length && onQuickChange(diff, false, true);
        };
        QuickEditComponent2.prototype.handleChange = function(values, diff) {
          var _a = this.props, onQuickChange = _a.onQuickChange, quickEdit = _a.quickEdit;
          Object.keys(diff).length && onQuickChange(
            diff,
            // 只变化差异部分，其他值有可能是旧的
            quickEdit.saveImmediately,
            false,
            quickEdit
          );
        };
        QuickEditComponent2.prototype.handleFormItemChange = function(value) {
          var _a = this.props, onQuickChange = _a.onQuickChange, quickEdit = _a.quickEdit, name = _a.name;
          var data = {};
          setVariable(data, name, value);
          onQuickChange(data, quickEdit.saveImmediately, false, quickEdit);
        };
        QuickEditComponent2.prototype.handleBulkChange = function(values) {
          var _a = this.props, onQuickChange = _a.onQuickChange, quickEdit = _a.quickEdit;
          onQuickChange(values, quickEdit.saveImmediately, false, quickEdit);
        };
        QuickEditComponent2.prototype.openQuickEdit = function() {
          currentOpened = this;
          this.setState({
            isOpened: true
          });
        };
        QuickEditComponent2.prototype.closeQuickEdit = function() {
          var _this = this;
          if (!this.state.isOpened) {
            return;
          }
          currentOpened = null;
          var ns = this.props.classPrefix;
          this.setState({
            isOpened: false
          }, function() {
            var el = (0, import_react_dom.findDOMNode)(_this);
            var table = el.closest("table");
            (table && table.querySelectorAll("td.".concat(ns, "Field--quickEditable:focus")).length || el) && el.focus();
          });
        };
        QuickEditComponent2.prototype.buildSchema = function() {
          var _a = this.props, quickEdit = _a.quickEdit, name = _a.name, label = _a.label, __ = _a.translate, id = _a.id;
          var schema;
          var isline = quickEdit.mode === "inline";
          if (quickEdit === true) {
            schema = {
              type: "form",
              title: "",
              autoFocus: true,
              body: [
                {
                  type: "input-text",
                  name,
                  placeholder: label,
                  label: false
                }
              ]
            };
          } else if (quickEdit) {
            if (quickEdit === null || quickEdit === void 0 ? void 0 : quickEdit.isFormMode) {
              schema = {
                mode: "normal",
                type: "form",
                wrapWithPanel: false,
                body: [
                  __assign(__assign({}, (0, import_omit.default)(quickEdit, "isFormMode")), { label: false })
                ]
              };
            } else if (quickEdit.body && !~["combo", "group", "panel", "fieldSet", "fieldset"].indexOf(quickEdit.type)) {
              schema = __assign(__assign({ title: "", autoFocus: !isline }, quickEdit), { mode: "normal", type: "form" });
            } else {
              schema = {
                title: "",
                className: quickEdit.formClassName,
                type: "form",
                autoFocus: !isline,
                mode: "normal",
                body: [
                  __assign(__assign(__assign({ type: quickEdit.type || "input-text", name: quickEdit.name || name }, isline ? { id } : {}), quickEdit), { mode: void 0 })
                ]
              };
            }
          }
          var isFormMode = quickEdit === null || quickEdit === void 0 ? void 0 : quickEdit.isFormMode;
          if (schema) {
            schema = __assign(__assign({}, schema), { wrapWithPanel: !(isline || isFormMode), actions: isline || isFormMode ? [] : [
              {
                type: "button",
                label: __("cancel"),
                actionType: "cancel"
              },
              {
                label: __("confirm"),
                type: "submit",
                primary: true
              }
            ] });
          }
          return schema || "error";
        };
        QuickEditComponent2.prototype.handleKeyUp = function(e) {
          var _a, _b, _c, _d;
          var code = (0, import_keycode.default)(e);
          if (code === "space" && !~["INPUT", "TEXTAREA"].indexOf(e.target.tagName)) {
            e.preventDefault();
            e.stopPropagation();
            if (this.formItem) {
              (_b = (_a = this.formItem) === null || _a === void 0 ? void 0 : _a.focus) === null || _b === void 0 ? void 0 : _b.call(_a);
            } else if (this.form) {
              (_d = (_c = this.form) === null || _c === void 0 ? void 0 : _c.focus) === null || _d === void 0 ? void 0 : _d.call(_c);
            } else {
              this.openQuickEdit();
            }
          }
        };
        QuickEditComponent2.prototype.renderPopOver = function() {
          var _this = this;
          var _a = this.props, quickEdit = _a.quickEdit, render = _a.render, popOverContainer = _a.popOverContainer, ns = _a.classPrefix, cx = _a.classnames, canAccessSuperData = _a.canAccessSuperData;
          var content = import_react.default.createElement("div", { ref: this.overlayRef, className: cx(quickEdit.className) }, render("quick-edit-form", this.buildSchema(), {
            value: void 0,
            defaultStatic: false,
            onSubmit: this.handleSubmit,
            onAction: this.handleAction,
            onChange: null,
            formLazyChange: false,
            ref: this.formRef,
            popOverContainer: function() {
              return _this.overlay;
            },
            canAccessSuperData,
            formStore: void 0
          }));
          popOverContainer = popOverContainer || function() {
            return (0, import_react_dom.findDOMNode)(_this);
          };
          return import_react.default.createElement(
            Overlay,
            { container: popOverContainer, target: function() {
              return _this.target;
            }, onHide: this.closeQuickEdit, placement: "left-top right-top left-bottom right-bottom left-top-right-top left-bottom-right-bottom left-top", show: true },
            import_react.default.createElement(PopOver$1, { classPrefix: ns, className: cx("".concat(ns, "QuickEdit-popover"), quickEdit.popOverClassName), onHide: this.closeQuickEdit, overlay: true }, content)
          );
        };
        QuickEditComponent2.prototype.renderInlineForm = function() {
          var _a, _b;
          var _c = this.props, render = _c.render, cx = _c.classnames, canAccessSuperData = _c.canAccessSuperData, disabled = _c.disabled, value = _c.value, name = _c.name;
          var schema = this.buildSchema();
          if (Array.isArray(schema.body) && schema.body.length === 1 && !schema.body[0].unique && // 唯一模式还不支持
          !schema.body[0].value && // 不能有默认值表达式什么的情况
          !((_a = schema.body[0]) === null || _a === void 0 ? void 0 : _a.extraName) && schema.body[0].name && schema.body[0].name === name && schema.body[0].type && ((_b = getRendererByName(schema.body[0].type)) === null || _b === void 0 ? void 0 : _b.isFormItem)) {
            return import_react.default.createElement(InlineFormItem, __assign({}, this.props, { schema: schema.body[0], onChange: this.handleFormItemChange, onBulkChange: this.handleBulkChange, formItemRef: this.formItemRef }));
          }
          return render("inline-form", schema, {
            value: void 0,
            wrapperComponent: "div",
            className: cx("Form--quickEdit"),
            ref: this.formRef,
            simpleMode: true,
            onInit: this.handleInit,
            onChange: this.handleChange,
            onBulkChange: this.handleBulkChange,
            formLazyChange: false,
            canAccessSuperData,
            disabled,
            defaultStatic: false,
            // 不下发这下面的属性，否则当使用表格类型的 Picker 时（或其他会用到 Table 的自定义组件），会导致一些异常行为
            buildItemProps: null,
            // quickEditFormRef: null,
            // ^ 不知道为什么，这里不能阻挡下发，否则单测 Renderer:input-table formula 过不了
            quickEditFormItemRef: null
          });
        };
        QuickEditComponent2.prototype.render = function() {
          var _a = this.props, onQuickChange = _a.onQuickChange, quickEdit = _a.quickEdit, quickEditEnabled = _a.quickEditEnabled, className = _a.className, cx = _a.classnames, render = _a.render, noHoc = _a.noHoc, canAccessSuperData = _a.canAccessSuperData, disabled = _a.disabled, isStatic = _a.static;
          var _b = this.props, buildItemProps = _b.buildItemProps, quickEditFormRef = _b.quickEditFormRef, quickEditFormItemRef = _b.quickEditFormItemRef, restProps = __rest(_b, ["buildItemProps", "quickEditFormRef", "quickEditFormItemRef"]);
          if (isStatic || !quickEdit || !onQuickChange || !(typeof quickEdit === "object" && (quickEdit === null || quickEdit === void 0 ? void 0 : quickEdit.isQuickEditFormMode)) && quickEditEnabled === false || noHoc) {
            return import_react.default.createElement(Component2, __assign({}, restProps, { formItemRef: this.formItemRef }));
          }
          if (quickEdit.mode === "inline" || quickEdit.isFormMode) {
            return import_react.default.createElement(Component2, __assign({}, restProps, { className: cx("Field--quickEditable", className), tabIndex: quickEdit.focusable === false ? void 0 : "0", onKeyUp: disabled ? noop : this.handleKeyUp }), this.renderInlineForm());
          } else {
            return import_react.default.createElement(
              Component2,
              __assign({}, restProps, { className: cx("Field--quickEditable", className, {
                in: this.state.isOpened
              }), tabIndex: quickEdit.focusable === false ? void 0 : "0", onKeyUp: disabled ? noop : this.handleKeyUp }),
              import_react.default.createElement(Component2, __assign({}, restProps, { contentsOnly: true, noHoc: true })),
              disabled ? null : render("quick-edit-button", {
                type: "button",
                tabIndex: "-1",
                onClick: this.openQuickEdit,
                className: "Field-quickEditBtn",
                icon: quickEdit.icon || "edit",
                level: "link"
              }),
              this.state.isOpened ? this.renderPopOver() : null
            );
          }
        };
        QuickEditComponent2.ComposedComponent = Component2;
        return QuickEditComponent2;
      }(import_react.default.PureComponent)
    );
    (0, import_hoist_non_react_statics.default)(QuickEditComponent, Component2);
    return QuickEditComponent;
  };
};
function InlineFormItem(props) {
  var _a;
  var render = props.render, schema = props.schema, data = props.data, onChange = props.onChange, onBulkChange = props.onBulkChange, formItemRef = props.formItemRef, canAccessSuperData = props.canAccessSuperData;
  canAccessSuperData && import_react.default.useEffect(function() {
    var value = getPropValue(props);
    if (value && value !== getPropValue(__assign(__assign({}, props), { canAccessSuperData: false }))) {
      onChange(value);
    }
  }, []);
  return render("inline-form-item", schema, {
    mode: "normal",
    value: (_a = getPropValue(props)) !== null && _a !== void 0 ? _a : "",
    onChange,
    onBulkChange,
    formItemRef,
    defaultStatic: false,
    // 不下发下面的属性，否则当使用表格类型的 Picker 时（或其他会用到 Table 的自定义组件），会导致一些异常行为
    buildItemProps: null,
    quickEditFormRef: null,
    quickEditFormItemRef: null
  });
}

// node_modules/amis/esm/renderers/Copyable.js
var import_react2 = __toESM(require_react());
var import_hoist_non_react_statics2 = __toESM(require_hoist_non_react_statics_cjs());
var HocCopyable = function() {
  return function(Component2) {
    var QuickEditComponent = (
      /** @class */
      function(_super) {
        __extends(QuickEditComponent2, _super);
        function QuickEditComponent2() {
          return _super !== null && _super.apply(this, arguments) || this;
        }
        QuickEditComponent2.prototype.handleClick = function(content) {
          var _a = this.props, env = _a.env, copyFormat = _a.copyFormat;
          env.copy && env.copy(content, { format: copyFormat });
        };
        QuickEditComponent2.prototype.render = function() {
          var _a = this.props, name = _a.name, className = _a.className, data = _a.data, noHoc = _a.noHoc, cx = _a.classnames, __ = _a.translate, env = _a.env, tooltipContainer = _a.tooltipContainer;
          var copyable = this.props.copyable;
          if (copyable && !noHoc) {
            var content = filter(copyable.content || "${" + name + " | raw }", data);
            var tooltip = (copyable === null || copyable === void 0 ? void 0 : copyable.tooltip) != null ? filter(copyable.tooltip, data) : copyable === null || copyable === void 0 ? void 0 : copyable.tooltip;
            if (content) {
              return import_react2.default.createElement(
                Component2,
                __assign({}, this.props, { className: cx("Field--copyable", className) }),
                import_react2.default.createElement(Component2, __assign({}, this.props, { contentsOnly: true, noHoc: true })),
                import_react2.default.createElement(
                  TooltipWrapper$1,
                  { placement: "right", tooltip: tooltip !== null && tooltip !== void 0 ? tooltip : __("Copyable.tip"), trigger: "hover", container: tooltipContainer || (env === null || env === void 0 ? void 0 : env.getModalContainer) },
                  import_react2.default.createElement(
                    "a",
                    { key: "edit-btn", className: cx("Field-copyBtn"), onClick: this.handleClick.bind(this, content) },
                    import_react2.default.createElement(Icon, { icon: "copy", className: "icon" })
                  )
                )
              );
            }
          }
          return import_react2.default.createElement(Component2, __assign({}, this.props));
        };
        QuickEditComponent2.ComposedComponent = Component2;
        return QuickEditComponent2;
      }(import_react2.default.PureComponent)
    );
    (0, import_hoist_non_react_statics2.default)(QuickEditComponent, Component2);
    return QuickEditComponent;
  };
};

// node_modules/amis/esm/renderers/PopOver.js
var import_react3 = __toESM(require_react());
var import_react_dom2 = __toESM(require_react_dom());
var import_hoist_non_react_statics3 = __toESM(require_hoist_non_react_statics_cjs());
var HocPopOver = function(config) {
  if (config === void 0) {
    config = {};
  }
  return function(Component2) {
    var lastOpenedInstance = null;
    var PopOverComponent = (
      /** @class */
      function(_super) {
        __extends(PopOverComponent2, _super);
        function PopOverComponent2(props) {
          var _this = _super.call(this, props) || this;
          _this.openPopOver = _this.openPopOver.bind(_this);
          _this.closePopOver = _this.closePopOver.bind(_this);
          _this.closePopOverLater = _this.closePopOverLater.bind(_this);
          _this.clearCloseTimer = _this.clearCloseTimer.bind(_this);
          _this.targetRef = _this.targetRef.bind(_this);
          _this.state = {
            isOpened: false
          };
          return _this;
        }
        PopOverComponent2.prototype.targetRef = function(ref) {
          this.target = ref;
        };
        PopOverComponent2.prototype.openPopOver = function(event) {
          var _this = this;
          var onPopOverOpened = this.props.onPopOverOpened;
          lastOpenedInstance === null || lastOpenedInstance === void 0 ? void 0 : lastOpenedInstance.closePopOver();
          lastOpenedInstance = this;
          var e = event.currentTarget;
          if (this.getClassName() === "ellipsis" && e && e.clientHeight >= e.scrollHeight || this.getClassName() === "noWrap") {
            return;
          }
          this.setState({
            isOpened: true
          }, function() {
            return onPopOverOpened && onPopOverOpened(_this.props.popOver);
          });
        };
        PopOverComponent2.prototype.closePopOver = function() {
          var _this = this;
          clearTimeout(this.timer);
          if (!this.state.isOpened) {
            return;
          }
          lastOpenedInstance = null;
          var onPopOverClosed = this.props.onPopOverClosed;
          this.setState({
            isOpened: false
          }, function() {
            return onPopOverClosed && onPopOverClosed(_this.props.popOver);
          });
        };
        PopOverComponent2.prototype.closePopOverLater = function() {
          this.timer = setTimeout(this.closePopOver, 500);
        };
        PopOverComponent2.prototype.clearCloseTimer = function() {
          clearTimeout(this.timer);
        };
        PopOverComponent2.prototype.buildSchema = function() {
          var _a = this.props, popOver = _a.popOver, name = _a.name, label = _a.label, __ = _a.translate;
          var schema;
          if (popOver === true) {
            schema = {
              type: "panel",
              body: "${".concat(name, "}")
            };
          } else if (popOver && (popOver.mode === "dialog" || popOver.mode === "drawer")) {
            schema = __assign(__assign({ actions: [
              {
                label: __("Dialog.close"),
                type: "button",
                actionType: "cancel"
              }
            ] }, popOver), { type: popOver.mode });
          } else if (typeof popOver === "string") {
            schema = {
              type: "panel",
              body: popOver
            };
          } else if (popOver) {
            schema = __assign({ type: "panel" }, popOver);
          } else if (this.getClassName() === "ellipsis") {
            schema = {
              type: "panel",
              body: "${".concat(name, "}")
            };
          }
          return schema || "error";
        };
        PopOverComponent2.prototype.getOffset = function() {
          var popOver = this.props.popOver;
          if (!popOver || typeof popOver === "boolean" || !popOver.offset) {
            return void 0;
          }
          return {
            x: popOver.offset.left || 0,
            y: popOver.offset.top || 0
          };
        };
        PopOverComponent2.prototype.renderPopOver = function() {
          var _this = this;
          var _a = this.props, popOver = _a.popOver, render = _a.render, popOverContainer = _a.popOverContainer, cx = _a.classnames, ns = _a.classPrefix;
          if (popOver && (popOver.mode === "dialog" || popOver.mode === "drawer")) {
            return render("popover-detail", this.buildSchema(), {
              show: true,
              onClose: this.closePopOver,
              onConfirm: this.closePopOver
            });
          }
          var content = render("popover-detail", this.buildSchema(), {
            className: cx(popOver && popOver.className)
          });
          if (!popOverContainer) {
            popOverContainer = function() {
              return (0, import_react_dom2.findDOMNode)(_this);
            };
          }
          var selectClassName = this.getClassName();
          var defaultPositon = selectClassName === "ellipsis" && !popOver ? "right-top-center-bottom" : "center";
          var position2 = popOver && popOver.position || "";
          var isFixed = /^fixed\-/.test(position2);
          return isFixed ? import_react3.default.createElement(RootClose, { disabled: !this.state.isOpened, onRootClose: this.closePopOver }, function(ref) {
            return import_react3.default.createElement("div", { className: cx("PopOverAble--fixed PopOverAble--".concat(position2)), onMouseLeave: (popOver === null || popOver === void 0 ? void 0 : popOver.trigger) === "hover" ? _this.closePopOver : void 0, onMouseEnter: (popOver === null || popOver === void 0 ? void 0 : popOver.trigger) === "hover" ? _this.clearCloseTimer : void 0, ref }, content);
          }) : import_react3.default.createElement(
            Overlay,
            { container: popOverContainer, placement: position2 || config.position || defaultPositon, target: function() {
              return _this.target;
            }, onHide: this.closePopOver, rootClose: true, show: true },
            import_react3.default.createElement(PopOver$1, { classPrefix: ns, className: cx("PopOverAble-popover", popOver && popOver.popOverClassName), offset: this.getOffset(), onMouseLeave: (popOver === null || popOver === void 0 ? void 0 : popOver.trigger) === "hover" || selectClassName ? this.closePopOver : void 0, onMouseEnter: (popOver === null || popOver === void 0 ? void 0 : popOver.trigger) === "hover" || selectClassName ? this.clearCloseTimer : void 0 }, content)
          );
        };
        PopOverComponent2.prototype.getClassName = function() {
          var textOverflow = this.props.textOverflow;
          return textOverflow === "default" ? "" : textOverflow;
        };
        PopOverComponent2.prototype.render = function() {
          var _a = this.props, popOver = _a.popOver, popOverEnabled = _a.popOverEnabled, popOverEnable = _a.popOverEnable, className = _a.className, noHoc = _a.noHoc, width = _a.width, cx = _a.classnames, showIcon = _a.showIcon;
          var selectClassName = this.getClassName();
          if (!popOver && !selectClassName || popOverEnabled === false || noHoc || popOverEnable === false) {
            return import_react3.default.createElement(Component2, __assign({}, this.props));
          }
          var triggerProps = {};
          var trigger = popOver === null || popOver === void 0 ? void 0 : popOver.trigger;
          if (trigger === "hover" || selectClassName === "ellipsis" && !popOver) {
            triggerProps.onMouseEnter = this.openPopOver;
            triggerProps.onMouseLeave = this.closePopOverLater;
          } else {
            triggerProps.onClick = this.openPopOver;
          }
          return import_react3.default.createElement(Component2, __assign({}, this.props, { className: cx("Field--popOverAble", className, {
            in: this.state.isOpened
          }), ref: config.targetOutter ? this.targetRef : void 0 }), (popOver === null || popOver === void 0 ? void 0 : popOver.showIcon) !== false && popOver ? import_react3.default.createElement(
            import_react3.default.Fragment,
            null,
            import_react3.default.createElement(Component2, __assign({}, this.props, { contentsOnly: true, noHoc: true })),
            import_react3.default.createElement(
              "span",
              __assign({ key: "popover-btn", className: cx("Field-popOverBtn") }, triggerProps, { ref: config.targetOutter ? void 0 : this.targetRef }),
              import_react3.default.createElement(Icon, { icon: "zoom-in", className: "icon" })
            ),
            this.state.isOpened ? this.renderPopOver() : null
          ) : import_react3.default.createElement(
            import_react3.default.Fragment,
            null,
            import_react3.default.createElement(
              "div",
              __assign({ className: cx("Field-popOverWrap", selectClassName ? "Field-popOverWrap-" + selectClassName : "") }, triggerProps, { ref: config.targetOutter ? void 0 : this.targetRef }),
              import_react3.default.createElement(Component2, __assign({}, this.props, { contentsOnly: true, noHoc: true }))
            ),
            this.state.isOpened ? this.renderPopOver() : null
          ));
        };
        PopOverComponent2.ComposedComponent = Component2;
        return PopOverComponent2;
      }(import_react3.default.Component)
    );
    (0, import_hoist_non_react_statics3.default)(PopOverComponent, Component2);
    return PopOverComponent;
  };
};

// node_modules/amis/node_modules/mobx-react/dist/mobxreact.esm.js
var import_react11 = __toESM(require_react());

// node_modules/amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/assertEnvironment.js
var import_react4 = __toESM(require_react());
if (!import_react4.useState) {
  throw new Error("mobx-react-lite requires React with Hooks support");
}
if (!spy) {
  throw new Error("mobx-react-lite requires mobx at least version 4 to be available");
}

// node_modules/amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/utils/reactBatchedUpdates.js
var import_react_dom3 = __toESM(require_react_dom());

// node_modules/amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/utils.js
var import_react5 = __toESM(require_react());
var __read2 = function(o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m)
    return o;
  var i = m.call(o), r, ar = [], e;
  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done)
      ar.push(r.value);
  } catch (error) {
    e = { error };
  } finally {
    try {
      if (r && !r.done && (m = i["return"]))
        m.call(i);
    } finally {
      if (e)
        throw e.error;
    }
  }
  return ar;
};
function useForceUpdate() {
  var _a = __read2((0, import_react5.useState)(0), 2), setTick = _a[1];
  var update = (0, import_react5.useCallback)(function() {
    setTick(function(tick) {
      return tick + 1;
    });
  }, []);
  return update;
}
function getSymbol(name) {
  if (typeof Symbol === "function") {
    return Symbol.for(name);
  }
  return "__$mobx-react " + name + "__";
}
var mockGlobal = {};
function getGlobal() {
  if (typeof window !== "undefined") {
    return window;
  }
  if (typeof global !== "undefined") {
    return global;
  }
  if (typeof self !== "undefined") {
    return self;
  }
  return mockGlobal;
}

// node_modules/amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/observerBatching.js
var observerBatchingConfiguredSymbol = getSymbol("observerBatching");
function defaultNoopBatch(callback) {
  callback();
}
function observerBatching(reactionScheduler) {
  if (!reactionScheduler) {
    reactionScheduler = defaultNoopBatch;
    if (true) {
      console.warn("[MobX] Failed to get unstable_batched updates from react-dom / react-native");
    }
  }
  configure({ reactionScheduler });
  getGlobal()[observerBatchingConfiguredSymbol] = true;
}

// node_modules/amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/staticRendering.js
var globalIsUsingStaticRendering = false;
function isUsingStaticRendering() {
  return globalIsUsingStaticRendering;
}

// node_modules/amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/observer.js
var import_react8 = __toESM(require_react());

// node_modules/amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/useObserver.js
var import_react7 = __toESM(require_react());

// node_modules/amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/printDebugValue.js
function printDebugValue(v) {
  return getDependencyTree(v);
}

// node_modules/amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/reactionCleanupTracking.js
function createTrackingData(reaction2) {
  var trackingData = {
    cleanAt: Date.now() + CLEANUP_LEAKED_REACTIONS_AFTER_MILLIS,
    reaction: reaction2
  };
  return trackingData;
}
var CLEANUP_LEAKED_REACTIONS_AFTER_MILLIS = 1e4;
var CLEANUP_TIMER_LOOP_MILLIS = 1e4;
var uncommittedReactionRefs = /* @__PURE__ */ new Set();
var reactionCleanupHandle;
function ensureCleanupTimerRunning() {
  if (reactionCleanupHandle === void 0) {
    reactionCleanupHandle = setTimeout(cleanUncommittedReactions, CLEANUP_TIMER_LOOP_MILLIS);
  }
}
function scheduleCleanupOfReactionIfLeaked(ref) {
  uncommittedReactionRefs.add(ref);
  ensureCleanupTimerRunning();
}
function recordReactionAsCommitted(reactionRef) {
  uncommittedReactionRefs.delete(reactionRef);
}
function cleanUncommittedReactions() {
  reactionCleanupHandle = void 0;
  var now = Date.now();
  uncommittedReactionRefs.forEach(function(ref) {
    var tracking = ref.current;
    if (tracking) {
      if (now >= tracking.cleanAt) {
        tracking.reaction.dispose();
        ref.current = null;
        uncommittedReactionRefs.delete(ref);
      }
    }
  });
  if (uncommittedReactionRefs.size > 0) {
    ensureCleanupTimerRunning();
  }
}

// node_modules/amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/useQueuedForceUpdate.js
var import_react6 = __toESM(require_react());
var insideRender = false;
var forceUpdateQueue = [];
function useQueuedForceUpdate(forceUpdate) {
  return function() {
    if (insideRender) {
      forceUpdateQueue.push(forceUpdate);
    } else {
      forceUpdate();
    }
  };
}
function useQueuedForceUpdateBlock(callback) {
  insideRender = true;
  forceUpdateQueue = [];
  try {
    var result = callback();
    insideRender = false;
    var queue_1 = forceUpdateQueue.length > 0 ? forceUpdateQueue : void 0;
    import_react6.default.useLayoutEffect(function() {
      if (queue_1) {
        queue_1.forEach(function(x) {
          return x();
        });
      }
    }, [queue_1]);
    return result;
  } finally {
    insideRender = false;
  }
}

// node_modules/amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/useObserver.js
var EMPTY_OBJECT = {};
function observerComponentNameFor(baseComponentName) {
  return "observer" + baseComponentName;
}
function useObserver(fn, baseComponentName, options) {
  if (baseComponentName === void 0) {
    baseComponentName = "observed";
  }
  if (options === void 0) {
    options = EMPTY_OBJECT;
  }
  if (isUsingStaticRendering()) {
    return fn();
  }
  var wantedForceUpdateHook = options.useForceUpdate || useForceUpdate;
  var forceUpdate = wantedForceUpdateHook();
  var queuedForceUpdate = useQueuedForceUpdate(forceUpdate);
  var reactionTrackingRef = import_react7.default.useRef(null);
  if (!reactionTrackingRef.current) {
    var newReaction_1 = new Reaction(observerComponentNameFor(baseComponentName), function() {
      if (trackingData_1.mounted) {
        queuedForceUpdate();
      } else {
        newReaction_1.dispose();
        reactionTrackingRef.current = null;
      }
    });
    var trackingData_1 = createTrackingData(newReaction_1);
    reactionTrackingRef.current = trackingData_1;
    scheduleCleanupOfReactionIfLeaked(reactionTrackingRef);
  }
  var reaction2 = reactionTrackingRef.current.reaction;
  import_react7.default.useDebugValue(reaction2, printDebugValue);
  import_react7.default.useEffect(function() {
    recordReactionAsCommitted(reactionTrackingRef);
    if (reactionTrackingRef.current) {
      reactionTrackingRef.current.mounted = true;
    } else {
      reactionTrackingRef.current = {
        reaction: new Reaction(observerComponentNameFor(baseComponentName), function() {
          queuedForceUpdate();
        }),
        cleanAt: Infinity
      };
      queuedForceUpdate();
    }
    return function() {
      reactionTrackingRef.current.reaction.dispose();
      reactionTrackingRef.current = null;
    };
  }, []);
  return useQueuedForceUpdateBlock(function() {
    var rendering;
    var exception;
    reaction2.track(function() {
      try {
        rendering = fn();
      } catch (e) {
        exception = e;
      }
    });
    if (exception) {
      throw exception;
    }
    return rendering;
  });
}

// node_modules/amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/observer.js
var __assign2 = function() {
  __assign2 = Object.assign || function(t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];
      for (var p in s)
        if (Object.prototype.hasOwnProperty.call(s, p))
          t[p] = s[p];
    }
    return t;
  };
  return __assign2.apply(this, arguments);
};
function observer(baseComponent, options) {
  if (isUsingStaticRendering()) {
    return baseComponent;
  }
  var realOptions = __assign2({ forwardRef: false }, options);
  var baseComponentName = baseComponent.displayName || baseComponent.name;
  var wrappedComponent = function(props, ref) {
    return useObserver(function() {
      return baseComponent(props, ref);
    }, baseComponentName);
  };
  wrappedComponent.displayName = baseComponentName;
  var memoComponent;
  if (realOptions.forwardRef) {
    memoComponent = (0, import_react8.memo)((0, import_react8.forwardRef)(wrappedComponent));
  } else {
    memoComponent = (0, import_react8.memo)(wrappedComponent);
  }
  copyStaticProperties(baseComponent, memoComponent);
  memoComponent.displayName = baseComponentName;
  return memoComponent;
}
var hoistBlackList = {
  $$typeof: true,
  render: true,
  compare: true,
  type: true
};
function copyStaticProperties(base, target) {
  Object.keys(base).forEach(function(key) {
    if (!hoistBlackList[key]) {
      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(base, key));
    }
  });
}

// node_modules/amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/ObserverComponent.js
function ObserverComponent(_a) {
  var children = _a.children, render = _a.render;
  var component = children || render;
  if (typeof component !== "function") {
    return null;
  }
  return useObserver(component);
}
ObserverComponent.propTypes = {
  children: ObserverPropsCheck,
  render: ObserverPropsCheck
};
ObserverComponent.displayName = "Observer";
function ObserverPropsCheck(props, key, componentName, location, propFullName) {
  var extraKey = key === "children" ? "render" : "children";
  var hasProp = typeof props[key] === "function";
  var hasExtraProp = typeof props[extraKey] === "function";
  if (hasProp && hasExtraProp) {
    return new Error("MobX Observer: Do not use children and render in the same time in`" + componentName);
  }
  if (hasProp || hasExtraProp) {
    return null;
  }
  return new Error("Invalid prop `" + propFullName + "` of type `" + typeof props[key] + "` supplied to `" + componentName + "`, expected `function`.");
}

// node_modules/amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/useAsObservableSource.js
var import_react9 = __toESM(require_react());

// node_modules/amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/useLocalStore.js
var import_react10 = __toESM(require_react());

// node_modules/amis/node_modules/mobx-react/node_modules/mobx-react-lite/es/index.js
observerBatching(import_react_dom3.unstable_batchedUpdates);

// node_modules/amis/node_modules/mobx-react/dist/mobxreact.esm.js
var symbolId = 0;
function createSymbol(name) {
  if (typeof Symbol === "function") {
    return Symbol(name);
  }
  var symbol = "__$mobx-react " + name + " (" + symbolId + ")";
  symbolId++;
  return symbol;
}
var createdSymbols = {};
function newSymbol(name) {
  if (!createdSymbols[name]) {
    createdSymbols[name] = createSymbol(name);
  }
  return createdSymbols[name];
}
function shallowEqual(objA, objB) {
  if (is(objA, objB))
    return true;
  if (typeof objA !== "object" || objA === null || typeof objB !== "object" || objB === null) {
    return false;
  }
  var keysA = Object.keys(objA);
  var keysB = Object.keys(objB);
  if (keysA.length !== keysB.length)
    return false;
  for (var i = 0; i < keysA.length; i++) {
    if (!Object.hasOwnProperty.call(objB, keysA[i]) || !is(objA[keysA[i]], objB[keysA[i]])) {
      return false;
    }
  }
  return true;
}
function is(x, y) {
  if (x === y) {
    return x !== 0 || 1 / x === 1 / y;
  } else {
    return x !== x && y !== y;
  }
}
function setHiddenProp(target, prop, value) {
  if (!Object.hasOwnProperty.call(target, prop)) {
    Object.defineProperty(target, prop, {
      enumerable: false,
      configurable: true,
      writable: true,
      value
    });
  } else {
    target[prop] = value;
  }
}
var mobxMixins = newSymbol("patchMixins");
var mobxPatchedDefinition = newSymbol("patchedDefinition");
function getMixins(target, methodName) {
  var mixins = target[mobxMixins] = target[mobxMixins] || {};
  var methodMixins = mixins[methodName] = mixins[methodName] || {};
  methodMixins.locks = methodMixins.locks || 0;
  methodMixins.methods = methodMixins.methods || [];
  return methodMixins;
}
function wrapper(realMethod, mixins) {
  var _this = this;
  for (var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {
    args[_key - 2] = arguments[_key];
  }
  mixins.locks++;
  try {
    var retVal;
    if (realMethod !== void 0 && realMethod !== null) {
      retVal = realMethod.apply(this, args);
    }
    return retVal;
  } finally {
    mixins.locks--;
    if (mixins.locks === 0) {
      mixins.methods.forEach(function(mx) {
        mx.apply(_this, args);
      });
    }
  }
}
function wrapFunction(realMethod, mixins) {
  var fn = function fn2() {
    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
      args[_key2] = arguments[_key2];
    }
    wrapper.call.apply(wrapper, [this, realMethod, mixins].concat(args));
  };
  return fn;
}
function patch(target, methodName, mixinMethod) {
  var mixins = getMixins(target, methodName);
  if (mixins.methods.indexOf(mixinMethod) < 0) {
    mixins.methods.push(mixinMethod);
  }
  var oldDefinition = Object.getOwnPropertyDescriptor(target, methodName);
  if (oldDefinition && oldDefinition[mobxPatchedDefinition]) {
    return;
  }
  var originalMethod = target[methodName];
  var newDefinition = createDefinition(target, methodName, oldDefinition ? oldDefinition.enumerable : void 0, mixins, originalMethod);
  Object.defineProperty(target, methodName, newDefinition);
}
function createDefinition(target, methodName, enumerable, mixins, originalMethod) {
  var _ref;
  var wrappedFunc = wrapFunction(originalMethod, mixins);
  return _ref = {}, _ref[mobxPatchedDefinition] = true, _ref.get = function get() {
    return wrappedFunc;
  }, _ref.set = function set(value) {
    if (this === target) {
      wrappedFunc = wrapFunction(value, mixins);
    } else {
      var newDefinition = createDefinition(this, methodName, enumerable, mixins, value);
      Object.defineProperty(this, methodName, newDefinition);
    }
  }, _ref.configurable = true, _ref.enumerable = enumerable, _ref;
}
var mobxAdminProperty = $mobx || "$mobx";
var mobxObserverProperty = newSymbol("isMobXReactObserver");
var mobxIsUnmounted = newSymbol("isUnmounted");
var skipRenderKey = newSymbol("skipRender");
var isForcingUpdateKey = newSymbol("isForcingUpdate");
function makeClassComponentObserver(componentClass) {
  var target = componentClass.prototype;
  if (componentClass[mobxObserverProperty]) {
    var displayName = getDisplayName(target);
    console.warn("The provided component class (" + displayName + ") \n                has already been declared as an observer component.");
  } else {
    componentClass[mobxObserverProperty] = true;
  }
  if (target.componentWillReact)
    throw new Error("The componentWillReact life-cycle event is no longer supported");
  if (componentClass["__proto__"] !== import_react11.PureComponent) {
    if (!target.shouldComponentUpdate)
      target.shouldComponentUpdate = observerSCU;
    else if (target.shouldComponentUpdate !== observerSCU)
      throw new Error("It is not allowed to use shouldComponentUpdate in observer based components.");
  }
  makeObservableProp(target, "props");
  makeObservableProp(target, "state");
  var baseRender = target.render;
  target.render = function() {
    return makeComponentReactive.call(this, baseRender);
  };
  patch(target, "componentWillUnmount", function() {
    var _this$render$mobxAdmi;
    if (isUsingStaticRendering() === true)
      return;
    (_this$render$mobxAdmi = this.render[mobxAdminProperty]) === null || _this$render$mobxAdmi === void 0 ? void 0 : _this$render$mobxAdmi.dispose();
    this[mobxIsUnmounted] = true;
    if (!this.render[mobxAdminProperty]) {
      var _displayName = getDisplayName(this);
      console.warn("The reactive render of an observer class component (" + _displayName + ") \n                was overriden after MobX attached. This may result in a memory leak if the \n                overriden reactive render was not properly disposed.");
    }
  });
  return componentClass;
}
function getDisplayName(comp) {
  return comp.displayName || comp.name || comp.constructor && (comp.constructor.displayName || comp.constructor.name) || "<component>";
}
function makeComponentReactive(render) {
  var _this = this;
  if (isUsingStaticRendering() === true)
    return render.call(this);
  setHiddenProp(this, skipRenderKey, false);
  setHiddenProp(this, isForcingUpdateKey, false);
  var initialName = getDisplayName(this);
  var baseRender = render.bind(this);
  var isRenderingPending = false;
  var reaction2 = new Reaction(initialName + ".render()", function() {
    if (!isRenderingPending) {
      isRenderingPending = true;
      if (_this[mobxIsUnmounted] !== true) {
        var hasError = true;
        try {
          setHiddenProp(_this, isForcingUpdateKey, true);
          if (!_this[skipRenderKey])
            import_react11.Component.prototype.forceUpdate.call(_this);
          hasError = false;
        } finally {
          setHiddenProp(_this, isForcingUpdateKey, false);
          if (hasError)
            reaction2.dispose();
        }
      }
    }
  });
  reaction2["reactComponent"] = this;
  reactiveRender[mobxAdminProperty] = reaction2;
  this.render = reactiveRender;
  function reactiveRender() {
    isRenderingPending = false;
    var exception = void 0;
    var rendering = void 0;
    reaction2.track(function() {
      try {
        rendering = allowStateChanges(false, baseRender);
      } catch (e) {
        exception = e;
      }
    });
    if (exception) {
      throw exception;
    }
    return rendering;
  }
  return reactiveRender.call(this);
}
function observerSCU(nextProps, nextState) {
  if (isUsingStaticRendering()) {
    console.warn("[mobx-react] It seems that a re-rendering of a React component is triggered while in static (server-side) mode. Please make sure components are rendered only once server-side.");
  }
  if (this.state !== nextState) {
    return true;
  }
  return !shallowEqual(this.props, nextProps);
}
function makeObservableProp(target, propName) {
  var valueHolderKey = newSymbol("reactProp_" + propName + "_valueHolder");
  var atomHolderKey = newSymbol("reactProp_" + propName + "_atomHolder");
  function getAtom() {
    if (!this[atomHolderKey]) {
      setHiddenProp(this, atomHolderKey, createAtom("reactive " + propName));
    }
    return this[atomHolderKey];
  }
  Object.defineProperty(target, propName, {
    configurable: true,
    enumerable: true,
    get: function get() {
      var prevReadState = false;
      if (allowStateReadsStart && allowStateReadsEnd) {
        prevReadState = allowStateReadsStart(true);
      }
      getAtom.call(this).reportObserved();
      if (allowStateReadsStart && allowStateReadsEnd) {
        allowStateReadsEnd(prevReadState);
      }
      return this[valueHolderKey];
    },
    set: function set(v) {
      if (!this[isForcingUpdateKey] && !shallowEqual(this[valueHolderKey], v)) {
        setHiddenProp(this, valueHolderKey, v);
        setHiddenProp(this, skipRenderKey, true);
        getAtom.call(this).reportChanged();
        setHiddenProp(this, skipRenderKey, false);
      } else {
        setHiddenProp(this, valueHolderKey, v);
      }
    }
  });
}
var hasSymbol = typeof Symbol === "function" && Symbol.for;
var ReactForwardRefSymbol = hasSymbol ? Symbol.for("react.forward_ref") : typeof import_react11.forwardRef === "function" && (0, import_react11.forwardRef)(function(props) {
  return null;
})["$$typeof"];
var ReactMemoSymbol = hasSymbol ? Symbol.for("react.memo") : typeof import_react11.memo === "function" && (0, import_react11.memo)(function(props) {
  return null;
})["$$typeof"];
function observer2(component) {
  if (component["isMobxInjector"] === true) {
    console.warn("Mobx observer: You are trying to use 'observer' on a component that already has 'inject'. Please apply 'observer' before applying 'inject'");
  }
  if (ReactMemoSymbol && component["$$typeof"] === ReactMemoSymbol) {
    throw new Error("Mobx observer: You are trying to use 'observer' on a function component wrapped in either another observer or 'React.memo'. The observer already applies 'React.memo' for you.");
  }
  if (ReactForwardRefSymbol && component["$$typeof"] === ReactForwardRefSymbol) {
    var baseRender = component["render"];
    if (typeof baseRender !== "function")
      throw new Error("render property of ForwardRef was not a function");
    return (0, import_react11.forwardRef)(function ObserverForwardRef() {
      var args = arguments;
      return (0, import_react11.createElement)(ObserverComponent, null, function() {
        return baseRender.apply(void 0, args);
      });
    });
  }
  if (typeof component === "function" && (!component.prototype || !component.prototype.render) && !component["isReactClass"] && !Object.prototype.isPrototypeOf.call(import_react11.Component, component)) {
    return observer(component);
  }
  return makeClassComponentObserver(component);
}
function _extends() {
  _extends = Object.assign || function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends.apply(this, arguments);
}
function _objectWithoutPropertiesLoose(source, excluded) {
  if (source == null)
    return {};
  var target = {};
  var sourceKeys = Object.keys(source);
  var key, i;
  for (i = 0; i < sourceKeys.length; i++) {
    key = sourceKeys[i];
    if (excluded.indexOf(key) >= 0)
      continue;
    target[key] = source[key];
  }
  return target;
}
var MobXProviderContext = import_react11.default.createContext({});
function Provider(props) {
  var children = props.children, stores = _objectWithoutPropertiesLoose(props, ["children"]);
  var parentValue = import_react11.default.useContext(MobXProviderContext);
  var mutableProviderRef = import_react11.default.useRef(_extends({}, parentValue, stores));
  var value = mutableProviderRef.current;
  if (true) {
    var newValue = _extends({}, value, stores);
    if (!shallowEqual(value, newValue)) {
      throw new Error("MobX Provider: The set of provided stores has changed. See: https://github.com/mobxjs/mobx-react#the-set-of-provided-stores-has-changed-error.");
    }
  }
  return import_react11.default.createElement(MobXProviderContext.Provider, {
    value
  }, children);
}
Provider.displayName = "MobXProvider";
var protoStoreKey = newSymbol("disposeOnUnmountProto");
var instStoreKey = newSymbol("disposeOnUnmountInst");
function createChainableTypeChecker(validator) {
  function checkType(isRequired, props, propName, componentName, location, propFullName) {
    for (var _len = arguments.length, rest = new Array(_len > 6 ? _len - 6 : 0), _key = 6; _key < _len; _key++) {
      rest[_key - 6] = arguments[_key];
    }
    return untracked(function() {
      componentName = componentName || "<<anonymous>>";
      propFullName = propFullName || propName;
      if (props[propName] == null) {
        if (isRequired) {
          var actual = props[propName] === null ? "null" : "undefined";
          return new Error("The " + location + " `" + propFullName + "` is marked as required in `" + componentName + "`, but its value is `" + actual + "`.");
        }
        return null;
      } else {
        return validator.apply(void 0, [props, propName, componentName, location, propFullName].concat(rest));
      }
    });
  }
  var chainedCheckType = checkType.bind(null, false);
  chainedCheckType.isRequired = checkType.bind(null, true);
  return chainedCheckType;
}
function isSymbol(propType, propValue) {
  if (propType === "symbol") {
    return true;
  }
  if (propValue["@@toStringTag"] === "Symbol") {
    return true;
  }
  if (typeof Symbol === "function" && propValue instanceof Symbol) {
    return true;
  }
  return false;
}
function getPropType(propValue) {
  var propType = typeof propValue;
  if (Array.isArray(propValue)) {
    return "array";
  }
  if (propValue instanceof RegExp) {
    return "object";
  }
  if (isSymbol(propType, propValue)) {
    return "symbol";
  }
  return propType;
}
function getPreciseType(propValue) {
  var propType = getPropType(propValue);
  if (propType === "object") {
    if (propValue instanceof Date) {
      return "date";
    } else if (propValue instanceof RegExp) {
      return "regexp";
    }
  }
  return propType;
}
function createObservableTypeCheckerCreator(allowNativeType, mobxType) {
  return createChainableTypeChecker(function(props, propName, componentName, location, propFullName) {
    return untracked(function() {
      if (allowNativeType) {
        if (getPropType(props[propName]) === mobxType.toLowerCase())
          return null;
      }
      var mobxChecker;
      switch (mobxType) {
        case "Array":
          mobxChecker = isObservableArray;
          break;
        case "Object":
          mobxChecker = isObservableObject;
          break;
        case "Map":
          mobxChecker = isObservableMap;
          break;
        default:
          throw new Error("Unexpected mobxType: " + mobxType);
      }
      var propValue = props[propName];
      if (!mobxChecker(propValue)) {
        var preciseType = getPreciseType(propValue);
        var nativeTypeExpectationMessage = allowNativeType ? " or javascript `" + mobxType.toLowerCase() + "`" : "";
        return new Error("Invalid prop `" + propFullName + "` of type `" + preciseType + "` supplied to `" + componentName + "`, expected `mobx.Observable" + mobxType + "`" + nativeTypeExpectationMessage + ".");
      }
      return null;
    });
  });
}
function createObservableArrayOfTypeChecker(allowNativeType, typeChecker) {
  return createChainableTypeChecker(function(props, propName, componentName, location, propFullName) {
    for (var _len2 = arguments.length, rest = new Array(_len2 > 5 ? _len2 - 5 : 0), _key2 = 5; _key2 < _len2; _key2++) {
      rest[_key2 - 5] = arguments[_key2];
    }
    return untracked(function() {
      if (typeof typeChecker !== "function") {
        return new Error("Property `" + propFullName + "` of component `" + componentName + "` has invalid PropType notation.");
      } else {
        var error = createObservableTypeCheckerCreator(allowNativeType, "Array")(props, propName, componentName, location, propFullName);
        if (error instanceof Error)
          return error;
        var propValue = props[propName];
        for (var i = 0; i < propValue.length; i++) {
          error = typeChecker.apply(void 0, [propValue, i, componentName, location, propFullName + "[" + i + "]"].concat(rest));
          if (error instanceof Error)
            return error;
        }
        return null;
      }
    });
  });
}
var observableArray = createObservableTypeCheckerCreator(false, "Array");
var observableArrayOf = createObservableArrayOfTypeChecker.bind(null, false);
var observableMap = createObservableTypeCheckerCreator(false, "Map");
var observableObject = createObservableTypeCheckerCreator(false, "Object");
var arrayOrObservableArray = createObservableTypeCheckerCreator(true, "Array");
var arrayOrObservableArrayOf = createObservableArrayOfTypeChecker.bind(null, true);
var objectOrObservableObject = createObservableTypeCheckerCreator(true, "Object");
if (!import_react11.Component)
  throw new Error("mobx-react requires React to be available");
if (!observable)
  throw new Error("mobx-react requires mobx to be available");

// node_modules/amis/esm/renderers/Table/TableCell.js
var import_omit2 = __toESM(require_omit());
var TableCell = (
  /** @class */
  function(_super) {
    __extends(TableCell2, _super);
    function TableCell2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.propsNeedRemove = [];
      return _this;
    }
    TableCell2.prototype.render = function() {
      var _a = this.props, cx = _a.classnames, className = _a.className, classNameExpr = _a.classNameExpr, render = _a.render, _b = _a.style, style = _b === void 0 ? {} : _b, Component2 = _a.wrapperComponent, contentsOnly = _a.contentsOnly, column = _a.column, value = _a.value, data = _a.data, children = _a.children, width = _a.width, align = _a.align, vAlign = _a.vAlign, innerClassName = _a.innerClassName, label = _a.label, tabIndex = _a.tabIndex, onKeyUp = _a.onKeyUp, rowSpan = _a.rowSpan, _body = _a.body, tpl = _a.tpl, remark = _a.remark, cellPrefix = _a.cellPrefix, cellAffix = _a.cellAffix, isHead = _a.isHead, colIndex = _a.colIndex, row = _a.row, showBadge = _a.showBadge, itemBadge = _a.itemBadge, textOverflow = _a.textOverflow, testIdBuilder = _a.testIdBuilder, rest = __rest(_a, ["classnames", "className", "classNameExpr", "render", "style", "wrapperComponent", "contentsOnly", "column", "value", "data", "children", "width", "align", "vAlign", "innerClassName", "label", "tabIndex", "onKeyUp", "rowSpan", "body", "tpl", "remark", "cellPrefix", "cellAffix", "isHead", "colIndex", "row", "showBadge", "itemBadge", "textOverflow", "testIdBuilder"]);
      if (isHead) {
        Component2 = "th";
      } else {
        Component2 = Component2 || "td";
      }
      var isTableCell = Component2 === "td" || Component2 === "th";
      var schema = __assign(__assign({}, column), {
        // 因为列本身已经做过显隐判断了，单元格不应该再处理
        visibleOn: "",
        hiddenOn: "",
        visible: true,
        hidden: false,
        style: column.innerStyle,
        className: innerClassName,
        type: column && column.type || "plain"
      });
      if (schema.type !== "button" && schema.type !== "dropdown-button") {
        delete schema.label;
      }
      var body = children ? children : render("field", schema, __assign(__assign({}, (0, import_omit2.default)(rest, Object.keys(schema), this.propsNeedRemove)), {
        // inputOnly 属性不能传递给子组件，在 SchemaRenderer.renderChild 中处理掉了
        inputOnly: true,
        value,
        data
      }));
      if (isTableCell) {
        style = (0, import_omit2.default)(style, ["width", "position", "display"]);
      } else if (width) {
        style = __assign(__assign({}, style), { width: style && style.width || width });
      }
      if (align) {
        style = __assign(__assign({}, style), { textAlign: align });
      }
      if (vAlign) {
        style = __assign(__assign({}, style), { verticalAlign: vAlign });
      }
      if (column.backgroundScale) {
        var backgroundScale = column.backgroundScale;
        var min = backgroundScale.min;
        var max = backgroundScale.max;
        if (isPureVariable(min)) {
          min = resolveVariableAndFilter(min, data, "| raw");
        }
        if (isPureVariable(max)) {
          max = resolveVariableAndFilter(max, data, "| raw");
        }
        if (typeof min === "undefined") {
          min = Math.min.apply(Math, __spreadArray([], __read(data.rows.map(function(r) {
            return r[column.name];
          })), false));
        }
        if (typeof max === "undefined") {
          max = Math.max.apply(Math, __spreadArray([], __read(data.rows.map(function(r) {
            return r[column.name];
          })), false));
        }
        var colorScale = new ColorScale(min, max, backgroundScale.colors || ["#FFEF9C", "#FF7127"]);
        var value_1 = data[column.name];
        if (isPureVariable(backgroundScale.source)) {
          value_1 = resolveVariableAndFilter(backgroundScale.source, data, "| raw");
        }
        var color = colorScale.getColor(Number(value_1)).toHexString();
        style.background = color;
      }
      if (contentsOnly) {
        return body;
      }
      return import_react12.default.createElement(
        Component2,
        __assign({ rowSpan: rowSpan > 1 ? rowSpan : void 0, style, className: cx(className), tabIndex, onKeyUp }, testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild("cell").getTestId()),
        showBadge ? import_react12.default.createElement(Badge, { classnames: cx, badge: __assign(__assign({}, itemBadge), { className: cx("Table-badge", itemBadge === null || itemBadge === void 0 ? void 0 : itemBadge.className) }), data: row.data }) : null,
        cellPrefix,
        textOverflow === "ellipsis" && width ? import_react12.default.createElement("div", { className: cx("TableCell-ellipsis") }, body) : body,
        cellAffix
      );
    };
    TableCell2.defaultProps = {
      wrapperComponent: "td"
    };
    TableCell2.propsList = [
      "type",
      "label",
      "column",
      "body",
      "tpl",
      "rowSpan",
      "remark",
      "contentsOnly"
    ];
    return TableCell2;
  }(import_react12.default.Component)
);
var TableCellRenderer = (
  /** @class */
  function(_super) {
    __extends(TableCellRenderer2, _super);
    function TableCellRenderer2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    TableCellRenderer2.propsList = __spreadArray([
      "quickEdit",
      "quickEditEnabledOn",
      "popOver",
      "copyable",
      "inline"
    ], __read(TableCell.propsList), false);
    TableCellRenderer2 = __decorate([
      Renderer({
        type: "cell",
        name: "table-cell"
      }),
      HocPopOver({
        targetOutter: true
      }),
      HocQuickEdit(),
      HocCopyable(),
      observer2
    ], TableCellRenderer2);
    return TableCellRenderer2;
  }(TableCell)
);
var FieldRenderer = (
  /** @class */
  function(_super) {
    __extends(FieldRenderer2, _super);
    function FieldRenderer2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    FieldRenderer2.defaultProps = __assign(__assign({}, TableCell.defaultProps), { wrapperComponent: "div" });
    FieldRenderer2 = __decorate([
      Renderer({
        type: "field",
        name: "field"
      }),
      HocPopOver(),
      HocCopyable()
    ], FieldRenderer2);
    return FieldRenderer2;
  }(TableCell)
);

// node_modules/amis/esm/renderers/Table/HeadCellFilterDropdown.js
var import_react13 = __toESM(require_react());
var import_xor = __toESM(require_xor());
var import_react_dom4 = __toESM(require_react_dom());
var HeadCellFilterDropDown = (
  /** @class */
  function(_super) {
    __extends(HeadCellFilterDropDown2, _super);
    function HeadCellFilterDropDown2(props) {
      var _this = _super.call(this, props) || this;
      _this.state = {
        isOpened: false,
        keyword: "",
        filterOptions: []
      };
      _this.sourceInvalid = false;
      _this.open = _this.open.bind(_this);
      _this.close = _this.close.bind(_this);
      _this.handleClick = _this.handleClick.bind(_this);
      _this.handleCheck = _this.handleCheck.bind(_this);
      return _this;
    }
    HeadCellFilterDropDown2.prototype.componentDidMount = function() {
      var _a = this.props, filterable = _a.filterable, data = _a.data;
      var _b = filterable || {}, source = _b.source, options = _b.options;
      if (source && isPureVariable(source)) {
        var datasource = resolveVariableAndFilter(source, this.props.superData, "| raw");
        this.setState({
          filterOptions: this.alterOptions(datasource)
        });
      } else if (source && isEffectiveApi(source, data)) {
        this.fetchOptions();
      } else if ((options === null || options === void 0 ? void 0 : options.length) > 0) {
        this.setState({
          filterOptions: this.alterOptions(filterable.options)
        });
      }
    };
    HeadCellFilterDropDown2.prototype.componentDidUpdate = function(prevProps, prevState) {
      var _a, _b, _c, _d;
      var name = this.props.name;
      var props = this.props;
      this.sourceInvalid = false;
      if (prevProps.name !== props.name || prevProps.filterable !== props.filterable || prevProps.data !== props.data) {
        if (props.filterable.source) {
          this.sourceInvalid = isApiOutdated(prevProps.filterable.source, props.filterable.source, prevProps.data, props.data);
        } else if (props.filterable.options) {
          this.setState({
            filterOptions: this.alterOptions(props.filterable.options || [])
          });
        } else if (name && !this.state.filterOptions.length && (Array.isArray((_a = props.store) === null || _a === void 0 ? void 0 : _a.data.itemsRaw) || Array.isArray((_b = props.store) === null || _b === void 0 ? void 0 : _b.data.items))) {
          var itemsRaw = ((_c = props.store) === null || _c === void 0 ? void 0 : _c.data.itemsRaw) || ((_d = props.store) === null || _d === void 0 ? void 0 : _d.data.items);
          var values_1 = [];
          itemsRaw.forEach(function(item) {
            var value2 = getVariable(item, name);
            if (!~values_1.indexOf(value2)) {
              values_1.push(value2);
            }
          });
          if (values_1.length) {
            this.setState({
              filterOptions: this.alterOptions(values_1)
            });
          }
        }
      }
      var value = this.props.data ? this.props.data[name] : void 0;
      var prevValue = prevProps.data ? prevProps.data[name] : void 0;
      if (value !== prevValue && this.state.filterOptions.length && prevState.filterOptions !== this.props.filterOptions) {
        this.setState({
          filterOptions: this.alterOptions(this.state.filterOptions)
        });
      }
      this.sourceInvalid && this.fetchOptions();
    };
    HeadCellFilterDropDown2.prototype.fetchOptions = function() {
      return __awaiter(this, void 0, void 0, function() {
        var _a, env, filterable, data, api, ret, options;
        return __generator(this, function(_b) {
          switch (_b.label) {
            case 0:
              _a = this.props, env = _a.env, filterable = _a.filterable, data = _a.data;
              if (!isEffectiveApi(filterable.source, data)) {
                return [
                  2
                  /*return*/
                ];
              }
              api = normalizeApi(filterable.source);
              api.cache = 3e3;
              return [4, env.fetcher(api, data)];
            case 1:
              ret = _b.sent();
              options = ret.data && ret.data.options || [];
              this.setState({
                filterOptions: ret && ret.data && this.alterOptions(options)
              });
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    HeadCellFilterDropDown2.prototype.alterOptions = function(options, keyword) {
      var _this = this;
      if (keyword === void 0) {
        keyword = "";
      }
      var _a = this.props, data = _a.data, filterable = _a.filterable, name = _a.name;
      var labelField = filterable.labelField, valueField = filterable.valueField;
      var filterValue = data && typeof data[name] !== "undefined" ? data[name] : "";
      options = normalizeOptions(options);
      options = options.map(function(option) {
        option.visible = !!matchSorter([option], keyword, {
          keys: [labelField || "label", valueField || "value"],
          threshold: matchSorter.rankings.CONTAINS
        }).length;
        return option;
      });
      if (filterable.multiple) {
        options = options.map(function(option) {
          return __assign(__assign({}, option), { selected: filterValue.split(",").indexOf(option.value) > -1 });
        });
      } else {
        options = options.map(function(option) {
          return __assign(__assign({}, option), { selected: _this.optionComparator(option, filterValue) });
        });
      }
      return options;
    };
    HeadCellFilterDropDown2.prototype.optionComparator = function(option, selected) {
      var filterable = this.props.filterable;
      if (isNumeric(option.value)) {
        return isNumeric(selected) ? option.value == selected : false;
      }
      return (filterable === null || filterable === void 0 ? void 0 : filterable.strictMode) === true ? option.value === selected : option.value == selected;
    };
    HeadCellFilterDropDown2.prototype.handleClickOutside = function() {
      this.close();
    };
    HeadCellFilterDropDown2.prototype.open = function() {
      return __awaiter(this, void 0, void 0, function() {
        var _a, filterable, source, datasource;
        return __generator(this, function(_b) {
          switch (_b.label) {
            case 0:
              _a = this.props, filterable = _a.filterable, source = _a.source;
              if (!(filterable.refreshOnOpen && filterable.source))
                return [3, 3];
              if (!(source && isPureVariable(source)))
                return [3, 1];
              datasource = resolveVariableAndFilter(source, this.props.superData, "| raw");
              this.setState({
                filterOptions: this.alterOptions(datasource)
              });
              return [3, 3];
            case 1:
              return [4, this.fetchOptions()];
            case 2:
              _b.sent();
              _b.label = 3;
            case 3:
              this.setState({
                isOpened: true
              });
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    HeadCellFilterDropDown2.prototype.close = function() {
      this.setState({
        isOpened: false
      });
    };
    HeadCellFilterDropDown2.prototype.handleClick = function(value) {
      return __awaiter(this, void 0, void 0, function() {
        var _a, onQuery, name, data, dispatchEvent, rendererEvent;
        var _b;
        return __generator(this, function(_c) {
          switch (_c.label) {
            case 0:
              _a = this.props, onQuery = _a.onQuery, name = _a.name, data = _a.data, dispatchEvent = _a.dispatchEvent;
              return [4, dispatchEvent("columnFilter", createObject(data, {
                filterName: name,
                filterValue: value
              }))];
            case 1:
              rendererEvent = _c.sent();
              if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                return [
                  2
                  /*return*/
                ];
              }
              onQuery((_b = {}, _b[name] = value, _b), false, false, true);
              this.close();
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    HeadCellFilterDropDown2.prototype.handleCheck = function(value) {
      return __awaiter(this, void 0, void 0, function() {
        var _a, data, name, onQuery, dispatchEvent, query, rendererEvent;
        var _b;
        return __generator(this, function(_c) {
          switch (_c.label) {
            case 0:
              _a = this.props, data = _a.data, name = _a.name, onQuery = _a.onQuery, dispatchEvent = _a.dispatchEvent;
              if (data[name] && data[name] === value) {
                query = "";
              } else {
                query = data[name] && (0, import_xor.default)(data[name].split(","), [value]).join(",") || value;
              }
              return [4, dispatchEvent("columnFilter", createObject(data, {
                filterName: name,
                filterValue: query
              }))];
            case 1:
              rendererEvent = _c.sent();
              if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                return [
                  2
                  /*return*/
                ];
              }
              onQuery((_b = {}, _b[name] = query, _b));
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    HeadCellFilterDropDown2.prototype.handleReset = function() {
      return __awaiter(this, void 0, void 0, function() {
        var _a, name, dispatchEvent, data, onQuery, rendererEvent;
        var _b;
        return __generator(this, function(_c) {
          switch (_c.label) {
            case 0:
              _a = this.props, name = _a.name, dispatchEvent = _a.dispatchEvent, data = _a.data, onQuery = _a.onQuery;
              return [4, dispatchEvent("columnFilter", createObject(data, {
                filterName: name,
                filterValue: void 0
              }))];
            case 1:
              rendererEvent = _c.sent();
              if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                return [
                  2
                  /*return*/
                ];
              }
              onQuery((_b = {}, _b[name] = void 0, _b), false, false, true);
              this.close();
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    HeadCellFilterDropDown2.prototype.handleSearch = function(keyword) {
      var filterOptions = this.state.filterOptions;
      this.setState({
        keyword,
        filterOptions: this.alterOptions(filterOptions, keyword)
      });
    };
    HeadCellFilterDropDown2.prototype.render = function() {
      var _this = this;
      var _a, _b, _c, _d;
      var _e = this.state, isOpened = _e.isOpened, filterOptions = _e.filterOptions;
      var _f = this.props, data = _f.data, name = _f.name, filterable = _f.filterable, popOverContainer = _f.popOverContainer, ns = _f.classPrefix, cx = _f.classnames, __ = _f.translate;
      var searchConfig = (filterable === null || filterable === void 0 ? void 0 : filterable.searchConfig) || {};
      return import_react13.default.createElement(
        "span",
        { className: cx("".concat(ns, "TableCell-filterBtn"), data && typeof data[name] !== "undefined" ? "is-active" : "") },
        import_react13.default.createElement(
          "span",
          { onClick: this.open },
          import_react13.default.createElement(Icon, { icon: "column-filter", className: "icon" })
        ),
        isOpened ? import_react13.default.createElement(
          Overlay,
          { container: popOverContainer || function() {
            return (0, import_react_dom4.findDOMNode)(_this);
          }, placement: "left-bottom-left-top right-bottom-right-top", target: popOverContainer ? function() {
            return (0, import_react_dom4.findDOMNode)(_this).parentNode;
          } : null, show: true },
          import_react13.default.createElement(PopOver$1, { classPrefix: ns, onHide: this.close, className: cx("".concat(ns, "TableCell-filterPopOver"), filterable.className), overlay: true }, filterOptions && filterOptions.length > 0 ? import_react13.default.createElement(
            import_react13.default.Fragment,
            null,
            (filterable === null || filterable === void 0 ? void 0 : filterable.searchable) ? import_react13.default.createElement(SearchBox$1, { className: cx("TableCell-filterPopOver-SearchBox", searchConfig === null || searchConfig === void 0 ? void 0 : searchConfig.className), mini: (_a = searchConfig.mini) !== null && _a !== void 0 ? _a : false, enhance: (_b = searchConfig.enhance) !== null && _b !== void 0 ? _b : false, clearable: (_c = searchConfig.clearable) !== null && _c !== void 0 ? _c : true, searchImediately: searchConfig.searchImediately, placeholder: searchConfig.placeholder, defaultValue: "", value: (_d = this.state.keyword) !== null && _d !== void 0 ? _d : "", onSearch: this.handleSearch, onChange: (
              /** 为了消除react报错 */
              noop
            ) }) : null,
            import_react13.default.createElement(
              "ul",
              { className: cx("DropDown-menu") },
              !filterable.multiple ? filterOptions.map(function(option, index) {
                return option.visible && import_react13.default.createElement("li", { key: index, className: cx({
                  "is-active": option.selected
                }), onClick: _this.handleClick.bind(_this, option.value) }, option.label);
              }) : filterOptions.map(function(option, index) {
                return option.visible && import_react13.default.createElement(
                  "li",
                  { key: index },
                  import_react13.default.createElement(Checkbox$1, { size: "sm", classPrefix: ns, onChange: _this.handleCheck.bind(_this, option.value), checked: option.selected }, option.label)
                );
              }),
              filterOptions.some(function(item) {
                return item.selected;
              }) ? import_react13.default.createElement("li", { key: "DropDown-menu-reset", onClick: this.handleReset.bind(this) }, __("reset")) : null
            )
          ) : null)
        ) : null
      );
    };
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [String]),
      __metadata("design:returntype", void 0)
    ], HeadCellFilterDropDown2.prototype, "handleSearch", null);
    return HeadCellFilterDropDown2;
  }(import_react13.default.Component)
);

// node_modules/amis/esm/renderers/Table/HeadCellSearchDropdown.js
var import_react14 = __toESM(require_react());
function HeadCellSearchDropDown(_a) {
  var _this = this;
  var searchable = _a.searchable, name = _a.name, label = _a.label, onQuery = _a.onQuery, data = _a.data, dispatchEvent = _a.dispatchEvent, onAction = _a.onAction, cx = _a.classnames, __ = _a.translate, ns = _a.classPrefix, popOverContainer = _a.popOverContainer, render = _a.render, testIdBuilder = _a.testIdBuilder;
  var ref = import_react14.default.createRef();
  var _b = __read(import_react14.default.useMemo(function() {
    var schema;
    var formItems2 = [];
    if (searchable === true) {
      schema = {
        title: "",
        body: [
          {
            type: "input-text",
            name,
            placeholder: label,
            clearable: true
          }
        ]
      };
    } else if (searchable) {
      if (!searchable.type && (searchable.body || searchable.tabs || searchable.fieldSet)) {
        schema = __assign(__assign({ title: "" }, searchable), { body: Array.isArray(searchable.body) ? searchable.body.concat() : void 0 });
      } else {
        schema = {
          title: "",
          className: searchable.formClassName,
          body: [
            __assign({ type: searchable.type || "input-text", name: searchable.name || name, placeholder: label }, searchable)
          ]
        };
      }
    }
    function findFormItems(schema2) {
      Array.isArray(schema2.body) && schema2.body.forEach(function(item) {
        item.name && formItems2.push(item.name);
        item.extraName && typeof item.extraName === "string" && formItems2.push(item.extraName);
        findFormItems(item);
      });
    }
    if (schema) {
      findFormItems(schema);
      schema = __assign(__assign({}, schema), { type: "form", wrapperComponent: "div", canAccessSuperData: false, actions: [
        {
          type: "button",
          label: __("reset"),
          actionType: "clear-and-submit"
        },
        {
          type: "button",
          label: __("cancel"),
          actionType: "cancel"
        },
        {
          label: __("search"),
          type: "submit",
          primary: true
        }
      ] });
    }
    return [schema || "error", formItems2];
  }, [searchable, name, label]), 2), formSchema = _b[0], formItems = _b[1];
  var _c = __read(import_react14.default.useState(false), 2), isOpened = _c[0], setIsOpened = _c[1];
  var open = import_react14.default.useCallback(function() {
    return setIsOpened(true);
  }, []);
  var close = import_react14.default.useCallback(function() {
    return setIsOpened(false);
  }, []);
  var handleSubmit = import_react14.default.useCallback(function(values) {
    return __awaiter(_this, void 0, void 0, function() {
      var rendererEvent;
      return __generator(this, function(_a2) {
        switch (_a2.label) {
          case 0:
            return [4, dispatchEvent("columnSearch", createObject(data, {
              searchName: name,
              searchValue: values
            }))];
          case 1:
            rendererEvent = _a2.sent();
            if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
              return [
                2
                /*return*/
              ];
            }
            close();
            onQuery(values);
            return [
              2
              /*return*/
            ];
        }
      });
    });
  }, []);
  var handleAction = import_react14.default.useCallback(function(e, action, ctx) {
    if (action.actionType === "cancel" || action.actionType === "close") {
      close();
      return;
    }
    if (action.actionType === "reset") {
      close();
      handleReset();
      return;
    }
    onAction && onAction(e, action, ctx);
  }, []);
  var handleReset = import_react14.default.useCallback(function() {
    var values = __assign({}, data);
    formItems.forEach(function(key) {
      return setVariable(values, key, void 0);
    });
    onQuery(values);
  }, [data]);
  var isActive = import_react14.default.useMemo(function() {
    return formItems.some(function(key) {
      return data === null || data === void 0 ? void 0 : data[key];
    });
  }, [data]);
  return import_react14.default.createElement(
    "span",
    __assign({ ref, className: cx("".concat(ns, "TableCell-searchBtn"), isActive ? "is-active" : "", isOpened ? "is-opened" : "") }, testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getTestId()),
    import_react14.default.createElement(
      "span",
      { onClick: open },
      import_react14.default.createElement(Icon, { icon: "search", className: "icon" })
    ),
    isOpened ? import_react14.default.createElement(
      Overlay,
      { container: popOverContainer || function() {
        return ref.current;
      }, placement: "left-bottom-left-top right-bottom-right-top", target: popOverContainer ? function() {
        var _a2;
        return (_a2 = ref.current) === null || _a2 === void 0 ? void 0 : _a2.parentNode;
      } : null, show: true },
      import_react14.default.createElement(PopOver$1, { classPrefix: ns, onHide: close, className: cx("".concat(ns, "TableCell-searchPopOver"), searchable.className), overlay: true }, render("quick-search-form", formSchema, {
        popOverContainer,
        data,
        onSubmit: handleSubmit,
        onAction: handleAction
      }))
    ) : null
  );
}

// node_modules/amis/esm/renderers/Table/TableContent.js
var import_react19 = __toESM(require_react());

// node_modules/amis/esm/renderers/Table/TableBody.js
var import_react16 = __toESM(require_react());

// node_modules/amis/esm/renderers/Table/TableRow.js
var import_react15 = __toESM(require_react());
var TableRow = (
  /** @class */
  function(_super) {
    __extends(TableRow2, _super);
    function TableRow2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    TableRow2.prototype.handleMouseEnter = function(e) {
      var _a = this.props, item = _a.item, itemIndex = _a.itemIndex, onRowMouseEnter = _a.onRowMouseEnter;
      onRowMouseEnter === null || onRowMouseEnter === void 0 ? void 0 : onRowMouseEnter(item, itemIndex);
    };
    TableRow2.prototype.handleMouseLeave = function(e) {
      var _a = this.props, item = _a.item, itemIndex = _a.itemIndex, onRowMouseLeave = _a.onRowMouseLeave;
      onRowMouseLeave === null || onRowMouseLeave === void 0 ? void 0 : onRowMouseLeave(item, itemIndex);
    };
    TableRow2.prototype.handleItemClick = function(e) {
      var _a;
      return __awaiter(this, void 0, void 0, function() {
        var shiftKey, _b, itemAction, onAction, item, itemIndex, onCheck, onRowClick, checkOnItemClick, rendererEvent;
        return __generator(this, function(_c) {
          switch (_c.label) {
            case 0:
              if (isClickOnInput(e)) {
                return [
                  2
                  /*return*/
                ];
              }
              shiftKey = (_a = e.nativeEvent) === null || _a === void 0 ? void 0 : _a.shiftKey;
              e.preventDefault();
              e.stopPropagation();
              _b = this.props, itemAction = _b.itemAction, onAction = _b.onAction, item = _b.item, itemIndex = _b.itemIndex, onCheck = _b.onCheck, onRowClick = _b.onRowClick, checkOnItemClick = _b.checkOnItemClick;
              return [4, onRowClick === null || onRowClick === void 0 ? void 0 : onRowClick(item, itemIndex)];
            case 1:
              rendererEvent = _c.sent();
              if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                return [
                  2
                  /*return*/
                ];
              }
              if (itemAction) {
                onAction && onAction(e, itemAction, item === null || item === void 0 ? void 0 : item.locals);
              } else {
                if (item.checkable && item.isCheckAvaiableOnClick && checkOnItemClick) {
                  onCheck === null || onCheck === void 0 ? void 0 : onCheck(item, !item.checked, shiftKey);
                }
              }
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    TableRow2.prototype.handleDbClick = function(e) {
      var _a = this.props, item = _a.item, itemIndex = _a.itemIndex, onRowDbClick = _a.onRowDbClick;
      onRowDbClick === null || onRowDbClick === void 0 ? void 0 : onRowDbClick(item, itemIndex);
    };
    TableRow2.prototype.handleAction = function(e, action, ctx) {
      var _a = this.props, onAction = _a.onAction, item = _a.item;
      return onAction && onAction(e, action, ctx || item.locals);
    };
    TableRow2.prototype.handleQuickChange = function(values, saveImmediately, savePristine, options) {
      var _a = this.props, onQuickChange = _a.onQuickChange, item = _a.item;
      onQuickChange && onQuickChange(item, values, saveImmediately, savePristine, options);
    };
    TableRow2.prototype.handleChange = function(value, name, submit, changePristine) {
      if (!name || typeof name !== "string") {
        return;
      }
      var _a = this.props, item = _a.item, onQuickChange = _a.onQuickChange;
      var data = {};
      var keyPath = keyToPath(name);
      if (keyPath.length > 1) {
        data[keyPath[0]] = __assign({}, item.data[keyPath[0]]);
      }
      setVariable(data, name, value);
      onQuickChange === null || onQuickChange === void 0 ? void 0 : onQuickChange(item, data, submit, changePristine);
    };
    TableRow2.prototype.render = function() {
      var _a, _b;
      var _this = this;
      var _c;
      var _d = this.props, itemClassName = _d.itemClassName, itemIndex = _d.itemIndex, item = _d.item, columns = _d.columns, renderCell = _d.renderCell, children = _d.children, footableMode = _d.footableMode, ignoreFootableContent = _d.ignoreFootableContent, footableColSpan = _d.footableColSpan, regionPrefix = _d.regionPrefix, checkOnItemClick = _d.checkOnItemClick, ns = _d.classPrefix, render = _d.render, cx = _d.classnames, parent = _d.parent, itemAction = _d.itemAction, onEvent = _d.onEvent, expanded = _d.expanded, parentExpanded = _d.parentExpanded, id = _d.id, newIndex = _d.newIndex, isHover = _d.isHover, checked = _d.checked, modified = _d.modified, moved = _d.moved, depth = _d.depth, expandable = _d.expandable, appeard = _d.appeard, checkdisable = _d.checkdisable, trRef = _d.trRef, isNested = _d.isNested, testIdBuilder = _d.testIdBuilder, rowPath = _d.rowPath, rest = __rest(_d, ["itemClassName", "itemIndex", "item", "columns", "renderCell", "children", "footableMode", "ignoreFootableContent", "footableColSpan", "regionPrefix", "checkOnItemClick", "classPrefix", "render", "classnames", "parent", "itemAction", "onEvent", "expanded", "parentExpanded", "id", "newIndex", "isHover", "checked", "modified", "moved", "depth", "expandable", "appeard", "checkdisable", "trRef", "isNested", "testIdBuilder", "rowPath"]);
      if (footableMode) {
        if (!expanded) {
          return null;
        }
        return import_react15.default.createElement(
          "tr",
          { ref: trRef, "data-id": id, "data-index": newIndex, onClick: checkOnItemClick || itemAction || (onEvent === null || onEvent === void 0 ? void 0 : onEvent.rowClick) ? this.handleItemClick : void 0, onDoubleClick: this.handleDbClick, onMouseEnter: this.handleMouseEnter, onMouseLeave: this.handleMouseLeave, className: cx("Table-table-tr", itemClassName, (_a = {
            "is-hovered": isHover,
            "is-checked": checked,
            "is-modified": modified,
            "is-moved": moved
          }, _a["Table-tr--hasItemAction"] = itemAction, _a["Table-tr--odd"] = itemIndex % 2 === 0, _a["Table-tr--even"] = itemIndex % 2 === 1, _a)) },
          import_react15.default.createElement(
            "td",
            { className: cx("Table-foot"), colSpan: footableColSpan },
            import_react15.default.createElement(
              "table",
              { className: cx("Table-footTable") },
              import_react15.default.createElement("tbody", null, ignoreFootableContent ? columns.map(function(column) {
                return import_react15.default.createElement(
                  "tr",
                  { key: column.id },
                  column.label !== false ? import_react15.default.createElement("th", null) : null,
                  import_react15.default.createElement("td", null)
                );
              }) : columns.map(function(column) {
                return import_react15.default.createElement(
                  "tr",
                  { key: column.id },
                  column.label !== false ? import_react15.default.createElement("th", null, render("".concat(regionPrefix).concat(itemIndex, "/").concat(column.index, "/tpl"), column.label)) : null,
                  appeard ? renderCell("".concat(regionPrefix).concat(itemIndex, "/").concat(column.index), column, item, __assign(__assign({}, rest), { width: null, rowIndex: itemIndex, rowIndexPath: item.path, colIndex: column.index, rowPath, key: column.id, onAction: _this.handleAction, onQuickChange: _this.handleQuickChange, onChange: _this.handleChange })) : import_react15.default.createElement(
                    "td",
                    { key: column.id },
                    import_react15.default.createElement("div", { className: cx("Table-emptyBlock") }, " ")
                  )
                );
              }))
            )
          )
        );
      }
      if (parent && !parent.expanded) {
        return null;
      }
      return import_react15.default.createElement("tr", __assign({ ref: trRef, onClick: checkOnItemClick || itemAction || (onEvent === null || onEvent === void 0 ? void 0 : onEvent.rowClick) ? this.handleItemClick : void 0, onDoubleClick: this.handleDbClick, onMouseEnter: this.handleMouseEnter, onMouseLeave: this.handleMouseLeave, "data-index": depth === 1 ? newIndex : void 0, "data-id": id, className: cx("Table-table-tr", itemClassName, (_b = {
        "is-hovered": isHover,
        "is-checked": checked,
        "is-modified": modified,
        "is-moved": moved,
        "is-expanded": expanded && expandable,
        "is-expandable": expandable
      }, _b["Table-tr--hasItemAction"] = itemAction, _b["Table-tr--odd"] = itemIndex % 2 === 0, _b["Table-tr--even"] = itemIndex % 2 === 1, _b), "Table-tr--".concat(depth, "th")) }, (_c = testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder(rowPath)) === null || _c === void 0 ? void 0 : _c.getTestId()), columns.map(function(column) {
        return appeard ? renderCell("".concat(itemIndex, "/").concat(column.index), column, item, __assign(__assign({}, rest), { rowIndex: itemIndex, colIndex: column.index, rowIndexPath: item.path, rowPath, key: column.id, onAction: _this.handleAction, onQuickChange: _this.handleQuickChange, onChange: _this.handleChange })) : column.name && item.rowSpans[column.name] === 0 ? null : import_react15.default.createElement(
          "td",
          { key: column.id },
          import_react15.default.createElement("div", { className: cx("Table-emptyBlock") }, " ")
        );
      }));
    };
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], TableRow2.prototype, "handleMouseEnter", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], TableRow2.prototype, "handleMouseLeave", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", Promise)
    ], TableRow2.prototype, "handleItemClick", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], TableRow2.prototype, "handleDbClick", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object, Action, Object]),
      __metadata("design:returntype", void 0)
    ], TableRow2.prototype, "handleAction", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object, Boolean, Boolean, Object]),
      __metadata("design:returntype", void 0)
    ], TableRow2.prototype, "handleQuickChange", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object, String, Boolean, Boolean]),
      __metadata("design:returntype", void 0)
    ], TableRow2.prototype, "handleChange", null);
    return TableRow2;
  }(import_react15.default.PureComponent)
);
var TableRow$1 = observer2(function(props) {
  var item = props.item;
  var parent = props.parent;
  var store = props.store;
  var columns = props.columns;
  var canAccessSuperData = store.canAccessSuperData || columns.some(function(item2) {
    return item2.pristine.canAccessSuperData;
  });
  var _a = useInView({
    threshold: 0,
    onChange: item.markAppeared,
    skip: !item.lazyRender
  }), ref = _a.ref, inView = _a.inView;
  return import_react15.default.createElement(TableRow, __assign({}, props, {
    trRef: ref,
    expanded: item.expanded,
    parentExpanded: parent === null || parent === void 0 ? void 0 : parent.expanded,
    id: item.id,
    newIndex: item.newIndex,
    isHover: item.isHover,
    partial: item.partial,
    checked: item.checked,
    modified: item.modified,
    moved: item.moved,
    depth: item.depth,
    expandable: item.expandable,
    checkdisable: item.checkdisable,
    loading: item.loading,
    error: item.error,
    // data 在 TableRow 里面没有使用，这里写上是为了当列数据变化的时候 TableRow 重新渲染，
    // 不是 item.locals 的原因是 item.locals 会变化多次，比如父级上下文变化也会进来，但是 item.data 只会变化一次。
    data: canAccessSuperData ? item.locals : item.data,
    appeard: item.lazyRender ? item.appeared || inView : true,
    isNested: store.isNested
  }));
});

// node_modules/amis/esm/renderers/Table/TableBody.js
var TableBody = (
  /** @class */
  function(_super) {
    __extends(TableBody2, _super);
    function TableBody2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    TableBody2.prototype.componentDidMount = function() {
      this.props.store.initTableWidth();
    };
    TableBody2.prototype.testIdBuilder = function(rowPath) {
      var _a;
      return (_a = this.props.testIdBuilder) === null || _a === void 0 ? void 0 : _a.getChild("row-".concat(rowPath));
    };
    TableBody2.prototype.renderRows = function(rows, columns, rowProps, indexPath) {
      var _this = this;
      if (columns === void 0) {
        columns = this.props.columns;
      }
      if (rowProps === void 0) {
        rowProps = {};
      }
      var _a = this.props, rowClassName = _a.rowClassName, rowClassNameExpr = _a.rowClassNameExpr, onAction = _a.onAction, buildItemProps = _a.buildItemProps, checkOnItemClick = _a.checkOnItemClick, cx = _a.classnames, render = _a.render, renderCell = _a.renderCell, onCheck = _a.onCheck, onQuickChange = _a.onQuickChange, footable = _a.footable, ignoreFootableContent = _a.ignoreFootableContent, footableColumns = _a.footableColumns, itemAction = _a.itemAction, onRowClick = _a.onRowClick, onRowDbClick = _a.onRowDbClick, onRowMouseEnter = _a.onRowMouseEnter, onRowMouseLeave = _a.onRowMouseLeave, store = _a.store;
      return rows.map(function(item, rowIndex) {
        var itemProps = buildItemProps ? buildItemProps(item, rowIndex) : null;
        var rowPath = "".concat(indexPath ? indexPath + "/" : "").concat(rowIndex);
        var doms = [
          import_react16.default.createElement(TableRow$1, __assign({}, itemProps, {
            testIdBuilder: _this.testIdBuilder,
            store,
            itemAction,
            classnames: cx,
            checkOnItemClick,
            key: item.id,
            itemIndex: rowIndex,
            rowPath,
            item,
            itemClassName: cx(rowClassNameExpr ? filter(rowClassNameExpr, item.locals) : rowClassName, {
              "is-last": item.depth > 1 && rowIndex === rows.length - 1 && !item.children.length
            }),
            columns,
            renderCell,
            render,
            onAction,
            onCheck,
            // todo 先注释 quickEditEnabled={item.depth === 1}
            onQuickChange,
            onRowClick,
            onRowDbClick,
            onRowMouseEnter,
            onRowMouseLeave
          }, rowProps))
        ];
        if (footable && footableColumns.length) {
          if (item.depth === 1) {
            doms.push(import_react16.default.createElement(TableRow$1, __assign({}, itemProps, { store, itemAction, classnames: cx, checkOnItemClick, key: "foot-".concat(item.id), itemIndex: rowIndex, rowPath, item, itemClassName: cx(rowClassNameExpr ? filter(rowClassNameExpr, item.locals) : rowClassName), columns: footableColumns, renderCell, render, onAction, onCheck, onRowClick, onRowDbClick, onRowMouseEnter, onRowMouseLeave, footableMode: true, footableColSpan: columns.length, onQuickChange, ignoreFootableContent }, rowProps, { testIdBuilder: _this.testIdBuilder })));
          }
        } else if (item.children.length && item.expanded) {
          doms.push.apply(doms, __spreadArray([], __read(_this.renderRows(item.children, columns, __assign(__assign({}, rowProps), { parent: item }), rowPath)), false));
        }
        return doms;
      });
    };
    TableBody2.prototype.renderSummaryRow = function(position2, items, rowIndex) {
      var _a, _b;
      var _c = this.props, columns = _c.columns, render = _c.render, data = _c.data, cx = _c.classnames, rows = _c.rows, prefixRowClassName = _c.prefixRowClassName, affixRowClassName = _c.affixRowClassName, store = _c.store;
      if (!(Array.isArray(items) && items.length)) {
        return null;
      }
      var offset2 = 0;
      var result = items.map(function(item2, index) {
        var colIdxs = [offset2 + index];
        if (item2.colSpan > 1) {
          for (var i2 = 1; i2 < item2.colSpan; i2++) {
            colIdxs.push(offset2 + index + i2);
          }
          offset2 += item2.colSpan - 1;
        }
        var matchedColumns2 = colIdxs.map(function(idx) {
          return columns.find(function(col) {
            return col.rawIndex === idx;
          });
        }).filter(function(item3) {
          return item3;
        });
        return __assign(__assign({}, item2), { colSpan: matchedColumns2.length, firstColumn: matchedColumns2[0], lastColumn: matchedColumns2[matchedColumns2.length - 1] });
      }).filter(function(item2) {
        return item2.colSpan;
      });
      if (result[0] && typeof ((_a = columns[0]) === null || _a === void 0 ? void 0 : _a.type) === "string" && ((_b = columns[0]) === null || _b === void 0 ? void 0 : _b.type.substring(0, 2)) === "__") {
        result[0].firstColumn = columns[0];
        result[0].colSpan = (result[0].colSpan || 1) + 1;
      }
      var resultLen = result.reduce(function(p, c) {
        return p + (c.colSpan || 1);
      }, 0);
      var appendLen = columns.length - resultLen;
      while (appendLen < 0) {
        var item = result.pop();
        if (!item) {
          break;
        }
        appendLen += item.colSpan || 1;
      }
      if (appendLen) {
        var item = {
          type: "html",
          html: "&nbsp;"
        };
        for (var i = resultLen; i < store.filteredColumns.length; i++) {
          var matchedColumns = [i].map(function(idx) {
            return store.filteredColumns.find(function(col) {
              return col.rawIndex === idx;
            });
          }).filter(function(item2) {
            return item2;
          });
          result.push(__assign(__assign({}, item), { colSpan: matchedColumns.length, firstColumn: matchedColumns[0], lastColumn: matchedColumns[matchedColumns.length - 1] }));
        }
      }
      var ctx = createObject(data, {
        items: rows.map(function(row) {
          return row.locals;
        })
      });
      return import_react16.default.createElement("tr", { className: cx("Table-table-tr", "is-summary", position2 === "prefix" ? prefixRowClassName : "", position2 === "affix" ? affixRowClassName : ""), key: "summary-".concat(position2, "-").concat(rowIndex || 0) }, result.map(function(item2, index) {
        var Com = item2.isHead ? "th" : "td";
        var firstColumn = item2.firstColumn;
        var lastColumn = item2.lastColumn;
        var style = __assign({}, item2.style);
        if (item2.align) {
          style.textAlign = item2.align;
        }
        if (item2.vAlign) {
          style.verticalAlign = item2.vAlign;
        }
        var _a2 = __read(store.getStickyStyles(lastColumn.fixed === "right" ? lastColumn : firstColumn, store.filteredColumns, item2.colSpan), 2), stickyStyle = _a2[0], stickyClassName = _a2[1];
        Object.assign(style, stickyStyle);
        return import_react16.default.createElement(Com, { key: index, colSpan: item2.colSpan == 1 ? void 0 : item2.colSpan, style, className: (item2.cellClassName || "") + " " + stickyClassName }, render("summary-row/".concat(index), item2, {
          data: ctx
        }));
      }));
    };
    TableBody2.prototype.renderSummary = function(position2, items) {
      var _this = this;
      return Array.isArray(items) ? items.some(function(i) {
        return Array.isArray(i);
      }) ? items.map(function(i, rowIndex) {
        return _this.renderSummaryRow(position2, Array.isArray(i) ? i : [i], rowIndex);
      }) : this.renderSummaryRow(position2, items) : null;
    };
    TableBody2.prototype.render = function() {
      var _a = this.props, cx = _a.classnames, className = _a.className, render = _a.render, rows = _a.rows, columns = _a.columns, rowsProps = _a.rowsProps, prefixRow = _a.prefixRow, affixRow = _a.affixRow, __ = _a.translate;
      return import_react16.default.createElement("tbody", { className }, rows.length ? import_react16.default.createElement(
        import_react16.default.Fragment,
        null,
        this.renderSummary("prefix", prefixRow),
        this.renderRows(rows, columns, rowsProps),
        this.renderSummary("affix", affixRow)
      ) : null);
    };
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [String]),
      __metadata("design:returntype", void 0)
    ], TableBody2.prototype, "testIdBuilder", null);
    TableBody2 = __decorate([
      observer2
    ], TableBody2);
    return TableBody2;
  }(import_react16.default.Component)
);

// node_modules/amis/esm/renderers/Table/ItemActionsWrapper.js
var import_react17 = __toESM(require_react());
function ItemActionsWrapper(props) {
  var _a;
  var cx = props.classnames;
  var children = props.children;
  var store = props.store;
  var divRef = (0, import_react17.useRef)(null);
  var updatePosition = import_react17.default.useCallback(function(id) {
    var _a2, _b;
    if (id === void 0) {
      id = ((_a2 = store.hoverRow) === null || _a2 === void 0 ? void 0 : _a2.id) || "";
    }
    var frame = (_b = divRef.current.parentElement) === null || _b === void 0 ? void 0 : _b.querySelector("table");
    var dom = frame === null || frame === void 0 ? void 0 : frame.querySelector('tr[data-id="'.concat(id, '"]'));
    if (!dom) {
      return;
    }
    var rect = dom.getBoundingClientRect();
    var height = rect.height;
    var top = rect.top - frame.getBoundingClientRect().top + parseInt(getComputedStyle(frame)["marginTop"], 10);
    divRef.current.style.cssText += "top: ".concat(top, "px;height: ").concat(height, "px; left: ").concat(frame.parentElement.scrollLeft, "px;");
  }, []);
  (0, import_react17.useEffect)(function() {
    var row = store.hoverRow;
    if (!row) {
      return;
    }
    updatePosition(row.id);
  }, [(_a = store.hoverRow) === null || _a === void 0 ? void 0 : _a.id]);
  (0, import_react17.useEffect)(function() {
    var frame = divRef.current.parentElement;
    if (!frame) {
      return;
    }
    var onScroll = function() {
      var _a2;
      updatePosition((_a2 = store.hoverRow) === null || _a2 === void 0 ? void 0 : _a2.id);
    };
    frame.addEventListener("scroll", onScroll);
    return function() {
      frame.removeEventListener("scroll", onScroll);
    };
  });
  return import_react17.default.createElement("div", { className: cx("Table-itemActions-wrap"), ref: divRef }, children);
}
var ItemActionsWrapper$1 = observer2(ItemActionsWrapper);

// node_modules/amis/esm/renderers/Table/ColGroup.js
var import_react18 = __toESM(require_react());
function ColGroup(_a) {
  var columns = _a.columns, store = _a.store;
  var domRef = import_react18.default.createRef();
  import_react18.default.useEffect(function() {
    var table = domRef.current.parentElement;
    var trs = [];
    function reConnect() {
      var doms = [].slice.call(table.querySelectorAll(":scope > thead > tr > *"));
      if (doms.some(function(d, index) {
        return trs[index] !== d;
      })) {
        observer3.disconnect();
        trs = doms;
        doms.forEach(function(dom) {
          observer3.observe(dom);
        });
      }
    }
    var observer3 = new ResizeObserver(function() {
      reConnect();
      store.syncTableWidth();
    });
    store.initTableWidth();
    store.syncTableWidth();
    reConnect();
    return function() {
      observer3.disconnect();
    };
  }, [columns.length]);
  if (isSafari || typeof chromeVersion === "number" && chromeVersion < 91) {
    import_react18.default.useEffect(function() {
      if (domRef.current) {
        var ths = [].slice.call(domRef.current.parentElement.querySelectorAll(":scope > thead > tr > th[data-index]"));
        ths.forEach(function(th) {
          var index = parseInt(th.getAttribute("data-index"), 10);
          var column = store.columns[index];
          var style = "";
          var width = -1;
          if (store.columnWidthReady && column.width) {
            width = column.width;
          } else if (column.pristine.width) {
            width = column.pristine.width;
          }
          if (width === -1) {
            return;
          }
          style += "width: ".concat(
            // 有可能是百分比
            typeof width === "number" ? "".concat(width, "px") : width,
            ";"
          );
          if (store.tableLayout === "auto") {
            style += "min-width: ".concat(typeof width === "number" ? "".concat(width, "px") : width, ";");
          }
          th.style.cssText = style;
        });
      }
    }, columns.map(function(column) {
      return column.width;
    }).concat(store.columnWidthReady));
  }
  return import_react18.default.createElement("colgroup", { ref: domRef }, columns.map(function(column) {
    var style = {};
    if (store.columnWidthReady && column.width) {
      style.width = column.width;
    } else if (column.pristine.width) {
      style.width = column.pristine.width;
    }
    if (store.tableLayout === "auto" && style.width) {
      style.minWidth = style.width;
    }
    return import_react18.default.createElement("col", { "data-index": column.index, style, key: column.id });
  }));
}
var ColGroup$1 = observer2(ColGroup);

// node_modules/amis/esm/renderers/Table/TableContent.js
function renderItemActions(props) {
  var itemActions = props.itemActions, render = props.render, store = props.store, cx = props.classnames;
  if (!store.hoverRow) {
    return null;
  }
  var finalActions = Array.isArray(itemActions) ? itemActions.filter(function(action) {
    return !action.hiddenOnHover;
  }) : [];
  if (!finalActions.length) {
    return null;
  }
  return import_react19.default.createElement(
    ItemActionsWrapper$1,
    { store, classnames: cx },
    import_react19.default.createElement("div", { className: cx("Table-itemActions") }, finalActions.map(function(action, index) {
      return render("itemAction/".concat(index), __assign(__assign({}, action), { isMenuItem: true }), {
        key: index,
        item: store.hoverRow,
        data: store.hoverRow.locals,
        rowIndex: store.hoverRow.index
      });
    }))
  );
}
var TableContent = (
  /** @class */
  function(_super) {
    __extends(TableContent2, _super);
    function TableContent2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    TableContent2.prototype.render = function() {
      var _a = this.props, placeholder = _a.placeholder, cx = _a.classnames, render = _a.render, className = _a.className, columns = _a.columns, columnsGroup = _a.columnsGroup, onMouseMove = _a.onMouseMove, onScroll = _a.onScroll, tableRef = _a.tableRef, rows = _a.rows, renderHeadCell = _a.renderHeadCell, renderCell = _a.renderCell, onCheck = _a.onCheck, onRowClick = _a.onRowClick, onRowDbClick = _a.onRowDbClick, onRowMouseEnter = _a.onRowMouseEnter, onRowMouseLeave = _a.onRowMouseLeave, rowClassName = _a.rowClassName, onQuickChange = _a.onQuickChange, footable = _a.footable, footableColumns = _a.footableColumns, checkOnItemClick = _a.checkOnItemClick, buildItemProps = _a.buildItemProps, onAction = _a.onAction, rowClassNameExpr = _a.rowClassNameExpr, affixRowClassName = _a.affixRowClassName, prefixRowClassName = _a.prefixRowClassName, data = _a.data, prefixRow = _a.prefixRow, locale = _a.locale, translate = _a.translate, itemAction = _a.itemAction, affixRow = _a.affixRow, store = _a.store, dispatchEvent = _a.dispatchEvent, onEvent = _a.onEvent, loading = _a.loading, testIdBuilder = _a.testIdBuilder, children = _a.children;
      var tableClassName = cx("Table-table", this.props.tableClassName);
      var hideHeader = columns.every(function(column) {
        return !column.label;
      });
      return import_react19.default.createElement(
        "div",
        { onMouseMove, className: cx("Table-content", className), onScroll },
        import_react19.default.createElement(
          "table",
          { ref: tableRef, className: cx(tableClassName, store.tableLayout === "fixed" ? "is-layout-fixed" : void 0) },
          import_react19.default.createElement(ColGroup$1, { columns, store }),
          import_react19.default.createElement(
            "thead",
            null,
            columnsGroup.length ? import_react19.default.createElement("tr", null, columnsGroup.map(function(item, index) {
              var _a2 = __read(store.getStickyStyles(item, columnsGroup), 2), stickyStyle = _a2[0], stickyClassName = _a2[1];
              return !!~["__checkme", "__expandme"].indexOf(item.has[0].type) || item.has.length === 1 && !/^__/.test(item.has[0].type) && !item.has[0].groupName ? renderHeadCell(item.has[0], {
                "data-index": item.has[0].index,
                "key": index,
                "colSpan": item.colSpan,
                "rowSpan": item.rowSpan,
                "style": stickyStyle,
                "className": stickyClassName
              }) : import_react19.default.createElement("th", { key: index, "data-index": item.index, colSpan: item.colSpan, rowSpan: item.rowSpan, style: stickyStyle, className: stickyClassName }, item.label ? render("tpl", item.label) : null);
            })) : null,
            import_react19.default.createElement("tr", { className: hideHeader ? "fake-hide" : "" }, columns.map(function(column) {
              var _a2;
              return ((_a2 = columnsGroup.find(function(group) {
                return ~group.has.indexOf(column);
              })) === null || _a2 === void 0 ? void 0 : _a2.rowSpan) === 2 ? null : renderHeadCell(column, {
                "data-index": column.index,
                "key": column.index
              });
            }))
          ),
          !rows.length ? import_react19.default.createElement(
            "tbody",
            null,
            import_react19.default.createElement("tr", { className: cx("Table-placeholder") }, !loading ? import_react19.default.createElement("td", { colSpan: columns.length }, typeof placeholder === "string" ? import_react19.default.createElement(
              import_react19.default.Fragment,
              null,
              import_react19.default.createElement(Icon, { icon: "desk-empty", className: cx("Table-placeholder-empty-icon", "icon") }),
              translate(placeholder || "placeholder.noData")
            ) : render("placeholder", translate(placeholder || "placeholder.noData"))) : null)
          ) : import_react19.default.createElement(TableBody, { store, itemAction, classnames: cx, render, renderCell, onCheck, onRowClick, onRowDbClick, onRowMouseEnter, onRowMouseLeave, onQuickChange, footable, footableColumns, checkOnItemClick, buildItemProps, onAction, rowClassNameExpr, rowClassName, prefixRowClassName, affixRowClassName, rows, columns, locale, translate, prefixRow, affixRow, data, testIdBuilder, rowsProps: {
            dispatchEvent,
            onEvent
          } })
        ),
        children
      );
    };
    return TableContent2;
  }(import_react19.default.PureComponent)
);
var TableContent$1 = observer2(function(props) {
  var store = props.store;
  return import_react19.default.createElement(TableContent, __assign({}, props, { columnWidthReady: store.columnWidthReady, someChecked: store.someChecked, allChecked: store.allChecked, isSelectionThresholdReached: store.isSelectionThresholdReached, orderBy: store.orderBy, orderDir: store.orderDir }));
});

// node_modules/amis/esm/renderers/Table/exportExcel.js
var import_file_saver = __toESM(require_FileSaver_min());
var import_memoize = __toESM(require_memoize());
init_moment();
var loadDb = function() {
  return import("./CityDB-5QLYG4CN.js");
};
var getAbsoluteUrl = /* @__PURE__ */ function() {
  var link;
  return function(url) {
    if (!link)
      link = document.createElement("a");
    link.href = url;
    return link.href;
  };
}();
var rgba2argb = (0, import_memoize.default)(function(rgba) {
  var color = "".concat(rgba.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+\.{0,1}\d*))?\)$/).slice(1).map(function(n, i) {
    return (i === 3 ? Math.round(parseFloat(n) * 255) : parseFloat(n)).toString(16).padStart(2, "0").replace("NaN", "");
  }).join(""));
  if (color.length === 6) {
    return "FF" + color;
  }
  return color;
});
var getCellStyleByClassName = (0, import_memoize.default)(function(className) {
  if (!className)
    return {};
  var classNameElm = document.getElementsByClassName(className).item(0);
  if (classNameElm) {
    var computedStyle = getComputedStyle(classNameElm);
    var font = {};
    var fill = {};
    if (computedStyle.color && computedStyle.color.indexOf("rgb") !== -1) {
      var color = rgba2argb(computedStyle.color);
      if (!color.startsWith("00")) {
        font["color"] = { argb: color };
      }
    }
    if (computedStyle.fontWeight && parseInt(computedStyle.fontWeight) >= 700) {
      font["bold"] = true;
    }
    if (computedStyle.backgroundColor && computedStyle.backgroundColor.indexOf("rgb") !== -1) {
      var color = rgba2argb(computedStyle.backgroundColor);
      if (!color.startsWith("00")) {
        fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: color }
        };
      }
    }
    return { font, fill };
  }
  return {};
});
var applyCellStyle = function(sheetRow, columIndex, schema, data) {
  var e_1, _a, e_2, _b;
  var cellStyle = {};
  if (schema.className) {
    try {
      for (var _c = __values(schema.className.split(/\s+/)), _d = _c.next(); !_d.done; _d = _c.next()) {
        var className = _d.value;
        var style = getCellStyleByClassName(className);
        if (style) {
          cellStyle = __assign(__assign({}, cellStyle), style);
        }
      }
    } catch (e_1_1) {
      e_1 = { error: e_1_1 };
    } finally {
      try {
        if (_d && !_d.done && (_a = _c.return))
          _a.call(_c);
      } finally {
        if (e_1)
          throw e_1.error;
      }
    }
  }
  if (schema.classNameExpr) {
    var classNames = filter(schema.classNameExpr, data);
    if (classNames) {
      try {
        for (var _e = __values(classNames.split(/\s+/)), _f = _e.next(); !_f.done; _f = _e.next()) {
          var className = _f.value;
          var style = getCellStyleByClassName(className);
          if (style) {
            cellStyle = __assign(__assign({}, cellStyle), style);
          }
        }
      } catch (e_2_1) {
        e_2 = { error: e_2_1 };
      } finally {
        try {
          if (_f && !_f.done && (_b = _e.return))
            _b.call(_e);
        } finally {
          if (e_2)
            throw e_2.error;
        }
      }
    }
  }
  if (cellStyle.font && Object.keys(cellStyle.font).length > 0) {
    sheetRow.getCell(columIndex).font = cellStyle.font;
  }
  if (cellStyle.fill && Object.keys(cellStyle.fill).length > 0) {
    sheetRow.getCell(columIndex).fill = cellStyle.fill;
  }
};
var renderSummary = function(worksheet, data, summarySchema, rowIndex) {
  var e_3, _a, e_4, _b;
  if (summarySchema && summarySchema.length > 0) {
    var firstSchema = summarySchema[0];
    var affixRows = summarySchema;
    if (!Array.isArray(firstSchema)) {
      affixRows = [summarySchema];
    }
    try {
      for (var affixRows_1 = __values(affixRows), affixRows_1_1 = affixRows_1.next(); !affixRows_1_1.done; affixRows_1_1 = affixRows_1.next()) {
        var affix = affixRows_1_1.value;
        rowIndex += 1;
        var sheetRow = worksheet.getRow(rowIndex);
        var columIndex = 0;
        try {
          for (var affix_1 = (e_4 = void 0, __values(affix)), affix_1_1 = affix_1.next(); !affix_1_1.done; affix_1_1 = affix_1.next()) {
            var col = affix_1_1.value;
            columIndex += 1;
            if (col.text) {
              sheetRow.getCell(columIndex).value = col.text;
            }
            if (col.tpl) {
              sheetRow.getCell(columIndex).value = removeHTMLTag(decodeEntity(filter(col.tpl, data)));
            }
            if (col.colSpan) {
              worksheet.mergeCells(rowIndex, columIndex, rowIndex, columIndex + col.colSpan - 1);
              columIndex += col.colSpan - 1;
            }
          }
        } catch (e_4_1) {
          e_4 = { error: e_4_1 };
        } finally {
          try {
            if (affix_1_1 && !affix_1_1.done && (_b = affix_1.return))
              _b.call(affix_1);
          } finally {
            if (e_4)
              throw e_4.error;
          }
        }
      }
    } catch (e_3_1) {
      e_3 = { error: e_3_1 };
    } finally {
      try {
        if (affixRows_1_1 && !affixRows_1_1.done && (_a = affixRows_1.return))
          _a.call(affixRows_1);
      } finally {
        if (e_3)
          throw e_3.error;
      }
    }
  }
  return rowIndex;
};
function getMap(remoteMappingCache, env, column, data, rowData) {
  return __awaiter(this, void 0, void 0, function() {
    var map, source, sourceValue, mapKey, res;
    return __generator(this, function(_a) {
      switch (_a.label) {
        case 0:
          map = column.pristine.map;
          source = column.pristine.source;
          if (!source)
            return [3, 4];
          sourceValue = source;
          if (!isPureVariable(source))
            return [3, 1];
          map = resolveVariableAndFilter(source, rowData, "| raw");
          return [3, 4];
        case 1:
          if (!isEffectiveApi(source, data))
            return [3, 4];
          mapKey = JSON.stringify(source);
          if (!(mapKey in remoteMappingCache))
            return [3, 2];
          map = remoteMappingCache[mapKey];
          return [3, 4];
        case 2:
          return [4, env.fetcher(sourceValue, rowData)];
        case 3:
          res = _a.sent();
          if (res.data) {
            remoteMappingCache[mapKey] = res.data;
            map = res.data;
          }
          _a.label = 4;
        case 4:
          return [2, map];
      }
    });
  });
}
function exportExcel(ExcelJS, props, toolbar, withoutData) {
  var _a, _b, _c, _d;
  if (withoutData === void 0) {
    withoutData = false;
  }
  return __awaiter(this, void 0, void 0, function() {
    var store, env, cx, __, data, prefixRow, affixRow, columns, rows, tmpStore, filename, pageField, perPageField, ctx, res, _e, _f, key, workbook, worksheet, exportColumnNames, hasCustomExportColumns, columns_1, columns_1_1, column, filteredColumns, firstRowLabels, firstRow, remoteMappingCache, rowIndex, rows_1, rows_1_1, row, rowData, sheetRow, columIndex, _loop_1, filteredColumns_1, filteredColumns_1_1, column, e_5_1, e_6_1;
    var _g, e_7, _h, e_8, _j, e_6, _k, e_5, _l;
    return __generator(this, function(_m) {
      switch (_m.label) {
        case 0:
          store = props.store, env = props.env, cx = props.classnames, __ = props.translate, data = props.data, prefixRow = props.prefixRow, affixRow = props.affixRow;
          columns = store.exportColumns || [];
          rows = [];
          filename = "data";
          if (!(typeof toolbar === "object" && toolbar.api))
            return [3, 2];
          pageField = toolbar.pageField || "page";
          perPageField = toolbar.perPageField || "perPage";
          ctx = createObject(data, __assign(__assign({}, props.query), (_g = {}, _g[pageField] = data.page || 1, _g[perPageField] = data.perPage || 10, _g)));
          return [4, env.fetcher(toolbar.api, ctx, {
            autoAppend: true,
            pageField,
            perPageField
          })];
        case 1:
          res = _m.sent();
          if (!res.data) {
            env.notify("warning", __("placeholder.noData"));
            return [
              2
              /*return*/
            ];
          }
          if (Array.isArray(res.data)) {
            rows = res.data;
          } else if (Array.isArray((_a = res.data) === null || _a === void 0 ? void 0 : _a.rows)) {
            rows = res.data.rows;
          } else if (Array.isArray((_b = res.data) === null || _b === void 0 ? void 0 : _b.items)) {
            rows = res.data.items;
          } else {
            try {
              for (_e = __values(Object.keys(res.data)), _f = _e.next(); !_f.done; _f = _e.next()) {
                key = _f.value;
                if (res.data.hasOwnProperty(key) && Array.isArray(res.data[key])) {
                  rows = res.data[key];
                  break;
                }
              }
            } catch (e_7_1) {
              e_7 = { error: e_7_1 };
            } finally {
              try {
                if (_f && !_f.done && (_h = _e.return))
                  _h.call(_e);
              } finally {
                if (e_7)
                  throw e_7.error;
              }
            }
          }
          tmpStore = TableStore.create(getSnapshot(store));
          tmpStore.initRows(rows);
          rows = tmpStore.rows;
          return [3, 3];
        case 2:
          rows = store.rows;
          _m.label = 3;
        case 3:
          if (typeof toolbar === "object" && toolbar.filename) {
            filename = filter(toolbar.filename, data, "| raw");
          }
          if (rows.length === 0) {
            env.notify("warning", __("placeholder.noData"));
            return [
              2
              /*return*/
            ];
          }
          workbook = new ExcelJS.Workbook();
          worksheet = workbook.addWorksheet("sheet", {
            properties: { defaultColWidth: 15 }
          });
          worksheet.views = [{ state: "frozen", xSplit: 0, ySplit: 1 }];
          exportColumnNames = toolbar.columns;
          if (isPureVariable(exportColumnNames)) {
            exportColumnNames = resolveVariableAndFilter(exportColumnNames, data, "| raw");
          }
          hasCustomExportColumns = toolbar.exportColumns && Array.isArray(toolbar.exportColumns);
          if (hasCustomExportColumns) {
            columns = toolbar.exportColumns;
            try {
              for (columns_1 = __values(columns), columns_1_1 = columns_1.next(); !columns_1_1.done; columns_1_1 = columns_1.next()) {
                column = columns_1_1.value;
                column.pristine = column;
              }
            } catch (e_8_1) {
              e_8 = { error: e_8_1 };
            } finally {
              try {
                if (columns_1_1 && !columns_1_1.done && (_j = columns_1.return))
                  _j.call(columns_1);
              } finally {
                if (e_8)
                  throw e_8.error;
              }
            }
          }
          filteredColumns = exportColumnNames ? columns.filter(function(column2) {
            var filterColumnsNames = exportColumnNames;
            if (column2.name && filterColumnsNames.indexOf(column2.name) !== -1) {
              return hasCustomExportColumns ? true : (column2 === null || column2 === void 0 ? void 0 : column2.type) !== "operation";
            }
            return false;
          }) : columns.filter(function(column2) {
            return (column2 === null || column2 === void 0 ? void 0 : column2.type) !== "operation";
          });
          firstRowLabels = filteredColumns.map(function(column2) {
            return filter(column2.label, data);
          });
          firstRow = worksheet.getRow(1);
          firstRow.values = firstRowLabels;
          worksheet.autoFilter = {
            from: {
              row: 1,
              column: 1
            },
            to: {
              row: 1,
              column: firstRowLabels.length
            }
          };
          if (withoutData) {
            return [2, exportExcelWithoutData(workbook, worksheet, filteredColumns, filename, env, data)];
          }
          remoteMappingCache = {};
          rowIndex = 1;
          if (toolbar.rowSlice) {
            rows = arraySlice(rows, toolbar.rowSlice);
          }
          rowIndex = renderSummary(worksheet, data, prefixRow, rowIndex);
          rows = flattenTree(rows, function(item) {
            return item;
          });
          _m.label = 4;
        case 4:
          _m.trys.push([4, 15, 16, 17]);
          rows_1 = __values(rows), rows_1_1 = rows_1.next();
          _m.label = 5;
        case 5:
          if (!!rows_1_1.done)
            return [3, 14];
          row = rows_1_1.value;
          rowData = createObject(data, row.data);
          rowIndex += 1;
          sheetRow = worksheet.getRow(rowIndex);
          columIndex = 0;
          _loop_1 = function(column2) {
            var name_1, value, type, imageData, imageDimensions, imageWidth, imageHeight, imageMaxSize, imageMatch, imageExt, imageId, linkURL, e_9, href, linkURL, body, text, absoluteURL, map, valueField_1, labelField, viewValue, label, text, viewValue, _o, fromNow, _p, format, _q, valueFormat, ISODate, NormalDate, db, cellValue;
            return __generator(this, function(_r) {
              switch (_r.label) {
                case 0:
                  columIndex += 1;
                  name_1 = column2.name;
                  value = getVariable(rowData, name_1);
                  if (typeof value === "undefined" && !column2.pristine.tpl) {
                    return [2, "continue"];
                  }
                  if (name_1 in row.rowSpans) {
                    if (row.rowSpans[name_1] === 0) {
                      return [2, "continue"];
                    } else {
                      worksheet.mergeCells(rowIndex, columIndex, rowIndex + row.rowSpans[name_1] - 1, columIndex);
                    }
                  }
                  applyCellStyle(sheetRow, columIndex, column2.pristine, rowData);
                  type = column2.type || "plain";
                  if (!((type === "image" || type === "static-image") && value))
                    return [3, 6];
                  _r.label = 1;
                case 1:
                  _r.trys.push([1, 4, , 5]);
                  return [4, toDataURL(value)];
                case 2:
                  imageData = _r.sent();
                  return [4, getImageDimensions(imageData)];
                case 3:
                  imageDimensions = _r.sent();
                  imageWidth = imageDimensions.width;
                  imageHeight = imageDimensions.height;
                  imageMaxSize = 100;
                  if (imageWidth > imageHeight) {
                    if (imageWidth > imageMaxSize) {
                      imageHeight = imageMaxSize * imageHeight / imageWidth;
                      imageWidth = imageMaxSize;
                    }
                  } else {
                    if (imageHeight > imageMaxSize) {
                      imageWidth = imageMaxSize * imageWidth / imageHeight;
                      imageHeight = imageMaxSize;
                    }
                  }
                  imageMatch = imageData.match(/data:image\/(.*);/);
                  imageExt = "png";
                  if (imageMatch) {
                    imageExt = imageMatch[1];
                  }
                  if (imageExt != "png" && imageExt != "jpeg" && imageExt != "gif") {
                    sheetRow.getCell(columIndex).value = value;
                    return [2, "continue"];
                  }
                  imageId = workbook.addImage({
                    base64: imageData,
                    extension: imageExt
                  });
                  linkURL = getAbsoluteUrl(value);
                  worksheet.addImage(imageId, {
                    // 这里坐标位置是从 0 开始的，所以要减一
                    tl: { col: columIndex - 1, row: rowIndex - 1 },
                    ext: {
                      width: imageWidth,
                      height: imageHeight
                    },
                    hyperlinks: {
                      tooltip: linkURL
                    }
                  });
                  return [3, 5];
                case 4:
                  e_9 = _r.sent();
                  console.warn(e_9);
                  return [3, 5];
                case 5:
                  return [3, 13];
                case 6:
                  if (!(type == "link" || type === "static-link"))
                    return [3, 7];
                  href = column2.pristine.href;
                  linkURL = (typeof href === "string" && href ? filter(href, rowData, "| raw") : void 0) || value;
                  body = column2.pristine.body;
                  text = typeof body === "string" && body ? filter(body, rowData, "| raw") : void 0;
                  absoluteURL = getAbsoluteUrl(linkURL);
                  sheetRow.getCell(columIndex).value = {
                    text: text || absoluteURL,
                    hyperlink: absoluteURL
                  };
                  return [3, 13];
                case 7:
                  if (!(type === "mapping" || type === "static-mapping"))
                    return [3, 9];
                  return [4, getMap(remoteMappingCache, env, column2, data, rowData)];
                case 8:
                  map = _r.sent();
                  valueField_1 = column2.pristine.valueField || "value";
                  labelField = column2.pristine.labelField || "label";
                  if (Array.isArray(map)) {
                    map = map.reduce(function(res2, now) {
                      if (now == null) {
                        return res2;
                      } else if (isObject(now)) {
                        var keys = Object.keys(now);
                        if (keys.length === 1 || keys.length == 2 && keys.includes("$$id")) {
                          keys = keys.filter(function(key2) {
                            return key2 !== "$$id";
                          });
                          res2[keys[0]] = now[keys[0]];
                        } else if (keys.length > 1) {
                          res2[now[valueField_1]] = now;
                        }
                      }
                      return res2;
                    }, {});
                  }
                  if (typeof value !== "undefined" && map && ((_c = map[value]) !== null && _c !== void 0 ? _c : map["*"])) {
                    viewValue = (_d = map[value]) !== null && _d !== void 0 ? _d : value === true && map["1"] ? map["1"] : value === false && map["0"] ? map["0"] : map["*"];
                    label = viewValue;
                    if (isObject(viewValue)) {
                      if (labelField === void 0 || labelField === "") {
                        if (!viewValue.hasOwnProperty("type")) {
                          label = viewValue["label"];
                        }
                      } else {
                        label = viewValue[labelField || "label"];
                      }
                    }
                    text = removeHTMLTag(label);
                    if (isPureVariable(text)) {
                      text = resolveVariableAndFilter(text, rowData, "| raw");
                    } else {
                      text = filter(text, rowData);
                    }
                    sheetRow.getCell(columIndex).value = text;
                  } else {
                    sheetRow.getCell(columIndex).value = removeHTMLTag(value);
                  }
                  return [3, 13];
                case 9:
                  if (!(type === "date" || type === "static-date"))
                    return [3, 10];
                  viewValue = void 0;
                  _o = column2.pristine, fromNow = _o.fromNow, _p = _o.format, format = _p === void 0 ? "YYYY-MM-DD" : _p, _q = _o.valueFormat, valueFormat = _q === void 0 ? "X" : _q;
                  if (value) {
                    ISODate = moment_default(value, moment_default.ISO_8601);
                    NormalDate = moment_default(value, valueFormat);
                    viewValue = ISODate.isValid() ? ISODate.format(format) : NormalDate.isValid() ? NormalDate.format(format) : false;
                  }
                  if (fromNow) {
                    viewValue = moment_default(value).fromNow();
                  }
                  if (viewValue) {
                    sheetRow.getCell(columIndex).value = viewValue;
                  }
                  return [3, 13];
                case 10:
                  if (!(type === "input-city"))
                    return [3, 12];
                  return [4, loadDb()];
                case 11:
                  db = _r.sent();
                  if (db.default && value && value in db.default) {
                    sheetRow.getCell(columIndex).value = db.default[value];
                  }
                  return [3, 13];
                case 12:
                  if (column2.pristine.tpl) {
                    sheetRow.getCell(columIndex).value = removeHTMLTag(decodeEntity(filter(column2.pristine.tpl, rowData)));
                  } else {
                    sheetRow.getCell(columIndex).value = value;
                  }
                  _r.label = 13;
                case 13:
                  cellValue = sheetRow.getCell(columIndex).value;
                  if (Number.isInteger(cellValue)) {
                    sheetRow.getCell(columIndex).numFmt = "0";
                  }
                  return [
                    2
                    /*return*/
                  ];
              }
            });
          };
          _m.label = 6;
        case 6:
          _m.trys.push([6, 11, 12, 13]);
          filteredColumns_1 = (e_5 = void 0, __values(filteredColumns)), filteredColumns_1_1 = filteredColumns_1.next();
          _m.label = 7;
        case 7:
          if (!!filteredColumns_1_1.done)
            return [3, 10];
          column = filteredColumns_1_1.value;
          return [5, _loop_1(column)];
        case 8:
          _m.sent();
          _m.label = 9;
        case 9:
          filteredColumns_1_1 = filteredColumns_1.next();
          return [3, 7];
        case 10:
          return [3, 13];
        case 11:
          e_5_1 = _m.sent();
          e_5 = { error: e_5_1 };
          return [3, 13];
        case 12:
          try {
            if (filteredColumns_1_1 && !filteredColumns_1_1.done && (_l = filteredColumns_1.return))
              _l.call(filteredColumns_1);
          } finally {
            if (e_5)
              throw e_5.error;
          }
          return [
            7
            /*endfinally*/
          ];
        case 13:
          rows_1_1 = rows_1.next();
          return [3, 5];
        case 14:
          return [3, 17];
        case 15:
          e_6_1 = _m.sent();
          e_6 = { error: e_6_1 };
          return [3, 17];
        case 16:
          try {
            if (rows_1_1 && !rows_1_1.done && (_k = rows_1.return))
              _k.call(rows_1);
          } finally {
            if (e_6)
              throw e_6.error;
          }
          return [
            7
            /*endfinally*/
          ];
        case 17:
          renderSummary(worksheet, data, affixRow, rowIndex);
          downloadFile(workbook, filename);
          return [
            2
            /*return*/
          ];
      }
    });
  });
}
function downloadFile(workbook, filename) {
  return __awaiter(this, void 0, void 0, function() {
    var buffer, blob;
    return __generator(this, function(_a) {
      switch (_a.label) {
        case 0:
          return [4, workbook.xlsx.writeBuffer()];
        case 1:
          buffer = _a.sent();
          if (buffer) {
            blob = new Blob([buffer], {
              type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            });
            (0, import_file_saver.saveAs)(blob, filename + ".xlsx");
          }
          return [
            2
            /*return*/
          ];
      }
    });
  });
}
function numberToLetters(num) {
  var letters = "";
  while (num >= 0) {
    letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"[num % 26] + letters;
    num = Math.floor(num / 26) - 1;
  }
  return letters;
}
function exportExcelWithoutData(workbook, worksheet, filteredColumns, filename, env, data) {
  var _a;
  return __awaiter(this, void 0, void 0, function() {
    var index, rowNumber, mapCache, filteredColumns_2, filteredColumns_2_1, column, map, keys, rowIndex, e_10_1;
    var e_10, _b;
    return __generator(this, function(_c) {
      switch (_c.label) {
        case 0:
          index = 0;
          rowNumber = 100;
          mapCache = {};
          _c.label = 1;
        case 1:
          _c.trys.push([1, 6, 7, 8]);
          filteredColumns_2 = __values(filteredColumns), filteredColumns_2_1 = filteredColumns_2.next();
          _c.label = 2;
        case 2:
          if (!!filteredColumns_2_1.done)
            return [3, 5];
          column = filteredColumns_2_1.value;
          index += 1;
          if (!(((_a = column.pristine) === null || _a === void 0 ? void 0 : _a.type) === "mapping"))
            return [3, 4];
          return [4, getMap(mapCache, env, column, data, {})];
        case 3:
          map = _c.sent();
          if (map && isObject(map)) {
            keys = Object.keys(map);
            for (rowIndex = 1; rowIndex < rowNumber; rowIndex++) {
              worksheet.getCell(numberToLetters(index) + rowIndex).dataValidation = {
                type: "list",
                allowBlank: true,
                formulae: ['"'.concat(keys.join(","), '"')]
              };
            }
          }
          _c.label = 4;
        case 4:
          filteredColumns_2_1 = filteredColumns_2.next();
          return [3, 2];
        case 5:
          return [3, 8];
        case 6:
          e_10_1 = _c.sent();
          e_10 = { error: e_10_1 };
          return [3, 8];
        case 7:
          try {
            if (filteredColumns_2_1 && !filteredColumns_2_1.done && (_b = filteredColumns_2.return))
              _b.call(filteredColumns_2);
          } finally {
            if (e_10)
              throw e_10.error;
          }
          return [
            7
            /*endfinally*/
          ];
        case 8:
          downloadFile(workbook, filename);
          return [
            2
            /*return*/
          ];
      }
    });
  });
}

// node_modules/amis/esm/renderers/Table/AutoFilterForm.js
var import_react20 = __toESM(require_react());
function AutoFilterForm(_a) {
  var autoGenerateFilter = _a.autoGenerateFilter, searchFormExpanded = _a.searchFormExpanded, activedSearchableColumns = _a.activedSearchableColumns, searchableColumns = _a.searchableColumns, onItemToggleExpanded = _a.onItemToggleExpanded, onToggleExpanded = _a.onToggleExpanded, cx = _a.classnames, __ = _a.translate, render = _a.render, data = _a.data, onSearchableFromReset = _a.onSearchableFromReset, onSearchableFromSubmit = _a.onSearchableFromSubmit, onSearchableFromInit = _a.onSearchableFromInit, popOverContainer = _a.popOverContainer, testIdBuilder = _a.testIdBuilder, canAccessSuperData = _a.canAccessSuperData;
  var schema = import_react20.default.useMemo(function() {
    var _a2 = typeof autoGenerateFilter === "boolean" ? {
      columnsNum: 3,
      showBtnToolbar: true
    } : autoGenerateFilter, columnsNum = _a2.columnsNum, showBtnToolbar = _a2.showBtnToolbar;
    var body = padArr(activedSearchableColumns, columnsNum).map(function(group) {
      return {
        type: "group",
        body: group.map(function(column) {
          var _a3, _b, _c, _d;
          return __assign(__assign({}, column.searchable === true ? {
            type: "input-text",
            name: column.name,
            label: column.label,
            testIdBuilder: testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild(column.name)
          } : __assign({ type: "input-text", name: column.name, testIdBuilder: testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild(column.name) }, column.searchable)), { name: (_b = (_a3 = column.searchable) === null || _a3 === void 0 ? void 0 : _a3.name) !== null && _b !== void 0 ? _b : column.name, label: (_d = (_c = column.searchable) === null || _c === void 0 ? void 0 : _c.label) !== null && _d !== void 0 ? _d : column.label });
        })
      };
    });
    var showExpander = activedSearchableColumns.length >= columnsNum;
    if (!searchFormExpanded && body.length) {
      body.splice(1, body.length - 1);
      body[0].body.splice(columnsNum - 1, body[0].body.length - columnsNum + 1);
    }
    var lastGroup = body[body.length - 1];
    if (!Array.isArray(lastGroup === null || lastGroup === void 0 ? void 0 : lastGroup.body) || lastGroup.body.length >= columnsNum) {
      lastGroup = {
        type: "group",
        body: []
      };
      body.push(lastGroup);
    }
    var count = Math.max(columnsNum - lastGroup.body.length - 1);
    while (count-- > 0) {
      lastGroup.body.push({
        type: "tpl",
        tpl: ""
      });
    }
    var moreTestIdBuilder = testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild("more");
    lastGroup.body.push({
      type: "container",
      className: "AutoFilterToolbar",
      wrapperBody: false,
      body: [
        {
          type: "dropdown-button",
          label: __("Table.searchFields"),
          className: cx("Table-searchableForm-dropdown", "mr-2"),
          level: "link",
          trigger: "click",
          size: "sm",
          align: "right",
          visible: showBtnToolbar,
          testIdBuilder: moreTestIdBuilder,
          buttons: searchableColumns.map(function(column) {
            return {
              children: function(_a3) {
                var _b, _c;
                var render2 = _a3.render;
                return render2("column-search-toggler-".concat(column.id), {
                  type: "checkbox",
                  label: false,
                  className: cx("Table-searchableForm-checkbox"),
                  inputClassName: cx("Table-searchableForm-checkbox-inner"),
                  name: "__whatever_name",
                  option: (_c = (_b = column.searchable) === null || _b === void 0 ? void 0 : _b.label) !== null && _c !== void 0 ? _c : column.label,
                  testIdBuilder: moreTestIdBuilder === null || moreTestIdBuilder === void 0 ? void 0 : moreTestIdBuilder.getChild(column.name + ""),
                  badge: {
                    offset: [-10, 5],
                    visibleOn: "".concat(column.toggable && !column.toggled && column.enableSearch)
                  }
                }, {
                  value: activedSearchableColumns.includes(column),
                  onChange: function(value) {
                    return onItemToggleExpanded === null || onItemToggleExpanded === void 0 ? void 0 : onItemToggleExpanded(column, value);
                  }
                });
              }
            };
          })
        },
        {
          type: "submit",
          label: __("search"),
          size: "sm",
          level: "primary",
          className: "w-18 mr-2"
        },
        {
          type: "reset",
          label: __("reset"),
          size: "sm",
          className: "w-18",
          actionType: "clear-and-submit"
        },
        {
          children: function() {
            return showExpander ? import_react20.default.createElement(
              "a",
              { className: cx("Table-SFToggler", searchFormExpanded ? "is-expanded" : ""), onClick: onToggleExpanded },
              __(searchFormExpanded ? "collapse" : "expand"),
              import_react20.default.createElement(
                "span",
                { className: cx("Table-SFToggler-arrow") },
                import_react20.default.createElement(Icon, { icon: "right-arrow-bold", className: "icon" })
              )
            ) : null;
          }
        }
      ]
    });
    return {
      type: "form",
      api: null,
      title: "",
      mode: "horizontal",
      submitText: __("search"),
      body,
      actions: [],
      canAccessSuperData
    };
  }, [
    autoGenerateFilter,
    activedSearchableColumns,
    searchableColumns,
    searchFormExpanded,
    canAccessSuperData,
    __
    // 保证语言更新后能重新渲染
  ]);
  return render("searchable-form", schema, {
    key: "searchable-form",
    panelClassName: cx("Table-searchableForm"),
    actionsClassName: cx("Table-searchableForm-footer"),
    onReset: onSearchableFromReset,
    onSubmit: onSearchableFromSubmit,
    onInit: onSearchableFromInit,
    formStore: void 0,
    data,
    popOverContainer
  });
}
var AutoFilterForm$1 = observer2(function(_a) {
  var store = _a.store, query = _a.query, data = _a.data, rest = __rest(_a, ["store", "query", "data"]);
  var onItemToggleExpanded = import_react20.default.useCallback(function(column, value) {
    column.setEnableSearch(value);
    value && store.setSearchFormExpanded(true);
  }, []);
  var onToggleExpanded = import_react20.default.useCallback(function() {
    store.toggleSearchFormExpanded();
  }, []);
  var ctx = import_react20.default.useMemo(function() {
    return query ? createObject(data, query) : data;
  }, [query, data]);
  return import_react20.default.createElement(AutoFilterForm, __assign({}, rest, { activedSearchableColumns: store.activedSearchableColumns, searchableColumns: store.searchableColumns, searchFormExpanded: store.searchFormExpanded, onItemToggleExpanded, onToggleExpanded, data: ctx }));
});

// node_modules/amis/esm/renderers/Table/Cell.js
var import_react21 = __toESM(require_react());
function Cell(_a) {
  var _b;
  var region = _a.region, column = _a.column, item = _a.item, props = _a.props, ignoreDrag = _a.ignoreDrag, render = _a.render, filterItemIndex = _a.filterItemIndex, store = _a.store, multiple = _a.multiple, itemBadge = _a.itemBadge, cx = _a.classnames, ns = _a.classPrefix, canAccessSuperData = _a.canAccessSuperData, onCheck = _a.onCheck, onDragStart = _a.onDragStart, popOverContainer = _a.popOverContainer, quickEditFormRef = _a.quickEditFormRef, onImageEnlarge = _a.onImageEnlarge, __ = _a.translate, testIdBuilder = _a.testIdBuilder;
  if (column.name && item.rowSpans[column.name] === 0) {
    return null;
  }
  var _c = __read(import_react21.default.useMemo(function() {
    var style2 = __assign({}, column.pristine.style);
    var _a2 = __read(store.getStickyStyles(column, store.filteredColumns), 2), stickyStyle = _a2[0], stickyClassName2 = _a2[1];
    return [Object.assign(style2, stickyStyle), stickyClassName2];
  }, []), 2), style = _c[0], stickyClassName = _c[1];
  var onCheckboxChange = import_react21.default.useCallback(function() {
    onCheck === null || onCheck === void 0 ? void 0 : onCheck(item);
  }, []);
  var _d = __read(import_react21.default.useMemo(function() {
    var prefix2 = [];
    var affix2 = [];
    var addtionalClassName2 = "";
    if (column.isPrimary && store.isNested) {
      addtionalClassName2 = "Table-primayCell";
      prefix2.push(import_react21.default.createElement("span", { key: "indent", className: cx("Table-indent"), style: item.indentStyle }));
      prefix2.push(item.loading ? import_react21.default.createElement(Spinner$1, { key: "loading", size: "sm", show: true }) : item.error ? import_react21.default.createElement(
        "a",
        __assign({ className: cx("Table-retryBtn"), key: "retryBtn", onClick: item.resetDefered, "data-tooltip": __("Options.retry", { reason: item.error }) }, testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild("retry").getTestId()),
        import_react21.default.createElement(Icon, { icon: "retry", className: "icon" })
      ) : item.expandable ? import_react21.default.createElement(
        "a",
        __assign({
          key: "expandBtn2",
          className: cx("Table-expandBtn2", item.expanded ? "is-active" : ""),
          // data-tooltip="展开/收起"
          // data-position="top"
          onClick: item.toggleExpanded
        }, testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild(item.expanded ? "fold" : "expand").getTestId()),
        import_react21.default.createElement(Icon, { icon: "right-arrow-bold", className: "icon" })
      ) : import_react21.default.createElement("span", { key: "expandSpace", className: cx("Table-expandSpace") }));
    }
    if (!ignoreDrag && column.isPrimary && store.isNested && store.draggable && item.draggable) {
      affix2.push(import_react21.default.createElement(
        "a",
        __assign({ key: "dragBtn", draggable: true, onDragStart, className: cx("Table-dragBtn") }, testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild("drag").getTestId()),
        import_react21.default.createElement(Icon, { icon: "drag", className: "icon" })
      ));
    }
    return [prefix2, affix2, addtionalClassName2];
  }, [
    item.expandable,
    item.expanded,
    item.error,
    item.loading,
    column.isPrimary,
    store.isNested
  ]), 3), prefix = _d[0], affix = _d[1], addtionalClassName = _d[2];
  var hasCustomTrackExpression = typeof column.pristine.trackExpression !== "undefined";
  var trackExpression = hasCustomTrackExpression ? column.pristine.trackExpression : import_react21.default.useMemo(function() {
    return buildTrackExpression(column.pristine);
  }, []);
  var data = import_react21.default.useMemo(function() {
    return item.locals;
  }, [
    hasCustomTrackExpression ? "" : JSON.stringify(item.locals),
    evalTrackExpression(trackExpression, item.locals)
  ]);
  var finalCanAccessSuperData = (_b = column.pristine.canAccessSuperData) !== null && _b !== void 0 ? _b : canAccessSuperData;
  var subProps = __assign(__assign({}, props), {
    // 操作列不下发loading，否则会导致操作栏里面的所有按钮都出现loading
    loading: column.type === "operation" ? false : props.loading,
    btnDisabled: store.dragging,
    data,
    // 不要下发 value，组件基本上都会自己取
    // 如果下发了表单项会认为是 controlled value
    // 就不会去跑 extraName 之类的逻辑了
    // value: column.name
    //   ? resolveVariable(
    //       column.name,
    //       finalCanAccessSuperData ? item.locals : item.data
    //     )
    //   : column.value,
    popOverContainer,
    rowSpan: item.rowSpans[column.name],
    quickEditFormRef,
    cellPrefix: prefix,
    cellAffix: affix,
    onImageEnlarge,
    canAccessSuperData: finalCanAccessSuperData,
    row: item,
    itemBadge,
    showBadge: !props.isHead && itemBadge && store.firstToggledColumnIndex === props.colIndex,
    onQuery: void 0,
    style,
    className: cx(column.pristine.className, stickyClassName, addtionalClassName),
    testIdBuilder: testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild(column.name || column.value)
  });
  delete subProps.label;
  if (column.type === "__checkme") {
    return import_react21.default.createElement(
      "td",
      __assign({ style, className: cx(column.pristine.className, stickyClassName) }, testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getTestId()),
      import_react21.default.createElement(Checkbox$1, { classPrefix: ns, type: multiple ? "checkbox" : "radio", partial: multiple ? item.partial : false, checked: item.checked || (multiple ? item.partial : false), disabled: item.checkdisable || !item.checkable, onChange: onCheckboxChange, testIdBuilder: testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild("chekbx") })
    );
  } else if (column.type === "__dragme") {
    return import_react21.default.createElement("td", __assign({ style, className: cx(column.pristine.className, stickyClassName, {
      "is-dragDisabled": !item.draggable
    }) }, testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild("drag").getTestId()), item.draggable ? import_react21.default.createElement(Icon, { icon: "drag", className: "icon" }) : null);
  } else if (column.type === "__expandme") {
    return import_react21.default.createElement("td", { style, className: cx(column.pristine.className, stickyClassName) }, item.expandable ? import_react21.default.createElement(
      "a",
      __assign({
        className: cx("Table-expandBtn", item.expanded ? "is-active" : ""),
        // data-tooltip="展开/收起"
        // data-position="top"
        onClick: item.toggleExpanded
      }, testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild(item.expanded ? "fold" : "expand").getTestId()),
      import_react21.default.createElement(Icon, { icon: "right-arrow-bold", className: "icon" })
    ) : null);
  } else if (column.type === "__index") {
    return import_react21.default.createElement("td", { style, className: cx(column.pristine.className, stickyClassName) }, "".concat(filterItemIndex ? filterItemIndex(item.path, item) : item.path).split(".").map(function(a) {
      return parseInt(a, 10) + 1;
    }).join("."));
  }
  return render(region, __assign(__assign({}, column.pristine), {
    // 因为列本身已经做过显隐判断了，单元格不应该再处理
    visibleOn: "",
    hiddenOn: "",
    visible: true,
    hidden: false,
    column: column.pristine,
    type: "cell"
  }), subProps);
}

// node_modules/amis/esm/renderers/Table/index.js
var Table = (
  /** @class */
  function(_super) {
    __extends(Table2, _super);
    function Table2(props, context) {
      var _this = _super.call(this, props) || this;
      _this.dom = import_react22.default.createRef();
      _this.renderedToolbars = [];
      _this.subForms = {};
      _this.toDispose = [];
      _this.updateTableInfoLazy = (0, import_debounce.default)(_this.updateTableInfo.bind(_this), 250, {
        trailing: true,
        leading: false
      });
      _this.updateAutoFillHeightLazy = (0, import_debounce.default)(_this.updateAutoFillHeight.bind(_this), 250, {
        trailing: true,
        leading: false
      });
      var scoped = context;
      scoped.registerComponent(_this);
      _this.handleOutterScroll = _this.handleOutterScroll.bind(_this);
      _this.tableRef = _this.tableRef.bind(_this);
      _this.affixedTableRef = _this.affixedTableRef.bind(_this);
      _this.updateTableInfo = _this.updateTableInfo.bind(_this);
      _this.handleAction = _this.handleAction.bind(_this);
      _this.handleCheck = _this.handleCheck.bind(_this);
      _this.handleCheckAll = _this.handleCheckAll.bind(_this);
      _this.handleQuickChange = _this.handleQuickChange.bind(_this);
      _this.handleSave = _this.handleSave.bind(_this);
      _this.handleSaveOrder = _this.handleSaveOrder.bind(_this);
      _this.reset = _this.reset.bind(_this);
      _this.dragTipRef = _this.dragTipRef.bind(_this);
      _this.getPopOverContainer = _this.getPopOverContainer.bind(_this);
      _this.renderCell = _this.renderCell.bind(_this);
      _this.renderHeadCell = _this.renderHeadCell.bind(_this);
      _this.renderToolbar = _this.renderToolbar.bind(_this);
      _this.handleMouseMove = _this.handleMouseMove.bind(_this);
      _this.handleMouseLeave = _this.handleMouseLeave.bind(_this);
      _this.subFormRef = _this.subFormRef.bind(_this);
      _this.handleColumnToggle = _this.handleColumnToggle.bind(_this);
      _this.handleRowClick = _this.handleRowClick.bind(_this);
      _this.handleRowDbClick = _this.handleRowDbClick.bind(_this);
      _this.handleRowMouseEnter = _this.handleRowMouseEnter.bind(_this);
      _this.handleRowMouseLeave = _this.handleRowMouseLeave.bind(_this);
      _this.updateAutoFillHeight = _this.updateAutoFillHeight.bind(_this);
      var store = props.store, columns = props.columns, selectable = props.selectable, columnsTogglable = props.columnsTogglable, draggable = props.draggable, orderBy = props.orderBy, orderDir = props.orderDir, multiple = props.multiple, footable = props.footable, primaryField = props.primaryField, itemCheckableOn = props.itemCheckableOn, itemDraggableOn = props.itemDraggableOn, hideCheckToggler = props.hideCheckToggler, combineFromIndex = props.combineFromIndex, expandConfig = props.expandConfig, formItem = props.formItem, keepItemSelectionOnPageChange = props.keepItemSelectionOnPageChange, maxKeepItemSelectionLength = props.maxKeepItemSelectionLength, maxItemSelectionLength = props.maxItemSelectionLength, onQuery = props.onQuery, autoGenerateFilter = props.autoGenerateFilter, loading = props.loading, canAccessSuperData = props.canAccessSuperData, lazyRenderAfter = props.lazyRenderAfter, tableLayout = props.tableLayout, resolveDefinitions = props.resolveDefinitions, showIndex = props.showIndex;
      var combineNum = props.combineNum;
      if (typeof combineNum === "string") {
        combineNum = parseInt(resolveVariableAndFilter(combineNum, props.data, "| raw"), 10);
      }
      store.update({
        selectable,
        draggable,
        columns,
        columnsTogglable,
        orderBy: onQuery ? orderBy : void 0,
        orderDir,
        multiple,
        footable,
        expandConfig,
        primaryField,
        itemCheckableOn,
        itemDraggableOn,
        hideCheckToggler,
        combineNum,
        combineFromIndex,
        keepItemSelectionOnPageChange,
        maxKeepItemSelectionLength,
        maxItemSelectionLength,
        loading,
        canAccessSuperData,
        lazyRenderAfter,
        tableLayout,
        showIndex
      }, {
        resolveDefinitions
      });
      if ((0, import_isPlainObject.default)(autoGenerateFilter) && autoGenerateFilter.defaultCollapsed === false) {
        store.setSearchFormExpanded(true);
      }
      formItem && isAlive(formItem) && formItem.setSubStore(store);
      Table2.syncRows(store, _this.props, void 0) && _this.syncSelected();
      _this.toDispose.push(reaction(function() {
        return store.getExpandedRows().filter(function(row) {
          return row.defer && !row.loaded && !row.loading && !row.error;
        });
      }, function(rows) {
        return rows.forEach(_this.loadDeferredRow);
      }));
      return _this;
    }
    Table2.syncRows = function(store, props, prevProps) {
      var source = props.source;
      var value = getPropValue(props, function(props2) {
        return props2.items;
      });
      var rows = [];
      var updateRows = false;
      if (Array.isArray(value)) {
        if (!prevProps || !(0, import_isEqual.default)(getPropValue(prevProps, function(props2) {
          return props2.items;
        }), value)) {
          updateRows = true;
          rows = value;
        }
      } else if (typeof source === "string") {
        var resolved = resolveVariableAndFilter(source, props.data, "| raw");
        var prev = prevProps ? resolveVariableAndFilter(source, prevProps.data, "| raw") : null;
        if (prev === resolved) {
          updateRows = false;
        } else if (loopTooMuch("Table.syncRows".concat(store.id)) && (0, import_isEqual.default)(prev, resolved)) {
          updateRows = false;
        } else {
          updateRows = true;
          rows = Array.isArray(resolved) ? resolved : [];
        }
      }
      if (updateRows) {
        store.initRows(rows, props.getEntryId, props.reUseRow, props.fullItems, props.selected);
      } else if (props.reUseRow === false) {
        updateRows = true;
        store.initRows(value, props.getEntryId, props.reUseRow, props.fullItems, props.selected);
      }
      Array.isArray(props.selected) && store.updateSelected(props.selected, props.valueField);
      return updateRows;
    };
    Table2.prototype.componentDidMount = function() {
      var currentNode = this.dom.current;
      this.initAutoFillHeight();
      this.toDispose.push(resizeSensor(currentNode, this.updateTableInfoLazy, false, "width"));
      var table = this.table;
      var _a = this.props, store = _a.store, autoGenerateFilter = _a.autoGenerateFilter, onSearchableFromInit = _a.onSearchableFromInit;
      if (autoGenerateFilter && !store.searchableColumns.length && onSearchableFromInit) {
        onSearchableFromInit({});
      }
    };
    Table2.prototype.loadDeferredRow = function(row) {
      return __awaiter(this, void 0, void 0, function() {
        var env, deferApi, response, e_1;
        return __generator(this, function(_a) {
          switch (_a.label) {
            case 0:
              env = this.props.env;
              deferApi = row.data.deferApi || this.props.deferApi;
              if (!isEffectiveApi(deferApi)) {
                throw new Error("deferApi is required");
              }
              _a.label = 1;
            case 1:
              _a.trys.push([1, 3, 4, 5]);
              row.markLoading(true);
              return [4, env.fetcher(deferApi, row.locals)];
            case 2:
              response = _a.sent();
              if (!response.ok) {
                throw new Error(response.msg);
              }
              row.updateData(response.data);
              row.markLoaded(true);
              row.setError("");
              return [3, 5];
            case 3:
              e_1 = _a.sent();
              row.setError(e_1.message);
              env.notify("error", e_1.message);
              return [3, 5];
            case 4:
              row.markLoading(false);
              return [
                7
                /*endfinally*/
              ];
            case 5:
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    Table2.prototype.initAutoFillHeight = function() {
      var props = this.props;
      var currentNode = this.dom.current;
      if (props.autoFillHeight) {
        this.autoFillHeightDispose = resizeSensor(currentNode.parentElement, this.updateAutoFillHeightLazy, false, "height");
        this.toDispose.push(this.autoFillHeightDispose);
        this.updateAutoFillHeight();
      }
    };
    Table2.prototype.updateAutoFillHeight = function() {
      var _this = this;
      var _a = this.props, autoFillHeight = _a.autoFillHeight, footerToolbar = _a.footerToolbar, ns = _a.classPrefix;
      if (!autoFillHeight) {
        return;
      }
      var table = this.table;
      var tableContent = table.parentElement;
      if (!tableContent) {
        return;
      }
      if (!tableContent.offsetHeight || tableContent.getBoundingClientRect().height / tableContent.offsetHeight < 0.8) {
        this.timer = setTimeout(function() {
          _this.updateAutoFillHeight();
        }, 100);
        return;
      }
      var viewportHeight = window.innerHeight;
      var tableContentTop = offset(tableContent).top;
      var parent = getScrollParent(tableContent.parentElement);
      if (parent && parent !== document.body) {
        viewportHeight = parent.clientHeight - 1;
        tableContentTop = position(tableContent, parent).top;
      }
      var tableContentBottom = 0;
      var selfNode = tableContent;
      var parentNode = selfNode.parentElement;
      while (parentNode) {
        var paddingBottom = getStyleNumber(parentNode, "padding-bottom");
        var borderBottom = getStyleNumber(parentNode, "border-bottom-width");
        var nextSiblingHeight = 0;
        var nextSibling = selfNode.nextElementSibling;
        while (nextSibling) {
          var positon = getComputedStyle(nextSibling).position;
          if (positon !== "absolute" && positon !== "fixed") {
            var rect1 = selfNode.getBoundingClientRect();
            var rect2 = nextSibling.getBoundingClientRect();
            if (rect1.bottom <= rect2.top) {
              nextSiblingHeight += nextSibling.offsetHeight + getStyleNumber(nextSibling, "margin-bottom");
            }
          }
          nextSibling = nextSibling.nextElementSibling;
        }
        var marginBottom = getStyleNumber(selfNode, "margin-bottom");
        tableContentBottom += paddingBottom + borderBottom + marginBottom + nextSiblingHeight;
        selfNode = parentNode;
        parentNode = selfNode.parentElement;
        if (parent && parent !== document.body && parent === selfNode) {
          break;
        }
      }
      var heightField = autoFillHeight && autoFillHeight.maxHeight ? "maxHeight" : "height";
      var heightValue = isObject(autoFillHeight) ? autoFillHeight[heightField] : 0;
      var tableContentHeight = heightValue ? "".concat(heightValue, "px") : "".concat(Math.round(viewportHeight - tableContentTop - tableContentBottom - 1), "px");
      tableContent.style[heightField] = tableContentHeight;
      tableContent.style.setProperty("--Table-content-".concat(heightField), tableContentHeight);
    };
    Table2.prototype.componentDidUpdate = function(prevProps) {
      var _a;
      var props = this.props;
      var store = props.store;
      changedEffect([
        "selectable",
        "columnsTogglable",
        "draggable",
        "orderBy",
        "orderDir",
        "multiple",
        "footable",
        "primaryField",
        "itemCheckableOn",
        "itemDraggableOn",
        "hideCheckToggler",
        "combineNum",
        "combineFromIndex",
        "expandConfig",
        "columns",
        "loading",
        "canAccessSuperData",
        "lazyRenderAfter",
        "tableLayout",
        "showIndex"
      ], prevProps, props, function(changes) {
        if (changes.hasOwnProperty("combineNum") && typeof changes.combineNum === "string") {
          changes.combineNum = parseInt(resolveVariableAndFilter(changes.combineNum, props.data, "| raw"), 10);
        }
        if (changes.orderBy && !props.onQuery) {
          delete changes.orderBy;
        }
        store.update(changes, {
          resolveDefinitions: props.resolveDefinitions
        });
      });
      if (anyChanged(["source", "value", "items"], prevProps, props) || !props.value && !props.items && (props.data !== prevProps.data || typeof props.source === "string" && isPureVariable(props.source))) {
        Table2.syncRows(store, props, prevProps) && this.syncSelected();
      } else if (isArrayChildrenModified(prevProps.selected, props.selected)) {
        var prevSelectedRows = store.selectedRows.map(function(item) {
          return item.id;
        }).join(",");
        store.updateSelected(props.selected || [], props.valueField);
        if (Array.isArray(props.selected) && Array.isArray(prevProps.selected) && props.selected.length === prevProps.selected.length) {
          var selectedRows = store.selectedRows.map(function(item) {
            return item.id;
          }).join(",");
          prevSelectedRows !== selectedRows && this.syncSelected();
        } else {
          this.syncSelected();
        }
      }
      if (props.autoFillHeight !== prevProps.autoFillHeight) {
        if (this.autoFillHeightDispose) {
          var idx = this.toDispose.indexOf(this.autoFillHeightDispose);
          if (idx !== -1) {
            this.toDispose.splice(idx, 1);
          }
          this.autoFillHeightDispose();
          delete this.autoFillHeightDispose;
          var tableContent = (_a = this.table) === null || _a === void 0 ? void 0 : _a.parentElement;
          if (tableContent) {
            tableContent.style.height = "";
          }
        }
        this.initAutoFillHeight();
      }
    };
    Table2.prototype.componentWillUnmount = function() {
      var formItem = this.props.formItem;
      this.toDispose.forEach(function(fn) {
        return fn();
      });
      this.toDispose = [];
      delete this.autoFillHeightDispose;
      this.updateTableInfoLazy.cancel();
      this.updateAutoFillHeightLazy.cancel();
      formItem && isAlive(formItem) && formItem.setSubStore(null);
      clearTimeout(this.timer);
      var scoped = this.context;
      scoped.unRegisterComponent(this);
    };
    Table2.prototype.scrollToTop = function() {
      var _a, _b;
      (_a = this.dom.current) === null || _a === void 0 ? void 0 : _a.scrollIntoView();
      if (this.props.autoFillHeight) {
        (_b = this.table) === null || _b === void 0 ? void 0 : _b.scrollIntoView();
      }
      var scrolledY = window.scrollY;
      scrolledY && window.scroll(0, scrolledY);
    };
    Table2.prototype.subFormRef = function(form, x, y) {
      var quickEditFormRef = this.props.quickEditFormRef;
      quickEditFormRef && quickEditFormRef(form, x, y);
      this.subForms["".concat(x, "-").concat(y)] = form;
      form && this.props.store.addForm(form.props.store, y);
    };
    Table2.prototype.handleAction = function(e, action, ctx) {
      var onAction = this.props.onAction;
      return onAction(e, action, ctx);
    };
    Table2.prototype.handleCheck = function(item, value, shift) {
      return __awaiter(this, void 0, void 0, function() {
        var _a, store, data, dispatchEvent, selectable;
        return __generator(this, function(_b) {
          switch (_b.label) {
            case 0:
              _a = this.props, store = _a.store, data = _a.data, dispatchEvent = _a.dispatchEvent, selectable = _a.selectable;
              if (!selectable) {
                return [
                  2
                  /*return*/
                ];
              }
              value = value !== void 0 ? value : !item.checked;
              if (shift) {
                store.toggleShift(item, value);
              } else {
                item.toggle(value);
              }
              this.syncSelected();
              return [4, dispatchEvent("selectedChange", createObject(data, __assign(__assign({}, store.eventContext), { item: item.data })))];
            case 1:
              _b.sent();
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    Table2.prototype.handleRowClick = function(item, index) {
      var _a, _b;
      var _c = this.props, dispatchEvent = _c.dispatchEvent, filterItemIndex = _c.filterItemIndex, store = _c.store, data = _c.data;
      return dispatchEvent("rowClick", createObject(data, {
        rowItem: item.data,
        item: item.data,
        index: parseInt("".concat((_a = filterItemIndex === null || filterItemIndex === void 0 ? void 0 : filterItemIndex(item.index, item)) !== null && _a !== void 0 ? _a : item.index), 10),
        indexPath: (_b = filterItemIndex === null || filterItemIndex === void 0 ? void 0 : filterItemIndex(item.path, item)) !== null && _b !== void 0 ? _b : item.path
      }));
    };
    Table2.prototype.handleRowDbClick = function(item, index) {
      var _a, _b;
      var _c = this.props, dispatchEvent = _c.dispatchEvent, filterItemIndex = _c.filterItemIndex, store = _c.store, data = _c.data;
      return dispatchEvent("rowDbClick", createObject(data, {
        item: item.data,
        index: parseInt("".concat((_a = filterItemIndex === null || filterItemIndex === void 0 ? void 0 : filterItemIndex(item.index, item)) !== null && _a !== void 0 ? _a : item.index), 10),
        indexPath: (_b = filterItemIndex === null || filterItemIndex === void 0 ? void 0 : filterItemIndex(item.path, item)) !== null && _b !== void 0 ? _b : item.path
      }));
    };
    Table2.prototype.handleRowMouseEnter = function(item, index) {
      var _a, _b;
      var _c = this.props, dispatchEvent = _c.dispatchEvent, filterItemIndex = _c.filterItemIndex, store = _c.store, data = _c.data;
      return dispatchEvent("rowMouseEnter", createObject(data, {
        item: item.data,
        index: parseInt("".concat((_a = filterItemIndex === null || filterItemIndex === void 0 ? void 0 : filterItemIndex(item.index, item)) !== null && _a !== void 0 ? _a : item.index), 10),
        indexPath: (_b = filterItemIndex === null || filterItemIndex === void 0 ? void 0 : filterItemIndex(item.path, item)) !== null && _b !== void 0 ? _b : item.path
      }));
    };
    Table2.prototype.handleRowMouseLeave = function(item, index) {
      var _a, _b;
      var _c = this.props, dispatchEvent = _c.dispatchEvent, filterItemIndex = _c.filterItemIndex, store = _c.store, data = _c.data;
      return dispatchEvent("rowMouseLeave", createObject(data, {
        item: item.data,
        index: parseInt("".concat((_a = filterItemIndex === null || filterItemIndex === void 0 ? void 0 : filterItemIndex(item.index, item)) !== null && _a !== void 0 ? _a : item.index), 10),
        indexPath: (_b = filterItemIndex === null || filterItemIndex === void 0 ? void 0 : filterItemIndex(item.path, item)) !== null && _b !== void 0 ? _b : item.path
      }));
    };
    Table2.prototype.handleCheckAll = function() {
      return __awaiter(this, void 0, void 0, function() {
        var _a, store, data, dispatchEvent;
        return __generator(this, function(_b) {
          switch (_b.label) {
            case 0:
              _a = this.props, store = _a.store, data = _a.data, dispatchEvent = _a.dispatchEvent;
              store.toggleAll();
              this.syncSelected();
              return [4, dispatchEvent("selectedChange", createObject(data, __assign({}, store.eventContext)))];
            case 1:
              _b.sent();
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    Table2.prototype.handleQuickChange = function(item, values, saveImmediately, savePristine, options) {
      if (!isAlive(item)) {
        return;
      }
      var _a = this.props, onSave = _a.onSave, onPristineChange = _a.onPristineChange, propsSaveImmediately = _a.saveImmediately, primaryField = _a.primaryField, onItemChange = _a.onItemChange;
      item.change(values, savePristine);
      item.modified && this.syncSelected();
      if (savePristine) {
        onPristineChange === null || onPristineChange === void 0 ? void 0 : onPristineChange(item.data, item.path);
        return;
      }
      onItemChange === null || onItemChange === void 0 ? void 0 : onItemChange(item.data, difference(item.data, item.pristine, ["id", primaryField]), item.path);
      if (!saveImmediately && !propsSaveImmediately) {
        return;
      } else if (saveImmediately && saveImmediately.api) {
        this.props.onAction(null, {
          actionType: "ajax",
          api: saveImmediately.api,
          reload: options === null || options === void 0 ? void 0 : options.reload
        }, item.locals);
        return;
      }
      if (!onSave) {
        return;
      }
      onSave(item.data, difference(item.data, item.pristine, ["id", primaryField]), item.path, void 0, item.pristine, options);
    };
    Table2.prototype.handleSave = function() {
      return __awaiter(this, void 0, void 0, function() {
        var _a, store, onSave, primaryField, subForms, result, subFormItems, result, rows, rowIndexes, diff, unModifiedRows;
        var _this = this;
        return __generator(this, function(_b) {
          switch (_b.label) {
            case 0:
              _a = this.props, store = _a.store, onSave = _a.onSave, primaryField = _a.primaryField;
              if (!onSave || !store.modifiedRows.length) {
                return [
                  2
                  /*return*/
                ];
              }
              subForms = [];
              Object.keys(this.subForms).forEach(function(key) {
                return _this.subForms[key] && subForms.push(_this.subForms[key]);
              });
              if (!subForms.length)
                return [3, 2];
              return [4, Promise.all(subForms.map(function(item) {
                return item.validate();
              }))];
            case 1:
              result = _b.sent();
              if (~result.indexOf(false)) {
                return [
                  2
                  /*return*/
                ];
              }
              _b.label = 2;
            case 2:
              subFormItems = store.children.filter(function(item) {
                return (item === null || item === void 0 ? void 0 : item.storeType) === "FormItemStore";
              });
              if (!subFormItems.length)
                return [3, 4];
              return [4, Promise.all(subFormItems.map(function(item) {
                var ctx = {};
                if (item.rowIndex && store.rows[item.rowIndex]) {
                  ctx = store.rows[item.rowIndex].data;
                }
                return item.validate(ctx);
              }))];
            case 3:
              result = _b.sent();
              if (~result.indexOf(false)) {
                return [
                  2
                  /*return*/
                ];
              }
              _b.label = 4;
            case 4:
              rows = store.modifiedRows.map(function(item) {
                return item.data;
              });
              rowIndexes = store.modifiedRows.map(function(item) {
                return item.path;
              });
              diff = store.modifiedRows.map(function(item) {
                return difference(item.data, item.pristine, ["id", primaryField]);
              });
              unModifiedRows = store.rows.filter(function(item) {
                return !item.modified;
              }).map(function(item) {
                return item.data;
              });
              return [2, onSave(rows, diff, rowIndexes, unModifiedRows, store.modifiedRows.map(function(item) {
                return item.pristine;
              }))];
          }
        });
      });
    };
    Table2.prototype.handleSaveOrder = function() {
      return __awaiter(this, void 0, void 0, function() {
        var _a, store, onSaveOrder, data, dispatchEvent, movedItems, items, rendererEvent;
        return __generator(this, function(_b) {
          switch (_b.label) {
            case 0:
              _a = this.props, store = _a.store, onSaveOrder = _a.onSaveOrder, data = _a.data, dispatchEvent = _a.dispatchEvent;
              movedItems = store.movedRows.map(function(item) {
                return item.data;
              });
              items = store.rows.map(function(item) {
                return item.getDataWithModifiedChilden();
              });
              return [4, dispatchEvent("orderChange", createObject(data, { movedItems }))];
            case 1:
              rendererEvent = _b.sent();
              if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                return [
                  2
                  /*return*/
                ];
              }
              if (!onSaveOrder || !store.movedRows.length) {
                return [
                  2
                  /*return*/
                ];
              }
              onSaveOrder(movedItems, items);
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    Table2.prototype.syncSelected = function() {
      var _a = this.props, store = _a.store, onSelect = _a.onSelect;
      onSelect && onSelect(store.selectedRows.map(function(item) {
        return item.data;
      }), store.unSelectedRows.map(function(item) {
        return item.data;
      }));
    };
    Table2.prototype.reset = function() {
      var _this = this;
      var store = this.props.store;
      store.reset();
      var subForms = [];
      Object.keys(this.subForms).forEach(function(key) {
        return _this.subForms[key] && subForms.push(_this.subForms[key]);
      });
      subForms.forEach(function(item) {
        return item.clearErrors();
      });
      var subFormItems = store.children.filter(function(item) {
        return (item === null || item === void 0 ? void 0 : item.storeType) === "FormItemStore";
      });
      if (subFormItems.length) {
        subFormItems.map(function(item) {
          return item.reset();
        });
      }
    };
    Table2.prototype.bulkUpdate = function(value, items) {
      var _a = this.props, store = _a.store, primaryField = _a.primaryField;
      if (primaryField && value.ids) {
        var ids_1 = value.ids.split(",");
        var rows = store.rows.filter(function(item) {
          return (0, import_find.default)(ids_1, function(id) {
            return id && id == item.data[primaryField];
          });
        });
        var newValue_1 = __assign(__assign({}, value), { ids: void 0 });
        rows.forEach(function(row) {
          return row.change(newValue_1);
        });
      } else if (Array.isArray(items)) {
        var rows = store.rows.filter(function(item) {
          return ~items.indexOf(item.pristine);
        });
        rows.forEach(function(row) {
          return row.change(value);
        });
      }
    };
    Table2.prototype.getSelected = function() {
      var store = this.props.store;
      return store.selectedRows.map(function(item) {
        return item.data;
      });
    };
    Table2.prototype.updateTableInfo = function(callback) {
      if (this.resizeLine) {
        return;
      }
      this.props.store.initTableWidth();
      this.props.store.syncTableWidth();
      this.handleOutterScroll();
      callback && setTimeout(callback, 20);
    };
    Table2.prototype.handleOutterScroll = function() {
      var table = this.table;
      if (!table) {
        return;
      }
      var outter = table === null || table === void 0 ? void 0 : table.parentNode;
      var scrollLeft = outter.scrollLeft;
      if (this.affixedTable) {
        this.affixedTable.parentElement.scrollLeft = scrollLeft;
      }
      if (this.props.store.filteredColumns.some(function(column) {
        return column.fixed;
      })) {
        var leading_1 = scrollLeft === 0;
        var trailing_1 = Math.ceil(scrollLeft) + outter.offsetWidth >= table.scrollWidth;
        [table, this.affixedTable].filter(function(item) {
          return item;
        }).forEach(function(table2) {
          table2.classList.remove("table-fixed-left", "table-fixed-right");
          leading_1 || table2.classList.add("table-fixed-left");
          trailing_1 || table2.classList.add("table-fixed-right");
        });
      }
    };
    Table2.prototype.tableRef = function(ref) {
      var _this = this;
      var _a;
      this.table = ref;
      isAlive(this.props.store) && this.props.store.setTable(ref);
      (_a = this.tableUnWatchResize) === null || _a === void 0 ? void 0 : _a.call(this);
      if (ref) {
        this.handleOutterScroll();
        this.tableUnWatchResize = resizeSensor(ref, function() {
          _this.handleOutterScroll();
        });
      }
    };
    Table2.prototype.dragTipRef = function(ref) {
      if (!this.dragTip && ref) {
        this.initDragging();
      } else if (this.dragTip && !ref) {
        this.destroyDragging();
      }
      this.dragTip = ref;
    };
    Table2.prototype.affixedTableRef = function(ref) {
      this.affixedTable = ref;
      ref && this.handleOutterScroll();
    };
    Table2.prototype.initDragging = function() {
      var _this = this;
      var _a = this.props, store = _a.store, ns = _a.classPrefix;
      this.sortable = new sortable_esm_default(this.table.querySelector(":scope>tbody"), {
        group: "table",
        animation: 150,
        handle: ".".concat(ns, "Table-dragCell"),
        filter: ".".concat(ns, "Table-dragCell.is-dragDisabled"),
        ghostClass: "is-dragging",
        onEnd: function(e) {
          return __awaiter(_this, void 0, void 0, function() {
            var parent;
            return __generator(this, function(_a2) {
              if (e.newIndex === e.oldIndex) {
                return [
                  2
                  /*return*/
                ];
              }
              parent = e.to;
              if (e.oldIndex < parent.childNodes.length - 1) {
                parent.insertBefore(e.item, parent.childNodes[e.oldIndex > e.newIndex ? e.oldIndex + 1 : e.oldIndex]);
              } else {
                parent.appendChild(e.item);
              }
              store.exchange(e.oldIndex, e.newIndex);
              return [
                2
                /*return*/
              ];
            });
          });
        }
      });
    };
    Table2.prototype.destroyDragging = function() {
      this.sortable && this.sortable.destroy();
    };
    Table2.prototype.getPopOverContainer = function() {
      return this.dom.current;
    };
    Table2.prototype.handleMouseMove = function(e) {
      var tr = e.target.closest("tr[data-id]");
      if (!tr) {
        return;
      }
      var _a = this.props, store = _a.store, affixColumns = _a.affixColumns, itemActions = _a.itemActions;
      var id = tr.getAttribute("data-id");
      var row = store.hoverRow;
      if ((row === null || row === void 0 ? void 0 : row.id) === id) {
        return;
      }
      eachTree(store.rows, function(item) {
        return item.setIsHover(item.id === id);
      });
    };
    Table2.prototype.handleMouseLeave = function() {
      var store = this.props.store;
      var row = store.hoverRow;
      row === null || row === void 0 ? void 0 : row.setIsHover(false);
    };
    Table2.prototype.handleDragStart = function(e) {
      var store = this.props.store;
      var target = e.currentTarget;
      var tr = this.draggingTr = target.closest("tr");
      var id = tr.getAttribute("data-id");
      var tbody = tr.parentNode;
      this.originIndex = Array.prototype.indexOf.call(tbody.childNodes, tr);
      tbody.classList.add("is-dragging");
      tr.classList.add("is-dragging");
      e.dataTransfer.effectAllowed = "move";
      e.dataTransfer.setData("text/plain", id);
      e.dataTransfer.setDragImage(tr, 0, 0);
      var item = store.getRowById(id);
      store.collapseAllAtDepth(item.depth);
      var siblings = store.rows;
      if (item.parentId) {
        var parent_1 = store.getRowById(item.parentId);
        siblings = parent_1.children;
      }
      siblings = siblings.filter(function(sibling) {
        return sibling !== item;
      });
      tbody.addEventListener("dragover", this.handleDragOver);
      tbody.addEventListener("drop", this.handleDrop);
      this.draggingSibling = siblings.map(function(item2) {
        var tr2 = tbody.querySelector(':scope>tr[data-id="'.concat(item2.id, '"]'));
        tr2.classList.add("is-drop-allowed");
        return tr2;
      });
      tr.addEventListener("dragend", this.handleDragEnd);
    };
    Table2.prototype.handleDragOver = function(e) {
      if (!e.target) {
        return;
      }
      e.preventDefault();
      e.dataTransfer.dropEffect = "move";
      var overTr = e.target.closest("tr");
      if (!overTr || !~overTr.className.indexOf("is-drop-allowed") || overTr === this.draggingTr || Animation.animating) {
        return;
      }
      var tbody = overTr.parentElement;
      var tRect = overTr.getBoundingClientRect();
      var next = (e.clientY - tRect.top) / (tRect.bottom - tRect.top) > 0.5;
      Animation.capture(tbody);
      var before = next ? overTr.nextSibling : overTr;
      before ? tbody.insertBefore(this.draggingTr, before) : tbody.appendChild(this.draggingTr);
      Animation.animateAll();
    };
    Table2.prototype.handleDrop = function() {
      return __awaiter(this, void 0, void 0, function() {
        var store, tr, tbody, index, item;
        return __generator(this, function(_a) {
          store = this.props.store;
          tr = this.draggingTr;
          tbody = tr.parentElement;
          index = Array.prototype.indexOf.call(tbody.childNodes, tr);
          item = store.getRowById(tr.getAttribute("data-id"));
          this.handleDragEnd();
          store.exchange(this.originIndex, index, item);
          return [
            2
            /*return*/
          ];
        });
      });
    };
    Table2.prototype.handleDragEnd = function() {
      var tr = this.draggingTr;
      var tbody = tr.parentElement;
      var index = Array.prototype.indexOf.call(tbody.childNodes, tr);
      tbody.insertBefore(tr, tbody.childNodes[index < this.originIndex ? this.originIndex + 1 : this.originIndex]);
      tr.classList.remove("is-dragging");
      tbody.classList.remove("is-dragging");
      tr.removeEventListener("dragend", this.handleDragEnd);
      tbody.removeEventListener("dragover", this.handleDragOver);
      tbody.removeEventListener("drop", this.handleDrop);
      this.draggingSibling.forEach(function(item) {
        return item.classList.remove("is-drop-allowed");
      });
    };
    Table2.prototype.handleImageEnlarge = function(info, target) {
      var onImageEnlarge = this.props.onImageEnlarge;
      if (Array.isArray(info.list) && info.enlargeWithGallary !== true || info.enlargeWithGallary === false) {
        return onImageEnlarge && onImageEnlarge(info, target);
      }
      var store = this.props.store;
      var column = store.columns[target.colIndex].pristine;
      var index = target.rowIndex;
      var list = [];
      store.rows.forEach(function(row, i) {
        var src = resolveVariable(column.name, row.data);
        if (!src) {
          if (i < target.rowIndex) {
            index--;
          }
          return;
        }
        var images = Array.isArray(src) ? src : [src];
        list = list.concat(images.map(function(item) {
          return {
            src: item,
            originalSrc: column.originalSrc ? filter(column.originalSrc, row.data) : item,
            title: column.enlargeTitle ? filter(column.enlargeTitle, row.data) : column.title ? filter(column.title, row.data) : void 0,
            caption: column.enlargeCaption ? filter(column.enlargeCaption, row.data) : column.caption ? filter(column.caption, row.data) : void 0
          };
        }));
      });
      if (list.length > 1) {
        onImageEnlarge && onImageEnlarge(__assign(__assign({}, info), { list, index }), target);
      } else {
        onImageEnlarge && onImageEnlarge(info, target);
      }
    };
    Table2.prototype.handleColResizeMouseDown = function(e) {
      this.lineStartX = e.clientX;
      var currentTarget = e.currentTarget;
      this.resizeLine = currentTarget;
      var store = this.props.store;
      var index = parseInt(this.resizeLine.getAttribute("data-index"), 10);
      var column = store.columns[index];
      this.lineStartWidth = column.realWidth || column.width;
      this.resizeLine.classList.add("is-resizing");
      document.addEventListener("mousemove", this.handleColResizeMouseMove);
      document.addEventListener("mouseup", this.handleColResizeMouseUp);
      e.preventDefault();
      e.stopPropagation();
    };
    Table2.prototype.handleColResizeMouseMove = function(e) {
      var moveX = e.clientX - this.lineStartX;
      var store = this.props.store;
      var index = parseInt(this.resizeLine.getAttribute("data-index"), 10);
      var column = store.columns[index];
      column.setWidth(Math.max(this.lineStartWidth + moveX, 30, column.minWidth));
    };
    Table2.prototype.handleColResizeMouseUp = function(e) {
      this.resizeLine.classList.remove("is-resizing");
      delete this.resizeLine;
      document.removeEventListener("mousemove", this.handleColResizeMouseMove);
      document.removeEventListener("mouseup", this.handleColResizeMouseUp);
    };
    Table2.prototype.handleColumnToggle = function(columns) {
      var store = this.props.store;
      store.updateColumns(columns);
      store.persistSaveToggledColumns();
    };
    Table2.prototype.renderAutoFilterForm = function() {
      var _a = this.props, render = _a.render, store = _a.store, onSearchableFromReset = _a.onSearchableFromReset, onSearchableFromSubmit = _a.onSearchableFromSubmit, onSearchableFromInit = _a.onSearchableFromInit, cx = _a.classnames, __ = _a.translate, query = _a.query, data = _a.data, autoGenerateFilter = _a.autoGenerateFilter, testIdBuilder = _a.testIdBuilder, _b = _a.filterCanAccessSuperData, filterCanAccessSuperData = _b === void 0 ? true : _b;
      var searchableColumns = store.searchableColumns;
      if (!searchableColumns.length) {
        return null;
      }
      return import_react22.default.createElement(AutoFilterForm$1, { store, query, data, translate: __, classnames: cx, render, canAccessSuperData: filterCanAccessSuperData, autoGenerateFilter, onSearchableFromReset, onSearchableFromSubmit, onSearchableFromInit, popOverContainer: this.getPopOverContainer, testIdBuilder: testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild("filter") });
    };
    Table2.prototype.renderHeading = function() {
      var _a = this.props, title = _a.title, store = _a.store, hideQuickSaveBtn = _a.hideQuickSaveBtn, data = _a.data, cx = _a.classnames, saveImmediately = _a.saveImmediately, headingClassName = _a.headingClassName, quickSaveApi = _a.quickSaveApi, __ = _a.translate, columns = _a.columns;
      var isModifiedColumnSaveImmediately = false;
      if (store.modifiedRows.length === 1) {
        var saveImmediatelyColumnNames = (columns === null || columns === void 0 ? void 0 : columns.map(function(column) {
          var _a2;
          return ((_a2 = column === null || column === void 0 ? void 0 : column.quickEdit) === null || _a2 === void 0 ? void 0 : _a2.saveImmediately) ? column === null || column === void 0 ? void 0 : column.name : "";
        }).filter(function(a) {
          return a;
        })) || [];
        var item = store.modifiedRows[0];
        var diff = difference(item.data, item.pristine);
        if ((0, import_intersection.default)(saveImmediatelyColumnNames, Object.keys(diff)).length) {
          isModifiedColumnSaveImmediately = true;
        }
      }
      if (title || quickSaveApi && !saveImmediately && !isModifiedColumnSaveImmediately && store.modified && !hideQuickSaveBtn || store.moved) {
        return import_react22.default.createElement("div", { className: cx("Table-heading", headingClassName), key: "heading" }, !saveImmediately && store.modified && !hideQuickSaveBtn && !isModifiedColumnSaveImmediately ? import_react22.default.createElement(
          "span",
          null,
          __("Table.modified", {
            modified: store.modified
          }),
          import_react22.default.createElement(
            "button",
            { type: "button", className: cx("Button Button--size-xs Button--success m-l-sm"), onClick: this.handleSave },
            import_react22.default.createElement(Icon, { icon: "check", className: "icon m-r-xs" }),
            __("Form.submit")
          ),
          import_react22.default.createElement(
            "button",
            { type: "button", className: cx("Button Button--size-xs Button--danger m-l-sm"), onClick: this.reset },
            import_react22.default.createElement(Icon, { icon: "close", className: "icon m-r-xs" }),
            __("Table.discard")
          )
        ) : store.moved ? import_react22.default.createElement(
          "span",
          null,
          __("Table.moved", {
            moved: store.moved
          }),
          import_react22.default.createElement(
            "button",
            { type: "button", className: cx("Button Button--xs Button--success m-l-sm"), onClick: this.handleSaveOrder },
            import_react22.default.createElement(Icon, { icon: "check", className: "icon m-r-xs" }),
            __("Form.submit")
          ),
          import_react22.default.createElement(
            "button",
            { type: "button", className: cx("Button Button--xs Button--danger m-l-sm"), onClick: this.reset },
            import_react22.default.createElement(Icon, { icon: "close", className: "icon m-r-xs" }),
            __("Table.discard")
          )
        ) : title ? filter(title, data) : "");
      }
      return null;
    };
    Table2.prototype.renderHeadCell = function(column, props) {
      var _this = this;
      var _a, _b;
      var _c = this.props, store = _c.store, query = _c.query, onQuery = _c.onQuery, render = _c.render, ns = _c.classPrefix, resizable = _c.resizable, cx = _c.classnames, autoGenerateFilter = _c.autoGenerateFilter, dispatchEvent = _c.dispatchEvent, data = _c.data, testIdBuilder = _c.testIdBuilder, __ = _c.translate;
      var tIdCell = testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild("head-cell-".concat(column.name));
      var style = __assign({}, props.style);
      var _d = __read(store.getStickyStyles(column, store.filteredColumns), 2), stickyStyle = _d[0], stickyClassName = _d[1];
      Object.assign(style, stickyStyle);
      var resizeLine = import_react22.default.createElement("div", { className: cx("Table-content-colDragLine"), key: "resize-".concat(column.id), "data-index": column.index, onMouseDown: this.handleColResizeMouseDown });
      if (style === null || style === void 0 ? void 0 : style.width) {
        delete style.width;
      }
      if (column.pristine.headerAlign) {
        style.textAlign = column.pristine.headerAlign;
      } else if (column.pristine.align) {
        style.textAlign = column.pristine.align;
      }
      var key = props.key, restProps = __rest(props, ["key"]);
      if (column.type === "__checkme") {
        return import_react22.default.createElement(
          "th",
          __assign({}, restProps, { key, style, className: cx(column.pristine.className, stickyClassName) }),
          store.rows.length && store.multiple ? import_react22.default.createElement(Checkbox$1, { classPrefix: ns, partial: store.someChecked && !store.allChecked, checked: store.someChecked, disabled: store.isSelectionThresholdReached && !store.someChecked, onChange: this.handleCheckAll }) : " ",
          resizable === false ? null : resizeLine
        );
      } else if (column.type === "__dragme") {
        return import_react22.default.createElement("th", __assign({}, restProps, { key, style, className: cx(column.pristine.className, stickyClassName) }));
      } else if (column.type === "__expandme") {
        return import_react22.default.createElement(
          "th",
          __assign({}, restProps, { key, style, className: cx(column.pristine.className, stickyClassName) }),
          store.footable && (store.footable.expandAll === false || store.footable.accordion) || store.expandConfig && (store.expandConfig.expandAll === false || store.expandConfig.accordion) ? null : import_react22.default.createElement(
            "a",
            {
              className: cx("Table-expandBtn", store.allExpanded ? "is-active" : ""),
              // data-tooltip="展开/收起全部"
              // data-position="top"
              onClick: store.toggleExpandAll
            },
            import_react22.default.createElement(Icon, { icon: "right-arrow-bold", className: "icon" })
          ),
          resizable === false ? null : resizeLine
        );
      } else if (column.type === "__index") {
        return import_react22.default.createElement(
          "th",
          __assign({}, restProps, { key, style, className: cx(column.pristine.className, stickyClassName) }),
          __("Table.index"),
          resizable === false ? null : resizeLine
        );
      }
      var prefix = [];
      var affix = [];
      if (column.isPrimary && store.isNested) {
        store.footable && (store.footable.expandAll === false || store.footable.accordion) || store.expandConfig && (store.expandConfig.expandAll === false || store.expandConfig.accordion) || prefix.push(import_react22.default.createElement(
          "a",
          {
            key: "expandBtn",
            className: cx("Table-expandBtn2", store.allExpanded ? "is-active" : ""),
            // data-tooltip="展开/收起全部"
            // data-position="top"
            onClick: store.toggleExpandAll
          },
          import_react22.default.createElement(Icon, { icon: "right-arrow-bold", className: "icon" })
        ));
      }
      if (column.searchable && column.name && !autoGenerateFilter) {
        affix.push(import_react22.default.createElement(HeadCellSearchDropDown, __assign({}, restProps, { key: "table-head-search" }, this.props, { onQuery, name: column.name, searchable: column.searchable, type: column.type, data: query, testIdBuilder: tIdCell === null || tIdCell === void 0 ? void 0 : tIdCell.getChild("search"), popOverContainer: this.getPopOverContainer })));
      }
      if (column.sortable && column.name) {
        affix.push(import_react22.default.createElement(
          "span",
          __assign({}, restProps, { key: "table-head-sort", className: cx("TableCell-sortBtn"), onClick: function() {
            return __awaiter(_this, void 0, void 0, function() {
              var orderBy, orderDir, order, rendererEvent;
              return __generator(this, function(_a2) {
                switch (_a2.label) {
                  case 0:
                    orderBy = "";
                    orderDir = "";
                    if (column.name === store.orderBy) {
                      if (store.orderDir !== "desc") {
                        orderBy = column.name;
                        orderDir = "desc";
                      }
                    } else {
                      orderBy = column.name;
                    }
                    order = orderBy ? orderDir ? "desc" : "asc" : "";
                    return [4, dispatchEvent("columnSort", createObject(data, {
                      orderBy,
                      orderDir: order
                    }))];
                  case 1:
                    rendererEvent = _a2.sent();
                    if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                      return [
                        2
                        /*return*/
                      ];
                    }
                    if (!onQuery || onQuery({
                      orderBy,
                      orderDir: order
                    }) === false) {
                      store.changeOrder(orderBy, order);
                    }
                    return [
                      2
                      /*return*/
                    ];
                }
              });
            });
          } }),
          import_react22.default.createElement(
            "i",
            { className: cx("TableCell-sortBtn--down", store.orderBy === column.name && store.orderDir === "desc" ? "is-active" : "") },
            import_react22.default.createElement(Icon, { icon: "sort-desc", className: "icon" })
          ),
          import_react22.default.createElement(
            "i",
            { className: cx("TableCell-sortBtn--up", store.orderBy === column.name && store.orderDir === "asc" ? "is-active" : "") },
            import_react22.default.createElement(Icon, { icon: "sort-asc", className: "icon" })
          ),
          import_react22.default.createElement(
            "i",
            { className: cx("TableCell-sortBtn--default", store.orderBy === column.name ? "" : "is-active") },
            import_react22.default.createElement(Icon, { icon: "sort-default", className: "icon" })
          )
        ));
      }
      if (!column.searchable && column.filterable && column.name && onQuery) {
        affix.push(import_react22.default.createElement(HeadCellFilterDropDown, __assign({ key: "table-head-filter" }, this.props, { onQuery, name: column.name, type: column.type, data: query, superData: createObject(data, query), filterable: column.filterable, popOverContainer: this.getPopOverContainer, testIdBuilder: tIdCell === null || tIdCell === void 0 ? void 0 : tIdCell.getChild("filter") })));
      }
      return import_react22.default.createElement(
        "th",
        __assign({}, restProps, { key, style, className: cx(props ? props.className : "", stickyClassName, {
          "TableCell--sortable": column.sortable,
          "TableCell--searchable": column.searchable,
          "TableCell--filterable": column.filterable,
          "Table-operationCell": column.type === "operation"
        }) }, tIdCell === null || tIdCell === void 0 ? void 0 : tIdCell.getTestId()),
        prefix,
        import_react22.default.createElement(
          "div",
          { key: "content", className: cx("TableCell--title", column.pristine.className, column.pristine.labelClassName), style: props.style },
          ((_a = props.label) !== null && _a !== void 0 ? _a : column.label) ? render("tpl", (_b = props.label) !== null && _b !== void 0 ? _b : column.label) : null,
          column.remark ? render("remark", {
            type: "remark",
            tooltip: column.remark,
            container: this.getPopOverContainer
          }) : null
        ),
        affix,
        resizable === false ? null : resizeLine
      );
    };
    Table2.prototype.renderCell = function(region, column, item, props, ignoreDrag) {
      if (ignoreDrag === void 0) {
        ignoreDrag = false;
      }
      var _a = this.props, render = _a.render, store = _a.store, ns = _a.classPrefix, cx = _a.classnames, canAccessSuperData = _a.canAccessSuperData, itemBadge = _a.itemBadge, translate = _a.translate, testIdBuilder = _a.testIdBuilder, filterItemIndex = _a.filterItemIndex;
      return import_react22.default.createElement(Cell, { key: props.key, region, column, item, props, ignoreDrag, render, filterItemIndex, store, multiple: store.multiple, canAccessSuperData, classnames: cx, classPrefix: ns, itemBadge, onCheck: this.handleCheck, onDragStart: this.handleDragStart, popOverContainer: this.getPopOverContainer, quickEditFormRef: this.subFormRef, onImageEnlarge: this.handleImageEnlarge, translate, testIdBuilder: testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getChild("cell-".concat(props.rowPath, "-").concat(column.index)) });
    };
    Table2.prototype.renderAffixHeader = function(tableClassName) {
      var _this = this;
      var _a = this.props, store = _a.store, affixHeader = _a.affixHeader, render = _a.render, cx = _a.classnames, autoFillHeight = _a.autoFillHeight, env = _a.env;
      var hideHeader = store.filteredColumns.every(function(column) {
        return !column.label;
      });
      var columnsGroup = store.columnGroup;
      return affixHeader && !autoFillHeight ? import_react22.default.createElement(
        import_react22.default.Fragment,
        null,
        import_react22.default.createElement(
          "div",
          { className: cx("Table-fixedTop", {
            "is-fakeHide": hideHeader
          }) },
          this.renderHeader(false),
          this.renderHeading(),
          store.columnWidthReady ? import_react22.default.createElement(
            "div",
            { className: cx("Table-wrapper") },
            import_react22.default.createElement(
              "table",
              { ref: this.affixedTableRef, className: cx(tableClassName, store.tableLayout === "fixed" ? "is-layout-fixed" : "") },
              import_react22.default.createElement("colgroup", null, store.filteredColumns.map(function(column) {
                var style = {
                  width: "var(--Table-column-".concat(column.index, "-width)")
                };
                if (store.tableLayout === "auto") {
                  style.minWidth = style.width;
                }
                return import_react22.default.createElement("col", { "data-index": column.index, style, key: column.id });
              })),
              import_react22.default.createElement(
                "thead",
                null,
                columnsGroup.length ? import_react22.default.createElement("tr", null, columnsGroup.map(function(item, index) {
                  var _a2 = __read(store.getStickyStyles(item, columnsGroup), 2), stickyStyle = _a2[0], stickyClassName = _a2[1];
                  return item.rowSpan === 1 ? (
                    // 如果是分组自己，则用 th 渲染
                    import_react22.default.createElement("th", { key: index, "data-index": item.index, colSpan: item.colSpan, rowSpan: item.rowSpan, style: stickyStyle, className: stickyClassName }, item.label ? render("tpl", item.label) : null)
                  ) : (
                    // 否则走 renderCell 因为不走的话，排序按钮不会渲染
                    _this.renderHeadCell(item.has[0], {
                      "label": item.label,
                      "key": index,
                      "data-index": item.index,
                      "colSpan": item.colSpan,
                      "rowSpan": item.rowSpan,
                      "style": stickyStyle,
                      "className": stickyClassName
                    })
                  );
                })) : null,
                import_react22.default.createElement("tr", null, store.filteredColumns.map(function(column) {
                  var _a2;
                  return ((_a2 = columnsGroup.find(function(group) {
                    return ~group.has.indexOf(column);
                  })) === null || _a2 === void 0 ? void 0 : _a2.rowSpan) === 2 ? null : _this.renderHeadCell(column, {
                    "key": column.index,
                    "data-index": column.index
                  });
                }))
              )
            )
          ) : null
        )
      ) : null;
    };
    Table2.prototype.renderToolbar = function(toolbar) {
      var type = toolbar.type || toolbar;
      if (type === "columns-toggler") {
        this.renderedToolbars.push(type);
        return this.renderColumnsToggler(toolbar);
      } else if (type === "drag-toggler") {
        this.renderedToolbars.push(type);
        return this.renderDragToggler();
      } else if (type === "export-excel") {
        this.renderedToolbars.push(type);
        return this.renderExportExcel(toolbar);
      } else if (type === "export-excel-template") {
        this.renderedToolbars.push(type);
        return this.renderExportExcelTemplate(toolbar);
      }
      return void 0;
    };
    Table2.prototype.renderColumnsToggler = function(config) {
      var _this = this;
      var _a;
      var _b = this.props, className = _b.className, store = _b.store, ns = _b.classPrefix, cx = _b.classnames, affixRow = _b.affixRow, rest = __rest(_b, ["className", "store", "classPrefix", "classnames", "affixRow"]);
      var __ = rest.translate;
      var env = rest.env;
      var render = this.props.render;
      if (!store.columnsTogglable) {
        return null;
      }
      return import_react22.default.createElement(
        ColumnToggler,
        __assign({}, rest, isObject(config) ? config : {}, { tooltip: {
          content: (config === null || config === void 0 ? void 0 : config.tooltip) || __("Table.columnsVisibility"),
          placement: "bottom"
        }, tooltipContainer: rest.popOverContainer || env.getModalContainer, align: (_a = config === null || config === void 0 ? void 0 : config.align) !== null && _a !== void 0 ? _a : "left", isActived: store.hasColumnHidden(), classnames: cx, classPrefix: ns, key: "columns-toggable", size: (config === null || config === void 0 ? void 0 : config.size) || "sm", icon: config === null || config === void 0 ? void 0 : config.icon, label: config === null || config === void 0 ? void 0 : config.label, draggable: config === null || config === void 0 ? void 0 : config.draggable, columns: store.columnsData, activeToggaleColumns: store.activeToggaleColumns, onColumnToggle: this.handleColumnToggle }),
        store.toggableColumns.length ? import_react22.default.createElement(
          "li",
          { className: cx("ColumnToggler-menuItem"), key: "selectAll", onClick: function() {
            return __awaiter(_this, void 0, void 0, function() {
              var _a2, data, dispatchEvent, allToggled, rendererEvent;
              return __generator(this, function(_b2) {
                switch (_b2.label) {
                  case 0:
                    _a2 = this.props, data = _a2.data, dispatchEvent = _a2.dispatchEvent;
                    allToggled = !(store.activeToggaleColumns.length === store.toggableColumns.length);
                    return [4, dispatchEvent("columnToggled", createObject(data, {
                      columns: allToggled ? store.toggableColumns.map(function(column) {
                        return column.pristine;
                      }) : []
                    }))];
                  case 1:
                    rendererEvent = _b2.sent();
                    if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                      return [
                        2
                        /*return*/
                      ];
                    }
                    store.toggleAllColumns();
                    return [
                      2
                      /*return*/
                    ];
                }
              });
            });
          } },
          import_react22.default.createElement(Checkbox$1, { size: "sm", classPrefix: ns, key: "checkall", checked: !!store.activeToggaleColumns.length, partial: !!(store.activeToggaleColumns.length && store.activeToggaleColumns.length !== store.toggableColumns.length) }, __("Select.checkAll"))
        ) : null,
        !(config === null || config === void 0 ? void 0 : config.draggable) && store.toggableColumns.map(function(column) {
          return import_react22.default.createElement(
            "li",
            { className: cx("ColumnToggler-menuItem"), key: column.index, onClick: function() {
              return __awaiter(_this, void 0, void 0, function() {
                var _a2, data, dispatchEvent, columns, rendererEvent;
                return __generator(this, function(_b2) {
                  switch (_b2.label) {
                    case 0:
                      _a2 = this.props, data = _a2.data, dispatchEvent = _a2.dispatchEvent;
                      columns = store.activeToggaleColumns.map(function(item) {
                        return item.pristine;
                      });
                      if (!column.toggled) {
                        columns.push(column.pristine);
                      } else {
                        columns = columns.filter(function(c) {
                          return c.name !== column.pristine.name;
                        });
                      }
                      return [4, dispatchEvent("columnToggled", createObject(data, {
                        columns
                      }))];
                    case 1:
                      rendererEvent = _b2.sent();
                      if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                        return [
                          2
                          /*return*/
                        ];
                      }
                      column.toggleToggle();
                      return [
                        2
                        /*return*/
                      ];
                  }
                });
              });
            } },
            import_react22.default.createElement(Checkbox$1, { size: "sm", classPrefix: ns, checked: column.toggled }, column.label ? render("tpl", column.label) : null)
          );
        })
      );
    };
    Table2.prototype.renderDragToggler = function() {
      var _a = this.props, store = _a.store, env = _a.env, draggable = _a.draggable, ns = _a.classPrefix, __ = _a.translate, popOverContainer = _a.popOverContainer;
      if (!draggable || store.isNested) {
        return null;
      }
      return import_react22.default.createElement(
        Button$1,
        { disabled: !!store.modified, classPrefix: ns, key: "dragging-toggle", tooltip: { content: __("Table.startSort"), placement: "bottom" }, tooltipContainer: popOverContainer || env.getModalContainer, size: "sm", active: store.dragging, onClick: function(e) {
          e.preventDefault();
          store.toggleDragging();
          store.dragging && store.clear();
        }, iconOnly: true },
        import_react22.default.createElement(Icon, { icon: "exchange", className: "icon" })
      );
    };
    Table2.prototype.renderExportExcel = function(toolbar) {
      var _this = this;
      var _a = this.props, store = _a.store, __ = _a.translate, render = _a.render;
      var columns = store.filteredColumns || [];
      if (!columns) {
        return null;
      }
      return render("exportExcel", __assign(__assign({ label: __("CRUD.exportExcel") }, toolbar), { type: "button" }), {
        loading: store.exportExcelLoading,
        onAction: function() {
          store.update({ exportExcelLoading: true });
          import("./exceljs.js").then(function(E) {
            return __awaiter(_this, void 0, void 0, function() {
              var ExcelJS, error_1;
              return __generator(this, function(_a2) {
                switch (_a2.label) {
                  case 0:
                    ExcelJS = E.default || E;
                    _a2.label = 1;
                  case 1:
                    _a2.trys.push([1, 3, 4, 5]);
                    return [4, exportExcel(ExcelJS, this.props, toolbar)];
                  case 2:
                    _a2.sent();
                    return [3, 5];
                  case 3:
                    error_1 = _a2.sent();
                    console.error(error_1);
                    return [3, 5];
                  case 4:
                    store.update({ exportExcelLoading: false });
                    return [
                      7
                      /*endfinally*/
                    ];
                  case 5:
                    return [
                      2
                      /*return*/
                    ];
                }
              });
            });
          });
        }
      });
    };
    Table2.prototype.renderExportExcelTemplate = function(toolbar) {
      var _this = this;
      var _a = this.props, store = _a.store, __ = _a.translate, render = _a.render;
      var columns = store.filteredColumns || [];
      if (!columns) {
        return null;
      }
      return render("exportExcelTemplate", __assign(__assign({ label: __("CRUD.exportExcelTemplate") }, toolbar), { type: "button" }), {
        onAction: function() {
          import("./exceljs.js").then(function(E) {
            return __awaiter(_this, void 0, void 0, function() {
              var ExcelJS, error_2;
              return __generator(this, function(_a2) {
                switch (_a2.label) {
                  case 0:
                    ExcelJS = E.default || E;
                    _a2.label = 1;
                  case 1:
                    _a2.trys.push([1, 3, , 4]);
                    return [4, exportExcel(ExcelJS, this.props, toolbar, true)];
                  case 2:
                    _a2.sent();
                    return [3, 4];
                  case 3:
                    error_2 = _a2.sent();
                    console.error(error_2);
                    return [3, 4];
                  case 4:
                    return [
                      2
                      /*return*/
                    ];
                }
              });
            });
          });
        }
      });
    };
    Table2.prototype.renderActions = function(region) {
      var _this = this;
      var _a = this.props, actions = _a.actions, render = _a.render, store = _a.store, cx = _a.classnames, data = _a.data;
      actions = Array.isArray(actions) ? actions.concat() : [];
      if (store.toggable && region === "header" && !~this.renderedToolbars.indexOf("columns-toggler")) {
        actions.push({
          type: "button",
          children: this.renderColumnsToggler()
        });
      }
      if (store.draggable && !store.isNested && region === "header" && store.rows.length > 1 && !~this.renderedToolbars.indexOf("drag-toggler")) {
        actions.push({
          type: "button",
          children: this.renderDragToggler()
        });
      }
      return Array.isArray(actions) && actions.length ? import_react22.default.createElement("div", { className: cx("Table-actions") }, actions.map(function(action, key) {
        return render("action/".concat(key), __assign({ type: "button" }, action), {
          onAction: _this.handleAction,
          key,
          btnDisabled: store.dragging,
          data: store.getData(data)
        });
      })) : null;
    };
    Table2.prototype.renderHeader = function(editable) {
      var _a = this.props, header = _a.header, headerClassName = _a.headerClassName, toolbarClassName = _a.toolbarClassName, headerToolbarClassName = _a.headerToolbarClassName, headerToolbarRender = _a.headerToolbarRender, render = _a.render, showHeader = _a.showHeader, store = _a.store, cx = _a.classnames, data = _a.data, __ = _a.translate;
      if (showHeader === false) {
        return null;
      }
      var otherProps = {};
      var child = headerToolbarRender ? headerToolbarRender(__assign(__assign(__assign({}, this.props), store.eventContext), otherProps), this.renderToolbar) : null;
      var actions = this.renderActions("header");
      var toolbarNode = actions || child || store.dragging ? import_react22.default.createElement(
        "div",
        { className: cx("Table-toolbar Table-headToolbar", toolbarClassName, headerToolbarClassName), key: "header-toolbar" },
        actions,
        child,
        store.dragging ? import_react22.default.createElement("div", { className: cx("Table-dragTip"), ref: this.dragTipRef }, __("Table.dragTip")) : null
      ) : null;
      var headerNode = header && (!Array.isArray(header) || header.length) ? import_react22.default.createElement("div", { className: cx("Table-header", headerClassName), key: "header" }, render("header", header, __assign(__assign({}, editable === false ? otherProps : null), { data: store.getData(data) }))) : null;
      return headerNode && toolbarNode ? [headerNode, toolbarNode] : headerNode || toolbarNode || null;
    };
    Table2.prototype.renderFooter = function() {
      var _a = this.props, footer = _a.footer, toolbarClassName = _a.toolbarClassName, footerToolbarClassName = _a.footerToolbarClassName, footerClassName = _a.footerClassName, footerToolbarRender = _a.footerToolbarRender, render = _a.render, showFooter = _a.showFooter, store = _a.store, data = _a.data, cx = _a.classnames, affixFooter = _a.affixFooter;
      if (showFooter === false) {
        return null;
      }
      var child = footerToolbarRender ? footerToolbarRender(__assign(__assign({}, this.props), store.eventContext), this.renderToolbar) : null;
      var actions = this.renderActions("footer");
      var footerNode = footer && (!Array.isArray(footer) || footer.length) ? import_react22.default.createElement("div", { className: cx("Table-footer", footerClassName, affixFooter ? "Table-footer--affix" : ""), key: "footer" }, render("footer", footer, {
        data: store.getData(data)
      })) : null;
      var toolbarNode = actions || child ? import_react22.default.createElement(
        "div",
        { className: cx("Table-toolbar Table-footToolbar", toolbarClassName, footerToolbarClassName, !footerNode && affixFooter ? "Table-footToolbar--affix" : ""), key: "footer-toolbar" },
        actions,
        child
      ) : null;
      return footerNode && toolbarNode ? [toolbarNode, footerNode] : footerNode || toolbarNode || null;
    };
    Table2.prototype.renderTableContent = function() {
      var _a = this.props, cx = _a.classnames, tableClassName = _a.tableClassName, store = _a.store, placeholder = _a.placeholder, render = _a.render, checkOnItemClick = _a.checkOnItemClick, buildItemProps = _a.buildItemProps, rowClassNameExpr = _a.rowClassNameExpr, rowClassName = _a.rowClassName, prefixRow = _a.prefixRow, locale = _a.locale, affixRow = _a.affixRow, tableContentClassName = _a.tableContentClassName, translate = _a.translate, itemAction = _a.itemAction, affixRowClassNameExpr = _a.affixRowClassNameExpr, affixRowClassName = _a.affixRowClassName, prefixRowClassNameExpr = _a.prefixRowClassNameExpr, prefixRowClassName = _a.prefixRowClassName, autoFillHeight = _a.autoFillHeight, affixHeader = _a.affixHeader, itemActions = _a.itemActions, dispatchEvent = _a.dispatchEvent, onEvent = _a.onEvent, loadingConfig = _a.loadingConfig, testIdBuilder = _a.testIdBuilder, data = _a.data;
      store.rows.length;
      return import_react22.default.createElement(
        import_react22.default.Fragment,
        null,
        import_react22.default.createElement(TableContent$1, { testIdBuilder, tableClassName: cx({
          "Table-table--checkOnItemClick": checkOnItemClick,
          "Table-table--withCombine": store.combineNum > 0,
          "Table-table--affixHeader": affixHeader && !autoFillHeight && store.columnWidthReady,
          "Table-table--tableFillHeight": autoFillHeight && !store.items.length
        }, tableClassName), className: tableContentClassName, itemActions, itemAction, store, classnames: cx, columns: store.filteredColumns, columnsGroup: store.columnGroup, rows: store.items, placeholder, render, onMouseMove: (
          // 如果没有 itemActions, 那么就不需要处理了。
          Array.isArray(itemActions) && itemActions.length ? this.handleMouseMove : void 0
        ), onScroll: this.handleOutterScroll, tableRef: this.tableRef, renderHeadCell: this.renderHeadCell, renderCell: this.renderCell, onCheck: this.handleCheck, onRowClick: this.handleRowClick, onRowDbClick: this.handleRowDbClick, onRowMouseEnter: this.handleRowMouseEnter, onRowMouseLeave: this.handleRowMouseLeave, onQuickChange: store.dragging ? void 0 : this.handleQuickChange, footable: store.footable, footableColumns: store.footableColumns, checkOnItemClick, buildItemProps, onAction: this.handleAction, rowClassNameExpr, rowClassName, data: store.data, prefixRow, affixRow, prefixRowClassName, affixRowClassName, locale, translate, dispatchEvent, onEvent, loading: store.loading }, renderItemActions({
          store,
          classnames: cx,
          render,
          itemActions
        })),
        import_react22.default.createElement(Spinner$1, { loadingConfig, overlay: true, show: store.loading })
      );
    };
    Table2.prototype.render = function() {
      var _a = this.props, className = _a.className, style = _a.style, store = _a.store, cx = _a.classnames, affixColumns = _a.affixColumns, affixHeader = _a.affixHeader, autoFillHeight = _a.autoFillHeight, autoGenerateFilter = _a.autoGenerateFilter, mobileUI = _a.mobileUI, testIdBuilder = _a.testIdBuilder, id = _a.id;
      this.renderedToolbars = [];
      var heading = affixHeader && !autoFillHeight ? null : this.renderHeading();
      var header = affixHeader && !autoFillHeight ? null : this.renderHeader();
      var footer = this.renderFooter();
      var tableClassName = cx("Table-table", this.props.tableClassName, {
        "Table-table--withCombine": store.combineNum > 0
      });
      return import_react22.default.createElement(
        "div",
        __assign({ ref: this.dom, className: cx("Table", { "is-mobile": mobileUI }, className, {
          "Table--unsaved": !!store.modified || !!store.moved,
          "Table--autoFillHeight": autoFillHeight
        }), style: store.buildStyles(style), "data-id": id }, testIdBuilder === null || testIdBuilder === void 0 ? void 0 : testIdBuilder.getTestId()),
        autoGenerateFilter ? this.renderAutoFilterForm() : null,
        this.renderAffixHeader(tableClassName),
        header,
        heading,
        import_react22.default.createElement("div", { className: cx("Table-contentWrap"), onMouseLeave: this.handleMouseLeave }, this.renderTableContent()),
        footer
      );
    };
    Table2.contextType = ScopedContext;
    Table2.propsList = [
      "header",
      "headerToolbarRender",
      "footer",
      "footerToolbarRender",
      "footable",
      "expandConfig",
      "placeholder",
      "tableClassName",
      "headingClassName",
      "source",
      "selectable",
      "columnsTogglable",
      "affixHeader",
      "affixColumns",
      "headerClassName",
      "footerClassName",
      "selected",
      "multiple",
      "primaryField",
      "hideQuickSaveBtn",
      "itemCheckableOn",
      "itemDraggableOn",
      "draggable",
      "checkOnItemClick",
      "hideCheckToggler",
      "itemAction",
      "itemActions",
      "combineNum",
      "combineFromIndex",
      "items",
      "columns",
      "valueField",
      "saveImmediately",
      "rowClassName",
      "rowClassNameExpr",
      "affixRowClassNameExpr",
      "prefixRowClassNameExpr",
      "popOverContainer",
      "headerToolbarClassName",
      "toolbarClassName",
      "footerToolbarClassName",
      "itemBadge",
      "autoFillHeight",
      "onSelect",
      "keepItemSelectionOnPageChange",
      "maxKeepItemSelectionLength",
      "maxItemSelectionLength",
      "autoGenerateFilter"
    ];
    Table2.defaultProps = {
      className: "",
      placeholder: "placeholder.noData",
      tableClassName: "",
      source: "$items",
      selectable: false,
      columnsTogglable: "auto",
      affixHeader: true,
      headerClassName: "",
      footerClassName: "",
      toolbarClassName: "",
      headerToolbarClassName: "",
      footerToolbarClassName: "",
      primaryField: "id",
      itemCheckableOn: "",
      itemDraggableOn: "",
      hideCheckToggler: false,
      canAccessSuperData: false,
      resizable: true
    };
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", Promise)
    ], Table2.prototype, "loadDeferredRow", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], Table2.prototype, "handleDragStart", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], Table2.prototype, "handleDragOver", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", Promise)
    ], Table2.prototype, "handleDrop", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", void 0)
    ], Table2.prototype, "handleDragEnd", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object, Object]),
      __metadata("design:returntype", void 0)
    ], Table2.prototype, "handleImageEnlarge", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], Table2.prototype, "handleColResizeMouseDown", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [MouseEvent]),
      __metadata("design:returntype", void 0)
    ], Table2.prototype, "handleColResizeMouseMove", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [MouseEvent]),
      __metadata("design:returntype", void 0)
    ], Table2.prototype, "handleColResizeMouseUp", null);
    return Table2;
  }(import_react22.default.Component)
);
var TableRendererBase = (
  /** @class */
  function(_super) {
    __extends(TableRendererBase2, _super);
    function TableRendererBase2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    TableRendererBase2.prototype.receive = function(values, subPath) {
      var _a, _b, _c;
      var scoped = this.context;
      if ((_a = this.props) === null || _a === void 0 ? void 0 : _a.host) {
        return (_c = (_b = this.props.host).receive) === null || _c === void 0 ? void 0 : _c.call(_b, values, subPath);
      }
      if (subPath) {
        return scoped.send(subPath, values);
      }
    };
    TableRendererBase2.prototype.getEventTargets = function(ctx, index, condition, oldCondition) {
      return __awaiter(this, void 0, void 0, function() {
        var store;
        return __generator(this, function(_a) {
          store = this.props.store;
          return [2, getMatchedEventTargets(store.rows, ctx || this.props.data, index, condition, oldCondition)];
        });
      });
    };
    TableRendererBase2.prototype.reload = function(subPath, query, ctx, silent, replace, args) {
      var _a, _b, _c;
      return __awaiter(this, void 0, void 0, function() {
        var targets, scoped;
        var _this = this;
        return __generator(this, function(_d) {
          switch (_d.label) {
            case 0:
              if (!((args === null || args === void 0 ? void 0 : args.index) || (args === null || args === void 0 ? void 0 : args.condition)))
                return [3, 3];
              return [4, this.getEventTargets(ctx || this.props.data, args.index, args === null || args === void 0 ? void 0 : args.condition)];
            case 1:
              targets = _d.sent();
              return [4, Promise.all(targets.map(function(target) {
                return _this.loadDeferredRow(target);
              }))];
            case 2:
              _d.sent();
              return [
                2
                /*return*/
              ];
            case 3:
              scoped = this.context;
              if ((_a = this.props) === null || _a === void 0 ? void 0 : _a.host) {
                return [2, (_c = (_b = this.props.host).reload) === null || _c === void 0 ? void 0 : _c.call(_b, subPath, query, ctx)];
              }
              if (subPath) {
                return [2, scoped.reload(subPath, ctx)];
              }
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    TableRendererBase2.prototype.setData = function(values, replace, index, condition) {
      var _a, _b, _c, _d;
      return __awaiter(this, void 0, void 0, function() {
        var store, targets, data;
        return __generator(this, function(_e) {
          switch (_e.label) {
            case 0:
              store = this.props.store;
              if (!(index !== void 0 || condition !== void 0))
                return [3, 2];
              return [4, this.getEventTargets(this.props.data, index, condition)];
            case 1:
              targets = _e.sent();
              targets.forEach(function(target) {
                target.updateData(values);
              });
              return [3, 3];
            case 2:
              if ((_a = this.props) === null || _a === void 0 ? void 0 : _a.host) {
                return [2, (_c = (_b = this.props.host).setData) === null || _c === void 0 ? void 0 : _c.call(_b, values, replace, index, condition)];
              } else {
                data = __assign(__assign({}, values), {
                  rows: (_d = values.rows) !== null && _d !== void 0 ? _d : values.items
                  // 做个兼容
                });
                return [2, store.updateData(data, void 0, replace)];
              }
              _e.label = 3;
            case 3:
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    TableRendererBase2.prototype.getData = function() {
      var _a = this.props, store = _a.store, data = _a.data;
      return store.getData(data);
    };
    TableRendererBase2.prototype.hasModifiedItems = function() {
      return this.props.store.modified;
    };
    TableRendererBase2.prototype.doAction = function(action, ctx, throwErrors, args) {
      return __awaiter(this, void 0, void 0, function() {
        var _a, store, valueField, data, actionType, _b, rows, targets, targets2;
        return __generator(this, function(_c) {
          switch (_c.label) {
            case 0:
              _a = this.props, store = _a.store, valueField = _a.valueField, data = _a.data;
              actionType = action === null || action === void 0 ? void 0 : action.actionType;
              _b = actionType;
              switch (_b) {
                case "selectAll":
                  return [3, 1];
                case "clearAll":
                  return [3, 2];
                case "select":
                  return [3, 3];
                case "initDrag":
                  return [3, 5];
                case "cancelDrag":
                  return [3, 6];
                case "submitQuickEdit":
                  return [3, 7];
                case "toggleExpanded":
                  return [3, 8];
                case "setExpanded":
                  return [3, 10];
              }
              return [3, 12];
            case 1:
              store.clear();
              store.toggleAll();
              this.syncSelected();
              return [3, 13];
            case 2:
              store.clear();
              this.syncSelected();
              return [3, 13];
            case 3:
              return [4, this.getEventTargets(ctx, args.index, args.condition, args.selected)];
            case 4:
              rows = _c.sent();
              store.updateSelected(rows.map(function(item) {
                return item.data;
              }), valueField);
              this.syncSelected();
              return [3, 13];
            case 5:
              store.startDragging();
              return [3, 13];
            case 6:
              store.stopDragging();
              return [3, 13];
            case 7:
              this.handleSave();
              return [3, 13];
            case 8:
              return [4, this.getEventTargets(ctx, args.index, args.condition)];
            case 9:
              targets = _c.sent();
              targets.forEach(function(target) {
                store.toggleExpanded(target);
              });
              return [3, 13];
            case 10:
              return [4, this.getEventTargets(ctx, args.index, args.condition)];
            case 11:
              targets2 = _c.sent();
              targets2.forEach(function(target) {
                store.setExpanded(target, !!args.value);
              });
              return [3, 13];
            case 12:
              return [2, this.handleAction(void 0, action, data)];
            case 13:
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    return TableRendererBase2;
  }(Table)
);
var TableRenderer = (
  /** @class */
  function(_super) {
    __extends(TableRenderer2, _super);
    function TableRenderer2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    TableRenderer2 = __decorate([
      Renderer({
        type: "table",
        storeType: TableStore.name,
        name: "table"
      })
    ], TableRenderer2);
    return TableRenderer2;
  }(TableRendererBase)
);

export {
  HocQuickEdit,
  HocCopyable,
  HocPopOver,
  observer2 as observer,
  TableCell,
  Table,
  TableRendererBase,
  TableRenderer
};
//# sourceMappingURL=chunk-4AUUPZ2O.js.map
