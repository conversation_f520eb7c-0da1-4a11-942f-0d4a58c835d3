import {
  AnchorNavSection,
  ThemedAnchorNav
} from "./chunk-GFI6XVUE.js";
import "./chunk-ZRTRFIWU.js";
import "./chunk-6C4OBBQZ.js";
import "./chunk-M5OFQAQB.js";
import "./chunk-YPPVVTGH.js";
import {
  __assign,
  __decorate,
  __extends,
  __metadata,
  __read,
  __spreadArray
} from "./chunk-F25BIIHK.js";
import {
  Renderer,
  autobind,
  filter,
  isVisible
} from "./chunk-LZQZ2OHM.js";
import "./chunk-QMS2VRR4.js";
import "./chunk-5QW7M2DY.js";
import "./chunk-ZJNJ4ZV3.js";
import "./chunk-KEABM62F.js";
import "./chunk-CNJUQDSN.js";
import "./chunk-YZFIV7S4.js";
import "./chunk-7XBFYOTW.js";
import {
  require_react
} from "./chunk-ZUWHLQVA.js";
import "./chunk-KNGT5NUO.js";
import "./chunk-X3JSFYRQ.js";
import {
  __toESM
} from "./chunk-GFT2G5UO.js";

// node_modules/amis/esm/renderers/AnchorNav.js
var import_react = __toESM(require_react());
var AnchorNav = (
  /** @class */
  function(_super) {
    __extends(AnchorNav2, _super);
    function AnchorNav2(props) {
      var _this = _super.call(this, props) || this;
      var links = props.links;
      var active = 0;
      if (typeof props.active !== "undefined") {
        active = props.active;
      } else {
        var section = _this.getActiveSection(links, props.active, null);
        active = section && section.href ? section.href : links[0] && links[0].href || 0;
      }
      _this.state = {
        active
      };
      return _this;
    }
    AnchorNav2.prototype.getActiveSection = function(links, active, section) {
      var _this = this;
      if (section) {
        return section;
      }
      links.forEach(function(link) {
        if (link.href === active) {
          section = link;
        } else {
          if (link.children) {
            _this.getActiveSection(link.children, active, section);
          }
        }
      });
      return section;
    };
    AnchorNav2.prototype.handleSelect = function(key) {
      this.setState({
        active: key
      });
    };
    AnchorNav2.prototype.locateTo = function(index) {
      var links = this.props.links;
      Array.isArray(links) && links[index] && this.setState({
        active: links[index].href || index
      });
    };
    AnchorNav2.prototype.renderSections = function(links, parentIdx) {
      var _this = this;
      var _a = this.props, cx = _a.classnames, ns = _a.classPrefix, sectionRender = _a.sectionRender, render = _a.render, data = _a.data;
      links = Array.isArray(links) ? links : [links];
      var children = [];
      links.forEach(function(section, index) {
        if (isVisible(section, data)) {
          var curIdx = (parentIdx ? parentIdx + "-" : "") + index;
          children.push(
            /** 内容区 */
            import_react.default.createElement(AnchorNavSection, __assign({}, section, { title: filter(section.title, data), key: curIdx, name: section.href || curIdx }), _this.renderSection ? _this.renderSection(section, _this.props, curIdx) : sectionRender ? sectionRender(section, _this.props, curIdx) : render("section/".concat(curIdx), section.body || ""))
          );
          if (section.children) {
            children.push.apply(children, __spreadArray([], __read(_this.renderSections(section.children, curIdx)), false));
          }
        }
      });
      return children.filter(function(item) {
        return !!item;
      });
    };
    AnchorNav2.prototype.render = function() {
      var _a = this.props, cx = _a.classnames, ns = _a.classPrefix, className = _a.className, style = _a.style, linkClassName = _a.linkClassName, sectionClassName = _a.sectionClassName, direction = _a.direction, sectionRender = _a.sectionRender, render = _a.render, data = _a.data;
      var links = this.props.links;
      if (!links) {
        return null;
      }
      var children = this.renderSections(links);
      return import_react.default.createElement(ThemedAnchorNav, { classPrefix: ns, classnames: cx, className, style, linkClassName, sectionClassName, onSelect: this.handleSelect, active: this.state.active, direction }, children);
    };
    AnchorNav2.defaultProps = {
      className: "",
      linkClassName: "",
      sectionClassName: ""
    };
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], AnchorNav2.prototype, "handleSelect", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Number]),
      __metadata("design:returntype", void 0)
    ], AnchorNav2.prototype, "locateTo", null);
    return AnchorNav2;
  }(import_react.default.Component)
);
var AnchorNavRenderer = (
  /** @class */
  function(_super) {
    __extends(AnchorNavRenderer2, _super);
    function AnchorNavRenderer2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    AnchorNavRenderer2 = __decorate([
      Renderer({
        type: "anchor-nav"
      })
    ], AnchorNavRenderer2);
    return AnchorNavRenderer2;
  }(AnchorNav)
);
export {
  AnchorNavRenderer,
  AnchorNav as default
};
//# sourceMappingURL=AnchorNav-PPZABFJK.js.map
