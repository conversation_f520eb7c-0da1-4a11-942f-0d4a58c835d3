Object.defineProperty(exports,"__esModule",{value:!0});var _exportNames={install:!0},_core=(exports.install=install,require("@vxe-ui/core")),_column=require("./column"),_colgroup=(Object.keys(_column).forEach(function(e){"default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(_exportNames,e)||e in exports&&exports[e]===_column[e]||Object.defineProperty(exports,e,{enumerable:!0,get:function(){return _column[e]}})}),require("./colgroup")),_grid=(Object.keys(_colgroup).forEach(function(e){"default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(_exportNames,e)||e in exports&&exports[e]===_colgroup[e]||Object.defineProperty(exports,e,{enumerable:!0,get:function(){return _colgroup[e]}})}),require("./grid")),_table=(Object.keys(_grid).forEach(function(e){"default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(_exportNames,e)||e in exports&&exports[e]===_grid[e]||Object.defineProperty(exports,e,{enumerable:!0,get:function(){return _grid[e]}})}),require("./table")),_toolbar=(Object.keys(_table).forEach(function(e){"default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(_exportNames,e)||e in exports&&exports[e]===_table[e]||Object.defineProperty(exports,e,{enumerable:!0,get:function(){return _table[e]}})}),require("./toolbar")),_zhCN=(Object.keys(_toolbar).forEach(function(e){"default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(_exportNames,e)||e in exports&&exports[e]===_toolbar[e]||Object.defineProperty(exports,e,{enumerable:!0,get:function(){return _toolbar[e]}})}),_interopRequireDefault(require("./locale/lang/zh-CN"))),_ui=require("./ui");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}Object.keys(_ui).forEach(function(e){"default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(_exportNames,e)||e in exports&&exports[e]===_ui[e]||Object.defineProperty(exports,e,{enumerable:!0,get:function(){return _ui[e]}})});let components=[_column.VxeColumn,_colgroup.VxeColgroup,_grid.VxeGrid,_table.VxeTable,_toolbar.VxeToolbar];function install(t,e){_core.VxeUI.setConfig(e),components.forEach(e=>e.install(t))}if(!_core.VxeUI.hasLanguage("zh-CN")){let e="zh-CN";_core.VxeUI.setI18n(e,_zhCN.default),_core.VxeUI.setLanguage(e)}_core.VxeUI.setTheme("light");