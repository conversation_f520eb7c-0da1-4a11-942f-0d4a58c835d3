{"version": 3, "sources": ["../../amis/esm/renderers/Form/ChartRadios.js"], "sourcesContent": ["/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __assign, __decorate, __metadata } from 'tslib';\nimport React from 'react';\nimport { autobind, OptionsControl } from 'amis-core';\nimport { supportStatic } from './StaticHoc.js';\n\nvar ChartRadiosControl = /** @class */ (function (_super) {\n    __extends(ChartRadiosControl, _super);\n    function ChartRadiosControl() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.highlightIndex = -1;\n        _this.prevIndex = -1;\n        return _this;\n    }\n    ChartRadiosControl.prototype.chartRef = function (chart) {\n        var _this = this;\n        var _a;\n        this.chart = chart;\n        (_a = this.chart) === null || _a === void 0 ? void 0 : _a.on('click', 'series', function (params) {\n            _this.props.onToggle(_this.props.options[params.dataIndex]);\n        });\n        // 因为会要先 setOptions 再来。\n        setTimeout(function () { return _this.highlight(); });\n    };\n    ChartRadiosControl.prototype.highlight = function (index) {\n        if (index === void 0) { index = this.highlightIndex; }\n        if (this.props.static) {\n            return;\n        }\n        this.highlightIndex = index;\n        if (!this.chart || this.prevIndex === index) {\n            return;\n        }\n        if (~this.prevIndex) {\n            this.chart.dispatchAction({\n                type: 'downplay',\n                seriesIndex: 0,\n                dataIndex: this.prevIndex\n            });\n        }\n        if (~index) {\n            this.chart.dispatchAction({\n                type: 'highlight',\n                seriesIndex: 0,\n                dataIndex: index\n            });\n            // 显示 tooltip\n            if (this.props.showTooltipOnHighlight) {\n                this.chart.dispatchAction({\n                    type: 'showTip',\n                    seriesIndex: 0,\n                    dataIndex: index\n                });\n            }\n        }\n        this.prevIndex = index;\n    };\n    ChartRadiosControl.prototype.componentDidMount = function () {\n        // to do 初始化有值的情况暂时无法生效\n        if (this.props.selectedOptions.length) {\n            this.highlight(this.props.options.indexOf(this.props.selectedOptions[0]));\n        }\n    };\n    ChartRadiosControl.prototype.componentDidUpdate = function () {\n        if (this.props.selectedOptions.length) {\n            this.highlight(this.props.options.indexOf(this.props.selectedOptions[0]));\n        }\n    };\n    ChartRadiosControl.prototype.renderStatic = function (displayValue) {\n        if (displayValue === void 0) { displayValue = '-'; }\n        this.prevIndex = -1;\n        this.highlightIndex = -1;\n        var _a = this.props, _b = _a.options, options = _b === void 0 ? [] : _b, selectedOptions = _a.selectedOptions, _c = _a.labelField, labelField = _c === void 0 ? 'label' : _c, _d = _a.valueField, valueField = _d === void 0 ? 'value' : _d, chartValueField = _a.chartValueField;\n        if (options.length && selectedOptions.length) {\n            var count = options.reduce(function (all, cur) {\n                return all + cur[chartValueField || valueField];\n            }, 0);\n            if (count > 0) {\n                var percent = ((+selectedOptions[0][chartValueField || valueField] / count) *\n                    100).toFixed(2);\n                displayValue = \"\".concat(selectedOptions[0][labelField], \"\\uFF1A\").concat(percent, \"%\");\n            }\n        }\n        return React.createElement(React.Fragment, null, displayValue);\n    };\n    ChartRadiosControl.prototype.render = function () {\n        var _a = this.props, options = _a.options, labelField = _a.labelField, chartValueField = _a.chartValueField, valueField = _a.valueField, render = _a.render;\n        var config = __assign(__assign({ legend: {\n                top: 10\n            }, tooltip: {\n                formatter: function (params) {\n                    return \"\".concat(params.name, \"\\uFF1A\").concat(params.value[chartValueField || valueField || 'value'], \"\\uFF08\").concat(params.percent, \"%\\uFF09\");\n                }\n            }, series: [\n                {\n                    type: 'pie',\n                    top: 30,\n                    bottom: 0\n                }\n            ] }, this.props.config), { dataset: {\n                dimensions: [\n                    labelField || 'label',\n                    chartValueField || valueField || 'value'\n                ],\n                source: options\n            } });\n        return render('chart', {\n            type: 'chart'\n        }, {\n            config: config,\n            chartRef: this.chartRef\n        });\n    };\n    __decorate([\n        autobind,\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", [Object]),\n        __metadata(\"design:returntype\", void 0)\n    ], ChartRadiosControl.prototype, \"chartRef\", null);\n    __decorate([\n        supportStatic(),\n        __metadata(\"design:type\", Function),\n        __metadata(\"design:paramtypes\", []),\n        __metadata(\"design:returntype\", void 0)\n    ], ChartRadiosControl.prototype, \"render\", null);\n    return ChartRadiosControl;\n}(React.Component));\nvar RadiosControlRenderer = /** @class */ (function (_super) {\n    __extends(RadiosControlRenderer, _super);\n    function RadiosControlRenderer() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    RadiosControlRenderer.defaultProps = {\n        multiple: false\n    };\n    RadiosControlRenderer = __decorate([\n        OptionsControl({\n            type: 'chart-radios',\n            sizeMutable: false\n        })\n    ], RadiosControlRenderer);\n    return RadiosControlRenderer;\n}(ChartRadiosControl));\n\nexport { RadiosControlRenderer, ChartRadiosControl as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAOA,mBAAkB;AAIlB,IAAI;AAAA;AAAA,EAAoC,SAAU,QAAQ;AACtD,cAAUA,qBAAoB,MAAM;AACpC,aAASA,sBAAqB;AAC1B,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,iBAAiB;AACvB,YAAM,YAAY;AAClB,aAAO;AAAA,IACX;AACA,IAAAA,oBAAmB,UAAU,WAAW,SAAU,OAAO;AACrD,UAAI,QAAQ;AACZ,UAAI;AACJ,WAAK,QAAQ;AACb,OAAC,KAAK,KAAK,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,GAAG,SAAS,UAAU,SAAU,QAAQ;AAC9F,cAAM,MAAM,SAAS,MAAM,MAAM,QAAQ,OAAO,SAAS,CAAC;AAAA,MAC9D,CAAC;AAED,iBAAW,WAAY;AAAE,eAAO,MAAM,UAAU;AAAA,MAAG,CAAC;AAAA,IACxD;AACA,IAAAA,oBAAmB,UAAU,YAAY,SAAU,OAAO;AACtD,UAAI,UAAU,QAAQ;AAAE,gBAAQ,KAAK;AAAA,MAAgB;AACrD,UAAI,KAAK,MAAM,QAAQ;AACnB;AAAA,MACJ;AACA,WAAK,iBAAiB;AACtB,UAAI,CAAC,KAAK,SAAS,KAAK,cAAc,OAAO;AACzC;AAAA,MACJ;AACA,UAAI,CAAC,KAAK,WAAW;AACjB,aAAK,MAAM,eAAe;AAAA,UACtB,MAAM;AAAA,UACN,aAAa;AAAA,UACb,WAAW,KAAK;AAAA,QACpB,CAAC;AAAA,MACL;AACA,UAAI,CAAC,OAAO;AACR,aAAK,MAAM,eAAe;AAAA,UACtB,MAAM;AAAA,UACN,aAAa;AAAA,UACb,WAAW;AAAA,QACf,CAAC;AAED,YAAI,KAAK,MAAM,wBAAwB;AACnC,eAAK,MAAM,eAAe;AAAA,YACtB,MAAM;AAAA,YACN,aAAa;AAAA,YACb,WAAW;AAAA,UACf,CAAC;AAAA,QACL;AAAA,MACJ;AACA,WAAK,YAAY;AAAA,IACrB;AACA,IAAAA,oBAAmB,UAAU,oBAAoB,WAAY;AAEzD,UAAI,KAAK,MAAM,gBAAgB,QAAQ;AACnC,aAAK,UAAU,KAAK,MAAM,QAAQ,QAAQ,KAAK,MAAM,gBAAgB,CAAC,CAAC,CAAC;AAAA,MAC5E;AAAA,IACJ;AACA,IAAAA,oBAAmB,UAAU,qBAAqB,WAAY;AAC1D,UAAI,KAAK,MAAM,gBAAgB,QAAQ;AACnC,aAAK,UAAU,KAAK,MAAM,QAAQ,QAAQ,KAAK,MAAM,gBAAgB,CAAC,CAAC,CAAC;AAAA,MAC5E;AAAA,IACJ;AACA,IAAAA,oBAAmB,UAAU,eAAe,SAAU,cAAc;AAChE,UAAI,iBAAiB,QAAQ;AAAE,uBAAe;AAAA,MAAK;AACnD,WAAK,YAAY;AACjB,WAAK,iBAAiB;AACtB,UAAI,KAAK,KAAK,OAAO,KAAK,GAAG,SAAS,UAAU,OAAO,SAAS,CAAC,IAAI,IAAI,kBAAkB,GAAG,iBAAiB,KAAK,GAAG,YAAY,aAAa,OAAO,SAAS,UAAU,IAAI,KAAK,GAAG,YAAY,aAAa,OAAO,SAAS,UAAU,IAAI,kBAAkB,GAAG;AAClQ,UAAI,QAAQ,UAAU,gBAAgB,QAAQ;AAC1C,YAAI,QAAQ,QAAQ,OAAO,SAAU,KAAK,KAAK;AAC3C,iBAAO,MAAM,IAAI,mBAAmB,UAAU;AAAA,QAClD,GAAG,CAAC;AACJ,YAAI,QAAQ,GAAG;AACX,cAAI,WAAY,CAAC,gBAAgB,CAAC,EAAE,mBAAmB,UAAU,IAAI,QACjE,KAAK,QAAQ,CAAC;AAClB,yBAAe,GAAG,OAAO,gBAAgB,CAAC,EAAE,UAAU,GAAG,GAAQ,EAAE,OAAO,SAAS,GAAG;AAAA,QAC1F;AAAA,MACJ;AACA,aAAO,aAAAC,QAAM,cAAc,aAAAA,QAAM,UAAU,MAAM,YAAY;AAAA,IACjE;AACA,IAAAD,oBAAmB,UAAU,SAAS,WAAY;AAC9C,UAAI,KAAK,KAAK,OAAO,UAAU,GAAG,SAAS,aAAa,GAAG,YAAY,kBAAkB,GAAG,iBAAiB,aAAa,GAAG,YAAY,SAAS,GAAG;AACrJ,UAAI,SAAS,SAAS,SAAS,EAAE,QAAQ;AAAA,QACjC,KAAK;AAAA,MACT,GAAG,SAAS;AAAA,QACR,WAAW,SAAU,QAAQ;AACzB,iBAAO,GAAG,OAAO,OAAO,MAAM,GAAQ,EAAE,OAAO,OAAO,MAAM,mBAAmB,cAAc,OAAO,GAAG,GAAQ,EAAE,OAAO,OAAO,SAAS,IAAS;AAAA,QACrJ;AAAA,MACJ,GAAG,QAAQ;AAAA,QACP;AAAA,UACI,MAAM;AAAA,UACN,KAAK;AAAA,UACL,QAAQ;AAAA,QACZ;AAAA,MACJ,EAAE,GAAG,KAAK,MAAM,MAAM,GAAG,EAAE,SAAS;AAAA,QAChC,YAAY;AAAA,UACR,cAAc;AAAA,UACd,mBAAmB,cAAc;AAAA,QACrC;AAAA,QACA,QAAQ;AAAA,MACZ,EAAE,CAAC;AACP,aAAO,OAAO,SAAS;AAAA,QACnB,MAAM;AAAA,MACV,GAAG;AAAA,QACC;AAAA,QACA,UAAU,KAAK;AAAA,MACnB,CAAC;AAAA,IACL;AACA,eAAW;AAAA,MACP;AAAA,MACA,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,MAAM,CAAC;AAAA,MACxC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,oBAAmB,WAAW,YAAY,IAAI;AACjD,eAAW;AAAA,MACP,cAAc;AAAA,MACd,WAAW,eAAe,QAAQ;AAAA,MAClC,WAAW,qBAAqB,CAAC,CAAC;AAAA,MAClC,WAAW,qBAAqB,MAAM;AAAA,IAC1C,GAAGA,oBAAmB,WAAW,UAAU,IAAI;AAC/C,WAAOA;AAAA,EACX,EAAE,aAAAC,QAAM,SAAS;AAAA;AACjB,IAAI;AAAA;AAAA,EAAuC,SAAU,QAAQ;AACzD,cAAUC,wBAAuB,MAAM;AACvC,aAASA,yBAAwB;AAC7B,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,uBAAsB,eAAe;AAAA,MACjC,UAAU;AAAA,IACd;AACA,IAAAA,yBAAwB,WAAW;AAAA,MAC/B,eAAe;AAAA,QACX,MAAM;AAAA,QACN,aAAa;AAAA,MACjB,CAAC;AAAA,IACL,GAAGA,sBAAqB;AACxB,WAAOA;AAAA,EACX,EAAE,kBAAkB;AAAA;", "names": ["ChartRadiosControl", "React", "RadiosControlRenderer"]}