import {
  __commonJS,
  __esm,
  __export,
  __toCommonJS
} from "./chunk-GFT2G5UO.js";

// node_modules/svg.js/dist/svg.js
var require_svg = __commonJS({
  "node_modules/svg.js/dist/svg.js"(exports, module) {
    (function(root, factory) {
      if (typeof define === "function" && define.amd) {
        define(function() {
          return factory(root, root.document);
        });
      } else if (typeof exports === "object") {
        module.exports = root.document ? factory(root, root.document) : function(w) {
          return factory(w, w.document);
        };
      } else {
        root.SVG = factory(root, root.document);
      }
    })(typeof window !== "undefined" ? window : exports, function(window2, document2) {
      var globalRef = typeof this !== "undefined" ? this : window2;
      var SVG = globalRef.SVG = function(element) {
        if (SVG.supported) {
          element = new SVG.Doc(element);
          if (!SVG.parser.draw)
            SVG.prepare();
          return element;
        }
      };
      SVG.ns = "http://www.w3.org/2000/svg";
      SVG.xmlns = "http://www.w3.org/2000/xmlns/";
      SVG.xlink = "http://www.w3.org/1999/xlink";
      SVG.svgjs = "http://svgjs.com/svgjs";
      SVG.supported = function() {
        return !!document2.createElementNS && !!document2.createElementNS(SVG.ns, "svg").createSVGRect;
      }();
      if (!SVG.supported)
        return false;
      SVG.did = 1e3;
      SVG.eid = function(name) {
        return "Svgjs" + capitalize(name) + SVG.did++;
      };
      SVG.create = function(name) {
        var element = document2.createElementNS(this.ns, name);
        element.setAttribute("id", this.eid(name));
        return element;
      };
      SVG.extend = function() {
        var modules, methods, key, i2;
        modules = [].slice.call(arguments);
        methods = modules.pop();
        for (i2 = modules.length - 1; i2 >= 0; i2--)
          if (modules[i2])
            for (key in methods)
              modules[i2].prototype[key] = methods[key];
        if (SVG.Set && SVG.Set.inherit)
          SVG.Set.inherit();
      };
      SVG.invent = function(config) {
        var initializer = typeof config.create == "function" ? config.create : function() {
          this.constructor.call(this, SVG.create(config.create));
        };
        if (config.inherit)
          initializer.prototype = new config.inherit();
        if (config.extend)
          SVG.extend(initializer, config.extend);
        if (config.construct)
          SVG.extend(config.parent || SVG.Container, config.construct);
        return initializer;
      };
      SVG.adopt = function(node) {
        if (!node)
          return null;
        if (node.instance)
          return node.instance;
        var element;
        if (node.nodeName == "svg")
          element = node.parentNode instanceof window2.SVGElement ? new SVG.Nested() : new SVG.Doc();
        else if (node.nodeName == "linearGradient")
          element = new SVG.Gradient("linear");
        else if (node.nodeName == "radialGradient")
          element = new SVG.Gradient("radial");
        else if (SVG[capitalize(node.nodeName)])
          element = new SVG[capitalize(node.nodeName)]();
        else
          element = new SVG.Element(node);
        element.type = node.nodeName;
        element.node = node;
        node.instance = element;
        if (element instanceof SVG.Doc)
          element.namespace().defs();
        element.setData(JSON.parse(node.getAttribute("svgjs:data")) || {});
        return element;
      };
      SVG.prepare = function() {
        var body = document2.getElementsByTagName("body")[0], draw = (body ? new SVG.Doc(body) : SVG.adopt(document2.documentElement).nested()).size(2, 0);
        SVG.parser = {
          body: body || document2.documentElement,
          draw: draw.style("opacity:0;position:absolute;left:-100%;top:-100%;overflow:hidden").attr("focusable", "false").node,
          poly: draw.polyline().node,
          path: draw.path().node,
          native: SVG.create("svg")
        };
      };
      SVG.parser = {
        native: SVG.create("svg")
      };
      document2.addEventListener("DOMContentLoaded", function() {
        if (!SVG.parser.draw)
          SVG.prepare();
      }, false);
      SVG.regex = {
        // Parse unit value
        numberAndUnit: /^([+-]?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?)([a-z%]*)$/i,
        hex: /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,
        rgb: /rgb\((\d+),(\d+),(\d+)\)/,
        reference: /#([a-z0-9\-_]+)/i,
        transforms: /\)\s*,?\s*/,
        whitespace: /\s/g,
        isHex: /^#[a-f0-9]{3,6}$/i,
        isRgb: /^rgb\(/,
        isCss: /[^:]+:[^;]+;?/,
        isBlank: /^(\s+)?$/,
        isNumber: /^[+-]?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,
        isPercent: /^-?[\d\.]+%$/,
        isImage: /\.(jpg|jpeg|png|gif|svg)(\?[^=]+.*)?/i,
        delimiter: /[\s,]+/,
        hyphen: /([^e])\-/gi,
        pathLetters: /[MLHVCSQTAZ]/gi,
        isPathLetter: /[MLHVCSQTAZ]/i,
        numbersWithDots: /((\d?\.\d+(?:e[+-]?\d+)?)((?:\.\d+(?:e[+-]?\d+)?)+))+/gi,
        dots: /\./g
      };
      SVG.utils = {
        // Map function
        map: function(array, block) {
          var i2, il2 = array.length, result = [];
          for (i2 = 0; i2 < il2; i2++)
            result.push(block(array[i2]));
          return result;
        },
        filter: function(array, block) {
          var i2, il2 = array.length, result = [];
          for (i2 = 0; i2 < il2; i2++)
            if (block(array[i2]))
              result.push(array[i2]);
          return result;
        },
        radians: function(d) {
          return d % 360 * Math.PI / 180;
        },
        degrees: function(r) {
          return r * 180 / Math.PI % 360;
        },
        filterSVGElements: function(nodes) {
          return this.filter(nodes, function(el) {
            return el instanceof window2.SVGElement;
          });
        }
      };
      SVG.defaults = {
        // Default attribute values
        attrs: {
          // fill and stroke
          "fill-opacity": 1,
          "stroke-opacity": 1,
          "stroke-width": 0,
          "stroke-linejoin": "miter",
          "stroke-linecap": "butt",
          fill: "#000000",
          stroke: "#000000",
          opacity: 1,
          x: 0,
          y: 0,
          cx: 0,
          cy: 0,
          width: 0,
          height: 0,
          r: 0,
          rx: 0,
          ry: 0,
          offset: 0,
          "stop-opacity": 1,
          "stop-color": "#000000",
          "font-size": 16,
          "font-family": "Helvetica, Arial, sans-serif",
          "text-anchor": "start"
        }
      };
      SVG.Color = function(color) {
        var match;
        this.r = 0;
        this.g = 0;
        this.b = 0;
        if (!color)
          return;
        if (typeof color === "string") {
          if (SVG.regex.isRgb.test(color)) {
            match = SVG.regex.rgb.exec(color.replace(SVG.regex.whitespace, ""));
            this.r = parseInt(match[1]);
            this.g = parseInt(match[2]);
            this.b = parseInt(match[3]);
          } else if (SVG.regex.isHex.test(color)) {
            match = SVG.regex.hex.exec(fullHex(color));
            this.r = parseInt(match[1], 16);
            this.g = parseInt(match[2], 16);
            this.b = parseInt(match[3], 16);
          }
        } else if (typeof color === "object") {
          this.r = color.r;
          this.g = color.g;
          this.b = color.b;
        }
      };
      SVG.extend(SVG.Color, {
        // Default to hex conversion
        toString: function() {
          return this.toHex();
        },
        toHex: function() {
          return "#" + compToHex(this.r) + compToHex(this.g) + compToHex(this.b);
        },
        toRgb: function() {
          return "rgb(" + [this.r, this.g, this.b].join() + ")";
        },
        brightness: function() {
          return this.r / 255 * 0.3 + this.g / 255 * 0.59 + this.b / 255 * 0.11;
        },
        morph: function(color) {
          this.destination = new SVG.Color(color);
          return this;
        },
        at: function(pos) {
          if (!this.destination)
            return this;
          pos = pos < 0 ? 0 : pos > 1 ? 1 : pos;
          return new SVG.Color({
            r: ~~(this.r + (this.destination.r - this.r) * pos),
            g: ~~(this.g + (this.destination.g - this.g) * pos),
            b: ~~(this.b + (this.destination.b - this.b) * pos)
          });
        }
      });
      SVG.Color.test = function(color) {
        color += "";
        return SVG.regex.isHex.test(color) || SVG.regex.isRgb.test(color);
      };
      SVG.Color.isRgb = function(color) {
        return color && typeof color.r == "number" && typeof color.g == "number" && typeof color.b == "number";
      };
      SVG.Color.isColor = function(color) {
        return SVG.Color.isRgb(color) || SVG.Color.test(color);
      };
      SVG.Array = function(array, fallback) {
        array = (array || []).valueOf();
        if (array.length == 0 && fallback)
          array = fallback.valueOf();
        this.value = this.parse(array);
      };
      SVG.extend(SVG.Array, {
        // Make array morphable
        morph: function(array) {
          this.destination = this.parse(array);
          if (this.value.length != this.destination.length) {
            var lastValue = this.value[this.value.length - 1], lastDestination = this.destination[this.destination.length - 1];
            while (this.value.length > this.destination.length)
              this.destination.push(lastDestination);
            while (this.value.length < this.destination.length)
              this.value.push(lastValue);
          }
          return this;
        },
        settle: function() {
          for (var i2 = 0, il2 = this.value.length, seen = []; i2 < il2; i2++)
            if (seen.indexOf(this.value[i2]) == -1)
              seen.push(this.value[i2]);
          return this.value = seen;
        },
        at: function(pos) {
          if (!this.destination)
            return this;
          for (var i2 = 0, il2 = this.value.length, array = []; i2 < il2; i2++)
            array.push(this.value[i2] + (this.destination[i2] - this.value[i2]) * pos);
          return new SVG.Array(array);
        },
        toString: function() {
          return this.value.join(" ");
        },
        valueOf: function() {
          return this.value;
        },
        parse: function(array) {
          array = array.valueOf();
          if (Array.isArray(array))
            return array;
          return this.split(array);
        },
        split: function(string) {
          return string.trim().split(SVG.regex.delimiter).map(parseFloat);
        },
        reverse: function() {
          this.value.reverse();
          return this;
        },
        clone: function() {
          var clone = new this.constructor();
          clone.value = array_clone(this.value);
          return clone;
        }
      });
      SVG.PointArray = function(array, fallback) {
        SVG.Array.call(this, array, fallback || [[0, 0]]);
      };
      SVG.PointArray.prototype = new SVG.Array();
      SVG.PointArray.prototype.constructor = SVG.PointArray;
      SVG.extend(SVG.PointArray, {
        // Convert array to string
        toString: function() {
          for (var i2 = 0, il2 = this.value.length, array = []; i2 < il2; i2++)
            array.push(this.value[i2].join(","));
          return array.join(" ");
        },
        toLine: function() {
          return {
            x1: this.value[0][0],
            y1: this.value[0][1],
            x2: this.value[1][0],
            y2: this.value[1][1]
          };
        },
        at: function(pos) {
          if (!this.destination)
            return this;
          for (var i2 = 0, il2 = this.value.length, array = []; i2 < il2; i2++)
            array.push([
              this.value[i2][0] + (this.destination[i2][0] - this.value[i2][0]) * pos,
              this.value[i2][1] + (this.destination[i2][1] - this.value[i2][1]) * pos
            ]);
          return new SVG.PointArray(array);
        },
        parse: function(array) {
          var points = [];
          array = array.valueOf();
          if (Array.isArray(array)) {
            if (Array.isArray(array[0])) {
              return array.map(function(el) {
                return el.slice();
              });
            } else if (array[0].x != null) {
              return array.map(function(el) {
                return [el.x, el.y];
              });
            }
          } else {
            array = array.trim().split(SVG.regex.delimiter).map(parseFloat);
          }
          if (array.length % 2 !== 0)
            array.pop();
          for (var i2 = 0, len = array.length; i2 < len; i2 = i2 + 2)
            points.push([array[i2], array[i2 + 1]]);
          return points;
        },
        move: function(x, y) {
          var box = this.bbox();
          x -= box.x;
          y -= box.y;
          if (!isNaN(x) && !isNaN(y))
            for (var i2 = this.value.length - 1; i2 >= 0; i2--)
              this.value[i2] = [this.value[i2][0] + x, this.value[i2][1] + y];
          return this;
        },
        size: function(width, height) {
          var i2, box = this.bbox();
          for (i2 = this.value.length - 1; i2 >= 0; i2--) {
            if (box.width)
              this.value[i2][0] = (this.value[i2][0] - box.x) * width / box.width + box.x;
            if (box.height)
              this.value[i2][1] = (this.value[i2][1] - box.y) * height / box.height + box.y;
          }
          return this;
        },
        bbox: function() {
          SVG.parser.poly.setAttribute("points", this.toString());
          return SVG.parser.poly.getBBox();
        }
      });
      var pathHandlers = {
        M: function(c, p, p0) {
          p.x = p0.x = c[0];
          p.y = p0.y = c[1];
          return ["M", p.x, p.y];
        },
        L: function(c, p) {
          p.x = c[0];
          p.y = c[1];
          return ["L", c[0], c[1]];
        },
        H: function(c, p) {
          p.x = c[0];
          return ["H", c[0]];
        },
        V: function(c, p) {
          p.y = c[0];
          return ["V", c[0]];
        },
        C: function(c, p) {
          p.x = c[4];
          p.y = c[5];
          return ["C", c[0], c[1], c[2], c[3], c[4], c[5]];
        },
        S: function(c, p) {
          p.x = c[2];
          p.y = c[3];
          return ["S", c[0], c[1], c[2], c[3]];
        },
        Q: function(c, p) {
          p.x = c[2];
          p.y = c[3];
          return ["Q", c[0], c[1], c[2], c[3]];
        },
        T: function(c, p) {
          p.x = c[0];
          p.y = c[1];
          return ["T", c[0], c[1]];
        },
        Z: function(c, p, p0) {
          p.x = p0.x;
          p.y = p0.y;
          return ["Z"];
        },
        A: function(c, p) {
          p.x = c[5];
          p.y = c[6];
          return ["A", c[0], c[1], c[2], c[3], c[4], c[5], c[6]];
        }
      };
      var mlhvqtcsa = "mlhvqtcsaz".split("");
      for (var i = 0, il = mlhvqtcsa.length; i < il; ++i) {
        pathHandlers[mlhvqtcsa[i]] = /* @__PURE__ */ function(i2) {
          return function(c, p, p0) {
            if (i2 == "H")
              c[0] = c[0] + p.x;
            else if (i2 == "V")
              c[0] = c[0] + p.y;
            else if (i2 == "A") {
              c[5] = c[5] + p.x, c[6] = c[6] + p.y;
            } else
              for (var j = 0, jl = c.length; j < jl; ++j) {
                c[j] = c[j] + (j % 2 ? p.y : p.x);
              }
            return pathHandlers[i2](c, p, p0);
          };
        }(mlhvqtcsa[i].toUpperCase());
      }
      SVG.PathArray = function(array, fallback) {
        SVG.Array.call(this, array, fallback || [["M", 0, 0]]);
      };
      SVG.PathArray.prototype = new SVG.Array();
      SVG.PathArray.prototype.constructor = SVG.PathArray;
      SVG.extend(SVG.PathArray, {
        // Convert array to string
        toString: function() {
          return arrayToString(this.value);
        },
        move: function(x, y) {
          var box = this.bbox();
          x -= box.x;
          y -= box.y;
          if (!isNaN(x) && !isNaN(y)) {
            for (var l, i2 = this.value.length - 1; i2 >= 0; i2--) {
              l = this.value[i2][0];
              if (l == "M" || l == "L" || l == "T") {
                this.value[i2][1] += x;
                this.value[i2][2] += y;
              } else if (l == "H") {
                this.value[i2][1] += x;
              } else if (l == "V") {
                this.value[i2][1] += y;
              } else if (l == "C" || l == "S" || l == "Q") {
                this.value[i2][1] += x;
                this.value[i2][2] += y;
                this.value[i2][3] += x;
                this.value[i2][4] += y;
                if (l == "C") {
                  this.value[i2][5] += x;
                  this.value[i2][6] += y;
                }
              } else if (l == "A") {
                this.value[i2][6] += x;
                this.value[i2][7] += y;
              }
            }
          }
          return this;
        },
        size: function(width, height) {
          var i2, l, box = this.bbox();
          for (i2 = this.value.length - 1; i2 >= 0; i2--) {
            l = this.value[i2][0];
            if (l == "M" || l == "L" || l == "T") {
              this.value[i2][1] = (this.value[i2][1] - box.x) * width / box.width + box.x;
              this.value[i2][2] = (this.value[i2][2] - box.y) * height / box.height + box.y;
            } else if (l == "H") {
              this.value[i2][1] = (this.value[i2][1] - box.x) * width / box.width + box.x;
            } else if (l == "V") {
              this.value[i2][1] = (this.value[i2][1] - box.y) * height / box.height + box.y;
            } else if (l == "C" || l == "S" || l == "Q") {
              this.value[i2][1] = (this.value[i2][1] - box.x) * width / box.width + box.x;
              this.value[i2][2] = (this.value[i2][2] - box.y) * height / box.height + box.y;
              this.value[i2][3] = (this.value[i2][3] - box.x) * width / box.width + box.x;
              this.value[i2][4] = (this.value[i2][4] - box.y) * height / box.height + box.y;
              if (l == "C") {
                this.value[i2][5] = (this.value[i2][5] - box.x) * width / box.width + box.x;
                this.value[i2][6] = (this.value[i2][6] - box.y) * height / box.height + box.y;
              }
            } else if (l == "A") {
              this.value[i2][1] = this.value[i2][1] * width / box.width;
              this.value[i2][2] = this.value[i2][2] * height / box.height;
              this.value[i2][6] = (this.value[i2][6] - box.x) * width / box.width + box.x;
              this.value[i2][7] = (this.value[i2][7] - box.y) * height / box.height + box.y;
            }
          }
          return this;
        },
        equalCommands: function(pathArray) {
          var i2, il2, equalCommands;
          pathArray = new SVG.PathArray(pathArray);
          equalCommands = this.value.length === pathArray.value.length;
          for (i2 = 0, il2 = this.value.length; equalCommands && i2 < il2; i2++) {
            equalCommands = this.value[i2][0] === pathArray.value[i2][0];
          }
          return equalCommands;
        },
        morph: function(pathArray) {
          pathArray = new SVG.PathArray(pathArray);
          if (this.equalCommands(pathArray)) {
            this.destination = pathArray;
          } else {
            this.destination = null;
          }
          return this;
        },
        at: function(pos) {
          if (!this.destination)
            return this;
          var sourceArray = this.value, destinationArray = this.destination.value, array = [], pathArray = new SVG.PathArray(), i2, il2, j, jl;
          for (i2 = 0, il2 = sourceArray.length; i2 < il2; i2++) {
            array[i2] = [sourceArray[i2][0]];
            for (j = 1, jl = sourceArray[i2].length; j < jl; j++) {
              array[i2][j] = sourceArray[i2][j] + (destinationArray[i2][j] - sourceArray[i2][j]) * pos;
            }
            if (array[i2][0] === "A") {
              array[i2][4] = +(array[i2][4] != 0);
              array[i2][5] = +(array[i2][5] != 0);
            }
          }
          pathArray.value = array;
          return pathArray;
        },
        parse: function(array) {
          if (array instanceof SVG.PathArray)
            return array.valueOf();
          var i2, x0, y0, s, seg, arr, x = 0, y = 0, paramCnt = { "M": 2, "L": 2, "H": 1, "V": 1, "C": 6, "S": 4, "Q": 4, "T": 2, "A": 7, "Z": 0 };
          if (typeof array == "string") {
            array = array.replace(SVG.regex.numbersWithDots, pathRegReplace).replace(SVG.regex.pathLetters, " $& ").replace(SVG.regex.hyphen, "$1 -").trim().split(SVG.regex.delimiter);
          } else {
            array = array.reduce(function(prev, curr) {
              return [].concat.call(prev, curr);
            }, []);
          }
          var arr = [], p = new SVG.Point(), p0 = new SVG.Point(), index = 0, len = array.length;
          do {
            if (SVG.regex.isPathLetter.test(array[index])) {
              s = array[index];
              ++index;
            } else if (s == "M") {
              s = "L";
            } else if (s == "m") {
              s = "l";
            }
            arr.push(
              pathHandlers[s].call(
                null,
                array.slice(index, index = index + paramCnt[s.toUpperCase()]).map(parseFloat),
                p,
                p0
              )
            );
          } while (len > index);
          return arr;
        },
        bbox: function() {
          SVG.parser.path.setAttribute("d", this.toString());
          return SVG.parser.path.getBBox();
        }
      });
      SVG.Number = SVG.invent({
        // Initialize
        create: function(value, unit) {
          this.value = 0;
          this.unit = unit || "";
          if (typeof value === "number") {
            this.value = isNaN(value) ? 0 : !isFinite(value) ? value < 0 ? -34e37 : 34e37 : value;
          } else if (typeof value === "string") {
            unit = value.match(SVG.regex.numberAndUnit);
            if (unit) {
              this.value = parseFloat(unit[1]);
              if (unit[5] == "%")
                this.value /= 100;
              else if (unit[5] == "s")
                this.value *= 1e3;
              this.unit = unit[5];
            }
          } else {
            if (value instanceof SVG.Number) {
              this.value = value.valueOf();
              this.unit = value.unit;
            }
          }
        },
        extend: {
          // Stringalize
          toString: function() {
            return (this.unit == "%" ? ~~(this.value * 1e8) / 1e6 : this.unit == "s" ? this.value / 1e3 : this.value) + this.unit;
          },
          toJSON: function() {
            return this.toString();
          },
          // Convert to primitive
          valueOf: function() {
            return this.value;
          },
          plus: function(number) {
            number = new SVG.Number(number);
            return new SVG.Number(this + number, this.unit || number.unit);
          },
          minus: function(number) {
            number = new SVG.Number(number);
            return new SVG.Number(this - number, this.unit || number.unit);
          },
          times: function(number) {
            number = new SVG.Number(number);
            return new SVG.Number(this * number, this.unit || number.unit);
          },
          divide: function(number) {
            number = new SVG.Number(number);
            return new SVG.Number(this / number, this.unit || number.unit);
          },
          to: function(unit) {
            var number = new SVG.Number(this);
            if (typeof unit === "string")
              number.unit = unit;
            return number;
          },
          morph: function(number) {
            this.destination = new SVG.Number(number);
            if (number.relative) {
              this.destination.value += this.value;
            }
            return this;
          },
          at: function(pos) {
            if (!this.destination)
              return this;
            return new SVG.Number(this.destination).minus(this).times(pos).plus(this);
          }
        }
      });
      SVG.Element = SVG.invent({
        // Initialize node
        create: function(node) {
          this._stroke = SVG.defaults.attrs.stroke;
          this._event = null;
          this._events = {};
          this.dom = {};
          if (this.node = node) {
            this.type = node.nodeName;
            this.node.instance = this;
            this._events = node._events || {};
            this._stroke = node.getAttribute("stroke") || this._stroke;
          }
        },
        extend: {
          // Move over x-axis
          x: function(x) {
            return this.attr("x", x);
          },
          y: function(y) {
            return this.attr("y", y);
          },
          cx: function(x) {
            return x == null ? this.x() + this.width() / 2 : this.x(x - this.width() / 2);
          },
          cy: function(y) {
            return y == null ? this.y() + this.height() / 2 : this.y(y - this.height() / 2);
          },
          move: function(x, y) {
            return this.x(x).y(y);
          },
          center: function(x, y) {
            return this.cx(x).cy(y);
          },
          width: function(width) {
            return this.attr("width", width);
          },
          height: function(height) {
            return this.attr("height", height);
          },
          size: function(width, height) {
            var p = proportionalSize(this, width, height);
            return this.width(new SVG.Number(p.width)).height(new SVG.Number(p.height));
          },
          clone: function(parent) {
            this.writeDataToDom();
            var clone = assignNewId(this.node.cloneNode(true));
            if (parent)
              parent.add(clone);
            else
              this.after(clone);
            return clone;
          },
          remove: function() {
            if (this.parent())
              this.parent().removeElement(this);
            return this;
          },
          replace: function(element) {
            this.after(element).remove();
            return element;
          },
          addTo: function(parent) {
            return parent.put(this);
          },
          putIn: function(parent) {
            return parent.add(this);
          },
          id: function(id) {
            return this.attr("id", id);
          },
          inside: function(x, y) {
            var box = this.bbox();
            return x > box.x && y > box.y && x < box.x + box.width && y < box.y + box.height;
          },
          show: function() {
            return this.style("display", "");
          },
          hide: function() {
            return this.style("display", "none");
          },
          visible: function() {
            return this.style("display") != "none";
          },
          toString: function() {
            return this.attr("id");
          },
          classes: function() {
            var attr = this.attr("class");
            return attr == null ? [] : attr.trim().split(SVG.regex.delimiter);
          },
          hasClass: function(name) {
            return this.classes().indexOf(name) != -1;
          },
          addClass: function(name) {
            if (!this.hasClass(name)) {
              var array = this.classes();
              array.push(name);
              this.attr("class", array.join(" "));
            }
            return this;
          },
          removeClass: function(name) {
            if (this.hasClass(name)) {
              this.attr("class", this.classes().filter(function(c) {
                return c != name;
              }).join(" "));
            }
            return this;
          },
          toggleClass: function(name) {
            return this.hasClass(name) ? this.removeClass(name) : this.addClass(name);
          },
          reference: function(attr) {
            return SVG.get(this.attr(attr));
          },
          parent: function(type) {
            var parent = this;
            if (!parent.node.parentNode)
              return null;
            parent = SVG.adopt(parent.node.parentNode);
            if (!type)
              return parent;
            while (parent && parent.node instanceof window2.SVGElement) {
              if (typeof type === "string" ? parent.matches(type) : parent instanceof type)
                return parent;
              if (!parent.node.parentNode || parent.node.parentNode.nodeName == "#document" || parent.node.parentNode.nodeName == "#document-fragment")
                return null;
              parent = SVG.adopt(parent.node.parentNode);
            }
          },
          doc: function() {
            return this instanceof SVG.Doc ? this : this.parent(SVG.Doc);
          },
          parents: function(type) {
            var parents = [], parent = this;
            do {
              parent = parent.parent(type);
              if (!parent || !parent.node)
                break;
              parents.push(parent);
            } while (parent.parent);
            return parents;
          },
          matches: function(selector) {
            return matches(this.node, selector);
          },
          native: function() {
            return this.node;
          },
          svg: function(svg) {
            var well = document2.createElement("svg");
            if (svg && this instanceof SVG.Parent) {
              well.innerHTML = "<svg>" + svg.replace(/\n/, "").replace(/<([\w:-]+)([^<]+?)\/>/g, "<$1$2></$1>") + "</svg>";
              for (var i2 = 0, il2 = well.firstChild.childNodes.length; i2 < il2; i2++)
                this.node.appendChild(well.firstChild.firstChild);
            } else {
              well.appendChild(svg = document2.createElement("svg"));
              this.writeDataToDom();
              svg.appendChild(this.node.cloneNode(true));
              return well.innerHTML.replace(/^<svg>/, "").replace(/<\/svg>$/, "");
            }
            return this;
          },
          writeDataToDom: function() {
            if (this.each || this.lines) {
              var fn = this.each ? this : this.lines();
              fn.each(function() {
                this.writeDataToDom();
              });
            }
            this.node.removeAttribute("svgjs:data");
            if (Object.keys(this.dom).length)
              this.node.setAttribute("svgjs:data", JSON.stringify(this.dom));
            return this;
          },
          setData: function(o) {
            this.dom = o;
            return this;
          },
          is: function(obj) {
            return is(this, obj);
          }
        }
      });
      SVG.easing = {
        "-": function(pos) {
          return pos;
        },
        "<>": function(pos) {
          return -Math.cos(pos * Math.PI) / 2 + 0.5;
        },
        ">": function(pos) {
          return Math.sin(pos * Math.PI / 2);
        },
        "<": function(pos) {
          return -Math.cos(pos * Math.PI / 2) + 1;
        }
      };
      SVG.morph = function(pos) {
        return function(from, to) {
          return new SVG.MorphObj(from, to).at(pos);
        };
      };
      SVG.Situation = SVG.invent({
        create: function(o) {
          this.init = false;
          this.reversed = false;
          this.reversing = false;
          this.duration = new SVG.Number(o.duration).valueOf();
          this.delay = new SVG.Number(o.delay).valueOf();
          this.start = +/* @__PURE__ */ new Date() + this.delay;
          this.finish = this.start + this.duration;
          this.ease = o.ease;
          this.loop = 0;
          this.loops = false;
          this.animations = {
            // functionToCall: [list of morphable objects]
            // e.g. move: [SVG.Number, SVG.Number]
          };
          this.attrs = {
            // holds all attributes which are not represented from a function svg.js provides
            // e.g. someAttr: SVG.Number
          };
          this.styles = {
            // holds all styles which should be animated
            // e.g. fill-color: SVG.Color
          };
          this.transforms = [
            // holds all transformations as transformation objects
            // e.g. [SVG.Rotate, SVG.Translate, SVG.Matrix]
          ];
          this.once = {
            // functions to fire at a specific position
            // e.g. "0.5": function foo(){}
          };
        }
      });
      SVG.FX = SVG.invent({
        create: function(element) {
          this._target = element;
          this.situations = [];
          this.active = false;
          this.situation = null;
          this.paused = false;
          this.lastPos = 0;
          this.pos = 0;
          this.absPos = 0;
          this._speed = 1;
        },
        extend: {
          /**
           * sets or returns the target of this animation
           * @param o object || number In case of Object it holds all parameters. In case of number its the duration of the animation
           * @param ease function || string Function which should be used for easing or easing keyword
           * @param delay Number indicating the delay before the animation starts
           * @return target || this
           */
          animate: function(o, ease, delay) {
            if (typeof o == "object") {
              ease = o.ease;
              delay = o.delay;
              o = o.duration;
            }
            var situation = new SVG.Situation({
              duration: o || 1e3,
              delay: delay || 0,
              ease: SVG.easing[ease || "-"] || ease
            });
            this.queue(situation);
            return this;
          },
          delay: function(delay) {
            var situation = new SVG.Situation({
              duration: delay,
              delay: 0,
              ease: SVG.easing["-"]
            });
            return this.queue(situation);
          },
          target: function(target) {
            if (target && target instanceof SVG.Element) {
              this._target = target;
              return this;
            }
            return this._target;
          },
          timeToAbsPos: function(timestamp) {
            return (timestamp - this.situation.start) / (this.situation.duration / this._speed);
          },
          absPosToTime: function(absPos) {
            return this.situation.duration / this._speed * absPos + this.situation.start;
          },
          startAnimFrame: function() {
            this.stopAnimFrame();
            this.animationFrame = window2.requestAnimationFrame(function() {
              this.step();
            }.bind(this));
          },
          stopAnimFrame: function() {
            window2.cancelAnimationFrame(this.animationFrame);
          },
          start: function() {
            if (!this.active && this.situation) {
              this.active = true;
              this.startCurrent();
            }
            return this;
          },
          startCurrent: function() {
            this.situation.start = +/* @__PURE__ */ new Date() + this.situation.delay / this._speed;
            this.situation.finish = this.situation.start + this.situation.duration / this._speed;
            return this.initAnimations().step();
          },
          queue: function(fn) {
            if (typeof fn == "function" || fn instanceof SVG.Situation)
              this.situations.push(fn);
            if (!this.situation)
              this.situation = this.situations.shift();
            return this;
          },
          dequeue: function() {
            this.stop();
            this.situation = this.situations.shift();
            if (this.situation) {
              if (this.situation instanceof SVG.Situation) {
                this.start();
              } else {
                this.situation.call(this);
              }
            }
            return this;
          },
          initAnimations: function() {
            var i2, j, source;
            var s = this.situation;
            if (s.init)
              return this;
            for (i2 in s.animations) {
              source = this.target()[i2]();
              if (!Array.isArray(source)) {
                source = [source];
              }
              if (!Array.isArray(s.animations[i2])) {
                s.animations[i2] = [s.animations[i2]];
              }
              for (j = source.length; j--; ) {
                if (s.animations[i2][j] instanceof SVG.Number)
                  source[j] = new SVG.Number(source[j]);
                s.animations[i2][j] = source[j].morph(s.animations[i2][j]);
              }
            }
            for (i2 in s.attrs) {
              s.attrs[i2] = new SVG.MorphObj(this.target().attr(i2), s.attrs[i2]);
            }
            for (i2 in s.styles) {
              s.styles[i2] = new SVG.MorphObj(this.target().style(i2), s.styles[i2]);
            }
            s.initialTransformation = this.target().matrixify();
            s.init = true;
            return this;
          },
          clearQueue: function() {
            this.situations = [];
            return this;
          },
          clearCurrent: function() {
            this.situation = null;
            return this;
          },
          stop: function(jumpToEnd, clearQueue) {
            var active = this.active;
            this.active = false;
            if (clearQueue) {
              this.clearQueue();
            }
            if (jumpToEnd && this.situation) {
              !active && this.startCurrent();
              this.atEnd();
            }
            this.stopAnimFrame();
            return this.clearCurrent();
          },
          reset: function() {
            if (this.situation) {
              var temp = this.situation;
              this.stop();
              this.situation = temp;
              this.atStart();
            }
            return this;
          },
          finish: function() {
            this.stop(true, false);
            while (this.dequeue().situation && this.stop(true, false))
              ;
            this.clearQueue().clearCurrent();
            return this;
          },
          atStart: function() {
            return this.at(0, true);
          },
          atEnd: function() {
            if (this.situation.loops === true) {
              this.situation.loops = this.situation.loop + 1;
            }
            if (typeof this.situation.loops == "number") {
              return this.at(this.situation.loops, true);
            } else {
              return this.at(1, true);
            }
          },
          at: function(pos, isAbsPos) {
            var durDivSpd = this.situation.duration / this._speed;
            this.absPos = pos;
            if (!isAbsPos) {
              if (this.situation.reversed)
                this.absPos = 1 - this.absPos;
              this.absPos += this.situation.loop;
            }
            this.situation.start = +/* @__PURE__ */ new Date() - this.absPos * durDivSpd;
            this.situation.finish = this.situation.start + durDivSpd;
            return this.step(true);
          },
          speed: function(speed) {
            if (speed === 0)
              return this.pause();
            if (speed) {
              this._speed = speed;
              return this.at(this.absPos, true);
            } else
              return this._speed;
          },
          loop: function(times, reverse) {
            var c = this.last();
            c.loops = times != null ? times : true;
            c.loop = 0;
            if (reverse)
              c.reversing = true;
            return this;
          },
          pause: function() {
            this.paused = true;
            this.stopAnimFrame();
            return this;
          },
          play: function() {
            if (!this.paused)
              return this;
            this.paused = false;
            return this.at(this.absPos, true);
          },
          reverse: function(reversed) {
            var c = this.last();
            if (typeof reversed == "undefined")
              c.reversed = !c.reversed;
            else
              c.reversed = reversed;
            return this;
          },
          progress: function(easeIt) {
            return easeIt ? this.situation.ease(this.pos) : this.pos;
          },
          after: function(fn) {
            var c = this.last(), wrapper = function wrapper2(e) {
              if (e.detail.situation == c) {
                fn.call(this, c);
                this.off("finished.fx", wrapper2);
              }
            };
            this.target().on("finished.fx", wrapper);
            return this._callStart();
          },
          during: function(fn) {
            var c = this.last(), wrapper = function(e) {
              if (e.detail.situation == c) {
                fn.call(this, e.detail.pos, SVG.morph(e.detail.pos), e.detail.eased, c);
              }
            };
            this.target().off("during.fx", wrapper).on("during.fx", wrapper);
            this.after(function() {
              this.off("during.fx", wrapper);
            });
            return this._callStart();
          },
          afterAll: function(fn) {
            var wrapper = function wrapper2(e) {
              fn.call(this);
              this.off("allfinished.fx", wrapper2);
            };
            this.target().off("allfinished.fx", wrapper).on("allfinished.fx", wrapper);
            return this._callStart();
          },
          duringAll: function(fn) {
            var wrapper = function(e) {
              fn.call(this, e.detail.pos, SVG.morph(e.detail.pos), e.detail.eased, e.detail.situation);
            };
            this.target().off("during.fx", wrapper).on("during.fx", wrapper);
            this.afterAll(function() {
              this.off("during.fx", wrapper);
            });
            return this._callStart();
          },
          last: function() {
            return this.situations.length ? this.situations[this.situations.length - 1] : this.situation;
          },
          add: function(method, args, type) {
            this.last()[type || "animations"][method] = args;
            return this._callStart();
          },
          step: function(ignoreTime) {
            if (!ignoreTime)
              this.absPos = this.timeToAbsPos(+/* @__PURE__ */ new Date());
            if (this.situation.loops !== false) {
              var absPos, absPosInt, lastLoop;
              absPos = Math.max(this.absPos, 0);
              absPosInt = Math.floor(absPos);
              if (this.situation.loops === true || absPosInt < this.situation.loops) {
                this.pos = absPos - absPosInt;
                lastLoop = this.situation.loop;
                this.situation.loop = absPosInt;
              } else {
                this.absPos = this.situation.loops;
                this.pos = 1;
                lastLoop = this.situation.loop - 1;
                this.situation.loop = this.situation.loops;
              }
              if (this.situation.reversing) {
                this.situation.reversed = this.situation.reversed != Boolean((this.situation.loop - lastLoop) % 2);
              }
            } else {
              this.absPos = Math.min(this.absPos, 1);
              this.pos = this.absPos;
            }
            if (this.pos < 0)
              this.pos = 0;
            if (this.situation.reversed)
              this.pos = 1 - this.pos;
            var eased = this.situation.ease(this.pos);
            for (var i2 in this.situation.once) {
              if (i2 > this.lastPos && i2 <= eased) {
                this.situation.once[i2].call(this.target(), this.pos, eased);
                delete this.situation.once[i2];
              }
            }
            if (this.active)
              this.target().fire("during", { pos: this.pos, eased, fx: this, situation: this.situation });
            if (!this.situation) {
              return this;
            }
            this.eachAt();
            if (this.pos == 1 && !this.situation.reversed || this.situation.reversed && this.pos == 0) {
              this.stopAnimFrame();
              this.target().fire("finished", { fx: this, situation: this.situation });
              if (!this.situations.length) {
                this.target().fire("allfinished");
                if (!this.situations.length) {
                  this.target().off(".fx");
                  this.active = false;
                }
              }
              if (this.active)
                this.dequeue();
              else
                this.clearCurrent();
            } else if (!this.paused && this.active) {
              this.startAnimFrame();
            }
            this.lastPos = eased;
            return this;
          },
          eachAt: function() {
            var i2, len, at, self = this, target = this.target(), s = this.situation;
            for (i2 in s.animations) {
              at = [].concat(s.animations[i2]).map(function(el) {
                return typeof el !== "string" && el.at ? el.at(s.ease(self.pos), self.pos) : el;
              });
              target[i2].apply(target, at);
            }
            for (i2 in s.attrs) {
              at = [i2].concat(s.attrs[i2]).map(function(el) {
                return typeof el !== "string" && el.at ? el.at(s.ease(self.pos), self.pos) : el;
              });
              target.attr.apply(target, at);
            }
            for (i2 in s.styles) {
              at = [i2].concat(s.styles[i2]).map(function(el) {
                return typeof el !== "string" && el.at ? el.at(s.ease(self.pos), self.pos) : el;
              });
              target.style.apply(target, at);
            }
            if (s.transforms.length) {
              at = s.initialTransformation;
              for (i2 = 0, len = s.transforms.length; i2 < len; i2++) {
                var a = s.transforms[i2];
                if (a instanceof SVG.Matrix) {
                  if (a.relative) {
                    at = at.multiply(new SVG.Matrix().morph(a).at(s.ease(this.pos)));
                  } else {
                    at = at.morph(a).at(s.ease(this.pos));
                  }
                  continue;
                }
                if (!a.relative)
                  a.undo(at.extract());
                at = at.multiply(a.at(s.ease(this.pos)));
              }
              target.matrix(at);
            }
            return this;
          },
          once: function(pos, fn, isEased) {
            var c = this.last();
            if (!isEased)
              pos = c.ease(pos);
            c.once[pos] = fn;
            return this;
          },
          _callStart: function() {
            setTimeout(function() {
              this.start();
            }.bind(this), 0);
            return this;
          }
        },
        parent: SVG.Element,
        construct: {
          // Get fx module or create a new one, then animate with given duration and ease
          animate: function(o, ease, delay) {
            return (this.fx || (this.fx = new SVG.FX(this))).animate(o, ease, delay);
          },
          delay: function(delay) {
            return (this.fx || (this.fx = new SVG.FX(this))).delay(delay);
          },
          stop: function(jumpToEnd, clearQueue) {
            if (this.fx)
              this.fx.stop(jumpToEnd, clearQueue);
            return this;
          },
          finish: function() {
            if (this.fx)
              this.fx.finish();
            return this;
          },
          pause: function() {
            if (this.fx)
              this.fx.pause();
            return this;
          },
          play: function() {
            if (this.fx)
              this.fx.play();
            return this;
          },
          speed: function(speed) {
            if (this.fx)
              if (speed == null)
                return this.fx.speed();
              else
                this.fx.speed(speed);
            return this;
          }
        }
      });
      SVG.MorphObj = SVG.invent({
        create: function(from, to) {
          if (SVG.Color.isColor(to))
            return new SVG.Color(from).morph(to);
          if (SVG.regex.delimiter.test(from)) {
            if (SVG.regex.pathLetters.test(from))
              return new SVG.PathArray(from).morph(to);
            else
              return new SVG.Array(from).morph(to);
          }
          if (SVG.regex.numberAndUnit.test(to))
            return new SVG.Number(from).morph(to);
          this.value = from;
          this.destination = to;
        },
        extend: {
          at: function(pos, real) {
            return real < 1 ? this.value : this.destination;
          },
          valueOf: function() {
            return this.value;
          }
        }
      });
      SVG.extend(SVG.FX, {
        // Add animatable attributes
        attr: function(a, v, relative) {
          if (typeof a == "object") {
            for (var key in a)
              this.attr(key, a[key]);
          } else {
            this.add(a, v, "attrs");
          }
          return this;
        },
        style: function(s, v) {
          if (typeof s == "object")
            for (var key in s)
              this.style(key, s[key]);
          else
            this.add(s, v, "styles");
          return this;
        },
        x: function(x, relative) {
          if (this.target() instanceof SVG.G) {
            this.transform({ x }, relative);
            return this;
          }
          var num = new SVG.Number(x);
          num.relative = relative;
          return this.add("x", num);
        },
        y: function(y, relative) {
          if (this.target() instanceof SVG.G) {
            this.transform({ y }, relative);
            return this;
          }
          var num = new SVG.Number(y);
          num.relative = relative;
          return this.add("y", num);
        },
        cx: function(x) {
          return this.add("cx", new SVG.Number(x));
        },
        cy: function(y) {
          return this.add("cy", new SVG.Number(y));
        },
        move: function(x, y) {
          return this.x(x).y(y);
        },
        center: function(x, y) {
          return this.cx(x).cy(y);
        },
        size: function(width, height) {
          if (this.target() instanceof SVG.Text) {
            this.attr("font-size", width);
          } else {
            var box;
            if (!width || !height) {
              box = this.target().bbox();
            }
            if (!width) {
              width = box.width / box.height * height;
            }
            if (!height) {
              height = box.height / box.width * width;
            }
            this.add("width", new SVG.Number(width)).add("height", new SVG.Number(height));
          }
          return this;
        },
        width: function(width) {
          return this.add("width", new SVG.Number(width));
        },
        height: function(height) {
          return this.add("height", new SVG.Number(height));
        },
        plot: function(a, b, c, d) {
          if (arguments.length == 4) {
            return this.plot([a, b, c, d]);
          }
          return this.add("plot", new (this.target()).morphArray(a));
        },
        leading: function(value) {
          return this.target().leading ? this.add("leading", new SVG.Number(value)) : this;
        },
        viewbox: function(x, y, width, height) {
          if (this.target() instanceof SVG.Container) {
            this.add("viewbox", new SVG.ViewBox(x, y, width, height));
          }
          return this;
        },
        update: function(o) {
          if (this.target() instanceof SVG.Stop) {
            if (typeof o == "number" || o instanceof SVG.Number) {
              return this.update({
                offset: arguments[0],
                color: arguments[1],
                opacity: arguments[2]
              });
            }
            if (o.opacity != null)
              this.attr("stop-opacity", o.opacity);
            if (o.color != null)
              this.attr("stop-color", o.color);
            if (o.offset != null)
              this.attr("offset", o.offset);
          }
          return this;
        }
      });
      SVG.Box = SVG.invent({
        create: function(x, y, width, height) {
          if (typeof x == "object" && !(x instanceof SVG.Element)) {
            return SVG.Box.call(this, x.left != null ? x.left : x.x, x.top != null ? x.top : x.y, x.width, x.height);
          } else if (arguments.length == 4) {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
          }
          fullBox(this);
        },
        extend: {
          // Merge rect box with another, return a new instance
          merge: function(box) {
            var b = new this.constructor();
            b.x = Math.min(this.x, box.x);
            b.y = Math.min(this.y, box.y);
            b.width = Math.max(this.x + this.width, box.x + box.width) - b.x;
            b.height = Math.max(this.y + this.height, box.y + box.height) - b.y;
            return fullBox(b);
          },
          transform: function(m) {
            var xMin = Infinity, xMax = -Infinity, yMin = Infinity, yMax = -Infinity, p, bbox;
            var pts = [
              new SVG.Point(this.x, this.y),
              new SVG.Point(this.x2, this.y),
              new SVG.Point(this.x, this.y2),
              new SVG.Point(this.x2, this.y2)
            ];
            pts.forEach(function(p2) {
              p2 = p2.transform(m);
              xMin = Math.min(xMin, p2.x);
              xMax = Math.max(xMax, p2.x);
              yMin = Math.min(yMin, p2.y);
              yMax = Math.max(yMax, p2.y);
            });
            bbox = new this.constructor();
            bbox.x = xMin;
            bbox.width = xMax - xMin;
            bbox.y = yMin;
            bbox.height = yMax - yMin;
            fullBox(bbox);
            return bbox;
          }
        }
      });
      SVG.BBox = SVG.invent({
        // Initialize
        create: function(element) {
          SVG.Box.apply(this, [].slice.call(arguments));
          if (element instanceof SVG.Element) {
            var box;
            try {
              if (!document2.documentElement.contains) {
                var topParent = element.node;
                while (topParent.parentNode) {
                  topParent = topParent.parentNode;
                }
                if (topParent != document2)
                  throw new Exception("Element not in the dom");
              } else {
                if (!document2.documentElement.contains(element.node))
                  throw new Exception("Element not in the dom");
              }
              box = element.node.getBBox();
            } catch (e) {
              if (element instanceof SVG.Shape) {
                var clone = element.clone(SVG.parser.draw.instance).show();
                box = clone.node.getBBox();
                clone.remove();
              } else {
                box = {
                  x: element.node.clientLeft,
                  y: element.node.clientTop,
                  width: element.node.clientWidth,
                  height: element.node.clientHeight
                };
              }
            }
            SVG.Box.call(this, box);
          }
        },
        inherit: SVG.Box,
        parent: SVG.Element,
        construct: {
          // Get bounding box
          bbox: function() {
            return new SVG.BBox(this);
          }
        }
      });
      SVG.BBox.prototype.constructor = SVG.BBox;
      SVG.extend(SVG.Element, {
        tbox: function() {
          console.warn("Use of TBox is deprecated and mapped to RBox. Use .rbox() instead.");
          return this.rbox(this.doc());
        }
      });
      SVG.RBox = SVG.invent({
        // Initialize
        create: function(element) {
          SVG.Box.apply(this, [].slice.call(arguments));
          if (element instanceof SVG.Element) {
            SVG.Box.call(this, element.node.getBoundingClientRect());
          }
        },
        inherit: SVG.Box,
        parent: SVG.Element,
        extend: {
          addOffset: function() {
            this.x += window2.pageXOffset;
            this.y += window2.pageYOffset;
            return this;
          }
        },
        construct: {
          // Get rect box
          rbox: function(el) {
            if (el)
              return new SVG.RBox(this).transform(el.screenCTM().inverse());
            return new SVG.RBox(this).addOffset();
          }
        }
      });
      SVG.RBox.prototype.constructor = SVG.RBox;
      SVG.Matrix = SVG.invent({
        // Initialize
        create: function(source) {
          var i2, base = arrayToMatrix([1, 0, 0, 1, 0, 0]);
          source = source instanceof SVG.Element ? source.matrixify() : typeof source === "string" ? arrayToMatrix(source.split(SVG.regex.delimiter).map(parseFloat)) : arguments.length == 6 ? arrayToMatrix([].slice.call(arguments)) : Array.isArray(source) ? arrayToMatrix(source) : typeof source === "object" ? source : base;
          for (i2 = abcdef.length - 1; i2 >= 0; --i2)
            this[abcdef[i2]] = source[abcdef[i2]] != null ? source[abcdef[i2]] : base[abcdef[i2]];
        },
        extend: {
          // Extract individual transformations
          extract: function() {
            var px = deltaTransformPoint(this, 0, 1), py = deltaTransformPoint(this, 1, 0), skewX = 180 / Math.PI * Math.atan2(px.y, px.x) - 90;
            return {
              // translation
              x: this.e,
              y: this.f,
              transformedX: (this.e * Math.cos(skewX * Math.PI / 180) + this.f * Math.sin(skewX * Math.PI / 180)) / Math.sqrt(this.a * this.a + this.b * this.b),
              transformedY: (this.f * Math.cos(skewX * Math.PI / 180) + this.e * Math.sin(-skewX * Math.PI / 180)) / Math.sqrt(this.c * this.c + this.d * this.d),
              skewX: -skewX,
              skewY: 180 / Math.PI * Math.atan2(py.y, py.x),
              scaleX: Math.sqrt(this.a * this.a + this.b * this.b),
              scaleY: Math.sqrt(this.c * this.c + this.d * this.d),
              rotation: skewX,
              a: this.a,
              b: this.b,
              c: this.c,
              d: this.d,
              e: this.e,
              f: this.f,
              matrix: new SVG.Matrix(this)
            };
          },
          clone: function() {
            return new SVG.Matrix(this);
          },
          morph: function(matrix) {
            this.destination = new SVG.Matrix(matrix);
            return this;
          },
          at: function(pos) {
            if (!this.destination)
              return this;
            var matrix = new SVG.Matrix({
              a: this.a + (this.destination.a - this.a) * pos,
              b: this.b + (this.destination.b - this.b) * pos,
              c: this.c + (this.destination.c - this.c) * pos,
              d: this.d + (this.destination.d - this.d) * pos,
              e: this.e + (this.destination.e - this.e) * pos,
              f: this.f + (this.destination.f - this.f) * pos
            });
            return matrix;
          },
          multiply: function(matrix) {
            return new SVG.Matrix(this.native().multiply(parseMatrix(matrix).native()));
          },
          inverse: function() {
            return new SVG.Matrix(this.native().inverse());
          },
          translate: function(x, y) {
            return new SVG.Matrix(this.native().translate(x || 0, y || 0));
          },
          scale: function(x, y, cx, cy) {
            if (arguments.length == 1) {
              y = x;
            } else if (arguments.length == 3) {
              cy = cx;
              cx = y;
              y = x;
            }
            return this.around(cx, cy, new SVG.Matrix(x, 0, 0, y, 0, 0));
          },
          rotate: function(r, cx, cy) {
            r = SVG.utils.radians(r);
            return this.around(cx, cy, new SVG.Matrix(Math.cos(r), Math.sin(r), -Math.sin(r), Math.cos(r), 0, 0));
          },
          flip: function(a, o) {
            return a == "x" ? this.scale(-1, 1, o, 0) : a == "y" ? this.scale(1, -1, 0, o) : this.scale(-1, -1, a, o != null ? o : a);
          },
          skew: function(x, y, cx, cy) {
            if (arguments.length == 1) {
              y = x;
            } else if (arguments.length == 3) {
              cy = cx;
              cx = y;
              y = x;
            }
            x = SVG.utils.radians(x);
            y = SVG.utils.radians(y);
            return this.around(cx, cy, new SVG.Matrix(1, Math.tan(y), Math.tan(x), 1, 0, 0));
          },
          skewX: function(x, cx, cy) {
            return this.skew(x, 0, cx, cy);
          },
          skewY: function(y, cx, cy) {
            return this.skew(0, y, cx, cy);
          },
          around: function(cx, cy, matrix) {
            return this.multiply(new SVG.Matrix(1, 0, 0, 1, cx || 0, cy || 0)).multiply(matrix).multiply(new SVG.Matrix(1, 0, 0, 1, -cx || 0, -cy || 0));
          },
          native: function() {
            var matrix = SVG.parser.native.createSVGMatrix();
            for (var i2 = abcdef.length - 1; i2 >= 0; i2--)
              matrix[abcdef[i2]] = this[abcdef[i2]];
            return matrix;
          },
          toString: function() {
            return "matrix(" + float32String(this.a) + "," + float32String(this.b) + "," + float32String(this.c) + "," + float32String(this.d) + "," + float32String(this.e) + "," + float32String(this.f) + ")";
          }
        },
        parent: SVG.Element,
        construct: {
          // Get current matrix
          ctm: function() {
            return new SVG.Matrix(this.node.getCTM());
          },
          // Get current screen matrix
          screenCTM: function() {
            if (this instanceof SVG.Nested) {
              var rect = this.rect(1, 1);
              var m = rect.node.getScreenCTM();
              rect.remove();
              return new SVG.Matrix(m);
            }
            return new SVG.Matrix(this.node.getScreenCTM());
          }
        }
      });
      SVG.Point = SVG.invent({
        // Initialize
        create: function(x, y) {
          var i2, source, base = { x: 0, y: 0 };
          source = Array.isArray(x) ? { x: x[0], y: x[1] } : typeof x === "object" ? { x: x.x, y: x.y } : x != null ? { x, y: y != null ? y : x } : base;
          this.x = source.x;
          this.y = source.y;
        },
        extend: {
          // Clone point
          clone: function() {
            return new SVG.Point(this);
          },
          morph: function(x, y) {
            this.destination = new SVG.Point(x, y);
            return this;
          },
          at: function(pos) {
            if (!this.destination)
              return this;
            var point = new SVG.Point({
              x: this.x + (this.destination.x - this.x) * pos,
              y: this.y + (this.destination.y - this.y) * pos
            });
            return point;
          },
          native: function() {
            var point = SVG.parser.native.createSVGPoint();
            point.x = this.x;
            point.y = this.y;
            return point;
          },
          transform: function(matrix) {
            return new SVG.Point(this.native().matrixTransform(matrix.native()));
          }
        }
      });
      SVG.extend(SVG.Element, {
        // Get point
        point: function(x, y) {
          return new SVG.Point(x, y).transform(this.screenCTM().inverse());
        }
      });
      SVG.extend(SVG.Element, {
        // Set svg element attribute
        attr: function(a, v, n) {
          if (a == null) {
            a = {};
            v = this.node.attributes;
            for (n = v.length - 1; n >= 0; n--)
              a[v[n].nodeName] = SVG.regex.isNumber.test(v[n].nodeValue) ? parseFloat(v[n].nodeValue) : v[n].nodeValue;
            return a;
          } else if (typeof a == "object") {
            for (v in a)
              this.attr(v, a[v]);
          } else if (v === null) {
            this.node.removeAttribute(a);
          } else if (v == null) {
            v = this.node.getAttribute(a);
            return v == null ? SVG.defaults.attrs[a] : SVG.regex.isNumber.test(v) ? parseFloat(v) : v;
          } else {
            if (a == "stroke-width")
              this.attr("stroke", parseFloat(v) > 0 ? this._stroke : null);
            else if (a == "stroke")
              this._stroke = v;
            if (a == "fill" || a == "stroke") {
              if (SVG.regex.isImage.test(v))
                v = this.doc().defs().image(v, 0, 0);
              if (v instanceof SVG.Image)
                v = this.doc().defs().pattern(0, 0, function() {
                  this.add(v);
                });
            }
            if (typeof v === "number")
              v = new SVG.Number(v);
            else if (SVG.Color.isColor(v))
              v = new SVG.Color(v);
            else if (Array.isArray(v))
              v = new SVG.Array(v);
            if (a == "leading") {
              if (this.leading)
                this.leading(v);
            } else {
              typeof n === "string" ? this.node.setAttributeNS(n, a, v.toString()) : this.node.setAttribute(a, v.toString());
            }
            if (this.rebuild && (a == "font-size" || a == "x"))
              this.rebuild(a, v);
          }
          return this;
        }
      });
      SVG.extend(SVG.Element, {
        // Add transformations
        transform: function(o, relative) {
          var target = this, matrix, bbox;
          if (typeof o !== "object") {
            matrix = new SVG.Matrix(target).extract();
            return typeof o === "string" ? matrix[o] : matrix;
          }
          matrix = new SVG.Matrix(target);
          relative = !!relative || !!o.relative;
          if (o.a != null) {
            matrix = relative ? (
              // relative
              matrix.multiply(new SVG.Matrix(o))
            ) : (
              // absolute
              new SVG.Matrix(o)
            );
          } else if (o.rotation != null) {
            ensureCentre(o, target);
            matrix = relative ? (
              // relative
              matrix.rotate(o.rotation, o.cx, o.cy)
            ) : (
              // absolute
              matrix.rotate(o.rotation - matrix.extract().rotation, o.cx, o.cy)
            );
          } else if (o.scale != null || o.scaleX != null || o.scaleY != null) {
            ensureCentre(o, target);
            o.scaleX = o.scale != null ? o.scale : o.scaleX != null ? o.scaleX : 1;
            o.scaleY = o.scale != null ? o.scale : o.scaleY != null ? o.scaleY : 1;
            if (!relative) {
              var e = matrix.extract();
              o.scaleX = o.scaleX * 1 / e.scaleX;
              o.scaleY = o.scaleY * 1 / e.scaleY;
            }
            matrix = matrix.scale(o.scaleX, o.scaleY, o.cx, o.cy);
          } else if (o.skew != null || o.skewX != null || o.skewY != null) {
            ensureCentre(o, target);
            o.skewX = o.skew != null ? o.skew : o.skewX != null ? o.skewX : 0;
            o.skewY = o.skew != null ? o.skew : o.skewY != null ? o.skewY : 0;
            if (!relative) {
              var e = matrix.extract();
              matrix = matrix.multiply(new SVG.Matrix().skew(e.skewX, e.skewY, o.cx, o.cy).inverse());
            }
            matrix = matrix.skew(o.skewX, o.skewY, o.cx, o.cy);
          } else if (o.flip) {
            if (o.flip == "x" || o.flip == "y") {
              o.offset = o.offset == null ? target.bbox()["c" + o.flip] : o.offset;
            } else {
              if (o.offset == null) {
                bbox = target.bbox();
                o.flip = bbox.cx;
                o.offset = bbox.cy;
              } else {
                o.flip = o.offset;
              }
            }
            matrix = new SVG.Matrix().flip(o.flip, o.offset);
          } else if (o.x != null || o.y != null) {
            if (relative) {
              matrix = matrix.translate(o.x, o.y);
            } else {
              if (o.x != null)
                matrix.e = o.x;
              if (o.y != null)
                matrix.f = o.y;
            }
          }
          return this.attr("transform", matrix);
        }
      });
      SVG.extend(SVG.FX, {
        transform: function(o, relative) {
          var target = this.target(), matrix, bbox;
          if (typeof o !== "object") {
            matrix = new SVG.Matrix(target).extract();
            return typeof o === "string" ? matrix[o] : matrix;
          }
          relative = !!relative || !!o.relative;
          if (o.a != null) {
            matrix = new SVG.Matrix(o);
          } else if (o.rotation != null) {
            ensureCentre(o, target);
            matrix = new SVG.Rotate(o.rotation, o.cx, o.cy);
          } else if (o.scale != null || o.scaleX != null || o.scaleY != null) {
            ensureCentre(o, target);
            o.scaleX = o.scale != null ? o.scale : o.scaleX != null ? o.scaleX : 1;
            o.scaleY = o.scale != null ? o.scale : o.scaleY != null ? o.scaleY : 1;
            matrix = new SVG.Scale(o.scaleX, o.scaleY, o.cx, o.cy);
          } else if (o.skewX != null || o.skewY != null) {
            ensureCentre(o, target);
            o.skewX = o.skewX != null ? o.skewX : 0;
            o.skewY = o.skewY != null ? o.skewY : 0;
            matrix = new SVG.Skew(o.skewX, o.skewY, o.cx, o.cy);
          } else if (o.flip) {
            if (o.flip == "x" || o.flip == "y") {
              o.offset = o.offset == null ? target.bbox()["c" + o.flip] : o.offset;
            } else {
              if (o.offset == null) {
                bbox = target.bbox();
                o.flip = bbox.cx;
                o.offset = bbox.cy;
              } else {
                o.flip = o.offset;
              }
            }
            matrix = new SVG.Matrix().flip(o.flip, o.offset);
          } else if (o.x != null || o.y != null) {
            matrix = new SVG.Translate(o.x, o.y);
          }
          if (!matrix)
            return this;
          matrix.relative = relative;
          this.last().transforms.push(matrix);
          return this._callStart();
        }
      });
      SVG.extend(SVG.Element, {
        // Reset all transformations
        untransform: function() {
          return this.attr("transform", null);
        },
        // merge the whole transformation chain into one matrix and returns it
        matrixify: function() {
          var matrix = (this.attr("transform") || "").split(SVG.regex.transforms).slice(0, -1).map(function(str) {
            var kv = str.trim().split("(");
            return [kv[0], kv[1].split(SVG.regex.delimiter).map(function(str2) {
              return parseFloat(str2);
            })];
          }).reduce(function(matrix2, transform) {
            if (transform[0] == "matrix")
              return matrix2.multiply(arrayToMatrix(transform[1]));
            return matrix2[transform[0]].apply(matrix2, transform[1]);
          }, new SVG.Matrix());
          return matrix;
        },
        // add an element to another parent without changing the visual representation on the screen
        toParent: function(parent) {
          if (this == parent)
            return this;
          var ctm = this.screenCTM();
          var pCtm = parent.screenCTM().inverse();
          this.addTo(parent).untransform().transform(pCtm.multiply(ctm));
          return this;
        },
        // same as above with parent equals root-svg
        toDoc: function() {
          return this.toParent(this.doc());
        }
      });
      SVG.Transformation = SVG.invent({
        create: function(source, inversed) {
          if (arguments.length > 1 && typeof inversed != "boolean") {
            return this.constructor.call(this, [].slice.call(arguments));
          }
          if (Array.isArray(source)) {
            for (var i2 = 0, len = this.arguments.length; i2 < len; ++i2) {
              this[this.arguments[i2]] = source[i2];
            }
          } else if (typeof source == "object") {
            for (var i2 = 0, len = this.arguments.length; i2 < len; ++i2) {
              this[this.arguments[i2]] = source[this.arguments[i2]];
            }
          }
          this.inversed = false;
          if (inversed === true) {
            this.inversed = true;
          }
        },
        extend: {
          arguments: [],
          method: "",
          at: function(pos) {
            var params = [];
            for (var i2 = 0, len = this.arguments.length; i2 < len; ++i2) {
              params.push(this[this.arguments[i2]]);
            }
            var m = this._undo || new SVG.Matrix();
            m = new SVG.Matrix().morph(SVG.Matrix.prototype[this.method].apply(m, params)).at(pos);
            return this.inversed ? m.inverse() : m;
          },
          undo: function(o) {
            for (var i2 = 0, len = this.arguments.length; i2 < len; ++i2) {
              o[this.arguments[i2]] = typeof this[this.arguments[i2]] == "undefined" ? 0 : o[this.arguments[i2]];
            }
            o.cx = this.cx;
            o.cy = this.cy;
            this._undo = new SVG[capitalize(this.method)](o, true).at(1);
            return this;
          }
        }
      });
      SVG.Translate = SVG.invent({
        parent: SVG.Matrix,
        inherit: SVG.Transformation,
        create: function(source, inversed) {
          this.constructor.apply(this, [].slice.call(arguments));
        },
        extend: {
          arguments: ["transformedX", "transformedY"],
          method: "translate"
        }
      });
      SVG.Rotate = SVG.invent({
        parent: SVG.Matrix,
        inherit: SVG.Transformation,
        create: function(source, inversed) {
          this.constructor.apply(this, [].slice.call(arguments));
        },
        extend: {
          arguments: ["rotation", "cx", "cy"],
          method: "rotate",
          at: function(pos) {
            var m = new SVG.Matrix().rotate(new SVG.Number().morph(this.rotation - (this._undo ? this._undo.rotation : 0)).at(pos), this.cx, this.cy);
            return this.inversed ? m.inverse() : m;
          },
          undo: function(o) {
            this._undo = o;
            return this;
          }
        }
      });
      SVG.Scale = SVG.invent({
        parent: SVG.Matrix,
        inherit: SVG.Transformation,
        create: function(source, inversed) {
          this.constructor.apply(this, [].slice.call(arguments));
        },
        extend: {
          arguments: ["scaleX", "scaleY", "cx", "cy"],
          method: "scale"
        }
      });
      SVG.Skew = SVG.invent({
        parent: SVG.Matrix,
        inherit: SVG.Transformation,
        create: function(source, inversed) {
          this.constructor.apply(this, [].slice.call(arguments));
        },
        extend: {
          arguments: ["skewX", "skewY", "cx", "cy"],
          method: "skew"
        }
      });
      SVG.extend(SVG.Element, {
        // Dynamic style generator
        style: function(s, v) {
          if (arguments.length == 0) {
            return this.node.style.cssText || "";
          } else if (arguments.length < 2) {
            if (typeof s == "object") {
              for (v in s)
                this.style(v, s[v]);
            } else if (SVG.regex.isCss.test(s)) {
              s = s.split(/\s*;\s*/).filter(function(e) {
                return !!e;
              }).map(function(e) {
                return e.split(/\s*:\s*/);
              });
              while (v = s.pop()) {
                this.style(v[0], v[1]);
              }
            } else {
              return this.node.style[camelCase(s)];
            }
          } else {
            this.node.style[camelCase(s)] = v === null || SVG.regex.isBlank.test(v) ? "" : v;
          }
          return this;
        }
      });
      SVG.Parent = SVG.invent({
        // Initialize node
        create: function(element) {
          this.constructor.call(this, element);
        },
        inherit: SVG.Element,
        extend: {
          // Returns all child elements
          children: function() {
            return SVG.utils.map(SVG.utils.filterSVGElements(this.node.childNodes), function(node) {
              return SVG.adopt(node);
            });
          },
          add: function(element, i2) {
            if (i2 == null)
              this.node.appendChild(element.node);
            else if (element.node != this.node.childNodes[i2])
              this.node.insertBefore(element.node, this.node.childNodes[i2]);
            return this;
          },
          put: function(element, i2) {
            this.add(element, i2);
            return element;
          },
          has: function(element) {
            return this.index(element) >= 0;
          },
          index: function(element) {
            return [].slice.call(this.node.childNodes).indexOf(element.node);
          },
          get: function(i2) {
            return SVG.adopt(this.node.childNodes[i2]);
          },
          first: function() {
            return this.get(0);
          },
          last: function() {
            return this.get(this.node.childNodes.length - 1);
          },
          each: function(block, deep) {
            var i2, il2, children = this.children();
            for (i2 = 0, il2 = children.length; i2 < il2; i2++) {
              if (children[i2] instanceof SVG.Element)
                block.apply(children[i2], [i2, children]);
              if (deep && children[i2] instanceof SVG.Container)
                children[i2].each(block, deep);
            }
            return this;
          },
          removeElement: function(element) {
            this.node.removeChild(element.node);
            return this;
          },
          clear: function() {
            while (this.node.hasChildNodes())
              this.node.removeChild(this.node.lastChild);
            delete this._defs;
            return this;
          },
          // Get defs
          defs: function() {
            return this.doc().defs();
          }
        }
      });
      SVG.extend(SVG.Parent, {
        ungroup: function(parent, depth) {
          if (depth === 0 || this instanceof SVG.Defs || this.node == SVG.parser.draw)
            return this;
          parent = parent || (this instanceof SVG.Doc ? this : this.parent(SVG.Parent));
          depth = depth || Infinity;
          this.each(function() {
            if (this instanceof SVG.Defs)
              return this;
            if (this instanceof SVG.Parent)
              return this.ungroup(parent, depth - 1);
            return this.toParent(parent);
          });
          this.node.firstChild || this.remove();
          return this;
        },
        flatten: function(parent, depth) {
          return this.ungroup(parent, depth);
        }
      });
      SVG.Container = SVG.invent({
        // Initialize node
        create: function(element) {
          this.constructor.call(this, element);
        },
        inherit: SVG.Parent
      });
      SVG.ViewBox = SVG.invent({
        create: function(source) {
          var i2, base = [0, 0, 0, 0];
          var x, y, width, height, box, view, we, he, wm = 1, hm = 1, reg = /[+-]?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?/gi;
          if (source instanceof SVG.Element) {
            we = source;
            he = source;
            view = (source.attr("viewBox") || "").match(reg);
            box = source.bbox;
            width = new SVG.Number(source.width());
            height = new SVG.Number(source.height());
            while (width.unit == "%") {
              wm *= width.value;
              width = new SVG.Number(we instanceof SVG.Doc ? we.parent().offsetWidth : we.parent().width());
              we = we.parent();
            }
            while (height.unit == "%") {
              hm *= height.value;
              height = new SVG.Number(he instanceof SVG.Doc ? he.parent().offsetHeight : he.parent().height());
              he = he.parent();
            }
            this.x = 0;
            this.y = 0;
            this.width = width * wm;
            this.height = height * hm;
            this.zoom = 1;
            if (view) {
              x = parseFloat(view[0]);
              y = parseFloat(view[1]);
              width = parseFloat(view[2]);
              height = parseFloat(view[3]);
              this.zoom = this.width / this.height > width / height ? this.height / height : this.width / width;
              this.x = x;
              this.y = y;
              this.width = width;
              this.height = height;
            }
          } else {
            source = typeof source === "string" ? source.match(reg).map(function(el) {
              return parseFloat(el);
            }) : Array.isArray(source) ? source : typeof source == "object" ? [source.x, source.y, source.width, source.height] : arguments.length == 4 ? [].slice.call(arguments) : base;
            this.x = source[0];
            this.y = source[1];
            this.width = source[2];
            this.height = source[3];
          }
        },
        extend: {
          toString: function() {
            return this.x + " " + this.y + " " + this.width + " " + this.height;
          },
          morph: function(x, y, width, height) {
            this.destination = new SVG.ViewBox(x, y, width, height);
            return this;
          },
          at: function(pos) {
            if (!this.destination)
              return this;
            return new SVG.ViewBox([
              this.x + (this.destination.x - this.x) * pos,
              this.y + (this.destination.y - this.y) * pos,
              this.width + (this.destination.width - this.width) * pos,
              this.height + (this.destination.height - this.height) * pos
            ]);
          }
        },
        parent: SVG.Container,
        construct: {
          // get/set viewbox
          viewbox: function(x, y, width, height) {
            if (arguments.length == 0)
              return new SVG.ViewBox(this);
            return this.attr("viewBox", new SVG.ViewBox(x, y, width, height));
          }
        }
      });
      [
        "click",
        "dblclick",
        "mousedown",
        "mouseup",
        "mouseover",
        "mouseout",
        "mousemove",
        "mouseenter",
        "mouseleave",
        "touchstart",
        "touchmove",
        "touchleave",
        "touchend",
        "touchcancel"
      ].forEach(function(event) {
        SVG.Element.prototype[event] = function(f) {
          if (f == null) {
            SVG.off(this, event);
          } else {
            SVG.on(this, event, f);
          }
          return this;
        };
      });
      SVG.listenerId = 0;
      SVG.on = function(node, events, listener, binding, options) {
        var l = listener.bind(binding || node);
        var n = node instanceof SVG.Element ? node.node : node;
        n.instance = n.instance || { _events: {} };
        var bag = n.instance._events;
        if (!listener._svgjsListenerId) {
          listener._svgjsListenerId = ++SVG.listenerId;
        }
        events.split(SVG.regex.delimiter).forEach(function(event) {
          var ev = event.split(".")[0];
          var ns = event.split(".")[1] || "*";
          bag[ev] = bag[ev] || {};
          bag[ev][ns] = bag[ev][ns] || {};
          bag[ev][ns][listener._svgjsListenerId] = l;
          n.addEventListener(ev, l, options || false);
        });
      };
      SVG.off = function(node, events, listener, options) {
        var n = node instanceof SVG.Element ? node.node : node;
        if (!n.instance)
          return;
        if (typeof listener === "function") {
          listener = listener._svgjsListenerId;
          if (!listener)
            return;
        }
        var bag = n.instance._events;
        (events || "").split(SVG.regex.delimiter).forEach(function(event) {
          var ev = event && event.split(".")[0];
          var ns = event && event.split(".")[1];
          var namespace, l;
          if (listener) {
            if (bag[ev] && bag[ev][ns || "*"]) {
              n.removeEventListener(ev, bag[ev][ns || "*"][listener], options || false);
              delete bag[ev][ns || "*"][listener];
            }
          } else if (ev && ns) {
            if (bag[ev] && bag[ev][ns]) {
              for (l in bag[ev][ns]) {
                SVG.off(n, [ev, ns].join("."), l);
              }
              delete bag[ev][ns];
            }
          } else if (ns) {
            for (event in bag) {
              for (namespace in bag[event]) {
                if (ns === namespace) {
                  SVG.off(n, [event, ns].join("."));
                }
              }
            }
          } else if (ev) {
            if (bag[ev]) {
              for (namespace in bag[ev]) {
                SVG.off(n, [ev, namespace].join("."));
              }
              delete bag[ev];
            }
          } else {
            for (event in bag) {
              SVG.off(n, event);
            }
            n.instance._events = {};
          }
        });
      };
      SVG.extend(SVG.Element, {
        // Bind given event to listener
        on: function(event, listener, binding, options) {
          SVG.on(this, event, listener, binding, options);
          return this;
        },
        // Unbind event from listener
        off: function(event, listener) {
          SVG.off(this.node, event, listener);
          return this;
        },
        fire: function(event, data) {
          if (event instanceof window2.Event) {
            this.node.dispatchEvent(event);
          } else {
            this.node.dispatchEvent(event = new SVG.CustomEvent(event, { detail: data, cancelable: true }));
          }
          this._event = event;
          return this;
        },
        event: function() {
          return this._event;
        }
      });
      SVG.Defs = SVG.invent({
        // Initialize node
        create: "defs",
        inherit: SVG.Container
      });
      SVG.G = SVG.invent({
        // Initialize node
        create: "g",
        inherit: SVG.Container,
        extend: {
          // Move over x-axis
          x: function(x) {
            return x == null ? this.transform("x") : this.transform({ x: x - this.x() }, true);
          },
          y: function(y) {
            return y == null ? this.transform("y") : this.transform({ y: y - this.y() }, true);
          },
          cx: function(x) {
            return x == null ? this.gbox().cx : this.x(x - this.gbox().width / 2);
          },
          cy: function(y) {
            return y == null ? this.gbox().cy : this.y(y - this.gbox().height / 2);
          },
          gbox: function() {
            var bbox = this.bbox(), trans = this.transform();
            bbox.x += trans.x;
            bbox.x2 += trans.x;
            bbox.cx += trans.x;
            bbox.y += trans.y;
            bbox.y2 += trans.y;
            bbox.cy += trans.y;
            return bbox;
          }
        },
        construct: {
          // Create a group element
          group: function() {
            return this.put(new SVG.G());
          }
        }
      });
      SVG.Doc = SVG.invent({
        // Initialize node
        create: function(element) {
          if (element) {
            element = typeof element == "string" ? document2.getElementById(element) : element;
            if (element.nodeName == "svg") {
              this.constructor.call(this, element);
            } else {
              this.constructor.call(this, SVG.create("svg"));
              element.appendChild(this.node);
              this.size("100%", "100%");
            }
            this.namespace().defs();
          }
        },
        inherit: SVG.Container,
        extend: {
          // Add namespaces
          namespace: function() {
            return this.attr({ xmlns: SVG.ns, version: "1.1" }).attr("xmlns:xlink", SVG.xlink, SVG.xmlns).attr("xmlns:svgjs", SVG.svgjs, SVG.xmlns);
          },
          defs: function() {
            if (!this._defs) {
              var defs;
              if (defs = this.node.getElementsByTagName("defs")[0])
                this._defs = SVG.adopt(defs);
              else
                this._defs = new SVG.Defs();
              this.node.appendChild(this._defs.node);
            }
            return this._defs;
          },
          parent: function() {
            if (!this.node.parentNode || this.node.parentNode.nodeName == "#document" || this.node.parentNode.nodeName == "#document-fragment")
              return null;
            return this.node.parentNode;
          },
          spof: function() {
            var pos = this.node.getScreenCTM();
            if (pos)
              this.style("left", -pos.e % 1 + "px").style("top", -pos.f % 1 + "px");
            return this;
          },
          remove: function() {
            if (this.parent()) {
              this.parent().removeChild(this.node);
            }
            return this;
          },
          clear: function() {
            while (this.node.hasChildNodes())
              this.node.removeChild(this.node.lastChild);
            delete this._defs;
            if (!SVG.parser.draw.parentNode)
              this.node.appendChild(SVG.parser.draw);
            return this;
          },
          clone: function(parent) {
            this.writeDataToDom();
            var node = this.node;
            var clone = assignNewId(node.cloneNode(true));
            if (parent) {
              (parent.node || parent).appendChild(clone.node);
            } else {
              node.parentNode.insertBefore(clone.node, node.nextSibling);
            }
            return clone;
          }
        }
      });
      SVG.extend(SVG.Element, {
        // Get all siblings, including myself
        siblings: function() {
          return this.parent().children();
        },
        position: function() {
          return this.parent().index(this);
        },
        next: function() {
          return this.siblings()[this.position() + 1];
        },
        previous: function() {
          return this.siblings()[this.position() - 1];
        },
        forward: function() {
          var i2 = this.position() + 1, p = this.parent();
          p.removeElement(this).add(this, i2);
          if (p instanceof SVG.Doc)
            p.node.appendChild(p.defs().node);
          return this;
        },
        backward: function() {
          var i2 = this.position();
          if (i2 > 0)
            this.parent().removeElement(this).add(this, i2 - 1);
          return this;
        },
        front: function() {
          var p = this.parent();
          p.node.appendChild(this.node);
          if (p instanceof SVG.Doc)
            p.node.appendChild(p.defs().node);
          return this;
        },
        back: function() {
          if (this.position() > 0)
            this.parent().removeElement(this).add(this, 0);
          return this;
        },
        before: function(element) {
          element.remove();
          var i2 = this.position();
          this.parent().add(element, i2);
          return this;
        },
        after: function(element) {
          element.remove();
          var i2 = this.position();
          this.parent().add(element, i2 + 1);
          return this;
        }
      });
      SVG.Mask = SVG.invent({
        // Initialize node
        create: function() {
          this.constructor.call(this, SVG.create("mask"));
          this.targets = [];
        },
        inherit: SVG.Container,
        extend: {
          // Unmask all masked elements and remove itself
          remove: function() {
            for (var i2 = this.targets.length - 1; i2 >= 0; i2--)
              if (this.targets[i2])
                this.targets[i2].unmask();
            this.targets = [];
            SVG.Element.prototype.remove.call(this);
            return this;
          }
        },
        construct: {
          // Create masking element
          mask: function() {
            return this.defs().put(new SVG.Mask());
          }
        }
      });
      SVG.extend(SVG.Element, {
        // Distribute mask to svg element
        maskWith: function(element) {
          this.masker = element instanceof SVG.Mask ? element : this.parent().mask().add(element);
          this.masker.targets.push(this);
          return this.attr("mask", 'url("#' + this.masker.attr("id") + '")');
        },
        unmask: function() {
          delete this.masker;
          return this.attr("mask", null);
        }
      });
      SVG.ClipPath = SVG.invent({
        // Initialize node
        create: function() {
          this.constructor.call(this, SVG.create("clipPath"));
          this.targets = [];
        },
        inherit: SVG.Container,
        extend: {
          // Unclip all clipped elements and remove itself
          remove: function() {
            for (var i2 = this.targets.length - 1; i2 >= 0; i2--)
              if (this.targets[i2])
                this.targets[i2].unclip();
            this.targets = [];
            this.parent().removeElement(this);
            return this;
          }
        },
        construct: {
          // Create clipping element
          clip: function() {
            return this.defs().put(new SVG.ClipPath());
          }
        }
      });
      SVG.extend(SVG.Element, {
        // Distribute clipPath to svg element
        clipWith: function(element) {
          this.clipper = element instanceof SVG.ClipPath ? element : this.parent().clip().add(element);
          this.clipper.targets.push(this);
          return this.attr("clip-path", 'url("#' + this.clipper.attr("id") + '")');
        },
        unclip: function() {
          delete this.clipper;
          return this.attr("clip-path", null);
        }
      });
      SVG.Gradient = SVG.invent({
        // Initialize node
        create: function(type) {
          this.constructor.call(this, SVG.create(type + "Gradient"));
          this.type = type;
        },
        inherit: SVG.Container,
        extend: {
          // Add a color stop
          at: function(offset, color, opacity) {
            return this.put(new SVG.Stop()).update(offset, color, opacity);
          },
          update: function(block) {
            this.clear();
            if (typeof block == "function")
              block.call(this, this);
            return this;
          },
          fill: function() {
            return "url(#" + this.id() + ")";
          },
          toString: function() {
            return this.fill();
          },
          attr: function(a, b, c) {
            if (a == "transform")
              a = "gradientTransform";
            return SVG.Container.prototype.attr.call(this, a, b, c);
          }
        },
        construct: {
          // Create gradient element in defs
          gradient: function(type, block) {
            return this.defs().gradient(type, block);
          }
        }
      });
      SVG.extend(SVG.Gradient, SVG.FX, {
        // From position
        from: function(x, y) {
          return (this._target || this).type == "radial" ? this.attr({ fx: new SVG.Number(x), fy: new SVG.Number(y) }) : this.attr({ x1: new SVG.Number(x), y1: new SVG.Number(y) });
        },
        to: function(x, y) {
          return (this._target || this).type == "radial" ? this.attr({ cx: new SVG.Number(x), cy: new SVG.Number(y) }) : this.attr({ x2: new SVG.Number(x), y2: new SVG.Number(y) });
        }
      });
      SVG.extend(SVG.Defs, {
        // define gradient
        gradient: function(type, block) {
          return this.put(new SVG.Gradient(type)).update(block);
        }
      });
      SVG.Stop = SVG.invent({
        // Initialize node
        create: "stop",
        inherit: SVG.Element,
        extend: {
          // add color stops
          update: function(o) {
            if (typeof o == "number" || o instanceof SVG.Number) {
              o = {
                offset: arguments[0],
                color: arguments[1],
                opacity: arguments[2]
              };
            }
            if (o.opacity != null)
              this.attr("stop-opacity", o.opacity);
            if (o.color != null)
              this.attr("stop-color", o.color);
            if (o.offset != null)
              this.attr("offset", new SVG.Number(o.offset));
            return this;
          }
        }
      });
      SVG.Pattern = SVG.invent({
        // Initialize node
        create: "pattern",
        inherit: SVG.Container,
        extend: {
          // Return the fill id
          fill: function() {
            return "url(#" + this.id() + ")";
          },
          update: function(block) {
            this.clear();
            if (typeof block == "function")
              block.call(this, this);
            return this;
          },
          toString: function() {
            return this.fill();
          },
          attr: function(a, b, c) {
            if (a == "transform")
              a = "patternTransform";
            return SVG.Container.prototype.attr.call(this, a, b, c);
          }
        },
        construct: {
          // Create pattern element in defs
          pattern: function(width, height, block) {
            return this.defs().pattern(width, height, block);
          }
        }
      });
      SVG.extend(SVG.Defs, {
        // Define gradient
        pattern: function(width, height, block) {
          return this.put(new SVG.Pattern()).update(block).attr({
            x: 0,
            y: 0,
            width,
            height,
            patternUnits: "userSpaceOnUse"
          });
        }
      });
      SVG.Shape = SVG.invent({
        // Initialize node
        create: function(element) {
          this.constructor.call(this, element);
        },
        inherit: SVG.Element
      });
      SVG.Bare = SVG.invent({
        // Initialize
        create: function(element, inherit) {
          this.constructor.call(this, SVG.create(element));
          if (inherit) {
            for (var method in inherit.prototype)
              if (typeof inherit.prototype[method] === "function")
                this[method] = inherit.prototype[method];
          }
        },
        inherit: SVG.Element,
        extend: {
          // Insert some plain text
          words: function(text) {
            while (this.node.hasChildNodes())
              this.node.removeChild(this.node.lastChild);
            this.node.appendChild(document2.createTextNode(text));
            return this;
          }
        }
      });
      SVG.extend(SVG.Parent, {
        // Create an element that is not described by SVG.js
        element: function(element, inherit) {
          return this.put(new SVG.Bare(element, inherit));
        }
      });
      SVG.Symbol = SVG.invent({
        // Initialize node
        create: "symbol",
        inherit: SVG.Container,
        construct: {
          // create symbol
          symbol: function() {
            return this.put(new SVG.Symbol());
          }
        }
      });
      SVG.Use = SVG.invent({
        // Initialize node
        create: "use",
        inherit: SVG.Shape,
        extend: {
          // Use element as a reference
          element: function(element, file) {
            return this.attr("href", (file || "") + "#" + element, SVG.xlink);
          }
        },
        construct: {
          // Create a use element
          use: function(element, file) {
            return this.put(new SVG.Use()).element(element, file);
          }
        }
      });
      SVG.Rect = SVG.invent({
        // Initialize node
        create: "rect",
        inherit: SVG.Shape,
        construct: {
          // Create a rect element
          rect: function(width, height) {
            return this.put(new SVG.Rect()).size(width, height);
          }
        }
      });
      SVG.Circle = SVG.invent({
        // Initialize node
        create: "circle",
        inherit: SVG.Shape,
        construct: {
          // Create circle element, based on ellipse
          circle: function(size2) {
            return this.put(new SVG.Circle()).rx(new SVG.Number(size2).divide(2)).move(0, 0);
          }
        }
      });
      SVG.extend(SVG.Circle, SVG.FX, {
        // Radius x value
        rx: function(rx) {
          return this.attr("r", rx);
        },
        ry: function(ry) {
          return this.rx(ry);
        }
      });
      SVG.Ellipse = SVG.invent({
        // Initialize node
        create: "ellipse",
        inherit: SVG.Shape,
        construct: {
          // Create an ellipse
          ellipse: function(width, height) {
            return this.put(new SVG.Ellipse()).size(width, height).move(0, 0);
          }
        }
      });
      SVG.extend(SVG.Ellipse, SVG.Rect, SVG.FX, {
        // Radius x value
        rx: function(rx) {
          return this.attr("rx", rx);
        },
        ry: function(ry) {
          return this.attr("ry", ry);
        }
      });
      SVG.extend(SVG.Circle, SVG.Ellipse, {
        // Move over x-axis
        x: function(x) {
          return x == null ? this.cx() - this.rx() : this.cx(x + this.rx());
        },
        y: function(y) {
          return y == null ? this.cy() - this.ry() : this.cy(y + this.ry());
        },
        cx: function(x) {
          return x == null ? this.attr("cx") : this.attr("cx", x);
        },
        cy: function(y) {
          return y == null ? this.attr("cy") : this.attr("cy", y);
        },
        width: function(width) {
          return width == null ? this.rx() * 2 : this.rx(new SVG.Number(width).divide(2));
        },
        height: function(height) {
          return height == null ? this.ry() * 2 : this.ry(new SVG.Number(height).divide(2));
        },
        size: function(width, height) {
          var p = proportionalSize(this, width, height);
          return this.rx(new SVG.Number(p.width).divide(2)).ry(new SVG.Number(p.height).divide(2));
        }
      });
      SVG.Line = SVG.invent({
        // Initialize node
        create: "line",
        inherit: SVG.Shape,
        extend: {
          // Get array
          array: function() {
            return new SVG.PointArray([
              [this.attr("x1"), this.attr("y1")],
              [this.attr("x2"), this.attr("y2")]
            ]);
          },
          plot: function(x1, y1, x2, y2) {
            if (x1 == null)
              return this.array();
            else if (typeof y1 !== "undefined")
              x1 = { x1, y1, x2, y2 };
            else
              x1 = new SVG.PointArray(x1).toLine();
            return this.attr(x1);
          },
          move: function(x, y) {
            return this.attr(this.array().move(x, y).toLine());
          },
          size: function(width, height) {
            var p = proportionalSize(this, width, height);
            return this.attr(this.array().size(p.width, p.height).toLine());
          }
        },
        construct: {
          // Create a line element
          line: function(x1, y1, x2, y2) {
            return SVG.Line.prototype.plot.apply(
              this.put(new SVG.Line()),
              x1 != null ? [x1, y1, x2, y2] : [0, 0, 0, 0]
            );
          }
        }
      });
      SVG.Polyline = SVG.invent({
        // Initialize node
        create: "polyline",
        inherit: SVG.Shape,
        construct: {
          // Create a wrapped polyline element
          polyline: function(p) {
            return this.put(new SVG.Polyline()).plot(p || new SVG.PointArray());
          }
        }
      });
      SVG.Polygon = SVG.invent({
        // Initialize node
        create: "polygon",
        inherit: SVG.Shape,
        construct: {
          // Create a wrapped polygon element
          polygon: function(p) {
            return this.put(new SVG.Polygon()).plot(p || new SVG.PointArray());
          }
        }
      });
      SVG.extend(SVG.Polyline, SVG.Polygon, {
        // Get array
        array: function() {
          return this._array || (this._array = new SVG.PointArray(this.attr("points")));
        },
        plot: function(p) {
          return p == null ? this.array() : this.clear().attr("points", typeof p == "string" ? p : this._array = new SVG.PointArray(p));
        },
        clear: function() {
          delete this._array;
          return this;
        },
        move: function(x, y) {
          return this.attr("points", this.array().move(x, y));
        },
        size: function(width, height) {
          var p = proportionalSize(this, width, height);
          return this.attr("points", this.array().size(p.width, p.height));
        }
      });
      SVG.extend(SVG.Line, SVG.Polyline, SVG.Polygon, {
        // Define morphable array
        morphArray: SVG.PointArray,
        x: function(x) {
          return x == null ? this.bbox().x : this.move(x, this.bbox().y);
        },
        y: function(y) {
          return y == null ? this.bbox().y : this.move(this.bbox().x, y);
        },
        width: function(width) {
          var b = this.bbox();
          return width == null ? b.width : this.size(width, b.height);
        },
        height: function(height) {
          var b = this.bbox();
          return height == null ? b.height : this.size(b.width, height);
        }
      });
      SVG.Path = SVG.invent({
        // Initialize node
        create: "path",
        inherit: SVG.Shape,
        extend: {
          // Define morphable array
          morphArray: SVG.PathArray,
          array: function() {
            return this._array || (this._array = new SVG.PathArray(this.attr("d")));
          },
          plot: function(d) {
            return d == null ? this.array() : this.clear().attr("d", typeof d == "string" ? d : this._array = new SVG.PathArray(d));
          },
          clear: function() {
            delete this._array;
            return this;
          },
          move: function(x, y) {
            return this.attr("d", this.array().move(x, y));
          },
          x: function(x) {
            return x == null ? this.bbox().x : this.move(x, this.bbox().y);
          },
          y: function(y) {
            return y == null ? this.bbox().y : this.move(this.bbox().x, y);
          },
          size: function(width, height) {
            var p = proportionalSize(this, width, height);
            return this.attr("d", this.array().size(p.width, p.height));
          },
          width: function(width) {
            return width == null ? this.bbox().width : this.size(width, this.bbox().height);
          },
          height: function(height) {
            return height == null ? this.bbox().height : this.size(this.bbox().width, height);
          }
        },
        construct: {
          // Create a wrapped path element
          path: function(d) {
            return this.put(new SVG.Path()).plot(d || new SVG.PathArray());
          }
        }
      });
      SVG.Image = SVG.invent({
        // Initialize node
        create: "image",
        inherit: SVG.Shape,
        extend: {
          // (re)load image
          load: function(url) {
            if (!url)
              return this;
            var self = this, img = new window2.Image();
            SVG.on(img, "load", function() {
              SVG.off(img);
              var p = self.parent(SVG.Pattern);
              if (p === null)
                return;
              if (self.width() == 0 && self.height() == 0)
                self.size(img.width, img.height);
              if (p && p.width() == 0 && p.height() == 0)
                p.size(self.width(), self.height());
              if (typeof self._loaded === "function")
                self._loaded.call(self, {
                  width: img.width,
                  height: img.height,
                  ratio: img.width / img.height,
                  url
                });
            });
            SVG.on(img, "error", function(e) {
              SVG.off(img);
              if (typeof self._error === "function") {
                self._error.call(self, e);
              }
            });
            return this.attr("href", img.src = this.src = url, SVG.xlink);
          },
          loaded: function(loaded) {
            this._loaded = loaded;
            return this;
          },
          error: function(error) {
            this._error = error;
            return this;
          }
        },
        construct: {
          // create image element, load image and set its size
          image: function(source, width, height) {
            return this.put(new SVG.Image()).load(source).size(width || 0, height || width || 0);
          }
        }
      });
      SVG.Text = SVG.invent({
        // Initialize node
        create: function() {
          this.constructor.call(this, SVG.create("text"));
          this.dom.leading = new SVG.Number(1.3);
          this._rebuild = true;
          this._build = false;
          this.attr("font-family", SVG.defaults.attrs["font-family"]);
        },
        inherit: SVG.Shape,
        extend: {
          // Move over x-axis
          x: function(x) {
            if (x == null)
              return this.attr("x");
            return this.attr("x", x);
          },
          y: function(y) {
            var oy = this.attr("y"), o = typeof oy === "number" ? oy - this.bbox().y : 0;
            if (y == null)
              return typeof oy === "number" ? oy - o : oy;
            return this.attr("y", typeof y.valueOf() === "number" ? y + o : y);
          },
          cx: function(x) {
            return x == null ? this.bbox().cx : this.x(x - this.bbox().width / 2);
          },
          cy: function(y) {
            return y == null ? this.bbox().cy : this.y(y - this.bbox().height / 2);
          },
          text: function(text) {
            if (typeof text === "undefined") {
              var text = "";
              var children = this.node.childNodes;
              for (var i2 = 0, len = children.length; i2 < len; ++i2) {
                if (i2 != 0 && children[i2].nodeType != 3 && SVG.adopt(children[i2]).dom.newLined == true) {
                  text += "\n";
                }
                text += children[i2].textContent;
              }
              return text;
            }
            this.clear().build(true);
            if (typeof text === "function") {
              text.call(this, this);
            } else {
              text = text.split("\n");
              for (var i2 = 0, il2 = text.length; i2 < il2; i2++)
                this.tspan(text[i2]).newLine();
            }
            return this.build(false).rebuild();
          },
          size: function(size2) {
            return this.attr("font-size", size2).rebuild();
          },
          leading: function(value) {
            if (value == null)
              return this.dom.leading;
            this.dom.leading = new SVG.Number(value);
            return this.rebuild();
          },
          lines: function() {
            var node = (this.textPath && this.textPath() || this).node;
            var lines = SVG.utils.map(SVG.utils.filterSVGElements(node.childNodes), function(el) {
              return SVG.adopt(el);
            });
            return new SVG.Set(lines);
          },
          rebuild: function(rebuild) {
            if (typeof rebuild == "boolean")
              this._rebuild = rebuild;
            if (this._rebuild) {
              var self = this, blankLineOffset = 0, dy = this.dom.leading * new SVG.Number(this.attr("font-size"));
              this.lines().each(function() {
                if (this.dom.newLined) {
                  if (!self.textPath())
                    this.attr("x", self.attr("x"));
                  if (this.text() == "\n") {
                    blankLineOffset += dy;
                  } else {
                    this.attr("dy", dy + blankLineOffset);
                    blankLineOffset = 0;
                  }
                }
              });
              this.fire("rebuild");
            }
            return this;
          },
          build: function(build) {
            this._build = !!build;
            return this;
          },
          setData: function(o) {
            this.dom = o;
            this.dom.leading = new SVG.Number(o.leading || 1.3);
            return this;
          }
        },
        construct: {
          // Create text element
          text: function(text) {
            return this.put(new SVG.Text()).text(text);
          },
          plain: function(text) {
            return this.put(new SVG.Text()).plain(text);
          }
        }
      });
      SVG.Tspan = SVG.invent({
        // Initialize node
        create: "tspan",
        inherit: SVG.Shape,
        extend: {
          // Set text content
          text: function(text) {
            if (text == null)
              return this.node.textContent + (this.dom.newLined ? "\n" : "");
            typeof text === "function" ? text.call(this, this) : this.plain(text);
            return this;
          },
          dx: function(dx) {
            return this.attr("dx", dx);
          },
          dy: function(dy) {
            return this.attr("dy", dy);
          },
          newLine: function() {
            var t = this.parent(SVG.Text);
            this.dom.newLined = true;
            return this.dy(t.dom.leading * t.attr("font-size")).attr("x", t.x());
          }
        }
      });
      SVG.extend(SVG.Text, SVG.Tspan, {
        // Create plain text node
        plain: function(text) {
          if (this._build === false)
            this.clear();
          this.node.appendChild(document2.createTextNode(text));
          return this;
        },
        tspan: function(text) {
          var node = (this.textPath && this.textPath() || this).node, tspan = new SVG.Tspan();
          if (this._build === false)
            this.clear();
          node.appendChild(tspan.node);
          return tspan.text(text);
        },
        clear: function() {
          var node = (this.textPath && this.textPath() || this).node;
          while (node.hasChildNodes())
            node.removeChild(node.lastChild);
          return this;
        },
        length: function() {
          return this.node.getComputedTextLength();
        }
      });
      SVG.TextPath = SVG.invent({
        // Initialize node
        create: "textPath",
        inherit: SVG.Parent,
        parent: SVG.Text,
        construct: {
          morphArray: SVG.PathArray,
          path: function(d) {
            var path = new SVG.TextPath(), track = this.doc().defs().path(d);
            while (this.node.hasChildNodes())
              path.node.appendChild(this.node.firstChild);
            this.node.appendChild(path.node);
            path.attr("href", "#" + track, SVG.xlink);
            return this;
          },
          array: function() {
            var track = this.track();
            return track ? track.array() : null;
          },
          plot: function(d) {
            var track = this.track(), pathArray = null;
            if (track) {
              pathArray = track.plot(d);
            }
            return d == null ? pathArray : this;
          },
          track: function() {
            var path = this.textPath();
            if (path)
              return path.reference("href");
          },
          textPath: function() {
            if (this.node.firstChild && this.node.firstChild.nodeName == "textPath")
              return SVG.adopt(this.node.firstChild);
          }
        }
      });
      SVG.Nested = SVG.invent({
        // Initialize node
        create: function() {
          this.constructor.call(this, SVG.create("svg"));
          this.style("overflow", "visible");
        },
        inherit: SVG.Container,
        construct: {
          // Create nested svg document
          nested: function() {
            return this.put(new SVG.Nested());
          }
        }
      });
      SVG.A = SVG.invent({
        // Initialize node
        create: "a",
        inherit: SVG.Container,
        extend: {
          // Link url
          to: function(url) {
            return this.attr("href", url, SVG.xlink);
          },
          show: function(target) {
            return this.attr("show", target, SVG.xlink);
          },
          target: function(target) {
            return this.attr("target", target);
          }
        },
        construct: {
          // Create a hyperlink element
          link: function(url) {
            return this.put(new SVG.A()).to(url);
          }
        }
      });
      SVG.extend(SVG.Element, {
        // Create a hyperlink element
        linkTo: function(url) {
          var link = new SVG.A();
          if (typeof url == "function")
            url.call(link, link);
          else
            link.to(url);
          return this.parent().put(link).put(this);
        }
      });
      SVG.Marker = SVG.invent({
        // Initialize node
        create: "marker",
        inherit: SVG.Container,
        extend: {
          // Set width of element
          width: function(width) {
            return this.attr("markerWidth", width);
          },
          height: function(height) {
            return this.attr("markerHeight", height);
          },
          ref: function(x, y) {
            return this.attr("refX", x).attr("refY", y);
          },
          update: function(block) {
            this.clear();
            if (typeof block == "function")
              block.call(this, this);
            return this;
          },
          toString: function() {
            return "url(#" + this.id() + ")";
          }
        },
        construct: {
          marker: function(width, height, block) {
            return this.defs().marker(width, height, block);
          }
        }
      });
      SVG.extend(SVG.Defs, {
        // Create marker
        marker: function(width, height, block) {
          return this.put(new SVG.Marker()).size(width, height).ref(width / 2, height / 2).viewbox(0, 0, width, height).attr("orient", "auto").update(block);
        }
      });
      SVG.extend(SVG.Line, SVG.Polyline, SVG.Polygon, SVG.Path, {
        // Create and attach markers
        marker: function(marker, width, height, block) {
          var attr = ["marker"];
          if (marker != "all")
            attr.push(marker);
          attr = attr.join("-");
          marker = arguments[1] instanceof SVG.Marker ? arguments[1] : this.doc().marker(width, height, block);
          return this.attr(attr, marker);
        }
      });
      var sugar = {
        stroke: ["color", "width", "opacity", "linecap", "linejoin", "miterlimit", "dasharray", "dashoffset"],
        fill: ["color", "opacity", "rule"],
        prefix: function(t, a) {
          return a == "color" ? t : t + "-" + a;
        }
      };
      ["fill", "stroke"].forEach(function(m) {
        var i2, extension = {};
        extension[m] = function(o) {
          if (typeof o == "undefined")
            return this;
          if (typeof o == "string" || SVG.Color.isRgb(o) || o && typeof o.fill === "function")
            this.attr(m, o);
          else
            for (i2 = sugar[m].length - 1; i2 >= 0; i2--)
              if (o[sugar[m][i2]] != null)
                this.attr(sugar.prefix(m, sugar[m][i2]), o[sugar[m][i2]]);
          return this;
        };
        SVG.extend(SVG.Element, SVG.FX, extension);
      });
      SVG.extend(SVG.Element, SVG.FX, {
        // Map rotation to transform
        rotate: function(d, cx, cy) {
          return this.transform({ rotation: d, cx, cy });
        },
        skew: function(x, y, cx, cy) {
          return arguments.length == 1 || arguments.length == 3 ? this.transform({ skew: x, cx: y, cy: cx }) : this.transform({ skewX: x, skewY: y, cx, cy });
        },
        scale: function(x, y, cx, cy) {
          return arguments.length == 1 || arguments.length == 3 ? this.transform({ scale: x, cx: y, cy: cx }) : this.transform({ scaleX: x, scaleY: y, cx, cy });
        },
        translate: function(x, y) {
          return this.transform({ x, y });
        },
        flip: function(a, o) {
          o = typeof a == "number" ? a : o;
          return this.transform({ flip: a || "both", offset: o });
        },
        matrix: function(m) {
          return this.attr("transform", new SVG.Matrix(arguments.length == 6 ? [].slice.call(arguments) : m));
        },
        opacity: function(value) {
          return this.attr("opacity", value);
        },
        dx: function(x) {
          return this.x(new SVG.Number(x).plus(this instanceof SVG.FX ? 0 : this.x()), true);
        },
        dy: function(y) {
          return this.y(new SVG.Number(y).plus(this instanceof SVG.FX ? 0 : this.y()), true);
        },
        dmove: function(x, y) {
          return this.dx(x).dy(y);
        }
      });
      SVG.extend(SVG.Rect, SVG.Ellipse, SVG.Circle, SVG.Gradient, SVG.FX, {
        // Add x and y radius
        radius: function(x, y) {
          var type = (this._target || this).type;
          return type == "radial" || type == "circle" ? this.attr("r", new SVG.Number(x)) : this.rx(x).ry(y == null ? x : y);
        }
      });
      SVG.extend(SVG.Path, {
        // Get path length
        length: function() {
          return this.node.getTotalLength();
        },
        pointAt: function(length) {
          return this.node.getPointAtLength(length);
        }
      });
      SVG.extend(SVG.Parent, SVG.Text, SVG.Tspan, SVG.FX, {
        // Set font
        font: function(a, v) {
          if (typeof a == "object") {
            for (v in a)
              this.font(v, a[v]);
          }
          return a == "leading" ? this.leading(v) : a == "anchor" ? this.attr("text-anchor", v) : a == "size" || a == "family" || a == "weight" || a == "stretch" || a == "variant" || a == "style" ? this.attr("font-" + a, v) : this.attr(a, v);
        }
      });
      SVG.Set = SVG.invent({
        // Initialize
        create: function(members) {
          if (members instanceof SVG.Set) {
            this.members = members.members.slice();
          } else {
            Array.isArray(members) ? this.members = members : this.clear();
          }
        },
        extend: {
          // Add element to set
          add: function() {
            var i2, il2, elements = [].slice.call(arguments);
            for (i2 = 0, il2 = elements.length; i2 < il2; i2++)
              this.members.push(elements[i2]);
            return this;
          },
          remove: function(element) {
            var i2 = this.index(element);
            if (i2 > -1)
              this.members.splice(i2, 1);
            return this;
          },
          each: function(block) {
            for (var i2 = 0, il2 = this.members.length; i2 < il2; i2++)
              block.apply(this.members[i2], [i2, this.members]);
            return this;
          },
          clear: function() {
            this.members = [];
            return this;
          },
          length: function() {
            return this.members.length;
          },
          has: function(element) {
            return this.index(element) >= 0;
          },
          index: function(element) {
            return this.members.indexOf(element);
          },
          get: function(i2) {
            return this.members[i2];
          },
          first: function() {
            return this.get(0);
          },
          last: function() {
            return this.get(this.members.length - 1);
          },
          valueOf: function() {
            return this.members;
          },
          bbox: function() {
            if (this.members.length == 0)
              return new SVG.RBox();
            var rbox = this.members[0].rbox(this.members[0].doc());
            this.each(function() {
              rbox = rbox.merge(this.rbox(this.doc()));
            });
            return rbox;
          }
        },
        construct: {
          // Create a new set
          set: function(members) {
            return new SVG.Set(members);
          }
        }
      });
      SVG.FX.Set = SVG.invent({
        // Initialize node
        create: function(set2) {
          this.set = set2;
        }
      });
      SVG.Set.inherit = function() {
        var m, methods = [];
        for (var m in SVG.Shape.prototype)
          if (typeof SVG.Shape.prototype[m] == "function" && typeof SVG.Set.prototype[m] != "function")
            methods.push(m);
        methods.forEach(function(method) {
          SVG.Set.prototype[method] = function() {
            for (var i2 = 0, il2 = this.members.length; i2 < il2; i2++)
              if (this.members[i2] && typeof this.members[i2][method] == "function")
                this.members[i2][method].apply(this.members[i2], arguments);
            return method == "animate" ? this.fx || (this.fx = new SVG.FX.Set(this)) : this;
          };
        });
        methods = [];
        for (var m in SVG.FX.prototype)
          if (typeof SVG.FX.prototype[m] == "function" && typeof SVG.FX.Set.prototype[m] != "function")
            methods.push(m);
        methods.forEach(function(method) {
          SVG.FX.Set.prototype[method] = function() {
            for (var i2 = 0, il2 = this.set.members.length; i2 < il2; i2++)
              this.set.members[i2].fx[method].apply(this.set.members[i2].fx, arguments);
            return this;
          };
        });
      };
      SVG.extend(SVG.Element, {
        // Store data values on svg nodes
        data: function(a, v, r) {
          if (typeof a == "object") {
            for (v in a)
              this.data(v, a[v]);
          } else if (arguments.length < 2) {
            try {
              return JSON.parse(this.attr("data-" + a));
            } catch (e) {
              return this.attr("data-" + a);
            }
          } else {
            this.attr(
              "data-" + a,
              v === null ? null : r === true || typeof v === "string" || typeof v === "number" ? v : JSON.stringify(v)
            );
          }
          return this;
        }
      });
      SVG.extend(SVG.Element, {
        // Remember arbitrary data
        remember: function(k, v) {
          if (typeof arguments[0] == "object")
            for (var v in k)
              this.remember(v, k[v]);
          else if (arguments.length == 1)
            return this.memory()[k];
          else
            this.memory()[k] = v;
          return this;
        },
        forget: function() {
          if (arguments.length == 0)
            this._memory = {};
          else
            for (var i2 = arguments.length - 1; i2 >= 0; i2--)
              delete this.memory()[arguments[i2]];
          return this;
        },
        memory: function() {
          return this._memory || (this._memory = {});
        }
      });
      SVG.get = function(id) {
        var node = document2.getElementById(idFromReference(id) || id);
        return SVG.adopt(node);
      };
      SVG.select = function(query, parent) {
        return new SVG.Set(
          SVG.utils.map((parent || document2).querySelectorAll(query), function(node) {
            return SVG.adopt(node);
          })
        );
      };
      SVG.extend(SVG.Parent, {
        // Scoped select method
        select: function(query) {
          return SVG.select(query, this.node);
        }
      });
      function pathRegReplace(a, b, c, d) {
        return c + d.replace(SVG.regex.dots, " .");
      }
      function array_clone(arr) {
        var clone = arr.slice(0);
        for (var i2 = clone.length; i2--; ) {
          if (Array.isArray(clone[i2])) {
            clone[i2] = array_clone(clone[i2]);
          }
        }
        return clone;
      }
      function is(el, obj) {
        return el instanceof obj;
      }
      function matches(el, selector) {
        return (el.matches || el.matchesSelector || el.msMatchesSelector || el.mozMatchesSelector || el.webkitMatchesSelector || el.oMatchesSelector).call(el, selector);
      }
      function camelCase(s) {
        return s.toLowerCase().replace(/-(.)/g, function(m, g) {
          return g.toUpperCase();
        });
      }
      function capitalize(s) {
        return s.charAt(0).toUpperCase() + s.slice(1);
      }
      function fullHex(hex) {
        return hex.length == 4 ? [
          "#",
          hex.substring(1, 2),
          hex.substring(1, 2),
          hex.substring(2, 3),
          hex.substring(2, 3),
          hex.substring(3, 4),
          hex.substring(3, 4)
        ].join("") : hex;
      }
      function compToHex(comp) {
        var hex = comp.toString(16);
        return hex.length == 1 ? "0" + hex : hex;
      }
      function proportionalSize(element, width, height) {
        if (width == null || height == null) {
          var box = element.bbox();
          if (width == null)
            width = box.width / box.height * height;
          else if (height == null)
            height = box.height / box.width * width;
        }
        return {
          width,
          height
        };
      }
      function deltaTransformPoint(matrix, x, y) {
        return {
          x: x * matrix.a + y * matrix.c + 0,
          y: x * matrix.b + y * matrix.d + 0
        };
      }
      function arrayToMatrix(a) {
        return { a: a[0], b: a[1], c: a[2], d: a[3], e: a[4], f: a[5] };
      }
      function parseMatrix(matrix) {
        if (!(matrix instanceof SVG.Matrix))
          matrix = new SVG.Matrix(matrix);
        return matrix;
      }
      function ensureCentre(o, target) {
        o.cx = o.cx == null ? target.bbox().cx : o.cx;
        o.cy = o.cy == null ? target.bbox().cy : o.cy;
      }
      function arrayToString(a) {
        for (var i2 = 0, il2 = a.length, s = ""; i2 < il2; i2++) {
          s += a[i2][0];
          if (a[i2][1] != null) {
            s += a[i2][1];
            if (a[i2][2] != null) {
              s += " ";
              s += a[i2][2];
              if (a[i2][3] != null) {
                s += " ";
                s += a[i2][3];
                s += " ";
                s += a[i2][4];
                if (a[i2][5] != null) {
                  s += " ";
                  s += a[i2][5];
                  s += " ";
                  s += a[i2][6];
                  if (a[i2][7] != null) {
                    s += " ";
                    s += a[i2][7];
                  }
                }
              }
            }
          }
        }
        return s + " ";
      }
      function assignNewId(node) {
        for (var i2 = node.childNodes.length - 1; i2 >= 0; i2--)
          if (node.childNodes[i2] instanceof window2.SVGElement)
            assignNewId(node.childNodes[i2]);
        return SVG.adopt(node).id(SVG.eid(node.nodeName));
      }
      function fullBox(b) {
        if (b.x == null) {
          b.x = 0;
          b.y = 0;
          b.width = 0;
          b.height = 0;
        }
        b.w = b.width;
        b.h = b.height;
        b.x2 = b.x + b.width;
        b.y2 = b.y + b.height;
        b.cx = b.x + b.width / 2;
        b.cy = b.y + b.height / 2;
        return b;
      }
      function idFromReference(url) {
        var m = (url || "").toString().match(SVG.regex.reference);
        if (m)
          return m[1];
      }
      function float32String(v) {
        return Math.abs(v) > 1e-37 ? v : 0;
      }
      var abcdef = "abcdef".split("");
      if (typeof window2.CustomEvent !== "function") {
        var CustomEventPoly = function(event, options) {
          options = options || { bubbles: false, cancelable: false, detail: void 0 };
          var e = document2.createEvent("CustomEvent");
          e.initCustomEvent(event, options.bubbles, options.cancelable, options.detail);
          return e;
        };
        CustomEventPoly.prototype = window2.Event.prototype;
        SVG.CustomEvent = CustomEventPoly;
      } else {
        SVG.CustomEvent = window2.CustomEvent;
      }
      (function(w) {
        var lastTime = 0;
        var vendors = ["moz", "webkit"];
        for (var x = 0; x < vendors.length && !window2.requestAnimationFrame; ++x) {
          w.requestAnimationFrame = w[vendors[x] + "RequestAnimationFrame"];
          w.cancelAnimationFrame = w[vendors[x] + "CancelAnimationFrame"] || w[vendors[x] + "CancelRequestAnimationFrame"];
        }
        w.requestAnimationFrame = w.requestAnimationFrame || function(callback) {
          var currTime = (/* @__PURE__ */ new Date()).getTime();
          var timeToCall = Math.max(0, 16 - (currTime - lastTime));
          var id = w.setTimeout(function() {
            callback(currTime + timeToCall);
          }, timeToCall);
          lastTime = currTime + timeToCall;
          return id;
        };
        w.cancelAnimationFrame = w.cancelAnimationFrame || w.clearTimeout;
      })(window2);
      return SVG;
    });
  }
});

// node_modules/component-query/index.js
var require_component_query = __commonJS({
  "node_modules/component-query/index.js"(exports, module) {
    function one(selector, el) {
      return el.querySelector(selector);
    }
    exports = module.exports = function(selector, el) {
      el = el || document;
      return one(selector, el);
    };
    exports.all = function(selector, el) {
      el = el || document;
      return el.querySelectorAll(selector);
    };
    exports.engine = function(obj) {
      if (!obj.one)
        throw new Error(".one callback required");
      if (!obj.all)
        throw new Error(".all callback required");
      one = obj.one;
      exports.all = obj.all;
      return exports;
    };
  }
});

// node_modules/bpmn-js-token-simulation/node_modules/min-dom/lib/query.js
var require_query = __commonJS({
  "node_modules/bpmn-js-token-simulation/node_modules/min-dom/lib/query.js"(exports, module) {
    module.exports = require_component_query();
  }
});

// node_modules/bpmn-js-token-simulation/lib/util/EventHelper.js
var require_EventHelper = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/util/EventHelper.js"(exports, module) {
    var prefix = "tokenSimulation";
    module.exports = {
      TOGGLE_MODE_EVENT: prefix + ".toggleMode",
      GENERATE_TOKEN_EVENT: prefix + ".generateToken",
      CONSUME_TOKEN_EVENT: prefix + ".consumeToken",
      PLAY_SIMULATION_EVENT: prefix + ".playSimulation",
      PAUSE_SIMULATION_EVENT: prefix + ".pauseSimulation",
      RESET_SIMULATION_EVENT: prefix + ".resetSimulation",
      TERMINATE_EVENT: prefix + ".terminateEvent",
      UPDATE_ELEMENTS_EVENT: prefix + ".updateElements",
      UPDATE_ELEMENT_EVENT: prefix + ".updateElement",
      PROCESS_INSTANCE_CREATED_EVENT: prefix + ".processInstanceCreated",
      PROCESS_INSTANCE_FINISHED_EVENT: prefix + ".processInstanceFinished",
      PROCESS_INSTANCE_SHOWN_EVENT: prefix + ".processInstanceShown",
      PROCESS_INSTANCE_HIDDEN_EVENT: prefix + ".processInstanceHidden",
      ANIMATION_CREATED_EVENT: prefix + ".animationCreated"
    };
  }
});

// node_modules/bpmn-js-token-simulation/node_modules/min-dash/dist/index.esm.js
var index_esm_exports = {};
__export(index_esm_exports, {
  assign: () => assign,
  bind: () => bind,
  debounce: () => debounce,
  ensureArray: () => ensureArray,
  every: () => every,
  filter: () => filter,
  find: () => find,
  findIndex: () => findIndex,
  flatten: () => flatten,
  forEach: () => forEach,
  get: () => get,
  groupBy: () => groupBy,
  has: () => has,
  isArray: () => isArray,
  isDefined: () => isDefined,
  isFunction: () => isFunction,
  isNil: () => isNil,
  isNumber: () => isNumber,
  isObject: () => isObject,
  isString: () => isString,
  isUndefined: () => isUndefined,
  keys: () => keys,
  map: () => map,
  matchPattern: () => matchPattern,
  merge: () => merge,
  omit: () => omit,
  pick: () => pick,
  reduce: () => reduce,
  set: () => set,
  size: () => size,
  some: () => some,
  sortBy: () => sortBy,
  throttle: () => throttle,
  unionBy: () => unionBy,
  uniqueBy: () => uniqueBy,
  values: () => values,
  without: () => without
});
function flatten(arr) {
  return Array.prototype.concat.apply([], arr);
}
function isUndefined(obj) {
  return obj === void 0;
}
function isDefined(obj) {
  return obj !== void 0;
}
function isNil(obj) {
  return obj == null;
}
function isArray(obj) {
  return nativeToString.call(obj) === "[object Array]";
}
function isObject(obj) {
  return nativeToString.call(obj) === "[object Object]";
}
function isNumber(obj) {
  return nativeToString.call(obj) === "[object Number]";
}
function isFunction(obj) {
  var tag = nativeToString.call(obj);
  return tag === "[object Function]" || tag === "[object AsyncFunction]" || tag === "[object GeneratorFunction]" || tag === "[object AsyncGeneratorFunction]" || tag === "[object Proxy]";
}
function isString(obj) {
  return nativeToString.call(obj) === "[object String]";
}
function ensureArray(obj) {
  if (isArray(obj)) {
    return;
  }
  throw new Error("must supply array");
}
function has(target, key) {
  return nativeHasOwnProperty.call(target, key);
}
function find(collection, matcher) {
  matcher = toMatcher(matcher);
  var match;
  forEach(collection, function(val, key) {
    if (matcher(val, key)) {
      match = val;
      return false;
    }
  });
  return match;
}
function findIndex(collection, matcher) {
  matcher = toMatcher(matcher);
  var idx = isArray(collection) ? -1 : void 0;
  forEach(collection, function(val, key) {
    if (matcher(val, key)) {
      idx = key;
      return false;
    }
  });
  return idx;
}
function filter(collection, matcher) {
  var result = [];
  forEach(collection, function(val, key) {
    if (matcher(val, key)) {
      result.push(val);
    }
  });
  return result;
}
function forEach(collection, iterator) {
  var val, result;
  if (isUndefined(collection)) {
    return;
  }
  var convertKey = isArray(collection) ? toNum : identity;
  for (var key in collection) {
    if (has(collection, key)) {
      val = collection[key];
      result = iterator(val, convertKey(key));
      if (result === false) {
        return val;
      }
    }
  }
}
function without(arr, matcher) {
  if (isUndefined(arr)) {
    return [];
  }
  ensureArray(arr);
  matcher = toMatcher(matcher);
  return arr.filter(function(el, idx) {
    return !matcher(el, idx);
  });
}
function reduce(collection, iterator, result) {
  forEach(collection, function(value, idx) {
    result = iterator(result, value, idx);
  });
  return result;
}
function every(collection, matcher) {
  return !!reduce(collection, function(matches, val, key) {
    return matches && matcher(val, key);
  }, true);
}
function some(collection, matcher) {
  return !!find(collection, matcher);
}
function map(collection, fn) {
  var result = [];
  forEach(collection, function(val, key) {
    result.push(fn(val, key));
  });
  return result;
}
function keys(collection) {
  return collection && Object.keys(collection) || [];
}
function size(collection) {
  return keys(collection).length;
}
function values(collection) {
  return map(collection, function(val) {
    return val;
  });
}
function groupBy(collection, extractor) {
  var grouped = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};
  extractor = toExtractor(extractor);
  forEach(collection, function(val) {
    var discriminator = extractor(val) || "_";
    var group = grouped[discriminator];
    if (!group) {
      group = grouped[discriminator] = [];
    }
    group.push(val);
  });
  return grouped;
}
function uniqueBy(extractor) {
  extractor = toExtractor(extractor);
  var grouped = {};
  for (var _len = arguments.length, collections = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
    collections[_key - 1] = arguments[_key];
  }
  forEach(collections, function(c) {
    return groupBy(c, extractor, grouped);
  });
  var result = map(grouped, function(val, key) {
    return val[0];
  });
  return result;
}
function sortBy(collection, extractor) {
  extractor = toExtractor(extractor);
  var sorted = [];
  forEach(collection, function(value, key) {
    var disc = extractor(value, key);
    var entry = {
      d: disc,
      v: value
    };
    for (var idx = 0; idx < sorted.length; idx++) {
      var d = sorted[idx].d;
      if (disc < d) {
        sorted.splice(idx, 0, entry);
        return;
      }
    }
    sorted.push(entry);
  });
  return map(sorted, function(e) {
    return e.v;
  });
}
function matchPattern(pattern) {
  return function(el) {
    return every(pattern, function(val, key) {
      return el[key] === val;
    });
  };
}
function toExtractor(extractor) {
  return isFunction(extractor) ? extractor : function(e) {
    return e[extractor];
  };
}
function toMatcher(matcher) {
  return isFunction(matcher) ? matcher : function(e) {
    return e === matcher;
  };
}
function identity(arg) {
  return arg;
}
function toNum(arg) {
  return Number(arg);
}
function debounce(fn, timeout) {
  var timer;
  var lastArgs;
  var lastThis;
  var lastNow;
  function fire(force) {
    var now = Date.now();
    var scheduledDiff = force ? 0 : lastNow + timeout - now;
    if (scheduledDiff > 0) {
      return schedule(scheduledDiff);
    }
    fn.apply(lastThis, lastArgs);
    clear();
  }
  function schedule(timeout2) {
    timer = setTimeout(fire, timeout2);
  }
  function clear() {
    if (timer) {
      clearTimeout(timer);
    }
    timer = lastNow = lastArgs = lastThis = void 0;
  }
  function flush() {
    if (timer) {
      fire(true);
    }
    clear();
  }
  function callback() {
    lastNow = Date.now();
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    lastArgs = args;
    lastThis = this;
    if (!timer) {
      schedule(timeout);
    }
  }
  callback.flush = flush;
  callback.cancel = clear;
  return callback;
}
function throttle(fn, interval) {
  var throttling = false;
  return function() {
    if (throttling) {
      return;
    }
    fn.apply(void 0, arguments);
    throttling = true;
    setTimeout(function() {
      throttling = false;
    }, interval);
  };
}
function bind(fn, target) {
  return fn.bind(target);
}
function _typeof(obj) {
  "@babel/helpers - typeof";
  if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") {
    _typeof = function(obj2) {
      return typeof obj2;
    };
  } else {
    _typeof = function(obj2) {
      return obj2 && typeof Symbol === "function" && obj2.constructor === Symbol && obj2 !== Symbol.prototype ? "symbol" : typeof obj2;
    };
  }
  return _typeof(obj);
}
function _extends() {
  _extends = Object.assign || function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends.apply(this, arguments);
}
function assign(target) {
  for (var _len = arguments.length, others = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
    others[_key - 1] = arguments[_key];
  }
  return _extends.apply(void 0, [target].concat(others));
}
function set(target, path, value) {
  var currentTarget = target;
  forEach(path, function(key, idx) {
    if (typeof key !== "number" && typeof key !== "string") {
      throw new Error("illegal key type: " + _typeof(key) + ". Key should be of type number or string.");
    }
    if (key === "constructor") {
      throw new Error("illegal key: constructor");
    }
    if (key === "__proto__") {
      throw new Error("illegal key: __proto__");
    }
    var nextKey = path[idx + 1];
    var nextTarget = currentTarget[key];
    if (isDefined(nextKey) && isNil(nextTarget)) {
      nextTarget = currentTarget[key] = isNaN(+nextKey) ? {} : [];
    }
    if (isUndefined(nextKey)) {
      if (isUndefined(value)) {
        delete currentTarget[key];
      } else {
        currentTarget[key] = value;
      }
    } else {
      currentTarget = nextTarget;
    }
  });
  return target;
}
function get(target, path, defaultValue) {
  var currentTarget = target;
  forEach(path, function(key) {
    if (isNil(currentTarget)) {
      currentTarget = void 0;
      return false;
    }
    currentTarget = currentTarget[key];
  });
  return isUndefined(currentTarget) ? defaultValue : currentTarget;
}
function pick(target, properties) {
  var result = {};
  var obj = Object(target);
  forEach(properties, function(prop) {
    if (prop in obj) {
      result[prop] = target[prop];
    }
  });
  return result;
}
function omit(target, properties) {
  var result = {};
  var obj = Object(target);
  forEach(obj, function(prop, key) {
    if (properties.indexOf(key) === -1) {
      result[key] = prop;
    }
  });
  return result;
}
function merge(target) {
  for (var _len2 = arguments.length, sources = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
    sources[_key2 - 1] = arguments[_key2];
  }
  if (!sources.length) {
    return target;
  }
  forEach(sources, function(source) {
    if (!source || !isObject(source)) {
      return;
    }
    forEach(source, function(sourceVal, key) {
      if (key === "__proto__") {
        return;
      }
      var targetVal = target[key];
      if (isObject(sourceVal)) {
        if (!isObject(targetVal)) {
          targetVal = {};
        }
        target[key] = merge(targetVal, sourceVal);
      } else {
        target[key] = sourceVal;
      }
    });
  });
  return target;
}
var nativeToString, nativeHasOwnProperty, unionBy;
var init_index_esm = __esm({
  "node_modules/bpmn-js-token-simulation/node_modules/min-dash/dist/index.esm.js"() {
    nativeToString = Object.prototype.toString;
    nativeHasOwnProperty = Object.prototype.hasOwnProperty;
    unionBy = uniqueBy;
  }
});

// node_modules/bpmn-js-token-simulation/lib/util/ElementHelper.js
var require_ElementHelper = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/util/ElementHelper.js"(exports, module) {
    "use strict";
    var every2 = (init_index_esm(), __toCommonJS(index_esm_exports)).every;
    var some2 = (init_index_esm(), __toCommonJS(index_esm_exports)).some;
    module.exports.is = function(element, types) {
      if (element.type === "label") {
        return;
      }
      if (!Array.isArray(types)) {
        types = [types];
      }
      var isType = false;
      types.forEach(function(type) {
        if (type === element.type) {
          isType = true;
        }
      });
      return isType;
    };
    module.exports.isTypedEvent = function(event, eventDefinitionType, filter2) {
      function matches(definition, filter3) {
        return every2(filter3, function(val, key) {
          return definition[key] == val;
        });
      }
      return some2(event.eventDefinitions, function(definition) {
        return definition.$type === eventDefinitionType && matches(event, filter2);
      });
    };
    module.exports.getBusinessObject = function(element) {
      return element && element.businessObject || element;
    };
    function isAncestor(ancestor, descendant) {
      var childParent = descendant.parent;
      while (childParent) {
        if (childParent === ancestor) {
          return true;
        }
        childParent = childParent.parent;
      }
      return false;
    }
    module.exports.isAncestor = isAncestor;
    module.exports.getDescendants = function(elements, ancestor) {
      return elements.filter(function(element) {
        return isAncestor(ancestor, element);
      });
    };
    module.exports.supportedElements = [
      "bpmn:Association",
      "bpmn:BoundaryEvent",
      "bpmn:BusinessRuleTask",
      "bpmn:CallActivity",
      "bpmn:DataInputAssociation",
      "bpmn:DataObjectReference",
      "bpmn:DataOutputAssociation",
      "bpmn:DataStoreReference",
      "bpmn:EndEvent",
      "bpmn:EventBasedGateway",
      "bpmn:ExclusiveGateway",
      "bpmn:IntermediateCatchEvent",
      "bpmn:ManualTask",
      "bpmn:ParallelGateway",
      "bpmn:Process",
      "bpmn:ScriptTask",
      "bpmn:SequenceFlow",
      "bpmn:ServiceTask",
      "bpmn:StartEvent",
      "bpmn:SubProcess",
      "bpmn:Task",
      "bpmn:TextAnnotation",
      "bpmn:UserTask"
    ];
  }
});

// node_modules/bpmn-js-token-simulation/lib/util/GeometryUtil.js
var require_GeometryUtil = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/util/GeometryUtil.js"(exports, module) {
    module.exports.getMid = function(element) {
      var bbox = element.bbox();
      return {
        x: bbox.x + bbox.width / 2,
        y: bbox.y + bbox.height / 2
      };
    };
    module.exports.distance = function(a, b) {
      return Math.sqrt(Math.pow(a.x - b.x, 2) + Math.pow(a.y - b.y, 2));
    };
  }
});

// node_modules/bpmn-js-token-simulation/lib/animation/Animation.js
var require_Animation = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/animation/Animation.js"(exports, module) {
    "use strict";
    var SVG = require_svg();
    var domQuery = require_query();
    var events = require_EventHelper();
    var RESET_SIMULATION_EVENT = events.RESET_SIMULATION_EVENT;
    var PLAY_SIMULATION_EVENT = events.PLAY_SIMULATION_EVENT;
    var PAUSE_SIMULATION_EVENT = events.PAUSE_SIMULATION_EVENT;
    var TERMINATE_EVENT = events.TERMINATE_EVENT;
    var PROCESS_INSTANCE_FINISHED_EVENT = events.PROCESS_INSTANCE_FINISHED_EVENT;
    var ANIMATION_CREATED_EVENT = events.ANIMATION_CREATED_EVENT;
    var isAncestor = require_ElementHelper().isAncestor;
    var geometryUtil = require_GeometryUtil();
    var distance = geometryUtil.distance;
    var STROKE_COLOR = getComputedStyle(document.documentElement).getPropertyValue("--token-simulation-green-base-44");
    function isFirstSegment(index) {
      return index === 1;
    }
    function isSingleSegment(waypoints) {
      return waypoints.length == 2;
    }
    var DELAY = 0;
    var EASE_LINEAR = "-";
    var EASE_IN = "<";
    var EASE_IN_OUT = "<>";
    var TOKEN_SIZE = 20;
    function Animation(canvas, eventBus) {
      var self = window.animation = this;
      this._eventBus = eventBus;
      this.animations = [];
      this.hiddenAnimations = [];
      this.animationSpeed = 1;
      eventBus.on("import.done", function() {
        var draw = SVG(canvas._svg);
        var viewport = domQuery(".viewport", canvas._svg);
        var groupParent = SVG.adopt(viewport);
        self.group = draw.group().attr("id", "token-simulation");
        groupParent.put(self.group);
      });
      eventBus.on(TERMINATE_EVENT, function(context) {
        var element = context.element, parent = element.parent;
        self.animations.forEach(function(animation) {
          if (isAncestor(parent, animation.element)) {
            animation.animation.stop();
            self.animations = self.animations.filter(function(a) {
              return a !== animation;
            });
          }
        });
      });
      eventBus.on(PROCESS_INSTANCE_FINISHED_EVENT, function(context) {
        var parent = context.parent;
        self.animations.forEach(function(animation) {
          if (context.processInstanceId === animation.processInstanceId || isAncestor(parent, animation.element)) {
            animation.animation.stop();
            self.animations = self.animations.filter(function(a) {
              return a !== animation;
            });
          }
        });
      });
      eventBus.on(RESET_SIMULATION_EVENT, function() {
        self.animations.forEach(function(animation) {
          animation.animation.stop();
        });
        self.animations = [];
        self.hiddenAnimations = [];
      });
      eventBus.on(PAUSE_SIMULATION_EVENT, function() {
        self.animations.forEach(function(animation) {
          animation.animation.pause();
        });
      });
      eventBus.on(PLAY_SIMULATION_EVENT, function() {
        self.animations.forEach(function(animation) {
          animation.animation.play();
        });
      });
    }
    Animation.prototype.createAnimation = function(connection, processInstanceId, done) {
      var self = this;
      if (!this.group) {
        return;
      }
      var tokenGfx = this._createTokenGfx(processInstanceId);
      var animation;
      animation = new _Animation(tokenGfx, connection.waypoints, function() {
        self.animations = self.animations.filter(function(a) {
          return a.animation !== animation;
        });
        if (done) {
          done();
        }
      });
      if (this.hiddenAnimations.includes(processInstanceId)) {
        tokenGfx.hide();
      }
      tokenGfx.fx._speed = this.animationSpeed;
      this.animations.push({
        tokenGfx,
        animation,
        element: connection,
        processInstanceId
      });
      this._eventBus.fire(ANIMATION_CREATED_EVENT, {
        tokenGfx,
        animation,
        element: connection,
        processInstanceId
      });
      return animation;
    };
    Animation.prototype.setAnimationSpeed = function(speed) {
      this.animations.forEach(function(animation) {
        animation.tokenGfx.fx._speed = speed;
      });
      this.animationSpeed = speed;
    };
    Animation.prototype._createTokenGfx = function(processInstanceId) {
      var parent = this.group.group().attr("class", "token").hide();
      parent.circle(TOKEN_SIZE, TOKEN_SIZE).attr("fill", STROKE_COLOR).attr("class", "circle");
      parent.text(processInstanceId.toString()).attr("transform", "translate(10, -7)").attr("text-anchor", "middle").attr("class", "text");
      return parent;
    };
    Animation.prototype.showProcessInstanceAnimations = function(processInstanceId) {
      this.animations.forEach(function(animation) {
        if (animation.processInstanceId === processInstanceId) {
          animation.tokenGfx.show();
        }
      });
      this.hiddenAnimations = this.hiddenAnimations.filter(function(id) {
        return id !== processInstanceId;
      });
    };
    Animation.prototype.hideProcessInstanceAnimations = function(processInstanceId) {
      this.animations.forEach(function(animation) {
        if (animation.processInstanceId === processInstanceId) {
          animation.tokenGfx.hide();
        }
      });
      this.hiddenAnimations.push(processInstanceId);
    };
    Animation.$inject = ["canvas", "eventBus"];
    module.exports = Animation;
    function _Animation(gfx, waypoints, done) {
      this.gfx = this.fx = gfx;
      this.waypoints = waypoints;
      this.done = done;
      this.create();
    }
    _Animation.prototype.create = function() {
      var gfx = this.gfx, waypoints = this.waypoints, done = this.done, fx = this.fx;
      gfx.show().move(waypoints[0].x - TOKEN_SIZE / 2, waypoints[0].y - TOKEN_SIZE / 2);
      waypoints.forEach(function(waypoint, index) {
        if (index > 0) {
          var x = waypoint.x - TOKEN_SIZE / 2, y = waypoint.y - TOKEN_SIZE / 2;
          var ease = isFirstSegment(index) ? EASE_IN : EASE_LINEAR;
          if (isSingleSegment(waypoints)) {
            ease = EASE_IN_OUT;
          }
          var duration = distance(waypoints[index - 1], waypoint) * 20;
          fx = fx.animate(duration, ease, DELAY).move(x, y);
        }
      });
      fx.after(function() {
        gfx.remove();
        done();
      });
    };
    _Animation.prototype.play = function() {
      this.gfx.play();
    };
    _Animation.prototype.pause = function() {
      this.gfx.pause();
    };
    _Animation.prototype.stop = function() {
      this.fx.stop();
      this.gfx.remove();
    };
  }
});

// node_modules/domify/index.js
var require_domify = __commonJS({
  "node_modules/domify/index.js"(exports, module) {
    module.exports = parse;
    var innerHTMLBug = false;
    var bugTestDiv;
    if (typeof document !== "undefined") {
      bugTestDiv = document.createElement("div");
      bugTestDiv.innerHTML = '  <link/><table></table><a href="/a">a</a><input type="checkbox"/>';
      innerHTMLBug = !bugTestDiv.getElementsByTagName("link").length;
      bugTestDiv = void 0;
    }
    var map2 = {
      legend: [1, "<fieldset>", "</fieldset>"],
      tr: [2, "<table><tbody>", "</tbody></table>"],
      col: [2, "<table><tbody></tbody><colgroup>", "</colgroup></table>"],
      // for script/link/style tags to work in IE6-8, you have to wrap
      // in a div with a non-whitespace character in front, ha!
      _default: innerHTMLBug ? [1, "X<div>", "</div>"] : [0, "", ""]
    };
    map2.td = map2.th = [3, "<table><tbody><tr>", "</tr></tbody></table>"];
    map2.option = map2.optgroup = [1, '<select multiple="multiple">', "</select>"];
    map2.thead = map2.tbody = map2.colgroup = map2.caption = map2.tfoot = [1, "<table>", "</table>"];
    map2.polyline = map2.ellipse = map2.polygon = map2.circle = map2.text = map2.line = map2.path = map2.rect = map2.g = [1, '<svg xmlns="http://www.w3.org/2000/svg" version="1.1">', "</svg>"];
    function parse(html, doc) {
      if ("string" != typeof html)
        throw new TypeError("String expected");
      if (!doc)
        doc = document;
      var m = /<([\w:]+)/.exec(html);
      if (!m)
        return doc.createTextNode(html);
      html = html.replace(/^\s+|\s+$/g, "");
      var tag = m[1];
      if (tag == "body") {
        var el = doc.createElement("html");
        el.innerHTML = html;
        return el.removeChild(el.lastChild);
      }
      var wrap = Object.prototype.hasOwnProperty.call(map2, tag) ? map2[tag] : map2._default;
      var depth = wrap[0];
      var prefix = wrap[1];
      var suffix = wrap[2];
      var el = doc.createElement("div");
      el.innerHTML = prefix + html + suffix;
      while (depth--)
        el = el.lastChild;
      if (el.firstChild == el.lastChild) {
        return el.removeChild(el.firstChild);
      }
      var fragment = doc.createDocumentFragment();
      while (el.firstChild) {
        fragment.appendChild(el.removeChild(el.firstChild));
      }
      return fragment;
    }
  }
});

// node_modules/bpmn-js-token-simulation/node_modules/min-dom/lib/domify.js
var require_domify2 = __commonJS({
  "node_modules/bpmn-js-token-simulation/node_modules/min-dom/lib/domify.js"(exports, module) {
    module.exports = require_domify();
  }
});

// node_modules/component-event/index.js
var require_component_event = __commonJS({
  "node_modules/component-event/index.js"(exports) {
    var bind2 = window.addEventListener ? "addEventListener" : "attachEvent";
    var unbind = window.removeEventListener ? "removeEventListener" : "detachEvent";
    var prefix = bind2 !== "addEventListener" ? "on" : "";
    exports.bind = function(el, type, fn, capture) {
      el[bind2](prefix + type, fn, capture || false);
      return fn;
    };
    exports.unbind = function(el, type, fn, capture) {
      el[unbind](prefix + type, fn, capture || false);
      return fn;
    };
  }
});

// node_modules/bpmn-js-token-simulation/node_modules/min-dom/lib/event.js
var require_event = __commonJS({
  "node_modules/bpmn-js-token-simulation/node_modules/min-dom/lib/event.js"(exports, module) {
    module.exports = require_component_event();
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/context-pads/handler/BoundaryEventHandler.js
var require_BoundaryEventHandler = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/context-pads/handler/BoundaryEventHandler.js"(exports, module) {
    "use strict";
    var domify = require_domify2();
    var domEvent = require_event();
    var is = require_ElementHelper().is;
    var events = require_EventHelper();
    var GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT;
    var UPDATE_ELEMENT_EVENT = events.UPDATE_ELEMENT_EVENT;
    function BoundaryEventHandler(eventBus, processInstances, processInstanceSettings) {
      this._eventBus = eventBus;
      this._processInstances = processInstances;
      this._processInstanceSettings = processInstanceSettings;
    }
    BoundaryEventHandler.prototype.createContextPads = function(element) {
      if (!element.attachers.length) {
        return;
      }
      if (!this._processInstances.getProcessInstances(element).length) {
        return;
      }
      var incomingSequenceFlows = element.incoming.filter(function(incoming) {
        return is(incoming, "bpmn:SequenceFlow");
      });
      var self = this;
      var contextPads = [];
      element.attachers.forEach(function(attachedElement) {
        var outgoingSequenceFlows = attachedElement.outgoing.filter(function(outgoing) {
          return is(outgoing, "bpmn:SequenceFlow");
        });
        if (!incomingSequenceFlows.length || !outgoingSequenceFlows.length) {
          return;
        }
        var contextPad = domify('<div class="context-pad" title="Trigger Event"><i class="fa fa-play"></i></div>');
        contextPads.push({
          element: attachedElement,
          html: contextPad
        });
        domEvent.bind(contextPad, "click", function() {
          self._processInstances.getProcessInstances(element).forEach(function(processInstance) {
            var parentProcessInstanceId = processInstance.parentProcessInstanceId;
            if (attachedElement.businessObject.cancelActivity) {
              element.children.forEach(function(child) {
                if (child.tokenCount && child.tokenCount[processInstance.processInstanceId]) {
                  child.tokenCount[processInstance.processInstanceId]--;
                }
              });
              self._processInstances.finish(processInstance.processInstanceId);
              self._eventBus.fire(UPDATE_ELEMENT_EVENT, {
                element
              });
            }
            self._eventBus.fire(GENERATE_TOKEN_EVENT, {
              element: attachedElement,
              processInstanceId: parentProcessInstanceId
            });
          });
        });
      });
      return contextPads;
    };
    BoundaryEventHandler.$inject = ["eventBus", "processInstances", "processInstanceSettings"];
    module.exports = BoundaryEventHandler;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/context-pads/handler/ExclusiveGatewayHandler.js
var require_ExclusiveGatewayHandler = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/context-pads/handler/ExclusiveGatewayHandler.js"(exports, module) {
    "use strict";
    var is = require_ElementHelper().is;
    var domify = require_domify2();
    var domEvent = require_event();
    function ExclusiveGatewayHandler(exluciveGatewaySettings) {
      this._exclusiveGatewaySettings = exluciveGatewaySettings;
    }
    ExclusiveGatewayHandler.prototype.createContextPads = function(element) {
      var self = this;
      var outgoingSequenceFlows = element.outgoing.filter(function(outgoing) {
        return is(outgoing, "bpmn:SequenceFlow");
      });
      if (outgoingSequenceFlows.length < 2) {
        return;
      }
      var contextPad = domify('<div class="context-pad" title="Set Sequence Flow"><i class="fa fa-code-fork"></i></div>');
      domEvent.bind(contextPad, "click", function() {
        self._exclusiveGatewaySettings.setSequenceFlow(element);
      });
      return [{
        element,
        html: contextPad
      }];
    };
    ExclusiveGatewayHandler.$inject = ["exclusiveGatewaySettings"];
    module.exports = ExclusiveGatewayHandler;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/context-pads/handler/IntermediateCatchEventHandler.js
var require_IntermediateCatchEventHandler = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/context-pads/handler/IntermediateCatchEventHandler.js"(exports, module) {
    "use strict";
    var domify = require_domify2();
    var domEvent = require_event();
    var is = require_ElementHelper().is;
    var events = require_EventHelper();
    var GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT;
    function IntermeditateCatchEventHandler(eventBus) {
      this._eventBus = eventBus;
    }
    IntermeditateCatchEventHandler.prototype.createContextPads = function(element) {
      var processInstanceId = element.parent.shownProcessInstance;
      var incomingSequenceFlows = element.incoming.filter(function(incoming) {
        return is(incoming, "bpmn:SequenceFlow");
      });
      var eventBasedGatewaysHaveTokens = [];
      incomingSequenceFlows.forEach(function(incoming) {
        var source = incoming.source;
        if (is(source, "bpmn:EventBasedGateway") && source.tokenCount && source.tokenCount[processInstanceId]) {
          eventBasedGatewaysHaveTokens.push(source);
        }
      });
      var outgoingSequenceFlows = element.outgoing.filter(function(outgoing) {
        return is(outgoing, "bpmn:SequenceFlow");
      });
      if (!incomingSequenceFlows.length || !outgoingSequenceFlows.length) {
        return;
      }
      var self = this;
      var contextPad;
      if (element.tokenCount && element.tokenCount[processInstanceId]) {
        contextPad = domify('<div class="context-pad" title="Trigger Event"><i class="fa fa-play"></i></div>');
        domEvent.bind(contextPad, "click", function() {
          element.tokenCount[processInstanceId]--;
          self._eventBus.fire(GENERATE_TOKEN_EVENT, {
            element,
            processInstanceId
          });
        });
      } else if (eventBasedGatewaysHaveTokens.length) {
        contextPad = domify('<div class="context-pad" title="Trigger Event"><i class="fa fa-play"></i></div>');
        domEvent.bind(contextPad, "click", function() {
          eventBasedGatewaysHaveTokens.forEach(function(eventBasedGateway) {
            eventBasedGateway.tokenCount[processInstanceId]--;
          });
          self._eventBus.fire(GENERATE_TOKEN_EVENT, {
            element,
            processInstanceId
          });
        });
      } else {
        return;
      }
      return [{
        element,
        html: contextPad
      }];
    };
    IntermeditateCatchEventHandler.$inject = ["eventBus"];
    module.exports = IntermeditateCatchEventHandler;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/context-pads/handler/ProcessHandler.js
var require_ProcessHandler = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/context-pads/handler/ProcessHandler.js"(exports, module) {
    "use strict";
    var domify = require_domify2();
    var domEvent = require_event();
    function ProcessHandler(processInstances, processInstanceSettings) {
      this._processInstances = processInstances;
      this._processInstanceSettings = processInstanceSettings;
    }
    ProcessHandler.prototype.createContextPads = function(element) {
      var self = this;
      var processInstances = this._processInstances.getProcessInstances(element).filter(function(processInstance) {
        return !processInstance.isFinished;
      });
      if (processInstances.length < 2) {
        return;
      }
      var contextPad = domify('<div class="context-pad" title="View Process Instances"><i class="fa fa-list-ol"></i></div>');
      domEvent.bind(contextPad, "click", function() {
        self._processInstanceSettings.showNext(element);
      });
      return [{
        element,
        html: contextPad
      }];
    };
    ProcessHandler.$inject = ["processInstances", "processInstanceSettings"];
    module.exports = ProcessHandler;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/context-pads/handler/StartEventHandler.js
var require_StartEventHandler = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/context-pads/handler/StartEventHandler.js"(exports, module) {
    "use strict";
    var domify = require_domify2();
    var domEvent = require_event();
    var is = require_ElementHelper().is;
    var events = require_EventHelper();
    var GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT;
    function StartEventHandler(eventBus, elementRegistry, animation) {
      this._eventBus = eventBus;
      this._elementRegistry = elementRegistry;
      this._animation = animation;
    }
    StartEventHandler.prototype.createContextPads = function(element) {
      var tokens = false;
      this._elementRegistry.forEach(function(element2) {
        if (element2.tokenCount) {
          Object.values(element2.tokenCount).forEach(function(tokenCount) {
            if (tokenCount) {
              tokens = true;
            }
          });
        }
      });
      if (is(element.parent, "bpmn:SubProcess") || tokens || this._animation.animations.length) {
        return;
      }
      var self = this;
      var contextPad = domify('<div class="context-pad"><i class="fa fa-play"></i></div>');
      domEvent.bind(contextPad, "click", function() {
        self._eventBus.fire(GENERATE_TOKEN_EVENT, {
          element
        });
      });
      return [{
        element,
        html: contextPad
      }];
    };
    StartEventHandler.$inject = ["eventBus", "elementRegistry", "animation"];
    module.exports = StartEventHandler;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/context-pads/ContextPads.js
var require_ContextPads = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/context-pads/ContextPads.js"(exports, module) {
    "use strict";
    var elementHelper = require_ElementHelper();
    var isAncestor = elementHelper.isAncestor;
    var events = require_EventHelper();
    var TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;
    var RESET_SIMULATION_EVENT = events.RESET_SIMULATION_EVENT;
    var TERMINATE_EVENT = events.TERMINATE_EVENT;
    var UPDATE_ELEMENTS_EVENT = events.UPDATE_ELEMENTS_EVENT;
    var UPDATE_ELEMENT_EVENT = events.UPDATE_ELEMENT_EVENT;
    var PROCESS_INSTANCE_SHOWN_EVENT = events.PROCESS_INSTANCE_SHOWN_EVENT;
    var BoundaryEventHandler = require_BoundaryEventHandler();
    var ExclusiveGatewayHandler = require_ExclusiveGatewayHandler();
    var IntermediateCatchEventHandler = require_IntermediateCatchEventHandler();
    var ProcessHandler = require_ProcessHandler();
    var StartEventHandler = require_StartEventHandler();
    var LOW_PRIORITY = 500;
    var OFFSET_TOP = -15;
    var OFFSET_LEFT = -15;
    function ContextPads(eventBus, elementRegistry, overlays, injector, canvas, processInstances) {
      var self = this;
      this._elementRegistry = elementRegistry;
      this._overlays = overlays;
      this._injector = injector;
      this._canvas = canvas;
      this._processInstances = processInstances;
      this.overlayIds = {};
      this.handlers = {};
      this.registerHandler("bpmn:ExclusiveGateway", ExclusiveGatewayHandler);
      this.registerHandler("bpmn:IntermediateCatchEvent", IntermediateCatchEventHandler);
      this.registerHandler("bpmn:SubProcess", ProcessHandler);
      this.registerHandler("bpmn:SubProcess", BoundaryEventHandler);
      this.registerHandler("bpmn:StartEvent", StartEventHandler);
      eventBus.on(TOGGLE_MODE_EVENT, LOW_PRIORITY, function(context) {
        var simulationModeActive = context.simulationModeActive;
        if (simulationModeActive) {
          self.openContextPads();
        } else {
          self.closeContextPads();
        }
      });
      eventBus.on(RESET_SIMULATION_EVENT, LOW_PRIORITY, function() {
        self.closeContextPads();
        self.openContextPads();
      });
      eventBus.on(TERMINATE_EVENT, LOW_PRIORITY, function(context) {
        var element = context.element, parent = element.parent;
        self.closeContextPads(parent);
      });
      eventBus.on(UPDATE_ELEMENTS_EVENT, LOW_PRIORITY, function(context) {
        var elements = context.elements;
        elements.forEach(function(element) {
          self.closeElementContextPads(element);
          self.openElementContextPads(element);
        });
      });
      eventBus.on(UPDATE_ELEMENT_EVENT, LOW_PRIORITY, function(context) {
        var element = context.element;
        self.closeElementContextPads(element);
        self.openElementContextPads(element);
      });
      eventBus.on(PROCESS_INSTANCE_SHOWN_EVENT, function(context) {
        var processInstanceId = context.processInstanceId;
        var processInstance = processInstances.getProcessInstance(processInstanceId), parent = processInstance.parent;
        self.closeContextPads(parent);
        self.openContextPads(parent);
      });
    }
    ContextPads.prototype.registerHandler = function(type, handlerCls) {
      var handler = this._injector.instantiate(handlerCls);
      if (!this.handlers[type]) {
        this.handlers[type] = [];
      }
      this.handlers[type].push(handler);
    };
    ContextPads.prototype.openContextPads = function(parent) {
      var self = this;
      if (!parent) {
        parent = this._canvas.getRootElement();
      }
      this._elementRegistry.forEach(function(element) {
        if (self.handlers[element.type] && isAncestor(parent, element)) {
          self.openElementContextPads(element);
        }
      });
    };
    ContextPads.prototype.openElementContextPads = function(element) {
      if (!this.handlers[element.type]) {
        return;
      }
      var elementContextPads = [];
      this.handlers[element.type].forEach(function(handler) {
        var contextPads = handler.createContextPads(element);
        if (contextPads) {
          contextPads.forEach(function(contextPad) {
            if (contextPad) {
              elementContextPads.push(contextPad);
            }
          });
        }
      });
      var self = this;
      elementContextPads.forEach(function(contextPad) {
        var position = { top: OFFSET_TOP, left: OFFSET_LEFT };
        var overlayId = self._overlays.add(contextPad.element, "context-menu", {
          position,
          html: contextPad.html,
          show: {
            minZoom: 0.5
          }
        });
        self.overlayIds[contextPad.element.id] = overlayId;
      });
    };
    ContextPads.prototype.closeContextPads = function(parent) {
      var self = this;
      if (!parent) {
        parent = this._canvas.getRootElement();
      }
      this._elementRegistry.forEach(function(element) {
        if (isAncestor(parent, element)) {
          self.closeElementContextPads(element);
        }
      });
    };
    ContextPads.prototype.closeElementContextPads = function(element) {
      var self = this;
      if (element.attachers && element.attachers.length > 0) {
        element.attachers.forEach(function(attachedElement) {
          self.closeElementContextPads(attachedElement);
        });
      }
      if (element.children && element.children.length > 0) {
        element.children.forEach(function(child) {
          self.closeElementContextPads(child);
        });
      }
      var overlayId = this.overlayIds[element.id];
      if (!overlayId) {
        return;
      }
      this._overlays.remove(overlayId);
      delete this.overlayIds[element.id];
    };
    ContextPads.$inject = ["eventBus", "elementRegistry", "overlays", "injector", "canvas", "processInstances"];
    module.exports = ContextPads;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/context-pads/index.js
var require_context_pads = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/context-pads/index.js"(exports, module) {
    module.exports = require_ContextPads();
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/disable-modeling/DisableModeling.js
var require_DisableModeling = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/disable-modeling/DisableModeling.js"(exports, module) {
    "use strict";
    var events = require_EventHelper();
    var TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;
    var HIGH_PRIORITY = 10001;
    function DisableModeling(eventBus, contextPad, dragging, directEditing, editorActions, modeling, palette, paletteProvider) {
      var self = this;
      this._eventBus = eventBus;
      this.modelingDisabled = false;
      eventBus.on(TOGGLE_MODE_EVENT, HIGH_PRIORITY, function(context) {
        var simulationModeActive = context.simulationModeActive;
        self.modelingDisabled = simulationModeActive;
        if (self.modelingDisabled) {
          directEditing.cancel();
          contextPad.close();
          dragging.cancel();
        }
        palette._update();
      });
      function intercept(obj, fnName, cb) {
        var fn = obj[fnName];
        obj[fnName] = function() {
          return cb.call(this, fn, arguments);
        };
      }
      function ignoreIfModelingDisabled(obj, fnName) {
        intercept(obj, fnName, function(fn, args) {
          if (self.modelingDisabled) {
            return;
          }
          return fn.apply(this, args);
        });
      }
      function throwIfModelingDisabled(obj, fnName) {
        intercept(obj, fnName, function(fn, args) {
          if (self.modelingDisabled) {
            throw new Error("model is read-only");
          }
          return fn.apply(this, args);
        });
      }
      ignoreIfModelingDisabled(contextPad, "open");
      ignoreIfModelingDisabled(dragging, "init");
      ignoreIfModelingDisabled(directEditing, "activate");
      ignoreIfModelingDisabled(dragging, "init");
      ignoreIfModelingDisabled(directEditing, "activate");
      throwIfModelingDisabled(modeling, "moveShape");
      throwIfModelingDisabled(modeling, "updateAttachment");
      throwIfModelingDisabled(modeling, "moveElements");
      throwIfModelingDisabled(modeling, "moveConnection");
      throwIfModelingDisabled(modeling, "layoutConnection");
      throwIfModelingDisabled(modeling, "createConnection");
      throwIfModelingDisabled(modeling, "createShape");
      throwIfModelingDisabled(modeling, "createLabel");
      throwIfModelingDisabled(modeling, "appendShape");
      throwIfModelingDisabled(modeling, "removeElements");
      throwIfModelingDisabled(modeling, "distributeElements");
      throwIfModelingDisabled(modeling, "removeShape");
      throwIfModelingDisabled(modeling, "removeConnection");
      throwIfModelingDisabled(modeling, "replaceShape");
      throwIfModelingDisabled(modeling, "pasteElements");
      throwIfModelingDisabled(modeling, "alignElements");
      throwIfModelingDisabled(modeling, "resizeShape");
      throwIfModelingDisabled(modeling, "createSpace");
      throwIfModelingDisabled(modeling, "updateWaypoints");
      throwIfModelingDisabled(modeling, "reconnectStart");
      throwIfModelingDisabled(modeling, "reconnectEnd");
      intercept(editorActions, "trigger", function(fn, args) {
        var action = args[0];
        if (self.modelingDisabled && isAnyAction([
          "undo",
          "redo",
          "copy",
          "paste",
          "removeSelection",
          "spaceTool",
          "lassoTool",
          "globalConnectTool",
          "distributeElements",
          "alignElements",
          "directEditing"
        ], action)) {
          return;
        }
        return fn.apply(this, args);
      });
    }
    DisableModeling.$inject = [
      "eventBus",
      "contextPad",
      "dragging",
      "directEditing",
      "editorActions",
      "modeling",
      "palette",
      "paletteProvider"
    ];
    module.exports = DisableModeling;
    function isAnyAction(actions, action) {
      return actions.indexOf(action) > -1;
    }
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/disable-modeling/index.js
var require_disable_modeling = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/disable-modeling/index.js"(exports, module) {
    module.exports = require_DisableModeling();
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/element-notifications/ElementNotifications.js
var require_ElementNotifications = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/element-notifications/ElementNotifications.js"(exports, module) {
    "use strict";
    var domify = require_domify2();
    var events = require_EventHelper();
    var TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;
    var RESET_SIMULATION_EVENT = events.RESET_SIMULATION_EVENT;
    var GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT;
    var OFFSET_TOP = -15;
    var OFFSET_RIGHT = 15;
    function ElementNotifications(overlays, eventBus) {
      var self = this;
      this._overlays = overlays;
      eventBus.on(TOGGLE_MODE_EVENT, function(context) {
        var simulationModeActive = context.simulationModeActive;
        if (!simulationModeActive) {
          self.removeElementNotifications();
        }
      });
      eventBus.on([
        RESET_SIMULATION_EVENT,
        GENERATE_TOKEN_EVENT
      ], function() {
        self.removeElementNotifications();
      });
    }
    ElementNotifications.prototype.addElementNotifications = function(elements, options) {
      var self = this;
      elements.forEach(function(element) {
        self.addElementNotification(element, options);
      });
    };
    ElementNotifications.prototype.addElementNotification = function(element, options) {
      var position = {
        top: OFFSET_TOP,
        right: OFFSET_RIGHT
      };
      var markup = '<div class="element-notification ' + (options.type || "") + '">' + (options.icon ? '<i class="fa ' + options.icon + '"></i>' : "") + ('<span class="text">' + options.text + "</span>" || "") + "</div>";
      var html = domify(markup);
      this._overlays.add(element, "element-notification", {
        position,
        html,
        show: {
          minZoom: 0.5
        }
      });
    };
    ElementNotifications.prototype.removeElementNotifications = function(elements) {
      var self = this;
      if (!elements) {
        this._overlays.remove({ type: "element-notification" });
      } else {
        elements.forEach(function(element) {
          self.removeElementNotification(element);
        });
      }
    };
    ElementNotifications.prototype.removeElementNotification = function(element) {
      this._overlays.remove({ element });
    };
    ElementNotifications.$inject = ["overlays", "eventBus"];
    module.exports = ElementNotifications;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/element-notifications/index.js
var require_element_notifications = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/element-notifications/index.js"(exports, module) {
    module.exports = require_ElementNotifications();
  }
});

// node_modules/component-indexof/index.js
var require_component_indexof = __commonJS({
  "node_modules/component-indexof/index.js"(exports, module) {
    module.exports = function(arr, obj) {
      if (arr.indexOf)
        return arr.indexOf(obj);
      for (var i = 0; i < arr.length; ++i) {
        if (arr[i] === obj)
          return i;
      }
      return -1;
    };
  }
});

// node_modules/component-classes/index.js
var require_component_classes = __commonJS({
  "node_modules/component-classes/index.js"(exports, module) {
    try {
      index = require_component_indexof();
    } catch (err) {
      index = require_component_indexof();
    }
    var index;
    var re = /\s+/;
    var toString = Object.prototype.toString;
    module.exports = function(el) {
      return new ClassList(el);
    };
    function ClassList(el) {
      if (!el || !el.nodeType) {
        throw new Error("A DOM element reference is required");
      }
      this.el = el;
      this.list = el.classList;
    }
    ClassList.prototype.add = function(name) {
      if (this.list) {
        this.list.add(name);
        return this;
      }
      var arr = this.array();
      var i = index(arr, name);
      if (!~i)
        arr.push(name);
      this.el.className = arr.join(" ");
      return this;
    };
    ClassList.prototype.remove = function(name) {
      if ("[object RegExp]" == toString.call(name)) {
        return this.removeMatching(name);
      }
      if (this.list) {
        this.list.remove(name);
        return this;
      }
      var arr = this.array();
      var i = index(arr, name);
      if (~i)
        arr.splice(i, 1);
      this.el.className = arr.join(" ");
      return this;
    };
    ClassList.prototype.removeMatching = function(re2) {
      var arr = this.array();
      for (var i = 0; i < arr.length; i++) {
        if (re2.test(arr[i])) {
          this.remove(arr[i]);
        }
      }
      return this;
    };
    ClassList.prototype.toggle = function(name, force) {
      if (this.list) {
        if ("undefined" !== typeof force) {
          if (force !== this.list.toggle(name, force)) {
            this.list.toggle(name);
          }
        } else {
          this.list.toggle(name);
        }
        return this;
      }
      if ("undefined" !== typeof force) {
        if (!force) {
          this.remove(name);
        } else {
          this.add(name);
        }
      } else {
        if (this.has(name)) {
          this.remove(name);
        } else {
          this.add(name);
        }
      }
      return this;
    };
    ClassList.prototype.array = function() {
      var className = this.el.getAttribute("class") || "";
      var str = className.replace(/^\s+|\s+$/g, "");
      var arr = str.split(re);
      if ("" === arr[0])
        arr.shift();
      return arr;
    };
    ClassList.prototype.has = ClassList.prototype.contains = function(name) {
      return this.list ? this.list.contains(name) : !!~index(this.array(), name);
    };
  }
});

// node_modules/bpmn-js-token-simulation/node_modules/min-dom/lib/classes.js
var require_classes = __commonJS({
  "node_modules/bpmn-js-token-simulation/node_modules/min-dom/lib/classes.js"(exports, module) {
    module.exports = require_component_classes();
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/element-support/ElementSupport.js
var require_ElementSupport = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/element-support/ElementSupport.js"(exports, module) {
    "use strict";
    var domClasses = require_classes();
    var elementHelper = require_ElementHelper();
    var is = elementHelper.is;
    var SUPPORTED_ELEMENTS = elementHelper.supportedElements;
    var events = require_EventHelper();
    var TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;
    var GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT;
    var IGNORED_ELEMENTS = [
      "bpmn:Process",
      "bpmn:Collaboration",
      "bpmn:Participant",
      "bpmn:Lane",
      "bpmn:TextAnnotation"
    ];
    function isLabel(element) {
      return element.labelTarget;
    }
    function ElementSupport(eventBus, elementRegistry, canvas, notifications, elementNotifications) {
      var self = this;
      this._eventBus = eventBus;
      this._elementRegistry = elementRegistry;
      this._elementNotifications = elementNotifications;
      this._notifications = notifications;
      this.canvasParent = canvas.getContainer().parentNode;
      eventBus.on(GENERATE_TOKEN_EVENT, 2e4, function(context) {
        var element = context.element;
        if (!is(element, "bpmn:StartEvent")) {
          return;
        }
        if (!self.allElementsSupported()) {
          self.showWarnings();
          domClasses(self.canvasParent).add("warning");
          return true;
        }
      });
      eventBus.on(TOGGLE_MODE_EVENT, function(context) {
        var simulationModeActive = context.simulationModeActive;
        if (!simulationModeActive) {
          domClasses(self.canvasParent).remove("warning");
        }
      });
    }
    ElementSupport.prototype.allElementsSupported = function() {
      var allElementsSupported = true;
      this._elementRegistry.forEach(function(element) {
        if (!is(element, IGNORED_ELEMENTS) && !is(element, SUPPORTED_ELEMENTS) && !isLabel(element)) {
          allElementsSupported = false;
        }
      });
      return allElementsSupported;
    };
    ElementSupport.prototype.showWarnings = function(elements) {
      var self = this;
      var warnings = [];
      this._elementRegistry.forEach(function(element) {
        if (!is(element, IGNORED_ELEMENTS) && !is(element, SUPPORTED_ELEMENTS) && !isLabel(element)) {
          self.showWarning(element);
          if (warnings.indexOf(element.type)) {
            self._notifications.showNotification(element.type + " not supported", "warning");
            warnings.push(element.type);
          }
        }
      });
    };
    ElementSupport.prototype.showWarning = function(element) {
      this._elementNotifications.addElementNotification(element, {
        type: "warning",
        icon: "fa-exclamation-triangle",
        text: "Not supported"
      });
    };
    ElementSupport.$inject = ["eventBus", "elementRegistry", "canvas", "notifications", "elementNotifications"];
    module.exports = ElementSupport;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/element-support/index.js
var require_element_support = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/element-support/index.js"(exports, module) {
    module.exports = require_ElementSupport();
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/exclusive-gateway-settings/ExclusiveGatewaySettings.js
var require_ExclusiveGatewaySettings = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/exclusive-gateway-settings/ExclusiveGatewaySettings.js"(exports, module) {
    "use strict";
    var is = require_ElementHelper().is;
    var events = require_EventHelper();
    var TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;
    var NOT_SELECTED_COLOR = getComputedStyle(document.documentElement).getPropertyValue("--token-simulation-grey-lighten-56");
    var SELECTED_COLOR = getComputedStyle(document.documentElement).getPropertyValue("--token-simulation-grey-darken-30");
    function getNext(gateway) {
      var outgoing = gateway.outgoing.filter(isSequenceFlow);
      var index = outgoing.indexOf(gateway.sequenceFlow);
      if (outgoing[index + 1]) {
        return outgoing[index + 1];
      } else {
        return outgoing[0];
      }
    }
    function isSequenceFlow(connection) {
      return is(connection, "bpmn:SequenceFlow");
    }
    function ExclusiveGatewaySettings(eventBus, elementRegistry, graphicsFactory) {
      var self = this;
      this._elementRegistry = elementRegistry;
      this._graphicsFactory = graphicsFactory;
      eventBus.on(TOGGLE_MODE_EVENT, function(context) {
        var simulationModeActive = context.simulationModeActive;
        if (!simulationModeActive) {
          self.resetSequenceFlows();
        } else {
          self.setSequenceFlowsDefault();
        }
      });
    }
    ExclusiveGatewaySettings.prototype.setSequenceFlowsDefault = function() {
      var self = this;
      var exclusiveGateways = this._elementRegistry.filter(function(element) {
        return is(element, "bpmn:ExclusiveGateway");
      });
      exclusiveGateways.forEach(function(exclusiveGateway) {
        if (exclusiveGateway.outgoing.filter(isSequenceFlow).length) {
          self.setSequenceFlow(
            exclusiveGateway,
            exclusiveGateway.outgoing.filter(isSequenceFlow)[0]
          );
        }
      });
    };
    ExclusiveGatewaySettings.prototype.resetSequenceFlows = function() {
      var self = this;
      var exclusiveGateways = this._elementRegistry.filter(function(element) {
        return is(element, "bpmn:ExclusiveGateway");
      });
      exclusiveGateways.forEach(function(exclusiveGateway) {
        if (exclusiveGateway.outgoing.filter(isSequenceFlow).length) {
          self.resetSequenceFlow(exclusiveGateway);
        }
      });
    };
    ExclusiveGatewaySettings.prototype.resetSequenceFlow = function(gateway) {
      if (gateway.sequenceFlow) {
        delete gateway.sequenceFlow;
      }
    };
    ExclusiveGatewaySettings.prototype.setSequenceFlow = function(gateway) {
      var self = this;
      var outgoing = gateway.outgoing.filter(isSequenceFlow);
      if (!outgoing.length) {
        return;
      }
      var sequenceFlow = gateway.sequenceFlow;
      if (sequenceFlow) {
        gateway.sequenceFlow = getNext(gateway);
      } else {
        gateway.sequenceFlow = outgoing[0];
      }
      gateway.outgoing.forEach(function(outgoing2) {
        if (outgoing2 === gateway.sequenceFlow) {
          self.setColor(outgoing2, SELECTED_COLOR);
        } else {
          self.setColor(outgoing2, NOT_SELECTED_COLOR);
        }
      });
    };
    ExclusiveGatewaySettings.prototype.setColor = function(sequenceFlow, color) {
      var businessObject = sequenceFlow.businessObject;
      businessObject.di.set("stroke", color);
      var gfx = this._elementRegistry.getGraphics(sequenceFlow);
      this._graphicsFactory.update("connection", sequenceFlow, gfx);
    };
    ExclusiveGatewaySettings.$inject = ["eventBus", "elementRegistry", "graphicsFactory"];
    module.exports = ExclusiveGatewaySettings;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/exclusive-gateway-settings/index.js
var require_exclusive_gateway_settings = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/exclusive-gateway-settings/index.js"(exports, module) {
    module.exports = require_ExclusiveGatewaySettings();
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/log/Log.js
var require_Log = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/log/Log.js"(exports, module) {
    "use strict";
    var domify = require_domify2();
    var domClasses = require_classes();
    var domEvent = require_event();
    var domQuery = require_query();
    var elementHelper = require_ElementHelper();
    var getBusinessObject = elementHelper.getBusinessObject;
    var is = elementHelper.is;
    var isTypedEvent = elementHelper.isTypedEvent;
    var events = require_EventHelper();
    var GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT;
    var CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT;
    var TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;
    var RESET_SIMULATION_EVENT = events.RESET_SIMULATION_EVENT;
    var PROCESS_INSTANCE_CREATED_EVENT = events.PROCESS_INSTANCE_CREATED_EVENT;
    function getElementName(element) {
      return element && element.businessObject.name;
    }
    function Log(eventBus, notifications, tokenSimulationPalette, canvas) {
      var self = this;
      this._notifications = notifications;
      this._tokenSimulationPalette = tokenSimulationPalette;
      this._canvas = canvas;
      this._init();
      eventBus.on(GENERATE_TOKEN_EVENT, function(context) {
        var element = context.element, elementName = getElementName(element);
        if (is(element, "bpmn:BusinessRuleTask")) {
          self.log(elementName || "Business Rule Task", "info", "bpmn-icon-business-rule");
        } else if (is(element, "bpmn:CallActivity")) {
          self.log(elementName || "Call Activity", "info", "bpmn-icon-call-activity");
        } else if (is(element, ["bpmn:IntermediateCatchEvent", "bpmn:IntermediateThrowEvent"])) {
          self.log(elementName || "Intermediate Event", "info", "bpmn-icon-intermediate-event-none");
        } else if (is(element, "bpmn:ManualTask")) {
          self.log(elementName || "Manual Task", "info", "bpmn-icon-manual");
        } else if (is(element, "bpmn:ScriptTask")) {
          self.log(elementName || "Script Task", "info", "bpmn-icon-script");
        } else if (is(element, "bpmn:ServiceTask")) {
          self.log(elementName || "Service Task", "info", "bpmn-icon-service");
        } else if (is(element, "bpmn:StartEvent")) {
          self.log(elementName || "Start Event", "info", "bpmn-icon-start-event-none");
        } else if (is(element, "bpmn:Task")) {
          self.log(elementName || "Task", "info", "bpmn-icon-task");
        } else if (is(element, "bpmn:UserTask")) {
          self.log(elementName || "User Task", "info", "bpmn-icon-user");
        } else if (is(element, "bpmn:ExclusiveGateway")) {
          if (element.outgoing.length < 2) {
            return;
          }
          var sequenceFlowName = getElementName(element.sequenceFlow);
          var text = elementName || "Gateway";
          if (sequenceFlowName) {
            text = text.concat(' <i class="fa fa-angle-right" aria-hidden="true"></i> ' + sequenceFlowName);
          }
          self.log(text, "info", "bpmn-icon-gateway-xor");
        }
      });
      eventBus.on(CONSUME_TOKEN_EVENT, function(context) {
        var element = context.element, elementName = getElementName(element);
        if (is(element, "bpmn:EndEvent")) {
          if (isTypedEvent(getBusinessObject(element), "bpmn:TerminateEventDefinition")) {
            self.log(elementName || "Terminate End Event", "info", "bpmn-icon-end-event-terminate");
          } else {
            self.log(elementName || "End Event", "info", "bpmn-icon-end-event-none");
          }
        }
      });
      eventBus.on(PROCESS_INSTANCE_CREATED_EVENT, function(context) {
        var processInstanceId = context.processInstanceId, parent = context.parent;
        if (is(parent, "bpmn:Process")) {
          self.log("Process " + processInstanceId + " started", "success", "fa-check");
        } else {
          self.log("Subprocess " + processInstanceId + " started", "info", "fa-check");
        }
      });
      eventBus.on(TOGGLE_MODE_EVENT, function(context) {
        var simulationModeActive = context.simulationModeActive;
        if (!simulationModeActive) {
          self.emptyLog();
          domClasses(self.container).add("hidden");
        }
      });
      eventBus.on(RESET_SIMULATION_EVENT, function(context) {
        self.emptyLog();
        domClasses(self.container).add("hidden");
      });
    }
    Log.prototype._init = function() {
      var self = this;
      this.container = domify(
        '<div class="token-simulation-log hidden"><div class="header"><i class="fa fa-align-left"></i><button class="close"><i class="fa fa-times" aria-hidden="true"></i></button></div><div class="content"><p class="entry placeholder">No Entries</p></div></div>'
      );
      this.placeholder = domQuery(".placeholder", this.container);
      this.content = domQuery(".content", this.container);
      domEvent.bind(this.content, "wheel", function(e) {
        e.stopPropagation();
      });
      domEvent.bind(this.content, "mousedown", function(e) {
        e.stopPropagation();
      });
      this.close = domQuery(".close", this.container);
      domEvent.bind(this.close, "click", function() {
        domClasses(self.container).add("hidden");
      });
      this.icon = domQuery(".fa-align-left", this.container);
      domEvent.bind(this.icon, "click", function() {
        domClasses(self.container).add("hidden");
      });
      this._canvas.getContainer().appendChild(this.container);
      this.paletteEntry = domify('<div class="entry" title="Show Simulation Log"><i class="fa fa-align-left"></i></div>');
      domEvent.bind(this.paletteEntry, "click", function() {
        domClasses(self.container).remove("hidden");
      });
      this._tokenSimulationPalette.addEntry(this.paletteEntry, 3);
    };
    Log.prototype.toggle = function() {
      var container = this.container;
      if (domClasses(container).has("hidden")) {
        domClasses(container).remove("hidden");
      } else {
        domClasses(container).add("hidden");
      }
    };
    Log.prototype.log = function(text, type, icon) {
      domClasses(this.placeholder).add("hidden");
      var date = /* @__PURE__ */ new Date();
      var dateString = date.toLocaleTimeString() + ":" + date.getUTCMilliseconds();
      this._notifications.showNotification(text, type, icon);
      var iconMarkup;
      if (!icon) {
        icon = "fa-info";
      }
      if (icon.includes("bpmn")) {
        iconMarkup = '<span class="icon ' + icon + '">';
      } else {
        iconMarkup = '<i class="icon fa ' + icon + '"></i>';
      }
      var logEntry = domify('<p class="entry ' + type + '"><span class="date">' + dateString + "</span>" + iconMarkup + "</span>" + text + "</p>");
      this.content.appendChild(logEntry);
      this.content.scrollTop = this.content.scrollHeight;
    };
    Log.prototype.emptyLog = function() {
      while (this.content.firstChild) {
        this.content.removeChild(this.content.firstChild);
      }
      this.placeholder = domify('<p class="entry placeholder">No Entries</p>');
      this.content.appendChild(this.placeholder);
    };
    Log.$inject = ["eventBus", "notifications", "tokenSimulationPalette", "canvas"];
    module.exports = Log;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/log/index.js
var require_log = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/log/index.js"(exports, module) {
    module.exports = require_Log();
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/notifications/Notifications.js
var require_Notifications = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/notifications/Notifications.js"(exports, module) {
    "use strict";
    var domify = require_domify2();
    var events = require_EventHelper();
    var TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;
    var NOTIFICATION_TIME_TO_LIVE = 2e3;
    function Notifications(eventBus, canvas) {
      var self = this;
      this._eventBus = eventBus;
      this._canvas = canvas;
      this._init();
      eventBus.on(TOGGLE_MODE_EVENT, function(context) {
        var simulationModeActive = context.simulationModeActive;
        if (!simulationModeActive) {
          self.removeAll();
        }
      });
    }
    Notifications.prototype._init = function() {
      this.container = domify('<div class="notifications"></div>');
      this._canvas.getContainer().appendChild(this.container);
    };
    Notifications.prototype.showNotification = function(text, type, icon) {
      var iconMarkup;
      if (!icon) {
        icon = "fa-info";
      }
      if (icon.includes("bpmn")) {
        iconMarkup = '<i class="' + icon + '"></i>';
      } else {
        iconMarkup = '<i class="fa ' + icon + '"></i>';
      }
      var notification = domify(
        '<div class="notification ' + type + '"><span class="icon">' + iconMarkup + "</span>" + text + "</div>"
      );
      this.container.appendChild(notification);
      while (this.container.children.length > 5) {
        this.container.children[0].remove();
      }
      setTimeout(function() {
        notification.remove();
      }, NOTIFICATION_TIME_TO_LIVE);
    };
    Notifications.prototype.removeAll = function() {
      while (this.container.children.length) {
        this.container.children[0].remove();
      }
    };
    Notifications.$inject = ["eventBus", "canvas"];
    module.exports = Notifications;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/notifications/index.js
var require_notifications = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/notifications/index.js"(exports, module) {
    module.exports = require_Notifications();
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/pause-simulation/PauseSimulation.js
var require_PauseSimulation = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/pause-simulation/PauseSimulation.js"(exports, module) {
    "use strict";
    var domify = require_domify2();
    var domClasses = require_classes();
    var domEvent = require_event();
    var events = require_EventHelper();
    var TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;
    var PLAY_SIMULATION_EVENT = events.PLAY_SIMULATION_EVENT;
    var PAUSE_SIMULATION_EVENT = events.PAUSE_SIMULATION_EVENT;
    var RESET_SIMULATION_EVENT = events.RESET_SIMULATION_EVENT;
    var ANIMATION_CREATED_EVENT = events.ANIMATION_CREATED_EVENT;
    var PROCESS_INSTANCE_CREATED_EVENT = events.PROCESS_INSTANCE_CREATED_EVENT;
    var PLAY_MARKUP = '<i class="fa fa-play"></i>';
    var PAUSE_MARKUP = '<i class="fa fa-pause"></i>';
    function PauseSimulation(eventBus, tokenSimulationPalette, notifications, canvas) {
      var self = this;
      this._eventBus = eventBus;
      this._tokenSimulationPalette = tokenSimulationPalette;
      this._notifications = notifications;
      this.canvasParent = canvas.getContainer().parentNode;
      this.isActive = false;
      this.isPaused = false;
      this._init();
      eventBus.on(PROCESS_INSTANCE_CREATED_EVENT, function(context) {
        var parent = context.parent;
        if (!parent.parent) {
          self.activate();
          self.unpause();
          notifications.showNotification("Start Simulation", "info");
        }
      });
      eventBus.on([
        RESET_SIMULATION_EVENT,
        TOGGLE_MODE_EVENT
      ], function() {
        self.deactivate();
        self.unpause();
      });
      eventBus.on(ANIMATION_CREATED_EVENT, function(context) {
        var animation = context.animation;
        if (self.isPaused) {
          animation.pause();
        }
      });
    }
    PauseSimulation.prototype._init = function() {
      this.paletteEntry = domify(
        '<div class="entry disabled" title="Play/Pause Simulation">' + PLAY_MARKUP + "</div>"
      );
      domEvent.bind(this.paletteEntry, "click", this.toggle.bind(this));
      this._tokenSimulationPalette.addEntry(this.paletteEntry, 1);
    };
    PauseSimulation.prototype.toggle = function() {
      if (!this.isActive) {
        return;
      }
      if (this.isPaused) {
        this.unpause();
      } else {
        this.pause();
      }
    };
    PauseSimulation.prototype.pause = function() {
      if (!this.isActive) {
        return;
      }
      domClasses(this.paletteEntry).remove("active");
      domClasses(this.canvasParent).add("paused");
      this.paletteEntry.innerHTML = PLAY_MARKUP;
      this._eventBus.fire(PAUSE_SIMULATION_EVENT);
      this._notifications.showNotification("Pause Simulation", "info");
      this.isPaused = true;
    };
    PauseSimulation.prototype.unpause = function() {
      if (!this.isActive) {
        return;
      }
      domClasses(this.paletteEntry).add("active");
      domClasses(this.canvasParent).remove("paused");
      this.paletteEntry.innerHTML = PAUSE_MARKUP;
      this._eventBus.fire(PLAY_SIMULATION_EVENT);
      this._notifications.showNotification("Play Simulation", "info");
      this.isPaused = false;
    };
    PauseSimulation.prototype.activate = function() {
      this.isActive = true;
      domClasses(this.paletteEntry).remove("disabled");
    };
    PauseSimulation.prototype.deactivate = function() {
      this.isActive = false;
      domClasses(this.paletteEntry).remove("active");
      domClasses(this.paletteEntry).add("disabled");
    };
    PauseSimulation.$inject = ["eventBus", "tokenSimulationPalette", "notifications", "canvas"];
    module.exports = PauseSimulation;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/pause-simulation/index.js
var require_pause_simulation = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/pause-simulation/index.js"(exports, module) {
    module.exports = require_PauseSimulation();
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/preserve-element-colors/PreserveElementColors.js
var require_PreserveElementColors = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/preserve-element-colors/PreserveElementColors.js"(exports, module) {
    "use strict";
    var events = require_EventHelper();
    var TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;
    var VERY_HIGH_PRIORITY = 5e4;
    function PreserveElementColors(eventBus, elementRegistry, graphicsFactory) {
      var self = this;
      this._elementRegistry = elementRegistry;
      this._graphicsFactory = graphicsFactory;
      this.elementColors = {};
      eventBus.on(TOGGLE_MODE_EVENT, VERY_HIGH_PRIORITY, function(context) {
        var simulationModeActive = context.simulationModeActive;
        if (!simulationModeActive) {
          self.resetColors();
        } else {
          self.preserveColors();
        }
      });
    }
    PreserveElementColors.prototype.preserveColors = function() {
      var self = this;
      this._elementRegistry.forEach(function(element) {
        self.elementColors[element.id] = {
          stroke: element.businessObject.di.get("stroke"),
          fill: element.businessObject.di.get("fill")
        };
        self.setColor(element, "#000", "#fff");
      });
    };
    PreserveElementColors.prototype.resetColors = function() {
      var self = this;
      this._elementRegistry.forEach(function(element) {
        if (self.elementColors[element.id]) {
          self.setColor(element, self.elementColors[element.id].stroke, self.elementColors[element.id].fill);
        }
      });
      this.elementColors = {};
    };
    PreserveElementColors.prototype.setColor = function(element, stroke, fill) {
      var businessObject = element.businessObject;
      businessObject.di.set("stroke", stroke);
      businessObject.di.set("fill", fill);
      var gfx = this._elementRegistry.getGraphics(element);
      var type = element.waypoints ? "connection" : "shape";
      this._graphicsFactory.update(type, element, gfx);
    };
    PreserveElementColors.$inject = ["eventBus", "elementRegistry", "graphicsFactory"];
    module.exports = PreserveElementColors;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/preserve-element-colors/index.js
var require_preserve_element_colors = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/preserve-element-colors/index.js"(exports, module) {
    module.exports = require_PreserveElementColors();
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/process-instance-ids/ProcessInstanceIds.js
var require_ProcessInstanceIds = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/process-instance-ids/ProcessInstanceIds.js"(exports, module) {
    "use strict";
    var events = require_EventHelper();
    var TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;
    var RESET_SIMULATION_EVENT = events.RESET_SIMULATION_EVENT;
    function ProcessInstanceIds(eventBus) {
      this.nextProcessInstanceId = 1;
      eventBus.on(TOGGLE_MODE_EVENT, this.reset.bind(this));
      eventBus.on(RESET_SIMULATION_EVENT, this.reset.bind(this));
    }
    ProcessInstanceIds.prototype.getNext = function() {
      var processInstanceId = this.nextProcessInstanceId;
      this.nextProcessInstanceId++;
      return processInstanceId;
    };
    ProcessInstanceIds.prototype.reset = function() {
      this.nextProcessInstanceId = 1;
    };
    ProcessInstanceIds.$inject = ["eventBus"];
    module.exports = ProcessInstanceIds;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/process-instance-ids/index.js
var require_process_instance_ids = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/process-instance-ids/index.js"(exports, module) {
    module.exports = require_ProcessInstanceIds();
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/process-instance-settings/ProcessInstanceSettings.js
var require_ProcessInstanceSettings = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/process-instance-settings/ProcessInstanceSettings.js"(exports, module) {
    "use strict";
    var events = require_EventHelper();
    var TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;
    var PROCESS_INSTANCE_CREATED_EVENT = events.PROCESS_INSTANCE_CREATED_EVENT;
    var PROCESS_INSTANCE_FINISHED_EVENT = events.PROCESS_INSTANCE_FINISHED_EVENT;
    var PROCESS_INSTANCE_SHOWN_EVENT = events.PROCESS_INSTANCE_SHOWN_EVENT;
    var PROCESS_INSTANCE_HIDDEN_EVENT = events.PROCESS_INSTANCE_HIDDEN_EVENT;
    var LOW_PRIORITY = 500;
    function ProcessInstanceSettings(animation, eventBus, processInstances, elementRegistry) {
      var self = this;
      this._animation = animation;
      this._eventBus = eventBus;
      this._processInstances = processInstances;
      this._elementRegistry = elementRegistry;
      this._eventBus.on(PROCESS_INSTANCE_CREATED_EVENT, LOW_PRIORITY, function(context) {
        var parent = context.parent, processInstanceId = context.processInstanceId;
        var processInstancesWithParent = processInstances.getProcessInstances(parent).filter(function(processInstance) {
          return !processInstance.isFinished;
        });
        if (processInstancesWithParent.length === 1) {
          self.showProcessInstance(processInstanceId, parent);
        } else if (processInstancesWithParent.length > 1) {
          self.hideProcessInstance(processInstanceId);
        }
      });
      this._eventBus.on(PROCESS_INSTANCE_FINISHED_EVENT, LOW_PRIORITY, function(context) {
        var parent = context.parent, processInstanceId = context.processInstanceId;
        var processInstancesWithParent = processInstances.getProcessInstances(parent).filter(function(processInstance) {
          return processInstanceId !== processInstance.processInstanceId && !processInstance.isFinished;
        });
        if (processInstancesWithParent.length && processInstanceId === parent.shownProcessInstance) {
          self.showProcessInstance(processInstancesWithParent[0].processInstanceId, parent);
        } else {
          delete parent.shownProcessInstance;
        }
        if (!parent.parent) {
          elementRegistry.forEach(function(element) {
            delete element.shownProcessInstance;
          });
        }
      });
      eventBus.on(TOGGLE_MODE_EVENT, function() {
        elementRegistry.forEach(function(element) {
          delete element.shownProcessInstance;
        });
      });
    }
    ProcessInstanceSettings.prototype.showProcessInstance = function(processInstanceId, parent) {
      this._animation.showProcessInstanceAnimations(processInstanceId);
      parent.shownProcessInstance = processInstanceId;
      this._eventBus.fire(PROCESS_INSTANCE_SHOWN_EVENT, {
        processInstanceId
      });
    };
    ProcessInstanceSettings.prototype.hideProcessInstance = function(processInstanceId) {
      this._animation.hideProcessInstanceAnimations(processInstanceId);
      this._eventBus.fire(PROCESS_INSTANCE_HIDDEN_EVENT, {
        processInstanceId
      });
    };
    ProcessInstanceSettings.prototype.showNext = function(parent) {
      var self = this;
      var processInstancesWithParent = this._processInstances.getProcessInstances(parent);
      var shownProcessInstance = parent.shownProcessInstance;
      var indexOfShownProcessInstance = 0;
      for (let i = 0; i < processInstancesWithParent.length; i++) {
        if (processInstancesWithParent[i].processInstanceId === shownProcessInstance) {
          break;
        } else {
          indexOfShownProcessInstance++;
        }
      }
      processInstancesWithParent.forEach(function(processInstance) {
        self.hideProcessInstance(processInstance.processInstanceId);
      });
      if (indexOfShownProcessInstance === processInstancesWithParent.length - 1) {
        this.showProcessInstance(processInstancesWithParent[0].processInstanceId, parent);
      } else {
        this.showProcessInstance(processInstancesWithParent[indexOfShownProcessInstance + 1].processInstanceId, parent);
      }
    };
    ProcessInstanceSettings.$inject = ["animation", "eventBus", "processInstances", "elementRegistry"];
    module.exports = ProcessInstanceSettings;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/process-instance-settings/index.js
var require_process_instance_settings = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/process-instance-settings/index.js"(exports, module) {
    module.exports = require_ProcessInstanceSettings();
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/process-instances/ProcessInstances.js
var require_ProcessInstances = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/process-instances/ProcessInstances.js"(exports, module) {
    "use strict";
    var events = require_EventHelper();
    var TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;
    var RESET_SIMULATION_EVENT = events.RESET_SIMULATION_EVENT;
    var PROCESS_INSTANCE_CREATED_EVENT = events.PROCESS_INSTANCE_CREATED_EVENT;
    var PROCESS_INSTANCE_FINISHED_EVENT = events.PROCESS_INSTANCE_FINISHED_EVENT;
    function ProcessInstances(eventBus, processInstanceIds) {
      var self = this;
      this._eventBus = eventBus;
      this._processInstanceIds = processInstanceIds;
      this.processInstances = [];
      eventBus.on([TOGGLE_MODE_EVENT, RESET_SIMULATION_EVENT], function() {
        self.processInstances = [];
      });
    }
    ProcessInstances.prototype.create = function(parent, parentProcessInstanceId) {
      var processInstanceId = this._processInstanceIds.getNext();
      var processInstance = {
        parent,
        processInstanceId,
        parentProcessInstanceId
      };
      this.processInstances.push(processInstance);
      this._eventBus.fire(PROCESS_INSTANCE_CREATED_EVENT, processInstance);
      return processInstanceId;
    };
    ProcessInstances.prototype.remove = function(processInstanceId) {
      this.processInstances = this.processInstances.filter(function(processInstance) {
        return processInstance.processInstanceId !== processInstanceId;
      });
    };
    ProcessInstances.prototype.finish = function(processInstanceId) {
      var processInstance = this.processInstances.find(function(processInstance2) {
        return processInstance2.processInstanceId === processInstanceId;
      });
      this._eventBus.fire(PROCESS_INSTANCE_FINISHED_EVENT, processInstance);
      processInstance.isFinished = true;
    };
    ProcessInstances.prototype.getProcessInstances = function(parent, options) {
      if (!parent) {
        return this.processInstances;
      }
      var processInstances = this.processInstances.filter(function(processInstance) {
        return processInstance.parent === parent;
      });
      if (options && options.includeFinished !== true) {
        processInstances = processInstances.filter(function(processInstance) {
          return !processInstance.isFinished;
        });
      }
      return processInstances;
    };
    ProcessInstances.prototype.getProcessInstance = function(processInstanceId) {
      return this.processInstances.find(function(processInstance) {
        return processInstance.processInstanceId === processInstanceId;
      });
    };
    ProcessInstances.$inject = ["eventBus", "processInstanceIds"];
    module.exports = ProcessInstances;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/process-instances/index.js
var require_process_instances = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/process-instances/index.js"(exports, module) {
    module.exports = require_ProcessInstances();
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/reset-simulation/ResetSimulation.js
var require_ResetSimulation = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/reset-simulation/ResetSimulation.js"(exports, module) {
    "use strict";
    var domify = require_domify2();
    var domClasses = require_classes();
    var domEvent = require_event();
    var is = require_ElementHelper().is;
    var events = require_EventHelper();
    var TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;
    var GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT;
    var RESET_SIMULATION_EVENT = events.RESET_SIMULATION_EVENT;
    function ResetSimulation(eventBus, tokenSimulationPalette, notifications, elementRegistry) {
      var self = this;
      this._eventBus = eventBus;
      this._tokenSimulationPalette = tokenSimulationPalette;
      this._notifications = notifications;
      this._elementRegistry = elementRegistry;
      this._init();
      eventBus.on(GENERATE_TOKEN_EVENT, function(context) {
        if (!is(context.element, "bpmn:StartEvent")) {
          return;
        }
        domClasses(self.paletteEntry).remove("disabled");
      });
      eventBus.on(TOGGLE_MODE_EVENT, function(context) {
        var simulationModeActive = context.simulationModeActive;
        if (!simulationModeActive) {
          self.resetSimulation();
        }
      });
    }
    ResetSimulation.prototype._init = function() {
      var self = this;
      this.paletteEntry = domify('<div class="entry disabled" title="Reset Simulation"><i class="fa fa-refresh"></i></div>');
      domEvent.bind(this.paletteEntry, "click", function() {
        self.resetSimulation();
        self._notifications.showNotification("Reset Simulation", "info");
      });
      this._tokenSimulationPalette.addEntry(this.paletteEntry, 2);
    };
    ResetSimulation.prototype.resetSimulation = function() {
      domClasses(this.paletteEntry).add("disabled");
      this._elementRegistry.forEach(function(element) {
        if (element.tokenCount !== void 0) {
          delete element.tokenCount;
        }
      });
      this._eventBus.fire(RESET_SIMULATION_EVENT);
    };
    ResetSimulation.$inject = ["eventBus", "tokenSimulationPalette", "notifications", "elementRegistry"];
    module.exports = ResetSimulation;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/reset-simulation/index.js
var require_reset_simulation = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/reset-simulation/index.js"(exports, module) {
    module.exports = require_ResetSimulation();
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/set-animation-speed/SetAnimationSpeed.js
var require_SetAnimationSpeed = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/set-animation-speed/SetAnimationSpeed.js"(exports, module) {
    "use strict";
    var domify = require_domify2();
    var domClasses = require_classes();
    var domEvent = require_event();
    var domQuery = require_query();
    var events = require_EventHelper();
    var TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;
    function SetAnimationSpeed(canvas, animation, eventBus) {
      var self = this;
      this._canvas = canvas;
      this._animation = animation;
      this._eventBus = eventBus;
      this._init();
      eventBus.on(TOGGLE_MODE_EVENT, function(context) {
        var simulationModeActive = context.simulationModeActive;
        if (!simulationModeActive) {
          domClasses(self.container).add("hidden");
        } else {
          domClasses(self.container).remove("hidden");
        }
      });
    }
    SetAnimationSpeed.prototype._init = function() {
      var self = this;
      this.container = domify(
        '<div class="set-animation-speed hidden"><i title="Set Animation Speed" class="fa fa-tachometer" aria-hidden="true"></i><div class="animation-speed-buttons"><div title="Slow" id="animation-speed-1" class="animation-speed-button"><i class="fa fa-angle-right" aria-hidden="true"></i></div><div title="Normal" id="animation-speed-2" class="animation-speed-button active"><i class="fa fa-angle-right" aria-hidden="true"></i><i class="fa fa-angle-right" aria-hidden="true"></i></div><div title="Fast" id="animation-speed-3" class="animation-speed-button"><i class="fa fa-angle-right" aria-hidden="true"></i><i class="fa fa-angle-right" aria-hidden="true"></i><i class="fa fa-angle-right" aria-hidden="true"></i></div></div></div>'
      );
      var speed1 = domQuery("#animation-speed-1", this.container), speed2 = domQuery("#animation-speed-2", this.container), speed3 = domQuery("#animation-speed-3", this.container);
      domEvent.bind(speed1, "click", function() {
        self.setActive(speed1);
        self._animation.setAnimationSpeed(0.5);
      });
      domEvent.bind(speed2, "click", function() {
        self.setActive(speed2);
        self._animation.setAnimationSpeed(1);
      });
      domEvent.bind(speed3, "click", function() {
        self.setActive(speed3);
        self._animation.setAnimationSpeed(1.5);
      });
      this._canvas.getContainer().appendChild(this.container);
    };
    SetAnimationSpeed.prototype.setActive = function(element) {
      domQuery.all(".animation-speed-button", this.container).forEach(function(button) {
        domClasses(button).remove("active");
      });
      domClasses(element).add("active");
    };
    SetAnimationSpeed.$inject = ["canvas", "animation", "eventBus"];
    module.exports = SetAnimationSpeed;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/set-animation-speed/index.js
var require_set_animation_speed = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/set-animation-speed/index.js"(exports, module) {
    module.exports = require_SetAnimationSpeed();
  }
});

// node_modules/bpmn-js-token-simulation/node_modules/min-dom/lib/clear.js
var require_clear = __commonJS({
  "node_modules/bpmn-js-token-simulation/node_modules/min-dom/lib/clear.js"(exports, module) {
    module.exports = function(el) {
      var c;
      while (el.childNodes.length) {
        c = el.childNodes[0];
        el.removeChild(c);
      }
      return el;
    };
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/show-process-instance/ShowProcessInstance.js
var require_ShowProcessInstance = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/show-process-instance/ShowProcessInstance.js"(exports, module) {
    "use strict";
    var domify = require_domify2();
    var domClasses = require_classes();
    var domEvent = require_event();
    var domQuery = require_query();
    var domClear = require_clear();
    var events = require_EventHelper();
    var TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;
    var PROCESS_INSTANCE_CREATED_EVENT = events.PROCESS_INSTANCE_CREATED_EVENT;
    var PROCESS_INSTANCE_FINISHED_EVENT = events.PROCESS_INSTANCE_FINISHED_EVENT;
    var PROCESS_INSTANCE_SHOWN_EVENT = events.PROCESS_INSTANCE_SHOWN_EVENT;
    var PROCESS_INSTANCE_HIDDEN_EVENT = events.PROCESS_INSTANCE_HIDDEN_EVENT;
    var RESET_SIMULATION_EVENT = events.RESET_SIMULATION_EVENT;
    var FILL_COLOR = getComputedStyle(document.documentElement).getPropertyValue("--token-simulation-silver-base-97");
    var STROKE_COLOR = getComputedStyle(document.documentElement).getPropertyValue("--token-simulation-green-base-44");
    function isNull(value) {
      return value === null;
    }
    function ShowProcessInstance(eventBus, canvas, processInstanceSettings, processInstances, graphicsFactory, elementRegistry) {
      var self = this;
      this._eventBus = eventBus;
      this._canvas = canvas;
      this._processInstanceSettings = processInstanceSettings;
      this._processInstances = processInstances;
      this._graphicsFactory = graphicsFactory;
      this._elementRegistry = elementRegistry;
      this.highlightedElement = null;
      this._init();
      eventBus.on(TOGGLE_MODE_EVENT, function(context) {
        var simulationModeActive = context.simulationModeActive;
        if (!simulationModeActive) {
          domClasses(self.container).add("hidden");
          domClear(self.container);
          if (!isNull(self.highlightedElement)) {
            self.removeHighlightFromProcess(self.highlightedElement.element);
            self.highlightedElement = null;
          }
        } else {
          domClasses(self.container).remove("hidden");
        }
      });
      eventBus.on(PROCESS_INSTANCE_CREATED_EVENT, function(context) {
        self.addInstance(context);
      });
      eventBus.on(PROCESS_INSTANCE_FINISHED_EVENT, function(context) {
        self.removeInstance(context);
      });
      eventBus.on(PROCESS_INSTANCE_SHOWN_EVENT, function(context) {
        self.setInstanceShown(context.processInstanceId);
      });
      eventBus.on(PROCESS_INSTANCE_HIDDEN_EVENT, function(context) {
        self.setInstanceHidden(context.processInstanceId);
      });
      eventBus.on(RESET_SIMULATION_EVENT, function() {
        self.removeAllInstances();
      });
    }
    ShowProcessInstance.prototype._init = function() {
      this.container = domify('<div class="process-instances hidden"></div>');
      this._canvas.getContainer().appendChild(this.container);
    };
    ShowProcessInstance.prototype.addInstance = function(context) {
      var self = this;
      var processInstanceId = context.processInstanceId, parent = context.parent;
      var element = domify(
        '<div id="instance-' + processInstanceId + '" class="process-instance" title="View Process Instance ' + processInstanceId + '">' + processInstanceId + "</div>"
      );
      domEvent.bind(element, "click", function() {
        var processInstancesWithParent = self._processInstances.getProcessInstances(parent);
        processInstancesWithParent.forEach(function(processInstance) {
          self._processInstanceSettings.hideProcessInstance(processInstance.processInstanceId);
        });
        self._processInstanceSettings.showProcessInstance(processInstanceId, parent);
      });
      domEvent.bind(element, "mouseenter", function() {
        self.highlightedElement = {
          element: parent,
          stroke: parent.businessObject.di.get("stroke"),
          fill: parent.businessObject.di.get("fill")
        };
        self.addHighlightToProcess(parent);
      });
      domEvent.bind(element, "mouseleave", function() {
        self.removeHighlightFromProcess(parent);
        self.highlightedElement = null;
      });
      this.container.appendChild(element);
    };
    ShowProcessInstance.prototype.removeInstance = function(context) {
      var processInstanceId = context.processInstanceId;
      var element = domQuery("#instance-" + processInstanceId, this.container);
      if (element) {
        element.remove();
      }
    };
    ShowProcessInstance.prototype.removeAllInstances = function() {
      this.container.innerHTML = "";
    };
    ShowProcessInstance.prototype.setInstanceShown = function(processInstanceId) {
      var element = domQuery("#instance-" + processInstanceId, this.container);
      if (element) {
        domClasses(element).add("active");
      }
    };
    ShowProcessInstance.prototype.setInstanceHidden = function(processInstanceId) {
      var element = domQuery("#instance-" + processInstanceId, this.container);
      if (element) {
        domClasses(element).remove("active");
      }
    };
    ShowProcessInstance.prototype.addHighlightToProcess = function(element) {
      this.setColor(element, STROKE_COLOR, FILL_COLOR);
      if (!element.parent) {
        domClasses(this._canvas.getContainer()).add("highlight");
      }
    };
    ShowProcessInstance.prototype.removeHighlightFromProcess = function(element) {
      if (isNull(this.highlightedElement)) {
        return;
      }
      this.setColor(element, this.highlightedElement.stroke, this.highlightedElement.fill);
      if (!element.parent) {
        domClasses(this._canvas.getContainer()).remove("highlight");
      }
    };
    ShowProcessInstance.prototype.setColor = function(element, stroke, fill) {
      var businessObject = element.businessObject;
      businessObject.di.set("stroke", stroke);
      businessObject.di.set("fill", fill);
      var gfx = this._elementRegistry.getGraphics(element);
      this._graphicsFactory.update("connection", element, gfx);
    };
    ShowProcessInstance.$inject = [
      "eventBus",
      "canvas",
      "processInstanceSettings",
      "processInstances",
      "graphicsFactory",
      "elementRegistry"
    ];
    module.exports = ShowProcessInstance;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/show-process-instance/index.js
var require_show_process_instance = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/show-process-instance/index.js"(exports, module) {
    module.exports = require_ShowProcessInstance();
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/simulation-state/SimulationState.js
var require_SimulationState = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/simulation-state/SimulationState.js"(exports, module) {
    "use strict";
    var elementHelper = require_ElementHelper();
    var getBusinessObject = elementHelper.getBusinessObject;
    var is = elementHelper.is;
    var isAncestor = elementHelper.isAncestor;
    var isTypedEvent = elementHelper.isTypedEvent;
    var events = require_EventHelper();
    var CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT;
    var VERY_LOW_PRIORITY = 250;
    function SimulationState(eventBus, animation, elementRegistry, log, elementNotifications, canvas, processInstances) {
      this._animation = animation;
      this._elementRegistry = elementRegistry;
      this._log = log;
      this._elementNotifications = elementNotifications;
      this._canvas = canvas;
      this._processInstances = processInstances;
      eventBus.on(CONSUME_TOKEN_EVENT, VERY_LOW_PRIORITY, function() {
      });
    }
    SimulationState.prototype.isDeadlock = function() {
      var self = this;
      var hasTokens = [];
      this._elementRegistry.forEach(function(element) {
        if (element.tokenCount) {
          hasTokens.push(element);
        }
      });
      var cannotContinue = [];
      var hasTerminate = [];
      hasTokens.forEach(function(element) {
        var outgoingSequenceFlows = element.outgoing.filter(function(outgoing) {
          return is(outgoing, "bpmn:SequenceFlow");
        });
        if (!outgoingSequenceFlows.length) {
          cannotContinue.push(element);
        }
        if (is(element, "bpmn:ParallelGateway")) {
          var incomingSequenceFlows = element.incoming.filter(function(incoming) {
            return is(incoming, "bpmn:SequenceFlow");
          });
          if (incomingSequenceFlows.length > element.tokenCount) {
            cannotContinue.push(element);
          }
        }
        var visited = [];
        function checkIfHasTerminate(element2) {
          element2.outgoing.forEach(function(outgoing) {
            if (visited.indexOf(outgoing.target) !== -1) {
              return;
            }
            visited.push(outgoing.target);
            var isTerminate = isTypedEvent(getBusinessObject(outgoing.target), "bpmn:TerminateEventDefinition");
            if (isTerminate) {
              hasTerminate.push(element2);
            }
            checkIfHasTerminate(outgoing.target);
          });
        }
        checkIfHasTerminate(element);
      });
      if (hasTokens.length && !hasTerminate.length && cannotContinue.length && !this._animation.animations.length) {
        self._log.log("Deadlock", "warning", "fa-exclamation-triangle");
        cannotContinue.forEach(function(element) {
          self._elementNotifications.addElementNotification(element, {
            type: "warning",
            icon: "fa-exclamation-triangle",
            text: "Deadlock"
          });
        });
      }
    };
    SimulationState.prototype.isFinished = function(element, processInstanceId) {
      var processInstance = this._processInstances.getProcessInstance(processInstanceId);
      var parent = processInstance.parent;
      var hasTokens = false;
      if (!parent) {
        parent = this._canvas.getRootElement();
      }
      parent.children.forEach(function(element2) {
        if (element2.tokenCount && element2.tokenCount[processInstanceId] && element2.tokenCount[processInstanceId].length) {
          hasTokens = true;
        }
      });
      var hasAnimations = false;
      this._animation.animations.forEach(function(animation) {
        if (isAncestor(parent, animation.element) && animation.processInstanceId === processInstanceId) {
          hasAnimations = true;
        }
      });
      if (!hasTokens && !hasAnimations) {
        if (is(parent, "bpmn:SubProcess")) {
          this._log.log("Subprocess " + processInstanceId + " finished", "info", "fa-check-circle");
        } else {
          this._log.log("Process " + processInstanceId + " finished", "success", "fa-check-circle");
          this._elementNotifications.addElementNotification(element, {
            type: "success",
            icon: "fa-check-circle",
            text: "Finished"
          });
        }
        return true;
      }
    };
    SimulationState.$inject = [
      "eventBus",
      "animation",
      "elementRegistry",
      "log",
      "elementNotifications",
      "canvas",
      "processInstances"
    ];
    module.exports = SimulationState;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/simulation-state/index.js
var require_simulation_state = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/simulation-state/index.js"(exports, module) {
    module.exports = require_SimulationState();
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/toggle-mode/modeler/ToggleMode.js
var require_ToggleMode = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/toggle-mode/modeler/ToggleMode.js"(exports, module) {
    "use strict";
    var domify = require_domify2();
    var domClasses = require_classes();
    var domEvent = require_event();
    var domQuery = require_query();
    var events = require_EventHelper();
    var TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;
    function ToggleMode(eventBus, canvas, selection, contextPad) {
      var self = this;
      this._eventBus = eventBus;
      this._canvas = canvas;
      this._selection = selection;
      this._contextPad = contextPad;
      this.simulationModeActive = false;
      eventBus.on("import.done", function() {
        self.canvasParent = self._canvas.getContainer().parentNode;
        self.palette = domQuery(".djs-palette", self._canvas.getContainer());
        self._init();
      });
    }
    ToggleMode.prototype._init = function() {
      this.container = domify(`
    <div class="toggle-mode">
      Token Simulation <span class="toggle"><i class="fa fa-toggle-off"></i></span>
    </div>
  `);
      domEvent.bind(this.container, "click", this.toggleMode.bind(this));
      this._canvas.getContainer().appendChild(this.container);
    };
    ToggleMode.prototype.toggleMode = function() {
      if (this.simulationModeActive) {
        this.container.innerHTML = 'Token Simulation <span class="toggle"><i class="fa fa-toggle-off"></i></span>';
        domClasses(this.canvasParent).remove("simulation");
        domClasses(this.palette).remove("hidden");
        this._eventBus.fire(TOGGLE_MODE_EVENT, {
          simulationModeActive: false
        });
        var elements = this._selection.get();
        if (elements.length === 1) {
          this._contextPad.open(elements[0]);
        }
      } else {
        this.container.innerHTML = 'Token Simulation <span class="toggle"><i class="fa fa-toggle-on"></i></span>';
        domClasses(this.canvasParent).add("simulation");
        domClasses(this.palette).add("hidden");
        this._eventBus.fire(TOGGLE_MODE_EVENT, {
          simulationModeActive: true
        });
      }
      this.simulationModeActive = !this.simulationModeActive;
    };
    ToggleMode.$inject = ["eventBus", "canvas", "selection", "contextPad"];
    module.exports = ToggleMode;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/toggle-mode/modeler/index.js
var require_modeler = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/toggle-mode/modeler/index.js"(exports, module) {
    module.exports = require_ToggleMode();
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/token-count/TokenCount.js
var require_TokenCount = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/token-count/TokenCount.js"(exports, module) {
    "use strict";
    var domify = require_domify2();
    var elementHelper = require_ElementHelper();
    var isAncestor = elementHelper.isAncestor;
    var events = require_EventHelper();
    var TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;
    var GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT;
    var CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT;
    var RESET_SIMULATION_EVENT = events.RESET_SIMULATION_EVENT;
    var TERMINATE_EVENT = events.TERMINATE_EVENT;
    var PROCESS_INSTANCE_SHOWN_EVENT = events.PROCESS_INSTANCE_SHOWN_EVENT;
    var OFFSET_BOTTOM = 10;
    var OFFSET_LEFT = -15;
    var LOW_PRIORITY = 500;
    function TokenCount(eventBus, overlays, elementRegistry, canvas, processInstances) {
      var self = this;
      this._overlays = overlays;
      this._elementRegistry = elementRegistry;
      this._canvas = canvas;
      this._processInstances = processInstances;
      this.overlayIds = {};
      eventBus.on(TOGGLE_MODE_EVENT, function(context) {
        var simulationModeActive = context.simulationModeActive;
        if (!simulationModeActive) {
          self.removeTokenCounts();
        }
      });
      eventBus.on(RESET_SIMULATION_EVENT, function() {
        self.removeTokenCounts();
      });
      eventBus.on(TERMINATE_EVENT, function(context) {
        var element = context.element, parent = element.parent;
        self.removeTokenCounts(parent);
      });
      eventBus.on([GENERATE_TOKEN_EVENT, CONSUME_TOKEN_EVENT], LOW_PRIORITY, function(context) {
        var element = context.element, parent = element.parent;
        self.removeTokenCounts(parent);
        self.addTokenCounts(parent);
      });
      eventBus.on(PROCESS_INSTANCE_SHOWN_EVENT, function(context) {
        var processInstanceId = context.processInstanceId;
        var processInstance = processInstances.getProcessInstance(processInstanceId), parent = processInstance.parent;
        self.removeTokenCounts(parent);
        self.addTokenCounts(parent);
      });
    }
    TokenCount.prototype.addTokenCounts = function(parent) {
      var self = this;
      if (!parent) {
        parent = this._canvas.getRootElement();
      }
      var shownProcessInstance = parent.shownProcessInstance;
      if (!shownProcessInstance) {
        var processInstancesWithParent = this._processInstances.getProcessInstances(parent);
        if (!processInstancesWithParent.length) {
          return;
        }
        shownProcessInstance = processInstancesWithParent[0].processInstanceId;
      }
      this._elementRegistry.forEach(function(element) {
        if (isAncestor(parent, element)) {
          self.addTokenCount(element, shownProcessInstance);
        }
      });
    };
    TokenCount.prototype.addTokenCount = function(element, shownProcessInstance) {
      var tokenCount = element.tokenCount && element.tokenCount[shownProcessInstance];
      if (!tokenCount) {
        return;
      }
      var html = this.createTokenCount(tokenCount);
      var position = { bottom: OFFSET_BOTTOM, left: OFFSET_LEFT };
      var overlayId = this._overlays.add(element, "token-count", {
        position,
        html,
        show: {
          minZoom: 0.5
        }
      });
      this.overlayIds[element.id] = overlayId;
    };
    TokenCount.prototype.createTokenCount = function(tokenCount) {
      return domify('<div class="token-count waiting">' + tokenCount + "</div>");
    };
    TokenCount.prototype.removeTokenCounts = function(parent) {
      var self = this;
      if (!parent) {
        parent = this._canvas.getRootElement();
      }
      this._elementRegistry.forEach(function(element) {
        if (isAncestor(parent, element)) {
          self.removeTokenCount(element);
        }
      });
    };
    TokenCount.prototype.removeTokenCount = function(element) {
      var overlayId = this.overlayIds[element.id];
      if (!overlayId) {
        return;
      }
      this._overlays.remove(overlayId);
      delete this.overlayIds[element.id];
    };
    TokenCount.$inject = ["eventBus", "overlays", "elementRegistry", "canvas", "processInstances"];
    module.exports = TokenCount;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/token-count/index.js
var require_token_count = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/token-count/index.js"(exports, module) {
    module.exports = require_TokenCount();
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/EndEventHandler.js
var require_EndEventHandler = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/EndEventHandler.js"(exports, module) {
    "use strict";
    var elementHelper = require_ElementHelper();
    var getBusinessObject = elementHelper.getBusinessObject;
    var is = elementHelper.is;
    var isAncestor = elementHelper.isAncestor;
    var getDescendants = elementHelper.getDescendants;
    var isTypedEvent = elementHelper.isTypedEvent;
    var events = require_EventHelper();
    var GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT;
    var TERMINATE_EVENT = events.TERMINATE_EVENT;
    var UPDATE_ELEMENTS_EVENT = events.UPDATE_ELEMENTS_EVENT;
    function EndEventHandler(animation, eventBus, log, simulationState, elementRegistry, processInstances) {
      this._animation = animation;
      this._eventBus = eventBus;
      this._log = log;
      this._simulationState = simulationState;
      this._elementRegistry = elementRegistry;
      this._processInstances = processInstances;
    }
    EndEventHandler.prototype.consume = function(context) {
      var element = context.element, processInstanceId = context.processInstanceId;
      var isTerminate = isTypedEvent(getBusinessObject(element), "bpmn:TerminateEventDefinition"), isSubProcessChild = is(element.parent, "bpmn:SubProcess");
      if (isTerminate) {
        this._eventBus.fire(TERMINATE_EVENT, context);
        this._elementRegistry.forEach(function(e) {
          if (isAncestor(element.parent, e) && e.tokenCount && e.tokenCount[processInstanceId]) {
            delete e.tokenCount[processInstanceId];
          }
        });
        this._processInstances.finish(processInstanceId);
      }
      var isFinished = this._simulationState.isFinished(element, processInstanceId);
      if (isFinished) {
        this._processInstances.finish(processInstanceId);
      }
      if ((isFinished || isTerminate) && isSubProcessChild) {
        var processInstance = this._processInstances.getProcessInstance(processInstanceId);
        this._eventBus.fire(GENERATE_TOKEN_EVENT, {
          element: element.parent,
          processInstanceId: processInstance.parentProcessInstanceId
        });
      }
      this._eventBus.fire(UPDATE_ELEMENTS_EVENT, {
        elements: getDescendants(this._elementRegistry.getAll(), element.parent)
      });
    };
    EndEventHandler.prototype.generate = function(context) {
    };
    EndEventHandler.$inject = ["animation", "eventBus", "log", "simulationState", "elementRegistry", "processInstances"];
    module.exports = EndEventHandler;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/EventBasedGatewayHandler.js
var require_EventBasedGatewayHandler = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/EventBasedGatewayHandler.js"(exports, module) {
    "use strict";
    var is = require_ElementHelper().is;
    var events = require_EventHelper();
    var UPDATE_ELEMENTS_EVENT = events.UPDATE_ELEMENTS_EVENT;
    function ExclusiveGatewayHandler(eventBus, animation) {
      this._eventBus = eventBus;
      this._animation = animation;
    }
    ExclusiveGatewayHandler.prototype.consume = function(context) {
      var element = context.element, processInstanceId = context.processInstanceId;
      if (!element.tokenCount) {
        element.tokenCount = {};
      }
      if (!element.tokenCount[processInstanceId]) {
        element.tokenCount[processInstanceId] = 0;
      }
      element.tokenCount[processInstanceId]++;
      var outgoing = element.outgoing, events2 = [];
      outgoing.forEach(function(outgoing2) {
        var target = outgoing2.target;
        if (is(target, "bpmn:IntermediateCatchEvent")) {
          events2.push(target);
        }
      });
      this._eventBus.fire(UPDATE_ELEMENTS_EVENT, {
        elements: events2
      });
    };
    ExclusiveGatewayHandler.prototype.generate = function() {
    };
    ExclusiveGatewayHandler.$inject = ["eventBus", "animation"];
    module.exports = ExclusiveGatewayHandler;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/ExclusiveGatewayHandler.js
var require_ExclusiveGatewayHandler2 = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/ExclusiveGatewayHandler.js"(exports, module) {
    "use strict";
    var events = require_EventHelper();
    var CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT;
    var GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT;
    function ExclusiveGatewayHandler(eventBus, animation, elementRegistry) {
      this._eventBus = eventBus;
      this._animation = animation;
      this._elementRegistry = elementRegistry;
    }
    ExclusiveGatewayHandler.prototype.consume = function(context) {
      var element = context.element;
      if (!element.sequenceFlow) {
        throw new Error("no sequence flow configured for element " + element.id);
      }
      this._eventBus.fire(GENERATE_TOKEN_EVENT, context);
    };
    ExclusiveGatewayHandler.prototype.generate = function(context) {
      var element = context.element, processInstanceId = context.processInstanceId;
      if (!element.sequenceFlow) {
        throw new Error("no sequence flow configured for element " + element.id);
      }
      var self = this;
      var sequenceFlow = this._elementRegistry.get(element.sequenceFlow.id);
      this._animation.createAnimation(sequenceFlow, processInstanceId, function() {
        self._eventBus.fire(CONSUME_TOKEN_EVENT, {
          element: sequenceFlow.target,
          processInstanceId
        });
      });
    };
    ExclusiveGatewayHandler.$inject = ["eventBus", "animation", "elementRegistry"];
    module.exports = ExclusiveGatewayHandler;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/IntermediateCatchEventHandler.js
var require_IntermediateCatchEventHandler2 = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/IntermediateCatchEventHandler.js"(exports, module) {
    "use strict";
    var elementHelper = require_ElementHelper();
    var is = elementHelper.is;
    var events = require_EventHelper();
    var CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT;
    var UPDATE_ELEMENT_EVENT = events.UPDATE_ELEMENT_EVENT;
    var UPDATE_ELEMENTS_EVENT = events.UPDATE_ELEMENTS_EVENT;
    function IntermediateCatchEventHandler(animation, eventBus, elementRegistry) {
      this._animation = animation;
      this._eventBus = eventBus;
      this._elementRegistry = elementRegistry;
    }
    IntermediateCatchEventHandler.prototype.consume = function(context) {
      var element = context.element, processInstanceId = context.processInstanceId;
      if (!element.tokenCount) {
        element.tokenCount = {};
      }
      if (!element.tokenCount[processInstanceId]) {
        element.tokenCount[processInstanceId] = 0;
      }
      element.tokenCount[processInstanceId]++;
      this._eventBus.fire(UPDATE_ELEMENT_EVENT, {
        element
      });
    };
    IntermediateCatchEventHandler.prototype.generate = function(context) {
      var self = this;
      var element = context.element, processInstanceId = context.processInstanceId;
      var outgoingSequenceFlows = element.outgoing.filter(function(outgoing) {
        return is(outgoing, "bpmn:SequenceFlow");
      });
      outgoingSequenceFlows.forEach(function(connection) {
        self._animation.createAnimation(connection, processInstanceId, function() {
          self._eventBus.fire(CONSUME_TOKEN_EVENT, {
            element: connection.target,
            processInstanceId
          });
        });
      });
      var parent = element.parent;
      var events2 = this._elementRegistry.filter(function(element2) {
        return is(element2, "bpmn:IntermediateCatchEvent") && element2.parent === parent;
      });
      this._eventBus.fire(UPDATE_ELEMENTS_EVENT, {
        elements: events2
      });
    };
    IntermediateCatchEventHandler.$inject = ["animation", "eventBus", "elementRegistry"];
    module.exports = IntermediateCatchEventHandler;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/IntermediateThrowEventHandler.js
var require_IntermediateThrowEventHandler = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/IntermediateThrowEventHandler.js"(exports, module) {
    "use strict";
    var is = require_ElementHelper().is;
    var events = require_EventHelper();
    var CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT;
    var GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT;
    function IntermediateThrowEventHandler(animation, eventBus) {
      this._animation = animation;
      this._eventBus = eventBus;
    }
    IntermediateThrowEventHandler.prototype.consume = function(element) {
      this._eventBus.fire(GENERATE_TOKEN_EVENT, {
        element
      });
    };
    IntermediateThrowEventHandler.prototype.generate = function(element) {
      var self = this;
      var outgoingSequenceFlows = element.outgoing.filter(function(outgoing) {
        return is(outgoing, "bpmn:SequenceFlow");
      });
      outgoingSequenceFlows.forEach(function(connection) {
        self._animation.createAnimation(connection, function() {
          self._eventBus.fire(CONSUME_TOKEN_EVENT, {
            element: connection.target
          });
        });
      });
    };
    IntermediateThrowEventHandler.$inject = ["animation", "eventBus"];
    module.exports = IntermediateThrowEventHandler;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/ParallelGatewayHandler.js
var require_ParallelGatewayHandler = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/ParallelGatewayHandler.js"(exports, module) {
    "use strict";
    var is = require_ElementHelper().is;
    var events = require_EventHelper();
    var CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT;
    var GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT;
    function ParallelGatewayHandler(animation, eventBus) {
      this._animation = animation;
      this._eventBus = eventBus;
    }
    ParallelGatewayHandler.prototype.consume = function(context) {
      var element = context.element, processInstanceId = context.processInstanceId;
      if (!element.tokenCount) {
        element.tokenCount = {};
      }
      if (!element.tokenCount[processInstanceId]) {
        element.tokenCount[processInstanceId] = 0;
      }
      element.tokenCount[processInstanceId]++;
      var incoming = element.incoming;
      if (incoming.length === element.tokenCount[processInstanceId]) {
        this._eventBus.fire(GENERATE_TOKEN_EVENT, context);
        element.tokenCount[processInstanceId] = 0;
      }
    };
    ParallelGatewayHandler.prototype.generate = function(context) {
      var self = this;
      var element = context.element, processInstanceId = context.processInstanceId;
      var outgoingSequenceFlows = element.outgoing.filter(function(outgoing) {
        return is(outgoing, "bpmn:SequenceFlow");
      });
      outgoingSequenceFlows.forEach(function(outgoing) {
        self._animation.createAnimation(outgoing, processInstanceId, function() {
          self._eventBus.fire(CONSUME_TOKEN_EVENT, {
            element: outgoing.target,
            processInstanceId
          });
        });
      });
    };
    ParallelGatewayHandler.$inject = ["animation", "eventBus"];
    module.exports = ParallelGatewayHandler;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/StartEventHandler.js
var require_StartEventHandler2 = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/StartEventHandler.js"(exports, module) {
    "use strict";
    var is = require_ElementHelper().is;
    var events = require_EventHelper();
    var CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT;
    var UPDATE_ELEMENTS_EVENT = events.UPDATE_ELEMENTS_EVENT;
    function StartEventHandler(animation, eventBus, elementRegistry, processInstances) {
      this._animation = animation;
      this._eventBus = eventBus;
      this._elementRegistry = elementRegistry;
      this._processInstances = processInstances;
    }
    StartEventHandler.prototype.consume = function() {
    };
    StartEventHandler.prototype.generate = function(context) {
      var self = this;
      var element = context.element, parentProcessInstanceId = context.parentProcessInstanceId;
      var outgoingSequenceFlows = element.outgoing.filter(function(outgoing) {
        return is(outgoing, "bpmn:SequenceFlow");
      });
      var parent = element.parent, processInstanceId = this._processInstances.create(parent, parentProcessInstanceId);
      outgoingSequenceFlows.forEach(function(connection) {
        self._animation.createAnimation(connection, processInstanceId, function() {
          self._eventBus.fire(CONSUME_TOKEN_EVENT, {
            element: connection.target,
            processInstanceId
          });
        });
      });
      if (is(element.parent, "bpmn:SubProcess")) {
        return;
      }
      var startEvents = this._elementRegistry.filter(function(element2) {
        return is(element2, "bpmn:StartEvent");
      });
      this._eventBus.fire(UPDATE_ELEMENTS_EVENT, {
        elements: startEvents
      });
    };
    StartEventHandler.$inject = ["animation", "eventBus", "elementRegistry", "processInstances"];
    module.exports = StartEventHandler;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/SubProcessHandler.js
var require_SubProcessHandler = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/SubProcessHandler.js"(exports, module) {
    "use strict";
    var is = require_ElementHelper().is;
    var events = require_EventHelper();
    var CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT;
    var GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT;
    var UPDATE_ELEMENT_EVENT = events.UPDATE_ELEMENT_EVENT;
    function SubProcessHandler(animation, eventBus, log) {
      this._animation = animation;
      this._eventBus = eventBus;
      this._log = log;
    }
    SubProcessHandler.prototype.consume = function(context) {
      var element = context.element, processInstanceId = context.processInstanceId;
      var startEvent = element.children.filter(function(child) {
        return is(child, "bpmn:StartEvent");
      })[0];
      if (!startEvent) {
        this._log.log("Skipping Subprocess", "info", "fa-angle-double-right");
        this._eventBus.fire(GENERATE_TOKEN_EVENT, context);
      } else {
        this._log.log("Starting Subprocess", "info", "fa-sign-in");
        this._eventBus.fire(GENERATE_TOKEN_EVENT, {
          element: startEvent,
          parentProcessInstanceId: processInstanceId
        });
      }
      this._eventBus.fire(UPDATE_ELEMENT_EVENT, {
        element
      });
    };
    SubProcessHandler.prototype.generate = function(context) {
      var self = this;
      var element = context.element, processInstanceId = context.processInstanceId;
      var outgoingSequenceFlows = element.outgoing.filter(function(outgoing) {
        return is(outgoing, "bpmn:SequenceFlow");
      });
      outgoingSequenceFlows.forEach(function(outgoing) {
        self._animation.createAnimation(outgoing, processInstanceId, function() {
          self._eventBus.fire(CONSUME_TOKEN_EVENT, {
            element: outgoing.target,
            processInstanceId
          });
        });
      });
      this._eventBus.fire(UPDATE_ELEMENT_EVENT, {
        element
      });
    };
    SubProcessHandler.$inject = ["animation", "eventBus", "log"];
    module.exports = SubProcessHandler;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/BoundaryEventHandler.js
var require_BoundaryEventHandler2 = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/BoundaryEventHandler.js"(exports, module) {
    "use strict";
    var elementHelper = require_ElementHelper();
    var is = elementHelper.is;
    var events = require_EventHelper();
    var CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT;
    var UPDATE_ELEMENT_EVENT = events.UPDATE_ELEMENT_EVENT;
    function BoundaryEventHandler(animation, eventBus, elementRegistry) {
      this._animation = animation;
      this._eventBus = eventBus;
      this._elementRegistry = elementRegistry;
    }
    BoundaryEventHandler.prototype.consume = function(context) {
      var element = context.element, processInstanceId = context.processInstanceId;
      if (!element.tokenCount) {
        element.tokenCount = {};
      }
      if (!element.tokenCount[processInstanceId]) {
        element.tokenCount[processInstanceId] = 0;
      }
      element.tokenCount[processInstanceId]++;
      this._eventBus.fire(UPDATE_ELEMENT_EVENT, {
        element
      });
    };
    BoundaryEventHandler.prototype.generate = function(context) {
      var self = this;
      var element = context.element, processInstanceId = context.processInstanceId;
      var outgoingSequenceFlows = element.outgoing.filter(function(outgoing) {
        return is(outgoing, "bpmn:SequenceFlow");
      });
      outgoingSequenceFlows.forEach(function(connection) {
        self._animation.createAnimation(connection, processInstanceId, function() {
          self._eventBus.fire(CONSUME_TOKEN_EVENT, {
            element: connection.target,
            processInstanceId
          });
        });
      });
    };
    BoundaryEventHandler.$inject = ["animation", "eventBus", "elementRegistry"];
    module.exports = BoundaryEventHandler;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/TaskHandler.js
var require_TaskHandler = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/handler/TaskHandler.js"(exports, module) {
    "use strict";
    var is = require_ElementHelper().is;
    var events = require_EventHelper();
    var CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT;
    var GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT;
    function TaskHandler(animation, eventBus) {
      this._animation = animation;
      this._eventBus = eventBus;
    }
    TaskHandler.prototype.consume = function(context) {
      this._eventBus.fire(GENERATE_TOKEN_EVENT, context);
    };
    TaskHandler.prototype.generate = function(context) {
      var self = this;
      var element = context.element, processInstanceId = context.processInstanceId;
      var outgoingSequenceFlows = element.outgoing.filter(function(outgoing) {
        return is(outgoing, "bpmn:SequenceFlow");
      });
      outgoingSequenceFlows.forEach(function(outgoing) {
        self._animation.createAnimation(outgoing, processInstanceId, function() {
          self._eventBus.fire(CONSUME_TOKEN_EVENT, {
            element: outgoing.target,
            processInstanceId
          });
        });
      });
    };
    TaskHandler.$inject = ["animation", "eventBus"];
    module.exports = TaskHandler;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/TokenSimulationBehavior.js
var require_TokenSimulationBehavior = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/TokenSimulationBehavior.js"(exports, module) {
    "use strict";
    var EndEventHandler = require_EndEventHandler();
    var EventBasedGatewayHandler = require_EventBasedGatewayHandler();
    var ExclusiveGatewayHandler = require_ExclusiveGatewayHandler2();
    var IntermediateCatchEventHandler = require_IntermediateCatchEventHandler2();
    var IntermediateThrowEventHandler = require_IntermediateThrowEventHandler();
    var ParallelGatewayHandler = require_ParallelGatewayHandler();
    var StartEventHandler = require_StartEventHandler2();
    var SubProcessHandler = require_SubProcessHandler();
    var BoundaryEventHandler = require_BoundaryEventHandler2();
    var TaskHandler = require_TaskHandler();
    var events = require_EventHelper();
    var GENERATE_TOKEN_EVENT = events.GENERATE_TOKEN_EVENT;
    var CONSUME_TOKEN_EVENT = events.CONSUME_TOKEN_EVENT;
    function TokenSimulationBehavior(eventBus, animation, injector) {
      var self = this;
      this._injector = injector;
      this.handlers = {};
      this.registerHandler("bpmn:EndEvent", EndEventHandler);
      this.registerHandler("bpmn:EventBasedGateway", EventBasedGatewayHandler);
      this.registerHandler("bpmn:ExclusiveGateway", ExclusiveGatewayHandler);
      this.registerHandler("bpmn:IntermediateCatchEvent", IntermediateCatchEventHandler);
      this.registerHandler("bpmn:IntermediateThrowEvent", IntermediateThrowEventHandler);
      this.registerHandler("bpmn:ParallelGateway", ParallelGatewayHandler);
      this.registerHandler("bpmn:StartEvent", StartEventHandler);
      this.registerHandler("bpmn:SubProcess", SubProcessHandler);
      this.registerHandler("bpmn:BoundaryEvent", BoundaryEventHandler);
      this.registerHandler([
        "bpmn:BusinessRuleTask",
        "bpmn:CallActivity",
        "bpmn:ManualTask",
        "bpmn:ScriptTask",
        "bpmn:ServiceTask",
        "bpmn:Task",
        "bpmn:UserTask"
      ], TaskHandler);
      eventBus.on(GENERATE_TOKEN_EVENT, function(context) {
        var element = context.element;
        if (!self.handlers[element.type]) {
          throw new Error("no handler for type " + element.type);
        }
        self.handlers[element.type].generate(context);
      });
      eventBus.on(CONSUME_TOKEN_EVENT, function(context) {
        var element = context.element;
        if (!self.handlers[element.type]) {
          throw new Error("no handler for type " + element.type);
        }
        self.handlers[element.type].consume(context);
      });
    }
    TokenSimulationBehavior.prototype.registerHandler = function(types, handlerCls) {
      var self = this;
      var handler = this._injector.instantiate(handlerCls);
      if (!Array.isArray(types)) {
        types = [types];
      }
      types.forEach(function(type) {
        self.handlers[type] = handler;
      });
    };
    TokenSimulationBehavior.$inject = ["eventBus", "animation", "injector"];
    module.exports = TokenSimulationBehavior;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/index.js
var require_token_simulation_behavior = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/token-simulation-behavior/index.js"(exports, module) {
    module.exports = require_TokenSimulationBehavior();
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/editor-actions/EditorActions.js
var require_EditorActions = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/editor-actions/EditorActions.js"(exports, module) {
    "use strict";
    function EditorActions(eventBus, toggleMode, pauseSimulation, log, resetSimulation, editorActions) {
      editorActions.register({
        toggleTokenSimulation: function() {
          toggleMode.toggleMode();
        }
      });
      editorActions.register({
        togglePauseTokenSimulation: function() {
          pauseSimulation.toggle();
        }
      });
      editorActions.register({
        resetTokenSimulation: function() {
          resetSimulation.resetSimulation();
        }
      });
      editorActions.register({
        toggleTokenSimulationLog: function() {
          log.toggle();
        }
      });
    }
    EditorActions.$inject = [
      "eventBus",
      "toggleMode",
      "pauseSimulation",
      "log",
      "resetSimulation",
      "editorActions"
    ];
    module.exports = EditorActions;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/editor-actions/index.js
var require_editor_actions = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/editor-actions/index.js"(exports, module) {
    module.exports = require_EditorActions();
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/keyboard-bindings/KeyboardBindings.js
var require_KeyboardBindings = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/keyboard-bindings/KeyboardBindings.js"(exports, module) {
    "use strict";
    var events = require_EventHelper();
    var TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;
    var VERY_HIGH_PRIORITY = 1e4;
    function KeyboardBindings(eventBus, injector) {
      var editorActions = injector.get("editorActions", false), keyboard = injector.get("keyboard", false);
      if (!keyboard || !editorActions) {
        return;
      }
      var isActive = false;
      function handleKeyEvent(keyEvent) {
        if (isKey(["t", "T"], keyEvent)) {
          editorActions.trigger("toggleTokenSimulation");
          return true;
        }
        if (!isActive) {
          return;
        }
        if (isKey(["l", "L"], keyEvent)) {
          editorActions.trigger("toggleTokenSimulationLog");
          return true;
        }
        if (isKey([" ", "Spacebar"], keyEvent)) {
          editorActions.trigger("togglePauseTokenSimulation");
          return true;
        }
        if (isKey(["r", "R"], keyEvent)) {
          editorActions.trigger("resetTokenSimulation");
          return true;
        }
      }
      eventBus.on("keyboard.init", function() {
        keyboard.addListener(VERY_HIGH_PRIORITY, function(event) {
          var keyEvent = event.keyEvent;
          handleKeyEvent(keyEvent);
        });
      });
      eventBus.on(TOGGLE_MODE_EVENT, function(context) {
        var simulationModeActive = context.simulationModeActive;
        if (simulationModeActive) {
          isActive = true;
        } else {
          isActive = false;
        }
      });
    }
    KeyboardBindings.$inject = ["eventBus", "injector"];
    module.exports = KeyboardBindings;
    function isKey(keys2, event) {
      return keys2.indexOf(event.key) > -1;
    }
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/keyboard-bindings/index.js
var require_keyboard_bindings = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/keyboard-bindings/index.js"(exports, module) {
    module.exports = require_KeyboardBindings();
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/palette/Palette.js
var require_Palette = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/palette/Palette.js"(exports, module) {
    "use strict";
    var domify = require_domify2();
    var domClasses = require_classes();
    var events = require_EventHelper();
    var TOGGLE_MODE_EVENT = events.TOGGLE_MODE_EVENT;
    function Palette(eventBus, canvas) {
      var self = this;
      this._canvas = canvas;
      this.entries = [];
      this._init();
      eventBus.on(TOGGLE_MODE_EVENT, function(context) {
        var simulationModeActive = context.simulationModeActive;
        if (simulationModeActive) {
          domClasses(self.container).remove("hidden");
        } else {
          domClasses(self.container).add("hidden");
        }
      });
    }
    Palette.prototype._init = function() {
      this.container = domify('<div class="token-simulation-palette hidden"></div>');
      this._canvas.getContainer().appendChild(this.container);
    };
    Palette.prototype.addEntry = function(entry, index) {
      var childIndex = 0;
      this.entries.forEach(function(entry2) {
        if (index >= entry2.index) {
          childIndex++;
        }
      });
      this.container.insertBefore(entry, this.container.childNodes[childIndex]);
      this.entries.push({
        entry,
        index
      });
    };
    Palette.$inject = ["eventBus", "canvas"];
    module.exports = Palette;
  }
});

// node_modules/bpmn-js-token-simulation/lib/features/palette/index.js
var require_palette = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/features/palette/index.js"(exports, module) {
    module.exports = require_Palette();
  }
});

// node_modules/bpmn-js-token-simulation/lib/modeler.js
var require_modeler2 = __commonJS({
  "node_modules/bpmn-js-token-simulation/lib/modeler.js"(exports, module) {
    module.exports = {
      __init__: [
        "animation",
        "contextPads",
        "disableModeling",
        "elementNotifications",
        "elementSupport",
        "exclusiveGatewaySettings",
        "log",
        "notifications",
        "pauseSimulation",
        "preserveElementColors",
        "processInstanceIds",
        "processInstanceSettings",
        "processInstances",
        "resetSimulation",
        "setAnimationSpeed",
        "showProcessInstance",
        "simulationState",
        "toggleMode",
        "tokenCount",
        "tokenSimulationBehavior",
        "tokenSimulationEditorActions",
        "tokenSimulationKeyboardBindings",
        "tokenSimulationPalette"
      ],
      "animation": ["type", require_Animation()],
      "contextPads": ["type", require_context_pads()],
      "disableModeling": ["type", require_disable_modeling()],
      "elementNotifications": ["type", require_element_notifications()],
      "elementSupport": ["type", require_element_support()],
      "exclusiveGatewaySettings": ["type", require_exclusive_gateway_settings()],
      "log": ["type", require_log()],
      "notifications": ["type", require_notifications()],
      "pauseSimulation": ["type", require_pause_simulation()],
      "preserveElementColors": ["type", require_preserve_element_colors()],
      "processInstanceIds": ["type", require_process_instance_ids()],
      "processInstanceSettings": ["type", require_process_instance_settings()],
      "processInstances": ["type", require_process_instances()],
      "resetSimulation": ["type", require_reset_simulation()],
      "setAnimationSpeed": ["type", require_set_animation_speed()],
      "showProcessInstance": ["type", require_show_process_instance()],
      "simulationState": ["type", require_simulation_state()],
      "toggleMode": ["type", require_modeler()],
      "tokenCount": ["type", require_token_count()],
      "tokenSimulationBehavior": ["type", require_token_simulation_behavior()],
      "tokenSimulationEditorActions": ["type", require_editor_actions()],
      "tokenSimulationKeyboardBindings": ["type", require_keyboard_bindings()],
      "tokenSimulationPalette": ["type", require_palette()]
    };
  }
});

// node_modules/bpmn-js-token-simulation/index.js
var require_bpmn_js_token_simulation = __commonJS({
  "node_modules/bpmn-js-token-simulation/index.js"(exports, module) {
    module.exports = require_modeler2();
  }
});
export default require_bpmn_js_token_simulation();
/*! Bundled license information:

svg.js/dist/svg.js:
  (*!
  * svg.js - A lightweight library for manipulating and animating SVG.
  * @version 2.7.1
  * https://svgdotjs.github.io/
  *
  * @copyright Wout Fierens <<EMAIL>>
  * @license MIT
  *
  * BUILT: Fri Nov 30 2018 10:01:55 GMT+0100 (GMT+01:00)
  *)
*/
//# sourceMappingURL=bpmn-js-token-simulation.js.map
