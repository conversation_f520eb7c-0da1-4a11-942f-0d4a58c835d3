var _vue=require("vue"),_xeUtils=_interopRequireDefault(require("xe-utils")),_ui=require("../../../ui"),_util=require("../../src/util"),_dom=require("../../../ui/src/dom"),_utils=require("../../../ui/src/utils");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let{renderer,hooks}=_ui.VxeUI,tableFilterMethodKeys=["openFilter","setFilter","clearFilter","saveFilterPanel","resetFilterPanel","getCheckedFilters","updateFilterOptionStatus"];hooks.add("tableFilterModule",{setupTable(_){let{props:d,reactData:h,internalData:g}=_,{refElem:k,refTableFilter:C}=_.getRefMaps(),{computeFilterOpts:b,computeMouseOpts:f}=_.getComputeMaps(),i=e=>{var t=h.filterStore;t.options.forEach(e=>{e.checked=e._checked}),_.confirmFilterEvent(e)},n=(e,t,l)=>{var r=h.filterStore;r.options.forEach(e=>{e._checked=!1}),l._checked=t,_.checkFilterOptions(),i(e)},a=(e,t,l)=>{l._checked=t,_.checkFilterOptions()},t=e=>{var t=h.filterStore;_.handleClearFilter(t.column),_.confirmFilterEvent(e),e&&_.dispatchEvent("clear-filter",{filterList:[]},e)};var e={checkFilterOptions(){var e=h.filterStore;e.isAllSelected=e.options.every(e=>e._checked),e.isIndeterminate=!e.isAllSelected&&e.options.some(e=>e._checked)},triggerFilterEvent(e,i,t){let{initStore:l,filterStore:m}=h,F=g.elemStore;if(m.column===i&&m.visible)m.visible=!1;else{let c=k.value,{scrollTop:u,scrollLeft:s,visibleHeight:d,visibleWidth:h}=(0,_dom.getDomNode)();let f=b.value.transfer,v=c.getBoundingClientRect(),p=e.currentTarget;var{filters:n,filterMultiple:a,filterRender:o}=i,o=(0,_utils.isEnableConf)(o)?renderer.get(o.name):null;let r=i.filterRecoverMethod||(o?o.tableFilterRecoverMethod||o.filterRecoverMethod:null);g._currFilterParams=t,Object.assign(m,{multiple:a,options:n,column:i,style:null}),m.options.forEach(e=>{var{_checked:t,checked:l}=e;(e._checked=l)||t===l||r&&r({option:e,column:i,$table:_})}),this.checkFilterOptions(),m.visible=!0,l.filter=!0,(0,_vue.nextTick)(()=>{if((0,_util.getRefElem)(F["main-header-scroll"])){var r=C.value,r=r?r.getRefMaps().refElem.value:null;if(r){var i=p.getBoundingClientRect(),n=r.querySelector(".vxe-table--filter-header"),a=r.querySelector(".vxe-table--filter-footer"),r=r.offsetWidth,o=r/2;let e=0,t=0,l=0;f?(e=i.left-o+s,t=i.top+p.clientHeight+u,l=Math.min(Math.max(v.height,Math.floor(d/2)),Math.max(80,d-t-(n?n.clientHeight:0)-(a?a.clientHeight:0)-28)),e<16?e=16:e>h-r-16&&(e=h-r-16)):(e=i.left-v.left-o,t=i.top-v.top+p.clientHeight,l=Math.max(40,c.clientHeight-t-(n?n.clientHeight:0)-(a?a.clientHeight:0)-14),e<1?e=1:e>c.clientWidth-r-1&&(e=c.clientWidth-r-1)),m.style={top:(0,_dom.toCssUnit)(t),left:(0,_dom.toCssUnit)(e)},m.maxHeight=l}}})}_.dispatchEvent("filter-visible",{column:i,field:i.field,property:i.field,filterList:_.getCheckedFilters(),visible:m.visible},e)},handleClearFilter(e){if(e){var{filters:l,filterRender:r}=e;if(l){r=(0,_utils.isEnableConf)(r)?renderer.get(r.name):null;let t=e.filterResetMethod||(r?r.tableFilterResetMethod||r.filterResetMethod:null);l.forEach(e=>{e._checked=!1,e.checked=!1,t||(e.data=_xeUtils.default.clone(e.resetValue,!0))}),t&&t({options:l,column:e,$table:_})}}},handleColumnConfirmFilter(e,t){var l=d.mouseConfig;let{scrollXLoad:r,scrollYLoad:i}=h;var n=b.value,a=f.value,o=e.field;let c=[],u=[];e.filters.forEach(e=>{e.checked&&(c.push(e.value),u.push(e.data))});var s=_.getCheckedFilters(),e={$table:_,$event:t,column:e,field:o,property:o,values:c,datas:u,filters:s,filterList:s};return n.remote||(_.handleTableData(!0),_.checkSelectionStatus()),l&&a.area&&_.handleFilterEvent&&_.handleFilterEvent(t,e),t&&_.dispatchEvent("filter-change",e,t),_.closeFilter(),_.updateFooter().then(()=>{var{scrollXLoad:e,scrollYLoad:t}=h;if(r||e||i||t)return(r||e)&&_.updateScrollXSpace(),(i||t)&&_.updateScrollYSpace(),_.refreshScroll()}).then(()=>(_.updateCellAreas(),_.recalculate(!0))).then(()=>{setTimeout(()=>_.recalculate(),50)})},confirmFilterEvent(e){var t=h.filterStore,t=t.column;_.handleColumnConfirmFilter(t,e)},handleFilterChangeRadioOption:n,handleFilterChangeMultipleOption:a,handleFilterChangeOption(e,t,l){var r=h.filterStore;r.multiple?a(0,t,l):n(e,t,l)},handleFilterConfirmFilter:i,handleFilterResetFilter:t};return Object.assign(Object.assign({},{openFilter(e){let r=(0,_util.handleFieldOrColumn)(_,e);if(r&&r.filters){let t=g.elemStore,l=r.fixed;return _.scrollToColumn(r).then(()=>{var e=(0,_util.getRefElem)(t[`${l||"main"}-header-wrapper`]||t["main-header-wrapper"]);e&&(e=e.querySelector(`.vxe-header--column.${r.id} .vxe-cell--filter`),(0,_dom.triggerEvent)(e,"click"))})}return(0,_vue.nextTick)()},setFilter(e,t,l){e=(0,_util.handleFieldOrColumn)(_,e);return e&&e.filters&&(e.filters=(0,_util.toFilters)(t||[]),l)?_.handleColumnConfirmFilter(e,new Event("click")):(0,_vue.nextTick)()},clearFilter(e){var t=h.filterStore,l=g.tableFullColumn,r=b.value;let i;return e?(i=(0,_util.handleFieldOrColumn)(_,e))&&_.handleClearFilter(i):l.forEach(_.handleClearFilter),e&&i===t.column||Object.assign(t,{isAllSelected:!1,isIndeterminate:!1,style:null,options:[],column:null,multiple:!1,visible:!1}),r.remote?(0,_vue.nextTick)():_.updateData()},saveFilterPanel(){return i(null),(0,_vue.nextTick)()},saveFilterPanelByEvent(e){return i(e),(0,_vue.nextTick)()},resetFilterPanel(){return t(null),(0,_vue.nextTick)()},resetFilterPanelByEvent(e){return t(e),(0,_vue.nextTick)()},getCheckedFilters(){var e=g.tableFullColumn;let n=[];return e.forEach(e=>{var{field:t,filters:l}=e;let r=[],i=[];l&&l.length&&(l.forEach(e=>{e.checked&&(r.push(e.value),i.push(e.data))}),r.length)&&n.push({column:e,field:t,property:t,values:r,datas:i})}),n},updateFilterOptionStatus(e,t){return e._checked=t,e.checked=t,(0,_vue.nextTick)()}}),e)},setupGrid(e){return e.extendTableMethods(tableFilterMethodKeys)}});