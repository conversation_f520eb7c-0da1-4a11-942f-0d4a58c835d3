import { VxeUI } from '@vxe-ui/core';
import VxeListDesignComponent from './src/list-design';
import { dynamicApp } from '../dynamics';
import './render';
export const VxeListDesign = Object.assign({}, VxeListDesignComponent, {
    install(app) {
        app.component(VxeListDesignComponent.name, VxeListDesignComponent);
    }
});
const listDesignHandle = {};
dynamicApp.use(VxeListDesign);
VxeUI.component(VxeListDesignComponent);
VxeUI.listDesignHandle = listDesignHandle;
export const ListDesign = VxeListDesign;
export default VxeListDesign;
