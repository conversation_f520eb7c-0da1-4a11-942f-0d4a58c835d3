{"version": 3, "sources": ["../../amis/esm/renderers/ButtonGroup.js"], "sourcesContent": ["/**\n * amis v6.12.0\n * build time: 2025-04-03\n * Copyright 2018-2025 baidu\n */\n\nimport { __extends, __decorate } from 'tslib';\nimport ButtonGroupControl from './Form/ButtonGroupSelect.js';\nexport { default } from './Form/ButtonGroupSelect.js';\nimport { Renderer } from 'amis-core';\n\nvar ButtonGroupRenderer = /** @class */ (function (_super) {\n    __extends(ButtonGroupRenderer, _super);\n    function ButtonGroupRenderer() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    ButtonGroupRenderer = __decorate([\n        Renderer({\n            type: 'button-group'\n        })\n    ], ButtonGroupRenderer);\n    return ButtonGroupRenderer;\n}(ButtonGroupControl));\n\nexport { ButtonGroupRenderer };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAWA,IAAI;AAAA;AAAA,EAAqC,SAAU,QAAQ;AACvD,cAAUA,sBAAqB,MAAM;AACrC,aAASA,uBAAsB;AAC3B,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,uBAAsB,WAAW;AAAA,MAC7B,SAAS;AAAA,QACL,MAAM;AAAA,MACV,CAAC;AAAA,IACL,GAAGA,oBAAmB;AACtB,WAAOA;AAAA,EACX,EAAE,kBAAkB;AAAA;", "names": ["ButtonGroup<PERSON><PERSON><PERSON>"]}