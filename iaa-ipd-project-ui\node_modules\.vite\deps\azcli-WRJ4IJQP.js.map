{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/azcli/azcli.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    comments: {\n        lineComment: '#'\n    }\n};\nexport var language = {\n    defaultToken: 'keyword',\n    ignoreCase: true,\n    tokenPostfix: '.azcli',\n    str: /[^#\\s]/,\n    tokenizer: {\n        root: [\n            { include: '@comment' },\n            [\n                /\\s-+@str*\\s*/,\n                {\n                    cases: {\n                        '@eos': { token: 'key.identifier', next: '@popall' },\n                        '@default': { token: 'key.identifier', next: '@type' }\n                    }\n                }\n            ],\n            [\n                /^-+@str*\\s*/,\n                {\n                    cases: {\n                        '@eos': { token: 'key.identifier', next: '@popall' },\n                        '@default': { token: 'key.identifier', next: '@type' }\n                    }\n                }\n            ]\n        ],\n        type: [\n            { include: '@comment' },\n            [\n                /-+@str*\\s*/,\n                {\n                    cases: {\n                        '@eos': { token: 'key.identifier', next: '@popall' },\n                        '@default': 'key.identifier'\n                    }\n                }\n            ],\n            [\n                /@str+\\s*/,\n                {\n                    cases: {\n                        '@eos': { token: 'string', next: '@popall' },\n                        '@default': 'string'\n                    }\n                }\n            ]\n        ],\n        comment: [\n            [\n                /#.*$/,\n                {\n                    cases: {\n                        '@eos': { token: 'comment', next: '@popall' }\n                    }\n                }\n            ]\n        ]\n    }\n};\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,UAAU;AAAA,IACN,aAAa;AAAA,EACjB;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,KAAK;AAAA,EACL,WAAW;AAAA,IACP,MAAM;AAAA,MACF,EAAE,SAAS,WAAW;AAAA,MACtB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ,EAAE,OAAO,kBAAkB,MAAM,UAAU;AAAA,YACnD,YAAY,EAAE,OAAO,kBAAkB,MAAM,QAAQ;AAAA,UACzD;AAAA,QACJ;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ,EAAE,OAAO,kBAAkB,MAAM,UAAU;AAAA,YACnD,YAAY,EAAE,OAAO,kBAAkB,MAAM,QAAQ;AAAA,UACzD;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,MAAM;AAAA,MACF,EAAE,SAAS,WAAW;AAAA,MACtB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ,EAAE,OAAO,kBAAkB,MAAM,UAAU;AAAA,YACnD,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ,EAAE,OAAO,UAAU,MAAM,UAAU;AAAA,YAC3C,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,QAAQ,EAAE,OAAO,WAAW,MAAM,UAAU;AAAA,UAChD;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;", "names": []}