"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _vue = require("vue");
var _comp = require("../../../ui/src/comp");
var _ui = require("../../../ui");
var _utils = require("../../../ui/src/utils");
var _xeUtils = _interopRequireDefault(require("xe-utils"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
const {
  getIcon
} = _ui.VxeUI;
var _default = exports.default = (0, _comp.defineVxeComponent)({
  name: 'VxeTableMenuPanel',
  setup(props, context) {
    const xID = _xeUtils.default.uniqueId();
    const $xeTable = (0, _vue.inject)('$xeTable', {});
    const {
      reactData: tableReactData
    } = $xeTable;
    const refElem = (0, _vue.ref)();
    const refMaps = {
      refElem
    };
    const $xeMenuPanel = {
      xID,
      props,
      context,
      getRefMaps: () => refMaps
    };
    const renderVN = () => {
      const {
        ctxMenuStore
      } = tableReactData;
      const {
        computeMenuOpts
      } = $xeTable.getComputeMaps();
      const menuOpts = computeMenuOpts.value;
      return (0, _vue.h)(_vue.Teleport, {
        to: 'body',
        disabled: false
      }, [(0, _vue.h)('div', {
        ref: refElem,
        class: ['vxe-table--context-menu-wrapper', menuOpts.className, {
          'is--visible': ctxMenuStore.visible
        }],
        style: ctxMenuStore.style
      }, ctxMenuStore.list.map((options, gIndex) => {
        return options.every(item => item.visible === false) ? (0, _vue.createCommentVNode)() : (0, _vue.h)('ul', {
          class: 'vxe-context-menu--option-wrapper',
          key: gIndex
        }, options.map((item, index) => {
          const hasChildMenus = item.children && item.children.some(child => child.visible !== false);
          const prefixOpts = Object.assign({}, item.prefixConfig);
          const suffixOpts = Object.assign({}, item.suffixConfig);
          const menuContent = (0, _utils.getFuncText)(item.name);
          return item.visible === false ? null : (0, _vue.h)('li', {
            class: [item.className, {
              'link--disabled': item.disabled,
              'link--active': item === ctxMenuStore.selected
            }],
            key: `${gIndex}_${index}`
          }, [(0, _vue.h)('a', {
            class: 'vxe-context-menu--link',
            onClick(evnt) {
              $xeTable.ctxMenuLinkEvent(evnt, item);
            },
            onMouseover(evnt) {
              $xeTable.ctxMenuMouseoverEvent(evnt, item);
            },
            onMouseout(evnt) {
              $xeTable.ctxMenuMouseoutEvent(evnt, item);
            }
          }, [(0, _vue.h)('div', {
            class: ['vxe-context-menu--link-prefix', prefixOpts.className || '']
          }, [(0, _vue.h)('i', {
            class: prefixOpts.icon || item.prefixIcon
          }), prefixOpts.content ? (0, _vue.h)('span', {}, `${prefixOpts.content}`) : (0, _vue.createCommentVNode)()]), (0, _vue.h)('div', {
            class: 'vxe-context-menu--link-content',
            title: menuContent
          }, menuContent), (0, _vue.h)('div', {
            class: ['vxe-context-menu--link-suffix', suffixOpts.className || '']
          }, [(0, _vue.h)('i', {
            class: suffixOpts.icon || item.suffixIcon || (hasChildMenus ? getIcon().TABLE_MENU_OPTIONS : '')
          }), suffixOpts.content ? (0, _vue.h)('span', `${suffixOpts.content}`) : (0, _vue.createCommentVNode)()])]), hasChildMenus ? (0, _vue.h)('ul', {
            class: ['vxe-table--context-menu-clild-wrapper', {
              'is--show': item === ctxMenuStore.selected && ctxMenuStore.showChild
            }]
          }, item.children.map((child, cIndex) => {
            const childPrefixOpts = Object.assign({}, child.prefixConfig);
            const childSuffixOpts = Object.assign({}, child.suffixConfig);
            const childMenuContent = (0, _utils.getFuncText)(child.name);
            return child.visible === false ? null : (0, _vue.h)('li', {
              class: [child.className, {
                'link--disabled': child.disabled,
                'link--active': child === ctxMenuStore.selectChild
              }],
              key: `${gIndex}_${index}_${cIndex}`
            }, [(0, _vue.h)('a', {
              class: 'vxe-context-menu--link',
              onClick(evnt) {
                $xeTable.ctxMenuLinkEvent(evnt, child);
              },
              onMouseover(evnt) {
                $xeTable.ctxMenuMouseoverEvent(evnt, item, child);
              },
              onMouseout(evnt) {
                $xeTable.ctxMenuMouseoutEvent(evnt, item);
              }
            }, [(0, _vue.h)('div', {
              class: ['vxe-context-menu--link-prefix', childPrefixOpts.className || '']
            }, [(0, _vue.h)('i', {
              class: childPrefixOpts.icon || child.prefixIcon
            }), childPrefixOpts.content ? (0, _vue.h)('span', `${childPrefixOpts.content}`) : (0, _vue.createCommentVNode)()]), (0, _vue.h)('div', {
              class: 'vxe-context-menu--link-content',
              title: childMenuContent
            }, childMenuContent), (0, _vue.h)('div', {
              class: ['vxe-context-menu--link-suffix', childSuffixOpts.className || '']
            }, [(0, _vue.h)('i', {
              class: childSuffixOpts.icon
            }), childSuffixOpts.content ? (0, _vue.h)('span', `${childSuffixOpts.content}`) : (0, _vue.createCommentVNode)()])])]);
          })) : null]);
        }));
      }))]);
    };
    $xeMenuPanel.renderVN = renderVN;
    return $xeMenuPanel;
  },
  render() {
    return this.renderVN();
  }
});