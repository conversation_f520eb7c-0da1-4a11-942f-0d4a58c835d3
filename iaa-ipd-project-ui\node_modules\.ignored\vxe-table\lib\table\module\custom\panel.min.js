Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var _vue=require("vue"),_comp=require("../../../ui/src/comp"),_ui=require("../../../ui"),_utils=require("../../../ui/src/utils"),_dom=require("../../../ui/src/dom"),_log=require("../../../ui/src/log"),_xeUtils=_interopRequireDefault(require("xe-utils"));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}let{getI18n,getIcon,renderEmptyElement}=_ui.VxeUI;var _default=exports.default=(0,_comp.defineVxeComponent)({name:"TableCustomPanel",props:{customStore:{type:Object,default:()=>({})}},setup(D){let U=_ui.VxeUI.getComponent("VxeModal"),L=_ui.VxeUI.getComponent("VxeDrawer"),F=_ui.VxeUI.getComponent("VxeButton"),M=_ui.VxeUI.getComponent("VxeNumberInput"),R=_ui.VxeUI.getComponent("VxeRadioGroup"),H=(0,_vue.inject)("$xeTable",{}),{props:z,reactData:K,internalData:s}=H,{computeCustomOpts:W,computeColumnDragOpts:X,computeColumnOpts:ie,computeIsMaxFixedColumn:$,computeResizableOpts:re}=H.getComputeMaps(),A=(0,_vue.ref)(),q=(0,_vue.ref)(),r=(0,_vue.ref)(),n=(0,_vue.ref)(),u=(0,_vue.ref)(),b,C=!1,f,S=e=>{var t=D.customStore;t.activeWrapper=!0,H.customOpenEvent(e)},O=e=>{let t=D.customStore;t.activeWrapper=!1,setTimeout(()=>{t.activeBtn||t.activeWrapper||H.customCloseEvent(e)},300)},G=({$event:e})=>{K.isCustomStatus=!0,H.saveCustom(),H.closeCustom(),H.emitCustomEvent("confirm",e)},j=({$event:e})=>{H.closeCustom(),H.emitCustomEvent("close",e)},P=({$event:e})=>{H.cancelCustom(),H.closeCustom(),H.emitCustomEvent("cancel",e)},l=e=>{H.resetCustom(!0),H.closeCustom(),H.emitCustomEvent("reset",e)},Y=({$event:t})=>{_ui.VxeUI.modal?_ui.VxeUI.modal.confirm({content:getI18n("vxe.custom.cstmConfirmRestore"),className:"vxe-table--ignore-clear",escClosable:!0}).then(e=>{"confirm"===e&&l(t)}):l(t)},o=t=>{var e=K.customColumnList,e=_xeUtils.default.findTree(e,e=>e===t);e&&e.parent&&(e=e.parent,e.children)&&e.children.length&&(e.renderVisible=e.children.every(e=>e.renderVisible),e.halfVisible=!e.renderVisible&&e.children.some(e=>e.renderVisible||e.halfVisible),o(e))},Z=e=>{let t=!e.renderVisible;W.value.immediate?(_xeUtils.default.eachTree([e],e=>{e.visible=t,e.renderVisible=t,e.halfVisible=!1}),K.isCustomStatus=!0,H.handleCustom(),H.saveCustomStore("update:visible")):_xeUtils.default.eachTree([e],e=>{e.renderVisible=t,e.halfVisible=!1}),o(e),H.checkCustomStatus()},ne=e=>{W.value.immediate&&e.renderResizeWidth!==e.renderWidth&&(e.resizeWidth=e.renderResizeWidth,e.renderWidth=e.renderResizeWidth,K.isCustomStatus=!0,H.handleCustom(),H.saveCustomStore("update:width"))},J=(e,t)=>{var l=$.value;W.value.immediate?(e.renderFixed===t?_xeUtils.default.eachTree([e],e=>{e.fixed="",e.renderFixed=""}):l&&!e.renderFixed||_xeUtils.default.eachTree([e],e=>{e.fixed=t,e.renderFixed=t}),K.isCustomStatus=!0,H.handleCustom(),H.saveCustomStore("update:fixed")):e.renderFixed===t?_xeUtils.default.eachTree([e],e=>{e.renderFixed=""}):l&&!e.renderFixed||_xeUtils.default.eachTree([e],e=>{e.renderFixed=t})},Q=()=>{H.toggleCustomAllCheckbox()},c=(e,t,l,o)=>{var a,s,i=q.value;i&&(a=i.getBoundingClientRect(),t&&(s=r.value)&&(l?(t=t.getBoundingClientRect(),s.style.display="block",s.style.top=Math.max(1,t.y+i.scrollTop-a.y)+"px",s.style.height=t.height+"px",s.style.width=t.width+"px",s.setAttribute("drag-pos",o),s.setAttribute("drag-to-child",C?"y":"n")):s.style.display=""),t=n.value)&&(t.style.display="block",t.style.top=Math.min(i.clientHeight+i.scrollTop-t.clientHeight,e.clientY+i.scrollTop-a.y)+"px",t.style.left=Math.min(i.clientWidth+i.scrollLeft-t.clientWidth,e.clientX+i.scrollLeft-a.x)+"px",t.setAttribute("drag-status",l?C?"sub":"normal":"disabled"))},a=()=>{var e=n.value,t=r.value;e&&(e.style.display=""),t&&(t.style.display="")},ee=e=>{var e=e.currentTarget.parentElement.parentElement.parentElement,t=e.getAttribute("colid"),t=H.getColumnById(t);e.draggable=!0,u.value=t,(0,_dom.addClass)(e,"active--drag-origin")},te=e=>{e=e.currentTarget.parentElement.parentElement.parentElement;a(),e.draggable=!1,(u.value=null,_dom.removeClass)(e,"active--drag-origin")},le=e=>{e.dataTransfer&&e.dataTransfer.setDragImage((0,_dom.getTpImg)(),0,0)},oe=c=>{let d=z.mouseConfig,m=K.customColumnList,v=s.collectColumn;let p=W.value.immediate;var e=c.currentTarget,t=u.value;let{isCrossDrag:x,isSelfToChildDrag:h,isToChildDrag:_,dragEndMethod:l}=X.value,g="bottom"===f?1:0;if(b&&t&&b!==t){let n=t,u=b;Promise.resolve(!l||l({oldColumn:n,newColumn:u,dragColumn:n,dragPos:f,dragToChild:!!C,offsetIndex:g})).then(a=>{if(a){let e=-1,t=-1,l={},o=(_xeUtils.default.eachTree([n],e=>{l[e.id]=e}),!1);if(p){if(n.parentId&&u.parentId){if(!x)return;if(l[u.id]&&(o=!0,!x||!h))return void(_ui.VxeUI.modal&&_ui.VxeUI.modal.message({status:"error",content:getI18n("vxe.error.treeDragChild")}))}else if(n.parentId){if(!x)return}else if(u.parentId){if(!x)return;if(l[u.id]&&(o=!0,!x||!h))return void(_ui.VxeUI.modal&&_ui.VxeUI.modal.message({status:"error",content:getI18n("vxe.error.treeDragChild")}))}var s,i,r,a=_xeUtils.default.findTree(v,e=>e.id===n.id),a=(o&&x&&h?a&&({items:s,index:r}=a,(i=n.children||[]).forEach(e=>{e.parentId=n.parentId}),s.splice(r,1,...i),n.children=[]):a&&({items:s,index:r,parent:i}=a,s.splice(r,1),i||(e=r)),_xeUtils.default.findTree(v,e=>e.id===u.id));a&&({items:s,index:i,parent:r}=a,x&&_&&C?(n.parentId=u.id,u.children=(u.children||[]).concat([n])):(n.parentId=u.parentId,s.splice(i+g,0,n)),r||(t=i)),_xeUtils.default.eachTree(v,(e,t,l,o,a)=>{a||(e.renderSortNumber=t+1)})}else e=_xeUtils.default.findIndexOf(m,e=>e.id===n.id),m.splice(e,1),t=_xeUtils.default.findIndexOf(m,e=>e.id===u.id),m.splice(t+g,0,n);K.isDragColMove=!0,d&&(H.clearSelected&&H.clearSelected(),H.clearCellAreas)&&(H.clearCellAreas(),H.clearCopyCellArea()),H.dispatchEvent("column-dragend",{oldColumn:n,newColumn:u,dragColumn:n,dragPos:f,offsetIndex:g,_index:{newIndex:t,oldIndex:e}},c),p&&(K.customColumnList=v.slice(0),H.handleColDragSwapColumn())}}).catch(()=>{})}a(),u.value=null,e.draggable=!1,e.removeAttribute("drag-pos"),(0,_dom.removeClass)(e,"active--drag-target"),(0,_dom.removeClass)(e,"active--drag-origin")},ae=e=>{var t,l=W.value.immediate,{isCrossDrag:o,isToChildDrag:a}=X.value,s=e.currentTarget,i=(0,_dom.hasControlKey)(e),r=s.getAttribute("colid"),r=H.getColumnById(r),n=u.value;r&&(o||1===r.level)&&(e.preventDefault(),t=e.clientY-s.getBoundingClientRect().y<s.clientHeight/2?"top":"bottom",n&&n.id===r.id||!o&&1<r.level||!l&&1<r.level||r.renderFixed?c(e,s,!1,t):(C=!!(o&&a&&i&&l),b=r,f=t,c(e,s,!0,t)))},se=()=>{var e=u.value,t=X.value;return(0,_vue.h)("div",{},[(0,_vue.h)("div",{ref:r,class:["vxe-table-custom-popup--drag-line",{"is--guides":t.showGuidesStatus}]}),(0,_vue.h)("div",{ref:n,class:"vxe-table-custom-popup--drag-tip"},[(0,_vue.h)("div",{class:"vxe-table-custom-popup--drag-tip-wrapper"},[(0,_vue.h)("div",{class:"vxe-table-custom-popup--drag-tip-status"},[(0,_vue.h)("span",{class:["vxe-table-custom-popup--drag-tip-normal-status",getIcon().TABLE_DRAG_STATUS_ROW]}),(0,_vue.h)("span",{class:["vxe-table-custom-popup--drag-tip-sub-status",getIcon().TABLE_DRAG_STATUS_SUB_ROW]}),(0,_vue.h)("span",{class:["vxe-table-custom-popup--drag-tip-disabled-status",getIcon().TABLE_DRAG_DISABLED]})]),(0,_vue.h)("div",{class:"vxe-table-custom-popup--drag-tip-content"},getI18n("vxe.custom.cstmDragTarget",[e&&"html"!==e.type?e.getTitle():""]))])])])};return(0,_vue.nextTick)(()=>{var e=W.value.mode;U||"modal"!==e||(0,_log.errLog)("vxe.error.reqComp",["vxe-modal"]),L||"drawer"!==e||(0,_log.errLog)("vxe.error.reqComp",["vxe-drawer"]),F||(0,_log.errLog)("vxe.error.reqComp",["vxe-button"]),M||(0,_log.errLog)("vxe.error.reqComp",["vxe-number-input"]),R||(0,_log.errLog)("vxe.error.reqComp",["vxe-radio-group"])}),()=>{var e=W.value;return(["modal","drawer","popup"].includes(""+e.mode)?()=>{var e=H.xeGrid;let t=D.customStore,c=z.resizable,{isCustomStatus:l,customColumnList:o}=K,a=W.value,d=a.immediate;var s=X.value;let{mode:i,modalOptions:r,drawerOptions:n,allowVisible:m,allowSort:v,allowFixed:p,allowResizable:x,checkMethod:h,visibleMethod:_}=a,g=ie.value,u=g.maxFixedSize,{minWidth:b,maxWidth:C}=re.value;var f=Object.assign({},r),I=Object.assign({},n);let T=$.value,E=s.isCrossDrag;s=a.slots||{};let V=s.header,y=s.top,k=s.bottom,A=s.default,S=s.footer,O=[],B=t.isAll,w=t.isIndeterminate,N={$table:H,$grid:e,columns:o,isAllChecked:B,isAllIndeterminate:w,isCustomStatus:l};_xeUtils.default.eachTree(o,(o,a,e,t,s)=>{if(!_||_({$table:H,column:o})){let e=0,t=0;x&&(a={$table:H,column:o,columnIndex:a,$columnIndex:a,$rowIndex:-1},b&&(e=_xeUtils.default.toNumber(_xeUtils.default.isFunction(b)?b(a):b)),C)&&(t=_xeUtils.default.toNumber(_xeUtils.default.isFunction(C)?C(a):C));var a=o.renderVisible,i=o.halfVisible,r=(0,_utils.formatText)(o.getTitle(),1),n=o.children&&o.children.length;let l=!!h&&!h({$table:H,column:o});var u=!a;O.push((0,_vue.h)("tr",{key:o.id,colid:o.id,class:["vxe-table-custom-popup--row level--"+o.level,{"is--group":n}],onDragstart:le,onDragend:oe,onDragover:ae},[m?(0,_vue.h)("td",{class:"vxe-table-custom-popup--column-item col--visible"},[(0,_vue.h)("div",{class:["vxe-table-custom--checkbox-option",{"is--checked":a,"is--indeterminate":i,"is--disabled":l}],title:getI18n("vxe.custom.setting.colVisible"),onClick:()=>{l||Z(o)}},[(0,_vue.h)("span",{class:["vxe-checkbox--icon",i?getIcon().TABLE_CHECKBOX_INDETERMINATE:a?getIcon().TABLE_CHECKBOX_CHECKED:getIcon().TABLE_CHECKBOX_UNCHECKED]})])]):(0,_vue.createCommentVNode)(),(0,_vue.h)("td",{class:"vxe-table-custom-popup--column-item col--name"},[(0,_vue.h)("div",{class:"vxe-table-custom-popup--name"},[v?E&&d||1===o.level?(0,_vue.h)("div",Object.assign({class:["vxe-table-custom-popup--column-sort-btn",{"is--disabled":l||u||o.renderFixed}],title:getI18n("vxe.custom.setting.sortHelpTip")},l||u||o.renderFixed?{}:{onMousedown:ee,onMouseup:te}),[(0,_vue.h)("i",{class:getIcon().TABLE_CUSTOM_SORT})]):(0,_vue.h)("div",{class:"vxe-table-custom-popup--column-sort-placeholder"}):(0,_vue.createCommentVNode)(),"html"===o.type?(0,_vue.h)("div",{key:"1",class:"vxe-table-custom-popup--title",innerHTML:r}):(0,_vue.h)("div",{key:"0",class:"vxe-table-custom-popup--title",title:r},r)])]),x?(0,_vue.h)("td",{class:"vxe-table-custom-popup--column-item col--resizable"},[o.children&&o.children.length||!(_xeUtils.default.isBoolean(o.resizable)?o.resizable:g.resizable||c)?(0,_vue.h)("span","-"):M?(0,_vue.h)(M,{type:"integer",immediate:!1,disabled:l||u,modelValue:o.renderResizeWidth,min:e||void 0,max:t||void 0,"onUpdate:modelValue"(e){e=Math.max(0,Number(e));o.renderResizeWidth=e},onChange(){ne(o)}}):(0,_vue.createCommentVNode)()]):(0,_vue.createCommentVNode)(),p?(0,_vue.h)("td",{class:"vxe-table-custom-popup--column-item col--fixed"},[s?(0,_vue.h)("span","-"):R?(0,_vue.h)(R,{modelValue:o.renderFixed||"",type:"button",size:"mini",disabled:l||u,options:[{label:getI18n("vxe.custom.setting.fixedLeft"),value:"left",disabled:l||u||T},{label:getI18n("vxe.custom.setting.fixedUnset"),value:"",disabled:l||u},{label:getI18n("vxe.custom.setting.fixedRight"),value:"right",disabled:l||u||T}],"onUpdate:modelValue"(e){J(o,e)}}):(0,_vue.createCommentVNode)()]):(0,_vue.createCommentVNode)()]))}});s={default:()=>A?H.callSlot(A,N):(0,_vue.h)("div",{ref:q,class:"vxe-table-custom-popup--body"},[y?(0,_vue.h)("div",{class:"vxe-table-custom-popup--table-top"},H.callSlot(y,N)):renderEmptyElement(H),(0,_vue.h)("div",{class:"vxe-table-custom-popup--table-wrapper"},[(0,_vue.h)("table",{},[(0,_vue.h)("colgroup",{},[m?(0,_vue.h)("col",{class:"vxe-table-custom-popup--table-col-seq"}):(0,_vue.createCommentVNode)(),(0,_vue.h)("col",{class:"vxe-table-custom-popup--table-col-title"}),x?(0,_vue.h)("col",{class:"vxe-table-custom-popup--table-col-width"}):(0,_vue.createCommentVNode)(),p?(0,_vue.h)("col",{class:"vxe-table-custom-popup--table-col-fixed"}):(0,_vue.createCommentVNode)()]),(0,_vue.h)("thead",{},[(0,_vue.h)("tr",{},[m?(0,_vue.h)("th",{},[(0,_vue.h)("div",{class:["vxe-table-custom--checkbox-option",{"is--checked":B,"is--indeterminate":w}],title:getI18n("vxe.table.allTitle"),onClick:Q},[(0,_vue.h)("span",{class:["vxe-checkbox--icon",w?getIcon().TABLE_CHECKBOX_INDETERMINATE:B?getIcon().TABLE_CHECKBOX_CHECKED:getIcon().TABLE_CHECKBOX_UNCHECKED]}),(0,_vue.h)("span",{class:"vxe-checkbox--label"},getI18n("vxe.toolbar.customAll"))])]):(0,_vue.createCommentVNode)(),(0,_vue.h)("th",{},getI18n("vxe.custom.setting.colTitle")),x?(0,_vue.h)("th",{},getI18n("vxe.custom.setting.colResizable")):(0,_vue.createCommentVNode)(),p?(0,_vue.h)("th",{},getI18n("vxe.custom.setting."+(u?"colFixedMax":"colFixed"),[u])):(0,_vue.createCommentVNode)()])]),(0,_vue.h)(_vue.TransitionGroup,{class:"vxe-table-custom--panel-list",tag:"tbody",name:"vxe-table-custom--list"},{default:()=>O})])]),k?(0,_vue.h)("div",{class:"vxe-table-custom-popup--table-bottom"},H.callSlot(k,N)):renderEmptyElement(H),se()]),footer:()=>S?H.callSlot(S,N):(0,_vue.h)("div",{class:"vxe-table-custom-popup--footer"},[F?(0,_vue.h)(F,{content:a.resetButtonText||getI18n("vxe.custom.cstmRestore"),disabled:!l,onClick:Y}):(0,_vue.createCommentVNode)(),d?F?(0,_vue.h)(F,{content:a.closeButtonText||getI18n("vxe.table.customClose"),onClick:j}):(0,_vue.createCommentVNode)():F?(0,_vue.h)(F,{content:a.cancelButtonText||getI18n("vxe.table.customCancel"),onClick:P}):(0,_vue.createCommentVNode)(),!d&&F?(0,_vue.h)(F,{status:"primary",content:a.confirmButtonText||getI18n("vxe.custom.cstmConfirm"),onClick:G}):(0,_vue.createCommentVNode)()])};return V&&(s.header=()=>H.callSlot(V,N)),"drawer"===i?L?(0,_vue.h)(L,{key:"drawer",className:["vxe-table-custom-drawer-wrapper","vxe-table--ignore-clear",I.className||""].join(" "),modelValue:t.visible,title:I.title||getI18n("vxe.custom.cstmTitle"),width:I.width||Math.min(880,Math.floor(.6*document.documentElement.clientWidth)),position:I.position,resize:!!I.resize,escClosable:!!I.escClosable,maskClosable:!!I.maskClosable,destroyOnClose:!0,showFooter:!0,"onUpdate:modelValue"(e){t.visible=e}},s):(0,_vue.createCommentVNode)():U?(0,_vue.h)(U,{key:"modal",className:["vxe-table-custom-modal-wrapper","vxe-table--ignore-clear",f.className||""].join(" "),modelValue:t.visible,title:f.title||getI18n("vxe.custom.cstmTitle"),width:f.width||Math.min(880,document.documentElement.clientWidth),minWidth:f.minWidth||700,height:f.height||Math.min(680,document.documentElement.clientHeight),minHeight:f.minHeight||400,showZoom:f.showZoom,showMaximize:f.showMaximize,showMinimize:f.showMinimize,mask:f.mask,lockView:f.lockView,resize:f.resize,escClosable:!!f.escClosable,maskClosable:!!f.maskClosable,destroyOnClose:!0,showFooter:!0,"onUpdate:modelValue"(e){t.visible=e}},s):(0,_vue.createCommentVNode)()}:()=>{var e=H.xeGrid,t=H.props,l=D.customStore,{treeConfig:t,rowGroupConfig:o,aggregateConfig:a}=t,{isCustomStatus:s,customColumnList:i}=K,r=W.value;let c=r.immediate;var n=X.value,u=l.maxHeight;let{checkMethod:d,visibleMethod:m,allowVisible:v,allowSort:p,allowFixed:x,trigger:h,placement:_}=r,g=$.value,b=n.isCrossDrag;var n=r.slots||{},C=n.header,f=n.top,I=n.bottom,T=n.default,n=n.footer;let E=[];var V={},y=l.isAll,k=l.isIndeterminate,e=("hover"===h&&(V.onMouseenter=S,V.onMouseleave=O),{$table:H,$grid:e,columns:i,isAllChecked:y,isAllIndeterminate:k,isCustomStatus:s});return _xeUtils.default.eachTree(i,(t,e,l,o,a)=>{if(!m||m({$table:H,column:t})){var s=t.renderVisible,i=t.halfVisible,r=t.children&&t.children.length,n=(0,_utils.formatText)(t.getTitle(),1);let e=!!d&&!d({$table:H,column:t});var u=!s;E.push((0,_vue.h)("li",{key:t.id,colid:t.id,class:["vxe-table-custom--option","level--"+t.level,{"is--hidden":e||u,"is--group":r}],onDragstart:le,onDragend:oe,onDragover:ae},[v?(0,_vue.h)("div",{class:["vxe-table-custom--checkbox-option",{"is--checked":s,"is--indeterminate":i,"is--disabled":e}],title:getI18n("vxe.custom.setting.colVisible"),onClick:()=>{e||Z(t)}},[(0,_vue.h)("span",{class:["vxe-checkbox--icon",i?getIcon().TABLE_CHECKBOX_INDETERMINATE:s?getIcon().TABLE_CHECKBOX_CHECKED:getIcon().TABLE_CHECKBOX_UNCHECKED]})]):(0,_vue.createCommentVNode)(),(0,_vue.h)("div",{class:"vxe-table-custom--name-option"},[p&&(b&&c||1===t.level)?(0,_vue.h)("div",{class:"vxe-table-custom--sort-option"},[(0,_vue.h)("span",Object.assign({class:["vxe-table-custom--sort-btn",{"is--disabled":e||u||t.renderFixed}],title:getI18n("vxe.custom.setting.sortHelpTip")},e||u||t.renderFixed?{}:{onMousedown:ee,onMouseup:te}),[(0,_vue.h)("i",{class:getIcon().TABLE_CUSTOM_SORT})])]):(0,_vue.createCommentVNode)(),"html"===t.type?(0,_vue.h)("div",{key:"1",class:"vxe-table-custom--checkbox-label",innerHTML:n}):(0,_vue.h)("div",{key:"0",class:"vxe-table-custom--checkbox-label"},n)]),!a&&x?(0,_vue.h)("div",{class:"vxe-table-custom--fixed-option"},[F?(0,_vue.h)(F,{mode:"text",icon:"left"===t.renderFixed?getIcon().TOOLBAR_TOOLS_FIXED_LEFT_ACTIVE:getIcon().TOOLBAR_TOOLS_FIXED_LEFT,status:"left"===t.renderFixed?"primary":"",disabled:e||u||g&&!t.renderFixed,title:getI18n("left"===t.renderFixed?"vxe.toolbar.cancelFixed":"vxe.toolbar.fixedLeft"),onClick:()=>{J(t,"left")}}):(0,_vue.createCommentVNode)(),F?(0,_vue.h)(F,{mode:"text",icon:"right"===t.renderFixed?getIcon().TOOLBAR_TOOLS_FIXED_RIGHT_ACTIVE:getIcon().TOOLBAR_TOOLS_FIXED_RIGHT,status:"right"===t.renderFixed?"primary":"",disabled:e||u||g&&!t.renderFixed,title:getI18n("right"===t.renderFixed?"vxe.toolbar.cancelFixed":"vxe.toolbar.fixedRight"),onClick:()=>{J(t,"right")}}):(0,_vue.createCommentVNode)()]):(0,_vue.createCommentVNode)()]))}}),(0,_vue.h)("div",{ref:A,key:"simple",class:["vxe-table-custom-wrapper","placement--"+_,{"is--active":l.visible}],style:u&&!["left","right"].includes(_)?{maxHeight:u+"px"}:{}},l.visible?[!t&&(a||o)&&H.getPivotTableAggregateSimplePanel?(0,_vue.h)(H.getPivotTableAggregateSimplePanel(),{customStore:l}):renderEmptyElement(H),(0,_vue.h)("div",{class:"vxe-table-custom--handle-wrapper"},[(0,_vue.h)("div",{class:"vxe-table-custom--header"},C?H.callSlot(C,e):[(0,_vue.h)("ul",{class:"vxe-table-custom--panel-list"},[(0,_vue.h)("li",{class:"vxe-table-custom--option"},[v?(0,_vue.h)("div",{class:["vxe-table-custom--checkbox-option",{"is--checked":y,"is--indeterminate":k}],title:getI18n("vxe.table.allTitle"),onClick:Q},[(0,_vue.h)("span",{class:["vxe-checkbox--icon",k?getIcon().TABLE_CHECKBOX_INDETERMINATE:y?getIcon().TABLE_CHECKBOX_CHECKED:getIcon().TABLE_CHECKBOX_UNCHECKED]}),(0,_vue.h)("span",{class:"vxe-checkbox--label"},getI18n("vxe.toolbar.customAll"))]):(0,_vue.h)("span",{class:"vxe-checkbox--label"},getI18n("vxe.table.customTitle"))])])]),(0,_vue.h)("div",{ref:q,class:"vxe-table-custom--body"},[f?(0,_vue.h)("div",{class:"vxe-table-custom--panel-top"},H.callSlot(f,e)):renderEmptyElement(H),T?(0,_vue.h)("div",{class:"vxe-table-custom--panel-body"},H.callSlot(T,e)):(0,_vue.h)(_vue.TransitionGroup,Object.assign({class:"vxe-table-custom--panel-list",name:"vxe-table-custom--list",tag:"ul"},V),{default:()=>E}),I?(0,_vue.h)("div",{class:"vxe-table-custom--panel-bottom"},H.callSlot(I,e)):renderEmptyElement(H),se()]),r.showFooter?(0,_vue.h)("div",{class:"vxe-table-custom--footer"},n?H.callSlot(n,e):[(0,_vue.h)("div",{class:"vxe-table-custom--footer-buttons"},[F?(0,_vue.h)(F,{mode:"text",content:r.resetButtonText||getI18n("vxe.table.customRestore"),disabled:!s,onClick:Y}):(0,_vue.createCommentVNode)(),c?F?(0,_vue.h)(F,{mode:"text",content:r.closeButtonText||getI18n("vxe.table.customClose"),onClick:j}):(0,_vue.createCommentVNode)():F?(0,_vue.h)(F,{mode:"text",content:r.cancelButtonText||getI18n("vxe.table.customCancel"),onClick:P}):(0,_vue.createCommentVNode)(),!c&&F?(0,_vue.h)(F,{mode:"text",status:"primary",content:r.confirmButtonText||getI18n("vxe.table.customConfirm"),onClick:G}):(0,_vue.createCommentVNode)()])]):null])]:[])})()}}});