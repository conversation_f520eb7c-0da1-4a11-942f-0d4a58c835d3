{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/php/php.js"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\nexport var conf = {\n    wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n    comments: {\n        lineComment: '//',\n        blockComment: ['/*', '*/']\n    },\n    brackets: [\n        ['{', '}'],\n        ['[', ']'],\n        ['(', ')']\n    ],\n    autoClosingPairs: [\n        { open: '{', close: '}', notIn: ['string'] },\n        { open: '[', close: ']', notIn: ['string'] },\n        { open: '(', close: ')', notIn: ['string'] },\n        { open: '\"', close: '\"', notIn: ['string'] },\n        { open: \"'\", close: \"'\", notIn: ['string', 'comment'] }\n    ],\n    folding: {\n        markers: {\n            start: new RegExp('^\\\\s*(#|//)region\\\\b'),\n            end: new RegExp('^\\\\s*(#|//)endregion\\\\b')\n        }\n    }\n};\nexport var language = {\n    defaultToken: '',\n    tokenPostfix: '',\n    // ignoreCase: true,\n    // The main tokenizer for our languages\n    tokenizer: {\n        root: [\n            [/<\\?((php)|=)?/, { token: '@rematch', switchTo: '@phpInSimpleState.root' }],\n            [/<!DOCTYPE/, 'metatag.html', '@doctype'],\n            [/<!--/, 'comment.html', '@comment'],\n            [/(<)(\\w+)(\\/>)/, ['delimiter.html', 'tag.html', 'delimiter.html']],\n            [/(<)(script)/, ['delimiter.html', { token: 'tag.html', next: '@script' }]],\n            [/(<)(style)/, ['delimiter.html', { token: 'tag.html', next: '@style' }]],\n            [/(<)([:\\w]+)/, ['delimiter.html', { token: 'tag.html', next: '@otherTag' }]],\n            [/(<\\/)(\\w+)/, ['delimiter.html', { token: 'tag.html', next: '@otherTag' }]],\n            [/</, 'delimiter.html'],\n            [/[^<]+/] // text\n        ],\n        doctype: [\n            [/<\\?((php)|=)?/, { token: '@rematch', switchTo: '@phpInSimpleState.comment' }],\n            [/[^>]+/, 'metatag.content.html'],\n            [/>/, 'metatag.html', '@pop']\n        ],\n        comment: [\n            [/<\\?((php)|=)?/, { token: '@rematch', switchTo: '@phpInSimpleState.comment' }],\n            [/-->/, 'comment.html', '@pop'],\n            [/[^-]+/, 'comment.content.html'],\n            [/./, 'comment.content.html']\n        ],\n        otherTag: [\n            [/<\\?((php)|=)?/, { token: '@rematch', switchTo: '@phpInSimpleState.otherTag' }],\n            [/\\/?>/, 'delimiter.html', '@pop'],\n            [/\"([^\"]*)\"/, 'attribute.value'],\n            [/'([^']*)'/, 'attribute.value'],\n            [/[\\w\\-]+/, 'attribute.name'],\n            [/=/, 'delimiter'],\n            [/[ \\t\\r\\n]+/] // whitespace\n        ],\n        // -- BEGIN <script> tags handling\n        // After <script\n        script: [\n            [/<\\?((php)|=)?/, { token: '@rematch', switchTo: '@phpInSimpleState.script' }],\n            [/type/, 'attribute.name', '@scriptAfterType'],\n            [/\"([^\"]*)\"/, 'attribute.value'],\n            [/'([^']*)'/, 'attribute.value'],\n            [/[\\w\\-]+/, 'attribute.name'],\n            [/=/, 'delimiter'],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@scriptEmbedded.text/javascript',\n                    nextEmbedded: 'text/javascript'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [\n                /(<\\/)(script\\s*)(>)/,\n                ['delimiter.html', 'tag.html', { token: 'delimiter.html', next: '@pop' }]\n            ]\n        ],\n        // After <script ... type\n        scriptAfterType: [\n            [\n                /<\\?((php)|=)?/,\n                {\n                    token: '@rematch',\n                    switchTo: '@phpInSimpleState.scriptAfterType'\n                }\n            ],\n            [/=/, 'delimiter', '@scriptAfterTypeEquals'],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@scriptEmbedded.text/javascript',\n                    nextEmbedded: 'text/javascript'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/script\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        // After <script ... type =\n        scriptAfterTypeEquals: [\n            [\n                /<\\?((php)|=)?/,\n                {\n                    token: '@rematch',\n                    switchTo: '@phpInSimpleState.scriptAfterTypeEquals'\n                }\n            ],\n            [\n                /\"([^\"]*)\"/,\n                {\n                    token: 'attribute.value',\n                    switchTo: '@scriptWithCustomType.$1'\n                }\n            ],\n            [\n                /'([^']*)'/,\n                {\n                    token: 'attribute.value',\n                    switchTo: '@scriptWithCustomType.$1'\n                }\n            ],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@scriptEmbedded.text/javascript',\n                    nextEmbedded: 'text/javascript'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/script\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        // After <script ... type = $S2\n        scriptWithCustomType: [\n            [\n                /<\\?((php)|=)?/,\n                {\n                    token: '@rematch',\n                    switchTo: '@phpInSimpleState.scriptWithCustomType.$S2'\n                }\n            ],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@scriptEmbedded.$S2',\n                    nextEmbedded: '$S2'\n                }\n            ],\n            [/\"([^\"]*)\"/, 'attribute.value'],\n            [/'([^']*)'/, 'attribute.value'],\n            [/[\\w\\-]+/, 'attribute.name'],\n            [/=/, 'delimiter'],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/script\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        scriptEmbedded: [\n            [\n                /<\\?((php)|=)?/,\n                {\n                    token: '@rematch',\n                    switchTo: '@phpInEmbeddedState.scriptEmbedded.$S2',\n                    nextEmbedded: '@pop'\n                }\n            ],\n            [/<\\/script/, { token: '@rematch', next: '@pop', nextEmbedded: '@pop' }]\n        ],\n        // -- END <script> tags handling\n        // -- BEGIN <style> tags handling\n        // After <style\n        style: [\n            [/<\\?((php)|=)?/, { token: '@rematch', switchTo: '@phpInSimpleState.style' }],\n            [/type/, 'attribute.name', '@styleAfterType'],\n            [/\"([^\"]*)\"/, 'attribute.value'],\n            [/'([^']*)'/, 'attribute.value'],\n            [/[\\w\\-]+/, 'attribute.name'],\n            [/=/, 'delimiter'],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@styleEmbedded.text/css',\n                    nextEmbedded: 'text/css'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [\n                /(<\\/)(style\\s*)(>)/,\n                ['delimiter.html', 'tag.html', { token: 'delimiter.html', next: '@pop' }]\n            ]\n        ],\n        // After <style ... type\n        styleAfterType: [\n            [\n                /<\\?((php)|=)?/,\n                {\n                    token: '@rematch',\n                    switchTo: '@phpInSimpleState.styleAfterType'\n                }\n            ],\n            [/=/, 'delimiter', '@styleAfterTypeEquals'],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@styleEmbedded.text/css',\n                    nextEmbedded: 'text/css'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/style\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        // After <style ... type =\n        styleAfterTypeEquals: [\n            [\n                /<\\?((php)|=)?/,\n                {\n                    token: '@rematch',\n                    switchTo: '@phpInSimpleState.styleAfterTypeEquals'\n                }\n            ],\n            [\n                /\"([^\"]*)\"/,\n                {\n                    token: 'attribute.value',\n                    switchTo: '@styleWithCustomType.$1'\n                }\n            ],\n            [\n                /'([^']*)'/,\n                {\n                    token: 'attribute.value',\n                    switchTo: '@styleWithCustomType.$1'\n                }\n            ],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@styleEmbedded.text/css',\n                    nextEmbedded: 'text/css'\n                }\n            ],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/style\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        // After <style ... type = $S2\n        styleWithCustomType: [\n            [\n                /<\\?((php)|=)?/,\n                {\n                    token: '@rematch',\n                    switchTo: '@phpInSimpleState.styleWithCustomType.$S2'\n                }\n            ],\n            [\n                />/,\n                {\n                    token: 'delimiter.html',\n                    next: '@styleEmbedded.$S2',\n                    nextEmbedded: '$S2'\n                }\n            ],\n            [/\"([^\"]*)\"/, 'attribute.value'],\n            [/'([^']*)'/, 'attribute.value'],\n            [/[\\w\\-]+/, 'attribute.name'],\n            [/=/, 'delimiter'],\n            [/[ \\t\\r\\n]+/],\n            [/<\\/style\\s*>/, { token: '@rematch', next: '@pop' }]\n        ],\n        styleEmbedded: [\n            [\n                /<\\?((php)|=)?/,\n                {\n                    token: '@rematch',\n                    switchTo: '@phpInEmbeddedState.styleEmbedded.$S2',\n                    nextEmbedded: '@pop'\n                }\n            ],\n            [/<\\/style/, { token: '@rematch', next: '@pop', nextEmbedded: '@pop' }]\n        ],\n        // -- END <style> tags handling\n        phpInSimpleState: [\n            [/<\\?((php)|=)?/, 'metatag.php'],\n            [/\\?>/, { token: 'metatag.php', switchTo: '@$S2.$S3' }],\n            { include: 'phpRoot' }\n        ],\n        phpInEmbeddedState: [\n            [/<\\?((php)|=)?/, 'metatag.php'],\n            [\n                /\\?>/,\n                {\n                    token: 'metatag.php',\n                    switchTo: '@$S2.$S3',\n                    nextEmbedded: '$S3'\n                }\n            ],\n            { include: 'phpRoot' }\n        ],\n        phpRoot: [\n            [\n                /[a-zA-Z_]\\w*/,\n                {\n                    cases: {\n                        '@phpKeywords': { token: 'keyword.php' },\n                        '@phpCompileTimeConstants': { token: 'constant.php' },\n                        '@default': 'identifier.php'\n                    }\n                }\n            ],\n            [\n                /[$a-zA-Z_]\\w*/,\n                {\n                    cases: {\n                        '@phpPreDefinedVariables': {\n                            token: 'variable.predefined.php'\n                        },\n                        '@default': 'variable.php'\n                    }\n                }\n            ],\n            // brackets\n            [/[{}]/, 'delimiter.bracket.php'],\n            [/[\\[\\]]/, 'delimiter.array.php'],\n            [/[()]/, 'delimiter.parenthesis.php'],\n            // whitespace\n            [/[ \\t\\r\\n]+/],\n            // comments\n            [/(#|\\/\\/)$/, 'comment.php'],\n            [/(#|\\/\\/)/, 'comment.php', '@phpLineComment'],\n            // block comments\n            [/\\/\\*/, 'comment.php', '@phpComment'],\n            // strings\n            [/\"/, 'string.php', '@phpDoubleQuoteString'],\n            [/'/, 'string.php', '@phpSingleQuoteString'],\n            // delimiters\n            [/[\\+\\-\\*\\%\\&\\|\\^\\~\\!\\=\\<\\>\\/\\?\\;\\:\\.\\,\\@]/, 'delimiter.php'],\n            // numbers\n            [/\\d*\\d+[eE]([\\-+]?\\d+)?/, 'number.float.php'],\n            [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, 'number.float.php'],\n            [/0[xX][0-9a-fA-F']*[0-9a-fA-F]/, 'number.hex.php'],\n            [/0[0-7']*[0-7]/, 'number.octal.php'],\n            [/0[bB][0-1']*[0-1]/, 'number.binary.php'],\n            [/\\d[\\d']*/, 'number.php'],\n            [/\\d/, 'number.php']\n        ],\n        phpComment: [\n            [/\\*\\//, 'comment.php', '@pop'],\n            [/[^*]+/, 'comment.php'],\n            [/./, 'comment.php']\n        ],\n        phpLineComment: [\n            [/\\?>/, { token: '@rematch', next: '@pop' }],\n            [/.$/, 'comment.php', '@pop'],\n            [/[^?]+$/, 'comment.php', '@pop'],\n            [/[^?]+/, 'comment.php'],\n            [/./, 'comment.php']\n        ],\n        phpDoubleQuoteString: [\n            [/[^\\\\\"]+/, 'string.php'],\n            [/@escapes/, 'string.escape.php'],\n            [/\\\\./, 'string.escape.invalid.php'],\n            [/\"/, 'string.php', '@pop']\n        ],\n        phpSingleQuoteString: [\n            [/[^\\\\']+/, 'string.php'],\n            [/@escapes/, 'string.escape.php'],\n            [/\\\\./, 'string.escape.invalid.php'],\n            [/'/, 'string.php', '@pop']\n        ]\n    },\n    phpKeywords: [\n        'abstract',\n        'and',\n        'array',\n        'as',\n        'break',\n        'callable',\n        'case',\n        'catch',\n        'cfunction',\n        'class',\n        'clone',\n        'const',\n        'continue',\n        'declare',\n        'default',\n        'do',\n        'else',\n        'elseif',\n        'enddeclare',\n        'endfor',\n        'endforeach',\n        'endif',\n        'endswitch',\n        'endwhile',\n        'extends',\n        'false',\n        'final',\n        'for',\n        'foreach',\n        'function',\n        'global',\n        'goto',\n        'if',\n        'implements',\n        'interface',\n        'instanceof',\n        'insteadof',\n        'namespace',\n        'new',\n        'null',\n        'object',\n        'old_function',\n        'or',\n        'private',\n        'protected',\n        'public',\n        'resource',\n        'static',\n        'switch',\n        'throw',\n        'trait',\n        'try',\n        'true',\n        'use',\n        'var',\n        'while',\n        'xor',\n        'die',\n        'echo',\n        'empty',\n        'exit',\n        'eval',\n        'include',\n        'include_once',\n        'isset',\n        'list',\n        'require',\n        'require_once',\n        'return',\n        'print',\n        'unset',\n        'yield',\n        '__construct'\n    ],\n    phpCompileTimeConstants: [\n        '__CLASS__',\n        '__DIR__',\n        '__FILE__',\n        '__LINE__',\n        '__NAMESPACE__',\n        '__METHOD__',\n        '__FUNCTION__',\n        '__TRAIT__'\n    ],\n    phpPreDefinedVariables: [\n        '$GLOBALS',\n        '$_SERVER',\n        '$_GET',\n        '$_POST',\n        '$_FILES',\n        '$_REQUEST',\n        '$_SESSION',\n        '$_ENV',\n        '$_COOKIE',\n        '$php_errormsg',\n        '$HTTP_RAW_POST_DATA',\n        '$http_response_header',\n        '$argc',\n        '$argv'\n    ],\n    escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/\n};\n// TESTED WITH\n// <style type=\"text/css\" >\n//   .boo { background: blue;\n//   <?=''?>\n//   }\n//   .boo { background: blue;  <?=''?>  }\n// </style>\n// <!--\n// <?= '' ?>\n// -->\n// <?php\n// // The next line contains a syntax error:\n// __construct\n// if () {\n// \treturn \"The parser recovers from this type of syntax error\";\n// }\n// ?>\n// <html>\n// <head>\n// \t<title <?=''?>>Example page</title>\n//   <style <?=''?>>\n//     .boo { background: blue; <?=''?> }\n//   </style>\n// </head>\n// <body>\n// <script <?=''?> type<?=''?>=<?=''?>\"text/javascript\"<?=''?>>\n// \t// Some PHP embedded inside JS\n// \t// Generated <?=date('l, F jS, Y')?>\n// \tvar server_token = <?=rand(5, 10000)?>\n// \tif (typeof server_token === 'number') {\n// \t\talert('token: ' + server_token);\n// \t}\n// </script>\n// <div>\n// Hello\n// <? if (isset($user)) { ?>\n// \t<b><?=$user?></b>\n// <? } else { ?>\n// \t<i>guest</i>\n// <? } ?>\n// !\n// </div>\n// <?php\n// \t/* Example PHP file\n// \tmultiline comment\n// \t*/\n//  # Another single line comment\n// \t$cards = array(\"ah\", \"ac\", \"ad\", \"as\",\n// \t\t\"2h\", \"2c\", \"2d\", \"2s\",\n// \t\t\"3h\", \"3c\", \"3d\", \"3s\",\n// \t\t\"4h\", \"4c\", \"4d\", \"4s\",\n// \t\t\"5h\", \"5c\", \"5d\", \"5s\",\n// \t\t\"6h\", \"6c\", \"6d\", \"6s\",\n// \t\t\"7h\", \"7c\", \"7d\", \"7s\",\n// \t\t\"8h\", \"8c\", \"8d\", \"8s\",\n// \t\t\"9h\", \"9c\", \"9d\", \"9s\",\n// \t\t\"th\", \"tc\", \"td\", \"ts\",\n// \t\t\"jh\", \"jc\", \"jd\", \"js\",\n// \t\t\"qh\", \"qc\", \"qd\", \"qs\",\n// \t\t\"kh\", \"kc\", \"kd\", \"ks\");\n// \tsrand(time());\n// \tfor($i = 0; $i < 52; $i++) {\n// \t\t$count = count($cards);\n// \t\t$random = (rand()%$count);\n// \t\tif($cards[$random] == \"\") {\n// \t\t\t$i--;\n// \t\t} else {\n// \t\t\t$deck[] = $cards[$random];\n// \t\t\t$cards[$random] = \"\";\n// \t\t}\n// \t}\n// $_GET\n// __CLASS__\n// \tsrand(time());\n// \t$starting_point = (rand()%51);\n// \tprint(\"Starting point for cut cards is: $starting_point<p>\");\n// \t// display shuffled cards (EXAMPLE ONLY)\n// \tfor ($index = 0; $index < 52; $index++) {\n// \t\tif ($starting_point == 52) { $starting_point = 0; }\n// \t\tprint(\"Uncut Point: <strong>$deck[$index]</strong> \");\n// \t\tprint(\"Starting Point: <strong>$deck[$starting_point]</strong><br>\");\n// \t\t$starting_point++;\n// \t}\n// ?>\n// </body>\n// </html>\n"], "mappings": ";;;AAIO,IAAI,OAAO;AAAA,EACd,aAAa;AAAA,EACb,UAAU;AAAA,IACN,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC7B;AAAA,EACA,UAAU;AAAA,IACN,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACb;AAAA,EACA,kBAAkB;AAAA,IACd,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EAC1D;AAAA,EACA,SAAS;AAAA,IACL,SAAS;AAAA,MACL,OAAO,IAAI,OAAO,sBAAsB;AAAA,MACxC,KAAK,IAAI,OAAO,yBAAyB;AAAA,IAC7C;AAAA,EACJ;AACJ;AACO,IAAI,WAAW;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAAA;AAAA;AAAA,EAGd,WAAW;AAAA,IACP,MAAM;AAAA,MACF,CAAC,iBAAiB,EAAE,OAAO,YAAY,UAAU,yBAAyB,CAAC;AAAA,MAC3E,CAAC,aAAa,gBAAgB,UAAU;AAAA,MACxC,CAAC,QAAQ,gBAAgB,UAAU;AAAA,MACnC,CAAC,iBAAiB,CAAC,kBAAkB,YAAY,gBAAgB,CAAC;AAAA,MAClE,CAAC,eAAe,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,UAAU,CAAC,CAAC;AAAA,MAC1E,CAAC,cAAc,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,SAAS,CAAC,CAAC;AAAA,MACxE,CAAC,eAAe,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,YAAY,CAAC,CAAC;AAAA,MAC5E,CAAC,cAAc,CAAC,kBAAkB,EAAE,OAAO,YAAY,MAAM,YAAY,CAAC,CAAC;AAAA,MAC3E,CAAC,KAAK,gBAAgB;AAAA,MACtB,CAAC,OAAO;AAAA;AAAA,IACZ;AAAA,IACA,SAAS;AAAA,MACL,CAAC,iBAAiB,EAAE,OAAO,YAAY,UAAU,4BAA4B,CAAC;AAAA,MAC9E,CAAC,SAAS,sBAAsB;AAAA,MAChC,CAAC,KAAK,gBAAgB,MAAM;AAAA,IAChC;AAAA,IACA,SAAS;AAAA,MACL,CAAC,iBAAiB,EAAE,OAAO,YAAY,UAAU,4BAA4B,CAAC;AAAA,MAC9E,CAAC,OAAO,gBAAgB,MAAM;AAAA,MAC9B,CAAC,SAAS,sBAAsB;AAAA,MAChC,CAAC,KAAK,sBAAsB;AAAA,IAChC;AAAA,IACA,UAAU;AAAA,MACN,CAAC,iBAAiB,EAAE,OAAO,YAAY,UAAU,6BAA6B,CAAC;AAAA,MAC/E,CAAC,QAAQ,kBAAkB,MAAM;AAAA,MACjC,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY;AAAA;AAAA,IACjB;AAAA;AAAA;AAAA,IAGA,QAAQ;AAAA,MACJ,CAAC,iBAAiB,EAAE,OAAO,YAAY,UAAU,2BAA2B,CAAC;AAAA,MAC7E,CAAC,QAAQ,kBAAkB,kBAAkB;AAAA,MAC7C,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb;AAAA,QACI;AAAA,QACA,CAAC,kBAAkB,YAAY,EAAE,OAAO,kBAAkB,MAAM,OAAO,CAAC;AAAA,MAC5E;AAAA,IACJ;AAAA;AAAA,IAEA,iBAAiB;AAAA,MACb;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA,CAAC,KAAK,aAAa,wBAAwB;AAAA,MAC3C;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACzD;AAAA;AAAA,IAEA,uBAAuB;AAAA,MACnB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACzD;AAAA;AAAA,IAEA,sBAAsB;AAAA,MAClB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY;AAAA,MACb,CAAC,iBAAiB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACzD;AAAA,IACA,gBAAgB;AAAA,MACZ;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,UACV,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,aAAa,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,IAC3E;AAAA;AAAA;AAAA;AAAA,IAIA,OAAO;AAAA,MACH,CAAC,iBAAiB,EAAE,OAAO,YAAY,UAAU,0BAA0B,CAAC;AAAA,MAC5E,CAAC,QAAQ,kBAAkB,iBAAiB;AAAA,MAC5C,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb;AAAA,QACI;AAAA,QACA,CAAC,kBAAkB,YAAY,EAAE,OAAO,kBAAkB,MAAM,OAAO,CAAC;AAAA,MAC5E;AAAA,IACJ;AAAA;AAAA,IAEA,gBAAgB;AAAA,MACZ;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA,CAAC,KAAK,aAAa,uBAAuB;AAAA,MAC1C;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACxD;AAAA;AAAA,IAEA,sBAAsB;AAAA,MAClB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY;AAAA,MACb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACxD;AAAA;AAAA,IAEA,qBAAqB;AAAA,MACjB;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,QACd;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,aAAa,iBAAiB;AAAA,MAC/B,CAAC,WAAW,gBAAgB;AAAA,MAC5B,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY;AAAA,MACb,CAAC,gBAAgB,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,IACxD;AAAA,IACA,eAAe;AAAA,MACX;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,UACV,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,CAAC,YAAY,EAAE,OAAO,YAAY,MAAM,QAAQ,cAAc,OAAO,CAAC;AAAA,IAC1E;AAAA;AAAA,IAEA,kBAAkB;AAAA,MACd,CAAC,iBAAiB,aAAa;AAAA,MAC/B,CAAC,OAAO,EAAE,OAAO,eAAe,UAAU,WAAW,CAAC;AAAA,MACtD,EAAE,SAAS,UAAU;AAAA,IACzB;AAAA,IACA,oBAAoB;AAAA,MAChB,CAAC,iBAAiB,aAAa;AAAA,MAC/B;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,UACP,UAAU;AAAA,UACV,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,MACA,EAAE,SAAS,UAAU;AAAA,IACzB;AAAA,IACA,SAAS;AAAA,MACL;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,gBAAgB,EAAE,OAAO,cAAc;AAAA,YACvC,4BAA4B,EAAE,OAAO,eAAe;AAAA,YACpD,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,UACI,OAAO;AAAA,YACH,2BAA2B;AAAA,cACvB,OAAO;AAAA,YACX;AAAA,YACA,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA,MAEA,CAAC,QAAQ,uBAAuB;AAAA,MAChC,CAAC,UAAU,qBAAqB;AAAA,MAChC,CAAC,QAAQ,2BAA2B;AAAA;AAAA,MAEpC,CAAC,YAAY;AAAA;AAAA,MAEb,CAAC,aAAa,aAAa;AAAA,MAC3B,CAAC,YAAY,eAAe,iBAAiB;AAAA;AAAA,MAE7C,CAAC,QAAQ,eAAe,aAAa;AAAA;AAAA,MAErC,CAAC,KAAK,cAAc,uBAAuB;AAAA,MAC3C,CAAC,KAAK,cAAc,uBAAuB;AAAA;AAAA,MAE3C,CAAC,4CAA4C,eAAe;AAAA;AAAA,MAE5D,CAAC,0BAA0B,kBAAkB;AAAA,MAC7C,CAAC,4BAA4B,kBAAkB;AAAA,MAC/C,CAAC,iCAAiC,gBAAgB;AAAA,MAClD,CAAC,iBAAiB,kBAAkB;AAAA,MACpC,CAAC,qBAAqB,mBAAmB;AAAA,MACzC,CAAC,YAAY,YAAY;AAAA,MACzB,CAAC,MAAM,YAAY;AAAA,IACvB;AAAA,IACA,YAAY;AAAA,MACR,CAAC,QAAQ,eAAe,MAAM;AAAA,MAC9B,CAAC,SAAS,aAAa;AAAA,MACvB,CAAC,KAAK,aAAa;AAAA,IACvB;AAAA,IACA,gBAAgB;AAAA,MACZ,CAAC,OAAO,EAAE,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,MAC3C,CAAC,MAAM,eAAe,MAAM;AAAA,MAC5B,CAAC,UAAU,eAAe,MAAM;AAAA,MAChC,CAAC,SAAS,aAAa;AAAA,MACvB,CAAC,KAAK,aAAa;AAAA,IACvB;AAAA,IACA,sBAAsB;AAAA,MAClB,CAAC,WAAW,YAAY;AAAA,MACxB,CAAC,YAAY,mBAAmB;AAAA,MAChC,CAAC,OAAO,2BAA2B;AAAA,MACnC,CAAC,KAAK,cAAc,MAAM;AAAA,IAC9B;AAAA,IACA,sBAAsB;AAAA,MAClB,CAAC,WAAW,YAAY;AAAA,MACxB,CAAC,YAAY,mBAAmB;AAAA,MAChC,CAAC,OAAO,2BAA2B;AAAA,MACnC,CAAC,KAAK,cAAc,MAAM;AAAA,IAC9B;AAAA,EACJ;AAAA,EACA,aAAa;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,yBAAyB;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,wBAAwB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AACb;", "names": []}