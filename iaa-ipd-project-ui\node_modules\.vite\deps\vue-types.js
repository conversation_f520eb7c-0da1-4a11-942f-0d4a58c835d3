import "./chunk-GFT2G5UO.js";

// node_modules/is-plain-object/dist/is-plain-object.mjs
function isObject(o2) {
  return Object.prototype.toString.call(o2) === "[object Object]";
}
function isPlainObject(o2) {
  var ctor, prot;
  if (isObject(o2) === false)
    return false;
  ctor = o2.constructor;
  if (ctor === void 0)
    return true;
  prot = ctor.prototype;
  if (isObject(prot) === false)
    return false;
  if (prot.hasOwnProperty("isPrototypeOf") === false) {
    return false;
  }
  return true;
}

// node_modules/vue-types/dist/vue-types.modern.js
function t() {
  return t = Object.assign ? Object.assign.bind() : function(e) {
    for (var t2 = 1; t2 < arguments.length; t2++) {
      var n2 = arguments[t2];
      for (var r2 in n2)
        Object.prototype.hasOwnProperty.call(n2, r2) && (e[r2] = n2[r2]);
    }
    return e;
  }, t.apply(this, arguments);
}
function n(e, t2) {
  if (null == e)
    return {};
  var n2 = {};
  for (var r2 in e)
    if (Object.prototype.hasOwnProperty.call(e, r2)) {
      if (t2.indexOf(r2) >= 0)
        continue;
      n2[r2] = e[r2];
    }
  return n2;
}
var r = { silent: false, logLevel: "warn" };
var i = ["validator"];
var o = Object.prototype;
var a = o.toString;
var s = o.hasOwnProperty;
var u = /^\s*function (\w+)/;
function l(e) {
  var t2;
  const n2 = null !== (t2 = null == e ? void 0 : e.type) && void 0 !== t2 ? t2 : e;
  if (n2) {
    const e2 = n2.toString().match(u);
    return e2 ? e2[1] : "";
  }
  return "";
}
var c = isPlainObject;
function f() {
}
var d = f;
if (true) {
  const e = "undefined" != typeof console;
  d = e ? function(e2, t2 = r.logLevel) {
    false === r.silent && console[t2](`[VueTypes warn]: ${e2}`);
  } : f;
}
var p = (e, t2) => s.call(e, t2);
var y = Number.isInteger || function(e) {
  return "number" == typeof e && isFinite(e) && Math.floor(e) === e;
};
var v = Array.isArray || function(e) {
  return "[object Array]" === a.call(e);
};
var h = (e) => "[object Function]" === a.call(e);
var b = (e, t2) => c(e) && p(e, "_vueTypes_name") && (!t2 || e._vueTypes_name === t2);
var g = (e) => c(e) && (p(e, "type") || ["_vueTypes_name", "validator", "default", "required"].some((t2) => p(e, t2)));
function O(e, t2) {
  return Object.defineProperty(e.bind(t2), "__original", { value: e });
}
function m(e, t2, n2 = false) {
  let r2, i2 = true, o2 = "";
  r2 = c(e) ? e : { type: e };
  const a2 = b(r2) ? r2._vueTypes_name + " - " : "";
  if (g(r2) && null !== r2.type) {
    if (void 0 === r2.type || true === r2.type)
      return i2;
    if (!r2.required && null == t2)
      return i2;
    v(r2.type) ? (i2 = r2.type.some((e2) => true === m(e2, t2, true)), o2 = r2.type.map((e2) => l(e2)).join(" or ")) : (o2 = l(r2), i2 = "Array" === o2 ? v(t2) : "Object" === o2 ? c(t2) : "String" === o2 || "Number" === o2 || "Boolean" === o2 || "Function" === o2 ? function(e2) {
      if (null == e2)
        return "";
      const t3 = e2.constructor.toString().match(u);
      return t3 ? t3[1].replace(/^Async/, "") : "";
    }(t2) === o2 : t2 instanceof r2.type);
  }
  if (!i2) {
    const e2 = `${a2}value "${t2}" should be of type "${o2}"`;
    return false === n2 ? (d(e2), false) : e2;
  }
  if (p(r2, "validator") && h(r2.validator)) {
    const e2 = d, o3 = [];
    if (d = (e3) => {
      o3.push(e3);
    }, i2 = r2.validator(t2), d = e2, !i2) {
      const e3 = (o3.length > 1 ? "* " : "") + o3.join("\n* ");
      return o3.length = 0, false === n2 ? (d(e3), i2) : e3;
    }
  }
  return i2;
}
function j(e, t2) {
  const n2 = Object.defineProperties(t2, { _vueTypes_name: { value: e, writable: true }, isRequired: { get() {
    return this.required = true, this;
  } }, def: { value(e2) {
    return void 0 === e2 ? this.type === Boolean || Array.isArray(this.type) && this.type.includes(Boolean) ? void (this.default = void 0) : (p(this, "default") && delete this.default, this) : h(e2) || true === m(this, e2, true) ? (this.default = v(e2) ? () => [...e2] : c(e2) ? () => Object.assign({}, e2) : e2, this) : (d(`${this._vueTypes_name} - invalid default value: "${e2}"`), this);
  } } }), { validator: r2 } = n2;
  return h(r2) && (n2.validator = O(r2, n2)), n2;
}
function _(e, t2) {
  const n2 = j(e, t2);
  return Object.defineProperty(n2, "validate", { value(e2) {
    return h(this.validator) && d(`${this._vueTypes_name} - calling .validate() will overwrite the current custom validator function. Validator info:
${JSON.stringify(this)}`), this.validator = O(e2, this), this;
  } });
}
function T(e, t2, r2) {
  const o2 = function(e2) {
    const t3 = {};
    return Object.getOwnPropertyNames(e2).forEach((n2) => {
      t3[n2] = Object.getOwnPropertyDescriptor(e2, n2);
    }), Object.defineProperties({}, t3);
  }(t2);
  if (o2._vueTypes_name = e, !c(r2))
    return o2;
  const { validator: a2 } = r2, s2 = n(r2, i);
  if (h(a2)) {
    let { validator: e2 } = o2;
    e2 && (e2 = null !== (l2 = (u2 = e2).__original) && void 0 !== l2 ? l2 : u2), o2.validator = O(e2 ? function(t3) {
      return e2.call(this, t3) && a2.call(this, t3);
    } : a2, o2);
  }
  var u2, l2;
  return Object.assign(o2, s2);
}
function $(e) {
  return e.replace(/^(?!\s*$)/gm, "  ");
}
var w = () => _("any", {});
var x = () => _("function", { type: Function });
var P = () => _("boolean", { type: Boolean });
var A = () => _("string", { type: String });
var E = () => _("number", { type: Number });
var S = () => _("array", { type: Array });
var N = () => _("object", { type: Object });
var V = () => j("integer", { type: Number, validator(e) {
  const t2 = y(e);
  return false === t2 && d(`integer - "${e}" is not an integer`), t2;
} });
var q = () => j("symbol", { validator(e) {
  const t2 = "symbol" == typeof e;
  return false === t2 && d(`symbol - invalid value "${e}"`), t2;
} });
var k = () => Object.defineProperty({ type: null, validator(e) {
  const t2 = null === e;
  return false === t2 && d("nullable - value should be null"), t2;
} }, "_vueTypes_name", { value: "nullable" });
function D(e, t2 = "custom validation failed") {
  if ("function" != typeof e)
    throw new TypeError("[VueTypes error]: You must provide a function as argument");
  return j(e.name || "<<anonymous function>>", { type: null, validator(n2) {
    const r2 = e(n2);
    return r2 || d(`${this._vueTypes_name} - ${t2}`), r2;
  } });
}
function L(e) {
  if (!v(e))
    throw new TypeError("[VueTypes error]: You must provide an array as argument.");
  const t2 = `oneOf - value should be one of "${e.map((e2) => "symbol" == typeof e2 ? e2.toString() : e2).join('", "')}".`, n2 = { validator(n3) {
    const r2 = -1 !== e.indexOf(n3);
    return r2 || d(t2), r2;
  } };
  if (-1 === e.indexOf(null)) {
    const t3 = e.reduce((e2, t4) => {
      if (null != t4) {
        const n3 = t4.constructor;
        -1 === e2.indexOf(n3) && e2.push(n3);
      }
      return e2;
    }, []);
    t3.length > 0 && (n2.type = t3);
  }
  return j("oneOf", n2);
}
function B(e) {
  if (!v(e))
    throw new TypeError("[VueTypes error]: You must provide an array as argument");
  let t2 = false, n2 = false, r2 = [];
  for (let i3 = 0; i3 < e.length; i3 += 1) {
    const o2 = e[i3];
    if (g(o2)) {
      if (h(o2.validator) && (t2 = true), b(o2, "oneOf") && o2.type) {
        r2 = r2.concat(o2.type);
        continue;
      }
      if (b(o2, "nullable")) {
        n2 = true;
        continue;
      }
      if (true === o2.type || !o2.type) {
        d('oneOfType - invalid usage of "true" and "null" as types.');
        continue;
      }
      r2 = r2.concat(o2.type);
    } else
      r2.push(o2);
  }
  r2 = r2.filter((e2, t3) => r2.indexOf(e2) === t3);
  const i2 = false === n2 && r2.length > 0 ? r2 : null;
  return j("oneOfType", t2 ? { type: i2, validator(t3) {
    const n3 = [], r3 = e.some((e2) => {
      const r4 = m(e2, t3, true);
      return "string" == typeof r4 && n3.push(r4), true === r4;
    });
    return r3 || d(`oneOfType - provided value does not match any of the ${n3.length} passed-in validators:
${$(n3.join("\n"))}`), r3;
  } } : { type: i2 });
}
function F(e) {
  return j("arrayOf", { type: Array, validator(t2) {
    let n2 = "";
    const r2 = t2.every((t3) => (n2 = m(e, t3, true), true === n2));
    return r2 || d(`arrayOf - value validation error:
${$(n2)}`), r2;
  } });
}
function Y(e) {
  return j("instanceOf", { type: e });
}
function I(e) {
  return j("objectOf", { type: Object, validator(t2) {
    let n2 = "";
    const r2 = Object.keys(t2).every((r3) => (n2 = m(e, t2[r3], true), true === n2));
    return r2 || d(`objectOf - value validation error:
${$(n2)}`), r2;
  } });
}
function J(e) {
  const t2 = Object.keys(e), n2 = t2.filter((t3) => {
    var n3;
    return !(null === (n3 = e[t3]) || void 0 === n3 || !n3.required);
  }), r2 = j("shape", { type: Object, validator(r3) {
    if (!c(r3))
      return false;
    const i2 = Object.keys(r3);
    if (n2.length > 0 && n2.some((e2) => -1 === i2.indexOf(e2))) {
      const e2 = n2.filter((e3) => -1 === i2.indexOf(e3));
      return d(1 === e2.length ? `shape - required property "${e2[0]}" is not defined.` : `shape - required properties "${e2.join('", "')}" are not defined.`), false;
    }
    return i2.every((n3) => {
      if (-1 === t2.indexOf(n3))
        return true === this._vueTypes_isLoose || (d(`shape - shape definition does not include a "${n3}" property. Allowed keys: "${t2.join('", "')}".`), false);
      const i3 = m(e[n3], r3[n3], true);
      return "string" == typeof i3 && d(`shape - "${n3}" property validation error:
 ${$(i3)}`), true === i3;
    });
  } });
  return Object.defineProperty(r2, "_vueTypes_isLoose", { writable: true, value: false }), Object.defineProperty(r2, "loose", { get() {
    return this._vueTypes_isLoose = true, this;
  } }), r2;
}
var M = ["name", "validate", "getter"];
var R = ((e) => ((e = class {
  static get any() {
    return w();
  }
  static get func() {
    return x().def(this.defaults.func);
  }
  static get bool() {
    return void 0 === this.defaults.bool ? P() : P().def(this.defaults.bool);
  }
  static get string() {
    return A().def(this.defaults.string);
  }
  static get number() {
    return E().def(this.defaults.number);
  }
  static get array() {
    return S().def(this.defaults.array);
  }
  static get object() {
    return N().def(this.defaults.object);
  }
  static get integer() {
    return V().def(this.defaults.integer);
  }
  static get symbol() {
    return q();
  }
  static get nullable() {
    return k();
  }
  static extend(e2) {
    if (d("VueTypes.extend is deprecated. Use the ES6+ method instead. See https://dwightjack.github.io/vue-types/advanced/extending-vue-types.html#extending-namespaced-validators-in-es6 for details."), v(e2))
      return e2.forEach((e3) => this.extend(e3)), this;
    const { name: t2, validate: r2 = false, getter: i2 = false } = e2, o2 = n(e2, M);
    if (p(this, t2))
      throw new TypeError(`[VueTypes error]: Type "${t2}" already defined`);
    const { type: a2 } = o2;
    if (b(a2))
      return delete o2.type, Object.defineProperty(this, t2, i2 ? { get: () => T(t2, a2, o2) } : { value(...e3) {
        const n2 = T(t2, a2, o2);
        return n2.validator && (n2.validator = n2.validator.bind(n2, ...e3)), n2;
      } });
    let s2;
    return s2 = i2 ? { get() {
      const e3 = Object.assign({}, o2);
      return r2 ? _(t2, e3) : j(t2, e3);
    }, enumerable: true } : { value(...e3) {
      const n2 = Object.assign({}, o2);
      let i3;
      return i3 = r2 ? _(t2, n2) : j(t2, n2), n2.validator && (i3.validator = n2.validator.bind(i3, ...e3)), i3;
    }, enumerable: true }, Object.defineProperty(this, t2, s2);
  }
}).defaults = {}, e.sensibleDefaults = void 0, e.config = r, e.custom = D, e.oneOf = L, e.instanceOf = Y, e.oneOfType = B, e.arrayOf = F, e.objectOf = I, e.shape = J, e.utils = { validate: (e2, t2) => true === m(t2, e2, true), toType: (e2, t2, n2 = false) => n2 ? _(e2, t2) : j(e2, t2) }, e))();
function U(e = { func: () => {
}, bool: true, string: "", number: 0, array: () => [], object: () => ({}), integer: 0 }) {
  var n2;
  return (n2 = class extends R {
    static get sensibleDefaults() {
      return t({}, this.defaults);
    }
    static set sensibleDefaults(n3) {
      this.defaults = false !== n3 ? t({}, true !== n3 ? n3 : e) : {};
    }
  }).defaults = t({}, e), n2;
}
var z = class extends U() {
};
export {
  w as any,
  S as array,
  F as arrayOf,
  P as bool,
  r as config,
  U as createTypes,
  D as custom,
  z as default,
  T as fromType,
  x as func,
  Y as instanceOf,
  V as integer,
  k as nullable,
  E as number,
  N as object,
  I as objectOf,
  L as oneOf,
  B as oneOfType,
  J as shape,
  A as string,
  q as symbol,
  j as toType,
  _ as toValidableType,
  m as validateType
};
/*! Bundled license information:

is-plain-object/dist/is-plain-object.mjs:
  (*!
   * is-plain-object <https://github.com/jonschlinkert/is-plain-object>
   *
   * Copyright (c) 2014-2017, Jon Schlinkert.
   * Released under the MIT License.
   *)
*/
//# sourceMappingURL=vue-types.js.map
