import {
  ThemedImageThumb
} from "./chunk-RPFUHWA6.js";
import "./chunk-GFI6XVUE.js";
import "./chunk-ZRTRFIWU.js";
import "./chunk-6C4OBBQZ.js";
import {
  Html$1
} from "./chunk-M5OFQAQB.js";
import {
  Icon
} from "./chunk-YPPVVTGH.js";
import {
  __assign,
  __awaiter,
  __decorate,
  __extends,
  __generator,
  __metadata,
  __read,
  __rest
} from "./chunk-F25BIIHK.js";
import {
  CustomStyle,
  ENTERED,
  ENTERING,
  EXITED,
  EXITING,
  Renderer,
  ScopedContext,
  Transition_default,
  autobind,
  createObject,
  getPropValue,
  isArrayChildrenModified,
  isObject,
  resolveVariableAndFilter,
  setThemeClassName
} from "./chunk-LZQZ2OHM.js";
import "./chunk-QMS2VRR4.js";
import "./chunk-5QW7M2DY.js";
import "./chunk-ZJNJ4ZV3.js";
import "./chunk-KEABM62F.js";
import "./chunk-CNJUQDSN.js";
import "./chunk-YZFIV7S4.js";
import "./chunk-7XBFYOTW.js";
import {
  require_react
} from "./chunk-ZUWHLQVA.js";
import "./chunk-KNGT5NUO.js";
import "./chunk-X3JSFYRQ.js";
import {
  __toESM
} from "./chunk-GFT2G5UO.js";

// node_modules/amis/esm/renderers/Carousel.js
var import_react = __toESM(require_react());
var _a;
var animationStyles = (_a = {}, _a[ENTERING] = "in", _a[ENTERED] = "in", _a[EXITING] = "out", _a);
var defaultSchema = {
  component: function(props) {
    var _a2, _b;
    var data = props.data || {};
    var thumbMode = props.thumbMode;
    var cx = props.classnames;
    return import_react.default.createElement(import_react.default.Fragment, null, data.hasOwnProperty("image") ? import_react.default.createElement(ThemedImageThumb, { src: data.image, title: data.title, href: data.href, blank: data.blank, htmlTarget: data.htmlTarget, caption: data.description, thumbMode: (_b = (_a2 = data.thumbMode) !== null && _a2 !== void 0 ? _a2 : thumbMode) !== null && _b !== void 0 ? _b : "contain", imageMode: "original", className: cx("Carousel-image") }) : data.hasOwnProperty("html") ? import_react.default.createElement(Html$1, { html: data.html, filterHtml: props.env.filterHtml }) : data.hasOwnProperty("item") ? import_react.default.createElement("span", null, data.item) : import_react.default.createElement("p", null));
  }
};
var SCROLL_THRESHOLD = 20;
var Carousel = (
  /** @class */
  function(_super) {
    __extends(Carousel2, _super);
    function Carousel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.wrapperRef = import_react.default.createRef();
      _this.state = {
        current: 0,
        options: _this.props.options || getPropValue(_this.props) || [],
        nextAnimation: "",
        mouseStartLocation: null,
        isPaused: false
      };
      _this.loading = false;
      _this.marqueeRef = import_react.default.createRef();
      _this.contentRef = import_react.default.createRef();
      return _this;
    }
    Carousel2.prototype.componentDidMount = function() {
      this.prepareAutoSlide();
      if (this.props.animation === "marquee") {
        this.marquee();
      }
    };
    Carousel2.prototype.componentDidUpdate = function(prevProps) {
      var props = this.props;
      var nextOptions = props.options || getPropValue(props) || [];
      var prevOptions = prevProps.options || getPropValue(prevProps) || [];
      if (isArrayChildrenModified(prevOptions, nextOptions)) {
        this.setState({
          options: nextOptions
        });
      }
      if (this.props.animation === "marquee" && prevProps.animation !== "marquee") {
        this.marquee();
      }
    };
    Carousel2.prototype.componentWillUnmount = function() {
      this.clearAutoTimeout();
      cancelAnimationFrame(this.marqueeRequestId);
    };
    Carousel2.prototype.marquee = function() {
      var _this = this;
      if (!this.marqueeRef.current || !this.contentRef.current) {
        return;
      }
      var positionNum = 0;
      var lastTime = performance.now();
      var contentDom = this.contentRef.current;
      var animate = function(time) {
        var _a2, _b;
        var diffTime = time - lastTime;
        lastTime = time;
        var wrapWidth = (_b = (_a2 = _this.marqueeRef.current) === null || _a2 === void 0 ? void 0 : _a2.offsetWidth) !== null && _b !== void 0 ? _b : 0;
        if (!_this.state.isPaused) {
          var moveDistance = wrapWidth * (diffTime / _this.props.duration);
          positionNum += -moveDistance;
          var contentWidth = contentDom.scrollWidth / 2;
          if (Math.abs(positionNum) >= contentWidth) {
            positionNum = 0;
          }
          contentDom.style.transform = "translateX(".concat(positionNum, "px)");
        }
        _this.marqueeRequestId = requestAnimationFrame(animate);
      };
      this.marqueeRequestId = requestAnimationFrame(animate);
    };
    Carousel2.prototype.doAction = function(action, ctx, throwErrors, args) {
      var actionType = action === null || action === void 0 ? void 0 : action.actionType;
      if (!!~["next", "prev"].indexOf(actionType)) {
        this.autoSlide(actionType);
      } else if (actionType === "goto-image") {
        this.changeSlide((args === null || args === void 0 ? void 0 : args.activeIndex) - 1);
      }
    };
    Carousel2.prototype.prepareAutoSlide = function() {
      if (this.state.options.length < 2) {
        return;
      }
      this.clearAutoTimeout();
      if (this.props.auto) {
        var interval = this.props.interval;
        this.intervalTimeout = setTimeout(this.autoSlide, typeof interval === "string" ? resolveVariableAndFilter(interval, this.props.data) || 5e3 : interval);
      }
    };
    Carousel2.prototype.autoSlide = function(rel) {
      this.clearAutoTimeout();
      var animation = this.props.animation;
      var nextAnimation = this.state.nextAnimation;
      switch (rel) {
        case "prev":
          animation === "slide" ? nextAnimation = "slideRight" : nextAnimation = "";
          this.transitFramesTowards("right", nextAnimation);
          break;
        case "next":
        default:
          nextAnimation = "";
          this.transitFramesTowards("left", nextAnimation);
          break;
      }
      this.durationTimeout = setTimeout(this.prepareAutoSlide, this.props.duration);
    };
    Carousel2.prototype.transitFramesTowards = function(direction, nextAnimation) {
      return __awaiter(this, void 0, void 0, function() {
        var current, prevIndex, _a2, dispatchEvent, data, rendererEvent;
        return __generator(this, function(_b) {
          switch (_b.label) {
            case 0:
              current = this.state.current;
              prevIndex = current;
              if (this.props.loop === false && (current === 0 && direction === "right" || current === this.state.options.length - 1 && direction === "left")) {
                return [
                  2
                  /*return*/
                ];
              }
              switch (direction) {
                case "left":
                  current = this.getFrameId("next");
                  break;
                case "right":
                  current = this.getFrameId("prev");
                  break;
              }
              _a2 = this.props, dispatchEvent = _a2.dispatchEvent, data = _a2.data;
              return [4, dispatchEvent("change", createObject(data, {
                activeIndex: current + 1,
                prevIndex
              }))];
            case 1:
              rendererEvent = _b.sent();
              if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                return [
                  2
                  /*return*/
                ];
              }
              this.setState({
                current,
                nextAnimation
              });
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    Carousel2.prototype.getFrameId = function(pos) {
      var _a2 = this.state, options = _a2.options, current = _a2.current;
      var total = options.length;
      switch (pos) {
        case "prev":
          return (current - 1 + total) % total;
        case "next":
          return (current + 1) % total;
        default:
          return current;
      }
    };
    Carousel2.prototype.next = function() {
      var multiple = this.props.multiple;
      if (this.loading && multiple && multiple.count > 1) {
        return;
      }
      this.autoSlide("next");
    };
    Carousel2.prototype.prev = function() {
      var multiple = this.props.multiple;
      if (this.loading && multiple && multiple.count > 1) {
        return;
      }
      this.autoSlide("prev");
    };
    Carousel2.prototype.clearAutoTimeout = function() {
      clearTimeout(this.intervalTimeout);
      clearTimeout(this.durationTimeout);
    };
    Carousel2.prototype.changeSlide = function(index) {
      return __awaiter(this, void 0, void 0, function() {
        var current, _a2, dispatchEvent, data, multiple, rendererEvent;
        return __generator(this, function(_b) {
          switch (_b.label) {
            case 0:
              current = this.state.current;
              _a2 = this.props, dispatchEvent = _a2.dispatchEvent, data = _a2.data, multiple = _a2.multiple;
              if (this.loading && multiple && multiple.count > 1) {
                return [
                  2
                  /*return*/
                ];
              }
              return [4, dispatchEvent("change", createObject(data, {
                activeIndex: index,
                prevIndex: current
              }))];
            case 1:
              rendererEvent = _b.sent();
              if (rendererEvent === null || rendererEvent === void 0 ? void 0 : rendererEvent.prevented) {
                return [
                  2
                  /*return*/
                ];
              }
              this.setState({
                current: index
              });
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    Carousel2.prototype.renderDots = function() {
      var _this = this;
      var cx = this.props.classnames;
      var _a2 = this.state, current = _a2.current, options = _a2.options;
      return import_react.default.createElement("div", { className: cx("Carousel-dotsControl"), onMouseEnter: this.handleMouseEnter, onMouseLeave: this.handleMouseLeave }, Array.from({ length: options.length }).map(function(_, i) {
        return import_react.default.createElement("span", { key: i, onClick: function() {
          return _this.changeSlide(i);
        }, className: cx("Carousel-dot", current === i ? "is-active" : "") });
      }));
    };
    Carousel2.prototype.renderArrows = function() {
      var cx = this.props.classnames;
      return import_react.default.createElement(
        "div",
        { className: cx("Carousel-arrowsControl"), onMouseEnter: this.handleMouseEnter, onMouseLeave: this.handleMouseLeave },
        import_react.default.createElement(
          "div",
          { className: cx("Carousel-leftArrow"), onClick: this.prev },
          import_react.default.createElement(Icon, { icon: "left-arrow", className: "icon" })
        ),
        import_react.default.createElement(
          "div",
          { className: cx("Carousel-rightArrow"), onClick: this.next },
          import_react.default.createElement(Icon, { icon: "right-arrow", className: "icon" })
        )
      );
    };
    Carousel2.prototype.handleMouseEnter = function() {
      var multiple = this.props.multiple;
      if (multiple && multiple.count > 1) {
        return;
      }
      this.clearAutoTimeout();
    };
    Carousel2.prototype.handleMouseLeave = function() {
      var multiple = this.props.multiple;
      if (multiple && multiple.count > 1) {
        return;
      }
      this.prepareAutoSlide();
    };
    Carousel2.prototype.getNewOptions = function(options, count) {
      if (count === void 0) {
        count = 1;
      }
      var newOptions = options;
      if (Array.isArray(options) && options.length) {
        newOptions = new Array(options.length);
        for (var i = 0; i < options.length; i++) {
          newOptions[i] = new Array(count);
          for (var j = 0; j < count; j++) {
            newOptions[i][j] = options[(i + j) % options.length];
          }
        }
      }
      return newOptions;
    };
    Carousel2.prototype.getEventScreenXY = function(event) {
      var _a2, _b, _c, _d, _e, _f;
      var screenX, screenY;
      if (event.screenX !== void 0) {
        screenX = event.screenX;
        screenY = event.screenY;
      } else if ((_a2 = event.touches) === null || _a2 === void 0 ? void 0 : _a2.length) {
        screenX = (_b = event.touches[0]) === null || _b === void 0 ? void 0 : _b.screenX;
        screenY = (_c = event.touches[0]) === null || _c === void 0 ? void 0 : _c.screenY;
      } else if ((_d = event.changedTouches) === null || _d === void 0 ? void 0 : _d.length) {
        screenX = (_e = event.changedTouches[0]) === null || _e === void 0 ? void 0 : _e.screenX;
        screenY = (_f = event.changedTouches[0]) === null || _f === void 0 ? void 0 : _f.screenY;
      }
      return {
        screenX,
        screenY
      };
    };
    Carousel2.prototype.addMouseDownListener = function(event) {
      var direction = this.props.direction;
      var _a2 = this.getEventScreenXY(event), screenX = _a2.screenX, screenY = _a2.screenY;
      var location = direction === "vertical" ? screenY : screenX;
      location !== void 0 && this.setState({
        mouseStartLocation: location
      });
    };
    Carousel2.prototype.addMouseUpListener = function(event) {
      var _a2 = this.getEventScreenXY(event), screenX = _a2.screenX, screenY = _a2.screenY;
      var direction = this.props.direction;
      var location = direction === "vertical" ? screenY : screenX;
      if (this.state.mouseStartLocation !== null && location !== void 0) {
        if (location - this.state.mouseStartLocation > SCROLL_THRESHOLD) {
          this.autoSlide("prev");
        } else if (this.state.mouseStartLocation - location > SCROLL_THRESHOLD) {
          this.autoSlide();
        }
        this.setState({
          mouseStartLocation: null
        });
      }
    };
    Carousel2.prototype.render = function() {
      var _a2, _b;
      var _this = this;
      var _c = this.props, render = _c.render, className = _c.className, style = _c.style, cx = _c.classnames, itemSchema = _c.itemSchema, animation = _c.animation, width = _c.width, height = _c.height, controls = _c.controls, controlsTheme = _c.controlsTheme, placeholder = _c.placeholder, data = _c.data, name = _c.name, duration = _c.duration, multiple = _c.multiple, alwaysShowArrow = _c.alwaysShowArrow, icons = _c.icons, id = _c.id, wrapperCustomStyle = _c.wrapperCustomStyle, env = _c.env, themeCss = _c.themeCss;
      var _d = this.state, options = _d.options, current = _d.current, nextAnimation = _d.nextAnimation;
      var body = null;
      var carouselStyles = style ? __assign({}, style) : {};
      if (width) {
        !isNaN(Number(width)) ? carouselStyles.width = width + "px" : carouselStyles.width = width;
      }
      if (height) {
        !isNaN(Number(height)) ? carouselStyles.height = height + "px" : carouselStyles.height = height;
      }
      var _e = __read([
        controls.indexOf("dots") > -1 && animation !== "marquee",
        controls.indexOf("arrows") > -1 && animation !== "marquee"
      ], 2), dots = _e[0], arrows = _e[1];
      var animationName = nextAnimation || animation;
      if (Array.isArray(options) && options.length) {
        var multipleCount_1 = 1;
        if (multiple && typeof multiple.count === "number" && multiple.count >= 2) {
          multipleCount_1 = Math.floor(multiple.count) < options.length ? Math.floor(multiple.count) : options.length;
        }
        var newOptions_1 = this.getNewOptions(options, multipleCount_1);
        var transitionDuration = multipleCount_1 > 1 && typeof duration === "number" ? "".concat(duration, "ms") : duration || "500ms";
        var timeout_1 = multipleCount_1 > 1 && typeof duration === "number" ? duration : 500;
        var transformStyles = (_a2 = {}, _a2[ENTERING] = 0, _a2[ENTERED] = 0, _a2[EXITING] = animationName === "slideRight" ? 100 / multipleCount_1 : -100 / multipleCount_1, _a2[EXITED] = animationName === "slideRight" ? -100 / multipleCount_1 : 100 / multipleCount_1, _a2);
        var itemStyle_1 = multipleCount_1 > 1 ? __assign({ transitionTimingFunction: "linear", transitionDuration }, animation === "slide" ? {
          transform: "translateX(".concat(transformStyles[status], "%)")
        } : {}) : {};
        var itemRender_1 = function(option) {
          var _a3;
          var optionItemSchema = option.itemSchema, restOption = __rest(option, ["itemSchema"]);
          return render("".concat(current, "/body"), optionItemSchema || itemSchema ? optionItemSchema || itemSchema : defaultSchema, {
            thumbMode: _this.props.thumbMode,
            data: createObject(data, isObject(option) ? restOption : (_a3 = { item: option }, _a3[name] = option, _a3))
          });
        };
        body = animation === "marquee" ? import_react.default.createElement(
          "div",
          { ref: this.marqueeRef, className: cx("Marquee-container"), onMouseEnter: function() {
            return _this.setState({
              isPaused: true
            });
          }, onMouseLeave: function() {
            return _this.setState({
              isPaused: false
            });
          }, style: {
            width: "100%",
            height
          } },
          import_react.default.createElement("div", { className: cx("Marquee-content"), ref: this.contentRef }, options.concat(options).map(function(option, key) {
            return import_react.default.createElement(
              "div",
              { key, className: cx("Marquee-item") },
              multipleCount_1 === 1 ? itemRender_1(option) : null,
              multipleCount_1 > 1 ? newOptions_1.concat(newOptions_1)[key].map(function(option2, index) {
                return import_react.default.createElement("div", { key: index, style: {
                  width: 100 / multipleCount_1 + "%",
                  height: "100%",
                  float: "left"
                } }, itemRender_1(option2));
              }) : null
            );
          }))
        ) : import_react.default.createElement("div", { ref: this.wrapperRef, className: cx("Carousel-container"), onMouseEnter: this.handleMouseEnter, onMouseLeave: this.handleMouseLeave }, options.map(function(option, key) {
          return import_react.default.createElement(Transition_default, { mountOnEnter: true, unmountOnExit: true, in: key === current, timeout: timeout_1, key }, function(status2) {
            if (status2 === ENTERING) {
              _this.wrapperRef.current && _this.wrapperRef.current.childNodes.forEach(function(item) {
                return item.offsetHeight;
              });
            }
            if (multipleCount_1 > 1) {
              if ((status2 === ENTERING || status2 === EXITING) && !_this.loading) {
                _this.loading = true;
              } else if ((status2 === ENTERED || status2 === EXITED) && _this.loading) {
                _this.loading = false;
              }
            }
            return import_react.default.createElement(
              "div",
              { className: cx("Carousel-item", animationName, animationStyles[status2]), style: itemStyle_1 },
              multipleCount_1 === 1 ? itemRender_1(option) : null,
              multipleCount_1 > 1 ? newOptions_1[key].map(function(option2, index) {
                return import_react.default.createElement("div", { key: index, style: {
                  width: 100 / multipleCount_1 + "%",
                  height: "100%",
                  float: "left"
                } }, itemRender_1(option2));
              }) : null
            );
          });
        }));
      }
      return import_react.default.createElement(
        "div",
        { className: cx("Carousel Carousel--".concat(controlsTheme), (_b = {}, _b["Carousel-arrow--always"] = !!alwaysShowArrow, _b), className, setThemeClassName(__assign(__assign({}, this.props), { name: "baseControlClassName", id, themeCss })), setThemeClassName(__assign(__assign({}, this.props), { name: "wrapperCustomStyle", id, themeCss: wrapperCustomStyle })), { "Carousel-vertical": this.props.direction === "vertical" }), onMouseDown: this.props.mouseEvent ? this.addMouseDownListener : void 0, onMouseUp: this.props.mouseEvent ? this.addMouseUpListener : void 0, onMouseLeave: this.props.mouseEvent ? this.addMouseUpListener : void 0, onTouchStart: this.props.mouseEvent ? this.addMouseDownListener : void 0, onTouchEnd: this.props.mouseEvent ? this.addMouseUpListener : void 0, style: carouselStyles },
        body ? body : placeholder,
        dots ? this.renderDots() : null,
        arrows ? import_react.default.createElement("div", { className: cx("Carousel-leftArrow", setThemeClassName(__assign(__assign({}, this.props), { name: "galleryControlClassName", id, themeCss }))), onClick: this.prev }, icons && icons.prev ? import_react.default.isValidElement(icons.prev) ? icons.prev : render("arrow-prev", icons.prev) : import_react.default.createElement(Icon, { icon: "left-arrow", className: "icon", iconContent: "ImageGallery-prevBtn" })) : null,
        arrows ? import_react.default.createElement("div", { className: cx("Carousel-rightArrow", setThemeClassName(__assign(__assign({}, this.props), { name: "galleryControlClassName", id, themeCss }))), onClick: this.next }, icons && icons.next ? import_react.default.isValidElement(icons.next) ? icons.next : render("arrow-next", icons.next) : import_react.default.createElement(Icon, { icon: "right-arrow", className: "icon", iconContent: "ImageGallery-nextBtn" })) : null,
        import_react.default.createElement(CustomStyle, __assign({}, this.props, { config: {
          wrapperCustomStyle,
          id,
          themeCss,
          classNames: [
            {
              key: "baseControlClassName"
            },
            {
              key: "galleryControlClassName",
              weights: {
                default: {
                  suf: " svg",
                  important: true
                }
              }
            }
          ]
        }, env }))
      );
    };
    Carousel2.defaultProps = {
      auto: true,
      interval: 5e3,
      duration: 500,
      controlsTheme: "light",
      animation: "fade",
      controls: ["dots", "arrows"],
      placeholder: "-",
      multiple: { count: 1 },
      alwaysShowArrow: false
    };
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", void 0)
    ], Carousel2.prototype, "prepareAutoSlide", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [String]),
      __metadata("design:returntype", void 0)
    ], Carousel2.prototype, "autoSlide", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [String, String]),
      __metadata("design:returntype", Promise)
    ], Carousel2.prototype, "transitFramesTowards", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [String]),
      __metadata("design:returntype", void 0)
    ], Carousel2.prototype, "getFrameId", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", void 0)
    ], Carousel2.prototype, "next", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", void 0)
    ], Carousel2.prototype, "prev", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", void 0)
    ], Carousel2.prototype, "clearAutoTimeout", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Number]),
      __metadata("design:returntype", Promise)
    ], Carousel2.prototype, "changeSlide", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", void 0)
    ], Carousel2.prototype, "handleMouseEnter", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", []),
      __metadata("design:returntype", void 0)
    ], Carousel2.prototype, "handleMouseLeave", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], Carousel2.prototype, "addMouseDownListener", null);
    __decorate([
      autobind,
      __metadata("design:type", Function),
      __metadata("design:paramtypes", [Object]),
      __metadata("design:returntype", void 0)
    ], Carousel2.prototype, "addMouseUpListener", null);
    return Carousel2;
  }(import_react.default.Component)
);
var CarouselRenderer = (
  /** @class */
  function(_super) {
    __extends(CarouselRenderer2, _super);
    function CarouselRenderer2(props, context) {
      var _this = _super.call(this, props) || this;
      var scoped = context;
      scoped.registerComponent(_this);
      return _this;
    }
    CarouselRenderer2.prototype.componentWillUnmount = function() {
      var _a2;
      (_a2 = _super.prototype.componentWillUnmount) === null || _a2 === void 0 ? void 0 : _a2.call(this);
      var scoped = this.context;
      scoped.unRegisterComponent(this);
    };
    CarouselRenderer2.contextType = ScopedContext;
    CarouselRenderer2 = __decorate([
      Renderer({
        type: "carousel"
      }),
      __metadata("design:paramtypes", [Object, Object])
    ], CarouselRenderer2);
    return CarouselRenderer2;
  }(Carousel)
);
export {
  Carousel,
  CarouselRenderer
};
//# sourceMappingURL=Carousel-47HCU33G.js.map
