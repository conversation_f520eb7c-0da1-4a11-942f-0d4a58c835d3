.vxe-grid {
  position: relative;
  overflow: auto;
  display: flex;
  flex-direction: column;
}
.vxe-grid.is--loading:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  background-color: var(--vxe-ui-loading-background-color);
}
.vxe-grid.is--loading > .vxe-table .vxe-loading {
  background-color: transparent;
}
.vxe-grid.is--maximize {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 0.5em 1em;
  background-color: var(--vxe-ui-layout-background-color);
}
.vxe-grid .vxe-grid--form-wrapper,
.vxe-grid .vxe-grid--top-wrapper,
.vxe-grid .vxe-grid--bottom-wrapper {
  position: relative;
}
.vxe-grid .vxe-grid--table-container {
  display: flex;
  flex-direction: row;
}
.vxe-grid .vxe-grid--left-wrapper,
.vxe-grid .vxe-grid--right-wrapper {
  flex-shrink: 0;
  overflow: auto;
  outline: 0;
}
.vxe-grid .vxe-grid--table-wrapper {
  flex-grow: 1;
  overflow: hidden;
}

.vxe-grid--layout-body-wrapper {
  display: flex;
  flex-direction: row;
  overflow: auto;
  flex-grow: 1;
}

.vxe-grid--layout-body-content-wrapper {
  flex-grow: 1;
  overflow: auto;
}

.vxe-grid--layout-header-wrapper,
.vxe-grid--layout-footer-wrapper,
.vxe-grid--layout-aside-left-wrapper,
.vxe-grid--layout-aside-left-wrapper {
  flex-shrink: 0;
  overflow: auto;
}

.vxe-grid {
  font-size: var(--vxe-ui-font-size-default);
}
.vxe-grid.size--medium {
  font-size: var(--vxe-ui-font-size-medium);
}
.vxe-grid.size--small {
  font-size: var(--vxe-ui-font-size-small);
}
.vxe-grid.size--mini {
  font-size: var(--vxe-ui-font-size-mini);
}