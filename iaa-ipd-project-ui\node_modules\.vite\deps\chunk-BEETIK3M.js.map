{"version": 3, "sources": ["../../lodash/assign.js"], "sourcesContent": ["var assignValue = require('./_assignValue'),\n    copyObject = require('./_copyObject'),\n    createAssigner = require('./_createAssigner'),\n    isArrayLike = require('./isArrayLike'),\n    isPrototype = require('./_isPrototype'),\n    keys = require('./keys');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Assigns own enumerable string keyed properties of source objects to the\n * destination object. Source objects are applied from left to right.\n * Subsequent sources overwrite property assignments of previous sources.\n *\n * **Note:** This method mutates `object` and is loosely based on\n * [`Object.assign`](https://mdn.io/Object/assign).\n *\n * @static\n * @memberOf _\n * @since 0.10.0\n * @category Object\n * @param {Object} object The destination object.\n * @param {...Object} [sources] The source objects.\n * @returns {Object} Returns `object`.\n * @see _.assignIn\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n * }\n *\n * function Bar() {\n *   this.c = 3;\n * }\n *\n * Foo.prototype.b = 2;\n * Bar.prototype.d = 4;\n *\n * _.assign({ 'a': 0 }, new Foo, new Bar);\n * // => { 'a': 1, 'c': 3 }\n */\nvar assign = createAssigner(function(object, source) {\n  if (isPrototype(source) || isArrayLike(source)) {\n    copyObject(source, keys(source), object);\n    return;\n  }\n  for (var key in source) {\n    if (hasOwnProperty.call(source, key)) {\n      assignValue(object, key, source[key]);\n    }\n  }\n});\n\nmodule.exports = assign;\n"], "mappings": ";;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,cAAc;AAAlB,QACI,aAAa;AADjB,QAEI,iBAAiB;AAFrB,QAGI,cAAc;AAHlB,QAII,cAAc;AAJlB,QAKI,OAAO;AAGX,QAAI,cAAc,OAAO;AAGzB,QAAI,iBAAiB,YAAY;AAkCjC,QAAI,SAAS,eAAe,SAAS,QAAQ,QAAQ;AACnD,UAAI,YAAY,MAAM,KAAK,YAAY,MAAM,GAAG;AAC9C,mBAAW,QAAQ,KAAK,MAAM,GAAG,MAAM;AACvC;AAAA,MACF;AACA,eAAS,OAAO,QAAQ;AACtB,YAAI,eAAe,KAAK,QAAQ,GAAG,GAAG;AACpC,sBAAY,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,QACtC;AAAA,MACF;AAAA,IACF,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;", "names": []}