Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var _vue=require("vue"),_comp=require("../../ui/src/comp"),_column=require("./column"),_util=require("./util"),_cell=_interopRequireDefault(require("./cell"));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}var _default=exports.default=(0,_comp.defineVxeComponent)({name:"VxeColgroup",props:_column.columnProps,setup(e,{slots:u}){let l=(0,_vue.ref)(),r=(0,_vue.inject)("$xeTable",null),o=(0,_vue.inject)("$xeColgroup",null);if(!r)return()=>(0,_vue.createCommentVNode)();let t=_cell.default.createColumn(r,e);var n={},n=(u.header&&(n.header=u.header),t.slots=n,t.children=[],(0,_util.watchColumn)(r,e,t),(0,_vue.onMounted)(()=>{var e=l.value;e&&(0,_util.assembleColumn)(r,e,t,o)}),(0,_vue.onUnmounted)(()=>{(0,_util.destroyColumn)(r,t)}),{columnConfig:t});return(0,_vue.provide)("$xeColgroup",n),(0,_vue.provide)("$xeGrid",null),()=>(0,_vue.h)("div",{ref:l},u.default?u.default():[])}});