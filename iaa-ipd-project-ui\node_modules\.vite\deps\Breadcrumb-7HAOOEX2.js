import {
  Breadcrumb$1
} from "./chunk-GFI6XVUE.js";
import "./chunk-ZRTRFIWU.js";
import "./chunk-6C4OBBQZ.js";
import "./chunk-M5OFQAQB.js";
import "./chunk-YPPVVTGH.js";
import {
  __assign,
  __decorate,
  __extends,
  __rest
} from "./chunk-F25BIIHK.js";
import {
  Renderer,
  filter,
  resolveVariableAndFilter
} from "./chunk-LZQZ2OHM.js";
import "./chunk-QMS2VRR4.js";
import "./chunk-5QW7M2DY.js";
import "./chunk-ZJNJ4ZV3.js";
import "./chunk-KEABM62F.js";
import "./chunk-CNJUQDSN.js";
import "./chunk-YZFIV7S4.js";
import "./chunk-7XBFYOTW.js";
import {
  require_react
} from "./chunk-ZUWHLQVA.js";
import "./chunk-KNGT5NUO.js";
import "./chunk-X3JSFYRQ.js";
import {
  __toESM
} from "./chunk-GFT2G5UO.js";

// node_modules/amis/esm/renderers/Breadcrumb.js
var import_react = __toESM(require_react());
var BreadcrumbField = (
  /** @class */
  function(_super) {
    __extends(BreadcrumbField2, _super);
    function BreadcrumbField2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    BreadcrumbField2.prototype.render = function() {
      var _a = this.props, items = _a.items, source = _a.source, data = _a.data, env = _a.env, restProps = __rest(_a, ["items", "source", "data", "env"]);
      var crumbItems = items ? items : resolveVariableAndFilter(source, data, "| raw");
      if (crumbItems) {
        crumbItems = crumbItems.map(function(item) {
          if (item.label) {
            item.label = filter(item.label, data);
          }
          if (item.href) {
            item.href = resolveVariableAndFilter(item.href, data, "| raw");
          }
          if (item.dropdown) {
            item.dropdown = item.dropdown.map(function(dropdownItem) {
              if (dropdownItem.label) {
                dropdownItem.label = filter(dropdownItem.label, data);
              }
              if (dropdownItem.href) {
                dropdownItem.href = resolveVariableAndFilter(dropdownItem.href, data, "| raw");
              }
              return dropdownItem;
            });
          }
          return item;
        });
      }
      return import_react.default.createElement(Breadcrumb$1, __assign({ items: crumbItems, tooltipContainer: env === null || env === void 0 ? void 0 : env.getModalContainer }, restProps));
    };
    return BreadcrumbField2;
  }(import_react.default.Component)
);
var BreadcrumbFieldRenderer = (
  /** @class */
  function(_super) {
    __extends(BreadcrumbFieldRenderer2, _super);
    function BreadcrumbFieldRenderer2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    BreadcrumbFieldRenderer2 = __decorate([
      Renderer({
        type: "breadcrumb"
      })
    ], BreadcrumbFieldRenderer2);
    return BreadcrumbFieldRenderer2;
  }(BreadcrumbField)
);
export {
  BreadcrumbField,
  BreadcrumbFieldRenderer
};
//# sourceMappingURL=Breadcrumb-7HAOOEX2.js.map
