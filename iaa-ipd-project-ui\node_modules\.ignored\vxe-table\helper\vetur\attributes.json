{"vxe-table/id": {"type": "string", "description": "唯一标识（被某些特定的功能所依赖）"}, "vxe-table/data": {"type": "any[]", "description": "表格数据（与 loadData 行为一致，更新数据是不会重置状态）"}, "vxe-table/height": {"type": "number | string", "description": "表格的高度；支持铺满父容器或者固定高度，如果设置 auto 为铺满父容器（如果设置为 auto，则必须确保存在父节点且不允许存在相邻元素）"}, "vxe-table/min-height": {"type": "number | string", "description": "表格最小高度"}, "vxe-table/max-height": {"type": "number | string", "description": "表格的最大高度"}, "vxe-table/auto-resize": {"type": "boolean", "description": "自动监听父元素的变化去重新计算表格（对于父元素可能存在动态变化、显示隐藏的容器中、列宽异常等场景中的可能会用到）"}, "vxe-table/sync-resize": {"type": "boolean | string | number", "description": "自动跟随某个属性的变化去重新计算表格，和手动调用 recalculate 方法是一样的效果（对于通过某个属性来控制显示/隐藏切换时可能会用到）"}, "vxe-table/resizable": {"type": "boolean", "description": "已废弃，被 column-config.resizable 替换"}, "vxe-table/stripe": {"type": "boolean", "description": "是否带有斑马纹（需要注意的是，在可编辑表格场景下，临时插入的数据不会有斑马纹样式）"}, "vxe-table/border": {"type": "boolean | string", "description": "是否带有边框"}, "vxe-table/padding": {"type": "boolean"}, "vxe-table/round": {"type": "boolean", "description": "是否为圆角边框"}, "vxe-table/size": {"type": "string", "description": "表格的尺寸"}, "vxe-table/loading": {"type": "boolean", "description": "表格是否显示加载中"}, "vxe-table/align": {"type": "string", "description": "所有的列对齐方式"}, "vxe-table/header-align": {"type": "string", "description": "所有的表头列的对齐方式"}, "vxe-table/footer-align": {"type": "string", "description": "所有的表尾列的对齐方式"}, "vxe-table/show-header": {"type": "boolean", "description": "是否显示表头"}, "vxe-table/highlight-current-row": {"type": "boolean", "description": "已废弃，被 row-config.isCurrent 替换"}, "vxe-table/highlight-hover-row": {"type": "boolean", "description": "已废弃，被 row-config.isHover 替换"}, "vxe-table/highlight-current-column": {"type": "boolean", "description": "已废弃，被 column-config.isCurrent 替换"}, "vxe-table/highlight-hover-column": {"type": "boolean", "description": "已废弃，被 column-config.isHover 替换"}, "vxe-table/row-class-name": {"type": "string | (({ row, rowindex, $rowindex }) => any)", "description": "给行附加 className"}, "vxe-table/cell-class-name": {"type": "string | (({ row, rowindex, $rowindex, column, columnindex, $columnindex }) => any)", "description": "给单元格附加 className"}, "vxe-table/header-row-class-name": {"type": "string | (({ $rowindex }) => any)", "description": "给表头的行附加 className"}, "vxe-table/header-cell-class-name": {"type": "string | (({ $rowindex, column, columnindex, $columnindex }) => any)", "description": "给表头的单元格附加 className"}, "vxe-table/footer-row-class-name": {"type": "string | (({ $rowindex }) => any)", "description": "给表尾的行附加 className"}, "vxe-table/footer-cell-class-name": {"type": "string | (({ $rowindex, column, columnindex, $columnindex }) => any)", "description": "给表尾的单元格附加 className"}, "vxe-table/cell-style": {"type": "any | (({ row, rowindex, $rowindex, column, columnindex, $columnindex }) => any)", "description": "给单元格附加样式"}, "vxe-table/header-cell-style": {"type": "any | (({ $rowindex, column, columnindex, $columnindex }) => any)", "description": "给表头单元格附加样式"}, "vxe-table/footer-cell-style": {"type": "any | (({ $rowindex, column, columnindex, $columnindex }) => any)", "description": "给表尾单元格附加样式"}, "vxe-table/row-style": {"type": "any | (({ row, rowindex, $rowindex }) => any)", "description": "给行附加样式，也可以是函数"}, "vxe-table/header-row-style": {"type": "any | (({ $rowindex, column, columnindex, $columnindex }) => any)", "description": "给表头行附加样式"}, "vxe-table/footer-row-style": {"type": "any | (({ $rowindex }) => any)", "description": "给表尾行附加样式"}, "vxe-table/show-footer": {"type": "boolean", "description": "是否显示表尾"}, "vxe-table/footer-data": {"type": "any[]", "description": "表尾数据"}, "vxe-table/footer-method": {"type": "({ columns, data }) => any[][]", "description": "表尾的数据获取方法，返回一个二维数组"}, "vxe-table/merge-cells": {"type": "array<{ row: number, col: number, rowspan: number, colspan: number }>", "description": "临时合并指定的单元格 (不能用于展开行，不建议用于固定列、树形结构)"}, "vxe-table/merge-footer-items": {"type": "array<{ row: number, col: number, rowspan: number, colspan: number }>", "description": "临时合并表尾 (不能用于展开行，不建议用于固定列、树形结构)"}, "vxe-table/span-method": {"type": "({ row, rowindex, $rowindex, _rowindex, column, columnindex, $columnindex, _columnindex, data }) => { rowspan: number, colspan: number}", "description": "自定义合并函数，返回计算后的值 (不能用于虚拟滚动、展开行，不建议用于固定列、树形结构)"}, "vxe-table/footer-span-method": {"type": "({ $rowindex, column, columnindex, $columnindex, _columnindex, data }) => { rowspan: number, colspan: number}", "description": "表尾合并行或列，返回计算后的值 (不能用于虚拟滚动、展开行，不建议用于固定列、树形结构)"}, "vxe-table/show-overflow": {"type": "boolean | string", "description": "设置所有内容过长时显示为省略号（如果是固定列建议设置该值，提升渲染速度）"}, "vxe-table/show-header-overflow": {"type": "boolean | string", "description": "设置表头所有内容过长时显示为省略号"}, "vxe-table/show-footer-overflow": {"type": "boolean | string", "description": "设置表尾所有内容过长时显示为省略号"}, "vxe-table/column-key": {"type": "boolean", "description": "已废弃，被 column-config.useKey 替换"}, "vxe-table/row-key": {"type": "boolean", "description": "已废弃，被 row-config.useKey 替换"}, "vxe-table/row-id": {"type": "string", "description": "已废弃，被 row-config.keyField 替换"}, "vxe-table/keep-source": {"type": "boolean", "description": "保持原始值的状态，被某些功能所依赖，比如编辑状态、还原数据等（开启后影响性能，具体取决于数据量）"}, "vxe-table/column-config": {"type": "any", "description": "列配置信息"}, "vxe-table/current-column-config": {"type": "any", "description": "当前列配置信息"}, "vxe-table/cell-config": {"type": "any"}, "vxe-table/header-cell-config": {"type": "any"}, "vxe-table/footer-cell-config": {"type": "any"}, "vxe-table/row-config": {"type": "any", "description": "行配置信息"}, "vxe-table/current-row-config": {"type": "any", "description": "当前行配置信息"}, "vxe-table/row-group-config": {"type": "any", "description": "当前行配置信息"}, "vxe-table/resize-config": {"type": "object", "description": "响应式布局配置项"}, "vxe-table/resizable-config": {"type": "object", "description": "列宽拖动配置项"}, "vxe-table/seq-config": {"type": "any", "description": "序号配置项"}, "vxe-table/sort-config": {"type": "any", "description": "排序配置项"}, "vxe-table/drag-config": {"type": "any", "description": "拖拽配置信息"}, "vxe-table/row-drag-config": {"type": "any", "description": "行拖拽配置信息"}, "vxe-table/column-drag-config": {"type": "any", "description": "拖拽配置信息"}, "vxe-table/filter-config": {"type": "any", "description": "筛选配置项"}, "vxe-table/export-config": {"type": "any", "description": "导出配置项"}, "vxe-table/import-config": {"type": "any", "description": "导入配置项"}, "vxe-table/print-config": {"type": "any", "description": "打印配置项"}, "vxe-table/radio-config": {"type": "any", "description": "单选框配置项"}, "vxe-table/checkbox-config": {"type": "any", "description": "复选框配置项"}, "vxe-table/tooltip-config": {"type": "any", "description": "tooltip 配置项"}, "vxe-table/expand-config": {"type": "any", "description": "展开行配置项（不支持虚拟滚动）"}, "vxe-table/tree-config": {"type": "any", "description": "树形结构配置项"}, "vxe-table/menu-config": {"type": "any", "description": "右键菜单配置项"}, "vxe-table/clip-config": {"type": "any", "description": "复制/粘贴配置项"}, "vxe-table/fnr-config": {"type": "any", "description": "查找/替换配置项"}, "vxe-table/mouse-config": {"type": "any", "description": "鼠标配置项"}, "vxe-table/area-config": {"type": "any", "description": "区域选取配置项"}, "vxe-table/keyboard-config": {"type": "any", "description": "按键配置项"}, "vxe-table/edit-config": {"type": "any", "description": "可编辑配置项"}, "vxe-table/valid-config": {"type": "any", "description": "校验配置项"}, "vxe-table/edit-rules": {"type": "{ [field: string]: vxetabledefines.validatorrule[] }", "description": "校验规则配置项"}, "vxe-table/empty-text": {"type": "string", "description": "空数据时显示的内容"}, "vxe-table/empty-render": {"type": "any", "description": "空内容渲染配置项，empty-render 的优先级大于 empty-text"}, "vxe-table/loading-config": {"type": "any", "description": "加载中配置项"}, "vxe-table/custom-config": {"type": "any", "description": "个性化信息配置项"}, "vxe-table/scroll-x": {"type": "any", "description": "横向滚动配置"}, "vxe-table/scroll-y": {"type": "any", "description": "纵向滚动配置"}, "vxe-table/virtual-x-config": {"type": "any", "description": "横向滚动配置"}, "vxe-table/virtual-y-config": {"type": "any", "description": "纵向滚动配置"}, "vxe-table/scrollbar-config": {"type": "any", "description": "滚动条配置"}, "vxe-table/params": {"type": "any", "description": "自定义参数（可以用来存放一些自定义的数据）"}, "vxe-colgroup/field": {"type": "string", "description": "列字段名（注：属性层级越深，渲染性能就越差，例如：aa.bb.cc.dd.ee）"}, "vxe-colgroup/title": {"type": "string", "description": "列标题（支持开启国际化）"}, "vxe-colgroup/width": {"type": "number | string", "description": "列宽度（如果为空则均匀分配剩余宽度，如果全部列固定了，可能会存在宽屏下不会铺满，可以配合 \"%\" 或者 \"min-width\" 布局）"}, "vxe-colgroup/min-width": {"type": "number | string", "description": "最小列宽度；会自动将剩余空间按比例分配"}, "vxe-colgroup/resizable": {"type": "boolean", "description": "列是否允许拖动列宽调整大小"}, "vxe-colgroup/visible": {"type": "boolean", "description": "默认是否显示"}, "vxe-colgroup/fixed": {"type": "string", "description": "将列固定在左侧或者右侧（注意：固定列应该放在左右两侧的位置）"}, "vxe-colgroup/align": {"type": "string", "description": "列对齐方式"}, "vxe-colgroup/header-align": {"type": "string", "description": "表头列的对齐方式"}, "vxe-colgroup/show-overflow": {"type": "string | boolean", "description": "当内容过长时显示为省略号"}, "vxe-colgroup/show-header-overflow": {"type": "string | boolean", "description": "当表头内容过长时显示为省略号"}, "vxe-colgroup/header-class-name": {"type": "string | (({ $rowindex, column, columnindex, $columnindex }) => any)", "description": "给表头的单元格附加 className"}, "vxe-column/type": {"type": "string", "description": "列的类型（部分功能需要设置 column-config.useKey | row-config.useKey）"}, "vxe-column/field": {"type": "string", "description": "列字段名（注：属性层级越深，渲染性能就越差，例如：aa.bb.cc.dd.ee）"}, "vxe-column/title": {"type": "string", "description": "列标题（支持开启国际化）"}, "vxe-column/width": {"type": "number | string", "description": "列宽度（如果为空则均匀分配剩余宽度，如果全部列固定了，可能会存在宽屏下不会铺满，可以配合 \"%\" 或者 \"min-width\" 布局）"}, "vxe-column/min-width": {"type": "number | string", "description": "最小列宽度；会自动将剩余空间按比例分配"}, "vxe-column/resizable": {"type": "boolean", "description": "列是否允许拖动列宽调整大小"}, "vxe-column/visible": {"type": "boolean", "description": "默认是否显示"}, "vxe-column/fixed": {"type": "string", "description": "将列固定在左侧或者右侧（注意：固定列应该放在左右两侧的位置）"}, "vxe-column/align": {"type": "string", "description": "列对齐方式"}, "vxe-column/header-align": {"type": "string", "description": "表头列的对齐方式"}, "vxe-column/footer-align": {"type": "string", "description": "表尾列的对齐方式"}, "vxe-column/show-overflow": {"type": "string | boolean", "description": "当内容过长时显示为省略号"}, "vxe-column/show-header-overflow": {"type": "string | boolean", "description": "当表头内容过长时显示为省略号"}, "vxe-column/show-footer-overflow": {"type": "boolean | string", "description": "当表尾内容过长时显示为省略号"}, "vxe-column/class-name": {"type": "string | (({row, rowindex, $rowindex, column, columnindex, $columnindex}) => any)", "description": "给单元格附加 className"}, "vxe-column/header-class-name": {"type": "string | (({ $rowindex, column, columnindex, $columnindex }) => any)", "description": "给表头的单元格附加 className"}, "vxe-column/footer-class-name": {"type": "string | (({ $rowindex, column, columnindex, $columnindex }) => any)", "description": "给表尾的单元格附加 className"}, "vxe-column/padding": {"type": "boolean"}, "vxe-column/vertical-align": {"type": "string"}, "vxe-column/formatter": {"type": "(({ cellvalue, row, column }) => string) | any[] | string", "description": "格式化显示内容"}, "vxe-column/sortable": {"type": "boolean", "description": "数据排序，是否允许列排序"}, "vxe-column/sort-by": {"type": "string | (({ row, column }) => string | number)", "description": "数据排序，只对 sortable 有效，指定排序的字段（当值 formatter 格式化后，可以设置该字段，使用值进行排序）"}, "vxe-column/sort-type": {"type": "string", "description": "数据排序，排序的字段类型，比如字符串转数值等"}, "vxe-column/filters": {"type": "any[]", "description": "数据筛选，配置筛选条件（注：筛选只能用于列表，如果是树结构则过滤根节点）"}, "vxe-column/filter-multiple": {"type": "boolean", "description": "数据筛选，只对 filters 有效，筛选是否允许多选"}, "vxe-column/filter-method": {"type": "({ value, option, cellvalue, row, column }) => boolean", "description": "数据筛选，只对 filters 有效，列的筛选方法，该方法的返回值用来决定该行是否显示"}, "vxe-column/filter-reset-method": {"type": "({ options, column }) => void", "description": "数据筛选，只对 filters 有效，自定义筛选重置方法"}, "vxe-column/filter-recover-method": {"type": "({ option, column }) => void", "description": "数据筛选，只对 filters 有效，自定义筛选复原方法（使用自定义筛选时可能会用到）"}, "vxe-column/filter-render": {"type": "any", "description": "数据筛选，筛选渲染器配置项"}, "vxe-column/header-export-method": {"type": "({ column, options }) => string", "description": "自定义表头单元格数据导出方法，返回自定义的标题"}, "vxe-column/export-method": {"type": "({ row, column, options }) => string", "description": "自定义单元格数据导出方法，返回自定义的值"}, "vxe-column/footer-export-method": {"type": "({ items, _columnindex, options }) => string", "description": "自定义表尾单元格数据导出方法，返回自定义的值"}, "vxe-column/title-help": {"type": "any", "description": "即将废弃，请使用 title.prefix"}, "vxe-column/title-prefix": {"type": "any", "description": "标题前缀图标配置项"}, "vxe-column/title-suffix": {"type": "any", "description": "标题后缀图标配置项"}, "vxe-column/cell-type": {"type": "string", "description": "只对特定功能有效，单元格值类型（例如：导出数据类型设置）"}, "vxe-column/cell-render": {"type": "any", "description": "默认的渲染器配置项"}, "vxe-column/edit-render": {"type": "any", "description": "可编辑渲染器配置项"}, "vxe-column/content-render": {"type": "any", "description": "内容渲染配置项"}, "vxe-column/tree-node": {"type": "boolean", "description": "只对 tree-config 配置时有效，指定为树节点"}, "vxe-column/params": {"type": "any", "description": "额外的参数（可以用来存放一些私有参数）"}, "vxe-column/col-id": {"type": "string | number", "description": "自定义列的唯一主键（注：列主键必须确保唯一，操作不正确将导致出现问题）"}, "vxe-grid/id": {"type": "string", "description": "唯一标识（被某些特定的功能所依赖）"}, "vxe-grid/columns": {"type": "array", "description": "列配置"}, "vxe-grid/data": {"type": "any[]", "description": "表格数据（与 loadData 行为一致，更新数据是不会重置状态）"}, "vxe-grid/height": {"type": "number | string", "description": "表格的高度；支持铺满父容器或者固定高度，如果设置 auto 为铺满父容器（如果设置为 auto，则必须确保存在父节点且不允许存在相邻元素）"}, "vxe-grid/min-height": {"type": "number | string", "description": "表格最小高度"}, "vxe-grid/max-height": {"type": "number | string", "description": "表格的最大高度"}, "vxe-grid/auto-resize": {"type": "boolean", "description": "自动监听父元素的变化去重新计算表格（对于父元素可能存在动态变化、显示隐藏的容器中、列宽异常等场景中的可能会用到）"}, "vxe-grid/sync-resize": {"type": "boolean | string | number", "description": "自动跟随某个属性的变化去重新计算表格，和手动调用 recalculate 方法是一样的效果（对于通过某个属性来控制显示/隐藏切换时可能会用到）"}, "vxe-grid/resizable": {"type": "boolean", "description": "已废弃，被 column-config.resizable 替换"}, "vxe-grid/stripe": {"type": "boolean", "description": "是否带有斑马纹（需要注意的是，在可编辑表格场景下，临时插入的数据不会有斑马纹样式）"}, "vxe-grid/border": {"type": "boolean | string", "description": "是否带有边框"}, "vxe-grid/padding": {"type": "boolean"}, "vxe-grid/round": {"type": "boolean", "description": "是否为圆角边框"}, "vxe-grid/size": {"type": "string", "description": "表格的尺寸"}, "vxe-grid/loading": {"type": "boolean", "description": "表格是否显示加载中"}, "vxe-grid/align": {"type": "string", "description": "所有的列对齐方式"}, "vxe-grid/header-align": {"type": "string", "description": "所有的表头列的对齐方式"}, "vxe-grid/footer-align": {"type": "string", "description": "所有的表尾列的对齐方式"}, "vxe-grid/show-header": {"type": "boolean", "description": "是否显示表头"}, "vxe-grid/highlight-current-row": {"type": "boolean", "description": "已废弃，被 row-config.isCurrent 替换"}, "vxe-grid/highlight-hover-row": {"type": "boolean", "description": "已废弃，被 row-config.isHover 替换"}, "vxe-grid/highlight-current-column": {"type": "boolean", "description": "已废弃，被 column-config.isCurrent 替换"}, "vxe-grid/highlight-hover-column": {"type": "boolean", "description": "已废弃，被 column-config.isHover 替换"}, "vxe-grid/row-class-name": {"type": "string | (({ row, rowindex, $rowindex }) => any)", "description": "给行附加 className"}, "vxe-grid/cell-class-name": {"type": "string | (({ row, rowindex, $rowindex, column, columnindex, $columnindex }) => any)", "description": "给单元格附加 className"}, "vxe-grid/header-row-class-name": {"type": "string | (({ $rowindex }) => any)", "description": "给表头的行附加 className"}, "vxe-grid/header-cell-class-name": {"type": "string | (({ $rowindex, column, columnindex, $columnindex }) => any)", "description": "给表头的单元格附加 className"}, "vxe-grid/footer-row-class-name": {"type": "string | (({ $rowindex }) => any)", "description": "给表尾的行附加 className"}, "vxe-grid/footer-cell-class-name": {"type": "string | (({ $rowindex, column, columnindex, $columnindex }) => any)", "description": "给表尾的单元格附加 className"}, "vxe-grid/cell-style": {"type": "any | (({ row, rowindex, $rowindex, column, columnindex, $columnindex }) => any)", "description": "给单元格附加样式"}, "vxe-grid/header-cell-style": {"type": "any | (({ $rowindex, column, columnindex, $columnindex }) => any)", "description": "给表头单元格附加样式"}, "vxe-grid/footer-cell-style": {"type": "any | (({ $rowindex, column, columnindex, $columnindex }) => any)", "description": "给表尾单元格附加样式"}, "vxe-grid/row-style": {"type": "any | (({ row, rowindex, $rowindex }) => any)", "description": "给行附加样式，也可以是函数"}, "vxe-grid/header-row-style": {"type": "any | (({ $rowindex, column, columnindex, $columnindex }) => any)", "description": "给表头行附加样式"}, "vxe-grid/footer-row-style": {"type": "any | (({ $rowindex }) => any)", "description": "给表尾行附加样式"}, "vxe-grid/show-footer": {"type": "boolean", "description": "是否显示表尾"}, "vxe-grid/footer-data": {"type": "any[]", "description": "表尾数据"}, "vxe-grid/footer-method": {"type": "({ columns, data }) => any[][]", "description": "表尾的数据获取方法，返回一个二维数组"}, "vxe-grid/merge-cells": {"type": "array<{ row: number, col: number, rowspan: number, colspan: number }>", "description": "临时合并指定的单元格 (不能用于展开行，不建议用于固定列、树形结构)"}, "vxe-grid/merge-footer-items": {"type": "array<{ row: number, col: number, rowspan: number, colspan: number }>", "description": "临时合并表尾 (不能用于展开行，不建议用于固定列、树形结构)"}, "vxe-grid/span-method": {"type": "({ row, rowindex, $rowindex, _rowindex, column, columnindex, $columnindex, _columnindex, data }) => { rowspan: number, colspan: number}", "description": "自定义合并函数，返回计算后的值 (不能用于虚拟滚动、展开行，不建议用于固定列、树形结构)"}, "vxe-grid/footer-span-method": {"type": "({ $rowindex, column, columnindex, $columnindex, _columnindex, data }) => { rowspan: number, colspan: number}", "description": "表尾合并行或列，返回计算后的值 (不能用于虚拟滚动、展开行，不建议用于固定列、树形结构)"}, "vxe-grid/show-overflow": {"type": "boolean | string", "description": "设置所有内容过长时显示为省略号（如果是固定列建议设置该值，提升渲染速度）"}, "vxe-grid/show-header-overflow": {"type": "boolean | string", "description": "设置表头所有内容过长时显示为省略号"}, "vxe-grid/show-footer-overflow": {"type": "boolean | string", "description": "设置表尾所有内容过长时显示为省略号"}, "vxe-grid/column-key": {"type": "boolean", "description": "已废弃，被 column-config.useKey 替换"}, "vxe-grid/row-key": {"type": "boolean", "description": "已废弃，被 row-config.useKey 替换"}, "vxe-grid/row-id": {"type": "string", "description": "已废弃，被 row-config.keyField 替换"}, "vxe-grid/keep-source": {"type": "boolean", "description": "保持原始值的状态，被某些功能所依赖，比如编辑状态、还原数据等（开启后影响性能，具体取决于数据量）"}, "vxe-grid/column-config": {"type": "any", "description": "列配置信息"}, "vxe-grid/current-column-config": {"type": "any", "description": "当前列配置信息"}, "vxe-grid/cell-config": {"type": "any"}, "vxe-grid/header-cell-config": {"type": "any"}, "vxe-grid/footer-cell-config": {"type": "any"}, "vxe-grid/row-config": {"type": "any", "description": "行配置信息"}, "vxe-grid/current-row-config": {"type": "any", "description": "当前行配置信息"}, "vxe-grid/row-group-config": {"type": "any", "description": "当前行配置信息"}, "vxe-grid/resize-config": {"type": "object", "description": "响应式布局配置项"}, "vxe-grid/resizable-config": {"type": "object", "description": "列宽拖动配置项"}, "vxe-grid/seq-config": {"type": "any", "description": "序号配置项"}, "vxe-grid/sort-config": {"type": "any", "description": "排序配置项"}, "vxe-grid/drag-config": {"type": "any", "description": "拖拽配置信息"}, "vxe-grid/row-drag-config": {"type": "any", "description": "行拖拽配置信息"}, "vxe-grid/column-drag-config": {"type": "any", "description": "拖拽配置信息"}, "vxe-grid/filter-config": {"type": "any", "description": "筛选配置项"}, "vxe-grid/export-config": {"type": "any", "description": "导出配置项"}, "vxe-grid/import-config": {"type": "any", "description": "导入配置项"}, "vxe-grid/print-config": {"type": "any", "description": "打印配置项"}, "vxe-grid/radio-config": {"type": "any", "description": "单选框配置项"}, "vxe-grid/checkbox-config": {"type": "any", "description": "复选框配置项"}, "vxe-grid/tooltip-config": {"type": "any", "description": "tooltip 配置项"}, "vxe-grid/expand-config": {"type": "any", "description": "展开行配置项（不支持虚拟滚动）"}, "vxe-grid/tree-config": {"type": "any", "description": "树形结构配置项"}, "vxe-grid/menu-config": {"type": "any", "description": "右键菜单配置项"}, "vxe-grid/clip-config": {"type": "any", "description": "复制/粘贴配置项"}, "vxe-grid/fnr-config": {"type": "any", "description": "查找/替换配置项"}, "vxe-grid/mouse-config": {"type": "any", "description": "鼠标配置项"}, "vxe-grid/area-config": {"type": "any", "description": "区域选取配置项"}, "vxe-grid/keyboard-config": {"type": "any", "description": "按键配置项"}, "vxe-grid/edit-config": {"type": "any", "description": "可编辑配置项"}, "vxe-grid/valid-config": {"type": "any", "description": "校验配置项"}, "vxe-grid/edit-rules": {"type": "{ [field: string]: vxetabledefines.validatorrule[] }", "description": "校验规则配置项"}, "vxe-grid/empty-text": {"type": "string", "description": "空数据时显示的内容"}, "vxe-grid/empty-render": {"type": "any", "description": "空内容渲染配置项，empty-render 的优先级大于 empty-text"}, "vxe-grid/loading-config": {"type": "any", "description": "加载中配置项"}, "vxe-grid/custom-config": {"type": "any", "description": "个性化信息配置项"}, "vxe-grid/scroll-x": {"type": "any", "description": "横向滚动配置"}, "vxe-grid/scroll-y": {"type": "any", "description": "纵向滚动配置"}, "vxe-grid/virtual-x-config": {"type": "any", "description": "横向滚动配置"}, "vxe-grid/virtual-y-config": {"type": "any", "description": "纵向滚动配置"}, "vxe-grid/scrollbar-config": {"type": "any", "description": "滚动条配置"}, "vxe-grid/params": {"type": "any", "description": "自定义参数（可以用来存放一些自定义的数据）"}, "vxe-grid/form-config": {"type": "any", "description": "表单配置项"}, "vxe-grid/toolbar-config": {"type": "any", "description": "工具栏配置"}, "vxe-grid/pager-config": {"type": "any", "description": "分页配置项"}, "vxe-grid/proxy-config": {"type": "any", "description": "数据代理配置项（基于 Promise API）"}, "vxe-grid/zoom-config": {"type": "any", "description": "缩放配置项"}, "vxe-grid/layouts": {"type": "string[][]", "description": "自定义布局"}, "vxe-toolbar/size": {"type": "string", "description": "尺寸"}, "vxe-toolbar/loading": {"type": "boolean", "description": "是否加载中"}, "vxe-toolbar/class-name": {"type": "string | (({}) => string)", "description": "给工具栏 className"}, "vxe-toolbar/import": {"type": "boolean | object", "description": "导入按钮配置（需要设置 \"import-config\"）"}, "vxe-toolbar/export": {"type": "boolean | object", "description": "导出按钮配置（需要设置 \"export-config\"）"}, "vxe-toolbar/print": {"type": "boolean | object", "description": "打印按钮配置"}, "vxe-toolbar/refresh": {"type": "boolean | object", "description": "刷新按钮配置"}, "vxe-toolbar/custom": {"type": "boolean | object", "description": "自定义列配置"}, "vxe-toolbar/buttons": {"type": "any[]", "description": "左侧按钮列表"}, "vxe-toolbar/tools": {"type": "any[]", "description": "右侧工具列表"}}