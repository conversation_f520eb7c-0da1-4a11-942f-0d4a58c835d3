{"version": 3, "sources": ["../../highlight.js/es/languages/typescript.js"], "sourcesContent": ["const IDENT_RE = '[A-Za-z$_][0-9A-Za-z$_]*';\nconst KEYWORDS = [\n  \"as\", // for exports\n  \"in\",\n  \"of\",\n  \"if\",\n  \"for\",\n  \"while\",\n  \"finally\",\n  \"var\",\n  \"new\",\n  \"function\",\n  \"do\",\n  \"return\",\n  \"void\",\n  \"else\",\n  \"break\",\n  \"catch\",\n  \"instanceof\",\n  \"with\",\n  \"throw\",\n  \"case\",\n  \"default\",\n  \"try\",\n  \"switch\",\n  \"continue\",\n  \"typeof\",\n  \"delete\",\n  \"let\",\n  \"yield\",\n  \"const\",\n  \"class\",\n  // J<PERSON> handles these with a special rule\n  // \"get\",\n  // \"set\",\n  \"debugger\",\n  \"async\",\n  \"await\",\n  \"static\",\n  \"import\",\n  \"from\",\n  \"export\",\n  \"extends\"\n];\nconst LITERALS = [\n  \"true\",\n  \"false\",\n  \"null\",\n  \"undefined\",\n  \"NaN\",\n  \"Infinity\"\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects\nconst TYPES = [\n  // Fundamental objects\n  \"Object\",\n  \"Function\",\n  \"Boolean\",\n  \"Symbol\",\n  // numbers and dates\n  \"Math\",\n  \"Date\",\n  \"Number\",\n  \"BigInt\",\n  // text\n  \"String\",\n  \"RegExp\",\n  // Indexed collections\n  \"Array\",\n  \"Float32Array\",\n  \"Float64Array\",\n  \"Int8Array\",\n  \"Uint8Array\",\n  \"Uint8ClampedArray\",\n  \"Int16Array\",\n  \"Int32Array\",\n  \"Uint16Array\",\n  \"Uint32Array\",\n  \"BigInt64Array\",\n  \"BigUint64Array\",\n  // Keyed collections\n  \"Set\",\n  \"Map\",\n  \"WeakSet\",\n  \"WeakMap\",\n  // Structured data\n  \"ArrayBuffer\",\n  \"SharedArrayBuffer\",\n  \"Atomics\",\n  \"DataView\",\n  \"JSON\",\n  // Control abstraction objects\n  \"Promise\",\n  \"Generator\",\n  \"GeneratorFunction\",\n  \"AsyncFunction\",\n  // Reflection\n  \"Reflect\",\n  \"Proxy\",\n  // Internationalization\n  \"Intl\",\n  // WebAssembly\n  \"WebAssembly\"\n];\n\nconst ERROR_TYPES = [\n  \"Error\",\n  \"EvalError\",\n  \"InternalError\",\n  \"RangeError\",\n  \"ReferenceError\",\n  \"SyntaxError\",\n  \"TypeError\",\n  \"URIError\"\n];\n\nconst BUILT_IN_GLOBALS = [\n  \"setInterval\",\n  \"setTimeout\",\n  \"clearInterval\",\n  \"clearTimeout\",\n\n  \"require\",\n  \"exports\",\n\n  \"eval\",\n  \"isFinite\",\n  \"isNaN\",\n  \"parseFloat\",\n  \"parseInt\",\n  \"decodeURI\",\n  \"decodeURIComponent\",\n  \"encodeURI\",\n  \"encodeURIComponent\",\n  \"escape\",\n  \"unescape\"\n];\n\nconst BUILT_IN_VARIABLES = [\n  \"arguments\",\n  \"this\",\n  \"super\",\n  \"console\",\n  \"window\",\n  \"document\",\n  \"localStorage\",\n  \"sessionStorage\",\n  \"module\",\n  \"global\" // Node.js\n];\n\nconst BUILT_INS = [].concat(\n  BUILT_IN_GLOBALS,\n  TYPES,\n  ERROR_TYPES\n);\n\n/*\nLanguage: JavaScript\nDescription: JavaScript (JS) is a lightweight, interpreted, or just-in-time compiled programming language with first-class functions.\nCategory: common, scripting, web\nWebsite: https://developer.mozilla.org/en-US/docs/Web/JavaScript\n*/\n\n\n/** @type LanguageFn */\nfunction javascript(hljs) {\n  const regex = hljs.regex;\n  /**\n   * Takes a string like \"<Booger\" and checks to see\n   * if we can find a matching \"</Booger\" later in the\n   * content.\n   * @param {RegExpMatchArray} match\n   * @param {{after:number}} param1\n   */\n  const hasClosingTag = (match, { after }) => {\n    const tag = \"</\" + match[0].slice(1);\n    const pos = match.input.indexOf(tag, after);\n    return pos !== -1;\n  };\n\n  const IDENT_RE$1 = IDENT_RE;\n  const FRAGMENT = {\n    begin: '<>',\n    end: '</>'\n  };\n  // to avoid some special cases inside isTrulyOpeningTag\n  const XML_SELF_CLOSING = /<[A-Za-z0-9\\\\._:-]+\\s*\\/>/;\n  const XML_TAG = {\n    begin: /<[A-Za-z0-9\\\\._:-]+/,\n    end: /\\/[A-Za-z0-9\\\\._:-]+>|\\/>/,\n    /**\n     * @param {RegExpMatchArray} match\n     * @param {CallbackResponse} response\n     */\n    isTrulyOpeningTag: (match, response) => {\n      const afterMatchIndex = match[0].length + match.index;\n      const nextChar = match.input[afterMatchIndex];\n      if (\n        // HTML should not include another raw `<` inside a tag\n        // nested type?\n        // `<Array<Array<number>>`, etc.\n        nextChar === \"<\" ||\n        // the , gives away that this is not HTML\n        // `<T, A extends keyof T, V>`\n        nextChar === \",\"\n        ) {\n        response.ignoreMatch();\n        return;\n      }\n\n      // `<something>`\n      // Quite possibly a tag, lets look for a matching closing tag...\n      if (nextChar === \">\") {\n        // if we cannot find a matching closing tag, then we\n        // will ignore it\n        if (!hasClosingTag(match, { after: afterMatchIndex })) {\n          response.ignoreMatch();\n        }\n      }\n\n      // `<blah />` (self-closing)\n      // handled by simpleSelfClosing rule\n\n      let m;\n      const afterMatch = match.input.substring(afterMatchIndex);\n\n      // some more template typing stuff\n      //  <T = any>(key?: string) => Modify<\n      if ((m = afterMatch.match(/^\\s*=/))) {\n        response.ignoreMatch();\n        return;\n      }\n\n      // `<From extends string>`\n      // technically this could be HTML, but it smells like a type\n      // NOTE: This is ugh, but added specifically for https://github.com/highlightjs/highlight.js/issues/3276\n      if ((m = afterMatch.match(/^\\s+extends\\s+/))) {\n        if (m.index === 0) {\n          response.ignoreMatch();\n          // eslint-disable-next-line no-useless-return\n          return;\n        }\n      }\n    }\n  };\n  const KEYWORDS$1 = {\n    $pattern: IDENT_RE,\n    keyword: KEYWORDS,\n    literal: LITERALS,\n    built_in: BUILT_INS,\n    \"variable.language\": BUILT_IN_VARIABLES\n  };\n\n  // https://tc39.es/ecma262/#sec-literals-numeric-literals\n  const decimalDigits = '[0-9](_?[0-9])*';\n  const frac = `\\\\.(${decimalDigits})`;\n  // DecimalIntegerLiteral, including Annex B NonOctalDecimalIntegerLiteral\n  // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n  const decimalInteger = `0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*`;\n  const NUMBER = {\n    className: 'number',\n    variants: [\n      // DecimalLiteral\n      { begin: `(\\\\b(${decimalInteger})((${frac})|\\\\.)?|(${frac}))` +\n        `[eE][+-]?(${decimalDigits})\\\\b` },\n      { begin: `\\\\b(${decimalInteger})\\\\b((${frac})\\\\b|\\\\.)?|(${frac})\\\\b` },\n\n      // DecimalBigIntegerLiteral\n      { begin: `\\\\b(0|[1-9](_?[0-9])*)n\\\\b` },\n\n      // NonDecimalIntegerLiteral\n      { begin: \"\\\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\\\b\" },\n      { begin: \"\\\\b0[bB][0-1](_?[0-1])*n?\\\\b\" },\n      { begin: \"\\\\b0[oO][0-7](_?[0-7])*n?\\\\b\" },\n\n      // LegacyOctalIntegerLiteral (does not include underscore separators)\n      // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n      { begin: \"\\\\b0[0-7]+n?\\\\b\" },\n    ],\n    relevance: 0\n  };\n\n  const SUBST = {\n    className: 'subst',\n    begin: '\\\\$\\\\{',\n    end: '\\\\}',\n    keywords: KEYWORDS$1,\n    contains: [] // defined later\n  };\n  const HTML_TEMPLATE = {\n    begin: '\\.?html`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'xml'\n    }\n  };\n  const CSS_TEMPLATE = {\n    begin: '\\.?css`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'css'\n    }\n  };\n  const GRAPHQL_TEMPLATE = {\n    begin: '\\.?gql`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'graphql'\n    }\n  };\n  const TEMPLATE_STRING = {\n    className: 'string',\n    begin: '`',\n    end: '`',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ]\n  };\n  const JSDOC_COMMENT = hljs.COMMENT(\n    /\\/\\*\\*(?!\\/)/,\n    '\\\\*/',\n    {\n      relevance: 0,\n      contains: [\n        {\n          begin: '(?=@[A-Za-z]+)',\n          relevance: 0,\n          contains: [\n            {\n              className: 'doctag',\n              begin: '@[A-Za-z]+'\n            },\n            {\n              className: 'type',\n              begin: '\\\\{',\n              end: '\\\\}',\n              excludeEnd: true,\n              excludeBegin: true,\n              relevance: 0\n            },\n            {\n              className: 'variable',\n              begin: IDENT_RE$1 + '(?=\\\\s*(-)|$)',\n              endsParent: true,\n              relevance: 0\n            },\n            // eat spaces (not newlines) so we can find\n            // types or variables\n            {\n              begin: /(?=[^\\n])\\s/,\n              relevance: 0\n            }\n          ]\n        }\n      ]\n    }\n  );\n  const COMMENT = {\n    className: \"comment\",\n    variants: [\n      JSDOC_COMMENT,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.C_LINE_COMMENT_MODE\n    ]\n  };\n  const SUBST_INTERNALS = [\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    HTML_TEMPLATE,\n    CSS_TEMPLATE,\n    GRAPHQL_TEMPLATE,\n    TEMPLATE_STRING,\n    // Skip numbers when they are part of a variable name\n    { match: /\\$\\d+/ },\n    NUMBER,\n    // This is intentional:\n    // See https://github.com/highlightjs/highlight.js/issues/3288\n    // hljs.REGEXP_MODE\n  ];\n  SUBST.contains = SUBST_INTERNALS\n    .concat({\n      // we need to pair up {} inside our subst to prevent\n      // it from ending too early by matching another }\n      begin: /\\{/,\n      end: /\\}/,\n      keywords: KEYWORDS$1,\n      contains: [\n        \"self\"\n      ].concat(SUBST_INTERNALS)\n    });\n  const SUBST_AND_COMMENTS = [].concat(COMMENT, SUBST.contains);\n  const PARAMS_CONTAINS = SUBST_AND_COMMENTS.concat([\n    // eat recursive parens in sub expressions\n    {\n      begin: /(\\s*)\\(/,\n      end: /\\)/,\n      keywords: KEYWORDS$1,\n      contains: [\"self\"].concat(SUBST_AND_COMMENTS)\n    }\n  ]);\n  const PARAMS = {\n    className: 'params',\n    // convert this to negative lookbehind in v12\n    begin: /(\\s*)\\(/, // to match the parms with \n    end: /\\)/,\n    excludeBegin: true,\n    excludeEnd: true,\n    keywords: KEYWORDS$1,\n    contains: PARAMS_CONTAINS\n  };\n\n  // ES6 classes\n  const CLASS_OR_EXTENDS = {\n    variants: [\n      // class Car extends vehicle\n      {\n        match: [\n          /class/,\n          /\\s+/,\n          IDENT_RE$1,\n          /\\s+/,\n          /extends/,\n          /\\s+/,\n          regex.concat(IDENT_RE$1, \"(\", regex.concat(/\\./, IDENT_RE$1), \")*\")\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.class\",\n          5: \"keyword\",\n          7: \"title.class.inherited\"\n        }\n      },\n      // class Car\n      {\n        match: [\n          /class/,\n          /\\s+/,\n          IDENT_RE$1\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.class\"\n        }\n      },\n\n    ]\n  };\n\n  const CLASS_REFERENCE = {\n    relevance: 0,\n    match:\n    regex.either(\n      // Hard coded exceptions\n      /\\bJSON/,\n      // Float32Array, OutT\n      /\\b[A-Z][a-z]+([A-Z][a-z]*|\\d)*/,\n      // CSSFactory, CSSFactoryT\n      /\\b[A-Z]{2,}([A-Z][a-z]+|\\d)+([A-Z][a-z]*)*/,\n      // FPs, FPsT\n      /\\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\\d)*([A-Z][a-z]*)*/,\n      // P\n      // single letters are not highlighted\n      // BLAH\n      // this will be flagged as a UPPER_CASE_CONSTANT instead\n    ),\n    className: \"title.class\",\n    keywords: {\n      _: [\n        // se we still get relevance credit for JS library classes\n        ...TYPES,\n        ...ERROR_TYPES\n      ]\n    }\n  };\n\n  const USE_STRICT = {\n    label: \"use_strict\",\n    className: 'meta',\n    relevance: 10,\n    begin: /^\\s*['\"]use (strict|asm)['\"]/\n  };\n\n  const FUNCTION_DEFINITION = {\n    variants: [\n      {\n        match: [\n          /function/,\n          /\\s+/,\n          IDENT_RE$1,\n          /(?=\\s*\\()/\n        ]\n      },\n      // anonymous function\n      {\n        match: [\n          /function/,\n          /\\s*(?=\\()/\n        ]\n      }\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    label: \"func.def\",\n    contains: [ PARAMS ],\n    illegal: /%/\n  };\n\n  const UPPER_CASE_CONSTANT = {\n    relevance: 0,\n    match: /\\b[A-Z][A-Z_0-9]+\\b/,\n    className: \"variable.constant\"\n  };\n\n  function noneOf(list) {\n    return regex.concat(\"(?!\", list.join(\"|\"), \")\");\n  }\n\n  const FUNCTION_CALL = {\n    match: regex.concat(\n      /\\b/,\n      noneOf([\n        ...BUILT_IN_GLOBALS,\n        \"super\",\n        \"import\"\n      ].map(x => `${x}\\\\s*\\\\(`)),\n      IDENT_RE$1, regex.lookahead(/\\s*\\(/)),\n    className: \"title.function\",\n    relevance: 0\n  };\n\n  const PROPERTY_ACCESS = {\n    begin: regex.concat(/\\./, regex.lookahead(\n      regex.concat(IDENT_RE$1, /(?![0-9A-Za-z$_(])/)\n    )),\n    end: IDENT_RE$1,\n    excludeBegin: true,\n    keywords: \"prototype\",\n    className: \"property\",\n    relevance: 0\n  };\n\n  const GETTER_OR_SETTER = {\n    match: [\n      /get|set/,\n      /\\s+/,\n      IDENT_RE$1,\n      /(?=\\()/\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [\n      { // eat to avoid empty params\n        begin: /\\(\\)/\n      },\n      PARAMS\n    ]\n  };\n\n  const FUNC_LEAD_IN_RE = '(\\\\(' +\n    '[^()]*(\\\\(' +\n    '[^()]*(\\\\(' +\n    '[^()]*' +\n    '\\\\)[^()]*)*' +\n    '\\\\)[^()]*)*' +\n    '\\\\)|' + hljs.UNDERSCORE_IDENT_RE + ')\\\\s*=>';\n\n  const FUNCTION_VARIABLE = {\n    match: [\n      /const|var|let/, /\\s+/,\n      IDENT_RE$1, /\\s*/,\n      /=\\s*/,\n      /(async\\s*)?/, // async is optional\n      regex.lookahead(FUNC_LEAD_IN_RE)\n    ],\n    keywords: \"async\",\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [\n      PARAMS\n    ]\n  };\n\n  return {\n    name: 'JavaScript',\n    aliases: ['js', 'jsx', 'mjs', 'cjs'],\n    keywords: KEYWORDS$1,\n    // this will be extended by TypeScript\n    exports: { PARAMS_CONTAINS, CLASS_REFERENCE },\n    illegal: /#(?![$_A-z])/,\n    contains: [\n      hljs.SHEBANG({\n        label: \"shebang\",\n        binary: \"node\",\n        relevance: 5\n      }),\n      USE_STRICT,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      HTML_TEMPLATE,\n      CSS_TEMPLATE,\n      GRAPHQL_TEMPLATE,\n      TEMPLATE_STRING,\n      COMMENT,\n      // Skip numbers when they are part of a variable name\n      { match: /\\$\\d+/ },\n      NUMBER,\n      CLASS_REFERENCE,\n      {\n        className: 'attr',\n        begin: IDENT_RE$1 + regex.lookahead(':'),\n        relevance: 0\n      },\n      FUNCTION_VARIABLE,\n      { // \"value\" container\n        begin: '(' + hljs.RE_STARTERS_RE + '|\\\\b(case|return|throw)\\\\b)\\\\s*',\n        keywords: 'return throw case',\n        relevance: 0,\n        contains: [\n          COMMENT,\n          hljs.REGEXP_MODE,\n          {\n            className: 'function',\n            // we have to count the parens to make sure we actually have the\n            // correct bounding ( ) before the =>.  There could be any number of\n            // sub-expressions inside also surrounded by parens.\n            begin: FUNC_LEAD_IN_RE,\n            returnBegin: true,\n            end: '\\\\s*=>',\n            contains: [\n              {\n                className: 'params',\n                variants: [\n                  {\n                    begin: hljs.UNDERSCORE_IDENT_RE,\n                    relevance: 0\n                  },\n                  {\n                    className: null,\n                    begin: /\\(\\s*\\)/,\n                    skip: true\n                  },\n                  {\n                    begin: /(\\s*)\\(/,\n                    end: /\\)/,\n                    excludeBegin: true,\n                    excludeEnd: true,\n                    keywords: KEYWORDS$1,\n                    contains: PARAMS_CONTAINS\n                  }\n                ]\n              }\n            ]\n          },\n          { // could be a comma delimited list of params to a function call\n            begin: /,/,\n            relevance: 0\n          },\n          {\n            match: /\\s+/,\n            relevance: 0\n          },\n          { // JSX\n            variants: [\n              { begin: FRAGMENT.begin, end: FRAGMENT.end },\n              { match: XML_SELF_CLOSING },\n              {\n                begin: XML_TAG.begin,\n                // we carefully check the opening tag to see if it truly\n                // is a tag and not a false positive\n                'on:begin': XML_TAG.isTrulyOpeningTag,\n                end: XML_TAG.end\n              }\n            ],\n            subLanguage: 'xml',\n            contains: [\n              {\n                begin: XML_TAG.begin,\n                end: XML_TAG.end,\n                skip: true,\n                contains: ['self']\n              }\n            ]\n          }\n        ],\n      },\n      FUNCTION_DEFINITION,\n      {\n        // prevent this from getting swallowed up by function\n        // since they appear \"function like\"\n        beginKeywords: \"while if switch catch for\"\n      },\n      {\n        // we have to count the parens to make sure we actually have the correct\n        // bounding ( ).  There could be any number of sub-expressions inside\n        // also surrounded by parens.\n        begin: '\\\\b(?!function)' + hljs.UNDERSCORE_IDENT_RE +\n          '\\\\(' + // first parens\n          '[^()]*(\\\\(' +\n            '[^()]*(\\\\(' +\n              '[^()]*' +\n            '\\\\)[^()]*)*' +\n          '\\\\)[^()]*)*' +\n          '\\\\)\\\\s*\\\\{', // end parens\n        returnBegin:true,\n        label: \"func.def\",\n        contains: [\n          PARAMS,\n          hljs.inherit(hljs.TITLE_MODE, { begin: IDENT_RE$1, className: \"title.function\" })\n        ]\n      },\n      // catch ... so it won't trigger the property rule below\n      {\n        match: /\\.\\.\\./,\n        relevance: 0\n      },\n      PROPERTY_ACCESS,\n      // hack: prevents detection of keywords in some circumstances\n      // .keyword()\n      // $keyword = x\n      {\n        match: '\\\\$' + IDENT_RE$1,\n        relevance: 0\n      },\n      {\n        match: [ /\\bconstructor(?=\\s*\\()/ ],\n        className: { 1: \"title.function\" },\n        contains: [ PARAMS ]\n      },\n      FUNCTION_CALL,\n      UPPER_CASE_CONSTANT,\n      CLASS_OR_EXTENDS,\n      GETTER_OR_SETTER,\n      {\n        match: /\\$[(.]/ // relevance booster for a pattern common to JS libs: `$(something)` and `$.something`\n      }\n    ]\n  };\n}\n\n/*\nLanguage: TypeScript\nAuthor: Panu Horsmalahti <<EMAIL>>\nContributors: Ike Ku <<EMAIL>>\nDescription: TypeScript is a strict superset of JavaScript\nWebsite: https://www.typescriptlang.org\nCategory: common, scripting\n*/\n\n\n/** @type LanguageFn */\nfunction typescript(hljs) {\n  const tsLanguage = javascript(hljs);\n\n  const IDENT_RE$1 = IDENT_RE;\n  const TYPES = [\n    \"any\",\n    \"void\",\n    \"number\",\n    \"boolean\",\n    \"string\",\n    \"object\",\n    \"never\",\n    \"symbol\",\n    \"bigint\",\n    \"unknown\"\n  ];\n  const NAMESPACE = {\n    begin: [\n      /namespace/,\n      /\\s+/,\n      hljs.IDENT_RE\n    ],\n    beginScope: {\n      1: \"keyword\",\n      3: \"title.class\"\n    }\n  };\n  const INTERFACE = {\n    beginKeywords: 'interface',\n    end: /\\{/,\n    excludeEnd: true,\n    keywords: {\n      keyword: 'interface extends',\n      built_in: TYPES\n    },\n    contains: [ tsLanguage.exports.CLASS_REFERENCE ]\n  };\n  const USE_STRICT = {\n    className: 'meta',\n    relevance: 10,\n    begin: /^\\s*['\"]use strict['\"]/\n  };\n  const TS_SPECIFIC_KEYWORDS = [\n    \"type\",\n    // \"namespace\",\n    \"interface\",\n    \"public\",\n    \"private\",\n    \"protected\",\n    \"implements\",\n    \"declare\",\n    \"abstract\",\n    \"readonly\",\n    \"enum\",\n    \"override\",\n    \"satisfies\"\n  ];\n\n  /*\n    namespace is a TS keyword but it's fine to use it as a variable name too.\n    const message = 'foo';\n    const namespace = 'bar';\n  */\n\n  const KEYWORDS$1 = {\n    $pattern: IDENT_RE,\n    keyword: KEYWORDS.concat(TS_SPECIFIC_KEYWORDS),\n    literal: LITERALS,\n    built_in: BUILT_INS.concat(TYPES),\n    \"variable.language\": BUILT_IN_VARIABLES\n  };\n  const DECORATOR = {\n    className: 'meta',\n    begin: '@' + IDENT_RE$1,\n  };\n\n  const swapMode = (mode, label, replacement) => {\n    const indx = mode.contains.findIndex(m => m.label === label);\n    if (indx === -1) { throw new Error(\"can not find mode to replace\"); }\n\n    mode.contains.splice(indx, 1, replacement);\n  };\n\n\n  // this should update anywhere keywords is used since\n  // it will be the same actual JS object\n  Object.assign(tsLanguage.keywords, KEYWORDS$1);\n\n  tsLanguage.exports.PARAMS_CONTAINS.push(DECORATOR);\n\n  // highlight the function params\n  const ATTRIBUTE_HIGHLIGHT = tsLanguage.contains.find(c => c.className === \"attr\");\n  tsLanguage.exports.PARAMS_CONTAINS.push([\n    tsLanguage.exports.CLASS_REFERENCE, // class reference for highlighting the params types\n    ATTRIBUTE_HIGHLIGHT, // highlight the params key\n  ]);\n  tsLanguage.contains = tsLanguage.contains.concat([\n    DECORATOR,\n    NAMESPACE,\n    INTERFACE,\n  ]);\n\n  // TS gets a simpler shebang rule than JS\n  swapMode(tsLanguage, \"shebang\", hljs.SHEBANG());\n  // JS use strict rule purposely excludes `asm` which makes no sense\n  swapMode(tsLanguage, \"use_strict\", USE_STRICT);\n\n  const functionDeclaration = tsLanguage.contains.find(m => m.label === \"func.def\");\n  functionDeclaration.relevance = 0; // () => {} is more typical in TypeScript\n\n  Object.assign(tsLanguage, {\n    name: 'TypeScript',\n    aliases: [\n      'ts',\n      'tsx',\n      'mts',\n      'cts'\n    ]\n  });\n\n  return tsLanguage;\n}\n\nexport { typescript as default };\n"], "mappings": ";;;AAAA,IAAM,WAAW;AACjB,IAAM,WAAW;AAAA,EACf;AAAA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAGA,IAAM,QAAQ;AAAA;AAAA,EAEZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AACF;AAEA,IAAM,cAAc;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,mBAAmB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,qBAAqB;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AACF;AAEA,IAAM,YAAY,CAAC,EAAE;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AACF;AAWA,SAAS,WAAW,MAAM;AACxB,QAAM,QAAQ,KAAK;AAQnB,QAAM,gBAAgB,CAAC,OAAO,EAAE,MAAM,MAAM;AAC1C,UAAM,MAAM,OAAO,MAAM,CAAC,EAAE,MAAM,CAAC;AACnC,UAAM,MAAM,MAAM,MAAM,QAAQ,KAAK,KAAK;AAC1C,WAAO,QAAQ;AAAA,EACjB;AAEA,QAAM,aAAa;AACnB,QAAM,WAAW;AAAA,IACf,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAEA,QAAM,mBAAmB;AACzB,QAAM,UAAU;AAAA,IACd,OAAO;AAAA,IACP,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,IAKL,mBAAmB,CAAC,OAAO,aAAa;AACtC,YAAM,kBAAkB,MAAM,CAAC,EAAE,SAAS,MAAM;AAChD,YAAM,WAAW,MAAM,MAAM,eAAe;AAC5C;AAAA;AAAA;AAAA;AAAA,QAIE,aAAa;AAAA;AAAA,QAGb,aAAa;AAAA,QACX;AACF,iBAAS,YAAY;AACrB;AAAA,MACF;AAIA,UAAI,aAAa,KAAK;AAGpB,YAAI,CAAC,cAAc,OAAO,EAAE,OAAO,gBAAgB,CAAC,GAAG;AACrD,mBAAS,YAAY;AAAA,QACvB;AAAA,MACF;AAKA,UAAI;AACJ,YAAM,aAAa,MAAM,MAAM,UAAU,eAAe;AAIxD,UAAK,IAAI,WAAW,MAAM,OAAO,GAAI;AACnC,iBAAS,YAAY;AACrB;AAAA,MACF;AAKA,UAAK,IAAI,WAAW,MAAM,gBAAgB,GAAI;AAC5C,YAAI,EAAE,UAAU,GAAG;AACjB,mBAAS,YAAY;AAErB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,aAAa;AAAA,IACjB,UAAU;AAAA,IACV,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,IACV,qBAAqB;AAAA,EACvB;AAGA,QAAM,gBAAgB;AACtB,QAAM,OAAO,OAAO,aAAa;AAGjC,QAAM,iBAAiB;AACvB,QAAM,SAAS;AAAA,IACb,WAAW;AAAA,IACX,UAAU;AAAA;AAAA,MAER,EAAE,OAAO,QAAQ,cAAc,MAAM,IAAI,YAAY,IAAI,eAC1C,aAAa,OAAO;AAAA,MACnC,EAAE,OAAO,OAAO,cAAc,SAAS,IAAI,eAAe,IAAI,OAAO;AAAA;AAAA,MAGrE,EAAE,OAAO,6BAA6B;AAAA;AAAA,MAGtC,EAAE,OAAO,2CAA2C;AAAA,MACpD,EAAE,OAAO,+BAA+B;AAAA,MACxC,EAAE,OAAO,+BAA+B;AAAA;AAAA;AAAA,MAIxC,EAAE,OAAO,kBAAkB;AAAA,IAC7B;AAAA,IACA,WAAW;AAAA,EACb;AAEA,QAAM,QAAQ;AAAA,IACZ,WAAW;AAAA,IACX,OAAO;AAAA,IACP,KAAK;AAAA,IACL,UAAU;AAAA,IACV,UAAU,CAAC;AAAA;AAAA,EACb;AACA,QAAM,gBAAgB;AAAA,IACpB,OAAO;AAAA,IACP,KAAK;AAAA,IACL,QAAQ;AAAA,MACN,KAAK;AAAA,MACL,WAAW;AAAA,MACX,UAAU;AAAA,QACR,KAAK;AAAA,QACL;AAAA,MACF;AAAA,MACA,aAAa;AAAA,IACf;AAAA,EACF;AACA,QAAM,eAAe;AAAA,IACnB,OAAO;AAAA,IACP,KAAK;AAAA,IACL,QAAQ;AAAA,MACN,KAAK;AAAA,MACL,WAAW;AAAA,MACX,UAAU;AAAA,QACR,KAAK;AAAA,QACL;AAAA,MACF;AAAA,MACA,aAAa;AAAA,IACf;AAAA,EACF;AACA,QAAM,mBAAmB;AAAA,IACvB,OAAO;AAAA,IACP,KAAK;AAAA,IACL,QAAQ;AAAA,MACN,KAAK;AAAA,MACL,WAAW;AAAA,MACX,UAAU;AAAA,QACR,KAAK;AAAA,QACL;AAAA,MACF;AAAA,MACA,aAAa;AAAA,IACf;AAAA,EACF;AACA,QAAM,kBAAkB;AAAA,IACtB,WAAW;AAAA,IACX,OAAO;AAAA,IACP,KAAK;AAAA,IACL,UAAU;AAAA,MACR,KAAK;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACA,QAAM,gBAAgB,KAAK;AAAA,IACzB;AAAA,IACA;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,UAAU;AAAA,QACR;AAAA,UACE,OAAO;AAAA,UACP,WAAW;AAAA,UACX,UAAU;AAAA,YACR;AAAA,cACE,WAAW;AAAA,cACX,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,OAAO;AAAA,cACP,KAAK;AAAA,cACL,YAAY;AAAA,cACZ,cAAc;AAAA,cACd,WAAW;AAAA,YACb;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,OAAO,aAAa;AAAA,cACpB,YAAY;AAAA,cACZ,WAAW;AAAA,YACb;AAAA;AAAA;AAAA,YAGA;AAAA,cACE,OAAO;AAAA,cACP,WAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,UAAU;AAAA,IACd,WAAW;AAAA,IACX,UAAU;AAAA,MACR;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACA,QAAM,kBAAkB;AAAA,IACtB,KAAK;AAAA,IACL,KAAK;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA,EAAE,OAAO,QAAQ;AAAA,IACjB;AAAA;AAAA;AAAA;AAAA,EAIF;AACA,QAAM,WAAW,gBACd,OAAO;AAAA;AAAA;AAAA,IAGN,OAAO;AAAA,IACP,KAAK;AAAA,IACL,UAAU;AAAA,IACV,UAAU;AAAA,MACR;AAAA,IACF,EAAE,OAAO,eAAe;AAAA,EAC1B,CAAC;AACH,QAAM,qBAAqB,CAAC,EAAE,OAAO,SAAS,MAAM,QAAQ;AAC5D,QAAM,kBAAkB,mBAAmB,OAAO;AAAA;AAAA,IAEhD;AAAA,MACE,OAAO;AAAA,MACP,KAAK;AAAA,MACL,UAAU;AAAA,MACV,UAAU,CAAC,MAAM,EAAE,OAAO,kBAAkB;AAAA,IAC9C;AAAA,EACF,CAAC;AACD,QAAM,SAAS;AAAA,IACb,WAAW;AAAA;AAAA,IAEX,OAAO;AAAA;AAAA,IACP,KAAK;AAAA,IACL,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,UAAU;AAAA,EACZ;AAGA,QAAM,mBAAmB;AAAA,IACvB,UAAU;AAAA;AAAA,MAER;AAAA,QACE,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,MAAM,OAAO,YAAY,KAAK,MAAM,OAAO,MAAM,UAAU,GAAG,IAAI;AAAA,QACpE;AAAA,QACA,OAAO;AAAA,UACL,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,MACF;AAAA;AAAA,MAEA;AAAA,QACE,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,MACF;AAAA,IAEF;AAAA,EACF;AAEA,QAAM,kBAAkB;AAAA,IACtB,WAAW;AAAA,IACX,OACA,MAAM;AAAA;AAAA,MAEJ;AAAA;AAAA,MAEA;AAAA;AAAA,MAEA;AAAA;AAAA,MAEA;AAAA;AAAA;AAAA;AAAA;AAAA,IAKF;AAAA,IACA,WAAW;AAAA,IACX,UAAU;AAAA,MACR,GAAG;AAAA;AAAA,QAED,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAEA,QAAM,aAAa;AAAA,IACjB,OAAO;AAAA,IACP,WAAW;AAAA,IACX,WAAW;AAAA,IACX,OAAO;AAAA,EACT;AAEA,QAAM,sBAAsB;AAAA,IAC1B,UAAU;AAAA,MACR;AAAA,QACE,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA;AAAA,QACE,OAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,IACA,OAAO;AAAA,IACP,UAAU,CAAE,MAAO;AAAA,IACnB,SAAS;AAAA,EACX;AAEA,QAAM,sBAAsB;AAAA,IAC1B,WAAW;AAAA,IACX,OAAO;AAAA,IACP,WAAW;AAAA,EACb;AAEA,WAAS,OAAO,MAAM;AACpB,WAAO,MAAM,OAAO,OAAO,KAAK,KAAK,GAAG,GAAG,GAAG;AAAA,EAChD;AAEA,QAAM,gBAAgB;AAAA,IACpB,OAAO,MAAM;AAAA,MACX;AAAA,MACA,OAAO;AAAA,QACL,GAAG;AAAA,QACH;AAAA,QACA;AAAA,MACF,EAAE,IAAI,OAAK,GAAG,CAAC,SAAS,CAAC;AAAA,MACzB;AAAA,MAAY,MAAM,UAAU,OAAO;AAAA,IAAC;AAAA,IACtC,WAAW;AAAA,IACX,WAAW;AAAA,EACb;AAEA,QAAM,kBAAkB;AAAA,IACtB,OAAO,MAAM,OAAO,MAAM,MAAM;AAAA,MAC9B,MAAM,OAAO,YAAY,oBAAoB;AAAA,IAC/C,CAAC;AAAA,IACD,KAAK;AAAA,IACL,cAAc;AAAA,IACd,UAAU;AAAA,IACV,WAAW;AAAA,IACX,WAAW;AAAA,EACb;AAEA,QAAM,mBAAmB;AAAA,IACvB,OAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,IACA,UAAU;AAAA,MACR;AAAA;AAAA,QACE,OAAO;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,QAAM,kBAAkB,6DAMb,KAAK,sBAAsB;AAEtC,QAAM,oBAAoB;AAAA,IACxB,OAAO;AAAA,MACL;AAAA,MAAiB;AAAA,MACjB;AAAA,MAAY;AAAA,MACZ;AAAA,MACA;AAAA;AAAA,MACA,MAAM,UAAU,eAAe;AAAA,IACjC;AAAA,IACA,UAAU;AAAA,IACV,WAAW;AAAA,MACT,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,IACA,UAAU;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS,CAAC,MAAM,OAAO,OAAO,KAAK;AAAA,IACnC,UAAU;AAAA;AAAA,IAEV,SAAS,EAAE,iBAAiB,gBAAgB;AAAA,IAC5C,SAAS;AAAA,IACT,UAAU;AAAA,MACR,KAAK,QAAQ;AAAA,QACX,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,WAAW;AAAA,MACb,CAAC;AAAA,MACD;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA,EAAE,OAAO,QAAQ;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,QACE,WAAW;AAAA,QACX,OAAO,aAAa,MAAM,UAAU,GAAG;AAAA,QACvC,WAAW;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA;AAAA,QACE,OAAO,MAAM,KAAK,iBAAiB;AAAA,QACnC,UAAU;AAAA,QACV,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,UACA,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA;AAAA;AAAA;AAAA,YAIX,OAAO;AAAA,YACP,aAAa;AAAA,YACb,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,UAAU;AAAA,kBACR;AAAA,oBACE,OAAO,KAAK;AAAA,oBACZ,WAAW;AAAA,kBACb;AAAA,kBACA;AAAA,oBACE,WAAW;AAAA,oBACX,OAAO;AAAA,oBACP,MAAM;AAAA,kBACR;AAAA,kBACA;AAAA,oBACE,OAAO;AAAA,oBACP,KAAK;AAAA,oBACL,cAAc;AAAA,oBACd,YAAY;AAAA,oBACZ,UAAU;AAAA,oBACV,UAAU;AAAA,kBACZ;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA;AAAA,YACE,UAAU;AAAA,cACR,EAAE,OAAO,SAAS,OAAO,KAAK,SAAS,IAAI;AAAA,cAC3C,EAAE,OAAO,iBAAiB;AAAA,cAC1B;AAAA,gBACE,OAAO,QAAQ;AAAA;AAAA;AAAA,gBAGf,YAAY,QAAQ;AAAA,gBACpB,KAAK,QAAQ;AAAA,cACf;AAAA,YACF;AAAA,YACA,aAAa;AAAA,YACb,UAAU;AAAA,cACR;AAAA,gBACE,OAAO,QAAQ;AAAA,gBACf,KAAK,QAAQ;AAAA,gBACb,MAAM;AAAA,gBACN,UAAU,CAAC,MAAM;AAAA,cACnB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA,QAGE,eAAe;AAAA,MACjB;AAAA,MACA;AAAA;AAAA;AAAA;AAAA,QAIE,OAAO,oBAAoB,KAAK,sBAC9B;AAAA;AAAA,QAOF,aAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,UACR;AAAA,UACA,KAAK,QAAQ,KAAK,YAAY,EAAE,OAAO,YAAY,WAAW,iBAAiB,CAAC;AAAA,QAClF;AAAA,MACF;AAAA;AAAA,MAEA;AAAA,QACE,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA;AAAA;AAAA;AAAA;AAAA,MAIA;AAAA,QACE,OAAO,QAAQ;AAAA,QACf,WAAW;AAAA,MACb;AAAA,MACA;AAAA,QACE,OAAO,CAAE,wBAAyB;AAAA,QAClC,WAAW,EAAE,GAAG,iBAAiB;AAAA,QACjC,UAAU,CAAE,MAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,QACE,OAAO;AAAA;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AAaA,SAAS,WAAW,MAAM;AACxB,QAAM,aAAa,WAAW,IAAI;AAElC,QAAM,aAAa;AACnB,QAAMA,SAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,YAAY;AAAA,IAChB,OAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,KAAK;AAAA,IACP;AAAA,IACA,YAAY;AAAA,MACV,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AACA,QAAM,YAAY;AAAA,IAChB,eAAe;AAAA,IACf,KAAK;AAAA,IACL,YAAY;AAAA,IACZ,UAAU;AAAA,MACR,SAAS;AAAA,MACT,UAAUA;AAAA,IACZ;AAAA,IACA,UAAU,CAAE,WAAW,QAAQ,eAAgB;AAAA,EACjD;AACA,QAAM,aAAa;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,OAAO;AAAA,EACT;AACA,QAAM,uBAAuB;AAAA,IAC3B;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAQA,QAAM,aAAa;AAAA,IACjB,UAAU;AAAA,IACV,SAAS,SAAS,OAAO,oBAAoB;AAAA,IAC7C,SAAS;AAAA,IACT,UAAU,UAAU,OAAOA,MAAK;AAAA,IAChC,qBAAqB;AAAA,EACvB;AACA,QAAM,YAAY;AAAA,IAChB,WAAW;AAAA,IACX,OAAO,MAAM;AAAA,EACf;AAEA,QAAM,WAAW,CAAC,MAAM,OAAO,gBAAgB;AAC7C,UAAM,OAAO,KAAK,SAAS,UAAU,OAAK,EAAE,UAAU,KAAK;AAC3D,QAAI,SAAS,IAAI;AAAE,YAAM,IAAI,MAAM,8BAA8B;AAAA,IAAG;AAEpE,SAAK,SAAS,OAAO,MAAM,GAAG,WAAW;AAAA,EAC3C;AAKA,SAAO,OAAO,WAAW,UAAU,UAAU;AAE7C,aAAW,QAAQ,gBAAgB,KAAK,SAAS;AAGjD,QAAM,sBAAsB,WAAW,SAAS,KAAK,OAAK,EAAE,cAAc,MAAM;AAChF,aAAW,QAAQ,gBAAgB,KAAK;AAAA,IACtC,WAAW,QAAQ;AAAA;AAAA,IACnB;AAAA;AAAA,EACF,CAAC;AACD,aAAW,WAAW,WAAW,SAAS,OAAO;AAAA,IAC/C;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAGD,WAAS,YAAY,WAAW,KAAK,QAAQ,CAAC;AAE9C,WAAS,YAAY,cAAc,UAAU;AAE7C,QAAM,sBAAsB,WAAW,SAAS,KAAK,OAAK,EAAE,UAAU,UAAU;AAChF,sBAAoB,YAAY;AAEhC,SAAO,OAAO,YAAY;AAAA,IACxB,MAAM;AAAA,IACN,SAAS;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO;AACT;", "names": ["TYPES"]}